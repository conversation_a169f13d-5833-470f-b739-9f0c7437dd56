Ext.create('Ext.data.Store',{storeId:'simpsonsStore',fields:['name','email','phone'],data:{'items':[{'name':'<PERSON>','email':'<EMAIL>','phone':'************'},{'name':'<PERSON>','email':'<EMAIL>','phone':'************'},{'name':'Homer','email':'<EMAIL>','phone':'************'},{'name':'Mar<PERSON>','email':'<EMAIL>','phone':'************'}]},proxy:{type:'memory',reader:{type:'json',root:'items'}}});Ext.create('Ext.grid.Panel',{width:'100%',height:'',padding:'',margin:'',border:'',bodyStyle:'',renderTo:Ext.get('lb_Ext_grid_Panel_auto4'),title:'Simpsons',store:Ext.data.StoreManager.lookup('simpsonsStore'),columns:[{text:'Nam1e',dataIndex:'name'},{text:'Email',dataIndex:'email',flex:1},{text:'Phone',dataIndex:'phone'}]});