var store;
var agentWin;
var userCertWin;
Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	
	
	var addButton = Ext.create("Ext.Button", {
		text : '增加',
		cls : 'Common_Btn',
		handler : function() {
			addCfg();
		}
	});
	var saveButton = Ext.create("Ext.Button", {
		text : '保存',
		cls : 'Common_Btn',
		handler : function() {
			saveCfg();
		}
	});
	var delButton = Ext.create("Ext.Button", {
		text : '删除',
		cls : 'Common_Btn',
		handler : function() {
			delCfgs();
		}
	});
	
	   var typeStore = Ext.create('Ext.data.Store', {
	        fields: ['id', 'name'],
	        data: [{
	            'id': '1',
	            'name': '采集对比'
	        },
	        {
	            'id': '2',
	            'name': '漏洞扫描'
	        }]
	    });
	   
	   var ssendStore = Ext.create('Ext.data.Store', {
	        fields: ['id', 'name'],
	        data: [{
	            'id': '1',
	            'name': '否'
	        },
	        {
	            'id': '2',
	            'name': '是'
	        }]
	    });
	
	// 定义方案名称
	Ext.define('model', {
			extend : 'Ext.data.Model',
			 fields: [{
		            name: 'iid',
		            type: 'Long'
		        },
		        {
		            name: 'itype',
		            type: 'Long'
		        },
		        {
		            name: 'iaddressor',
		            type: 'string'
		        },
		        {
		            name: 'issend',
		            type: 'Long'
		        },
		        {
		            name: 'icopyuser',
		            type: 'String'
		        },
		        {
		            name: 'iemail',
		            type: 'String'
		        }]
		});
	
	store = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'model',
			proxy : {
				type : 'ajax',
				url : 'getSenderBeanList.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProterty : 'total'
				}
			}
		});

	var typeCombo = Ext.create('Ext.form.field.ComboBox', {
        margin: '5',
        store: typeStore,
        width: 600,
        forceSelection: true,
        typeAhead: false,
        displayField: 'name',
        valueField: 'id',
        editable: false,
        triggerAction: 'all'
    });
	
	 var ssendCombo = Ext.create('Ext.form.field.ComboBox', {
	        margin: '5',
	        store: ssendStore,
	        width: 600,
	        forceSelection: true,
	        typeAhead: false,
	        displayField: 'name',
	        valueField: 'id',
	        editable: false,
	        triggerAction: 'all'
	    });

	var columns = [{
			text : '序号',
			width:40,
			xtype : 'rownumberer'
		},{
			text : 'iid',
			dataIndex : 'iid',
			hidden : true
		},{
	        text: '类型',
	        dataIndex: 'itype',
	        flex: 0.5,
	        editor: typeCombo,
	        renderer: function(value, metadata, record) {
	            var index = typeStore.find('id', value);
	            if (index != -1) {
	                return typeStore.getAt(index).data.name
	            } else {
	                return ''
	            }
	        }
	    }, {
	        text: '收件人',
	        dataIndex: 'iaddressor',
	        flex: 0.5,
	        border: true,
	        columnLines: true,
	        renderer : geticonfig,
	    },{
	        text: '是否发送',
	        dataIndex: 'issend',
	        flex: 0.5,
	        editor: ssendCombo,
	        renderer: function(value, metadata, record) {
	            var index = ssendStore.find('id', value);
	            if (index != -1) {
	                return ssendStore.getAt(index).data.name
	            } else {
	                return ''
	            }
	        }
	    },{
	        text: '抄送人',
	        dataIndex: 'icopyuser',
	        flex: 2,
	        hidden: true
	    },{
			text : '邮箱',
			width:200,
			dataIndex : 'iemail',
			
		}];
	
	
	 function geticonfig (value, p, record) {
		 var displayValue = record.get("iaddressor");
		 if (null == displayValue || "" == displayValue) {
	    		displayValue = "选择收件人";
		 }
    		return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"addressor('"
			+ record.get("iid") +"');\">"
			+ "<span class='abc' style='color:#3167ff;'>"
			+ displayValue
			+ "</span>" + "</a>";
	    }
	
	
	var form = Ext.create('Ext.ux.ideal.form.Panel', {
		baseCls:'customize_gray_back',
		region: 'north',
		border : false,
		dockedItems : [ {
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ '->', addButton,delButton,saveButton]
		} ]
	});

	var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		cls:'customize_panel_back',
		region : 'center',
		store : store,
		padding : grid_space,
		autoScroll : true,
		border : false,
		columnLines : true,
		selModel:Ext.create('Ext.selection.CheckboxModel'),
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		ipageSize : 30,
		columns : columns,
		viewConfig:{  
            enableTextSelection:true  
        },
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })]
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		layout : 'border',
		renderTo : "sender",
		border : false,
		height : contentPanel.getHeight() - modelHeigth,
		width  : contentPanel.getWidth,
		items : [ form, grid ]
	});


	contentPanel.on('resize', function() {
			mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
			mainPanel.setWidth(contentPanel.getWidth);
	});

	//删除
	function addCfg(){
		var p = new model({
			 iid: '',
	         itype: '',
	         iaddressor: '',
	         issend: '',
	         icopyuser: '',
	         iemail: ''
		});
		store.insert(0,p);
	}
	
	//保存
	function saveCfg(){
		var records = store.getModifiedRecords();
		var m = store.getModifiedRecords();
		if(records.length==0){
			Ext.Msg.show({
			     title:'提示',
			     msg: '信息没有变更，请选择需要保存的记录',
			     buttons: Ext.Msg.OK,
			     icon: Ext.Msg.INFO 
			});

		}else {
            var jsonData = '[';
            for (var i = 0; i < m.length; i++) {
                var date = Ext.JSON.encode(m[i].data);
                var itype = m[i].get('itype');
                if (itype == '' || itype == null) {
                    Ext.Msg.alert('提示', '请选择类型!');
                    return;
                }
                if (i == 0) jsonData = jsonData + date;
                else jsonData = jsonData + ',' + date
            }
            
            jsonData = jsonData + ']';
                Ext.Ajax.request({
                    url: 'saveSenderBean.do',
                    method: 'post',
                    params: {
                        jsonData: jsonData
                    },
                    success: function(response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (true == success) {
                            store.reload();
                            Ext.Msg.alert('提示', message)
                        } else {
                            Ext.Msg.alert('提示', message)
                        }
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, '操作失败！')
                    }
                })
		}
	}

	//删除
	function delCfgs() {
    			if (Ext.isIE) {
    				CollectGarbage();
    			}
    			var seleCount = grid.getSelectionModel().getSelection();
    			if (seleCount.length == 0) {
    				Ext.MessageBox.alert("提示", "请选择要删除的数据");
    				return;
    			}
    
    			Ext.MessageBox.buttonText.yes = "确定";
    			Ext.MessageBox.buttonText.no = "取消";
    			Ext.Msg.confirm("确认删除", "确定删除选中的组记录", function(id) {
    				if (id == 'yes')
    					delCfg();
    			});
		}

	function delCfg() {		
			var records = grid.getSelectionModel().getSelection();
			console.log(records);
			var jsonArray=[];
			Ext.each(records,function(item){
				if(item.data.iid!=""){
					jsonArray.push(item.data.iid);
				}
				store.remove(item);
			});
			if(jsonArray.length>0){
			Ext.Ajax.request( {
				url : 'deleteSenderBean.do',
				method : 'post',
				params : {
					deleteIds : jsonArray.join(',')
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						grid.ipage.moveFirst();
						Ext.Msg.alert('提示', message);
					} else {
						Ext.Msg.alert('提示', message);
					}
				},
				failure : function(result, request) {
					secureFilterRs(result,"请求返回失败！",request);
				}
			});
			}else{
				return;
			}
		}
	
	
	
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
				function(obj, options, eOpts) {
					Ext.destroy(mainPanel);
					if (Ext.isIE) {
						CollectGarbage();
					}
				});
	String.prototype.trim = function() {
			return this.replace(/(^\s*)|(\s*$)/g, "");
		};
	});