var store;Ext.onReady(function() {    Ext.tip.QuickTipManager.init();    var selModel = Ext.create('Ext.selection.CheckboxModel', {        checkOnly: true,        listeners: {            selectionchange: function(selModel, selections) {}        }    });    var queryButton = Ext.create('Ext.Button', {        text: '111查询',        cls: 'Common_Btn',        handler: function() {            queryBtnFun()        }    });    var resetButton = Ext.create('Ext.Button', {        text: '重置',        cls: 'Common_Btn',        handler: function() {            resetBtnFun()        }    });    var addButton = Ext.create('Ext.Button', {        text: '增加',        cls: 'Common_Btn',        handler: function() {            add()        }    });    var saveButton = Ext.create('Ext.Button', {        text: '保存',        cls: 'Common_Btn',        handler: function() {            saveRepairProgram()        }    });    var delButton = Ext.create('Ext.Button', {        text: '删除',        cls: 'Common_Btn',        handler: function() {            deleteRepairProgram()        }    });    var excelExport = Ext.create('Ext.Button', {        text: '导出',        cls: 'Common_Btn',        handler: function() {            window.location.href = 'exportStaffZhBoExcel.do'        }    });    Ext.define('model', {        extend: 'Ext.data.Model',        fields: [{            name: 'iid',            type: 'Long'        },        {            name: 'iname',            type: 'String'        },        {            name: 'iconfig',            type: 'string'        },        {            name: 'idesc',            type: 'String'        },        {            name: 'itype',            type: 'Long'        }]    });    store = Ext.create('Ext.data.Store', {        autoLoad: true,        autoDestroy: true,        model: 'model',        remoteSort: true,        proxy: {            type: 'ajax',            url: 'getDailyZhBoService.do',            reader: {                type: 'json',                root: 'dataList',                totalProperty: 'total'            }        }    });    var typeStore = Ext.create('Ext.data.Store', {        fields: ['id', 'name'],        data: [{            'id': '1',            'name': '标准运维'        },        {            'id': '2',            'name': '自定义运维'        }]    });    var typeCombo = Ext.create('Ext.form.field.ComboBox', {        margin: '5',        store: typeStore,        width: 600,        forceSelection: true,        typeAhead: false,        displayField: 'name',        valueField: 'id',        editable: false,        triggerAction: 'all'    });    var columns = [{        text: '序号',        width: 35,        xtype: 'rownumberer'    },    {        text: 'iid',        dataIndex: 'iid',        hidden: true    },    {        text: '名称',        dataIndex: 'iname',        flex: 0.5,        editor: {            xtype: 'textfield',            allowBlank: false        }    },    {        text: '类型',        dataIndex: 'itype',        flex: 0.5,        editor: typeCombo,        renderer: function(value, metadata, record) {            var index = typeStore.find('id', value);            if (index != -1) {                return typeStore.getAt(index).data.name            } else {                return ''            }        }    },    {        text: '配置',        dataIndex: 'iconfig',        hidden: true    },    {        text: '配置名',        flex: 1.5,        renderer: getname    },    {        text: '描述',        dataIndex: 'idesc',        flex: 2,        editor: {            xtype: 'textfield',            allowBlank: false        }    },    {        text: '参数配置',        flex: 0.5,        renderer: geticonfig    }];    function getname(value, p, record) {        var displayValue = record.get('iconfig');        if (null == displayValue || '' == displayValue) {} else {            var obj = JSON.parse(displayValue);            displayValue = obj.taskName        }        return displayValue    }    function geticonfig(value, p, record) {        var displayValue = record.get('iconfig');        if (null == displayValue || '' == displayValue) {            displayValue = '未配置'        } else {            displayValue = '已配置'        }        var itype = record.get('itype');        if (itype === '') {            return        } else if (itype === 0) {            Ext.Msg.alert('提示', '请先选择类型!');            return        } else if (itype == 1) {} else if (itype == 2) {}    }    var grid = Ext.create('Ext.ux.ideal.grid.Panel', {        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',        cls: 'customize_panel_back panel_space_top_bottom panel_space_left_right',        store: store,        selModel: selModel,        columns: columns    });    var inameQuery = Ext.create('Ext.form.TextField', {        emptyText: '--请输入名称--',        width: 200,        xtype: 'textfield',        listeners: {            specialkey: function(field, e) {                if (e.getKey() == Ext.EventObject.ENTER) {                    var sname = field.getValue() == null ? '': field.getValue();                    store.load({                        params: {                            sname: sname.trim()                        }                    })                }            }        }    });    var form = Ext.create('Ext.form.Panel', {        baseCls: 'customize_gray_back',        region: 'north',        border: false,        dockedItems: [{            baseCls: 'customize_gray_back',            xtype: 'toolbar',            border: false,            dock: 'top',            items: [inameQuery, queryButton, resetButton, '->', addButton, saveButton, delButton, excelExport]        }]    });    var mainPanel = Ext.create('Ext.panel.Panel', {        layout: 'border',        renderTo: 'sdas',        border: false,        height: 800,        width: '100%',        items: [form, grid]    });    store.on('beforeload',    function(store, options) {        var new_params = {            sname: inameQuery.getValue().trim()        };        Ext.apply(store.proxy.extraParams, new_params)    });    function queryBtnFun() {        if (Ext.isIE) {            CollectGarbage()        }        grid.ipage.moveFirst()    }    function resetBtnFun() {        inameQuery.setValue('');        store.load()    }    function add() {        var p = new model({            iid: '',            iname: '',            iconfig: '',            idesc: '',            itype: ''        });        store.insert(0, p)    }    function saveRepairProgram() {        var m = store.getModifiedRecords();        if (m.length != 1) {            Ext.MessageBox.alert('提示', '请保存一条数据!');            return        } else {            var flag = true;            var jsonData = '[';            for (var i = 0; i < m.length; i++) {                var date = Ext.JSON.encode(m[i].data);                var itype = m[i].get('itype');                if (itype == '' || itype == null) {                    Ext.Msg.alert('提示', '请选择类型!');                    flag = false;                    return                }                if (i == 0) jsonData = jsonData + date;                else jsonData = jsonData + ',' + date            }            jsonData = jsonData + ']';            if (flag) {                Ext.Ajax.request({                    url: 'savezhbo.do',                    method: 'post',                    params: {                        jsonData: jsonData                    },                    success: function(response, request) {                        var success = Ext.decode(response.responseText).success;                        var message = Ext.decode(response.responseText).message;                        if (true == success) {                            store.reload();                            Ext.Msg.alert('提示', message)                        } else {                            Ext.Msg.alert('提示', message)                        }                    },                    failure: function(result, request) {                        secureFilterRs(result, '操作失败！')                    }                })            }        }    }    function deleteRepairProgram() {        var m = grid.getSelectionModel().getSelection();        if (m.length == 0) {            Ext.MessageBox.alert('提示', '没有需要的删除条目！');            return        } else {            {                Ext.Msg.confirm('请确认', '是否真的要删除数据？',                function(button, text) {                    if (button == 'yes') {                        var ids = new Array();                        Ext.Array.each(m,                        function(record) {                            ids.push(record.get('iid'))                        });                        Ext.Ajax.request({                            url: 'deletezhbo.do',                            params: {                                iids: ids.join(',')                            },                            method: 'POST',                            success: function(response, opts) {                                var success = Ext.decode(response.responseText).success;                                if (success) {                                    grid.ipage.moveFirst();                                    Ext.Msg.alert('提示', '删除成功！')                                } else {                                    Ext.Msg.alert('提示', '删除失败！')                                }                            }                        })                    }                })            }        }    }});