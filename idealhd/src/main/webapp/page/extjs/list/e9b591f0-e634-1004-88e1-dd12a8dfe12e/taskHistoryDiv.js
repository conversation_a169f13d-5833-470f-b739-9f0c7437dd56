var pagelimit=30;Ext.define('businessSystemModel',{extend:'Ext.data.Model',remoteSort:true,fields:[{name:'taskName',type:'string'},{name:'taskId',type:'string'}]});var taskNameStore=Ext.create('Ext.data.JsonStore',{fields:['taskName','taskName'],autoLoad:true,autoDestroy:true,model:'businessSystemModel',proxy:{type:'ajax',url:'icTaskHistoryCombox.do',reader:{type:'json',root:'dataList'}}});var taskComb=Ext.create('Ext.ux.ideal.form.ComboBox',{name:'taskName',store:taskNameStore,emptyText:'--请选择任务名称--',labelAlign:'right',fieldLabel:'任务名称',labelWidth:65,width:'50%'});var stateStore=Ext.create('Ext.data.Store',{fields:['id','name'],data:[{'id':'0,50','name':'运行'},{'id':'2','name':'结束'},{'id':'4','name':'终止'},{'id':'46','name':'异常'},{'id':'2,4,46','name':'非运行'}]});var task1Comb=Ext.create('Ext.ux.ideal.form.ComboBox',{fieldLabel:'执行状态',emptyText:'请选择执行状态',displayField:'name',valueField:'id',store:stateStore,queryMode:'local',editable:false,width:'50%',labelAlign:'right',labelWidth:65});Ext.define('userModel',{extend:'Ext.data.Model',fields:[{name:'fullName',type:'string'},{name:'loginName',type:'string'}]});var startUserStore=Ext.create('Ext.data.Store',{autoLoad:true,autoDestroy:true,model:'userModel',proxy:{type:'ajax',url:'icUserList.do',reader:{type:'json',root:'dataList'}}});var fqrComb=Ext.create('Ext.ux.ideal.form.ComboBox',{emptyText:'请选择发起人',fieldLabel:'发起人',queryMode:'local',width:'50%',labelAlign:'right',store:startUserStore,displayField:'fullName',valueField:'fullName',labelWidth:65,listConfig:{minHeight:80}});var audioUserStore=Ext.create('Ext.data.Store',{autoLoad:true,autoDestroy:true,model:'userModel',proxy:{type:'ajax',url:'icUserList.do',reader:{type:'json',root:'dataList'}},listeners:{load:function(){checkAuditor();pageBar.moveFirst();taskNameStore.reload()}}});var zxrComb=Ext.create('Ext.ux.ideal.form.ComboBox',{fieldLabel:'执行人',emptyText:'请选择执行人',width:'50%',labelAlign:'right',queryMode:'local',store:audioUserStore,displayField:'fullName',valueField:'fullName',labelWidth:65,listConfig:{minHeight:80}});function checkAuditor(){var ckname=zxrComb.value;if(ckname==null){zxrComb.setValue('Default User')}else{zxrComb.setValue(backauditorUser)}}var pagelimit=30;var initTime=new Date();initTime.setDate(initTime.getDate()-7);initTime.setHours(0);initTime.setMinutes(0);initTime.setSeconds(0);var startTime_t=Ext.create('Ext.form.field.Date',{fieldLabel:'开始时间',format:'Y-m-d H:i:s',labelWidth:65,labelAlign:'right',width:'50%',value:initTime,});var initEndTime=new Date();initEndTime.setDate(initEndTime.getDate());initEndTime.setHours(23);initEndTime.setMinutes(59);initEndTime.setSeconds(59);var endTime_t=Ext.create('Ext.form.field.Date',{fieldLabel:'至',format:'Y-m-d H:i:s',labelWidth:65,labelAlign:'right',width:'50%',value:initEndTime,});var systemTypeNameForQuery=Ext.create('Ext.form.TextField',{fieldLabel:'分类',labelAlign:'right',readOnly:true,emptyText:'--请选分类--',labelWidth:65,width:'50%',xtype:'textfield'});var systemTypeUUIDForQuery=Ext.create('Ext.form.TextField',{margin:'5',hidden:true,readOnly:true,labelWidth:80,width:160,xtype:'textfield'});var queryButtonForType=Ext.create('Ext.Button',{cls:'Common_Btn',text:'选择分类',handler:queryType});var queryButtonForSearch=Ext.create('Ext.Button',{margin:'0 0 0 5',text:'查询',cls:'Common_Btn',handler:queryWhere});var queryButtonForClear=Ext.create('Ext.Button',{text:'清空',cls:'Common_Btn',margin:'0 0 0 5',handler:queryReset,});var queryButtonForExport=Ext.create('Ext.Button',{text:'总览导出',margin:'0 0 0 5',cls:'Common_Btn',handler:function(){var record=taskHListGrid.getSelectionModel().getSelection();var ids=[];if(record.length!=0){Ext.each(record,function(item){if(item.data.taskId!=0){ids.push(item.data.taskId);ids.join(',')}})}else{Ext.Msg.alert('提示','请选择需要导出的数据!');return}window.location.href='exportTaskHistoryExcel.do?ids='+ids+'&exportType=1'}});var queryButtonForDetailedExport=Ext.create('Ext.Button',{text:'明细导出',margin:'0 0 0 5',cls:'Common_Btn',handler:function(){var record=taskHListGrid.getSelectionModel().getSelection();var ids=[];if(record.length!=0){Ext.each(record,function(item){if(item.data.taskId!=0){ids.push(item.data.taskId);ids.join(',')}})}else{Ext.Msg.alert('提示','请选择需要导出的数据!');return}window.location.href='exportTaskHistoryExcel.do?ids='+ids+'&exportType=2'}});function queryType(){var queryTypeWin=Ext.create('page.webStudio.project.systemTypeWin',{retWin:retTypeWin,listeners:{'close':{fn:systemTypeWinclose}}});queryTypeWin.show()}function queryWhere(){var stTime=startTime_t.getValue();var edTime=endTime_t.getValue();var vsTime=startTime_t.getRawValue();var veTime=endTime_t.getRawValue();console.log(vsTime);if(!isDateFormat(vsTime)){return}if(!isDateFormat(veTime)){return}if(stTime!=null&&stTime!=''&&edTime!=null&&edTime!=''){if(stTime>edTime){Ext.Msg.alert('提示','开始时间不能大于结束时间!');return}}taskHListGrid.ipage.moveFirst();taskNameStore.reload()}function queryReset(){conditionPanel.form.reset();startTime_t.setValue('');endTime_t.setValue('')}function retTypeWin(ret){systemTypeUUIDForQuery.setValue(ret.isystemtypeuuid);systemTypeNameForQuery.setValue(ret.ibusName)}function systemTypeWinclose(me){console.log('me.winRet',me.winRet)}var conditionPanel=Ext.create('Ext.ux.ideal.form.Panel',{region:'north',layout:'anchor',collapsible:true,collapsed:false,buttonAlign:'right',border:false,baseCls:'customize_gray_back',iselect:false,dockedItems:[{xtype:'toolbar',border:false,dock:'top',baseCls:'customize_gray_back',items:[taskComb,task1Comb]},{xtype:'toolbar',border:false,dock:'top',baseCls:'customize_gray_back',items:[fqrComb,zxrComb]},{xtype:'toolbar',border:false,baseCls:'customize_gray_back',dock:'top',items:[startTime_t,endTime_t]},{xtype:'toolbar',border:false,labelAlign:'right',baseCls:'customize_gray_back',dock:'top',items:[systemTypeNameForQuery,systemTypeUUIDForQuery,queryButtonForType,'->',queryButtonForSearch,queryButtonForClear,queryButtonForExport,queryButtonForDetailedExport]}]});var taskStore=Ext.create('Ext.data.Store',{autoLoad:false,autoDestroy:true,pageSize:pagelimit,model:'taskGridModel',proxy:{type:'ajax',url:'queryTaskHistory.do',reader:{type:'json',root:'dataList',totalProperty:'total'}}});var pageBar=Ext.create('Ext.PagingToolbar',{pageSize:pagelimit,store:taskStore,displayInfo:true,displayMsg:'显示{0}-{1}条，共{2}条',baseCls:Ext.baseCSSPrefix+'toolbar customize_toolbar',emptyMsg:'没有数据'});var columns=[{text:'序号',width:40,align:'left',xtype:'rownumberer'},{text:'ID',sortable:true,dataIndex:'taskId',hidden:true},{text:'执行状态',sortable:true,dataIndex:'status',width:100,renderer:function(value,metaData,record){if(value=='运行'){return'<span class="Green_color State_Color">'+value+'</span>'}else if(value=='结束'){return'<span class="Gray_color State_Color">'+value+'</span>'}else if(value=='终止'){return'<span class="Red_color State_Color">'+value+'</span>'}else if(value=='暂停'){return'<span class="Blue_color State_Color">'+value+'</span>'}else if(value=='异常终止'){return'<span class="yellow_color State_Color">'+value+'</span>'}else if(value=='异常'){return'<span  class="yellow_color State_Color">'+value+'</span>'}else if(value=='灾难恢复中'){return'<span class="purple_color State_Color">'+value+'</span>'}else if(value=='灾难恢复失败'){return'<span class="orange_color State_Color">'+value+'</span>'}else{return value}}},{text:'任务名称',sortable:true,dataIndex:'taskName',width:130,flex:1},{text:'分类',sortable:true,dataIndex:'ibusname',width:80},{text:'发起人',sortable:true,dataIndex:'startUser',width:100},{text:'执行人',sortable:true,dataIndex:'performUser',width:100},{text:'单号',sortable:true,dataIndex:'butterflyVersion',width:150,renderer:function(value,metaData,record){var rStatus=record.data.status;var taskId=record.data.taskId;if(rStatus=='结束'||rStatus=='终止'||rStatus=='异常终止'){if(value==null||value==''){try{var time=record.data.startTime;var dateBegin=new Date(time.replace(/-/g,'/'));var dateEnd=new Date();var dateDiff=dateEnd.getTime()-dateBegin.getTime();var dayDiff=Math.floor(dateDiff/(24*3600*1000));if(dayDiff<=3){return'<a onclick="writeVersion("'+taskId+'")">填写单号</a>'}else{return value}}catch(e){}}}return value}},{text:'执行策略',sortable:true,dataIndex:'execStrategy',width:100,renderer:function(value,metaData,record){if(value==1){return'触发执行'}else if(value==2){return'定时执行'}else if(value==3){return'周期执行'}else{return''}}},{text:'开始时间',sortable:true,dataIndex:'startTime',width:120},{text:'结束时间',sortable:true,dataIndex:'endTime',width:120},{text:'运行时长',sortable:true,dataIndex:'runTime',width:80},{text:'操作',sortable:true,dataIndex:'taskId',width:120,renderer:function(value,metaData,record){var taskId=record.get('taskId');var taskName=record.get('taskName');var rStatus=record.data.status;var execStrategy=record.get('execStrategy');var performUser=record.get('performUser');var isHiddenKillBtn=false;if(performUser!=loginName){isHiddenKillBtn=true}var returnStr='<a href="javascript:void(0)"  onclick="showTaskDetile('+isHiddenKillBtn+','+taskId+','+taskName+')">详情</a> ';if((performUser==loginName)&&(execStrategy==2||execStrategy==3)&&rStatus!='终止'&&rStatus!='结束'){returnStr+=' <a href="javascript:void(0)"  onclick="killTask('+taskId+')">终止任务</a>'}else if((performUser==loginName)&&(execStrategy==1)&&rStatus!='终止'&&rStatus!='结束'){returnStr+=' <a href="javascript:void(0)"  onclick="killTriggerTask('+taskId+')">终止任务</a>'}return returnStr}}];var taskHListGrid=Ext.create('Ext.ux.ideal.grid.Panel',{region:'center',border:false,store:taskStore,columns:columns,cls:'customize_panel_back',ipageBaseCls:Ext.baseCSSPrefix+'toolbar customize_toolbar',loadMask:{msg:' 数据加载中，请稍等 ',removeMask:true},selModel:Ext.create('Ext.selection.CheckboxModel',{checkOnly:true})});var mainPanel=Ext.create('Ext.panel.Panel',{renderTo:Ext.get('taskHistoryDiv'),layout:'border',width:'100%',height:'800px',border:false,items:[conditionPanel,taskHListGrid]});