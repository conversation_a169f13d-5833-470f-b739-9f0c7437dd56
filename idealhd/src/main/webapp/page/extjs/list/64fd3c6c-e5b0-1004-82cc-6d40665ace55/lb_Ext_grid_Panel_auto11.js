var keyQuery=Ext.create('Ext.form.TextField',{margin:'5',name:'key',emptyText:'--请输入key--',labelWidth:80,width:'15%',xtype:'textfield'});var extGridPanelStore=Ext.create('Ext.data.Store',{storeId:'simpsonsStore',fields:[{name:'key',type:'String'},{name:'value',type:'String'}],pageSize:10,proxy:{type:'ajax',url:'configList.do',reader:{type:'json',root:'dataList',totalProperty:'total'}}});extGridPanelStore.loadPage(1);function save(){var m=extGridPanelStore.getModifiedRecords();if(m.length==0){Ext.MessageBox.alert('提示','没有需要保存的条目！');return}else{var jsonData='[';for(var i=0;i<m.length;i++){var date=Ext.JSON.encode(m[i].data);if(i==0)jsonData=jsonData+date;else jsonData=jsonData+','+date}jsonData=jsonData+']';Ext.Ajax.request({url:'configSave.do',method:'post',params:{jsonData:jsonData},success:function(response,request){var success=Ext.decode(response.responseText).success;if(true==success){extGridPanelStore.reload({params:{start:0,limit:10,page:1}});Ext.Msg.alert('提示','保存成功！')}else{Ext.Msg.alert('提示','保存失败！')}},failure:function(result,request){secureFilterRs(result,'操作失败！')}})}}function QueryMessage(){extGridPanelStore.reload({params:{start:0,limit:10,page:1,keyPar:keyQuery.getValue().trim()}})}Ext.create('Ext.grid.Panel',{store:Ext.data.StoreManager.lookup('simpsonsStore'),columns:[{text:'序号',width:35,xtype:'rownumberer'},{text:'key',dataIndex:'key',flex:1},{text:'value',dataIndex:'value',flex:1,editor:{xtype:'textfield'}}],plugins:[Ext.create('Ext.grid.plugin.CellEditing',{clicksToEdit:1})],dockedItems:[{xtype:'pagingtoolbar',store:extGridPanelStore,dock:'bottom',displayInfo:true},{xtype:'toolbar',dock:'top',items:[keyQuery,{xtype:'button',cls:'Common_Btn',text:'查询',handler:function(){QueryMessage()}},'->',{xtype:'button',cls:'Common_Btn',text:'保存',handler:save}]}],width:'100%',renderTo:'lb_Ext_grid_Panel_auto11'});