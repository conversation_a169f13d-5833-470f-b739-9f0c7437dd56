var whichComponentSite2_3=1;
function makePartSite(e){
	
	$('.lbPosBox').children('.lbSortableBox').remove();
	if(e.parent('.configuration').siblings('.preview').text()=='grid2.3'){//判断当前点击按钮的对应模板是哪个
		console.log('grid2.3');
		if($("#makeSiteBox2_3").length>1){
			$("#makeSiteBox2_3").remove();
		}else if($("#makeSiteBox2_3").length==0){
			e.siblings('.lbPosBox').css('flex-direction','column').prepend('<div style="color:#fff;margin-right:10px;" id="makeSiteBox2_3">更改右侧组件<input style="margin:0;width:20px;"name="Fruit" type="checkbox" value=""  /></div>');
		}
		
		$("#makeSiteBox2_3").children('input').click(function(){
			if($("#makeSiteBox2_3").children('input')[0].checked){
				$('.lbPosBox').children('.lbSortableBox').remove();
				var localStorageId=e.parent('.configuration').siblings('.view').attr('codeid');
				var mainCode=localStorage.getItem(localStorageId);
				var objCode='';
				function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
					var v1=code.indexOf(str1,position);//获取关健字的位置
					if(v1==-1){Ext.Msg.alert('查询失败','没有此项');return false}
					var v2=code.indexOf('=',v1);
					var v3=code.lastIndexOf('var',v2);
					
					var checkV1=code.indexOf(code.substring(v3,v2));
					var checkV2=code.indexOf('});',checkV1)+3;
					var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
					console.log(checkV3);
					if(checkV3.indexOf("Ext.create('Ext.grid.Panel',") >0){
						objCode=checkV3
					}else{
						strGetObjCode(mainCode,'var groupInfo_grid',v1+1);
					}
				}
				strGetObjCode(mainCode,'var groupInfo_grid');
				console.log(objCode);
				
				//获取对象代码中的items组件数组
				var s1=objCode.indexOf('items',0);
				s1=objCode.indexOf('[',s1)+1;
				var s2=objCode.indexOf(']',s1);
				var objArr=(objCode.substring(s1,s2)).split(",");
				console.log(objArr);
				$('.lbPosBox').append('<div class="lbSortableBox"></div>');
				for(var i=0;i<objArr.length;i++){
					console.log(objArr[i]);
					if(objArr[i]=="'->'"){
						var button="<span class='arrAutoButtonSplit'id='decollate20191108'></span>";
					}else if(objArr[i]==""){
						continue
					}else{
						var button="<span class='arrAutoButton'id='"+objArr[i]+"'></span>";
					}
					
					$('.lbSortableBox').append(button);
					
				}
				$('.lbPosBox').append('<style>.lbPosBox{padding:20px;}.arrAutoButtonSplit{display: block;float:left;width:8px;height:8px;border-radius:50%;background:red;box-shadow: 0px 0px 5px 2px red;margin:10px;cursor:pointer;}.arrAutoButtonSplit:hover{background:red;box-shadow:0px 0px 5px 2px red;}.arrAutoButton{display: block;float:left;width:8px;height:8px;border-radius:50%;background:#59ff04;margin:10px;cursor:pointer;}.arrAutoButton:hover{background:#59ff04;box-shadow:0px 0px 1px 1px #59ff04;}</style>');
				
				console.log($('.lbSortableBox'));
				$('.lbSortableBox').sortable({
					revert: false,
					start: function( event, ui ) {console.log('start');
						
					},
					stop: function( event, ui ) {console.log('stop');
						console.log($(event.target).children());
						var changeArr=[];
						for(var i=0;i<$(event.target).children().length;i++){
							if($(event.target).children().eq(i).attr('id')=='decollate20191108'){
								$(event.target).children().eq(i).attr('id',"'->'");
							}
							changeArr.push($(event.target).children().eq(i).attr('id'));
							
						}
						console.log(changeArr);
						changeArr='['+changeArr.join(',')+']';
						
						function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
							var v1=code.indexOf(changeVal2,0);//获取关健字的位置
							var v2=code.indexOf(":",v1)+1;
							var v3=code.indexOf("]",v2)+1;
							if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
								v3=code.indexOf("});",v2);
							}
							console.log(code.substring(0,v2));
							console.log(changeVal3);
							console.log(code.substring(v3));
							return code.substring(0,v2)+changeVal3+code.substring(v3)
						}
						var newObjCode=changeFinalCode(objCode,'items',changeArr);
						console.log(newObjCode);
						
						function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
							//找到对象代码中的变量名称 
							var v1=newObjCode.indexOf('var',0);
							var v2=newObjCode.indexOf('=',v1);
							var objName=newObjCode.substring(v1,v2);
							var vv1=localCode.indexOf(objName,0);
							var vv2=localCode.indexOf("});",vv1)+3;
							return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
						}
						mainCode=changeObjCod(mainCode,newObjCode);
						if(!mainCode){return}
						console.log(localStorageId);
						//组件重新渲染三部曲 
						$('#'+localStorageId).children().remove();
						localStorage.setItem(localStorageId,mainCode);
						eval(localStorage.getItem(localStorageId));
						
						
						if(myWindow){
							myWindow.close();//关闭编辑代码窗口
						}
					}
				});	
			    $(".lbSortableBox").disableSelection();
				whichComponentSite2_3=2;
			}else{
				$('.lbPosBox').children('.lbSortableBox').remove();
				var localStorageId=e.parent('.configuration').siblings('.view').attr('codeid');
				var mainCode=localStorage.getItem(localStorageId);
				var objCode='';
				function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
					var v1=code.indexOf(str1,position);//获取关健字的位置
					if(v1==-1){Ext.Msg.alert('查询失败','没有此项');return false}
					var v2=code.indexOf('=',v1);
					var v3=code.lastIndexOf('var',v2);
					
					var checkV1=code.indexOf(code.substring(v3,v2));
					var checkV2=code.indexOf('});',checkV1)+3;
					var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
					console.log(checkV3);
					if(checkV3.indexOf("Ext.create('Ext.grid.Panel',") >0){
						objCode=checkV3
					}else{
						strGetObjCode(mainCode,'var groupName_grid',v1+1);
					}
				}
				strGetObjCode(mainCode,'var groupName_grid');
				console.log(objCode);
				
				//获取对象代码中的items组件数组
				var s1=objCode.indexOf('items',0);
				s1=objCode.indexOf('[',s1)+1;
				var s2=objCode.indexOf(']',s1);
				var objArr=(objCode.substring(s1,s2)).split(",");
				console.log(objArr);
				$('.lbPosBox').append('<div class="lbSortableBox"></div>');
				for(var i=0;i<objArr.length;i++){
					console.log(objArr[i]);
					if(objArr[i]=="'->'"){
						var button="<span class='arrAutoButtonSplit'id='decollate20191108'></span>";
					}else if(objArr[i]==""){
						continue
					}else{
						var button="<span class='arrAutoButton'id='"+objArr[i]+"'></span>";
					}
					
					$('.lbSortableBox').append(button);
					
				}
				$('.lbPosBox').append('<style>.lbPosBox{padding:20px;}.arrAutoButtonSplit{display: block;float:left;width:8px;height:8px;border-radius:50%;background:red;box-shadow: 0px 0px 5px 2px red;margin:10px;cursor:pointer;}.arrAutoButtonSplit:hover{background:red;box-shadow:0px 0px 5px 2px red;}.arrAutoButton{display: block;float:left;width:8px;height:8px;border-radius:50%;background:#59ff04;margin:10px;cursor:pointer;}.arrAutoButton:hover{background:#59ff04;box-shadow:0px 0px 1px 1px #59ff04;}</style>');
				
				console.log($('.lbSortableBox'));
				$('.lbSortableBox').sortable({
					revert: false,
					start: function( event, ui ) {console.log('start');
						
					},
					stop: function( event, ui ) {console.log('stop');
						console.log($(event.target).children());
						var changeArr=[];
						for(var i=0;i<$(event.target).children().length;i++){
							if($(event.target).children().eq(i).attr('id')=='decollate20191108'){
								$(event.target).children().eq(i).attr('id',"'->'");
							}
							changeArr.push($(event.target).children().eq(i).attr('id'));
							
						}
						console.log(changeArr);
						changeArr='['+changeArr.join(',')+']';
						
						function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
							var v1=code.indexOf(changeVal2,0);//获取关健字的位置
							var v2=code.indexOf(":",v1)+1;
							var v3=code.indexOf("]",v2)+1;
							if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
								v3=code.indexOf("});",v2);
							}
							console.log(code.substring(0,v2));
							console.log(changeVal3);
							console.log(code.substring(v3));
							return code.substring(0,v2)+changeVal3+code.substring(v3)
						}
						var newObjCode=changeFinalCode(objCode,'items',changeArr);
						console.log(newObjCode);
						
						function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
							//找到对象代码中的变量名称 
							var v1=newObjCode.indexOf('var',0);
							var v2=newObjCode.indexOf('=',v1);
							var objName=newObjCode.substring(v1,v2);
							var vv1=localCode.indexOf(objName,0);
							var vv2=localCode.indexOf("});",vv1)+3;
							return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
						}
						mainCode=changeObjCod(mainCode,newObjCode);
						if(!mainCode){return}
						console.log(localStorageId);
						//组件重新渲染三部曲 
						$('#'+localStorageId).children().remove();
						localStorage.setItem(localStorageId,mainCode);
						eval(localStorage.getItem(localStorageId));
						
						
						if(myWindow){
							myWindow.close();//关闭编辑代码窗口
						}
					}
				});	
			    $(".lbSortableBox").disableSelection();
				whichComponentSite2_3=1;
			}
		});
		
		if(whichComponentSite2_3==1){
			var localStorageId=e.parent('.configuration').siblings('.view').attr('codeid');
			var mainCode=localStorage.getItem(localStorageId);
			var objCode='';
			function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
				var v1=code.indexOf(str1,position);//获取关健字的位置
				if(v1==-1){Ext.Msg.alert('查询失败','没有此项');return false}
				var v2=code.indexOf('=',v1);
				var v3=code.lastIndexOf('var',v2);
				
				var checkV1=code.indexOf(code.substring(v3,v2));
				var checkV2=code.indexOf('});',checkV1)+3;
				var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
				console.log(checkV3);
				if(checkV3.indexOf("Ext.create('Ext.grid.Panel',") >0){
					objCode=checkV3
				}else{
					strGetObjCode(mainCode,'var groupName_grid',v1+1);
				}
			}
			strGetObjCode(mainCode,'var groupName_grid');
			console.log(objCode);
			
			//获取对象代码中的items组件数组
			var s1=objCode.indexOf('items',0);
			s1=objCode.indexOf('[',s1)+1;
			var s2=objCode.indexOf(']',s1);
			var objArr=(objCode.substring(s1,s2)).split(",");
			console.log(objArr);
			$('.lbPosBox').append('<div class="lbSortableBox"></div>');
			for(var i=0;i<objArr.length;i++){
				console.log(objArr[i]);
				if(objArr[i]=="'->'"){
					var button="<span class='arrAutoButtonSplit'id='decollate20191108'></span>";
				}else if(objArr[i]==""){
					continue
				}else{
					var button="<span class='arrAutoButton'id='"+objArr[i]+"'></span>";
				}
				
				$('.lbSortableBox').append(button);
				
			}
			$('.lbPosBox').append('<style>.lbPosBox{padding:20px;}.arrAutoButtonSplit{display: block;float:left;width:8px;height:8px;border-radius:50%;background:red;box-shadow: 0px 0px 5px 2px red;margin:10px;cursor:pointer;}.arrAutoButtonSplit:hover{background:red;box-shadow:0px 0px 5px 2px red;}.arrAutoButton{display: block;float:left;width:8px;height:8px;border-radius:50%;background:#59ff04;margin:10px;cursor:pointer;}.arrAutoButton:hover{background:#59ff04;box-shadow:0px 0px 1px 1px #59ff04;}</style>');
			
			console.log($('.lbSortableBox'));
			$('.lbSortableBox').sortable({
				revert: false,
				start: function( event, ui ) {console.log('start');
					
				},
				stop: function( event, ui ) {console.log('stop');
					console.log($(event.target).children());
					var changeArr=[];
					for(var i=0;i<$(event.target).children().length;i++){
						if($(event.target).children().eq(i).attr('id')=='decollate20191108'){
							$(event.target).children().eq(i).attr('id',"'->'");
						}
						changeArr.push($(event.target).children().eq(i).attr('id'));
						
					}
					console.log(changeArr);
					changeArr='['+changeArr.join(',')+']';
					
					function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
						var v1=code.indexOf(changeVal2,0);//获取关健字的位置
						var v2=code.indexOf(":",v1)+1;
						var v3=code.indexOf("]",v2)+1;
						if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
							v3=code.indexOf("});",v2);
						}
						console.log(code.substring(0,v2));
						console.log(changeVal3);
						console.log(code.substring(v3));
						return code.substring(0,v2)+changeVal3+code.substring(v3)
					}
					var newObjCode=changeFinalCode(objCode,'items',changeArr);
					console.log(newObjCode);
					
					function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
						//找到对象代码中的变量名称 
						var v1=newObjCode.indexOf('var',0);
						var v2=newObjCode.indexOf('=',v1);
						var objName=newObjCode.substring(v1,v2);
						var vv1=localCode.indexOf(objName,0);
						var vv2=localCode.indexOf("});",vv1)+3;
						return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
					}
					mainCode=changeObjCod(mainCode,newObjCode);
					if(!mainCode){return}
					console.log(localStorageId);
					//组件重新渲染三部曲 
					$('#'+localStorageId).children().remove();
					localStorage.setItem(localStorageId,mainCode);
					eval(localStorage.getItem(localStorageId));
					
					
					if(myWindow){
						myWindow.close();//关闭编辑代码窗口
					}
				}
			});	
		    $(".lbSortableBox").disableSelection();
		}else{
			var localStorageId=e.parent('.configuration').siblings('.view').attr('codeid');
			var mainCode=localStorage.getItem(localStorageId);
			var objCode='';
			function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
				var v1=code.indexOf(str1,position);//获取关健字的位置
				if(v1==-1){Ext.Msg.alert('查询失败','没有此项');return false}
				var v2=code.indexOf('=',v1);
				var v3=code.lastIndexOf('var',v2);
				
				var checkV1=code.indexOf(code.substring(v3,v2));
				var checkV2=code.indexOf('});',checkV1)+3;
				var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
				console.log(checkV3);
				if(checkV3.indexOf("Ext.create('Ext.grid.Panel',") >0){
					objCode=checkV3
				}else{
					strGetObjCode(mainCode,'var groupInfo_grid',v1+1);
				}
			}
			strGetObjCode(mainCode,'var groupInfo_grid');
			console.log(objCode);
			
			//获取对象代码中的items组件数组
			var s1=objCode.indexOf('items',0);
			s1=objCode.indexOf('[',s1)+1;
			var s2=objCode.indexOf(']',s1);
			var objArr=(objCode.substring(s1,s2)).split(",");
			console.log(objArr);
			$('.lbPosBox').append('<div class="lbSortableBox"></div>');
			for(var i=0;i<objArr.length;i++){
				console.log(objArr[i]);
				if(objArr[i]=="'->'"){
					var button="<span class='arrAutoButtonSplit'id='decollate20191108'></span>";
				}else if(objArr[i]==""){
					continue
				}else{
					var button="<span class='arrAutoButton'id='"+objArr[i]+"'></span>";
				}
				
				$('.lbSortableBox').append(button);
				
			}
			$('.lbPosBox').append('<style>.lbPosBox{padding:20px;}.arrAutoButtonSplit{display: block;float:left;width:8px;height:8px;border-radius:50%;background:red;box-shadow: 0px 0px 5px 2px red;margin:10px;cursor:pointer;}.arrAutoButtonSplit:hover{background:red;box-shadow:0px 0px 5px 2px red;}.arrAutoButton{display: block;float:left;width:8px;height:8px;border-radius:50%;background:#59ff04;margin:10px;cursor:pointer;}.arrAutoButton:hover{background:#59ff04;box-shadow:0px 0px 1px 1px #59ff04;}</style>');
			
			console.log($('.lbSortableBox'));
			$('.lbSortableBox').sortable({
				revert: false,
				start: function( event, ui ) {console.log('start');
					
				},
				stop: function( event, ui ) {console.log('stop');
					console.log($(event.target).children());
					var changeArr=[];
					for(var i=0;i<$(event.target).children().length;i++){
						if($(event.target).children().eq(i).attr('id')=='decollate20191108'){
							$(event.target).children().eq(i).attr('id',"'->'");
						}
						changeArr.push($(event.target).children().eq(i).attr('id'));
						
					}
					console.log(changeArr);
					changeArr='['+changeArr.join(',')+']';
					
					function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
						var v1=code.indexOf(changeVal2,0);//获取关健字的位置
						var v2=code.indexOf(":",v1)+1;
						var v3=code.indexOf("]",v2)+1;
						if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
							v3=code.indexOf("});",v2);
						}
						console.log(code.substring(0,v2));
						console.log(changeVal3);
						console.log(code.substring(v3));
						return code.substring(0,v2)+changeVal3+code.substring(v3)
					}
					var newObjCode=changeFinalCode(objCode,'items',changeArr);
					console.log(newObjCode);
					
					function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
						//找到对象代码中的变量名称 
						var v1=newObjCode.indexOf('var',0);
						var v2=newObjCode.indexOf('=',v1);
						var objName=newObjCode.substring(v1,v2);
						var vv1=localCode.indexOf(objName,0);
						var vv2=localCode.indexOf("});",vv1)+3;
						return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
					}
					mainCode=changeObjCod(mainCode,newObjCode);
					if(!mainCode){return}
					console.log(localStorageId);
					//组件重新渲染三部曲 
					$('#'+localStorageId).children().remove();
					localStorage.setItem(localStorageId,mainCode);
					eval(localStorage.getItem(localStorageId));
					
					
					if(myWindow){
						myWindow.close();//关闭编辑代码窗口
					}
				}
			});	
		    $(".lbSortableBox").disableSelection();
		}
		
	}
	if(e.parent('.configuration').siblings('.preview').text()=='grid2.2'){//判断当前点击按钮的对应模板是哪个
		console.log('grid2.2');
		var localStorageId=e.parent('.configuration').siblings('.view').attr('codeid');
		var mainCode=localStorage.getItem(localStorageId);
		var objCode='';
		function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
			var v1=code.indexOf(str1,position);//获取关健字的位置
			if(v1==-1){Ext.Msg.alert('查询失败','没有此项');return false}
			var v2=code.lastIndexOf('=',v1);
			var v3=code.lastIndexOf('var',v2);
			
			var checkV1=code.indexOf(code.substring(v3,v2));
			var checkV2=code.indexOf('});',checkV1)+3;
			var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
			console.log(checkV3);
			if(checkV3.indexOf("Ext.create('Ext.form.Panel',") >0){
				objCode=checkV3
			}else{
				strGetObjCode(mainCode,'form',v1+1);
			}
		}
		strGetObjCode(mainCode,'form');
		console.log(objCode);
		
		//获取对象代码中的items组件数组
		var s1=objCode.indexOf('items',0);
		s1=objCode.indexOf('[',s1)+1;
		var s2=objCode.indexOf(']',s1);
		var objArr=(objCode.substring(s1,s2)).split(",");
		console.log(objArr);
		$('.lbPosBox').append('<div class="lbSortableBox"></div>');
		for(var i=0;i<objArr.length;i++){
			console.log(objArr[i]);
			if(objArr[i]=="'->'" || objArr[i]==" '->'"){
				var button="<span class='arrAutoButtonSplit'id='decollate20191108'></span>";
			}else if(objArr[i]==""){
				continue
			}else{
				var button="<span class='arrAutoButton'id='"+objArr[i]+"'></span>";
			}
			
			$('.lbSortableBox').append(button);
			
		}
		$('.lbPosBox').append('<style>.lbPosBox{padding:20px;}.arrAutoButtonSplit{display: block;float:left;width:8px;height:8px;border-radius:50%;background:red;box-shadow: 0px 0px 5px 2px red;margin:10px;cursor:pointer;}.arrAutoButtonSplit:hover{background:red;box-shadow:0px 0px 5px 2px red;}.arrAutoButton{display: block;float:left;width:8px;height:8px;border-radius:50%;background:#59ff04;margin:10px;cursor:pointer;}.arrAutoButton:hover{background:#59ff04;box-shadow:0px 0px 1px 1px #59ff04;}</style>');
		
		console.log($('.lbSortableBox'));
		$('.lbSortableBox').sortable({
			revert: false,
			start: function( event, ui ) {console.log('start');
				
			},
			stop: function( event, ui ) {console.log('stop');
				console.log($(event.target).children());
				var changeArr=[];
				for(var i=0;i<$(event.target).children().length;i++){
					if($(event.target).children().eq(i).attr('id')=='decollate20191108'){
						$(event.target).children().eq(i).attr('id',"'->'");
					}
					changeArr.push($(event.target).children().eq(i).attr('id'));
					
				}
				console.log(changeArr);
				changeArr='['+changeArr.join(',')+']';
				
				function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
					var v1=code.indexOf(changeVal2,0);//获取关健字的位置
					var v2=code.indexOf(":",v1)+1;
					var v3=code.indexOf("]",v2)+1;
					if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
						v3=code.indexOf("});",v2);
					}
					console.log(code.substring(0,v2));
					console.log(changeVal3);
					console.log(code.substring(v3));
					return code.substring(0,v2)+changeVal3+code.substring(v3)
				}
				var newObjCode=changeFinalCode(objCode,'items',changeArr);
				console.log(newObjCode);
				
				function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
					//找到对象代码中的变量名称 
					var v1=newObjCode.indexOf('var',0);
					var v2=newObjCode.indexOf('=',v1);
					var objName=newObjCode.substring(v1,v2);
					var vv1=localCode.indexOf(objName,0);
					var vv2=localCode.indexOf("});",vv1)+3;
					return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
				}
				mainCode=changeObjCod(mainCode,newObjCode);
				if(!mainCode){return}
				console.log(localStorageId);
				//组件重新渲染三部曲 
				$('#'+localStorageId).children().remove();
				localStorage.setItem(localStorageId,mainCode);
				eval(localStorage.getItem(localStorageId));
				
				
				if(myWindow){
					myWindow.close();//关闭编辑代码窗口
				}
			}
		});	
	    $(".lbSortableBox").disableSelection();
	
	}
	if(e.parent('.configuration').siblings('.preview').text()=='grid1.0'){//判断当前点击按钮的对应模板是哪个
		console.log('grid2.2');
		var localStorageId=e.parent('.configuration').siblings('.view').attr('codeid');
		var mainCode=localStorage.getItem(localStorageId);
		var objCode='';
		function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
			var v1=code.indexOf(str1,position);//获取关健字的位置
			if(v1==-1){Ext.Msg.alert('查询失败','没有此项');return false}
			var v2=code.lastIndexOf('=',v1);
			var v3=code.lastIndexOf('var',v2);
			
			var checkV1=code.indexOf(code.substring(v3,v2));
			var checkV2=code.indexOf('});',checkV1)+3;
			var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
			console.log(checkV3);
			if(checkV3.indexOf("Ext.create('Ext.ux.ideal.form.Panel',") >0){
				objCode=checkV3
			}else{
				strGetObjCode(mainCode,'conditionPanel',v1+1);
			}
		}
		strGetObjCode(mainCode,'conditionPanel');
		console.log(objCode);
		
		//获取对象代码中的items组件数组
		var s1=objCode.indexOf('items',0);
		s1=objCode.indexOf('[',s1)+1;
		var s2=objCode.indexOf(']',s1);
		var objArr=(objCode.substring(s1,s2)).split(",");
		console.log(objArr);
		$('.lbPosBox').append('<div class="lbSortableBox"></div>');
		for(var i=0;i<objArr.length;i++){
			console.log(objArr[i]);
			if(objArr[i]=="'->'"){
				var button="<span class='arrAutoButtonSplit'id='decollate20191108'></span>";
			}else if(objArr[i]==""){
				continue
			}else{
				var button="<span class='arrAutoButton'id='"+objArr[i]+"'></span>";
			}
			
			$('.lbSortableBox').append(button);
			
		}
		$('.lbPosBox').append('<style>.lbPosBox{padding:20px;}.arrAutoButtonSplit{display: block;float:left;width:8px;height:8px;border-radius:50%;background:red;box-shadow: 0px 0px 5px 2px red;margin:10px;cursor:pointer;}.arrAutoButtonSplit:hover{background:red;box-shadow:0px 0px 5px 2px red;}.arrAutoButton{display: block;float:left;width:8px;height:8px;border-radius:50%;background:#59ff04;margin:10px;cursor:pointer;}.arrAutoButton:hover{background:#59ff04;box-shadow:0px 0px 1px 1px #59ff04;}</style>');
		
		console.log($('.lbSortableBox'));
		$('.lbSortableBox').sortable({
			revert: false,
			start: function( event, ui ) {console.log('start');
				
			},
			stop: function( event, ui ) {console.log('stop');
				console.log($(event.target).children());
				var changeArr=[];
				for(var i=0;i<$(event.target).children().length;i++){
					if($(event.target).children().eq(i).attr('id')=='decollate20191108'){
						$(event.target).children().eq(i).attr('id',"'->'");
					}
					changeArr.push($(event.target).children().eq(i).attr('id'));
					
				}
				console.log(changeArr);
				changeArr='['+changeArr.join(',')+']';
				
				function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
					var v1=code.indexOf(changeVal2,0);//获取关健字的位置
					var v2=code.indexOf(":",v1)+1;
					var v3=code.indexOf("]",v2)+1;
					if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
						v3=code.indexOf("});",v2);
					}
					console.log(code.substring(0,v2));
					console.log(changeVal3);
					console.log(code.substring(v3));
					return code.substring(0,v2)+changeVal3+code.substring(v3)
				}
				var newObjCode=changeFinalCode(objCode,'items',changeArr);
				console.log(newObjCode);
				
				function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
					//找到对象代码中的变量名称 
					var v1=newObjCode.indexOf('var',0);
					var v2=newObjCode.indexOf('=',v1);
					var objName=newObjCode.substring(v1,v2);
					var vv1=localCode.indexOf(objName,0);
					var vv2=localCode.indexOf("});",vv1)+3;
					return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
				}
				mainCode=changeObjCod(mainCode,newObjCode);
				if(!mainCode){return}
				console.log(localStorageId);
				//组件重新渲染三部曲 
				$('#'+localStorageId).children().remove();
				localStorage.setItem(localStorageId,mainCode);
				eval(localStorage.getItem(localStorageId));
				
				
				if(myWindow){
					myWindow.close();//关闭编辑代码窗口
				}
			}
		});	
	    $(".lbSortableBox").disableSelection();
	
	}
}