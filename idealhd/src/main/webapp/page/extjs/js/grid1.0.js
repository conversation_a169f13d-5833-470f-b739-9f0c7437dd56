function makeButtonSelectBox1_0(e){
	console.log('aa1.0');
	//删除项
	//先删除原来的组件 然后创建新的
	if($(e.target).siblings('.lbConfigButtonBox').find('.submitDelConfig').siblings('div').children('table').length==1){
		$(e.target).siblings('.lbConfigButtonBox').find('.submitDelConfig').siblings('div').empty();
	}
	$(e.target).siblings('.lbConfigButtonBox').find('.submitDelConfig').siblings('div').attr('id','configDeleteButton20191121').append("<style>#configDeleteButton20191121 *{border:none;}</style>");
	var statesArr=[];
	var finalArr=[];
	(function(){
		var codeId=$(e.target).parent().siblings('.view').attr('codeid');
		var mainCode=localStorage.getItem(codeId);
		function getMainItems(code,objName){//获取主要panel里的item变量名 返回一个数组  参数1：总代吗  2：主panel变量名称(必须唯一)
			var v1=code.indexOf(objName,0);
			v1=code.indexOf('items',v1+1);
			v1=code.indexOf('[',v1+1)+1;
			var v2=code.indexOf(']',v1);
			console.log(code.substring(v1,v2));
			return code.substring(v1,v2)	
		}
		console.log(getMainItems(mainCode,'var conditionPanel'));
		var itemsArr=getMainItems(mainCode,'var conditionPanel').split(',');
		for(var i=0;i<itemsArr.length;i++){
			itemsArr[i]=itemsArr[i].trim();
		}
		console.log(itemsArr);
		(function(){//剔除不是按钮的项
			for(var i=0;i<itemsArr.length;){
				console.log(i);
				if(itemsArr[i]=="'->'"||itemsArr[i]==" '->'"){
					itemsArr.splice(i,1);
				}
				var v1=mainCode.indexOf("var "+itemsArr[i],0);
				var v2=mainCode.indexOf("});",v1);
				
				console.log(mainCode.substring(v1,v2));
				if(mainCode.substring(v1,v2).indexOf("Ext.create('Ext.Button',")==-1){
					itemsArr.splice(i,1);
					i=0;
				}else{i++}
			}
		})();
		console.log(itemsArr);
		for(var i=0;i<itemsArr.length;i++){
			finalArr[i]={"name":itemsArr[i]};
		}
		console.log(finalArr);
	})();
	
	var states = Ext.create('Ext.data.Store', {
	    fields: ['name'],	
	    data : finalArr
	});

	
	Ext.create('Ext.form.ComboBox', {
	    store: states,
	    queryMode: 'local',
	    emptyText:'name',
	    displayField: 'name',
	    valueField: 'name',
	    editable:false,
	    renderTo: Ext.get('configDeleteButton20191121')
	});
	//修改项
	//先删除原来的组件 然后创建新的
	if($(e.target).siblings('.lbConfigButtonBox').find('.submitButtonConfigChange').siblings('div').children('table').length==1){
		$(e.target).siblings('.lbConfigButtonBox').find('.submitButtonConfigChange').siblings('div').empty();
	}
	$(e.target).siblings('.lbConfigButtonBox').find('.submitButtonConfigChange').siblings('div').attr('id','configChangeButton20191122').append("<style>#configChangeButton20191122 *{border:none;}</style>");
	var statesArr=[];
	var finalArr=[];
	(function(){
		var codeId=$(e.target).parent().siblings('.view').attr('codeid');
		var mainCode=localStorage.getItem(codeId);
		function getMainItems(code,objName){//获取主要panel里的item变量名 返回一个数组  参数1：总代吗  2：主panel变量名称(必须唯一)
			var v1=code.indexOf(objName,0);
			v1=code.indexOf('items',v1+1);
			v1=code.indexOf('[',v1+1)+1;
			var v2=code.indexOf(']',v1);
			console.log(code.substring(v1,v2));
			return code.substring(v1,v2)
		}
		console.log(getMainItems(mainCode,'var conditionPanel'));
		var itemsArr=getMainItems(mainCode,'var conditionPanel').split(',');
		
		for(var i=0;i<itemsArr.length;i++){
			itemsArr[i]=itemsArr[i].trim();
		}
		
		(function(){//剔除不是按钮的项
			for(var i=0;i<itemsArr.length;){
				if(itemsArr[i]=="'->'"||itemsArr[i]==" '->'"){
					itemsArr.splice(i,1);
				}
				var v1=mainCode.indexOf("var "+itemsArr[i],0);
				var v2=mainCode.indexOf("});",v1);
				console.log(mainCode.substring(v1,v2));
				if(mainCode.substring(v1,v2).indexOf("Ext.create('Ext.Button',")==-1){
					
					itemsArr.splice(i,1);
					i=0;
				}else{
					i++
				}
			}
		})();
		console.log(itemsArr);
		for(var i=0;i<itemsArr.length;i++){
			finalArr[i]={"name":itemsArr[i]}
		}
		console.log(finalArr);
	})();
	
	var states = Ext.create('Ext.data.Store', {
	    fields: ['name'],	
	    data : finalArr
	});

	
	Ext.create('Ext.form.ComboBox', {
	    store: states,
	    queryMode: 'local',
	    emptyText:'name',
	    editable:false,
	    displayField: 'name',
	    valueField: 'name',
	    renderTo: Ext.get('configChangeButton20191122')
	});
}
function grid1_0ButtonDelete(e,delVal,codeid,localCode){
	if(delVal==''){
		return
	}
	function strGetObjName(code,str1,position=0){//查询指定字符的对象名称 参数:1在哪个字符串里查 2要查询的关键字 
		str1=str1.trim();
		var v1=code.indexOf(str1,position);//获取关健字的位置x
		console.log(v1);
		console.log(str1);
		
		if(v1==-1){
			Ext.Msg.alert('删除失败','没有此项');
			$('#configDeleteButton20191121').find('input').val('');
			return false
		}
		var v2=code.indexOf('=',v1);
		var v3=code.lastIndexOf('var',v2)+4;
		//检查当前返回的变量名后面是否是Ext.form.TextField对象
		var checkV1=code.indexOf(' '+code.substring(v3,v2));
		console.log(checkV1);
		var checkV2=code.indexOf('});',checkV1);
		var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
		
		console.log(checkV3);
		if(checkV3.indexOf("Ext.create('Ext.Button',") == -1){//如果检查不包含Ext.button 则返回false 不删除 因为不是button组件 此事件只能删除button组件
			strGetObjName(code,str1,v1+1);
			//Ext.Msg.alert('删除失败1','没有此项');return false
		}else{
			console.log(code.substring(v3,v2));
			final=code.substring(v3,v2);
			return true
		}
		
	}
	var final='';
	var oneStep=true;
	oneStep=strGetObjName(localCode,delVal); //获取当前删除的对象名称
	console.log(final);
	if(final){
		final=final.trim();
	}
	if(oneStep){
		function strDelItemObjName(code,ObjName){//删除item的对象名称和对象代码和事件函数体  返回新代码 参数 1字符串2要删除的对象名称
			console.log(ObjName);
			var objNameLen=ObjName.length;
			var v1=code.indexOf('var conditionPanel',0);
			v1=code.indexOf('items',v1);
			var v2=code.indexOf(ObjName,v1);
			v2=code.lastIndexOf(',',v2);//获取前面逗号位置 因为逗号也要删除
			
			if(v2<v1){//如果要删除的前面没有逗号 是第一个
				v2=code.indexOf('[',v1)+1;
			}
			var v3=code.indexOf(',',v2+1);//获取后面逗号位置
			console.log(v3);
			if(v3-v2-objNameLen>6){//那就是数组中的最后一个需要删除
				v3=code.indexOf(']',v2+1)
			}
			var v4=code.substring(0,v2)+code.substring(v3);
			var v11=v4.indexOf(ObjName,0)-4;
			var v22=v4.indexOf('});',v11)+3;
			var v33=v4.substring(0,v11)+v4.substring(v22);
			//删除事件函数体
			console.log(v33);
			return v33
		}
		localCode=strDelItemObjName(localCode,final);
		if(!localCode){return}
		$('#configDeleteButton20191121').find('input').val('');
		//组件重新渲染三部曲 
		$('#'+codeid).children().remove();
		localStorage.setItem(codeid,localCode);
		eval(localStorage.getItem(codeid));
		
		
		$('.lbConfigDelBox').css('display','none');
		$(e.target).siblings('input').val('');
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}
	}
}


function grid1_0ButtonChange(localCode,changeVal,changeVal2,changeVal3,codeid){
	var oneStep=true;
	function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
		console.log(str1);
		var v1=code.indexOf(str1,position);//获取关健字的位置
		if(v1==-1){Ext.Msg.alert('修改失败','没有此项');oneStep=false;return false}
		var v2=code.lastIndexOf('=',v1);
		var v3=code.lastIndexOf('var',v2);
		
		var checkV1=code.indexOf(code.substring(v3,v2));
		var checkV2=code.indexOf('});',checkV1)+3;
		var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
		console.log(checkV3);
		if(checkV3.indexOf("Ext.create('Ext.Button',") >0){
			changeObjCode=checkV3
			oneStep=true;
		}else{
			strGetObjCode(localCode,changeVal,v1+1);
		}
	}
	var changeObjCode='';
	strGetObjCode(localCode,changeVal);
	console.log(changeObjCode);
	if(oneStep){
		function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
			var v1=code.indexOf(changeVal2,0);//获取关健字的位置
			var v2=code.indexOf(":",v1)+1;
			var v3=code.indexOf(",",v2);
			if(v3==-1){//找到的是最后一个属性 恰好后面没有逗号
				v3=code.indexOf("});",v2);
			}
			return code.substring(0,v2)+"'"+changeVal3+"'"+code.substring(v3)
		}
		var newObjCode=changeFinalCode(changeObjCode,changeVal2,changeVal3);
		console.log(newObjCode);
		
		function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
			//找到对象代码中的变量名称 
			var v1=newObjCode.indexOf('var',0);
			var v2=newObjCode.indexOf('=',v1);
			var objName=newObjCode.substring(v1,v2);
			var vv1=localCode.indexOf(objName,0);
			var vv2=localCode.indexOf("});",vv1)+3;
			return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
		}
		localCode=changeObjCod(localCode,newObjCode);
		if(!localCode){return}
		console.log(codeid);
		//组件重新渲染三部曲 
		$('#'+codeid).children().remove();
		localStorage.setItem(codeid,localCode);
		eval(localStorage.getItem(codeid));
		
		$('.lbConfigButtonBox').css('display','none');
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}
	}
}
function grid1_0ButtonAdd(e){
	console.log('1.0');
	var addCodeArr=[];
	var lbTimeNow=new Date().getTime();
	
	for(var i=0;i<e.siblings('input').length;i++){
		addCodeArr.push(e.siblings('input').eq(i).val());
	}
	console.log(addCodeArr);
	if(addCodeArr[0]==''){return}
	//var finalCode="Ext.define('userModel',{extend:'Ext.data.Model',fields:[{name:'fullName',type:'string'},{name:'loginName',type:'string'}]});var addStore"+lbTimeNow+"=Ext.create('Ext.data.Store',{autoLoad:true,autoDestroy:true,model:'userModel',proxy:{type:'ajax',url:'icUserList.do',reader:{type:'json',root:'dataList'}}});var addCombobox"+lbTimeNow+"=Ext.create('Ext.ux.ideal.form.ComboBox',{emptyText:'请选择发起人',fieldLabel:'发起人',queryMode:'local',width:'50%',labelAlign:'right',store:addStore"+lbTimeNow+",displayField:'fullName',labelWidth:65,});";
	var finalCode="Ext.define('userModel"+lbTimeNow+"',{extend:'Ext.data.Model',fields:[{name:'fullName',type:'string'},{name:'loginName',type:'string'}]});var addStore"+lbTimeNow+"=Ext.create('Ext.data.Store',{autoLoad:true,autoDestroy:true,model:'userModel"+lbTimeNow+"',proxy:{type:'ajax',url:'"+addCodeArr[2]+"',reader:{type:'json',root:'dataList'}}});var addCombobox"+lbTimeNow+"=Ext.create('Ext.ux.ideal.form.ComboBox', {emptyText:'"+addCodeArr[0]+"',fieldLabel:'"+addCodeArr[1]+"',store:addStore"+lbTimeNow+",width:'50%',queryMode: 'local',labelAlign:'right',displayField:'fullName',valueField:'fullName',labelWidth:65,listConfig:{minHeight:80}});";//不生成事件函数
	//var finalCode="var addButton"+lbTimeNow+"=Ext.create('Ext.Button',{cls: 'Common_Btn', margin: '0 0 0 5',text:'"+addCodeArr[0]+"',handler:"+addCodeArr[1]+"});";//生成事件函数
	var codeid=e.parent().parent().parent('.configuration').siblings('.view').attr('codeid');
	var localCode=localStorage.getItem(codeid);
	console.log(codeid);
	var addButtonFun='function '+addCodeArr[1]+'(){};';
	localCode=finalCode+localCode;//生成事件函数名addButtonFun+finalCode+localCode;
	var buttonVar='addCombobox'+lbTimeNow+',';
	var addComboCode1=localCode.indexOf('[',localCode.indexOf('item',localCode.indexOf('var conditionPanel')))+1;//获取关键字dockedItems之后的第一个->的位置
	
	localCode=localCode.slice(0,addComboCode1)+buttonVar+localCode.slice(addComboCode1);
	
	//组件重新渲染三部曲 
	$('#'+codeid).children().remove();
	localStorage.setItem(codeid,localCode);
	eval(localStorage.getItem(codeid));
	
	
	$('.lbConfigComboBoxDelBox').css('display','none');
	if(myWindow){
		myWindow.close();//关闭编辑代码窗口
	}
	$(e.target).parent().parent('.lbConfigButtonBox').hide();
	$(e.target).siblings('input').val('');
}
function makeComboboxSelectBox1_0(e){
	//删除项
	//先删除原来的组件 然后创建新的
	if($(e.target).siblings('.lbConfigComboBoxDelBox').find('.submitComboBoxDelConfig').eq(0).siblings('div').children('table').length==1){
		$(e.target).siblings('.lbConfigComboBoxDelBox').find('.submitComboBoxDelConfig').eq(0).siblings('div').empty();
	}
	$(e.target).siblings('.lbConfigComboBoxDelBox').find('.submitComboBoxDelConfig').siblings('div').eq(0).attr('id','configDeleteChange20191123').append("<style>#configDeleteChange20191123 *{border:none;}</style>");
	var statesArr=[];
	var finalArr=[];
	(function(){
		var codeId=$(e.target).parent().siblings('.view').attr('codeid');
		var mainCode=localStorage.getItem(codeId);
		function getMainItems(code,objName){//获取主要panel里的item变量名 返回一个数组  参数1：总代吗  2：主panel变量名称(必须唯一)
			var v1=code.indexOf(objName,0);
			v1=code.indexOf('items',v1+1);
			v1=code.indexOf('[',v1+1)+1;
			var v2=code.indexOf(']',v1);
			console.log(code.substring(v1,v2));
			return code.substring(v1,v2)	
		}
		console.log(getMainItems(mainCode,'var conditionPanel'));
		var itemsArr=getMainItems(mainCode,'var conditionPanel').split(',');
		console.log(itemsArr);
		(function(){//剔除不是输入框的项
			for(var i=0;i<itemsArr.length;){
				console.log(itemsArr);
				console.log(itemsArr[i]);
				var v1=mainCode.indexOf("var "+itemsArr[i],0);
				var v2=mainCode.indexOf("});",v1);
				console.log(mainCode.substring(v1,v2));
				if(mainCode.substring(v1,v2).indexOf("Ext.create('Ext.form.TextField',")==-1&&mainCode.substring(v1,v2).indexOf("Ext.create('Ext.ux.ideal.form.ComboBox',")==-1){
					itemsArr.splice(i,1);
					i=0;
				}else{i++}
				
			}
		})();
		console.log(itemsArr);
		for(var i=0;i<itemsArr.length;i++){
			finalArr[i]={"name":itemsArr[i]}
		}
		console.log(finalArr);
	})();
	var states = Ext.create('Ext.data.Store', {
	    fields: ['name'],	
	    data : finalArr
	});

	
	Ext.create('Ext.form.ComboBox', {
	    store: states,
	    queryMode: 'local',
	    emptyText:'name',
	    displayField: 'name',
	    valueField: 'name',
	    editable:false,
	    renderTo: Ext.get('configDeleteChange20191123')
	});
	
	//修改项
	//先删除原来的组件 然后创建新的
	if($(e.target).siblings('.lbConfigComboBoxDelBox').find('#lb20191011').children('div').children('table').length==1){
		$(e.target).siblings('.lbConfigComboBoxDelBox').find('#lb20191011').children('div').empty();
	}
	$(e.target).siblings('.lbConfigComboBoxDelBox').find('#lb20191011').children('div').attr('id','configDeleteChange20191124').append("<style>#configDeleteChange20191124 *{border:none;}</style>");
	var statesArr=[];
	var finalArr=[];
	(function(){
		var codeId=$(e.target).parent().siblings('.view').attr('codeid');
		var mainCode=localStorage.getItem(codeId);
		function getMainItems(code,objName){//获取主要panel里的item变量名 返回一个数组  参数1：总代吗  2：主panel变量名称(必须唯一)
			var v1=code.indexOf(objName,0);
			v1=code.indexOf('items',v1+1);
			v1=code.indexOf('[',v1+1)+1;
			var v2=code.indexOf(']',v1);
			console.log(code.substring(v1,v2));
			return code.substring(v1,v2)	
		}
		console.log(getMainItems(mainCode,'var conditionPanel'));
		var itemsArr=getMainItems(mainCode,'var conditionPanel').split(',');
		(function(){//剔除不是输入框的项
			for(var i=0;i<itemsArr.length;){
				console.log(itemsArr);
				console.log(itemsArr[i]);
				var v1=mainCode.indexOf("var "+itemsArr[i],0);
				var v2=mainCode.indexOf("});",v1);
				if(mainCode.substring(v1,v2).indexOf("Ext.create('Ext.form.TextField',")==-1&&mainCode.substring(v1,v2).indexOf("Ext.create('Ext.ux.ideal.form.ComboBox',")==-1){
					itemsArr.splice(i,1);
					i=0;
				}else{i++}
				
			}
		})();
		console.log(itemsArr);
		for(var i=0;i<itemsArr.length;i++){
			finalArr[i]={"name":itemsArr[i]}
		}
		console.log(finalArr);
	})();
	var states = Ext.create('Ext.data.Store', {
	    fields: ['name'],	
	    data : finalArr
	});

	
	Ext.create('Ext.form.ComboBox', {
	    store: states,
	    queryMode: 'local',
	    emptyText:'name',
	    displayField: 'name',
	    valueField: 'name',
	    editable:false,
	    renderTo: Ext.get('configDeleteChange20191124')
	});
}
function grid1_0ComboboxDelete(e,delVal,codeid,localCode){
	console.log(delVal);
	var oneFinal='';
	var whichComponent='';
	function strGetObjName(code,str1,position=0){//查询指定字符的对象名称 参数:1在哪个字符串里查 2要查询的关键字 
		var v1=code.indexOf(str1,position);//获取关健字的位置
		console.log(v1);
		console.log(str1);
		if(v1==-1){Ext.Msg.alert('删除失败1','没有此项');return false}
		var v2=code.indexOf('=',v1);
		var v3=code.lastIndexOf('var',v2)+4;
		//检查当前返回的变量名后面是否是Ext.ux.ideal.form.ComboBox对象
		var checkV1=code.indexOf(code.substring(v3,v2));
		var checkV2=code.indexOf('});',checkV1);
		var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
		console.log(checkV3);
		if(checkV3.indexOf("Ext.ux.ideal.form.ComboBox") >0){//如果检查不包含Ext.button 则返回false 不删除 因为不是button组件 此事件只能删除button组件
			oneFinal=code.substring(v3,v2);
			whichComponent='ComboBox';
			return true
		}
		if(checkV3.indexOf("Ext.create('Ext.form.TextField',")>0){
			oneFinal=code.substring(v3,v2);
			whichComponent='TextField';
			return true
		}
		strGetObjName(localCode,delVal,v1+1);
	}
	if(!strGetObjName(localCode,delVal)){//获取当前删除的对象名称
		return 
	}
	oneFinal=oneFinal.trim();
	console.log(oneFinal);
	
	function strDelItemObjName(code,ObjName){//删除item的 对象名称 对象代码 store model  返回新代码 参数 1字符串2要删除的对象名称
		//删除items对象中的变量名
		var objNameLen=ObjName.length;
		var v1=code.indexOf("Ext.create('Ext.ux.ideal.form.Panel',",0);
		var v2=code.indexOf(ObjName,v1);
		if(v2==-1){
			return false
		}
		var v3=v2+objNameLen;
		var v4=code.substring(0,v2)+code.substring(v3);
		var v11=v4.indexOf('var '+ObjName,0);
		var v22=v4.indexOf('});',v11)+3;
		var v33=v4.substring(0,v11)+v4.substring(v22);
		//判断删除的变量是左or右or中间
		console.log(code.substring(v2-2,v3+1));
		var lbVarPartcode=code.substring(v2-2,v3+1);
		if(lbVarPartcode.indexOf("[")!=-1){//第一个
			var v3=v2+objNameLen+1;
			var v4=code.substring(0,v2)+code.substring(v3);
			var v11=v4.indexOf('var '+ObjName,0);
			var v22=v4.indexOf('});',v11)+3;
			var v33=v4.substring(0,v11)+v4.substring(v22);
		}else if(lbVarPartcode.indexOf("]")!=-1){//最后一个
			if(lbVarPartcode.indexOf(" ")!=-1){//如果包含空格 也就是代码已经格式化了
				var v3=v2+objNameLen;
				var v4=code.substring(0,v2-2)+code.substring(v3);
				var v11=v4.indexOf('var '+ObjName,0);
				var v22=v4.indexOf('});',v11)+3;
				var v33=v4.substring(0,v11)+v4.substring(v22);
			}else{
				var v3=v2+objNameLen;
				var v4=code.substring(0,v2-1)+code.substring(v3);
				var v11=v4.indexOf('var '+ObjName,0);
				var v22=v4.indexOf('});',v11)+3;
				var v33=v4.substring(0,v11)+v4.substring(v22);
			}
		}else{//中间的
			var v3=v2+objNameLen;
			var v4=code.substring(0,v2)+code.substring(v3+1);
			var v11=v4.indexOf('var '+ObjName,0);
			var v22=v4.indexOf('});',v11)+3;
			var v33=v4.substring(0,v11)+v4.substring(v22);
		}
		
		if(whichComponent=='ComboBox'){
			//找到对象的store变量名称
			var vStoreName=v4.substring(v11,v22);
			var vStoreNameV1=vStoreName.indexOf('store:',0)+6;
			var vStoreNameV2=vStoreName.indexOf(',',vStoreNameV1);
			vStoreName=vStoreName.substring(vStoreNameV1,vStoreNameV2);
			console.log(vStoreName);
			//找到store对象代码中的model变量名称
			var vModelNameV1=v33.indexOf("var "+vStoreName,0);
			var vModelNameV2=v33.indexOf("});",vModelNameV1)+3;
			var vModelCode=v33.substring(vModelNameV1,vModelNameV2);
			var vModelNameZ1=vModelCode.indexOf('model:',0)+6;
			var vModelNameZ2=vModelCode.indexOf(',',vModelNameZ1);
			var vModelName=vModelCode.substring(vModelNameZ1,vModelNameZ2);
			console.log(vModelName);
			//删除store对象代码
			var v44=v33.substring(0,vModelNameV1)+v33.substring(vModelNameV2);
			//删除model对象代码
			var vvv1=v44.indexOf("Ext.define("+vModelName,0);
			var vvv2=v44.indexOf("});",vvv1)+3;
			v55=v44.substring(0,vvv1)+v44.substring(vvv2);
			return v55
		}
		return v33
		

	}
	localCode=strDelItemObjName(localCode,oneFinal);
	if(!localCode){return}
	//组件重新渲染三部曲 
	$('#'+codeid).children().remove();
	localStorage.setItem(codeid,localCode);
	eval(localStorage.getItem(codeid));
	
	$('.lbConfigComboBoxDelBox').css('display','none');
	if(myWindow){
		myWindow.close();//关闭编辑代码窗口
	}
}

function grid1_0ComboboxChange(e,delVal,codeid,localCode){
	console.log(e,delVal,codeid)
	var changeArr=[];
	var changeNArr=[];
	var oneFinal='';
	for(var i=0;i<$(e.target).siblings('input').length;i++){
		changeArr.push($(e.target).siblings('input').eq(i).val());
		changeNArr.push($(e.target).siblings('input').eq(i).attr('placeholder'));
	}
	
	var fieldLabel=delVal;
	function strGetObjName(code,str1,position=0){//查询指定字符的对象名称 参数:1在哪个字符串里查 2要查询的关键字 
		console.log(str1);
		var v1=code.indexOf(str1,position);//获取关健字的位置
		if(v1==-1){Ext.Msg.alert('修改失败','没有此项');return false}
		var v2=code.indexOf('=',v1);
		var v3=code.lastIndexOf('var',v2)+4;
		
		var checkV1=code.indexOf(code.substring(v3,v2));
		var checkV2=code.indexOf('});',checkV1);
		var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
		console.log(checkV3);
		if(checkV3.indexOf("Ext.ux.ideal.form.ComboBox") >0||checkV3.indexOf("Ext.form.TextField")>0){//如果检查不包含Ext.button 则返回false 不删除 因为不是button组件 此事件只能删除button组件
			oneFinal=code.substring(v3,v2);
			return
		}
		strGetObjName(code,str1,v1+1);
	}
	strGetObjName(localCode,fieldLabel); //获取当前删除的对象名称
	if(!oneFinal){return false}
	console.log(oneFinal);
	
	function strChangeObjAttr(localCode,changeObjName,attrName,attrVal){//修改指定变量名的对象中的属性  返回新代码  参数:1在哪个字符串里查 2要查询的对象变量名关键字3要修改的属性名数组4修改的属性值数组
		if(attrName.length<1||attrVal.length<1||attrName.length!=attrVal.length){console.log('参数错误');return}
		console.log(localCode,changeObjName,attrName,attrVal);
		var v1=localCode.indexOf(changeObjName);//变量名的起始位置
		var v2=localCode.indexOf('});',v1);//变量名所属对象的代码结束位置
		var changeObjCode=localCode.substring(v1,v2);//对象代码
		console.log(attrVal);
		console.log(attrName);
		console.log(changeObjCode);
		for(var i=0;i<attrName.length;i++){
			if(attrVal[i].length<1){continue}
			if(attrName[i]=='store'){//html istore input 必须放在最后一个
				console.log(changeObjCode);
				if(changeObjCode.indexOf('store:',0)==-1){
					return
				}
				var s1=changeObjCode.indexOf(attrName[i]);//属性名的起始位置
				console.log(s1);
				if(s1==-1){
					console.log('找不到属性名');
					return
				}
				var s2=changeObjCode.indexOf(',',s1);//属性名之后的,结束位置
				var s3=changeObjCode.indexOf(':',s1)+1;//属性名后:结束位置
				var storeName=changeObjCode.substring(s2,s3);//找到变量名
				
				var vv1=localCode.indexOf(storeName);//变量名的起始位置
				var vv2=localCode.indexOf('url:',vv1);//变量名所属对象的代码结束位置
				var vv3=localCode.indexOf(',',vv2);
				var vv4=localCode.indexOf('});',vv1);
				console.log(localCode.substring(vv1,vv4));
				var objCode=localCode.substring(vv1,vv4);//对象代码
				
				var storeQ1=objCode.indexOf(storeName,0);//找到变量的起始位置
				var storeQ2=objCode.indexOf('url:',storeQ1)+4;//找到变量后面的url起始位置
				var storeQ3=objCode.indexOf(',',storeQ2);//找到变量storeQ2后面的,起始位置
				
				changeObjCode=objCode.substring(0,storeQ2)+"'"+attrVal[i]+"'"+objCode.substring(storeQ3);
				console.log(changeObjCode);
				var v1=localCode.indexOf(storeName,0);//变量名的起始位置
				var v2=localCode.indexOf('});',v1);//变量名所属对象的代码结束位置
			}else{
				var s1=changeObjCode.indexOf(attrName[i]);//属性名的起始位置
				if(s1==-1){
					console.log('找不到属性名');
					return
				}
				
				var s2=changeObjCode.indexOf(',',s1);//属性名之后的,结束位置
				var s3=changeObjCode.indexOf(':',s1)+1;//属性名后:结束位置
				var newChangeCode1=changeObjCode.substring(0,s3);
				var newChangeCode2=changeObjCode.substring(s2);
				
				changeObjCode=newChangeCode1+"'"+attrVal[i]+"'"+newChangeCode2;
				console.log(changeObjCode);
				
			}
			
		}
		for(var i=0;i<$(e.target).siblings('input').length;i++){
			$(e.target).siblings('input').eq(i).val('');
		}
		console.log('qwe');console.log(changeObjCode);
		var finalCode=localCode.substring(0,v1)+changeObjCode+localCode.substring(v2);
		//组件重新渲染三部曲 
		$('#'+codeid).children().remove();
		localStorage.setItem(codeid,finalCode);
		eval(localStorage.getItem(codeid));
		
		
		$('.lbConfigComboBoxDelBox').css('display','none');
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}
		
	}
	strChangeObjAttr(localCode,oneFinal,changeNArr.slice(0),changeArr.slice(0));
}
