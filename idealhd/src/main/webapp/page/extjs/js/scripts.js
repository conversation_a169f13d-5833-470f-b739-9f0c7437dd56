//LbCode
//一个jquery的插件，用于监听元素宽度高度变化，调用方式：
//$("classname").bind('resize',function(){
        //callback
        //...
        //...
//});
(function ($, window, undefined) {
                var elems = $([]),
                    jq_resize = $.resize = $.extend($.resize, {}),
                    timeout_id,
                    str_setTimeout = 'setTimeout',
                    str_resize = 'resize',
                    str_data = str_resize + '-special-event',
                    str_delay = 'delay',
                    str_throttle = 'throttleWindow';
                jq_resize[str_delay] = 250;
                jq_resize[str_throttle] = true;
                $.event.special[str_resize] = {
                    setup: function () {
                        if (!jq_resize[str_throttle] && this[str_setTimeout]) {
                            return false;
                        }
                        var elem = $(this);
                        elems = elems.add(elem);
                        $.data(this, str_data, {
                            w: elem.width(),
                            h: elem.height()
                        });
                        if (elems.length === 1) {
                            loopy();
                        }
                    },
                    teardown: function () {
                        if (!jq_resize[str_throttle] && this[str_setTimeout]) {
                            return false;
                        }
                        var elem = $(this);
                        elems = elems.not(elem);
                        elem.removeData(str_data);
                        if (!elems.length) {
                            clearTimeout(timeout_id);
                        }
                    },
                    add: function (handleObj) {
                        if (!jq_resize[str_throttle] && this[str_setTimeout]) {
                            return false;
                        }
                        var old_handler;
 
                        function new_handler(e, w, h) {
                            var elem = $(this),
                                data = $.data(this, str_data);
                            data.w = w !== undefined ? w : elem.width();
                            data.h = h !== undefined ? h : elem.height();
                            old_handler.apply(this, arguments);
                        }
 
                        if ($.isFunction(handleObj)) {
                            old_handler = handleObj;
                            return new_handler;
                        } else {
                            old_handler = handleObj.handler;
                            handleObj.handler = new_handler;
                        }
                    }
                };
 
                function loopy() {
                    timeout_id = window[str_setTimeout](function () {
                        elems.each(function () {
                            var elem = $(this),
                                width = elem.width(),
                                height = elem.height(),
                                data = $.data(this, str_data);
                            if (width !== data.w || height !== data.h) {
                                elem.trigger(str_resize, [data.w = width, data.h = height]);
                            }
                        });
                        loopy();
                    }, jq_resize[str_delay]);
                }
})(jQuery, this);

var extGridPanel=[];
var extPanelPanel=[];
var extTreePanel=[];




window.onresize=function(){
	//WINDOW窗口改变大小时，改变页面所有gridpanel大小
	for(var ig=1;ig<extGridPanel.length;ig++){
		if(typeof extGridPanel[ig]!='undefined'){
			extGridPanel[ig].getView().refresh();
		}
	}
	//WINDOW窗口改变大小时，改变页面所有panelpanel大小
	for(var ig=1;ig<extPanelPanel.length;ig++){
		if(typeof extPanelPanel[ig]!='undefined'){
			extPanelPanel[ig].setWidth($('#'+extPanelPanel[ig].container.id).parent().width());
		}
	}
	//WINDOW窗口改变大小时，改变页面所有Treepanel大小
	for(var ig=1;ig<extTreePanel.length;ig++){
		if(typeof extTreePanel[ig]!='undefined'){
			extTreePanel[ig].getView().refresh();
		}
	}
};

function LbGridDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbGridDivCreate执行');
	
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	
	var initialGridCode="Ext.create('Ext.data.Store',{storeId:'simpsonsStore',fields:['name','email','phone'],data:{'items':[{'name':'Lisa','email':'<EMAIL>','phone':'************'},{'name':'Bart','email':'<EMAIL>','phone':'************'},{'name':'Homer','email':'<EMAIL>','phone':'************'},{'name':'Marge','email':'<EMAIL>','phone':'************'}]},proxy:{type:'memory',reader:{type:'json',root:'items'}}});Ext.create('Ext.grid.Panel',{width:'100%',height:'',padding:'',margin:'',border:'',bodyStyle:'',renderTo:Ext.get('lb_Ext_grid_Panel_auto"+LbGridDivLength+"'),title:'Simpsons',store:Ext.data.StoreManager.lookup('simpsonsStore'),columns:[{text:'Nam1e',dataIndex:'name'},{text:'Email',dataIndex:'email',flex:1},{text:'Phone',dataIndex:'phone'}]});";

	
	var LbGridDiv=$("<div id='lb_Ext_grid_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_grid_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	if($('*[id*=lb_Ext_grid_Panel_auto'+LbGridDivLength+']').length==0){
		console.log('无')
		return
	}
	
	eval(initialGridCode);
	

	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_grid_Panel_auto"+LbGridDivLength;
	var gridCode=initialGridCode; 
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
	
}



function LbPanelDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbPanelDivCreate执行');
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	console.log(LbGridDivLength);
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbGridDivLength)
	var LbGridDiv=$("<div id='lb_Ext_Panel_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Panel_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	
	
	var initialPanelCode="Ext.create('Ext.panel.Panel',{title:'Results',width:'100%',height:'170',padding:'',margin:'',border:'',bodyStyle:'',renderTo:Ext.get('lb_Ext_Panel_Panel_auto"+LbGridDivLength+"'),layout:{type:'vbox',align:'stretch',padding:5},});";
	eval(initialPanelCode);
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Panel_Panel_auto"+LbGridDivLength;
	var gridCode=initialPanelCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
}
function LbTreeDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbTreeDivCreate执行');
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	var LbGridDiv=$("<div id='lb_Ext_Tree_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Tree_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	
	var initialTreeCode="var store=Ext.create('Ext.data.TreeStore',{root:{expanded:true,children:[{text:'detention',leaf:true},{text:'homework',expanded:true,children:[{text:'book report',leaf:true},{text:'algebra',leaf:true}]},{text:'buy lottery tickets',leaf:true}]}});extTreePanel[LbGridDivLength]=Ext.create('Ext.tree.Panel',{title:'Simple Tree',width:'100%',height:'150',padding:'',margin:'',boder:'',bodyStyle:'',store:store,rootVisible:false,renderTo:Ext.get('lb_Ext_Tree_Panel_auto"+LbGridDivLength+"')});";
	eval(initialTreeCode);
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Tree_Panel_auto"+LbGridDivLength;
	var gridCode=initialTreeCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}

function LbTabsDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbTabsDivCreate执行');
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	var LbGridDiv=$("<div id='lb_Ext_Tabs_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Tabs_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	var initialTabsCode="Ext.create('Ext.tab.Panel',{width:'100%',height:'170',padding:'',margin:'',border:'',bodyStyle:'',activeTab:0,items:[{title:'Tab 1',bodyPadding:10,html:'t1'},{title:'Tab 2',html:'t2'}],renderTo:Ext.get('lb_Ext_Tabs_Panel_auto"+LbGridDivLength+"')});";
	eval(initialTabsCode);
	
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Tabs_Panel_auto"+LbGridDivLength;
	var gridCode=initialTabsCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}

function LbChartsDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbChartsDivCreate执行');
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	var LbGridDiv=$("<div id='lb_Ext_Charts_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Charts_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	
	var initialTabsCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data1','data2','data3','data4','data5'],data:[{'name':'metric one','data1':10,'data2':12,'data3':14,'data4':8,'data5':13},{'name':'metric two','data1':7,'data2':8,'data3':16,'data4':10,'data5':3},{'name':'metric three','data1':5,'data2':2,'data3':14,'data4':12,'data5':7},{'name':'metric four','data1':2,'data2':14,'data3':6,'data4':1,'data5':23},{'name':'metric five','data1':27,'data2':38,'data3':36,'data4':13,'data5':33}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Charts_Panel_auto'+LbGridDivLength),width:'100%',height:'400',padding:'',margin:'',border:'',bodyStyle:'',animate:true,store:store,shadow:true,theme:'Category1',legend:{position:'top'},axes:[{type:'Numeric',position:'left',fields:['data1','data2','data3','data4','data5'],title:'Sample Values',grid:{odd:{opacity:1,fill:'#ddd',stroke:'#bbb','stroke-width':1}},minimum:0,adjustMinimumByMajorUnit:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics',grid:true,label:{rotate:{degrees:315}}}],series:[{type:'area',highlight:false,axis:'left',xField:'name',yField:['data1','data2','data3','data4','data5'],style:{opacity:0.93}}]});";
	eval(initialTabsCode);
	    
	
	
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Charts_Panel_auto"+LbGridDivLength;
	var gridCode=initialTabsCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}

function LbChartsRadarDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbChartsDivCreate执行');
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	var LbGridDiv=$("<div id='lb_Ext_Radar_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Radar_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	
	var initialTabsCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data1','data2','data3'],data:[{'name':'metric one','data1':14,'data2':12,'data3':13},{'name':'metric two','data1':16,'data2':8,'data3':3},{'name':'metric three','data1':14,'data2':2,'data3':7},{'name':'metric four','data1':6,'data2':14,'data3':23},{'name':'metric five','data1':36,'data2':38,'data3':33}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Radar_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'400',padding:'',margin:'',border:'',bodyStyle:'',animate:true,background:'#fff',theme:'Category2',store:store,axes:[{type:'Radial',position:'radial',label:{display:true}}],series:[{showInLegend:true,type:'radar',xField:'name',yField:'data1',style:{opacity:0.4}},{showInLegend:true,type:'radar',xField:'name',yField:'data2',style:{opacity:0.4}},{showInLegend:true,type:'radar',xField:'name',yField:'data3',style:{opacity:0.4}}]});";
	eval(initialTabsCode);
	    
	
	
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Radar_Panel_auto"+LbGridDivLength;
	var gridCode=initialTabsCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}

function LbBrokenlineDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbBrokenlineDivCreate执行');
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	var LbGridDiv=$("<div id='lb_Ext_Brokenline_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Brokenline_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	var store = Ext.create('Ext.data.JsonStore', {
	    fields: ['name', 'data1', 'data2', 'data3', 'data4', 'data5'],
	    data: [
	        { 'name': 'metric one',   'data1': 10, 'data2': 12, 'data3': 14, 'data4': 8,  'data5': 13 },
	        { 'name': 'metric two',   'data1': 7,  'data2': 8,  'data3': 16, 'data4': 10, 'data5': 3  },
	        { 'name': 'metric three', 'data1': 5,  'data2': 2,  'data3': 14, 'data4': 12, 'data5': 7  },
	        { 'name': 'metric four',  'data1': 2,  'data2': 14, 'data3': 6,  'data4': 1,  'data5': 23 },
	        { 'name': 'metric five',  'data1': 4,  'data2': 4,  'data3': 36, 'data4': 13, 'data5': 33 }
	    ]
	});

	Ext.create('Ext.chart.Chart', {
	    renderTo:Ext.get("lb_Ext_Brokenline_Panel_auto"+LbGridDivLength),
	    width: '100%',
	    height: 300,
	    animate: true,
	    store: store,
	    axes: [
	        {
	            type: 'Numeric',
	            position: 'left',
	            fields: ['data1', 'data2'],
	            label: {
	                renderer: Ext.util.Format.numberRenderer('0,0')
	            },
	            title: 'Sample Values',
	            grid: true,
	            minimum: 0
	        },
	        {
	            type: 'Category',
	            position: 'bottom',
	            fields: ['name'],
	            title: 'Sample Metrics'
	        }
	    ],
	    series: [
	        {
	            type: 'line',
	            highlight: {
	                size: 7,
	                radius: 7
	            },
	            axis: 'left',
	            xField: 'name',
	            yField: 'data1',
	            markerConfig: {
	                type: 'cross',
	                size: 4,
	                radius: 4,
	                'stroke-width': 0
	            }
	        },
	        {
	            type: 'line',
	            highlight: {
	                size: 7,
	                radius: 7
	            },
	            axis: 'left',
	            fill: true,
	            xField: 'name',
	            yField: 'data2',
	            markerConfig: {
	                type: 'circle',
	                size: 4,
	                radius: 4,
	                'stroke-width': 0
	            }
	        }
	    ]
	});

	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Brokenline_Panel_auto"+LbGridDivLength;
	var gridCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data1','data2','data3','data4','data5'],data:[{'name':'metric one','data1':10,'data2':12,'data3':14,'data4':8,'data5':13},{'name':'metric two','data1':7,'data2':8,'data3':16,'data4':10,'data5':3},{'name':'metric three','data1':5,'data2':2,'data3':14,'data4':12,'data5':7},{'name':'metric four','data1':2,'data2':14,'data3':6,'data4':1,'data5':23},{'name':'metric five','data1':4,'data2':4,'data3':36,'data4':13,'data5':33}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('"+lbGridId+"'),width:'100%',height:300,animate:true,store:store,axes:[{type:'Numeric',position:'left',fields:['data1','data2'],label:{renderer:Ext.util.Format.numberRenderer('0,0')},title:'Sample Values',grid:true,minimum:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics'}],series:[{type:'line',highlight:{size:7,radius:7},axis:'left',xField:'name',yField:'data1',markerConfig:{type:'cross',size:4,radius:4,'stroke-width':0}},{type:'line',highlight:{size:7,radius:7},axis:'left',fill:true,xField:'name',yField:'data2',markerConfig:{type:'circle',size:4,radius:4,'stroke-width':0}}]});";
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}

function LbColumnDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbColumnDivCreate执行');
	
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	var LbGridDiv=$("<div id='lb_Ext_Column_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Column_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	var initialTabsCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data'],data:[{'name':'metric one','data':10},{'name':'metric two','data':7},{'name':'metric three','data':5},{'name':'metric four','data':2},{'name':'metric five','data':27}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Column_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'300',padding:'',margin:'',border:'',bodyStyle:'',animate:true,store:store,axes:[{type:'Numeric',position:'left',fields:['data'],label:{renderer:Ext.util.Format.numberRenderer('0,0')},title:'Sample Values',grid:true,minimum:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics'}],series:[{type:'column',axis:'left',highlight:true,tips:{trackMouse:true,width:140,height:28,renderer:function(storeItem,item){this.setTitle(storeItem.get('name')+': '+storeItem.get('data')+' $')}},label:{display:'insideEnd','text-anchor':'middle',field:'data',renderer:Ext.util.Format.numberRenderer('0'),orientation:'vertical',color:'#333'},xField:'name',yField:'data'}]});"
	eval(initialTabsCode);
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Column_Panel_auto"+LbGridDivLength;
	var gridCode=initialTabsCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}

function LbPieDivCreate(LbTarget1,LbGridDivLength){
	console.log('LbPieDivCreate执行');
		
	console.log($("#lbSave").find("*[id*=lb_Ext]").length);//20190610
	LbGridDivLength=LbGridDivLength+$("#lbSave").find("*[id*=lb_Ext]").length;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	var LbGridDiv=$("<div id='lb_Ext_Pie_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Pie_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbTarget1);
	console.log(LbGridScript);
	if(LbTarget1==null){
		return
	}
	
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	
	
	var initialTabsCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data'],data:[{'name':'metric one','data':10},{'name':'metric two','data':7},{'name':'metric three','data':5},{'name':'metric four','data':2},{'name':'metric five','data':27}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Pie_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'350',padding:'',margin:'',border:'',bodyStyle:'',animate:true,store:store,theme:'Base:gradients',series:[{type:'pie',angleField:'data',showInLegend:true,tips:{trackMouse:true,width:140,height:28,renderer:function(storeItem,item){var total=0;store.each(function(rec){total+=rec.get('data')});this.setTitle(storeItem.get('name')+': '+Math.round(storeItem.get('data')/total*100)+'%')}},highlight:{segment:{margin:20}},label:{field:'name',display:'rotate',contrast:true,font:'18px Arial'}}]});";
	eval(initialTabsCode);
	
	
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Pie_Panel_auto"+LbGridDivLength;
	var gridCode=initialTabsCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode);
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	console.log(bean);
}


var lbGridId=null;
var lbOpenWindow=false;//检测同一时间只能打开一个窗口
var myWindow=null;
var jsarr=[];
$(document).click(function(e) {
	console.log($(e.target).attr('class'));
	console.log($(e.target));
	
	if($(e.target).attr('class')=='x-panel customize_panel_back x-grid-with-col-lines x-grid-with-row-lines x-border-item x-box-item x-panel-default x-grid'){
		console.log($(e.target).children('div').eq(0).children('.x-box-inner').children('.x-box-target'));
	}
	
	if($(e.target).id == "lbSave"){//如果点击编辑
		consonle.log("lbSave");
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbedit")!=-1){//如果点击编辑
		if(!lbOpenWindow){//检测同一时间只能打开一个窗口
			lbGridId=$(e.target).parent().next().next().children().attr('id');//获取当前所点击的rendenTo元素id
			console.log(lbGridId);
			console.log($(e.target));
			$(e.target).siblings('div').hide();
			edit();
			
		}else{
			alert('您已经打开编辑窗口');
		}
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbCss")!=-1){//如果样式配置
		if($(e.target).siblings('.lbCssBox').css('display')=='none'){
			$(e.target).siblings('.lbCssBox').css('display','flex');
		}else{
			$(e.target).siblings('.lbCssBox').css('display','none');
		}
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbConfig")!=-1){//如果配置
		if($(e.target).siblings('.lbConfigBox').css('display')=='none'){
			$(e.target).siblings('.lbConfigBox').css('display','flex')	;
		}else{
			$(e.target).siblings('.lbConfigBox').css('display','none');
		}
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbButtonAdd")!=-1){//如果lbComboBoxAdd
		if($(e.target).siblings('.lbConfigBox').css('display')=='none'){
			$(e.target).siblings('.lbConfigBox').css('display','flex')	;
		}else{
			$(e.target).siblings('.lbConfigBox').css('display','none');
		}  
		
		 
		/*$('.lbConfigBox').css('display','none');
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}*/
			
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbComboBoxAdd")!=-1){//如果lbComboBoxAdd
		if($(e.target).siblings('.lbConfigComboBoxAddBox').css('display')=='none'){
			$(e.target).siblings('.lbConfigComboBoxAddBox').css('display','flex')	;
		}else{
			$(e.target).siblings('.lbConfigComboBoxAddBox').css('display','none');
		}
		
			
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitButtonConfigChange")!=-1){//如果submitConfigButtonChange
		hideAllLittleBox();
		var changeVal=$(e.target).siblings('div').find('input').val();
		var changeVal2=$(e.target).siblings('input').eq(0).attr('placeholder');
		var changeVal3=$(e.target).siblings('input').eq(0).val();
		var codeid=$(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.view').attr('codeid');
		var localCode=localStorage.getItem(codeid);//当前代码
		
		(function(){//2019113新添加功能 修改选项中查不到变量名 所以改为搜索test属性值 中文 修改changeVal
			var v1=localCode.indexOf(changeVal,0);
			v1=localCode.indexOf('text',v1);
			v1=localCode.indexOf("'",v1)+1;
			var v2=localCode.indexOf("'",v1);
			var name=localCode.substring(v1,v2);
			changeVal=name;
		})();
		console.log(changeVal);
		
		if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.0'){
			console.log('2.0');
			
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
			console.log('2.2');
			grid2_2ButtonChange(localCode,changeVal,changeVal2,changeVal3,codeid);
			$(e.target).siblings('input').eq(0).val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid1.0'){
			console.log('1.0');
			grid1_0ButtonChange(localCode,changeVal,changeVal2,changeVal3,codeid);
			$(e.target).siblings('input').eq(0).val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.3'){
			console.log('2.3');
			changeVal=$(e.target).siblings('div').find('input').val();
			grid2_3ButtonChange(localCode,changeVal,changeVal2,changeVal3,codeid);
			$(e.target).siblings('input').eq(0).val('');
		}else{
			
			function strGetObjCode(code,str1,position=0){//查询指定字符的对象代码 参数:1在哪个字符串里查 2要查询的关键字 3查找起始位置
				var v1=code.indexOf(str1,position);//获取关健字的位置
				if(v1==-1){Ext.Msg.alert('修改失败','没有此项');return false}
				var v2=code.lastIndexOf('=',v1);
				var v3=code.lastIndexOf('var',v2);
				
				var checkV1=code.indexOf(code.substring(v3,v2));
				var checkV2=code.indexOf('});',checkV1)+3;
				var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
				console.log(checkV3);
				if(checkV3.indexOf("Ext.create('Ext.Button',") >0){
					return checkV3
				}else{
					strGetObjCode(localCode,changeVal,v1+1);
				}
			}
			var changeObjCode=strGetObjCode(localCode,changeVal);
			
			function changeFinalCode(code,changeVal2,changeVal3){//修改对象属性 返回新对象代码  参数:1对象代码 2要修改的属性名3修改值
				var v1=code.indexOf(changeVal2,0);//获取关健字的位置
				var v2=code.indexOf(":",v1)+1;
				var v3=code.indexOf(",",v2);
				return code.substring(0,v2)+"'"+changeVal3+"'"+code.substring(v3)
			}
			var newObjCode=changeFinalCode(changeObjCode,changeVal2,changeVal3);
			
			function changeObjCod(localCode,newObjCode){//将对象代码替换  返回修改后代码 参数 1总代码2要替换的对象代码 
				//找到对象代码中的变量名称 
				var v1=newObjCode.indexOf('var',0);
				var v2=newObjCode.indexOf('=',v1);
				var objName=newObjCode.substring(v1,v2);
				
				var vv1=localCode.indexOf(objName,0);
				var vv2=localCode.indexOf("});",vv1)+3;
				console.log(localCode.substring(0,vv1))
				console.log(changeObjCode)
				console.log(localCode.substring(vv2))
				return localCode.substring(0,vv1)+newObjCode+localCode.substring(vv2)
			}
			localCode=changeObjCod(localCode,newObjCode);
			
			if(!localCode){return}
			//组件重新渲染三部曲 
			$('#'+codeid).children().remove();
			localStorage.setItem(codeid,localCode);
			eval(localStorage.getItem(codeid));
			
			$('.lbConfigComboBoxDelBox').css('display','none');
			if(myWindow){
				myWindow.close();//关闭编辑代码窗口
			}
		}
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitComboBoxDelConfig")!=-1){//如果submitComboBoxDelConfig
		(function(){
			hideAllLittleBox();
			var codeid=$(e.target).parent().parent().parent('.configuration').siblings('.view').attr('codeid');
			var localCode=localStorage.getItem(codeid);//当前代码
			var text=$(e.target).text();
			if(text=='删除'){
				var delVal=$(e.target).siblings('div').find('input').val();
				if(delVal==''){
					return
				}
				//检查是哪个组件模板
				if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.0'){
					grid2($(e.target),delVal,codeid,localCode);
				}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
					console.log('2.2');
					grid2_2ComboboxDelete($(e.target),delVal,codeid,localCode);
				}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid1.0'){
					console.log('1.0');
					grid1_0ComboboxDelete($(e.target),delVal,codeid,localCode);
				}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.3'){
					console.log('2.3');
					grid2_3ComboboxDelete($(e.target),delVal,codeid,localCode);
				}else{
					var changeVal=$(e.target).siblings('input').val();
					console.log(changeVal);
					function strGetObjName(code,str1){//查询指定字符的对象名称 参数:1在哪个字符串里查 2要查询的关键字 
						var v1=code.indexOf(str1,0);//获取关健字的位置
						if(v1==-1){Ext.Msg.alert('删除失败','没有此项');return false}
						var v2=code.lastIndexOf('=',v1);
						var v3=code.lastIndexOf('var',v2)+4;
						//检查当前返回的变量名后面是否是Ext.ux.ideal.form.ComboBox对象
						var checkV1=code.indexOf(code.substring(v3,v2));
						var checkV2=code.indexOf('});',checkV1);
						var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
						console.log(checkV3);
						if(checkV3.indexOf("Ext.ux.ideal.form.ComboBox") >0||checkV3.indexOf("Ext.form.TextField")>0){//如果检查不包含Ext.button 则返回false 不删除 因为不是button组件 此事件只能删除button组件
							return code.substring(v3,v2)
						}
						Ext.Msg.alert('删除失败1','没有此项');return false
					}
					var delObjName=strGetObjName(localCode,delVal); //获取当前删除的对象名称
					delObjName=delObjName.trim();
					console.log(delObjName);
					
					function strDelItemObjName(code,ObjName){//删除item的对象名称和对象代码  返回新代码 参数 1字符串2要删除的对象名称
						var objNameLen=ObjName.length;
						var v1=code.indexOf("Ext.create('Ext.ux.ideal.form.Panel',",0);
						var v2=code.indexOf(ObjName,v1);
						if(v2==-1){
							return false
						}
						var v3=v2+objNameLen+1;
						var v4=code.substring(0,v2)+code.substring(v3);
						
						var v11=v4.indexOf('var '+ObjName,0);
						var v22=v4.indexOf('});',v11)+3;
						var v33=v4.substring(0,v11)+v4.substring(v22);
						return v33

					}
					localCode=strDelItemObjName(localCode,delObjName);
					if(!localCode){return}
					//组件重新渲染三部曲 
					$('#'+codeid).children().remove();
					localStorage.setItem(codeid,localCode);
					eval(localStorage.getItem(codeid));
					
					console.log(delObjName);
					$('.lbConfigComboBoxDelBox').css('display','none');
					if(myWindow){
						myWindow.close();//关闭编辑代码窗口
					}
				}
				
			}
			if(text=='修改'){
				(function(){
					var delVal=$(e.target).siblings('div').find('input').val();
					var xxVal=true;
					for(var i=0;i<$(e.target).siblings('input').length;i++){
						if($(e.target).siblings('input').eq(i).val()!=''){
							xxVal=false;
						}
					}
					if(delVal==''||xxVal){
						console.log('sss');
						return
					}
					
					//检查是哪个组件模板
					if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.0'){
						grid2Revise(e,delVal,codeid,localCode);
					}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
						
						grid2_2ComboboxChange(e,delVal,codeid,localCode);
					}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid1.0'){
						
						grid1_0ComboboxChange(e,delVal,codeid,localCode);
					}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.3'){
						
						grid2_3ComboboxChange(e,delVal,codeid,localCode);
					}else{
						var changeArr=[];
						var changeNArr=[];
						for(var i=0;i<$(e.target).siblings('input').length;i++){
							changeArr.push($(e.target).siblings('input').eq(i).val());
							changeNArr.push($(e.target).siblings('input').eq(i).attr('placeholder'));
						}
						
						var fieldLabel=changeArr[0];
						function strGetObjName(code,str1){//查询指定字符的对象名称 参数:1在哪个字符串里查 2要查询的关键字 
							var v1=code.indexOf(str1,0);//获取关健字的位置
							if(v1==-1){Ext.Msg.alert('修改失败','没有此项');return false}
							var v2=code.lastIndexOf('=',v1);
							var v3=code.lastIndexOf('var',v2)+4;
							//检查当前返回的变量名后面是否是Ext.ux.ideal.form.ComboBox对象
							var checkV1=code.indexOf(code.substring(v3,v2));
							var checkV2=code.indexOf('});',checkV1);
							var checkV3=code.substring(checkV1,checkV2);//检查出来的那段对象代码
							console.log(checkV3);
							if(checkV3.indexOf("Ext.ux.ideal.form.ComboBox") == -1){//如果检查不包含Ext.button 则返回false 不删除 因为不是button组件 此事件只能删除button组件
								Ext.Msg.alert('修改失败1','没有此项');return false
							}
							return code.substring(v3,v2)
						}
						var changeObjName=strGetObjName(localCode,fieldLabel); //获取当前删除的对象名称
						if(!changeObjName){return false}
						console.log(delObjName);
						
						console.log(changeArr.slice(1));console.log(changeNArr.slice(1));
						
						function strChangeObjAttr(localCode,changeObjName,attrName,attrVal){//修改指定变量名的对象中的属性  返回新代码  参数:1在哪个字符串里查 2要查询的对象变量名关键字3要修改的属性名数组4修改的属性值数组
							if(attrName.length<1||attrVal.length<1||attrName.length!=attrVal.length){console.log('参数错误');return}
							var v1=localCode.indexOf(changeObjName);//变量名的起始位置
							var v2=localCode.indexOf('});',v1);//变量名所属对象的代码结束位置
							var changeObjCode=localCode.substring(v1,v2);//对象代码
							console.log(attrVal);
							
							for(var i=0;i<attrName.length;i++){
								if(attrVal[i].length<1){continue}
								if(attrName[i]=='store'){//html istore input 必须放在最后一个
									var s1=changeObjCode.indexOf(attrName[i]);//属性名的起始位置
									if(s1==-1){
										console.log('找不到属性名');
										return
									}
									var s2=changeObjCode.indexOf(',',s1);//属性名之后的,结束位置
									var s3=changeObjCode.indexOf(':',s1)+1;//属性名后:结束位置
									var storeName=changeObjCode.substring(s2,s3);//找到变量名
									
									var vv1=localCode.indexOf(storeName);//变量名的起始位置
									var vv2=localCode.indexOf('url:',vv1);//变量名所属对象的代码结束位置
									var vv3=localCode.indexOf(',',vv2);
									var vv4=localCode.indexOf('});',vv1);
									console.log(localCode.substring(vv1,vv4));
									var objCode=localCode.substring(vv1,vv4);//对象代码
									
									var storeQ1=objCode.indexOf(storeName,0);//找到变量的起始位置
									var storeQ2=objCode.indexOf('url:',storeQ1)+4;//找到变量后面的url起始位置
									var storeQ3=objCode.indexOf(',',storeQ2);//找到变量storeQ2后面的,起始位置
									//console.log("'"+attrVal[i]+"'");
									changeObjCode=objCode.substring(0,storeQ2)+"'"+attrVal[i]+"'"+objCode.substring(storeQ3);
									console.log(changeObjCode);
									var v1=localCode.indexOf(storeName,0);//变量名的起始位置
									var v2=localCode.indexOf('});',v1);//变量名所属对象的代码结束位置
								}else{
									var s1=changeObjCode.indexOf(attrName[i]);//属性名的起始位置
									if(s1==-1){
										console.log('找不到属性名');
										return
									}
									var s2=changeObjCode.indexOf(',',s1);//属性名之后的,结束位置
									var s3=changeObjCode.indexOf(':',s1)+1;//属性名后:结束位置
									var newChangeCode1=changeObjCode.substring(0,s3);
									var newChangeCode2=changeObjCode.substring(s2);
									
									changeObjCode=newChangeCode1+"'"+attrVal[i]+"'"+newChangeCode2;
									console.log(changeObjCode);
								}
								
							}
							for(var i=0;i<$(e.target).siblings('input').length;i++){
								$(e.target).siblings('input').eq(i).val('');
							}
							return localCode.substring(0,v1)+changeObjCode+localCode.substring(v2);
							
						}
						var finalCode=strChangeObjAttr(localCode,changeObjName,changeNArr.slice(1),changeArr.slice(1));
						
						//组件重新渲染三部曲 
						$('#'+codeid).children().remove();
						localStorage.setItem(codeid,finalCode);
						eval(localStorage.getItem(codeid));
						
						
						$('.lbConfigComboBoxDelBox').css('display','none');
						if(myWindow){
							myWindow.close();//关闭编辑代码窗口
						}
					}
					
					
				})();
			}
			if(changeVal==''){return}
			
		})();
		
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitLbGridUrlBox")!=-1){//如果提交
		(function(){
			hideAllLittleBox();
			//检查是哪个组件模板
			if($(e.target).parent().parent('.lbColumnBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
				var value=$(e.target).siblings('input').val();
				var codeid=$(e.target).parent().parent().parent('.configuration').siblings('.view').attr('codeid');
				var localCode=localStorage.getItem(codeid);//当前代码
				grid2_2GridUrlChange(e,codeid,localCode,value);
			}
		})();
		
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitLbColumnBox")!=-1){//如果提交
		(function(){
			hideAllLittleBox();
			var value=$(e.target).siblings('input').val();
			var codeid=$(e.target).parent().parent('.configuration').siblings('.view').attr('codeid');
			var localCode=localStorage.getItem(codeid);//当前代码
			//检查是哪个组件模板
			if($(e.target).parent('.lbColumnBox').parent('.configuration').siblings('.preview').text()=='grid2.0'){
				grid2ChangeColumn(e,delVal,codeid,localCode,value);
			}else if($(e.target).parent().parent('.lbColumnBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
				var value=$(e.target).siblings('input').val();
				var codeid=$(e.target).parent().parent().parent('.configuration').siblings('.view').attr('codeid');
				var localCode=localStorage.getItem(codeid);//当前代码
				grid2_2columnChange(e,delVal,codeid,localCode,value);
			}else{
				if(value.length>1){
					//$.ajax({url:value,success:function(result){
				        var result="[{text:'qwe 去',sortable:true,dataIndex:'taskName',width:130,flex:1},{text:'气味儿',sortable:true,dataIndex:'ibusname',width:80},{text:'按市场销售',sortable:true,dataIndex:'performUser',width:100},{text:'按市场销售',sortable:true,dataIndex:'performUser',width:100},{text:'按市场销售',sortable:true,dataIndex:'performUser',width:100},{text:'让他感到',sortable:true,dataIndex:'startUser',width:100},{text:'大幅度发',sortable:true,dataIndex:'performUser',width:100},{text:'按市场销售',sortable:true,dataIndex:'performUser',width:100},{text:'大幅度发',sortable:true,dataIndex:'performUser',width:100},{text:'发光飞碟',sortable:true,dataIndex:'performUser',width:100},{text:'水电费东风',sortable:true,dataIndex:'performUser',width:100},{text:'按市场销售',sortable:true,dataIndex:'performUser',width:100}]";
				        var v1=localCode.indexOf('var columns',0)+12;
				        var v2=localCode.indexOf('}];',v1)+2;
				        console.log(localCode.substring(0,v1));console.log(localCode.substring(v2));
				        localCode=localCode.substring(0,v1)+result+localCode.substring(v2)
				        //组件重新渲染三部曲 
						$('#'+codeid).children().remove();
						localStorage.setItem(codeid,localCode);
						eval(localStorage.getItem(codeid));
						
						console.log(delObjName);
						$('.lbColumnBox').css('display','none');
						if(myWindow){
							myWindow.close();//关闭编辑代码窗口
						}
				    //}});
				}else{
					
				}
			}
			
		})();
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitComboBoxConfig")!=-1){//如果提交
		hideAllLittleBox();
		if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
			grid2_2addCombobox($(e.target),localCode);
		}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid1.0'){
			grid1_0ButtonAdd($(e.target));
			$(e.target).siblings('input').val('');
		}else if($(e.target).parent().parent('.lbConfigComboBoxDelBox').parent('.configuration').siblings('.preview').text()=='grid2.3'){
			grid2_3ComboboxAdd($(e.target));
			$(e.target).siblings('input').val('');
		}else{
			
		}
		//清空输入框
		$(e.target).siblings('input').val('');
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitConfig")!=-1){//如果提交
		hideAllLittleBox();
		
		//检查是哪个组件模板
		if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.0'){
			grid2AddButton(e,delVal,codeid,localCode);
			$(e.target).parent().parent('.lbConfigButtonBox').hide();
			$(e.target).siblings('input').val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
			grid2_2ButtonAdd($(e.target));
			$(e.target).siblings('input').val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.3'){
			grid2_3ButtonAdd($(e.target));
			$(e.target).siblings('input').val('');
		}else{
			var addCodeArr=[];
			var lbTimeNow=new Date().getTime();
			for(var i=0;i<$(e.target).siblings('input').length;i++){
				addCodeArr.push($(e.target).siblings('input').eq(i).val());
			}
			console.log(addCodeArr);
			if(addCodeArr[0]==''){return}
			
			var finalCode="var addButton"+lbTimeNow+"=Ext.create('Ext.Button',{cls: 'Common_Btn', margin: '0 0 0 5',text:'"+addCodeArr[0]+"'});";//不生成事件函数
			//var finalCode="var addButton"+lbTimeNow+"=Ext.create('Ext.Button',{cls: 'Common_Btn', margin: '0 0 0 5',text:'"+addCodeArr[0]+"',handler:"+addCodeArr[1]+"});";//生成事件函数
			var codeid=$(e.target).parent().parent().parent('.configuration').siblings('.view').attr('codeid');
			var localCode=localStorage.getItem(codeid);
			var addButtonFun='function '+addCodeArr[1]+'(){};';
			localCode=finalCode+localCode;//生成事件函数名addButtonFun+finalCode+localCode;
			var buttonVar=',addButton'+lbTimeNow;
			console.log(buttonVar);
			var addComboCode1=localCode.indexOf(']',localCode.indexOf('var conditionPanel'));//获取关键字dockedItems之后的第一个->的位置
			
			localCode=localCode.slice(0,addComboCode1)+buttonVar+localCode.slice(addComboCode1);
			
			//组件重新渲染三部曲 
			$('#'+codeid).children().remove();
			localStorage.setItem(codeid,localCode);
			eval(localStorage.getItem(codeid));
			
			
			$('.lbConfigDelBox').css('display','none');
			if(myWindow){
				myWindow.close();//关闭编辑代码窗口
			}
			$(e.target).parent().parent('.lbConfigButtonBox').hide();
			$(e.target).siblings('input').val('');
		}
		
		
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitDelConfig")!=-1){//如果提交submitDelConfig
		hideAllLittleBox();
		var delVal=$(e.target).siblings('div').find('input').val();
		var codeid=$(e.target).parent().parent().parent('.configuration').siblings('.view').attr('codeid');
		console.log(delVal);
		var localCode=localStorage.getItem(codeid);//当前代码
		//检查是哪个组件模板
		if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.0'){
			console.log('2.0');
			grid2DelButton(e,delVal,codeid,localCode);
			$(e.target).siblings('input').val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.2'){
			grid2_2ButtonDelete(e,delVal,codeid,localCode);
			$(e.target).siblings('input').val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid1.0'){
			console.log('1.0');
			grid1_0ButtonDelete(e,delVal,codeid,localCode);
			$(e.target).siblings('input').val('');
		}else if($(e.target).parent().parent('.lbConfigButtonBox').parent('.configuration').siblings('.preview').text()=='grid2.3'){
			console.log('2.3');
			grid2_3ButtonDelete(e,delVal,codeid,localCode);
			$(e.target).siblings('input').val('');
		}else{
			function strGetObjName(code,str1,position=0){//查询指定字符的对象名称 参数:1在哪个字符串里查 2要查询的关键字 
				var v1=code.indexOf(str1,position);//获取关健字的位置
				if(v1==-1){
					Ext.MessageBox.alert("未找到","未找到此按钮.");
					return
				}
				var v2=code.lastIndexOf('var',v1);
				var v3=code.indexOf('});',v1);//获取关健字的位置
				var v4=code.substring(v2,v3);//检查出来的那段对象代码
				console.log(v4);
				if(v4.indexOf("Ext.create('Ext.Button',") == -1){
					strGetObjName(localCode,delVal,v3)
				}else{
					var s1=v4.indexOf('var',0)+4;
					var s2=v4.indexOf('=',s1);
					var s3=v4.substring(s1,s2);
					console.log(s3);
					return s3
				}
			}
			var delObjName=strGetObjName(localCode,delVal); //获取当前删除的对象名称
			console.log(delObjName);
			delObjName=delObjName.trim();
			console.log(delObjName);	
			function strDelItemObjName(code,ObjName){//删除item的对象名称和对象代码  返回新代码 参数 1字符串2要删除的对象名称
				var objNameLen=ObjName.length;
				var v1=code.indexOf('var conditionPanel',0);
				var v2=code.indexOf(ObjName,v1);
				v2=code.lastIndexOf(',',v2);//获取前面逗号位置
				if(v2==-1){
					return false
				}
				var v3=v2+objNameLen+1;//code.indexOf(',',v2+1);//获取后面逗号位置
				var v4=code.substring(0,v2)+code.substring(v3);
				var v11=v4.indexOf(ObjName,0)-4;
				var v22=v4.indexOf('});',v11)+3;
				var v33=v4.substring(0,v11)+v4.substring(v22);
				
				return v33
			}
			localCode=strDelItemObjName(localCode,delObjName);
			if(!localCode){return}
			//组件重新渲染三部曲 
			$('#'+codeid).children().remove();
			localStorage.setItem(codeid,localCode);
			eval(localStorage.getItem(codeid));
			
			console.log(delObjName);
			$('.lbConfigDelBox').css('display','none');
			$(e.target).siblings('input').val('');
			if(myWindow){
				myWindow.close();//关闭编辑代码窗口
			}
		}
		
		
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbButtonDel")!=-1){//如果lbComboBoxAdd
		if($(e.target).siblings('.lbConfigDelBox').css('display')=='none'){
			$(e.target).siblings('.lbConfigDelBox').css('display','flex')	;
		}else{
			$(e.target).siblings('.lbConfigDelBox').css('display','none');
		}
		
		
		/*$('.lbConfigBox').css('display','none');
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}*/
			
	}
	(function(){
		if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbComboBoxDel")!=-1){//如果lbComboBoxDel
			if($(e.target).siblings('.lbConfigComboBoxDelBox').css('display')=='none'){
				$(e.target).siblings('.lbConfigComboBoxDelBox').css('display','flex');
				console.log($(e.target).siblings('.lbConfigComboBoxDelBox').find('.submitDelConfig').siblings('div').children('table').length);
				if($(e.target).parent('.configuration').siblings('.preview').text()=='grid2.2'){
					makeComboboxSelectBox2_2(e);
				}else if($(e.target).parent('.configuration').siblings('.preview').text()=='grid1.0'){
					makeComboboxSelectBox1_0(e);
				}else if($(e.target).parent('.configuration').siblings('.preview').text()=='grid2.3'){
					makeComboboxSelectBox2_3(e);
				}
			}else{
				$(e.target).siblings('.lbConfigComboBoxDelBox').css('display','none');
			}
				
		}
	})();
	(function(){
		if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbItemsChange")!=-1){//如果lbItemsChange
			/*if($(e.target).siblings('.lbConfigButtonBox').css('display')=='none'){
				$(e.target).siblings('.lbConfigButtonBox').css('display','flex');
				if($(e.target).parent('.configuration').siblings('.preview').text()=='grid2.2'){
					makeButtonSelectBox2_2(e);
				}
				if($(e.target).parent('.configuration').siblings('.preview').text()=='grid2.3'){
					makeButtonSelectBox2_3(e);
				}
				if($(e.target).parent('.configuration').siblings('.preview').text()=='grid1.0'){
					makeButtonSelectBox1_0(e);
				}
			}else{
				$(e.target).siblings('.lbConfigButtonBox').css('display','none');
			}*/
			if(myWindow){myWindow.close();}
			lbItemsConfig(e);
			lbItemsSortable(e);
		}
	})();
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbColumnObj")!=-1){//如果lbColumn
		
		return
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbColumn")!=-1){//如果lbColumn
		
		if(myWindow){myWindow.close();}
		gridColumnConfig(e);
		lbColumnSortable(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbprev")!=-1){//如果lbprev
		
		if(myWindow){myWindow.close();}
		lbprev(e);
	}
if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbSM")!=-1){
		
		if(myWindow){myWindow.close();}
		lbSM(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbGridData")!=-1){//如果lbColumn
		
		lbGridData(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbAddObj")!=-1){//如果lbAddObj
		
		lbColumnAddObj(e);
	}
if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbItemsAddObj")!=-1){//如果lbItemsAddObj
		
		lbItemsAddObj(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lb_ColumnSubmit")!=-1){//如果lb_ColumnSubmit
		
		gridColumnConfigSubmit(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbDel")!=-1){//如果lbDel
		
		lbColumnDeleteObj(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lb_ItemsSubmit")!=-1){//如果lb_ColumnSubmit
		
		gridItemsConfigSubmit(e);
	}
if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lb_StoreSubmit")!=-1){//如果lb_StoreSubmit
		
	lb_StoreSubmit(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbItemsDel")!=-1){//lbItemsDel
		
		lbItemsDel(e);
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lbPos")!=-1){//如果lbPos
		if($(e.target).siblings('.lbPosBox').css('display')=='none'){
			$(e.target).siblings('.lbPosBox').css('display','flex')	;
			makePartSite($(e.target));
		}else{
			$(e.target).siblings('.lbPosBox').css('display','none');
		}
			
	}
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("submitCss")!=-1){//如果submitCss
		$('.lburlDiv').css('display','none');
		$('.lbCssBox').css('display','none');
		lb20190823CssConfig($(e.target));
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}
		
	}
	function lb20190823CssConfig(target){//修改css样式函数
		var lb20190823ID=target.parent().parent('.configuration').siblings('.view').children('div').attr('id');//要修改的缓存key
		var lbCssConfig=target.parent().parent('.configuration').siblings('.preview').attr('lbCssConfig');
		console.log(lbCssConfig);
		var lb20190823str=localStorage.getItem(lb20190823ID);//要修改的缓存代码
		//var lb20190823strNew='';//修改后的代码 插入缓存的
		for(var i=0;i<target.siblings('input').length;i++){
			if(target.siblings('input')[i].value.length>0){
				var name=target.siblings('input')[i].name;
				
				if(lb20190823str.indexOf(name)!=-1){
					console.log(lb20190823str.indexOf(lbCssConfig));
					var gridPanelWidth=lb20190823str.indexOf(name,lb20190823str.indexOf(lbCssConfig));
					var lb20190823str1=lb20190823str.indexOf("'",gridPanelWidth)+1;//开头
					console.log(lb20190823str1);
					
					var lb20190823str2=lb20190823str.indexOf("'",lb20190823str1);//结尾
					console.log(lb20190823str2);
					
					function replacepos(text,start,stop,replacetext){
					    mystr = text.substring(0,start)+replacetext+text.substring(stop);
					    console.log(text.substring(0,start));
					    console.log(replacetext);
					    console.log(text.substring(stop));
					    return mystr;
					}
					lb20190823str=replacepos(lb20190823str,lb20190823str1,lb20190823str2,target.siblings('input')[i].value);//替换
				}else{
					Ext.MessageBox.alert('提示','需要预设属性');
				}
			}
		}
		$('#'+lb20190823ID).children().remove();
		localStorage.setItem(lb20190823ID,lb20190823str);
		eval(localStorage.getItem(lb20190823ID));
	}
	
	if($(e.target).attr('class')!=undefined&&$(e.target).attr('class').indexOf("lburl")!=-1){//如果url配置
		if($(e.target).attr('class')=='lburl btn btn-mini'){
			if($(e.target).children('.lburlDiv').css('display')=='none'){
				$(e.target).children('.lburlDiv').css('display','flex');
			}else{
				$(e.target).children('.lburlDiv').css('display','none');
			}
		}
		
		var lbGridNowDivSpan=$(e.target).find('.submitUrl');
		$(lbGridNowDivSpan).on('click', function(e){console.log(e);
			if (e.target == e.currentTarget) {
				var value1=$(lbGridNowDivSpan).siblings('.lburlDiv1').val(); //url请求地址
			    var lbServerDataObjKeyCount=0;//返回的column列数
			    var lbServerDataObjKey=[];//返回的column列字段
			    var lbStrArr=[];
			    lbStrArr=value1;//.split("?");
			    var text=$(e.target).parent().parent().parent('.configuration').siblings('.preview').text();//点击哪个组件
			    if(value1.length<4){
			    	Ext.MessageBox.alert("错误","数据错误，请重新输入");
			    	return
			    }
			    if(text=='套件'){return}
			    $.post(value1,function(result){
			    	if(result.root!=undefined||result.item!=undefined||result.data!=undefined||1==1){
			    		var lbfangzhimubiao=$(lbGridNowDivSpan).parent().parent().parent().parent().parent('*[class*=span]');
			    		console.log(lbfangzhimubiao);
			    		//删除当前组件的缓存代码
			        	removeElm($(lbGridNowDivSpan).parent().parent().parent('.configuration').siblings('.remove'));
						//写一套新的url远程数据的组件
			        	
			        	if(text=='Tree面板'){
			        		var lbArr="root:{expanded:true,children:[{text:'detention',leaf:true},{text:'homework',expanded:true,children:[{text:'book report',leaf:true},{text:'alegrbra',leaf:true}]},{text:'buy lottery tickets',leaf:true}]}";//返回数据样例
			        		LbUrlTreeDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        	}
			        	if(text=='tabs面板'){
			        		var lbArr="items:[{title:'Tab 1',bodyPadding:10,html:'T1'},{title:'Tab 2',html:'T2'},{title:'Tab 3',html:'T3'}]";//返回数据样例
			        		LbUrlTabsDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        	}
			        	if(text=='charts面板'){
			        		var lbArr={data:[{'name':'metric one','data1':10,'data2':12,'data3':14,},{'name':'metric two','data1':7,'data2':8,'data3':16,},{'name':'metric three','data1':5,'data2':2,'data3':14,},{'name':'metric four','data1':2,'data2':14,'data3':6,},{'name':'metric five','data1':27,'data2':38,'data3':36,}]};//返回数据样例
			        		LbUrlChartsDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        	}
			        	if(text=='EXT折线图'){
			        		var lbArr={data:[{'name':'metric one','data1':10,'data2':12,'data3':14,'data4':8,'data5':13},{'name':'metric two','data1':7,'data2':8,'data3':16,'data4':10,'data5':3},{'name':'metric three','data1':5,'data2':2,'data3':14,'data4':12,'data5':7},{'name':'metric four','data1':2,'data2':14,'data3':6,'data4':1,'data5':23},{'name':'metric five','data1':4,'data2':4,'data3':36,'data4':13,'data5':33}]};//返回数据样例
			        		LbUrlChartsLineDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        	}
			        	if(text=='EXT柱状图'){
			        		var lbArr={data:[{'name':'metric one','data':10},{'name':'metric two','data':7},{'name':'metric three','data':5},{'name':'metric four','data':2},{'name':'metric five','data':27},{'name':'metric five','data':17},{'name':'metric five','data':7},{'name':'metric five','data':19}]};//返回数据样例
			        		LbUrlChartsColumnDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        	}
			        	if(text=='EXT饼图'){
			        		var lbArr={data:[{'name':'metric one','data':10},{'name':'metric two','data':7},{'name':'metric three','data':22},{'name':'metric four','data':2},{'name':'metric five','data':27},{'name':'metric five','data':17},{'name':'metric five','data':37}]};//返回数据样例
			        		LbUrlChartsPieDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        	}
			        	if(text=='EXT雷达图'){
			        		var lbArr={data:[{'name':'metric one','data1':64,'data2':72,'data3':53,'data4':62,'data5':63},{'name':'metric two','data1':56,'data2':8,'data3':3,'data4':52,'data5':23},{'name':'metric three','data1':54,'data2':2,'data3':7,'data4':22,'data5':33},{'name':'metric four','data1':66,'data2':14,'data3':23,'data4':32,'data5':53},{'name':'metric five','data1':76,'data2':38,'data3':33,'data4':12,'data5':13}]};//返回数据样例
			        		LbUrlChartsRadarDivCreate(lbArr,lbfangzhimubiao,lbStrArr)
			        		
			        	}
			        	if(myWindow){
			    			myWindow.close();//关闭编辑代码窗口
			    		}
			    	}else{
			        	Ext.MessageBox.alert("错误","数据错误1，请重新输入");
			        	$(lbGridNowDivSpan).siblings('.lburlDiv2').val('');
			        }
			    });
			}
			return 
		});
		
		
		
	}
	
	/*if($(e.target).attr('id')!=undefined&&$(e.target).attr('id').indexOf("topreview")!=-1){//如果点击预览
		var html = downloadLayoutSrc();
		console.log(html);
		localStorage.setItem('previewHtml', html);
		jsarr=[];
		console.log(localStorage);
		for(var i=0;i<200;i++){
			if(localStorage['lb_Ext_grid_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_grid_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Tree_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Tree_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Panel_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Panel_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Tabs_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Tabs_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Charts_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Charts_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Brokenline_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Brokenline_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Column_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Column_Panel_auto'+i]) ;
			}
			if(localStorage['lb_Ext_Pie_Panel_auto'+i]){
				jsarr.push(localStorage['lb_Ext_Pie_Panel_auto'+i]) ;
			}
			
			
		}
		localStorage.setItem('jsarr',JSON.stringify(jsarr));
		window.open("page/extjs/preview.html?&data="+Date.parse(new Date()));
	}*/
	
	
	
	
})

//
function edit(){
	
	if(lbGridId.indexOf("lb_Ext_grid_Panel_auto") >= 0){//如果编辑的是grid
		console.log('ssss');
		var gridCode="var keyQuery=Ext.create('Ext.form.TextField',{margin:'5',name:'key',emptyText:'--请输入key--',labelWidth:80,width:'15%',xtype:'textfield'});var extGridPanelStore=Ext.create('Ext.data.Store',{storeId:'simpsonsStore',fields:[{name:'key',type:'String'},{name:'value',type:'String'}],pageSize:10,proxy:{type:'ajax',url:'configList.do',reader:{type:'json',root:'dataList',totalProperty:'total'}}});extGridPanelStore.loadPage(1);function save(){var m=extGridPanelStore.getModifiedRecords();if(m.length==0){Ext.MessageBox.alert('提示','没有需要保存的条目！');return}else{var jsonData='[';for(var i=0;i<m.length;i++){var date=Ext.JSON.encode(m[i].data);if(i==0)jsonData=jsonData+date;else jsonData=jsonData+','+date}jsonData=jsonData+']';Ext.Ajax.request({url:'configSave.do',method:'post',params:{jsonData:jsonData},success:function(response,request){var success=Ext.decode(response.responseText).success;if(true==success){extGridPanelStore.reload({params:{start:0,limit:10,page:1}});Ext.Msg.alert('提示','保存成功！')}else{Ext.Msg.alert('提示','保存失败！')}},failure:function(result,request){secureFilterRs(result,'操作失败！')}})}}function QueryMessage(){extGridPanelStore.reload({params:{start:0,limit:10,page:1,keyPar:keyQuery.getValue().trim()}})}Ext.create('Ext.grid.Panel',{store:Ext.data.StoreManager.lookup('simpsonsStore'),columns:[{text:'序号',width:35,xtype:'rownumberer'},{text:'key',dataIndex:'key',flex:1},{text:'value',dataIndex:'value',flex:1,editor:{xtype:'textfield'}}],plugins:[Ext.create('Ext.grid.plugin.CellEditing',{clicksToEdit:1})],dockedItems:[{xtype:'pagingtoolbar',store:extGridPanelStore,dock:'bottom',displayInfo:true},{xtype:'toolbar',dock:'top',items:[keyQuery,{xtype:'button',cls:'Common_Btn',text:'查询',handler:function(){QueryMessage()}},'->',{xtype:'button',cls:'Common_Btn',text:'保存',handler:save}]}],width:'100%',renderTo:'"+lbGridId+"'});";
	}
	if(lbGridId.indexOf("lb_Ext_Panel_Panel_auto") >= 0){//如果编辑的是Panel
		var gridCode="Ext.create('Ext.panel.Panel',{title:'目标',html:'<p>基础面板</p>',width:'100%',renderTo:'"+lbGridId+"'\n});";
	}
	
	if(lbGridId.indexOf("lb_Ext_Tree_Panel_auto") >= 0){//如果编辑的是Tree
		var gridCode="var store=Ext.create('Ext.data.TreeStore',{root:{expanded:true,children:[{text:'detention',leaf:true},{text:'homework',expanded:true,children:[{text:'book report',leaf:true},{text:'algebra',leaf:true}]},{text:'buy lottery tickets',leaf:true}]}});Ext.create('Ext.tree.Panel',{title:'Simple Tree',height:150,store:store,rootVisible:false,width:'100%',renderTo:Ext.get('"+lbGridId+"')});";
	}
	if(lbGridId.indexOf("lb_Ext_Tbs_Panel_auto") >= 0){//如果编辑的是Tree
		var gridCode="Ext.create('Ext.tab.Panel',{width:300,height:200,activeTab:0,items:[{title:'Tab 1',bodyPadding:10,html:'看一个公会的实力首先看mt，甚至mt决定着公会的生死，如果公会的mt退坑了，也许那个公会就解散了。而且你是mt好装备都给你，60年代的橙色武器可不是烂大街，风剑首先给mt，橙锤想你要也会给你。mt推荐职业牛头人，60年代牛头人天赋多5%的血量，那5%可不是和现在的一样，那是总血量的5%。其次暗夜男1%闪避。。'},{title:'Tab 2',html:'要开60怀旧服了，我给大家介绍下60各职业情况。NO.1战士，战士应该是60年代唯一一个三系天赋登顶，pvepvp也登顶的职业。首先战士是团队本唯一的mt，60年代mt的重要性就不用说了'}],renderTo:Ext.get('"+lbGridId+"')});";
	}
	if(lbGridId.indexOf("lb_Ext_Charts_Panel_auto") >= 0){//如果编辑的是Tree
		var gridCode="var store=Ext.create('Ext.data.Jso	nStore',{fields:['name','data1','data2','data3','data4','data5'],data:[{'name':'metric one','data1':10,'data2':12,'data3':14,'data4':8,'data5':13},{'name':'metric two','data1':7,'data2':8,'data3':16,'data4':10,'data5':3},{'name':'metric three','data1':5,'data2':2,'data3':14,'data4':12,'data5':7},{'name':'metric four','data1':2,'data2':14,'data3':6,'data4':1,'data5':23},{'name':'metric five','data1':27,'data2':38,'data3':36,'data4':13,'data5':33}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('"+lbGridId+"'),width:'100%',height:300,animate:true,store:store,shadow:true,theme:'Category1',legend:{position:'top'},axes:[{type:'Numeric',position:'left',fields:['data1','data2','data3','data4','data5'],title:'Sample Values',grid:{odd:{opacity:1,fill:'#ddd',stroke:'#bbb','stroke-width':1}},minimum:0,adjustMinimumByMajorUnit:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics',grid:true,label:{rotate:{degrees:315}}}],series:[{type:'area',highlight:false,axis:'left',xField:'name',yField:['data1','data2','data3','data4','data5'],style:{opacity:0.93}}]});";
	}
	if(lbGridId.indexOf("lb_Ext_Brokenline_Panel_auto") >= 0){//如果编辑的是Tree
		var gridCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data1','data2','data3','data4','data5'],data:[{'name':'metric one','data1':10,'data2':12,'data3':14,'data4':8,'data5':13},{'name':'metric two','data1':7,'data2':8,'data3':16,'data4':10,'data5':3},{'name':'metric three','data1':5,'data2':2,'data3':14,'data4':12,'data5':7},{'name':'metric four','data1':2,'data2':14,'data3':6,'data4':1,'data5':23},{'name':'metric five','data1':4,'data2':4,'data3':36,'data4':13,'data5':33}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('"+lbGridId+"'),width:'100%',height:300,animate:true,store:store,axes:[{type:'Numeric',position:'left',fields:['data1','data2'],label:{renderer:Ext.util.Format.numberRenderer('0,0')},title:'Sample Values',grid:true,minimum:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics'}],series:[{type:'line',highlight:{size:7,radius:7},axis:'left',xField:'name',yField:'data1',markerConfig:{type:'cross',size:4,radius:4,'stroke-width':0}},{type:'line',highlight:{size:7,radius:7},axis:'left',fill:true,xField:'name',yField:'data2',markerConfig:{type:'circle',size:4,radius:4,'stroke-width':0}}]});";
	}
	if(lbGridId.indexOf("lb_Ext_Column_Panel_auto") >= 0){//如果编辑的是Tree
		var gridCode="var store=Ext.create('Ext.data.JsonStore',{/*数据源配置项*/fields:['name','data'],data:[{'name':'metric one','data':10},{'name':'metric two','data':7},{'name':'metric three','data':5},{'name':'metric four','data':2},{'name':'metric five','data':27}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('"+lbGridId+"'),width:'100%',height:300,animate:true,store:store,axes:[{type:'Numeric',position:'left',fields:['data'],label:{renderer:Ext.util.Format.numberRenderer('0,0')},title:'Sample Values',grid:true,minimum:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics'}],series:[{type:'column',axis:'left',highlight:true,tips:{trackMouse:true,width:140,height:28,renderer:function(storeItem,item){this.setTitle(storeItem.get('name')+': '+storeItem.get('data')+' $')}},label:{display:'insideEnd','text-anchor':'middle',field:'data',renderer:Ext.util.Format.numberRenderer('0'),orientation:'vertical',color:'#333'},xField:'name',yField:'data'}]});";
	}
	if(lbGridId.indexOf("lb_Ext_Pie_Panel_auto") >= 0){//如果编辑的是Tree
		var gridCode="var store=Ext.create('Ext.data.JsonStore',{fields:['name','data'],data:[{'name':'metric one','data':10},{'name':'metric two','data':7},{'name':'metric three','data':5},{'name':'metric four','data':2},{'name':'metric five','data':27}]});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('"+lbGridId+"'),width:'100%',height:350,animate:true,store:store,theme:'Base:gradients',series:[{type:'pie',angleField:'data',showInLegend:true,tips:{trackMouse:true,width:140,height:28,renderer:function(storeItem,item){var total=0;store.each(function(rec){total+=rec.get('data')});this.setTitle(storeItem.get('name')+': '+Math.round(storeItem.get('data')/total*100)+'%')}},highlight:{segment:{margin:20}},label:{field:'name',display:'rotate',contrast:true,font:'18px Arial'}}]});";
	}
	
	
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks22');
		localStorage.setItem(lbGridId, gridCode)
	}
	
	myWindow=window.open("page/extjs/edit.html?id="+lbGridId+"&data="+Date.parse(new Date()),'','width=1100,height=600');
	myWindow.focus();
	lbOpenWindow=true;//检测同一时间只能打开一个窗口
	//检测同一时间只能打开一个窗口
	var lbloop = setInterval(function() { 
		if(myWindow.closed) {  
			clearInterval(lbloop);  
			lbOpenWindow=false;
		}  
	}, 500);
	
	
}

function supportstorage(){//检查浏览器是否支持本地存储功能
	if (typeof window.localStorage=='object') 
		return true;
	else
		return false;		
}

function handleSaveLayout() {
	var e = $(".demo").html();
	if (!stopsave && e != window.demoHtml) {
		stopsave++;
		window.demoHtml = e;
		saveLayout();
		stopsave--;
	}
}

var layouthistory; 
function saveLayout(){
	var data = layouthistory;
	if (!data) {
		data={};
		data.count = 0;
		data.list = [];
	}
	if (data.list.length>data.count) {
		for (i=data.count;i<data.list.length;i++)
			data.list[i]=null;
	}
	data.list[data.count] = window.demoHtml;
	data.count++;
	if (supportstorage()) {
		//localStorage.setItem("layoutdata",JSON.stringify(data));
	}
	layouthistory = data;
	//console.log(data);
	/*$.ajax({  
		type: "POST",  
		url: "/build/saveLayout",  
		data: { layout: $('.demo').html() },  
		success: function(data) {
			//updateButtonsVisibility();
		}
	});*/
}

function downloadLayout(){
	console.log('downloadLayout执行');
	$.ajax({  
		type: "POST",  
		url: "/build/downloadLayout",  
		data: { layout: $('#download-layout').html() },  
		success: function(data) { window.location.href = '/build/download'; }
	});
}

function downloadHtmlLayout(){
	console.log('downloadHtmlLayout执行');
	$.ajax({  
		type: "POST",  
		url: "/build/downloadLayout",  
		data: { layout: $('#download-layout').html() },  
		success: function(data) { window.location.href = '/build/downloadHtml'; }
	});
}

function undoLayout() {
	console.log('undoLayout执行');
	var data = layouthistory;
	//console.log(data);
	if (data) {
		if (data.count<2) return false;
		window.demoHtml = data.list[data.count-2];
		data.count--;
		$('.demo').html(window.demoHtml);
		if (supportstorage()) {
			//localStorage.setItem("layoutdata",JSON.stringify(data));
		}
		return true;
	}
	return false;
	/*$.ajax({  
		type: "POST",  
		url: "/build/getPreviousLayout",  
		data: { },  
		success: function(data) {
			undoOperation(data);
		}
	});*/
}

function redoLayout() {
	console.log('redoLayout执行');
	var data = layouthistory;
	if (data) {
		if (data.list[data.count]) {
			window.demoHtml = data.list[data.count];
			data.count++;
			$('.demo').html(window.demoHtml);
			if (supportstorage()) {
				//localStorage.setItem("layoutdata",JSON.stringify(data));
			}
			return true;
		}
	}
	return false;
	/*
	$.ajax({  
		type: "POST",  
		url: "/build/getPreviousLayout",  
		data: { },  
		success: function(data) {
			redoOperation(data);
		}
	});*/
}

function handleJsIds() {
	console.log('handleJsIds执行');
	handleModalIds();
	handleAccordionIds();
	handleCarouselIds();
	handleTabsIds()
}
function handleAccordionIds() {
	console.log('handleAccordionIds执行');
	var e = $(".demo #myAccordion");
	var t = randomNumber();
	var n = "accordion-" + t;
	var r;
	e.attr("id", n);
	e.find(".accordion-group").each(function(e, t) {
		r = "accordion-element-" + randomNumber();
		$(t).find(".accordion-toggle").each(function(e, t) {
			$(t).attr("data-parent", "#" + n);
			$(t).attr("href", "#" + r)
		});
		$(t).find(".accordion-body").each(function(e, t) {
			$(t).attr("id", r)
		})
	})
}
function handleCarouselIds() {
	console.log('handleCarouselIds执行');
	var e = $(".demo #myCarousel");
	var t = randomNumber();
	var n = "carousel-" + t;
	e.attr("id", n);
	e.find(".carousel-indicators li").each(function(e, t) {
		$(t).attr("data-target", "#" + n)
	});
	e.find(".left").attr("href", "#" + n);
	e.find(".right").attr("href", "#" + n)
}
function handleModalIds() {
	console.log('handleModalIds执行');
	var e = $(".demo #myModalLink");
	var t = randomNumber();
	var n = "modal-container-" + t;
	var r = "modal-" + t;
	e.attr("id", r);
	e.attr("href", "#" + n);
	e.next().attr("id", n)
}
function handleTabsIds() {
	console.log('handleTabsIds执行');
	var e = $(".demo #myTabs");
	var t = randomNumber();
	var n = "tabs-" + t;
	e.attr("id", n);
	e.find(".tab-pane").each(function(e, t) {
		var n = $(t).attr("id");
		var r = "panel-" + randomNumber();
		$(t).attr("id", r);
		$(t).parent().parent().find("a[href=#" + n + "]").attr("href", "#" + r)
	})
}
function randomNumber() {
	console.log('randomNumber执行');
	return randomFromInterval(1, 1e6)
}
function randomFromInterval(e, t) {
	console.log('randomFromInterval执行');
	return Math.floor(Math.random() * (t - e + 1) + e)
}
function gridSystemGenerator() {
	console.log('gridSystemGenerator执行');
	$(".lyrow .preview input").bind("keyup", function() {
		var e = 0;
		var t = "";
		var n = $(this).val().split(" ", 12);
		$.each(n, function(n, r) {
			e = e + parseInt(r);
			t += '<div class="span' + r + ' column"></div>'
		});
		if (e == 12) {
			$(this).parent().next().children().html(t);
			$(this).parent().prev().show()
		} else {
			$(this).parent().prev().hide()
		}
	})
}
function configurationElm(e, t) {
	console.log('configurationElm执行');
	$(".demo").delegate(".configuration > a", "click", function(e) {
		e.preventDefault();
		var t = $(this).parent().next().next().children();
		$(this).toggleClass("active");
		t.toggleClass($(this).attr("rel"))
	});
	$(".demo").delegate(".configuration .dropdown-menu a", "click", function(e) {
		e.preventDefault();
		var t = $(this).parent().parent();
		var n = t.parent().parent().next().next().children();
		t.find("li").removeClass("active");
		$(this).parent().addClass("active");
		var r = "";
		t.find("a").each(function() {
			r += $(this).attr("rel") + " "
		});
		t.parent().removeClass("open");
		n.removeClass(r);
		n.addClass($(this).attr("rel"))
	})
}
function removeElm(that) {//删除元素按钮
	//url配置删除
	console.log('url删除');
	$(that).siblings('.view').find('*[id*=lb_Ext_grid_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Panel_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Tree_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Tabs_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Charts_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Brokenline_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Column_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Pie_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).siblings('.view').find('*[id*=lb_Ext_Radar_Panel_auto]').each(function(i,ele) {
		console.log('删除缓存字段 '+$(ele).prop('id'));
		localStorage.removeItem($(ele).prop('id')); 
	});
	$(that).parent().remove();
	if (!$(".demo .lyrow").length > 0) {
		clearDemo()
	}
	//点击删除按钮删除
	$(".demo").delegate(".remove", "click", function(e) {
		console.log(e)
		console.log('removeElm执行');
		//找到当前点击删除的按钮元素内的所有 renderTo的id字段 然后删除以这些字段缓存的js代码数据
		$(this).siblings('.view').children().each(function(i,ele) {
			console.log('20190903NEW删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		
		//找到当前点击删除的按钮元素内的所有 renderTo的id字段 然后删除以这些字段缓存的js代码数据
		console.log($(this).siblings('.view').find('*[id*=lb_Ext_grid_Panel_auto]'));
		$(this).siblings('.view').find('*[id*=lb_Ext_grid_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Panel_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Tree_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Tabs_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Charts_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Brokenline_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Column_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Pie_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		$(this).siblings('.view').find('*[id*=lb_Ext_Radar_Panel_auto]').each(function(i,ele) {
			console.log('删除缓存字段 '+$(ele).prop('id'));
			localStorage.removeItem($(ele).prop('id')); 
		});
		e.preventDefault();
		$(this).parent().remove();
		if (!$(".demo .lyrow").length > 0) {
			clearDemo()
		}
		if(myWindow){
			myWindow.close();//关闭编辑代码窗口
		}
		console.log('删除完毕');
		
	})
	
}

function clearDemo() {
	console.log('clearDemo执行');
	localStorage.clear();
	$(".demo").empty();
	layouthistory = null;
	if (supportstorage())
		localStorage.removeItem("layoutdata");
}
function removeMenuClasses() {
	console.log('removeMenuClasses执行');
	$("#menu-layoutit li button").removeClass("active")
}
function changeStructure(e, t) {
	console.log('changeStructure执行');
	$("#download-layout ." + e).removeClass(e).addClass(t)
}
function cleanHtml(e) {
	console.log('cleanHtml执行');
	$(e).parent().append($(e).children().html());
}
function downloadLayoutSrc() {//点击生成代码执行函数
	console.log('downloadLayoutSrc执行');
	var e = "";
	$("#download-layout").children().html($(".demo").html());//将当前拖拽后的元素html代码复制到另一个隐藏的元素里
	var t = $("#download-layout").children();//获取生成的代码元素
	t.find(".preview, .configuration, .drag, .remove").remove();
	t.find(".x-grid-with-row-lines").remove();//删除grid组件隐藏元素中多余代码 只留renderTo：一个元素
	t.find(".x-panel-default").remove();//删除panel组件隐藏元素中多余代码 只留renderTo：一个元素
	t.find(".x-surface-default").remove();//删除charts组件隐藏元素中多余代码 只留renderTo：一个元素
	t.find("script").remove();//在生成代码中删除所有script标签
	t.find(('div[id^="lb"]')).children().remove();//在生成代码中只保留renderTo元素
	
	t.find(".lyrow").addClass("removeClean");
	t.find(".box-element").addClass("removeClean");
	t.find(".lyrow .lyrow .lyrow .lyrow .lyrow .removeClean").each(function() {
		cleanHtml(this)
	});
	t.find(".lyrow .lyrow .lyrow .lyrow .removeClean").each(function() {
		cleanHtml(this)
	});
	t.find(".lyrow .lyrow .lyrow .removeClean").each(function() {
		cleanHtml(this)
	});
	t.find(".lyrow .lyrow .removeClean").each(function() {
		cleanHtml(this)
	});
	t.find(".lyrow .removeClean").each(function() {
		cleanHtml(this)
	});
	t.find(".removeClean").each(function() {
		cleanHtml(this)
	});
	t.find(".removeClean").remove();
	$("#download-layout .column").removeClass("ui-sortable");
	$("#download-layout .row-fluid").removeClass("clearfix").children().removeClass("column");
	if ($("#download-layout .container").length > 0) {//如果有$("#download-layout .container")这个元素
		changeStructure("row-fluid", "row")
	}
	formatSrc = $.htmlClean($("#download-layout").html(), {
		format: true,
		allowedAttributes: [
			["id"],
			["class"],
			["data-toggle"],
			["data-target"],
			["data-parent"],
			["role"],
			["data-dismiss"],
			["aria-labelledby"],
			["aria-hidden"],
			["data-slide-to"],
			["data-slide"],
			["src"],
			["type"],
			["value"],
			["lb-data"]
		]
	});
	//console.log(formatSrc);
	$("#download-layout").html(formatSrc);
	$("#downloadModal textarea").empty();
	$("#downloadModal textarea").val(formatSrc)
	return formatSrc
}

var currentDocument = null;
var timerSave = 1000;
var stopsave = 0;
var startdrag = 0;
var demoHtml = $(".demo").html();
var currenteditor = null;
var LbGridDivLength=0;
var LbTarget=null;
$(window).resize(function() {
	console.log('$(window).resize执行');
	
	
});

function restoreData(){
	console.log('restoreData执行');
	if (supportstorage()) {//如果浏览器支持本地存储
		layouthistory = JSON.parse(localStorage.getItem("layoutdata"));
		if (!layouthistory) return false;
		window.demoHtml = layouthistory.list[layouthistory.count-1];
		if (window.demoHtml) $(".demo").html(window.demoHtml);
	} 
}

function initContainer(){
	console.log('initContainer执行');
	$(".demo, .demo .column").sortable({
		connectWith: ".column",
		opacity: .35,
		handle: ".drag",
		start: function(e,t) {
			if (!startdrag) stopsave++;
			startdrag = 1;
		},
		stop: function(e,t) {
			if(stopsave>0) stopsave--;
			startdrag = 0;
		}
	});
	
	configurationElm();
}


$(document).ready(function() {
	console.log('onready');
	
	setTimeout(function(){
		$(".row-fluid .ui-sortable").eq(0).sortable({
			connectWith: ".ui-sortable",
			start: function(e,t) {
			},
			stop: function(e,t) {
			}
		});
		$(".demo .column").sortable({
			opacity: .35,
			handle: ".drag",
			connectWith: ".column",
			start: function(e,t) {
				if (!startdrag) stopsave++;
				startdrag = 1;
			},
			stop: function(e,t) {
				if(stopsave>0) stopsave--;
				startdrag = 0;
			}
		});
		//LbCode
		$( ".lyrow .view .row-fluid .column" ).droppable({
		  drop: function( event, ui ) {
			console.log('LbTarget放置目标');
		    console.log(event.target);//放置目标
			LbTarget=event.target;
			
		  }
		});
		console.log('tt2');
	},500);
	CKEDITOR.disableAutoInline = true;
	
	//restoreData();//检测是否支持本地存储和获取本地已存储数据
	var contenthandle = CKEDITOR.replace( 'contenteditor' ,{
		language: 'zh-cn',
		contentsCss: ['page/extjs/css/bootstrap-combined.min.css'],
		allowedContent: true
	});
	$("body").css("min-height", $(window).height() - 90);
	$(".demo").css("min-height", $(window).height() - 160);
	$(".sidebar-nav .lyrow").draggable({
		connectToSortable: ".demo",
		helper: "clone",
		handle: ".drag",
		start: function(e,t) {
			console.log('开始拖拽');
			if (!startdrag) stopsave++;
			startdrag = 1;
			console.log('startdrag=='+startdrag);
			console.log('stopsave=='+stopsave);
		},
		drag: function(e, t) {
			console.log('拖拽中...')
			t.helper.width(400);//拖拽途中显示的半透明helper的宽度
		},
		stop: function(e, t) {
			console.log('停止拖拽11')
			$(".demo .column").sortable({
				opacity: .35,
				handle: ".drag",
				connectWith: ".column",
				start: function(e,t) {
					if (!startdrag) stopsave++;
					startdrag = 1;
				},
				stop: function(e,t) {
					if(stopsave>0) stopsave--;
					startdrag = 0;
					
				}
			});
			if(stopsave>0) stopsave--;
			startdrag = 0;
			//LbCode
			$( ".lyrow .view .row-fluid .column" ).droppable({
			  drop: function( event, ui ) {
				console.log('LbTarget放置目标22');
			    console.log(event.target);//放置目标
				LbTarget=event.target;
				
			  }
			});
			//lbDragStop1(e);
			//end
		}
	});
	$(".sidebar-nav .box").draggable({
		connectToSortable: ".column",
		helper: "clone",
		handle: ".drag",
		start: function(e,t) {
			console.log('开始拖拽22')
			if (!startdrag) stopsave++;
			startdrag = 1;
			LbTarget=null;
			console.log(LbTarget);
		},
		drag: function(e, t) {
			console.log('拖拽中..')
			t.helper.width(400)
		},
		stop: function(event, ui) {
			console.log(event, ui);
			handleJsIds();
			if(stopsave>0) stopsave--;
			startdrag = 0;
			//LbCode 
			//拖动GRID大组件
			if($($(event)[0].target).parent(".boxes").siblings(".nav-header").html().indexOf("套件") >= 0){
				var bigCode=$($(event)[0].target).children('.view').attr('code');//要执行的代码
				var bigCodeId=$($(event)[0].target).children('.view').attr('CodeId');
				var LbGridDiv=$("<div class='"+bigCodeId+"' id='"+bigCodeId+"'></div>");
				var LbGridScript=$("<script type="+"text/javascript "+"src="+""+bigCodeId+".js"+"></script>");
				if(LbTarget==null){
					return
				}
				var nowRemove=null;
				for(var qq=0;qq<$(LbTarget).children('.box-element').length;qq++){
					if($(LbTarget).children('.box-element').eq(qq).children('.view').children().length==0){
						$(LbTarget).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
						nowRemove=$(LbTarget).children('.box-element').eq(qq).children('.remove');
						//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
					}
				}
				if($('.'+bigCodeId).length>1){
					removeElm(nowRemove);
					Ext.Msg.alert('提示', '刷新页面or更换组件的渲染ID'); 
					return 
				}
				eval(bigCode);
				
				
				
				//修复必须点击编辑才能看见组件预览的效果
				lbGridId=bigCodeId;
				var gridCode=bigCode;
				if(localStorage.getItem(lbGridId)==null){
					console.log('fristTime clicks');
					localStorage.setItem(lbGridId, gridCode)
				}
				//更新后台bean对象20190610
				console.log('更新后台bean对象');
				if(bean!=null){
					console.log('更新后台bean对象');
					var obj={};
					obj.content=gridCode;
					obj.path=lbGridId;
					bean[0].list.push(obj);
				}
				return
			}
			//如果拖动的是自定义组件
			if($($(event)[0].target).parent(".boxes").siblings(".nav-header").html().indexOf("自定义组件") >= 0){

				var ZdyCode=$($(event)[0].target).children('.view').attr('code');//要执行的代码
				
				var gridPanelWidth=ZdyCode.indexOf(name,ZdyCode.indexOf('Ext.get'));
				var lb20190823str1=ZdyCode.indexOf("'",gridPanelWidth)+1;//开头
				
				var lb20190823str2=ZdyCode.indexOf("'",lb20190823str1);//结尾
				
				var lb20190903str=ZdyCode.substring(lb20190823str1,lb20190823str2);

				var LbGridDiv=$("<div class='"+lb20190903str+"' id='"+lb20190903str+"'></div>");
				var LbGridScript=$("<script type="+"text/javascript "+"src="+""+lb20190903str+".js"+"></script>");
				
				
				if(LbTarget==null){
					return
				}
				var nowRemove=null;
				for(var qq=0;qq<$(LbTarget).children('.box-element').length;qq++){
					if($(LbTarget).children('.box-element').eq(qq).children('.view').children().length==0){
						$(LbTarget).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
						nowRemove=$(LbTarget).children('.box-element').eq(qq).children('.remove')
						//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
					}
				}
				if($('.'+lb20190903str).length>1){
					removeElm(nowRemove);
					Ext.Msg.alert('提示', 'ID相同，请更换组件的渲染ID'); 
					return 
				}
				eval(ZdyCode);
				
				
				
				//修复必须点击编辑才能看见组件预览的效果
				lbGridId=lb20190903str;
				var gridCode=ZdyCode;
				if(localStorage.getItem(lbGridId)==null){
					console.log('fristTime clicks');
					localStorage.setItem(lbGridId, gridCode)
				}
				//更新后台bean对象20190610
				console.log('更新后台bean对象');
				if(bean!=null){
					console.log('更新后台bean对象');
					var obj={};
					obj.content=gridCode;
					obj.path=lbGridId;
					bean[0].list.push(obj);
				}
				return
			}
			
			//如果拖动的是数据表格
			if(ui.helper[0].innerText.indexOf("套件") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					return
				} */
				console.log(LbTarget); 
				console.log($(LbTarget).children('.box-element').length); 
				console.log(ui.helper[0]);
				//第一次进页面的url数据源配置代码
				var lbGridNowDivId='lb_Ext_grid_Panel_auto'+LbGridDivCreate(LbTarget,++LbGridDivLength);
				lbGridUrlConfig(lbGridNowDivId,ui.helper);
				//end
				
			}
			
			//如果拖动的是Panel面板
			if(ui.helper[0].innerText.indexOf("Panel面板") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbPanelDivCreate(LbTarget,++LbGridDivLength);
			}
			
			//如果拖动的是Tree面板
			if(ui.helper[0].innerText.indexOf("Tree面板") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbTreeDivCreate(LbTarget,++LbGridDivLength);
			}
			
			//如果拖动的是tabs面板
			if(ui.helper[0].innerText.indexOf("tabs面板") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbTabsDivCreate(LbTarget,++LbGridDivLength);
			}
			
			//如果拖动的是Charts面板
			if(ui.helper[0].innerText.indexOf("charts面板") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbChartsDivCreate(LbTarget,++LbGridDivLength);
			}
			
			//如果拖动的是EXT折线图面板
			if(ui.helper[0].innerText.indexOf("EXT折线图") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbBrokenlineDivCreate(LbTarget,++LbGridDivLength);
			}
			
			//如果拖动的是EXT柱状图面板
			if(ui.helper[0].innerText.indexOf("EXT柱状图") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbColumnDivCreate(LbTarget,++LbGridDivLength);
			}
			
			//如果拖动的是EXT柱状图面板
			if(ui.helper[0].innerText.indexOf("EXT饼图") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbPieDivCreate(LbTarget,++LbGridDivLength);
			}
			
			if(ui.helper[0].innerText.indexOf("EXT雷达图") >= 0){
				/* if($(LbTarget).children('.box-element').length>1){
					console.log('同一区域只能有一个ext组件');
					alert('请新建空白区块放置ext组件');
					for(var lbi=0;lbi<$(LbTarget).children('.box-element').length;lbi++){
						if($(LbTarget).children('.box-element').eq(lbi).children('.view').children().length==0){
							$(LbTarget).children('.box-element').eq(lbi).remove();
							if (!$(".demo .lyrow").length > 0) {
								clearDemo()
							}
						}
					}
					
					return
				} */
				console.log(LbTarget); 
				LbChartsRadarDivCreate(LbTarget,++LbGridDivLength);
			}
			if(ui.helper[0].innerText.indexOf("按钮") >= 0){
				lbDragStop('按钮',event);
			}
			console.log('停止拖拽');
		}
	});
	
	initContainer();
	
	$('body.edit .demo').on("click","[data-target=#editorModal]",function(e) {
		e.preventDefault();
		currenteditor = $(this).parent().parent().find('.view');
		var eText = currenteditor.html();
		contenthandle.setData(eText);
	});
	$("#savecontent").click(function(e) {
		e.preventDefault();
		currenteditor.html(contenthandle.getData());
	});
	$("[data-target=#downloadModal]").click(function(e) {
		e.preventDefault();
		downloadLayoutSrc();
	});
	$("[data-target=#shareModal]").click(function(e) {
		e.preventDefault();
		handleSaveLayout();
	});
	$("#download").click(function() {
		console.log('$("#download").click执行');
		downloadLayout();
		return false
	});
	$("#downloadhtml").click(function() {
		console.log('$("#downloadhtml").click执行');
		downloadHtmlLayout();
		return false
	});
	$("#edit").click(function() {
		console.log('$("#edit").click执行');
		$("body").removeClass("devpreview sourcepreview");
		$("body").addClass("edit");
		removeMenuClasses();
		$(this).addClass("active");
		
		return false
	});
	$("#clear").click(function(e) {
		console.log('$("#clear").click执行');
		e.preventDefault();
		clearDemo()
	});
	$("#devpreview").click(function() {
		console.log('$("#devpreview").click执行');
		$("body").removeClass("edit sourcepreview");
		$("body").addClass("devpreview");
		removeMenuClasses();
		$(this).addClass("active");
		
		return false
	});
	$("#sourcepreview").click(function() {
		console.log('$("#sourcepreview").click执行');
		$("body").removeClass("edit");
		$("body").addClass("devpreview sourcepreview");
		removeMenuClasses();
		$(this).addClass("active");
		
		return false
	});
	$("#fluidPage").click(function(e) {
		console.log('$("#fluidPage").click执行');
		e.preventDefault();
		changeStructure("container", "container-fluid");
		$("#fixedPage").removeClass("active");
		$(this).addClass("active");
		downloadLayoutSrc()
	});
	$("#fixedPage").click(function(e) {
		console.log('$("#fixedPage").click执行');
		e.preventDefault();
		changeStructure("container-fluid", "container");
		$("#fluidPage").removeClass("active");
		$(this).addClass("active");
		downloadLayoutSrc()
	});
	$(".nav-header").click(function() {
		console.log('$(".nav-header").click执行');
		$(".sidebar-nav .boxes, .sidebar-nav .rows").hide();
		$(this).next().slideDown()
	});
	$('#undo').click(function(){
		console.log('$("#undo").click执行');
		stopsave++;
		if (undoLayout()) initContainer();
		stopsave--;
	});
	$('#redo').click(function(){
		console.log('$("#redo").click执行');
		stopsave++;
		if (redoLayout()) initContainer();
		stopsave--;
	});
	removeElm();
	gridSystemGenerator();
	setInterval(function() {
		handleSaveLayout()
	}, timerSave);
	

	
	
});
//grid表格请求数据返回结果拼装
function lbGridUrlConfig(lbId,helper){
	var lbGridNowDivId=lbId;
	console.log(lbGridNowDivId);
	console.log($('#'+lbGridNowDivId).parent('.view').siblings('.configuration').children('.lburl'))
	
	var lbGridNowDivSpan=$('#'+lbGridNowDivId).parent('.view').siblings('.configuration').children('.lburl').find('.submitUrl');
	$(lbGridNowDivSpan).on('click', function(e){
		if (e.target == e.currentTarget) {
			var value1=$(lbGridNowDivSpan).siblings('.lburlDiv1').val(); //url请求地址
			console.log(value1);
		    var lbServerDataObjKeyCount=0;//返回的column列数
		    var lbServerDataObjKey=[];//返回的column列字段
		    var lbStrArr=[];
		    lbStrArr=value1/*.split("?")*/;
		    if(value1.length<8){
		    	Ext.MessageBox.alert("错误","数据与组件column不匹配，请重新输入");
		    	return
		    }
		    $.post(value1,function(result){console.log(result.dataList);
		    	if(result.dataList!=undefined){
		    		//result.dataList.unshift({value: "asdasdasd", key: "22222222222", flex2: '12213123', flex3: '12213123', flex33: '12213123'});
		    		console.log(result.dataList);
		    		jQuery.each(result.dataList[0], function(i, val) {  
		    			lbServerDataObjKeyCount+=1;
		    			lbServerDataObjKey.push(i);
			        });
		    		
		    	}
		        if($(lbGridNowDivSpan).find('.lburlDiv2').val()!=''&&lbServerDataObjKeyCount==$(lbGridNowDivSpan).siblings('.lburlDiv2').val()){
		        	console.log('succ1')
		        	console.log(lbServerDataObjKey);
		        	lbGridId=$(e.target).parent().siblings('.view').children('*[id*=lb_Ext_grid_Panel_auto]').attr('id');//当前点击的id
		        	
					//localStorage.removeItem(lbGridId);
		        	var lbArr=[];
		    		lbArr.push(result); lbArr.push(lbServerDataObjKey);
		    		
		    		
		    		
		    		var lbfangzhimubiao=$(lbGridNowDivSpan).parent().parent().parent().parent().parent('*[class*=span]');
		    		console.log(lbfangzhimubiao);
		    		//删除当前组件的缓存代码
		        	removeElm($(lbGridNowDivSpan).parent().parent().parent('.configuration').siblings('.remove'));
					//写一套新的url远程数据的grid组件
		    		LbUrlGridDivCreate(lbArr,lbfangzhimubiao,lbStrArr/*[0]*/)
					//将新代码存入缓存对应key 
					//执行新代码显示效果
		        }else{
		        	Ext.MessageBox.alert("错误","数据与组件column不匹配，请重新输入");
		        	$(lbGridNowDivSpan).siblings('.lburlDiv2').val('');
		        }
		    });
		}
		return 
	});
}
function LbUrlChartsColumnDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('EXT柱状图')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	function ChartsLineStoreFields(){
		var arr=[];
		for(var key in lbArr.data[0]){
			arr.push(key);
		}
		console.log(arr)
		return JSON.stringify(arr)
	}
	var lbinitialUrlCode="var store=Ext.create('Ext.data.JsonStore',{fields:"+ChartsLineStoreFields()+",data:"+JSON.stringify(lbArr.data)+"});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Column_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'300',padding:'',margin:'',border:'',bodyStyle:'',animate:true,store:store,axes:[{type:'Numeric',position:'left',fields:['data'],label:{renderer:Ext.util.Format.numberRenderer('0,0')},title:'Sample Values',grid:true,minimum:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics'}],series:[{type:'column',axis:'left',highlight:true,tips:{trackMouse:true,width:140,height:28,renderer:function(storeItem,item){this.setTitle(storeItem.get('name')+': '+storeItem.get('data')+' $')}},label:{display:'insideEnd','text-anchor':'middle',field:'data',renderer:Ext.util.Format.numberRenderer('0'),orientation:'vertical',color:'#333'},xField:'name',yField:'data'}]});";
	var LbGridDiv=$("<div id='lb_Ext_Column_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Column_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,JSON.stringify(lbArr));
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Column_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}



function LbUrlChartsPieDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('EXT饼图')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	function ChartsLineStoreFields(){
		var arr=[];
		for(var key in lbArr.data[0]){
			arr.push(key);
		}
		console.log(arr)
		return JSON.stringify(arr)
	}
	var lbinitialUrlCode="var store=Ext.create('Ext.data.JsonStore',{fields:"+ChartsLineStoreFields()+",data:"+JSON.stringify(lbArr.data)+"});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Pie_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'350',padding:'',border:'',margin:'',bodyStyle:'',animate:true,store:store,theme:'Base:gradients',series:[{type:'pie',angleField:'data',showInLegend:true,tips:{trackMouse:true,width:140,height:28,renderer:function(storeItem,item){var total=0;store.each(function(rec){total+=rec.get('data')});this.setTitle(storeItem.get('name')+': '+Math.round(storeItem.get('data')/total*100)+'%')}},highlight:{segment:{margin:20}},label:{field:'name',display:'rotate',contrast:true,font:'18px Arial'}}]});";
	var LbGridDiv=$("<div id='lb_Ext_Pie_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Pie_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,JSON.stringify(lbArr));
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Pie_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}

function LbUrlChartsRadarDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('EXT雷达图')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	function ChartsLineStoreFields(){
		var arr=[];
		for(var key in lbArr.data[0]){
			arr.push(key);
		}
		console.log(arr)
		return JSON.stringify(arr)
	}
	function ChartsRadarSeries(){
		var arr=[];
		for(var i=0;i<lbArr.data.length;i++){
			arr.push("{showInLegend:true,type:'radar',xField:'name',yField:'data"+(i+1)+"',style:{opacity:0.4}}");
		}
		console.log(arr);
		return '['+arr+']'
		
	}
	var lbinitialUrlCode="var store=Ext.create('Ext.data.JsonStore',{fields:"+ChartsLineStoreFields()+",data:"+JSON.stringify(lbArr.data)+"});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Radar_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'400',background:'#fff',padding:'',margin:'',border:'',padding:'',bodyStyle:'',animate:true,theme:'Category1',store:store,axes:[{type:'Radial',position:'radial',label:{display:true}}],series:"+ChartsRadarSeries()+"});";
	var LbGridDiv=$("<div id='lb_Ext_Radar_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Radar_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,JSON.stringify(lbArr));
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Radar_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}

function LbUrlChartsLineDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('EXT折线图')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	function ChartsLineStoreFields(){
		var arr=[];
		for(var key in lbArr.data[0]){
			arr.push(key);
		}
		console.log(arr)
		return JSON.stringify(arr)
	}
	function ChartsLineSeries(){
		var arr=[];
		for(var i=0;i<lbArr.data.length;i++){
			arr.push("{type:'line',highlight:{size:7,radius:7},axis:'left',fill:true,xField:'name',yField:'data"+(i+1)+"',markerConfig:{type:'circle',size:4,radius:4,'stroke-width':0}}");
		}
		console.log(arr);
		return '['+arr+']'
		
	}
	var lbinitialUrlCode="var store=Ext.create('Ext.data.JsonStore',{fields:"+ChartsLineStoreFields()+",data:"+JSON.stringify(lbArr.data)+"});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Brokenline_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'300',padding:'',margin:'',border:'',bodyStyle:'',animate:true,store:store,axes:[{type:'Numeric',position:'left',fields:"+ChartsLineStoreFields()+",label:{renderer:Ext.util.Format.numberRenderer('0,0')},title:'Sample Values',grid:true,minimum:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics'}],series:"+ChartsLineSeries()+"});";
	var LbGridDiv=$("<div id='lb_Ext_Brokenline_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Brokenline_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,JSON.stringify(lbArr));
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Brokenline_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}
function LbUrlChartsDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('charts面板')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	function ChartsStoreFields(){
		var obj=[];
		jQuery.each(lbArr.data[0], function(i, val) {  
			obj.push(i);
        });
		console.log(JSON.stringify(obj));
		return JSON.stringify(obj)
	}
	function ChartsAxesFields(){
		var obj=[];
		jQuery.each(lbArr.data[0], function(i, val) { 
			if(i!='name'){
				obj.push(i);
			}
			
        });
		console.log(JSON.stringify(obj));
		return JSON.stringify(obj)
	}
	function ChartsSeriesFields(){
		var obj=[];
		jQuery.each(lbArr.data[0], function(i, val) { 
			if(i!='name'){
				obj.push(i);
			}
			
        });
		console.log(JSON.stringify(obj));
		return JSON.stringify(obj)
	}
	var lbinitialUrlCode="var store=Ext.create('Ext.data.JsonStore',{fields:"+ChartsStoreFields()+",data:"+JSON.stringify(lbArr.data)+"});Ext.create('Ext.chart.Chart',{renderTo:Ext.get('lb_Ext_Charts_Panel_auto"+LbGridDivLength+"'),width:'100%',height:'400',padding:'',margin:'',border:'',bodyStyle:'',animate:true,store:store,shadow:true,theme:'Category1',legend:{position:'top'},axes:[{type:'Numeric',position:'left',fields:"+ChartsAxesFields()+",title:'Sample Values',grid:{odd:{opacity:1,fill:'#ddd',stroke:'#bbb','stroke-width':1}},minimum:0,adjustMinimumByMajorUnit:0},{type:'Category',position:'bottom',fields:['name'],title:'Sample Metrics',grid:true,label:{rotate:{degrees:315}}}],series:[{type:'area',highlight:false,axis:'left',xField:'name',yField:"+ChartsSeriesFields()+",style:{opacity:0.93}}]});";
	var LbGridDiv=$("<div id='lb_Ext_Charts_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Charts_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,JSON.stringify(lbArr));
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Charts_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}
function LbUrlTabsDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('tabs面板')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	
	var lbinitialUrlCode="Ext.create('Ext.tab.Panel',{width:'100%',height:'170',padding:'',margin:'',border:'',bodyStyle:'',activeTab:0,"+lbArr+",renderTo:Ext.get('lb_Ext_Tabs_Panel_auto"+LbGridDivLength+"')});";
	var LbGridDiv=$("<div id='lb_Ext_Tabs_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Tabs_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,lbArr);
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Tabs_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	
	return LbGridDivLength;
}

function LbUrlTreeDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find(".preview:contains('Tree面板')").parent('.box-element').clone());
	var LbGridDivLength=1;
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	
	
	var lbinitialUrlCode="var store=Ext.create('Ext.data.TreeStore',{"+lbArr+"});Ext.create('Ext.tree.Panel',{title:'Simple Tree',width:'100%',height:'150',padding:'',margin:'',border:'',bodyStyle:'',store:store,rootVisible:false,renderTo:Ext.get('lb_Ext_Tree_Panel_auto"+LbGridDivLength+"')});";

	var LbGridDiv=$("<div id='lb_Ext_Tree_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_Tree_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	localStorage.setItem('lbArr'+LbGridDivLength,lbArr);
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_Tree_Panel_auto"+LbGridDivLength;
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}
function LbUrlGridDivCreate(lbArr,LbTarget1,lbStrArr){
	var editCode='';
	console.log(lbArr);
	console.log(LbTarget1);
	console.log(lbStrArr);
	$(LbTarget1).append($('.sidebar-nav').find("[lbcssconfig='Ext.grid.Panel']").parent('.box-element').clone());
	var LbGridDivLength=1;//20190610
	for(var lby=0;lby<$("#lbSave").find("*[id*=lb_Ext]").length;){
		if($("#lbSave").find("*[id*=lb_Ext]")[lby].id.charAt($("#lbSave").find("*[id*=lb_Ext]")[lby].id.length-1)==LbGridDivLength){
			++LbGridDivLength;lby=0;
		}else{
			++lby;
		}
	}
	console.log(LbTarget1);
	console.log(LbGridDivLength);
	
	function makeLbGirdColumn() {
	    var column = [];
	    for (var i = 0; i < lbArr[1].length; i++) {
	        var obj = {
	            text: lbArr[1][i],
	            dataIndex: lbArr[1][i],
	            flex: 1
	        };
	        column.push(obj);
	    }
	    column.unshift({
	        text: '序号',
	        width: 45,
	        xtype: 'rownumberer'
	    });
	    return column;
	}
	var gridPanelColumn=JSON.stringify(makeLbGirdColumn());
	console.log(gridPanelColumn);
	
	function makeLbGirdFields() {
	    var column = [];
	    for (var i = 0; i < lbArr[1].length; i++) {
	        var obj = {
	            name: lbArr[1][i],
	            type: 'String'
	        };
	        column.push(obj);
	    }

	    return column;
	}
	var gridStoreColumn=JSON.stringify(makeLbGirdFields());
	console.log(gridStoreColumn);
	
	var lbinitialUrlCode="var extGridPanelStore=Ext.create('Ext.data.Store',{storeId:'simpsonsStore',fields:"+gridStoreColumn+",pageSize:10,proxy:{type:'ajax',url:'"+lbStrArr+"',reader:{type:'json',root:'dataList',totalProperty:'total'}}});extGridPanelStore.loadPage(1);function QueryMessage(){extGridPanelStore.reload({params:{start:0,limit:10,page:1,keyPar:keyQuery.getValue().trim()}})}var keyQuery=Ext.create('Ext.form.TextField',{margin:'5',name:'key',emptyText:'--请输入key--',labelWidth:80,width:'15%',xtype:'textfield'});Ext.create('Ext.grid.Panel',{width:'100%',height:'',padding:'',margin:'',border:'',store:Ext.data.StoreManager.lookup('simpsonsStore'),columns:"+gridPanelColumn+",plugins:[Ext.create('Ext.grid.plugin.CellEditing',{clicksToEdit:1})],dockedItems:[{xtype:'pagingtoolbar',store:extGridPanelStore,dock:'bottom',displayInfo:true},{xtype:'toolbar',dock:'top',items:[keyQuery,{xtype:'button',cls:'Common_Btn',text:'查询',handler:function(){QueryMessage()}}]}],renderTo:Ext.get('lb_Ext_grid_Panel_auto"+LbGridDivLength+"')});";

	
	var LbGridDiv=$("<div id='lb_Ext_grid_Panel_auto"+LbGridDivLength+"'></div>");
	var LbGridScript=$("<script type="+"text/javascript "+"src="+"newjs/lb_Ext_grid_Panel_auto"+LbGridDivLength+".js"+"></script>");
	console.log(LbGridScript);
	if(LbTarget1==null){
		console.log('LbTarget1==null')
		return
	}
	console.log($(LbTarget1).children('.box-element').length)
	for(var qq=0;qq<$(LbTarget1).children('.box-element').length;qq++){
		if($(LbTarget1).children('.box-element').eq(qq).children('.view').children().length==0){
			$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridDiv);
			//$(LbTarget1).children('.box-element').eq(qq).children('.view').append(LbGridScript);
		}
	}
	console.log('uuuuuuu');
	eval(lbinitialUrlCode);
	/*var aaaa='keyQuery'+LbGridDivLength;
	var keyQuery = Ext.create('Ext.form.TextField', {
		margin : '5',
		name : 'key',
		emptyText : '--请输入key--',
		labelWidth : 80,
		width : '15%',
		xtype : 'textfield'
	});
	function makeLbGirdFields(){
		console.log(lbArr)
		console.log(lbStrArr)
		var column=[];
		for(var i=0;i<lbArr[1].length;i++){
			var obj={name:lbArr[1][i],type:'String'};
			column.push(obj);
		}
		
		console.log(column)
		return column;
	}
	var extGridPanelStore = Ext.create('Ext.data.Store', {
		storeId: 'simpsonsStore',
		fields: makeLbGirdFields(),[{
			name: 'key',
			type: 'String'
		}, {
			name: 'value',
			type: 'String'
		}],
		pageSize: 10,
		proxy: {
			type: 'ajax',
			url: lbStrArr,
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});
	extGridPanelStore.loadPage(1);
	function save() {
		var m = extGridPanelStore.getModifiedRecords();
		if(m.length==0){
			Ext.MessageBox.alert("提示", "没有需要保存的条目！");
			return;
		}else{
			var jsonData = "[";
			for (var i = 0; i < m.length; i++) {
				var date = Ext.JSON.encode(m[i].data);
				if (i == 0)
					jsonData = jsonData + date;
				else
					jsonData = jsonData + "," + date;
			}
			jsonData = jsonData + "]";
			Ext.Ajax.request({
				url : 'configSave.do',
				method : 'post',
				params : {
					jsonData : jsonData
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					if (true == success) {
						extGridPanelStore.reload({
							params : {
								start : 0,
								limit : 10,
								page :1
							}
						});
						Ext.Msg.alert("提示", "保存成功！");
					} else {
						Ext.Msg.alert("提示", "保存失败！");
					}
				},
				failure : function(result, request) {
					secureFilterRs(result,"操作失败！");
	
				}
			});
		}
	}
	function QueryMessage (){
		extGridPanelStore.reload({
			params : {
				start : 0,
				limit : 10,
				page :1,
				keyPar : keyQuery.getValue().trim()
			}
		});
	}
	function makeLbGirdColumn(){
		console.log(lbArr)
		var column=[];
		for(var i=0;i<lbArr[1].length;i++){
			var obj={text:lbArr[1][i],dataIndex:lbArr[1][i],flex:1};
			column.push(obj);
		}
		console.log(column)
		column.unshift({text: '序号',width: 45,xtype: 'rownumberer'});
		return column;
	}
	
	Ext.create('Ext.grid.Panel', {
		store: Ext.data.StoreManager.lookup('simpsonsStore'),
		columns: makeLbGirdColumn(),
		plugins: [Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit: 1
		})],
		dockedItems: [{
			xtype: 'pagingtoolbar',
			store: extGridPanelStore,
			dock: 'bottom',
			displayInfo: true
		},{
			xtype : 'toolbar',
			dock : 'top',
			items : [keyQuery, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function() {
					QueryMessage();
				}
			},'->',{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '保存'
			}]
		}],
		width: '100%',
		renderTo: 'lb_Ext_grid_Panel_auto'+LbGridDivLength
	});*/
	localStorage.setItem('lbArr'+LbGridDivLength,JSON.stringify(lbArr));
	var lbGridNowDivSpan=$('#lb_Ext_grid_Panel_auto'+LbGridDivLength).parent('.view').siblings('.configuration').children('.lburl').find('.submitUrl');
	$(lbGridNowDivSpan).on('click', function(e){console.log(e);
		if (e.target == e.currentTarget) {
			var value1=$(lbGridNowDivSpan).siblings('.lburlDiv1').val(); //url请求地址
		    var lbServerDataObjKeyCount=0;//返回的column列数
		    var lbServerDataObjKey=[];//返回的column列字段
		    var lbStrArr=[];
		    lbStrArr=value1/*.split("?")*/;
		    if(value1.length<8){
		    	Ext.MessageBox.alert("错误","数据与组件column不匹配，请重新输入");
		    	return
		    }
		    $.post(value1,function(result){
		    	if(result.dataList!=undefined){
		    		//result.dataList.unshift({value: "asdasdasd", key: "22222222222", flex: '12213123'});
		    		console.log(result.dataList);
		    		jQuery.each(result.dataList[0], function(i, val) {  
		    			lbServerDataObjKeyCount+=1;
		    			lbServerDataObjKey.push(i);
			        });
		    	}
		        if($(lbGridNowDivSpan).siblings('.lburlDiv2').val()!=''&&lbServerDataObjKeyCount==$(lbGridNowDivSpan).siblings('.lburlDiv2').val()){
		        	console.log('succ1')
		        	console.log(lbServerDataObjKey);
		        	lbGridId=$(e.target).parent().siblings('.view').children('*[id*=lb_Ext_grid_Panel_auto]').attr('id');//当前点击的id
		        	
					//localStorage.removeItem(lbGridId);
		        	var lbArr=[];
		    		lbArr.push(result); lbArr.push(lbServerDataObjKey);
		    		var lbfangzhimubiao=$(lbGridNowDivSpan).parent().parent().parent().parent().parent('*[class*=span]');
		    		console.log(lbfangzhimubiao);
		    		//删除当前组件的缓存代码
		        	removeElm($(lbGridNowDivSpan).parent().parent().parent('.configuration').siblings('.remove'));
					//写一套新的url远程数据的grid组件
		    		LbUrlGridDivCreate(lbArr,lbfangzhimubiao,lbStrArr/*[0]*/)
					//将新代码存入缓存对应key 
					//执行新代码显示效果
		        }else{
		        	Ext.MessageBox.alert("错误","数据与组件column不匹配，请重新输入");
		        	console.log(lbServerDataObjKeyCount);
		        	$(lbGridNowDivSpan).siblings('.lburlDiv2').val('');
		        }
		    });
		}
		return 
	});
	
	//修复必须点击编辑才能看见组件预览的效果
	lbGridId="lb_Ext_grid_Panel_auto"+LbGridDivLength;
	
	
	var gridCode=lbinitialUrlCode;
	if(localStorage.getItem(lbGridId)==null){
		console.log('fristTime clicks');
		localStorage.setItem(lbGridId, gridCode)
	}
	//更新后台bean对象20190610
	console.log('更新后台bean对象');
	if(bean!=null){
		console.log('更新后台bean对象	');
		var obj={};
		obj.content=gridCode;
		obj.path=lbGridId;
		bean[0].list.push(obj);
	}
	return LbGridDivLength;
}

//自定义按钮函数
function yourself(e){
	var arr=[];
	arr.push($("#zidingyi").val());
	arr.push($("#zidingyiName").val());
	if($("#zidingyi").val()==''||$("#zidingyiName").val()==''){
		return
	}
	$.ajax({url:"",data:arr,success:function(result){
		Ext.Msg.alert('成功', '保存成功'+result); 
    },error:function(result){
		Ext.Msg.alert('失败', '保存失败'+result); 
    }});
}

function hideAllLittleBox(){
	$('.lbPosBox,.lbColumnBox,.lbConfigComboBoxDelBox,.lbConfigButtonBox').hide();
}













