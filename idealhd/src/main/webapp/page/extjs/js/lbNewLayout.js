var lbNewLayoutArr=[];//控制localStorage.lbJ的数组
function lbDragStop(e,event){ //拖动组件松开时触发
	var timestamp = (new Date()).valueOf();
	var lbObj={};
	var lbOppCode='';
	var lbElement='';
	var lbCurrentDiv='';
	if(e=='按钮'){
		lbElement="<div id='lbEleButton"+timestamp+"'></div>";
		lbOppCode="Ext.create('Ext.Button',{text:'按钮',renderTo:Ext.get('"+$(lbElement).attr('id')+"')});";
	}
	if($(LbTarget).children().length==1){
		$(LbTarget).children('.box-element').attr('lb','lb');
		$(LbTarget).children('.box-element').children('.view').append(lbElement);
		var aa="codeJs"+timestamp+"";
		var bb="codeHtml"+timestamp+"";
		var codeJsObj={[aa]:lbOppCode,[bb]:lbElement};
		lbNewLayoutArr.push(codeJsObj);
		localStorage.setItem("lbJ",JSON.stringify(lbNewLayoutArr));
		eval(lbOppCode);
	}else{
		if($(event.target).attr('class')=='box box-element ui-draggable'){//如果拖动的是组件  不是布局
			for(var i=0;i<$(LbTarget).children().length;i++){
				if($(LbTarget).children().eq(i).attr('class')=='box box-element ui-draggable'){
					if(!$(LbTarget).children().eq(i).attr('lb')){
						$(LbTarget).children().eq(i).remove();
					}
				}
			}
		}
	}
	console.log(lbNewLayoutArr);
}
function lbDragStop1(e){

	if($(LbTarget).parent().parent('.view').parent('.lblyrowUi-draggable').length==1){
		return 
	}
	if($(LbTarget).children().length==1){
		$(LbTarget).children('.lyrow.ui-draggable').attr('lb','lb');
	}
	if($(e.target).attr('class')=='lyrow ui-draggable'){
		if($(LbTarget).children().length>1){
			for(var i=0;i<$(LbTarget).children().length;i++){
				if($(LbTarget).children().eq(i).attr('class')=='lyrow ui-draggable'){
					if(!$(LbTarget).children().eq(i).attr('lb')){
						$(LbTarget).children().eq(i).remove();
					}
				}
			}
		}
	}
}
function lbDeleteCompopent(e){ //点击删除时时触发
	console.log(e);
	var copylbNewLayoutArr=lbNewLayoutArr;
	if($(e).is('a')){
		var lbEle=$(e).parent('div').find($('div[id^="lbEle"]'));
		for(var i=0;i<lbEle.length;i++){
			var deleteId=lbEle.eq(i).attr('id');
			for(var u=0;u<lbNewLayoutArr.length;u++){
				for(let key in copylbNewLayoutArr[u]){　
					if(key.slice(-13)==deleteId.slice(-13)){
						copylbNewLayoutArr.splice(u,1);
						localStorage.setItem("lbJ",JSON.stringify(copylbNewLayoutArr));//重新设置localStorage.lbJ
						break;
					}
				}
			}
		}
		lbNewLayoutArr=copylbNewLayoutArr;
		console.log(lbNewLayoutArr);
	}
}