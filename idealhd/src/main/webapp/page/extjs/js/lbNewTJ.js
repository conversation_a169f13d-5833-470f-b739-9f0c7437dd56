
$(document).ready(function(){
	//套件预览图功能
	$('body').append("<div id='sltDiv' style='position:fixed;top:120px;left:200px;width:800px;z-index:999999;'><img src='' /></div>");
	$('#sltDiv').hide();
	$('.sidebar-nav').find('.preview').mouseenter(function(e){
		//console.log($(e));
		
		$('#sltDiv').children('img').attr('src','page/extjs/img/tt/'+$(e.target).text()+'.jpg');
		$('#sltDiv').show();
	});
	$(document).click(function(){
		$('#sltDiv').hide();
	});
	//套件预览图功能 end
	
});
//新增items组件 函数内容自动加入结束关键字lbfunEnd
function lbInputValTextonblur(e){
	$(e).on("blur",'input',function(e){
		 //console.log($(e.target).val());
		 let str=$(e.target).val();
		 if(str.indexOf('function ')!=-1 && str.indexOf('var lbfunEnd')==-1){
			 str=str.substring(str.length-1,0)+'var lbfunEnd=null;'+str.substring(str.length-1);
			 $(e.target).val(str);
		 }else if(str.indexOf('var ')<3 && str.indexOf('var ')>=0 && str.indexOf('lbLisEnd:')==-1){
			 str=str.substring(str.length-2,0)+'lbLisEnd:function(){},'+str.substring(str.length-2);
			 $(e.target).val(str);
		 }
	});
}
//textarea点击扩大textarea
function lbtextareacon(e){
	console.log(e);
	var text = $(e).val();
	var arry = text.split("\n");
	var bstr='';
	for(let i=0;i<arry.length;i++){
		if(bstr.length<arry[i].length){
			bstr=arry[i];
		}
	}
	$(e).width(bstr.length*9);
	$(e).height(arry.length*22);
}
//缩小textaera
function lbtextareacon1(e){
	$(e).width(200).height(40);
}
//全局返回步骤代码变量
var PNCode='';
//上一步
function lbprev(e){
	var codeId=$(e.target).parents('.configuration').siblings('.view').attr('codeid');
	$(e.target).siblings('div').remove();
	if(PNCode.length>0){
		//console.log('执行lbprev');
		$('#'+codeId).children().remove();
		localStorage.setItem(codeId,PNCode);
		eval(localStorage.getItem(codeId));
		PNCode='';
	}
	
}
//右键隐藏控制面板
$(document).on("contextmenu",function(e){
	if($(e.target).attr('class')=='lbHandleFunction' ||$(e.target).attr('class')=='lbListenersFunction'){return }
	$(".configuration div").remove();
	return false
});
//end
//js代码格式化
function js_beautify(js_source_text, indent_size, indent_character, indent_level)
{

    var input, output, token_text, last_type, last_text, last_word, current_mode, modes, indent_string;
    var whitespace, wordchar, punct, parser_pos, line_starters, in_case;
    var prefix, token_type, do_block_just_closed, var_line, var_line_tainted;



    function trim_output()
    {
        while (output.length && (output[output.length - 1] === ' ' || output[output.length - 1] === indent_string)) {
            output.pop();
        }
    }

    function print_newline(ignore_repeated)
    {
        ignore_repeated = typeof ignore_repeated === 'undefined' ? true: ignore_repeated;
        
        trim_output();

        if (!output.length) {
            return; // no newline on start of file
        }

        if (output[output.length - 1] !== "\n" || !ignore_repeated) {
            output.push("\n");
        }
        for (var i = 0; i < indent_level; i++) {
            output.push(indent_string);
        }
    }



    function print_space()
    {
        var last_output = output.length ? output[output.length - 1] : ' ';
        if (last_output !== ' ' && last_output !== '\n' && last_output !== indent_string) { // prevent occassional duplicate space
            output.push(' ');
        }
    }


    function print_token()
    {
        output.push(token_text);
    }

    function indent()
    {
        indent_level++;
    }


    function unindent()
    {
        if (indent_level) {
            indent_level--;
        }
    }


    function remove_indent()
    {
        if (output.length && output[output.length - 1] === indent_string) {
            output.pop();
        }
    }


    function set_mode(mode)
    {
        modes.push(current_mode);
        current_mode = mode;
    }


    function restore_mode()
    {
        do_block_just_closed = current_mode === 'DO_BLOCK';
        current_mode = modes.pop();
    }


    function in_array(what, arr)
    {
        for (var i = 0; i < arr.length; i++)
        {
            if (arr[i] === what) {
                return true;
            }
        }
        return false;
    }



    function get_next_token()
    {
        var n_newlines = 0;
        var c = '';

        do {
            if (parser_pos >= input.length) {
                return ['', 'TK_EOF'];
            }
            c = input.charAt(parser_pos);

            parser_pos += 1;
            if (c === "\n") {
                n_newlines += 1;
            }
        }
        while (in_array(c, whitespace));

        if (n_newlines > 1) {
            for (var i = 0; i < 2; i++) {
                print_newline(i === 0);
            }
        }
        var wanted_newline = (n_newlines === 1);


        if (in_array(c, wordchar)) {
            if (parser_pos < input.length) {
                while (in_array(input.charAt(parser_pos), wordchar)) {
                    c += input.charAt(parser_pos);
                    parser_pos += 1;
                    if (parser_pos === input.length) {
                        break;
                    }
                }
            }

            // small and surprisingly unugly hack for 1E-10 representation
            if (parser_pos !== input.length && c.match(/^[0-9]+[Ee]$/) && input.charAt(parser_pos) === '-') {
                parser_pos += 1;

                var t = get_next_token(parser_pos);
                c += '-' + t[0];
                return [c, 'TK_WORD'];
            }

            if (c === 'in') { // hack for 'in' operator
                return [c, 'TK_OPERATOR'];
            }
            return [c, 'TK_WORD'];
        }
        
        if (c === '(' || c === '[') {
            return [c, 'TK_START_EXPR'];
        }

        if (c === ')' || c === ']') {
            return [c, 'TK_END_EXPR'];
        }

        if (c === '{') {
            return [c, 'TK_START_BLOCK'];
        }

        if (c === '}') {
            return [c, 'TK_END_BLOCK'];
        }

        if (c === ';') {
            return [c, 'TK_END_COMMAND'];
        }

        if (c === '/') {
            var comment = '';
            // peek for comment /* ... */
            if (input.charAt(parser_pos) === '*') {
                parser_pos += 1;
                if (parser_pos < input.length) {
                    while (! (input.charAt(parser_pos) === '*' && input.charAt(parser_pos + 1) && input.charAt(parser_pos + 1) === '/') && parser_pos < input.length) {
                        comment += input.charAt(parser_pos);
                        parser_pos += 1;
                        if (parser_pos >= input.length) {
                            break;
                        }
                    }
                }
                parser_pos += 2;
                return ['/*' + comment + '*/', 'TK_BLOCK_COMMENT'];
            }
            // peek for comment // ...
            if (input.charAt(parser_pos) === '/') {
                comment = c;
                while (input.charAt(parser_pos) !== "\x0d" && input.charAt(parser_pos) !== "\x0a") {
                    comment += input.charAt(parser_pos);
                    parser_pos += 1;
                    if (parser_pos >= input.length) {
                        break;
                    }
                }
                parser_pos += 1;
                if (wanted_newline) {
                    print_newline();
                }
                return [comment, 'TK_COMMENT'];
            }

        }

        if (c === "'" || // string
        c === '"' || // string
        (c === '/' &&
        ((last_type === 'TK_WORD' && last_text === 'return') || (last_type === 'TK_START_EXPR' || last_type === 'TK_END_BLOCK' || last_type === 'TK_OPERATOR' || last_type === 'TK_EOF' || last_type === 'TK_END_COMMAND')))) { // regexp
            var sep = c;
            var esc = false;
            c = '';

            if (parser_pos < input.length) {

                while (esc || input.charAt(parser_pos) !== sep) {
                    c += input.charAt(parser_pos);
                    if (!esc) {
                        esc = input.charAt(parser_pos) === '\\';
                    } else {
                        esc = false;
                    }
                    parser_pos += 1;
                    if (parser_pos >= input.length) {
                        break;
                    }
                }

            }

            parser_pos += 1;
            if (last_type === 'TK_END_COMMAND') {
                print_newline();
            }
            return [sep + c + sep, 'TK_STRING'];
        }

        if (in_array(c, punct)) {
            while (parser_pos < input.length && in_array(c + input.charAt(parser_pos), punct)) {
                c += input.charAt(parser_pos);
                parser_pos += 1;
                if (parser_pos >= input.length) {
                    break;
                }
            }
            return [c, 'TK_OPERATOR'];
        }

        return [c, 'TK_UNKNOWN'];
    }


    //----------------------------------

    indent_character = indent_character || ' ';
    indent_size = indent_size || 4;

    indent_string = '';
    while (indent_size--) {
        indent_string += indent_character;
    }

    input = js_source_text;

    last_word = ''; // last 'TK_WORD' passed
    last_type = 'TK_START_EXPR'; // last token type
    last_text = ''; // last token text
    output = [];

    do_block_just_closed = false;
    var_line = false;
    var_line_tainted = false;

    whitespace = "\n\r\t ".split('');
    wordchar = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_$'.split('');
    punct = '+ - * / % & ++ -- = += -= *= /= %= == === != !== > < >= <= >> << >>> >>>= >>= <<= && &= | || ! !! , : ? ^ ^= |='.split(' ');

    // words which should always start on new line.
    line_starters = 'continue,try,throw,return,var,if,switch,case,default,for,while,break,function'.split(',');

    // states showing if we are currently in expression (i.e. "if" case) - 'EXPRESSION', or in usual block (like, procedure), 'BLOCK'.
    // some formatting depends on that.
    current_mode = 'BLOCK';
    modes = [current_mode];

    indent_level = indent_level || 0;
    parser_pos = 0; // parser position
    in_case = false; // flag for parser that case/default has been processed, and next colon needs special attention
    while (true) {
        var t = get_next_token(parser_pos);
        token_text = t[0];
        token_type = t[1];
        if (token_type === 'TK_EOF') {
            break;
        }

        switch (token_type) {

        case 'TK_START_EXPR':
            var_line = false;
            set_mode('EXPRESSION');
            if (last_type === 'TK_END_EXPR' || last_type === 'TK_START_EXPR') {
                // do nothing on (( and )( and ][ and ]( ..
            } else if (last_type !== 'TK_WORD' && last_type !== 'TK_OPERATOR') {
                print_space();
            } else if (in_array(last_word, line_starters) && last_word !== 'function') {
                print_space();
            }
            print_token();
            break;

        case 'TK_END_EXPR':
            print_token();
            restore_mode();
            break;

        case 'TK_START_BLOCK':
            
            if (last_word === 'do') {
                set_mode('DO_BLOCK');
            } else {
                set_mode('BLOCK');
            }
            if (last_type !== 'TK_OPERATOR' && last_type !== 'TK_START_EXPR') {
                if (last_type === 'TK_START_BLOCK') {
                    print_newline();
                } else {
                    print_space();
                }
            }
            print_token();
            indent();
            break;

        case 'TK_END_BLOCK':
            if (last_type === 'TK_START_BLOCK') {
                // nothing
                trim_output();
                unindent();
            } else {
                unindent();
                print_newline();
            }
            print_token();
            restore_mode();
            break;

        case 'TK_WORD':

            if (do_block_just_closed) {
                print_space();
                print_token();
                print_space();
                break;
            }

            if (token_text === 'case' || token_text === 'default') {
                if (last_text === ':') {
                    // switch cases following one another
                    remove_indent();
                } else {
                    // case statement starts in the same line where switch
                    unindent();
                    print_newline();
                    indent();
                }
                print_token();
                in_case = true;
                break;
            }


            prefix = 'NONE';
            if (last_type === 'TK_END_BLOCK') {
                if (!in_array(token_text.toLowerCase(), ['else', 'catch', 'finally'])) {
                    prefix = 'NEWLINE';
                } else {
                    prefix = 'SPACE';
                    print_space();
                }
            } else if (last_type === 'TK_END_COMMAND' && (current_mode === 'BLOCK' || current_mode === 'DO_BLOCK')) {
                prefix = 'NEWLINE';
            } else if (last_type === 'TK_END_COMMAND' && current_mode === 'EXPRESSION') {
                prefix = 'SPACE';
            } else if (last_type === 'TK_WORD') {
                prefix = 'SPACE';
            } else if (last_type === 'TK_START_BLOCK') {
                prefix = 'NEWLINE';
            } else if (last_type === 'TK_END_EXPR') {
                print_space();
                prefix = 'NEWLINE';
            }

            if (last_type !== 'TK_END_BLOCK' && in_array(token_text.toLowerCase(), ['else', 'catch', 'finally'])) {
                print_newline();
            } else if (in_array(token_text, line_starters) || prefix === 'NEWLINE') {
                if (last_text === 'else') {
                    // no need to force newline on else break
                    print_space();
                } else if ((last_type === 'TK_START_EXPR' || last_text === '=') && token_text === 'function') {
                    // no need to force newline on 'function': (function
                    // DONOTHING
                } else if (last_type === 'TK_WORD' && (last_text === 'return' || last_text === 'throw')) {
                    // no newline between 'return nnn'
                    print_space();
                } else if (last_type !== 'TK_END_EXPR') {
                    if ((last_type !== 'TK_START_EXPR' || token_text !== 'var') && last_text !== ':') {
                        // no need to force newline on 'var': for (var x = 0...)
                        if (token_text === 'if' && last_type === 'TK_WORD' && last_word === 'else') {
                            // no newline for } else if {
                            print_space();
                        } else {
                            print_newline();
                        }
                    }
                } else {
                    if (in_array(token_text, line_starters) && last_text !== ')') {
                        print_newline();
                    }
                }
            } else if (prefix === 'SPACE') {
                print_space();
            }
            print_token();
            last_word = token_text;

            if (token_text === 'var') {
                var_line = true;
                var_line_tainted = false;
            }

            break;

        case 'TK_END_COMMAND':

            print_token();
            var_line = false;
            break;

        case 'TK_STRING':

            if (last_type === 'TK_START_BLOCK' || last_type === 'TK_END_BLOCK') {
                print_newline();
            } else if (last_type === 'TK_WORD') {
                print_space();
            }
            print_token();
            break;

        case 'TK_OPERATOR':

            var start_delim = true;
            var end_delim = true;
            if (var_line && token_text !== ',') {
                var_line_tainted = true;
                if (token_text === ':') {
                    var_line = false;
                }
            }

            if (token_text === ':' && in_case) {
                print_token(); // colon really asks for separate treatment
                print_newline();
                break;
            }

            in_case = false;

            if (token_text === ',') {
                if (var_line) {
                    if (var_line_tainted) {
                        print_token();
                        print_newline();
                        var_line_tainted = false;
                    } else {
                        print_token();
                        print_space();
                    }
                } else if (last_type === 'TK_END_BLOCK') {
                    print_token();
                    print_newline();
                } else {
                    if (current_mode === 'BLOCK') {
                        print_token();
                        print_newline();
                    } else {
                        // EXPR od DO_BLOCK
                        print_token();
                        print_space();
                    }
                }
                break;
            } else if (token_text === '--' || token_text === '++') { // unary operators special case
                if (last_text === ';') {
                    // space for (;; ++i)
                    start_delim = true;
                    end_delim = false;
                } else {
                    start_delim = false;
                    end_delim = false;
                }
            } else if (token_text === '!' && last_type === 'TK_START_EXPR') {
                // special case handling: if (!a)
                start_delim = false;
                end_delim = false;
            } else if (last_type === 'TK_OPERATOR') {
                start_delim = false;
                end_delim = false;
            } else if (last_type === 'TK_END_EXPR') {
                start_delim = true;
                end_delim = true;
            } else if (token_text === '.') {
                // decimal digits or object.property
                start_delim = false;
                end_delim = false;

            } else if (token_text === ':') {
                // zz: xx
                // can't differentiate ternary op, so for now it's a ? b: c; without space before colon
                if (last_text.match(/^\d+$/)) {
                    // a little help for ternary a ? 1 : 0;
                    start_delim = true;
                } else {
                    start_delim = false;
                }
            }
            if (start_delim) {
                print_space();
            }

            print_token();

            if (end_delim) {
                print_space();
            }
            break;

        case 'TK_BLOCK_COMMENT':

            print_newline();
            print_token();
            print_newline();
            break;

        case 'TK_COMMENT':

            // print_newline();
            print_space();
            print_token();
            print_newline();
            break;

        case 'TK_UNKNOWN':
            print_token();
            break;
        }

        last_type = token_type;
        last_text = token_text;
    }

    return output.join('');

}
//end
//store|model面板
function lbSM(e){
	$(e.target).siblings('div').remove();
	var codeId=$(e.target).parent().siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
	mainCode = mainCode.replace(/[\r\n]/g,"");
	mainCode = mainCode.replace(/[\n]/g,"");
	mainCode = mainCode.replace(/,\s*},\s*];/g , ",},];");
	mainCode = mainCode.replace(/,\s*},\s*{/g , ",},{");
	mainCode = mainCode.replace(/,\s*];/g,",];");
	
	//console.log('lbNewTJ.js执行代码压缩');
	$('#'+codeId).children().remove();
	localStorage.setItem(codeId,mainCode);
	eval(localStorage.getItem(codeId));
	
	if($('#lbSMConfigBox').length>0){
		$('#lbSMConfigBox').remove();
	}else{
		var d1="<div id='lbSMConfigBox'></div>";
		$(e.target).parent('.configuration').append(d1);
		$('#lbSMConfigBox').show();	
		var v1=0;var vv1=0;
		var lbo=0;
		while(v1!=-1 || vv1!=-1){
			var div=$("<div class='lbstoreD' style='display: flex;align-items: flex-start;margin-bottom:20px;    border-left: 6px solid red;'></div>");
			
			var lbItemsName='var lb'+lbo+'_store';
			v1=mainCode.indexOf(lbItemsName,0);
			if(v1!=-1){
				$('#lbSMConfigBox').append(div);
				var pre=$("<pre contenteditable='true'class='lbStorePre' style='margin:0;display:inline-block;'></pre>");
				var v2=mainCode.indexOf('});',v1);
				var v3=mainCode.substring(v1,v2+3);
				
				var js_source = v3;
		        var tabsize = 4; //缩进大小
		        var tabchar = ' ';
		        if (tabsize == 1) {
		            tabchar = '\t';
		        }
				$(pre).text(js_beautify(js_source, tabsize, tabchar));	
				div.append(pre);
			}
			var lbItemsName='var lb'+lbo+'_model';
			vv1=mainCode.indexOf(lbItemsName,0);
			if(vv1!=-1){
				$('#lbSMConfigBox').append(div);
				var pre=$("<pre contenteditable='true'class='lbStorePre' style='margin:0;display:inline-block;'></pre>");
				var v2=mainCode.indexOf('});',vv1);
				var v3=mainCode.substring(vv1,v2+3);
				
				var js_source = v3;
		        var tabsize = 4; //缩进大小	
		        var tabchar = ' ';
		        if (tabsize == 1) {
		            tabchar = '\t';
		        }
				$(pre).text(js_beautify(js_source, tabsize, tabchar));	
				div.append(pre);
			}
			lbo++;
		}
		$('#lbSMConfigBox').append('<div style="width:100px;margin:20px 0px;color:#fff;text-align:center;font-size:18px;line-height:28px;cursor:pointer;height:28px;border-radius:16px;background:orange;"class="lb_StoreSubmit" >提交</div>');
		
	}
}
//sotre面板提交函数
function lb_StoreSubmit(e){
	var codeId=$(e.target).parents('.configuration').siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
	PNCode=mainCode;
	for(var i=0;i<$(e.target).siblings('.lbstoreD').length;i++){
		for(var j=0;j<$('.lbstoreD').eq(i).children('pre').length;j++){
			var varlbStore=$('.lbstoreD').eq(i).children('pre').eq(j).text();
			var v1=mainCode.indexOf('var lb'+i+'_store',0);
			if(v1==-1){continue}
			var v2=mainCode.indexOf('});',v1)+3;
			var v3=mainCode.substring(0,v1);
			var v4=mainCode.substring(v2);
			mainCode=v3+varlbStore+v4;
		}
		for(var j=0;j<$('.lbstoreD').eq(i).children('pre').length;j++){
			var varlbStore=$('.lbstoreD').eq(i).children('pre').eq(j).text();
			var v1=mainCode.indexOf('var lb'+i+'_model',0);
			if(v1==-1){continue}
			var v2=mainCode.indexOf('});',v1)+3;
			var v3=mainCode.substring(0,v1);
			var v4=mainCode.substring(v2);
			mainCode=v3+varlbStore+v4;
		}
	}
	mainCode = mainCode.replace(/[\r\n]/g,"");
	mainCode = mainCode.replace(/[\n]/g,"");
	mainCode = mainCode.replace(/,\s*},\s*];/g , ",},];");
	mainCode = mainCode.replace(/,\s*},\s*{/g , ",},{");
	mainCode = mainCode.replace(/,\s*];/g,",];");
	mainCode = mainCode.replace(/}\s*\)\s*;/g,"});");

	
	$('#'+codeId).children().remove();
	localStorage.setItem(codeId,mainCode);
	eval(localStorage.getItem(codeId));
	$('#lbSMConfigBox').remove();
}


//按钮控制面板
//按钮变量命名规则 var lb0_buttons   var lb1_buttons   ..........
//按钮对象的lbbuttonte和lbbuttonend属性不可修改，删除。
//items中的变量所包含的对象不用用new操作符创建 必须用 ext.create
//只有使用Ext.create(创建的对象才能被控制面板解析
//对象值中不能有英文冒号类似format: 'Y-m-d H:i:s', 会解析失败 但中文冒号可以
//不能有重名变量 不同作用域也不行
function lbItemsConfig(e){
	$(e.target).siblings('div').remove();
	var codeId=$(e.target).parent().siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
	mainCode = mainCode.replace(/[\r\n]/g,"");
	mainCode = mainCode.replace(/[\n]/g,"");
	mainCode = mainCode.replace(/,\s*},\s*];/g , ",},];");
	mainCode = mainCode.replace(/,\s*},\s*{/g , ",},{");
	mainCode = mainCode.replace(/,\s*];/g,",];");
	
	//console.log('lbNewTJ.js执行代码压缩');
	$('#'+codeId).children().remove();
	localStorage.setItem(codeId,mainCode);
	eval(localStorage.getItem(codeId));
	
	var lbo=0;
	var lbItemsName='var lb'+lbo+'_items';
	var v1=mainCode.indexOf(lbItemsName,0);
	//console.log(v1);
	if(v1==-1){return}
	if($('#lbItemsConfigBox').length>0){
		$('#lbItemsConfigBox').remove();
	}else{
		var d1="<div id='lbItemsConfigBox'></div>";
		$(e.target).parent('.configuration').append(d1);
		$('#lbItemsConfigBox').show();	
		
		while(v1!=-1){
			
			var v2=mainCode.indexOf('];',v1);
			if(v2==-1){
				//console.log(v2);
				return
			}
			//console.log(v2);
			var lbItemsCode=mainCode.substring(v1,v2+3);
			//console.log(lbItemsCode);
			var whichVar=lbItemsCode.indexOf('=',0);
			whichVar=lbItemsCode.substring(0,whichVar).trimStart().trimEnd();
			//console.log(whichVar);
			$('#lbItemsConfigBox').append('<div lbVar="'+whichVar+'"class="lbItemsConfigBox_s"style="display: flex;align-items: flex-start;margin-bottom:20px;"id="lbItemsConfigBox_s'+lbo+'"></div>')
			var d1name="#lbItemsConfigBox_s"+lbo+"";
			$(d1name).append('<div class="lbItemsAddObj" style="border-radius:6px;cursor:pointer;background:#9fdaec;line-height:25px;min-width:40px;height:24px;text-align:center;color:#02151c;">添加</div>');

			var v11=lbItemsCode.indexOf('[',0)+1;
			var v22=lbItemsCode.indexOf('];',v11)+1;
			if(v22==-1){ return}
			var lbItemsCodeArr=lbItemsCode.substring(v11,v22);
			var lbItemsCodeArr=lbItemsCodeArr.split(',');
			var lbItemsCodeArrLength=lbItemsCodeArr.length-1;
			//console.log(lbItemsCodeArrLength);
			for(var i=0;i<lbItemsCodeArrLength;i++){//循环有几个组件方块	
				if(lbItemsCodeArr[i].indexOf('->')>0){
					var lbItemsCodeArrVarName=lbItemsCodeArr[i].trimStart().trimEnd();
					lbItemsCodeArrVarName="var "+lbItemsCodeArrVarName;
					//console.log(lbItemsCodeArrVarName);
					
					var d1=mainCode.indexOf(lbItemsCodeArrVarName,0);
					var d2=mainCode.indexOf('});',d1)+3;
					var lbItemsCodeArrVarNameCode=mainCode.substring(d1,d2);
					//console.log(lbItemsCodeArrVarNameCode);
					var d1div="<div class='bj lbItemsObj' style='padding:8px;display:inline-block;font-size:22px;color:#fff'>-></div>";
					$(d1name).append(d1div);
					continue
				}

				var lbItemsCodeArrVarName=lbItemsCodeArr[i].trimStart().trimEnd();
				lbItemsCodeArrVarName="var "+lbItemsCodeArrVarName;
				//console.log(lbItemsCodeArrVarName);
				
				var d1=mainCode.indexOf(lbItemsCodeArrVarName,0);
				var xxxcode=mainCode.indexOf(':',d1);
				xxxcode=mainCode.substring(d1,xxxcode);
				var d2=mainCode.indexOf('});',d1)+3;
				var lbItemsCodeArrVarNameCode=mainCode.substring(d1,d2);
				if(xxxcode.indexOf('Ext.create(')==-1){
					var d2=mainCode.indexOf('};',d1)+2;
					var lbItemsCodeArrVarNameCode=mainCode.substring(d1,d2);
				}
				//console.log(lbItemsCodeArrVarNameCode);
				var d1div="<div lbVar='"+lbItemsCodeArrVarName+"' class='lbItemsObj lbItemsObj"+i+"'style='padding:8px;display:inline-block;'></div>";
				$(d1name).append(d1div);
				
				var d11=lbItemsCodeArrVarNameCode.indexOf('Ext.create(',0)+11;
				var d22=lbItemsCodeArrVarNameCode.indexOf(',',d11);
				var code1=lbItemsCodeArrVarNameCode.substring(d11,d22);
				if(xxxcode.indexOf('Ext.create(')==-1){
					var code1='普通对象';
				}
				//console.log(code1);
				var ele=".lbItemsObj"+(i);
				var lbInput="<input style='border-radius:4px;line-height:0px !important;' disabled='true' id='lbItems"+lbo+"Input"+(i).toString()+"'value=\""+code1+"\"/>";
				$(d1name).children(ele).append('<div class="lbItemsDel" style="background:#9fdaec;width:40px;border-radius: 4px;height:18px;cursor:pointer;text-align:center;color:#1b3c66;">删除</div>');

				$(d1name).children(ele).append("<div style='color:white;display: flex;flex-direction: row;justify-content: space-between;'><span style='min-width:10px'>组件名称</span>"+lbInput+"</div>");
				//input自适应宽度
				let sensor = $('<span class="lbSpanWid"></span>').css({display: 'none'}); 
				$('body').append(sensor); 
			    sensor.html(code1);
			    let inputWidth = 100;	//sensor.width()+10;//开启 关闭 input自适应宽度
				$("#lbItems"+lbo+"Input"+(i).toString()).width(inputWidth);
				sensor.remove();
				//input自适应宽度 end
				//console.log(lbItemsCodeArrVarNameCode);
				//console.log(lbItemsCodeArrVarNameCode.indexOf('Ext.create(',0));
				if(lbItemsCodeArrVarNameCode.indexOf('Ext.create(',0)==-1){
					
				}
				var dd11=lbItemsCodeArrVarNameCode.indexOf('Ext.create(',0)+11;
				dd11=lbItemsCodeArrVarNameCode.indexOf(',',dd11);
				dd11=lbItemsCodeArrVarNameCode.indexOf('{',dd11)+1;
				var dd22=lbItemsCodeArrVarNameCode.indexOf('});',dd11);
				
				if(xxxcode.indexOf('Ext.create(')==-1){
					var dd11=lbItemsCodeArrVarNameCode.indexOf('{',0)+1;
					var dd22=lbItemsCodeArrVarNameCode.indexOf('};',dd11);
				}
				var code11=lbItemsCodeArrVarNameCode.substring(dd11,dd22);
				//console.log(code11);
				var codeArr=code11.split(',');
				//console.log(codeArr);
				//console.log(lbItemsCodeArrVarNameCode);
				for(var j=0;j<(codeArr.length-1);j++){
					//console.log(codeArr[j]);
					var KV=codeArr[j].split(':');
					//console.log(KV);
					if(KV!=''){
						var key=KV[0].trimStart().trimEnd();
						var value=KV[1].trimStart().trimEnd();
						
						var ele=".lbItemsObj"+(i);
						var lbInput="<input style='border-radius:4px;line-height:0px !important;' id='lbItems"+lbo+"Input"+(i).toString()+j+"'value=\""+value+"\"/>";
						
						if(key=='handler'){//handler函数检测
							var stt1=mainCode.indexOf('function '+value+'(',0);
							var stt2=mainCode.indexOf('var lbfunEnd',stt1);
							stt2=mainCode.indexOf('}',stt2)+1;
							var stt3=mainCode.substring(stt1,stt2);
							
							var js_source = stt3;
					        var tabsize = 4; //缩进大小
					        var tabchar = ' ';
					        if (tabsize == 1) {
					            tabchar = '\t';
					        }
					        stt3=js_beautify(js_source, tabsize, tabchar);	
							lbInput="<textarea class='lbHandleFunction' oncontextmenu='lbtextareacon1(this)'onclick='lbtextareacon(this)'id=lb"+value+">"+stt3+"</textarea>";
							
						}
						
						if(key=='listeners'){//listeners函数检测
							var stt1=mainCode.indexOf('var '+value,0);
							var stt2=mainCode.indexOf('lbLisEnd:',stt1);
							stt2=mainCode.indexOf('};',stt2)+2;
							var stt3=mainCode.substring(stt1,stt2);
							
							var js_source = stt3;
					        var tabsize = 4; //缩进大小
					        var tabchar = ' ';
					        if (tabsize == 1) {
					            tabchar = '\t';
					        }
					        stt3=js_beautify(js_source, tabsize, tabchar);	
							lbInput="<textarea class='lbListenersFunction' oncontextmenu='lbtextareacon1(this)'onclick='lbtextareacon(this)'id=lb"+value+">"+stt3+"</textarea>";
							
						}
						
						$(d1name).children(ele).append("<div style='color:white;display: flex;flex-direction: row;justify-content: space-between;'><span style='min-width:10px'>"+key+"</span>"+lbInput+"</div>");
						if(key!='handler' && key!='listeners'){
							//input自适应宽度
							let sensor = $('<span class="lbSpanWid"></span>').css({display: 'none'}); 
							$('body').append(sensor); 
							//console.log(value);
						    sensor.html(value);
						    //console.log(sensor.width());
						    let inputWidth = 100;	//sensor.width()+10;//开启 关闭 input自适应宽度
							//console.log(inputWidth);
							//console.log($("#lbItems"+lbo+"Input"+(i).toString()+j)[0]);
							$("#lbItems"+lbo+"Input"+(i).toString()+j)[0].style.width=inputWidth+'px';
							sensor.remove();
						}
						
					}
				}
			}
			lbo++;
			lbItemsName='var lb'+lbo+'_items';
			v1=mainCode.indexOf(lbItemsName,0);
		}
		$('#lbItemsConfigBox').append('<div style="width:100px;margin:20px 0px;color:#fff;text-align:center;font-size:18px;line-height:28px;cursor:pointer;height:28px;border-radius:16px;background:orange;"class="lb_ItemsSubmit" >提交</div>');
		lbInputAutoWidth();
	}
}
//items拖拽换位
function lbItemsSortable(e){
	$('.lbItemsConfigBox_s').sortable({
		revert: true,
		cancel:'input,.lbItemsAddObj,.tt2,.lbHandleFunction,.lbListenersFunction',
		stop: function( event, ui ) {
			var codeId=$(e.target).parent().siblings('.view').attr('codeid');
			var mainCode=localStorage.getItem(codeId);
			
			var whichVar=$(event.target).attr('lbvar');
			var arr=[];
			for(var i=0;i<$(event.target).children('.lbItemsObj').length;i++){
				if($(event.target).children('.lbItemsObj').eq(i).attr('lbvar')){
					var str=$(event.target).children('.lbItemsObj').eq(i).attr('lbvar');
					arr.push(str=str.replace("var ",""));
					
				}else{
					arr.push("'->'");
				}
			}
			//console.log(arr);
			var v1=mainCode.indexOf(whichVar,0);     
			var v2=mainCode.indexOf('];',v1)+2;
			var code1=mainCode.substring(0,v1);
			var code2=mainCode.substring(v2);
			var r1=whichVar+'=[';
			for(var i=0;i<arr.length;i++){
				r1=r1+arr[i]+',';
			}
			r1=r1+'];'
			//console.log(code1);
			//console.log(r1);
			//console.log(code2);
			mainCode=code1+r1+code2;
			$('#'+codeId).children().remove();
			localStorage.setItem(codeId,mainCode);
			eval(localStorage.getItem(codeId));
		}
	});
}
//items删除
function lbItemsDel(e){
	if($(e.target).parent('.lbItemsObj').attr('lbItemsDel')==1){
		$(e.target).css('background','#9fdaec').parent('.lbItemsObj').css('border','1px solid white').css('box-shadow','0px 0px 5px white').attr('lbItemsDel','0');
	}else{
		$(e.target).css('background','red').parent('.lbItemsObj').css('border','1px solid red').css('box-shadow','0px 0px 5px red').attr('lbItemsDel','1');
	
	}
}
//items提交执行方法
function gridItemsConfigSubmit(e){
	$('.lbSpanWid').remove();
	var codeId=$(e.target).parents('.configuration').siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
	PNCode=mainCode;
	var lbo=0;
	var finalCode='';
	var lbColumnName='var lb'+lbo+'_items';
	
		for(var i=0;i<$('#lbItemsConfigBox').children('.lbItemsConfigBox_s').length;i++,lbo++){
			lbColumnName='var lb'+lbo+'_items';
			var div1=$('#lbItemsConfigBox').children('.lbItemsConfigBox_s').eq(i);
			for(var ii=0;ii<div1.children('.lbItemsObj').length;ii++){
				var div2=div1.children('.lbItemsObj').eq(ii);
				for(var iii=0;iii<div2.children('div').length;iii++){
					var div3=div2.children('div').eq(iii);
					//if(div3.children('input').attr('disabled')=='disabled'){continue}
					var step1=div3.parent('div').parent('div').attr('lbvar').trimStart().trimEnd();
					var step2=div3.parent('div').attr('lbvar').trimStart().trimEnd();
					
					var step3=div3.children('span').text();
					
					
					
					var step4='';
					if(div3.children('input').length>0){
						step4=div3.children('input').val();
					}else if(div3.children('textarea').length>0 && div3.children('textarea').attr('class')=='lbHandleFunction'){
						
						var funName=div3.children('textarea').attr('id').substring(2);
						var f1=mainCode.indexOf('function '+funName,0);
						var f2=mainCode.indexOf('var lbfunEnd',f1);
						f2=mainCode.indexOf('}',f2)+1;
						var f3=mainCode.substring(f2);
						mainCode=(mainCode.substring(0,f1))+f3;
						//console.log(mainCode);
						
						
						var funCode=div3.children('textarea').val();
						mainCode=(mainCode.substring(f1,0))+funCode+f3;
						step4=funName;
					}else if(div3.children('textarea').length>0 && div3.children('textarea').attr('class')=='lbListenersFunction'){
						var funName=div3.children('textarea').attr('id').substring(2);
						var f1=mainCode.indexOf('var '+funName,0);
						var f2=mainCode.indexOf('lbLisEnd:',f1);
						f2=mainCode.indexOf('};',f2)+2;
						var f3=mainCode.substring(f2);
						mainCode=(mainCode.substring(0,f1))+f3;
						//console.log(mainCode);
						
						
						var funCode=div3.children('textarea').val();
						mainCode=(mainCode.substring(f1,0))+funCode+f3;
						step4=funName;
					}
					
					
					
					
					//console.log(step3);
					//console.log(step4);
					if(step4==undefined||step3=='组件名称'||step3==undefined||step3==''){continue}
					//console.log(step2);
					var v1=mainCode.indexOf(step2,0);
					var v2=mainCode.indexOf('{',v1)+1;
					var v3=mainCode.indexOf(step3,v2);
					var v4=mainCode.indexOf(':',v3)+1;
					var code1=mainCode.substring(0,v4);
					var v5=mainCode.indexOf(',',v4);
					var code2=mainCode.substring(v5);
					mainCode=code1+step4+code2;
				}
			}
		}
		
		//解析删除obj
		for(var i=0;i<$('#lbItemsConfigBox').children('.lbItemsConfigBox_s').length;i++,lbo++){
			lbColumnName='var lb'+lbo+'_items';
			var div1=$('#lbItemsConfigBox').children('.lbItemsConfigBox_s').eq(i);
			for(var ii=0;ii<div1.children('.lbItemsObj').length;ii++){
				var div2=div1.children('.lbItemsObj').eq(ii);
				if(div1.children('.lbItemsObj').eq(ii).attr('lbitemsdel')==1){
					var lbWhichVar=div1.children('.lbItemsObj').eq(ii).attr('lbvar');
					var v1=mainCode.indexOf(lbWhichVar,0);
					var v2=mainCode.indexOf('});',v1)+3;
					var code1=mainCode.substring(0,v1);
					var code2=mainCode.substring(v2);	
					//console.log(code1);
					//console.log(code2);
					mainCode=code1+code2;
					
					var lbWhichVar1=div1.children('.lbItemsObj').eq(ii).parent('.lbItemsConfigBox_s').attr('lbvar');
					var v1=mainCode.indexOf(lbWhichVar1,0);
					lbWhichVar=lbWhichVar.replace("var ","");
					var v2=mainCode.indexOf(lbWhichVar,v1);
					var v3=mainCode.indexOf(',',v2)+1;
					var code1=mainCode.substring(0,v2);
					var code2=mainCode.substring(v3);	
					//console.log(code1);
					//console.log(code2);
					mainCode=code1+code2;
					
				}
			}
		}
		//items添加
		for(var i=0;i<$('#lbItemsConfigBox').find('.tt2').length;i++){
			var tt2=$('#lbItemsConfigBox').find('.tt2').eq(i);
			var whichvar=tt2.parent('.lbItemsConfigBox_s').attr('lbvar');
			var tt2Val=tt2.attr('lbvar');
			var tt2ObjKV='';
			var k,v='';
			var lb20200825FunContent='';
			for(var ii=0;ii<tt2.find('input').length;ii++){
				if(tt2.find('input').eq(ii).val().length>0){
					if(ii>1){
						if(ii%2 ==0){
							k=tt2.find('input').eq(ii).val();
							tt2ObjKV+=tt2.find('input').eq(ii).val()+':';
						}else{
							v=tt2.find('input').eq(ii).val();
							
							
							if(v.indexOf('function ')>=0 && v.indexOf('function ')<10){
								console.log(1);
								lb20200825FunContent=tt2.find('input').eq(ii).val();
								let str=(tt2.find('input').eq(ii).val());
								let s1=str.indexOf('function ',0)+9;
								let s2=str.indexOf('(',s1);
								str=str.substring(s1,s2);
								tt2ObjKV+=str+',';
							}else if(v.indexOf('var ')>=0 && v.indexOf('var ')<10){console.log(2);
								lb20200825FunContent=tt2.find('input').eq(ii).val();
								let str=(tt2.find('input').eq(ii).val());
								let s1=str.indexOf('var ',0)+4;
								let s2=str.indexOf('=',s1);
								str=str.substring(s1,s2);
								console.log(str);
								tt2ObjKV+=str+',';
							}else{console.log(3);
								tt2ObjKV+=tt2.find('input').eq(ii).val()+',';

							}
							
						}
					}
				}
			}
			console.log(tt2ObjKV);
			var a1=tt2.find('input').eq(0).val();
			var a2=tt2.find('input').eq(1).val();
			var objCode1='var '+tt2Val+'=Ext.create('+a2+',';
			var objCode2='{'+tt2ObjKV+'});';
			var newObjCode=objCode1+objCode2;
			//console.log(newObjCode);
			
			var s1=mainCode.indexOf(whichvar,0);
			s1=mainCode.indexOf('[',s1)+1;
			var s2=mainCode.substring(0,s1);
			var s3=mainCode.substring(s1);
			mainCode=s2+tt2Val+','+s3;//console.log(mainCode);
			
			var s5=mainCode.indexOf(whichvar,0);
			var s6=mainCode.substring(0,s5);
			var s7=mainCode.substring(s5);
			var s8=s6+newObjCode+s7;
			mainCode=s8;
			mainCode=lb20200825FunContent+mainCode;
			
			
			
			
		}
		
		
		
		$('#'+codeId).children().remove();
		localStorage.setItem(codeId,mainCode);
		eval(localStorage.getItem(codeId));
	
	
	   
	
	
	
	//关闭控制面板
	if($('#lbItemsConfigBox').length>0){
		$('#lbItemsConfigBox').remove();
	}
}

//items添加执行方法
function lbItemsAddObj(e){
	$('.lbSpanWid').remove();
	//创建框体
	if($(e.target).siblings('div').is('.tt2')){
		$(e.target).siblings('.tt2').remove();
		$(e.target).css('background','#9fdaec');
	}else{
		var lbVar='T'+Date.parse(new Date());
		var tNAme='<div style="color:white;display: flex;flex-direction: row;justify-content: space-between;"><input style="border-radius: 4px; line-height: 0px !important; width: 63px;" value="组件名称" disabled="disabled"><input style="border-radius: 4px; line-height: 0px !important; width: 132px;"></div>';
		var div="<div class='tt2'lbVar='"+lbVar+"' style='border-radius: 6px; padding: 8px; border: 1px solid #00ff00; box-shadow: 0px 0px 5px #00ff00;display: inline-block;'>"+tNAme+"</div>"
		$(e.target).after(div);
		$(e.target).css('background','#00ff00');
	}
	//添加input输入框
	var dd='<div style="color:white;display: flex;flex-direction: row;justify-content: space-between;"><input key="key"style="border-radius: 4px; line-height: 0px !important; width: 82px;"><input vval="vval" style="border-radius: 4px; line-height: 0px !important; width: 132px;"></div>';
	$(e.target).siblings('.tt2').append(dd);
	
	$(e.target).siblings('.tt2').on("input propertychange","input",function(asd){
		//console.log(asd.target);
		if($('.tt2 input').eq($('.tt2 input').length-1).val()!=''){
			$(e.target).siblings('.tt2').append(dd);
		}else{
			if($(asd.target).attr('key')){
				return
			}
			if($(e.target).siblings('.tt2').children('div').eq(-2).find('input').eq(0).val().length==0&&$(e.target).siblings('.tt2').children('div').eq(-2).find('input').eq(1).val().length==0){
				
				$(e.target).siblings('.tt2').children('div:last').remove();
				
				
			}
			
		}
		
	});
	lbInputValTextonblur($('.tt2'));
}
//end

//表格修改column列头功能
//修改的代码中的column对象变量名称必须为lbColumnName变量值
//使用条件：column必须有lbttee(n):(n)属性 且此属性必须是小对象中的第一个属性    column变量名称必须为lb_columns 
//如有多个column 则每个column对象按照如此顺序命名 lb0_columns  lb1_columns lb2_columns 以此类推
//column中每个小对象中的键值对结束必须加逗号 最后一个小对象结束不用加逗号 
//当column小对象中包含函数或复杂json写法的属性值时 会导致解析冲突
//如果在同一个column小对象中有重复的关键字 那程序默认只会检索到前面的关键字进行修改 
//column中的最后一个小对象结束位置也必须包含逗号 并且 小对象中如果包含   }, 关键字会导致冲突

function gridColumnConfig(e){//console.log('gridColumnConfig');
	
	$(e.target).siblings('div').remove();
	var codeId=$(e.target).parent().siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
	mainCode = mainCode.replace(/[\r\n]/g,"");
	mainCode = mainCode.replace(/[\n]/g,"");
	mainCode = mainCode.replace(/,\s*},\s*];/g , ",},];");
	mainCode = mainCode.replace(/,\s*},\s*{/g , ",},{");
	//console.log('lbNewTJ.js执行代码压缩');
	$('#'+codeId).children().remove();
	localStorage.setItem(codeId,mainCode);
	eval(localStorage.getItem(codeId));
	
	var lbo=0;
	var lbColumnName='var lb'+lbo+'_columns';
	var v1=mainCode.indexOf(lbColumnName,0);
	//console.log(v1);
	if(v1==-1){return}
	if($('#lbGridColumnConfigBox').length>0){
		$('#lbGridColumnConfigBox').remove();
	}else{
		var d1="<div id='lbGridColumnConfigBox'></div>";
		$(e.target).parent('.configuration').append(d1);
		$('#lbGridColumnConfigBox').show();	
		//$('#lbGridColumnConfigBox').overscroll();
		while(v1!=-1){
			lbo++;
			var v2=mainCode.indexOf(',},];',v1);
			if(v2==-1){
				//console.log(v2);
				return
			}
			//console.log(v2);
			var lbColumnCode=mainCode.substring(v1,v2+5);
			var v11=lbColumnCode.indexOf('[',0);
			var v22=lbColumnCode.indexOf(',},];',v11);
			if(v22==-1){
				v22=mainCode.indexOf('},\n];',v11);
				
			}
			var lbColumnCodeObj=lbColumnCode.substring(v11+2,v22);//console.log(lbColumnCodeObj);
			var lbColumnCodeObjLength=lbColumnCodeObj.split('lbttee').length-1;//console.log(lbColumnCodeObj.split('lbttee'));
			
			var whichVar=lbColumnCode.indexOf('=',0);
			whichVar=lbColumnCode.substring(0,whichVar);
			
			$('#lbGridColumnConfigBox').append('<div lbVar="'+whichVar+'"class="lbGridColumnConfigBox_s"style="display: flex;align-items: flex-start;margin-bottom:20px;"id="lbGridColumnConfigBox_s'+lbo+'"></div>')
			var asd="#lbGridColumnConfigBox_s"+lbo;
			$(asd).append('<div class="lbAddObj" style="border-radius:6px;cursor:pointer;background:#9fdaec;line-height:25px;min-width:40px;height:24px;text-align:center;color:#02151c;">添加</div>');
			for(var i=0;i<lbColumnCodeObjLength;i++){
				
				//console.log(parseInt(lbColumnCodeObj.split('lbttee')[i+1]));
				var whichSmallObj='lbttee'+parseInt(lbColumnCodeObj.split('lbttee')[i+1]);
				//console.log(lbColumnCodeObj.split('lbttee')[i+1].substring(0,tekken));
				
				var d1="<div lbVar='"+whichSmallObj+"' class='lbColumnObj lbColumnObj"+i+"'style='border-radius:6px;padding:8px;display:inline-block;'></div>";
				var d1name="#lbGridColumnConfigBox_s"+lbo+"";
				$(d1name).append(d1);
			}
			
			var lbColumnCodeObj_new=lbColumnCodeObj.split('lbttee');
			var lbCheckArr=['text:','xtype:','dataIndex:','hidden:','flex:','width:','align:','sortable:','resizable:'];
			for(var i=1;i<lbColumnCodeObj_new.length;i++){
				var ele=".lbColumnObj"+(i-1);
				$(d1name).children(ele).append('<div class="lbDel" style="background:#9fdaec;width:40px;border-radius: 4px;height:18px;cursor:pointer;text-align:center;color:#1b3c66;">删除</div>');
				
				for(var j=0;j<lbCheckArr.length;j++){
					var q1=lbColumnCodeObj_new[i].indexOf(lbCheckArr[j],0);
					if(q1==-1){
						continue
					}
					var q2=lbColumnCodeObj_new[i].indexOf(':',q1);
					var xCode=lbColumnCodeObj_new[i].substring(q1,q2);
					xCode=xCode.replace(/[’|//|,|}|{|\“|\”|\，]/g,"");
					
					var t1=lbColumnCodeObj_new[i].indexOf(lbCheckArr[j],0);
					t1=lbColumnCodeObj_new[i].indexOf(':',t1)+1;
					var t2=lbColumnCodeObj_new[i].indexOf(',',t1);
					var xCode1=lbColumnCodeObj_new[i].substring(t1,t2);
					xCode1=xCode1.replace(/[’|//|,|}|{|\“|\”|\，]/g,"");
					
					var ele=".lbColumnObj"+(i-1);
					var lbInput="<input style='border-radius:4px;line-height:0px !important;' id='lb"+lbo+"Input"+(i-1).toString()+j+"'value=\""+xCode1+"\"/>";
					$(d1name).children(ele).append("<div style='color:white;display: flex;flex-direction: row;justify-content: space-between;'><span style='min-width:10px'>"+xCode+"</span>"+lbInput+"</div>");
					//input自适应宽度
					let sensor = $('<span class="lbSpanWid"></span>').css({display: 'none'}); 
					$('body').append(sensor); 
				    sensor.html(xCode1);
					let inputWidth = 100;	//sensor.width()+10;//开启 关闭 input自适应宽度
					$("#lb"+lbo+"Input"+(i-1).toString()+j).width(inputWidth);
					sensor.remove();
					//input自适应宽度 end
					
					//console.log(inputWidth);
					//end
					
					/*if(xCode1==''){
						$(d1name).children(ele).append("<div style='color:white;display: flex;flex-direction: row;justify-content: space-between;'><span style='min-width:10px'>"+xCode+"</span>:<input style='width:70px;line-height:0px !important;' "+xCode1+" /></div>");
					}else{
						$(d1name).children(ele).append("<div style='color:white;display: flex;flex-direction: row;justify-content: space-between;'><span style='min-width:10px'>"+xCode+"</span>:<input style='width:70px;line-height:0px !important;' value="+xCode1+" /></div>");

					}*/
				}
			}
			
			lbColumnName='var lb'+lbo+'_columns';
			v1=mainCode.indexOf(lbColumnName,0);
			
		}
		$('#lbGridColumnConfigBox').append('<div style="width:100px;margin:20px 0px;color:#fff;text-align:center;font-size:18px;line-height:28px;cursor:pointer;height:28px;border-radius:16px;background:orange;"class="lb_ColumnSubmit" >提交</div>');
		
	}
	lbInputAutoWidth();
}
//column提交执行方法
function gridColumnConfigSubmit(e){
	 
	$('.lbSpanWid').remove();
	var codeId=$(e.target).parents('.configuration').siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
	PNCode=mainCode;
	var lbo=0;
	var finalCode='';
	var lbColumnName='var lb'+lbo+'_columns';
	
	
		for(var i=0;i<$('#lbGridColumnConfigBox').children('.lbGridColumnConfigBox_s').length;i++,lbo++){
			lbColumnName='var lb'+lbo+'_columns';
			var div1=$('#lbGridColumnConfigBox').children('.lbGridColumnConfigBox_s').eq(i);
			for(var ii=0;ii<div1.children('.lbColumnObj').length;ii++){
				var div2=div1.children('.lbColumnObj').eq(ii);
				for(var iii=0;iii<div2.children('div').length;iii++){
					var div3=div2.children('div').eq(iii);
					var step1=div3.parent('div').parent('div').attr('lbvar');
					var step2=div3.parent('div').attr('lbvar');
					var step3=div3.children('span').text();
					var step4=div3.children('input').val();
					//console.log(div3.parent('div').parent('div').attr('lbvar'));
					//console.log(div3.parent('div').attr('lbvar'));
					//console.log(div3.children('span').text());
					//console.log(div3.children('input').val());
					
					var v1=mainCode.indexOf(step1,0);
					var v2=mainCode.indexOf(step2,v1);
					var v3=mainCode.indexOf(step3,v2);
					var v4=mainCode.indexOf(':',v3)+1;
					var code1=mainCode.substring(0,v4);
					var v5=mainCode.indexOf(',',v4);
					var code2=mainCode.substring(v5);
					mainCode=code1+step4+code2;
					/*var v1=mainCode.indexOf(lbColumnName,0);
					var v1_1=mainCode.indexOf('lbttee'+(ii+1),v1);
					var v2=mainCode.indexOf(div3.children('span').text(),v1_1);
					var v3=mainCode.indexOf(':',v2)+1;
					var code1=mainCode.substring(0,v3);
					var v4=mainCode.indexOf(',',v3);
					var code2=mainCode.substring(v4);
					mainCode=code1+div3.children('input').val()+code2;*/
					
				}
			}
		}
		//解析删除obj
		for(var i=0;i<$('#lbGridColumnConfigBox').children('.lbGridColumnConfigBox_s').length;i++,lbo++){
			lbColumnName='var lb'+lbo+'_columns';
			var div1=$('#lbGridColumnConfigBox').children('.lbGridColumnConfigBox_s').eq(i);
			for(var ii=0;ii<div1.children('.lbColumnObj').length;ii++){
				var div2=div1.children('.lbColumnObj').eq(ii);
				if(div1.children('.lbColumnObj').eq(ii).attr('lbdel')==1){
					var lbWhichVar=div1.children('.lbColumnObj').eq(ii).parent('.lbGridColumnConfigBox_s').attr('lbvar');
					var lbWhichLbttee=div1.children('.lbColumnObj').eq(ii).attr('lbvar');
					//console.log(lbWhichVar);
					//console.log(lbWhichLbttee);
					
					var v1=mainCode.indexOf(lbWhichVar,0);
					var v2=mainCode.indexOf(lbWhichLbttee,v1);
					var v3=mainCode.lastIndexOf('{',v2);//找到小对象开始位置
					var v4=mainCode.indexOf('lbend',v3);
					v4=mainCode.indexOf('},',v4)+2;//找到小对象结束位置
					var code1=mainCode.substring(0,v3);
					var code2=mainCode.substring(v4);
					//console.log(code1);
					//console.log(code2);
					mainCode=code1+code2;
				}
				
				
			}
		}
		//添加column小对象
		if(newClone){
			var whichLbtteeBig=0;
			for(var i=0;i<newClone.siblings('.lbColumnObj').length;i++){
				var num=parseFloat(newClone.siblings('.lbColumnObj').eq(i).attr('lbvar').replace(/[^0-9]/ig,""));
				//console.log(newClone.siblings('.lbColumnObj').eq(i).attr('lbvar'));
				if(whichLbtteeBig<num){
					whichLbtteeBig=num;
				}
			}
			//console.log(whichLbtteeBig);
			var whichVar=newClone.parent('.lbGridColumnConfigBox_s').attr('lbvar');
			var key,value=null;
			var xObjBig="";
			for(var i=0;i<newClone.find('span').length;i++){
				var xObj=newClone.find('span').eq(i).text()+":"+newClone.find('input').eq(i).val()+',';
				//console.log(xObj);
				xObjBig+=xObj;
			}
			xObjBig='lbttee'+(whichLbtteeBig+1)+':'+(whichLbtteeBig+1)+','+xObjBig;
			var a='{';
			var b='lbend:1,},';
			xObjBig=a+xObjBig+b;
			//console.log(xObjBig);
			var v1=mainCode.indexOf(whichVar,0);
			var v2=mainCode.indexOf('[{',v1)+1;
			var code1=mainCode.substring(0,v2);
			var code2=mainCode.substring(v2);
			
			mainCode=code1+xObjBig+code2;
			
			
			newClone=null;
		}
		
		$('#'+codeId).children().remove();
		localStorage.setItem(codeId,mainCode);
		eval(localStorage.getItem(codeId));
	   
	//关闭控制面板
	if($('#lbGridColumnConfigBox').length>0){
		$('#lbGridColumnConfigBox').remove();
	}
}
//column小对象删除
function lbColumnDeleteObj(e){
	//console.log($(e.target));
	if($(e.target).parent('.lbColumnObj').attr('lbDel')==1){
		$(e.target).css('background','#9fdaec').parent('.lbColumnObj').css('border','1px solid white').css('box-shadow','0px 0px 5px white').attr('lbDel','0');
	}else{
		$(e.target).css('background','red').parent('.lbColumnObj').css('border','1px solid red').css('box-shadow','0px 0px 5px red').attr('lbDel','1');
		
	}
}
//column小对象添加
var newClone='';
function lbColumnAddObj(e){
	$('.lbSpanWid').remove();
	//一次只能添加一个小对象
	if($(e.target).parent('.lbGridColumnConfigBox_s').siblings('.lbGridColumnConfigBox_s').children('.newClone').length>0){return}
	if($(e.target).siblings('.newClone').length>0){
		$(e.target).css('background','#9fdaec');
		$(e.target).siblings('.newClone').remove();
		return
	}
	//end
	$(e.target).css('background','#00ff00');
	
	newClone=$(e.target).next().clone();
	
	$(e.target).after(newClone);
	newClone.find('input').eq(0).focus();
	newClone.find('.lbDel').remove();
	newClone.css('border','1px solid #00ff00');
	newClone.css('box-shadow','0px 0px 5px #00ff00');
	newClone.attr('lbdel',0).attr('lbvar',null);
	newClone.attr('class','newClone');
	
	
}
//拖拽换位置
function lbColumnSortable(e) {
	$('.lbGridColumnConfigBox_s').sortable({
		revert: true,
        cancel:'input,.lbAddObj',
		stop: function( event, ui ) {
			//console.log($(event.target).attr('lbvar'));
			var whichVar=$(event.target).attr('lbvar');//console.log(whichVar);
			var lbtteeArr=[];
			for(var i=0;i<$(event.target).children('.lbColumnObj').length;i++){
				lbtteeArr.push($(event.target).children('.lbColumnObj').eq(i).attr('lbvar'));
			}
			//console.log(lbtteeArr); //顺序数组
			var codeId=$(e.target).parent().siblings('.view').attr('codeid');
			var mainCode=localStorage.getItem(codeId);
			//console.log(codeId);
			mainCode=mainCode.replace(/[\r\n]/g,"");
			var v1=mainCode.indexOf(whichVar,0);
			var v2=mainCode.indexOf(',},];',v1)+5;
			//console.log(mainCode);
			//console.log(v2);
			if(v2==-1){
				v2=mainCode.indexOf(',},\n];',v1)+5;
				
			}
			var code1=mainCode.substring(0,v1);
			var code2=mainCode.substring(v1,v2);
			var code3=mainCode.substring(v2);
			//console.log(code1);
			//console.log(code2);
			//console.log(code3);
			var lbtteeObj='';	
			var lbtteeNewObjCode='';
			for(var i=0;i<lbtteeArr.length;i++){
				var d1=code2.indexOf(lbtteeArr[i],0);
				d1=code2.lastIndexOf('{',d1)
				var d2=code2.indexOf('lbend',d1);
				d2=code2.indexOf(',},',d2)+3;
				var code11=code2.substring(d1,d2);
				//console.log(code11);
				lbtteeObj+=code11;
			}
			lbtteeNewObjCode=whichVar+'='+'['+lbtteeObj+'];';
			//console.log(lbtteeNewObjCode);
			
			mainCode=code1+lbtteeNewObjCode+code3;
			$('#'+codeId).children().remove();
			localStorage.setItem(codeId,mainCode);
			eval(localStorage.getItem(codeId));
		}
	});
}
//grid本地模拟数据功能 点击创建控制面板
function lbGridData(e){
	$(e.target).siblings('div').remove();
	var codeId=$(e.target).parent().siblings('.view').attr('codeid');
	var mainCode=localStorage.getItem(codeId);
}

//增加input双击宽度事件
function lbInputAutoWidth(){
	function a(e){
		let sensor = $('<span class="lbSpanWid"></span>').css({display: 'none'}); 
		$('body').append(sensor); 
	    sensor.html($(e.target).val());
		let inputWidth =sensor.width()+33;
		if($(e.target).width()>inputWidth){
			return
		}
		$(e.target).css('transition','0.3s');
		$(e.target).width(inputWidth);
		sensor.remove();
	}
	$("#lbItemsConfigBox").on('focus','input',function(e){
		$("#lbItemsConfigBox input").width(100);
		a(e);
	});
	$("#lbGridColumnConfigBox").on('focus','input',function(e){
		$("#lbGridColumnConfigBox input").width(100);
		a(e);
	});
}
