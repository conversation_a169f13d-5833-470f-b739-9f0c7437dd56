body {
	padding-top:10px;
	padding-bottom: 10px;
	margin-left:200px;
	-webkit-transition: margin 500ms ease;
	-moz-transition: margin 500ms ease;
	-ms-transition: margin 500ms ease;
	-o-transition: margin 500ms ease;
	transition: margin 500ms ease;
}
@media (max-width: 980px) {
	/* Enable use of floated navbar text */
	.navbar-text.pull-right {
		float: none;
		padding-left: 5px;
		padding-right: 5px;
	}
}
@media (max-width: 979px) {
	.navbar-fixed-top { position:fixed; }
}
.navbar-inverse .brand {width:180px; color:#007af5; }
.brand img {float:left; margin:2px 10px 0 0; }
.brand .label { 
	position:relative; 
	left:10px; 
	top:-3px; 
	font-weight:normal; 
	font-size:9px;
	background:#666;
	-webkit-box-shadow: inset 1px 1px 3px #007af5;
	-moz-box-shadow: inset 1px 1px 3px #007af5;
	box-shadow: inset 1px 1px 3px #007af5;
}

.edit .demo { margin-left:0px; margin-top:13px; padding:4px 0px 0px; border: 0px solid #7e7b7b; border-radius: 4px; position:relative; word-wrap: break-word;}
/* .edit .demo:after {
	background-color: #F5F5F5;
	border: 1px solid #DDDDDD;
	border-radius: 4px 0 4px 0;
	color: #9DA0A4;
	content: "Container";
	font-size: 12px;
	font-weight: bold;
	left: -1px;
	padding: 3px 7px;
	position: absolute;
	top: -1px;
} */
.sidebar-nav {
	position:fixed;
	width:200px;
	left:0px;
	bottom:0;
	top:43px;
	background:#1b0f0f;
	padding: 9px 0; z-index:10;
	-webkit-transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease;
}
.sidebar-nav .nav-header { cursor:pointer; font-size:14px; color:#fff; text-shadow:0 1px 0 rgba(0, 0, 0, 0.3);}
.sidebar-nav .nav-header span.label { font-size:10px; /*padding-bottom:0;*/ position:relative; top:-1px;}
.sidebar-nav .nav-header i.icon-plus {}
.sidebar-nav .nav-header .popover {color:#999; text-shadow:none;}

.popover-info {position:relative;}
.popover-info .popover {display:none; top: -12.5px; left:15px; }
.popover-info:hover .popover {display:block; opacity:1; width:400px;}
.popover-info:hover .popover .arrow {top:23px;}

.sidebar-nav .accordion-group { border:none; }
.boxes {}
.sidebar-nav li { line-height:25px; }
.sidebar-nav .box { line-height:25px; width:170px; height:25px; }
.sidebar-nav .preview { display: block; color:#ffc91c; font-size:12px; line-height:22px;font-family: '微软雅黑', '黑体';}
.sidebar-nav .preview input { width:90px; padding:0 10px; background:#fff; font-size:10px; color:#1e032d; line-height:20px; height:20px; position:relative; top:-1px; }
.sidebar-nav .view { display: none; }
.sidebar-nav .remove,
.sidebar-nav .configuration { display: none; }

.sidebar-nav .boxes { display:none;}

.demo .preview { display: none; }
.demo .box .view { display: block; padding-top:25px;}

.ui-draggable-dragging .view { display:block;}
/*.demo .ui-sortable-placeholder { outline: 5px dotted #ddd; visibility: visible!Important; border-radius: 4px; }*/
.ui-sortable-placeholder { padding-top:30px;outline: 1px dashed #fff;visibility: visible!Important; border-radius: 4px;}
.lbSortableBox .ui-sortable-placeholder { padding-top:0px;outline: 0px dashed #fff;background:#fff;box-shadow:0px 0px 4px 3px #fff;visibility: hidden; border-radius: 4px;}
.edit .drag { position: absolute; top: 0;right: 0; cursor: pointer; }

.box,.lyrow { position:relative;}

.edit .demo .lyrow .drag { top:5px; right:80px; z-index:10; }
.edit .demo .column .box .drag { top:5px; }
.edit .demo .column .box .configuration {position: absolute; top: 3px; right: 140px;z-index:10;white-space:nowrap; }
.edit .demo .remove { position: absolute; top: 5px; right: 5px; z-index:10; }
.demo .configuration {
	filter: alpha(opacity=0);
	opacity: 0;
	-webkit-transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease;
}
.demo .drag, .demo .remove {
	filter: alpha(opacity=20); opacity: 0;
	-webkit-transition: all 500ms ease;
	-moz-transition: all 500ms ease;
	-ms-transition: all 500ms ease;
	-o-transition: all 500ms ease;
	transition: all 500ms ease;
}
.demo .lyrow:hover > .drag, 
.demo .lyrow:hover > .configuration, 
.demo .lyrow:hover > .remove,
.demo .box:hover .drag, 
.demo .box:hover .configuration, 
.demo .box:hover .remove { filter: alpha(opacity=100); opacity: 1; }
/* .edit .demo .row-fluid:before {
	background-color: #F5F5F5;
	border: 1px solid #DDDDDD;
	border-radius: 4px 0 4px 0;
	color: #9DA0A4;
	content: "Row";
	font-size: 12px;
	font-weight: bold;
	left: -1px;
	line-height:2;
	padding: 3px 7px;
	position: absolute;
	top: -1px;
} */
.demo .row-fluid {
	background-color: rgb(123, 123, 123);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-shadow: inset 0 1px 13px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: inset 0 1px 13px rgba(0, 0, 0, 0.1);
	box-shadow: inset 0 1px 13px rgba(0, 0, 0, 0.1);
	border: 0px solid #8F0000;
	border-radius: 4px 4px 4px 4px;
	/* margin: 15px 0; */
	position: relative;
	padding: 10px  0px 0;
}
/* .edit .column:after {
	background-color: #F5F5F5;
	border: 1px solid #DDDDDD;
	border-radius: 4px 0 4px 0;
	color: #9DA0A4;
	content: "Column";
	font-size: 12px;
	font-weight: bold;
	left: -1px;
	padding: 3px 7px;
	position: absolute;
	top: -1px;
} */
.column {
	background-color: #AEAEAE;
	border: 1px solid #898989;
	border-radius: 4px 4px 4px 4px;
	margin: 15px 0 0 0;
	/* padding: 39px 19px 24px; */
	position: relative;
}

/* preview */
body.devpreview { margin-left:0px;}
.devpreview .sidebar-nav { 
	left:-200px;
	-webkit-transition: all 0ms ease;
	-moz-transition: all 0ms ease;
	-ms-transition: all 0ms ease;
	-o-transition: all 0ms ease;
	transition: all 0ms ease;
}
.devpreview .drag, .devpreview .configuration, .devpreview .remove { display:none !Important; }
.sourcepreview .column, .sourcepreview .row-fluid, .sourcepreview .demo .box {
	margin:0px 0;
	padding:0px;
	background:none;
	border:none;
	-webkit-box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.00);
	-moz-box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.00);
	box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.00);
}
.devpreview .demo .box, .devpreview .demo .row-fluid { padding-top:0; background:none; }
.devpreview .demo .column { padding-top:19px; padding-bottom:19px; }
#download-layout { display: none }
#editorModal textarea,
#lb-edit textarea,
#downloadModal textarea { width:100%;height:280px;resize: none;-moz-box-sizing: border-box;-webkit-box-sizing: border-box;box-sizing: border-box;background:#cecece; }
#zidingyizujian textarea { width:100%;height:280px;resize: none;-moz-box-sizing: border-box;-webkit-box-sizing: border-box;box-sizing: border-box;background:#cecece; }

#editorModal {width:640px;}
a.language-selected { font-style: italic; font-weight: bold; }