Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();

	var subPanel = Ext.create('Ext.panel.Panel', {
		region : "center",
		autoScroll:true,
		baseCls:'customize_panel_back',
		layout : "fit",
		border : false,
		html: docHtml
	});
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		height : contentPanel.getHeight(),
		renderTo : "showtemplate_grid",
		layout : 'border',
//		cls:'customize_panel_back',
		border : false,
		items : [ subPanel ],
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
//			baseCls:'customize_panel_back',
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [{
                text: '返回',
                xtype:'button',
                cls: 'Common_Btn',
                handler: function(){
              	destroyRubbish();
          		contentPanel.getLoader().load({url: 'templateManagement.do',scripts: true});
              }
          } ]
		} ]
	});
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight ());
		mainPanel.setWidth (contentPanel.getWidth ());
    });
});

$('.test').html('dddddddddddddddddddd');
//$('#showtemplate_grid').html('DDD');