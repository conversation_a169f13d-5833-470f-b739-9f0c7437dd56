var pageLimit = 30;
var upLoadformPane = '';
var upldWin ;
var upadteWin = '';
var updateformPane = '';
var showWin = '';
var showformPane = '';

Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	
	Ext.define('dataModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'itype',
			type : 'string'
		}, {
			name : 'itemplatetype',
			type : 'string'
		}, {
			name : 'itemplatename',
			type : 'string'
		}, {
			name : 'iservename',
			type : 'string'
		}, {
			name : 'iplatform',
			type : 'string'
		}, {
			name : 'itooltype',
			type : 'string'
		}, {
			name : 'iauth',
			type : 'string'
		}, {
			name : 'iserveone',
			type : 'string'
		},{
			name : 'bsname',
			type : 'string'
		},{
			name : 'typename',
			type : 'string'
		},{
			name : 'iservetwo',
			type : 'string'
		},{
			name : 'itemplatedes',
			type : 'string'
		}, {
			name : 'itemplate',
			type : 'string'
		}, {
			name : 'updateTime',
			type : 'string'
		},  {
			name : 'iloginname',
			type : 'string'
		},
		{
			name : 'ifullname',
			type : 'string'
		}
		]
	});
	
	//定义store
	var store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'dataModel',
		pageSize : pageLimit,
		proxy : {
			type : 'ajax',
			url : 'queryTemplate.do',
			reader : {
				type : 'json',
				root : 'dataList',
			}
		}
	});
	store.on('beforeload', function (store, options) {
		var condition  =  form.getForm().findField("leftC").getValue();
		var moduleType = form.getForm().findField("moduleTypeCombo").getValue();
		Ext.apply(store.proxy.extraParams, {
			condition : condition,
			moduleType : moduleType
		});
	});
	
	/** 上传窗口--业务类别Store * */
	var sateComboStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
//        data: [[1, '作业调度'], [2, '健康巡检'], [3, '应用变更'], [4, '灾备切换'], [5, '定时任务'],[6,'应急操作'],[100,'平台管理'],[101,'通用'],[8,'其他']]
        data: [[3, '应用变更'], [4, '灾备切换'], [5, '定时任务'],[6,'应急操作']]
    });
	/** 上传窗口--适用系统Store * */
	var sysComboStore = new Ext.data.ArrayStore({
        fields: ['name'],
        data: [['WINDOWS'],['LINUX'], ['HPUX'], ['Unix']]
    });
	/** 上传窗口--权限Store * */
	var authSateComboStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data: [[2, '高级用户可见'], [3, '中级用户可见'],[4, '低级用户可见']]
    });
	
	/** 查询条件--业务类别Store * */
	var moduleTypeStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data: [[null,'全部'],[3, '应用变更'], [4, '灾备切换'], [5, '定时任务'],[6,'应急操作']]
    });
	
	/** 查询条件 * */
	var itemNameForQuery = Ext.create ('Ext.form.TextField',
	{
		name : 'leftC',
	    margin : '5',
	    fieldLabel : '查询条件',
	    emptyText : '文档名称/文档说明',
	    labelWidth : 80,
	    width : '25%',
	    xtype : 'textfield'
	});
	
	//查询条件--业务类别下拉框
	var moduleTypeCombo = Ext.create('Ext.form.ComboBox', {
    	id:'moduleTypeCombo',
    	name:'moduleTypeCombo',
        width : '25%',
		fieldLabel: '业务类别',
		labelWidth : 80,
		hidden:true,
        store: moduleTypeStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        emptyText: '请选择...',
        allowBlank: true,
        editable: false,
        mode: 'local',
        margin : '5',
        anchor: '100%',
	});

	//业务类别下拉框
	var combState = Ext.create('Ext.form.ComboBox', {
    	id:'comboState',
        width : '23%',
		fieldLabel: '业务类别',
		labelWidth : 80,
        store: sateComboStore,
        hidden : true,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        emptyText: '请选择...',
        allowBlank: true,
        editable: false,
        mode: 'local',
        anchor: '99.5%'
	});
	//权限下拉框
	var authCombState = Ext.create('Ext.form.ComboBox', {
    	id:'authCombState',
        width : '23%',
		fieldLabel: '权限',
		labelWidth : 80,
        store: authSateComboStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        emptyText: '请选择权限...',
        allowBlank: true,
        editable: false,
        mode: 'local',
        anchor: '99.5%'
	});
	//适用系统下拉框
	var sysCombState = Ext.create('Ext.form.ComboBox', {
    	id:'sysCombState',
        width : '23%',
		fieldLabel: '适用系统',
		labelWidth : 80,
        store: sysComboStore,
        displayField: 'name',
       // valueField: 'id',
        triggerAction: 'all',
        emptyText: '请选择适用系统...',
        allowBlank: true,
        editable: false,
        mode: 'local',
        anchor: '99.5%'
	});
	
	/** 分页工具栏 * */
	pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : store,
		dock : 'bottom',
		displayInfo : true,
		baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
	});
	
	/** 定义复选框 * */
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	 var bussData = Ext.create('Ext.data.Store', {
	        fields: ['iid', 'bsName'],
	        autoLoad: true,
	        proxy: {
	            type: 'ajax',
	            url: 'bsManager/getBsAll.do',
	            reader: {
	                type: 'json',
	                root: 'dataList'
	            }
	        }
	    });
	 /** 一级分类* */
	 var bussCb = Ext.create('Ext.form.field.ComboBox', {
	        name: 'sysName',
	        labelWidth: 80,
	        width: '23%',
	        fieldLabel: '一级分类',
	      //  padding: '0 5 0 0',
	        displayField: 'bsName',
	        valueField: 'iid',
	        editable: false,
	        queryMode: 'local',
	        emptyText: '--请选择一级分类--',
	        store: bussData,
	        mode: 'local',
	        anchor: '99.5%',
	        listeners: {
	            change: function() { // old is keyup
	                bussTypeCb.clearValue();
	                bussTypeCb.applyEmptyText();
	                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
	                bussTypeData.load({
	                    params: {
	                        fk: this.value
	                    }
	                });
	            }
	        }
	    });
	 /** 二级分类* */
	    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
	        name: 'bussType',
	       // padding: '0 5 0 0',
	        labelWidth: 80,
	        width: '23%',
	        queryMode: 'local',
	        fieldLabel: '二级分类',
	        displayField: 'sysType',
	        valueField: 'sysTypeId',
	        editable: false,
	        emptyText: '--请选择二级分类--',
	        store: bussTypeData,
	        mode: 'local',
	        anchor: '99.5%'
	    });
	/** form * */
	var form = Ext.create('Ext.form.Panel', {
		region : 'north',
	  	layout : 'anchor',
	  	buttonAlign : 'center',
//	  	collapsible : true,//可收缩
//	    collapsed : true,//默认收缩
	  	baseCls:'customize_gray_back',
	    border: false,
	    dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
	  		items:[
	  			itemNameForQuery, 
				moduleTypeCombo,
				{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						QueryMessage();
					}
				}, 
				{
					xtype : 'button',
					text : '重置',
					cls : 'Common_Btn',
					handler : function() {
						resetQuery();
					}
				},  '->',{
					xtype : 'button',
					text : '增加',
					cls : 'Common_Btn',
					handler : function() {
						upload();
					}
				}, 
				{
					xtype : 'button',
					text : '删除',
					cls : 'Common_Btn',
					handler : function() {
						DeleteMessage();
					}
				}
	  		]
		}]
	});

	/** Comumn**/
	var columns = [ {
		width : 40,
//        locked : true,
        resizable : true,
		xtype : 'rownumberer'
	}, {
		text : '表主键',
		width : 50,
		hidden:true,
		dataIndex : 'iid'
	}, {
		text : '业务类别',
		width: 80,
		hidden:true,
		dataIndex : 'itype',
		renderer : function(value, metadata, record) {
			var values = value;
			if(value==1)
			{
				values='作业调度';
			}
			else if(value==7){
				values='健康巡检';
			}
			else if(value==3){
				values='应用变更';
			}
			else if(value==4){
				values='灾备切换';
			}
			else if(value==5){
				values='定时任务';
			}
			else if(value==6){
				values='应急操作';
			}
			else if(value==100){
				values='平台管理';
			}
			else if(value==101){
				values='通用';
			}
			else if(value==8){
				values='日常操作';
			}
			
			return values;
		}
	},
	{
		text : '文档类型',
		width : 80,
		dataIndex : 'itemplatetype'
	}, 
	{
		text : '文档名称',
		width: 200,
		flex:1,
		dataIndex : 'itemplatename',renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, 
	{
		text : '服务名称',
		width: 100,
		dataIndex : 'iservename',renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, 
	{
		text : '适用系统',
		width: 80,
		dataIndex : 'iplatform'
//		editor : new Ext.form.field.ComboBox (
//		            {
//		                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
//		                editable : false,// 是否可输入编辑
//		                store : sysComboStore,
//		                queryMode : 'local',
//		                displayField : 'name',
//		                valueField : 'name',
//		               // xtype : 'textfield'
//		            }),
	}, 
	{
		text : '工具类型',
		width: 50,
		hidden:true,
		dataIndex : 'itooltype'
	},
	{
		text : '权限',
		width: 100,
		dataIndex : 'iauth',
		renderer : function(value, metadata, record) {
			var values = value;
			if(value==2)
			{
				values='高级用户可见';
			}else if(value==3){
				values='中级用户可见';
			}
			else {
				values='低级用户可见';
			}
			return values;
		}
	},
	{
		text : '一级分类ID',
		width: 10,
		hidden : true,
		dataIndex : 'iserveone'
	},{
		text : '一级分类',
		width: 100,
		dataIndex : 'bsname',renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	},{
		text : '二级分类ID',
		width: 10,
		hidden : true,
		dataIndex : 'iservetwo',
	},{
		text : '二级分类',
		width: 100,
		dataIndex : 'typename',renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	},
	{
		text : '文档说明',
		width: 180,
		dataIndex : 'itemplatedes',
//		editor : {
//			xtype : 'textfield'
//		},
		renderer : function(value, metadata, record) {
			metadata.tdAttr = 'data-qtip="' + value.replace(/\r\n/gi, '<br/>').replace(/\n/gi, '<br/>') + '"';
			return value.replace(/\r\n/gi, '<br/>').replace(/\n/gi, '<br/>');
		}
	}, 
	{
		text : '更新时间',
		hidden : true,
		width: 150,
		dataIndex : 'updateTime'
	},	
	{
		text : '更新人',
		hidden : true,
		width: 100,
		dataIndex : 'ifullname'
	},	
	{
		text : '操作',
		width: 165,
		dataIndex : 'download',
		//renderer:getfile
		renderer: function(value, p, record) {
			 var iid = record.get('iid');
			 var iauth = record.get('iauth');
			 var itemplatename = record.get('itemplatename');
			 var iplatform = record.get('iplatform');
			 var iservename =record.get('iservename');
			 var itemplatedes = record.get('itemplatedes');
			 var iserveone = record.get('iserveone'); 
			 var iservetwo = record.get('iservetwo');
			 var bsname = record.get('bsname');
			 var typename = record.get('typename');
			 var updateTime = record.get('updateTime');
			 var ifullname = record.get('ifullname');
			 var itemplatetype = record.get('itemplatetype');
			 return '<a href="javascript:void(0)" onclick="showTmplt(' + iid + ',' + iauth + ',\'' + itemplatename + '\',\'' + iplatform + '\',\'' + iservename + '\',\'' + itemplatedes + '\',\'' + bsname + '\',\'' + typename + '\',\'' + ifullname + '\',\'' + updateTime + '\')">&nbsp;查看</a>&nbsp;&nbsp;<a href="javascript:void(0)" onclick="updateTmplt(' + iid + ',' + iauth + ',\'' + itemplatename + '\',\'' + iplatform + '\',\'' + iservename + '\',\'' + itemplatedes + '\',' + iserveone + ',' + iservetwo + ')">编辑</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="downloadTmplt(' + iid + ')">下载</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="showylTmplt(' + iid+",\'" +itemplatetype+ '\')">预览</a>&nbsp;&nbsp;' + '</span>';
		//return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"downloadTmplt('"+record.get("iid")+"');\"><span>下载</span></a>";
		}
	}];
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	/** GridPanel * */
	var grid = Ext.create('Ext.grid.Panel', {
		id:"template_grid",
		region : 'center',
		border : false,
		cls:'customize_panel_back',
		store : store,
		autoScroll : true,
		columnLines : true,
		selModel : selModel,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		bbar : pageBar,
		columns : columns,
		plugins : [ cellEditing ]
	});
	//导入文件Panel
	upLoadformPane =Ext.create('Ext.form.Panel', {
        //id:'upLoadpanel',  
        bodyPadding : 10,
        border:false,
		items: [
			combState,{
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'iservename',
		        fieldLabel: '服务名称',
		        id:'iservename',
		        anchor: '99.5%'
		    },
		    sysCombState,
		    authCombState,
		    bussCb,
		    bussTypeCb,
			{
		        xtype     : 'textareafield',
		        labelWidth: 80,
		        name      : 'itemplatedes',
		        fieldLabel: '文档说明',
		        id:'itemplatedes',
		        height : 100,
		        anchor: '99.5%'
		    },
		    
			{
				xtype: 'filefield',
				name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel: '选择文件',
				labelWidth: 80,
				labelStyle:'margin:10px 0 0 0',
				msgTarget: 'side',
				anchor: '100%',
				height : 30,
				buttonText: '浏览'
			},
			{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items:['->',
					{
						id:'upldBtnId',
						xtype : 'button',
						text: '导入',
						cls : 'Common_Btn',
						handler: function() {
							//业务系统校验
//							var itype  =  Ext.getCmp('comboState').getValue();
//							if('' == itype||null == itype){
//			    				Ext.Msg.alert('提示',"请选择业务类别");
//			    				return ;
//			    			}
							//文件选择为空校验
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件");
			    				return ;
			    			}
			    			 var iserveone = bussCb.getValue(); 
			    			 var iservetwo = bussTypeCb.getValue();
			    			//文件类型校验
//			    			var hdtmpFilNam=form.findField("file").getValue();
//			    			if(!checkFile(hdtmpFilNam)){
//				    			  form.findField("file").setRawValue('');
//				    			  return;
//				    		}
			    			//文档说明校验
			    			var itemplatedes=form.findField("itemplatedes").getValue();
							if('' == itemplatedes||null == itemplatedes){
			    				Ext.Msg.alert('提示',"请填写文档说明");
			    				return ;
			    			}
							//服务名称校验
			    			var iservename=form.findField("iservename").getValue();
							if('' == iservename||null == iservename){
			    				Ext.Msg.alert('提示',"请填写服务名称");
			    				return ;
			    			}
							//适用系统校验
							var iplatform=form.findField("sysCombState").getValue();
							if('' == iplatform||null == iplatform){
			    				Ext.Msg.alert('提示',"请选择适用系统");
			    				return ;
			    			}
							//工具类型校验
//			    			var itooltype=form.findField("itooltype").getValue();
//							if('' == itooltype||null == itooltype){
//			    				Ext.Msg.alert('提示',"请填写工具类型");
//			    				return ;
//			    			}
							//权限校验
							var iauth=form.findField("authCombState").getValue();
							if('' == iauth||null == iauth){
			    				Ext.Msg.alert('提示',"请选择权限");
			    				return ;
			    			}
							//服务一级分类校验
                            if ('' == iserveone||null == iserveone) {
                                Ext.MessageBox.alert("提示", "请选择服务一级分类!");
                                return;
                            }
                            //服务二级分类校验
                            if ('' == iservetwo||null == iservetwo) {
                                Ext.MessageBox.alert("提示", "请选择服务二级分类!");
                                return;
                            }
							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
//									 return;
								form.submit({
									url: 'uploadTemplate.do',
									params:{
										  //itype:itype,
						            	  itemplatedes:itemplatedes,
						            	  iservename:iservename,iplatform:iplatform,
						            	  //itooltype:itooltype,
						            	  iauth:iauth,
						            	  iserveone:iserveone,iservetwo:iservetwo
					            	  },
								    success: function(form, action) 
								    {
								    	var respText = Ext.JSON.decode(action.response.responseText);
								    	var msg = respText.message;//提示信息
										var isOk =  respText.isOk;//是否导入成功
										//提示消息
										Ext.Msg.alert('提示', msg);
										//导入成功，重新加载数据
										if(isOk)
										{
									        upldWin.close();
									        //重新加载数据
									        QueryMessage();
									        return;
										}
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					},
					{
						xtype : 'button',
						text:'重置',
						cls : 'Common_Btn',
						handler:function(){
							this.up('form').getForm().reset();
			            }
			        }, {  
			        	xtype : 'button',
			            text : '关闭', 
			            cls : 'Common_Btn',
			            handler : function() {
			            	upldWin.close();  
			            }  
			        }
				]
			}
		]
	});
	 if (!upldWin) {
		 upldWin = Ext.create('widget.window', {
			 title: '上传新文档',
			    width: 800,
			    height: 450,
			    resizable:false,
//					    layout: 'fit',
			    modal:true,
			    closeAction: 'hide',
			    items:  [upLoadformPane]
		 })
		 }
	/** 主面板Panel * */
	var mainPanel = Ext.create('Ext.panel.Panel', {
		height : contentPanel.getHeight() - modelHeigth,
		renderTo : "template_grid",
		layout : 'border',
		border : false,
//		cls:'customize_panel_back panel_space_top',
//		cls:'customize_panel_back',
//		bodyPadding : 5,
		items : [ form, grid ]
	});
	
	/** 重置窗口大小 * */
	contentPanel.on('resize', function() {
		mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
	});
	
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
	
	/** 自定义方法 *******************************************************************/
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};
	
	//查询按钮方法
	function QueryMessage() 
	{
		pageBar.moveFirst();
	}
	//重置按钮方法
	function resetQuery()
	{
		form.getForm().findField("leftC").setValue(null);//查询条件
		form.getForm().findField("moduleTypeCombo").setValue(null);//业务类别
	}
	
	//删除按钮方法
	function DeleteMessage() {
		var seledCnt = grid.getSelectionModel().getSelection();
		if (seledCnt.length < 1) {
			Ext.MessageBox.alert("提示", "请选择要删除的数据");
			return;
		}

		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认删除", "确定删除选中的记录", function(id) {
			if (id == 'yes')
				deleteData();
		});
	}
	function deleteData() {
		var m = grid.getSelectionModel().getSelection();
		var iidStr = "";
		for (var i = 0, len = m.length; i < len; i++) {
			if (i == 0)
				iidStr +=  m[i].data.iid;
			else
				iidStr +=  "," + m[i].data.iid;
		}
		//请求后台
		Ext.Ajax.request({
			url : 'deleteTemplate.do',
			method : 'post',
			params : {
				iidStr:iidStr
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					Ext.Msg.alert('提示', '删除操作成功');
					pageBar.moveFirst();
				} else {
					Ext.Msg.alert('提示', '删除操作失败');
				}
			},
			failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
	}
	
	
	//文件类型校验方法
	function checkFile(fileName)
	{
	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
	    if(!file_reg.test(fileName)){  
	    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件(xls/xlsx/xlsm)'); 
	        return false;
	    }
	    return true;
	}
	//上传窗口--上传方法
	function upload() 
	{
		upldWin.show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnId").up('form').getForm();
			form.reset();
		});
	}

});

function showylTmplt(recordiid,itemplatetype) 
{
	if(itemplatetype=='xls' || itemplatetype=='xlsx' || itemplatetype=='doc' || itemplatetype=='docx' || itemplatetype=='log' || itemplatetype=='txt' || itemplatetype=='config' || itemplatetype=='bat' || itemplatetype=='shell' || itemplatetype=='py'){
		
	}
	contentPanel.getLoader().load({
		url : "showtemplateManagement.do",
		scripts : true,
		params : {
			iid : recordiid,
			itemplatetype:itemplatetype
		}
	});
}
//下载
function downloadTmplt(recordiid) 
{
	window.location.href = 'downloadTemplate.do?iid='+ encodeURI(recordiid);
}
function showTmplt(iid ,auth,templatename ,platform , servename ,templatedes , bsname,typename,ifullname,updateTime){


	//销毁win窗口
	if(!(null==showWin || undefined==showWin || ''==showWin)){
		showWin.destroy();
		showWin = null;
	}
	if(!(null==showformPane || undefined==showformPane || ''==showformPane)){
		showformPane.destroy();
		showformPane = null;
	} 
	//查看文件Panel
	showformPane =Ext.create('Ext.form.Panel', {
        //id:'upLoadpanel',  
        bodyPadding : 10,
        border:false,
		items: [
			{
			    xtype     : 'textfield',
			    labelWidth: 80,
			    name      : 'templatename',
			    value: templatename,
			    fieldLabel: '文档名称',
			    readOnly:true,
			    id:'templatename',
			    anchor: '99.5%'
			},
			{
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'servename',
		        value: servename,
		        fieldLabel: '服务名称',
		        readOnly:true,
		        id:'servename',
		        anchor: '99.5%'
		    },
		    {
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'platform',
		        value: platform,
		        fieldLabel: '适用系统',
		        readOnly:true,
		       // disabled:true,
		        id:'platform',
		        anchor: '99.5%'
		    },
		    {
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'bsname',
		        value: bsname,
		        fieldLabel: '一级分类',
		        readOnly:true,
		        id:'bsname',
		        anchor: '99.5%'
		    },
		    {
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'typename',
		        value: typename,
		        fieldLabel: '二级分类',
		        readOnly:true,
		        id:'typename',
		        anchor: '99.5%'
		    },
		    {
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'ifullname',
		        value: ifullname,
		        fieldLabel: '创建人',
		        readOnly:true,
		        id:'ifullname',
		        anchor: '99.5%'
		    },
		    {
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'updateTime',
		        value: updateTime,
		        fieldLabel: '创建时间',
		        readOnly:true,
		        id:'updateTime',
		        anchor: '99.5%'
		    },
			{
		        xtype     : 'textareafield',
		        labelWidth: 80,
		        name      : 'templatedes',
		        fieldLabel: '文档说明',
		        readOnly:true,
		        value: templatedes,
		        id:'templatedes',
		        height : 100,
		        anchor: '99.5%'
		    },{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items:['->',
					{  
			        	xtype : 'button',
			            text : '关闭', 
			            cls : 'Common_Btn',
			            handler : function() {
			            	showWin.close();  
			            }  
			        }
				]
			}
		]
	});
	//查看窗口
	showWin = Ext.create('Ext.window.Window', {
	    title: '查看文档信息',
	    width: 800,
	    height: 490,
	    resizable:false,
//			    layout: 'fit',
	    modal:true,
	    closeAction: 'destroy',
	    items:  [showformPane]
	}).show();
	showWin.on("beforeshow",function(self, eOpts){
		var form = showformPane.getForm();
		form.reset();
	});
	showWin.on("destroy",function(self, eOpts){
		showformPane.destroy();
	});
}
function updateTmplt(iid ,auth,templatename ,platform , servename ,templatedes , serveone , servetwo ){

	//销毁win窗口
	if(!(null==upadteWin || undefined==upadteWin || ''==upadteWin)){
		upadteWin.destroy();
		upadteWin = null;
	}
	if(!(null==updateformPane || undefined==updateformPane || ''==updateformPane)){
		updateformPane.destroy();
		updateformPane = null;
	}
	/** 编辑窗口--适用系统Store * */
	var sysComboStore1 = new Ext.data.ArrayStore({
        fields: ['name'],
        data: [['WINDOWS'],['LINUX'], ['HPUX'], ['Unix']]
    });
	/** 编辑窗口--权限Store * */
	var authSateComboStore1 = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data: [[2, '高级用户可见'], [3, '中级用户可见'],[4, '低级用户可见']]
    });
	//编辑权限下拉框
	var authCombState1 = Ext.create('Ext.form.ComboBox', {
    	id:'authCombState1',
        width : '23%',
		fieldLabel: '权限',
		labelWidth : 80,
        store: authSateComboStore1,
        value: auth,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        emptyText: '请选择权限...',
        allowBlank: true,
        editable: false,
        mode: 'local',
        anchor: '99.5%'
	});
	//编辑适用系统下拉框
	var sysCombState1 = Ext.create('Ext.form.ComboBox', {
    	id:'sysCombState1',
        width : '23%',
		fieldLabel: '适用系统',
		labelWidth : 80,
        store: sysComboStore1,
        value: platform,
        displayField: 'name',
       // valueField: 'id',
        triggerAction: 'all',
        emptyText: '请选择适用系统...',
        allowBlank: true,
        editable: false,
        mode: 'local',
        anchor: '99.5%'
	});
	 //编辑一级分类Store
		 var bussData1 = Ext.create('Ext.data.Store', {
		        fields: ['iid', 'bsName'],
		        autoLoad: true,
		        proxy: {
		            type: 'ajax',
		            url: 'bsManager/getBsAll.do',
		            reader: {
		                type: 'json',
		                root: 'dataList'
		            }
		        }
		    });
	 /** 编辑一级分类* */
	 var bussCb1 = Ext.create('Ext.form.field.ComboBox', {
	        name: 'sysName1',
	        labelWidth: 80,
	        width: '23%',
	        fieldLabel: '一级分类',
	      //  padding: '0 5 0 0',
	        displayField: 'bsName',
	        valueField: 'iid',
	        editable: false,
	        queryMode: 'local',
	        store: bussData1,
	        value:serveone,
	        mode: 'local',
	        anchor: '99.5%',
	        listeners: {
	            change: function() { // old is keyup
	                bussTypeCb1.clearValue();
	                bussTypeCb1.applyEmptyText();
	                bussTypeCb1.getPicker().getSelectionModel().doMultiSelect([], false);
	                bussTypeData1.load({
	                    params: {
	                        fk: this.value
	                    }
	                });
	            }
	        }
	    });
	//编辑二级分类Store
	 var bussTypeData1 = Ext.create('Ext.data.Store', {
	        fields: ['sysTypeId', 'sysType'],
	        autoLoad: true,
	        proxy: {
	            type: 'ajax',
	            url: 'bsManager/getBsTypeByFk.do',
	            reader: {
	                type: 'json',
	                root: 'dataList'
	            }
	        }
	    });
	 
	 bussTypeData1.on('beforeload', function(store, options) {
			var new_params = {
				fk: serveone
			};
			Ext.apply(bussTypeData1.proxy.extraParams, new_params);
		});
	 /** 编辑二级分类* */
	    var bussTypeCb1 = Ext.create('Ext.form.field.ComboBox', {
	        name: 'bussType1',
	       // padding: '0 5 0 0',
	        labelWidth: 80,
	        width: '23%',
	        fieldLabel: '二级分类',
	        displayField: 'sysType',
	        valueField: 'sysTypeId',
	        editable: false,
	        queryMode: 'local',
	        store: bussTypeData1,
	        value:servetwo,
	        mode: 'local',
	        anchor: '99.5%'
	    });
	   
	//编辑时导入文件Panel
	updateformPane =Ext.create('Ext.form.Panel', {
        //id:'upLoadpanel',  
        bodyPadding : 10,
        border:false,
		items: [
			{
			    xtype     : 'textfield',
			    labelWidth: 80,
			    name      : 'templatename',
			    value: templatename,
			    fieldLabel: '文档名称',
			    id:'templatename',
			    anchor: '99.5%'
			},
			{
		        xtype     : 'textfield',
		        labelWidth: 80,
		        name      : 'servename',
		        value: servename,
		        fieldLabel: '服务名称',
		        id:'servename',
		        anchor: '99.5%'
		    },
		    sysCombState1,
		    authCombState1,
		    bussCb1,
		    bussTypeCb1,
			{
		        xtype     : 'textareafield',
		        labelWidth: 80,
		        name      : 'templatedes',
		        fieldLabel: '文档说明',
		        value: templatedes,
		        id:'templatedes',
		        height : 100,
		        anchor: '99.5%'
		    },{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items:['->',
					{
						id:'updateBtnId',
						xtype : 'button',
						text: '保存',
						cls : 'Common_Btn',
						handler: function() {
			    			 var iserveone = bussCb1.getValue(); 
			    			 var iservetwo = bussTypeCb1.getValue();
			    			 var itemplatename=updateformPane.getForm().findField("templatename").getValue();
			    			 if('' == itemplatename||null == itemplatename){
				    				Ext.Msg.alert('提示',"请填写文档名称");
				    				return ;
				    			}
			    			//文档说明校验
			    			var itemplatedes=updateformPane.getForm().findField("templatedes").getValue();
							if('' == itemplatedes||null == itemplatedes){
			    				Ext.Msg.alert('提示',"请填写文档说明");
			    				return ;
			    			}
							//服务名称校验
			    			var iservename=updateformPane.getForm().findField("servename").getValue();
							if('' == iservename||null == iservename){
			    				Ext.Msg.alert('提示',"请填写服务名称");
			    				return ;
			    			}
							//适用系统校验
							var iplatform=updateformPane.getForm().findField("sysCombState1").getValue();
							if('' == iplatform||null == iplatform){
			    				Ext.Msg.alert('提示',"请选择适用系统");
			    				return ;
			    			}
							//权限校验
							var iauth=updateformPane.getForm().findField("authCombState1").getValue();
							if('' == iauth||null == iauth){
			    				Ext.Msg.alert('提示',"请选择权限");
			    				return ;
			    			}
							//服务一级分类校验
                            if ('' == iserveone||null == iserveone) {
                                Ext.MessageBox.alert("提示", "请选择服务一级分类!");
                                return;
                            }
                            //服务二级分类校验
                            if ('' == iservetwo||null == iservetwo) {
                                Ext.MessageBox.alert("提示", "请选择服务二级分类!");
                                return;
                            }
                            if (iservetwo == servetwo && iservetwo == servetwo && iauth == auth && iplatform == platform && iservename == servename && itemplatedes == templatedes && itemplatename == templatename) {
            					Ext.MessageBox.alert("提示", "没有修改内容！");
            					return;
            				}
							if (updateformPane.getForm().isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
//									 return;
								 updateformPane.getForm().submit({
									url: 'updateDocumnet.do',
									params:{
										  //itype:itype,
										  iid:iid,itemplatename:itemplatename,
						            	  itemplatedes:itemplatedes,
						            	  iservename:iservename,iplatform:iplatform,
						            	  //itooltype:itooltype,
						            	  iauth:iauth,
						            	  iserveone:iserveone,iservetwo:iservetwo
					            	  },
					            	  success : function(form, action) {
					            		  var success = Ext.JSON.decode(action.response.responseText).isok;
					      				if (success) {
					      					var store = Ext.getCmp("template_grid").store;
					      					store.reload({
					      						params : {
					      							start : 0,
					      							limit : 30,
					      						}
					      					});
					      					if(upadteWin!=null && upadteWin!=undefined && upadteWin!="")
					      					{
					      						upadteWin.close();
					      					}
					      					Ext.Msg.alert("提示", "保存成功！");
					      				} else {
					      					Ext.Msg.alert("提示", "保存失败！");
					      				}
					      			},
					      			failure : function(result, request) {
					      				secureFilterRs(result,"操作失败！");

					      			}
								});
					         }
						}
					},
					{
						xtype : 'button',
						text:'重置',
						cls : 'Common_Btn',
						handler:function(){
							this.up('form').getForm().reset();
			            }
			        }, {  
			        	xtype : 'button',
			            text : '关闭', 
			            cls : 'Common_Btn',
			            handler : function() {
			            	upadteWin.close();  
			            }  
			        }
				]
			}
		]
	});
	//编辑窗口
	upadteWin = Ext.create('Ext.window.Window', {
	    title: '编辑文档',
	    width: 800,
	    height: 450,
	    resizable:false,
//			    layout: 'fit',
	    modal:true,
	    closeAction: 'destroy',
	    items:  [updateformPane]
	}).show();
	upadteWin.on("beforeshow",function(self, eOpts){
		var form = Ext.getCmp("updateBtnId").up('form').getForm();
		form.reset();
	});
	upadteWin.on("destroy",function(self, eOpts){
		updateformPane.destroy();
	});

}


