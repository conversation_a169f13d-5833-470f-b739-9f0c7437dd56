var singleNumber = null;
Ext.onReady(function() {
	/* ****是否筛选未启用设备start**** */
	var enableFlag = false;
	/* ****是否筛选未启用设备end**** */
	//destroyRubbish();
	//Ext.tip.QuickTipManager.init();
	var publishAuditingSMWin;
	var auditing_form_sm;
	var itemsPerPage;
	//var filterFlag = false;
	var filterString = "readyAgentChosedList";
	var upldWin;
	var upLoadformPane = '';
	var importAgentIds = new Array();
	var editingChosedAgentIds = new Array();
	var editingChosedCpIds = new Array();
	var confirmFlag = false;
	var selModel1=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	Ext.define('sendEmailUserModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'id',
			type : 'string'
		}, {
			name : 'fullName',
			type : 'string'
		}]
	});

	/***AB角、所属网段查询***/

	var getsysAdminAStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'sysAdminA', 'sysAdminA' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdminAForWhiteListSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var getsysAdminBStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'sysAdminB', 'sysAdminB' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdminBForWhiteListSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var getBelongIpStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'belongIp', 'belongIp' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getBelongIpForWhiteListSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var sysAdminA = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'sysAdminA',
		displayField : 'sysAdminA',
		valueField : 'sysAdminA',
		labelWidth : 100,
		width :'20%',
		istore : getsysAdminAStore,
		labelAlign : 'right',
		forceSelection : false,
		//value:sysAdminForWhite,
		emptyText : "--请选择系统管理员A角--"
	});

	var sysAdminB = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'sysAdminB',
		displayField : 'sysAdminB',
		valueField : 'sysAdminB',
		labelWidth : 100,
		width :'20%',
		istore : getsysAdminBStore,
		labelAlign : 'right',
		forceSelection : false,
		//value:sysAdminForWhite,
		emptyText : "--请选择系统管理员B角--"
	});

	var belongIp = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'belongIp',
		displayField : 'belongIp',
		valueField : 'belongIp',
		labelWidth : 100,
		width :'20%',
		istore : getBelongIpStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择所属网段--"
	});

	/***单号***/

	//开关控制是否展示单号输入框
	if(equipSwitch == 'true'){
		singleNumber = new Ext.form.TextField({
			name: 'singleNumber',
			fieldLabel: '单号',
			emptyText: '',
			hidden: true,
			labelWidth : 65,
			labelAlign : 'right',
			value:suUser,
			width: "17%"
		});
	}

	var sendEmailUserStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		model : 'sendEmailUserModel',
		proxy : {
			type : 'ajax',
			url : 'getAllUserForSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var getHostNameStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'hostName', 'hostName' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getHostNameCombo.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getHostNameStore.on ('beforeload', function (store, options)
	{
		var new_params = {
			envtype : 1,//生产
			state : 0//状态
		};
		Ext.apply (getHostNameStore.proxy.extraParams, new_params);
	});
//	var hostName = Ext.create('Ext.form.field.ComboBox', {
//				store : getHostNameStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : '主机名称',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'hostName',
//				valueField : 'hostName',
//				labelWidth : 100,
//				labelAlign : 'right',
//				emptyText : "--请选择主机名称--",
//				triggerAction : "all"
//   });

	var hostName = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'hostName',
//			displayField : 'hostName',
//			valueField : 'hostName',
// 		fieldLabel : '计算机名',
		labelWidth : 100,
		width :'20%',
		istore : getHostNameStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择计算机名--"
	});


	var getIpStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'ip', 'ip' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getIpForAgentCombo.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getIpStore.on ('beforeload', function (store, options)
	{
		var new_params = {
			envtype : 1,//生产
			state : 0//状态
		};
		Ext.apply (getIpStore.proxy.extraParams, new_params);
	});
//	var agentIp = Ext.create('Ext.form.field.ComboBox', {
//				store : getIpStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : 'IP',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'ip',
//				valueField : 'ip',
//				labelWidth : 100,
//				labelAlign : 'right',
//				emptyText : "--请选择IP--",
//				triggerAction : "all"
//     });

	var agentIp = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'ip',
		// fieldLabel : 'IP',
		labelWidth : 100,
		width :'20%',
		istore : getIpStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择IP--"
	});

	var getsysAdminStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'sysAdmin', 'sysAdmin' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdminCombo.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getsysAdminStore.on ('beforeload', function (store, options)
	{
		var new_params = {
			envtype : 1,//生产
			state : 0//状态
		};
		Ext.apply (getsysAdminStore.proxy.extraParams, new_params);
	});
//	var sysAdmin1 = Ext.create('Ext.form.field.ComboBox', {
//				store : getsysAdminStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : '系统管理员',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'sysAdmin',
//				valueField : 'sysAdmin',
//				labelWidth : 100,
//				labelAlign : 'right',
//				emptyText : "--请选择系统管理员--",
//				triggerAction : "all"
//     });
	var sysAdmin1 = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'sysAdmin',
		fieldLabel : '系统管理员',
		labelWidth : 100,
		width :'25%',
		istore : getsysAdminStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择系统管理员--"
	});
	var getSystemNameStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'systemName', 'systemName' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSystemNameCombo.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getSystemNameStore.on ('beforeload', function (store, options)
	{
		var new_params = {
			envtype : 1,//生产
			state : 0//状态
		};
		Ext.apply (getSystemNameStore.proxy.extraParams, new_params);
	});
//	var system4 = Ext.create('Ext.form.field.ComboBox', {
//
//				store : getSystemNameStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : '信息系统名称',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'systemName',
//				valueField : 'systemName',
//				labelWidth : 100,
//				labelAlign : 'right',
//				emptyText : "--请选择信息系统名称--",
//				triggerAction : "all"
//     });
	var system4 = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'systemName',
		// fieldLabel : '信息系统名称',
		labelWidth : 100,
		width :'20%',
		istore : getSystemNameStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择信息系统名称--"
	});

	var centerStore = Ext.create('Ext.data.Store', {
		fields : [ 'centername' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'centerList.do',
			reader : {
				type : 'json',
				root : 'centerlist'
			}
		}
	});

//   	var centerCombo = Ext.create('Ext.form.field.ComboBox', {
//		//margin : '5',
//		store : centerStore,
//		fieldLabel : '所属区域',
//		queryMode : 'local',
//	    width : '25%',
//	    labelWidth : 100,
//	    labelAlign : 'right',
//		forceSelection : true, // 要求输入值必须在列表中存在
//		typeAhead : true, // 允许自动选择
//		displayField : 'centername',
//		valueField : 'centername',
//		triggerAction : "all",
//		emptyText : "--请选择所属区域--"
//	});
	var centerCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'centername',
		// fieldLabel : '所属区域',
		labelWidth : 100,
		width :'20%',
		istore : centerStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择所属区域--"
	});

	var osTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=OS_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

//	var osType_q = Ext.create('Ext.form.field.ComboBox', {
//		store : osTypeStore,
//		queryMode : 'local',
//		name : 'OS_type',
//		labelAlign : 'right',
//		width:'25%',
//		fieldLabel :'操作系统类型',
//		displayField : 'collectresult',
//		valueField : 'collectresult',
//		triggerAction : "all",
//		emptyText : "--请选择操作系统类型--"
//	});
	var osType_q = Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'OS_type',
		iname :'collectresult',
		// fieldLabel : '操作系统类型',
		labelWidth : 100,
		width :'20%',
		istore : osTypeStore,
		multiSelect :true,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择操作系统类型--"
	});

	var dbTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=db_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

//	var dbType_q = Ext.create('Ext.form.field.ComboBox', {
//		store : dbTypeStore,
//		queryMode : 'local',
//		fieldLabel :'数据库类型',
//		labelAlign : 'right',
//		width:'25%',
//		name : 'db_type',
//		displayField : 'collectresult',
//		valueField : 'collectresult',
//		triggerAction : "all",
//		emptyText : "--请选择数据库类型--"
//	});


	var dbType_q = Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'db_type',
		iname:'collectresult',
		// fieldLabel : '数据库类型',
		labelWidth : 100,
		width :'20%',
		istore : dbTypeStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择数据库类型--"
	});

	var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});

	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 100,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '25.5%',
		labelAlign : 'right',
		value:'0',
		listeners : {
			'change' : function(e) {
				hostName.setValue();
				agentIp.setValue();
				sysAdmin1.setValue();
				system4.setValue();
			}
		}
	});

	/**
	 * checkBox筛选数据为未启用设备（触发查询方法，传递一个参数，后台sql条件进行拼接）
	 * @type {Ext.form.field.Checkbox}
	 */
	var enableProperty = null;
	//开关控制是否展示筛选未启用设备checkBox
	if(equipSwitch){
		if(equipSwitch == 'true'){
			enableProperty = Ext.create('Ext.form.field.Checkbox', {
				boxLabel: '筛选未启用设备',
				inputValue: 1,
				width: 120,
				margin: '10 0 0 10',
				listeners: { //监听
					change:function(el,checked){
						//alert(singleNumber.getValue().trim());
						if(!checked){
							enableFlag = false;
						}
						if(checked){
							enableFlag = true;
						}
						//通过未启用设备checkbox控制单号的显示、隐藏
						if(checked){//勾选状态
							singleNumber.show();
						}else{//未勾选状态，清空单号值并且隐藏
							singleNumber.hide();
							singleNumber.setValue('');
						}
						agentGridForSPDB.ipage.moveFirst();
					}
				}
			});
		}
	}

	//agentStatusCb.setValue("0");//默认查询正常的
	var middlewareTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=middleware_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

//	var middleType = Ext.create('Ext.form.field.ComboBox', {
//		store : middlewareTypeStore,
//		queryMode : 'local',
//		fieldLabel :'中间件类型',
//		labelAlign : 'right',
//		width:'25%',
//		name : 'middleware_type',
//		displayField : 'collectresult',
//		valueField : 'collectresult',
//		triggerAction : "all",
//		emptyText : "--请选择中间件类型--"
//	});

	var middleType = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'collectresult',
		// fieldLabel : '中间件类型',
		labelWidth : 100,
		width :'20%',
		istore : middlewareTypeStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择中间件类型--"
	});

	function checkFile(fileName){
		var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
		if(!file_reg.test(fileName)){
			Ext.Msg.alert('提示','文件类型错误,请选择Excel文件');
			//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)');
			return false;
		}
		return true;
	}
	function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}

		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
			width:370,
			height:100,
			frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 80,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: [
				{
					id:'upldBtnIdAudi',
					text: '导入Agent文件',
					handler: function() {
						var form = this.up('form').getForm();
						var upfile=form.findField("file").getValue();
						if(upfile==''){
							Ext.Msg.alert('提示',"请选择文件...");
							return ;
						}

						var hdtmpFilNam=form.findField("file").getValue();
						if(!checkFile(hdtmpFilNam)){
							form.findField("file").setRawValue('');
							return;
						}

						if (form.isValid()) {
							//Ext.MessageBox.wait("数据处理中...", "进度条");
							form.submit({
								url: 'importAgentForStart.do',
								waitMsg:'正在提交数据，请稍候...',
								waitTitle:'提示',
								params:{
									envType:1,
									scriptType:scriptTypeForTaskAudi,
									scriptPlatmFrom:scriptPlatmFrom,
									state:agentStatusCb.getValue(),
									selectedDeviceType:chosedAgentFlagArray[0],
									//isGreatChange:listComBox2.getValue()==1?true:false,
									inumber:butterflyVerison2.getValue(),
									enableFlag:enableFlag
								},
								success: function(form, action) {

									//判断导入是否全部为未启用设备返回状态
									var equipSuccess = Ext.decode(action.response.responseText).successEquip;
									if(equipSuccess == 'false'){
										//返回提示
										var equipMsg = Ext.decode(action.response.responseText).message;
										Ext.MessageBox.alert("提示", equipMsg);
										return;
									}

									/*******如下为原有代码******/

									var msg = Ext.decode(action.response.responseXML.activeElement.innerHTML).message;
									var status = Ext.decode(action.response.responseText).status;
									var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
									var matchCpIds = Ext.decode(action.response.responseText).matchCpIds;
									//filterFlag = true;
									filterString = "inAgentChosedList";
									if(status==1) {
										if(matchAgentIds && matchAgentIds.length>0) {

											//msg = msg.replace(/\r\n/gi, '<br/>').replace(/\n/gi, '<br/>');
											var msgTextArea = Ext.create('Ext.form.field.TextArea', {
												name: 'pubdesc',
												fieldLabel: '提示',
												//emptyText: '',
												labelWidth: 40,
												labelAlign : 'right',
												// margin : '10 0 0 0',
												//maxLength: 255,
												height: 140,
												columnWidth:.98,
												readOnly:true,
												autoScroll: true
											});
											msgTextArea.setValue(msg);
											var messagePa = Ext.create('Ext.form.Panel', {
												width: 580,
												height:200,
												layout : 'anchor',
												bodyCls : 'x-docked-noborder-top',
												buttonAlign : 'center',
												border : false,
												items: [{
													//	    	layout:'form',
													anchor:'98%',
													padding : '5 0 5 0',
													border : false,
													items: [ {
														layout:'column',
														border : false,
														items:[msgTextArea]
													}]
												}],
												bbar: ['->',{
													xtype : 'button',
													text : '确定',
													cls : 'Common_Btn',
													handler :function() {
														this.up("window").close();
														Ext.Msg.alert('提示', "导入成功！");
														search_form_center.getForm().reset();
														editingChosedAgentIds  = Array.from(new Set(editingChosedAgentIds.concat(matchAgentIds)));
														editingChosedCpIds = Array.from(new Set(editingChosedCpIds.concat(matchCpIds)));
														importAgentIds = Array.from(new Set(importAgentIds.concat(matchAgentIds)));
														agentGridForSPDB.ipage.moveFirst();
													}
												},{
													xtype : 'button',
													text : '取消',
													cls : 'Common_Btn',
													handler :function() {
														this.up("window").close();
													}
												},'->']
											});

											var messageWin = Ext.create('Ext.window.Window', {
												title: '请确认',
												width: 640,
												height: 300,
												modal:true,
												resizable: false,
												closeAction: 'destroy',
												items:  [messagePa]
											});
											messageWin.show();
										} else {
											// Ext.Msg.alert('提示-没有匹配项', msg);
											var msgTextArea2 = Ext.create('Ext.form.field.TextArea', {
												name: 'pubdesc',
												fieldLabel: '提示',
												//emptyText: '',
												labelWidth: 40,
												labelAlign : 'right',
												// margin : '10 0 0 0',
												//maxLength: 255,
												height: 140,
												columnWidth:.98,
												readOnly:true,
												autoScroll: true
											});
											msgTextArea2.setValue(msg);
											var messagePa2 = Ext.create('Ext.form.Panel', {
												width: 580,
												height:200,
												layout : 'anchor',
												bodyCls : 'x-docked-noborder-top',
												buttonAlign : 'center',
												border : false,
												items: [{
													//	    	layout:'form',
													anchor:'98%',
													padding : '5 0 5 0',
													border : false,
													items: [ {
														layout:'column',
														border : false,
														items:[msgTextArea2]
													}]
												}],
												bbar: ['->',{
													xtype : 'button',
													text : '确定',
													cls : 'Common_Btn',
													handler :function() {
														this.up("window").close();
													}
												},'->']
											});

											var messageWin2 = Ext.create('Ext.window.Window', {
												title: '请确认',
												width: 640,
												height: 300,
												modal:true,
												resizable: false,
												closeAction: 'destroy',
												items:  [messagePa2]
											});
											messageWin2.show();
										}

									} else {
										Ext.Msg.alert('提示', "导入成功！");
										search_form_center.getForm().reset();
										editingChosedAgentIds= Array.from(new Set(editingChosedAgentIds.concat(matchAgentIds)));
										editingChosedCpIds = Array.from(new Set(editingChosedCpIds.concat(matchCpIds)));
										importAgentIds = Array.from(new Set(importAgentIds.concat(matchAgentIds)));
										agentGridForSPDB.ipage.moveFirst();
									}

									upldWin.close();
									return;
								},
								failure: function(form, action) {
									secureFilterRsFrom(form, action);
								}
							});
						}
					}
				}, {
					text: '下载模板',
					handler: function() {
						window.location.href = 'downloadAgentTemplate.do?fileName=AgentImportMould_new.xls';
					}
				}
			]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
			title: '设备信息批量导入',
			width: 400,
			height: 200,
			modal:true,
			resizable: false,
			closeAction: 'destroy',
			items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});

		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}

	var search_form_center = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		iselect :false,
		bodyCls : 'x-docked-noborder-top',
		iqueryFun :function(){
			agentGridForSPDB.ipage.moveFirst();
		},
		border: false,
		dockedItems : [
			{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [ hostName,agentIp,sysAdminA,sysAdminB,centerCombo]
			},
			{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [system4,middleType,osType_q,dbType_q,belongIp ]
			},{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [agentStatusCb,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler :function() {
						agentGridForSPDB.ipage.moveFirst();
					}
				},
					{
						xtype : 'button',
						text : '重置',
						cls : 'Common_Btn',
						handler : function() {
							search_form_center.getForm().reset();
							//chosedAgentIds.splice(0,chosedAgentIds.length)
							//filterString = "readyAgentChosedList";
							pubDesc_sm.setValue('');
							agentListStore.load();
						}
					},{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '导入',
						handler : importExcel
					},
					/*{
						xtype : 'button',
						cls : 'Common_Btn',
						hidden:!batchQuerySwitch,
						text : '批量查询',
						handler : batchQuery
					},*/
					enableProperty
					,
					singleNumber
				]
			}
//	    		{
//	    			xtype: 'toolbar',
//	    			border :false,
//	    			dock :'top',
//	    			items:[{
//		    			xtype : 'toolbar',
//						border : false,
//						dock : 'top',
//						items: [
//							{
//								xtype : 'button',
//								text : '查询',
//								cls : 'Common_Btn',
//								handler :function() {
//									agentGridForSPDB.ipage.moveFirst();
//								}
//							},
//							{
//								xtype : 'button',
//								text : '重置',
//								cls : 'Common_Btn',
//								handler : function() {
//									search_form_center.getForm().reset();
//								}
//							},{
//								xtype : 'button',
//								cls : 'Common_Btn',
//								text : '导入',
//								handler : importExcel
//							}
//						]
//		    		}]
//	    		}
		]
	});

	var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
		name: 'pubdesc',
		fieldLabel: '计算机名',
		labelAlign:'right',
		emptyText: '请输入计算机名，多个值请换行',
		labelWidth: 93,
		margin : '10 0 0 0',
		maxLength: 500,
		height: 350,
		columnWidth:.98,
		autoScroll: true
	});

	//批量添加计算机名查询弹窗
	function batchQuery() {
		if (!publishAuditingSMWin) {
			publishAuditingSMWin = Ext.create('widget.window', {
				title: '批量查询',
				closable: true,
				closeAction: 'hide',
				modal: true,
				width: 600,
				height: 500,
				layout: {
					type: 'border',
					padding: 5
				},
				items: [auditing_form_sm],
				dockedItems: [{
					xtype: 'toolbar',
					//baseCls:'customize_gray_back',
					dock: 'bottom',
					layout: {pack: 'center'},
					items: [{
						xtype: "button",
						cls: 'Common_Btn',
						text: "确定",
						handler: function(){
							this.up("window").close();
						}
					}, {
						xtype: "button",
						cls: 'Common_Btn',
						text: "取消",
						handler: function () {
							pubDesc_sm.setValue('');
							this.up("window").close();
						}
					}]
				}]
			});
		}
		publishAuditingSMWin.show();
		//pubDesc_sm.setValue('');
	}

	auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'center',
		layout : 'anchor',
		bodyCls : 'x-docked-noborder-top',
		buttonAlign : 'center',
		border : false,
		items: [{
//	    	layout:'form',
			anchor:'98%',
			padding : '5 0 5 0',
			border : false,
			items: [{
				layout:'column',
				border : false,
				items:[pubDesc_sm]
			}]
		}]
	});

	itemsPerPage = 50;
	agentListStore = Ext.create ('Ext.data.Store',
		{
			autoLoad : true,
			autoDestroy : true,
			pageSize: itemsPerPage,
			model : 'page.dubbo.scriptService.spdb.agent.agentModel',
			proxy :
				{
					type : 'ajax',
					actionMethods: {
						create : 'POST',
						read   : 'POST', // by default GET
						update : 'POST',
						destroy: 'POST'
					},
					url : 'getAllAgentListForSPDB.do',
					reader :
						{
							type : 'json',
							root : 'dataList',
							totalProperty: 'total'
						}
				}
		});
	agentListStore.on ('beforeload', function (store, options)
	{
		if(checkInumber){// 只有任务申请需要校验重大变更的单号
			var new_params = {
				hostName: hostName.rawValue,
				agentIp: agentIp.rawValue,
				agentIds : /**filterFlag == false?null:**/JSON.stringify(chosedAgentIds),
				importAgentIds :JSON.stringify(importAgentIds), //导入的agent id 数组
				from : filterString, //内层增加服务器窗口不显示已增加的服务器(导出除外)
				sysAdmin:sysAdmin1.rawValue,
				centerName:centerCombo.getValue(),
				systemInfo:system4.rawValue,
				middlewareType:middleType.value,
				osType: osType_q.getValue().length !=0 ? osType_q.getValue(): scriptPlatmFrom, //osType_q.getValue(), 那操作系统类型是不是也需要限制？？？？？
				flag :1,
				agentState : agentStatusCb.getValue(),
				dbType : dbType_q.getValue(),
				//isGreatChange:listComBox2.getValue()==1?true:false, //是否重大变更操作类型
				inumber:butterflyVerison2.getValue(),//单号
				enableFlag:enableFlag,//筛选未启用设备
				userA : sysAdminA.getValue(),
				userB : sysAdminB.getValue(),
				belongIp : belongIp.getValue(),
				batchComputerName:pubDesc_sm.getValue()//批量查询
			};
		}else{
			var new_params = {
				hostName: hostName.rawValue,
				agentIp: agentIp.rawValue,
				agentIds : /**filterFlag == false?null:**/JSON.stringify(chosedAgentIds),
				importAgentIds :JSON.stringify(importAgentIds), //导入的agent id 数组
				from : filterString, //内层增加服务器窗口不显示已增加的服务器(导出除外)
				sysAdmin:sysAdmin1.rawValue,
				centerName:centerCombo.getValue(),
				systemInfo:system4.rawValue,
				middlewareType:middleType.value,
				osType: osType_q.getValue().length !=0 ? osType_q.getValue(): scriptPlatmFrom, //osType_q.getValue(), 那操作系统类型是不是也需要限制？？？？？
				flag :1,
				agentState : agentStatusCb.getValue(),
				dbType : dbType_q.getValue(),
				//isGreatChange:false,//是否重大变更操作类型
				inumber:-1,//单号
				enableFlag:enableFlag,//筛选未启用设备
				userA : sysAdminA.getValue(),
				userB : sysAdminB.getValue(),
				belongIp : belongIp.getValue(),
				batchComputerName:pubDesc_sm.getValue()//批量查询
			};
		}

		Ext.apply (agentListStore.proxy.extraParams, new_params);
	});



//	var comboPage = Ext.create('Ext.form.ComboBox', {
//		name : 'pagesize',
//		hiddenName : 'pagesize',
//		store : new Ext.data.ArrayStore({
//			fields : [ 'text', 'value' ],
//			data : [ [ '50', 50 ], [ '100', 100 ], [ '150', 150 ] ,[ '200', 200 ]]
//		}),
//		valueField : 'value',
//		displayField : 'text',
//		emptyText : 50,

//		width : 50
//	});

	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store: agentListStore,
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		dock: 'bottom',
		displayInfo: true
//	    items : [ '-', comboPage, '->'],
//	    emptyMsg:'找不到任何记录'
	});

//	  // 添加下拉显示条数菜单选中事件
//	comboPage.on("select", function(comboBox) {
//		var pagingToolbar = Ext.getCmp('pagingbar2');
//		pagingToolbar.pageSize = parseInt(comboBox.getValue());
//		itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
//		agentListStore.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
//		//search();
//		//agentListStore.moveFirst();
//		agentGridForSPDB.ipage.moveFirst();
//	});
	var agentGridForSPDB = Ext.create('Ext.ux.ideal.grid.Panel', {
		region: 'center',
		autoScroll: true,
		store:agentListStore,
		selModel : selModel1,
		plugins: [ cellEditing ],
//		    bbar : pageBar,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		border:false,
		columnLines : true,
		columns : agentColumnsForSPDB,
		listeners: {
			select: function( e, record, index, eOpts ){
				if(editingChosedAgentIds.indexOf(record.get('iid'))==-1) {
					editingChosedAgentIds.push(record.get('iid'));
					editingChosedCpIds.push(record.get('cpid'));
				}
				var platTemp;

				var osTypeV = record.get('osTypeAgentinfo'); // 当前选择行的AgentInfo里的 系统类型
				if(scriptTypeForTaskAudi != 'py'){// 当脚本类型不为Python时，不允许同时选择windows和非Windows设备
					if(osTypeV.toUpperCase().indexOf('WIN') != -1 && chosedAgentFlagArray.indexOf('WIN') == -1){
						chosedAgentFlagArray.push('WIN');
						platTemp ='WIN';
					}
					if(osTypeV.toUpperCase().indexOf('WIN') == -1  && chosedAgentFlagArray.indexOf('非WIN') == -1){
						chosedAgentFlagArray.push('非WIN');
						platTemp ='非WIN';
					}

					if(chosedAgentFlagArray.length ==2){
						Ext.Msg.alert('提示', "非python脚本不允许选择Windows和非Windows的设备!");
						chosedAgentFlagArray.remove(platTemp);
						agentGridForSPDB.getSelectionModel().deselect(record,false);
						return;
					}
				}
			},
			deselect: function( e, record, index, eOpts ){
				var selDatas = agentGridForSPDB.getSelectionModel().getSelection();
				if(editingChosedAgentIds.indexOf(record.get('iid'))>-1) {
					editingChosedAgentIds.remove(record.get('iid'));
					editingChosedCpIds.remove(record.get('cpid'));
				}
				if(selDatas.length == 0 && editingChosedAgentIds.length ==0){
					if(chosedAgentFlagArray.indexOf('WIN') != -1){
						chosedAgentFlagArray.remove('WIN');
					}else if(chosedAgentFlagArray.indexOf('非WIN') != -1){
						chosedAgentFlagArray.remove('非WIN');
					}
				}
			}
		}
	});
	agentListStore.addListener('load', function(me, records, successful, eOpts) {
		// 选择后构建出的agentID数组
		if (editingChosedAgentIds.length>0) {
//        	if(filterFlag){//导入成功后才过滤已导入的
//        		agentListStore.filterBy(function(record) {
//		       	  //导入的数组元素中含有当前记录 那么不过滤
//	              return   chosedAgentIds.indexOf(record.get('iid')) !=-1
//	            });
//        	}
			var chosedRecords = []; //存放选中记录
			$.each(records,
				function(index, record) {
					if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
						chosedRecords.push(record);
					}
				});
			agentGridForSPDB.getSelectionModel().select(chosedRecords, false, false); //选中记录
		}

	});
	chosedAgentWinForSPDB = Ext.create('Ext.window.Window', {
		title : '增加服务器',
		autoScroll : true,
		modal : true,
		resizable : false,
		closeAction : 'hide',
		layout: 'border',
		draggable: false,
		width : contentPanel.getWidth()-110,
		height : contentPanel.getHeight(),
		items:[search_form_center, agentGridForSPDB],
		dockedItems: [{
			xtype: 'toolbar',
			dock:'bottom',
			layout: {pack: 'center'},
			items: [{
				xtype: "button",
				text: "确定",
				cls:'Common_Btn',
				margin:'6',
				handler: function () {
					var bzflag = true;
					//判断选中的设备是否存在未启用设备，交单单号等
					if(equipSwitch && equipSwitch == 'true') {
						//获取当前选中的设备的iid
						/*var jsonArray = [];
                        agentListStore.each(function(item) {
                            jsonArray.push(item.data.iid);
                        }, this);
                        var selectIIDs = jsonArray.join(",");*/

						var records = agentGridForSPDB.getSelectionModel().getSelection();
						var jsonArray = [];
						Ext.each(records, function(item) {// 遍历
							jsonArray.push(item.data.iid);
						});
						var selectIIDs = jsonArray.join(",");
						//校验
						Ext.Ajax.request({
							url : 'checkEquipStatusAndNumber.do',
							method : 'POST',
							timeout : 1000000,
							params : {
								selectIIDs: selectIIDs,
								singleNumber:null == singleNumber ? "" : singleNumber.getValue().trim()
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								if (!success) {
									bzflag = false;
									Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
									return;
								}
								if(success){
									//chosedAgentIds = editingChosedAgentIds;
									chosedAgentIds  = Array.from(new Set(chosedAgentIds.concat(editingChosedAgentIds)));
									chosedCpIds  = Array.from(new Set(chosedCpIds.concat(editingChosedCpIds)));
									agent_store_chosed.load();
									if( typeof  agent_store_chosedForSee != "undefined"){
										agent_store_chosedForSee.load();
									}
									//filterFlag = false;
									filterString = "notinAgentChosedList";
									confirmFlag = true;
									agentGridForSPDB.ipage.moveFirst();
									chosedAgentWinForSPDB.close();
								}

							},
							failure : function(result, request) {
								Ext.Msg.alert('提示', '校验失败！');
							}
						});

					}else {
						//chosedAgentIds = editingChosedAgentIds;
						chosedAgentIds  = Array.from(new Set(chosedAgentIds.concat(editingChosedAgentIds)));
						chosedCpIds  = Array.from(new Set(chosedCpIds.concat(editingChosedCpIds)));
						agent_store_chosed.load();
						if( typeof  agent_store_chosedForSee != "undefined"){
							agent_store_chosedForSee.load();
						}
						//filterFlag = false;
						filterString = "notinAgentChosedList";
						confirmFlag = true;
						agentGridForSPDB.ipage.moveFirst();
						chosedAgentWinForSPDB.close();
					}
				}
			}/**,{
			  			xtype: "button",
			  			text: "关闭", 
			  			cls:'Common_Btn',
			  			margin:'6',
			  			handler: function () {
			  				this.up("window").close();
			  				//filterFlag = false;
			  			    filterString = "readyAgentChosedList";
			  			}
			  		 }**/ ]
		}],
		listeners: {
			close:function(){
				if(filterString == "inAgentChosedList" && !confirmFlag){
					filterString = "readyAgentChosedList";
				}
				editingChosedAgentIds =[];
				editingChosedCpIds =[];
				importAgentIds.splice(0,importAgentIds.length);
				confirmFlag = false;
			}
		}
	});


	// sendEmailUser = Ext.create('Ext.form.MultiSelect', {
	sendEmailUser = Ext.create('Ext.form.ComboBox', {
		fieldLabel : '收件人',
		emptyText : '--请选择收件人--',
		labelWidth : 65,
		labelAlign : 'right',
		editable : true,
		width: "16.4%",
		multiSelect: true,
		store : sendEmailUserStore,
		hidden:!taskApplyForSPDBSwitch,
		displayField : 'fullName',
		valueField : 'id',
		triggerAction : 'all',
		// tpl: comboTPL,
		queryMode: 'local',
		listeners : {
			/*expand: function(combo) {
				var node;
				Ext.each(combo.picker.getSelectionModel().selected.items, function(rec) {
					rec.data.checked="true";
					node = combo.getPicker().getNode(rec);
					Ext.get(node).down('input').dom.checked = true;
				});
			},*/
			/*select : function(combo, records, eOpts) {
				var node;

				Ext.each(records, function(rec) {
					rec.data.checked="true";
					node = combo.getPicker().getNode(rec);
					Ext.get(node).down('input').dom.checked = true;
				});
				if (records) {
					// sendEmailUserRecord.push(records.data);
					/!*for (var i = 0; i < records.length; i++) {
						sendEmailUserRecord.push(records[i]);
					}*!/
					sendEmailUserRecord = [];
					for (let i = 0; i < records.length; i++) {
						sendEmailUserRecord.push(records[i].data);
					}
				}
			},*/
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll  ) {
					var sendEmailUserV  =  e.query;
					sendEmailUserV = sendEmailUserV.replace(/，/ig,",");
//				                	var curvalue =visiableUserV .substring(visiableUserV .lastIndexOf(", ") + 1, visiableUserV .length);
					var sendEmailUserArray = sendEmailUserV.split(",");
					if(sendEmailUserArray[sendEmailUserArray.length-1] ==""){
						sendEmailUserArray.pop();
					}
//				                    var value = Ext.util.Format.trim(curvalue);
					var sendEmailUserArray2 =[];
					combo.store.filterBy(function(record, id) {
						var text = record.get(sendEmailUser.displayField);
//				                        visiableUserArray.forEach((tempvisiableUser, index, array) => {
//										     if((text.toLowerCase().indexOf(Ext.util.Format.trim(tempvisiableUser).toLowerCase()) != -1)){
//					                        	visiableUserArray2.push(text);
//					                        	return (text.toLowerCase().indexOf(Ext.util.Format.trim(tempvisiableUser).toLowerCase()) != -1);
//					                        }
//										});
						for (var tempsendEmailUser of sendEmailUserArray) {
							if((text.toLowerCase().indexOf(Ext.util.Format.trim(tempsendEmailUser).toLowerCase()) != -1)){
								sendEmailUserArray2.push(text);
								return (text.toLowerCase().indexOf(Ext.util.Format.trim(tempsendEmailUser).toLowerCase()) != -1);
							}
						}
					});
					/*combo.getStore().on("load", function(s, r, o) {
						combo.setValue(sendEmailUserArray2);// 第一个值
					});*/
					combo.expand();
					return false;
				}else{
					sendEmailUserStore.reload();
					combo.expand();
				}
			}
		}
	});
});