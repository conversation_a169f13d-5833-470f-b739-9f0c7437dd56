Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();

	var mainPanel;
	var bussId;
	var bussTypeId;
	var iid;
	var serviceName;
	
    var bussName;
    var bussTypeName;
    var funcDescText;
    var creatorFullName;
	
	var ilter_bussId = filter_bussIdForAllJobs;
	var ilter_bussTypeId = filter_bussTypeIdForAllJobs;
	var ilter_scriptName = filter_scriptNameForAllJobs;
	var ilter_serviceName = filter_serviceNameForAllJobs;
	var ilter_scriptType = filter_scriptTypeForAllJobs;

	Ext.tip.QuickTipManager.init();

	var mainP = Ext.create('Ext.panel.Panel', {
		border : false,
		region : 'center',
		height : contentPanel.getHeight(),
		loader : {},
		buttonAlign: 'center',
        buttons: [ /*{
				text : '测试',
				height : 30,
				cls : 'Common_Btn',
				handler : TEST
			},{
				text : '版本回退',
				height : 30,
				cls : 'Common_Btn',
				handler : versionRollBack
			},  */{
	            text: '基本信息',
	            height : 30,
	            cls: 'Common_Btn',
	            handler: function() {
	            	Ext.Ajax.request({
	                    url: 'scriptService/queryOneService.do',
	                    params: {
	                        iid: iid
	                    },
	                    method: 'POST',
	                    async: false,
	                    success: function(response, options) {
	                        var data = Ext.decode(response.responseText);
	                        if (data.success) {
	                            bussId = parseInt(data.sysName);
	                            bussTypeId = parseInt(data.bussName);
	                            bussName = data.bussN;
	                            bussTypeName = data.bussT;
	                            funcDescText = data.funcDesc;
	                            serviceName = data.serviceName;
	                            creatorFullName = data.fullName;
	                        }
	                    },
	                    failure: function(result, request) {}
	                });
	            	Ext.create('Ext.window.Window', {
	    	            title: '基本信息',
	    	            autoScroll: true,
	    	            modal: true,
	    	            closeAction: 'destroy',
	    	            buttonAlign: 'center',
	    	            draggable: true,
	    	            resizable: false,
	    	            width: 500,
	    	            height: 328,
	    	            loader: {
	    	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
	    	            	params: {
	    	            		creatorFullName: creatorFullName,
	    		                bussName: bussName,
	    		                bussTypeName:bussTypeName,
	    		                funcDescText: funcDescText,
	    		                serviceName:serviceName
	    	            	},
	    	            	autoLoad: true
	    	            },
	    	            dockedItems: [{
	    	                xtype: 'toolbar',
	    	                border: false,
	    	                dock: 'bottom',
	    	                margin: '0 0 5 0',
	    	                layout: {pack: 'center'},
	    	                items: [{
	    	                    xtype: 'button',
	    	                    text: '关闭',
	    	                    cls: 'Common_Btn',
	    	                    handler: function() {
	    	                        this.up("window").close();
	    	                    }
	    	                }]
	    	            }]
	    	        }).show();
	            }
	        }, {
				text : '返回',
				height : 30,
				cls : 'Common_Btn',
				handler : function() {
					destroyRubbish(); // 销毁本页垃圾
					contentPanel.getLoader().load({
						url : 'viewAllJobs.do',
						params: {
							'filter_bussId': ilter_bussId,
							'filter_bussTypeId': ilter_bussTypeId,
							'filter_scriptName': ilter_scriptName,
							'filter_serviceName': ilter_serviceName,
							'filter_scriptType': ilter_scriptType
						},
						scripts : true
					});
				}
			} ]
		
	});
	
	Ext.define('versionModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'long'
		}, {
			name : 'bussId',
			type : 'long'
		}, {
			name : 'bussTypeId',
			type : 'long'
		}, {
			name : 'createTime',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		}, {
			name : 'onlyVersion',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		} ]
	});

	var versionStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'versionModel',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceVersionList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	versionStore.on('beforeload', function(store, options) {
		var new_params = {
			serviceId : serviceIdForAllJobs,
			flag : flagForAllJobs
		};

		Ext.apply(versionStore.proxy.extraParams, new_params);
	});

	versionStore.on('load', function(store, options) {
		versionGrid.getSelectionModel().select(0, true);
	});

	var versionColumns = [ {
		text : '序号',
		xtype : 'rownumberer',
		width : 40
	}, {
		text : '服务主键',
		dataIndex : 'iid',
		width : 40,
		hidden : true
	}, {
		text : '版本',
		dataIndex : 'onlyVersion',
		flex : 1
	}, {
		text : '创建时间',
		dataIndex : 'createTime',
		width : 150
	} ];

	var versionGrid = Ext.create('Ext.grid.Panel', {
		width : 320,
		height : contentPanel.getHeight() - 146,
		margin: '5 5 0 0',
		store : versionStore,
		border : true,
		columnLines : true,
		columns : versionColumns,
		region : 'west',
		listeners : {
			select : function(me, record, index, eOpts) {
				iid = record.get('iid');
				serviceName = record.get('serviceName');
				mainP.getLoader().load({
					url : 'flowCustomizedInitScriptServiceGFSSVIEWVERSIONFORALLJOBS.do',
//					url : 'jobVersionViewer.do',
					params : {
						iid : iid,
						serviceName : record.get('serviceName'),
						actionType : 'view',
						bussId : record.get('bussId'),
						bussTypeId : record.get('bussTypeId'),
						flag : 0
					},
					scripts : true
				});
			}
		}
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptViewVersionForAllJobs_area",
		layout : {
			type : 'border'
		},
		border : false,
		height : contentPanel.getHeight(),
		items : [ versionGrid, mainP ]
	});

	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - 40);
		mainPanel.setWidth(contentPanel.getWidth());
		mainP.setWidth(contentPanel.getWidth() / 2);
	});

});