<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<% 
	long serviceId_allScript=Long.parseLong(request.getParameter("serviceId"));
	boolean allScriptSwitch = Environment.getInstance().getAllScriptSwitch();
	boolean templateSwich = Environment.getInstance().getTempUploadTabSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
	boolean checkBeforeExecSwitch = Environment.getInstance().getYCScriptCheckBeforeExec();
	//工作目录
	boolean jlnsScriptWorkDirSwitch = Environment.getInstance().getJlnxScriptWorkDir();
	// 福建农信CI
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
%>
<html>
<head>
<script type="text/javascript">
	var labelEdit="<%=request.getAttribute("labels")%>";
	var serviceIdForAllScripts="<%=serviceId_allScript%>";
	var checkRadioForAllScripts = 0;
	var flagForAllScripts = '<%=request.getParameter("flag")%>';
	var statusForAllScripts = '<%=request.getParameter("status")%>';
	var allScriptSwitch = <%=allScriptSwitch%>;
	var scriptuuid = '<%=request.getParameter("uuid")%>';
	//全部脚本跳转过来的参数，返回时使用  start
	var queryServicesName = '<%=request.getParameter("servicesName")==null?"":request.getParameter("servicesName")%>';
	var queryKeywordsSerach = '<%=request.getParameter("keywordsSerach")==null?"":request.getParameter("keywordsSerach")%>';
	var queryScriptName = '<%=request.getParameter("scriptName")==null?"":request.getParameter("scriptName")%>';
	var queryOneLevelType = '<%=request.getParameter("oneLevelType")==null?"":request.getParameter("oneLevelType")%>';
	var querySecondLevelType ='<%=request.getParameter("secondLevelType")==null?"":request.getParameter("secondLevelType")%>';
	var threeTypeId ='<%=request.getParameter("threeTypeId")==null?"":request.getParameter("threeTypeId")%>';
	var queryScriptType ='<%=request.getParameter("scriptType")==null?"":request.getParameter("scriptType")%>';
	var queryScriptState ='<%=request.getParameter("scriptState")==null?"":request.getParameter("scriptState")%>';
	var queryCreateUser ='<%=request.getParameter("createUser")==null?"":request.getParameter("createUser")%>';
	var queryScriptDir ='<%=request.getParameter("scriptDir")==null?"[]":request.getParameter("scriptDir")%>';
	//全部脚本跳转过来的参数，返回时使用  end

	//模板tab页展示开关
	var templateSwitch = <%=templateSwich%>
	var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>
	var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
	var checkBeforeExecSwitch = <%=checkBeforeExecSwitch%>;
	var scriptWorkDirSwitch = <%=jlnsScriptWorkDirSwitch%>
    var fjnxCISwitch = <%=fjnxCISwitch%>;
</script>

<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/scriptViewVersionForAllScripts.js">
	var projectFlagForAll = '<%=request.getAttribute("projectFlag")==null?"0":request.getAttribute("projectFlag")%>';	
</script>
<link type="text/css" rel="stylesheet" href="<%=request.getContextPath()%>/css/mergely.css"  />
<script type="text/javascript" src="<%=request.getContextPath()%>/js/mergely.js" ></script>
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin_v2/css/input_style.css">
</head>
<body>
	<div id="gridBasicScriptForAllScripts_view_area"></div>
</body>
</html>