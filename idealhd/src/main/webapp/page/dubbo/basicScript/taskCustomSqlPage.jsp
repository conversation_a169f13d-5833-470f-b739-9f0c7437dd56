<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<% 
	long iid=Long.parseLong(request.getParameter("iid"));
	boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	boolean showConfigSwitch = Environment.getInstance().getScriptShowConfigSwitch();
	long customId=null!=request.getParameter("customId")?Long.parseLong(request.getParameter("customId")):0L;
	boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
	//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
	boolean firstUserSelected = Environment.getInstance().getSelectedFirstUserSwitch();
%>
<html>
<head>
<script type="text/javascript">
	var iidForTaskAudi="<%=iid%>";
	var customIid ="<%=customId%>";
	var showConfigSwitch = <%=showConfigSwitch%>;
	var scriptLevelForTaskAudi='<%=request.getParameter("scriptLevel")%>';
	var serviceNameForTaskAudi='<%=request.getParameter("serviceName")%>';
	var customTaskName = '<%=request.getAttribute("customName")%>';
	var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;
	var checkRadioForTaskAudi = 0;
	var eachNumForA = <%=request.getAttribute("eachNum")%>;
	var loginUser = '<%=request.getAttribute("loginUser")%>';
	var ssTimerTaskSwitch = <%=request.getAttribute("ssTimerTaskSwitch")%>;
	var execUserSwitch = <%=execUserSwitch%>;
	var CMDBFlag = <%=request.getAttribute("CMDBflag")%>;
	var cmdbFlag;
	if(CMDBFlag){
		cmdbFlag = false;
	}else{
		cmdbFlag = true;
	}
	//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
	var firstUserSelected = <%=firstUserSelected%>;
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskCustomSqlPage.js"></script>
</head>
<body>
	<input type="hidden" id="taskCustomSqlPageExecUserNameText" />
	<div id="taskCustomSqlPage_area" style="width: 100%; height: 25%;"></div>
</body>
</html>
