var language;
Ext.onReady(function () {
    var variableTabLoad = true;
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    var groupName;
    var selectedSysRecords = [];
    var selectedAgentRecords = new Array();
    var sysid;
    var groupName;
    var secUUID = "";
    var auditing_form_sm;
    var agent_panel;
    var project_panelScript;
    var project_panel;
    var oldserviceName;
    var uploadProcessWin;
    var warnningGrid;
    var warnningGrid1;
    var baseInfoOfScriptWin;
    var chooseTestAgentWin;
    var publishAuditingSMWin;
    var attachmentIds = [];
    var tempmentIds = [];
    var warnningWin;
    var warnningWin1;
    let scriptTemplateScriptTypeValue;
    var scriptName = "";
    var scriptDesc = "";
    var saveFromBottom = true;
    var countdown = 10;
    var countdownFJ = 10;
    var tryRequestId = '';
    var tryAgentIp = '';
    var tryAgentPort = '';
    var cmdRequestId = '';
    var cmdAgentIp = '';
    var cmdAgentPort = '';
    var refreshTryForUpdate;
    var refreshCmdForUpdate;
    var refreshCmd;
    var newServiceId = iidForUpdateScriptEdit;
    var scriptuuid = uuidForUpdateScriptEdit;
    var isFromTryATry = 0;
    var chosedAgentIds = [];
    var whichButtonIsClicked = 0; // 1:尝试一下    2:测试      3:终端选择服务器
    var sysID;
    var busID;
    var cmdVForbasicUpdate = null;//CMD输入内容
    var chosedAppSys = new Array();
    var resGroupFlag;
    var chosedGroupIds = [];
    var chosedGroupWin;
    var execUserConfigWindow;
    var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    var requiredCfg = '<span style="color:red;font-weight:bold" data-qtip="Required">  *按输入周期执行</span>';
    var sysID;
    var busID;
    var threeBsTypeId;
    var initLoadThreeBsFlag = true;
    var vv = '';
    var agentiids = new Array();
    let selectedAgent = new Set();
    //编辑
    var la = new Ext.util.HashMap();
    var labels;
    var golbalParamName;
    var scriptDirId;
    var scriptBindHis;
    var scriptVersionCount;
    var scriptStatus;
    var label;
    var state = 0;
    var state1 = 0;
    var variableTab;
    var functionTab;
    var checkItems = [];
    var variableCheckItems = [];
    var scriptTab;
    var variableId = new Array();
    var variableDeleteId = new Array();
    var funDeleteId = new Array();
    var funId = new Array();
    var bindInsertArray = [];               //选中变量、函数的存放数组
    var bindDeleteArray = [];               //取消选中变量、函数的存放数组
    var VarandFuncBand = {};                   //最终传给后台的函数、变量绑定数组
    var VarandFuncBandData  = null;          //存放json格式
    var varBindInsert = [];                 //存放变量勾选状态下的数据
    var varBindDelete = [];                 //存放变量取消勾选下的数据
    var funcBindInsert = [];                //存放函数勾选状态下的数据
    var funcBindDelete = [];                //存放函数取消勾选下的数据
    var alreadyBind = [];                   //存放已绑定数据
    var nowScriptuuid;
    var cgUuid = uuidForUpdateScriptEdit;
    var sysIDSec;
    var systemId;
    var type;
    var ipStart = Ext.create('Ext.form.TextField',
        {
            labelWidth: 79,
            fieldLabel: '起始IP',
            emptyText: '--请输入开始IP--',
            //labelSeparator : '',
            width: '40%',
            labelAlign: 'right'
        });
    /** 结束ip* */
    var ipEnd = Ext.create('Ext.form.TextField',
        {
            labelWidth: 70,
            fieldLabel: '终止IP',
            emptyText: '--请输入截止IP--',
            //labelSeparator : '',
            labelAlign: 'right',
            width: '40%',
        });

    var selModelAgents = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelAgentsSS',
        checkOnly: true
    });


    var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: function () {
            agent_grid.ipage.moveFirst();
        },
        bodyCls: 'x-docked-noborder-top',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [ipStart, ipEnd,
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        agentgridScript.ipage.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: 'IP清空',
                    handler: function () {
                        ipStart.setValue('');
                        ipEnd.setValue('');
                        agentgridScript.ipage.moveFirst();
                    }
                }]
        }]
    });

    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: sdFunctionSortSwitch,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    groupNameStore.on('load', function (store, options) {
        if (groupName) {
            groupNameCombo.setValue(groupName);
            bussData.load({
                params: {
                    fk: groupName
                }
            });
        }
    });
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: sdFunctionSortSwitch ? false : true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    Ext.define('paramRuleModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'paramRuleIn',
            type: 'string'
        }, {
            name: 'paramRuleType',
            type: 'int'
        }, {
            name: 'paramRuleLen',
            type: 'int'
        }, {
            name: 'paramRuleOut',
            type: 'string'
        }, {
            name: 'paramRuleDesc',
            type: 'string'
        }, {
            name: 'paramRuleOrder',
            type: 'int'
        }, {
            name: 'dataList',
            type: 'string'
        }]
    });

    Ext.define('attaTempModelEdit', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });
    /*    var dbsourceStore= Ext.create('Ext.data.Store', {
    	fields: ['iid', 'dsName'],
    	autoLoad: false,
    	proxy: {
    		type: 'ajax',
    		url: 'getAllDataSourceNamesByCpid.do',
    		reader: {
    			type: 'json',
    			root: 'dataList'
    		}
    	}
    });*/
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussData.on('load', function (store, options) {
        if (sysID != "undefined" && typeof (sysID) != "undefined") {
            if (state == 0) {
                bussCb.setValue(sysID);
                state = 1;
            }
            bussTypeData.load({
                params: {
                    fk: sysID
                }
            });
        }
        if (busID != "undefined" && typeof (sysID) != "undefined") {
            if (state1 == 0) {
                bussTypeCb.setValue(busID);
                state1 = 1;
            }
        }
    });

    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var bussCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '一级分类',
        afterLabelTextTpl: required,
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'bussType',
        //padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '二级分类',
        afterLabelTextTpl: required,
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value !== null && scriptThreeBstypeSwitch) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    threeBussTypeData.on('load', function (store, options) {
        if (threeBsTypeId && threeBussTypeData.getRange().length > 0 && initLoadThreeBsFlag) {
            threeBussTypeCb.setValue(threeBsTypeId);
            initLoadThreeBsFlag = false;
        } else {
            threeBussTypeCb.setValue('');
        }
    });

    //北京邮储 三级分类
    var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threeBussTypeCb',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        hidden: !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var sqlexectype = Ext.create('Ext.data.Store', {
        fields: ['iid', 'name'],
        data: [
            {"iid": "1", "name": "Agent"},
            {"iid": "2", "name": "JDBC"}
        ]
    });
    Ext.define('scriptTemplateComboboxModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'IID', type: 'long'},
            {name: 'INAME', type: 'string'},
            {name: 'ICONTENT', type: 'string'}
        ]
    });
    var scriptTemplateComboboxStore = Ext.create('Ext.data.Store', {
        model: 'scriptTemplateComboboxModel',
        autoLoad: scriptTemplateSwitch,
        proxy: {
            type: 'ajax',
            url: 'scriptService/getScriptTemplateComboboxStore.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    scriptTemplateComboboxStore.on('beforeload', function (store, options) {
        var new_params = {
            iscripttype: scriptTemplateScriptTypeValue
        };
        Ext.apply(scriptTemplateComboboxStore.proxy.extraParams, new_params);
    });


    //模版下拉选，根据选择脚本类型进行展示
    var scriptTemplateCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTemplateCombobox',
        width: 130,
        displayField: 'INAME',
        valueField: 'IID',
        editable: true,
        queryMode: 'local',
        hidden: !scriptTemplateSwitch,
        emptyText: '--选择脚本模版--',
        store: scriptTemplateComboboxStore,
        listeners: {//监听事件
            select: function (combo, records, eOpts) {
                let scriptContent = records[0].data.ICONTENT;
                editor.getDoc().setValue(scriptContent);
                editor.refresh();
            }
        }

    });
    var chooseSqlExecModel = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'chooseSqlExecModel',
        width: 100,
        displayField: 'name',
        valueField: 'iid',
        editable: true,
        queryMode: 'local',
        value: sqlexectypeValue,
        emptyText: '执行模式',
        store: sqlexectype,
        listeners: {//监听事件
            select: function () {
                if (updatecheckRadio == '4' && chooseSqlExecModel.getValue() == '2') {
                    tryTestbtn.hide();
                    agentPullChosedStore.load(
                        {
                            url: "getTryAgentListByResource.do"
                        });
                } else {
                    tryTestbtn.show();
                    agentPullChosedStore.load(
                        {
                            url: "getTryAgentList.do"
                        });
                }
                agentPullChosedCb.setValue('');
            }
        }
    });


    /** *********************Panel********************* */
    var FieldContainer = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 70,
        name: 'ra_s_type_update',
//        padding: '0 5 10 5',
        items: [{
            name: 'ra_s_type_update',
            width: 50,
            inputValue: '0',
            boxLabel: 'shell',
            hidden: projectFlag == 1 ? true : false,
            checked: true,
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function (value) {
                        if (updatecheckRadio != 0) {
                            editor.setOption("mode", 'shell');
                            updatecheckRadio = 0;
                            scriptTemplateScriptTypeValue = 'shell';
                            if (scriptTemplateSwitch) {
                                scriptTemplateComboboxStore.reload();
                            }
//                            editor.isEmpty("");
                        }
                        outruleGrid.hide();
                        chooseSqlExecModel.hide();
                        attachmentGrid.show();
                        maimPanels.setTitle('编辑框---shell');
                        agentPullChosedStore.load(
                            {
                                url: "getTryAgentList.do"
                            });
                        consolePanel.setTitle("日志");
                        language='1'
                        if (scriptEditBookSwitch){
                            functionTab.ipage.moveFirst();
                            variableStore.load();
                            //切换脚本类型时清空选中的函数数据
                            funId = [];
                            funcBindInsert= [];
                        }
                    }
                }
            }
        },
            {
                name: 'ra_s_type_update',
                width: 50,
                inputValue: '1',
                boxLabel: 'bat',
                hidden: projectFlag == 1 ? true : false,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            if (updatecheckRadio != 1) {
                                updatecheckRadio = 1;
                                editor.setOption("mode", 'bat');
                                scriptTemplateScriptTypeValue = 'bat';
                                scriptTemplateCombobox.setValue();
                                if (scriptTemplateSwitch) {
                                    scriptTemplateComboboxStore.reload();
                                }
//                            editor.isEmpty("");
                            }
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            maimPanels.setTitle('编辑框---bat');
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                            language='all'
                            if (scriptEditBookSwitch){
                                functionTab.ipage.moveFirst();
                                variableTab.ipage.moveFirst();
                            }
                        }
                    }
                }
            },
            {
                name: 'ra_s_type_update',
                width: 50,
                inputValue: '2',
                hidden: projectFlag == 1 ? true : false,
                boxLabel: 'perl',
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            updatecheckRadio = 2;
                            editor.setOption("mode", 'text/x-perl');
                            maimPanels.setTitle('编辑框---perl');
                            scriptTemplateScriptTypeValue = 'perl';
                            scriptTemplateCombobox.setValue();
                            if (scriptTemplateSwitch) {
                                scriptTemplateComboboxStore.reload();
                            }
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                            language='all'
                            if (scriptEditBookSwitch){
                                functionTab.ipage.moveFirst();
                                variableTab.ipage.moveFirst();
                            }
                        }
                    }
                }
            },
            {
                name: 'ra_s_type_update',
                width: 60,
                inputValue: '3',
                boxLabel: 'python',
                hidden: projectFlag == 1 ? true : false,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            updatecheckRadio = 3;
                            editor.setOption("mode", 'python');
                            maimPanels.setTitle('编辑框---python');
                            scriptTemplateScriptTypeValue = 'python';
                            scriptTemplateCombobox.setValue();
                            if (scriptTemplateSwitch) {
                                scriptTemplateComboboxStore.reload();
                            }
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                            language='2'
                            if (scriptEditBookSwitch){
                                functionTab.ipage.moveFirst();
                                variableStore.load();
                                funId = [];
                                funcBindInsert= [];
                            }
                        }
                    }
                }
            }, {
                name: 'ra_s_type_update',
                width: 45,
                inputValue: '4',
                boxLabel: 'sql',
                hidden:scriptSqlShowSwitch,
                checked: projectFlag == 1 ? true : false,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            updatecheckRadio = 4;
                            editor.setOption("mode", 'sql');
                            maimPanels.setTitle('编辑框---sql');
                            scriptTemplateScriptTypeValue = 'sql';
                            scriptTemplateCombobox.setValue();
                            if (scriptTemplateSwitch) {
                                scriptTemplateComboboxStore.reload();
                            }
                            if (projectFlag == 1) {
                                outruleGrid.show();
                                chooseSqlExecModel.show();
                                attachmentGrid.hide();
                            } else {
                                outruleGrid.hide();
                                attachmentGrid.show();
                                chooseSqlExecModel.hide();
                            }
                            if (chooseSqlExecModel.getValue() == '2') {
                                agentPullChosedStore.load(
                                    {
                                        url: "getTryAgentListByResource.do"
                                    });
                            }
                            consolePanel.setTitle("日志");
                            language='all'
                            if (scriptEditBookSwitch){
                                functionTab.ipage.moveFirst();
                                variableTab.ipage.moveFirst();
                            }
                        }
                    }
                }
            }, {
                name: 'ra_s_type_update',
                width: 100,
                inputValue: '6',
                boxLabel: 'powershell',
                hidden: projectFlag == 1 ? true : false,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            updatecheckRadio = 6;
                            editor.setOption("mode", 'powershell');
                            maimPanels.setTitle('编辑框---powershell');
                            scriptTemplateScriptTypeValue = 'powershell';
                            scriptTemplateCombobox.setValue();
                            if (scriptTemplateSwitch) {
                                scriptTemplateComboboxStore.reload();
                            }
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                            language='3'
                            if (scriptEditBookSwitch){
                                functionTab.ipage.moveFirst();
                                variableStore.load();
                                funId = [];
                                funcBindInsert= [];
                            }
                        }
                    }
                }
            }]
    });

    var FieldContainerForOnecmd = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 60,
        name: 'ra_s_type1_update',
//        padding: '0 5 10 5',
        items: [{
            name: 'ra_s_type1_update',
            width: 60,
            inputValue: '0',
            boxLabel: 'shell',
            checked: true
        },
            {
                name: 'ra_s_type1_update',
                width: 50,
                inputValue: '1',
                boxLabel: 'bat'
            },
            {
                name: 'ra_s_type1_update',
                width: 50,
                inputValue: '2',
                boxLabel: 'perl'
            },
            {
                name: 'ra_s_type1_update',
                width: 60,
                inputValue: '3',
                boxLabel: 'python'
            }, {
                name: 'ra_s_type1_update',
                width: 100,
                inputValue: '6',
                boxLabel: 'powershell'
            }]
    });

    var agentPullChosedStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: agentPullChosedStoreUrl,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var agentPullChosedCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'agentPullChosed',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        hidden: scriptTrySwitch,
        valueField: 'iid',
        editable: true,
        emptyText: '--请选择服务器--',
        store: agentPullChosedStore
    });

    var agentPullChosedStoreForOneCmd = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: singleCommandSwitch,
        proxy: {
            type: 'ajax',
            url: 'getTryAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var agentPullChosedCbForOneCmd = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'agentPullChosedForOneCmd',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选择服务器--',
        store: agentPullChosedStoreForOneCmd
    });
    var agentPullChosedStoreForCmd = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agentIp'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListNoPage.do?flag=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var agentPullChosedCbForCmd = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'agentPullChosedForCmd',
        labelWidth: 70,
        width: 120,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agentIp',
        valueField: 'iid',
        editable: true,
        emptyText: '请选择服务器',
        store: agentPullChosedStoreForCmd
    });

    var protocolTypeStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'name'],
        data: [
            {"iid": "1", "name": "SSH"},
            {"iid": "2", "name": "TELNET"}
        ]
    });

    var chooseServerTypeForConsole = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'chooseServerTypeForConsole',
        width: 80,
        queryMode: 'local',
        displayField: 'name',
        valueField: 'iid',
        editable: false,
        emptyText: '协议',
        store: protocolTypeStore
    });

    var choosePortForConsole = new Ext.form.NumberField({
        name: 'choosePortForConsole',
        emptyText: '端口',
        width: 70
    });

    var userNameForConsole = new Ext.form.TextField({
        name: 'userNameForConsole',
        emptyText: '用户名',
        width: 100
    });

    var userPasswordForConsole = new Ext.form.TextField({
        name: 'userPasswordForConsole',
        emptyText: '密码',
        inputType: 'password',
        width: 100
    });
    var scriptfile = new Ext.form.field.File({
        name: 'scriptfile', // 设置该文件上传空间的name，也就是请求参数的名字
        fieldLabel: '本地文件',
        labelWidth: 70,
        labelStyle: 'margin:10px 0 0 0',
        padding: '0 8 0 0',
        msgTarget: 'side',
        anchor: '100%',
        height: 30,
        buttonText: '选择本地文件',
        columnWidth: 1,
        listeners: {//监听事件
            'change': function () {//读取
                var importfile = scriptForm.getForm().findField("scriptfile").getValue();
                if (importfile != '') {
                    scriptForm.getForm().submit({
                        url: 'ajaxImportScript.do',
                        success: function (form, action) {
                            var result = Ext.JSON.decode(action.response.responseText);
                            var errMsg = result.errMsg;
                            var scriptContent = result.scriptContent;
                            if (!errMsg) {
                                editor.getDoc().setValue(scriptContent);
                                editor.refresh();
                            }
                        },
                        failure: function (form, action) {
                        }
                    });
                }
                Ext.Msg.alert('提示', "脚本内容已导入，请保存！");
            }
        }
    });
    var sName = new Ext.form.TextField({
        name: 'serviceName',
        fieldLabel: '服务名称',
        afterLabelTextTpl: required,
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5,
        listeners: {
            blur: function (e) {
                //失去焦点事件
                if (e.getRawValue() != '') {
                    e.setRawValue(e.getRawValue().toString().trim())
                }
            }
        }
    });
    // =======================================光大项目，脚本名称输入框，获取上一个页面的值开关
    var scNameField = 'scriptName';
//    var usePlantFormStore;
    if (projectFlag == 1) {
        scNameField = scriptName;
//    	usePlantFormStore= Ext.create('Ext.data.Store', {
//        	fields: ['value', 'text'],
//            data: [['全部', '全部'],['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
//        });
    }
//    else{
//    	 usePlantFormStore= Ext.create('Ext.data.Store', {
//    	    	fields: ['value', 'text'],
//    	        data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
//    	    });
//    }

    var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    usePlantFormStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: projectFlag
        };
        Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
    });

    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        afterLabelTextTpl: required,
        displayField: scNameField,
        emptyText: '',
        labelWidth: 70,
        regex: /^[0-9a-zA-Z_]{1,}$/,
        regexText: '只允许输入数字、字母、下划线',
//        padding: '0 5 0 0',
        columnWidth: .5
    });

    var usePlantForm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '适用平台',
        afterLabelTextTpl: required,
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: false,
        multiSelect: true,//启用多选
        emptyText: '--请选择平台--',
        store: usePlantFormStore
    });

    var scriptWorkDir = new Ext.form.TextField({
        name: 'scriptWorkDir',
        fieldLabel: '工作目录',
        hidden: !scriptWorkDirSwitch,
        displayField: 'scriptWorkDir',
        emptyText:'',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });

    // 添加下拉显示条数菜单选中事件
    usePlantForm.on("select", function (comboBox, records, eOpts) {
        if (records.length > 1) {
            var windowsFlag = false;
            var linuxFlag = false;
            Ext.each(records, function (record) {
                var iname1 = record.data.INAME;
                if (iname1 == 'Windows') {
                    windowsFlag = true;
                }
                if (iname1 != 'Windows') {
                    linuxFlag = true;
                }
                if (linuxFlag && windowsFlag) {
                    Ext.Msg.alert('提示', 'Windows平台和非Windows平台不能同时选择！');
                    usePlantForm.clearValue();
                    return;
                }
            });
        }
        var scriptTypeValue = FieldContainer.getChecked()[0].boxLabel;
        if (scriptTypeValue == 'bat' && comboBox.getValue() != 'Windows') {
            Ext.Msg.alert('提示', 'bat脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if (scriptTypeValue == 'powershell' && comboBox.getValue() != 'Windows') {
            Ext.Msg.alert('提示', 'powershell脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if ((scriptTypeValue == 'shell' || scriptTypeValue == 'perl') && comboBox.getValue() == 'Windows') {
            Ext.Msg.alert('提示', '只允许 bat、python、sql、powershell脚本才能选择Windows平台');
            usePlantForm.setValue();
            return;
        }
    });
    var timeout = new Ext.form.TextField({
        name: 'timeout',
        fieldLabel: '超时(秒)',
        displayField: 'timeout',
        hidden: !scriptTimeoutSwitch,
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });

    var excepResultTypeStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "1", "name": "lastLine"},
            {"id": "2", "name": "exitCode"}
        ]
    });

    // var excepResultType = new Ext.form.TextField({
    //     name: 'excepResultType',
    //     fieldLabel: '预期结果类型',
    //     displayField: 'excepResultType',
    //     emptyText: '',
    //     labelWidth: 70,
    //     value: excepResultTypeStore,
    //     padding: '0 5 0 0',
    //     columnWidth: .5
    // });

    var excepResultType = Ext.create('Ext.form.field.ComboBox', {
        name: 'excepResultType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '预期类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择预期结果类型--',
        store: excepResultTypeStore
    });

    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
//        padding: '0 5 0 0',
        columnWidth: .5
    });
    var textred = ' ';
    if (isSu) {
        textred = required;
    }
    var suUser = new Ext.form.TextField({
        name: 'suUser',
        fieldLabel: '启动用户',
        displayField: 'suUser',
        emptyText: '',
        afterLabelTextTpl: textred,
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '请输入功能说明...',
        columnWidth: 1,
        height: 136,
        hidden: true,
        autoScroll: true,
        listeners: {
            'blur': function (me, e, eOpts) {
                scriptDesc = me.getValue();
                funcDescInWin.setValue(scriptDesc);
            }
        }
    });

    var labelStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'iscriptuuid'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'queryLabels.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    labelStore.on('beforeload', function (store, options) {
        var new_params = {
            uuid: uuidForUpdateScriptEdit
        };
        Ext.apply(scriptTemplateComboboxStore.proxy.extraParams, new_params);
    });
    label = Ext.create('Ext.panel.Panel', {
        // region: 'south',
        border: false,
        columnWidth: 1,
        width: '100%',
        hidden: !labelSwitch,
        html: '<div class="report_box">' +
            '<div class="tagsinput-primary form-group" id="signDiv">' +
            '<label class="s_tit" ><span>标签:</span></label>' +
            '<input name="tagsinput" id="tagsinputval" class="tagsinput" data-role="tagsinput" value=""   >' +
            '</div>' +
            '</div>'
    });

    label.on("afterrender", function () {
        //console.log($("#tagsinputval"),$("#tagsinputval").siblings('.bootstrap-tagsinput'));
        if ($("#tagsinputval").siblings('.bootstrap-tagsinput').length > 0) {
            $("#tagsinputval").siblings('.bootstrap-tagsinput').remove();
            $('#tagsinputval').remove();

        }
        if ($.fn.tagsinput) {

            $("#tagsinputval").tagsinput();
        }

        function setLabel() {
            if (labelEdit != null && labelEdit != '') {
                var labs = labelEdit.split(",")
                for (var i = 0; i < labs.length; i++) {
                    la.add(labs[i], i);
                }
                addTags(la)
            }
        }

        setLabel();
    }, this);

    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
        name: 'funcDescInWin',
        fieldLabel: '功能说明',
        displayField: 'funcDescInWin',
        afterLabelTextTpl: required,
        emptyText: '请输入功能说明...',
        labelWidth: 70,
        height: 136,
        columnWidth: .99,
        autoScroll: true
    });

    var ycCheckBeforeExec =  Ext.create('Ext.form.field.Checkbox',{
        hidden:!checkBeforeExecSwitch,
        margin:0,
        boxLabel: '先试点，后批量原则'
    })

    var buttonFormPanel = Ext.create('Ext.form.FieldContainer', {
        defaultType: 'checkboxfield',
        items: [
            ycCheckBeforeExec
        ]
    })

    Ext.define('dbModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'string'
        }, {
            name: 'name',
            type: 'string'
        }]
    });
    var dbStore = Ext.create('Ext.data.Store', {
        autoDestroy: true,
        autoLoad: projectFlag == 1,
        model: 'dbModel',
        proxy: {
            type: 'ajax',
            url: 'getDatabaseType.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var dbtest = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'dbType',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '数据库类型',
        padding: '0 5 0 0',
        editable: false,
        displayField: 'name',
        valueField: 'id',
        emptyText: '--请选择数据库类型--',
        store: dbStore
    });

    var planTime_sm = Ext.create('Go.form.field.DateTime', {
        fieldLabel: '计划时间',
        format: 'Y-m-d H:i:s',
        labelWidth: 85,
        hidden: true,
//	    width:200,
        columnWidth: .98,
        margin: '10 0 0 0'
    });

    var requiredVersionAndResource = '<span style="color:red;font-weight:bold" data-qtip="请配置版本或者资源组" onclick="versionAndResource()">  点击配置选项</span>';
    var versionAresource = Ext.create('Ext.form.DisplayField', {
        fieldLabel: '版本与资源组配置',
        afterLabelTextTpl: requiredVersionAndResource,
        labelWidth: 200,
        columnWidth: .44,
        padding: '0 0 0 0',
        margin: '10 0 0 0'
    });
    var planTime_DD = Ext.create('Ext.form.NumberField', {
        fieldLabel: '天数',
        editable: true,
        name: 'DD',
        padding: '0 5 0 0',
        labelWidth: 40,
        columnWidth: .20,
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (null == newv || newv == '' || trim(newv) == '') {
                    planTime_DD.setValue("")
                } else {
                    if (/^[0-9]([0-9])*$/.test(newv)) {
                        if (newv > 31) {
                            Ext.Msg.alert("提示", "天数值需在1~31之间!");
                            planTime_DD.setValue(oldv)
                        }
                        return true;
                    } else {
                        Ext.Msg.alert("提示", "天数窗口只能正整数");
                        planTime_DD.setValue(oldv)
                        return false;
                    }
                }
            }
        }
    });
    var planTime_HH = Ext.create('Ext.form.NumberField', {
        fieldLabel: '小时',
        editable: true,
        name: 'HH',
        padding: '0 5 0 0',
        labelWidth: 40,
        columnWidth: .20,
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (null == newv || newv == '' || trim(newv) == '') {
                    planTime_HH.setValue("")
                } else {
                    if (/^[0-9]([0-9])*$/.test(newv)) {
                        if (newv > 23) {
                            Ext.Msg.alert("提示", "小时值需在1~23之间!");
                            planTime_HH.setValue(oldv)
                        }
                        return true;
                    } else {
                        Ext.Msg.alert("提示", "小时窗口只能正整数");
                        planTime_HH.setValue(oldv)
                        return false;
                    }
                }
            }
        }
    });
    var planTime_mi = Ext.create('Ext.form.NumberField', {
        fieldLabel: '分钟',
        editable: true,
        name: 'mi',
        padding: '0 5 0 0',
        labelWidth: 40,
        columnWidth: .20,
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (null == newv || newv == '' || trim(newv) == '') {
                    planTime_mi.setValue("")
                } else {
                    if (/^[0-9]([0-9])*$/.test(newv)) {
                        if (newv > 59) {
                            Ext.Msg.alert("提示", "分钟值需在1~59之间!");
                            planTime_mi.setValue(oldv)
                        }
                        return true;
                    } else {
                        Ext.Msg.alert("提示", "分钟窗口只能正整数");
                        planTime_mi.setValue(oldv)
                        return false;
                    }
                }
            }
        }
    });
    var planTime_MM = Ext.create('Ext.ux.ideal.form.ComboBox', {
        fieldLabel: '周期类型',
        editable: false,
        name: 'MM',
        padding: '0 5 0 0',
        matchFieldWidth: false,// 此处要有
        labelWidth: 85,
        columnWidth: .35,
        store: {
            fields: ['value'],
            data: [
                {"value": "按计划执行一次"},
                {"value": "间隔x日"},
                {"value": "间隔x小时"},
                {"value": "间隔x分钟"}
            ]
        },
        displayField: 'value',
        value: "间隔x日",
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (newv == '间隔x日') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.show();
                    planTime_HH.show();
                    planTime_mi.show();
                } else if (newv == '间隔x小时') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.hide();
                    planTime_HH.show();
                    planTime_mi.show();
                } else if (newv == '间隔x分钟') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.show();
                } else if (newv == '按计划执行一次') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.hide();
                }
            }
        }
    });

    var cfg_Display = Ext.create('Ext.form.DisplayField', {
        name: 'display',
        fieldLabel: '周期配置',
        afterLabelTextTpl: requiredCfg,
        labelWidth: 350,
        columnWidth: .9
    });
    var tableName = new Ext.form.TextField({
        name: 'tablename',
        fieldLabel: '表名称',
        displayField: 'tablename',
        labelAlign: 'right',
        emptyText: '',
        margin: '10 0 0 0',
        labelWidth: 93,
        columnWidth: .98
    });

    /** 工程类型下拉框* */
    var serviceType = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'serviceType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '服务类型',
        afterLabelTextTpl: required,
        displayField: 'text',
        valueField: 'value',
        editable: false,
        emptyText: '--请选择服务类型--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['0', '应用'], ['1', '采集']]
        }),
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (newv == "应用"
                    || newv == "0"
                    || newv == 0) {
                    planTime_sm.hide();
                    versionAresource.hide();
                    cfg_Display.hide();
                    tableName.hide();
                    planTime_MM.hide();
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.hide();
                } else {
                    planTime_sm.show();
                    versionAresource.show();
                    cfg_Display.show();
                    tableName.show();
                    planTime_MM.show();
                    planTime_DD.show();
                    planTime_HH.show();
                    planTime_mi.show();
                }
            }
        }
    });
    var audittest = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'audittest',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '发起审核',
        afterLabelTextTpl: required,
        padding: '0 5 0 0',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        emptyText: '--请选择--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '是'], ['0', '否']]
        })
    });
    // 光大的开关==============================基本信息面板中 输入框开关
    var scriptFormItems = [{
        border: false,
        layout: 'column',
        margin: '5',
        items: [sName, scName]
    },
        {
            border: false,
            layout: 'column',
            margin: '5',
            items: [suUser,groupNameCombo]
        }, {
            border: false,
            layout: 'column',
            margin: '5',
            items: [bussCb, bussTypeCb]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [threeBussTypeCb, timeout]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResultType]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResult, errExcepResult]
        }, {
            border: false,
            layout: 'column',
            margin: '5',
            items: [dbtest, serviceType]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm, /*suUser,*/ scriptWorkDir, audittest]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [scriptfile]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [label]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDescInWin]
        }];

    var scriptForm = Ext.create('Ext.ux.ideal.form.Panel', {
        // width: 590,
        bodyCls: 'x-docked-noborder-top',
        // height: 350,
        width: contentPanel.getWidth() * 0.6,
        height: contentPanel.getHeight() * 0.8,
        border: false,
//        margin: 10,
        collapsible: false,
//        title: '基本信息',
        items: scriptFormItems,
        autoScroll:true
    });

    var funcDescForm = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        width: '100%',
        bodyCls: 'x-docked-noborder-top',
//        height: 168,
        border: true,
        layout: 'anchor',
        margin: '0 0 5 0',
        collapsible: false,
        title: '基本信息',
        items: [{
            hidden: true,
            layout: 'column',
            border: false,
            items: [funcDesc]
        }],
        tools: [{
            type: 'print',
            tooltip: '基本信息',
            handler: function (event, toolEl, panelHeader) {
                // Ext.Ajax.request({
                //     url: 'scriptService/queryScriptLabels.do',
                //     method: 'POST',
                //     async: false,
                //     params: {
                //         uuid: scriptuuid
                //     },
                //     success: function (response, request) {
                //         var labs = Ext.decode(response.responseText).dataList;
                //         for (var i = 0; i < labs.length; i++) {
                //             la1.add(labs[i], i);
                //         }
                //         addTags(la1)
                //     },
                //     failure: function (result, request) {
                //     }
                // });
                //var labs = labelEdit;
                // console.log(labelEdit)
                if (!scName.getValue()) {
                    scName.setValue(scriptName);
                }
                if (!funcDescInWin.getValue()) {
                    funcDescInWin.setValue(scriptDesc);
                }
                //saveFromBottom = false;
                //usePlantForm.setValue();
                if (projectFlag == 1) {
                    saveFromBottom = true;
                }
                baseInfoOfScriptWin.show();

            }
        }
//        ,{
//            type:'help',
//            tooltip: '帮助',
//            callback: function(panel, tool, event) {
//            	window.open("scriptHelpDoc.do","resizable=yes").focus();
//            }
//        }
        ]
    });

    if (!baseInfoOfScriptWin) {
        baseInfoOfScriptWin = Ext.create('widget.window', {
            title: '基本信息',
            closable: true,
            closeAction: 'hide',
            tbar: [FieldContainer, '->'/* agentPullChosedCb,tryATrybtn*/],
            resizable: false,
            modal: true,
            // width: 600,
            // height: 430,
            width: contentPanel.getWidth() * 0.64,
            height: contentPanel.getHeight() * 0.92,
            minWidth: 350,
            minHeight: 350,
            layout: {
                type: 'border',
                padding: 5
            },
            items: [scriptForm],
            listeners: {
                show: function () {
                    try {
                        Ext.Msg.close();
                    }catch (err){

                    }

                }
            },
            dockedItems:[
                {
                    xtype: 'toolbar',
                    dock:'bottom',
                    layout: {
                        pack: 'center'
                    },
                    items:[{
                        xtype: "button",
                        cls:'Common_Btn',
                        text: "保存",
                        // id: 'saveButton',
                        handler: function () {
                            Ext.Msg.wait('处理中，请稍后...', '提示');
                            if (saveFromBottom) {
                                var sysId = bussCb.getValue();
                                var bussTypeId = bussTypeCb.getValue();
                                var threeBussTypeId = threeBussTypeCb.getValue();
                                var sysName = bussCb.getRawValue();
                                var bussType = bussTypeCb.getRawValue();
                                var serverName = sName.getValue();
                                var scriptName1 = scName.getValue();
                                var up = usePlantForm.getValue() || "";
                                var errExcepResult1 = errExcepResult.getValue();
                                var excepResult1 = excepResult.getValue();
                                var scriptDesc1 = funcDescInWin.getValue();
                                // 光大项目 发起审核下拉选
                                var audittestValue = audittest.getValue();
                                // 光大项目数据库类型下拉选
                                var dbtestValue = dbtest.getValue();
                                // 光大项目 服务类型下拉选
                                var serviceTtextValue = serviceType.getValue();
                                var groupName = groupNameCombo.getValue();
                                if (!scriptName1) {
                                    scName.setValue(scriptName);
                                }
                                if (!scriptDesc1) {
                                    funcDescInWin.setValue(scriptDesc);
                                }
                                if (serverName.trim() == '') {
                                    Ext.MessageBox.alert("提示", "服务名称不能为空!");
                                    return;
                                }
                                if (scriptName1.trim() == '') {
                                    Ext.MessageBox.alert("提示", "脚本名称不能为空!");
                                    return;
                                }
                                if (!(scriptForm.getForm().isValid())) {
                                    Ext.Msg.alert('提示', '脚本名称只允许输入数字、字母、下划线!');
                                    return;
                                }
                                if (projectFlag == 1) {// ==============================光大页面设置的开关

                                    if (!audittestValue) {
                                        Ext.MessageBox.alert("提示", "请选择发起审核输入框!");
                                        return;
                                    }
                                    if (!dbtestValue) {
                                        Ext.MessageBox.alert("提示", "请选择数据库类型!");
                                        return;
                                    }

                                    if (!serviceTtextValue) {
                                        Ext.MessageBox.alert("提示", "请选择服务类型!");
                                        return;
                                    }
                                } else {
                                    if (sdFunctionSortSwitch) {
                                        if (!groupName) {
                                            Ext.MessageBox.alert("提示", "请选择功能分类!");
                                            return;
                                        }
                                    }
                                    if (!sysId) {
                                        Ext.MessageBox.alert("提示", "请选择一级分类!");
                                        return;
                                    }
                                    if (!bussTypeId) {
                                        Ext.MessageBox.alert("提示", "请选择二级分类!");
                                        return;
                                    }
                                    if (!threeBussTypeId && scriptThreeBstypeSwitch) {
                                        Ext.MessageBox.alert("提示", "请选择三级分类!");
                                        return;
                                    }
                                }
                                if (up.length == 0) {
                                    Ext.MessageBox.alert("提示", "适用平台不能为空!");
                                    return;
                                }

                                if (scriptDesc1.trim() == '') {
                                    Ext.MessageBox.alert("提示", "功能说明不能为空!");
                                    return;
                                }
                                if (projectFlag != 1 && limitTwenty) {
                                    if (scriptDesc1.trim().length < 20) {
                                        Ext.MessageBox.alert("提示", "功能说明不能少于20字符!");
                                        return;
                                    }
                                }
                                if (isSu) {
                                    if (suUser.getValue().trim() == '') {
                                        Ext.MessageBox.alert("提示", "启动用户不能为空!");
                                        return;
                                    }
                                }

                                save(0);
                            } else {
                                this.up("window").close();
                            }
                            scriptDesc = funcDescInWin.getValue();
                        }
                    }, {
                        xtype: "button",
	  			        cls:'Common_Btn',
                        text: "取消",
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
                },
                {
                    xtype: 'toolbar',
                    dock:'bottom',
                    layout: {
                        pack: 'center'
                    },
                    items:[buttonFormPanel]
                }
            ]
        });
    }

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'paramType',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            },
            {
                name: 'ruleName',
                type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }, {
                name: 'parameterName',
                type: 'string'
            }]
    });

    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var paramRulesStore = Ext.create('Ext.data.Store', {
        autoLoad: projectFlag == 1,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramRuleModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    paramRulesStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: uuidForUpdateScriptEdit,
            iflag: "0"
        };
        Ext.apply(paramRulesStore.proxy.extraParams, new_params);
    });
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: uuidForUpdateScriptEdit
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    attachmentStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: uuidForUpdateScriptEdit,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    attachmentStore.on('load', function (me, records, successful, eOpts) {
        attachmentIds = [];
        $.each(records, function (index, record) {
            attachmentIds.push(record.get('iid'));
        });
    });

//    var paramTypeStore = Ext.create('Ext.data.Store', {
//        fields: ['name'],
//        data: [{
//            "name": "string"
//        },
//        {
//            "name": "int"
//        },
//        {
//            "name": "float"
//        }]
//    });
    Ext.define('paramManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
            {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'parameterValue',
                type: 'string'
            },
            {
                name: 'parameterDesc',
                type: 'string'
            }]
    });
    var enumValueStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        // autoDestroy: true,
        // pageSize: 10,
        model: 'paramManangerModel',
        proxy: {
            type: 'ajax',
            url: 'getParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                // totalProperty: 'total'
            }
        }
    });
    var defultEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true
        })
    });
    var passwordEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true,
            inputType: 'password'
        })
    });

    var paramTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "枚举"
        }, {
            "name": "IN-string"
        },
            {
                "name": "IN-string(加密)"
            },
            {
                "name": "IN-int"
            },
            {
                "name": "IN-float"
            },
            {
                "name": "OUT-string"
            },
            {
                "name": "OUT-int"
            },
            {
                "name": "OUT-float"
            }]
    });
//    var paramTypeCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
//        margin: '5',
//        store: paramTypeStore,
//        queryMode: 'local',
//        width: 600,
//        forceSelection: true,
//        // 要求输入值必须在列表中存在
//        typeAhead: true,
//        // 允许自动选择
//        displayField: 'name',
//        valueField: 'name',
//        triggerAction: "all"
//    });
    Ext.define('parameterCheckModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'ruleName',
            type: 'string'
        }, {
            name: 'checkRule',
            type: 'string'
        }, {
            name: 'ruleDes',
            type: 'string'
        }]
    });
    var store = Ext.create('Ext.data.Store', {
        autoLoad: bhParameterCheckSwitch,
        model: 'parameterCheckModel',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterCheck.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var paramDesc='描述';
    if(fjFlag){
        paramDesc='参数名称';
    }
    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 50,
            editor: {
                allowBlank: false
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    backValue = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch ? "<br>验证规则：" + record.get('ruleName') : "";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" + record.get('parameterName')
                    + "<br>默认值：" + backValue
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';

                return value;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramType',
            width: 120,
            text: '参数类型',
            editor: {
                xtype: 'combobox',
                store: paramTypeStore,
                queryMode: 'local',
                displayField: 'name',
                valueField: 'name',
                listeners: {
                    change: function (field, newValue, oldValue) {
                        if (oldValue == 'IN-string(加密)' && newValue != 'IN-string(加密)') {
                            var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                            paramDefaultValue.set("paramDefaultValue", "");
                        }
                        if (oldValue != newValue) {
                            var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                            paramDefaultValue.set("paramDefaultValue", "");
                            paramDefaultValue.set("parameterName", "");
                            paramDefaultValue.set("ruleName", "");
                        }
                    }
                }
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                if (value == 'IN-string(加密)') {
                    coun = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    coun = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch ? "<br>验证规则：" + record.get('ruleName') : "";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" + record.get('parameterName')
                    + "<br>默认值：" + coun
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            dataIndex: 'parameterName',
            width: 80,
            text: '枚举名称',
            editable: false,
            editor: {},
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                coun = record.get('paramDefaultValue');

                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>默认值：" + coun
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        }, {
            dataIndex: 'paramDefaultValue',
            width: 80,
            text: '默认值',
            editor: {},
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                let showValue = value;

                let paramType = record.get('paramType');

                if (paramType == 'IN-string(加密)') {
                    var decodeVal = getSMEncode(value,0);
                    if(!(decodeVal == null || decodeVal == '')){
                        value = decodeVal;
                    }
                    let xing = "";
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }
        },
        {
            dataIndex: 'ruleName',
            width: 120,
            text: '验证规则',
            hidden: !bhParameterCheckSwitch,
            editor: {
                xtype: 'combobox',
                store: store,
                queryMode: 'local',
                displayField: 'ruleName',
                valueField: 'ruleName',
                editable: false
            }
        },
        {
            text: paramDesc,
            dataIndex: 'paramDesc',
            flex: 1,
            editor: {
                allowBlank: true
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    backValue = record.get('paramDefaultValue');
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>默认值：" + backValue
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';

                return value;
            }
        }];
    var paramRuleTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name', 'id'],
        data: [{
            "name": "VARCHAR",
            "id": 0
        }, {
            "name": "INTEGER",
            "id": 1
        }, {
            "name": "DECIMAL",
            "id": 2
        }, {
            "name": "TIMESTAMP",
            "id": 3
        }, {
            "name": "CLOB",
            "id": 4
        }, {
            "name": "DATE",
            "id": 5
        }, {
            "name": "LONG",
            "id": 6
        }]
    });

    var paramRuleCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
        store: paramRuleTypeStore,
        queryMode: 'local',
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'id',
        triggerAction: "all"
    });
    var paramRulesColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '顺序',
            dataIndex: 'paramRuleOrder',
            width: 50,
            editor: {
                allowBlank: false,
                xtype: 'numberfield',
                maxValue: 30,
                minValue: 1
            },
            renderer: function (value, metaData, record, rowIdx,
                                colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String
                        .htmlEncode(/*
												 * " 输入：" +
												 * record.get('paramRuleIn') + "<br>
												 */"输出列名称："
                            + record
                                .get('paramRuleOut')
                            + "<br>排序："
                            + record
                                .get('paramRuleOrder')
                            + "<br>别名："
                            + record
                                .get('paramRuleDesc'))
                    + '"';
                return value;
            }
        }, {
            text: '分隔符',
            dataIndex: 'paramRuleIn',
            width: 85,
            editor: {},
            hidden: true
        }, {
            text: '输出列名称',
            dataIndex: 'paramRuleOut',
            width: 140,
            editor: {
                allowBlank: false
            }
        },
        {
            text: '类型',
            dataIndex: 'paramRuleType',
            width: 85,
            editor: paramRuleCombo,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode(" 类型：" + record.get('paramRuleType')) + '"';
                if (value == 0) {
                    value = "VARCHAR";
                } else if (value == 1) {
                    value = "INTEGER";
                } else if (value == 2) {
                    value = "DECIMAL";
                } else if (value == 3) {
                    value = "TIMESTAMP";
                } else if (value == 4) {
                    value = "CLOB";
                } else if (value == 5) {
                    value = "DATE";
                } else if (value == 6) {
                    value = "LONG";
                } else {
                    value = "VARCHAR"
                }
                return value;
            }
        }, {
            text: '长度',
            dataIndex: 'paramRuleLen',
            width: 85,
            value: 50,
            editor: {
                xtype: 'numberfield',
                maxValue: 4000,
                minValue: 1
            }
        }, {
            text: '别名',
            dataIndex: 'paramRuleDesc',
            flex: 1,
            editor: {
                allowBlank: true
            }
        }];

    function removeByValue(arr, val) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == val) {
                arr.splice(i, 1);
                break;
            }
        }
    }

    var attachmentColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '附件名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        /*{
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    },*/
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function (grid, rowIndex, colIndex) {

                    var rec = attachmentStore.getAt(rowIndex);
                    Ext.Msg.confirm("请确认", "是否真的要删除附件？", function (button, text) {
                        if (button == "yes") {
                            if (hasVersionForUpdateScriptEdit == 1) {
                                Ext.Msg.confirm("请确认", "该脚本服务已经上线，删除附件会生成无版本号版本", function (button, text) {
                                    if (button == "yes") {
                                        removeByValue(attachmentIds, rec.get('iid'));
                                        save(0);
                                        hasVersionForUpdateScriptEdit = 0;
                                    }
                                });
                            } else {
                                var a = [];
                                a.push(rec.get('iid'));
                                Ext.Ajax.request({
                                    url: 'deleteScriptAttachment.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                        iids: a
                                    },
                                    success: function (response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            removeByValue(attachmentIds, rec.get('iid'));
                                            attachmentStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "保存失败！");
                                    }
                                });
                            }
                        }
                    });
                }

            },
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attachmentStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttachment.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];


    Ext.define('paramManangerModel2', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'paravalue',
                type: 'string'
            }]
    });

    var defaultValueStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'paramManangerModel2',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    defaultValueStore.on('beforeload', function (store, options) {
        var new_params = {
            paramName: golbalParamName
        };
        Ext.apply(defaultValueStore.proxy.extraParams, new_params);
    });
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var cellEditing3 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
//    var selModelAtta = Ext.create('Ext.selection.CheckboxModel', {
//        checkOnly: true
//    });
    var paramGrid = Ext.create('Ext.grid.Panel', {
        width: '100%',
        region: 'center',
        cls: 'customize_panel_back',
        title: "脚本参数",
        store: paramStore,
        margin: '0 0 5 0',
        selModel: selModel,
        emptyText: '没有脚本参数',
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns,
        tools: [
            {
                type: 'variable',
                tooltip: '变量清单',
                handler: envFlowConButton
            },
            {
            type: 'plus',
            tooltip: '增加',
            handler: addParam
        },
            {
                type: 'minus',
                tooltip: '删除',
                callback: function (panel, tool, event) {
                    var data = paramGrid.getView().getSelectionModel().getSelection();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的行!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要删除参数？", function (button, text) {
                            if (button == "yes") {
                                if (hasVersionForUpdateScriptEdit == 1) {
                                    Ext.Msg.confirm("请确认", "该脚本服务已经上线，删除参数会生成无版本号版本", function (button, text) {
                                        if (button == "yes") {

                                            //计数
                                            var indexCount = 0;
                                            var deleteIds = [];
                                            //保存参数的iid与排序序号，为已保存的参数修改使用
                                            var updateValue = [];
                                            var needSortOrder = false;
                                            $.each(data, function (index, record) {
                                                // 当要删除的参数有一个是已经存在的参数时，就需要重新排序 否则不需要
                                                if (!record.dirty) {
                                                    needSortOrder = true;
                                                }
                                                if (record.data.iid > 0) {
                                                    deleteIds.push(record.data.iid);
                                                } else {
                                                    paramStore.remove(data);
                                                }
                                            });
                                            // 验证是否拥有删除权限
                                            Ext.Ajax.request({
                                                url: 'deleteScriptParams.do',
                                                method: 'POST',
                                                sync: true,
                                                params: {
                                                    iids: [],
                                                },
                                                success: function (response, request) {
                                                    var success = Ext.decode(response.responseText).success;
                                                    if (success) {
                                                        if (deleteIds.length > 0) {
                                                            paramStore.remove(data);
                                                            if (needSortOrder) {
                                                                //删除脚本参数后，刷新序号
                                                                paramGrid.getStore().each(function (record, thisindex) {
                                                                    var thisVal = paramGrid.getStore().getAt(thisindex);
                                                                    var storeval = paramGrid.getStore();
                                                                    var roval = storeval.getCount();
                                                                    //未保存时，参数倒叙排列，需要使用下面方式计算
                                                                    var orderNums = roval - thisindex;
                                                                    //保存后，参数正序排列，使用index正常排序即可
                                                                    if (thisVal.get("iid") > 0) {
                                                                        for (var it = 0; it < deleteIds.length; it++) {
                                                                            //删除的参数不参与排序，不做处理
                                                                            if (deleteIds[it] == thisVal.get("iid")) {
                                                                                orderNums = thisindex + 1;
                                                                                break;
                                                                            }
                                                                            if ((deleteIds[it] != thisVal.get("iid")) && (it == deleteIds.length - 1)) {
                                                                                //未删除的数据做排序处理
                                                                                orderNums = ++indexCount;
                                                                            }
                                                                        }
                                                                    }
                                                                    thisVal.set("paramOrder", orderNums);
                                                                    if (thisVal.get("iid") > 0) {
                                                                        updateValue.push(thisVal.get("iid") + "aoms" + orderNums);
                                                                    }
                                                                });
                                                            }
                                                            //更新现有参数的顺序
                                                            if (updateValue != null && updateValue.length > 0) {
                                                                Ext.Ajax.request({
                                                                    url: 'updateScriptParamsOrder.do',
                                                                    method: 'POST',
                                                                    sync: true,
                                                                    params: {
                                                                        idorder: updateValue
                                                                    },
                                                                    success: function (response, request) {
                                                                        var success = Ext.decode(response.responseText).success;
                                                                        if (success) {
                                                                            if (deleteIds.length > 0) {
                                                                                save(0);
                                                                                hasVersionForUpdateScriptEdit = 0;
                                                                            } else {
                                                                                paramGrid.getView().refresh();
                                                                            }
                                                                        } else {
                                                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                                        }
                                                                    },
                                                                    failure: function (result, request) {
                                                                        secureFilterRs(result, "删除失败！");
                                                                    }
                                                                });
                                                            } else {
                                                                paramGrid.getView().refresh();
                                                            }
                                                        } else {
                                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                        }
                                                    } else {
                                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                    }
                                                },
                                                failure: function (result, request) {
                                                        secureFilterRs(result, "删除失败！");
                                                }
                                            });
                                        }
                                    });
                                } else{
                                    //计数
                                    var indexCount = 0;
                                    var deleteIds = [];
                                    //保存参数的iid与排序序号，为已保存的参数修改使用
                                    var updateValue = [];
                                    var needSortOrder = false;
                                    $.each(data, function (index, record) {
                                        // 当要删除的参数有一个是已经存在的参数时，就需要重新排序 否则不需要
                                        if (!record.dirty) {
                                            needSortOrder = true;
                                        }
                                        if (record.data.iid > 0) {
                                            deleteIds.push(record.data.iid);
                                        } else {
                                            paramStore.remove(data);
                                        }
                                    });
                                    if (deleteIds.length > 0) {
                                        Ext.Ajax.request({
                                            url: 'deleteScriptParams.do',
                                            method: 'POST',
                                            sync: true,
                                            params: {
                                                iids: deleteIds
                                            },
                                            success: function (response, request) {
                                                var success = Ext.decode(response.responseText).success;
                                                if (success) {
                                                    Ext.Msg.alert('提示', '删除成功！');
                                                    paramStore.remove(data);
                                                    //更新现有参数的顺序
                                                    if (needSortOrder) {
                                                        //删除脚本参数后，刷新序号
                                                        paramGrid.getStore().each(function (record, thisindex) {
                                                            var thisVal = paramGrid.getStore().getAt(thisindex);
                                                            var storeval = paramGrid.getStore();
                                                            var roval = storeval.getCount();
                                                            //未保存时，参数倒叙排列，需要使用下面方式计算
                                                            var orderNums = roval - thisindex;
                                                            //保存后，参数正序排列，使用index正常排序即可
                                                            if (thisVal.get("iid") > 0) {
                                                                for (var it = 0; it < deleteIds.length; it++) {
                                                                    //删除的参数不参与排序，不做处理
                                                                    if (deleteIds[it] == thisVal.get("iid")) {
                                                                        orderNums = thisindex + 1;
                                                                        break;
                                                                    }
                                                                    if ((deleteIds[it] != thisVal.get("iid")) && (it == deleteIds.length - 1)) {
                                                                        //未删除的数据做排序处理
                                                                        orderNums = ++indexCount;
                                                                    }
                                                                }
                                                            }
                                                            thisVal.set("paramOrder", orderNums);
                                                            if (thisVal.get("iid") > 0) {
                                                                updateValue.push(thisVal.get("iid") + "aoms" + orderNums);
                                                            }
                                                        });
                                                    }
                                                    if (updateValue != null && updateValue.length > 0) {
                                                        Ext.Ajax.request({
                                                            url: 'updateScriptParamsOrder.do',
                                                            method: 'POST',
                                                            sync: true,
                                                            params: {
                                                                idorder: updateValue
                                                            },
                                                            success: function (response, request) {
                                                                var success = Ext.decode(response.responseText).success;
                                                                if (success) {
                                                                    paramGrid.getView().refresh();
                                                                } else {
                                                                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                                }
                                                            },
                                                            failure: function (result, request) {
                                                                secureFilterRs(result, "删除失败！");
                                                            }
                                                        });
                                                    } else {
                                                        paramGrid.getView().refresh();
                                                    }
                                                } else {
                                                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                }
                                            },
                                            failure: function (result, request) {
                                                secureFilterRs(result, "删除失败！");
                                            }
                                        });
                                    }
                                }
                            }
                        });
                    }
                }
            }],
        listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {

                var columnIndex = e.column.dataIndex;
                // 点击的当前行数据
                var recordData = e.record.data;

                var paramType = recordData.paramType;           // 是否为枚举类型
                var parameterName = recordData.parameterName;   // 参数名称
                // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue";
                var columnBooParameterName = columnIndex == "parameterName";
                var columnBooparamDefaultValue = columnIndex == "paramDefaultValue"
                // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                    golbalParamName = parameterName;
                    defaultValueStore.load();
                }
                // 判断如果为枚举类型，并且当前操作列为“参数名称”，设置单元格为下拉框
                if (paramType == "枚举" && columnBooParameterName) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "parameterName",
                        displayField: "parameterName",
                        store: enumValueStore,
                        editable: false,
                        listeners: {
                            change: function (field, newValue, oldValue) {
                                if (oldValue != newValue) {
                                    var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                                    paramDefaultValue.set("paramDefaultValue", "");
                                }
                            }
                        }
                    });
                }
                if (paramType == "枚举" && columnBooparamDefaultValue) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore,
                        editable: false
                    });
                }
                // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                if (paramType != "枚举" && columnBoo) {
                    e.column.setEditor({
                        xtype: 'textfield',
                        readOnly: columnIndex == "parameterName" ? true : false,

                    })
                }

                if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {

                    // let mimi = Ext.create('Ext.grid.CellEditor', {
                    //     field: Ext.create('Ext.form.field.Text', {
                    //         // selectOnFocus: true,
                    //         inputType: 'password'
                    //     })
                    // })
                    // e.column.setEditor


                    let pass = new Ext.form.TextField({
                        inputType: 'password',
                        // listeners:{
                        //     renderer:function (a,b,c,d){
                        //         alert(a);
                        //     }
                        // }
                    });

                    e.column.setEditor(pass)
                }
            }
        }
    });
    var selectedAttachmentButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加附件',
            handler: selectAttachmentFun
        });

    var selectedTemplateButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加模板',
            handler: selectTempFun
        });
    /*var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	region: 'south',
    	cls: 'attachments customize_panel_back',
        height: 200,
        title: '附件',
        store: attachmentStore,
        border: true,
        columnLines: true,
        emptyText: '没有附件',
        columns: attachmentColumns,
        dockedItems : [{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items:[
				'->',selectedAttachmentButton//添加附件按钮
			]
		}]
    });*/

    //获取所有模板
    var attaTempStore = Ext.create('Ext.data.Store', {
        autoLoad: templateSwitch,
        autoDestroy: true,
        pageSize: 10,
        model: 'attaTempModelEdit',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttaTemplate.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var tempColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '模板名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attaTempStore.getAt(rowIndex);
                    Ext.Msg.confirm("请确认", "是否真的要删除模板？", function (button, text) {
                        if (button == "yes") {
                            var a = [];
                            a.push(rec.get('iid'));
                            Ext.Ajax.request({
                                url: 'deleteScriptAttaTemplate.do',
                                method: 'POST',
                                sync: true,
                                params: {
                                    iids: a
                                },
                                success: function (response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    if (success) {
                                        Ext.Msg.alert('提示', '删除成功！');
                                        removeByValue(tempmentIds, rec.get('iid'));
                                        attaTempStore.load();
                                        if (newServiceId != 0) {
                                            save(0);
                                        }
                                    } else {
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                    }
                                },
                                failure: function (result, request) {
                                    secureFilterRs(result, "保存失败！");
                                }
                            });
                        }
                    });

                }
            },
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attaTempStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttaTemplate.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];

    var attachmentGrid1 = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: attachmentStore,
        viewConfig: {
            enableTextSelection: true
        },
        border: true,
        columnLines: true,
        margin: '0 10 5 10',
        columns: attachmentColumns,
        emptyText: '没有附件',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [
                '->', selectedAttachmentButton//添加附件按钮
            ]
        }]
    });

    var attachmentGrid2 = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: attaTempStore,
        border: true,
        columnLines: true,
        viewConfig: {
            enableTextSelection: true
        },
        columnLines: true,
        columns: tempColumns,
        margin: '0 10 5 10',
        emptyText: '没有附件',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [
                '->', selectedTemplateButton//添加附件按钮
            ]
        }]
    });

    var pagetab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            //cls:'customize_panel_back',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            region: 'center',
            activeTab: 0,
            //width : '100%',
            height: contentPanel.getHeight() * 0.25,
            border: false,
//			    autoScroll: true,
            items: [
                {
                    title: '附件',
                    layout: 'fit',
                    items: [attachmentGrid1]
                },
                {
                    title: '模板',
                    layout: 'fit',
                    hidden: !templateSwitch,
                    items: [attachmentGrid2]
                }
            ]
        });

    var attachmentGrid = Ext.create('Ext.panel.Panel', {
        region: 'south',
        margin: '0 0 5 0',
        cls: 'attachments customize_panel_back panel_space_top',
        items: [pagetab/*attachmentGrid*/]

    });

    /********************/
    attaTempStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: tempmentIds
        };
        Ext.apply(attaTempStore.proxy.extraParams, new_params);
    });
    attaTempStore.on('load', function (me, records, successful, eOpts) {
        tempmentIds = [];
        $.each(records, function (index, record) {
            tempmentIds.push(record.get('iid'));
        });
    });
    /********************/

    var selModel_outruleGrid = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var outruleGrid = Ext.create('Ext.grid.Panel', {
        selModel: selModel_outruleGrid,
        region: 'south',
        height: 250,
        hidden: true,
        margin: '0 0 5 0',
        title: '输出规则',
        cls: 'customize_panel_back',
        plugins: [cellEditing2],
        store: paramRulesStore,
        border: true,
        columnLines: true,
        columns: paramRulesColumns,
        emptyText: '没有规则参数',
        tools: [
            {
                type: 'plus',
                tooltip: '增加',
                handler:
                addOutPara
//						function() {
//						paramRulesStore.insert(1,new paramRuleModel());
//						cellEditing2.startEditByPosition({row : 0,column : 0});
//					}
            },
            {
                type: 'minus',
                tooltip: '删除',
                callback: function (panel, tool,
                                    event) {
                    var data = outruleGrid.getView().getSelectionModel().getSelection();
                    if (updateScript_Status == 1) {
                        Ext.Msg.alert('提示', '已上线的脚本不允许删除');
                        return;
                    }
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的行!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要删除所选记录？",
                            function (button, text) {
                                if (button == "yes") {
                                    var deleteIds = [];
                                    $.each(data, function (index, record) {
                                        if (record.data.iid > 0) {
                                            deleteIds.push(record.data.iid);
                                        } else {
                                            paramRulesStore.remove(data);
                                        }
                                    });
                                    if (deleteIds.length > 0) {
                                        Ext.Ajax.request({
                                            url: 'deleteScriptRuleParams.do',
                                            method: 'POST',
                                            sync: true,
                                            params: {
                                                iids: deleteIds,
                                                iflag: "0"
                                            },
                                            success: function (response, request) {
                                                var success = Ext.decode(response.responseText).success;
                                                if (success) {
                                                    Ext.Msg.alert('提示', '删除成功！');
                                                    paramRulesStore.load();
                                                } else {
                                                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                }
                                            },
                                            failure: function (result, request) {
                                                secureFilterRs(result, "保存失败！");
                                            }
                                        });
                                    } else {
                                        outruleGrid.getView().refresh();
                                    }
                                }
                            });
                    }
                }
            }]
    });

    var scriptoutput = Ext.create('Ext.form.Panel', {
        region: 'south',
        hidden:!scriptFunctionOutputSwitch,
        width: '100%',
        cls: 'customize_panel_back  bottom_margin',
        height: 60,
        // border: true,
//         layout: 'anchor',
        margin: '0 0 5 0',
        collapsible: false,
        title: '输出定义',
        tools: [
            {
                type: 'print',
                tooltip: '输出定义',
                handler: function (event, toolEl, panelHeader) {
                    var height = 600;
                    var width = 800;
                    if (scriptuuid==''){
                        Ext.Msg.alert('提示', '请先保存脚本再配置输出定义');
                        return;
                    }
                    Ext.define('scriptoutputModel', {
                        extend:'Ext.data.Model',
                        fields:[{
                            name:'iorder',
                            type:'int'
                        },{
                            name:'iid',
                            type:'long'
                        },{
                            name:'ialias',
                            type:'string'
                        },{
                            name:'ivalue',
                            type:'string'
                        }]
                    })
                    var scriptOutputStore = Ext.create('Ext.data.Store', {
                        model:'scriptoutputModel',
                        proxy:{
                            url:'getScriptOutputList.do',
                            type:'ajax'
                        }
                    })

                    scriptOutputStore.on('beforeload', function(){
                        Ext.apply(scriptOutputStore.proxy.extraParams, {
                            scriptuuid : scriptuuid
                        })
                    })
                    scriptOutputStore.load();
                    var scriptOutputGrid = Ext.create('Ext.grid.Panel', {
                        region: 'center',
                        cls: 'customize_panel_back',
                        store: scriptOutputStore,
                        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
                        // ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                        padding: grid_space,
                        columnLines: true,
                        forceFit: true,
                        dockedItems:[{
                            xtype:'toolbar',
                            items:['->', {
                                xtype:'button',
                                text:'增加',
                                cls:'Common_Btn',
                                handler:function(){
                                    var items = scriptOutputStore.data.items;
                                    var maxOrder = 0;
                                    for (let i = 0; i < items.length; i++) {
                                        if (items[i].data.iorder > maxOrder){
                                            maxOrder = items[i].data.iorder
                                        }
                                    }

                                    scriptOutputStore.insert(0, Ext.create('scriptoutputModel', {
                                        iid:0,
                                        iorder:++maxOrder,
                                        ialias:'',
                                        ivalue:''
                                    }))
                                }
                            },{
                                xtype:'button',
                                text:'删除',
                                cls:'Common_Btn',
                                handler:function(){
                                    var m = scriptOutputGrid.getSelectionModel().getSelection();
                                    var ids = [];
                                    for (let i = 0; i < m.length; i++) {
                                        if (m[i].data.iid != 0){
                                            ids.push(m[i].data.iid);
                                        }else{
                                            scriptOutputStore.remove(m[i]);
                                        }
                                    }
                                    if (ids.length>0){
                                        Ext.Ajax.request({
                                            url:'delScriptOutput.do',
                                            method:'POST',
                                            params:{
                                                ids:ids
                                            },
                                            success:function (response){
                                                var data = Ext.decode(response.responseText);
                                                Ext.Msg.alert('提示', data.message);
                                                if (data.success){
                                                    scriptOutputStore.load();
                                                }
                                            },
                                            failure:function(){
                                                Ext.Msg.alert('提示', '网络连接失败');
                                            }
                                        })
                                    }
                                }
                            },{
                                xtype:'button',
                                text:'保存',
                                cls:'Common_Btn',
                                handler:function(){
                                    var m = scriptOutputStore.getModifiedRecords();
                                    if(m.length==0){
                                        Ext.Msg.alert('提示','没有数据需要保存');
                                        return;
                                    }
                                    for (let i = 0; i < m.length; i++) {
                                        if (m[i].data.ialias == ''){
                                            Ext.Msg.alert('提示', '列标题不能为空');
                                            return;
                                        }
                                        if (m[i].data.ivalue == ''){
                                            Ext.Msg.alert('提示', '列ID不能为空');
                                            return;
                                        }
                                    }
                                    var n = scriptOutputStore.getNewRecords();
                                    var newRecords = [];
                                    for (let i = 0; i < n.length; i++) {
                                        newRecords.push(n[i].data);
                                    }
                                    var o = scriptOutputStore.getUpdatedRecords();
                                    var updateRecords = [];
                                    for (let i = 0; i < o.length; i++) {
                                        updateRecords.push(o[i].data);
                                    }
                                    Ext.Ajax.request({
                                        url:'saveScriptOutput.do',
                                        method:'POST',
                                        params:{
                                            newRecords:Ext.encode(newRecords),
                                            updateRecords:Ext.encode(updateRecords),
                                            scriptuuid:scriptuuid
                                        },
                                        success:function (response){
                                            var data = Ext.decode(response.responseText);
                                            Ext.Msg.alert('提示', data.message);
                                            if (data.success){
                                                scriptOutputStore.load();
                                            }
                                        },
                                        failure:function(){
                                            Ext.Msg.alert('提示', '网络连接失败');
                                        }
                                    })
                                }
                            }]
                        }],
                        columns: [{
                            dataIndex:'iorder',
                            text:'排序',
                            editor:{
                                xtype:'numberfield',
                                step:1,
                                minValue:1,
                                maxValue:9999,
                                regex:/^[1-9][0-9]{0,3}$/,
                                regexText:'请输入1-9999之间的整数'
                            }
                        },{
                            dataIndex:'ivalue',
                            text:'列ID',
                            editor:{
                                regex:/^[^\u4e00-\u9fa5][^\u4e00-\u9fa5]*$/,
                                regexText:'不支持录入中文',
                                listeners:{
                                    blur:function (t, even, ops) {
                                        if (checkLength(t.value) > 255){
                                            Ext.Msg.alert('提示','最大输入字符长度255');
                                            Ext.getCmp(t.id).setValue('');
                                        }
                                    }
                                }
                            }
                        },{
                            dataIndex:'ialias',
                            text:'列标题',
                            editor:{
                                listeners:{
                                    blur:function (t, even, ops) {
                                        if (checkLength(t.value) > 255){
                                            Ext.Msg.alert('提示','最大输入字符长度255');
                                            Ext.getCmp(t.id).setValue('');
                                        }
                                    }
                                }
                            }
                        }],
                        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
                        height: height - 80,
                        width: '100%'
                    })
                    var scriptOutputWin = Ext.create('Ext.window.Window', {
                        modal:true,
                        height:height,
                        width:width,
                        title:'输出定义',
                        draggable: false,
                        resizable: false,
                        items:[scriptOutputGrid]
                    }).show();
                }
            }
        ]
    });

    var selModelDependScript = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        mode: 'SINGLE',
        allowDeselect: true
    });

    // ==============光大开关去掉附件面板
    var paramItems = [funcDescForm, paramGrid, scriptoutput, outruleGrid, attachmentGrid];
    if (scriptEditBookSwitch){
        var treeStore = Ext.create('Ext.data.TreeStore', {
            autoLoad: JlnxScriptClassSwitch,
            fields:['iid', 'iname', 'iparentid'],
            proxy: {
                type: 'ajax',
                url: 'variableBase/getClassList.do?classType=1'
            },
            root: {
                expanded: JlnxScriptClassSwitch
            }
        });
        var variableFormPanel = Ext.create('Ext.form.Panel', {
            width: '40%',
            height: '100%',
            hidden: !JlnxScriptClassSwitch,
            items: [{
                border: false,
                layout: 'column',
                margin: '5',
                items: [{
                    width: 240,
                    minPickerHeight: 20,
                    id: 'variableClassId',
                    emptyText: '变量分类搜索',
                    displayField: 'iname',
                    valueField: 'iparentid',
                    editable: false,
                    xtype: 'treepicker',
                    value: '',
                    store: treeStore
                }]
            }]
        })
        //关键字
        var keywordNameVar = new Ext.form.TextField({
            emptyText: '关键字：可输入变量名称、描述',
            labelWidth: 65,
//		padding : '5',
            width: 300,
            labelAlign: 'right',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        variableTab.ipage.moveFirst();
                    }
                }
            }
        });

        //关键字
        var keywordNameFunc = new Ext.form.TextField({
            emptyText: '关键字：可输入函数名称、说明',
            labelWidth: 65,
//		padding : '5',
            width: 300,
            labelAlign: 'right',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        functionTab.ipage.moveFirst();
                    }
                }
            }
        });

        Ext.define('gridListModelVariable', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long',
            }, {
                name: 'bindId',
                type: 'long',
            }, {
                name: 'iname',
                type: 'string'
            }, {
                name: 'itype',
                type: 'string'
            }, {
                name: 'ivalue',
                type: 'string'
            }, {
                name: 'idesc',
                type: 'string'
            }, {
                name: 'iattribute',
                type: 'string'
            }]
        });


        //绑定状态下拉内容
        var varBindStateVal = Ext.create('Ext.data.Store', {
            fields: ['id', 'name'],
            data: [{
                "id": "",
                "name": "全部"
            }, {
                "id": "1",
                "name": "已绑定"
            },
                {
                    "id": "2",
                    "name": "未绑定"
                }
            ]
        });

        //绑定状态
        let varBindState = Ext.create('Ext.form.field.ComboBox', {
            fieldLabel: "绑定状态",
            labelWidth: 65,
            width: JlnxScriptClassSwitch?'34%':'35%',
            labelAlign: 'right',
            store: varBindStateVal,
            value: '',
            queryMode: 'local',
            displayField: 'name',
            editable: true,
            valueField: 'id',
            xtype: 'combobox'
        });

        //变量store
        var variableStore = Ext.create('Ext.data.Store', {
            autoLoad: scriptEditBookSwitch || !funVariShowSwtich,
            autoDestroy: true,
            pageSize: 30,
            model: 'gridListModelVariable',
            proxy: {
                type: 'ajax',
                url: 'funcAndVarUseNoteBookQuery.do',
                reader: {
                    type: 'json',
                    root: 'blList',
                    totalProperty: 'varCount'
                }
            }
        });

        variableStore.on('beforeload', function (store, options) {
            var new_params = {
                keywordName: keywordNameVar.getValue(),
                getType:'0',
                classId:Ext.getCmp('variableClassId').getValue() == ''?-1:Ext.getCmp('variableClassId').getValue(),
                uuid:uuidForUpdateScriptEdit,
                bindState:varBindState.getValue(),
            };
            Ext.apply(variableStore.proxy.extraParams, new_params);
        });

        Ext.define('gridListModelFucntion', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long',
            }, {
                name: 'bindId',
                type: 'long',
            },{
                name: 'iname',
                type: 'string'
            }, {
                name: 'ilanguagetype',
                type: 'string'
            }, {
                name: 'idesc',
                type: 'string'
            }, {
                name: 'iattribute',
                type: 'string'
            }]
        });

        //函数store
        var functionStore = Ext.create('Ext.data.Store', {
            autoLoad: scriptEditBookSwitch || !funVariShowSwtich,
            autoDestroy: true,
            pageSize: 30,
            model: 'gridListModelFucntion',
            proxy: {
                type: 'ajax',
                url: 'funcAndVarUseNoteBookQuery.do',
                reader: {
                    type: 'json',
                    root: 'hsList',
                    totalProperty: 'funCount'
                }
            }
        });

        functionStore.on('beforeload', function (store, options) {
            var new_params = {
                keywordName: keywordNameFunc.getValue(),
                language:language,                          //获取支持语言内容
                getType:'1',
                functionId:Ext.getCmp('functionClassId').getValue() == ''?-1:Ext.getCmp('functionClassId').getValue(),
                uuid:uuidForUpdateScriptEdit,
                bindState:funcBindState.getValue(),         //获取绑定状态下拉选内容
            };
            Ext.apply(functionStore.proxy.extraParams, new_params);

        });

        //变量展示列
        var variableColumns = [{
            text: '序号',
            xtype: 'rownumberer',
            width:'5%'
        },{
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
            {
                text: '变量名称',
                dataIndex: 'iname',
                width: '35%',
                renderer: function (value, metaData, record) {
                    var iid = record.get("iid");
                    var idesc = record.get("idesc")
                    var attribute = record.get('iattribute')
                    return '<a href="#" onclick="showVariable(\''+ value +'\', \'' + idesc + '\', \'' + attribute + '\')">' + value + '</a>';
                }
            },
            {
                text: '描述',
                dataIndex: 'idesc',
                width: '55%',
                renderer: function (value, metaData, record) {
                    return '<span title="'+value+'">' + value + '</span>';
                }
            },{
                hidden:true,
                text: '属性',
                dataIndex: 'iattribute',
                width: '5%',
                renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == '1') {
                        backValue = "自定义";
                    } else if (value == '2') {
                        backValue = "内置";
                    }
                    return backValue;
                }
            },
            {
                hidden:true,
                text: '类型',
                dataIndex: 'itype',
                width: '58%',
                renderer: function (value, metaData, record) {
                    return '<span title="'+value+'">' + value + '</span>';
                }
            }];

        //函数展示列
        var functionColumns = [{
            text: '序号',
            xtype: 'rownumberer',
            width:'5%'
        },{
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
            {
                text: '函数名称',
                dataIndex: 'iname',
                width: '35%',
                renderer: function (value, metaData, record) {
                    var iid = record.get("iid");
                    return '<a href="#" onclick="showFunction('+iid+')">' + value + '</a>';
                }
            },
            {
                text: '说明',
                dataIndex: 'idesc',
                width: '55%',
                renderer: function (value, metaData, record) {
                    return '<span title="'+value+'">' + value + '</span>';
                }
            },{
                hidden:true,
                text: '属性',
                dataIndex: 'iattribute',
                width: '5%',
                renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == '1') {
                        backValue = "自定义";
                    } else if (value == '2') {
                        backValue = "内置";
                    }
                    return backValue;
                }
            },
            {
                hidden:true,
                text: '类型',
                dataIndex: 'itype',
                width: '58%',
                renderer: function (value, metaData, record) {
                    return '<span title="'+value+'">' + value + '</span>';
                }
            }];

        //变量tab绑定状态下拉选变化监听，自动刷新
        varBindState.on('change', function (combo, newValue, oldValue, eOpts) {
            // 执行数据筛选
            variableTab.ipage.moveFirst();
        });


        //变量展示tab页
        variableTab = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            // overflowY: 'hidden',
            store: variableStore,
            width:640,
            margin:'0 0 0 10',
            height: contentPanel.getHeight()-130,
            // selModel: selModelvariable,
            // plugins: [cellEditingvariable],
            selModel:Ext.create('Ext.selection.CheckboxModel',
                {
                    //showHeaderCheckbox : false,
                    //内置变量不显示勾选框
                    checkOnly : true,
                    renderer:function(a,b,record){
                        if(record.get("iattribute") == "2"){
                            return "";
                        }
                        return '<div class="x-grid-row-checker"> </div>';
                    },
                }),
            plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
            //dockedItems: scriptServiceitems,
            border: true,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            // bbar : pageBar,
            columnLines: true,
            layout:'fit',
            cls: 'customize_panel_back',
            // padding: grid_space,
            columns: variableColumns,
            overflowX:false,
            listeners:{
                itemdblclick: function (t, record) {
                    editor.replaceSelection(record.raw.iname);
                    editor.focus();
                },
                select: function (t, record, index) {
                    //用于判断是否为内置
                    if (variableCheckItems.indexOf(record) == -1) {
                        variableCheckItems.push(record);
                    }
                    /*//选中的变量id
                    if (variableId.indexOf(record.get('iid')) == -1) {
                        variableId.push(record.get('iid'));
                    }
                    //默认选中的变量取消后再勾选，从variableDeleteId中移除
                    if (variableId.indexOf(record.get('iid')) > -1) {
                        variableDeleteId.remove(record.get('iid'));
                    }*/
                    // 检查数据是否在 varBindDelete 数组中，如果存在则从数组中删除，此过程在保存之前，避免点着玩，delete数组内容过大的情况
                    var index = varBindDelete.findIndex(item => item.data.iid === record.data.iid);
                    if (index !== -1) {
                        varBindDelete.splice(index, 1);
                        variableDeleteId.remove(record.get('iid'));
                    }
                    if (!varBindInsert.some(item => item.data.iid === record.data.iid)) {
                            varBindInsert.push(record);
                            variableId.push(record.get('iid'));
                    }
                },
                deselect: function (t, record, index) {

                    var index = variableCheckItems.findIndex(item => item.data.iid === record.data.iid);
                    if (index !== -1) {
                        variableCheckItems.splice(index, 1);
                    }


                    // 检查数据是否在 varBindInsert 数组中，如果存在则从数组中删除，此过程在保存之前，避免点着玩,insert数组内容过大的情况
                    var index = varBindInsert.findIndex(item => item.data.iid === record.data.iid);
                    if (index !== -1) {
                        varBindInsert.splice(index, 1);
                        variableId.remove(record.get('iid'));
                    }

                    let isAlready = alreadyBind.findIndex(item => item.data.iid === record.data.iid);
                    if(isAlready !== -1){
                        if (!varBindDelete.some(item => item.data.iid === record.data.iid)) {
                            varBindDelete.push(record);
                            variableDeleteId.push(record.get('iid'));
                        }
                    }

                },
                //afterlayout需要在select和deselect后
                afterlayout: function (grid) {
                    var records = grid.getStore().getRange();
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        //如果变量id和ieai_script_bind_func_var表中变量id相同并且该id不在移除id中加上勾选状态
                        //record.get('bindId')取值需要在model中加上bindId属性
                        if(record.get('iid') == record.get('bindId') &&!variableDeleteId.includes(record.get('bindId'))){
                            variableId.push(record.get('iid'));
                            //id去重
                            variableId=Array.from(new Set(variableId))
                            //加上勾选状态
                            grid.getSelectionModel().select(records[i], true);

                            let already = alreadyBind.findIndex(item => item.data.iid === record.data.iid);
                            if(already == -1){
                                record.data.ibandType = 1; // 将 ibandType 字段添加到 data 对象中
                                alreadyBind.push(record);
                                alreadyBind=Array.from(new Set(alreadyBind));
                            }
                            //在已经绑定过的数据添加到数组中，防止初始添加会有重复
                            bindInsertArray.push({
                                iname: record.data.iname,
                                bindVarFuncId: record.data.iid,
                                ibandType: 1
                            });
                            //数组去重
                            bindInsertArray=Array.from(new Set(bindInsertArray));
                        }else{
                            //手动勾选的数据
                            for(var j=0 ; j<variableId.length ; j++){
                                if (variableId[j] == record.get("iid")) {
                                    grid.getSelectionModel().select(records[i], true);
                                }
                            }
                        }
                    }
                }
            },
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [variableFormPanel,
                    keywordNameVar,
                    varBindState,
                    {
                        xtype:'button',
                        cls:'Common_Btn',
                        text:'查询',
                        handler:function(){
                            variableTab.ipage.moveFirst();
                        }
                    },
                ]
            },{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [{
                    xtype: 'label',
                    fieldLabel: '变量可在脚本中直接使用，支持脚本语言:shell、python、powershell。',
                    html: '变量可在脚本中直接使用，支持脚本语言:shell、python、powershell。',
                    margin: '0 0 0 0'
                }
                ]
            }]

        });
        var functionLibarayTreeStore = Ext.create('Ext.data.TreeStore', {
            autoLoad: JlnxScriptClassSwitch,
            fields:['iid', 'iname', 'iparentid'],
            proxy: {
                type: 'ajax',
                url: 'variableBase/getClassList.do?classType=2'
            },
            root: {
                expanded: JlnxScriptClassSwitch
            }
        });
        var functionLibraryFormPanel = Ext.create('Ext.form.Panel', {
            width: '40%',
            height: '100%',
            hidden: !JlnxScriptClassSwitch,
            items: [{
                border: false,
                layout: 'column',
                margin: '5',
                items: [{
                    width: 240,
                    minPickerHeight: 20,
                    id: 'functionClassId',
                    emptyText: '函数分类搜索',
                    displayField: 'iname',
                    valueField: 'iparentid',
                    editable: false,
                    xtype: 'treepicker',
                    value: '',
                    store: functionLibarayTreeStore
                }]
            }]
        })


        //绑定状态下拉内容
        var funcBindStateVal = Ext.create('Ext.data.Store', {
            fields: ['id', 'name'],
            data: [{
                "id": "",
                "name": "全部"
            }, {
                "id": "1",
                "name": "已绑定"
            },
                {
                    "id": "2",
                    "name": "未绑定"
                }
            ]
        });

        //绑定状态
        let funcBindState = Ext.create('Ext.form.field.ComboBox', {
            fieldLabel: "绑定状态",
            labelWidth: 65,
            width: JlnxScriptClassSwitch?'34%':'35%',
            labelAlign: 'right',
            store: funcBindStateVal,
            value: '',
            queryMode: 'local',
            displayField: 'name',
            editable: true,
            valueField: 'id',
            xtype: 'combobox'
        });


        //函数tab绑定状态下拉选变化监听，自动刷新
        funcBindState.on('change', function (combo, newValue, oldValue, eOpts) {
            functionTab.ipage.moveFirst();
        });


        //函数展示tab页
        functionTab = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store: functionStore,
            width:640,
            layout:'fit',
            margin:'0 0 0 10',
            height: contentPanel.getHeight()-130,
            // selModel: selModel,
            // plugins: [cellEditing],
            selModel: Ext.create('Ext.selection.CheckboxModel',
                {
                    checkOnly : true,
                    renderer:function(a,b,record){
                        if(record.get("iattribute") == "2"){
                            return "";
                        }
                        return '<div class="x-grid-row-checker"> </div>';
                    },
                }),
            plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
            //dockedItems: scriptServiceitems,
            border: true,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            // bbar : pageBar,
            columnLines: true,
            cls: 'customize_panel_back',
            // padding: grid_space,
            columns: functionColumns,
            overflowX:false,
            listeners:{
                itemdblclick:function(t, record){
                    editor.replaceSelection(record.raw.iexample);
                    editor.focus();
                },
                select: function (t, record, index) {
                    if (checkItems.indexOf(record) == -1) {
                        checkItems.push(record);
                    }


                    // 检查数据是否在 funcBindDelete 数组中，如果存在则从数组中删除，此过程在保存之前，避免点着玩，delete数组内容过大的情况
                    var index = funcBindDelete.findIndex(item => item.data.iid === record.data.iid);
                    if (index !== -1) {
                        funcBindDelete.splice(index, 1);
                        funDeleteId.remove(record.get('iid'));
                    }

                    if (!funcBindInsert.some(item => item.data.iid === record.data.iid)) {
                        funcBindInsert.push(record);
                        funId.push(record.get('iid'));
                    }
                },
                deselect: function (t, record, index) {
                    if (checkItems.indexOf(record) > -1) {
                        checkItems.remove(record);
                    }

                    // 检查数据是否在 funcBindInsert 数组中，如果存在则从数组中删除，此过程在保存之前，避免点着玩,insert数组内容过大的情况
                    var index = funcBindInsert.findIndex(item => item.data.iid === record.data.iid);
                    if (index !== -1) {
                        funcBindInsert.splice(index, 1);
                        funId.remove(record.get('iid'));
                    }
                    let isAlready = alreadyBind.findIndex(item => item.data.iid === record.data.iid);
                    if(isAlready !== -1){
                        if (!funcBindDelete.some(item => item.data.iid === record.data.iid)) {
                            funcBindDelete.push(record);
                            funDeleteId.push(record.get('iid'));
                        }
                    }
                },
                afterlayout: function (grid) {
                    alreadyBind = [];
                    var records = grid.getStore().getRange();
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if(record.get('iid') == record.get('bindId') &&!funDeleteId.includes(record.get('bindId'))){
                            funId.push(record.get('iid'));

                            funId=Array.from(new Set(funId))
                            grid.getSelectionModel().select(records[i], true);

                            let already = alreadyBind.findIndex(item => item.data.iid === record.data.iid);
                            if(already == -1){
                                record.data.ibandType = 2;
                                alreadyBind.push(record);
                                alreadyBind=Array.from(new Set(alreadyBind));
                            }

                             //在已经绑定过的数据添加到数组中，防止初始添加会有重复
                             bindInsertArray.push({
                                 iname: record.data.iname,
                                 bindVarFuncId: record.data.iid,
                                 ibandType: 2
                             });

                             //数组去重
                             bindInsertArray=Array.from(new Set(bindInsertArray))
                        }else{
                            for(var j=0 ; j<funId.length ; j++){
                                if (funId[j] == record.get("iid")) {
                                    grid.getSelectionModel().select(records[i], true);
                                }
                            }
                        }
                    }
                }
            },

            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [functionLibraryFormPanel,
                    keywordNameFunc,
                    funcBindState,
                    {
                        xtype:'button',
                        cls:'Common_Btn',
                        text:'查询',
                        handler:function(){
                            functionTab.ipage.moveFirst();
                        }
                    }
                ]
            }]
        });
    }


    if(getScriptEditeTabShowSwitch){
        //根据脚本id查询脚本的状态与uuid
        function getScriptByServiceId(){
            var hadPub = false;
            Ext.Ajax.request({
                url: 'getScriptByServiceId.do',
                method: 'POST',
                async: false,
                params: {
                    serviceId: newServiceId
                },
                success: function (response, request) {
                    //根据serviceId获取当前脚本的状态、uuid，如果不为草稿，则无法绑定、解绑脚本
                    var status = Ext.decode(response.responseText).status;
                    var scriptuuid = Ext.decode(response.responseText).scriptuuid;
                    cgUuid = scriptuuid;
                    if(status == -1){
                        hadPub = true;
                    }
                }
            });
            return hadPub;
        }
        //脚本查询条件
        var serviceNameScript = new Ext.form.TextField({
            emptyText: '--请输入服务名称--',
            labelWidth: 65,
            width: '30%',
            labelAlign: 'right'
        });

        var scriptNameScript = new Ext.form.TextField({
            emptyText: '--请输入脚本名称--',
            labelWidth: 65,
            width: '30%',
            labelAlign: 'right'
        });

        //脚本列表
        var scriptColumns = [{
            text: '序号',
            xtype: 'rownumberer',
            width:'5%'
        },
            {
                text: '主键',
                dataIndex: 'iid',
                hidden:true,
                width: '35%'
            },{
                text: '源uuid',
                dataIndex: 'srcScriptUuid',
                hidden:true,
                width: '35%'
            },{
                text: '源脚本id',
                dataIndex: 'srcScriptIid',
                hidden:true,
                width: '35%'
            },{
                text: '源服务名称',
                dataIndex: 'srcServiceName',
                width: '20%',
                renderer: function (value, metadata, record) {
                    var a = record.data.srcServiceName;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return value;
                }
            },{
                text: '源脚本名称',
                dataIndex: 'srcScriptName',
                width: '20%',
                renderer: function (value, metadata, record) {
                    var a = record.data.srcScriptName;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return value;
                }
            },{
                dataIndex: 'dependScriptIid',
                hidden:true,
                text: '脚本id'
            }, {
                dataIndex: 'dependScriptUuid',
                hidden:true,
                text: '脚本uuid'
            }, {
                dataIndex: 'dependScriptType',
                hidden:true,
                text: '脚本类型'
            }, {
                dataIndex: 'dependServiceName',
                width:'20%',
                text: '服务名称',
                renderer: function (value, metadata, record) {
                    var a = record.data.dependServiceName;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return value;
                }
            }, {
                dataIndex: 'dependScriptName',
                width:'20%',
                text: '脚本名称',
                renderer: function (value, metadata, record) {
                    var a = record.data.dependScriptName;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return value;
                }
            }, {
                dataIndex: 'dependScriptVersion',
                width:'10%',
                text: '版本号'
            }, {
                dataIndex: 'dependFlag',
                hidden:true,
                text: '标识'
            }];

        Ext.define('gridListModelScriptSec', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long',
            }, {
                name: 'srcScriptUuid',
                type: 'string'
            }, {
                name: 'srcScriptIid',
                type: 'string'
            }, {
                name: 'srcServiceName',
                type: 'string'
            }, {
                name: 'srcScriptName',
                type: 'string'
            }, {
                name: 'dependScriptIid',
                type: 'long'
            }, {
                name: 'dependScriptUuid',
                type: 'string'
            }, {
                name: 'dependScriptType',
                type: 'string'
            }, {
                name: 'dependServiceName',
                type: 'string'
            }, {
                name: 'dependScriptName',
                type: 'string'
            }, {
                name: 'dependScriptVersion',
                type: 'string'
            }, {
                name: 'dependFlag',
                type: 'string'
            }]
        });



        var scriptStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            pageSize: 30,
            model: 'gridListModelScriptSec',
            proxy: {
                type: 'ajax',
                url: 'getDependScript.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });

        scriptStore.on('beforeload', function (store, options) {
            var new_params = {
                scriptuuid: cgUuid,
                dependServiceName:serviceNameScript.getValue(),
                bussCb : bussCbSec.getValue(),
                bussCbSec : bussTypeCbSec.getValue(),
                applyForm : usePlantFormSec.getValue(),
                scriptName : scriptNameScript.getValue()
            };
            Ext.apply(scriptStore.proxy.extraParams, new_params);
        });
        var bussDataSec = Ext.create('Ext.data.Store', {
            fields: ['iid', 'bsName'],
            autoLoad: false,
            proxy: {
                type: 'ajax',
                url: 'bsManager/getBsAll.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });
        bussDataSec.on('load', function (store, options) {
            if (sysIDSec) {
                bussCbSec.setValue(sysIDSec);
                bussTypeDataSec.load({
                    params: {
                        fk: sysIDSec
                    }
                });
            }
        });


        //一级分类
        var bussCbSec = Ext.create('Ext.form.field.ComboBox', {
            name: 'sysName',
            labelWidth: 70,
            width : '25%',
            queryMode: 'local',
            // afterLabelTextTpl: required,
            // fieldLabel: '一级分类',
            padding: '0 5 0 0',
            displayField: 'bsName',
            valueField: 'iid',
            editable: true,
            emptyText: '--请选择一级分类--',
            store: bussDataSec,
            listeners: {
                change: function () { // old is keyup
                    bussTypeCbSec.clearValue();
                    bussTypeCbSec.applyEmptyText();
                    bussTypeCbSec.getPicker().getSelectionModel().doMultiSelect([], false);
                    bussTypeDataSec.load({
                        params: {
                            fk: this.value
                        }
                    });
                },
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = Ext.util.Format.trim(e.query);
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }
            }
        });

        var bussTypeDataSec = Ext.create('Ext.data.Store', {
            fields: ['sysTypeId', 'sysType'],
            autoLoad: false,
            proxy: {
                type: 'ajax',
                url: 'bsManager/getBsTypeByFk.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        bussTypeDataSec.on('load', function (store, options) {
            if (busID) {
                bussTypeCbSec.setValue(busIDSec);
            }
        });

        //二级分类
        var bussTypeCbSec = Ext.create('Ext.form.field.ComboBox', {
            name: 'bussType',
            padding: '0 5 0 0',
            labelWidth: 70,
            width : '25%',
            queryMode: 'local',
            // afterLabelTextTpl: required,
            // fieldLabel: '二级分类',
            displayField: 'sysType',
            valueField: 'sysTypeId',
            editable: true,
            emptyText: '--请选择二级分类--',
            store: bussTypeDataSec,
            listeners: {
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = Ext.util.Format.trim(e.query);
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }
            }
        });

        //适用平台
        var usePlantFormStoreSec = Ext.create('Ext.data.JsonStore', {
            fields: ['INAME', 'ICODEVALUE'],
            //autoDestroy : true,
            autoLoad: true,
            proxy: {
                type: 'ajax',
                url: 'getScriptPlatformCode.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        var usePlantFormSec = Ext.create('Ext.form.field.ComboBox', {
            name: 'useplantform',
            padding: '0 5 0 0',
            labelWidth: 70,
            // columnWidth: .5,
            width : '25%',
            queryMode: 'local',
            // fieldLabel: '适用平台',
            // afterLabelTextTpl: required,
            displayField: 'INAME',
            valueField: 'ICODEVALUE',
            // multiSelect: true,//启用多选
            editable: true,
            emptyText: '--请选择适用平台--',
            store: usePlantFormStoreSec
        });

        function openScriptDenendScriptWindow() {

            // if (cgUuid == '' || cgUuid == null) {
            //     Ext.MessageBox.alert("提示", "非草稿状态数据无法绑定脚本依赖！");
            //     return;
            // }
            if(!getScriptByServiceId()){
                Ext.MessageBox.alert("提示", "非草稿状态数据无法绑定脚本依赖！");
                return;
            }

            scriptPublishsIds = [];
            let ss = selModel.getSelection();
            //脚本状态 如果不是草稿 -1 那么不允许依赖、也不允许取消依赖，只能查看已经依赖的脚本
            let scriptStatus = -1;
            //选择脚本的uuid
            let scriptUuid = cgUuid;

            Ext.define('dependScriptLeftModel', {
                extend: 'Ext.data.Model',
                fields: [{
                    name: 'iid',
                    type: 'long',
                }
                    // , {
                    //     name: 'srcScriptUuid',
                    //     type: 'string'
                    // }, {
                    //     name: 'srcScriptIid',
                    //     type: 'string'
                    // }, {
                    //     name: 'srcServiceName',
                    //     type: 'string'
                    // }, {
                    //     name: 'srcScriptName',
                    //     type: 'string'
                    // }, {
                    //     name: 'dependScriptIid',
                    //     type: 'long'
                    // }, {
                    //     name: 'dependScriptUuid',
                    //     type: 'string'
                    // }, {
                    //     name: 'dependScriptType',
                    //     type: 'string'
                    // }, {
                    //     name: 'dependServiceName',
                    //     type: 'string'
                    // }, {
                    //     name: 'dependScriptName',
                    //     type: 'string'
                    // }, {
                    //     name: 'dependScriptVersion',
                    //     type: 'string'
                    // }, {
                    //     name: 'dependFlag',
                    //     type: 'string'
                    // }
                ]
            });



            let dependScript_columns = [{
                text: '序号',
                xtype: 'rownumberer',
                width: 40
            },
                {
                    text: 'iid',
                    dataIndex: 'iid',
                    width: 40,
                    hidden: true
                },
                {
                    text: '源脚本uuid',
                    dataIndex: 'srcScriptUuid',
                    width: 40,
                    hidden: true
                }, {
                    text: '源脚本iid',
                    dataIndex: 'srcScriptIid',
                    width: 40,
                    hidden: true
                }, {
                    text: '源脚本服务名',
                    dataIndex: 'srcServiceName',
                    width: 120,
                }, {
                    text: '源脚本名称',
                    dataIndex: 'srcScriptName',
                    width: 120,
                }, {
                    text: '依赖脚本iid',
                    dataIndex: 'dependScriptIid',
                    width: 40,
                    hidden: true
                }, {
                    text: '依赖脚本uuid',
                    dataIndex: 'dependScriptUuid',
                    width: 40,
                    hidden: true
                }, {
                    text: '依赖脚本服务名',
                    dataIndex: 'dependServiceName',
                    width: 120,
                }, {
                    text: '依赖脚本名称',
                    dataIndex: 'dependScriptName',
                    width: 120,
                }, {
                    text: '依赖脚本类型',
                    dataIndex: 'dependScriptType',
                    width: 100,
                }, {
                    text: '依赖脚本版本',
                    dataIndex: 'dependScriptVersion',
                    width: 80,
                    flex: 1
                }, {
                    text: '是否直接依赖',
                    dataIndex: 'dependFlag',
                    width: 40,
                    hidden: true
                }];


            Ext.define('canDependScriptRightModel', {
                extend: 'Ext.data.Model',
                fields: [{
                    name: 'iid',
                    type: 'long',
                }, {
                    name: 'scriptuuid',
                    type: 'string'
                }, {
                    name: 'serviceName',
                    type: 'string'
                }, {
                    name: 'scriptName',
                    type: 'string'
                }, {
                    name: 'scriptType',
                    type: 'string'
                }, {
                    name: 'version',
                    type: 'long'
                }, {
                    name: 'createUserName',
                    type: 'string'
                }]
            });

            let canDependScript_columns = [{
                text: '序号',
                xtype: 'rownumberer',
                width: 40
            },
                {
                    text: 'iid',
                    dataIndex: 'iid',
                    width: 40,
                    hidden: true
                },
                {
                    text: '脚本uuid',
                    dataIndex: 'srcScriptUuid',
                    width: 40,
                    hidden: true
                }, {
                    text: '服务名称',
                    dataIndex: 'serviceName',
                    width: 140,

                }, {
                    text: '脚本名称',
                    dataIndex: 'scriptName',
                    width: 140,
                }, {
                    text: '版本',
                    dataIndex: 'version',
                    width: 140,
                }, {
                    text: '脚本类型',
                    dataIndex: 'scriptType',
                    width: 140,
                }, {
                    text: '创建人',
                    dataIndex: 'createUserName',
                    flex: 1,
                    width: 40
                }];

            let serviceNameLeft = new Ext.form.TextField({
                name: 'serviceNameLeft',
                fieldLabel: '依赖脚本服务名称',
                emptyText: '--请输入服务名称--',
                labelWidth: 120,
                labelAlign: 'right',
                width: '33%'
            });
            let serviceNameRight = new Ext.form.TextField({
                name: 'serviceNameRight',
                fieldLabel: '服务名称',
                emptyText: '--请输入服务名称--',
                labelWidth: 65,
                labelAlign: 'right',
                width: '50%'
            });
            let dependScriptStore = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy: true,
                model: 'gridListModelScript',
                proxy: {
                    type: 'ajax',
                    url: 'getDependScript.do',
                    reader: {
                        type: 'json',
                        root: 'dataList'
                    }
                }
            });
            dependScriptStore.on('beforeload', function (store, options) {
                let new_params = {
                    scriptuuid: scriptUuid,
                    dependServiceName: Ext.util.Format.trim(serviceNameLeft.getValue())
                };
                Ext.apply(dependScriptStore.proxy.extraParams, new_params);
            });

            let dependScriptLeftGrid = Ext.create('Ext.grid.Panel', {
                region: 'center',
                store: dependScriptStore,
                border: false,
                columnLines: true,
                viewConfig: {
                    enableTextSelection: true
                },
                cls: 'window_border panel_space_left',
                columns: dependScript_columns,
                selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true, mode: 'SINGLE', allowDeselect: true})
            });

            let canDependScriptStore = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy: true,
                model: 'canDependScriptRightModel',
                pageSize: 30,
                proxy: {
                    type: 'ajax',
                    url: 'getCanDependScript.do',
                    reader: {
                        type: 'json',
                        root: 'dataList',
                        totalProperty: 'total'
                    }
                }
            });
            canDependScriptStore.on('beforeload', function (store, options) {
                let new_params = {
                    scriptuuid: scriptUuid,
                    serviceName: Ext.util.Format.trim(serviceNameRight.getValue())
                };
                Ext.apply(canDependScriptStore.proxy.extraParams, new_params);
            });
            let canDependScriptRightGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
                region: 'center',
                store: canDependScriptStore,
                border: false,
                columnLines: true,
                cls: 'window_border panel_space_left',
                columns: canDependScript_columns,
                ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true, mode: 'SINGLE', allowDeselect: true})
            });


            let panelRightForm = Ext.create('Ext.ux.ideal.form.Panel', {
                    layout: 'anchor',
                    region: 'north',
                    // buttonAlign : 'center',
                    autoScroll: true,
                    border: false,
                    bodyCls: 'x-docked-noborder-top',
                    columnLines: true,
                    cls: 'window_border panel_space_left',
                    iqueryFun: function () {
                        canDependScriptRightGrid.ipage.moveFirst();
                    },
                    dockedItems: [{
                        xtype: 'toolbar',
                        border: false,
                        dock: 'top',
                        items: [serviceNameRight,'->',{
                            xtype: 'toolbar',
                            border: false,
                            items: [{
                                xtype: 'button',
                                text: '查询',
                                cls: 'Common_Btn',
                                handler: function () {
                                    canDependScriptRightGrid.ipage.moveFirst();
                                }
                            }, {
                                xtype: 'button',
                                cls: 'Common_Btn',
                                text: '清空',
                                handler: function () {
                                    serviceNameRight.setValue();
                                }
                            }, {
                                xtype: 'button',
                                text: '绑定依赖',
                                cls: 'Common_Btn',
                                handler: function () {
                                    if (scriptStatus !== -1) {
                                        Ext.Msg.alert('提示', '只有在草稿状态下才可以绑定依赖脚本！');
                                    } else {
                                        let selectData = canDependScriptRightGrid.getSelectionModel().getSelection();
                                        if (selectData.length === 0) {
                                            Ext.Msg.alert('提示', '请选择数据！');
                                        } else {
                                            let scriptDenends = [];
                                            selectData.forEach(obj => {
                                                //依赖脚本对象构建
                                                let scriptDenend = {
                                                    "srcScriptUuid": scriptUuid,
                                                    "dependScriptUuid": obj.data.scriptuuid
                                                };
                                                scriptDenends.push(scriptDenend);
                                            });
                                            Ext.Ajax.request({
                                                url: 'saveDependScript.do',
                                                method: 'POST',
                                                dataType: 'json',
                                                jsonData: Ext.JSON.encode(scriptDenends),
                                                success: function (response, request) {
                                                    let getSuccess = Ext.decode(response.responseText).success;
                                                    let getMessage = Ext.decode(response.responseText).message;
                                                    if (getSuccess === 'true' || getSuccess) {
                                                        // dependScriptStore.reload();
                                                        canDependScriptRightGrid.ipage.moveFirst();
                                                        scriptStore.reload();
                                                        Ext.Msg.alert('提示', getMessage);
                                                    } else {
                                                        Ext.Msg.alert('提示', getMessage);
                                                    }
                                                    //刷新绑定列表
                                                    scriptStore.reload();
                                                },
                                                failure: function (result, request) {
                                                    secureFilterRs(result, "操作失败！");
                                                }
                                            });
                                        }
                                    }
                                }
                            }]
                        }]
                    }]
                }
            );

            let panelRight = Ext.create('Ext.panel.Panel', {
                layout: 'border',
                title: '待依赖的脚本',
                bodyCls: 'x-docked-noborder-top',
                cls: 'window_border panel_space_right',
                region: 'center',
                border: false,
                // split : true,
                items: [panelRightForm, canDependScriptRightGrid]
            });


            let scriptDenendScriptWindow = Ext.create('Ext.window.Window', {
                // title : '依赖脚本',
                autoScroll: true,
                modal: true,
                resizable: false,
                constrain: true,
                closeAction: 'destroy',
                layout: 'border',
                width: 1000,
                height: 700,
                items: [
                    // panelLeft,
                    panelRight]
            }).show();
        }



        scriptTab = Ext.create('Ext.grid.Panel', {
            region: 'center',
            store: scriptStore,
            width:650,
            layout:'fit',
            margin:'0 0 0 10',
            height: contentPanel.getHeight()-130,
            // selModel: selModel,
            // plugins: [cellEditing],
            //dockedItems: scriptServiceitems,
            border: true,
            // ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            // bbar : pageBar,
            selModel:selModelDependScript,
            columnLines: true,
            cls: 'customize_panel_back',
            // padding: grid_space,
            columns: scriptColumns,
            overflowX:false,
            // listeners:{
            //     itemdblclick:function(t, record){
            //         editor.replaceSelection(record.raw.iexample);
            //         editor.focus();
            //     }
            // },
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [
                    bussCbSec,bussTypeCbSec,usePlantFormSec,'->',
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            serviceNameScript.setValue('');
                            scriptNameScript.setValue('');
                            bussCbSec.setValue('');
                            bussTypeCbSec.setValue('');
                            usePlantFormSec.setValue('');
                        }
                    },
                    {
                        xtype:'button',
                        cls:'Common_Btn',
                        text: '查询',
                        handler:function(){
                            // scriptTab.ipage.moveFirst();
                            scriptStore.reload();
                        }
                    }
                ]
            },{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [
                    serviceNameScript,scriptNameScript,'->',
                    {
                        text: '脚本依赖',
                        cls: 'Common_Btn',
                        handler: openScriptDenendScriptWindow
                    },{
                        xtype: 'button',
                        text: '取消依赖',
                        cls: 'Common_Btn',
                        handler: function () {
                            if (!getScriptByServiceId()) {
                                Ext.MessageBox.alert("提示", "非草稿状态数据无法解绑脚本依赖！");
                            } else {
                                let selectData = scriptTab.getSelectionModel().getSelection();
                                if (selectData.length === 0) {
                                    Ext.Msg.alert('提示', '请选择数据！');
                                } else {
                                    let scriptDenendIids = [];
                                    let canDeleteFlag = true;
                                    selectData.forEach(obj => {
                                        if (obj.data.dependFlag === '1') {
                                            scriptDenendIids.push(obj.data.iid);
                                        } else {
                                            canDeleteFlag = false;
                                        }
                                    });
                                    if (!canDeleteFlag) {
                                        Ext.Msg.alert('提示', '选择的依赖脚本为间接依赖，不是直接依赖，不允许取消依赖！');
                                    } else {
                                        Ext.Ajax.request({
                                            url: 'deleteDependScript.do',
                                            method: 'POST',
                                            dataType: 'json',
                                            jsonData: Ext.JSON.encode(scriptDenendIids),
                                            success: function (response, request) {
                                                let getSuccess = Ext.decode(response.responseText).success;
                                                let getMessage = Ext.decode(response.responseText).message;
                                                if (getSuccess === 'true' || getSuccess) {
                                                    scriptStore.reload();
                                                    Ext.Msg.alert('提示', getMessage);
                                                } else {
                                                    Ext.Msg.alert('提示', getMessage);
                                                }
                                            },
                                            failure: function (result, request) {
                                                secureFilterRs(result, "操作失败！");
                                            }
                                        });
                                    }

                                }
                            }
                        }
                    }
                ]
            }]
        });
    }



    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
        region: 'east',
        border: false,
        width: '100%',
        autoScroll:false,
        height:contentPanel.getHeight()-140,
        layout:'border',
        items: paramItems
    });

    var infoPanel = new Ext.tab.Panel({
        region: 'east',
        border: false,
        width: 660,
        height: contentPanel.getHeight(),
        layout: 'border',
        tabPosition: 'top',
        style:'background-color:white',
        // cls: 'normatab no_verticaltab',
        activeTab: 0,
        defaults:
            {
                autoScroll: true
            },
        items: [
            {
                title: '脚本属性',
                items: paramsAndFuncDescPanel,
                listeners:
                    {
                        activate: function (tab) {
                            // paramGrid.setHeight(infoPanel.getHeight() - 420);

                        }
                    }
            },
            {
                title: '变量',
                items: [variableTab],
                hidden:!scriptEditBookSwitch || funVariShowSwtich,
                listeners:
                    {
                        activate: function (tab) {
                            // variableTab.ipage.moveFirst();

                        }
                    }
            },
            {
                title: '函数',
                items: [functionTab],
                hidden:!scriptEditBookSwitch || funVariShowSwtich,
                listeners:
                    {
                        activate: function (tab) {
                            // functionTab.ipage.moveFirst();

                        }
                    }
            },
            {
                title: '脚本',
                hidden:!getScriptEditeTabShowSwitch,
                items: [scriptTab],
                listeners:
                    {
                        activate: function (tab) {
                        }
                    }
            }
        ]
    })

    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        }, {
            name: 'name',
            type: 'string'
        }, {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: !removeAgentSwitch,
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    resourceGroupStore.on('load', function () {
        var ins_rec = Ext.create('resourceGroupModel', {
            id: '-1',
            name: '未分组',
            description: ''
        });
        resourceGroupStore.insert(0, ins_rec);
    });
    var resourceGroupObj = Ext.create('Ext.ux.ideal.form.ComboBox',
        {
            fieldLabel: '资源组',
            emptyText: '--请选择资源组--',
            labelAlign: 'right',
            labelWidth: 70,
            width: '25.5%',
            columnWidth: 1,
            multiSelect: true,
            hidden: removeAgentSwitch,
            store: resourceGroupStore,
            displayField: 'name',
            valueField: 'id',
            triggerAction: 'all',
            queryMode: 'local',
            editable: true,
            mode: 'local',
            listeners: {
                change: function (comb, newValue, oldValue, eOpts) {
                    /*
								 * chosedResGroups_forest = new Array(); for(var
								 * i=0;i<newValue.length;i++) {
								 * chosedResGroups_forest.push(newValue[i]); }
								 */
                    // agent_store.load();
                    agent_grid.ipage.moveFirst();
                }
            }
        });
    var tempModel = 'sysNameModel';
    if (projectFlag == 1) {
        tempModel = 'paramRuleModel'
    }
    var sys_name_store = Ext.create('Ext.data.Store', {
        autoLoad: CMDBflag,
        model: tempModel,
        proxy: {
            type: 'ajax',
            url: 'getAgentSysNameList.do?envType=0&switchFlag=' + projectFlag,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var sys_name = Ext.create('Ext.ux.ideal.form.ComboBox', {
//	    editable: false,
        name: 'sysname',
        fieldLabel: "名称",
        hidden: !CMDBflag,
        emptyText: '--请选择名称--',
        store: sys_name_store,
        queryMode: 'local',
        width: "25%",
        displayField: 'sysName',
        valueField: 'sysName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '计算机名',
        displayField: 'hostname',
        emptyText: '--请输入计算机名--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%'
    });

    var app_name_store = Ext.create('Ext.data.Store', {
        autoLoad: CMDBflag,
        model: 'appNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentAppNameList.do?envType=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var app_name = Ext.create('Ext.ux.ideal.form.ComboBox', {
//	    editable: false,
        name: 'appname',
        fieldLabel: "应用名称",
        emptyText: '--请选择应用名称--',
        store: app_name_store,
        hidden: !CMDBflag,
        queryMode: 'local',
        width: "25%",
        displayField: 'appName',
        valueField: 'appName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '操作系统',
        displayField: 'ostype',
        emptyText: '--请输入操作系统--',
        labelWidth: 70,
        labelAlign: 'right',
        width: CMDBflag ? '25%' : '24.2%'
    });

    var agentStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "0", "name": "正常"},
            {"id": "1", "name": "异常"},
            {"id": "2", "name": "升级中"}
        ]
    });

    var agent_ip = new Ext.form.TextField({
        name: 'agentIp',
        fieldLabel: 'Agent IP',
        displayField: 'agentIp',
        emptyText: '--请输入Agent IP--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.5%'
    });

    var agentStatusCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'agentStatus',
        labelWidth: 79,
        queryMode: 'local',
        fieldLabel: 'Agent状态',
        closeAction: 'destroy',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择Agent状态--',
        store: agentStatusStore,
        width: '25.2%',
        labelAlign: 'right'
    });


    function getExecUserForShow(newServiceId) {
        var suUser = '';
        Ext.Ajax.request({
            url: 'scriptService/queryOneService.do',
            method: 'POST',
            async: false,
            params: {
                iid: newServiceId
            },
            success: function (response, request) {
                var reader = Ext.decode(response.responseText);
                suUser = reader.suUser;
                vv = suUser;
            },
            failure: function (result, request) {
                secureFilterRs(result, "出现错误！");
            }
        });
        return suUser;
    }

    var execUserForTry = new Ext.form.TextField({
        name: 'execUserForTry',
//    	id:'execUserForTry',
        fieldLabel: '执行用户',
        value: vv,
        closeAction: 'destroy',
        emptyText: '-请输入执行用户-',
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%'
    });

    var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: function () {
            agent_grid.ipage.moveFirst();
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [sys_name, app_name, host_name, os_type
            ]
        },
            {
                xtype: 'toolbar',
                dock: 'top',
                items: [agent_ip, resourceGroupObj, agentStatusCb,
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '查询',
                        handler: function () {
                            agent_grid.ipage.moveFirst();
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            agent_ip.setValue('');
                            app_name.setValue('');
                            sys_name.setValue('');
                            host_name.setValue('');
                            os_type.setValue('');
                            resourceGroupObj.setValue('');
                            agentStatusCb.setValue('');
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '查看已选择服务器',
                        hidden: true,
                        handler: function () {
                            if (!chosedAgentWin) {
                                chosedAgentWin = Ext.create('Ext.window.Window', {
                                    title: '已选择服务器',
                                    autoScroll: true,
                                    modal: true,
                                    resizable: false,
                                    closeAction: 'hide',
                                    width: contentPanel.getWidth() - 250,
                                    height: 530,
                                    items: [agent_grid_chosed],
                                    buttonAlign: 'center',
                                    buttons: [{
                                        xtype: "button",
                                        text: "关闭",
                                        handler: function () {
                                            this.up("window").close();
                                        }
                                    }]
                                });
                            }
                            chosedAgentWin.show();
                            agent_store.load();
                        }
                    }
                ]
            }, {
                xtype: 'toolbar',
                dock: 'top',
                items: [execUserForTry
                ]
            }]
    });

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'appName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentState', type: 'int'}
        ]
    });

    Ext.define('dsModel', {
        extend: 'Ext.data.Model',
        idProperty: 'dsId',
        fields: [
            {name: 'dsId', type: 'int'},
            {name: 'dsIp', type: 'String'},
            {name: 'dsName', type: 'String'},
            {name: 'dsUser', type: 'String'},
            {name: 'dsPwd', type: 'String'},
            {name: 'dsRole', type: 'String'},
            {name: 'dsIns', type: 'String'},
            {name: 'dsUrl', type: 'String'},
            {name: 'dsType', type: 'String'}
        ]
    });

    var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var dsinfo_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'dsModel',
        proxy: {
            type: 'ajax',
            url: 'getDsInfoByDsId.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    function openExecUserConfigData(record) {
        if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
            if (isSumpAgentSwitch == true) {
                var sumpAgentStore = Ext.create('Ext.data.Store', {
                    fields: ['iid', 'userName'],
                    autoLoad: true,
                    proxy: {
                        type: 'ajax',
                        url: 'getSumpAgentUserList.do',
                        reader: {
                            type: 'json',
                            root: 'dataList'
                        }
                    }
                });

                sumpAgentStore.on('beforeload', function (store, options) {
                    var queryparams = {
                        agentId: record.get('iid')
                    };
                    Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
                });

                execUserNameText = Ext.create('Ext.form.field.ComboBox', {
                    name: 'execUserName',
                    labelWidth: 65,
                    queryMode: 'local',
                    fieldLabel: '执行用户',
                    width: 320,
                    displayField: 'userName',
                    valueField: 'iid',
                    editable: true,
                    typeAhead: true,
                    emptyText: '--请选择执行用户--',
                    store: sumpAgentStore,
                    labelAlign: 'right'
                });
                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var sumpAgentCount = sumpAgentStore.getRange();
                    var newExecUserName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (sumpAgentCount.length > 0) {
                        if (undefined == newExecUserName) {
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                        }
                    } else {
                        if (undefined == newExecUserName) {
                            execUserNameText.setValue(record.get('execuser'));
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                            execUserNameText.setRawValue(newExecUserName);
                        }
                    }
                }
            } else {
                execUserNameText = Ext.create('Ext.form.TextField',
                    {
                        fieldLabel: '执行用户',
                        labelAlign: 'right',
                        name: "execUserName",
                        labelWidth: 65,
                        emptyText: '--请输入执行用户--',
                        width: 320,
                        xtype: 'textfield'
                    });

                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var newExecUserName1 = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (undefined == newExecUserName1) {
                        execUserNameText.setValue(record.get('execuser'));
                    } else {
                        execUserNameText.setValue(newExecUserName1);
                    }
                }
            }

            execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel', {
                region: 'north',
                layout: 'anchor',
                //iqueryFun : queryBtnFun,
                buttonAlign: 'right',
                baseCls: 'customize_gray_back',
                collapsible: false,//可收缩
                collapsed: false,//默认收缩
                border: false,
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    baseCls: 'customize_gray_back',
                    items: [execUserNameText]
                }, {
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    baseCls: 'customize_gray_back',
                    items: ['->', {
                        text: '确定',
                        cls: 'Common_Btn',
                        icon: '',
                        handler: function () {
                            chosedExecUser(record);
                        }
                    }]
                }]
            });

            var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
                layout: 'border',
                width: "100%",
                height: "100%",
                border: false,
                items: [execUserConfigForm],
                cls: 'customize_panel_bak'
            });

            execUserConfigWindow = Ext.create('Ext.window.Window', {
                title: "配置执行用户",
                modal: true,
                closeAction: 'destroy',
                constrain: true,
                autoScroll: false,
                //upperWin : errorTaskWin,
                width: 380,
                height: 200,
                draggable: false,// 禁止拖动
                resizable: false,// 禁止缩放
                layout: 'fit',
                items: [execUserConfig_mainPanel]
            });
        }
        execUserConfigWindow.show();
    }

    var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', width: 70},
        {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
        {text: '计算机名', dataIndex: 'hostName', width: 120},
        {text: 'IP', dataIndex: 'agentIp', width: 90},
        {text: '端口号', dataIndex: 'agentPort', width: 90},
        {text: '操作系统', dataIndex: 'osType', width: 120},
        {text: '描述', dataIndex: 'agentDesc', flex: 1, hidden: true},
        {
            text: '状态', dataIndex: 'agentState', flex: 1, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        },
        {
            text: '执行用户',
            dataIndex: 'execuser',
            width: 90,
            align: 'left',//整体左对齐
            renderer: function (value, metaData, record, rowNum) {
                var displayValue = value;
                var recordedData = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                if (undefined == recordedData) {
                    if ("" == value || undefined == value) {
                        displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                    } else {
                        displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + displayValue + "</a>";
                    }
                } else {
                    if ("" == recordedData) {
                        displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                    } else {
                        displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + recordedData + "</a>";
                    }
                }
                return displayValue;
            },
            listeners: {
                click: function (a, b, c, d, e, record) {
                    openExecUserConfigData(record);
                }
            }
        }
    ];

    var ds_columns = [
        {text: '主键', dataIndex: 'dsId', hidden: true},
        {text: 'DSIP', dataIndex: 'dsIp', hidden: true},
        {text: '数据源名称', dataIndex: 'dsName', width: 100},
        {text: '用户', dataIndex: 'dsUser', width: 90},
        {text: '密码', dataIndex: 'dsPwd', hidden: true},
        {text: '角色', dataIndex: 'dsRole', hidden: true},
        {text: '实例名', dataIndex: 'dsIns', hidden: true},
        {text: '类型', dataIndex: 'dsType', width: 50},
        {text: 'URL', dataIndex: 'dsUrl', flex: 1}
    ];

    agent_store.on('beforeload', function (store, options) {
        var new_params = {
            agentIp: Ext.util.Format.trim(agent_ip.getValue()),
            appName: app_name.getValue() == null ? '' : Ext.util.Format.trim(app_name.getValue() + ""),
            sysName: sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + ""),
            hostName: Ext.util.Format.trim(host_name.getValue()),
            osType: Ext.util.Format.trim(os_type.getValue()),
            rgIds: resourceGroupObj.getValue(),
            agentState: agentStatusCb.getValue(),
            flag: 0,
            switchFlag: projectFlag
        };

        Ext.apply(agent_store.proxy.extraParams, new_params);
    });

//    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//    	store: agent_store,
//    	dock: 'bottom',
//		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//		displayInfo: true,
//		border:false,
//		displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
//		emptyMsg: "没有记录"
//    });

    /*var db_source_combox = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'dbsourcename',
        labelWidth: 120,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '请选择数据源',
        padding: '0 5 0 0',
        displayField: 'dsName',
        valueField: 'iid',
        editable: false,
        queryMode: 'local',
        emptyText: '--请选择数据源--',
        store: dbsourceStore,
        listeners: {
            change: function() { // old is keyup
            	alert(this.value);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });*/
    var agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        //width:'100%',
        store: agent_store,
        border: true,
        columnLines: true,
        columns: agent_columns,
        //bbar: pageBar,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        listeners: {
            select: function (e, record, index, eOpts) {
                if (chosedAgentIds.indexOf(record.get('iid')) == -1) {
                    chosedAgentIds.push(record.get('iid'));
                }
                if (updatecheckRadio == 4) {
                    var cpid = record.get('iid');
                    dsinfo_store.reload({
                        params: {cpid: cpid, switchFlag: projectFlag}  //参数
                    });

                }
            },
            deselect: function (e, record, index, eOpts) {
                if (chosedAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedAgentIds.remove(record.get('iid'));
                }
                if (updatecheckRadio == 4) {
                    var cpid = record.get('iid');
                    dsinfo_store.reload({
                        params: {cpid: cpid}  //参数
                    });

                }
            }
        }
    });
    agent_store.on('load', function (store, options) {
        agent_grid.getSelectionModel().select(0);
    });

    var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        mode: "SINGLE"
    });

    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
        store: dsinfo_store,
        width: '40%',
        region: 'east',
        border: true,
        columnLines: true,
        columns: ds_columns,
        selModel: selModel2
    });
    dsinfo_store.on('load', function (store, options) {
        db_soucre_grid.getSelectionModel().select(0);
    });

    function clearQueryWhere() {
        resourceGroupObj.setValue('');
        search_form.getForm().findField("agentIp").setValue('');
        //search_form.getForm().findField("agentDesc").setValue('');
    }

    var attachmentUploadWin = null;

    function selectAttachmentFun() {
        var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_idupdate',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash", "text/plain","audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        const LENGTH = arrs.length; 
                        for (var i = 0; i < files.length; i++) {
                        	var fileName=files[i].name;
                        	if(LENGTH>0 && arrs[0]!==''){
                        		var suffix = fileName.match(/[^.]+$/)[0];
    	                        //初始化result状态，只要能找到匹配的则修改为true
    	                        let result = false;
	                        	for (var j=0; j < LENGTH; j++) {
	                        	  if (arrs[j] === suffix) {
	                        	    result = true;
	                        	    break;
	                        	  }
	                        	}
                            	if(!result){
                            		 Ext.Msg.alert('提示', "所选文件扩展名不正确，支持扩展名为 （"+arrs+"）类型文件");
                            		 Ext.getCmp('attachment_idupdate').setRawValue("");    //files为组件的id
                                     return;
                            	}
                        	}
                            str += fileName;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_idupdate').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        attachmentUploadWin = Ext.create('Ext.window.Window', {
            title: '附件信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 180,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            },
            /*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout: 'fit'
        });

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        /** 自定义遮罩效果* */
        var myUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplate(form) {
            if (form.isValid()) {
                myUploadMask.show();
                attachmentUploadWin.hide();
                form.submit({
                    url: 'uploadScriptAttachmentFile.do',
                    params:{
                        attachmentIds:Ext.encode(attachmentIds)
                    },
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            attachmentIds.push.apply(attachmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        attachmentUploadWin.close();
                        myUploadMask.hide();
                        attachmentStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        myUploadMask.hide();
                    }
                });
            }
        }

        attachmentUploadWin.show();
    }

    var tempUploadWin = null;

    function selectTempFun() {
        var uploadTempForm;
        uploadTempForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_idbasic',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash", "audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        for (var i = 0; i < files.length; i++) {
                            str += files[i].name;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_idbasic').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclTempData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        tempUploadWin = Ext.create('Ext.window.Window', {
            title: '模板信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 200,
            items: [uploadTempForm],
            listeners: {
                close: function (g, opt) {
                    uploadTempForm.destroy();
                }
            },
            /*
             * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
             */layout: 'fit'
        });

        function upExeclTempData() {
            var form = uploadTempForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplateSec(form);
        }

        /** 自定义遮罩效果* */
        var tempUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplateSec(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploadScriptAttaTemplate.do',
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            tempmentIds.push.apply(tempmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        tempUploadWin.close();
                        tempUploadMask.hide();
                        attaTempStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        tempUploadMask.hide();
                    }
                });
            }
        }

        tempUploadWin.show();
    }

    function tryATry() {
        editor.save();
        outruleGrid.getStore().removeAll();

        var m = paramStore.getRange();
        for (var i = 0, len = m.length; i < len; i++) {
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
            let count = 0;
            for (let j = 0; j < paramDefaultValue.length; j++) {
                if (paramDefaultValue[j] == "'") {
                    count++;
                }
            }
            if (count % 2 !=0) {
                setMessage('参数默认值输入单引号，必须前引号、后引号同时出现！');
                return;
            }
            if (paramDefaultValue.indexOf('"') > -1) {
                setMessage('参数默认值不允许输入双引号！');
                return;
            }
        }

        if (updatecheckRadio == '4' && projectFlag != 1) {
            Ext.Msg.alert('提示', 'sql类型脚本暂不支持尝试功能！');
            return;
        }
        var content = document.getElementById('code-edit').value;
        if (!content) {
            Ext.Msg.alert('提示', '请填入脚本内容！');
            return;
        }
        var agentId = agentPullChosedCb.getValue();
        if (!agentId) {
            Ext.Msg.alert('提示', '请选择服务器！');
            return;
        }

        function tryReall() {
            if (paramInvalidMessage) {
                Ext.MessageBox.alert("提示", paramInvalidMessage);
            } else {
                whichButtonIsClicked = 1;
                var scriptPara = getParams();
                var type = "sh";
                if (updatecheckRadio == '0') {
                    type = "sh";
                } else if (updatecheckRadio == '1') {
                    type = "bat";
                } else if (updatecheckRadio == '2') {
                    type = "perl";
                } else if (updatecheckRadio == '3') {
                    type = "py";
                } else if (updatecheckRadio == '4') {
                    type = "sql";
                } else if (updatecheckRadio == '6') {
                    type = "ps1";
                }
                var rulejsonData = "[";
                var tryStoreDbaas = '0';
                if (updatecheckRadio == '4' && chooseSqlExecModel.getValue() == '2') {
                    tryStoreDbaas = '1';
                    var patt1 = new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
                    var mm = paramRulesStore.getRange();
                    var outArr = {};
                    var arrContain = [];
                    for (var i = 0, len = mm.length; i < len; i++) {
                        var n = 0;
                        var paramRuleIn = mm[i].get("paramRuleIn") ? mm[i].get("paramRuleIn").trim() : '';
                        var paramRuleType = mm[i].get("paramRuleType");
                        var paramRuleLen = mm[i].get("paramRuleLen");
                        var paramRuleOrder = mm[i].get("paramRuleOrder") ? mm[i].get("paramRuleOrder") : '';
                        var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
                        var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
                        paramRuleOut = paramRuleOut.toUpperCase();
                        if ("" == paramRuleOut) {
                            setMessage('输出规则输出不能为空！');
                            return;
                        }
                        if ("GROUP" == paramRuleOut || "IID" == paramRuleOut || "IRESID_BULID_IN" == paramRuleOut || "IDBID_BULID_IN" == paramRuleOut || "ITIME_BULID_IN" == paramRuleOut || "ISERVICEID_BULID_IN" == paramRuleOut || "ISTARTTIME_BULID_IN" == paramRuleOut || "IDBCOPY_BULID_IN" == paramRuleOut || "ITRIMODEL_BULID_IN" == paramRuleOut || "IFLAG_BULID_IN" == paramRuleOut || "IFLOWID_BULID_IN" == paramRuleOut) {
                            setMessage('部分输出规则已经内置,请重新填写！' + "“" + paramRuleOut + "”");
                            return;
                        }
                        if (arrContain.contains(paramRuleOut) > 0) {
                            setMessage('输出规则不能重复！');
                            return;
                        } else {
                            arrContain.push(paramRuleOut)
                        }
                        if (!patt1.test(paramRuleOut)) {
                            setMessage('请正确填写输出规则名称！');
                            return;
                        }
                        if (paramRuleType == 6) {
                            setMessage('分区表不支持LONG类型列！');
                            return;
                        }
                        if (paramRuleLen == 0 && paramRuleType != 5/* && paramRuleType!=6*/) {
                            setMessage('长度不能为0字符！');
                            return;
                        } else {
                            if (paramRuleType == 2) {
                                if (paramRuleLen > 38) {
                                    setMessage('DECIMAL类型的长度最大为38!');
                                    return;
                                }
                            }
                        }
                        if (fucCheckLength(paramRuleDesc) > 250) {
                            setMessage('输出规则描述不能超过250字符！');
                            return;
                        }
                        if (outArr[paramRuleOrder]) {
                            setMessage('输出规则序号不能重复！');
                            return;
                        } else {
                            outArr[paramRuleOrder] = true;
                        }
                        var ss = Ext.JSON.encode(mm[i].data);
                        if (i == 0)
                            rulejsonData = rulejsonData + ss;
                        else
                            rulejsonData = rulejsonData + "," + ss;
                    }
                }
                rulejsonData = rulejsonData + "]";

                var scriptWorkDirValue = scriptWorkDir.getValue().trim()
                if (checkLength(scriptWorkDirValue) > 255) {
                    Ext.Msg.alert('提示', '工作目录不能超过255字符');
                    scriptWorkDir.setValue();
                    return;
                }
                var url = "tryATry.do";
                if (projectFlag == 1) {
                    url = "tryAResource.do"
                }

                Ext.MessageBox.wait("数据处理中...", "进度条");
                Ext.Ajax.request({
                    url: url,
                    method: 'POST',
                    sync: true,
                    timeout: 6000000000,
                    params: {
                        scriptName: scriptName,
                        scriptType: type,
                        scriptContent: document.getElementById('code-edit').value,
                        scriptPara: scriptPara,
                        scriptAttachmentIds: attachmentIds,
                        agentId: agentId,
                        jdbcExec: projectFlag,
                        rulejsonData: rulejsonData,
                        switchFlag: projectFlag,
                        sqlExecModel: chooseSqlExecModel.getValue(),
                        tryStoreDbaas: tryStoreDbaas,
                        scriptWorkDir: scriptWorkDirValue
                    },
                    success: function (response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        var content = Ext.decode(response.responseText).content;
                        if (projectFlag == 1 && updatecheckRadio == '4') {
                            outruleGrid.store.removeAll();
                            var columns = Ext.decode(response.responseText).column;
                            var column = columns.split(",");
                            var store = outruleGrid.getStore();
                            var ro = store.getCount();
                            var p;
                            if (columns != '') {
                                if (ro == 0) {
                                    for (var i = 0; i < column.length; i++) {
                                        var datas = column[i].split(":");
                                        p = {
                                            iid: '',
                                            paramRuleOrder: ro + 1,
                                            paramRuleIn: '',
                                            paramRuleOut: datas[0],
                                            paramRuleType: datas[1],
                                            paramRuleLen: datas[2],
                                            paramRuleDesc: ''
                                        };
                                        store.insert(i, p);
                                        ro++;
                                    }
                                }
                                outruleGrid.getView().refresh();
                            }
                        }
                        if (success) {
                            if (projectFlag == 1) {
                                $('#consoleLog-edit').html(
                                    content);
                                consolePanel.body.scroll(
                                    'bottom', 300000);
                                isFromTryATry = 1;
                                tryRequestId = Ext
                                    .decode(response.responseText).requestId;
                                tryAgentIp = Ext
                                    .decode(response.responseText).agentIp;
                                tryAgentPort = Ext
                                    .decode(response.responseText).agentPort;
                            } else {
                                isFromTryATry = 1;
                                tryRequestId = Ext.decode(response.responseText).requestId;
                                tryAgentIp = Ext.decode(response.responseText).agentIp;
                                tryAgentPort = Ext.decode(response.responseText).agentPort;

                                if (refreshTryForUpdate) {
                                    clearInterval(refreshTryForUpdate);
                                }

                                refreshTryForUpdate = setInterval(function () {
                                    loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                }, 4 * 1000);

                                settime(tryATrybtn, '尝试');
                            }
                        }
                        Ext.Msg.alert('提示', message);

                    },
                    failure: function (result, request) {
                        secureFilterRs(result, "出现错误！");
                    }
                });
            }
        }

        var p = orgParams();
        var paramInvalidMessage = p.paramInvalidMessage;
        var someParamIsEmpty = p.someParamIsEmpty;
        if (someParamIsEmpty) {
            Ext.MessageBox.buttonText.yes = "确定";
            Ext.MessageBox.buttonText.no = "取消";
            Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function (id) {
                if (id == 'yes') {
                    tryReall();
                }
            });
        } else {
            tryReall();
        }
    }

    function tryCmd() {
        var content = cmdContent.getValue();
        if (!content) {
            Ext.Msg.alert('提示', '请填入命令！');
            return;
        }

        var agentId = agentPullChosedCbForOneCmd.getValue();
        if (!agentId) {
            Ext.Msg.alert('提示', '请选择服务器！');
            return;
        }

        var typeValue = FieldContainerForOnecmd.getValue()['ra_s_type1_update'];
        var type = "sh";
        if (typeValue == '0') {
            type = "sh";
        } else if (typeValue == '1') {
            type = "bat";
        } else if (typeValue == '2') {
            type = "perl";
        } else if (typeValue == '3') {
            type = "py";
        } else if (typeValue == '6') {
            type = "ps1";
        }

        Ext.Ajax.request({
            url: 'tryATry.do',
            method: 'POST',
            sync: true,
            params: {
                scriptName: 'scriptCmd',
                scriptType: type,
                scriptContent: content,
                agentId: agentId,
                switchFlag: projectFlag
            },
            success: function (response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    cmdRequestId = Ext.decode(response.responseText).requestId;
                    cmdAgentIp = Ext.decode(response.responseText).agentIp;
                    cmdAgentPort = Ext.decode(response.responseText).agentPort;

                    if (refreshCmdForUpdate) {
                        clearInterval(refreshCmdForUpdate);
                    }

                    refreshCmdForUpdate = setInterval(function () {
                        loadShelloutputhisInfoCmd(cmdRequestId, cmdAgentIp, cmdAgentPort);
                    }, 4 * 1000);

                    settime(cmdbtn, '执行');
                    $('#consoleLogForCmdUpdate').html("");
                }
                Ext.Msg.alert('提示', message);

            },
            failure: function (result, request) {
                secureFilterRs(result, "出现错误！");
            }
        });
    }

    var tryATrybtn = Ext.create('Ext.Button', {
        text: '尝试',
        cls: 'Common_Btn',
        hidden: scriptTrySwitch,
        width: 80,
        handler: function () {
            tryATry();
        }
    });

    var cmdbtn = Ext.create('Ext.Button', {
        text: '执行',
        width: 80,
        cls: 'Common_Btn',
        handler: function () {
            tryCmd();
        }
    });

    function settime(btn, text) {
        if (countdown == 0) {
            btn.setDisabled(false);
            btn.setText(text);
            btn.setWidth(80);
            countdown = 10;
        } else {
            btn.setDisabled(true);
            btn.setText(text + "(" + countdown + ")");
            btn.setWidth(100);
            countdown--;
            setTimeout(function () {
                settime(btn, text)
            }, 1000)
        }

    }

    function settimeFJ(btn, text) {
        if (countdownFJ == 0) {
            btn.setDisabled(false);
            countdownFJ = 10;
        } else {
            btn.setDisabled(true);
            countdownFJ--;
            setTimeout(function () {
                settimeFJ(btn, text)
            }, 1000)
        }

    }

    var cmdStr = new Ext.form.TextField({
        width: '50%',
        labelWidth: 70,
        fieldLabel: 'CMD',
        listeners: {
            specialkey: function (textfield, e) {
                if (e.getKey() == Ext.EventObject.ENTER) {
                    cmdVForbasicUpdate = cmdStr.getValue();
                    cmdStr.setValue("");
                }
            }
        }
    });

    var showAllVariableAndFunction = new Ext.panel.Panel({
        // width: 400,
        // height: 200,
        renderTo : Ext.getBody(),
        hidden:scriptEditBookSwitch,
        items: [
            {
                xtype: 'label',
                text: '查看全局变量、函数',
                style: {
                    'text-decoration' : 'underline',
                    'color':'blue'
                },
                listeners:{
                    click: {
                        element: 'el',
                        fn: function(value) {
                            window.open('funcAndVarUseNoteBookPage.do', 'width=400, height=300, menubar=no, toolbar=no, scrollbars=yes');
                        }
                    }
                }
            }
        ]
    });


    var bbarItems;
    if (projectFlag == 1) {// ===========================================光大新增开关，去掉CMD输入框
        bbarItems = [''];
    } else {
        bbarItems = [cmdStr];
    }
    var mainP = Ext.create('Ext.panel.Panel', {
        minHeight: 80,
        border: true,
        region: 'center',
        autoScroll: true,
        height: contentPanel.getHeight(),
        html: '<textarea id="code-edit" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>',
        tbar: [FieldContainer, '->',showAllVariableAndFunction, scriptTemplateCombobox, chooseSqlExecModel, agentPullChosedCb, tryATrybtn],
        // bbar: bbarItems,
        tools: [{
            xtype: 'tool',
            type: 'restore',
            handler: function (event, toolEl, owner, tool) {
                if (!owner.maximize) {
                    owner.restoreSize = owner.getSize();
                    owner.maximize = true;
                    infoPanel.hide();
                    paramsAndFuncDescPanel.hide();
                    consolePanel.hide();
                    cmdStr.hide();
                    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 95);
                } else {
                    owner.maximize = false;
                    owner.setSize(owner.restoreSize);
                    infoPanel.show();
                    paramsAndFuncDescPanel.show();
                    consolePanel.show();
                    cmdStr.show();
                    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 95);
                }
            }
        }
        ]
    });
//    var cmdField = new Ext.form.TextField({
//        name: 'cmd',
//        emptyText: '请输入命令',
//        width: 280
//    });

    var sTStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "bat", "name": "BAT"},
            {"id": "sh", "name": "SHELL"},
            {"id": "py", "name": "PYTHON"},
            {"id": "perl", "name": "PERL"}
        ]
    });
//
//    var sTCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
//        name: 'stcb',
//        padding: '0 5 0 0',
//        queryMode: 'local',
//        displayField: 'name',
//        valueField: 'id',
//        editable: false,
//        emptyText: '',
//        value: 'bat',
//        width: 90,
//        store: sTStore
//    });

    var execCmdBtn = Ext.create('Ext.Button', {
        text: '连接',
        cls: 'Common_Btn',
        handler: function () {
            var agentIp = agentPullChosedCbForCmd.getRawValue() || '';
            var protocol = chooseServerTypeForConsole.getValue() || '';
            var shellPort = (choosePortForConsole.getValue() || '') + "";
            var shellUserName = userNameForConsole.getValue() || '';
            var shellUserPassword = userPasswordForConsole.getValue() || '';
            if (Ext.isEmpty(Ext.util.Format.trim(agentIp))) {
                Ext.Msg.alert('提示', '请选择服务器！');
                return;
            }
            if (Ext.isEmpty(Ext.util.Format.trim(protocol))) {
                Ext.Msg.alert('提示', '请选择协议！');
                return;
            }
            if (Ext.isEmpty(Ext.util.Format.trim(shellPort))) {
                Ext.Msg.alert('提示', '请填写端口号！');
                return;
            } else {
                if (!checkIsInteger(shellPort)) {
                    Ext.Msg.alert('提示', '端口号必须为正整数！');
                    return;
                }
            }
            if (Ext.isEmpty(Ext.util.Format.trim(shellUserName))) {
                Ext.Msg.alert('提示', '请填写用户名！');
                return;
            }
            if (Ext.isEmpty(Ext.util.Format.trim(shellUserPassword))) {
                Ext.Msg.alert('提示', '请填写密码！');
                return;
            }

            var options = {
                host: agentIp,
                port: shellPort,
                username: shellUserName,
                password: shellUserPassword
            }
            connect(protocol, options);
        }
    });

    var tapPanelForConsole = Ext.create("Ext.tab.Panel", {
        activeTab: 0, // 默认激活第几个tab页
        border: false,
        bodyStyle: 'background:#000;',
//		plugins : Ext.create('Ext.ux.TabCloseMenu', {
//			closeTabText : '关闭本页',
//			closeOthersTabsText : '关闭其他',
//			closeAllTabsText : '关闭全部',
//			closeLeftAllTabsText : '关闭左侧全部',
//			closeRightAllTabsText : '关闭右侧全部'
//		}),
        listeners: {
            'tabchange': function (tab, newc, oldc) {
//				var termold = $('#term-create-'+ hex_md5(newc.title)).data('term');
//				if(termold) {
//					termold.focus();
//				}
            },
            'beforeremove': function (tabs, tab) {
                var termold = $('#term-update-' + hex_md5(tab.title)).data('term');
                var clientold = $('#term-update-' + hex_md5(tab.title)).data('client');
                if (termold) {
                    termold.destroy();
                }
                if (clientold) {
                    clientold.close();
                }
            }
        }
    });

    var consoleOneCmdPanel = Ext.create('Ext.panel.Panel', {
        border: true,
        autoScroll: true,
        bodyStyle: 'background:#000;',
        title: "终端",
        tbar: [agentPullChosedCbForCmd, chooseServerTypeForConsole, choosePortForConsole, userNameForConsole, userPasswordForConsole, execCmdBtn],
        items: [tapPanelForConsole]
    });

    var cmdContent = new Ext.form.TextField({
        name: 'cmdContent',
//		fieldLabel: '命令',
        emptyText: '--请输入命令--',
//		labelWidth : 50,
        width: '85%',
        labelAlign: 'right'
    });

    var consoleCmdPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        border: false,
        autoScroll: true,
        title: '日志',
        html: '<textarea id="consoleLogForCmdUpdate" style="width:100%;height:100%;background: #4b4b4b;color: white;margin:0;"></textarea>'
    });
    var oneCmdPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        autoScroll: true,
        title: "单指令测试",
        hidden:!singleCommandSwitch,
        layout: 'border',
        items: [consoleCmdPanel],
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            cls: 'whitelist',
            items: [FieldContainerForOnecmd, '->', agentPullChosedCbForOneCmd]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            cls: 'whitelist',
            items: [cmdContent, '->', cmdbtn]
        }]
    });
    var consolePanel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        height: contentPanel.getHeight() - 253,
        border: true,
        collapsible: true,
        collapsed: true,
        autoScroll: true,
        title: '日志',
        bbar: bbarItems,
        html: '<textarea id="consoleLog-edit" style="width:100%;height:100%;background: #4b4b4b;color: white;margin:0;"></textarea>'
    });
    var maimPanels = Ext.create('Ext.panel.Panel', {
        border: false,
        layout: {
            type: 'border'
        },
        title: "...",
        items: [mainP, consolePanel]
    });

    var itemstabsCenter;
    if (projectFlag == 1) {
        itemstabsCenter = [maimPanels];
    } else {
        itemstabsCenter = [maimPanels, /*consoleOneCmdPanel,*/oneCmdPanel];
    }
    var tabsCenter = Ext.createWidget('tabpanel', {
        //      activeTab: 1,                       //指定默认的活动tab
        region: 'center',
        minHeight: 80,
        cls: 'customize_panel_back',
        height: contentPanel.getHeight(),
        plain: true,                        //True表示tab候选栏上没有背景图片（默认为false）
        enableTabScroll: true,              //选项卡过多时，允许滚动
        defaults: {autoScroll: true},
        border: true,
        layout: {
            type: 'border'
        },
        items: itemstabsCenter
    });

    var tryTestbtn = Ext.create('Ext.Button', {
        text: '测试',
//        cls: 'Common_Btn',
        handler: function () {
            editor.save();
            var testcontent = document.getElementById('code-edit').value;
            if (testcontent.trim() == '') {
                Ext.MessageBox.alert("提示", "测试脚本内容不能为空!");
                return;
            }
            if (updatecheckRadio == 4) {
                agent_grid.setWidth((contentPanel.getWidth() - 250) / 2);
                db_soucre_grid.setWidth((contentPanel.getWidth() - 250) / 2);
                db_soucre_grid.show();
            } else {
                agent_grid.setWidth((contentPanel.getWidth() - 250));
                db_soucre_grid.hide();
            }
            save(0, testScript, '测试');
        }
    });
    var warnningggbtn = Ext.create('Ext.Button', {
        text: '影响信息',
//        cls: 'Common_Btn',
        handler: function () {
            if (!warnningWin1) {
                warnningWin1 = Ext.create('widget.window', {
                    title: '提示信息,发布该脚本将影响以下作业。',
                    closable: true,
                    closeAction: 'hide',
                    modal: true,
                    width: 600,
                    minWidth: 350,
                    height: 300,
                    layout: {
                        type: 'border',
                        padding: 5
                    },
                    items: [warnningGrid1],
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'bottom',
                        layout: {pack: 'center'},
                        items: [{
                            xtype: "button",
                            cls: 'Common_Btn',
                            text: "确定",
                            handler: function () {
                                this.up("window").close();
                            }
                        }]
                    }]
                });
            }
            warnningWin1.show();
            warnningStore.load();
        }
    });
    if (projectFlag == 1) {
        excepResult.hide();
        errExcepResult.hide();
        suUser.hide();
        dbtest.show();
        serviceType.show();
        audittest.show();
        consoleOneCmdPanel.hide();
        oneCmdPanel.hide();
        warnningggbtn.hidden = true;
        if (serviceType.getValue() == "应用" || serviceType.getValue() == "0" || serviceType.getValue() == 0) {
            planTime_sm.hide();
            versionAresource.hide();
            cfg_Display.hide();
            tableName.hide();
            planTime_MM.hide();
            planTime_DD.hide();
            planTime_HH.hide();
            planTime_mi.hide();
        } else {
            planTime_sm.show();
            var newv = planTime_MM.getValue();
            if (newv == '间隔x日') {
                planTime_DD.show();
                planTime_HH.show();
                planTime_mi.show();
            } else if (newv == '间隔x小时') {
                planTime_DD.setValue('');
                planTime_HH.setValue('');
                planTime_mi.setValue('');
                planTime_DD.hide();
                planTime_HH.show();
                planTime_mi.show();
            } else if (newv == '间隔x分钟') {
                planTime_DD.setValue('');
                planTime_HH.setValue('');
                planTime_mi.setValue('');
                planTime_DD.hide();
                planTime_HH.hide();
                planTime_mi.show();
            } else if (newv == '按计划执行一次') {
                planTime_DD.setValue('');
                planTime_HH.setValue('');
                planTime_mi.setValue('');
                planTime_DD.hide();
                planTime_HH.hide();
                planTime_mi.hide();
            }
        }
        if (sqlexectypeValue == '2') {
            tryTestbtn.hide();
        } else {
            tryTestbtn.show();
        }
    } else {
        excepResult.show();
        errExcepResult.show();
        dbtest.hide();
        serviceType.hide();
        audittest.hide();
        suUser.show();
        chooseSqlExecModel.hide();
    }
    var checkBtn = Ext.create('Ext.Button', {
        text: '检查',
        hidden: scriptCheckRuleSwitch,
        handler: checkFunction
    });
    var westPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        cls: 'customize_panel_back',
//        padding: 5,
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        height: contentPanel.getHeight(),
        items: [/*attachmentGrid,*/ tabsCenter],
        buttonAlign: 'center',
        buttons: [checkBtn, tryTestbtn, {
            text: '发布',
            hidden: istrySwitch,
//            cls: 'Common_Btn',
            handler: function () {
                if(fjFlag){
                    settimeFJ(this, '发布');
                }else{
                    settime(this, '发布');
                }
                editor.save();
                var testcontent = document.getElementById('code-edit').value;
                if (testcontent.trim() == '') {
                    Ext.MessageBox.alert("提示", "脚本内容不能为空!");
                    return;
                }
//		       	save(0);//发布提交审核前先保存
                save(0, publishScript, '发布');
            }
        }, warnningggbtn, {
            text: '保存',
            // id: 'saveButton1',
//            cls: 'Common_Btn',
            handler: function () {
                Ext.Msg.wait('处理中，请稍后...', '提示');


                save(0); // 正常保存
            }
        }, {
            text: '返回',
//            cls: 'Common_Btn',
            handler: function () {
                baseInfoOfScriptWin.destroy();
                destroyRubbish();
                var url = 'forwardScriptServiceRelease.do';
                if (projectFlag == 1) {
                    url = 'inintDbaasServiceManage.do';
                }
                contentPanel.getLoader().load({
                    url: url,
                    params: {
                        'filter_bussId': filter_bussIdForUpdateScriptEdit,
                        'filter_bussTypeId': filter_bussTypeIdForUpdateScriptEdit,
                        'filter_scriptName': filter_scriptNameForUpdateScriptEdit,
                        'filter_keywords': filter_keywordsForUpdateScriptEdit,
                        'filter_serviceName': filter_serviceNameForUpdateScriptEdit,
                        'filter_scriptType': filter_scriptTypeForUpdateScriptEdit,
                        'filter_scriptStatus': filter_scriptStatusForUpdateScriptEdit,
                        'filter_serviceType': filter_serviceTypeForUpdateScriptEdit,
                        'filter_patFromValue': filter_patFromValueForUpdateScriptEdit,
                        'switchFlag': projectFlag,
                        'filter_scriptDir': filter_scriptdirForUpdateScriptEdit,
                        'filter_selectUnboundScript': filter_selectUnboundScriptForUpdateScriptEdit,
                        'scriptName1':filterScriptName
                    },
                    scripts: true
                });
                if (refreshTryForUpdate) {
                    clearInterval(refreshTryForUpdate);
                }
                $('#uploadify-base-edit').uploadify('destroy');

            }
        }]
    });


    Ext.define('scriptDirModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iscriptDirName',
                type: 'string'
            }, {
                name: 'iscriptDirSort',
                type: 'long'
            }, {
                name: 'iid',
                type: 'long'
            }, {
                name: 'iscriptDirDiscript',
                type: 'string'
            }, {
                name: 'iscriptDirLevel',
                type: 'long'
            }, {
                name: 'iscriptDirRootId',
                type: 'string'
            }
        ]

    })

    var scriptDirTreeStore = Ext.create('Ext.data.TreeStore', {
        autoLoad: gfScriptDirFunctionSwitch,
        model: 'scriptDirModel',
        proxy: {
            type: 'ajax',
            url: 'scriptEdit/scriptDirList.do'
        },
        root: {
            expanded: gfScriptDirFunctionSwitch,
            iscriptDirName: '根目录',
            icon: 'ext/resources/ext-theme-neptune/images/tree/folder.png'
        }
    });

    scriptDirTreeStore.on('load', function (store, records) {
        if (records.childNodes.length == 0) {
            var flag = true;
            var treeViewDiv = treePanel.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(treePanel.getView().emptyText);
                treePanel.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            Ext.Ajax.request({
                url: 'updateScript/getSelectScriptDir.do',
                method: 'POST',
                params: {
                    iid: iidForUpdateScriptEdit
                },
                success: function (response) {
                    var iid = Ext.decode(response.responseText).iid;
                    scriptVersionCount = Ext.decode(response.responseText).count;
                    scriptStatus = Ext.decode(response.responseText).status;
                    scriptDirId = iid;
                    var m = store.tree.nodeHash;
                    for (let node in m) {
                        if (node == 'root') {
                            continue;
                        }
                        var row = m[node];
                        if (scriptDirId == row.data.id) {
                            scriptBindHis = row;
                            treePanel.getSelectionModel().select(row);
                            break;
                        }
                    }
                },
                failure: function () {
                    console.log('获取绑定目录失败');
                }
            })
        }
    })

    var treePanel = Ext.create('Ext.tree.Panel', {
        // height : '80%',
        region: 'west',
        width: '16%',
        id: 'treeId',
        title: '脚本目录',
        autoScroll: true,
        collapsible: true,
        cls: 'customize_panel_back',
        animate: true,
        useArrows: true,
        hidden: !gfScriptDirFunctionSwitch,
        rootVisible: false,
        store: scriptDirTreeStore,
        hideHeaders: true,
        columns: [{
            xtype: 'treecolumn',
            text: '目录',
            flex: 1,
            dataIndex: 'iscriptDirName',
            sortable: false
        }],
        listeners: {
            select: function (r, record) {

                // scriptVersionCount > 1
                // 存在历史版本情况：1.存在多条历史版本，
                //                              （1）存在绑定目录   不支持修改目录
                //                              （2）不存在绑定目录 支持修改（历史版本同步）
                // scriptVersionCount == 1
                // 1.当前脚本 为上线状态 status == 1
                //          （1）存在绑定关系 不支持修改目录
                //          （2）不存在绑定目录 支持修改（历史版本同步）
                // 2.当前脚本 为草稿状态 status != 1
                //          （1）支持修改

                // 存在历史版本                 只有一条记录 并且是上线状态
                console.log(scriptVersionCount)
                console.log(scriptStatus)
                if (scriptVersionCount > 1 || (scriptVersionCount == 1 && 1 == scriptStatus)) {
                    if (record == scriptBindHis) {
                        console.log('选中目录 与回显目录相同')
                        return;
                        // 选中的 目录与回显目录不同
                    } else {
                        // 脚本存在目录绑定 不支持选中其他目录
                        if (scriptBindHis != undefined) {
                            treePanel.getSelectionModel().select(scriptBindHis);
                            console.log('脚本存在目录绑定 不支持选中其他目录')
                            // 脚本不存在目录绑定 支持修改
                        } else {
                            console.log('脚本不存在目录绑定 支持修改')
                            return;
                        }
                    }
                }
            }
        },
        border: true,
        padding: grid_space,
        columnLines: true,
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "gridUpdateScriptEdit_area",
        padding: 5,
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: true,
        bodyPadding: grid_margin,
        bodyCls: 'service_platform_bodybg',
        height: contentPanel.getHeight() - modelHeigth,
        items: [westPanel, treePanel, infoPanel]
    });


    var codeMirrorComponentName = CodeMirror;
    if(scriptEditHelperSwitch){
        codeMirrorComponentName = CodeMirrorExtension;
    }
    var editor = codeMirrorComponentName.fromTextArea(document.getElementById('code-edit'), {
        mode: 'shell',
        theme: "lesser-dark", // 主题
        keyMap: "sublime", // 快键键风格
        extraKeys: {
            "Ctrl-Q": "autocomplete",
            "Ctrl-D": "deleteLine"
        },
        lineNumbers: true, // 显示行号
        smartIndent: true, // 智能缩进
        indentUnit: 4, // 智能缩进单位为4个空格长度
        indentWithTabs: true, // 使用制表符进行智能缩进
        lineWrapping: true, //
        // 在行槽中添加行号显示器、折叠器、语法检测器
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
        foldGutter: true, // 启用行槽中的代码折叠
        autofocus: true, // 自动聚焦
        matchBrackets: true, // 匹配结束符号，比如"]、}"
        autoCloseBrackets: true, // 自动闭合符号
        styleActiveLine: true // 显示选中行的样式
//        extraKeys: {
//            "F11": function(cm) {
//              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
//            },
//            "Esc": function(cm) {
//              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
//            }
//          }
    });
    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 100);
    contentPanel.on('resize', function () {
        editor.getDoc().clearHistory();
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 100);
        if (baseInfoOfScriptWin) {
            baseInfoOfScriptWin.center();
        }
        if (chooseTestAgentWin) {
            chooseTestAgentWin.center();
        }
        if (publishAuditingSMWin) {
            publishAuditingSMWin.center();
        }
    });
    tabsCenter.on('resize', function () {
        editor.getDoc().clearHistory();
        editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 100);
    });

    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };


    var testparamColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 100,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramDefaultValue',
            width: 100,
            text: '参数值',
            editor: {},
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    //加密参数解密获取长度
                    var decodeVal = getSMEncode(value,0);
                    if(!(decodeVal == null || decodeVal == '')){
                        value = decodeVal;
                    }
                    backValue = StringToPassword(value);
                } else {
                    backValue = value;
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';

                return backValue;
            }
        },
//    {
//        text: '参数值',
//        dataIndex: 'paramDefaultValue',
//        width: 70,
//        editor: {
//            allowBlank: true
//        },
//        renderer:function (value, metaData, record, rowIdx, colIdx, store){
//            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
//            return value;
//        }
//    },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 70,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }];
    var testparamStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    //加密参数将参数值转换成明文在前台展示
    testparamStore.on ('load', function (me, records, successful, eOpts) {
        $.each(records, function(index, record){
            if(record.get('paramType') == 'IN-string(加密)'){
                record.set('paramDefaultValue', getSMEncode(record.get('paramDefaultValue'),0));
            }
        });
    });
    var testparamGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        store: testparamStore,
        plugins: [cellEditing3],
        border: false,
        columnLines: true,
//        collapsible : true,
//        collapsed: true,
        //height:30,
        columns: testparamColumns,
        listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {

                var columnIndex = e.column.dataIndex;
                // 点击的当前行数据
                var recordData = e.record.data;

                var paramType = recordData.paramType;           // 是否为枚举类型
                var parameterName = recordData.parameterName;   // 参数名称
                // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                    golbalParamName = parameterName;
                    defaultValueStore.load();
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore,
                        editable: false
                    });
                }
                if (paramType == 'IN-string(加密)') {
                    e.column.setEditor(passwordEditor)
                }
            }
        }
    });

    testparamStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid
        };

        Ext.apply(testparamStore.proxy.extraParams, new_params);
    });
    var paramPanel = Ext.create('Ext.panel.Panel', {
        region: 'north',
        cls: 'window_border panel_space_top panel_space_right panel_space_left panel_space_bottom ',
        border: true,
        layout: 'border',
        height: contentPanel.getHeight() * 0.25,
        items: [testparamGrid]
    });

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    function testScript() {
        execUserForTry.setValue(suUser.getValue());
        if (chooseTestAgentWin) {
            chooseTestAgentWin.show();
        } else {
            var grdi_panel = Ext.create('Ext.panel.Panel', {
                title: '已选服务器',
                width: contentPanel.getWidth() - 252,
                height: contentPanel.getHeight() - 80,
                cls: 'window_border  panel_space_right panel_space_left panel_space_bottom ',
                layout: 'border',
//				header : false,
                region: 'center',
                border: false,
                listeners: {
                    activate: function (tab) {
                        resGroupFlag = 'false';
                        agent_store.load();
                        chosedGroupIds.splice(0, chosedGroupIds.length);
                    }
                },
                //items : [search_form, agent_grid, db_soucre_grid ]
                items: [search_form, agent_grid, db_soucre_grid]
            });


            //*********************************************增加资源组相关控件 start***************************************************************************

            Ext.define('groupModel', {
                extend: 'Ext.data.Model',
                idProperty: 'id',
                fields: [
                    {name: 'id', type: 'long'},
                    {name: 'name', type: 'string'},
                    {name: 'execUserName', type: 'string'},
                    {name: 'description', type: 'string'}
                ]
            });
            var group_columns = [
                {text: '序号', xtype: 'rownumberer', width: 40},
                {text: '主键', dataIndex: 'id', hidden: true},
                {text: '组名称', dataIndex: 'name', width: 160},
                {text: '启动用户', dataIndex: 'execUserName', width: 160},
                {text: '描述', dataIndex: 'description', width: 280},
                {
                    text: '操作',
                    xtype: 'actiontextcolumn',
                    flex: 1,
                    align: 'left',
                    items: [{
                        text: '详情',
                        iconCls: 'execute',
                        handler: function (grid, rowIndex) {
                            var iid = grid.getStore().data.items[rowIndex].data.id;
                            getAgentInfoByGroupId(iid);
                        }
                    }]
                }];

            /**
             * 获取资源组下的agent
             */
            function getAgentInfoByGroupId(iid) {
                Ext.define('agentModelByGroup', {
                    extend: 'Ext.data.Model',
                    idProperty: 'id',
                    fields: [
                        {name: 'id', type: 'long'},
                        {name: 'ip', type: 'string'},
                        {name: 'port', type: 'string'},
                        {name: 'hostName', type: 'string'}
                    ]
                });

                var agentinfo_group_store = Ext.create('Ext.data.Store', {
                    autoLoad: false,
                    pageSize: 50,
                    model: 'agentModelByGroup',
                    proxy: {
                        type: 'ajax',
                        url: 'agentGroup/getServersForTaskApply.do',
                        reader: {
                            type: 'json',
                            root: 'dataList',
                            totalProperty: 'total'
                        }
                    }
                });

                agentinfo_group_store.on('beforeload', function (store, options) {
                    var new_params = {
                        groupId: iid
                    };

                    Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
                });
                var agentinfo_columns_group = [
                    {text: '序号', xtype: 'rownumberer', width: 40},
                    {text: '主键', dataIndex: 'id', hidden: true},
                    {text: 'Agent名称', dataIndex: 'hostName', flex: 1},
                    {text: 'IP', dataIndex: 'ip', width: 160},
                    {text: '端口', dataIndex: 'port', width: 160}];


                var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
                    region: 'center',
                    store: agentinfo_group_store,
                    border: false,
                    columnLines: true,
                    cls: 'customize_panel_back',
                    columns: agentinfo_columns_group,
                    ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
                });
                agentinfo_group_store.load();
                var agentinfoGroupWin = Ext.create('Ext.window.Window', {
                    title: '增加资源组',
                    autoScroll: true,
                    modal: true,
                    resizable: false,
                    closeAction: 'hide',
                    layout: 'border',
                    width: contentPanel.getWidth() - 190,
                    height: contentPanel.getHeight(),
                    items: [agentinfo_group_grid]
                });
                agentinfoGroupWin.show();
            }


            var group_columns_chosed = [
                {text: '序号', xtype: 'rownumberer', width: 40},
                {text: '主键', dataIndex: 'id', hidden: true},
                {text: '组名称', dataIndex: 'name', width: 160},
                {text: '启动用户', dataIndex: 'execUserName', width: 160},
                {text: '描述', dataIndex: 'description', flex: 1}];
            var group_store_chosed = Ext.create('Ext.data.Store', {
                autoLoad: false,
                pageSize: 30,
                model: 'groupModel',
                proxy: {
                    type: 'ajax',
                    url: 'agentGroup/groups.do',
                    actionMethods: {
                        create: 'POST',
                        read: 'POST', // by default GET
                        update: 'POST',
                        destroy: 'POST'
                    },
                    reader: {
                        type: 'json',
                        root: 'dataList',
                        totalProperty: 'total'
                    }
                }
            });
            var group_store = Ext.create('Ext.data.Store', {
                autoLoad: true,
                pageSize: 50,
                model: 'groupModel',
                proxy: {
                    type: 'ajax',
                    url: 'agentGroup/groups.do',
                    reader: {
                        type: 'json',
                        root: 'dataList',
                        totalProperty: 'total'
                    }
                }
            });
            group_store_chosed.on('beforeload', function (store, options) {
                var new_params = {
                    groupIids: chosedGroupIds,
                    from: "taskApplayChosed"
                }
                Ext.apply(group_store_chosed.proxy.extraParams, new_params);
            });

            group_store.on('beforeload', function (store, options) {
                var new_params = {
                    agentGroupName: iresName.getValue()
//		    	iexecUser:iexecUser.getValue()
                };
                Ext.apply(group_store.proxy.extraParams, new_params);
            });
            group_store.on('load', function (store, options) {
                var records = [];//存放选中记录
                for (var i = 0; i < group_store.getCount(); i++) {
                    var record = group_store.getAt(i);
                    for (var ii = 0; ii < chosedGroupIds.length; ii++) {
                        if (+chosedGroupIds[ii] == record.data.id) {
                            records.push(record);
                        }
                    }
                }
                group_grid.getSelectionModel().select(records, false, true); //选中记录
            });


            var group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
                region: 'center',
                store: group_store,
                border: false,
                columnLines: true,
                cls: 'customize_panel_back',
                columns: group_columns,
                ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
                listeners: {
                    select: function (e, record, index, eOpts) {
                        if (chosedGroupIds.indexOf(record.get('id')) == -1) {
                            chosedGroupIds.push(record.get('id'));
                        }
                    },
                    deselect: function (e, record, index, eOpts) {
                        if (chosedGroupIds.indexOf(record.get('id')) > -1) {
                            chosedGroupIds.remove(record.get('id'));
                        }
                    }
                }
            });

            var iresName = new Ext.form.TextField({
                name: 'iresName',
                fieldLabel: '组名称',
                displayField: 'iresName',
                emptyText: '--请输入组名称--',
                labelWidth: 70,
                labelAlign: 'right',
                width: '25%'
            });
            var iexecUser = new Ext.form.TextField({
                name: 'iexecUser',
                fieldLabel: '启动用户',
                displayField: 'iexecUser',
                hidden: true,
                emptyText: '--启动用户--',
                labelWidth: 70,
                labelAlign: 'right',
                width: '25%'
            });

            var search_group_form = Ext.create('Ext.ux.ideal.form.Panel', {
                region: 'north',
                border: false,
                iqueryFun: function () {
                    group_grid.ipage.moveFirst();
                },
                bodyCls: 'x-docked-noborder-top',
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    items: [iresName, iexecUser,
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '查询',
                            handler: function () {
                                group_grid.ipage.moveFirst();
                            }
                        },
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '清空',
                            handler: function () {
                                iresName.setValue('');
                                iexecUser.setValue('');
                            }
                        }]
                }]
            });


            var group_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
                title: '已选资源组',
                region: 'west',
                cls: 'window_border panel_space_top panel_space_left panel_space_right',
                store: group_store_chosed,
                border: true,
                width: '100%',
                columnLines: true,
                height: contentPanel.getHeight() * 0.48,
                ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                emptyText: '没有选择资源组',
                columns: group_columns_chosed,
                selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
                listeners: {
                    activate: function (tab) {
                        resGroupFlag = 'true';
                        group_store_chosed.load();
                        chosedAgentIds.splice(0, chosedAgentIds.length);
                    }
                },
                dockedItems: [
                    {
                        xtype: 'toolbar',
                        dock: 'top',
                        items: [
                            {
                                xtype: 'button',
                                cls: 'Common_Btn',
                                text: '删除',
                                handler: function () {
                                    var records = group_grid_chosed.getSelectionModel().getSelection();
                                    if (records.length > 0) {
                                        for (var i = 0, len = records.length; i < len; i++) {
                                            chosedGroupIds.remove(records[i].get('id'));
                                        }
                                        group_grid_chosed.ipage.moveFirst();
                                        group_grid.ipage.moveFirst();
                                    } else {
                                        Ext.Msg.alert('提示', '请选择资源组！')
                                        return
                                    }
                                }
                            },
                            {
                                xtype: 'button',
                                cls: 'Common_Btn',
                                text: '增加资源组',
                                handler: function () {
                                    if (!chosedGroupWin) {
                                        chosedGroupWin = Ext.create('Ext.window.Window', {
                                            title: '增加资源组',
                                            autoScroll: true,
                                            modal: true,
                                            resizable: false,
                                            closeAction: 'hide',
                                            layout: 'border',
                                            width: contentPanel.getWidth() - 190,
                                            height: contentPanel.getHeight(),
                                            items: [search_group_form, group_grid],
                                            dockedItems: [
                                                {
                                                    xtype: 'toolbar',
                                                    dock: 'bottom',
                                                    layout: {pack: 'center'},
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            text: '确定',
                                                            cls: 'Common_Btn',
                                                            margin: '6',
                                                            handler: function () {
                                                                group_store_chosed.load();
                                                                this.up('window').close();
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            text: '关闭',
                                                            cls: 'Common_Btn',
                                                            margin: '6',
                                                            handler: function () {
                                                                this.up('window').close();
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        });
                                    }

                                    chosedGroupWin.show();
                                    group_store.load();
                                }
                            }
                        ]
                    }
                ]
            });


            //*********************************************增加资源组相关控件 end***************************************************************************


            /** 已选窗口 资源组、单独agent tabPanel* */
            var tabPanelForChosedDevice = Ext.create('Ext.tab.Panel',
                {
                    tabPosition: 'top',
                    region: 'center',
//				    region : 'west',
                    activeTab: 0,
                    //	    cls:'customize_panel_back',
                    cls: 'window_border panel_space_top panel_space_left panel_space_right',
                    width: '100%',
//				    height: contentPanel.getHeight()*0.58,
                    //	    height : contentPanel.getHeight (),
                    border: false,
                    defaults:
                        {
                            autoScroll: false
                        },
                    items: [grdi_panel, group_grid_chosed]

                });


            var fPanel = Ext.create('Ext.panel.Panel', {
                border: true,
                layout: 'border',
                width: contentPanel.getWidth() - 115,
                height: contentPanel.getHeight() - 70,
                items: [paramPanel, tabPanelForChosedDevice]
            });
            chooseTestAgentWin = Ext.create('Ext.window.Window', {
                title: '选择测试服务器',
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                width: contentPanel.getWidth() - 95,
                height: contentPanel.getHeight() - 20,
                items: [fPanel],
                buttonAlign: 'center',
                buttons: [{
                    xtype: "button",
//		  			cls:'Common_Btn',
                    text: "确定",
                    handler: function () {
                        var p = orgParamsTest();
                        var paramInvalidMessage = p.paramInvalidMessage;
                        if(paramInvalidMessage){
                            Ext.MessageBox.alert("提示", paramInvalidMessage);
                            return;;
                        }
                        var type = "sh";
                        if (updatecheckRadio == '0') {
                            type = "sh";
                        } else if (updatecheckRadio == '1') {
                            type = "bat";
                        } else if (updatecheckRadio == '2') {
                            type = "perl";
                        } else if (updatecheckRadio == '3') {
                            type = "py";
                        } else if (updatecheckRadio == '4') {
                            type = "sql";
                        } else if (updatecheckRadio == '6') {
                            type = "ps1";
                        }


                        if (resGroupFlag == 'true' && updatecheckRadio == 4) {
                            Ext.Msg.alert('提示', 'sql类型不允许选择资源组！');
                            return;
                        }

                        var me = this;
                        var records = agent_grid.getSelectionModel().getSelection();
                        var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
                        if (resGroupFlag == 'true' && chosedGroupIds.length <= 0) {
                            Ext.Msg.alert('提示', '请选择资源组！');
                            return;
                        } else {
                            if (records.length != 1) {
                                Ext.Msg.alert('提示', '请选择记录，并且只能选择一条记录！');
                                return;
                            }
                            if (updatecheckRadio == 4) {
                                if (dsrecords.length != 1) {
                                    Ext.Msg.alert('提示', '请选择数据源，并且只能选择一个！');
                                    return;
                                }
                            }
                        }
                        var i = records[0].data;
                        var jsonData = "[" + Ext.JSON.encode(records[0].data) + "]";
                        var dsid = 0;
                        if (dsrecords.length > 0) {
                            dsid = Ext.JSON.encode(dsrecords[0].data.dsId);
                        }
                        var scriptPara = '';
                        testparamStore.sort('paramOrder', 'ASC');
                        var m = testparamStore.getRange(0, testparamStore.getCount() - 1);
                        var aaaa = [];
                        for (var i = 0, len = m.length; i < len; i++) {
                            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
                            if(paramDefaultValue !=  '') {
                                aaaa.push(getSMEncode(paramDefaultValue,1));
                            } else {
                                aaaa.push('');
                            }
//		  		            scriptPara += paramDefaultValue + " ";
                        }
                        scriptPara = aaaa.join("@@script@@service@@");
                        var jsonDataPara = "[";
                        for (var i = 0, len = m.length; i < len; i++) {
                            var ss = Ext.JSON.encode(m[i].data);
                            if (i == 0) jsonDataPara = jsonDataPara + ss;
                            else jsonDataPara = jsonDataPara + "," + ss;
                        }
                        jsonDataPara = jsonDataPara + "]";
                        var rulejsonData = "[";
                        if (projectFlag == 1) {
                            var patt1 = new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
                            var mm = paramRulesStore
                                .getRange();
                            var outArr = {};
                            var arrContain = [];
                            for (var i = 0, len = mm.length; i < len; i++) {
                                var paramRuleOrder = mm[i].get("paramRuleOrder") ? mm[i].get("paramRuleOrder") : '';
                                var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
                                var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
                                var paramRuleType = mm[i].get("paramRuleType");
                                var paramRuleLen = mm[i].get("paramRuleLen");
                                if ("" == paramRuleOrder) {
                                    setMessage('输出规则顺序不能为空！');
                                    return;
                                }
                                paramRuleOut = paramRuleOut.toUpperCase();
                                if ("" == paramRuleOut) {
                                    setMessage('输出规则输出不能为空！');
                                    return;
                                }
                                if ("GROUP" == paramRuleOut || "IID" == paramRuleOut || "IRESID_BULID_IN" == paramRuleOut || "IDBID_BULID_IN" == paramRuleOut || "ITIME_BULID_IN" == paramRuleOut || "ISERVICEID_BULID_IN" == paramRuleOut || "ISTARTTIME_BULID_IN" == paramRuleOut || "IDBCOPY_BULID_IN" == paramRuleOut || "ITRIMODEL_BULID_IN" == paramRuleOut || "IFLAG_BULID_IN" == paramRuleOut || "IFLOWID_BULID_IN" == paramRuleOut) {
                                    setMessage('部分输出规则已经内置,请重新填写！' + "“" + paramRuleOut + "”");
                                    return;
                                }
                                if (arrContain.contains(paramRuleOut) > 0) {
                                    setMessage('输出规则不能重复！');
                                    return;
                                } else {
                                    arrContain.push(paramRuleOut)
                                }
                                if (!patt1.test(paramRuleOut)) {
                                    setMessage('请正确填写输出规则！');
                                    return;
                                }
                                if (paramRuleType == 6) {
                                    setMessage('分区表不支持LONG类型列！');
                                    return;
                                }
                                if (paramRuleLen == 0 && paramRuleType != 5/*&& paramRuleType!=6*/) {
                                    setMessage('长度不能为0字符！');
                                    return;
                                } else {
                                    if (paramRuleType == 2) {
                                        if (paramRuleLen > 38) {
                                            setMessage('DECIMAL类型的长度最大为38!');
                                            return;
                                        }
                                    }
                                }
                                if (fucCheckLength(paramRuleDesc) > 250) {
                                    setMessage('输出规则描述不能超过250字符！');
                                    return;
                                }
                                if (outArr[paramRuleOrder]) {
                                    setMessage('输出规则序号不能重复！');
                                    return;
                                } else {
                                    outArr[paramRuleOrder] = true;
                                }
                                var ss = Ext.JSON
                                    .encode(mm[i].data);
                                if (i == 0)
                                    rulejsonData = rulejsonData
                                        + ss;
                                else
                                    rulejsonData = rulejsonData
                                        + ","
                                        + ss;
                            }
                        }
                        rulejsonData = rulejsonData
                            + "]";

                        var userName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + records[0].get('iid'));

                        Ext.Ajax.request({
                            url: 'execScriptServiceForSync.do',
                            method: 'POST',
                            params: {
                                serviceId: newServiceId,
//			      				execUser: execUserForTry.getValue(),
                                execUser: (userName == '' || userName == null) ? execUserForTry.getValue() : userName,
                                scriptPara: scriptPara,
                                jsonDataPara: jsonDataPara,
                                resGroupFlag: resGroupFlag,
                                chosedGroupIds: chosedGroupIds,
                                jsonData: jsonData,
                                dbsourceid: dsid,
                                ifrom: 0,
                                flag: 0,
                                gdSwitch: projectFlag
                            },
                            success: function (response, request) {
//			      				var coatId = Ext.decode(response.responseText).coatId;
//			      				var flowId = Ext.decode(response.responseText).flowId;
                                if (resGroupFlag == 'false') {
                                    var requestId = Ext.decode(response.responseText).requestIds[0];
                                    isFromTryATry = 0;
                                    tryRequestId = requestId;
                                    tryAgentIp = records[0].get('agentIp');
                                    tryAgentPort = records[0].get('agentPort');
                                    if (refreshTryForUpdate) {
                                        clearInterval(refreshTryForUpdate);
                                    }

                                    refreshTryForUpdate = setInterval(function () {
                                        loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                    }, 5 * 1000);
                                    if (projectFlag != 1 && script_showGridSwitch) {
                                        consolePanel.setTitle('执行日志&nbsp;&nbsp;-> &nbsp;&nbsp;<a href="gotoOperResultData.do?operId=' + requestId + '"  target="_Blank">表格展示...</a>');
                                    }
                                }

                                me.up("window").close();
                                $('#consoleLog-edit').html("");
                                settime(tryTestbtn, '测试');
                                if (resGroupFlag == 'true') {
                                    Ext.Msg.alert('提示', "脚本已在指定服务器上运行，选择的资源组类型，请在测试历史中查看日志！");
                                } else {
                                    Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
                                }
                                destroyRubbish();

                            },
                            failure: function (result, request) {
                                Ext.Msg.alert('提示', '执行失败！');
                                $this.html('执行脚本');
                            }
                        });
                    }
                }, {
                    xtype: "button",
//		  			cls:'Gray_button',
                    text: "取消",
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            }).show();
        }
        resourceGroupObj.setValue('');
        search_form.getForm().findField("agentIp").setValue('');
        //search_form.getForm().findField("agentDesc").setValue('');
        agent_store.load();
    }

    function getParams() {
        paramStore.sort('paramOrder', 'ASC');
        var m = paramStore.getRange(0, paramStore.getCount() - 1);
        var aaaa = [];
        for (var i = 0, len = m.length; i < len; i++) {
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue") : '';
            var paramType = m[i].get("paramType") ? m[i].get("paramType") : '';
            if (projectFlag == 1) {
                aaaa.push(paramType + ":" + paramDefaultValue);
            } else {
                aaaa.push(paramDefaultValue);
            }
        }
        return aaaa.join("@@script@@service@@");
    }


    function orgParams() {
        paramStore.sort('paramOrder', 'ASC');
        var m = paramStore.getRange(0, paramStore.getCount() - 1);
        var aaaa = [];
        var someParamIsEmpty = false;
        var paramInvalidMessage = '';
        for (var i = 0, len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue") : '';

            if (!paramDefaultValue) {
                someParamIsEmpty = true;
            }
            if ((paramType == 'OUT-int' || paramType == 'IN-int') && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    paramInvalidMessage = '参数类型为int，但参数值不是int类型！';
                    break;
                }
            }
            if ((paramType == 'OUT-float' || paramType == 'IN-float') && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    paramInvalidMessage = '参数类型为float，但参数值不是float类型！';
                    break;
                }
            }
            if (paramDefaultValue.indexOf('"') >= 0) {
                if (updatecheckRadio == '1') {
                    paramInvalidMessage = 'bat脚本暂时不支持具有双引号的参数值';
                    break;
                }
            }
            aaaa.push(paramDefaultValue);
        }

        return {
            someParamIsEmpty: someParamIsEmpty,
            paramInvalidMessage: paramInvalidMessage,
            scriptPara: aaaa.join("@@script@@service@@")
        };
    }

    function orgParamsTest() {
        testparamStore.sort('paramOrder', 'ASC');
        var m = testparamStore.getRange(0, testparamStore.getCount() - 1);
        var aaaa = [];
        var someParamIsEmpty = false;
        var paramInvalidMessage = '';
        for (var i = 0, len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue") : '';

            if (!paramDefaultValue) {
                someParamIsEmpty = true;
            }
            if ((paramType == 'OUT-int' || paramType == 'IN-int') && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    paramInvalidMessage = '参数类型为int，但参数值不是int类型！';
                    break;
                }
            }
            if ((paramType == 'OUT-float' || paramType == 'IN-float') && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    paramInvalidMessage = '参数类型为float，但参数值不是float类型！';
                    break;
                }
            }
            if (paramDefaultValue.indexOf('"') >= 0) {
                if (updatecheckRadio == '1') {
                    paramInvalidMessage = 'bat脚本暂时不支持具有双引号的参数值';
                    break;
                }
            }
            aaaa.push(paramDefaultValue);
        }

        return {
            someParamIsEmpty: someParamIsEmpty,
            paramInvalidMessage: paramInvalidMessage,
            scriptPara: aaaa.join("@@script@@service@@")
        };
    }

    function openTipWindow(response, callback, callType) {
        let msgTextArea = Ext.create('Ext.form.HtmlEditor', {
            name: 'msgTextArea',
            fieldLabel: '提示',
            //emptyText: '',
            labelWidth: 40,
            labelAlign: 'right',
            enableFont: false,  //隐藏或显示字体选项
            enableFontSize: false, //允许增大/缩小字号按钮(默认为 true)。
            enableFormat: false,   //允许字体加粗
            enableLinks: false,  //启用链接创建按钮。
            enableSourceEdit: false,  //允许切换到源码编辑按钮。
            enableAlignments: false, //启用左对齐、中间对齐、右对齐按钮
            enableColors: false,  //启用回退/高亮颜色按钮
            enableLists: false, //启用bullet和有限数量的按钮列表。
            border: false,
            // margin : '10 0 0 0',
            //maxLength: 255,
            cls: 'scriptTipKeyWord',
            height: 200,
            columnWidth: .98,
            readOnly: true,
            autoScroll: true,
            value: Ext.decode(response.responseText).message
        });
        // msgTextArea.setValue(Ext.decode(response.responseText).message);
        let messagePa = Ext.create('Ext.form.Panel', {
            width: 630,
            layout: 'anchor',
            bodyCls: 'x-docked-noborder-top',
            buttonAlign: 'center',
            border: false,
            items: [{
                //	    	layout:'form',
                anchor: '98%',
                padding: '5 0 5 0',
                border: false,
                items: [{
                    layout: 'column',
                    border: false,
                    items: [msgTextArea]
                }]
            }],
            bbar: ['->', {
                xtype: 'button',
                text: '是',
                cls: 'Common_Btn',
                handler: function () {
                    this.up("window").close();
                    save(1, callback, callType);
                }
            }, {
                xtype: 'button',
                text: '否',
                cls: 'Common_Btn',
                handler: function () {
                    this.up("window").close();
                }
            }, '->']
        });

        let messageWin = Ext.create('Ext.window.Window', {
            title: "脚本中存在关键命令,是否继续发布脚本？",
            width: 650,
            height: 340,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            items: [messagePa]
        });
        // Ext.Msg.close();
        messageWin.show();
    }
    function checkWin(ignoreFlag, callback, callType, ignoreSyntaxFlag,checkFlag,checkLineFlag,checkResult) {
        Ext.Msg
            .confirm(
                "请确认",
                "脚本未包含"+ checkResult+"函数？<br>是否继续发布？",
                function(button, text) {
                    if (button == "yes") {
                        save(ignoreFlag, callback, callType, ignoreSyntaxFlag,1,checkLineFlag);
                    }
                });
    }
    function checkLastLineWin(ignoreFlag, callback, callType, ignoreSyntaxFlag,checkFlag,checkLineFla,excepResult1) {
        Ext.Msg
            .confirm(
                "请确认",
                "脚本最后一行输出值和预期值"+excepResult1+"不同<br>是否继续发布？",
                function(button, text) {
                    if (button == "yes") {
                        save(ignoreFlag, callback, callType, ignoreSyntaxFlag,checkFlag,1);
                    }
                });
    }
    /**
     * 保存脚本信息
     * @param ignoreFlag 跳过危险命令标识 1强制保存
     * @param callback
     * @param callType
     * @param ignoreSyntaxFlag 跳过语法校验标识 1跳过
     */
    function save(ignoreFlag, callback, callType, ignoreSyntaxFlag,checkFlag,checkLineFlag) {
        // ignoreFlag ::
        //  0:正常保存
        //  1:忽略提醒命令，继续保存
        var sysId = bussCb.getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussTypeId = bussTypeCb.getValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var sysName = bussCb.getRawValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussType = bussTypeCb.getRawValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var threeBussTypeId = threeBussTypeCb.getValue();
        var threeBussTypeName = threeBussTypeCb.getRawValue();
        var serverName = sName.getValue().trim();
        var scriptName1 = scName.getValue().trim();
        var up = usePlantForm.getValue() || "";
        var errExcepResult1 = errExcepResult.getValue();
        var excepResult1 = excepResult.getValue();
        var scriptDesc1 = funcDescInWin.getValue();
        var suExecUser = suUser.getValue() || "";//启动用户
        var groupId = groupNameCombo.getValue();
        var groupName = groupNameCombo.getRawValue();
        var scriptWorkDirValue = scriptWorkDir.getValue().trim();
        //功能分类开关开启，需要检验脚本功能分类是否填写
        if(sdFunctionSortSwitch){
            if(groupName == '' || groupName == null){
                Ext.Msg.alert('提示', '请填写功能分类');
                return;
            }
        }
        if (gfScriptDirFunctionSwitch) {
            var selScriptDir = Ext.getCmp('treeId').getSelectionModel().getSelection();
            if (selScriptDir.length > 1) {
                Ext.Msg.alert('提示', '要保存的目录大于1');
                return;
            } else if (selScriptDir.length < 1) {
                if (scriptDirId == 0) {
                    Ext.Msg.alert('提示', '请选择要保存的目录');
                    return;
                }
            } else {
                if (selScriptDir[0].childNodes.length > 0) {
                    Ext.Msg.alert('提示', '所选目录不是末级目录');
                    return;
                } else if (selScriptDir[0].childNodes.length == 0) {
                    scriptDirId = selScriptDir[0].data.iid;
                } else {
                    return;
                }
            }
            // 处理 老数据 没有历史绑定关系， 且
            if (scriptDirId == '' || scriptDirId == undefined) {
                Ext.Msg.alert('提示', '当前脚本无绑定关系，不能保存');
            }
        }
        if (versionFlag) {
            if (!(serverName == oldserviceName)) {
                setMessage('该脚本存在已上线版本，不能再修改服务名称！');
                return;
            }
        }
//        var timeoutValue = timeout.getValue()==null?-1:timeout.getValue();
        var timeoutValue = timeout.getValue();
        if (timeoutValue) {
            if (!checkIsInteger(timeoutValue)) {
                setMessage('请输入数字类型的超时时间！');
                return;
            }
        }
        // 光大项目 发起审核下拉选
        var audittestValue = audittest.getValue();
        // 光大项目数据库类型下拉选
        var dbtestValue = dbtest.getValue();
        // 光大项目 服务类型下拉选
        var serviceTtextValue = serviceType.getValue();
        if (!scriptName1) {
            scName.setValue(scriptName);
        }
        if (!scriptDesc1) {
            funcDescInWin.setValue(scriptDesc);
        }
        baseInfoOfScriptWin.close();
        if (projectFlag != 1) {// ===============================光大页面增加的开关
            if (!sysId) {
                saveFromBottom = true;
                baseInfoOfScriptWin.show();
                // Ext.MessageBox.alert("提示", "请选择一级分类!");
                return;
            }
            if (!bussTypeId) {
                saveFromBottom = true;
                baseInfoOfScriptWin.show();
                // Ext.MessageBox.alert("提示", "请选择二级分类!");
                return;
            }
        }
        if (serverName.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //   Ext.MessageBox.alert("提示", "服务名称不能为空!");
            return;
        }
        if (scriptName1.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //   Ext.MessageBox.alert("提示", "脚本名称不能为空!");
            return;
        }
        if (projectFlag == 1) {
            if (audittestValue.trim() == '') {// ======================================开关
                saveFromBottom = true;
                baseInfoOfScriptWin.show();
                return;
            }
            if (dbtestValue == '') {// ======================================开关
                saveFromBottom = true;
                baseInfoOfScriptWin.show();
                return;
            }
            if (serviceTtextValue.trim() == '') {// ======================================开关
                saveFromBottom = true;
                baseInfoOfScriptWin.show();
                return;
            }
        }
        if (up.length == 0) {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //  Ext.MessageBox.alert("提示", "适用平台不能为空!");
            return;
        }
        if (scriptDesc1.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
//            Ext.MessageBox.alert("提示", "功能说明不能为空!");
            return;
        }

        /*if(hasVersion) {
        	var m = paramStore.getRange();
        } else {
        	var m = paramStore.getModifiedRecords();
        }*/
        var m = paramStore.getRange();
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
            var iorder = m[i].get("paramOrder");
            var parameterName = m[i].get("parameterName");
            var ruleName = m[i].get("ruleName");
            if (paramType == '枚举') {
                if (paramDefaultValue == "" || paramDefaultValue == null) {
                    setMessage('默认值不能为空！');
                    return;
                }
            }
            //根据选中的验证规则名拿到对应的正则表达式
            if (bhParameterCheckSwitch) {
                if (ruleName != "" && ruleName != null) {
                    Ext.Ajax.request({
                        url: 'queryParameterCheckRule.do',
                        method: 'POST',
                        async: false,
                        params: {
                            ruleName: ruleName
                        },
                        success: function (response, request) {
                            dataList = Ext.decode(response.responseText).dataList;

                        },
                        failure: function (result, request) {
                            Ext.Msg.alert('提示', '获取验证规则失败！');
                        }
                    });
                    //用拿到的正则去校验默认值
                    var patt = new RegExp(dataList);
                    if (patt.exec(paramDefaultValue) == null) {
                        setMessage('顺序为' + "“" + iorder + "”" + '的默认值校验不通过请检查！');
                        return;
                    }
                }
            }
            if ("" == paramType) {
                setMessage('参数类型不能为空！');
                return;
            }
            if (fucCheckLength(paramDesc) > 250) {
                setMessage('参数描述不能超过250字符！');
                return;
            }
            let count = 0;
            for (let j = 0; j < paramDefaultValue.length; j++) {
                if (paramDefaultValue[j] == "'") {
                    count++;
                }
            }
            if (count % 2 !=0) {
                setMessage('参数默认值输入单引号，必须前引号、后引号同时出现！');
                return;
            }
            if (paramDefaultValue.indexOf('"') > -1) {
                setMessage('参数默认值不允许输入双引号！');
                return;
            }

            if ((paramType == 'OUT-int' || paramType == 'IN-int' || paramType == 'int') && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    setMessage('参数类型为int，但参数默认值不是int类型！');
                    return;
                }
            }
            if ((paramType == 'OUT-float' || paramType == 'IN-float' || paramType == 'float') && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    setMessage('参数类型为float，但参数默认值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"') != -1) {
                setMessage('参数中不允许有双引号！');
                return ;
            }
            for (var k = 0; k < paramStore.getCount(); k++) {
                var record = paramStore.getAt(k);
                var order = record.data.paramOrder;
                if (order == iorder) {
                    n = n + 1;
                }
            }
            if (n > 1) {
                Ext.MessageBox.alert("提示", "参数顺序不能重复！");
                return;
            }
            if (paramType == '枚举') {
                if (parameterName == "" || parameterName == null) {
                    setMessage('枚举名不能为空！');
                    return;
                }
            }
            if (paramType == '枚举') {
                if (paramDefaultValue == "" || paramDefaultValue == null) {
                    setMessage('默认值不能为空！');
                    return;
                }
            }

            //如果参数类型为加密字符串，则对值进行加密处理
            var defaultStrValue = "";
            if (paramType == 'IN-string(加密)') {
                if(paramDefaultValue != null && paramDefaultValue != ''){
                    var encodeVal = getSMEncode(paramDefaultValue,0);
                    if(encodeVal != '' && encodeVal != null){
                        paramDefaultValue = encodeVal;
                    }
                    defaultStrValue = getSMEncode(paramDefaultValue,1);
                }
            }else {
                defaultStrValue = paramDefaultValue;
            }

            var paramRecord = {
                iid: m[i].data.iid,
                paramType: m[i].data.paramType,
                paramDefaultValue: defaultStrValue,
                ruleName: m[i].data.ruleName,
                paramDesc: m[i].data.paramDesc,
                paramOrder: m[i].data.paramOrder,
                parameterName: m[i].data.parameterName
            }
            var ss = Ext.JSON.encode(paramRecord);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }

        jsonData = jsonData + "]";
        var rulejsonData = "[";
        if (projectFlag == 1) {
            var patt1 = new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
            var mm = paramRulesStore.getRange();
            var outArr = {};
            var arrContain = [];
            for (var i = 0, len = mm.length; i < len; i++) {
                var n = 0;
                var paramRuleOrder = mm[i].get("paramRuleOrder") ? mm[i].get("paramRuleOrder") : '';
                var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
                var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
                var paramRuleType = mm[i].get("paramRuleType");
                var paramRuleLen = mm[i].get("paramRuleLen");
                if ("" == paramRuleOrder) {
                    setMessage('输出规则顺序不能为空！');
                    return;
                }
                paramRuleOut = paramRuleOut.toUpperCase();
                if ("" == paramRuleOut) {
                    setMessage('输出规则输出不能为空！');
                    return;
                }
                if ("GROUP" == paramRuleOut || "IID" == paramRuleOut || "IRESID_BULID_IN" == paramRuleOut || "IDBID_BULID_IN" == paramRuleOut || "ITIME_BULID_IN" == paramRuleOut || "ISERVICEID_BULID_IN" == paramRuleOut || "ISTARTTIME_BULID_IN" == paramRuleOut || "IDBCOPY_BULID_IN" == paramRuleOut || "ITRIMODEL_BULID_IN" == paramRuleOut || "IFLAG_BULID_IN" == paramRuleOut || "IFLOWID_BULID_IN" == paramRuleOut) {
                    setMessage('部分输出规则已经内置,请重新填写！' + "“" + paramRuleOut + "”");
                    return;
                }
                if (arrContain.contains(paramRuleOut) > 0) {
                    setMessage('输出规则不能重复！');
                    return;
                } else {
                    arrContain.push(paramRuleOut)
                }
                if (!patt1.test(paramRuleOut)) {
                    setMessage('请正确填写输出规则！');
                    return;
                }
                if (paramRuleType == 6) {
                    setMessage('分区表不支持LONG类型列！');
                    return;
                }
                if (paramRuleLen == 0 && paramRuleType != 5/*&& paramRuleType!=6*/) {
                    setMessage('长度不能为0字符！');
                    return;
                } else {
                    if (paramRuleType == 2) {
                        if (paramRuleLen > 38) {
                            setMessage('NUMBER类型的长度最大为38!');
                            return;
                        }
                    }
                }
                if (fucCheckLength(paramRuleDesc) > 250) {
                    setMessage('输出规则描述不能超过250字符！');
                    return;
                }
                if (outArr[paramRuleOrder]) {
                    setMessage('输出规则序号不能重复！');
                    return;
                } else {
                    outArr[paramRuleOrder] = true;
                }
                var ss = Ext.JSON.encode(mm[i].data);
                if (i == 0)
                    rulejsonData = rulejsonData + ss;
                else
                    rulejsonData = rulejsonData + "," + ss;
            }
            //判断输出字段是否在黑名单中，如果有，则提示
            var iskeyWordResult;
            var isKeyWord;
            Ext.Ajax.request({
                url: 'checkKeyWord.do',
                method: 'POST',
                async: false,
                params: {
                    paramRules: arrContain
                },
                success: function (response, opts) {
                    iskeyWordResult = Ext.decode(response.responseText).result;
                    isKeyWord = Ext.decode(response.responseText).isKeyWord;
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
            if (iskeyWordResult) {
                Ext.MessageBox.alert("提示", "输出字段:" + isKeyWord + "为关键字，请修改！");
                return;
            }
        }
        rulejsonData = rulejsonData + "]";
        editor.save();
        var content = document.getElementById('code-edit').value;
        if (content.trim() == '') {
            Ext.MessageBox.alert("提示", "脚本内容不能为空！");
            return;
        }
        var type = "sh";
        if (updatecheckRadio == '0') {
            type = "sh";
        } else if (updatecheckRadio == '1') {
            type = "bat";
        } else if (updatecheckRadio == '2') {
            type = "perl";
        } else if (updatecheckRadio == '3') {
            type = "py";
        } else if (updatecheckRadio == '4') {
            type = "sql";
        } else if (updatecheckRadio == '6') {
            type = "ps1";
        }
        if (type == 'bat' && up != 'Windows') {
            Ext.Msg.alert('提示', 'bat脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if (type == 'ps1' && up != 'Windows') {
            Ext.Msg.alert('提示', 'powershell脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if ((type == 'sh' || type == 'perl') && up.indexOf("Windows") != -1) {
            Ext.Msg.alert('提示', '只允许 bat、python、sql、powershell脚本才能选择Windows平台');
            usePlantForm.setValue();
            return;
        }
        if (checkLength(scriptWorkDirValue) > 255) {
            Ext.Msg.alert('提示', '工作目录不能超过255字符');
            scriptWorkDir.setValue();
            return;
        }

        // 基本信息-标签 输入框没有渲染 标签值取我的脚本传递过来的值
        if ($('#signDiv').children().length == 0) {
            labels = labelEdit;
        } else {
            labels = $("#tagsinputval").val();
        }
        // console.log(labels);
        var saveUrl = "saveScriptEdit.do";
        if (newServiceId != 0) {
            saveUrl = "updateScriptEdit.do"
        }
        var ver = "0";// =================光大新加
        // Ext.getCmp('saveButton').disable();
        // Ext.getCmp('saveButton1').disable();
        //脚本编写合规性检查
        var checkFalse = false;
        var checkLine = false;
        //检查脚本内容是否包含check,nomal,force,log,isContinue
        if(scriptCheck!=null&&scriptCheck!=undefined&&scriptCheck!="null"){
            var scriptCheckArr = [];
            var checkResult='';
            scriptCheckArr = scriptCheck.split(",");
            for (var k = 0; k < scriptCheckArr.length; k++) {
                if (content.indexOf (scriptCheckArr[k]) <0) {
                    checkResult+=scriptCheckArr[k];
                    if(k<scriptCheckArr.length-1){
                        checkResult+=",";
                    }
                }
            }
            if (checkResult.length > 0&&checkFlag!=1) {
                checkFalse=true;
            }
        }
        if(scriptCheck!=null&&scriptCheck!=undefined&&scriptCheck!="null"){
            if(checkFlag!=null&&checkFlag!=undefined){
                if (1==checkFlag) {
                    checkFalse = false;
                }
            }
        }
        if(checkFalse&&checkFlag!=1){
            checkWin(ignoreFlag, callback, callType, ignoreSyntaxFlag,checkFlag,checkLineFlag,checkResult);
            return;
        }
        //检查脚本最后一行是否为echo 0
        if (scriptCheck != null && scriptCheck != undefined && scriptCheck != "null") {
            if (excepResult1 != null && excepResult1 != '') {
                var allLine = [];
                allLine = content.split("\n");
                var lastLine = allLine[allLine.length - 1];
                if (type == 'bat' || type == 'sh') {
                    lastLine = lastLine.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "");
                    lastLine = lastLine.slice(4)
                    if (lastLine != (excepResult1.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "")) && checkLineFlag != 1) {
                        checkLine = true;
                    }
                } else if (type == 'py') {
                    //lastLine = lastLine.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "").replaceAll("(", "").replaceAll(")", "");
                    lastLine = lastLine.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "").replace(/\(/g, "").replace(/\)/g, "");

                    lastLine = lastLine.slice(5)
                    if (lastLine != (excepResult1.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "")) && checkLineFlag != 1) {
                        checkLine = true;
                    }
                }else if(type == 'ps1'){
                    //去除空格，单双引号
                    lastLine = lastLine.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "");
                    lastLine = lastLine.slice(12)
                    if (lastLine != (excepResult1.replace(/\s*/g, "").replace(/\"/g, "").replace(/\'/g, "")) && checkLineFlag != 1) {
                        checkLine = true;
                    }
                }
            }
        }
        if(scriptCheck!=null&&scriptCheck!=undefined&&scriptCheck!="null"){
            if(checkLineFlag!=null&&checkLineFlag!=undefined){
                if (1==checkLineFlag) {
                    checkLine = false;
                }
            }
        }
        if (checkLine&&checkLineFlag!=1) {
            checkLastLineWin(ignoreFlag, callback, callType, ignoreSyntaxFlag,checkFlag,checkLineFlag,excepResult1);
            return;
        }

        /**
         *  绑定变量
         */
        //先判断在之前的插入数组bindInsertArray有无数据，避免后台重复插入
        // （filter() 方法会返回一个新的数组，其中仅包含在 varBindInsert 中没有在 bindInsertArray 中找到匹配的元素。）
        varBindInsert = varBindInsert.filter(selectedRecord => {
            let isVarInsert = bindInsertArray.some(item =>
                item.iname === selectedRecord.data.iname &&
                item.bindVarFuncId === selectedRecord.data.iid &&
                item.ibandType === 1
            );
            return !isVarInsert;
        });
        //先判断在之前的删除数组bindDeleteArray有无数据，避免后台重复删除
        // （filter() 方法会返回一个新的数组，其中仅包含在 varBindDelete 中没有在 bindDeleteArray 中找到匹配的元素。）
        varBindDelete = varBindDelete.filter(selectedRecord => {
            let isVarDelete = bindDeleteArray.some(item =>
                item.iname === selectedRecord.data.iname &&
                item.bindVarFuncId === selectedRecord.data.iid &&
                item.ibandType === 1
            );
            return !isVarDelete;
        });

        /**
         *  绑定函数
         */
        //先判断在之前的插入数组bindInsertArray有无数据，避免后台重复插入
        // （filter() 方法会返回一个新的数组，其中仅包含在 funcBindInsert 中没有在 bindInsertArray 中找到匹配的元素。）
        funcBindInsert = funcBindInsert.filter(selectedRecord => {
            let isFuncInsert = bindInsertArray.some(item =>
                item.iname === selectedRecord.data.iname &&
                item.bindVarFuncId === selectedRecord.data.iid &&
                item.ibandType === 2
            );
            return !isFuncInsert;
        });
        //先判断在之前的删除数组bindDeleteArray有无数据，避免后台重复删除
        // （filter() 方法会返回一个新的数组，其中仅包含在 funcBindDelete 中没有在 bindDeleteArray 中找到匹配的元素。）
        funcBindDelete = funcBindDelete.filter(selectedRecord => {
            let isFuncDelete = bindDeleteArray.some(item =>
                item.iname === selectedRecord.data.iname &&
                item.bindVarFuncId === selectedRecord.data.iid &&
                item.ibandType === 2
            );
            return !isFuncDelete;
        });


        //每次将数组置空，避免前一次的数组内容重复添加或者删除
        bindInsertArray = [];
        bindDeleteArray = [];
        //alreadyBind = [];

        varBindInsert.forEach((selectedRecord) => {
            let variable = {};

            variable.iname = selectedRecord.data.iname;
            variable.bindVarFuncId = selectedRecord.data.iid;
            variable.ibandType = 1;
            //选中的变量存放insert数组中
            bindInsertArray.push(variable);


            let already = alreadyBind.findIndex(item => item.data.iid === selectedRecord.data.iid);
            if(already == -1){
                selectedRecord.data.ibandType = 1; // 将 ibandType 字段添加到 data 对象中
                alreadyBind.push(selectedRecord);
                alreadyBind=Array.from(new Set(alreadyBind));
            }


        });
        varBindDelete.forEach((deSelectedRecord) => {
            let deVariable = {};

            deVariable.iname = deSelectedRecord.data.iname;
            deVariable.bindVarFuncId = deSelectedRecord.data.iid;
            deVariable.ibandType = 1;
            //选中的变量存放insert数组中
            bindDeleteArray.push(deVariable);

            let index = alreadyBind.findIndex(item => item.data.iid === deSelectedRecord.data.iid && item.data.ibandType == 1);
            if(index !== -1){
               alreadyBind.splice(index, 1); // 从数组中删除匹配到的项
            }
        });

        funcBindInsert.forEach((selectedRecord) => {
            let functions = {};

            functions.iname = selectedRecord.data.iname;
            functions.bindVarFuncId = selectedRecord.data.iid;
            functions.ibandType = 2;
            //选中函数存放insert数组
            bindInsertArray.push(functions);


            let already = alreadyBind.findIndex(item => item.data.iid === selectedRecord.data.iid);
            if(already == -1){
                selectedRecord.data.ibandType = 1; // 将 ibandType 字段添加到 data 对象中
                alreadyBind.push(selectedRecord);
                alreadyBind=Array.from(new Set(alreadyBind));
            }

        });
        funcBindDelete.forEach((deSelectedRecord) => {
            let deFunctions = {};

            deFunctions.iname = deSelectedRecord.data.iname;
            deFunctions.bindVarFuncId = deSelectedRecord.data.iid;
            deFunctions.ibandType = 2;
            //选中的变量存放insert数组中
            bindDeleteArray.push(deFunctions);


            let index = alreadyBind.findIndex(item => item.data.iid === deSelectedRecord.data.iid && item.data.ibandType == 2);
            if(index !== -1){
               alreadyBind.splice(index, 1); // 从数组中删除匹配到的项
            }
        });

        //函数变量绑定的数组中的insert对象
        VarandFuncBand.insert = bindInsertArray;
        VarandFuncBand.delete = bindDeleteArray;
        VarandFuncBandData = JSON.stringify(VarandFuncBand);
        console.log(VarandFuncBandData);
        funcBindDelete = [];
        funcBindInsert = [];
        varBindDelete = [];
        varBindInsert = [];


        if (projectFlag == 1) {
            if (callType == "发布") {
                ver = "1";
                publishScript();
            } else {
                Ext.Ajax.request({
                    url: saveUrl,
                    method: 'POST',
                    sync: true,
                    params: {
                        iid: newServiceId,
                        sysName: sysName,
                        sysId: sysId,
                        bussTypeId: bussTypeId,
                        bussType: bussType,
                        threeBussTypeId: threeBussTypeId,
                        threeBussTypeName: threeBussTypeName,
                        serverName: serverName.trim(),
                        // 系统名称 系统分类
                        scriptName: scriptName1.trim(),
                        excepResult: excepResult1,
                        errExcepResult: errExcepResult1,
                        usePlantForm: up,
                        funcDesc: scriptDesc1,
                        checkRadio: type,
                        content: content,
                        params: jsonData,
                        ruleparams: rulejsonData,
                        attachmentIds: attachmentIds,
                        templateIds: tempmentIds,
                        ignoreTipCmd: ignoreFlag,
                        // 光大项目 发起审核下拉选
                        isExam: audittestValue,
                        // 光大项目数据库类型下拉选
                        dbType: dbtestValue,
                        // 光大项目 服务类型下拉选
                        serviceType: serviceTtextValue,
                        version1: ver,
                        suUser: suExecUser,
                        switchFlag: projectFlag,
                        uuid: scriptuuid,
                        sqlExecModel: chooseSqlExecModel.getValue(),
                        serviceId: serviceIdNum,
                        timeout: timeoutValue,
                        labels: labels,
                        scriptDirId: scriptDirId,
                        groupId: groupId,
                        groupName: groupName,
                        execType:excepResultType.getValue(),
                        checkBeforeExec:ycCheckBeforeExec.getValue()?1:0,
                        ignoreSyntaxFlag:ignoreSyntaxFlag,
                        scriptWorkDir:scriptWorkDirValue,
                        BindVarAndFunc: VarandFuncBandData  //json格式向后台传输的函数、变量绑定数据
                    },
                    success: function (response, request) {
                        // Ext.getCmp('saveButton').enable();
                        // Ext.getCmp('saveButton1').enable();
                        var success = Ext
                            .decode(response.responseText).success;
                        var scriptExits = Ext
                            .decode(response.responseText).scriptExits;
                        if (scriptExits) {
                            Ext.Msg.alert('提示', '服务名称重复！');
                            return;
                        }
                        var iid = "";
                        var iscriptuuid;
                        if (newServiceId != 0) {
                            iid = Ext.decode(response.responseText).newId;
                            newiid = iid;
                            iscriptuuid = Ext.decode(response.responseText).scriptuuid;//新生成的
                            cgUuid = Ext.decode(response.responseText).scriptuuid;
                        } else {
                            iid = Ext.decode(response.responseText).iid;
                            iscriptuuid = Ext.decode(response.responseText).iscriptuuid;//原来的
                            cgUuid = Ext.decode(response.responseText).iscriptuuid;
                        }
                        if (success && iid != "" && iid != null) {
                            newServiceId = iid;
                            scriptuuid = iscriptuuid;
                            uuidForUpdateScriptEdit = scriptuuid;
                            attachmentIds = [];
                            paramStore.load();
                            testparamStore.load();
                            paramRulesStore.load();
                            attachmentStore.load();
                            attaTempStore.load();
                            if (callback) {
                                callback();
                            } else {
                                Ext.Msg.alert('提示', '保存成功！');
                                if (gfScriptDirFunctionSwitch) {
                                    scriptBindHis = scriptDirId;
                                    scriptDirTreeStore.load();
                                }
                            }
                            funcDesc.setValue(scriptDesc1);
                        } else {
                            var ccc = "保存";
                            if (callback && callType) {
                                ccc = callType;
                            }
                            paramStore.load();
                            testparamStore.load();
                            paramRulesStore.load();
                            attachmentStore.load();
                            attaTempStore.load();
                            var hasTipKeyWord = Ext
                                .decode(response.responseText).hasTipKeyWord;
                            var hasScreenKeyWord = Ext
                                .decode(response.responseText).hasScreenKeyWord;
                            if (hasScreenKeyWord) {
                                Ext.Msg.alert('提示', "脚本中存在屏蔽命令，无法" + ccc + "！<br>" + Ext.decode(response.responseText).message);
                                return;
                            }
                            if (hasTipKeyWord && ignoreFlag != 1) {
                                try {
                                    Ext.Msg.close();
                                } catch (e) {
                                    console.log(e)
                                }
                                openTipWindow(response, callback, callType);
                                return;
                            }

                            if (scriptShellSyntaxValidateSwitch){
                                var syntaxError = Ext.decode(response.responseText).syntaxError;
                                var syntaxInfo = Ext.decode(response.responseText).syntaxInfo;
                                if (syntaxError && ignoreSyntaxFlag != 1){
                                    Ext.Msg.close();
                                    var descTextArea = null;

                                    descTextArea = Ext.create('Ext.form.field.TextArea', {
                                        fieldLabel: '',
                                        // afterLabelTextTpl: required,
                                        labelWidth: 70,
                                        height: 370,
                                        readOnly:true,
                                        width:'864px',
                                        columnWidth: .99,
                                        autoScroll: true
                                    });
                                    descTextArea.setValue(syntaxInfo);

                                    var syntaxWin = Ext.create('Ext.window.Window', {
                                        title: '语法检查失败',
                                        border:false,
                                        modal: true,
                                        cls:"instruction_box",
                                        width: 900,
                                        height: 500,
                                        draggable: false,
                                        items:[descTextArea],
                                        closable:false,
                                        buttonAlign:'center',
                                        buttons:[
                                            {
                                                xtype:'button',
                                                cls:'Common_Btn',
                                                text:'跳过',
                                                handler:function (){
                                                    syntaxWin.hide();
                                                    save(ignoreFlag, callback, callType, 1);
                                                }
                                            },
                                            {
                                                xtype:'button',
                                                cls:'Common_Btn',
                                                text:'去修改',
                                                handler:function (){
                                                    syntaxWin.hide();
                                                }
                                            }
                                        ]
                                    }).show()
                                    return;
                                }
                                var mess = Ext.decode(response.responseText).message;
                                Ext.Msg.alert('提示', mess);
                            }
                        }
                    },
                    failure: function (result, request) {
                        // Ext.getCmp('saveButton').enable();
                        // Ext.getCmp('saveButton1').enable();
                        secureFilterRs(result, "保存失败！");
                    }
                });
            }
        } else {
            Ext.Ajax.request({
                url: saveUrl,
                method: 'POST',
                sync: true,
                params: {
                    iid: newServiceId,
                    uuid: scriptuuid,
                    sysName: sysName,
                    sysId: sysId,
                    bussTypeId: bussTypeId,
                    bussType: bussType,
                    threeBussTypeId: threeBussTypeId,
                    threeBussTypeName: threeBussTypeName,
                    serverName: serverName,
                    // 系统名称 系统分类
                    scriptName: scriptName1,
                    excepResult: excepResult1,
                    errExcepResult: errExcepResult1,
                    usePlantForm: up,
                    funcDesc: scriptDesc1,
                    checkRadio: type,
                    content: content,
                    params: jsonData,
                    attachmentIds: attachmentIds,
                    templateIds: tempmentIds,
                    ignoreTipCmd: ignoreFlag,
                    suUser: suExecUser,
                    switchFlag: projectFlag,
                    sqlExecModel: chooseSqlExecModel.getValue(),
                    timeout: timeoutValue,
                    labels: labels,
                    scriptDirId: scriptDirId,
                    groupId: groupId,
                    groupName: groupName,
                    execType:excepResultType.getValue(),
                    checkBeforeExec:ycCheckBeforeExec.getValue()?1:0,
                    ignoreSyntaxFlag:ignoreSyntaxFlag,
                    scriptWorkDir:scriptWorkDirValue,
                    BindVarAndFunc: VarandFuncBandData  //json格式向后台传输的函数、变量绑定数据
                },
                success: function (response, request) {
                    // Ext.getCmp('saveButton').enable();
                    // Ext.getCmp('saveButton1').enable();
                    secUUID = Ext.decode(response.responseText).scriptuuid;//新生成的
                    //刷新绑定列表
                    if(getScriptEditeTabShowSwitch){
                        cgUuid = secUUID;
                        // scriptTab.ipage.moveFirst();
                        scriptStore.reload();
                    }
                    // cgUuid = Ext.decode(response.responseText).scriptuuid;
                    // scriptTop.reload();
                    var success = Ext.decode(response.responseText).success;
                    var scriptExits = Ext.decode(response.responseText).scriptExits;
                    if (scriptExits) {
                        baseInfoOfScriptWin.show();
                        Ext.Msg.alert('提示', '服务名称重复！');
                        return;
                    }
                    var exitstype = Ext.decode(response.responseText).exitstype;
                    if (exitstype) {
                        var mess = Ext.decode(response.responseText).message;
                        baseInfoOfScriptWin.show();
                        Ext.Msg.alert('提示', mess);
                        return;
                    }
                    if (newServiceId != 0) {
                        var iid = Ext.decode(response.responseText).newId;
                        var iscriptuuid = Ext.decode(response.responseText).scriptuuid;//新生成的
                        hasVersionForUpdateScriptEdit = 0;
                    } else {
                        var iid = Ext.decode(response.responseText).iid;
                        var iscriptuuid = Ext.decode(response.responseText).iscriptuuid;//原来的
                    }

                    if (success && iid != "" && iid != null) {
                        newServiceId = iid;
                        scriptuuid = iscriptuuid;
                        uuidForUpdateScriptEdit = scriptuuid;
                        attachmentIds = [];
                        paramStore.load();
                        testparamStore.load();
                        attachmentStore.load();
                        attaTempStore.load();
                        /*if (refreshTry) {
			              		clearInterval(refreshTry);
			              	}*/
                        if (callback) {
                            callback();
                        } else {
                            Ext.Msg.alert('提示', '保存成功！');
                            if (gfScriptDirFunctionSwitch) {
                                scriptBindHis = scriptDirId;
                                scriptDirTreeStore.load();
                            }
                        }
                        funcDesc.setValue(scriptDesc1);
                    } else {
                        var ccc = "保存";
                        if (callback && callType) {
                            ccc = callType;
                        }
                        var hasTipKeyWord = Ext.decode(response.responseText).hasTipKeyWord;
                        var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;
                        if (hasScreenKeyWord) {
                            Ext.Msg.alert('提示', "脚本中存在屏蔽命令，无法" + ccc + "！<br>" + Ext.decode(response.responseText).message);
                            return;
                        }
                        if (hasTipKeyWord && ignoreFlag != 1) {
                            try {
                                Ext.Msg.close();
                            } catch (e) {
                                console.log(e)
                            }

                            openTipWindow(response, callback, callType);
                            return;
                        }
                        if (scriptShellSyntaxValidateSwitch) {
                            var syntaxError = Ext.decode(response.responseText).syntaxError;
                            var syntaxInfo = Ext.decode(response.responseText).syntaxInfo;
                            if (syntaxError && ignoreSyntaxFlag != 1) {
                                Ext.Msg.close();
                                var descTextArea = null;

                                descTextArea = Ext.create('Ext.form.field.TextArea', {
                                    fieldLabel: '',
                                    // afterLabelTextTpl: required,
                                    labelWidth: 70,
                                    height: 370,
                                    readOnly: true,
                                    width: '864px',
                                    columnWidth: .99,
                                    autoScroll: true
                                });
                                descTextArea.setValue(syntaxInfo);

                                var syntaxWin = Ext.create('Ext.window.Window', {
                                    title: '语法检查失败',
                                    border: false,
                                    modal: true,
                                    cls: "instruction_box",
                                    width: 900,
                                    height: 500,
                                    draggable: false,
                                    items: [descTextArea],
                                    closable: false,
                                    buttonAlign: 'center',
                                    buttons: [
                                        {
                                            xtype: 'button',
                                            cls: 'Common_Btn',
                                            text: '跳过',
                                            handler: function () {
                                                syntaxWin.hide();
                                                save(ignoreFlag, callback, callType, 1);
                                            }
                                        },
                                        {
                                            xtype: 'button',
                                            cls: 'Common_Btn',
                                            text: '去修改',
                                            handler: function () {
                                                syntaxWin.hide();
                                            }
                                        }
                                    ]
                                }).show()
                                return;
                            }
                            var mess = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', mess);
                        }
                    }
                },
                failure: function (result, request) {
                    // Ext.getCmp('saveButton').enable();
                    // Ext.getCmp('saveButton1').enable();
                    secureFilterRs(result, "保存失败！");
                }
            });
        }
    }

    function loadShelloutputhisInfo(requestId, iip, iport) {
        var surl = "getScriptExecOutputForTry.do";
        Ext.Ajax.request({
            url: surl,
            params: {
                isFromTryATry: isFromTryATry,
                requestId: requestId,
                agentIp: iip,
                agentPort: iport,
                input: cmdVForbasicUpdate
            },
            success: function (response, opts) {
                var msg = Ext.decode(response.responseText).out;
                var status = Ext.decode(response.responseText).status;
                if (!Ext.isEmpty(Ext.util.Format.trim(msg))) {
                    $('#consoleLog-edit').html(msg);
                    consolePanel.body.scroll('bottom', 300000);
                }
                if (status == 2) {
                    if (refreshTryForUpdate) {
                        clearInterval(refreshTryForUpdate);
                    }
                }
            },
            failure: function (response, opts) {
                $('#consoleLog-edit').html('获取执行信息失败');
            }

        });
        cmdVForbasicUpdate = null;
    }

    function loadShelloutputhisInfoCmd(requestId, iip, iport) {
        var surl = "getScriptExecOutputForTry.do";
        Ext.Ajax.request({
            url: surl,
            params: {
                isFromTryATry: 1,
                requestId: requestId,
                agentIp: iip,
                agentPort: iport,
                input: cmdVForbasicUpdate
            },
            success: function (response, opts) {
                var msg = Ext.decode(response.responseText).out;
                var status = Ext.decode(response.responseText).status;
                if (!Ext.isEmpty(Ext.util.Format.trim(msg))) {
                    $('#consoleLogForCmdUpdate').html(msg);
                    consoleCmdPanel.body.scroll('bottom', 300000);
                }
                if (status == 2) {
                    if (refreshTryForUpdate) {
                        clearInterval(refreshTryForUpdate);
                    }
                    execCmdBtn.setDisabled(false);
                }
            },
            failure: function (response, opts) {
                $('#consoleLogForCmdUpdate').html('获取执行信息失败');
            }

        });
        cmdVForbasicUpdate = null;
    }

    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }]
    });

    var auditorStore_sm = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getPublishAuditorList.do?dbaasFlag=' + projectFlag,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var wbChose;
    if (psbcBindAgentSwitch) {
        wbChose = .98;
    } else if (projectFlag == 1) {
        wbChose = .98;
    } else {
        wbChose = .95;
    }

    var auditorComBox_sm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        editable: true,
        fieldLabel: "审核人",
        labelWidth: 93,
//	    padding: 5,
        store: auditorStore_sm,
        labelAlign: 'right',
        queryMode: 'local',
//	    width: 200,
        columnWidth: wbChose,
        margin: '10 0 0 0',
        displayField: 'fullName',
        valueField: 'loginName',
        listeners: { //监听
            render : function(combo) {//渲染
	            combo.getStore().on("load", function(s, r, o) {
	                combo.setValue(r[0].get('loginName'));//第一个值
	            });
	        },
            select: function (combo, records, eOpts) {
                var fullName = records[0].raw.fullName;
                combo.setRawValue(fullName);
            },
            blur: function (combo, records, eOpts) {
                var displayField = auditorComBox_sm.getRawValue();
                if (!Ext.isEmpty(displayField)) {
                    //判断输入是否合法标志，默认false，代表不合法
                    var flag = false;
                    //遍历下拉框绑定的store，获取displayField
                    auditorStore_sm.each(function (record) {
                        //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                        var data_fullName = record.get('fullName');
                        if (data_fullName == displayField) {
                            flag = true;
                            auditorComBox_sm.setValue(record.get('loginName'));
                        }
                    });
                    if (!flag) {
                        Ext.Msg.alert('提示', "输入的审核人非法");
                        auditorComBox_sm.setValue("");
                        return;
                    }
                }

            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var orderNumber_sm = new Ext.form.TextField({
        fieldLabel: '单号',
        emptyText: '--请输入单号--',
        labelWidth: 93,
        labelAlign: 'right',
        hidden: !orderNumberSwitch,
        columnWidth: .98
    });

    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        emptyText: '',
        labelWidth: 93,
        margin: '10 0 0 0',
        labelAlign: 'right',
        height: 70,
        columnWidth: .98,
        autoScroll: true
    });

    if (psbcBindAgentSwitch) {
        pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
            name: 'pubdesc',
            fieldLabel: '发布申请说明',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            height: 200,
            columnWidth: .98,
            autoScroll: true
        });
    }

    var levelStore_sm = Ext.create('Ext.data.Store', {
        fields: ['iid', 'scriptLevel'],
        data: [
            {"iid": "0", "scriptLevel": "白名单"},
            {"iid": "1", "scriptLevel": "高级风险"},
            {"iid": "2", "scriptLevel": "中级风险"},
            {"iid": "3", "scriptLevel": "低级风险"}
        ]
    });

    var scriptLevelCb_sm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 93,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '风险级别',
        margin: '10 0 0 0',
        displayField: 'scriptLevel',
        labelAlign: 'right',
        valueField: 'iid',
        editable: false,
        hidden: !scriptLevelSwitch,
        emptyText: '--请选择风险级别--',
        store: levelStore_sm
    });

    Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: reviewSwitch,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    appSysStore1.on('load', function () {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
    var appSysObj1 = Ext.create('Ext.ux.ideal.form.ComboBox', {
        fieldLabel: '应用系统',
        emptyText: '--请选择应用系统--',
        labelAlign: 'right',
        hidden: !reviewSwitch,
        multiSelect: true,
        labelWidth: 93,
        columnWidth: .95,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function (combo, records, eOpts) {
                if (records) {
                    chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                        chosedAppSys.push(records[i].data.id);
                    }
                }

            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        hidden: !reviewSwitch,
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });
    var forbidden = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否禁用旧版本',
        inputValue: 1,
        width: 120,
        hidden: projectFlag == 1,
        margin: '10 0 0 10'
    });

    if (psbcBindAgentSwicht) {
        forbidden = Ext.create('Ext.form.field.Checkbox', {
            boxLabel: '是否禁用旧版本',
            inputValue: 1,
            margin: '10 0 0 10',
            fieldLabel: '旧版本禁用',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            columnWidth: .98
        });
    }

    var serviceAutoStore_sm = Ext.create('Ext.data.Store', {
        fields: ['iid', 'serviceAuto'],
        data: [
            {"iid": "0", "serviceAuto": "DBA"},
            {"iid": "1", "serviceAuto": "项目组"}
        ]
    });
    var serviceAuto_sm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'serviceAuto',
        labelWidth: 93,
        labelAlign: 'right',
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '服务权限',
        margin: '10 0 0 0',
        displayField: 'serviceAuto',
        valueField: 'iid',
        editable: false,
//        hidden: !reviewSwitch,
        emptyText: '--请选择服务权限--',
        store: serviceAutoStore_sm
    });
    Ext.define('systemModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'systemName',
            type: 'string'
        }, {
            name: 'itype',
            type: 'int'
        }]
    });

    var systemStore = Ext.create('Ext.data.Store', {
        autoLoad: isProject,
        autoDestroy: true,
        model: 'systemModel',
        proxy: {
            type: 'ajax',
            url: 'getSystem.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        },
        listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;

                //根据全局的选择，初始化选中的列
                var selModel = systemgrid.getSelectionModel();
                for (var j = 0; j < selectedSysRecords.length; j++) {
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if (selectedSysRecords[j] == record.get("iid")) {
                            selModel.select(record, true, true);    //选中record，并且保持现有的选择，不触发选中事件
                        }
                    }
                }
            }
        }
    });

    systemStore.on('beforeload', function (store, options) {
        var new_params = {
            sysname: sysname.getValue()
        };

        Ext.apply(systemStore.proxy.extraParams, new_params);
    });

    var systemColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '业务系统名称', dataIndex: 'systemName', flex: 1},
        {text: '工程类型', dataIndex: 'itype', flex: 1, hidden: true}];
    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'cpId',
            type: 'int'
        }, {
            name: 'cpName',
            type: 'string'
        }, {
            name: 'ip',
            type: 'string'
        }, {
            name: 'iagentinfo_id',
            type: 'int'
        }]
    });

    var agentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'businessSystemComputerList.do',
            reader:
                {
                    type: 'json',
                    root: 'dataList'
                }
        },
        listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;

                //根据全局的选择，初始化选中的列
                var selModel = agentgrid.getSelectionModel();
                selectedAgentRecords.forEach(function (item) {
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if (item.agentid == record.get("cpId")) {
                            selModel.select(record, true, true);    //选中record，并且保持现有的选择，不触发选中事件
                        }
                    }
                })
            }
        }
    });

    agentStore.on('beforeload', function (store, options) {
        var new_params = {
            sysIdForQuery: systemId,
            ipBetween: '',
            ipEnd: '',
            opersystype: type == null ? 0 : type,
            cpName: '',
            ipAddr:ipaddr.getValue()
        };

        Ext.apply(agentStore.proxy.extraParams, new_params);
    });

    var selModelsystem = Ext.create('Ext.selection.CheckboxModel', {
        mode: "MULTI",
        listeners: {
            select: function (selModel, record, index, eOpts) {
                sysid = record.get("iid");
                systemId =record.get("iid");
                type=record.get("itype"),
                    agentStore.load(
                    {
                        params: {
                            sysIdForQuery: record.get("iid"),
                            ipBetween: '',
                            ipEnd: '',
                            opersystype: record.get("itype") == null ? 0 : record.get("itype"),
                            cpName: '',
                            ipAddr:ipaddr.getValue()
                        }
                    }
                );
                selectedSysRecords.push(record.get("iid"));
            },
            deselect: function (selModel, record, index, eOpts) {
                systemId=undefined;
                agentStore.removeAll();
                selectedAgentRecords.forEach(function (item) {
                    if (item.sysid == sysid) {
                        selectedAgentRecords.remove(item);
                    }
                })
                selectedSysRecords.remove(record.get('iid'));
            }
        }
    });


    var agentGroupStoreScript = Ext.create('Ext.data.Store', {
        autoLoad: isProject,
        autoDestroy: true,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAgentListWithOutSys.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    agentGroupStoreScript.on('beforeload', function (store, options) {
        var new_params = {
            agentGroup: agentGroup.getValue()
        };

        Ext.apply(agentGroupStoreScript.proxy.extraParams, new_params);
    });

    var agentColumnsScriptssss = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', width: 150},
        {text: 'IP', dataIndex: 'agentIp', width: 150},
        {text: '计算机名', dataIndex: 'hostName', width: 150},
        {text: '操作系统', dataIndex: 'osType', width: 150},
        {text: '端口', dataIndex: 'agentPort', width: 120},
        {text: '描述', dataIndex: 'agentDesc', flex: 1}
        //{ text: '状态',  dataIndex: 'agentip'}
    ];

    var selModelAgentssss = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelAgentsSSSSS',
        checkOnly: true
    });

    var sysname = new Ext.form.TextField({
        name: 'userPermission',
        fieldLabel: '系统名称',
        emptyText: '--请输入系统名称--',
        labelWidth: 65,
        labelAlign: 'right',
        width: 260
    });

    var ipaddr = new Ext.form.TextField({
        name: 'ipaddr',
        fieldLabel: 'ip地址',
        emptyText: '--请输入ip地址--',
        labelWidth: 65,
        labelAlign: 'right',
        width: 260
    });

    var agentGroup = new Ext.form.TextField({
        name: 'agentGroup',
        fieldLabel: 'ip地址',
        emptyText: '--请输入ip地址--',
        labelWidth: 65,
        labelAlign: 'right',
        width: 260
    });

    var agentGroupGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: agentGroupStoreScript,
        selModel: selModelAgentssss,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border',
        columns: agentColumnsScriptssss,
        dockedItems: [
            {
                xtype: 'toolbar',
                cls: 'customize_panel_back',
                items: [agentGroup, {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        agentGroupGrid.ipage.moveFirst();
                    }
                }
                ]
            }]
    });


    agent_panel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1000,
        height: 220,
        layout: 'border',
        items: [agentGroupGrid]
    });


    var systemgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        width: 650,
        title: '业务系统',
        store: systemStore,
        selModel: selModelsystem,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: systemColumns,
        dockedItems: [
            {
                xtype: 'toolbar',
                cls: 'customize_panel_back',
                items: [sysname, {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        systemgrid.ipage.moveFirst();
                    }
                }
                ]
            }]
    });

    var selModelagent = Ext.create('Ext.selection.CheckboxModel', {
        mode: "MULTI",
        listeners: {
            select: function (selModel, record, index, eOpts) {
                var dsid = record.get("cpId");
                var tmpRec = {};
                tmpRec.sysid = sysid;
                tmpRec.agentid = dsid;
                selectedAgentRecords.push(tmpRec);
                selectedSysRecords.remove(sysid);
                console.log("------------");
                console.log("selectedSysRecords:" + selectedSysRecords);
                console.log("selectedAgentRecords:" + selectedAgentRecords);
            },
            deselect: function (selModel, record, index, eOpts) {
                var dsid = record.get("cpId");
                selectedAgentRecords.forEach(function (item) {
                    console.log(item.sysid + '---' + item.agentid);
                    if (item.sysid == sysid && item.agentid == dsid) {
                        selectedAgentRecords.remove(item);
                    }
                })
                console.log("+++++++++++");
                console.log("selectedSysRecords:" + selectedSysRecords);
                console.log("selectedAgentRecords:" + selectedAgentRecords);
            }
        }
    });

    var agentColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: 'cpId', dataIndex: 'cpId', hidden: true},
        {text: '机器名', dataIndex: 'cpName', flex: 1},
        {text: '地址', dataIndex: 'ip', width: 120},
        {text: 'iagentinfo_id', dataIndex: 'iagentinfo_id', width: 120, hidden: true}];

    var agentgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        title: '设备',
        store: agentStore,
        width: 650,
        selModel: selModelagent,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left panel_space_right',
        columns: agentColumns,
        dockedItems : [  {
            xtype : 'toolbar',
            cls:'customize_panel_back',
            items: [ipaddr, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    if(systemId==undefined){
                        Ext.Msg.alert('提示', "请选择业务系统");
                        return
                    }
                    agentgrid.ipage.moveFirst();
                }
            }
            ]
        }]
    });

    project_panel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1300,
        padding: '8 0 0 0',
        height: 260,
        layout: 'border',
        items: [systemgrid, agentgrid]
    });


    //邮储，确认审核信息，业务系统+设备与绑定agent组tab页
    var showTab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            region: 'center',
            activeTab: 0,
            width: '100%',
            height: 360,
            border: false,
            items: [
                {
                    title: '业务系统/设备',
                    layout: 'fit',
                    items: [project_panel]
                },
                {
                    title: 'Agent组',
                    layout: 'fit',
                    items: [agent_panel]
                }
            ]
        });

    var tabpanel = Ext.create('Ext.panel.Panel', {
        height: 360,
        region: 'south',
        cls: 'panel_space_top',
        items: [showTab]
    });


    auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'center',
        layout: 'anchor',
        bodyCls: 'x-docked-noborder-top',
        buttonAlign: 'center',
        border: false,
        items: [{
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [
                {
                    layout: 'column',
                    border: false,
                    items: [planTime_sm, versionAresource]
                },
                {
                    layout: 'column',
                    border: false,
                    items: [cfg_Display]
                },
                {
                    layout: 'column',
                    border: false,
                    items: [planTime_MM, planTime_DD,
                        planTime_HH, planTime_mi]
                },
                {
                    layout: 'column',
                    border: false,
                    items: [tableName]
                }, {
                    layout: 'column',
                    border: false,
                    items: [scriptLevelCb_sm]
                }, {
                    layout: 'column',
                    border: false,
                    items: [appSysObj1, isEMscript]
                },
                {
                    layout: 'column',
                    border: false,
                    items: [auditorComBox_sm, forbidden]
                }, {
                    layout: 'column',
                    border: false,
                    items: [serviceAuto_sm]
                },
                {
                    layout: 'column',
                    border: false,
                    items: [pubDesc_sm]
                }, {
                    layout: 'column',
                    border: false,
                    items: [orderNumber_sm]
                }
            ]
        }]
    });

    /************** 绑定用户组用 start **************/

    if (psbcBindAgentSwitch) {
        auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
            region: 'center',
            layout: 'anchor',
            bodyCls: 'x-docked-noborder-top',
            buttonAlign: 'center',
            border: false,
            items: [{
//	    	layout:'form',
                anchor: '98%',
                padding: '5 0 5 0',
                border: false,
                items: [{
                    layout: 'column',
                    border: false,
                    items: [planTime_sm]
                }, {
                    layout: 'column',
                    border: false,
                    items: [scriptLevelCb_sm],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [appSysObj1],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [isEMscript],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [auditorComBox_sm],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [forbidden],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [pubDesc_sm],
                    width: 400
                }]
            }]
        });
    }

    var agentColumnsScript = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName'},
        {text: 'IP', dataIndex: 'agentIp'},
        {text: '计算机名', dataIndex: 'hostName'},
        {text: '操作系统', dataIndex: 'osType'},
        {text: '端口', dataIndex: 'agentPort'},
        {text: '描述', dataIndex: 'agentDesc'}
        //{ text: '状态',  dataIndex: 'agentip'}
    ];

    Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'}
        ]
    });

    var agentStoreScript = Ext.create('Ext.data.Store', {
        autoLoad: psbcBindAgentSwitch,
        autoDestroy: true,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListAllScript.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    agentStoreScript.on('beforeload', function (store, options) {
        var new_params = {
            startIp: ipStart.getValue().trim(),
            endIp: ipEnd.getValue().trim()
        };

        Ext.apply(agentStoreScript.proxy.extraParams, new_params);
    });

    agentStoreScript.addListener('load', function (me, records, successful, eOpts) {
        if (agentiids.length > 0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
                function (index, record) {
                    if (agentiids.indexOf(record.get('iid')) > -1) {
                        chosedRecords.push(record);
                    }
                });
            agentgridScript.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
    });


    var agentgridScript = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        title: '设备',
        store: agentStoreScript,
        width: 720,
        selModel: selModelAgents,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: agentColumnsScript,
        listeners: {
            select: function (e, record, index, eOpts) {
                if (agentiids.indexOf(record.get('iid')) == -1) {
                    agentiids.push(record.get('iid'));
                }
                if (!selectedAgent.has(record.data)) {
                    selectedAgent.add(record.data);
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (agentiids.indexOf(record.get('iid')) > -1) {
                    agentiids.remove(record.get('iid'));
                }
                if (selectedAgent.has(record.data)) {
                    selectedAgent.delete(record.data);
                }
            }
        }
    });

    var rightArea = Ext.create('Ext.ux.ideal.form.Panel', {
            width: 730,
            height: 350,
            layout: 'border',
            region: 'east',
            autoScroll: true,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            border: true,
            columnLines: true,
            cls: 'window_border panel_space_left',
            items: [search_ip_form, agentgridScript]
        }
    );

    project_panelScript = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1200,
        height: 430,
        layout: 'border',
        items: [auditing_form_sm, rightArea]
    });

    /************** 绑定用户组用 end **************/

    Ext.define('warnningModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'serviceName',
            type: 'string'
        }, {
            name: 'sysName',
            type: 'string'
        }, {
            name: 'bussName',
            type: 'string'
        }, {
            name: 'version',
            type: 'string'
        }, {
            name: 'user',
            type: 'string'
        }]
    });
    var warnningStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'warnningModel',
        proxy: {
            type: 'ajax',
            url: 'scriptCallSearch.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    warnningStore.on('beforeload', function (store, options) {
        var new_params = {
            iid: newServiceId
        };

        Ext.apply(warnningStore.proxy.extraParams, new_params);
    });
    var warnningColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '服务名称', dataIndex: 'serviceName', flex: 1},
        {text: '一级分类', dataIndex: 'sysName', width: 120},
        {text: '二级分类', dataIndex: 'bussName', width: 100},
        {
            text: '版本', dataIndex: 'version', width: 80, renderer: function (value, p, record, rowIndex) {
                if (value) {
                    return value;
                } else {
                    return '无版本号';
                }
            }
        },
        {text: '创建用户', dataIndex: 'user', width: 120}
    ];
    warnningGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: warnningStore,
        border: false,
        columnLines: true,
        columns: warnningColumns
    });
    warnningGrid1 = Ext.create('Ext.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: warnningStore,
        border: false,
        columnLines: true,
        columns: warnningColumns
    });

    function publishScript() {
        versionAresource.hide();
        planTime_sm.hide();
        cfg_Display.hide();
        planTime_MM.hide();
        planTime_DD.hide();
        planTime_HH.hide();
        planTime_mi.hide();
        if (projectFlag != 1) {
            tableName.hide();
            serviceAuto_sm.hide();
        }
        var tName = "";
        var tableFlag = false;
        if (updatecheckRadio != 4) {
            tableName.hide();
        } else {
            if (projectFlag == 1 && chooseSqlExecModel.getValue() == '2') {
                Ext.Ajax.request({
                    url: 'scriptTableSearch.do',
                    method: 'POST',
                    async: false,
                    params: {
                        iid: newServiceId
                    },
                    success: function (response, opts) {
                        var success = Ext.decode(response.responseText).success;
                        tName = Ext.decode(response.responseText).TABLENAME;
                        if (!success) {
                            tableName.show();
                            tableFlag = false;
                        } else {
                            tableFlag = true;
                            tableName.hide();
                        }
                    }
                })
            } else {
                tableName.hide();
            }
        }
        Ext.Ajax.request({
            url: 'scriptHasVersion.do',
            method: 'POST',
            params: {
                serviceId: newServiceId
            },
            success: function (response, opts) {
                var hasVersion = Ext.decode(response.responseText).hasVersion;
                if (hasVersion == 1) {
                    Ext.Msg.alert('提示', "该脚本已经发布过！");
                    return;
                } else {
                    Ext.Ajax.request({
                        url: 'scriptStatus.do',
                        method: 'POST',
                        params: {
                            serviceId: newServiceId
                        },
                        success: function (response, opts) {
                            var status = Ext.decode(response.responseText).status;
                            if (status == 2) {
                                Ext.Msg.alert('提示', "该脚本正处于审核中！");
                                return;
                            } else {
                                //先查这个脚本是否被作业调用
                                Ext.Ajax.request({
                                    url: 'scriptCallSearch.do',
                                    method: 'POST',
                                    params: {
                                        iid: newServiceId
                                    },
                                    success: function (response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {//有需要提示的内容
                                            if (!warnningWin) {
                                                warnningWin = Ext.create('widget.window', {
                                                    title: '提示信息,发布该脚本将影响以下作业，是否继续发布？',
                                                    closable: true,
                                                    closeAction: 'hide',
                                                    modal: true,
                                                    width: 600,
                                                    minWidth: 350,
                                                    height: 300,
                                                    layout: {
                                                        type: 'border',
                                                        padding: 5
                                                    },
                                                    items: [warnningGrid],
                                                    dockedItems: [{
                                                        xtype: 'toolbar',
                                                        border: false,
                                                        dock: 'bottom',
                                                        layout: {pack: 'center'},
                                                        items: [{
                                                            xtype: "button",
                                                            cls: 'Common_Btn',
                                                            text: "是",
                                                            handler: function () {
                                                                this.up("window").close();
                                                                if (scriptCrossPublishPassSwitch) {
                                                                    submitAutiding()
                                                                } else {
                                                                    var tempHt;
                                                                    if (projectFlag == 1) {
                                                                        tempHt = 450;
                                                                    } else {
                                                                        tempHt = reviewSwitch ? 380 : 360;
                                                                    }
                                                                    if (scriptCrossPublishPassSwitch) {
                                                                        submitAutiding()
                                                                    } else {
                                                                        if (!publishAuditingSMWin) {
                                                                            //绑定用户组
                                                                            if (psbcBindAgentSwitch) {
                                                                                publishAuditingSMWin = Ext.create('widget.window', {
                                                                                    title: '确认审核信息',
                                                                                    closable: true,
                                                                                    closeAction: 'hide',
                                                                                    modal: true,
                                                                                    width: 1200,
                                                                                    height: 570,
                                                                                    layout: {
                                                                                        type: 'border',
                                                                                        padding: 5
                                                                                    },
                                                                                    items: [project_panelScript],
                                                                                    dockedItems: [{
                                                                                        xtype: 'toolbar',
                                                                                        //baseCls:'customize_gray_back',
                                                                                        dock: 'bottom',
                                                                                        layout: {pack: 'center'},
                                                                                        items: [{
                                                                                            xtype: "button",
                                                                                            cls: 'Common_Btn',
                                                                                            text: "确定",
                                                                                            handler: submitAutiding
                                                                                        }, {
                                                                                            xtype: "button",
                                                                                            cls: 'Common_Btn',
                                                                                            text: "取消",
                                                                                            handler: function () {
                                                                                                selectedSysRecords = [];
                                                                                                selectedAgentRecords = [];
                                                                                                systemStore.load();
                                                                                                agentStore.load(
                                                                                                    {
                                                                                                        params: {
                                                                                                            sysIdForQuery: 0,
                                                                                                            ipBetween: '',
                                                                                                            ipEnd: '',
                                                                                                            opersystype: 0,
                                                                                                            cpName: ''
                                                                                                        }
                                                                                                    }
                                                                                                );
                                                                                                agentiids = [];
                                                                                                selectedAgent.clear();
                                                                                                agentStoreScript.load();
                                                                                                agentgridScript.getSelectionModel().select([], false, false); //取消选中
                                                                                                this.up("window").close();
                                                                                            }
                                                                                        }]
                                                                                    }]
                                                                                });
                                                                            } else if (isProject) {
                                                                                publishAuditingSMWin = Ext.create('widget.window', {
                                                                                    title: '确认审核信息',
                                                                                    closable: true,
                                                                                    closeAction: 'hide',
                                                                                    modal: true,
                                                                                    width: 1200,
                                                                                    height: 700,
                                                                                    layout: {
                                                                                        type: 'border',
                                                                                        padding: 5
                                                                                    },
                                                                                    items: [auditing_form_sm, tabpanel/*project_panel*/],
                                                                                    dockedItems: [{
                                                                                        xtype: 'toolbar',
                                                                                        //baseCls:'customize_gray_back',
                                                                                        dock: 'bottom',
                                                                                        layout: {pack: 'center'},
                                                                                        items: [{
                                                                                            xtype: "button",
                                                                                            cls: 'Common_Btn',
                                                                                            text: "确定",
                                                                                            handler: submitAutiding
                                                                                        }, {
                                                                                            xtype: "button",
                                                                                            cls: 'Common_Btn',
                                                                                            text: "取消",
                                                                                            handler: function () {
                                                                                                selectedSysRecords = [];
                                                                                                selectedAgentRecords = [];
                                                                                                systemStore.load();
                                                                                                agentStore.load(
                                                                                                    {
                                                                                                        params: {
                                                                                                            sysIdForQuery: 0,
                                                                                                            ipBetween: '',
                                                                                                            ipEnd: '',
                                                                                                            opersystype: 0,
                                                                                                            cpName: ''
                                                                                                        }
                                                                                                    }
                                                                                                );
                                                                                                this.up("window").close();
                                                                                            }
                                                                                        }]
                                                                                    }]
                                                                                });
                                                                            } else {
                                                                                publishAuditingSMWin = Ext.create('widget.window', {
                                                                                    title: '确认审核信息',
                                                                                    closable: true,
                                                                                    closeAction: 'hide',
                                                                                    resizable: false,
                                                                                    modal: true,
                                                                                    width: 600,
                                                                                    minWidth: 350,
                                                                                    height: tempHt,
                                                                                    layout: {
                                                                                        type: 'border',
                                                                                        padding: 5
                                                                                    },
                                                                                    items: [auditing_form_sm],
                                                                                    dockedItems: [{
                                                                                        xtype: 'toolbar',
                                                                                        dock: 'bottom',
                                                                                        layout: {pack: 'center'},
                                                                                        items: [{
                                                                                            xtype: "button",
                                                                                            cls: 'Common_Btn',
                                                                                            text: "确定",
                                                                                            handler: function () {
                                                                                                var planTime = planTime_sm.getRawValue();
                                                                                                var scriptLevel = scriptLevelCb_sm.getValue();
                                                                                                if (!scriptLevelSwitch) {
                                                                                                    scriptLevel = 100;
                                                                                                }
                                                                                                var publishDesc = pubDesc_sm.getValue();
                                                                                                var publishDescr = publishDesc;
                                                                                                // publishDescr = publishDescr.replaceAll(" ", "");
                                                                                                publishDescr = publishDescr.replace(/ /g, "");
                                                                                                if (publishDescr.indexOf('</\script\>') > -1 || publishDescr.indexOf('</\SCRIPT\>=') > -1) {
                                                                                                    Ext.Msg.alert('提示', "发布申请说明禁止输入&lt;/script&gt;");
                                                                                                    return;
                                                                                                }
                                                                                                var auditor = auditorComBox_sm.getValue();
                                                                                                var isEmScript = isEMscript.getValue();
                                                                                                if (isEmScript) {
                                                                                                    isEmScript = 1;
                                                                                                } else {
                                                                                                    isEmScript = 0;
                                                                                                }
                                                                                                var isForbidden = forbidden.getValue();
                                                                                                if (isForbidden) {
                                                                                                    isForbidden = 1;
                                                                                                } else {
                                                                                                    isForbidden = 0;
                                                                                                }
//		    				    		        			  				if(!planTime) {
//		    				    		        			  					Ext.Msg.alert('提示', "没有填写计划时间！");
//		    				    		        			  					return;
//		    				    		        			  				}
                                                                                                var planTime_MM_1 = planTime_MM.getValue();
                                                                                                var planTime_DD_1 = planTime_DD.getValue();
                                                                                                var planTime_HH_1 = planTime_HH.getValue();
                                                                                                var planTime_mi_1 = planTime_mi.getValue();
                                                                                                var tablename = tableName.getValue();
                                                                                                if (tableFlag) {
                                                                                                    tablename = tName;
                                                                                                }
                                                                                                var serviceAuto = serviceAuto_sm.getValue();
                                                                                                var f1 = 0;

                                                                                                if (updatecheckRadio == 4 && chooseSqlExecModel.getValue() == '2') {
                                                                                                    var stype = serviceType.getValue();
                                                                                                    var mm = paramRulesStore.getRange();
                                                                                                    if (stype == '1') {
                                                                                                        if (mm.length == 0 && tablename != '') {
                                                                                                            Ext.Msg.alert('提示', "表中列定义，请填写表表中列信息！");
                                                                                                            return;
                                                                                                        } else if (mm.length == 0 && tablename == '') {
                                                                                                            f1 = 1;
                                                                                                        } else if (mm.length > 0 && tablename == '') {
                                                                                                            Ext.Msg.alert('提示', "表中列定义完成，请填写表名称！");
                                                                                                            return;
                                                                                                        } else if (mm.length > 0 && tablename.length > 30) {
                                                                                                            Ext.Msg.alert('提示', "表名称不能超过30个字符！");
                                                                                                            return;
                                                                                                        }
                                                                                                    }

                                                                                                    if (!serviceAuto) {
                                                                                                        Ext.Msg.alert('提示', "没有选择服务权限！");
                                                                                                        return;
                                                                                                    }
                                                                                                }

                                                                                                if (!scriptLevel) {
                                                                                                    Ext.Msg.alert('提示', "没有选择风险级别！");
                                                                                                    return;
                                                                                                }
                                                                                                if (!publishDesc) {
                                                                                                    Ext.Msg.alert('提示', "没有填写详细说明！");
                                                                                                    return;
                                                                                                }
                                                                                                if (!auditor) {
                                                                                                    Ext.Msg.alert('提示', "没有选择审核人！");
                                                                                                    return;
                                                                                                }
                                                                                                var orderNumber = orderNumber_sm.getValue().trim();
                                                                                                if(orderNumberSwitch) {
                                                                                                    // 发布功能校验单号不为空 特定银行需求
                                                                                                    console.debug('单号:',orderNumber, ',isUndefined:',(undefined == orderNumber), ',islength=0:', orderNumber.length==0)
                                                                                                    if (orderNumber.length==0) {
                                                                                                        Ext.Msg.alert('提示', "单号不能为空");
                                                                                                        return;
                                                                                                    }
                                                                                                }
                                                                                                if (f1 == 1) {
                                                                                                    Ext.MessageBox.buttonText.yes = "确定";
                                                                                                    Ext.MessageBox.buttonText.no = "取消";
                                                                                                    Ext.Msg.confirm("确认发布", "没有定义表名及表中列，确认不定义采集结果表？",
                                                                                                        function (id) {
                                                                                                            if (id == 'no') {
                                                                                                                return;
                                                                                                            }
                                                                                                        });
                                                                                                }
//																				if(planTime_MM_1=='间隔x日'){
//																					if (planTime_DD_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
//																						Ext.Msg.alert('提示',"天数必须填写！");
//																						return;
//																					}
//																				}else if(planTime_MM_1=='间隔x小时'){
//																					if (planTime_HH_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
//																						Ext.Msg.alert('提示',"小时必须填写！");
//																						return;
//																					}
//																				}else if(planTime_MM_1=='间隔x分钟'){
//																					if (planTime_mi_1==''||planTime_mi_1==null||planTime_mi_1=='null') {
//																						Ext.Msg.alert('提示',"分钟必须填写！");
//																						return;
//																					}
//																				}else{
//																					Ext.Msg.alert('提示',"请选择周期类型！");
//																					return;
//																				}

                                                                                                var sIds = new Array();
                                                                                                sIds.push(newServiceId);
                                                                                                Ext.Ajax.request({
                                                                                                    url: 'scriptPublishAuditing.do',
                                                                                                    method: 'POST',
                                                                                                    params: {
                                                                                                        sIds: sIds,
                                                                                                        planTime: planTime,
                                                                                                        scriptLevel: scriptLevel,
                                                                                                        publishDesc: publishDesc,
                                                                                                        auditor: auditor,
                                                                                                        flag: 0, //0-来着个人脚本库
                                                                                                        isEmScript: isEmScript,
                                                                                                        appSysIds: chosedAppSys,
                                                                                                        planTime_Type: planTime_MM_1,
                                                                                                        planTime_DD: planTime_DD_1,
                                                                                                        planTime_HH: planTime_HH_1,
                                                                                                        planTime_mm: planTime_mi_1,
                                                                                                        serviceType: serviceType.getValue(),
                                                                                                        isForbidden: isForbidden,
                                                                                                        tablename: tablename,
                                                                                                        switchFlag: projectFlag,
                                                                                                        radio: updatecheckRadio,
                                                                                                        serviceAuto: serviceAuto,
                                                                                                        tableFlag: tableFlag,
                                                                                                        orderNumber: orderNumber
                                                                                                    },
                                                                                                    success: function (response, opts) {
                                                                                                        var success = Ext.decode(response.responseText).success;
                                                                                                        var message = Ext.decode(response.responseText).message;
                                                                                                        if (!success) {
                                                                                                            Ext.MessageBox.alert("提示", message);
                                                                                                        } else {
                                                                                                            Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                                                                                        }
                                                                                                        publishAuditingSMWin.close();

                                                                                                    },
                                                                                                    failure: function (result, request) {
                                                                                                        secureFilterRs(result, "操作失败！");
                                                                                                        publishAuditingSMWin.close();
                                                                                                    }
                                                                                                });

                                                                                            }
                                                                                        }, {
                                                                                            xtype: "button",
                                                                                            cls: 'Common_Btn',
                                                                                            text: "取消",
                                                                                            handler: function () {
                                                                                                this.up("window").close();
                                                                                            }
                                                                                        }]
                                                                                    }]
                                                                                });
                                                                            }

                                                                        }
                                                                        publishAuditingSMWin.show();
                                                                        auditorStore_sm.load();
                                                                        planTime_sm.setValue('');
                                                                        scriptLevelCb_sm.setValue('');
                                                                        pubDesc_sm.setValue('');
                                                                        auditorComBox_sm.setValue('');
                                                                        isEMscript.setValue(0);
                                                                        forbidden.setValue(0);
                                                                        appSysObj1.setValue('');
                                                                        chosedAppSys = '';
                                                                    }
                                                                }
                                                            }
                                                        }, {
                                                            xtype: "button",
                                                            cls: 'Common_Btn',
                                                            text: "否",
                                                            handler: function () {
                                                                this.up("window").close();
                                                            }
                                                        }]
                                                    }]
                                                });
                                            }
                                            warnningWin.show();
                                            warnningStore.load();
                                        } else {
                                            if (scriptCrossPublishPassSwitch) {
                                                submitAutiding()
                                            } else {
                                                if (!publishAuditingSMWin) {
                                                    //绑定用户组
                                                    if (psbcBindAgentSwitch) {
                                                        publishAuditingSMWin = Ext.create('widget.window', {
                                                            title: '确认审核信息',
                                                            closable: true,
                                                            closeAction: 'hide',
                                                            modal: true,
                                                            width: 1200,
                                                            height: 570,
                                                            layout: {
                                                                type: 'border',
                                                                padding: 5
                                                            },
                                                            items: [project_panelScript],
                                                            dockedItems: [{
                                                                xtype: 'toolbar',
                                                                //baseCls:'customize_gray_back',
                                                                dock: 'bottom',
                                                                layout: {pack: 'center'},
                                                                items: [{
                                                                    xtype: "button",
                                                                    cls: 'Common_Btn',
                                                                    text: "确定",
                                                                    handler: submitAutiding
                                                                }, {
                                                                    xtype: "button",
                                                                    cls: 'Common_Btn',
                                                                    text: "取消",
                                                                    handler: function () {
                                                                        selectedSysRecords = [];
                                                                        selectedAgentRecords = [];
                                                                        systemStore.load();
                                                                        agentStore.load(
                                                                            {
                                                                                params: {
                                                                                    sysIdForQuery: 0,
                                                                                    ipBetween: '',
                                                                                    ipEnd: '',
                                                                                    opersystype: 0,
                                                                                    cpName: ''
                                                                                }
                                                                            }
                                                                        );
                                                                        this.up("window").close();
                                                                    }
                                                                }]
                                                            }]
                                                        });
                                                    } else if (isProject) {
                                                        publishAuditingSMWin = Ext.create('widget.window', {
                                                            title: '确认审核信息',
                                                            closable: true,
                                                            closeAction: 'hide',
                                                            modal: true,
                                                            width: 1500,
                                                            height: 700,
                                                            layout: {
                                                                type: 'border',
                                                                padding: 5
                                                            },
                                                            items: [auditing_form_sm, tabpanel/*project_panel*/],
                                                            dockedItems: [{
                                                                xtype: 'toolbar',
                                                                //baseCls:'customize_gray_back',
                                                                dock: 'bottom',
                                                                layout: {pack: 'center'},
                                                                items: [{
                                                                    xtype: "button",
                                                                    cls: 'Common_Btn',
                                                                    text: "确定",
                                                                    handler: submitAutiding
                                                                }, {
                                                                    xtype: "button",
                                                                    cls: 'Common_Btn',
                                                                    text: "取消",
                                                                    handler: function () {
                                                                        selectedSysRecords = [];
                                                                        selectedAgentRecords = [];
                                                                        systemStore.load();
                                                                        agentStore.load(
                                                                            {
                                                                                params: {
                                                                                    sysIdForQuery: 0,
                                                                                    ipBetween: '',
                                                                                    ipEnd: '',
                                                                                    opersystype: 0,
                                                                                    cpName: ''
                                                                                }
                                                                            }
                                                                        );
                                                                        this.up("window").close();
                                                                    }
                                                                }]
                                                            }]
                                                        });
                                                    } else {
                                                        var tempHt;
                                                        if (projectFlag == 1) {
                                                            tempHt = 450;
                                                        } else {
                                                            tempHt = reviewSwitch ? 380 : 360;
                                                        }
                                                        publishAuditingSMWin = Ext.create('widget.window', {
                                                            title: '确认审核信息',
                                                            closable: true,
                                                            closeAction: 'hide',
                                                            resizable: false,
                                                            modal: true,
                                                            width: 600,
                                                            minWidth: 350,
                                                            height: tempHt,
                                                            layout: {
                                                                type: 'border',
                                                                padding: 5
                                                            },
                                                            items: [auditing_form_sm],
                                                            dockedItems: [{
                                                                xtype: 'toolbar',
                                                                dock: 'bottom',
                                                                layout: {pack: 'center'},
                                                                items: [{
                                                                    xtype: "button",
                                                                    cls: 'Common_Btn',
                                                                    text: "确定",
                                                                    handler: function () {
                                                                        var planTime = planTime_sm.getRawValue();
                                                                        var scriptLevel = scriptLevelCb_sm.getValue();
                                                                        if (!scriptLevelSwitch) {
                                                                            scriptLevel = 100;
                                                                        }
                                                                        var publishDesc = pubDesc_sm.getValue();
                                                                        var publishDescr = publishDesc;
                                                                        //publishDescr = publishDescr.replaceAll(" ", "");
                                                                        publishDescr = publishDescr.replace(/ /g, "");
                                                                        if (publishDescr.indexOf('</\script\>') > -1 || publishDescr.indexOf('</\SCRIPT\>=') > -1) {
                                                                            Ext.Msg.alert('提示', "发布申请说明禁止输入&lt;/script&gt;");
                                                                            return;
                                                                        }
                                                                        var auditor = auditorComBox_sm.getValue();
                                                                        var isEmScript = isEMscript.getValue();
                                                                        if (isEmScript) {
                                                                            isEmScript = 1;
                                                                        } else {
                                                                            isEmScript = 0;
                                                                        }
                                                                        var isForbidden = forbidden.getValue();
                                                                        if (isForbidden) {
                                                                            isForbidden = 1;
                                                                        } else {
                                                                            isForbidden = 0;
                                                                        }
                                                                        var planTime_MM_1 = planTime_MM.getValue();
                                                                        var planTime_DD_1 = planTime_DD.getValue();
                                                                        var planTime_HH_1 = planTime_HH.getValue();
                                                                        var planTime_mi_1 = planTime_mi.getValue();
                                                                        var tablename = tableName.getValue();
                                                                        if (tableFlag) {
                                                                            tablename = tName;
                                                                        }
                                                                        var serviceAuto = serviceAuto_sm.getValue();
                                                                        if (updatecheckRadio == '4' && chooseSqlExecModel.getValue() == '2') {
                                                                            var f1 = 0;
                                                                            var stype = serviceType.getValue();
                                                                            var mm = paramRulesStore.getRange();
                                                                            if (stype == '1') {
                                                                                if (mm.length == 0 && tablename != '') {
                                                                                    Ext.Msg.alert('提示', "表中未定义列，请填写表表中列信息！");
                                                                                    return;
                                                                                } else if (mm.length == 0 && tablename == '') {
                                                                                    //							    				  								f1=1;
                                                                                    Ext.Msg.alert('提示', "请填写表名称！");
                                                                                    return;
                                                                                } else if (mm.length > 0 && tablename == '') {
                                                                                    Ext.Msg.alert('提示', "表中列定义完成，请填写表名称！");
                                                                                    return;
                                                                                } else if (mm.length > 0 && tablename != '') {
                                                                                    var reg = new RegExp("^[a-zA-Z]");
                                                                                    if (!reg.test(tablename)) {
                                                                                        Ext.Msg.alert('提示', "表名称首字符必须是字母，请重新输入");
                                                                                        return;
                                                                                    }
                                                                                }

                                                                                if (mm.length > 0 && tablename.length > 30) {
                                                                                    Ext.Msg.alert('提示', "表名称不能超过30个字符！");
                                                                                    return;
                                                                                }
                                                                            }
                                                                            if (f1 == 1) {
                                                                                Ext.Msg.confirm("确认发布", "没有定义表名及表中列，确认不定义采集结果表？",
                                                                                    function (id) {
                                                                                        if (id == 'no') {
                                                                                            return;
                                                                                        } else {

                                                                                        }
                                                                                    });
                                                                            }
                                                                        }

                                                                        if (projectFlag == 1) {
                                                                            if (!serviceAuto) {
                                                                                Ext.Msg.alert('提示', "没有选择服务权限！");
                                                                                return;
                                                                            }

                                                                        }
                                                                        if (!scriptLevel) {
                                                                            Ext.Msg.alert('提示', "没有选择风险级别！");
                                                                            return;
                                                                        }
                                                                        if (!publishDesc) {
                                                                            Ext.Msg.alert('提示', "没有填写发布申请说明！");
                                                                            return;
                                                                        }
                                                                        if (publishDesc.length > 255) {
                                                                            Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
                                                                            return;
                                                                        }
                                                                        if (!auditor) {
                                                                            Ext.Msg.alert('提示', "没有选择审核人！");
                                                                            return;
                                                                        }
                                                                        var orderNumber = orderNumber_sm.getValue().trim();
                                                                        if(orderNumberSwitch) {
                                                                            // 发布功能校验单号不为空 特定银行需求
                                                                            console.debug('单号:',orderNumber, ',isUndefined:',(undefined == orderNumber), ',islength=0:', orderNumber.length==0)
                                                                            if (orderNumber.length==0) {
                                                                                Ext.Msg.alert('提示', "单号不能为空");
                                                                                return;
                                                                            }
                                                                        }
                                                                        var sIds = new Array();
                                                                        sIds.push(newServiceId);
                                                                        Ext.Ajax.request({
                                                                            url: 'scriptPublishAuditing.do',
                                                                            method: 'POST',
                                                                            params: {
                                                                                sIds: sIds,
                                                                                planTime: planTime,
                                                                                scriptLevel: scriptLevel,
                                                                                publishDesc: publishDesc,
                                                                                auditor: auditor,
                                                                                flag: 0, //0-来着个人脚本库
                                                                                isEmScript: isEmScript,
                                                                                appSysIds: chosedAppSys,
                                                                                planTime_Type: planTime_MM_1,
                                                                                planTime_DD: planTime_DD_1,
                                                                                planTime_HH: planTime_HH_1,
                                                                                planTime_mm: planTime_mi_1,
                                                                                serviceType: serviceType.getValue(),
                                                                                isForbidden: isForbidden,
                                                                                tablename: tablename,
                                                                                switchFlag: projectFlag,
                                                                                radio: updatecheckRadio,
                                                                                serviceAuto: serviceAuto,
                                                                                tableFlag: tableFlag,
                                                                                orderNumber: orderNumber
                                                                            },
                                                                            success: function (response, opts) {
                                                                                var success = Ext.decode(response.responseText).success;
                                                                                var message = Ext.decode(response.responseText).message;
                                                                                if (!success) {
                                                                                    Ext.MessageBox.alert("提示", message);
                                                                                } else {
                                                                                    Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                                                                }
                                                                                publishAuditingSMWin.close();

                                                                            },
                                                                            failure: function (result, request) {
                                                                                secureFilterRs(result, "操作失败！");
                                                                                publishAuditingSMWin.close();
                                                                            }
                                                                        });

                                                                    }
                                                                }, {
                                                                    xtype: "button",
                                                                    cls: 'Common_Btn',
                                                                    text: "取消",
                                                                    handler: function () {
                                                                        this.up("window").close();
                                                                    }
                                                                }]
                                                            }]
                                                        });

                                                    }
                                                }
                                                systemgrid.getSelectionModel().select([], false, false); //选中记录
                                                agentGroupGrid.getSelectionModel().select([], false, false); //选中记录
                                                sysname.setValue('');
                                                ipaddr.setValue('');
                                                agentGroup.setValue('');
                                                systemStore.load();
                                                publishAuditingSMWin.show();
                                                auditorStore_sm.load();
                                                planTime_sm.setValue('');
                                                scriptLevelCb_sm.setValue('');
                                                pubDesc_sm.setValue('');
                                                auditorComBox_sm.setValue('');
                                                isEMscript.setValue(0);
                                                forbidden.setValue(0);
                                                appSysObj1.setValue('');
                                                chosedAppSys = '';
                                            }
                                        }
                                        },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "操作失败！");
                                    }
                                });

                            }
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                            return;
                        }
                    });
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
                return;
            }
        });
    }

    function submitAutiding() {
        if (scriptCrossPublishPassSwitch) {
            var orderNumber = orderNumber_sm.getValue().trim();
            if(orderNumberSwitch) {
                // 发布功能校验单号不为空 特定银行需求
                console.debug('单号:',orderNumber, ',isUndefined:',(undefined == orderNumber), ',islength=0:', orderNumber.length==0)
                if (orderNumber.length==0) {
                    Ext.Msg.alert('提示', "单号不能为空");
                    return;
                }
            }

            var sIds = new Array();
            sIds.push(newServiceId);

            var planTime = "";
            var scriptLevel = "0";
            var publishDesc = "";
            var auditor = "";
            var isEmScript = isEMscript.getValue();

            if (isEmScript) {
                isEmScript = 1;
            } else {
                isEmScript = 0;
            }
            var isForbidden = 0;

            //定义发布审核url
            var auditUrl = 'scriptPublishAuditing.do';

            //获取agent组数据
            var agentIdArray = [];
            var agentRecords = agentGroupGrid.getSelectionModel().getSelection();
            Ext.each(agentRecords, function (item) {
                agentIdArray.push(item.data.iid);//agentIId
            });
            var iidstr = agentIdArray.join(",");
            //获取uuid
            //新生成uuid不为空使用新生成的uuid关联，否则使用原有uuid
            var transUUID = "";
            if (secUUID != null && secUUID != '') {
                transUUID = secUUID;
            } else {
                transUUID = oldUUID;
            }

            //获取uuid
            var uuidVal = transUUID;
            var scriptId = newServiceId;
            //定义发布参数
            var auditParams = {
                sIds: sIds,
                planTime: planTime,
                scriptLevel: scriptLevel,
                publishDesc: publishDesc,
                auditor: auditor,
                flag: 0, //0-来着个人脚本库
                isEmScript: isEmScript,
                appSysIds: chosedAppSys,
                isForbidden: isForbidden,
                tablename: "",
                switchFlag: 0,
                radio: updatecheckRadio,
                serviceAuto: 1,
                attachmentIds: attachmentIds,
                systemIds: selectedSysRecords,
                cpIds: JSON.stringify(selectedAgentRecords),
                agentIIds: iidstr,
                uuidVal: transUUID,
                orderNumber: orderNumber
            };
            Ext.Ajax.request({
                url: auditUrl,
                method: 'POST',
                params: auditParams,
                success: function (response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (!success) {
                        Ext.MessageBox.alert("提示", message);
                    } else {
                        var workItemid = Ext.decode(response.responseText).workitemId;
                        // 发布成功后，调用发布成功url
                        Ext.Ajax.request(
                            {
                                url: 'scriptPublishForOneRecord.do',
                                method: 'POST',
                                params:
                                    {
                                        iid: scriptId,
                                        scriptUuid: uuidVal,
                                        flag: 0,
                                        iworkItemid: workItemid,
                                        scriptLevel: scriptLevel,
                                        isEmScript: isEmScript,
                                        appSysIds: 0,
                                        forbidden: 0
                                    },
                                success: function (response, opts) {
                                    Ext.MessageBox.alert("提示", Ext.decode(response.responseText).message);
                                    if (Ext.decode(response.responseText).success) {
                                        selectedSysRecords = [];
                                        selectedAgentRecords = [];
                                    }
                                }
                            }
                        );
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        } else {
            //判断输入的审核人是否合法 start
            var displayField = auditorComBox_sm.getRawValue();
            if (!Ext.isEmpty(displayField)) {
                //判断输入是否合法标志，默认false，代表不合法
                var flag = false;
                //遍历下拉框绑定的store，获取displayField
                auditorStore_sm.each(function (record) {
                    //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                    var data_fullName = record.get('fullName');
                    if (data_fullName == displayField) {
                        flag = true;
                        auditorComBox_sm.setValue(record.get('loginName'));
                    }
                });
                if (!flag) {
                    Ext.Msg.alert('提示', "输入的审核人非法");
                    auditorComBox_sm.setValue("");
                    return;
                }
            }
            //判断输入的审核人是否合法  end
            var planTime = planTime_sm.getRawValue();
            var scriptLevel = scriptLevelCb_sm.getValue();
            if (!scriptLevelSwitch) {
                scriptLevel = 100;
            }
            var publishDesc = pubDesc_sm.getValue();
            var publishDescr=publishDesc;
            //publishDescr = publishDescr.replaceAll(" ", "");
            publishDescr = publishDescr.replace(/ /g, "");
            if(publishDescr.indexOf('</\script\>')>-1||publishDescr.indexOf('</\SCRIPT\>=')>-1){
                Ext.Msg.alert('提示', "发布申请说明禁止输入&lt;/script&gt;");
                return;
            }
            var auditor = auditorComBox_sm.getValue();
            var isEmScript = isEMscript.getValue();

            if (isEmScript) {
                isEmScript = 1;
            } else {
                isEmScript = 0;
            }
            var isForbidden = forbidden.getValue();
            if (isForbidden) {
                isForbidden = 1;
            } else {
                isForbidden = 0;
            }

            if (!scriptLevel) {
                Ext.Msg.alert('提示', "没有选择风险级别！");
                return;
            }
            if (!publishDesc) {
                Ext.Msg.alert('提示', "没有填写发布申请说明！");
                return;
            }
            if (publishDesc.length > 255) {
                Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
                return;
            }
            if (!auditor) {
                Ext.Msg.alert('提示', "没有选择审核人！");
                return;
            }
            var orderNumber = orderNumber_sm.getValue().trim();
            if(orderNumberSwitch) {
                // 发布功能校验单号不为空 特定银行需求
                console.debug('单号:',orderNumber, ',isUndefined:',(undefined == orderNumber), ',islength=0:', orderNumber.length==0)
                if (orderNumber.length==0) {
                    Ext.Msg.alert('提示', "单号不能为空");
                    return;
                }
            }
            if (!psbcBindAgentSwitch) {
                if (isProject) {
                    var records = systemgrid.getSelectionModel().getSelection();
                    if (records.length <= 0) {
                        Ext.Msg.alert('提示', "请选择业务系统！");
                        return;
                    }
                }
            }

            var sIds = new Array();
            sIds.push(newServiceId);
            //定义发布审核url
            var auditUrl = 'scriptPublishAuditing.do';
            //获取agent组数据
            var agentIdArray = [];
            var agentRecords = agentGroupGrid.getSelectionModel().getSelection();
            Ext.each(agentRecords, function (item) {
                agentIdArray.push(item.data.iid);//agentIId
            });
            var iidstr = agentIdArray.join(",");
            //获取uuid
            //新生成uuid不为空使用新生成的uuid关联，否则使用原有uuid
            var transUUID = "";
            if (secUUID != null && secUUID != '') {
                transUUID = secUUID;
            } else {
                transUUID = oldUUID;
            }
            //定义发布参数
            var auditParams = {
                sIds: sIds,
                planTime: planTime,
                scriptLevel: scriptLevel,
                publishDesc: publishDesc,
                auditor: auditor,
                flag: 0, //0-来着个人脚本库
                isEmScript: isEmScript,
                appSysIds: chosedAppSys,
                isForbidden: isForbidden,
                tablename: "",
                switchFlag: 0,
                radio: updatecheckRadio,
                serviceAuto: 1,
                attachmentIds: attachmentIds,
                systemIds: selectedSysRecords,
                cpIds: JSON.stringify(selectedAgentRecords),
                agentIIds: iidstr,
                uuidVal: transUUID,
                orderNumber: orderNumber
            };

            if (psbcBindAgentSwitch) {
                auditUrl = 'auditSuccessForScript.do';
                //获取选中的设备
                var IIdArray = [];
                var records = agentgridScript.getSelectionModel().getSelection();
                //设备不能为空
                if (records == null || records == '') {
                    Ext.Msg.alert('提示', "请选择设备！");
                    return;
                }
                //获取uuid
                // var uuidselect = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                // var uuidVal = "";
                // Ext.each(uuidselect, function(item) {// 遍历
                //     uuidVal = item.data.uuid;
                // });
                Ext.each(records, function (item) {// 遍历
                    IIdArray.push(item.data.iid);
                });
                var iidsStr = IIdArray.join(",");
                if (agentiids.length > 0) {
                    iidsStr = agentiids.toString();
                }
                /*//新生成uuid不为空使用新生成的uuid关联，否则使用原有uuid
                var transUUID = "";
                if(secUUID != null && secUUID != ''){
                    transUUID = secUUID;
                }else {
                    transUUID = oldUUID;
                }*/
                auditParams = {
                    sIds: sIds,
                    planTime: planTime,
                    scriptLevel: scriptLevel,
                    publishDesc: publishDesc,
                    auditor: auditor,
                    flag: 0, //0-来着个人脚本库
                    isEmScript: isEmScript,
                    appSysIds: chosedAppSys,
                    isForbidden: isForbidden,
                    tablename: "",
                    switchFlag: 0,
                    radio: updatecheckRadio,
                    serviceAuto: 1,
                    attachmentIds: attachmentIds,
                    systemIds: selectedSysRecords,
                    cpIds: JSON.stringify(selectedAgentRecords),
                    iidsStr: iidsStr,
                    uuid: transUUID,
                    orderNumber: orderNumber
                };
            }

            Ext.Ajax.request({
                url: auditUrl,
                method: 'POST',
                params: auditParams,
                success: function (response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (!success) {
                        Ext.MessageBox.alert("提示", message);
                    } else {
                        Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                        publishAuditingSMWin.close();
                        selectedSysRecords = [];
                        selectedAgentRecords = [];
                        systemStore.load();
                        agentStore.load(
                            {
                                params: {
                                    sysIdForQuery: 0,
                                    ipBetween: '',
                                    ipEnd: '',
                                    opersystype: 0,
                                    cpName: ''
                                }
                            }
                        );
                    }


                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                    publishAuditingSMWin.close();
                }
            });
            agentiids = [];
            selectedAgent.clear();
            agentStoreScript.load();
            agentgridScript.getSelectionModel().select([], false, false); //取消选中
        }
    }

    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function (store, options) {
        var queryparams = {
            iid: newServiceId
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function (store, options, success) {
        var reader = store.getProxy().getReader();
        //						Ext.getCmp("scriptpara").setValue(reader.jsonData.scriptPara);
        scName.setValue(reader.jsonData.scriptName);
//        $('#s-n-t-e').html(reader.jsonData.scriptName);
        // attachmentGrid.setTitle('脚本工程('+ reader.jsonData.scriptName +')');
        scriptName = reader.jsonData.scriptName;
        oldserviceName = reader.jsonData.serviceName;
        sName.setValue(reader.jsonData.serviceName);
        scName.setValue(reader.jsonData.scriptName);
        excepResult.setValue(reader.jsonData.excepResult);
        excepResultType.setValue(reader.jsonData.excepResultType);
        timeout.setValue(reader.jsonData.timeout == -1 ? '' : reader.jsonData.timeout);
        errExcepResult.setValue(reader.jsonData.errExcepResult);
        usePlantForm.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);
        funcDescInWin.setValue(reader.jsonData.funcDesc);
        suUser.setValue(reader.jsonData.suUser);
        chooseSqlExecModel.setValue(reader.jsonData.isAgent);
        if (projectFlag == 1) {
            dbtest.setValue(reader.jsonData.dbType);
            serviceType.setValue(reader.jsonData.serviceType);
            audittest.setValue(reader.jsonData.iisExam);
            FieldContainer.items.items[4].setValue(true);
            updatecheckRadio = 4;
            editor.setOption("mode", 'text/x-plsql');
        }
        if (reader.jsonData.scriptName) {
            maimPanels.setTitle(reader.jsonData.scriptName);
        } else {
            maimPanels.setTitle("编辑框");
        }
        var scriptT = reader.jsonData.scriptType;
        if (scriptT == 'sh') {
            FieldContainer.items.items[0].setValue(true);
            language='1';
            if (scriptEditBookSwitch){
                functionTab.ipage.moveFirst();
            }
            updatecheckRadio = 0;
            editor.setOption("mode", 'shell');
            scriptTemplateScriptTypeValue = 'shell';
        } else if (scriptT == 'bat') {
            FieldContainer.items.items[1].setValue(true);
            language='all';
            if (scriptEditBookSwitch){
                functionTab.ipage.moveFirst();
            }
            updatecheckRadio = 1;
            editor.setOption("mode", 'bat');
            scriptTemplateScriptTypeValue = 'bat';
        } else if (scriptT == 'py') {
            FieldContainer.items.items[3].setValue(true);
            language='2';
            if (scriptEditBookSwitch){
                functionTab.ipage.moveFirst();
            }
            updatecheckRadio = 3;
            editor.setOption("mode", 'python');
            scriptTemplateScriptTypeValue = 'python';
        } else if (scriptT == 'sql') {
            FieldContainer.items.items[4].setValue(true);
            language='all';
            if (scriptEditBookSwitch){
                functionTab.ipage.moveFirst();
            }
            updatecheckRadio = 4;
            editor.setOption("mode", 'text/x-plsql');
            scriptTemplateScriptTypeValue = 'sql';
        } else if (scriptT == 'perl') {
            FieldContainer.items.items[2].setValue(true);
            language='all';
            if (scriptEditBookSwitch){
                functionTab.ipage.moveFirst();
            }
            updatecheckRadio = 2;
            editor.setOption("mode", 'text/x-perl');
            scriptTemplateScriptTypeValue = 'perl';
        } else if (scriptT == 'ps1') {
            FieldContainer.items.items[5].setValue(true);
            language='3';
            if (scriptEditBookSwitch){
                functionTab.ipage.moveFirst();
            }
            updatecheckRadio = 6;
            editor.setOption("mode", 'powershell');
            scriptTemplateScriptTypeValue = 'powershell';
        }
        if (scriptTemplateSwitch) {
            scriptTemplateComboboxStore.reload();
        }
        if (updatecheckRadio == 4 && projectFlag == 1) {
            chooseSqlExecModel.show();
            outruleGrid.show();
        } else {
            chooseSqlExecModel.hide();
            outruleGrid.hide();
        }
        editor.setOption('value', reader.jsonData.content);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        groupName = parseInt(reader.jsonData.groupName);
        threeBsTypeId = parseInt(reader.jsonData.threeTypeId);
        ycCheckBeforeExec.setValue(reader.jsonData.checkBeforeExec);
        scriptWorkDir.setValue(reader.jsonData.scriptWorkDir);
        //判断功能分类开关是否开启
        //逻辑：开关开启并且功能分类为空、开关关闭   以上两种情况均走之前的逻辑（直接加载一级、二级分类）
        //逻辑：开关开启并且功能分类不为空，加载功能分类、一级分类、二级分类
        if (sdFunctionSortSwitch && groupName != 'undefined' && groupName != null && groupName != '') {
            groupNameStore.load();
        } else{
            bussData.load()
        }
        if (projectFlag == 1 && updatecheckRadio == 4) {
            chooseSqlExecModel.show();

            outruleGrid.show();
            attachmentGrid.hide();
        } else {
            chooseSqlExecModel.hide();
            outruleGrid.hide();
            attachmentGrid.show();
        }
        vv = reader.suUser;
        execUserForTry.setValue(vv);
    });

    function openTerminal(webshell_endpoint, server_type, options) {
        var tabTitle = options.host + '-'
        if (server_type == "1") {
            tabTitle += "SSH";
        } else if (server_type == "2") {
            tabTitle += "TELNET";
        }
        var tabCount = 0; // 页签组中当前功能tab页的数量
        var existedTab = null;
        tapPanelForConsole.items.each(function (item) {
            if (item.title == tabTitle) {
                tabCount = 1;
                existedTab = item;
            }
        });
        var mdfive = hex_md5(tabTitle);
        if (tabCount > 0) {
            var termold = $('#term-update-' + mdfive).data('term');
            var clientold = $('#term-update-' + mdfive).data('client');
            if (termold) {
                termold.destroy();
            }
            if (clientold) {
                clientold.close();
            }
        } else {
            existedTab = tapPanelForConsole.add({
                title: tabTitle,
                bodyStyle: 'background:#000;',
                border: false,
                height: consoleOneCmdPanel.getHeight() - 85,
                activeItem: 0,
                autoScroll: true,
                html: '<div id="term-update-' + mdfive + '"></div>',
                closable: true // 允许关闭
            });
        }
        tapPanelForConsole.setActiveTab(existedTab);

        var client = new WSSHClient(webshell_endpoint, server_type);
        var term = new Terminal({
            cols: 80,
            rows: parseInt((consoleOneCmdPanel.getHeight() - 85) / 16),
            screenKeys: true,
            useStyle: true
        });
        term.on('data', function (data) {
            client.sendClientData(data);
        });
        term.open();

        $(term.element).detach().appendTo('#term-update-' + mdfive);
        $('#term-update-' + mdfive).data('term', term);
        $('#term-update-' + mdfive).data('client', client);
        term.write('Connecting...');
        client.connect({
            onError: function (error) {
                term.write('Error: ' + error + '\r\n');
                console.debug('error happened');
            },
            onConnect: function () {
                client.sendInitData(options);
                client.sendClientData('\r');
                console.debug('connection established');
            },
            onClose: function () {
                term.write("\rconnection closed")
                console.debug('connection reset by peer');
            },
            onData: function (data) {
                term.write(data);
                console.debug('get data:' + data);
            }
        });
    }

    function StringToPassword(strs) {
        if (strs && strs != null & strs != '') {
            var password = '';
            for (var i = 0; i < strs.length; i++) {
                password = password + '●';
            }
            return password;
        } else {
            return '';
        }
    }

    function connect(server_type, options) {
        if (webshell_endpoint == "") {
            Ext.MessageBox.alert("提示", "Web Shell服务没有配置，无法使用仿真终端功能！");
            return;
        }
        openTerminal(webshell_endpoint, server_type, options)

    }

    function addParam() {
        var store = paramGrid.getStore();
        var ro = store.getCount();
        var p = {
            iid: '',
            paramOrder: ro + 1,
            paramType: 'IN-string',
            parameterName: '',
            paramDefaultValue: '',
            paramDesc: ''
        };
        store.insert(0, p);
        paramGrid.getView().refresh();
    }

    //变量清单窗口
    function envFlowConButton() {
        var moniUrl = "page/sus/CICD_FlowTask/CICD_VariableList.jsp";
        envVariableWin = Ext.create('Ext.window.Window',
            {
                title: '变量清单',
                autoScroll: true,
                modal: true,
                closeAction: 'destroy',
                buttonAlign: 'center',
                draggable: true,// 禁止拖动
                resizable: false,// 禁止缩放
                width: contentPanel.getWidth() * 0.6,
                height: contentPanel.getHeight() * 0.9,
                loader:
                    {
                        url: moniUrl,
                        params: {
                            taskid: 0
                        },
                        autoLoad: true,
                        autoDestroy: true,
                        scripts: true
                    }
            }).show();
        envVariableWin.on('resize', function(a) {

        });
    }


    function addOutPara() {
        var store = outruleGrid.getStore();
        var ro = store.getCount();
        var p = {
            iid: '',
            paramRuleOrder: ro + 1,
            paramRuleIn: '',
            paramRuleOut: '',
            paramRuleType: 0,
            paramRuleLen: 50,
            paramRuleDesc: ''
        };
        store.insert(0, p);
        outruleGrid.getView().refresh();
    }

    function checkFunction() {
        editor.save();
        var scriptContent = document.getElementById('code-edit').value;
        var scripttype = "sh";
        if (updatecheckRadio == '0') {
            scripttype = "sh";
        } else if (updatecheckRadio == '1') {
            scripttype = "bat";
        } else if (updatecheckRadio == '2') {
            scripttype = "perl";
        } else if (updatecheckRadio == '3') {
            scripttype = "py";
        } else if (updatecheckRadio == '4') {
            scripttype = "sql";
        } else if (updatecheckRadio == '6') {
            scripttype = "ps1";
        }

        Ext.Ajax.request({
            url: 'checkScriptForRules.do',
            method: 'POST',
            params: {
                scriptContent: scriptContent,
                scripttype: scripttype
            },
            success: function (response, request) {
                var success1 = Ext.decode(response.responseText).success;
                if (success1) {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                } else {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                }
            }
        });
    }

    function chosedExecUser(record) {
        $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"), execUserConfigForm.getForm().findField('execUserName').getRawValue());
        if (isSumpAgentSwitch == true) {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getRawValue());
        } else {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getValue());
        }
        record.commit();
        execUserConfigWindow.hide();
    }

    function checkIsNotEmptyAndUndefined(str) {
        if (trim(str) == "" && trim(str) == "undefined")
            return false;
        else
            return true;
    }

});

function versionAndResource() {
    Ext.create('Ext.window.Window', {
        title: '版本与资源组配置',
        modal: true,
        closeAction: 'destroy',
        constrain: true,
        autoScroll: true,
        width: 500,
        height: 450,
        draggable: false,// 禁止拖动
        resizable: false,// 禁止缩放
        layout: 'fit',
        loader: {
            url: 'goVersionAndResource.do',
            params: {
                serviceId: iidForUpdateScriptEdit
            },
            autoLoad: true,
            scripts: true
        }
    }).show();
}

function exportTestData(operId) {
    window.location.href = 'exportSqlOperResultDetailExcel.do?operId=' + operId;
}

function addTags(las) {
    var labKey = las.getKeys();
    // console.log(9999999999999);
    // console.log(labKey);
    // console.log(7777777777777);
    // console.log(labKey.join(','));
    // var labs=las;
    // var labKey="[";
    // labKey=labKey+labs+"]";

    $("#tagsinputval").tagsinput('add', labKey.join(','));
    console.log(222222)
}
function checkLength(strTemp)
{
    var i,sum;
    sum=0;
    for(i=0;i<strTemp.length;i++)
    {
        if ((strTemp.charCodeAt(i)>=0) && (strTemp.charCodeAt(i)<=255))
            sum=sum+1;
        else
            sum=sum+2;
    }
    return sum;
}