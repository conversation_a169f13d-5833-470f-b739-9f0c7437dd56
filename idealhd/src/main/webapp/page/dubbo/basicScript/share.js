Ext.onReady(function () {
// 清理主面板的各种监听时间
    destroyRubbish();

    var bussData = Ext.create('Ext.data.Store', {
        fields: [{name: 'iid', type: 'string'}, {name: 'bsName', type: 'string'}],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "sh", "name": "shell"},
            {"id": "bat", "name": "bat"},
            {"id": "py", "name": "python"},
            {"id": "perl", "name": "perl"},
            {"id": "sql", "name": "sql"},
            {"id": "ps1", "name": "powershell"}
        ]
    });
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: sdFunctionSortSwitch,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var sgroupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                sbussCb.clearValue();
                sbussCb.applyEmptyText();
                sbussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var sbussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        width: '20%',
        queryMode: 'local',
        fieldLabel: '一级分类',
//		padding : '0 5 0 5',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        labelAlign: 'right',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                sbussTypeCb.clearValue();
                sbussTypeCb.applyEmptyText();
                sbussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    /** 二级分类* */
    var sbussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
//		padding : '0 5 0 5',
        labelWidth: 65,
        width: '20%',
        queryMode: 'local',
        labelAlign: 'right',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value !== null) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    //北京邮储 三级分类
    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    /*threeBussTypeData.on('load', function(store, options) {
        if(threeBsTypeId) {
            threeBussTypeCb.setValue(threeBsTypeId);
        }
    });*/
    var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threeBussTypeCb',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        width: '20%',
        labelAlign: 'right',
        hidden: !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    /** 脚本类型* */
    var sscriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'sscriptTypeParam',
//		padding : '0,5,0,5',
        labelWidth: 65,
        width: '15%',
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        labelAlign: 'right',
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    usePlantFormStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: "0"
        };
        Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
    });
    var splatFromCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'splatFromCombobox',
        labelWidth: 65,
        // columnWidth : .2,
        labelAlign: 'right',
        width: '15%',
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: true,
        emptyText: '--请选择适用平台--',
        store: usePlantFormStore

    });

    var ssName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '--请输入服务名称--',
        labelWidth: 65,
        labelAlign: 'right',
//		padding : '0 5 0 5',
        width: '25%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var slabel = new Ext.form.TextField({
        name: 'slabel',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        hidden: !labelSwitch,
        //value : labelSwitch,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '16%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var skeywords = new Ext.form.TextField({
        name: 'skeywords',
        fieldLabel: '关键字',
        emptyText: '-请输入关键字-',
        value: filter_keywords_share,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var sscriptName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '--请输入脚本名称--',
        labelAlign: 'right',
        labelWidth: 65,
//		padding : '0 5 0 5',
        width: '21.3%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var createUser = new Ext.form.TextField({
        name: 'createUser',
        fieldLabel: '创建人',
        emptyText: '--请输入创建人--',
        labelWidth: 65,
        labelAlign: 'right',
        width: '20%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var ssearch_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        baseCls: 'customize_gray_back',
        iqueryFun: function () {
            pageBar.moveFirst();
        },
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [ssName, sscriptTypeParam, sgroupNameCombo, sbussCb, sbussTypeCb, threeBussTypeCb]
        }, {
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [slabel, sscriptName, splatFromCombobox, createUser, skeywords, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    pageBar.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    clearQueryWhere();
                }
            }]
        }]
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'uuid', type: 'string'},
            {name: 'serviceName', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'groupName', type: 'string'},
            {name: 'bussName', type: 'string'},
            {name: 'buss', type: 'string'},
            {name: 'bussType', type: 'string'},
            {name: 'threeTypeName', type: 'string'},
            {name: 'bussId', type: 'int'},
            {name: 'bussTypeId', type: 'int'},
            {name: 'scriptType', type: 'string'},
            {name: 'label', type: 'string'},
            {name: 'isflow', type: 'string'},
            {name: 'scriptName', type: 'string'},
            {name: 'servicePara', type: 'string'},
            {name: 'serviceState', type: 'string'},
            {name: 'platForm', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'content', type: 'string'},
            {name: 'isCollected', type: 'int'},
            {name: 'userID', type: 'int'},
            {name: 'isshare', type: 'string'},
            {name: 'status', type: 'int'},
            {name: 'isEmScript', type: 'string'},
            {name: 'appSystem', type: 'string'},
            {name: 'useTimes', type: 'int'},
            {name: 'winTimes', type: 'string'},
            {name: 'createUser', type: 'string'},
            {name: 'keywords', type: 'string'}
        ]
    });

    var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForShare.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    scriptServiceReleaseStore.on('beforeload', function (store, options) {
        var new_params = {
            bussId: ssearch_form.getForm().findField("sysName").getValue(),
            bussTypeId: ssearch_form.getForm().findField("bussType").getValue(),
            threeTypeId: ssearch_form.getForm().findField("threeBussTypeCb").getValue(),
            scriptName: ssearch_form.getForm().findField("scriptName").getValue().trim(),
            serviceName: ssearch_form.getForm().findField("serverName").getValue().trim(),
            scriptType: ssearch_form.getForm().findField("sscriptTypeParam").getValue(),
            isFlow: 0,
            platForm: splatFromCombobox.getValue(),
            createUserName: createUser.getValue(),
            keywords: skeywords.getValue(),
            label: slabel.getValue(),
            groupName: sgroupNameCombo.getValue()
//            		skeywords:ssearch_form.getForm().findField("skeywords").getValue().trim()
        };

        Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });

    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '服务主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '服务主键uuid',
            dataIndex: 'uuid',
            width: 40,
            hidden: true
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            width: 200, flex: 1
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            width: 260, flex: 1
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            width: 200, flex: 1,
            hidden: !sdFunctionSortSwitch
        },
        {
            text: '一级分类',
            dataIndex: 'buss',
            width: 200, flex: 1
        },
        {
            text: '二级分类',
            dataIndex: 'bussType',
            width: 250, flex: 1
        },
        {
            text: '三级分类',
            dataIndex: 'threeTypeName',
            hidden: !scriptThreeBstypeSwitch,
            width: 250, flex: 1
        },
        {
            text: '脚本类型',
            dataIndex: 'scriptType',
            width: 80, flex: 1,
            renderer: function (value, p, record, rowIndex) {
                var isflow = record.get('isflow');
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                } else if (value == "ps1") {
                    backValue = "powershell";
                }
                if (isflow == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        },
        {
            text: '标签',
            dataIndex: 'label',
            hidden: !labelSwitch,
            width: 250,
            flex: 1,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '适用平台',
            dataIndex: 'platForm',
            width: 100
        }, {
            text: '使用次数',
            dataIndex: 'useTimes',
            hidden: shareuseTimesswitch,
            //hidden: true,
            width: 80
        },
        {
            text: '成功率',
            dataIndex: 'winTimes',
            hidden: shareuseTimesswitch,
            //hidden: true,
            width: 80
        }, {
            text: '创建人',
            dataIndex: 'createUser',
            width: 80
        }, {
            text: '是否应急',
            dataIndex: 'isEmScript',
            width: 75,
            hidden: !reviewSwitch,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '否';
                } else if (value == 1) {
                    return '<font color="#F01024">是</font>';
                } else {
                    return '未知';
                }
            }
        },
        {
            text: '所属系统',
            dataIndex: 'appSystem',
            hidden: !reviewSwitch,
            width: 50,
            flex: 1
        },
        {
            text: '脚本状态',
            dataIndex: 'status',
            width: 100,
            renderer: function (value, p, record, rowIndex) {
                if (value == -1) {
                    return '<font color="#F01024">草稿</font>';
                } else if (value == 1) {
                    return '<font color="#0CBF47">已上线</font>';
                } else if (value == 2) {
                    return '<font color="#FFA602">审核中</font>';
                } else if (value == 3) {
                    return '<font color="#13B1F5">已共享</font>';
                } else if (value == 9) {
                    return '<font color="">已共享未发布</font>';
                } else {
                    return '<font color="#CCCCCC">未知</font>';
                }
            }
        },
        {
            text: '共享状态',
            dataIndex: 'isshare',
            hidden: true,
            width: 100,
            renderer: function (value, p, record, rowIndex) {
                return '<font color="#0CBF47">已共享</font>';
            }
        },
        {
            text: '版本',
            dataIndex: 'version',
            width: 200,
            hidden: true
        },
        {
            text: '收藏',
            xtype: 'actioncolumn',
            width: 50,
            hidden: true,
            items: [{
                getClass: function (v, meta, rec) {
                    if (rec.get('isCollected') == 0) { // 没有收藏
                        return 'uncollection-col';
                    } else { // 已经收藏
                        return 'collection-col';
                    }
                },
                getTip: function (v, meta, rec) {
                    if (rec.get('isCollected') == 0) {
                        return '收藏';
                    } else {
                        return '取消收藏';
                    }
                },
                handler: function (grid, rowIndex, colIndex) {
                    var rec = grid.getStore().getAt(rowIndex);

                    Ext.Ajax.request({
                        url: 'collectScriptService.do',
                        method: 'POST',
                        params: {
                            scriptuuid: rec.get('uuid')
                        },
                        success: function (response, request) {
                            scriptServiceReleaseStore.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            }]
        },
//		{ 
//			text: '操作',  
//			dataIndex: 'stepOperation',
////			xtype : 'actiontextcolumn',
//			width:150,
//			renderer:function(value,p,record,rowIndex){
//				var iid =  record.get('iid'); // 其实是requestID
//				var uuid =  record.get('uuid');
//				var isflow = record.get('isflow');
//				var serviceName = record.get('serviceName');
//				var bussId = record.get('bussId');
//				var bussTypeId  =record.get('bussTypeId');
//				var viewFuncName = "viewScript";
//				
//				var userID = record.get('userID');
//				if(isflow=='1') {
//					viewFuncName = "viewScriptFlow";
//				}
//				if(userID==userId_share){
//					return '<span class="switch_span">'+
//				       '<a href="javascript:void(0)" onclick="'+viewFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+')">'+
//				   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;查看'+
//				   	   '</a>'+
//				   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
//				}else{
//					return '<span class="switch_span">'+
//				       '<a href="javascript:void(0)" onclick="'+viewFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+')">'+
//				   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;查看'+
//				   	   '</a>'+
//				   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'+
//				   '<span class="switch_span">'+
//			       '<a href="javascript:void(0)" onclick="copyScript('+iid+',\''+uuid+'\')">'+
//			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_copy"></img>&nbsp;复制'+
//			   	   '</a>'+
//			   '</span>';
//				}
//				
//			}
//		}
        {
            text: '操作',
            xtype: 'actiontextcolumn',
            dataIndex: 'stepOperation',
            width: 150,
            items: [{
                text: '查看',
                iconCls: 'monitor_search',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    viewScript(iid, label);
                }
            }, {
                text: '复制',
                iconCls: 'script_copy',
                getClass: function (v, metadata, record) {
                    if (record.data.userID == userId_share) {
                        return 'x-hidden';
                    }
                },
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var uuid = grid.getStore().data.items[rowIndex].data.uuid;
                    copyScript(iid, uuid);
                }
            }]
        }
    ];
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptServiceReleaseStore,
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var scriptServiceReleaseGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        width: contentPanel.getWidth(),
        store: scriptServiceReleaseStore,
        cls: 'customize_panel_back',
        selModel: selModel,
        padding: grid_space,
        plugins: [cellEditing],
//	    bbar : pageBar,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border: false,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        animCollapse: false
    });
    let renderTo = "scriptService_share";
    if(isTabSwitch){
        renderTo += snow;
    }
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: renderTo,
        border: false,
        layout: 'border',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        items: [ssearch_form, scriptServiceReleaseGrid]
    });


    /* 解决IE下trim问题 */
    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

    function clearQueryWhere() {
//    	ssearch_form.getForm().findField("serviceName").setValue('');
//    	ssearch_form.getForm().findField("status").setValue('1');
        ssearch_form.getForm().findField("sysName").setValue('');
        ssearch_form.getForm().findField("bussType").setValue('');
        ssearch_form.getForm().findField("scriptName").setValue('');
        ssearch_form.getForm().findField("serverName").setValue('');
        ssearch_form.getForm().findField("sscriptTypeParam").setValue('');
        ssearch_form.getForm().findField("splatFromCombobox").setValue('');
        ssearch_form.getForm().findField("createUser").setValue('');
        ssearch_form.getForm().findField("skeywords").setValue('');
        bussTypeData.removeAll();
        slabel.setValue('');
        sgroupNameCombo.setValue('');
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
    }

    //从一个json对象中，解析出key=iid的value,返回改val
//	function parsIIDJson(key ,jsonObj){
//		 var eValue=eval('jsonObj.'+key);  
//		 return jsonObj[''+key+''];
//	}
    function viewScript(iid, label) {

        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForView.do',
                params: {
                    iid: iid,
                    flag: 1,
                    hideReturnBtn: 1,
                    label: label
                },
                autoLoad: true,
                scripts: true
            }
        });

        DetailWinTi.show();
    }

    function copyScript(iid, uuid) {
        popNewTab('脚本编写', 'basicScriptEdit.do', {iid: iid, uuid: uuid}, 10, true);

    }
});


//function viewScriptFlow(iid,serviceName,bussId,bussTypeId){
////	alert(iid);
//	destroyRubbish(); //销毁本页垃圾
//	contentPanel.getLoader().load({url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
//		params: {
//			iid:iid,
//			serviceName:serviceName,
//			actionType:'view',
//			bussId:bussId,
//			bussTypeId:bussTypeId,
//			flag:3
//		},
//		scripts: true});
//}


//function putToolBox(iid,serviceName,bussId,bussTypeId){
//	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
//		if (btn == 'yes') {
//			Ext.Ajax.request({
//				url : 'putScriptToToolBox.do',
//				method : 'POST',
//				params : {
//					iid:iid, 
//					table:'IEAI_SCRIPT_SHARE'
//				},
//				success : function(response, request) {
//					var success = Ext.decode(response.responseText).success;
//					var message = Ext.decode(response.responseText).message;
//					if (success) {
//						Ext.Msg.alert('提示', message);
//					} else {
//						Ext.Msg.alert('提示', message);
//					}
//					scriptmonitor_store.reload();
//				},
//				failure : function(result, request) {
//					secureFilterRs(result,"操作失败！");
//				}
//			});
//		}
//	});
//}
