<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ConfigReader"%>
<%
	ConfigReader cr = ConfigReader.getInstance();
	boolean shareuseTimesswitch = cr.getBooleanProperties(Environment.SS_SERVICEMANAGE_USETIMESWITCH , false);
	boolean sharewinTimesswitch = cr.getBooleanProperties(Environment.SS_SERVICEMANAGE_WINTIMESSWITCH, false);
	//标签
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
%>
<html>
<head>
	<script type="text/javascript">
		var snow="";
		if(isTabSwitch){
			$(document).ready(function(){
				$("#scriptService_share").attr('id',$("#scriptService_share").attr('id')+snow)
			});
		}
	</script>
<script type="text/javascript">
var userId_share = <%=request.getAttribute("userId")%>;
var filter_keywords_share  = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
var shareuseTimesswitch = <%=shareuseTimesswitch%>;
var sharewinTimesswitch = <%=sharewinTimesswitch%>;
var labelSwitch = <%=sdScriptLabelEditSwitch%>;
var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/share.js"></script>
</head>
<body>
<div id="scriptService_share" style="width: 100%;height: 100%">
</body>
</html>