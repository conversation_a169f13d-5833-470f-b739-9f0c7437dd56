<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<html>
<head>

<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
	var filter_serverNameQuery ='<%=request.getParameter("filter_serverNameQuery")==null?"":request.getParameter("filter_serverNameQuery")%>';
	var filter_scriptStateQuery ='<%=request.getParameter("filter_scriptStateQuery")==null?"":request.getParameter("filter_scriptStateQuery")%>';
<%
};
%>
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/scriptFlowManage.js"></script>
</head>
<body>
<div id="scriptService_flow_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>