Ext.onReady(function () {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    var tempmentIds = [];
    var mainPanel;
    var sysID;
    var busID;
    var threeBsTypeId;
    var editor;
    var chooseTestAgentWin;
    var attachmentIds = [];
    var scriptName = "";
    var countdown = 10;
    var tryRequestId = '';
    var tryAgentIp = '';
    var tryAgentPort = '';
    var refreshTry;
    var newServiceId = 0;
    var newUuid = "";
    var isFromTryATry = 0;
    var editScriptStore;
    var checkRadioForAllScripts = 0;
    var golbalParamName;
    //标签框
    var label;
    //编辑
    var la = new Ext.util.HashMap();
    //存放标签
    var labels = '';
    var groupName;
    /*=====================左边版本信息====================*/
    Ext.define('versionModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'uuid', type: 'string'},
            {name: 'createTime', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'onlyVersion', type: 'string'},
            {name: 'status', type: 'long'}
        ]
    });

    var versionStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'versionModel',
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceVersionListForAllScript.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    versionStore.on('beforeload', function (store, options) {
        var new_params = {
            serviceId: serviceIdForAllScripts,
            flag: flagForAllScripts
        };
        Ext.apply(versionStore.proxy.extraParams, new_params);
    });

    versionStore.on('load', function (store, options) {
        versionGrid.getSelectionModel().select(0, true);
    });

    Ext.define('attaTempModelAllScritpsView', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    var versionColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '服务主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        }, {
            text: '服务主键uuid',
            dataIndex: 'uuid',
            width: 40,
            hidden: true
        }, {
            text: '是否禁用',
            dataIndex: 'status',
            width: 80,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 2) {
                    backValue = "是";
                } else {
                    backValue = "否";
                }
                return backValue;
            }
        },
        {
            text: '版本',
            dataIndex: 'onlyVersion',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '创建时间',
            dataIndex: 'createTime',
            width: 100,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }
    ];
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var versionGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        width: 300,
        height: contentPanel.getHeight() - 146,
        store: versionStore,
        cls: 'customize_panel_back panel_space_right',
        // padding : grid_space,
        border: true,
        // margin:'5',
        columnLines: true,
        columns: versionColumns,
        selModel: selModel,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        region: 'west',
        listeners: {
            select: function (me, record, index, eOpts) {
                newServiceId = record.get('iid');
                newUuid = record.get('uuid');
                editScriptStore.load();
            }
        },
        dockedItems: [
            {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: [
                    '->',
                    {
                        text: '脚本内容比对',
                        cls: 'Common_Btn',
                        handler: compareScript
                    }]
            }]
    });

    /*===================功能说明基本信息=============================================*/
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussData.on('load', function (store, options) {
        if (!Ext.isEmpty(sysID)) {
            bussCb.setValue(sysID);
            bussCb1.setValue(sysID);
            bussTypeData.load({
                params: {
                    fk: sysID
                }
            });
        }
    });

    bussTypeData.on('load', function (store, options) {
        bussTypeCb.setValue(busID);
        bussTypeCb1.setValue(busID);
    });
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    groupNameStore.on('load', function (store, options) {
        if (groupName) {
            groupNameCombo.setValue(groupName);
            bussData.load({
                params: {
                    fk: groupName
                }
            });
        }
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        readOnly: true
    });
    var tmpname = "一级分类";
    var tmptext = "--请选择一级分类--";
    if (projectFlagForAll == 1) {
        tmpname = "脚本分类";
        tmptext = "--请选择脚本分类--";
    }
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .5,
        fieldLabel: tmpname,
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        queryMode: 'local',
        emptyText: tmptext,
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });
    var bussCb1 = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .9,
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        queryMode: 'local',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb1.clearValue();
                bussTypeCb1.applyEmptyText();
                bussTypeCb1.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });
    var tmpname1 = "二级分类";
    var tmptext1 = "--请选择二级分类--";
    if (projectFlagForAll == 1) {
        tmpname1 = "操作类型";
        tmptext1 = "--请选择操作类型--";
    }
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: tmpname1,
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: tmptext1,
        store: bussTypeData,
        listeners: {
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value !== null) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            }
        }
    });

    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussTypeData.on('load', function (store, options) {
        if (threeBsTypeId) {
            threeBussTypeCb.setValue(threeBsTypeId);
        }
    });
    var threeBussTypeCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        hidden: !scriptThreeBstypeSwitch,
        valueField: 'threeBsTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选三级分类--',
        store: threeBussTypeData
    });
    var bussTypeCb1 = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .9,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });
    var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    Ext.define('paramRuleModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'paramRuleIn',
            type: 'string'
        }, {
            name: 'paramRuleType',
            type: 'int'
        }, {
            name: 'paramRuleLen',
            type: 'int'
        }, {
            name: 'paramRuleOut',
            type: 'string'
        }, {
            name: 'paramRuleDesc',
            type: 'string'
        }, {
            name: 'paramRuleOrder',
            type: 'int'
        }, {
            name: 'dataList',
            type: 'string'
        }]
    });
    var paramRulesStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramRuleModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    paramRulesStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: serviceIdForAllScripts,
            iflag: "0"
        };
        Ext.apply(paramRulesStore.proxy.extraParams, new_params);
    });
    var paramRuleTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name', 'id'],
        data: [{
            "name": "VARCHAR",
            "id": 0
        }, {
            "name": "INTEGER",
            "id": 1
        }, {
            "name": "NUMBER",
            "id": 2
        }, {
            "name": "DATE",
            "id": 3
        }, {
            "name": "CLOB",
            "id": 4
        }]
    });

    var paramRuleCombo = Ext.create('Ext.form.field.ComboBox', {
        store: paramRuleTypeStore,
        queryMode: 'local',
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'id',
        triggerAction: "all"
    });
    var paramRulesColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '顺序',
            dataIndex: 'paramRuleOrder',
            width: 50,
            editor: {
                allowBlank: false,
                xtype: 'numberfield',
                maxValue: 30,
                minValue: 1
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode("输出列名称：" + record.get('paramRuleOut') + "<br>排序：" + record.get('paramRuleOrder') + "<br>描述：" + record.get('paramRuleDesc'))
                    + '"';
                return value;
            }
        }, {
            text: '分隔符',
            dataIndex: 'paramRuleIn',
            width: 85,
            editor: {},
            hidden: true

        }, {
            text: '输出列名称',
            dataIndex: 'paramRuleOut',
            width: 130,
            editor: {
                allowBlank: false
            }
        },
        {
            text: '类型',
            dataIndex: 'paramRuleType',
            width: 85,
            editor: paramRuleCombo,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                if (value == 0) {
                    value = "VARCHAR";
                } else if (value == 1) {
                    value = "INTEGER";
                } else if (value == 2) {
                    value = "NUMBER";
                } else if (value == 3) {
                    value = "DATE";
                } else if (value == 4) {
                    value = "CLOB";
                } else {
                    value = "VARCHAR"
                }
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode(" 类型：" + value) + '"';
                return value;
            }
        }, {
            text: '长度',
            dataIndex: 'paramRuleLen',
            width: 85,
            value: 20,
            editor: {
                xtype: 'numberfield',
                maxValue: 4000,
                minValue: 1
            }
        }, {
            text: '描述',
            dataIndex: 'paramRuleDesc',
            flex: 1,
            editor: {
                allowBlank: true
            }
        }];
    var outruleGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'south',
        height: 250,
        title: '输出规则',
        plugins: [cellEditing2],
        store: paramRulesStore,
        border: true,
        columnLines: true,
        columns: paramRulesColumns,
        emptyText: '没有规则参数',
        tools: [{
            type: 'plus',
            tooltip: '增加',
            handler: addOut
        }, {
            type: 'minus',
            tooltip: '删除',
            callback: function (panel, tool, event) {
                var data = outruleGrid.getView().getSelectionModel().getSelection();
                if (data.length == 0) {
                    Ext.Msg.alert('提示', '请先选择您要操作的行!');
                    return;
                } else {
                    Ext.Msg.confirm("请确认", "是否真的要删除所选记录？", function (button, text) {
                        if (button == "yes") {
                            var deleteIds = [];
                            $.each(data, function (index, record) {
                                if (record.data.iid > 0) {
                                    deleteIds.push(record.data.iid);
                                } else {
                                    paramRulesStore.remove(data);
                                }
                            });
                            if (deleteIds.length > 0) {
                                Ext.Ajax.request({
                                    url: 'deleteScriptParams.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                        iids: deleteIds
                                    },
                                    success: function (response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            paramRulesStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "删除失败！");
                                    }
                                });
                            } else {
                                outruleGrid.getView().refresh();
                            }
                        }
                    });
                }
            }
        }]
    });
    var FieldContainer;
    if (projectFlagForAll == 1) {
        FieldContainer = new Ext.form.RadioGroup({
            fieldLabel: '脚本类型',
            labelWidth: 70,
            labelCls: Ext.baseCSSPrefix + 'form-item-label label_space',
            name: 'ra_s_type',
            padding: '0 5 10 5',
            items: [{
                name: 'ra_s_type',
                width: 80,
                inputValue: '0',
                boxLabel: 'shell',
                checked: true,
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            if (checkRadioForAllScripts != 0) {
                                editor.setOption("mode", 'shell');
                                checkRadioForAllScripts = 0;
                                editor.isEmpty("");
                            }
                        }
                    }
                }
            },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '1',
                    boxLabel: 'bat',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                if (checkRadioForAllScripts != 1) {
                                    checkRadioForAllScripts = 1;
                                    editor.setOption("mode", 'bat');
                                    editor.isEmpty("");
                                }
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '2',
                    boxLabel: 'perl',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 2;
                                editor.setOption("mode", 'text/x-perl');
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '3',
                    boxLabel: 'python',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 3;
                                editor.setOption("mode", 'python');
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 100,
                    inputValue: '6',
                    boxLabel: 'powershell',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 6;
                                editor.setOption("mode", 'powershell');
                            }
                        }
                    }
                }]
        });
    } else {
        FieldContainer = new Ext.form.RadioGroup({
            fieldLabel: '脚本类型',
            labelWidth: 70,
            name: 'ra_s_type',
            padding: '0 5 10 5',
            items: [{
                name: 'ra_s_type',
                width: 80,
                inputValue: '0',
                boxLabel: 'shell',
                checked: true,
                disabled: true,
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            if (checkRadioForAllScripts != 0) {
                                editor.setOption("mode", 'shell');
                                checkRadioForAllScripts = 0;
                                editor.isEmpty("");
                            }
                        }
                    }
                }
            },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '1',
                    boxLabel: 'bat',
                    disabled: true,
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                if (checkRadioForAllScripts != 1) {
                                    checkRadioForAllScripts = 1;
                                    editor.setOption("mode", 'bat');
                                    editor.isEmpty("");
                                }
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '2',
                    boxLabel: 'perl',
                    disabled: true,
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 2;
                                editor.setOption("mode", 'text/x-perl');
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '3',
                    boxLabel: 'python',
                    disabled: true,
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 3;
                                editor.setOption("mode", 'python');
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 80,
                    inputValue: '4',
                    boxLabel: 'sql',
                    disabled: true,
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 4;
                                editor.setOption("mode", 'sql');
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type',
                    width: 100,
                    inputValue: '6',
                    boxLabel: 'powershell',
                    disabled: true,
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function (value) {
                                checkRadioForAllScripts = 6;
                                editor.setOption("mode", 'powershell');
                            }
                        }
                    }
                }]
        });
    }
    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });
    var sName1 = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .9
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });
    var scName1 = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .9
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 2 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });

    var usePlantForm1 = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .9,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });

    var scriptWorkDir = new Ext.form.TextField({
        name: 'scriptWorkDir',
        fieldLabel: '工作目录',
        editable: false,
        readOnly: true,
        hidden: !scriptWorkDirSwitch,
        displayField: 'scriptWorkDir',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });

    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });
    var excepResult1 = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .9
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: .5
    });
//     var labelStore = Ext.create('Ext.data.Store', {
//         autoLoad: true,
//         fields: ['label'],
//         proxy: {
//             type: 'ajax',
//             url: 'queryLabels.do',
//             reader: {
//                 type: 'json',
//                 root: 'dataList'
//             }
//         }
//     });
// //查询标签
//     labelStore.on('beforeload', function (store, options) {
//         var new_params = {
//             scriptuuid: scriptuuid
//         };
//         Ext.apply(labelStore.proxy.extraParams, new_params);
//     });
//     labelStore.on('load', function (store, records) {
//         for (let i = 0; i < records.length; i++) {
//             console.log(records[i].data.label);
//             labels += records[i].data.label + ' ';
//         }
//         label.setValue(labels);
//     });
//     label = Ext.create('Ext.ux.ideal.form.ComboBox', {
//         name: 'label',
//         labelWidth: 70,
//         columnWidth: 1,
//         queryMode: 'local',
//         fieldLabel: '标签',
//         displayField: 'label',
//         valueField: 'label',
//         readOnly: true,
//         store: labelStore,
//     });
    label = Ext.create('Ext.panel.Panel', {
        // region: 'south',
        border: false,
        columnWidth: 1,
        width: '100%',
        hidden: !labelSwitch,
        html: '<div class="report_box unclickable">' +
            '<div class="tagsinput-primary form-group" id="signDiv">' +
            '<label class="s_tit" ><span>标签:</span></label>' +
            '<input name="tagsinput" id="tagsinputval" class="tagsinput" data-role="tagsinput" value=""  disabled="disabled" >' +
            '</div>' +
            '</div>'
    });

    label.on("afterrender", function () {
        //console.log($("#tagsinputval"),$("#tagsinputval").siblings('.bootstrap-tagsinput'));
        if ($("#tagsinputval").siblings('.bootstrap-tagsinput').length > 0) {
            $("#tagsinputval").siblings('.bootstrap-tagsinput').remove();
            $('#tagsinputval').remove();

        }
        if ($.fn.tagsinput) {

            $("#tagsinputval").tagsinput();
        }

        function setLabel() {
            if (labelEdit != null && labelEdit != '') {
                var labs = labelEdit.split(",")
                for (var i = 0; i < labs.length; i++) {
                    la.add(labs[i], i);
                }
                addTags(la)
            }
        }
        setLabel();
    }, this);
    var errExcepResult1 = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: .9
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
// fieldLabel: '功能概述',
        displayField: 'funcdesc',
        emptyText: '',
// labelWidth: 70,
// padding: '0 5 0 0',
        readOnly: true,
        columnWidth: 1,
        height: 130,
        autoScroll: true
    });
    var ycCheckBeforeExec =  Ext.create('Ext.form.field.Checkbox',{
        hidden:!checkBeforeExecSwitch,
        margin:0,
        boxLabel: '先试点，后批量原则',
        readOnly:true
    })

    var buttonFormPanel = Ext.create('Ext.form.FieldContainer', {
        defaultType: 'checkboxfield',
        items: [
            ycCheckBeforeExec
        ]
    })

    var timeout = new Ext.form.TextField({
        name: 'timeout',
        fieldLabel: '超时(秒)',
        displayField: 'timeout',
        emptyText: '',
        hidden: !scriptTimeoutSwitch,
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var scriptForm = Ext.create('Ext.form.Panel', {
        width: 650,
        height: 320,
        border: false,
        layout: 'anchor',
// margin: 10,
        collapsible: false,
// title: '基本信息',
        items: [{
            border: false,
            layout: 'column',
            margin: '5',
            items: [sName, scName]
        }, {
            border: false,
            layout: 'column',
            margin: '5',
            items: [groupNameCombo,bussCb, bussTypeCb]
        },
            {
                layout: 'column',
                border: false,
                margin: '5',
                items: [threeBussTypeCb, excepResult]
            },
            {
                layout: 'column',
                border: false,
                margin: '5',
                items: [errExcepResult, usePlantForm]
            },{
                layout: 'column',
                border: false,
                margin: '5',
                items: [scriptWorkDir]
            },{
                layout: 'column',
                border: false,
                margin: '5',
                items: [label]
            }, {
                layout: 'column',
                border: false,
                margin: '5',
                items: [timeout]
            }]
    });

    var sriptDetialForm = Ext.create('Ext.form.Panel', {
        region: 'south',
        height: 290,
        border: true,
        layout: 'anchor',
        margin: '5 3 0 0',
        collapsible: false,
        hidden: !allScriptSwitch,
        title: '基本信息',
        items: [{
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [sName1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [scName1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [bussCb1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [bussTypeCb1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [excepResult1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [errExcepResult1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [usePlantForm1]
        }, {
            layout: 'column',
            border: false,
            margin: '5 3 0 15',
            items: [scriptWorkDir]
        }]
    });

    var baseInfoOfScriptWin = Ext.create('widget.window', {
        title: '基本信息',
        closable: true,
        closeAction: 'hide',
        modal: true,
        width: 720,
        minWidth: 350,
        height: 450,
        layout: {
            type: 'border',
            padding: 5
        },
        items: [scriptForm],
        dockedItems:[
            {
                xtype: 'toolbar',
                dock:'bottom',
                layout: {
                    pack: 'center'
                },
                items:[{
                    xtype: "button",
                    cls:'Common_Btn',
                    text: "关闭",
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            },
            {
                xtype: 'toolbar',
                dock:'bottom',
                layout: {
                    pack: 'center'
                },
                items:[buttonFormPanel]
            }
        ]
    });


    var funcDescForm = Ext.create('Ext.form.Panel', {
        region: 'north',
        height: 140,
        border: true,
        layout: 'anchor',
        margin: '5 3 0 0',
        collapsible: false,
        cls: 'customize_panel_back',
        title: '功能说明',
        items: [{
            layout: 'column',
            border: false,
            items: [funcDesc]
        }],
        tools: [{
            type: 'print',
            tooltip: '基本信息',
            hidden: allScriptSwitch,
            handler: function (event, toolEl, panelHeader) {
                if (!scName.getValue()) {
                    scName.setValue(scriptName);
                }

                baseInfoOfScriptWin.show();
            }
        }, {
            type: 'help',
            tooltip: '帮助',
            callback: function (panel, tool, event) {
                window.open("scriptHelpDoc.do", "resizable=yes").focus();
            }
        }]
    });


    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'paramType',
                type: 'string'
            }, {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            },
            {
                name: 'ruleName',
                type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }]
    });

    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    attachmentStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });


    var defultEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true
        })
    });
    var passwordEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true,
            inputType: 'password'
        })
    });
    var paramColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 60,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                coun = record.get('paramDefaultValue');
                let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" +  record.get('parameterName')
                    + "<br>默认值：" + coun
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            dataIndex: 'parameterName',
            width: 80,
            text: '枚举名称',
            editable: false,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                coun = record.get('paramDefaultValue');
                let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" +  record.get('parameterName')
                    + "<br>默认值：" + coun
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramDefaultValue',
            width: 70,
            text: '参数值',
            allowBlank: true,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                let showValue = value;

                let paramType = record.get('paramType');

                if (paramType == 'IN-string(加密)') {
                    let xing = "";
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }
        }, {
            dataIndex: 'ruleName',
            width: 120,
            text: '验证规则',
            hidden: !bhParameterCheckSwitch
        },
//		    {
//		        text: '默认值',
//		        dataIndex: 'paramDefaultValue',
//		        width: 70,
//		        editor: {
//		            allowBlank: true
//		        },
//		        renderer:function (value, metaData, record, rowIdx, colIdx, store){
//	                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
//	                return value;
//	            }
//		    },
        {
            text: fjnxCISwitch?'参数名称':'描述',
            dataIndex: 'paramDesc',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 50,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }];


    var attachmentColumns = [{
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    }, {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer: function (value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    }, {
        menuDisabled: true,
        sortable: false,
        xtype: 'actioncolumn',
        width: 50,
        items: [
            {
                iconCls: 'script_download',
                tooltip: '下载',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attachmentStore.getAt(rowIndex);
                    //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                    window.location.href = 'downloadScriptAttachment.do?iid=' + rec.get('iid');
                }
            }
        ]
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    Ext.define('paramManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
            {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'parameterValue',
                type: 'string'
            },
            {
                name: 'parameterDesc',
                type: 'string'
            }]
    });

    Ext.define('paramManangerModel2', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'paravalue',
                type: 'string'
            }]
    });
    var enumValueStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        // autoDestroy: true,
        // pageSize: 10,
        model: 'paramManangerModel',
        proxy: {
            type: 'ajax',
            url: 'getParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                // totalProperty: 'total'
            }
        }
    });
    var defaultValueStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'paramManangerModel2',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    defaultValueStore.on('beforeload', function (store, options) {
        var new_params = {
            paramName: golbalParamName
        };
        Ext.apply(defaultValueStore.proxy.extraParams, new_params);
    });
    var paramGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        margin: '3 3 0 0',
        cls: 'customize_panel_back',
        store: paramStore,
        plugins: [cellEditing],
        emptyText: '没有脚本参数',
        border: true,
        columnLines: true,
        columns: paramColumns,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {

                var columnIndex = e.column.dataIndex;
                // 点击的当前行数据
                var recordData = e.record.data;

                var paramType = recordData.paramType;           // 是否为枚举类型
                var parameterName = recordData.parameterName;   // 参数名称
                // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue";
                var columnBooParameterName = columnIndex == "parameterName";
                var columnBooparamDefaultValue = columnIndex == "paramDefaultValue"
                // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                    golbalParamName = parameterName;
                    defaultValueStore.load();
                }
                // 判断如果为枚举类型，并且当前操作列为“参数名称”，设置单元格为下拉框
                if (paramType == "枚举" && columnBooParameterName) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "parameterName",
                        displayField: "parameterName",
                        store: enumValueStore,
                        editable: false
                    });
                }
                if (paramType == "枚举" && columnBooparamDefaultValue) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore,
                        editable: false
                    });
                }
                // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                if (paramType != "枚举" && columnBoo) {
                    e.column.setEditor({
                        xtype: 'textfield',
                        readOnly: columnIndex == "parameterName" ? true : false,

                    })
                }

                if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {

                    let pass = new Ext.form.TextField({
                        inputType: 'password',

                    });

                    e.column.setEditor(pass)
                }
            }
        }
    });

    var attachmentGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'south',
        height: 190,
        margin: '3 3 0 0',
        cls: 'customize_panel_back',
        //title: '附件',
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        store: attachmentStore,
        border: true,
        hidden: allScriptSwitch,
        emptyText: '没有附件',
        columnLines: true,
        columns: attachmentColumns/*
											 * , dockedItems: [{ xtype:
											 * 'toolbar', height: 45, items: [{
											 * xtype: 'tbtext', id: 's-n-t-e-v',
											 * text: '.' }] }]
											 */
    });

    var tempColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '模板名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attaTempStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttaTemplate.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];

    //获取所有模板
    var attaTempStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attaTempModelAllScritpsView',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttaTemplate.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    /********************/
    attaTempStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: tempmentIds
        };
        Ext.apply(attaTempStore.proxy.extraParams, new_params);
    });
    attaTempStore.on('load', function (me, records, successful, eOpts) {
        tempmentIds = [];
        $.each(records, function (index, record) {
            tempmentIds.push(record.get('iid'));
        });
    });
    /********************/

    var attachmentGrid2 = Ext.create('Ext.grid.Panel', {
        region: 'north',
        cls: 'window_border panel_space_top panel_space_right panel_space_left panel_space_bottom ',
        border: true,
        layout: 'border',
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        margin: '5 0 0 0',
        height: 190,
        emptyText: '没有附件',
        store: attaTempStore,
        columnLines: true,
        columns: tempColumns
    });

    var pagetab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            region: 'center',
            activeTab: 0,
            height: contentPanel.getHeight() * 0.25,
            border: false,
            items: [
                {
                    title: '附件',
                    layout: 'fit',
                    items: [attachmentGrid]
                },
                {
                    title: '模板',
                    layout: 'fit',
                    hidden: !templateSwitch,
                    items: [attachmentGrid2]
                }
            ]
        });

    var templateGrid = Ext.create('Ext.panel.Panel', {
        region: 'south',
        cls: 'attachments customize_panel_back panel_space_top',
        items: [pagetab]

    });


    var paramsAndFuncDescPanel;
    if (projectFlagForAll == 1) {
        paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
            region: 'east',
//			        collapsible : false,
            collapsible: true,
            collapsed: true,
            border: false,
            width: 450,
            layout: {
                type: 'border'
            },
            items: [funcDescForm, paramGrid, outruleGrid]
        });
    } else {
        paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
            region: 'east',
//			        collapsible : false,
            collapsible: true,
            collapsed: true,
            border: false,
            width: 450,
            layout: {
                type: 'border'
            },
            items: [funcDescForm, paramGrid, templateGrid/*attachmentGrid*/, sriptDetialForm]
        });
    }
    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        }, {
            name: 'name',
            type: 'string'
        }, {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    resourceGroupStore.on('load', function () {
        var ins_rec = Ext.create('resourceGroupModel', {
            id: '-1',
            name: '未分组',
            description: ''
        });
        resourceGroupStore.insert(0, ins_rec);
    });
    var resourceGroupObj = Ext.create('Ext.form.field.ComboBox',
        {
            fieldLabel: '资源组',
            emptyText: '--请选择资源组--',
            hidden: removeAgentSwitch,
            width: '25.2%',
            labelAlign: 'right',
            labelWidth: 70,
            multiSelect: true,
            store: resourceGroupStore,
            displayField: 'name',
            valueField: 'id',
            triggerAction: 'all',
            editable: false,
            mode: 'local',
            listeners: {
                change: function (comb, newValue, oldValue, eOpts) {
                    /*
										 * chosedResGroups_forest = new Array();
										 * for(var i=0;i<newValue.length;i++) {
										 * chosedResGroups_forest.push(newValue[i]); }
										 */
                    agent_store.load();
                }
            }
        });

    var agentStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "0", "name": "正常"},
            {"id": "1", "name": "异常"},
            {"id": "2", "name": "升级中"}
        ]
    });

    var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentStatus',
        labelWidth: 70,
        queryMode: 'local',
        fieldLabel: 'Agent状态',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择Agent状态--',
        store: agentStatusStore,
        width: '25.2%',
        labelAlign: 'right'
    });

    Ext.define('appNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'appName',
            type: 'string'
        }]
    });

    var app_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'appNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentAppNameList.do?envType=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var app_name = Ext.create('Ext.form.ComboBox', {
// editable: false,
        name: 'appname',
        fieldLabel: "应用名称",
        emptyText: '--请选择应用名称--',
        store: app_name_store,
        queryMode: 'local',
        width: "25%",
        displayField: 'appName',
        valueField: 'appName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'Agent IP',
        displayField: 'agentip',
        emptyText: '--请输入Agent IP--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.2%'
    });
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '主机名称',
        displayField: 'hostname',
        emptyText: '--请输入主机名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%'
    });
    /*
			 * var sys_name = new Ext.form.TextField({ name : 'sysname',
			 * fieldLabel : '系统名称', displayField : 'sysname', emptyText :
			 * '--请输入系统名称--', labelWidth : 70, labelAlign : 'right', width :
			 * '25%' });
			 */

    Ext.define('sysNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'sysName',
            type: 'string'
        }]
    });

    var sys_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'sysNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentSysNameList.do?envType=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var sys_name = Ext.create('Ext.form.ComboBox', {
// editable: false,
        name: 'sysname',
        fieldLabel: "系统名称",
        emptyText: '--请选择系统名称--',
        store: sys_name_store,
        queryMode: 'local',
        width: "25%",
        displayField: 'sysName',
        valueField: 'sysName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '系统类型',
        displayField: 'ostype',
        emptyText: '--请输入系统类型--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%'
    });


    var search_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [sys_name, app_name, host_name, os_type
            ]
        },
            {
                xtype: 'toolbar',
                dock: 'top',
                items: [agent_ip, resourceGroupObj, agentStatusCb,
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '查询',
                        handler: function () {
                            pageBar.moveFirst();
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            agent_ip.setValue('');
                            app_name.setValue('');
                            sys_name.setValue('');
                            host_name.setValue('');
                            os_type.setValue('');
                            resourceGroupObj.setValue('');
                            agentStatusCb.setValue('');
                        }
                    }
                ]
            }]
    });


    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'appName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentState', type: 'int'}
        ]
    });

    var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var agent_columns = [
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '系统名称', dataIndex: 'sysName', flex: 1},
        {text: '应用名称', dataIndex: 'appName', flex: 1},
        {text: '主机名称', dataIndex: 'hostName', flex: 1},
        {text: 'IP', dataIndex: 'agentIp', width: 150},
        {text: '端口号', dataIndex: 'agentPort', width: 100},
        {text: '系统类型', dataIndex: 'osType', width: 140},
        {
            text: '描述', dataIndex: 'agentDesc', flex: 1, hidden: true,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }
    ];

    agent_store.on('beforeload', function (store, options) {
        var new_params = {
            agentIp: Ext.util.Format.trim(agent_ip.getValue()),
            appName: app_name.getValue() == null ? '' : Ext.util.Format.trim(app_name.getValue() + ""),
            sysName: sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + ""),
            hostName: Ext.util.Format.trim(host_name.getValue()),
            osType: Ext.util.Format.trim(os_type.getValue()),
            rgIds: resourceGroupObj.getValue(),
            agentState: agentStatusCb.getValue(),
            flag: 0,
            switchFlag: projectFlagForAll
        };

        Ext.apply(agent_store.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });

    var agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: agent_store,
        border: false,
        columnLines: true,
        columns: agent_columns,
        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
//			    bbar: pageBar
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar'
    });

    function settime(btn, text) {
        if (countdown == 0) {
            btn.setDisabled(false);
            btn.setText(text);
            btn.setWidth(80);
            countdown = 10;
        } else {
            btn.setDisabled(true);
            btn.setText(text + "(" + countdown + ")");
            btn.setWidth(100);
            countdown--;
            setTimeout(function () {
                settime(btn, text)
            }, 1000)
        }

    }

    var mainP = Ext.create('Ext.panel.Panel', {
// width: '100%',
// margin: 10,
        region: 'center',
        minHeight: 80,
        border: true,
        collapsible: false,
        cls: 'customize_panel_back',
        autoScroll: true,
        title: "编辑框",
        height: contentPanel.getHeight(),
        html: '<textarea id="code-edit-view" value style="height:100%;"></textarea>',
        /*
				 * items: [FieldContainer,{ xtype: 'button', text : 'My Button'
				 * }],
				 */
        tbar: [FieldContainer/* ,'->', tryATrybtn */]
    });

    var consolePanel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        height: 150,
        border: false,
        collapsible: false,
        autoScroll: true,
        html: '<pre id="consoleLog-edit-view" style="height:100%;background: #4b4b4b;color: white;margin:0;"></pre>'
    });

    var tryTestbtn = Ext.create('Ext.Button', {
        text: '测试',
// cls: 'Blue_button',
        handler: function () {
            testScript();
        }
    });

    var westPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        cls: 'panel_space_right_zb',
        // padding: '5 0 0 0',
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        height: contentPanel.getHeight(),
        items: [mainP/* , consolePanel */],
        buttonAlign: 'center',
        buttons: [  /*
								 * tryTestbtn, { text: '发布', handler:
								 * function(){ publishScript(); } }, { text :
								 * '版本回退', handler : versionRollBack },
								 */{
            text: '返回',
// cls : 'Red_button',
            handler: fanhui
        }]
    });

    var pppPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
// padding: 5,
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        height: contentPanel.getHeight(),
        items: [westPanel, paramsAndFuncDescPanel]
    });


    mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "gridBasicScriptForAllScripts_view_area",
        layout: {
            type: 'border'
        },
        border: false,
        height: contentPanel.getHeight() - modelHeigth,
        items: [versionGrid, pppPanel]
    });

    editor = CodeMirror.fromTextArea(document.getElementById('code-edit-view'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        readOnly: true
    });
    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 83);
    contentPanel.on('resize',
        function () {
            editor.getDoc().clearHistory();
            mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
            mainPanel.setWidth(contentPanel.getWidth());
            editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 88);
            if (chooseTestAgentWin) {
                chooseTestAgentWin.center();
            }
        });

    function fanhui() {
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'viewAllScripts.do',
            scripts: true,
            params: {
                servicesName: queryServicesName,
                scriptName: queryScriptName,
                oneLevelType: queryOneLevelType,
                secondLevelType: querySecondLevelType,
                threeTypeId: threeTypeId,
                scriptType: queryScriptType,
                scriptState: queryScriptState,
                createUser: queryCreateUser,
                keywordsSerach: queryKeywordsSerach,
                scriptDir: queryScriptDir
            }
        });
    }

    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };


    function testScript() {
        if (chooseTestAgentWin) {
            chooseTestAgentWin.show();
        } else {
            chooseTestAgentWin = Ext.create('Ext.window.Window', {
                title: '选择测试服务器',
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                width: contentPanel.getWidth() - 190,
                height: contentPanel.getHeight(),
                layout: 'border',
                items: [search_form, agent_grid],
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'bottom',
                    layout: {pack: 'center'},
                    items: [{
                        xtype: "button",
                        cls: 'Common_Btn',
                        margin: '6',
                        text: "确定",
                        handler: function () {
                            var me = this;
                            var records = agent_grid.getSelectionModel().getSelection();
                            if (records.length != 1) {
                                Ext.Msg.alert('提示', '请选择记录，并且只能选择一条记录！');
                                return;
                            }

                            function realTest() {
                                var jsonData = "[" + Ext.JSON.encode(records[0].data) + "]";

                                var scriptPara = '';
                                paramStore.sort('paramOrder', 'ASC');
                                var m = paramStore.getRange(0, paramStore.getCount() - 1);
                                var aaaa = [];
                                for (var i = 0, len = m.length; i < len; i++) {
                                    var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                                    var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';

                                    if (paramType == 'int' && paramDefaultValue) {
                                        if (!checkIsInteger(paramDefaultValue)) {
                                            Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                                            return;
                                        }
                                    }
                                    if (paramType == 'float' && paramDefaultValue) {
                                        if (!checkIsDouble(paramDefaultValue)) {
                                            Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                                            return;
                                        }
                                    }
                                    if (paramDefaultValue.indexOf('"') >= 0) {
                                        if (checkRadioForAllScripts == '1') {
                                            Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                                            return;
                                        }
                                    }
                                    aaaa.push(paramDefaultValue);
// scriptPara += paramDefaultValue + " ";
                                }
                                scriptPara = aaaa.join("@@script@@service@@");
                                Ext.Ajax.request({
                                    url: 'execScriptServiceForSync.do',
                                    method: 'POST',
                                    params: {
                                        serviceId: newServiceId,
                                        execUser: "",
                                        scriptPara: scriptPara,
                                        jsonData: jsonData,
                                        ifrom: 0,
                                        flag: 0
                                    },
                                    success: function (response, request) {
// var coatId = Ext.decode(response.responseText).coatId;
// var flowId = Ext.decode(response.responseText).flowId;
                                        var requestId = Ext.decode(response.responseText).requestIds[0];
                                        isFromTryATry = 0;
                                        tryRequestId = requestId;
                                        tryAgentIp = records[0].get('agentIp');
                                        tryAgentPort = records[0].get('agentPort');

                                        if (refreshTry) {
                                            clearInterval(refreshTry);
                                        }

                                        refreshTry = setInterval(function () {
                                            loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                        }, 5 * 1000);

                                        me.up("window").close();
                                        settime(tryTestbtn, '测试');
                                        Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
                                        $('#consoleLog-edit-view').html("");
                                    },
                                    failure: function (result, request) {
                                        Ext.Msg.alert('提示', '执行失败！');
                                        $this.html('执行脚本');
                                    }
                                });
                            }

                            var agentState = records[0].get('agentState')
                            if (agentState != 0) {
                                Ext.Msg.confirm("请确认", "选择的代理状态为异常，是否仍然进行测试？", function (id) {
                                    if (id == 'yes') {
                                        realTest();
                                    }
                                });
                            } else {
                                realTest();
                            }
                        }
                    }, {
                        xtype: "button",
                        cls: 'Common_Btn',
                        margin: '6',
                        text: "取消",
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
                }]
            }).show();
        }
        resourceGroupObj.setValue('');
        agentStatusCb.setValue('');
        search_form.getForm().findField("agentIp").setValue('');
        search_form.getForm().findField("agentDesc").setValue('');
        agent_store.load();
    }


    function compareScript() {
        var seledCnt = selModel.getCount();
        if (seledCnt != 2) {
            Ext.MessageBox.alert("提示", "请选择两个不同版本的脚本进行比对！");
            return;
        }
        var ss = selModel.getSelection();
        var ids = [];
        for (var i = 0; i < 2; i++) {
            var version1 = ss[i].data.iid;
            ids.push(version1);
        }
        if (ids.length > 0) {
            Ext.Ajax.request({
                url: 'compareScript.do',
                method: 'post',
                params: {
                    ids: ids
                },
                success: function (response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        var leftVersion = Ext.decode(response.responseText).leftVersion;
                        var rightVersion = Ext.decode(response.responseText).rightVersion;
                        var leftContent = Ext.decode(response.responseText).leftContent;
                        var rightContent = Ext.decode(response.responseText).rightContent;
                        var compareWin = Ext.create('widget.window', {
                            title: '对比详情',
                            closable: true,
                            closeAction: 'destroy',
                            width: contentPanel.getWidth() - 200,
                            minWidth: 350,
                            height: contentPanel.getHeight(),
                            draggable: false,
                            // 禁止拖动
                            resizable: false,
                            // 禁止缩放
                            modal: true,
                            loader: {
                                url: 'showCompare.do',
                                params: {
                                    leftVersion: leftVersion,
                                    rightVersion: rightVersion,
                                    leftContent: leftContent,
                                    rightContent: rightContent
                                },
                                autoLoad: true,
                                scripts: true
                            }
                        });
                        compareWin.show();
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "请求返回失败！", request);
                }
            });
        }
    }


    function loadShelloutputhisInfo(requestId, iip, iport) {
        var surl = "getScriptExecOutputForTry.do";
        Ext.Ajax.request({
            url: surl,
            params: {
                isFromTryATry: isFromTryATry,
                requestId: requestId,
                agentIp: iip,
                agentPort: iport
            },
            success: function (response, opts) {
                var msg = Ext.decode(response.responseText).out;
                var status = Ext.decode(response.responseText).status;
                if (!Ext.isEmpty(Ext.util.Format.trim(msg))) {
                    $('#consoleLog-edit-view').html(msg);
                    consolePanel.body.scroll('bottom', 300000);
                }
                if (status == 2) {
                    if (refreshTry) {
                        clearInterval(refreshTry);
                    }
                }
            },
            failure: function (response, opts) {
                $('#consoleLog-edit-view').html('获取执行信息失败');
            }

        });
    }

    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }]
    });


    editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function (store, options) {
        var queryparams = {
            iid: newServiceId
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });

    editScriptStore.on('load', function (store, options, success) {
        var reader = store.getProxy().getReader();
        // Ext.getCmp("scriptpara").setValue(reader.jsonData.scriptPara);
        scName.setValue(reader.jsonData.scriptName);
// $('#s-n-t-e-v').html(reader.jsonData.scriptName);
        if (reader.jsonData.scriptName) {
            mainP.setTitle(reader.jsonData.scriptName);
        } else {
            mainP.setTitle("编辑框");
        }
        scriptName = reader.jsonData.scriptName;
        sName.setValue(reader.jsonData.serviceName);
        timeout.setValue(reader.jsonData.timeout == -1 ? null : reader.jsonData.timeout);
        sName1.setValue(reader.jsonData.serviceName);
        scName.setValue(reader.jsonData.scriptName);
        scName1.setValue(reader.jsonData.scriptName);
        excepResult.setValue(reader.jsonData.excepResult);
        excepResult1.setValue(reader.jsonData.excepResult);
        errExcepResult.setValue(reader.jsonData.errExcepResult);
        errExcepResult1.setValue(reader.jsonData.errExcepResult);
        usePlantForm.setValue(reader.jsonData.platForm);
        usePlantForm1.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);
        ycCheckBeforeExec.setValue(reader.jsonData.checkBeforeExec);
        scriptWorkDir.setValue(reader.jsonData.scriptWorkDir);

        var scriptT = reader.jsonData.scriptType;
        if (scriptT == 'sh') {
            FieldContainer.items.items[0].setValue(true);
            FieldContainer.items.items[0].setDisabled(false);
            checkRadioForAllScripts = 0;
            editor.setOption("mode", 'shell');
        } else if (scriptT == 'bat') {
            FieldContainer.items.items[1].setValue(true);
            FieldContainer.items.items[1].setDisabled(false);
            checkRadioForAllScripts = 1;
            editor.setOption("mode", 'bat');
        } else if (scriptT == 'py') {
            FieldContainer.items.items[3].setValue(true);
            FieldContainer.items.items[3].setDisabled(false);
            checkRadioForAllScripts = 3;
            editor.setOption("mode", 'python');
        } else if (scriptT == 'sql') {
            FieldContainer.items.items[4].setValue(true);
            FieldContainer.items.items[4].setDisabled(false);
            checkRadioForAllScripts = 4;
            editor.setOption("mode", 'sql');
        } else if (scriptT == 'perl') {
            FieldContainer.items.items[2].setValue(true);
            FieldContainer.items.items[2].setDisabled(false);
            checkRadioForAllScripts = 2;
            editor.setOption("mode", 'text/x-perl');
        } else if (scriptT == 'ps1') {
            FieldContainer.items.items[5].setValue(true);
            FieldContainer.items.items[5].setDisabled(false);
            checkRadioForAllScripts = 6;
            editor.setOption("mode", 'powershell');
        }
        editor.setOption('value', reader.jsonData.content);
        groupName = parseInt(reader.jsonData.groupName);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        threeBsTypeId = parseInt(reader.jsonData.threeTypeId);
        groupNameStore.load();
        bussData.load();
        attachmentStore.load();
        paramStore.load();

    });

    function addOut() {
        var store = outruleGrid.getStore();
        var ro = store.getCount();
        var p = {
            iid: '',
            paramRuleOrder: ro + 1,
            paramRuleIn: '',
            paramRuleOut: '',
            paramRuleType: 0,
            paramRuleLen: 50,
            paramRuleDesc: ''
        };
        store.insert(0, p);
        outruleGrid.getView().refresh();
    }

    function StringToPassword(strs) {
        if (strs && strs != null & strs != '') {
            var password = '';
            for (var i = 0; i < strs.length; i++) {
                password = password + '●';
            }
            return password;
        } else {
            return '';
        }
    }
});
function addTags(las) {
    var labKey = las.getKeys();
    $("#tagsinputval").tagsinput('add', labKey.join(','));
}