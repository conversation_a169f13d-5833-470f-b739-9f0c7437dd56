Ext.onReady(function() {
	destroyRubbish();
	
	var jspParms = tempData;
	delete tempData;
	
	var bussData = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'bsName' ],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsAll.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var bussTypeData = Ext.create('Ext.data.Store', {
		fields : [ 'sysTypeId', 'sysType' ],
		autoLoad : false,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsTypeByFk.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var tmpname="一级分类";
	var tmptext="--请选择一级分类--";
	if(projectFlag==1){
		tmpname="脚本分类";
		tmptext="--请选择脚本分类--";
	}
	var bussCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'sysName',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : tmpname,
		
		displayField : 'bsName',
		valueField : 'iid',
		editable : false,
		emptyText : tmptext,
		store : bussData,
		width : '22%',
        labelAlign : 'right',
		listeners : {
			change : function() { // old is keyup
				bussTypeCb.clearValue();
				bussTypeCb.applyEmptyText();
				bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
				bussTypeData.load({
					params : {
						fk : this.value
					}
				});
			},
			 specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
		}
	});
	var tmpname1="一级分类";
	var tmptext1="--请选择一级分类--";
	if(projectFlag==1){
		tmpname1="操作类型";
		tmptext1="--请选择操作类型--";
	}
	/** 操作类型* */
	var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussType',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : tmpname1,
		displayField : 'sysType',
		valueField : 'sysTypeId',
		editable : false,
		hidden:true,
		emptyText : tmptext1,
		store : bussTypeData,
		width : '22%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		value:filter_serverNameQuery,
		labelWidth : 70,
		width : '22%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var scriptStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"-1", "name":"草稿"},
			{"id":"1", "name":"已上线"},
			{"id":"3", "name":"共享"},
			{"id":"2", "name":"审核中"}
		]
	});
	
	var scriptStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptStatus',
		labelWidth : 60,
		queryMode : 'local',
		fieldLabel : '作业状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择作业状态--',
		value:filter_scriptStateQuery,
		store : scriptStatusStore,
		width : '22%',
		labelAlign : 'right',
		listeners: {
			afterRender : function(combo) {
		          if(filter_scriptStateQuery=='-10000') {
								combo.setValue(scriptStatusStore.getAt(0).data.id);
							} else if(filter_scriptStateQuery=='-1'){
								combo.setValue(scriptStatusStore.getAt(1).data.id);
							}else if(filter_scriptStateQuery=='1'){
								combo.setValue(scriptStatusStore.getAt(2).data.id);
							}else if(filter_scriptStateQuery=='2'){
								combo.setValue(scriptStatusStore.getAt(3).data.id);
							}else if(filter_scriptStateQuery=='3'){
								combo.setValue(scriptStatusStore.getAt(4).data.id);
							}
		        },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	Ext.define('startUserModel', {
		extend : 'Ext.data.Model',
		fields : [{
		  name : 'iid',
		  type : 'string'
		}, {
		  name : 'iusername',
		  type : 'string'
		}, {
			  name : 'iuserfullname',
			  type : 'string'
			}]
	});
	
	var startUserStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'startUserModel',
		proxy : {
			type : 'ajax',
			url : 'getStartUser.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
	    }
	});
	
	var scriptAuthorCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptAuthor',
		labelWidth : 60,
		queryMode : 'local',
		fieldLabel : '创建者',
		displayField : 'iuserfullname',
		valueField : 'iid',
		editable : false,
		emptyText : '--请选择创建者--',
		store : startUserStore,
		width : '24%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	 var search_form = Ext.create('Ext.form.Panel', {
		 region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	border : false,
	    	baseCls:'customize_gray_back',
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back', 
				border : false,
				dock : 'top',
				items: [sName,bussCb,bussTypeCb,scriptStatusCb,scriptAuthorCb, {
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						pageBar.moveFirst();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
					}
				}]
			}]
		});
	
	Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'author'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'}
	    ]
	});
	
	var scriptServiceReleaseStoreForFlow = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'scriptService/queryAllJobs.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	scriptServiceReleaseStoreForFlow.on('beforeload', function (store, options) {
	    var new_params = {  
			bussId:bussCb.getValue(),
			bussTypeId:bussTypeCb.getValue(),
			serviceName:sName.getValue(),
			scriptStatus:scriptStatusCb.getValue(),
			scriptAuthor:scriptAuthorCb.getValue(),
			onlyScript: 0
	    };
	    
	    Ext.apply(scriptServiceReleaseStoreForFlow.proxy.extraParams, new_params);
    });
	var tmpname2="一级分类";
	var tmptext2="二级分类";
	if(projectFlag==1){
		tmpname2="脚本分类";
		tmptext2="操作类型";
	}
	var scriptServiceReleaseColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		}, 
		{
		    text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
			text : '服务名称',
		    dataIndex : 'serviceName',
		    width : 200,flex:1
		},
		{
		    text : tmpname2,
		    dataIndex : 'buss',
		    width : 200,flex:1
		}, 
		{
			text : tmptext2,
			dataIndex : 'bussType',
			width : 250,flex:1
		},
		{
			text : '作业状态',
			dataIndex : 'status',
			width : 100,
			renderer:function(value,p,record,rowIndex){
		    	if(value==-1) {
		    		return '<font color="#F01024">草稿</font>';
		    	} else if (value==1) {
		    		return '<font color="#0CBF47">已上线</font>';
		    	} else if (value==2) {
		    		return '<font color="#FFA602">审核中</font>';
		    	} else if (value==3) {
		    		return '<font color="#13B1F5">已共享</font>';
		    	} else if (value==9) {
		    		return '<font color="">已共享未发布</font>';
		    	} else {
		    		return '<font color="#CCCCCC">未知</font>';
		    	}
		    }
		},
		{
			text : '创建者',
			dataIndex : 'author',
			width : 150
		}, 
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			width:80,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid'); // 其实是requestID
				var serviceName = record.get('serviceName');
				var bussId = record.get('bussId');
				var bussTypeId  =record.get('bussTypeId');
				var status = record.get('status');
				var viewVersionFuncName = "viewVersionForFlowForFlowForAllJobs";
				var filter_serverNameQuery = search_form.getForm().findField("serverName").getValue()==null?'':search_form.getForm().findField("serverName").getValue();
				var filter_scriptStateQuery = search_form.getForm().findField("scriptStatus").getValue()==null?'':search_form.getForm().findField("scriptStatus").getValue();
				
        		return  '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+viewVersionFuncName+'('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+jspParms.menuId+',\''+filter_serverNameQuery+'\',\''+filter_scriptStateQuery+'\''+')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"/>'+'查看'+
	       			   	'</a>'+
	       			   '</span>';
        		;
			}
		}
	];
	  var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: scriptServiceReleaseStoreForFlow,
		  	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    dock: 'bottom',
		    displayInfo: true,
		    emptyMsg:'找不到任何记录'
		  });
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : scriptServiceReleaseStoreForFlow,
	    cls:'customize_panel_back',
	    bbar : pageBar,
	    border:false,
	    columnLines : true,
	    columns : scriptServiceReleaseColumns
	});
	 var mainPanel = Ext.create('Ext.panel.Panel',{
		 renderTo : "all_jobs_grid_area",
	        width : contentPanel.getWidth(),
		    height :contentPanel.getHeight() - modelHeigth,
	        border : true,
	        layout: 'border',
	        items : [search_form,scriptServiceReleaseGrid]
	});
	 
	 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function (){
			mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
		});
		

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(mainPanel);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});
	
	function clearQueryWhere(){
		bussCb.setValue('');
		bussTypeCb.setValue('');
		scriptStatusCb.setValue('');
		scriptAuthorCb.setValue('');
		//scName.setValue('');
		sName.setValue('');
		//scriptTypeParam.setValue('');
	}
});

function viewVersionForFlowForFlowForAllJobs(iid,status,serviceName,bussId,bussTypeId,menuId,filter_serverNameQuery,filter_scriptStateQuery){
	
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: 'scriptViewVersionForFlow.do',
		params: {
			serviceId: iid,
			iid: iid,
			menuId: menuId,
			status: status,
			serviceName:serviceName,
			actionType:'view',
			bussId:bussId,
			bussTypeId:bussTypeId,
			submitType: 'ah',
			flag:0,
			rootspace: 'view',
			filter_serverNameQuery:filter_serverNameQuery,
			filter_scriptStateQuery:filter_scriptStateQuery,
			ifrom : 'viewAllJobs.do'
		},
		
		scripts: true});
}
