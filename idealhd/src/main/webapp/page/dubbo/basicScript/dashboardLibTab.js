Ext.onReady(function() {
	Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'platForm',type : 'string'}, 
		    {name : 'version',type : 'string'}, 
		    {name : 'content'     ,type : 'string'} ,
		    {name : 'status'     ,type : 'int'},
		    {name : 'isCollected'     ,type : 'int'}
	    ]
	});

    var shareScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 6,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForLib.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    shareScriptStore.on('beforeload',function(store, options) {
        var new_params = {
            libType: libType,
            serviceName: libServiceName,
            sysType: libSysType,
            onlyScript: libScriptType=='script'?1:0
        };

        Ext.apply(shareScriptStore.proxy.extraParams, new_params);
    });
    
    shareScriptStore.on('load', function(store, options) {
    	shareScriptsDataView.getSelectionModel().select(0);  
    });

    var shareScriptsPageBar = Ext.create('Ext.PagingToolbar', {
        store: shareScriptStore,
        dock: 'bottom',
        displayInfo: false,
        afterPageText : '页',
		beforePageText : '第',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '',
        emptyMsg: '找不到任何记录'
    });

    var shareScriptsDataView = Ext.create('Ext.view.View', {
        store: shareScriptStore,
        tpl: [
              '<ul>',
	              '<tpl for=".">',
		              '<li class="shareScriptS">',
		              	  '<div class="Script_library">',
				              '<table cellpadding="0" cellspacing="0" border="0" width="100%">',
					              '<tr>',
						              '<td width="26"><div class="{scriptType:this.humanScriptType}"></div></td>',
						              '<td><div class="Script_textF2">{serviceName}</div></td>',
						              '<td width="24"><a href="javascript:void(0);" data-service-id="{iid}" class="collect-it {isCollected:this.collection}"></a></td>',
					              '</tr>',
				              '</table>',
			              '</div>',
		              '</li>',
	              '</tpl>',
              '</ul>', 
              {
            	  humanScriptType: function(name) {
            		  	var scriptType = '';
		                if (name == 'sh') {
		                    scriptType = 'shell';
		                } else if (name == 'bat') {
		                    scriptType = 'bat';
		                } else if (name == 'py') {
		                    scriptType = 'python';
		                } else if (name == 'perl') {
		                    scriptType = 'perl';
		                }else if (name == 'sql') {
		                    scriptType = 'sql';
		                } else if (name == '组合') {
		                    scriptType = 'ssflow';
		                }
		                return scriptType;
            	  },
            	  collection: function(name) {
		                var css = 'collection-col';
		                if (name == 0) {
		                	css = 'uncollection-col';
		                }
		                return css;
            	  }
              }],
        multiSelect: false,
        height: '100%',
        trackOver: true,
        overItemCls: 'x-item-over',
        itemSelector: 'li.shareScriptS',
        selectedItemCls: 'active',
        emptyText: '没有脚本',
        listeners: {
        	select: function( view, record, eOpts ) {
        		whichScriptIsClick = record.get('iid');
        		var fromType = 0;
        		if(libType=='share') {
        			fromType = 3;
        		} else if(libType=='hot') {
        			fromType = 1;
        		} else if(libType=='new') {
        			fromType = 3;
        		}
        		$.getJSON("scriptService/queryOneService.do", {fromType:fromType, iid:record.get('iid')}, function(res){
    				$('#scr-creator-'+(libScriptType==='script'?"":"_job")).html("<span>"+res.fullName+"</span>");
    				$('#scr-create-time-'+(libScriptType==='script'?"":"_job")).html("<span>"+res.createTime+"</span>");
    				$('#scr-platform-'+(libScriptType==='script'?"":"_job")).html("<span>"+(res.platForm?res.platForm:'')+"</span>");
    				$('#scr-buss-'+(libScriptType==='script'?"":"_job")).html("<span>"+res.bussN+"</span>");
    				$('#scr-buss-type-'+(libScriptType==='script'?"":"_job")).html("<span>"+res.bussT+"</span>");
    				$('#scr_func_desc-'+(libScriptType==='script'?"":"_job")).html("<span>"+res.funcDesc+"</span>");
    			});
        		$.getJSON("scriptService/queryOneServiceOfComment.do", {iid:record.get('iid')}, function(res){
    				var ss = '';
    				$.each(res, function(index, val){
    					var likeImg = 'unlike';
        				if(val.isLike>0) {
        					likeImg = 'like';
        				}
    					ss += '<div class="Comment_List1">'+
    								val.content+
    								'<div class="List_tool">'+
    									'<span class="List_left">'+val.createTime+'</span>'+
    									'<span class="List_right"><img class="like-it" data-id="'+val.iid+'" src="images/'+likeImg+'.png" align="absmiddle"/><font>('+val.likeNum+')</font></span>'+
    								'</div>'+
    							'</div>';
    				});
    				$('#comment-overview-'+(libScriptType=='script'?"":"_job")).html(ss);
    			});
        		return true;
            },
            'itemdblclick': function(view, record, item, idx, event, opts) {
            	whichScriptIsClick = record.get('iid');
            	var fromType = 0;
        		if(libType=='share') {
        			fromType = 3;
        		} else if(libType=='hot') {
        			fromType = 2;
        		} else if(libType=='new') {
        			fromType = 3;
        		}
        		if(record.get('scriptType')=='组合') {
        			var DetailWinTi = Ext.create('widget.window', {
        				title: '详细信息',
        				closable: true,
        				closeAction: 'destroy',
        				width: contentPanel.getWidth(),
        				minWidth: 350,
        				height: contentPanel.getHeight(),
        				draggable: false,
        				// 禁止拖动
        				resizable: false,
        				// 禁止缩放
        				modal: true,
        				loader: {
        					url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
        					params: {
        						iid:record.get('iid'),
        						serviceName:record.get('serviceName'),
        						actionType:'view',
        						bussId:record.get('bussId'),
        						bussTypeId:record.get('bussTypeId'),
        						flag:0,	
        						isShowInWindow: 1
        					},
        					autoLoad: true,
        					scripts: true
        				}
        			});
        			DetailWinTi.show();
        		} else {
        			if (!scriptDetailWin) {
        				scriptDetailWin = Ext.create('widget.window', {
        					title: '详细信息',
        					closable: true,
        					closeAction: 'hide',
        					width: contentPanel.getWidth(),
        					minWidth: 350,
        					height: contentPanel.getHeight(),
        					draggable: false,
        					resizable: false,
        					modal: true,
        					loader: {}
        				});
        			}
        			
        			scriptDetailWin.getLoader().load({
        				url: 'queryOneServiceForView.do',
        				params: {
        					iid: record.get('iid'),
        					flag: fromType,
        					hideReturnBtn: 1
        				},
        				autoLoad: true,
        				scripts: true
        			});
        			scriptDetailWin.show();
        		}
            },
            afterrender: function( view, eOpts ){
            	view.getSelectionModel().select(0);  
            }
        }
    });

    var shareScriptsGrid = Ext.create('Ext.Panel', {
        region: 'center',
    	border: false,
    	autoScroll: true,
        items: shareScriptsDataView,
        bbar: shareScriptsPageBar,
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "dashboardLibTab_area_"+libType+libScriptType,
        width: '100%',
        height: '100%',
        border: false,
        layout: 'border',
        defaults: {
            split: true
        },
        items: [shareScriptsGrid]
    });
    
    $(document).off('click', '.comment-window').on('click', '.comment-window', function(res){
		commentWindow.show ();
		commentFormPanel.getForm().findField("comm").setValue('');
	});
    
    $('body').off('click', '.collect-it').on('click', '.collect-it', function(e){
    	var $this = $(this);
    	var serviceId = $this.data('service-id');
    	Ext.Ajax.request({
			url : 'collectScriptService.do',
			method : 'POST',
			params : {
				iid:serviceId
			},
			success : function(response, request) {
				shareScriptStore.reload();
			},
			failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
    });
    
    contentPanel.on ('resize', function (){
		mainPanel.setHeight ('100%');
		mainPanel.setWidth ('100%');
	});

});