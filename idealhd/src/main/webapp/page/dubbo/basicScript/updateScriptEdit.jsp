<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.commons.Constants" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<% 
	long iid=Long.parseLong(request.getParameter("serviceId"));
	boolean scriptLevelSwitch= Environment.getInstance().getScriptLevelSwitch();
	boolean scriptCheckRuleSwitch= Environment.getInstance().getScriptCheckRuleSwitch ();
	boolean scriptTemplateSwitch= Environment.getInstance().getScriptEditTemplateSwitch();
	boolean limitTwenty= Environment.getInstance().getLimitTwenty();
	boolean istrySwitch=Environment.getInstance().getScriptTrySwitch();
	boolean  isSu= Environment.getInstance().getSuUserSwitch();
	boolean isProject=Environment.getInstance().getScriptProjectSwitch();
	boolean psbcBindAgent=Environment.getInstance().getScriptPsbcBindAgentSwitch();
	boolean templateSwich = Environment.getInstance().getTempUploadTabSwitch();
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	boolean gfScriptDirFunctionSwitch = Environment.getInstance().getGFScriptDirFunctionSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
	// 脚本输出定义
	boolean scriptFunctionOutputSwitch = Environment.getInstance().getScriptFunctionOutputSwitch();
	boolean checkBeforeExecSwitch = Environment.getInstance().getYCScriptCheckBeforeExec();
	boolean scriptEditBookSwitch = Environment.getInstance().getScriptEditBookSwitch();
	boolean scriptShellSyntaxValidateSwitch = Environment.getInstance().getScriptShellSyntaxValidate();
	//脚本编写合规性检查
	String scriptCheck = Environment.getInstance().getScriptReasonableCheck();
	//分类目录
	boolean JlnxScriptClassSwitch = Environment.getInstance().getJlnxScriptClassSwitch();
	//脚本tab页显隐开关
	boolean getScriptEditeTabShowSwitch = Environment.getInstance().getScriptEditeTabShowSwitch();
	//单指令显隐开关
	boolean getSingleCommandSwitch = Environment.getInstance().getScriptSingleCommandTestSwitch();
	//SQL类型脚本显示隐藏
	boolean getScriptSqlShowSwitch = Environment.getInstance().getScriptSqlShowSwitch();
	//工作目录
	boolean jlnsScriptWorkDirSwitch = Environment.getInstance().getJlnxScriptWorkDir();
	// 福建农信 脚本发布直接通过
	boolean scriptCrossPublishPassSwitch = Environment.getInstance().getScriptCrossPublishPass();
	boolean orderNumberSwitch = StringUtils.isNotEmpty(Environment.getInstance().getHSScriptPublishItsm());
	//尝试功能显隐控制
	boolean scriptTrySwitch=Environment.getInstance().getScriptTryScripShowSwitch();
%>
<html>
<head>
<script type="text/javascript">
	var JlnxScriptClassSwitch=<%=JlnxScriptClassSwitch%>;
	var scriptCheck = '<%=scriptCheck%>';
	var scriptFunctionOutputSwitch = <%=scriptFunctionOutputSwitch%>;
	var iidForUpdateScriptEdit="<%=iid%>";
	var isProject=<%=isProject%>;
	var psbcBindAgentSwitch = <%=psbcBindAgent%>;
	var scriptTemplateSwitch=<%=scriptTemplateSwitch%>;
	var limitTwenty=<%=limitTwenty%>;
	var istrySwitch = <%=istrySwitch%>;
	var hasVersionForUpdateScriptEdit="<%=request.getAttribute("hasVersion")%>";
	var versionFlag=<%=request.getAttribute("versionflag")%>;
	var uuidForUpdateScriptEdit="<%=request.getAttribute("uuid")%>";
	var labelEdit="<%=request.getAttribute("labels")%>";
	var updatecheckRadio = 0;
	var sessionIdForUpdateScriptEdit = '<%=request.getSession().getId()%>';
	var scriptCheckRuleSwitch=<%=scriptCheckRuleSwitch%>;
	var checkBeforeExecSwitch = <%=checkBeforeExecSwitch%>;
	var filter_bussIdForUpdateScriptEdit = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
	var filter_bussTypeIdForUpdateScriptEdit = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
	var filter_scriptNameForUpdateScriptEdit = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_keywordsForUpdateScriptEdit = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
	var filter_serviceNameForUpdateScriptEdit = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_scriptTypeForUpdateScriptEdit = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
	var filter_scriptStatusForUpdateScriptEdit = '<%=request.getParameter("filter_scriptStatus")==null?-10000:request.getParameter("filter_scriptStatus")%>';
	var filter_serviceTypeForUpdateScriptEdit  = '<%=request.getParameter("filter_serviceType")==null?"":request.getParameter("filter_serviceType")%>';
	var filter_patFromValueForUpdateScriptEdit = '<%=request.getParameter("filter_patFromValue")==null?"":request.getParameter("filter_patFromValue")%>';
	var filter_scriptdirForUpdateScriptEdit = '<%=request.getParameter("filter_scriptDir")==null?"":request.getParameter("filter_scriptDir")%>';
	var filter_selectUnboundScriptForUpdateScriptEdit = '<%=request.getParameter("filter_selectUnboundScript")==null?"false":request.getParameter("filter_selectUnboundScript")%>';
	var serviceIdNum = '<%=request.getParameter("serviceIdNum")==null?"":request.getParameter("serviceIdNum")%>';
	var projectFlag = '<%=request.getAttribute("projectFlag")==null?"0":request.getAttribute("projectFlag")%>';
	var sqlexectypeValue = '<%=request.getAttribute("sqlexectypeValue")==null?"1":request.getAttribute("sqlexectypeValue")%>';
	var scriptLevelSwitch = <%=scriptLevelSwitch%>;
	var sqlModel='<%=request.getParameter("sqlModel")==null?0:request.getParameter("sqlModel")%>';
	var scriptType='<%=request.getParameter("scriptType")==null?"":request.getParameter("scriptType")%>';
	var updateScript_Status='<%=request.getAttribute("status")==null?"":request.getAttribute("status")%>';
	var script_showGridSwitch=<%=request.getAttribute("showGridSwitch")==null?"":request.getAttribute("showGridSwitch")%>;

	var filterScriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';

	//判断脚本类型和执行模式
    var agentPullChosedStoreUrl;
    if(sqlModel==2 && scriptType=='sql' ){
    	agentPullChosedStoreUrl="getTryAgentListByResource.do";
    }else{
    	agentPullChosedStoreUrl="getTryAgentList.do";
    }
    var isSu = <%=isSu%>;
	var oldUUID = '<%=request.getAttribute("oldUUID")%>';
	var templateSwitch = <%=templateSwich%>;
	var labelSwitch = <%=sdScriptLabelEditSwitch%>;
	var gfScriptDirFunctionSwitch = <%=gfScriptDirFunctionSwitch%>;
	var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>;
	var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>;
	var scriptEditBookSwitch = <%=scriptEditBookSwitch%>;
	var scriptShellSyntaxValidateSwitch=<%=scriptShellSyntaxValidateSwitch%>;
	var singleCommandSwitch = <%=getSingleCommandSwitch%>;
	var scriptSqlShowSwitch = <%=getScriptSqlShowSwitch%>;
	var scriptCrossPublishPassSwitch = <%=scriptCrossPublishPassSwitch%>;
	var arrs=new Array();
	var thisStatus  = '<%=request.getAttribute("thisStatus")%>';
	<%
	//渤海上传附件扩展名控制
	String upFileType=Environment.getInstance().getSysConfig("script.create.basic.upfile.extension", "");
	String[] actInfo=upFileType.split(",");
    for (int i = 0; i < actInfo.length; i++) {
	%>
		arrs.push('<%=actInfo[i]%>');
	<%
    	}
	%>

	var getScriptEditeTabShowSwitch=<%=getScriptEditeTabShowSwitch%>;
	var scriptWorkDirSwitch = <%=jlnsScriptWorkDirSwitch%>
	<%
    String bankFlag = Environment.getInstance().getBankSwitch();
         boolean fjFlag=false;
          if((Constants.BANK_FJNX).equals(bankFlag))
          {
              fjFlag=true;
          }
    %>
	var fjFlag =<%=fjFlag%>;
	// hs银行 发布对接
	var orderNumberSwitch = <%=orderNumberSwitch%>
	//尝试一下显隐控制
	var scriptTrySwitch = <%=scriptTrySwitch%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/md5.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/functionAndVariableCommon.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/updateScriptEdit.js"></script>
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin_v2/css/input_style.css">
</head>
<body>
	<input type="hidden" id="scriptManagePageExecUserNameText" />
	<div id="gridUpdateScriptEdit_area" style="width: 100%; height: 25%;"></div>
</body>
</html>
<%-- <%@page contentType="text/html; charset=utf-8"%>
<html>
<title>CodeMirror: Shell mode</title>
<meta charset="utf-8"/>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<h2>Shell mode</h2>
<textarea id=code>
#!/bin/bash

# clone the repository
git clone http://github.com/garden/tree

# generate HTTPS credentials
cd tree
openssl genrsa -aes256 -out https.key 1024
openssl req -new -nodes -key https.key -out https.csr
openssl x509 -req -days 365 -in https.csr -signkey https.key -out https.crt
cp https.key{,.orig}
openssl rsa -in https.key.orig -out https.key

# start the server in HTTPS mode
cd web
sudo node ../server.js 443 'yes' &gt;&gt; ../node.log &amp;

# here is how to stop the server
for pid in `ps aux | grep 'node ../server.js' | awk '{print $2}'` ; do
  sudo kill -9 $pid 2&gt; /dev/null
done

exit 0</textarea>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
    mode: 'shell',
    lineNumbers: true,
    matchBrackets: true
  });
</script>
</html> --%>