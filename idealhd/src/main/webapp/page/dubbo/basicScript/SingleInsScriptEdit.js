var newiid = 0;
var golbalParamName;    // 参数名称列对应的选择值

Ext.onReady(function () {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    var dataList;
    var selectedSysRecords = [];
    var selectedAgentRecords = new Array();
    var sysid;
    var project_panel;
    var scriptForm;
    var newUUID;
    var project_panelScript;
    var uploadProcessWin;
    var baseInfoOfScriptWin;
    let scriptTemplateScriptTypeValue = 'shell';
    var chooseTestAgentWin;
    var publishAuditingSMWin;
    var attachmentIds = [];
    var tempmentIds = [];
    var scriptName = "";
    var auditing_form_sm;
    var saveFromBottom = true;
    var countdown = 10;
    var tryRequestId = '';
    var tryAgentIp = '';
    var tryAgentPort = '';
    var cmdRequestId = '';
    var cmdAgentIp = '';
    var cmdAgentPort = '';
    var execUserConfigWindow;
    var refreshTryForBasic;
    var refreshCmdForBasic;
    var scriptDesc = "";
    var newServiceId = 0;
    var scriptuuid = "";
    var isFromTryATry = 0;
    var agentChosedId = 0;
    var whichButtonIsClicked = 0; // 1:尝试一下 2:测试 3:终端选择服务器
    var cmdVForbasicEdit = null;//CMD输入内容
    var sysID;
    var groupName;
    var busID;
    var threeBsTypeId;
    var checkRadioForBasicScriptEdit = 0;
    var chosedAgentWin;
    var bdyhzUUID;
//	var chosedAgentIds = [];
    var chosedAppSys = new Array();
    var kk;
    var isGroupType = 0;
    var agentiids = new Array();
    let selectedAgent = new Set();
    var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    var requiredCfg = '<span style="color:red;font-weight:bold" data-qtip="Required">  *按输入周期执行</span>';
    var requiredVersionAndResource = '<span style="color:red;font-weight:bold" data-qtip="请配置版本或者资源组" onclick="versionAndResource()">  点击配置选项</span>';

    var authSateComboStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data: [[2, 'DBA可见'], [3, '项目经理可见'], [4, '普通用户可见']]
    });

    var enumdd = '';


    var ipStart = Ext.create('Ext.form.TextField',
        {
            labelWidth: 79,
            fieldLabel: '起始IP',
            emptyText: '--请输入开始IP--',
            //labelSeparator : '',
            width: '40%',
            labelAlign: 'right'
        });
    /** 结束ip* */
    var ipEnd = Ext.create('Ext.form.TextField',
        {
            labelWidth: 70,
            fieldLabel: '终止IP',
            emptyText: '--请输入截止IP--',
            //labelSeparator : '',
            labelAlign: 'right',
            width: '40%'
        });

    var selModelAgents = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelAgentsSS',
        checkOnly: true
    });

    var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: function () {
            agent_grid.ipage.moveFirst();
        },
        bodyCls: 'x-docked-noborder-top',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [ipStart, ipEnd,
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        agentgridScript.ipage.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: 'IP清空',
                    handler: function () {
                        ipStart.setValue('');
                        ipEnd.setValue('');
                        agentgridScript.ipage.moveFirst();
                    }
                }]
        }]
    });
    Ext.define('groupNameModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'GNAME', // 名称
            type : 'string'
        }, {
            name : 'IID', // ID
            type : 'long'
        } ]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model : 'groupNameModel',
        autoLoad : true,
        proxy : {
            type : 'ajax',
            url : 'queryComboGroupName.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });
    groupNameStore.on('load', function (store, options) {
        if (groupName) {
            groupNameCombo.setValue(groupName);
            bussData.load({
                params: {
                    fk: groupName
                }
            });
        }
    });
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: sdFunctionSortSwitch?false:true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    bussData.on('load', function (store, options) {
        if (sysID) {
            bussCb.setValue(sysID);
            bussTypeData.load({
                params: {
                    fk: sysID
                }
            });
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussTypeData.on('load', function (store, options) {
        if (busID) {
            bussTypeCb.setValue(busID);
        }
    });

    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    threeBussTypeData.on('load', function (store, options) {
        if (threeBsTypeId) {
            threeBussTypeCb.setValue(threeBsTypeId);
        }
    });

    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden:!sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value !== null) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    //北京邮储 三级分类
    var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threeBussTypeCb',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        hidden: !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var authCombox = Ext.create('Ext.form.field.ComboBox', {
        name: 'authoCombox',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '权限',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        hidden: true,
        store: authSateComboStore
    });

    /** *********************Panel********************* */
    /*var FieldContainer_win = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 60,
        labelCls: Ext.baseCSSPrefix + 'form-item-label label_space',
        name: 'ra_s_type_win',
        items: [{
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '0',
            hidden: gdSwitch == 1 ? true : false,
            boxLabel: 'shell',
            checked: true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function (value) {
                        if (checkRadioForBasicScriptEdit != 0) {
                            editor.setOption("mode", 'shell');
                            checkRadioForBasicScriptEdit = 0;
                            scriptTemplateScriptTypeValue = 'shell';
                            scriptTemplateCombobox.setValue();
                            scriptTemplateComboboxStore.reload();
                        }
                        outruleGrid.hide();
                        chooseSqlExecModel.hide();
                        attachmentGrid.show();
                        maimPanels.setTitle('编辑框---shell');
                        clickNotdoc();
                        agentPullChosedStore.load(
                            {
                                url: "getTryAgentList.do"
                            });
                        consolePanel.setTitle("日志");
                    }
                }
            }
        },
            {
                name: 'ra_s_type_win',
                width: 50,
                inputValue: '1',
                boxLabel: 'bat',
                hidden: gdSwitch == 1 ? true : false,
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            if (checkRadioForBasicScriptEdit != 1) {
                                checkRadioForBasicScriptEdit = 1;
                                editor.setOption("mode", 'bat');
                                scriptTemplateScriptTypeValue = 'bat';
                                scriptTemplateCombobox.setValue();
                                scriptTemplateComboboxStore.reload();
                            }
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            maimPanels.setTitle('编辑框---bat');
                            clickNotdoc();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                        }
                    }
                }
            },
            {
                name: 'ra_s_type_win',
                width: 50,
                inputValue: '2',
                hidden: gdSwitch == 1 ? true : false,
                boxLabel: 'perl',
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            checkRadioForBasicScriptEdit = 2;
                            editor.setOption("mode", 'text/x-perl');
                            maimPanels.setTitle('编辑框---perl');
                            scriptTemplateScriptTypeValue = 'perl';
                            scriptTemplateCombobox.setValue();
                            scriptTemplateComboboxStore.reload();
                            clickNotdoc();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                        }
                    }
                }
            },
            {
                name: 'ra_s_type_win',
                width: 60,
                inputValue: '3',
                hidden: gdSwitch == 1 ? true : false,
                boxLabel: 'python',
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            checkRadioForBasicScriptEdit = 3;
                            editor.setOption("mode", 'python');
                            maimPanels.setTitle('编辑框---python');
                            scriptTemplateScriptTypeValue = 'python';
                            scriptTemplateCombobox.setValue();
                            scriptTemplateComboboxStore.reload();
                            clickNotdoc();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                        }
                    }
                }
            }, {
                name: 'ra_s_type_win',
                width: 45,
                inputValue: '4',
                checked: gdSwitch == 1 ? true : false,
                boxLabel: 'sql',
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            if (gdSwitch == 1) {
                                outruleGrid.show();
                                chooseSqlExecModel.show();
                                attachmentGrid.hide();
                            } else {
                                attachmentGrid.show();
                                chooseSqlExecModel.hide();
                                outruleGrid.hide();
                            }

                            editor.setOption("mode", 'text/x-plsql');
                            checkRadioForBasicScriptEdit = 4;
                            maimPanels.setTitle('编辑框---sql');
                            scriptTemplateScriptTypeValue = 'sql';
                            scriptTemplateCombobox.setValue();
                            scriptTemplateComboboxStore.reload();
                            clickNotdoc();
                            if (chooseSqlExecModel.getValue() == '2') {
                                agentPullChosedStore.load(
                                    {
                                        url: "getTryAgentListByResource.do"
                                    });
                            }
                            consolePanel.setTitle("日志");
                        }
                    }
                }
            }, {
                name: 'ra_s_type_win',
                width: 60,
                hidden: true,
                inputValue: '5',
                boxLabel: '文档',
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            checkRadioForBasicScriptEdit = 5;
                            clickdoc();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                            consolePanel.setTitle("日志");
                            /!*
						 * authCombox.show(); docfile.show(); scName.hide();
						 * errExcepResult.hide(); excepResult.hide();
						 *
						 * tryTestbtn.disabled =true; publishBtn.disabled =true;
						 * saveBtn.disabled =true;
						 *!/
                        }
                    }
                }
            }, {
                name: 'ra_s_type_win',
                width: 100,
                inputValue: '6',
                hidden: gdSwitch == 1 ? true : false,
                boxLabel: 'powershell',
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            outruleGrid.hide();
                            chooseSqlExecModel.hide();
                            attachmentGrid.show();
                            checkRadioForBasicScriptEdit = 6;
                            editor.setOption("mode", 'powershell');
                            maimPanels.setTitle('编辑框---powershell');
                            scriptTemplateScriptTypeValue = 'powershell';
                            scriptTemplateCombobox.setValue();
                            scriptTemplateComboboxStore.reload();
                            clickNotdoc();
                            agentPullChosedStore.load(
                                {
                                    url: "getTryAgentList.do"
                                });
                        }
                    }
                }
            }]
    });*/

    // var FieldContainerForOnecmd = new Ext.form.RadioGroup({
    //     fieldLabel: '脚本类型111',
    //     labelWidth: 60,
    //     labelCls: Ext.baseCSSPrefix + 'form-item-label label_space',
    //     name: 'ra_s_type1',
    //     items: [{
    //         name: 'ra_s_type1',
    //         width: 60,
    //         inputValue: '0',
    //         boxLabel: 'shell',
    //         checked: true
    //     },
    //         {
    //             name: 'ra_s_type1',
    //             width: 50,
    //             inputValue: '1',
    //             boxLabel: 'bat'
    //         },
    //         {
    //             name: 'ra_s_type1',
    //             width: 50,
    //             inputValue: '2',
    //             boxLabel: 'perl'
    //         },
    //         {
    //             name: 'ra_s_type1',
    //             width: 60,
    //             inputValue: '3',
    //             boxLabel: 'python'
    //         }, {
    //             name: 'ra_s_type1',
    //             width: 100,
    //             inputValue: '6',
    //             boxLabel: 'powershell'
    //         }]
    // });
    var agentUrl = 'getTryAgentList.do';
    if (gdSwitch == 1) {
        agentUrl = "getTryAgentListByResource.do";
    }
    var agentPullChosedStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: agentUrl,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var agentPullChosedStoreForOneCmd = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getTryAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var agentPullChosedCbForOneCmd = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentPullChosedForOneCmd',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选择服务器--',
        store: agentPullChosedStoreForOneCmd

    });
    var agentPullChosedCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentPullChosed',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选择服务器--',
        store: agentPullChosedStore
    });

    var agentPullChosedStoreForCmd = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agentIp'],
        autoLoad: true,
        remoteSort: true,
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListNoPage.do?flag=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var agentPullChosedCbForCmd = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentPullChosedForCmd',
        labelWidth: 70,
        width: 120,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agentIp',
        valueField: 'iid',
        editable: true,
        emptyText: '请选择服务器',
        store: agentPullChosedStoreForCmd
    });

    var protocolTypeStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'name'],
        data: [
            {"iid": "1", "name": "SSH"},
            {"iid": "2", "name": "TELNET"}
        ]
    });

    var sqlexectype = Ext.create('Ext.data.Store', {
        fields: ['iid', 'name'],
        data: [
            {"iid": "1", "name": "Agent"},
            {"iid": "2", "name": "JDBC"}
        ]
    });
    Ext.define('scriptTemplateComboboxModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'IID', type: 'long'},
            {name: 'INAME', type: 'string'},
            {name: 'ICONTENT', type: 'string'}
        ]
    });
    var scriptTemplateComboboxStore = Ext.create('Ext.data.Store', {
        model: 'scriptTemplateComboboxModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'scriptService/getScriptTemplateComboboxStore.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    scriptTemplateComboboxStore.on('beforeload', function (store, options) {
        var new_params = {
            iscripttype: scriptTemplateScriptTypeValue
        };
        Ext.apply(scriptTemplateComboboxStore.proxy.extraParams, new_params);
    });
    //模版下拉选，根据选择脚本类型进行展示
    /*var scriptTemplateCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTemplateCombobox',
        width: 130,
        displayField: 'INAME',
        valueField: 'IID',
        editable: true,
        queryMode: 'local',
        hidden: !scriptTemplateSwitch,
        emptyText: '--选择脚本模版--',
        store: scriptTemplateComboboxStore,
        listeners: {//监听事件
            select: function (combo, records, eOpts) {
                let scriptContent = records[0].data.ICONTENT;
                editor.getDoc().setValue(scriptContent);
                editor.refresh();
            }
        }

    });*/
    var chooseSqlExecModel = Ext.create('Ext.form.field.ComboBox', {
        name: 'chooseSqlExecModel',
        width: 100,
        displayField: 'name',
        valueField: 'iid',
        editable: false,
        queryMode: 'local',
        value: gdSwitch == 1 ? '2' : '1',
        hidden: gdSwitch == 1 ? false : true,
        emptyText: '执行模式',
        store: sqlexectype,
        listeners: {//监听事件
            select: function () {
                if (checkRadioForBasicScriptEdit == 4 && chooseSqlExecModel.getValue() == '2') {
                    tryTestbtn.hide();
                    agentPullChosedStore.load(
                        {
                            url: "getTryAgentListByResource.do"
                        });
                } else {
                    tryTestbtn.show();
                    agentPullChosedStore.load(
                        {
                            url: "getTryAgentList.do"
                        });
                }
                agentPullChosedCb.setValue('');
            }
        }

    });

    var scriptfile = new Ext.form.field.File({
        name: 'scriptfile', // 设置该文件上传空间的name，也就是请求参数的名字
        fieldLabel: '本地文件',
        labelWidth: 70,
        labelStyle: 'margin:10px 0 0 0',
        padding: '0 8 0 0',
        msgTarget: 'side',
        anchor: '100%',
        height: 30,
        buttonText: '选择本地文件',
        columnWidth: 1,
        listeners: {//监听事件
            'change': function () {//读取
                var importfile = scriptForm.getForm().findField("scriptfile").getValue();
                if (importfile != '') {
                    scriptForm.getForm().submit({
                        url: 'ajaxImportScript.do',
                        success: function (form, action) {
                            var result = Ext.JSON.decode(action.response.responseText);
                            var errMsg = result.errMsg;
                            var scriptContent = result.scriptContent;
                            if (!errMsg) {
                                editor.getDoc().setValue(scriptContent);
                                editor.refresh();
                            }
                        },
                        failure: function (form, action) {
                        }
                    });
                }
                Ext.Msg.alert('提示', "脚本内容已导入，请保存！");
            }
        }
    });

    var chooseServerTypeForConsole = Ext.create('Ext.form.field.ComboBox', {
        name: 'chooseServerTypeForConsole',
        width: 80,
        displayField: 'name',
        valueField: 'iid',
        editable: false,
        queryMode: 'local',
        emptyText: '协议',
        store: protocolTypeStore
    });

    var choosePortForConsole = new Ext.form.NumberField({
        name: 'choosePortForConsole',
        emptyText: '端口',
        width: 70
    });

    var userNameForConsole = new Ext.form.TextField({
        name: 'userNameForConsole',
        emptyText: '用户名',
        width: 100
    });

    var userPasswordForConsole = new Ext.form.TextField({
        name: 'userPasswordForConsole',
        emptyText: '密码',
        inputType: 'password',
        width: 100
    });

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        afterLabelTextTpl: required,
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5,
        listeners: {
            blur: function (e) {
                //失去焦点事件
                if (e.getRawValue() != '') {
                    e.setRawValue(e.getRawValue().toString().trim())
                }
            }
        }
    });


    var labelNamePane = Ext.create('Ext.panel.Panel', {
        border: false,
        columnWidth: 1,
        width: '100%',
        hidden: !labelSwitch,
        html: '<div class="report_box">' +
            '<div class="tagsinput-primary form-group" id="signDiv">' +
            '<label class="s_tit" ><span>标签:</span></label>' +
            '<input name="tagsinput" id="tagsinputval" class="tagsinput" data-role="tagsinput" value=""   >' +
            '</div>' +
            '</div>'
    });

    labelNamePane.on("afterrender", function () {
        console.log($("#tagsinputval"), $("#tagsinputval").siblings('.bootstrap-tagsinput'));
        if ($("#tagsinputval").siblings('.bootstrap-tagsinput').length > 0) {
            $("#tagsinputval").siblings('.bootstrap-tagsinput').remove();
            $('#tagsinputval').remove();
        }
        if ($.fn.tagsinput) {
            $("#tagsinputval").tagsinput();
        }
    }, this);

    var docfile = new Ext.form.field.File({
        name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
        fieldLabel: '选择文档',
        labelWidth: 70,
        labelStyle: 'margin:10px 0 0 0',
        padding: '0 8 0 0',
        msgTarget: 'side',
        anchor: '100%',
        height: 30,
        buttonText: '浏览文档',
        hidden: true,
        columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        afterLabelTextTpl: required,
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5,
        regex: /^[0-9a-zA-Z_]{1,}$/,
        regexText: '只允许输入数字、字母、下划线' //^\w+$/gi,
    });
//    var usePlantFormStore;
//    if (gdSwitch == 1) {
//    	usePlantFormStore= Ext.create('Ext.data.SimpleStore', {
//        	fields: ['value', 'text'],
//            data: [['全部', '全部'],['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
//        });
//    }else{
//    	 usePlantFormStore= Ext.create('Ext.data.SimpleStore', {
//    	    	fields: ['value', 'text'],
//    	        data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
//    	    });
//    }

    var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    usePlantFormStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: gdSwitch
        };
        Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
    });


    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '适用平台',
        afterLabelTextTpl: required,
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        multiSelect: true,//启用多选
        editable: true,
        emptyText: '--请选择平台--',
        store: usePlantFormStore
    });

    // 添加下拉显示条数菜单选中事件
    /*usePlantForm.on("select", function (comboBox, records, eOpts) {
        if (records.length > 1) {
            var windowsFlag = false;
            var linuxFlag = false;
            Ext.each(records, function (record) {
                var iname1 = record.data.INAME;
                if (iname1 == 'Windows') {
                    windowsFlag = true;
                }
                if (iname1 != 'Windows') {
                    linuxFlag = true;
                }
                if (linuxFlag && windowsFlag) {
                    Ext.Msg.alert('提示', 'Windows平台和非Windows平台不能同时选择！');
                    usePlantForm.clearValue();
                    return;
                }
            });
        }
        var scriptTypeValue = FieldContainer_win.getChecked()[0].boxLabel;
        if (scriptTypeValue == 'bat' && comboBox.getValue() != 'Windows') {
            Ext.Msg.alert('提示', 'bat脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if (scriptTypeValue == 'powershell' && comboBox.getValue() != 'Windows') {
            Ext.Msg.alert('提示', 'powershell脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if ((scriptTypeValue == 'shell' || scriptTypeValue == 'perl') && comboBox.getValue() == 'Windows') {
            Ext.Msg.alert('提示', '只允许 bat、python、sql、powershell脚本才能选择Windows平台');
            usePlantForm.setValue();
            return;
        }
    });*/

    var expectValue = 0;
    if (expectValueSwitch) {
        expectValue = '';
    }
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        value: expectValue,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var timeout = new Ext.form.TextField({
        name: 'timeout',
        fieldLabel: '超时(秒)',
        displayField: 'timeout',
        emptyText: '',
        hidden: !scriptTimeoutSwitch,
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var textred = ' ';
    if (isSu) {
        textred = required;
    }
    var suUser = new Ext.form.TextField({
        name: 'suUser',
        fieldLabel: '启动用户',
        displayField: 'suUser',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5,
        afterLabelTextTpl: textred,
        hidden: !db_suExecUser && gdSwitch == 1
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '请输入功能说明...',
        columnWidth: 1,
        height: 136,
        hidden: true,
        autoScroll: true,
        listeners: {
            'blur': function (me, e, eOpts) {
                scriptDesc = me.getValue();
                funcDescInWin.setValue(scriptDesc);
            }
        }
    });
    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
        name: 'funcDescInWin',
        fieldLabel: '功能说明',
        afterLabelTextTpl: required,
        displayField: 'funcDescInWin',
        emptyText: '请输入功能说明...',
        labelWidth: 70,
        height: 136,
        columnWidth: .99,
        autoScroll: true
    });
    Ext.define('dbModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'string'
        }, {
            name: 'name',
            type: 'string'
        }]
    });
    var dbStore = Ext.create('Ext.data.Store', {
        autoDestroy: true,
        autoLoad: true,
        model: 'dbModel',
        proxy: {
            type: 'ajax',
            url: 'getDatabaseType.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    // 数据库类型
    var dbType = Ext.create('Ext.form.field.ComboBox', {
        name: 'dbType',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '数据库类型',
        padding: '0 5 0 0',
        editable: false,
        displayField: 'name',
        valueField: 'id',
        emptyText: '--请选择数据库类型--',
        store: dbStore
//		new Ext.data.SimpleStore({
//			fields : [ 'id', 'text' ],
//			data : [ [ '1', 'ORACLE' ], [ '2', 'DB2' ], [ '3', 'MYSQL' ] ]
//		})
    });

    var planTime_HH = Ext.create('Ext.form.NumberField', {
        fieldLabel: '小时',
        editable: true,
        name: 'HH',
        padding: '0 5 0 0',
        labelWidth: 40,
        columnWidth: .20,
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (null == newv || newv == '' || trim(newv) == '') {
                    planTime_HH.setValue("")
                } else {
                    if (/^[0-9]([0-9])*$/.test(newv)) {
                        if (newv > 23) {
                            Ext.Msg.alert("提示", "小时值需在1~23之间!");
                            planTime_HH.setValue(oldv)
                        }
                        return true;
                    } else {
                        Ext.Msg.alert("提示", "小时窗口只能正整数");
                        planTime_HH.setValue(oldv)
                        return false;
                    }
                }
            }
        }
    });
    var cfg_Display = Ext.create('Ext.form.DisplayField', {
        name: 'display',
        fieldLabel: '周期配置',
        afterLabelTextTpl: requiredCfg,
        labelWidth: 350,
        columnWidth: .9
    });
    var planTime_mi = Ext.create('Ext.form.NumberField', {
        fieldLabel: '分钟',
        editable: true,
        name: 'mi',
        padding: '0 5 0 0',
        labelWidth: 40,
        columnWidth: .20,
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (null == newv || newv == '' || trim(newv) == '') {
                    planTime_mi.setValue("")
                } else {
                    if (/^[0-9]([0-9])*$/.test(newv)) {
                        if (newv > 59) {
                            Ext.Msg.alert("提示", "分钟值需在1~59之间!");
                            planTime_mi.setValue(oldv)
                        }
                        return true;
                    } else {
                        Ext.Msg.alert("提示", "分钟窗口只能正整数");
                        planTime_mi.setValue(oldv)
                        return false;
                    }
                }
            }
        }
    });
    var planTime_DD = Ext.create('Ext.form.NumberField', {
        fieldLabel: '天数',
        editable: true,
        name: 'DD',
        padding: '0 5 0 0',
        labelWidth: 40,
        columnWidth: .20,
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (null == newv || newv == '' || trim(newv) == '') {
                    planTime_DD.setValue("")
                } else {
                    if (/^[0-9]([0-9])*$/.test(newv)) {
                        if (newv > 31) {
                            Ext.Msg.alert("提示", "天数值需在1~31之间!");
                            planTime_DD.setValue(oldv)
                        }
                        return true;
                    } else {
                        Ext.Msg.alert("提示", "天数窗口只能正整数");
                        planTime_DD.setValue(oldv)
                        return false;
                    }
                }
            }
        }
    });
    var versionAresource = Ext.create('Ext.form.DisplayField', {
        fieldLabel: '版本与资源组配置',
        afterLabelTextTpl: requiredVersionAndResource,
        labelWidth: 200,
        columnWidth: .44,
        padding: '0 0 0 0',
        margin: '10 0 0 0'
    });
    // 服务类型
    var serviceType = Ext.create('Ext.form.field.ComboBox', {
        name: 'serviceType',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '服务类型',
        padding: '0 5 0 0',
        editable: false,
        displayField: 'text',
        valueField: 'value',
        value: '1',
        emptyText: '--请选择服务类型--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['0', '应用'], ['1', '采集']]
        }),
        listeners: {
            select: function (combo, record, opts) {
                if (serviceType.getValue() == "应用" || serviceType.getValue() == "0" || serviceType.getValue() == 0) {
                    planTime_sm.hide();
                    versionAresource.hide();
                    tableName.hide();
                    cfg_Display.hide();
                    planTime_MM.hide();
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.hide();
                } else {
                    planTime_sm.show();
                    versionAresource.show();
                    tableName.show();
                    cfg_Display.show();
                    planTime_MM.show();
                    planTime_DD.show();
                    planTime_HH.show();
                    planTime_mi.show();
                }
                versionAresource.hide();
                planTime_sm.hide();
                cfg_Display.hide();
                planTime_MM.hide();
                planTime_DD.hide();
                planTime_HH.hide();
                planTime_mi.hide();
            },
            change: function (nf, newv, oldv) {
                if (newv == "应用"
                    || newv == "0"
                    || newv == 0) {
                    planTime_sm.hide();
                    versionAresource.hide();
                    tableName.hide();
                    cfg_Display.hide();
                    planTime_MM.hide();
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.hide();
                } else {
                    planTime_sm.show();
                    versionAresource.show();
                    tableName.show();
                    cfg_Display.show();
                    planTime_MM.show();
                    planTime_DD.show();
                    planTime_HH.show();
                    planTime_mi.show();
                }
                versionAresource.hide();
                planTime_sm.hide();
                cfg_Display.hide();
                planTime_MM.hide();
                planTime_DD.hide();
                planTime_HH.hide();
                planTime_mi.hide();
            }
        }
    });
    // 发起审核
    var isExam = Ext.create('Ext.form.field.ComboBox', {
        name: 'isExam',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '发起审核',
        padding: '0 5 0 0',
        editable: false,
        displayField: 'text',
        valueField: 'value',
        value: '1',
        emptyText: '--请选择--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '是'], ['0', '否']]
        })
    });

    var isAutoSub = Ext.create('Ext.form.field.ComboBox', {
        name: 'isAutoSub',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '自动订阅',
        padding: '0 5 0 0',
        editable: false,
        displayField: 'text',
        valueField: 'value',
        value: '0',
        emptyText: '--请选择--',
//		hidden:db_isAutoSub,
        hidden: true,
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '是'], ['0', '否']]
        })
    });
    var manualStart = Ext.create('Ext.form.field.ComboBox', {
        name: 'manualStart',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '手动发起',
        padding: '0 5 0 0',
        editable: false,
        displayField: 'text',
        valueField: 'value',
        value: '0',
        emptyText: '--请选择--',
        hidden: db_manualStart,
        hidden: true,
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '支持'], ['0', '不支持']]
        })
    });

    var formItems = [{
        border: false,
        layout: 'column',
        margin: '5',
        items: [sName, scName, authCombox]
    },
        {
            border: false,
            layout: 'column',
            margin: '5',
            items: [groupNameCombo]
        },{
        border: false,
        layout: 'column',
        margin: '5',
        items: [bussCb, bussTypeCb]
    }, {
        layout: 'column',
        border: false,
        margin: '5',
        items: [threeBussTypeCb, timeout]
    }, {
        layout: 'column',
        border: false,
        margin: '5',
        items: [excepResult, errExcepResult]
    }, {
        border: false,
        layout: 'column',
        margin: '5',
        items: [dbType, serviceType]
    }, {
        layout: 'column',
        border: false,
        margin: '5',
        items: [usePlantForm, suUser, isExam]
    },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [isAutoSub, manualStart]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [docfile]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [scriptfile]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [labelNamePane]
        }, {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDescInWin]
        }];

    scriptForm = Ext.create('Ext.form.Panel', {
        width: contentPanel.getWidth() * 0.6,
        height: contentPanel.getHeight() * 0.8,
        // bodyCls : 'x-docked-noborder-top',
        // baseCls:'customize_gray_back',
        border: false,
        layout: 'anchor',
        collapsible: false,
        items: formItems
    });

    var funcDescForm = Ext.create('Ext.form.Panel', {
        region: 'north',
        width: '100%',
        bodyCls: 'x-docked-noborder-top',
        cls: 'customize_panel_back  bottom_margin',
//         height: 168,
        border: true,
//         layout: 'anchor',
        margin: '0 0 5 0',
        collapsible: false,
        title: '基本信息',
        items: [{
            hidden: true,
            layout: 'column',
            border: false,
            items: [funcDesc]
        }],
        tools: [
            {
                type: 'print',
                tooltip: '基本信息',
                handler: function (event, toolEl, panelHeader) {
                    if (!scName.getValue()) {
                        scName.setValue(scriptName);
                    }
                    if (!funcDescInWin.getValue()) {
                        funcDescInWin.setValue(scriptDesc);
                    }
                    //saveFromBottom = false;
                    //usePlantForm.setValue();
                    baseInfoOfScriptWin.show();
                }
            }
//            ,{
//             type:'help',
//             tooltip: '帮助',
//             callback: function(panel, tool, event) {
//             	window.open("scriptHelpDoc.do","resizable=yes").focus();
//             }
//         }
        ]
    });

    var scriptoutput = Ext.create('Ext.form.Panel', {
        region: 'south',
        hidden:!scriptFunctionOutputSwitch,
        width: '100%',
        cls: 'customize_panel_back  bottom_margin',
        height: 60,
        // border: true,
//         layout: 'anchor',
        margin: '0 0 5 0',
        collapsible: false,
        title: '输出定义',
        tools: [
            {
                type: 'print',
                tooltip: '输出定义',
                handler: function (event, toolEl, panelHeader) {
                    var height = 600;
                    var width = 800;
                    if (scriptuuid==''){
                        Ext.Msg.alert('提示', '请先保存脚本再配置输出定义');
                        return;
                    }
                    Ext.define('scriptoutputModel', {
                        extend:'Ext.data.Model',
                        fields:[{
                            name:'iorder',
                            type:'int'
                        },{
                            name:'iid',
                            type:'long'
                        },{
                            name:'ialias',
                            type:'string'
                        },{
                            name:'ivalue',
                            type:'string'
                        }]
                    })
                    var scriptOutputStore = Ext.create('Ext.data.Store', {
                        model:'scriptoutputModel',
                        proxy:{
                            url:'getScriptOutputList.do',
                            type:'ajax'
                        }
                    })

                    scriptOutputStore.on('beforeload', function(){
                        Ext.apply(scriptOutputStore.proxy.extraParams, {
                            scriptuuid : scriptuuid
                        })
                    })
                    scriptOutputStore.load();
                    var scriptOutputGrid = Ext.create('Ext.grid.Panel', {
                        region: 'center',
                        cls: 'customize_panel_back',
                        store: scriptOutputStore,
                        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
                        // ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                        padding: grid_space,
                        columnLines: true,
                        forceFit: true,
                        dockedItems:[{
                            xtype:'toolbar',
                            items:['->', {
                                xtype:'button',
                                text:'增加',
                                cls:'Common_Btn',
                                handler:function(){
                                    var items = scriptOutputStore.data.items;
                                    var maxOrder = 0;
                                    for (let i = 0; i < items.length; i++) {
                                        if (items[i].data.iorder > maxOrder){
                                            maxOrder = items[i].data.iorder
                                        }
                                    }

                                    scriptOutputStore.insert(0, Ext.create('scriptoutputModel', {
                                        iid:0,
                                        iorder:++maxOrder,
                                        ialias:'',
                                        ivalue:''
                                    }))
                                }
                            },{
                                xtype:'button',
                                text:'删除',
                                cls:'Common_Btn',
                                handler:function(){
                                    var m = scriptOutputGrid.getSelectionModel().getSelection();
                                    var ids = [];
                                    for (let i = 0; i < m.length; i++) {
                                        if (m[i].data.iid != 0){
                                            ids.push(m[i].data.iid);
                                        }else {
                                            scriptOutputStore.remove(m[i]);
                                        }
                                    }
                                    if (ids.length>0){
                                        Ext.Ajax.request({
                                            url:'delScriptOutput.do',
                                            method:'POST',
                                            params:{
                                                ids:ids
                                            },
                                            success:function (response){
                                                var data = Ext.decode(response.responseText);
                                                Ext.Msg.alert('提示', data.message);
                                                if (data.success){
                                                    scriptOutputStore.load();
                                                }
                                            },
                                            failure:function(){
                                                Ext.Msg.alert('提示', '网络连接失败');
                                            }
                                        })
                                    }
                                }
                            },{
                                xtype:'button',
                                text:'保存',
                                cls:'Common_Btn',
                                handler:function(){
                                    var m = scriptOutputStore.getModifiedRecords();
                                    if(m.length==0){
                                        Ext.Msg.alert('提示','没有数据需要保存');
                                        return;
                                    }
                                    for (let i = 0; i < m.length; i++) {
                                        if (m[i].data.ialias == ''){
                                            Ext.Msg.alert('提示', '列标题不能为空');
                                            return;
                                        }
                                        if (m[i].data.ivalue == ''){
                                            Ext.Msg.alert('提示', '列ID不能为空');
                                            return;
                                        }
                                    }
                                    var n = scriptOutputStore.getNewRecords();
                                    var newRecords = [];
                                    for (let i = 0; i < n.length; i++) {
                                        newRecords.push(n[i].data);
                                    }
                                    var o = scriptOutputStore.getUpdatedRecords();
                                    var updateRecords = [];
                                    for (let i = 0; i < o.length; i++) {
                                        updateRecords.push(o[i].data);
                                    }
                                    Ext.Ajax.request({
                                        url:'saveScriptOutput.do',
                                        method:'POST',
                                        params:{
                                            newRecords:Ext.encode(newRecords),
                                            updateRecords:Ext.encode(updateRecords),
                                            scriptuuid:scriptuuid
                                        },
                                        success:function (response){
                                            var data = Ext.decode(response.responseText);
                                            Ext.Msg.alert('提示', data.message);
                                            if (data.success){
                                                scriptOutputStore.load();
                                            }
                                        },
                                        failure:function(){
                                            Ext.Msg.alert('提示', '网络连接失败');
                                        }
                                    })
                                }
                            }]
                        }],
                        columns: [{
                            dataIndex:'iorder',
                            text:'排序',
                            editor:{
                                xtype:'numberfield',
                                step:1,
                                minValue:1,
                                maxValue:9999,
                                regex:/^[1-9][0-9]{0,3}$/,
                                regexText:'请输入1-9999之间的整数'
                            }
                        },{
                            dataIndex:'ivalue',
                            text:'列ID',
                            editor:{
                                regex:/^[^\u4e00-\u9fa5][^\u4e00-\u9fa5]*$/,
                                regexText:'不支持录入中文',
                                listeners:{
                                    blur:function (t, even, ops) {
                                        if (checkLength(t.value) > 255){
                                            Ext.Msg.alert('提示','最大输入字符长度255');
                                            Ext.getCmp(t.id).setValue('');
                                        }
                                    }
                                }
                            }
                        },{
                            dataIndex:'ialias',
                            text:'列标题',
                            editor:{
                                listeners:{
                                    blur:function (t, even, ops) {
                                        if (checkLength(t.value) > 255){
                                            Ext.Msg.alert('提示','最大输入字符长度255');
                                            Ext.getCmp(t.id).setValue('');
                                        }
                                    }
                                }
                            }
                        }],
                        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
                        height: height - 80,
                        width: '100%'
                    })
                    var scriptOutputWin = Ext.create('Ext.window.Window', {
                        modal:true,
                        height:height,
                        width:width,
                        title:'输出定义',
                        draggable: false,
                        resizable: false,
                        items:[scriptOutputGrid]
                    }).show();
                }
            }
        ]
    });




//     if (!baseInfoOfScriptWin) {
//         baseInfoOfScriptWin = Ext.create('widget.window', {
//             title: '基本信息',
//             closable: true,
//             closeAction: 'hide',
//             // tbar: [FieldContainer_win,'->'/* agentPullChosedCb,tryATrybtn */],
//             resizable: false,
//             modal: true,
//             width: contentPanel.getWidth() * 0.63,
//             height: contentPanel.getHeight() * 0.8,
//             minWidth: 350,
//             minHeight: 350,
//             layout: {
//                 type: 'border',
//                 padding: 5
//             },
//             items: [scriptForm],
//             buttonAlign: 'center',
//             listeners: {
//                 show: function () {
//                     Ext.Msg.close();
//                 }
//             },
//             buttons: [{
//                 xtype: "button",
//                 text: "保存",
//                 // id: 'saveButton1',
//                 handler: function () {
//                    Ext.Msg.wait('处理中，请稍后...', '提示');
//                     if (saveFromBottom) {
//                         if (checkRadioForBasicScriptEdit == 5) {
//                             savedoc(scriptForm.getForm());
//                             /*
// 						 * scriptForm.getForm().submit({
// 						 * url:'uploadTemplate.do', waitMsg:'文件上传中',//提示等待信息
// 						 * success:function(){
// 						 * Ext.Msg.alert("success","文件上传成功"); } });
// 						 */
//                         } else {
//                             var sysId = bussCb.getValue();
//                             var bussTypeId = bussTypeCb.getValue();
//                             var threeBussTypeId = threeBussTypeCb.getValue();
//                             var serverName = sName.getValue();
//                             var scriptName1 = scName.getValue();
//                             var up = usePlantForm.getValue() || "";
//                             var scriptDesc1 = funcDescInWin.getValue();
//                             var groupName=groupNameCombo.getValue();
//                             // console.log($($('#signDiv').children('div')[0]).children('span'))
//                             // var spanss=[];
//                             // var spans = $($('#signDiv').children('div')[0]).children('span');
//                             // for (let i = 0; i < spans.length; i++) {
//                             //     console.log($(spans[i]).text())
//                             //     spanss.push($(spans[i]).text())
//                             // }
//                             var spanss = $("#tagsinputval").val();
//                             if (1 == gdSwitch) {
//
//                                 var importfile = scriptForm.getForm().findField("scriptfile").getValue();
//                                 if (importfile != '') {
//                                     scriptForm.getForm().submit({
//                                         url: 'ajaxImportScript.do',
//                                         success: function (form, action) {
//                                             var result = Ext.JSON.decode(action.response.responseText);
//                                             var flag = result.success;// 提示信息
//                                             var msg = result.message;// 提示信息
//                                             if (flag) {
//                                                 editor.getDoc().setValue(msg);
//                                                 editor.refresh();
//                                             }
//                                         },
//                                         failure: function (form, action) {
//                                         }
//                                     });
//                                 }
//                                 var dbType2 = dbType.getValue() || "";// 数据库类型
//                                 var serviceType2 = serviceType.getValue() || "";// 服务类型
//                                 var isExam2 = isExam.getValue() || "";// 发起审核
//                                 if (!scriptName1) {
//                                     scName.setValue(scriptName);
//                                 }
//                                 if (!scriptDesc1) {
//                                     funcDescInWin.setValue(scriptDesc);
//                                 }
//                                 if (serverName.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "服务名称不能为空!");
//                                     return;
//                                 }
//                                 if (scriptName1.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "脚本名称不能为空!");
//                                     return;
//                                 }
//                                 if (!sysId && gdSwitch == 0) {
//                                     Ext.MessageBox.alert("提示", "请选择脚本分类!");
//                                     return;
//                                 }
//                                 if (!bussTypeId && gdSwitch == 0) {
//                                     Ext.MessageBox.alert("提示", "请选择操作类型!");
//                                     return;
//                                 }
//                                 if (gdSwitch != 0 && dbType2.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "数据库类型不能为空!");
//                                     return;
//                                 }
//                                 if (gdSwitch != 0 && serviceType2.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "服务类型不能为空!");
//                                     return;
//                                 }
//                                 if (gdSwitch != 0 && isExam2.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "是否发起审核不能为空!");
//                                     return;
//                                 }
//
//                                 if (up.length == 0) {
//                                     Ext.MessageBox.alert("提示", "适用平台不能为空!");
//                                     return;
//                                 }
//                                 // if (spanss.length == 0) {
//                                 //     Ext.MessageBox.alert("提示", "标签不能为空!");
//                                 //     return;
//                                 // }
//                                 if (scriptDesc1.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "功能说明不能为空!");
//                                     return;
//                                 }
//                                 if (gdSwitch != 1 && limitTwenty) {
//                                     if (scriptDesc1.trim().length < 20) {
//                                         Ext.MessageBox.alert("提示", "功能说明不能少于20字符!");
//                                         return;
//                                     }
//                                 }
//                                 save(0);
//                             } else {
//                                 if (!scriptName1) {
//                                     scName.setValue(scriptName);
//                                 }
//                                 if (!scriptDesc1) {
//                                     funcDescInWin.setValue(scriptDesc);
//                                 }
//                                 if (serverName.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "服务名称不能为空!");
//                                     return;
//                                 }
//                                 if (scriptName1.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "脚本名称不能为空!");
//                                     return;
//                                 }
//                                 if (isSu) {
//                                     if (suUser.getValue().trim() == '') {
//                                         Ext.MessageBox.alert("提示", "启动用户不能为空!");
//                                         return;
//                                     }
//                                 }
//                                 if (!(scriptForm.getForm().isValid())) {
//                                     Ext.Msg.alert('提示', '脚本名称只允许输入数字、字母、下划线!');
//                                     return;
//                                 }
//                                 if(sdFunctionSortSwitch){
//                                     if(!groupName){
//                                         Ext.MessageBox.alert("提示", "请选择功能分类!");
//                                         return;
//                                     }
//                                 }
//                                 if (!sysId) {
//                                     Ext.MessageBox.alert("提示", "请选择一级分类!");
//                                     return;
//                                 }
//                                 if (!bussTypeId) {
//                                     Ext.MessageBox.alert("提示", "请选择二级分类!");
//                                     return;
//                                 }
//
//                                 if (!threeBussTypeId && scriptThreeBstypeSwitch) {
//                                     Ext.MessageBox.alert("提示", "请选择三级分类!");
//                                     return;
//                                 }
//
//                                 if (up.length == 0) {
//                                     Ext.MessageBox.alert("提示", "请选择适用平台!");
//                                     return;
//                                 }
//                                 // if (spanss.length == 0) {
//                                 //     Ext.MessageBox.alert("提示", "标签不能为空!");
//                                 //     return;
//                                 // }
//                                 if (scriptDesc1.trim() == '') {
//                                     Ext.MessageBox.alert("提示", "功能说明不能为空!");
//                                     return;
//                                 }
//                                 if (gdSwitch != 1 && limitTwenty) {
//                                     if (scriptDesc1.trim().length < 20) {
//                                         Ext.MessageBox.alert("提示", "功能说明不能少于20字符!");
//                                         return;
//                                     }
//                                 }
//                                 kk = suUser.getValue().trim();
//                                 save(0);
//                             }
//                         }
//                     } else {
//                         if (checkRadioForBasicScriptEdit != 5) {
//                             var importfile = scriptForm.getForm().findField("scriptfile").getValue();
//                             if (importfile != '') {
//                                 scriptForm.getForm().submit({
//                                     url: 'ajaxImportScript.do',
//                                     success: function (form, action) {
//                                         var result = Ext.JSON.decode(action.response.responseText);
//                                         var flag = result.success;// 提示信息
//                                         var msg = result.message;// 提示信息
// //  									document.getElementById("code").value=msg;
//                                         if (flag) {
//                                             editor.getDoc().setValue(msg);
//                                             editor.refresh();
//                                         }
//                                     },
//                                     failure: function (form, action) {
//                                     }
//                                 });
// //	  						$.get(importfile).success(function(content){
// //	  							alert(content);
// //	  						});
//                             }
//                         }
//                         this.up("window").close();
//                     }
//                 }
//             }, {
//                 xtype: "button",
//                 text: "取消",
//                 handler: function () {
//                     this.up("window").close();
//                 }
//             }]
//         });
//     }
    Ext.define('paramManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
            {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'parameterValue',
                type: 'string'
            },
            {
                name: 'parameterDesc',
                type: 'string'
            }]
    });

    Ext.define('paramManangerModel2', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'paravalue',
                type: 'string'
            }]
    });

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'paramType',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            },
            {
                name: 'ruleName',
                type: 'string'
            },
            {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }]
    });
    Ext.define('paramRuleModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'paramRuleIn',
            type: 'string'
        }, {
            name: 'paramRuleOut',
            type: 'string'
        }, {
            name: 'paramRuleType',
            type: 'int'
        }, {
            name: 'paramRuleLen',
            type: 'int'
        }, {
            name: 'paramRuleDesc',
            type: 'string'
        }, {
            name: 'paramRuleOrder',
            type: 'int'
        }]
    });

    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    Ext.define('attaTempModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    Ext.define('tempModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var enumValueStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        // autoDestroy: true,
        // pageSize: 10,
        model: 'paramManangerModel',
        proxy: {
            type: 'ajax',
            url: 'getParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                // totalProperty: 'total'
            }
        }
    });

    var defaultValueStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'paramManangerModel2',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    defaultValueStore.on('beforeload', function (store, options) {
        var new_params = {
            paramName: golbalParamName
        };
        Ext.apply(defaultValueStore.proxy.extraParams, new_params);
    });

    var paramRulesStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramRuleModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    paramRulesStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            iflag: "0"
        };
        Ext.apply(paramRulesStore.proxy.extraParams, new_params);
    });
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    //获取所有模板
    var attaTempStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attaTempModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttaTemplate.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    attachmentStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: attachmentIds
            // ,
            // tempIds : tempmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    attachmentStore.on('load', function (me, records, successful, eOpts) {
        attachmentIds = [];
        $.each(records, function (index, record) {
            attachmentIds.push(record.get('iid'));
        });
        console.log(attachmentIds);
    });

    /********************/
    attaTempStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: tempmentIds
        };
        Ext.apply(attaTempStore.proxy.extraParams, new_params);
    });
    attaTempStore.on('load', function (me, records, successful, eOpts) {
        tempmentIds = [];
        $.each(records, function (index, record) {
            tempmentIds.push(record.get('iid'));
        });
    });
    /********************/

    var defultEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true
        })
    });
    var passwordEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true,
            inputType: 'password'
        })
    });
    var paramTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "枚举"
        }, {
            "name": "IN-string"
        },
            {
                "name": "IN-string(加密)"
            },
            {
                "name": "IN-int"
            },
            {
                "name": "IN-float"
            },
            {
                "name": "OUT-string"
            },
            {
                "name": "OUT-int"
            },
            {
                "name": "OUT-float"
            }]
    });
    Ext.define('parameterCheckModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'ruleName',
            type: 'string'
        }, {
            name: 'checkRule',
            type: 'string'
        }, {
            name: 'ruleDes',
            type: 'string'
        }]
    });
    var store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'parameterCheckModel',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterCheck.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var paramColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        }, {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 55,
            editor: {
                allowBlank: false
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    backValue = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" +  record.get('parameterName')
                    + "<br>默认值：" + backValue
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';

                return value;
            }
        },
        {
            dataIndex: 'paramType',
            width: 120,
            text: '参数类型',
            editor: {
                xtype: 'combobox',
                store: paramTypeStore,
                queryMode: 'local',
                displayField: 'name',
                valueField: 'name',
                editable: false,
                listeners: {
                    change: function (field, newValue, oldValue) {
                        if (oldValue == 'IN-string(加密)' && newValue != 'IN-string(加密)') {
                            var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                            paramDefaultValue.set("paramDefaultValue", "");
                            paramDefaultValue.set("parameterName", "");
                        }
                        if (oldValue != newValue) {
                            var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                            paramDefaultValue.set("paramDefaultValue", "");
                            paramDefaultValue.set("parameterName", "");
                            paramDefaultValue.set("ruleName", "");
                        }
                    }
                }
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                if (value == 'IN-string(加密)') {
                    coun = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    coun = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" +  record.get('parameterName')
                    + "<br>默认值：" + coun
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            dataIndex: 'parameterName',
            width: 80,
            text: '枚举名称',
            editable: false,
            editor: {
            }
        },
        {
            dataIndex: 'paramDefaultValue',
            width: 80,
            text: '默认值',
            editor: {},
            renderer: function (value, metaData, record) {
                //alert(value);
                let showValue = value;

                let paramType = record.get('paramType');

                if (paramType == 'IN-string(加密)') {
                    let xing = "";
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }

        },
        {
            dataIndex: 'ruleName',
            width: 120,
            text: '验证规则',
            hidden: !bhParameterCheckSwitch,
            editor: {
                xtype: 'combobox',
                store: store,
                queryMode: 'local',
                displayField: 'ruleName',
                valueField: 'ruleName',
                editable: false,
            }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            editor: {
                allowBlank: true
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    backValue = record.get('paramDefaultValue');
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>默认值：" + backValue
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';

                return value;
            }
        }];
    var paramRuleTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name', 'id'],
        data: [{
            "name": "VARCHAR",
            "id": 0
        }, {
            "name": "INTEGER",
            "id": 1
        }, {
            "name": "DECIMAL",
            "id": 2
        }, {
            "name": "TIMESTAMP",
            "id": 3
        }, {
            "name": "CLOB",
            "id": 4
        }, {
            "name": "DATE",
            "id": 5
        }, {
            "name": "LONG",
            "id": 6
        }]
    });

    var paramRuleCombo = Ext.create('Ext.form.field.ComboBox', {
        store: paramRuleTypeStore,
        queryMode: 'local',
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'id',
        triggerAction: "all"
    });
    var paramRulesColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '顺序',
            dataIndex: 'paramRuleOrder',
            width: 55,
            editor: {
                allowBlank: false,
                xtype: 'numberfield',
                maxValue: 30,
                minValue: 1
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode("输出列名称：" + record.get('paramRuleOut') + "<br>排序：" + record.get('paramRuleOrder') + "<br>描述：" + record.get('paramRuleDesc'))
                    + '"';
                return value;
            }
        }, {
            text: '分隔符',
            dataIndex: 'paramRuleIn',
            width: 85,
            editor: {},
            hidden: true

        }, {
            text: '输出列名称',
            dataIndex: 'paramRuleOut',
            width: 130,
            editor: {
                allowBlank: false
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode("输出列名称：" + record.get('paramRuleOut') + "<br>排序：" + record.get('paramRuleOrder') + "<br>描述：" + record.get('paramRuleDesc'))
                    + '"';
                return value;
            }
        },
        {
            text: '类型',
            dataIndex: 'paramRuleType',
            width: 85,
            editor: paramRuleCombo,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                if (value == 0) {
                    value = "VARCHAR";
                } else if (value == 1) {
                    value = "INTEGER";
                } else if (value == 2) {
                    value = "DECIMAL";
                } else if (value == 3) {
                    value = "TIMESTAMP";
                } else if (value == 4) {
                    value = "CLOB";
                } else if (value == 5) {
                    value = "DATE";
                } else if (value == 6) {
                    value = "LONG";
                } else {
                    value = "VARCHAR"
                }
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode(" 类型：" + value) + '"';
                return value;
            }
        }, {
            text: '长度',
            dataIndex: 'paramRuleLen',
            width: 85,
            value: 20,
            editor: {
                xtype: 'numberfield',
                maxValue: 4000,
                minValue: 0
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                if (record.get('paramRuleType') == 5) {
                    return '';
                } else {
                    return value;
                }
            }
        }, {
            text: '别名',
            dataIndex: 'paramRuleDesc',
            width: 85,
//				flex : 1,
            editor: {
                allowBlank: true
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode("输出列名称：" + record.get('paramRuleOut') + "<br>排序：" + record.get('paramRuleOrder') + "<br>描述：" + record.get('paramRuleDesc'))
                    + '"';
                return value;
            }
        }];

    function removeByValue(arr, val) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == val) {
                arr.splice(i, 1);
                break;
            }
        }
    }

    var attachmentColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '附件名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        /*
	 * { text: '附件大小', dataIndex: 'attachmentSize', width: 200 }, { text:
	 * '上传时间', dataIndex: 'attachmentUploadTime', flex: 1 },
	 */
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attachmentStore.getAt(rowIndex);
                    var a = [];
                    a.push(rec.get('iid'));
                    Ext.Ajax.request({
                        url: 'deleteScriptAttachment.do',
                        method: 'POST',
                        sync: true,
                        params: {
                            iids: a
                        },
                        success: function (response, request) {
                            var success = Ext.decode(response.responseText).success;
                            if (success) {
                                Ext.Msg.alert('提示', '删除成功！');
                                removeByValue(attachmentIds, rec.get('iid'));
                                attachmentStore.load();
                                if (newServiceId != 0) {
                                    save(0);
                                }
                            } else {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "保存失败！");
                        }
                    });
                }
            },
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attachmentStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttachment.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];


    /*******************************************/
    var tempColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '模板名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attaTempStore.getAt(rowIndex);
                    Ext.Msg.confirm("请确认", "是否真的要删除模板？", function (button, text) {
                        if (button == "yes") {
                            var a = [];
                            a.push(rec.get('iid'));
                            Ext.Ajax.request({
                                url: 'deleteScriptAttaTemplate.do',
                                method: 'POST',
                                sync: true,
                                params: {
                                    iids: a
                                },
                                success: function (response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    if (success) {
                                        Ext.Msg.alert('提示', '删除成功！');
                                        removeByValue(tempmentIds, rec.get('iid'));
                                        attaTempStore.load();
                                        if (newServiceId != 0) {
                                            save(0);
                                        }
                                    } else {
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                    }
                                },
                                failure: function (result, request) {
                                    secureFilterRs(result, "保存失败！");
                                }
                            });
                        }
                    });
                }
            },
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attaTempStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttaTemplate.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];
    /*******************************************/

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing3 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
//    var selModelAtta = Ext.create('Ext.selection.CheckboxModel', {
//        checkOnly: true
//    });
    var paramGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        store: paramStore,
        cls: 'window_border   panel_space_top',
        margin: '0 0 5 0',
        selModel: selModel,
        viewConfig: {
            enableTextSelection: true
        },
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns,
        emptyText: '没有脚本参数',
        tools: [{
            type: 'plus',
            tooltip: '增加',
            handler: addParam
        },
            {
                type: 'minus',
                tooltip: '删除',
                callback: function (panel, tool, event) {
                    var data = paramGrid.getView().getSelectionModel().getSelection();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的行!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要删除参数？", function (button, text) {
                            if (button == "yes") {
                                var deleteIds = [];
                                $.each(data, function (index, record) {
                                    if (record.data.iid > 0) {
                                        deleteIds.push(record.data.iid);
                                    } else {
                                        paramStore.remove(data);
                                    }
                                });
                                if (deleteIds.length > 0) {
                                    Ext.Ajax.request({
                                        url: 'deleteScriptParams.do',
                                        method: 'POST',
                                        sync: true,
                                        params: {
                                            iids: deleteIds
                                        },
                                        success: function (response, request) {
                                            var success = Ext.decode(response.responseText).success;
                                            if (success) {
                                                Ext.Msg.alert('提示', '删除成功！');
                                                paramStore.load();
                                            } else {
                                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                            }
                                        },
                                        failure: function (result, request) {
                                            secureFilterRs(result, "保存失败！");
                                        }
                                    });
                                } else {
                                    paramGrid.getView().refresh();
                                }
                            }
                        });
                    }
                }
            }],
        listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {

                var columnIndex = e.column.dataIndex;
                // 点击的当前行数据
                var recordData = e.record.data;

                var paramType = recordData.paramType;           // 是否为枚举类型
                var parameterName = recordData.parameterName;   // 参数名称

                // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue";
                var columnBooParameterName = columnIndex == "parameterName";
                var columnBooparamDefaultValue = columnIndex == "paramDefaultValue"
                // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                    golbalParamName = parameterName;
                    defaultValueStore.load();
                }
                // 判断如果为枚举类型，并且当前操作列为“参数名称”，设置单元格为下拉框
                if (paramType == "枚举" && columnBooParameterName) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "parameterName",
                        displayField: "parameterName",
                        store: enumValueStore,
                        editable: false,
                        listeners: {
                            change: function (field, newValue, oldValue) {
                                if (oldValue != newValue) {
                                    var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                                    paramDefaultValue.set("paramDefaultValue", "");
                                }
                            }
                        }
                    });
                }
                if (paramType == "枚举" && columnBooparamDefaultValue) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore,
                        editable: false
                    });
                }
                // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                if (paramType != "枚举" && columnBoo) {
                    e.column.setEditor({
                        xtype: 'textfield',
                        readOnly: columnIndex == "parameterName" ? true : false,

                    })
                }

                if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {
                    let pass = new Ext.form.TextField({
                        inputType: 'password',
                        // listeners:{
                        //     renderer:function (a,b,c,d){
                        //         alert(a);
                        //     }
                        // }
                    });

                    e.column.setEditor(pass)
                }
            }
        }
    });
    var selectedAttachmentButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加附件',
            handler: selectAttachmentFun
        });

    var selectedTemplateButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加模板',
            handler: selectTempFun
        });
    /*var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	region: 'south',
    	cls: 'attachments customize_panel_back panel_space_top',
        height: 200,
        title: '附件',
        store: attachmentStore,
// selModel: selModelAtta,
        border: true,
        viewConfig:{
        	enableTextSelection:true
        },
        columnLines: true,
        columns: attachmentColumns,
        emptyText: '没有附件',
        dockedItems : [{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items:[
				'->',selectedAttachmentButton//添加附件按钮
			]
		}]
    });*/

    var attachmentGrid1 = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: attachmentStore,
        viewConfig: {
            enableTextSelection: true
        },
        border: true,
        columnLines: true,
        margin: '0 10 5 10',
        columns: attachmentColumns,
        emptyText: '没有附件',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [
                '->', selectedAttachmentButton//添加附件按钮
            ]
        }]
    });

    var attachmentGrid2 = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: attaTempStore,
        border: true,
        columnLines: true,
        viewConfig: {
            enableTextSelection: true
        },
        columnLines: true,
        columns: tempColumns,
        margin: '0 10 5 10',
        emptyText: '没有附件',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [
                '->', selectedTemplateButton//添加附件按钮
            ]
        }]
    });

    var pagetab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            //cls:'customize_panel_back',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            region: 'center',
            activeTab: 0,
            //width : '100%',
            height: contentPanel.getHeight() * 0.25,
            border: false,
//			    autoScroll: true,
            items: [
                {
                    title: '附件',
                    layout: 'fit',
                    items: [attachmentGrid1]
                },
                {
                    title: '模板',
                    layout: 'fit',
                    hidden: !templateSwitch,
                    items: [attachmentGrid2]
                }
            ]
        });

    var attachmentGrid = Ext.create('Ext.panel.Panel', {
        region: 'south',
        cls: 'attachments customize_panel_back panel_space_top',
        margin: '0 0 5 0',
        items: [pagetab/*attachmentGrid*/]

    });


    var selModel_outruleGrid = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var outruleGrid = Ext.create('Ext.grid.Panel', {
        selModel: selModel_outruleGrid,
        region: 'south',
        height: 250,
        title: '输出规则',
        margin: '0 0 5 0',
        plugins: [cellEditing2],
        store: paramRulesStore,
        border: true,
        columnLines: true,
        viewConfig: {
            enableTextSelection: true
        },
        columns: paramRulesColumns,
        emptyText: '没有规则数据',
        tools: [{
            type: 'plus',
            tooltip: '增加',
            handler: addOut
        }, {
            type: 'minus',
            tooltip: '删除',
            callback: function (panel, tool, event) {
                var data = outruleGrid.getView().getSelectionModel().getSelection();
                if (data.length == 0) {
                    Ext.Msg.alert('提示', '请先选择您要操作的行!');
                    return;
                } else {
                    Ext.Msg.confirm("请确认", "是否真的要删除所选记录？", function (button, text) {
                        if (button == "yes") {
                            var deleteIds = [];
                            $.each(data, function (index, record) {
                                if (record.data.iid > 0) {
                                    deleteIds.push(record.data.iid);
                                } else {
                                    paramRulesStore.remove(data);
                                }
                            });
                            if (deleteIds.length > 0) {
                                Ext.Ajax.request({
                                    url: 'deleteScriptRuleParams.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                        iids: deleteIds
                                    },
                                    success: function (response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            paramRulesStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "删除失败！");
                                    }
                                });
                            } else {
                                outruleGrid.getView().refresh();
                            }
                        }
                    });
                }
            }
        }]
    });

    var itemsattach = [funcDescForm, paramGrid, scriptoutput, outruleGrid, attachmentGrid/*,pagetab*/];
    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
        region: 'east',
        border: false,
        width: 550,
        height: contentPanel.getHeight(),
        layout: 'border',
//        tbar: ['->',{
//            text: '帮助文档',
//            cls: 'Common_Btn',
//            handler: function(){
//            	window.open("scriptHelpDoc.do","resizable=yes").focus();
//            }
//        },
//        {
//            text: '基本信息',
//            cls: 'Common_Btn',
//            handler: function(){
//            	if(!scName.getValue()) {
//            		scName.setValue(scriptName);
//            	}
//                if(checkRadioForBasicScriptEdit==5){
//                	saveFromBottom = true;
//                }else {
//                	saveFromBottom = false;
//                }
//            	baseInfoOfScriptWin.show();
//            }
//        }],
        items: itemsattach
    });

    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        }, {
            name: 'name',
            type: 'string'
        }, {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    resourceGroupStore.on('load', function () {
        var ins_rec = Ext.create('resourceGroupModel', {
            id: '-1',
            name: '未分组',
            description: ''
        });
        resourceGroupStore.insert(0, ins_rec);
    });
    var resourceGroupObj = Ext.create('Ext.form.field.ComboBox',
        {
            fieldLabel: '资源组',
            emptyText: '--请选择资源组--',
            labelAlign: 'right',
            labelWidth: 70,
            hidden: removeAgentSwitch,
            width: '25.5%',
            columnWidth: 1,
            multiSelect: true,
            store: resourceGroupStore,
            displayField: 'name',
            valueField: 'id',
            triggerAction: 'all',
            queryMode: 'local',
            editable: true,
            mode: 'local',
            listeners: {
                change: function (comb, newValue, oldValue, eOpts) {
                    /*
								 * chosedResGroups_forest = new Array(); for(var
								 * i=0;i<newValue.length;i++) {
								 * chosedResGroups_forest.push(newValue[i]); }
								 */
                    // agent_store.load();
                    pageBar.moveFirst();
                }
            }
        });

    var app_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'appNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentAppNameList.do?envType=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var app_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
        name: 'appname',
        fieldLabel: "应用名称",
        emptyText: '--请选择应用名称--',
        store: app_name_store,
        queryMode: 'local',
        hidden: !CMDBflag,
        width: "25%",
        displayField: 'appName',
        valueField: 'appName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var sys_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'sysNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentSysNameList.do?envType=0&switchFlag=' + gdSwitch,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var sys_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
        name: 'sysname',
        fieldLabel: "名称",
        emptyText: '--请选择名称--',
        store: sys_name_store,
        hidden: !CMDBflag,
        queryMode: 'local',
        width: "25%",
        displayField: 'sysName',
        valueField: 'sysName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '计算机名',
        displayField: 'hostname',
        emptyText: '--请输入计算机名--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%'
    });

    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '操作系统',
        displayField: 'ostype',
        emptyText: '--请输入操作系统--',
        labelWidth: 70,
        labelAlign: 'right',
        width: CMDBflag ? '25%' : '24.2%'
    });

    var agent_ip = new Ext.form.TextField({
        name: 'agentIp',
        fieldLabel: 'Agent IP',
        displayField: 'agentIp',
        emptyText: '--请输入Agent IP--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.5%'
    });


    var agentStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "0", "name": "正常"},
            {"id": "1", "name": "异常"},
            {"id": "2", "name": "升级中"}
        ]
    });

    var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentStatus',
        labelWidth: 79,
        queryMode: 'local',
        fieldLabel: 'Agent状态',
        closeAction: 'destroy',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择Agent状态--',
        store: agentStatusStore,
        width: '25.2%',
        labelAlign: 'right'
    });

    var execUserForTry = new Ext.form.TextField({
        name: 'execUserForTry',
//    	id:'execUserForTry',
        fieldLabel: '执行用户',
        value: kk,
        closeAction: 'destroy',
        emptyText: '-请输入执行用户-',
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%'
    });

    var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        bodyCls: 'x-docked-noborder-top',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        iqueryFun: function () {
            pageBar.moveFirst();
        },
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [sys_name, app_name, host_name, os_type
            ]
        },
            {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: [agent_ip, resourceGroupObj, agentStatusCb,
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '查询',
                        handler: function () {
                            pageBar.moveFirst();
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            agent_ip.setValue('');
                            app_name.setValue('');
                            sys_name.setValue('');
                            host_name.setValue('');
                            os_type.setValue('');
                            resourceGroupObj.setValue('');
                            agentStatusCb.setValue('');
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '查看已选择服务器',
                        hidden: true,
                        handler: function () {
                            if (!chosedAgentWin) {
                                chosedAgentWin = Ext.create('Ext.window.Window', {
                                    title: '已选择服务器',
                                    autoScroll: true,
                                    modal: true,
                                    resizable: false,
                                    closeAction: 'hide',
                                    width: contentPanel.getWidth() - 250,
                                    height: 530,
                                    items: [agent_grid_chosed],
                                    buttonAlign: 'center',
                                    buttons: [{
                                        xtype: "button",
                                        text: "关闭",
                                        handler: function () {
                                            this.up("window").close();
                                        }
                                    }]
                                });
                            }
                            chosedAgentWin.show();
                            agent_store_chosed.load();
                        }
                    }
                ]
            }, {
                xtype: 'toolbar',
                dock: 'top',
                items: [execUserForTry
                ]
            }]
    });

//	var search_form = Ext.create('Ext.form.Panel', {
//    	layout : 'anchor',
//    	height : 60,
//    	region : 'north',
//    	buttonAlign : 'center',
//    	border : false,
//	    items: [{
//	    	layout:'form',
//	    	anchor:'95%',
//	    	padding : '5 0 5 0',
//	    	border : false,
//	    	items: [{
//	    		layout:'column',
//		    	border : false,
//	    		items:[resourceGroupObj,{
//		            fieldLabel: 'IP',
//		            labelAlign : 'right',
//		            labelWidth : 70,
//		            margin : '0 10 0 10',
//		            name: 'agentIp',
//		            columnWidth:.45,
//		            xtype: 'textfield'
//		        },{
//		            fieldLabel: 'Agent描述',
//		            labelAlign : 'right',
//		            labelWidth : 70,
//		            hidden: true,
//		            margin : '0 10 0 10',
//		            name: 'agentDesc',
//		            columnWidth:.45,
//		            xtype: 'textfield'
//		        },{
//					xtype: 'button',
//// cls:'Common_Btn',
//					margin: '0 5 0 0',
//					text : '查询',
//					handler : function() {
//						pageBar.moveFirst();
//					}
//				},{
//					xtype: 'button',
//// cls:'Common_Btn',
//					margin: '0 5 0 0',
//					text : '清空',
//					handler : function() {
//						clearQueryWhere();
//					}
//				}]
//	    	}]
//	    }]
//	});

    if (gdSwitch == 1) {
        attachmentGrid.hide();
        checkRadioForBasicScriptEdit = 4;
        chooseSqlExecModel.setValue('2');
        outruleGrid.show();
    }

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'appName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentState', type: 'int'}
        ]
    });
    Ext.define('dsModel', {
        extend: 'Ext.data.Model',
        idProperty: 'dsId',
        fields: [
            {name: 'dsId', type: 'int'},
            {name: 'dsIp', type: 'String'},
            {name: 'dsName', type: 'String'},
            {name: 'dsUser', type: 'String'},
            {name: 'dsPwd', type: 'String'},
            {name: 'dsRole', type: 'String'},
            {name: 'dsIns', type: 'String'},
            {name: 'dsUrl', type: 'String'},
            {name: 'dsType', type: 'String'}
        ]
    });

    var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var dsinfo_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'dsModel',
        proxy: {
            type: 'ajax',
            url: 'getDsInfoByDsId.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    function openExecUserConfigData(record) {
        if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
            if (isSumpAgentSwitch == true) {
                var sumpAgentStore = Ext.create('Ext.data.Store', {
                    fields: ['iid', 'userName'],
                    autoLoad: true,
                    proxy: {
                        type: 'ajax',
                        url: 'getSumpAgentUserList.do',
                        reader: {
                            type: 'json',
                            root: 'dataList'
                        }
                    }
                });

                sumpAgentStore.on('beforeload', function (store, options) {
                    var queryparams = {
                        agentId: record.get('iid')
                    };
                    Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
                });

                execUserNameText = Ext.create('Ext.form.field.ComboBox', {
                    name: 'execUserName',
                    labelWidth: 65,
                    queryMode: 'local',
                    fieldLabel: '执行用户',
                    width: 320,
                    displayField: 'userName',
                    valueField: 'iid',
                    editable: true,
                    typeAhead: true,
                    emptyText: '--请选择执行用户--',
                    store: sumpAgentStore,
                    labelAlign: 'right'
                });
                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var sumpAgentCount = sumpAgentStore.getRange();
                    var newExecUserName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (sumpAgentCount.length > 0) {
                        if (undefined == newExecUserName) {
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                        }
                    } else {
                        if (undefined == newExecUserName) {
                            execUserNameText.setValue(record.get('execuser'));
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                            execUserNameText.setRawValue(newExecUserName);
                        }
                    }
                }
            } else {
                execUserNameText = Ext.create('Ext.form.TextField',
                    {
                        fieldLabel: '执行用户',
                        labelAlign: 'right',
                        name: "execUserName",
                        labelWidth: 65,
                        emptyText: '--请输入执行用户--',
                        width: 320,
                        xtype: 'textfield'
                    });

                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var newExecUserName1 = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (undefined == newExecUserName1) {
                        execUserNameText.setValue(record.get('execuser'));
                    } else {
                        execUserNameText.setValue(newExecUserName1);
                    }
                }
            }

            execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel', {
                region: 'north',
                layout: 'anchor',
                //iqueryFun : queryBtnFun,
                buttonAlign: 'right',
                baseCls: 'customize_gray_back',
                collapsible: false,//可收缩
                collapsed: false,//默认收缩
                border: false,
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    baseCls: 'customize_gray_back',
                    items: [execUserNameText]
                }, {
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    baseCls: 'customize_gray_back',
                    items: ['->', {
                        text: '确定',
                        cls: 'Common_Btn',
                        icon: '',
                        handler: function () {
                            chosedExecUser(record);
                        }
                    }]
                }]
            });

            var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
                layout: 'border',
                width: "100%",
                height: "100%",
                border: false,
                items: [execUserConfigForm],
                cls: 'customize_panel_bak'
            });

            execUserConfigWindow = Ext.create('Ext.window.Window', {
                title: "配置执行用户",
                modal: true,
                closeAction: 'destroy',
                constrain: true,
                autoScroll: false,
                //upperWin : errorTaskWin,
                width: 380,
                height: 200,
                draggable: false,// 禁止拖动
                resizable: false,// 禁止缩放
                layout: 'fit',
                items: [execUserConfig_mainPanel]
            });
        }
        execUserConfigWindow.show();
    }

//    var agent_columns = [{ text: '主键',  dataIndex: 'iid',hidden:true},
//        { text: 'IP',  dataIndex: 'agentIp',flex:1},
//        { text: '端口号',  dataIndex: 'agentPort',width:180},
//        { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden:true},
//        { text: '状态',  dataIndex: 'agentState',width:180,renderer:function(value,p,record){
//        	var backValue = "";
//        	if(value==0){
//        		backValue = "Agent正常";
//        	}else if(value==1){
//        		backValue = "Agent异常";
//        	}
//        	return backValue;
//        }}];
    var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', width: 70},
        {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
        {text: '计算机名', dataIndex: 'hostName', width: 120},
        {text: 'IP', dataIndex: 'agentIp', width: 90},
        {text: '端口号', dataIndex: 'agentPort', width: 100},
        {text: '操作系统', dataIndex: 'osType', width: 120},
        {
            text: '描述', dataIndex: 'agentDesc', flex: 1, hidden: true,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '状态', dataIndex: 'agentState', flex: 1, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        },
        {
            text: '执行用户',
            dataIndex: 'execuser',
            width: 90,
            align: 'left',//整体左对齐
            renderer: function (value, metaData, record, rowNum) {
                var displayValue = value;
                var recordedData = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                if (undefined == recordedData) {
                    if ("" == value || undefined == value) {
                        displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                    } else {
                        displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + displayValue + "</a>";
                    }
                } else {
                    if ("" == recordedData) {
                        displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                    } else {
                        displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + recordedData + "</a>";
                    }
                }
                return displayValue;
            },
            listeners: {
                click: function (a, b, c, d, e, record) {
                    openExecUserConfigData(record);
                }
            }
        }
    ];
    var ds_columns = [
        {text: '主键', dataIndex: 'dsId', hidden: true},
        {text: 'DSIP', dataIndex: 'dsIp', hidden: true},
        {text: '数据源名称', dataIndex: 'dsName', width: 100},
        {text: '用户', dataIndex: 'dsUser', width: 90},
        {text: '密码', dataIndex: 'dsPwd', hidden: true},
        {text: '角色', dataIndex: 'dsRole', hidden: true},
        {text: '实例名', dataIndex: 'dsIns', hidden: true},
        {text: '类型', dataIndex: 'dsType', width: 50},
        {text: 'URL', dataIndex: 'dsUrl', flex: 1}
    ];

    agent_store.on('beforeload', function (store, options) {
        var new_params = {
            agentIp: Ext.util.Format.trim(agent_ip.getValue()),
            appName: app_name.getValue() == null ? '' : Ext.util.Format.trim(app_name.getValue() + ""),
            sysName: sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + ""),
            hostName: Ext.util.Format.trim(host_name.getValue()),
            osType: Ext.util.Format.trim(os_type.getValue()),
            rgIds: resourceGroupObj.getValue(),
            agentState: agentStatusCb.getValue(),
            flag: 0,
            switchFlag: gdSwitch
        };

        Ext.apply(agent_store.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agent_store,
        dock: 'bottom',
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border: false,
        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
        emptyMsg: "没有记录"
    });

    var agent_grid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        store: agent_store,
        border: true,
        columnLines: true,
        viewConfig: {
            enableTextSelection: true
        },
        columns: agent_columns,
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        bbar: pageBar,
        // selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        listeners: {
            select: function (flwogird, record, item, index, e, eOpts) {
                if (checkRadioForBasicScriptEdit == 4) {
                    var cpid = record.get('iid');
                    dsinfo_store.reload({
                        params: {cpid: cpid, switchFlag: gdSwitch}
                    });

                }
            }
        }
    });
    agent_store.on('load', function (store, options) {
        agent_grid.getSelectionModel().select(0);
    });
    var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        mode: "SINGLE"
    });
    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
        store: dsinfo_store,
        width: '40%',
        region: 'east',
        viewConfig: {
            enableTextSelection: true
        },
        border: true,
        cls: 'window_border panel_space_top  panel_space_right',
        columnLines: true,
        columns: ds_columns,
        selModel: selModel2
    });
    dsinfo_store.on('load', function (store, options) {
        db_soucre_grid.getSelectionModel().select(0);
    });

    var defultEditor1 = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true
        })
    });
    var passwordEditor1 = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true,
            inputType: 'password'
        })
    });
    var testparamColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 60,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramDefaultValue',
            width: 100,
            text: '参数值',
            getEditor: function (record) {
                if (record.get('paramType') != 'IN-string(加密)') {
                    return defultEditor1;
                } else {
                    return passwordEditor1;
                }
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(value);
                } else {
                    backValue = value;
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';

                return backValue;
            }
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 70,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }];
    var testparamStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var testparamGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        store: testparamStore,
        viewConfig: {
            enableTextSelection: true
        },
        plugins: [cellEditing3],
        border: false,
        columnLines: true,
//        collapsible : true,
//        collapsed: true,
        //height:'30%',
        columns: testparamColumns
    });

    testparamStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid
        };

        Ext.apply(testparamStore.proxy.extraParams, new_params);
    });
    var paramPanel = Ext.create('Ext.panel.Panel', {
        region: 'north',
        border: true,
        layout: 'border',
        cls: 'window_border panel_space_left panel_space_right',
        height: contentPanel.getHeight() - 510,
        items: [testparamGrid]
    });
    if (chooseTestAgentWin) {
        chooseTestAgentWin.show();
    } else {
        var grid_panel = Ext.create('Ext.panel.Panel', {
            width: contentPanel.getWidth() - 252,
            height: contentPanel.getHeight() - 80,
            layout: 'border',
            region: 'center',
            header: false,
            border: false,
            items: [search_form, agent_grid, db_soucre_grid]
        });
        var fPanel = Ext.create('Ext.panel.Panel', {
            border: true,
            layout: 'border',
            width: contentPanel.getWidth() - 205,
            height: contentPanel.getHeight() - 150,
            items: [paramPanel, grid_panel]
        });
        chooseTestAgentWin = Ext.create('Ext.window.Window', {
            title: '选择测试服务器',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'hide',
            width: contentPanel.getWidth() - 200,
            height: contentPanel.getHeight() - 50,
            items: [fPanel],
            buttons: [{
                xtype: "button",
                // cls:'Common_Btn',
                text: "确定",
                handler: function () {
                    var type = "sh";
                    if (checkRadioForBasicScriptEdit == '0') {
                        type = "sh";
                    } else if (checkRadioForBasicScriptEdit == '1') {
                        type = "bat";
                    } else if (checkRadioForBasicScriptEdit == '2') {
                        type = "perl";
                    } else if (checkRadioForBasicScriptEdit == '3') {
                        type = "py";
                    } else if (checkRadioForBasicScriptEdit == '4') {
                        type = "sql";
                    } else if (checkRadioForBasicScriptEdit == '6') {
                        type = "ps1";
                    }
                    var me = this;
                    var records = agent_grid.getSelectionModel().getSelection();
                    var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
                    if (records.length != 1) {
                        Ext.Msg.alert('提示', '请选择记录，并且只能选择一条记录！');
                        return;
                    }
                    if (checkRadioForBasicScriptEdit == 4) {
                        if (dsrecords.length != 1) {
                            Ext.Msg.alert('提示', '请选择数据源，并且只能选择一个！');
                            return;
                        }
                    }
                    var rulejsonData = "[";
                    if (gdSwitch == 1) {
                        var patt1 = new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
                        var mm = paramRulesStore.getRange();
                        var outArr = {};
                        var arrContain = [];
                        for (var i = 0, len = mm.length; i < len; i++) {
                            //var paramRuleIn = mm[i].get("paramRuleIn") ? mm[i].get("paramRuleIn").trim() : '';
                            var paramRuleType = mm[i].get("paramRuleType");
                            var paramRuleLen = mm[i].get("paramRuleLen");
                            var paramRuleOrder = mm[i].get("paramRuleOrder") ? mm[i].get("paramRuleOrder") : '';
                            var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
                            var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
                            paramRuleOut = paramRuleOut.toUpperCase();
                            if ("" == paramRuleOut) {
                                setMessage('输出规则输出不能为空！');
                                return;
                            }
                            if ("GROUP" == paramRuleOut || "IID" == paramRuleOut || "IDBID_BULID_IN" == paramRuleOut || "ITIME_BULID_IN" == paramRuleOut || "ISERVICEID_BULID_IN" == paramRuleOut || "ISTARTTIME_BULID_IN" == paramRuleOut || "IDBCOPY_BULID_IN" == paramRuleOut || "ITRIMODEL_BULID_IN" == paramRuleOut || "IFLAG_BULID_IN" == paramRuleOut || "IFLOWID_BULID_IN" == paramRuleOut) {
                                setMessage('部分输出规则已经内置,请重新填写！“' + paramRuleOut + "”");
                                return;
                            }
                            if (arrContain.contains(paramRuleOut) > 0) {
                                setMessage('输出规则不能重复！');
                                return;
                            } else {
                                arrContain.push(paramRuleOut)
                            }
                            if (!patt1.test(paramRuleOut)) {
                                setMessage('请正确填写输出规则名称！');
                                return;
                            }
                            if (paramRuleType == 6) {
                                setMessage('分区表不支持LONG类型列！');
                                return;
                            }
                            if (paramRuleLen == 0 && paramRuleType != 5 /*&& paramRuleType!=6*/) {
                                setMessage('长度不能为0字符！');
                                return;
                            } else {
                                if (paramRuleType == 2) {
                                    if (paramRuleLen > 38) {
                                        setMessage('DECIMAL类型的长度最大为38!');
                                        return;
                                    }
                                }
                            }
                            if (fucCheckLength(paramRuleDesc) > 250) {
                                setMessage('输出规则描述不能超过250字符！');
                                return;
                            }
                            if (outArr[paramRuleOrder]) {
                                setMessage('输出规则序号不能重复！');
                                return;
                            } else {
                                outArr[paramRuleOrder] = true;
                            }
                            var ss = Ext.JSON.encode(mm[i].data);
                            if (i == 0)
                                rulejsonData = rulejsonData + ss;
                            else
                                rulejsonData = rulejsonData + "," + ss;
                        }
                    }
                    rulejsonData = rulejsonData + "]";
                    if (whichButtonIsClicked == 1) { // 尝试一下


                        Ext.Ajax.request({
                            url: 'tryATry.do',
                            method: 'POST',
                            sync: true,
                            params: {
                                scriptName: scriptName,
                                scriptType: type,
                                scriptContent: document.getElementById('code').value,
                                scriptPara: scriptPara,
                                scriptAttachmentIds: attachmentIds,
                                agentId: records[0].get('iid'),
                                rulejsonData: rulejsonData,
                                switchFlag: gdSwitch
                            },
                            success: function (response, request) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if (success) {
                                    isFromTryATry = 1;
                                    tryRequestId = Ext.decode(response.responseText).requestId;
                                    tryAgentIp = Ext.decode(response.responseText).agentIp;
                                    tryAgentPort = Ext.decode(response.responseText).agentPort;

                                    if (refreshTryForBasic) {
                                        clearInterval(refreshTryForBasic);
                                    }

                                    refreshTryForBasic = setInterval(function () {
                                        loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                    }, 5 * 1000);

                                    me.up("window").close();
                                    settime(tryATrybtn, '尝试');

                                }
                                Ext.Msg.alert('提示', message);

                            },
                            failure: function (result, request) {
                                secureFilterRs(result, "出现错误！");
                            }
                        });
                    } else if (whichButtonIsClicked == 2) {
                        var i = records[0].data;
                        var jsonData = "[" + Ext.JSON.encode(records[0].data) + "]";
                        var dsrecords2 = db_soucre_grid.getSelectionModel().getSelection();
                        var dsid = 0;
                        if (dsrecords2.length > 0) {
                            dsid = Ext.JSON.encode(dsrecords2[0].data.dsId);
                        }
                        var scriptPara = getTestParams();//获取测试参数
                        var m = testparamStore.getRange(0, paramStore.getCount() - 1);
                        var jsonDataPara = "[";
                        for (var ii = 0, len2 = m.length; ii < len2; ii++) {
                            var ss2 = Ext.JSON.encode(m[ii].data);
                            if (ii == 0) jsonDataPara = jsonDataPara + ss2;
                            else jsonDataPara = jsonDataPara + "," + ss2;
                        }
                        jsonDataPara = jsonDataPara + "]";

                        var userName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + records[0].get('iid'));

                        Ext.Ajax.request({
                            url: 'execScriptServiceForSync.do',
                            method: 'POST',
                            params: {
                                serviceId: newServiceId,
//			      				execUser: execUserForTry.getValue(),
                                execUser: (userName == '' || userName == null) ? execUserForTry.getValue() : userName,
                                scriptPara: scriptPara,
                                jsonDataPara: jsonDataPara,
                                jsonData: jsonData,
                                dbsourceid: dsid,
                                ifrom: 0,
                                flag: 0,
                                gdSwitch: gdSwitch
                            },
                            success: function (response, request) {
// var coatId = Ext.decode(response.responseText).coatId;
// var flowId = Ext.decode(response.responseText).flowId;
                                var requestId = Ext.decode(response.responseText).requestIds[0];
                                isFromTryATry = 0;
                                tryRequestId = requestId;
                                tryAgentIp = records[0].get('agentIp');
                                tryAgentPort = records[0].get('agentPort');

                                if (refreshTryForBasic) {
                                    clearInterval(refreshTryForBasic);
                                }

                                refreshTryForBasic = setInterval(function () {
                                    loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                }, 5 * 1000);
                                if (gdSwitch != 1 && script_showGridSwitch) {
                                    consolePanel.setTitle('执行日志&nbsp;&nbsp;-> &nbsp;&nbsp;<a href="gotoOperResultData.do?operId=' + requestId + '"  target="_Blank">表格展示...</a>');
                                }
                                me.up("window").close();
                                $('#consoleLog').html('');
                                settime(tryTestbtn, '测试');
                                Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
                                destroyRubbish();
                            },
                            failure: function (result, request) {
                                Ext.Msg.alert('提示', '执行失败！');
                                $this.html('执行脚本');
                            }
                        });
                    } else if (whichButtonIsClicked == 3) {
                        agentChosedId = records[0].get('iid');
                        var agentChosedIp = records[0].get('agentIp');
                        var agentChosedPort = records[0].get('agentPort');
                        $('#c-a-i').html('已选服务器 ' + agentChosedIp + ':' + agentChosedPort);
                        Ext.Ajax.request({
                            url: 'getAgentOs.do',
                            method: 'POST',
                            params: {
                                agentIp: agentChosedIp,
                                agentPort: agentChosedPort
                            },
                            success: function (response, request) {
                                var agentOs = Ext.decode(response.responseText).agentOs;
                                $('#c-a-i').html($('#c-a-i').html() + " " + agentOs);
                            },
                            failure: function (result, request) {
                            }
                        });

                        me.up("window").close();
                    }
                }
            }, {
                xtype: "button",
// cls:'Gray_button',
                text: "取消",
                handler: function () {
                    this.up("window").close();
                }
            }]
        });
    }

    var attachmentUploadWin = null;

    function selectAttachmentFun() {
        var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_idbasic',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash","text/plain", "audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        const LENGTH = arrs.length; 
                        for (var i = 0; i < files.length; i++) {
                        	var fileName=files[i].name;
                        	if(LENGTH>0 && arrs[0]!==''){
                        		var suffix = fileName.match(/[^.]+$/)[0];
    	                        //初始化result状态，只要能找到匹配的则修改为true
    	                        let result = false;
	                        	for (var j=0; j < LENGTH; j++) {
	                        	  if (arrs[j] === suffix) {
	                        	    result = true;
	                        	    break;
	                        	  }
	                        	}
                            	if(!result){
                            		 Ext.Msg.alert('提示', "所选文件扩展名不正确，支持扩展名为 （"+arrs+"）类型文件");
                            		 Ext.getCmp('attachment_idbasic').setRawValue("");    //files为组件的id
                                     return;
                            	}
                        	}
                            str += fileName;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_idbasic').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        attachmentUploadWin = Ext.create('Ext.window.Window', {
            title: '附件信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 200,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            },
            /*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout: 'fit'
        });

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        /** 自定义遮罩效果* */
        var myUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploadScriptAttachmentFile.do',
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            attachmentIds.push.apply(attachmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        attachmentUploadWin.close();
                        myUploadMask.hide();
                        attachmentStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        myUploadMask.hide();
                    }
                });
            }
        }

        attachmentUploadWin.show();
    }

    /***********************************/
    var tempUploadWin = null;

    function selectTempFun() {
        var uploadTempForm;
        uploadTempForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_idbasic',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash", "audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        for (var i = 0; i < files.length; i++) {
                            str += files[i].name;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_idbasic').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclTempData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        tempUploadWin = Ext.create('Ext.window.Window', {
            title: '模板信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 200,
            items: [uploadTempForm],
            listeners: {
                close: function (g, opt) {
                    uploadTempForm.destroy();
                }
            },
            /*
             * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
             */layout: 'fit'
        });

        function upExeclTempData() {
            var form = uploadTempForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplateSec(form);
        }

        /** 自定义遮罩效果* */
        var tempUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplateSec(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploadScriptAttaTemplate.do',
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            tempmentIds.push.apply(tempmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        tempUploadWin.close();
                        tempUploadMask.hide();
                        attaTempStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        tempUploadMask.hide();
                    }
                });
            }
        }

        tempUploadWin.show();
    }

    /**************************************/

    function clearQueryWhere() {
        resourceGroupObj.setValue('');
        search_form.getForm().findField("agentIp").setValue('');
        search_form.getForm().findField("agentDesc").setValue('');
    }

    function tryATry() {

        editor.save();
        outruleGrid.getStore().removeAll();
        if (gdSwitch != 1 && checkRadioForBasicScriptEdit == '4') {
            Ext.Msg.alert('提示', 'sql类型脚本暂不支持尝试功能！');
            return;
        }
        var content = document.getElementById('code').value;
        if (!content) {
            Ext.Msg.alert('提示', '请填入脚本内容！');
            return;
        }

        var agentId = agentPullChosedCb.getValue();
        if (!agentId || agentId == '') {
            Ext.Msg.alert('提示', '请选择服务器！');
            return;
        }

        function tryReal() {
            if (paramInvalidMessage) {
                Ext.MessageBox.alert("提示", paramInvalidMessage);
            } else {
                whichButtonIsClicked = 1;
                var scriptPara = getParams();
                var type = "sh";
                if (checkRadioForBasicScriptEdit == '0') {
                    type = "sh";
                } else if (checkRadioForBasicScriptEdit == '1') {
                    type = "bat";
                } else if (checkRadioForBasicScriptEdit == '2') {
                    type = "perl";
                } else if (checkRadioForBasicScriptEdit == '3') {
                    type = "py";
                } else if (checkRadioForBasicScriptEdit == '4') {
                    type = "sql";
                } else if (checkRadioForBasicScriptEdit == '6') {
                    type = "ps1";
                }
                var tryStoreDbaas = '0';
                var rulejsonData = "[";
                if (checkRadioForBasicScriptEdit == '4' && chooseSqlExecModel.getValue() == '2') {
                    tryStoreDbaas = '1';
                    var patt1 = new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
                    var mm = paramRulesStore.getRange();
                    var outArr = {};
                    var arrContain = [];
                    for (var i = 0, len = mm.length; i < len; i++) {
                        var n = 0;
                        //var paramRuleIn = mm[i].get("paramRuleIn") ? mm[i].get("paramRuleIn").trim() : '';
                        var paramRuleOrder = mm[i].get("paramRuleOrder") ? mm[i].get("paramRuleOrder") : '';
                        var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
                        var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
                        var paramRuleType = mm[i].get("paramRuleType");
                        var paramRuleLen = mm[i].get("paramRuleLen");
                        paramRuleOut = paramRuleOut.toUpperCase();
                        if ("" == paramRuleOut) {
                            setMessage('输出规则输出不能为空！');
                            return;
                        }
                        if ("GROUP" == paramRuleOut || "IID" == paramRuleOut || "IDBID_BULID_IN" == paramRuleOut || "ITIME_BULID_IN" == paramRuleOut || "ISERVICEID_BULID_IN" == paramRuleOut || "ISTARTTIME_BULID_IN" == paramRuleOut || "IDBCOPY_BULID_IN" == paramRuleOut || "ITRIMODEL_BULID_IN" == paramRuleOut || "IFLAG_BULID_IN" == paramRuleOut || "IFLOWID_BULID_IN" == paramRuleOut) {
                            setMessage('部分输出规则已经内置,请重新填写！' + "“" + paramRuleOut + "”");
                            return;
                        }
                        if (arrContain.contains(paramRuleOut) > 0) {
                            setMessage('输出规则不能重复！');
                            return;
                        } else {
                            arrContain.push(paramRuleOut)
                        }
                        if (!patt1.test(paramRuleOut)) {
                            setMessage('请正确填写输出规则！');
                            return;
                        }
                        if (paramRuleType == 6) {
                            setMessage('分区表不支持LONG类型列！');
                            return;
                        }
                        if (paramRuleLen == 0 && paramRuleType != 5 /*&& paramRuleType!=6*/) {
                            setMessage('长度不能为0字符！');
                            return;
                        } else {
                            if (paramRuleType == 2) {
                                if (paramRuleLen > 38) {
                                    setMessage('DECIMAL类型的长度最大为38!');
                                    return;
                                }
                            }
                        }
                        if (fucCheckLength(paramRuleDesc) > 250) {
                            setMessage('输出规则描述不能超过250字符！');
                            return;
                        }
                        if (outArr[paramRuleOrder]) {
                            setMessage('输出规则序号不能重复！');
                            return;
                        } else {
                            outArr[paramRuleOrder] = true;
                        }
                        var ss = Ext.JSON.encode(mm[i].data);
                        if (i == 0)
                            rulejsonData = rulejsonData + ss;
                        else
                            rulejsonData = rulejsonData + "," + ss;
                    }
                }
                rulejsonData = rulejsonData + "]";

                var url = "tryATry.do";
                if (gdSwitch == 1) {
                    url = "tryAResource.do"
                }

                Ext.MessageBox.wait("数据处理中...", "进度条");
                Ext.Ajax.request({
                    url: url,
                    method: 'POST',
                    sync: true,
                    timeout: 6000000000,
                    params: {
                        scriptName: 'script',
                        scriptType: type,
                        scriptContent: document.getElementById('code').value,
                        scriptPara: scriptPara,
                        scriptAttachmentIds: attachmentIds,
                        agentId: agentId,
                        jdbcExec: gdSwitch,
                        rulejsonData: rulejsonData,
                        switchFlag: gdSwitch,
                        sqlexecModel: chooseSqlExecModel.getValue(),
                        tryStoreDbaas: tryStoreDbaas
                    },
                    success: function (response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        var content = Ext.decode(response.responseText).content;
                        //自动添加输出规则
                        if (gdSwitch == 1 && checkRadioForBasicScriptEdit == '4') {
                            outruleGrid.store.removeAll();
                            var columns = Ext.decode(response.responseText).column;
                            var column = columns.split(",");
                            var store = outruleGrid.getStore();
                            var ro = store.getCount();
                            var p;
                            if (columns != '') {
                                console.log(ro, column)
                                if (ro == 0) {
                                    for (var i = 0; i < column.length; i++) {
                                        var datas = column[i].split(":");
                                        p = {
                                            iid: '',
                                            paramRuleOrder: ro + 1,
                                            paramRuleIn: '',
                                            paramRuleOut: datas[0],
                                            paramRuleType: datas[1],
                                            paramRuleLen: datas[2],
                                            paramRuleDesc: ''
                                        };
                                        ro++;
                                        store.insert(i, p);
                                    }
                                    outruleGrid.getView().refresh();
                                }
                            }
                        }
                        if (success) {
                            if (gdSwitch == 1) {
                                $('#consoleLog').html(content);
                                consolePanel.body.scroll('bottom', 300000);
                                tryRequestId = Ext.decode(response.responseText).requestId;
                                tryAgentIp = Ext.decode(response.responseText).agentIp;
                                tryAgentPort = Ext.decode(response.responseText).agentPort;
                            } else {
                                isFromTryATry = 1;
                                tryRequestId = Ext.decode(response.responseText).requestId;
                                tryAgentIp = Ext.decode(response.responseText).agentIp;
                                tryAgentPort = Ext.decode(response.responseText).agentPort;

                                if (refreshTryForBasic) {
                                    clearInterval(refreshTryForBasic);
                                }

                                refreshTryForBasic = setInterval(function () {
                                    loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                }, 5 * 1000);

                                settime(tryATrybtn, '尝试');
                            }
                        } else {
                            if (gdSwitch == 1) {
                                $('#consoleLog').html(content);
                            }
                        }
                        Ext.Msg.alert('提示', message);

                    },
                    failure: function (result, request) {
                        if (gdSwitch == 1) {
                            $('#consoleLog').html("获取信息失败");
                        }
                        secureFilterRs(result, "出现错误！");
                    }
                });
            }
        }

        var p = orgParams();
        var paramInvalidMessage = p.paramInvalidMessage;
        var someParamIsEmpty = p.someParamIsEmpty;
        if (someParamIsEmpty) {
            Ext.MessageBox.buttonText.yes = "确定";
            Ext.MessageBox.buttonText.no = "取消";
            Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function (id) {
                if (id == 'yes') {
                    tryReal();
                }
            });
        } else {
            tryReal();
        }
    }

    function tryCmd() {
        var content = cmdContent.getValue();
        if (!content) {
            Ext.Msg.alert('提示', '请填入命令！');
            return;
        }

        var agentId = agentPullChosedCbForOneCmd.getValue();
        if (!agentId) {
            Ext.Msg.alert('提示', '请选择服务器！');
            return;
        }

        var typeValue = "";//FieldContainerForOnecmd.getValue()['ra_s_type1'];

        //执行时，根据所选agent自动匹配服务器类型
        Ext.Ajax.request({
            url: 'getTryAgentById.do',
            method: 'POST',
            async: false,
            params: {
                iid: agentPullChosedCbForOneCmd.getValue()
            },
            success: function (response, request) {
                var reader = Ext.decode(response.responseText);
                typeValue = reader.osType;
            },
            failure: function (result, request) {
                secureFilterRs(result, "查询服务器出错！");
            }
        });

        var type = "sh";
        if (typeValue.indexOf("Linux") != -1 || typeValue.indexOf("linux") != -1) {
            type = "sh";
        } else if (typeValue.indexOf("Windows") != -1 || typeValue.indexOf("windows") != -1) {
            type = "bat";
        }

        Ext.Ajax.request({
            url: 'tryATry.do',
            method: 'POST',
            sync: true,
            params: {
                scriptName: 'scriptCmd',
                scriptType: type,
                scriptContent: content,
                agentId: agentId,
                switchFlag: gdSwitch
            },
            success: function (response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    cmdRequestId = Ext.decode(response.responseText).requestId;
                    cmdAgentIp = Ext.decode(response.responseText).agentIp;
                    cmdAgentPort = Ext.decode(response.responseText).agentPort;

                    if (refreshCmdForBasic) {
                        clearInterval(refreshCmdForBasic);
                    }

                    refreshCmdForBasic = setInterval(function () {
                        loadShelloutputhisInfoCmd(cmdRequestId, cmdAgentIp, cmdAgentPort);
                    }, 4 * 1000);

                    settime(cmdbtn, '执行');
                    //$('#consoleLogForCmd').html("");
                }
                Ext.Msg.alert('提示', message);

            },
            failure: function (result, request) {
                secureFilterRs(result, "出现错误！");
            }
        });
    }

    var tryATrybtn = Ext.create('Ext.Button', {
        text: '尝试',
        width: 80,
        cls: 'Common_Btn',
        handler: function () {
            tryATry();
        }
    });


    var cmdbtn = Ext.create('Ext.Button', {
        text: '执行',
        width: 80,
        cls: 'Common_Btn',
        handler: function () {
            tryCmd();
        }
    });

    function settime(btn, text) {
        if (countdown == 0) {
            btn.setDisabled(false);
            btn.setText(text);
            btn.setWidth(80);
            countdown = 10;
        } else {
            btn.setDisabled(true);
            btn.setText(text + "(" + countdown + ")");
            btn.setWidth(100);
            countdown--;
            setTimeout(function () {
                settime(btn, text)
            }, 1000)
        }

    }

    var cmdStr = new Ext.form.TextField({
        width: '40%',
        labelWidth: 70,
        fieldLabel: 'CMD',
        listeners: {
            specialkey: function (textfield, e) {
                if (e.getKey() == Ext.EventObject.ENTER) {
                    cmdVForbasicEdit = cmdStr.getValue();
                    cmdStr.setValue("");
                }
            }
        }
    });

    var cmd_input;
    if (gdSwitch == 1) {
        cmd_input = [''];
    } else {
        cmd_input = [cmdStr];
    }
    var mainP = Ext.create('Ext.panel.Panel', {
        minHeight: 80,
        border: false,
        autoScroll: true,
        region: 'center',
        cls: 'customize_panel_back',
        height: contentPanel.getHeight(),
        html: '<textarea id="code" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>',
        tbar: [/*FieldContainer_win,*/ '->'/*, scriptTemplateCombobox, chooseSqlExecModel*/, agentPullChosedCb, tryATrybtn],
        // bbar: cmd_input,
        tools: [{
            xtype: 'tool',
            type: 'restore',
            handler: function (event, toolEl, owner, tool) {
                if (!owner.maximize) {
                    owner.restoreSize = owner.getSize();
                    owner.maximize = true;
                    paramsAndFuncDescPanel.hide();
                    consolePanel.hide();
                    cmdStr.hide();
                    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 95);
                } else {
                    owner.maximize = false;
                    owner.setSize(owner.restoreSize);
                    paramsAndFuncDescPanel.show();
                    consolePanel.show();
                    cmdStr.show();
                    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 95);
                }
            }
        }
        ]
    });

//    var cmdField = new Ext.form.TextField({
//        name: 'cmd',
//        emptyText: '请输入命令',
//        width: 280
//    });

//    var sTStore = Ext.create('Ext.data.Store', {
//	    fields: ['id', 'name'],
//	    data : [
//	        {"id":"bat", "name":"BAT"},
//	        {"id":"sh", "name":"SHELL"},
//	        {"id":"py", "name":"PYTHON"},
//	        {"id":"perl", "name":"PERL"}
//	    ]
//	});

//    var sTCb = Ext.create('Ext.form.field.ComboBox', {
//        name: 'stcb',
//        padding: '0 5 0 0',
//        queryMode: 'local',
//        displayField: 'name',
//        valueField: 'id',
//        editable: false,
//        emptyText: '',
//        value: 'bat',
//        width: 90,
//        store: sTStore
//    });

    var execCmdBtn = Ext.create('Ext.Button', {
        text: '连接',
        cls: 'Common_Btn',
        handler: function () {
            var agentIp = agentPullChosedCbForCmd.getRawValue() || '';
            var protocol = chooseServerTypeForConsole.getValue() || '';
            var shellPort = (choosePortForConsole.getValue() || '') + "";
            var shellUserName = userNameForConsole.getValue() || '';
            var shellUserPassword = userPasswordForConsole.getValue() || '';
            if (Ext.isEmpty(Ext.util.Format.trim(agentIp))) {
                Ext.Msg.alert('提示', '请选择服务器！');
                return;
            }
            if (Ext.isEmpty(Ext.util.Format.trim(protocol))) {
                Ext.Msg.alert('提示', '请选择协议！');
                return;
            }
            if (Ext.isEmpty(Ext.util.Format.trim(shellPort))) {
                Ext.Msg.alert('提示', '请填写端口号！');
                return;
            } else {
                if (!checkIsInteger(shellPort)) {
                    Ext.Msg.alert('提示', '端口号必须为正整数！');
                    return;
                }
            }
            if (Ext.isEmpty(Ext.util.Format.trim(shellUserName))) {
                Ext.Msg.alert('提示', '请填写用户名！');
                return;
            }
            if (Ext.isEmpty(Ext.util.Format.trim(shellUserPassword))) {
                Ext.Msg.alert('提示', '请填写密码！');
                return;
            }

            var options = {
                host: agentIp,
                port: shellPort,
                username: shellUserName,
                password: shellUserPassword
            }
            connect(protocol, options);
        }
    });
//    var chooseAgentBtnForCmd = Ext.create('Ext.Button', {
//        text: '选择服务器',
//        cls: 'Common_Btn',
//        handler: function() {
//        	whichButtonIsClicked = 3;
//        	chooseTestAgentWin.show();
//        	resourceGroupObj.setValue('');
//    		search_form.getForm().findField("agentIp").setValue('');
//        	search_form.getForm().findField("agentDesc").setValue('');
//        	agent_store.load();
//        }
//    });

    var tapPanelForConsole = Ext.create("Ext.tab.Panel", {
        activeTab: 0, // 默认激活第几个tab页
        border: false,
        bodyStyle: 'background:#000;',
        plugins: Ext.create('Ext.ux.TabCloseMenu', {
            closeTabText: '关闭本页',
            closeOthersTabsText: '关闭其他',
            closeAllTabsText: '关闭全部',
            closeLeftAllTabsText: '关闭左侧全部',
            closeRightAllTabsText: '关闭右侧全部'
        }),
        listeners: {
            'tabchange': function (tab, newc, oldc) {
// var termold = $('#term-create-'+ hex_md5(newc.title)).data('term');
// if(termold) {
// termold.focus();
// }
            },
            'beforeremove': function (tabs, tab) {
                var termold = $('#term-create-' + hex_md5(tab.title)).data('term');
                var clientold = $('#term-create-' + hex_md5(tab.title)).data('client');
                if (termold) {
                    termold.destroy();
                }
                if (clientold) {
                    clientold.close();
                }
            }
        }
    });

    var consoleOneCmdPanel = Ext.create('Ext.panel.Panel', {
        border: true,
        autoScroll: true,
        bodyStyle: 'background:#000;',
        title: "终端",
        tbar: [agentPullChosedCbForCmd, chooseServerTypeForConsole, choosePortForConsole, userNameForConsole, userPasswordForConsole, execCmdBtn],
        items: [tapPanelForConsole]
    });
    var cmdContent = new Ext.form.TextField({
        name: 'cmdContent',
//		fieldLabel: '命令',
        emptyText: '--请输入命令--',
//		labelWidth : 50,
        width: '85%',
        labelAlign: 'right',
        listeners:{
            specialkey:function(field,e){
                if (e.getKey()==Ext.EventObject.ENTER){
                    tryCmd();
                }
            }
        }
    });


    var consoleCmdPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        border: false,
        autoScroll: true,
        title: '日志',
        height: contentPanel.getHeight()-180,
        html: '<textarea readonly="readonly" id="consoleLogForCmd" style="width:100%;height:99%;background: #4b4b4b;color: white;margin:0;"></textarea>'
    });


    /*var oneCmdPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        autoScroll: true,
        title: "单指令测试",
        layout: 'border',
        // items: [consoleCmdPanel],
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            cls: 'whitelist',
            items: [/!*FieldContainerForOnecmd,*!/ '->', agentPullChosedCbForOneCmd]
        }, consoleCmdPanel,{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            cls: 'whitelist',
            items: [cmdContent, '->', cmdbtn]
        }]
    });*/
    var consolePanel = Ext.create('Ext.panel.Panel', {
        height: contentPanel.getHeight() - 253,
        region: 'south',
        border: true,
        collapsible: true,
        collapsed: true,
        autoScroll: true,
        title: '日志',
        bbar: cmd_input,
        html: '<textarea id="consoleLog" style="width:100%;height:100%;background: #4b4b4b;color: white;margin:0;"></textarea>'
    });
    var maimPanels = Ext.create('Ext.panel.Panel', {
        border: false,
        layout: {
            type: 'border'
        },
        title: gdSwitch == 1 ? "编辑框---sql" : "编辑框---shell",
        items: [mainP, consolePanel]
    });
    //左侧Panel的上半部分，编写脚本的Panel

    /*var items = [oneCmdPanel];
    var tabsCenter = Ext.createWidget('tabpanel', {
        // activeTab: 1, //指定默认的活动tab
        region: 'center',
        minHeight: 80,
        height: contentPanel.getHeight(),
        plain: true,                        // True表示tab候选栏上没有背景图片（默认为false）
        enableTabScroll: true,              // 选项卡过多时，允许滚动
        defaults: {autoScroll: true},
        border: true,
        layout: {
            type: 'border'
        },
        items: items
    });*/

    var importPanel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        height: 150,
        border: false,
        collapsible: true,
        autoScroll: true,
        title: '日志',
        html: '<textarea id="consoleLog" style="width:100%;height:100%;background: #4b4b4b;color: white;margin:0;"></textarea>'
    });

    var tryTestbtn = Ext.create('Ext.Button', {
        text: '测试',
        handler: function () {
            editor.save();
            var content = document.getElementById('code').value;
            if (content.trim() == '') {
                Ext.MessageBox.alert("提示", "测试脚本内容不能为空!");
                return;
            }

            if (checkRadioForBasicScriptEdit == 4) {
                agent_grid.setWidth((contentPanel.getWidth() - 250) / 2);
                db_soucre_grid.setWidth((contentPanel.getWidth() - 250) / 2);
                db_soucre_grid.show();
            } else {
                agent_grid.setWidth((contentPanel.getWidth() - 250));
                db_soucre_grid.hide();
            }
            var p = orgParams();
            var paramInvalidMessage = p.paramInvalidMessage;
            var someParamIsEmpty = p.someParamIsEmpty;
            if (isAuditing()) {
                Ext.MessageBox.buttonText.yes = "确定";
                Ext.MessageBox.buttonText.no = "取消";
                Ext.Msg.confirm("请确认", "该原子脚本处于审核中，修改的内容不会保存，是否继续测试？", function (id) {
                    if (id == 'yes') {
                        if (someParamIsEmpty) {
                            Ext.MessageBox.buttonText.yes = "确定";
                            Ext.MessageBox.buttonText.no = "取消";
                            Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function (id) {
                                if (id == 'yes') {
                                    if (paramInvalidMessage) {
                                        Ext.MessageBox.alert("提示", paramInvalidMessage);
                                    } else {
                                        testScript();
                                    }
                                }
                            });
                        } else {
                            if (paramInvalidMessage) {
                                Ext.MessageBox.alert("提示", paramInvalidMessage);
                            } else {
                                testScript();
                            }
                        }
                    }
                });
            } else {
                if (someParamIsEmpty) {
                    Ext.MessageBox.buttonText.yes = "确定";
                    Ext.MessageBox.buttonText.no = "取消";
                    Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function (id) {
                        if (id == 'yes') {
                            if (paramInvalidMessage) {
                                Ext.MessageBox.alert("提示", paramInvalidMessage);
                            } else {
                                save(0, testScript, '测试');
                            }
                        }
                    });
                } else {
                    if (paramInvalidMessage) {
                        Ext.MessageBox.alert("提示", paramInvalidMessage);
                    } else {
                        save(0, testScript, '测试');
                    }
                }
            }
        }
    });
    // var checkBtn = Ext.create('Ext.Button', {
    //     text: '检查',
    //     hidden: scriptCheckRuleSwitch,
    //     handler: checkFunction
    // });

    var publishBtn = Ext.create('Ext.Button', {
        text: '发布',
        hidden: istrySwitch,
        handler: function () {
            editor.save();
            var content = document.getElementById('code').value;
            if (content.trim() == '') {
                Ext.MessageBox.alert("提示", "脚本内容不能为空!");
                return;
            }
            if (isAuditing()) {
                Ext.MessageBox.alert("提示", "该原子脚本处于审核中，不能再次发布！");
            } else {
                save(0, publishScript, '发布');
            }
        }
    });
    var saveBtn = Ext.create('Ext.Button', {
        text: '保存',
        //id: 'saveButton',
        handler: function () {
            if (isAuditing()) {
                Ext.MessageBox.alert("提示", "该原子脚本处于审核中，不能保存！");
            } else {
                Ext.Msg.wait('处理中，请稍后...', '提示');
                save(0); // 正常保存
            }
        }
    });

    //脚本编写左侧主panel
    /*var buttonItems = [checkBtn, tryTestbtn, publishBtn, saveBtn, {
        text: '重置',
        handler: quxiao
    }, {
        text: '返回',
        handler: function () {
            baseInfoOfScriptWin.destroy();
            destroyRubbish();
            contentPanel.getLoader().load({
                url: 'forwardScriptServiceRelease.do',
                params: {},
                scripts: true
            });
        }
    }];*/

    var westPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        cls: 'customize_panel_back panel_space_right_zb',
        height: contentPanel.getHeight(),
        // items: [items],
        buttonAlign: 'center',/*,
        buttons: buttonItems*/
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            cls: 'whitelist',
            items: [/*FieldContainerForOnecmd,*/ '->', agentPullChosedCbForOneCmd]
        }, consoleCmdPanel,{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            cls: 'whitelist',
            items: [cmdContent, '->', cmdbtn]
        }]
    });


    Ext.define('scriptDirModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iscriptDirName',
                type: 'string'
            }, {
                name: 'iscriptDirSort',
                type: 'long'
            }, {
                name: 'iid',
                type: 'long'
            }, {
                name: 'iscriptDirDiscript',
                type: 'string'
            }, {
                name: 'iscriptDirLevel',
                type: 'int'
            }, {
                name: 'iscriptDirRootId',
                type: 'string'
            }
        ]

    })

    var scriptDirTreeStore = Ext.create('Ext.data.TreeStore', {
        autoLoad: gfScriptDirFunctionSwitch,
        model: 'scriptDirModel',
        proxy: {
            type: 'ajax',
            url: 'scriptEdit/scriptDirList.do'
        },
        root: {
            expanded: gfScriptDirFunctionSwitch,
            iscriptDirName: '根目录',
            icon: 'ext/resources/ext-theme-neptune/images/tree/folder.png'
        }
    });

    scriptDirTreeStore.on("load",function(obj, node, records, successful, eOpts){
        if(records==''){
            var flag = true;
            var treeViewDiv = treePanel.body.dom.childNodes[0].childNodes;
            for(var i=0;i<treeViewDiv.length;i++){
                if(treeViewDiv[i].className=='x-grid-empty'){
                    flag = false;
                }
            }
            if(flag){
                var doc = document.createRange().createContextualFragment(treePanel.getView().emptyText);
                treePanel.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });

    var treePanel = Ext.create('Ext.tree.Panel', {
        // height : '80%',
        region: 'west',
        width: '16%',
        id: 'treeId',
        title: '脚本目录',
        autoScroll: true,
        collapsible: true,
        cls: 'customize_panel_back',
        animate: true,
        useArrows: true,
        hidden: !gfScriptDirFunctionSwitch,
        rootVisible: false,
        store: scriptDirTreeStore,
        hideHeaders: true,
        columns: [{
            xtype: 'treecolumn',
            text: '目录',
            flex: 1,
            dataIndex: 'iscriptDirName',
            sortable: false
        }],
        border:true,
        padding : grid_space,
        columnLines : true,
        emptyText : '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });


    //脚本编写页面主Panel
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "gridBasicScriptEdit_area",
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        padding: 5,
        autoScroll: true,
        border: true,
        bodyPadding: grid_margin,
        bodyCls: 'service_platform_bodybg',
        height: contentPanel.getHeight() - modelHeigth,
        items: [treePanel, westPanel/*, paramsAndFuncDescPanel*/]
    });

//     var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
//         mode: 'shell',
//         theme: "lesser-dark", // 主题
//         keyMap: "sublime", // 快键键风格
//         extraKeys: {
//             "Ctrl-Q": "autocomplete",
//             "Ctrl-D": "deleteLine"
//         },
//         lineNumbers: true, // 显示行号
//         smartIndent: true, // 智能缩进
//         indentUnit: 4, // 智能缩进单位为4个空格长度
//         indentWithTabs: true, // 使用制表符进行智能缩进
//         lineWrapping: true, //
//         // 在行槽中添加行号显示器、折叠器、语法检测器
//         gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
//         foldGutter: true, // 启用行槽中的代码折叠
//         autofocus: true, // 自动聚焦
//         matchBrackets: true, // 匹配结束符号，比如"]、}"
//         autoCloseBrackets: true, // 自动闭合符号
//         styleActiveLine: true // 显示选中行的样式
// //        ,extraKeys: {
// //            "F11": function(cm) {
// //              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
// //            },
// //            "Esc": function(cm) {
// //              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
// //            }
// //          }
//     });

    // editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 100);
    // contentPanel.on('resize',
    //     function () {
    //         editor.getDoc().clearHistory();
    //         mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
    //         mainPanel.setWidth(contentPanel.getWidth());
    //         editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 100);
    //         if (baseInfoOfScriptWin) {
    //             baseInfoOfScriptWin.center();
    //         }
    //         if (publishAuditingSMWin) {
    //             publishAuditingSMWin.center();
    //         }
    //         if (chooseTestAgentWin) {
    //             chooseTestAgentWin.center();
    //         }
    //     });
    // tabsCenter.on('resize', function () {
    //     editor.getDoc().clearHistory();
    //     editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 100);
    // });
//     if (oldScriptId > 0) {
//         Ext.Ajax.request({
//             url: 'scriptService/copyAttachments.do',
//             method: 'POST',
//             async: false,
//             params: {
//                 uuid: oldScriptUuid
//             },
//             success: function (response, request) {
//                 var reader = Ext.decode(response.responseText);
//                 attachmentIds = reader.attachmentIds;
//                 attachmentStore.load({params: {scriptId: '', ids: attachmentIds}});
//
//             },
//             failure: function (result, request) {
//                 secureFilterRs(result, "复制附件出错！");
//             }
//         });
//
//         Ext.Ajax.request({
//             url: 'scriptService/queryOneService.do',
//             method: 'POST',
//             async: false,
//             params: {
//                 iid: oldScriptId
//             },
//             success: function (response, request) {
//                 var reader = Ext.decode(response.responseText);
//                 scName.setValue(reader.scriptName);
// //                $('#s-n-t').html(reader.scriptName);
//                 if (reader.scriptName) {
//                     maimPanels.setTitle(reader.scriptName);
//                 } else {
//                     maimPanels.setTitle("编辑框");
//                 }
//                 scriptName = reader.scriptName;
//                 sName.setValue(reader.serviceName);
//                 scName.setValue(reader.scriptName);
//                 excepResult.setValue(reader.excepResult);
//                 errExcepResult.setValue(reader.errExcepResult);
//                 usePlantForm.setValue(reader.platForm);
//                 suUser.setValue(reader.suUser);
//                 timeout.setValue(reader.timeout == -1 ? null : reader.timeout);
//                 funcDesc.setValue(reader.funcDesc);
//                 funcDescInWin.setValue(reader.funcDesc);
//                 chooseSqlExecModel.setValue(reader.isAgent);
//
//                 var scriptT = reader.scriptType;
//                 if (scriptT == 'sh') {
//                     FieldContainer_win.items.items[0].setValue(true);
//                     checkRadioForBasicScriptEdit = 0;
//                     editor.setOption("mode", 'shell');
//                     chooseSqlExecModel.hide();
//                 } else if (scriptT == 'bat') {
//                     FieldContainer_win.items.items[1].setValue(true);
//                     checkRadioForBasicScriptEdit = 1;
//                     editor.setOption("mode", 'bat');
//                     chooseSqlExecModel.hide();
//                 } else if (scriptT == 'py') {
//                     FieldContainer_win.items.items[3].setValue(true);
//                     checkRadioForBasicScriptEdit = 3;
//                     editor.setOption("mode", 'python');
//                     chooseSqlExecModel.hide();
//                 } else if (scriptT == 'sql') {
//                     FieldContainer_win.items.items[4].setValue(true);
//                     checkRadioForBasicScriptEdit = 4;
//                     editor.setOption("mode", 'sql');
//                     if (gdSwitch == 1) {
//                         chooseSqlExecModel.show();
//                     } else {
//                         chooseSqlExecModel.hide();
//                     }
//
//                 } else if (scriptT == 'perl') {
//                     FieldContainer_win.items.items[2].setValue(true);
//                     checkRadioForBasicScriptEdit = 2;
//                     editor.setOption("mode", 'text/x-perl');
//                     chooseSqlExecModel.hide();
//                 } else if (scriptT == 'ps1') {
//                     FieldContainer_win.items.items[6].setValue(true);
//                     checkRadioForBasicScriptEdit = 6;
//                     editor.setOption("mode", 'powershell');
//                     chooseSqlExecModel.hide();
//                 }
//                 editor.setOption('value', '');
//                 editor.setOption('value', reader.content);
//                 groupName=parseInt(reader.groupName);
//                 sysID = parseInt(reader.sysName);
//                 busID = parseInt(reader.bussName);
//                 threeBsTypeId = parseInt(reader.threeTypeId);
//                 bussData.load();
//                 paramStore.load({params: {scriptId: oldScriptUuid}});
//             },
//             failure: function (result, request) {
//                 secureFilterRs(result, "获取脚本信息失败！");
//             }
//         });
//     }


    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    function orgParams() {
        paramStore.sort('paramOrder', 'ASC');
        var m = paramStore.getRange(0, paramStore.getCount() - 1);
        var aaaa = [];
        var someParamIsEmpty = false;
        var paramInvalidMessage = '';
        for (var i = 0, len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue") : '';

            if (!paramDefaultValue) {
                someParamIsEmpty = true;
            }
            if ((paramType == 'OUT-int' || paramType == 'IN-int') && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    paramInvalidMessage = '参数类型为int，但参数值不是int类型！';
                    break;
                }
            }
            if ((paramType == 'OUT-float' || paramType == 'IN-float') && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    paramInvalidMessage = '参数类型为float，但参数值不是float类型！';
                    break;
                }
            }
            if (paramDefaultValue.indexOf('"') >= 0) {
                if (checkRadioForBasicScriptEdit == '1') {
                    paramInvalidMessage = 'bat脚本暂时不支持具有双引号的参数值';
                    break;
                }
            }
            aaaa.push(paramDefaultValue);
        }

        return {
            someParamIsEmpty: someParamIsEmpty,
            paramInvalidMessage: paramInvalidMessage,
            scriptPara: aaaa.join("@@script@@service@@")
        };
    }

    function getParams() {
        paramStore.sort('paramOrder', 'ASC');
        var m = paramStore.getRange(0, paramStore.getCount() - 1);
        var aaaa = [];
        for (var i = 0, len = m.length; i < len; i++) {
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue") : '';
            var paramType = m[i].get("paramType") ? m[i].get("paramType") : '';
            if (gdSwitch > 0) {
                aaaa.push(paramType + ":" + paramDefaultValue);
            } else {
                aaaa.push(paramDefaultValue);
            }
        }
        return aaaa.join("@@script@@service@@");
    }

    function getTestParams() {
        testparamStore.sort('paramOrder', 'ASC');
        var m = testparamStore.getRange(0, testparamStore.getCount() - 1);
        var aaaa = [];
        for (var i = 0, len = m.length; i < len; i++) {
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue") : '';
            aaaa.push(paramDefaultValue);
        }
        return aaaa.join("@@script@@service@@");
    }

//     function quxiao() {
//         Ext.MessageBox.buttonText.yes = "确定";
//         Ext.MessageBox.buttonText.no = "取消";
//         if (newServiceId == 0) {
//             Ext.Msg.confirm("请确认", "是否确认重置所有内容？", function (id) {
//                 if (id == 'yes') {
//                     forword("basicScriptEdit.do?switchFlag=" + gdSwitch, "脚本编写");
//                 }
//             });
//         } else {
//             Ext.Msg.confirm("请确认", "是否确认重置所有内容？<br>将恢复到最后一次保存时的状态！", function (id) {
//                 if (id == 'yes') {
//                     Ext.Ajax.request({
//                         url: 'scriptService/queryOneService.do',
//                         method: 'POST',
//                         sync: true,
//                         params: {
//                             iid: newServiceId
//                         },
//                         success: function (response, request) {
//                             var reader = Ext.decode(response.responseText);
//                             scName.setValue(reader.scriptName);
// //                            $('#s-n-t').html(reader.scriptName);
//                             if (reader.scriptName) {
//                                 maimPanels.setTitle(reader.scriptName);
//                             } else {
//                                 maimPanels.setTitle("编辑框");
//                             }
//                             scriptName = reader.scriptName;
//                             sName.setValue(reader.serviceName);
//                             scName.setValue(reader.scriptName);
//                             excepResult.setValue(reader.excepResult);
//                             suUser.setValue(reader.suUser);
//                             errExcepResult.setValue(reader.errExcepResult);
//                             usePlantForm.setValue(reader.platForm);
//                             funcDesc.setValue(reader.funcDesc);
//                             funcDescInWin.setValue(reader.funcDesc);
//                             chooseSqlExecModel.setValue(reader.isAgent);
//                             var scriptT = reader.scriptType;
//                             if (scriptT == 'sh') {
//                                 FieldContainer_win.items.items[0].setValue(true);
//                                 checkRadioForBasicScriptEdit = 0;
//                                 editor.setOption("mode", 'shell');
//                             } else if (scriptT == 'bat') {
//                                 FieldContainer_win.items.items[1].setValue(true);
//                                 checkRadioForBasicScriptEdit = 1;
//                                 editor.setOption("mode", 'bat');
//                             } else if (scriptT == 'py') {
//                                 FieldContainer_win.items.items[3].setValue(true);
//                                 checkRadioForBasicScriptEdit = 3;
//                                 editor.setOption("mode", 'python');
//                             } else if (scriptT == 'SQL') {
//                                 FieldContainer_win.items.items[4].setValue(true);
//                                 checkRadioForBasicScriptEdit = 4;
//                                 editor.setOption("mode", 'sql');
//                             } else if (scriptT == 'perl') {
//                                 FieldContainer_win.items.items[2].setValue(true);
//                                 checkRadioForBasicScriptEdit = 2;
//                                 editor.setOption("mode", 'text/x-perl');
//                             } else if (scriptT == 'ps1') {
//                                 FieldContainer_win.items.items[6].setValue(true);
//                                 checkRadioForBasicScriptEdit = 6;
//                                 editor.setOption("mode", 'powershell');
//                             }
//                             editor.setOption('value', '');
//                             editor.setOption('value', reader.content);
//                             groupName=parseInt(reader.groupName);
//                             sysID = parseInt(reader.sysName);
//                             busID = parseInt(reader.bussName);
//                             bussData.load();
//                             attachmentIds = [];
//                             paramStore.load();
//                             testparamStore.load();
//                             attachmentStore.load();
//                             attaTempStore.load();
//                         },
//                         failure: function (result, request) {
//                             secureFilterRs(result, "获取脚本信息失败！");
//                         }
//                     });
//                 }
//             });
//         }
//     }

    function testScript() {
        whichButtonIsClicked = 2;
        chooseTestAgentWin.show();
        resourceGroupObj.setValue('');
        execUserForTry.setValue(suUser.getValue());
        //search_form.getForm().findField("agentIp").setValue('');
        //search_form.getForm().findField("agentDesc").setValue('');
        agent_store.load();
    }

    function isAuditing() {
        var isAuditing = false;
        Ext.Ajax.request({
            url: 'scriptStatus.do',
            method: 'POST',
            async: false,
            params: {
                serviceId: newServiceId
            },
            success: function (response, opts) {
                var status = Ext.decode(response.responseText).status;
                if (status == 2) {
                    isAuditing = true;
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
        return isAuditing;
    }

    function openTipWindow(response, callback, callType) {
        let msgTextArea = Ext.create('Ext.form.HtmlEditor', {
            name: 'msgTextArea',
            fieldLabel: '提示',
            //emptyText: '',
            labelWidth: 40,
            labelAlign: 'right',
            enableFont: false,  //隐藏或显示字体选项
            enableFontSize: false, //允许增大/缩小字号按钮(默认为 true)。
            enableFormat: false,   //允许字体加粗
            enableLinks: false,  //启用链接创建按钮。
            enableSourceEdit: false,  //允许切换到源码编辑按钮。
            enableAlignments: false, //启用左对齐、中间对齐、右对齐按钮
            enableColors: false,  //启用回退/高亮颜色按钮
            enableLists: false, //启用bullet和有限数量的按钮列表。
            border: false,
            // margin : '10 0 0 0',
            //maxLength: 255,
            cls: 'scriptTipKeyWord',
            height: 200,
            columnWidth: .98,
            readOnly: true,
            autoScroll: true,
            value: Ext.decode(response.responseText).message
        });
        let messagePa = Ext.create('Ext.form.Panel', {
            width: 630,
            layout: 'anchor',
            bodyCls: 'x-docked-noborder-top',
            buttonAlign: 'center',
            border: false,
            items: [{
                //	    	layout:'form',
                anchor: '98%',
                padding: '5 0 5 0',
                border: false,
                items: [{
                    layout: 'column',
                    border: false,
                    items: [msgTextArea]
                }]
            }],
            bbar: ['->', {
                xtype: 'button',
                text: '是',
                cls: 'Common_Btn',
                handler: function () {
                    this.up("window").close();
                    save(1, callback, callType);
                }
            }, {
                xtype: 'button',
                text: '否',
                cls: 'Common_Btn',
                handler: function () {
                    this.up("window").close();
                }
            }, '->']
        });

        let messageWin = Ext.create('Ext.window.Window', {
            title: "脚本中存在关键命令,是否继续发布脚本？",
            width: 650,
            height: 340,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            items: [messagePa]
        });
        Ext.Msg.close();
        messageWin.show();
    }

    /*function save(ignoreFlag, callback, callType) {
        // ignoreFlag ::
        // 0:正常保存
        // 1:忽略提醒命令，继续保存
        var scriptfile = scriptForm.getForm().findField("scriptfile").getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var sysId = bussCb.getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        //功能分类名和id
        var groupId=groupNameCombo.getValue();
        var groupName=groupNameCombo.getRawValue();
        var bussTypeId = bussTypeCb.getValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var threeBussTypeId = threeBussTypeCb.getValue();
        var threeBussTypeName = threeBussTypeCb.getRawValue();
        var sysName = bussCb.getRawValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussType = bussTypeCb.getRawValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var serverName = sName.getValue().trim();
        var scriptName1 = scName.getValue().trim();
        var up = usePlantForm.getValue() || "";
        var errExcepResult1 = errExcepResult.getValue();
        var excepResult1 = excepResult.getValue();

        // var timeoutValue = timeout.getValue() == null ? -1 : timeout.getValue();
        // if (timeoutValue) {
        //     if (!checkIsInteger(timeoutValue)) {
        //         setMessage('请输入数字类型的超时时间！');
        //         return;
        //     }
        //
        // var timeoutValue = timeout.getValue()==null?-1:timeout.getValue();
        // if(timeoutValue){
        // 	 if (!checkIsInteger(timeoutValue)) {
        //          setMessage('请输入数字类型的超时时间！');
        //          return;
        //      }

        var timeoutValue = timeout.getValue() == null ? -1 : timeout.getValue();
        var scriptDirId = 0;

        if (gfScriptDirFunctionSwitch) {
            var selScriptDir = Ext.getCmp('treeId').getSelectionModel().getSelection();
            console.log(selScriptDir)
            if (selScriptDir.length > 1) {
                Ext.Msg.alert('提示', '要保存的目录大于1');
                return;
            } else if (selScriptDir.length < 1) {
                Ext.Msg.alert('提示', '请选择要保存的目录');
                return;
            } else {
                console.log(selScriptDir[0].childNodes.length);
                if (selScriptDir[0].childNodes.length > 0) {
                    Ext.Msg.alert('提示', '所选目录不是末级目录');
                    return;
                } else if (selScriptDir[0].childNodes.length == 0) {
                    scriptDirId = selScriptDir[0].data.iid;
                } else {
                    return;
                }
            }
        }

        if (timeoutValue) {
            if (!checkIsInteger(timeoutValue)) {
                setMessage('请输入数字类型的超时时间！');
                return;
            }

        }
        var scriptDesc1 = funcDescInWin.getValue();
        var dbType1 = dbType.getValue() || "";// 数据库类型
        var serviceType1 = serviceType.getValue() || "";// 服务类型
        var isExam1 = isExam.getValue() || "";// 发起审核
        var suExecUser = suUser.getValue() || "";//启动用户
//		var isAutoSubValue = isAutoSub.getValue() || "";//启动用户
        if (gdSwitch == 1) {
            isGroupType = 1;
        }

        var manualStartValue = manualStart.getValue() || "";//启动用户
        if (!scriptfile) {
            saveFromBottom = true;
        }
        if (!scriptName1) {
            scName.setValue(scriptName);
        }
        if (!scriptDesc1) {
            funcDescInWin.setValue(scriptDesc);
        }
        baseInfoOfScriptWin.close();
        if (!sysId && gdSwitch == 0) {
            //saveFromBottom = true;
            baseInfoOfScriptWin.show();
            // Ext.MessageBox.alert("提示", "请选择一级分类!");
            return;
        }
        if (!bussTypeId && gdSwitch == 0) {
            //saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //   Ext.MessageBox.alert("提示", "请选择二级分类!");
            return;
        }
        if (!threeBussTypeId && gdSwitch == 0 && scriptThreeBstypeSwitch) {
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "请选择三级分类!");
            return;
        }


        if (serverName.trim() == '') {
            //saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //  Ext.MessageBox.alert("提示", "服务名称不能为空!");
            return;
        }
        if (scriptName1.trim() == '') {
            //saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //   Ext.MessageBox.alert("提示", "脚本名称不能为空!");
            return;
        }
        if (gdSwitch == 1 && dbType1.trim() == '') {
            baseInfoOfScriptWin.show();
            return;
        }
        if (gdSwitch == 1 && serviceType1.trim() == '') {
            baseInfoOfScriptWin.show();
            return;
        }
        if (gdSwitch == 1 && isExam1.trim() == '') {
            baseInfoOfScriptWin.show();
            return;
        }
//        $('#s-n-t').html(Ext.util.Format.trim(scriptName1));
        //  attachmentGrid.setTitle('脚本工程('+Ext.util.Format.trim(scriptName1)+')');
        if (up.length == 0) {
            //	saveFromBottom = true;
            baseInfoOfScriptWin.show();
            //   Ext.MessageBox.alert("提示", "适用平台不能为空!");
            return;
        }
        if (scriptDesc1.trim() == '') {
            //	saveFromBottom = true;
            baseInfoOfScriptWin.show();
//                Ext.MessageBox.alert("提示", "功能说明不能为空!");
            return;
        }

//        var m = paramStore.getModifiedRecords();
        var m = paramStore.getRange();
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
            var iorder = m[i].get("paramOrder");
            var parameterName = m[i].get("parameterName");
            var ruleName = m[i].get("ruleName");
            if (paramType == '枚举') {
                if (paramDefaultValue == "" || paramDefaultValue == null) {
                    setMessage('默认值不能为空！');
                    return;
                }
            }
            if (bhParameterCheckSwitch) {
                //根据选中的验证规则名拿到对应的正则表达式
                if (ruleName != "" && ruleName != null) {
                    Ext.Ajax.request({
                        url: 'queryParameterCheckRule.do',
                        method: 'POST',
                        async: false,
                        params: {
                            ruleName: ruleName
                        },
                        success: function (response, request) {
                            dataList = Ext.decode(response.responseText).dataList;
                        },
                        failure: function (result, request) {
                            Ext.Msg.alert('提示', '获取验证规则失败！');
                        }
                    });
                    //用拿到的正则去校验默认值
                    var patt = new RegExp(dataList);
                    if (patt.exec(paramDefaultValue) == null) {
                        setMessage('顺序为' + "“" + iorder + "”" + '的默认值校验不通过请检查！');
                        return;
                    }
                }
            }
            if ("" == paramType) {
                setMessage('参数类型不能为空！');
                return;
            }
            if (fucCheckLength(paramDesc) > 250) {
                setMessage('参数描述不能超过250字符！');
                return;
            }

            if ((paramType == 'OUT-int' || paramType == 'IN-int' || paramType == 'int') && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    setMessage('参数类型为int，但参数默认值不是int类型！');
                    return;
                }
            }
            if ((paramType == 'OUT-float' || paramType == 'IN-float' || paramType == 'float') && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    setMessage('参数类型为float，但参数默认值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"') >= 0) {
                if (checkRadioForBasicScriptEdit == '1') {
                    Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
                }
            }
            for (var k = 0; k < paramStore.getCount(); k++) {
                var record = paramStore.getAt(k);
                var order = record.data.paramOrder;
                if (order == iorder) {
                    n = n + 1;
                }
            }
            if (n > 1) {
                Ext.MessageBox.alert("提示", "参数顺序不能重复！");
                return;
            }
            if (paramType == '枚举') {
                if (parameterName == "" || parameterName == null) {
                    setMessage('枚举名不能为空！');
                    return;
                }
            }

            var paramRecord = {
                iid: m[i].data.iid,
                paramType: m[i].data.paramType,
                paramDefaultValue: m[i].data.paramDefaultValue,
                ruleName: m[i].data.ruleName,
                paramDesc: m[i].data.paramDesc,
                paramOrder: m[i].data.paramOrder,
                parameterName: m[i].data.parameterName
            }
            var ss = Ext.JSON.encode(paramRecord);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        var rulejsonData = "[";
        if (checkRadioForBasicScriptEdit == '4' && chooseSqlExecModel.getValue() == '2') {
            var patt1 = new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
            var mm = paramRulesStore.getRange();
            var outArr = {};
            var arrContain = [];
            for (var i = 0, len1 = mm.length; i < len1; i++) {
                var nn = 0;
                //var paramRuleIn = mm[i].get("paramRuleIn") ? mm[i].get("paramRuleIn").trim() : '';
                var paramRuleOrder = mm[i].get("paramRuleOrder") ? mm[i].get("paramRuleOrder") : '';
                var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
                var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
                var paramRuleType = mm[i].get("paramRuleType");
                var paramRuleLen = mm[i].get("paramRuleLen");
                paramRuleOut = paramRuleOut.toUpperCase();
                if ("" == paramRuleOut) {
                    setMessage('输出规则输出不能为空！');
                    return;
                }
                if ("GROUP" == paramRuleOut || "IID" == paramRuleOut || "IDBID_BULID_IN" == paramRuleOut || "ITIME_BULID_IN" == paramRuleOut || "ISERVICEID_BULID_IN" == paramRuleOut || "ISTARTTIME_BULID_IN" == paramRuleOut || "IDBCOPY_BULID_IN" == paramRuleOut || "ITRIMODEL_BULID_IN" == paramRuleOut || "IFLAG_BULID_IN" == paramRuleOut || "IFLOWID_BULID_IN" == paramRuleOut) {
                    setMessage('部分输出规则已经内置,请重新填写！' + "“" + paramRuleOut + "”");
                    return;
                }
                if (arrContain.contains(paramRuleOut) > 0) {
                    setMessage('输出规则不能重复！');
                    return;
                } else {
                    arrContain.push(paramRuleOut)
                }
                if (!patt1.test(paramRuleOut)) {
                    setMessage('请正确填写输出规则！');
                    return;
                }
                if (paramRuleType == 6) {
                    setMessage('分区表不支持LONG类型列！');
                    return;
                }
                if (paramRuleLen == 0 && paramRuleType != 5/!*&& paramRuleType!=6*!/) {
                    setMessage('长度不能为0字符！');
                    return;
                } else {
                    if (paramRuleType == 2) {
                        if (paramRuleLen > 38) {
                            setMessage('DECIMAL类型的长度最大为38!');
                            return;
                        }
                    }
                }
                if (fucCheckLength(paramRuleDesc) > 250) {
                    setMessage('输出规则描述不能超过250字符！');
                    return;
                }
                if (outArr[paramRuleOrder]) {
                    setMessage('输出规则序号不能重复！');
                    return;
                } else {
                    outArr[paramRuleOrder] = true;
                }
                var ss1 = Ext.JSON.encode(mm[i].data);
                if (i == 0)
                    rulejsonData = rulejsonData + ss1;
                else
                    rulejsonData = rulejsonData + "," + ss1;
            }
            //判断输出字段是否在黑名单中，如果有，则提示
            var iskeyWordResult;
            var isKeyWord;
            Ext.Ajax.request({
                url: 'checkKeyWord.do',
                method: 'POST',
                async: false,
                params: {
                    paramRules: arrContain
                },
                success: function (response, opts) {
                    iskeyWordResult = Ext.decode(response.responseText).result;
                    isKeyWord = Ext.decode(response.responseText).isKeyWord;
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
            if (iskeyWordResult) {
                Ext.MessageBox.alert("提示", "输出字段:" + isKeyWord + "为关键字，请修改！");
                return;
            }

        }
        rulejsonData = rulejsonData + "]";

        editor.save();
        var content = document.getElementById('code').value;
        if (content.trim() == '') {
            Ext.MessageBox.alert("提示", "脚本内容不能为空！");
            return;
        }
        var type = "sh";
        if (checkRadioForBasicScriptEdit == '0') {
            type = "sh";
        } else if (checkRadioForBasicScriptEdit == '1') {
            type = "bat";
        } else if (checkRadioForBasicScriptEdit == '2') {
            type = "perl";
        } else if (checkRadioForBasicScriptEdit == '3') {
            type = "py";
        } else if (checkRadioForBasicScriptEdit == '4') {
            type = "sql";
        } else if (checkRadioForBasicScriptEdit == '6') {
            type = "ps1";
        }
        if (type == 'bat' && up != 'Windows') {
            Ext.Msg.alert('提示', 'bat脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if (type == 'ps1' && up != 'Windows') {
            Ext.Msg.alert('提示', 'powershell脚本不能选择非Windows平台');
            usePlantForm.setValue();
            return;
        }
        if ((type == 'sh' || type == 'perl') && up.indexOf("Windows") != -1) {
            Ext.Msg.alert('提示', '只允许 bat、python、sql、powershell脚本才能选择Windows平台');
            usePlantForm.setValue();
            return;
        }
        var saveUrl = "saveScriptEdit.do";
        if (newServiceId != 0) {
            saveUrl = "updateScriptEdit.do"
        }
        // var spanss=[];
        // var spans = $($('#signDiv').children('div')[0]).children('span');
        // for (let i = 0; i < spans.length; i++) {
        //     console.log($(spans[i]).text())
        //     spanss.push($(spans[i]).text())
        // }
        // var labels = Ext.JSON.encode(spanss);
        var labels = $("#tagsinputval").val();
        //保存时保存按钮失效，禁止重复点击
        // Ext.getCmp('saveButton').disable();
        // Ext.getCmp('saveButton1').disable();
        Ext.Ajax.request({
            url: saveUrl,
            method: 'POST',
            sync: true,
            params: {
                iid: newServiceId,
                uuid: scriptuuid,
                sysName: sysName,
                sysId: sysId,
                bussTypeId: bussTypeId,
                bussType: bussType,
                threeBussTypeId: threeBussTypeId,
                threeBussTypeName: threeBussTypeName,
                serverName: serverName,
                // 系统名称 系统分类
                scriptName: scriptName1,
                excepResult: excepResult1,
                errExcepResult: errExcepResult1,
                usePlantForm: up,
                funcDesc: scriptDesc1,
                checkRadio: type,
                content: content,
                params: jsonData,
                ruleparams: rulejsonData,
                attachmentIds: attachmentIds,
                templateIds: tempmentIds,
                ignoreTipCmd: ignoreFlag,
                dbType: dbType1,
                serviceType: serviceType1,
                isExam: isExam1,
                suUser: suExecUser,
                switchFlag: gdSwitch,
                isAutoSub: isGroupType,
                manualStart: manualStartValue,
                sqlExecModel: chooseSqlExecModel.getValue(),
                timeout: timeoutValue,
                labels: labels,
                scriptDirId: scriptDirId,
                groupId:groupId,
                groupName:groupName
            },
            success: function (response, request) {
                // Ext.getCmp('saveButton').enable();
                // Ext.getCmp('saveButton1').enable();
                var success = Ext.decode(response.responseText).success;
                var scriptExits = Ext.decode(response.responseText).scriptExits;
                var getUUID = Ext.decode(response.responseText).newUUID;
                if (getUUID != null && getUUID != '') {
                    newUUID = getUUID;
                }
                if (scriptExits) {
                    baseInfoOfScriptWin.show();
                    Ext.Msg.alert('提示', '服务名称重复！');
                    return;
                }
                var exitstype = Ext.decode(response.responseText).exitstype;
                if (exitstype) {
                    var mess = Ext.decode(response.responseText).message;
                    baseInfoOfScriptWin.show();
                    Ext.Msg.alert('提示', mess);
                    return;
                }
                if (newServiceId != 0) {
                    var iscriptuuid = Ext.decode(response.responseText).scriptuuid;
                    var iid = Ext.decode(response.responseText).newId;
                    newiid = iid;
                } else {
                    var iid = Ext.decode(response.responseText).iid;
                    var iscriptuuid = Ext.decode(response.responseText).iscriptuuid;
                }

                if (success && iid != "" && iid != null) {
                    bdyhzUUID = Ext.decode(response.responseText).iscriptuuid;
                    newServiceId = iid;
                    scriptuuid = iscriptuuid;
                    attachmentIds = [];
                    paramStore.load();
                    testparamStore.load();
                    attachmentStore.load();
                    attaTempStore.load();
                    paramRulesStore.load();
                    /!*
					 * if (refreshTry) { clearInterval(refreshTry); }
					 *!/
                    if (callback) {

                        callback();
                    } else {
                        Ext.Msg.alert('提示', '保存成功！');
                    }
                    if (scriptName1) {
                        maimPanels.setTitle(scriptName1);
                    } else {
                        maimPanels.setTitle("编辑框");
                    }
                    funcDesc.setValue(scriptDesc1);
                } else {
                    var ccc = "保存";
                    if (callback && callType) {
                        ccc = callType;
                    }
                    var hasTipKeyWord = Ext.decode(response.responseText).hasTipKeyWord;
                    var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;
                    if (hasScreenKeyWord) {
                        Ext.Msg.alert('提示', "脚本中存在屏蔽命令，无法" + ccc + "！<br>" + Ext.decode(response.responseText).message);
                        return;
                    }
                    if (hasTipKeyWord) {
                        openTipWindow(response, callback, callType);
                    }
                }
                // Ext.Msg.alert('提示', '保存成功！');
            },
            failure: function (result, request) {
                // Ext.getCmp('saveButton').enable();
                // Ext.getCmp('saveButton1').enable();
                secureFilterRs(result, "保存失败！");
            }
        });
    }*/

    function loadShelloutputhisInfo(requestId, iip, iport) {
        var surl = "getScriptExecOutputForTry.do";
        Ext.Ajax.request({
            url: surl,
            params: {
                isFromTryATry: isFromTryATry,
                requestId: requestId,
                agentIp: iip,
                agentPort: iport,
                input: cmdVForbasicEdit,
                gdSwitch: gdSwitch
            },
            success: function (response, opts) {
                var msg = Ext.decode(response.responseText).out;
                var status = Ext.decode(response.responseText).status;
                if (!Ext.isEmpty(Ext.util.Format.trim(msg))) {
                    $('#consoleLog').html(msg);
                    consolePanel.body.scroll('bottom', 300000);
                }
                if (status == 2) {
                    if (refreshTryForBasic) {
                        clearInterval(refreshTryForBasic);
                    }
                }
            },
            failure: function (response, opts) {
                $('#consoleLog').html('获取执行信息失败');
            }

        });
        cmdVForbasicEdit = null;
    }

    function loadShelloutputhisInfoCmd(requestId, iip, iport) {
        var surl = "getScriptExecOutputForTry.do";
        Ext.Ajax.request({
            url: surl,
            params: {
                isFromTryATry: 1,
                requestId: requestId,
                agentIp: iip,
                agentPort: iport,
                input: cmdVForbasicEdit,
                gdSwitch: gdSwitch
            },
            success: function (response, opts) {
                var msg = Ext.decode(response.responseText).out;
                var status = Ext.decode(response.responseText).status;
                if (!Ext.isEmpty(Ext.util.Format.trim(msg))) {
                    var consoleLogForCmdValue = $('#consoleLogForCmd').val();
                    msg = consoleLogForCmdValue + msg;
                    $('#consoleLogForCmd').html(msg);
                    consoleCmdPanel.body.scroll('bottom', 300000);
                }
                if (status == 2) {
                    if (refreshCmdForBasic) {
                        clearInterval(refreshCmdForBasic);
                    }
                    execCmdBtn.setDisabled(false);
                }
            },
            failure: function (response, opts) {
                $('#consoleLogForCmd').html('获取执行信息失败');
            }

        });
        cmdVForbasicEdit = null;
    }

    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }]
    });
    var tableName = new Ext.form.TextField({
        name: 'tablename',
        fieldLabel: '表名称',
        displayField: 'tablename',
        emptyText: '',
        labelAlign: 'right',
        labelWidth: 93,
        margin: '10 0 0 0',
        columnWidth: .98
    });
    var auditorStore_sm = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getPublishAuditorList.do?dbaasFlag=' + gdSwitch,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var wbChose;
    if (psbcBindAgentSwitch) {
        wbChose = .98;
    } else if (gdSwitch == 1) {
        wbChose = .98;
    } else {
        wbChose = .95;
    }
    var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
        editable: true,
        fieldLabel: "审核人",
        labelWidth: 93,
        labelAlign: 'right',
        store: auditorStore_sm,
        queryMode: 'local',
        columnWidth: wbChose,
        margin: '10 0 0 0',
        displayField: 'fullName',
        valueField: 'loginName',
        listeners: { //监听
            render: function (combo) {//渲染
                combo.getStore().on("load", function (s, r, o) {
                    combo.setValue(r[0].get('loginName'));//第一个值
                });
            },
            select: function (combo, records, eOpts) {
                var fullName = records[0].raw.fullName;
                combo.setRawValue(fullName);
            },
//			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_sm.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_sm.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_sm.setValue("");
//					 	return;
//					}
//				}
//
//			},
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var planTime_sm = Ext.create('Go.form.field.DateTime', {
        fieldLabel: '计划时间',
        format: 'Y-m-d H:i:s',
        hidden: true,
        labelWidth: 60,
// width:200,
        columnWidth: .98,
        margin: '10 0 0 0'
    });

    var planTime_MM = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '周期类型',
        editable: false,
        name: 'MM',
        padding: '0 5 0 0',
        matchFieldWidth: false,// 此处要有
        labelWidth: 85,
        columnWidth: .35,
        store: {
            fields: ['value'],
            data: [
                {"value": "按计划执行一次"},
                {"value": "间隔x日"},
                {"value": "间隔x小时"},
                {"value": "间隔x分钟"}
            ]
        },
        displayField: 'value',
        value: "间隔x日",
        listeners: {
            select: function (nf, newv, oldv) {
            },
            change: function (nf, newv, oldv) {
                if (newv == '间隔x日') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.show();
                    planTime_HH.show();
                    planTime_mi.show();
                } else if (newv == '间隔x小时') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.hide();
                    planTime_HH.show();
                    planTime_mi.show();
                } else if (newv == '间隔x分钟') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.show();
                } else if (newv == '按计划执行一次') {
                    planTime_DD.setValue('');
                    planTime_HH.setValue('');
                    planTime_mi.setValue('');
                    planTime_DD.hide();
                    planTime_HH.hide();
                    planTime_mi.hide();
                }
            }
        }
    });
    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        emptyText: '',
        labelWidth: 93,
        margin: '10 0 0 0',
        height: 55,
        columnWidth: .98,
        autoScroll: true
    });

    if (psbcBindAgentSwitch) {
        pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
            name: 'pubdesc',
            fieldLabel: '发布申请说明',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            height: 200,
            columnWidth: .98,
            autoScroll: true
        });
    }

    var levelStore_sm = Ext.create('Ext.data.Store', {
        fields: ['iid', 'scriptLevel'],
        data: [
            {"iid": "0", "scriptLevel": "白名单"},
            {"iid": "1", "scriptLevel": "高级风险"},
            {"iid": "2", "scriptLevel": "中级风险"},
            {"iid": "3", "scriptLevel": "低级风险"}
        ]
    });
    var scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 93,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '风险级别',
        margin: '10 0 0 0',
        displayField: 'scriptLevel',
        labelAlign: 'right',
        valueField: 'iid',
        editable: false,
        hidden: !scriptLevelSwitch,
        emptyText: '--请选择风险级别--',
        store: levelStore_sm
    });

    Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    appSysStore1.on('load', function () {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
    var appSysObj1 = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '应用系统',
        hidden: !reviewSwitch,
        emptyText: '--请选择应用系统--',
        multiSelect: true,
        labelWidth: 93,
        labelAlign: 'right',
        columnWidth: .95,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function (combo, records, eOpts) {
                if (records) {
                    chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                        chosedAppSys.push(records[i].data.id);
                    }
                }

            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var forbidden = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否禁用旧版本',
        inputValue: 1,
        width: 120,
        hidden: gdSwitch == 1,
        margin: '10 0 0 10'
    });

    if (psbcBindAgentSwitch) {
        forbidden = Ext.create('Ext.form.field.Checkbox', {
            boxLabel: '是否禁用旧版本',
            inputValue: 1,
            margin: '10 0 0 10',
            fieldLabel: '旧版本禁用',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            columnWidth: .98
        });
    }

    var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        hidden: !reviewSwitch,
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });

    var serviceAutoStore_sm = Ext.create('Ext.data.Store', {
        fields: ['iid', 'serviceAuto'],
        data: [
            {"iid": "0", "serviceAuto": "DBA"},
            {"iid": "1", "serviceAuto": "项目组"}
        ]
    });
    var serviceAuto_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'serviceAuto',
        labelWidth: 93,
        labelAlign: 'right',
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '服务权限',
        margin: '10 0 0 0',
        displayField: 'serviceAuto',
        valueField: 'iid',
        editable: false,
//        hidden: !reviewSwitch,
        emptyText: '--请选择服务权限--',
        store: serviceAutoStore_sm
    });
    Ext.define('systemModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'systemName',
            type: 'string'
        }, {
            name: 'itype',
            type: 'int'
        }]
    });

    var systemStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'systemModel',
        proxy: {
            type: 'ajax',
            url: 'getSystem.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        },
        listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;

                //根据全局的选择，初始化选中的列
                var selModel = systemgrid.getSelectionModel();
                for (var j = 0; j < selectedSysRecords.length; j++) {
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if (selectedSysRecords[j] == record.get("iid")) {
                            selModel.select(record, true, true);    //选中record，并且保持现有的选择，不触发选中事件
                        }
                    }
                }
            }
        }
    });
    var systemColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '业务系统名称', dataIndex: 'systemName', flex: 1},
        {text: '工程类型', dataIndex: 'itype', flex: 1, hidden: true}];
    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'cpId',
            type: 'int'
        }, {
            name: 'cpName',
            type: 'string'
        }, {
            name: 'ip',
            type: 'string'
        }, {
            name: 'iagentinfo_id',
            type: 'int'
        }]
    });

    var agentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'businessSystemComputerList.do',
            reader:
                {
                    type: 'json',
                    root: 'dataList'
                }
        },
        listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;

                //根据全局的选择，初始化选中的列
                var selModel = agentgrid.getSelectionModel();
                selectedAgentRecords.forEach(function (item) {
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if (item.agentid == record.get("cpId")) {
                            selModel.select(record, true, true);    //选中record，并且保持现有的选择，不触发选中事件
                        }
                    }
                })
            }
        }
    });

    var selModelsystem = Ext.create('Ext.selection.CheckboxModel', {
        mode: "MULTI",
        listeners: {
            select: function (selModel, record, index, eOpts) {
                sysid = record.get("iid");
                agentStore.load(
                    {
                        params: {
                            sysIdForQuery: record.get("iid"),
                            ipBetween: '',
                            ipEnd: '',
                            opersystype: record.get("itype") == null ? 0 : record.get("itype"),
                            cpName: ''
                        }
                    }
                );
                selectedSysRecords.push(record.get("iid"));
            },
            deselect: function (selModel, record, index, eOpts) {
                agentStore.removeAll();
                selectedAgentRecords.forEach(function (item) {
                    if (item.sysid == sysid) {
                        selectedAgentRecords.remove(item);
                    }
                })
                selectedSysRecords.remove(record.get('iid'));
            }
        }
    });
    var systemgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        title: '业务系统',
        store: systemStore,
        selModel: selModelsystem,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: systemColumns
    });

    var selModelagent = Ext.create('Ext.selection.CheckboxModel', {
        mode: "MULTI",
        listeners: {
            select: function (selModel, record, index, eOpts) {
                var dsid = record.get("cpId");
                var tmpRec = {};
                tmpRec.sysid = sysid;
                tmpRec.agentid = dsid;
                selectedAgentRecords.push(tmpRec);
                selectedSysRecords.remove(sysid);
                console.log("------------");
                console.log("selectedSysRecords:" + selectedSysRecords);
                console.log("selectedAgentRecords:" + selectedAgentRecords);
            },
            deselect: function (selModel, record, index, eOpts) {
                var dsid = record.get("cpId");
                selectedAgentRecords.forEach(function (item) {
                    console.log(item.sysid + '---' + item.agentid);
                    if (item.sysid == sysid && item.agentid == dsid) {
                        selectedAgentRecords.remove(item);
                    }
                })
                console.log("+++++++++++");
                console.log("selectedSysRecords:" + selectedSysRecords);
                console.log("selectedAgentRecords:" + selectedAgentRecords);
            }
        }
    });

    var agentColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: 'cpId', dataIndex: 'cpId', hidden: true},
        {text: '机器名', dataIndex: 'cpName', flex: 1},
        {text: '地址', dataIndex: 'ip', width: 120},
        {text: 'iagentinfo_id', dataIndex: 'iagentinfo_id', width: 120, hidden: true}];
    var agentgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        store: agentStore,
        width: 550,
        title: '设备',
        selModel: selModelagent,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left panel_space_right',
        columns: agentColumns
    });

    project_panel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1200,
        padding: '8 0 0 0',
        height: 260,
        layout: 'border',
        items: [systemgrid, agentgrid]
    });

    auditing_form_sm = Ext.create('Ext.form.Panel', {
        region: 'center',
        layout: 'anchor',
        bodyCls: 'x-docked-noborder-top',
//    	baseCls:'customize_gray_back',
        buttonAlign: 'center',
        border: false,
        items: [{
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [planTime_sm, versionAresource]
            }, {
                layout: 'column',
                border: false,
                padding: '5 10 5 0',
                items: [cfg_Display]
            }, {
                layout: 'column',
                border: false,
                padding: '5 10 5 0',
                items: [planTime_MM, planTime_DD, planTime_HH, planTime_mi]
            }, {
                layout: 'column',
                border: false,
                items: [scriptLevelCb_sm]
            }, {
                layout: 'column',
                border: false,
                items: [tableName]
            }, {
                layout: 'column',
                border: false,
                items: [appSysObj1, isEMscript]
            }, {
                layout: 'column',
                border: false,
                items: [auditorComBox_sm, forbidden]
            }, {
                layout: 'column',
                border: false,
                items: [serviceAuto_sm]
            }, {
                layout: 'column',
                border: false,
                items: [pubDesc_sm]
            }]
        }]
    });

    /************** 绑定用户组用 start **************/

    if (psbcBindAgentSwitch) {
        auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
            region: 'center',
            layout: 'anchor',
            bodyCls: 'x-docked-noborder-top',
            buttonAlign: 'center',
            border: false,
            items: [{
//	    	layout:'form',
                anchor: '98%',
                padding: '5 0 5 0',
                border: false,
                items: [{
                    layout: 'column',
                    border: false,
                    items: [planTime_sm]
                }, {
                    layout: 'column',
                    border: false,
                    items: [scriptLevelCb_sm],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [appSysObj1],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [isEMscript],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [auditorComBox_sm],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [forbidden],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [pubDesc_sm],
                    width: 400
                }]
            }]
        });
    }

    var agentColumnsScript = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName'},
        {text: 'IP', dataIndex: 'agentIp'},
        {text: '计算机名', dataIndex: 'hostName'},
        {text: '操作系统', dataIndex: 'osType'},
        {text: '端口', dataIndex: 'agentPort'},
        {text: '描述', dataIndex: 'agentDesc'}
        //{ text: '状态',  dataIndex: 'agentip'}
    ];

    Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'}
        ]
    });

    var agentStoreScript = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListAllScript.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    agentStoreScript.on('beforeload', function (store, options) {
        var new_params = {
            startIp: ipStart.getValue().trim(),
            endIp: ipEnd.getValue().trim()
        };

        Ext.apply(agentStoreScript.proxy.extraParams, new_params);
    });

    agentStoreScript.addListener('load', function (me, records, successful, eOpts) {
        if (agentiids.length > 0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
                function (index, record) {
                    if (agentiids.indexOf(record.get('iid')) > -1) {
                        chosedRecords.push(record);
                    }
                });
            agentgridScript.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
    });

    var agentgridScript = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        title: '设备',
        store: agentStoreScript,
        width: 720,
        selModel: selModelAgents,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: agentColumnsScript,
        listeners: {
            select: function (e, record, index, eOpts) {
                if (agentiids.indexOf(record.get('iid')) == -1) {
                    agentiids.push(record.get('iid'));
                }
                if (!selectedAgent.has(record.data)) {
                    selectedAgent.add(record.data);
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (agentiids.indexOf(record.get('iid')) > -1) {
                    agentiids.remove(record.get('iid'));
                }
                if (selectedAgent.has(record.data)) {
                    selectedAgent.delete(record.data);
                }
            }
        }
    });

    var rightArea = Ext.create('Ext.ux.ideal.form.Panel', {
            width: 730,
            height: 350,
            layout: 'border',
            region: 'east',
            autoScroll: true,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            border: true,
            columnLines: true,
            cls: 'window_border panel_space_left',
            items: [search_ip_form, agentgridScript]
        }
    );

    project_panelScript = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1200,
        height: 430,
        layout: 'border',
        items: [auditing_form_sm, rightArea]
    });

    /************** 绑定用户组用 end **************/


    if (gdSwitch == 1) {
        if (serviceType.getValue() == "应用" || serviceType.getValue() == "0" || serviceType.getValue() == 0) {
            planTime_sm.hide();
            tableName.hide();
            cfg_Display.hide();
            planTime_MM.hide();
            planTime_DD.hide();
            planTime_HH.hide();
            planTime_mi.hide();
        } else {
            planTime_sm.show();
            var newv = planTime_MM.getValue();
            if (newv == '间隔x日') {
                planTime_DD.show();
                planTime_HH.show();
                planTime_mi.show();
            } else if (newv == '间隔x小时') {
                planTime_DD.setValue('');
                planTime_HH.setValue('');
                planTime_mi.setValue('');
                planTime_DD.hide();
                planTime_HH.show();
                planTime_mi.show();
            } else if (newv == '间隔x分钟') {
                planTime_DD.setValue('');
                planTime_HH.setValue('');
                planTime_mi.setValue('');
                planTime_DD.hide();
                planTime_HH.hide();
                planTime_mi.show();
            } else if (newv == '按计划执行一次') {
                planTime_DD.setValue('');
                planTime_HH.setValue('');
                planTime_mi.setValue('');
                planTime_DD.hide();
                planTime_HH.hide();
                planTime_mi.hide();
            }
        }
        planTime_sm.hide();
        cfg_Display.hide();
        planTime_MM.hide();
        planTime_DD.hide();
        planTime_HH.hide();
        planTime_mi.hide();
        versionAresource.hide();
        if (checkRadioForBasicScriptEdit == 4) {
            if (chooseSqlExecModel.getValue() == '2') {
                tableName.show();
                tryTestbtn.hide();
            } else {
                tryTestbtn.show();
            }
            chooseSqlExecModel.show();
        } else {
            chooseSqlExecModel.hide();
            tableName.hide();
            tryTestbtn.show();
        }
    } else {
        versionAresource.hide();
        planTime_sm.hide();
        cfg_Display.hide();
        planTime_MM.hide();
        planTime_DD.hide();
        planTime_HH.hide();
        planTime_mi.hide();
        tableName.hide();
        chooseSqlExecModel.hide();
        serviceAuto_sm.hide();
        tryTestbtn.show();
    }

    function publishScript() {
        agentStoreScript.load();
        Ext.Ajax.request({
            url: 'scriptHasVersion.do',
            method: 'POST',
            params: {
                serviceId: newServiceId
            },
            success: function (response, opts) {
                var hasVersion = Ext.decode(response.responseText).hasVersion;
                if (hasVersion == 1) {
                    Ext.Msg.alert('提示', "该脚本已经发布过！");
                    return;
                } else {
                    Ext.Ajax.request({
                        url: 'scriptStatus.do',
                        method: 'POST',
                        params: {
                            serviceId: newServiceId
                        },
                        success: function (response, opts) {
                            if (checkRadioForBasicScriptEdit != 4 || (checkRadioForBasicScriptEdit == 4 && chooseSqlExecModel.getValue() == '1')) {
                                tableName.hide();
                            } else {
                                tableName.show();
                            }
                            var status = Ext.decode(response.responseText).status;
                            if (status == 2) {
                                Ext.Msg.alert('提示', "该脚本正处于审核中！");
                                return;
                            } else {
                                if (!publishAuditingSMWin) {
                                    //绑定用户组
                                    if (psbcBindAgentSwitch) {
                                        publishAuditingSMWin = Ext.create('widget.window', {
                                            title: '确认审核信息',
                                            closable: true,
                                            closeAction: 'hide',
                                            modal: true,
                                            width: 1200,
                                            height: 570,
                                            layout: {
                                                type: 'border',
                                                padding: 5
                                            },
                                            items: [project_panelScript],
                                            dockedItems: [{
                                                xtype: 'toolbar',
                                                //baseCls:'customize_gray_back',
                                                dock: 'bottom',
                                                layout: {pack: 'center'},
                                                items: [{
                                                    xtype: "button",
                                                    cls: 'Common_Btn',
                                                    text: "确定",
                                                    handler: submitAutiding
                                                }, {
                                                    xtype: "button",
                                                    cls: 'Common_Btn',
                                                    text: "取消",
                                                    handler: function () {
                                                        selectedSysRecords = [];
                                                        selectedAgentRecords = [];
                                                        systemStore.load();
                                                        agentStore.load(
                                                            {
                                                                params: {
                                                                    sysIdForQuery: 0,
                                                                    ipBetween: '',
                                                                    ipEnd: '',
                                                                    opersystype: 0,
                                                                    cpName: ''
                                                                }
                                                            }
                                                        );
                                                        agentiids = [];
                                                        selectedAgent.clear();
                                                        agentStoreScript.load();
                                                        agentgridScript.getSelectionModel().select([], false, false); //取消选中
                                                        this.up("window").close();
                                                    }
                                                }]
                                            }]
                                        });
                                    } else if (isProject) {
                                        publishAuditingSMWin = Ext.create('widget.window', {
                                            title: '确认审核信息',
                                            closable: true,
                                            closeAction: 'hide',
                                            modal: true,
                                            width: 1200,
                                            height: 550,
                                            layout: {
                                                type: 'border',
                                                padding: 5
                                            },
                                            items: [auditing_form_sm, project_panel],
                                            dockedItems: [{
                                                xtype: 'toolbar',
                                                //baseCls:'customize_gray_back',
                                                dock: 'bottom',
                                                layout: {pack: 'center'},
                                                items: [{
                                                    xtype: "button",
                                                    cls: 'Common_Btn',
                                                    text: "确定",
                                                    handler: submitAutiding
                                                }, {
                                                    xtype: "button",
                                                    cls: 'Common_Btn',
                                                    text: "取消",
                                                    handler: function () {
                                                        selectedSysRecords = [];
                                                        selectedAgentRecords = [];
                                                        systemStore.load();
                                                        agentStore.load(
                                                            {
                                                                params: {
                                                                    sysIdForQuery: 0,
                                                                    ipBetween: '',
                                                                    ipEnd: '',
                                                                    opersystype: 0,
                                                                    cpName: ''
                                                                }
                                                            }
                                                        );
                                                        this.up("window").close();
                                                    }
                                                }]
                                            }]
                                        });
                                    } else {
                                        var tempHt;
                                        if (gdSwitch == 1) {
                                            tempHt = 450;
                                        } else {
                                            tempHt = reviewSwitch ? 390 : 340;
                                        }
                                        publishAuditingSMWin = Ext.create('widget.window', {
                                            title: '确认审核信息',
                                            closable: true,
                                            closeAction: 'hide',
                                            resizable: false,
                                            modal: true,
                                            width: 600,
                                            minWidth: 350,
                                            height: tempHt,
                                            layout: {
                                                type: 'border',
                                                padding: 5
                                            },
                                            items: [auditing_form_sm],
                                            dockedItems: [{
                                                xtype: 'toolbar',
                                                border: false,
                                                dock: 'bottom',
                                                layout: {pack: 'center'},
                                                items: [{
                                                    xtype: "button",
                                                    cls: 'Common_Btn',
                                                    text: "确定",
                                                    handler: function () {
                                                        //判断输入的审核人是否合法 start
                                                        var displayField = auditorComBox_sm.getRawValue();
                                                        if (!Ext.isEmpty(displayField)) {
                                                            //判断输入是否合法标志，默认false，代表不合法
                                                            var flag = false;
                                                            //遍历下拉框绑定的store，获取displayField
                                                            auditorStore_sm.each(function (record) {
                                                                //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                                                                var data_fullName = record.get('fullName');
                                                                if (data_fullName == displayField) {
                                                                    flag = true;
                                                                    auditorComBox_sm.setValue(record.get('loginName'));
                                                                }
                                                            });
                                                            if (!flag) {
                                                                Ext.Msg.alert('提示', "输入的审核人非法");
                                                                auditorComBox_sm.setValue("");
                                                                return;
                                                            }
                                                        }
                                                        //判断输入的审核人是否合法  end
                                                        var planTime_MM_1 = planTime_MM.getValue();
                                                        var planTime_DD_1 = planTime_DD.getValue();
                                                        var planTime_HH_1 = planTime_HH.getValue();
                                                        var planTime_mi_1 = planTime_mi.getValue();
                                                        var tablename = tableName.getValue();

                                                        var planTime = planTime_sm.getRawValue();
                                                        var scriptLevel = scriptLevelCb_sm.getValue();
                                                        if (!scriptLevelSwitch) {
                                                            scriptLevel = 100;
                                                        }
                                                        var publishDesc = pubDesc_sm.getValue();
                                                        var auditor = auditorComBox_sm.getValue();
                                                        var isEmScript = isEMscript.getValue();
                                                        if (isEmScript) {
                                                            isEmScript = 1;
                                                        } else {
                                                            isEmScript = 0;
                                                        }
                                                        var isForbidden = forbidden.getValue();
                                                        if (isForbidden) {
                                                            isForbidden = 1;
                                                        } else {
                                                            isForbidden = 0;
                                                        }
//    		        			  				if(!planTime) {
//    		        			  					Ext.Msg.alert('提示', "没有填写计划时间！");
//    		        			  					return;
//    		        			  				}
                                                        var f1 = 0;
                                                        var serviceAuto = serviceAuto_sm.getValue();
                                                        if (checkRadioForBasicScriptEdit == 4 && chooseSqlExecModel.getValue() == '2') {
//        		    				  				if(planTime_MM_1=='间隔x日'){
//    													if (planTime_DD_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
//    														Ext.Msg.alert('提示',"天数必须填写！");
//    														return;
//    													}
//    												}else if(planTime_MM_1=='间隔x小时'){
//    													if (planTime_HH_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
//    														Ext.Msg.alert('提示',"小时必须填写！");
//    														return;
//    													}
//    												}else if(planTime_MM_1=='间隔x分钟'){
//    													if (planTime_mi_1==''||planTime_mi_1==null||planTime_mi_1=='null') {
//    														Ext.Msg.alert('提示',"分钟必须填写！");
//    														return;
//    													}
//    												}else{
//    													Ext.Msg.alert('提示',"请选择周期类型！");
//    													return;
//    												}
                                                            var stype = serviceType.getValue();
                                                            var mm = paramRulesStore.getRange();
                                                            if (stype == '1') {
                                                                if (mm.length == 0 && tablename != '') {
                                                                    Ext.Msg.alert('提示', "表中列定义，请填写表表中列信息！");
                                                                    return;
                                                                } else if (mm.length == 0 && tablename == '') {
                                                                    f1 = 1;
                                                                    Ext.Msg.alert('提示', "请填写表名称！");
                                                                    return;
                                                                } else if (mm.length > 0 && tablename == '') {
                                                                    Ext.Msg.alert('提示', "表中列定义完成，请填写表名称！");
                                                                    return;
                                                                } else if (mm.length > 0 && tablename != '') {
                                                                    var reg = new RegExp("^[a-zA-Z]");
                                                                    if (!reg.test(tablename)) {
                                                                        Ext.Msg.alert('提示', "表名称首字符必须是字母，请重新输入");
                                                                        return;
                                                                    }
                                                                }
                                                                if (mm.length > 0 && tablename.length > 30) {
                                                                    Ext.Msg.alert('提示', "表名称不能超过30个字符！");
                                                                    return;
                                                                }
                                                            }
                                                        }
                                                        if (gdSwitch == 1) {
                                                            if (!serviceAuto) {
                                                                Ext.Msg.alert('提示', "没有选择服务权限！");
                                                                return;
                                                            }

                                                        }
                                                        if (!scriptLevel) {
                                                            Ext.Msg.alert('提示', "没有选择风险级别！");
                                                            return;
                                                        }
                                                        if (!publishDesc) {
                                                            Ext.Msg.alert('提示', "没有填写发布申请说明！");
                                                            return;
                                                        }
                                                        if (publishDesc.length > 255) {
                                                            Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
                                                            return;
                                                        }
                                                        if (!auditor) {
                                                            Ext.Msg.alert('提示', "没有选择审核人！");
                                                            return;
                                                        }
                                                        if (f1 == 1) {
                                                            Ext.MessageBox.buttonText.yes = "确定";
                                                            Ext.MessageBox.buttonText.no = "取消";
                                                            Ext.Msg.confirm("确认发布", "没有定义表名及表中列，确认不定义采集结果表？",
                                                                function (id) {
                                                                    if (id == 'no') {
                                                                        return;
                                                                    }
                                                                });
                                                        }
                                                        var sIds = new Array();
                                                        sIds.push(newServiceId);
                                                        Ext.Ajax.request({
                                                            url: 'scriptPublishAuditingForEditScript.do',
                                                            method: 'POST',
                                                            params: {
                                                                sIds: sIds,
                                                                planTime: planTime,
                                                                scriptLevel: scriptLevel,
                                                                publishDesc: publishDesc,
                                                                auditor: auditor,
                                                                flag: 0, // 0-来着个人脚本库
                                                                isEmScript: isEmScript,
                                                                appSysIds: chosedAppSys,
                                                                planTime_Type: planTime_MM_1,
                                                                planTime_DD: planTime_DD_1,
                                                                planTime_HH: planTime_HH_1,
                                                                planTime_mm: planTime_mi_1,
                                                                serviceType: serviceType.getValue(),
                                                                isForbidden: isForbidden,
                                                                tablename: tablename,
                                                                switchFlag: gdSwitch,
                                                                radio: checkRadioForBasicScriptEdit,
                                                                sqlExecModel: chooseSqlExecModel.getValue(),
                                                                serviceAuto: serviceAuto
                                                            },
                                                            success: function (response, opts) {
                                                                var success = Ext.decode(response.responseText).success;
                                                                var message = Ext.decode(response.responseText).message;
                                                                if (!success) {
                                                                    Ext.MessageBox.alert("提示", message);
                                                                } else {
                                                                    Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                                                }
                                                                publishAuditingSMWin.close();
                                                                if (gdSwitch == 1) {
                                                                    this.up("window").close();
                                                                }
                                                            },
                                                            failure: function (result, request) {
                                                                secureFilterRs(result, "操作失败！");
                                                                publishAuditingSMWin.close();
                                                                if (gdSwitch == 1) {
                                                                    this.up("window").close();
                                                                }
                                                            }
                                                        });

                                                    }
                                                }, {
                                                    xtype: "button",
                                                    cls: 'Common_Btn',
                                                    text: "取消",
                                                    handler: function () {
                                                        this.up("window").close();
                                                    }
                                                }]
                                            }]
                                        });

                                    }
                                }
                                publishAuditingSMWin.show();
                                auditorStore_sm.load();
                                planTime_sm.setValue('');
                                scriptLevelCb_sm.setValue('');
                                pubDesc_sm.setValue('');
                                auditorComBox_sm.setValue('');
                                isEMscript.setValue(0);
                                forbidden.setValue(0);
                                appSysObj1.setValue('');
                                chosedAppSys = '';
                                selectedSysRecords = [];
                                selectedAgentRecords = [];
                                systemStore.load();
                                agentStore.load(
                                    {
                                        params: {
                                            sysIdForQuery: 0,
                                            ipBetween: '',
                                            ipEnd: '',
                                            opersystype: 0,
                                            cpName: ''
                                        }
                                    }
                                );
                                /*
								 * Ext.MessageBox.buttonText.yes = "确定";
								 * Ext.MessageBox.buttonText.no = "取消";
								 * Ext.Msg.confirm("确认发布", "是否确认发布该脚本",
								 * function(id){ if(id=='yes') { } });
								 */
                            }
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                            return;
                        }
                    });
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
                return;
            }
        });
    }


    function submitAutiding() {
        //判断输入的审核人是否合法 start
        var displayField = auditorComBox_sm.getRawValue();
        if (!Ext.isEmpty(displayField)) {
            //判断输入是否合法标志，默认false，代表不合法
            var flag = false;
            //遍历下拉框绑定的store，获取displayField
            auditorStore_sm.each(function (record) {
                //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                var data_fullName = record.get('fullName');
                if (data_fullName == displayField) {
                    flag = true;
                    auditorComBox_sm.setValue(record.get('loginName'));
                }
            });
            if (!flag) {
                Ext.Msg.alert('提示', "输入的审核人非法");
                auditorComBox_sm.setValue("");
                return;
            }
        }
        //判断输入的审核人是否合法  end
        var planTime = planTime_sm.getRawValue();
        var scriptLevel = scriptLevelCb_sm.getValue();
        if (!scriptLevelSwitch) {
            scriptLevel = 100;
        }
        var publishDesc = pubDesc_sm.getValue();
        var auditor = auditorComBox_sm.getValue();
        var isEmScript = isEMscript.getValue();

        if (isEmScript) {
            isEmScript = 1;
        } else {
            isEmScript = 0;
        }
        var isForbidden = forbidden.getValue();
        if (isForbidden) {
            isForbidden = 1;
        } else {
            isForbidden = 0;
        }

        if (!scriptLevel) {
            Ext.Msg.alert('提示', "没有选择风险级别！");
            return;
        }
        if (!publishDesc) {
            Ext.Msg.alert('提示', "没有填写发布申请说明！");
            return;
        }
        if (publishDesc.length > 255) {
            Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
            return;
        }
        if (!auditor) {
            Ext.Msg.alert('提示', "没有选择审核人！");
            return;
        }
        if (!psbcBindAgentSwitch) {
            if (isProject) {
                var records = systemgrid.getSelectionModel().getSelection();
                if (records.length <= 0) {
                    Ext.Msg.alert('提示', "请选择业务系统！");
                    return;
                }
            }
        }
        var sIds = new Array();
        sIds.push(newServiceId);
        //定义发布审核url
        var auditUrl = 'scriptPublishAuditing.do';
        //定义发布参数
        var auditParams = {
            sIds: sIds,
            planTime: planTime,
            scriptLevel: scriptLevel,
            publishDesc: publishDesc,
            auditor: auditor,
            flag: 0, //0-来着个人脚本库
            isEmScript: isEmScript,
            appSysIds: chosedAppSys,
            isForbidden: isForbidden,
            tablename: "",
            switchFlag: 0,
            radio: checkRadioForBasicScriptEdit,
            serviceAuto: 1,
            attachmentIds: attachmentIds,
            systemIds: selectedSysRecords,
            cpIds: JSON.stringify(selectedAgentRecords)
        };
        if (psbcBindAgentSwitch) {
            auditUrl = 'auditSuccessForScript.do';
            //获取选中的设备
            var IIdArray = [];
            var records = agentgridScript.getSelectionModel().getSelection();
            //设备不能为空
            if (records == null || records == '') {
                Ext.Msg.alert('提示', "请选择设备！");
                return;
            }
            //获取uuid
            // var uuidselect = scriptServiceReleaseGrid.getSelectionModel().getSelection();
            // var uuidVal = "";
            // Ext.each(uuidselect, function(item) {// 遍历
            //     uuidVal = item.data.uuid;
            // });
            Ext.each(records, function (item) {// 遍历
                IIdArray.push(item.data.iid);
            });
            var iidsStr = IIdArray.join(",");
            if (agentiids.length > 0) {
                iidsStr = agentiids.toString();
            }
            auditParams = {
                sIds: sIds,
                planTime: planTime,
                scriptLevel: scriptLevel,
                publishDesc: publishDesc,
                auditor: auditor,
                flag: 0, //0-来着个人脚本库
                isEmScript: isEmScript,
                appSysIds: chosedAppSys,
                isForbidden: isForbidden,
                tablename: "",
                switchFlag: 0,
                radio: checkRadioForBasicScriptEdit,
                serviceAuto: 1,
                attachmentIds: attachmentIds,
                systemIds: selectedSysRecords,
                cpIds: JSON.stringify(selectedAgentRecords),
                iidsStr: iidsStr,
                uuid: newUUID
            };
        }

        Ext.Ajax.request({
            url: auditUrl,
            method: 'POST',
            params: auditParams,
            success: function (response, opts) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (!success) {
                    Ext.MessageBox.alert("提示", message);
                } else {
                    Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                    publishAuditingSMWin.close();
                    selectedSysRecords = [];
                    selectedAgentRecords = [];
                    systemStore.load();
                    agentStore.load(
                        {
                            params: {
                                sysIdForQuery: 0,
                                ipBetween: '',
                                ipEnd: '',
                                opersystype: 0,
                                cpName: ''
                            }
                        }
                    );
                }


            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
                publishAuditingSMWin.close();
            }
        });
        agentiids = [];
        selectedAgent.clear();
        agentStoreScript.load();
        agentgridScript.getSelectionModel().select([], false, false); //取消选中
    }

    function savedoc(fileform) {
        // ignoreFlag ::
        // 0:正常保存
        // 1:忽略提醒命令，继续保存
        var sysId = bussCb.getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussTypeId = bussTypeCb.getValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var threeBussTypeId = threeBussTypeCb.getValue();
        //var sysName = bussCb.getRawValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        //var bussType = bussTypeCb.getRawValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var serverName = sName.getValue();
        var up = usePlantForm.getValue() || "";
        var fd = funcDesc.getValue();
        var auths = authCombox.getValue();
        var vdbType = dbType.getValue() || "";
        var vserviceType = serviceType.getValue() || "";
        var visExam = isExam.getValue() || "";

        var upfile = fileform.findField("file").getValue();
        if (upfile == '') {
            Ext.Msg.alert('提示', "请选择文档");
            return;
        }
        if (auths == '') {
            Ext.Msg.alert('提示', "请选权限");
            return;
        }
        if (!sysId && gdSwitch == 0) {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "请选择一级分类!");
            return;
        }
        if (!bussTypeId && gdSwitch == 0) {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "请选择二级分类!");
            return;
        }
        if (!threeBussTypeId && gdSwitch == 0 && scriptThreeBstypeSwitch) {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "请选择三级分类!");
            return;
        }


        if (serverName.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "服务名称不能为空!");
            return;
        }
        if (gdSwitch != 0 && vdbType.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "数据库类型不能为空!");
            return;
        }
        if (gdSwitch == 1 && vserviceType.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "服务类型不能为空!");
            return;
        }
        if (gdSwitch == 1 && visExam.trim() == '') {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "是否发起审核不能为空!");
            return;
        }
        /*
		 * if (scriptName1.trim() == '') { saveFromBottom = true;
		 * baseInfoOfScriptWin.show(); Ext.MessageBox.alert("提示", "脚本名称不能为空!");
		 * return; }
		 */
        // $('#s-n-t').html(Ext.util.Format.trim(scriptName1));
        if (up.length == 0) {
            saveFromBottom = true;
            baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "适用平台不能为空!");
            return;
        }
        if (fd.trim() == '') {
            Ext.MessageBox.alert("提示", "功能说明不能为空!");
            return;
        }

        Ext.MessageBox.wait("数据处理中...", "进度条");
        fileform.submit({
            url: 'uploadTemplate.do',
            params: {
                // itype:itype,
                itemplatedes: fd,
                iservename: serverName,
                iplatform: up,
                iauth: auths,
                iserveone: sysId,
                iservetwo: bussTypeId
            },
            success: function (form, action) {
                var respText = Ext.JSON.decode(action.response.responseText);
                var msg = respText.message;// 提示信息
                var isOk = respText.isOk;// 是否导入成功
                // 提示消息
                if (isOk) {
                    Ext.Msg.alert('提示', msg);
                    // 导入成功，跳 转到文档管理页面
                    // forwardUrl('templateManagement.do')
                    baseInfoOfScriptWin.close();
                    form.reset();
                    popNewTab("文档管理", 'templateManagement.do', {}, 10, true);
                    //
                } else {
                    Ext.Msg.alert('提示', msg);
                }

            },
            failure: function (form, action) {
                secureFilterRsFrom(form, action);
            }
        });
    }

    function forwardUrl(_url) {
        contentPanel.getLoader().load({
            url: _url,
            scripts: true
        });
    }

    function openTerminal(webshell_endpoint, server_type, options) {
        var tabTitle = options.host + '-'
        if (server_type == "1") {
            tabTitle += "SSH";
        } else if (server_type == "2") {
            tabTitle += "TELNET";
        }
        var tabCount = 0; // 页签组中当前功能tab页的数量
        var existedTab = null;
        tapPanelForConsole.items.each(function (item) {
            if (item.title == tabTitle) {
                tabCount = 1;
                existedTab = item;
            }
        });
        var mdfive = hex_md5(tabTitle);
        if (tabCount > 0) {
            var termold = $('#term-create-' + mdfive).data('term');
            var clientold = $('#term-create-' + mdfive).data('client');
            if (termold) {
                termold.destroy();
            }
            if (clientold) {
                clientold.close();
            }
        } else {
            existedTab = tapPanelForConsole.add({
                title: tabTitle,
                bodyStyle: 'background:#000;',
                border: false,
                height: consoleOneCmdPanel.getHeight() - 85,
                activeItem: 0,
                autoScroll: true,
                html: '<div id="term-create-' + mdfive + '"></div>',
                closable: true // 允许关闭
            });
        }
        tapPanelForConsole.setActiveTab(existedTab);

        var client = new WSSHClient(webshell_endpoint, server_type);
        var term = new Terminal({
            cols: 80,
            rows: parseInt((consoleOneCmdPanel.getHeight() - 85) / 16),
            screenKeys: true,
            useStyle: true
        });
        term.on('data', function (data) {
            client.sendClientData(data);
        });
        term.open();

        $(term.element).detach().appendTo('#term-create-' + mdfive);
        $('#term-create-' + mdfive).data('term', term);
        $('#term-create-' + mdfive).data('client', client);
        term.write('Connecting...');
        client.connect({
            onError: function (error) {
                term.write('Error: ' + error + '\r\n');
                console.debug('error happened');
            },
            onConnect: function () {
                client.sendInitData(options);
                client.sendClientData('\r');
                console.debug('connection established');
            },
            onClose: function () {
                term.write("\rconnection closed")
                console.debug('connection reset by peer');
            },
            onData: function (data) {
                term.write(data);
                console.debug('get data:' + data);
            }
        });
    }

    //baseInfoOfScriptWin.show();
    function connect(server_type, options) {
        if (webshell_endpoint == "") {
            Ext.MessageBox.alert("提示", "Web Shell服务没有配置，无法使用仿真终端功能！");
            return;
        }
        openTerminal(webshell_endpoint, server_type, options)

    }

    function clickNotdoc() {
        versionAresource.hide();
        planTime_sm.hide();
        cfg_Display.hide();
        planTime_MM.hide();
        planTime_DD.hide();
        planTime_HH.hide();
        planTime_mi.hide();
        if (gdSwitch == 1) {
            excepResult.hide();
            errExcepResult.hide();
        } else {
            errExcepResult.show();
            excepResult.show();
        }


        authCombox.hide();
        docfile.hide();
        scName.show();
        tryTestbtn.disabled = false;
        publishBtn.disabled = false;
        saveBtn.disabled = false;
    }

    function clickdoc() {
        authCombox.show();
        scriptfile.hide();
        docfile.show();
        scName.hide();
        errExcepResult.hide();
        excepResult.hide();

        tryTestbtn.disabled = true;
        publishBtn.disabled = true;
        saveBtn.disabled = true;
    }

    function addParam() {
        var store = paramGrid.getStore();
        var ro = store.getCount();
        var p = {
            iid: '',
            paramOrder: ro + 1,
            paramType: 'IN-string',
            parameterName: '',
            paramDefaultValue: '',
            paramDesc: ''
        };
        store.insert(0, p);
        paramGrid.getView().refresh();
    }

    function StringToPassword(strs) {
        if (strs && strs != null & strs != '') {
            var password = '';
            for (var i = 0; i < strs.length; i++) {
                password = password + '●';
            }
            return password;
        } else {
            return '';
        }
    }

    function addOut() {
        var store = outruleGrid.getStore();
        var ro = store.getCount();
        var p = {
            iid: '',
            paramRuleOrder: ro + 1,
            paramRuleIn: '',
            paramRuleOut: '',
            paramRuleType: 0,
            paramRuleLen: 50,
            paramRuleDesc: ''
        };
        store.insert(0, p);
        outruleGrid.getView().refresh();
    }

    dbType.hide();
    serviceType.hide();
    isExam.hide();
    outruleGrid.hide();


    // function checkFunction() {
    //     editor.save();
    //     var scriptContent = document.getElementById('code').value;
    //     var scripttype = "sh";
    //     if (checkRadioForBasicScriptEdit == '0') {
    //         scripttype = "sh";
    //     } else if (checkRadioForBasicScriptEdit == '1') {
    //         scripttype = "bat";
    //     } else if (checkRadioForBasicScriptEdit == '2') {
    //         scripttype = "perl";
    //     } else if (checkRadioForBasicScriptEdit == '3') {
    //         scripttype = "py";
    //     } else if (checkRadioForBasicScriptEdit == '4') {
    //         scripttype = "sql";
    //     } else if (checkRadioForBasicScriptEdit == '6') {
    //         scripttype = "ps1";
    //     }
    //
    //     Ext.Ajax.request({
    //         url: 'checkScriptForRules.do',
    //         method: 'POST',
    //         params: {
    //             scriptContent: scriptContent,
    //             scripttype: scripttype
    //         },
    //         success: function (response, request) {
    //             var success1 = Ext.decode(response.responseText).success;
    //             if (success1) {
    //                 Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    //             } else {
    //                 Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    //             }
    //         }
    //     });
    // }

    function chosedExecUser(record) {
        $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"), execUserConfigForm.getForm().findField('execUserName').getRawValue());
        if (isSumpAgentSwitch == true) {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getRawValue());
        } else {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getValue());
        }
        record.commit();
        execUserConfigWindow.hide();
    }

    function checkIsNotEmptyAndUndefined(str) {
        if (trim(str) == "" && trim(str) == "undefined")
            return false;
        else
            return true;
    }


    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(maimPanels);
        if (Ext.isIE) {
            CollectGarbage();
        }
    })


});

function versionAndResource() {
    Ext.create('Ext.window.Window', {
        title: '版本与资源组配置',
        modal: true,
        closeAction: 'destroy',
        constrain: true,
        autoScroll: true,
        width: 500,
        height: 450,
        draggable: false,// 禁止拖动
        resizable: false,// 禁止缩放
        layout: 'fit',
        loader: {
            url: 'goVersionAndResource.do',
            params: {
                serviceId: newiid
            },
            autoLoad: true,
            scripts: true
        }
    }).show();
}
function checkLength(strTemp)
{
    var i,sum;
    sum=0;
    for(i=0;i<strTemp.length;i++)
    {
        if ((strTemp.charCodeAt(i)>=0) && (strTemp.charCodeAt(i)<=255))
            sum=sum+1;
        else
            sum=sum+2;
    }
    return sum;
}