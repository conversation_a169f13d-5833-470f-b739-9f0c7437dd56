Ext.onReady(function() {
	destroyRubbish();
	var search_formForTry;
	var scriptTry_store;
	var warnningGrid;
	var publishAuditingSMWin;
	var auditing_form_sm;
	var planTime_sm;
	var scriptLevelCb_sm;
	var auditorComBox_sm;
	var auditorStore_sm;
	var pubDesc_sm;
	var	warnningWin;
	var myhight;
	var radio=1;
	var chosedGroupIds =[];
	var chosedGroupNames =[];
	var chosedGroupWin;
	var attachmentIds = [];
	var workitemid=0;
	var uploadProcessWin;
	let selectedRecords = new Set();
	var execUserConfigWindow;
    var execUserConfigForm = null;
    var execUserNameText = null;
	
	if(reviewSwitch && isAttach){
		myhight=590;
	}else if(reviewSwitch){
		myhight=390;
	}else if(isAttach){
		myhight=530;
	}else{
		myhight=330;
	}
	
	pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        labelAlign:'right',
        emptyText: '',
        labelWidth: 93,
        margin : '10 0 0 0',
        maxLength: 255, 
        height: 55,
        columnWidth:.98,
        autoScroll: true
    });
	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do?dbaasFlag=0',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "审核人",
	    labelWidth: 93,
//	    padding: 5,
	    labelAlign:'right',
	    store: auditorStore_sm,
	    queryMode: 'local',
//	    width: 200,
	    columnWidth:.95,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    editable : true,
	    valueField: 'loginName',//,
	    listeners: { //监听 
	        render : function(combo) {//渲染 
	            combo.getStore().on("load", function(s, r, o) { 
	                combo.setValue(r[0].get('loginName'));//第一个值 
	            });
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            } 
	    } 
	  });
	
	planTime_sm = Ext.create('Go.form.field.DateTime',{
	    fieldLabel:'计划时间',
	    format:'Y-m-d H:i:s',
	    labelWidth : 65,
	    hidden:true,
//	    width:200,
	    columnWidth:.98,
	    margin : '10 0 0 0'
	  });
	var levelStore_sm = Ext.create('Ext.data.Store', {
	    fields: ['iid', 'scriptLevel'],
	    data : [
	        {"iid":"0", "scriptLevel":"白名单"},
	        {"iid":"1", "scriptLevel":"高级风险"},
	        {"iid":"2", "scriptLevel":"中级风险"},
	        {"iid":"3", "scriptLevel":"低级风险"}
	    ]
	});
	scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 93,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '风险级别',
        margin : '10 0 0 0',
        displayField: 'scriptLevel',
        valueField: 'iid',
        editable: false,
        labelAlign : 'right',
        hidden : !scriptLevelSwitch,
        emptyText: '--请选择风险级别--',
        store: levelStore_sm
    });
	
	var forbidden = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否禁用旧版本',
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });
    
    var seName = new Ext.form.TextField({
		name : 'serviceName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		labelWidth : 65,
		width :'19%',
        labelAlign : 'right',
//        value: filter_serviceName,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var scName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel : '脚本名称',
		emptyText : '--请输入脚本名称--',
		labelWidth : 65,
		width :'19%',
        labelAlign : 'right',
//        value: filter_serviceName,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var tryStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"1", "name":"未测试"},
			{"id":"2", "name":"已测试"},
			{"id":"3", "name":"已打回"}
		]
	});
    
    var state = Ext.create('Ext.form.field.ComboBox', {
		name : 'state',
		labelWidth : 65,
		queryMode : 'local',
		fieldLabel : '测试状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择测试状态--',
		store : tryStatusStore,
		width : '12%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    search_formForTry = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls:'customize_gray_back',
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items: [seName,
			        scName,
			        state,
            {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls : 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            }
            ]
		}]
        
    });

    Ext.define('scriptTryData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },{
            name: 'iscriptIid',
            type: 'string'
        },
        {
            name: 'iScriptUuid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'execUserName',
            type: 'string'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
        	name: 'createUserName',
        	type: 'string'
        },
        {
        	name: 'ibussid',
        	type: 'int'
        },
        {
        	name: 'ibusstypeid',
        	type: 'int'
        },
        {
        	name: 'istatus',
        	type: 'int'
        },
        {
        	name: 'iscripttype',
        	type: 'string'
        },
        {
        	name: 'returntext',
        	type: 'string'
        }
        ]
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
 attachStore.on('beforeload', function(store, options) {
        var new_params = {
            wId: workitemid,
            ids: attachmentIds
        };

        Ext.apply(attachStore.proxy.extraParams, new_params);
    });
 attachStore.on('load', function(me, records, successful, eOpts) { 
	 attachmentIds = []
    	$.each(records, function(index, record){
    		attachmentIds.push(record.get('iid'));
    	});
    	console.log(attachmentIds);
	});
var attachColumns = [
   {
		text: '主键',
		dataIndex: 'iid',
		width: 40,
		hidden: true
	},
	{
		text: '附件名称',
		dataIndex: 'attachmentName',
		flex: 1,
		renderer:function (value, metaData, record, rowIdx, colIdx, store){  
			metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
			return value;  
		}
	},
	{
		menuDisabled: true,
		sortable: false,
		xtype: 'actioncolumn',
		width: 50,
		items: [{
			iconCls: 'attachment_delete',
			tooltip: '删除',
			handler: function(grid, rowIndex, colIndex) {
				var rec = attachStore.getAt(rowIndex);
				var a = [];
				a.push(rec.get('iid'));
				Ext.Ajax.request({
					url: 'deleteAttachment.do',
					method: 'POST',
					sync: true,
					params: {
					   iids: a,
					   wId:workitemid
					},
					success: function(response, request) {
					   var success = Ext.decode(response.responseText).success;
					   if (success) {
					       Ext.Msg.alert('提示', '删除成功！');
					       removeByValue(attachmentIds, rec.get('iid'));
					       attachStore.load();
					   } else {
					       Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
					   }
					},
					failure: function(result, request) {
					   secureFilterRs(result, "保存失败！");
					}
				});
			}
		}]
	}];
function removeByValue(arr, val) {
	  for(var i=0; i<arr.length; i++) {
	    if(arr[i] == val) {
	      arr.splice(i, 1);
	      break;
	    }
	  }
  }

var attachmentUploadWin = null;
function selectAttachmentFun(){
	var uploadForm;
	uploadForm = Ext.create('Ext.form.FormPanel',{
		border : false,
		items : [{
			xtype: 'filefield',
			name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
			id : 'attachment_idtry',
			fieldLabel: '选择文件',
			labelWidth: 65,
			anchor: '90%',
			margin: '10 10 0 40',
			buttonText: '浏览',
			multipleFn: function($this){

		         var typeArray = ["application/x-shockwave-flash","audio/MP3","image/*","flv-application/octet-stream"];

		         var fileDom = $this.getEl().down('input[type=file]');

		         fileDom.dom.setAttribute("multiple","multiple");

		         fileDom.dom.setAttribute("accept",typeArray.join(","));

		},
		    listeners:{
		    	   afterrender: function(){
		    		this.multipleFn(this);
		    		},
		    		change: function(){
		    			var fileDom = this.getEl().down('input[type=file]'); 
		    			var files = fileDom.dom.files; 
		    			var str = ''; 
		    			for(var i = 0;  i < files.length;  i++){
		    			 str += files[i].name;
		    			 str += ' ';
		    			} 
		    			 Ext.getCmp('attachment_idtry').setRawValue(str);    //files为组件的id
		    			 this.multipleFn(this);
		    			}
		    	}
		}],
		buttonAlign : 'center',
		buttons :[{
			text : '确定',
			handler :upExeclData
		},{
			text : '取消',
			handler : function(){
				this.up("window").close();
			}
		}]
	});

	attachmentUploadWin = Ext.create('Ext.window.Window', {
		title : '附件信息',
		modal : true,
		closeAction : 'destroy',
		constrain : true,
		autoScroll : true,
		width : 600,
		height: 160,
		items : [ uploadForm ],
		listeners : {
			close : function(g, opt) {
				uploadForm.destroy();
			}
		},
		/*
		 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
		 */layout : 'fit'
	});

	function upExeclData(){
		var form = uploadForm.getForm();
		var hdupfile=form.findField("files").getValue();
		if(hdupfile==''){
			Ext.Msg.alert('提示',"请选择文件...");
			return ;
		}
		uploadTemplate(form);
	}

	/** 自定义遮罩效果* */
	var myUploadMask = new Ext.LoadMask (contentPanel,
	{
		msg : "附件上传中..."
	});
	function uploadTemplate(form) {
		if (form.isValid()) {
			form.submit({
				url: 'uploadAttachmentFile.do',
				success: function(form, action) {
					var success=Ext.decode(action.response.responseText).success;
					var msg = Ext.decode(action.response.responseText).message;
                  if(success){
                	  var ids = Ext.decode(action.response.responseText).ids;
                	  attachmentIds.push.apply(attachmentIds,ids.split(","));
                  }else{
                	  Ext.Msg.alert('提示',msg);
                  }
                  attachmentUploadWin.close();
			      myUploadMask.hide();
			      attachStore.load();
				},
				failure: function(form, action) {
					var msg = Ext.decode(action.response.responseText).message;
					Ext.Msg.alert('提示',msg);
					myUploadMask.hide();
				}
			});
		}
	}
	attachmentUploadWin.show();
}		
    
	var isEMscript = Ext.create('Ext.form.field.Checkbox', {
	    boxLabel: '是否应急',
	    hidden : !reviewSwitch,
	    inputValue: 1,
	    width: 120,
	    margin: '10 0 0 10'
	});
	
	Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	
	var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	appSysStore1.on('load', function() {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
	
	var appSysObj1 = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '应用系统',
        emptyText : '--请选择应用系统--',
        hidden : !reviewSwitch,
        multiSelect: true,
        labelWidth: 93,
        labelAlign : 'right',
        columnWidth: .95,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function(combo, records, eOpts) {
                if (records) {
                	chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                    	chosedAppSys.push(records[i].data.id);
                    }
                } 

            },
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
	var selectedAttachmentButton = Ext.create ("Ext.Button",
			{
				cls : 'Common_Btn',
				disabled : false,
				text : '添加附件',
				handler : selectAttachmentFun
			});
    var attachGrid = Ext.create('Ext.grid.Panel', {
		region: 'south',
		height:200,
		width:580,
		title: '附件',
		autoScroll: true,
	    store : attachStore,
	    border:false,
	    hidden:isAttach?false:true,
	    columnLines : true,
	    cls: 'attachments customize_panel_back',
	    columns : attachColumns,
	    margin:'0 10 0 0',
	    emptyText: '没有附件',
	    dockedItems : [{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items:[
				'->',selectedAttachmentButton//添加附件按钮
			]
		}]
	});
    
    auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
    	layout : 'anchor',
    	region : 'center',
    	bodyCls : 'x-docked-noborder-top',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
//	    	layout:'form',
	    	anchor:'98%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,		    	
	    		items:[planTime_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[scriptLevelCb_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[appSysObj1,isEMscript]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_sm,forbidden]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[pubDesc_sm]
	    	}]
	    }]
	});
    
    var warnningStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    autoDestroy: true,
	    model: 'warnningModel',
	    proxy: {
	      type: 'ajax',
	      url: 'scriptCallSearch.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	warnningStore.on('beforeload', function (store, options) {
		var new_params = {  
			iid : warnId
	};
		Ext.apply(warnningStore.proxy.extraParams, new_params);
	});
	
	var warnningColumns = [{ text: '序号', xtype:'rownumberer', width: 40 },
		             		{ text: '主键',  dataIndex: 'iscriptIid',hidden:true},
		             		{ text: '服务名称',  dataIndex: 'serviceName',flex:1},
		             		{ text: '一级分类',  dataIndex: 'sysName',width:120},
		             		{ text: '二级分类',  dataIndex: 'bussName',width:100},
		                    { text: '版本',  dataIndex: 'version', width: 80,renderer:function(value,p,record,rowIndex){
		        				if(value) {
		        					return value;
		        				} else {
		        					return '无版本号';
		        				}
		        			}},
		                    { text: '创建用户',  dataIndex: 'user',width:120}
		             		];
	
	warnningGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : warnningStore,
	    border:false,
	    columnLines : true,
	    cls:'customize_panel_back',
	    columns : warnningColumns
	});

    scriptTry_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 30,
//        sorters : [{
//        	        property : 'state', 
//        	        direction : 'ASC' 
//        	    }],
        model: 'scriptTryData',
        proxy: {
            type: 'ajax',
            url: 'getScriptTryList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    scriptTry_store.on('beforeload', function(store, options) {
        var new_params = {
    		serviceName: seName.getValue(),
    		scriptName: scName.getValue(),
            tryState: state.getValue()
        };
        Ext.apply(scriptTry_store.proxy.extraParams, new_params);
    });

    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '脚本主键',
        dataIndex: 'iscriptIid',
        hidden: true
    },
    {
        text: '脚本id',
        dataIndex: 'iScriptUuid',
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        flex: 1
    },
    {
        text: '脚本名称',
        dataIndex: 'scriptName',
        flex: 1
    },
    {
        text: '发起人',
        dataIndex: 'createUserName',
        width: 150
    },
//    {
//        text: '一级分类id',
//        dataIndex: 'ibussid',
//        width: 150
//    },
//    {
//        text: '二级分类id',
//        dataIndex: 'ibusstypeid',
//        width: 150
//    },
//    {
//        text: '脚本类型',
//        dataIndex: 'iscripttype',
//        width: 150
//    },
    {
        text: '测试状态',
        dataIndex: 'state',
        width: 150,
        renderer:function(value,p,record,rowIndex){
			if (value=="1") {
				return '<font color="#F01024">未测试</font>';
			} else if (value=="2"){
				return '<font color="#0CBF47">已测试</font>';
			} else if(value=="3"){
				return '<font color="#FFA602">已打回</font>';
			} else if(value=="4"){
				return '<font color="">已发布</font>';
			} 
		}
    },
    {
    	text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'sysOperation',
		width : 210,
		items : [
		         {
					text : '测试',
					iconCls : 'execute',
					getClass : function(v, metadata, record) {
					var state = record.data.state;
					if(state!=1){
						return 'x-hidden';
					}
				},
					handler : function(grid, rowIndex) {
						var iid =  grid.getStore().data.items[rowIndex].data.iscriptIid;
						var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
						var bussId = grid.getStore().data.items[rowIndex].data.ibussId;
			            var bussTypeId  =grid.getStore().data.items[rowIndex].data.ibussTypeId;
			            var scriptType = grid.getStore().data.items[rowIndex].data.iscriptType;
			            var uuid = grid.getStore().data.items[rowIndex].data.iScriptUuid;
			            var execUserName = grid.getStore().data.items[rowIndex].data.execUserName;
						testTry(iid,serviceName,bussId,bussTypeId,scriptType,uuid,execUserName);
					}
				},
     			{
     				text : '通过',
     				iconCls : 'execute',
     				getClass : function(v, metadata, record) {
						var state = record.data.state;
						if(state!=1){
							return 'x-hidden';
						}
					},
     				handler : function(grid, rowIndex) {
     					var iid =  grid.getStore().data.items[rowIndex].data.iid;
     					passTry(iid);
     				}
     			},
     			{
     				text : '打回',
     				iconCls : 'execute',
     				getClass : function(v, metadata, record) {
						var state = record.data.state;
						if(state!=1){
							return 'x-hidden';
						}
					},
     				handler : function(grid, rowIndex) {
     					var iid =  grid.getStore().data.items[rowIndex].data.iid;
     					returnTry(iid);
     				}
     			},
     			{
     				text : '发布',
     				iconCls : 'execute',
     				getClass : function(v, metadata, record) {
						var state = record.data.state;
						if(state!=2){
							return 'x-hidden';
						}
						var status = record.data.istatus;
						if(status!=-1){
							return 'x-hidden';
						}
					},
     				handler : function(grid, rowIndex) {
     					var iid =  grid.getStore().data.items[rowIndex].data.iscriptIid;
     					var statusForPublish =  grid.getStore().data.items[rowIndex].data.state;
     					publishTry(iid,statusForPublish);
     				}
     			},
     			{
     				text : '查看',
     				iconCls : 'script_text',
     				handler : function(grid, rowIndex) {
     					var iscriptIid =  grid.getStore().data.items[rowIndex].data.iscriptIid; 
     					scriptCoatLook(iscriptIid);
     				}
     			}]
    }];
    
    function scriptCoatLook(iscriptIid) {
    	var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceInfo.do',
                params: {
                    iid: iscriptIid,
                    flag: 0,
                    hideReturnBtn: 1
                },
                autoLoad: true,
                scripts: true
            }
        });
        DetailWinTi.show();
                
            
    }
    
    function testTry(iid,serviceName,bussId,bussTypeId,scriptType,uuid,execUserName){
		var chosedAgentWin;
		var chosedAgentIds = new Array();
		var upldWin;
		var upLoadformPane = '';
		var cpdsMap = {};//<cpid,dsid>
	    var selCpId = -1;
		Ext.define('resourceGroupModel', {
		    extend : 'Ext.data.Model',
		    fields : [{
		      name : 'id',
		      type : 'int',
		      useNull : true
		    }, {
		      name : 'name',
		      type : 'string'
		    }, {
		      name : 'description',
		      type : 'string'
		    }]
		  });
		
		var resourceGroupStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    autoDestroy: true,
		    model: 'resourceGroupModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getResGroupForScriptService.do',
		      reader: {
		        type: 'json',
		        root: 'dataList',
		        totalProperty: 'totalCount'
		      }
		    }
		  });
		resourceGroupStore.on('load', function() { 
			var ins_rec = Ext.create('resourceGroupModel',{
				id : '-1',
				name : '未分组',
				description : ''
			}); 
			resourceGroupStore.insert(0,ins_rec);
		});
		var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
				{
				    fieldLabel : '资源组',
				    emptyText : '--请选择资源组--',
				    labelAlign : 'right',
				    labelWidth : 51,
				    width : '25.5%',
		            columnWidth:1,
				    multiSelect: true,
				    hidden:removeAgentSwitch,
				    store : resourceGroupStore,
				    displayField : 'name',
				    valueField : 'id',
				    triggerAction : 'all',
				    editable : false,
				    mode : 'local',
			    	listeners: {
		    	      change: function( comb, newValue, oldValue, eOpts ) {
		    	    	  pageBar.moveFirst();
		    	      }
			    	}
		});
		
		var agentStatusStore = Ext.create('Ext.data.Store', {
			fields: ['id', 'name'],
			data : [
				{"id":"-10000", "name":"全部"},
				{"id":"0", "name":"正常"},
				{"id":"1", "name":"异常"},
				{"id":"2", "name":"升级中"}
			]
		});
		
		var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
			name : 'agentStatus',
			labelWidth : 79,
			queryMode : 'local',
			fieldLabel : 'Agent状态',
			displayField : 'name',
			valueField : 'id',
			editable : false,
			emptyText : '--请选择Agent状态--',
			store : agentStatusStore,
			width : '25.5%',
			labelAlign : 'right',
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
	        }
		});
		
		Ext.define('appNameModel', {
	    	extend: 'Ext.data.Model',
	    	fields : [ {
	    		name : 'appName',
	    		type : 'string'
	    	}]
	    });
		
		var app_name_store = Ext.create('Ext.data.Store', {
			autoLoad: true,
			model: 'appNameModel',
			proxy: {
				type: 'ajax',
				url: 'getAgentAppNameList.do?envType=0',
				reader: {
					type: 'json',
					root: 'dataList'
				}
			}
		});
		
		var app_name = Ext.create('Ext.form.ComboBox', {
			name : 'appname',
		    fieldLabel: "应用名称",
		    emptyText : '--请选择应用名称--',
		    store: app_name_store,
		    hidden: !CMDBflag,
		    queryMode: 'local',
		    width: "24.5%",
		    displayField: 'appName',
		    valueField: 'appName',
		    labelWidth : 65,
			labelAlign : 'right',
			listeners: {
				beforequery : function(e){
		            var combo = e.combo;
		              if(!e.forceAll){
		              	var value = Ext.util.Format.trim(e.query);
		              	combo.store.filterBy(function(record,id){
		              		var text = record.get(combo.displayField);
		              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
		              	});
		              combo.expand();
		              return false;
		              }
		         },
		         specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar1.moveFirst();
			                }
			    }
			}
		  });
		
		var agent_ip = new Ext.form.TextField({
			name : 'agentip',
			fieldLabel : 'Agent IP',
			displayField : 'agentip',
			emptyText : '--请输入Agent IP--',
			labelWidth : 70,
			labelAlign : 'right',
			width : '25.7%',
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
	        }
		});
		var host_name = new Ext.form.TextField({
			name : 'hostname',
			fieldLabel : '计算机名',
			displayField : 'hostname',
			emptyText : '--请输入计算机名--',
			labelWidth : 65,
			labelAlign : 'right',
			width : '24.6%',
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
	        }
		});
		
		Ext.define('sysNameModel', {
	    	extend: 'Ext.data.Model',
	    	fields : [ {
	    		name : 'sysName',
	    		type : 'string'
	    	}]
	    });
		
		var sys_name_store = Ext.create('Ext.data.Store', {
			autoLoad: true,
			model: 'sysNameModel',
			proxy: {
				type: 'ajax',
				url: 'getAgentSysNameList.do?envType=0&switchFlag=0',
				reader: {
					type: 'json',
					root: 'dataList'
				}
			}
		});
		
		var sys_name = Ext.create('Ext.form.ComboBox', {
			name : 'sysname',
		    fieldLabel: "名称",
		    emptyText : '--请选择名称--',
		    store: sys_name_store,
		    queryMode: 'local',
		    hidden: !CMDBflag,
		    width: "25%",
		    displayField: 'sysName',
		    valueField: 'sysName',
		    labelWidth : 37,
			labelAlign : 'right',
			listeners: {
				beforequery : function(e){
		            var combo = e.combo;
		              if(!e.forceAll){
		              	var value = Ext.util.Format.trim(e.query);
		              	combo.store.filterBy(function(record,id){
		              		var text = record.get(combo.displayField);
		              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
		              	});
		              combo.expand();
		              return false;
		              }
		         },
		        specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar1.moveFirst();
			                }
			    }
			}
		  });
		
		var os_type = new Ext.form.TextField({
			name : 'ostype',
			fieldLabel : '操作系统',
			displayField : 'ostype',
			emptyText : '--请输入操作系统--',
			labelWidth : 79,
			labelAlign : 'right',
			width : CMDBflag?'25.5%':'24.2%',
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
	        }
		});
		var sysName1 = new Ext.form.TextField({
	        name: 'sysName1',
	        fieldLabel: '名称',
	        displayField: 'sysName1',
	        emptyText: '--请输入名称--',
	        labelWidth: 37,
	        labelAlign: 'right',
	        width :'24.5%',
	        listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
	        }
	    });
		var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
			region : 'north',
			bodyCls : 'x-docked-noborder-top',
			border : false,
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				border: false,
				items : [ sys_name, app_name, host_name,sysName1, os_type
				]
			},
			{
				xtype : 'toolbar',
				dock : 'top',
				border: false,
				items : [ agent_ip, resourceGroupObj, agentStatusCb,
					{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function(){
							pageBar.moveFirst();
						}
					},
					{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '清空',
						handler : function(){
							agent_ip.setValue('');
					    	app_name.setValue('');
							sys_name.setValue('');
							host_name.setValue('');
							os_type.setValue('');
							sysName1.setValue('');
					    	resourceGroupObj.setValue('');
					    	agentStatusCb.setValue('');
						}
					},{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '导入',
						handler : importExcel
					}
				]
			}]
		});
	    
	    function checkFile(fileName){
		    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
		    if(!file_reg.test(fileName)){  
		    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
		    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
		        return false;
		    }
		    return true;
		}
	    
	    function importExcel() {
			//销毁win窗口
			if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
				upldWin.destroy();
				upldWin = null;
			}
			
			if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
				upLoadformPane.destroy();
				upLoadformPane = null;
			}
			//导入文件Panel
			upLoadformPane =Ext.create('Ext.ux.ideal.form.Panel', {
		        width:370,
		        height:100,
		        bodyCls : 'x-docked-noborder-top',
			    frame: true,
				items: [
					{
						xtype: 'filefield',
						name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
						fieldLabel: '选择文件',
						labelWidth: 65,
						msgTarget: 'side',
						anchor: '100%',
						buttonText: '浏览'
					}
				],
				buttons: [
						{
							id:'upldBtnIdAudi',
							text: '导入Agent文件',
							handler: function() {
								var form = this.up('form').getForm();
								var upfile=form.findField("file").getValue();
				    			if(upfile==''){
				    				Ext.Msg.alert('提示',"请选择文件...");
				    				return ;
				    			}
				    			
				    			var hdtmpFilNam=form.findField("file").getValue();
				    			if(!checkFile(hdtmpFilNam)){
					    			  form.findField("file").setRawValue('');
					    			  return;
					    		}

								if (form.isValid()) {
									 Ext.MessageBox.wait("数据处理中...", "进度条");
									form.submit({
										url: 'importAgentForStart.do',
										params:{
											envType:0
					                	},
									    success: function(form, action) {
									       var msg = Ext.decode(action.response.responseText).message;
									       
									    	   var status = Ext.decode(action.response.responseText).status;
									    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
									    	   
									    	   if(status==1) {
									    		   if(matchAgentIds && matchAgentIds.length>0) {
									    			   Ext.MessageBox.buttonText.yes = "确定"; 
									    				Ext.MessageBox.buttonText.no = "取消"; 
									    			   Ext.Msg.confirm("请确认", msg, function(id){
											  				 if(id=='yes'){
										  						Ext.Msg.alert('提示', "导入成功！");
										  						agent_ip.setValue('');
														    	app_name.setValue('');
																sys_name.setValue('');
																host_name.setValue('');
																os_type.setValue('');
														    	resourceGroupObj.setValue('');
														    	agentStatusCb.setValue('');
														    	chosedAgentIds = matchAgentIds;
													    	   pageBar.moveFirst();
											  				 }
											    		   });
									    		   } else {
									    			   Ext.Msg.alert('提示-没有匹配项', msg);
									    		   }
									    		   
									    	   } else {
									    		   Ext.Msg.alert('提示', "导入成功！");
										    	   agent_ip.setValue('');
											    	app_name.setValue('');
													sys_name.setValue('');
													host_name.setValue('');
													os_type.setValue('');
											    	resourceGroupObj.setValue('');
											    	agentStatusCb.setValue('');
											    	chosedAgentIds =  matchAgentIds;
										    	   pageBar.moveFirst();
									    	   }
									    	   
									       upldWin.close();
									       return;
									    },
									    failure: function(form, action) {
									    	 secureFilterRsFrom(form, action);
									    }
									});
						         }
							}
						}, {
							text: '下载模板',
							handler: function() {
								window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
							}
						}
					]
			});
			//导入窗口
			upldWin = Ext.create('Ext.window.Window', {
			    title: '设备信息批量导入',
			    width: 400,
			    height: 200,
			    modal:true,
			    resizable: false,
			    closeAction: 'destroy',
			    items:  [upLoadformPane]
			}).show();
			upldWin.on("beforeshow",function(self, eOpts){
				var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
				form.reset();
			});
			
			upldWin.on("destroy",function(self, eOpts){
				upLoadformPane.destroy();
			});
		}

		Ext.define('agentModel', {
	        extend: 'Ext.data.Model',
	        idProperty: 'iid',
	        fields: [
	            {name: 'iid',     type: 'string'},
	            {name: 'sysName',     type: 'string'},
	            {name: 'appName',     type: 'string'},
	            {name: 'hostName',     type: 'string'},
	            {name: 'osType',     type: 'string'},
	            {name: 'agentIp',     type: 'string'},
	            {name: 'agentPort',     type: 'string'},
	            {name: 'agentDesc',     type: 'string'},
	            {name: 'agentDesc',     type: 'string'},
	            {name: 'agentState',     type: 'int'}
	        ]
	    });
	    
		var agent_store = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        pageSize: 51,
	        model: 'agentModel',
	        proxy: {
	            type: 'ajax',
	            url: 'getAllAgentList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
		
		var agent_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: true,
			pageSize: 30,
			model: 'agentModel',
			proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
	    
	    var agent_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
	                        { text: '主键',  dataIndex: 'iid',hidden:true},
	                        { text: '名称',  dataIndex: 'sysName',flex:1},
	                        { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,flex:1},
	                        { text: '计算机名',  dataIndex: 'hostName',flex:1},
	                        { text: 'IP',  dataIndex: 'agentIp',width:150},
	                        { text: '端口号',  dataIndex: 'agentPort',width:100},
	                        { text: '操作系统',  dataIndex: 'osType',width:140},
			                { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden: true,
	                        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	                                return value;  
	                            }
	                        },
			                { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
			                	var backValue = "";
			                	if(value==0){
			                		backValue = "Agent正常";
			                	}else if(value==1){
			                		backValue = "Agent异常";
			                	}
			                	return backValue;
			                }}
			               ];
	    
	    function openExecUserConfigData(record){
	    	if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
	    		if(isSumpAgentSwitch == true) {
	    			var sumpAgentStore = Ext.create('Ext.data.Store', {
	    				fields : [ 'iid', 'userName' ],
	    				autoLoad : true,
	    				proxy : {
	    					type : 'ajax',
	    					url : 'getSumpAgentUserList.do',
	    					reader : {
	    						type : 'json',
	    						root : 'dataList'
	    					}
	    				}
	    			});
	    			
	    			sumpAgentStore.on('beforeload', function(store, options) {
	    		        var queryparams = {
	    		        	agentId: record.get('iid')
	    		        };
	    		        Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
	    		    });
	    			
	    			execUserNameText = Ext.create('Ext.form.field.ComboBox', {
	    				name : 'execUserName',
	    				labelWidth : 65,
	    				queryMode : 'local',
	    				fieldLabel : '执行用户',
	    				width : 320,
	    				displayField : 'userName',
	    				valueField : 'iid',
	    				editable : true,
	    				typeAhead : true,
	    				emptyText : '--请选择执行用户--',
	    				store : sumpAgentStore,
	    		        labelAlign : 'right'
	    			});
	    			if(null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))){
	            		var sumpAgentCount = sumpAgentStore.getRange();
	            		var newExecUserName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+record.get("iid"));
	                    if(sumpAgentCount.length>0) {
	        				if(undefined == newExecUserName){
	        					execUserNameText.setRawValue(record.get('execuser'));
	        				} else {
	        					execUserNameText.setValue(newExecUserName);
	        				}
	                    } else {
	                    	if(undefined == newExecUserName){
	                    		execUserNameText.setValue(record.get('execuser'));
	                        	execUserNameText.setRawValue(record.get('execuser'));
	        				} else {
	        					execUserNameText.setValue(newExecUserName);
	                        	execUserNameText.setRawValue(newExecUserName);
	        				}
	                    }
	            	}
	    		} else {
	    			execUserNameText =  Ext.create ('Ext.form.TextField',
					{
						fieldLabel: '执行用户',
						labelAlign: 'right',
						name : "execUserName",
					    labelWidth : 65,
					    emptyText : '--请输入执行用户--',
					    width : 320,
					    xtype : 'textfield'
					});
	    			
	    			if(null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))){
	    				var newExecUserName1 = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+record.get("iid"));
	    				if(undefined == newExecUserName1){
	    					execUserNameText.setValue(record.get('execuser'));
	    				} else {
	    					execUserNameText.setValue(newExecUserName1);
	    				}
	            	}
	    		}
	    		
	    		execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel',{
					region : 'north',
				  	layout : 'anchor',
				  	//iqueryFun : queryBtnFun,
				  	buttonAlign : 'right',
				  	// baseCls:'customize_gray_back',
					collapsible : false,//可收缩
					collapsed : false,//默认收缩
				  	border : false,
				  	dockedItems : [{
							xtype : 'toolbar',
							dock : 'top',
							border : false,
							// baseCls:'customize_gray_back',
							items:[execUserNameText]
				  	},{
						xtype : 'toolbar',
						dock : 'top',
						border : false,
						// baseCls:'customize_gray_back',
						items:['->',{
							text : '确定',
							cls : 'Common_Btn',
							icon : '',
							handler: function(){  
								chosedExecUser(record);  
		                    }  
						}]
				  	}]
				});
	    		
	    		var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
					layout : 'border',
			        width : "100%",
			        height : "100%",
					border : false,
					items : [ execUserConfigForm ],
					cls:'customize_panel_bak'
				});
	    		
	    		execUserConfigWindow = Ext.create('Ext.window.Window', {
					title : "配置执行用户",
					modal : true,
					closeAction : 'destroy',
					constrain : true,
					autoScroll : false,
					//upperWin : errorTaskWin,
					width : 370,
					height : 180,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					items:  [execUserConfig_mainPanel]
				});
			}
	    	execUserConfigWindow.show();
	    }
	    
	    var agent_columns_chosed = [{ text: '序号', xtype:'rownumberer', width: 40 },
	        { text: '主键',  dataIndex: 'iid',hidden:true},
	        { text: '名称',  dataIndex: 'sysName',width:160},
	        { text: 'IP',  dataIndex: 'agentIp',width:110},
	        { text: '计算机名',  dataIndex: 'hostName',width:150},
	        { text: '操作系统',  dataIndex: 'osType',width:110},
	        { text: '端口号',  dataIndex: 'agentPort',width:60},
	        { text: '描述',  dataIndex: 'agentDesc',flex:1,
	        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	                return value;  
	            }
	        },
	        {
	    		text : '执行用户',
	    		dataIndex : 'execuser',
	    		width : 90,
	    		align: 'left',//整体左对齐
	    		renderer : function (value, metaData, record, rowNum)
	    	    {
	    			var displayValue = value;
	    			var recordedData = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+record.get("iid"));
	    			if(undefined == recordedData){
	    				if("" == value || undefined == value){
	    					displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
	    				} else {
	    					displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + displayValue + "</a>";
	    				}
	    			} else {
	    				if("" == recordedData){
	    					displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
	    				} else {
	    					displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + recordedData + "</a>";
	    				}
	    			}
	    	        return displayValue;
	    	    },
	    	    listeners : {
	    			click : function(a, b, c, d, e, record) {
	    				openExecUserConfigData(record);
	    			}
	    		}
	    	},
	        { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
	        	var backValue = "";
	        	if(value==0){
	        		backValue = "Agent正常";
	        	}else if(value==1){
	        		backValue = "Agent异常";
	        	}
	        	return backValue;
	        }}
	       ];
	    
	    agent_store.on('beforeload', function (store, options) {
		    var new_params = {  
		    	agentIp : Ext.util.Format.trim(agent_ip.getValue()),
		    	appName : app_name.getValue()==null?'':Ext.util.Format.trim(app_name.getValue()+""),
				sysName : CMDBflag?(sys_name.getValue()==null?'':Ext.util.Format.trim(sys_name.getValue()+"")):Ext.util.Format.trim(sysName1.getValue()),
				hostName : Ext.util.Format.trim(host_name.getValue()),
				osType : Ext.util.Format.trim(os_type.getValue()),
		    	rgIds:resourceGroupObj.getValue(),
		    	agentState: agentStatusCb.getValue(),
		    	flag: 0,
		    	switchFlag:0
		    };
		    
		    Ext.apply(agent_store.proxy.extraParams, new_params);
	    });
	    
	    agent_store_chosed.on('beforeload', function (store, options) {
	    	var new_params = {  
	    			agentIds : JSON.stringify(chosedAgentIds)
	    	};
	    	
	    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
	    });
//	    agent_store_chosed.on('load', function(store, options) {
//	    	agent_grid_chosed.getSelectionModel().select(0);  
//	    });
	    
	    agent_store.on('load', function (store, options) {
	    	var records=[];//存放选中记录
		  for(var i=0;i<agent_store.getCount();i++){
		      var record = agent_store.getAt(i);
		      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
	    	    {   
		    	  
		    	  if((+chosedAgentIds[ii])==record.data.iid)
		    		  {
		    		  records.push(record);
		    		  }
	    	    }   
		  }
		  agent_grid.getSelectionModel().select(records, false, true);//选中记录
	    });
	    Ext.define('dbModel', {
	        extend: 'Ext.data.Model',
	        idProperty: 'iid',
	        fields: [
	            {name: 'iid',        type: 'string'},
	            {name: 'driverClass',type: 'string'},
	            {name: 'dbUrl',      type: 'string'},
	            {name: 'dbUser',     type: 'string'},
	            {name: 'dbType',     type: 'string'}
	        ]
	    });
	   
	    var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
	                            { text: '主键',  dataIndex: 'iid',hidden:true},
	                            { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	                                return value;  
	                            }},
	                            { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	                                return value;  
	                            }},
	                            { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
	                            { text: 'DB类型',  dataIndex: 'dbType',width:110}];
	       
		 var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
			  	store: agent_store,
			  	dock: 'bottom',
				baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo: true,
				border:false,
				displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
				emptyMsg: "没有记录"
			  });
		
	    var agent_grid = Ext.create('Ext.grid.Panel', {
	    	region: 'center',
		    store:agent_store,
		    border:false,
		    columnLines : true,
		    columns:agent_columns,
		    bbar : pageBar,
		    cls:'customize_panel_back',
		    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		    listeners: {
		        select: function( e, record, index, eOpts ){ 
	            	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
	            		chosedAgentIds.push(record.get('iid'));
	            	}
	            },
		        deselect: function( e, record, index, eOpts ){ 
	            	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
	            		chosedAgentIds.remove(record.get('iid'));
	            	}
	            }
		    }
		});
	    
//	    var pageBarForAgentChosedGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//	    	store : agent_store_chosed,
//	    	dock : 'bottom',
//			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//			border:false,
//			displayInfo : true
//	    });
	    var dbinfo_store = Ext.create('Ext.data.Store', {
			autoLoad: false,
			pageSize: 50,
			model: 'dbModel',
			proxy: {
				type: 'ajax',
				url: 'getDbSqlDriverInfo.do',
				reader: {
					type: 'json',
					root: 'dataList'
				}
			}
		});
		var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			listeners : {
				select:function(selModel, record, index, eOpts) {
					 dbinfo_store.load({
				            params: {
				            	agentId: record.get("iid"),
				                agentIp: record.get("agentIp"),
				                agentPort: record.get("agentPort")
				            }
				        });
					 //当前选中cpid
					 selCpId = record.get("iid");
				 },
				 deselect:function(selModel, record, index, eOpts) {
					 dbinfo_store.removeAll();
					 var cpid = record.get("iid");
					 cpdsMap[cpid] = -1;//清空
					 selCpId = -1;
				 }
			}
		});
		
		var execUserForTry = new Ext.form.TextField({
	    	name: 'execUserForTry',
	    	id:'execUserForTry',
	    	value:execUserName,
	    	fieldLabel: '执行用户',
	    	emptyText: '-请输入执行用户-',
	    	labelWidth: 60,
	    	padding: '5',
	    	labelAlign: 'right',
	    	width: '20%'
	    });
		
	    var agent_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
	    	title: '已选服务器',
	    	region : 'west',
	    	store:agent_store_chosed,
	    	border:true,
	    	width:'100%',
	    	columnLines : true,
//	    	height: contentPanel.getHeight()*0.48,
//	    	height: 450,
	    	emptyText: '没有选择服务器',
	    	columns:agent_columns_chosed,
	    	ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    	cls:'window_border panel_space_top panel_space_left panel_space_right',
	    	selModel:selModelForagent_grid_chosed,
//	    	bbar : pageBarForAgentChosedGrid,
	    	listeners : {
		  	activate:function(tab){
				  			 resGroupFlag='false';
					  		 agent_store_chosed.load();
					  		 chosedGroupIds.splice(0,chosedGroupIds.length);
					  		 chosedGroupNames.splice(0,chosedGroupNames.length);
					}
			},
	    	dockedItems : [ {
				xtype : 'toolbar',
//				baseCls:'customize_gray_back',
				dock : 'top',
				items : [execUserForTry,{
					xtype : 'button',
					cls :'Common_Btn',
					text : '删除',
					handler : function() {
						var records = agent_grid_chosed.getSelectionModel().getSelection();
						if(records.length>0) {
		  					for(var i = 0, len = records.length; i < len; i++){
		  						chosedAgentIds.remove(records[i].get('iid'));
		  						$("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+records[i].get('iid'),"");	
		  					}
		  					agent_grid_chosed.ipage.moveFirst();
		  					pageBar.moveFirst();
		  				} else {
		  					Ext.Msg.alert('提示', "请选择服务器！");
	                        return;
		  				}
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '增加服务器',
					handler : function(){
						if(!chosedAgentWin) {
							chosedAgentWin = Ext.create('Ext.window.Window', {
						  		title : '增加服务器',
						  		autoScroll : true,
						  		modal : true,
						  		resizable : false,
						  		closeAction : 'hide',
						  		layout: 'border',
						  		cls:'window_border panel_space_left panel_space_right',
						  		width : contentPanel.getWidth()-190,
						  		height : contentPanel.getHeight(),
						  		items:[search_ip_form, agent_grid],
						  		dockedItems: [{
						            xtype: 'toolbar',
						            dock:'bottom',
						            layout: {pack: 'center'},
							        items: [{ 
							  			xtype: "button",
							  			text: "确定", 
							  			cls:'Common_Btn',
							  			margin:'6',
							  			handler: function () {
							  				var agent_grid_chosedRecord = agent_grid_chosed.getStore().getRange();
						  					if(agent_grid_chosedRecord.length>0) {
						 	  					for(var x = 0, xLen = agent_grid_chosedRecord.length; x < xLen; x++){
						 	  						var flag = false;
						 	  						var chosedRecordIid = agent_grid_chosedRecord[x].get('iid');
						 	  						for(var y = 0, yLen = chosedAgentIds.length; y < yLen; y++){
						 	  							var chosedNewIid = chosedAgentIds[y];
						 	  							if(chosedRecordIid == chosedNewIid){
						 	  								flag = true;
						 	  								break;
						 	  							}
						 	  						}
						 	  						if(!flag){
						 	  							$("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+chosedRecordIid,"");	
						 	  						}
						 	  					}
			  								}
						  					agent_store_chosed.load();
							  				this.up("window").close();
							  			}
							  		 },{ 
							  			xtype: "button",
							  			text: "关闭", 
							  			cls:'Common_Btn',
							  			margin:'6',
							  			handler: function () {
							  				this.up("window").close();
							  			}
							  		 }]
						  		}]
						  	});
						}
						chosedAgentWin.show();
						agent_store.load();
					}
				} ]
			}]
	    });
	    // 定义复选框
		var selModelForDbsource = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			mode : "SINGLE",
			listeners : {
				select:function(selModel2, record, index, eOpts) {
					var dsid = record.get("iid");
					if(selCpId != -1)
					{
						cpdsMap[selCpId] = dsid;//绑定
					}
				},
				deselect:function(selModel2, record, index, eOpts) {
					cpdsMap[selCpId] = -1;//清空
				}
			}
		});
	    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
		    store:dbinfo_store,
		    width:'70%',
		    region : 'center',
		    border:true,
		    columnLines : true,
		    columns:dbsource_columns,
		    cls:'window_border panel_space_top  panel_space_right',
		    selModel:selModelForDbsource
		});
	    dbinfo_store.on('load', function(store, options) {
	    	db_soucre_grid.getSelectionModel().select(0);  
	    });
	    
	    Ext.define('paramModel', {
	        extend: 'Ext.data.Model',
	        fields: [{
	            name: 'iid',
	            type: 'int'
	        },
	        {
	            name: 'paramType',
	            type: 'string'
	        },
	        {
	            name: 'paramDefaultValue',
	            type: 'string'
	        },
	        {
	            name: 'paramDesc',
	            type: 'string'
	        },
	        {
	            name: 'paramOrder',
	            type: 'int'
	        }]
	    });

	    var paramStore = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        autoDestroy: true,
	        pageSize: 30,
	        model: 'paramModel',
	        proxy: {
	            type: 'ajax',
	            url: 'getAllScriptParams.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
		function StringToPassword(strs){
			if(strs&&strs!=null&strs!=''){
				var password = '';
				for(var i=0;i<strs.length;i++){
					password = password + '●';
				}
				return password;
			}else{
				return '';
			}
		}
	    paramStore.on('beforeload', function(store, options) {
	        var new_params = {
	            scriptId: uuid
	        };

	        Ext.apply(paramStore.proxy.extraParams, new_params);
	    });
		var defultEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true
			})
		});
		var passwordEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true,
				inputType : 'password'
			})
		});
	    var paramColumns = [
	    {
	        text: '主键',
	        dataIndex: 'iid',
	        width: 40,
	        hidden: true
	    },
	    {
	        text: '类型',
	        dataIndex: 'paramType',
	        width: 100,
	        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	            return value;  
	        }
	    },
	      {
			xtype : 'gridcolumn',
			dataIndex : 'paramDefaultValue',
			width: 100,
			text : '参数值',
			getEditor : function(record) {
				if (record.get('paramType') != 'IN-string(加密)' ) {
					return defultEditor;
				} else {
					return passwordEditor;
				}
			},
			renderer : function(value, metaData, record, rowIdx, colIdx, store){  
	        	var backValue = "";
	        	if(record.get('paramType')== 'IN-string(加密)'){
	        		backValue = StringToPassword(value);
	        	}else{
	        		backValue = value;
	        	}
	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
	        	
	        	return backValue;
	        }
		},
	    
//	    {
//	        text: '参数值',
//	        dataIndex: 'paramDefaultValue',
//	        width: 70,
//	        editor: {
//	            allowBlank: true
//	        },
//	        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
//	            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
//	            return value;  
//	        }
//	    },
	    {
	        text: '顺序',
	        dataIndex: 'paramOrder',
	        width: 50,
	        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	            return value;  
	        }
	    },
	    {
	        text: '描述',
	        dataIndex: 'paramDesc',
	        flex: 1,
	        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	            return value;  
	        }
	    }];
	    
	    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
	        clicksToEdit: 2
	    });
	    if(scriptType=='sql'){
			agent_grid_chosed.setWidth((contentPanel.getWidth()-250)/2);
			db_soucre_grid.setWidth((contentPanel.getWidth()-250)/2);
			db_soucre_grid.show();
			}else{
				agent_grid_chosed.setWidth((contentPanel.getWidth()-290));
				db_soucre_grid.hide();
			}
						
		
		
			


//*********************************************增加资源组相关控件 start***************************************************************************

 Ext.define('groupModel', {
    extend: 'Ext.data.Model',
    idProperty: 'id',
    fields: [
        {name: 'id',     type: 'long'},
        {name: 'name',     type: 'string'},
        {name: 'execUserName',     type: 'string'},
        {name: 'description',     type: 'string'}
    ]
}); 
 var group_columns = [
    { text: '序号', xtype: 'rownumberer', width: 40 },
    { text: '主键', dataIndex: 'id', hidden: true },
    { text: '组名称', dataIndex: 'name', width: 160 },
    { text: '启动用户', dataIndex: 'execUserName', width: 160 },
    { text: '描述', dataIndex: 'description', width: 280 },
    {
	text : '操作',
	xtype : 'actiontextcolumn',
	flex:1,
	align : 'left',
	items : [{
				text : '详情',
				iconCls : 'execute',
				handler : function(grid, rowIndex) {
					var iid = grid.getStore().data.items[rowIndex].data.id;
					getAgentInfoByGroupId(iid);
				}}]
    }];
    /**
     * 获取资源组下的agent
     */
    function getAgentInfoByGroupId(iid){
    	Ext.define('agentModelByGroup', {
	        extend: 'Ext.data.Model',
	        idProperty: 'id',
	        fields: [
	            {name: 'id',     type: 'long'},
	            {name: 'ip',     type: 'string'},
	            {name: 'port',     type: 'string'},
	            {name: 'hostName',  type: 'string'}
	        ]
	    });
	    
    	var agentinfo_group_store = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        pageSize: 50,
	        model: 'agentModelByGroup',
	        proxy: {
	            type: 'ajax',
	            url: 'agentGroup/getServersForTaskApply.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	    
	    agentinfo_group_store.on('beforeload', function (store, options) {
		    var new_params = {  
		     	groupId:iid
		 };
		    
		   Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
	    });
		 var  agentinfo_columns_group = [
		     { text: '序号', xtype: 'rownumberer', width: 40 },
		     { text: '主键', dataIndex: 'id', hidden: true },
		     { text: 'Agent名称', dataIndex: 'hostName', flex:1},
		     { text: 'IP', dataIndex: 'ip', width: 160 },
		     { text: '端口', dataIndex: 'port', width: 160 }];
		     
		
    	var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
	    	region: 'center',
		    store:agentinfo_group_store,
		    border:false,
		    columnLines : true,
		    cls:'customize_panel_back',
		    columns:agentinfo_columns_group,
		    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//		    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    });
	    agentinfo_group_store.load();
        var  agentinfoGroupWin = Ext.create('Ext.window.Window', {
            title: '增加资源组',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'hide',
            layout: 'border',
            width: contentPanel.getWidth() - 190,
            height: contentPanel.getHeight(),
            items: [agentinfo_group_grid]
          });
          agentinfoGroupWin.show();
    }
    
    
  var group_columns_chosed = [
     { text: '序号', xtype: 'rownumberer', width: 40 },
    { text: '主键', dataIndex: 'id', hidden: true },
    { text: '组名称', dataIndex: 'name', width: 160 },
    { text: '启动用户', dataIndex: 'execUserName', width: 160 },
    { text: '描述', dataIndex: 'description', flex:1 }];
 var group_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: false,
			pageSize: 30,
			model: 'groupModel',
			proxy: {
	            type: 'ajax',
	            url: 'agentGroup/groups.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
var group_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'groupModel',
        proxy: {
            type: 'ajax',
            url: 'agentGroup/groups.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
 group_store_chosed.on('beforeload', function(store, options) {
   var   new_params = {
        groupIids: chosedGroupIds,
        from :"taskApplayChosed"
      }
    Ext.apply(group_store_chosed.proxy.extraParams, new_params);
  });

	group_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentGroupName : iresName.getValue()
//	    	iexecUser:iexecUser.getValue()
	    };
	    Ext.apply(group_store.proxy.extraParams, new_params);
	});
  group_store.on('load', function(store, options) {
    var records = [];//存放选中记录
    for (var i = 0; i < group_store.getCount(); i++) {
      var record = group_store.getAt(i);
      for (var ii = 0; ii < chosedGroupIds.length; ii++) {
        if (+chosedGroupIds[ii] == record.data.id) {
          records.push(record);
        }
      }
    }
    group_grid.getSelectionModel().select(records, false, true); //选中记录
  });

 
   var group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
	    store:group_store,
	    border:false,
	    columnLines : true,
	    cls:'customize_panel_back',
	    columns:group_columns,
	    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		listeners: {
		        select: function( e, record, index, eOpts ){ 
	            	if(chosedGroupIds.indexOf(record.get('id'))==-1) {
	            		chosedGroupIds.push(record.get('id'));
	            		chosedGroupNames.push(record.get('name'));
	            	}
	            },
		        deselect: function( e, record, index, eOpts ){ 
	            	if(chosedGroupIds.indexOf(record.get('id'))>-1) {
	            		chosedGroupIds.remove(record.get('id'));
	            		chosedGroupNames.remove(record.get('name'));
	            	}
	            }
		    }
    });
  
var iresName = new Ext.form.TextField({
		name : 'iresName',
		fieldLabel : '组名称',
		displayField : 'iresName',
		emptyText : '--请输入组名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25%'
	});
	var iexecUser = new Ext.form.TextField({
		name : 'iexecUser',
		fieldLabel : '启动用户',
		displayField : 'iexecUser',
		hidden:true,
		emptyText : '--启动用户--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25%'
	});
    
var search_group_form = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		border : false,
		iqueryFun : function(){
			group_grid.ipage.moveFirst();
	    },
		bodyCls : 'x-docked-noborder-top',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [ iresName, iexecUser,
			  {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function(){
						group_grid.ipage.moveFirst();
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function(){
						iresName.setValue('');
						iexecUser.setValue('');
					}
				}]
		}]
	});
    
    
var group_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
  title: '已选资源组',
  region: 'west',
  cls: 'window_border panel_space_top panel_space_left panel_space_right',
  store: group_store_chosed,
  border: true,
  width: '100%',
  columnLines: true,
  height: contentPanel.getHeight() * 0.48,
  ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
  emptyText: '没有选择资源组',
  columns: group_columns_chosed,
  selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
   listeners : {
		  	activate:function(tab){
		  			  resGroupFlag='true';
		  			  group_store_chosed.load();
		  			  for(var y = 0, yLen = chosedAgentIds.length; y < yLen; y++){
						 $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+chosedAgentIds[y],"");	  	
  					  }
			  		  chosedAgentIds.splice(0,chosedAgentIds.length);
				}
	},
  dockedItems: [
    {
      xtype: 'toolbar',
      dock: 'top',
      items: [
        {
          xtype: 'button',
          cls: 'Common_Btn',
          text: '删除',
          handler: function() {
            var records = group_grid_chosed.getSelectionModel().getSelection();
            if (records.length > 0) {
              for (var i = 0, len = records.length; i < len; i++) {
                chosedGroupIds.remove(records[i].get('id'));
                chosedGroupNames.remove(records[i].get('name'));
              }
              group_grid_chosed.ipage.moveFirst();
              group_grid.ipage.moveFirst();
            } else {
              Ext.Msg.alert('提示', '请选择资源组！')
              return
            }
          }
        },
        {
          xtype: 'button',
          cls: 'Common_Btn',
          text: '增加资源组',
          handler: function() {
            if (!chosedGroupWin) {
              chosedGroupWin = Ext.create('Ext.window.Window', {
                title: '增加资源组',
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                layout: 'border',
                width: contentPanel.getWidth() - 190,
                height: contentPanel.getHeight(),
                items: [search_group_form, group_grid],
                dockedItems: [
                  {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    layout: { pack: 'center' },
                    items: [
                      {
                        xtype: 'button',
                        text: '确定',
                        cls: 'Common_Btn',
                        margin: '6',
                        handler: function() {
                         	group_store_chosed.load();
                            this.up('window').close();
                        }
                      },
                      {
                        xtype: 'button',
                        text: '关闭',
                        cls: 'Common_Btn',
                        margin: '6',
                        handler: function() {
                          this.up('window').close();
                        }
                      }
                    ]
                  }
                ]
              });
            }
             
            chosedGroupWin.show();
            group_store.load();
          }
        }
      ]
    }
  ]
});



//*********************************************增加资源组相关控件 end***************************************************************************






			
			
			
			/** 已选窗口 资源组、单独agent tabPanel* */
			var tabPanelForChosedDevice = Ext.create ('Ext.tab.Panel',
			{
			    tabPosition : 'top',
			    region : 'center',
//			    region : 'west',
			    activeTab : 0,
		//	    cls:'customize_panel_back',
			    cls:'window_border panel_space_top panel_space_left panel_space_right',
			    width:'100%',
//			    height: contentPanel.getHeight()*0.58,
		//	    height : contentPanel.getHeight (),
			    border : false,
			    defaults :
			    {
				    autoScroll : false
			    },
			    items : [agent_grid_chosed,group_grid_chosed]
			   
			});
			
	    var centerPanel = Ext.create('Ext.panel.Panel', {
	    	region : 'center',
	    	border: true,
	        layout : 'border',
//	        cls:'customize_panel_back',
	        items: [tabPanelForChosedDevice,db_soucre_grid]
	    });
	    
	    var paramGrid = Ext.create('Ext.grid.Panel', {
	    	region : 'north',
	    	title: "脚本参数",
	        store: paramStore,
	        plugins: [cellEditing],
	        border: false,
	        columnLines: true,
//	        collapsible : true,
//	        collapsed: true,
	        height:150,
	        cls:'window_border panel_space_top panel_space_left panel_space_right',
	        columns: paramColumns
	    });
	    
	    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
	    	region : 'center',
	    	border: true,
	        layout : 'border',
	        height:contentPanel.getHeight()-140,
	        cls:'customize_panel_back',
	        items: [centerPanel, paramGrid]
	    });
	    function setMessage(msg){
			Ext.Msg.alert('提示', msg);
		}
	    var testChooseAgentWin = Ext.create('Ext.window.Window', {
		  		title : '选择测试服务器',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		layout: 'border',
		  		closeAction : 'destroy',
		  		width : contentPanel.getWidth()-250,
		  		height : contentPanel.getHeight(),
		  		items:[chooseAgentPanel],
		  		dockedItems: [{
		            xtype: 'toolbar',
		            baseCls:'customize_gray_back',
		            dock:'bottom',
		            layout: {pack: 'center'},
		            items: [{ 
		  			xtype: "button",
		  			text: "确定", 
		  			cls:'Common_Btn',
		  			margin:'6',
		  			handler: function () {
		  				var me = this;
		  				var agents = new Array();
		  				if(chosedAgentIds.length<=0 && chosedGroupNames.length<=0) {
		  					 setMessage('请选择服务器或资源组！');
	                        return;
		  				}
		  				var isOk = false;
		  				var agentStateMsg = "";
		  				
		  				if(resGroupFlag!='true'){
		  					// 检查agent状态
			  				Ext.Ajax.request({
				      			url : 'checkAgentState.do',
				      			method : 'POST',
				      			async: false,
				      			params : {
				      				agentIds: chosedAgentIds
				      			},
				      			success : function(response, request) {
				      				isOk = Ext.decode(response.responseText).isOk;
				      				agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
				      			},
				      			failure : function(result, request) {
				      				agentStateMsg = "检查Agent状态出错！";
				      			}
				      		});
		  				}
		  				
		  				function realTest() {
		  					if(scriptType=='sql'){
		  						 var records = agent_grid_chosed.getSelectionModel().getSelection();
		  		  				if(records.length>0) {
		  		  					for(var k = 0, len = records.length; k < len; k++){
		  		  						var cpidTmp = records[k].get('iid');
		  		  						var dsidTmp = cpdsMap[cpidTmp];
		  		  						if(null==dsidTmp||undefined==dsidTmp||""==dsidTmp||-1==dsidTmp)
		  		  						{
		  		  							var agentIp = records[k].get('agentIp');
		  		  				            var agentPort = records[k].get('agentPort');
		  		  							var dsErrMsg = "服务器【"+agentIp+":"+agentPort+"】没有选择数据源！";
		  		  							Ext.Msg.alert('提示', dsErrMsg);
		  		  	                        return;
		  		  						}
		  		  						var tmpRec = {
		  		  							iid:cpidTmp,
		  		  						    dsid:dsidTmp
		  		  						};
//		  	  							tmpRec.cpid=cpidTmp;
//		  	  							tmpRec.dsid=dsidTmp;
		  		  						agents.push(tmpRec);
		  		  					}
		  		  				}
		  					}else{
		  						var agentRecords = agent_grid_chosed.getStore().getRange();
			  					$.each(chosedAgentIds, function(i,v){
			  						var execUser = '';
			  						if(agentRecords.length > 0){
			  							var agenta;
			  							for(var x = 0, len = agentRecords.length; x < len; x++){
			  		  						var cpidTmp = agentRecords[x].get('iid');
			  		  						if(cpidTmp == v){
			  		  							execUser =  $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+cpidTmp);
			  		  						} 
				  		  					agenta = {
				  									iid: v,
				  									userName:execUser
				  							};
				  						}
			  							agents.push(agenta);
			  						} else {
			  							var agentb = {
			  									iid: v,
			  									userName:''
			  							};
			  							agents.push(agentb);
			  						}
				  				});
		  					}
		  					
			  				paramStore.sort('paramOrder', 'ASC');
		                	var m = paramStore.getRange(0, paramStore.getCount()-1);
			  		        var jsonDataPara = "[";
			  		        for (var i = 0, len1 = m.length; i < len1; i++) {
			  		            var ss = Ext.JSON.encode(m[i].data);
			  		            if (i == 0) jsonDataPara = jsonDataPara + ss;
			  		            else jsonDataPara = jsonDataPara + "," + ss;
			  		        }
			  		       jsonDataPara = jsonDataPara + "]";
		                	var aaaa = new Array();
		                    for (var j = 0, len2 = m.length;  j < len2; j++) {
		                        //var n = 0;
		                        var paramType = m[j].get("paramType") ? m[j].get("paramType").trim() : '';
		                        var paramDefaultValue = m[j].get("paramDefaultValue") ? m[j].get("paramDefaultValue").trim() : '';
		                        var paramDesc = m[j].get("paramDesc") ? m[j].get("paramDesc").trim() : '';
		                        if ("" == paramType) {
		                            setMessage('参数类型不能为空！');
		                            return;
		                        }
		                        if (fucCheckLength(paramDesc) > 250) {
		                            setMessage('参数描述不能超过250字符！');
		                            return;
		                        }

		                        if (paramType == 'int') {
		                            if (!checkIsInteger(paramDefaultValue)) {
		                                setMessage('参数类型为int，但参数默认值不是int类型！');
		                                return;
		                            }
		                        }
		                        if (paramType == 'float') {
		                            if (!checkIsDouble(paramDefaultValue)) {
		                                setMessage('参数类型为float，但参数默认值不是float类型！');
		                                return;
		                            }
		                        }
		                        aaaa.push(paramDefaultValue);
		                    }

			  				
		                    var scriptPara = aaaa.join("@@script@@service@@");
		                    //var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
			  				var dsid=0;
//			  				if(dsrecords.length>0){
//			  					dsid =  Ext.JSON.encode(dsrecords[0].data.dsId);
//			  				}
			  				Ext.MessageBox.wait("数据处理中...", "进度条");
			  				Ext.Ajax.request({
				      			url : 'execScriptServiceForSync.do',
				      			method : 'POST',
				      			timeout : 1000000, 
				      			params : {
				      				serviceId: iid,
				      				execUser: execUserForTry.getValue(),
				      				scriptPara: scriptPara,
				      				jsonData:JSON.stringify(agents),
				      				resGroupFlag:resGroupFlag,
				      				jsonDataPara: jsonDataPara,
				      				chosedGroupIds:chosedGroupIds,
				      				dbsourceid:dsid,
				      				ifrom :0,
				      				flag: 0
				      			},
				      			success : function(response, request) {
		  		                	me.up("window").close();
		    		  				Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
		    		  				var coatId =   Ext.decode(response.responseText).coatId;
		    		  				destroyRubbish1();
		    		  			    forwadScriptTest(coatId,scriptType);
		    		  				
				      			},
				      			failure : function(result, request) {
				      				Ext.Msg.alert('提示', '执行失败！');
				      			}
				      		});
		  				}
		  				
		  				if(isOk|| resGroupFlag=='true') {
		  					realTest();
		  				} else {
		  					Ext.Msg.confirm("请确认", agentStateMsg+"<br>选择的代理状态为异常，是否仍然进行测试？", function(id){
	  			    			if(id=='yes') {
	  			    				realTest();
	  			    			}
	  			    		});
		  				}
			        }
		  		}, { 
		  			xtype: "button", 
		  			text: "取消", 
		  			cls:'Common_Btn',
		  			handler: function () {
		  				this.up("window").close();
		  			}
		  		}]
		  	  }]
		  	}).show();
	}
    
    function destroyRubbish1() {
		contentPanel.clearListeners();
		contentPanel.getLoader().clearListeners();
	}
    
    function forwadScriptTest(coatId,scriptType){
	    contentPanel.getLoader().load({
	    	url: "forwardscriptserver.do",
	        scripts: true,
	        params: {
	        	coatid: coatId,
	            scriptType:scriptType,
	            forMyScript : '脚本测试'
	        }
	    });
	}
    
    function publishTry(iid,statusForPublish){
		if(statusForPublish != 2){
			Ext.Msg.alert('提示', '该脚本尚未测试通过！');
			return;
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认发布",  "是否确认发布该服务", function(id){
			if(id=='yes') {
					Ext.Ajax.request({
						url : 'scriptCallSearch.do',
						method : 'POST',
						params : {
							iid : iid
						},
						success: function(response, opts) {
							var success = Ext.decode(response.responseText).success;
							if(success) {//有需要提示的内容
								if (!warnningWin) {
									warnningWin = Ext.create('widget.window', {
										title: '提示信息,发布该脚本将影响以下作业，是否继续发布？',
										closable: true,
										closeAction: 'hide',
										modal: true,
										width: 600,
										minWidth: 350,
										height: 300,
										layout: {
											type: 'border',
											padding: 5
										},
										items: [warnningGrid],
										dockedItems : [ {
											xtype : 'toolbar',
											dock : 'bottom',
											layout: {pack: 'center'},
											items : [ {
												xtype: "button",
												cls:'Common_Btn',
												text: "是",
												handler: function () {
													this.up("window").close();
													openAuditWin(iid);
												}
											}, {
												xtype: "button",
												cls:'Common_Btn',
												text: "否",
												handler: function () {
													this.up("window").close();
												}
											}]
										}]
									});
								}
								warnningWin.show();
								warnningStore.load();
							} else {
								openAuditWin(iid);
							}
						},
						failure: function(result, request) {
							secureFilterRs(result,"操作失败！");
						}
					});
			}
		});
	}
    
    function openAuditWin(iid) {
		if (!publishAuditingSMWin) {
			publishAuditingSMWin = Ext.create('widget.window', {
				title: '确认审核信息',
				closable: true,
				closeAction: 'hide',
				modal: true,
				width: 600,
				minWidth: 350,
				height: myhight,
				layout: {
					type: 'border',
					padding: 5
				},
				items: [auditing_form_sm,attachGrid],
				dockedItems: [{
					xtype: 'toolbar',
					dock: 'bottom',
					layout: {pack: 'center'},
					items: [{
						xtype: "button",
						cls: 'Common_Btn',
						text: "确定",
						handler: function() {
							submitAutiding(iid);
		                }
					}, {
						xtype: "button",
						cls: 'Common_Btn',
						text: "取消",
						handler: function () {
							this.up("window").close();
							attachmentIds = [];
							attachStore.reload();
						}
					}]
				}]
			});

		}
		publishAuditingSMWin.show();
		auditorStore_sm.load();
		planTime_sm.setValue('');
		scriptLevelCb_sm.setValue('');
		pubDesc_sm.setValue('');
		auditorComBox_sm.setValue('');
		isEMscript.setValue(0);
		forbidden.setValue(0);
		appSysObj1.setValue('');
		chosedAppSys = '';
	}
    
    function returnTry(iid){
    	Ext.MessageBox.wait ("数据处理中...", "进度条");
    	Ext.MessageBox.show({
    		title : '打回原因',
    	    msg : '请输入打回原因:',
    	    width : 300,
    	    buttons : Ext.MessageBox.OKCANCEL,
    	    multiline : true,
    	    scope : this,
    	    fn : function(btn, reason, cfg){ 
    	         if (btn == 'ok' && Ext.isEmpty(Ext.util.Format.trim(reason))) { 
    	        	 var newMsg = '<span style="color:red">打回原因不能为空</span>';
    	             Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    	         } else if (btn =='ok') {
    	             var backInfo = Ext.util.Format.trim(reason);  
    	             Ext.Ajax.request (
              			{
              			    url : 'returnScriptTry.do',
              			    method : 'POST',
              			    params :
              			    {
              			    	iid : iid,
              			    	backInfo : backInfo
              			    },
	              			success : function(response, request) {
	              				var success = Ext.decode(response.responseText).success;
	      						if (success) {
	      							scriptTry_store.reload();
	      							Ext.Msg.alert('提示', '操作成功！');
	      						} else {
	      							Ext.Msg.alert('提示', '操作失败！');
	      						}
	      					},
	      					failure : function(result, request) {
	      						Ext.Msg.alert('提示', '操作失败！');
	      					}
              			});
    	         }
    	    }
    	})
    }
    
    function passTry(iid){
    	Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认通过",  "是否确认通过测试", function(id){
			if(id=='yes') {
				Ext.Ajax.request({
					url : 'passScriptTry.do',
					method : 'POST',
					params : {
						iid : iid
					},
					success : function(response, request) {
						var success = Ext.decode(response.responseText).success;
						if (success) {
							scriptTry_store.reload();
							Ext.Msg.alert('提示', '操作成功！');
						} else {
							Ext.Msg.alert('提示', '操作失败！');
						}
					},
					failure : function(result, request) {
						Ext.Msg.alert('提示', '操作失败！');
					}
				});
			}
		})
    }

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptTry_store,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });

    var scriptTry_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	autoScroll: true,
        store: scriptTry_store,
        cls:'customize_panel_back',
        border: false,
        columnLines: true,
		padding : grid_space,
        columns: scriptmonitor_columns,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar'
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptTry_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_formForTry, scriptTry_grid]
    });
    
    /**
	 * 发布审核确定
	 */
	function submitAutiding(iid) {
		//判断输入的审核人是否合法 start
		var displayField = auditorComBox_sm.getRawValue();
		if (!Ext.isEmpty(displayField)) {
			//判断输入是否合法标志，默认false，代表不合法
			var flag = false;
			//遍历下拉框绑定的store，获取displayField
			auditorStore_sm.each(function (record) {
				//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
				var data_fullName = record.get('fullName');
				if (data_fullName == displayField) {
					flag = true;
				}
			});
			if (!flag) {
				Ext.Msg.alert('提示', "输入的审核人非法");
				auditorComBox_sm.setValue("");
				return;
			}
		}
		//判断输入的审核人是否合法  end
		var planTime = planTime_sm.getRawValue();
		var scriptLevel = scriptLevelCb_sm.getValue();
		if (!scriptLevelSwitch) {
			scriptLevel = 100;
		}
		var publishDesc = pubDesc_sm.getValue();
		var auditor = auditorComBox_sm.getValue();
		var isEmScript = isEMscript.getValue();

		if (isEmScript) {
			isEmScript = 1;
		} else {
			isEmScript = 0;
		}
		var isForbidden = forbidden.getValue();
		if (isForbidden) {
			isForbidden = 1;
		} else {
			isForbidden = 0;
		}

		if (!scriptLevel) {
			Ext.Msg.alert('提示', "没有选择风险级别！");
			return;
		}
		if (!publishDesc) {
			Ext.Msg.alert('提示', "没有填写发布申请说明！");
			return;
		}
		if (publishDesc.length > 255) {
			Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
			return;
		}
		if (!auditor) {
			Ext.Msg.alert('提示', "没有选择审核人！");
			return;
		}
		Ext.Ajax.request({
			url: 'scriptPublishAuditing.do',
			method: 'POST',
			params: {
				sIds: iid,
				planTime: planTime,
				scriptLevel: scriptLevel,
				publishDesc: publishDesc,
				auditor: auditor,
				flag: 0, //0-来自个人脚本库
				isEmScript: isEmScript,
				appSysIds: chosedAppSys,
				isForbidden: isForbidden,
				tablename: "",
				switchFlag: 0,
				radio: radio,
				serviceAuto: 1,
				attachmentIds:attachmentIds
			},
			success: function (response, opts) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (!success) {
					Ext.MessageBox.alert("提示", message);
				} else {
					Ext.MessageBox.alert("提示", "请求已经发送到审核人");
//					scriptPublishsIds = [];
					selectedRecords.clear();
//					scriptiids = [];
					scriptTry_store.load();
					publishAuditingSMWin.close();
					attachmentIds = [];
					attachStore.reload();
				}
			},
			failure: function (result, request) {
				secureFilterRs(result, "操作失败！");
				publishAuditingSMWin.close();
				attachmentIds = [];
				attachStore.reload();
			}
		});
	}

    function clearQueryWhere() {
    	seName.setValue(""),
    	scName.setValue(''),
    	state.setValue('');
    }

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    function chosedExecUser(record){
    	$("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText"+record.get("iid"),execUserConfigForm.getForm().findField('execUserName').getRawValue());
    	if(isSumpAgentSwitch == true) {
    		record.set('execuser',execUserConfigForm.getForm().findField('execUserName').getRawValue());
    	} else {
    		record.set('execuser',execUserConfigForm.getForm().findField('execUserName').getValue());
    	}
    	record.commit();
    	execUserConfigWindow.hide();
    }
    
    function checkIsNotEmptyAndUndefined(str)
    {
        if(trim(str)== "" && trim(str)== "undefined")
            return false;
        else
            return true;
    }
    
});



