<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<%
	boolean expectValueSwitch = Environment.getInstance().getScriptExpectValueSwitch();
	boolean scriptLevelSwitch= Environment.getInstance().getScriptLevelSwitch();
	boolean scriptCheckRuleSwitch= Environment.getInstance().getScriptCheckRuleSwitch ();
	boolean scriptTemplateSwitch= Environment.getInstance().getScriptEditTemplateSwitch();
	boolean limitTwenty= Environment.getInstance().getLimitTwenty();
	boolean istrySwitch=Environment.getInstance().getScriptTrySwitch();
	boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
	boolean  createScript= Environment.getInstance().getCreateScriptSwitch();
	boolean  isSu= Environment.getInstance().getSuUserSwitch();
	boolean isProject=Environment.getInstance().getScriptProjectSwitch();
	boolean psbcBindAgent=Environment.getInstance().getScriptPsbcBindAgentSwitch();
	boolean templateSwich = Environment.getInstance().getTempUploadTabSwitch();
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	boolean gfScriptDirFunctionSwitch = Environment.getInstance().getGFScriptDirFunctionSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
	//功能分类
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
	// 脚本输出定义
	boolean scriptFunctionOutputSwitch = Environment.getInstance().getScriptFunctionOutputSwitch();
%>
<html>
<head>
<script type="text/javascript">
	var scriptFunctionOutputSwitch = <%=scriptFunctionOutputSwitch%>;
	var sForm;
	var psbcBindAgentSwitch = <%=psbcBindAgent%>;
	var isProject=<%=isProject%>;
	var checkRadioForBasicScriptEdit= 0;
	var expectValueSwitch = <%=expectValueSwitch%>;
	var scriptCheckRuleSwitch=<%=scriptCheckRuleSwitch%>;
	var scriptTemplateSwitch=<%=scriptTemplateSwitch%>;
	var limitTwenty=<%=limitTwenty%>;
	var istrySwitch = <%=istrySwitch%>;
	var isSu = <%=isSu%>;
	var sessionIdForBasicScriptEdit = '<%=request.getSession().getId()%>';
	<% if (null==request.getParameter("oldScriptUuid") && null==request.getAttribute("oldScriptUuid")) { %>
		var oldScriptUuid='';
	<% } else { %>
	<% if(null!=request.getParameter("oldScriptUuid")) { %>
	  var oldScriptUuid='<%=request.getParameter("oldScriptUuid")%>';
	<% } else { %>
	  var oldScriptUuid='<%=request.getAttribute("oldScriptUuid")%>';
	<% } %>
	<% } %>
	<% if (null==request.getParameter("oldScriptId") && null==request.getAttribute("oldScriptId")) { %>
  	var oldScriptId=0;
<% } else { %>
	<% if(null!=request.getParameter("oldScriptId")) { %>
	  var oldScriptId=<%=request.getParameter("oldScriptId")%>;
	<% } else { %>
	  var oldScriptId=<%=request.getAttribute("oldScriptId")%>;
	<% } %>
<% } %>
	var gdSwitch = '<%=request.getAttribute("projectFlag")==null?"0":request.getAttribute("projectFlag")%>';
	var db_suExecUser = <%=request.getAttribute("db_suExecUser")%>;
	var db_isAutoSub = <%=request.getAttribute("db_isAutoSub")%>;
	var db_manualStart = <%=request.getAttribute("db_manualStart")%>;
	var scriptLevelSwitch = <%=scriptLevelSwitch%>;
	var createScript  = <%=createScript%>;
	var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;

	var script_showGridSwitch=<%=request.getAttribute("showGridSwitch")==null?"":request.getAttribute("showGridSwitch")%>;
	var templateSwitch = <%=templateSwich%>;
	var labelSwitch = <%=sdScriptLabelEditSwitch%>;
	var gfScriptDirFunctionSwitch = <%=gfScriptDirFunctionSwitch%>;
     var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>;
	var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>;
	var arrs=new Array();
	<%
	//渤海上传附件扩展名控制
	String upFileType=Environment.getInstance().getSysConfig("script.create.basic.upfile.extension", "");
	String[] actInfo=upFileType.split(",");
    for (int i = 0; i < actInfo.length; i++) {
	%>
		arrs.push('<%=actInfo[i]%>');
	<%
    	}
	%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/md5.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/SingleInsScriptEdit.js"></script>
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin_v2/css/input_style.css">
</head>
<body>
	<input type="hidden" id="scriptManagePageExecUserNameText" />
	<div id="gridBasicScriptEdit_area" style="width: 100%; height: 100%;"></div>
</body>
</html>
<%-- <%@page contentType="text/html; charset=utf-8"%>
<html>
<title>CodeMirror: Shell mode</title>
<meta charset="utf-8"/>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<h2>Shell mode</h2>
<textarea id=code>
#!/bin/bash

# clone the repository
git clone http://github.com/garden/tree

# generate HTTPS credentials
cd tree
openssl genrsa -aes256 -out https.key 1024
openssl req -new -nodes -key https.key -out https.csr
openssl x509 -req -days 365 -in https.csr -signkey https.key -out https.crt
cp https.key{,.orig}
openssl rsa -in https.key.orig -out https.key

# start the server in HTTPS mode
cd web
sudo node ../server.js 443 'yes' &gt;&gt; ../node.log &amp;

# here is how to stop the server
for pid in `ps aux | grep 'node ../server.js' | awk '{print $2}'` ; do
  sudo kill -9 $pid 2&gt; /dev/null
done

exit 0</textarea>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
    mode: 'shell',
    lineNumbers: true,
    matchBrackets: true
  });
</script>
</html> --%>