var severId = null;
Ext.onReady(function () {
    // 清理主面板的各种监听时间
    destroyRubbish();
    var search_form;
    var iids = new Array();
    Ext.define('scriptScanHistoryDetailModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'severId', type: 'long'},
            {name: 'serviceName', type: 'string'},
            {name: 'groupName', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'bussName', type: 'string'},
            {name: 'scriptType', type: 'string'},
            {name: 'scriptName', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'createUserName', type: 'string'},
            {name: 'iBegindatetime', type: 'string'},
            {name: 'iResult', type: 'string'},
            {name: 'iCommand', type: 'string'},
        ]
    });
    //脚本扫描详情
    var ScriptScanDetailStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'scriptScanHistoryDetailModel',
        proxy: {
            type: 'ajax',
            url: 'getScriptScanDetail.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    ScriptScanDetailStore.on('beforeload', function (store, options) {
        var new_params = {
            iid: scanIid,
            sysName: bussCb.getValue(),//一级分类
            bussName: bussTypeCb.getValue(),//二级分类
            scriptName: scName.getValue(),//脚本名称
            serviceName: sName.getValue(),//服务名称
            scriptType: scriptTypeParam.getValue(),//脚本类型
            groupName: groupNameCombo.getValue(),//功能分类
            result: scanResult.getValue()//扫描结果
        };
        Ext.apply(ScriptScanDetailStore.proxy.extraParams, new_params);
    });
    ScriptScanDetailStore.addListener('load', function (me, records, successful, eOpts) {
        if (iids.length > 0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
                function (index, record) {
                    if (iids.indexOf(record.get('iid')) > -1) {
                        chosedRecords.push(record);
                    }
                });
            detailPanel.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
    });
    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        emptyText: '--请输入服务名称--',
        labelWidth: 65,
        width: 350,
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        emptyText: '--请输入脚本名称--',
        labelWidth: 65,
        width: 350,
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "sh", "name": "shell"},
            {"id": "bat", "name": "bat"},
            {"id": "py", "name": "python"},
            {"id": "perl", "name": "perl"},
            {"id": "sql", "name": "sql"},
            {"id": "ps1", "name": "powershell"}
        ]
    });
    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        width: 300,
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    // 扫描结果
    var scanResultStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "1", "name": "合格"},
            {"id": "2", "name": "不合格"}
        ]
    });

    var scanResult = Ext.create('Ext.form.field.ComboBox', {
        name: 'scanResult',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '扫描结果',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择扫描结果--',
        store: scanResultStore,
        width: 300,
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    //功能分类
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussData = Ext.create('Ext.data.Store', {
        fields: [{name: 'iid', type: 'string'}, {name: 'bsName', type: 'string'}],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });


    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: [{name: 'sysTypeId', type: 'string'}, {name: 'sysType', type: 'string'}],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 65,
        fieldLabel: '功能分类',
        width: 350,
        labelAlign: 'right',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '一级分类',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选一级分类-',
        store: bussData,
        width: 350,
        labelAlign: 'right',
        //value:returnOneLevelType,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    bussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    /** 二级分类* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '--请选二级分类-',
        store: bussTypeData,
        width: 350,
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    search_form = Ext.create('Ext.ux.ideal.form.Panel', {
        layout: 'anchor',
        region: 'north',
        bodyCls: 'x-docked-noborder-top',
        baseCls: 'customize_gray_back',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [sName, scName, scriptTypeParam, scanResult]
        }, {
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [groupNameCombo, bussCb, bussTypeCb, "->", {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    detailPanel.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    search_form.getForm().reset();
                    bussTypeData.removeAll();
                    scName.setValue('');
                    sName.setValue('');
                    scriptTypeParam.setValue('');
                    groupNameCombo.setValue('');
                    if (sdFunctionSortSwitch) {
                        bussData.removeAll();
                    }
                    iids = [];
                    detailPanel.ipage.moveFirst();
                }
            }, {
                text: '导出',
                cls: 'Common_Btn',
                handler: exportDetail
            }, {
                text: '返回',
                cls: 'Common_Btn',
                handler: fanhui
            }]
        }]
    });

    function exportDetail() {
        // var record = detailPanel.getSelectionModel ().getSelection ();
        // var iidStr = "";
        // Ext.Array.each (record, function (recordObj)
        // {
        //     iidStr += "," + recordObj.get ('iid');
        // });
        if (iids.length <= 0) {
            Ext.Msg.alert('提示', '请选择要操作的行！');
            return;
        }
        // iidStr = iidStr.substr(1);
        window.location.href = 'exportScanHistoryDetail.do?iidStr=' + iids;
    }

    function fanhui() {
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.setTitle('脚本扫描历史');
        contentPanel.getLoader().load({
            url: 'scriptScanHistory.do',
            scripts: true,
            params: {
                iid: scanIid,
                ibegindatetime: ibegindatetime,
                ienddatetime: ienddatetime,
                iscriptcount: iscriptcount,
                iresultt: iresultt,
                iresulttf: iresulttf,
                istatus: istatus,
                icreateuser: icreateuser,
                icreatetime: icreatetime,
                createUser: createUser,
                startTime: startTime,
                endTime: endTime
            }
        });
    }

    var scrScanHistoryDetailColumn = [
        {
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        }, {
            text: 'iid',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        }, {
            text: 'severId',
            dataIndex: 'severId',
            width: 40,
            hidden: true
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            width: 200, flex: 1,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            width: 200, flex: 1,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '版本',
            dataIndex: 'version',
            width: 70, flex: 1
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            hidden: !sdFunctionSortSwitch,
            width: 100, flex: 1
        },
        {
            text: '一级分类',
            dataIndex: 'sysName',
            width: 100, flex: 1
        },
        {
            text: '二级分类',
            dataIndex: 'bussName',
            width: 100, flex: 1
        },
        {
            text: '脚本类型',
            dataIndex: 'scriptType',
            width: 50, flex: 1,
            renderer: function (value, p, record, rowIndex) {
                var isflow = record.get('isflow');
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                } else if (value == "ps1") {
                    backValue = "powershell";
                }
                if (isflow == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        },
        {
            text: '创建者',
            dataIndex: 'createUserName',
            width: 100
        }, {
            text: '扫描时间',
            dataIndex: 'iBegindatetime',
            width: 200
        }, {
            text: '扫描结果',
            dataIndex: 'iResult',
            flex: 1,
            width: 100,
            renderer: function (value, p, record, rowIndex) {
                if (value == 1) {
                    return '<font color="#0CBF47">合格</font>';
                } else if (value == 2) {
                    return '<font color="#F01024">不合格</font>';
                }
            }
        }, {
            text: '关键命令',
            dataIndex: 'iCommand',
            flex: 1,
            width: 100,
            renderer: function (value, metadata) {
                // 处理关键命令中含有双引号 导致关键命令tip展示不全问题
                if (value != '') {
                    var valueArray = value.replace('"', '&quot;').split('\n');
                    metadata.tdAttr = 'data-qtip="';
                    for (let i = 0; i < valueArray.length; i++) {
                        metadata.tdAttr += valueArray[i] + '<br>';
                    }
                    metadata.tdAttr += '"';
                }
                return value;
            }
        }, {
            text: '操作',
            width: 180,
            xtype: 'actiontextcolumn',
            items: [{
                text: '脚本',
                iconCls: 'script_text',
                handler: function (grid, rowIndex) {
                    //脚本主键
                    var iid = grid.getStore().data.items[rowIndex].data.severId;
                    script(iid);
                }
            }, {
                text: '扫描历史',
                iconCls: 'monitor_export',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.severId;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    severId = iid;
                    getScanHistory(serviceName);
                }
            }],
        }];

    //点击脚本跳转的请求
    function script(iid) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForView.do',
                params: {
                    iid: iid,
                    hideReturnBtn: 1
                },
                autoLoad: true,
                scripts: true
            }
        });
        DetailWinTi.show();
    }

    var paramColumns = [
        {
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
        {
            text: '扫描时间',
            dataIndex: 'iBegindatetime',
            width: 150,
            flex: 1
        },
        {
            text: '状态',
            dataIndex: 'iResult',
            width: 150,
            flex: 1,
            renderer: function (value, p, record, rowIndex) {
                if (value == 1) {
                    return '<font color="#0CBF47">合格</font>';
                } else if (value == 2) {
                    return '<font color="#F01024">不合格</font>';
                }
            }
        },
        {
            text: '关键命令',
            dataIndex: 'iCommand',
            width: 150,
            flex: 1,
            renderer: function (value, metadata) {
                if (value != '') {
                    var valueArray = value.replace('"', '&quot;').split('\n');
                    metadata.tdAttr = 'data-qtip="';
                    for (let i = 0; i < valueArray.length; i++) {
                        metadata.tdAttr += valueArray[i] + '<br>';
                    }
                    metadata.tdAttr += '"';
                }
                return value;
            }
        }
    ];

    //点击扫描历史打开的弹窗
    function getScanHistory(serviceName) {

        Ext.define('scanModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iBegindatetime',
                type: 'long'
            }, {
                name: 'iResult',
                type: 'long'
            }, {
                name: 'iCommand',
                type: 'string'
            }]
        });

        var scanStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            model: 'scanModel',
            proxy: {
                type: 'ajax',
                url: 'getScanScriptHistory.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        //severId是脚本的主键
        scanStore.on('beforeload', function (store, options) {
            var new_params = {
                severId: severId
            };
            Ext.apply(scanStore.proxy.extraParams, new_params);
        });
        var scanhisroryGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            padding: grid_space,
            height: '100%',
            width: '100%',
            cls: 'customize_panel_back',
            columnLines: true,
            ipageBaseCls: Ext.baseCSSPrefix
                + 'toolbar customize_toolbar',
            iqueryFun: function () {
                scanhisroryGrid.ipage.moveFirst();
            },
            border: false,
            columns: paramColumns,
            store: scanStore
        });
        var scanHistory = Ext.create('Ext.window.Window', {
            title: '扫描历史-' + serviceName,
            layout: 'border',
            width: 700,
            height: 420,
            modal: true,
            items: [scanhisroryGrid]
        }).show();

    }

    var detailPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        split: true,
        multiSelect: true,
        id: 'detailPanel',
        emptyText: '没有分组',
        plugins: [cellEditing],
        padding: grid_space,
        width: 400,
        height: contentPanel.getHeight() - 35,
        cls: 'customize_panel_back',
        selModel: selModel,
        columnLines: true,
        ipageBaseCls: Ext.baseCSSPrefix
            + 'toolbar customize_toolbar',
        iqueryFun: function () {
            detailPanel.ipage.moveFirst();
        },
        border: false,
        columns: scrScanHistoryDetailColumn,
        store: ScriptScanDetailStore,
        listeners: {
            select: function (e, record, index, eOpts) {
                if (iids.indexOf(record.get('iid')) == -1) {
                    iids.push(record.get('iid'));
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (iids.indexOf(record.get('iid')) > -1) {
                    iids.remove(record.get('iid'));
                }
            }
        }
    });
    var panel = Ext.create('Ext.panel.Panel', {
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        layout: 'border',
        header: false,
        border: false,
        items: [search_form, detailPanel],
        renderTo: "scriptScanHistoryDetail_area"
    });
    contentPanel.on('resize', function () {
        panel.setHeight(contentPanel.getHeight() - modelHeigth);
        panel.setWidth(contentPanel.getWidth());
    });

});