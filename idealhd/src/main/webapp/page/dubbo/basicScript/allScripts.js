var query_bussId
var query_bussTypeId
var query_threeTypeId
var query_keywordsSerach
var query_scriptName
var query_serviceName
var query_scriptType
var query_scriptStatus
var query_scriptAuthor
var query_onlyScript
var query_switchFlag
var query_label
var query_scriptDir
var query_groupName
var checkItems=[]
Ext.onReady(function () {
// 清理主面板的各种监听时间
    destroyRubbish();
    var scriptServiceReleaseStore;
    var itemsPerPage = 30;
    var bussData = Ext.create('Ext.data.Store', {
        fields: [{name: 'iid', type: 'string'}, {name: 'bsName', type: 'string'}],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });


    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: [{name: 'sysTypeId', type: 'string'}, {name: 'sysType', type: 'string'}],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "sh", "name": "shell"},
            {"id": "bat", "name": "bat"},
            {"id": "py", "name": "python"},
            {"id": "perl", "name": "perl"},
            {"id": "sql", "name": "sql"},
            {"id": "ps1", "name": "powershell"}
        ]
    });

    //开启此开关，屏蔽sql类型脚本查询条件
    if(getScriptSqlShowSwitch){
        cataStore = Ext.create('Ext.data.Store', {
            fields: ['id', 'name'],
            data: [
                {"id": "-1", "name": "全部"},
                {"id": "sh", "name": "shell"},
                {"id": "bat", "name": "bat"},
                {"id": "py", "name": "python"},
                {"id": "perl", "name": "perl"},
                {"id": "ps1", "name": "powershell"}
            ]
        });
    }

    var scriptStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "-1", "name": "草稿"},
            {"id": "1", "name": "已上线"},
            {"id": "3", "name": "共享"},
            {"id": "2", "name": "审核中"}
        ]
    });


    var tmp2name = "二级分类";
    var tmp2text = "--请选择二级分类--";
    if (projectFlagForAll == 1) {
        tmp2name = "操作类型";
        tmp2text = "--请选择操作类型--";
    }
    /** 二级分类* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: tmp2name,
        displayField: 'sysType',
        valueField: 'sysTypeId',
        hidden: !secondclassShow_allScript,
        editable: true,
        //hidden:true,
        emptyText: tmp2text,
        store: bussTypeData,
        width: '20%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
        //value :returnSecondLevelType
    });


    //北京邮储 三级分类
    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: [{name: 'threeBsTypeId', type: 'string'}, {name: 'threeBsTypeName', type: 'string'}],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });


    threeBussTypeData.on('load', function (store, options) {
        if (returnThreeLevelType > 0 && threeBussTypeData.getRange().length > 0) {
            threeBussTypeCb.setValue(returnThreeLevelType);
            returnThreeLevelType = 0;
        }
    });
    var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threeBussTypeCb',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        width: '20%',
        labelAlign: 'right',
        hidden: !secondclassShow_allScript || !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: sdFunctionSortSwitch,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var tmpname = "一级分类";
    var tmptext = "--请选择一级分类--";
    if (projectFlagForAll == 1) {
        tmpname = "脚本分类";
        tmptext = "--请选择脚本分类--";
    }
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: tmpname,
        displayField: 'bsName',
        valueField: 'iid',
        hidden: !firstclassShow_allScript,
        editable: true,
        emptyText: tmptext,
        store: bussData,
        width: '20%',
        labelAlign: 'right',
        //value:returnOneLevelType,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    bussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    bussCb.setValue(returnOneLevelType);

    bussTypeCb.setValue(returnSecondLevelType);


    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        hidden: scriptTypeSwitch_allScript,
        valueField: 'id',
        editable: false,
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        width: '14%',
        labelAlign: 'right',
        value: returnScriptType,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var scriptStatusCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptStatus',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '脚本状态',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择脚本状态--',
        store: scriptStatusStore,
        width: '18%',
        labelAlign: 'right',
        value: returnScriptState,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        emptyText: '--请输入服务名称--',
        labelWidth: 65,
        width: '18%',
        labelAlign: 'right',
        value: returnServicesName,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var label = new Ext.form.TextField({
        name: 'label',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        //value : labelSwitch,
        hidden: !labelSwitch,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        emptyText: '--请输入脚本名称--',
        labelWidth: 65,
        width: '18%',
        labelAlign: 'right',
        value: returnScriptName,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var keywords = new Ext.form.TextField({
        name: 'keywordsSerach',
        fieldLabel: '关键字',
        emptyText: '-请输入关键字-',
        value: returnKeywords,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var scriptAuthorCb = new Ext.form.TextField({
        name: 'scriptAuthor',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '创建者',
        displayField: 'iuserfullname',
        valueField: 'iid',
        editable: false,
        emptyText: '--请输入创建者--',
        width: '18%',
        labelAlign: 'right',
        value: returnCreateUser,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

//	var scriptAuthorCb = Ext.create('Ext.form.field.ComboBox', {
//		name : 'scriptAuthor',
//		labelWidth : 65,
//		queryMode : 'local',
//		fieldLabel : '创建者',
//		displayField : 'iuserfullname',
//		valueField : 'iid',
//		editable : false,
//		emptyText : '--请选择创建者--',
//		store : startUserStore,
//		width : '18%',
//		labelAlign : 'right',
//		value :returnCreateUser,
//		listeners: {
//			specialkey: function(field, e){
//				if (e.getKey() == e.ENTER) {
//					pageBar.moveFirst();
//				}
//			}
//		}
//	});

    Ext.define('startUserModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        }, {
            name: 'iusername',
            type: 'string'
        }, {
            name: 'iuserfullname',
            type: 'string'
        }]
    });

    var search_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        bodyCls: 'x-docked-noborder-top',
        baseCls: 'customize_gray_back',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [sName, scName, groupNameCombo, bussCb, bussTypeCb, threeBussTypeCb]
        }, {
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [label, scriptTypeParam, scriptStatusCb, scriptAuthorCb, keywords, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    var scriptDirId = getScriptDirId();
                    query_bussId=bussCb.getValue()
                    query_bussTypeId=bussTypeCb.getValue(),
                    query_threeTypeId=threeBussTypeCb.getValue(),
                    query_keywordsSerach=keywords.getValue(),
                    query_scriptName=scName.getValue(),
                    query_serviceName=sName.getValue(),
                    query_scriptType=scriptTypeParam.getValue(),
                    query_scriptStatus=scriptStatusCb.getValue(),
                    query_scriptAuthor=scriptAuthorCb.getValue(),
                    query_onlyScript=1
                    query_switchFlag=projectFlagForAll
                    query_label=label.getValue()
                    query_scriptDir=Ext.encode(scriptDirId)
                    query_groupName=groupNameCombo.getValue()
                    pageBar.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    query_bussId=undefined
                    query_bussTypeId=undefined
                    query_threeTypeId=undefined
                    query_keywordsSerach=undefined
                    query_scriptName=undefined
                    query_serviceName=undefined
                    query_scriptType=undefined
                    query_scriptStatus=undefined
                    query_scriptAuthor=undefined
                    query_onlyScript=1
                    query_switchFlag=projectFlagForAll
                    query_label=undefined
                    query_scriptDir=undefined
                    query_groupName=undefined
                    checkItems=[];
                    clearQueryWhere();
                }
            }, {
                xtype: 'button',
                text: '导出',
                cls: 'Common_Btn',
                handler: function () {
                    excelExportListener();
                }
            }]
        }]
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'uuid', type: 'String'},
            {name: 'serviceName', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'groupName', type: 'string'},
            {name: 'bussName', type: 'string'},
            {name: 'buss', type: 'string'},
            {name: 'bussType', type: 'string'},
            {name: 'bussId', type: 'int'},
            {name: 'bussTypeId', type: 'int'},
            {name: 'threeTypeId', type: 'int'},
            {name: 'threeTypeName', type: 'string'},
            {name: 'scriptType', type: 'string'},
            {name: 'label', type: 'string'},
            {name: 'isflow', type: 'string'},
            {name: 'scriptName', type: 'string'},
            {name: 'servicePara', type: 'string'},
            {name: 'serviceState', type: 'string'},
            {name: 'platForm', type: 'string'},
            {name: 'isshare', type: 'string'},
            {name: 'content', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'author', type: 'string'},
            {name: 'createuser', type: 'string'},
            {name: 'updateuser', type: 'string'},
            {name: 'status', type: 'int'},
            {name: 'isEmScript', type: 'string'},
            {name: 'appSystem', type: 'string'},
            {name: 'useTimes', type: 'int'},
            {name: 'taskCount', type: 'int'},
            {name: 'winTimes', type: 'string'},
            {name: 'ifuncdesc', type: 'string'},
            {name: 'keywordsSerach', type: 'string'},
            {name: 'icreateTime', type: 'string'}
        ]
    });

    scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: itemsPerPage,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryAllScripts.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    scriptServiceReleaseStore.on('beforeload', function (store, options) {
        var scriptDirId = getScriptDirId();

        if (scriptCreateUserSwitch) {
            var new_params = {
                bussId: bussCb.getValue(),
                bussTypeId: bussTypeCb.getValue(),
                threeTypeId: threeBussTypeCb.getValue(),
                keywordsSerach: keywords.getValue(),
                scriptName: scName.getValue(),
                serviceName: sName.getValue(),
                scriptType: scriptTypeParam.getValue(),
                scriptStatus: scriptStatusCb.getValue(),
                createUserNameForCz: scriptAuthorCb.getValue(),
                onlyScript: 1,
                switchFlag: projectFlagForAll,
                label: label.getValue(),
                scriptDir: Ext.encode(scriptDirId),
                groupName: groupNameCombo.getValue()
            };
        } else {
            var new_params = {
                bussId: bussCb.getValue(),
                bussTypeId: bussTypeCb.getValue(),
                threeTypeId: threeBussTypeCb.getValue(),
                keywordsSerach: keywords.getValue(),
                scriptName: scName.getValue(),
                serviceName: sName.getValue(),
                scriptType: scriptTypeParam.getValue(),
                scriptStatus: scriptStatusCb.getValue(),
                scriptAuthor: scriptAuthorCb.getValue(),
                onlyScript: 1,
                switchFlag: projectFlagForAll,
                label: label.getValue(),
                scriptDir: Ext.encode(scriptDirId),
                groupName: groupNameCombo.getValue()
            };
        }


        Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });

    scriptServiceReleaseStore.on('load', function (obj, records, successful, eOpts) {
        if (records == '') {

        } else {
            let selectItems = [];
            for (let i = 0; i < records.length; i++) {
                if (checkItems.indexOf(records[i].data.iid) > -1){
                    selectItems.push(records[i])
                }
            }
            scriptServiceReleaseGrid.getSelectionModel().select(selectItems);
        }
    })

    var scriptServiceReleaseColumns;
    if (projectFlagForAll == 1) {
        scriptServiceReleaseColumns = [{
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
            {
                text: '服务主键',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '服务名称',
                dataIndex: 'serviceName',
                width: 200, flex: 1
            },
            {
                text: '脚本名称',
                dataIndex: 'scriptName',
                width: 260, flex: 1,
//				renderer : function(value, metadata) {
//					metadata.tdAttr = 'data-qtip="' + 44 + '"';
//					return value;
//				}
                renderer: function (value, metadata, record) {
//					var a = record.get('ifuncdesc');
                    var a = record.data.ifuncdesc;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return value;
                }
            },
            {
                text: '功能分类',
                dataIndex: 'groupName',
                hidden: !sdFunctionSortSwitch,
                width: 200, flex: 1
            },
            {
                text: '脚本分类',
                dataIndex: 'buss',
                width: 200, flex: 1
            },
            {
                text: '操作类型',
                dataIndex: 'bussType',
                width: 250, flex: 1
            },
            {
                text: '脚本类型',
                dataIndex: 'scriptType',
                width: 80, flex: 1,
                renderer: function (value, p, record, rowIndex) {
                    var isflow = record.get('isflow');
                    if (isflow == "1") {
                        return "";
                    } else {
                        return value;
                    }
                }
            },
            {
                text: '适用平台',
                dataIndex: 'platForm',
                width: 150
            },
            {
                text: '脚本状态',
                dataIndex: 'status',
                width: 100,
                renderer: function (value, p, record, rowIndex) {
                    if (value == -1) {
                        return '<font color="#F01024">草稿</font>';
                    } else if (value == 1) {
                        return '<font color="#0CBF47">已上线</font>';
                    } else if (value == 2) {
                        return '<font color="#FFA602">审核中</font>';
                    } else if (value == 3) {
                        return '<font color="#13B1F5">已共享</font>';
                    } else if (value == 9) {
                        return '<font color="">已共享未发布</font>';
                    } else {
                        return '<font color="#CCCCCC">未知</font>';
                    }
                }
            },
            {
                text: '投产人',
                dataIndex: 'author',
                hidden: !scriptProductUserSwitch,//浙商开启开关true后 展示
                width: 120
            },
            {
                text: '创建者',
                dataIndex: 'author',
                hidden: scriptCreateUserSwitch,//浙商开启开关true后 不展示这个
                width: 150
            }, {
                text: '创建者',
                dataIndex: 'createuser',
                hidden: !scriptCreateUserSwitch,//浙商开启开关true后 展示
                width: 120
            },
            {
                text: '修改人',
                dataIndex: 'updateuser',
                hidden: !scriptCreateUserSwitch,//浙商开启开关true后 展示
                width: 120
            },
            {
                text: '操作',
                xtype: 'actiontextcolumn',
                dataIndex: 'stepOperation',
                width: 80,
                items: [{
                    text: '查看',
                    iconCls: 'monitor_search',
                    handler: function (grid, rowIndex) {
                        var uuid = grid.getStore().data.items[rowIndex].data.iid;
                        var iid = grid.getStore().data.items[rowIndex].data.iid; // 其实是requestID
                        var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                        var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                        var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                        var status = grid.getStore().data.items[rowIndex].data.status;
                        //查询条件传递给查看函数，以便于跳转回来条件不变
                        var queryServicesName = sName.getValue();
                        var queryKeyWords = keywords.getValue();
                        var queryScriptName = scName.getValue();
                        var queryOneLevelType = bussCb.getValue();
                        var querySecondLevelType = bussTypeCb.getValue();
                        var queryScriptType = scriptTypeParam.getValue();
                        var queryScriptState = scriptStatusCb.getValue();
                        var queryCreateUser = scriptAuthorCb.getValue();
                        viewVersionForAllScript(iid, status, serviceName, bussId, bussTypeId, queryServicesName, queryScriptName, queryOneLevelType, querySecondLevelType, queryScriptType, queryScriptState, queryCreateUser, queryKeyWords, uuid);
                    }
                }]
            }
        ];
    } else {
        scriptServiceReleaseColumns = [{
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
            {
                text: '服务主键',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '服务主键uuid',
                dataIndex: 'uuid',
                width: 40,
                hidden: true
            },
            {
                text: '服务名称',
                dataIndex: 'serviceName',
                width: 200, flex: 1
            },
//		{
//			text : '',
//			dataIndex : 'ifuncdesc',
//			width : 200,flex:1,
//			hidden : true
//		},
            {
                text: '脚本名称',
                dataIndex: 'scriptName',
                width: 260, flex: 1,
                renderer: function (value, metadata, record) {
//				var a = record.get('ifuncdesc');
                    var a = record.data.ifuncdesc;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return value;
                }
            },
            {
                text: '功能分类',
                dataIndex: 'groupName',
                hidden: !sdFunctionSortSwitch,
                width: 200, flex: 1
            },
            {
                text: '一级分类',
                dataIndex: 'buss',
                hidden: !firstclassShow_allScript,
                width: 200, flex: 1
            },
            {
                text: '二级分类',
                dataIndex: 'bussType',
                hidden: !secondclassShow_allScript,
                width: 250, flex: 1
            }, {
                text: '三级分类',
                dataIndex: 'threeTypeName',
                width: 200, flex: 1,
                hidden: !secondclassShow_allScript || !scriptThreeBstypeSwitch
            },
            {
                text: '脚本类型',
                dataIndex: 'scriptType',
                width: 80, flex: 1,
                hidden: scriptTypeSwitch_allScript,
                renderer: function (value, p, record, rowIndex) {
                    var isflow = record.get('isflow');
                    var backValue = "";
                    if (value == "sh") {
                        backValue = "shell";
                    } else if (value == "perl") {
                        backValue = "perl";
                    } else if (value == "py") {
                        backValue = "python";
                    } else if (value == "bat") {
                        backValue = "bat";
                    } else if (value == "sql") {
                        backValue = "sql";
                    } else if (value == "ps1") {
                        backValue = "powershell";
                    }
                    if (isflow == '1') {
                        backValue = "组合";
                    }
                    return backValue;
                }
            },
            {
                text: '标签',
                dataIndex: 'label',
                hidden: !labelSwitch,
                width: 100,
                renderer: function (value, metadata) {
                    metadata.tdAttr = 'data-qtip="' + value + '"';
                    return value;
                }
            },
            {
                text: '适用平台',
                dataIndex: 'platForm',
                width: 100
            },
            {
                text: '使用次数',
                dataIndex: 'useTimes',
                hidden: useTimeSwitch_allScript,
                width: 80
            },
            {
                text: '发起次数',
                dataIndex: 'taskCount',
                width: 70,
                hidden: !scriptSubmitTaskCountSwitch
            },
            {
                text: '成功率',
                dataIndex: 'winTimes',
                hidden: winTimesSwitch_allScript,
                width: 80
            }, {
                text: '是否应急',
                dataIndex: 'isEmScript',
                width: 75,
                hidden: !reviewSwitch,
                renderer: function (value, p, record, rowIndex) {
                    if (value == 0) {
                        return '否';
                    } else if (value == 1) {
                        return '<font color="#F01024">是</font>';
                    } else {
                        return '未知';
                    }
                }
            },
            {
                text: '所属系统',
                dataIndex: 'appSystem',
                hidden: !reviewSwitch,
                width: 50,
                flex: 1
            },
            {
                text: '脚本状态',
                dataIndex: 'status',
                width: 100,
                renderer: function (value, p, record, rowIndex) {
                    if (value == -1) {
                        return '<font color="#F01024">草稿</font>';
                    } else if (value == 1) {
                        return '<font color="#0CBF47">已上线</font>';
                    } else if (value == 2) {
                        return '<font color="#FFA602">审核中</font>';
                    } else if (value == 3) {
                        return '<font color="#13B1F5">已共享</font>';
                    } else if (value == 9) {
                        return '<font color="">已共享未发布</font>';
                    } else {
                        return '<font color="#CCCCCC">未知</font>';
                    }
                }
            },
            {
                text: '共享状态',
                dataIndex: 'isshare',
                width: 100,
                renderer: function (value, p, record, rowIndex) {
                    if (value == 0) {
                        return '<font color="">未共享</font>';
                    } else if (value == 1) {
                        return '<font color="#0CBF47">已共享</font>';
                    } else {
                        return '<font color="#CCCCCC">未知</font>';
                    }
                }
            },
            {
                text: '投产人',
                dataIndex: 'author',
                hidden: !scriptProductUserSwitch,//浙商开启开关true后 展示
                width: 120
            },
            {
                text: '创建者',
                dataIndex: 'author',
                hidden: scriptCreateUserSwitch,//浙商开启开关true后 不展示这个
                width: 150
            }, {
                text: '创建者',
                dataIndex: 'createuser',
                hidden: !scriptCreateUserSwitch,//浙商开启开关true后 展示
                width: 120
            },
            {
                text: '修改人',
                dataIndex: 'updateuser',
                hidden: !scriptCreateUserSwitch,//浙商开启开关true后 展示
                width: 120
            },
            {
                text: '最近修改时间',
                dataIndex: 'icreateTime',
                width: 150
            },
            {
                text: '操作',
                xtype: 'actiontextcolumn',
                dataIndex: 'stepOperation',
                width: 80,
                items: [{
                    text: '查看',
                    iconCls: 'monitor_search',
                    handler: function (grid, rowIndex) {
                        var iid = grid.getStore().data.items[rowIndex].data.iid; // 其实是requestID
                        var uuid = grid.getStore().data.items[rowIndex].data.uuid;
                        var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                        var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                        var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                        var threeTypeId = grid.getStore().data.items[rowIndex].data.threeTypeId;
                        var status = grid.getStore().data.items[rowIndex].data.status;
                        var label = grid.getStore().data.items[rowIndex].data.label;
                        //查询条件传递给查看函数，以便于跳转回来条件不变
                        var queryServicesName = sName.getValue();
                        var queryKeyWords = keywords.getValue();
                        var queryScriptName = scName.getValue();
                        var queryOneLevelType = bussCb.getValue();
                        var querySecondLevelType = bussTypeCb.getValue();
                        var queryScriptType = scriptTypeParam.getValue();
                        var queryScriptState = scriptStatusCb.getValue();
                        var queryCreateUser = scriptAuthorCb.getValue();

                        var scriptDirId = [];
                        if (gfScriptDirFunctionSwitch) {
                            var m = Ext.getCmp('treeId').getSelectionModel().getSelection();
                            for (let i = 0; i < m.length; i++) {
                                let iid = m[i].data.iid;
                                let iscriptDirLevel = m[i].data.iscriptDirLevel;
                                // 0级目录 root根目录
                                if (iscriptDirLevel == 0) {
                                    break;
                                }
                                scriptDirId.push(iid)
                            }
                        }
                        viewVersionForAllScript(iid, status, serviceName, bussId, bussTypeId, queryServicesName, queryScriptName, queryOneLevelType, querySecondLevelType, queryScriptType, queryScriptState, queryCreateUser, queryKeyWords, uuid, threeTypeId, Ext.encode(scriptDirId), label);
                    }
                }]
            }
        ];
    }
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptServiceReleaseStore,
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
//	var selModel=Ext.create('Ext.selection.CheckboxModel', {
//		checkOnly: true
//	});
    var scriptServiceReleaseGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        cls: 'customize_panel_back',
        padding: grid_space,
        store: scriptServiceReleaseStore,
        dockedItems:[{
            xtype:'toolbar',
            items:['->', {
                xtype: 'fieldcontainer',
                defaultType: 'checkboxfield',
                hidden:!bhScriptScanSwitch,
                items: [
                    {
                        boxLabel  : '全选',
                        name      : '全选',
                        inputValue: '1',
                        id        : 'allSelect'
                    }
                ]
                },
                {
                    xtype:'button',
                    cls:'Common_Btn',
                    text:'脚本扫描',
                    hidden:!bhScriptScanSwitch,
                    handler:function(){
                        console.log(Ext.getCmp('allSelect').value);
                        var allSelect = Ext.getCmp('allSelect').value;
                        Ext.Msg.wait('处理中，请稍后...', '提示');
                        // 未勾选 全选复选框， 按选中记录扫描
                        // 勾选  按照查询条件 扫描
                        if (!allSelect) {
                            if (checkItems.length == 0) {
                                Ext.Msg.alert('提示', '请选择要扫描的脚本！');
                                return;
                            }else{
                                scriptScan(allSelect);
                            }
                        }else {
                            scriptScan(allSelect);
                        }
                    }
                }
            ]
        }],
        plugins: [cellEditing],
//		    bbar : pageBar,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border: false,
        columnLines: true,
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        columns: scriptServiceReleaseColumns,
        listeners:{
            select: function (t, record, index) {
                if (checkItems.indexOf(record.get('iid')) == -1) {
                    checkItems.push(record.get('iid'));
                }
            },
            deselect: function (t, record, index) {
                if (checkItems.indexOf(record.get('iid')) > -1) {
                    checkItems.remove(record.get('iid'));
                }
            }
        }
    });

    Ext.define('scriptDirModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iscriptDirName',
                type: 'string'
            }, {
                name: 'iscriptDirSort',
                type: 'long'
            }, {
                name: 'iid',
                type: 'long'
            }, {
                name: 'iscriptDirDiscript',
                type: 'string'
            }, {
                name: 'iscriptDirLevel',
                type: 'int'
            }, {
                name: 'iscriptDirRootId',
                type: 'string'
            }
        ]

    })

    var scriptDirTreeStore = Ext.create('Ext.data.TreeStore', {
        autoLoad: gfScriptDirFunctionSwitch,
        model: 'scriptDirModel',
        proxy: {
            type: 'ajax',
            url: 'scriptEdit/scriptDirList.do'
        },
        root: {
            expanded: gfScriptDirFunctionSwitch,
            iscriptDirName: '根目录',
            icon: 'ext/resources/ext-theme-neptune/images/tree/folder.png'
        }
    });

    scriptDirTreeStore.on("load", function (obj, node, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = treePanel.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(treePanel.getView().emptyText);
                treePanel.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });

    var treePanel = Ext.create('Ext.tree.Panel', {
        // height : '80%',
        region: 'west',
        width: '16%',
        id: 'treeId',
        title: '脚本目录',
        autoScroll: true,
        collapsible: true,
        cls: 'customize_panel_back',
        animate: true,
        useArrows: true,
        hidden: !gfScriptDirFunctionSwitch,
        rootVisible: false,
        store: scriptDirTreeStore,
        hideHeaders: true,
        columns: [{
            xtype: 'treecolumn',
            text: '目录',
            flex: 1,
            dataIndex: 'iscriptDirName',
            sortable: false
        }],
        border: true,
        padding: grid_space,
        columnLines: true,
        listeners:{
            select:function(){
                var scriptDirId = getScriptDirId();
                query_bussId=bussCb.getValue()
                query_bussTypeId=bussTypeCb.getValue(),
                query_threeTypeId=threeBussTypeCb.getValue(),
                query_keywordsSerach=keywords.getValue(),
                query_scriptName=scName.getValue(),
                query_serviceName=sName.getValue(),
                query_scriptType=scriptTypeParam.getValue(),
                query_scriptStatus=scriptStatusCb.getValue(),
                query_scriptAuthor=scriptAuthorCb.getValue(),
                query_onlyScript=1
                query_switchFlag=projectFlagForAll
                query_label=label.getValue()
                query_scriptDir=Ext.encode(scriptDirId)
                query_groupName=groupNameCombo.getValue()
                pageBar.moveFirst();
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });
    scriptDirTreeStore.on('load', function (store, records) {
        var selModel = Ext.getCmp('treeId').getSelectionModel();
        var m = selModel.getSelection();
        var filterScriptDir = Ext.decode(returnScriptDir);
        if (filterScriptDir.length == 0) {
            var root = store.getRootNode();
            selModel.select(root)
        } else {
            var nodeHash = store.tree.nodeHash;
            for (let key in nodeHash) {
                if (nodeHash[key].data.iid == filterScriptDir[0]) {
                    selModel.select(nodeHash[key]);
                    break;
                }
            }
        }

    })

    let renderTo = "all_scripts_grid_area";
    if(isTabSwitch){
        renderTo += allnow;
    }
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: renderTo,
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        border: false,
        layout: 'border',
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        items: [search_form, treePanel, scriptServiceReleaseGrid]
    });

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });


    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

    function clearQueryWhere() {
//		bussCb.setValue('');
//		bussTypeCb.setValue('');
//		scriptTypeParam.setValue('');
        search_form.getForm().reset();
        bussTypeData.removeAll();
        scName.setValue('');
        sName.setValue('');
        scriptStatusCb.setValue('');
        scriptAuthorCb.setValue('');
        scriptTypeParam.setValue('');
        keywords.setValue('');
        label.setValue('');
        groupNameCombo.setValue('');
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
    }

    function viewVersionForAllScript(iid, status, serviceName, bussId, bussTypeId, queryServicesName, queryScriptName, queryOneLevelType, querySecondLevelType, queryScriptType, queryScriptState, queryCreateUser, queryKeyWords, uuid, threeTypeId, scriptDir, label) {
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'scriptViewVersionForAllScripts.do',
            params: {
                serviceId: iid,
                uuid: uuid,
                flag: 0,
                status: status,
                servicesName: queryServicesName,
                scriptName: queryScriptName,
                oneLevelType: queryOneLevelType,
                secondLevelType: querySecondLevelType,
                threeTypeId: threeTypeId,
                scriptType: queryScriptType,
                scriptState: queryScriptState,
                createUser: queryCreateUser,
                switchFlag: projectFlagForAll,
                keywordsSerach: queryKeyWords,
                scriptDir: scriptDir,
                label: label
            },
            scripts: true
        });
    }

    //导出
    function excelExportListener() {
        var records = scriptServiceReleaseGrid.getSelectionModel().getSelection();
        var jsonArray = [];
        Ext.each(records, function (item) {// 遍历
            jsonArray.push(item.data.iid);
        });
        var iids = jsonArray.join(",");

        Ext.Msg.confirm("确认", "请确认是否导出脚本信息？", function (button, text) {
            if (button == "yes") {
                $.fileDownload('scriptService/exportAllScripts.do', {
                    httpMethod: 'POST',
                    traditional: true,
                    data: {
                        bussId: bussCb.getValue(),
                        bussTypeId: bussTypeCb.getValue(),
                        threeTypeId: threeBussTypeCb.getValue(),
                        keywordsSerach: keywords.getValue(),
                        scriptName: scName.getValue(),
                        serviceName: sName.getValue(),
                        scriptType: scriptTypeParam.getValue(),
                        scriptStatus: scriptStatusCb.getValue(),
                        scriptAuthor: scriptAuthorCb.getValue(),
                        onlyScript: 1,
                        switchFlag: projectFlagForAll,
                        iids: iids
                    },
                    successCallback: function (url) {
                    },
                    failCallback: function (html, url) {
                        if(html.indexOf("没有操作权限") > -1){
                            Ext.Msg.alert('提示', '没有操作权限，导出失败！');
                        }else{
                            Ext.Msg.alert('提示', '导出失败！');
                        }
                        return;
                    }
                });
            } else {
                return;
            }
        });

    }

//	function getTip(){
//		var tip;
//		Ext.Ajax.request({
//			url : 'computerSave.do',
//			params : {
//				jsonData : jsonData
//			},
//			method : 'POST',
//			success : function(response, opts) {
//				// alert('success....');
//				var respText = Ext.JSON.decode(response.responseText);
//				 tip = respText.tip;
//			},
//			failure : function(result, request) {
//				secureFilterRs(result, "操作失败！");
//			}
//		});
//		return '666';
//	}
});
function scriptScan(allSelectSwitch){
    Ext.Ajax.request({
        method:'POST',
        url:'allScriptScanTask.do',
        params:{
            bussId:query_bussId,
            bussTypeId:query_bussTypeId,
            threeTypeId:query_threeTypeId,
            keywordsSerach:query_keywordsSerach,
            scriptName:query_scriptName,
            serviceName:query_serviceName,
            scriptType:query_scriptType,
            scriptStatus:query_scriptStatus,
            scriptAuthor:query_scriptAuthor,
            onlyScript:1,
            switchFlag:query_switchFlag,
            label:query_label,
            scriptDir:query_scriptDir,
            groupName:query_groupName,
            allSelect:allSelectSwitch,
            uuids:Ext.encode(checkItems),
            start:0,
            limit:0
        },
        success:function (response) {
            var data = Ext.decode(response.responseText);
            if (data.success){
                Ext.Msg.confirm('提示信息','脚本扫描任务已生成，是否进入扫描历史？',function (btn){
                   if (btn == 'yes'){
                       contentPanel.setTitle('脚本扫描历史');
                       contentPanel.getLoader().load({
                           url: "scriptScanHistory.do",
                           scripts: true,
                           params: {

                           }
                       });
                   }else {

                   }
                })
            }else {
                Ext.Msg.alert('提示', data.message);
            }
        },
        failure:function (){
            Ext.Msg.alert('提示', '网络连接失败');
        }
    })
}

function getScriptDirId(){
    var scriptDirId = [];
    if (gfScriptDirFunctionSwitch) {
        var m = Ext.getCmp('treeId').getSelectionModel().getSelection();
        for (let i = 0; i < m.length; i++) {
            let iid = m[i].data.iid;
            let iscriptDirLevel = m[i].data.iscriptDirLevel;
            // 0级目录 root根目录
            if (iscriptDirLevel == 0) {
                break;
            }
            getLastScriptDirId(m[i], scriptDirId);
//                scriptDirId.push(iid)
        }
        // 其他页面跳转回来 第一次查询 用filter 数据展示
        if (m.length == 0) {
            var filterScriptDir = Ext.decode(returnScriptDir);
            scriptDirId = filterScriptDir;
        }
    }
    return scriptDirId;
}

function getLastScriptDirId(record, scriptDirId) {
    if (record.childNodes.length > 0) {
        let m = record.childNodes;
        for (let i = 0; i < m.length; i++) {
            getLastScriptDirId(m[i], scriptDirId)
        }
    } else {
        let iid = record.data.iid;
        scriptDirId.push(iid);
    }
}
