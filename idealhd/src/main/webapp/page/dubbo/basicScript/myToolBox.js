Ext.onReady(function () {
// 清理主面板的各种监听时间
    destroyRubbish();

    // 将通过jsp跳转传递的变量全部进行转移和清理。要注意变量的值传递可以直接清理。变量如果传递的是引用，注意不要把真实值清理掉，清理引用即可。
    var menuId = tempDataForMyToolBox.menuidForMyToolBox;
    delete tempDataForMyToolBox;

    var win;
    var bussTypeIds = new Array();
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "sh", "name": "shell"},
            {"id": "bat", "name": "bat"},
            {"id": "py", "name": "python"},
            {"id": "perl", "name": "perl"},
            {"id": "sql", "name": "sql"},
            {"id": "ps1", "name": "powershell"}
//			        {"id":"-2", "name":"组合"},
        ]
    });
    var statusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        }, {
            "id": "1",
            "name": "启用"
        }, {
            "id": "2",
            "name": "禁用"
        }]
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        width: '20%',
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '5',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        width: '20%',
        queryMode: 'local',
        fieldLabel: '一级分类',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        labelAlign: 'right',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    bussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    /** 二级分类* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        labelWidth: 70,
        width: '20%',
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        labelAlign: 'right',
        editable: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    //北京邮储 三级分类
    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    /*threeBussTypeData.on('load', function(store, options) {
        if(threeBsTypeId) {
            threeBussTypeCb.setValue(threeBsTypeId);
        }
    });*/
    var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threeBussTypeCb',
        labelWidth: 70,
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        width: '24.9%',
        labelAlign: 'right',
        hidden: !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    /** 状态 * */
    var bussStatus = Ext.create('Ext.form.field.ComboBox', {
//		        padding : '5',
        fieldLabel: '状态',
        labelWidth: 70,
        // padding : '0 10 0 10',
        //labelAlign : 'right',
        width: '31%',
        name: 'status',
        displayField: 'name',
        valueField: 'id',
        emptyText: '--全部--',
        store: statusStore,
        queryMode: 'local',
        editable: false,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    /** 脚本类型*
     var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptTypeParam',
//		padding : '5',
		labelWidth : 70,
//		columnWidth : .33,
		width : '31%',
		queryMode : 'local',
		fieldLabel : '脚本类型',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择脚本类型--',
		store : cataStore
	});* */

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '--请输入服务名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '20%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '--请输入脚本名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '20%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
//		padding : '0,5,0,5',
        labelWidth: 70,
        width: '25.5%',
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        labelAlign: 'right',
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
//	 var search_form = Ext.create('Ext.form.Panel', {
//	    	layout : 'anchor',
//	    	buttonAlign : 'center',
//	    	border : false,
//		    items: [{
//		    	layout:'form',
//		    	anchor:'98%',
//		    	padding : '5 0 5 0',
//		    	border : false,
//		    	items: [{
//		    		layout:'column',
//			    	border : false,		    	
//		    		items:[sName,scName,scriptTypeParam]
//		    	},{
//		    		layout:'column',
//			    	border : false,
//			    	items:[ bussCb,bussTypeCb,{
//						xtype : 'button',
//						columnWidth:.125,
//						//width:60,
//			            height:30,
//						text : '查询',
//						margin:'5 0 0 80',
//						cls : 'Blue_button',
//						handler : function() {
//							pageBar.moveFirst();
//						}
//					},{
//						xtype : 'button',
//						columnWidth:.068,
//						//width:60,
//			            height:30,
//						text : '清空',
//						cls : 'Red_button',
//						margin:'5 0 0 15',
//						handler : function() {
//							clearQueryWhere();
//						}
//					}]
//		    	}]
//		    }]
//		});
    var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    usePlantFormStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: "0"
        };
        Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
    });
    var platFromCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'platFromCombobox',
        labelWidth: 70,
        // columnWidth : .2,
        labelAlign: 'right',
        width: '25.5%',
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: true,
        emptyText: '--请选择适用平台--',
        store: usePlantFormStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var createUser = new Ext.form.TextField({
        name: 'createUser',
        fieldLabel: '创建人',
        emptyText: '--请输入创建人--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.5%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var label = new Ext.form.TextField({
        name: 'label',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        hidden: !labelSwitch,
        //value : labelSwitch,
        labelWidth: 70,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    })
    var search_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        baseCls: 'customize_gray_back',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [sName, scName, groupNameCombo, bussCb, bussTypeCb]
        }, {
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [threeBussTypeCb, scriptTypeParam, platFromCombobox, createUser, label]
        }, {
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [
                '->', {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        pageBar.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    text: '清空',
                    cls: 'Common_Btn',
                    handler: function () {
                        clearQueryWhere();
                    }
                }
            ]
        }
        ]
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'oriScriptId', type: 'long'},
            {name: 'serviceName', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'groupName', type: 'string'},
            {name: 'bussName', type: 'string'},
            {name: 'threeBsTypeName', type: 'string'},
            {name: 'label', type: 'string'},
            {name: 'buss', type: 'string'},
            {name: 'bussType', type: 'string'},
            {name: 'bussId', type: 'int'},
            {name: 'bussTypeId', type: 'int'},
            {name: 'scriptType', type: 'string'},
            {name: 'createUser', type: 'string'},
            {name: 'timeout', type: 'int'},
            {name: 'isflow', type: 'string'},
            {name: 'scriptName', type: 'string'},
            {name: 'servicePara', type: 'string'},
            {name: 'serviceState', type: 'string'},
            {name: 'platForm', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'content', type: 'string'},
            {name: 'scriptLevel', type: 'int'},
            {
                name: 'scriptuuid',
                type: 'string'
            }
        ]
    });

    var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMyToolBox.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    scriptServiceReleaseStore.on('beforeload', function (store, options) {
        var new_params = {
            bussId: search_form.getForm().findField("sysName").getValue(),
            bussTypeId: search_form.getForm().findField("bussType").getValue(),
            threeBsTypeId: search_form.getForm().findField("threeBussTypeCb").getValue(),
            scriptName: search_form.getForm().findField("scriptName").getValue(),
            serviceName: search_form.getForm().findField("serverName").getValue(),
            scriptType: search_form.getForm().findField("scriptTypeParam").getValue(),
            platForm: platFromCombobox.getValue(),
            createUserName: createUser.getValue(),
            label: label.getValue(),
            groupName: groupNameCombo.getValue()
        };

        Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });

    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '服务主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '服务名称',
            flex: 1,
            dataIndex: 'serviceName',
            width: 150
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            flex: 1,
            //hidden:true,
            width: 150
        },
        {
            text: '适用平台',
            dataIndex: 'platForm',
            width: 100
        },
        {
            text: '功能分类',
            flex: 1,
            dataIndex: 'groupName',
            width: 90,
            hidden: !sdFunctionSortSwitch
        },
        {
            text: '一级分类',
            flex: 1,
            dataIndex: 'buss',
            width: 90
        },
        {
            text: '二级分类',
            flex: 1,
            dataIndex: 'bussType',
            width: 90
        }, {
            text: '三级分类',
            dataIndex: 'threeBsTypeName',
            hidden: !scriptThreeBstypeSwitch,
            width: 90
        }, {
            text: '标签',
            dataIndex: 'label',
            width: 100,
            hidden: !labelSwitch,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: 'uuid',
            dataIndex: 'scriptuuid',
            hidden: true,
            width: 90
        },
        {
            text: '创建人',
            dataIndex: 'createUser',
            width: 100
        },
        {
            text: '超时时间',
            dataIndex: 'timeout',
            hidden: true,
            width: 80
        },
        {
            text: '脚本类型',
            dataIndex: 'scriptType',
            width: 60,
            renderer: function (value, p, record, rowIndex) {
                var isflow = record.get('isflow');
//		    	if(isflow=="1") {
//		    		return "组合"
//		    	} else {
//		    		return value;
//		    	}
                var isflow = record.get('isflow');
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                } else if (value == "ps1") {
                    backValue = "powershell";
                }
                if (isflow == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        },
        {
            text: '脚本级别', dataIndex: 'scriptLevel', width: 80, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 1) {
                    backValue = '<font color="#F01024">高级风险</font>';
                } else if (value == 2) {
                    backValue = '<font color="#FF7824">中级风险</font>';
                } else if (value == 3) {
                    backValue = '<font color="#FFA826">低级风险</font>';
                } else if (value == 0) {
                    backValue = '<font color="#FFA826">白名单</font>';
                }
                return backValue;
            }
        },
        {
            text: '版本',
            dataIndex: 'version',
            width: 60
        },
        {
//			text: '操作',  
//			dataIndex: 'stepOperation',
//			width:190,
//			renderer:function(value,p,record,rowIndex){
//        		var iid =  record.get('iid');
//            	var isflow = record.get('isflow');
//            	var isflow1 = record.get('isflow');
//				var serviceName = record.get('serviceName');
//				var bussId = record.get('bussId');
//				var bussTypeId  =record.get('bussTypeId');
//				var scriptLevel  =record.get('scriptLevel');
//				var status = record.get('status');
//				var scriptType = record.get('scriptType');
//				var execFuncName = "forwardScriptExecAudiForCollection";
//				var viewDetailFuncName = "viewDetailForCollection";
//				if(!noScriptConvertSwitch){
//					isflow = "1";
//				}
//				if(isflow=='1') {
//					execFuncName = "forwardScriptFlowCollectionExecAudi";
//					viewDetailFuncName = "viewDetailForTaskIndexForFlowCollection";
//				}
//				if(status==2){
//					return '<span class="switch_span">'+
//       	         '<a href="javascript:void(0)" onclick="'+viewDetailFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+','+scriptLevel+','+ isflow1+',' + menuId+')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;查看</a>'+
//       	       '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//				}else{
//					return '<span class="switch_span">'+
//       	         '<a href="javascript:void(0)" onclick="'+execFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+','+scriptLevel+',\'' +scriptType+'\''+','+menuId+')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_task"></img>&nbsp;任务申请</a>'+
//       	       '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//       	       '<span class="switch_span">'+
//       	         '<a href="javascript:void(0)" onclick="'+viewDetailFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+','+scriptLevel+','+ isflow1+',' + menuId+')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;查看</a>'+
//       	       '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//				}
//            	
//			}

            text: '操作',
            xtype: 'actiontextcolumn',
            dataIndex: 'stepOperation',
            width: 190,
            items: [{
                text: '任务申请',
                iconCls: 'script_task',
                getClass: function (v, metadata, record) {
                    if (record.data.status == 2) {
                        return 'x-hidden';
                    }
                },
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                    var isflow1 = grid.getStore().data.items[rowIndex].data.isflow;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                    var status = grid.getStore().data.items[rowIndex].data.status;
                    var timeout = grid.getStore().data.items[rowIndex].data.timeout;
                    var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (!noScriptConvertSwitch) {
                        forwardScriptFlowCollectionExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, menuId);
                    } else {
                        forwardScriptExecAudiForCollection(iid, serviceName, bussId, bussTypeId, scriptLevel, timeout, uuid, scriptType, menuId, label);
                    }
                }
            },
                {
                    text: '查看',
                    iconCls: 'monitor_search',
                    handler: function (grid, rowIndex) {
                        var iid = grid.getStore().data.items[rowIndex].data.iid;
                        var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                        var isflow1 = grid.getStore().data.items[rowIndex].data.isflow;
                        var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                        var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                        var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                        var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                        var status = grid.getStore().data.items[rowIndex].data.status;
                        var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                        var label = grid.getStore().data.items[rowIndex].data.label;
                        if (!noScriptConvertSwitch) {
                            viewDetailForTaskIndexForFlowCollection(iid, serviceName, bussId, bussTypeId, scriptLevel, isflow1, menuId);
                        } else {
                            viewDetailForCollection(iid, label, serviceName, bussId, bussTypeId, scriptLevel, isflow1, menuId);
                        }
                    }
                }]
        }
    ];
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptServiceReleaseStore,
        dock: 'bottom',
        displayInfo: true,
        afterPageText: ' 页 共 {0} 页',
        beforePageText: '第 ',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '显示{0}- {1} 条，  共{2}条',
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var scriptServiceReleaseGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
//		width : '100%',
//	    height : contentPanel.getHeight() - 106,
        id: 'releaseGrid',
        store: scriptServiceReleaseStore,
        cls: 'customize_panel_back',
        selModel: selModel,
        padding: grid_space,
        plugins: [cellEditing],
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border: false,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        animCollapse: false,
        dockedItems: [{
            xtype: 'toolbar',
            items: ['->',
                /*{
                    text : '共享',
                    width:70,
                    height:30,
                    margin:'5 5',
                    textAlign:'center',
                    icon:'images/share.png',
                    cls : 'Blue_button',
                    handler : shareServiceRelease
                },{
                    text : '发布',
                    width:70,
                    height:30,
                    margin:'5 5',
                    textAlign:'center',
                    icon:'images/release.png',
                    cls : 'Blue_button',
                    handler : saveServiceRelease
                }, '-', */{
                    text: '取消收藏',
                    /*width:80,
                    height:30,*/
//			        margin:'5 5',
                    textAlign: 'center',
                    cls: 'Common_Btn',
                    handler: deleteServiceRelease
                }


            ]
        }]
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptService_tool_box",
        border: false,
//	        bodyPadding : 5,
        layout: 'border',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        items: [search_form, scriptServiceReleaseGrid]
    });
//	scriptServiceReleaseGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
//		scriptServiceReleaseGrid.down('#delete').setDisabled(selections.length === 0);
//	});

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    /* 解决IE下trim问题 */
    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
//		scriptServiceReleaseGrid.setHeight (contentPanel.getHeight () - 106);
        if (auditingWin) {
            auditingWin.center();
        }
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

    function shareServiceRelease() {
        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要共享的服务！");
            return;
        }
        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认共享", "是否共享选中的服务", function (id) {
            if (id == 'yes') release(4);
        });
    }


    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }]
    });

    var auditorStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getAuditorListForSsPublish.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    auditorStore.on('beforeload', function (store, options) {
        var new_params =
            {
                bussTypeIds: bussTypeIds
            };
        Ext.apply(auditorStore.proxy.extraParams, new_params);
    });
    auditorStore.on('load', function (store, records, successful, eOpts) {
        var countNum = 0;
        for (var i = 0; i < auditorStore.getCount(); i++) {
            if (auditorComBox.getValue() == auditorStore.getAt(i).data.loginName) {
                countNum++;
            }
        }

        if (countNum == 0) {
            auditorComBox.setValue('');
        }
    });

    var auditorComBox = Ext.create('Ext.form.ComboBox', {
        editable: false,
        fieldLabel: "审核人",
        labelWidth: 50,
//	    padding: 5,
        store: auditorStore,
        queryMode: 'local',
        width: 200,
//	    margin : '10 10 20 10',
        displayField: 'fullName',
        valueField: 'loginName'//,
        //value: auditor
    });


    function saveServiceRelease() {
        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要发布的服务！");
            return;
        }

        var flowIdList = Ext.getCmp('releaseGrid').getSelectionModel().getSelection();
        for (var i = 0, len = flowIdList.length; i < len; i++) {
            bussTypeIds.push(flowIdList[i].data.bussTypeId);
        }
        bussTypeIds = bussTypeIds.uniquelize();

        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认发布", "是否发布选中的服务", function (id) {
            if (id == 'yes') {
                if (!win) {
                    win = Ext.create('widget.window', {
                        title: '请选择审核人',
                        closable: true,
                        closeAction: 'hide',
                        width: 600,
                        minWidth: 350,
                        height: 150,
                        layout: {
                            type: 'border'
//		                    ,padding: 5
                        },
                        items: [auditorComBox],
                        buttons: [{
                            xtype: "button",
//				  			cls:'Blue_button',
                            text: "确定",
                            handler: function () {
                                flowIdList = Ext.getCmp('releaseGrid').getSelectionModel().getSelection();
                                var sIds = new Array();
                                for (var i = 0, len = flowIdList.length; i < len; i++) {
                                    sIds.push(flowIdList[i].data.iid);
                                }
                                Ext.Ajax.request({
                                    url: 'scriptService/sendAudit.do',
                                    method: 'POST',
                                    params: {
                                        sIds: sIds,
                                        auditor: auditorComBox.getValue(),
                                        flag: 0 //0-来着个人脚本库
                                    },
                                    success: function (response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        //var message = Ext.decode(response.responseText).message;
                                        win.close();
                                        Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "操作失败！");
                                        win.close();
                                    }
                                });

                            }
                        }, {
                            xtype: "button",
//				  			cls:'Gray_button',
                            text: "取消",
                            handler: function () {
                                this.up("window").close();
                            }
                        }]
                    });

                }
                win.show();
                auditorStore.load();
            }
        });
    }

    function deleteServiceRelease() {
        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要取消收藏的脚本！");
            return;
        }
        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认取消收藏", "是否取消收藏选中的脚本？", function (id) {
            if (id == 'yes') release(1);
        });
    }

    function release(optionState) {
        var message = "脚本发布成功";
        var errorMessage = "脚本发布失败";
        if (optionState == 1) {
            message = "操作成功";
            errorMessage = "操作失败";
        }

        var jsonData = getSelectedJsonData();
        if (jsonData == "[]") {
            Ext.MessageBox.alert("提示", signMessage);
            return;
        }

        Ext.Ajax.request({
            url: 'scriptService/serviceReleaseForToolBox.do',
            method: 'POST',
            params: {
                jsonData: jsonData,
                optionState: optionState
            },
            success: function (response, opts) {
                var success = Ext.decode(response.responseText).success;
                //var message = Ext.decode(response.responseText).message;
                if (success) {
                    Ext.MessageBox.show({
                        title: "提示",
                        msg: message,
                        buttonText: {
                            yes: '确定'
                        },
                        buttons: Ext.Msg.YES
                    });
                } else {
                    Ext.MessageBox.show({
                        title: "提示",
                        msg: errorMessage,
                        buttonText: {
                            yes: '确定'
                        },
                        buttons: Ext.Msg.YES
                    });
                }
                scriptServiceReleaseStore.reload();
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    // 将被选中的记录的flowid组织成json串，作为参数给后台处理
    function getSelectedJsonData() {
        var flowIdList = Ext.getCmp('releaseGrid').getSelectionModel().getSelection();
        if (flowIdList.length < 1) {
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = flowIdList.length; i < len; i++) {
            if (i == 0) {
                jsonData = jsonData + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
            } else {
                jsonData = jsonData + "," + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
            }
        }
        jsonData = jsonData + "]";
        return jsonData;
    }

    function clearQueryWhere() {
//    	search_form.getForm().findField("serviceName").setValue('');
//    	search_form.getForm().findField("status").setValue('1');
        search_form.getForm().findField("sysName").setValue('');
        search_form.getForm().findField("bussType").setValue('');
        search_form.getForm().findField("scriptName").setValue('');
        search_form.getForm().findField("serverName").setValue('');
        search_form.getForm().findField("scriptTypeParam").setValue('');
//		search_form.getForm().findField("status").setValue('');
        search_form.getForm().findField("platFromCombobox").setValue('');
        search_form.getForm().findField("createUser").setValue('');
        bussTypeData.removeAll();
        threeBussTypeData.removeAll();
        label.setValue('');
        groupNameCombo.setValue('');
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
    }

    //从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        var eValue = eval('jsonObj.' + key);
        return jsonObj['' + key + ''];
    }


    function viewDetailForTaskIndexForFlowCollection(serviceId, serviceName, bussId, bussTypeId, scriptLevel, isFlow, menuId) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() - 42,
            draggable: false,
            resizable: false,
            modal: true,
            loader: {
                url: 'flowCustomizedInitScriptService.do',
                params: {
                    serviceId: serviceId,
                    iid: serviceId,
                    menuId: menuId,
                    status: 0,
                    serviceName: serviceName,
                    actionType: 'view',
                    bussId: bussId,
                    bussTypeId: bussTypeId,
                    submitType: 'tv',
                    flag: 0,
                    windowHeight: contentPanel.getHeight() - 42,
                    rootspace: 'view',
                    isShowInWindow: 1,
                    isScriptConvertToFlow: isFlow != '1'
                },
                autoLoad: true,
                scripts: true
            }
        });

        DetailWinTi.show();
    }


    function forwardScriptFlowCollectionExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, menuId) {
        // alert(iid);
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptServiceGFSSCOLLECTAUDI.do',
            params: {
                iid: iid,
                serviceId: iid,
                menuId: menuId,
                serviceName: serviceName,
                actionType: 'audi',
                bussId: bussId,
                bussTypeId: bussTypeId,
                scriptType: scriptType,
                scriptLevel: scriptLevel,
                submitType: 'ms',
                ifrom: 'forwardScriptMyToolBox.do',
                flag: 1
            },
            scripts: true
        });
    }


    //编辑按钮执行一个.do跳转到编辑页面
    function editScript(iid) {
//		alert(iid);
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({url: 'forwardEditScript.do?serviceId=' + iid, scripts: true});
    }

    function editScriptFlow(iid, serviceName, bussId, bussTypeId) {
//		alert(iid);
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptService.do',
            params: {
                iid: iid,
                serviceName: serviceName,
                actionType: 'edit',
                bussId: bussId,
                bussTypeId: bussTypeId
            },
            scripts: true
        });
    }


    //function viewScript(iid){
//		destroyRubbish(); //销毁本页垃圾
//		contentPanel.getLoader().load({url: 'queryOneServiceForView.do',
//			params: {
//				iid:iid,
//				flag: 2
//			},
//			scripts: true});
    //}
    //
    //function viewScriptFlow(iid,serviceName,bussId,bussTypeId){
////		alert(iid);
//		destroyRubbish(); //销毁本页垃圾
//		contentPanel.getLoader().load({url: 'flowCustomizedInitScriptService.do',
//			params: {
//				iid:iid,
//				serviceName:serviceName,
//				actionType:'view',
//				bussId:bussId,
//				bussTypeId:bussTypeId,
//				flag:2
//			},
//			scripts: true});
    //}
    //
    function testScript(iid) {
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'scriptExecStart.do?serviceId=' + iid + '&flag=2&from=3' + '&url=forwardScriptMyToolBox.do',
            scripts: true
        });
    }

    function testScriptFlow(iid, serviceName, bussId, bussTypeId) {
//		alert(iid);
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptService.do',
            params: {
                iid: iid,
                serviceName: serviceName,
                actionType: 'exec',
                bussId: bussId,
                bussTypeId: bussTypeId,
                flag: 2
            },
            scripts: true
        });
    }

    function forwardExecAudiScriptFlow(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType) {
        // alert(iid);
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptService.do',
            params: {
                iid: iid,
                serviceName: serviceName,
                actionType: 'audi',
                bussId: bussId,
                bussTypeId: bussTypeId,
                scriptType: scriptType,
                scriptLevel: scriptLevel,
                flag: 2
            },
            scripts: true
        });
    }

    function viewForFlowDetailForTaskIndex(serviceId, serviceName, bussId, bussTypeId) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'flowCustomizedInitScriptService.do',
                params: {
                    iid: serviceId,
                    serviceName: serviceName,
                    actionType: 'view',
                    bussId: bussId,
                    bussTypeId: bussTypeId,
                    flag: 0,
                    isShowInWindow: 1
                },
                autoLoad: true,
                scripts: true
            }
        });
        DetailWinTi.show();
    }

    function viewDetailForCollection(serviceId, label) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForView.do',
                params: {
                    iid: serviceId,
                    flag: 1,
                    hideReturnBtn: 1,
                    label: label
                },
                autoLoad: true,
                scripts: true
            }
        });

        DetailWinTi.show();
    }

    function forwardScriptExecAudiForCollection(serviceId, a, b, c, scriptLevel, timeout, uuid, scriptType, menuId, label) {
        var whiteScript = "";
        if (scriptLevel == 0) {
            whiteScript = "whiteScript";
        }
//	if(!auditingWin) {
        auditingWin = Ext.create('widget.window', {
            title: '执行审核',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() + 30,
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForAuditing.do',
                params: {
                    iid: serviceId,
                    whiteScript: whiteScript,
                    scriptLevel: scriptLevel,
                    serviceName: a,
                    timeout: timeout,
                    uuid: uuid,
                    label: label

                },
                autoLoad: true,
                scripts: true
            }
        });
//	}
//	auditingWin.getLoader().load({});
        auditingWin.show();
    }
});

