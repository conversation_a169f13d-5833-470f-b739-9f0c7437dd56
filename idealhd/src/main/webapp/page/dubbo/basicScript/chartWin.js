function showChartWin(params, datas, tableName){
    var getCharDataUrl = 'getChartWinData.do'
    if (datas.length > 0){
        getCharDataUrl += '?haveData='+1
    }else {
        getCharDataUrl += '?haveData='+0
    }
    getCharDataUrl += '&tableName='+tableName
    var charWin = Ext.create('Ext.window.Window', {
        height:'90%',
        width:'90%',
        modal:true,
        title:'图形展示',
        draggable: false,
        resizable: false,
        html:'<div id="charDiv" style="width:100%;height: 100%"></div>'
    }).show();

    Ext.Msg.wait('处理中，请稍后...', '提示');
    // $.post("getTestData.do", {},function(result, data){})
    Ext.Ajax.request({
        url:getCharDataUrl,
        method:'POST',
        params:params,
        success:function (response){
            var result = Ext.decode(response.responseText);
            var dataList;
            var chartDom = document.getElementById('charDiv');
            var myChart = echarts.init(chartDom);

            var option;
            dataList = result['data'];
            if (datas.length > 0){
                dataList = datas
            }
            var title = result['title'];
            var dimensions = result['dimensions'];
            // var data = [];
            // for (let i = 1; i < dimensions.length; i++) {
            //     data[i-1]=dimensions[i];
            // }
            var selected = {};
            for (let i = 0; i < title.length; i++) {
                selected[title[i]]=i==0?true:false;
            }

            var series = [];
            for (let i = 0; i < title.length; i++) {
                series[i] = {type: 'line',
                    name:title[i]
                    // , smooth: true       曲线
                };
            }
            // 默认折线图
            option = {
                // 焦点
                tooltip: {
                    trigger: 'axis',
                    position: function (pt) {
                        return [pt[0], '10%'];
                    }
                },
                // 图例域
                legend:{
                    //  根据后台数据前端处理
                    data:title,
                    //  根据后台数据前端处理
                    selected:selected,
                    type: 'scroll',
                    width: '40%',
                    scrollDataIndex: 0,
                    pageButtonItemGap: 10,
                    pageButtonGap: 20,
                    pageButtonPosition: 'end',
                    pageFormatter: '{current}/{total}'
                },
                title: {
                    // left: 'center',
                    // text: 'Large Area Chart'
                },
                // 按钮域
                toolbox: {
                    feature: {
                        restore: {},
                        saveAsImage: {},
                        magicType: {
                            type: ['line', 'bar']
                        }
                    }
                },
                // 数据集
                dataset: {
                    // 用 dimensions 指定了维度的顺序。直角坐标系中，如果 X 轴 type 为 category，
                    // 默认把第一个维度映射到 X 轴上，后面维度映射到 Y 轴上。
                    // 如果不指定 dimensions，也可以通过指定 series.encode
                    // 完成映射，参见后文。
                    dimensions: dimensions,  //  后台配置数据
                    source: dataList
                },
                xAxis: {
                    type: 'category'
                },
                yAxis: {

                },
                // 大数据调节区
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 10
                    },
                    {
                        start: 0,
                        end: 10
                    }
                ],
                series: series
            };
            option && myChart.setOption(option);
            myChart.resize({width:'auto', height:'auto'});
            myChart.on('legendselectchanged', function (params) {
                var selectedName = params.name;
                for (const key in params.selected) {
                    if (key != selectedName && params.selected[key] == true){
                        myChart.dispatchAction({
                            type: 'legendUnSelect',
                            // 图例名称
                            name: key
                        })
                    }
                }
            })
            Ext.Msg.close();
        },
        failure:function(){
            Ext.Msg.alert('提示', '网络连接失败');
        }
    })
}

