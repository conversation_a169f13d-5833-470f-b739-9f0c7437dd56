<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
    //标签
    boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
    //功能分类
    boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
%>
<html>
<head>
<script type="text/javascript">
var tempDataForMyToolBox = {};
var labelSwitch = <%=sdScriptLabelEditSwitch%>;
var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
<%
String menuidForMyToolBox = request.getParameter("menuId");
Enumeration<String> paramNamesForMyToolBox = request.getParameterNames();
while( paramNamesForMyToolBox.hasMoreElements() )
{
    String paramName = paramNamesForMyToolBox.nextElement();
%>
	tempDataForMyToolBox.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/myToolBox.js"></script>
</head>
<body>
<div id="scriptService_tool_box" style="width: 100%;height: 100%">
</body>
</html>