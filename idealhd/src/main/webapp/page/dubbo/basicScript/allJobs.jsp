<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<html>
<head>
<script type="text/javascript">
var tempData = {};
<%
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/allJobs.js"></script>
<script type="text/javascript">
	var projectFlag = '<%=request.getAttribute("projectFlag")==null?"0":request.getAttribute("projectFlag")%>';
	var filter_serverNameQuery = '<%=request.getParameter("filter_serverNameQuery")==null?"":request.getParameter("filter_serverNameQuery")%>';
	var filter_scriptStateQuery  = <%=request.getParameter("filter_scriptStateQuery")==null?-10000:request.getParameter("filter_scriptStateQuery")%>;
</script>
</head>
<body>
<div id="all_jobs_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>