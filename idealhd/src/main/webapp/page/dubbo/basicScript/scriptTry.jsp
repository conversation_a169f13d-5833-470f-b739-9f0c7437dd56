<%@ page import="com.ideal.ieai.core.Environment" %>
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<%@page contentType="text/html; charset=utf-8"%>
<% 
boolean dangerCmdCheckTypeSwitch= Environment.getInstance().getScriptDangerCmdCheckTypeSwitch();
boolean isAttach=Environment.getInstance().getScriptAttachmentSwitch();
boolean scriptLevelSwitch= Environment.getInstance().getScriptLevelSwitch();
boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
%>
<html>
<head>
<script>
    var dangerCmdCheckTypeSwitch =<%=dangerCmdCheckTypeSwitch%>;
    var isAttach =<%=isAttach%>;
    var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;
    var scriptLevelSwitch =<%=scriptLevelSwitch%>;
</script>
<script type="text/javascript">
	var filter_scriptNameForToolMonitor = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var returnScriptTryState ='<%=request.getParameter("returnScriptTryState")==null?"":request.getParameter("returnScriptTryState")%>';
	var sessionIdaudi = '<%=request.getSession().getId()%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/scriptTry.js"></script>
</head>
<body>
<input type="hidden" id="scriptManagePageExecUserNameText" />
<div id="scriptTry_area" style="width: 100%;height: 100%">
</div>
</body>
</html>