<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>帮助文档</title>
<link rel="stylesheet" href="<%=request.getContextPath()%>/css/doc.css" />
</head>
<body>
<div class="main_grid">
	<div class="col_md-1 col_md">
    	<ul class="nav_list">
        	<li><a href="#">参考手册</a></li>
            <li><a href="#">选择器</a></li>
            <li><a href="#">听觉参考手册</a></li>
            <li><a href="#network">网络安全字体</a></li>
            <li><a href="#unit">单位</a></li>
            <li><a href="#colors">颜色</a></li>
        </ul>
    </div>
    <div class="col_md-2 col_md">
    	<h1 class="page_header" id="network">网络安全字体</h1>
        <h2 class="page_header_h2">常用的字体组合</h2>
        <p class="page_content">
        	font-family 属性应该使用若干种字体名称作为回退系统，以确保浏览器/操作系统之间的最大兼容性。如果浏览器不支持第一个字体，则会尝试下一个。
        </p>
        <figure class="highlight">
        	<pre>
            	<code class="language-html" data-lang="html">
<span class="nt">&lt;a</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">href=</span><span class="s">"#"</span> <span class="na">role=</span><span class="s">"button"</span><span class="nt">&gt;</span>Link<span class="nt">&lt;/a&gt;</span>
<span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"submit"</span><span class="nt">&gt;</span>Button<span class="nt">&lt;/button&gt;</span>
<span class="nt">&lt;input</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"button"</span> <span class="na">value=</span><span class="s">"Input"</span><span class="nt">&gt;</span>
<span class="nt">&lt;input</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"submit"</span> <span class="na">value=</span><span class="s">"Submit"</span><span class="nt">&gt;</span>
                </code>
            </pre>
        </figure>
        
         <h1 class="page_header" id="unit">单位</h1>
        <h2 class="page_header_h2">十六进制数</h2>
        <p class="page_content">
        	1em 等于当前的字体尺寸。<br />
			2em 等于当前字体尺寸的两倍。<br />
			例如，如果某元素以 12pt 显示，那么 2em 是24pt。<br />
			在 CSS 中，em 是非常有用的单位，因为它可以自动适应用户所使用的字体。
        </p>
        <figure class="highlight">
        	<pre>
            	<code class="language-html" data-lang="html">
<span class="nt">&lt;a</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">href=</span><span class="s">"#"</span> <span class="na">role=</span><span class="s">"button"</span><span class="nt">&gt;</span>Link<span class="nt">&lt;/a&gt;</span>
<span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"submit"</span><span class="nt">&gt;</span>Button<span class="nt">&lt;/button&gt;</span>
<span class="nt">&lt;input</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"button"</span> <span class="na">value=</span><span class="s">"Input"</span><span class="nt">&gt;</span>
<span class="nt">&lt;input</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"submit"</span> <span class="na">value=</span><span class="s">"Submit"</span><span class="nt">&gt;</span>
                </code>
            </pre>
        </figure>
        
    	<h1 class="page_header" id="colors">颜色</h1>
        <h2 class="page_header_h2">颜色值</h2>
        <p class="page_content">
        	CSS 颜色使用组合了红绿蓝颜色值 (RGB) 的十六进制 (hex) 表示法进行定义。对光源进行设置的最低值可以是 0（十六进制 00）。最高值是 255（十六进制 FF）。
        </p>
        <figure class="highlight">
        	<pre>
            	<code class="language-html" data-lang="html">
<span class="nt">&lt;a</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">href=</span><span class="s">"#"</span> <span class="na">role=</span><span class="s">"button"</span><span class="nt">&gt;</span>Link<span class="nt">&lt;/a&gt;</span>
<span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"submit"</span><span class="nt">&gt;</span>Button<span class="nt">&lt;/button&gt;</span>
<span class="nt">&lt;input</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"button"</span> <span class="na">value=</span><span class="s">"Input"</span><span class="nt">&gt;</span>
<span class="nt">&lt;input</span> <span class="na">class=</span><span class="s">"btn btn-default"</span> <span class="na">type=</span><span class="s">"submit"</span> <span class="na">value=</span><span class="s">"Submit"</span><span class="nt">&gt;</span>
                </code>
            </pre>
        </figure>
    </div>
</div>
</body>
</html>
