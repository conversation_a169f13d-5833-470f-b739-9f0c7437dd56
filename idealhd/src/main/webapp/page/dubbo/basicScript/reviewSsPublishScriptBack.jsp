<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean sendSwitch = Environment.getInstance().getScriptServiceSendSwitch();
	boolean scriptLevelSwitch= Environment.getInstance().getScriptLevelSwitch();
	boolean isAttach=Environment.getInstance().getScriptAttachmentSwitch();
%>
<html>
<head>
	<script>
		var iid = <%=request.getAttribute("iid")%>;
		var sessionIdaudi = '<%=request.getSession().getId()%>';
		var scriptUuid = "<%=request.getAttribute("scriptUuid")%>";
		var fromType = <%=request.getAttribute("fromType")%>;
		var workItemid = <%=request.getAttribute("workItemid")%>;
		var from = <%=request.getAttribute("from")==null?2:request.getAttribute("from")%>;
		var bsNameText='<%=request.getAttribute("bussName") %>';
		var bsTypeNameText='<%=request.getAttribute("bussTypeName") %>';
		var threeBsTypeNameText='<%=request.getAttribute("threeBsTypeName") %>';
		var platFormText='<%=request.getAttribute("platForm") %>';
		var serviceNameText='<%=request.getAttribute("serviceName") %>';
		var scriptNameText='<%=request.getAttribute("scriptName") %>';
		var scriptLevelDisplayText='<%=request.getAttribute("scriptLevelDisplay") %>';
		var planTimeText='<%=request.getAttribute("planTime") %>';
		var scriptTypeText='<%=request.getAttribute("scriptType") %>';
		if(scriptTypeText=='sh') {
			scriptTypeText = "shell";
		} else if(scriptTypeText=='py'){
			scriptTypeText = "python";
		} else if(scriptTypeText=='ps1'){
			scriptTypeText = "powershell";
		}
		var publishDescText='<%=request.getAttribute("publishDesc") %>';
		var backInfoText='<%=request.getAttribute("backInfo") %>';
		var backInfoText='<%=request.getAttribute("backInfo") %>';
		var istatus = '<%=request.getAttribute("scriptStatus") %>';
		var isForbiddenText = '<%=request.getAttribute("isForbiddenText") %>';
		// 从菜单模块  脚本服务化双人复核查询  功能发出的请求 获取 菜单名称
		var titleFordoublePersonSSQuery = '<%=request.getAttribute("title") %>';
		var audi_taskTime='<%=request.getAttribute("taskTime") %>';
		var audi_tableName='<%=request.getAttribute("tableName") %>';
		var audi_planTimeTestingByDate='<%=request.getAttribute("planTimeTestingByDate") %>';
		var audi_dbaas_flag='<%=request.getAttribute("audi_dbaas_flag") %>';
		var scriptLevelCode = '<%=request.getAttribute("scriptLevelCode") %>';
		var scriptLevelSwitch = <%=scriptLevelSwitch%>;
		var isAttach=<%=isAttach%>;
		var imodelType = '<%=request.getParameter("imodelType")==null?-1:request.getParameter("imodelType")%>';
		var scriptUuid = '<%=request.getAttribute("scriptUuid") %>';
	</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/reviewSsPublishScriptBack.js"></script>
</head>
<body>
<div id='aaaa_area'></div>
<%-- <table cellpadding="0" cellspacing="0" border="0" height="100%" align="center">
	<tr>
		<td height="100%">
			<div class="base_sc_border">
				<table cellpadding="0" cellspacing="0" border="0" class="base_table">
					<tr>
						<td class="base_td1">业务类别：</td>
						<td><%=request.getAttribute("bussName") %></td>
					</tr>
					<tr>
						<td class="base_td1">操作类型：</td>
						<td><%=request.getAttribute("bussTypeName") %></td>
					</tr>
					<tr>
						<td class="base_td1">服务名称：</td>
						<td><%=request.getAttribute("serviceName") %></td>
					</tr>
					<tr>
						<td class="base_td1">脚本名称：</td>
						<td><%=request.getAttribute("scriptName") %></td>
					</tr>
					<tr>
						<td class="base_td1">适用平台：</td>
						<td><%=request.getAttribute("platForm") %></td>
					</tr>
					<tr>
						<td class="base_td1">计划时间：</td>
						<td><%=request.getAttribute("planTime") %></td>
					</tr>
					<tr>
						<td class="base_td1">脚本级别：</td>
						<td><%=request.getAttribute("scriptLevelDisplay") %></td>
					</tr>
					<tr>
						<td class="base_td1">脚本类型：</td>
						<td><%=request.getAttribute("scriptType") %></td>
					</tr>
					<tr>
						<td class="base_td1">发布详细说明：</td>
						<td><%=request.getAttribute("publishDesc") %></td>
					</tr>
					<tr>
						<td class="base_td1">打回原因：</td>
						<td><%=request.getAttribute("backInfo") %></td>
					</tr>
					<tr>
						<td colspan="2" align="center">
							<button id="viewS" class="Blue_button base_button">查看脚本详情</button>
							<button id="submit" class="Blue_button base_button">提交</button>
							<button id="stop" class="Blue_button base_button">终止</button>
							<button id="return" class="Blue_button base_button">返回</button>
						</td>
					</tr>
				</table>
			</div>
		</td>
	</tr>
</table> --%>
</body>
</html>