<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<% 
	long iid=Long.parseLong(request.getParameter("iid"));
	boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
	boolean showConfigSwitch = Environment.getInstance().getScriptShowConfigSwitch();
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	long customId=null!=request.getParameter("customId")?Long.parseLong(request.getParameter("customId")):0L;
	boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
	boolean taskApplyUploadAttachmentSwitch =  Environment.getInstance().geTaskApplyUploadAttachmentSwitch();
	boolean equipSwitch = Environment.getInstance().getScriptTaskapplyAddAgentSwitch();
	boolean templateSwich = Environment.getInstance().getTempUploadTabSwitch();
	//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
	boolean firstUserSelected = Environment.getInstance().getSelectedFirstUserSwitch();
	// 邮储 任务申请-任务申请页面/常用任务配置页面，参数默认值字段变为不可编辑
	boolean paramEditDisable = Environment.getInstance().psbcParamDisEditSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
%>
<html>
<head>
<script type="text/javascript">
	var labelEdit="<%=request.getAttribute("labels")%>";
	var iidForTaskAudi="<%=iid%>";
	var customIid ="<%=customId%>";
	var scriptLevelForTaskAudi='<%=request.getParameter("scriptLevel")%>';
	var serviceNameForTaskAudi='<%=request.getParameter("serviceName")%>';
	var scriptTypeForTaskAudi='<%=request.getParameter("scriptType")%>';
	var customTaskName = '<%=request.getAttribute("customName")%>';
	var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;
	var taskApplyUploadAttachmentSwitch =<%=taskApplyUploadAttachmentSwitch%>;
	//适用平台
	var scriptPlatmFrom = '<%=request.getParameter("platmFrom")%>'; 
	var checkRadioForTaskAudi = 0;
	var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
	var execUserSwitch = <%=execUserSwitch%>;
	var eachNumForA = <%=request.getAttribute("eachNum")%>;
	var CMDBFlag = <%=request.getAttribute("CMDBflag")%>;
	var showConfigSwitch = <%=showConfigSwitch%>;
	var equipSwitch = <%=equipSwitch%>;
	var cmdbFlag;
	if(CMDBFlag){
		cmdbFlag = false;
	}else{
		cmdbFlag = true;
	}
	var agent_store_chosed = Ext.create('Ext.data.Store', {
	     model: 'page.dubbo.scriptService.spdb.agent.agentModel',
	     proxy: {
	     },
	     autoLoad: false
	 });
	var agentListStore = undefined;
	var chosedAgentIds = new Array();
	var chosedAgentFlagArray = new Array();//判断是否Windows和非Windows一起选择
	var chosedAgentWinForSPDB = new Ext.window.Window();
    var agentColumnsForSPDB = [];
    var  checkInumber=false;
	var searchUUID = '<%=request.getAttribute("searchUUID")%>';
	//模板tab页展示开关
	var templateSwitch = <%=templateSwich%>
	//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
	var firstUserSelected = <%=firstUserSelected%>;
	// 邮储 任务申请-任务申请页面/常用任务配置页面，参数默认值字段变为不可编辑
	var paramEditDisable = <%=paramEditDisable%>;
	var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>
</script>
	<%
		if(Environment.getInstance().getScriptTaskApplyAddAgentSwitch()) {
	%>
	<script    async type="text/javascript"
			   src="<%=request.getContextPath()%>/page/dubbo/basicScript/agentModelAndColumnForSPDB.js"  ></script>
	<script    type="text/javascript"
			   src="<%=request.getContextPath()%>/page/dubbo/basicScript/queryAgentInfoForSPDB.js" ></script>
	<%
		}
	%>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskCustomPage.js"></script>

</head>
<body>
	<input type="hidden" id="taskCustomPageExecUserNameText" />
	<div id="taskCustomPage_area" style="width: 100%; height: 25%;"></div>
</body>
</html>
