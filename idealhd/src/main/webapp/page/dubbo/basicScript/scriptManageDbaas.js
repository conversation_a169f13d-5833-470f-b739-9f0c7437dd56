Ext.Loader.setConfig({
  enabled:true,
  disableCaching:false,
  paths:{
    'Go':'js/ux/gooo'
  }
});

var scriptServiceReleaseStore;
Ext.onReady(function() {
// 清理主面板的各种监听时间
	destroyRubbish();
	var itemsPerPage = 30;
	var publishAuditingSMWin;
	var auditing_form_sm;
	var auditorStore_sm;
	var auditorComBox_sm;
	var planTime_sm;
	var pubDesc_sm;

	var chosedAppSys = new Array();
	var radio=1;
	var shareType=0; //共享类型  0 所有人  1 用户   2 用户组
	var chosedShareIds =[]; //共享用户、用户组  数组
	
	var scriptStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"-1", "name":"草稿"},
			{"id":"1", "name":"已上线"},
			{"id":"3", "name":"共享"},
			{"id":"2", "name":"审核中"}
		]
	});
	var emScriptStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-1", "name":"全部"},
			{"id":"0", "name":"否"},
			{"id":"1", "name":"是"}
		]
	});
	if(filter_scriptType=='sql'){
		radio=4;
	}
	
	var usenumtjText = new Ext.form.NumberField({
        name : 'usenumtjText',
        emptyText : '--输入条件值--',
        hidden:true,
        width :'5%',
        minValue:'0',
        listeners: {
            specialkey: function(field, e){
            if (e.getKey() == e.ENTER) {
                pageBar.moveFirst();
                }
            }
        }
	    });
	
   var usenumtjStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data : [
            {"id":"-1", "name":"全部"},
            {"id":">=", "name":">="},
            {"id":"<=", "name":"<="},
            {"id":"=", "name":"="}
        ]
    });
   var usetj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '使用次数',
        emptyText : '--请选择--',
        labelWidth: 65,
        hidden :usenumtj,
        labelAlign: 'right',
        //width: '8%',
        columnWidth : .5,
        store: usenumtjStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable : false,
        typeAhead : false,
        mode: 'local',
        listeners: {
           select : function(combo) {
               if(combo.getValue()!=-1) {
                   usenumtjText.show();
               }else {
                   usenumtjText.hide();
               }
           }
       }
    });
   var succtjText = new Ext.form.NumberField({
       name : 'succtjText',
       emptyText : '--输入条件值--',
       hidden:true,
       width :'5%',
       minValue:'0',
       maxValue:'100',
       listeners: {
           specialkey: function(field, e){
           if (e.getKey() == e.ENTER) {
               pageBar.moveFirst();
               }
           }
       }
       });
   
   
   
  var succtjStore = Ext.create('Ext.data.Store', {
       fields: ['id', 'name'],
       data : [
           {"id":"-1", "name":"全部"},
           {"id":">=", "name":">="},
           {"id":"<=", "name":"<="},
           {"id":"=", "name":"="}
       ]
   });
  var succtj = Ext.create('Ext.form.field.ComboBox', {
       fieldLabel: '成功率',
       emptyText : '--请选择--',
       labelWidth: 65,
       hidden :usenumtj,
       labelAlign: 'right',
       //width: '8%',
       columnWidth : .5,
       store: succtjStore,
       displayField: 'name',
       valueField: 'id',
       editable : false,
       typeAhead : false,
       triggerAction: 'all',
       mode: 'local',
       listeners: {
          select : function(combo) {
              if(combo.getValue()!=-1) {
                  succtjText.show();
              }else {
                  succtjText.hide();
              }
          }
      }
   });
	
	var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
		fields: ['INAME', 'ICODEVALUE'],
		//autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getScriptPlatformCode.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
	usePlantFormStore.on('beforeload', function(store, options) {
			var new_params = {
				gdSwitch :  "0"
			};
			Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
	});
    var platFromCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'platFromCombobox',
        labelWidth: 65,
        // columnWidth : .2,
        labelAlign: 'right',
        width: '20%',
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: false,
        emptyText: '-请选择适用平台-',
        store: usePlantFormStore,
        hidden:db_usePlantFormswitch,
        value:filter_patFromValue,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
	
	var scriptStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptStatus',
		labelWidth : 65,
		queryMode : 'local',
		fieldLabel:'服务状态',
		displayField : 'name',
		valueField : 'id',
//		padding : '5',
		editable : false,
		emptyText : '--请选择服务状态--',
		store : scriptStatusStore,
		width : reviewSwitch?'20.3%':'20.1%',
		labelAlign : 'right',
		listeners: {
            afterRender: function(combo) {
               if(filter_scriptStatus=='-10000') {
					combo.setValue(scriptStatusStore.getAt(0).data.id);
				} else if(filter_scriptStatus=='-1'){
					combo.setValue(scriptStatusStore.getAt(1).data.id);
				}else if(filter_scriptStatus=='1'){
					combo.setValue(scriptStatusStore.getAt(2).data.id);
				}else if(filter_scriptStatus=='3'){
					combo.setValue(scriptStatusStore.getAt(3).data.id);
				}else if(filter_scriptStatus=='2'){
					combo.setValue(scriptStatusStore.getAt(4).data.id);
				}
            },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	var serviceTypeStore = Ext.create('Ext.data.Store', {
		fields : [ 'value', 'text' ],
		data : [ {
			"value" : "-1",
			"text" : "全部"
		}, {
			"value" : "0",
			"text" : "应用"
		},
		 {
			 "value": "1",
			 "text": "采集"
		}]
	});
	var serviceTypeParam = Ext.create('Ext.form.field.ComboBox', {
		name : 'serviceType',
		padding : '0 5 0 0',
		labelWidth : 65,
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '服务类型',
		displayField : 'text',
		valueField : 'value',
		editable : false,
		emptyText : '--请选择服务类型--',
		store : serviceTypeStore,
		value :filter_serviceType,
		listeners : {
		afterRender : function(combo) {
			combo.setValue(filter_serviceType);
			}
		}
	});
	var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'emScript',
		labelWidth : 65,
		queryMode : 'local',
		fieldLabel : '是否应急',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		hidden :true,		
		emptyText : '--请选择是否应急--',
		store : emScriptStore,
		width : '20.3%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	var appSysObj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '所属系统',
        emptyText : '--请选择所属系统--',
        labelWidth: 65,
        hidden :!reviewSwitch,
        labelAlign: 'right',
        width: '20.3%',
        store: appSysStore,
        //padding: '0 0 5 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        mode: 'local',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
	
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		labelWidth : 65,
//		padding : '5',
		width :'20%',
        labelAlign : 'right',
        value: filter_serviceName,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	var serviceid = new Ext.form.TextField({
	    name : 'serviceid',
	    fieldLabel : '服务号',
	    emptyText : '--请输入服务号--',
	    labelWidth : 65,
//		padding : '5',
	    width :'20%',
	    labelAlign : 'right',
	    listeners: {
	    specialkey: function(field, e){
	    if (e.getKey() == e.ENTER) {
	        pageBar.moveFirst();
	    }
	}
	}
	});
	
	
	var updateUser = new Ext.form.TextField({
		name : 'updateUser',
		fieldLabel : '最后修改人',
		emptyText : '--请输入最后修改人--',
		labelWidth : 79,
//		padding : '5',
		width :'20%',
        labelAlign : 'right',
        value: filter_updateUser,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var isExam;	
	var searchItems ;
	
	 function queryData(){
			pageBar.moveFirst();
			var filter_serviceName = sName.getValue();
			var filter_scriptStatus = scriptStatusCb.getValue();
			var filter_patFromValue =platFromCombobox.getValue(); 
			var filter_serviceTypeValue = serviceTypeParam.getValue();
			var filter_usetj = usetj.getValue();
			var filter_usenumtjText = usenumtjText.getValue();
			var filter_succtj = succtj.getValue();
			var filter_succtjText = succtjText.getValue();
			var filter_serviceid = serviceid.getValue();
		
			var filter_updateUser=updateUser.getValue();
			filter = {
				'filter_bussId': filter_bussId,
				'filter_bussTypeId': filter_bussTypeId,
				'filter_scriptName': filter_scriptName,
				'filter_serviceName': filter_serviceName,
				'filter_scriptType': filter_scriptType,
				'filter_scriptStatus':filter_scriptStatus,
				'filter_patFromValue':filter_patFromValue,
				'filter_serviceType':filter_serviceTypeValue,
				'filter_usetj':filter_usetj,
				'filter_usenumtjText':filter_usenumtjText,
				'filter_succtj':filter_succtj,
				'filter_serviceid':filter_serviceid,
				'filter_succtjText':filter_succtjText,
				'filter_updateUser':filter_updateUser
				
			};
	}; 
	
		searchItems = Ext.create('Ext.ux.ideal.form.Panel', {
		 	region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	bodyCls : 'x-docked-noborder-top',
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				border : false,
				baseCls:'customize_gray_back',
				dock : 'top',
				items: [sName,serviceid,serviceTypeParam,usetj,usenumtjText,succtj,succtjText]
			},{
				xtype : 'toolbar',
				border : false,
				baseCls:'customize_gray_back',
				dock : 'top',
				items: [ scriptStatusCb,updateUser,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() { 
					queryData();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
						filter = {};
					}
				},{
					xtype: 'button',
					cls:'Common_Btn',
					margin: '0 5 0 0',
					text : '返回',
					hidden : !requestFromC3Char,
					handler: function(){
							popNewTab('', 'pandect2.do', {},10, true);
					}
				}]
			}]
		});
	 
	 Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'uuid'         ,type : 'String'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'isshare',type : 'string'},
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'},
		    {name : 'isEmScript' ,type : 'string'},
		    {name : 'appSystem' ,type : 'string'},
		    {name : 'useTimes' ,type : 'int'},
		    {name : 'winTimes' ,type : 'string'},
		    {name : 'dbType' ,type : 'string'},
		    {name : 'ssuer' ,type : 'string'},
		    {name : 'startType' ,type : 'string'},
		    {name : 'serviceType' ,type : 'string'},
		    {name : 'isExam' ,type : 'int'},
		    {name : 'outTableName' ,type : 'string'},
		    {name : 'sqlModel' ,type : 'string'},
		    {name : 'serviceId' ,type : 'string'},
		    {name : 'createUserName' ,type : 'string'},
		    {name : 'updateUserName' ,type : 'string'},
		    {name : 'keywords' ,type : 'string'}
	    ]
	});
	
	scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : itemsPerPage,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'scriptService/queryServiceForMySelfWithoutContent.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	scriptServiceReleaseStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		serviceName:sName.getValue().trim(),
		    		scriptStatus:scriptStatusCb.getValue(),
		    		onlyScript: 1,
		    		isEmScript:emScriptCb.getValue(),
		    		appId:appSysObj.getValue(),
		    		serviceType:serviceTypeParam.getValue(),
		    		platform:platFromCombobox.getValue(),
		    		switchFlag:1,
		            usetj : usetj.getValue(),
		            usenumtjText : usenumtjText.getValue(),
		            succtj : succtj.getValue(),
		            succtjText : succtjText.getValue(),
		            serviceId:serviceid.getValue(),
		            updateUser:updateUser.getValue()
		            
		    };
		    
		    Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
	    });
	
	var scriptServiceReleaseColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 50
		}, 
		{
		    text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
		    text : '服务主键uuid',
		    dataIndex : 'uuid',
		    width : 40,
		    hidden : true
		},
		{
		    text : '服务号',
		    dataIndex : 'serviceId',
		    width : 150,
		    renderer : function(value, metadata) {
		    metadata.tdAttr = 'data-qtip="' + value + '"';
		    return value;
		    }
		},
		{
			text : '服务名称',
		    dataIndex : 'serviceName',
		    minWidth : 150,
		    flex:1,
		    renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},
		{
			text : '脚本名称',
			dataIndex : 'scriptName',
			width : 160,
			hidden:true
		}, 
		{
		    text : '一级分类',
		    dataIndex : 'sysName',
		    hidden:db_f_class,
		    width :120 
		}, 
		{
			text : '二级分类',
			dataIndex : 'bussName',
			hidden:db_s_class,
			width :120 
		},
		{
		    text : '类型',
		    dataIndex : 'scriptType',
		    width : 80,
		    hidden:true,
		    renderer:function(value,p,record,rowIndex){
		    	var backValue = "";
				if (value == "sh") {
					backValue = "shell";
				} else if (value == "perl") {
					backValue = "perl";
				} else if (value == "py") {
					backValue = "python";
				} else if (value == "bat") {
					backValue = "bat";
				} else if (value == "sql") {
					backValue = "sql";
				}
				return backValue;
		    }
		}, 
		{
			text : '适用平台',
			dataIndex : 'platForm',
			width :100,
			hidden:true,
		},{
			text : '数据库',
		    dataIndex : 'dbType',
		    width : 80,
		    renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},{
			text : '版本号',
		    dataIndex : 'version',
		    width : 80,
		    hidden:db_versionswitch
		},{
			text : '服务类型',
		    dataIndex : 'serviceType',
		    hidden:db_serviceType,
		    width : 80
		},{
			text : '发布人',
		    dataIndex : 'ssuer',
		    width : 150,
		    hidden:db_ssuer
		},
		{
			text : '使用次数',
			dataIndex : 'useTimes',
			width : 80
		},
		{
			text : '成功率',
			dataIndex : 'winTimes',
			width : 80
		},
		{
			text : '服务状态',
			dataIndex : 'status',
			width :80,
			renderer:function(value,p,record,rowIndex){
		    	if(value==-1) {
		    		return '<font color="#F01024">草稿</font>';
		    	} else if (value==1) {
		    		return '<font color="#0CBF47">已上线</font>';
		    	} else if (value==2) {
		    		return '<font color="#FFA602">审核中</font>';
		    	} else if (value==3) {
		    		return '<font color="#13B1F5">已共享</font>';
		    	} else if (value==9) {
		    		return '<font color="">已共享未发布</font>';
		    	} else {
		    		return '<font color="#CCCCCC">未知</font>';
		    	}
		    }
		},
		/*{
			text : '发布状态',
		    dataIndex : 'startType',
		    width : 100
		},*/
		{
 			text : '服务运行方式',
			dataIndex : 'sqlModel',
			hidden : true,
			width : 20
		},
		{
			text : '创建人',
		    dataIndex : 'createUserName',
		    width : 70,
		    flex:1,
		    hidden:db_createUserNameSwitch
		},
		{
			text : '修改人',
		    dataIndex : 'updateUserName',
		    width : 70,
		    flex:1,
		    hidden:db_updateUserNameSwitch
		},
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			align:'left',
			width:235,
			labelWidth:150,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid'); // 其实是requestID
				var serviceName = record.get('serviceName');
				var scriptType = record.get('scriptType');
				var bussId = record.get('bussId');
				var bussTypeId  =record.get('bussTypeId');
				var version = record.get('version');
				var status = record.get('status');
				var uuid = record.get('uuid');
				var serviceIdNum = record.get('serviceId');
				var hasVersion = 0;
				if(version) {
					hasVersion = 1; // 最新版本有 版本号
				}
				var editFuncName = "editScript";
				var viewVersionFuncName = "viewVersion";
				
				//下面var是光大新增
				var serviceType=record.get("serviceType");
				var scriptName=record.get("scriptName");
				//使用平台
				var platForm=record.get("platForm");
				//发起审核
				var isExam=record.get("isExam");
				//数据库类型
				var dbType=record.get("dbType");
				var sqlModel=record.get("sqlModel")
				var retHtml='<span class="switch_span">'+
       			   	'<a href="javascript:void(0)" onclick="'+editFuncName+'('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+hasVersion+',\''+uuid+'\',\''+serviceType+'\',\''+scriptName+'\','+isExam+',\''+platForm+'\',\''+dbType+'\',\''+serviceIdNum+'\','+sqlModel+',\''+scriptType+'\')">'+
       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"></img>&nbsp;编辑'+
       			   	'</a>'+
					  '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
       				retHtml=retHtml+ '<span class="switch_span">'+
       			   	'<a href="javascript:void(0)" onclick="'+viewVersionFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+',\''+scriptType+'\',\''+uuid+'\')">'+
       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_version"></img>&nbsp;查看版本'+
       			   	'</a>'+
					  '</span>' +'&nbsp;&nbsp;&nbsp;&nbsp;';
					  return retHtml;

			}
		}
	];

	  var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: scriptServiceReleaseStore,
		  	dock: 'bottom',
			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			displayInfo: true,
			border:false,
			displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
			emptyMsg: "没有记录"
		  });
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	var selModel=Ext.create('Ext.selection.CheckboxModel', {
		id:'selModelSS',
		checkOnly: true
	});
	var scriptServiceitems=[
						{
						xtype : 'toolbar',
						border : false,
						//baseCls:'customize_gray_back',
						dock : 'top',
						items: [
							'->',{
							text : '发布',
							hidden:true,
							//	hidden :publishSwitch,
							cls : 'Common_Btn',
							handler : function(){
								var seledCnt = selModel.getCount();
								if(seledCnt != 1){
									Ext.MessageBox.alert("提示", "请选择要发布的记录，且每次只能选择一条！");
									return ;
								}
								var ss = selModel.getSelection();
								var version = ss[0].data.version; 
								isExam = ss[0].data.isExam;
								var hasVersion = 0;

								if(version==1){
									version=true;
								}else{
									version=false;
								}
								
								if(version) {			
									hasVersion = 1; // 最新版本有 版本号
								}

								publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
								
								
							}
						}, '-',
						{
							text: '删除',
							itemId: 'delete',
							cls : 'Common_Btn',
							// disabled: true,
							handler: deleteServiceRelease
						}
//						,{
//							text : '导入',
//							cls:'Common_Btn',
//							handler : uploadExcel
//						}
						]
				}];
//	//导入
//    function uploadExcel(){
//    	var uploadWindows;
//    	var uploadForm;
//        uploadForm = Ext.create('Ext.form.FormPanel',{
//        	border : false,
//        	items : [{
//            	xtype: 'filefield',
//    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
//    			fieldLabel: '选择文件',
//    			labelWidth: 80,
//    			anchor: '90%',
//    			margin: '10 10 0 40',
//    			buttonText: '浏览'
//            }],
//            buttonAlign : 'center',
//            buttons :[{
//            	text : '确定',
//            	handler :upExeclData
//            },{
//            	text : '取消',
//            	handler : function(){
//            		uploadWindows.close();
//            	}
//            }]
//        });
//        /**
//         * Excel导入Agent信息窗体
//         */
//        uploadWindows = Ext.create('Ext.window.Window', {
//    		title : 'Excel导入',
//    		layout : 'fit',
//    		height : 150,
//    		width : 600,
//    		modal : true,
////    		autoScroll : true,
//    		items : [ uploadForm ],
//    		listeners : {
//    			close : function(g, opt) {
//    				uploadForm.destroy();
//    			}
//    		}
//    	});
//        uploadWindows.show();
//        function upExeclData(){
//        	var form = uploadForm.getForm();
//    		var hdupfile=form.findField("fileName").getValue();
//    		if(hdupfile==''){
//    			Ext.Msg.alert('提示',"请选择文件...");
//    			return ;
//    		}
//    		uploadTemplate(form);
//        }
//        function uploadTemplate(form) {
//      	   if (form.isValid()) {
//             form.submit({
//            	 url: 'scriptService/importScriptManage.do',
//                 success: function(form, action) {
//                	var respText = Ext.JSON.decode(action.response.responseText);
//			    	var msg = respText.message;//提示信息
////			    	var info = respText.info;//
////					var isOk =  respText.isOk;//是否导入成功
//					//提示消息
//					Ext.Msg.alert('提示', msg);
//					var sumsg = Ext.decode(action.response.responseText);
//					console.log(sumsg);
//					//导入成功，重新加载数据
////                    var sumsg = Ext.decode(action.response.responseText).message;
////                    Ext.Msg.alert('提示',sumsg);
//            		uploadWindows.close();
//            		scriptServiceReleaseStore.reload();
//                    return;
//                 },
//                 failure: function(form, action) {
//                     var msg = Ext.decode(action.response.responseText).message;
//                     Ext.Msg.alert('提示',msg);
//                   return;
//                 }
//             });
//      	   }
//      	 }
//    }
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		overflowY:'hidden',
	    store : scriptServiceReleaseStore,
	    selModel : selModel,
	    plugins: [ cellEditing ],
	    dockedItems:scriptServiceitems,
	    border:true,
	    bbar : pageBar,
	    padding : panel_margin,
	    columnLines : true,
	    cls:'customize_panel_back',
	    columns : scriptServiceReleaseColumns
	});
	 var mainPanel = Ext.create('Ext.panel.Panel',{
		 	renderTo : "scriptService_grid_area",
	        width : contentPanel.getWidth(),
		    height :contentPanel.getHeight()-modelHeigth,
	        bodyPadding : grid_margin,
		    border : true,
	        layout: 'border',
	        bodyCls:'service_platform_bodybg',
	        cls:'customize_panel_back',
	        items : [searchItems,scriptServiceReleaseGrid]
	});
	 
	 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function (){
			mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
//			scriptServiceReleaseGrid.setHeight (contentPanel.getHeight ()-55);
		});
		
  
	/* 解决IE下trim问题 */
	String.prototype.trim=function(){
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(mainPanel);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});
	
	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do?dbaasFlag=1',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	

	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "审核人",
	    labelWidth: 93,
//	    padding: 5,
	    labelAlign:'right',
	    store: auditorStore_sm,
	    queryMode: 'local',
//	    width: 200,
	    columnWidth:.98,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    editable : true,
	    valueField: 'loginName',//,
	    listeners: { //监听 
	        render : function(combo) {//渲染 
	            combo.getStore().on("load", function(s, r, o) { 
	                combo.setValue(r[0].get('loginName'));//第一个值 
	            });
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
//			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_sm.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_sm.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_sm.setValue("");
//					 	return;
//					} 
//				}
//				
//			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            } 
	    } 
	  });
	
	planTime_sm = Ext.create('Go.form.field.DateTime',{
	    fieldLabel:'计划时间',
	    format:'Y-m-d H:i:s',
	    labelWidth : 65,
	    hidden:true,
//	    width:200,
	    columnWidth:.98,
	    margin : '10 0 0 0'
	  });
	
	pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        labelAlign:'right',
        emptyText: '',
        labelWidth: 93,
        margin : '10 0 0 0',
        maxLength: 255, 
        height: 55,
        columnWidth:.98,
        autoScroll: true
    });
	

	
	Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	appSysStore1.on('load', function() {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
	var appSysObj1 = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '应用系统',
        emptyText : '--请选择应用系统--',
        hidden : !reviewSwitch,
        multiSelect: true,
        labelWidth: 93,
        labelAlign : 'right',
        columnWidth: .95,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function(combo, records, eOpts) {
                if (records) {
                	chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                    	chosedAppSys.push(records[i].data.id);
                    }
                } 

            },
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
	var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        hidden :true,
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });
	var tableName = new Ext.form.TextField({
		name : 'tablename',
		fieldLabel : '表名称',
		displayField : 'tablename',
		emptyText : '',
		labelWidth : 93,
		columnWidth : .98,
		labelAlign:'right'
	});

	var forbidden = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否禁用旧版本',
        inputValue: 1,
        width: 120,
        hidden:true,
        margin: '10 0 0 10'
    });
	
	var serviceAutoStore_sm = Ext.create('Ext.data.Store', {
	    fields: ['iid', 'serviceAuto'],
	    data : [
	        {"iid":"0", "serviceAuto":"DBA"},
	        {"iid":"1", "serviceAuto":"项目组"}
	    ]
	});
	var serviceAuto_sm = Ext.create('Ext.form.field.ComboBox', {
		labelWidth: 93,
		labelAlign:'right',
		name: 'scriptLevel',
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '服务权限',
        margin : '10 0 0 0',
        displayField: 'serviceAuto',
        valueField: 'iid',
        editable: false,
//        hidden: !reviewSwitch,
        emptyText: '--请选择服务权限--',
        store: serviceAutoStore_sm
    });
	auditing_form_sm = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	bodyCls : 'x-docked-noborder-top',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
//	    	layout:'form',
	    	anchor:'98%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,		    	
	    		items:[planTime_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[tableName]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[appSysObj1,isEMscript]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_sm]
	    	},{
				layout : 'column',
				border : false,
				items : [ serviceAuto_sm ]
			},{
	    		layout:'column',
		    	border : false,
		    	items:[pubDesc_sm]
	    	}]
	    }]
	});
	
	function deleteServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt < 1){
			Ext.MessageBox.alert("提示", "请选择要删除的服务！");
			return ;
		}
		var ss = selModel.getSelection();
		for ( var i = 0, len = ss.length; i < len; i++) {
			if(ss[i].data.status!=-1) {
				Ext.MessageBox.alert("提示", "只能删除状态为'草稿'的服务！");
				return ;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认删除", "是否删除选中的服务", function(id){if(id=='yes') release(1);});
	}
	
    
	function release(optionState){
		var url = 'scriptService/serviceRelease.do';
		var message="服务发布成功";
		var errorMessage="服务发布失败";
		if(optionState==1){
			message="服务删除成功";
			errorMessage="服务删除失败";
			url = "scriptService/deleteScriptForTest.do";
	    } 		
		var jsonData = getSelectedJsonData();
		if(jsonData=="[]"){
			Ext.MessageBox.alert("提示", signMessage);
			return ;
		}	
	    Ext.Ajax.request({
		    url : url,
		    method : 'POST',
		    params : {
		  	  jsonData : jsonData,
		  	  optionState:optionState,
		  	  shareType:shareType,
		  	  chosedShareIds:chosedShareIds,
		  	  isflow:0,
		  	  isCustomTask:0
		    },
		    success: function(response, opts) {
		    	if(optionState==1) {
		    		var message1 = Ext.decode(response.responseText).message;
		    		Ext.MessageBox.show({
		                title : "提示",
		                msg : message1,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		    	} else {
		    		var success = Ext.decode(response.responseText).success;
			        //var message = Ext.decode(response.responseText).message;
			        if (success) {
			            Ext.MessageBox.show({
			                title : "提示",
			                msg : message,
			                buttonText: {
			                    yes: '确定'
			                },
			                buttons: Ext.Msg.YES
			              });
			          } else {
			            Ext.MessageBox.show({
			              title : "提示",
			              msg : errorMessage,
			              buttonText: {
			                  yes: '确定'
			              },
			              buttons: Ext.Msg.YES
			            });
			          }
		    	}
		    	scriptServiceReleaseStore.reload();
		        
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
	}
	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedJsonData(){
		var flowIdList = scriptServiceReleaseGrid.getSelectionModel().getSelection();
		if (flowIdList.length < 1) {
			return;
		}
		var jsonData = "[";
		for ( var i = 0, len = flowIdList.length; i < len; i++) {
			if (i == 0)
			{
				jsonData = jsonData + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			} else {
				jsonData = jsonData + "," + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			}
		}
		jsonData = jsonData + "]";
		return jsonData ;
	}
	function clearQueryWhere(){
		sName.setValue('');
		scriptStatusCb.setValue('');
		appSysObj.setValue('');
		emScriptCb.setValue('');
		platFromCombobox.setValue('');
		usetj.setValue('-1');
		succtj.setValue('-1');
		usenumtjText.setValue('');
		succtjText.setValue('');
		usenumtjText.hide();
        succtjText.hide();
        serviceid.setValue('');
	}
	//从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key ,jsonObj){
		 //var eValue=eval('jsonObj.'+key);  
		 return jsonObj[''+key+''];
	}
	
	
	function publishScript(iid,a,b,c,hasVersion, status){
			
		if(hasVersion==1) {
			Ext.Msg.alert('提示', "该服务已经发布过！");
			return;
		}
		
		if (status==2) { // 处于审核中
			Ext.Msg.alert('提示', "该服务正处于审核中！");
			return;
		}
			
  		var ss = selModel.getSelection();
  		var mes="";
  		var tableFlag = false;
  		if("2"==ss[0].data.sqlModel){
  			radio=4;
  			Ext.Ajax.request({
                url : 'scriptTableSearch.do',
                method : 'POST',
                async: false,
                params : {
                    iid : iid
                },
                success: function(response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    tName = Ext.decode(response.responseText).TABLENAME;
                    if(!success) {
                        tableName.show();
                        tableFlag=false;
                    }else {
                        tableFlag=true;
                        tableName.hide();
                    }
                }
            })
            tableName.show();
  		}else{
  			tableName.hide();
  		}
  		
  		
  		var scriptPublishsIds = new Array();
		scriptPublishsIds.push(ss[0].data.iid);
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认发布", mes+"是否确认发布该服务", function(id){
			if(id=='yes') {
	        	if (!publishAuditingSMWin) {
					publishAuditingSMWin = Ext.create('widget.window', {
		                title: '确认审核信息',
		                closable: true,
		                closeAction: 'hide',
		                modal: true,
		                width: 600,
		                minWidth: 350,
		                height: reviewSwitch?330:300,
		                layout: {
		                    type: 'border',
		                    padding: 5
		                },
		                items: [auditing_form_sm],
		                dockedItems : [ {
							xtype : 'toolbar',
//  										baseCls:'customize_gray_back',
							dock : 'bottom',
							layout: {pack: 'center'},
							items : [ {
				  			xtype: "button",
				  			cls:'Common_Btn',
				  			text: "确定", 
				  			handler: function () { 
				  				//判断输入的审核人是否合法 start
				            	var displayField =auditorComBox_sm.getRawValue();
								if(!Ext.isEmpty(displayField)){
									//判断输入是否合法标志，默认false，代表不合法
									var flag = false;
									//遍历下拉框绑定的store，获取displayField
									auditorStore_sm.each(function (record) {
										//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
									    var data_fullName = record.get('fullName');
									    if(data_fullName == displayField){
									    	flag =true;
									    	//combo.setValue(record.get('loginName'));
									    }
									});
									if(!flag){
									 	Ext.Msg.alert('提示', "输入的审核人非法");
									 	auditorComBox_sm.setValue("");
									 	return;
									} 
								}
								//判断输入的审核人是否合法  end
				  				var planTime = planTime_sm.getRawValue();
				  				var scriptLevel =100;

				  				
				  				var publishDesc = pubDesc_sm.getValue();
				  				var auditor = auditorComBox_sm.getValue();
				  				var isEmScript = isEMscript.getValue();
				  				if(isEmScript) {
				  					isEmScript = 1;
				  				}else{
				  					isEmScript = 0;
				  				}
					  			var isForbidden = forbidden.getValue();
				  				if(isForbidden) {
				  					isForbidden = 1;
				  				}else{
				  					isForbidden = 0;
				  				}
				  				var serviceAuto=serviceAuto_sm.getValue();
				  				if(radio=='4'){
					  				var tablename = tableName.getValue();
									if(tableFlag) {
									    tablename=tName;
									}
					  				if (!tablename) {
					  					Ext.Msg.alert('提示',"没有填写表名称！");
					  					return;
					  				}
						  			if (!serviceAuto) {
					  					Ext.Msg.alert('提示',"没有选择服务权限！");
					  					return;
					  				}
				  				}

				  				if(!publishDesc) {
				  					Ext.Msg.alert('提示', "没有填写发布申请说明！");
				  					return;
				  				}
				  				if(publishDesc.length > 255) {
				  					Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
				  					return;
				  				}
				  				if(!auditor) {
				  					Ext.Msg.alert('提示', "没有选择审核人！");
				  					return;
				  				}
				  				
				  				Ext.Ajax.request({				  				
				  				    url : 'scriptPublishAuditing.do',
				  				    method : 'POST',
				  				    params : {
				  				    	sIds : scriptPublishsIds,
				  				    	planTime: planTime,
				  				    	scriptLevel: scriptLevel,
				  				    	publishDesc: publishDesc,
				  				    	auditor: auditor,
				  				  	    flag:0, //0-来着个人服务库
					  				  	isEmScript: isEmScript,
				  				  	  	appSysIds:chosedAppSys,
				  				  	  	isForbidden:isForbidden,
				  				  	  	tablename:tablename,
				  				  	  	switchFlag:1,
				  				  	  	radio:radio,
				  				  	  	serviceAuto:serviceAuto,
				  				  	  	tableFlag:tableFlag
				  				    },
				  				    success: function(response, opts) {
				  				        var success = Ext.decode(response.responseText).success;
				  				        var message = Ext.decode(response.responseText).message;
				  				        if(!success) {
				  				        	Ext.MessageBox.alert("提示", message);
				  				        } else {
				  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
				  				        }
				  				      scriptServiceReleaseStore.load();
				  				      publishAuditingSMWin.close();
				  				      
				  				    },
				  				    failure: function(result, request) {
				  				    	secureFilterRs(result,"操作失败！");
				  				    	publishAuditingSMWin.close();
				  				    }
				  			    });
				  				
					        }
				  		}, { 
				  			xtype: "button", 
				  			cls:'Common_Btn',
				  			text: "取消", 
				  			handler: function () {
				  				this.up("window").close();
				  			}
				  		}]
					}]
		            });		            
		        }
				publishAuditingSMWin.show();
				auditorStore_sm.load();
				planTime_sm.setValue('');
				pubDesc_sm.setValue('');
				auditorComBox_sm.setValue('');
				isEMscript.setValue(0);
				forbidden.setValue(0);
				appSysObj1.setValue('');
				chosedAppSys='';
	        
			}
		});
		
	}

	
	
});



//编辑按钮执行一个.do跳转到编辑页面
function editScript(iid,status,a,b,c,hasVersion,uuid,serviceType,scriptName,isExam,platForm,dbType,serviceIdNum,sqlModel,scriptType){
	 if (status == 2 ) { // 已经不是草稿状态，处于审核中或者已经上线
		Ext.Msg.alert('提示', "该服务正在审核中，不能编辑！");
		return;
	}else{
		destroyRubbish(); //销毁本页垃圾
		contentPanel.getLoader().load({
			url: 'forwardEditScriptDbaas.do?serviceId='+iid + '&hasVersion='+hasVersion+ '&uuid='+uuid+ '&serviceName='+a+ '&serviceType='+serviceType+'&scriptName='+scriptName+'&isExam='+isExam+'&platForm='+platForm+'&dbType='+dbType+'&switchFlag=1'+'&serviceIdNum='+serviceIdNum+'&sqlModel='+sqlModel+'&scriptType='+scriptType+'&status='+status,
			params: filter,
			scripts: true
		});
	}
}
function editScriptFlow(iid,status,serviceName,bussId,bussTypeId){
	if (status == 2 ) { // 已经不是草稿状态，处于审核中或者已经上线
		Ext.Msg.alert('提示', "该服务正在审核中，不能编辑！");
		return;
	}else{
		destroyRubbish(); //销毁本页垃圾
		var params = filter;
		params['iid'] = iid;
		params['serviceName'] = serviceName;
		params['actionType'] = 'edit';
		params['bussId'] = bussId;
		params['bussTypeId'] = bussTypeId;
		params['flag'] = 0;
		contentPanel.getLoader().load({url: 'flowCustomizedInitScriptService.do',
			params: params,
			scripts: true});
	}
}


function destroyRubbish1() {
	contentPanel.clearListeners();
	contentPanel.getLoader().clearListeners();
}



function viewVersion(iid,serviceName,bussId,bussTypeId, scriptType,uuid,filter_serviceName,filter_serviceType){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: 'scriptViewVersion.do?serviceId='+iid +'&flag=0'+'&uuid='+uuid+'&switchFlag=1',
		params: filter,
		scripts: true});
}


