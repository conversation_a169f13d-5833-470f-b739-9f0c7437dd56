<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<% 
	long iid=Long.parseLong(request.getParameter("iid"));
	boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	String configNum =  Environment.getInstance().getConfigNum();
	boolean taskApplyCheckSysAdminSwitch = Environment.getInstance().geTaskApplyCheckSysAdminSwitch();
	boolean scriptTaskApplyShowTimeoutSwitchForWhite = Environment.getInstance().getScriptTaskApplyShowTimeoutSwitch();
	boolean taskApplyUploadAttachmentSwitch =  Environment.getInstance().geTaskApplyUploadAttachmentSwitch();
	boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
	boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
	boolean templateSwich = Environment.getInstance().getTempUploadTabSwitch();
	// 邮储 任务申请-任务申请页面/常用任务配置页面，参数默认值字段变为不可编辑
	boolean paramEditDisable = Environment.getInstance().psbcParamDisEditSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
	//邮储   任务申请-增加服务器不自动加载数据开关
	boolean enableAutoLoadSwitch = Environment.getInstance().getScriptAutoloadAgentSwitch();
	// 福建农信CI
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
	// 是否查询变更业务系统绑定的agent
	boolean agentFromChangeSys = Environment.getInstance().getScriptTaskApplySysAgentSwitch();
%>
<html>
<head>
<script type="text/javascript">
	var fjnxCISwitch = <%=fjnxCISwitch%>
	var editDelay = <%=StringUtils.isNotEmpty(request.getParameter("editDelay"))%>
	var taskNameStr = '<%=request.getParameter("taskName")%>'
	var workItemId_rspe="<%=request.getParameter("workItemId")%>";
	var isTimetask = '<%=request.getParameter("isTimetask")%>';
	var taskTime = '<%=request.getParameter("taskTime")%>';

	var labelEdit="<%=request.getAttribute("labels")%>";
	var iidForTaskAudi="<%=iid%>";
	var number = "<%=configNum%>";
	var scriptLevelForTaskAudi='<%=request.getParameter("scriptLevel")%>';
	var serviceNameForTaskAudi='<%=request.getParameter("serviceName")%>';
	var scriptTypeForTaskAudi='<%=request.getParameter("scriptType")%>';
	var sysAdminFlag = <%=request.getAttribute("sysAdminFlag")%>;
	var taskApplyCheckSysAdminSwitch = <%=taskApplyCheckSysAdminSwitch%>;
	//北京邮储 超时时间获取
	var timeoutForTaskAudiForWhite='<%=request.getParameter("timeout")%>';
	var scriptTaskApplyShowTimeoutSwitchForWhite = <%=scriptTaskApplyShowTimeoutSwitchForWhite%>;
	//适用平台
	var scriptPlatmFrom = '<%=request.getParameter("platmFrom")%>'; 
	var checkRadioForTaskAudi = 0;
	var eachNumForA = <%=request.getAttribute("eachNum")%>;
	var loginUser = '<%=request.getAttribute("loginUser")%>';
	var scriptuuid = '<%=request.getAttribute("scriptuuid")%>';
	var suUser = '<%=request.getAttribute("suUser")%>';
	var ssTimerTaskSwitch = <%=request.getAttribute("ssTimerTaskSwitch")%>;
	var execUserSwitch = <%=execUserSwitch%>;
	var taskApplyUploadAttachmentSwitch =<%=taskApplyUploadAttachmentSwitch%>;
	var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
	var CMDBFlag = <%=request.getAttribute("CMDBflag")%>;
	var cmdbFlag;
	var listComBox2;
	var butterflyVerison2;
	//白名单checkBox等开关
	var equipSwitch = '<%=request.getAttribute("equipSwitch")%>';
	//任务管理批量查询开关
	var batchQuerySwitch = "<%=batchQuery%>";
	//按照ip查询还是按照计算机名查询开关（true为山东城商需求，按照ip查询；false为bankCode001需求，按照计算机名查询）
	var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
	//获取uuid
	var searchUUID = '<%=request.getAttribute("searchUUID")%>';
	if(CMDBFlag){
		cmdbFlag = false;
	}else{
		cmdbFlag = true;
	}
	//corn表达式公共方法使用的参数 
	var iworkItemid = 0;
	var agent_store_chosed = Ext.create('Ext.data.Store', {
	     model: 'page.dubbo.scriptService.spdb.agent.agentModel',
	     proxy: {
	     },
	     autoLoad: false
	 });
	var chosedAgentIds = new Array();
	var chosedAgentFlagArray = new Array();//判断是否Windows和非Windows一起选择
	var chosedAgentWinForSPDB = new Ext.window.Window();
    var agentColumnsForSPDB = [];
	var sendEmailUser = Ext.create ('Ext.form.ComboBox',{});
	var chosedCpIds = [];
    var agentListStore = undefined;
    var  checkInumber=false;
	//模板tab页展示开关
	var templateSwitch = <%=templateSwich%>
	// 邮储 任务申请-任务申请页面/常用任务配置页面，参数默认值字段变为不可编辑
	var paramEditDisable = <%=paramEditDisable%>;
	var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>
	var scriptService = '<%=request.getParameter("scriptService")==null?"":request.getParameter("scriptService")%>';
	var params = '<%=request.getParameter("params")==null?"":request.getParameter("params")%>';
	var invoke = '<%=request.getParameter("invoke")==null?"":request.getParameter("invoke")%>';
	var invokeId = '<%=request.getParameter("invokeId")==null?"":request.getParameter("invokeId")%>';
	//邮储   任务申请-增加服务器不自动加载数据开关
	var enableAutoLoadSwitch = <%=enableAutoLoadSwitch%>
	//是否具有高权（判断是否为值班任务申请、值班人员）
	var heightPermissionFlag=<%=request.getAttribute("heightPermissionFlag") == null ? false : request.getAttribute("heightPermissionFlag")%>;
	//agent是否来自绑定的变更业务系统
	var agentFromChangeSys = <%=agentFromChangeSys%>
	var singleNumber = null
</script>
	<%
		if(Environment.getInstance().getScriptTaskApplyAddAgentSwitch()) {
	%>
	<script    async type="text/javascript"
			   src="<%=request.getContextPath()%>/page/dubbo/basicScript/agentModelAndColumnForSPDB.js"  ></script>
	<script    type="text/javascript"
			   src="<%=request.getContextPath()%>/page/dubbo/basicScript/queryAgentInfoForSPDB.js" ></script>
	<%
	}
	%>

<script    type="text/javascript"
   src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageIPSearch.js"></script>
<script    type="text/javascript" 
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageForWhiteScript.js"></script>
</head>
<body>
	<div id="taskAuditingPage_area" style="width: 100%; height: 25%;"></div>
	
</body>
</html>
