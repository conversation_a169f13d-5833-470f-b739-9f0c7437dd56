<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean sendSwitch = Environment.getInstance().getScriptServiceSendSwitch();
	boolean scriptLevelSwitch= Environment.getInstance().getScriptLevelSwitch();
%>
<html>
<head>
<script>
var usenumtj = false;
var db_f_class = <%=request.getAttribute("db_f_class")%>;
var db_s_class = <%=request.getAttribute("db_s_class")%>;
var db_s_level = <%=request.getAttribute("db_s_level")%>;
var db_serviceType = <%=request.getAttribute("db_serviceType")%>;
var db_scriptType = <%=request.getAttribute("db_serviceType")%>;
var db_dbType = <%=request.getAttribute("db_dbType")%>;
var db_ssuer = <%=request.getAttribute("db_ssuer")%>;

var importSwitch = <%=request.getAttribute("importSwitch")%>;
var filter_bussId = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeId = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptType = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
var filter_scriptStatus = '<%=request.getParameter("filter_scriptStatus")==null?-10000:request.getParameter("filter_scriptStatus")%>';
var filter_patFromValue ='<%=request.getParameter("filter_patFromValue")==null?"":request.getParameter("filter_patFromValue")%>';

var filter_updateUser= '<%=request.getParameter("filter_updateUser")==null?"":request.getParameter("filter_updateUser")%>';

var db_usePlantFormswitch = <%=request.getAttribute("db_usePlantFormswitch")%>;
var db_queryIssueRecordswitch= <%=request.getAttribute("db_queryIssueRecordswitch")%>;
var db_sendScriptswitch= <%=request.getAttribute("db_sendScriptswitch")%>;
var filter_serviceType = '<%=request.getParameter("filter_serviceType")==null?-1:request.getParameter("filter_serviceType")%>';


var db_winTimesswitch=<%=request.getAttribute("db_winTimesswitch")%>;
var db_versionswitch=<%=request.getAttribute("db_versionswitch")%>;
var db_scriptNameswitch=<%=request.getAttribute("db_scriptNameswitch")%>;


var publishSwitch=<%=request.getAttribute("publishSwitch")%>;
var serviceIdSwitch=<%=request.getAttribute("serviceIdSwitch")%>;
var db_createUserNameSwitch=<%=request.getAttribute("db_createUserNameSwitch")%>;
var db_updateUserNameSwitch=<%=request.getAttribute("db_updateUserNameSwitch")%>;

//是否脚本看板跳转过来的请求 
var requestFromC3Char = <%=request.getParameter("requestFromC3Char")==null?false:request.getParameter("requestFromC3Char")%>;
var filter = {
		'filter_bussId': filter_bussId,
		'filter_bussTypeId': filter_bussTypeId,
		'filter_scriptName': filter_scriptName,
		'filter_serviceName': filter_serviceName,
		'filter_scriptType': filter_scriptType,
		'filter_scriptStatus':filter_scriptStatus,
		'filter_serviceType':filter_serviceType,
		'filter_patFromValue':filter_patFromValue,
		'filter_updateUser':filter_updateUser
	};
var sendSwitch = <%=sendSwitch%>;

var filter_keywords  = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/scriptManageDbaas.js"></script>
</head>
<body>
<div id="scriptService_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>