/**
 * 浦发特殊需求  agent选择特殊展示样式 model column 对象
 */
Ext.define('page.dubbo.scriptService.spdb.agent.agentModel', {
	extend : 'Ext.data.Model',
	fields : [ 
		{name : 'iid',type : 'int'},
		{name : 'cpid',type : 'int'},
		{name: 'sysName',  type: 'string'},
		{name: 'centerName',  type: 'string'},
		{name: 'igroupid', type: 'string'},
        {name: 'iagentid', type: 'int'},
        //{name: 'sysAdmin', type: 'string'},
        {name: 'userA', type: 'string'},
        {name: 'userB', type: 'string'},
        {name: 'belongIp', type: 'string'},
        {name: 'appAdmin', type: 'string'},
        {name: 'systemInfo', type: 'string'},
        {name: 'ctype', type: 'string'},
        {name: 'middlewareType', type: 'string'},
        { name: 'appName',  type: 'string'},
        { name: 'hostName', type: 'string'},
        { name: 'osType',type: 'string'},
        { name: 'osTypeAgentinfo',type: 'string'},
        { name: 'agentIp', type: 'string'},
        {name: 'agentPort',type: 'string'},
        {name: 'envType',type: 'string'},
        { name: 'agentState', type: 'int'},
        {name: 'osVersion',type: 'string'},//系统版本
        {name: 'runDays',type: 'string'},//运行天数
        {name: 'isHa',type: 'string'},//是否双机
        {name: 'dbType',type: 'string'},//数据库类型
        {name: 'dbVersion',type: 'string'},//数据库版本
        {name: 'middlewareVersion',type: 'string'},//中间件版本
        {name: 'icreateTime',type: 'string'},//纳管时间
        {name: 'startUser',type: 'string'}//纳管启动人
 	]
});

agentColumnsHasStartUser = [ {
			text : '序号',
			width : 50,
			xtype : 'rownumberer'
		}, {
			text : 'iid',
			dataIndex : 'iid',
			flex : 1,
			hidden : true
		},{
            text : 'cpid',
            dataIndex : 'cpid',
            flex : 1,
            hidden : true
        },
		{ text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
        { text: 'iagentid',  dataIndex: 'iagentid',hidden:true},
        {
            dataIndex: 'agentStartUser',
            text: '启动用户',
            editor: {
                allowBlank: true
            },
            width: 100
        },
        {
            text: '计算机名',
            dataIndex: 'hostName',
            width: 200
        },{
            text: 'IP',
            dataIndex: 'agentIp',
            width: 120
        },{
            text: '所属区域',
            dataIndex: 'centerName',
            width: 100
        },
        {text: '系统管理员',  dataIndex: 'sysAdmin',width: 100},
        {text: '应用管理员',  dataIndex: 'appAdmin',width: 100},
        {text: '信息系统名称',  dataIndex: 'systemInfo',width: 100},
        {
            text: '运行天数',
            dataIndex: 'runDays',
            width: 100
        },
        {
            text: '是否双机',
            dataIndex: 'isHa',
            width: 100
        },
        {
            text: 'AgentInfo系统类型',
            dataIndex: 'osTypeAgentinfo',
            width: 100,
            hidden:true
        },
        {
            text: '操作系统类型',
            dataIndex: 'osType',
            width: 100
        },
         {
            text: '操作系统版本',
            dataIndex: 'osVersion',
            width: 100
        }, 
        {
            text: '数据库类型',
            dataIndex: 'dbType',
            width: 100
        },
        {
            text: '数据库版本',
            dataIndex: 'dbVersion',
            width: 100
        },
        {text: '中间件类型',  dataIndex: 'middlewareType',width: 100},
        {
            text: '中间件版本',
            dataIndex: 'middlewareVersion',
            width: 100
        },
        {
            text: '纳管时间',
            dataIndex: 'icreateTime',
            width: 100
        }, 
         {
            text: '纳管用户',
            dataIndex: 'startUser',
            width: 100
        }, 
        {text: '应用类型',  dataIndex: 'ctype',hidden:true,width: 100},
        {
            text: '状态',
            dataIndex: 'agentState',
            //flex: 1,
            width: 110,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        },
        {
            text: 'Agent端口',
            dataIndex: 'agentPort',
            width: 100,
            hidden:true
        },
        {
            text: '环境',
            dataIndex: 'envType',
            hidden: true,
            width: 100,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = '<font">测试</font>';
                } else if (value == 1) {
                    backValue = '<font >生产</font>';
                }
                return backValue;
            }
        }];	
        
        
 agentColumnsForSPDB = [ {
			text : '序号',
			width : 50,
			xtype : 'rownumberer'
		}, {
			text : 'iid',
			dataIndex : 'iid',
			flex : 1,
			hidden : true
		}, {
             text : 'cpid',
             dataIndex : 'cpid',
             flex : 1,
             hidden : true
         },
		{ text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
        { text: 'iagentid',  dataIndex: 'iagentid',hidden:true},
        {
            text: '计算机名',
            dataIndex: 'hostName',
            width: 200
        },{
            text: 'IP',
            dataIndex: 'agentIp',
            width: 120
        },
         {
             text: '所属网段',
             dataIndex: 'belongIp',
             width: 120
         },{
            text: '所属区域',
            dataIndex: 'centerName',
            width: 100
        },
        //{text: '系统管理员',  dataIndex: 'sysAdmin',width: 100},
         {text: '系统管理员A角',  dataIndex: 'userA',width: 100},
         {text: '系统管理员B角',  dataIndex: 'userB',width: 100},
        {text: '应用管理员',  dataIndex: 'appAdmin',width: 100},
        {text: '信息系统名称',  dataIndex: 'systemInfo',width: 100},
        {
            text: '运行天数',
            dataIndex: 'runDays',
            width: 100
        },
        {
            text: '是否双机',
            dataIndex: 'isHa',
            width: 100
        },
        {
            text: 'AgentInfo系统类型',
            dataIndex: 'osTypeAgentinfo',
            width: 100,
            hidden:true
        },
        {
            text: '操作系统类型',
            dataIndex: 'osType',
            width: 100
        },
         {
            text: '操作系统版本',
            dataIndex: 'osVersion',
            width: 100
        }, 
        {
            text: '数据库类型',
            dataIndex: 'dbType',
            width: 100
        },
        {
            text: '数据库版本',
            dataIndex: 'dbVersion',
            width: 100
        },
        {text: '中间件类型',  dataIndex: 'middlewareType',width: 100},
        {
            text: '中间件版本',
            dataIndex: 'middlewareVersion',
            width: 100
        },
        {
            text: '纳管时间',
            dataIndex: 'icreateTime',
            width: 100
        }, 
         {
            text: '纳管用户',
            dataIndex: 'startUser',
            width: 100
        }, 
        {text: '应用类型',  dataIndex: 'ctype',hidden:true,width: 100},
        {
            text: '状态',
            dataIndex: 'agentState',
            //flex: 1,
            width: 110,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        },
        {
            text: 'Agent端口',
            dataIndex: 'agentPort',
            width: 100,
            hidden:true
        },
        {
            text: '环境',
            dataIndex: 'envType',
            hidden: true,
            width: 100,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = '<font">测试</font>';
                } else if (value == 1) {
                    backValue = '<font >生产</font>';
                }
                return backValue;
            }
        }];	