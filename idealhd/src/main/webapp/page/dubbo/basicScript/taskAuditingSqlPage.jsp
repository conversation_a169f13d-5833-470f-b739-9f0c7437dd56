<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<% 
	long iid=Long.parseLong(request.getParameter("iid"));
	boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
	boolean showConfigSwitch = Environment.getInstance().getScriptShowConfigSwitch();
	boolean scriptConvertFlowSwitch = Environment.getInstance().getScriptConvertFlowSwitch();
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	boolean taskApplyUploadAttachmentSwitch =  Environment.getInstance().geTaskApplyUploadAttachmentSwitch();
	String configNum =  Environment.getInstance().getConfigNum();
	boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
%>
<html>
<head>
<script type="text/javascript">
	var iidForTaskAudi="<%=iid%>";
	var number = "<%=configNum%>";
	var scriptuuid = '<%=request.getAttribute("scriptuuid")%>';
	var suUser = '<%=request.getAttribute("suUser")%>';
	var scriptLevelForTaskAudi='<%=request.getParameter("scriptLevel")%>';
	var serviceNameForTaskAudi='<%=request.getParameter("serviceName")%>';
	var scriptTypeForTaskAudi='<%=request.getParameter("scriptType")%>';
	var sessionIdForTaskAuditing = '<%=request.getSession().getId()%>';
	var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;
	var checkRadioForTaskAudi = 0;
	var eachNumForA = <%=request.getAttribute("eachNum")%>;
	var loginUser = '<%=request.getAttribute("loginUser")%>';
	var ssTimerTaskSwitch = <%=request.getAttribute("ssTimerTaskSwitch")%>;
	var scriptPlatmFrom = '<%=request.getParameter("platmFrom")%>'; 
	var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
	var execUserSwitch = <%=execUserSwitch%>;
	var CMDBFlag = <%=request.getAttribute("CMDBflag")%>;
	var cmdbFlag;
	if(CMDBFlag){
		cmdbFlag = false;
	}else{
		cmdbFlag = true;
	}
	var iworkItemid= 0;
	var agent_store_chosed = Ext.create('Ext.data.Store', {
	     model: 'page.dubbo.scriptService.spdb.agent.agentModel',
	     proxy: {
	     },
	     autoLoad: false
	 });
	var chosedAgentIds = new Array();
	var chosedAgentFlagArray = new Array();//判断是否Windows和非Windows一起选择
	var chosedAgentWinForSPDB = new Ext.window.Window();
    var agentColumnsForSPDB = [];
    var agentListStore = Ext.create('Ext.data.Store', {
	     model: 'page.dubbo.scriptService.spdb.agent.agentModel',
	     proxy: {
	     },
	     autoLoad: false
	 }); 
	var showConfigSwitch  = <%=showConfigSwitch%>;
	var scriptConvertFlowSwitch  = <%=scriptConvertFlowSwitch%>;
	var taskApplyUploadAttachmentSwitch =<%=taskApplyUploadAttachmentSwitch%>;
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingSqlPage.js"></script>
</head>
<body>
	<input type="hidden" id="taskAuditingSqlPageExecUserNameText" />
	<div id="taskAuditingSqlPage_area" style="width: 100%; height: 25%;"></div>
</body>
</html>
