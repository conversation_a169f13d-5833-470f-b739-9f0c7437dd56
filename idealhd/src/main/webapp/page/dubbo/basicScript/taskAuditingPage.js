Ext.onReady(function () {
    //邮储 删除附件判断使用
    let delTempFlag = scriptTaskApplyAttachmentParamsSwitch ? '1' : '0';
    //枚举表主键
    var piid;
    var sysID;
    var busID;
    var DetailWinTi;
    var resGroupFlag;
    var publishAuditingSMWin;
    var project_panelScript;
    var auditing_form_sm;
    // 清理主面板的各种监听时间
//    destroyRubbish();
    var startData = {};
    var globalParams = {};
    //*****浦发需求 启用用户系统管理员校验 使用
    var systemAdminList = new Array();
    systemAdminList.push('root');
    systemAdminList.push('system');
    systemAdminList.push('administrator');
    //*****浦发需求 启用用户系统管理员校验 使用
    var attachmentIds = [];
    //邮储 临时附件表 数组 存的是id和是否临时表标志
    let scriptTempAttachmentArray = [];
    //被删除的脚本绑定的附件id数组
    let scriptTempAttachmentDeleteArray = [];
    var chosedAgentWin;
    var chosedGroupIds = [];
    var chosedGroupNames = [];
    //var chosedAgentIds = new Array();
    var upldWin;
    var upLoadformPane = '';
    var creatCronWin;
    var phoneNum;
    var chosedGroupWin;
    var tempmentIds = [];
    var startUserValue='';
    var startUser;
    Ext.tip.QuickTipManager.init();

    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    bussData.on('load', function (store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load', function (store, options) {
        bussTypeCb.setValue(busID);
    });

    var execDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: descPlaceholderSwitch ? '请输入事件单号或变更单号或执行原因' : '',
        columnWidth: 1,
        margin: '0 10 5 10',
        height: contentPanel.getHeight() * 0.25 - 50,
        autoScroll: true
    });
    var execDescForm = Ext.create('Ext.form.Panel', {
        width: '50%',
        height: contentPanel.getHeight() * 0.25,
        region: 'south',
        border: false,
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        title: '执行描述',
        items: [{
            layout: 'column',
            border: false,
            items: [execDesc]
        }]
    });

    Ext.define('editScriptModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
            {
                name: 'serviceName',
                type: 'string'
            },
            {
                name: 'sysName',
                type: 'string'
            },
            {
                name: 'bussName',
                type: 'string'
            },
            {
                name: 'scriptType',
                type: 'string'
            },
            {
                name: 'scriptName',
                type: 'string'
            },
            {
                name: 'servicePara',
                type: 'string'
            },
            {
                name: 'serviceState',
                type: 'string'
            },
            {
                name: 'excepResult',
                type: 'string'
            },
            {
                name: 'errExcepResult',
                type: 'string'
            },
            {
                name: 'content',
                type: 'string'
            }]
    });
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function (store, options) {
        var queryparams = {
            iid: iidForTaskAudi,
            fromType: 1 // 查ieai_script_services表
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function (store, options, success) {
        /*var reader = store.getProxy().getReader();
        scName.setValue(reader.jsonData.scriptName);
        sName.setValue(reader.jsonData.serviceName);
        excepResult.setValue(reader.jsonData.excepResult);
        errExcepResult.setValue(reader.jsonData.errExcepResult);

        usePlantForm.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);

        var scriptT = reader.jsonData.scriptType;
        if (scriptT == 'sh') {
        	FieldContainer.items.items[0].setValue(true);
            checkRadioForTaskAudi = 0;
            editor.setOption("mode", 'shell');
        } else if (scriptT == 'bat') {
        	FieldContainer.items.items[1].setValue(true);
            checkRadioForTaskAudi = 1;
            editor.setOption("mode", 'bat');
        } else if (scriptT == 'py') {
        	FieldContainer.items.items[3].setValue(true);
            checkRadioForTaskAudi = 3;
            editor.setOption("mode", 'python');
        } else if (scriptT == 'SQL') {
        	FieldContainer.items.items[4].setValue(true);
            checkRadioForTaskAudi = 4;
            editor.setOption("mode", 'sql');
        } else if (scriptT == 'perl') {
        	FieldContainer.items.items[2].setValue(true);
            checkRadioForTaskAudi = 2;
            editor.setOption("mode", 'text/x-perl');
        }
        editor.setOption('value', reader.jsonData.content);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        bussData.load();*/
    });
    /** *********************Panel********************* */
    /*var FieldContainer = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 80,
        name: 'ra_s_type_1',
        items: [{
            name: 'ra_s_type_1',
            width: 80,
            inputValue: 'sh',
            boxLabel: 'shell',
            checked: true,
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        if (checkRadioForTaskAudi != 0) {
                            editor.setOption("mode", 'shell');
                            checkRadioForTaskAudi = 0;
                        }
                    }
                }
            }
        },
        {
            name: 'ra_s_type_1',
            width: 80,
            inputValue: '1',
            boxLabel: 'bat',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        if (checkRadioForTaskAudi != 1) {
                            checkRadioForTaskAudi = 1;
                            editor.setOption("mode", 'bat');
                        }
                    }
                }
            }
        },
        {
            name: 'ra_s_type_1',
            width: 80,
            inputValue: '2',
            boxLabel: 'perl',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForTaskAudi = 2;
                        editor.setOption("mode", 'text/x-perl');
                    }
                }
            }
        },
        {
            name: 'ra_s_type_1',
            width: 80,
            inputValue: '3',
            boxLabel: 'python',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForTaskAudi = 3;
                        editor.setOption("mode", 'python');
                    }
                }
            }
        }]
    });*/


//    var scriptForm = Ext.create('Ext.form.Panel', {
//        width: '20%',
//        height: 230,
//        border: true,
//        layout: 'anchor',
//        title: '基本信息',
//        items: [{
//            border: false,
//            layout: 'column',
//            margin: '5',
//            items: [bussCb]
//        },
//        {
//            border: false,
//            margin: '5',
//            layout: 'column',
//            items: [bussTypeCb]
//        },
//        {
//            border: false,
//            margin: '5',
//            layout: 'column',
//            items: [sName]
//        },
//        {
//            border: false,
//            margin: '5',
//            layout: 'column',
//            items: [scName]
//        },
//        {
//            layout: 'column',
//            border: false,
//            margin: '5',
//            items: [usePlantForm]
//        },
//        {
//            layout: 'column',
//            border: false,
//            margin: '5',
//            items: [excepResult]
//        },
//        {
//            layout: 'column',
//            border: false,
//            margin: '5',
//            items: [errExcepResult]
//        },
//        {
//            layout: 'column',
//            border: false,
//            margin: '5',
//            items: [funcDesc]
//        }]
//    });
    var defultEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true
        })
    });
    var passwordEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true,
            inputType: 'password'
        })
    });
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'paraIid',
            type: 'int'
        },
            {
                name: 'paramType',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            }, {
                name: 'paramValue',
                type: 'string'
            },
            {
                name: 'ruleName',
                type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    //加密参数将参数值转换成明文在前台展示
    paramStore.on ('load', function (me, records, successful, eOpts) {

        // 判断是否为邮储银行itsm跳转过来的
        if (scriptService.length > 0) {
            console.log("ITSM跳转过来的")
            var param1 = base64Decode(params)
            if (params.length > 0) {
                var map = Ext.decode(param1);
                for (let i = 0; i < records.length; i++) {
                    records[i].set('paramValue', map[records[i].get('iid')])
                }
            }
        }

        $.each(records, function(index, record){
            if(record.get('paramType') == 'IN-string(加密)'){
                record.set('paramValue', getSMEncode(record.get('paramValue'),0));
            }
        });
    });

    Ext.define('paramManangerModel2', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'paravalue',
                type: 'string'
            }]
    });

    var defaultValueStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'paramManangerModel2',
        proxy: {
            type: 'ajax',
            url: 'getTaskParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    defaultValueStore.on('beforeload', function (store, options) {
        var new_params = {
            paraIid: piid
        };
        Ext.apply(defaultValueStore.proxy.extraParams, new_params);
    });
    Ext.define('parameterCheckModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'ruleName',
            type: 'string'
        }, {
            name: 'checkRule',
            type: 'string'
        }, {
            name: 'ruleDes',
            type: 'string'
        }]
    });
    var store = Ext.create('Ext.data.Store', {
        autoLoad: bhParameterCheckSwitch,
        model: 'parameterCheckModel',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterCheck.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: 'paraIid',
            dataIndex: 'paraIid',
            width: 40,
            hidden: true
        }, {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 50,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                if (value == 'IN-string(加密)') {
                    coun = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    coun = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>参数默认值：" + coun
                    + "<br>参数值：" + record.get('paramValue')
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 60,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                if (value == 'IN-string(加密)') {
                    coun = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    coun = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>参数默认值：" + coun
                    + "<br>参数值：" + record.get('paramValue')
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramDefaultValue',
            width: 80,
            text: '参数默认值',
            readonly: paramEditDisable,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                let showValue = value;

                let paramType = record.get('paramType');

                if (paramType == 'IN-string(加密)') {
                    //加密默认值解密后获取长度
                    var decodeVal = getSMEncode(value,0);
                    if(!(decodeVal == null || decodeVal == '')){
                        value = decodeVal;
                    }
                    let xing = "";
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramValue',
            width: 100,
            text: '参数值',
            maxLength: 1000,
            allowBlank: true,
            editor: {},
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                let showValue = value;

                let paramType = record.get('paramType');

                if (paramType == 'IN-string(加密)') {
                    //加密参数解密获取长度
                    var decodeVal = getSMEncode(value,0);
                    if(!(decodeVal == null || decodeVal == '')){
                        value = decodeVal;
                    }
                    let xing = "";
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                value = getSMEncode(value,0);
                return showValue;
            }
        },
        {
            dataIndex: 'ruleName',
            width: 120,
            text: '验证规则',
            hidden: !bhParameterCheckSwitch,
            editor: {
                xtype: 'combobox',
                store: store,
                queryMode: 'local',
                displayField: 'ruleName',
                valueField: 'ruleName',
                editable: false,
            }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            editor: {
                allowBlank: true,
                readOnly:true
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }
    ];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var paramGrid = Ext.create('Ext.grid.Panel', {
        //title: "参数",
        //width: '50%',
        region: 'center',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
//        height: 280,
//        margin: 10,
//        collapsible : true,
        store: paramStore,
        plugins: [cellEditing],
        margin: '0 10 5 10',
        border: true,
        columnLines: true,
        columns: paramColumns,
        listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {

                var columnIndex = e.column.dataIndex;
                // 点击的当前行数据
                var recordData = e.record.data;

                var paramType = recordData.paramType;           // 是否为枚举类型
                var piidValue = recordData.paraIid;   // 参数名称
                // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue" || columnIndex == "paramValue";
                //var columnBooParameterName = columnIndex == "parameterName";
                var columnBooparamDefaultValue = columnIndex == "paramDefaultValue";
                var columnBooparamValue = columnIndex == "paramValue";
                // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                    piid = piidValue;
                    defaultValueStore.load();
                }
                if (paramType == "枚举" && columnBooparamDefaultValue) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore,
                    });
                }
                if (paramType == "枚举" && columnIndex == "paramValue") {
                    piid = piidValue;
                    defaultValueStore.load();
                }
                if (paramType == "枚举" && columnBooparamValue) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore
                    });
                }
                // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                if (paramType != "枚举" && columnBoo) {
                    e.column.setEditor({
                        xtype: 'textfield',
                        readOnly: columnIndex == "parameterName" ? true : false,

                    })
                }

                if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {
                    let pass = new Ext.form.TextField({});

                    e.column.setEditor(pass)
                }
            }
        }
    });

    Ext.define('attaTempModelEdit', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }, {
                name: 'isTempFlag',//标记是否是临时附件表的标记  邮储使用
                type: 'string'
            }]
    });

    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: taskApplyUploadAttachmentSwitch,
        autoDestroy: true,
        pageSize: 100,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    //获取所有模板
    var attaTempStore = Ext.create('Ext.data.Store', {
        autoLoad: templateSwitch,
        autoDestroy: true,
        pageSize: 10,
        model: 'attaTempModelEdit',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttaTemplate.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    attachmentStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: attachmentIds,
            fromDescrip: scriptTaskApplyAttachmentParamsSwitch ? 'taskApplyExec' : ''//标记为来自任务申请，后台逻辑判断条件 开了邮储开关才传
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });

    attachmentStore.on('load', function (me, records, successful, eOpts) {
        attachmentIds = []
        $.each(records, function (index, record) {
            //判断删除的附件（脚本原有绑定的附件）数组的iid和store中的iid，移除已经删除的记录
            let temiid = record.get('iid');
            if (scriptTempAttachmentDeleteArray.contains(temiid)) {
                attachmentStore.remove(record);
            } else {
                if (scriptTaskApplyAttachmentParamsSwitch) {
                    if (record.get('isTempFlag') == '1') {
                        attachmentIds.push(record.get('iid'));
                    }
                } else {
                    attachmentIds.push(record.get('iid'));
                }
            }
        });
    });

    /********************/
    attaTempStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: tempmentIds
        };
        Ext.apply(attaTempStore.proxy.extraParams, new_params);
    });
    attaTempStore.on('load', function (me, records, successful, eOpts) {
        tempmentIds = [];
        $.each(records, function (index, record) {
            tempmentIds.push(record.get('iid'));
        });
    });

    /********************/

    function removeByValue(arr, val) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == val) {
                arr.splice(i, 1);
                break;
            }
        }
    }

    var attachmentColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '附件名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }, {
            text: '临时附件表标志',// 1是 0不是
            dataIndex: 'isTempFlag',
            hidden: true,
            width: 50
        },
        /*{
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    },*/
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attachmentStore.getAt(rowIndex);
                    Ext.Msg.confirm("请确认", "是否真的要删除附件？", function (button, text) {
                        if (button == "yes") {
                            //判断是否是临时表 并且开了 开关 邮储 开了的话 如果是脚本绑定的表那么只是前台删除
                            if (rec.get('isTempFlag') == '0' && scriptTaskApplyAttachmentParamsSwitch) {
                                scriptTempAttachmentDeleteArray.push(rec.get('iid'));
                                attachmentStore.remove(rec);
                            } else {
                                var a = [];
                                a.push(rec.get('iid'));
                                Ext.Ajax.request({
                                    url: 'deleteScriptAttachment.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                        iids: a,
                                        delTempFlag: delTempFlag
                                    },
                                    success: function (response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            removeByValue(attachmentIds, rec.get('iid'));
                                            attachmentStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "保存失败！");
                                    }
                                });
                            }
                        }
                    });
                }
            },
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attachmentStore.getAt(rowIndex);
                        let isTempFlag = rec.get('isTempFlag');
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttachment.do?iid=' + rec.get('iid') + '&isTempFlag=' + isTempFlag;
                    }
                }
            ]
        }];

    var tempColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '模板名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [/*{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function(grid, rowIndex, colIndex) {
                    var rec = attaTempStore.getAt(rowIndex);
                    var a = [];
                    a.push(rec.get('iid'));
                    Ext.Ajax.request({
                        url: 'deleteScriptAttaTemplate.do',
                        method: 'POST',
                        sync: true,
                        params: {
                            iids: a
                        },
                        success: function(response, request) {
                            var success = Ext.decode(response.responseText).success;
                            if (success) {
                                Ext.Msg.alert('提示', '删除成功！');
                                removeByValue(tempmentIds, rec.get('iid'));
                                attaTempStore.load();
                                if(newServiceId!=0){
                                    save(0);
                                }
                            } else {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "保存失败！");
                        }
                    });
                }
            },*/
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attaTempStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttaTemplate.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];

    var selectedAttachmentButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加附件',
            handler: selectAttachmentFun
        });

    var selectedTemplateButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加模板',
            handler: selectTempFun
        });

    var attachmentGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        //cls: 'attachments customize_panel_back',
        //height: '40%',
//        height: contentPanel.getHeight()*0.25-80,
        //title: '附件',
        store: attachmentStore,
        cls: 'window_border panel_space_top panel_space_left panel_space_right attachments customize_panel_back',
        border: true,
        columnLines: true,
        autoScroll: true,
        margin: '0 10 5 10',
//        emptyText: '没有附件',
        viewConfig: {
            emptyText: '没有附件',
            deferEmptyText: false
        },

        columns: attachmentColumns,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [
                '->', selectedAttachmentButton//添加附件按钮
            ]
        }]
    });

    var attachmentGrid2 = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: attaTempStore,
        border: true,
        columnLines: true,
        viewConfig: {
            enableTextSelection: true
        },
        columnLines: true,
        columns: tempColumns,
        margin: '0 10 5 10',
        emptyText: '没有附件'
    });

    var attachmentUploadWin = null;

    function selectAttachmentFun() {
        var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_idauditing',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash", "audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        for (var i = 0; i < files.length; i++) {
                            str += files[i].name;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_idauditing').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        attachmentUploadWin = Ext.create('Ext.window.Window', {
            title: '附件信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 190,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            },
            /*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout: 'fit'
        });

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        /** 自定义遮罩效果* */
        var myUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploadScriptAttachmentFile.do',
                    params: {scriptUuid: scriptuuid,attachmentIds:Ext.encode(attachmentIds)},
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            attachmentIds.push.apply(attachmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        attachmentUploadWin.close();
                        myUploadMask.hide();
                        attachmentStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        myUploadMask.hide();
                    }
                });
            }
        }

        attachmentUploadWin.show();
    }

    var tempUploadWin = null;

    function selectTempFun() {
        var uploadTempForm;
        uploadTempForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_idbasic',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash", "audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        for (var i = 0; i < files.length; i++) {
                            str += files[i].name;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_idbasic').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclTempData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        tempUploadWin = Ext.create('Ext.window.Window', {
            title: '模板信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 200,
            items: [uploadTempForm],
            listeners: {
                close: function (g, opt) {
                    uploadTempForm.destroy();
                }
            },
            /*
             * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
             */layout: 'fit'
        });

        function upExeclTempData() {
            var form = uploadTempForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplateSec(form);
        }

        /** 自定义遮罩效果* */
        var tempUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplateSec(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploadScriptAttaTemplate.do',
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            tempmentIds.push.apply(tempmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        tempUploadWin.close();
                        tempUploadMask.hide();
                        attaTempStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        tempUploadMask.hide();
                    }
                });
            }
        }

        tempUploadWin.show();
    }

    var pagetab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            region: 'center',
            activeTab: 0,
            height: contentPanel.getHeight() * 0.25,
            border: false,
            items: [
                {
                    title: '参数',
                    layout: 'fit',
                    items: [paramGrid]
                },
                {
                    title: '附件',
                    hidden: !taskApplyUploadAttachmentSwitch,
                    layout: 'fit',
                    items: [attachmentGrid]
                },
                {
                    title: '模板',
                    layout: 'fit',
                    hidden: !templateSwitch,
                    items: [attachmentGrid2]
                }
            ]
        });

    var paramsAndAttachmentsPanel = Ext.create('Ext.panel.Panel', {
        collapsible: false,
        border: false,
        region: 'center',
        cls: 'left_edge',
        height: contentPanel.getHeight() * 0.25,
        layout: 'border',
        items: [pagetab, execDescForm/*attachmentGrid*/]
    });

    /*var mainP = Ext.create('Ext.panel.Panel', {
        width: '33.7%',
        border: false,
        title: "脚本内容",
        // height : contentPanel.getHeight()-120,
        height: contentPanel.getHeight()-200,
        tbar:[FieldContainer],
        html: '<textarea id="codeEditView-for-auditing" value style="width: 100%;height:100%;"></textarea>'
    });*/


    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        }, {
            name: 'name',
            type: 'string'
        }, {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: !(removeAgentSwitch || agentFromChangeSys),
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    /*resourceGroupStore.on('load', function() {
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        });
		resourceGroupStore.insert(0,ins_rec);
	});  */
    var resourceGroupObj = Ext.create('Ext.form.field.ComboBox',
        {
            fieldLabel: '资源组',
            labelAlign: 'right',
            labelWidth: 70,
            width: '22.2%',
            //columnWidth:1,
            multiSelect: true,
            store: resourceGroupStore,
            hidden: removeAgentSwitch || agentFromChangeSys,
            displayField: 'name',
            valueField: 'id',
            triggerAction: 'all',
            editable: false,
            mode: 'local',
            listeners: {
                change: function (comb, newValue, oldValue, eOpts) {
                    agent_store.load();
                }
            }
        });

    var business = Ext.create('Ext.form.field.ComboBox', {
        labelWidth: 79,
        queryMode: 'local',
        fieldLabel: '业务系统',
        displayField: 'systemname',
        hidden: !fjnxCISwitch,
        valueField: 'systemid',
        editable: false,
        emptyText: '--请选择业务系统--',
        store: {
            fields: ["systemname","systemid"],
            autoLoad: fjnxCISwitch,
            proxy: {
                type: 'ajax',
                url: 'queryCICDBussINFO.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        },
        width: '23.6%',
        labelAlign: 'right'
    });

    var app_name = new Ext.form.TextField({
        name: 'appname',
        fieldLabel: '应用名称',
        displayField: 'appname',
        emptyText: '--请输入应用名称--',
        labelWidth: 58,
        hidden: true,
        labelAlign: 'right',
        width: '20%'
    });
    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'AgentIp',
        displayField: 'agentip',
        emptyText: '--请输入agentip--',
        labelWidth: 58,
        hidden: true,
        labelAlign: 'right',
        width: '23%'
    });
    var ipStart = Ext.create('Ext.form.TextField',
        {
            labelWidth: 79,
            fieldLabel: '起始IP',
            emptyText: '--请输入开始IP--',
            //labelSeparator : '',
            width: '23.2%',
            labelAlign: 'right'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
            // padding : '0 10 0 0'
        });
    /** 结束ip* */
    var ipEnd = Ext.create('Ext.form.TextField',
        {
            labelWidth: 70,
            fieldLabel: '终止IP',
            emptyText: '--请输入截止IP--',
            //labelSeparator : '',
            labelAlign: 'right',
            width: '23.2%'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
            // padding : '0 10 0 0'
        });
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '计算机名',
        displayField: 'hostname',
        emptyText: '--请输入计算机名--',
        llabelWidth: 70,
        labelAlign: 'right',
        width: '25.2%'
    });
    var sysName1 = new Ext.form.TextField({
        name: 'sysName1',
        fieldLabel: '名称',
        displayField: 'sysName1',
        emptyText: '--请输入名称--',
        labelWidth: 100,
        labelAlign: 'right',
        width: '26%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });
    var sys_name = new Ext.form.TextField({
        name: 'sysname',
        fieldLabel: '名称',
        displayField: 'sysname',
        emptyText: '--请输入名称--',
        hidden: cmdbFlag,
        labelWidth: 70,
        labelAlign: 'right',
        width: '23%'
    });
    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '操作系统',
        displayField: 'ostype',
        emptyText: '--操作系统--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '23.2%'
    });
    var agentStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "0", "name": "正常"},
            {"id": "1", "name": "异常"},
            {"id": "2", "name": "升级中"}
        ]
    });

    var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentStatus',
        labelWidth: 79,
        queryMode: 'local',
        fieldLabel: 'Agent状态',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择Agent状态--',
        store: agentStatusStore,
        width: '23.5%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });

    var centernameStore = Ext.create('Ext.data.JsonStore', {
        fields: ['centername'],
        autoDestroy: true,
        autoLoad: agentCmdbMessageSwitchpscb,
        proxy: {
            type: 'ajax',
            url: 'centerList.do',
            reader: {
                type: 'json',
                root: 'centerlist'
            }
        }
    });
    var centernameCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
        iname: 'centername',
        labelWidth: 79,
        fieldLabel: '所属中心',
        displayField: 'centername',
        editable: false,
        emptyText: '--请选择所属区域--',
        istore: centernameStore,
        hidden: !agentCmdbMessageSwitchpscb,
        width: '23.5%',
        labelAlign: 'right'
    });
    var selectAll = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '全选',
        inputValue: 1,
        width: 50,
        margin: '10 0 0 10',
        listeners: { //监听
            change: function (el, checked) {
                if (!checked) {
                    chosedAgentIds = [];
                    agent_grid.getSelectionModel().deselectAll();
                    selectAll.setValue(false);
                    agent_grid.ipage.moveFirst();
                    agent_grid_chosed.ipage.moveFirst();
                }
            }
        }
    });
    var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: function () {
            agent_grid.ipage.moveFirst();
        },
        bodyCls: 'x-docked-noborder-top',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [sys_name, app_name, host_name, os_type, ipStart, ipEnd
            ]
        },
            {
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [ /*agent_ip,*/sysName1, business, resourceGroupObj, agentStatusCb, centernameCombo, selectAll

                ]
            }, {
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '查询',
                        handler: function () {
                            agent_grid.ipage.moveFirst();
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            agent_ip.setValue('');
                            ipEnd.setValue('');
                            ipStart.setValue('');
                            app_name.setValue('');
                            sys_name.setValue('');
                            host_name.setValue('');
                            sysName1.setValue('');
                            os_type.setValue('');
                            resourceGroupObj.setValue('');
                            business.setValue('');
                            agentStatusCb.setValue('');
                            centernameCombo.setValue('');
                            pubDesc_sm.setValue('');
                            pubDesc_sm_ipsearch.setValue('');
                        }
                    }, {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '导入',
                        handler: importExcel
                    }, {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        hidden: batchQuerySwitch == 'false',
                        text: ipOrNameSwitch == 'true' ? 'IP批量查询' : '批量查询',
                        handler: function () {
                            //开关开启使用ip查询，并匹配设备ip，筛选出未找到的ip，否则走设备名查询
                            if (ipOrNameSwitch == 'true') {
                                batchQueryForIp();
                            } else {
                                batchQuery();
                            }
                        }
                    }
                ]
            }]
    });

    function checkFile(fileName) {
        var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
        if (!file_reg.test(fileName)) {
            Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
            //Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)');
            return false;
        }
        return true;
    }

    function importExcel() {
        //销毁win窗口
        if (!(null == upldWin || undefined == upldWin || '' == upldWin)) {
            upldWin.destroy();
            upldWin = null;
        }

        if (!(null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
            upLoadformPane.destroy();
            upLoadformPane = null;
        }
        //导入文件Panel
        upLoadformPane = Ext.create('Ext.form.Panel', {
            width: 370,
            height: 120,
            frame: true,
            items: [
                {
                    xtype: 'filefield',
                    name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
                    labelAlign: 'right',
                    fieldLabel: '选择文件',
                    labelWidth: 80,
                    msgTarget: 'side',
                    anchor: '100%',
                    buttonText: '浏览...',
                    width: 370
                }
            ],
            buttonAlign: 'left',
            buttons: ['->',
                {
                    id: 'upldBtnIdAudi',
                    text: '导入Agent文件',
                    handler: function () {
                        var form = this.up('form').getForm();
                        var upfile = form.findField("file").getValue();
                        if (upfile == '') {
                            Ext.Msg.alert('提示', "请选择文件...");
                            return;
                        }

                        var hdtmpFilNam = form.findField("file").getValue();
                        if (!checkFile(hdtmpFilNam)) {
                            form.findField("file").setRawValue('');
                            return;
                        }

                        if (form.isValid()) {
                            Ext.MessageBox.wait("数据处理中...", "进度条");
                            form.submit({
                                url: 'importAgentForStart.do',
                                params: {
                                    envType: 1
                                },
                                success: function (form, action) {
                                    var msg = Ext.decode(action.response.responseText).message;
                                    var status = Ext.decode(action.response.responseText).status;
                                    var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;

                                    if (status == 1) {
                                        if (matchAgentIds && matchAgentIds.length > 0) {
                                            Ext.MessageBox.buttonText.yes = "确定";
                                            Ext.MessageBox.buttonText.no = "取消";
                                            Ext.Msg.confirm("请确认", msg, function (id) {
                                                if (id == 'yes') {
                                                    Ext.Msg.alert('提示', "导入成功！");
                                                    agent_ip.setValue('');
                                                    app_name.setValue('');
                                                    sys_name.setValue('');
                                                    host_name.setValue('');
                                                    os_type.setValue('');
                                                    resourceGroupObj.setValue('');
                                                    business.setValue('');
                                                    agentStatusCb.setValue('');
                                                    chosedAgentIds = matchAgentIds;
                                                    agent_grid.ipage.moveFirst();
                                                }
                                            });
                                        } else {
                                            Ext.Msg.alert('提示-没有匹配项', msg);
                                        }

                                    } else if (status == 2) {
                                        Ext.Msg.alert('提示', msg);
                                    } else {
                                        var successDataHave = Ext.decode(action.response.responseText).successDataHave;
                                        if (successDataHave == 'false') {
                                            Ext.Msg.alert('提示', msg);
                                            return;
                                        }
                                        Ext.Msg.alert('提示', "导入成功！");
                                        agent_ip.setValue('');
                                        app_name.setValue('');
                                        sys_name.setValue('');
                                        host_name.setValue('');
                                        os_type.setValue('');
                                        resourceGroupObj.setValue('');
                                        business.setValue('');
                                        agentStatusCb.setValue('');
                                        chosedAgentIds = matchAgentIds;
                                        agent_grid.ipage.moveFirst();
                                        agent_store_chosed.load();
                                        chosedAgentWin.close();
                                    }

                                    upldWin.close();
                                    return;
                                },
                                failure: function (form, action) {
                                    secureFilterRsFrom(form, action);
                                }
                            });
                        }
                    }
                }, {
                    text: '下载模板',
                    handler: function () {
                        if(taskApplyForYCSwitch) {
                            window.location.href = 'downloadAgentTemplate.do?fileName=AgentImportMould_new.xls';
                        }else{
                            window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
                        }
                    }
                }
            ]
        });
        //导入窗口
        upldWin = Ext.create('Ext.window.Window', {
            title: '设备信息批量导入',
            width: 430,
            height: 200,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            items: [upLoadformPane]
        }).show();
        upldWin.on("beforeshow", function (self, eOpts) {
            var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
            form.reset();
        });

        upldWin.on("destroy", function (self, eOpts) {
            upLoadformPane.destroy();
        });
    }

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'centerName', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'execUserNmae', type: 'string'},
            {name: 'agentState', type: 'int'}
        ]
    });

    agent_grid_url = 'getAllAgentForIpSearch.do';
    agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'centerName', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'execUserNmae', type: 'string'},
            {name: 'agentState', type: 'int'}

        ]
    });
    var agent_storeAll = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListAll.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    //浦发agent展示样式特殊开关
    if (!taskApplyForSPDBSwitch) {//不开展示原有样式
        agent_store_chosed = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 30,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentChosedList.do',
                actionMethods: {
                    create: 'POST',
                    read: 'POST', // by default GET
                    update: 'POST',
                    destroy: 'POST'
                },
                reader: {
                    type: 'json',
                    root: 'dataList',
                    timeout: 600000,
                    totalProperty: 'total'
                }
            }
        });
    } else {//开了后展示白名单执行样式
        agent_store_chosed = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 30,
            model: 'page.dubbo.scriptService.spdb.agent.agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAllAgentListForSPDB.do',
                actionMethods: {
                    create: 'POST',
                    read: 'POST', // by default GET
                    update: 'POST',
                    destroy: 'POST'
                },
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
    }

    var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', width: 160},
        {text: 'IP', dataIndex: 'agentIp', width: 110},
        {text: '计算机名', dataIndex: 'hostName', width: 150},
        {text: '操作系统', dataIndex: 'osType', width: 110},
        {text: '端口号', dataIndex: 'agentPort', width: 60},
        {text: '所属中心', dataIndex: 'centerName', width: 65, hidden: !agentCmdbMessageSwitchpscb},
        {
            text: '描述', dataIndex: 'agentDesc', flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }
    ];


    var agent_columns_chosed = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', width: 160},
        {text: 'IP', dataIndex: 'agentIp', width: 110},
        {text: '计算机名', dataIndex: 'hostName', width: 150},
        {text: '操作系统', dataIndex: 'osType', width: 110},
        {text: '端口号', dataIndex: 'agentPort', width: 60},
        {
            text: '描述', dataIndex: 'agentDesc', flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '执行用户',
            dataIndex: 'execuser',
            width: 90,
            align: 'left',//整体左对齐
            renderer: function (value, metaData, record, rowNum) {
                var displayValue = value;
                var recordedData = $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + record.get("iid"));
                if (undefined == recordedData) {
                    if ("" == value || undefined == value) {
                        displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                    } else {
                        displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + displayValue + "</a>";
                    }
                } else {
                    if ("" == recordedData) {
                        displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                    } else {
                        displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + recordedData + "</a>";
                    }
                }
                return displayValue;
            },
            listeners: {
                click: function (a, b, c, d, e, record) {
                    openExecUserConfigData(record);
                }
            }
        },
        {
            text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }
    ];

    agent_store.on('beforeload', function (store, options) {
        new_params_agent = {
            agentIp: agent_ip.getValue(),
            startIp: ipStart.getValue().trim(),
            agentState: agentStatusCb.getValue(),
            centerName: centernameCombo.getValue(),
            endIp: ipEnd.getValue().trim(),
            appName: app_name.getValue(),
            sysName: CMDBflag ? (sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + "")) : Ext.util.Format.trim(sysName1.getValue()),
            hostName: host_name.getValue(),
            osType: os_type.getValue(),
            rgIds: resourceGroupObj.getValue(),
            business: business.getValue(),
            flag: 1,
            uuid: searchUUID,
            batchComputerName: ipOrNameSwitch == 'true' ? pubDesc_sm_ipsearch.getValue() : pubDesc_sm.getValue(),
            heightPermissionFlag: heightPermissionFlag
        };

        Ext.apply(agent_store.proxy.extraParams, new_params_agent);
    });

    agent_store_chosed.on('beforeload', function (store, options) {
        var new_params;
        if (taskApplyForSPDBSwitch) {
            new_params = {
                agentIds: JSON.stringify(chosedAgentIds),
                flag: 1,
                from: 'inAgentChosedList'
            };
        } else {
            new_params = {
                agentIds: JSON.stringify(chosedAgentIds),
                flag: 1
            };
        }
        Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    agent_store.on('load', function (store, options) {
        var records = [];//存放选中记录
        for (var i = 0; i < agent_store.getCount(); i++) {
            var record = agent_store.getAt(i);
            for (var ii = 0; ii < chosedAgentIds.length; ii++) {

                if ((+chosedAgentIds[ii]) == record.data.iid) {
                    records.push(record);
                }
            }
        }
        agent_grid.getSelectionModel().select(records, false, true);//选中记录
    });
//    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		store : agent_store,
//		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//		dock : 'bottom',
//		displayInfo : true
//	});
    agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: agent_store,
        border: false,
//	    bbar : pageBar,
        columnLines: true,
//	    rowLines: false,
        columns: agent_columns,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
        listeners: {
            select: function (e, record, index, eOpts) {
                if (chosedAgentIds.indexOf(record.get('iid')) == -1) {
                    chosedAgentIds.push(record.get('iid'));
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (chosedAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedAgentIds.remove(record.get('iid'));
                }
            }
        }
    });
//    var pageBarForAgentChosedGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//    	store : agent_store_chosed,
//    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//    	dock : 'bottom',
//    	displayInfo : true
//    });
    var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true

    });


    //*********************************************增加资源组相关控件 start***************************************************************************

    Ext.define('groupModel', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'name', type: 'string'},
            {name: 'execUserName', type: 'string'},
            {name: 'description', type: 'string'}
        ]
    });
    var group_columns = [
        {text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'id', hidden: true},
        {text: '组名称', dataIndex: 'name', width: 160},
        {text: '启动用户', dataIndex: 'execUserName', width: 160},
        {text: '描述', dataIndex: 'description', width: 280},
        {
            text: '操作',
            xtype: 'actiontextcolumn',
            flex: 1,
            align: 'left',
            items: [{
                text: '详情',
                iconCls: 'execute',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.id;
                    getAgentInfoByGroupId(iid);
                }
            }]
        }];

    /**
     * 获取资源组下的agent
     */
    function getAgentInfoByGroupId(iid) {
        Ext.define('agentModelByGroup', {
            extend: 'Ext.data.Model',
            idProperty: 'id',
            fields: [
                {name: 'id', type: 'long'},
                {name: 'ip', type: 'string'},
                {name: 'port', type: 'string'},
                {name: 'hostName', type: 'string'}
            ]
        });

        var agentinfo_group_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 50,
            model: 'agentModelByGroup',
            proxy: {
                type: 'ajax',
                url: 'agentGroup/getServersForTaskApply.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        agentinfo_group_store.on('beforeload', function (store, options) {
            var new_params = {
                groupId: iid
            };

            Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
        });
        var agentinfo_columns_group = [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: 'Agent名称', dataIndex: 'hostName', flex: 1},
            {text: 'IP', dataIndex: 'ip', width: 160},
            {text: '端口', dataIndex: 'port', width: 160}];


        var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store: agentinfo_group_store,
            border: false,
            columnLines: true,
            cls: 'customize_panel_back',
            columns: agentinfo_columns_group,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        });
        agentinfo_group_store.load();
        var agentinfoGroupWin = Ext.create('Ext.window.Window', {
            title: '增加资源组',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'hide',
            layout: 'border',
            width: contentPanel.getWidth() - 190,
            height: contentPanel.getHeight(),
            items: [agentinfo_group_grid]
        });
        agentinfoGroupWin.show();
    }


    var group_columns_chosed = [
        {text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'id', hidden: true},
        {text: '组名称', dataIndex: 'name', width: 160},
        {text: '启动用户', dataIndex: 'execUserName', width: 160},
        {text: '描述', dataIndex: 'description', flex: 1}];
    var group_store_chosed = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 30,
        model: 'groupModel',
        proxy: {
            type: 'ajax',
            url: 'agentGroup/groups.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var group_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'groupModel',
        proxy: {
            type: 'ajax',
            url: 'agentGroup/groups.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    group_store_chosed.on('beforeload', function (store, options) {
        var new_params = {
            groupIids: chosedGroupIds,
            from: "taskApplayChosed"
        }
        Ext.apply(group_store_chosed.proxy.extraParams, new_params);
    });

    group_store.on('beforeload', function (store, options) {
        var new_params = {
            agentGroupName: iresName.getValue()
//		    	iexecUser:iexecUser.getValue()
        };
        Ext.apply(group_store.proxy.extraParams, new_params);
    });
    group_store.on('load', function (store, options) {
        var records = [];//存放选中记录
        for (var i = 0; i < group_store.getCount(); i++) {
            var record = group_store.getAt(i);
            for (var ii = 0; ii < chosedGroupIds.length; ii++) {
                if (+chosedGroupIds[ii] == record.data.id) {
                    records.push(record);
                }
            }
        }
        group_grid.getSelectionModel().select(records, false, true); //选中记录
    });


    var group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: group_store,
        border: false,
        columnLines: true,
        cls: 'customize_panel_back',
        columns: group_columns,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
        listeners: {
            select: function (e, record, index, eOpts) {
                if (chosedGroupIds.indexOf(record.get('id')) == -1) {
                    chosedGroupIds.push(record.get('id'));
                    chosedGroupNames.push(record.get('name'));
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (chosedGroupIds.indexOf(record.get('id')) > -1) {
                    chosedGroupIds.remove(record.get('id'));
                    chosedGroupNames.remove(record.get('name'));
                }
            }
        }
    });

    var iresName = new Ext.form.TextField({
        name: 'iresName',
        fieldLabel: '组名称',
        displayField: 'iresName',
        emptyText: '--请输入组名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%'
    });
    var iexecUser = new Ext.form.TextField({
        name: 'iexecUser',
        fieldLabel: '启动用户',
        displayField: 'iexecUser',
        emptyText: '--启动用户--',
        labelWidth: 70,
        hidden: true,
        labelAlign: 'right',
        width: '25%'
    });

    var search_group_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: function () {
            group_grid.ipage.moveFirst();
        },
        bodyCls: 'x-docked-noborder-top',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [iresName, iexecUser,
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        group_grid.ipage.moveFirst();
                    }
                },
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '清空',
                    handler: function () {
                        iresName.setValue('');
                        iexecUser.setValue('');
                    }
                }]
        }]
    });


    var group_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
        title: '已选资源组',
        region: 'west',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: group_store_chosed,
        border: true,
        width: '60%',
        columnLines: true,
        height: contentPanel.getHeight() * 0.68,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        emptyText: '没有选择资源组',
        columns: group_columns_chosed,
        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
        listeners: {
            activate: function (tab) {
                resGroupFlag = 'true';
                group_store_chosed.load();
                for (var y = 0, yLen = chosedAgentIds.length; y < yLen; y++) {
                    $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + chosedAgentIds[y], "");
                }
                chosedAgentIds.splice(0, chosedAgentIds.length);
            }
        },
        dockedItems: [
            {
                xtype: 'toolbar',
                dock: 'top',
                items: [
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '删除',
                        handler: function () {
                            var records = group_grid_chosed.getSelectionModel().getSelection();
                            if (records.length > 0) {
                                for (var i = 0, len = records.length; i < len; i++) {
                                    chosedGroupIds.remove(records[i].get('id'));
                                    chosedGroupNames.remove(records[i].get('name'));
                                }
                                group_grid_chosed.ipage.moveFirst();
                                group_grid.ipage.moveFirst();
                            } else {
                                Ext.Msg.alert('提示', '请选择资源组！')
                                return
                            }
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '增加资源组',
                        handler: function () {
                            if (!chosedGroupWin) {
                                chosedGroupWin = Ext.create('Ext.window.Window', {
                                    title: '增加资源组',
                                    autoScroll: true,
                                    modal: true,
                                    resizable: false,
                                    closeAction: 'hide',
                                    layout: 'border',
                                    width: contentPanel.getWidth() - 190,
                                    height: contentPanel.getHeight(),
                                    items: [search_group_form, group_grid],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'bottom',
                                            layout: {pack: 'center'},
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    text: '确定',
                                                    cls: 'Common_Btn',
                                                    margin: '6',
                                                    handler: function () {
                                                        group_store_chosed.load();
                                                        this.up('window').close();
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    text: '关闭',
                                                    cls: 'Common_Btn',
                                                    margin: '6',
                                                    handler: function () {
                                                        this.up('window').close();
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                });
                            }
                            var optTypeValue = listComBox2.getValue();
                            if (
                                (optTypeValue == '变更' ||
                                    optTypeValue == '维护' ||
                                    optTypeValue == '0' ||
                                    optTypeValue == '2') &&
                                butterflyVerison2.getValue() == ''
                            ) {
                                Ext.Msg.show({
                                    title: '提示',
                                    msg: '变更、维护操作类型请输入单号才允许增加资源组!',
                                    buttons: Ext.Msg.OK,
                                    icon: Ext.Msg.INFO
                                })
                                return;
                            }
                            chosedGroupWin.show();
                            group_store.load();
                        }
                    }
                ]
            }
        ]
    });


    //*********************************************增加资源组相关控件 end***************************************************************************
    var sureButton = Ext.create ("Ext.Button",
        {
            cls : 'Common_Btn',
            text : '确定',
            handler :function () {
                var isSelectAll = selectAll.getValue();
                var agent_grid_chosedRecord = agent_grid_chosed.getStore().getRange();
                if (isSelectAll) {
                    agent_storeAll.load({
                        params: {
                            agentIp: agent_ip.getValue(),
                            startIp: ipStart.getValue().trim(),
                            agentState: agentStatusCb.getValue(),
                            centerName: centernameCombo.getValue(),
                            endIp: ipEnd.getValue().trim(),
                            appName: app_name.getValue(),
                            sysName: CMDBflag ? (sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + "")) : Ext.util.Format.trim(sysName1.getValue()),
                            hostName: host_name.getValue(),
                            osType: os_type.getValue(),
                            rgIds: resourceGroupObj.getValue(),
                            business: business.getValue(),
                            uuid: searchUUID
                        },
                        callback: function (r, options, success) {
                            agent_storeAll.each(function (record) {
                                chosedAgentIds.push(record.get('iid'));
                            });
                            if (agent_grid_chosedRecord.length > 0) {
                                for (var x = 0, xLen = agent_grid_chosedRecord.length; x < xLen; x++) {
                                    var flag = false;
                                    var chosedRecordIid = agent_grid_chosedRecord[x].get('iid');
                                    for (var y = 0, yLen = chosedAgentIds.length; y < yLen; y++) {
                                        var chosedNewIid = chosedAgentIds[y];
                                        if (chosedRecordIid == chosedNewIid) {
                                            flag = true;
                                            break;
                                        }
                                    }
                                    if (!flag) {
                                        $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + chosedRecordIid, "");
                                    }
                                }
                            }
                            agent_store_chosed.load();
                            chosedAgentWin.close();
                        }
                    });
                } else {
                    if (agent_grid_chosedRecord.length > 0) {
                        for (var x = 0, xLen = agent_grid_chosedRecord.length; x < xLen; x++) {
                            var flag = false;
                            var chosedRecordIid = agent_grid_chosedRecord[x].get('iid');
                            for (var y = 0, yLen = chosedAgentIds.length; y < yLen; y++) {
                                var chosedNewIid = chosedAgentIds[y];
                                if (chosedRecordIid == chosedNewIid) {
                                    flag = true;
                                    break;
                                }
                            }
                            if (!flag) {
                                $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + chosedRecordIid, "");
                            }
                        }
                    }
                    agent_store_chosed.load();
                    this.up("window").close();
                }
                sureButton.setDisabled(false);
            },
            listeners : {
                click : function() {
                    sureButton.setDisabled(true);
                }
            }
        });

    var agent_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
        title: '已选服务器',
        region: 'west',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: agent_store_chosed,
        border: true,
        width: '60%',
        columnLines: true,
        height: contentPanel.getHeight() * 0.68,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        emptyText: '没有选择服务器',
        //浦发特殊需求  agent选择特殊展示样式 判断
        columns: taskApplyForSPDBSwitch == true ? agentColumnsForSPDB : agent_columns_chosed,
        selModel: selModelForagent_grid_chosed,
        listeners: {
            activate: function (tab) {
                resGroupFlag = 'false';
                agent_store_chosed.load();
                chosedGroupIds.splice(0, chosedGroupIds.length);
                chosedGroupNames.splice(0, chosedGroupNames.length);
            }
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '删除',
                handler: function () {
                    var records = agent_grid_chosed.getSelectionModel().getSelection();
                    if (records.length > 0) {
                        for (var i = 0, len = records.length; i < len; i++) {
                            chosedAgentIds.remove(records[i].get('iid'));
                            if (taskApplyForSPDBSwitch) {
                                chosedCpIds.remove(records[i].get('cpid'));
                            }
                            $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + records[i].get('iid'), "");
                        }
                        if (chosedAgentIds.length == 0 && chosedAgentFlagArray.indexOf('WIN') != -1) {
                            chosedAgentFlagArray.remove('WIN');
                        } else if (chosedAgentIds.length == 0 && chosedAgentFlagArray.indexOf('非WIN') != -1) {
                            chosedAgentFlagArray.remove('非WIN');
                        }
                        agent_grid_chosed.ipage.moveFirst();
                        agent_grid.ipage.moveFirst();
                    } else {
                        Ext.Msg.alert('提示', "请选择服务器！");
                        return;
                    }
                }
            },
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '增加服务器',
                    handler: function () {
                        //浦发银行 选择agent同白名单执行一致的特殊需求，通过开关控制
                        if (!chosedAgentWin && !taskApplyForSPDBSwitch) {
                            chosedAgentWin = Ext.create('Ext.window.Window', {
                                title: '增加服务器',
                                autoScroll: true,
                                modal: true,
                                resizable: false,
                                closeAction: 'hide',
                                layout: 'border',
                                width: contentPanel.getWidth() - 190,
                                height: contentPanel.getHeight(),
                                items: [search_ip_form, agent_grid],
                                dockedItems: [{
                                    xtype: 'toolbar',
                                    dock: 'bottom',
                                    layout: {pack: 'center'},
                                    items: [sureButton, {
                                        xtype: "button",
                                        text: "关闭",
                                        cls: 'Common_Btn',
                                        margin: '6',
                                        handler: function () {
                                            this.up("window").close();
                                        }
                                    }]
                                }]
                            });
                        } else if (!chosedAgentWin && taskApplyForSPDBSwitch) {
                            chosedAgentWin = chosedAgentWinForSPDB;
                        }
                        var optTypeValue = listComBox2.getValue();
                        if ((optTypeValue == '变更' || optTypeValue == '维护' || optTypeValue == '0' || optTypeValue == '2') && butterflyVerison2.getValue() == '') {
                            Ext.Msg.show({
                                title: '提示',
                                msg: '变更、维护操作类型请输入单号才允许增加服务器!',
                                buttons: Ext.Msg.OK,
                                icon: Ext.Msg.INFO
                            });
                            return;
                        }
                        if (agentListStore != undefined) {
                            agentListStore.load();
                        }
                        chosedAgentWin.show();
                        if(!enableAutoLoadSwitch){
                            agent_store.load();
                        }
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '历史记录',
                    hidden: !logAnalizeSwitch,
                    handler: function () {
                        var historyWin = Ext.create('Ext.window.Window', {
                            title: '历史记录',
                            modal: true,
                            closeAction: 'destroy',
                            constrain: true,
                            autoScroll: true,
                            width: contentPanel.getWidth(),
                            height: contentPanel.getHeight(),
                            draggable: true,// 禁止拖动
                            resizable: true,// 禁止缩放
                            layout: 'fit',
                            loader: {
                                url: 'forwardTaskApplayHistoryWindow.do',
                                params: {
                                    serviceiid: iidForTaskAudi,
                                    scriptUUID: scriptuuid,
                                    agentIds: chosedAgentIds.length == 0 ? null : chosedAgentIds
                                },
                                autoLoad: true,
                                scripts: true
                            }
                        });
                        historyWin.show();
                    }
                }]
        }]
    });

    /** 已选窗口 资源组、单独agent tabPanel* */
    var tabPanelForChosedDevice = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
//	    region : 'center',
            region: 'west',
            activeTab: 0,
//	    cls:'customize_panel_back',
            cls: 'window_border  panel_space_right',
//	    width : '100%',
            width: '60%',
            height: contentPanel.getHeight() * 0.68,
//	    height : contentPanel.getHeight (),
            border: false,
            defaults:
                {
                    autoScroll: false
                },
            items: [agent_grid_chosed, group_grid_chosed]

        });
//    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
//    	title: "选择服务器",
//        border: true,
//        layout : 'border',
//        height: 300,
//        items: [search_ip_form, agent_grid]
//    });

    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }, {
            name: 'phoneNum',
            type: 'string'
        }]
    });

    var auditorStore_tap = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getExecAuditorList.do?scriptLevel=' + scriptLevelForTaskAudi,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    auditorStore_tap.on('beforeload', function (store, options) {
        var new_params = {
            heightPermissionFlag: heightPermissionFlag,
            scriptId: iidForTaskAudi
        };
        Ext.apply(auditorStore_tap.proxy.extraParams, new_params);
    });

    var groupLeader;
    Ext.Ajax.request({
        url: 'getGroupLeader.do',
        method: 'POST',
        async: false,
        success: function (response, options) {
            groupLeader = Ext.decode(response.responseText).leader;
        },
        failure: function (result, request) {
        }
    });
    var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
        editable: true,
        fieldLabel: "审核人",
        store: auditorStore_tap,
        // id: 'auditorComBox_tap',
        queryMode: 'local',
        width: "23.2%",
        displayField: 'fullName',
        valueField: 'loginName',
//	    hidden:scriptLevelForTaskAudi==0?true:false,
        hidden: scriptOddNumberSwitch ? true : false,
        labelWidth: 65,
        labelAlign: 'right',
        listeners: { //监听
            render: function (combo) {//渲染
                if (scriptLevelForTaskAudi == 0) {
                    combo.setValue(groupLeader);
                } else {
                    combo.getStore().on("load", function (s, r, o) {
                        if (firstUserSelected) {
                            combo.setValue(r[0].get('loginName'));//第一个值
                            phoneNum = r[0].get('phoneNum');
                            validCode.setValue(phoneNum);
                        }
                    });
                }
            },
            select: function (combo, records, eOpts) {
                var fullName = records[0].raw.fullName;
                combo.setRawValue(fullName);
                phoneNum = records[0].raw.phoneNum;
                validCode.setValue(phoneNum);
            },
            blur: function (combo, records, eOpts) {
//				var displayField =auditorComBox_tap.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_tap.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_tap.setValue("");
//					 	return;
//					}
//				}
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var execStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getExecUserList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var execComBox_tap = Ext.create('Ext.form.ComboBox', {
        fieldLabel: "执行人",
        store: execStore,
        queryMode: 'local',
        width: "15.3%",
        padding: '0 2 0 8',
        displayField: 'fullName',
        valueField: 'loginName',
        labelWidth: 58,
        editable: true,
        hidden: !execUserSwitch || scriptLevelForTaskAudi == 0 ? true : false,
        labelAlign: 'right'
    });
    execStore.load({
        callback: function (records, operation, success) {
            execComBox_tap.setValue(loginUser);
        }
    });

    var immediatelyExecRadio = Ext.create('Ext.form.field.Radio', {
        width: 80,
        name: 'execRadio',
        labelAlign: 'left',
        fieldLabel: '',
        boxLabel: '触发执行',
        checked: true,
        inputValue: 0
    });
    var timingExecRadio = Ext.create('Ext.form.field.Radio', {
        width: 80,
        name: 'execRadio',
        labelAlign: 'left',
        fieldLabel: '',
        boxLabel: '定时执行',
        inputValue: 2
    });
    var cycleExecRadio = Ext.create('Ext.form.field.Radio', {
        width: 80,
        name: 'execRadio',
        labelAlign: 'left',
        fieldLabel: '',
        boxLabel: '周期执行',
        inputValue: 1
    });

    var waitRadio = Ext.create('Ext.form.field.Radio', {
        width: 80,
        name: 'execRadio',
        labelAlign: 'left',
        fieldLabel: '',
        boxLabel: '候补执行',
        inputValue: 3
    });

    var execTimeComponent = Ext.create('Go.form.field.DateTime',
        {
            fieldLabel: '执行时间:',
            labelAlign: 'left',
            labelWidth: 60,
            width: 250,
            hidden: true,
            format: 'Y-m-d H:i:s'
        });
    var execModeGroup = Ext.create('Ext.form.RadioGroup', {
        name: 'cycleModeGroup',
        labelAlign: 'left',
        layout: 'column',
        width: '160',
        items: [immediatelyExecRadio, timingExecRadio, cycleExecRadio/*,waitRadio*/],
        listeners: {
            //通过change触发
            change: function (g, newValue, oldValue) {
                if (newValue.execRadio == 0)//触发
                {
                    execTimeComponent.hide();
                    cycleExecCronPanel.hide();
                } else if (newValue.execRadio == 2)//定时
                {
                    execTimeComponent.show();
                    cycleExecCronPanel.hide();
                } else if (newValue.execRadio == 1)//周期
                {
                    execTimeComponent.hide();
                    cycleExecCronPanel.show();
                }else if (newValue.execRadio == 3)//候补（回一个agent拉起一个agent）
                {
                    execTimeComponent.hide();
                    cycleExecCronPanel.hide();
                }
            }
        }
    });
//    var isTimerTask = Ext.create('Ext.form.field.Checkbox', {
//    	    checked : false,
//        	boxLabel: '定时任务',
//    		//margin:'3 5 0 5',
//    		name      : 'isDelay',
//	        id : 'isDelay',
//	        labelAlign : 'right',
//    		listeners: { //监听
//				       change:function(el,checked){
//				             if(checked){
//				             	cycleExecCronText.show();
//				             	selectCronButton.show();
//				             }else{
//				             	cycleExecCronText.hide();
//				             	selectCronButton.hide();
//				             }
//				        }
//		    }
//        });

    /** 选择生成周期表达式按钮 **/
    var selectCronButton = Ext.create("Ext.Button",
        {
            id: 'selectCronButton_id',
            cls: 'Common_Btn',
            text: "选择",
            handler: selectExecCron
        });

    function selectExecCron() {

        if (creatCronWin == undefined || !creatCronWin.isVisible()) {
            creatCronWin = Ext.create('Ext.window.Window', {
                title: '定时任务参数设置',
                modal: true,
                id: 'creatCronWin',
                closeAction: 'destroy',
                constrain: true,
                autoScroll: true,
                upperWin: creatCronWin,
                width: contentPanel.getWidth() - 350,
                height: contentPanel.getHeight() - 30,
                draggable: false,// 禁止拖动
                resizable: false,// 禁止缩放
                layout: 'fit',
                loader: {
                    url: 'cronMainForSpdb.do',
                    //					params : {
                    //						sysType : sysType,
                    //						state : state,
                    //						errorTaskId : errorTaskId,
                    //						pageType : pageType
                    //					},
                    autoLoad: true,
                    autoDestroy: true,
                    scripts: true
                }
            });
        }
        creatCronWin.show();
    }

    var cycleExecCronText = new Ext.form.TextField(
        {
            fieldLabel: '执行周期',
            labelWidth: 65,
            labelAlign: 'right',
            id: 'cycleExecCronText',
            name: 'cycleExecCronText',
            readOnly: true
        });

    var execUser = new Ext.form.TextField({
        name: 'execUser',
        fieldLabel: '启动用户',
        emptyText: '',
        labelWidth: 65,
        value: suUser,
        labelAlign: 'right',
        width: "17%",
        listeners: {
            change: function(t, newValue ,oldValue) {
                if (newValue !== oldValue) {
                    startUserValue = '';
                }
            }
        }
        /* listeners:{
		            blur: function(object,state,opt){
		            	if(taskApplyCheckSysAdminSwitch){
	                		var checkSysAdminMes = checkSysAdmin(object.getValue());
		                	if(checkSysAdminMes!=null && checkSysAdminMes !=""){
		                		execUser.setValue('');
		                		Ext.Msg.alert('提示', checkSysAdminMes);
							 	return;
		                	}
	                	}
		            }
         }*/
    });

    function CurentTime() {
        var now = new Date();

        var year = now.getFullYear();       //年
        var month = now.getMonth() + 1;     //月
        var day = now.getDate();            //日

        var hh = now.getHours();            //时
        var mm = now.getMinutes();          //分
        var ss = now.getSeconds();           //秒

        var clock = year;

        if (month < 10) {

            clock += "0";
        } else {
            clock += "";
        }

        clock += month;

        if (day < 10) {
            clock += "0";
        } else {
            clock += "";
        }


        clock += day;

        if (hh < 10) {
            clock += "0";
        } else {
            clock += "";
        }


        clock += hh;
        if (mm < 10) {
            clock += '0';
        } else {
            clock += "";
        }
        clock += mm;

        if (ss < 10) {
            clock += '0';
        } else {
            clock += "";
        }
        clock += ss;
        return (clock);
    }

    var taskName = new Ext.form.TextField({
        name: 'taskName',
        fieldLabel: '任务名称',
        emptyText: '',
        labelWidth: 65,
        labelAlign: 'right',
        width: "24.1%",
        value: serviceNameForTaskAudi + '_' + CurentTime()
    });
    var butterflyVerison = new Ext.form.TextField({
        name: 'butterflyversion',
        id: 'butterflyVerison',
        fieldLabel: '单号',
        padding: '3 2 0 8',
        hidden: (scriptLevelForTaskAudi == 0 || !scriptOddNumberSwitch) ? true : false,
        emptyText: '',
        labelWidth: 65,
        labelAlign: 'right',
        width: "17%"
    });


    var checkVersion = new Ext.form.Checkbox({
        id: "checkVersion",
        name: "checkVersion",
        boxLabel: "单号补添:",
        hidden: (scriptLevelForTaskAudi == 0 || !scriptOddNumberSwitch) ? true : false,
        boxLabelAlign: "before",
        labelWidth: 65,
        padding: '0 2 0 10'
    });
    var listStore = Ext.create("Ext.data.Store", {
        fields: ["listName", "listValue"],
        data: [
            {listName: "变更", listValue: 0},
//	        { listName: "重大变更", listValue: 1 },
            {listName: "维护", listValue: 2},
            {listName: "后补", listValue: 3}
        ]
    });
//	var listComBox = Ext.create ('Ext.form.ComboBox',
//	{
//	    editable : false,
//	    fieldLabel : "单号",
//	    labelWidth : 65,
//	    //hidden:hmmedStart,
//	    hidden:(scriptLevelForTaskAudi==0 || !scriptOddNumberSwitch)?true:false,
//	    store : listStore,
//	    value : '后补',
//	    queryMode : 'local',
//	    width : 150,
//	    displayField : 'listName',
//	    valueField : 'listValue'
//	});

    listComBox2 = Ext.create('Ext.form.ComboBox',
        {
            editable: false,
            fieldLabel: "操作类型",
            labelWidth: 65,
            hidden: (scriptLevelForTaskAudi == 0 || !scriptOddNumberSwitch) ? true : false,
            store: listStore,
            value: scriptOddNumberSwitch ? '变更' : '后补',
//			    value : '变更',
            queryMode: 'local',
            listeners: {
                //通过change触发
                change: function () {
                    if (listComBox2.getRawValue() == '后补') {
                        Ext.getCmp('butterflyversion2').hide();
                        auditorComBox_tap.displayField = 'fullName',
                            auditorComBox_tap.valueField = 'loginName',
                            auditorComBox_tap.show();
                        butterflyVerison2.hide();
                    } else if (listComBox2.getRawValue() == '变更') {
                        Ext.getCmp('butterflyversion2').show();
                        auditorComBox_tap.hide();
                        butterflyVerison2.show();
                    }
//		            	else if(listComBox2.getRawValue() == '重大变更')
//		            	{
//		            		Ext.getCmp('butterflyversion2').show();
//		            		Ext.getCmp('auditorComBox_tap').hide();
//		            		butterflyVerison2.show();
//		            	}
                    else if (listComBox2.getRawValue() == '维护') {
                        Ext.getCmp('butterflyversion2').show();
                        auditorComBox_tap.hide();
                        butterflyVerison2.show();
                    }
                }
            },
            width: 160,
            displayField: 'listName',
            valueField: 'listValue'
        });

    butterflyVerison2 = new Ext.form.TextField({
        name: 'butterflyversion2',
        id: 'butterflyversion2',
        fieldLabel: '单号',
        padding: '3 2 0 8',
        hidden: !(scriptLevelForTaskAudi == 0 || !scriptOddNumberSwitch || listComBox2.value == '后补' ) || itsmOrderNumberSwitch ? false : true,
        emptyText: '',
        labelWidth: 65,
        labelAlign: 'right',
        width: "17%"
    });

    addbutterflyVerison2 = new Ext.form.TextField({
        name: 'addbutterflyVerison2',
        id: 'addbutterflyVerison2',
        fieldLabel: '单号',
        padding: '3 2 0 8',
        hidden: true,
        emptyText: '',
        labelWidth: 65,
        labelAlign: 'right',
        width: "17%"
    });

    var eachNum = new Ext.form.NumberField({
        name: 'eachNum',
        fieldLabel: '并发数量',
        labelWidth: 65,
        labelAlign: 'right',
        minValue: 0,
        value: number,
        width: "12.6%"
    });
    var timeout = new Ext.form.NumberField({
        name: 'timeout',
        fieldLabel: '超时时间',
        labelWidth: 65,
        labelAlign: 'right',
        minValue: -1,
        hidden: !scriptTaskApplyShowTimeoutSwitch,
        value: timeoutForTaskAudi,
        width: "10%"
    });
    var shutdownCheckboxObj = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '关机维护',
        inputValue: 1,
        hidden: true,
//        hidden:(!scriptShutdownCheckboxSwitch)?true:false,
        width: 80,
        margin: '3 0 0 30'
    });
    var isSaveTemplateCk = new Ext.form.Checkbox({
        checked: false,
        boxLabel: '是否保存为常用任务',
        hidden: true,
        padding: '0 5 0 5'
    });
    var validCode = Ext.create('Ext.form.TextField',
        {
            fieldLabel: '手机号',
            labelWidth: 65,
            columnWidth: 150,
            id: 'myvalidCode',
            labelAlign: 'right',
            readOnly: true,
            hidden: (scriptLevelForTaskAudi == 0 || !audiCodeSwitch || smsSwitchNum == '0') ? true : false,
            regex: /^[1][3,4,5,7,8][0-9]{9}$/,
            xtype: 'textfield'
        });

    var authCode = Ext.create('Ext.form.TextField',
        {
            fieldLabel: '验证码',
            labelWidth: 65,
            // id: 'mauthCode',
            hidden: (scriptLevelForTaskAudi != 0 && audiCodeSwitch && smsSwitchNum != '0') ? false : true,
            labelAlign: 'right',
            width: 180,
            emptyText: '--请填写验证码--',
            regex: /^\d{6}$/,
            xtype: 'textfield'
        });

    var getValidCodeButton = Ext.create('Ext.Button',
        {
            columnWidth: .23,
            height: 33,
            name: 'getValidCodeButton',
            cls: 'Common_Btn',
            hidden: (scriptLevelForTaskAudi != 0 && audiCodeSwitch && smsSwitchNum != '0') ? false : true,
            id: 'getValidCodeButton',
            text: '获取验证码',
            handler: getValidCode
        });

    function isPoneAvailable(pone) {
        var myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
        if (!myreg.test(pone)) {
            return false;
        } else {
            return true;
        }
    }

    var countdown = 10;

    function settime() {
        if (countdown == 0) {
            Ext.getCmp('getValidCodeButton').setDisabled(false);
            countdown = 10;
            return false;
        } else {
            Ext.getCmp('getValidCodeButton').setDisabled(true);
            countdown--;
        }
        //显示验证码框
        setTimeout(function () {
            settime();
        }, 1000);
    }

    function getValidCode() {
        var phonenum = Ext.getCmp('myvalidCode').getValue();
        if (!isPoneAvailable(phonenum)) {
            Ext.MessageBox.alert("提示", "请填写正确的手机号！");
            return;
        }
        var auditor = auditorComBox_tap.getValue();
        if (!auditor) {
            Ext.Msg.alert('提示', "请选择审核人！");
            return;
        }
        var taskN = Ext.util.Format.trim(taskName.getValue());
        settime();
        //显示输入验证码框
        //Ext.getCmp('mauthCode').show();
        //验证码入库保存，组织验证码数据发送给短信接口，发送成功
        Ext.Ajax.request({
            url: 'sendAuthCode.do',
            params: {
                telephoneNum: phonenum,
                iauditUser: auditor,
                stepId: '-1',
                planId: '-1',
                iscenceId: '-1',
                serviceId: iidForTaskAudi,
                isscript: '0',
                proType: '10',
                planName: serviceNameForTaskAudi,
                systemId: '-1',
                menuName: '任务申请',
                operationType: '提交双人复核',
                taskName: taskN
            },
            method: 'post',
            success: function (response, text) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    Ext.Msg.show({
                        title: '提示',
                        msg: " 验证码已发送至手机！ ",
                        buttons: Ext.MessageBox.OK
                    });
                } else {
                    Ext.Msg.show({
                        title: '提示',
                        msg: message,
                        buttons: Ext.MessageBox.OK
                    });
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    var cycleExecCronPanel = Ext.create('Ext.form.Panel', {
        width: 330,
        border: false,
        hidden: true,
        name: 'execTimeComponent',
//        layout: {
//	            type: 'hbox',
//	            align : 'stretch'
//	    },
        items: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [cycleExecCronText, selectCronButton]
        }]
    });


    var taskAuditSendEmailUser = Ext.create('Ext.form.ComboBox', {
        editable: true,
        fieldLabel: '收件人',
        emptyText: '--请选择收件人--',
        labelAlign: 'right',
        labelWidth: 65,
        width: "17%",
        hidden: !taskApplyForSPDBSwitch
    });

    taskAuditSendEmailUser = sendEmailUser;

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    var topBar;
    if (ssTimerTaskSwitch) {
        topBar = Ext.create('Ext.form.Panel', {
            region: 'north',
            layout: 'anchor',
            width: '50%',
            buttonAlign: 'center',
            cls: 'window_border panel_space_left panel_space_right',
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [taskName, eachNum, execComBox_tap, isSaveTemplateCk, execModeGroup, shutdownCheckboxObj, validCode, getValidCodeButton,
                    {
                        xtype: 'button',
                        text: '查看脚本',
                        hidden: true,
                        cls: 'Common_Btn',
                        handler: function () {
                            viewDetail(iidForTaskAudi,labelEdit)
                        }
                    }]
            }, {
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [
                    auditorComBox_tap, execUser, listComBox2, butterflyVerison2, execTimeComponent, cycleExecCronPanel, authCode,//isTimerTask,cycleExecCronText,selectCronButton
//	        	auditorComBox_tap, execUser,butterflyVerison,listComBox, execTimeComponent,cycleExecCronPanel//isTimerTask,cycleExecCronText,selectCronButton
//	        	auditorComBox_tap, execUser,butterflyVerison,listComBox, execTimeComponent,cycleExecCronPanel,authCode,
                    {
                        xtype: 'button',
                        text: '查看脚本',
                        cls: 'Common_Btn',
                        handler: function () {
                            viewDetail(iidForTaskAudi,labelEdit)
                        }
                    }, timeout//isTimerTask,cycleExecCronText,selectCronButton
                ]
            }, {
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [taskAuditSendEmailUser]
            }
            ]
        });
    } else {
        topBar = Ext.create('Ext.form.Panel', {
            region: 'north',
            layout: 'anchor',
            width: '50%',
            buttonAlign: 'center',
            cls: 'window_border panel_space_left panel_space_right',
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [taskName, eachNum, auditorComBox_tap, isSaveTemplateCk, execUser, execComBox_tap, listComBox2, butterflyVerison2,
                    {
                        xtype: 'button',
                        text: '查看脚本',
                        cls: 'Common_Btn',
                        handler: function () {
                            viewDetail(iidForTaskAudi,labelEdit)
                        }
                    }, timeout]
            }]
        });
    }

    function checkOrderNumber(){
        var returnMsg = "";
        var orderNumber = butterflyVerison2.getValue();
        //单号为空时给出提示、弹出窗口填写手机号，获取单号后提交，获取失败给出错误提示
        if(orderNumber.trim() == null || orderNumber.trim() == ''){
            //单号输入框为空时，弹出提示，输入手机号
            Ext.Msg.confirm("请确认", "单号未填写，是否获取单号？", function (button, text) {
                if (button == "yes") {
                    //显示弹窗输入手机号
                    var telephoneShowWin = Ext.create('Ext.window.Window', {
                        // title: '请输入手机号',
                        closable: true,
                        closeAction: 'hide',
                        modal: true,
                        width: 500,
                        height: 130,
                        layout: {
                            type: 'border',
                            padding: 5
                        },
                        // items: [auditing_form_sm],
                        dockedItems: [{
                            xtype: 'toolbar',
                            dock: 'bottom',
                            layout: {pack: 'center'},
                            items: [teltephoneparam,'->',{
                                xtype: "button",
                                cls: 'Common_Btn',
                                text: "确定",
                                handler: function () {
                                    Ext.Ajax.request({
                                        url: 'getItsmOrderNumber.do',
                                        method: 'POST',
                                        params: {
                                            teltephone:teltephoneparam.getValue(),//电话号码，ieai_user的itelephone字段
                                            taskName:Ext.util.Format.trim(taskName.getValue()),//任务名称，任务申请时的任务名称
                                            serviceId:iidForTaskAudi//系统名称，脚本的二级分类
                                        },
                                        success: function (response, request) {
                                            //0为成功，其它为失败
                                            var success = Ext.decode(response.responseText).success;
                                            if (success == '0') {
                                                addbutterflyVerison2.setValue(Ext.decode(response.responseText).message);
                                                Ext.Msg.alert('提示', '单号获取完成，请提交任务');
                                            } else {
                                                returnMsg = "1:"+Ext.decode(response.responseText).message;
                                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                            }
                                        },
                                        failure: function (result, request) {
                                            secureFilterRs(result, "单号获取失败！");
                                        }
                                    });
                                    // auditingWin.close();
                                    this.up("window").close();
                                }
                            }]
                        }]
                    });
                    telephoneShowWin.show();
                }
            });
        }else{
        //单号不为空，执行单号校验，校验通过提交任务，校验不通过给出错误提示
            Ext.Ajax.request({
                url: 'checkItsmOrderNumber.do',
                method: 'POST',
                async:false,
                params: {
                    orderNumber: orderNumber
                },
                success: function (response, request) {
                    //0为成功，其它为失败
                    var success = Ext.decode(response.responseText).success;
                    if (success == '0') {
                        returnMsg = "0:"+orderNumber;
                    } else {
                        returnMsg = "1:"+Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "单号校验失败！");
                }
            });
            return returnMsg;
        }
    }


    var centerPanel = Ext.create('Ext.panel.Panel', {
        collapsible: false,
        border: false,
        region: 'center',
        height: contentPanel.getHeight() * 0.68,
        layout: 'border',
        items: [tabPanelForChosedDevice, paramsAndAttachmentsPanel/*attachmentGrid*/]
    });

    function checkComputerSys() {
        var res = "";
        Ext.Ajax.request({
            url: 'checkComputerPermissionForSPDB.do',
            method: 'POST',
            async: false,
            params: {
                cpids: chosedCpIds
            },
            success: function (response, request) {
                res = Ext.decode(response.responseText).message;
            },
            failure: function (result, request) {
                res = '查看是否具有系统管理员权限失败！';
                //Ext.Msg.alert('提示', '查看是否具有系统管理员权限失败！');
            }
        });
        return res;
    }

    var countdown = 1;
    function settimeSec(btn, text) {
        if (countdown == 0) {
            btn.setDisabled(false);
            btn.setText(text);
            btn.setWidth(80);
            countdown = 1;
        } else {
            btn.setDisabled(true);
            btn.setText(text + "(" + countdown + ")");
            btn.setWidth(100);
            countdown--;
            setTimeout(function () {
                settimeSec(btn, text)
            }, 1000)
        }

    }

    var subBtn = Ext.create('Ext.Button', {
                text: '提交',
                height: 38,
                id: 'submitBtn',
                cls: 'Common_Btn',
                handler: function () {
                    if (sysdeptauditRole != null && sysdeptauditRole != ''&& sysdeptauditRole != 'null') {
                        if ((execUser.getValue() == '' || execUser.getValue() == 'root') && startUserValue == '') {
                            openAuditWin();
                            return;
                        }
                    }
                    if (chosedAgentIds.length <= 0 && chosedGroupNames.length <= 0) {
                        Ext.Msg.alert('提示', "提交失败，请选择设备！");
                        return;
                    }
                    if (taskApplyCheckSysAdminSwitch && taskApplyForSPDBSwitch) {
                        var checkSysAdminMes = checkSysAdmin(execUser.getValue());
                        if (checkSysAdminMes != null && checkSysAdminMes != "") {
                            execUser.setValue('');
                            Ext.Msg.alert('提示', checkSysAdminMes);
                            return;
                        }
                    }
                    var sendEmailUserRecord = [];
                    let sendEmailUserIds = taskAuditSendEmailUser.getValue();
                    if (taskApplyForSPDBSwitch && !Array.isArray(sendEmailUserIds)) {
                        Ext.Msg.alert('提示', "手动输入后必须选择！");
                        return;
                    }
                    if (sendEmailUserIds != null) {
                        sendEmailUserIds.forEach(id => {
                            let index = taskAuditSendEmailUser.getStore().find('id', id);
                            sendEmailUserRecord.push(taskAuditSendEmailUser.getStore().getAt(index).data);
                        });
                    }
                    //判断输入的审核人是否合法 start
                    var displayField = auditorComBox_tap.getRawValue();
                    if (!Ext.isEmpty(displayField)) {
                        //判断输入是否合法标志，默认false，代表不合法
                        var flag = false;
                        //遍历下拉框绑定的store，获取displayField
                        auditorStore_tap.each(function (record) {
                            //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                            var data_fullName = record.get('fullName');
                            if (data_fullName == displayField) {
                                flag = true;
                                auditorComBox_tap.setValue(record.get('loginName'));
                            }
                        });
                        if (!flag) {
                            Ext.Msg.alert('提示', "输入的审核人非法");
                            auditorComBox_tap.setValue("");
                            return;
                        }
                    }
                    //判断输入的审核人是否合法  end
                    paramStore.sort('paramOrder', 'ASC');
                    var m = paramStore.getRange(0, paramStore.getCount() - 1);
                    var jsonData = "[";
                    for (var i = 0, len = m.length; i < len; i++) {
//                        var n = 0;

                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                        //如果参数类型为加密字符串，则对值进行加密处理
                        var defaultStrValue = "";
                        var strValue = "";
                        if (paramType == 'IN-string(加密)') {
                            var defaultStrJM = getSMEncode(m[i].get("paramDefaultValue"),0);
                            if(defaultStrJM == null || defaultStrJM == ''){
                                defaultStrValue = m[i].get("paramDefaultValue") ? getSMEncode(m[i].get("paramDefaultValue"),1) : '';
                            }else{
                                defaultStrValue = m[i].get("paramDefaultValue") ? getSMEncode(defaultStrJM,1) : '';
                            }
                            var strValueJM = getSMEncode(m[i].get("paramValue"),0);
                            if(strValueJM == null || strValueJM == ''){
                                strValue = m[i].get("paramValue") ? getSMEncode(m[i].get("paramValue"),1) : '';
                            }else{
                                strValue = m[i].get("paramValue") ? getSMEncode(strValueJM,1) : '';
                            }
                        }else {
                            defaultStrValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
                            strValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';
                        }
                        var paramDefaultValue = defaultStrValue;
                        var paramValue = strValue;
                        m[i].set("paramValue",paramValue);
                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
                        var iorder = m[i].get("paramOrder");
                        var ruleName = m[i].get("ruleName");
                        if (bhParameterCheckSwitch) {
                            //根据选中的验证规则名拿到对应的正则表达式
                            if (ruleName != "" && ruleName != null) {
                                Ext.Ajax.request({
                                    url: 'queryParameterCheckRule.do',
                                    method: 'POST',
                                    async: false,
                                    params: {
                                        ruleName: ruleName
                                    },
                                    success: function (response, request) {
                                        dataList = Ext.decode(response.responseText).dataList;

                                    },
                                    failure: function (result, request) {
                                        Ext.Msg.alert('提示', '获取验证规则失败！');
                                    }
                                });
                                //用拿到的正则去校验默认值
                                var patt = new RegExp(dataList);
                                if (patt.exec(paramValue) == null) {
                                    setMessage('顺序为' + "“" + iorder + "”" + '的默认值校验不通过请检查！');
                                    return;
                                }
                            }
                        }
                        let count = 0;
                        for (let j = 0; j < paramValue.length; j++) {
                            if (paramValue[j] == "'") {
                                count++;
                            }
                        }
                        if (count % 2 !=0) {
                            setMessage('参数默认值输入单引号，必须前引号、后引号同时出现！');
                            return;
                        }
                        if (paramValue.indexOf('"') != -1) {
                            setMessage('参数中不允许有双引号！');
                            return;
                        }
                        if (scriptTypeForTaskAudi == 'bat') {
                            if (paramDefaultValue.indexOf('"') >= 0) {
                                Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                                return;
                            }
                        }
                        if ("" == paramType) {
                            setMessage('参数类型不能为空！');
                            return;
                        }
                        if (fucCheckLength(paramDesc) > 250) {
                            setMessage('参数描述不能超过250字符！');
                            return;
                        }

                        if (fucCheckLength(paramValue) > 3000 || fucCheckLength(paramDefaultValue) > 3000) {
                            setMessage('参数不允许超过3000字符！');
                            return;
                        }

                        // if (Ext.isEmpty(Ext.util.Format.trim(paramValue)) && Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
                        //     setMessage('【默认参数值】为空时必须填写【参数值】！');
                        //     return;
                        // }


                if (paramType == 'IN-int' || paramType == 'OUT-int' || paramType == 'int') {
                    if (Ext.isEmpty(Ext.util.Format.trim(paramValue)) && paramDefaultValue) {
                        if(!checkIsInteger(paramDefaultValue)){
                            setMessage('参数类型为int，但参数默认值不是int类型！');
                            return;
                        }
                    }
                    if (Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue)) && paramValue) {
                        if(!checkIsInteger(paramValue)){
                            setMessage('参数类型为int，但参数值不是int类型！');
                            return;
                        }
                    }

                    if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue)) && !Ext.isEmpty(Ext.util.Format.trim(paramValue)) && !checkIsInteger(paramValue)) {
                        setMessage('参数类型为int，但参数值不是int类型！');
                        return;
                    }
                }
                if (paramType == 'IN-float' || paramType == 'OUT-float' || paramType == 'float') {
                    if (Ext.isEmpty(Ext.util.Format.trim(paramValue)) && paramDefaultValue) {
                        if(!checkIsDouble(paramDefaultValue)){
                            setMessage('参数类型为float，但参数默认值不是float类型！');
                            return;
                        }
                    }
                    if (Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue)) && paramValue) {
                        if(!checkIsDouble(paramValue)){
                            setMessage('参数类型为float，但参数值不是float类型！');
                            return;
                        }
                    }


                    if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue)) && !Ext.isEmpty(Ext.util.Format.trim(paramValue)) && !checkIsDouble(paramValue)) {
                        setMessage('参数类型为float，但参数值不是float类型！');
                        return;
                    }
                }
                if (!Ext.isEmpty(Ext.util.Format.trim(paramValue))) {
                    globalParams[m[i].get('iid')] = paramValue;
                } else if (Ext.isEmpty(Ext.util.Format.trim(paramValue)) && !Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
                    globalParams[m[i].get('iid')] = paramDefaultValue;
                } else {
                    delete globalParams[m[i].get('iid')];
                }
                var ss = Ext.JSON.encode(m[i].data);
                if (i == 0) jsonData = jsonData + ss;
                else jsonData = jsonData + "," + ss;
            }

            jsonData = jsonData + "]";

            var agent_grid_chosedRecord = agent_grid_chosed.getStore().getRange();
            if (agent_grid_chosedRecord.length == 0 && group_grid_chosed.getStore().getRange().length == 0) {
                Ext.Msg.alert('提示', "提交失败，请选择设备！");
                chosedGroupNames = [];
                chosedGroupIds = [];
                chosedAgentIds = [];
                return;
            }

            var agents = [];
            Ext.Array.each(chosedAgentIds, function (value) {
                agents.push(value);
            });
            let agentStr = agents.join(",");
            var chosedAgentUsers = [];
            var chosedExecUsers = [];
            if (chosedAgentIds.length > 0) {
                for (var x = 0, xLen = chosedAgentIds.length; x < xLen; x++) {
                    var execUserValue = $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + chosedAgentIds[x]);
                    if (undefined == execUserValue) {
                        execUserValue = "";
                    }
                    chosedAgentUsers.push(chosedAgentIds[x] + "#;#" + execUserValue);
                    let cho = new Object();
                    cho[chosedAgentIds[x]] = execUserValue;
                    chosedExecUsers.push(cho);
                }
            }
//                    if(agent_grid_chosedRecord.length>0) {
//	  					for(var x = 0, xLen = agent_grid_chosedRecord.length; x < xLen; x++){
//	  						if(undefined != agent_grid_chosedRecord[x].get('execuser')){
//	  							chosedAgentUsers.push(agent_grid_chosedRecord[x].get('iid')+"#;#"+agent_grid_chosedRecord[x].get('execuser'));
//	  							chosedExecUsers.push("{\""+agent_grid_chosedRecord[x].get('iid') + "\":\"" + agent_grid_chosedRecord[x].get('execuser')+"\"}");
//	  						}
//	  					}
//	  				}

//                    var chosedAgentIds = new Array();
//                    var records = agent_grid.getSelectionModel().getSelection();
//	  				if(records.length>0) {
//	  					for(var i = 0, len = records.length; i < len; i++){
//	  						agents.push(records[i].get('iid'));
//	  						chosedAgentIds.push(records[i].get('iid'));
//	  					}
//	  				} else {
//	  					Ext.Msg.alert('提示', "请选择服务器！");
//                        return;
//	  				}
            var auditor = auditorComBox_tap.getValue();

            if (listComBox2.getRawValue() == '后补') {
                var rgIds = resourceGroupObj.getValue();

//                    	alert(auditor);
                if (!auditor) {
                    Ext.Msg.alert('提示', "没有选择审核人！");
                    return;
                }
            }
            if (listComBox2.getRawValue() == '变更' || listComBox2.getRawValue() == '维护') {
                var rgIds = resourceGroupObj.getValue();

                var danHao = butterflyVerison2.getValue();
//                    	alert(danHao);
//                    	if(!audiCodeSwitch){
                if (scriptOddNumberSwitch) {
                    if (danHao == "") {
                        Ext.Msg.alert('提示', "请输入单号！");
                        return;
                    }
                }
            }

                    //var isdelay;
                    var execRadioValue; // 执行策略单选
                    var execT; //corn表达式 周期执行使用
                    var execTimeDisplay; //定时执行 执行时间 年月日时分秒展示
                    if (ssTimerTaskSwitch) {
                        execRadioValue = execModeGroup.getChecked()[0].inputValue;
                        if (execRadioValue == 1) {//周期执行
                            execT = cycleExecCronText.getValue();
                            execTimeDisplay = '';
                            if (execT == '' || execT == null) {
                                Ext.Msg.alert('提示', "执行策略为周期执行，执行周期不能为空！");
                                return;
                            }
                        } else if (execRadioValue == 2) {//定时执行
                            execTimeDisplay = execTimeComponent.getRawValue();
                            execT = '';
                            if (execTimeDisplay == '' || execTimeDisplay == null) {
                                Ext.Msg.alert('提示', "执行策略为定时执行，执行时间不能为空！");
                                return;
                            }
                        } else if (execRadioValue == 0 || execRadioValue == 3) {//触发执行
                            execTimeDisplay = '';
                            execT = '';
                        }
                    } else {
                        execTimeDisplay = '';
                        execT = "";
                        execRadioValue = 0;
                    }

            var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
            if (Ext.isEmpty(execDescForExec)) {
                Ext.Msg.alert('提示', "没有填写执行描述！");
                return;
            }
            if (fucCheckLength(execDescForExec) > 255) {
                setMessage('执行描述不能超过255字符！');
                return;
            }
            var taskN = Ext.util.Format.trim(taskName.getValue());
            if (Ext.isEmpty(taskN)) {
                Ext.Msg.alert('提示', "任务名称不能为空！");
                return;
            }

            if (fucCheckLength(taskN) > 255) {
                setMessage('任务名称不能超过255字符！');
                return;
            }
            var en = eachNum.getValue();
            if (!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
                if (en > eachNumForA) {
                    setMessage('并发数量不能超过' + eachNumForA);
                    return;
                }
            } else {
                setMessage('并发数量不合法！');
                return;
            }

            let timeoutValue = timeout.getValue();
            if (Ext.isEmpty(timeoutValue) || timeoutValue < -1) {
                Ext.MessageBox.alert("提示", "超时时间输入值非法");
                return;
            }


                    if (Ext.isEmpty(en)) {
                        en = eachNumForA;
                    }
                    //如果输入验证码，校验验证码是否正确
                    var authCodeForExec = Ext.util.Format.trim(authCode.getValue());
                    var phonenum = Ext.getCmp('myvalidCode').getValue();
                    if (!Ext.isEmpty(authCodeForExec)) {
                        if (!isPoneAvailable(phonenum)) {
                            Ext.MessageBox.alert("提示", "请填写正确的手机号！");
                            return;
                        }
                    }
                    var butterflyV = "";
                    //var check =Ext.getCmp("checkVersion").getValue();
                    var check = true;
                    var listComBoxValue = listComBox2.getValue();//操作类型
                    var oddNumbersType = 0;//单号类型
                    if (scriptOddNumberSwitch) {//打开单号开关时，校验单号
                        if (listComBoxValue == 0 || listComBoxValue == "变更" || listComBoxValue == 1) {
                            oddNumbersType = 0;
                        } else if (listComBoxValue == 2 || listComBoxValue == "维护") {
                            oddNumbersType = 1;
                        } else {
                            oddNumbersType = 2//后补
                        }
                        if (0 == oddNumbersType || 1 == oddNumbersType) {
                            check = false;
                        }
                        if (scriptLevelForTaskAudi != 0) {
                            if (scriptOddNumberSwitch && !check) {
                                butterflyV = Ext.util.Format.trim(butterflyVerison2.getValue());
                                if (butterflyV == null || butterflyV == "") {
                                    Ext.Msg.alert('提示', "单号不能为空！");
                                    return;
                                }
                                if (fucCheckLength(butterflyV) > 255) {
                                    Ext.Msg.alert('提示', "单号不能超过255字符！");
                                    return;
                                }
                            }
                        }
                    }

                    // 验证单号
                    // 1、验证通过：将单号保存并且正常启动任务；验证不通过：不执行提交操作，并提示验证不通过信息
                    // 2、不填写单号时，给出提示，确定后出现弹窗，输入手机号，之后调用itsm单号获取接口，并正常提交任务，否则提示错误信息
                    if(itsmOrderNumberSwitch){
                        if(addbutterflyVerison2.getValue().trim() != null && addbutterflyVerison2.getValue().trim() != ''){
                            butterflyV = addbutterflyVerison2.getValue().trim();
                        }else{
                            var orderNumerCheck = checkOrderNumber();
                            if(orderNumerCheck == null || orderNumerCheck == ''){
                                return;
                            }
                            if("0" != orderNumerCheck.split(":")[0]){
                                return;
                            }
                            //单号赋值
                            butterflyV = orderNumerCheck.split(":")[1];
                        }
                    }
                    var mxIid = iidForTaskAudi + ":" + 4;
                    startData[mxIid] = {
                        'actNo': 4,
                        'actType': 0,
                        'actName': '基础脚本1'
                    };
                    startData[mxIid]['isShutdown'] = shutdownCheckboxObj.getValue();
                    startData[mxIid]['chosedResGroups'] = chosedGroupNames;
                    startData[mxIid]['chosedAgentIds'] = chosedAgentIds;
                    startData[mxIid]['globalParams'] = globalParams;
                    startData[mxIid]['globalConfigParams'] = {};
                    startData[mxIid]['globalStartUser'] = Ext.util.Format.trim(execUser.getValue());
                    startData[mxIid]['globalConfigStartUser'] = {};
                    startData[mxIid]['finalChosedAgentsAndDbSources'] = {};
                    startData[mxIid]['eachNum'] = en;
                    startData[mxIid]['chosedAgentUser'] = chosedExecUsers;
                    startData[mxIid]['itimeout'] = timeoutValue;
                    var performUser = execComBox_tap.getValue();
                    var isSaveTemplate = isSaveTemplateCk.getValue();
                    attachmentStore.each(function (record) {
                        let scriptTempAttachment = {
                            "iid": record.get('iid'),
                            "isTempFlag": record.get('isTempFlag')
                        };
                        scriptTempAttachmentArray.push(scriptTempAttachment);
                    });

            //填写验证码了，去数据库校验。
            //校验验证码是否正确
            if (!Ext.isEmpty(authCodeForExec)) {//填写验证码了，去数据库校验。
                Ext.Ajax.request({
                    url: 'validAuthCode.do',
                    method: 'POST',
                    params: {
                        serviceId: iidForTaskAudi,
                        stepId: '-1',
                        planId: '-1',
                        iscenceId: '-1',
                        authCode: authCodeForExec,
                        telephoneNum: phonenum,
                        iauditUser: auditor,
                        isscript: '0',
                        proType: '10',
                        systemId: '-1',
                    },
                    success: function (response, opts) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                            if (isSaveTemplate) {
                                Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function (btn, text, cfg) {
                                    if (btn == 'ok') {
                                        if (Ext.isEmpty(Ext.util.Format.trim(text))) {
                                            var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
                                            Ext.Msg.show(Ext.apply({}, {msg: newMsg}, cfg));
                                        } else {
                                            Ext.MessageBox.wait("信息验证中...", "提示");
                                            var customName = Ext.util.Format.trim(text);
                                            Ext.Ajax.request({
                                                url: 'checkCustomTemplateNameIsExist.do',
                                                params: {
                                                    customName: customName,
                                                    flag: 1
                                                },
                                                method: 'POST',
                                                success: function (response, options) {
                                                    if (!Ext.decode(response.responseText).success) {
                                                        var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
                                                        Ext.Msg.show(Ext.apply({}, {msg: newMsg}, cfg));
                                                    } else {
                                                        Ext.Ajax.request({
                                                            url: 'scriptExecAuditing.do',
                                                            method: 'POST',
                                                            timeout: 6000000,
                                                            /*dataType:'json',
                                                        jsonData:Ext.JSON.encode(scriptTempAttachmentArray),*/
                                                            params: {
                                                                scriptTempAttachments: Ext.JSON.encode(scriptTempAttachmentArray),
                                                                serviceId: iidForTaskAudi,
                                                                execUser: execUser.getValue(),
                                                                agents: agentStr,
                                                                rgIds: rgIds,
                                                                params: jsonData,
                                                                execDesc: execDescForExec,
                                                                auditor: auditor,
                                                                //isDelay:isdelay,
                                                                execTime: execT,
                                                                execTimeDisplay: execTimeDisplay,
                                                                execStrategy: execRadioValue,
                                                                taskName: taskN,
                                                                performUser: performUser,
                                                                butterflyversion: butterflyV,
                                                                data: JSON.stringify(startData),
                                                                eachNum: en,
                                                                scriptLevel: scriptLevelForTaskAudi,
                                                                chosedAgentUsers: chosedAgentUsers,
                                                                oddNumbersType: oddNumbersType,
                                                                resGroupFlag: resGroupFlag,
                                                                attachmentIds: attachmentIds,
                                                                messageNotice:isMessageNotice.getValue() ? true : false
                                                            },
                                                            success: function (response, opts) {
                                                                var success = Ext.decode(response.responseText).success;
                                                                var message = Ext.decode(response.responseText).message;
                                                                if (success) {
                                                                    if (scriptLevelForTaskAudi != 0) {

                                                                        Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                                                    }
                                                                    var iworkItemid = Ext.decode(response.responseText).workItemId;
                                                                    Ext.Ajax.request({
                                                                        url: 'updateWokitemId.do',
                                                                        method: 'POST',
                                                                        params: {
                                                                            workitemId: iworkItemid,
                                                                            serviceId: iidForTaskAudi,
                                                                            stepId: '-1',
                                                                            planId: '-1',
                                                                            iscenceId: '-1',
                                                                            authCode: authCodeForExec,
                                                                            telephoneNum: phonenum,
                                                                            iauditUser: auditor,
                                                                            isscript: '0',
                                                                            proType: '10',
                                                                            systemId: '-1'
                                                                        },
                                                                        success: function (response, opts) {

                                                                        },
                                                                        failure: function (result, request) {
                                                                            secureFilterRs(result, "回更workitemid失败！");
                                                                        }
                                                                    });
                                                                    Ext.Ajax.request({
                                                                        url: 'scriptExecForOneRecord.do',
                                                                        method: 'POST',
                                                                        params:
                                                                            {
                                                                                iworkItemid: iworkItemid
                                                                            },
                                                                        success: function (response, opts) {
                                                                            var success = Ext.decode(response.responseText).success;
                                                                            if (success) {
                                                                                if (scriptLevelForTaskAudi == 0) {//白名单，直接调用执行方法
                                                                                    if (execRadioValue == 0) {//不是定时任务就直接调用任务执行中的执行方法
                                                                                        var uuid = '';
                                                                                        Ext.Ajax.request({
                                                                                            url: 'queryUuidById.do',
                                                                                            method: 'POST',
                                                                                            async: false,
                                                                                            params: {
                                                                                                serviceId: iidForTaskAudi
                                                                                            },
                                                                                            success: function (response, options) {
                                                                                                uuid = Ext.decode(response.responseText).serviceUuid;
                                                                                            },
                                                                                            failure: function (result, request) {
                                                                                            }
                                                                                        });
                                                                                        Ext.Ajax.request({
                                                                                            url: 'execScriptServiceStart.do',
                                                                                            method: 'POST',
                                                                                            params: {
                                                                                                serviceId: iidForTaskAudi,
                                                                                                uuid: uuid,
                                                                                                serviceName: serviceNameForTaskAudi,
                                                                                                scriptType: scriptTypeForTaskAudi,
                                                                                                workItemId: iworkItemid,
                                                                                                coatId: 0,
                                                                                                isFlow: 0,
                                                                                                attachmentIds: attachmentIds
                                                                                            },
                                                                                            success: function (response, request) {
                                                                                                var success = Ext.decode(response.responseText).success;
                                                                                                if (success) {
                                                                                                    var flowId = Ext.decode(response.responseText).content;
                                                                                                    Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId：" + flowId);
                                                                                                }
                                                                                            },
                                                                                            failure: function (result, request) {
                                                                                                Ext.Msg.alert('提示', '执行失败！');
                                                                                            }
                                                                                        });
                                                                                    }
                                                                                } else {
                                                                                    Ext.Msg.alert('提示', '提交成功');
                                                                                }
                                                                            }
                                                                        }
                                                                    });

                                                                    if (typeof (auditingWin) != "undefined" && auditingWin) {
                                                                        auditingWin.close();
                                                                    }

                                                                    Ext.Ajax.request({
                                                                        url: 'saveFlowCustomTemplate.do',
                                                                        method: 'POST',
                                                                        params: {
                                                                            customName: customName,
                                                                            serviceId: iidForTaskAudi,
                                                                            data: JSON.stringify(startData),
                                                                            audiUserLoginName: auditor,
                                                                            taskName: taskN,
                                                                            flag: 1
                                                                        },
                                                                        success: function (response, options) {
                                                                            var success1 = Ext.decode(response.responseText).success;
                                                                            //var message1 = Ext.decode(response.responseText).message;
                                                                            if (success1) {
                                                                                Ext.MessageBox.show({
                                                                                    title: "提示",
                                                                                    msg: '请求已经发送到审核人<br>常用任务保存成功！',
                                                                                    buttonText: {
                                                                                        yes: '确定'
                                                                                    },
                                                                                    buttons: Ext.Msg.YES
                                                                                });
                                                                            }
                                                                        },
                                                                        failure: function (result, request) {
                                                                            Ext.MessageBox.show({
                                                                                title: "提示",
                                                                                msg: "请求已经发送到审核人<br>模板保存失败",
                                                                                buttonText: {
                                                                                    yes: '确定'
                                                                                },
                                                                                buttons: Ext.Msg.YES
                                                                            });
                                                                        }

                                                                    });
                                                                } else {
                                                                    Ext.MessageBox.alert("提示", message);
                                                                }
                                                            },
                                                            failure: function (result, request) {
                                                                secureFilterRs(result, "操作失败！");
                                                            }
                                                        });
                                                    }
                                                },
                                                failure: function (result, request) {
                                                }
                                            });
                                        }

                                    }

                                        });
                                    } else {
                                        Ext.MessageBox.wait("信息验证中...", "提示");
                                        Ext.Ajax.request({
                                            url: 'scriptExecAuditing.do',
                                            method: 'POST',
                                            timeout: 6000000,
                                            /*dataType:'json',
                                        jsonData:Ext.JSON.encode(scriptTempAttachmentArray),*/
                                            params: {
                                                scriptTempAttachments: Ext.JSON.encode(scriptTempAttachmentArray),
                                                serviceId: iidForTaskAudi,
                                                execUser: execUser.getValue(),
                                                agents: agentStr,
                                                rgIds: rgIds,
                                                params: jsonData,
                                                execDesc: execDescForExec,
                                                auditor: auditor,
                                                execTime: execT,
                                                execTimeDisplay: execTimeDisplay,
                                                execStrategy: execRadioValue,
                                                taskName: taskN,
                                                performUser: performUser,
                                                butterflyversion: butterflyV,
                                                data: JSON.stringify(startData),
                                                eachNum: en,
                                                scriptLevel: scriptLevelForTaskAudi,
                                                oddNumbersType: oddNumbersType,
                                                chosedAgentUsers: chosedAgentUsers,
                                                resGroupFlag: resGroupFlag,
                                                isFromCustom: 1, //为了后台判断是否走带图的，1是走不带图的
                                                userlistJson: Ext.encode(sendEmailUserRecord),
                                                invoke:invoke,
                                                invokeId:invokeId,
                                                messageNotice:isMessageNotice.getValue() ? true : false
                                            },
                                            success: function (response, opts) {
                                                var success = Ext.decode(response.responseText).success;
                                                var message = Ext.decode(response.responseText).message;
                                                if (success) {
                                                    var iworkItemid = Ext.decode(response.responseText).workItemId;
                                                    Ext.Ajax.request({
                                                        url: 'updateWokitemId.do',
                                                        method: 'POST',
                                                        params: {
                                                            workitemId: iworkItemid,
                                                            serviceId: iidForTaskAudi,
                                                            stepId: '-1',
                                                            planId: '-1',
                                                            iscenceId: '-1',
                                                            authCode: authCodeForExec,
                                                            telephoneNum: phonenum,
                                                            iauditUser: auditor,
                                                            isscript: '0',
                                                            proType: '10',
                                                            systemId: '-1'
                                                        },
                                                        success: function (response, opts) {

                                                },
                                                failure: function (result, request) {
                                                    secureFilterRs(result, "回更workitemid失败！");
                                                }
                                            });
                                            Ext.Ajax.request({
                                                url: 'scriptExecForOneRecord.do',
                                                method: 'POST',
                                                params:
                                                    {
                                                        iworkItemid: iworkItemid
                                                    },
                                                success: function (response, opts) {
                                                    var success = Ext.decode(response.responseText).success;
                                                    if (success) {
                                                        if (scriptLevelForTaskAudi == 0) {//白名单，直接调用双人复核中，同意执行方法
                                                            if (execRadioValue == 0) {//不是定时任务就直接调用任务执行中的执行方法
                                                                var uuid = '';
                                                                Ext.Ajax.request({
                                                                    url: 'queryUuidById.do',
                                                                    method: 'POST',
                                                                    async: false,
                                                                    params: {
                                                                        serviceId: iidForTaskAudi
                                                                    },
                                                                    success: function (response, options) {
                                                                        uuid = Ext.decode(response.responseText).serviceUuid;
                                                                    },
                                                                    failure: function (result, request) {
                                                                    }
                                                                });
                                                                Ext.Ajax.request({
                                                                    url: 'execScriptServiceStart.do',
                                                                    method: 'POST',
                                                                    params: {
                                                                        serviceId: iidForTaskAudi,
                                                                        uuid: uuid,
                                                                        serviceName: serviceNameForTaskAudi,
                                                                        scriptType: scriptTypeForTaskAudi,
                                                                        workItemId: iworkItemid,
                                                                        coatId: 0,
                                                                        isFlow: 0
                                                                    },
                                                                    success: function (response, request) {
                                                                        var success = Ext.decode(response.responseText).success;
                                                                        if (success) {
                                                                            var flowId = Ext.decode(response.responseText).content;
                                                                            Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId：" + flowId);
                                                                        }
                                                                    },
                                                                    failure: function (result, request) {
                                                                        Ext.Msg.alert('提示', '执行失败！');
                                                                    }
                                                                });
                                                            }
                                                        } else {
                                                            Ext.Msg.alert('提示', '提交成功');
                                                        }
                                                    }
                                                }
                                            });
                                            if (typeof (auditingWin) != "undefined" && auditingWin) {
                                                auditingWin.close();
                                            }
                                        } else {
                                            Ext.MessageBox.alert("提示", message);
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "操作失败！");
                                        if (typeof (auditingWin) != "undefined" && auditingWin) {
                                            auditingWin.close();
                                        }
                                    }
                                });
                            }

                        } else {
                            Ext.MessageBox.alert("提示", message);
                        }
                    },
                    failure: function (result, request) {
                        secureFilterRs(result, "操作失败！");
                    }

                });
            } else {
                if (isSaveTemplate) {
                    Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function (btn, text, cfg) {
                        if (btn == 'ok') {
                            if (Ext.isEmpty(Ext.util.Format.trim(text))) {
                                var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
                                Ext.Msg.show(Ext.apply({}, {msg: newMsg}, cfg));
                            } else {
                                Ext.MessageBox.wait("信息验证中...", "提示");
                                var customName = Ext.util.Format.trim(text);
                                Ext.Ajax.request({
                                    url: 'checkCustomTemplateNameIsExist.do',
                                    params: {
                                        customName: customName,
                                        flag: 1
                                    },
                                    method: 'POST',
                                    success: function (response, options) {
                                        if (!Ext.decode(response.responseText).success) {
                                            var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
                                            Ext.Msg.show(Ext.apply({}, {msg: newMsg}, cfg));
                                        } else {
                                            Ext.Ajax.request({
                                                url: 'scriptExecAuditing.do',
                                                method: 'POST',
                                                timeout: 6000000,
                                                /*dataType:'json',
                                                jsonData:Ext.JSON.encode(scriptTempAttachmentArray),*/
                                                params: {
                                                    scriptTempAttachments: Ext.JSON.encode(scriptTempAttachmentArray),
                                                    serviceId: iidForTaskAudi,
                                                    execUser: execUser.getValue(),
                                                    agents: agentStr,
                                                    rgIds: rgIds,
                                                    params: jsonData,
                                                    execDesc: execDescForExec,
                                                    auditor: auditor,
                                                    //isDelay:isdelay,
                                                    execTime: execT,
                                                    execTimeDisplay: execTimeDisplay,
                                                    execStrategy: execRadioValue,
                                                    taskName: taskN,
                                                    performUser: performUser,
                                                    butterflyversion: butterflyV,
                                                    data: JSON.stringify(startData),
                                                    eachNum: en,
                                                    scriptLevel: scriptLevelForTaskAudi,
                                                    chosedAgentUsers: chosedAgentUsers,
                                                    resGroupFlag: resGroupFlag,
                                                    oddNumbersType: oddNumbersType,
                                                    messageNotice:isMessageNotice.getValue() ? true : false
                                                },
                                                success: function (response, opts) {
                                                    var success = Ext.decode(response.responseText).success;
                                                    var message = Ext.decode(response.responseText).message;
                                                    if (success) {
                                                        var iworkItemid = Ext.decode(response.responseText).workItemId;
                                                        if (scriptLevelForTaskAudi == 0) {//白名单，直接调用双人复核中，同意执行方法
                                                            Ext.Ajax.request({
                                                                url: 'scriptExecForOneRecord.do',
                                                                method: 'POST',
                                                                params:
                                                                    {
                                                                        iworkItemid: iworkItemid
                                                                    },
                                                                success: function (response, opts) {
                                                                    var success = Ext.decode(response.responseText).success;
                                                                    if (success) {
                                                                        if (execRadioValue == 0) {//不是定时任务就直接调用任务执行中的执行方法
                                                                            var uuid = '';
                                                                            Ext.Ajax.request({
                                                                                url: 'queryUuidById.do',
                                                                                method: 'POST',
                                                                                async: false,
                                                                                params: {
                                                                                    serviceId: iidForTaskAudi
                                                                                },
                                                                                success: function (response, options) {
                                                                                    uuid = Ext.decode(response.responseText).serviceUuid;
                                                                                },
                                                                                failure: function (result, request) {
                                                                                }
                                                                            });
                                                                            Ext.Ajax.request({
                                                                                url: 'execScriptServiceStart.do',
                                                                                method: 'POST',
                                                                                params: {
                                                                                    serviceId: iidForTaskAudi,
                                                                                    uuid: uuid,
                                                                                    serviceName: serviceNameForTaskAudi,
                                                                                    scriptType: scriptTypeForTaskAudi,
                                                                                    workItemId: iworkItemid,
                                                                                    coatId: 0,
                                                                                    isFlow: 0
                                                                                },
                                                                                success: function (response, request) {
                                                                                    var success = Ext.decode(response.responseText).success;
                                                                                    if (success) {
                                                                                        var flowId = Ext.decode(response.responseText).content;
                                                                                        Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId：" + flowId);
                                                                                    }
                                                                                },
                                                                                failure: function (result, request) {
                                                                                    Ext.Msg.alert('提示', '执行失败！');
                                                                                }
                                                                            });
                                                                        } else {
                                                                            Ext.Msg.alert('提示', '提交成功');
                                                                        }
                                                                    }
                                                                }
                                                            });
                                                        }
                                                        if (typeof (auditingWin) != "undefined" && auditingWin) {
                                                            auditingWin.close();
                                                        }

                                                        Ext.Ajax.request({
                                                            url: 'saveFlowCustomTemplate.do',
                                                            method: 'POST',
                                                            params: {
                                                                customName: customName,
                                                                serviceId: iidForTaskAudi,
                                                                data: JSON.stringify(startData),
                                                                audiUserLoginName: auditor,
                                                                taskName: taskN,
                                                                flag: 1
                                                            },
                                                            success: function (response, options) {
                                                                var success1 = Ext.decode(response.responseText).success;
                                                                //var message1 = Ext.decode(response.responseText).message;
                                                                if (success1) {
                                                                    Ext.MessageBox.show({
                                                                        title: "提示",
                                                                        msg: '请求已经发送到审核人<br>常用任务保存成功！',
                                                                        buttonText: {
                                                                            yes: '确定'
                                                                        },
                                                                        buttons: Ext.Msg.YES
                                                                    });
                                                                }
                                                            },
                                                            failure: function (result, request) {
                                                                Ext.MessageBox.show({
                                                                    title: "提示",
                                                                    msg: "请求已经发送到审核人<br>模板保存失败",
                                                                    buttonText: {
                                                                        yes: '确定'
                                                                    },
                                                                    buttons: Ext.Msg.YES
                                                                });
                                                            }

                                                        });
                                                    } else {
                                                        Ext.MessageBox.alert("提示", message);
                                                    }
                                                },
                                                failure: function (result, request) {
                                                    secureFilterRs(result, "操作失败！");
                                                }
                                            });
                                        }
                                    },
                                    failure: function (result, request) {
                                    }
                                });
                            }

                        }

                            });
                        } else {
                            Ext.MessageBox.wait("信息验证中...", "提示");
                            Ext.Ajax.request({
                                url: 'scriptExecAuditing.do',
                                method: 'POST',
                                timeout: 6000000,
                                /*dataType:'json',
                                jsonData:Ext.JSON.encode(scriptTempAttachmentArray),*/
                                params: {
                                    scriptTempAttachments: Ext.JSON.encode(scriptTempAttachmentArray),
                                    serviceId: iidForTaskAudi,
                                    execUser: execUser.getValue(),
                                    agents: agentStr,
                                    rgIds: rgIds,
                                    params: jsonData,
                                    execDesc: execDescForExec,
                                    auditor: auditor,
                                    execTime: execT,
                                    execTimeDisplay: execTimeDisplay,
                                    execStrategy: execRadioValue,
                                    taskName: taskN,
                                    performUser: performUser,
                                    butterflyversion: butterflyV,
                                    data: JSON.stringify(startData),
                                    eachNum: en,
                                    scriptLevel: scriptLevelForTaskAudi,
                                    oddNumbersType: oddNumbersType,
                                    chosedAgentUsers: chosedAgentUsers,
                                    resGroupFlag: resGroupFlag,
                                    isFromCustom: 1,//为了后台判断是否走带图的，1是走不带图的
                                    userlistJson: Ext.encode(sendEmailUserRecord),
                                    invoke: invoke,
                                    invokeId:invokeId,
                                     startUser:startUserValue,
                                    messageNotice:isMessageNotice.getValue() ? true : false
                                },
                                success: function (response, opts) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if (success) {
                                        var iworkItemid = Ext.decode(response.responseText).workItemId;
                                        if (scriptLevelForTaskAudi == 0 || listComBox2.getRawValue() == '变更' || listComBox2.getRawValue() == '维护') {//白名单，直接调用双人复核中，同意执行方法
                                            if (execRadioValue == 0) {//不是定时任务就直接调用任务执行中的执行方法
                                                var uuid = '';
                                                Ext.Ajax.request({
                                                    url: 'queryUuidById.do',
                                                    method: 'POST',
                                                    async: false,
                                                    params: {
                                                        serviceId: iidForTaskAudi
                                                    },
                                                    success: function (response, options) {
                                                        uuid = Ext.decode(response.responseText).serviceUuid;
                                                    },
                                                    failure: function (result, request) {
                                                    }
                                                });
                                                if (scriptLevelForTaskAudi == 0) {
                                                    Ext.Ajax.request({
                                                        url: 'execScriptServiceStart.do',
                                                        method: 'POST',
                                                        params: {
                                                            serviceId: iidForTaskAudi,
                                                            uuid: uuid,
                                                            serviceName: serviceNameForTaskAudi,
                                                            scriptType: scriptTypeForTaskAudi,
                                                            workItemId: iworkItemid,
                                                            coatId: 0,
                                                            isFlow: 0
                                                        },
                                                        success: function (response, request) {
                                                            var success = Ext.decode(response.responseText).success;
                                                            if (success) {
                                                                var flowId = Ext.decode(response.responseText).content;
                                                                Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId：" + flowId);
                                                            }
                                                        },
                                                        failure: function (result, request) {
                                                            Ext.Msg.alert('提示', '执行失败！');
                                                        }
                                                    });
                                                } else {
                                                    Ext.Msg.alert('提示', '提交成功!');
                                                }
                                            } else {
                                                Ext.Msg.alert('提示', '提交成功!');
                                            }
                                        } else {
                                            Ext.Msg.alert('提示', '提交成功!');
                                        }
                                        if (typeof (auditingWin) != "undefined" && auditingWin) {
                                            auditingWin.close();
                                        }
                                    } else {
                                        Ext.MessageBox.alert("提示", message);
                                    }
                                },
                                failure: function (result, request) {
                                    secureFilterRs(result, "操作失败！");
                                    if (typeof (auditingWin) != "undefined" && auditingWin) {
                                        auditingWin.close();
                                    }
                                }
                            });
                        }

                    }
                },
                listeners : {
                    click : function() {
                        settimeSec(Ext.getCmp('submitBtn'),'提交');
            }
        }
    });


    var isMessageNotice = Ext.create('Ext.form.field.Checkbox', {
        checked: false,
        boxLabel: '短信通知',
        name: 'messageNotice',
        id: 'messageNotice',
        labelAlign: 'right',
        hidden:!messageNoticeCheckboxSwitch
    });


    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "taskAuditingPage_area",
        layout: 'border',
        border: false,
        autoScroll: true,
        height: contentPanel.getHeight() - 40,
        items: [topBar, centerPanel/* execDescForm, scriptForm, mainP*/],
        buttons: [subBtn,isMessageNotice

        ],
        buttonAlign: 'center'
    });
    /*var editor = CodeMirror.fromTextArea(document.getElementById('codeEditView-for-auditing'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        readOnly: true
    });
    editor.setSize(contentPanel.getWidth()*0.337, contentPanel.getHeight() - 160);*/
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
//        editor.setSize(contentPanel.getWidth()*0.337, contentPanel.getHeight() - 160);
    });

    function openAuditWin() {
        Ext.define('AModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'loginName',
                type: 'string'
            }, {
                name: 'fullName',
                type: 'string'
            }, {
                name: 'phoneNum',
                type: 'string'
            }]
        });
        var startUserStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            model: 'AModel',
            proxy: {
                type: 'ajax',
                url: 'getStartUserList.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });
        startUser = Ext.create('Ext.form.ComboBox', {
            editable: true,
            fieldLabel: "审核人",
            store: startUserStore,
            queryMode: 'local',
            width: 300,
            displayField: 'fullName',
            valueField: 'loginName',
            labelWidth: 65,
            labelAlign: 'right'
        });
        var win = Ext.create('widget.window', {
            title: '启动用户root审核',
            modal: true,
            width: 390,
            height: 210,
            items: [startUser],
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'bottom',
                layout: {pack: 'center'},
                items: [{
                    xtype: "button",
                    cls: 'Common_Btn',
                    text: "确定",
                    handler: submitAutiding
                }, {
                    xtype: "button",
                    cls: 'Common_Btn',
                    text: "取消",
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            }]
        }).show();

        function submitAutiding() {
            if (startUser.getValue() == '' || startUser.getValue() == null) {
                Ext.Msg.alert('提示', "提交失败,请选择启动用户root审批人！");
                return;
            }
            startUserValue = startUser.getValue();
            Ext.Msg.alert('提示', "操作成功！");
            win.close();
        }
    }
    function StringToPassword(strs) {
        if (strs && strs != null & strs != '') {
            var password = '';
            for (var i = 0; i < strs.length; i++) {
                password = password + '●';
            }
            return password;
        } else {
            return '';
        }
    }

    function viewDetail(serviceId,labelEdit) {
//    	if(!DetailWinTi) {
        DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            modal: true,
            loader: {}
        });
//    	}

        DetailWinTi.getLoader().load(
            {
                url: 'queryOneServiceForView.do',
                params: {
                    iid: serviceId,
                    flag: 1,
                    hideReturnBtn: 1,
                    label:labelEdit
                },
                autoLoad: true,
                scripts: true
            });
        DetailWinTi.show();
    }

    /**
     * 校验是启动用户输入是否合法  返回值为空 通过校验，不为空 没通过校验
     */
    function checkSysAdmin(userName) {
        var res = "";
        var resComptr = checkComputerSys();

        //不是系统管理员 校验输入值是否为系统管理员中的三个
        if (((!sysAdminFlag && systemAdminList.indexOf(userName.toLowerCase()) != -1) ||
            (!sysAdminFlag && (userName == '' || userName == null))) && resComptr != "") {
            res = "当前登录用户不是系统高权用户，" + resComptr + "，启动用户必须输入非【root/system/administrator】用户！";
        }
        return res;
    }

    var execUserConfigWindow;
    var execUserConfigForm = null;
    var execUserNameText = null;

    function openExecUserConfigData(record) {
        if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
            if (isSumpAgentSwitch == true) {
                var sumpAgentStore = Ext.create('Ext.data.Store', {
                    fields: ['iid', 'userName'],
                    autoLoad: true,
                    proxy: {
                        type: 'ajax',
                        url: 'getSumpAgentUserList.do',
                        reader: {
                            type: 'json',
                            root: 'dataList'
                        }
                    }
                });

                sumpAgentStore.on('beforeload', function (store, options) {
                    var queryparams = {
                        agentId: record.get('iid')
                    };
                    Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
                });

                execUserNameText = Ext.create('Ext.form.field.ComboBox', {
                    name: 'execUserName',
                    labelWidth: 65,
                    queryMode: 'local',
                    fieldLabel: '执行用户',
                    width: 320,
                    displayField: 'userName',
                    valueField: 'iid',
                    editable: true,
                    typeAhead: true,
                    emptyText: '--请选择执行用户--',
                    store: sumpAgentStore,
                    labelAlign: 'right'
                });
                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var sumpAgentCount = sumpAgentStore.getRange();
                    var newExecUserName = $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + record.get("iid"));
                    if (sumpAgentCount.length > 0) {
                        if (undefined == newExecUserName) {
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                        }
                    } else {
                        if (undefined == newExecUserName) {
                            execUserNameText.setValue(record.get('execuser'));
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                            execUserNameText.setRawValue(newExecUserName);
                        }
                    }
                }
            } else {
                execUserNameText = Ext.create('Ext.form.TextField',
                    {
                        fieldLabel: '执行用户',
                        labelAlign: 'right',
                        name: "execUserName",
                        labelWidth: 65,
                        emptyText: '--请输入执行用户--',
                        width: 320,
                        xtype: 'textfield'
                    });
                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var newExecUserName1 = $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + record.get("iid"));
                    if (undefined == newExecUserName1) {
                        execUserNameText.setValue(record.get('execuser'));
                    } else {
                        execUserNameText.setValue(newExecUserName1);
                    }
                }
            }

            execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel', {
                region: 'north',
                layout: 'anchor',
                //iqueryFun : queryBtnFun,
                buttonAlign: 'right',
                // baseCls:'customize_gray_back',
                collapsible: false,//可收缩
                collapsed: false,//默认收缩
                border: false,
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    // baseCls:'customize_gray_back',
                    items: [execUserNameText]
                }, {
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    // baseCls:'customize_gray_back',
                    items: ['->', {
                        text: '确定',
                        cls: 'Common_Btn',
                        icon: '',
                        handler: function () {
                            chosedExecUser(record);
                        }
                    }]
                }]
            });

            var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
                layout: 'border',
                width: "100%",
                height: "100%",
                border: false,
                items: [execUserConfigForm],
                // cls:'customize_panel_bak'
            });

            execUserConfigWindow = Ext.create('Ext.window.Window', {
                title: "配置执行用户",
                modal: true,
                closeAction: 'destroy',
                constrain: true,
                autoScroll: false,
                //upperWin : errorTaskWin,
                width: 370,
                height: 210,
                draggable: false,// 禁止拖动
                resizable: false,// 禁止缩放
                layout: 'fit',
                items: [execUserConfig_mainPanel]
            });
        }
        execUserConfigWindow.show();
    }

    function chosedExecUser(record) {
        $("#taskAuditingPageExecUserNameText").attr("taskAuditingPageExecUserNameText" + record.get("iid"), execUserConfigForm.getForm().findField('execUserName').getRawValue());
        if (isSumpAgentSwitch == true) {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getRawValue());
        } else {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getValue());
        }
        record.commit();
        execUserConfigWindow.hide();
    }

    function checkIsNotEmptyAndUndefined(str) {
        if (trim(str) == "" && trim(str) == "undefined")
            return false;
        else
            return true;
    }

    //批量添加计算机名查询弹窗
    function batchQuery() {
        if (!publishAuditingSMWin) {
            publishAuditingSMWin = Ext.create('widget.window', {
                title: '批量查询',
                closable: true,
                closeAction: 'hide',
                modal: true,
                width: 600,
                height: 500,
                layout: {
                    type: 'border',
                    padding: 5
                },
                items: [auditing_form_sm],
                dockedItems: [{
                    xtype: 'toolbar',
                    //baseCls:'customize_gray_back',
                    dock: 'bottom',
                    layout: {pack: 'center'},
                    items: [{
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "确定并保存",
                        handler: function () {
                            this.up("window").close();
                        }
                    }, {
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "关闭并清空",
                        handler: function () {
                            pubDesc_sm.setValue('');
                            this.up("window").close();
                        }
                    }]
                }]
            });
        }
        publishAuditingSMWin.show();
        //pubDesc_sm.setValue('');
    }


    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: ipOrNameSwitch == 'true' ? 'IP' : '计算机名',
        labelAlign: 'top',
        emptyText: ipOrNameSwitch == 'true' ? '请输入IP，多个值请换行' : '请输入计算机名，多个值请换行',
        labelWidth: 93,
        margin: '10 0 0 0',
        //maxLength: 500,
        height: 350,
        width: 540,
        //columnWidth:.98,
        autoScroll: true
    });


    var teltephoneparam = Ext.create('Ext.form.field.Text', {
        name : 'teltephoneparam',
        allowBlank : false,
        maxLength : 100,
        emptyText: '请输入手机号码',
        // padding : '0 15 0 0',
        fieldLabel : '手机号码'
    });

    auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'center',
        layout: 'anchor',
        bodyCls: 'x-docked-noborder-top',
        buttonAlign: 'center',
        border: false,
        items: [{
//	    	layout:'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [pubDesc_sm]
            }]
        }]
    });
});