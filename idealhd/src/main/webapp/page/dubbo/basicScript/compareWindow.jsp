<%@page contentType="text/html; charset=utf-8"%>
<html>
	<script type="text/javascript">
	var leftVersion='<%=request.getAttribute("leftVersion")%>';
	var rightVersion='<%=request.getAttribute("rightVersion")%>';
	var leftContent=<%=request.getAttribute("leftContent")%>;
	var rightContent=<%=request.getAttribute("rightContent")%>;
	</script>
</head>
<body>
	
<div id="simpleDiv" style="width: 100%;height: 100%"></div>
<script type="text/javascript">
Ext.onReady(function () {
	//加载比对插件
	$('#simpleDiv').mergely({
		width: contentPanel.getWidth() - 300,
		height: contentPanel.getHeight() - 150,
		cmsettings: { readOnly: false },
		//加载左侧的内容
		lhs: function(setValue) {
			setValue(leftContent);
		},
		//加载右侧的内容
		rhs: function(setValue) {
			
			setValue(rightContent);
			
		},
		loaded: function () {
			var children =  $('#simpleDiv').children()
			for (const child of children) {
				if ($(child).prop('id') == '') {
					$(child).prepend('<p style="height: 30px" />')
				} else {
					$(child).prepend('<p id="'+$(child).prop('id')+'-version" style="height: 30px" />')
				}
			}
			$('#simpleDiv-editor-lhs-version').text('版本号:'+leftVersion)
			$('#simpleDiv-editor-rhs-version').text('版本号:'+rightVersion)
		}
	});
});

	</script>
<style>
	.mergely-column {
		border: none !important;
	}
	.cm-s-default {
		border: 1px solid #ccc !important;
	}
	.CodeMirror-hscrollbar {
		display: none !important;
	}
</style>
</body>
</html>
