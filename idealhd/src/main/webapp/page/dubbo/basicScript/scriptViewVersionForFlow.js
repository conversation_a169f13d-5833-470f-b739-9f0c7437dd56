Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	// 由于不确定变量作用域，无法确保后续变量变更位置，暂时保留原有变量，同时新增域变量备用
	var jspParms = tempData;
	delete tempData;
	
	var mainPanel;
	var bussId;
	var bussTypeId;
	var iid;
	var uuid;
	var serviceName;
	
	var ilter_bussId = filter_bussId;
	var ilter_bussTypeId = filter_bussTypeId;
	var ilter_scriptName = filter_scriptName;
	var ilter_serviceName = filter_serviceName;
	var ilter_scriptType = filter_scriptType;
	Ext.tip.QuickTipManager.init();
	var mainPP = Ext.create('Ext.panel.Panel', {
		border : false,
		region : 'center',
		height : contentPanel.getHeight(),
		loader : {},
		buttonAlign: 'center'
//        buttons: [
//		{
//				text : '测试',
//				height : 30,
//				cls : 'Common_Btn',
//				hidden : jspParms.submitType=='ah'?true:false,
//				handler : TEST
//			},{
//				text : '版本回退',
//				height : 30,
//				cls : 'Common_Btn',
//				hidden : jspParms.submitType=='ah'?true:false,
//				handler : versionRollBack
//			},  {
//	            text: '基本信息',
//	            height : 30,
//	            cls: 'Common_Btn',
//	            handler: function() {
//	            	Ext.Ajax.request({
//	                    url: 'scriptService/queryOneService.do',
//	                    params: {
//	                        iid: iid
//	                    },
//	                    method: 'POST',
//	                    async: false,
//	                    success: function(response, options) {
//	                        var data = Ext.decode(response.responseText);
//	                        if (data.success) {
//	                            bussId = parseInt(data.sysName);
//	                            bussTypeId = parseInt(data.bussName);
//	                            bussName = data.bussN;
//	                            bussTypeName = data.bussT;
//	                            funcDescText = data.funcDesc;
//	                            serviceName = data.serviceName;
//	                            creatorFullName = data.fullName;
//	                        }
//	                    },
//	                    failure: function(result, request) {}
//	                });
//	            	Ext.create('Ext.window.Window', {
//	    	            title: '基本信息',
//	    	            autoScroll: true,
//	    	            modal: true,
//	    	            closeAction: 'destroy',
//	    	            buttonAlign: 'center',
//	    	            draggable: true,
//	    	            resizable: false,
//	    	            width: 500,
//	    	            height: 328,
//	    	            loader: {
//	    	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
//	    	            	params: {
//	    	            		creatorFullName: creatorFullName,
//	    		                bussName: bussName,
//	    		                bussTypeName:bussTypeName,
//	    		                funcDescText: funcDescText,
//	    		                serviceName:serviceName
//	    	            	},
//	    	            	autoLoad: true
//	    	            },
//	    	            dockedItems: [{
//	    	                xtype: 'toolbar',
//	    	                border: false,
//	    	                dock: 'bottom',
//	    	                margin: '0 0 5 0',
//	    	                layout: {pack: 'center'},
//	    	                items: [{
//	    	                    xtype: 'button',
//	    	                    text: '关闭',
//	    	                    cls: 'Common_Btn',
//	    	                    handler: function() {
//	    	                        this.up("window").close();
//	    	                    }
//	    	                }]
//	    	            }]
//	    	        }).show();
//	            }
//	        }, {
//				text : '返回',
//				height : 30,
//				cls : 'Common_Btn',
//				handler : backFn
//			} ]
		
	});
	
	Ext.define('versionModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'long'
		},{
			name : 'uuid',
			type : 'string'
		},  {
			name : 'bussId',
			type : 'long'
		}, {
			name : 'bussTypeId',
			type : 'long'
		}, {
			name : 'createTime',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		}, {
			name : 'onlyVersion',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		} ]
	});

	  versionStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'versionModel',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceVersionList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	versionStore.on('beforeload', function(store, options) {
		var new_params = {
			serviceId : serviceId,
			flag : flag
		};

		Ext.apply(versionStore.proxy.extraParams, new_params);
	});

	versionStore.on('load', function(store, options) {
		versionGrid.getSelectionModel().select(0, true);
	});

	var versionColumns = [ {
		text : '序号',
		xtype : 'rownumberer',
		width : 40
	}, {
		text : '服务主键',
		dataIndex : 'iid',
		width : 40,
		hidden : true
	}, {
		text : '服务uuid主键',
		dataIndex : 'uuid',
		width : 40,
		hidden : true
	}, {
		text : '创建时间',
		dataIndex : 'createTime',
		width : 150
	}, {
		text : '版本',
		dataIndex : 'onlyVersion',
		flex : 1
	} ];

	  versionGrid = Ext.create('Ext.grid.Panel', {
		width : 320,
		height : contentPanel.getHeight() - 46,
		margin: '5 5 0 0',
		store : versionStore,
		border : true,
		columnLines : true,
		columns : versionColumns,
		region : 'west',
		listeners : {
			select : function(me, record, index, eOpts) {
				iid = record.get('iid');
				uuid = record.get('uuid');
				bussId = record.get('bussId');
				bussTypeId = record.get('bussTypeId');
				serviceName = record.get('serviceName');
				mainPP.getLoader().load({
					url : 'flowCustomizedInitScriptService.do',
					params : {
						iid : iid,
						menuId: jspParms.menuId,
						serviceName : record.get('serviceName'),
						actionType : jspParms.actionType,
						bussId : record.get('bussId'),
						bussTypeId : record.get('bussTypeId'),
						ifrom: jspParms.ifrom,
						filter_serverNameQuery:filter_serverNameQuery,
						filter_scriptStateQuery:filter_scriptStateQuery,
						flag : 0
					},
					scripts : true
				});
			}
		}
	});
	
	mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "gridBasicScript_area",
		layout : {
			type : 'border'
		},
		border : false,
		height : contentPanel.getHeight(),
		items : [ versionGrid, mainPP ]
	});
	
 
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - 40);
		mainPanel.setWidth(contentPanel.getWidth());
		mainPP.setWidth(contentPanel.getWidth() / 2);
	})
	function TEST() {
		destroyRubbish(); // 销毁本页垃圾
		toFlowGraphEdit('flowCustomizedInitScriptService.do',iid,null,serviceName,bussId,bussTypeId,jspParms.menuId,'exec');
	}
	
	function backFn(){
        destroyRubbish();
        contentPanel.getLoader().load({
          url : jspParms.ifrom,
          params: {
        	  menuId: jspParms.menuId,
        	  'filter_bussId': ilter_bussId,
        	  'filter_bussTypeId': ilter_bussTypeId,
			  'filter_scriptName': ilter_scriptName,
			  'filter_serviceName': ilter_serviceName,
			  'filter_scriptType': ilter_scriptType,
			  filter_serverNameQuery:filter_serverNameQuery,
			  filter_scriptStateQuery:filter_scriptStateQuery
          },
          scripts: true
        });
        if (Ext.isIE) {
          CollectGarbage();
        }
	}
	
	function fanhui() {
		destroyRubbish(); // 销毁本页垃圾
		contentPanel.getLoader().load({
			url : 'forwardScriptServiceRelease.do',
			params: {
				
			},
			scripts : true
		});
	}
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};
	function versionRollBack() {
		var selectedRows = versionGrid.getSelectionModel().getSelection();
		if (selectedRows.length ==1) {
			var noVersionId = -1;
			var noVersionUuid = "";
			var selectedRow = selectedRows[0];
			if("无版本号"==selectedRow.data.onlyVersion) {
				Ext.Msg.alert('提示', '该版本无版本号，无法回退！');
				return;
			} else {
				var canRoll = false;
				var hasNoVersion = false;
				versionStore.each(function(record) {   
			       if(record.get('onlyVersion')=="无版本号") {
			            hasNoVersion = true;
			            noVersionId = record.get('iid');
			            noVersionUuid = record.get('uuid');
			            return;
			       }
			    });  
			    
			    versionStore.each(function(record) {   
			       if(selectedRow.data.iid!=record.get('iid')) {
		    		   if(parseFloat(selectedRow.data.onlyVersion)<parseFloat(record.get('onlyVersion'))) {
			    		   canRoll = true;
			    		   return;
				       }
		    	   }
			    }); 
			    if(hasNoVersion) {
					 Ext.MessageBox.buttonText.yes = "确定"; 
                     Ext.MessageBox.buttonText.no = "取消"; 
			       	 Ext.Msg.confirm("请确认", "是否要回退该版本，如强制回退将会覆盖现在无版本号中内容！",
                     function(button, text) {
                     if (button == "yes") {
				    	Ext.Ajax.request({
						url : 'hasVersionRollBack.do',
						method : 'POST',
						sync : true,
						params : {
							iid : iid,
							oldId : noVersionId,
							oldUuid:noVersionUuid,
							uuid:uuid
						},
						success : function(response, request) {
							var success = Ext.decode(response.responseText).success;
							if (success) {
								Ext.Msg.alert('提示', '版本回退成功！');
								var lastId = Ext.decode(response.responseText).lastId;
								versionStore.load();
							} else {
								Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
							}
						},
						failure : function(result, request) {
							secureFilterRs(result, "版本回退失败！");
						}
					});  
                     }else{
                     	return;
                     }
                      });
				
			    } else {
			    	if(canRoll) {
						Ext.Ajax.request({
							url : 'versionRollBackForFlow.do',
							method : 'POST',
							sync : true,
							params : {
								iid : iid,
								uuid:uuid
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								if (success) {
									Ext.Msg.alert('提示', '版本回退成功！');
									var lastId = Ext.decode(response.responseText).lastId;
									versionStore.load();
								} else {
									Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
								}
							},
							failure : function(result, request) {
								secureFilterRs(result, "版本回退失败！");
							}
						});
			    	} else{
						Ext.Msg.alert('提示', '选择的版本是最新版本，无法回退！');
						return;
					}
			    }
				
			}
		} else {
			return;
		}
		
	}

});