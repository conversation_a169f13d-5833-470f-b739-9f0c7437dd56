<%@page contentType="text/html; charset=utf-8"%>
<% 
	long iidToolBox=Long.parseLong(request.getParameter("iid"));
%>
<html>
<head>
<script type="text/javascript">
	var iidForTaskAudiToolBox="<%=iidToolBox%>";
	var scriptLevelForTaskAudiToolBox='<%=request.getParameter("scriptLevel")%>';
	var checkRadioForTaskAudiToolBox = 0;
	var eachNumForAToolBox = <%=request.getAttribute("eachNum")%>;
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageToolBox.js"></script>
</head>
<body>
	<div id="taskAuditingPageToolBox_area" style="width: 100%; height: 25%;"></div>
</body>
</html>
