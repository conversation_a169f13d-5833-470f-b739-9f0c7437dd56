<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%> 
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<% 
	long iid=Long.parseLong(request.getParameter("iid"));
	boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	boolean logAnalizeSwitch =  Environment.getInstance().getScriptLogAnalizeSwitch();
	String configNum =  Environment.getInstance().getConfigNum();
	boolean taskApplyUploadAttachmentSwitch =  Environment.getInstance().geTaskApplyUploadAttachmentSwitch();
	boolean taskApplyCheckSysAdminSwitch = Environment.getInstance().geTaskApplyCheckSysAdminSwitch();
	boolean scriptTaskApplyShowTimeoutSwitch = Environment.getInstance().getScriptTaskApplyShowTimeoutSwitch();
	boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
	boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
	boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
	boolean templateSwich = Environment.getInstance().getTempUploadTabSwitch();
	//邮储  执行描述框placeholder属性=“请输入事件单号或变更单号或执行原因”
	boolean descPlaceholderSwitch = Environment.getInstance().getDescPlaceholderSwitch();
	//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
	boolean firstUserSelected = Environment.getInstance().getSelectedFirstUserSwitch();
	// 邮储 任务申请-任务申请页面/常用任务配置页面，参数默认值字段变为不可编辑
	boolean paramEditDisable = Environment.getInstance().psbcParamDisEditSwitch();
	// 邮储 agent导入excel只有IP列
	boolean taskApplyForYCSwitch = Environment.getInstance().getScriputTaskapplyYcAddAgentSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
	//单号输入框是否展示开关
	boolean itsmOrderNumberSwitch = Environment.getInstance().getItsmOrderNumShowSwitch();
	//邮储   任务申请-增加服务器不自动加载数据开关
	boolean enableAutoLoadSwitch = Environment.getInstance().getScriptAutoloadAgentSwitch();
    //上海宝蓝德启动用户校验
    String sysdeptauditRole = Environment.getInstance().getSysdeptauditRole();
	// 是否查询变更业务系统绑定的agent
	boolean agentFromChangeSys = Environment.getInstance().getScriptTaskApplySysAgentSwitch();
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
	boolean messageNoticeCheckboxSwitch = Environment.getInstance().getMessageNoticeCheckboxSwitch();
%>
<html>
<head>
<script type="text/javascript">
	var sysdeptauditRole = '<%=sysdeptauditRole%>';
	var labelEdit="<%=request.getAttribute("labels")%>";
	var iidForTaskAudi="<%=iid%>";
	var number = "<%=configNum%>";
	var scriptLevelForTaskAudi='<%=request.getParameter("scriptLevel")%>';
	var serviceNameForTaskAudi='<%=request.getParameter("serviceName")%>';
	var scriptTypeForTaskAudi='<%=request.getParameter("scriptType")%>';
	//北京邮储 超时时间获取
	var timeoutForTaskAudi='<%=request.getParameter("timeout")%>';
	var sessionIdForTaskAuditing = '<%=request.getSession().getId()%>';
	//适用平台
	var scriptPlatmFrom = '<%=request.getParameter("platmFrom")%>'; 
	var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;
	var checkRadioForTaskAudi = 0;
	var eachNumForA = <%=request.getAttribute("eachNum")%>;
	var loginUser = '<%=request.getAttribute("loginUser")%>';
	var scriptuuid = '<%=request.getAttribute("scriptuuid")%>';
	var suUser = '<%=request.getAttribute("suUser")%>';
	var ssTimerTaskSwitch = <%=request.getAttribute("ssTimerTaskSwitch")%>;
	var execUserSwitch = <%=execUserSwitch%>;
	var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
	var CMDBFlag = <%=request.getAttribute("CMDBflag")%>;
	var sysAdminFlag = <%=request.getAttribute("sysAdminFlag")%>;
	var cmdbFlag;
	if(CMDBFlag){
		cmdbFlag = false;
	}else{
		cmdbFlag = true;
	}
	//corn表达式公共方法使用的参数 
	var iworkItemid = 0;
	var agent_store_chosed = Ext.create('Ext.data.Store', {
	     model: 'page.dubbo.scriptService.spdb.agent.agentModel',
	     proxy: {
	     },
	     autoLoad: false
	 });
	var chosedAgentIds = new Array();
	var chosedCpIds = new Array();
	var chosedAgentFlagArray = new Array();//判断是否Windows和非Windows一起选择
	var chosedAgentWinForSPDB = new Ext.window.Window();
    var agentColumnsForSPDB = [];
    var agentListStore = undefined
   var butterflyVerison2 = new Ext.form.TextField({});
   var listComBox2 = Ext.create ('Ext.form.ComboBox',{});
   var sendEmailUser = Ext.create ('Ext.form.ComboBox',{ hidden:!taskApplyForSPDBSwitch});
   var  checkInumber=true;
   var logAnalizeSwitch = <%=logAnalizeSwitch%>;
   var taskApplyUploadAttachmentSwitch =<%=taskApplyUploadAttachmentSwitch%>;
   var taskApplyCheckSysAdminSwitch = <%=taskApplyCheckSysAdminSwitch%>;
   var scriptTaskApplyShowTimeoutSwitch = <%=scriptTaskApplyShowTimeoutSwitch%>;
	//白名单checkBox等开关
	var equipSwitch = '<%=request.getAttribute("equipSwitch")%>';
	//获取uuid
	var searchUUID = '<%=request.getAttribute("searchUUID")%>';
	//任务管理批量查询开关
	var batchQuerySwitch = "<%=batchQuery%>";
	//按照ip查询还是按照计算机名查询开关（true为山东城商需求，按照ip查询；false为bankCode001需求，按照计算机名查询）
	var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
	//模板tab页展示开关
	var templateSwitch = <%=templateSwich%>;
	//邮储  执行描述框placeholder属性=“请输入事件单号或变更单号或执行原因”
	var descPlaceholderSwitch = <%=descPlaceholderSwitch%>;
	//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
	var firstUserSelected = <%=firstUserSelected%>;
	// 邮储 任务申请-任务申请页面/常用任务配置页面，参数默认值字段变为不可编辑
	var paramEditDisable = <%=paramEditDisable%>;
	var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>;
	var taskApplyForYCSwitch = <%=taskApplyForYCSwitch%>;
	//单号输入框是否展示开关
	var itsmOrderNumberSwitch = <%=itsmOrderNumberSwitch%>;
	//邮储   任务申请-增加服务器不自动加载数据开关
	var enableAutoLoadSwitch = <%=enableAutoLoadSwitch%>
	var scriptService = '<%=request.getParameter("scriptService")==null?"":request.getParameter("scriptService")%>';
	var params = '<%=request.getParameter("params")==null?"":request.getParameter("params")%>';
	var invoke = '<%=request.getParameter("invoke")==null?"":request.getParameter("invoke")%>';
	var invokeId = '<%=request.getParameter("invokeId")==null?"":request.getParameter("invokeId")%>';
	//是否具有高权（判断是否为值班任务申请、值班人员）
	var heightPermissionFlag=<%=request.getAttribute("heightPermissionFlag") == null ? false : request.getAttribute("heightPermissionFlag")%>
	//agent是否来自绑定的变更业务系统
	var agentFromChangeSys = <%=agentFromChangeSys%>
	var fjnxCISwitch = <%=fjnxCISwitch%>
	var messageNoticeCheckboxSwitch = <%=messageNoticeCheckboxSwitch%>
</script>
	<%
		if(Environment.getInstance().getScriptTaskApplyAddAgentSwitch()) {
	%>
	<script    async type="text/javascript"
			   src="<%=request.getContextPath()%>/page/dubbo/basicScript/agentModelAndColumnForSPDB.js"  ></script>
	<script    type="text/javascript"
			   src="<%=request.getContextPath()%>/page/dubbo/basicScript/queryAgentInfoForSPDB.js" ></script>
	<%
		}
	%>
<script    type="text/javascript"
		   src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageIPSearch.js"></script>
<script    type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPage.js"></script>
</head>
<body>
	<input type="hidden" id="taskAuditingPageExecUserNameText" />
	<div id="taskAuditingPage_area" style="width: 100%; height: 25%;"></div>

</body>
</html>
