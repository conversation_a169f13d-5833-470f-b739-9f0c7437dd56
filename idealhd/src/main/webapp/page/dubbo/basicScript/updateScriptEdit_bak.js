var onlyToday = true;
var mainPanel;
var iframPanel;
var sysID;
var busID;
Ext
		.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();

			Ext.tip.QuickTipManager.init();

			var bussData = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'bsName' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			
			var bussTypeData = Ext.create('Ext.data.Store', {
				fields : [ 'sysTypeId', 'sysType' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			var bussCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'sysName',
				labelWidth : 80,
				columnWidth : .49,
				queryMode : 'local',
				fieldLabel : '一级分类',
				padding : '0 5 0 0',
				displayField : 'bsName',
				valueField : 'iid',
				editable : false,
				queryMode : 'local',
				emptyText : '--请选择一级分类--',
				store : bussData,
				listeners : {
					change : function() { // old is keyup
						bussTypeCb.clearValue();
						bussTypeCb.applyEmptyText();
						bussTypeCb.getPicker().getSelectionModel()
								.doMultiSelect([], false);
						bussTypeData.load({
							params : {
								fk : this.value
							}
						});
					}
				}
			});

			/** 工程类型下拉框* */
			var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'bussType',
				padding : '0 5 0 0',
				labelWidth : 80,
				columnWidth : .49,
				queryMode : 'local',
				fieldLabel : '二级分类',
				displayField : 'sysType',
				valueField : 'sysTypeId',
				editable : false,
				emptyText : '--请选择二级分类--',
				store : bussTypeData
			});
			
			bussData.on('load', function(store, options) {
				bussCb.setValue(sysID);
				bussTypeData.load({
					params : {
						fk : sysID
					}
				});
				
			});
			
			bussTypeData.on('load', function(store, options) {
				bussTypeCb.setValue(busID);
			});

			Ext.define('editScriptModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'serviceName',
					type : 'string'
				}, {
					name : 'sysName',
					type : 'string'
				}, {
					name : 'bussName',
					type : 'string'
				}, {
					name : 'scriptType',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'servicePara',
					type : 'string'
				}, {
					name : 'serviceState',
					type : 'string'
				}, {
					name : 'excepResult',
					type : 'string'
				}, {
					name : 'errExcepResult',
					type : 'string'
				}, {
					name : 'content',
					type : 'string'
				} ]
			});
			var editScriptStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 20,
				model : 'editScriptModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryOneService.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			editScriptStore.on('beforeload', function(store, options) {
				var queryparams = {
					iid : iid
				};
				Ext.apply(editScriptStore.proxy.extraParams, queryparams);
			});
			editScriptStore.on('load',
					function(store, options, success) {
						var reader = store.getProxy().getReader();
						Ext.getCmp("scriptpara").setValue(reader.jsonData.scriptPara);
						Ext.getCmp("scriptName").setValue(reader.jsonData.scriptName);
						Ext.getCmp("serviceName").setValue(reader.jsonData.serviceName);
						Ext.getCmp("excepResult").setValue(reader.jsonData.excepResult);
						Ext.getCmp("errExcepResult").setValue(reader.jsonData.errExcepResult);
						
//						Ext.getCmp("useplantform").setValue(reader.jsonData.platForm);
						usePlantForm.setValue(reader.jsonData.platForm);
						Ext.getCmp("inputparamdesc").setValue(reader.jsonData.inputParamDesc);
						Ext.getCmp("funcdesc").setValue(reader.jsonData.funcDesc);
						
						var scriptT = reader.jsonData.scriptType;
						if (scriptT == 'sh') {
							Ext.getCmp("scriptType").items.items[0]
									.setValue(true);
							checkRadio = 0;
							editor.setOption("mode", 'shell');
						} else if (scriptT == 'bat') {
							Ext.getCmp("scriptType").items.items[1]
									.setValue(true);
							checkRadio = 1;
							editor.setOption("mode", 'bat');
						} else if (scriptT == 'py') {
							Ext.getCmp("scriptType").items.items[3]
									.setValue(true);
							checkRadio = 3;
							editor.setOption("mode", 'python');
						} else if (scriptT == 'SQL') {
							Ext.getCmp("scriptType").items.items[4]
									.setValue(true);
							checkRadio = 4;
							editor.setOption("mode", 'sql');
						} else if (scriptT == 'perl') {
							Ext.getCmp("scriptType").items.items[2]
									.setValue(true);
							checkRadio = 2;
							editor.setOption("mode", 'text/x-perl');
						}
						editor.setOption('value', reader.jsonData.content);
						sysID = parseInt(reader.jsonData.sysName);
						busID = parseInt(reader.jsonData.bussName);
						bussData.load();
					});
			/** *********************Panel********************* */
			var FieldContainer = new Ext.form.RadioGroup({
				id : 'scriptType',
				fieldLabel : '脚本类型',
				labelWidth : 80,
				name : 'ra_s_type',
				padding : '0 5 10 5',
				items : [ {
					name : 'ra_s_type',
					width : 80,
					inputValue : 'sh',
					boxLabel : 'shell',
					checked : true,
					listeners : {
						click : {
							element : 'el', // bind to the
							// underlying el
							// property on
							fn : function(value) {
								if (checkRadio != 0) {
									editor.setOption("mode", 'shell');
									checkRadio = 0;
									// editor.isEmpty("");
								}
							}
						}
					}
				}, {
					name : 'ra_s_type',
					width : 80,
					inputValue : '1',
					boxLabel : 'bat',
					listeners : {
						click : {
							element : 'el', // bind to the
							// underlying el
							// property on
							fn : function(value) {
								if (checkRadio != 1) {
									checkRadio = 1;
									editor.setOption("mode", 'bat');
									// editor.isEmpty("");
								}
							}
						}
					}
				}, {
					name : 'ra_s_type',
					width : 80,
					inputValue : '2',
					boxLabel : 'perl',
					listeners : {
						click : {
							element : 'el', // bind to the
							// underlying el
							// property on
							fn : function(value) {
								checkRadio = 2;
								editor.setOption("mode", 'text/x-perl');
							}
						}
					}
				}, {
					name : 'ra_s_type',
					width : 80,
					inputValue : '3',
					boxLabel : 'python',
					listeners : {
						click : {
							element : 'el', // bind to the
							// underlying el
							// property on
							fn : function(value) {
								checkRadio = 3;
								editor.setOption("mode", 'python');
							}
						}
					}
				}
//				, {
//					name : 'ra_s_type',
//					width : 80,
//					inputValue : '4',
//					boxLabel : 'sql',
//					listeners : {
//						click : {
//							element : 'el', // bind to the
//							// underlying el
//							// property on
//							fn : function(value) {
//								checkRadio = 4;
//								editor.setOption("mode", 'text/x-mariadb');
//							}
//						}
//					}
//				}
				]
			});

			var sName = new Ext.form.TextField({
				name : 'serverName',
				id : 'serviceName',
				fieldLabel : '服务名称',
				displayField : 'serverName',
				emptyText : '',
				labelWidth : 80,
				padding : '0 5 0 0',
				columnWidth : .98
			});
			var scName = new Ext.form.TextField({
				name : 'scriptName',
				id : 'scriptName',
				fieldLabel : '脚本名称',
				displayField : 'scriptName',
				emptyText : '',
				labelWidth : 80,
				padding : '0 5 0 0',
				columnWidth : .98
			});
			var pName = new Ext.form.TextField({
				name : 'scriptpara',
				id : 'scriptpara',
				fieldLabel : '脚本参数',
				displayField : 'scriptpara',
				emptyText : '',
				labelWidth : 80,
				padding : '0 5 0 0',
				columnWidth : .98
			});
//			var usePlantForm = new Ext.form.TextField({
//				name : 'useplantform',
//				id : 'useplantform',
//				fieldLabel : '适用平台',
//				displayField : 'useplantform',
//				emptyText : '',
//				labelWidth : 80,
//				padding : '0 5 0 0',
//				columnWidth : .98
//			});
			var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
				name : 'useplantform',
				padding : '0 5 0 0',
				labelWidth : 80,
				columnWidth : .98,
				queryMode : 'local',
				fieldLabel : '适用平台',
				displayField : 'text',
				valueField : 'value',
				editable : false,
				emptyText : '--请选择平台--',
				store :  new Ext.data.SimpleStore({  
                    fields : ['value', 'text'],  
                    data : [['Windows', 'Windows'],  
                            ['Linux', 'Linux'],  
                            ['Unix', 'Unix'],  
                            ['Linux/Unix', 'Linux/Unix']]  
                })
			});
			var excepResult = new Ext.form.TextField({
				name : 'excepResult',
				id : 'excepResult',
				fieldLabel : '预期结果',
				displayField : 'excepResult',
				emptyText : '',
				labelWidth : 80,
				padding : '0 5 0 0',
				columnWidth : .49
			});
			var errExcepResult = new Ext.form.TextField({
				name : 'errExcepResult',
				id : 'errExcepResult',
				fieldLabel : '预期异常结果',
				displayField : 'errExcepResult',
				emptyText : '',
				labelWidth : 85,
				padding : '0 5 0 0',
				columnWidth : .49
			});
			var inputParamDesc = Ext.create ('Ext.form.field.TextArea', {
				name : 'inputparamdesc',
				id: 'inputparamdesc',
				fieldLabel : '入参说明',
				displayField : 'inputparamdesc',
				emptyText : '',
				labelWidth : 80,
				padding : '0 5 0 0',
				columnWidth : .98,
				height: (contentPanel.getHeight()-320)/2,
			    autoScroll : true
			});
			var funcDesc = Ext.create ('Ext.form.field.TextArea', {
				name : 'funcdesc',
				id:'funcdesc',
				fieldLabel : '功能概述',
				displayField : 'funcdesc',
				emptyText : '',
				labelWidth : 80,
				padding : '0 5 0 0',
				columnWidth : .98,
				height: (contentPanel.getHeight()-280)/2,
				autoScroll : true
			});
			var scriptForm = new Ext.form.FormPanel({
				region: 'center',
				width : contentPanel.getWidth()/2,
				border : false,
				layout : 'anchor',
				// margin : '5',
				items : [ {
					border : false,
					layout : 'column',
					margin : '5',
					items : [ bussCb, bussTypeCb ]
				}, {
					border : false,
					margin : '5',
					layout : 'column',
					items : [ sName ]
				},{
					border : false,
					margin : '5',
					layout : 'column',
					items : [ scName ]
				}, {
					border : false,
					margin : '5',
					layout : 'column',
					items : [ pName ]
				},{
					layout : 'column',
					border : false,
					margin : '5',
					items : [ usePlantForm ]
				}, {
					layout : 'column',
					border : false,
					margin : '5',
					items : [ excepResult,errExcepResult ]
				}, {
					layout : 'column',
					border : false,
					margin : '5',
					items : [ inputParamDesc ]
				}, {
					layout : 'column',
					border : false,
					margin : '5',
					items : [ funcDesc ]
				}  ]
			});

			var mainP = Ext
					.create(
							'Ext.panel.Panel',
							{
								border : false,
								region: 'east',
								width : contentPanel.getWidth()/2,
								// height : contentPanel.getHeight()-120,
								height : contentPanel.getHeight(),
								html : '<textarea id="code" value style="width: 100%;height:100%;"></textarea>',
								items : [ FieldContainer ]
							});
			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "gridBasicScript_area",
				layout: {
		            type: 'border'
		        },
				border : false,
				height : contentPanel.getHeight() - 40,
				items : [ scriptForm, mainP ],
				buttonAlign : 'center',
				buttons : [ {
					text : '保存',
					height : 30,
					cls : 'Blue_button',
					handler : save
				}, {
					text : '测试',
					height : 30,
					cls : 'Blue_button',
					handler : TEST
				}, {
					text : '返回',
					height : 30,
					cls : 'Red_button',
					handler : fanhui
				} ]
			});
			editor = CodeMirror.fromTextArea(document.getElementById('code'), {
				mode : 'shell',
				lineNumbers : true,
				matchBrackets : true
			});
			editor.setSize(contentPanel.getWidth() * 0.49, contentPanel
					.getHeight()-130);
			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - 40);
				mainPanel.setWidth(contentPanel.getWidth());
				scriptForm.setWidth(contentPanel.getWidth()/2);
				mainP.setWidth(contentPanel.getWidth()/2);
				editor.setSize(contentPanel.getWidth() * 0.49, contentPanel
						.getHeight()-130);
				inputParamDesc.setHeight((contentPanel.getHeight()-280)/2);
				funcDesc.setHeight((contentPanel.getHeight()-280)/2);
			})
			function TEST() {
				destroyRubbish(); // 销毁本页垃圾
				contentPanel.getLoader().load(
						{
							url : 'scriptExecStart.do?serviceId=' + iid
									+ '&flag=0' + '&url=forwardEditScript.do',
							scripts : true
						});
			}
			function fanhui() {
				destroyRubbish(); // 销毁本页垃圾
				contentPanel.getLoader().load({
					url : 'forwardScriptServiceRelease.do',
					scripts : true
				});
			}
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};
			function save() {
				// var sysName = scriptForm.getForm().findField("sysName")
				// .getRawValue();
				// var bussType = scriptForm.getForm().findField("bussType")
				// .getRawValue();
				var sysId = bussCb.getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
				var bussTypeId = bussTypeCb.getValue(); // scriptForm.getForm().findField("bussType").getRawValue();
				var sysName = bussCb.getRawValue(); // scriptForm.getForm().findField("sysName").getRawValue();
				var bussType = bussTypeCb.getRawValue();
				var serverName = scriptForm.getForm().findField('serverName')
						.getValue();
				var scriptName = scriptForm.getForm().findField('scriptName')
						.getValue();
				var scriptpara = scriptForm.getForm().findField('scriptpara')
						.getValue();
				var errExcepResult = scriptForm.getForm().findField('errExcepResult').getValue();
				var excepResult = scriptForm.getForm().findField('excepResult').getValue();
				var up = scriptForm.getForm().findField('useplantform').getValue();
				var ipd = scriptForm.getForm().findField('inputparamdesc').getValue();
				var fd = scriptForm.getForm().findField('funcdesc').getValue();
				if (!sysId) {
					Ext.MessageBox.alert("提示", "请选择一级分类!");
					return;
				}
				if (!bussTypeId) {
					Ext.MessageBox.alert("提示", "请选择二级分类!");
					return;
				}
				if (serverName.trim() == '') {
					Ext.MessageBox.alert("提示", "服务名称不能为空!");
					return;
				}
				if (scriptName.trim() == '') {
					Ext.MessageBox.alert("提示", "脚本名称不能为空!");
					return;
				}
				if (up.trim() == '') {
					Ext.MessageBox.alert("提示", "使用平台不能为空!");
					return;
				}
				if (ipd.trim() == '') {
					Ext.MessageBox.alert("提示", "入参说明不能为空!");
					return;
				}
				if (fd.trim() == '') {
					Ext.MessageBox.alert("提示", "功能概述不能为空!");
					return;
				}

				editor.save();
				var content = document.getElementById('code').value;
				// mainPanel.body.dom.innerHTML;
				var type = "sh";
				if (checkRadio == '0') {
					type = "sh";
				} else if (checkRadio == '1') {
					type = "bat";
				} else if (checkRadio == '2') {
					type = "perl";
				} else if (checkRadio == '3') {
					type = "py";
				} else if (checkRadio == '4') {
					type = "SQL";
				}
				Ext.Ajax
						.request({
							url : 'updateScriptEdit.do',
							method : 'POST',
							sync : true,
							params : {
								iid : iid,
								sysName : sysName,
								bussType : bussType,
								sysId : sysId,
								bussTypeId : bussTypeId,
								serverName : serverName,// 系统名称 系统分类
								scriptName : scriptName,
								scriptpara : scriptpara,
								excepResult:excepResult,
								errExcepResult:errExcepResult,
								usePlantForm : up,
								inputParamDesc : ipd,
								funcDesc : fd,
								checkRadio : type,
								content : content
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								if (success) {
									Ext.Msg.alert('提示', '保存成功！');
								} else {
									Ext.Msg
											.alert(
													'提示',
													Ext
															.decode(response.responseText).message);
								}
							},
							failure : function(result, request) {
								secureFilterRs(result, "保存失败！");
							}
						});
			}

		});