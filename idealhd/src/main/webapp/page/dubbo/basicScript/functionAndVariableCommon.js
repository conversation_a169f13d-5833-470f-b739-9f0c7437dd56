function showFunction(iid){
    Ext.Ajax.request({
        url: 'scriptService/getScriptFunctioneditorData.do',
        method: 'POST',
        params: {
            iid: [iid]
        },
        success: function (response) {
            var data = Ext.decode(response.responseText).data[0]
            var functionName = data.iname;
            var idesc = null;
            idesc = new Ext.Panel( {
                cls:"explain_cn",
                xtype: 'label',
                html: '<div id="descVal" style="width: 850px;tword-wrap: break-word;tword-break: break-all;">· 说明：' + data.idesc + ' </div>'
            });
            var example = null;
            example = new Ext.Panel( {
                cls:"explain_cn",
                xtype: 'label',
                html: '<div id="descVal" style="width: 850px;tword-wrap: break-word;tword-break: break-all;">· 用例：</div>'
            });

            var exampleTextArea = null;

            exampleTextArea = Ext.create('Ext.form.field.TextArea', {
                fieldLabel: '',
                // afterLabelTextTpl: required,
                displayField: 'funcExampleWin',
                // emptyText: '请输入功能说明...',
                labelWidth: 70,
                height: 515,
                readOnly:true,
                width:'864px',
                columnWidth: .99,
                autoScroll: true
            });
            exampleTextArea.setValue(data.functionExample);

            var showAllUserWin = Ext.create('widget.window', {
                title: '使用说明',
                border:false,
                closable: true,
                closeAction: 'hide',
                modal: true,
                cls:"instruction_box",
                width: 900,
                height: 780,
                draggable: false,
                layout: {
                    type: 'border',
                    padding: 0
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    border: false,
                    // baseCls: 'customize_gray_back',
                    dock: 'top',
                    cls:"primary_title",
                    height:25,
                    items: [functionName],
                },{
                    xtype: 'toolbar',
                    border: false,
                    cls:"explain_cn",
                    // baseCls: 'customize_gray_back',
                    dock: 'top',
                    items: [idesc]
                },{
                    xtype: 'toolbar',
                    border: false,
                    cls:"explain_cn",
                    // baseCls: 'customize_gray_back',
                    dock: 'top',
                    items: [example]
                },{
                    xtype: 'toolbar',
                    border: false,
                    cls:"describe_box",
                    // baseCls: 'customize_gray_back',
                    dock: 'top',
                    height:522,
                    items: [exampleTextArea]
                }]
            });
            showAllUserWin.show();


        },
        failure: function () {
            Ext.Msg.alert('提示', '网络连接失败');
        }
    })


}

function showVariable(name, desc, type){
    if (type == '1') {
        type = "自定义变量";
    } else if (type == '2') {
        type = "内置变量";
    }

    var itype = null;
    itype = new Ext.Panel( {
        cls:"explain_cn",
        xtype: 'label',
        html: '<div id="descVal" style="width: 850px;tword-wrap: break-word;tword-break: break-all;">· 属性：'+ type +'</div>'
    });

    var idesc = null;
    idesc = new Ext.Panel( {
        cls:"explain_cn",
        xtype: 'label',
        html: '<div id="descVal" style="width: 850px;tword-wrap: break-word;tword-break: break-all;">· 描述：</div>'
    });

    var descTextArea = null;

    descTextArea = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '',
        // afterLabelTextTpl: required,
        displayField: 'funcExampleWin',
        // emptyText: '请输入功能说明...',
        labelWidth: 70,
        height: 280,
        readOnly:true,
        width:'864px',
        columnWidth: .99,
        autoScroll: true
    });
    descTextArea.setValue(desc);

    var variableWin = Ext.create('Ext.window.Window', {
        title: '使用说明',
        border:false,
        closable: true,
        closeAction: 'hide',
        modal: true,
        cls:"instruction_box",
        width: 900,
        height: 500,
        draggable: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            // baseCls: 'customize_gray_back',
            dock: 'top',
            cls:"primary_title",
            height:25,
            items: [name],
        },{
            xtype: 'toolbar',
            border: false,
            cls:"explain_cn",
            // baseCls: 'customize_gray_back',
            dock: 'top',
            items: [itype]
        },{
            xtype: 'toolbar',
            border: false,
            cls:"explain_cn",
            //baseCls: 'customize_gray_back',
            dock: 'top',
            items: [idesc]
        },{
            xtype: 'toolbar',
            border: false,
            cls:"describe_box",
            // baseCls: 'customize_gray_back',
            dock: 'top',
            height:287,
            items: [descTextArea]
        }]
    }).show()
}

/**
 * 语法校验失败错误展示框
 * @param func 功能标识 涉及到脚本开发-脚本编写页面 、 测试历史编辑页面
 * @param syntaxInfo
 * @param ignoreFlag
 * @param callback
 * @param callType
 */
function showScriptShellSyntaxWin(syntaxInfo, ignoreFlag, callback, callType){
    var descTextArea = null;

    descTextArea = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '',
        // afterLabelTextTpl: required,
        labelWidth: 70,
        height: 370,
        readOnly:true,
        width:'864px',
        columnWidth: .99,
        autoScroll: true
    });
    descTextArea.setValue(syntaxInfo);

    var syntaxWin = Ext.create('Ext.window.Window', {
        title: '语法检查失败',
        border:false,
        modal: true,
        cls:"instruction_box",
        width: 900,
        height: 500,
        draggable: false,
        items:[descTextArea],
        closable:false,
        buttonAlign:'center',
        buttons:[
            {
                xtype:'button',
                cls:'Common_Btn',
                text:'跳过',
                handler:function (){
                    syntaxWin.hide();
                    return true;
                }
            },
            {
                xtype:'button',
                cls:'Common_Btn',
                text:'去修改',
                handler:function (){
                    syntaxWin.hide();
                    return false;
                }
            }
        ]
    }).show()
}