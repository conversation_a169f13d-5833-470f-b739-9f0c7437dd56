<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script>
var fromType = <%=request.getAttribute("fromType") %>;
	$('body').off('click', '#return').on('click', '#return', function(){
		var url = "";
		if(fromType==3) {
			url = 'forwardScriptShare.do';
		}else if(fromType==2) {
			url = 'forwardScriptMyToolBox.do';
		}
		contentPanel.getLoader().load({url: url, scripts: true});
	});
</script>

</head>
<body>
<div class="viewBase_all">
<table cellpadding="0" cellspacing="0" border="0" height="100%" align="center">
	<tr>
		<td height="100%">
			<div class="base_sc_border">
				<table cellpadding="0" cellspacing="0" border="0" class="base_table">
					<tr>
						<td class="base_td1">一级分类：</td>
						<td><%=request.getAttribute("bussName") %></td>
					</tr>
					<tr>
						<td class="base_td1">二级分类：</td>
						<td><%=request.getAttribute("bussTypeName") %></td>
					</tr>
					<tr>
						<td class="base_td1">服务名称：</td>
						<td><%=request.getAttribute("serviceName") %></td>
					</tr>
					<tr>
						<td class="base_td1">脚本名称：</td>
						<td><%=request.getAttribute("scriptName") %></td>
					</tr>
					<tr>
						<td class="base_td1">适用平台：</td>
						<td><%=request.getAttribute("platForm") %></td>
					</tr>
					<tr>
						<td class="base_td1">入参说明：</td>
						<td><%=request.getAttribute("inputParamDesc") %></td>
					</tr>
					<tr>
						<td class="base_td1">功能概述：</td>
						<td><%=request.getAttribute("funcDesc") %></td>
					</tr>
					<tr>
						<td class="base_td1">脚本类型：</td>
						<td><%=request.getAttribute("scriptType") %></td>
					</tr>
					<tr>
						<td class="base_td1">脚本内容：</td>
						<td><%=request.getAttribute("content") %></td>
					</tr>
					<% if(null!=request.getAttribute("hideReturnBtn")&&"1".equals(request.getAttribute("hideReturnBtn"))) { %>

					<% } else { %>
						<tr>
							<td colspan="2" align="center">
								<button id="return" class="Blue_button base_button">返回</button>
							</td>
						</tr>
					<% } %>
				</table>
			</div>
		</td>
	</tr>
</table>
</div>
</body>
</html>