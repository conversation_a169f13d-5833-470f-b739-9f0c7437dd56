Ext.onReady(function() {
	var sysID;
	var busID;
	var DetailWinTiToolBox;
    // 清理主面板的各种监听时间
//    destroyRubbish();
    
    var uploadProcessWin; 
    var attachmentIds = [];

    Ext.tip.QuickTipManager.init();

    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        queryMode: 'local',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    bussData.on('load', function(store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load', function(store, options) {
        bussTypeCb.setValue(busID);
    });

    Ext.define('editScriptModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'excepResult',
            type: 'string'
        },
        {
            name: 'errExcepResult',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        }]
    });
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
            iid: iidForTaskAudiToolBox,
            fromType: 1 // 查ieai_script_services表
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
        /*var reader = store.getProxy().getReader();
        scName.setValue(reader.jsonData.scriptName);
        sName.setValue(reader.jsonData.serviceName);
        excepResult.setValue(reader.jsonData.excepResult);
        errExcepResult.setValue(reader.jsonData.errExcepResult);

        usePlantForm.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);

        var scriptT = reader.jsonData.scriptType;
        if (scriptT == 'sh') {
        	FieldContainer.items.items[0].setValue(true);
            checkRadioForTaskAudiToolBox = 0;
            editor.setOption("mode", 'shell');
        } else if (scriptT == 'bat') {
        	FieldContainer.items.items[1].setValue(true);
            checkRadioForTaskAudiToolBox = 1;
            editor.setOption("mode", 'bat');
        } else if (scriptT == 'py') {
        	FieldContainer.items.items[3].setValue(true);
            checkRadioForTaskAudiToolBox = 3;
            editor.setOption("mode", 'python');
        } else if (scriptT == 'SQL') {
        	FieldContainer.items.items[4].setValue(true);
            checkRadioForTaskAudiToolBox = 4;
            editor.setOption("mode", 'sql');
        } else if (scriptT == 'perl') {
        	FieldContainer.items.items[2].setValue(true);
            checkRadioForTaskAudiToolBox = 2;
            editor.setOption("mode", 'text/x-perl');
        }
        editor.setOption('value', reader.jsonData.content);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        bussData.load();*/
    });
    /** *********************Panel********************* */
    /*var FieldContainer = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 80,
        name: 'ra_s_type_1',
        items: [{
            name: 'ra_s_type_1',
            width: 80,
            inputValue: 'sh',
            boxLabel: 'shell',
            checked: true,
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        if (checkRadioForTaskAudiToolBox != 0) {
                            editor.setOption("mode", 'shell');
                            checkRadioForTaskAudiToolBox = 0;
                        }
                    }
                }
            }
        },
        {
            name: 'ra_s_type_1',
            width: 80,
            inputValue: '1',
            boxLabel: 'bat',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        if (checkRadioForTaskAudiToolBox != 1) {
                            checkRadioForTaskAudiToolBox = 1;
                            editor.setOption("mode", 'bat');
                        }
                    }
                }
            }
        },
        {
            name: 'ra_s_type_1',
            width: 80,
            inputValue: '2',
            boxLabel: 'perl',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForTaskAudiToolBox = 2;
                        editor.setOption("mode", 'text/x-perl');
                    }
                }
            }
        },
        {
            name: 'ra_s_type_1',
            width: 80,
            inputValue: '3',
            boxLabel: 'python',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForTaskAudiToolBox = 3;
                        editor.setOption("mode", 'python');
                    }
                }
            }
        }]
    });*/

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        fieldLabel: '功能概述',
        displayField: 'funcdesc',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1,
        height: 130,
        autoScroll: true
    });
    var scriptForm = Ext.create('Ext.form.Panel', {
        width: '20%',
        height: 230,
        border: true,
        layout: 'anchor',
        title: '基本信息',
        items: [{
            border: false,
            layout: 'column',
            margin: '5',
            items: [bussCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [bussTypeCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [sName]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [scName]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [errExcepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDesc]
        }]
    });

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: iidForTaskAudiToolBox
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 60,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '参数值',
        dataIndex: 'paramDefaultValue',
        width: 70,
        editor: {
            allowBlank: true
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    
    var paramGrid = Ext.create('Ext.grid.Panel', {
    	title: "参数",
        width: '50%',
//        height: 280,
//        margin: 10,
//        collapsible : true,
        store: paramStore,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: iidForTaskAudiToolBox,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    
    var attachmentColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }/*,
    {
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    }*/];
    
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	title: "附件",
        width: '50%',
//        height: contentPanel.getHeight()-362,
//        margin: 10,
        store: attachmentStore,
        border: true,
        columnLines: true,
        columns: attachmentColumns
    });
    
    var paramsAndAttachmentsPanel = Ext.create('Ext.panel.Panel', {
         collapsible : false,
        border: false,
//        title: "参数与附件",
        height: 350,
        layout: {
            type: 'hbox',
//            padding:'5',
            align:'stretch'
        },
        items: [paramGrid, attachmentGrid]
    });

    /*var mainP = Ext.create('Ext.panel.Panel', {
        width: '33.7%',
        border: false,
        title: "脚本内容",
        // height : contentPanel.getHeight()-120,
        height: contentPanel.getHeight()-200,
        tbar:[FieldContainer],
        html: '<textarea id="codeEditView-for-auditing" value style="width: 100%;height:100%;"></textarea>'
    });*/
    
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	/*resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  */
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    labelAlign : 'right',
			    labelWidth : 58,
			    width : '92.3%',
	            columnWidth:1,
			    multiSelect: true,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
		    	listeners: {
	    	      change: function( comb, newValue, oldValue, eOpts ) {
	    	    	  agent_store.load();
	    	      }
		    	}
	});
	
	var app_name = new Ext.form.TextField({
		name : 'appname',
		fieldLabel : '应用名称',
		displayField : 'appname',
		emptyText : '--请输入应用名称--',
		labelWidth : 58,
		hidden : true,
		labelAlign : 'right',
		width : '20%'
	});
	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'AgentIp',
		displayField : 'agentip',
		emptyText : '--请输入agentip--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '23%'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '主机名称',
		displayField : 'hostname',
		emptyText : '--请输入主机名称--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '23%'
	});
	var sys_name = new Ext.form.TextField({
		name : 'sysname',
		fieldLabel : '系统名称',
		displayField : 'sysname',
		emptyText : '--请输入系统名称--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '23%'
	});
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '系统类型',
		displayField : 'ostype',
		emptyText : '--系统类型--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '23%'
	});
	
	var search_ip_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [ sys_name, app_name, agent_ip, host_name, os_type,
					{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function(){
							agent_store.load();
						}
					}
			]
		},
		{
			xtype : 'toolbar',
			dock : 'top',
			items : [ resourceGroupObj,
					{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '清空',
						handler : function(){
							agent_ip.setValue(''),
					    	app_name.setValue(''),
							sys_name.setValue(''),
							host_name.setValue(''),
							os_type.setValue(''),
					    	resourceGroupObj.setValue('')
						}
					}
			]
		}]
	});
    
	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
    
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var agent_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: '系统名称',  dataIndex: 'sysName',width:160},
                        { text: 'IP',  dataIndex: 'agentIp',width:110},
                        { text: '主机名称',  dataIndex: 'hostName',width:150},
                        { text: '系统类型',  dataIndex: 'osType',width:110},
                        { text: '端口号',  dataIndex: 'agentPort',width:60},
                        { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,
                        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }
                        }
		               ];
    
    agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp : agent_ip.getValue(),
	    	appName : app_name.getValue(),
			sysName : sys_name.getValue(),
			hostName : host_name.getValue(),
			osType : os_type.getValue(),
	    	rgIds:resourceGroupObj.getValue(),
	    	flag: 1
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    
    var agent_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
	    store:agent_store,
	    border:false,
	    columnLines : true,
//	    rowLines: false,
	    columns:agent_columns,
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})
	});
    
    var execDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '',
        columnWidth: 1,
        height: 112,
        autoScroll: true
    });
    
    var execDescForm = Ext.create('Ext.form.Panel', {
        width: '100%',
        height: 150,
        border: false,
        collapsible : false,
        title: '执行描述',
        items: [{
            layout: 'column',
            border: false,
//            margin: '5',
            items: [execDesc]
        }]
    });
    
    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
    	title: "选择服务器",
        border: true,
        layout : 'border',
        height: 350,
        items: [search_ip_form, agent_grid]
    });
    
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	var auditorStore_tap = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecAuditorList.do?scriptLevel='+scriptLevelForTaskAudiToolBox,
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
	    editable: false,
	    fieldLabel: "审核人",
	    store: auditorStore_tap,
	    queryMode: 'local',
	    width: "23%",
	    displayField: 'fullName',
	    valueField: 'loginName',
	    labelWidth : 58,
		labelAlign : 'right',
	  });
	
	var execUser = new Ext.form.TextField({
        name: 'execUser',
        fieldLabel: '执行用户',
        emptyText: '',
        labelWidth : 58,
		labelAlign : 'right',
        width: "23%"
    });
	
	var taskName = new Ext.form.TextField({
		name: 'taskName',
		fieldLabel: '任务命名',
		emptyText: '',
		labelWidth : 58,
		labelAlign : 'right',
		width: "23%"
	});
	
	var eachNum = new Ext.form.NumberField({
		name: 'eachNum',
		fieldLabel: '并发数量',
		labelWidth : 58,
		labelAlign : 'right',
		minValue:0,
		value:'',
		width: "23%"
	});
	
	function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
	
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "taskAuditingPageToolBox_area",
        layout: {
            type: 'vbox',
            align : 'stretch'
        },
        border: false,
        autoScroll: true,
        height: contentPanel.getHeight()-38,
        items: [chooseAgentPanel, paramsAndAttachmentsPanel, execDescForm/*, scriptForm, mainP*/],
        buttons: [
        	{
                text: '提交',
                height: 30,
                cls: 'Common_Btn',
                handler: function(){
                	paramStore.sort('paramOrder', 'ASC');
                	var m = paramStore.getRange(0, paramStore.getCount()-1);
                    var jsonData = "[";
                    for (var i = 0, len = m.length; i < len; i++) {
                        var n = 0;
                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                        var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
                        if ("" == paramType) {
                            setMessage('参数类型不能为空！');
                            return;
                        }
                        if (fucCheckLength(paramDesc) > 250) {
                            setMessage('参数描述不能超过250字符！');
                            return;
                        }

                        if (paramType == 'int') {
                            if (!checkIsInteger(paramDefaultValue)) {
                                setMessage('参数类型为int，但参数默认值不是int类型！');
                                return;
                            }
                        }
                        if (paramType == 'float') {
                            if (!checkIsDouble(paramDefaultValue)) {
                                setMessage('参数类型为float，但参数默认值不是float类型！');
                                return;
                            }
                        }
                        var ss = Ext.JSON.encode(m[i].data);
                        if (i == 0) jsonData = jsonData + ss;
                        else jsonData = jsonData + "," + ss;
                    }

                    jsonData = jsonData + "]";
                    
                    var agents = [];
                    var records = agent_grid.getSelectionModel().getSelection();
	  				if(records.length>0) {
	  					for(var i = 0, len = records.length; i < len; i++){
	  						agents.push(records[i].get('iid'));
	  					}
	  				} else {
	  					Ext.Msg.alert('提示', "请选择服务器！");
                        return;
	  				}
	  				
	  				var rgIds = resourceGroupObj.getValue();
	  				
	  				var auditor = auditorComBox_tap.getValue();
	  				if(!auditor) {
	  					Ext.Msg.alert('提示', "没有选择审核人！");
	  					return;
	  				}
	  				
	  				var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
	  				if(Ext.isEmpty(execDescForExec)) {
	  					Ext.Msg.alert('提示', "没有填写执行描述！");
	  					return;
	  				}
	  				var taskN = Ext.util.Format.trim(taskName.getValue());
	  				if(Ext.isEmpty(taskN)) {
	  					Ext.Msg.alert('提示', "任务命名不能为空！");
	  					return;
	  				}
	  				
	  				if (fucCheckLength(taskN) > 255) {
                        setMessage('任务命名不能超过255字符！');
                        return;
                    }
	  				
	  				var en = eachNum.getValue();
	  				if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
	  					if(en>eachNumForAToolBox) {
	  						setMessage('并发数量不能超过'+eachNumForAToolBox);
	                        return;
	  					}
	  				} else {
	  					setMessage('并发数量不合法！');
                        return;
	  				}
	  				
	  				if(Ext.isEmpty(en)) {
	  					en = eachNumForAToolBox;
	  				}
	  				
	  				Ext.Ajax.request({
	  				    url : 'scriptExecAuditing.do',
	  				    method : 'POST',
	  				    params : {
	  				    	serviceId: iidForTaskAudiToolBox,
	  				    	execUser: execUser.getValue(),
	  				    	agents: agents,
	  				    	rgIds: rgIds,
	  				    	params: jsonData,
	  				    	execDesc:execDescForExec,
	  				    	auditor: auditor,
	  				    	taskName: taskN,
	  				    	eachNum: en
	  				    },
	  				    success: function(response, opts) {
	  				        var success = Ext.decode(response.responseText).success;
	  				        var message = Ext.decode(response.responseText).message;
	  				        if(!success) {
	  				        	Ext.MessageBox.alert("提示", message);
	  				        } else {
	  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	  				        }
	  				        if(typeof(auditingWinWS)!="undefined" && auditingWinWS){
	  				        	auditingWinWS.close();
	  				        }
	  				        if(typeof(auditingWinTi)!="undefined" && auditingWinTi){
	  				        	auditingWinTi.close();
	  				        }
	  				        if(typeof(auditingWinTiToolBox)!="undefined" && auditingWinTiToolBox) {
	  				        	auditingWinTiToolBox.close();
	  				        }
	  				    },
	  				    failure: function(result, request) {
	  				    	secureFilterRs(result,"操作失败！");
	  				    	if(typeof(auditingWinWS)!="undefined" && auditingWinWS){
	  				    		auditingWinWS.close();
	  				        }
	  				    	if(typeof(auditingWinTi)!="undefined" && auditingWinTi){
	  				    		auditingWinTi.close();
	  				    	}
	  				        if(typeof(auditingWinTiToolBox)!="undefined" && auditingWinTiToolBox) {
	  				        	auditingWinTiToolBox.close();
	  				        }
	  				    }
	  			    });
                	
                }
            }
        ],
        buttonAlign: 'center',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [taskName, eachNum, execUser, auditorComBox_tap,{
            	text: '查看脚本',
                height: 30,
                cls: 'Common_Btn',
                handler: function(){
                	viewDetail(iidForTaskAudiToolBox)
                }
            }]
        }]
    });
    /*var editor = CodeMirror.fromTextArea(document.getElementById('codeEditView-for-auditing'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        readOnly: true
    });
    editor.setSize(contentPanel.getWidth()*0.337, contentPanel.getHeight() - 160);*/
    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
//        editor.setSize(contentPanel.getWidth()*0.337, contentPanel.getHeight() - 160);
    });
    
    
    function viewDetail(serviceId) {
//    	if(!DetailWinTiToolBox) {
    		DetailWinTiToolBox = Ext.create('widget.window', {
    			title : '详细信息',
    			closable : true,
    			closeAction : 'destroy',
    			width : contentPanel.getWidth(),
    			minWidth : 350,
    			height : contentPanel.getHeight(),
    			draggable : false,// 禁止拖动
    			resizable : false,// 禁止缩放
    			modal : true,
    			loader : {}
    		});
//    	}
    	
    	DetailWinTiToolBox.getLoader().load(
    			{
    				url : 'queryOneServiceForView.do',
    				params : {
    					iid:serviceId,
    					flag: 1,
    					hideReturnBtn: 1
    				},
    				autoLoad : true,
    				scripts : true
    			});
    	DetailWinTiToolBox.show();
    }
});