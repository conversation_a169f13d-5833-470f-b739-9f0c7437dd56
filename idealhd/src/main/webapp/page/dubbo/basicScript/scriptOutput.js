var checkItems = [];
var flexSwitch = false;
Ext.onReady(function () {
    var fields = [{
        name: 'agentIp',
        type: 'string'
    }, {
        name: 'agentPort',
        type: 'string'
    }, {
        name: 'id',
        type: 'long'
    }, {
        name: 'ip',
        type: 'string'
    }]
    var columns = [{
        text: '序号',
        width: 40,
        align: 'left',
        xtype: 'rownumberer'
    }, {
        text: '索引',             // 不能作为同一条数据的判断依据
        dataIndex: 'id',
        flex: 1,
        hidden: true
    }, {
        text: 'ip',             // 不能作为同一条数据的判断依据
        dataIndex: 'ip',
        flex: 1,
        hidden: true
    }, {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        width:120
    }, {
        text: 'Agent端口',
        dataIndex: 'agentPort',
        width:80
    }]
    if ((dimensions.length * 120 + 80 +120) < contentPanel.getWidth()){
        flexSwitch = true;
    }
    for (let i = 0; i < dimensions.length; i++) {
        fields.push({
            name: dimensions[i].ivalue,
            type: 'string'
        })
        var col = {};
        col['text']=dimensions[i].ialias;
        col['dataIndex']=dimensions[i].ivalue;
        col['width']=120;
        if (flexSwitch){
            col['flex']=1;
        }
        columns.push(col);
    }

    Ext.define('scriptOutputModel', {
        extend: 'Ext.data.Model',
        fields: fields
    })

    var showChartBtn = Ext.create('Ext.Button', {
        cls: 'Common_Btn',
        text: '图形展示',
        handler: function () {
            var datas = [];
            for (let i = 0; i < checkItems.length; i++) {
                datas.push(checkItems[i].data);
            }
            showChartWin({flowId: iflowId}, datas, queryTableName.getValue());
        }
    })

    var resetBtn = Ext.create('Ext.Button', {
        cls: 'Common_Btn',
        text: '重置',
        handler: function (){
            checkItems = [];
            queryTableName.setValue('全部');
            initGridPanel();
        }
    })

    var exportBtn = Ext.create('Ext.Button', {
        cls: 'Common_Btn',
        text: '导出',
        handler: function () {
            location.href = 'scriptOutputDataExport.do?flowId=' + iflowId+'&tableName=' + queryTableName.getValue();
        }
    })

    var goBackBtn = Ext.create('Ext.Button', {
        cls: 'Common_Btn',
        text: '返回',
        handler: function () {
            checkItems = [];
            if (type == 'testHistory') {
                contentPanel.setTitle('测试历史');
                contentPanel.getLoader().load({
                    url: "forwardscriptcoat.do",
                    scripts: true,
                    params: {
                        flowId: flowId,
                        forScriptFlow: forScriptFlow,
                        coatId: forMyScript != "" ? coatidForTestExec : '',//如果是我的脚本跳转过来后 点击返回跳回到历史页面只查询当前这条记录
                        forMyScript: forMyScript, //如果是我的脚本跳转过来后 点击返回跳回到历史页面只查询当前这条记录
                        flag: 0,
                        filter_scriptName: filter_scriptName,
                        filter_state: filter_state,
                        filter_startTime: filter_startTime,
                        filter_endTime: filter_endTime,
                        filter_serviceName: filter_serviceName,
                        filter_serviceState: filter_serviceState,
                        filter_serviceStartTime: filter_serviceStartTime,
                        filter_serviceEndTime: filter_serviceEndTime
                    }
                });
            } else if (type == 'execHistory') {
                contentPanel.setTitle('执行历史');
                contentPanel.getLoader().load({
                    url: "forwardscriptcoatforexec.do",
                    scripts: true,
                    params: {
                        flowId: flowId3ForExecForTaskExec,
                        flag: 1,
                        forScriptFlow: "",
                        filter_scriptName: filter_scriptNameForTaskExec,
                        filter_state: filter_stateForTaskExec,
                        filter_startTime: filter_startTimeForTaskExec,
                        filter_endTime: filter_endTimeForTaskExec,
                        filter_Ip: filter_IpForTaskExec,
                        filter_serviceName: filter_serviceNameForTaskExec,
                        filter_serviceCustomName: filter_serviceCustomNameForTaskExec,
                        filter_serviceState: filter_serviceStateForTaskExec,
                        filter_serviceStartTime: filter_serviceStartTimeForTaskExec,
                        filter_serviceEndTime: filter_serviceEndTimeForTaskExec
                    }
                });
            }
        }
    })

    var store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'scriptOutputModel',
        proxy: {
            url: 'getScriptOutputData.do',
            type: 'ajax',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    })

    store.on('beforeload', function () {
        Ext.apply(store.proxy.extraParams, {
            flowId: iflowId,
            tableName:queryTableName.getValue()
        })
    })

    store.on('load', function (obj, records, successful, eOpts) {
        gridPanel.getSelectionModel().select(checkItems)
    })

    var gridPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        cls: 'customize_panel_back',
        store: store,
        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        padding: grid_space,
        columnLines: true,
        autoRender:true,
        // forceFit: true,
        columns: columns,
        height: contentPanel.getHeight() - 120,
        width: contentPanel.getWidth(),
        listeners: {
            select: function (t, record, index) {
                if (checkItems.indexOf(record) == -1) {
                    checkItems.push(record);
                }
            },
            deselect: function (t, record, index) {
                if (checkItems.indexOf(record) > -1) {
                    checkItems.remove(record);
                }
            }
        }
    })
    var title = '脚本输出——【服务名称】:' + serviceName;
    if (type == 'testHistory') {

    } else if (type == 'execHistory') {
        title += "【任务名称】:" + taskName;
    }
    contentPanel.setTitle(title);

    var tableNameStore = Ext.create('Ext.data.Store', {
        fields:['tableName'],
        autoLoad:true,
        proxy:{
            url:'getTableNameList.do',
            type:'ajax'
        }
    })

    tableNameStore.on('beforeload', function () {
        Ext.apply(tableNameStore.proxy.extraParams, {
            flowId: iflowId
        })
    })

    var queryTableName = Ext.create('Ext.form.ComboBox', {
        fieldLabel: "表名",
        emptyText : '--请选择表名--',
        store: tableNameStore,
        queryMode: 'local',
        editable:false,
        width: "25%",
        value:'全部',
        displayField: 'tableName',
        valueField: 'tableName',
        labelWidth : 37,
    })

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: 'scriptOutput_area',
        height: contentPanel.getHeight() - 40,
        width: contentPanel.getWidth(),
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            items: [ queryTableName , {
                xtype:'button',
                cls:'Common_Btn',
                text:'查询',
                handler:function () {
                    initGridPanel();
                }
            },'->', showChartBtn, exportBtn, resetBtn, goBackBtn]
        }],
        items: [gridPanel]
    })

    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
        gridPanel.setHeight(contentPanel.getHeight() - 120);
        gridPanel.setWidth(contentPanel.getWidth());
    });

    function initGridPanel(){
        Ext.Ajax.request({
            url:'getColumnList.do',
            method:'POST',
            params:{
                flowId: iflowId,
                tableName:queryTableName.getValue()
            },
            success:function (response) {
                dimensions = Ext.decode(response.responseText).data;
                flexSwitch = false;


                fields = [{
                    name: 'agentIp',
                    type: 'string'
                }, {
                    name: 'agentPort',
                    type: 'string'
                }, {
                    name: 'id',
                    type: 'long'
                }, {
                    name: 'ip',
                    type: 'string'
                }]
                columns = [{
                    text: '序号',
                    width: 40,
                    align: 'left',
                    xtype: 'rownumberer'
                }, {
                    text: '索引',             // 不能作为同一条数据的判断依据
                    dataIndex: 'id',
                    flex: 1,
                    hidden: true
                }, {
                    text: 'ip',             // 不能作为同一条数据的判断依据
                    dataIndex: 'ip',
                    flex: 1,
                    hidden: true
                }, {
                    text: 'Agent地址',
                    dataIndex: 'agentIp',
                    width:120
                }, {
                    text: 'Agent端口',
                    dataIndex: 'agentPort',
                    width:80
                }]
                if ((dimensions.length * 120 + 80 +120) < contentPanel.getWidth()){
                    flexSwitch = true;
                }
                for (let i = 0; i < dimensions.length; i++) {
                    fields.push({
                        name: dimensions[i].ivalue,
                        type: 'string'
                    })
                    var col = {};
                    col['text']=dimensions[i].ialias;
                    col['dataIndex']=dimensions[i].ivalue;
                    col['width']=120;
                    if (flexSwitch){
                        col['flex']=1;
                    }
                    columns.push(col);
                }

                gridPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
                    region: 'center',
                    cls: 'customize_panel_back',
                    store: store,
                    selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
                    ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                    padding: grid_space,
                    columnLines: true,
                    autoRender:true,
                    // forceFit: true,
                    columns: columns,
                    height: contentPanel.getHeight() - 120,
                    width: contentPanel.getWidth(),
                    listeners: {
                        select: function (t, record, index) {
                            if (checkItems.indexOf(record) == -1) {
                                checkItems.push(record);
                            }
                        },
                        deselect: function (t, record, index) {
                            if (checkItems.indexOf(record) > -1) {
                                checkItems.remove(record);
                            }
                        }
                    }
                })
                mainPanel.removeAll(true);
                mainPanel.add(gridPanel);

                Ext.define('scriptOutputModel', {
                    extend: 'Ext.data.Model',
                    fields: fields
                })
                gridPanel.ipage.moveFirst();
            },
            failure:function () {
                Ext.Msg.alert('提示', '网络连接失败');
            }
        })
    }
})
