Ext.onReady(function() {
    destroyRubbish();
    let editor;
    let required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    let upldWin;
    let upLoadformPane = '';
    let scriptTypeCodeMap = new Map([
        ['shell',0],
        ['bat',1],
        ['perl',2],
        ['python',3],
        ['powershell',6],
    ]);
    let checkRadioForBasicScriptEdit=0;
    Ext.define('scriptTemplateModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'ITYPE',
            type: 'string'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'ICREATEUSER',
            type: 'string'
        },
        {
            name: 'ISCRIPTTYPE',
            type: 'string'
        }]
    });

    let scriptTemplateStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'scriptTemplateModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/getScriptEditTemplateList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    let templateTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "灾备"
        },{
            "name": "巡检"
        },{
            "name": "采集"
        }]
    });


    let scriptTemplateColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
        {
            text: 'ID',
            dataIndex: 'IID',
            width: 40,
            hidden: true
        },
        {
            text: '模版类型',
            dataIndex: 'ITYPE',
            flex: 1
        },
        {
            text: '模版名称',
            dataIndex: 'INAME',
            flex: 1,
         },
        {
            text: '脚本类型',
            dataIndex: 'ISCRIPTTYPE',
            width: 150,
            renderer:function(value){
                let backValue = "";
                if (value === "shell") {
                    backValue = "shell";
                } else if (value === "perl") {
                    backValue = "perl";
                } else if (value === "python") {
                    backValue = "python";
                } else if (value === "bat") {
                    backValue = "bat";
                }/* else if (value === "sql") {
                    backValue = "sql";
                }*/else if (value === "powershell") {
                    backValue = "powershell";
                }
                return backValue;
            }
        },
        {
            text: '创建人',
            dataIndex: 'ICREATEUSER',
            width: 150,
        },{
            text : '操作',
            xtype : 'actiontextcolumn',
            width : 90,
            flex:1,
            items : [{
                text : '编辑',
                iconCls : 'script_edit',
                handler : function(grid, rowIndex) {
                    let iid =  grid.getStore().data.items[rowIndex].data.IID;
                    let itype =  grid.getStore().data.items[rowIndex].data.ITYPE;
                    let scriptType =  grid.getStore().data.items[rowIndex].data.ISCRIPTTYPE;
                    let scriptTemplateName =  grid.getStore().data.items[rowIndex].data.INAME;
                    codeWin(iid,scriptType,itype,scriptTemplateName);
                }}]
        }];

    let form = Ext.create('Ext.form.FormPanel', {
        region: 'north',
        padding : '5 0 5 0',
        bodyCls : 'x-docked-noborder-top',
        border : false,
        dockedItems : [ {
            xtype : 'toolbar',
            baseCls:'customize_gray_back',
            border : false,
            dock : 'top',
            items : [ '->',{
                text: '增加',
                cls: 'Common_Btn',
                 handler: function () {
                     codeWin();
                 }
            },{
                    itemId: 'delete',
                    text: '删除',
                    cls: 'Common_Btn',
                    //iconCls:'sc_delete',
                    disabled: true,
                    handler: deleteScriptTemplate
               }]
        } ]
    });

    let scriptTemplateGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: scriptTemplateStore,
        cls:'customize_panel_back',
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        padding : grid_space,

        border:true,
        columnLines: true,
        columns: scriptTemplateColumns,
        animCollapse: false
    });

    scriptTemplateGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });

    let mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptEditTemplateMainPage",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,scriptTemplateGrid]
    });

    function codeWin(iid,scriptT, typeV,scriptTemplateName) {
        let content;
        let updatecheckRadio;
        checkRadioForBasicScriptEdit = scriptTypeCodeMap.get(scriptT);
        if(iid){
            Ext.Ajax.request({
                url : 'scriptService/getScriptEditTemplateRecord.do',
                method : 'POST',
                async:false,
                params : {
                    iid : iid,
                },
                success : function(response, request) {
                    content = Ext.decode(response.responseText).content;
                }
            });
        }
        let FieldContainer_win = new Ext.form.RadioGroup({
            fieldLabel: '脚本类型',
            labelWidth: 60,
            labelCls :Ext.baseCSSPrefix + 'form-item-label label_space',
            name: 'ra_s_type_win',
            items: [{
                name: 'ra_s_type_win',
                width: 60,
                inputValue: '0',
                boxLabel: 'shell',
                checked:true,
                listeners: {
                    click: {
                        element: 'el',
                        fn: function(value) {
                            if (checkRadioForBasicScriptEdit !== 0) {
                                editor.setOption("mode", 'shell');
                                checkRadioForBasicScriptEdit = 0;
                            }
                        }
                    }
                }
            },
                {
                    name: 'ra_s_type_win',
                    width: 50,
                    inputValue: '1',
                    boxLabel: 'bat',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function(value) {
                                if (checkRadioForBasicScriptEdit !== 1) {
                                    checkRadioForBasicScriptEdit = 1;
                                    editor.setOption("mode", 'bat');
                                }
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type_win',
                    width: 50,
                    inputValue: '2',
                    boxLabel: 'perl',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function(value) {
                                checkRadioForBasicScriptEdit = 2;
                                editor.setOption("mode", 'text/x-perl');
                            }
                        }
                    }
                },
                {
                    name: 'ra_s_type_win',
                    width: 65,
                    inputValue: '3',
                    boxLabel: 'python',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function(value) {
                                checkRadioForBasicScriptEdit = 3;
                                editor.setOption("mode", 'python');
                            }
                        }
                    }
                },/*{
                    name: 'ra_s_type_win',
                    width: 40,
                    inputValue: '4',
                    boxLabel: 'sql',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function(value) {


                                editor.setOption("mode", 'text/x-plsql');
                                checkRadioForBasicScriptEdit = 4;
                            }
                        }
                    }
                }, */{
                    name: 'ra_s_type_win',
                    width: 90,
                    inputValue: '6',
                    boxLabel: 'powershell',
                    listeners: {
                        click: {
                            element: 'el',
                            fn: function(value) {
                                checkRadioForBasicScriptEdit = 6;
                                editor.setOption("mode", 'powershell');
                            }
                        }
                    }
                }]
        });

        let　templateType = Ext.create('Ext.form.ComboBox', {
            fieldLabel: "模版类型",
            labelWidth: 93,
            editable: false,
            afterLabelTextTpl: required,
            labelAlign:'right',
            store: templateTypeStore,
            name:"templateType",
            queryMode: 'local',
            columnWidth:0.98,
            margin : '10 0 0 0',
            displayField: 'name',
            valueField: 'name',
            value: typeV
        });
        let templateName = new Ext.form.TextField({
            name : 'templateName',
            fieldLabel : '模版名称',
            afterLabelTextTpl: required,
            emptyText : '--请输入模版名称--',
            columnWidth:0.98,
            labelWidth : 93,
            margin : '10 10 0 0',
            labelAlign : 'right',
            value:scriptTemplateName
        });
        let codePanel = Ext.create('Ext.panel.Panel', {
            minHeight: 80,
            border: false,
            autoScroll: true,
            region:'center',
            cls:'customize_panel_back',
            height: contentPanel.getHeight(),
            html: '<textarea id="codeTemplate" style="height:100%;" placeholder="请输入脚本代码..."></textarea>',
            tbar: [FieldContainer_win,'->',templateType,templateName]
        });

        let	codePanelWin = Ext.create('widget.window', {
            title: '编辑模版',
            closable: true,
            closeAction: 'destroy',
            modal: true,
            width : contentPanel.getWidth()-120,
            height :contentPanel.getHeight() - modelHeigth,
            items: [codePanel],
            dockedItems : [{
                xtype : 'toolbar',
//			    baseCls:'customize_gray_back',
                dock : 'bottom',
                layout: {pack: 'center'},
                items : [{
                    xtype: "button",
                    cls:'Common_Btn',
                    text: "导入文件",
                    handler: importButton
                },{
                    xtype: "button",
                    cls:'Common_Btn',
                    text: "保存",
                    handler: function (){
                        if(templateType.getValue()==='' || templateType.getValue()===null || templateType.getValue() === undefined){
                            Ext.Msg.alert('提示',"请选择模版类型!");
                            return;
                        }
                        if(templateName.getValue()==='' || templateName.getValue()===null || templateName.getValue() === undefined){
                            Ext.Msg.alert('提示',"请输入模版名称!");
                            return;
                        }

                        if(templateName.getValue().length>255){
                            Ext.Msg.alert('提示',"模版名称不能大于255个字符!");
                            return;
                        }
                        let content = editor.getDoc().getValue();
                        if(content==='' || content===null || content === undefined){
                            Ext.Msg.alert('提示',"模版内容不能为空！");
                            return;
                        }
                        let scriptType = "shell";
                        if (checkRadioForBasicScriptEdit === 0) {
                            scriptType = "shell";
                        } else if (checkRadioForBasicScriptEdit ===1) {
                            scriptType = "bat";
                        } else if (checkRadioForBasicScriptEdit === 2) {
                            scriptType = "perl";
                        } else if (checkRadioForBasicScriptEdit === 3) {
                            scriptType = "python";
                        }/*else if (checkRadioForBasicScriptEdit === 4) {
                            scriptType = "sql";
                        }*/else if (checkRadioForBasicScriptEdit === 6) {
                            scriptType = "powershell";
                        }

                        Ext.Ajax.request({
                            url: 'addIeaiScriptEditTemplate.do',
                            params:{
                                iname:templateName.getValue(),
                                iscripttype:scriptType,
                                itype:templateType.getValue(),
                                icontent:content,
                                iid:iid !==undefined && iid > 0 ?iid:0
                            },
                            method: 'POST',
                            success: function(response, opts) {
                                let success = Ext.decode(response.responseText).success;
                                let message = Ext.decode(response.responseText).message;
                                if (success) {
                                    scriptTemplateStore.reload();
                                    codePanelWin.close();
                                    Ext.Msg.alert('提示', message);
                                }else{
                                    Ext.Msg.alert('提示', "保存失败！");
                                }
                            },
                            failure: function(result, request) {
                                secureFilterRs(result, "操作失败！");
                            }
                        });
                    }
                }
                ]
            }],
            listeners:{
                'close':function(){
                    templateType.setValue();
                    templateName.setValue();
                }
            }
        });


        codePanelWin.show();
        editor = CodeMirror.fromTextArea(document.getElementById('codeTemplate'), {
            mode: 'shell',
            theme: "lesser-dark", // 主题
            keyMap: "sublime", // 快键键风格
            extraKeys: {
                "Ctrl-Q": "autocomplete",
                "Ctrl-D":"deleteLine"
            },
            lineNumbers: true, // 显示行号
            smartIndent: true, // 智能缩进
            indentUnit: 4, // 智能缩进单位为4个空格长度
            indentWithTabs: true, // 使用制表符进行智能缩进
            lineWrapping: true, //
            // 在行槽中添加行号显示器、折叠器、语法检测器
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
            foldGutter: true, // 启用行槽中的代码折叠
            autofocus: true, // 自动聚焦
            matchBrackets: true, // 匹配结束符号，比如"]、}"
            autoCloseBrackets: true, // 自动闭合符号
            styleActiveLine: true // 显示选中行的样式
//        ,extraKeys: {
//            "F11": function(cm) {
//              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
//            },
//            "Esc": function(cm) {
//              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
//            }
//          }
        });
        editor.setOption('value', content);
        if (scriptT === 'shell') {
            FieldContainer_win.items.items[0].setValue(true);
            updatecheckRadio = 0;
            editor.setOption("mode", 'shell');
        } else if (scriptT === 'bat') {
            FieldContainer_win.items.items[1].setValue(true);
            updatecheckRadio = 1;
            editor.setOption("mode", 'bat');
        } else if (scriptT === 'python') {
            FieldContainer_win.items.items[3].setValue(true);
            updatecheckRadio = 3;
            editor.setOption("mode", 'python');
        }/* else if (scriptT === 'sql') {
            FieldContainer_win.items.items[4].setValue(true);
            updatecheckRadio = 4;
            editor.setOption("mode", 'text/x-plsql');
        }*/ else if (scriptT === 'perl') {
            FieldContainer_win.items.items[2].setValue(true);
            updatecheckRadio = 2;
            editor.setOption("mode", 'text/x-perl');
        } else if (scriptT === 'powershell') {
            FieldContainer_win.items.items[4].setValue(true);
            updatecheckRadio = 6;
            editor.setOption("mode", 'powershell');
        }
        editor.setSize(codePanel.getWidth()-2, codePanel.getHeight()-100 );
    }

    function importButton(){
        //销毁win窗口
        if(!(null===upldWin || undefined===upldWin || ''===upldWin)){
            upldWin.destroy();
            upldWin = null;
        }

        if(!(null===upLoadformPane || undefined===upLoadformPane || ''===upLoadformPane)){
            upLoadformPane.destroy();
            upLoadformPane = null;
        }
        //导入文件Panel
        upLoadformPane =Ext.create('Ext.form.Panel', {
            width:370,
            height:120,
            frame: true,
            items: [
                {
                    xtype: 'filefield',
                    name: 'scriptfile', // 设置该文件上传空间的name，也就是请求参数的名字
                    labelAlign:'right',
                    fieldLabel: '选择文件',
                    labelWidth: 80,
                    msgTarget: 'side',
                    anchor: '100%',
                    buttonText: '浏览...',
                    width:370
                }
            ],
            buttonAlign: 'left',
            buttons: ['->',
                {
                    id:'upldBtnIdAudi',
                    text: '导入文件',
                    handler: function() {
                        let form = this.up('form').getForm();
                        let upfile=form.findField("scriptfile").getValue();
                        if(upfile===''){
                            Ext.Msg.alert('提示',"请选择文件...");
                            return ;
                        }
                        if (form.isValid()) {
                            Ext.MessageBox.wait("数据处理中...", "进度条");
                            form.submit({
                                url: 'ajaxImportScript.do',
                                success: function(form, action) {
                                    let result = Ext.JSON.decode(action.response.responseText);
                                    let errMsg = result.errMsg;
                                    let scriptContent = result.scriptContent;
                                    if(!errMsg){
                                        editor.getDoc().setValue(scriptContent);
                                        editor.refresh();
                                    }
                                    upldWin.close();
                                    Ext.Msg.alert('提示', "脚本内容已导入，请保存！");
                                },
                                failure: function(form, action) {
                                    secureFilterRsFrom(form, action);
                                }
                            });
                        }
                    }
                }
            ]
        });
        //导入窗口
        upldWin = Ext.create('Ext.window.Window', {
            title: '脚本模版文件导入',
            width: 400,
            height: 200,
            modal:true,
            resizable: false,
            closeAction: 'destroy',
            items:  [upLoadformPane]
        }).show();
        upldWin.on("beforeshow",function(self, eOpts){
            let form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
            form.reset();
        });

        upldWin.on("destroy",function(self, eOpts){
            upLoadformPane.destroy();
        });
    }


    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };


    function deleteScriptTemplate() {
        let data = scriptTemplateGrid.getView().getSelectionModel().getSelection();
        if (data.length === 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
         } else {
            Ext.Msg.confirm("请确认", "是否真的要删除模版？",
                function(button ) {
                    if (button === "yes") {
                        let ids = [];
                        Ext.Array.each(data,
                            function(record) {
                                let iid = record.get('IID');
                                // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                                if (iid) {
                                    ids.push(iid);
                                }else{
                                    scriptTemplateStore.remove(record);
                                }
                            });
                        if(ids.length>0){
                            Ext.Ajax.request({
                                url: 'deleteScriptTemplate.do',
                                params: {
                                    deleteIds: ids.join(',')
                                },
                                method: 'POST',
                                success: function(response, opts) {
                                    let success = Ext.decode(response.responseText).success;
                                    // 当后台数据同步成功时
                                    if (success) {
                                        scriptTemplateStore.reload();
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                    } else {
                                        Ext.Msg.alert('提示', '删除失败！');
                                    }
                                },
                                failure: function(result, request) {
                                    secureFilterRs(result, "操作失败！");
                                }
                            });
                        }else{
                            scriptTemplateGrid.getView().refresh();
                        }

                    }
                });
        }
    }


    /** 窗口尺寸调节* */
    contentPanel.on('resize',
        function() {
            editor.getDoc().clearHistory();
            mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
            mainPanel.setWidth (contentPanel.getWidth () );
        });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function(obj, options, eOpts) {
            Ext.destroy(scriptTemplateGrid);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });
});