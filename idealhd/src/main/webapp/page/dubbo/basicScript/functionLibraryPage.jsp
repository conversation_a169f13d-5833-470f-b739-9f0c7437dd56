<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment" %>
<html>
<head>
    <%
        boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
        boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
        boolean scriptFunctionAllowEditSwitch = Environment.getInstance().getScriptFunctionAllowEditSwitch();
        //分类目录
        boolean JlnxScriptClassSwitch = Environment.getInstance().getJlnxScriptClassSwitch();
        boolean scriptEditBookSwitch = Environment.getInstance().getScriptEditBookSwitch();
    %>
    <script>
        //按照ip查询还是按照计算机名查询开关（true为山东城商需求，按照ip查询；false为bankCode001需求，按照计算机名查询）
        var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
        //任务管理批量查询开关
        var batchQuerySwitch = "<%=batchQuery%>";
        //内置函数允许编辑
        var scriptFunctionAllowEditSwitch = <%=scriptFunctionAllowEditSwitch%>;
        var JlnxScriptClassSwitch=<%=JlnxScriptClassSwitch%>;
        var classId1 = <%=request.getParameter("classId")%>;
        var scriptEditBookSwitch = <%=scriptEditBookSwitch%>;
    </script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/functionLibraryPage.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageIPSearch.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/setScope.js"></script>
</head>
<body>
<div id="functionLibraryPage_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>