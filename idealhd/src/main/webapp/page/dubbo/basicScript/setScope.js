var checkAgent = [];
var delAgent = [];
// var delAgentCache = [];
var checkGroup = [];
var delGroup = [];
// var delGroupCache = [];
var checkUser = [];
var delUser = [];
var checkUserGroup = [];
var delUserGroup = [];
var scopeGroupStore;
var scopeAgentStore;
var scopeUserStore;
var scopeUserGroupStore;
var publishAuditingSMWin;
var pubDesc_sm_value = '';
var clearData;
var clearUserData;
var activeTab;
var tabNum;
agent_grid_url = 'setScope/getAllAgentIpSearch.do';

function getActiveScope(iid, type, batchUpdate) {
    if (iid.length > 0 && iid[0] == undefined){
        Ext.Msg.alert('提示', '请先保存数据，在设置作用域');
        return;
    }

    Ext.Ajax.request({
        url: 'setScope/activeTab.do',
        params: {
            iid: iid,
            type: type
        },
        success: function (response) {
            activeTab = Ext.decode(response.responseText).activeTab;
            tabNum = activeTab[0];
            setScope(iid, type, batchUpdate);
        },
        failure: function () {
            Ext.Msg.alert('提示', '网络连接失败');
        }
    })
}


// 设置作用域 方法
// iid 变量库 函数库主键数组
// 批量设置作用域 iid=[1,2,3,4,5,6]
// 非批量设置    iid=[1]
// 假数据 设置作用域 需要自行校验
// type 变量库-1 函数库 功能id
// batchUpdate 批量设置作用域 true批量， false 非批量
function setScope(iid, type, batchUpdate) {
    clearData = 0;
    clearUserData = 0;
    if (batchUpdate) {
        activeTab = [];
        if (scriptEditBookSwitch) {
            tabNum = 2;
        } else {
            tabNum = 0;
        }
    }
    Ext.define('scopeAgentModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'appName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentState', type: 'int'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    Ext.define('scopeUserModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'id', type: 'string'},
            {name: 'ifullName', type: 'string'},
            {name: 'iloginName', type: 'string'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    Ext.define('scopeUserGroupModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'iname', type: 'string'},
            {name: 'ides', type: 'string'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    Ext.define('scopeGroupModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'name', type: 'string'},
            {name: 'execUserName', type: 'string'},
            {name: 'description', type: 'string'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    scopeAgentStore = Ext.create('Ext.data.Store', {
        model: 'scopeAgentModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getAgentList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    scopeUserStore = Ext.create('Ext.data.Store', {
        model: 'scopeUserModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getUserList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    scopeUserGroupStore = Ext.create('Ext.data.Store', {
        model: 'scopeUserGroupModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getUserGroupList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    scopeGroupStore = Ext.create('Ext.data.Store', {
        model: 'scopeGroupModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getAgentGroupList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var queryScopeAgent = Ext.create('Ext.form.TextField', {
        fieldLabel: '计算机名',
        emptyText: '-请输入计算机名-',
        labelWidth: 60,
        flex: 1,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    scopeAgentGrid.ipage.moveFirst();
                }
            }
        }
    })

    var queryScopeSysName = Ext.create('Ext.form.TextField', {
        fieldLabel: '操作系统',
        emptyText: '-请输入操作系统-',
        labelWidth: 60,
        flex: 1,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    scopeAgentGrid.ipage.moveFirst();
                }
            }
        }
    })

    var queryScopeIp = Ext.create('Ext.form.TextField', {
        fieldLabel: 'agentIp',
        emptyText: '-请输入IP-',
        labelWidth: 60,
        flex: 1,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    scopeAgentGrid.ipage.moveFirst();
                }
            }
        }
    })

    var scopeAgentGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [queryScopeAgent, queryScopeSysName, queryScopeIp, {
                type: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    scopeAgentGrid.ipage.moveFirst();
                }
            }, {
                type: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryScopeIp.setValue('');
                    queryScopeSysName.setValue('');
                    queryScopeAgent.setValue('');
                    scopeAgentGrid.ipage.moveFirst();
                }
            }, {
                type: 'button',
                text: '删除',
                cls: 'Common_Btn',
                handler: function () {
                    var m = scopeAgentGrid.getSelectionModel().getSelection();
                    if (m.length == 0) {
                        Ext.Msg.alert('提示', '请选择要删除的数据！');
                        return;
                    } else {
                        Ext.Msg.confirm('提示', '确定删除选中的数据吗？', function (btn) {
                            if (btn == 'yes') {
                                for (let i = 0; i < m.length; i++) {
                                    // 假数据 只在增加数组里移除
                                    if (checkAgent.indexOf(m[i].data.iid) > -1) {
                                        checkAgent.remove(m[i].data.iid);
                                    } else {
                                        // 真数据 先存在删除数组
                                        delAgent.push(m[i].data.iid);
                                        // 删除的数据缓存
                                        // if (delAgentCache.indexOf(m[i].data.iid) == -1) {
                                        //     delAgentCache.push(m[i].data.iid);
                                        // }
                                    }
                                }
                                scopeAgentGrid.ipage.moveFirst();
                            } else if (btn == 'no') {
                                return;
                            }
                        })
                    }
                }
            }, {
                type: 'button',
                text: '增加',
                cls: 'Common_Btn',
                handler: function () {
                    addAgent(iid, type, batchUpdate);
                }
            }]
        }],
        columns: [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'iid', hidden: true},
            {text: '作用域主键', dataIndex: 'scopeIid', hidden: true},
            {text: '名称', dataIndex: 'sysName', flex: 1},
            {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
            {text: '计算机名', dataIndex: 'hostName', flex: 1},
            {text: 'IP', dataIndex: 'agentIp', width: 150},
            {text: '端口号', dataIndex: 'agentPort', width: 100},
            {text: '操作系统', dataIndex: 'osType', width: 140}],
        store: scopeAgentStore,
        width: '100%',
        height: 550,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>',
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar'
    })

    var queryAgentGroupName = Ext.create('Ext.form.TextField', {
        fieldLabel: '组名称',
        emptyText: '-请输入组名称-',
        labelWidth: 60,
        flex: 1,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    scopeGroupGrid.ipage.moveFirst();
                }
            }
        }
    })

    var queryUserName = Ext.create('Ext.form.TextField', {
        fieldLabel: '用户名',
        emptyText: '-请输入用户名-',
        labelWidth: 60,
        flex: 1,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    scopeUserGrid.ipage.moveFirst();
                }
            }
        }
    })

    var queryUserGroupName = Ext.create('Ext.form.TextField', {
        fieldLabel: '用户组名',
        emptyText: '-请输入用户组名-',
        labelWidth: 60,
        flex: 1,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    scopeUserGroupGrid.ipage.moveFirst();
                }
            }
        }
    })

    var scopeGroupGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dockedItems: [{
            xtype: 'toolbar',
            items: [queryAgentGroupName, {
                type: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    scopeGroupGrid.ipage.moveFirst();
                }
            }, {
                type: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryAgentGroupName.setValue('');
                    scopeGroupGrid.ipage.moveFirst();
                }
            }, '->', {
                type: 'button',
                text: '删除',
                cls: 'Common_Btn',
                handler: function () {
                    var m = scopeGroupGrid.getSelectionModel().getSelection();
                    if (m.length == 0) {
                        Ext.Msg.alert('提示', '请选择要删除的数据！');
                        return;
                    } else {
                        Ext.Msg.confirm('提示', '确定删除选中的数据吗？', function (btn) {
                            if (btn == 'yes') {
                                for (let i = 0; i < m.length; i++) {
                                    // 假数据 只在增加数组里移除
                                    if (checkGroup.indexOf(m[i].data.id) > -1) {
                                        checkGroup.remove(m[i].data.id);
                                    } else {
                                        // 真数据 先存在删除数组
                                        delGroup.push(m[i].data.id);
                                        // 删除的数据缓存
                                        // if (delGroupCache.indexOf(m[i].data.iid) == -1) {
                                        //     delGroupCache.push(m[i].data.iid);
                                        // }
                                    }
                                }
                                scopeGroupGrid.ipage.moveFirst();
                            } else if (btn == 'no') {
                                return;
                            }
                        })
                    }
                }
            }, {
                type: 'button',
                text: '增加',
                cls: 'Common_Btn',
                handler: function () {
                    addGroup(iid, type, batchUpdate);
                }
            }]
        }],
        columns: [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '作用域主键', dataIndex: 'scopeIid', hidden: true},
            {text: '组名称', dataIndex: 'name', width: 160, flex: 1},
            {text: '启动用户', dataIndex: 'execUserName', width: 160, hidden: true},
            {text: '描述', dataIndex: 'description', width: 280, flex: 1}
        ],
        store: scopeGroupStore,
        width: '100%',
        height: 550,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    })

    var scopeUserGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dockedItems: [{
            xtype: 'toolbar',
            items: [queryUserName, {
                type: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    scopeUserGrid.ipage.moveFirst();
                }
            }, {
                type: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryUserName.setValue('');
                    scopeUserGrid.ipage.moveFirst();
                }
            }, '->', {
                type: 'button',
                text: '删除',
                cls: 'Common_Btn',
                handler: function () {
                    var m = scopeUserGrid.getSelectionModel().getSelection();
                    if (m.length == 0) {
                        Ext.Msg.alert('提示', '请选择要删除的数据！');
                        return;
                    } else {
                        Ext.Msg.confirm('提示', '确定删除选中的数据吗？', function (btn) {
                            if (btn == 'yes') {
                                for (let i = 0; i < m.length; i++) {
                                    // 假数据 只在增加数组里移除
                                    if (checkUser.indexOf(m[i].data.id) > -1) {
                                        checkUser.remove(m[i].data.id);
                                    } else {
                                        // 真数据 先存在删除数组
                                        delUser.push(m[i].data.id);
                                        // 删除的数据缓存
                                        // if (delGroupCache.indexOf(m[i].data.iid) == -1) {
                                        //     delGroupCache.push(m[i].data.iid);
                                        // }
                                    }
                                }
                                scopeUserGrid.ipage.moveFirst();
                            } else if (btn == 'no') {
                                return;
                            }
                        })
                    }
                }
            }, {
                type: 'button',
                text: '增加',
                cls: 'Common_Btn',
                handler: function () {
                    addUser(iid, type, batchUpdate);
                }
            }]
        }],
        columns: [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '作用域主键', dataIndex: 'scopeIid', hidden: true},
            {text: '用户名', dataIndex: 'ifullName', width: 160, flex: 1},
            {text: '登录名', dataIndex: 'iloginName', width: 160, flex: 1}
        ],
        store: scopeUserStore,
        width: '100%',
        height: 550,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    })

    var scopeUserGroupGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dockedItems: [{
            xtype: 'toolbar',
            items: [queryUserGroupName, {
                type: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    scopeUserGroupGrid.ipage.moveFirst();
                }
            }, {
                type: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryUserGroupName.setValue('');
                    scopeUserGroupGrid.ipage.moveFirst();
                }
            }, '->', {
                type: 'button',
                text: '删除',
                cls: 'Common_Btn',
                handler: function () {
                    var m = scopeUserGroupGrid.getSelectionModel().getSelection();
                    if (m.length == 0) {
                        Ext.Msg.alert('提示', '请选择要删除的数据！');
                        return;
                    } else {
                        Ext.Msg.confirm('提示', '确定删除选中的数据吗？', function (btn) {
                            if (btn == 'yes') {
                                for (let i = 0; i < m.length; i++) {
                                    // 假数据 只在增加数组里移除
                                    if (checkUserGroup.indexOf(m[i].data.id) > -1) {
                                        checkUserGroup.remove(m[i].data.id);
                                    } else {
                                        // 真数据 先存在删除数组
                                        delUserGroup.push(m[i].data.id);
                                        // 删除的数据缓存
                                        // if (delGroupCache.indexOf(m[i].data.iid) == -1) {
                                        //     delGroupCache.push(m[i].data.iid);
                                        // }
                                    }
                                }
                                scopeUserGroupGrid.ipage.moveFirst();
                            } else if (btn == 'no') {
                                return;
                            }
                        })
                    }
                }
            }, {
                type: 'button',
                text: '增加',
                cls: 'Common_Btn',
                handler: function () {
                    addUserGroup(iid, type, batchUpdate);
                }
            }]
        }],
        columns: [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '作用域主键', dataIndex: 'scopeIid', hidden: true},
            {text: '用户组名', dataIndex: 'iname', width: 160, flex: 1},
            {text: '组描述', dataIndex: 'ides', width: 160, flex: 1}
        ],
        store: scopeUserGroupStore,
        width: '100%',
        height: 550,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    })

    var tabPanel = Ext.create('Ext.tab.Panel', {
        width: '100%',
        height: '100%',
        layout: 'fit',
        tabPosition: 'top',
        cls: 'normatab no_verticaltab',
        region: 'center',
        activeTab: activeTab.length > 0?activeTab[0]:(scriptEditBookSwitch?2:0),
        border: false,
        defaults:
            {
                autoScroll: false
            },
        items: [
            {
                title: activeTab.indexOf(0) > -1?'<span style="color:green;">已选服务器</span>':'已选服务器',
                cls:'',
                hidden: scriptEditBookSwitch,
                items: [scopeAgentGrid],
                listeners:
                    {
                        activate: function (tab) {
                            scopeAgentStore.load();
                            if (activeTab.indexOf(1) > -1) {
                                clearData = 1;
                            }
                            tabNum = 0;
                            checkGroup = [];
                            delGroup = [];
                        }
                    }
            },
            {
                title: activeTab.indexOf(1) > -1?'<span style="color:green;">已选资源组</span>':'已选资源组',
                items: [scopeGroupGrid],
                hidden: scriptEditBookSwitch,
                listeners:
                    {
                        activate: function (tab) {
                            scopeGroupStore.load();
                            if (activeTab.indexOf(0) > -1) {
                                clearData = 1;
                            }
                            tabNum = 1;
                            checkAgent = [];
                            delAgent = [];
                        }
                    }
            },
            {
                title: activeTab.indexOf(2) > -1?'<span style="color:green;">已选用户</span>':'已选用户',
                items: [scopeUserGrid],
                listeners:
                    {
                        activate: function (tab) {
                            scopeUserStore.load();
                            if (activeTab.indexOf(3) > -1) {
                                clearUserData = 1;
                            }
                            tabNum = 2;
                            checkUserGroup = [];
                            delUserGroup = [];
                        }
                    }
            },
            {
                title: activeTab.indexOf(3) > -1?'<span style="color:green;">已选用户组</span>':'已选用户组',
                items: [scopeUserGroupGrid],
                listeners:
                    {
                        activate: function (tab) {
                            scopeUserGroupStore.load();
                            if (activeTab.indexOf(2) > -1) {
                                clearUserData = 1;
                            }
                            tabNum = 3;
                            checkUser = [];
                            delUser = [];
                        }
                    }
            }
        ]
    })

    scopeAgentStore.on('beforeload', function () {
        Ext.apply(scopeAgentStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkAgentIids: Ext.encode(checkAgent),
            delAgentIids: Ext.encode(delAgent),
            agentIp: queryScopeIp.getValue(),
            osType: queryScopeSysName.getValue(),
            hostName: queryScopeAgent.getValue(),
            batchUpdate: batchUpdate,
            clearData: clearData
        });
    })

    scopeGroupStore.on('beforeload', function () {
        Ext.apply(scopeGroupStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkGroupIids: Ext.encode(checkGroup),
            delGroupIids: Ext.encode(delGroup),
            agentGroupName: queryAgentGroupName.getValue(),
            batchUpdate: batchUpdate,
            clearData: clearData
        })
    })

    scopeUserStore.on('beforeload', function () {
        Ext.apply(scopeUserStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkUserIids: Ext.encode(checkUser),
            delUserIids: Ext.encode(delUser),
            queryUserName: queryUserName.getValue(),
            batchUpdate: batchUpdate,
            clearUserData: clearUserData
        })
    })

    scopeUserGroupStore.on('beforeload', function () {
        Ext.apply(scopeUserGroupStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkUserGroupIids: Ext.encode(checkUserGroup),
            delUserGroupIids: Ext.encode(delUserGroup),
            queryUserGroupName: queryUserGroupName.getValue(),
            batchUpdate: batchUpdate,
            clearUserData: clearUserData
        })
    })

    scopeAgentStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = scopeAgentGrid.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(scopeAgentGrid.getView().emptyText);
                scopeAgentGrid.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });

    scopeGroupStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = scopeGroupGrid.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(scopeGroupGrid.getView().emptyText);
                scopeGroupGrid.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });

    scopeUserStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = scopeUserGrid.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(scopeUserGrid.getView().emptyText);
                scopeUserGrid.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });

    scopeUserGroupStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = scopeUserGroupGrid.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(scopeUserGroupGrid.getView().emptyText);
                scopeUserGroupGrid.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });

    var scopeWin = Ext.create('Ext.window.Window', {
        title: '选择作用域',
        height: 700,
        width: 1200,
        layout: 'border',
        border: false,
        region: 'center',
        items: [tabPanel],
        buttonAlign: 'center',
        modal: true,
        buttons: [{
            xtype: 'button',
            text: '确定',
            cls: 'Common_Btn',
            handler: function () {
                if (!batchUpdate) {
                    var isUpdate = false;
                    if (clearData == 0
                        && checkAgent.length == 0 && delAgent.length == 0
                        && checkGroup.length == 0 && delGroup.length == 0
                        && clearUserData == 0
                        && checkUser.length == 0 && delUser.length == 0
                        && checkUserGroup.length == 0 && delUserGroup.length == 0
                    ) {
                        isUpdate=true;
                    }
                    if (isUpdate) {
                        Ext.Msg.alert('提示', '未作改动');
                        return;
                    }
                }
                Ext.Ajax.request({
                    url: 'setScope/saveScope.do',
                    method: 'POST',
                    params: {
                        ids: iid,
                        type: type,
                        checkAgentIids: Ext.encode(checkAgent),
                        delAgentIids: Ext.encode(delAgent),
                        checkGroupIids: Ext.encode(checkGroup),
                        delGroupIids: Ext.encode(delGroup),
                        delUserIids: Ext.encode(delUser),
                        checkUserIids: Ext.encode(checkUser),
                        delUserGroupIids: Ext.encode(delUserGroup),
                        checkUserGroupIids: Ext.encode(checkUserGroup),
                        batchUpdate: batchUpdate,
                        clearData: clearData,
                        clearUserData: clearUserData,
                        activeTab: activeTab
                    },
                    success: function (response) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', message);
                        if (success) {
                            scopeWin.destroy();
                            // todo 设置完作用域 更新页面展示 全域有效
                            switch (type) {
                                case 1:
                                    store.load();
                                    checkItems = [];
                                    break;
                                case 2:
                                    dataGrid.ipage.moveFirst();
                                default:
                                    break;
                            }
                        }
                    },
                    failure: function () {
                        Ext.Msg.alert('提示', '网络连接异常');
                    }
                })
            }
        }, {
            xtype: 'button',
            text: '取消',
            cls: 'Common_Btn',
            handler: function () {
                scopeWin.destroy();
            }
        }],
        listeners: {
            destroy: function () {
                checkAgent = [];
                delAgent = [];
                // delAgentCache = [];
                checkGroup = [];
                delGroup = [];
                // delGroupCache = [];
                checkUser = [];
                delUser = [];

                checkUserGroup = [];
                delUserGroup = [];
            }
        }
    }).show();
}

function addAgent(iid, type, batchUpdate) {
    var checks = [];
    Ext.define('addAgentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'appName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentState', type: 'int'}
        ]
    });

    agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'addAgentModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getAddAgentList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var agentStateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "0", "name": "正常"},
            {"id": "1", "name": "异常"},
            {"id": "2", "name": "升级中"}
        ]
    });

    var queryFlag = Ext.create('Ext.form.ComboBox', {
        fieldLabel: 'agent状态',
        emptyText: '-请选择agent状态-',
        store: agentStateStore,
        displayField: 'name',
        valueField: 'id',
        labelWidth: 80,
        width: 250,
        listeners: {
            change:function (){
                agent_grid.ipage.moveFirst();
            }
        }
    })

    Ext.define('agentGroupModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'id',
                type: 'int',
                useNull: true
            },
            {
                name: 'name',
                type: 'string'
            },
            {
                name: 'description',
                type: 'string'
            }]
    });

    var agentGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'agentGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });

    var queryHostName = Ext.create('Ext.form.TextField', {
        fieldLabel: '计算机名',
        emptyText: '-请输入计算机名-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    })

    var queryOsType = Ext.create('Ext.form.TextField', {
        fieldLabel: '操作系统',
        emptyText: '-请输入操作系统-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    })

    var querySysName = Ext.create('Ext.form.TextField', {
        fieldLabel: '名称',
        emptyText: '-请输入名称-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    })

    var queryRgIds = Ext.create('Ext.form.ComboBox', {
        fieldLabel: '资源组',
        emptyText: '-请选择资源组-',
        store: agentGroupStore,
        multiSelect: true,
        displayField: 'name',
        valueField: 'id',
        labelWidth: 60,
        width: 510,
        listeners: {
            change:function (){
                agent_grid.ipage.moveFirst();
            }
        }
    })

    var queryStartIp = Ext.create('Ext.form.TextField', {
        fieldLabel: '起始ip',
        emptyText: '-请输入开始IP-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    })

    var queryEndIp = Ext.create('Ext.form.TextField', {
        fieldLabel: '终止ip',
        emptyText: '-请输入截止IP-',
        labelWidth: 80,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    })

    agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [queryHostName, queryOsType, queryStartIp, queryEndIp]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [querySysName, queryRgIds, queryFlag]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: ['->', {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    agent_grid.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryFlag.setValue('');
                    queryHostName.setValue('');
                    queryOsType.setValue('');
                    querySysName.setValue('');
                    queryRgIds.setValue('');
                    queryStartIp.setValue('');
                    queryEndIp.setValue('');
                    agent_grid.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                cls: 'Common_Btn',
                hidden: batchQuerySwitch == 'false',
                text: ipOrNameSwitch == 'true' ? 'IP批量查询' : '批量查询',
                handler: function () {
                    //开关开启使用ip查询，并匹配设备ip，筛选出未找到的ip，否则走设备名查询
                    if (ipOrNameSwitch == 'true') {
                        batchQueryForIp();
                    } else {
                        batchQuery();
                    }
                }
            }]
        }],
        columns: [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'iid', hidden: true},
            {text: '名称', dataIndex: 'sysName', flex: 1},
            {text: '计算机名', dataIndex: 'hostName', flex: 1},
            {text: 'IP', dataIndex: 'agentIp', width: 150},
            {text: '端口号', dataIndex: 'agentPort', width: 100},
            {text: '操作系统', dataIndex: 'osType', width: 140},
            {
                text: '描述', dataIndex: 'agentDesc', flex: 1,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == 0) {
                        backValue = "Agent正常";
                    } else if (value == 1) {
                        backValue = "Agent异常";
                    } else if (value == 2) {
                        backValue = "升级中";
                    } else if (value == -1) {
                        backValue = "新建";
                    }
                    return backValue;
                }
            }
        ],
        store: agent_store,
        width: '100%',
        height: 630,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        listeners: {
            select: function (t, record, index) {
                if (checks.indexOf(record.get('iid')) == -1) {
                    checks.push(record.get('iid'));
                }
                // if (delAgent.indexOf(record.get('iid')) > -1) {
                //     delAgent.remove(record.get('iid'));
                //     checks.push(record.get('iid'))
                // } else if (checkAgent.indexOf(record.get('iid')) == -1) {
                //     checkAgent.push(record.get('iid'));
                //     checks.push(record.get('iid'));
                // }
            },
            deselect: function (t, record, index) {
                if (checks.indexOf(record.get('iid')) > -1) {
                    checks.remove(record.get('iid'));
                }
                // 取消勾选 判断是否数据为真数据， 如果是真数据 取消选择 需要重新加入 delAgent
                // if (delAgentCache.indexOf(record.get('iid')) > -1) {
                //     if (delAgent.indexOf(record.get('iid'))) {
                //         delAgent.push(record.get('iid'));
                //         checks.remove(record.get('iid'));
                //     }
                // } else if (checkAgent.indexOf(record.get('iid')) > -1) {
                //     checkAgent.remove(record.get('iid'));
                //     checks.remove(record.get('iid'));
                // }
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });

    agent_store.on('beforeload', function () {
        new_params_agent = {
            ids: iid,
            type: type,
            batchUpdate: batchUpdate,
            checkAgentIids: Ext.encode(checkAgent),
            delAgentIids: Ext.encode(delAgent),
            flag: queryFlag.getValue(),
            sysName: querySysName.getValue(),
            osType: queryOsType.getValue(),
            hostName: queryHostName.getValue(),
            rgId: queryRgIds.getValue(),
            startIp: queryStartIp.getValue(),
            endIp: queryEndIp.getValue(),
            batchComputerName: ipOrNameSwitch == 'true' ? pubDesc_sm_ipsearch.getValue() : pubDesc_sm_value,
            clearData: clearData
        }
        Ext.apply(agent_store.proxy.extraParams, new_params_agent)
    })

    agent_store.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = agent_grid.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(agent_grid.getView().emptyText);
                agent_grid.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            var checkedItems = [];
            for (let i = 0; i < records.length; i++) {
                for (let j = 0; j < checks.length; j++) {
                    if (records[i].data.iid == checks[j]) {
                        checkedItems.push(records[i]);
                    }
                }
            }
            agent_grid.getSelectionModel().select(checkedItems, true, true);
        }
    });

    const addAgentWin = Ext.create('Ext.window.Window', {
        title: '增加服务器',
        height: 700,
        width: 1200,
        layout: 'border',
        border: false,
        region: 'center',
        items: [agent_grid],
        buttonAlign: 'center',
        modal: true,
        buttons: [{
            xtype: 'button',
            text: '确定',
            cls: 'Common_Btn',
            handler: function () {
                if (checks.length == 0) {
                    Ext.Msg.alert('提示', '请选择服务器');
                    return;
                } else {
                    for (let i = 0; i < checks.length; i++) {
                        if (delAgent.indexOf(checks[i]) > -1) {
                            delAgent.remove(checks[i]);
                            continue;
                        } else if (checkAgent.indexOf(checks[i]) == -1) {
                            checkAgent.push(checks[i]);
                            continue;
                        }
                    }
                    //delAgentCache = delAgent;
                    scopeAgentStore.load();
                    addAgentWin.destroy();
                }
            }
        }, {
            xtype: 'button',
            text: '取消',
            cls: 'Common_Btn',
            handler: function () {
                // for (let i = 0; i < checks.length; i++) {
                //     if (checkAgent.indexOf(checks[i]) > -1) {
                //         checkAgent.remove(checks[i]);
                //     }
                // }
                checks = [];
                scopeAgentStore.load();
                addAgentWin.destroy();
            }
        }],
        listeners: {
            destroy: function () {
                pubDesc_sm_ipsearch.setValue('');
                pubDesc_sm_value = '';
            }
        }
    }).show();
}

function addGroup(iid, type, batchUpdate) {
    var checks = [];
    Ext.define('addGroupModel', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'name', type: 'string'},
            {name: 'execUserName', type: 'string'},
            {name: 'description', type: 'string'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    const addGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'addGroupModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getAddAgentGroupList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var queryAgentGroupName = Ext.create('Ext.form.TextField', {
        fieldLabel: '组名称',
        emptyText: '-请输入组名称-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    addGroupPanel.ipage.moveFirst();
                }
            }
        }
    })

    const addGroupPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dockedItems: [{
            xtype: 'toolbar',
            items: [queryAgentGroupName, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    addGroupPanel.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryAgentGroupName.setValue('');
                    addGroupPanel.ipage.moveFirst();
                }
            }]
        }],
        columns: [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '组名称', dataIndex: 'name', width: 160},
            {text: '启动用户', dataIndex: 'execUserName', width: 160},
            {text: '描述', dataIndex: 'description', width: 280},
            {
                text: '操作',
                xtype: 'actiontextcolumn',
                flex: 1,
                align: 'left',
                items: [{
                    text: '详情',
                    iconCls: 'execute',
                    handler: function (grid, rowIndex) {
                        var iid = grid.getStore().data.items[rowIndex].data.id;
                        getAgentInfoByGroupId(iid);
                    }
                }]
            }],
        store: addGroupStore,
        width: '100%',
        height: 630,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        listeners: {
            select: function (t, record, index) {
                if (checks.indexOf(record.get('id')) == -1) {
                    checks.push(record.get('id'));
                }
                // if (delGroup.indexOf(record.get('id')) > -1) {
                //     delGroup.remove(record.get('id'));
                //     checks.push(record.get('id'))
                // } else if (checkGroup.indexOf(record.get('id')) == -1) {
                //     checkGroup.push(record.get('id'));
                //     checks.push(record.get('id'));
                // }
            },
            deselect: function (t, record, index) {
                if (checks.indexOf(record.get('id')) > -1) {
                    checks.remove(record.get('id'));
                }
                // 取消勾选 判断是否数据为真数据， 如果是真数据 取消选择 需要重新加入 delAgent
                // if (delGroupCache.indexOf(record.get('id')) > -1) {
                //     if (delGroup.indexOf(record.get('id'))) {
                //         delGroup.push(record.get('id'));
                //         checks.remove(record.get('id'));
                //     }
                // } else if (checkGroup.indexOf(record.get('id')) > -1) {
                //     checkGroup.remove(record.get('id'));
                //     checks.remove(record.get('id'));
                // }
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });

    addGroupStore.on('beforeload', function () {
        Ext.apply(addGroupStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkGroupIids: Ext.encode(checkGroup),
            delGroupIids: Ext.encode(delGroup),
            agentGroupName: queryAgentGroupName.getValue(),
            batchUpdate: batchUpdate,
            clearData: clearData
        })
    })

    addGroupStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            let flag = true;
            const treeViewDiv = addGroupPanel.body.dom.childNodes[0].childNodes;
            for (let i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                const doc = document.createRange().createContextualFragment(addGroupPanel.getView().emptyText);
                addGroupPanel.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            var checkedItems = [];
            for (let i = 0; i < records.length; i++) {
                for (let j = 0; j < checks.length; j++) {
                    if (records[i].data.id == checks[j]) {
                        checkedItems.push(records[i]);
                    }
                }
            }
            addGroupPanel.getSelectionModel().select(checkedItems);
        }
    });

    const addGroupWin = Ext.create('Ext.window.Window', {
        title: '增加资源组',
        height: 700,
        width: 1200,
        layout: 'border',
        border: false,
        region: 'center',
        items: [addGroupPanel],
        buttonAlign: 'center',
        modal: true,
        buttons: [{
            xtype: 'button',
            text: '确定',
            cls: 'Common_Btn',
            handler: function () {
                if (checks.length == 0) {
                    Ext.Msg.alert('提示', '请选择资源组');
                    return;
                } else {
                    for (let i = 0; i < checks.length; i++) {
                        // 判断是否是 真数据
                        if (delGroup.indexOf(checks[i]) > -1) {
                            delGroup.remove(checks[i]);
                            continue;
                        } else if (checkGroup.indexOf(checks[i]) == -1) {
                            checkGroup.push(checks[i]);
                            continue;
                        }
                    }
                    //delGroupCache = delGroup;
                    scopeGroupStore.load();
                    addGroupWin.destroy();
                }
            }
        }, {
            xtype: 'button',
            text: '取消',
            cls: 'Common_Btn',
            handler: function () {
                // for (let i = 0; i < checks.length; i++) {
                //     if (checkGroup.indexOf(checks[i]) > -1) {
                //         checkGroup.remove(checks[i]);
                //     }
                // }
                checks = [];
                scopeGroupStore.load();
                addGroupWin.destroy();
            }
        }],
    }).show();
}

function addUser(iid, type, batchUpdate) {
    var checks = [];
    Ext.define('addUserModel', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'ifullName', type: 'string'},
            {name: 'iloginName', type: 'string'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    const addUserStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'addUserModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getAddUserList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var queryUserName = Ext.create('Ext.form.TextField', {
        fieldLabel: '用户名',
        emptyText: '-请输入用户名-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    addUserPanel.ipage.moveFirst();
                }
            }
        }
    })

    const addUserPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dockedItems: [{
            xtype: 'toolbar',
            items: [queryUserName, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    addUserPanel.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryUserName.setValue('');
                    addUserPanel.ipage.moveFirst();
                }
            }]
        }],
        columns: [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '作用域主键', dataIndex: 'scopeIid', hidden: true},
            {text: '用户名', dataIndex: 'ifullName', width: 160, flex: 1},
            {text: '登录名', dataIndex: 'iloginName', width: 160, flex: 1}],
        store: addUserStore,
        width: '100%',
        height: 630,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        listeners: {
            select: function (t, record, index) {
                if (checks.indexOf(record.get('id')) == -1) {
                    checks.push(record.get('id'));
                }
            },
            deselect: function (t, record, index) {
                if (checks.indexOf(record.get('id')) > -1) {
                    checks.remove(record.get('id'));
                }
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });

    addUserStore.on('beforeload', function () {
        Ext.apply(addUserStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkUserIids: Ext.encode(checkUser),
            delUserIids: Ext.encode(delUser),
            queryUserName: queryUserName.getValue(),
            batchUpdate: batchUpdate,
            clearData: clearData
        })
    })

    addUserStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            let flag = true;
            const treeViewDiv = addUserPanel.body.dom.childNodes[0].childNodes;
            for (let i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                const doc = document.createRange().createContextualFragment(addUserPanel.getView().emptyText);
                addUserPanel.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            var checkedItems = [];
            for (let i = 0; i < records.length; i++) {
                for (let j = 0; j < checks.length; j++) {
                    if (records[i].data.id == checks[j]) {
                        checkedItems.push(records[i]);
                    }
                }
            }
            addUserPanel.getSelectionModel().select(checkedItems);
        }
    });

    const addUserWin = Ext.create('Ext.window.Window', {
        title: '增加用户',
        height: 700,
        width: 1200,
        layout: 'border',
        border: false,
        region: 'center',
        items: [addUserPanel],
        buttonAlign: 'center',
        modal: true,
        buttons: [{
            xtype: 'button',
            text: '确定',
            cls: 'Common_Btn',
            handler: function () {
                if (checks.length == 0) {
                    Ext.Msg.alert('提示', '请选择资源组');
                    return;
                } else {
                    for (let i = 0; i < checks.length; i++) {
                        // 判断是否是 真数据
                        if (delUser.indexOf(checks[i]) > -1) {
                            delUser.remove(checks[i]);
                            continue;
                        } else if (checkUser.indexOf(checks[i]) == -1) {
                            checkUser.push(checks[i]);
                            continue;
                        }
                    }
                    scopeUserStore.load();
                    addUserWin.destroy();
                }
            }
        }, {
            xtype: 'button',
            text: '取消',
            cls: 'Common_Btn',
            handler: function () {
                checks = [];
                scopeUserStore.load();
                addUserWin.destroy();
            }
        }],
    }).show();
}

function addUserGroup(iid, type, batchUpdate) {
    var checks = [];
    Ext.define('addUserGroupModel', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'iname', type: 'string'},
            {name: 'ides', type: 'string'},
            {name: 'scopeIid', type: 'long'}
        ]
    });

    const addUserGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'addUserGroupModel',
        proxy: {
            type: 'ajax',
            url: 'setScope/getAddUserGroupList.do',
            actionMethods: {
                create: 'POST',
                read: 'POST', // by default GET
                update: 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var queryUserGroupName = Ext.create('Ext.form.TextField', {
        fieldLabel: '用户组名',
        emptyText: '-请输入用户组名-',
        labelWidth: 60,
        width: 250,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    addUserGroupPanel.ipage.moveFirst();
                }
            }
        }
    })

    const addUserGroupPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dockedItems: [{
            xtype: 'toolbar',
            items: [queryUserGroupName, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    addUserGroupPanel.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function () {
                    queryUserGroupName.setValue('');
                    addUserGroupPanel.ipage.moveFirst();
                }
            }]
        }],
        columns: [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '作用域主键', dataIndex: 'scopeIid', hidden: true},
            {text: '用户组名', dataIndex: 'iname', width: 160, flex: 1},
            {text: '组描述', dataIndex: 'ides', width: 160, flex: 1}],
        store: addUserGroupStore,
        width: '100%',
        height: 630,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        listeners: {
            select: function (t, record, index) {
                if (checks.indexOf(record.get('id')) == -1) {
                    checks.push(record.get('id'));
                }
            },
            deselect: function (t, record, index) {
                if (checks.indexOf(record.get('id')) > -1) {
                    checks.remove(record.get('id'));
                }
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });

    addUserGroupStore.on('beforeload', function () {
        Ext.apply(addUserGroupStore.proxy.extraParams, {
            ids: iid,
            type: type,
            checkUserGroupIids: Ext.encode(checkUserGroup),
            delUserGroupIids: Ext.encode(delUserGroup),
            queryUserGroupName: queryUserGroupName.getValue(),
            batchUpdate: batchUpdate,
            clearUserData: clearUserData
        })
    })

    addUserGroupStore.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            let flag = true;
            const treeViewDiv = addUserGroupPanel.body.dom.childNodes[0].childNodes;
            for (let i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                const doc = document.createRange().createContextualFragment(addUserGroupPanel.getView().emptyText);
                addUserGroupPanel.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            var checkedItems = [];
            for (let i = 0; i < records.length; i++) {
                for (let j = 0; j < checks.length; j++) {
                    if (records[i].data.id == checks[j]) {
                        checkedItems.push(records[i]);
                    }
                }
            }
            addUserGroupPanel.getSelectionModel().select(checkedItems);
        }
    });

    const addUserGroupWin = Ext.create('Ext.window.Window', {
        title: '增加用户组',
        height: 700,
        width: 1200,
        layout: 'border',
        border: false,
        region: 'center',
        items: [addUserGroupPanel],
        buttonAlign: 'center',
        modal: true,
        buttons: [{
            xtype: 'button',
            text: '确定',
            cls: 'Common_Btn',
            handler: function () {
                if (checks.length == 0) {
                    Ext.Msg.alert('提示', '请选择资源组');
                    return;
                } else {
                    for (let i = 0; i < checks.length; i++) {
                        // 判断是否是 真数据
                        if (delUserGroup.indexOf(checks[i]) > -1) {
                            delUserGroup.remove(checks[i]);
                            continue;
                        } else if (checkUserGroup.indexOf(checks[i]) == -1) {
                            checkUserGroup.push(checks[i]);
                            continue;
                        }
                    }
                    scopeUserGroupStore.load();
                    addUserGroupWin.destroy();
                }
            }
        }, {
            xtype: 'button',
            text: '取消',
            cls: 'Common_Btn',
            handler: function () {
                checks = [];
                scopeUserGroupStore.load();
                addUserGroupWin.destroy();
            }
        }],
    }).show();
}

function getAgentInfoByGroupId(iid) {
    Ext.define('agentModelByGroup', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id', type: 'long'},
            {name: 'ip', type: 'string'},
            {name: 'port', type: 'string'},
            {name: 'hostName', type: 'string'}
        ]
    });

    var agentinfo_group_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModelByGroup',
        proxy: {
            type: 'ajax',
            url: 'agentGroup/getServersForTaskApply.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    agentinfo_group_store.on('beforeload', function (store, options) {
        var new_params = {
            groupId: iid
        };

        Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
    });
    var agentinfo_columns_group = [
        {text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'id', hidden: true},
        {text: 'Agent名称', dataIndex: 'hostName', flex: 1},
        {text: 'IP', dataIndex: 'ip', width: 160},
        {text: '端口', dataIndex: 'port', width: 160}];


    var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: agentinfo_group_store,
        border: false,
        columnLines: true,
        cls: 'customize_panel_back',
        columns: agentinfo_columns_group,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
    });
    agentinfo_group_store.load();
    var agentinfoGroupWin = Ext.create('Ext.window.Window', {
        title: '增加资源组',
        autoScroll: true,
        modal: true,
        resizable: false,
        closeAction: 'hide',
        layout: 'border',
        width: contentPanel.getWidth() - 190,
        height: contentPanel.getHeight(),
        items: [agentinfo_group_grid]
    });
    agentinfoGroupWin.show();
}

//批量添加计算机名查询弹窗
function batchQuery() {
    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: ipOrNameSwitch == 'true' ? 'IP' : '计算机名',
        labelAlign: 'top',
        emptyText: ipOrNameSwitch == 'true' ? '请输入IP，多个值请换行' : '请输入计算机名，多个值请换行',
        labelWidth: 93,
        margin: '10 0 0 0',
        //maxLength: 500,
        height: 350,
        width: 540,
        //columnWidth:.98,
        autoScroll: true
    });
    var auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'center',
        layout: 'anchor',
        bodyCls: 'x-docked-noborder-top',
        buttonAlign: 'center',
        border: false,
        items: [{
//	    	layout:'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [pubDesc_sm]
            }]
        }]
    });

    if (!publishAuditingSMWin) {
        publishAuditingSMWin = Ext.create('widget.window', {
            title: '批量查询',
            closable: true,
            closeAction: 'hide',
            modal: true,
            width: 600,
            height: 500,
            layout: {
                type: 'border',
                padding: 5
            },
            items: [auditing_form_sm],
            dockedItems: [{
                xtype: 'toolbar',
                //baseCls:'customize_gray_back',
                dock: 'bottom',
                layout: {pack: 'center'},
                items: [{
                    xtype: "button",
                    cls: 'Common_Btn',
                    text: "确定并保存",
                    handler: function () {
                        pubDesc_sm_value = pubDesc_sm.getValue();
                        agent_grid.ipage.moveFirst();
                        this.up("window").close();
                    }
                }, {
                    xtype: "button",
                    cls: 'Common_Btn',
                    text: "关闭并清空",
                    handler: function () {
                        pubDesc_sm_value = '';
                        pubDesc_sm.setValue('');
                        agent_grid.ipage.moveFirst();
                        this.up("window").close();
                    }
                }]
            }]
        });
    }
    publishAuditingSMWin.show();
    //pubDesc_sm.setValue('');
}