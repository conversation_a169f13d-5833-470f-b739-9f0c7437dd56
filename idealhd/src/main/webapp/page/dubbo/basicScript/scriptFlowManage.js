Ext.Loader.setConfig({
  enabled:true,
  disableCaching:false,
  paths:{
    'Go':'js/ux/gooo'
  }
});
var scriptServiceReleaseStoreForFlow;

Ext.onReady(function() {
	destroyRubbish();
	
	// 将通过jsp跳转传递的变量全部进行转移和清理。要注意变量的值传递可以直接清理。变量如果传递的是引用，注意不要把真实值清理掉，清理引用即可。
	var menuId = tempData.menuId;
	delete tempData;
	
	var win;
	var publishAuditingSMWin;
	var auditing_form_sm;
	var auditorStore_sm;
	var auditorComBox_sm;
	var planTime_sm;
	var pubDesc_sm;
	var scriptLevelCb_sm;
	var chosedAppSys = new Array();
	var	warnningWin;
	var warnId=0;
	var shareWin;
	var shareType=0; //共享类型  0 所有人  1 用户   2 用户组
	var chosedShareIds =[]; //共享用户、用户组  数组
	var shareGrid;
	var shareedGrid;
	var iidArray = new Array();
	var bussData = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'bsName' ],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsAll.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var bussTypeData = Ext.create('Ext.data.Store', {
		fields : [ 'sysTypeId', 'sysType' ],
		autoLoad : false,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsTypeByFk.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var bussCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'sysName',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '一级分类',
		
		displayField : 'bsName',
		valueField : 'iid',
		editable : false,
		queryMode : 'local',
		emptyText : '--请选择一级分类--',
		store : bussData,
		width : '18%',
        labelAlign : 'right',
		listeners : {
			change : function() { // old is keyup
				bussTypeCb.clearValue();
				bussTypeCb.applyEmptyText();
				bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
				bussTypeData.load({
					params : {
						fk : this.value
					}
				});
			},
			 specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
		}
	});

	/** 二级分类* */
	var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussType',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '二级分类',
		displayField : 'sysType',
		valueField : 'sysTypeId',
		editable : false,
		hidden:true,
		emptyText : '--请选择二级分类--',
		store : bussTypeData,
		width : '18%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		value : filter_serverNameQuery,
		labelWidth : 70,
		width : '17%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var scriptStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"-1", "name":"草稿"},
			{"id":"1", "name":"已上线"},
			{"id":"3", "name":"共享"},
			{"id":"2", "name":"审核中"}
		]
	});
	
	var scriptStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptStatus',
		labelWidth : 60,
		queryMode : 'local',
		fieldLabel : '作业状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择作业状态--',
		store : scriptStatusStore,
		width : '18%',
		labelAlign : 'right',
		listeners: {
			afterRender : function(combo) {
		          if(filter_scriptStateQuery=='-10000') {
								combo.setValue(scriptStatusStore.getAt(0).data.id);
							} else if(filter_scriptStateQuery=='-1'){
								combo.setValue(scriptStatusStore.getAt(1).data.id);
							}else if(filter_scriptStateQuery=='1'){
								combo.setValue(scriptStatusStore.getAt(2).data.id);
							}else if(filter_scriptStateQuery=='2'){
								combo.setValue(scriptStatusStore.getAt(3).data.id);
							}else if(filter_scriptStateQuery=='3'){
								combo.setValue(scriptStatusStore.getAt(4).data.id);
							}
		        },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	var emScriptStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-1", "name":"全部"},
			{"id":"0", "name":"否"},
			{"id":"1", "name":"是"}
		]
	});
	var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'emScript',
		labelWidth : 60,
		queryMode : 'local',
		fieldLabel : '是否应急',
		hidden : !reviewSwitch,
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择是否应急--',
		store : emScriptStore,
		width : '18%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	var appSysObj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '所属系统',
        emptyText : '--请选择所属系统--',
        labelWidth: 60,
        hidden : !reviewSwitch,
        labelAlign: 'right',
        width: '18%',
        store: appSysStore,
        padding: '0 0 5 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        mode: 'local',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
	
	  var search_form = Ext.create('Ext.form.Panel', {
		 region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	border : false,
	    	baseCls:'customize_gray_back',
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back',   
				border : false,
				dock : 'top',
				items: [sName,bussCb,bussTypeCb,scriptStatusCb,appSysObj,emScriptCb,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						pageBar.moveFirst();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
					}
				}]
			}]
		});
	
	Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'},
		    {name : 'isshare'     ,type : 'string'},
		    {name : 'isEmScript' ,type : 'string'},
		    {name : 'appSystem' ,type : 'string'}
	    ]
	});
	
	scriptServiceReleaseStoreForFlow = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'scriptService/queryServiceForMySelf.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	scriptServiceReleaseStoreForFlow.on('beforeload', function (store, options) {
	    var new_params = {  
	    		bussId:bussCb.getValue(),
	    		bussTypeId:bussTypeCb.getValue(),
	    		serviceName:sName.getValue(),
	    		scriptStatus:scriptStatusCb.getValue(),
	    		isEmScript:emScriptCb.getValue(),
	    		appId:appSysObj.getValue(),
	    		onlyScript: 0
	    };
	    
	    Ext.apply(scriptServiceReleaseStoreForFlow.proxy.extraParams, new_params);
    });
	
    
        scriptServiceReleaseStoreForFlow.addListener('load', function(me, records, successful, eOpts) {
    	// 选择后构建出的ID数组
        if (iidArray.length>0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (iidArray.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            scriptServiceReleaseGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
 
    });
    
	var scriptServiceReleaseColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		}, 
		{
		    text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
			text : '服务名称',
		    dataIndex : 'serviceName',
		    width : 200,flex:1
		},
		{
		    text : '一级分类',
		    dataIndex : 'buss',
		    width : 100
		}, 
		{
			text : '二级分类',
			dataIndex : 'bussType',
			width : 150
		},{
			text : '是否应急',
			dataIndex : 'isEmScript',
			hidden :!reviewSwitch,
			width : 75,
			renderer:function(value,p,record,rowIndex){
		    	if(value==0) {
		    		return '否';
		    	} else if (value==1) {
		    		return '<font color="#F01024">是</font>';
		    	} else {
		    		return '未知';
		    	}
		    }
		},
		{
			text : '所属系统',
			dataIndex : 'appSystem',
			hidden :!reviewSwitch,
			width : 50,
			flex:1
		},
		{
			text : '共享状态',
			dataIndex : 'isshare',
			width : 100,
			//flex:1,
			renderer:function(value,p,record,rowIndex){
		    	 if(value==0) {
		    		return '<font color="">未共享</font>';
			    } else if (value==1) {
			    		return '<font color="#0CBF47">已共享</font>';
			    } else {
			    		return '<font color="#CCCCCC">未知</font>';
			    }
		    }
		},
		{
			text : '作业状态',
			dataIndex : 'status',
			width : 100,
			renderer:function(value,p,record,rowIndex){
		    	if(value==-1) {
		    		return '<font color="#F01024">草稿</font>';
		    	} else if (value==1) {
		    		return '<font color="#0CBF47">已上线</font>';
		    	} else if (value==2) {
		    		return '<font color="#FFA602">审核中</font>';
		    	}  else if (value==9) {
		    		return '<font color="">已共享未发布</font>';
		    	} else {
		    		return '<font color="#CCCCCC">未知</font>';
		    	}
		    }
		},
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			width:305,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid'); // 其实是requestID
				var serviceName = record.get('serviceName');
				var bussId = record.get('bussId');
				var bussTypeId  =record.get('bussTypeId');
				var status = record.get('status');
				var version = record.get('version');
				var hasVersion = 0;
				if(version) {
					hasVersion = 1; // 最新版本有 版本号
				}
				
				var editFuncName = "editScriptFlowForFlow";
				var execFuncName = "testScriptFlowForFlow";
				var testFuncName = "testScriptFlowForFlow";
				var viewVersionFuncName = "viewVersionForFlowForFlow";
				
				var filter_serverNameQuery = search_form.getForm().findField("serverName").getValue()==null?'':search_form.getForm().findField("serverName").getValue();
				var filter_scriptStateQuery = search_form.getForm().findField("scriptStatus").getValue()==null?'':search_form.getForm().findField("scriptStatus").getValue();
				
				var flowCustomHy = '';
				if(status!=1) {
					flowCustomHy = '&nbsp;&nbsp;&nbsp;&nbsp;<span class="switch_span">'+
       			   	'<a href="javascript:void(0)" onclick="customScriptFlowInScriptManagerForFlow('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+hasVersion+',\''+filter_serverNameQuery+'\',\''+filter_scriptStateQuery+'\''+')">'+
       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_template"/>'+'定制模版'+
       			   	'</a>'+
       			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
				}
				
				
        		return '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+editFuncName+'('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+hasVersion+',' + menuId +',\''+filter_serverNameQuery+'\',\''+filter_scriptStateQuery+'\''+  ')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"/>'+'编辑'+
	       			   	'</a>'+
	       			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'+
	       			   
        			   '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+testFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+',' + menuId +',\''+filter_serverNameQuery+'\',\''+filter_scriptStateQuery+'\''+ ')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_test"/>'+'测试'+
	       			   	'</a>'+
	       			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'+
	       			   
		       		   '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+viewVersionFuncName+'('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+',' + menuId+',\''+filter_serverNameQuery+'\',\''+filter_scriptStateQuery+'\''+ ')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"/>'+'查看'+
	       			   	'</a>'+
	       			   '</span>' + flowCustomHy;
			}
		}
	];
	  var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: scriptServiceReleaseStoreForFlow,
		  	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    dock: 'bottom',
		    displayInfo: true,
		    emptyMsg:'找不到任何记录'
		  });
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	var selModel=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : scriptServiceReleaseStoreForFlow,
	    cls:'customize_panel_back',
	    selModel : selModel,
	    plugins: [ cellEditing ],
	    bbar : pageBar,
	    border:false,
	    columnLines : true,
	    columns : scriptServiceReleaseColumns,
	    listeners: {
		        select: function( e, record, index, eOpts ){ 
		        	if(iidArray.indexOf(record.get('iid'))==-1) {
	            		iidArray.push(record.get('iid'));
	            	}
		        },
		         deselect: function( e, record, index, eOpts ){
	            	if(iidArray.indexOf(record.get('iid'))>-1) {
	            		iidArray.remove(record.get('iid'));
	            	}
	            }
		 },
	    dockedItems : [ {
	    	xtype : 'toolbar',
	    	items : ['->',
		    	{
			        text : '共享',
			        cls : 'Common_Btn',
			        handler : shareServiceRelease
		    	},{
			        text : '发布',
			        cls : 'Common_Btn',
			        handler : function(){
			        	var seledCnt = selModel.getCount();
			    		if(seledCnt < 1){
			    			Ext.MessageBox.alert("提示", "请选择要发布的作业！");
			    			return ;
			    		}
			    		if(seledCnt > 1){
			    			Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
			    			return ;
			    		}
			    		var ss = selModel.getSelection();
			    		var version = ss[0].data.version;
						var hasVersion = 0;
						if(version) {
							hasVersion = 1; // 最新版本有 版本号
						}
						if(ss[0].get('isflow')=='1') {
							// 检查脚本组合里，是否有没有发布的脚本
							Ext.Ajax.request({
							    url : 'checkScriptFlowHasTestScript.do',
							    method : 'POST',
							    params : {
							  	  serviceId : ss[0].data.iid
							    },
							    success: function(response, opts) {
						    		var success = Ext.decode(response.responseText).success;
						    		if(success) {
						    			publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
						    		} else {
						    			var probScripts = Ext.decode(response.responseText).probScripts;
						    			var probJobs = Ext.decode(response.responseText).probJobs;
						    			var msg = "该作业中，某些步骤所选择的";
						    			var notPublishMsg = "";
						    			if(probScripts.length>0 && probJobs.length>0) {
						    				msg += "原子脚本服务或者作业";
						    				notPublishMsg += "<br>没有发布的原子脚本服务有："+probScripts.join(',');
						    				notPublishMsg += "<br>没有发布的作业有："+probJobs.join(',');
						    			} else if(probScripts.length>0) {
						    				msg += "原子脚本服务";
						    				notPublishMsg += "<br>没有发布的原子脚本服务有："+probScripts.join(',');
						    			} else if(probJobs.length>0) {
						    				msg += "作业";
						    				notPublishMsg += "<br>没有发布的作业有："+probJobs.join(',');
						    			}
						    			msg += "没有发布，请发布后重试。" + notPublishMsg;
						    			Ext.MessageBox.alert("提示", msg);
						    			return;
						    		}
							    },
							    failure: function(result, request) {
							    	secureFilterRs(result,"操作失败！");
							    }
						    });
						} else {
							publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
						}
			        }
		    	}, {
			        text: '删除',
			        cls : 'Common_Btn',
			        handler: deleteServiceRelease
		    	}, 
//		    	{
//			        text: '导入',
//			        cls : 'Common_Btn',
//			        handler: importScriptForFlow
//		    	},
		    	{
			        text : '导出',
			        cls : 'Common_Btn',
			        handler :  function() {
					        	if(iidArray.length==0){
					        		 Ext.MessageBox.alert("提示", "请选择要导出的作业！");
									 return ;
					        	}else{
					        		$.fileDownload('exportScriptMyJob.do',{
									  httpMethod: 'POST',
									  traditional: true,
									  data:{ iids :iidArray
				 					  },
									  successCallback: function(url){
											
									  },
									  failCallback: function (html, url) {
									   		 Ext.Msg.alert('提示', '导出失败！');
				                       		  return;
									   }
								  });  
					        	}
			        }
		    	}
		    	
		    	
		    ]
	    } ]
	});
	 var mainPanel = Ext.create('Ext.panel.Panel',{
		 renderTo : "scriptService_flow_grid_area",
	        width : contentPanel.getWidth(),
		    height :contentPanel.getHeight() - modelHeigth,
	        border : true,
	        layout: 'border',
	        items : [search_form,scriptServiceReleaseGrid]
	});
	 
	 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function (){
			mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
		});
		

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(mainPanel);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});
	function shareServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt <1){
			Ext.MessageBox.alert("提示", "请选择要共享的作业！");
			return ;
		}
//		if(seledCnt >1){
//			Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
//			return ;
//		}
//		var ss = scriptServiceReleaseGrid.getSelectionModel().getSelection()[0];
//		if(ss.data.status!=1) {
//			Ext.MessageBox.alert("提示", "只有已上线的服务可以共享！");
//			return;
//		}
		
		var checkMessage="";
		Ext.Ajax.request({
				url : 'checkScriptStatu.do',
				method : 'POST',
				async: false,
				params : {
					scriptiids: iidArray
				},
				success : function(response, request) { 
					checkMessage = Ext.decode(response.responseText).message;
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '校验作业状态失败！');
				}
			});
		if(checkMessage !=null && checkMessage !=""){
			Ext.Msg.alert('提示', checkMessage);
			return;
		}
		
//		if(ss.data.isshare==1) {
//			Ext.MessageBox.alert("提示", "该作业已共享！");
//			return;
//		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认共享", "是否共享选中的服务", function(id){
			if(id=='yes') {
				// 先判断是否作业中有没有共享的原子脚本
				Ext.Ajax.request({
				    url : 'checkScriptFlowHasNoShareScript.do',
				    method : 'POST',
				    params : {
				  	  serviceId : iidArray
				    },
				    success: function(response, opts) {
			    		var success = Ext.decode(response.responseText).success;
			    		if(success) {
			    			openShareWin();
			    			//release(4);
			    		} else {
			    			var message = Ext.decode(response.responseText).message;
			    			Ext.MessageBox.alert("提示", "选择的作业中，某些作业步骤所选择的原子脚本服务没有共享，请共享后重试。<br>没有共享的原子脚本有："+message);
			    			return;
			    		}
				    },
				    failure: function(result, request) {
				    	secureFilterRs(result,"操作失败！");
				    }
			    });
			}
		});
	}
	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    editable: true,
	    fieldLabel: "审核人",
	    labelWidth:93,
	    labelAlign:'right',
	    store: auditorStore_sm,
	    queryMode: 'local',
	    columnWidth:.98,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    valueField: 'loginName',
	    listeners: { //监听 
	        render : function(combo) {//渲染 
	            combo.getStore().on("load", function(s, r, o) { 
	                combo.setValue(r[0].get('loginName'));//第一个值 
	            }); 
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
//			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_sm.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_sm.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_sm.setValue("");
//					 	return;
//					} 
//				}
//			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
	    } 
	  });
	
	planTime_sm = Ext.create('Go.form.field.DateTime',{
	    fieldLabel:'计划时间',
	    format:'Y-m-d H:i:s',
	    hidden:true,
	    labelWidth : 85,
	    columnWidth:.98,
	    margin : '10 0 0 0'
	  });
	
	pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        emptyText: '',
        labelAlign:'right',
        labelWidth: 93,
        margin : '10 0 0 0',
        height: 85,
        columnWidth:.98,
        autoScroll: true
    });
	
	var levelStore_sm = Ext.create('Ext.data.Store', {
	    fields: ['iid', 'scriptLevel'],
	    data : [
	        {"iid":"1", "scriptLevel":"高级风险"},
	        {"iid":"2", "scriptLevel":"中级风险"},
	        {"iid":"3", "scriptLevel":"低级风险"}
	    ]
	});
	
	scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 85,
        columnWidth: .97,
        queryMode: 'local',
        fieldLabel: '风险级别',
        margin : '10 0 0 0',
        displayField: 'scriptLevel',
        valueField: 'iid',
        editable: false,
        hidden : !reviewSwitch,
        queryMode: 'local',
        emptyText: '--请选择作业级别--',
        store: levelStore_sm
    });
	
	Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	appSysStore1.on('load', function() {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
	var appSysObj1 = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '应用系统',
        emptyText : '--请选择应用系统--',
        hidden : !reviewSwitch,
        multiSelect: true,
        labelWidth: 93,
        columnWidth: .98,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function(combo, records, eOpts) {
                if (records) {
                	chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                    	chosedAppSys.push(records[i].data.id);
                    }
                } 

            },
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
	var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        hidden : !reviewSwitch,
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });
	
	auditing_form_sm = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	buttonAlign : 'center',
    	border : false,
    	baseCls:'customize_gray_back',
	    items: [{
//	    	layout:'form',
	    	anchor:'98%',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,		    	
	    		items:[planTime_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[isEMscript,scriptLevelCb_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[appSysObj1]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[pubDesc_sm]
	    	}]
	    }]
	});
	Ext.define('warnningModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'iid',
	      type : 'int'
	    }, {
	          name : 'serviceName',
	          type : 'string'
	    }, {
	          name : 'sysName',
	          type : 'string'
	    }, {
		      name : 'bussName',
		      type : 'string'
		}, {
			  name : 'version',
			  type : 'string'
		}, {
			  name : 'user',
			  type : 'string'
		}]
	  });
	
	var warnningStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'warnningModel',
	    proxy: {
	      type: 'ajax',
	      url: 'scriptCallSearch.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	warnningStore.on('beforeload', function (store, options) {
	    	var new_params = {  
	    			iid : warnId
	    	};
	    	
	    	Ext.apply(warnningStore.proxy.extraParams, new_params);
	    });
	var warnningColumns = [{ text: '序号', xtype:'rownumberer', width: 40 },
	             		{ text: '主键',  dataIndex: 'iid',hidden:true},
	             		{ text: '服务名称',  dataIndex: 'serviceName',flex:1},
	             		{ text: '一级分类',  dataIndex: 'sysName',width:120},
	             		{ text: '二级分类',  dataIndex: 'bussName',width:100},
	                    { text: '版本',  dataIndex: 'version', width: 80,renderer:function(value,p,record,rowIndex){
	        				if(value) {
	        					return value;
	        				} else {
	        					return '无版本号';
	        				}
	        			}},
	                    { text: '创建用户',  dataIndex: 'user',width:120}
	             		];
	warnningGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : warnningStore,
	    cls:'customize_panel_back',
	    border:false,
	    columnLines : true,
	    columns : warnningColumns
	});

	function deleteServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt < 1){
			Ext.MessageBox.alert("提示", "请选择要删除的作业！");
			return ;
		}
		var ss = selModel.getSelection();
		for ( var i = 0, len = ss.length; i < len; i++) {
			if(ss[i].data.status!=-1) {
				Ext.MessageBox.alert("提示", "只能删除状态为'草稿'的作业！");
				return ;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认删除", "是否删除选中的作业,与之级联的模板也将被删除！", function(id){if(id=='yes') release(1);});
	}
	
	function release(optionState){
		var url = 'scriptService/serviceRelease.do';
		var message="作业发布成功";
		var errorMessage="作业发布失败";
		if(optionState==1){
			message="作业删除成功";
			errorMessage="作业删除失败";
			url = "scriptService/deleteScriptForTestMyJob.do";
	    } else if(optionState==4){
			message="作业共享成功";
			errorMessage="作业共享失败";
		}
		
		var jsonData = getSelectedJsonData();
		if(jsonData=="[]"){
			Ext.MessageBox.alert("提示", signMessage);
			return ;
		}	
	    Ext.Ajax.request({
		    url : url,
		    method : 'POST',
		    params : {
		  	  jsonData : jsonData,
		  	  optionState:optionState,
		  	  shareType:shareType,
		  	  chosedShareIds:chosedShareIds,
		  	  isflow:1,
		  	  isCustomTask:0
		    },
		    success: function(response, opts) {
		    	if(optionState==1) {
		    		var message1 = Ext.decode(response.responseText).message;
		    		Ext.MessageBox.show({
		                title : "提示",
		                msg : message1,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		    	} else {
		    		var success = Ext.decode(response.responseText).success;
			        if (success) {
			            Ext.MessageBox.show({
			                title : "提示",
			                msg : message,
			                buttonText: {
			                    yes: '确定'
			                },
			                buttons: Ext.Msg.YES
			              });
			          } else {
			            Ext.MessageBox.show({
			              title : "提示",
			              msg : errorMessage,
			              buttonText: {
			                  yes: '确定'
			              },
			              buttons: Ext.Msg.YES
			            });
			          }
		    	}
		    	if(optionState==4){
		    		shareWin.close();
		    	}
		    	scriptServiceReleaseStoreForFlow.reload();
		        
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
	}
	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedJsonData(){
		var flowIdList = scriptServiceReleaseGrid.getSelectionModel().getSelection();
		if (flowIdList.length < 1) {
			return;
		}
		var jsonData = "[";
		for ( var i = 0, len = flowIdList.length; i < len; i++) {
			if (i == 0)
			{
				jsonData = jsonData + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			} else {
				jsonData = jsonData + "," + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			}
		}
		jsonData = jsonData + "]";
		return jsonData ;
	}
	function clearQueryWhere(){
		bussCb.setValue('');
		bussTypeCb.setValue('');
		scriptStatusCb.setValue('');
		//scName.setValue('');
		sName.setValue('');
		appSysObj.setValue('');
		emScriptCb.setValue('');
		//scriptTypeParam.setValue('');
	}
	//从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key ,jsonObj){
		 var eValue=eval('jsonObj.'+key);  
		 return jsonObj[''+key+''];
	}
	
	function publishScript(iid,a,b,c,hasVersion, status){
		
		if(hasVersion==1) {
			Ext.Msg.alert('提示', "该作业已经发布过！");
			return;
		}
		
		if (status==2) { // 处于审核中
			Ext.Msg.alert('提示', "该作业正处于审核中！");
			return;
		}
		
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认发布", "是否确认发布该作业", function(id){
			if(id=='yes') {
				
				Ext.Ajax.request({
  				    url : 'scriptCallSearch.do',
  				    method : 'POST',
  				    params : {
  				    	iid : iid
  				    },
  				    success: function(response, opts) {
  				        var success = Ext.decode(response.responseText).success;
  				        warnId=iid;
  				        if(success) {//有需要提示的内容
  				        	if (!warnningWin) {
  				        		warnningWin = Ext.create('widget.window', {
					                title: '提示信息,发布该脚本将影响以下脚本及作业，是否继续发布？',
					                closable: true,
					                closeAction: 'hide',
					                modal: true,
					                width: 600,
					                minWidth: 350,
					                height: 300,
					                layout: {
					                    type: 'border',
					                    padding: 5
					                },
					                items: [warnningGrid],
					                dockedItems : [ {
										xtype : 'toolbar',
										baseCls:'customize_gray_back', 
										dock : 'bottom',
										layout: {pack: 'center'},
										items : [ {
							  			xtype: "button",
							  			cls:'Common_Btn',
							  			text: "是", 
							  			handler: function () {
							  				this.up("window").close();
							  				if (!publishAuditingSMWin) {
												publishAuditingSMWin = Ext.create('widget.window', {
									                title: '确认审核信息',
									                closable: true,
									                closeAction: 'hide',
									                modal: true,
									                width: 600,
									                minWidth: 350,
									                height: reviewSwitch?310:250,
									                layout: {
									                    type: 'border',
									                    padding: '0 5 0 5'
									                },
									                items: [auditing_form_sm],
									                dockedItems : [ {
									    	            xtype: 'toolbar',
									    	            baseCls:'customize_gray_back', 
									    	            dock:'bottom',
									    	            layout: {pack: 'center'},
									    	            items: [{ 
											  			xtype: "button",
											  			cls:'Common_Btn',
											  			margin:'10',
											  			text: "确定", 
											  			margin:'5',
											  			cls : 'Common_Btn',
											  			handler: function () { 
											  				//判断输入的审核人是否合法 start
											            	var displayField =auditorComBox_sm.getRawValue();
															if(!Ext.isEmpty(displayField)){
																//判断输入是否合法标志，默认false，代表不合法
																var flag = false;
																//遍历下拉框绑定的store，获取displayField
																auditorStore_sm.each(function (record) {
																	//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
																    var data_fullName = record.get('fullName');
																    if(data_fullName == displayField){
																    	flag =true;
																    	//combo.setValue(record.get('loginName'));
																    }
																});
																if(!flag){
																 	Ext.Msg.alert('提示', "输入的审核人非法");
																 	auditorComBox_sm.setValue("");
																 	return;
																} 
															}
															//判断输入的审核人是否合法  end
											  				
											  				var planTime = planTime_sm.getRawValue();
											  				var scriptLevel = scriptLevelCb_sm.getValue();
											  				if(!reviewSwitch){
											  					scriptLevel = 100;
											  				}
											  				var publishDesc = pubDesc_sm.getValue();
											  				var auditor = auditorComBox_sm.getValue();
											  				var isEmScript = isEMscript.getValue();
											  				if(isEmScript) {
											  					isEmScript = 1;
											  				}else{
											  					isEmScript = 0;
											  				}
//											  				if(!planTime) {
//											  					Ext.Msg.alert('提示', "没有填写计划时间！");
//											  					return;
//											  				}
											  				
											  				if(!scriptLevel) {
											  					Ext.Msg.alert('提示', "没有选择风险级别！");
											  					return;
											  				}
											  				if(!publishDesc) {
											  					Ext.Msg.alert('提示', "没有填写发布申请说明！");
											  					return;
											  				}
											  				if(publishDesc.length > 255) {
											  					Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
											  					return;
											  				}
											  				if(!auditor) {
											  					Ext.Msg.alert('提示', "没有选择审核人！");
											  					return;
											  				}
											  				
												    		var ss = selModel.getSelection();
											  				
											  				var sIds = new Array();
											  				sIds.push(ss[0].data.iid);
											  				Ext.Ajax.request({
											  				    url : 'scriptPublishAuditingForMyJob.do',
											  				    method : 'POST',
											  				    params : {
											  				    	sIds : sIds,
											  				    	planTime: planTime,
											  				    	scriptLevel: scriptLevel,
											  				    	publishDesc: publishDesc,
											  				    	auditor: auditor,
											  				  	    flag:0, //0-来着个人脚本库
												  				  	isEmScript: isEmScript,
											  				  	  	appSysIds:chosedAppSys
											  				    },
											  				    success: function(response, opts) {
											  				        var success = Ext.decode(response.responseText).success;
											  				        var message = Ext.decode(response.responseText).message;
											  				        if(!success) {
											  				        	Ext.MessageBox.alert("提示", message);
											  				        } else {
											  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
											  				        }
											  				      scriptServiceReleaseStoreForFlow.load();
											  				      publishAuditingSMWin.close();
											  				      
											  				    },
											  				    failure: function(result, request) {
											  				    	secureFilterRs(result,"操作失败！");
											  				    	publishAuditingSMWin.close();
											  				    }
											  			    });
											  				
												        }
											  		}, { 
											  			xtype: "button", 
											  			cls : 'Common_Btn',
											  			text: "取消", 
											  			handler: function () {
											  				this.up("window").close();
											  			}
											  		  }]
									                }]
									            });
									            
									        }
											publishAuditingSMWin.show();
											auditorStore_sm.load();
											planTime_sm.setValue('');
											scriptLevelCb_sm.setValue('');
											pubDesc_sm.setValue('');
											auditorComBox_sm.setValue('');
											isEMscript.setValue(0);
											appSysObj1.setValue('');
											chosedAppSys='';
                                             }
							  		}, { 
							  			xtype: "button", 
							  			cls:'Common_Btn',
							  			text: "否", 
							  			handler: function () {
							  				this.up("window").close();
							  			}
							  		}]
								}]
					            });
  				        	}
  				      warnningWin.show();
  				      warnningStore.load();
  				        } else {
  				        	if (!publishAuditingSMWin) {
  								publishAuditingSMWin = Ext.create('widget.window', {
  					                title: '确认审核信息',
  					                closable: true,
  					                closeAction: 'hide',
  					                modal: true,
  					                width: 600,
  					                minWidth: 350,
  					                height: reviewSwitch?310:250,
  					                layout: {
  					                    type: 'border',
  					                    padding: '0 5 0 5'
  					                },
  					                items: [auditing_form_sm],
  					                dockedItems : [ {
  					    	            xtype: 'toolbar',
  					    	            baseCls:'customize_gray_back', 
  					    	            dock:'bottom',
  					    	            layout: {pack: 'center'},
  					    	            items: [{ 
  							  			xtype: "button",
  							  			cls:'Common_Btn',
  							  			margin:'10',
  							  			text: "确定", 
  							  			margin:'5',
  							  			cls : 'Common_Btn',
  							  			handler: function () { 
  							  				//判断输入的审核人是否合法 start
							            	var displayField =auditorComBox_sm.getRawValue();
											if(!Ext.isEmpty(displayField)){
												//判断输入是否合法标志，默认false，代表不合法
												var flag = false;
												//遍历下拉框绑定的store，获取displayField
												auditorStore_sm.each(function (record) {
													//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
												    var data_fullName = record.get('fullName');
												    if(data_fullName == displayField){
												    	flag =true;
												    	//combo.setValue(record.get('loginName'));
												    }
												});
												if(!flag){
												 	Ext.Msg.alert('提示', "输入的审核人非法");
												 	auditorComBox_sm.setValue("");
												 	return;
												} 
											}
											//判断输入的审核人是否合法  end
  							  				var planTime = planTime_sm.getRawValue();
  							  				var scriptLevel = scriptLevelCb_sm.getValue();
  							  				if(!reviewSwitch){
  							  					scriptLevel = 100;
  							  				}
  							  				var publishDesc = pubDesc_sm.getValue();
  							  				var auditor = auditorComBox_sm.getValue();
  							  				var isEmScript = isEMscript.getValue();
  							  				if(isEmScript) {
  							  					isEmScript = 1;
  							  				}else{
  							  					isEmScript = 0;
  							  				}
//  							  				if(!planTime) {
//  							  					Ext.Msg.alert('提示', "没有填写计划时间！");
//  							  					return;
//  							  				}
  							  				
  							  				if(!scriptLevel) {
  							  					Ext.Msg.alert('提示', "没有选择风险级别！");
  							  					return;
  							  				}
  							  				if(!publishDesc) {
  							  					Ext.Msg.alert('提示', "没有填写发布申请说明！");
  							  					return;
  							  				}
  							  				if(publishDesc.length > 255) {
  							  					Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
  							  					return;
  							  				}
  							  				if(!auditor) {
  							  					Ext.Msg.alert('提示', "没有选择审核人！");
  							  					return;
  							  				}
  							  				
  								    		var ss = selModel.getSelection();
  							  				
  							  				var sIds = new Array();
  							  				sIds.push(ss[0].data.iid);
  							  				Ext.Ajax.request({
  							  				    url : 'scriptPublishAuditingForMyJob.do',
  							  				    method : 'POST',
  							  				    params : {
  							  				    	sIds : sIds,
  							  				    	planTime: planTime,
  							  				    	scriptLevel: scriptLevel,
  							  				    	publishDesc: publishDesc,
  							  				    	auditor: auditor,
  							  				  	    flag:0, //0-来着个人脚本库
  								  				  	isEmScript: isEmScript,
  							  				  	  	appSysIds:chosedAppSys
  							  				    },
  							  				    success: function(response, opts) {
  							  				        var success = Ext.decode(response.responseText).success;
  							  				        var message = Ext.decode(response.responseText).message;
  							  				        if(!success) {
  							  				        	Ext.MessageBox.alert("提示", message);
  							  				        } else {
  							  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
  							  				        }
  							  				      scriptServiceReleaseStoreForFlow.load();
  							  				      publishAuditingSMWin.close();
  							  				      
  							  				    },
  							  				    failure: function(result, request) {
  							  				    	secureFilterRs(result,"操作失败！");
  							  				    	publishAuditingSMWin.close();
  							  				    }
  							  			    });
  							  				
  								        }
  							  		}, { 
  							  			xtype: "button", 
  							  			cls : 'Common_Btn',
  							  			text: "取消", 
  							  			handler: function () {
  							  				this.up("window").close();
  							  			}
  							  		  }]
  					                }]
  					            });
  					            
  					        }
  							publishAuditingSMWin.show();
  							auditorStore_sm.load();
  							planTime_sm.setValue('');
  							scriptLevelCb_sm.setValue('');
  							pubDesc_sm.setValue('');
  							auditorComBox_sm.setValue('');
  							isEMscript.setValue(0);
  							appSysObj1.setValue('');
  							chosedAppSys='';
  				        }
  				    },
  				    failure: function(result, request) {
  				    	secureFilterRs(result,"操作失败！");
  				    }
  			    });
				
				
				
				
				
				
			}
		});
		
	}
	
	function openShareWin(){
		
     Ext.define('shareClumnModel', {
		extend : 'Ext.data.Model',
		fields : [{
	      name : 'IID',
	      type : 'long'
	    }, {
	      name : 'IFULLNAME',//用户名
	      type : 'string'
	    }, {
	      name : 'ILOGINNAME',//用户登录ID
	      type : 'string'
	    }, {
	      name : 'IGROUPNAME',//组名称 
	      type : 'string'
	    },{ 
	      name : 'OBJECTNAME',//对象名称 已共享使用 
	      type : 'string'
	    },{ 
	      name : 'ISERVICESNAME',//服务名称 已共享使用 
	      type : 'string'
	    },{
	      name : 'SHARETYPE', // 共享类型 已共享使用
	      type : 'string'
	    },{
	      name : 'IGROUPDES', //组描述
	      type : 'string'
	    }]
	});
	//待共享展示列表 Store
	var shareColumnStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    autoDestroy: true,
	    model: 'shareClumnModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getScriptShareColumnObject.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	 shareColumnStore.on('beforeload', function (store, options) {
		var new_params = {  
				 shareType:shareType,
				 shareed:0, //0代表查询未共享的
				 scriptiids:iidArray,
				 objectName:objectName.getValue()
		};
		
		Ext.apply(shareColumnStore.proxy.extraParams, new_params);
	});
	
	shareColumnStore.addListener('load', function(me, records, successful, eOpts) {
        if (chosedShareIds.length>0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (chosedShareIds.indexOf(record.get('IID')) > -1) {
                    chosedRecords.push(record);
                }
            });
            shareGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
 
    });
	
	    
    //已共享展示列表 Store
	var shareedColumnStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'shareClumnModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getScriptShareColumnObject.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	 shareedColumnStore.on('beforeload', function (store, options) {
		var new_params = {  
				 shareType:shareType,
				 shareed:1, //已共享 参数  1代表查询已共享
				 scriptiids:iidArray
		};
		
		Ext.apply(shareedColumnStore.proxy.extraParams, new_params);
	});
    
	var shareedColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		    store: shareedColumnStore,
			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			border:false
	    });
	var shareColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		    store: shareColumnStore,
			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			border:false
	    });
	
	var shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
	var shareedColumns =[{ text: '序号', xtype:'rownumberer', width: 40 },
										{ text: '用户主键',  dataIndex: 'IID',hidden:true},
										{ text: '服务名称',  dataIndex: 'ISERVICESNAME',width:200},
										{ text: '共享类型',  dataIndex: 'SHARETYPE',width:200},
										{ text: '对象名称',  dataIndex: 'OBJECTNAME',width:200}];
	
	//已共享grid
	shareedGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : shareedColumnStore,
	    cls:'customize_panel_back',
	    border:false,
	    bbar:shareedColumnStorePageBar,
	    columnLines : true,
//	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    columns : shareedColumns
//		listeners: {
//	        select: function( e, record, index, eOpts ){ 
//	        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
//	        		chosedShareIds.push(record.get('IID'));
//	        	}
//	        },
//	        deselect: function( e, record, index, eOpts ){ 
//	        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
//	        		chosedShareIds.remove(record.get('IID'));
//	        	}
//	        }
//	    }
	});
	
	var shareUserRadio = Ext.create('Ext.form.field.Radio', {
		width: 80,
		name:'shareRadio',
		labelAlign : 'left',
		fieldLabel: '',
	    boxLabel: '用户共享',
	    inputValue : 1
	});
	var shareGroupRadio = Ext.create('Ext.form.field.Radio', {
		width: 90,
		name:'shareRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '用户组共享',
		inputValue : 2
	});
	
	var shareAllRadio = Ext.create('Ext.form.field.Radio', {
		width: 100,
		name:'shareRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '所有人共享',
		inputValue : 0
	});
	 var shareRadioComment =  Ext.create('Ext.form.RadioGroup', {
			name:'shareRadioComment',
			labelAlign : 'left',
			layout: 'column',
			width : '160',
			items:[shareUserRadio,shareGroupRadio,shareAllRadio],
			listeners:{
	            //通过change触发
	            change: function(g , newValue , oldValue){
	            	if(newValue.shareRadio == 0)//所有人
	            	{
	            		chosedShareIds=[];
	            		chosedShareIds.push(-1);
	            		shareType = 0;
	            		shareGrid.hide();
	            	} 
	            	else if(newValue.shareRadio == 1)//用户
	            	{
	            		 chosedShareIds=[];
	            		 shareType = 1;
	            		 shareColumns=[];
	            		 shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
	            		 shareColumns.push({ text: '主键',  dataIndex: 'IID',hidden:true});
	            		 shareColumns.push({ text: '用户名称',  dataIndex: 'IFULLNAME',width:200});
	            		 shareColumns.push({ text: '用户ID',  dataIndex: 'ILOGINNAME',width:200,flex:1});
				 
			          	 shareGrid.reconfigure(shareColumnStore,shareColumns);
			             shareColumnStore.load();
			             shareGrid.show();
	            	}
	            	else if(newValue.shareRadio == 2)//组
	            	{
	            		  chosedShareIds=[];
	            		  shareType = 2;
	            		  shareColumns=[];
						  shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
	            		  shareColumns.push({ text: '主键',  dataIndex: 'IID',hidden:true});
	            		  shareColumns.push({ text: '用户组名称',  dataIndex: 'IGROUPNAME',width:200});
	            		  shareColumns.push({ text: '用户组描述',  dataIndex: 'IGROUPDES',width:160,flex:1});
				          shareGrid.reconfigure(shareColumnStore,shareColumns);
				          shareColumnStore.load();
				          shareGrid.show();
	            	}
	            }
	        }
		}); 
 	
		
    shareGrid = Ext.create('Ext.grid.Panel', {
			region: 'center',
			autoScroll: true,
		    store : shareColumnStore,
		    border:false,
		    bbar:shareColumnStorePageBar,
		    columnLines : true,
		    cls:'customize_panel_back',
		    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		    columns : shareColumns,
			listeners: {
	        select: function( e, record, index, eOpts ){ 
	        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
	        		chosedShareIds.push(record.get('IID'));
	        	}
	        },
	        deselect: function( e, record, index, eOpts ){ 
	        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
	        		chosedShareIds.remove(record.get('IID'));
	        	}
	        }
	    }
		});
			var objectName = new Ext.form.TextField({
				name : 'objectName',
				//fieldLabel : '对象名称',
				emptyText : '--对象名称--',
				//labelWidth : 65,
		//		padding : '5',
				width :'20%',
		        //labelAlign : 'right',
		     //  value: filter_serviceName,
		        listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	if(shareRadioComment.getChecked().length==0){
								Ext.MessageBox.alert("提示", "请选择共享类型");
								return;
							}else{
								var shareRadio =shareRadioComment.getChecked()[0].inputValue;
								if(shareRadio == 0  ){
									Ext.MessageBox.alert("提示", "只能查询共享类型为用户、用户组的对象");
									return;
								}
							}
		                	shareColumnStorePageBar.moveFirst();
		                }
		            }
		        }
			});
		var share_form = Ext.create('Ext.form.Panel', {
			//layout : 'anchor',
			region: 'north',
			cls:'customize_panel_back',
			//buttonAlign : 'center',
			//bodyCls : 'x-docked-noborder-top',
			border : false,
			 dockedItems:{	
				    	xtype: 'toolbar',
				        dock: 'top',
				        border:false,
				        items: [shareRadioComment,'->',objectName,{
					        			xtype : 'button',
										cls : 'Common_Btn',
										text : '查询',
										handler : function(){
											if(shareRadioComment.getChecked().length==0){
											
												Ext.MessageBox.alert("提示", "请选择共享类型");
															return;
											}else{
													var shareRadio =shareRadioComment.getChecked()[0].inputValue;
													if(shareRadio == 0  ){
														Ext.MessageBox.alert("提示", "只能查询共享类型为用户、用户组的对象");
														return;
													}
											}
											shareColumnStorePageBar.moveFirst();
										}
									},{
				        			xtype : 'button',
									cls : 'Common_Btn',
									text : '确定',
									handler : function(){
											if(shareRadioComment.getChecked().length==0){
												Ext.MessageBox.alert("提示", "请选择共享类型");
															return;
											}
									        var shareRadio =shareRadioComment.getChecked()[0].inputValue;
											if(shareRadio == 0 || shareRadio ==1 || shareRadio ==2){
												if(shareRadio ==0){//所有人
													shareType = 0;
													release(4);
												}else if(shareRadio ==1){//用户
													shareType = 1;
													if(chosedShareIds.length ==0){
														Ext.MessageBox.alert("提示", "请选择要共享的用户");
														return;
													}else{ 
														release(4);
													}
												}else{ //组 2
													shareType = 2;
													if(chosedShareIds.length ==0){
														Ext.MessageBox.alert("提示", "请选择要共享的用户组");
														return;
													}else{
														release(4);
													}
												}
											}else{
												Ext.MessageBox.alert("提示", "请选择共享模式！");
												return;
											}
									}
				        }]
					}
		});
			var pagetab = Ext.create ('Ext.tab.Panel',
			{
			    tabPosition : 'top',
			    cls:'customize_panel_back',
//			    cls:'window_border panel_space_top panel_space_left panel_space_right',
			    region : 'center',
			    activeTab : 0,
			    //width : '100%',
			    width: 700,
                minWidth: 350,
                height: 450,
			    border : false,
//			    autoScroll: true,
			    items : [
						{
							title : '待共享',
							layout:'border',
							items:[share_form,shareGrid]
						},
						{
							title : '已共享',
							layout:'fit',
							items:[shareedGrid]
						}
			    ]
			});
		
    shareWin = Ext.create('widget.window', {
						                title: '共享列表',
						                closable: true,
						                closeAction : 'destroy',
						                modal: true,
						                width: 700,
						                minWidth: 350,
						                height: 550,
						                layout: {
						                    type: 'border',
						                    padding: 5
						                },
						                items: [pagetab]
		});
		shareWin.show();		
	}
	
	
	function importScriptForFlow(){
    	var uploadWindows;
    	var uploadForm
        uploadForm = Ext.create('Ext.form.FormPanel',{
        	border : false,
        	items : [{
            	xtype: 'filefield',
    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
    			fieldLabel: '选择文件',
    			labelWidth: 80,
    			anchor: '90%',
//    			labelAlign: 'right',
    			margin: '10 10 0 40',
    			buttonText: '浏览'
            }],
            buttonAlign : 'center',
            buttons :[{
            	text : '确定',
            	handler :upExeclData
            },{
            	text : '取消',
            	handler : function(){
            		uploadWindows.close();
            	}
            },
            {
				text: '下载模板',
				handler: function() {
					window.location.href = 'downloadSsTemplate.do?fileName=3';
				}
			}]
        });
        uploadWindows = Ext.create('Ext.window.Window', {
    		title : '作业导入',
    		layout : 'fit',
    		height : 140,
    		width : 600,
    		modal : true,
//    		autoScroll : true,
    		items : [ uploadForm ],
    		listeners : {
    			close : function(g, opt) {
    				uploadForm.destroy();
    			}
    		}
    	});
        uploadWindows.show();
        function upExeclData(){
        	var form = uploadForm.getForm();
    		var hdupfile=form.findField("fileName").getValue();
    		if(hdupfile==''){
    			Ext.Msg.alert('提示',"请选择文件...");
    			return ;
    		}
    		uploadTemplate(form);
        }
        function uploadTemplate(form) {
      	   if (form.isValid()) {
             form.submit({
               url: 'importScriptForFlow.do',
                 success: function(form, action) {
                 var sumsg = Ext.decode(action.response.responseText).message;
                    Ext.Msg.alert('提示',sumsg);
            		uploadWindows.close();
            		pageBar.moveFirst();
            		/*scriptServiceReleaseStore.reload();*/
                    return;
                 },
                 failure: function(form, action) {
                     var msg = Ext.decode(action.response.responseText).message;
                     var mess = Ext.create('Ext.window.MessageBox', {
                     minHeight : 110,
                     minWidth : 500,
                     resizable : false
                   });
                     Ext.Msg.alert('提示',msg);
                   return;
                 }
             });
      	   }
      	 }
    }
});

function toFlowGraphEdit(url,iid,status,serviceName,bussId,bussTypeId,menuId,actionType,filter_serverNameQuery,filter_scriptStateQuery)
{
	
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: url,
		params: {
			serviceId: iid,
			iid: iid,
			menuId: menuId,
			status: status,
			serviceName:serviceName,
			actionType:actionType,
			bussId:bussId,
			bussTypeId:bussTypeId,
			flag:0,
			rootspace: actionType,
			filter_serverNameQuery:filter_serverNameQuery,
			filter_scriptStateQuery:filter_scriptStateQuery,
			ifrom : 'forwardScriptServiceReleaseFlow.do'
		},
		scripts: true});
}

function editScriptFlowForFlow(iid,status,serviceName,bussId,bussTypeId,hasVersion,menuId,filter_serverNameQuery,filter_scriptStateQuery){
	 if (status == 2 ) { // 已经不是草稿状态，处于审核中或者已经上线
			Ext.Msg.alert('提示', "该作业正在审核中，不能编辑！");
			return;
		}else{
			toFlowGraphEdit('flowCustomizedInitScriptService.do',iid,status,serviceName,bussId,bussTypeId,menuId,'edit',filter_serverNameQuery,filter_scriptStateQuery);
		}
}

function testScriptFlowForFlow(iid,serviceName,bussId,bussTypeId,menuId,filter_serverNameQuery,filter_scriptStateQuery){
	destroyRubbish(); //销毁本页垃圾
	toFlowGraphEdit('flowCustomizedInitScriptService.do',iid,null,serviceName,bussId,bussTypeId,menuId,'test',filter_serverNameQuery,filter_scriptStateQuery);
}

function viewVersionForFlowForFlow(iid,status,serviceName,bussId,bussTypeId,menuId,filter_serverNameQuery,filter_scriptStateQuery){
	destroyRubbish(); //销毁本页垃圾
	toFlowGraphEdit('scriptViewVersionForFlow.do',iid,null,serviceName,bussId,bussTypeId,menuId,'view',filter_serverNameQuery,filter_scriptStateQuery);
}

function customScriptFlowInScriptManagerForFlow(iid,status,serviceName,bussId,bussTypeId,menuId,filter_serverNameQuery,filter_scriptStateQuery){
	
	toFlowGraphEdit('flowCustomizedInitScriptService.do',iid,null,serviceName,bussId,bussTypeId,menuId,'model',filter_serverNameQuery,filter_scriptStateQuery);
}


