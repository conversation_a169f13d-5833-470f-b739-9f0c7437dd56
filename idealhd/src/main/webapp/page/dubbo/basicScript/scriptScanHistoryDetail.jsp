<%@ page import="com.ideal.ieai.core.Environment" %>
<%@page contentType="text/html; charset=utf-8"%>
<%
    boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
%>
<html>
<head>
<script type="text/javascript">
var scanIid ='<%=request.getParameter("iid")==null?"":request.getParameter("iid")%>';

//跳转过来的参数，返回时使用  start
var iid = '<%=request.getParameter("iid")==null?"":request.getParameter("iid")%>';
var ibegindatetime = '<%=request.getParameter("ibegindatetime")==null?"":request.getParameter("ibegindatetime")%>';
var ienddatetime = '<%=request.getParameter("ienddatetime")==null?"":request.getParameter("ienddatetime")%>';
var iscriptcount = '<%=request.getParameter("iscriptcount")==null?"":request.getParameter("iscriptcount")%>';
var iresultt = '<%=request.getParameter("iresultt")==null?"":request.getParameter("iresultt")%>';
var iresulttf ='<%=request.getParameter("iresulttf")==null?"":request.getParameter("iresulttf")%>';
var istatus ='<%=request.getParameter("istatus")==null?"":request.getParameter("istatus")%>';
var icreateuser ='<%=request.getParameter("icreateuser")==null?"":request.getParameter("icreateuser")%>';
var icreatetime ='<%=request.getParameter("icreatetime")==null?"":request.getParameter("icreatetime")%>';
var createUser ='<%=request.getParameter("createUser")==null?"":request.getParameter("createUser")%>';
var startTime ='<%=request.getParameter("startTime")==null?"":request.getParameter("startTime")%>';
var endTime ='<%=request.getParameter("endTime")==null?"":request.getParameter("endTime")%>';
//跳转过来的参数，返回时使用  end

var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/scriptScanHistoryDetail.js"></script>
</head>
<body>
<div id="scriptScanHistoryDetail_area" style="width: 100%;height: 100%">
</div>
</body>
</html>