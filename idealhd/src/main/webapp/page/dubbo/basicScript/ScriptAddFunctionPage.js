Ext.onReady(function () {
    var success= true;
    var iname;
    var editor;
    var editorf;
    var editorone;
    var editorfOne;
    var editorTwo;
    var editorfTwo;
    //数据状态istatus标识，1草稿，2发布，3草稿（发布后再编辑的草稿）
    var editorStatus = "";
    //必填标识
    var required = '<span style="color:#ff0000;font-weight:bold" data-qtip="Required">*</span>';
    // 函数内容
    var functionContent = Ext.create('Ext.panel.Panel', {
        minHeight: 70,
        width:1710,
        border: false,
        autoScroll: true,
        region: 'center',
        margin: "0 5 5 5",
        height: contentPanel.getHeight()-585,
        html: '<textarea id="code" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
    });

    //用例
    var functionExample = Ext.create('Ext.panel.Panel', {
        minHeight: 70,
        width:1710,
        border: false,
        autoScroll: true,
        region: 'center',
        margin: "0 5 5 5",
        height: contentPanel.getHeight()-585,
        html: '<textarea id="example" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
    });
    //函数内容
    var functionContentOne = Ext.create('Ext.panel.Panel', {
        minHeight: 70,
        width:1710,
        border: false,
        autoScroll: true,
        region: 'center',
        margin: "0 5 5 5",
        height: contentPanel.getHeight()-585,
        html: '<textarea id="codeOne" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
    });

    //用例
    var functionExampleOne = Ext.create('Ext.panel.Panel', {
        minHeight: 70,
        width:1710,
        border: false,
        autoScroll: true,
        region: 'center',
        margin: "0 5 5 5",
        height: contentPanel.getHeight()-585,
        html: '<textarea id="exampleOne" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
    });
    //函数内容
    var functionContentTwo = Ext.create('Ext.panel.Panel', {
        minHeight: 70,
        width:1710,
        border: false,
        autoScroll: true,
        region: 'center',
        margin: "0 5 5 5",
        height: contentPanel.getHeight()-585,
        html: '<textarea id="codeTwo" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
    });

    //用例
    var functionExampleTwo = Ext.create('Ext.panel.Panel', {
        minHeight: 70,
        width:1710,
        border: false,
        autoScroll: true,
        region: 'center',
        margin: "0 5 5 5",
        height: contentPanel.getHeight()-585,
        html: '<textarea id="exampleTwo" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
    });

    //支持语言editorFlag
    var supportLanguage = new Ext.form.RadioGroup({
        name: 'supportLanguage',
        // fieldLabel: '函数内容',
        afterLabelTextTpl: required,
        margin:" 10 0 0 0 ",
        // disabled: editorFlag,
        labelAlign:'right',
        // columnWidth:.98,
        labelWidth: 93,
        name: 'ra_s_type_win',
        items: [{
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '1',
            boxLabel: 'shell',
            // listeners: {
            //     click: {
            //         element: 'el',
            //         fn: function(){
            //             functionContentOne.hide();
            //             functionExampleOne.hide();
            //             functionExampleTwo.hide();
            //             functionContentTwo.hide();
            //         }
            //     },
            //
            // }
        },
            {
                name: 'ra_s_type_win',
                width: 70,
                inputValue: '2',
                boxLabel: 'python',
                // listeners: {
                //     click: {
                //         element: 'el',
                //         fn: function(){
                //             functionContent.hide();
                //             functionExample.hide();
                //             functionExampleTwo.hide();
                //             functionContentTwo.hide();
                //         }
                //     },
                //
                // }
            },
            {
                name: 'ra_s_type_win',
                width: 100,
                inputValue: '3',
                boxLabel: 'powershell',
                // listeners: {
                //     click: {
                //         element: 'el',
                //         fn: function(){
                //             functionContentOne.hide();
                //             functionExampleOne.hide();
                //             functionExample.hide();
                //             functionContent.hide();
                //         }
                //     },
                //
                // }
            }],
        listeners: {
            change: function (comb, newValue, oldValue, eOpts) {
                if(newValue.ra_s_type_win=='2'){
                    functionContentOne.show();
                    functionExampleOne.show();
                    functionExampleTwo.hide();
                    functionContentTwo.hide();
                    functionExample.hide();
                    functionContent.hide();
                }
                if(newValue.ra_s_type_win=='1'){
                    functionContentOne.hide();
                    functionExampleOne.hide();
                    functionExampleTwo.hide();
                    functionContentTwo.hide();
                    functionExample.show();
                    functionContent.show();
                }
                if(newValue.ra_s_type_win=='3'){
                    functionContentOne.hide();
                    functionExampleOne.hide();
                    functionExampleTwo.show();
                    functionContentTwo.show();
                    functionExample.hide();
                    functionContent.hide();
                }
            }
        }
    });



    //功能说明
    var functionDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'functionDesc',
        fieldLabel: '功能说明',
        afterLabelTextTpl: required,
        labelAlign:'right',
        emptyText: '--最大输入长度为125--',
        labelWidth: 93,
        //margin : '10 0 0 0',
        maxLength: 125,
        height: 70,
        width:'100%',
        columnWidth:.98,
        autoScroll: true
    });

    //函数名称
    var functionName = Ext.create('Ext.form.field.Text', {
        name: 'functionName',
        fieldLabel: '函数名称',
        afterLabelTextTpl: required,
        labelAlign:'right',
        emptyText: '--最大输入长度为25--',
        readOnly:editorFlag,
        labelWidth: 93,
        //margin : '10 0 0 0',
        maxLength: 25,
        height: 35,
        width:'100%',
        columnWidth:.98,
        autoScroll: true
    });
    // var buttonPanel = Ext.create('Ext.ux.ideal.form.Panel', {
    //     autoScroll: true,
    //     border: true,
    //     tbar: [/*{
    //         text: '保存并发布',
    //         cls: 'Common_Btn',
    //         handler: function () {
    //             saveFunctionData(true);
    //         }
    //     },*/{
    //         text: '保存',
    //         cls: 'Common_Btn',
    //         handler: function () {
    //             saveFunctionData(false);
    //         }
    //     },{
    //         text: '返回',
    //         cls: 'Common_Btn',
    //         handler: function () {
    //             //返回页面跳转
    //             contentPanel.getLoader().load({
    //                 url: 'functionLibraryPage.do'
    //             });
    //         }
    //     }]
    // });
    // var tabPanel = Ext.create('Ext.tab.Panel', {
    //     width: '100%',
    //     height: '100%',
    //     layout: 'fit',
    //     tabPosition: 'top',
    //     cls: 'normatab no_verticaltab',
    //     region: 'center',
    //     activeTab: activeTab,
    //     border: false,
    //     defaults:
    //         {
    //             autoScroll: false
    //         },
    //     items: [
    //         {
    //             title: 'shell',
    //             items: [{
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype: 'label',
    //                     width: '100',
    //                     height: '50',
    //                     margin: " 10 0 0 0 ",
    //                     afterLabelTextTpl: required,
    //                     text: '函数内容:*',
    //                 }]
    //             }, functionContent, {
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype: 'label',
    //                     width: '100',
    //                     height: '50',
    //                     margin: " 10 0 0 0 ",
    //                     afterLabelTextTpl: required,
    //                     text: '用例:*',
    //                 }]
    //             }, functionExample],
    //             listeners:
    //                 {
    //                 }
    //         },
    //         {
    //             title: 'powerShell',
    //             items: [{
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype: 'label',
    //                     width: '100',
    //                     height: '50',
    //                     margin: " 10 0 0 0 ",
    //                     afterLabelTextTpl: required,
    //                     text: '函数内容:*',
    //                 }]
    //             }, functionContentOne, {
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype: 'label',
    //                     width: '100',
    //                     height: '50',
    //                     margin: " 10 0 0 0 ",
    //                     afterLabelTextTpl: required,
    //                     text: '用例:*',
    //                 }]
    //             }, functionExampleOne],
    //             listeners:
    //                 {
    //                 }
    //         },
    //         {
    //             title: 'python',
    //             items: [{
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype: 'label',
    //                     width: '100',
    //                     height: '50',
    //                     margin: " 10 0 0 0 ",
    //                     afterLabelTextTpl: required,
    //                     text: '函数内容:*',
    //                 }]
    //             }, functionContentTwo, {
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype: 'label',
    //                     width: '100',
    //                     height: '50',
    //                     margin: " 10 0 0 0 ",
    //                     afterLabelTextTpl: required,
    //                     text: '用例:*',
    //                 }]
    //             }, functionExampleTwo],
    //             listeners:
    //                 {
    //                 }
    //         }
    //     ]
    // })


    //保存功能
    function saveFunctionData(publishFlag){
        //publishFlag=true代表保存并发布，publishFlag=false表示只保存
        editor.save();
        editorf.save();
        editorone.save();
        editorfOne.save();
        editorTwo.save();
        editorfTwo.save();
        //4、简单校验函数内容是否包含函数名称
        var zcLanguage = supportLanguage.getValue().ra_s_type_win;
        //获取函数名称
        var thisFunName = functionName.getValue();
        //校验表单
        //1、表单不能为空
        if(thisFunName == '' || thisFunName == null){
            Ext.Msg.alert('提示', '函数名称不能为空！');
            return;
        }
        if(functionDesc.getValue() == '' || functionDesc.getValue() == null){
            Ext.Msg.alert('提示', '功能说明不能为空！');
            return;
        }

        if((document.getElementById('code').value == '' || document.getElementById('code').value == null)&&(document.getElementById('codeOne').value == '' || document.getElementById('codeOne').value == null)&&(document.getElementById('codeTwo').value == '' || document.getElementById('codeTwo').value == null)){
            Ext.Msg.alert('提示', '函数内容不能为空！');
            return;
        }
        if((document.getElementById('example').value == '' || document.getElementById('example').value == null)&&(document.getElementById('exampleOne').value == '' || document.getElementById('exampleOne').value == null)&&(document.getElementById('exampleTwo').value == '' || document.getElementById('exampleTwo').value == null)){
            Ext.Msg.alert('提示', '用例不能为空！');
            return;
        }
        if('1' == zcLanguage){
            if((document.getElementById('code').value == '' || document.getElementById('code').value == null)&&(document.getElementById('example').value != '' && document.getElementById('example').value != null)){
                Ext.Msg.alert('提示', 'shell函数内容不能为空！');
                return;
            }
            if((document.getElementById('code').value != '' && document.getElementById('code').value != null)&&(document.getElementById('example').value === '' || document.getElementById('example').value == null)){
                Ext.Msg.alert('提示', 'shell用例不能为空！');
                return;
            }
            if((document.getElementById('codeOne').value == '' || document.getElementById('codeOne').value == null)&&(document.getElementById('exampleOne').value != '' && document.getElementById('exampleOne').value != null)){
                Ext.Msg.alert('提示', 'python函数内容不能为空！');
                return;
            }
            if((document.getElementById('codeOne').value != '' && document.getElementById('codeOne').value != null)&&(document.getElementById('exampleOne').value === '' || document.getElementById('exampleOne').value == null)){
                Ext.Msg.alert('提示', 'python用例不能为空！');
                return;
            }
            if((document.getElementById('codeTwo').value == '' || document.getElementById('codeTwo').value == null)&&(document.getElementById('exampleTwo').value != '' && document.getElementById('exampleTwo').value != null)){
                Ext.Msg.alert('提示', 'powerShell函数内容不能为空！');
                return;
            }
            if((document.getElementById('codeTwo').value != '' && document.getElementById('codeTwo').value != null)&&(document.getElementById('exampleTwo').value === '' || document.getElementById('exampleTwo').value == null)){
                Ext.Msg.alert('提示', 'powerShell用例不能为空！');
                return;
            }
        }
        if('2' == zcLanguage){
            if((document.getElementById('code').value == '' || document.getElementById('code').value == null)&&(document.getElementById('example').value != '' && document.getElementById('example').value != null)){
                Ext.Msg.alert('提示', 'shell函数内容不能为空！');
                return;
            }
            if((document.getElementById('code').value != '' && document.getElementById('code').value != null)&&(document.getElementById('example').value === '' || document.getElementById('example').value == null)){
                Ext.Msg.alert('提示', 'shell用例不能为空！');
                return;
            }
            if((document.getElementById('codeOne').value == '' || document.getElementById('codeOne').value == null)&&(document.getElementById('exampleOne').value != '' && document.getElementById('exampleOne').value != null)){
                Ext.Msg.alert('提示', 'python函数内容不能为空！');
                return;
            }
            if((document.getElementById('codeOne').value != '' && document.getElementById('codeOne').value != null)&&(document.getElementById('exampleOne').value === '' || document.getElementById('exampleOne').value == null)){
                Ext.Msg.alert('提示', 'python用例不能为空！');
                return;
            }
            if((document.getElementById('codeTwo').value == '' || document.getElementById('codeTwo').value == null)&&(document.getElementById('exampleTwo').value != '' && document.getElementById('exampleTwo').value != null)){
                Ext.Msg.alert('提示', 'powerShell函数内容不能为空！');
                return;
            }
            if((document.getElementById('codeTwo').value != '' && document.getElementById('codeTwo').value != null)&&(document.getElementById('exampleTwo').value === '' || document.getElementById('exampleTwo').value == null)){
                Ext.Msg.alert('提示', 'powerShell用例不能为空！');
                return;
            }
        }
        if('3' == zcLanguage){
            if((document.getElementById('code').value == '' || document.getElementById('code').value == null)&&(document.getElementById('example').value != '' && document.getElementById('example').value != null)){
                Ext.Msg.alert('提示', 'shell函数内容不能为空！');
                return;
            }
            if((document.getElementById('code').value != '' && document.getElementById('code').value != null)&&(document.getElementById('example').value === '' || document.getElementById('example').value == null)){
                Ext.Msg.alert('提示', 'shell用例不能为空！');
                return;
            }
            if((document.getElementById('codeOne').value == '' || document.getElementById('codeOne').value == null)&&(document.getElementById('exampleOne').value != '' && document.getElementById('exampleOne').value != null)){
                Ext.Msg.alert('提示', 'python函数内容不能为空！');
                return;
            }
            if((document.getElementById('codeOne').value != '' && document.getElementById('codeOne').value != null)&&(document.getElementById('exampleOne').value === '' || document.getElementById('exampleOne').value == null)){
                Ext.Msg.alert('提示', 'python用例不能为空！');
                return;
            }
            if((document.getElementById('codeTwo').value == '' || document.getElementById('codeTwo').value == null)&&(document.getElementById('exampleTwo').value != '' && document.getElementById('exampleTwo').value != null)){
                Ext.Msg.alert('提示', 'powerShell函数内容不能为空！');
                return;
            }
            if((document.getElementById('codeTwo').value != '' && document.getElementById('codeTwo').value != null)&&(document.getElementById('exampleTwo').value === '' || document.getElementById('exampleTwo').value == null)){
                Ext.Msg.alert('提示', 'powerShell用例不能为空！');
                return;
            }
        }

        //2、函数名称、功能说明长度校验
        if(thisFunName.length > 25){
            Ext.Msg.alert('提示', '函数名称过长，请重新填写！');
            return;
        }
        if(functionDesc.getValue().length > 125){
            Ext.Msg.alert('提示', '功能说明过长，请重新填写！');
            return;
        }
        //3、正则表达式校验函数名称，只能由数字、字母、下划线组成，并且首字符不能为数字
        var objRegExp = /^[0-9a-zA-Z_]{1,}$/;//数字字母下划线校验
        var zzRegExp = /^[1-9]\d*$/;//数字校验
        if(!objRegExp.test(thisFunName)){
            Ext.Msg.alert('提示', '函数名称只能由数字、字母、下划线组成，且首字母不能为数字，请重新填写！');
            return;
        }
        if(zzRegExp.test(thisFunName.charAt(0))){
            Ext.Msg.alert('提示', '函数名称只能由数字、字母、下划线组成，且首字母不能为数字，请重新填写！');
            return;
        }


        //脚本内容验证正则表达式
        var functionContentCheckExp = null;
        //shell
        // if ('1' == zcLanguage) {
            var functionContentCheckExpshell = eval('/(function)\\s+(' + thisFunName + ')/');
            var functionContentCheckExpSec = eval('/\\s*(' + thisFunName + ')\\s*[(]/');
            if (document.getElementById('code').value != '' &&document.getElementById('code').value != null) {
                if (!functionContentCheckExpshell.test(document.getElementById('code').value)
                    && !functionContentCheckExpSec.test(document.getElementById('code').value)) {
                    Ext.Msg.alert('提示', 'shell函数内容必须包含一个与函数名称相同的方法名，请重新填写！');
                    return;
                }
            }
        // }
        //python
        // if('2' == zcLanguage){
           var functionContentCheckExppython = eval('/(def)\\s+('+thisFunName+')\\s*[(]/');
        // }
        //powershell
        // if('3' == zcLanguage){
           var  functionContentCheckExppowerShell = eval('/(Function)\\s+('+thisFunName+')\\s*/');
        // }
        // if ('2' == zcLanguage) {
            if (document.getElementById('codeOne').value != '' && document.getElementById('codeOne').value != null/*document.getElementById('codeOne').value == '' || document.getElementById('codeOne').value == null*/) {
                if (!functionContentCheckExppython.test(document.getElementById('codeOne').value)) {
                    Ext.Msg.alert('提示', 'python函数内容必须包含一个与函数名称相同的方法名，请重新填写！');
                    return;
                }
            }
        // }
        // if ('3' == zcLanguage) {
            if (document.getElementById('codeTwo').value != '' && document.getElementById('codeTwo').value != null) {
                if (!functionContentCheckExppowerShell.test(document.getElementById('codeTwo').value)) {
                    Ext.Msg.alert('提示', 'powerShell函数内容必须包含一个与函数名称相同的方法名，请重新填写！');
                    return;
                }
            }
        // }


        //5、验证函数名称是否重复
        if(dataIid.length<=0||functionName.getValue()!=iname){
        Ext.Ajax.request({
            url: 'scriptService/checkFunctionNameOnly.do',
            method: 'POST',
            async: false,
            params: {
                iname : functionName.getValue(),
                // isupportLanguage : supportLanguage.getValue(),
                iidss : dataIid
            },
            success: function (response, request) {
                success = Ext.decode(response.responseText).success;
                // if(success){
                // }else{
                //     Ext.Msg.alert('提示', '函数名称已存在，请重新填写函数名称！');
                // }
                if (!success) {
                    Ext.Msg.alert('提示', '函数名称已存在，请重新填写函数名称！');
                }
            }
            ,
            failure: function (result, request) {
                secureFilterRs(result, "校验失败！");
            }
        });}
        if(success){
        var dataMap = {};
        // if (document.getElementById('code').value!=''&&document.getElementById('code').value!=null&&
        //     document.getElementById('example').example!=''&&document.getElementById('example').value!=null){
        var shellMap = {};
        shellMap.code = document.getElementById('code').value
        shellMap.example = document.getElementById('example').value
        dataMap.shell=shellMap;
        // }
        // if (document.getElementById('codeOne').value!=''&&document.getElementById('codeOne').value!=null&&
        //     document.getElementById('exampleOne').example!=''&&document.getElementById('exampleOne').value!=null){
        var pythonMap = {};
        pythonMap.code = document.getElementById('codeOne').value
        pythonMap.example = document.getElementById('exampleOne').value
        dataMap.python=pythonMap;
        // }
        // if (document.getElementById('codeTwo').value!=''&&document.getElementById('codeTwo').value!=null&&
        //     document.getElementById('exampleTwo').example!=''&&document.getElementById('exampleTwo').value!=null){
        var powerShellMap = {};
        powerShellMap.code = document.getElementById('codeTwo').value
        powerShellMap.example = document.getElementById('exampleTwo').value
        dataMap.powerShell=powerShellMap;
        // }
        //保存数据
        Ext.Ajax.request({
            url: 'scriptService/saveFunctionData.do',
            method: 'POST',
            async: false,
            params: {
                iidss:dataIid,
                iname: functionName.getValue(),
                idesc:functionDesc.getValue(),
                istatus:editorStatus,
                // isupportLanguage:supportLanguage.getValue(),
                // functionContent:document.getElementById('code').value,
                // functionExample:document.getElementById('example').value,
                // functionContentOne:document.getElementById('codeOne').value,
                // functionExampleOne:document.getElementById('exampleOne').value,
                // functionContentTwo:document.getElementById('codeTwo').value,
                // functionExampleTwo:document.getElementById('exampleTwo').value,
                dataMapString:Ext.encode(dataMap),
                publishFlag:publishFlag,
                classId:classId
            },
            success: function (response, request) {
                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                //返回页面跳转
                contentPanel.getLoader().load({
                    url: 'functionLibraryPage.do?classId=' + classId
                });
            },
            failure: function (result, request) {
                secureFilterRs(result, "保存失败！");
            }
        });
     }
    }

    //表单区
    // var formItems = Ext.create('Ext.form.Panel', {
    //     region: 'north',
    //     layout: 'anchor',
    //     buttonAlign: 'center',
    //     bodyCls: 'x-docked-noborder-top',
    //     border: true,
    //     dockedItems: [{
    //         xtype: 'toolbar',
    //         border: false,
    //         baseCls: 'customize_gray_back',
    //         dock: 'top',
    //         items: [functionName]
    //     },{
    //         xtype: 'toolbar',
    //         border: false,
    //         baseCls: 'customize_gray_back',
    //         dock: 'top',
    //         items: [functionDesc]
    //     }, {
    //         xtype: 'toolbar',
    //         border: false,
    //         baseCls: 'customize_gray_back',
    //         dock: 'top',
    //         items: [supportLanguage]
    //     },  new Ext.panel.Panel({
    //         width: contentPanel.getWidth()-100,
    //         height: contentPanel.getHeight() - modelHeigth-100,
    //         items:[{
    //             xtype: 'toolbar',
    //             border: false,
    //             baseCls: 'customize_gray_back',
    //             dock: 'top',
    //             items: [{
    //                 xtype : 'label',
    //                 width:'100',
    //                 height:'50',
    //                 margin:" 10 0 0 0 ",
    //                 afterLabelTextTpl:required,
    //                 text : '函数:*',
    //             }],
    //         },
    //             functionContent,functionContentOne,functionContentTwo,{
    //                 xtype: 'toolbar',
    //                 border: false,
    //                 baseCls: 'customize_gray_back',
    //                 dock: 'top',
    //                 items: [{
    //                     xtype : 'label',
    //                     width:'100',
    //                     height:'50',
    //                     margin:" 10 0 0 0 ",
    //                     afterLabelTextTpl:required,
    //                     text : '用例:*',
    //                 }]
    //             },functionExample,functionExampleOne,functionExampleTwo,buttonPanel
    //         ]
    //     })
    //
    //         /*{
    //             xtype: 'toolbar',
    //             border: false,
    //             baseCls: 'customize_gray_back',
    //             dock: 'top',
    //             items: [{
    //                 xtype : 'label',
    //                 width:'100',
    //                 height:'50',
    //                 margin:" 10 0 0 0 ",
    //                 afterLabelTextTpl:required,
    //                 text : '函数:*',
    //             }],
    //         },
    //        functionContent,functionContentOne,functionContentTwo,{
    //             xtype: 'toolbar',
    //             border: false,
    //             baseCls: 'customize_gray_back',
    //             dock: 'top',
    //             items: [{
    //                 xtype : 'label',
    //                 width:'100',
    //                 height:'50',
    //                 margin:" 10 0 0 0 ",
    //                 afterLabelTextTpl:required,
    //                 text : '用例:*',
    //             }]
    //         },functionExample,,functionExampleOne,,functionExampleTwo,buttonPanel*/
    //     ]
    // });

    var formItems = Ext.create('Ext.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        bodyCls: 'x-docked-noborder-top',
        border: true,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [functionName]
        },{
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [functionDesc]
        }, {
            border: false,
            layout: 'column',
            baseCls: 'customize_gray_back',
            items: [{
                xtype: 'label',
                fieldLabel: '函数内容',
                html: '函数内容:'+required,
                margin: '0 5 0 28'
            }, new Ext.panel.Panel({
                width: contentPanel.getWidth() - 107,
                height: contentPanel.getHeight() - modelHeigth - 200,
                margin:0,
                cls:'functionContentPanel',
                border:true,
                items: [supportLanguage,
                    {
                        xtype: 'toolbar',
                        border: false,
                        // baseCls: 'customize_gray_back',
                        dock: 'top',
                        items: [{
                            xtype: 'label',
                            width: '100',
                            height: '50',
                            margin: "10 0 0 5",
                            afterLabelTextTpl: required,
                            text: '函数:*',
                        }],
                    }, functionContent, functionContentOne, functionContentTwo,
                    {
                        xtype: 'toolbar',
                        border: false,
                        // baseCls: 'customize_gray_back',
                        dock: 'top',
                        items: [{
                            xtype: 'label',
                            width: '100',
                            height: '50',
                            margin: "10 0 0 5",
                            afterLabelTextTpl: required,
                            text: '用例:*',
                        }]
                    }, functionExample, functionExampleOne, functionExampleTwo
                ],
                listeners: {
                    render: function () {
                        //函数内容
                         editor = CodeMirror.fromTextArea(document.getElementById('code'), {
                            mode: 'shell',
                            theme: "lesser-dark", // 主题
                            keyMap: "sublime", // 快键键风格
                            extraKeys: {
                                "Ctrl-Q": "autocomplete",
                                "Ctrl-D": "deleteLine"
                            },
                            lineNumbers: true, // 显示行号
                            smartIndent: true, // 智能缩进
                            indentUnit: 4, // 智能缩进单位为4个空格长度
                            indentWithTabs: true, // 使用制表符进行智能缩进
                            lineWrapping: true, //
                            // 在行槽中添加行号显示器、折叠器、语法检测器
                            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
                            foldGutter: true, // 启用行槽中的代码折叠
                            autofocus: true, // 自动聚焦
                            matchBrackets: true, // 匹配结束符号，比如"]、}"
                            autoCloseBrackets: true, // 自动闭合符号
                            styleActiveLine: true // 显示选中行的样式
//        ,extraKeys: {
//            "F11": function(cm) {
//              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
//            },
//            "Esc": function(cm) {
//              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
//            }
//          }
                        });

                         editorf = CodeMirror.fromTextArea(document.getElementById('example'), {
                            mode: 'shell',
                            theme: "lesser-dark", // 主题
                            keyMap: "sublime", // 快键键风格
                            extraKeys: {
                                "Ctrl-Q": "autocomplete",
                                "Ctrl-D": "deleteLine"
                            },
                            lineNumbers: true, // 显示行号
                            smartIndent: true, // 智能缩进
                            indentUnit: 4, // 智能缩进单位为4个空格长度
                            indentWithTabs: true, // 使用制表符进行智能缩进
                            lineWrapping: true, //
                            // 在行槽中添加行号显示器、折叠器、语法检测器
                            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
                            foldGutter: true, // 启用行槽中的代码折叠
                            autofocus: true, // 自动聚焦
                            matchBrackets: true, // 匹配结束符号，比如"]、}"
                            autoCloseBrackets: true, // 自动闭合符号
                            styleActiveLine: true // 显示选中行的样式
                        });
                        //函数内容
                         editorone = CodeMirror.fromTextArea(document.getElementById('codeOne'), {
                            mode: 'shell',
                            theme: "lesser-dark", // 主题
                            keyMap: "sublime", // 快键键风格
                            extraKeys: {
                                "Ctrl-Q": "autocomplete",
                                "Ctrl-D": "deleteLine"
                            },
                            lineNumbers: true, // 显示行号
                            smartIndent: true, // 智能缩进
                            indentUnit: 4, // 智能缩进单位为4个空格长度
                            indentWithTabs: true, // 使用制表符进行智能缩进
                            lineWrapping: true, //
                            // 在行槽中添加行号显示器、折叠器、语法检测器
                            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
                            foldGutter: true, // 启用行槽中的代码折叠
                            autofocus: true, // 自动聚焦
                            matchBrackets: true, // 匹配结束符号，比如"]、}"
                            autoCloseBrackets: true, // 自动闭合符号
                            styleActiveLine: true // 显示选中行的样式
                        });

                         editorfOne = CodeMirror.fromTextArea(document.getElementById('exampleOne'), {
                            mode: 'shell',
                            theme: "lesser-dark", // 主题
                            keyMap: "sublime", // 快键键风格
                            extraKeys: {
                                "Ctrl-Q": "autocomplete",
                                "Ctrl-D": "deleteLine"
                            },
                            lineNumbers: true, // 显示行号
                            smartIndent: true, // 智能缩进
                            indentUnit: 4, // 智能缩进单位为4个空格长度
                            indentWithTabs: true, // 使用制表符进行智能缩进
                            lineWrapping: true, //
                            // 在行槽中添加行号显示器、折叠器、语法检测器
                            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
                            foldGutter: true, // 启用行槽中的代码折叠
                            autofocus: true, // 自动聚焦
                            matchBrackets: true, // 匹配结束符号，比如"]、}"
                            autoCloseBrackets: true, // 自动闭合符号
                            styleActiveLine: true // 显示选中行的样式
                        });
                        //函数内容
                         editorTwo = CodeMirror.fromTextArea(document.getElementById('codeTwo'), {
                            mode: 'shell',
                            theme: "lesser-dark", // 主题
                            keyMap: "sublime", // 快键键风格
                            extraKeys: {
                                "Ctrl-Q": "autocomplete",
                                "Ctrl-D": "deleteLine"
                            },
                            lineNumbers: true, // 显示行号
                            smartIndent: true, // 智能缩进
                            indentUnit: 4, // 智能缩进单位为4个空格长度
                            indentWithTabs: true, // 使用制表符进行智能缩进
                            lineWrapping: true, //
                            // 在行槽中添加行号显示器、折叠器、语法检测器
                            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
                            foldGutter: true, // 启用行槽中的代码折叠
                            autofocus: true, // 自动聚焦
                            matchBrackets: true, // 匹配结束符号，比如"]、}"
                            autoCloseBrackets: true, // 自动闭合符号
                            styleActiveLine: true // 显示选中行的样式
                        });

                         editorfTwo = CodeMirror.fromTextArea(document.getElementById('exampleTwo'), {
                            mode: 'shell',
                            theme: "lesser-dark", // 主题
                            keyMap: "sublime", // 快键键风格
                            extraKeys: {
                                "Ctrl-Q": "autocomplete",
                                "Ctrl-D": "deleteLine"
                            },
                            lineNumbers: true, // 显示行号
                            smartIndent: true, // 智能缩进
                            indentUnit: 4, // 智能缩进单位为4个空格长度
                            indentWithTabs: true, // 使用制表符进行智能缩进
                            lineWrapping: true, //
                            // 在行槽中添加行号显示器、折叠器、语法检测器
                            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
                            foldGutter: true, // 启用行槽中的代码折叠
                            autofocus: true, // 自动聚焦
                            matchBrackets: true, // 匹配结束符号，比如"]、}"
                            autoCloseBrackets: true, // 自动闭合符号
                            styleActiveLine: true // 显示选中行的样式
                        });


                        //如果为编辑，为页面各个组件赋值，iid大于0为编辑，否则为新增
                        if(dataIid.length>0){
                            // console.log(dataIid);
                            Ext.Ajax.request({
                                url: 'scriptService/getScriptFunctioneditorData.do',
                                method: 'POST',
                                sync: true,
                                params: {
                                    iid:dataIid
                                },
                                success: function (response, request) {
                                    var list= Ext.decode(response.responseText).data;
                                    var listLang=[];
                                    for (var i = 0; i <list.length ; i++) {
                                        iname=list[i].iname;
                                        if(list[i].isupportLanguage==3){
                                            listLang.push(list[i].isupportLanguage);
                                            functionName.setValue(list[i].iname);
                                            functionDesc.setValue(list[i].idesc);
                                            // supportLanguage.items.items[Ext.decode(response.responseText).ilanguagetype-1].setValue(true);
                                            editorTwo.getDoc().setValue(list[i].functionContent);
                                            editorfTwo.getDoc().setValue(list[i].functionExample);
                                            editorStatus =list[i].istatus;
                                        }
                                        if(list[i].isupportLanguage==2){
                                            listLang.push(list[i].isupportLanguage);
                                            functionName.setValue(list[i].iname);
                                            functionDesc.setValue(list[i].idesc);
                                            // supportLanguage.items.items[Ext.decode(response.responseText).ilanguagetype-1].setValue(true);
                                            // console.log(list[i].functionContent)
                                            editorone.getDoc().setValue(list[i].functionContent);
                                            editorfOne.getDoc().setValue(list[i].functionExample);
                                            editorStatus =list[i].istatus;
                                        }
                                        if(list[i].isupportLanguage==1){
                                            listLang.push(list[i].isupportLanguage);
                                            functionName.setValue(list[i].iname);
                                            functionDesc.setValue(list[i].idesc);
                                            // supportLanguage.items.items[Ext.decode(response.responseText).ilanguagetype-1].setValue(true);
                                            editor.getDoc().setValue(list[i].functionContent);
                                            editorf.getDoc().setValue(list[i].functionExample);
                                            editorStatus =list[i].istatus;
                                        }

                                    }
                                    for(var i = 0; i <listLang.length ; i++){
                                        if(listLang[i]==3){
                                            supportLanguage.setValue({'ra_s_type_win':'3'});
                                        }else if(listLang[i]==2){
                                            supportLanguage.setValue({'ra_s_type_win':'2'});
                                        }
                                        else if(listLang[i]==1){
                                            supportLanguage.setValue({'ra_s_type_win':'1'});
                                        }else{
                                            supportLanguage.setValue({'ra_s_type_win':'1'});
                                        }
                                    }

                                },
                                failure: function (result, request) {
                                    secureFilterRs(result, "保存失败！");
                                }
                            });
                        }
                    }
                }
            })
            ]
        }]
    });
    if (dataIid.length <= 0) {
        supportLanguage.setValue({'ra_s_type_win': '1'});
    }
    /*var mainSec = Ext.create('Ext.panel.Panel', {
        region: 'north',
        border: true,
        layout: 'border',
        cls: 'window_border panel_space_left panel_space_right',
        height: contentPanel.getHeight() - 510,
        width:'100%',
        items: [formItems]
    });*/

    //写入div主方法
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "ScriptAddFunctionPage_grid_area",
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        bodyPadding: grid_margin,
        border: true,
        layout: 'border',
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        items: [formItems,
            {
                xtype: 'toolbar',
                dock: 'bottom',
                ui: 'footer',
                margin:'0 0 0 95',
                baseCls: 'customize_gray_back',
                items:[{
                    text: '保存',
                    cls: 'Common_Btn',
                    handler: function () {
                        saveFunctionData(false);
                    }
                },{
                    text: '返回',
                    cls: 'Common_Btn',
                    handler: function () {
                        //返回页面跳转
                        contentPanel.getLoader().load({
                            url: 'functionLibraryPage.do?classId='+classId
                        });
                    }
                }]
            }
        ]
    });
});

