<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
    <script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
    <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/css/handbook.css" />
</head>
<style type="text/css">
    /*置顶样式*/
    #form_top{display:block; bottom:3px; right:3px; position:fixed;}
	.idesctd{
		width:700px;
		word-wrap: break-word;
		word-break: break-all;
	}
	.idescbltd{
		width:380px;
		word-wrap: break-word;
		word-break: break-all;
	}
	.nametd{
		width: 260px;
		word-wrap: break-word;
		word-break: break-all;
	}
    /*.top_img{
        background-image: url("page/extjs/img/functionAndVariable/smjt.png");
        width: 40px;
        height:50px;
        cursor: pointer;
    }*/
</style>

<body>

<div class="handbook_box">
    <%--置顶--%>
    <div class="handbook_bg line_style">
    	<div class="handbook_cn property_02">
		    <a id="_top"></a>
		    <h1>函数及变量使用手册</h1>
		    <div class="search_box">
			    <input id="search" placeholder="作用域查询:可输入Agent IP 或资源组名称"/>
			    <button onclick="searchData()">查询</button>
		    </div>
	    </div>
    </div>
    <%--变量--%>
    <div class="space_box">
	    <div class="handbook_bg">
	    	<div class="handbook_cn property_01">
			    <h2>变量</h2>
			    <p>变量可以在脚本代码中直接使用，支持脚本语言:shell、python、powershell,平台中可用的变量如下：</p>
			    <table id="blTable" class="form_box"></table>
	    	</div>
	    </div>
	    <div class="handbook_bg" id="jbslId">
	    	<div class="handbook_cn property_01">
			    <h2>脚本示例:</h2>
			    <div class="script_box">
				    <span>#!/bin/bash</span>
				    <span>echo "ENTEGOR_SERVER_IP    = " $ENTEGOR_SERVER_IP</span>
				    <span>echo "ENTEGOR_SERVER_PORT  = " $ENTEGOR_SERVER_PORT</span>
			    </div>
		    </div>
	    </div>
	    
	    <div class="handbook_bg" id="jbscId">
	    	<div class="handbook_cn property_01">
			    <h2>脚本输出:</h2>
			    <div class="script_box">
				    <span>ENTEGOR_SERVER_IP    = "***********"</span>
				    <span>ENTEGOR_SERVER_PORT  = "8888"</span>
				    
				</div>
	    	</div>
	    </div>
	    <%--函数--%>
	    <div class="handbook_bg">
	    	<div class="handbook_cn property_01">
			    <h2>函数</h2>
			    <p>函数可以在脚本代码中直接使用，支持脚本语言:shell、python、powershell,平台中可用的函数如下：</p>
			    <table id="hsTable" class="form_box"></table>
		    </div>
	    </div>
	<%--函数用例区--%>
	<div id="functionExample"></div>
    </div>
</div>
</body>
<script type="text/javascript">
    //监听，回车事件
    $(document).keydown(function(e) {
        if (e.keyCode == 13) {
            searchData();
        }
    });
    $(document).ready(function() {
        //进入页面先加载一次查询
        searchData();
    });
    //置顶
    function form_top(){
        document.write('<div id="form_top"><a href="#_top" title="回到顶部"><font size="6">top</font></div></a></div>')
    }
    form_top();
    //查询方法
    function searchData(){
        //查询条件
        var searchVal = $("#search").val();
        //查询数据
        $.ajax({
            url: 'funcAndVarUseNoteBookQuery.do',
            type: "post",
            data: {"searchVal":searchVal},
            dataType:"text",
            success: function (data) {
                //返回的数据
                var newData = eval("("+data+")");
                /**
                 * 变量
                 */
                var varList = newData.blList;
                //清除table上次内容
                $('#blTable').empty();
                //追加表头
                $("#blTable").append("<tr><th>序号</th><th>变量名称</th><th width='75px'>变量类型</th><th>变量值</th><th>描述</th><th width='50px'>属性</th></tr>");
                //如果变量查询结果为空，不显示变量的脚本输出与脚本示例
				if(varList == null){
					$("#jbslId").hide();
					$("#jbscId").hide();
				}else {
					$("#jbslId").show();
					$("#jbscId").show();
				}
				//table追加数据
                $(varList).each(function (index){
                    var tdStr = "";
                    var tdStr2 = "";
                    //变量类型  1:数值2:字符串3:加密字符串4:数组5:字典
                    if("1" == varList[index].itype){
                        tdStr += "<td>数值</td>";
                    }
                    if("2" == varList[index].itype){
                        tdStr += "<td>字符串</td>";
                    }
                    if("3" == varList[index].itype){
                        tdStr += "<td>加密字符串</td>";
                    }
                    if("4" == varList[index].itype){
                        tdStr += "<td>数组</td>";
                    }
                    if("5" == varList[index].itype){
                        tdStr += "<td>字典</td>";
                    }
					if("6" == varList[index].itype){
						tdStr += "<td>4A密码类型</td>";
					}

                    $("#blTrId").append("<td>"+varList[index].ivalue+"</td>");
                    $("#blTrId").append("<td>"+varList[index].idesc+"</td>");
                    //属性 1:自定义2:内置
                    if("1" == varList[index].iattribute){
                        tdStr2 += "<td>自定义</td>";
                    }
                    if("2" == varList[index].iattribute){
                        tdStr2 += "<td>内置</td>";
                    }

					var blValue = varList[index].ivalue;
					if(blValue == null){
						blValue = '';
					}
					var msValue = varList[index].idesc;
					if(msValue == null){
						msValue = '';
					}

                    $("#blTable").append("<tr>"
                        +"<td>"+(index+1)+"</td>"
                        +"<td class='nametd'>"+varList[index].iname+"</td>"
                        +tdStr
                        +"<td class='idescbltd'>"+blValue+"</td>"
                        +"<td class='idescbltd'>"+msValue+"</td>"
                        +tdStr2
                    );
                    $("#blTable").append("</tr>");
                });

                /**
                 * 函数
                 */
                var funcList = newData.hsList;
                //清除table、用例上次内容
                $('#hsTable').empty();
                $("#functionExample").empty();
                //追加表头
                $("#hsTable").append("<tr><th>序号</th><th>函数名称</th><th>支持语言</th><th>说明</th><th>属性</th></tr>");
                //table追加数据
                $(funcList).each(function (index){
                    var tdStr = "";
                    //属性 1:自定义2:内置
                    if("1" == funcList[index].iattribute){
                        tdStr += "<td>自定义</td>";
                    }
                    if("2" == funcList[index].iattribute){
                        tdStr += "<td>内置</td>";
                    }
                    //描述换行
                    // var smLength = funcList[index].idesc.length;
                    // var rowNum = Math.ceil(smLength/50);
                    // var descStr = "<td>";
                    // for(var p=0 ; p<rowNum ; p++){
                    // 	if(p < rowNum-1){
					// 		descStr += (funcList[index].idesc).substr(p*50,p*rowNum+50)+"</br>";
					// 	}else {
					// 		descStr += (funcList[index].idesc).substr(p*50,smLength);
					// 	}
					// }
					// descStr += "</td>";
                    //函数列表
                    $("#hsTable").append("<tr>"
                        +"<td>"+(index+1)+"</td>"
                        +"<td class='nametd'>"+funcList[index].iname+"</td>"
                        +"<td>"+funcList[index].ilanguagetype+"</td>"
                        +"<td class='idesctd'>"+funcList[index].idesc+"</td>"
                        +tdStr);
                    $("#hsTable").append("</tr>");

                    //函数用例说明
                    $("#functionExample").append("<div class=\"handbook_bg\"><div class=\"handbook_cn property_01\"><h2>"+funcList[index].iname+"</h2>"+
                        "<ul><li>说明："+funcList[index].idesc+"</li>"+
                        "<li>语言："+funcList[index].ilanguagetype+"</li>"+
                        "<li>用例："+"</li></ul>"+
						"<div class=\"script_box\">"+
						funcList[index].functionExample+"</div></div></div>")
					;
					//PutRow函数特殊，需要图片说明
					if(funcList[index].iname == 'PutRow'){
						$("#functionExample").append("<div class=\"handbook_cn property_01\"><ul><li>· 相关配置：需要在脚本编写时配置输出定义。使用时列ID要与输出函数json参数中的“key”逐一对应，否则无法输出显示。</li></ul></div><div class=\"handbook_img\"><ul>"
								+"<img src =\"page/extjs/img/functionAndVariable/function1.png\" style=\"width: 1350px;height: 680px;\"/>"
								+"<img src =\"page/extjs/img/functionAndVariable/function3.png\" style=\"width: 1350px;height: 680px;\"/>"
								+"<img src =\"page/extjs/img/functionAndVariable/function4.png\" style=\"width: 1350px;height: 680px;\"/>"
								+"</ul></div>");
					}
                });
            }
        });
    }
</script>
</html>