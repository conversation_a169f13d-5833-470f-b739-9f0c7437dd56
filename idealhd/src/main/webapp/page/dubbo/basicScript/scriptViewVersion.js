var lastVal = null;
Ext.onReady(function () {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    var radio = 0;
    var mainPanel;
    var sysID;
    var tempmentIds = [];
    var busID;
    var threeBsTypeId;
    var warnningWin;
    var warnningGrid;
    var baseInfoOfScriptWin;
    var chooseTestAgentWin;
    var publishAuditingSMWin;
    var attachmentIds = [];
    var scriptName = "";
    var saveFromBottom = true;
    var countdown = 10;
    var tryRequestId = '';
    var tryAgentIp = '';
    var tryAgentPort = '';
    var newServiceId = 0;
    //var newUuid = "";
    var chosedAgentIds = [];
    var isFromTryATry = 0;
    var versioncheckRadio;
    var editor;
    var cmdVForbasicVersion = null;
    var scriptDesc = "";
    var chosedAppSys = new Array();
    var golbalParamName;
    var groupName;
    //存放标签
    var labels = '';
    //标签框
    var label;
    var labelss='';
    //编辑
    Ext.define('versionModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'uuid', type: 'string'},
            {name: 'onlyVersion', type: 'string'},
            {name: 'createTime', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'status', type: 'long'},
            {name: 'updateUserId', type: 'string'},
            {name: 'updateUserName', type: 'string'},
            {name: 'analyzeText', type: 'string'}
        ]
    });

    Ext.define('attaTempModelVersionShow', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    var versionStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'versionModel',
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceVersionListForAllScript.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    versionStore.on('beforeload', function (store, options) {
        var new_params = {
            serviceId: serviceId,
            flag: flag
        };

        Ext.apply(versionStore.proxy.extraParams, new_params);
    });

    versionStore.on('load', function (store, options) {
        versionGrid.getSelectionModel().select(0, true);
    });
    if (scriptTypeForVersion == 'sql') {
        radio = 4;
    }
    var versionColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '服务主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        }, {
            text: '服务主键uuid',
            dataIndex: 'uuid',
            width: 40,
            hidden: true
        }, {
            text: '状态',
            dataIndex: 'status',
            width: 50,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 2) {
                    backValue = "禁用";
                } else {
                    backValue = "启用";
                }
                return backValue;
            }
        }, {
            text: '版本',
            dataIndex: 'onlyVersion',
            width: 50,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }, {
            text: '创建时间',
            dataIndex: 'createTime',
            width: 160,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '修改者id',
            dataIndex: 'updateUserId',
            width: 100,
            hidden: modifyUserSwitch,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '修改者姓名',
            dataIndex: 'updateUserName',
            width: 100,
            hidden: modifyUserSwitch,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '操作',
            dataIndex: 'stepOperation',
            align: 'left',
            width: 120,
            hidden: analyzeSwitch,
            labelWidth: 150,
            renderer: displayWord,
            listeners: {
                click: function (a, b, c, d) {
                    var uuid = a.getStore().getAt(c).data.uuid;
                    var iid = a.getStore().getAt(c).data.iid;
                    //alert(enable);
                    toAnalyzeTextWin(uuid, iid);
                }
            }
        }
    ];
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
//			        mode: "SINGLE"
    });
    label = Ext.create('Ext.panel.Panel', {
        // region: 'south',
        border: false,
        columnWidth: 1,
        width: '100%',
        hidden: !labelSwitch,
        html: '<div class="report_box unclickable">' +
            '<div class="tagsinput-primary form-group" id="signDiv">' +
            '<label class="s_tit" ><span>标签:</span></label>' +
            '<input name="tagsinput" id="tagsinputval" class="tagsinput" data-role="tagsinput" value=""  disabled="disabled" >' +
            '</div>' +
            '</div>'
    });

    var versionGrid = Ext.create('Ext.grid.Panel', {
        width: 324,
        /*height : contentPanel.getHeight() - 140,*/
        store: versionStore,
        selModel:selModel,
        // selModel:selModel,
        cls: 'customize_panel_back panel_space_right',
        border: true,
        columnLines: true,
        columns: versionColumns,
        region: 'west',
        listeners: {
            select: function (me, record, index, eOpts) {
                newServiceId = record.get('iid');
                newUuid = record.get('uuid');
                //查询标签
                var labelStore = Ext.create('Ext.data.Store', {
                    autoLoad: true,
                    fields: ['label'],
                    proxy: {
                        type: 'ajax',
                        url: 'queryLabel.do',
                        reader: {
                            type: 'json',
                            root: 'dataList'
                        }
                    }
                });

                labelStore.on('beforeload', function (store, options) {
                    var new_params = {
                        iid: newServiceId
                    };
                    Ext.apply(labelStore.proxy.extraParams, new_params);
                });
                labelStore.on('load', function (store, records) {
                    var la1=new Ext.util.HashMap();
                    for (let i = 0; i < records.length; i++) {
                        la1.add(records[i].data.label, i);
                    }
                    // console.log($('#signDiv').children('div'));
                    //  console.log( $('#signDiv').children('div').val(""));
                    // $('#signDiv').children('span').val("");
                    // $("#tagsinputval").val("");
                    labelss=la1;
                    addTags(la1);
                });
                editScriptStore.load();
            },
            itemclick: function (me, record) {
                newServiceId = record.get('iid');
                newUuid = record.get('uuid');
                //查询标签
                var labelStore = Ext.create('Ext.data.Store', {
                    autoLoad: true,
                    fields: ['label'],
                    proxy: {
                        type: 'ajax',
                        url: 'queryLabel.do',
                        reader: {
                            type: 'json',
                            root: 'dataList'
                        }
                    }
                });

                labelStore.on('beforeload', function (store, options) {
                    var new_params = {
                        iid: newServiceId
                    };
                    Ext.apply(labelStore.proxy.extraParams, new_params);
                });
                labelStore.on('load', function (store, records) {
                    var la1=new Ext.util.HashMap();
                    for (let i = 0; i < records.length; i++) {
                        la1.add(records[i].data.label, i);
                    }
                    // console.log($('#signDiv').children('div'));
                    //  console.log( $('#signDiv').children('div').val(""));
                    // $('#signDiv').children('span').val("");
                    // $("#tagsinputval").val("");
                    labelss=la1;
                    addTags(la1);
                });
                editScriptStore.load();
            }
        },
        dockedItems: [
            {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: [
                    '->',
                    {
                        text: '脚本内容比对',
                        cls: 'Common_Btn',
                        handler: compareScript
                    }]
            }]
    });
    label.on("afterrender", function () {
        if ($("#tagsinputval").siblings('.bootstrap-tagsinput').length > 0 ) {
            //labelss.push($("#tagsinputval").parent('#signDiv'));
            //labelss.push($('#tagsinputval'));
            $("#tagsinputval").siblings('.bootstrap-tagsinput').remove();
            $('#tagsinputval').remove();
            //$(labelss[0]).append("<div class=\"bootstrap-tagsinput\"><input type=\"text\" placeholder=\"\"></div>");
            //$(labelss[0]).append("<input name=\"tagsinput\" id=\"tagsinputval\" class=\"tagsinput\" data-role=\"tagsinput\" value=\"\" disabled=\"disabled\" style=\"display: none;\">");
            //labelss=[];
        }
        if ($.fn.tagsinput) {
            addTags(labelss)
        }
    }, this);
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function (store, options) {
        var queryparams = {
            iid: newServiceId
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function (store, options, success) {
        var reader = store.getProxy().getReader();
        // Ext.getCmp("scriptpara").setValue(reader.jsonData.scriptPara);
        //scName.setValue(reader.jsonData.scriptName);
        $('#s-n-t-e-v').html(reader.jsonData.scriptName);

        if (reader.jsonData.scriptName) {
            mainP.setTitle(reader.jsonData.scriptName);
        } else {
            mainP.setTitle("编辑框");
        }
        var scriptT = reader.jsonData.scriptType;
        if (scriptT == 'sh') {
            FieldContainer.items.items[0].setValue(true);
            FieldContainer.items.items[0].setDisabled(false);
            versioncheckRadio = 0;
            editor.setOption("mode", 'shell');
        } else if (scriptT == 'bat') {
            FieldContainer.items.items[1].setValue(true);
            FieldContainer.items.items[1].setDisabled(false);
            versioncheckRadio = 1;
            editor.setOption("mode", 'bat');
        } else if (scriptT == 'py') {
            FieldContainer.items.items[3].setValue(true);
            FieldContainer.items.items[3].setDisabled(false);
            versioncheckRadio = 3;
            editor.setOption("mode", 'python');
        } else if (scriptT == 'sql') {
            FieldContainer.items.items[4].setValue(true);
            FieldContainer.items.items[4].setDisabled(false);
            versioncheckRadio = 4;
            editor.setOption("mode", 'text/x-plsql');
        } else if (scriptT == 'perl') {
            FieldContainer.items.items[2].setValue(true);
            FieldContainer.items.items[2].setDisabled(false);
            versioncheckRadio = 2;
            editor.setOption("mode", 'text/x-perl');
        } else if (scriptT == 'ps1') {
            FieldContainer.items.items[5].setValue(true);
            FieldContainer.items.items[5].setDisabled(false);
            versioncheckRadio = 6;
            editor.setOption("mode", 'powershell');
        }
        editor.setOption('value', reader.jsonData.content);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        groupName = parseInt(reader.jsonData.groupName);
        threeBsTypeId = parseInt(reader.jsonData.threeTypeId);
        scriptName = reader.jsonData.scriptName;
        sName.setValue(reader.jsonData.serviceName);
        timeout.setValue(reader.jsonData.timeout == -1 ? null : reader.jsonData.timeout);
        scName.setValue(reader.jsonData.scriptName);
        tableName.setValue(reader.jsonData.tableName);
        excepResult.setValue(reader.jsonData.excepResult);
        //预期类型，1、lastLine，2、exitCode
        if("1" == reader.jsonData.excepResultType){
            excepResultType.setValue("lastLine");
        }else {
            excepResultType.setValue("exitCode");
        }
        errExcepResult.setValue(reader.jsonData.errExcepResult);
        suUser.setValue(reader.jsonData.suUser);
        usePlantForm.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);
        ycCheckBeforeExec.setValue(reader.jsonData.checkBeforeExec);
        scriptWorkDir.setValue(reader.jsonData.scriptWorkDir);
        if (projectFlag == 1) {
            dbType.setValue(reader.jsonData.dbType);
            serviceType.setValue(reader.jsonData.serviceType);
            isExam.setValue(reader.jsonData.iisExam);

        }
        //判断功能分类开关是否开启
        //逻辑：开关开启并且功能分类为空、开关关闭以上两种情况均走之前的逻辑（直接加载一级、二级分类）
        //逻辑：开关开启并且功能分类不为空，加载功能分类、一级分类、二级分类
        if (sdFunctionSortSwitch && groupName != 'undefined' && groupName != null && groupName != '') {
            groupNameStore.load();
        } else{
            bussData.load()
        }
        attachmentStore.load();
        paramStore.load();
        testparamStore.load();
    });

    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    groupNameStore.on('load', function (store, options) {
        if (groupName) {
            groupNameCombo.setValue(groupName);
            bussData.load({
                params: {
                    fk: groupName
                }
            });
        }
    });
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussData.on('load', function (store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load', function (store, options) {
        bussTypeCb.setValue(busID);
    });

    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussTypeData.on('load', function (store, options) {
        if (!Ext.isEmpty(threeBsTypeId) && !isNaN(threeBsTypeId)) {
            threeBussTypeCb.setValue(threeBsTypeId);
        }
    });


    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        readOnly: true
    });
    var bussCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });
    // //查询标签
    // var labelStore = Ext.create('Ext.data.Store', {
    //     autoLoad: true,
    //     fields: ['label'],
    //     proxy: {
    //         type: 'ajax',
    //         url: 'queryLabels.do',
    //         reader: {
    //             type: 'json',
    //             root: 'dataList'
    //         }
    //     }
    // });
    //
    // labelStore.on('beforeload', function (store, options) {
    //     var new_params = {
    //         scriptuuid: newUuid
    //     };
    //     Ext.apply(labelStore.proxy.extraParams, new_params);
    // });
    // labelStore.on('load', function (store, records) {
    //     for (let i = 0; i < records.length; i++) {
    //         console.log(records[i].data.label);
    //         labels += records[i].data.label + ' ';
    //     }
    //     label.setValue(labels);
    // });

    // label = Ext.create('Ext.ux.ideal.form.ComboBox', {
    //     name: 'label',
    //     labelWidth: 70,
    //     height: 68,
    //     columnWidth: 1,
    //     queryMode: 'local',
    //     fieldLabel: '标签',
    //     displayField: 'label',
    //     valueField: 'label',
    //     readOnly: true,
    //     store: labelStore
    // });
    // label = Ext.create('Ext.panel.Panel', {
    //     // region: 'south',
    //     border: false,
    //     columnWidth: 1,
    //     width: '100%',
    //     hidden: !labelSwitch,
    //     html: '<div class="report_box unclickable">' +
    //         '<div class="tagsinput-primary form-group" id="signDiv">' +
    //         '<label class="s_tit" ><span>标签:</span></label>' +
    //         '<input name="tagsinput" id="tagsinputval" class="tagsinput" data-role="tagsinput" value=""  disabled="disabled" >' +
    //         '</div>' +
    //         '</div>'
    // });
    //
    // label.on("afterrender", function () {
    //     //console.log($("#tagsinputval"),$("#tagsinputval").siblings('.bootstrap-tagsinput'));
    //     if ($("#tagsinputval").siblings('.bootstrap-tagsinput').length > 0) {
    //         $("#tagsinputval").siblings('.bootstrap-tagsinput').remove();
    //         $('#tagsinputval').remove();
    //
    //     }
    //     if ($.fn.tagsinput) {
    //
    //         $("#tagsinputval").tagsinput();
    //     }
    //
    //     function setLabel() {
    //         if (labelEdit != null && labelEdit != '') {
    //             var labs = labelEdit.split(",")
    //             for (var i = 0; i < labs.length; i++) {
    //                 la.add(labs[i], i);
    //             }
    //             addTags(la)
    //         }
    //     }
    //     setLabel();
    // }, this);
    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选二级分类--',
        store: bussTypeData,
        listeners: {
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value !== null) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            }
        }
    });

    var threeBussTypeCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        hidden: !scriptThreeBstypeSwitch,
        valueField: 'threeBsTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选三级分类--',
        store: threeBussTypeData
    });

    /** *********************Panel********************* */
    var FieldContainer = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 70,
        name: 'ra_s_view_type',
        padding: '0 5 10 5',
        items: [{
            name: 'ra_s_view_type',
            width: 80,
            inputValue: '0',
            boxLabel: 'shell',
            checked: true,
            disabled: true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function (value) {
                        if (versioncheckRadio != 0) {
                            editor.setOption("mode", 'shell');
                            versioncheckRadio = 0;
                            editor.isEmpty("");
                        }
                    }
                }
            }
        },
            {
                name: 'ra_s_view_type',
                width: 80,
                inputValue: '1',
                boxLabel: 'bat',
                disabled: true,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            if (versioncheckRadio != 1) {
                                versioncheckRadio = 1;
                                editor.setOption("mode", 'bat');
                                editor.isEmpty("");
                            }
                        }
                    }
                }
            },
            {
                name: 'ra_s_view_type',
                width: 80,
                inputValue: '2',
                boxLabel: 'perl',
                disabled: true,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            versioncheckRadio = 2;
                            editor.setOption("mode", 'text/x-perl');
                        }
                    }
                }
            },
            {
                name: 'ra_s_view_type',
                width: 80,
                inputValue: '3',
                boxLabel: 'python',
                disabled: true,
                listeners: {
                    click: {
                        element: 'el',
                        fn: function (value) {
                            versioncheckRadio = 3;
                            editor.setOption("mode", 'python');
                        }
                    }
                }
            },
            {
                name: 'ra_s_view_type',
                width: 80,
                inputValue: '4',
                boxLabel: 'sql',
                disabled: true,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            versioncheckRadio = 4;
                            editor.setOption("mode", 'text/x-plsql');
                        }
                    }
                }
            }, {
                name: 'ra_s_view_type',
                width: 100,
                inputValue: '6',
                boxLabel: 'powershell',
                disabled: true,
                listeners: {
                    click: {
                        element: 'el',
                        // bind to the
                        fn: function (value) {
                            versioncheckRadio = 6;
                            editor.setOption("mode", 'powershell');
                        }
                    }
                }
            }]
    });
    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });

    var usePlantForm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });

    var scriptWorkDir = new Ext.form.TextField({
        name: 'scriptWorkDir',
        fieldLabel: '工作目录',
        editable: false,
        readOnly: true,
        hidden: !scriptWorkDirSwitch,
        displayField: 'scriptWorkDir',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });

    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });

    var excepResultType = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期类型',
        displayField: 'excepResultType',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });

    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var suUser = new Ext.form.TextField({
        name: 'suUser',
        fieldLabel: '启动用户',
        displayField: 'suUser',
        readOnly: true,
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });

    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        hidden: true,
        // fieldLabel: '功能说明',
        displayField: 'funcdesc',
        emptyText: '',
        // labelWidth: 70,
        // padding: '0 5 0 0',
        readOnly: true,
        columnWidth: 1,
        height: 130,
        autoScroll: true,
        listeners: {
            'blur': function (me, e, eOpts) {
                scriptDesc = me.getValue();
                funcDescInWin.setValue(scriptDesc);
            }
        }
    });
    var dbType = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'dbType',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '数据库类型',
        padding: '0 5 0 0',
        editable: false,
        readOnly: true,
        displayField: 'text',
        valueField: 'value',
        value: '1',
        emptyText: '--请选择数据库类型--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', 'ORACLE'], ['2', 'DB2'],
                ['3', 'MYSQL']]
        })
    });
    // 服务类型
    var serviceType = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'serviceType',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '服务类型',
        padding: '0 5 0 0',
        editable: false,
        readOnly: true,
        displayField: 'text',
        valueField: 'value',
        value: '1',
        emptyText: '--请选择服务类型--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['0', '应用'], ['1', '采集']]
        })
    });
    // 发起审核
    var isExam = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'isExam',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '发起审核',
        padding: '0 5 0 0',
        editable: false,
        readOnly: true,
        displayField: 'text',
        valueField: 'value',
        emptyText: '--请选择--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '是'], ['0', '否']]
        })
    });
    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
        name: 'funcDescInWin',
        fieldLabel: '功能说明',
        displayField: 'funcDescInWin',
        emptyText: '请输入功能说明...',
        labelWidth: 70,
        height: 136,
        readOnly: true,
        columnWidth: 1,
        autoScroll: true
    });

    var ycCheckBeforeExec =  Ext.create('Ext.form.field.Checkbox',{
        hidden:!checkBeforeExecSwitch,
        margin:0,
        boxLabel: '先试点，后批量原则',
        readOnly:true
    })

    var buttonFormPanel = Ext.create('Ext.form.FieldContainer', {
        defaultType: 'checkboxfield',
        items: [
            ycCheckBeforeExec
        ]
    })

    var timeout = new Ext.form.TextField({
        name: 'timeout',
        fieldLabel: '超时(秒)',
        displayField: 'timeout',
        emptyText: '',
        hidden: !scriptTimeoutSwitch,
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var tableName = new Ext.form.TextField({
        name: 'tableName',
        fieldLabel: '表名称',
        displayField: 'tableName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        readOnly: true,
        columnWidth: .5
    });
    if (projectFlag == 1) {
        bussCb.hidden = true;
        bussTypeCb.hidden = true;
        threeBussTypeCb.hidden = true;
        excepResult.hidden = true;
        errExcepResult.hidden = true;
    } else {
        dbType.hidden = true;
        serviceType.hidden = true;
        isExam.hidden = true;
        tableName.hidden = true;
    }
    var tempItems = [{
        border: false,
        layout: 'column',
        margin: '5',
        items: [groupNameCombo, bussCb, bussTypeCb]
    }, {
        layout: 'column',
        border: false,
        margin: '5',
        items: [threeBussTypeCb, timeout]
    }, {
        border: false,
        layout: 'column',
        margin: '5',
        items: [sName, scName]
    },{
        layout: 'column',
        border: false,
        margin: '5',
        items: [excepResultType]
    },{
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResult, errExcepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm, suUser]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [scriptWorkDir]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [label]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDesc]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDescInWin]
        }];
    if (projectFlag == 1) {
        tempItems = [{
            border: false,
            layout: 'column',
            margin: '5',
            items: [bussCb, bussTypeCb]
        }, {
            border: false,
            layout: 'column',
            margin: '5',
            items: [sName, scName]
        },
            {
                border: false,
                layout: 'column',
                margin: '5',
                items: [dbType, serviceType]
            },
            {
                layout: 'column',
                border: false,
                margin: '5',
                items: [excepResult, errExcepResult]
            },
            {
                layout: 'column',
                border: false,
                margin: '5',
                items: [usePlantForm, scriptWorkDir, isExam]
            },
            {
                layout: 'column',
                border: false,
                margin: '5',
                items: [tableName]
            },
            {
                layout: 'column',
                border: false,
                margin: '5',
                items: [funcDescInWin]
            }]
    }
    var scriptForm = Ext.create('Ext.ux.ideal.form.Panel', {
        width: 590,
        height: 490,
        border: false,
        layout: 'anchor',
        // margin: 10,
        collapsible: false,
        // title: '基本信息',
        items: tempItems,
        overflowY:'auto'
    });

    var funcDescForm = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        // height: 168,
        border: true,
        layout: 'anchor',
        collapsible: false,
        margin: '0 0 8 0',
        cls: 'customize_panel_back',
        title: '基本信息',
        items: [{
            hidden: true,
            layout: 'column',
            border: false,
            items: [funcDesc]
        }],
        tools: [{
            type: 'print',
            tooltip: '基本信息',
            handler: function (event, toolEl, panelHeader) {
                scriptDesc = funcDesc.getValue();
                if (!scName.getValue()) {
                    scName.setValue(scriptName);
                }
                // if (!funcDescInWin.getValue()) {
                //     funcDescInWin.setValue(scriptDesc);
                // }
                //切换不同版本，清空功能说明，重新赋值
                funcDescInWin.setValue('');
                funcDescInWin.setValue(scriptDesc);
                saveFromBottom = false;
                baseInfoOfScriptWin.show();
            }
        }
//		        ,{
//		            type:'help',
//		            tooltip: '帮助',
//		            callback: function(panel, tool, event) {
//		            	window.open("scriptHelpDoc.do","resizable=yes").focus();
//		            }
//		        }
        ]
    });

    if (!baseInfoOfScriptWin) {
        baseInfoOfScriptWin = Ext.create('widget.window', {
            title: '基本信息',
            closable: true,
            closeAction: 'hide',
            modal: true,
            width: 640,
            minWidth: 350,
            height: 610,
            layout: {
                type: 'border',
                padding: 5
            },
            items: [scriptForm],
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'bottom',
                layout: {pack: 'center'},
                items: [{
                    xtype: "button",
                    text: "确定",
                    cls: 'Common_Btn',
                    handler: function () {
                        if (saveFromBottom) {
                            save(0);
                        } else {
                            this.up("window").close();
                        }
                    }
                }, {
                    xtype: "button",
                    text: "取消",
                    cls: 'Common_Btn',
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            },{
                xtype: 'toolbar',
                dock:'bottom',
                layout: {
                    pack: 'center'
                },
                items:[buttonFormPanel]
            }]
        });
    }
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'paramType',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            },
            {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'ruleName',
                type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }]
    });

    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });
    Ext.define('paramRuleModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'paramRuleIn',
            type: 'string'
        }, {
            name: 'paramRuleType',
            type: 'int'
        }, {
            name: 'paramRuleLen',
            type: 'int'
        }, {
            name: 'paramRuleOut',
            type: 'string'
        }, {
            name: 'paramRuleDesc',
            type: 'string'
        }, {
            name: 'paramRuleOrder',
            type: 'int'
        }, {
            name: 'dataList',
            type: 'string'
        }]
    });
    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    attachmentStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    Ext.define('paramManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
            {
                name: 'parameterName',
                type: 'string'
            },
            {
                name: 'parameterValue',
                type: 'string'
            },
            {
                name: 'parameterDesc',
                type: 'string'
            }]
    });
    var enumValueStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        // autoDestroy: true,
        // pageSize: 10,
        model: 'paramManangerModel',
        proxy: {
            type: 'ajax',
            url: 'getParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                // totalProperty: 'total'
            }
        }
    });
    Ext.define('paramManangerModel2', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'paravalue',
                type: 'string'
            }]
    });
    var defaultValueStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'paramManangerModel2',
        proxy: {
            type: 'ajax',
            url: 'getScriptParameterList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    defaultValueStore.on('beforeload', function (store, options) {
        var new_params = {
            paramName: golbalParamName
        };
        Ext.apply(defaultValueStore.proxy.extraParams, new_params);
    });
    var defultEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true
        })
    });
    var passwordEditor = Ext.create('Ext.grid.CellEditor', {
        field: Ext.create('Ext.form.field.Text', {
            selectOnFocus: true,
            inputType: 'password'
        })
    });
    var paramDesc='描述';
    if(fjFlag){
        paramDesc='参数名称';
    }
    var paramColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 60,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                if (value == 'IN-string(加密)') {
                    coun = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    coun = record.get('paramDefaultValue');
                }
                let ruleMsg = bhParameterCheckSwitch ? "<br>验证规则：" + record.get('ruleName') : "";
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>枚举名：" + record.get('parameterName')
                    + "<br>默认值：" + coun
                    + ruleMsg
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        }, {
            dataIndex: 'parameterName',
            width: 80,
            text: '枚举名称',
            editable: false,
            //editor: {},
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                coun = record.get('parameterName');

                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>默认值：" + coun
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';
                return coun;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramDefaultValue',
            width: 80,
            text: '默认值',
            //editor: {},
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                let showValue = value;
                let paramType = record.get('paramType');
                if (paramType == 'IN-string(加密)') {
                    let xing = "";
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }

        }, {
            dataIndex: 'ruleName',
            width: 120,
            text: '验证规则',
            hidden: !bhParameterCheckSwitch
            // editor: {
            //     xtype: 'combobox',
            //     store: store,
            //     queryMode: 'local',
            //     displayField: 'ruleName',
            //     valueField: 'ruleName',
            //     editable: false,
            // }
        },
        {
            text: paramDesc,
            dataIndex: 'paramDesc',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var coun = '';
                if (record.get('paramType') == 'IN-string(加密)') {
                    coun = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    coun = record.get('paramDefaultValue');
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + value
                    + "<br>默认值：" + coun
                    + "<br>排    序：" + record.get('paramOrder')
                    + "<br>描    述：" + record.get('paramDesc'))
                    + '"';
                return value;
            }
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 50,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(record.get('paramDefaultValue'));
                } else {
                    backValue = record.get('paramDefaultValue');
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>默认值：" + backValue
                    + "<br>排序：" + record.get('paramOrder')
                    + "<br>描述：" + record.get('paramDesc'))
                    + '"';

                return value;
            }
        }];


    var attachmentColumns = [{
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    }, {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer: function (value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    }, {
        menuDisabled: true,
        sortable: false,
        xtype: 'actioncolumn',
        width: 50,
        items: [
            {
                iconCls: 'script_download',
                tooltip: '下载',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attachmentStore.getAt(rowIndex);
                    //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                    window.location.href = 'downloadScriptAttachment.do?iid=' + rec.get('iid');
                }
            }]
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var cellEditing3 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var paramGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        margin: '-3 0 0 0',
        title: "脚本参数",
        cls: 'customize_panel_back',
        store: paramStore,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns,
        listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {

                var columnIndex = e.column.dataIndex;
                // 点击的当前行数据
                var recordData = e.record.data;

                var paramType = recordData.paramType;           // 是否为枚举类型
                var parameterName = recordData.parameterName;   // 参数名称
                // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue";
                var columnBooParameterName = columnIndex == "parameterName";
                var columnBooparamDefaultValue = columnIndex == "paramDefaultValue"
                // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                    golbalParamName = parameterName;
                    defaultValueStore.load();
                }
                // 判断如果为枚举类型，并且当前操作列为“参数名称”，设置单元格为下拉框
                if (paramType == "枚举" && columnBooParameterName) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "parameterName",
                        displayField: "parameterName",
                        store: enumValueStore,
                        editable: false,
                        listeners: {
                            change: function (field, newValue, oldValue) {
                                if (oldValue != newValue) {
                                    var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                                    paramDefaultValue.set("paramDefaultValue", "");
                                    s
                                }
                            }
                        }
                    });
                }
                if (paramType == "枚举" && columnBooparamDefaultValue) {
                    e.column.setEditor({
                        xtype: 'combobox',
                        valueField: "paravalue",
                        displayField: "paravalue",
                        store: defaultValueStore,
                        editable: false
                    });

                }
                // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                if (paramType != "枚举" && columnBoo) {
                    e.column.setEditor({
                        xtype: 'textfield',
                        readOnly: columnIndex == "parameterName" ? true : false,

                    })
                }

                if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {
                    let pass = new Ext.form.TextField({
                        inputType: 'password'
                    });

                    e.column.setEditor(pass)
                }
            }
        }
    });

    var attachmentGrid = Ext.create('Ext.grid.Panel', {
        region: 'north',
        cls: 'window_border panel_space_top panel_space_right panel_space_left panel_space_bottom ',
        border: true,
        layout: 'border',
        margin: '5 0 0 0',
        height: 190,
        emptyText: '没有附件',
        store: attachmentStore,
        columnLines: true,
        columns: attachmentColumns
//		        dockedItems: [{
//		            xtype: 'toolbar',
//		            height: 45,
//		            items: [{
//		            	xtype: 'tbtext', id: 's-n-t-e-v', text: '.'
//		            }]
//		        }]
    });

    //获取所有模板
    var attaTempStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attaTempModelVersionShow',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttaTemplate.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    /********************/
    attaTempStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid,
            ids: tempmentIds
        };
        Ext.apply(attaTempStore.proxy.extraParams, new_params);
    });
    attaTempStore.on('load', function (me, records, successful, eOpts) {
        tempmentIds = [];
        $.each(records, function (index, record) {
            tempmentIds.push(record.get('iid'));
        });
    });
    /********************/

    var tempColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '模板名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [
                {
                    iconCls: 'script_download',
                    tooltip: '下载',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attaTempStore.getAt(rowIndex);
                        //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
                        window.location.href = 'downloadScriptAttaTemplate.do?iid=' + rec.get('iid');
                    }
                }
            ]
        }];

    var attachmentGrid2 = Ext.create('Ext.grid.Panel', {
        region: 'north',
        cls: 'window_border panel_space_top panel_space_right panel_space_left panel_space_bottom ',
        border: true,
        layout: 'border',
        margin: '5 0 0 0',
        height: 190,
        emptyText: '没有附件',
        store: attaTempStore,
        columnLines: true,
        columns: tempColumns
    });

    var pagetab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            region: 'center',
            activeTab: 0,
            height: contentPanel.getHeight() * 0.25,
            border: false,
            items: [
                {
                    title: '附件',
                    layout: 'fit',
                    items: [attachmentGrid]
                },
                {
                    title: '模板',
                    layout: 'fit',
                    hidden: !templateSwitch,
                    items: [attachmentGrid2]
                }
            ]
        });

    var templateGrid = Ext.create('Ext.panel.Panel', {
        region: 'south',
        cls: 'attachments customize_panel_back panel_space_top',
        items: [pagetab]

    });

    var paramRulesStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramRuleModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    paramRulesStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid,
            iflag: "0"
        };

        Ext.apply(paramRulesStore.proxy.extraParams, new_params);
    });
    var paramRuleTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name', 'id'],
        data: [{
            "name": "VARCHAR",
            "id": 0
        }, {
            "name": "INTEGER",
            "id": 1
        }, {
            "name": "DECIMAL",
            "id": 2
        }, {
            "name": "TIMESTAMP",
            "id": 3
        }, {
            "name": "CLOB",
            "id": 4
        }, {
            "name": "DATE",
            "id": 5
        }, {
            "name": "LONG",
            "id": 6
        }]
    });
    var paramRuleCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
        store: paramRuleTypeStore,
        queryMode: 'local',
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'id',
        triggerAction: "all"
    });
    var paramRulesColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '顺序',
            dataIndex: 'paramRuleOrder',
            width: 50,
            editor: {
                allowBlank: false,
                xtype: 'numberfield',
                maxValue: 30,
                minValue: 1
            },
            renderer: function (value, metaData, record, rowIdx,
                                colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String
                        .htmlEncode(/*
     												 * " 输入：" +
     												 * record.get('paramRuleIn') + "<br>
     												 */"输出列名称："
                            + record
                                .get('paramRuleOut')
                            + "<br>排序："
                            + record
                                .get('paramRuleOrder')
                            + "<br>别名："
                            + record
                                .get('paramRuleDesc'))
                    + '"';
                return value;
            }
        }, {
            text: '分隔符',
            dataIndex: 'paramRuleIn',
            width: 85,
            editor: {},
            hidden: true
        }, {
            text: '输出列名称',
            dataIndex: 'paramRuleOut',
            width: 140,
            editor: {
                allowBlank: false
            }
        },
        {
            text: '类型',
            dataIndex: 'paramRuleType',
            width: 85,
            editor: paramRuleCombo,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="'
                    + Ext.String.htmlEncode(" 类型：" + record.get('paramRuleType')) + '"';
                if (value == 0) {
                    value = "VARCHAR";
                } else if (value == 1) {
                    value = "INTEGER";
                } else if (value == 2) {
                    value = "DECIMAL";
                } else if (value == 3) {
                    value = "TIMESTAMP";
                } else if (value == 4) {
                    value = "CLOB";
                } else if (value == 5) {
                    value = "DATE";
                } else if (value == 6) {
                    value = "LONG";
                } else {
                    value = "VARCHAR"
                }
                return value;
            }
        }, {
            text: '长度',
            dataIndex: 'paramRuleLen',
            width: 85,
            value: 50,
            editor: {
                xtype: 'numberfield',
                maxValue: 4000,
                minValue: 1
            }
        }, {
            text: '别名',
            dataIndex: 'paramRuleDesc',
            flex: 1,
            editor: {
                allowBlank: true
            }
        }];
    var outruleGrid = Ext.create('Ext.grid.Panel', {
        region: 'south',
        margin: '5 0 0 0',
        cls: 'customize_panel_back',
        height: 190,
        title: '输出规则',
//		        plugins : [ cellEditing2 ],//设置为不可编辑
        emptyText: '没有输出规则',
        store: paramRulesStore,
        border: true,
        columnLines: true,
        columns: paramRulesColumns
    });
    if (projectFlag == 1) {
        attachmentGrid.hidden = true;
    } else {
        outruleGrid.hidden = true;
    }
    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
        region: 'east',
        collapsible: false,
        border: false,
        width: 450,
        layout: {
            type: 'border'
        },
        items: [funcDescForm, paramGrid, templateGrid, outruleGrid]
    });

    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        }, {
            name: 'name',
            type: 'string'
        }, {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    resourceGroupStore.on('load', function () {
        var ins_rec = Ext.create('resourceGroupModel', {
            id: '-1',
            name: '未分组',
            description: ''
        });
        resourceGroupStore.insert(0, ins_rec);
    });

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'appName', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentDesc', type: 'string'},
            {name: 'agentState', type: 'int'}
        ]
    });

    var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agent_store,
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });
    var resourceGroupObj = Ext.create('Ext.ux.ideal.form.ComboBox',
        {
            fieldLabel: '资源组',
            emptyText: '--请选择资源组--',
            hidden: removeAgentSwitch,
            labelAlign: 'right',
            labelWidth: 70,
            width: '25.5%',
            columnWidth: 1,
            multiSelect: true,
            store: resourceGroupStore,
            displayField: 'name',
            valueField: 'id',
            triggerAction: 'all',
            editable: false,
            mode: 'local',
            listeners: {
                change: function (comb, newValue, oldValue, eOpts) {
                    /*
									 * chosedResGroups_forest = new Array(); for(var
									 * i=0;i<newValue.length;i++) {
									 * chosedResGroups_forest.push(newValue[i]); }
									 */
                    // agent_store.load();
                    agent_grid.ipage.moveFirst();
                }
            }
        });

    var sys_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'sysNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentSysNameList.do?envType=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var sys_name = Ext.create('Ext.ux.ideal.form.ComboBox', {
//	    editable: false,
        name: 'sysname',
        fieldLabel: "名称",
        hidden: !CMDBflag,
        emptyText: '--请选择名称--',
        store: sys_name_store,
        queryMode: 'local',
        width: "25%",
        displayField: 'sysName',
        valueField: 'sysName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '计算机名',
        displayField: 'hostname',
        emptyText: '--请输入计算机名--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });

    var app_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'appNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentAppNameList.do?envType=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var app_name = Ext.create('Ext.ux.ideal.form.ComboBox', {
//	        editable: false,
        name: 'appname',
        fieldLabel: "应用名称",
        emptyText: '--请选择应用名称--',
        hidden: !CMDBflag,
        store: app_name_store,
        queryMode: 'local',
        width: "25%",
        displayField: 'appName',
        valueField: 'appName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });

    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '操作系统',
        displayField: 'ostype',
        emptyText: '--请输入操作系统--',
        labelWidth: 70,
        labelAlign: 'right',
        width: CMDBflag ? '25%' : '24.2%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });

    var agentStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "0", "name": "正常"},
            {"id": "1", "name": "异常"},
            {"id": "2", "name": "升级中"}
        ]
    });
    var agent_ip = new Ext.form.TextField({
        name: 'agentIp',
        fieldLabel: 'Agent IP',
        displayField: 'agentIp',
        emptyText: '--请输入Agent IP--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.5%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });

    var agentStatusCb = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'agentStatus',
        labelWidth: 79,
        queryMode: 'local',
        fieldLabel: 'Agent状态',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择Agent状态--',
        store: agentStatusStore,
        width: '25.2%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    agent_grid.ipage.moveFirst();
                }
            }
        }
    });

    var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: searcformquery,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [sys_name, app_name, host_name, os_type]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            items: [agent_ip, resourceGroupObj, agentStatusCb, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function () {
                    agent_grid.ipage.moveFirst();
                }
            }, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function () {
                    agent_ip.setValue('');
                    app_name.setValue('');
                    sys_name.setValue('');
                    host_name.setValue('');
                    os_type.setValue('');
                    resourceGroupObj.setValue('');
                    agentStatusCb.setValue('');
                }
            }, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查看已选择服务器',
                hidden: true,
                handler: function () {
                    if (!chosedAgentWin) {
                        chosedAgentWin = Ext.create(
                            'Ext.window.Window', {
                                title: '已选择服务器',
                                autoScroll: true,
                                modal: true,
                                resizable: false,
                                closeAction: 'hide',
                                width: contentPanel
                                        .getWidth()
                                    - 250,
                                height: 530,
                                items: [agent_grid_chosed],
                                buttonAlign: 'center',
                                buttons: [{
                                    xtype: "button",
                                    text: "关闭",
                                    handler: function () {
                                        this.up("window")
                                            .close();
                                    }
                                }]
                            });
                    }
                    chosedAgentWin.show();
                    agent_store_chosed.load();
                }
            }]
        }]
    });


    Ext.define('dsModel', {
        extend: 'Ext.data.Model',
        idProperty: 'dsId',
        fields: [
            {name: 'dsId', type: 'int'},
            {name: 'dsIp', type: 'String'},
            {name: 'dsName', type: 'String'},
            {name: 'dsUser', type: 'String'},
            {name: 'dsPwd', type: 'String'},
            {name: 'dsRole', type: 'String'},
            {name: 'dsIns', type: 'String'},
            {name: 'dsUrl', type: 'String'},
            {name: 'dsType', type: 'String'}
        ]
    });

    var dsinfo_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'dsModel',
        proxy: {
            type: 'ajax',
            url: 'getDsInfoByDsId.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', hidden: !CMDBflag, width: 70},
        {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
        {text: '计算机名', dataIndex: 'hostName', width: 120},
        {text: '操作系统', dataIndex: 'osType', width: 140},
        {text: 'IP', dataIndex: 'agentIp', width: 90},
        {text: '端口号', dataIndex: 'agentPort', width: 90},
        {text: '描述', dataIndex: 'agentDesc', flex: 1, hidden: true},
        {
            text: '状态', dataIndex: 'agentState', flex: 1, renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }
    ];
    var ds_columns = [
        {text: '主键', dataIndex: 'dsId', hidden: true},
        {text: 'DSIP', dataIndex: 'dsIp', hidden: true},
        {text: '数据源名称', dataIndex: 'dsName', width: 100},
        {text: '用户', dataIndex: 'dsUser', width: 90},
        {text: '密码', dataIndex: 'dsPwd', hidden: true},
        {text: '角色', dataIndex: 'dsRole', hidden: true},
        {text: '实例名', dataIndex: 'dsIns', hidden: true},
        {text: '类型', dataIndex: 'dsType', width: 50},
        {text: 'URL', dataIndex: 'dsUrl', flex: 1}
    ];
    agent_store.on('beforeload', function (store, options) {
        var new_params = {
            agentIp: Ext.util.Format.trim(agent_ip.getValue()),
            appName: app_name.getValue() == null ? '' : Ext.util.Format.trim(app_name.getValue() + ""),
            sysName: sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + ""),
            hostName: Ext.util.Format.trim(host_name.getValue()),
            osType: Ext.util.Format.trim(os_type.getValue()),
            rgIds: resourceGroupObj.getValue(),
            agentState: agentStatusCb.getValue(),
            flag: 0,
            switchFlag: projectFlag
        };

        Ext.apply(agent_store.proxy.extraParams, new_params);
    });


    var agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        //width:'100%',
        store: agent_store,
        border: true,
        columnLines: true,
        columns: agent_columns,
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        //bbar: pageBar,
        // 	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        listeners: {
            select: function (e, record, index, eOpts) {
                if (chosedAgentIds.indexOf(record.get('iid')) == -1) {
                    chosedAgentIds.push(record.get('iid'));
                }
                if (versioncheckRadio == 4) {
                    var cpid = record.get('iid');
                    dsinfo_store.reload({
                        params: {cpid: cpid, switchFlag: projectFlag}  //参数
                    });

                }
            },
            deselect: function (e, record, index, eOpts) {
                if (chosedAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedAgentIds.remove(record.get('iid'));
                }
                if (versioncheckRadio == 4) {
                    var cpid = record.get('iid');
                    dsinfo_store.reload({
                        params: {cpid: cpid}  //参数
                    });

                }
            }
        }
    });
    agent_store.on('load', function (store, options) {
        agent_grid.getSelectionModel().select(0);
    });
    var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        mode: "SINGLE"
    });
    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
        store: dsinfo_store,
        width: '40%',
        region: 'east',
        border: true,
        columnLines: true,
        columns: ds_columns,
        selModel: selModel2
    });
    dsinfo_store.on('load', function (store, options) {
        db_soucre_grid.getSelectionModel().select(0);
    });
//		    function clearQueryWhere(){
//		    	resourceGroupObj.setValue('');
//		    	search_form.getForm().findField("agentIp").setValue('');
//		    	//search_form.getForm().findField("agentDesc").setValue('');
//		    }

    function settime(btn, text) {
        if (countdown == 0) {
            btn.setDisabled(false);
            btn.setText(text);
            btn.setWidth(80);
            countdown = 10;
        } else {
            btn.setDisabled(true);
            btn.setText(text + "(" + countdown + ")");
            btn.setWidth(100);
            countdown--;
            setTimeout(function () {
                settime(btn, text)
            }, 1000)
        }

    }

    var cmdStr = new Ext.form.TextField({
        width: '95%',
        labelWidth: 70,
        margin: '5 0 5 0',
        fieldLabel: 'CMD',
        listeners: {
            specialkey: function (textfield, e) {
                if (e.getKey() == Ext.EventObject.ENTER) {
                    cmdVForbasicVersion = cmdStr.getValue();
                    cmdStr.setValue("");
                }
            }
        }
    });
    if (projectFlag == 1) {
        cmdStr.hidden = true;
    }
    var mainP = Ext.create('Ext.panel.Panel', {
// width: '100%',
// margin: 10,
        region: 'center',
        /*minHeight: 120,*/
        border: true,
        collapsible: false,
        autoScroll: true,
        title: "编辑框",
        /*height: contentPanel.getHeight()-,*/
        html: '<textarea id="code-edit-view" value style="height:100%;"></textarea>',
        tbar: [FieldContainer],
        bbar: []
    });

    var consolePanel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        height: 110,
        border: false,
        collapsible: false,
        autoScroll: true,
        html: '<pre id="consoleLog-edit-view" style="height:100%;background: #4b4b4b;color: white;margin:0;"></pre>'
    });
    if (projectFlag == 1) {
        consolePanel.hide();
    }

    var tryTestbtn = Ext.create('Ext.Button', {
        text: '测试',
        hidden: true,
        handler: function () {
            editor.save();
            paramStore.sort('paramOrder', 'ASC');
            var m = paramStore.getRange(0, paramStore.getCount() - 1);
            for (var i = 0, len = m.length; i < len; i++) {
                var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
                if ((paramType == 'OUT-int' || paramType == 'IN-int' || paramType == 'int') && paramDefaultValue) {
                    if (!checkIsInteger(paramDefaultValue)) {
                        setMessage('参数类型为int，但参数默认值不是int类型！');
                        return;
                    }
                }
                if ((paramType == 'OUT-float' || paramType == 'IN-float' || paramType == 'float') && paramDefaultValue) {
                    if (!checkIsDouble(paramDefaultValue)) {
                        setMessage('参数类型为float，但参数默认值不是float类型！');
                        return;
                    }
                }
                if (paramDefaultValue.indexOf('"') >= 0) {
                    if (versioncheckRadio == '1') {
                        Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                        return;
                    }
                }
            }
            var viewcontent = document.getElementById('code-edit-view').value;
            if (viewcontent.trim() == '') {
                Ext.MessageBox.alert("提示", "测试脚本内容不能为空!");
                return;
            }
            if (versioncheckRadio == 4) {
                agent_grid.setWidth((contentPanel.getWidth() - 250) / 2);
                db_soucre_grid.setWidth((contentPanel.getWidth() - 250) / 2);
                db_soucre_grid.show();
            } else {
                agent_grid.setWidth('100%');
                db_soucre_grid.hide();
            }

            testScript();
        }
    });
    if (projectFlag == 1) {
        tryTestbtn.hidden = true;
    }
    var westPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        /*padding: '5 0 0 0',*/
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        cls: 'customize_panel_back panel_space_right',
        /*height: contentPanel.getHeight()-146,*/
        items: [/* attachmentGrid, */ mainP],
        buttonAlign: 'center',
        buttons: [{
            text: '版本回退',
            // id: 'saveButton',
            // cls : 'Common_Btn',
            handler: versionRollBack
        }, tryTestbtn, {
            text: '发布',
            // cls: 'Common_Btn',
            hidden: true,
            handler: function () {
                publishScript();
            }
        }, {
            text: '返回',
            // cls : 'Common_Btn',
            handler: fanhui
        }]
    });


    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    var pppPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',

        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        height: contentPanel.getHeight(),
        items: [westPanel, paramsAndFuncDescPanel]
    });


    mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "gridBasicScript_view_area",
        layout: {
            type: 'border'
        },
        border: false,
        height: contentPanel.getHeight() - modelHeigth,
        items: [versionGrid, pppPanel]
    });

    editor = CodeMirror.fromTextArea(document.getElementById('code-edit-view'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        readOnly: true
    });
    editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 125);
    contentPanel.on('resize',
        function () {
            editor.getDoc().clearHistory();
            mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
            mainPanel.setWidth(contentPanel.getWidth());
            editor.setSize(mainP.getWidth() - 2, mainP.getHeight() - 125);
            if (chooseTestAgentWin) {
                chooseTestAgentWin.center();
            }
        });

    function fanhui() {
        var url = 'forwardScriptServiceRelease.do';
        if (projectFlag == 1) {
            url = 'inintDbaasServiceManage.do';
        }
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: url,
            params: {
                'filter_bussId': filter_bussIdForViewVersion,
                'filter_bussTypeId': filter_bussTypeIdForViewVersion,
                'filter_scriptName': filter_scriptNameForViewVersion,
                'filter_keywords': filter_keywordsForViewVersion,
                'filter_serviceName': filter_serviceNameForViewVersion,
                'filter_scriptType': filter_scriptTypeForViewVersion,
                'filter_scriptStatus': filter_scriptStatusForViewVersion,
                'filter_serviceType': filter_serviceTypeForViewVersion,
                'filter_patFromValue': filter_patFromValueForUpdateScriptEdit,
                'switchFlag': projectFlag,
                'filter_scriptDir': filter_scriptDirForViewVersion,
                'filter_selectUnboundScript': filter_selectUnboundScriptForViewVersion,
                'scriptName1':filterScriptName
            },
            scripts: true
        });
        if (refreshTryForVersion) {
            clearInterval(refreshTryForVersion);
        }
    }

    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function versionRollBack() {
        Ext.Msg.wait('处理中，请稍后...', '提示');
        var selectedRows = versionGrid.getSelectionModel().getSelection();
        if (selectedRows.length == 1) {
            var maxId = versionStore.getAt(0).get('iid');
            Ext.Ajax.request({
                url: 'scriptStatus.do',
                method: 'POST',
                params: {
                    serviceId: maxId
                },
                success: function (response, opts) {
                    var status = Ext.decode(response.responseText).status;
                    if (status == 2) { // 处于审核中
                        Ext.Msg.alert('提示', "该脚本正在审核中，请等待审核结束后回退！");
                        return;
                    } else {
                        var message = Ext.decode(response.responseText).message;
                        if (null !=message) {
                            Ext.Msg.alert('提示', message);
                            return;
                        }
                        var noVersionId = -1;
                        var noVersionUuid = "";
                        var selectedRow = selectedRows[0];
                        if ("无版本号" == selectedRow.data.onlyVersion) {
                            Ext.Msg.alert('提示', '该版本无版本号，无法回退！');
                            return;
                        } else {
                            var canRoll = false;
                            var hasNoVersion = false;
                            versionStore.each(function (record) {
                                if (record.get('onlyVersion') == "无版本号") {
                                    hasNoVersion = true;
                                    noVersionId = record.get('iid');
                                    noVersionUuid = record.get('uuid');
                                    return;
                                }
                            });

                            versionStore.each(function (record) {
                                if (selectedRow.data.iid != record.get('iid')) {
                                    if (parseFloat(selectedRow.data.onlyVersion) < parseFloat(record.get('onlyVersion'))) {
                                        canRoll = true;
                                        return;
                                    }
                                }
                            });
                            // Ext.getCmp('saveButton').disable();
                            if (hasNoVersion) {
                                Ext.MessageBox.buttonText.yes = "确定";
                                Ext.MessageBox.buttonText.no = "取消";
                                Ext.Msg.confirm("请确认", "是否要回退该版本，如强制回退将会覆盖现在无版本号中内容！",
                                    function (button, text) {
                                        if (button == "yes") {
                                            Ext.Ajax.request({
                                                url: 'hasVersionRollBack.do',
                                                method: 'POST',
                                                sync: true,
                                                params: {
                                                    iid: newServiceId,
                                                    oldId: noVersionId,
                                                    oldUuid: noVersionUuid,
                                                    uuid: newUuid
                                                },
                                                success: function (response, request) {
                                                    // Ext.getCmp('saveButton').enable();
                                                    var success = Ext.decode(response.responseText).success;
                                                    if (success) {
                                                        Ext.Msg.alert('提示', '版本回退成功！');
//													var lastId = Ext.decode(response.responseText).lastId;
                                                        versionStore.load();
                                                    } else {
                                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                    }
                                                },
                                                failure: function (result, request) {
                                                    // Ext.getCmp('saveButton').enable();
                                                    secureFilterRs(result, "版本回退失败！");
                                                }
                                            });
                                        } else {
                                            // Ext.getCmp('saveButton').enable();
                                            return;
                                        }
                                    });

                            } else {
                                if (canRoll) {
                                    Ext.Ajax.request({
                                        url: 'versionRollBack.do',
                                        method: 'POST',
                                        sync: true,
                                        params: {
                                            iid: newServiceId,
                                            uuid: newUuid
                                        },
                                        success: function (response, request) {
                                            // Ext.getCmp('saveButton').enable();
                                            var success = Ext.decode(response.responseText).success;
                                            if (success) {
                                                Ext.Msg.alert('提示', '版本回退成功！');
//														var lastId = Ext.decode(response.responseText).lastId;
                                                versionStore.load();
                                            } else {
                                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                            }
                                        },
                                        failure: function (result, request) {
                                            // Ext.getCmp('saveButton').enable();
                                            secureFilterRs(result, "版本回退失败！");
                                        }
                                    });
                                } else {
                                    // Ext.getCmp('saveButton').enable();
                                    Ext.Msg.alert('提示', '选择的版本是最新版本，无法回退！');
                                    return;
                                }
                            }

                        }
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                    return;
                }
            });
        } else {
            Ext.Msg.alert('提示', '只能选择一个版本进行回退！');
            return;
        }
    }

    function compareScript() {
        var seledCnt = selModel.getCount();
        if (seledCnt != 2) {
            Ext.MessageBox.alert("提示", "请选择两个不同版本的脚本进行比对！");
            return;
        }
        var ss = selModel.getSelection();
        var ids = [];
        for (var i = 0; i < 2; i++) {
            var version1 = ss[i].data.iid;
            ids.push(version1);
        }
        if (ids.length > 0) {
            Ext.Ajax.request({
                url: 'compareScript.do',
                method: 'post',
                params: {
                    ids: ids
                },
                success: function (response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        var leftVersion = Ext.decode(response.responseText).leftVersion;
                        var rightVersion = Ext.decode(response.responseText).rightVersion;
                        var leftContent = Ext.decode(response.responseText).leftContent;
                        var rightContent = Ext.decode(response.responseText).rightContent;
                        var compareWin = Ext.create('widget.window', {
                            title: '对比详情',
                            closable: true,
                            closeAction: 'destroy',
                            width: contentPanel.getWidth() - 200,
                            minWidth: 350,
                            height: contentPanel.getHeight(),
                            draggable: false,
                            // 禁止拖动
                            resizable: false,
                            // 禁止缩放
                            modal: true,
                            loader: {
                                url: 'showCompare.do',
                                params: {
                                    leftVersion: leftVersion,
                                    rightVersion: rightVersion,
                                    leftContent: leftContent,
                                    rightContent: rightContent
                                },
                                autoLoad: true,
                                scripts: true
                            }
                        });
                        compareWin.show();
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "请求返回失败！", request);
                }
            });
        }
    }

    var testparamColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 60,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            xtype: 'gridcolumn',
            dataIndex: 'paramDefaultValue',
            width: 70,
            text: '参数值',
            getEditor: function (record) {
                if (record.get('paramType') != 'IN-string(加密)') {
                    return defultEditor;
                } else {
                    return passwordEditor;
                }
            },
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                var backValue = "";
                if (record.get('paramType') == 'IN-string(加密)') {
                    backValue = StringToPassword(value);
                } else {
                    backValue = value;
                }
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                    + "<br>默认值：" + backValue
                    + "<br>排    序：" + record.get('paramOrder')
                    + "<br>描    述：" + record.get('paramDesc'))
                    + '"';

                return backValue;
            }
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 70,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }];
    var testparamStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var testparamGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        store: testparamStore,
        plugins: [cellEditing3],
        border: false,
        columnLines: true,
        //        collapsible : true,
        //        collapsed: true,
        //height:30,
        columns: testparamColumns
    });

    testparamStore.on('beforeload', function (store, options) {
        var new_params = {
            scriptId: newUuid
        };

        Ext.apply(testparamStore.proxy.extraParams, new_params);
    });
    var paramPanel = Ext.create('Ext.panel.Panel', {
        region: 'north',
        cls: 'window_border panel_space_top panel_space_right panel_space_left panel_space_bottom ',
        border: true,
        layout: 'border',
        height: contentPanel.getHeight() - 510,
        items: [testparamGrid]
    });

    function testScript() {
        if (chooseTestAgentWin) {
            chooseTestAgentWin.show();
        } else {
            var grdi_panel = Ext.create('Ext.panel.Panel', {
                width: contentPanel.getWidth() - 252,
                height: contentPanel.getHeight() - 80,
                cls: 'window_border  panel_space_right panel_space_left panel_space_bottom ',
                layout: 'border',
                header: false,
                region: 'center',
                border: false,
                items: [search_form, agent_grid, db_soucre_grid]
            });
            var fPanel = Ext.create('Ext.panel.Panel', {
                border: true,
                layout: 'border',
                width: contentPanel.getWidth() - 205,
                height: contentPanel.getHeight() - 150,
                items: [paramPanel, grdi_panel]
            });
            chooseTestAgentWin = Ext.create('Ext.window.Window', {
                title: '选择测试服务器',
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                width: contentPanel.getWidth() - 200,
                height: contentPanel.getHeight() - 50,
                buttonAlign: 'center',
                items: [fPanel],
                buttons: [{
                    xtype: "button",
// cls:'Common_Btn',
                    text: "确定",
                    handler: function () {
                        var me = this;
                        var records = agent_grid.getSelectionModel().getSelection();
                        var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
                        if (records.length != 1) {
                            Ext.Msg.alert('提示', '请选择记录，并且只能选择一条记录！');
                            return;
                        }
                        if (versioncheckRadio == 4) {
                            if (dsrecords.length != 1) {
                                Ext.Msg.alert('提示', '请选择数据源，并且只能选择一个！');
                                return;
                            }
                        }
                        var jsonData = "[" + Ext.JSON.encode(records[0].data) + "]";
                        var dsid = 0;
                        if (dsrecords.length > 0) {
                            dsid = Ext.JSON.encode(dsrecords[0].data.dsId);
                        }
                        var scriptPara = '';
                        testparamStore.sort('paramOrder', 'ASC');
                        var m = testparamStore.getRange(0, testparamStore.getCount() - 1);
                        var aaaa = [];
                        for (var i = 0, len = m.length; i < len; i++) {
                            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';

                            if ((paramType == 'OUT-int' || paramType == 'IN-int' || paramType == 'int') && paramDefaultValue) {
                                if (!checkIsInteger(paramDefaultValue)) {
                                    Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                                    return;
                                }
                            }
                            if ((paramType == 'OUT-float' || paramType == 'IN-float' || paramType == 'float') && paramDefaultValue) {
                                if (!checkIsDouble(paramDefaultValue)) {
                                    Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                                    return;
                                }
                            }
                            if (paramDefaultValue.indexOf('"') >= 0) {
                                if (scriptTypeForVersion == 'bat') {
                                    Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                                    return;
                                }
                            }
                            aaaa.push(paramDefaultValue);
// scriptPara += paramDefaultValue + " ";
                        }
                        scriptPara = aaaa.join("@@script@@service@@");
                        var jsonDataPara = "[";
                        for (var ii = 0, len1 = m.length; ii < len1; ii++) {
                            var ss = Ext.JSON.encode(m[ii].data);
                            if (ii == 0) jsonDataPara = jsonDataPara + ss;
                            else jsonDataPara = jsonDataPara + "," + ss;
                        }
                        jsonDataPara = jsonDataPara + "]";
                        Ext.Ajax.request({
                            url: 'execScriptServiceForSync.do',
                            method: 'POST',
                            params: {
                                serviceId: newServiceId,
                                execUser: "",
                                scriptPara: scriptPara,
                                jsonDataPara: jsonDataPara,
                                jsonData: jsonData,
                                dbsourceid: dsid,
                                ifrom: 0,
                                flag: 0
                            },
                            success: function (response, request) {
// var coatId = Ext.decode(response.responseText).coatId;
// var flowId = Ext.decode(response.responseText).flowId;
                                var requestId = Ext.decode(response.responseText).requestIds[0];
                                isFromTryATry = 0;
                                tryRequestId = requestId;
                                tryAgentIp = records[0].get('agentIp');
                                tryAgentPort = records[0].get('agentPort');

                                if (refreshTryForVersion) {
                                    clearInterval(refreshTryForVersion);
                                }

                                refreshTryForVersion = setInterval(function () {
                                    loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
                                }, 5 * 1000);

                                me.up("window").close();
                                settime(tryTestbtn, '测试');
                                Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
                            },
                            failure: function (result, request) {
                                Ext.Msg.alert('提示', '执行失败！');
                                $this.html('执行脚本');
                            }
                        });
                    }
                }, {
                    xtype: "button",
// cls:'Gray_button',
                    text: "取消",
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            }).show();
        }
        resourceGroupObj.setValue('');
        search_form.getForm().findField("agentIp").setValue('');
        //search_form.getForm().findField("agentDesc").setValue('');
        agent_store.load();
    }

    function loadShelloutputhisInfo(requestId, iip, iport) {
        var surl = "getScriptExecOutputForTry.do";
        Ext.Ajax.request({
            url: surl,
            params: {
                isFromTryATry: isFromTryATry,
                requestId: requestId,
                agentIp: iip,
                agentPort: iport,
                input: cmdVForbasicVersion
            },
            success: function (response, opts) {
                var msg = Ext.decode(response.responseText).out;
                var status = Ext.decode(response.responseText).status;
                if (!Ext.isEmpty(Ext.util.Format.trim(msg))) {
                    $('#consoleLog-edit-view').html(msg);
                    consolePanel.body.scroll('bottom', 300000);
                }
                if (status == 2) {
                    if (refreshTryForVersion) {
                        clearInterval(refreshTryForVersion);
                    }
                }
            },
            failure: function (response, opts) {
                $('#consoleLog-edit-view').html('获取执行信息失败');
            }

        });
        cmdVForbasicVersion = null;
    }

    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }]
    });

    var auditorStore_sm = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getPublishAuditorList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var auditorComBox_sm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        editable: true,
        fieldLabel: "审核人",
        labelWidth: 93,
// padding: 5,
        labelAlign: 'right',
        store: auditorStore_sm,
        queryMode: 'local',
// width: 200,
        columnWidth: projectFlag == 1 ? .98 : .95,
        margin: '10 0 0 0',
        displayField: 'fullName',
        valueField: 'loginName',
        listeners: { //监听
            render: function (combo) {//渲染
                combo.getStore().on("load", function (s, r, o) {
                    combo.setValue(r[0].get('loginName'));//第一个值
                });
            },
            select: function (combo, records, eOpts) {
                var fullName = records[0].raw.fullName;
                combo.setRawValue(fullName);
            },
            blur: function (combo, records, eOpts) {
                var displayField = auditorComBox_sm.getRawValue();
                if (!Ext.isEmpty(displayField)) {
                    //判断输入是否合法标志，默认false，代表不合法
                    var flag = false;
                    //遍历下拉框绑定的store，获取displayField
                    auditorStore_sm.each(function (record) {
                        //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                        var data_fullName = record.get('fullName');
                        if (data_fullName == displayField) {
                            flag = true;
                            combo.setValue(record.get('loginName'));
                        }
                    });
                    if (!flag) {
                        Ext.Msg.alert('提示', "输入的审核人非法");
                        auditorComBox_sm.setValue("");
                        return;
                    }
                }

            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var planTime_sm = Ext.create('Go.form.field.DateTime', {
        fieldLabel: '计划时间',
        format: 'Y-m-d H:i:s',
        hidden: true,
        labelWidth: 93,
        labelAlign: 'right',
// width:200,
        columnWidth: .98,
        margin: '10 0 0 0'
    });

    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        emptyText: '',
        labelWidth: 93,
        labelAlign: 'right',
        margin: '10 0 0 0',
        height: 70,
        columnWidth: .98,
        autoScroll: true
    });

    var levelStore_sm = Ext.create('Ext.data.Store', {
        fields: ['iid', 'scriptLevel'],
        data: [
            {"iid": "0", "scriptLevel": "白名单"},
            {"iid": "1", "scriptLevel": "高级风险"},
            {"iid": "2", "scriptLevel": "中级风险"},
            {"iid": "3", "scriptLevel": "低级风险"}
        ]
    });

    var scriptLevelCb_sm = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 93,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '风险级别',
        margin: '10 0 0 0',
        displayField: 'scriptLevel',
        valueField: 'iid',
        labelAlign: 'right',
        editable: false,
        hidden: !scriptLevelSwitch,
        emptyText: '--请选择风险级别--',
        store: levelStore_sm
    });

    Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    appSysStore1.on('load', function () {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
    var appSysObj1 = Ext.create('Ext.ux.ideal.form.ComboBox', {
        fieldLabel: '应用系统',
        emptyText: '--请选择应用系统--',
        hidden: !reviewSwitch,
        multiSelect: true,
        labelWidth: 93,
        labelAlign: 'right',
        columnWidth: .95,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function (combo, records, eOpts) {
                if (records) {
                    chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                        chosedAppSys.push(records[i].data.id);
                    }
                }

            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        hidden: !reviewSwitch,
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });
    var forbidden = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否禁用旧版本',
        inputValue: 1,
        width: 120,
        hidden: projectFlag == 1,
        margin: '10 0 0 10'
    });
    var auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
        width: 600,
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        items: [{
// layout:'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [planTime_sm]
            }, {
                layout: 'column',
                border: false,
                items: [scriptLevelCb_sm]
            }, {
                layout: 'column',
                border: false,
                items: [appSysObj1, isEMscript]
            }, {
                layout: 'column',
                border: false,
                items: [auditorComBox_sm, forbidden]
            }, {
                layout: 'column',
                border: false,
                items: [pubDesc_sm]
            }]
        }]
    });
    Ext.define('warnningModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'serviceName',
            type: 'string'
        }, {
            name: 'sysName',
            type: 'string'
        }, {
            name: 'bussName',
            type: 'string'
        }, {
            name: 'version',
            type: 'string'
        }, {
            name: 'user',
            type: 'string'
        }]
    });
    var warnningStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'warnningModel',
        proxy: {
            type: 'ajax',
            url: 'scriptCallSearch.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    warnningStore.on('beforeload', function (store, options) {
        var new_params = {
            iid: newServiceId
        };

        Ext.apply(warnningStore.proxy.extraParams, new_params);
    });
    var warnningColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '服务名称', dataIndex: 'serviceName', flex: 1},
        {text: '一级分类', dataIndex: 'sysName', width: 120},
        {text: '二级分类', dataIndex: 'bussName', width: 100},
        {
            text: '版本', dataIndex: 'version', width: 80, renderer: function (value, p, record, rowIndex) {
                if (value) {
                    return value;
                } else {
                    return '无版本号';
                }
            }
        },
        {text: '创建用户', dataIndex: 'user', width: 120}
    ];
    warnningGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: warnningStore,
        border: false,
        columnLines: true,
        columns: warnningColumns
    });

    function publishScript() {
        Ext.Ajax.request({
            url: 'scriptHasVersion.do',
            method: 'POST',
            params: {
                serviceId: newServiceId
            },
            success: function (response, opts) {
                var hasVersion = Ext.decode(response.responseText).hasVersion;
                if (hasVersion == 1) {
                    Ext.Msg.alert('提示', "该脚本已经发布过！");
                    return;
                } else {
                    Ext.Ajax.request({
                        url: 'scriptStatus.do',
                        method: 'POST',
                        params: {
                            serviceId: newServiceId
                        },
                        success: function (response, opts) {
                            var status = Ext.decode(response.responseText).status;
                            if (status == 2) {
                                Ext.Msg.alert('提示', "该脚本正处于审核中！");
                                return;
                            } else {

                                //先查这个脚本是否被作业调用
                                Ext.Ajax.request({
                                    url: 'scriptCallSearch.do',
                                    method: 'POST',
                                    params: {
                                        iid: newServiceId
                                    },
                                    success: function (response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {//有需要提示的内容
                                            if (!warnningWin) {
                                                warnningWin = Ext.create('widget.window', {
                                                    title: '提示信息,发布该脚本将影响以下作业，是否继续发布？',
                                                    closable: true,
                                                    closeAction: 'hide',
                                                    modal: true,
                                                    width: 600,
                                                    minWidth: 350,
                                                    height: 300,
                                                    layout: {
                                                        type: 'border',
                                                        padding: 5
                                                    },
                                                    items: [warnningGrid],
                                                    dockedItems: [{
                                                        xtype: 'toolbar',
                                                        dock: 'bottom',
                                                        layout: {pack: 'center'},
                                                        items: [{
                                                            xtype: "button",
                                                            cls: 'Common_Btn',
                                                            text: "是",
                                                            handler: function () {
                                                                this.up("window").close();
                                                                if (!publishAuditingSMWin) {
                                                                    publishAuditingSMWin = Ext.create('widget.window', {
                                                                        title: '确认审核信息',
                                                                        closable: true,
                                                                        closeAction: 'hide',
                                                                        modal: true,
                                                                        width: 600,
                                                                        minWidth: 350,
                                                                        height: reviewSwitch ? 330 : 280,
                                                                        layout: {
                                                                            type: 'border',
                                                                            padding: 5
                                                                        },
                                                                        items: [auditing_form_sm],
                                                                        dockedItems: [{
                                                                            xtype: 'toolbar',
                                                                            dock: 'bottom',
                                                                            layout: {pack: 'center'},
                                                                            items: [{
                                                                                xtype: "button",
                                                                                cls: 'Common_Btn',
                                                                                text: "确定",
                                                                                handler: function () {
                                                                                    var planTime = planTime_sm.getRawValue();
                                                                                    var scriptLevel = scriptLevelCb_sm.getValue();
                                                                                    if (!scriptLevelSwitch) {
                                                                                        scriptLevel = 100;
                                                                                    }
                                                                                    var publishDesc = pubDesc_sm.getValue();
                                                                                    var auditor = auditorComBox_sm.getValue();
                                                                                    var isEmScript = isEMscript.getValue();
                                                                                    if (isEmScript) {
                                                                                        isEmScript = 1;
                                                                                    } else {
                                                                                        isEmScript = 0;
                                                                                    }
                                                                                    var isForbidden = forbidden.getValue();
                                                                                    if (isForbidden) {
                                                                                        isForbidden = 1;
                                                                                    } else {
                                                                                        isForbidden = 0;
                                                                                    }
                                                                                    if (!scriptLevel) {
                                                                                        Ext.Msg.alert('提示', "没有选择风险级别！");
                                                                                        return;
                                                                                    }
                                                                                    if (!publishDesc) {
                                                                                        Ext.Msg.alert('提示', "没有填写发布申请说明！");
                                                                                        return;
                                                                                    }
                                                                                    if (publishDesc.length > 255) {
                                                                                        Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
                                                                                        return;
                                                                                    }
                                                                                    if (!auditor) {
                                                                                        Ext.Msg.alert('提示', "没有选择审核人！");
                                                                                        return;
                                                                                    }

                                                                                    var sIds = new Array();
                                                                                    sIds.push(newServiceId);
                                                                                    Ext.Ajax.request({
                                                                                        url: 'scriptPublishAuditing.do',
                                                                                        method: 'POST',
                                                                                        params: {
                                                                                            sIds: sIds,
                                                                                            planTime: planTime,
                                                                                            scriptLevel: scriptLevel,
                                                                                            publishDesc: publishDesc,
                                                                                            auditor: auditor,
                                                                                            flag: 0, // 0-来着个人脚本库
                                                                                            isEmScript: isEmScript,
                                                                                            isForbidden: isForbidden,
                                                                                            appSysIds: chosedAppSys,
                                                                                            switchFlag: projectFlag,
                                                                                            radio: radio
                                                                                        },
                                                                                        success: function (response, opts) {
                                                                                            var success = Ext.decode(response.responseText).success;
                                                                                            var message = Ext.decode(response.responseText).message;
                                                                                            if (!success) {
                                                                                                Ext.MessageBox.alert("提示", message);
                                                                                            } else {
                                                                                                Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                                                                            }
                                                                                            publishAuditingSMWin.close();

                                                                                        },
                                                                                        failure: function (result, request) {
                                                                                            secureFilterRs(result, "操作失败！");
                                                                                            publishAuditingSMWin.close();
                                                                                        }
                                                                                    });

                                                                                }
                                                                            }, {
                                                                                xtype: "button",
                                                                                text: "取消",
                                                                                cls: 'Common_Btn',
                                                                                handler: function () {
                                                                                    this.up("window").close();
                                                                                }
                                                                            }]
                                                                        }]
                                                                    });

                                                                }
                                                                publishAuditingSMWin.show();
                                                                auditorStore_sm.load();
                                                                planTime_sm.setValue('');
                                                                scriptLevelCb_sm.setValue('');
                                                                pubDesc_sm.setValue('');
                                                                auditorComBox_sm.setValue('');
                                                                isEMscript.setValue(0);
                                                                forbidden.setValue(0);
                                                                appSysObj1.setValue('');
                                                                chosedAppSys = '';
                                                            }
                                                        }, {
                                                            xtype: "button",
                                                            cls: 'Common_Btn',
                                                            text: "否",
                                                            handler: function () {
                                                                this.up("window").close();
                                                            }
                                                        }]
                                                    }]
                                                });
                                            }
                                            warnningWin.show();
                                            warnningStore.load();
                                        } else {
                                            if (!publishAuditingSMWin) {
                                                publishAuditingSMWin = Ext.create('widget.window', {
                                                    title: '确认审核信息',
                                                    closable: true,
                                                    closeAction: 'hide',
                                                    modal: true,
                                                    width: 600,
                                                    minWidth: 350,
                                                    height: reviewSwitch ? 330 : 280,
                                                    layout: {
                                                        type: 'border',
                                                        padding: 5
                                                    },
                                                    items: [auditing_form_sm],
                                                    dockedItems: [{
                                                        xtype: 'toolbar',
                                                        dock: 'bottom',
                                                        layout: {pack: 'center'},
                                                        items: [{
                                                            xtype: "button",
                                                            cls: 'Common_Btn',
                                                            text: "确定",
                                                            handler: function () {
                                                                var planTime = planTime_sm.getRawValue();
                                                                var scriptLevel = scriptLevelCb_sm.getValue();
                                                                if (!scriptLevelSwitch) {
                                                                    scriptLevel = 100;
                                                                }
                                                                var publishDesc = pubDesc_sm.getValue();
                                                                var auditor = auditorComBox_sm.getValue();
                                                                var isEmScript = isEMscript.getValue();
                                                                if (isEmScript) {
                                                                    isEmScript = 1;
                                                                } else {
                                                                    isEmScript = 0;
                                                                }
                                                                var isForbidden = forbidden.getValue();
                                                                if (isForbidden) {
                                                                    isForbidden = 1;
                                                                } else {
                                                                    isForbidden = 0;
                                                                }
                                                                if (!scriptLevel) {
                                                                    Ext.Msg.alert('提示', "没有选择风险级别！");
                                                                    return;
                                                                }
                                                                if (!publishDesc) {
                                                                    Ext.Msg.alert('提示', "没有填写发布申请说明！");
                                                                    return;
                                                                }
                                                                if (publishDesc.length > 255) {
                                                                    Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
                                                                    return;
                                                                }
                                                                if (!auditor) {
                                                                    Ext.Msg.alert('提示', "没有选择审核人！");
                                                                    return;
                                                                }

                                                                var sIds = new Array();
                                                                sIds.push(newServiceId);
                                                                Ext.Ajax.request({
                                                                    url: 'scriptPublishAuditing.do',
                                                                    method: 'POST',
                                                                    params: {
                                                                        sIds: sIds,
                                                                        planTime: planTime,
                                                                        scriptLevel: scriptLevel,
                                                                        publishDesc: publishDesc,
                                                                        auditor: auditor,
                                                                        flag: 0, // 0-来着个人脚本库
                                                                        isEmScript: isEmScript,
                                                                        appSysIds: chosedAppSys,
                                                                        isForbidden: isForbidden,
                                                                        switchFlag: projectFlag,
                                                                        radio: radio
                                                                    },
                                                                    success: function (response, opts) {
                                                                        var success = Ext.decode(response.responseText).success;
                                                                        var message = Ext.decode(response.responseText).message;
                                                                        if (!success) {
                                                                            Ext.MessageBox.alert("提示", message);
                                                                        } else {
                                                                            Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                                                                        }
                                                                        publishAuditingSMWin.close();

                                                                    },
                                                                    failure: function (result, request) {
                                                                        secureFilterRs(result, "操作失败！");
                                                                        publishAuditingSMWin.close();
                                                                    }
                                                                });

                                                            }
                                                        }, {
                                                            xtype: "button",
                                                            text: "取消",
                                                            cls: 'Common_Btn',
                                                            handler: function () {
                                                                this.up("window").close();
                                                            }
                                                        }]
                                                    }]
                                                });

                                            }
                                            publishAuditingSMWin.show();
                                            auditorStore_sm.load();
                                            planTime_sm.setValue('');
                                            scriptLevelCb_sm.setValue('');
                                            pubDesc_sm.setValue('');
                                            auditorComBox_sm.setValue('');
                                            isEMscript.setValue(0);
                                            forbidden.setValue(0);
                                            appSysObj1.setValue('');
                                            chosedAppSys = '';
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "操作失败！");
                                    }
                                });
                            }
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                            return;
                        }
                    });
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
                return;
            }
        });
    }


    function StringToPassword(strs) {
        if (strs && strs != null & strs != '') {
            var password = '';
            for (var i = 0; i < strs.length; i++) {
                password = password + '●';
            }
            return password;
        } else {
            return '';
        }
    }

    function displayWord(value, p, record) {
        return "<a style='cursor: pointer;'>查看分析算法</a>";
    }

    function toAnalyzeTextWin(uuid, iid) {
        Ext.create('Ext.window.Window', {
            title: '分析算法',
            height: '60%',  //Number型  也可以是字符串类型  width:'60%'
            width: '60%',
            layout: 'fit',
            constrain: true, 		//闲置窗口不超出浏览器
            constrainHeader: true, 	//标题不能超出浏览器边界
            modal: true,			//设置模态窗口
            plain: true, 			//窗口设置透明背景
            draggable: false,
            resizable: false,
            loader: {
                url: 'getAnalyzeTextWin.do',
                params: {
                    uuid: uuid,
                    iid: iid
                },
                autoLoad: true,
                scripts: true
            }
        }).show();
    }

    function searcformquery() {
        agent_grid.ipage.moveFirst();
    }

});

function addTags(las) {
    var labKey = las.getKeys();
    //切换不同版本，判断标签不为空，移除标签
    if (lastVal != null) {
        $("#tagsinputval").tagsinput('removeAll');
        // for(var i=0;i<labKey.length;i++){
        //         console.log(labKey[i].trim(),333)
        //     $("#tagsinputval").tagsinput('remove',labKey[i].trim());
        // }
        //$("#tagsinputval").tagsinput('remove', lastVal.join(','));
    }
    $("#tagsinputval").tagsinput('add', labKey.join(','));
    lastVal = new Object(labKey);
}