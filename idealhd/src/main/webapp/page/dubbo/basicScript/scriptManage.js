var bacthUpdateScriptDirWin;
Ext.onReady(function () {
    $("#main-content-panel_header_hd-textEl").html("我的脚本");
// 清理主面板的各种监听时间t
    destroyRubbish();
    var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    var auditorStore_sm1;
    var selectedSysRecords = [];
    var selectedAgentRecords = new Array();
    var sysid;
    var systemId;
    var type;
    var publishAuditingWin;
    var auditing_form_sync;
    var project_panel;
    var agent_panel;
    var project_panelScript;
    var myhight;
    var attachmentIds = [];
    var workitemid = 0;
    var openTryWin;
    var try_form;
    var tryStore_sm;
    var tryGroup;
    var uploadProcessWin;
    var itemsPerPage = 30;
    var isjc = 0;//下发时间戳
    var publishAuditingSMWin;
    var auditing_form_sm;
    var warnningGrid;
    var auditorStore_sm;
    var auditorComBox_sm;
    var planTime_sm;
    var pubDesc_sm;
    var scriptLevelCb_sm;
    //var filter;
    var chosedAppSys = new Array();
    var warnningWin;
    var warnId = [];
    var radio = 1;
    var shareWin;
    var shareType = 0; //共享类型  0 所有人  1 用户   2 用户组
    var chosedShareIds = []; //共享用户、用户组  数组
    var scriptPublishsIds = new Array(); //勾选的脚本iid数组
    var scriptiids = new Array(); //勾选的脚本iid数组 共享用
    var checkItems=[];
    var agentiids = new Array();
    var shareGrid;
    var shareedGrid;
    var saveAtomicScriptWin;
    var resGroupFlag;
    var chosedGroupIdCopy = [];
    var chosedGroupNameCopy = [];
    var chosedGroupIds = [];
    var chosedGroupNames = [];
    var chosedGroupWin;
    let selectedRecords = new Set();
    let selectedAgent = new Set();
    var showAllUserWin;
    var la = new Ext.util.HashMap();
    var golbalParamName;
    var laArr = [];
    var labels;
    var bacthUpdateScriptFunctionSortWin;
    Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'}
        ]
    });

    Ext.define('userModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'userid', type: 'string'},
            {name: 'ifullname', type: 'string'},
            {name: 'groupnames', type: 'string'}
        ]
    });


    var agentStoreScript = Ext.create('Ext.data.Store', {
        autoLoad: psbcBindAgentSwicht,
        autoDestroy: true,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListAllScript.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });


    var agentGroupStoreScript = Ext.create('Ext.data.Store', {
        autoLoad: isProject,
        autoDestroy: true,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAgentListWithOutSys.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    agentGroupStoreScript.on('beforeload', function (store, options) {
        var new_params = {
            agentGroup: agentGroup.getValue()
        };

        Ext.apply(agentGroupStoreScript.proxy.extraParams, new_params);
    });

    Ext.Loader.setConfig({
        enabled: true,
        disableCaching: false,
        paths: {
            'Go': 'js/ux/gooo'
        }
    });

    var scriptServiceReleaseStore;
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var ipStart = Ext.create('Ext.form.TextField',
        {
            labelWidth: 79,
            fieldLabel: '起始IP',
            emptyText: '--请输入开始IP--',
            //labelSeparator : '',
            width: '40%',
            labelAlign: 'right'
        });
    /** 结束ip* */
    var ipEnd = Ext.create('Ext.form.TextField',
        {
            labelWidth: 70,
            fieldLabel: '终止IP',
            emptyText: '--请输入截止IP--',
            //labelSeparator : '',
            labelAlign: 'right',
            width: '40%'
        });


    var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        iqueryFun: function () {
            agent_grid.ipage.moveFirst();
        },
        bodyCls: 'x-docked-noborder-top',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [ipStart, ipEnd,
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        agentgridScript.ipage.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: 'IP清空',
                    handler: function () {
                        ipStart.setValue('');
                        ipEnd.setValue('');
                        agentgridScript.ipage.moveFirst();
                    }
                }]
        }]
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var cataData = [
        {"id": "-1", "name": "全部"},
        {"id": "sh", "name": "shell"},
        {"id": "bat", "name": "bat"},
        {"id": "py", "name": "python"},
        {"id": "perl", "name": "perl"},
        {"id": "sql", "name": "sql"},
        {"id": "ps1", "name": "powershell"}
    ];
    //开启此开关，屏蔽sql类型脚本查询条件
    if(getScriptSqlShowSwitch){
        cataData = [
            {"id": "-1", "name": "全部"},
            {"id": "sh", "name": "shell"},
            {"id": "bat", "name": "bat"},
            {"id": "py", "name": "python"},
            {"id": "perl", "name": "perl"},
            {"id": "ps1", "name": "powershell"}
        ];
    }
//	if(scriptServiceScriptFlowSwitch) {
//		cataData.push({"id":"-2", "name":"组合"});
//	}
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: cataData
    });
    var scriptStatusStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-10000", "name": "全部"},
            {"id": "-1", "name": "草稿"},
            {"id": "1", "name": "已上线"},
            {"id": "3", "name": "共享"}/*,
            {"id": "2", "name": "审核中"}*/
        ]
    });
    var emScriptStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "0", "name": "否"},
            {"id": "1", "name": "是"}
        ]
    });
    if (filter_scriptType == 'sql') {
        radio = 4;
    }

    var searchUser = Ext.create('Ext.form.TextField',
        {
            labelWidth: 79,
            fieldLabel: '用户名',
            emptyText: '--请输入用户名--',
            width: '40%',
            labelAlign: 'right'
        });

    var search_user_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        border: false,
        bodyCls: 'x-docked-noborder-top',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [searchUser,
                {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        usersGrid.ipage.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '清空',
                    handler: function () {
                        searchUser.setValue('');
                        usersGrid.ipage.moveFirst();
                    }
                }]
        }]
    });

    var usenumtjText = new Ext.form.NumberField({
        name: 'usenumtjText',
        emptyText: '--输入条件值--',
        hidden: true,
        width: '5%',
        minValue: '0',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var usenumtjStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": ">=", "name": ">="},
            {"id": "<=", "name": "<="},
            {"id": "=", "name": "="}
        ]
    });
    var usetj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '使用次数',
        emptyText: '--请选择--',
        labelWidth: 65,
        hidden: usenumtj,
        labelAlign: 'right',
        //width: '8%',
        columnWidth: .5,
        store: usenumtjStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable: false,
        typeAhead: false,
        mode: 'local',
        listeners: {
            select: function (combo) {
                if (combo.getValue() != -1) {
                    usenumtjText.show();
                } else {
                    usenumtjText.hide();
                }
            }
        }
    });
    var succtjText = new Ext.form.NumberField({
        name: 'succtjText',
        emptyText: '--输入条件值--',
        hidden: true,
        width: '5%',
        minValue: '0',
        maxValue: '100',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });


    var succtjStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": ">=", "name": ">="},
            {"id": "<=", "name": "<="},
            {"id": "=", "name": "="}
        ]
    });
    var succtj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '成功率',
        emptyText: '--请选择--',
        labelWidth: 65,
        hidden: usenumtj,
        labelAlign: 'right',
        //width: '8%',
        columnWidth: .5,
        store: succtjStore,
        displayField: 'name',
        valueField: 'id',
        editable: false,
        typeAhead: false,
        triggerAction: 'all',
        mode: 'local',
        listeners: {
            select: function (combo) {
                if (combo.getValue() != -1) {
                    succtjText.show();
                } else {
                    succtjText.hide();
                }
            }
        }
    });

    var smbussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '一级分类',
//		padding : '5',
        hidden: db_f_class,
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选择一级分类--',
        store: bussData,
        width: '15%',
        labelAlign: 'right',
        listeners: {
            change: function () {
                smbussTypeCb.clearValue();
                smbussTypeCb.applyEmptyText();
                smbussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            afterRender: function (combo) {
                if (filter_bussId != '-1' && filter_bussId != '') {
                    smbussCb.setValue(parseInt(filter_bussId));
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    bussData.on('load', function (store, options) {
        if (filter_bussId != '-1' && filter_bussId != '') {
            smbussCb.setValue(parseInt(filter_bussId));
        }
        if (filter_bussTypeId != '-1' && filter_bussTypeId != '') {
            smbussTypeCb.setValue(parseInt(filter_bussTypeId));
        }
    });

    /** 二级分类* */
    var smbussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
//		padding : '5',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        hidden: db_s_class,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        width: '20%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            },
            change: function () { // old is keyup
                threesmbussTypeCb.clearValue();
                threesmbussTypeCb.applyEmptyText();
                threesmbussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    //北京邮储 三级分类
    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    /*threeBussTypeData.on('load', function(store, options) {
		if(threeBsTypeId) {
			threesmbussTypeCb.setValue(threeBsTypeId);
		}
	});*/

    var importScriptsStatusSearch = Ext.create('Ext.form.field.ComboBox', {
        labelWidth: 65,
        queryMode: 'local',
        hidden: importSwitch,
        fieldLabel: '导入状态',
        emptyText: '--请选择导入状态--',
        displayField: 'name',
        valueField: 'value',
        store: Ext.create('Ext.data.Store', {
            fields: ['name', 'value'],
            data:[
                {name: '全部', value: '0'},
                {name: '导入修改', value:'1' }
            ]
        }),
    })

    var threesmbussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threesmbussTypeCb',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        width: '20%',
        labelAlign: 'right',
        hidden: db_s_class || !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });


    /** 脚本类型* */
    var smscriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'smscriptTypeParam',
//		padding : '5',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        hidden: !db_scriptTypeswitch,
        valueField: 'id',
        editable: false,
        //hidden:db_scriptType,
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        width: reviewSwitch ? '15.3%' : '15.1%',
        labelAlign: 'right',
        listeners: {
            afterRender: function (combo) {
                if (filter_scriptType == '-1') {
                    combo.setValue(cataStore.getAt(0).data.id);
                } else if (filter_scriptType == 'sh') {
                    combo.setValue(cataStore.getAt(1).data.id);
                } else if (filter_scriptType == 'bat') {
                    combo.setValue(cataStore.getAt(2).data.id);
                } else if (filter_scriptType == 'py') {
                    combo.setValue(cataStore.getAt(3).data.id);
                } else if (filter_scriptType == 'perl') {
                    combo.setValue(cataStore.getAt(4).data.id);
                } else if (filter_scriptType == 'sql') {
                    combo.setValue(cataStore.getAt(5).data.id);
                }else if (filter_scriptType == 'ps1') {
                    combo.setValue(cataStore.getAt(6).data.id);
                }//else if(filter_scriptType=='-2'){
//					combo.setValue(cataStore.getAt(6).data.id);
//				}
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: !db_usePlantFormswitch,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    usePlantFormStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: "0"
        };
        Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
    });
    var smplatFromCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'smplatFromCombobox',
        labelWidth: 65,
        // columnWidth : .2,
        labelAlign: 'right',
        width: '15%',
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: true,
        emptyText: '-请选择适用平台-',
        store: usePlantFormStore,
        hidden: db_usePlantFormswitch,
        value: filter_patFromValue

    });

    var smscriptStatusCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptStatus',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: db_statusswitch == true ? '脚本状态' : '服务状态',
        displayField: 'name',
        valueField: 'id',
//		padding : '5',
        editable: false,
        emptyText: '--请选择脚本状态--',
        store: scriptStatusStore,
        width: '12%',
        labelAlign: 'right',
        listeners: {
            afterRender: function (combo) {
                if (filter_scriptStatus == '-10000') {
                    combo.setValue(scriptStatusStore.getAt(0).data.id);
                } else if (filter_scriptStatus == '-1') {
                    combo.setValue(scriptStatusStore.getAt(1).data.id);
                } else if (filter_scriptStatus == '1') {
                    combo.setValue(scriptStatusStore.getAt(2).data.id);
                } else if (filter_scriptStatus == '3') {
                    combo.setValue(scriptStatusStore.getAt(3).data.id);
                } else if (filter_scriptStatus == '2') {
                    combo.setValue(scriptStatusStore.getAt(4).data.id);
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var serviceTypeStore = Ext.create('Ext.data.Store', {
        fields: ['value', 'text'],
        data: [{
            "value": "-1",
            "text": "全部"
        }, {
            "value": "0",
            "text": "应用"
        },
            {
                "value": "1",
                "text": "采集"
            }]
    });

    var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'emScript',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '是否应急',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        hidden: !reviewSwitch,
        emptyText: '--请选择是否应急--',
        store: emScriptStore,
        width: '10.3%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: reviewSwitch,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStoreQuery = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: sdFunctionSortSwitch,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var appSysObj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '所属系统',
        emptyText: '--请选择所属系统--',
        labelWidth: 65,
        hidden: !reviewSwitch,
        labelAlign: 'right',
        width: '18.3%',
        store: appSysStore,
        //padding: '0 0 5 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        mode: 'local',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var smName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        emptyText: '--请输入服务名称--',
        labelWidth: 65,
//		padding : '5',
        width: '19%',
        labelAlign: 'right',
        value: filter_serviceName,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var serviceid = new Ext.form.TextField({
        name: 'serviceid',
        fieldLabel: '服务号',
        emptyText: '--请输入服务号--',
        hidden: serviceIdSwitch,
        labelWidth: 65,
//		padding : '5',
        width: '20%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });


    Ext.define('scriptNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'SCRIPTNAME',
            type: 'string'
        }]
    });
    let scriptNameStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'scriptNameModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryScriptNameComboBox.do',
            reader: {
                type: 'json',
            }
        }
    });
    let smscriptName = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: "脚本名称",
        labelWidth: 65,
        labelAlign: 'right',
        store: scriptNameStore,
        queryMode: 'local',
        width: '19%',
        displayField: 'SCRIPTNAME',
        editable: true,
        valueField: 'SCRIPTNAME',
        value: scriptName,
        listeners: { //监听
            select: function (combo, records, eOpts) {
                let scriptName = records[0].raw.SCRIPTNAME;
                combo.setRawValue(scriptName);
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var smkeywords = new Ext.form.TextField({
        name: 'smkeywords',
        fieldLabel: '关键字',
        emptyText: '-请输入关键字-',
        value: filter_keywords,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var smlabel = new Ext.form.TextField({
        name: 'smlabel',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        hidden: !labelSwitch,
        //value : labelSwitch,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var smgroupNameQuery = Ext.create('Ext.form.field.ComboBox', {
        name: 'smgroupNameQuery',
        queryMode: 'local',
        id: 'smgroupNameQuery',
        fieldLabel: '功能分类',
        hidden: !sdFunctionSortSwitch,
        labelWidth: 65,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选择功能分类--',
        store: groupNameStoreQuery,
        width: '19%',
        triggerAction: "all",
        labelAlign: 'right',
        listeners: {
            change: function () { // old is keyup
                smbussCb.clearValue();
                smbussCb.applyEmptyText();
                smbussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var updateUser = new Ext.form.TextField({
        name: 'updateUser',
        fieldLabel: '最后修改人',
        emptyText: '--请输入最后修改人--',
        labelWidth: 79,
//		padding : '5',
        width: '20%',
        labelAlign: 'right',
        value: filter_updateUser,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });


    var isExam;
    var searchItems;

    function queryData() {
        scriptServiceReleaseGrid.ipage.moveFirst();
        var filter_bussId = smbussCb.getValue();
        var filter_bussTypeId = smbussTypeCb.getValue();
        var filter_scriptName = smscriptName.getRawValue();
        var filter_keywords = smkeywords.getValue();
        var filter_serviceName = smName.getValue();
        var filter_scriptType = smscriptTypeParam.getValue();
        var filter_scriptStatus = smscriptStatusCb.getValue();
        var filter_patFromValue = smplatFromCombobox.getValue();
        var filter_serviceTypeValue = "";
        var filter_usetj = usetj.getValue();
        var filter_usenumtjText = usenumtjText.getValue();
        var filter_succtj = succtj.getValue();
        var filter_succtjText = succtjText.getValue();
        var filter_serviceid = serviceid.getValue();

        var scriptDirId = [];
        if (gfScriptDirFunctionSwitch) {
            var m = Ext.getCmp('treeId').getSelectionModel().getSelection();
            for (let i = 0; i < m.length; i++) {
                let iid = m[i].data.iid;
                let iscriptDirLevel = m[i].data.iscriptDirLevel;
                // 0级目录 root根目录
                if (iscriptDirLevel == 0) {
                    break;
                }
                scriptDirId.push(iid)
            }
        }
        var selectUnboundScript = false;
        if (gfScriptDirUnboundSelectSwitch) {
            selectUnboundScript = Ext.getCmp('selectUnboundScriptDir').checked;
        }

        var filter_updateUser = updateUser.getValue();
        //var filter_label=label.getValue();
        filter = {
            'filter_bussId': filter_bussId,
            'filter_bussTypeId': filter_bussTypeId,
            'filter_scriptName': filter_scriptName,
            'filter_keywords': filter_keywords,
            'filter_serviceName': filter_serviceName,
            'filter_scriptType': filter_scriptType,
            'filter_scriptStatus': filter_scriptStatus,
            'filter_patFromValue': filter_patFromValue,
            'filter_serviceType': filter_serviceTypeValue,
            'filter_usetj': filter_usetj,
            'filter_usenumtjText': filter_usenumtjText,
            'filter_succtj': filter_succtj,
            'filter_serviceid': filter_serviceid,
            'filter_succtjText': filter_succtjText,
            'filter_updateUser': filter_updateUser,
            'filter_scriptDir': Ext.encode(scriptDirId),
            'filter_selectUnboundScript': Ext.encode(selectUnboundScript)
            //'filter_label':filter_label
        };
    };


    searchItems = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        bodyCls: 'x-docked-noborder-top',
        border: false,
        iqueryFun: function () {
            queryData();
        },
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [smName, smscriptName, smplatFromCombobox, smscriptStatusCb, smkeywords, appSysObj, smlabel]
        }, {
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [smgroupNameQuery, emScriptCb, smscriptTypeParam, smbussCb, smbussTypeCb, threesmbussTypeCb, importScriptsStatusSearch, '->',
                {
                    xtype: 'fieldcontainer',
                    defaultType: 'checkboxfield',
                    hidden: !gfScriptDirUnboundSelectSwitch,
                    items: [
                        {
                            boxLabel: '查询未绑定目录的脚本',
                            name: 'selectUnboundScriptDir',
                            inputValue: '1',
                            id: 'selectUnboundScriptDir',
                            checked: filter_selectUnboundScript == 'true' ? true : false
                        }
                    ]
                }, {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        scriptPublishsIds = [];
                        selectedRecords.clear();
                        scriptiids = [];
                        queryData();
                    }
                }, {
                    xtype: 'button',
                    text: '清空',
                    cls: 'Common_Btn',
                    handler: function () {
                        clearQueryWhere();
                        filter = {filter_scriptDir: Ext.encode([])};
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    margin: '0 5 0 0',
                    text: '返回',
                    hidden: !requestFromC3Char,
                    handler: function () {
                        popNewTab('脚本看板', 'pandect1.do', {}, 10, true);
                    }
                }]
        }]
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'long'},
            {name: 'uuid', type: 'String'},
            {name: 'serviceName', type: 'string'},
            {name: 'groupName', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'bussName', type: 'string'},
            {name: 'threeTypeName', type: 'string'},
            {name: 'buss', type: 'string'},
            {name: 'bussType', type: 'string'},
            {name: 'bussId', type: 'int'},
            {name: 'bussTypeId', type: 'int'},
            {name: 'scriptType', type: 'string'},
            {name: 'isflow', type: 'string'},
            {name: 'scriptName', type: 'string'},
            {name: 'label', type: 'string'},
            {name: 'servicePara', type: 'string'},
            {name: 'serviceState', type: 'string'},
            {name: 'isshare', type: 'string'},
            {name: 'isTry', type: 'string'},
            {name: 'returnText', type: 'string'},
            {name: 'platForm', type: 'string'},
            {name: 'content', type: 'string'},
            {name: 'version', type: 'string'},
            {name: 'status', type: 'int'},
            {name: 'isEmScript', type: 'string'},
            {name: 'isAtomic', type: 'int'},
            {name: 'uperId', type: 'int'},
            {name: 'appSystem', type: 'string'},
            {name: 'useTimes', type: 'int'},
            {name: 'taskCount', type: 'int'},
            {name: 'winTimes', type: 'string'},
            {name: 'dbType', type: 'string'},
            {name: 'ssuer', type: 'string'},
            {name: 'execUserName', type: 'string'},
            {name: 'startType', type: 'string'},
            {name: 'serviceType', type: 'string'},
            {name: 'isExam', type: 'int'},
            {name: 'outTableName', type: 'string'},
            {name: 'sqlModel', type: 'string'},
            {name: 'serviceId', type: 'string'},
            {name: 'createUserName', type: 'string'},
            {name: 'updateUserName', type: 'string'},
            {name: 'keywords', type: 'string'},
            {name: 'issync', type: 'long'}
        ]
    });

    scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: itemsPerPage,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMySelfWithoutContent.do',
            timeout: 6000000,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    function getLastScriptDirId(record, scriptDirId) {
        if (record.childNodes.length > 0) {
            let m = record.childNodes;
            for (let i = 0; i < m.length; i++) {
                getLastScriptDirId(m[i], scriptDirId)
            }
        } else {
            let iid = record.data.iid;
            scriptDirId.push(iid);
        }
    }

    scriptServiceReleaseStore.on('beforeload', function (store, options) {
        var scriptDirId = [];
        if (gfScriptDirFunctionSwitch) {
            var m = Ext.getCmp('treeId').getSelectionModel().getSelection();
            for (let i = 0; i < m.length; i++) {
                let iid = m[i].data.iid;
                let iscriptDirLevel = m[i].data.iscriptDirLevel;
                // 0级目录 root根目录
                if (iscriptDirLevel == 0) {
                    break;
                }
                getLastScriptDirId(m[i], scriptDirId);
//                scriptDirId.push(iid)
            }
            // 其他页面跳转回来 第一次查询 用filter 数据展示
            if (m.length == 0) {
                var filterScriptDir = Ext.decode(filter.filter_scriptDir);
                scriptDirId = filterScriptDir;
            }
        }

        var selectUnboundScript = false;
        if (gfScriptDirUnboundSelectSwitch) {
            selectUnboundScript = Ext.getCmp('selectUnboundScriptDir').checked;
            // 跳页返回 首次取值
            if (filter_selectUnboundScript == 'true') {
                selectUnboundScript = Ext.decode(filter_selectUnboundScript)
                filter_selectUnboundScript = 'false';
            }
        }

        var new_params = {
            bussId: smbussCb.getValue(),
            bussTypeId: smbussTypeCb.getValue(),
            threeBsTypeId: threesmbussTypeCb.getValue(),
            scriptName: smscriptName.getRawValue().trim(),
            serviceName: smName.getValue().trim(),
            scriptType: smscriptTypeParam.getValue(),
            scriptStatus: smscriptStatusCb.getValue(),
            onlyScript: 1,
            isEmScript: emScriptCb.getValue(),
            appId: appSysObj.getValue(),
            serviceType: "",
            platform: smplatFromCombobox.getValue(),
            switchFlag: 0,
            usetj: usetj.getValue(),
            usenumtjText: usenumtjText.getValue(),
            succtj: succtj.getValue(),
            succtjText: succtjText.getValue(),
            serviceId: serviceid.getValue(),
            updateUser: updateUser.getValue(),
            keywords: smkeywords.getValue().trim(),
            scriptDir: Ext.encode(scriptDirId),
            label: smlabel.getValue(),
            selectUnboundScript: selectUnboundScript,
            groupName: smgroupNameQuery.getValue(),
            importScriptsStatusSearch: importScriptsStatusSearch.getValue(),

        };

        Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });
    scriptServiceReleaseStore.addListener('load', function (me, records, successful, eOpts) {
        if (scriptiids.length > 0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
                function (index, record) {
                    if (scriptiids.indexOf(record.get('iid')) > -1) {
                        chosedRecords.push(record);
                    }
                });
            scriptServiceReleaseGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70
    },
        {
            text: 'uperId',
            dataIndex: 'uperId',
            width: 40,
            hidden: true
        },
        {
            text: '服务主键',
            dataIndex: 'iid',
            width: 80,
            hidden: !showIdClomn
        },
        {
            text: '服务主键uuid',
            dataIndex: 'uuid',
            width: 300,
            hidden: !showIdClomn
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            width: 150,
            minWidth: 150,
            flex: 1,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            width: 150,
            minWidth: 150,
            flex: 1
        },
        {
            text: '标签',
            dataIndex: 'label',
            width: 100,
            hidden: !labelSwitch,
            flex: 1,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            hidden: !sdFunctionSortSwitch,
            width: 100
        },
        {
            text: '一级分类',
            dataIndex: 'buss',
            width: 100
        },
        {
            text: '二级分类',
            dataIndex: 'bussType',
            width: 100
        }, {
            text: '三级分类',
            dataIndex: 'threeTypeName',
            hidden: !scriptThreeBstypeSwitch,
            width: 100
        },
        {
            text: '脚本类型',
            dataIndex: 'scriptType',
            width: 80,
            hidden: !db_scriptTypeswitch,
            renderer: function (value, p, record, rowIndex) {
                var isflow = record.get('isflow');
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                } else if (value == "ps1") {
                    backValue = "powershell";
                }
                if (isflow == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        }, {
            text: '同步',
            dataIndex: 'issync',
            width: 80,
            hidden: !issync,
            renderer: function (value, p, record, rowIndex) {
                var backValue = "";
                if (value == "0") {
                    backValue = "未同步";
                } else if (value == "1") {
                    backValue = "已同步";
                } else if (value == "2") {
                    backValue = "审核中";
                }
                return backValue;
            }
        },
//		{
//		    text : '类型',
//		    dataIndex : 'scriptType',
//		    width : 80,
//		    hidden:db_scriptTypeswitch,
//		    renderer:function(value,p,record,rowIndex){
//		    	var isflow = record.get('isflow');
//		    	var backValue = "";
//				if (value == "sh") {
//					backValue = "shell";
//				} else if (value == "perl") {
//					backValue = "perl";
//				} else if (value == "py") {
//					backValue = "python";
//				} else if (value == "bat") {
//					backValue = "bat";
//				} else if (value == "sql") {
//					backValue = "sql";
//				}
//				if (isflow == '1') {
//					backValue = "组合";
//				}
//				return backValue;
//		    }
//		},
        {
            text: '适用平台',
            dataIndex: 'platForm',
            width: 80
        }, {
            text: '版本号',
            dataIndex: 'version',
            width: 80,
            hidden: db_versionswitch
        }, {
            text: '发布人',
            dataIndex: 'ssuer',
            width: 150,
            hidden: db_ssuer
        },
        {
            text: '使用次数',
            dataIndex: 'useTimes',
            width: 70,
            hidden: !db_useTimesswitch
        },
        {
            text: '发起次数',
            dataIndex: 'taskCount',
            width: 70,
            hidden: !scriptSubmitTaskCountSwitch
        },
        {
            text: '成功率',
            dataIndex: 'winTimes',
            width: 60,
            hidden: !db_winTimesswitch
        }, {
            text: '是否应急',
            dataIndex: 'isEmScript',
            width: 60,
            hidden: !reviewSwitch,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '否';
                } else if (value == 1) {
                    return '<font color="#F01024">是</font>';
                } else {
                    return '未知';
                }
            }
        },
        {
            text: '原子活动',
            dataIndex: 'isAtomic',
            width: 75,
            hidden: atomicScript,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '否';
                } else if (value == 1) {
                    return '<font color="#0CBF47">是</font>';
                }
            }
        },
        {
            text: '所属系统',
            dataIndex: 'appSystem',
            hidden: !reviewSwitch,
            width: 120
        },
        {
            text: '脚本状态',
            dataIndex: 'status',
            width: 70,
            renderer: function (value, p, record, rowIndex) {
                if (value == -1) {
                    return '<font color="#F01024">草稿</font>';
                } else if (value == 1) {
                    return '<font color="#0CBF47">已上线</font>';
                } else if (value == 2) {
                    return '<font color="#FFA602">审核中</font>';
                } else if (value == 3) {
                    return '<font color="#13B1F5">已共享</font>';
                } else if (value == 9) {
                    return '<font color="">已共享未发布</font>';
                } else {
                    return '<font color="#CCCCCC">未知</font>';
                }
            }
        },
        /*{
			text : '发布状态',
		    dataIndex : 'startType',
		    width : 100
		},*/
        {
            text: '共享状态',
            dataIndex: 'isshare',
            width: 80,
            hidden: db_isshareswitch,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '<font color="">未共享</font>';
                } else if (value == 1) {
                    return '<font color="#0CBF47">已共享</font>';
                } else {
                    return '<font color="#CCCCCC">未知</font>';
                }
            }
        },
        {
            text: '测试状态',
            dataIndex: 'isTry',
            width: 80,
            hidden: !istrySwitch,
            renderer: function (value, metadata, record) {
                if (value == "0" || value == "") {
                    return '<font color="#F01024">未测试</font>';
                } else if (value == "1") {
                    return '<font color="#FFA602">测试中</font>';
                } else if (value == "2") {
                    return '<font color="#0CBF47">已测试</font>';
                } else if (value == "3") {
                    var a = record.data.returnText;
                    metadata.tdAttr = 'data-qtip="' + a + '"';
                    return '<font color="">被打回</font>';
                }
            }
        },
        {
            text: '创建人',
            dataIndex: 'createUserName',
            width: 70,
            flex: 1,
            hidden: db_createUserNameSwitch
        },
        {
            text: '修改人',
            dataIndex: 'updateUserName',
            width: 70,
            flex: 1,
            hidden: db_updateUserNameSwitch
        },
        {
            text: '操作',
            xtype: 'actiontextcolumn',
            width: 280,
            align: 'left',
            items: [{
                text: '编辑',
                iconCls: 'script_edit',
                getClass: function (v, metadata, record) {
                    var isAtomic = record.data.isAtomic;
                    var status = record.data.status;
                    if (status == -1) {
                        if (isAtomic != 0) {
                            return 'x-hidden';
                        }
                    }
                },
                handler: function (grid, rowIndex) {

                    var iid = grid.getStore().data.items[rowIndex].data.iid; // 其实是requestID
                    var status = grid.getStore().data.items[rowIndex].data.status;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var version = grid.getStore().data.items[rowIndex].data.version;
                    var hasVersion = 0;
                    if (version) {
                        hasVersion = 1; // 最新版本有 版本号
                    }
                    var uuid = grid.getStore().data.items[rowIndex].data.uuid;
                    //下面var是光大新增
                    var serviceType = grid.getStore().data.items[rowIndex].data.serviceType;
                    var scriptName = grid.getStore().data.items[rowIndex].data.scriptName;
                    var filter_keywords = smkeywords.getValue();
                    //发起审核
                    var isExam = grid.getStore().data.items[rowIndex].data.isExam;
                    //使用平台
                    var platForm = grid.getStore().data.items[rowIndex].data.platForm;
                    //数据库类型
                    var dbType = grid.getStore().data.items[rowIndex].data.dbType;
                    var serviceIdNum = grid.getStore().data.items[rowIndex].data.serviceId;
                    var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                    var isTry = grid.getStore().data.items[rowIndex].data.isTry;
                    Ext.Ajax.request({
                        url: 'scriptService/queryScriptLabels.do',
                        method: 'POST',
                        async: false,
                        params: {
                            uuid: uuid
                        },
                        success: function (response, request) {
                            var lab = Ext.decode(response.responseText).dataList;
                            labels = lab.join(',')
                        },
                        failure: function (result, request) {
                        }
                    });
                    if (isflow == '1') {
                        editScriptFlow(iid, status, serviceName, bussId, bussTypeId, hasVersion, uuid, serviceType, scriptName, filter_keywords, isExam, platForm, dbType, serviceIdNum, isTry);
                    } else {
                        editScript(iid, status, serviceName, bussId, bussTypeId, hasVersion, uuid, serviceType, scriptName, filter_keywords, isExam, platForm, dbType, serviceIdNum, isTry, labels);
                    }
                }
            }, {
                text: '自测',
                hidden: fjFlag,
                iconCls: 'script_test',
                getClass: function (v, metadata, record) {
                    var scriptType = record.data.scriptType;
                    if (scriptType == 'sql'|| fjFlag) {
                        return 'x-hidden';
                    }
                },
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid; // 其实是requestID
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var uuid = grid.getStore().data.items[rowIndex].data.uuid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                    var execUserName = grid.getStore().data.items[rowIndex].data.execUserName;
                    if (scriptType != 'sql') {
                        if (isflow == '1') {
                            testScriptFlow(iid, serviceName, bussId, bussTypeId, scriptType, uuid);
                        } else {
                            testScriptNew(iid, serviceName, bussId, bussTypeId, scriptType, uuid, execUserName);
                        }
                    }
                }
            }, {
                text: '查看版本',
                iconCls: 'monitor_version',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid; // 其实是requestID
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var uuid = grid.getStore().data.items[rowIndex].data.uuid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (isflow == '1') {
                        viewVersionForFlow(iid, serviceName, bussId, bussTypeId, scriptType, uuid);
                    } else {
                        viewVersion(iid, serviceName, bussId, bussTypeId, scriptType, uuid, label);
                    }
                }
            }]
        }
    ];
    // 分页工具
//	var pageBar = Ext.create('Ext.PagingToolbar', {
//    	store: scriptServiceReleaseStore,
//        dock: 'bottom',
//        displayInfo: true,
//        afterPageText:' 页 共 {0} 页',
//        beforePageText:'第 ',
//        firstText:'第一页 ',
//        prevText:'前一页',
//        nextText:'下一页',
//        lastText:'最后一页',
//        refreshText:'刷新',
//        displayMsg:'第{0}条 到 {1} 条数据  共找到{2}条记录',
//        emptyMsg:'找不到任何记录'
//	});
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptServiceReleaseStore,
        dock: 'bottom',
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border: false,
        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
        emptyMsg: "没有记录"
    });
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelSS',
        checkOnly: true
    });
    var selModelAgents = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelAgentsSS',
        checkOnly: true
    });

    var selModelAgentssss = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelAgentsSSSSS',
        checkOnly: true
    });

    Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'sysName', type: 'string'},
            {name: 'agentIp', type: 'string'},
            {name: 'hostName', type: 'string'},
            {name: 'osType', type: 'string'},
            {name: 'agentPort', type: 'string'},
            {name: 'agentDesc', type: 'string'}
        ]
    });

    var allUsersStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'userModel',
        proxy: {
            type: 'ajax',
            url: 'getAllUsersToChangeUser.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    allUsersStore.on('beforeload', function (store, options) {
        var new_params = {
            userid: searchUser.getValue().trim(),
        };

        Ext.apply(allUsersStore.proxy.extraParams, new_params);
    });

    var allUsersColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '人员主键', dataIndex: 'userid', hidden: true},
        {text: '用户名称', dataIndex: 'ifullname', width: 120},
        {
            text: '隶属用户组',
            dataIndex: 'groupnames',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }
    ];

    var selModelAllUsers = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelAllUsersSS',
        mode: "SINGLE",
        checkOnly: true
    });

    var usersGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        title: '人员列表',
        store: allUsersStore,
        width: 650,
        selModel: selModelAllUsers,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: allUsersColumns
    });

    var users_panel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 680,
        height: 430,
        layout: 'border',
        items: [search_user_form, usersGrid]
    });

    function openScriptDenendScriptWindow() {
        let seledCnt = selModel.getCount();

        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要操作的脚本！");
            return;
        }
        scriptPublishsIds = [];
        if (seledCnt > 1 && !scriptBatchReleaseSwitch) {
            Ext.MessageBox.alert("提示", "每次只能选择一条脚本设置依赖脚本！");
            return;
        }
        let ss = selModel.getSelection();
        //脚本状态 如果不是草稿 -1 那么不允许依赖、也不允许取消依赖，只能查看已经依赖的脚本
        let scriptStatus = ss[0].data.status;
        //选择脚本的uuid
        let scriptUuid = ss[0].data.uuid;

        Ext.define('dependScriptLeftModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long',
            }, {
                name: 'srcScriptUuid',
                type: 'string'
            }, {
                name: 'srcScriptIid',
                type: 'string'
            }, {
                name: 'srcServiceName',
                type: 'string'
            }, {
                name: 'srcScriptName',
                type: 'string'
            }, {
                name: 'dependScriptIid',
                type: 'long'
            }, {
                name: 'dependScriptUuid',
                type: 'string'
            }, {
                name: 'dependScriptType',
                type: 'string'
            }, {
                name: 'dependServiceName',
                type: 'string'
            }, {
                name: 'dependScriptName',
                type: 'string'
            }, {
                name: 'dependScriptVersion',
                type: 'string'
            }, {
                name: 'dependFlag',
                type: 'string'
            }]
        });

        let dependScript_columns = [{
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
            {
                text: 'iid',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '源脚本uuid',
                dataIndex: 'srcScriptUuid',
                width: 40,
                hidden: true
            }, {
                text: '源脚本iid',
                dataIndex: 'srcScriptIid',
                width: 40,
                hidden: true
            }, {
                text: '源脚本服务名',
                dataIndex: 'srcServiceName',
                width: 120,
            }, {
                text: '源脚本名称',
                dataIndex: 'srcScriptName',
                width: 120,
            }, {
                text: '依赖脚本iid',
                dataIndex: 'dependScriptIid',
                width: 40,
                hidden: true
            }, {
                text: '依赖脚本uuid',
                dataIndex: 'dependScriptUuid',
                width: 40,
                hidden: true
            }, {
                text: '依赖脚本服务名',
                dataIndex: 'dependServiceName',
                width: 120,
            }, {
                text: '依赖脚本名称',
                dataIndex: 'dependScriptName',
                width: 120,
            }, {
                text: '依赖脚本类型',
                dataIndex: 'dependScriptType',
                width: 100,
            }, {
                text: '依赖脚本版本',
                dataIndex: 'dependScriptVersion',
                width: 80,
                flex: 1
            }, {
                text: '是否直接依赖',
                dataIndex: 'dependFlag',
                width: 40,
                hidden: true
            }];


        Ext.define('canDependScriptRightModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long',
            }, {
                name: 'scriptuuid',
                type: 'string'
            }, {
                name: 'serviceName',
                type: 'string'
            }, {
                name: 'scriptName',
                type: 'string'
            }, {
                name: 'scriptType',
                type: 'string'
            }, {
                name: 'version',
                type: 'long'
            }, {
                name: 'createUserName',
                type: 'string'
            }]
        });

        let canDependScript_columns = [{
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
            {
                text: 'iid',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '脚本uuid',
                dataIndex: 'srcScriptUuid',
                width: 40,
                hidden: true
            }, {
                text: '服务名称',
                dataIndex: 'serviceName',
                width: 140,

            }, {
                text: '脚本名称',
                dataIndex: 'scriptName',
                width: 140,
            }, {
                text: '版本',
                dataIndex: 'version',
                width: 140,
            }, {
                text: '脚本类型',
                dataIndex: 'scriptType',
                width: 140,
            }, {
                text: '创建人',
                dataIndex: 'createUserName',
                flex: 1,
                width: 40
            }];

        let serviceNameLeft = new Ext.form.TextField({
            name: 'serviceNameLeft',
            fieldLabel: '依赖脚本服务名称',
            emptyText: '--请输入服务名称--',
            labelWidth: 120,
            labelAlign: 'right',
            width: '33%'
        });
        let serviceNameRight = new Ext.form.TextField({
            name: 'serviceNameRight',
            fieldLabel: '服务名称',
            emptyText: '--请输入服务名称--',
            labelWidth: 65,
            labelAlign: 'right',
            width: '33%'
        });
        let dependScriptStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            model: 'dependScriptLeftModel',
            proxy: {
                type: 'ajax',
                url: 'getDependScript.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });
        dependScriptStore.on('beforeload', function (store, options) {
            let new_params = {
                scriptuuid: scriptUuid,
                dependServiceName: Ext.util.Format.trim(serviceNameLeft.getValue())
            };
            Ext.apply(dependScriptStore.proxy.extraParams, new_params);
        });

        let dependScriptLeftGrid = Ext.create('Ext.grid.Panel', {
            region: 'center',
            store: dependScriptStore,
            border: false,
            columnLines: true,
            viewConfig: {
                enableTextSelection: true
            },
            cls: 'window_border panel_space_left',
            columns: dependScript_columns,
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true, mode: 'SINGLE', allowDeselect: true})
            // listeners: {
            // 	select: function( e, record, index, eOpts ){
            // 		console.log(123);
            // 		// if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            // 		// 	chosedAgentIds.push(record.get('iid'));
            // 		// }
            // 		// if(chosedAgentShutdownIdsMap.size>0){
            // 		// 	chosedAgentShutdownIdsMap.set(record.get('iid'),"0");
            // 		// }
            // 	},
            // 	deselect: function( e, record, index, eOpts ){
            // 		console.log(123);
            // 		// if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
            // 		// 	chosedAgentIds.remove(record.get('iid'));
            // 		// }
            // 		// if(chosedAgentShutdownIdsMap.indexOf(record.get('iid'))>-1) {
            // 		// 	chosedAgentShutdownIdsMap.delete(record.get('iid'));
            // 		// }
            // 	}
            // }
        });

        let canDependScriptStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            model: 'canDependScriptRightModel',
            pageSize: 30,
            proxy: {
                type: 'ajax',
                url: 'getCanDependScript.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        canDependScriptStore.on('beforeload', function (store, options) {
            let new_params = {
                scriptuuid: scriptUuid,
                serviceName: Ext.util.Format.trim(serviceNameRight.getValue())
            };
            Ext.apply(canDependScriptStore.proxy.extraParams, new_params);
        });
        let canDependScriptRightGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store: canDependScriptStore,
            border: false,
            columnLines: true,
            cls: 'window_border panel_space_left',
            columns: canDependScript_columns,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true, mode: 'SINGLE', allowDeselect: true})
            /*listeners: {
				select: function( e, record, index, eOpts ){
					console.log(123);
					// if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
					// 	chosedAgentIds.push(record.get('iid'));
					// }
					// if(chosedAgentShutdownIdsMap.size>0){
					// 	chosedAgentShutdownIdsMap.set(record.get('iid'),"0");
					// }
				},
				deselect: function( e, record, index, eOpts ){
					console.log(123);
					// if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
					// 	chosedAgentIds.remove(record.get('iid'));
					// }
					// if(chosedAgentShutdownIdsMap.indexOf(record.get('iid'))>-1) {
					// 	chosedAgentShutdownIdsMap.delete(record.get('iid'));
					// }
				}
			}*/
        });


        let panelLeftForm = Ext.create('Ext.ux.ideal.form.Panel', {
                layout: 'anchor',
                region: 'north',
                // buttonAlign : 'center',
                autoScroll: true,
                border: false,
                bodyCls: 'x-docked-noborder-top',
                columnLines: true,
                cls: 'window_border panel_space_left',
                iqueryFun: function () {
                    dependScriptStore.reload();
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    border: false,
                    dock: 'top',
                    items: [serviceNameLeft]
                }, {
                    xtype: 'toolbar',
                    border: false,
                    dock: 'top',
                    items: ["->", {
                        xtype: 'button',
                        text: '查询',
                        cls: 'Common_Btn',
                        handler: function () {
                            dependScriptStore.reload();
                        }
                    }, {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            serviceNameLeft.setValue();
                        }
                    }, {
                        xtype: 'button',
                        text: '取消依赖',
                        cls: 'Common_Btn',
                        handler: function () {
                            if (scriptStatus !== -1) {
                                Ext.Msg.alert('提示', '只有在草稿状态下才能取消脚本依赖！');
                            } else {
                                let selectData = dependScriptLeftGrid.getSelectionModel().getSelection();
                                if (selectData.length === 0) {
                                    Ext.Msg.alert('提示', '请选择数据！');
                                } else {
                                    let scriptDenendIids = [];
                                    let canDeleteFlag = true;
                                    selectData.forEach(obj => {
                                        if (obj.data.dependFlag === '1') {
                                            scriptDenendIids.push(obj.data.iid);
                                        } else {
                                            canDeleteFlag = false;
                                        }
                                    });
                                    if (!canDeleteFlag) {
                                        Ext.Msg.alert('提示', '选择的依赖脚本为间接依赖，不是直接依赖，不允许取消依赖！');
                                    } else {
                                        Ext.Ajax.request({
                                            url: 'deleteDependScript.do',
                                            method: 'POST',
                                            dataType: 'json',
                                            jsonData: Ext.JSON.encode(scriptDenendIids),
                                            success: function (response, request) {
                                                let getSuccess = Ext.decode(response.responseText).success;
                                                let getMessage = Ext.decode(response.responseText).message;
                                                if (getSuccess === 'true' || getSuccess) {
                                                    dependScriptStore.reload();
                                                    canDependScriptRightGrid.ipage.moveFirst();
                                                    Ext.Msg.alert('提示', getMessage);
                                                } else {
                                                    Ext.Msg.alert('提示', getMessage);
                                                }
                                            },
                                            failure: function (result, request) {
                                                secureFilterRs(result, "操作失败！");
                                            }
                                        });
                                    }

                                }
                            }
                        }
                    }]
                }]
            }
        );
        let panelRightForm = Ext.create('Ext.ux.ideal.form.Panel', {
                layout: 'anchor',
                region: 'north',
                // buttonAlign : 'center',
                autoScroll: true,
                border: false,
                bodyCls: 'x-docked-noborder-top',
                columnLines: true,
                cls: 'window_border panel_space_left',
                iqueryFun: function () {
                    canDependScriptRightGrid.ipage.moveFirst();
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    border: false,
                    dock: 'top',
                    items: [serviceNameRight]
                }, {
                    xtype: 'toolbar',
                    border: false,
                    items: ["->", {
                        xtype: 'button',
                        text: '查询',
                        cls: 'Common_Btn',
                        handler: function () {
                            canDependScriptRightGrid.ipage.moveFirst();
                        }
                    }, {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            serviceNameRight.setValue();
                        }
                    }, {
                        xtype: 'button',
                        text: '绑定依赖',
                        cls: 'Common_Btn',
                        handler: function () {
                            if (scriptStatus !== -1) {
                                Ext.Msg.alert('提示', '只有在草稿状态下才可以绑定依赖脚本！');
                            } else {
                                let selectData = canDependScriptRightGrid.getSelectionModel().getSelection();
                                if (selectData.length === 0) {
                                    Ext.Msg.alert('提示', '请选择数据！');
                                } else {
                                    let scriptDenends = [];
                                    selectData.forEach(obj => {
                                        //依赖脚本对象构建
                                        let scriptDenend = {
                                            "srcScriptUuid": scriptUuid,
                                            "dependScriptUuid": obj.data.scriptuuid
                                        };
                                        scriptDenends.push(scriptDenend);
                                    });
                                    Ext.Ajax.request({
                                        url: 'saveDependScript.do',
                                        method: 'POST',
                                        dataType: 'json',
                                        jsonData: Ext.JSON.encode(scriptDenends),
                                        success: function (response, request) {
                                            let getSuccess = Ext.decode(response.responseText).success;
                                            let getMessage = Ext.decode(response.responseText).message;
                                            if (getSuccess === 'true' || getSuccess) {
                                                dependScriptStore.reload();
                                                canDependScriptRightGrid.ipage.moveFirst();
                                                Ext.Msg.alert('提示', getMessage);
                                            } else {
                                                Ext.Msg.alert('提示', getMessage);
                                            }
                                        },
                                        failure: function (result, request) {
                                            secureFilterRs(result, "操作失败！");
                                        }
                                    });
                                }
                            }
                        }
                    }]
                }]
            }
        );

        let panelLeft = Ext.create('Ext.panel.Panel', {
            layout: 'border',
            title: '依赖脚本',
            bodyCls: 'x-docked-noborder-top',
            cls: 'window_border  right_edge',
            region: 'west',
            border: false,
            // split : true,
            width: '50%',
            items: [panelLeftForm, dependScriptLeftGrid]
        });

        let panelRight = Ext.create('Ext.panel.Panel', {
            layout: 'border',
            title: '待依赖的脚本',
            bodyCls: 'x-docked-noborder-top',
            cls: 'window_border panel_space_right',
            region: 'center',
            border: false,
            // split : true,
            width: '50%',
            items: [panelRightForm, canDependScriptRightGrid]
        });


        let scriptDenendScriptWindow = Ext.create('Ext.window.Window', {
            // title : '依赖脚本',
            autoScroll: true,
            modal: true,
            resizable: false,
            constrain: true,
            closeAction: 'destroy',
            layout: 'border',
            width: contentPanel.getWidth() - 120,
            height: contentPanel.getHeight(),
            items: [panelLeft, panelRight]
        }).show();


    }

    var scriptServiceitems = [
        {
            xtype: 'toolbar',
            border: false,
            //baseCls:'customize_gray_back',
            dock: 'top',
            items: ['->', {
                text: '创建',
                cls: 'Common_Btn',
                hidden: !createScript,
                handler: function () {
                    destroyRubbish(); //销毁本页垃圾
                    contentPanel.getLoader().load({
                        url: 'basicScriptEdit.do',
                        //		params: filter,
                        scripts: true
                    });
                }
            }, {
                text: '设置原子活动',
                cls: 'Common_Btn',
                hidden: atomicScript,
                handler: function () {
                    var data = scriptServiceReleaseGrid.getView().getSelectionModel().getSelection();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请至少选择一条记录设置原子活动!');
                    } else {
                        var draftCon = 0;
                        for (var i = 0; i < data.length; i++) {
                            var status = data[i].get('status');//脚本状态已上线可以设置为原子活动
                            var isAtomic = data[i].get('isAtomic')
                            /*if(status!=1){
						    				Ext.Msg.alert('提示', '脚本状态为已上线时可以设置为原子活动，请重新选择!');
						    				draftCon++;
						    				break;
						    			}*/
                            if (isAtomic != 0) {
                                Ext.Msg.alert('提示', '已经设置为原子活动，请重新选择!');
                                draftCon++;
                                break;
                            }
                        }
                        if (draftCon == 0) {
                            saveAtomicScript();
                        }
                    }
                }
            }, {
                text: '取消原子活动',
                cls: 'Common_Btn',
                hidden: atomicScript,
                handler: function () {
                    var data = scriptServiceReleaseGrid.getView().getSelectionModel().getSelection();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请至少选择一条记录取消原子活动!');
                    } else {
                        var draftCon = 0;
                        for (var i = 0; i < data.length; i++) {
                            var isAtomic = data[i].get('isAtomic');//脚本状态已上线可以设置为原子活动
                            if (isAtomic == 0) {
                                Ext.Msg.alert('提示', '已经取消原子活动，请重新选择!');
                                draftCon++;
                                break;
                            }
                        }
                        if (draftCon == 0) {
                            deleteAtomic();
                        }
                    }
                }
            }, {
                text: '同步到生产',
                cls: 'Common_Btn',
                hidden: !issync,
                handler: function () {
                    var seledCnt = selModel.getCount();
                    if (seledCnt < 1) {
                        Ext.MessageBox.alert("提示", "请选择要同步的记录！");
                        return;
                    }
                    scriptPublishsIds = [];
                    if (seledCnt > 1) {
                        Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
                        return;
                    }
                    publishSyncScript(selectedRecords);

                }
            },
                {
                    text: '交叉测试',
                    cls: 'Common_Btn',
                    hidden: !istrySwitch,
                    handler: function () {
                        var seledCnt = selModel.getCount();
                        if (seledCnt < 1) {
                            Ext.MessageBox.alert("提示", "请选择要测试的记录！");
                            return;
                        }
                        scriptPublishsIds = [];
                        if (seledCnt > 1) {
                            Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
                            return;
                        }
                        var ss = selModel.getSelection();
                        if (ss[0].data.status != -1 && ss[0].data.status != 9) {
                            Ext.MessageBox.alert("提示", "只能测试未发布的脚本！");
                            return;
                        }
                        var isTry = ss[0].data.isTry;
                        if (isTry == 0 || isTry == 3 || isTry == null) {
                            tryScript(selectedRecords);
                        } else if (isTry == 1) {
                            Ext.MessageBox.alert("提示", "该记录已经在测试中！");
                        } else if (isTry == 2) {
                            Ext.MessageBox.alert("提示", "该记录已测试通过！");
                        }
                    }
                },
                {
                    text: '依赖脚本',
                    cls: 'Common_Btn',
                    hidden: !scriptDenendScriptSwitch,
                    handler: openScriptDenendScriptWindow
                },
                {
                    text: '创建者转移',
                    cls: 'Common_Btn',
                    handler: function () {
                        var seledScriptCnt = selModel.getCount();
                        if (seledScriptCnt < 1) {
                            Ext.MessageBox.alert("提示", "请选择要转移的脚本！");
                            return;
                        }
                        allUsersStore.load();

                        //弹框，展示所有人
                        showAllUserWin = Ext.create('widget.window', {
                            title: '选择目标人员',
                            closable: true,
                            closeAction: 'hide',
                            modal: true,
                            width: 700,
                            height: 570,
                            layout: {
                                type: 'border',
                                padding: 5
                            },
                            items: [users_panel],
                            dockedItems: [{
                                xtype: 'toolbar',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    cls: 'Common_Btn',
                                    text: "转移",
                                    handler: function (grid, rowIndex) {
                                        var seledCnt = selModelAllUsers.getCount();
                                        if (seledCnt < 1) {
                                            Ext.MessageBox.alert("提示", "请选择人员！");
                                            return;
                                        }
                                        //获取转移者
                                        var userid = '';
                                        var userids = usersGrid.getSelectionModel().getSelection();
                                        Ext.each(userids, function (item) {// 遍历
                                            userid = item.data.userid;
                                        });

                                        //获取脚本id
                                        var scriptiidsArr = [];
                                        var scriptiids = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                                        Ext.each(scriptiids, function (item) {// 遍历
                                            scriptiidsArr.push(item.data.uperId)
                                        });
                                        var iids = scriptiidsArr.join(",");


                                        var jsonData = getSelectedJsonData();
                                        Ext.Ajax.request({
                                            url: 'changeTransCreateUser.do',
                                            method: 'POST',
                                            async: false,
                                            params: {
                                                userid: userid,
                                                iids: iids
                                            },
                                            success: function (response, request) {
                                                var getSuccess = Ext.decode(response.responseText).success;
                                                var getMessage = Ext.decode(response.responseText).message;
                                                if (getSuccess == 'true' || getSuccess == true) {
                                                    showAllUserWin.close();
                                                    allUsersStore.reload();
                                                    scriptServiceReleaseStore.reload();
                                                    Ext.Msg.alert('提示', '转移成功！');
                                                } else {
                                                    showAllUserWin.close();
                                                    allUsersStore.reload();
                                                    scriptServiceReleaseStore.reload();
                                                    Ext.Msg.alert('提示', getMessage);
                                                }
                                            },
                                            failure: function (result, request) {
                                                showAllUserWin.close();
                                                secureFilterRs(result, "操作失败！");
                                            }
                                        });

                                    }
                                }, {
                                    xtype: "button",
                                    cls: 'Common_Btn',
                                    text: "取消",
                                    handler: function () {
                                        allUsersStore.reload();
                                        showAllUserWin.close();
                                    }
                                }]
                            }]
                        });
                        showAllUserWin.show();
                    }
                },
                {
                    text: '共享',
                    cls: 'Common_Btn',
                    hidden: shareSwitch,
                    handler: shareServiceRelease
                }, {
                    text: '提交ITSM',
                    cls: 'Common_Btn',
                    hidden: !cibItsmScriptSwitch,
                    handler: cibItsmScriptPublish
                }, {
                    text: '发布',
                    cls: 'Common_Btn',
                    handler: function () {
                        var seledCnt = selModel.getCount();
                        if (seledCnt < 1) {
                            Ext.MessageBox.alert("提示", "请选择要发布的记录！");
                            return;
                        }
                        scriptPublishsIds = [];
                        if (seledCnt > 1 && !scriptBatchReleaseSwitch) {
                            Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
                            return;
                        }
                        if (istrySwitch) {
                            var ss = selModel.getSelection();
                            for (var i = 0, len = ss.length; i < len; i++) {
                                if (ss[i].data.isTry != 2) {
                                    Ext.MessageBox.alert("提示", "选择项中含有未测试通过的脚本！");
                                    return;
                                }
                            }
                        }
                        //外层发布按钮对脚本内容是否为空校验
                        let uuid = [];
                        selectedRecords.forEach((selectedRecord) => {
                            uuid.push(selectedRecord.uuid);
                        });
                        Ext.Ajax.request({
                            url: 'checkScriptContent.do',
                            method: 'POST',
                            params: {
                                uuid: uuid
                            },
                            success: function (response, opts) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if (success) {
                                    Ext.Msg.alert('提示', message);
                                    return
                                } else {
                                    //不为空正常发布
                                    publishScript(selectedRecords);
                                }
                            },
                            failure: function (result, request) {
                                secureFilterRs(result, "操作失败！");
                            }
                        });
                        /*
								var ss = selModel.getSelection();
								var version = ss[0].data.version;
								var hasVersion = 0;
								if(version) {
									hasVersion = 1; // 最新版本有 版本号
								}
								if(ss[0].get('isflow')=='1') {
									// 检查脚本组合里，是否有没有发布的脚本
									Ext.Ajax.request({
										url : 'checkScriptFlowHasTestScript.do',
										method : 'POST',
										params : {
											serviceId : ss[0].data.iid
										},
										success: function(response, opts) {
											var success = Ext.decode(response.responseText).success;
											if(success) {
												publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
											} else {
												var message = Ext.decode(response.responseText).message;
												Ext.MessageBox.alert("提示", "该脚本组合中，某些步骤所选择的脚本服务没有发布，请发布后重试。<br>没有发布的脚本有："+message);
												return;
											}
										},
										failure: function(result, request) {
											secureFilterRs(result,"操作失败！");
										}
									});
								} else {
									publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
								}*/
                    }
                }, {
                    text: '批量修改',
                    cls: 'Common_Btn',
                    hidden: !gfScriptDirFunctionSwitch,
                    handler: function () {
                        // var m = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                        // if (m.length == 0) {
                        //     Ext.Msg.alert('提示', "请选择要修改绑定目录的脚本");
                        //     return;
                        // } else {
                        //     if (bacthUpdateScriptDirWin != null) {
                        //         bacthUpdateScriptDirWin.close();
                        //     }
                        //     var uuids = [];
                        //     for (let i = 0; i < m.size; i++) {
                        //         uuids.push(m[i].value.uuid)
                        //     }
                        var m = selectedRecords;
                        if (m.size == 0) {
                            Ext.Msg.alert('提示', "请选择要修改绑定目录的脚本");
                            return;
                        } else {
                            if (bacthUpdateScriptDirWin != null) {
                                bacthUpdateScriptDirWin.close();
                            }
                            var uuids = [];
                            selectedRecords.forEach((selectedRecord) => {
                                uuids.push(selectedRecord.uuid);
                            });
                            var formPanel = Ext.create('Ext.form.Panel', {
                                width: '100%',
                                height: '100%',
                                items: [{
                                    border: false,
                                    layout: 'column',
                                    margin: '5',
                                    items: [{
                                        width: 400,
                                        minPickerHeight: 20,
                                        id: 'batchUpdateScriptDirId',
                                        displayField: 'iscriptDirName',
                                        valueField: 'iid',
                                        editable: false,
                                        xtype: 'treepicker',
                                        value: '',
                                        store: Ext.create('Ext.data.TreeStore', {
                                            autoLoad: gfScriptDirFunctionSwitch,
                                            fields: ['iid', 'iscriptDirName'],
                                            root: {
                                                expanded: gfScriptDirFunctionSwitch
                                            },
                                            proxy: {
                                                type: 'ajax',
                                                url: 'scriptEdit/scriptDirList.do'
                                            }
                                        }),
                                        listeners: {
                                            select: function (picker, record) {
                                                // 校验选择 不是末级目录，无法选择
                                                if (record.childNodes.length > 0) {
                                                    picker.setValue('')
                                                }
                                            }
                                        }
                                    }]
                                }],
                                buttons: [
                                    {
                                        xtype: 'button',
                                        text: '保存',
                                        cls: 'Common_Btn',
                                        handler: function () {
                                            var scriptDirId = Ext.getCmp('batchUpdateScriptDirId').getValue();
                                            if (scriptDirId == '') {
                                                Ext.Msg.alert('提示', '请选择要修改的末级目录');
                                            } else {
                                                Ext.Ajax.request({
                                                    url: 'updateScript/updateScriptDirBind.do',
                                                    method: 'POST',
                                                    params: {
                                                        uuids: Ext.encode(uuids),
                                                        scriptDirId: scriptDirId
                                                    },
                                                    success: function (response) {
                                                        var success = Ext.decode(response.responseText).success;
                                                        var message = Ext.decode(response.responseText).message;
                                                        if (success) {
                                                            bacthUpdateScriptDirWin.close();
                                                        }
                                                        scriptServiceReleaseStore.reload();
                                                        Ext.Msg.alert('提示', message);
                                                    },
                                                    failure: function () {
                                                        Ext.Msg.alert('提示', '网络连接失败');
                                                    }
                                                })
                                            }
                                        }
                                    },
                                    {
                                        xtype: 'button',
                                        text: '取消',
                                        cls: 'Common_Btn',
                                        handler: function () {
                                            bacthUpdateScriptDirWin.close();
                                        }
                                    }
                                ]
                            })

                            bacthUpdateScriptDirWin = Ext.create('Ext.window.Window', {
                                title: '批量修改',
                                layout: 'border',
                                height: 180,
                                width: 450,
                                items: [formPanel]
                            }).show();
                        }
                    }
                }, {
                    text: '批量修改',
                    cls: 'Common_Btn',
                    hidden: !sdFunctionSortSwitch,
                    handler: function () {
                        var m = selectedRecords;
                        if (m.size == 0) {
                            Ext.Msg.alert('提示', "请选择要修改的脚本");
                            return;
                        }else{
                        var groupNameStore = Ext.create('Ext.data.Store', {
                            model: 'groupNameModel',
                            autoLoad: sdFunctionSortSwitch,
                            proxy: {
                                type: 'ajax',
                                url: 'queryComboGroupName.do',
                                reader: {
                                    type: 'json',
                                    root: 'dataList'
                                }
                            }
                        });
                        var bussData = Ext.create('Ext.data.Store', {
                            fields: ['iid', 'bsName'],
                            autoLoad: sdFunctionSortSwitch ? false : true,
                            proxy: {
                                type: 'ajax',
                                url: 'bsManager/getBsAll.do',
                                reader: {
                                    type: 'json',
                                    root: 'dataList'
                                }
                            }
                        });
                        var bussTypeData = Ext.create('Ext.data.Store', {
                            fields: ['sysTypeId', 'sysType'],
                            autoLoad: false,
                            proxy: {
                                type: 'ajax',
                                url: 'bsManager/getBsTypeByFk.do',
                                reader: {
                                    type: 'json',
                                    root: 'dataList'
                                }
                            }
                        });

                        var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
                            name: 'groupName',
                            labelWidth: 70,
                            columnWidth: .5,
                            queryMode: 'local',
                            afterLabelTextTpl: required,
                            fieldLabel: '功能分类',
                            padding: '0 5 0 0',
                            hidden: !sdFunctionSortSwitch,
                            displayField: 'GNAME',
                            valueField: 'IID',
                            editable: true,
                            emptyText: '--请选功能分类-',
                            store: groupNameStore,
                            listeners: {
                                change: function () { // old is keyup
                                    smbussCb.clearValue();
                                    smbussCb.applyEmptyText();
                                    smbussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                                    bussData.load({
                                        params: {
                                            fk: this.value
                                        }
                                    });
                                },
                                beforequery: function (e) {
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var value = Ext.util.Format.trim(e.query);
                                        combo.store.filterBy(function (record, id) {
                                            var text = record.get(combo.displayField);
                                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                }
                            }
                        });
                        var smbussCb = Ext.create('Ext.form.field.ComboBox', {
                            name: 'sysName',
                            labelWidth: 70,
                            columnWidth: .5,
                            queryMode: 'local',
                            afterLabelTextTpl: required,
                            fieldLabel: '一级分类',
                            padding: '0 5 0 0',
                            displayField: 'bsName',
                            valueField: 'iid',
                            editable: true,
                            emptyText: '--请选一级分类--',
                            store: bussData,
                            listeners: {
                                change: function () { // old is keyup
                                    smbussTypeCb.clearValue();
                                    smbussTypeCb.applyEmptyText();
                                    smbussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                                    bussTypeData.load({
                                        params: {
                                            fk: this.value
                                        }
                                    });
                                },
                                beforequery: function (e) {
                                    var combo = e.combo;
                                    if (!e.forceAll) {
                                        var value = Ext.util.Format.trim(e.query);
                                        combo.store.filterBy(function (record, id) {
                                            var text = record.get(combo.displayField);
                                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                                        });
                                        combo.expand();
                                        return false;
                                    }
                                }
                            }
                        });

                        var smbussTypeCb = Ext.create('Ext.form.field.ComboBox', {
                            name: 'bussType',
                            padding: '0 5 0 0',
                            labelWidth: 70,
                            columnWidth: .5,
                            queryMode: 'local',
                            afterLabelTextTpl: required,
                            fieldLabel: '二级分类',
                            displayField: 'sysType',
                            valueField: 'sysTypeId',
                            editable: true,
                            emptyText: '--请选择二级分类--',
                            store: bussTypeData
                        });
                        var formPanel = Ext.create('Ext.form.Panel', {
                            width: '100%',
                            height: '100%',
                            buttonAlign: 'center',
                            items: [{
                                border: false,
                                layout: 'column',
                                margin: '5',
                                items: [groupNameCombo, smbussCb, smbussTypeCb
                                ]
                            }],
                            buttons: [
                                {
                                    xtype: 'button',
                                    text: '保存',
                                    cls: 'Common_Btn',
                                    handler: function () {
                                        var groupNameValue = groupNameCombo.getValue();
                                        var bussValue = smbussCb.getValue();
                                        var bussTypeValue = smbussTypeCbsmbussTypeCb.getValue();
                                        if (groupNameValue == null || groupNameValue == '') {
                                            Ext.Msg.alert('提示', '请选择功能分类');
                                            return;
                                        }
                                        if (bussValue == null || bussValue == '') {
                                            Ext.Msg.alert('提示', '请选择一级分类');
                                            return;
                                        }
                                        if (bussTypeValue == null || bussTypeValue == '') {
                                            Ext.Msg.alert('提示', '请选择二级分类');
                                            return;
                                        }
                                        var uperIds = [];
                                        selectedRecords.forEach((selectedRecord) => {
                                            uperIds.push(selectedRecord.uperId);
                                        });
                                        // var data = scriptServiceReleaseGrid.getView().getSelectionModel().getSelection();
                                        // var uperIds = [];
                                        // Ext.Array.each(data, function (
                                        //     record) {
                                        //     var uperId = record.get('uperId');
                                        //     uperIds.push(uperId);
                                        // });
                                        Ext.Ajax.request({
                                            url: 'updateBatchScript.do',
                                            method: 'POST',
                                            params: {
                                                uperId: uperIds.join(','),
                                                groupName: groupNameCombo.getRawValue(),
                                                bsName: smbussCb.getRawValue(),
                                                bussName: smbussTypeCb.getRawValue(),
                                                groupId: groupNameCombo.getValue(),
                                                bsId: smbussCb.getValue(),
                                                bussId: smbussTypeCb.getValue()

                                            },
                                            success: function (response) {
                                                var success = Ext.decode(response.responseText).success;
                                                var message = Ext.decode(response.responseText).message;
                                                if (success) {
                                                    bacthUpdateScriptFunctionSortWin.close();
                                                }
                                                scriptServiceReleaseStore.reload();
                                                Ext.Msg.alert('提示', message);
                                            },
                                            failure: function () {
                                                Ext.Msg.alert('提示', '网络连接失败');
                                            }
                                        })
                                        bacthUpdateScriptFunctionSortWin.close();
                                    }
                                },
                                {
                                    xtype: 'button',
                                    text: '取消',
                                    cls: 'Common_Btn',
                                    handler: function () {
                                        bacthUpdateScriptFunctionSortWin.close();
                                    }
                                }
                            ]
                        })
                        bacthUpdateScriptFunctionSortWin = Ext.create('Ext.window.Window', {
                            title: '批量修改',
                            layout: 'border',
                            height: 260,
                            width: 400,
                            closeAction: 'destroy',
                            modal: true,
                            items: [formPanel]
                        }).show();
                        // if(bacthUpdateScriptFunctionSortWin!=null){
                        //     bacthUpdateScriptFunctionSortWin.destroy();
                        // }
                    }
                    }
                }, '-', {
                    text: '删除',
                    itemId: 'delete',
                    cls: 'Common_Btn',
                    // disabled: true,
                    handler: deleteServiceRelease
                }, {
                    text: '投产介质',
                    cls: 'Common_Btn',
                    hidden: !scriptManageProductionSwitch,
                    handler: function () {
                        if (selectedRecords.size == 0) {
                            Ext.MessageBox.alert("提示", "请选择要导出的任务！");
                            return;
                        } else {    	//window.location.href = 'exportServiceJsonFileForProduction.do?iidStr[]='+iidStr+'&exportType=1';
                            var ids = [];
                            var continueSwitch = true;
                            selectedRecords.forEach((selectedRecord) => {
                                if(selectedRecord.status != 1) {
                                    Ext.MessageBox.alert('提示', '投产介质只有允许导出已上线状态脚本');
                                    continueSwitch = false;
                                }
                                ids.push(selectedRecord.iid);
                            });
                            if (continueSwitch) {
                                $.fileDownload('exportServiceJsonFileForProduction.do', {
                                    httpMethod: 'POST',
                                    traditional: true,
                                    data: {
                                        iidStr: scriptiids,
                                        exportType: "1"
                                    },
                                    successCallback: function (url) {

                                    },
                                    failCallback: function (html, url) {
                                        Ext.Msg.alert('提示', '导出失败！');
                                        return;
                                    }
                                });
                            }
                        }
                    }
                }, {
                    text: '导入',
                    cls: 'Common_Btn',
                    hidden: importSwitch,
                    handler: uploadExcel
                }, {
                    text: '下发',
                    cls: 'Common_Btn',
                    hidden: issuedbuttonSwitch,
                    handler: sendScript
                },
                {
                    text: '下载脚本',
                    cls: 'Common_Btn',
                    handler: function () {
                        downloadScript();
                    }
                }, {
                    text: '查看下发结果',
                    cls: 'Common_Btn',
                    hidden: issuedbuttonSwitch,
                    handler: function () {
                        queryIssueRecord(2);
                    }
                }]
        }];

    var scriptServiceReleaseGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        overflowY: 'hidden',
        store: scriptServiceReleaseStore,
        selModel: selModel,
        plugins: [cellEditing],
        dockedItems: scriptServiceitems,
        border: true,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        // bbar : pageBar,
        columnLines: true,
        cls: 'customize_panel_back',
        padding: grid_space,
        columns: scriptServiceReleaseColumns,
        listeners: {
            select: function (e, record, index, eOpts) {
                if (scriptiids.indexOf(record.get('iid')) == -1) {
                    scriptiids.push(record.get('iid'));
                    checkItems.push(record.get('iid'));
                }
                if (!selectedRecords.has(record.data)) {
                    selectedRecords.add(record.data);
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (scriptiids.indexOf(record.get('iid')) > -1) {
                    scriptiids.remove(record.get('iid'));
                    checkItems.remove(record.get('iid'));
                }
                if (selectedRecords.has(record.data)) {
                    selectedRecords.delete(record.data);
                }
            }
        }
    });


    Ext.define('scriptDirModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iscriptDirName',
                type: 'string'
            }, {
                name: 'iscriptDirSort',
                type: 'long'
            }, {
                name: 'iid',
                type: 'long'
            }, {
                name: 'iscriptDirDiscript',
                type: 'string'
            }, {
                name: 'iscriptDirLevel',
                type: 'int'
            }, {
                name: 'iscriptDirRootId',
                type: 'string'
            }
        ]

    })

    var scriptDirTreeStore = Ext.create('Ext.data.TreeStore', {
        autoLoad: gfScriptDirFunctionSwitch,
        model: 'scriptDirModel',
        proxy: {
            type: 'ajax',
            url: 'scriptEdit/scriptDirList.do'
        },
        root: {
            expanded: gfScriptDirFunctionSwitch,
            iscriptDirName: '根目录',
            icon: 'ext/resources/ext-theme-neptune/images/tree/folder.png'
        }
    });

    var treePanel = Ext.create('Ext.tree.Panel', {
        // height : '80%',
        region: 'west',
        width: '16%',
        id: 'treeId',
        title: '脚本目录',
        autoScroll: true,
        collapsible: true,
        cls: 'customize_panel_back',
        animate: true,
        useArrows: true,
        hidden: !gfScriptDirFunctionSwitch,
        rootVisible: false,
        store: scriptDirTreeStore,
        hideHeaders: true,
        columns: [{
            xtype: 'treecolumn',
            text: '目录',
            flex: 1,
            dataIndex: 'iscriptDirName',
            sortable: false
        }],
        border: true,
        padding: grid_space,
        columnLines: true,
        listeners:{
            select:function(){
                scriptPublishsIds = [];
                selectedRecords.clear();
                scriptiids = [];
                queryData();
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });

    scriptDirTreeStore.on('load', function (store, records) {
        if (records.childNodes.length == 0) {
            var flag = true;
            var treeViewDiv = treePanel.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(treePanel.getView().emptyText);
                treePanel.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            var selModel = Ext.getCmp('treeId').getSelectionModel();
            var m = selModel.getSelection();
            var filterScriptDir = Ext.decode(filter.filter_scriptDir);
            if (filterScriptDir.length == 0) {
                var root = store.getRootNode();
                selModel.select(root)
            } else {
                var nodeHash = store.tree.nodeHash;
                for (let key in nodeHash) {
                    if (nodeHash[key].data.iid == filterScriptDir[0]) {
                        selModel.select(nodeHash[key]);
                        break;
                    }
                }
            }
        }

    })
    let renderTo = "scriptService_grid_area";
    if(isTabSwitch){
        renderTo += smnow;
    }
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: renderTo,
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        bodyPadding: grid_margin,
        border: true,
        layout: 'border',
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        items: [searchItems, treePanel, scriptServiceReleaseGrid]
    });

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

    function shareServiceRelease() {
        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要共享的脚本！");
            return;
        }

        var checkMessage = "";
        Ext.Ajax.request({
            url: 'checkScriptStatu.do',
            method: 'POST',
            async: false,
            params: {
                scriptiids: scriptiids
            },
            success: function (response, request) {
                checkMessage = Ext.decode(response.responseText).message;
            },
            failure: function (result, request) {
                Ext.Msg.alert('提示', '校验脚本状态失败！');
            }
        });
        if (checkMessage != null && checkMessage != "") {
            Ext.Msg.alert('提示', checkMessage);
            return;
        }

        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认共享", "是否共享选中的服务", function (id) {
            if (id == 'yes') {
                openShareWin();
            }
        });
    }

    /**
     * 兴业itsm提交发布方法
     */
    function cibItsmScriptPublish() {
        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要提交的脚本！");
            return;
        }

        // 已经发布过的服务名数组
        let msgArray1 = [];
        // 处于审核中的服务名数组
        let msgArray2 = [];
        scriptPublishsIds = [];
        let selectedRecordItsmArray = [];
        selectedRecords.forEach((selectedRecord) => {
            let itsmObj = {}
            if (selectedRecord.version) {
                msgArray1.push(selectedRecord.serviceName);
            }
            if (selectedRecord.status == 2) { // 处于审核中
                msgArray2.push(selectedRecord.serviceName);
            }
            itsmObj["iid"] = 0;
            itsmObj["workitemiid"] = 0;
            itsmObj["scriptiid"] = selectedRecord.iid;
            itsmObj["scriptuuid"] = selectedRecord.uuid;
            itsmObj["taskuuid"] = "";
            selectedRecordItsmArray.push(itsmObj);
            // scriptPublishsIds.push(selectedRecord.iid);
        });

        if (msgArray1.length > 0 || msgArray2.length > 0) {
            // scriptPublishsIds.splice(0,scriptPublishsIds.length);
            let msg1 = '';
            let msg2 = '';
            msgArray1.length > 0 ? msg1 = '【' + msgArray1.join('，') + '】服务已经发布过！' : '';
            msgArray2.length > 0 ? msg2 = '【' + msgArray2.join('，') + '】服务正处于审核中！' : '';
            Ext.Msg.alert('提示', msg1 + '&#10' + msg2);
            return;
        }

        //Ext.JSON.encode(selectedRecordItsmArray)
        submitItsmWindown(selectedRecordItsmArray);

    }

    function submitItsmWindown(selectedRecordItsmArray) {
        let submitItsmWin;
        let taskName = new Ext.form.TextField({
            name: 'taskName',
            fieldLabel: '标题',
            emptyText: '--请填写标题--',
            labelWidth: 93,
            margin: '10 0 0 0',
            afterLabelTextTpl: required,
            allowBlank: false, //不允许为空
            blankText: "不能为空",
            labelAlign: 'right',
            columnWidth: .5
        });
        var flagStore = Ext.create('Ext.data.Store', {
            fields: ['id', 'name'],
            data: [
                {"id": "1", "name": "否"},
                {"id": "0", "name": "是"}
            ]
        });
        var stateCombo = Ext.create('Ext.form.field.ComboBox', {
            name: 'stateCombo',
            // padding : '5',
            labelWidth: 93,
            queryMode: 'local',
            fieldLabel: '应急处置',
            displayField: 'name',
            valueField: 'id',
            margin: '10 0 0 0',
            afterLabelTextTpl: required,
            allowBlank: false, //不允许为空
            blankText: "不能为空",
            editable: false,
            emptyText: '--请选择状态--',
            store: flagStore,
            columnWidth: .5,
            // width: '50%',
            // value:"1",
            labelAlign: 'right',
            listeners: {
                change: function (combo, records, eOpts) {
                    approverUser.setValue('');
                    var isFlag = this.getValue();
                    if (this.value != null && this.value != "") {
                        approverStore.load({
                            params: {
                                isFlag: isFlag
                            }
                        });
                    }
                },
                afterRender: function (combo) {
                    var firstValue = flagStore.getAt(0).get("name");
                    var id = flagStore.getAt(0).get("id");
                    combo.setRawValue(firstValue);//同时下拉框会将与name为firstValue值对应的 text显示
                    combo.setValue(id);
                }
            }
        });
        let approverStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            autoDestroy: true,
            storeId: 'approver_store',
            fields: ['iid', 'iname'],
            proxy: {
                type: 'ajax',
                url: 'getItsmapprover.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        approverStore.on('beforeload', function (store, options) {
            let new_params = {
                isFlag: 1
            };
            Ext.apply(approverStore.proxy.extraParams, new_params);
        });
        let approverUser = Ext.create('Ext.form.field.ComboBox', {
            name: 'approver',
            store: approverStore,
            fieldLabel: '审批人',
            labelWidth: 93,
            columnWidth: .5,
            afterLabelTextTpl: required,
            // width: '45%',
            labelAlign: 'right',
            allowBlank: false, //不允许为空
            blankText: "不能为空",
            margin: '10 0 0 0',
            displayField: 'iname',
            valueField: 'iname',
            listConfig: {
                maxHeight: 200
            },
            editable: true,
            listeners: {
                select: function () {
                },
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = e.query;
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(
                                value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }
            }
        });

        let urgencyStore = Ext.create('Ext.data.Store', {
            fields: ['iid', 'name'],
            data: [
                {"iid": "0", "name": "特急"},
                {"iid": "1", "name": "高"},
                {"iid": "2", "name": "中"},
                {"iid": "3", "name": "低"}
            ]
        });
        let urgencyComboBox = Ext.create('Ext.form.field.ComboBox', {
            fieldLabel: '紧急程度',
            labelWidth: 93,
            name: 'urgency',
            margin: '10 0 0 0',
            labelAlign: 'right',
            allowBlank: false, //不允许为空
            blankText: "不能为空",
            displayField: 'name',
            valueField: 'name',
            afterLabelTextTpl: required,
            store: urgencyStore,
            queryMode: 'local',
            editable: false,
            columnWidth: .5,
            // width: '50%',
            listeners: {
                afterRender: function (combo) {
                    var firstValue = urgencyStore.getAt(1).get("name");
                    combo.setValue(firstValue);//同时下拉框会将与name为firstValue值对应的 text显示
                }
            }
        });
        let taskDesc = Ext.create('Ext.form.field.TextArea', {
            name: 'taskDesc',
            fieldLabel: '申请理由',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            height: 55,
            afterLabelTextTpl: required,
            allowBlank: false, //不允许为空
            blankText: "不能为空",
            columnWidth: 1,
            autoScroll: true
        });
        let handlingOpinions = Ext.create('Ext.form.field.TextArea', {
            name: 'handlingOpinions',
            fieldLabel: '审批意见',
            afterLabelTextTpl: required,
            allowBlank: false, //不允许为空
            blankText: "不能为空",
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            height: 55,
            columnWidth: 1,
            autoScroll: true
        });

        let scriptLevelCb_itsm = Ext.create('Ext.form.field.ComboBox', {
            name: 'scriptLevel',
            labelWidth: 93,
            columnWidth: 1,
            queryMode: 'local',
            fieldLabel: '风险级别',
            margin: '10 0 0 0',
            displayField: 'scriptLevel',
            valueField: 'iid',
            editable: false,
            afterLabelTextTpl: required,
            allowBlank: false, //不允许为空
            labelAlign: 'right',
            hidden: !scriptLevelSwitch,
            emptyText: '--请选择风险级别--',
            store: levelStore_sm
        })

        let submitItsmWin_form = Ext.create('Ext.ux.ideal.form.Panel', {
            width: 600,
            layout: 'anchor',
            bodyCls: 'x-docked-noborder-top',
            buttonAlign: 'center',
            border: false,
            items: [{
                //	    	layout:'form',
                anchor: '98%',
                padding: '5 0 5 0',
                border: false,
                items: [{
                    layout: 'column',
                    border: false,
                    items: [taskName, approverUser]//标题 审批人
                }, {
                    layout: 'column',
                    border: false,
                    items: [stateCombo, urgencyComboBox]//是否应急
                }, {
                    layout: 'column',
                    border: false,
                    items: [scriptLevelCb_itsm]//风险级别
                }, {
                    layout: 'column',
                    border: false,
                    items: [taskDesc]//申请理由
                }, {
                    layout: 'column',
                    border: false,
                    items: [handlingOpinions]//处理意见
                }]
            }
            ]
        });


        if (!submitItsmWin) {
            submitItsmWin = Ext.create('widget.window', {
                title: '提交ITSM',
                closable: true,
                closeAction: 'destroy',
                modal: true,
                width: 680,
                minWidth: 350,
                height: 430,
                layout: {
                    type: 'border',
                    padding: 5
                },
                items: [submitItsmWin_form],
                dockedItems: [{
                    xtype: 'toolbar',
//				  	 	baseCls:'customize_gray_back',
                    dock: 'bottom',
                    layout: {pack: 'center'},
                    items: [{
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "提交",
                        handler: function () {
                            if (!submitItsmWin_form.getForm().isValid()) {
                                Ext.Msg.alert('提示', "请输入必填项!!");
                                return;
                            }
                            //默认审核人
                            var auditor = auditorComBox_sm.getValue();
                            //分线级别
                            var scriptLevel = scriptLevelCb_itsm.getValue();
                            var taskNameValue = taskName.getValue();
                            var taskDescValue = taskDesc.getValue();
                            var approverUserValue = approverUser.getValue();
                            var stateValue = stateCombo.getValue();
                            var urgencyValue = urgencyComboBox.getValue();
                            var handlingOpinionsValue = handlingOpinions.getValue();

                            // let productData = productRecordGrid.getSelectionModel().getSelection();
                            //
                            // let fileName=productData[0].data.fileName;
                            // let productIid =productData[0].data.iid;
                            Ext.MessageBox.wait("数据处理中...", "进度条");
                            Ext.Ajax.request({
                                url: 'scriptPublishSubmitToItsm.do',
                                method: 'POST',
                                // async: false,
                                dataType: 'json',
                                // headers:{'Content-Type':'application/json'},
                                jsonData: Ext.JSON.encode(selectedRecordItsmArray),
                                params: {
                                    // itsmpublishList:
                                    scriptLevel: scriptLevel,
                                    itaskname: taskNameValue,
                                    itaskdesc: taskDescValue,//申请理由
                                    iapprover: approverUserValue,
                                    isflag: stateValue,//应急处置
                                    iappidea: handlingOpinionsValue,//审批意见
                                    iurgency: urgencyValue,//紧急程度,
                                    iapprovestep: '提交'
                                },
                                success: function (response, request) {
                                    let msg = Ext.decode(response.responseText).msg;
                                    Ext.Msg.alert('提示', msg);
                                    selectedRecords.clear();
                                    scriptPublishsIds = [];
                                    scriptiids = [];
                                    scriptServiceReleaseGrid.ipage.moveFirst();
                                    submitItsmWin.close();
                                    return;
                                },
                                failure: function (result, request) {
                                    Ext.Msg.alert('提示', '提交itsm失败！');
                                    return;
                                }
                            });
                        }
                    }
                    ]
                }],
                listeners: {
                    'close': function () {
                        submitItsmWin_form.getForm().reset();
                        stateCombo.setValue("1");
                        urgencyComboBox.setValue("高");
                    }
                }
            });

        }
        submitItsmWin.show();
    }

    //下载脚本
    function downloadScript() {
//    	var record = scriptservice_grid.getSelectionModel ().getSelection ();
//    	var iidStr = "";
//		Ext.Array.each (record, function (recordObj)
//		{
//			iidStr += "," + recordObj.get ('iid');
//		});
//    	if(iidStr.length<=0)
//    	{
//    		Ext.Msg.alert ('提示','请选择要操作的行！');
//    		return;
//    	}
//    	iidStr = iidStr.substr(1);
//    	window.location.href = 'scriptDownload.do?iid='+iidStr;


//    	var seledCnt = selModel.getCount();
//		if(seledCnt < 1){
//			Ext.MessageBox.alert("提示", "请选择要下载的脚本！");
//			return ;
//		}
//         var record = scriptServiceReleaseGrid.getSelectionModel().getSelection();
//         var iidStr = "";
//         Ext.Array.each(record, function (recordObj) {
//             iidStr += "," + recordObj.get('iid');
//         });
        if (checkItems.length <= 0) {
            Ext.Msg.alert('提示', '请选择要操作的行！');
            return;
        }
        // iidStr = iidStr.substr(1);
        window.location.href = 'scriptDownload.do?iid=' + checkItems + '&questionType=1';

    }

    Ext.define('AuditorModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'loginName',
            type: 'string'
        }, {
            name: 'fullName',
            type: 'string'
        }]
    });

    auditorStore_sm = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getPublishAuditorList.do?dbaasFlag=0',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });


    auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
        fieldLabel: "审核人",
        labelWidth: 93,
//	    padding: 5,
        labelAlign: 'right',
        store: auditorStore_sm,
        queryMode: 'local',
//	    width: 200,
        columnWidth: .98,
        margin: '10 0 0 0',
        displayField: 'fullName',
        editable: true,
        valueField: 'loginName',//,
        listeners: { //监听
            render: function (combo) {//渲染
                combo.getStore().on("load", function (s, r, o) {
                    combo.setValue(r[0].get('loginName'));//第一个值
                });
            },
            select: function (combo, records, eOpts) {
                var fullName = records[0].raw.fullName;
                combo.setRawValue(fullName);
            },
//			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_sm.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_sm.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_sm.setValue("");
//					 	return;
//					}
//				}
//
//			},
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    auditorStore_sm1 = Ext.create('Ext.data.Store', {
        autoLoad: issync,
        model: 'AuditorModel',
        proxy: {
            type: 'ajax',
            url: 'getPublishSyncAuditorList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    Ext.define('tryModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'name',
            type: 'string'
        }, {
            name: 'id',
            type: 'string'
        }]
    });

    tryStore_sm = Ext.create('Ext.data.Store', {
        autoLoad: istrySwitch,
        model: 'tryModel',
        proxy: {
            type: 'ajax',
            url: 'getTryGroupList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    tryGroup = Ext.create('Ext.form.ComboBox', {
        fieldLabel: "审核组",
        labelWidth: 93,
        labelAlign: 'right',
        store: tryStore_sm,
        queryMode: 'local',
        columnWidth: .95,
        margin: '10 0 0 0',
        displayField: 'name',
        editable: true,
        valueField: 'id',//,
        listeners: { //监听
            render: function (combo) {//渲染
                combo.getStore().on("load", function (s, r, o) {
                    combo.setValue(r[0].get('id'));//第一个值
                });
            },
            select: function (combo, records, eOpts) {
                var name = records[0].raw.name;
                combo.setRawValue(name);
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var auditorComBox_sm1 = Ext.create('Ext.form.ComboBox', {//同步发布的审核人
        fieldLabel: "审核人",
        labelWidth: 93,
//	    padding: 5,
        labelAlign: 'right',
        store: auditorStore_sm1,
        queryMode: 'local',
//	    width: 200,
        columnWidth: .95,
        margin: '10 0 0 0',
        displayField: 'fullName',
        editable: true,
        valueField: 'loginName',//,
        listeners: { //监听
            render: function (combo) {//渲染
                combo.getStore().on("load", function (s, r, o) {
                    if (r.length > 0) {
                        combo.setValue(r[0].get('loginName'));//第一个值
                    }
                });
            },
            select: function (combo, records, eOpts) {
                var fullName = records[0].raw.fullName;
                combo.setRawValue(fullName);
            },
//			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_sm.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_sm.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_sm.setValue("");
//					 	return;
//					}
//				}
//
//			},
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    planTime_sm = Ext.create('Go.form.field.DateTime', {
        fieldLabel: '计划时间',
        format: 'Y-m-d H:i:s',
        labelWidth: 65,
        hidden: true,
//	    width:200,
        columnWidth: .98,
        margin: '10 0 0 0'
    });

    pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        labelAlign: 'right',
        emptyText: '',
        labelWidth: 93,
        margin: '10 0 0 0',
        maxLength: 255,
        height: 55,
        columnWidth: .98,
        autoScroll: true
    });

    var orderNumber_sm = new Ext.form.TextField({
        fieldLabel: '单号',
        emptyText: '--请输入单号--',
        labelWidth: 93,
        labelAlign: 'right',
        hidden: !orderNumberSwitch,
        columnWidth: .98
    });

    if (psbcBindAgentSwicht) {
        pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
            name: 'pubdesc',
            fieldLabel: '发布申请说明',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            height: 200,
            columnWidth: .98,
            autoScroll: true
        });
    }

    var pubDesc_sm1 = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        labelAlign: 'right',
        emptyText: '',
        labelWidth: 93,
        margin: '10 0 0 0',
        maxLength: 255,
        height: 55,
        columnWidth: .98,
        autoScroll: true
    });

    var levelStore_sm = Ext.create('Ext.data.Store', {
        fields: ['iid', 'scriptLevel'],
        data: [
            {"iid": "0", "scriptLevel": "白名单"},
            {"iid": "1", "scriptLevel": "高级风险"},
            {"iid": "2", "scriptLevel": "中级风险"},
            {"iid": "3", "scriptLevel": "低级风险"}
        ]
    });

    scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 93,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '风险级别',
        margin: '10 0 0 0',
        displayField: 'scriptLevel',
        valueField: 'iid',
        editable: false,
        labelAlign: 'right',
        hidden: !scriptLevelSwitch,
        emptyText: '--请选择风险级别--',
        store: levelStore_sm
    });

    Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    appSysStore1.on('load', function () {
        var ins_rec = Ext.create('AppSysModel1', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        //字符串转数组
    });
    var appSysObj1 = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '应用系统',
        emptyText: '--请选择应用系统--',
        hidden: !reviewSwitch,
        multiSelect: true,
        labelWidth: 93,
        labelAlign: 'right',
        columnWidth: .95,
        store: appSysStore1,
        padding: '10 0 0 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            select: function (combo, records, eOpts) {
                if (records) {
                    chosedAppSys = new Array();
                    for (var i = 0; i < records.length; i++) {
                        chosedAppSys.push(records[i].data.id);
                    }
                }

            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        hidden: !reviewSwitch,
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });
    var forbidden = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否禁用旧版本',
        inputValue: 1,
        width: 120,
        margin: '10 0 0 10'
    });

    if (psbcBindAgentSwicht) {
        forbidden = Ext.create('Ext.form.field.Checkbox', {
            boxLabel: '是否禁用旧版本',
            inputValue: 1,
            margin: '10 0 0 10',
            fieldLabel: '旧版本禁用',
            labelAlign: 'right',
            emptyText: '',
            labelWidth: 93,
            margin: '10 0 0 0',
            maxLength: 255,
            columnWidth: .98
        });
    }

    auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'center',
        layout: 'anchor',
        bodyCls: 'x-docked-noborder-top',
        buttonAlign: 'center',
        border: false,
        items: [{
//	    	layout:'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [planTime_sm]
            }, {
                layout: 'column',
                border: false,
                items: [scriptLevelCb_sm]
            }, {
                layout: 'column',
                border: false,
                items: [appSysObj1, isEMscript]
            }, {
                layout: 'column',
                border: false,
                items: [auditorComBox_sm, forbidden]
            }, {
                layout: 'column',
                border: false,
                items: [pubDesc_sm]
            }, {
                layout: 'column',
                border: false,
                items: [orderNumber_sm]
            }]
        }]
    });

    if (psbcBindAgentSwicht) {
        auditing_form_sm = Ext.create('Ext.ux.ideal.form.Panel', {
            region: 'center',
            layout: 'anchor',
            bodyCls: 'x-docked-noborder-top',
            buttonAlign: 'center',
            border: false,
            items: [{
//	    	layout:'form',
                anchor: '98%',
                padding: '5 0 5 0',
                border: false,
                items: [{
                    layout: 'column',
                    border: false,
                    items: [planTime_sm]
                }, {
                    layout: 'column',
                    border: false,
                    items: [scriptLevelCb_sm],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [appSysObj1],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [isEMscript],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [auditorComBox_sm],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [forbidden],
                    width: 400
                }, {
                    layout: 'column',
                    border: false,
                    items: [pubDesc_sm],
                    width: 400
                }]
            }]
        });
    }

    Ext.define('systemModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'systemName',
            type: 'string'
        }, {
            name: 'itype',
            type: 'int'
        }]
    });

    var systemStore = Ext.create('Ext.data.Store', {
        autoLoad: psbcBindAgentSwicht,
        autoDestroy: true,
        model: 'systemModel',
        proxy: {
            type: 'ajax',
            url: 'getSystem.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        },
        listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;

                //根据全局的选择，初始化选中的列
                var selModel = systemgrid.getSelectionModel();
                for (var j = 0; j < selectedSysRecords.length; j++) {
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if (selectedSysRecords[j] == record.get("iid")) {
                            selModel.select(record, true, true);    //选中record，并且保持现有的选择，不触发选中事件
                        }
                    }
                }
            }
        }
    });

    systemStore.on('beforeload', function (store, options) {
        var new_params = {
            sysname: sysname.getValue()
        };

        Ext.apply(systemStore.proxy.extraParams, new_params);
    });

    var systemColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '业务系统名称', dataIndex: 'systemName', flex: 1},
        {text: '工程类型', dataIndex: 'itype', flex: 1, hidden: true}];
    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'cpId',
            type: 'int'
        }, {
            name: 'cpName',
            type: 'string'
        }, {
            name: 'ip',
            type: 'string'
        }, {
            name: 'iagentinfo_id',
            type: 'int'
        }]
    });

    var agentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'businessSystemComputerList.do',
            reader:
                {
                    type: 'json',
                    root: 'dataList'
                }
        },
        listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;

                //根据全局的选择，初始化选中的列
                var selModel = agentgrid.getSelectionModel();
                selectedAgentRecords.forEach(function (item) {
                    for (var i = 0; i < records.length; i++) {
                        var record = records[i];
                        if (item.agentid == record.get("cpId")) {
                            selModel.select(record, true, true);    //选中record，并且保持现有的选择，不触发选中事件
                        }
                    }
                })
            }
        }
    });
    agentStore.on('beforeload', function (store, options) {
        var new_params = {
            sysIdForQuery: systemId,
            ipBetween: '',
            ipEnd: '',
            opersystype: type == null ? 0 : type,
            cpName: '',
            ipAddr:ipaddr.getValue()
        };

        Ext.apply(agentStore.proxy.extraParams, new_params);
    });

    var selModelsystem = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        // mode: "SINGLE",
        listeners: {
            select: function (selModel, record, index, eOpts) {
                sysid = record.get("iid");
                systemId =record.get("iid");
                type=record.get("itype"),
                agentStore.load(
                    {
                        params: {
                            sysIdForQuery: record.get("iid"),
                            ipBetween: '',
                            ipEnd: '',
                            opersystype: record.get("itype") == null ? 0 : record.get("itype"),
                            cpName: '',
                            ipAddr:ipaddr.getValue()
                        }
                    }
                );
                selectedSysRecords.push(record.get("iid"));
            },
            deselect: function (selModel, record, index, eOpts) {
                systemId=undefined;
                agentStore.removeAll();
                selectedAgentRecords.forEach(function (item) {
                    if (item.sysid == sysid) {
                        selectedAgentRecords.remove(item);
                    }
                })
                selectedSysRecords.remove(record.get('iid'));
            }
        }
    });

    var sysname = new Ext.form.TextField({
        name: 'userPermission',
        fieldLabel: '系统名称',
        emptyText: '--请输入系统名称--',
        labelWidth: 65,
        labelAlign: 'right',
        width: 260
    });
    var ipaddr = new Ext.form.TextField({
        name: 'ipaddr',
        fieldLabel: 'ip地址',
        emptyText: '--请输入ip地址--',
        labelWidth: 65,
        labelAlign: 'right',
        width: 260
    });
    var agentGroup = new Ext.form.TextField({
        name: 'agentGroup',
        fieldLabel: 'ip地址',
        emptyText: '--请输入ip地址--',
        labelWidth: 65,
        labelAlign: 'right',
        width: 260
    });
    var systemgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        //title:'业务系统',
        autoScroll: true,
        width: 650,
        store: systemStore,
        selModel: selModelsystem,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border',
        columns: systemColumns,
        dockedItems: [
            {
                xtype: 'toolbar',
                cls: 'customize_panel_back',
                items: [sysname, {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        systemgrid.ipage.moveFirst();
                    }
                }
                ]
            }]
    });

    var agentColumnsScriptssss = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName', width: 150},
        {text: 'IP', dataIndex: 'agentIp', width: 150},
        {text: '计算机名', dataIndex: 'hostName', width: 150},
        {text: '操作系统', dataIndex: 'osType', width: 150},
        {text: '端口', dataIndex: 'agentPort', width: 120},
        {text: '描述', dataIndex: 'agentDesc', flex: 1}
        //{ text: '状态',  dataIndex: 'agentip'}
    ];


    var agentGroupGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: agentGroupStoreScript,
        selModel: selModelAgentssss,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border',
        columns: agentColumnsScriptssss,
        dockedItems: [
            {
                xtype: 'toolbar',
                cls: 'customize_panel_back',
                items: [agentGroup, {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        agentGroupGrid.ipage.moveFirst();
                    }
                }
                ]
            }]
    });


    var selModelagent = Ext.create('Ext.selection.CheckboxModel', {
        //mode: "MULTI",
        checkOnly: true,
        listeners: {
            select: function (selModel, record, index, eOpts) {
                var dsid = record.get("cpId");
                var tmpRec = {};
                tmpRec.sysid = sysid;
                tmpRec.agentid = dsid;
                selectedAgentRecords.push(tmpRec);
                selectedSysRecords.remove(sysid);
                console.log("------------");
                console.log("selectedSysRecords:" + selectedSysRecords);
                console.log("selectedAgentRecords:" + selectedAgentRecords);
            },
            deselect: function (selModel, record, index, eOpts) {
                var dsid = record.get("cpId");
                selectedAgentRecords.forEach(function (item) {
                    console.log(item.sysid + '---' + item.agentid);
                    if (item.sysid == sysid && item.agentid == dsid) {
                        selectedAgentRecords.remove(item);
                    }
                })
                console.log("+++++++++++");
                console.log("selectedSysRecords:" + selectedSysRecords);
                console.log("selectedAgentRecords:" + selectedAgentRecords);
            }
        }
    });

    var agentColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: 'cpId', dataIndex: 'cpId', hidden: true},
        {text: '机器名', dataIndex: 'cpName', flex: 1},
        {text: '地址', dataIndex: 'ip', width: 150},
        {text: 'iagentinfo_id', dataIndex: 'iagentinfo_id', width: 120, hidden: true}];

    var agentColumnsScript = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '名称', dataIndex: 'sysName'},
        {text: 'IP', dataIndex: 'agentIp'},
        {text: '计算机名', dataIndex: 'hostName'},
        {text: '操作系统', dataIndex: 'osType'},
        {text: '端口', dataIndex: 'agentPort'},
        {text: '描述', dataIndex: 'agentDesc'}
        //{ text: '状态',  dataIndex: 'agentip'}
    ];


    var agentgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        //title:'设备',
        store: agentStore,
        width: 650,
        selModel: selModelagent,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: agentColumns,
        dockedItems : [  {
            xtype : 'toolbar',
            cls:'customize_panel_back',
            items: [ipaddr, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    if(systemId==undefined){
                        Ext.Msg.alert('提示', "请选择业务系统");
                        return
                    }
                    agentgrid.ipage.moveFirst();
                }
            }
            ]
        }]
    });

    var agentgridScript = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'east',
        autoScroll: true,
        title: '设备',
        store: agentStoreScript,
        width: 720,
        selModel: selModelAgents,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border: true,
        columnLines: true,
        cls: 'window_border panel_space_left',
        columns: agentColumnsScript,
        listeners: {
            select: function (e, record, index, eOpts) {
                if (agentiids.indexOf(record.get('iid')) == -1) {
                    agentiids.push(record.get('iid'));
                }
                if (!selectedAgent.has(record.data)) {
                    selectedAgent.add(record.data);
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (agentiids.indexOf(record.get('iid')) > -1) {
                    agentiids.remove(record.get('iid'));
                }
                if (selectedAgent.has(record.data)) {
                    selectedAgent.delete(record.data);
                }
            }
        }
    });

    project_panel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1300,
        height: 300,
        layout: 'border',
        items: [systemgrid, agentgrid]
    });

    agent_panel = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1100,
        height: 300,
        layout: 'border',
        items: [agentGroupGrid]
    });

    var rightArea = Ext.create('Ext.ux.ideal.form.Panel', {
            width: 730,
            height: 350,
            layout: 'border',
            region: 'east',
            autoScroll: true,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            border: true,
            columnLines: true,
            cls: 'window_border panel_space_left',
            items: [search_ip_form, agentgridScript]
        }
    );

    project_panelScript = Ext.create('Ext.panel.Panel', {
        region: 'south',
        border: false,
        width: 1200,
        height: 430,
        layout: 'border',
        items: [auditing_form_sm, rightArea]
    });

    agentStoreScript.on('beforeload', function (store, options) {
        var new_params = {
            startIp: ipStart.getValue().trim(),
            endIp: ipEnd.getValue().trim()
        };

        Ext.apply(agentStoreScript.proxy.extraParams, new_params);
    });

    agentStoreScript.addListener('load', function (me, records, successful, eOpts) {
        if (agentiids.length > 0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
                function (index, record) {
                    if (agentiids.indexOf(record.get('iid')) > -1) {
                        chosedRecords.push(record);
                    }
                });
            agentgridScript.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
    });

    auditing_form_sync = Ext.create('Ext.ux.ideal.form.Panel', {
        layout: 'anchor',
        region: 'center',
        bodyCls: 'x-docked-noborder-top',
        buttonAlign: 'center',
        border: false,
        items: [{
//	    	layout:'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [auditorComBox_sm1]
            }, {
                layout: 'column',
                border: false,
                items: [pubDesc_sm1]
            }]
        }]
    });

    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
            {
                name: 'attachmentName',
                type: 'string'
            },
            {
                name: 'attachmentSize',
                type: 'string'
            },
            {
                name: 'attachmentUploadTime',
                type: 'string'
            }]
    });

    try_form = Ext.create('Ext.ux.ideal.form.Panel', {
        layout: 'anchor',
        region: 'center',
        bodyCls: 'x-docked-noborder-top',
        buttonAlign: 'center',
        border: false,
        items: [{
//		    	layout:'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [tryGroup]
            }]
        }]
    });

    var attachStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    attachStore.on('beforeload', function (store, options) {
        var new_params = {
            wId: workitemid,
            ids: attachmentIds
        };

        Ext.apply(attachStore.proxy.extraParams, new_params);
    });
    attachStore.on('load', function (me, records, successful, eOpts) {
        attachmentIds = []
        $.each(records, function (index, record) {
            attachmentIds.push(record.get('iid'));
        });
        console.log(attachmentIds);
    });
    var attachColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '附件名称',
            dataIndex: 'attachmentName',
            flex: 1,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            menuDisabled: true,
            sortable: false,
            xtype: 'actioncolumn',
            width: 50,
            items: [{
                iconCls: 'attachment_delete',
                tooltip: '删除',
                handler: function (grid, rowIndex, colIndex) {
                    var rec = attachStore.getAt(rowIndex);
                    var a = [];
                    a.push(rec.get('iid'));
                    Ext.Ajax.request({
                        url: 'deleteAttachment.do',
                        method: 'POST',
                        sync: true,
                        params: {
                            iids: a,
                            wId: workitemid
                        },
                        success: function (response, request) {
                            var success = Ext.decode(response.responseText).success;
                            if (success) {
                                Ext.Msg.alert('提示', '删除成功！');
                                removeByValue(attachmentIds, rec.get('iid'));
                                attachStore.load();
                            } else {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "保存失败！");
                        }
                    });
                }
            }
            ]
        }];
    var selectedAttachmentButton = Ext.create("Ext.Button",
        {
            cls: 'Common_Btn',
            disabled: false,
            text: '添加附件',
            handler: selectAttachmentFun
        });

    function removeByValue(arr, val) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == val) {
                arr.splice(i, 1);
                break;
            }
        }
    }

    var attachGrid = Ext.create('Ext.grid.Panel', {
        region: 'south',
        height: 200,
        width: 580,
        title: '附件',
        autoScroll: true,
        store: attachStore,
        hidden: isAttach ? false : true,
        border: false,
        columnLines: true,
        cls: 'attachments customize_panel_back',
        columns: attachColumns,
        margin: '0 10 0 0',
        emptyText: '没有附件',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            border: false,
            items: [
                '->', selectedAttachmentButton//添加附件按钮
            ]
        }]
    });
    if (reviewSwitch && isAttach) {
        myhight = 610;
    } else if (reviewSwitch) {
        myhight = 410;
    } else if (isAttach) {
        myhight = 580;
    } else {
        myhight = 350;
    }
    Ext.define('warnningModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'serviceName',
            type: 'string'
        }, {
            name: 'sysName',
            type: 'string'
        }, {
            name: 'bussName',
            type: 'string'
        }, {
            name: 'version',
            type: 'string'
        }, {
            name: 'user',
            type: 'string'
        }]
    });

    var warnningStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'warnningModel',
        proxy: {
            type: 'ajax',
            url: 'scriptCallSearch.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    warnningStore.on('beforeload', function (store, options) {
        var new_params = {
            iid: warnId
        };

        Ext.apply(warnningStore.proxy.extraParams, new_params);
    });
    var warnningColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
        {text: '主键', dataIndex: 'iid', hidden: true},
        {text: '服务名称', dataIndex: 'serviceName', flex: 1},
        {text: '一级分类', dataIndex: 'sysName', width: 120},
        {text: '二级分类', dataIndex: 'bussName', width: 100},
        {
            text: '版本', dataIndex: 'version', width: 80, renderer: function (value, p, record, rowIndex) {
                if (value) {
                    return value;
                } else {
                    return '无版本号';
                }
            }
        },
        {text: '创建用户', dataIndex: 'user', width: 120}
    ];
    warnningGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: warnningStore,
        border: false,
        columnLines: true,
        cls: 'customize_panel_back',
        columns: warnningColumns
    });

    function deleteServiceRelease() {
        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要删除的脚本！");
            return;
        }
        if(!scriptCrossPublishPassSwitch){
            var ss = selModel.getSelection();
            for (var i = 0, len = ss.length; i < len; i++) {
                if (ss[i].data.status != -1) {
                    Ext.MessageBox.alert("提示", "只能删除状态为'草稿'的脚本！");
                    return;
                }
            }
        }

        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认删除", "是否删除选中的脚本", function (id) {
            if (id == 'yes') release(1);
        });
    }

    var attachmentUploadWin = null;

    function selectAttachmentFun() {
        var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'attachment_id',
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
                // margin: '10 10 0 40',
                buttonText: '浏览',
                multipleFn: function ($this) {

                    var typeArray = ["application/x-shockwave-flash", "audio/MP3", "image/*", "flv-application/octet-stream"];

                    var fileDom = $this.getEl().down('input[type=file]');

                    fileDom.dom.setAttribute("multiple", "multiple");

                    fileDom.dom.setAttribute("accept", typeArray.join(","));

                },
                listeners: {
                    afterrender: function () {
                        this.multipleFn(this);
                    },
                    change: function () {
                        var fileDom = this.getEl().down('input[type=file]');
                        var files = fileDom.dom.files;
                        var str = '';
                        for (var i = 0; i < files.length; i++) {
                            str += files[i].name;
                            str += ' ';
                        }
                        Ext.getCmp('attachment_id').setRawValue(str);    //files为组件的id
                        this.multipleFn(this);
                    }
                }
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    this.up("window").close();
                }
            }]
        });

        attachmentUploadWin = Ext.create('Ext.window.Window', {
            title: '附件信息',
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: 600,
            height: 200,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            },
            /*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout: 'fit'
        });

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("files").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        /** 自定义遮罩效果* */
        var myUploadMask = new Ext.LoadMask(contentPanel,
            {
                msg: "附件上传中..."
            });

        function uploadTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploadAttachmentFile.do',
                    success: function (form, action) {
                        var success = Ext.decode(action.response.responseText).success;
                        var msg = Ext.decode(action.response.responseText).message;
                        if (success) {
                            var ids = Ext.decode(action.response.responseText).ids;
                            attachmentIds.push.apply(attachmentIds, ids.split(","));
                        } else {
                            Ext.Msg.alert('提示', msg);
                        }
                        attachmentUploadWin.close();
                        myUploadMask.hide();
                        attachStore.load();
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', msg);
                        myUploadMask.hide();
                    }
                });
            }
        }

        attachmentUploadWin.show();
    }


    function openShareWin() {
        Ext.define('shareClumnModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'IID',
                type: 'long'
            }, {
                name: 'IFULLNAME',//用户名
                type: 'string'
            }, {
                name: 'ILOGINNAME',//用户登录ID
                type: 'string'
            }, {
                name: 'IGROUPNAME',//组名称
                type: 'string'
            }, {
                name: 'OBJECTNAME',//对象名称 已共享使用
                type: 'string'
            }, {
                name: 'ISERVICESNAME',//服务名称 已共享使用
                type: 'string'
            }, {
                name: 'SHARETYPE', // 共享类型 已共享使用
                type: 'string'
            }, {
                name: 'IGROUPDES', //组描述
                type: 'string'
            }]
        });
        //待共享展示列表 Store
        var shareColumnStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            autoDestroy: true,
            model: 'shareClumnModel',
            proxy: {
                type: 'ajax',
                url: 'getScriptShareColumnObject.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });

        shareColumnStore.on('beforeload', function (store, options) {
            var new_params = {
                shareType: shareType,
                shareed: 0, //0代表查询未共享的
                scriptiids: scriptiids,
                objectName: objectName.getValue()
            };

            Ext.apply(shareColumnStore.proxy.extraParams, new_params);
        });

        shareColumnStore.addListener('load', function (me, records, successful, eOpts) {
            if (chosedShareIds.length > 0) {
                var chosedRecords = []; //存放选中记录
                $.each(records,
                    function (index, record) {
                        if (chosedShareIds.indexOf(record.get('IID')) > -1) {
                            chosedRecords.push(record);
                        }
                    });
                shareGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
            }

        });


        //已共享展示列表 Store
        var shareedColumnStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            model: 'shareClumnModel',
            proxy: {
                type: 'ajax',
                url: 'getScriptShareColumnObject.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });

        shareedColumnStore.on('beforeload', function (store, options) {
            var new_params = {
                shareType: shareType,
                shareed: 1, //已共享 参数  1代表查询已共享
                scriptiids: scriptiids
            };

            Ext.apply(shareedColumnStore.proxy.extraParams, new_params);
        });

//	var shareColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		    store: shareColumnStore,
//			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//			border:false
//	    });
//	var shareedColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		    store: shareedColumnStore,
//			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//			border:false
//	    });
        var shareColumns = [{text: '序号', xtype: 'rownumberer', width: 40}
        ];
        var shareedColumns = [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '用户主键', dataIndex: 'IID', hidden: true},
            {text: '服务名称', dataIndex: 'ISERVICESNAME', width: 200},
            {text: '共享类型', dataIndex: 'SHARETYPE', width: 200},
            {text: '对象名称', dataIndex: 'OBJECTNAME', width: 200}];

        //已共享grid
        shareedGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            autoScroll: true,
            store: shareedColumnStore,
            border: false,
//	    bbar:shareedColumnStorePageBar,
            columnLines: true,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            cls: 'customize_panel_back',
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
            columns: shareedColumns,
            listeners: {
                select: function (e, record, index, eOpts) {
                    if (chosedShareIds.indexOf(record.get('IID')) == -1) {
                        chosedShareIds.push(record.get('IID'));
                    }
                },
                deselect: function (e, record, index, eOpts) {
                    if (chosedShareIds.indexOf(record.get('IID')) > -1) {
                        chosedShareIds.remove(record.get('IID'));
                    }
                }
            }
        });


        var shareUserRadio = Ext.create('Ext.form.field.Radio', {
            width: 80,
            name: 'shareRadio',
            labelAlign: 'left',
            fieldLabel: '',
            boxLabel: '用户共享',
            inputValue: 1
        });
        var shareGroupRadio = Ext.create('Ext.form.field.Radio', {
            width: 90,
            name: 'shareRadio',
            labelAlign: 'left',
            fieldLabel: '',
            boxLabel: '用户组共享',
            inputValue: 2
        });

        var shareAllRadio = Ext.create('Ext.form.field.Radio', {
            width: 100,
            name: 'shareRadio',
            labelAlign: 'left',
            fieldLabel: '',
            boxLabel: '所有人共享',
            inputValue: 0
        });
        var shareRadioComment = Ext.create('Ext.form.RadioGroup', {
            name: 'shareRadioComment',
            labelAlign: 'left',
            layout: 'column',
            width: '160',
            items: [shareUserRadio, shareGroupRadio, shareAllRadio],
            listeners: {
                //通过change触发
                change: function (g, newValue, oldValue) {
                    if (newValue.shareRadio == 0)//所有人
                    {
                        chosedShareIds = [];
                        chosedShareIds.push(-1);
                        shareType = 0;
                        shareGrid.hide();
                    } else if (newValue.shareRadio == 1)//用户
                    {
                        chosedShareIds = [];
                        shareType = 1;
                        shareColumns = [];
                        shareColumns = [{text: '序号', xtype: 'rownumberer', width: 40}];
                        shareColumns.push({text: '主键', dataIndex: 'IID', hidden: true});
                        shareColumns.push({text: '用户名称', dataIndex: 'IFULLNAME', width: 200});
                        shareColumns.push({text: '用户ID', dataIndex: 'ILOGINNAME', width: 200, flex: 1});

                        shareGrid.reconfigure(shareColumnStore, shareColumns);
                        shareColumnStore.load();
                        shareGrid.show();
                    } else if (newValue.shareRadio == 2)//组
                    {
                        chosedShareIds = [];
                        shareType = 2;
                        shareColumns = [];
                        shareColumns = [{text: '序号', xtype: 'rownumberer', width: 40}];
                        shareColumns.push({text: '主键', dataIndex: 'IID', hidden: true});
                        shareColumns.push({text: '用户组名称', dataIndex: 'IGROUPNAME', width: 200});
                        shareColumns.push({text: '用户组描述', dataIndex: 'IGROUPDES', width: 160, flex: 1});
                        shareGrid.reconfigure(shareColumnStore, shareColumns);
                        shareColumnStore.load();
                        shareGrid.show();
                    }
                }
            }
        });

        shareGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            autoScroll: true,
            store: shareColumnStore,
            border: false,
//		    bbar:shareColumnStorePageBar,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
            columnLines: true,
            cls: 'customize_panel_back',
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
            columns: shareColumns,
            listeners: {
                select: function (e, record, index, eOpts) {
                    if (chosedShareIds.indexOf(record.get('IID')) == -1) {
                        chosedShareIds.push(record.get('IID'));
                    }
                },
                deselect: function (e, record, index, eOpts) {
                    if (chosedShareIds.indexOf(record.get('IID')) > -1) {
                        chosedShareIds.remove(record.get('IID'));
                    }
                }
            }
        });
        var objectName = new Ext.form.TextField({
            name: 'objectName',
            //fieldLabel : '对象名称',
            emptyText: '--对象名称--',
            //labelWidth : 65,
            //		padding : '5',
            width: '20%',
            //labelAlign : 'right',
            //  value: filter_serviceName,
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        if (shareRadioComment.getChecked().length == 0) {
                            Ext.MessageBox.alert("提示", "请选择共享类型");
                            return;
                        } else {
                            var shareRadio = shareRadioComment.getChecked()[0].inputValue;
                            if (shareRadio == 0) {
                                Ext.MessageBox.alert("提示", "只能查询共享类型为用户、用户组的对象");
                                return;
                            }
                        }
                        shareGrid.ipage.moveFirst();
                    }
                }
            }
        });


        var share_form = Ext.create('Ext.ux.ideal.form.Panel', {
            //layout : 'anchor',
            region: 'north',
            cls: 'customize_panel_back',
            //buttonAlign : 'center',
            //bodyCls : 'x-docked-noborder-top',
            border: false,
            dockedItems: {
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [shareRadioComment, '->', objectName, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        if (shareRadioComment.getChecked().length == 0) {

                            Ext.MessageBox.alert("提示", "请选择共享类型");
                            return;
                        } else {
                            var shareRadio = shareRadioComment.getChecked()[0].inputValue;
                            if (shareRadio == 0) {
                                Ext.MessageBox.alert("提示", "只能查询共享类型为用户、用户组的对象");
                                return;
                            }
                        }
                        shareGrid.ipage.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '确定',
                    handler: function () {
                        if (shareRadioComment.getChecked().length == 0) {
                            Ext.MessageBox.alert("提示", "请选择共享类型");
                            return;
                        }
                        var shareRadio = shareRadioComment.getChecked()[0].inputValue;
                        if (shareRadio == 0 || shareRadio == 1 || shareRadio == 2) {
                            if (shareRadio == 0) {//所有人
                                shareType = 0;
                                release(4);
                            } else if (shareRadio == 1) {//用户
                                shareType = 1;
                                if (chosedShareIds.length == 0) {
                                    Ext.MessageBox.alert("提示", "请选择要共享的用户");
                                    return;
                                } else {
                                    release(4);
                                }
                            } else { //组 2
                                shareType = 2;
                                if (chosedShareIds.length == 0) {
                                    Ext.MessageBox.alert("提示", "请选择要共享的用户组");
                                    return;
                                } else {
                                    release(4);
                                }
                            }
                        } else {
                            Ext.MessageBox.alert("提示", "请选择共享模式！");
                            return;
                        }
                    }
                }]
            }
        });

        var shareed_form = Ext.create('Ext.ux.ideal.form.Panel', {
            //layout : 'anchor',
            region: 'north',
            cls: 'customize_panel_back',
            //buttonAlign : 'center',
            //bodyCls : 'x-docked-noborder-top',
            border: false,
            dockedItems: {
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: ['->', {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '取消共享',
                    handler: function () {
                        if (shareedGrid.getSelectionModel().getSelection().length == 0) {
                            Ext.MessageBox.alert("提示", "请选择要取消共享的条目");
                            return;
                        }
                        Ext.MessageBox.buttonText.yes = "确定";
                        Ext.MessageBox.buttonText.no = "取消";
                        Ext.Msg.confirm("确认取消共享", "是否取消共享", function (id) {
                            if (id == 'yes') {
                                cancelShare(chosedShareIds);
                            }
                        });
                    }
                }]
            }
        });

        function cancelShare(chosedShareIdss) {
            //获取服务主键
            var record = scriptServiceReleaseGrid.getSelectionModel().getSelection();
            var iidStr = [];
            Ext.Array.each(record, function (recordObj) {
                iidStr.push(recordObj.get('iid'));
            });
            Ext.Ajax.request({
                url: 'scriptService/cancelShare.do',
                method: 'POST',
                params: {
                    chosedShareIds: chosedShareIdss,
                    iidStr: iidStr
                },
                success: function (response, opts) {
                    var message = '取消共享成功！';
                    Ext.MessageBox.show({
                        title: "提示",
                        msg: message,
                        buttonText: {
                            yes: '确定'
                        },
                        buttons: Ext.Msg.YES
                    });
                    shareedColumnStore.reload();
                    scriptServiceReleaseStore.reload();
                    chosedShareIds = [];
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }

        var pagetab = Ext.create('Ext.tab.Panel',
            {
                tabPosition: 'top',
                cls: 'customize_panel_back',
//			    cls:'window_border panel_space_top panel_space_left panel_space_right',
                region: 'center',
                activeTab: 0,
                //width : '100%',
                width: 700,
                minWidth: 350,
                height: 450,
                border: false,
//			    autoScroll: true,
                items: [
                    {
                        title: '待共享',
                        layout: 'border',
                        items: [share_form, shareGrid]
                    },
                    {
                        title: '已共享',
                        layout: 'border',
                        items: [shareed_form, shareedGrid]
                    }
                ]
            });


        shareWin = Ext.create('widget.window', {
            title: '共享列表',
            closable: true,
            closeAction: 'destroy',
            modal: true,
            width: 750,
            minWidth: 350,
            height: 550,
            layout: {
                type: 'border',
                padding: 5
            },
            items: [pagetab]

        });
        shareWin.show();
    }

    function release(optionState) {
        var url = 'scriptService/serviceRelease.do';
        var message = "脚本发布成功";
        var errorMessage = "脚本发布失败";
        if (optionState == 1) {
            message = "脚本删除成功";
            errorMessage = "脚本删除失败";
            url = "scriptService/deleteScriptForTest.do";
        } else if (optionState == 4) {
            message = "脚本共享成功";
            errorMessage = "脚本共享失败";
        }

        var jsonData = getSelectedJsonData();
        if (jsonData == "[]") {
            Ext.MessageBox.alert("提示", signMessage);
            return;
        }
        Ext.Ajax.request({
            url: url,
            method: 'POST',
            params: {
                jsonData: jsonData,
                optionState: optionState,
                shareType: shareType,
                chosedShareIds: chosedShareIds,
                isflow: 0,
                isCustomTask: 0
            },
            success: function (response, opts) {
                if (optionState == 1) {
                    deleteAtomact();
                    var message1 = Ext.decode(response.responseText).message
                    var isPublish = Ext.decode(response.responseText).isPublish
                    if(!isPublish){
                         Ext.MessageBox.show({
                        title: "提示",
                        msg: message1,
                        buttonText: {
                            yes: '确定'
                        },
                        buttons: Ext.Msg.YES
                    });
                    }else {

                        var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
                            grow: true,
                            readOnly: true,
                            value: message1
                        });

                        var textErrorMsg = Ext.create('Ext.window.Window', {
                            title: '提示信息',
                            width: 900,
                            height: 300,
                            layout: 'fit',

                            items: [pubDesc_sm]
                        });

                        textErrorMsg.show();

                    }

                } else {
                    var success = Ext.decode(response.responseText).success;
                    if (success) {
                        Ext.MessageBox.show({
                            title: "提示",
                            msg: message,
                            buttonText: {
                                yes: '确定'
                            },
                            buttons: Ext.Msg.YES
                        });
                    } else {
                        var message2 = Ext.decode(response.responseText).message;
                        if (null !=message2){
                            Ext.Msg.alert('提示', message2);
                        }else {
                            Ext.MessageBox.show({
                                title: "提示",
                                msg: errorMessage,
                                buttonText: {
                                    yes: '确定'
                                },
                                buttons: Ext.Msg.YES
                            });
                        }
                    }
                }
                if (optionState == 4) {
                    shareWin.close();
                }
                scriptServiceReleaseStore.reload();
                scriptNameStore.reload();
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    // 将被选中的记录的flowid组织成json串，作为参数给后台处理
    function getSelectedJsonData() {
        var flowIdList = scriptServiceReleaseGrid.getSelectionModel().getSelection();
        if (flowIdList.length < 1) {
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = flowIdList.length; i < len; i++) {
            if (i == 0) {
                jsonData = jsonData + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
            } else {
                jsonData = jsonData + "," + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
            }
        }
        jsonData = jsonData + "]";
        return jsonData;
    }

    function clearQueryWhere() {
        smbussCb.setValue('');
        smbussTypeCb.setValue('');
        threesmbussTypeCb.setValue('');
        smscriptName.setValue('');
        smName.setValue('');
        smscriptTypeParam.setValue('');
        smscriptStatusCb.setValue('');
        appSysObj.setValue('');
        emScriptCb.setValue('');
        smplatFromCombobox.setValue('');
        usetj.setValue('-1');
        succtj.setValue('-1');
        usenumtjText.setValue('');
        succtjText.setValue('');
        usenumtjText.hide();
        succtjText.hide();
        serviceid.setValue('');
        smkeywords.setValue('');
        smlabel.setValue('');
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
        smgroupNameQuery.setValue('');
        importScriptsStatusSearch.setValue('')
    }

    //从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        //var eValue=eval('jsonObj.'+key);
        return jsonObj['' + key + ''];
    }

    function sendScriptMethond(iidArray, agentIds, sendPath, parameterPermissionValue, groupPermissionValue, userPermissionValue, me, forceSaveFlag, isjcpa) {
        Ext.Ajax.request({
            url: 'sendScriptToAgents.do',
            method: 'POST',
            params: {
                serviceId: iidArray,
                agentIds: agentIds,
                sendPath: sendPath,
                parameterPermission: parameterPermissionValue,//权限
                groupPermission: groupPermissionValue,//所属用户组
                userPermission: userPermissionValue,// 所属用户
                isjc: isjcpa,//时间戳 强制下发使用
                forceSaveFlag: forceSaveFlag//邮储 md5不一致 是否强制覆盖下发 0不强制 1强制覆盖
            },
            success: function (response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                isjc = Ext.decode(response.responseText).isjc;
                me.up("window").close();
                Ext.Msg.alert('提示', message, function () {
                    if (success) {
                        queryIssueRecord(1);
                    }
                });
            },
            failure: function (result, request) {
                Ext.Msg.alert('提示', '下发失败！');
            }
        });
    }

    function sendScript(iid, serviceName, bussId, bussTypeId) {
        Ext.define('resourceGroupModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'id',
                type: 'int',
                useNull: true
            }, {
                name: 'name',
                type: 'string'
            }, {
                name: 'description',
                type: 'string'
            }]
        });

        var resourceGroupStore = Ext.create('Ext.data.Store', {
            autoLoad: !removeAgentSwitch,
            autoDestroy: true,
            model: 'resourceGroupModel',
            proxy: {
                type: 'ajax',
                url: 'getResGroupForScriptService.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });
        resourceGroupStore.on('load', function () {
            var ins_rec = Ext.create('resourceGroupModel', {
                id: '-1',
                name: '未分组',
                description: ''
            });
            resourceGroupStore.insert(0, ins_rec);
        });
        var resourceGroupObj = Ext.create('Ext.form.field.ComboBox', {
            fieldLabel: '资源组',
            labelAlign: 'right',
            labelWidth: 65,
            width: '21%',
            multiSelect: true,
            hidden: removeAgentSwitch,
            store: resourceGroupStore,
            displayField: 'name',
            valueField: 'id',
            triggerAction: 'all',
            editable: false,
            mode: 'local',
            listeners: {
                change: function (comb, newValue, oldValue, eOpts) {
                    /*chosedResGroups_forest = new Array();
			    	    	  for(var i=0;i<newValue.length;i++) {
			    	    		  chosedResGroups_forest.push(newValue[i]);
			    	    	  }*/
                    agent_store.load();
                }
            }
        });
        var userPermission = new Ext.form.TextField({
            name: 'userPermission',
            fieldLabel: '所属用户',
            emptyText: '--请输入所属用户--',
            labelWidth: 90,
            labelAlign: 'right',
            width: '21%'
        });
        var groupPermission = new Ext.form.TextField({
            name: 'groupPermission',
            fieldLabel: '所属组',
            emptyText: '--请输入所属用户组--',
            labelWidth: 70,
            labelAlign: 'right',
            width: '21%'
        });

        //
        //var parameterPermission =  new Ext.form.NumberField({
        var parameterPermission = new Ext.form.TextField({
            name: 'parameterPermission',
            fieldLabel: '权限参数',
            emptyText: '--请输入权限参数--',
            labelWidth: 65,
            labelAlign: 'right',
            width: '21.1%',
            regex: /^[0-7]{1,4}$/, // 四位数字 0-7
            regexText: "权限参数不符合要求" //定义不符合正则表达式的提示信息
        });

        var smsearch_form = Ext.create('Ext.ux.ideal.form.Panel', {
            layout: 'anchor',
            buttonAlign: 'center',
            bodyCls: 'x-docked-noborder-top',
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [resourceGroupObj, {
                    fieldLabel: 'IP',
                    labelAlign: 'right',
                    labelWidth: 65,
                    name: 'agentIp',
                    width: '21%',
                    xtype: 'textfield',
                    listeners: {
                        specialkey: function (field, e) {
                            if (e.getKey() == e.ENTER) {
                                pageBar1.moveFirst();
                            }
                        }
                    }
                }, {
                    fieldLabel: 'Agent描述',
                    labelAlign: 'right',
                    labelWidth: 90,
                    name: 'agentDesc',
                    width: '42.2%',
                    xtype: 'textfield',
                    listeners: {
                        specialkey: function (field, e) {
                            if (e.getKey() == e.ENTER) {
                                pageBar1.moveFirst();
                            }
                        }
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        pageBar1.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '清空',
                    handler: function () {
                        clearQueryWhere();
                    }
                }]
            },
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [{
                        fieldLabel: '下发路径',
                        labelAlign: 'right',
                        labelWidth: 65,
                        name: 'sendPath',
                        width: '21.1%',
                        xtype: 'textfield'
                    }, parameterPermission, userPermission, groupPermission, {
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "确定",
                        handler: function () {
                            if (!smsearch_form.isValid()) {
                                Ext.Msg.alert('提示', '权限参数校验未通过！');
                                return;
                            }
                            var me = this;
                            var records = agent_grid.getSelectionModel().getSelection();
                            if (records.length < 1) {
                                Ext.Msg.alert('提示', '请选择下发服务器！');
                                return;
                            }
                            var agentIds = new Array();
                            $.each(records, function (i, v) {
                                agentIds.push(v.data.iid);
                            });

                            var sendPath = Ext.util.Format.trim(smsearch_form.getForm().findField("sendPath").getValue());

                            if (!Ext.isEmpty(sendPath)) {
//						Ext.Msg.alert('提示', '请填写下发路径！');
//						return;
                                if (sendPath.indexOf("\\") > -1) {
                                    Ext.Msg.alert('提示', '下发路径的分隔符请使用 "/"！');
                                    return;
                                }

                                var lastStr = sendPath.charAt(sendPath.length - 1);
                                if (lastStr != '/') {
                                    Ext.Msg.alert('提示', '下发路径请以 "/"结尾！');
                                    return;
                                }
                            }
//					else{
//						Ext.Msg.alert('提示', '请填写下发路径！');
//	 					return;
//					}

                            //
                            var parameterPermissionValue = parameterPermission.getValue();
                            var groupPermissionValue = groupPermission.getValue();
                            var userPermissionValue = userPermission.getValue();

                            if ((groupPermissionValue != '' && groupPermissionValue != null) && (userPermissionValue == '' || userPermissionValue == null)) {
                                Ext.Msg.alert('提示', '所属用户和所属组必须同时填写！');
                                return
                            }
                            if ((groupPermissionValue == '' || groupPermissionValue == null) && (userPermissionValue != '' && userPermissionValue != null)) {
                                Ext.Msg.alert('提示', '所属用户和所属组必须同时填写！');
                                return
                            }

                            if (parameterPermissionValue == '' || parameterPermissionValue == null) {
                                parameterPermissionValue = 755;
                            }

                            //search_form.v

                            sendScriptMethond(iidArray, agentIds, sendPath, parameterPermissionValue, groupPermissionValue, userPermissionValue, me, '0', "");
                        }
                    }, {
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "取消",
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
                }]
        });

        Ext.define('agentModel', {
            extend: 'Ext.data.Model',
            idProperty: 'iid',
            fields: [
                {name: 'iid', type: 'string'},
                {name: 'hostName', type: 'string'},
                {name: 'sysName', type: 'string'},
                {name: 'agentIp', type: 'string'},
                {name: 'agentPort', type: 'string'},
                {name: 'agentDesc', type: 'string'},
                {name: 'agentState', type: 'int'}
            ]
        });

        var agent_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 50,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAllAgentList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'iid', hidden: true},
            {text: '名称', dataIndex: 'sysName', flex: 1},
            {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
            {text: '计算机名', dataIndex: 'hostName', flex: 1},
            {text: 'IP', dataIndex: 'agentIp', width: 120},
            {text: '端口号', dataIndex: 'agentPort', width: 100},
            {text: '描述', dataIndex: 'agentDesc', flex: 1},
            {
                text: '状态', dataIndex: 'agentState', width: 130, renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == 0) {
                        backValue = "Agent正常";
                    } else if (value == 1) {
                        backValue = "Agent异常";
                    }
                    return backValue;
                }
            }
        ];

        agent_store.on('beforeload', function (store, options) {
            var new_params = {
                agentIp: smsearch_form.getForm().findField("agentIp").getValue(),
                agentDesc: smsearch_form.getForm().findField("agentDesc").getValue(),
                rgIds: resourceGroupObj.getValue(),
                flag: 2
            };

            Ext.apply(agent_store.proxy.extraParams, new_params);
        });

        var pageBar1 = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
            store: agent_store,
            dock: 'bottom',
            baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            displayInfo: true,
            border: false,
            displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
            emptyMsg: "没有记录"
        });

//	var pageBar1 = Ext.create('Ext.PagingToolbar', {
//		store: agent_store,
//		dock : 'bottom',
//		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//		border:false,
//		displayInfo : true
//	});
        var agent_grid = Ext.create('Ext.grid.Panel', {
            height: contentPanel.getHeight() - 175,
            store: agent_store,
            border: true,
            columnLines: true,
            columns: agent_columns,
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
            bbar: pageBar1
        });

        function clearQueryWhere() {
            smsearch_form.getForm().findField("agentIp").setValue('');
            smsearch_form.getForm().findField("agentDesc").setValue('');
        }

        var seledCnt = selModel.getCount();
        if (seledCnt < 1) {
            Ext.MessageBox.alert("提示", "请选择要下发的脚本！");
            return;
        }
        var ss = selModel.getSelection();
        var iidArray = new Array();
        for (var i = 0, len = ss.length; i < len; i++) {
            iidArray.push(ss[i].data.iid);
        }

        var testChooseSendAgentWin = Ext.create('Ext.window.Window', {
            title: '选择服务器',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            width: contentPanel.getWidth() - 120,
            height: contentPanel.getHeight(),
            items: [smsearch_form, agent_grid]
        }).show();
//	resourceGroupObj.setValue('');
//	search_form.getForm().findField("agentIp").setValue('');
//	search_form.getForm().findField("agentDesc").setValue('');
        agent_store.load();

    }

    /**
     * 查询下发记录数据的方法
     * 下发后自动调用，点击查询下发记录也会调用
     */
    function queryIssueRecord(flag) {
        var isjcForQuery = flag == 2 ? '' : isjc;
        var currentDate = flag == 2 ? Ext.Date.format(new Date(), 'Y-m-d') : '';
        Ext.define('scriptIssueRecordModel', {
            extend: 'Ext.data.Model',
            fields: [
                {name: 'iid', type: 'int'},
                {name: 'uperId', type: 'int'},
                {name: 'iagentInfo_id', type: 'int'},
                {name: 'isendPath', type: 'string'},
                {name: 'iagent_ip', type: 'string'},
                {name: 'iagent_port', type: 'string'},
                {name: 'iservicesName', type: 'string'},
                {name: 'iscriptiid', type: 'string'},
                {name: 'iscriptName', type: 'string'},
                {name: 'imessage', type: 'string'},
                {name: 'istatus', type: 'string'},
                {name: 'times', type: 'string'},
                {name: 'permission', type: 'string'},
                {name: 'userpermission', type: 'string'},
                {name: 'grouppermission', type: 'string'},
                {name: 'isjc', type: 'string'}
            ]
        });
        //构建store
        var scriptIssueRecordStore = Ext.create('Ext.data.Store',
            {
                autoLoad: false,
                autoDestroy: true,
                pageSize: 50,
                model: 'scriptIssueRecordModel',
                proxy:
                    {
                        type: 'ajax',
                        url: 'queryScriptIssueRecordList.do',
                        reader:
                            {
                                type: 'json',
                                root: 'dataList',
                                totalProperty: 'total'
                            }
                    }
            });
        scriptIssueRecordStore.on('beforeload', function (store, options) {
            var new_params = {
                sjc: isjcForQuery,
                istatus: stateCombo.getValue(),
                idate: idateComment.getRawValue()
            };
            Ext.apply(scriptIssueRecordStore.proxy.extraParams, new_params);
        });

        var issueScriptRecBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
            store: scriptIssueRecordStore,
            dock: 'bottom',
            baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            border: false,
            displayInfo: true
        });
        var scriptIssueRecColumns = [{
            text: '序号',
            width: 50,
            xtype: 'rownumberer'
        }, {
            text: 'iid',
            dataIndex: 'iid',
            flex: 1,
            hidden: true
        }, {
            text: 'iagentInfo_id',
            dataIndex: 'iagentInfo_id',
            flex: 1,
            hidden: true
        }, {
            text: '下发路径',
            width: 110,
            flex: 1,
            dataIndex: 'isendPath',
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        }, {
            text: 'AgentIP',
            dataIndex: 'iagent_ip',
            width: 90
        }, {
            text: 'Agent端口',
            width: 80,
            dataIndex: 'iagent_port'
        }, {
            text: 'iscriptiid',
            flex: 1,
            dataIndex: 'iscriptiid',
            hidden: true
        }, {
            text: 'isjc',
            flex: 1,
            dataIndex: 'isjc',
            hidden: true
        }, {
            text: '服务名称',
            width: 110,
            dataIndex: 'iservicesName'
        }, {
            text: '脚本名称',
            width: 120,
            dataIndex: 'iscriptName'
        }, {
            text: '下发时间',
            width: 150,
            dataIndex: 'times'
        }, {
            text: '权限参数',
            width: 70,
            dataIndex: 'permission'
        }, {
            text: '所属用户',
            width: 70,
            dataIndex: 'userpermission'
        }, {
            text: '所属用户组',
            width: 100,
            dataIndex: 'grouppermission'
        }, {
            text: '下发状态',
            width: 100,
            dataIndex: 'istatus',
            renderer: function (value, p, record, rowIndex) {
                if (value == '0') {
                    return '<span class="Abnormal_Operation_orange State_Color">待下发</span>';
                } else if (value == '1') {
                    return '<span class="Run_Green State_Color">下发成功</span>';
                } else if (value == '2') {
                    return '<span class="Abnormal_yellow State_Color">正在下发</span>';
                } else if (value == '3') {
                    return '<span class="Kill_red State_Color">下发失败</span>';
                } else if (value == '4') {
                    return '<span class="Kill_red State_Color">MD5不一致</span>';
                }
            }
        }, {
            text: '下发消息',
            width: 300,
            flex: 2,
            dataIndex: 'imessage',
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        }, {
            text: '操作',
            xtype: 'actiontextcolumn',
            flex: 1,
            hidden: !scriptIssuedCheckMd5Switch,
            // align : 'center',
            items: [{
                text: '强制下发',
                getClass: function (v, metadata, record) {
                    if (record.data.istatus != 4) {
                        return 'x-hidden';
                    }
                },
                handler: function (grid, rowIndex) {
                    /*let iid = grid.getStore().data.items[rowIndex].data.iid;
					let scriptuuida = grid.getStore().data.items[rowIndex].data.scriptuuid;
					forwardTaskMonitor(iid,scriptuuida,'scriptTaskApplySs');*/
                    let iidArray = [grid.getStore().data.items[rowIndex].data.iscriptiid];
                    let agentIds = [grid.getStore().data.items[rowIndex].data.iagentInfo_id];
                    let sendPath = grid.getStore().data.items[rowIndex].data.isendPath;
                    let parameterPermissionValue = grid.getStore().data.items[rowIndex].data.permission;
                    let groupPermissionValue = grid.getStore().data.items[rowIndex].data.grouppermission;
                    let userPermissionValue = grid.getStore().data.items[rowIndex].data.userpermission;
                    let isjcc = grid.getStore().data.items[rowIndex].data.isjc;
                    sendScriptMethond(iidArray, agentIds, sendPath, parameterPermissionValue, groupPermissionValue, userPermissionValue, search_formForScriptIssueRec, '1', isjcc);
                }
            }]
        }];
        var scriptIssueRecGrid = Ext.create('Ext.grid.Panel', {
            store: scriptIssueRecordStore,
            region: 'center',
            height: contentPanel.getHeight() - 285,
            bbar: issueScriptRecBar,
            border: false,
            columnLines: true,
            cls: 'customize_panel_back',
            columns: scriptIssueRecColumns
        });

        var idateComment = new Ext.form.field.Date({
            name: 'idate',
            fieldLabel: '下发日期',
            labelWidth: 65,
            width: '25%',
            labelAlign: 'right',
            format: 'Y-m-d',
            value: currentDate,
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        issueScriptRecBar.moveFirst();
                    }
                }
            }
        });
        if (scriptIssuedCheckMd5Switch) {
            var stateStore = Ext.create('Ext.data.Store', {
                fields: ['id', 'name'],
                data: [{
                    "id": "-100",
                    "name": "全部"
                }, {
                    "id": "0",
                    "name": "待下发"
                }, {
                    "id": "1",
                    "name": "下发成功"
                }, {
                    "id": "2",
                    "name": "正在下发"
                }, {
                    "id": "3",
                    "name": "下发异常"
                }, {
                    "id": "4",
                    "name": "MD5不一致"
                }]
            });
        } else {
            var stateStore = Ext.create('Ext.data.Store', {
                fields: ['id', 'name'],
                data: [{
                    "id": "-100",
                    "name": "全部"
                }, {
                    "id": "0",
                    "name": "待下发"
                }, {
                    "id": "1",
                    "name": "下发成功"
                }, {
                    "id": "2",
                    "name": "正在下发"
                }, {
                    "id": "3",
                    "name": "下发异常"
                }]
            });
        }
        var stateCombo = Ext.create('Ext.form.field.ComboBox', {
            name: 'stateCombo',
            // padding : '5',
            labelWidth: 65,
            queryMode: 'local',
            fieldLabel: '下发状态',
            displayField: 'name',
            valueField: 'id',
            editable: false,
            emptyText: '--请选择状态--',
            store: stateStore,
            width: '25%',
            labelAlign: 'right'
        });

        var search_formForScriptIssueRec = Ext.create('Ext.ux.ideal.form.Panel', {
            layout: 'anchor',
            buttonAlign: 'center',
            iqueryFun: function () {
                issueScriptRecBar.moveFirst();
            },
            bodyCls: 'x-docked-noborder-top',
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: [idateComment, stateCombo, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    margin: '0 5 0 0',
                    text: '查询',
                    handler: function () {
                        issueScriptRecBar.moveFirst();
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    margin: '0 5 0 0',
                    text: '清空',
                    handler: function () {
                        idateComment.setValue(currentDate);
                        stateCombo.setValue('');
                        isjcForQuery = '';
                    }
                }]
            }]
        });

        scriptIssueRecordStore.load();
        var scriptIssueRecWin = Ext.create('Ext.window.Window', {
            title: '下发记录',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'hide',
            width: contentPanel.getWidth() - 30,
            height: contentPanel.getHeight() - 180,
            items: [search_formForScriptIssueRec, scriptIssueRecGrid]
        });
        scriptIssueRecWin.show();
    }

    function submitTry() {
        var displayField = tryGroup.getRawValue();
        if (!Ext.isEmpty(displayField)) {
            var flag = false;
            tryStore_sm.each(function (record) {
                var data_id = record.get('name');
                if (data_id == displayField) {
                    flag = true;
                }
            });
            if (!flag) {
                Ext.Msg.alert('提示', "输入的审核组非法");
                tryGroup.setValue("");
                return;
            }
        }
        var ss = selModel.getSelection();
        var uuid = ss[0].data.uuid;
        Ext.Ajax.request({
            url: 'scriptPublishTry.do',
            method: 'POST',
            params: {
                uuid: uuid,
                group: tryGroup.getValue()
            },
            success: function (response, opts) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (!success) {
                    Ext.MessageBox.alert("提示", message);
                } else {
                    Ext.MessageBox.alert("提示", "请求已经发送到审核组");
                    selectedRecords.clear();
                    scriptServiceReleaseStore.load();
                    openTryWin.close();
                }


            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
                openTryWin.close();
            }
        });
    }

    function submitSyncAutiding() {

        //判断输入的审核人是否合法 start
        var displayField = auditorComBox_sm1.getRawValue();
        if (!Ext.isEmpty(displayField)) {
            //判断输入是否合法标志，默认false，代表不合法
            var flag = false;
            //遍历下拉框绑定的store，获取displayField
            auditorStore_sm1.each(function (record) {
                //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                var data_fullName = record.get('fullName');
                if (data_fullName == displayField) {
                    flag = true;
                    auditorComBox_sm1.setValue(record.get('loginName'));
                }
            });
            if (!flag) {
                Ext.Msg.alert('提示', "输入的审核人非法");
                auditorComBox_sm1.setValue("");
                return;
            }
        }
        var publishDesc = pubDesc_sm1.getValue();
        var auditor = auditorComBox_sm1.getValue();
        if (!publishDesc) {
            Ext.Msg.alert('提示', "没有填写发布申请说明！");
            return;
        }
        if (publishDesc.length > 255) {
            Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
            return;
        }
        if (!auditor) {
            Ext.Msg.alert('提示', "没有选择审核人！");
            return;
        }
        Ext.Ajax.request({
            url: 'scriptPublishSyncAuditing.do',
            method: 'POST',
            params: {
                sIds: scriptPublishsIds,
                publishDesc: publishDesc,
                auditor: auditor,
                flag: 0//0-来着个人脚本库
            },
            success: function (response, opts) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (!success) {
                    Ext.MessageBox.alert("提示", message);
                } else {
                    Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                    scriptPublishsIds = [];
                    selectedRecords.clear();
                    scriptiids = [];
                    scriptServiceReleaseStore.load();
                    publishAuditingWin.close();
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
                publishAuditingSMWin.close();
            }
        });

    }

    //数组去重
    function uniq(array) {
        var temp = [];
        for (var i = 0; i < array.length; i++) {
            //如果当前数组的第i项在当前数组中第一次出现的位置是i，才存入数组；否则代表是重复的
            if (array.indexOf(array[i]) == i) {
                temp.push(array[i])
            }
        }
        return temp;
    }

    /**
     * 绑定用户组提交
     */

    /*function submitAutidingAgentGroup(){
		//发布申请说明
		var auditDescs = scriptLevelCb_sm.getValue();
		//风险级别
		var fxLevel = scriptLevelCb_sm.getValue();
		//审核人
		var auditUser = auditorComBox_sm.getValue();
		//获取选中的设备
		var IIdArray = [];
		var records = agentgrid.getSelectionModel().getSelection();
		alert(records);
		Ext.each(records, function(item) {// 遍历
			IIdArray.push(item.data.iid);
		});
		var iidsStr = IIdArray.join(",");
		//校验，以上条件为空不得提交
		if(auditDescs == null || auditDescs == ''){
			Ext.Msg.alert('提示', '发布申请说明不能为空！');
			return;
		}
		if(fxLevel == null || fxLevel == ''){
			Ext.Msg.alert('提示', '风险级别不能为空！');
			return;
		}
		if(auditUser == null || auditUser == ''){
			Ext.Msg.alert('提示', '审核人不能为空！');
			return;
		}
		if(IIdArray == null || IIdArray == ''){
			Ext.Msg.alert('提示', '请选择设备！');
			return;
		}
		//校验通过，访问后台
		Ext.Ajax.request({
			url : 'auditSuccessForScript.do',
			params : {
				auditDescs : auditDescs,
				fxLevel : fxLevel,
				auditUser : auditUser,
				workitemId : "",
				iidsStr : iidsStr
			},
			method : 'POST',
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				//隐藏选择审核人界面
				publishAuditingWin.hide();
				if(success){
					//重新加载页面数据
					scriptServiceReleaseStore.load({
						params: {

						}
					});
				}
				destroyRubbish();
				Ext.Msg.alert('提示', message);
			},
			failure : function(result, request) {
				var message = Ext.decode(response.responseText).message;
				//隐藏选择审核人界面
				publishAuditingWin.hide();
				Ext.Msg.alert('提示', message);
			}
		});
	}*/

    /**
     * 发布审核确定
     */
    function submitAutiding() {
        if (scriptCrossPublishPassSwitch) {
            var planTime = "";
            var scriptLevel = "0";
            var publishDesc = "";
            var auditor = "";
            var isEmScript = isEMscript.getValue();

            if (isEmScript) {
                isEmScript = 1;
            } else {
                isEmScript = 0;
            }
            var isForbidden = 0;

            //定义发布审核url
            var auditUrl = 'scriptPublishAuditing.do';
            //获取agent组数据
            var agentIdArray = [];
            var agentRecords = agentGroupGrid.getSelectionModel().getSelection();
            Ext.each(agentRecords, function (item) {
                agentIdArray.push(item.data.iid);//agentIId
            });
            //获取uuid
            var uuidselect = scriptServiceReleaseGrid.getSelectionModel().getSelection();
            var uuidVal = "";
            Ext.each(uuidselect, function (item) {// 遍历
                uuidVal = item.data.uuid;
            });
            var iidUuidMap = new Map()
            for (const selectedRecord of selectedRecords) {
                iidUuidMap.set(selectedRecord.iid, selectedRecord.uuid)
            }
            var iidstr = agentIdArray.join(",");
            //定义发布参数
            var sidsLast = uniq(scriptPublishsIds);
            var auditParams = {
                sIds: sidsLast,
                planTime: planTime,
                scriptLevel: scriptLevel,
                publishDesc: publishDesc,
                auditor: auditor,
                flag: 0, //0-来着个人脚本库
                isEmScript: isEmScript,
                appSysIds: chosedAppSys,
                isForbidden: isForbidden,
                tablename: "",
                switchFlag: 0,
                radio: radio,
                serviceAuto: 1,
                attachmentIds: attachmentIds,
                systemIds: selectedSysRecords,
                cpIds: JSON.stringify(selectedAgentRecords),
                agentIIds: iidstr,
                uuidVal: uuidVal
            };
            Ext.Ajax.request({
                url: auditUrl,
                method: 'POST',
                params: auditParams,
                success: function (response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    var workItemMap = Ext.decode(response.responseText).workItemMap;

                    if (!success && null == workItemMap) {
                        Ext.MessageBox.alert("提示", message);
                    }  else {
                        var workItemIds = new Array()
                        var handlerDataMap = {}
                        for (const sid of sidsLast) {
                            let workItemId = workItemMap[sid]
                            workItemIds.push(workItemId)
                            let uuid = iidUuidMap.get(sid)
                            var aaaa = {}
                            aaaa[""+sid] = uuid
                            handlerDataMap[""+workItemId] = aaaa
                        }
                        // 发布成功后，调用发布成功url
                        Ext.Ajax.request(
                            {
                                url: 'scriptPublishForOneRecord.do',
                                method: 'POST',
                                params:
                                    {
                                        iworkItemid: 0,
                                        iid: "0",
                                        iworkItemids: workItemIds,
                                        scriptLevel: scriptLevel,
                                        isEmScript: isEmScript,
                                        appSysIds: 0,
                                        forbidden: 0,
                                        selectReleaseJson: Ext.encode(handlerDataMap),
                                        scriptUuid: uuidVal
                                    },
                                success: function (response, opts) {
                                    if (Ext.decode(response.responseText).success) {
                                        if (!success) {
                                            Ext.MessageBox.alert("提示", "部分脚本发布成功");
                                        } else {
                                            Ext.MessageBox.alert("提示", Ext.decode(response.responseText).message);
                                        }
                                        scriptPublishsIds = [];
                                        selectedRecords.clear();
                                        scriptiids = [];
                                        scriptServiceReleaseStore.load();
                                        agentStoreScript.load();
                                        //$('#uploadify-audi').uploadify('destroy');
                                        attachmentIds = [];
                                        attachStore.reload();
                                        selectedSysRecords = [];
                                        selectedAgentRecords = [];
                                    } else {
                                        if (success) {
                                            Ext.MessageBox.alert("提示", Ext.decode(response.responseText).message);
                                        } else {
                                            Ext.MessageBox.alert("提示", "发布失败，部分脚本已经提交至小信封审批，需要本人在小信封手动通过");
                                        }
                                    }
                                }
                            }
                        );
                    }
                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                    publishAuditingSMWin.close();
                    //$('#uploadify-audi').uploadify('destroy');
                    attachmentIds = [];
                    attachStore.reload();
                }
            });
        } else {
            //判断输入的审核人是否合法 start
            var displayField = auditorComBox_sm.getRawValue();
            if (!Ext.isEmpty(displayField)) {
                //判断输入是否合法标志，默认false，代表不合法
                var flag = false;
                //遍历下拉框绑定的store，获取displayField
                auditorStore_sm.each(function (record) {
                    //获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
                    var data_fullName = record.get('fullName');
                    if (data_fullName == displayField) {
                        flag = true;
                        auditorComBox_sm.setValue(record.get('loginName'));
                    }
                });
                if (!flag) {
                    Ext.Msg.alert('提示', "输入的审核人非法");
                    auditorComBox_sm.setValue("");
                    return;
                }
            }
            //判断输入的审核人是否合法  end
            var planTime = planTime_sm.getRawValue();
            var scriptLevel = scriptLevelCb_sm.getValue();
            if (!scriptLevelSwitch) {
                scriptLevel = 100;
            }
            var publishDesc = pubDesc_sm.getValue();
            var auditor = auditorComBox_sm.getValue();
            var isEmScript = isEMscript.getValue();

            if (isEmScript) {
                isEmScript = 1;
            } else {
                isEmScript = 0;
            }
            var isForbidden = forbidden.getValue();
            if (isForbidden) {
                isForbidden = 1;
            } else {
                isForbidden = 0;
            }

            if (!scriptLevel) {
                Ext.Msg.alert('提示', "没有选择风险级别！");
                return;
            }
            if (!publishDesc) {
                Ext.Msg.alert('提示', "没有填写发布申请说明！");
                return;
            }
            if (publishDesc.length > 255) {
                Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
                return;
            }
            if (!auditor) {
                Ext.Msg.alert('提示', "没有选择审核人！");
                return;
            }

            var orderNumber = orderNumber_sm.getValue().trim();
            if(orderNumberSwitch) {
                // 发布功能校验单号不为空 特定银行需求
                console.debug('单号:',orderNumber, ',isUndefined:',(undefined == orderNumber), ',islength=0:', orderNumber.length==0)
                if (orderNumber.length==0) {
                    Ext.Msg.alert('提示', "单号不能为空");
                    return;
                }
            }

            //定义发布审核url
            var auditUrl = 'scriptPublishAuditing.do';
            //获取agent组数据
            var agentIdArray = [];
            var agentRecords = agentGroupGrid.getSelectionModel().getSelection();
            Ext.each(agentRecords, function (item) {
                agentIdArray.push(item.data.iid);//agentIId
            });
            //获取uuid
            var uuidselect = scriptServiceReleaseGrid.getSelectionModel().getSelection();
            var uuidVal = "";
            Ext.each(uuidselect, function (item) {// 遍历
                uuidVal = item.data.uuid;
            });
            var iidstr = agentIdArray.join(",");
            //定义发布参数
            var sidsLast = uniq(scriptPublishsIds);
            var auditParams = {
                sIds: sidsLast,
                planTime: planTime,
                scriptLevel: scriptLevel,
                publishDesc: publishDesc,
                auditor: auditor,
                flag: 0, //0-来着个人脚本库
                isEmScript: isEmScript,
                appSysIds: chosedAppSys,
                isForbidden: isForbidden,
                tablename: "",
                switchFlag: 0,
                radio: radio,
                serviceAuto: 1,
                attachmentIds: attachmentIds,
                systemIds: selectedSysRecords,
                cpIds: JSON.stringify(selectedAgentRecords),
                agentIIds: iidstr,
                uuidVal: uuidVal,
                orderNumber: orderNumber
            };
            //如果绑定脚本开关开启，url为绑定用户方法
            if (psbcBindAgentSwicht) {
                auditUrl = 'auditSuccessForScript.do';
                //获取选中的设备
                var IIdArray = [];
                var records = agentgridScript.getSelectionModel().getSelection();
                //设备不能为空
                if (records == null || records == '') {
                    Ext.Msg.alert('提示', "请选择设备！");
                    return;
                }


                Ext.each(records, function (item) {// 遍历
                    IIdArray.push(item.data.iid);
                });
                var iidsStr = IIdArray.join(",");

                if (agentiids.length > 0) {
                    iidsStr = agentiids.toString();
                }
                auditParams = {
                    sIds: sidsLast,
                    planTime: planTime,
                    scriptLevel: scriptLevel,
                    publishDesc: publishDesc,
                    auditor: auditor,
                    flag: 0, //0-来着个人脚本库
                    isEmScript: isEmScript,
                    appSysIds: chosedAppSys,
                    isForbidden: isForbidden,
                    tablename: "",
                    switchFlag: 0,
                    radio: radio,
                    serviceAuto: 1,
                    attachmentIds: attachmentIds,
                    systemIds: selectedSysRecords,
                    cpIds: JSON.stringify(selectedAgentRecords),
                    iidsStr: iidsStr,
                    uuid: uuidVal
                };
            } else if (isProject) {
                var records = systemgrid.getSelectionModel().getSelection();
                var agentGroupRecords = agentGroupGrid.getSelectionModel().getSelection();
                if (records.length <= 0 && agentGroupRecords.length <= 0) {
                    Ext.Msg.alert('提示', "请选择业务系统或Agent！");
                    return;
                }
            }

            Ext.Ajax.request({
                url: auditUrl,
                method: 'POST',
                params: auditParams,
                success: function (response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (!success) {
                        Ext.MessageBox.alert("提示", message);
                    } else {
                        Ext.MessageBox.alert("提示", "请求已经发送到审核人");
                        scriptPublishsIds = [];
                        selectedRecords.clear();
                        scriptiids = [];
                        scriptServiceReleaseStore.load();
                        agentStoreScript.load();
                        publishAuditingSMWin.close();
                        //$('#uploadify-audi').uploadify('destroy');
                        attachmentIds = [];
                        attachStore.reload();
                        selectedSysRecords = [];
                        selectedAgentRecords = [];
                        systemStore.load();
                        agentStore.load(
                            {
                                params: {
                                    sysIdForQuery: 0,
                                    ipBetween: '',
                                    ipEnd: '',
                                    opersystype: 0,
                                    cpName: ''
                                }
                            }
                        );
                    }


                },
                failure: function (result, request) {
                    secureFilterRs(result, "操作失败！");
                    publishAuditingSMWin.close();
                    //$('#uploadify-audi').uploadify('destroy');
                    attachmentIds = [];
                    attachStore.reload();
                }
            });
            agentiids = [];
            selectedAgent.clear();
            agentStoreScript.load();
            agentgridScript.getSelectionModel().select([], false, false); //清空选中记录
            agentGroupGrid.getSelectionModel().select([], false, false); //清空选中记录
        }
    }

    function openTryWindows() {
        if (!openTryWin) {
            openTryWin = Ext.create('widget.window', {
                title: '确认测试信息',
                closable: true,
                closeAction: 'hide',
                modal: true,
                width: 600,
                minWidth: 350,
                height: 270,
                layout: {
                    type: 'border',
                    padding: 5
                },
                items: [try_form],
                dockedItems: [{
                    xtype: 'toolbar',
                    //baseCls:'customize_gray_back',
                    dock: 'bottom',
                    layout: {pack: 'center'},
                    items: [{
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "确定",
                        handler: submitTry
                    }, {
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "取消",
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
                }]
            });
        }
        openTryWin.show();
    }

    //邮储，确认审核信息，业务系统+设备与绑定agent组tab页
    var showTab = Ext.create('Ext.tab.Panel',
        {
            tabPosition: 'top',
            //cls:'window_border panel_space_top panel_space_left panel_space_right',
            region: 'center',
            activeTab: 0,
            width: '100%',
            height: 300,
            border: false,
            items: [
                {
                    title: '业务系统/设备',
                    layout: 'fit',
                    items: [project_panel]
                },
                {
                    title: 'Agent组',
                    layout: 'fit',
                    items: [agent_panel]
                }
            ]
        });

    var tabpanel = Ext.create('Ext.panel.Panel', {
        height: 280,
        region: 'south',
        cls: 'panel_space_top',
        items: [showTab]
    });

    function openAuditWin() {
        if (!publishAuditingSMWin) {
            //根据开关判断是否绑定用户组，弹出不同页面
            //绑定用户组
            if (psbcBindAgentSwicht) {
                publishAuditingSMWin = Ext.create('widget.window', {
                    title: '确认审核信息',
                    closable: true,
                    closeAction: 'hide',
                    modal: true,
                    width: 1200,
                    height: 570,
                    layout: {
                        type: 'border',
                        padding: 5
                    },
                    items: [project_panelScript],
                    dockedItems: [{
                        xtype: 'toolbar',
                        //baseCls:'customize_gray_back',
                        dock: 'bottom',
                        layout: {pack: 'center'},
                        items: [{
                            xtype: "button",
                            cls: 'Common_Btn',
                            text: "确定",
                            handler: submitAutiding
                        }, {
                            xtype: "button",
                            cls: 'Common_Btn',
                            text: "取消",
                            handler: function () {
                                selectedSysRecords = [];
                                selectedAgentRecords = [];
                                systemStore.load();
                                agentStore.load(
                                    {
                                        params: {
                                            sysIdForQuery: 0,
                                            ipBetween: '',
                                            ipEnd: '',
                                            opersystype: 0,
                                            cpName: ''
                                        }
                                    }
                                );
                                agentiids = [];
                                selectedAgent.clear();
                                agentStoreScript.load();
                                agentgridScript.getSelectionModel().select([], false, false); //选中记录
                                this.up("window").close();
                            }
                        }]
                    }]
                });
            } else {
                //不绑定用户组
                if (isProject) {
                    publishAuditingSMWin = Ext.create('widget.window', {
                        title: '确认审核信息',
                        closable: true,
                        closeAction: 'hide',
                        modal: true,
                        width: 1500,
                        height: 700,
                        layout: {
                            type: 'border',
                            padding: 5
                        },
                        items: [auditing_form_sm, tabpanel],
                        dockedItems: [{
                            xtype: 'toolbar',
                            //baseCls:'customize_gray_back',
                            dock: 'bottom',
                            layout: {pack: 'center'},
                            items: [{
                                xtype: "button",
                                cls: 'Common_Btn',
                                text: "确定",
                                handler: submitAutiding
                            }, {
                                xtype: "button",
                                cls: 'Common_Btn',
                                text: "取消",
                                handler: function () {
                                    selectedSysRecords = [];
                                    selectedAgentRecords = [];
                                    systemStore.load();
                                    agentStore.load(
                                        {
                                            params: {
                                                sysIdForQuery: 0,
                                                ipBetween: '',
                                                ipEnd: '',
                                                opersystype: 0,
                                                cpName: ''
                                            }
                                        }
                                    );
                                    this.up("window").close();
                                }
                            }]
                        }]
                    });
                } else {
                    publishAuditingSMWin = Ext.create('widget.window', {
                        title: '确认审核信息',
                        closable: true,
                        closeAction: 'hide',
                        modal: true,
                        width: 600,
                        minWidth: 350,
                        height: myhight,
                        layout: {
                            type: 'border',
                            padding: 5
                        },
                        items: [auditing_form_sm, attachGrid],
                        dockedItems: [{
                            xtype: 'toolbar',
                            //baseCls:'customize_gray_back',
                            dock: 'bottom',
                            layout: {pack: 'center'},
                            items: [{
                                xtype: "button",
                                cls: 'Common_Btn',
                                text: "确定",
                                handler: submitAutiding
                            }, {
                                xtype: "button",
                                cls: 'Common_Btn',
                                text: "取消",
                                handler: function () {
                                    this.up("window").close();
                                    //$('#uploadify-audi').uploadify('destroy');
                                    attachmentIds = [];
                                    attachStore.reload();
                                }
                            }]
                        }]
                    });
                }
            }
        }
        publishAuditingSMWin.show();
        auditorStore_sm.load();
        planTime_sm.setValue('');
        scriptLevelCb_sm.setValue('');
        pubDesc_sm.setValue('');
        auditorComBox_sm.setValue('');
        isEMscript.setValue(0);
        forbidden.setValue(0);
        appSysObj1.setValue('');
        chosedAppSys = '';
    }

    function openSyncAuditWin() {
        if (!publishAuditingWin) {
            publishAuditingWin = Ext.create('widget.window', {
                title: '确认审核信息',
                closable: true,
                closeAction: 'hide',
                modal: true,
                width: 600,
                minWidth: 350,
                height: 300,
                layout: {
                    type: 'border',
                    padding: 5
                },
                items: [auditing_form_sync],
                dockedItems: [{
                    xtype: 'toolbar',
                    //baseCls:'customize_gray_back',
                    dock: 'bottom',
                    layout: {pack: 'center'},
                    items: [{
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "确定",
                        handler: submitSyncAutiding
                    }, {
                        xtype: "button",
                        cls: 'Common_Btn',
                        text: "取消",
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
                }]
            });

        }
        publishAuditingWin.show();
        auditorStore_sm1.load();
        pubDesc_sm1.setValue('');
        auditorComBox_sm1.setValue('');
    }

    function publishSyncScript(selectedRecords) {
        var flag = false;
        scriptPublishsIds = [];
        selectedRecords.forEach((selectedRecord) => {
            if (selectedRecord.status == 1 && selectedRecord.issync == 0) { // 已上线状态
                flag = true;
            }
            scriptPublishsIds.push(selectedRecord.iid);
        });
        if (flag) {
            Ext.MessageBox.buttonText.yes = "确定";
            Ext.MessageBox.buttonText.no = "取消";
            Ext.Msg.confirm("确认发布", "是否确认发布该服务", function (id) {
                if (id == 'yes') {
                    openSyncAuditWin();
                }
            });
        } else {
            Ext.Msg.alert('提示', "只能操作已上线且未同步的服务！");
            return;
        }
    }

    function publishScript(selectedRecords) {
        agentStoreScript.load();
        // 已经发布过的服务名数组
        let msgArray1 = [];
        // 处于审核中的服务名数组
        let msgArray2 = [];
        scriptPublishsIds = [];
        selectedRecords.forEach((selectedRecord) => {
            if (selectedRecord.version) {
                msgArray1.push(selectedRecord.serviceName);
            }
            if (selectedRecord.status == 2) { // 处于审核中
                msgArray2.push(selectedRecord.serviceName);
            }
            scriptPublishsIds.push(selectedRecord.iid);
        });

        if (msgArray1.length > 0 || msgArray2.length > 0) {
            scriptPublishsIds.splice(0, scriptPublishsIds.length);
            let msg1 = '';
            let msg2 = '';
            msgArray1.length > 0 ? msg1 = '【' + msgArray1.join('，') + '】服务已经发布过！' : '';
            msgArray2.length > 0 ? msg2 = '【' + msgArray2.join('，') + '】服务正处于审核中！' : '';
            Ext.Msg.alert('提示', msg1 + '&#10' + msg2);
            return;
        }
        /*if(hasVersion==1) {
			Ext.Msg.alert('提示', "该服务已经发布过！");
			return;
		}

		if (status==2) { // 处于审核中
			Ext.Msg.alert('提示', "该服务正处于审核中！");
			return;
		}*/

        // scriptPublishsIds.push(ss[0].data.iid);
        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认发布", "是否确认发布该服务", function (id) {
            if (id == 'yes') {
                systemgrid.getSelectionModel().select([], false, false); //选中记录
                agentGroupGrid.getSelectionModel().select([], false, false); //选中记录
                sysname.setValue('');
                ipaddr.setValue('');
                agentGroup.setValue('');
                systemStore.load();
                //先查这个脚本是否被作业调用  bankCode001需求，批量发布不校验是否被作业调用过
                if (scriptPublishsIds.length == 1) {
                    Ext.Ajax.request({
                        url: 'scriptCallSearch.do',
                        method: 'POST',
                        params: {
                            iid: scriptPublishsIds[0]
                        },
                        success: function (response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            warnId = scriptPublishsIds;
                            if (success) {//有需要提示的内容
                                if (!warnningWin) {
                                    warnningWin = Ext.create('widget.window', {
                                        title: '提示信息,发布该脚本将影响以下作业，是否继续发布？',
                                        closable: true,
                                        closeAction: 'hide',
                                        modal: true,
                                        width: 600,
                                        minWidth: 350,
                                        height: 300,
                                        layout: {
                                            type: 'border',
                                            padding: 5
                                        },
                                        items: [warnningGrid],
                                        dockedItems: [{
                                            xtype: 'toolbar',
                                            dock: 'bottom',
//										baseCls:'customize_gray_back',
                                            layout: {pack: 'center'},
                                            items: [{
                                                xtype: "button",
                                                cls: 'Common_Btn',
                                                text: "是",
                                                handler: function () {
                                                    this.up("window").close();
                                                    if (scriptCrossPublishPassSwitch) {
                                                        submitAutiding()
                                                    } else {
                                                        openAuditWin();
                                                    }
                                                }
                                            }, {
                                                xtype: "button",
                                                cls: 'Common_Btn',
                                                text: "否",
                                                handler: function () {
                                                    this.up("window").close();
                                                }
                                            }]
                                        }]
                                    });
                                }
                                warnningWin.show();
                                warnningStore.load();
                            } else {
                                if (scriptCrossPublishPassSwitch) {
                                    submitAutiding();
                                } else {
                                    openAuditWin();
                                }
                            }
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                } else {
                    if (scriptCrossPublishPassSwitch) {
                        submitAutiding()
                    } else {
                        openAuditWin();
                    }
                }

            }
        });

    }

    function tryScript(selectedRecords) {
        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        Ext.Msg.confirm("确认测试", "是否确认测试该脚本", function (id) {
            if (id == 'yes') {
                openTryWindows();
            }
        });
    }

    function uploadExcel() {
        var uploadWindows;
        var uploadForm
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
                fieldLabel: '选择文件',
                labelWidth: 65,
                anchor: '90%',
//	    			labelAlign: 'right',
                margin: '10 10 0 40',
                buttonText: '浏览'
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    uploadWindows.close();
                }
            }]
        });
        uploadWindows = Ext.create('Ext.window.Window', {
            title: '脚本导入',
            layout: 'fit',
            height: 240,
            width: 600,
            modal: true,
//	    		autoScroll : true,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            }
        });
        uploadWindows.show();

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("fileName").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        function uploadTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'uploaScripts.do',
                    success: function (form, action) {
                        var sumsg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', sumsg);
                        uploadWindows.close();
                        pageBar.moveFirst();
                        /*scriptServiceReleaseStore.reload();*/
                        return;
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
//	                     var mess = Ext.create('Ext.window.MessageBox', {
//		                     minHeight : 110,
//		                     minWidth : 500,
//		                     resizable : false
//		                 });
                        Ext.Msg.alert('提示', msg);
                        return;
                    }
                });
            }
        }
    }

    function createttomAct() {
        var changeditems = selModel.getSelection();
        var updateRecordArr = new Array();
        for (var i = 0; i < changeditems.length; i++) {
            updateRecordArr.push(changeditems[i].getData());
        }
        var updateJsonStr = JSON.stringify(updateRecordArr);
        var result;
        Ext.Ajax.request({
            async: false,
            url: 'createAtomAct.do',
            method: 'POST',
            params: {
                records: updateJsonStr
            },
            success: function (response, opts) {
                var obj = Ext.decode(response.responseText);
                if (obj.success) {
                    result = true;
                } else {
                    result = false;
                }
            },
            failure: function (response, opts) {
                result = false;
            }

        });
        return result;
    }

    function deleteAtomact() {
        var changeditems = selModel.getSelection();
        var updateRecordArr = new Array();
        for (var i = 0; i < changeditems.length; i++) {
            updateRecordArr.push(changeditems[i].getData());
        }
        var updateJsonStr = JSON.stringify(updateRecordArr);
        var result;
        Ext.Ajax.request({
            async: false,
            url: 'deleteAtomAct.do',
            method: 'POST',
            params: {
                records: updateJsonStr
            },
            success: function (response, opts) {
                var obj = Ext.decode(response.responseText);
                if (obj.success) {
                    result = true;
                } else {
                    result = false;
                }
            },
            failure: function (response, opts) {
                result = false;
            }

        });
        return result;
    }

    function saveAtomicScript() {
        if (!createttomAct()) {
            return;
        }

        var smbussCbOutSide = Ext.create('Ext.form.field.ComboBox', {
            name: 'bussId',
            queryMode: 'local',
            fieldLabel: "一级分类",
            padding: '0 5 0 0',
            displayField: 'bsName',
            valueField: 'iid',
            editable: false,
            emptyText: "--请选择一级分类--",
            store: bussData,
            listeners: {
                change: function () { // old is keyup
                    smbussTypeCbOutSide.clearValue();
                    smbussTypeCbOutSide.applyEmptyText();
                    smbussTypeCbOutSide.getPicker().getSelectionModel().doMultiSelect([], false);
                    bussTypeData.load({params: {fk: this.value}});
                }
            }
        });

        var smbussTypeCbOutSide = Ext.create('Ext.form.field.ComboBox', {
            name: 'bussTypeId',
            padding: '0 5 0 0',
            queryMode: 'local',
            fieldLabel: "二级分类",
            displayField: 'sysType',
            valueField: 'sysTypeId',
            editable: false,
            emptyText: "--请选择二级分类--",
            allowBlank: false,
            store: bussTypeData
        });


        var queryForm = Ext.create('Ext.form.Panel', {
            border: false,
            frame: true,
            width: '100%',
            layout: 'form',
            fieldDefaults: {
                labelWidth: 60,
                labelAlign: "left",
                width: '100%'
            },
            items: [smbussCbOutSide, smbussTypeCbOutSide]
        });

        saveAtomicScriptWin = Ext.create('Ext.window.Window', {
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            title: '设置原子脚本',
            closable: true,
            //id:'upLoadpanel',
            modal: true,//背景灰
            width: 500,
            height: 200,
            frame: true,
            items: [queryForm],
            buttons: [
                {
                    id: 'upldBtnId',
                    text: '确定',
                    baseCls: 'Common_Btn',
                    handler: function () {

                        var bussId = queryForm.getForm().findField("bussId").getValue();
                        var bussTypeId = queryForm.getForm().findField("bussTypeId").getValue();
                        var records = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                        var scriptNameArray = [];
                        var uperIdArray = []
                        Ext.each(records, function (item) {
                            scriptNameArray.push(item.data.scriptName);//uperId
                            uperIdArray.push(item.data.uperId);
                        });

                        if (!Ext.isEmpty(bussId) && !Ext.isEmpty(bussTypeId)) {
                            Ext.Ajax.request({
                                url: 'saveAtomicScriptRuleParams.do',
                                method: 'POST',
                                params: {
                                    bussId: bussId,
                                    bussTypeId: bussTypeId,
                                    atomicBSnames: scriptNameArray.join(),
                                    uperIds: uperIdArray.join()
                                },
                                success: function (response, opts) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    Ext.Msg.alert('提示', message);
                                    pageBar.moveFirst();
                                    saveAtomicScriptWin.destroy();
                                },
                                failure: function (result, request) {
                                    secureFilterRs(result, "请求返回失败！", request);
                                }
                            });

                        } else if (Ext.isEmpty(bussId)) {
                            Ext.Msg.alert('提示', "保存失败，请选择一级分类");
                        } else if (Ext.isEmpty(bussTypeId)) {
                            Ext.Msg.alert('提示', "保存失败，请选择二级分类");
                        }
                    }
                }
            ]
        });

        saveAtomicScriptWin.show();

    }

    function deleteAtomic() {
        if (!deleteAtomact()) {
            return;
        }

        var records = scriptServiceReleaseGrid.getSelectionModel().getSelection();
        var uperIdArray = []
        Ext.each(records, function (item) {
            uperIdArray.push(item.data.uperId);
        });
        if (uperIdArray.length > 0) {
            Ext.Ajax.request({
                url: 'deleteAtomicScript.do',
                method: 'POST',
                params: {
                    uperIds: uperIdArray.join()
                },
                success: function (response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    pageBar.moveFirst();
                },
                failure: function (result, request) {
                    pageBar.moveFirst();
                    secureFilterRs(result, "请求返回失败！", request);
                }
            });
        } else {
            Ext.Msg.alert('提示', "请选择需要取消的原子活动");
        }
    }

    //编辑按钮执行一个.Ext.Ajax.request({do跳转到编辑页面
    function editScript(iid, status, a, b, c, hasVersion, uuid, serviceType, scriptName, keywords, isExam, platForm, dbType, serviceIdNum, isTry, labels) {
        if (istrySwitch) {
            if (isTry == 1) {
                Ext.Msg.alert('提示', "该脚本正在测试中，不能编辑！");
                return;
            }
        }
        if (status == 2) { // 已经不是草稿状态，处于审核中或者已经上线
            Ext.Msg.alert('提示', "该脚本正在审核中，不能编辑！");
            return;
        } else {
            destroyRubbish(); //销毁本页垃圾
            var thisUrl = 'forwardEditScript.do?serviceId=' + iid + '&hasVersion=' + hasVersion + '&uuid=' + uuid + '&serviceName=' + a + '&serviceType=' + serviceType + '&scriptName=' + scriptName + '&keywords=' + keywords + '&isExam=' + isExam + '&platForm=' + platForm + '&dbType=' + dbType + '&switchFlag=0' + '&serviceIdNum=' + serviceIdNum + '&labels=' + labels ;
            contentPanel.getLoader().load({
                url: encodeURI(thisUrl),
//	   			params: {
//	   				filter_serviceName:filter_serviceName,
//	   				filter_serviceType:filter_serviceType
//	   			},
                params: filter,
                scripts: true
            });
        }
    }

    function editScriptFlow(iid, status, serviceName, bussId, bussTypeId, isTry) {
//	   	alert(iid);
        if (istrySwitch) {
            if (isTry == 1) {
                Ext.Msg.alert('提示', "该脚本正在测试中，不能编辑！");
                return;
            }
        }
        if (status == 2) { // 已经不是草稿状态，处于审核中或者已经上线
            Ext.Msg.alert('提示', "该脚本正在审核中，不能编辑！");
            return;
        } else {
            destroyRubbish(); //销毁本页垃圾
            var params = filter;
            params['iid'] = iid;
            params['serviceName'] = serviceName;
            params['actionType'] = 'edit';
            params['bussId'] = bussId;
            params['bussTypeId'] = bussTypeId;
            params['flag'] = 0;
            contentPanel.getLoader().load({
                url: 'flowCustomizedInitScriptService.do',
                params: params,
                scripts: true
            });
        }
    }

    function testScript(iid) {
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'scriptExecStart.do?serviceId=' + iid + '&flag=0' + '&url=forwardScriptServiceRelease.do',
            scripts: true
        });
    }

    function testScriptNew(iid, serviceName, bussId, bussTypeId, scriptType, uuid, execUserName) {
        var chosedAgentWin;
        var chosedAgentIds = new Array();
        var upldWin;
        var upLoadformPane = '';
        var cpdsMap = {};//<cpid,dsid>
        var selCpId = -1;
        Ext.define('resourceGroupModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'id',
                type: 'int',
                useNull: true
            }, {
                name: 'name',
                type: 'string'
            }, {
                name: 'description',
                type: 'string'
            }]
        });

        var resourceGroupStore = Ext.create('Ext.data.Store', {
            autoLoad: !removeAgentSwitch,
            autoDestroy: true,
            model: 'resourceGroupModel',
            proxy: {
                type: 'ajax',
                url: 'getResGroupForScriptService.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });
        resourceGroupStore.on('load', function () {
            var ins_rec = Ext.create('resourceGroupModel', {
                id: '-1',
                name: '未分组',
                description: ''
            });
            resourceGroupStore.insert(0, ins_rec);
        });
        var resourceGroupObj = Ext.create('Ext.form.field.ComboBox',
            {
                fieldLabel: '资源组',
                emptyText: '--请选择资源组--',
                labelAlign: 'right',
                labelWidth: 51,
                width: '25.5%',
                columnWidth: 1,
                multiSelect: true,
                hidden: removeAgentSwitch,
                store: resourceGroupStore,
                displayField: 'name',
                valueField: 'id',
                triggerAction: 'all',
                editable: true,
                queryMode: 'local',
                mode: 'local'

            });

        var agentStatusStore = Ext.create('Ext.data.Store', {
            fields: ['id', 'name'],
            data: [
                {"id": "-10000", "name": "全部"},
                {"id": "0", "name": "正常"},
                {"id": "1", "name": "异常"},
                {"id": "2", "name": "升级中"}
            ]
        });

        var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
            name: 'agentStatus',
            labelWidth: 79,
            queryMode: 'local',
            fieldLabel: 'Agent状态',
            displayField: 'name',
            valueField: 'id',
            editable: false,
            emptyText: '--请选择Agent状态--',
            store: agentStatusStore,
            width: '25.5%',
            labelAlign: 'right'

        });

        Ext.define('appNameModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'appName',
                type: 'string'
            }]
        });

        var app_name_store = Ext.create('Ext.data.Store', {
            autoLoad: CMDBflag,
            model: 'appNameModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentAppNameList.do?envType=0',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        var app_name = Ext.create('Ext.form.ComboBox', {
            name: 'appname',
            fieldLabel: "应用名称",
            emptyText: '--请选择应用名称--',
            store: app_name_store,
            hidden: !CMDBflag,
            queryMode: 'local',
            width: "24.5%",
            displayField: 'appName',
            valueField: 'appName',
            labelWidth: 65,
            labelAlign: 'right',
            listeners: {
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = Ext.util.Format.trim(e.query);
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }

            }
        });

        var agent_ip = new Ext.form.TextField({
            name: 'agentip',
            fieldLabel: 'Agent IP',
            displayField: 'agentip',
            emptyText: '--请输入Agent IP--',
            labelWidth: 70,
            labelAlign: 'right',
            width: '25.7%'

        });
        var host_name = new Ext.form.TextField({
            name: 'hostname',
            fieldLabel: '计算机名',
            displayField: 'hostname',
            emptyText: '--请输入计算机名--',
            labelWidth: 65,
            labelAlign: 'right',
            width: '24.6%'

        });

        var descriptions = new Ext.form.TextField({
            name: 'descriptions',
            fieldLabel: '描述',
            displayField: 'hostname',
            emptyText: '--请输入描述--',
            labelWidth: 65,
            labelAlign: 'right',
            width: '24.6%'

        });

        Ext.define('sysNameModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'sysName',
                type: 'string'
            }]
        });

        var sys_name_store = Ext.create('Ext.data.Store', {
            autoLoad: CMDBflag,
            model: 'sysNameModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentSysNameList.do?envType=0&switchFlag=0',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        var sys_name = Ext.create('Ext.form.ComboBox', {
            name: 'sysname',
            fieldLabel: "名称",
            emptyText: '--请选择名称--',
            store: sys_name_store,
            queryMode: 'local',
            hidden: !CMDBflag,
            width: "25%",
            displayField: 'sysName',
            valueField: 'sysName',
            labelWidth: 37,
            labelAlign: 'right',
            listeners: {
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = Ext.util.Format.trim(e.query);
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }
            }
        });

        var os_type = new Ext.form.TextField({
            name: 'ostype',
            fieldLabel: '操作系统',
            displayField: 'ostype',
            emptyText: '--请输入操作系统--',
            labelWidth: 79,
            labelAlign: 'right',
            width: CMDBflag ? '25.5%' : '24.2%'

        });
        var sysName1 = new Ext.form.TextField({
            name: 'sysName1',
            fieldLabel: '名称',
            displayField: 'sysName1',
            emptyText: '--请输入名称--',
            labelWidth: 37,
            labelAlign: 'right',
            width: '24.5%'

        });
        var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
            region: 'north',
            bodyCls: 'x-docked-noborder-top',
            border: false,
            iqueryFun: function () {
                agent_grid.ipage.moveFirst();
            },
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [sys_name, app_name, host_name, sysName1, os_type, descriptions
                ]
            },
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    items: [agent_ip, resourceGroupObj, agentStatusCb,
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '查询',
                            handler: function () {
                                agent_grid.ipage.moveFirst();
                            }
                        },
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '清空',
                            handler: function () {
                                agent_ip.setValue('');
                                app_name.setValue('');
                                sys_name.setValue('');
                                host_name.setValue('');
                                os_type.setValue('');
                                sysName1.setValue('');
                                resourceGroupObj.setValue('');
                                agentStatusCb.setValue('');
                                descriptions.setValue('');
                            }
                        }, {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '导入',
                            handler: importExcel
                        }, {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            hidden: !batchQuerySwitch,
                            text: 'IP批量查询',
                            handler: function () {
                                batchQueryForIp();
                            }
                        }
                    ]
                }]
        });

        function checkFile(fileName) {
            var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
            if (!file_reg.test(fileName)) {
                Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
                //Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)');
                return false;
            }
            return true;
        }

        function importExcel() {
            //销毁win窗口
            if (!(null == upldWin || undefined == upldWin || '' == upldWin)) {
                upldWin.destroy();
                upldWin = null;
            }

            if (!(null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
                upLoadformPane.destroy();
                upLoadformPane = null;
            }
            //导入文件Panel
            upLoadformPane = Ext.create('Ext.ux.ideal.form.Panel', {
                width: 370,
                height: 100,
                bodyCls: 'x-docked-noborder-top',
                frame: true,
                items: [
                    {
                        xtype: 'filefield',
                        name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
                        fieldLabel: '选择文件',
                        labelWidth: 65,
                        msgTarget: 'side',
                        anchor: '100%',
                        buttonText: '浏览'
                    }
                ],
                buttons: [
                    {
                        id: 'upldBtnIdAudi',
                        text: '导入Agent文件',
                        handler: function () {
                            var form = this.up('form').getForm();
                            var upfile = form.findField("file").getValue();
                            if (upfile == '') {
                                Ext.Msg.alert('提示', "请选择文件...");
                                return;
                            }

                            var hdtmpFilNam = form.findField("file").getValue();
                            if (!checkFile(hdtmpFilNam)) {
                                form.findField("file").setRawValue('');
                                return;
                            }

                            if (form.isValid()) {
                                Ext.MessageBox.wait("数据处理中...", "进度条");
                                form.submit({
                                    url: 'importAgentForStart.do',
                                    params: {
                                        envType: 0
                                    },
                                    success: function (form, action) {
                                        var msg = Ext.decode(action.response.responseText).message;

                                        var status = Ext.decode(action.response.responseText).status;
                                        var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;

                                        if (status == 1) {
                                            if (matchAgentIds && matchAgentIds.length > 0) {
                                                Ext.MessageBox.buttonText.yes = "确定";
                                                Ext.MessageBox.buttonText.no = "取消";
                                                Ext.Msg.confirm("请确认", msg, function (id) {
                                                    if (id == 'yes') {
                                                        Ext.Msg.alert('提示', "导入成功！");
                                                        agent_ip.setValue('');
                                                        app_name.setValue('');
                                                        sys_name.setValue('');
                                                        host_name.setValue('');
                                                        os_type.setValue('');
                                                        resourceGroupObj.setValue('');
                                                        agentStatusCb.setValue('');
                                                        chosedAgentIds = matchAgentIds;
                                                        pageBar.moveFirst();
                                                    }
                                                });
                                            } else {
                                                Ext.Msg.alert('提示-没有匹配项', msg);
                                            }

                                        } else {
                                            Ext.Msg.alert('提示', "导入成功！");
                                            agent_ip.setValue('');
                                            app_name.setValue('');
                                            sys_name.setValue('');
                                            host_name.setValue('');
                                            os_type.setValue('');
                                            resourceGroupObj.setValue('');
                                            agentStatusCb.setValue('');
                                            chosedAgentIds = matchAgentIds;
                                            pageBar.moveFirst();
                                        }

                                        upldWin.close();
                                        return;
                                    },
                                    failure: function (form, action) {
                                        secureFilterRsFrom(form, action);
                                    }
                                });
                            }
                        }
                    }, {
                        text: '下载模板',
                        handler: function () {
                            window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
                        }
                    }
                ]
            });
            //导入窗口
            upldWin = Ext.create('Ext.window.Window', {
                title: '设备信息批量导入',
                width: 400,
                height: 200,
                modal: true,
                resizable: false,
                closeAction: 'destroy',
                items: [upLoadformPane]
            }).show();
            upldWin.on("beforeshow", function (self, eOpts) {
                var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
                form.reset();
            });

            upldWin.on("destroy", function (self, eOpts) {
                upLoadformPane.destroy();
            });
        }

        Ext.define('agentModel', {
            extend: 'Ext.data.Model',
            idProperty: 'iid',
            fields: [
                {name: 'iid', type: 'string'},
                {name: 'sysName', type: 'string'},
                {name: 'appName', type: 'string'},
                {name: 'hostName', type: 'string'},
                {name: 'osType', type: 'string'},
                {name: 'agentIp', type: 'string'},
                {name: 'agentPort', type: 'string'},
                {name: 'agentDesc', type: 'string'},
                {name: 'agentDesc', type: 'string'},
                {name: 'agentState', type: 'int'}
            ]
        });

        agent_grid_url = 'getAllAgentForIpSearch.do';
        agent_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 51,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAllAgentList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        var agent_store_chosed = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 30,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentChosedList.do',
                actionMethods: {
                    create: 'POST',
                    read: 'POST', // by default GET
                    update: 'POST',
                    destroy: 'POST'
                },
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'iid', hidden: true},
            {text: '名称', dataIndex: 'sysName', flex: 1},
            {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
            {text: '计算机名', dataIndex: 'hostName', flex: 1},
            {text: 'IP', dataIndex: 'agentIp', width: 150},
            {text: '端口号', dataIndex: 'agentPort', width: 100},
            {text: '操作系统', dataIndex: 'osType', width: 140},
            {
                text: '描述', dataIndex: 'agentDesc', flex: 1,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == 0) {
                        backValue = "Agent正常";
                    } else if (value == 1) {
                        backValue = "Agent异常";
                    }
                    return backValue;
                }
            }
        ];

        var agent_columns_chosed = [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'iid', hidden: true},
            {text: '名称', dataIndex: 'sysName', width: 160},
            {text: 'IP', dataIndex: 'agentIp', width: 110},
            {text: '计算机名', dataIndex: 'hostName', width: 150},
            {text: '操作系统', dataIndex: 'osType', width: 110},
            {text: '端口号', dataIndex: 'agentPort', width: 60},
            {
                text: '描述', dataIndex: 'agentDesc', flex: 1,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                text: '执行用户',
                dataIndex: 'execuser',
                width: 90,
                align: 'left',//整体左对齐
                renderer: function (value, metaData, record, rowNum) {
                    var displayValue = value;
                    var recordedData = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (undefined == recordedData) {
                        if ("" == value || undefined == value) {
                            displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                        } else {
                            displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + displayValue + "</a>";
                        }
                    } else {
                        if ("" == recordedData) {
                            displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
                        } else {
                            displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + recordedData + "</a>";
                        }
                    }
                    return displayValue;
                },
                listeners: {
                    click: function (a, b, c, d, e, record) {
                        openExecUserConfigData(record);
                    }
                }
            },
            {
                text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == 0) {
                        backValue = "Agent正常";
                    } else if (value == 1) {
                        backValue = "Agent异常";
                    }
                    return backValue;
                }
            }
        ];

        agent_store.on('beforeload', function (store, options) {
            new_params_agent = {
                agentIp: Ext.util.Format.trim(agent_ip.getValue()),
                appName: app_name.getValue() == null ? '' : Ext.util.Format.trim(app_name.getValue() + ""),
                sysName: CMDBflag ? (sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + "")) : Ext.util.Format.trim(sysName1.getValue()),
                hostName: Ext.util.Format.trim(host_name.getValue()),
                agentDesc: Ext.util.Format.trim(descriptions.getValue()),
                osType: Ext.util.Format.trim(os_type.getValue()),
                rgIds: resourceGroupObj.getValue(),
                agentState: agentStatusCb.getValue(),
                flag: 0,
                switchFlag: 0,
                batchComputerName: pubDesc_sm_ipsearch.getValue(),
            };

            Ext.apply(agent_store.proxy.extraParams, new_params_agent);
        });

        agent_store_chosed.on('beforeload', function (store, options) {
            var new_params = {
                agentIds: JSON.stringify(chosedAgentIds)
            };

            Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
        });
//		    agent_store_chosed.on('load', function(store, options) {
//		    	agent_grid_chosed.getSelectionModel().select(0);
//		    });

        agent_store.on('load', function (store, options) {
            var records = [];//存放选中记录
            for (var i = 0; i < agent_store.getCount(); i++) {
                var record = agent_store.getAt(i);
                for (var ii = 0; ii < chosedAgentIds.length; ii++) {

                    if ((+chosedAgentIds[ii]) == record.data.iid) {
                        records.push(record);
                    }
                }
            }
            agent_grid.getSelectionModel().select(records, false, true);//选中记录
        });
        Ext.define('dbModel', {
            extend: 'Ext.data.Model',
            idProperty: 'iid',
            fields: [
                {name: 'iid', type: 'string'},
                {name: 'driverClass', type: 'string'},
                {name: 'dbUrl', type: 'string'},
                {name: 'dbUser', type: 'string'},
                {name: 'dbType', type: 'string'}
            ]
        });

        var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
            {text: '主键', dataIndex: 'iid', hidden: true},
            {
                text: '驱动类',
                dataIndex: 'driverClass',
                width: 200,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                text: 'DBURL',
                dataIndex: 'dbUrl',
                flex: 1,
                width: 80,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {text: 'DB用户', dataIndex: 'dbUser', width: 150, hidden: true},
            {text: 'DB类型', dataIndex: 'dbType', width: 110}];

        /*var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
				  	store: agent_store,
				  	dock: 'bottom',
					baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
					displayInfo: true,
					border:false,
					displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
					emptyMsg: "没有记录"
				  });*/

        agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store: agent_store,
            border: false,
            columnLines: true,
            columns: agent_columns,
            // bbar : pageBar,
            cls: 'customize_panel_back',
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
            listeners: {
                select: function (e, record, index, eOpts) {
                    if (chosedAgentIds.indexOf(record.get('iid')) == -1) {
                        chosedAgentIds.push(record.get('iid'));
                    }
                },
                deselect: function (e, record, index, eOpts) {
                    if (chosedAgentIds.indexOf(record.get('iid')) > -1) {
                        chosedAgentIds.remove(record.get('iid'));
                    }
                }
            }
        });

//		    var pageBarForAgentChosedGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		    	store : agent_store_chosed,
//		    	dock : 'bottom',
//				baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//				border:false,
//				displayInfo : true
//		    });
        var dbinfo_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 50,
            model: 'dbModel',
            proxy: {
                type: 'ajax',
                url: 'getDbSqlDriverInfo.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });
        var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true,
            listeners: {
                select: function (selModel, record, index, eOpts) {
                    dbinfo_store.load({
                        params: {
                            agentId: record.get("iid"),
                            agentIp: record.get("agentIp"),
                            agentPort: record.get("agentPort")
                        }
                    });
                    //当前选中cpid
                    selCpId = record.get("iid");
                },
                deselect: function (selModel, record, index, eOpts) {
                    dbinfo_store.removeAll();
                    var cpid = record.get("iid");
                    cpdsMap[cpid] = -1;//清空
                    selCpId = -1;
                }
            }
        });

        var execUserForTry = new Ext.form.TextField({
            name: 'execUserForTry',
            id: 'execUserForTry',
            fieldLabel: '执行用户',
            value: execUserName,
            emptyText: '-请输入执行用户-',
            labelWidth: 60,
            padding: '5',
            labelAlign: 'right',
            width: '17%'
        });

        var agent_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
            title: '已选服务器',
            region: 'west',
            store: agent_store_chosed,
            border: true,
            width: '100%',
            columnLines: true,
//		    	height: contentPanel.getHeight()*0.48,
//		    	height: 450,
            emptyText: '没有选择服务器',
            columns: agent_columns_chosed,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            selModel: selModelForagent_grid_chosed,
//		    	bbar : pageBarForAgentChosedGrid,
            listeners: {
                activate: function (tab) {
                    resGroupFlag = 'false';
                    agent_store_chosed.load();
                    chosedGroupIdCopy.splice(0, chosedGroupIdCopy.length);
                    chosedGroupIds.splice(0, chosedGroupIds.length);
                    chosedGroupNames.splice(0, chosedGroupNames.length);
                    chosedGroupNameCopy.splice(0, chosedGroupNames.length);
                }
            },
            dockedItems: [execUserForTry, {
                xtype: 'toolbar',
//					baseCls:'customize_gray_back',
                dock: 'top',
                items: [{
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '删除',
                    handler: function () {
                        var records = agent_grid_chosed.getSelectionModel().getSelection();
                        if (records.length > 0) {
                            for (var i = 0, len = records.length; i < len; i++) {
                                chosedAgentIds.remove(records[i].get('iid'));
                                $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + records[i].get('iid'), "");
                            }
                            agent_grid_chosed.ipage.moveFirst();
                            pageBar.moveFirst();
                        } else {
                            Ext.Msg.alert('提示', "请选择服务器！");
                            return;
                        }
                    }
                },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '增加服务器',
                        handler: function () {
                            if (!chosedAgentWin) {
                                chosedAgentWin = Ext.create('Ext.window.Window', {
                                    title: '增加服务器',
                                    autoScroll: true,
                                    modal: true,
                                    resizable: false,
                                    closeAction: 'hide',
                                    layout: 'border',
                                    cls: 'window_border panel_space_left panel_space_right',
                                    width: contentPanel.getWidth() - 190,
                                    height: contentPanel.getHeight(),
                                    items: [search_ip_form, agent_grid],
                                    dockedItems: [{
                                        xtype: 'toolbar',
                                        dock: 'bottom',
                                        layout: {pack: 'center'},
                                        items: [{
                                            xtype: "button",
                                            text: "确定",
                                            cls: 'Common_Btn',
                                            margin: '6',
                                            handler: function () {
                                                var agent_grid_chosedRecord = agent_grid_chosed.getStore().getRange();
                                                if (agent_grid_chosedRecord.length > 0) {
                                                    for (var x = 0, xLen = agent_grid_chosedRecord.length; x < xLen; x++) {
                                                        var flag = false;
                                                        var chosedRecordIid = agent_grid_chosedRecord[x].get('iid');
                                                        for (var y = 0, yLen = chosedAgentIds.length; y < yLen; y++) {
                                                            var chosedNewIid = chosedAgentIds[y];
                                                            if (chosedRecordIid == chosedNewIid) {
                                                                flag = true;
                                                                break;
                                                            }
                                                        }
                                                        if (!flag) {
                                                            $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + chosedRecordIid, "");
                                                        }
                                                    }
                                                }
                                                agent_store_chosed.load();
                                                this.up("window").close();
                                            }
                                        }, {
                                            xtype: "button",
                                            text: "关闭",
                                            cls: 'Common_Btn',
                                            margin: '6',
                                            handler: function () {
                                                this.up("window").close();
                                            }
                                        }]
                                    }]
                                });
                            }
                            chosedAgentWin.show();
                            agent_store.load();
                        }
                    }]
            }]
        });
        // 定义复选框
        var selModelForDbsource = Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true,
            mode: "SINGLE",
            listeners: {
                select: function (selModel2, record, index, eOpts) {
                    var dsid = record.get("iid");
                    if (selCpId != -1) {
                        cpdsMap[selCpId] = dsid;//绑定
                    }
                },
                deselect: function (selModel2, record, index, eOpts) {
                    cpdsMap[selCpId] = -1;//清空
                }
            }
        });
        var db_soucre_grid = Ext.create('Ext.grid.Panel', {
            store: dbinfo_store,
            width: '70%',
            region: 'center',
            border: true,
            columnLines: true,
            columns: dbsource_columns,
            cls: 'window_border panel_space_top  panel_space_right',
            selModel: selModelForDbsource
        });
        dbinfo_store.on('load', function (store, options) {
            db_soucre_grid.getSelectionModel().select(0);
        });

        Ext.define('paramModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            },
                {
                    name: 'paramType',
                    type: 'string'
                },
                {
                    name: 'parameterName',
                    type: 'string'
                },
                {
                    name: 'paramDefaultValue',
                    type: 'string'
                },
                {
                    name: 'ruleName',
                    type: 'string'
                },
                {
                    name: 'paramDesc',
                    type: 'string'
                },
                {
                    name: 'paramOrder',
                    type: 'int'
                }]
        });

        var paramStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            pageSize: 30,
            model: 'paramModel',
            proxy: {
                type: 'ajax',
                url: 'getAllScriptParams.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        paramStore.on('beforeload', function (store, options) {
            var new_params = {
                scriptId: uuid
            };

            Ext.apply(paramStore.proxy.extraParams, new_params);
        });

        //加密参数将参数值转换成明文在前台展示
        paramStore.on ('load', function (me, records, successful, eOpts) {
            $.each(records, function(index, record){
                if(record.get('paramType') == 'IN-string(加密)'){
                    record.set('paramDefaultValue', getSMEncode(record.get('paramDefaultValue'),0));
                }
            });
        });

        var defultEditor = Ext.create('Ext.grid.CellEditor', {
            field: Ext.create('Ext.form.field.Text', {
                selectOnFocus: true
            })
        });
        var passwordEditor = Ext.create('Ext.grid.CellEditor', {
            field: Ext.create('Ext.form.field.Text', {
                selectOnFocus: true,
                inputType: 'password'
            })
        });
        Ext.define('parameterCheckModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            }, {
                name: 'ruleName',
                type: 'string'
            }, {
                name: 'checkRule',
                type: 'string'
            }, {
                name: 'ruleDes',
                type: 'string'
            }]
        });
        var store = Ext.create('Ext.data.Store', {
            autoLoad: bhParameterCheckSwitch,
            model: 'parameterCheckModel',
            proxy: {
                type: 'ajax',
                url: 'getScriptParameterCheck.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        var paramColumns = [
            {
                text: '主键',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '类型',
                dataIndex: 'paramType',
                width: 100,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    var coun = '';
                    if (value == 'IN-string(加密)') {
                        coun = StringToPassword(record.get('paramDefaultValue'));
                    } else {
                        coun = record.get('paramDefaultValue');
                    }
                    let ruleMsg = bhParameterCheckSwitch ? "<br>验证规则：" + record.get('ruleName') : "";
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                        + "<br>枚举名：" + record.get('parameterName')
                        + "<br>默认值：" + coun
                        + ruleMsg
                        + "<br>排序：" + record.get('paramOrder')
                        + "<br>描述：" + record.get('paramDesc'))
                        + '"';
                    return value;
                }
            },
            {
                dataIndex: 'parameterName',
                width: 80,
                text: '枚举名称',
                editable: false,
                editor: {},
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    var coun = '';
                    coun = record.get('parameterName');

                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                        + "<br>默认值：" + coun
                        + "<br>排序：" + record.get('paramOrder')
                        + "<br>描述：" + record.get('paramDesc'))
                        + '"';
                    return coun;
                }
            }, {
                dataIndex: 'paramDefaultValue',
                width: 80,
                text: '参数值',
                editor: {},
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    let showValue = value;

                    let paramType = record.get('paramType');

                    if (paramType == 'IN-string(加密)') {
                        //加密参数解密获取长度
                        var decodeVal = getSMEncode(value,0);
                        if(!(decodeVal == null || decodeVal == '')){
                            value = decodeVal;
                        }
                        let xing = "";
                        let len = value.length;
                        for (let i = 0; i < len; i++) {
                            xing += "*";
                        }
                        showValue = xing;
                    }
                    return showValue;
                }
            }, {
                dataIndex: 'ruleName',
                width: 120,
                text: '验证规则',
                hidden: !bhParameterCheckSwitch,
                editor: {
                    xtype: 'combobox',
                    store: store,
                    queryMode: 'local',
                    displayField: 'ruleName',
                    valueField: 'ruleName',
                    editable: false,
                }
            },
//		    },
            {
                text: '顺序',
                dataIndex: 'paramOrder',
                width: 50,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                text: '描述',
                dataIndex: 'paramDesc',
                flex: 1,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            }];

        var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
            clicksToEdit: 2
        });
        if (scriptType == 'sql') {
            agent_grid_chosed.setWidth((contentPanel.getWidth() - 250) / 2);
            db_soucre_grid.setWidth((contentPanel.getWidth() - 250) / 2);
            db_soucre_grid.show();
        } else {
            agent_grid_chosed.setWidth((contentPanel.getWidth() - 290));
            db_soucre_grid.hide();
        }


        //*********************************************增加资源组相关控件 start***************************************************************************

        Ext.define('groupModel', {
            extend: 'Ext.data.Model',
            idProperty: 'id',
            fields: [
                {name: 'id', type: 'long'},
                {name: 'name', type: 'string'},
                {name: 'execUserName', type: 'string'},
                {name: 'description', type: 'string'}
            ]
        });
        var group_columns = [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '组名称', dataIndex: 'name', width: 160},
            {text: '启动用户', dataIndex: 'execUserName', width: 160},
            {text: '描述', dataIndex: 'description', width: 280},
            {
                text: '操作',
                xtype: 'actiontextcolumn',
                flex: 1,
                align: 'left',
                items: [{
                    text: '详情',
                    iconCls: 'execute',
                    handler: function (grid, rowIndex) {
                        var iid = grid.getStore().data.items[rowIndex].data.id;
                        getAgentInfoByGroupId(iid);
                    }
                }]
            }];

        /**
         * 获取资源组下的agent
         */
        function getAgentInfoByGroupId(iid) {
            Ext.define('agentModelByGroup', {
                extend: 'Ext.data.Model',
                idProperty: 'id',
                fields: [
                    {name: 'id', type: 'long'},
                    {name: 'ip', type: 'string'},
                    {name: 'port', type: 'string'},
                    {name: 'hostName', type: 'string'}
                ]
            });

            var agentinfo_group_store = Ext.create('Ext.data.Store', {
                autoLoad: false,
                pageSize: 50,
                model: 'agentModelByGroup',
                proxy: {
                    type: 'ajax',
                    url: 'agentGroup/getServersForTaskApply.do',
                    reader: {
                        type: 'json',
                        root: 'dataList',
                        totalProperty: 'total'
                    }
                }
            });

            agentinfo_group_store.on('beforeload', function (store, options) {
                var new_params = {
                    groupId: iid
                };

                Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
            });
            var agentinfo_columns_group = [
                {text: '序号', xtype: 'rownumberer', width: 40},
                {text: '主键', dataIndex: 'id', hidden: true},
                {text: 'Agent名称', dataIndex: 'hostName', flex: 1},
                {text: 'IP', dataIndex: 'ip', width: 160},
                {text: '端口', dataIndex: 'port', width: 160}];


            var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
                region: 'center',
                store: agentinfo_group_store,
                border: false,
                columnLines: true,
                cls: 'customize_panel_back',
                columns: agentinfo_columns_group,
                ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
            });
            agentinfo_group_store.load();
            var agentinfoGroupWin = Ext.create('Ext.window.Window', {
                title: '增加资源组',
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                layout: 'border',
                width: contentPanel.getWidth() - 190,
                height: contentPanel.getHeight(),
                items: [agentinfo_group_grid]
            });
            agentinfoGroupWin.show();
        }


        var group_columns_chosed = [
            {text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'id', hidden: true},
            {text: '组名称', dataIndex: 'name', width: 160},
            {text: '启动用户', dataIndex: 'execUserName', width: 160},
            {text: '描述', dataIndex: 'description', flex: 1}];
        var group_store_chosed = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 30,
            model: 'groupModel',
            proxy: {
                type: 'ajax',
                url: 'agentGroup/groups.do',
                actionMethods: {
                    create: 'POST',
                    read: 'POST', // by default GET
                    update: 'POST',
                    destroy: 'POST'
                },
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        group_store_chosed.on('beforeload', function (store, options) {
            var new_params = {
                groupIids: chosedGroupIdCopy,
                from: "taskApplayChosed"
            }
            Ext.apply(group_store_chosed.proxy.extraParams, new_params);
        });

        var group_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
            title: '已选资源组',
            region: 'west',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            store: group_store_chosed,
            border: true,
            width: '100%',
            columnLines: true,
            height: contentPanel.getHeight() * 0.48,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            emptyText: '没有选择资源组',
            columns: group_columns_chosed,
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
            listeners: {
                activate: function (tab) {
                    resGroupFlag = 'true';
                    group_store_chosed.load();
                    for (var y = 0, yLen = chosedAgentIds.length; y < yLen; y++) {
                        $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + chosedAgentIds[y], "");
                    }
                    chosedAgentIds.splice(0, chosedAgentIds.length);
                }
            },
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '删除',
                            handler: function () {
                                var records = group_grid_chosed.getSelectionModel().getSelection();
                                if (records.length > 0) {
                                    for (var i = 0, len = records.length; i < len; i++) {
                                        chosedGroupIdCopy.remove(records[i].get('id'));
                                        chosedGroupIds.remove(records[i].get('id'));
                                        chosedGroupNames.remove(records[i].get('name'));
                                        chosedGroupNameCopy.remove(records[i].get('name'));
                                    }
                                    group_grid_chosed.ipage.moveFirst();
                                    group_grid.ipage.moveFirst();
                                } else {
                                    Ext.Msg.alert('提示', '请选择资源组！')
                                    return
                                }
                            }
                        },
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '增加资源组',
                            handler: function () {
                                chosedGroupIds = chosedGroupIdCopy;
                                choesdGroupNames=chosedGroupNameCopy;
                                var iresName = new Ext.form.TextField({
                                    name: 'iresName',
                                    fieldLabel: '组名称',
                                    displayField: 'iresName',
                                    emptyText: '--请输入组名称--',
                                    labelWidth: 70,
                                    labelAlign: 'right',
                                    width: '25%'
                                });
                                var iexecUser = new Ext.form.TextField({
                                    name: 'iexecUser',
                                    fieldLabel: '启动用户',
                                    displayField: 'iexecUser',
                                    hidden: true,
                                    emptyText: '--启动用户--',
                                    labelWidth: 70,
                                    labelAlign: 'right',
                                    width: '25%'
                                });

                                var search_group_form = Ext.create('Ext.ux.ideal.form.Panel', {
                                    region: 'north',
                                    border: false,
                                    iqueryFun: function () {
                                        group_grid.ipage.moveFirst();
                                    },
                                    bodyCls: 'x-docked-noborder-top',
                                    dockedItems: [{
                                        xtype: 'toolbar',
                                        dock: 'top',
                                        border: false,
                                        items: [iresName, iexecUser,
                                            {
                                                xtype: 'button',
                                                cls: 'Common_Btn',
                                                text: '查询',
                                                handler: function () {
                                                    group_grid.ipage.moveFirst();
                                                }
                                            },
                                            {
                                                xtype: 'button',
                                                cls: 'Common_Btn',
                                                text: '清空',
                                                handler: function () {
                                                    iresName.setValue('');
                                                    iexecUser.setValue('');
                                                }
                                            }]
                                    }]
                                });

                                var group_store = Ext.create('Ext.data.Store', {
                                    autoLoad: false,
                                    pageSize: 50,
                                    model: 'groupModel',
                                    proxy: {
                                        type: 'ajax',
                                        url: 'agentGroup/groups.do',
                                        reader: {
                                            type: 'json',
                                            root: 'dataList',
                                            totalProperty: 'total'
                                        }
                                    }
                                });
                                group_store.on('beforeload', function (store, options) {
                                    var new_params = {
                                        agentGroupName: iresName.getValue()
//		    	iexecUser:iexecUser.getValue()
                                    };
                                    Ext.apply(group_store.proxy.extraParams, new_params);
                                });
                                group_store.on('load', function (store, options) {
                                    var records = [];//存放选中记录
                                    for (var i = 0; i < group_store.getCount(); i++) {
                                        var record = group_store.getAt(i);
                                        for (var ii = 0; ii < chosedGroupIds.length; ii++) {
                                            if (chosedGroupIds[ii] == record.data.id) {
                                                records.push(record);
                                            }
                                        }
                                    }
                                    group_grid.getSelectionModel().select(records, false, true); //选中记录
                                });


                                var group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
                                    region: 'center',
                                    store: group_store,
                                    border: false,
                                    columnLines: true,
                                    cls: 'customize_panel_back',
                                    columns: group_columns,
                                    ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
                                    listeners: {
                                        select: function (e, record, index, eOpts) {
                                            if (chosedGroupIds.indexOf(record.get('id')) == -1) {
                                                chosedGroupIds.push(record.get('id'));
                                                chosedGroupNames.push(record.get('name'));
                                            }
                                        },
                                        deselect: function (e, record, index, eOpts) {
                                            if (chosedGroupIds.indexOf(record.get('id')) > -1) {
                                                chosedGroupIds.remove(record.get('id'));
                                                chosedGroupNames.remove(record.get('name'));
                                            }
                                        }
                                    }
                                });

                                    chosedGroupWin = Ext.create('Ext.window.Window', {
                                        title: '增加资源组',
                                        autoScroll: true,
                                        modal: true,
                                        resizable: false,
                                        layout: 'border',
                                        width: contentPanel.getWidth() - 190,
                                        height: contentPanel.getHeight(),
                                        items: [search_group_form, group_grid],
                                        dockedItems: [
                                            {
                                                xtype: 'toolbar',
                                                dock: 'bottom',
                                                layout: {pack: 'center'},
                                                items: [
                                                    {
                                                        xtype: 'button',
                                                        text: '确定',
                                                        cls: 'Common_Btn',
                                                        margin: '6',
                                                        handler: function () {
                                                            chosedGroupIdCopy = chosedGroupIds;
                                                            chosedGroupNameCopy = chosedGroupNames;
                                                            chosedGroupNames = [];
                                                            chosedGroupIds = [];
                                                            group_store_chosed.load();
                                                            this.up('window').close();
                                                        }
                                                    },
                                                    {
                                                        xtype: 'button',
                                                        text: '关闭',
                                                        cls: 'Common_Btn',
                                                        margin: '6',
                                                        handler: function () {
                                                            chosedGroupNames = [];
                                                            chosedGroupIds = [];
                                                            this.up('window').close();
                                                        }
                                                    }
                                                ]
                                            }
                                        ]
                                    });

                                chosedGroupWin.show();
                                group_store.load();
                            }
                        }
                    ]
                }
            ]
        });


        //*********************************************增加资源组相关控件 end***************************************************************************


        /** 已选窗口 资源组、单独agent tabPanel* */
        var tabPanelForChosedDevice = Ext.create('Ext.tab.Panel',
            {
                tabPosition: 'top',
                region: 'center',
//				    region : 'west',
                activeTab: 0,
                //	    cls:'customize_panel_back',
                cls: 'window_border panel_space_top panel_space_left panel_space_right',
                width: '100%',
//				    height: contentPanel.getHeight()*0.58,
                //	    height : contentPanel.getHeight (),
                border: false,
                defaults:
                    {
                        autoScroll: false
                    },
                items: [agent_grid_chosed, group_grid_chosed]

            });

        var centerPanel = Ext.create('Ext.panel.Panel', {
            region: 'center',
            border: true,
            layout: 'border',
//		        cls:'customize_panel_back',
            items: [tabPanelForChosedDevice, db_soucre_grid]
        });
        Ext.define('paramManangerModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long'
            },
                {
                    name: 'parameterName',
                    type: 'string'
                },
                {
                    name: 'parameterValue',
                    type: 'string'
                },
                {
                    name: 'parameterDesc',
                    type: 'string'
                }]
        });

        Ext.define('paramManangerModel2', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'paravalue',
                    type: 'string'
                }]
        });
        var enumValueStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            // autoDestroy: true,
            // pageSize: 10,
            model: 'paramManangerModel',
            proxy: {
                type: 'ajax',
                url: 'getParameterList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    // totalProperty: 'total'
                }
            }
        });
        var defaultValueStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            model: 'paramManangerModel2',
            proxy: {
                type: 'ajax',
                url: 'getScriptParameterList.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        defaultValueStore.on('beforeload', function (store, options) {
            var new_params = {
                paramName: golbalParamName
            };
            Ext.apply(defaultValueStore.proxy.extraParams, new_params);
        });
        var paramGrid = Ext.create('Ext.grid.Panel', {
            region: 'north',
            title: "脚本参数",
            store: paramStore,
            plugins: [cellEditing],
            border: false,
            columnLines: true,
//		        collapsible : true,
//		        collapsed: true,
            height: 150,
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            columns: paramColumns,
            listeners: {
                //监听函数，在点击之前进行监听
                beforeedit: function (editor, e, eOpts) {

                    var columnIndex = e.column.dataIndex;
                    // 点击的当前行数据
                    var recordData = e.record.data;

                    var paramType = recordData.paramType;           // 是否为枚举类型
                    var parameterName = recordData.parameterName;   // 参数名称
                    // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                    var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue";
                    var columnBooParameterName = columnIndex == "parameterName";
                    var columnBooparamDefaultValue = columnIndex == "paramDefaultValue"
                    // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                    if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                        golbalParamName = parameterName;
                        defaultValueStore.load();
                    }
                    // 判断如果为枚举类型，并且当前操作列为“参数名称”，设置单元格为下拉框
                    if (paramType == "枚举" && columnBooParameterName) {
                        e.column.setEditor({
                            xtype: 'combobox',
                            valueField: "parameterName",
                            displayField: "parameterName",
                            store: enumValueStore,
                            editable: false,
                            listeners: {
                                change: function (field, newValue, oldValue) {
                                    if (oldValue != newValue) {
                                        var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                                        paramDefaultValue.set("paramDefaultValue", "");
                                    }
                                }
                            }
                        });
                    }
                    if (paramType == "枚举" && columnBooparamDefaultValue) {
                        e.column.setEditor({
                            xtype: 'combobox',
                            valueField: "paravalue",
                            displayField: "paravalue",
                            store: defaultValueStore,
                            editable: false
                        });
                    }
                    // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                    if (paramType != "枚举" && columnBoo) {
                        e.column.setEditor({
                            xtype: 'textfield',
                            readOnly: columnIndex == "parameterName" ? true : false,

                        })
                    }

                    if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {

                        // let mimi = Ext.create('Ext.grid.CellEditor', {
                        //     field: Ext.create('Ext.form.field.Text', {
                        //         // selectOnFocus: true,
                        //         inputType: 'password'
                        //     })
                        // })
                        // e.column.setEditor


                        let pass = new Ext.form.TextField({
                            inputType: 'password',
                            // listeners:{
                            //     renderer:function (a,b,c,d){
                            //         alert(a);
                            //     }
                            // }
                        });

                        e.column.setEditor(pass)
                    }
                }
            }
        });

        var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
            region: 'center',
            border: true,
            layout: 'border',
            height: contentPanel.getHeight() - 140,
            cls: 'customize_panel_back',
            items: [centerPanel, paramGrid]
        });

        function setMessage(msg) {
            Ext.Msg.alert('提示', msg);
        }

        var testChooseAgentWin = Ext.create('Ext.window.Window', {
            title: '选择测试服务器',
            autoScroll: true,
            modal: true,
            resizable: false,
            layout: 'border',
            closeAction: 'destroy',
            width: contentPanel.getWidth() - 250,
            height: contentPanel.getHeight(),
            items: [chooseAgentPanel],
            dockedItems: [{
                xtype: 'toolbar',
                // baseCls:'customize_gray_back',
                dock: 'bottom',
                layout: {pack: 'center'},
                items: [{
                    xtype: "button",
                    text: "确定",
                    cls: 'Common_Btn',
                    margin: '6',
                    handler: function () {
                        var me = this;
                        var agents = new Array();
                        if (chosedAgentIds.length <= 0 && chosedGroupNameCopy.length <= 0) {
                            setMessage('请选择服务器或资源组！');
                            return;
                        }
                        var isOk = false;
                        var agentStateMsg = "";

                        if (resGroupFlag != 'true') {
                            // 检查agent状态
                            Ext.Ajax.request({
                                url: 'checkAgentState.do',
                                method: 'POST',
                                async: false,
                                params: {
                                    agentIds: chosedAgentIds
                                },
                                success: function (response, request) {
                                    isOk = Ext.decode(response.responseText).isOk;
                                    agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
                                },
                                failure: function (result, request) {
                                    agentStateMsg = "检查Agent状态出错！";
                                }
                            });
                        }

                        function realTest() {
                            if (scriptType == 'sql') {
                                var records = agent_grid_chosed.getSelectionModel().getSelection();
                                if (records.length > 0) {
                                    for (var k = 0, len = records.length; k < len; k++) {
                                        var cpidTmp = records[k].get('iid');
                                        var dsidTmp = cpdsMap[cpidTmp];
                                        if (null == dsidTmp || undefined == dsidTmp || "" == dsidTmp || -1 == dsidTmp) {
                                            var agentIp = records[k].get('agentIp');
                                            var agentPort = records[k].get('agentPort');
                                            var dsErrMsg = "服务器【" + agentIp + ":" + agentPort + "】没有选择数据源！";
                                            Ext.Msg.alert('提示', dsErrMsg);
                                            return;
                                        }
                                        var tmpRec = {
                                            iid: cpidTmp,
                                            dsid: dsidTmp
                                        };
//			  	  							tmpRec.cpid=cpidTmp;
//			  	  							tmpRec.dsid=dsidTmp;
                                        agents.push(tmpRec);
                                    }
                                }
                            } else {
                                var agentRecords = agent_grid_chosed.getStore().getRange();
                                $.each(chosedAgentIds, function (i, v) {
                                    var execUser = '';
                                    if (agentRecords.length > 0) {
                                        var agenta;
                                        for (var x = 0, len = agentRecords.length; x < len; x++) {
                                            var cpidTmp = agentRecords[x].get('iid');
                                            if (cpidTmp == v) {
                                                execUser = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + cpidTmp);
                                            }
                                            agenta = {
                                                iid: v,
                                                userName: execUser
                                            };
                                        }
                                        agents.push(agenta);
                                    } else {
                                        var agentb = {
                                            iid: v,
                                            userName: ''
                                        };
                                        agents.push(agentb);
                                    }
                                });
                            }

                            paramStore.sort('paramOrder', 'ASC');
                            var m = paramStore.getRange(0, paramStore.getCount() - 1);
                            var jsonDataPara = "[";
                            for (var i = 0, len1 = m.length; i < len1; i++) {
                                var ss = Ext.JSON.encode(m[i].data);
                                if (i == 0) jsonDataPara = jsonDataPara + ss;
                                else jsonDataPara = jsonDataPara + "," + ss;
                            }
                            jsonDataPara = jsonDataPara + "]";
                            var aaaa = new Array();
                            for (var j = 0, len2 = m.length; j < len2; j++) {
                                //var n = 0;
                                var paramType = m[j].get("paramType") ? m[j].get("paramType").trim() : '';
                                var paramDefaultValue = m[j].get("paramDefaultValue") ? m[j].get("paramDefaultValue").trim() : '';
                                var paramDesc = m[j].get("paramDesc") ? m[j].get("paramDesc").trim() : '';
                                var iorder = m[j].get("paramOrder");
                                var parameterName = m[j].get("parameterName");
                                var ruleName = m[j].get("ruleName");
                                //根据选中的验证规则名拿到对应的正则表达式
                                if (bhParameterCheckSwitch) {
                                    if (ruleName != "" && ruleName != null) {
                                        Ext.Ajax.request({
                                            url: 'queryParameterCheckRule.do',
                                            method: 'POST',
                                            async: false,
                                            params: {
                                                ruleName: ruleName
                                            },
                                            success: function (response, request) {
                                                dataList = Ext.decode(response.responseText).dataList;

                                            },
                                            failure: function (result, request) {
                                                Ext.Msg.alert('提示', '获取验证规则失败！');
                                            }
                                        });
                                        //用拿到的正则去校验默认值
                                        var patt = new RegExp(dataList);
                                        if (patt.exec(paramDefaultValue) == null) {
                                            setMessage('顺序为' + "“" + iorder + "”" + '的默认值校验不通过请检查！');
                                            return;
                                        }
                                    }
                                }
                                if ("" == paramType) {
                                    setMessage('参数类型不能为空！');
                                    return;
                                }
                                if (fucCheckLength(paramDesc) > 250) {
                                    setMessage('参数描述不能超过250字符！');
                                    return;
                                }

                                if (paramType.indexOf('int') > -1) {

                                    if (!checkIsInteger(paramDefaultValue) && !Ext.isEmpty(paramDefaultValue)) {
                                        setMessage('参数类型为int，但参数默认值不是int类型！');
                                        return;
                                    }
                                }
                                if (paramType.indexOf('float') > -1) {
                                    if (!checkIsDouble(paramDefaultValue)) {
                                        setMessage('参数类型为float，但参数默认值不是float类型！');
                                        return;
                                    }
                                }
                                if (paramType == '枚举') {
                                    if (parameterName == "" || parameterName == null) {
                                        setMessage('枚举名不能为空！');
                                        return;
                                    }
                                }
                                //加密类型的参数需要加密处理
                                aaaa.push(getSMEncode(paramDefaultValue,1));

                            }


                            var scriptPara = aaaa.join("@@script@@service@@");
                            //var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
                            var dsid = 0;
//				  				if(dsrecords.length>0){
//				  					dsid =  Ext.JSON.encode(dsrecords[0].data.dsId);
//				  				}
                            Ext.MessageBox.wait("数据处理中...", "进度条");
                            Ext.Ajax.request({
                                url: 'execScriptServiceForSync.do',
                                method: 'POST',
                                timeout: 1000000,
                                params: {
                                    serviceId: iid,
                                    execUser: execUserForTry.getValue(),
                                    scriptPara: scriptPara,
                                    jsonData: JSON.stringify(agents),
                                    resGroupFlag: resGroupFlag,
                                    jsonDataPara: jsonDataPara,
                                    chosedGroupIds: chosedGroupIdCopy,
                                    dbsourceid: dsid,
                                    ifrom: 0,
                                    flag: 0
                                },
                                success: function (response, request) {
                                    //var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if (null != message){
                                        Ext.Msg.alert('提示', message);
                                    }else {
                                        me.up("window").close();
                                        chosedGroupNames = [];
                                        chosedGroupIds = [];
                                        chosedGroupIdCopy=[];
                                        chosedGroupNameCopy = [];
                                        Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
                                        var coatId = Ext.decode(response.responseText).coatId;
                                        destroyRubbish1();
                                        forwadScriptTest(coatId, scriptType);
                                    }
                                },
                                failure: function (result, request) {
                                    Ext.Msg.alert('提示', '执行失败！');
                                }
                            });
                        }

                        if (isOk || resGroupFlag == 'true') {
                            realTest();
                        } else {
                            Ext.Msg.confirm("请确认", agentStateMsg + "<br>选择的代理状态为异常，是否仍然进行测试？", function (id) {
                                if (id == 'yes') {
                                    realTest();
                                }
                            });
                        }
                    }
                }, {
                    xtype: "button",
                    text: "取消",
                    cls: 'Common_Btn',
                    handler: function () {
                        chosedGroupNames = [];
                        chosedGroupIds = [];
                        chosedGroupIdCopy = [];
                        chosedGroupNameCopy = [];
                        this.up("window").close();
                    }
                }]
            }]
        }).show();
    }

    function testScriptFlow(iid, serviceName, bussId, bussTypeId) {
        destroyRubbish(); //销毁本页垃圾
        var params = filter;
        params['iid'] = iid;
        params['serviceName'] = serviceName;
        params['actionType'] = 'exec';
        params['bussId'] = bussId;
        params['bussTypeId'] = bussTypeId;
        params['flag'] = 0;
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptService.do',
            params: params,
            scripts: true
        });
    }

    function viewVersion(iid, serviceName, bussId, bussTypeId, scriptType, uuid, label, filter_serviceName, filter_serviceType) {
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'scriptViewVersion.do?serviceId=' + iid + '&flag=0' + '&uuid=' + uuid + '&switchFlag=0' + '&label=' + label,
            params: filter,
            scripts: true
        });
    }

    function viewVersionForFlow(iid, serviceName, bussId, bussTypeId) {
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'scriptViewVersionForFlow.do?serviceId=' + iid + '&flag=0',
            params: filter,
            scripts: true
        });
    }

    function putToolBox(iid, serviceName, bussId, bussTypeId) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function (btn) {
            if (btn == 'yes') {
                Ext.Ajax.request({
                    url: 'putScriptToToolBox.do',
                    method: 'POST',
                    params: {
                        iid: iid,
                        table: 'IEAI_SCRIPT_TEST'
                    },
                    success: function (response, request) {
//							var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', message);
                        scriptServiceReleaseStore.reload();
                    },
                    failure: function (result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }
        });
    }

    function topit(iid, serviceName, bussId, bussTypeId) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function (btn) {
            if (btn == 'yes') {
                Ext.Ajax.request({
                    url: 'scriptTopIt.do',
                    method: 'POST',
                    params: {
                        iid: iid,
                        table: 'IEAI_SCRIPT_TEST'
                    },
                    success: function (response, request) {
//							 var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', message);
                        scriptServiceReleaseStore.reload();
                    },
                    failure: function (result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }
        });
    }

    function StringToPassword(strs) {
        if (strs && strs != null & strs != '') {
            var password = '';
            for (var i = 0; i < strs.length; i++) {
                password = password + '●';
            }
            return password;
        } else {
            return '';
        }
    }

    function destroyRubbish1() {
        contentPanel.clearListeners();
        contentPanel.getLoader().clearListeners();
    }

    function forwadScriptTest(coatId, scriptType) {
        contentPanel.getLoader().load({
            url: "forwardscriptserver.do",
            scripts: true,
            params: {
                coatid: coatId,
                scriptType: scriptType,
                forMyScript: '脚本编写测试'
            }
        });
    }

    var execUserConfigWindow;
    var execUserConfigForm = null;
    var execUserNameText = null;

    function openExecUserConfigData(record) {
        if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
            if (isSumpAgentSwitch == true) {
                var sumpAgentStore = Ext.create('Ext.data.Store', {
                    fields: ['iid', 'userName'],
                    autoLoad: true,
                    proxy: {
                        type: 'ajax',
                        url: 'getSumpAgentUserList.do',
                        reader: {
                            type: 'json',
                            root: 'dataList'
                        }
                    }
                });

                sumpAgentStore.on('beforeload', function (store, options) {
                    var queryparams = {
                        agentId: record.get('iid')
                    };
                    Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
                });

                execUserNameText = Ext.create('Ext.form.field.ComboBox', {
                    name: 'execUserName',
                    labelWidth: 65,
                    queryMode: 'local',
                    fieldLabel: '执行用户',
                    width: 320,
                    displayField: 'userName',
                    valueField: 'iid',
                    editable: true,
                    typeAhead: true,
                    emptyText: '--请选择执行用户--',
                    store: sumpAgentStore,
                    labelAlign: 'right'
                });
                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var sumpAgentCount = sumpAgentStore.getRange();
                    var newExecUserName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (sumpAgentCount.length > 0) {
                        if (undefined == newExecUserName) {
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                        }
                    } else {
                        if (undefined == newExecUserName) {
                            execUserNameText.setValue(record.get('execuser'));
                            execUserNameText.setRawValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName);
                            execUserNameText.setRawValue(newExecUserName);
                        }
                    }
                }
            } else {
                execUserNameText = Ext.create('Ext.form.TextField',
                    {
                        fieldLabel: '执行用户',
                        labelAlign: 'right',
                        name: "execUserName",
                        labelWidth: 65,
                        emptyText: '--请输入执行用户--',
                        width: 320,
                        xtype: 'textfield'
                    });

                if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                    var newExecUserName1 = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                    if (undefined == newExecUserName1) {
                        execUserNameText.setValue(record.get('execuser'));
                    } else {
                        execUserNameText.setValue(newExecUserName1);
                    }
                }
            }

            execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel', {
                region: 'north',
                layout: 'anchor',
                //iqueryFun : queryBtnFun,
                buttonAlign: 'right',
                // baseCls:'customize_gray_back',
                collapsible: false,//可收缩
                collapsed: false,//默认收缩
                border: false,
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    // baseCls:'customize_gray_back',
                    items: [execUserNameText]
                }, {
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    // baseCls:'customize_gray_back',
                    items: ['->', {
                        text: '确定',
                        cls: 'Common_Btn',
                        icon: '',
                        handler: function () {
                            chosedExecUser(record);
                        }
                    }]
                }]
            });

            var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
                layout: 'border',
                width: "100%",
                height: "100%",
                border: false,
                items: [execUserConfigForm],
                // cls:'customize_panel_bak'
            });

            execUserConfigWindow = Ext.create('Ext.window.Window', {
                title: "配置执行用户",
                modal: true,
                closeAction: 'destroy',
                constrain: true,
                autoScroll: false,
                //upperWin : errorTaskWin,
                width: 370,
                height: 180,
                draggable: false,// 禁止拖动
                resizable: false,// 禁止缩放
                layout: 'fit',
                items: [execUserConfig_mainPanel]
            });
        }
        execUserConfigWindow.show();
    }

    function chosedExecUser(record) {
        $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"), execUserConfigForm.getForm().findField('execUserName').getRawValue());
        if (isSumpAgentSwitch == true) {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getRawValue());
        } else {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getValue());
        }
        record.commit();
        execUserConfigWindow.hide();
    }

    function checkIsNotEmptyAndUndefined(str) {
        if (trim(str) == "" && trim(str) == "undefined")
            return false;
        else
            return true;
    }
});


//
