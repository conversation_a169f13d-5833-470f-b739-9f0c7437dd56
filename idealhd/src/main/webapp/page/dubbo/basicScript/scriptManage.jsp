<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.CommonConfigEnv"%>
<%@ page import="com.ideal.ieai.commons.Constants" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<%
	boolean sendSwitch = Environment.getInstance().getScriptServiceSendSwitch();
	boolean scriptLevelSwitch= Environment.getInstance().getScriptLevelSwitch();
	boolean issuedbuttonSwitch= Environment.getInstance().getIssuedbuttonSwitch();
	boolean  atomicScript= Environment.getInstance().getAtomicScriptSwitch();
	boolean  createScript= Environment.getInstance().getCreateScriptSwitch();
	boolean isSumpAgentSwitch=CommonConfigEnv.isSumpAgentSwitchValue();
	boolean istrySwitch=Environment.getInstance().getScriptTrySwitch();
	boolean isAttach=Environment.getInstance().getScriptAttachmentSwitch();
	boolean issync=Environment.getInstance().getScriptSyncSwitch();
	boolean isProject=Environment.getInstance().getScriptProjectSwitch();
	boolean psbcBindAgent=Environment.getInstance().getScriptPsbcBindAgentSwitch();
	//北京邮储 下发脚本校验md5开关
	boolean scriptIssuedCheckMd5Switch=Environment.getInstance().getIssuedCheckMd5Switch();
	// 批量发布开关 bankCode001
	boolean scriptBatchReleaseSwitch = Environment.getInstance().getScriptBatchReleaseSwitch();
	boolean cibItsmScriptSwitch = Environment.getInstance().getScriptServiceToProductSwitch();
	//光大 福建农信 脚本依赖功能开关
	boolean scriptDenendScriptSwitch = Environment.getInstance().getScriptDenendScriptSwitch();
	//山东标签开关
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	//批量查询开关
	boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
	//按照ip查询开关
	boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
	//bankCode001 脚本目录
	boolean gfScriptDirFunctionSwitch = Environment.getInstance().getGFScriptDirFunctionSwitch();
	// bankCode001 未绑定目录脚本查询 开关
	boolean gfScriptDirUnboundSelectSwitch = Environment.getInstance().getGFScriptDirUnboundSelectSwitch();
   //渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
	//SQL类型脚本显示隐藏
	boolean getScriptSqlShowSwitch = Environment.getInstance().getScriptSqlShowSwitch();
	boolean scriptManageProductionSwitch = Environment.getInstance().getScriptManageProductionSwitch();
	boolean scriptCrossPublishPassSwitch = Environment.getInstance().getScriptCrossPublishPass();
	// hs银行 发布对接
	boolean orderNumberSwitch = StringUtils.isNotEmpty(Environment.getInstance().getHSScriptPublishItsm());
%>
<html>
<head>
	<script type="text/javascript">
		var smnow="";
		if(isTabSwitch){
			$(document).ready(function(){
				$("#scriptService_grid_area").attr('id',$("#scriptService_grid_area").attr('id')+smnow)
			});
		}
	</script>
<script>
var arr =[];
arr = <%=request.getAttribute("atomact")%>;
console.log(arr)
var usenumtj = false;
var db_f_class = <%=request.getAttribute("db_f_class")%>;
var db_s_class = <%=request.getAttribute("db_s_class")%>;
var db_s_level = <%=request.getAttribute("db_s_level")%>;
var db_scriptType = <%=request.getAttribute("db_serviceType")%>;
var db_ssuer = <%=request.getAttribute("db_ssuer")%>;
var shareSwitch = <%=request.getAttribute("shareSwitch")%>;
var importSwitch = <%=request.getAttribute("importSwitch")%>;
//隐藏掉导入按钮
var isSumpAgentSwitch = <%=isSumpAgentSwitch%>;
var istrySwitch = <%=istrySwitch%>;
var scriptBatchReleaseSwitch = <%=scriptBatchReleaseSwitch%>;
var filter_bussId = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeId = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_keywords = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptType = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
var filter_scriptStatus = '<%=request.getParameter("filter_scriptStatus")==null?-10000:request.getParameter("filter_scriptStatus")%>';
var filter_patFromValue ='<%=request.getParameter("filter_patFromValue")==null?"":request.getParameter("filter_patFromValue")%>';
var filter_scriptDir = '<%=request.getParameter("filter_scriptDir")==null?"[]":request.getParameter("filter_scriptDir")%>';
var filter_selectUnboundScript = '<%=request.getParameter("filter_selectUnboundScript")==null?"false":request.getParameter("filter_selectUnboundScript")%>';

var scriptName = '<%=request.getParameter("scriptName1")==null?"":request.getParameter("scriptName1")%>';

var filter_updateUser= '<%=request.getParameter("filter_updateUser")==null?"":request.getParameter("filter_updateUser")%>';
var isAttach=<%=isAttach%>;
var issync=<%=issync%>;
var isProject=<%=isProject%>;
var db_usePlantFormswitch = <%=request.getAttribute("db_usePlantFormswitch")%>;
var db_queryIssueRecordswitch= <%=request.getAttribute("db_queryIssueRecordswitch")%>;
var db_sendScriptswitch= <%=request.getAttribute("db_sendScriptswitch")%>;
var filter_serviceType = '<%=request.getParameter("filter_serviceType")==null?-1:request.getParameter("filter_serviceType")%>';
var db_isshareswitch=<%=request.getAttribute("db_isshareswitch")%>;
var db_useTimesswitch=<%=request.getAttribute("db_useTimesswitch")%>;
var db_winTimesswitch=<%=request.getAttribute("db_winTimesswitch")%>;
var db_versionswitch=<%=request.getAttribute("db_versionswitch")%>;
var db_scriptNameswitch=<%=request.getAttribute("db_scriptNameswitch")%>;
var db_scriptTypeswitch=<%=request.getAttribute("db_scriptTypeswitch")%>;
var db_statusswitch=<%=request.getAttribute("db_statusswitch")%>;
var publishSwitch=<%=request.getAttribute("publishSwitch")%>;
var serviceIdSwitch=<%=request.getAttribute("serviceIdSwitch")%>;
var db_createUserNameSwitch=<%=request.getAttribute("db_createUserNameSwitch")%>;
var db_updateUserNameSwitch=<%=request.getAttribute("db_updateUserNameSwitch")%>;
var gfScriptDirFunctionSwitch=<%=gfScriptDirFunctionSwitch%>;
var gfScriptDirUnboundSelectSwitch=<%=gfScriptDirUnboundSelectSwitch%>;
var showIdClomn = <%=request.getAttribute("showIdClomn")%>;

//是否脚本看板跳转过来的请求 
var requestFromC3Char = <%=request.getParameter("requestFromC3Char")==null?false:request.getParameter("requestFromC3Char")%>;
var filter = {
		'filter_bussId': filter_bussId,
		'filter_bussTypeId': filter_bussTypeId,
		'filter_scriptName': filter_scriptName,
		'filter_keywords': filter_keywords,
		'filter_serviceName': filter_serviceName,
		'filter_scriptType': filter_scriptType,
		'filter_scriptStatus':filter_scriptStatus,
		'filter_serviceType':filter_serviceType,
		'filter_patFromValue':filter_patFromValue,
		'filter_updateUser':filter_updateUser,
		'filter_scriptDir':filter_scriptDir,
		'filter_selectUnboundScript':filter_selectUnboundScript
	};
var sendSwitch = <%=sendSwitch%>;
var scriptLevelSwitch = <%=scriptLevelSwitch%>;
var issuedbuttonSwitch = <%=issuedbuttonSwitch%>;
var atomicScript  = <%=atomicScript%>;
var createScript  = <%=createScript%>;
var scriptIssuedCheckMd5Switch  = <%=scriptIssuedCheckMd5Switch%>;
var cibItsmScriptSwitch  = <%=cibItsmScriptSwitch%>;
var scriptDenendScriptSwitch  = <%=scriptDenendScriptSwitch%>;
var psbcBindAgentSwicht = <%=psbcBindAgent%>;
var filter_keywords  = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
var labelSwitch = <%=sdScriptLabelEditSwitch%>;
var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>
//批量查询开关
var batchQuerySwitch = <%=batchQuery%>;
//按照ip查询还是按照计算机名查询开关（true为山东城商需求，按照ip查询；false为bankCode001需求，按照计算机名查询）
var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
var getScriptSqlShowSwitch=<%=getScriptSqlShowSwitch%>
var scriptManageProductionSwitch = <%=scriptManageProductionSwitch%>
var scriptCrossPublishPassSwitch = <%=scriptCrossPublishPassSwitch%>
var orderNumberSwitch = <%=orderNumberSwitch%>
<%
String bankFlag = Environment.getInstance().getBankSwitch();
     boolean fjFlag=false;
      if((Constants.BANK_FJNX).equals(bankFlag))
      {
          fjFlag=true;
      }
%>
var fjFlag =<%=fjFlag%>; //根据是否为福建农信然后隐藏界面自测按钮
</script>
	<script type="text/javascript"
			src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"> </script>
<script type="text/javascript"
		src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script    type="text/javascript"
		   src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageIPSearch.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/scriptManage.js"></script>
</head>
<body>
<input type="hidden" id="scriptManagePageExecUserNameText" />
<div id="scriptService_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>