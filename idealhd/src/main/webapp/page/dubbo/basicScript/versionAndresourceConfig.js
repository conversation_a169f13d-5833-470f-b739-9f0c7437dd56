Ext.onReady(function() {
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  Ext.define ('subReleaseModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'ibusName',
	                type : 'string'
	            },
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'icreateUser',
	                type : 'string'
	            },
	            {
	                name : 'icreateTime',
	                type : 'string'
	            },
	            {
	                name : 'ibusnDesc',
	                type : 'string'
	            },
	            {
	                name : 'iorder',
	                type : 'string'
	            },
	            {
	                name : 'iparentid',
	                type : 'string'
	            },
	            {
	                name: 'IID',
	                type: 'long'
	            },
	            {
	                name: 'IGNAME',
	                type: 'string'
	            },
	            {
	                name: 'IGDESC',
	                type: 'string'
	            },
	            {
	                name: 'ICREATETIME',
	                type: 'string'
	            },
	            {
	                name: 'IUPDATETIME',
	                type: 'string'
	            }
	            
	    ]
	});
	
	var scriptServiceReleaseLColumns = [
	     {
	            text : 'ID',
	            dataIndex : 'iid',
	            flex : 1,
	            hidden : true
	        },
	        {
	        	
	        	xtype: 'treecolumn', 
	            text : '版本信息',
	            dataIndex : 'ibusName',
	            flex : 1
	        },
	        {
	            text : '创建人',
	            sortable : true,
	            dataIndex : 'icreateUser',
	            flex : 1,
	            hidden : true
	        },
	        {
	            text : '创建时间',
	            sortable : true,
	            dataIndex : 'icreateTime',
	            flex : 1,
	            hidden : true
	        },
	        {
	            text : '版本信息描述',
	            sortable : true,
	            dataIndex : 'ibusnDesc',
	            flex : 1,
	            hidden : true
	        },
	        {
	            text : '排序号',
	            sortable : true,
	            dataIndex : 'iorder',
	            hidden : true
	        },
	        {
	            text : 'iparentid',
	            dataIndex : 'iparentid',
	            flex : 1,
	            hidden : true
	        }
    ];
	var scriptServiceReleaseRColumns = [
	 {
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '资源组名称',
        dataIndex: 'IGNAME',
        flex:1,
        width: 120
    },
	{
        text: '描述',
        dataIndex: 'IGDESC',
        width: 120,
        flex:1,
        hidden : true
    },
    {
        text: '创建时间',
        dataIndex: 'ICREATETIME',
        width: 160,
        hidden : true
    },
    {
        text: '修改时间',
        dataIndex: 'IUPDATETIME',
        width: 160,
        hidden : true
    }
    ,
    {
        text: '资源信息',
        dataIndex: 'IUPDATETIME',
        width: 160,
		renderer:function(value,p,record,rowIndex){
			var IID =  record.get('IID');			
			return ' <div> <a href="javascript:void(0)" onclick="showResourceInfo('+IID+')"> 资源信息 </a>&nbsp;&nbsp;</div>';
		}
    }
];
	
	 var nameField = Ext.create("Ext.form.field.Text", {
	        fieldLabel: '资源组名称',
	        labelWidth: 80,
	        labelAlign: 'left',
	        name: 'groupName',
	        width: '50%'
	    }); 	
	
	var store = Ext.create('Ext.data.Store', {
		autoDestroy: true,
		pageSize: 20,
		model: 'subReleaseModel',
		proxy: {
			type: 'ajax',
			url: 'resGroupListForTree.do?serviceId='+tmp_serviceId+'&tmpServiceGroupId='+tmp_serviceGroupId,
			reader: {
				type: 'json',
				root: 'dataList'
			}
    }
  });	
	var addIds = new Array();
	var removeIds = new Array();
	var initIds = new Array();
	store.addListener('load',function(me, records, success, opts){ 
		var records = [];// 存放选中记录
		for (var i = 0; i < store.getCount(); i++) {
			var record = store.getAt(i);
			if (record.data.checked) {
				var isExists=true;
				for (var j = 0; j < removeIds.length; j++) {
					if (removeIds[j] == record.data.IID) {
						isExists = false;
						break;
					}
				}
				if(isExists){
					records.push(record);
				}
				initIds.push(record.data.IID);
			}else{
				var isExists2=true;
				for (var k = 0; k < addIds.length; k++) {
					if (addIds[k] == record.data.IID) {
						isExists2 = false;
						break;
					}
				}
				if(!isExists2){
					records.push(record);
				}
			}
		}
		selModelp.select(records);// 选中记
	});
	
  store.on('beforeload', function(store, options) {
      var new_params = {
          groupName: nameField.getValue()
      };
      Ext.apply(store.proxy.extraParams, new_params);
  });
  
  
  var choosedStore = Ext.create ('Ext.data.TreeStore',
		{
		    model : 'subReleaseModel',
		    proxy :
		    {
		        type : 'ajax',
		        url : 'versionTreeListForTree.do?serviceId='+tmp_serviceId+'&tmpServiceGroupId='+tmp_serviceGroupId
		    },
		    root : {
	            expanded : true,
	            leaf : false
	        },
	        autoLoad:true
		});
  
	var pageBarRight = Ext.create('Ext.PagingToolbar', {
    	store: store, 
    	dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false
    });  
	queryWhere();	
	var selModelp = Ext.create ('Ext.selection.CheckboxModel',
			{
				checkOnly : true
			});
	
	var form = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : ['->', {
				xtype : 'button',
				text : '绑定',
				cls : 'Common_Btn',
				handler : onSaveListener
			}]
		}]
	});
	
	var unChosedServList = Ext.create('Ext.grid.Panel', {
		    selModel : selModelp,
		    region: 'center',
		    multiSelect: true,
		    split : true,
		    columnLines : true,
		    emptyText: '没有资源信息',
		    store : store,
		    margins : 8,
			border:true,
		    columns : scriptServiceReleaseRColumns,
		    listeners: {
				select : function(selModelp, record, index, eOpts) {
					removeIds.remove(record.data.IID);
					var isExists = true;
					var isExists2 = true;
					if (addIds.length == 0) {
						for (var i = 0; i < initIds.length; i++) {
							if (initIds[i] == record.data.IID) {
								isExists = false;
								break;
							}
						}
						if (isExists) {
							addIds.push(record.data.IID);
						}
					} else {
						for (var j = 0; j < addIds.length; j++) {
							if (addIds[j] == record.data.IID) {
								isExists = false;
								break;
							}
						}
						if (isExists) {
							for (var k = 0; k < initIds.length; k++) {
								if (initIds[k] == record.data.IID) {
									isExists2 = false;
									break;
								}
							}
							if (isExists2) {
								addIds.push(record.data.IID);
							}
						}
					}
				},
				deselect : function(roleSelModel, record, index, eOpts) {
					addIds.remove(record.data.IID);
					var isExists = true;
					var isExists2 = true;
					if (removeIds.length == 0) {
						for (var i = 0; i < initIds.length; i++) {
							if (initIds[i] == record.data.IID) {
								isExists = false;
								break;
							}
						}
						if (!isExists) {
							removeIds.push(record.data.IID);
						}
					} else {
						for (var j = 0; j < removeIds.length; j++) {
							if (removeIds[j] == record.data.IID) {
								isExists = false;
								break;
							}
						}
						if (isExists) {
							for (var k = 0; k < initIds.length; k++) {
								if (initIds[k] == record.data.IID) {
									isExists2 = false;
									break;
								}
							}
							if (!isExists2) {
								removeIds.push(record.data.IID);
							}
						}
					}
				}
	        }
	  	});
	
	 
	var unform = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [nameField,
			            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                	QueryMessage();
                }
            },]
		}]
	});
  
	var unPanel = Ext.create('Ext.panel.Panel',{
//    	layout : 'fit',
		title:'资源组信息',
		layout: 'border',
		border:false,
		region : 'center',
		width : '58%', 
		height:contentPanel.getHeight(),
		bbar: pageBarRight,
		items : [unform,unChosedServList]
    });
	
	
  var ChosedServList = Ext.create('Ext.tree.Panel', {
	    store : choosedStore,
	    /*selModel : Ext.create ('Ext.selection.CheckboxModel'),*/
        useArrows: true,  
        rootVisible: false,  
        containerScroll: true,
        multiSelect: false,
        selType: 'rowmodel',
        viewConfig: {
            stripeRows: true
        },
        enableDD: false,
        checked:true,
        columns : scriptServiceReleaseLColumns,
        columnLines : true,
        margins : 8,
		border:true
		/*,
        listeners:{
	        'checkChange':function(node,checked){
				setPnode(node, checked);
				setCnode(node, checked);
	    	}
        }*/
	  });
  
	var usePanel = Ext.create('Ext.panel.Panel',{
		layout : 'fit',
		border:false,
		title:'资源版本信息',
		region : 'west',
		width : '42%', 
		height:contentPanel.getHeight(),
		//bbar: pageBarRight,
		items : [ChosedServList]
   });
//父节点选中
  function setPnode(node, checked) {
       if (node.parentNode) {
           var flg = 0;
           node.parentNode.eachChild(function (child) {
               if (child.get('checked')) {
                   node.parentNode.set('checked', true);
               } else {
                   flg++;
                   if (flg == node.parentNode.childNodes.length) {
                       node.parentNode.set('checked', false);
                   }
               }
           });
           setPnode(node.parentNode, checked);
       }
   }
   //子节点被选中
  function setCnode(node, checked) {
	  if (node) {
           node.eachChild(function (child) {
               child.set('checked', checked);
               setCnode(child, checked);
           });
       }
   }

  var mainPage_S = Ext.create('Ext.panel.Panel', {
	    width : '100%',
	    height:contentPanel.getHeight(),
	    layout : 'border',
	    header : false,
	    border : false,
	    items : [ form,usePanel, unPanel ],
	    renderTo : "scripttempService_dev"
	  });
  
  contentPanel.on('resize',function(){
	  mainPage_S.setWidth('100%');
	  mainPage_S.setHeight(contentPanel.getHeight()-298);
  });
  
  contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
  	Ext.destroy(mainPage_S);
		if(Ext.isIE){
      	CollectGarbage(); 
  	}
  });
  
  function queryWhere(){
//		store.load();
		pageBarRight.moveFirst();
  }
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
//数组是否包含指定元素
containsF = function(array,suArr){
      for(var i = 0; i < array.length; i ++){  
          if(array[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };
  
  function onSaveListener(){
	  onSaveLeftScreen();
  }
  function onSaveLeftScreen(){
	  var leftDrops = [];	
	  var rightDrops = [];
	  var record = ChosedServList.getChecked();
	  Ext.each(record, function (node) {
	         if (node.data) {
	        	 leftDrops.push(node.data.iid+"##"+node.isLeaf());
	        	 leftDrops.join(',')
	         }
	  });
	  var unrecord = unChosedServList.getSelectionModel().getSelection();
	  
	  if(unrecord.length!=0){
		Ext.each(unrecord,function(item){
			var iid = item.get('IID');
			if(iid != 0){
				rightDrops.push(iid);
				rightDrops.join(',')
			}
		});
	  }
		var jsonRightData= Array.union(addIds,initIds);
		for(var i=0;i<removeIds.length;i++){
			console.log(containsF(jsonRightData,removeIds[i]));			
			if(containsF(jsonRightData,removeIds[i])){
				jsonRightData.remove(removeIds[i]);
			}
		}
	  
	  Ext.Ajax.request({
	      url : 'saveVersionAndResource.do',
	      method : 'POST',
	      params : {
	    	jsonLeftData : leftDrops.join (','),
//	    	jsonRightData: rightDrops.join(','),
	    	jsonRightData : jsonRightData.join(','),
	        serviceId:tmp_serviceId,
	        tmpServiceGroupId:tmp_serviceGroupId
	      },
	      success : function(response, request) {
	        var success = Ext.decode(response.responseText).success;
	        if (success) {
	            store.reload();
	            choosedStore.reload();
	            Ext.Msg.alert('提示', '操作成功执行');
	          } else {
	            Ext.Msg.alert('提示', '绑定操作失败！');
	          }
	      },
	      failure : function(result, request) {
	        Ext.Msg.alert('提示', '保存失败');
	      }
	    });
  
  }
  
  function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBarRight.moveFirst();
	}
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
});
var showResourceConfig; 
function showResourceInfo(IID){
	showResourceConfig = Ext.create('Ext.window.Window', {
	    title: '详情',
	    height: '80%',  //Number型  也可以是字符串类型  width:'60%'
	    width: '70%',
	    layout: 'fit',
	    constrain: true, 		//闲置窗口不超出浏览器
	    constrainHeader:true, 	//标题不能超出浏览器边界
	    modal: true,			//设置模态窗口
	    plain:true, 			//窗口设置透明背景
	    draggable: false,
	    resizable: false,
	    loader: {
			url: 'groupConfig.do',
			params : {
				IID : IID,
				flag:1
			},
			autoLoad: true,
			scripts: true
		}
	//,
	    //autoScroll:true //显示滚动条
	});
	showResourceConfig.show();
}
