Ext.onReady(function() {
	var dbIds={};
	var sysID;
	var busID;
	var DetailWinTi;
    // 清理主面板的各种监听时间
//    destroyRubbish();
	var phoneNum;
    var uploadProcessWin; 
    var attachmentIds = [];
    var startData={};
    var globalParams={};
    var finalChosedAgentsAndDbSources = {};
    var cpdsMap = {};//<cpid,dsid>
    var selCpId = -1;
    var chosedAgentWin;
	var chosedAgentIds = new Array();
	var upldWin;
	var upLoadformPane = '';
	var cpdsMap = {};//<cpid,dsid>
    var selCpId = -1;
  //var allinfoIdStr = "";
  //var map = {};  
  ////put  
  //var key = "key1";  
  //var value = "value1";  
  //map[key] = value;  
  ////get  
  //alert(map[key]);  
  //
  //if("key1" in map) { //判断是否存在  
  //alert("OK");  
  //}  
  ////删除  
  //delete map["key1"];   
  ////遍历  
  //for(key in map){  
  // alert(key + map[key]);  
  //}

    Ext.tip.QuickTipManager.init();

    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
    	fields: ['sysTypeId', 'sysType'],
    	autoLoad: false,
    	proxy: {
    		type: 'ajax',
    		url: 'bsManager/getBsTypeByFk.do',
    		reader: {
    			type: 'json',
    			root: 'dataList'
    		}
    	}
    });
    var dbsourceCHKStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        queryMode: 'local',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    bussData.on('load', function(store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load', function(store, options) {
        bussTypeCb.setValue(busID);
    });
    
    var execDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '',
        columnWidth: 1,
        margin:'0 10 10 10',
        height: 95,
        autoScroll: true
    });
    
    var execDescForm = Ext.create('Ext.form.Panel', {
        width: '50%',
        height: 300,
        border: true,
        title: '执行描述',
        cls:'window_border panel_space_top panel_space_left panel_space_right',
        items: [{
            layout: 'column',
            border: false,
            items: [execDesc]
        }]
    });

    Ext.define('editScriptModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'excepResult',
            type: 'string'
        },
        {
            name: 'errExcepResult',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        }]
    });
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
            iid: iidForTaskAudi,
            fromType: 1 // 查ieai_script_services表
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
    });
    /** *********************Panel********************* */

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        fieldLabel: '功能概述',
        displayField: 'funcdesc',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1,
        height: 130,
        autoScroll: true
    });
    var scriptForm = Ext.create('Ext.form.Panel', {
        width: '20%',
        height: 230,
        border: true,
        layout: 'anchor',
        title: '基本信息',
        items: [{
            border: false,
            layout: 'column',
            margin: '5',
            items: [bussCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [bussTypeCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [sName]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [scName]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [errExcepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDesc]
        }]
    });

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },{
            name: 'paramValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: scriptuuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 60,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '参数默认值',
        dataIndex: 'paramDefaultValue',
        width: 80,
        editor: {
            allowBlank: true
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },{
        text: '参数值',
        dataIndex: 'paramValue',
        width: 100,
        editor: {
            allowBlank: true
        },
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    
    var paramGrid = Ext.create('Ext.grid.Panel', {
    	title: "参数",
        width: '50%',
        cls:'window_border panel_space_top panel_space_left panel_space_right',
//        height: 280,
//        margin: 10,
//        collapsible : true,
        store: paramStore,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: iidForTaskAudi,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    
    var attachmentColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }/*,
    {
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    }*/];
    
    
    
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	title: "附件",
        width: '50%',
//        height: contentPanel.getHeight()-362,
//        margin: 10,
        store: attachmentStore,
        border: true,
        columnLines: true,
        columns: attachmentColumns
    });
    
    var paramsAndAttachmentsPanel = Ext.create('Ext.panel.Panel', {
         collapsible : false,
        border: false,
//        title: "参数与附件",
        height: 180,
        layout: {
            type: 'hbox',
//            padding:'5',
            align:'stretch'
        },
        items: [paramGrid, execDescForm/*attachmentGrid*/]
    });

    /*var mainP = Ext.create('Ext.panel.Panel', {
        width: '33.7%',
        border: false,
        title: "脚本内容",
        // height : contentPanel.getHeight()-120,
        height: contentPanel.getHeight()-200,
        tbar:[FieldContainer],
        html: '<textarea id="codeEditView-for-auditing" value style="width: 100%;height:100%;"></textarea>'
    });*/
    
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	/*resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  */
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    labelAlign : 'right',
			    labelWidth : 65,
			    width : '24%',
	            columnWidth:1,
	            hidden:removeAgentSwitch,
			    multiSelect: true,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
		    	listeners: {
	    	      change: function( comb, newValue, oldValue, eOpts ) {
	    	    	  agent_store.load();
	    	      }
		    	}
	});
	
	var app_name = new Ext.form.TextField({
		name : 'appname',
		fieldLabel : '应用名称',
		displayField : 'appname',
		emptyText : '--请输入应用名称--',
		labelWidth : 65,
		hidden : true,
		labelAlign : 'right',
		width : '20%'
	});
	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'AgentIp',
		displayField : 'agentip',
		emptyText : '--请输入agentip--',
		labelWidth : 65,
		labelAlign : 'right',
		hidden :true,
		width : '23%'
	});
	var ipStart = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 79,
	     fieldLabel : '起始IP',
	    emptyText : '--请输入开始IP--',
	    //labelSeparator : '',
	    width : '23.2%',
	    labelAlign : 'right'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 65,
	    fieldLabel : '终止IP',
	    emptyText : '--请输入截止IP--',
	    //labelSeparator : '',
	    labelAlign : 'right',
	    width : '23.2%'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '计算机名',
		displayField : 'hostname',
		emptyText : '--请输入计算机名--',
		labelWidth : 65,
		labelAlign : 'right',
		width : '25.2%'
	});
	var sysName1 = new Ext.form.TextField({
        name: 'sysName1',
        fieldLabel: '名称',
        displayField: 'sysName1',
        emptyText: '--请输入名称--',
        labelWidth: 65,
        labelAlign: 'right',
        width :'26%',
        listeners:{
	        specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		    }
	    }
    });
	var sys_name = new Ext.form.TextField({
		name : 'sysname',
		fieldLabel : '名称',
		displayField : 'sysname',
		emptyText : '--请输入名称--',
		labelWidth : 65,
		hidden:cmdbFlag,
		labelAlign : 'right',
		width : '23%'
	});
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '操作系统',
		displayField : 'ostype',
		emptyText : '--操作系统--',
		labelWidth : 65,
		labelAlign : 'right',
		width : '23.2%'
	});
	
	var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 79,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '23.5%',
		labelAlign : 'right',
		listeners:{
		        specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			    }
		    }
	});
	var selectAll = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '全选',
        inputValue: 1,
        width: 50,
        hidden:taskApplyForSPDBSwitch,
        margin: '10 0 0 10'
    });
	
	var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		border : false,
		cls:'window_border panel_space_left panel_space_right',
		iqueryFun : function(){
			pageBar.moveFirst();
	    },
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [ sys_name, app_name, host_name, os_type,ipStart,ipEnd
			]
		},
		{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [ /*agent_ip,*/sysName1, resourceGroupObj, agentStatusCb,selectAll,
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function(){
						pageBar.moveFirst();
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function(){
						agent_ip.setValue('');
						ipEnd.setValue('');
						ipStart.setValue('');
				    	app_name.setValue('');
						sys_name.setValue('');
						sysName1.setValue('');
						host_name.setValue('');
						os_type.setValue('');
				    	resourceGroupObj.setValue('');
				    	agentStatusCb.setValue('');
					}
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '导入',
					handler : importExcel
				}
			]
		}]
	});
	 function checkFile(fileName){
		    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
		    if(!file_reg.test(fileName)){  
		    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
		    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
		        return false;
		    }
		    return true;
		}
	function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:100,
		    frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 65,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: [
					{
						id:'upldBtnIdAudi',
						text: '导入Agent文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
			    			if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}

							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'importAgentForStart.do',
									params:{
										envType:1
				                	},
								    success: function(form, action) {
								       var msg = Ext.decode(action.response.responseText).message;
								       
								    	   var status = Ext.decode(action.response.responseText).status;
								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
								    	   
								    	   if(status==1) {
								    		   if(matchAgentIds && matchAgentIds.length>0) {
								    			   Ext.MessageBox.buttonText.yes = "确定"; 
								    				Ext.MessageBox.buttonText.no = "取消"; 
								    			   Ext.Msg.confirm("请确认", msg, function(id){
										  				 if(id=='yes'){
									  						Ext.Msg.alert('提示', "导入成功！");
									  						agent_ip.setValue('');
													    	app_name.setValue('');
															sys_name.setValue('');
															host_name.setValue('');
															os_type.setValue('');
													    	resourceGroupObj.setValue('');
													    	agentStatusCb.setValue('');
													    	chosedAgentIds = matchAgentIds;
												    	   pageBar.moveFirst();
										  				 }
										    		   });
								    		   } else {
								    			   Ext.Msg.alert('提示-没有匹配项', msg);
								    		   }
								    		   
								    	   } else {
								    		   Ext.Msg.alert('提示', "导入成功！");
									    	   agent_ip.setValue('');
										    	app_name.setValue('');
												sys_name.setValue('');
												host_name.setValue('');
												os_type.setValue('');
										    	resourceGroupObj.setValue('');
										    	agentStatusCb.setValue('');
										    	chosedAgentIds =  matchAgentIds;
									    	    pageBar.moveFirst();
									    	    agent_store_chosed.load();
			                                    chosedAgentWin.close();
								    	   }
								    	   
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}, {
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '设备信息批量导入',
		    width: 400,
		    height: 140,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}

	var dbssourceCHKObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : 'dbs',
			    labelAlign : 'right',
			    labelWidth : 65,
			    width : '92.3%',
	            columnWidth:1,
			    multiSelect: true,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
		    	listeners: {
	    	      change: function( comb, newValue, oldValue, eOpts ) {
	    	    	  agent_store.load();
	    	      }
		    	}
	});
	var dbsourceForm = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [dbssourceCHKObj]
		}]
	});
    
	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
    
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	Ext.define('dbModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',        type: 'string'},
            {name: 'driverClass',type: 'string'},
            {name: 'dbUrl',      type: 'string'},
            {name: 'dbUser',     type: 'string'},
            {name: 'dbType',     type: 'string'}
        ]
    });
    var dbinfo_store = Ext.create('Ext.data.Store', {
    	model:'dbModel',
    	autoLoad: false,
    	proxy: {
    		type: 'ajax',
    		url: 'getDbSqlDriverInfo.do',
    		reader: {
    			type: 'json',
    			root: 'dataList'
    		}
    	}
    });
    var agent_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: '名称',  dataIndex: 'sysName',width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                            return value;  
                        }},
                        { text: 'IP',  dataIndex: 'agentIp',width:100,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                            return value;  
                        }},
                        { text: '计算机名',  dataIndex: 'hostName',width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                            return value;  
                        }},
                        { text: '操作系统',  dataIndex: 'osType',width:80},
                        { text: '端口号',  dataIndex: 'agentPort',width:80},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,
                        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }
                        },
		                { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ];
    var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
                         { text: '主键',  dataIndex: 'iid',hidden:true},
                         { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                             metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                             return value;  
                         }},
                         { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                             metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                             return value;  
                         }},
                         { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
                         { text: 'DB类型',  dataIndex: 'dbType',width:110}];
    
    agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp : agent_ip.getValue(),
            startIp :ipStart.getValue().trim(),
            agentState: agentStatusCb.getValue(),
	    	endIp :ipEnd.getValue().trim(),
	    	appName : app_name.getValue(),
	    	sysName: CMDBflag?(sys_name.getValue() == null ? '': Ext.util.Format.trim(sys_name.getValue() + "")):Ext.util.Format.trim(sysName1.getValue()),
			hostName : host_name.getValue(),
			osType : os_type.getValue(),
	    	rgIds:resourceGroupObj.getValue(),
	    	flag: 1
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
	Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
	var agent_storeAll = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListAll.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	var agent_store_chosed = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 30,
		model: 'agentModel',
		proxy: {
            type: 'ajax',
            url: 'getAgentChosedList.do',
            actionMethods: {  
                create : 'POST',  
                read   : 'POST', // by default GET  
                update : 'POST',  
                destroy: 'POST'  
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
	});
    agent_store_chosed.on('beforeload', function (store, options) {
    	var new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds)
    	};
    	
    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    agent_store.on('load', function (store, options) {
    	var records=[];//存放选中记录
	  for(var i=0;i<agent_store.getCount();i++){
	      var record = agent_store.getAt(i);
	      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
    	    {   
	    	  
	    	  if((+chosedAgentIds[ii])==record.data.iid)
	    		  {
	    		  records.push(record);
	    		  }
    	    }   
	  }
	  agent_grid.getSelectionModel().select(records, false, true);//选中记录
    });
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : agent_store,
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		dock : 'bottom',
		displayInfo : true
	});

    
    // 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
//			selectionchange : function(selModel, selections) {
//			},
			select:function(selModel, record, index, eOpts) {
				 dbinfo_store.load({
			            params: {
			            	agentId: record.get("iid"),
			                agentIp: record.get("agentIp"),
			                agentPort: record.get("agentPort")
			            }
			        });
				 //当前选中cpid
				 selCpId = record.get("iid");
			 },
			 deselect:function(selModel, record, index, eOpts) {
				 dbinfo_store.removeAll();
				 var cpid = record.get("iid");
				 cpdsMap[cpid] = -1;//清空
//				 delete cpdsMap[cpid];
				 selCpId = -1;
			 }
		}
	});
    var agent_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	width: '50%',
	    store:agent_store,
	    bbar : pageBar,
	    cls:'window_border panel_space_top panel_space_left panel_space_right',
	    border:true,
	    columnLines : true,
//	    rowLines: false,
	    columns:agent_columns,
//	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    selModel:selModel,
	    listeners: {
	        select: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.push(record.get('iid'));
            	}
            },
	        deselect: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
            		chosedAgentIds.remove(record.get('iid'));
            	}
            }
	    }
});
var pageBarForAgentChosedGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	store : agent_store_chosed,
	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	dock : 'bottom',
	displayInfo : true
});
var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
	checkOnly : true,
	listeners : {
		select:function(selModel, record, index, eOpts) {
			 dbinfo_store.load({
		            params: {
		            	agentId: record.get("iid"),
		                agentIp: record.get("agentIp"),
		                agentPort: record.get("agentPort")
		            }
		        });
			 //当前选中cpid
			 selCpId = record.get("iid");
		 },
		 deselect:function(selModel, record, index, eOpts) {
			 dbinfo_store.removeAll();
			 var cpid = record.get("iid");
			 cpdsMap[cpid] = -1;//清空
			 selCpId = -1;
		 }
	}
});

    // 定义复选框
	var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		mode : "SINGLE",
		listeners : {
//			selectionchange : function(selModel, selections) {
//			},
//			beforedeselect:function(selModel2, record, index, eOpts) {
//				var len = selModel2.getSelection();
//				if(len==1)
//				{
//					Ext.MessageBox.alert("提示", "不能没有数据源信息!");
//					return false;
//				}
//			},
			select:function(selModel2, record, index, eOpts) {
				var dsid = record.get("iid");
				if(selCpId != -1)
				{
					cpdsMap[selCpId] = dsid;//绑定
				}
			},
			deselect:function(selModel2, record, index, eOpts) {
				cpdsMap[selCpId] = -1;//清空
			}
		}
	});
    var dbsource_grid = Ext.create('Ext.grid.Panel', {
    	store:dbinfo_store,
//    	 region: 'center',
    	 width: '40%',
    	border:true,
    	columnLines : true,
    	cls:'window_border panel_space_top panel_space_right',
    	columns:dbsource_columns,
//    	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})
    	selModel:selModel2
    });
    
    
    var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
    	title: '已选服务器',
    	region : 'west',
    	store:agent_store_chosed,
    	cls:'window_border panel_space_top panel_space_left panel_space_right',
    	border:true,
    	width:'60%',
    	columnLines : true,
    	height: 300,
    	emptyText: '没有选择服务器',
    	columns:agent_columns,
    	selModel:selModelForagent_grid_chosed,
    	bbar : pageBarForAgentChosedGrid,
    	dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [{
				xtype : 'button',
				cls :'Common_Btn',
				text : '删除',
				handler : function() {
					var records = agent_grid_chosed.getSelectionModel().getSelection();
					if(records.length>0) {
	  					for(var i = 0, len = records.length; i < len; i++){
	  						chosedAgentIds.remove(records[i].get('iid'));
	  					}
	  					pageBarForAgentChosedGrid.moveFirst();
	  					pageBar.moveFirst();
	  				} else {
	  					Ext.Msg.alert('提示', "请选择服务器！");
                        return;
	  				}
				}
			},
			{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加服务器',
				handler : function(){
					if(!chosedAgentWin) {
						chosedAgentWin = Ext.create('Ext.window.Window', {
					  		title : '增加服务器',
					  		autoScroll : true,
					  		modal : true,
					  		resizable : false,
					  		closeAction : 'hide',
					  		layout: 'border',
					  		width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
					  		items:[search_ip_form, agent_grid],
					  		dockedItems: [{
					            xtype: 'toolbar',
					            dock:'bottom',
					            layout: {pack: 'center'},
						        items: [{ 
						  			xtype: "button",
						  			text: "确定", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				var isSelectAll = selectAll.getValue();
						  				if(isSelectAll){
						  					agent_storeAll.load({
						  						 params : {  
						  							agentIp : agent_ip.getValue(),
						  					    	startIp :ipStart.getValue().trim(),
						  					    	agentState: agentStatusCb.getValue(),
						  					    	endIp :ipEnd.getValue().trim(),
						  					    	appName : app_name.getValue(),
						  					    	sysName: CMDBflag?(sys_name.getValue() == null ? '': Ext.util.Format.trim(sys_name.getValue() + "")):Ext.util.Format.trim(sysName1.getValue()),
						  							hostName : host_name.getValue(),
						  							osType : os_type.getValue(),
						  					    	rgIds:resourceGroupObj.getValue()
						  						 },
						  						callback : function(r, options, success) {
						  							agent_storeAll.each(function(record) { 
								  						chosedAgentIds.push(record.get('iid'));
								  					}); 
								  					agent_store_chosed.load();
								  					chosedAgentWin.close();
						  						}
						  					});
						  				}else{
						  					agent_store_chosed.load();
						  					this.up("window").close();
						  				}
						  			}
						  		 },{ 
						  			xtype: "button",
						  			text: "关闭", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				this.up("window").close();
						  			}
						  		 }]
					  		}]
					  	});
					}
					chosedAgentWin.show();
					agent_store.load();
				}
			} ]
		}]
    });
    var agentAndSourcePanel = Ext.create('Ext.panel.Panel', {
        collapsible : false,
        region: 'center',
       border: false,
       height: 300,
       layout: {
           type: 'hbox',
           align:'stretch'
       },
       items: [agent_grid_chosed, dbsource_grid/*attachmentGrid*/]
   });
    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
    	title: "选择服务器",
        border: true,
        layout : 'border',
        height: 300,
        items: [search_ip_form, agentAndSourcePanel]
    });
    
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }, {
		      name : 'phoneNum',
		      type : 'string'
		 }]
	  });
	
	var auditorStore_tap = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecAuditorList.do?scriptLevel='+scriptLevelForTaskAudi,
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	var groupLeader ;
	Ext.Ajax.request({
            url: 'getGroupLeader.do',
            method: 'POST',
            async: false,
            success: function(response, options) {
            	groupLeader = Ext.decode(response.responseText).leader;
            },
            failure: function(result, request) {
            }
        });
	var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
	    editable: false,
	    fieldLabel: "审核人",
	    store: auditorStore_tap,
	    id:'auditorComBox_tap',
	    queryMode: 'local',
	    width: "22.5%",
	    displayField: 'fullName',
	    valueField: 'loginName',
//	    hidden:scriptLevelForTaskAudi==0?true:false,
//	    hidden:true,
	    hidden:scriptOddNumberSwitch?true:false,
	    labelWidth : 65,
		labelAlign : 'right',
		listeners: { //监听 
	        render : function(combo) {
	        	if(scriptLevelForTaskAudi==0){
	        		combo.setValue(groupLeader);
	        	}else{//渲染 
		            combo.getStore().on("load", function(s, r, o) { 
		            combo.setValue(r[0].get('loginName'));//第一个值 
		            phoneNum = r[0].get('phoneNum');
	        	    validCode.setValue(phoneNum);
		            }); 
	        	}
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
				phoneNum =records[0].raw.phoneNum;
				validCode.setValue(phoneNum);
			},
			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_tap.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_tap.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_tap.setValue("");
//					 	return;
//					} 
//				}
			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }  
	    }
	  });
	var execStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecUserList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	var execComBox_tap = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "执行人",
	    store: execStore,
	    queryMode: 'local',
	    width: "18.1%",
	    padding: '0 2 0 8',
	    displayField: 'fullName',
	    valueField: 'loginName',
	    labelWidth : 65,
	    editable : true,
	    hidden:!execUserSwitch || scriptLevelForTaskAudi==0?true:false,
		labelAlign : 'right'
	  });
	execStore.load({
	    callback : function (records, operation, success)
	    {
	    	execComBox_tap.setValue (loginUser);
	    }
    });
	
//	var execTime_sm = Ext.create('Go.form.field.DateTime',{
//	    fieldLabel:'执行时间',
//	    format:'Y-m-d H:i:s',
//	    labelWidth : 58,
//	    width:'23%',
////	    margin : '10 0 0 0',
//	    labelAlign : 'right'
//    });
//	var execTime_sm = new Ext.form.TextField (
//	{
//	    fieldLabel : '执行时间',
//	    labelWidth : 58,
//	    labelAlign : 'right',
//	    width : '23%'
//	});
	
    var isTimerTask = Ext.create('Ext.form.field.Checkbox', {
//    	{
//	            	xtype:'checkboxfield',
//	            	boxLabel: '定时任务',
//	            	name      : 'isDelay',
//	            	id : 'isDelay',
//	            	labelWidth : 58,
//	        	    width:'10%',
//	        	    
//	                checked:false
//	            }
    	    checked : true,
        	boxLabel: '定时任务',
    		//margin:'3 5 0 5',
    		name      : 'isDelay',
	        id : 'isDelay',
	        labelAlign : 'right',
    		listeners: { //监听 
				       change:function(el,checked){
				             if(checked){
				             	cycleExecCronText.show(); 
				             	selectCronButton.show();
				             }else{
				             	cycleExecCronText.hide();
				             	selectCronButton.hide();
				             }
				        }
		    }
        });
    
     /** 选择生成周期表达式按钮 **/
		var selectCronButton = Ext.create ("Ext.Button",
		{
			id : 'selectCronButton_id',
		    cls : 'Common_Btn',
		    text : "选择",
		    hidden: !(isTimerTask.getValue()),
		    handler : selectExecCron
		});
		
		function selectExecCron()
		{
			var creatCronWin;
			if (creatCronWin == undefined || !creatCronWin.isVisible()) {
				creatCronWin = Ext.create('Ext.window.Window', {
					title : '定时任务参数设置',
					modal : true,
					id : 'creatCronWin',
					closeAction : 'destroy',
					constrain : true,
					autoScroll : true,
					upperWin : creatCronWin,
					width : contentPanel.getWidth() - 350,
					height : contentPanel.getHeight() - 30,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					loader : {
						url : 'cronMainForSpdb.do',
	//					params : {
	//						sysType : sysType,
	//						state : state,
	//						errorTaskId : errorTaskId,
	//						pageType : pageType
	//					},
						autoLoad : true,
						autoDestroy : true,
						scripts : true
					}
				});
			}
			creatCronWin.show();
		}    
    
	var cycleExecCronText = new Ext.form.TextField (
			{
			    fieldLabel : '执行时间',
			    labelWidth : 65,
			    labelAlign : 'right',
			    id : 'cycleExecCronText',
				name: 'cycleExecCronText',
			    width : '18%',
			    readOnly : true,
			    hidden:   !(isTimerTask.getValue())
    });
			
	var execUser = new Ext.form.TextField({
        name: 'execUser',
        fieldLabel: '启动用户',
        emptyText: '',
        labelWidth : 65,
        value:suUser,
		labelAlign : 'right',
        width: "18.4%"
    });
	function CurentTime()
    { 
        var now = new Date();
        
        var year = now.getFullYear();       //年
        var month = now.getMonth() + 1;     //月
        var day = now.getDate();            //日
        
        var hh = now.getHours();            //时
        var mm = now.getMinutes();          //分
        var ss = now.getSeconds();           //秒
        
        var clock = year;
        
        if(month < 10){
        	
        	clock += "0";
        }else{
        	clock += "";
        }
        
        clock +=month;
        
        if(day < 10){
        	clock += "0";
        }else{
        	clock += "";
        }
            
            
        clock += day;
        
        if(hh < 10){
        	clock += "0";
        }else{
        	clock += "";
        }
            
            
        clock += hh;
        if (mm < 10) 
        {
        	clock += '0'; 
        	}
        else{
        	clock += "";
        }
        clock += mm ; 
         
        if (ss < 10) {
        	clock += '0'; 
        }
        else{
        	clock += "";
        }
        clock += ss; 
        return(clock); 
    }
	var taskName = new Ext.form.TextField({
		name: 'taskName',
		fieldLabel: '任务名称',
		emptyText: '',
		labelWidth : 65,
		value:serviceNameForTaskAudi+'_'+CurentTime(),
		labelAlign : 'right',
		width: "22.1%"
	});
	
	var eachNum = new Ext.form.NumberField({
		name: 'eachNum',
		fieldLabel: '并发数量',
		labelWidth : 65,
		labelAlign : 'right',
		minValue:0,
		value:number,
		width: "18.1%"
	});
	
	 var isSaveTemplateCk =new  Ext.form.Checkbox({ 
	 		checked : false,
	 		boxLabel: '是否保存为常用任务',
	 		hidden:true,
	 		margin:'0 5 0 5'
	 	});
	
	 var validCode = Ext.create ('Ext.form.TextField',
				{
					fieldLabel:'手机号',
					labelWidth : 65,
					columnWidth: 150,
					readOnly: true,
					id : 'myvalidCode',
					labelAlign : 'right',
					hidden :(scriptLevelForTaskAudi!=0 && audiCodeSwitch && smsSwitchNum!='0')?false:true,
					regex: /^[1][3,4,5,7,8][0-9]{9}$/,
				    xtype : 'textfield'
				});
			
		var authCode = Ext.create ('Ext.form.TextField',
			{
				fieldLabel:'验证码',
				labelWidth: 65,
				id : 'mauthCode',
				hidden :(scriptLevelForTaskAudi!=0 && audiCodeSwitch && smsSwitchNum!='0')?false:true,
				labelAlign : 'right',
				width: 200,
				emptyText : '--请填写验证码--',
				regex: /^\d{6}$/,
			    xtype : 'textfield'
			});
	 			
		var getValidCodeButton   = Ext.create('Ext.Button', 
			{columnWidth: .23,height:33, name : 'getValidCodeButton',cls: 'Common_Btn',hidden :(scriptLevelForTaskAudi!=0 && audiCodeSwitch && smsSwitchNum!='0')?false:true,id:'getValidCodeButton', text : '获取验证码', handler :  getValidCode
		 });
		function isPoneAvailable(pone){
			var myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
		    if (!myreg.test(pone)) {
		      return false;
		    } else {
		      return true;
		    }
		}
		var countdown=10;
		function settime(){
		 if (countdown == 0) {
			 	Ext.getCmp('getValidCodeButton').setDisabled(false);
		        countdown = 10;
		        return false;
		    } else {
		    	 Ext.getCmp('getValidCodeButton').setDisabled(true);
		    	 countdown--;
		    }
		 //显示验证码框
		    setTimeout(function() {
		    	settime();
		    },1000);
		}
		function getValidCode(){
			var phonenum = Ext.getCmp('myvalidCode').getValue();
			if(!isPoneAvailable(phonenum)){
				Ext.MessageBox.alert("提示", "请填写正确的手机号！");
				return;
			}
			var auditor  =auditorComBox_tap.getValue();
			if(!auditor) {
				Ext.Msg.alert('提示', "请选择审核人！");
				return;
			}
			var taskN = Ext.util.Format.trim(taskName.getValue());
			settime();
			//显示输入验证码框
			//Ext.getCmp('mauthCode').show();
			//验证码入库保存，组织验证码数据发送给短信接口，发送成功
			Ext.Ajax.request({
				url : 'sendAuthCode.do',
				params : {
					telephoneNum :phonenum ,
					iauditUser :auditor,
					stepId:'-1',
					planId:'-1',
					iscenceId:'-1',
					serviceId: iidForTaskAudi,
					planName:serviceNameForTaskAudi,
					isscript:'0',
					proType:'10',
					systemId:'-1',
                                        menuName: '定时配置[sql]',
                                        operationType: '任务申请审核',
                                        taskName: taskN
				},
				method : 'post',
				success : function(response, text) {
					    var success = Ext.decode(response.responseText).success;
					    var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.show({
							title : '提示',
							msg : " "+message ,
							buttons : Ext.MessageBox.OK
						});
					} else {
						Ext.Msg.show({
							title : '提示',
							msg : message,
							buttons : Ext.MessageBox.OK
						});
					}
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		}
		
		var listStore = Ext.create("Ext.data.Store", {
		    fields: ["listName", "listValue"],
		    data: [
		        { listName: "日常变更", listValue: 0 },
		        { listName: "重大变更", listValue: 1 },
		        { listName: "维护", listValue: 2 },
		        { listName: "后补", listValue: 3 }
		    ]
		});
		
		listComBox2 = Ext.create ('Ext.form.ComboBox',
				{
				    editable : false,
				    fieldLabel : "操作类型",
				    labelWidth : 65,
				    hidden:(scriptLevelForTaskAudi==0||!scriptOddNumberSwitch)?true:false,
//				    hidden:(scriptLevelForTaskAudi==0)?true:false,
				    store : listStore,
				    value:scriptOddNumberSwitch?'日常变更':'后补',
//				    value : '日常变更',
				    queryMode : 'local',
				    listeners:{
			            //通过change触发
			            change: function(){
			            	if(listComBox2.getRawValue() == '后补')
			            	{
			            		Ext.getCmp('butterflyVerison').hide();
			            		Ext.getCmp('auditorComBox_tap').show();
//			            		butterflyVerison.hide();
			            		auditorComBox_tap.show();
			            	} 
			            	else if(listComBox2.getRawValue() == '日常变更')
			            	{
			            		Ext.getCmp('butterflyVerison').show();
			            		Ext.getCmp('auditorComBox_tap').hide();
			            		butterflyVerison.show();
			            	}
			            	else if(listComBox2.getRawValue() == '重大变更')
			            	{
			            		Ext.getCmp('butterflyVerison').show();
			            		Ext.getCmp('auditorComBox_tap').hide();
			            		butterflyVerison.show();
			            	}
			            	else if(listComBox2.getRawValue() == '维护')
			            	{
			            		Ext.getCmp('butterflyVerison').show();
			            		Ext.getCmp('auditorComBox_tap').hide();
			            		butterflyVerison.show();
			            	}
			            }
			        },
				    width : 160,
				    displayField : 'listName',
				    valueField : 'listValue'
				});
		
	var butterflyVerison = new Ext.form.TextField({
		name: 'butterflyversion',
		id:'butterflyVerison',
		fieldLabel: '单号',
//		hidden:(scriptLevelForTaskAudi==0 || !scriptOddNumberSwitch)?true:false,
		hidden:(scriptLevelForTaskAudi==0 || !scriptOddNumberSwitch || listComBox2.value=='后补')?true:false,
		padding: '3 2 0 8',
		emptyText: '',
		labelWidth : 65,
		labelAlign : 'right',
		width: "18%"
	});
	var checkVersion=new  Ext.form.Checkbox({   
        id:"checkVersion",               
	    name:"checkVersion",
	    boxLabel:"单号补添:" ,
	    hidden:(scriptLevelForTaskAudi==0 || !scriptOddNumberSwitch)?true:false,
	    boxLabelAlign:"before",
	    labelWidth:65,
	    padding: '0 2 0 10'
	});

	
	function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
	var topBar;
	if(ssTimerTaskSwitch) {
		topBar = Ext.create('Ext.form.Panel', {
	        region: 'west',
	        layout: 'anchor',
	        width: '50%',
	        buttonAlign: 'center',
	        cls:'window_border panel_space_left panel_space_right',
	        border: false,
	        dockedItems: [{
	        xtype: 'toolbar',
	        dock: 'top',
	        border:false,
	        padding:'0 5 0 5',
	        items: [taskName, eachNum,execComBox_tap,isSaveTemplateCk,validCode,getValidCodeButton
	        	]
	    },{
	    	xtype: 'toolbar',
	        dock: 'top',
	        border:false,
	        padding:'5 5 5 0',
	        items: [
	        	auditorComBox_tap, execUser,listComBox2,butterflyVerison,checkVersion,authCode,isTimerTask,cycleExecCronText,selectCronButton,{
		        	xtype : 'button',
		        	text: '查看脚本',
		            cls: 'Common_Btn',
		            handler: function(){
		            	viewDetail(iidForTaskAudi)
		            }
		        }
	        ]
	    }]
		});
	} else {
		topBar = Ext.create('Ext.form.Panel', {
	        region: 'west',
	        layout: 'anchor',
	        width: '50%',
	        buttonAlign: 'center',
	        cls:'window_border panel_space_left panel_space_right',
	        border: false,
	        dockedItems: [{
	        xtype: 'toolbar',
	        dock: 'top',
	        border:false,
	        padding:'0 5 0 5',
	        items: [taskName, eachNum, auditorComBox_tap,isSaveTemplateCk, execUser,listComBox2,execComBox_tap,butterflyVerison,checkVersion,
	        	{
	        	xtype : 'button',
	        	text: '查看脚本',
	            cls: 'Common_Btn',
	            handler: function(){
	            	viewDetail(iidForTaskAudi)
	            }
	        }]
	    }]
		});
	}
	
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "timeTaskAuditingSqlPage_area",
        layout: {
            type: 'vbox',
            align : 'stretch'
        },
        border: false,
        autoScroll: true,
        height: contentPanel.getHeight()-38,
        items: [topBar,agentAndSourcePanel, paramsAndAttachmentsPanel/* execDescForm, scriptForm, mainP*/],
        buttons: [
        	{
                text: '提交',
                cls: 'Common_Btn',
                handler: function(){
                	paramStore.sort('paramOrder', 'ASC');
                	var m = paramStore.getRange(0, paramStore.getCount()-1);
                    var jsonData = "[";
                    for (var i = 0, len = m.length; i < len; i++) {
                        var n = 0;
                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                        var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
                        if ("" == paramType) {
                            setMessage('参数类型不能为空！');
                            return;
                        }
                        if (fucCheckLength(paramDesc) > 250) {
                            setMessage('参数描述不能超过250字符！');
                            return;
                        }

                        if (paramType == 'int') {
                            if (!checkIsInteger(paramDefaultValue)) {
                                setMessage('参数类型为int，但参数默认值不是int类型！');
                                return;
                            }
                        }
                        if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
                            globalParams[m[i].get('iid')] = paramDefaultValue;
                        } else {
                        	delete globalParams[m[i].get('iid')];
                        }
                        if (paramType == 'float') {
                            if (!checkIsDouble(paramDefaultValue)) {
                                setMessage('参数类型为float，但参数默认值不是float类型！');
                                return;
                            }
                        }
                        var ss = Ext.JSON.encode(m[i].data);
                        if (i == 0) jsonData = jsonData + ss;
                        else jsonData = jsonData + "," + ss;
                    }

                    jsonData = jsonData + "]";
                    
//                    var agents = [];
//                    var records = agent_grid.getSelectionModel().getSelection();
//	  				if(records.length>0) {
//	  					for(var i = 0, len = records.length; i < len; i++){
//	  						agents.push(records[i].get('iid'));
//	  					}
//	  				} else {
//	  					Ext.Msg.alert('提示', "请选择服务器！");
//                        return;
//	  				}
                    var agents = [];
                    var chosedAgentIds = new Array();
                    var records = agent_grid.getSelectionModel().getSelection();
	  				if(records.length>0) {
	  					for(var i = 0, len = records.length; i < len; i++){
	  						var cpidTmp = records[i].get('iid');
	  						var dsidTmp = cpdsMap[cpidTmp];
//	  						if(cpidTmp!=null && cpidTmp!="" && cpidTmp!=undefined
//	  								&& dsidTmp!=null && dsidTmp!="" && dsidTmp!=undefined
//	  								&& cpidTmp!=-1 && dsidTmp!=-1)
//	  						{
//	  							var tmpRec = {};
//	  							tmpRec.cpid=cpidTmp;
//	  							tmpRec.dsid=dsidTmp;
//		  						agents.push(tmpRec);
//	  						}
	  						if(null==dsidTmp||undefined==dsidTmp||""==dsidTmp||-1==dsidTmp)
	  						{
	  							var agentIp = records[i].get('agentIp');
	  				            var agentPort = records[i].get('agentPort');
	  							var dsErrMsg = "服务器【"+agentIp+":"+agentPort+"】没有选择数据源！";
	  							Ext.Msg.alert('提示', dsErrMsg);
	  	                        return;
	  						}
	  						var tmpRec = {};
  							tmpRec.cpid=cpidTmp;
  							tmpRec.dsid=dsidTmp;
	  						agents.push(tmpRec);
	  						chosedAgentIds.push(records[i].get('iid'));
	  						finalChosedAgentsAndDbSources[cpidTmp] = dsidTmp;
	  					}
	  				} else {
	  					Ext.Msg.alert('提示', "提交失败，请选择设备！");
                        return;
	  				}
	  				if(agents.length<=0)
	  				{
	  					Ext.Msg.alert('提示', "请选择服务器和数据源！");
                        return;
	  				}
	  				var auditor = auditorComBox_tap.getValue();
	  				if(listComBox2.getRawValue() == '后补'){
                    	var rgIds = resourceGroupObj.getValue();
                    	if(!auditor) {
                    		Ext.Msg.alert('提示', "没有选择审核人！");
                    		return;
                    	}
                    }
	  				
	  				if(listComBox2.getRawValue() == '日常变更'||listComBox2.getRawValue() == '重大变更'||listComBox2.getRawValue() == '维护'){
                    	var rgIds = resourceGroupObj.getValue();
                    	
                    	var danHao = butterflyVerison.getValue();
                    	if(scriptOddNumberSwitch){
                    		if(danHao == "") {
                    			Ext.Msg.alert('提示', "请输入单号！");
                    			return;
                    		}
                    	}
                    }
	  				
	  				//判断输入的审核人是否合法 start
                	var displayField =auditorComBox_tap.getRawValue();
					if(!Ext.isEmpty(displayField)){
						//判断输入是否合法标志，默认false，代表不合法
						var flag = false;
						//遍历下拉框绑定的store，获取displayField
						auditorStore_tap.each(function (record) {
							//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
						    var data_fullName = record.get('fullName');
						    if(data_fullName == displayField){
						    	flag =true;
						    	//combo.setValue(record.get('loginName'));
						    }
						});
						if(!flag){
						 	Ext.Msg.alert('提示', "输入的审核人非法");
						 	auditorComBox_tap.setValue("");
						 	return;
						} 
					}
					//判断输入的审核人是否合法  end
	  				
	  				if(ssTimerTaskSwitch) {
	  					//执行时间校验,勾选了就必须填
		  				var isdelay = Ext.getCmp("isDelay").getValue();
		  				var execRadioValue=cornType; // 执行策略单选
		  				var execT = cornTime;
		  				if(isdelay && ""==execT)
		  				{
		  					Ext.Msg.alert('提示', "没有填写执行时间！");
		  					return;
		  				}
	  				} else {
	  					var isdelay = false;
	  					var execT = "";
	  				}
	  				
	  				var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
	  				if(Ext.isEmpty(execDescForExec)) {
	  					Ext.Msg.alert('提示', "没有填写执行描述！");
	  					return;
	  				}
	  				if (fucCheckLength(execDescForExec) > 255) {
                        setMessage('执行描述不能超过255字符！');
                        return;
                    }
	  				var taskN = Ext.util.Format.trim(taskName.getValue());
	  				if(Ext.isEmpty(taskN)) {
	  					Ext.Msg.alert('提示', "任务名称不能为空！");
	  					return;
	  				}
	  				
	  				if (fucCheckLength(taskN) > 255) {
                        setMessage('任务名称不能超过255字符！');
                        return;
                    }
	  			//如果输入验证码，校验验证码是否正确
	  				var authCodeForExec = Ext.util.Format.trim(authCode.getValue());
	  				var phonenum = Ext.getCmp('myvalidCode').getValue();
	  				if(!Ext.isEmpty(authCodeForExec)){
		  	    		if(!isPoneAvailable(phonenum)){
		  	    			Ext.MessageBox.alert("提示", "请填写正确的手机号！");
		  	    			return;
		  	    		}
	  				}
	  				var butterflyV = "";
	  				var check =Ext.getCmp("checkVersion").getValue();
	  				if(scriptLevelForTaskAudi !=0 ||scriptOddNumberSwitch){
	  					if(scriptOddNumberSwitch&&!check){
		  					butterflyV=Ext.util.Format.trim(butterflyVerison.getValue());
//		  					if(butterflyV==null||butterflyV==""){
//		  						Ext.Msg.alert('提示', "单号不能为空！");
//			  					return;
//		  					}
		  					if (fucCheckLength(butterflyV) > 255) {
		                        Ext.Msg.alert('提示', "单号不能超过255字符！");
		                        return;
		                    }
		  				}
	  				}
	  				var performUser = execComBox_tap.getValue();
	  				var en = eachNum.getValue();
	  				if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
	  					if(en>eachNumForA) {
	  						setMessage('并发数量不能超过'+eachNumForA);
	                        return;
	  					}
	  				} else {
	  					setMessage('并发数量不合法！');
                        return;
	  				}
	  				
	  				var mxIid = iidForTaskAudi+":"+4;
	  				startData[mxIid] = {
							'actNo': 4,
							'actType': 0,
							'actName': '基础脚本1',
							'isShutdown':false
					};
	  				if(Ext.isEmpty(en)) {
	  					en = eachNumForA;
	  				}
	  				startData[mxIid]['chosedResGroups'] = rgIds;
	  			    startData[mxIid]['chosedAgentIds'] = chosedAgentIds;
	  			    startData[mxIid]['globalParams'] = globalParams;
	  			    startData[mxIid]['globalConfigParams'] = {};
	  			    startData[mxIid]['globalStartUser'] = Ext.util.Format.trim(execUser.getValue());
	  			    startData[mxIid]['globalConfigStartUser'] = {};
	  			    startData[mxIid]['finalChosedAgentsAndDbSources'] = finalChosedAgentsAndDbSources;
	  			    startData[mxIid]['eachNum'] = en;
	  				var isSaveTemplate = isSaveTemplateCk.getValue();
	  			//填写验证码了，去数据库校验。
  					//校验验证码是否正确
	  				if(!Ext.isEmpty(authCodeForExec)){//填写验证码了，去数据库校验。
  					Ext.Ajax.request({
  						url : 'validAuthCode.do',
  						method : 'POST',
  						params : {
  							serviceId: iidForTaskAudi ,
  							stepId:'-1',
  							planId:'-1',
  							iscenceId:'-1',
  							authCode : authCodeForExec,
  							telephoneNum :phonenum ,
  							iauditUser :auditor,
  							isscript:'0',
  							proType:'10',
  							systemId:'-1'
  						},
  						success: function(response, opts) {
  							var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
  							if(success) {
  				  				if(isSaveTemplate) {
  				  					Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
  				  				        if(btn=='ok') {
  				  				        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
  				  				        		var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
  				  				                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
  				  				        	} else {
  				  				        		Ext.MessageBox.wait("信息验证中...","提示");
  				  				        		var customName = Ext.util.Format.trim(text);
  				  				        		Ext.Ajax.request({
  				  				        	        url: 'checkCustomTemplateNameIsExist.do',
  				  				        	        params: {
  				  				        	            customName: customName,
  				  				        	            flag: 1
  				  				        	        },
  				  				        	        method: 'POST',
  				  				        	        success: function(response, options) {
  				  				        	            if (!Ext.decode(response.responseText).success) {
  				  				        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
  				  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
  				  				        	            } else {
  				  				        	            var isFromCustom = 0;
  				  							    	if(showConfigSwitch || scriptConvertFlowSwitch){
  				  							    		isFromCustom=1;
  				  							    	}
  				  				  					Ext.Ajax.request({
//  				  				  				    url : 'scriptExecAuditing.do',
  				  				  						url : 'scriptExecAuditingForSQL.do',
  				  				  						method : 'POST',
  				  				  						params : {
  				  				  							serviceId: iidForTaskAudi,
  				  				  							execUser: execUser.getValue(),
//  				  				  				    	agents: agents,
  				  				  							agentsJson: Ext.encode(agents),
  				  				  							rgIds: rgIds,
  				  				  							params: jsonData,
  				  				  							execDesc:execDescForExec,
  				  				  							auditor: auditor,
  				  				  							isDelay:isdelay,
  				  				  							execTime:execT,
  				  				  							execStrategy:execRadioValue,
  				  				  							taskName: taskN,
  				  				  							performUser:performUser,
  				  				  							butterflyversion:butterflyV,
  				  				  							data: JSON.stringify(startData),
  				  				  							eachNum: en,
  				  				  							scriptLevel: scriptLevelForTaskAudi,
  				  				  							isFromCustom : isFromCustom ,//为了后台判断是否走带图的，1是走不带图的
  				  				  							timeTaskid:timeTaskid
  					  				  	  						},
  					  				  	  						success: function(response, opts) {
  					  				  	  							var success = Ext.decode(response.responseText).success;
  					  				  	  							var message = Ext.decode(response.responseText).message;
  					  				  	  							if(success) {
  					  					  								if(scriptLevelForTaskAudi!=0){
  					  					  								 
  					  					  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
  					  					  								}
  					  					  							    updateStatus(timeTaskid,1);
  					  					  								var iworkItemid = Ext.decode(response.responseText).workItemId;
	  					  					  							Ext.Ajax.request({
			  						  										url : 'updateWokitemId.do',
			  				    		    		  						method : 'POST',
			  				    		    		  						params : {
			  				    		    		  							workitemId : iworkItemid ,
			  				    		    		  							serviceId: iidForTaskAudi ,
			  				    		    		  							stepId:'-1',
			  				    		    		  							planId:'-1',
			  				    		    		  							iscenceId:'-1',
			  				    		    		  							authCode : authCodeForExec,
			  				    		    		  							telephoneNum :phonenum ,
			  				    		    		  							iauditUser :auditor,
				  				    		    		  						isscript:'0',
				  				    		    	  							proType:'10',
				  				    		    	  							systemId:'-1'
			  				    		    		  						},
			  		   					  	  								 success : function (response, opts)
			  							  	  								 {
			  			   					  	  								
			  							  	  								 },
			  						    		    		  					failure: function(result, request) {
			  						    		    		  					secureFilterRs(result,"回更workitemid失败！");
			  						    		    		  				}
			  						  									});
  					  					  									Ext.Ajax.request ({
  					  					  										url : 'scriptExecForOneRecord.do',
  					  					  										method : 'POST',
  					  					  										params :
  					  					  										{
  					  					  											iworkItemid: iworkItemid
  					  					  										},
  					  					  										success : function (response, opts)
  					  					  										{
  					  					  											var success = Ext.decode (response.responseText).success;
  					  					  											if(success){
  					  					  											  if(scriptLevelForTaskAudi==0){//白名单，直接调用执行方法
  					  					  												if(execRadioValue == 0){//不是定时任务就直接调用任务执行中的执行方法
  					  					  												var	uuid ='';
  							  					  											Ext.Ajax.request({
  							  					  								                url: 'queryUuidById.do',
  							  					  								                method: 'POST',
  							  					  								                async: false,
  							  					  								                params: {
  							  					  								                	serviceId: iidForTaskAudi
  							  					  								                },
  							  					  								                success: function(response, options) {
  							  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
  							  					  								                },
  							  					  								                failure: function(result, request) {
  							  					  								                }
  							  					  								            });
  					  					  													Ext.Ajax.request({
  					  					  														url : 'execScriptServiceStart.do',
  					  					  														method : 'POST',
  					  					  														params : {
  					  					  															serviceId : iidForTaskAudi,
  					  					  															uuid:uuid,
  					  					  															serviceName : serviceNameForTaskAudi,
  					  					  															scriptType:scriptTypeForTaskAudi,
  					  					  															workItemId : iworkItemid,
  					  					  															coatId : 0,
  					  					  															isFlow: 0
  					  					  														},
  					  					  														success : function(response, request) {
  					  					  															var success = Ext.decode(response.responseText).success;
  					  					  															if (success) {
  					  					  																var flowId = Ext.decode(response.responseText).content;
  					  					  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
  					  					  															}
  					  					  														},
  					  					  														failure : function(result, request) {
  					  					  															Ext.Msg.alert('提示', '执行失败！');
  					  					  														}
  					  					  													}); 
  					  					  												   updateStatus(timeTaskid,3);
  					  					  												 } 
  					  					  												}else
  					  					  												{
  					  					  												Ext.Msg.alert('提示', '提交成功');
  					  					  												}
  					  					  											}
  					  					  										}
  					  					  									});
  					  					  								
  					  					  								if(typeof(auditingWin)!="undefined" && auditingWin){
  					  					  									auditingWin.close();
  					  					  								}
  					  					  							
  						  				  	  							Ext.Ajax.request({
  						  		        	        	                    url: 'saveFlowCustomTemplate.do',
  						  		        	        	                    method: 'POST',
  						  		        	        	                    params: {
  						  		        	        	                    	customName: customName,
  						  		        	        	                    	serviceId: iidForTaskAudi,
  						  		        	        	                        data: JSON.stringify(startData),
  						  		        	        	                        audiUserLoginName: auditor,
  								  				  	  							taskName: taskN,
  						  		        	        	                        flag: 1
  						  		        	        	                    },
  						  		        	        	                    success: function(response, options) {
  						  		        	        	                        var success1 = Ext.decode(response.responseText).success;
  						  		        	        	                        //var message1 = Ext.decode(response.responseText).message;
  						  		        	        	                        if (success1) {
  						  		        	        	                            Ext.MessageBox.show({
  						  		        	        	                                title: "提示",
  						  		        	        	                                msg: '请求已经发送到审核人<br>常用任务保存成功！',
  						  		        	        	                                buttonText: {
  						  		        	        	                                    yes: '确定'
  						  		        	        	                                },
  						  		        	        	                                buttons: Ext.Msg.YES
  						  		        	        	                            });
  						  		        	        	                        }
  						  		        	        	                    },
  						  		        	        	                    failure: function(result, request) {
  						  		        	        	                        Ext.MessageBox.show({
  						  		        	        	                            title: "提示",
  						  		        	        	                            msg: "请求已经发送到审核人<br>模板保存失败",
  						  		        	        	                            buttonText: {
  						  		        	        	                                yes: '确定'
  						  		        	        	                            },
  						  		        	        	                            buttons: Ext.Msg.YES
  						  		        	        	                        });
  						  		        	        	                    }
  				
  						  		        	        	                });
  					  				  	  							} else {
  					  				  	  								Ext.MessageBox.alert("提示", message);
  					  				  	  							}
  					  				  	  						},
  					  				  	  						failure: function(result, request) {
  					  				  	  							secureFilterRs(result,"操作失败！");
  					  				  	  						}
  					  				  	  					});
  				  				        	            }
  				  				        	        },
  				  				        	        failure: function(result, request) {}
  				  				        	    });
  				  				        	}
  				  				        	
  				  				        }
  				  				        
  				  				    });
  				  				}else{
  				  					Ext.MessageBox.wait("信息验证中...","提示");
  				  				var isFromCustom = 0;
  						    	if(showConfigSwitch || scriptConvertFlowSwitch){
  						    		isFromCustom=1;
  						    	}
  			  					Ext.Ajax.request({
//  			  				    url : 'scriptExecAuditing.do',
  			  						url : 'scriptExecAuditingForSQL.do',
  			  						method : 'POST',
  			  						params : {
  			  							serviceId: iidForTaskAudi,
  			  							execUser: execUser.getValue(),
//  			  				    	agents: agents,
  			  							agentsJson: Ext.encode(agents),
  			  							rgIds: rgIds,
  			  							params: jsonData,
  			  							execDesc:execDescForExec,
  			  							auditor: auditor,
  			  							isDelay:isdelay,
  			  							execTime:execT,
  			  							execStrategy:execRadioValue,
  			  							taskName: taskN,
  			  							performUser:performUser,
  			  							butterflyversion:butterflyV,
  			  							data: JSON.stringify(startData),
  			  							eachNum: en,
  			  							scriptLevel: scriptLevelForTaskAudi,
  			  							isFromCustom : isFromCustom, //为了后台判断是否走带图的，1是走不带图的
  			  						    timeTaskid:timeTaskid
  				  						},
  				  						success: function(response, opts) {
  				  							var success = Ext.decode(response.responseText).success;
  				  							var message = Ext.decode(response.responseText).message;
  				  							if(success) {
  				  								if(scriptLevelForTaskAudi !=0){
  				  								 
  				  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
  				  								}
  				  							    updateStatus(timeTaskid,1);
  				  								var iworkItemid = Ext.decode(response.responseText).workItemId;
	  				  							Ext.Ajax.request({
				  										url : 'updateWokitemId.do',
		    		    		  						method : 'POST',
		    		    		  						params : {
		    		    		  							workitemId : iworkItemid ,
		    		    		  							serviceId: iidForTaskAudi ,
		    		    		  							stepId:'-1',
		    		    		  							planId:'-1',
		    		    		  							iscenceId:'-1',
		    		    		  							authCode : authCodeForExec,
		    		    		  							telephoneNum :phonenum ,
		    		    		  							iauditUser :auditor,
		    		    		  							isscript:'0',
		    		    		  							proType:'10',
		    		    		  							systemId:'-1'
		    		    		  						},
						  	  								 success : function (response, opts)
					  	  								 {
	 					  	  								
					  	  								 },
				    		    		  					failure: function(result, request) {
				    		    		  					secureFilterRs(result,"回更workitemid失败！");
				    		    		  				}
				  									});
  				  									Ext.Ajax.request ({
  				  										url : 'scriptExecForOneRecord.do',
  				  										method : 'POST',
  				  										params :
  				  										{
  				  											iworkItemid: iworkItemid
  				  										},
  				  										success : function (response, opts)
  				  										{
  				  											var success = Ext.decode (response.responseText).success;
  				  											if(success){
  				  											if(scriptLevelForTaskAudi==0){//白名单，直接调用双人复核中，同意执行方法
  				  												if(execRadioValue == 0){//不是定时任务就直接调用任务执行中的执行方法
  				  													var	uuid ='';
  							  											Ext.Ajax.request({
  							  								                url: 'queryUuidById.do',
  							  								                method: 'POST',
  							  								                async: false,
  							  								                params: {
  							  								                	serviceId: iidForTaskAudi
  							  								                },
  							  								                success: function(response, options) {
  							  								                    uuid = Ext.decode(response.responseText).serviceUuid;
  							  								                },
  							  								                failure: function(result, request) {
  							  								                }
  							  								            });
  				  													Ext.Ajax.request({
  				  														url : 'execScriptServiceStart.do',
  				  														method : 'POST',
  				  														params : {
  				  															serviceId : iidForTaskAudi,
  				  															uuid :uuid,
  				  															serviceName : serviceNameForTaskAudi,
  				  															scriptType:scriptTypeForTaskAudi,
  				  															workItemId : iworkItemid,
  				  															coatId : 0,
  				  															isFlow: 0
  				  														},
  				  														success : function(response, request) {
  				  															var success = Ext.decode(response.responseText).success;
  				  															if (success) {
  				  																var flowId = Ext.decode(response.responseText).content;
  				  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
  				  															}
  				  														},
  				  														failure : function(result, request) {
  				  															Ext.Msg.alert('提示', '执行失败！');
  				  														}
  				  													});
  				  												 updateStatus(timeTaskid,3);
  				  												 }
  				  												}else
  				  												{
  				  													Ext.Msg.alert('提示', '提交成功');
  				  												}
  				  											}
  				  										}
  				  									});
  				  								if(typeof(auditingWin)!="undefined" && auditingWin){
  				  									auditingWin.close();
  				  								}
  				  							} else {
  				  								Ext.MessageBox.alert("提示", message);
  				  							}
  				  						},
  				  						failure: function(result, request) {
  				  							secureFilterRs(result,"操作失败！");
  				  							if(typeof(auditingWin)!="undefined" && auditingWin){
  				  								auditingWinWS.close();
  				  							}
  				  						}
  				  					});
  				  				}
  				  				
  							}else{
  								Ext.MessageBox.alert("提示", message);
  							}
  						},
	  					failure: function(result, request) {
	  						secureFilterRs(result,"操作失败！");
	  					}
  						
  					});
	  				}else{
	  					if(isSaveTemplate) {
		  					Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
		  				        if(btn=='ok') {
		  				        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
		  				        		var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
		  				                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
		  				        	} else {
		  				        		var customName = Ext.util.Format.trim(text);
		  				        		Ext.Ajax.request({
		  				        	        url: 'checkCustomTemplateNameIsExist.do',
		  				        	        params: {
		  				        	            customName: customName,
		  				        	            flag: 1
		  				        	        },
		  				        	        method: 'POST',
		  				        	        success: function(response, options) {
		  				        	            if (!Ext.decode(response.responseText).success) {
		  				        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
		  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
		  				        	            } else {
		  				        	            	Ext.Ajax.request({
		  				        	            		url : 'scriptExecAuditingForSQL.do',
		  				  	  						   method : 'POST',
			  				  	  						params : {
			  				  	  						serviceId: iidForTaskAudi,
			  				  							execUser: execUser.getValue(),
//			  				  				    	agents: agents,
			  				  							agentsJson: Ext.encode(agents),
			  				  							rgIds: rgIds,
			  				  							params: jsonData,
			  				  							execDesc:execDescForExec,
			  				  							auditor: auditor,
			  				  							isDelay:isdelay,
			  				  							execTime:execT,
			  				  							execStrategy:execRadioValue,
			  				  							taskName: taskN,
			  				  							performUser:performUser,
			  				  							butterflyversion:butterflyV,
			  				  							data: JSON.stringify(startData),
			  				  							eachNum: en,
			  				  							scriptLevel: scriptLevelForTaskAudi,
			  				  						    timeTaskid:timeTaskid
			  				  	  						},
			  				  	  						success: function(response, opts) {
			  				  	  							var success = Ext.decode(response.responseText).success;
			  				  	  							var message = Ext.decode(response.responseText).message;
			  				  	  							if(success) {
			  					  								if(scriptLevelForTaskAudi==0){
			  					  								}else{
			  					  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
			  					  								}
			  					  								updateStatus(timeTaskid,1);
			  					  								var iworkItemid = Ext.decode(response.responseText).workItemId;
			  					  								if(scriptLevelForTaskAudi==0){//白名单，直接调用双人复核中，同意执行方法
			  					  									Ext.Ajax.request ({
			  					  										url : 'scriptExecForOneRecord.do',
			  					  										method : 'POST',
			  					  										params :
			  					  										{
			  					  											iworkItemid: iworkItemid
			  					  										},
			  					  										success : function (response, opts)
			  					  										{
			  					  											var success = Ext.decode (response.responseText).success;
			  					  											if(success){
			  					  												if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
				  					  												var	uuid ='';
					  					  											Ext.Ajax.request({
					  					  								                url: 'queryUuidById.do',
					  					  								                method: 'POST',
					  					  								                async: false,
					  					  								                params: {
					  					  								                	serviceId: iidForTaskAudi
					  					  								                },
					  					  								                success: function(response, options) {
					  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
					  					  								                },
					  					  								                failure: function(result, request) {
					  					  								                }
					  					  								            });
			  					  													Ext.Ajax.request({
			  					  														url : 'execScriptServiceStart.do',
			  					  														method : 'POST',
			  					  														params : {
			  					  															serviceId : iidForTaskAudi,
			  					  															uuid :uuid,
			  					  															serviceName : serviceNameForTaskAudi,
			  					  															scriptType:scriptTypeForTaskAudi,
			  					  															workItemId : iworkItemid,
			  					  															coatId : 0,
			  					  															isFlow: 0
			  					  														},
			  					  														success : function(response, request) {
			  					  															var success = Ext.decode(response.responseText).success;
			  					  															if (success) {
			  					  																var flowId = Ext.decode(response.responseText).content;
			  					  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
			  					  															}
			  					  														},
			  					  														failure : function(result, request) {
			  					  															Ext.Msg.alert('提示', '执行失败！');
			  					  														}
			  					  													}); 
			  					  												updateStatus(timeTaskid,3);
			  					  												}
			  					  											}
			  					  										}
			  					  									});
			  					  								}
			  					  								if(typeof(auditingWin)!="undefined" && auditingWin){
			  					  									auditingWin.close();
			  					  								}
			  					  							
				  				  	  							Ext.Ajax.request({
				  		        	        	                    url: 'saveFlowCustomTemplate.do',
				  		        	        	                    method: 'POST',
				  		        	        	                    params: {
				  		        	        	                    	customName: customName,
				  		        	        	                    	serviceId: iidForTaskAudi,
				  		        	        	                        data: JSON.stringify(startData),
				  		        	        	                        audiUserLoginName: auditor,
						  				  	  							taskName: taskN,
				  		        	        	                        flag: 1
				  		        	        	                    },
				  		        	        	                    success: function(response, options) {
				  		        	        	                        var success1 = Ext.decode(response.responseText).success;
				  		        	        	                        var message1 = Ext.decode(response.responseText).message;
				  		        	        	                        if (success1) {
				  		        	        	                            Ext.MessageBox.show({
				  		        	        	                                title: "提示",
				  		        	        	                                msg: '请求已经发送到审核人<br>常用任务保存成功！',
				  		        	        	                                buttonText: {
				  		        	        	                                    yes: '确定'
				  		        	        	                                },
				  		        	        	                                buttons: Ext.Msg.YES
				  		        	        	                            });
				  		        	        	                        }
				  		        	        	                    },
				  		        	        	                    failure: function(result, request) {
				  		        	        	                        Ext.MessageBox.show({
				  		        	        	                            title: "提示",
				  		        	        	                            msg: "请求已经发送到审核人<br>模板保存失败",
				  		        	        	                            buttonText: {
				  		        	        	                                yes: '确定'
				  		        	        	                            },
				  		        	        	                            buttons: Ext.Msg.YES
				  		        	        	                        });
				  		        	        	                    }
		
				  		        	        	                });
			  				  	  							} else {
			  				  	  								Ext.MessageBox.alert("提示", message);
			  				  	  							}
			  				  	  						},
			  				  	  						failure: function(result, request) {
			  				  	  							secureFilterRs(result,"操作失败！");
			  				  	  						}
			  				  	  					});
		  				        	            }
		  				        	        },
		  				        	        failure: function(result, request) {}
		  				        	    });
		  				        	}
		  				        	
		  				        }
		  				        
		  				    });
		  				}else{
		  					
		  					var isFromCustom = 0;
					    	if(showConfigSwitch || scriptConvertFlowSwitch){
					    		isFromCustom=1;
					    	}
		  					Ext.Ajax.request({
//		  				    url : 'scriptExecAuditing.do',
		  						url : 'scriptExecAuditingForSQL.do',
		  						method : 'POST',
		  						params : {
		  							serviceId: iidForTaskAudi,
		  							execUser: execUser.getValue(),
//		  				    	agents: agents,
		  							agentsJson: Ext.encode(agents),
		  							rgIds: rgIds,
		  							params: jsonData,
		  							execDesc:execDescForExec,
		  							auditor: auditor,
		  							isDelay:isdelay,
		  							execTime:execT,
		  							execStrategy:execRadioValue,
		  							taskName: taskN,
		  							performUser:performUser,
		  							butterflyversion:butterflyV,
		  							data: JSON.stringify(startData),
		  							eachNum: en,
		  							scriptLevel: scriptLevelForTaskAudi,
		  							isFromCustom : isFromCustom, //为了后台判断是否走带图的，1是走不带图的
		  							timeTaskid:timeTaskid
		  						},
		  						success: function(response, opts) {
		  							var success = Ext.decode(response.responseText).success;
		  							var message = Ext.decode(response.responseText).message;
		  							if(success) {
		  								if(scriptLevelForTaskAudi==0){
		  								}else{
		  									if(scriptLevelForTaskAudi !=0&&listComBox2.getRawValue() == '后补'){
				  								 
			  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
			  								}
			  								if(listComBox2.getRawValue() == '日常变更'||listComBox2.getRawValue() == '重大变更'||listComBox2.getRawValue() == '维护'){
				  								 
			  									Ext.MessageBox.alert("提示", "已发送");
			  								}
		  								}
		  								updateStatus(timeTaskid,1);
		  								var iworkItemid = Ext.decode(response.responseText).workItemId;
		  								if(scriptLevelForTaskAudi==0||listComBox2.getRawValue() == '日常变更'||listComBox2.getRawValue() == '重大变更'||listComBox2.getRawValue() == '维护'){//白名单，直接调用双人复核中，同意执行方法
		  									Ext.Ajax.request ({
		  										url : 'scriptExecForOneRecord.do',
		  										method : 'POST',
		  										params :
		  										{
		  											iworkItemid: iworkItemid
		  										},
		  										success : function (response, opts)
		  										{
		  											var success = Ext.decode (response.responseText).success;
		  											if(success){
		  												if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
		  													var	uuid ='';
				  											Ext.Ajax.request({
				  								                url: 'queryUuidById.do',
				  								                method: 'POST',
				  								                async: false,
				  								                params: {
				  								                	serviceId: iidForTaskAudi
				  								                },
				  								                success: function(response, options) {
				  								                    uuid = Ext.decode(response.responseText).serviceUuid;
				  								                },
				  								                failure: function(result, request) {
				  								                }
				  								            });
				  											if(scriptLevelForTaskAudi==0){
			  													Ext.Ajax.request({
			  														url : 'execScriptServiceStart.do',
			  														method : 'POST',
			  														params : {
			  															serviceId : iidForTaskAudi,
			  															uuid : uuid,
			  															serviceName : serviceNameForTaskAudi,
			  															scriptType:scriptTypeForTaskAudi,
			  															workItemId : iworkItemid,
			  															coatId : 0,
			  															isFlow: 0
			  														},
			  														success : function(response, request) {
			  															var success = Ext.decode(response.responseText).success;
			  															if (success) {
			  																var flowId = Ext.decode(response.responseText).content;
			  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
			  															}
			  														},
			  														failure : function(result, request) {
			  															Ext.Msg.alert('提示', '执行失败！');
			  														}
			  													}); 
				  											}
				  											updateStatus(timeTaskid,3);
		  												}
		  											}
		  										}
		  									});
		  								}
		  								if(typeof(auditingWin)!="undefined" && auditingWin){
		  									auditingWin.close();
		  								}
		  							} else {
		  								Ext.MessageBox.alert("提示", message);
		  							}
		  						},
		  						failure: function(result, request) {
		  							secureFilterRs(result,"操作失败！");
		  							if(typeof(auditingWin)!="undefined" && auditingWin){
		  								auditingWinWS.close();
		  							}
		  						}
		  					});
		  				}
	  				}
	  				
                	
                }
            }
        ],
        buttonAlign: 'center'
    });
    /*var editor = CodeMirror.fromTextArea(document.getElementById('codeEditView-for-auditing'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        readOnly: true
    });
    editor.setSize(contentPanel.getWidth()*0.337, contentPanel.getHeight() - 160);*/
    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
//        editor.setSize(contentPanel.getWidth()*0.337, contentPanel.getHeight() - 160);
    });
    
    
    function viewDetail(serviceId) {
//    	if(!DetailWinTi) {
    		DetailWinTi = Ext.create('widget.window', {
    			title : '详细信息',
    			closable : true,
    			closeAction : 'destroy',
    			width : contentPanel.getWidth(),
    			minWidth : 350,
    			height : contentPanel.getHeight(),
    			draggable : false,// 禁止拖动
    			resizable : false,// 禁止缩放
    			modal : true,
    			loader : {}
    		});
//    	}
    	
    	DetailWinTi.getLoader().load(
    			{
    				url : 'queryOneServiceForView.do',
    				params : {
    					iid:serviceId,
    					flag: 1,
    					hideReturnBtn: 1
    				},
    				autoLoad : true,
    				scripts : true
    			});
    	DetailWinTi.show();
    }
});