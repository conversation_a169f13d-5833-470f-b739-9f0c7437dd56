Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    var rownum = '';
    var creatCronWin ;
    var	configWin;
    var topBar;
    var scriptTimeStore;
    var cycleExecCronPanel;
    var cycleExecCronText;
    var selectCronButton;
    var execModeGroup;
    Ext.define('ScriptTime', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },{
            name: 'iname',
            type: 'string'
        },
        {
            name: 'createtime',
            type: 'string'
        },{
            name: 'createuser',
            type: 'string'
        },{
            name: 'username',
            type: 'string'
        },
        {
            name: 'corntime',
            type: 'string'
        },{
            name: 'corntype',
            type: 'long'
        },
        {
            name: 'serviceid',
            type: 'long'
        },
        {
            name: 'servicename',
            type: 'string'
        },
        {
            name: 'idesc',
            type: 'string'
        },
        {
            name: 'status',
            type: 'int'
        }]
    });

    scriptTimeStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'ScriptTime',
        proxy: {
            type: 'ajax',
            url: 'getScriptTimeConfigList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    Ext.define('ServiceModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		} ]
	});
	var serviceStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		model : 'ServiceModel',
		proxy : {
			type : 'ajax',
			url : 'getServiceList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	serviceStore.on('load', function() {
		var ins_rec = Ext.create('ServiceModel', {
			id : '-1',
			name : '未选服务'
		});
		serviceStore.insert(0, ins_rec);

	});
	var clickGroudRow = 0;
	var servieCombo = Ext.create('Ext.form.field.ComboBox', {
		labelWidth : 70,
		labelAlign : 'right',
		width : '80%',
		store : serviceStore,
		padding : '0 0 5 0',
		displayField : 'name',
		valueField : 'id',
		triggerAction : 'all',
		editable: false,
		queryMode : 'local',
		listeners : {
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = Ext.util.Format.trim(e.query);
					combo.store
							.filterBy(function(record, id) {
								var text = record.get(combo.displayField);
								return (text.toLowerCase().indexOf(
										value.toLowerCase()) != -1);
							});
					combo.expand();
					return false;
				}
			},
			select : function(combo, records, eOpts) {
				if(clickGroudRow != null){
					var record = scriptTimeStore.getAt(clickGroudRow);
					var selectValue = combo.getValue();
					record.data.serviceid = selectValue;
				}
			}
		}
	});
	 var statusStore = Ext.create('Ext.data.Store', {
	        fields: ['id', 'name'],
	        data: [{
	            'id': '0',
	            'name': '新建'
	        },
	        {
	            'id': '1',
	            'name': '提交'
	        },
	        {
	            'id': '2',
	            'name': '打回'
	        },
	        {
	            'id': '3',
	            'name': '通过'
	        },
	        {
	            'id': '4',
	            'name': '终止'
	        },
	        {
	            'id': '5',
	            'name': '提醒'
	        }]
	    });
    var scriptTimeColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: 'ID',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },{
        text: 'corntype',
        dataIndex: 'corntype',
        width: 40,
        hidden: true
    },{
        text: '名称',
        dataIndex: 'iname',
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '脚本ID',
        dataIndex: 'serviceid',
        hidden : true,
   	 	width : 50,
   	 	editor : {
   		 
   	 	}
    },{
        text: '服务名称',
        dataIndex: 'servicename',
        width: 150,
        editor:servieCombo,
        renderer : function(value, metadata, record) {
			var serviceid = record.data.serviceid;
			var index = serviceStore.find('id', serviceid);
			var str = '';
			if (index != -1) {
				str = serviceStore.getAt(index).data.name;
			}
			record.data.servicename = str;
			return str;
   	 	}
    },
    {
        text: '创建时间',
        dataIndex: 'createtime',
        flex: 1
    },
    {
        text: '创建人',
        dataIndex: 'username',
        flex: 1
    },{
        text: '状态',
        dataIndex: 'status',
        flex: 1,
        renderer: function(value) {
            var index = statusStore.find('id', value);
            var record = statusStore.getAt(index);
            if (record != null) {
                return record.data.name;
            } else {
                return value;
            }
        }
    },
    {
        text: '定时时间',
        dataIndex: 'corntime',
        width: 150,
        renderer : getTaskRunTime,
        listeners:{
                    click:function(a,b,c,d){
                        var taskRuntime=a.getStore().getAt(c).data.corntime;
                        setCronRunTime(taskRuntime,c);
                    }
        }
    },
    {
        text: '定时说明',
        dataIndex: 'idesc',
        flex: 1,
        editor: {
            allowBlank: false
        }
    },{
//		text : '操作',
//		dataIndex : 'sysOperation',
//		width : 160,
//		xtype : 'actioncolumn',
//		renderer : function(value, p, record) {
//			var iid = record.get('iid');
//			var serviceid = record.get('serviceid');
//			var serviceName = record.get('servicename');
//			var corntime = record.get('corntime');
//			var corntype = record.get('corntype');
//			return '<span class="switch_span">'
//						+ '<a href="javascript:void(0)" onclick="forwardScriptExecAudiForTaskIndex('+ iid+ ',' + serviceid + ',\'' + serviceName + '\',\'' + corntime + '\',' + corntype + ',)"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;任务申请</a>&nbsp;&nbsp;';
//			
//
//		}
    	text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'sysOperation',
		width : 160,
		items : [{
			text : '任务申请',
			iconCls : 'monitor_search',					 
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid; 
				var serviceid = grid.getStore().data.items[rowIndex].data.serviceid; 
				var serviceName = grid.getStore().data.items[rowIndex].data.servicename; 
				var corntime = grid.getStore().data.items[rowIndex].data.corntime; 
	            var corntype = grid.getStore().data.items[rowIndex].data.corntype;
	            forwardScriptExecAudiForTaskIndex(iid,serviceid,serviceName,corntime,corntype);
			}
		}]
	}];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptTimeStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false
    });
    serviceStore.load({
		callback : function(records, operation, success) {
			scriptTimeStore.load();
		}
	});
	var form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : [ '->',{
		        text: '增加',
		        cls: 'Common_Btn',
		        //iconCls:'sc_add',
		        handler: add
		    },
		    {
		        text: '保存',
		        cls: 'Common_Btn',
		        //iconCls:'sc_save',
		        handler: saveDangerCmd
		    },
		    '-', {
		        itemId: 'delete',
		        text: '删除',
		        cls: 'Common_Btn',
		       //iconCls:'sc_delete',
		        disabled: true,
		        handler: deleteDangerCmd
		    }]
		} ]
	});
	
    var scriptTimeGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: scriptTimeStore,
        cls:'customize_panel_back',
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        plugins: [cellEditing],
        padding : panel_margin,
		border:true,
//		bbar: pageBar, 
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        columnLines: true,
        columns: scriptTimeColumns,
        animCollapse: false
    });
    scriptTimeGrid.on("celldblclick", function(obj, td, cellIndex, record,tr, rowIndex, e, eOpts) {
		clickGroudRow = rowIndex;
	})
    scriptTimeGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptTimeConfig_grid_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,scriptTimeGrid]
    });
    
    function setCronExpression(){}

    function getTaskRunTime(value, p, record) {
    	var displayValue = value;
    	if (null == displayValue || "" == displayValue) {
    		displayValue = "选择";
    	}
    	return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" >"
    			+ "<span class='abc' style='color:#3167ff;'>"
    			+ displayValue
    			+ "</span>" + "</a>";
    }
	
	var timingExecRadio = Ext.create('Ext.form.field.Radio', {
		width: 80,
		name:'execRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '定时执行',
//		checked :true,
		inputValue : 2
	});
	var cycleExecRadio = Ext.create('Ext.form.field.Radio', {
		width: 80,
		name:'execRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '周期执行',
		inputValue : 1
	});
    var execTimeComponent = Ext.create('Go.form.field.DateTime',
	{
		fieldLabel: '执行时间:',
//		id : 'execTimeComponent',
		labelAlign: 'left', 
		labelWidth : 60,
		width : 250,
		hidden : true,
	    format : 'Y-m-d H:i:s'
	});
    execModeGroup =  Ext.create('Ext.form.RadioGroup', {
			name:'cycleModeGroup',
			labelAlign : 'left',
			layout: 'column',
			width : '160',
			items:[timingExecRadio,cycleExecRadio],
			listeners:{
	            //通过change触发
	            change: function(g , newValue , oldValue){
	            	if(newValue.execRadio == 2)//定时
	            	{
	            		execTimeComponent.show();
	            		cycleExecCronPanel.hide();
	            	}
	            	else if(newValue.execRadio == 1)//周期
	            	{
	            		execTimeComponent.hide();
	            		cycleExecCronPanel.show(	);
	            	}
	            }
	        }
		}); 
    /** 选择生成周期表达式按钮 **/
	selectCronButton = Ext.create ("Ext.Button",
	{
		id : 'selectCronButton_id',
	    cls : 'Common_Btn',
	    text : "选择",
	    handler : selectExecCron
	});
	
	function selectExecCron()
	{
		
		if (creatCronWin == undefined || !creatCronWin.isVisible()) {
			creatCronWin = Ext.create('Ext.window.Window', {
				title : '定时任务参数设置',
				modal : true,
				id : 'creatCronWin',
				closeAction : 'destroy',
				constrain : true,
				autoScroll : true,
				upperWin : creatCronWin,
				width : contentPanel.getWidth() - 350,
				height : contentPanel.getHeight() - 30,
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				layout : 'fit',
				loader : {
					url : 'cronMainForSpdb.do',
//					params : {
//						sysType : sysType,
//						state : state,
//						errorTaskId : errorTaskId,
//						pageType : pageType
//					},
					autoLoad : true,
					autoDestroy : true,
					scripts : true
				}
			});
		}
		creatCronWin.show();
	}    

  cycleExecCronText = new Ext.form.TextField (
		{
		    fieldLabel : '执行周期',
		    labelWidth : 65,
		    labelAlign : 'right',
		    id : 'cycleExecCronText',
			name: 'cycleExecCronText',
		    readOnly : true
		});

  cycleExecCronPanel = Ext.create('Ext.form.Panel', {
    width: 330,
   border: false,
   hidden : true,
   name :'execTimeComponent',
//   layout: {
//           type: 'hbox',
//           align : 'stretch'
//   },
   items: [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ cycleExecCronText,selectCronButton]
		}]
});

topBar = Ext.create('Ext.form.Panel', {
    region: 'north',
    layout: 'anchor',
    width: '80%',
    buttonAlign: 'center',
    cls:'window_border panel_space_left panel_space_right',
    border: false,
    dockedItems: [{
    xtype: 'toolbar',
    dock: 'top',
    border:false,
    items: [execModeGroup]
},{
	xtype: 'toolbar',
    dock: 'top',
    border:false,
    items: [execTimeComponent,cycleExecCronPanel]
}]
});

function setCronRunTime(taskRuntime,index)
{
	cycleExecCronText.setValue(""); 
	execTimeComponent.setValue("");
	console.log(execModeGroup);
	execModeGroup.setValue({execRadio: "2"});
	configWin = Ext.create('Ext.window.Window', {
	  		title : '定时任务参数设置',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'hidden',
	  		layout: 'border',
	  		 width: 400,
	 	    height: 220,
	  		items:[topBar],
	  		dockedItems: [{
	            xtype: 'toolbar',
	            dock:'bottom',
	            layout: {pack: 'center'},
		        items: [{
		            text: '确定',
		            cls: 'Common_Btn',
		            handler: function(){
		            	var execRadioValue =  execModeGroup.getChecked()[0].inputValue;
		            	var execT;
		            	var execTimeDisplay;
		            	var expression;
							 if(execRadioValue == 1){//周期执行
							 	execT =cycleExecCronText.getValue(); 
							 	expression = execT;
							 	execTimeDisplay = '';
							 	if(execT== '' || execT == null){
							 		Ext.Msg.alert('提示', "执行策略为周期执行，执行周期不能为空！");
									return;
							 	}
							 }else if(execRadioValue == 2){//定时执行
							 	execTimeDisplay = execTimeComponent.getRawValue();
							 	expression = execTimeDisplay;
							 	execT ='';
							 	if(execTimeDisplay== '' || execTimeDisplay == null){
							 		Ext.Msg.alert('提示', "执行策略为定时执行，执行时间不能为空！");
									return;
							 	}
							 }
		            	scriptTimeGrid.getStore().getAt(index).set("corntime",expression);
		            	scriptTimeGrid.getStore().getAt(index).set("corntype",execRadioValue);
		            	this.up("window").close();
		            	}
		        }]
	  		}]
	  	}).show();
} 
    
    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    function add() {
        var store = scriptTimeGrid.getStore();
        var p = {
            iid: '',
            cmd: '',
            scriptType: '',
            scriptCmdLevel: '',
            scriptCmdRemark: ''
        };
        store.insert(0, p);
        scriptTimeGrid.getView().refresh();
    }

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function saveDangerCmd() {
        var m = scriptTimeStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var n = 0;
            var cmd = m[i].get("iname").trim();
           for (var k = 0; k < scriptTimeStore.getCount(); k++) {
						var record = scriptTimeStore.getAt(k);
						var cname = record.data.iname;
						if (cname.trim() == cmd) {
							n = n + 1;
						}
					}
					if (n > 1) {
						setMessage('任务名不能有重复！');
						return;
					}
			var p = 0;
			 var status = m[i].get("status");
			 if (!status == 0) {
				 setMessage('只能修改状态为新建的任务！');
					return;
				      }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveScriptTimeConfig.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                	scriptTimeStore.modified = [];
                	scriptTimeStore.reload();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', '保存失败！');
                }
            },
            // failure : function(result, request) {
            // Ext.Msg.alert('提示', '保存失败！');
            // }
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function deleteDangerCmd() {
        var data = scriptTimeGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否真的要删除命令？",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        var status = record.get('status');
                        if (!status == 0){
                        	Ext.Msg.alert('提示', '只能删除状态为新建的任务！');
                            return;
                        }
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                            ids.push(iid);
                        }else{
                        	scriptTimeStore.remove(record);
                             }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteScriptTimeConfig.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                            	scriptTimeStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    	scriptTimeGrid.getView().refresh();
                    }
                  
                }
            });
        }
    }

    /** 窗口尺寸调节* */
    contentPanel.on('resize',
    function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(scriptTimeGrid);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function forwardScriptExecAudiForTaskIndex(iid,serviceId, serviceName,corntime,corntype) {
    	auditingWin = Ext.create('widget.window', {
    		title: '任务申请：'+serviceName,
    		closable: true,
    		closeAction: 'destroy',
    		width: contentPanel.getWidth(),
    		minWidth: 350,
    		height: contentPanel.getHeight(),
    		draggable: false,
    		// 禁止拖动
    		resizable: false,
    		// 禁止缩放
    		modal: true,
    		loader: {
    			url: 'queryOneServiceForTimeTaskAuditing.do',
    			params: {
    				timeTaskid:iid,
    				iid: serviceId,
    				serviceName:serviceName,
    				corntime:corntime,
    				corntype:corntype
    			},
    			autoLoad: true,
    			scripts: true
    		}
    	});
    	auditingWin.show();
    }
});

