Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();

	Ext.define('scriptCheckRules', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'rulesid',
			type : 'long'
		}, {
			name : 'startsymbol',
			type : 'string'
		}, {
			name : 'endsymbol',
			type : 'string'
		}, {
			name : 'scripttype',
			type : 'string'
		}, {
			name : 'rulesstatus',
			type : 'int'
		} ]
	});

	var scriptCheckRulesStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'scriptCheckRules',
		proxy : {
			type : 'ajax',
			url : 'getScriptCheckRulesList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var pagebar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		pageSize : 30,
		dock : 'bottom',
		id : 'pageBarId',
		store : scriptCheckRulesStore,
		baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		displayInfo : true,
		displayMsg : '显示 {0}-{1}条记录，共 {2} 条',
		emptyMsg : "没有记录"
	});

	var statusStore = Ext.create('Ext.data.Store', {
		fields : [ 'statusText', 'statusValue' ],
		data : [ {
			'statusText' : '可用',
			'statusValue' : '0'
		}, {
			'statusText' : '禁用',
			'statusValue' : '1'
		} ]
	});

	var scriptTypeStore = Ext.create('Ext.data.Store', {
		fields : [ 'scriptTypeName', 'scriptTypeValue' ],
		data : [ {
			'scriptTypeName' : 'shell',
			'scriptTypeValue' : 'sh'
		}, {
			'scriptTypeName' : 'bat',
			'scriptTypeValue' : 'bat'
		}, {
			'scriptTypeName' : 'Python',
			'scriptTypeValue' : 'py'
		}, {
			'scriptTypeName' : 'Perl',
			'scriptTypeValue' : 'perl'
		}, {
			'scriptTypeName' : 'SQL',
			'scriptTypeValue' : 'sql'
		} ]
	});

	var statusCombo = Ext.create('Ext.form.field.ComboBox', {
		store : statusStore,
		queryMode : 'local',
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'statusText',
		valueField : 'statusValue',
		triggerAction : "all"
	});

	var scriptTypeCombo = Ext.create('Ext.form.field.ComboBox', {
		store : scriptTypeStore,
		queryMode : 'local',
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'scriptTypeName',
		valueField : 'scriptTypeValue',
		triggerAction : "all"
	});

	var scriptCheckRulesColumns = [ {
		text : '序号',
		xtype : 'rownumberer',
		width : 40
	}, {
		text : '主键',
		dataIndex : 'rulesid',
		width : 100,
		hidden : true
	}, {
		text : '开始符',
		dataIndex : 'startsymbol',
		width : 200,
		editor : {
			allowBlank : false
		}
	}, {
		text : '结束符',
		dataIndex : 'endsymbol',
		width : 200,
		editor : {
			allowBlank : false
		}
	}, {
		text : '脚本类型',
		dataIndex : 'scripttype',
		width : 100,
		editor : scriptTypeCombo,
		renderer : function(value, metadata, record) {
			var index = scriptTypeStore.find('scriptTypeValue', value);
			if (index != -1) {
				return scriptTypeStore.getAt(index).data.scriptTypeName;
			}
			return value;
		}
	}, {
		text : '状态',
		dataIndex : 'rulesstatus',
		width : 100,
		editor : statusCombo,
		renderer : function(value, metadata, record) {
			var index = statusStore.find('statusValue', value);
			if (index != -1) {
				return statusStore.getAt(index).data.statusText;
			}
			return value;
		}
	} ];

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});

	var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		iselect : false,
		bodyCls : 'x-docked-noborder-top',
		baseCls : 'customize_gray_back',
		// collapsed : true,///默认收缩
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls : 'customize_gray_back',
			items : [ {
				fieldLabel : '查询条件',
				name : 'queryConditionSign',
				labelAlign : 'right',
				width : '30%',
				labelWidth : 70,
				emptyText : '开始符/结束符',
				xtype : 'textfield',
				listeners : {
					specialkey : function(field, e) {
						if (e.getKey() == e.ENTER) {
							scriptCheckRulesGrid.ipage.moveFirst();
						}
					}
				}
			}, {
				xtype:'combo',
				fieldLabel : '脚本类型',
				name : 'scrtiptTypeCondition',
				labelAlign : 'right',
				store : scriptTypeStore,
				queryMode : 'local',
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				displayField : 'scriptTypeName',
				valueField : 'scriptTypeValue',
				triggerAction : "all"
			}, {
				text : '查询',
				cls : 'Common_Btn',
				handler : function() {
					queryBtnFun();
				}
			}, {
				text : '重置',
				cls : 'Common_Btn',
				handler : function() {
					formMainPanel.getForm().findField("queryConditionSign").setValue('');
					formMainPanel.getForm().findField("scrtiptTypeCondition").setValue('');
				}
			}, '->', {
				text : '增加',
				cls : 'Common_Btn',
				handler : function() {
					scriptCheckRulesStore.insert(0, new scriptCheckRules());
					cellEditing.startEditByPosition({
						row : 0,
						column : 0
					});
				}
			}, {
				text : '保存',
				cls : 'Common_Btn',
				handler : savescriptCheckRules
			}, '-', {
				itemId : 'delete',
				text : '删除',
				cls : 'Common_Btn',
				disabled : true,
				handler : deletescriptCheckRules
			} ]
		} ]
	});

	var scriptCheckRulesGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		width : contentPanel.getWidth(),
		cls : 'customize_panel_back',
		region : 'center',
		forceFit : true,
		store : scriptCheckRulesStore,
		selModel : Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true
		}),
		plugins : [ cellEditing ],
		border : false,
		columnLines : true,
		columns : scriptCheckRulesColumns,
		animCollapse : false,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		autoScroll : true,
		listeners : {
			beforeedit : function(editor, e, eOpts) {
				return true;
			}
		}
	});

	scriptCheckRulesStore.on('beforeload', function(store, options) {
		var queryConditionSign = formMainPanel.getForm().findField("queryConditionSign").getValue();
		var scrtiptTypeCondition = formMainPanel.getForm().findField("scrtiptTypeCondition").getValue();
		Ext.apply(scriptCheckRulesStore.proxy.extraParams, {
			queryString : queryConditionSign,
			scrtiptTypeCondition:scrtiptTypeCondition
		});
	});

	// scriptCheckRulesStore.load({params:{limit:pagelimit,start:0}});
	// //不要写这个地方，不然查询的执行的load()方法不好使。

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptcheckrules_grid_area",
		border : false,
		layout : 'border',
		bodyPadding : 0,
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		cls : 'customize_panel_back', // 引入圆角
		bodyCls : 'service_platform_bodybg',
		items : [ formMainPanel, scriptCheckRulesGrid ]
	});

	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - 5);
	})

	scriptCheckRulesGrid.getSelectionModel().on('selectionchange',
			function(selModel, selections) {
				mainPanel.down('#delete').setDisabled(selections.length === 0);
			});

	function setMessage(msg) {
		Ext.Msg.alert('提示', msg);
	}

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		contentPanel.getHeader().show();
		Ext.destroy(scriptCheckRulesGrid);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	function savescriptCheckRules() {

		var m = scriptCheckRulesStore.getModifiedRecords();
		if (m.length < 1) {
			setMessage('数据未发生改变，不需要保存！');
			return;
		}

		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			var n = 0;
			var startsymbol = m[i].get("startsymbol").trim();
			var endsymbol = m[i].get("endsymbol").trim();
			var scripttype = m[i].get("scripttype");

			if ("" == startsymbol || null == startsymbol) {
				setMessage('开始符不能为空！');
				return;
			}

			if (fucCheckLength(startsymbol) > 100) {
				setMessage('开始符不能超过100字符！');
				return;
			}

			if ("" == endsymbol || null == endsymbol) {
				setMessage('开始符不能为空！');
				return;
			}

			if (fucCheckLength(endsymbol) > 100) {
				setMessage('开始符不能超过100字符！');
				return;
			}

			if ("" == scripttype || null == scripttype) {
				setMessage('脚本类型不能为空！');
				return;
			}
			
			if (endsymbol == startsymbol ){
				setMessage('开始符与结束符不能相同！');
				return;
			}

			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";

		Ext.Ajax.request({
			url : 'saveScriptCheckRules.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success1 = Ext.decode(response.responseText).success;
				if (success1) {
					scriptCheckRulesStore.modified = [];
					scriptCheckRulesStore.reload();
					Ext.Msg.alert('提示', '保存成功');
				} else {
					Ext.Msg.alert('提示', '保存失败！<br>'+ Ext.decode(response.responseText).message);
				}
			}
		});

	};

	function deletescriptCheckRules() {

		var data = scriptCheckRulesGrid.getView().getSelectionModel().getSelection();
		
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请先选择您要操作的数据!');
			return;
		} else {
			Ext.Msg.confirm("请确认","确定要删除吗？",
				function(button, text) {
					if (button == "yes") {
						var rulesIds = [];
						Ext.Array.each(data, function(record) {
							var rulesId = record.get('rulesid');
							// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
							if (rulesId) {
								rulesIds.push(rulesId);
							}
							scriptCheckRulesStore.remove(record);
					});

					if (rulesIds.length > 0) {
						Ext.Ajax.request({
							url : 'deleteScriptCheckRules.do',
							params : {
								deleteIds : rulesIds.join(',')
							},
							method : 'POST',
							success : function(response, opts) {
								var success = Ext.decode(response.responseText).success;
								// 当后台数据同步成功时
								if (success) {
									scriptCheckRulesStore.reload();
									Ext.Msg.alert('提示','删除成功');
								} else {
									Ext.Msg.alert('提示','删除失败！'+Ext.decode(response.responseText).message);
								}
							},
							failure : function(result,request) {
								secureFilterRs(result,"操作失败！");
							}
						});
					}
				}
			});
		}
	}

	function queryBtnFun() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pagebar.moveFirst();
	}
});
