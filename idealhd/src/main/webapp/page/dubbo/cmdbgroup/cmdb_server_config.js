/*!
 * 资源组与服务器绑定并定制个性化参数
 *
 * Copyright: Copyright 2003
 * Company: ideal
 * Author: li_yang
 *
 * Date: 2014-8-22
 */

Ext.onReady(function() {
	Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel' ]);
	Ext.tip.QuickTipManager.init();
	var currentGroupId = bsid;
	Ext.define('serverModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'sysname',
			type : 'string'
		}, {
			name : 'appname',
			type : 'string'
		}, {
			name : 'hostName',
			type : 'string'
		}, {
			name : 'ip',
			type : 'string'
		}, {
			name : 'port',
			type : 'string',
			defaultValue : 1500
		}, {
			name : 'priority',
			type : 'int',
			defaultValue : 5
		}, {
			name : 'systemType',
			type : 'string'
		}, {
			name : 'checked',
			type : 'boolean'
		}, {
			name : 'itemClicked',
			type : 'boolean'
		}, {
			name : 'isleftScreen',
			type : 'boolean',
			defaultValue : false
		} ]
	});

	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'AgentIp',
		displayField : 'agentip',
		emptyText : '--请输入AgentIp--',
		labelWidth : 70,
		padding : '5',
		labelAlign : 'right',
		width : '22%'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '主机名称',
		displayField : 'hostname',
		emptyText : '--请输入主机名称--',
		labelWidth : 70,
		padding : '5',
		labelAlign : 'right',
		width : '22%'
	});
	var sys_name = new Ext.form.TextField({
		name : 'sysname',
		fieldLabel : '系统名称',
		displayField : 'sysname',
		emptyText : '--请输入系统名称--',
		labelWidth : 70,
		padding : '5',
		labelAlign : 'right',
		width : '22%'
	});
	var formPanel = Ext.create('Ext.form.Panel', {
		region : 'north',
		width : contentPanel.getWidth(),
		height : 60,
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [ agent_ip, sys_name, host_name, {
				xtype : 'button',
				text : '查询',
				cls : 'Common_Btn',
				handler : function() {
					agent_store.load();
					choosedStore.load();
				}
			}, {
				xtype : 'button',
				text : '清空',
				cls : 'Common_Btn',
				handler : function() {
					recover();
				}
			}, {
				xtype : 'button',
				text : '还原',
				cls : 'Common_Btn',
				handler : function() {
					agent_store.load();
					choosedStore.load();
				}
			}, {
				xtype : 'button',
				text : '保存',
				cls : 'Common_Btn',
				handler : onSaveListener
			} ]
		} ]
	});

	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	var chosedSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});

	var choosedStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'serverModel',
		proxy : {
			type : 'ajax',
			url : 'cmdbServers.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	choosedStore.addListener('load', function() {
		var records = [];// 存放选中记录
		for (var i = 0; i < choosedStore.getCount(); i++) {
			var record = choosedStore.getAt(i);
			if (record.data.checked) {
				records.push(record);
			}
		}
		chosedSelModel.select(records);// 选中记录
	});

	choosedStore.on('beforeload', function(store, options) {
		var new_params = {
			hostname : host_name.getValue(),
			sysname : sys_name.getValue(),
			agentip : agent_ip.getValue(),
			isChoosed : true,
			groupId : currentGroupId
		};

		Ext.apply(choosedStore.proxy.extraParams, new_params);
	});
	var agent_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'serverModel',
		proxy : {
			type : 'ajax',
			url : 'getCmdbServer.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	agent_store.addListener('load', function() {
		var records = [];// 存放选中记录
		for (var i = 0; i < agent_store.getCount(); i++) {
			var record = agent_store.getAt(i);
			if (record.data.checked) {
				records.push(record);
			}
		}
		selModel.select(records);// 选中记录
	});

	agent_store.on('beforeload', function(store, options) {
		var new_params = {
			// hostname : formPanel.getForm().findField("host_name")
			// .getValue(),
			hostname : host_name.getValue(),
			sysname : sys_name.getValue(),
			agentip : agent_ip.getValue(),
			isChoosed : false,
			groupId : currentGroupId
		};

		Ext.apply(agent_store.proxy.extraParams, new_params);
	});

	var pageBar = Ext.create('Ext.PagingToolbar', {
		store : agent_store,
		dock : 'bottom',
		displayInfo : true
	});

	var unChosedServList = Ext.create('Ext.grid.Panel', {
		region : 'center',
		title : '待选择的服务器',
		width : "50%",
		bbar : pageBar,
		multiSelect : true,
		split : true,
		// plugins : [ cellEditing ],
		// xtype: 'cell-editing',
		// frame : true,
		viewConfig : {
			plugins : {
				ptype : 'gridviewdragdrop',
				dragGroup : 'secondGridDDGroup',
				dropGroup : 'firstGridDDGroup'
			},
			listeners : {
				drop : function(node, data, dropRec, dropPosition) {
					var dropOn = dropRec ? ' ' + dropPosition + ' '
							+ dropRec.get('name') : ' on empty view';
				}
			}
		},
		columnLines : true,
		emptyText : '没有服务器信息',
		store : agent_store,
		// selModel: selModel,
		// selModel: {
		// selType: 'cellmodel'
		// },
		columns : [
				{
					text : '编号',
					width : 40,
					sortable : true,
					hidden : true,
					dataIndex : 'id'
				},
				{
					text : '序号',
					xtype : 'rownumberer',
					width : 40
				},
				{
					xtype : 'checkcolumn',
					header : '选中',
					hidden : true,
					dataIndex : 'checked',
					width : 95,
					stopSelection : false,
					listeners : {
						checkchange : function(column, recordIndex, checked) {
							if (checked)
								unChosedServList.down('#view').setDisabled(
										false);
							else {
								var serverStore = unChosedServList.getStore();
								var storeCnt = serverStore.getCount();
								var isChecked = null;
								var cnt = 0;
								for (var i = 0; i < storeCnt; i++) {
									isChecked = serverStore.getAt(i).get(
											'checked');
									if (isChecked == true) {
										cnt++;
									}
								}
							}

							/*
							 * if(cnt==0){
							 * unChosedServList.down('#view').setDisabled(true); }
							 */
						}
					}
				},
				{
					text : '系统名',
					dataIndex : 'sysname',
					sortable : true,
					flex : 1,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '主机名',
					dataIndex : 'hostName',
					width : 100,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '应用名',
					dataIndex : 'appname',
					width : 100,
					hidden : true,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : 'IP',
					dataIndex : 'ip',
					width : 130,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '端口号',
					dataIndex : 'port',
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '优先级',
					dataIndex : 'priority',
					sortable : true,
					hidden : true,
					// editor : {
					// allowBlank : false
					// }
					editor : new Ext.form.field.ComboBox({
						typeAhead : true,
						triggerAction : 'all',
						editable : false,
						store : [ [ 1, 1 ], [ 2, 2 ], [ 3, 3 ], [ 4, 4 ],
								[ 5, 5 ], [ 6, 6 ], [ 7, 7 ], [ 8, 8 ],
								[ 9, 9 ], [ 10, 10 ] ]
					})
				}, {
					text : '应用标识',
					dataIndex : 'systemType',
					flex : 1,
					hidden : true,
					sortable : true,
					editor : {
						allowBlank : false
					}
				} ],
		listeners : {
			itemclick : function(dv, record, item, index, e) {
				var groupId = currentGroupId;
				// if(groupId){
				agent_store.each(function(rec) {
					rec.set('itemClicked', false);
				});
				record.set('itemClicked', true);
				currentServerId = record.get('id');
				// serverPara.down('#add').setDisabled(false);
				// serverParaStore.getProxy().setExtraParam('groupId',
				// groupId);
				// serverParaStore.getProxy().setExtraParam('serverId',
				// record.get('id'));
				// serverParaStore.load();
				// }else{
				// Ext.Msg.alert('提示',
				// '请选择资源组，然后可以查看服务器的个性化参数配置!');
				// }
			}
		}
	});
	queryWhere();

	var ChosedServList = Ext.create('Ext.grid.Panel', {
		region : 'west',
		title : '绑定服务器',
		width : "50%",
		multiSelect : true,
		split : true,
		// plugins : [ chosedcellEditing ],
		// xtype: 'cell-editing',
		// frame : true,
		viewConfig : {
			plugins : {
				ptype : 'gridviewdragdrop',
				dragGroup : 'firstGridDDGroup',
				dropGroup : 'secondGridDDGroup'
			},
			listeners : {
				drop : function(node, data, dropRec, dropPosition) {
					var dropOn = dropRec ? ' ' + dropPosition + ' '
							+ dropRec.get('name') : ' on empty view';
				}
			}
		},
		columnLines : true,
		emptyText : '没有服务器信息',
		store : choosedStore,
		// selModel: selModel,
		// selModel: {
		// selType: 'cellmodel'
		// },
		columns : [
				{
					text : '编号',
					width : 40,
					sortable : true,
					hidden : true,
					dataIndex : 'id'
				},
				{
					text : '序号',
					xtype : 'rownumberer',
					width : 40
				},
				{
					xtype : 'checkcolumn',
					header : '选中',
					hidden : true,
					dataIndex : 'checked',
					width : 95,
					stopSelection : false,
					listeners : {
						checkchange : function(column, recordIndex, checked) {
							if (checked)
								unChosedServList.down('#view').setDisabled(
										false);
							else {
								var serverStore = unChosedServList.getStore();
								var storeCnt = serverStore.getCount();
								var isChecked = null;
								var cnt = 0;
								for (var i = 0; i < storeCnt; i++) {
									isChecked = serverStore.getAt(i).get(
											'checked');
									if (isChecked == true) {
										cnt++;
									}
								}
							}

							/*
							 * if(cnt==0){
							 * unChosedServList.down('#view').setDisabled(true); }
							 */
						}
					}
				},
				{
					text : '系统名',
					dataIndex : 'sysname',
					sortable : true,
					flex : 1,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '主机名',
					dataIndex : 'hostName',
					width : 150,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '应用名',
					dataIndex : 'appname',
					width : 150,
					hidden : true,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '服务器名',
					dataIndex : 'hostName',
					hidden : true,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : 'IP',
					dataIndex : 'ip',
					width : 130,
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '端口号',
					dataIndex : 'port',
					sortable : true,
					editor : {
						allowBlank : false
					}
				},
				{
					text : '优先级',
					dataIndex : 'priority',
					hidden : true,
					sortable : true,
					editor : new Ext.form.field.ComboBox({
						typeAhead : true,
						triggerAction : 'all',
						editable : false,
						store : [ [ 1, 1 ], [ 2, 2 ], [ 3, 3 ], [ 4, 4 ],
								[ 5, 5 ], [ 6, 6 ], [ 7, 7 ], [ 8, 8 ],
								[ 9, 9 ], [ 10, 10 ] ]
					})
				}, {
					text : '应用标识',
					dataIndex : 'systemType',
					hidden : true,
					flex : 1,
					sortable : true,
					editor : {
						allowBlank : false
					}
				} ],
		// viewConfig: {
		// getRowClass: function(record, rowIndex, rowParams, store){
		// if (record.get("itemClicked")) {
		// return 'x-grid-record-seablue';
		// }
		// }
		// },
		// dockedItems : [ formPanel ],
		listeners : {
			itemclick : function(dv, record, item, index, e) {
				var groupId = currentGroupId;
				// if(groupId){
				agent_store.each(function(rec) {
					rec.set('itemClicked', false);
				});
				record.set('itemClicked', true);
				currentServerId = record.get('id');
				// serverPara.down('#add').setDisabled(false);
				// serverParaStore.getProxy().setExtraParam('groupId',
				// groupId);
				// serverParaStore.getProxy().setExtraParam('serverId',
				// record.get('id'));
				// serverParaStore.load();
				// }else{
				// Ext.Msg.alert('提示', '请选择资源组，然后可以查看服务器的个性化参数配置!');
				// }
			}
		}
	});

	var server_config_panel = Ext.create('Ext.panel.Panel', {
		width : contentPanel.getWidth()-20,
		height : contentPanel.getHeight()-40,
		layout : 'border',
		header : false,
		border : false,
		items : [ formPanel, ChosedServList, unChosedServList ],
		renderTo : "cmdb-servers"
	});
	contentPanel.on('resize', function() {
		server_config_panel.setWidth(contentPanel.getWidth()-20);
		server_config_panel.setHeight(contentPanel.getHeight()-40);
		// search_form.setWidth(contentPanel.getWidth()-300);
		// search_form.getForm().findField("serviceName").setWidth(contentPanel.getWidth()*0.3);
		// search_form.getForm().findField("status").setWidth(contentPanel.getWidth()*0.2);
	});
	/*
	 * resizePanel(function(){
	 * //server_config_panel.setWidth(contentPanel.getWidth()-15);
	 * //server_config_panel.setHeight(contentPanel.getHeight()-100); });
	 */

	// 数组功能扩展
	Array.prototype.each = function(fn) {
		fn = fn || Function.K;
		var a = [];
		var args = Array.prototype.slice.call(arguments, 1);
		for (var i = 0; i < this.length; i++) {
			var res = fn.apply(this, [ this[i], i ].concat(args));
			if (res != null)
				a.push(res);
		}
		return a;
	};
	// 数组是否包含指定元素
	Array.prototype.contains = function(suArr) {
		for (var i = 0; i < this.length; i++) {
			if (this[i] == suArr) {
				return true;
			}
		}
		return false;
	};
	// 不重复元素构成的数组
	Array.prototype.uniquelize = function() {
		var ra = new Array();
		for (var i = 0; i < this.length; i++) {
			if (!ra.contains(this[i])) {
				ra.push(this[i]);
			}
		}
		return ra;
	};
	// 两个数组并集
	Array.union = function(a, b) {
		return a.concat(b).uniquelize();
	};

	function onSaveListener() {
		var m = [];
		m = agent_store.getModifiedRecords();
		if (-1 == currentGroupId && m.length < 1) {
			Ext.Msg.alert('提示', '请先选择资源组后，再进行绑定操作!');
			agent_store.reload();
			choosedStore.reload();
			return;
		}
		onSaveScreen();
	}

	/**
	 * @desc 单击保存按钮后，对“右屏”即“待选择的服务器”面板。中的数据进行保存。
	 *       右屏保存时，仅仅对对服务器信息进行保存，不存在与资源组的绑定关系，
	 *       所以每条记录的checed属性必须全部为为false。后台才不进行绑定
	 */
	function onSaveScreen() {
		var leftFlag = 0;
		var rightFlag = 0;
		var leftDrops = []; // 从右屏拽到左屏的数据,是需要与资源组进行绑定的服务器
		agent_store.each(function(rec) {
			if (true == rec.get("checked")) {
				leftFlag = 1;
				// rec.set("checked", false);
				leftDrops.push(rec.get("id"));
			}
		});
		var rightDrops = []; // 从右屏拽到左屏的数据,是需要与资源组进行绑定的服务器
		choosedStore.each(function(rec) {
			if (false == rec.get("checked")) {
				rightFlag = 1;
				// rec.set("checked", true);
				rightDrops.push(rec.get("id"));
			}
		});

		// var m = agent_store.getModifiedRecords();
		// alert(m.length);
		// var jsonData = "[";
		// if (m.length > 0) {
		// for (var i = 0, len = m.length; i < len; i++) {
		// var ss = Ext.JSON.encode(m[i].data);
		// if (i == 0)
		// jsonData = jsonData + ss;
		// else
		// jsonData = jsonData + "," + ss;
		// }
		// leftFlag = 1;
		// }
		// jsonData = jsonData + "]";
		//
		// var rightDrops = []; // 从右屏拽到左屏的数据,是需要与资源组进行绑定的服务器
		// choosedStore.each(function(rec) {
		// if (false == rec.get("checked")) {
		// rec.set("checked", true);
		// rightDrops.push(rec);
		// }
		// });
		//
		// var m = choosedStore.getSelectionModel().getSelection();
		// alert(m.length);
		// var jsonDataL = "[";
		// if (m.length < 1 && rightDrops.length < 1) {
		// } else {
		// for (var i = 0, len = m.length; i < len; i++) {
		// var ss = Ext.JSON.encode(m[i].data);
		// if (i == 0)
		// jsonDataL = jsonDataL + ss;
		// else
		// jsonDataL = jsonDataL + "," + ss;
		// }
		// rightFlag = 1;
		// }
		// jsonDataL = jsonDataL + "]";
		if (leftFlag != 0 || rightFlag != 0) {

			// 右屏的Ajax请求住宅要负责解绑操作
			Ext.Ajax.request({
				url : 'serversCmdbSave.do',
				method : 'POST',
				params : {
					jsonData : leftDrops,
					jsonDataL : rightDrops,
					currentGroupId : currentGroupId
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						agent_store.reload();
						choosedStore.reload();
						Ext.Msg.alert('提示', '操作成功执行');
					} else {
						Ext.Msg.alert('提示', '保存失败');
					}
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '保存失败');
				}
			});
		}

	}

	function recover() {
		host_name.setValue('');
		sys_name.setValue('');
		agent_ip.setValue('');
	}

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	function queryWhere() {
		agent_store.reload();
	}
	// store.getProxy().setExtraParam("groupId", bsid);
	// store.getProxy().setExtraParam("isChoosed", false);
	// store.getProxy().setExtraParam("curServer", curServer);
	// store.load();
	//
	// choosedStore.getProxy().setExtraParam("groupId", bsid);
	// choosedStore.getProxy().setExtraParam("isChoosed", true);
	// choosedStore.load();
});