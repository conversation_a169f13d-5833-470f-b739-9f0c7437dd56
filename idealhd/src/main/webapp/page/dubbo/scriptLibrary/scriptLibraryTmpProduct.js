var scriptDetailWin;
var shareWindow;
var commentWindow;
var commentFormPanel;
var whichScriptIsClick;
var shareSearchValue = '';
Ext.onReady(function() {
//	envname = '生产环境';
	var envSign=1;
	var myScriptTitleName='我的脚本';
	var scriptBazaarTitleName='脚本库';
	if(envname=='生产环境'){
		envSign=0;
		myScriptTitleName='我的作业';
		scriptBazaarTitleName='作业库';
	}
	Ext.override(Ext.toolbar.Paging, {
		getPagingItems : function() {
			var me = this;
			var aa = [ {
						itemId : 'first',
						tooltip : me.firstText,
						overflowText : me.firstText,
						iconCls : Ext.baseCSSPrefix + 'tbar-page-first',
						disabled : true,
						handler : me.moveFirst,
						scope : me
					}, {
						itemId : 'prev',
						tooltip : me.prevText,
						overflowText : me.prevText,
						iconCls : Ext.baseCSSPrefix + 'tbar-page-prev',
						disabled : true,
						handler : me.movePrevious,
						scope : me
					} ];
					if (me.beforePageText != '' || me.beforePageText != '') {
						aa.push(me.beforePageText);
						aa.push({
							xtype : 'displayfield',
							itemId : 'inputItem',
							name : 'inputItem',
							cls : Ext.baseCSSPrefix + 'tbar-page-number',
							allowDecimals : false,
							minValue : 1,
							hideTrigger : true,
							enableKeyEvents : true,
							keyNavEnabled : false,
							selectOnFocus : true,
							submitValue : false,
							// mark it as not a field so the form will not catch
							// it when getting fields
							isFormField : false,
							width : 10,
							margins : '-2 0 0 0',
							listeners : {
								scope : me,
								keydown : me.onPagingKeyDown,
								blur : me.onPagingBlur
							}
						});
						aa.push({
							xtype : 'tbtext',
							itemId : 'afterTextItem',
							text : Ext.String.format(me.afterPageText, 1)
						});
					}

					aa.push({
						itemId : 'next',
						tooltip : me.nextText,
						overflowText : me.nextText,
						iconCls : Ext.baseCSSPrefix + 'tbar-page-next',
						disabled : true,
						handler : me.moveNext,
						scope : me
					});
					aa.push({
						itemId : 'last',
						tooltip : me.lastText,
						overflowText : me.lastText,
						iconCls : Ext.baseCSSPrefix + 'tbar-page-last',
						disabled : true,
						handler : me.moveLast,
						scope : me
					});
					aa.push({
						itemId : 'refresh',
						tooltip : me.refreshText,
						overflowText : me.refreshText,
						iconCls : Ext.baseCSSPrefix + 'tbar-loading',
						handler : me.doRefresh,
						scope : me
					});
					return aa;
				}
			});

			var bussData = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'bsName' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var bussTypeData = Ext.create('Ext.data.Store', {
				fields : [ 'sysTypeId', 'sysType' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var cataStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
					"id" : "-1",
					"name" : "全部"
				}, {
					"id" : "sh",
					"name" : "shell"
				}, {
					"id" : "bat",
					"name" : "bat"
				}, {
					"id" : "py",
					"name" : "python"
				}, {
					"id" : "perl",
					"name" : "perl"
				} ]
			});
			var bussCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'sysName',
				labelWidth : 70,
				columnWidth : .33,
				queryMode : 'local',
				fieldLabel : '一级分类',
				padding : '5',
				displayField : 'bsName',
				valueField : 'iid',
				editable : false,
				emptyText : '--请选择一级分类--',
				store : bussData,
				listeners : {
					change : function() { // old is keyup
						bussTypeCb.clearValue();
						bussTypeCb.applyEmptyText();
						bussTypeCb.getPicker().getSelectionModel()
								.doMultiSelect([], false);
						bussTypeData.load({
							params : {
								fk : this.value
							}
						});
					}
				}
			});

			/** 二级分类* */
			var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'bussType',
				padding : '5',
				labelWidth : 70,
				columnWidth : .33,
				queryMode : 'local',
				fieldLabel : '二级分类',
				displayField : 'sysType',
				valueField : 'sysTypeId',
				editable : false,
				emptyText : '--请选择二级分类--',
				store : bussTypeData
			});

			/** 脚本类型* */
			var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
				name : 'scriptTypeParam',
				padding : '5',
				labelWidth : 70,
				columnWidth : .33,
				queryMode : 'local',
				fieldLabel : '脚本类型',
				displayField : 'name',
				valueField : 'id',
				editable : false,
				emptyText : '--请选择二级分类--',
				store : cataStore
			});

			var sName = new Ext.form.TextField({
				name : 'serverName',
				fieldLabel : '服务名称',
				displayField : 'serverName',
				emptyText : '--请输入服务名称--',
				labelWidth : 70,
				padding : '5',
				columnWidth : .33
			});
			var scName = new Ext.form.TextField({
				name : 'scriptName',
				fieldLabel : '脚本名称',
				displayField : 'scriptName',
				emptyText : '--请输入脚本名称--',
				labelWidth : 70,
				padding : '5',
				columnWidth : .33
			});

			var search_form = Ext.create('Ext.form.Panel', {
				layout : 'anchor',
				buttonAlign : 'center',
				border : false,
				items : [ {
					layout : 'form',
					anchor : '98%',
					padding : '5 0 5 0',
					border : false,
					items : [ {
						layout : 'column',
						border : false,
						items : [ sName, scName, scriptTypeParam ]
					}, {
						layout : 'column',
						border : false,
						items : [ bussCb, bussTypeCb, {
							xtype : 'button',
							columnWidth : .125,
							// width:60,
							height : 30,
							text : '查询',
							margin : '5 0 0 80',
							cls : 'Common_Btn',
							handler : function() {
								pageBar.moveFirst();
							}
						}, {
							xtype : 'button',
							columnWidth : .068,
							// width:60,
							height : 30,
							text : '清空',
							cls : 'Common_Btn',
							margin : '5 0 0 15',
							handler : function() {
								clearQueryWhere();
							}
						} ]
					} ]
				} ]
			});

			Ext.define('scriptServiceReleaseModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'serviceName',
					type : 'string'
				}, {
					name : 'sysName',
					type : 'string'
				}, {
					name : 'bussName',
					type : 'string'
				}, {
					name : 'buss',
					type : 'string'
				}, {
					name : 'bussType',
					type : 'string'
				}, {
					name : 'bussId',
					type : 'int'
				}, {
					name : 'bussTypeId',
					type : 'int'
				}, {
					name : 'scriptType',
					type : 'string'
				}, {
					name : 'isflow',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'servicePara',
					type : 'string'
				}, {
					name : 'serviceState',
					type : 'string'
				}, {
					name : 'platForm',
					type : 'string'
				}, {
					name : 'version',
					type : 'string'
				}, {
					name : 'content',
					type : 'string'
				}, {
					name : 'scriptLevel',
					type : 'int'
				}, {
					name : 'status',
					type : 'int'
				} ]
			});

			var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 10,
				model : 'scriptServiceReleaseModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryServiceForShare.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			scriptServiceReleaseStore.on('beforeload',
					function(store, options) {
						var new_params = {
							bussId : search_form.getForm().findField("sysName")
									.getValue(),
							bussTypeId : search_form.getForm().findField(
									"bussType").getValue(),
							scriptName : search_form.getForm().findField(
									"scriptName").getValue(),
							serviceName : search_form.getForm().findField(
									"serverName").getValue(),
							scriptType : search_form.getForm().findField(
									"scriptTypeParam").getValue(),
							onlyScript : 1
						};

						Ext.apply(scriptServiceReleaseStore.proxy.extraParams,
								new_params);
					});

			var scriptServiceReleaseColumns = [ {
				text : '序号',
				xtype : 'rownumberer',
				width : 40
			}, {
				text : '服务主键',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			}, {
				text : '服务名称',
				dataIndex : 'serviceName',
				width : 200,
				flex : 1
			}, {
				text : '适用平台',
				dataIndex : 'platForm',
				width : 200
			}, {
				text : '一级分类',
				dataIndex : 'buss',
				width : 200,
				flex : 1
			}, {
				text : '二级分类',
				dataIndex : 'bussType',
				width : 250,
				flex : 1
			}, {
				text : '脚本名称',
				dataIndex : 'scriptName',
				width : 260,
				flex : 1
			}, {
				text : '脚本类型',
				dataIndex : 'scriptType',
				width : 80,
				flex : 1,
				renderer : function(value, p, record, rowIndex) {
					var isflow = record.get('isflow');
					if (isflow == "1") {
						return "组合"
					} else {
						return value;
					}
				}
			}, {
				text : '版本',
				dataIndex : 'version',
				width : 200
			} ];

			// 分页工具
			var pageBar = Ext.create('Ext.PagingToolbar', {
				store : scriptServiceReleaseStore,
				dock : 'bottom',
				displayInfo : true,
				emptyMsg : '找不到任何记录'
			});

			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});

			var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
				width : '100%',
				height : contentPanel.getHeight() - 196,
				border:false,
				id : 'releaseGrid',
				store : scriptServiceReleaseStore,
				selModel : selModel,
				bbar : pageBar,
				border : true,
				columnLines : true,
				columns : scriptServiceReleaseColumns,
				animCollapse : false
			});

			var mainPanel11 = Ext.create('Ext.panel.Panel', {
				border : false,
				bodyPadding : 5,
				items : [ search_form, scriptServiceReleaseGrid ]
			});

			if (shareWindow == undefined || !shareWindow.isVisible()) {
				shareWindow = Ext.create('Ext.window.Window',{
					title : scriptBazaarTitleName,
					modal : true,
					closeAction : 'hide',
					constrain : true,
					autoScroll : true,
					width : contentPanel.getWidth(),
					height : contentPanel.getHeight(),
					draggable : false,
					resizable : false,
					layout : 'fit',
					items : [ mainPanel11 ],
					buttonAlign : 'center',
					buttons : [ {
						xtype : 'button',
						icon : '',
						handler : function() {
							var datas = scriptServiceReleaseGrid.getSelectionModel().getSelection();
							var ids = [];
							for (var i = 0, len = datas.length; i < len; i++) {
								ids.push(datas[i].data.iid);
							}
							Ext.Ajax.request({
								url : 'addScriptToMyCollection.do',
								method : 'POST',
								params : {
									ids : ids
								},
								success : function(response, opts) {
									var success = Ext.decode(response.responseText).success;
									shareWindow.close();
									myCollectionPageBar.moveFirst();
									Ext.MessageBox.alert("提示","收藏成功！");
								},
								failure : function(result, request) {
									secureFilterRs(result,"操作失败！");
									shareWindow.close();
								}
							});
						}
					} ]
				});
			}

			commentFormPanel = Ext.create('Ext.form.FormPanel', {
				width : 400,
				border : false,
				bodyPadding : 10,
				items : [ {
					xtype : 'textareafield',
					grow : true,
					name : 'comm',
					fieldLabel : '填写评论',
					anchor : '100%',
					height : 100
				}]
			});

			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};

			if (commentWindow == undefined || !commentWindow.isVisible()) {
				commentWindow = Ext.create('Ext.window.Window',{
					title : '评 论',
					modal : true,
					closeAction : 'hide',
					constrain : true,
					autoScroll : true,
					border : false,
					width : 500,
					height : 200,
					draggable : false,
					// 禁止拖动
					resizable : false,
					// 禁止缩放
					layout : 'fit',
					items : [ commentFormPanel ],
					buttonAlign : 'center',
					buttons : [ {
						xtype : 'button',
						icon : '',
						text : '提交',
						margin : '0 0 0 5',
						handler : function() {
							var content = commentFormPanel.getForm().findField("comm").getValue();
							if (content.trim() == '') {
								Ext.Msg.alert('提示', '评论内容不可以为空');
								return;
							}
							if (fucCheckLength(content) > 3000) {
								Ext.Msg.alert('提示', '评论字符数不可超过3000！');
								return;
							}
							if (whichScriptIsClick) {
								var iid = whichScriptIsClick;
								$.post("scriptService/commentForShare.do",{
											content : content,
											iid : iid
										},
										function(res) {
											var now = new Date();
											var year = now.getFullYear(); // 年
											var month = now.getMonth() + 1; // 月
											var day = now.getDate();
											var current = year+ '年' + month+ '月' + day+ '日';
											if (res.success) {
												var c = '<div class="Comment_List1">'+ content
													      + '<div class="List_tool">'
																+ '<span class="List_left">'
																	+ current
																+ '</span>'
																+ '<span class="List_right"><img class="like-it" data-id="'+ res.pk+ '" src="images/unlike.png" align="absmiddle"/><font>(0)</font></span>'
														  + '</div>'
													  + '</div>';
												var tba = tabs.getActiveTab();
												var ly = '';
												if (tba.title == '热门脚本') {
													ly = 'hot';
												} else if (tba.title == '最新脚本') {
													ly = 'new';
												} else if (tba.title == scriptBazaarTitleName) {
													ly = 'share';
												}
												ly = "_job";
												$('#comment-overview-'+ ly).prepend(c);
												commentFormPanel.getForm().findField("comm").setValue('');
												commentWindow.close();
											} else {
												commentWindow.close();
												Ext.Msg.alert('提示',res.message);
											}
										});
								}
							}
					}]
				});
			}

			function clearQueryWhere() {
				search_form.getForm().findField("sysName").setValue('');
				search_form.getForm().findField("bussType").setValue('');
				search_form.getForm().findField("scriptName").setValue('');
				search_form.getForm().findField("serverName").setValue('');
				search_form.getForm().findField("scriptTypeParam").setValue('');
			}

			// 对Date的扩展，将 Date 转化为指定格式的String
			// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
			// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
			// 例子：
			// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02
			// 08:09:04.423
			// (new Date()).Format("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
			Date.prototype.Format = function(fmt) { // author: meizz
				var o = {
					"M+" : this.getMonth() + 1,
					// 月份
					"d+" : this.getDate(),
					// 日
					"h+" : this.getHours(),
					// 小时
					"m+" : this.getMinutes(),
					// 分
					"s+" : this.getSeconds(),
					// 秒
					"q+" : Math.floor((this.getMonth() + 3) / 3),
					// 季度
					"S" : this.getMilliseconds()
				// 毫秒
				};
				if (/(y+)/.test(fmt))
					fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
				for ( var k in o)
					if (new RegExp("(" + k + ")").test(fmt))
						fmt = fmt.replace(RegExp.$1,(RegExp.$1.length == 1) ? (o[k]): (("00" + o[k]).substr(("" + o[k]).length)));
				return fmt;
			}

			$.post("scriptService/scriptStatistic.do", {}, function(res) {
				var yywhl = res.yywhl;
				var xtwhl = res.xtwhl;
				var wlwhl = res.wlwhl;
				var wjczl = res.wjczl;
				var dsfrjl = res.dsfrjl;
				var ty = res.ty;
				$('#shell_total').html(yywhl ? yywhl : 0);
				$('#bat_total').html(xtwhl ? xtwhl : 0);
				$('#perl_total').html(wlwhl ? wlwhl : 0);
				$('#python_total').html(wjczl ? wjczl : 0);
				$('#dsfrjl_total').html(dsfrjl ? dsfrjl : 0);
				$('#ty_total').html(ty ? ty : 0);
			});

			$(document).off('click', '.sys-type-color').on('click','.sys-type-color', function() {
				$('.sys-type-color').removeClass('script_select');
				var $this = $(this);
				$this.addClass('script_select');
				var sys_type = $this.data('sys-type');
				var tba = tabs.getActiveTab();
				tba.getLoader().load({
					params : {
						'serviceName' : shareSearchValue,
						'sysType' : sys_type
					}
				});
			});

			$(document).off('click', '#scr_lib_myself_search').on('click','#scr_lib_myself_search', function(e) {
				myScriptsPageBar.moveFirst();
			});
			
			$(document).off('click', '#job_lib_myself_search').on('click','#job_lib_myself_search', function(e) {
				myJobsPageBar.moveFirst();
			});

			$(document).off('click', '#scr_lib_mycollection_search').on('click', '#scr_lib_mycollection_search', function(e) {
				myCollectionPageBar.moveFirst();
			});

			$(document).off('click', '#scr_lib_share_search').on('click','#scr_lib_share_search',function(e) {
				shareSearchValue = $('#scr_lib_share_search_value').val();
				var tba = tabs.getActiveTab();
				tba.getLoader().load({
					params : {
						'serviceName' : shareSearchValue
					}
				});
			});

			$(document).off('keypress', '#scr_lib_myself_search_value').on('keypress', '#scr_lib_myself_search_value',function(event) {
				if (event.keyCode == "13") {
					myScriptsPageBar.moveFirst();
				}
			});
			
			$(document).off('keypress', '#job_lib_myself_search_value').on('keypress', '#job_lib_myself_search_value',function(event) {
				if (event.keyCode == "13") {
					myJobsPageBar.moveFirst();
				}
			});

			$(document).off('keypress', '#scr_lib_mycollection_search_value').on('keypress', '#scr_lib_mycollection_search_value',function(event) {
				if (event.keyCode == "13") {
					myCollectionPageBar.moveFirst();
				}
			});

			$(document).off('keypress', '#scr_lib_share_search_value').on('keypress','#scr_lib_share_search_value',function(event) {
				if (event.keyCode == "13") {
					shareSearchValue = $('#scr_lib_share_search_value').val();
					var tba = tabs.getActiveTab();
					tba.getLoader().load({
						params : {
							'serviceName' : shareSearchValue
						}
					});
				}
			});

			$(document).off('click', 'img.like-it').on('click','img.like-it',function(e) {
				var $this = $(this);
				var iid = $this.attr('data-id');
				$.post('scriptService/commentLikeIt.do', {
					iid : iid
				}, function(res) {
					if (res.success) {
						$this.next().html('(' + res.likeNum + ')');
						$this.attr('src', 'images/' + res.likeImg+ '.png');
					} else {
						Ext.Msg.alert('提示', res.message);
					}
				});
			});

			$('#my-scr-more').off('click').on('click', function(res) {
				destroyRubbish(); // 销毁本页垃圾
				contentPanel.getLoader().load({
					url : 'forwardScriptServiceRelease.do',
					scripts : true
				});
			});

			$('#scr-lib-more').off('click').on('click', function(res) {
				destroyRubbish(); // 销毁本页垃圾
				contentPanel.getLoader().load({
					url : 'forwardScriptShare.do',
					scripts : true
				});
			});

			$('#scr-task-more').off('click').on('click', function(res) {
				showMessageWindow();
			});

			$('body').off('click', '#add-scr').on('click', '#add-scr',function(res) {
				shareWindow.show();
				pageBar.moveFirst();
			});

			var myScriptStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 10,
				model : 'scriptServiceReleaseModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryServiceForMySelf.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			myScriptStore.on('beforeload', function(store, options) {
				var new_params = {
					onlyScript : envSign,
					scriptStatus: 1, // 已上线
					serviceName : $('#scr_lib_myself_search_value').val()
				};

				Ext.apply(myScriptStore.proxy.extraParams, new_params);
			});
			var myJobStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 10,
				model : 'scriptServiceReleaseModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryServiceForMySelf.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			
			myJobStore.on('beforeload', function(store, options) {
				var new_params = {
						onlyScript : 0,
						serviceName : $('#job_lib_myself_search_value').val()
				};
				
				Ext.apply(myJobStore.proxy.extraParams, new_params);
			});

			var myScriptsPageBar = Ext.create('Ext.PagingToolbar', {
				store : myScriptStore,
				dock : 'bottom',
				displayInfo : false,
				afterPageText : '页',
				beforePageText : '第',
				firstText : '第一页 ',
				prevText : '前一页',
				nextText : '下一页',
				lastText : '最后一页',
				refreshText : '刷新',
				displayMsg : '',
				emptyMsg : '找不到任何记录',
				
			});
			
			var myJobsPageBar = Ext.create('Ext.PagingToolbar', {
				store : myJobStore,
				dock : 'bottom',
				displayInfo : false,
				afterPageText : '页',
				beforePageText : '第',
				firstText : '第一页 ',
				prevText : '前一页',
				nextText : '下一页',
				lastText : '最后一页',
				refreshText : '刷新',
				displayMsg : '',
				emptyMsg : '找不到任何记录',
				
			});

			var myScriptsDataView = Ext.create('Ext.view.View',{
				store : myScriptStore,
				padding : 5,
				tpl : [
						'<tpl for=".">',
						'<div class="Script_library myScriptM">',
						'<table cellpadding="0" cellspacing="0" border="0" width="100%">',
						'<tr>',
						'<td width="26"><div class="{scriptType:this.humanScriptType}"></div></td>',
						'<td><div class="Script_textF2">{serviceName}</div></td>',
						'</tr>', '</table>', '</div>',
						'</tpl>', {
							humanScriptType : function(name) {
								var scriptType = '';
								if (name == 'sh') {
									scriptType = 'shell';
								} else if (name == 'bat') {
									scriptType = 'bat';
								} else if (name == 'py') {
									scriptType = 'python';
								} else if (name == 'perl') {
									scriptType = 'perl';
								} else if (name == '组合') {
									scriptType = 'ssflow';
								}
								return scriptType;
							}
						} ],
				multiSelect : false,
				height : '100%',
				trackOver : true,
				overItemCls : 'x-item-over',
				itemSelector : 'div.myScriptM',
				emptyText : '没有脚本',
				listeners : {
					'itemdblclick' : function(view, record,item, idx, event, opts) {
						if (record.get('scriptType') == '组合') {
							popNewTab('任务申请','flowCustomizedInitScriptServiceGFSSAUDI.do?iid='+ record.get('iid')
										+ '&actionType=audi&scriptType=&scriptLevel='+record.get('scriptLevel')+'&flag=1&serviceName='+ record.get('serviceName')
										+ '&bussId='+ record.get('bussId')
										+ '&bussTypeId='+ record.get('bussTypeId'),{},10, true);
						} else {
							popNewTab('我的脚本','forwardEditScript.do?serviceId='+ record.get('iid'),{},10, true)
						}
					}
				}
			});
			
			var myJobsDataView = Ext.create('Ext.view.View',{
				store : myJobStore,
				padding : 5,
				tpl : [
					'<tpl for=".">',
					'<div class="Script_library myJobM">',
					'<table cellpadding="0" cellspacing="0" border="0" width="100%">',
					'<tr>',
					'<td width="26"><div class="ssflow"></div></td>',
					'<td><div class="Script_textF2">{serviceName}</div></td>',
					'</tr>', '</table>', '</div>',
					'</tpl>'],
					multiSelect : false,
					height : '100%',
					trackOver : true,
					overItemCls : 'x-item-over',
					itemSelector : 'div.myJobM',
					emptyText : '没有作业',
					listeners : {
						'itemdblclick' : function(view, record,item, idx, event, opts) {
							popNewTab('我的作业','flowCustomizedInitScriptServiceGFSSEDIT.do?iid='+ record.get('iid')
									+ '&actionType=edit&flag=0&serviceName='+ record.get('serviceName')
									+ '&bussId='+ record.get('bussId')
									+ '&bussTypeId='+ record.get('bussTypeId'),{},10, true)
						}
					}
			});

			var myScriptsGrid = Ext.create('Ext.Panel', {
				region : 'center',
				title : '',
				width : '100%',
				autoScroll : true,
				border : false,
				items : myScriptsDataView,
				bbar : myScriptsPageBar,
			});
			var myJobsGrid = Ext.create('Ext.Panel', {
				region : 'center',
				title : '',
				width : '100%',
				autoScroll : true,
				border : false,
				items : myJobsDataView,
				bbar : myJobsPageBar,
			});

			var myScriptsSearch = Ext
					.create(
							'Ext.Panel',
							{
								region : 'north',
								title : '',
								width : '100%',
								padding : 5,
								autoScroll : true,
								border : false,
								html : ''
										+ '<div class="scriptL_Search">'
										+ '<input type="text" name="search" class="scriptL_Searchinput" placeholder="输入脚本名称" id="scr_lib_myself_search_value" />'
										+ '<a href="javascript:void(0);" class="scriptL_SearchA" id="scr_lib_myself_search"><div class="Search_button"></div></a>'
										+ '</div>',
							});
			var myJobsSearch = Ext.create('Ext.Panel', {
				region : 'north',
				title : '',
				width : '100%',
				padding : 5,
				autoScroll : true,
				border : false,
				html : ''
					+ '<div class="scriptL_Search">'
					+ '<input type="text" name="searchMyJobs" class="scriptL_Searchinput" placeholder="输入作业名称" id="job_lib_myself_search_value" />'
					+ '<a href="javascript:void(0);" class="scriptL_SearchA" id="job_lib_myself_search"><div class="Search_button"></div></a>'
					+ '</div>',
			});

			var myScriptsPanel = Ext.create('Ext.Panel', {
				region : 'west',
				title : myScriptTitleName,
				width : 260,
				autoScroll : true,
				border : true,
				layout : 'border',
				items : [ myScriptsSearch, myScriptsGrid ],
			});
			
			var myJobsPanel = Ext.create('Ext.Panel', {
				title : '我的作业',
				width : 260,
				autoScroll : true,
				border : true,
				layout : 'border',
				items : [ myJobsSearch, myJobsGrid ],
			});
			
			var myScriptAndJobTabs = null;
			if(envSign==1) {
				myScriptAndJobTabs = Ext.widget('tabpanel', {
					region : 'west',
					border: false,
					width : 260,
					activeTab: 0,
					items: [myScriptsPanel, myJobsPanel]
				});
			}

			var myCollectionStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 10,
				model : 'scriptServiceReleaseModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryServiceForMyToolBox.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			myCollectionStore.on('beforeload', function(store, options) {
				var new_params = {
					onlyScript:envSign,
					serviceName : $('#scr_lib_mycollection_search_value').val()
				};

				Ext.apply(myCollectionStore.proxy.extraParams, new_params);
			});

			var myCollectionPageBar = Ext.create('Ext.PagingToolbar', {
				store : myCollectionStore,
				dock : 'bottom',
				displayInfo : false,
				afterPageText : '页',
				beforePageText : '第',
				firstText : '第一页 ',
				prevText : '前一页',
				nextText : '下一页',
				lastText : '最后一页',
				refreshText : '刷新',
				displayMsg : '',
				emptyMsg : '找不到任何记录'
			});

			var myCollectionDataView = Ext.create('Ext.view.View',{
				store : myCollectionStore,
				padding : 5,
				tpl : [
						'<tpl for=".">',
						'<div class="Script_library myCollectionM">',
						'<table cellpadding="0" cellspacing="0" border="0" width="100%">',
						'<tr>',
						'<td width="26"><div class="{scriptType:this.humanScriptType}"></div></td>',
						'<td><div class="Script_textF2">{serviceName}</div></td>',
						'</tr>', '</table>', '</div>',
						'</tpl>', {
							humanScriptType : function(name) {
								var scriptType = '';
								if (name == 'sh') {
									scriptType = 'shell';
								} else if (name == 'bat') {
									scriptType = 'bat';
								} else if (name == 'py') {
									scriptType = 'python';
								} else if (name == 'perl') {
									scriptType = 'perl';
								} else if (name == '组合') {
									scriptType = 'ssflow';
								}
								return scriptType;
							}
						} ],
				multiSelect : false,
				height : '100%',
				trackOver : true,
				overItemCls : 'x-item-over',
				itemSelector : 'div.myCollectionM',
				listeners : {'itemdblclick' : function(view, record,item, idx, event, opts) {
					popNewTab('我的收藏','flowCustomizedInitScriptServiceGFSSCOLLECTAUDI.do?menuId=0&iid='+ record.get('iid')
							+ '&actionType=audi&scriptType=&scriptLevel='+record.get('scriptLevel')+'&flag=1&serviceName='+ record.get('serviceName')
							+ '&bussId='+ record.get('bussId')
							+ '&bussTypeId='+ record.get('bussTypeId'),{},10, true);
					}
				}
			});

			var myCollectionGrid = Ext.create('Ext.Panel', {
				region : 'center',
				title : '',
				width : '100%',
				autoScroll : true,
				border : false,
				items : myCollectionDataView,
				bbar : myCollectionPageBar,
			});

			var myCollectionSearch = Ext.create('Ext.Panel',{
				region : 'north',
				title : '',
				width : '100%',
				padding : 5,
				autoScroll : true,
				border : false,
				html : ''
						+ '<div class="scriptL_Search">'
						+ '<input type="text" name="search" class="scriptL_Searchinput" placeholder="输入脚本名称" id="scr_lib_mycollection_search_value" />'
						+ '<a href="javascript:void(0);" class="scriptL_SearchA" id="scr_lib_mycollection_search"><div class="Search_button"></div></a>'
						+ '</div>',
			});

			var myCollectionPanel = Ext.create('Ext.Panel', {
				region : 'east',
				title : '我的收藏',
				width : 260,
				autoScroll : true,
				border : true,
				layout : 'border',
				items : [ myCollectionSearch, myCollectionGrid ],
			});

			var tabs = Ext.widget('tabpanel', {
				region : 'west',
				activeTab : 0,
				width:240,
				border : true,
				margin:'5 5 0 0',
				bodyCls : 'market_Cleft',
				items : [ {
					title : '热门作业',
					loader : {
						url : 'dashboardLibTab.do',
						params : {
							'libType' : 'hot',
							'scriptType': 'job'
						},
						loadMask : true,
						scripts : true
					},
					listeners : {
						activate : function(tab) {
							tab.loader.load({
								params : {
									'serviceName' : shareSearchValue
								}
							});
						}
					}
				}, {
					title : '最新作业',
					loader : {
						url : 'dashboardLibTab.do',
						params : {
							'libType' : 'new',
							'scriptType': 'job'
						},
						loadMask : true,
						scripts : true
					},
					listeners : {
						activate : function(tab) {
							tab.loader.load({
								params : {
									'serviceName' : shareSearchValue
								}
							});
						}
					}
				}, {
					title : scriptBazaarTitleName,//脚本市场或者是作业市场
					loader : {
						url : 'dashboardLibTab.do',
						params : {
							'libType' : 'share',
							'scriptType': 'job'
						},
						loadMask : true,
						scripts : true
					},
					listeners : {
						activate : function(tab) {
							tab.loader.load({
								params : {
									'serviceName' : shareSearchValue
								}
							});
						}
					}
				} ]
			});
			
			var detailTabsForWorkStation = Ext.widget('tabpanel', {
		    	region: 'center',
		        activeTab: 0,
		        margin: '5 0 0 0',
		        deferredRender: false,
		        autoScroll: true,
		        border: false,
		        items: [{
		            title: '基本信息',
		            autoScroll: true,
		            html: ''+
			            '<table cellpadding="0" cellspacing="0" border="0" class="mar_CR_table">'+
				        '	<tr>'+
				        '    	<td width="118"><div class="Developers"></div></td>'+
				        '        <td valign="top">'+
				        '        	<table cellpadding="0" cellspacing="0" border="0" class="mar_CR_info">'+
				        '            	<tr>'+
				        '                	<td class="mar_td">开发者：</td>'+
				        '                    <td id="scr-creator-_job"></td>'+
				        '                </tr>'+
				        '                <tr>'+
				        '                	<td class="mar_td">上线时间：</td>'+
				        '                    <td id="scr-create-time-_job"></td>'+
				        '                </tr>'+
				        '                <tr>'+
				        '                	<td class="mar_td">适用平台：</td>'+
				        '                    <td id="scr-platform-_job"></td>'+
				        '                </tr>'+
				        '                <tr>'+
				        '                	<td class="mar_td">一级分类：</td>'+
				        '                    <td id="scr-buss-_job"></td>'+
				        '                </tr>'+
				        '                <tr>'+
				        '                	<td class="mar_td">二级分类：</td>'+
				        '                    <td id="scr-buss-type-_job"></td>'+
				        '                </tr>'+
				        '            </table>'+
				        '        </td>'+
				        '    </tr>'+
				        '    <tr><td colspan="2"><div class="mar_line"></div></td></tr>'+
				        '    <tr>'+
				        '    	<td colspan="2">'+
				        '			<div class="mar_desc">功能说明：</div>'+
				        '        	<div class="mar_content" id="scr_func_desc-_job"></div>'+
				        '        </td>'+
				        '    </tr>'+
				        '</table>'
		        },{
		            title: '评论',
		            autoScroll: true,
		            html: ''+
		            	'<div class="Commnet_info">                                             '+
						'	<div class="Comment_btn2 comment-window">                      '+
						'		<div class="Commnet_button Common_Btn">                                       '+
						'			<img src="images/Comment_icon.png" align="absmiddle" />评论 '+
						'		</div>                                                          '+
						'	</div>                                                              '+
						'</div>                                                                 '+
						'<div class="Comment_overview" id="comment-overview-_job'+ "" +'"></div>'
		        }]
		    });

			var topColorPanel = Ext
					.create(
							'Ext.Panel',
							{title : scriptBazaarTitleName,
								region : 'north',
								border : true,
								html : '<div class="panel-body3">'
										+ '<table cellpadding="0" cellspacing="0" border="0"                                               '
										+ '	class="Script_market">                                                                       '
										+ '	<tr>                                                                                         '
										+ '		<td>                                                                                     '
										+ '			<div class="sys-type-color Overview_shell" data-sys-type="应用维护类">               '
										+ '				<div class="script_L_icon1 script_L_common"></div>'
										+ '				<div class="script_R">'
										+ '					<span class="normal_font">'
										+ '应用维护类'
										+ '					</span>'
										+ '					<span class="Large_font" id="shell_total"></span>                      '
										+ '				</div>'
										+ '                                                                                                '
										+ '			</div>                                                                               '
										+ '				</td>                                                                            '
										+ '				<td>                                                                             '
										+ '					<div class="sys-type-color Overview_bat" data-sys-type="系统维护类">         '
										+ '					<div class="script_L_icon2 script_L_common"></div>'
										+ '					<div class="script_R">'
										+ '						<span class="normal_font">'
										+ '系统维护类'
										+ '						</span>'
										+ '						<span class="Large_font" id="bat_total"></span>                '
										+ '					</div>'
										+ '					</div>                                                                       '
										+ '				</td>                                                                            '
										+ '				<td>                                                                             '
										+ '					<div class="sys-type-color Overview_perl" data-sys-type="网络维护类">        '
										+ '						<div class="script_L_icon3 script_L_common"></div>'
										+ '						<div class="script_R">'
										+ '							<span class="normal_font">'
										+ '网络维护类'
										+ '							</span>'
										+ '							<span class="Large_font" id="perl_total"></span>               '
										+ '						</div>'
										+ '					</div>                                                                       '
										+ '				</td>                                                                            '
										+ '				<td>                                                                             '
										+ '					<div class="sys-type-color Overview_python" data-sys-type="文件操作类">      '
										+ '						<div class="script_L_icon4 script_L_common"></div>'
										+ '						<div class="script_R">'
										+ '							<span class="normal_font">'
										+ '文件操作类'
										+ '							</span>'
										+ '							<span class="Large_font" id="python_total"></span>             '
										+ '						</div>'
										+ '					</div>                                                                       '
										+ '				</td>                                                                            '
										+ '				<td>                                                                             '
										+ '					<div class="sys-type-color Overview_color1" data-sys-type="第三方软件类">    '
										+ '						<div class="script_L_icon5 script_L_common"></div>'
										+ '						<div class="script_R">'
										+ '							<span class="normal_font">'
										+ '第三方软件类'
										+ '							</span>'
										+ '							<span class="Large_font" id="dsfrjl_total"></span>           '
										+ '						</div>'
										+ '					</div>                                                                       '
										+ '				</td>                                                                            '
										+ '				<td>                                                                             '
										+ '					<div class="sys-type-color Overview_color2" data-sys-type="通用">            '
										+ '						<div class="script_L_icon6 script_L_common"></div>'
										+ '						<div class="script_R">'
										+ '							<span class="normal_font">'
										+ '通用'
										+ '							</span>'
										+ '							<span class="Large_font" id="ty_total"></span>                       '
										+ '						</div>'
										+ '					</div>                                                                       '
										+ '				</td>                                                                            '
										+ '			</tr>                                                                                '
										+ '		</table>                                                                                 '
										+ '		<div class="scriptL_SearchR">                                                            '
										+ '			<input type="text" name="search" class="scriptL_SearchinputR"                        '
										+ '				placeholder="请输入脚本关键字搜索" id="scr_lib_share_search_value" /> <a         '
										+ '				href="javascript:void(0);" class="scriptL_SearchA"                               '
										+ '				id="scr_lib_share_search"><div class="Search_button"></div></a>                  '
										+ '		</div>                                                                                   '
										+ '	</div>',
							});

			var shareMarketPanel = Ext.create('Ext.Panel', {
				region : 'center',
				width : 800,
				border : false,
				autoScroll : true,
				layout : 'border',
				items : [ topColorPanel, tabs,detailTabsForWorkStation ]
			});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "scriptLibraryTmpProduct",
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				border : false,
				layout : 'border',
				defaults : {
					split : true
				},
				items : [ envSign==0?myScriptsPanel:myScriptAndJobTabs, shareMarketPanel, myCollectionPanel ]
			});

			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
				if (auditingWin) {
					auditingWin.center();
				}
			});

		});