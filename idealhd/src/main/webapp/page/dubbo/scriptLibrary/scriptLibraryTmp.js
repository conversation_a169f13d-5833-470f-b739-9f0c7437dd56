var scriptDetailWin;
var shareWindow;
var commentWindow;
var commentFormPanel;
var whichScriptIsClick;
var shareSearchValue = '';
Ext.onReady(function() {
	destroyRubbish();
    Ext.override(Ext.toolbar.Paging, {
        getPagingItems: function() {
            var me = this;
            var aa = [{
                itemId: 'first',
                tooltip: me.firstText,
                overflowText: me.firstText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-first',
                disabled: true,
                handler: me.moveFirst,
                scope: me
            },
            {
                itemId: 'prev',
                tooltip: me.prevText,
                overflowText: me.prevText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-prev',
                disabled: true,
                handler: me.movePrevious,
                scope: me
            }];
            if (me.beforePageText != '' || me.beforePageText != '') {
                aa.push(me.beforePageText);
                aa.push({
                    xtype: 'displayfield',
                    itemId: 'inputItem',
                    name: 'inputItem',
                    cls: Ext.baseCSSPrefix + 'tbar-page-number',
                    allowDecimals: false,
                    minValue: 1,
                    hideTrigger: true,
                    enableKeyEvents: true,
                    keyNavEnabled: false,
                    selectOnFocus: true,
                    submitValue: false,
                    // mark it as not a field so the form will not catch
                    // it when getting fields
                    isFormField: false,
                    width: 10,
                    margins: '-2 0 0 0',
                    listeners: {
                        scope: me,
                        keydown: me.onPagingKeyDown,
                        blur: me.onPagingBlur
                    }
                });
                aa.push({
                    xtype: 'tbtext',
                    itemId: 'afterTextItem',
                    text: Ext.String.format(me.afterPageText, 1)
                });
            }

            aa.push({
                itemId: 'next',
                tooltip: me.nextText,
                overflowText: me.nextText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-next',
                disabled: true,
                handler: me.moveNext,
                scope: me
            });
            aa.push({
                itemId: 'last',
                tooltip: me.lastText,
                overflowText: me.lastText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-last',
                disabled: true,
                handler: me.moveLast,
                scope: me
            });
            aa.push({
                itemId: 'refresh',
                tooltip: me.refreshText,
                overflowText: me.refreshText,
                iconCls: Ext.baseCSSPrefix + 'tbar-loading',
                handler: me.doRefresh,
                scope: me
            });
            return aa;
        }
    });

    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        },
        {
            "id": "sh",
            "name": "shell"
        },
        {
            "id": "bat",
            "name": "bat"
        },
        {
            "id": "py",
            "name": "python"
        },
        {
            "id": "perl",
            "name": "perl"
        }]
    });
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .33,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '5',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 二级分类* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '5',
        labelWidth: 70,
        columnWidth: .33,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        padding: '5',
        labelWidth: 70,
        columnWidth: .33,
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: cataStore
    });

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '--请输入服务名称--',
        labelWidth: 70,
        padding: '5',
        columnWidth: .33
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '--请输入脚本名称--',
        labelWidth: 70,
        padding: '5',
        columnWidth: .33
    });

    var search_form = Ext.create('Ext.form.Panel', {
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        items: [{
            layout: 'form',
            anchor: '98%',
            padding: '5 0 5 0',
            border: false,
            items: [{
                layout: 'column',
                border: false,
                items: [sName, scName, scriptTypeParam]
            },
            {
                layout: 'column',
                border: false,
                items: [bussCb, bussTypeCb, {
                    xtype: 'button',
                    columnWidth: .125,
                    // width:60,
                    height: 30,
                    text: '查询',
                    margin: '5 0 0 80',
                    cls: 'Common_Btn',
                    handler: function() {
                        pageBar.moveFirst();
                    }
                },
                {
                    xtype: 'button',
                    columnWidth: .068,
                    // width:60,
                    height: 30,
                    text: '清空',
                    cls: 'Common_Btn',
                    margin: '5 0 0 15',
                    handler: function() {
                        clearQueryWhere();
                    }
                }]
            }]
        }]
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'buss',
            type: 'string'
        },
        {
            name: 'bussType',
            type: 'string'
        },
        {
            name: 'bussId',
            type: 'int'
        },
        {
            name: 'bussTypeId',
            type: 'int'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'isflow',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'platForm',
            type: 'string'
        },
        {
            name: 'version',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        },
        {
            name: 'scriptLevel',
            type: 'int'
        },
        {
            name: 'status',
            type: 'int'
        }]
    });

    var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForShare.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    scriptServiceReleaseStore.on('beforeload',
    function(store, options) {
        var new_params = {
            bussId: search_form.getForm().findField("sysName").getValue(),
            bussTypeId: search_form.getForm().findField("bussType").getValue(),
            scriptName: search_form.getForm().findField("scriptName").getValue(),
            serviceName: search_form.getForm().findField("serverName").getValue(),
            scriptType: search_form.getForm().findField("scriptTypeParam").getValue(),
            onlyScript: 1
        };

        Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });

    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '服务主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        width: 200,
        flex: 1
    },
    {
        text: '适用平台',
        dataIndex: 'platForm',
        width: 200
    },
    {
        text: '一级分类',
        dataIndex: 'buss',
        width: 200,
        flex: 1
    },
    {
        text: '二级分类',
        dataIndex: 'bussType',
        width: 250,
        flex: 1
    },
    {
        text: '脚本名称',
        dataIndex: 'scriptName',
        width: 260,
        flex: 1
    },
    {
        text: '脚本类型',
        dataIndex: 'scriptType',
        width: 80,
        flex: 1,
        renderer: function(value, p, record, rowIndex) {
            var isflow = record.get('isflow');
            if (isflow == "1") {
                return "组合"
            } else {
                return value;
            }
        }
    },
    {
        text: '版本',
        dataIndex: 'version',
        width: 200
    }];

    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: scriptServiceReleaseStore,
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
        width: '100%',
        height: contentPanel.getHeight() - 196,
        border: false,
        id: 'releaseGrid',
        store: scriptServiceReleaseStore,
        selModel: selModel,
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        animCollapse: false
    });

    var mainPanel11 = Ext.create('Ext.panel.Panel', {
        border: false,
        bodyPadding: 5,
        items: [search_form, scriptServiceReleaseGrid]
    });

    if (shareWindow == undefined || !shareWindow.isVisible()) {
        shareWindow = Ext.create('Ext.window.Window', {
            title: '脚本库',
            modal: true,
            closeAction: 'hide',
            constrain: true,
            autoScroll: true,
            width: contentPanel.getWidth(),
            height: contentPanel.getHeight(),
            draggable: false,
            resizable: false,
            layout: 'fit',
            items: [mainPanel11],
            buttonAlign: 'center',
            buttons: [{
                xtype: 'button',
                icon: '',
                handler: function() {
                    var datas = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                    var ids = [];
                    for (var i = 0,
                    len = datas.length; i < len; i++) {
                        ids.push(datas[i].data.iid);
                    }
                    Ext.Ajax.request({
                        url: 'addScriptToMyCollection.do',
                        method: 'POST',
                        params: {
                            ids: ids
                        },
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            shareWindow.close();
                            myCollectionPageBar.moveFirst();
                            Ext.MessageBox.alert("提示", "收藏成功！");
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                            shareWindow.close();
                        }
                    });
                }
            }]
        });
    }

    commentFormPanel = Ext.create('Ext.form.FormPanel', {
        width: 400,
        border: false,
        bodyPadding: 10,
        items: [{
            xtype: 'textareafield',
            grow: true,
            name: 'comm',
            fieldLabel: '填写评论',
            anchor: '100%',
            height: 100
        }]
    });

    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    if (commentWindow == undefined || !commentWindow.isVisible()) {
        commentWindow = Ext.create('Ext.window.Window', {
            title: '评 论',
            modal: true,
            closeAction: 'hide',
            constrain: true,
            autoScroll: true,
            border: false,
            width: 500,
            height: 200,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            layout: 'fit',
            items: [commentFormPanel],
            buttonAlign: 'center',
            buttons: [{
                xtype: 'button',
                icon: '',
                text: '提交',
                margin: '0 0 0 5',
                handler: function() {
                    var content = commentFormPanel.getForm().findField("comm").getValue();
                    if (content.trim() == '') {
                        Ext.Msg.alert('提示', '评论内容不可以为空');
                        return;
                    }
                    if (fucCheckLength(content) > 3000) {
                        Ext.Msg.alert('提示', '评论字符数不可超过3000！');
                        return;
                    }
                    if (whichScriptIsClick) {
                        var iid = whichScriptIsClick;
                        $.post("scriptService/commentForShare.do", {
                            content: content,
                            iid: iid
                        },
                        function(res) {
                            var now = new Date();
                            var year = now.getFullYear(); // 年
                            var month = now.getMonth() + 1; // 月
                            var day = now.getDate();
                            var current = year + '年' + month + '月' + day + '日';
                            if (res.success) {
                                var c = '<div class="Comment_List1">' + content + '<div class="List_tool">' + '<span class="List_left">' + current + '</span>' + '<span class="List_right"><img class="like-it" data-id="' + res.pk + '" src="images/unlike.png" align="absmiddle"/><font>(0)</font></span>' + '</div>' + '</div>';
                                var tba = tabsMarket.getActiveTab();
                                var ly = '';
                                if (tba.title == '作业库') {
                                    ly = '_job';
                                } 
                                $('#comment-overview-' + ly).prepend(c);
                                commentFormPanel.getForm().findField("comm").setValue('');
                                commentWindow.close();
                            } else {
                                commentWindow.close();
                                Ext.Msg.alert('提示', res.message);
                            }
                        });
                    }
                }
            }]
        });
    }

    function clearQueryWhere() {
        search_form.getForm().findField("sysName").setValue('');
        search_form.getForm().findField("bussType").setValue('');
        search_form.getForm().findField("scriptName").setValue('');
        search_form.getForm().findField("serverName").setValue('');
        search_form.getForm().findField("scriptTypeParam").setValue('');
    }

    // 对Date的扩展，将 Date 转化为指定格式的String
    // 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
    // 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
    // 例子：
    // (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02
    // 08:09:04.423
    // (new Date()).Format("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
    Date.prototype.Format = function(fmt) { // author: meizz
        var o = {
            "M+": this.getMonth() + 1,
            // 月份
            "d+": this.getDate(),
            // 日
            "h+": this.getHours(),
            // 小时
            "m+": this.getMinutes(),
            // 分
            "s+": this.getSeconds(),
            // 秒
            "q+": Math.floor((this.getMonth() + 3) / 3),
            // 季度
            "S": this.getMilliseconds()
            // 毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o) if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }

    $(document).off('click', '#scr_lib_myself_search').on('click', '#scr_lib_myself_search',
    function(e) {
        myScriptsPageBar.moveFirst();
    });

    $(document).off('click', '#job_lib_myself_search').on('click', '#job_lib_myself_search',
    function(e) {
        myJobsPageBar.moveFirst();
    });

    $(document).off('click', '#scr_lib_mycollection_search').on('click', '#scr_lib_mycollection_search',
    function(e) {
        myCollectionPageBar.moveFirst();
    });

    
    
    

    $(document).off('keypress', '#scr_lib_myself_search_value').on('keypress', '#scr_lib_myself_search_value',
    function(event) {
        if (event.keyCode == "13") {
            myScriptsPageBar.moveFirst();
        }
    });

    $(document).off('keypress', '#job_lib_myself_search_value').on('keypress', '#job_lib_myself_search_value',
    function(event) {
        if (event.keyCode == "13") {
            myJobsPageBar.moveFirst();
        }
    });

    $(document).off('keypress', '#scr_lib_mycollection_search_value').on('keypress', '#scr_lib_mycollection_search_value',
    function(event) {
        if (event.keyCode == "13") {
            myCollectionPageBar.moveFirst();
        }
    });

    $(document).off('click', 'img.like-it').on('click', 'img.like-it',
    function(e) {
        var $this = $(this);
        var iid = $this.attr('data-id');
        $.post('scriptService/commentLikeIt.do', {
            iid: iid
        },
        function(res) {
            if (res.success) {
                $this.next().html('(' + res.likeNum + ')');
                $this.attr('src', 'images/' + res.likeImg + '.png');
            } else {
                Ext.Msg.alert('提示', res.message);
            }
        });
    });

    $('#my-scr-more').off('click').on('click',
    function(res) {
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'forwardScriptServiceRelease.do',
            scripts: true
        });
    });

    $('#scr-lib-more').off('click').on('click',
    function(res) {
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'forwardScriptShare.do',
            scripts: true
        });
    });

    $('#scr-task-more').off('click').on('click',
    function(res) {
        showMessageWindow();
    });

    $('body').off('click', '#add-scr').on('click', '#add-scr',
    function(res) {
        shareWindow.show();
        pageBar.moveFirst();
    });

    var myScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMySelf.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    myScriptStore.on('beforeload',
    function(store, options) {
        var new_params = {
            onlyScript: 1,
            serviceName: $('#scr_lib_myself_search_value').val()
        };

        Ext.apply(myScriptStore.proxy.extraParams, new_params);
    });
    var myJobStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMySelf.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    myJobStore.on('beforeload',
    function(store, options) {
        var new_params = {
            onlyScript: 0,
            serviceName: $('#job_lib_myself_search_value').val()
        };

        Ext.apply(myJobStore.proxy.extraParams, new_params);
    });

    var myScriptsPageBar = Ext.create('Ext.PagingToolbar', {
        store: myScriptStore,
        dock: 'bottom',
        displayInfo: false,
        afterPageText: '页',
        beforePageText: '第',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '',
        emptyMsg: '找不到任何记录',

    });

    var myJobsPageBar = Ext.create('Ext.PagingToolbar', {
        store: myJobStore,
        dock: 'bottom',
        displayInfo: false,
        afterPageText: '页',
        beforePageText: '第',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '',
        emptyMsg: '找不到任何记录',

    });

    var myScriptsDataView = Ext.create('Ext.view.View', {
        store: myScriptStore,
        padding: 5,
        tpl: ['<tpl for=".">', '<div class="Script_library myScriptM">', '<table cellpadding="0" cellspacing="0" border="0" width="100%">', '<tr>', '<td width="26"><div class="{scriptType:this.humanScriptType}"></div></td>', '<td><div class="Script_textF2">{serviceName}</div></td>', '</tr>', '</table>', '</div>', '</tpl>', {
            humanScriptType: function(name) {
                var scriptType = '';
                if (name == 'sh') {
                    scriptType = 'shell';
                } else if (name == 'bat') {
                    scriptType = 'bat';
                } else if (name == 'py') {
                    scriptType = 'python';
                } else if (name == 'perl') {
                    scriptType = 'perl';
                }else if (name == 'sql') {
                    scriptType = 'sql';
                }else if (name == '组合') {
                    scriptType = 'ssflow';
                }
                return scriptType;
            }
        }],
        multiSelect: false,
        height: '100%',
        trackOver: true,
        overItemCls: 'x-item-over',
        itemSelector: 'div.myScriptM',
        emptyText: '没有脚本',
        listeners: {
            'itemdblclick': function(view, record, item, idx, event, opts) {
                if (record.get('scriptType') == '组合') {
                    popNewTab('我的作业', 'flowCustomizedInitScriptServiceGFSSEDIT.do?iid=' + record.get('iid') + '&actionType=edit&flag=0&serviceName=' + record.get('serviceName') + '&bussId=' + record.get('bussId') + '&bussTypeId=' + record.get('bussTypeId'), {},
                   10, true)
                } else {
                    popNewTab('我的脚本', 'forwardEditScript.do?serviceId=' + record.get('iid'), {},
                    10,true)
                }
            }
        }
    });

    var myJobsDataView = Ext.create('Ext.view.View', {
        store: myJobStore,
        padding: 0,
        tpl: ['<tpl for=".">', '<div class="Script_library myJobM">', '<table cellpadding="0" cellspacing="0" border="0" width="100%">', '<tr>', '<td width="26"><div class="ssflow"></div></td>', '<td><div class="Script_textF2">{serviceName}</div></td>', '</tr>', '</table>', '</div>', '</tpl>'],
        multiSelect: false,
        height: '100%',
        trackOver: true,
        overItemCls: 'x-item-over',
        itemSelector: 'div.myJobM',
        emptyText: '没有作业',
        listeners: {
            'itemdblclick': function(view, record, item, idx, event, opts) {
                popNewTab('我的作业', 'flowCustomizedInitScriptServiceGFSSEDIT.do?iid=' + record.get('iid') + '&actionType=edit&flag=0&serviceName=' + record.get('serviceName') + '&bussId=' + record.get('bussId') + '&bussTypeId=' + record.get('bussTypeId'), {ifrom : 'forwardScriptServiceReleaseFlow.do'},
                10,true)
            }
        }
    });

    var myScriptsGrid = Ext.create('Ext.Panel', {
        region: 'center',
        title: '',
        width: '100%',
        autoScroll: true,
        border: false,
        items: myScriptsDataView,
        bbar: myScriptsPageBar,
    });
    var myJobsGrid = Ext.create('Ext.Panel', {
        region: 'center',
        title: '',
        width: '100%',
        autoScroll: true,
        border: false,
        items: myJobsDataView,
        bbar: myJobsPageBar,
    });

    var myScriptsSearch = Ext.create('Ext.Panel', {
        region: 'north',
        title: '',
        width: '100%',
        padding: 5,
        autoScroll: true,
        border: false,
        html: '' + '<div class="scriptL_Search">' + '<input type="text" name="search" class="scriptL_Searchinput" placeholder="输入脚本名称" id="scr_lib_myself_search_value" />' + '<a href="javascript:void(0);" class="scriptL_SearchA" id="scr_lib_myself_search"><div class="Search_button"></div></a>' + '</div>',
    });
    var myJobsSearch = Ext.create('Ext.Panel', {
        region: 'north',
        title: '',
        width: '100%',
        padding: 5,
        autoScroll: true,
        border: false,
        html: '' + '<div class="scriptL_Search">' + '<input type="text" name="searchMyJobs" class="scriptL_Searchinput" placeholder="输入作业名称" id="job_lib_myself_search_value" />' + '<a href="javascript:void(0);" class="scriptL_SearchA" id="job_lib_myself_search"><div class="Search_button"></div></a>' + '</div>',
    });

    var myScriptsPanel = Ext.create('Ext.Panel', {
        title: '我的脚本',
        width: 260,
        autoScroll: true,
        border: true,
        layout: 'border',
        items: [myScriptsSearch, myScriptsGrid],
    });

    var myJobsPanel = Ext.create('Ext.Panel', {
        title: '我的作业',
        width: 260,
        autoScroll: true,
        border: true,
        layout: 'border',
        items: [myJobsSearch, myJobsGrid],
    });

    var myScriptAndJobTabs = Ext.widget('tabpanel', {
        region: 'west',
        border: false,
        width: 260,
        activeTab: 0,
        cls:'customize_panel_back',
        items: [myScriptsPanel, myJobsPanel]
    });

    var myCollectionStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMyToolBox.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    myCollectionStore.on('beforeload',
    function(store, options) {
        var new_params = {
            onlyScript: 1,
            serviceName: $('#scr_lib_mycollection_search_value').val()
        };

        Ext.apply(myCollectionStore.proxy.extraParams, new_params);
    });

    var myCollectionPageBar = Ext.create('Ext.PagingToolbar', {
        store: myCollectionStore,
        dock: 'bottom',
        displayInfo: false,
        afterPageText: '页',
        beforePageText: '第',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '',
        emptyMsg: '找不到任何记录'
    });

    var myCollectionDataView = Ext.create('Ext.view.View', {
        store: myCollectionStore,
        padding: 5,
        tpl: ['<tpl for=".">', '<div class="Script_library myCollectionM">', '<table cellpadding="0" cellspacing="0" border="0" width="100%">', '<tr>', '<td width="26"><div class="{scriptType:this.humanScriptType}"></div></td>', '<td><div class="Script_textF2">{serviceName}</div></td>', '</tr>', '</table>', '</div>', '</tpl>', {
            humanScriptType: function(name) {
                var scriptType = '';
                if (name == 'sh') {
                    scriptType = 'shell';
                } else if (name == 'bat') {
                    scriptType = 'bat';
                } else if (name == 'py') {
                    scriptType = 'python';
                } else if (name == 'perl') {
                    scriptType = 'perl';
                }else if (name == 'sql') {
                    scriptType = 'sql';
                }  else if (name == '组合') {
                    scriptType = 'ssflow';
                }
                return scriptType;
            }
        }],
        multiSelect: false,
        height: '100%',
        trackOver: true,
        overItemCls: 'x-item-over',
        itemSelector: 'div.myCollectionM',
        listeners: {
            'itemdblclick': function(view, record, item, idx, event, opts) {
            	Ext.MessageBox.buttonText.yes = "测试"; 
				Ext.MessageBox.buttonText.no = "查看"; 
				Ext.Msg.confirm("请确认", '可以进行下列两种操作，请选择。', function(id){
					if(id=='yes'){
						myCollectionTestScript(record.get('iid'));
					} else if (id=='no'){
						var DetailWinTi = Ext.create('widget.window', {
					        title: '详细信息',
					        closable: true,
					        closeAction: 'destroy',
					        width: contentPanel.getWidth(),
					        minWidth: 350,
					        height: contentPanel.getHeight(),
					        draggable: false,
					        resizable: false,
					        modal: true,
					        loader: {
					            url: 'queryOneServiceForView.do',
					            params: {
					                iid: record.get('iid'),
					                flag: 0,
					                hideReturnBtn: 1
					            },
					            autoLoad: true,
					            scripts: true
					        }
					    });
						DetailWinTi.show();
					}
				});
            }
        }
    });

    var myCollectionGrid = Ext.create('Ext.Panel', {
        region: 'center',
        title: '',
        width: '100%',
        autoScroll: true,
        border: false,
        items: myCollectionDataView,
        bbar: myCollectionPageBar,
    });

    var myCollectionSearch = Ext.create('Ext.Panel', {
        region: 'north',
        title: '',
        width: '100%',
        padding: 5,
        autoScroll: true,
        border: false,
        html: '' + '<div class="scriptL_Search">' + '<input type="text" name="search" class="scriptL_Searchinput" placeholder="输入脚本名称" id="scr_lib_mycollection_search_value" />' + '<a href="javascript:void(0);" class="scriptL_SearchA" id="scr_lib_mycollection_search"><div class="Search_button"></div></a>' + '</div>',
    });

    var myCollectionPanel = Ext.create('Ext.Panel', {
        title: '我的收藏 - 脚本',
        width: 260,
        autoScroll: true,
        border: true,
        layout: 'border',
        items: [myCollectionSearch, myCollectionGrid],
    });
    
    var myJobCollectionStore = Ext.create('Ext.data.Store', {
    	autoLoad: true,
    	autoDestroy: true,
    	pageSize: 10,
    	model: 'scriptServiceReleaseModel',
    	proxy: {
    		type: 'ajax',
    		url: 'scriptService/queryServiceForMyToolBox.do',
    		reader: {
    			type: 'json',
    			root: 'dataList',
    			totalProperty: 'total'
    		}
    	}
    });
    
    myJobCollectionStore.on('beforeload', function(store, options) {
    	var new_params = {
    			onlyScript: 0,
    			serviceName: $('#job_lib_mycollection_search_value').val()
    	};
    	
    	Ext.apply(myJobCollectionStore.proxy.extraParams, new_params);
    });
    
    var myJobCollectionPageBar = Ext.create('Ext.PagingToolbar', {
    	store: myJobCollectionStore,
    	dock: 'bottom',
    	displayInfo: false,
    	afterPageText: '页',
    	beforePageText: '第',
    	firstText: '第一页 ',
    	prevText: '前一页',
    	nextText: '下一页',
    	lastText: '最后一页',
    	refreshText: '刷新',
    	displayMsg: '',
    	emptyMsg: '找不到任何记录'
    });
    
    var myJobCollectionDataView = Ext.create('Ext.view.View', {
    	store: myJobCollectionStore,
    	padding: 5,
    	tpl: ['<tpl for=".">', '<div class="Script_library myJobCollectionM">', '<table cellpadding="0" cellspacing="0" border="0" width="100%">', '<tr>', '<td width="26"><div class="ssflow"></div></td>', '<td><div class="Script_textF2">{serviceName}</div></td>', '</tr>', '</table>', '</div>', '</tpl>'],
    	multiSelect: false,
    	height: '100%',
    	trackOver: true,
    	overItemCls: 'x-item-over',
    	itemSelector: 'div.myJobCollectionM',
    	listeners: {
    		'itemdblclick': function(view, record, item, idx, event, opts) {
    			Ext.MessageBox.buttonText.yes = "测试"; 
				Ext.MessageBox.buttonText.no = "查看"; 
				Ext.Msg.confirm("请确认", '可以进行下列两种操作，请选择。', function(id){
					if(id=='yes'){
						popNewTab('我的作业', 'flowCustomizedInitScriptService.do', {
							iid:record.get('iid'),
							serviceId:record.get('iid'),
							serviceName:'',
							actionType:'test',
							menuId : 0,
							submitType:'mc',
							bussId:0,
							bussTypeId:0,
							btnCode : 22,
							ifrom : 'forwardScriptServiceReleaseFlow.do',
							flag:0
						},10, true);
					} else if (id=='no'){
						var DetailWinTi = Ext.create('widget.window', {
							title: '详细信息',
							closable: true,
							closeAction: 'destroy',
							width: contentPanel.getWidth(),
							minWidth: 350,
							height: contentPanel.getHeight(),
							draggable: false,
							resizable: false,
							modal: true,
						loader: {
							url: 'flowCustomizedInitScriptService.do',
							params: {
								serviceId: record.get('iid'),
								iid: record.get('iid'),
								menuId: 0,
								status: 0,
								serviceName:'',
								actionType:'view',
								bussId:0,
								bussTypeId:0,
								submitType:'tv',
								flag:0,
								windowHeight : contentPanel.getHeight() - 42,
								rootspace: 'view',
								isShowInWindow: 1,
								isScriptConvertToFlow : 0
							},
							autoLoad: true,
							scripts: true
						}
						});
						
						DetailWinTi.show();
					}
				});
    		}
    	}
    });
    
    var myJobCollectionGrid = Ext.create('Ext.Panel', {
    	region: 'center',
    	title: '',
    	width: '100%',
    	autoScroll: true,
    	border: false,
    	items: myJobCollectionDataView,
    	bbar: myJobCollectionPageBar,
    });
    
    var myJobCollectionSearch = Ext.create('Ext.Panel', {
    	region: 'north',
    	title: '',
    	width: '100%',
    	padding: 5,
    	autoScroll: true,
    	border: false,
    	html: '' + '<div class="scriptL_Search">' + '<input type="text" name="search" class="scriptL_Searchinput" placeholder="输入作业名称" id="job_lib_mycollection_search_value" />' + '<a href="javascript:void(0);" class="scriptL_SearchA" id="job_lib_mycollection_search"><div class="Search_button"></div></a>' + '</div>',
    });
    
    var myJobCollectionPanel = Ext.create('Ext.Panel', {
    	title: '我的收藏 - 作业',
    	width: 260,
    	autoScroll: true,
    	border: true,
    	layout: 'border',
    	items: [myJobCollectionSearch, myJobCollectionGrid],
    });
    
    var myScriptAndJobCollectionTabs = Ext.widget('tabpanel', {
        region: 'east',
        border: false,
        width: 260,
        activeTab: 0,
        cls:'customize_panel_back',
        items: [myCollectionPanel, myJobCollectionPanel]
    });

    var tabsMarket = Ext.widget('tabpanel', {
    	region: 'center',
    	activeTab: 0,
        plain: true,
        border: false,
        defaults :{
            autoScroll: true,
        },
        items: [{
                title: '脚本库',
                loader: {
                    url: 'getSSLibMarket.do',
                    loadMask: true,
                    scripts : true
                },
                listeners: {
                    activate: function(tab) {
                        tab.loader.load();
                    }
                }
            },{
                title: '作业库',
                loader: {
                	url: 'getSSLibMarketForJob.do',
                    scripts : true,
                    loadMask: true
                },
                listeners: {
                    activate: function(tab) {
                        tab.loader.load();
                    }
                }
            }
        ]
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptLibraryTmp",
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        border: false,
        layout: 'border',
        defaults: {
            split: true
        },
        items: [myScriptAndJobTabs, tabsMarket, myScriptAndJobCollectionTabs]
    });

    contentPanel.on('resize',
    function() {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
        if (auditingWin) {
            auditingWin.center();
        }
    });
    
    
    
    function myCollectionTestScript(iid){
    	var chosedAgentWin;
    	var chosedAgentIds = new Array();
    	var upldWin;
    	var upLoadformPane = '';
    	Ext.define('resourceGroupModel', {
    	    extend : 'Ext.data.Model',
    	    fields : [{
    	      name : 'id',
    	      type : 'int',
    	      useNull : true
    	    }, {
    	      name : 'name',
    	      type : 'string'
    	    }, {
    	      name : 'description',
    	      type : 'string'
    	    }]
    	  });
    	
    	var resourceGroupStore = Ext.create('Ext.data.Store', {
    	    autoLoad: true,
    	    autoDestroy: true,
    	    model: 'resourceGroupModel',
    	    proxy: {
    	      type: 'ajax',
    	      url: 'getResGroupForScriptService.do',
    	      reader: {
    	        type: 'json',
    	        root: 'dataList',
    	        totalProperty: 'totalCount'
    	      }
    	    }
    	  });
    	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
    			{
    			    fieldLabel : '资源组',
    			    labelAlign : 'right',
    			    labelWidth : 58,
    			    width : '25.5%',
    	            columnWidth:1,
    			    multiSelect: true,
    			    hidden:removeAgentSwitch,
    			    store : resourceGroupStore,
    			    displayField : 'name',
    			    valueField : 'id',
    			    triggerAction : 'all',
    			    editable : false,
    			    mode : 'local',
    		    	listeners: {
    	    	      change: function( comb, newValue, oldValue, eOpts ) {
    	    	    	  pageBar.moveFirst();
    	    	      }
    		    	}
    	});
    	
    	Ext.define('appNameModel', {
        	extend: 'Ext.data.Model',
        	fields : [ {
        		name : 'appName',
        		type : 'string'
        	}]
        });
    	
    	var app_name_store = Ext.create('Ext.data.Store', {
    		autoLoad: true,
    		model: 'appNameModel',
    		proxy: {
    			type: 'ajax',
    			url: 'getAgentAppNameList.do?envType=0',
    			reader: {
    				type: 'json',
    				root: 'dataList'
    			}
    		}
    	});
    	
    	var app_name = Ext.create('Ext.form.ComboBox', {
    		name : 'appname',
    	    fieldLabel: "应用名称",
    	    store: app_name_store,
    	    hidden: !CMDBflag,
    	    queryMode: 'local',
    	    width: "25%",
    	    displayField: 'appName',
    	    valueField: 'appName',
    	    labelWidth : 58,
    		labelAlign : 'right',
    		listeners: {
    			beforequery : function(e){
    	            var combo = e.combo;
    	              if(!e.forceAll){
    	              	var value = Ext.util.Format.trim(e.query);
    	              	combo.store.filterBy(function(record,id){
    	              		var text = record.get(combo.displayField);
    	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
    	              	});
    	              combo.expand();
    	              return false;
    	              }
    	         }
    		}
    	  });
    	
    	var agent_ip = new Ext.form.TextField({
    		name : 'agentip',
    		fieldLabel : 'IP',
    		displayField : 'agentip',
    		emptyText : '--请输入agentip--',
    		labelWidth : 58,
    		labelAlign : 'right',
    		width : '25%'
    	});
    	var host_name = new Ext.form.TextField({
    		name : 'hostname',
    		fieldLabel : '主机名称',
    		displayField : 'hostname',
    		emptyText : '--请输入主机名称--',
    		labelWidth : 58,
    		labelAlign : 'right',
    		width : '25%'
    	});
    	
    	Ext.define('sysNameModel', {
        	extend: 'Ext.data.Model',
        	fields : [ {
        		name : 'sysName',
        		type : 'string'
        	}]
        });
    	
    	var sys_name_store = Ext.create('Ext.data.Store', {
    		autoLoad: true,
    		model: 'sysNameModel',
    		proxy: {
    			type: 'ajax',
    			url: 'getAgentSysNameList.do?envType=0',
    			reader: {
    				type: 'json',
    				root: 'dataList'
    			}
    		}
    	});
    	
    	var sys_name = Ext.create('Ext.form.ComboBox', {
    		name : 'sysname',
    	    fieldLabel: "系统名称",
    	    hidden: !CMDBflag,
    	    store: sys_name_store,
    	    queryMode: 'local',
    	    width: "25%",
    	    displayField: 'sysName',
    	    valueField: 'sysName',
    	    labelWidth : 58,
    		labelAlign : 'right',
    		listeners: {
    			beforequery : function(e){
    	            var combo = e.combo;
    	              if(!e.forceAll){
    	              	var value = Ext.util.Format.trim(e.query);
    	              	combo.store.filterBy(function(record,id){
    	              		var text = record.get(combo.displayField);
    	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
    	              	});
    	              combo.expand();
    	              return false;
    	              }
    	         }
    		}
    	  });
    	
    	var os_type = new Ext.form.TextField({
    		name : 'ostype',
    		fieldLabel : '系统类型',
    		displayField : 'ostype',
    		emptyText : '--系统类型--',
    		labelWidth : 58,
    		labelAlign : 'right',
    		width : '25.5%'
    	});
    	
    	var search_ip_form = Ext.create('Ext.form.Panel', {
    		region : 'north',
    		border : false,
    		dockedItems : [ {
    			xtype : 'toolbar',
    			dock : 'top',
    			border: false,
    			items : [ sys_name, app_name, host_name, agent_ip
    			]
    		},
    		{
    			xtype : 'toolbar',
    			dock : 'top',
    			border: false,
    			items : [ os_type, resourceGroupObj,
    				{
    					xtype : 'button',
    					cls : 'Common_Btn',
    					text : '查询',
    					handler : function(){
    						pageBar.moveFirst();
    					}
    				},
    				{
    					xtype : 'button',
    					cls : 'Common_Btn',
    					text : '清空',
    					handler : function(){
    						agent_ip.setValue(''),
    				    	app_name.setValue(''),
    						sys_name.setValue(''),
    						host_name.setValue(''),
    						os_type.setValue(''),
    				    	resourceGroupObj.setValue('')
    					}
    				},{
    					xtype : 'button',
    					cls : 'Common_Btn',
    					text : '导入',
    					handler : importExcel
    				}
    			]
    		}]
    	});
        
        function checkFile(fileName){
    	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
    	    if(!file_reg.test(fileName)){  
    	    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
    	    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
    	        return false;
    	    }
    	    return true;
    	}
        
        function importExcel() {
    		//销毁win窗口
    		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
    			upldWin.destroy();
    			upldWin = null;
    		}
    		
    		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
    			upLoadformPane.destroy();
    			upLoadformPane = null;
    		}
    		//导入文件Panel
    		upLoadformPane =Ext.create('Ext.form.Panel', {
    	        width:370,
    	        height:85,
    		    frame: true,
    			items: [
    				{
    					xtype: 'filefield',
    					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
    					fieldLabel: '选择文件',
    					labelWidth: 80,
    					msgTarget: 'side',
    					anchor: '100%',
    					buttonText: '浏览...',
    					width:370
    				}
    			],
    			buttonAlign: 'left',
    			buttons: [
    					{
    						id:'upldBtnIdAudi',
    						text: '导入Agent文件',
    						handler: function() {
    							var form = this.up('form').getForm();
    							var upfile=form.findField("file").getValue();
    			    			if(upfile==''){
    			    				Ext.Msg.alert('提示',"请选择文件...");
    			    				return ;
    			    			}
    			    			
    			    			var hdtmpFilNam=form.findField("file").getValue();
    			    			if(!checkFile(hdtmpFilNam)){
    				    			  form.findField("file").setRawValue('');
    				    			  return;
    				    		}

    							if (form.isValid()) {
    								 Ext.MessageBox.wait("数据处理中...", "进度条");
    								form.submit({
    									url: 'importAgentForStart.do',
    									params:{
    										envType:0
    				                	},
    								    success: function(form, action) {
    								       var msg = Ext.decode(action.response.responseText).message;
    								       
    								    	   var status = Ext.decode(action.response.responseText).status;
    								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
    								    	   
    								    	   if(status==1) {
    								    		   if(matchAgentIds && matchAgentIds.length>0) {
    								    			   Ext.MessageBox.buttonText.yes = "确定"; 
    								    				Ext.MessageBox.buttonText.no = "取消"; 
    								    			   Ext.Msg.confirm("请确认", msg, function(id){
    										  				 if(id=='yes'){
    									  						Ext.Msg.alert('提示', "导入成功！");
    									  						agent_ip.setValue(''),
    													    	app_name.setValue(''),
    															sys_name.setValue(''),
    															host_name.setValue(''),
    															os_type.setValue(''),
    													    	resourceGroupObj.setValue('')
    													    	chosedAgentIds = matchAgentIds;
    												    	   pageBar.moveFirst();
    										  				 }
    										    		   });
    								    		   } else {
    								    			   Ext.Msg.alert('提示-没有匹配项', msg);
    								    		   }
    								    		   
    								    	   } else {
    								    		   Ext.Msg.alert('提示', "导入成功！");
    									    	   agent_ip.setValue(''),
    										    	app_name.setValue(''),
    												sys_name.setValue(''),
    												host_name.setValue(''),
    												os_type.setValue(''),
    										    	resourceGroupObj.setValue('')
    										    	chosedAgentIds = msg;
    									    	   pageBar.moveFirst();
    								    	   }
    								    	   
    								       upldWin.close();
    								       return;
    								    },
    								    failure: function(form, action) {
    								    	 secureFilterRsFrom(form, action);
    								    }
    								});
    					         }
    						}
    					}, {
    						text: '下载模板',
    						handler: function() {
    							window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
    						}
    					}
    				]
    		});
    		//导入窗口
    		upldWin = Ext.create('Ext.window.Window', {
    		    title: '设备信息批量导入',
    		    width: 400,
    		    height: 120,
    		    modal:true,
    		    resizable: false,
    		    closeAction: 'destroy',
    		    items:  [upLoadformPane]
    		}).show();
    		upldWin.on("beforeshow",function(self, eOpts){
    			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
    			form.reset();
    		});
    		
    		upldWin.on("destroy",function(self, eOpts){
    			upLoadformPane.destroy();
    		});
    	}

    	Ext.define('agentModel', {
            extend: 'Ext.data.Model',
            idProperty: 'iid',
            fields: [
                {name: 'iid',     type: 'string'},
                {name: 'sysName',     type: 'string'},
                {name: 'appName',     type: 'string'},
                {name: 'hostName',     type: 'string'},
                {name: 'osType',     type: 'string'},
                {name: 'agentIp',     type: 'string'},
                {name: 'agentPort',     type: 'string'},
                {name: 'agentDesc',     type: 'string'},
                {name: 'agentDesc',     type: 'string'},
                {name: 'agentState',     type: 'int'}
            ]
        });
        
    	var agent_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 30,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAllAgentList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
    	
    	var agent_store_chosed = Ext.create('Ext.data.Store', {
    		autoLoad: true,
    		pageSize: 30,
    		model: 'agentModel',
    		proxy: {
                type: 'ajax',
                url: 'getAgentChosedList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
    	});
        
        var agent_columns = [
            { text: '主键',  dataIndex: 'iid',hidden:true},
            { text: '系统名称',  dataIndex: 'sysName',flex:1},
            { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,flex:1},
            { text: '主机名称',  dataIndex: 'hostName',flex:1},
            { text: 'IP',  dataIndex: 'agentIp',width:150},
            { text: '端口号',  dataIndex: 'agentPort',width:100},
            { text: '系统类型',  dataIndex: 'osType',width:140},
            { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden: true,
            	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                    return value;  
                }
            },
            { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
            	var backValue = "";
            	if(value==0){
            		backValue = "Agent正常";
            	}else if(value==1){
            		backValue = "Agent异常";
            	}
            	return backValue;
            }}
           ];
        
        agent_store.on('beforeload', function (store, options) {
    	    var new_params = {  
    	    	agentIp : Ext.util.Format.trim(agent_ip.getValue()),
    	    	appName : app_name.getValue()==null?'':Ext.util.Format.trim(app_name.getValue()+""),
    			sysName : sys_name.getValue()==null?'':Ext.util.Format.trim(sys_name.getValue()+""),
    			hostName : Ext.util.Format.trim(host_name.getValue()),
    			osType : Ext.util.Format.trim(os_type.getValue()),
    	    	rgIds:resourceGroupObj.getValue(),
    	    	flag: 0
    	    };
    	    
    	    Ext.apply(agent_store.proxy.extraParams, new_params);
        });
        
        agent_store_chosed.on('beforeload', function (store, options) {
        	var new_params = {  
        			agentIds : JSON.stringify(chosedAgentIds)
        	};
        	
        	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
        });
        
        agent_store.on('load', function (store, options) {
        	var records=[];//存放选中记录
    	  for(var i=0;i<agent_store.getCount();i++){
    	      var record = agent_store.getAt(i);
    	      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
        	    {   
    	    	  
    	    	  if((+chosedAgentIds[ii])==record.data.iid)
    	    		  {
    	    		  records.push(record);
    	    		  }
        	    }   
    	  }
    	  agent_grid.getSelectionModel().select(records, false, true);//选中记录
        });
        
        var pageBar = Ext.create('Ext.PagingToolbar', {
    		store : agent_store,
    		dock : 'bottom',
    		displayInfo : true
    	});
        
        var agent_grid = Ext.create('Ext.grid.Panel', {
        	region: 'center',
    	    store:agent_store,
    	    height: 450,
    	    border:false,
    	    columnLines : true,
    	    columns:agent_columns,
    	    bbar : pageBar,
    	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
    	    listeners: {
    	        select: function( e, record, index, eOpts ){ 
                	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
                		chosedAgentIds.push(record.get('iid'));
                	}
                },
    	        deselect: function( e, record, index, eOpts ){ 
                	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
                		chosedAgentIds.remove(record.get('iid'));
                	}
                }
    	    }
    	});
        
        var pageBarForAgentChosedGrid = Ext.create('Ext.PagingToolbar', {
        	store : agent_store_chosed,
        	dock : 'bottom',
        	displayInfo : true
        });
        
        var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
        	title: '已选服务器',
        	region: 'center',
        	store:agent_store_chosed,
        	border:true,
        	columnLines : true,
//        	height: 450,
        	emptyText: '没有选择服务器',
        	columns:agent_columns,
        	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        	bbar : pageBarForAgentChosedGrid,
        	dockedItems : [ {
    			xtype : 'toolbar',
    			dock : 'top',
    			items : [{
    				xtype : 'button',
    				cls :'Common_Btn',
    				text : '删除',
    				handler : function() {
    					var records = agent_grid_chosed.getSelectionModel().getSelection();
    					if(records.length>0) {
    	  					for(var i = 0, len = records.length; i < len; i++){
    	  						chosedAgentIds.remove(records[i].get('iid'));
    	  					}
    	  					pageBarForAgentChosedGrid.moveFirst();
    	  					pageBar.moveFirst();
    	  				} else {
    	  					Ext.Msg.alert('提示', "请选择服务器！");
                            return;
    	  				}
    				}
    			},
    			{
    				xtype : 'button',
    				cls : 'Common_Btn',
    				text : '增加服务器',
    				handler : function(){
    					if(!chosedAgentWin) {
    						chosedAgentWin = Ext.create('Ext.window.Window', {
    					  		title : '增加服务器',
    					  		autoScroll : true,
    					  		modal : true,
    					  		resizable : false,
    					  		closeAction : 'hide',
    					  		layout: 'border',
    					  		width : contentPanel.getWidth()-190,
    					  		height : contentPanel.getHeight(),
    					  		items:[search_ip_form, agent_grid],
    					  		dockedItems: [{
    					            xtype: 'toolbar',
    					            dock:'bottom',
    					            layout: {pack: 'center'},
    						        items: [{ 
    						  			xtype: "button",
    						  			text: "确定", 
    						  			cls:'Common_Btn',
    						  			margin:'6',
    						  			handler: function () {
    						  				agent_store_chosed.load();
    						  				this.up("window").close();
    						  			}
    						  		 },{ 
    						  			xtype: "button",
    						  			text: "关闭", 
    						  			cls:'Common_Btn',
    						  			margin:'6',
    						  			handler: function () {
    						  				this.up("window").close();
    						  			}
    						  		 }]
    					  		}]
    					  	});
    					}
    					chosedAgentWin.show();
    					agent_store.load();
    				}
    			} ]
    		}]
        });
        
        Ext.define('paramModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            },
            {
                name: 'paramType',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }]
        });

        paramStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            pageSize: 30,
            model: 'paramModel',
            proxy: {
                type: 'ajax',
                url: 'getAllScriptParams.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        paramStore.on('beforeload', function(store, options) {
            var new_params = {
                scriptId: iid
            };

            Ext.apply(paramStore.proxy.extraParams, new_params);
        });

        var paramColumns = [
        {
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 60,
            renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                return value;  
            }
        },
        {
            text: '参数值',
            dataIndex: 'paramDefaultValue',
            width: 70,
            editor: {
                allowBlank: true
            },
            renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                return value;  
            }
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 50,
            renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                return value;  
            }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                return value;  
            }
        }];
        
        var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
            clicksToEdit: 2
        });
        
        var centerPanel = Ext.create('Ext.panel.Panel', {
        	region : 'center',
        	border: false,
            layout : 'border',
            items: [agent_grid_chosed]
        });
        
        var paramGrid = Ext.create('Ext.grid.Panel', {
        	region : 'north',
        	title: "脚本参数",
            store: paramStore,
            plugins: [cellEditing],
            border: false,
            columnLines: true,
//            collapsible : true,
//            collapsed: true,
            height:150,
            columns: paramColumns
        });
        
        var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
        	region : 'center',
        	border: true,
            layout : 'border',
            height:contentPanel.getHeight()-140,
            items: [centerPanel, paramGrid]
        });
        function setMessage(msg){
    		Ext.Msg.alert('提示', msg);
    	}
        var testChooseAgentWin = Ext.create('Ext.window.Window', {
    	  		title : '选择测试服务器',
    	  		autoScroll : true,
    	  		modal : true,
    	  		resizable : false,
    	  		layout: 'border',
    	  		closeAction : 'destroy',
    	  		width : contentPanel.getWidth()-250,
    	  		height : contentPanel.getHeight(),
    	  		items:[chooseAgentPanel],
    	  		dockedItems: [{
    	            xtype: 'toolbar',
    	            dock:'bottom',
    	            items: ['->',{ 
    	  			xtype: "button",
    	  			text: "确定", 
    	  			cls:'Common_Btn',
    	  			margin:'6',
    	  			handler: function () {
    	  				var me = this;
    	  				var agents = new Array();
    	  				if(chosedAgentIds.length<=0) {
    	  					 setMessage('请选择服务器！');
                            return;
    	  				}
    	  				var isOk = false;
    	  				var agentStateMsg = "";
    	  				
    	  				// 检查agent状态
    	  				Ext.Ajax.request({
    		      			url : 'checkAgentState.do',
    		      			method : 'POST',
    		      			async: false,
    		      			params : {
    		      				agentIds: chosedAgentIds,
    		      			},
    		      			success : function(response, request) {
    		      				isOk = Ext.decode(response.responseText).isOk;
    		      				agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
    		      			},
    		      			failure : function(result, request) {
    		      				agentStateMsg = "检查Agent状态出错！";
    		      			}
    		      		});
    	  				
    	  				function realTest() {
    	  					$.each(chosedAgentIds, function(i,v){
    		  					var a = {
    		  							iid: v
    		  					};
    		  					agents.push(a);
    		  				});
    		  				
    		  				paramStore.sort('paramOrder', 'ASC');
    	                	var m = paramStore.getRange(0, paramStore.getCount()-1);
    	                	var aaaa = new Array();
    	                    for (var i = 0, len = m.length; i < len; i++) {
    	                        var n = 0;
    	                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
    	                        var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
    	                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
    	                        if ("" == paramType) {
    	                            setMessage('参数类型不能为空！');
    	                            return;
    	                        }
    	                        if (fucCheckLength(paramDesc) > 250) {
    	                            setMessage('参数描述不能超过250字符！');
    	                            return;
    	                        }

    	                        if (paramType == 'int') {
    	                            if (!checkIsInteger(paramDefaultValue)) {
    	                                setMessage('参数类型为int，但参数默认值不是int类型！');
    	                                return;
    	                            }
    	                        }
    	                        if (paramType == 'float') {
    	                            if (!checkIsDouble(paramDefaultValue)) {
    	                                setMessage('参数类型为float，但参数默认值不是float类型！');
    	                                return;
    	                            }
    	                        }
    	                        aaaa.push(paramDefaultValue);
    	                    }

    		  				
    	                    var scriptPara = aaaa.join("@@script@@service@@");
    		  				Ext.Ajax.request({
    			      			url : 'execScriptServiceForSync.do',
    			      			method : 'POST',
    			      			params : {
    			      				serviceId: iid,
    			      				execUser: "",
    			      				scriptPara: scriptPara,
    			      				jsonData:JSON.stringify(agents),
    			      				ifrom :0,
    			      				flag: 0
    			      			},
    			      			success : function(response, request) {
    	  		                	me.up("window").close();
    	    		  				Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
    			      			},
    			      			failure : function(result, request) {
    			      				Ext.Msg.alert('提示', '执行失败！');
    			      				$this.html('执行脚本');
    			      			}
    			      		});
    	  				}
    	  				
    	  				if(isOk) {
    	  					realTest();
    	  				} else {
    	  					Ext.Msg.confirm("请确认", agentStateMsg+"<br>选择的代理状态为异常，是否仍然进行测试？", function(id){
      			    			if(id=='yes') {
      			    				realTest();
      			    			}
      			    		});
    	  				}
    		        }
    	  		}, { 
    	  			xtype: "button", 
    	  			text: "取消", 
    	  			cls:'Common_Btn',
    	  			handler: function () {
    	  				this.up("window").close();
    	  			}
    	  		}]
    	  	  }]
    	  	}).show();
    }

});