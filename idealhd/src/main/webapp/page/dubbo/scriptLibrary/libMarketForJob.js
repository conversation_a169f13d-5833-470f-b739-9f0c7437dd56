Ext.onReady(function() {
	destroyRubbish();
	var shareSearchValue = "";
    Ext.override(Ext.toolbar.Paging, {
        getPagingItems: function() {
            var me = this;
            var aa = [{
                itemId: 'first',
                tooltip: me.firstText,
                overflowText: me.firstText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-first',
                disabled: true,
                handler: me.moveFirst,
                scope: me
            },
            {
                itemId: 'prev',
                tooltip: me.prevText,
                overflowText: me.prevText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-prev',
                disabled: true,
                handler: me.movePrevious,
                scope: me
            }];
            if (me.beforePageText != '' ) {
                aa.push(me.beforePageText);
                aa.push({
                    xtype: 'displayfield',
                    itemId: 'inputItem',
                    name: 'inputItem',
                    cls: Ext.baseCSSPrefix + 'tbar-page-number',
                    allowDecimals: false,
                    minValue: 1,
                    hideTrigger: true,
                    enableKeyEvents: true,
                    keyNavEnabled: false,
                    selectOnFocus: true,
                    submitValue: false,
                    // mark it as not a field so the form will not catch
                    // it when getting fields
                    isFormField: false,
                    width: 10,
                    margins: '-2 0 0 0',
                    listeners: {
                        scope: me,
                        keydown: me.onPagingKeyDown,
                        blur: me.onPagingBlur
                    }
                });
                aa.push({
                    xtype: 'tbtext',
                    itemId: 'afterTextItem',
                    text: Ext.String.format(me.afterPageText, 1)
                });
            }

            aa.push({
                itemId: 'next',
                tooltip: me.nextText,
                overflowText: me.nextText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-next',
                disabled: true,
                handler: me.moveNext,
                scope: me
            });
            aa.push({
                itemId: 'last',
                tooltip: me.lastText,
                overflowText: me.lastText,
                iconCls: Ext.baseCSSPrefix + 'tbar-page-last',
                disabled: true,
                handler: me.moveLast,
                scope: me
            });
            aa.push({
                itemId: 'refresh',
                tooltip: me.refreshText,
                overflowText: me.refreshText,
                iconCls: Ext.baseCSSPrefix + 'tbar-loading',
                handler: me.doRefresh,
                scope: me
            });
            return aa;
        }
    });
    
    var tabs = Ext.widget('tabpanel', {
        region: 'west',
        activeTab: 0,
        width: 240,
        border: false,
        margin: '5 5 0 0',
        bodyCls: 'market_Cleft',
        items: [{
            title: '热门作业',
            loader: {
                url: 'dashboardLibTab.do',
                params: {
                    'libType': 'hot',
                    'envSign': 0
                },
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load({
                        params: {
                            'serviceName': shareSearchValue,
                            'envSign': 0
                        }
                    });
                }
            }
        },
        {
            title: '最新作业',
            loader: {
                url: 'dashboardLibTab.do',
                params: {
                    'libType': 'new',
                    'envSign': 0
                },
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load({
                        params: {
                            'serviceName': shareSearchValue,
                            'envSign': 0
                        }
                    });
                }
            }
        },
        {
            title: '作业库',
            loader: {
                url: 'dashboardLibTab.do',
                params: {
                    'libType': 'share',
                    'envSign': 0
                },
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load({
                        params: {
                            'serviceName': shareSearchValue,
                            'envSign': 0
                        }
                    });
                }
            }
        }]
    });

    var detailTabsForWorkStation = Ext.widget('tabpanel', {
        region: 'center',
        activeTab: 0,
        margin: '5 0 0 0',
        deferredRender: false,
        autoScroll: true,
        border: false,
        items: [{
            title: '基本信息',
            autoScroll: true,
            html: '' +
            '<table cellpadding="0" cellspacing="0" border="0" class="mar_CR_table">' +
            '	<tr>' +
            '    	<td width="118"><div class="Developers"></div></td>' + 
            '        <td valign="top">' + 
            '        	<table cellpadding="0" cellspacing="0" border="0" class="mar_CR_info">' + 
            '            	<tr>' +
            '                	<td class="mar_td">开发者：</td>' + 
            '                    <td id="scr-creator-_job"></td>' + 
            '                </tr>' + 
            '                <tr>' + 
            '                	<td class="mar_td">上线时间：</td>' + 
            '                    <td id="scr-create-time-_job"></td>' + 
            '                </tr>' +
            '                <tr>' + 
            '                	<td class="mar_td">适用平台：</td>' + 
            '                    <td id="scr-platform-_job"></td>' + 
            '                </tr>' + 
            '                <tr>' + 
            '                	<td class="mar_td">一级分类：</td>' +
            '                    <td id="scr-buss-_job"></td>' +
            '                </tr>' + 
            '                <tr>' +
            '                	<td class="mar_td">二级分类：</td>' + 
            '                    <td id="scr-buss-type-_job"></td>' + 
            '                </tr>' + 
            '            </table>' + 
            '        </td>' +
            '    </tr>' + 
            '    <tr><td colspan="2"><div class="mar_line"></div></td></tr>' + 
            '    <tr>' + 
            '    	<td colspan="2">' +
            '			<div class="mar_desc">功能说明：</div>' +
            '        	<div class="mar_content" id="scr_func_desc-_job"></div>' + 
            '        </td>' + 
            '    </tr>' +
            '</table>'
        },
        {
            title: '评论',
            autoScroll: true,
            html: '' + '<div class="Commnet_info">                                             ' + 
            '	<div class="Comment_btn2 comment-window">                      ' + 
            '		<div class="Commnet_button Common_Btn">                                       ' + 
            '			<img src="images/Comment_icon.png" align="absmiddle" />评论 ' + 
            '		</div>                                                          ' + 
            '	</div>                                                              ' + 
            '</div>                                                                 ' + 
            '<div class="Comment_overview" id="comment-overview-_job' + "" + '"></div>'
        }]
    });

    var topColorPanel = Ext.create('Ext.Panel', {
        region: 'north',
        border: false,
        html: '<div class="panel-body3">' +
        '<table cellpadding="0" cellspacing="0" border="0"' +
        '	class="Script_market">' +
        '	<tr>' +
        '<td>' + 
        '<div class="sys-type-color_job Overview_shell" data-sys-type="应用维护类">' +
        '<div class="script_L_icon1 script_L_common"></div>' +
        '<div class="script_R">' +
        '<span class="normal_font">' +
        '应用维护类' + 
        '</span>' +
        '<span class="Large_font" id="shell_total_job"></span>' +
        '</div>' +
        ' ' + 
        '</div>' +
        '</td>' +
        '<td>' +
        '<div class="sys-type-color_job Overview_bat" data-sys-type="系统维护类">' +
        '<div class="script_L_icon2 script_L_common"></div>' +
        '<div class="script_R">' +
        '<span class="normal_font">' +
        '系统维护类' +
        '</span>' +
        '<span class="Large_font" id="bat_total_job"></span>' +
        '</div>' + 
        '</div>' + 
        '</td>' + 
        '<td>' +
        '<div class="sys-type-color_job Overview_perl" data-sys-type="网络维护类">' +
        '<div class="script_L_icon3 script_L_common"></div>' +
        '<div class="script_R">' +
        '<span class="normal_font">' +
        '网络维护类' +
        '</span>' +
        '<span class="Large_font" id="perl_total_job"></span>' + 
        '</div>' + 
        '</div>' +
        '</td>' + 
        '<td>' +
        '<div class="sys-type-color_job Overview_python" data-sys-type="文件操作类">' +
        '<div class="script_L_icon4 script_L_common"></div>' +
        '<div class="script_R">' +
        '<span class="normal_font">' +
        '文件操作类' + 
        '</span>' + 
        '<span class="Large_font" id="python_total_job"></span>' +
        '</div>' +
        '</div>' +
        '</td>' + 
        '<td>' +
        '<div class="sys-type-color_job Overview_color1" data-sys-type="第三方软件类">    ' +
        '						<div class="script_L_icon5 script_L_common"></div>' +
        '						<div class="script_R">' +
        '							<span class="normal_font">' + 
        '第三方软件类' +
        '							</span>' +
        '							<span class="Large_font" id="dsfrjl_total_job"></span>           ' +
        '						</div>' +
        '					</div>                                                                       ' +
        '				</td>                                                                            ' +
        '				<td>                                                                             ' +
        '					<div class="sys-type-color_job Overview_color2" data-sys-type="通用">            ' +
        '						<div class="script_L_icon6 script_L_common"></div>' +
        '						<div class="script_R">' +
        '							<span class="normal_font">' +
        '通用' + '							</span>' +
        '							<span class="Large_font" id="ty_total_job"></span>                       ' +
        '						</div>' + 
        '					</div>                                                                       ' +
        '				</td>                                                                            ' + 
        '			</tr>                                                                                ' +
        '		</table>                                                                                 ' +
        '		<div class="scriptL_SearchR">                                                            ' + 
        '			<input type="text" name="search" class="scriptL_SearchinputR"                        ' + 
        '				placeholder="请输入脚本关键字搜索" id="job_lib_share_search_value" /> <a         ' + 
        '				href="javascript:void(0);" class="scriptL_SearchA"                               ' + 
        '				id="job_lib_share_search"><div class="Search_button"></div></a>                  ' + 
        '		</div>                                                                                   ' + 
        '	</div>'
    });
    
    Ext.create('Ext.Panel', {
        renderTo: "libMarketForJob",
        height: contentPanel.getHeight() - 40,
        border: false,
        autoScroll: true,
        layout: 'border',
        items: [topColorPanel, tabs, detailTabsForWorkStation]
    });
    
    $.post("scriptService/scriptStatistic.do", {'scriptType': 'job'},
    function(res) {
        var yywhl = res.yywhl;
        var xtwhl = res.xtwhl;
        var wlwhl = res.wlwhl;
        var wjczl = res.wjczl;
        var dsfrjl = res.dsfrjl;
        var ty = res.ty;
        $('#shell_total_job').html(yywhl ? yywhl: 0);
        $('#bat_total_job').html(xtwhl ? xtwhl: 0);
        $('#perl_total_job').html(wlwhl ? wlwhl: 0);
        $('#python_total_job').html(wjczl ? wjczl: 0);
        $('#dsfrjl_total_job').html(dsfrjl ? dsfrjl: 0);
        $('#ty_total_job').html(ty ? ty: 0);
    });

    $(document).off('click', '.sys-type-color_job').on('click', '.sys-type-color_job',
    function() {
        $('.sys-type-color_job').removeClass('script_select');
        var $this = $(this);
        $this.addClass('script_select');
        var sys_type = $this.data('sys-type');
        var tba = tabs.getActiveTab();
        tba.getLoader().load({
            params: {
                'serviceName': shareSearchValue,
                'sysType': sys_type
            }
        });
    });
    
    $(document).off('click', '#job_lib_share_search').on('click', '#job_lib_share_search',
    		function(e) {
    	shareSearchValue = $('#job_lib_share_search_value').val();
    	var tba = tabs.getActiveTab();
    	tba.getLoader().load({
    		params: {
    			'serviceName': shareSearchValue
    		}
    	});
    });
    
    $(document).off('keypress', '#job_lib_share_search_value').on('keypress', '#job_lib_share_search_value',
    		function(event) {
    	if (event.keyCode == "13") {
    		shareSearchValue = $('#job_lib_share_search_value').val();
    		var tba = tabs.getActiveTab();
    		tba.getLoader().load({
    			params: {
    				'serviceName': shareSearchValue
    			}
    		});
    	}
    });

});