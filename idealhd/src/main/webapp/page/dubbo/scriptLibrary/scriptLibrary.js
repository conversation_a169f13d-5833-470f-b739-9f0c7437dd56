Ext.onReady(function() {
	var scriptLibraryPanel4 = Ext.create('Ext.panel.Panel', {
		title : '热门脚本',
		border:false,
		loader : {
//			url : 'welcome.do',
//			url : 'pandect1.do',
			url : 'page/common/welcome.jsp',
			autoLoad : true,
			scripts : true
		}
	});
	var scriptLibraryPanel5 = Ext.create('Ext.panel.Panel', {
		title : '最新脚本',
		loader : {
//			url : 'welcome.do',
//			url : 'pandect1.do',
			url : 'page/common/welcome.jsp',
			autoLoad : true,
			border:false,
			scripts : true
		}
			
	});
	var scriptLibraryTabPanel1 = Ext.create('Ext.tab.Panel',{
		activeTab : 0,
		items : [ scriptLibraryPanel4, scriptLibraryPanel5]
	});
	var scriptLibraryPanel3 = Ext.create('Ext.panel.Panel', {
		region : 'east',
		width : 200,
		bodypading:5,
		border:false,
		title : '',
		items : [ scriptLibraryTabPanel1 ]
	});
	var scriptLibraryPanel7 = Ext.create('Ext.panel.Panel', {
		region : 'center',
		title : '',
		loader : {
//			url : 'welcome.do',
//			url : 'pandect1.do',
			url : 'page/common/welcome.jsp',
			autoLoad : true,
			scripts : true
		}
	});
	var scriptLibraryPanel2 = Ext.create('Ext.panel.Panel', {
		region : 'center',
		layout : 'border',
		title : '共享脚本库',
		items : [ scriptLibraryPanel3, scriptLibraryPanel7 ]
	});
	var scriptLibraryPanel8 = Ext.create('Ext.panel.Panel', {
		region : 'west',
		width : 300,
		border:false,
		title : '我的脚本库',
		loader : {
//				url : 'welcome.do',
//			url : 'pandect1.do',
			url : 'page/common/welcome.jsp',
			autoLoad : true,
			scripts : true
		}
	});
	var scriptLibraryPanel1 = Ext.create('Ext.panel.Panel', {
		renderTo : 'scriptLibrary_div',
		height : contentPanel.getHeight() - 55,
		width : contentPanel.getWidth()-2,
		layout : 'border',
		items : [ scriptLibraryPanel2, scriptLibraryPanel8 ]
	});/*
		 * contentPanel.on('resize', function() {scriptLibraryPanel1.setHeight
		 * (contentPanel.getHeight() - titleHeight);scriptLibraryPanel1.setWidth
		 * (contentPanel.getWidth());});
		 */
});