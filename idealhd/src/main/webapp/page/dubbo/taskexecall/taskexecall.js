
/*******************************************************************************
 * 任务执行整合TAB页
 ******************************************************************************/
Ext.onReady (function ()
{	
	var  ICPanel=null;
	var  EMPanel=null;
	var tabPanelForBSM =null;
	if(!isTabSwitch){
		contentPanel.getHeader().hide();
	}
	ICPanel={
		    title : '普通任务',
		    border : false,
		    loader :
		    {
		        url : 'scriptTaskexec.do?whichDo='+whichDoForTaskexecall,
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true,
				params: {
					scriptTaskApplySs:fromMenuTaskexecall==='scriptTaskApplySs'?'scriptTaskApplySs':'',
					serviceName:serviceName,
					platForm:platForm,
					taskName:taskName,
					taskStateForm:taskStateForm,
					scriptName:scriptName,
					bsName:bsName,
					bussType:bussType
				}
		    },
		    listeners :
		    {
		       activate : function (tab)
		        {
		            tab.loader.load ({
		           
		            });
		        }
		    }
		};
	
	EMPanel={
	    title : '定时任务',
	    border : false,
	    loader :
	    {
	       url : 'scriptTaskexecDelaye.do',
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true,
			params: {
				scriptTaskApplySs:fromMenuTaskexecall==='scriptTaskApplySs'?'scriptTaskApplySs':'',
				serviceName:serviceName,
				platForm:platForm,
				taskName:taskName,
				taskStateForm:taskStateForm,
				scriptName:scriptName,
				bsName:bsName,
				bussType:bussType
			}
	    },
	    listeners :
	    {
	        activate : function (tab)
	        {
	            tab.loader.load ({
	           
	            });
	        }
	    }
	};

	/** 窗口tabPanel* */
	tabPanelForBSM = Ext.create ('Ext.tab.Panel',
	{
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : activeTabNumForTaskexecall,
	    cls:'customize_panel_back',
	    width : '100%',
	    height : contentPanel.getHeight (),
	    border : false,
	    defaults :
	    {
		    autoScroll : false
	    },
		tabBar: {
			items: [{
				xtype: 'tbfill'
			},
			{
				xtype: 'button',
				cls: 'Common_Btn',
				text: '返回',
				hidden: fromMenuTaskexecall!=='scriptTaskApplySs',
				handler: function() {
					destroyRubbish();
					contentPanel.getHeader().show();
					contentPanel.getLoader().load({
						url: 'scriptTaskApply.do',
						scripts: true
					});

				}
			}
			]
		},
	    items : [ICPanel,EMPanel]
	});
	/** 主Panel* */
	var businessTypeWindowPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "taskexecallDiv",
	    width : '100%',
	    height : '100%',
	    border : false,
	    bodyPadding : 0,
	    items : [
		    tabPanelForBSM
	    ]
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (businessTypeWindowPanel);
		Ext.destroy (tabPanelForBSM);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	setTitle();
	function setTitle(){
		changeTaskCnt();
	}
	
	function changeTaskCnt(){
		var ICPanel_title=ICPanel.title;
		var EMPanel_title=EMPanel.title;
		tabPanelForBSM.getComponent(0).setTitle(ICPanel_title);
		tabPanelForBSM.getComponent(1).setTitle(EMPanel_title);
	}
});