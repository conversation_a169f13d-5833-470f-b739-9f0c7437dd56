
/*******************************************************************************
 * 任务执行整合TAB页
 ******************************************************************************/
Ext.onReady (function ()
{	
	var  ICPanelCommon=null;
	var  EMPanelTime=null;
	var tabPanelForBSMForWhite =null;
//	contentPanel.getHeader().hide();
	if(!isTabSwitch){
		contentPanel.getHeader().hide();
	}
	ICPanelCommon={
		    title : '普通任务',
		    border : false,
		    loader :
		    {
		        url : 'scriptTaskexecforwhite.do?whichDo='+whichDoForWhiteTaskexecall,
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		    },
		    listeners :
		    {
		       activate : function (tab)
		        {
		            tab.loader.load ({
		           
		            });
		        }
		    }
		};
	
	EMPanelTime={
	    title : '定时任务',
	    border : false,
	    loader :
	    {
	       url : 'scriptTaskexecDelayeFroWhite.do',
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	    },
	    listeners :
	    {
	        activate : function (tab)
	        {
	            tab.loader.load ({
	           
	            });
	        }
	    }
	};

	/** 窗口tabPanel* */
	tabPanelForBSMForWhite = Ext.create ('Ext.tab.Panel',
	{
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : activeTabNumForWhiteTaskexecall,
	    cls:'customize_panel_back',
	    width : '100%',
	    height : contentPanel.getHeight (),
	    border : false,
	    defaults :
	    {
		    autoScroll : false
	    },
		tabBar: {
			items: [{
				xtype: 'tbfill'
			},
				{
					xtype: 'button',
					cls: 'Common_Btn',
					text: '返回',
					hidden: fromMenuForWhiteTaskexecall!=='scriptTaskApplySs',
					handler: function() {
						destroyRubbish();
						contentPanel.getHeader().show();
						contentPanel.getLoader().load({
							url: 'scriptTaskApply.do',
							scripts: true
						});

					}
				}
			]
		},
	    items : [ICPanelCommon,EMPanelTime]
	});
	/** 主Panel* */
	var businessTypeWindowPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "taskexecallDivforwhite",
	    width : '100%',
	    height : '100%',
	    border : false,
	    bodyPadding : 0,
	    items : [
	             tabPanelForBSMForWhite
	    ]
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (businessTypeWindowPanel);
		Ext.destroy (tabPanelForBSMForWhite);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	setTitle();
	function setTitle(){
		changeTaskCnt();
	}
	
	function changeTaskCnt(){
		var ICPanelCommon_title=ICPanelCommon.title;
		var EMPanelTime_title=EMPanelTime.title;
		tabPanelForBSMForWhite.getComponent(0).setTitle(ICPanelCommon_title);
		tabPanelForBSMForWhite.getComponent(1).setTitle(EMPanelTime_title);
	}
});



