<%@ page import="com.ideal.ieai.core.Environment" %>
<%@page contentType="text/html; charset=utf-8"%>
<%
    boolean scriptFileSendSftpSwitch = Environment.getInstance().geScriptFileSendSftpSwitch();
%>
<html>
<head>
<script type="text/javascript">
<%-- var flowId = <%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>;
	var flag = <%=request.getParameter("flag")==null?"":request.getParameter("flag")%>; --%>
    var scriptFileSendSftpSwitch = <%=scriptFileSendSftpSwitch%>;
	var sessionId_extractConfig = '<%=request.getSession().getId()%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/extractConfig/extractConfig.js"></script>
</head>
<body>
<div id="extractConfig_area" style="width: 100%;height: 100%">
</div>
</body>
</html>