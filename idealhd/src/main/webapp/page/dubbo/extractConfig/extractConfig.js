Ext.onReady(function() {
    destroyRubbish();
	
	Ext.define('FtpModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'iid',
	      type : 'int'
	    }, {
	      name : 'ftpName',
	      type : 'string'
	    }]
	  });
	Ext.define('OsScriptModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iid',
			type : 'int'
		}, {
			name : 'scriptName',
			type : 'string'
		}]
	});

	var ftp_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'FtpModel',
		proxy: {
			type: 'ajax',
			url: 'getExtraFtpConfigList.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var os_script_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'OsScriptModel',
		proxy: {
			type: 'ajax',
			url: 'getExtractScriptList.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var ftpId = null;
	var protocol = null;
	Ext.Ajax.request({
		url : 'getExtractFtpConfig.do',
		method : 'POST',
		async: false,
		success : function(response, request) {
			var data = Ext.decode(response.responseText);
			if(data.key>0) {
				ftpId = data.key;
				protocol = data.protocol;
			}
		},
		failure : function(result, request) {
			
		}
	});
	var protocolStore = Ext.create('Ext.data.Store', {
		fields: ['protocolType', 'protocolType'],
		data: [{
			"protocolType": "FTP",
			"protocolType": "FTP"
		},
			{
				"protocolType": "SFTP",
				"protocolType": "SFTP"
			}]
	});
	var protocolType = Ext.create('Ext.form.field.ComboBox', {
		name: 'protocolType',
		fieldLabel: 'FTP协议',
		store: protocolStore,
		displayField: 'protocolType',
		valueField: 'protocolType',
		triggerAction: 'all',
		editable: false,
		hidden:!scriptFileSendSftpSwitch,
		labelAlign: 'right',
		labelWidth: 300,
		mode: 'local',
		value: protocol
	});
	var ftp_combo = Ext.create('Ext.form.field.ComboBox', {
        name: 'ftp',
		fieldLabel: 'FTP',
        store: ftp_store,
        displayField: 'ftpName',
        valueField: 'iid',
        triggerAction: 'all',
        editable: false,
        labelAlign: 'right',
        labelWidth: 300,
        mode: 'local',
        value: ftpId
    });
	
	var configItems = [ftp_combo,protocolType];
	Ext.Ajax.request({
		url : 'getOsAndScriptForExtract.do',
		method : 'POST',
		async: false,
		params : {
			
		},
		success : function(response, request) {
			var data = Ext.decode(response.responseText);
			for(var os in data) {
			     var scriptUperId = data[os];
			     configItems.push(Ext.create('Ext.form.field.ComboBox', {
			         name: os,
			    	 fieldLabel: os,
			         store: os_script_store,
			         displayField: 'scriptName',
			         valueField: 'iid',
			         triggerAction: 'all',
			         editable: true,
			         value: scriptUperId>0?scriptUperId:null,
			         mode: 'local'
			     }));
			}
		},
		failure : function(result, request) {
			
		}
	});
	
    var config_form = Ext.widget({
    	region: 'center',
    	xtype: 'form',
        layout: 'form',
        cls : 'customize_panel_back',
        autoScroll : true,
        bodyPadding: '5 5 0',
        fieldDefaults: {
            labelWidth: 300,
            labelAlign: 'right'
        },
        defaultType: 'textfield',
        items: configItems,
        dockedItems: [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [ '->',{
  			xtype: "button",
  			text: "保存",
  			cls : 'Common_Btn',
  			handler: function () {
  				config_form.getForm().submit({
  				    clientValidation: true,
  				    url: 'updateExtractConfig.do',
  				    success: function(form, action) {
  				       Ext.Msg.alert('成功', '配置保存成功');
  				     forword('forwardExtractConfig.do','提取配置');
  				    },
  				    failure: function(form, action) {
  				        switch (action.failureType) {
  				            case Ext.form.action.Action.CLIENT_INVALID:
  				                Ext.Msg.alert('错误', 'Form fields may not be submitted with invalid values');
  				                break;
  				            case Ext.form.action.Action.CONNECT_FAILURE:
  				                Ext.Msg.alert('错误', 'Ajax communication failed');
  				                break;
  				            case Ext.form.action.Action.SERVER_INVALID:
  				               Ext.Msg.alert('错误', action.result.message);
  				       }
  				    }
  				});
  			}
  		}]
  		}]
    });
    
    var issuePanel = Ext.create('Ext.panel.Panel', {
        renderTo: "extractConfig_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [config_form]
    });

  
    contentPanel.on('resize', function() {
    	issuePanel.setHeight (contentPanel.getHeight () - modelHeigth);
    	issuePanel.setWidth (contentPanel.getWidth () );
    });
    
   
});

