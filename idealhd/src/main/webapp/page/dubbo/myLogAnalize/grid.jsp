<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
var scriptNameForMyLogAnalize ='<%=request.getParameter("scriptName")==null?"":request.getParameter("scriptName")%>';
var taskNameForMyLogAnalize ='<%=request.getParameter("taskName")==null?"":request.getParameter("taskName")%>';
var flowIdformyloganalize =  '<%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>';

var scriptuuidformyloganalize = '<%=request.getParameter("scriptuuidformyloganalize")==null?"":request.getParameter("scriptuuidformyloganalize")%>';
var serviceiidformyloganalize =  '<%=request.getParameter("serviceiid")==null?"":request.getParameter("serviceiid")%>';
var agentIp = '<%=request.getParameter("agentIp")==null?"":request.getParameter("agentIp")%>';
var agentNameForMyLogAnalize = '<%=request.getParameter("agentName")==null?"":request.getParameter("agentName")%>';
var osTypeForMyLogAnalize = '<%=request.getParameter("osType")==null?"":request.getParameter("osType")%>';

</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/myLogAnalize/grid.js"></script>
</head>
<body>
<div id="result_grid_area" style="width: 100%;height: 100%">
</div>
</body>
</html>