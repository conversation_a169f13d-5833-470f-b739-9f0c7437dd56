var resultPanelForMyLogAnalize;
Ext.onReady(function() {
			Ext.tip.QuickTipManager.init();
			destroyRubbish();
			var scriptsTabs_store;
			var tablessearch_form;
			 var columns;
			 var fields;
			var scriptNameStore = Ext.create('Ext.data.JsonStore', {
				fields : [ 'scriptName', 'scriptName' ],
				autoLoad : true,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getScriptName.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var tsNameStore = Ext.create('Ext.data.JsonStore', {
				fields : [ 'taskName', 'taskName' ],
				autoLoad : false,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getTaskName.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var scName = Ext.create('Ext.ux.ideal.form.ComboBox', {
				iname : 'scriptName',
				fieldLabel : '脚本名称',
				labelWidth : 100,
				width :'25%',
				istore : scriptNameStore,
				labelAlign : 'right',
				forceSelection : false,
				hidden:true,
				emptyText : "--请选择脚本名称--",
				listeners: {
		            change: function() { // old is keyup
		            	tsName.clearValue();
		            	tsName.applyEmptyText();
		            	tsName.getPicker().getSelectionModel().doMultiSelect([], false);
		            	tsNameStore.load({
		                    params: {
		                    	scriptName: this.value
		                    }
		                });
		            }
		        }
			});
			  var tsName = Ext.create('Ext.ux.ideal.form.ComboBox', {
					iname : 'taskName',
					fieldLabel : '任务名称',
					labelWidth : 100,
					width :'25%',
					istore : tsNameStore,
					hidden:true,
					labelAlign : 'right',
					forceSelection : false,
					emptyText : "--请选择任务名称--"
				});
				
			 	var agentName = new Ext.form.TextField({
					name: 'agentName',
					fieldLabel: '名称',
					emptyText: '',
					labelWidth : 65,
					labelAlign : 'right',
					width: "24.1%",
					listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	queryLog();
			                }
			            }
			        }
				});
				 var ip = new Ext.form.TextField({
					name: 'ip',
					fieldLabel: '地址',
					emptyText: '',
					labelWidth : 65,
					labelAlign : 'right',
					width: "24.1%",
					listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	queryLog();
			                }
			            }
			        }
				});
				 var osType = new Ext.form.TextField({
					name: 'osType',
					fieldLabel: '操作系统',
					emptyText: '',
					labelWidth : 65,
					labelAlign : 'right',
					width: "24.1%",
					listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	queryLog();
			                }
			            }
			        }
				});
			tablessearch_form = Ext.create('Ext.form.Panel', {
				layout : 'anchor',
				region : 'north',
				border : false,
//				baseCls:'customize_gray_back',
				dockedItems : [{
					xtype : 'toolbar',
//					baseCls:'customize_gray_back',
					dock : 'top',
					items : [agentName, ip,osType,{
								xtype : 'button',
								text : '查询',
								cls : 'Common_Btn',
//								id:'queryId',
								handler :  function() {
									queryLog();
									}
							}, {
								xtype : 'button',
								text : '清空',
								cls : 'Common_Btn',
								handler : function() {
									clearQueryWhere();
									}
							}, {
								xtype : 'button',
								text : '导出',
								cls : 'Common_Btn',
								handler : function() {
//									 if(scName.getValue()==''||null==scName.getValue()){
//									      Ext.Msg.alert('消息提示', '请选择脚本名称!');
//									      return ;
//									    }
									 
									 if(execHistoryFlag){
									      Ext.Msg.show({
															     title:'提示',
															     msg: '只支持导出日志解析的结果！',
															     buttons: Ext.Msg.OK,
															     icon: Ext.Msg.INFO
															});	
											return;
									    }else{
									    	if(selectedFlowids.length==0){
									    		 Ext.Msg.show({
															     title:'提示',
															     msg: '请选择要导出的日志解析记录！',
															     buttons: Ext.Msg.OK,
															     icon: Ext.Msg.INFO
															});	
												return;
									    	}
									    	
											$.fileDownload('sSResultReport_export.do',{
												  httpMethod: 'POST',
												  traditional: true,
												  data:{ flowids :selectedFlowids,
												  			serviceiid:serviceiidformyloganalize,
												  			scriptName:serviceName
												  		  },
												  successCallback: function(url){
												     console.log("执行历史——日志解析导出，selectedFlowids："+selectedFlowids);
												  },
												  failCallback: function (html, url) {
												   		 Ext.Msg.alert('提示', '导出失败！');
							                       		  return;
												   }
											  });
									    	
									 		//window.location.href = 'sSResultReport_export.do?scriptName='+scName.getValue()+'&taskName='+tsName.getValue();
									    }
								}
							} ]
				} ]
			});
	
 function queryLog (){
//		 if(scName.getValue()==''||null==scName.getValue()){
//		      Ext.Msg.alert('消息提示', '请选择脚本名称!');
//		      return ;
//		    }
//		 if(tsName.getValue()==''||null==tsName.getValue()){
//		      Ext.Msg.alert('消息提示', '请选择任务名称!');
//		      return ;
//		    }
		resultPanelForMyLogAnalize.getLoader().load({
    		url: 'resultGrid_SS.do',
    		params: {
//							    			scriptName: scName.getValue(),
//							    			taskName: tsName.getValue()
    			agentIp:ip.getValue().trim(),
    			agentName:agentName.getValue().trim(),
    			osType:osType.getValue().trim(),
    			serviceiid:serviceiidformyloganalize,
    			flowId:flowIdformyloganalize
    		},
    		scripts: true
    	});
	}
	
			resultPanelForMyLogAnalize = Ext.create('Ext.panel.Panel', {
				  border:false,
				  region : 'center', 
				  cls:'customize_panel_back',
				    loader : {
				      url : 'resultGrid_SS.do',
				      autoLoad : true,
			   		  params: {
			    			serviceiid:serviceiidformyloganalize,
			    			scriptuuidformyloganalize:scriptuuidformyloganalize,
			    			flowId:flowIdformyloganalize
			    		},
				      scripts : true
				    }
			  });
			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "myLogAnalize_area",
				layout : 'border',
				border : false,
				height : contentPanel.getHeight()-modelHeigth ,
				width : contentPanel.getWidth(),
				items : [ tablessearch_form, resultPanelForMyLogAnalize ]
			});

			function clearQueryWhere() {
				tablessearch_form.getForm().findField("ip").setValue('');
				tablessearch_form.getForm().findField("agentName").setValue('');
				tablessearch_form.getForm().findField("osType").setValue('');
			}
			/** 窗口尺寸调节* */
			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
				});
			//queryLog();
		});