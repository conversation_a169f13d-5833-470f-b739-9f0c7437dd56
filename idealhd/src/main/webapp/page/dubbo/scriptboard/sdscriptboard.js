Ext.onReady(function() {
	
	//---------------------------查询条件Form start--------------------------
	
	 var execBeginTime = Ext.create('Go.form.field.DateTime',{
		name : 'execBeginTime',
    	fieldLabel: '开始时间',
    	labelAlign: 'right',
		labelWidth : 93,
	    emptyText : '',
	    format : 'Y-m-d H:i:s',
	    width : '22%',
		listeners: {
	        specialkey: function(field, e){
	            if (e.getKey() == e.ENTER) {
	            	queryFunction();
//	            	grid.ipage.moveFirst();
//	            	sysStore.reload();
	            }
	        }
	    }
	});
	var execEndTime = Ext.create('Go.form.field.DateTime',{
		name : 'execEndTime',
		fieldLabel: '结束时间',
		labelAlign:'right',
		labelWidth : 93,
	    emptyText : '',
	    format : 'Y-m-d H:i:s',
	    width : '22%',
	    listeners: {
	        specialkey: function(field, e){
	            if (e.getKey() == e.ENTER) {
	            	queryFunction();
//	                    	grid.ipage.moveFirst();
//	                    	sysStore.reload();
	            }
	        }
	    }
	});
	
	  var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
		 	region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				baseCls:'customize_gray_back',
				items: [ '->',execBeginTime,execEndTime,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						queryFunction();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						 search_form.getForm().reset();
					}
				},{
					xtype : 'button',
					text : '导出',
					cls : 'Common_Btn',
					handler : function() {
						exportFunction();
					}
				}]
			}]
		});
	  
	//---------------------------查询条件Form end----------------------------
	  
	//---------------------------饼图任务执行情况报表：脚本服务任务执行情况，All任务执行次数(>0)，成功失败次数，成功率；  start--------------------------
	  Ext.define('yjChartModel', {
			extend: 'Ext.data.Model',
			fields: [
			      { name : 'name',type : 'string' },// 名称
				  { name : 'data',type : 'long' }  //数量
			]
		});

		var yjcz_store = Ext.create('Ext.data.Store', {
			autoLoad : false,
			autoDestroy : true,
			model : 'yjChartModel',
			proxy : {
				type : 'ajax',
				url : 'getSdScriptServiceExecStatisticExt.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});
		yjcz_store.on('beforeload', function (store, options) {
			var startTime=execBeginTime.getRawValue();
			var endTime=execEndTime.getRawValue();
			if(endTime.length>0 && startTime>endTime){
              return;
            }
	        var new_params = {
	        	startDate: startTime,
	        	endDate: endTime
	        };
	        Ext.apply(yjcz_store.proxy.extraParams, new_params);
		});
		
		yjcz_store.load({  
		    callback: function(records, options, success){
		    	var legendData = [];
				var seriesData = [];
		    	var totalCount=0;
				records.each(function(rec) {
	               rec.get('data');
	               var value = rec.get('data');
	               var groupname=  rec.get('name');
	               legendData.push(groupname);
	               seriesData.push({
	                   name: groupname,
	                   value: value
	               });
	               totalCount =totalCount + value;
	            });
				
				var option = {
	    		    tooltip : {
	    		        trigger: 'item'
	    		    },
	    		    title: {
	    		    	  text: ''+totalCount,
	    		          left: 'center',
	    		          bottom: '40%',
	    		          textStyle: {
	    		             // color: '#999',
	    		              fontWeight: 'normal',
	    		              fontSize: 16
	    		          }
	    		    },
	    		    legend: {
	    		        orient: 'horizontal',
	    		        left: 'center',
	    		        top: 5,
	    		        bottom: 10,
	    		        height : 181,
	    		        data: legendData,

	    		        textStyle: {
	    		            fontSize: 16
	    		        }
	    		    },
	    		    series : [
	    		        {
	    		            type: 'pie',
	    		            //radius : '60%',
	    		            radius: ['40%', '60%'],
	    		            center: ['50%', '55%'],
	    	                backgroundColor: '#eee',
	    	                borderColor: '#aaa',
	    	                borderWidth: 1,
	    	                borderRadius: 4,
	    	                label: {
	    	                    formatter: '{b}: {@2012} ({d}%)',
	    	                    fontSize: 16
	    	                },
	    	                rich: {
	    	                    a: {
	    	                        color: '#999',
	    	                        lineHeight: 22,
	    	                        align: 'center'
	    	                    },
	    	                     abg: {
	    	                         backgroundColor: '#333',
	    	                         width: '100%',
	    	                         align: 'right',
	    	                         height: 22,
	    	                         borderRadius: [4, 4, 0, 0]
	    	                     },
	    	                    hr: {
	    	                        borderColor: '#aaa',
	    	                        width: '100%',
	    	                        borderWidth: 0.5,
	    	                        height: 0
	    	                    },
	    	                    b: {
	    	                        fontSize: 16,
	    	                        lineHeight: 33
	    	                    },
	    	                    per: {
	    	                        color: '#eee',
	    	                        backgroundColor: '#334455',
	    	                        padding: [2, 4],
	    	                        borderRadius: 2
	    	                    }
	    	                },
	    		            data: seriesData,
	    		            itemStyle: {
	    		                emphasis: {
	    		                    shadowBlur: 10,
	    		                    shadowOffsetX: 0,
	    		                    shadowColor: 'rgba(0, 0, 0, 0.5)'
	    		                }
	    		                ,normal: {
			                    color: function(params) {
			                            //自定义颜色
			                            var colorList = [
			                               '#05c464','#bbbbbb'
			                            ];
			                            return colorList[params.dataIndex]
			                        }
			                    }
	    		            }
	    		        }
	    		    ]
	    		};
				var dom2 = document.getElementById("circle");
	    		var myChart2 = echarts.init(dom2);
	    		myChart2.setOption(option, true);
		    }

		 });
		
	    var jbzcbbPanel = Ext.create('Ext.panel.Panel', {
	    	width:'50%',
	    	layout : 'fit',
			//title : '脚本执行报表',
	    	title : '任务执行情况报表',
			cls:'customize_panel_back panel_space_top panel_space_right',
			//items : [ yjcz_chart ],
			html : '<div  id="circle" style="width: 100%;height: 100%"></div>'
		});
	//---------------------------饼图任务执行情况报表：脚本服务任务执行情况，All任务执行次数(>0)，成功失败次数，成功率；  end----------------------------	  
	  
	//---------------------------服务发布统计  start-------------------------------
	    
	    Ext.define('fwfbCloumnModel', {
			extend : 'Ext.data.Model',
			fields : [ 
			        { name : 'name',type : 'string' },// 系统类型
					{ name : 'data',type : 'long' },
					{ name : 'datatec',type : 'long' }
			]
		});
	    
		var fwfb_store = Ext.create('Ext.data.Store', {
			autoLoad : false,
			autoDestroy : true,
			model : 'fwfbCloumnModel',
			proxy : {
				type : 'ajax',
				url : 'getSdScriptServiceEveryMonthStatisticExt.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});
		fwfb_store.on ('beforeload', function (store, options)
		{
			var startTime=execBeginTime.getRawValue();
			var endTime=execEndTime.getRawValue();
			if(endTime.length>0 && startTime>endTime){
              return;
            }
	        var new_params = {
	        	startDate: startTime,
	        	endDate: endTime
	        };
			Ext.apply (fwfb_store.proxy.extraParams, new_params);
		});
			    
		fwfb_store.load({ 
			callback: function(records, options, success){  
				 var legendData = [];
				 var seriesData = [];
				 var seriesData2 = [];
				 records.each(function(rec) {
                   rec.get('data');
                   var value = rec.get('data');
                   var value2 = rec.get('datatec');
                   var groupname=  rec.get('name');
                   legendData.push(groupname);
                   seriesData.push({
                       value: value
                   });
                   seriesData2.push({
                	   value: value2
                   });
                });
				 var baroption = {
						    tooltip : {
						        trigger: 'axis',
						        axisPointer : {            
						            type : 'shadow'        
						        }
						    },
						    /**toolbox: {
					            show: true,
					            feature: {
					                mark: {show: true},
					                dataView: {show: true, readOnly: false},
					                magicType: {show: true, type: ['line', 'bar']},
					                restore: {show: true},
					                saveAsImage: {show: true}
					            }
					        },*/
						    calculable : true,
						    xAxis : [
						        {
						            type : 'category',
						            data : legendData,
						            axisLabel: {
			                           interval:0,
			                           rotate:40,
			                           textStyle: {
			                               color:'#4D4D4D'  //这里用参数代替了
			                           }
			                        },
			                        axisLine: {
			                            lineStyle: {
			                            	color: '#DDDDDD'
			                             }
			                        },
						        }
						    ],
						    yAxis : [
						        {
						            type : 'value',
						            axisLabel : {
						                textStyle: {
						                    color:'#4D4D4D'  //这里用参数代替了
						                }
						            },
						            axisLine: {
						                   lineStyle: {
						                       color: '#DDDDDD'
						                   }
						               },
						        }
						    ],
						    /*dataZoom: [
					            {
					                show: true,
					                start: 0,
					                end: 100
					            },
					            {
					                type: 'inside',
					                start: 0,
					                end: 100
					            },
					            {
					                show: true,
					                yAxisIndex: 0,
					                filterMode: 'empty',
					                width: 30,
					                height: '80%',
					                showDataShadow: false,
					                left: '93%'
					            }
					        ],*/
						    series : [
						        {
						            name:'已发布任务数',
						            type:'bar',
						            data:seriesData,
						            itemStyle:{
						                  normal:{color:'#05c464'}
						            }
						        },{
						        	 name:'未发布任务数',
						             type:'bar',
						             data:seriesData2,
						             itemStyle:{
						            	 normal:{color:'#bbbbbb'}
						             }
						        }
						    ]
						};
					var dom6 = document.getElementById("fwfbRun_bar");
					var myChart6 = echarts.init(dom6);
					myChart6.setOption(baroption, true);
			}
		});
	    
	    var fwfbPanel = Ext.create('Ext.panel.Panel', {
			width:'50%',
			layout : 'fit',
			//title : '服务发布统计',
			title : '任务发布情况报表',
			cls:'customize_panel_back panel_space_top',
			//items : [ fwfb_chart ]
			html : '<div  id="fwfbRun_bar" style="width: 100%;height: 100%"></div>'
		});
	    
	
		var pandectnewPanel3 = Ext.create('Ext.panel.Panel', {
			flex : 1,
			border : false,
			title : '',
			layout : {
				type : 'hbox',
				align : 'stretch'
			},
			items : [jbzcbbPanel,fwfbPanel]
		});
		//---------------------------服务发布统计  end---------------------------------    
		
		
	
		//2。用户维度统计图表---------------------------用户为维度统计，执行脚本次数、发布脚本次数、创建脚本次数  start----------------------
	    
	    Ext.define('userStaticScriptCloumnModel', {
			extend : 'Ext.data.Model',
			fields : [ 
			        { name : 'name',type : 'string' },
					{ name : 'total',type : 'long' },
					{ name : 'yfb',type : 'long' },
					{ name : 'cjs',type : 'long' }
			]
		});
	    
		var userStatisticScriptStore = Ext.create('Ext.data.Store', {
			autoLoad : false,
			autoDestroy : true,
			model : 'userStaticScriptCloumnModel',
			proxy : {
				type : 'ajax',
				//url : 'getScriptServiceEveryMonthStatisticExt.do',
				url : 'getScriptServiceUserStatisticExt.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});

	    
	    var userStatisticScriptPanel = Ext.create('Ext.panel.Panel', {
			width:'100%',
			layout : 'fit',
			title : '人员使用情况报表',
			cls:'customize_panel_back panel_space_top',
			html : '<div  id="userStatisticScriptBar" style="width: 100%;height: 100%"></div>'

		});
	    userStatisticScriptStore.on ('beforeload', function (store, options)
		{
	    	var startTime=execBeginTime.getRawValue();
			var endTime=execEndTime.getRawValue();
			if(endTime.length>0 && startTime>endTime){
              return;
            }
	        var new_params = {
	        	startDate: startTime,
	        	endDate: endTime
	        };

			Ext.apply (userStatisticScriptStore.proxy.extraParams, new_params);
		});
	    
    	userStatisticScriptStore.load({ 
			callback: function(records, options, success){  
				 var legendData5 = [];
				 var seriesData5 = [];
				 var seriesData6 = [];
				 var seriesData7 = [];
				 records.each(function(rec) {
                   rec.get('total');
                   var value = rec.get('total');
                   var groupname=  rec.get('name');
                   var yfb = rec.get('yfb');
                   var wfb = rec.get('cjs');
                   legendData5.push(groupname);
                   seriesData5.push({
                       value: value
                   });
                   seriesData6.push({
                	   value: yfb
                   });
                   seriesData7.push({
                	   value: wfb
                   });
                });
				 
				 
				 var baroption = {
						    tooltip : {
						        trigger: 'axis',
						        axisPointer : {            
						            type : 'shadow'        
						        }
						    },
						    /**toolbox: {
					            show: true,
					            feature: {
					                mark: {show: true},
					                dataView: {show: true, readOnly: false},
					                magicType: {show: true, type: ['line', 'bar']},
					                restore: {show: true},
					                saveAsImage: {show: true}
					            }
					        },*/
						    calculable : true,
						    legend: {
						        data: legendData5
						    },
						    xAxis : [
						        {
						            type : 'category',
						            data : legendData5,
						            axisLabel: {
			                           interval:0,
			                           rotate:40,
			                           textStyle: {
			                               color:'#4D4D4D'  //这里用参数代替了
			                           }
			                        },
			                        axisLine: {
			                            lineStyle: {
			                            	color: '#DDDDDD'
			                             }
			                        },
						        }
						    ],
						    yAxis : [
						        {
						            type : 'value',
						            axisLabel : {
						                textStyle: {
						                    color:'#4D4D4D'  //这里用参数代替了
						                }
						            },
						            axisLine: {
						                   lineStyle: {
						                       color: '#DDDDDD'
						                   }
						               },
						        }
						    ],
						    /**dataZoom: [
					            {
					                show: true,
					                start: 0,
					                end: 100
					            },
					            {
					                type: 'inside',
					                start: 0,
					                end: 100
					            },
					            {
					                show: true,
					                yAxisIndex: 0,
					                filterMode: 'empty',
					                width: 30,
					                height: '80%',
					                showDataShadow: false,
					                left: '93%'
					            }
					        ],*/
						    dataZoom: [{
						        type: 'inside'
						    }, {
						        type: 'slider'
						    }],
						    series : [
						        {
						            name:'执行脚本次数',
						            type:'bar',
						            data:seriesData5,
						            itemStyle:{
						                  normal:{color:'#fc6630'}
						            }
						        },{
						        	 name:'发布脚本次数',
						             type:'bar',
						             data:seriesData6,
						             itemStyle:{
						            	 normal:{color:'#05c464'}
						             }
						        },{
						        	 name:'创建脚本次数',
						             type:'bar',
						             data:seriesData7,
						             itemStyle:{
						            	 normal:{color:'#409dfa'}
						             }
						        }
						    ]
						};
					var dom6 = document.getElementById("userStatisticScriptBar");
					var myChart6 = echarts.init(dom6);
					myChart6.setOption(baroption, true);
			}
		});
		
	  
	//---------------------------用户为维度统计，执行脚本次数、发布脚本次数、创建脚本次数  end--------------------------	

	var pandectnewPanel2 = Ext.create('Ext.panel.Panel', {
		flex : 1,
		border : false,
		title : '',
//		layout : {
//			type : 'hbox',
//			align : 'stretch'
//		},
		layout : 'fit',
		items : [ userStatisticScriptPanel/*,jbcglPanel,ztxxhzPanel*/ ]
	});
	
	var pandectnewPanel1 = Ext.create('Ext.panel.Panel', {
		border : false,
		region: 'center',
		title:'',
		layout : {
			type : 'vbox',
			align : 'stretch'
		},
		items : [ pandectnewPanel3,pandectnewPanel2  ]
	});
	
	var autoScrollPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		height : contentPanel.getHeight()-50,
		layout : {
			type : 'border'
		},
		items : [ search_form, pandectnewPanel1 ]
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "sdscriptboard_div",
		border : false,
		autoScroll : true,
		height : contentPanel.getHeight(),
		width : contentPanel.getWidth(),
		items : [ autoScrollPanel ]
	});
	mainPanel.setWidth(contentPanel.getWidth());
	mainPanel.setHeight(contentPanel.getHeight());
	
    contentPanel.on('resize',function(){
    	mainPanel.setWidth(contentPanel.getWidth());
    	mainPanel.setHeight(contentPanel.getHeight());
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function (obj, options, eOpts) {
            Ext.destroy(mainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });
    
    //查询按钮增加时间的查询条件
    function queryFunction(){
    	fwfb_store.reload();
		yjcz_store.reload();
		userStatisticScriptStore.reload();
	}
    
    function exportFunction(){
    	var stime = execBeginTime.getRawValue();
    	var etime = execEndTime.getRawValue();
    	if(!checkIsNotEmpty(stime) && checkIsNotEmpty(etime) ){
			Ext.Msg.alert ('提示', '请选择开始日期！');
				return false;
		}
    	if(!checkIsNotEmpty(etime) && checkIsNotEmpty(stime) ){
			Ext.Msg.alert ('提示', '请选择结束日期！');
				return false;
		}
    	if(stime>etime){
			Ext.Msg.alert ('提示', '开始日期不能大于结束日期！');
				return false;
		}
    	$.fileDownload('exportSdScriptBoardData.do',{
		  httpMethod: 'POST',
		  traditional: true,
		  data:{ 
			  startDate :stime,
			  endDate: etime
		  },
		  successCallback: function(url){
		  },
		  failCallback: function (html, url) {
		   		 Ext.Msg.alert('提示', '导出失败！');
         		  return;
		   }
	  }); 
    }
    
});