Ext.onReady(function() {
	
	//---------------------------查询条件Form start--------------------------
	
	 var execBeginTime = Ext.create('Go.form.field.DateTime',{
		name : 'execBeginTime',
    	fieldLabel: '开始时间',
    	labelAlign: 'right',
		labelWidth : 93,
	    emptyText : '',
	    format : 'Y-m-d H:i:s',
	    width : '22%',
		listeners: {
	        specialkey: function(field, e){
	            if (e.getKey() == e.ENTER) {
	            	queryFunction();
//	            	grid.ipage.moveFirst();
//	            	sysStore.reload();
	            }
	        }
	    }
	});
	var execEndTime = Ext.create('Go.form.field.DateTime',{
		name : 'execEndTime',
		fieldLabel: '结束时间',
		labelAlign:'right',
		labelWidth : 93,
	    emptyText : '',
	    format : 'Y-m-d H:i:s',
	    width : '22%',
	    listeners: {
	        specialkey: function(field, e){
	            if (e.getKey() == e.ENTER) {
	            	queryFunction();
//	                    	grid.ipage.moveFirst();
//	                    	sysStore.reload();
	            }
	        }
	    }
	});
	
	  var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
		 	region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				baseCls:'customize_gray_back',
				items: [ '->',execBeginTime,execEndTime,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						queryFunction();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						 search_form.getForm().reset();
					}
				}]
			}]
		});
	  
	//---------------------------查询条件Form end----------------------------
	  
	//---------------------------脚本执行报表  start--------------------------
	  Ext.define('yjChartModel', {
			extend: 'Ext.data.Model',
			fields: [
			     { name:'name', type:'string' },
			     { name:'total', type:'long' },
			     { name:'success', type:'long' },// 成功
			     { name:'failure', type:'long' }// 终止
			]
		});

		var yjcz_store = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'yjChartModel',
			proxy : {
				type : 'ajax',
				url : 'getScriptServiceEveryDayStatisticExt.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});
		yjcz_store.on('beforeload', function (store, options) {
			var startTime=execBeginTime.getRawValue();
			var endTime=execEndTime.getRawValue();
			if(endTime.length>0 && startTime>endTime){
              return;
            }
	        var new_params = {
	        	startDate: startTime,
	        	endDate: endTime
	        };
	        Ext.apply(yjcz_store.proxy.extraParams, new_params);
		});
	    var yjcz_chart = Ext.create('Ext.chart.Chart', {
	        animate: true,
	        store: yjcz_store,
	        shadow: false,
	        theme: 'Base:gradients',
//	        legend: {
//                  position: 'right',
//                  itemSpacing :5,
//			      boxFill :skinObj.colourRGB, 
//			      boxStroke:skinObj.colourRGB, 
//			      labelFont :'14px 微软雅黑',
//			      labelColor:skinObj.labelcolor
//            },
	        axes: [{
	            type: 'Numeric',
	            minimum: 0,
	            position: 'left',
	            fields: ['success','failure'],
	            minorTickSteps: 1,
	            majorTickSteps:3,
	            grid:true,
	            dashSize:0,
	            
	        }, {
	            type: 'Category',
	            position: 'bottom',
	            fields: ['name'],
	            title: '',
	            dashSize:0
	            
	        }],
	        series: [{
	            type: 'line',
	            style: {
	                fill: '#0083fe',
	                stroke: '#0083fe',
	                'stroke-width': 3
	            },
	            highlight: {
	                size: 6,
	                radius: 6,
	                fill: 'red',
	                'stroke-width': 3,
	                stroke: '#fff'
	            },
	            axis: 'left',
	            smooth: true,
//	            fill: true,
	            xField: 'name',
	            yField: 'success',
	            markerConfig: {
	                type: 'circle',
	                size: 6,
	                radius: 6,
	                'stroke-width': 3,
	                stroke: '#fff'
	            }
	        },{
	            type: 'line',
	            style: {
	                fill: '#dedede',
	                stroke: '#dedede',
	                'stroke-width': 3
	            },
	            highlight: {
	                size: 6,
	                radius: 6,
	                fill: 'red',
	                'stroke-width': 3,
	                stroke: '#fff'
	            },
	            axis: 'left',
	            smooth: true,
//	            fill: true,
	            xField: 'name',
	            yField: 'failure',
	            markerConfig: {
	                type: 'circle',
	                size: 6,
	                radius: 6,
	                'stroke-width': 3,
	                stroke: '#fff'
	            }
	        }]
	    });
	    var jbzcbbPanel = Ext.create('Ext.panel.Panel', {
			width:'49.4%',
			layout : 'fit',
			title : '脚本执行报表',
			cls:'customize_panel_back panel_space_top panel_space_right',
//			cls:' panel_space_top_right ',
			items : [ yjcz_chart ]
		});
	//---------------------------脚本执行报表  end----------------------------	  
	  
	//---------------------------服务发布统计  start-------------------------------
	    
	    Ext.define('fwfbCloumnModel', {
			extend : 'Ext.data.Model',
			fields : [ 
			        { name : 'name',type : 'string' },// 系统类型
					{ name : 'data',type : 'long' }
			]
		});
	    
		var fwfb_store = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'fwfbCloumnModel',
			proxy : {
				type : 'ajax',
				url : 'getScriptServiceEveryMonthStatisticExt.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});

	    var fwfb_chart = Ext.create('Ext.chart.Chart', {
	        animate: true,
	        store: fwfb_store,
	        axes: [{
	                type: 'Numeric',
	                position: 'left',
	                fields: ['data'],
	                label: {
	                    renderer: Ext.util.Format.numberRenderer('0,0')
	                },
	                grid: false,
	                minimum: 0
	            },
	            {
	                type: 'Category',
	                position: 'bottom',
	                fields: ['name']
	            }
	        ],
	        series: [{
	                type: 'column',
	                axis: 'left',
	                theme: 'Base:gradients',
	                highlight: true,
	                renderer: function(sprite, storeItem, barAttr, i, store) {    
	                    barAttr.fill = '#007AF5';  
	                    barAttr.width=40;
	                    return barAttr;    
	                },
	                tips: {
	                  trackMouse: true,
	                  width: 30,
	                  height: 30,
	                  renderer: function(storeItem, item) {
	                    this.setTitle(storeItem.get('data'));
	                  }
	                },
	                label: {
	                 	display: 'insideEnd',
	                 	'text-anchor': 'middle',
	                    field: '',
	                    renderer: Ext.util.Format.numberRenderer('0'),
	                    orientation: 'vertical',
	                    color: 'red'
	                },
	                xField: 'name',
	                yField: 'data'
	            }
	        ]
        });
	    
	    var fwfbPanel = Ext.create('Ext.panel.Panel', {
			width:'50%',
			layout : 'fit',
			title : '服务发布统计',
			cls:'customize_panel_back panel_space_top',
			items : [ fwfb_chart ]
		});
	    
	//---------------------------服务发布统计  end---------------------------------    
	//---------------------------第二层 start-------------------------------------
		var pandectnewPanel3 = Ext.create('Ext.panel.Panel', {
			flex : 1,
			border : false,
			title : '',
			layout : {
				type : 'hbox',
				align : 'stretch'
			},
			items : [jbzcbbPanel,fwfbPanel]
		});
	//---------------------------第二层 end--------------------------------------
	
	//---------------------------原子脚本服务使用频率  start----------------------
		var yzjbfwsylModel = Ext.define('yzjbfwsylModel', {
	        extend: 'Ext.data.Model',
	        fields: [
	                 {name: 'iid',  type: 'long'},
	                 {name: 'serviceName',  type: 'string'},
	                 {name: 'version', type: 'string'},
	                 {name: 'total',	type:'int'},
	                 {name: 'createTime', type: 'string'},
	                 {name: 'createUser',  type: 'string'}
		           ]
		});
		
		function yzjbfwsylForward(a, b, c, d, e, record) {
			var iid = record.get('iid');
			popNewTab('任务申请', 'scriptServicesTaskExec.do', {requestFromC3Char:true,scriptIID:iid},10, true);
		}
		
		var yzjbfwsylColumns = [
   			{
   	            text : '序号',
   	            width : 70,
   	            align:'left',
   	            xtype : 'rownumberer',
   				listeners: {
   					click :  yzjbfwsylForward
   				}
   	        },
   	        {
   	        	text : '主键',
   				dataIndex : 'iid',
   				hidden:true,
   				width : 50
   			},
   	        {
   	        	text : '服务名称',
   				dataIndex : 'serviceName',
   				width : 150,
   				flex:1,
   				listeners: {
   					click :  yzjbfwsylForward
   				}
   			},
   			{
   	        	text : '版本',
   				dataIndex : 'version',
   				width : 60,
   				listeners: {
   					click :  yzjbfwsylForward
   				}
   			}, 
   			{
   				text : '使用次数',
   				dataIndex : 'total',
   				width : 120,
   				listeners: {
   					click :  yzjbfwsylForward
   				}
   			}, 
   			{
   				text : '发布时间',
   				dataIndex : 'createTime',
   				width : 150,
   				listeners: {
   					click :  yzjbfwsylForward
   				}
   			}, 
   			{
   				text : '创建人',
   				dataIndex : 'createUser',
   				width : 150,
   				listeners: {
   					click :  yzjbfwsylForward
   				}
   			}
       	];
		
		var yzjbfwsylStore = Ext.create('Ext.data.Store', {
	        model: 'yzjbfwsylModel',
	        autoLoad:true,
			pageSize:30,
	        proxy: {
	            type: 'ajax',
	            url: 'getScriptServiceTopFiveStatistic.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
		yzjbfwsylStore.on('beforeload', function (store, options) {
			var startTime=execBeginTime.getRawValue();
			var endTime=execEndTime.getRawValue();
			if(endTime.length>0 && startTime>endTime){
            	Ext.Msg.alert('提示', "开始时间不能晚于结束时间！");
              return;
            }
	        var new_params = {
	        	startDate: startTime,
	        	endDate: endTime
	        };
	        Ext.apply(yzjbfwsylStore.proxy.extraParams, new_params);
		});
		
//		var yzjbfwsyl_grid = Ext.create('Ext.grid.Panel', {
		var yzjbfwsyl_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
			border:true,
			cls:'customize_panel_back',
			ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			forceFit: true,
			autoScroll:false,
		    store: yzjbfwsylStore,	
		    columns: yzjbfwsylColumns,
			padding : grid_space,
		    columnLines : true,
		    cellTip : true,
			selType: 'cellmodel'
		});	
	  
	  var yzjbfwsylPanel = Ext.create('Ext.panel.Panel', {
			width:'50.5%',
			layout : 'fit',
			title : '原子脚本服务使用频率',
			cls:'customize_panel_back panel_space_top panel_space_right',
			items : [ yzjbfwsyl_grid ]
		});
	  
	//---------------------------原子脚本服务使用频率  end--------------------------	
		
		
	//---------------------------脚本成功率 start----------------------------------
    Ext.define('zyddPieModel', {
		extend : 'Ext.data.Model',
		fields : [ 
		        { name : 'name',type : 'string' },// 系统类型
				{ name : 'data',type : 'long' }
		]
	});

	var zydd_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'zyddPieModel',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceExecStatisticExt.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	zydd_store.on('beforeload', function (store, options) {
		var startTime=execBeginTime.getRawValue();
		var endTime=execEndTime.getRawValue();
		if(endTime.length>0 && startTime>endTime){
          return;
        }
        var new_params = {
        	startDate: startTime,
        	endDate: endTime
        };
        Ext.apply(zydd_store.proxy.extraParams, new_params);
	});
	
    var zydd_chart = Ext.create('Ext.chart.Chart', {
//    	html : '<div class="yybs_circle"><div class="yysb_circle_cn">当前运行情况</div></div>',
    	region: 'center',
        xtype: 'chart',
        animate: true,
        store: zydd_store,
        shadow: false,
        insetPadding: 30,
        legend: {
              position: 'bottom',
              itemSpacing :5,
		      boxFill :skinObj.colourRGB, 
		      boxStroke:skinObj.colourRGB, 
		      labelFont :'14px 微软雅黑',
		      labelColor:skinObj.labelcolor,
		      data: [
		             {
                        name:'成功',
                        icon:'circle'
		             },
		             {
                        name:'失败',
                        icon:'circle'
		             }
                ]
        },
        
        theme: 'Base:gradients',
        series: [{
            type: 'pie',
            field: 'data',
            showInLegend: true,
            donut: 70,
            colorSet: ['#26d6b0','#dedede'],
            tips: {
              trackMouse: true,
              width: 140,
              height: 28,
              renderer: function(storeItem, item) {
                var total = 0;
                bggl_store.each(function(rec) {
                    total += rec.get('data');
                });
                this.setTitle(storeItem.get('name') + ': ' + Math.round(storeItem.get('data')));
              }
            },
            highlight: {
              segment: {
                margin: 0
              }
            },
            label: {
                field: 'name',
                display: 'none',
                contrast: true,
                font: '18px Arial'
            }
        }]
    });
	
//    var zydd_html = Ext.create('Ext.panel.Panel', {
//    	 region: 'south',
//		 border :false,
//		 height : 30,
//		 html : ''
//    });
//    
//    zydd_store.on('load',function(obj, records, successful, eOpts){
//    	var colorArray = ['#63E3FE','#007AF5'];
//    	var htmlValue = '<div class="zzdd_Fonts">';
//    	for(var i=0;i<records.length;i++){
//    		if(i==0){
//    			htmlValue = htmlValue + "<span style='display:inline-block;margin-right:9px;margin-left:8px;vertical-align:middle;width:5px;height:5px;border-radius:50%;background:"+colorArray[i]+"'></span>"+records[i].get('name') +"" + '<font color="'+colorArray[i]+'">' + /*records[i].get('data') +*/ '</font>';
//    		}else{
//    			htmlValue = htmlValue + "<span style='display:inline-block;margin-right:9px;margin-left:8px;vertical-align:middle;width:5px;height:5px;border-radius:50%;background:"+colorArray[i]+"'></span>" + records[i].get('name') +"" + '<font color="'+colorArray[i]+'">' + /*records[i].get('data') + */'</font>';
//    		}
//    	}
//    	htmlValue = htmlValue + '</div>';
//    	zydd_html.body.update(htmlValue);
//    });
    
	var jbcglPanel = Ext.create('Ext.panel.Panel', {
		width:'25%',
		layout : 'border',
		title : '脚本成功率',
		cls:'customize_panel_back panel_space_top panel_space_right',
		html:'',
		items : [ zydd_chart /*,zydd_html*/ ]
	});
	//---------------------------脚本成功率  end---------------------------------------
	//---------------------------整体信息汇总  start-----------------------------------
	var ztxxhzModel = Ext.define('ztxxhzModel', {
        extend: 'Ext.data.Model',
        fields: [
             {name: 'typeKey',  type: 'string'},
             {name: 'typeName',  type: 'string'},
             {name: 'typeNum', type: 'int'}
        ]
	});
	
	function ztxxhzFowSkip(a, b, c, d, e, record) {
		
		var typeKey = record.get('typeKey');
		
		//一级分类添加跳转
		if(typeKey=='bsTotal'){
		   popNewTab('类别维护', 'forwardBsManager.do', {requestFromC3Char:true},10, true);
		};
		
		//设备数量添加跳转
		if(typeKey=='agentTotal'){
		   popNewTab('Agent管理', 'agentMaintainIndex.do', {requestFromC3Char:true},10, true);
		};
		
		//脚本数量添加跳转
		if(typeKey=='scriptTotal'){
		   popNewTab('我的脚本', 'forwardScriptServiceRelease.do', {requestFromC3Char:true},10, true);
		};
	}
	
	var ztxxhzColumns = [
		{
			text : '分类key',
			dataIndex : 'typeKey',
			hidden:true
		},                
		{
        	text : '汇总分类',
			dataIndex : 'typeName',
			width : 150,
			flex:1,
			listeners: {
				click :  ztxxhzFowSkip
			}
		}, 
		{
			text : '数量',
			dataIndex : 'typeNum',
			width : 100,
			renderer : function(value, cellmeta, record, rowIndex,columnIndex, store) {
				return record.data['typeNum'];
			},
			listeners: {
				click :  ztxxhzFowSkip
			}
		}
   	];
	
	var ztxxhzStore = Ext.create('Ext.data.Store', {
        model: 'ztxxhzModel',
        autoLoad:true,
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceOtherStatisticExt.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
	});
	ztxxhzStore.on('beforeload', function (store, options) {});
//	var ztxxhz_grid = Ext.create('Ext.grid.Panel', {
	var ztxxhz_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
			cls:'customize_panel_back panel_space_left panel_space_right',
			ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			forceFit: true,
			hideHeaders:true,
			autoScroll:false,
		    store: ztxxhzStore,	
		    columns: ztxxhzColumns,
		    columnLines : true,
		    cellTip : true,
			selType: 'cellmodel'
	});	

  var ztxxhzPanel = Ext.create('Ext.panel.Panel', {
		width:'25%',
		layout : 'fit',
		title : '整体信息汇总',
		cls:'customize_panel_back panel_space_top',
		items : [ ztxxhz_grid ]
	});
  
//---------------------------整体信息汇总  end--------------------------	
	var pandectnewPanel2 = Ext.create('Ext.panel.Panel', {
		flex : 1,
		border : false,
		title : '',
		layout : {
			type : 'hbox',
			align : 'stretch'
		},
		items : [ yzjbfwsylPanel,jbcglPanel,ztxxhzPanel ]
	});
	
	var pandectnewPanel1 = Ext.create('Ext.panel.Panel', {
		border : false,
		region: 'center',
		title:'',
		layout : {
			type : 'vbox',
			align : 'stretch'
		},
		items : [ pandectnewPanel3,pandectnewPanel2  ]
	});
	
	var autoScrollPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		height : contentPanel.getHeight()-50,
		layout : {
			type : 'border'
		},
		items : [ search_form, pandectnewPanel1 ]
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptboard_div",
		border : false,
		autoScroll : true,
		height : contentPanel.getHeight(),
		width : contentPanel.getWidth(),
		items : [ autoScrollPanel ]
	});
	mainPanel.setWidth(contentPanel.getWidth());
	mainPanel.setHeight(contentPanel.getHeight());
	
    contentPanel.on('resize',function(){
    	mainPanel.setWidth(contentPanel.getWidth());
    	mainPanel.setHeight(contentPanel.getHeight());
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function (obj, options, eOpts) {
            Ext.destroy(mainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });
    
    //查询按钮增加时间的查询条件
    function queryFunction(){
    	yzjbfwsylStore.reload();
		yjcz_store.reload();
		zydd_store.reload();
	}
});