Ext
		.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			// IEAI_SCRIPT_APPSYSTEM_PROP
			Ext.define('ResourceAttribute', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'idbtype',
					type : 'string'
				}, {
					name : 'iorder',
					type : 'string'
				}, {
					name : 'icreatetime',
					type : 'long'
				}, {
					name : 'imodifytime',
					type : 'long'
				}, {
					name : 'iurl',
					type : 'string'
				}, {
					name : 'idriver',
					type : 'string'
				} ]
			});

			resourceAttributeStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 50,
				model : 'ResourceAttribute',
				proxy : {
					type : 'ajax',
					url : 'getResourceAttributeList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			var resourceAttributeColumns = [ {
				text : '序号',
				xtype : 'rownumberer',
				width : 70,
				resizable : true
			}, {
				text : 'ID',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			}, {
				text : '类型名称',
				dataIndex : 'idbtype',
				minwidth : 80,
				editor : {
					allowBlank : false
				}
			}, {
				text : '顺序',
				dataIndex : 'iorder',
				minwidth : 40,
				editor : {
					allowBlank : false
				}
			}, {
				text : 'db.url（{IP}:{PORT}/{NAME}）',
				dataIndex : 'iurl',
				flex : 2,
//				minwidth : 500,
				editor : {
					allowBlank : false
				}
			}, {
				text : 'db.driver',
				dataIndex : 'idriver',
				flex : 1,
				editor : {
					allowBlank : false
				}
			}, {
                text : '创建时间',
                dataIndex : 'icreatetime',
                width : 180
            }, {
                text : '修改时间',
                dataIndex : 'imodifytime',
                width : 180
            } ];
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 1
			});

			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
				store : resourceAttributeStore,
				dock : 'bottom',
				baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo : true,
				border : false
			});

			var form = Ext.create('Ext.form.FormPanel', {
				region : 'north',
				padding : '5 0 5 0',
				bodyCls : 'x-docked-noborder-top',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					baseCls : 'customize_gray_back',
					border : false,
					dock : 'top',
					items : [ '->', {
						text : '增加',
						cls : 'Common_Btn',
						handler : add
					}, {
						text : '保存',
						cls : 'Common_Btn',
						handler : save
					}, '-', {
						itemId : 'delete',
						text : '删除',
						cls : 'Common_Btn',
						disabled : true,
						handler : dele
					} ]
				} ]
			});

			var resourceAttributeGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : resourceAttributeStore,
				cls : 'customize_panel_back',
				selModel : Ext.create('Ext.selection.CheckboxModel', {
					checkOnly : true
				}),
				plugins : [ cellEditing ],
				padding : panel_margin,
				border : true,
				bbar : pageBar,
				columnLines : true,
				columns : resourceAttributeColumns,
				animCollapse : false
			});

			resourceAttributeGrid.getSelectionModel().on(
					'selectionchange',
					function(selModel, selections) {
						form.down('#delete').setDisabled(
								selections.length === 0);
					});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "resourceAttribute_grid_area",
				layout : 'border',
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				bodyPadding : grid_margin,
				border : true,
				bodyCls : 'service_platform_bodybg',
				items : [ form, resourceAttributeGrid ]
			});

			function setMessage(msg) {
				Ext.Msg.alert('提示', msg);
			}

			function add() {
				var store = resourceAttributeGrid.getStore();
				var p = {
					iid : '',
					idbtype : '',
					iorder : '',
					icreatetime : '',
					imodifytime : '',
					iurl : '',
					idriver : '',
				};
				store.insert(0, p);
				resourceAttributeGrid.getView().refresh();
			}

			/* 解决IE下trim问题 */
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};

			function save() {
				var m = resourceAttributeStore.getModifiedRecords();
				if (m.length < 1) {
					setMessage('无需要增加或者修改的数据！');
					return;
				}
				var jsonData = "[";
				for (var i = 0, len = m.length; i < len; i++) {
					var iid =m[i].get("iid");
					var idbtype = m[i].get("idbtype").trim();
					var iorder = m[i].get("iorder");
					var iurl = m[i].get("iurl").trim();
					var idriver = m[i].get("idriver").trim();
					if(iid==1 || iid==2|| iid==3) {
					    setMessage('内置类型不可删除！');
                        return;
					}
					if ("" == idbtype || null == idbtype) {
						setMessage('类别名称不能为空！');
						return;
					}
					if ("" == iorder || null == iorder) {
						setMessage('顺序不能为空！');
						return;
					}
					if ("" == iurl || null == iurl) {
						setMessage('db.url不能为空,且IP，端口，服务名采用{IP}:{PORT}替换！');
						return;
					}
//					if (iurl.indexOf("jdbc-") == -1) {
//						setMessage('db.url不符合规则，请重新输入！');
//						return;
//					}
					if ("" == idriver || null == idriver) {
						setMessage('db.driver不能为空！');
						return;
					}
					
//					if(!idriver.match(){
//					25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)
//						setMessage('driver不符合规则，请重新输入！');
//						return;
//					}
					
					var ss = Ext.JSON.encode(m[i].data);
					if (i == 0)
						jsonData = jsonData + ss;
					else
						jsonData = jsonData + "," + ss;
				}
				jsonData = jsonData + "]";
				Ext.Ajax
						.request({
							url : 'saveResourceAttribute.do',
							method : 'POST',
							params : {
								jsonData : jsonData
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
								if (success) {
									resourceAttributeStore.modified = [];
									resourceAttributeStore.reload();
									Ext.Msg.alert('提示', message);
								} else {
									Ext.Msg.alert('提示', message);
								}
							},
							failure : function(result, request) {
								secureFilterRs(result, "操作失败！");
							}
						});
			}

			function dele() {
				var data = resourceAttributeGrid.getView().getSelectionModel()
						.getSelection();
				if (data.length == 0) {
					Ext.Msg.alert('提示', '请先选择您要操作的行!');
					return;
				} else {
					Ext.Msg
							.confirm(
									"请确认",
									"是否真的要删除命令？",
									function(button, text) {
										if (button == "yes") {
											var ids = [];
											Ext.Array.each(data, function(
													record) {
												var iid = record.get('iid');
												// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
												if (iid) {
													ids.push(iid);
												} else {
													resourceAttributeStore
															.remove(record);
												}
											});
											if (ids.length > 0) {
												// 如果删除的事内置数据，oracle，db2，mysql，不允许删除
												if(ids.indexOf(1)||ids.indexOf(2)||ids.indexOf(3)){
													Ext.Msg.alert('提示','您所删除的数据包含内置数据，不可以删除，请重新选择');
												}
												else{
													Ext.Ajax
															.request({
																url : 'deleteResourceAttribute.do',
																params : {
																	deleteIds : ids
																			.join(',')
																},
																method : 'POST',
																success : function(
																		response,
																		opts) {
																	var success = Ext
																			.decode(response.responseText).success;
																	// 当后台数据同步成功时
																	if (success) {
																		resourceAttributeStore
																				.reload();
																		Ext.Msg
																				.alert(
																						'提示',
																						Ext
																								.decode(response.responseText).message);
																	} else {
																		Ext.Msg
																				.alert(
																						'提示',
																						'删除失败！');
																	}
																},
																failure : function(
																		result,
																		request) {
																	secureFilterRs(
																			result,
																			"操作失败！");
																}
															});
													}
											} else {
												resourceAttributeGrid.getView()
														.refresh();
											}
										}
									});
				}
			}

			/** 窗口尺寸调节* */
			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
			});
			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(resourceAttributeGrid);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
		});
