/*!
 * 资源组与服务器绑定并定制个性化参数
 *
 * Copyright: Copyright 2003
 * Company: ideal
 * Author: li_yang
 *
 * Date: 2014-8-22
 */
Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);

Ext.onReady(function() {
  var paraWaitForChooseWin;
  var paraViewWin;
  var currentServerId;
  var currentGroupId=-1;
  var curServer=-1;
  var toBindServerIdstr = [];//待增加服务器数组
  var unBindServerIdstr = [];//选择的要解绑的服务器数组
  Ext.tip.QuickTipManager.init();
  
  Ext.define('serverModel', {
    extend : 'Ext.data.Model',
    fields : [ {
      name : 'id',
      type : 'int',
      useNull : true
    }, {
      name : 'hostName',
      type : 'string'
    }, {
      name : 'ip',
      type : 'string'
    }, {
      name : 'iexecUserName',
      type : 'string'
	}, {
	  name : 'port',
	  type : 'string',
	  defaultValue: 1500
    },{
      name : 'priority',
      type : 'int',
      defaultValue: 5
    }, {
      name : 'systemType',
      type : 'string'
    }, {
      name : 'checked',
      type : 'boolean'
    }, {
      name : 'itemClicked',
      type : 'boolean'
    }, {
        name : 'isleftScreen',
        type : 'boolean',
        defaultValue: false
      } ]
  });
  
  Ext.define('ServerParameterModel', {
    extend : 'Ext.data.Model',
    fields : [ {
      name : 'id',
      type : 'int',
      useNull : true
    }, {
      name : 'name',
      type : 'string'
    }, {
      name : 'paramType',
      type : 'string'
    }, {
      name : 'value',
      type : 'string'
    },{
      name : 'vid',
      type : 'int'
    },{
      name : 'serverId',
      type : 'int'
    } ]
  });
  
  Ext.define('resourceGroupModel', {
    extend : 'Ext.data.Model',
    fields : [{
      name : 'id',
      type : 'int',
      useNull : true
    }, {
      name : 'name',
      type : 'string'
    }, {
      name : 'description',
      type : 'string'
    } ]
  });
  
  Ext.define('paraViewModel', {
    extend : 'Ext.data.Model',
    fields : [{
      name : 'resName',
      type : 'string'
    }, {
      name : 'name',
      type : 'string'
    }, {
      name : 'paramType',
      type : 'string'
    },{
      name : 'vid',
      type : 'int'
    }, {
      name : 'originalValue',
      type : 'string'
    }, {
      name : 'value',
      type : 'string'
    }, {
        name : 'typeprimal',
        type : 'string'
      }]
  });
  
  var resourceGroupColumns = [
    {text: '编号', hidden: true, dataIndex: 'id'},
    {text: '序号', xtype: 'rownumberer', width: 40},
    {text: '名称', dataIndex: 'name', flex: 1, sortable: true},
    {text: '描述', dataIndex: 'description', hidden: true, flex: 1}
  ];
  
  var paraColumns = [
    {text: '编号', width: 40, sortable: true, hidden: true, dataIndex: 'id'},
    {text: '序号', xtype: 'rownumberer', width: 40},
    {text: '参数名称', dataIndex: 'name', flex: 2, sortable: true},
    {text: '参数类型', dataIndex: 'paramType', flex: 1, sortable: true},
    {text: '参数值', dataIndex: 'value', flex: 2, sortable: true, editor: {allowBlank: true}} 
  ];
  
	var paraViewColumns = [
	                       {text: '序号', xtype: 'rownumberer', width: 40},
	                       {text: '资源组名称', dataIndex: 'resName',  sortable: true , width: 238},
	                       {text: '参数名称', dataIndex: 'name', sortable: true , width: 100},
	                       {text: '参数类型', dataIndex: 'paramType',  sortable: true, width: 80},
	                       {text: '资源组参数', dataIndex: 'originalValue', sortable: true, width: 390, 
	                           renderer: function(value,p,r) {
	                   		    var typeInDB =r.get('typeprimal') ;
	                   		    var ptyp =r.get('paramType') ;
	                   		    
	                       	    if(typeInDB =='String 加密'){
	                   	    		  var temp ='';
	                   	    		  if(value!=null && value!=''){
	                   	    			  for(var i=0;i <value.length;i++){
	                   	    				  temp+='●';
	                   	    			  }
	                   	    		  }
	                   	    		  return temp;
	                       	    }
	                       	    
	                       	    if(ptyp !='String 加密' ){
	                       	    	return value;
	                       	    }else{
	                   	    		  var temp ='';
	                   	    		  if(value!=null && value!=''){
	                   	    			  for(var i=0;i <value.length;i++){
	                   	    				  temp+='●';
	                   	    			  }
	                   	    		  }
	                   	    		  return temp;
	                       	    }
	                          }//,
//	                          getEditor: function(record) {
//	                            if(record.get('paramType')=="String"){
//	                              return Ext.create('Ext.grid.CellEditor', {
//	                                field: Ext.create( 'Ext.form.field.Text', {
//	                                  selectOnFocus : true
//	                                })
//	                              });
//	                            }else{
//	                              return Ext.create('Ext.grid.CellEditor', {
//	                                field: Ext.create( 'Ext.form.field.Text', {
//	                                  selectOnFocus : true,
//	                                  inputType:'password'
//	                                })
//	                              });
//	                            }
//	                          }
	                          },
	                       {text: '服务器参数', dataIndex: 'value',  sortable: true,  renderer: function(value,p,r) {
	                   		    var typeInDB =r.get('typeprimal') ;
	                   		    var ptyp =r.get('paramType') ;
	                   		    
	                       	    if(typeInDB =='String 加密'){
	                   	    		  var temp ='';
	                   	    		  if(value!=null && value!=''){
	                   	    			  for(var i=0;i <value.length;i++){
	                   	    				  temp+='●';
	                   	    			  }
	                   	    		  }
	                   	    		  return temp;
	                       	    }
	                       	    
	                       	    if(ptyp !='String 加密' ){
	                       	    	return value;
	                       	    }else{
	                   	    		  var temp ='';
	                   	    		  if(value!=null && value!=''){
	                   	    			  for(var i=0;i <value.length;i++){
	                   	    				  temp+='●';
	                   	    			  }
	                   	    		  }
	                   	    		  return temp;
	                       	    }
	                          },
	                          getEditor: function(record) {
	                            if(record.get('paramType')!="String 加密"){
	                              return Ext.create('Ext.grid.CellEditor', {
	                                field: Ext.create( 'Ext.form.field.Text', {
	                                  selectOnFocus : true
	                                })
	                              });
	                            }else{
	                              return Ext.create('Ext.grid.CellEditor', {
	                                field: Ext.create( 'Ext.form.field.Text', {
	                                  selectOnFocus : true,
	                                  inputType:'password'
	                                })
	                              });
	                            }
	                          }, width: 422} 
	                     ];
  
  //右侧的grid store
  var store = Ext.create('Ext.data.Store', {
	autoLoad: false,
	autoDestroy: true,
    pageSize: 50,
    model: 'serverModel',
    proxy: {
      type: 'ajax',
      url: 'agentGroup/getServers.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  var allServerStore= Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    pageSize: 50,
	    model: 'serverModel',
	    proxy: {
	      type: 'ajax',
	      url: 'resourceServer/getAllIP.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
  
  allServerStore.on('beforeload', function(store, options) {
      var new_params =
      {
    		  ibusSysId : 0
      };
      Ext.apply (store.proxy.extraParams, new_params);  
  });

  store.on ('beforeload', function (store, options)
	{
		  var new_params = {  
				 hostName:host_name1.getValue(),
				 startIp:ipStart1.getValue().trim(),
				 endIp:ipEnd1.getValue().trim()
         };
		Ext.apply (store.proxy.extraParams, new_params);
	});
	  
  store.addListener('load',function(me, records, successful, eOpts){
	    var chosedRecords=[];//存放选中记录
	       $.each(records,
            function(index, record) {
                if (toBindServerIdstr.indexOf(record.get('id')) > -1) {
                    chosedRecords.push(record);
                }
            });
            unChosedServList.getSelectionModel().select(chosedRecords, false, false); //选中记录

  });
  
  
  var choosedStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    autoDestroy: true,
	    pageSize: 500,
	    model: 'serverModel',
	    proxy: {
	      type: 'ajax',
	      url: 'agentGroup/getServers.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
  
   choosedStore.on ('beforeload', function (store, options)
	{
		 var new_params = {  
				 hostName:host_name2.getValue(),
				 startIp:ipStart2.getValue().trim(),
				 endIp:ipEnd2.getValue().trim()
         };
		Ext.apply (choosedStore.proxy.extraParams, new_params);
	});
	  
	  
  choosedStore.addListener('load',function(me, records, successful, eOpts){
	    var chosedRecords=[];//存放选中记录
	       $.each(records,
            function(index, record) {
                if (unBindServerIdstr.indexOf(record.get('id')) > -1) {
                    chosedRecords.push(record);
                }
            });
            ChosedServList.getSelectionModel().select(chosedRecords, false, false); //选中记录
	  });
   
	  
  var serverParaStore = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    model: 'ServerParameterModel',
    proxy: {
      type: 'ajax',
      url: 'resourceServer/serverPara.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  var paraWaitForChooseStore = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    model: 'ServerParameterModel',
    proxy: {
      type: 'ajax',
      url: 'resourceServer/unchoosedParams.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  var resourceGroupWaitForChooseStore = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    model: 'resourceGroupModel',
    proxy: {
      type: 'ajax',
      url: 'resourceGroup/groups.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  var paraViewStore = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    model: 'paraViewModel',
    proxy: {
      type: 'ajax',
      url: 'resourceServer/viewParamsByRes.do',
      reader: {
        type: 'json',
        root: 'dataList',
        idProperty:'id'+'vid' 
      }
    }
  });
  
  var resourceGroupStore = Ext.create('Ext.data.Store', {
    autoLoad: true,
    autoDestroy: true,
    model: 'resourceGroupModel',
    proxy: {
      type: 'ajax',
      url: 'resourceGroup/groups.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });

  
  var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
    clicksToEdit : 2
  });
 
  var paraCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
    clicksToEdit : 1
  });
  
  var selModel = Ext.create('Ext.selection.CheckboxModel', {
    checkOnly: true,
    listeners: {
      selectionchange: function(sm, selections) {
      }
    }
  });
 
   
	
  var serverCombo = Ext.create('Ext.form.field.ComboBox', {
		    fieldLabel: '选择IP',
		    renderTo: 'agent-group-list',
		    hidden: true ,
		    displayField: 'ip',
		    valueField: 'ip',
		    width: 200,
		    labelWidth: 40,
		    //padding: '5 0 10 0',
		    editable : true,
		    typeAhead:true,
		    store: allServerStore,
		    queryMode: 'local',
		    listeners: {
		      select: function() {
		    	curServer = this.getValue();
		        store.getProxy().setExtraParam("groupId", currentGroupId);
		        store.getProxy().setExtraParam("isChoosed", false);
		        store.getProxy().setExtraParam("curServer", curServer);
		        store.load();
		        
		      }, beforequery : function(e){
		            var combo = e.combo;
		              if(!e.forceAll){
		              	var value = e.query;
		              	combo.store.filterBy(function(record,id){
		              		var text = record.get(combo.displayField);
		              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
		              	});
		              combo.expand();
		              return false;
		              }
		         }
		    }
  }); 
  var selModel1=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
	
  var rightColumn = [{
      text: '编号',
      width: 40,
      sortable: true,
      hidden: true,
      dataIndex: 'id'
    }, {text: '序号', xtype: 'rownumberer', width: 40}, {
      xtype: 'checkcolumn',
      header: '选中',
      hidden: true,
      dataIndex: 'checked',
      width: 95,
      stopSelection: false,
      listeners: {
          checkchange: function (column, recordIndex, checked) {
        	  if(checked)
                  unChosedServList.down('#view').setDisabled(false);
        	  else{
        	      var serverStore = unChosedServList.getStore();
        		  var storeCnt = serverStore.getCount();
        	      var isChecked =null;
        	      var cnt =0;
                  for(var i=0;i<storeCnt;i++){
                	  isChecked= serverStore.getAt(i).get('checked');
                	  if(isChecked==true){
                		  cnt++;
                	  }
                  }
        	  }
        	  
/*              if(cnt==0){
            	  unChosedServList.down('#view').setDisabled(true);
              }*/
          }
      }
    },{
      text: '主机名称',
      dataIndex: 'hostName',
      flex: 1,
      sortable: true,
      editor: {
        allowBlank: false
      }
    }, {
      text: 'IP',
      dataIndex: 'ip',
      flex: 1,
      sortable: true,
      editor: {
        allowBlank: false
      }
    }, {
      text: '端口号',
      dataIndex: 'port',
      sortable: true,
      editor: {
        allowBlank: false
      }
    }];
    
   var ipStart1 = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 58,
	    fieldLabel : '起始IP',
	    emptyText : '--请输入开始IP--',
	    //labelSeparator : '',
	    width : '31%',//33.3%
	    labelAlign : 'right',
	    listeners: {
	       specialkey: function(field, e){
	           if (e.getKey() == e.ENTER) {
	        	   pageBar.moveFirst();
	           }
	       }
	   }
	    
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	
	/** 结束ip* */
	var ipEnd1 = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 58,
	    fieldLabel : '终止IP',
	    emptyText : '--请输入截止IP--',
	    //labelSeparator : '',
	    labelAlign : 'right',
	    width : '31%',
	    listeners: {
	       specialkey: function(field, e){
	           if (e.getKey() == e.ENTER) {
	        	   pageBar.moveFirst();
	           }
	       }
	   }
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	
    var host_name1 = new Ext.form.TextField({
		name : 'hostname1',
		fieldLabel : '主机名称',
		emptyText : '--请输入主机名称--',
		labelWidth: 65,
		labelAlign : 'right',
		width : '33%',
		listeners: {
	       specialkey: function(field, e){
	           if (e.getKey() == e.ENTER) {
	        	   pageBar.moveFirst();
	           }
	       }
	   }
	});
  function queryToBindMessage(){
	  pageBar.moveFirst(); 
  }
  
   function queryBindedMessage(){
	   pageBar1.moveFirst(); 
  }
  
  var rightQueryButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '查询',
		handler : queryToBindMessage
	});
   var rightResetButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '清空',
		handler : function(){
			host_name1.setValue();
			ipStart1.setValue();
			ipEnd1.setValue();
		}
	});
	
	var rightBindButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '绑定',
		handler : toBindServer 
	});
  
	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	store : store,
	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	dock : 'bottom',
	displayInfo : true,
	emptyMsg : '找不到任何记录'
	});
  //右边grid  Ext.grid.Panel  Ext.ux.ideal.grid.Panel
  var unChosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
  	iqueryFun : queryToBindMessage,
    region : 'center',
    //width : "50%",
//     bbar: pageBar,
    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    multiSelect: true,
    padding : panel_margin,
    split : true,
    selModel :selModel1,
     plugins : '',
//    xtype: 'cell-editing',
//    frame : true,
//    viewConfig: {
//      plugins: {
//        ptype: 'gridviewdragdrop',  
//        dragGroup: 'secondGridDDGroup',  
//        dropGroup: 'firstGridDDGroup'  
//      },  
//      listeners: {  
////        drop: function(node, data, dropRec, dropPosition) {  
////          var dropOn = dropRec ? ' ' + dropPosition + ' ' + dropRec.get('name') : ' on empty view';  
////        }  
//      }  
//    },
    columnLines : true,
    emptyText: '没有服务器信息',
    store : store,
    columns : rightColumn,
    dockedItems: [{
					xtype : 'toolbar',
//					baseCls:'customize_gray_back',  
					border : false,
					dock : 'top',
					items: [ ipStart1,ipEnd1,host_name1] //查询条件
				} ,
			    {
			      xtype: 'toolbar',
//			      baseCls:'customize_gray_back',  
			      id:'unch_toolbar',
			      items: [ rightQueryButton,rightResetButton,rightBindButton,
			        '-',{
			        itemId : 'add',
			        text : '添加',
			        textAlign : 'center',
			        cls : 'Blue_button',
			        height : 30,
			        iconCls:'sc_add',
				    margin:'5',
			        hidden: true ,
			//      disabled : true,
			        handler : function() {
			          // empty record
			          store.insert(0, new serverModel());
			          // rowEditing.startEdit(0, 0);
			          cellEditing.startEditByPosition({
			            row : 0,
			            column : 0
			          });
			        }
			      }, '-',{
			        itemId : 'delete',
			        text : '删除',
			        textAlign : 'center',
			        cls : 'Blue_button',
			        height : 30,
			        iconCls:'sc_delete',
				    margin:'5',
			        hidden: true ,
			        disabled : false,
			        handler : onDeleteListener
			      }, /*'-',*/{
			        itemId : 'view',
			        text : '服务器参数总览',
			        hidden: true ,
			        disabled : true,
			        iconCls:'acquiretask',
			        handler : onParamViewListener
			      }, '-',{
			        itemId : 'import',
			        text : '<u>导入</u>',
			        hidden: true ,
			//      disabled : true,
			        iconCls:'import',
			        handler : onImportListener
			      }, '-', {
			        itemId : 'export',
			        hidden: true ,
			        text : '<u>导出</u>',
			//      disabled : true,
			        iconCls:'server_export',
			        handler : onExportListener
			      },/*'-',*/ {
			          itemId : 'copyHost',
			          text : '服务器复制',
			          hidden: true ,
			          disabled : true,
			          iconCls:'server_copy',
			          handler : onHostCopyListener
			        },'-',serverCombo, {
			            itemId : 'recover',
			            hidden:true,
			            text : '<u>重置</u>',
			//          disabled : true,
			            handler : recover
			          },{
			         xtype: 'tbfill' }, {
			        	  hidden:true,
			        xtype: 'tbtext',
			        text: '<span data-qtip="选择资源组后，将要绑定到该资源组的服务器勾选上，点击保存即可">操作提示</span>',
			        height: 22, 
			        padding:'4 0 0 0'
			      }]
			    }],
    listeners: {
//      itemclick: function(dv, record, item, index, e) {
//        var groupId = resourceGroupCombo.getValue();
////        if(groupId){
//          store.each(function(rec) {
//            rec.set('itemClicked',false);
//          });
//          record.set('itemClicked',true);
//          currentServerId = record.get('id');
//          unChosedServList.down('#delete').setDisabled(false);
//          unChosedServList.down('#view').setDisabled(false);
////          serverPara.down('#add').setDisabled(false);
////          serverParaStore.getProxy().setExtraParam('groupId', groupId);
////          serverParaStore.getProxy().setExtraParam('serverId', record.get('id'));
////          serverParaStore.load();
////        }else{
////          Ext.Msg.alert('提示', '请选择资源组，然后可以查看服务器的个性化参数配置!');
////        }
//      }
    	 select: function( e, record, index, eOpts ){ 
     		 if(toBindServerIdstr.indexOf(record.get('id'))==-1) {
	            		toBindServerIdstr.push(record.get('id'));
	         }
     	 },
     	 deselect: function( e, record, index, eOpts ){
        	if(toBindServerIdstr.indexOf(record.get('id'))>-1) {
        		toBindServerIdstr.remove(record.get('id'));
        	}
     	 }
    }
  });
  var leftColumns = [{
	      text: '编号',
	      width: 40,
	      sortable: true,
	      hidden: true,
	      dataIndex: 'id'
	    }, {text: '序号', xtype: 'rownumberer', width: 40}, {
	      xtype: 'checkcolumn',
	      header: '选中',
	      hidden: true,
	      dataIndex: 'checked',
	      width: 95,
	      stopSelection: false,
	      listeners: {
	          checkchange: function (column, recordIndex, checked) {
	        	  if(checked)
	                  unChosedServList.down('#view').setDisabled(false);
	        	  else{
	        	      var serverStore = unChosedServList.getStore();
	        		  var storeCnt = serverStore.getCount();
	        	      var isChecked =null;
	        	      var cnt =0;
	                  for(var i=0;i<storeCnt;i++){
	                	  isChecked= serverStore.getAt(i).get('checked');
	                	  if(isChecked==true){
	                		  cnt++;
	                	  }
	                  }
	        	  }
	        	  
	/*              if(cnt==0){
	            	  unChosedServList.down('#view').setDisabled(true);
	              }*/
	          }
	      }
	    },{
	      text: '主机名称',
	      dataIndex: 'hostName',
	      flex: 1,
	      sortable: true,
	      //editor: {
	       //   allowBlank: false
	       // }
	    }, {
	      text: 'IP',
	      dataIndex: 'ip',
	      flex: 1,
	      sortable: true,
	      //editor: {
	       //   allowBlank: false
	       // }
	    }, {
	      text: '执行用户',
	      dataIndex: 'iexecUserName',
	      sortable: true,
	      editor : {
	    	  allowBlank: false,
			  maxLength : 255,
			  listeners:{
				  blur: function(){
					  var m = choosedStore.getModifiedRecords();
					  for ( var i = 0, len = m.length; i < len; i++) {
					    var value=m[i].get("iexecUserName").value;
					  }
					  Ext.Ajax.request({
						  	url : 'agentGroup/insertSave.do',
							method : 'POST',
							params : {
								iexecUserName : this.value,
								groupId : bsid_serverConfig,
								iid : ChosedServList.getSelectionModel().getSelection()[0].get("id")
							},
							success : function(response, request) {
								choosedStore.reload();
								var success = Ext.decode(response.responseText).success;
								if (success) {
									Ext.Msg.alert('提示', '保存成功');
								} else {
									Ext.Msg.alert('提示', '保存失败！');
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示', '保存失败！');
							}
						});
				  }
			  }
			},
	    },{
	      text: '端口号',
	      dataIndex: 'port',
	      sortable: true,
	      //editor: {
	        //  allowBlank: false
        //}
	    },{
	      text : '优先级',
	      dataIndex : 'priority',
	      hidden:true,
	      sortable : true,
	      editor: new Ext.form.field.ComboBox({
	          typeAhead: true,
	          triggerAction: 'all',
	          editable : false,
	          store: [[1,1],[2,2],[3,3],[4,4],[5,5],[6,6],[7,7],[8,8],[9,9],[10,10]]
	        })
	    }, {
	      text: '应用标识',
	      dataIndex: 'systemType',
	      hidden: true,
	      flex: 1,
	      sortable: true,
	      //editor: {
	      //    allowBlank: false
	      //  }
	    }];
  
  function getSave(dataIndex){
	  var record = ChosedServList.getSelectionModel().getSelection();
	  return record.get(dataIndex);
  }
  
  var ipStart2 = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 58,
	     fieldLabel : '起始IP',
	    emptyText : '--请输入开始IP--',
	    //labelSeparator : '',
	    width : '31%',
	    labelAlign : 'right',
    	listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar1.moveFirst();
                }
            }
        }
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	
	/** 结束ip* */
	var ipEnd2 = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 58,
	    fieldLabel : '终止IP',
	    emptyText : '--请输入截止IP--',
	    //labelSeparator : '',
	    labelAlign : 'right',
	    width : '31%',
	    listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar1.moveFirst();
                }
            }
        }
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	
    var host_name2 = new Ext.form.TextField({
		name : 'hostname2',
		fieldLabel : '主机名称',
		emptyText : '--请输入主机名称--',
		labelWidth: 65,
		labelAlign : 'right',
		width : '33%',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar1.moveFirst();
                }
            }
        }
	});
	var leftQueryButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '查询',
		handler : queryBindedMessage
	});
	var leftResetButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '清空',
		handler : function(){
			host_name2.setValue();
			ipStart2.setValue();
			ipEnd2.setValue();
		}
	});
	
	var leftUnbindButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '解绑',
		handler : toUnBindServer
	});
	
	var batchImportButton   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '批量导入',
		handler : function() {
			importExcel();
		}
	});
	
	function importExcel() {
		var uploadWindows;
		var uploadForm
		uploadForm = Ext.create('Ext.form.FormPanel', {
					border : false,
					items : [{
								xtype : 'filefield',
								name : 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
								fieldLabel : '选择文件',
								labelWidth : 80,
								anchor : '90%',
								// labelAlign: 'right',
								margin : '10 10 0 40',
								buttonText : '浏览'
							}],
					buttonAlign : 'center',
					buttons : [{
								text : '确定',
								handler : upExeclData
							}, {
								text : '取消',
								handler : function() {
									uploadWindows.close();
								}
							},
							{
								text: '下载模板',
								handler: function() {
									window.location.href = 'downloadSsTemplate.do?fileName=2';
								}
							}]
				});
		uploadWindows = Ext.create('Ext.window.Window', {
					title : '导入Agent文件',
					layout : 'fit',
					height : 140,
					width : 600,
					modal : true,
					// autoScroll : true,
					items : [uploadForm],
					listeners : {
						close : function(g, opt) {
							uploadForm.destroy();
						}
					}
				});
		uploadWindows.show();
		function upExeclData() {
			var form = uploadForm.getForm();
			var hdupfile = form.findField("fileName").getValue();
			if (hdupfile == '') {
				Ext.Msg.alert('提示', "请选择文件...");
				return;
			}
			uploadTemplate(form);
		}
		function uploadTemplate(form) {
			if (form.isValid()) {
				form.submit({
					url : 'agentGroup/importAgentExcel.do',
					params : {
						bsManagerId : bsid_serverConfig,
						importType : 1
					},
					success : function(form, action) {
						var sumsg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示', sumsg);
						pageBar.moveFirst(); 
				        pageBar1.moveFirst(); 
						uploadWindows.close();
						myWhiteListGrid.ipage.moveFirst();
						return;
					},
					failure : function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
//						var mess = Ext.create('Ext.window.MessageBox', {
//									minHeight : 110,
//									minWidth : 500,
//									resizable : false
//								});
						Ext.Msg.alert('提示', msg);
						return;
					}
				});
			}
		}
	}
	
	var getImportExcelModel   = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		hidden : true,
		text : '获取导入模板',
		handler : function() {
			getImportExcelModelFunction();
		}
	});
	
	function getImportExcelModelFunction()
    {
//    	var record = groupList.getSelectionModel ().getSelection ();
//    	var iidStr = "";
//		Ext.Array.each (record, function (recordObj)
//		{
//			iidStr += "," + recordObj.get ('id');
//		});
//    	if(iidStr.length<=0)
//    	{
//    		Ext.Msg.alert ('提示','请选择要操作的行！');
//    		return;
//    	}
//    	iidStr = iidStr.substr(1);
    	window.location.href = 'agentGroup/getImportExcelModel.do';
    }
  
   var selModel2=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
   var pageBar1 = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : choosedStore,
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		dock : 'bottom',
		displayInfo : true,
		emptyMsg : '找不到任何记录'
		});
  var ChosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
	    region : 'west',
 	   // width : "50%",
	    multiSelect: true,
	    padding : panel_margin,
	     selModel :selModel2,
	    iqueryFun : queryBindedMessage,
	    split : true, 
//	    bbar:pageBar1,
	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//	    viewConfig: {  
//	        plugins: {
//	          ptype: 'gridviewdragdrop',  
//	          dragGroup: 'firstGridDDGroup',  
//	          dropGroup: 'secondGridDDGroup'  
//	        },  
//	        listeners: {  
//	          drop: function(node, data, dropRec, dropPosition) {  
//	            var dropOn = dropRec ? ' ' + dropPosition + ' ' + dropRec.get('name') : ' on empty view';  
//	          }  
//	        }  
//	      },
	    columnLines : true,
	    emptyText: '没有服务器信息',
	    store : choosedStore,
	    clicksToEdit:1,
	    columns : leftColumns,
//	    viewConfig: {
//	      getRowClass: function(record, rowIndex, rowParams, store){
//	        if (record.get("itemClicked")) {        
//	          return 'x-grid-record-seablue';
//	        }
//	      }
//	    },
	    dockedItems: [{
					xtype : 'toolbar',
//					baseCls:'customize_gray_back',  
					border : false,
					dock : 'top',
					items: [ ipStart2,ipEnd2,host_name2] //查询条件
				},  {
					xtype : 'toolbar',
//					baseCls:'customize_gray_back',  
					border : false,
					dock : 'top',
					items: [  leftQueryButton,leftResetButton,leftUnbindButton,batchImportButton,getImportExcelModel]  
				}],
	    listeners: {
//	      itemclick: function(dv, record, item, index, e) {
//	        var groupId = resourceGroupCombo.getValue();
////	        if(groupId){
//	          store.each(function(rec) {
//	            rec.set('itemClicked',false);
//	          });
//	          record.set('itemClicked',true);
//	          currentServerId = record.get('id');
//	          unChosedServList.down('#delete').setDisabled(false);
//	          unChosedServList.down('#view').setDisabled(false);
////	          serverPara.down('#add').setDisabled(false);
////	          serverParaStore.getProxy().setExtraParam('groupId', groupId);
////	          serverParaStore.getProxy().setExtraParam('serverId', record.get('id'));
////	          serverParaStore.load();
////	        }else{
////	          Ext.Msg.alert('提示', '请选择资源组，然后可以查看服务器的个性化参数配置!');
////	        }
//	      }
		     select: function( e, record, index, eOpts ){ 
	     		 if(unBindServerIdstr.indexOf(record.get('id'))==-1) {
		            		unBindServerIdstr.push(record.get('id'));
		         }
	     	 },
	     	 deselect: function( e, record, index, eOpts ){
	        	if(unBindServerIdstr.indexOf(record.get('id'))>-1) {
	        		unBindServerIdstr.remove(record.get('id'));
	        	}
	     	 }
	    }
	  });
  
  var resourceGroupCombo = Ext.create('Ext.form.field.ComboBox', {
    fieldLabel: '选择资源组',
    hidden:true,
    renderTo: 'agent-group-list',
    displayField: 'name',
    valueField: 'id',
    width: 500,
    labelWidth: 70,
    padding: '5 0 10 0',
    editable : true,
    typeAhead:true,
    store: resourceGroupStore,
    queryMode: 'local',
    listeners: {
      select: function() {
  		curServer=null;
		serverCombo.setValue('');
    	  
        currentGroupId = this.getValue();
        
        store.getProxy().setExtraParam("groupId", this.getValue());
        store.getProxy().setExtraParam("isChoosed", false);
        store.getProxy().setExtraParam("curServer", curServer); 
        store.load();
        
        choosedStore.getProxy().setExtraParam("groupId", this.getValue());
        choosedStore.getProxy().setExtraParam("isChoosed", true);
        choosedStore.load();
        
        serverParaStore.removeAll();
		unChosedServList.down('#view').setDisabled(false); 
		unChosedServList.down('#copyHost').setDisabled(false);
        serverPara.down('#add').setDisabled(true);
        store.sort('checked','DESC'); 
      }, beforequery : function(e){
            var combo = e.combo;
              if(!e.forceAll){
              	var value = e.query;
              	combo.store.filterBy(function(record,id){
              		var text = record.get(combo.displayField);
              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
              	});
              combo.expand();
              return false;
              }
         }
    }
  }); 
//  resourceGroupCombo.setValue(bsid_serverConfig);
  

  
  var serverPara = Ext.create('Ext.ux.ideal.grid.Panel', {
    region: 'center',
    title: '服务器参数',
    split: true,
    multiSelect: true,
    plugins: [ paraCellEditing ],
    emptyText: '没有服务器个性化参数',
    width: "50%",
//    frame: true,
    columnLines : true,
    store: serverParaStore,
    columns: paraColumns,
    dockedItems: [{
      xtype: 'toolbar',
      baseCls:'customize_gray_back',  
      items: [{
        itemId: 'add',
      	text: '<u>增加</u>',
      	iconCls:'add',
       	disabled: true,
       	handler: onAddServerParaListener
      },{
        itemId: 'save',
      	text: '<u>保存</u>',
       	handler: onSaveServerParaListener
      },{
        itemId: 'delete',
      	text: '<u>删除</u>',
      	iconCls:'delete',
       	disabled: true,
       	handler: onDeleteServerParaListener
      }]
    }]
  });
  serverPara.getSelectionModel().on('selectionchange', function(selModel, selections) {
    serverPara.down('#delete').setDisabled(selections.length === 0);
  });
  
  var resourceGroupWaitForChooseGird = Ext.create('Ext.ux.ideal.grid.Panel', {
    title: '资源组列表',
    width: 200,
    split: true,
    floatable: false,
    store: resourceGroupWaitForChooseStore,
    columns: resourceGroupColumns,
    region: 'west',
    emptyText: '没有数据',
    loadMask: true
  });
  resourceGroupWaitForChooseGird.getSelectionModel().on('selectionchange', function(selModel, selections) {
    var resourceGroupId;
    Ext.Array.each(selections, function(record) {
      resourceGroupId = record.get('id');
    });
    paraWaitForChooseStore.getProxy().setExtraParam("groupId", resourceGroupId);
    paraWaitForChooseStore.load();
  });
  
  var paraWaitForChooseGird = Ext.create('Ext.ux.ideal.grid.Panel', {
    multiSelect: true,
    title : '待选择个性化参数列表',
    columnLines : true,
    store : paraWaitForChooseStore,
    columns : paraColumns,
    region : 'center',
    emptyText: '没有数据',
    loadMask: true,
    border: false,
    dockedItems : [{
      xtype : 'toolbar',
      baseCls:'customize_gray_back',  
      itemId : 'confirm',
      disabled: true,
      items : [{
        text : '确认',
        iconCls:'confirm',
        handler : onConfirmForChooseListener
      }]
    }]
  });
  paraWaitForChooseGird.getSelectionModel().on('selectionchange', function(selModel, selections) {
    paraWaitForChooseGird.down('#confirm').setDisabled(selections.length === 0);
  });
  
  var viewParaCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
    clicksToEdit : 1
  });
//  var paraViewGird = Ext.create('Ext.grid.Panel', {
////    title : '个性化参数总览',
//    columnLines : true,
//    store : paraViewStore,
//    plugins: [ viewParaCellEditing ],
//    columns : paraViewColumns,
//    region : 'center',
//    emptyText: '没有数据',
//    loadMask: true,
//    border: false,
//    dockedItems: [{
//      xtype: 'toolbar',
//      items: [{
//        itemId : 'save',
//        text : '保存',
//        icon: 'images/save.gif',
//        handler : onViewParaSaveListener
//      }]
//    }]
//  });
  
  	var panelleft = Ext.create('Ext.panel.Panel',{
    	layout : 'fit',
        title : '绑定服务器',
    	bodyCls : 'x-docked-noborder-top',
    	cls:'window_border panel_space_left panel_space_right',
    	region : 'west',
    	border : false,
    	 split : true,
    	width : '50%',
		items : [ChosedServList]
    });
  
  	var panelrigth = Ext.create('Ext.panel.Panel',{
    	layout : 'fit',
        title : '待选择的服务器',
    	bodyCls : 'x-docked-noborder-top',
    	cls:'window_border panel_space_right',
    	region : 'center',
    	border : false,
    	 split : true,
    	width : '50%',
		items : [unChosedServList]
    });
  var server_config_panel = Ext.create('Ext.panel.Panel', {
    height : contentPanel.getHeight()-40,
    layout : 'border',
    bodyPadding : grid_margin,
    border : true,
    items : [ panelleft ,panelrigth ],
    renderTo : "agent-group-servers"
  });
  
/*  resizePanel(function(){
    //server_config_panel.setWidth(contentPanel.getWidth()-15);
    //server_config_panel.setHeight(contentPanel.getHeight()-100);
  });*/
  
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };
  
  /**
   * @description 绑定agent
   */
  function toBindServer(){
  	 if(toBindServerIdstr.length ==0){
	   	   Ext.Msg.alert('提示','请选择要绑定的Agent!');
	   	   return;
   	  }
	  Ext.Ajax.request({
	      url : 'agentGroup/toBindServer.do',
	      params : {
	    	serverIds :toBindServerIdstr,
	        groupId: currentGroupId
	      },
	      method : 'POST',
	      success : function(response, opts) {
	        var success = Ext.decode(response.responseText).success;
	        // 当后台数据同步成功时
	        if (success) {
	          toBindServerIdstr = [];
	          unBindServerIdstr = [];
	          pageBar.moveFirst(); 
	          pageBar1.moveFirst(); 
	          Ext.Msg.alert('提示', '绑定成功');
	        } else {
	          Ext.Msg.alert('提示', '绑定失败！');
	        }
	      },
	      failure : function(result, request) {
	          Ext.Msg.alert('提示',   '绑定失败');
	      }
	  });
  }
  
   function toUnBindServer(){
   	  if(unBindServerIdstr.length ==0){
	   	   Ext.Msg.alert('提示','请选择要解绑的Agent!');
	   	   return;
   	  }
	  Ext.Ajax.request({
	      url : 'agentGroup/toUnBindServer.do',
	      params : {
	    	serverIds :unBindServerIdstr,
	        groupId: currentGroupId
	      },
	      method : 'POST',
	      success : function(response, opts) {
	        var success = Ext.decode(response.responseText).success;
	        // 当后台数据同步成功时
	        if (success) {
	          toBindServerIdstr = [];
	          unBindServerIdstr = [];
	          pageBar.moveFirst(); 
	          pageBar1.moveFirst(); 
	          Ext.Msg.alert('提示', '解绑成功！');
	          
	        } else {
	          Ext.Msg.alert('提示', '解绑失败！');
	        }
	      },
	      failure : function(result, request) {
	          Ext.Msg.alert('提示',   '解绑失败');
	      }
	  });
  }
  
 
  
  function recover(){
		curServer=null;
		serverCombo.setValue('');
        store.getProxy().setExtraParam("groupId",currentGroupId);
        store.getProxy().setExtraParam("isChoosed", false);
        store.getProxy().setExtraParam("curServer", curServer);
        store.load(); 
  }
  function onHostCopyListener(){	    
		unChosedServList.down('#copyHost').setDisabled(true);//防止表单多次提交
	    var choosedids = [];
	    store.each(function(record) {
	      if(record.get('checked') && record.get('id')){
	        choosedids.push(record);
	      }
	    });
	    
	    var m = choosedids;
	    if (m.length < 1) {
	    	Ext.Msg.alert('提示', '该资源组下没有服务器，无法进行复制');	
	      return;
	    }
	    var message = "";

	    var jsonData = "[";
	    for ( var i = 0, len = m.length; i < len; i++) {
	      var n = 0;
	      var hostName=m[i].get("hostName").trim();
	      var ip=m[i].get("ip").trim();
	      var port = m[i].get("port");
	      var systemType = m[i].get("systemType") || '共用节点';
	      if(""==hostName||null==hostName){
	        setMessage('服务器名不能为空！');
	        return;
	      }
	      if(fucCheckLength(hostName)>250){
	        setMessage('服务器名不能超过250字符！');
	        return;
	      }
	      if(""==ip||null==ip){
	        setMessage('IP地址不能为空！');
	        return;
	      }

//	      if (!isYesIp(ip)) {
//	        setMessage('IP地址不合法！');
//	        return;
//	      }
	      if(!isNumber(port)) {
	        setMessage('端口号必须为整数！');
	        return;
	      }
	      if(port>99999 || port<1){
	        setMessage('端口号范围必须在1-99999之间！');
	        return;
	      }
//	      if(systemType!='' && systemType.trim()==''){
//	        setMessage('应用标识不可以全部是空白符！');
//	        return;
//	      }
	      if(fucCheckLength(systemType)>250){
	        setMessage('应用标识不能超过250字符！');
	        return;
	      }
	      
	      for(var k=0;k<store.getCount();k++){
	        var record = store.getAt(k);
	        var cip = record.data.ip;
	        var csystype = record.data.systemType || '共用节点';
	        if (cip == ip && csystype == systemType){
	          n = n+1;
	        }
	      }
	      
	      var ss = Ext.JSON.encode(m[i].data);
	      if (i == 0)
	        jsonData = jsonData + ss;
	      else
	        jsonData = jsonData + "," + ss;
	    }
	    jsonData = jsonData + "]";
	    Ext.Ajax.request({
	      url : 'resourceServer/copyHost.do',
	      method : 'POST',
	      params : {
	        jsonData : jsonData,
	        groupId: currentGroupId
	      },
	      success : function(response, request) {
	        var success = Ext.decode(response.responseText).success;
			unChosedServList.down('#copyHost').setDisabled(false);
	        if (success) {
                store.reload();
                choosedStore.reload();
                Ext.Msg.alert('提示', '操作成功执行');
	        } else {
	          Ext.Msg.alert('提示', message + '保存失败');
	        }
	      },
	      failure : function(result, request) {
	        Ext.Msg.alert('提示', message + '保存失败');
	      }
	    });
	  }
  
  
  
  
  
  function onDeleteListener(){
    var serverStore = unChosedServList.getStore();
    var storeCnt = serverStore.getCount();
    if (storeCnt == 0) {
      Ext.Msg.alert('提示', '没有可删除的数据');
      return;
    } else {
      Ext.Msg.confirm("请确认", "是否真的要删除数据？", function(button, text) {
        if (button == "yes") {
          var serverids = [];
          var isChecked =null;
          var serverId  =null;
          var cnt =0;
/*          for(var i=0;i<storeCnt;i++){
        	  isChecked= serverStore.getAt(i).get('checked');
        	  if(isChecked==true){
        		  serverId =serverStore.getAt(i).get('id');
        		  serverids.push(serverId);
        		  cnt++;
        	  }
          }*/
          var data = unChosedServList.getSelectionModel().getSelection();
          for(var i=0;i<data.length;i++){
    		  serverId =data[i].get('id');
    		  serverids.push(serverId);
    		  cnt++;
          }
          
          var chosedGridData = ChosedServList.getSelectionModel().getSelection();
          var chosedCnt =chosedGridData.length;
          if(chosedCnt >0){
        	  Ext.Msg.confirm('提示','绑定服务器面板中有'+chosedCnt+'条记录选中，是否要删除掉',function(btn){
        		  if(btn=='yes'){
        	          for(var i=0;i<chosedGridData.length;i++){
        	    		  serverId =chosedGridData[i].get('id');
        	    		  serverids.push(serverId);
        	    		  cnt++;
        	          }

        	          Ext.Ajax.request({
        	            url : 'resourceServer/serversDelete.do',
        	            params : {
        	              deleteIds : serverids.join(',')
        	            },
        	            method : 'POST',
        	            success : function(response, opts) {
        	              var success = Ext.decode(response.responseText).success;
        	              // 当后台数据同步成功时
        	              if (success) {
        	                Ext.Msg.alert('提示', '数据删除成功');
        	                serverStore.reload();
        	                choosedStore.reload();
        	              } else {
        	                Ext.Msg.alert('提示', '数据删除失败');
        	              }
        	            }
        	          });
        		  }
        		  
        		  
        		  if(btn=='no'){
        	          if(cnt ==0){
        	              Ext.Msg.alert('提示', '请选择要删除的数据后，再进行删除');
        	              return;
        	          }

        	          Ext.Ajax.request({
        	            url : 'resourceServer/serversDelete.do',
        	            params : {
        	              deleteIds : serverids.join(',')
        	            },
        	            method : 'POST',
        	            success : function(response, opts) {
        	              var success = Ext.decode(response.responseText).success;
        	              // 当后台数据同步成功时
        	              if (success) {
        	                Ext.Msg.alert('提示', '数据删除成功');
        	                serverStore.reload();
        	              } else {
        	                Ext.Msg.alert('提示', '数据删除失败');
        	              }
        	            }
        	          });
        		  }	  
        		  
        	  },this);
          }else{
        	  //只有“待选择的服务器”面板中包含选中的记录
	          if(cnt ==0){
	              Ext.Msg.alert('提示', '请选择要删除的数据后，再进行删除');
	              return;
	          }

	          Ext.Ajax.request({
	            url : 'resourceServer/serversDelete.do',
	            params : {
	              deleteIds : serverids.join(',')
	            },
	            method : 'POST',
	            success : function(response, opts) {
	              var success = Ext.decode(response.responseText).success;
	              // 当后台数据同步成功时
	              if (success) {
	                Ext.Msg.alert('提示', '数据删除成功');
	                serverStore.reload();
	              } else {
	                Ext.Msg.alert('提示', '数据删除失败');
	              }
	            }
	          });
		  
        	  
          }
          

        }
      });
    }
  }
  
  function onExportListener(){
	    var serverStore = unChosedServList.getStore();
	    var storeCnt = serverStore.getCount();
	    if (storeCnt == 0) {
	      Ext.Msg.alert('提示', '没有可导出的数据');
	      return;
	    } else {
	      Ext.Msg.confirm("请确认", "是否真的要到导出数据？", function(button, text) {
	        if (button == "yes") {
	          var serverids = [];
	          var isChecked =null;
	          var serverId  =null;
	          var cnt =0;
	          
	          
	          var data = unChosedServList.getSelectionModel().getSelection();
	          for(var i=0;i<data.length;i++){
	    		  serverId =data[i].get('id');
	    		  serverids.push(serverId);
	    		  cnt++;
	          }
	          
	          var chosedGridData = ChosedServList.getSelectionModel().getSelection();
	          var chosedCnt =chosedGridData.length;
	          if(chosedCnt >0){
	        	  Ext.Msg.confirm('提示','绑定服务器面板中有'+chosedCnt+'条记录选中，是否要导出',function(btn){
	        		  if(btn=='yes'){
	        	          for(var i=0;i<chosedGridData.length;i++){
	        	    		  serverId =chosedGridData[i].get('id');
	        	    		  serverids.push(serverId);
	        	    		  cnt++;
	        	          }
	        	          window.location.href = 'resourceServer/exportServers.do?serverids=' + serverids.join(',');
	        		  }
	        		  
	        		  
	        		  if(btn=='no'){
	        	          if(cnt ==0){
	        	              Ext.Msg.alert('提示', '没有需要导出数据,请选择要导出的数据后，再进行导出');
	        	              return;
	        	          }
	        	          window.location.href = 'resourceServer/exportServers.do?serverids=' + serverids.join(',');
	        		  }	  
	        		  
	        	  },this);
	          }else{
	        	  //只有“待选择的服务器”面板中包含选中的记录
		          if(cnt ==0){
		              Ext.Msg.alert('提示', '请选择要导出的数据后，再进行导出');
		              return;
		          }
		          window.location.href = 'resourceServer/exportServers.do?serverids=' + serverids.join(',');
	          }
	          

	        }
	      });
	    }
	  
  }
  

  function onImportListener() {
    upLoadformPanel = Ext.create('Ext.form.Panel', {
      id : 'upLoadpanel',
      width: 400,
      floating: true,
      frame: true,
      closable : true,
      renderTo: Ext.getBody(),
      items : [{
        xtype : 'filefield',
        name : 'file', // 设置该文件上传空间的name，也就是请求参数的名字
        fieldLabel : '文件路径',
        labelWidth : 80,
        msgTarget : 'side',
        allowBlank : false,
        anchor : '100%',
        buttonText : '选择Excel', // 浏览按钮的文本
        emptyText : '还没有选择Excel文件'
      }],
      buttons : [{
        text : '上传',
        handler : function() {
          var form = this.up('form').getForm();
          if (form.isValid()) {
            upLoadformPanel.hide();
            form.submit({
              url : 'resourceServer/importServers.do',
              success : function(form, action) {
                store.reload();
                choosedStore.reload();
                Ext.Msg.alert('提示', "上传成功");
                upLoadformPanel.close();
              },
              failure : function(form, action) {
                var msg = Ext.decode(action.response.responseText).msg;
                store.reload();
                Ext.Msg.alert('提示', msg);
                upLoadformPanel.close();
              }
            });
          }
        }
      }]
    });
  }
  
  function onAddServerParaListener(){
    if(!(resourceGroupCombo.getValue() && currentServerId)){
      Ext.Msg.alert('提示', '请选择资源组以及服务器!');
    }
    if (!paraWaitForChooseWin) {
      paraWaitForChooseWin = Ext.create('widget.window', {
          title: '个性化配置',
          closeAction: 'hide',
          width: 700,
          minWidth: 350,
          height: 450,
          modal: true,
          layout: 'border',
          items: [/*resourceGroupWaitForChooseGird, */paraWaitForChooseGird ]
      });
    }
    
    if (paraWaitForChooseWin.isVisible()) {
      paraWaitForChooseWin.hide(this, function() {
      });
    } else {
      paraWaitForChooseWin.show(this, function() {
        paraWaitForChooseStore.getProxy().setExtraParam("groupId", currentGroupId);
        paraWaitForChooseStore.getProxy().setExtraParam("serverId", currentServerId);
        paraWaitForChooseStore.load();
      });
    }
  }
  
  function onParamViewListener(){
	    var data = unChosedServList.getSelectionModel().getSelection();
	    if (data.length > 1) {
	      Ext.Msg.alert('提示', '最多选择一行!');
	      return;
	    }
	    if(data.length > 0){
		    currentServerId =data[0].get('id');
	    }else{
	        Ext.Msg.alert('提示', '请选择一个服务器后，在进行服务器参数总览操作！');
	    	return;
	    }

	    

	    var paraViewGird = Ext.create('Ext.ux.ideal.grid.Panel', {
//  	    title : '个性化参数总览',
  	    columnLines : true,
  	    store : paraViewStore,
  	    plugins: [ viewParaCellEditing ],
  	    columns : paraViewColumns,
  	    region : 'center',
  	    emptyText: '没有数据',
  	    loadMask: true,
  	    border: false,
  	    dockedItems: [{
  	      xtype: 'toolbar',
  	      baseCls:'customize_gray_back',  
  	      items: [{
  	        itemId : 'save',
  	        text : '保存',
  	      cls : 'Common_Btn',
  	        handler : onViewParaSaveListener
  	      }]
  	    }]
  	  }); 
  	paraViewWin = Ext.create('widget.window', {
        title: '服务器参数总览',
        closeAction: 'hide',
        width: 1300,
        minWidth: 350,
        height: 450,
        modal: true,
        layout: 'border',
        items: [ paraViewGird ]
    });
    if (paraViewWin.isVisible()) {
      paraViewWin.hide(this, function() {
      });
    } else {
      paraViewWin.show(this, function() {
        paraViewStore.getProxy().setExtraParam("serverId", currentServerId);
        paraViewStore.getProxy().setExtraParam("lResId", currentGroupId);
        paraViewStore.load();
      });
    }
  }
  
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  
  function onSaveServerParaListener(){
    var m = serverParaStore.getModifiedRecords();
    if (m.length < 1) {
      return;
    }
    var jsonData = "[";
    for ( var i = 0, len = m.length; i < len; i++) {
      var value=m[i].get("value");
      if(value!='' && value.trim()==''){
        setMessage('参数值不可以全部是空白符！');
        return;
      }
      if(fucCheckLength(value)>250){
        setMessage('参数值不能超过250字符！');
        return;
      }
      var ss = Ext.JSON.encode(m[i].data);
      if (i == 0)
        jsonData = jsonData + ss;
      else
        jsonData = jsonData + "," + ss;
    }
    jsonData = jsonData + "]";
    Ext.Ajax.request({
      url : 'resourceServer/saveServerPara.do',
      method : 'POST',
      params : {
        jsonData : jsonData,
        serverId: currentServerId
      },
      success : function(response, request) {
        var success = Ext.decode(response.responseText).success;
        if (success) {
          serverParaStore.reload();
          Ext.Msg.alert('提示', '保存成功');
        } else {
          Ext.Msg.alert('提示', '数据保存失败！');
        }
      },
      failure : function(result, request) {
        Ext.Msg.alert('提示', '数据保存失败！');
      }
    });
  }
  
  function onDeleteServerParaListener(){
    var data = serverPara.getSelectionModel().getSelection();
    if (data.length == 0) {
      Ext.Msg.alert('提示', '请先选择您要操作的行!');
      return;
    } else {
      Ext.Msg.confirm("请确认", "是否真的要删除数据？", function(button, text) {
        if (button == "yes") {
          var paramids = [];
          Ext.Array.each(data, function(record) {
            var paramId = record.get('id');
            // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
            if (paramId) {
              paramids.push(paramId);
            }
          });

          Ext.Ajax.request({
            url : 'resourceServer/deleteServerPara.do',
            params : {
              deleteIds : paramids.join(','),
              serverId: currentServerId
            },
            method : 'POST',
            success : function(response, opts) {
              var success = Ext.decode(response.responseText).success;
              // 当后台数据同步成功时
              if (success) {
                Ext.Array.each(data, function(record) {
                  serverParaStore.remove(record);// 页面效果
                });
                Ext.Msg.alert('提示', '删除成功');
              } else {
                Ext.Msg.alert('提示', '数据删除失败！');
              }
            }
          });
        }
      });
    }
  }
  
  function onViewParaSaveListener(btn){
    var m = paraViewStore.getModifiedRecords();
    if (m.length < 1) {
      return;
    }
    var jsonData = "[";
    for ( var i = 0, len = m.length; i < len; i++) {
      var value=m[i].get("value");
      if(value!='' && value.trim()==''){
        setMessage('个性化参数值不可以全部是空白符！');
        return;
      }
      if(fucCheckLength(value)>250){
        setMessage('个性化参数值不能超过250字符！');
        return;
      }
      var ss = Ext.JSON.encode(m[i].data);
      if (i == 0)
        jsonData = jsonData + ss;
      else
        jsonData = jsonData + "," + ss;
    }
    jsonData = jsonData + "]";
    btn.disabled=true;
    Ext.Ajax.request({
      url : 'resourceServer/saveServerPara.do',
      method : 'POST',
      params : {
        jsonData : jsonData,
        serverId: currentServerId
      },
      success : function(response, request) {
        var success = Ext.decode(response.responseText).success;
        if (success) {
          paraViewStore.reload();
          //当每次进行资源组服务器的参数修改后，对原有的store进行重新加载，否则在 store.getModifiedRecords();会返回垃圾值
          store.getProxy().setExtraParam("groupId", currentGroupId);
          store.getProxy().setExtraParam("isChoosed", false);
          store.load();
          
          choosedStore.getProxy().setExtraParam("groupId", currentGroupId);
          choosedStore.getProxy().setExtraParam("isChoosed", true);
          choosedStore.load();
          
          Ext.Msg.alert('提示', '保存成功');
        } else {
          Ext.Msg.alert('提示', '数据保存失败！');
        }
        btn.disabled=false;
      },
      failure : function(result, request) {
        Ext.Msg.alert('提示', '数据保存失败！');
        btn.disabled=false;
      }
    });
  }
  
  function onConfirmForChooseListener(){
    var paras = paraWaitForChooseGird.getSelectionModel().getSelection();
    var ids = [];
    Ext.Array.each(paras, function(record) {
      ids.push(record.get('id'));
      record.dirty = true;
    });
    /*将这些param加到指定的server(serverId)的 param表中*/
    //var paras = [['苹果',29.89,0.24,0.81,'']];  // 这是数据格式，一样才行
    // 第二个参数如果为false，则会清空serverParaStore的数据后再追加
    // 这个方法有问题，不能显示dirty标志，让人不知道是保存了还是没有保存，果断放弃这个方法
    serverParaStore.loadData(paras, true);
    
    // 新方法 同步到数据库中，然后store load数据
    
    paraWaitForChooseWin.hide(this, function() {});
  }
 
  curServer=null;
  serverCombo.setValue('');
	  
  currentGroupId = bsid_serverConfig;
  
  store.getProxy().setExtraParam("groupId", bsid_serverConfig);
  store.getProxy().setExtraParam("isChoosed", false);
  store.getProxy().setExtraParam("curServer", curServer); 
  store.load();
  
  choosedStore.getProxy().setExtraParam("groupId", bsid_serverConfig);
  choosedStore.getProxy().setExtraParam("isChoosed", true);
  choosedStore.load();
});