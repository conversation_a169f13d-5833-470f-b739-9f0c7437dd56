Ext
		.onReady(function() {
			var res_bus_window;
			Ext.require([ '*' ]);
			Ext.require([ 'Ext.data.*', 'Ext.grid.*','Ext.selection.CellModel' ]);
			var clickGroudRow;
			Ext.define('agentGroupModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'id',
					type : 'int',
					useNull : true
				}, {
					name : 'agBsId',
					type : 'long'
				}, {
					name : 'name',
					type : 'string'
				}, {
					name : 'agBs',
					type : 'string'
				}, {
					name : 'agBsTypeId',
					type : 'long'
				}, {
					name : 'agBsType',
					type : 'string'
				}, {
					name : 'execUserName',
					type : 'string'
				}, {
					name : 'description',
					type : 'string'
				} ]
			});
			Ext.define('agBsModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'BSNAME', // 名称
					type : 'string'
				}, {
					name : 'IID', // 系统ID
					type : 'long'
				} ]
			});

			Ext.define('agBsTypeModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'BSTYPENAME', // 名称
					type : 'string'
				}, {
					name : 'BSTYPEID', // 系统ID
					type : 'long'
				} ]
			});
			var store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 12,
				model : 'agentGroupModel',
				proxy : {
					type : 'ajax',
					url : 'agentGroup/groups.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			store.on('beforeload', function(store, options) {
				var new_params = {
					agentGroupName : nameField.getValue(),
					agBsId : agBsComboQuery.getValue() == null
							|| agBsComboQuery.getValue() == '' ? 0
							: agBsComboQuery.getValue(),
					agBsTypeId : agBsTypeComboQuery.getValue() == null
							|| agBsTypeComboQuery.getValue() == '' ? 0
							: agBsTypeComboQuery.getValue()
				};

				Ext.apply(store.proxy.extraParams, new_params);
			});

			var group_ds = store;

			// var rowEditing = Ext.create('Ext.grid.plugin.RowEditing', {
			// listeners : {
			// cancelEdit : function(rowEditing, context) {
			// if (context.record.phantom) {
			// store.remove(context.record);
			// }
			// }
			// }
			// });

			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});
			// 分页工具
			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
				store : store,
				dock : 'bottom',
				baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo : true,
				emptyMsg : '找不到任何记录'
			});

			var nameField = Ext.create("Ext.form.field.Text", {
				fieldLabel : '分组名称',
				emptyText : '--请输入分组名称--',
				labelWidth : 65,
				labelAlign : 'right',
				name : 'resName',
				width : '20%',
				listeners : {
					specialkey : function(field, e) {
						if (e.getKey() == e.ENTER) {
							pageBar.moveFirst();
						}
					}
				}
			});

			var agBsStoreQuery = Ext.create('Ext.data.Store', {
				model : 'agBsModel',
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getAgentGroupBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			var agBsTypeStoreQuery = Ext.create('Ext.data.Store', {
				model : 'agBsTypeModel',
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getAgentGroupBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			agBsTypeStoreQuery.on('beforeload', function(store, options) {
				var new_params = {
					fk : 0
				};
				Ext.apply(agBsTypeStoreQuery.proxy.extraParams, new_params);
			});

			var agBsComboQuery = Ext.create('Ext.form.field.ComboBox', {
				name : 'agBsComboQuery',
				queryMode : 'local',
				id : 'agBsComboQuery',
				fieldLabel : '一级分类',
				labelWidth : 65,
				// padding : '5',
				displayField : 'BSNAME',
				valueField : 'IID',
				editable : true,
				emptyText : '--请选择一级分类--',
				store : agBsStoreQuery,
				width : '20%',
				triggerAction : "all",
				labelAlign : 'right',
				listeners : {
					change : function() {
						agBsTypeComboQuery.clearValue();
						agBsTypeComboQuery.applyEmptyText();
						agBsTypeComboQuery.getPicker().getSelectionModel()
								.doMultiSelect([], false);
						if (this.value != "" && this.value != null) {
							agBsTypeStoreQuery.load({
								params : {
									fk : this.value
								}
							});
						}
					},
					specialkey : function(field, e) {
						if (e.getKey() == e.ENTER) {
							pageBar.moveFirst();
						}
					}
				}
			});

			var agBsTypeComboQuery = Ext.create('Ext.form.field.ComboBox', {
				name : 'agBsTypeComboQuery',
				// padding : '5',
				id : 'agBsTypeComboQuery',
				queryMode : 'local',
				fieldLabel : '二级分类',
				labelWidth : 65,
				// forceSelection : true, // 要求输入值必须在列表中存在
				displayField : 'BSTYPENAME',
				valueField : 'BSTYPEID',
				editable : true,
				labelAlign : 'right',
				emptyText : '--请选择二级分类--',
				store : agBsTypeStoreQuery,
				width : '20%',
				triggerAction : "all",
				listeners : {
					specialkey : function(field, e) {
						if (e.getKey() == e.ENTER) {
							pageBar.moveFirst();
						}
					}
				}
			});

			var agBsStore = Ext.create('Ext.data.Store', {
				model : 'agBsModel',
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getAgentGroupBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			var agBsTypeStore = Ext.create('Ext.data.Store', {
				model : 'agBsTypeModel',
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getAgentGroupBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			agBsTypeStore.on('beforeload', function(store, options) {
				var new_params = {
					fk : 0
				};
				Ext.apply(agBsTypeStore.proxy.extraParams, new_params);
			});

			var agBsCombo = Ext.create('Ext.form.field.ComboBox', {
				name : 'agBsName',
				queryMode : 'local',
				id : 'agBsName',
				// padding : '5',
				displayField : 'BSNAME',
				valueField : 'IID',
				editable : false,
				emptyText : '--请选择一级分类--',
				store : agBsStore,
				width : '25%',
				triggerAction : "all",
				listeners : {
					// change : function() {
					// // agBsTypeCombo.clearValue();
					// // agBsTypeCombo.applyEmptyText();
					// agBsTypeCombo.getPicker().getSelectionModel().doMultiSelect([],
					// false);
					// agBsTypeStore.load({
					// params : {
					// fk : this.value
					// }
					// });
					// },
					select : function(combo, records, eOpts) {
						var record = store.getAt(clickGroudRow);
						record.data.agBsId = combo.getValue();
						record.data.agBsTypeId = '';
						record.data.agBsType = '';
						// var seletedGird =
						// groupList.getSelectionModel().getSelection();//获得选中的项
						// var rowid =
						// groupList.getStore().indexOf(seletedGird[0]);//获得选中的第一项在store内的行号
						var selectValue = combo.getValue();
						Ext.getCmp("agBsType").clearValue();
						Ext.getCmp("agBsType").setValue();
						agBsTypeStore.removeAll();
						var insId = selectValue;
						agBsTypeStore.load({
							params : {
								fk : insId
							}
						});
					}
				// afterRender: function(combo) {
				// if(filter_bussId!='-1' && filter_bussId!='') {
				// agBsCombo.setValue(parseInt(filter_bussId));
				// }
				// }
				}
			});

			// agBsStore.on('load', function (store, options) {
			// if(filter_bussId!='-1'&& filter_bussId!='') {
			// agBsCombo.setValue(parseInt(filter_bussId));
			// }
			// });

			/** 二级分类* */
			var agBsTypeCombo = Ext.create('Ext.form.field.ComboBox', {
				name : 'agBsType',
				// padding : '5',
				id : 'agBsType',
				queryMode : 'local',
				// forceSelection : true, // 要求输入值必须在列表中存在
				displayField : 'BSTYPENAME',
				valueField : 'BSTYPEID',
				editable : false,
				emptyText : '--请选择二级分类--',
				store : agBsTypeStore,
				width : '25%',
				triggerAction : "all",
				listeners : {
					click : {
						element : 'el', // bind to the underlying el property on
										// the panel
						fn : function() {
							var record = store.getAt(clickGroudRow);
							console.log(record.data.agBsId);
							var insId = record.data.agBsId;
							if (insId == '') {
								insId = 0;
							}
							agBsTypeStore.load({
								params : {
									fk : insId
								}
							});
						}
					},
					select : function(combo, records, eOpts) {
						var record = store.getAt(clickGroudRow);
						record.data.agBsTypeId = combo.getValue();
					}
				}
			});

			var search_form = Ext.create('Ext.form.Panel', {
				region : 'north',
				border : false,
				baseCls : 'customize_gray_back',
				dockedItems : [ {
					xtype : 'toolbar',
					baseCls : 'customize_gray_back',
					dock : 'top',
					border : false,
					items : [ nameField, agBsComboQuery, agBsTypeComboQuery, {
						text : '查询',
						textAlign : 'center',
						cls : 'Common_Btn',
						handler : function() {
							pageBar.moveFirst();
						}
					}, {
						text : '清空',
						textAlign : 'center',
						cls : 'Common_Btn',
						handler : function() {
							nameField.setValue();
							agBsComboQuery.setValue();
							agBsTypeStoreQuery.removeAll();
						}
					}, {
						text : '增加',
						textAlign : 'center',
						cls : 'Common_Btn',
						handler : add
					}, {
						text : '保存',
						cls : 'Common_Btn',
						handler : saveGroups
					}, {
						itemId : 'delete',
						text : '删除',
						cls : 'Common_Btn',
						// disabled : true,
						handler : deleteGroups
					}, {
						text : '导入',
						cls : 'Common_Btn',
						handler : importGroups
					}, {
						text : '导出',
						cls : 'Common_Btn',
						handler : exportGroups
					} ]
				} ]
			});
			
			function exportGroups()
		    {
		    	var record = groupList.getSelectionModel ().getSelection ();
		    	var iidStr = "";
				Ext.Array.each (record, function (recordObj)
				{
					iidStr += "," + recordObj.get ('id');
				});
		    	if(iidStr.length<=0)
		    	{
		    		Ext.Msg.alert ('提示','请选择要操作的行！');
		    		return;
		    	}
		    	iidStr = iidStr.substr(1);
		    	window.location.href = 'agentGroup/exportAgentGroup.do?iidStr='+iidStr;
		    }

			var groupList = Ext
					.create(
							'Ext.ux.ideal.grid.Panel',
							{
								region : 'center',
								split : true,
								multiSelect : true,
								emptyText : '没有分组',
								plugins : [ cellEditing ],
								padding : grid_space,
								width : 400,
								height : contentPanel.getHeight() - 35,
								cls : 'customize_panel_back',
								// frame: true,
								selModel : selModel,
								columnLines : true,
								ipageBaseCls : Ext.baseCSSPrefix
										+ 'toolbar customize_toolbar',
								// bbar : pageBar,
								iqueryFun : function() {
									pageBar.moveFirst();
								},
								store : store,
								// padding: '0 0 0 0', //在frame为true时
								// 用于清除gridpanel的内容的内补丁
								border : false,
								// listeners: {
								// select: function( e, record, index, eOpts ){
								// agBsTypeCombo.clearValue();
								// agBsTypeCombo.applyEmptyText();
								// agBsTypeStore.removeAll();
								// }
								// },
								columns : [
										{
											text : '编号',
											width : 40,
											sortable : true,
											hidden : true,
											dataIndex : 'id'
										},
										{
											text : '序号',
											xtype : 'rownumberer',
											width : 40
										},
										{
											text : '分组名称',
											dataIndex : 'name',
											flex : 1,
											sortable : true,
											editor : {
												allowBlank : false
											}
										},
										{
											text : '一级分类id',
											dataIndex : 'agBsId',
											width : 80,
											hidden : true
										},
										{
											text : '一级分类',
											dataIndex : 'agBs',
											editor : agBsCombo,
											flex : 1,

											renderer : function(value,
													metadata, record) {
												// var index =
												// agBsStore.find('IID', value);
												// if (index != -1) {
												// return
												// agBsStore.getAt(index).data.BSNAME;
												// } else {
												// return '';
												// }
												agBsStore.reload();
												var agBsId = value;
												var str = '';
												// var index =
												// agBsStore.find('IID',
												// agBsId);
												var index = agBsStore.find(
														'IID', agBsId, 0,
														false, true, true);
												if (index != -1) {
													str = str
															+ agBsStore
																	.getAt(index).data.BSNAME;
												} else {
													str = value;
												}
												record.data.BSNAME = str;
												record.data.agBs = str;
												return str;
											}
										},
										{
											text : '二级分类id',
											dataIndex : 'agBsTypeId',
											width : 80,
											hidden : true
										},
										{
											text : '二级分类',
											dataIndex : 'agBsType',
											editor : agBsTypeCombo,
											flex : 1,
											renderer : function(value,
													metadata, record) {
												agBsTypeStore.reload();
												if (value != "") {
													var agBsTypeId = value;
													var str = '';
													agBsTypeStore
															.load({
																params : {
																	fk : record.data.agBsId
																}
															});
													// var index =
													// agBsTypeStore.find('BSTYPEID',
													// agBsTypeId);
													var index = agBsTypeStore
															.find('BSTYPEID',
																	agBsTypeId,
																	0, false,
																	true, true);
													if (index != -1) {
														str = str
																+ agBsTypeStore
																		.getAt(index).data.BSTYPENAME;
													} else {
														str = value;
													}
													record.data.BSTYPENAME = str;
													record.data.agBsType = str;
													return str;
												}
											}
										},
										{
											text : '执行用户',
											dataIndex : 'execUserName',
											flex : 1,
											sortable : true,
											editor : {
												allowBlank : true
											}
										},
										{
											text : '描述',
											dataIndex : 'description',
											flex : 1,
											sortable : true,
											editor : {
												allowBlank : true
									}
								},
								{
											// text : '操作',
											// width : 150,
											// renderer : function(value, p,
											// record,
											// rowIndex) {
											// var iid = record.get('id');
											// var name = record.get('name');
											// //console.log(name);
											// return '<span
											// class="switch_span">'
											// + '<a href="javascript:void(0)"
											// onclick="showres_bus_window('
											// + iid
											// + ',\''
											// + name
											// + '\''
											// + ')">'
											// + '<img
											// src="images/monitor_bg.png"
											// align="absmiddle"
											// class="script_set"></img>&nbsp;服务器配置'
											// + '</a>' + '</span>'
											// + '&nbsp;&nbsp;&nbsp;&nbsp;';
											// }
											text : '操作',
											xtype : 'actiontextcolumn',
											dataIndex : 'stepOperation',
											width : 150,
											items : [ {
												text : '服务器配置',
												iconCls : 'script_set',
												handler : function(grid,
														rowIndex) {
													var iid = grid.getStore().data.items[rowIndex].data.id;
													var name = grid.getStore().data.items[rowIndex].data.name;
													showres_bus_window(iid,
															name);
												}
											} ]
										} ]
							// dockedItems : [ {
							// xtype : 'toolbar',
							// baseCls:'customize_gray_back',
							// items : [
							// nameField,agBsComboQuery,agBsTypeComboQuery, {
							// text : '查询',
							// textAlign : 'center',
							// cls : 'Common_Btn',
							// handler : function() {
							// pageBar.moveFirst();
							// }
							// },{
							// text : '清空',
							// textAlign : 'center',
							// cls : 'Common_Btn',
							// handler : function() {
							// nameField.setValue();
							// agBsComboQuery.setValue();
							// agBsTypeStoreQuery.removeAll();
							// }
							// }, {
							// text : '增加',
							// textAlign : 'center',
							// cls : 'Common_Btn',
							// handler : add
							// }, {
							// text : '保存',
							// cls : 'Common_Btn',
							// handler : saveGroups
							// }, {
							// itemId : 'delete',
							// text : '删除',
							// cls : 'Common_Btn',
							// disabled : true,
							// handler : deleteGroups
							// } ]
							// } ]
							});
			// groupList.getSelectionModel().on('selectionchange',
			// function(selModel, selections) {
			// groupList.down('#delete').setDisabled(selections.length === 0);
			// });

			groupList.on("celldblclick", function(obj, td, cellIndex, record,
					tr, rowIndex, e, eOpts) {
				clickGroudRow = rowIndex;
			})
			var groupListPanel = groupList;

			var pp = Ext.create('Ext.panel.Panel', {
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				layout : 'border',
				// cls:'customize_panel_back',
				header : false,
				border : false,
				items : [ search_form, groupList ],
				renderTo : "agent-group"
			});
			contentPanel.on('resize', function() {
				pp.setHeight(contentPanel.getHeight() - modelHeigth);
				pp.setWidth(contentPanel.getWidth());
			});

			function setMessage(msg) {
				Ext.Msg.alert('提示', msg);
			}

			/* 解决IE下trim问题 */
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};
			function add() {
				var store = groupList.getStore();
				var p = {
					id : '',
					name : '',
					description : ''
				};
				store.insert(0, p);
				groupList.getView().refresh();
			}

			/* 保存分组记录 */
			function saveGroups() {
				var m = group_ds.getModifiedRecords();
				if (m.length < 1) {
					return;
				}
				var jsonData = "[";
				for (var i = 0, len = m.length; i < len; i++) {
					var resName = m[i].get("name").trim();
					var resDes = m[i].get("description").trim();
					var resiid = m[i].get("id");
					var agBsTypeId = m[i].get("agBsTypeId");
					var agBsId = m[i].get("agBsId");
					var execUserName = m[i].get("execUserName");
					if ("" == agBsId || null == agBsId) {
						setMessage('【' + resName + '】' + '一级分类不能为空！');
						return;
					}
					if ("" == agBsTypeId || null == agBsTypeId) {
						setMessage('【' + resName + '】' + '二级分类不能为空！');
						return;
					}
					if ("" == resName || null == resName) {
						setMessage('分组名称不能为空！');
						return;
					}
					if (fucCheckLength(resName) > 250) {
						setMessage('分组名称不能超过250字符！');
						return;
					}
					if (fucCheckLength(resDes) > 250) {
						setMessage('分组描述不能超过250字符！');
						return;
					}
					// for (var k = 0; k < group_ds.getCount(); k++) {
					// var record = group_ds.getAt(k);
					// var cname = record.data.name;
					// if (cname.trim() == resName) {
					// n = n + 1;
					// }
					// }
					// if (n > 1) {
					// setMessage('分组名称不能有重复！');
					// return;
					// }
					var isOk = false;

					// 检查分组名称是否有重复
					Ext.Ajax.request({
						url : 'agentGroup/checkGroupExist.do',
						method : 'POST',
						async : false,
						params : {
							groupName : resName,
							iid : resiid == '' || null == resiid ? 0 : resiid
						},
						success : function(response, request) {
							isOk = Ext.decode(response.responseText).success;
						},
						failure : function(result, request) {
							Ext.Msg.alert('提示', '保存失败！');
						}
					});

					if (isOk) {
						setMessage('分组名称' + resName + '有重复！');
						return;
					}
					var ss = Ext.JSON.encode(m[i].data);
					if (i == 0)
						jsonData = jsonData + ss;
					else
						jsonData = jsonData + "," + ss;
				}
				jsonData = jsonData + "]";
				Ext.Ajax
						.request({
							url : 'agentGroup/groupsSave.do',
							method : 'POST',
							params : {
								jsonData : jsonData
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
								if (success) {
									group_ds.modified = [];
									group_ds.reload();
									Ext.Msg.alert('提示', '保存成功');
								} else {
									if (null != message) {
										Ext.Msg.alert('提示', message);
									} else {
										Ext.Msg.alert('提示', '保存失败！');
									}
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示', '保存失败！');
							}
						});
			}

			// 根据ID删除
			function deleteGroups() {
				var data = groupListPanel.getView().getSelectionModel()
						.getSelection();
				if (data.length == 0) {
					Ext.Msg.alert('提示', '请先选择您要操作的行!');
					return;
				} else {
					Ext.Msg
							.confirm(
									"请确认",
									"是否真的要删除数据？<br>部份关联数据级联删除？",
									function(button, text) {
										if (button == "yes") {
											var ids = [];
											Ext.Array.each(data, function(
													record) {
												var groupId = record.get('id');
												// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
												if (groupId) {
													ids.push(groupId);
												} else {
													group_ds.remove(record);
												}
											});

											if (ids.length > 0) {
												Ext.Ajax
														.request({
															url : 'agentGroup/groupsDelete.do',
															params : {
																deleteIds : ids
																		.join(','),
																strDelIds : ids
															},
															method : 'POST',
															success : function(response, opts) {
																var message = Ext.decode(response.responseText).message;
																var success = Ext.decode(response.responseText).success;
																// 当后台数据同步成功时
																if (success) {
																	group_ds.reload();
																	Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
																} else {
																	if (null != message){
																		Ext.Msg.alert('提示', message);
																	}else {
																		Ext.Msg.alert('提示', '删除失败！');
																	}

																}
															}
														});
											} else {
												groupList.getView().refresh();
											}

										}
									});

				}

			}

			function importGroups() {
				var uploadWindows;
				var uploadForm
				uploadForm = Ext.create('Ext.form.FormPanel', {
					border : false,
					items : [ {
						xtype : 'filefield',
						name : 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
						fieldLabel : '选择文件',
						labelWidth : 80,
						anchor : '90%',
						// labelAlign: 'right',
						margin : '10 10 0 40',
						buttonText : '浏览'
					} ],
					buttonAlign : 'center',
					buttons : [ {
						text : '确定',
						handler : upExeclData
					}, {
						text : '取消',
						handler : function() {
							uploadWindows.close();
						}
					},
					{
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadSsTemplate.do?fileName=1';
						}
					}]
				});
				uploadWindows = Ext.create('Ext.window.Window', {
					title : '导入文件',
					layout : 'fit',
					height : 190,
					width : 600,
					modal : true,
					// autoScroll : true,
					items : [ uploadForm ],
					listeners : {
						close : function(g, opt) {
							uploadForm.destroy();
						}
					}
				});
				uploadWindows.show();
				function upExeclData() {
					var form = uploadForm.getForm();
					var hdupfile = form.findField("fileName").getValue();
					if (hdupfile == '') {
						Ext.Msg.alert('提示', "请选择文件...");
						return;
					}
					uploadTemplate(form);
				}
				function uploadTemplate(form) {
					if (form.isValid()) {
						form
								.submit({
									url : 'agentGroup/importAgentExcel.do',
									params : {
										importType : 2
									},
									success : function(form, action) {
										var sumsg = Ext
												.decode(action.response.responseText).message;
										Ext.Msg.alert('提示', sumsg);
										uploadWindows.close();
										agBsStoreQuery.reload();
										agBsTypeStoreQuery.reload();
										pageBar.moveFirst();
										return;
									},
									failure : function(form, action) {
										var msg = Ext
												.decode(action.response.responseText).message;
										Ext.Msg.alert('提示', msg);
										return;
									}
								});
					}
				}
			}

			function showres_bus_window(bsManagerId, groupName) {

				if (bsManagerId == null || bsManagerId == '') {
					Ext.Msg.alert('提示', '请先保存分组后再配置服务。');
					return;
				}


					var paraWaitForChooseWin;
					var paraViewWin;
					var currentServerId;
					var currentGroupId=-1;
					var curServer=-1;
					var toBindServerIdstr = [];//待增加服务器数组
					var unBindServerIdstr = [];//选择的要解绑的服务器数组


					Ext.define('serverModel', {
						extend : 'Ext.data.Model',
						fields : [ {
							name : 'id',
							type : 'int',
							useNull : true
						}, {
							name : 'hostName',
							type : 'string'
						}, {
							name : 'ip',
							type : 'string'
						}, {
							name : 'iexecUserName',
							type : 'string'
						}, {
							name : 'port',
							type : 'string',
							defaultValue: 1500
						},{
							name : 'priority',
							type : 'int',
							defaultValue: 5
						}, {
							name : 'systemType',
							type : 'string'
						}, {
							name : 'checked',
							type : 'boolean'
						}, {
							name : 'itemClicked',
							type : 'boolean'
						}, {
							name : 'isleftScreen',
							type : 'boolean',
							defaultValue: false
						} , {
							name : 'iosName',
							type : 'string',
						}  , {
							name : 'iagentVersion',
							type : 'string',
						}]
					});

					Ext.define('ServerParameterModel', {
						extend : 'Ext.data.Model',
						fields : [ {
							name : 'id',
							type : 'int',
							useNull : true
						}, {
							name : 'name',
							type : 'string'
						}, {
							name : 'paramType',
							type : 'string'
						}, {
							name : 'value',
							type : 'string'
						},{
							name : 'vid',
							type : 'int'
						},{
							name : 'serverId',
							type : 'int'
						} ]
					});

					Ext.define('resourceGroupModel', {
						extend : 'Ext.data.Model',
						fields : [{
							name : 'id',
							type : 'int',
							useNull : true
						}, {
							name : 'name',
							type : 'string'
						}, {
							name : 'description',
							type : 'string'
						} ]
					});

					Ext.define('paraViewModel', {
						extend : 'Ext.data.Model',
						fields : [{
							name : 'resName',
							type : 'string'
						}, {
							name : 'name',
							type : 'string'
						}, {
							name : 'paramType',
							type : 'string'
						},{
							name : 'vid',
							type : 'int'
						}, {
							name : 'originalValue',
							type : 'string'
						}, {
							name : 'value',
							type : 'string'
						}, {
							name : 'typeprimal',
							type : 'string'
						}]
					});

					var resourceGroupColumns = [
						{text: '编号', hidden: true, dataIndex: 'id'},
						{text: '序号', xtype: 'rownumberer', width: 40},
						{text: '名称', dataIndex: 'name', flex: 1, sortable: true},
						{text: '描述', dataIndex: 'description', hidden: true, flex: 1}
					];

					var paraColumns = [
						{text: '编号', width: 40, sortable: true, hidden: true, dataIndex: 'id'},
						{text: '序号', xtype: 'rownumberer', width: 40},
						{text: '参数名称', dataIndex: 'name', flex: 2, sortable: true},
						{text: '参数类型', dataIndex: 'paramType', flex: 1, sortable: true},
						{text: '参数值', dataIndex: 'value', flex: 2, sortable: true, editor: {allowBlank: true}}
					];

					var paraViewColumns = [
						{text: '序号', xtype: 'rownumberer', width: 40},
						{text: '资源组名称', dataIndex: 'resName',  sortable: true , width: 238},
						{text: '参数名称', dataIndex: 'name', sortable: true , width: 100},
						{text: '参数类型', dataIndex: 'paramType',  sortable: true, width: 80},
						{text: '资源组参数', dataIndex: 'originalValue', sortable: true, width: 390,
							renderer: function(value,p,r) {
								var typeInDB =r.get('typeprimal') ;
								var ptyp =r.get('paramType') ;

								if(typeInDB =='String 加密'){
									var temp ='';
									if(value!=null && value!=''){
										for(var i=0;i <value.length;i++){
											temp+='●';
										}
									}
									return temp;
								}

								if(ptyp !='String 加密' ){
									return value;
								}else{
									var temp ='';
									if(value!=null && value!=''){
										for(var i=0;i <value.length;i++){
											temp+='●';
										}
									}
									return temp;
								}
							}//,
//	                          getEditor: function(record) {
//	                            if(record.get('paramType')=="String"){
//	                              return Ext.create('Ext.grid.CellEditor', {
//	                                field: Ext.create( 'Ext.form.field.Text', {
//	                                  selectOnFocus : true
//	                                })
//	                              });
//	                            }else{
//	                              return Ext.create('Ext.grid.CellEditor', {
//	                                field: Ext.create( 'Ext.form.field.Text', {
//	                                  selectOnFocus : true,
//	                                  inputType:'password'
//	                                })
//	                              });
//	                            }
//	                          }
						},
						{text: '服务器参数', dataIndex: 'value',  sortable: true,  renderer: function(value,p,r) {
								var typeInDB =r.get('typeprimal') ;
								var ptyp =r.get('paramType') ;

								if(typeInDB =='String 加密'){
									var temp ='';
									if(value!=null && value!=''){
										for(var i=0;i <value.length;i++){
											temp+='●';
										}
									}
									return temp;
								}

								if(ptyp !='String 加密' ){
									return value;
								}else{
									var temp ='';
									if(value!=null && value!=''){
										for(var i=0;i <value.length;i++){
											temp+='●';
										}
									}
									return temp;
								}
							},
							getEditor: function(record) {
								if(record.get('paramType')!="String 加密"){
									return Ext.create('Ext.grid.CellEditor', {
										field: Ext.create( 'Ext.form.field.Text', {
											selectOnFocus : true
										})
									});
								}else{
									return Ext.create('Ext.grid.CellEditor', {
										field: Ext.create( 'Ext.form.field.Text', {
											selectOnFocus : true,
											inputType:'password'
										})
									});
								}
							}, width: 422}
					];

					//右侧的grid store
					var store = Ext.create('Ext.data.Store', {
						autoLoad: false,
						autoDestroy: true,
						pageSize: 50,
						model: 'serverModel',
						proxy: {
							type: 'ajax',
							url: 'agentGroup/getServers.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					var allServerStore= Ext.create('Ext.data.Store', {
						autoLoad: true,
						autoDestroy: true,
						pageSize: 50,
						model: 'serverModel',
						proxy: {
							type: 'ajax',
							url: 'resourceServer/getAllIP.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					allServerStore.on('beforeload', function(store, options) {
						var new_params =
							{
								ibusSysId : 0
							};
						Ext.apply (store.proxy.extraParams, new_params);
					});

					store.on ('beforeload', function (store, options)
					{
						var new_params = {
							hostName:host_name1.getValue(),
							startIp:ipStart1.getValue().trim(),
							endIp:ipEnd1.getValue().trim(),
							iagentVersion:agentVersion.getValue(),
							iosName:iosName.getValue()
						};
						Ext.apply (store.proxy.extraParams, new_params);
					});

					store.addListener('load',function(me, records, successful, eOpts){
						var chosedRecords=[];//存放选中记录
						$.each(records,
							function(index, record) {
								if (toBindServerIdstr.indexOf(record.get('id')) > -1) {
									chosedRecords.push(record);
								}
							});
						unChosedServList.getSelectionModel().select(chosedRecords, false, false); //选中记录

					});


					var choosedStore = Ext.create('Ext.data.Store', {
						autoLoad: false,
						autoDestroy: true,
						pageSize: 500,
						model: 'serverModel',
						proxy: {
							type: 'ajax',
							url: 'agentGroup/getServers.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					choosedStore.on ('beforeload', function (store, options)
					{
						var new_params = {
							hostName:host_name2.getValue(),
							startIp:ipStart2.getValue().trim(),
							endIp:ipEnd2.getValue().trim(),
							iosName:iosName1.getValue(),
							iagentVersion:agentVersion1.getValue()
						};
						Ext.apply (choosedStore.proxy.extraParams, new_params);
					});


					choosedStore.addListener('load',function(me, records, successful, eOpts){
						var chosedRecords=[];//存放选中记录
						$.each(records,
							function(index, record) {
								if (unBindServerIdstr.indexOf(record.get('id')) > -1) {
									chosedRecords.push(record);
								}
							});
						ChosedServList.getSelectionModel().select(chosedRecords, false, false); //选中记录
					});


					var serverParaStore = Ext.create('Ext.data.Store', {
						autoDestroy: true,
						model: 'ServerParameterModel',
						proxy: {
							type: 'ajax',
							url: 'resourceServer/serverPara.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					var paraWaitForChooseStore = Ext.create('Ext.data.Store', {
						autoDestroy: true,
						model: 'ServerParameterModel',
						proxy: {
							type: 'ajax',
							url: 'resourceServer/unchoosedParams.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					var resourceGroupWaitForChooseStore = Ext.create('Ext.data.Store', {
						autoDestroy: true,
						model: 'resourceGroupModel',
						proxy: {
							type: 'ajax',
							url: 'resourceGroup/groups.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					var paraViewStore = Ext.create('Ext.data.Store', {
						autoDestroy: true,
						model: 'paraViewModel',
						proxy: {
							type: 'ajax',
							url: 'resourceServer/viewParamsByRes.do',
							reader: {
								type: 'json',
								root: 'dataList',
								idProperty:'id'+'vid'
							}
						}
					});

					var resourceGroupStore = Ext.create('Ext.data.Store', {
						autoLoad: true,
						autoDestroy: true,
						model: 'resourceGroupModel',
						proxy: {
							type: 'ajax',
							url: 'resourceGroup/groups.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});


					var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
						clicksToEdit : 2
					});

					var paraCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
						clicksToEdit : 1
					});

					var selModel = Ext.create('Ext.selection.CheckboxModel', {
						checkOnly: true,
						listeners: {
							selectionchange: function(sm, selections) {
							}
						}
					});




					var selModel1=Ext.create('Ext.selection.CheckboxModel', {
						checkOnly: true
					});

					var rightColumn = [{
						text: '编号',
						width: 40,
						sortable: true,
						hidden: true,
						dataIndex: 'id'
					}, {text: '序号', xtype: 'rownumberer', width: 40}, {
						xtype: 'checkcolumn',
						header: '选中',
						hidden: true,
						dataIndex: 'checked',
						width: 95,
						stopSelection: false,
						listeners: {
							checkchange: function (column, recordIndex, checked) {
								if(checked)
									unChosedServList.down('#view').setDisabled(false);
								else{
									var serverStore = unChosedServList.getStore();
									var storeCnt = serverStore.getCount();
									var isChecked =null;
									var cnt =0;
									for(var i=0;i<storeCnt;i++){
										isChecked= serverStore.getAt(i).get('checked');
										if(isChecked==true){
											cnt++;
										}
									}
								}

								/*              if(cnt==0){
                                                  unChosedServList.down('#view').setDisabled(true);
                                              }*/
							}
						}
					},{
						text: '主机名称',
						dataIndex: 'hostName',
						flex: 1,
						sortable: true,
						editor: {
							allowBlank: false
						}
					}, {
						text: 'IP',
						dataIndex: 'ip',
						flex: 1,
						sortable: true,
						editor: {
							allowBlank: false
						}
					}, {
						text: '端口号',
						dataIndex: 'port',
						sortable: true,
						editor: {
							allowBlank: false
						}
					}, {
						text: '操作系统',
						flex: 1,
						dataIndex: 'iosName',
						sortable: true,
						editor: {
							allowBlank: false
						}
					}, {
						text: 'Agent版本',
						flex: 1,
						dataIndex: 'iagentVersion',
						sortable: true,
						editor: {
							allowBlank: false
						}
					}];

					var ipStart1 = Ext.create ('Ext.form.TextField',
						{
							labelWidth : 58,
							fieldLabel : '起始IP',
							emptyText : '--请输入开始IP--',
							//labelSeparator : '',
							width : '31%',//33.3%
							labelAlign : 'right',
							listeners: {
								specialkey: function(field, e){
									if (e.getKey() == e.ENTER) {
										pageBar.moveFirst();
									}
								}
							}

//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
							// padding : '0 10 0 0'
						});

					/** 结束ip* */
					var ipEnd1 = Ext.create ('Ext.form.TextField',
						{
							labelWidth : 58,
							fieldLabel : '终止IP',
							emptyText : '--请输入截止IP--',
							//labelSeparator : '',
							labelAlign : 'right',
							width : '31%',
							listeners: {
								specialkey: function(field, e){
									if (e.getKey() == e.ENTER) {
										pageBar.moveFirst();
									}
								}
							}
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
							// padding : '0 10 0 0'
						});
				var iosName = Ext.create ('Ext.form.TextField',
					{
						labelWidth : 65,
						fieldLabel : '操作系统',
						emptyText : '--请输入操作系统--',
						//labelSeparator : '',
						width : '37%',//33.3%
						labelAlign : 'right',
						listeners: {
							specialkey: function(field, e){
								if (e.getKey() == e.ENTER) {
									pageBar.moveFirst();
								}
							}
						}
					});
				var agentVersion = Ext.create ('Ext.form.TextField',
					{
						labelWidth : 58,
						fieldLabel : '版本号',
						emptyText : '--请输入版本号--',
						//labelSeparator : '',
						width : '37%',//33.3%
						labelAlign : 'right',
						listeners: {
							specialkey: function(field, e){
								if (e.getKey() == e.ENTER) {
									pageBar.moveFirst();
								}
							}
						}
					});
					var host_name1 = new Ext.form.TextField({
						name : 'hostname1',
						fieldLabel : '主机名称',
						emptyText : '--请输入主机名称--',
						labelWidth: 65,
						labelAlign : 'right',
						width : '33%',
						listeners: {
							specialkey: function(field, e){
								if (e.getKey() == e.ENTER) {
									pageBar.moveFirst();
								}
							}
						}
					});
					function queryToBindMessage(){
						pageBar.moveFirst();
					}

					function queryBindedMessage(){
						pageBar1.moveFirst();
					}

					var rightQueryButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '查询',
						handler : queryToBindMessage
					});
					var rightResetButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '清空',
						handler : function(){
							host_name1.setValue();
							ipStart1.setValue();
							agentVersion.setValue();
							iosName.setValue();
							ipEnd1.setValue();
						}
					});

					var rightBindButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '绑定',
						handler : toBindServer
					});

					var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
						store : store,
						baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
						dock : 'bottom',
						displayInfo : true,
						emptyMsg : '找不到任何记录'
					});
					//右边grid  Ext.grid.Panel  Ext.ux.ideal.grid.Panel
					var unChosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
						iqueryFun : queryToBindMessage,
						region : 'center',
						//width : "50%",
//     bbar: pageBar,
						ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
						multiSelect: true,
						padding : panel_margin,
						split : true,
						selModel :selModel1,
						plugins : '',
//    xtype: 'cell-editing',
//    frame : true,
//    viewConfig: {
//      plugins: {
//        ptype: 'gridviewdragdrop',
//        dragGroup: 'secondGridDDGroup',
//        dropGroup: 'firstGridDDGroup'
//      },
//      listeners: {
////        drop: function(node, data, dropRec, dropPosition) {
////          var dropOn = dropRec ? ' ' + dropPosition + ' ' + dropRec.get('name') : ' on empty view';
////        }
//      }
//    },
						columnLines : true,
						emptyText: '没有服务器信息',
						store : store,
						columns : rightColumn,
						dockedItems: [{
							xtype : 'toolbar',
//					baseCls:'customize_gray_back',
							border : false,
							dock : 'top',
							items: [ ipStart1,ipEnd1,host_name1] //查询条件
						} ,
							{
								xtype : 'toolbar',
								border : false,
								dock : 'top',
								items: [ iosName,agentVersion] //查询条件
							} ,
							{
								xtype: 'toolbar',
//			      baseCls:'customize_gray_back',
								id:'unch_toolbar',
								items: [ rightQueryButton,rightResetButton,rightBindButton]
							}],
						listeners: {
//      itemclick: function(dv, record, item, index, e) {
//        var groupId = resourceGroupCombo.getValue();
////        if(groupId){
//          store.each(function(rec) {
//            rec.set('itemClicked',false);
//          });
//          record.set('itemClicked',true);
//          currentServerId = record.get('id');
//          unChosedServList.down('#delete').setDisabled(false);
//          unChosedServList.down('#view').setDisabled(false);
////          serverPara.down('#add').setDisabled(false);
////          serverParaStore.getProxy().setExtraParam('groupId', groupId);
////          serverParaStore.getProxy().setExtraParam('serverId', record.get('id'));
////          serverParaStore.load();
////        }else{
////          Ext.Msg.alert('提示', '请选择资源组，然后可以查看服务器的个性化参数配置!');
////        }
//      }
							select: function( e, record, index, eOpts ){
								if(toBindServerIdstr.indexOf(record.get('id'))==-1) {
									toBindServerIdstr.push(record.get('id'));
								}
							},
							deselect: function( e, record, index, eOpts ){
								if(toBindServerIdstr.indexOf(record.get('id'))>-1) {
									toBindServerIdstr.remove(record.get('id'));
								}
							}
						}
					});
					var leftColumns = [{
						text: '编号',
						width: 40,
						sortable: true,
						hidden: true,
						dataIndex: 'id'
					}, {text: '序号', xtype: 'rownumberer', width: 40}, {
						xtype: 'checkcolumn',
						header: '选中',
						hidden: true,
						dataIndex: 'checked',
						width: 95,
						stopSelection: false,
						listeners: {
							checkchange: function (column, recordIndex, checked) {
								if(checked)
									unChosedServList.down('#view').setDisabled(false);
								else{
									var serverStore = unChosedServList.getStore();
									var storeCnt = serverStore.getCount();
									var isChecked =null;
									var cnt =0;
									for(var i=0;i<storeCnt;i++){
										isChecked= serverStore.getAt(i).get('checked');
										if(isChecked==true){
											cnt++;
										}
									}
								}

								/*              if(cnt==0){
                                                  unChosedServList.down('#view').setDisabled(true);
                                              }*/
							}
						}
					},{
						text: '主机名称',
						dataIndex: 'hostName',
						flex: 1,
						sortable: true,
						//editor: {
						//   allowBlank: false
						// }
					}, {
						text: 'IP',
						dataIndex: 'ip',
						flex: 1,
						sortable: true,
						//editor: {
						//   allowBlank: false
						// }
					}, {
						text: '执行用户',
						dataIndex: 'iexecUserName',
						sortable: true,
						editor : {
							allowBlank: false,
							maxLength : 255,
							listeners:{
								blur: function(){
									var m = choosedStore.getModifiedRecords();
									for ( var i = 0, len = m.length; i < len; i++) {
										var value=m[i].get("iexecUserName").value;
									}
									Ext.Ajax.request({
										url : 'agentGroup/insertSave.do',
										method : 'POST',
										params : {
											iexecUserName : this.value,
											groupId : bsManagerId,
											iid : ChosedServList.getSelectionModel().getSelection()[0].get("id")
										},
										success : function(response, request) {
											choosedStore.reload();
											var success = Ext.decode(response.responseText).success;
											if (success) {
												Ext.Msg.alert('提示', '保存成功');
											} else {
												Ext.Msg.alert('提示', '保存失败！');
											}
										},
										failure : function(result, request) {
											Ext.Msg.alert('提示', '保存失败！');
										}
									});
								}
							}
						},
					},{
						text: '端口号',
						dataIndex: 'port',
						sortable: true,
						//editor: {
						//  allowBlank: false
						//}
					},{
						text : '优先级',
						dataIndex : 'priority',
						hidden:true,
						sortable : true,
						editor: new Ext.form.field.ComboBox({
							typeAhead: true,
							triggerAction: 'all',
							editable : false,
							store: [[1,1],[2,2],[3,3],[4,4],[5,5],[6,6],[7,7],[8,8],[9,9],[10,10]]
						})
					}, {
						text: '应用标识',
						dataIndex: 'systemType',
						hidden: true,
						flex: 1,
						sortable: true,
						//editor: {
						//    allowBlank: false
						//  }
					} , {
						text: '操作系统',
						flex: 1,
						dataIndex: 'iosName',
						sortable: true,
						editor: {
							allowBlank: false
						}
					}, {
						text: 'Agent版本',
						flex: 1,
						dataIndex: 'iagentVersion',
						sortable: true,
						editor: {
							allowBlank: false
						}
					} ];

					function getSave(dataIndex){
						var record = ChosedServList.getSelectionModel().getSelection();
						return record.get(dataIndex);
					}

					var ipStart2 = Ext.create ('Ext.form.TextField',
						{
							labelWidth : 58,
							fieldLabel : '起始IP',
							emptyText : '--请输入开始IP--',
							//labelSeparator : '',
							width : '31%',
							labelAlign : 'right',
							listeners: {
								specialkey: function(field, e){
									if (e.getKey() == e.ENTER) {
										pageBar1.moveFirst();
									}
								}
							}
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
							// padding : '0 10 0 0'
						});

					/** 结束ip* */
					var ipEnd2 = Ext.create ('Ext.form.TextField',
						{
							labelWidth : 58,
							fieldLabel : '终止IP',
							emptyText : '--请输入截止IP--',
							//labelSeparator : '',
							labelAlign : 'right',
							width : '31%',
							listeners: {
								specialkey: function(field, e){
									if (e.getKey() == e.ENTER) {
										pageBar1.moveFirst();
									}
								}
							}
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
							// padding : '0 10 0 0'
						});
				var iosName1 = Ext.create ('Ext.form.TextField',
					{
						labelWidth : 65,
						fieldLabel : '操作系统',
						emptyText : '--请输入操作系统--',
						//labelSeparator : '',
						width : '37%',//33.3%
						labelAlign : 'right',
						listeners: {
							specialkey: function(field, e){
								if (e.getKey() == e.ENTER) {
									pageBar.moveFirst();
								}
							}
						}
					});
				var agentVersion1 = Ext.create ('Ext.form.TextField',
					{
						labelWidth : 58,
						fieldLabel : '版本号',
						emptyText : '--请输入版本号--',
						//labelSeparator : '',
						width : '37%',//33.3%
						labelAlign : 'right',
						listeners: {
							specialkey: function(field, e){
								if (e.getKey() == e.ENTER) {
									pageBar.moveFirst();
								}
							}
						}
					});
					var host_name2 = new Ext.form.TextField({
						name : 'hostname2',
						fieldLabel : '主机名称',
						emptyText : '--请输入主机名称--',
						labelWidth: 65,
						labelAlign : 'right',
						width : '33%',
						listeners: {
							specialkey: function(field, e){
								if (e.getKey() == e.ENTER) {
									pageBar1.moveFirst();
								}
							}
						}
					});
					var leftQueryButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '查询',
						handler : queryBindedMessage
					});
					var leftResetButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '清空',
						handler : function(){
							host_name2.setValue();
							ipStart2.setValue();
							ipEnd2.setValue();
							iosName1.setValue();
							agentVersion1.setValue()
						}
					});

					var leftUnbindButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '解绑',
						handler : toUnBindServer
					});

					var batchImportButton   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						text : '批量导入',
						handler : function() {
							importExcel();
						}
					});

					function importExcel() {
						var uploadWindows;
						var uploadForm
						uploadForm = Ext.create('Ext.form.FormPanel', {
							border : false,
							items : [{
								xtype : 'filefield',
								name : 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
								fieldLabel : '选择文件',
								labelWidth : 80,
								anchor : '90%',
								// labelAlign: 'right',
								margin : '10 10 0 40',
								buttonText : '浏览'
							}],
							buttonAlign : 'center',
							buttons : [{
								text : '确定',
								handler : upExeclData
							}, {
								text : '取消',
								handler : function() {
									uploadWindows.close();
								}
							},
								{
									text: '下载模板',
									handler: function() {
										window.location.href = 'downloadSsTemplate.do?fileName=2';
									}
								}]
						});
						uploadWindows = Ext.create('Ext.window.Window', {
							title : '导入Agent文件',
							layout : 'fit',
							height : 200,
							width : 600,
							modal : true,
							// autoScroll : true,
							items : [uploadForm],
							listeners : {
								close : function(g, opt) {
									uploadForm.destroy();
								}
							}
						});
						uploadWindows.show();
						function upExeclData() {
							var form = uploadForm.getForm();
							var hdupfile = form.findField("fileName").getValue();
							if (hdupfile == '') {
								Ext.Msg.alert('提示', "请选择文件...");
								return;
							}
							uploadTemplate(form);
						}
						function uploadTemplate(form) {
							if (form.isValid()) {
								form.submit({
									url : 'agentGroup/importAgentExcel.do',
									params : {
										bsManagerId : bsManagerId,
										importType : 1
									},
									success : function(form, action) {
										var sumsg = Ext.decode(action.response.responseText).message;
										Ext.Msg.alert('提示', sumsg);
										pageBar.moveFirst();
										pageBar1.moveFirst();
										uploadWindows.close();
										myWhiteListGrid.ipage.moveFirst();
										return;
									},
									failure : function(form, action) {
										var msg = Ext.decode(action.response.responseText).message;
//						var mess = Ext.create('Ext.window.MessageBox', {
//									minHeight : 110,
//									minWidth : 500,
//									resizable : false
//								});
										Ext.Msg.alert('提示', msg);
										return;
									}
								});
							}
						}
					}

					var getImportExcelModel   = Ext.create("Ext.Button", {
						cls : 'Common_Btn',
						textAlign : 'center',
						hidden : true,
						text : '获取导入模板',
						handler : function() {
							getImportExcelModelFunction();
						}
					});

					function getImportExcelModelFunction()
					{
//    	var record = groupList.getSelectionModel ().getSelection ();
//    	var iidStr = "";
//		Ext.Array.each (record, function (recordObj)
//		{
//			iidStr += "," + recordObj.get ('id');
//		});
//    	if(iidStr.length<=0)
//    	{
//    		Ext.Msg.alert ('提示','请选择要操作的行！');
//    		return;
//    	}
//    	iidStr = iidStr.substr(1);
						window.location.href = 'agentGroup/getImportExcelModel.do';
					}

					var selModel2=Ext.create('Ext.selection.CheckboxModel', {
						checkOnly: true
					});
					var pageBar1 = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
						store : choosedStore,
						baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
						dock : 'bottom',
						displayInfo : true,
						emptyMsg : '找不到任何记录'
					});
					var ChosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
						region : 'west',
						// width : "50%",
						multiSelect: true,
						padding : panel_margin,
						selModel :selModel2,
						iqueryFun : queryBindedMessage,
						split : true,
//	    bbar:pageBar1,
						ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//	    viewConfig: {
//	        plugins: {
//	          ptype: 'gridviewdragdrop',
//	          dragGroup: 'firstGridDDGroup',
//	          dropGroup: 'secondGridDDGroup'
//	        },
//	        listeners: {
//	          drop: function(node, data, dropRec, dropPosition) {
//	            var dropOn = dropRec ? ' ' + dropPosition + ' ' + dropRec.get('name') : ' on empty view';
//	          }
//	        }
//	      },
						columnLines : true,
						emptyText: '没有服务器信息',
						store : choosedStore,
						clicksToEdit:1,
						columns : leftColumns,
//	    viewConfig: {
//	      getRowClass: function(record, rowIndex, rowParams, store){
//	        if (record.get("itemClicked")) {
//	          return 'x-grid-record-seablue';
//	        }
//	      }
//	    },
						dockedItems: [{
							xtype : 'toolbar',
//					baseCls:'customize_gray_back',
							border : false,
							dock : 'top',
							items: [ ipStart2,ipEnd2,host_name2] //查询条件
						},
							{
								xtype : 'toolbar',
								border : false,
								dock : 'top',
								items: [ iosName1,agentVersion1] //查询条件
							}, {
							xtype : 'toolbar',
//					baseCls:'customize_gray_back',
							border : false,
							dock : 'top',
							items: [  leftQueryButton,leftResetButton,leftUnbindButton,batchImportButton,getImportExcelModel]
						}],
						listeners: {
//	      itemclick: function(dv, record, item, index, e) {
//	        var groupId = resourceGroupCombo.getValue();
////	        if(groupId){
//	          store.each(function(rec) {
//	            rec.set('itemClicked',false);
//	          });
//	          record.set('itemClicked',true);
//	          currentServerId = record.get('id');
//	          unChosedServList.down('#delete').setDisabled(false);
//	          unChosedServList.down('#view').setDisabled(false);
////	          serverPara.down('#add').setDisabled(false);
////	          serverParaStore.getProxy().setExtraParam('groupId', groupId);
////	          serverParaStore.getProxy().setExtraParam('serverId', record.get('id'));
////	          serverParaStore.load();
////	        }else{
////	          Ext.Msg.alert('提示', '请选择资源组，然后可以查看服务器的个性化参数配置!');
////	        }
//	      }
							select: function( e, record, index, eOpts ){
								if(unBindServerIdstr.indexOf(record.get('id'))==-1) {
									unBindServerIdstr.push(record.get('id'));
								}
							},
							deselect: function( e, record, index, eOpts ){
								if(unBindServerIdstr.indexOf(record.get('id'))>-1) {
									unBindServerIdstr.remove(record.get('id'));
								}
							}
						}
					});



					var serverPara = Ext.create('Ext.ux.ideal.grid.Panel', {
						region: 'center',
						title: '服务器参数',
						split: true,
						multiSelect: true,
						plugins: [ paraCellEditing ],
						emptyText: '没有服务器个性化参数',
						width: "50%",
//    frame: true,
						columnLines : true,
						store: serverParaStore,
						columns: paraColumns,
						dockedItems: [{
							xtype: 'toolbar',
							baseCls:'customize_gray_back',
							items: [{
								itemId: 'add',
								text: '<u>增加</u>',
								iconCls:'add',
								disabled: true,
								handler: onAddServerParaListener
							},{
								itemId: 'save',
								text: '<u>保存</u>',
								handler: onSaveServerParaListener
							},{
								itemId: 'delete',
								text: '<u>删除</u>',
								iconCls:'delete',
								disabled: true,
								handler: onDeleteServerParaListener
							}]
						}]
					});
					serverPara.getSelectionModel().on('selectionchange', function(selModel, selections) {
						serverPara.down('#delete').setDisabled(selections.length === 0);
					});

					var resourceGroupWaitForChooseGird = Ext.create('Ext.ux.ideal.grid.Panel', {
						title: '资源组列表',
						width: 200,
						split: true,
						floatable: false,
						store: resourceGroupWaitForChooseStore,
						columns: resourceGroupColumns,
						region: 'west',
						emptyText: '没有数据',
						loadMask: true
					});
					resourceGroupWaitForChooseGird.getSelectionModel().on('selectionchange', function(selModel, selections) {
						var resourceGroupId;
						Ext.Array.each(selections, function(record) {
							resourceGroupId = record.get('id');
						});
						paraWaitForChooseStore.getProxy().setExtraParam("groupId", resourceGroupId);
						paraWaitForChooseStore.load();
					});

					var paraWaitForChooseGird = Ext.create('Ext.ux.ideal.grid.Panel', {
						multiSelect: true,
						title : '待选择个性化参数列表',
						columnLines : true,
						store : paraWaitForChooseStore,
						columns : paraColumns,
						region : 'center',
						emptyText: '没有数据',
						loadMask: true,
						border: false,
						dockedItems : [{
							xtype : 'toolbar',
							baseCls:'customize_gray_back',
							itemId : 'confirm',
							disabled: true,
							items : [{
								text : '确认',
								iconCls:'confirm',
								handler : onConfirmForChooseListener
							}]
						}]
					});
					paraWaitForChooseGird.getSelectionModel().on('selectionchange', function(selModel, selections) {
						paraWaitForChooseGird.down('#confirm').setDisabled(selections.length === 0);
					});

					var viewParaCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
						clicksToEdit : 1
					});
//  var paraViewGird = Ext.create('Ext.grid.Panel', {
////    title : '个性化参数总览',
//    columnLines : true,
//    store : paraViewStore,
//    plugins: [ viewParaCellEditing ],
//    columns : paraViewColumns,
//    region : 'center',
//    emptyText: '没有数据',
//    loadMask: true,
//    border: false,
//    dockedItems: [{
//      xtype: 'toolbar',
//      items: [{
//        itemId : 'save',
//        text : '保存',
//        icon: 'images/save.gif',
//        handler : onViewParaSaveListener
//      }]
//    }]
//  });

					var panelleft = Ext.create('Ext.panel.Panel',{
						layout : 'fit',
						title : '绑定服务器',
						bodyCls : 'x-docked-noborder-top',
						cls:'window_border   right_edge',
						region : 'west',
						border : false,
						split : true,
						width : '50%',
						items : [ChosedServList]
					});

					var panelrigth = Ext.create('Ext.panel.Panel',{
						layout : 'fit',
						title : '待选择的服务器',
						bodyCls : 'x-docked-noborder-top',
						cls:'window_border ',
						region : 'center',
						border : false,
						split : true,
						width : '50%',
						items : [unChosedServList]
					});
					// var server_config_panel = Ext.create('Ext.panel.Panel', {
					// 	height : contentPanel.getHeight()-40,
					// 	layout : 'border',
					// 	bodyPadding : grid_margin,
					// 	border : true,
					// 	items : [ panelleft ,panelrigth ],
					// 	renderTo : "agent-group-servers"
					// });

					/*  resizePanel(function(){
                        //server_config_panel.setWidth(contentPanel.getWidth()-15);
                        //server_config_panel.setHeight(contentPanel.getHeight()-100);
                      });*/

					//数组功能扩展
					Array.prototype.each = function(fn){
						fn = fn || Function.K;
						var a = [];
						var args = Array.prototype.slice.call(arguments, 1);
						for(var i = 0; i < this.length; i++){
							var res = fn.apply(this,[this[i],i].concat(args));
							if(res != null) a.push(res);
						}
						return a;
					};
					//数组是否包含指定元素
					Array.prototype.contains = function(suArr){
						for(var i = 0; i < this.length; i ++){
							if(this[i] == suArr){
								return true;
							}
						}
						return false;
					}
					//不重复元素构成的数组
					Array.prototype.uniquelize = function(){
						var ra = new Array();
						for(var i = 0; i < this.length; i ++){
							if(!ra.contains(this[i])){
								ra.push(this[i]);
							}
						}
						return ra;
					};
					//两个数组并集
					Array.union = function(a, b){
						return a.concat(b).uniquelize();
					};

					/**
					 * @description 绑定agent
					 */
					function toBindServer(){
						if(toBindServerIdstr.length ==0){
							Ext.Msg.alert('提示','请选择要绑定的Agent!');
							return;
						}
						Ext.Ajax.request({
							url : 'agentGroup/toBindServer.do',
							params : {
								serverIds :toBindServerIdstr,
								groupId: currentGroupId
							},
							method : 'POST',
							success : function(response, opts) {
								var success = Ext.decode(response.responseText).success;
								// 当后台数据同步成功时
								if (success) {
									toBindServerIdstr = [];
									unBindServerIdstr = [];
									pageBar.moveFirst();
									pageBar1.moveFirst();
									Ext.Msg.alert('提示', '绑定成功');
								} else {
									Ext.Msg.alert('提示', '绑定失败！');
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示',   '绑定失败');
							}
						});
					}

					function toUnBindServer(){
						if(unBindServerIdstr.length ==0){
							Ext.Msg.alert('提示','请选择要解绑的Agent!');
							return;
						}
						Ext.Ajax.request({
							url : 'agentGroup/toUnBindServer.do',
							params : {
								serverIds :unBindServerIdstr,
								groupId: currentGroupId
							},
							method : 'POST',
							success : function(response, opts) {
								var success = Ext.decode(response.responseText).success;
								// 当后台数据同步成功时
								if (success) {
									toBindServerIdstr = [];
									unBindServerIdstr = [];
									pageBar.moveFirst();
									pageBar1.moveFirst();
									Ext.Msg.alert('提示', '解绑成功！');

								} else {
									Ext.Msg.alert('提示', '解绑失败！');
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示',   '解绑失败');
							}
						});
					}



					function recover(){
						curServer=null;
						// serverCombo.setValue('');
						store.getProxy().setExtraParam("groupId",currentGroupId);
						store.getProxy().setExtraParam("isChoosed", false);
						store.getProxy().setExtraParam("curServer", curServer);
						store.load();
					}

					function onAddServerParaListener(){
						if(!(resourceGroupCombo.getValue() && currentServerId)){
							Ext.Msg.alert('提示', '请选择资源组以及服务器!');
						}
						if (!paraWaitForChooseWin) {
							paraWaitForChooseWin = Ext.create('widget.window', {
								title: '个性化配置',
								closeAction: 'hide',
								width: 700,
								minWidth: 350,
								height: 450,
								modal: true,
								layout: 'border',
								items: [/*resourceGroupWaitForChooseGird, */paraWaitForChooseGird ]
							});
						}

						if (paraWaitForChooseWin.isVisible()) {
							paraWaitForChooseWin.hide(this, function() {
							});
						} else {
							paraWaitForChooseWin.show(this, function() {
								paraWaitForChooseStore.getProxy().setExtraParam("groupId", currentGroupId);
								paraWaitForChooseStore.getProxy().setExtraParam("serverId", currentServerId);
								paraWaitForChooseStore.load();
							});
						}
					}


					/* 解决IE下trim问题 */
					String.prototype.trim=function(){
						return this.replace(/(^\s*)|(\s*$)/g, "");
					};

					function onSaveServerParaListener(){
						var m = serverParaStore.getModifiedRecords();
						if (m.length < 1) {
							return;
						}
						var jsonData = "[";
						for ( var i = 0, len = m.length; i < len; i++) {
							var value=m[i].get("value");
							if(value!='' && value.trim()==''){
								setMessage('参数值不可以全部是空白符！');
								return;
							}
							if(fucCheckLength(value)>250){
								setMessage('参数值不能超过250字符！');
								return;
							}
							var ss = Ext.JSON.encode(m[i].data);
							if (i == 0)
								jsonData = jsonData + ss;
							else
								jsonData = jsonData + "," + ss;
						}
						jsonData = jsonData + "]";
						Ext.Ajax.request({
							url : 'resourceServer/saveServerPara.do',
							method : 'POST',
							params : {
								jsonData : jsonData,
								serverId: currentServerId
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								if (success) {
									serverParaStore.reload();
									Ext.Msg.alert('提示', '保存成功');
								} else {
									Ext.Msg.alert('提示', '数据保存失败！');
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示', '数据保存失败！');
							}
						});
					}

					function onDeleteServerParaListener(){
						var data = serverPara.getSelectionModel().getSelection();
						if (data.length == 0) {
							Ext.Msg.alert('提示', '请先选择您要操作的行!');
							return;
						} else {
							Ext.Msg.confirm("请确认", "是否真的要删除数据？", function(button, text) {
								if (button == "yes") {
									var paramids = [];
									Ext.Array.each(data, function(record) {
										var paramId = record.get('id');
										// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
										if (paramId) {
											paramids.push(paramId);
										}
									});

									Ext.Ajax.request({
										url : 'resourceServer/deleteServerPara.do',
										params : {
											deleteIds : paramids.join(','),
											serverId: currentServerId
										},
										method : 'POST',
										success : function(response, opts) {
											var success = Ext.decode(response.responseText).success;
											// 当后台数据同步成功时
											if (success) {
												Ext.Array.each(data, function(record) {
													serverParaStore.remove(record);// 页面效果
												});
												Ext.Msg.alert('提示', '删除成功');
											} else {
												Ext.Msg.alert('提示', '数据删除失败！');
											}
										}
									});
								}
							});
						}
					}


					function onConfirmForChooseListener(){
						var paras = paraWaitForChooseGird.getSelectionModel().getSelection();
						var ids = [];
						Ext.Array.each(paras, function(record) {
							ids.push(record.get('id'));
							record.dirty = true;
						});
						/*将这些param加到指定的server(serverId)的 param表中*/
						//var paras = [['苹果',29.89,0.24,0.81,'']];  // 这是数据格式，一样才行
						// 第二个参数如果为false，则会清空serverParaStore的数据后再追加
						// 这个方法有问题，不能显示dirty标志，让人不知道是保存了还是没有保存，果断放弃这个方法
						serverParaStore.loadData(paras, true);

						// 新方法 同步到数据库中，然后store load数据

						paraWaitForChooseWin.hide(this, function() {});
					}

					curServer=null;
					// serverCombo.setValue('');

					currentGroupId = bsManagerId;

					store.getProxy().setExtraParam("groupId", bsManagerId);
					store.getProxy().setExtraParam("isChoosed", false);
					store.getProxy().setExtraParam("curServer", curServer);
					store.load();

					choosedStore.getProxy().setExtraParam("groupId", bsManagerId);
					choosedStore.getProxy().setExtraParam("isChoosed", true);
					choosedStore.load();
















				//================================================







				if (res_bus_window == undefined || !res_bus_window.isVisible()) {
					res_bus_window = Ext.create('Ext.window.Window', {
						title : '服务器配置 - 分组名称：' + groupName,
						modal : true,
						closeAction : 'destroy',
						constrain : true,
						autoScroll : true,
						width : contentPanel.getWidth(),
						height : contentPanel.getHeight(),
						draggable : false,// 禁止拖动
						resizable : false,// 禁止缩放
						layout : 'border',
						bodyPadding : grid_margin,
						items : [ panelleft ,panelrigth ]
						// loader : {
						// 	url : 'agentGroup/server_config_panel.do',
						// 	params : {
						// 		bsManagerId : bsManagerId
						// 	},
						// 	autoLoad : true,
						// 	scripts : true
						// }
					});
				}
				res_bus_window.show();
			}
		});
