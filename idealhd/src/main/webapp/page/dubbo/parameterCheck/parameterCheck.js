Ext.onReady(function () {
    var store;
    Ext.define('parameterCheckModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        }, {
            name: 'ruleName',
            type: 'string'
        }, {
            name: 'checkRule',
            type: 'string'
        }, {
            name: 'ruleDes',
            type: 'string'
        }]
    });
    store = Ext.create('Ext.data.Store', {
        // fields: ['ruleName', 'checkRule', 'ruleDes'],
        // data: [{
        //     'ruleName': "name",
        //     'checkRule': "checkRule",
        //     'ruleDes': "des"
        // }, {
        //     'ruleName': "name",
        //     'checkRule': "checkRule",
        //     'ruleDes': "des"
        // }, {
        //     'ruleName': "name",
        //     'checkRule': "checkRule",
        //     'ruleDes': "des"
        // }]
        autoLoad: true,
        model: 'parameterCheckModel',
        proxy: {
            type: 'ajax',
            url: 'getParameterCheck.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    store.on('beforeload', function (store, options) {
        var new_params = {
            ruleName: nameField.getValue()
        };
        Ext.apply(store.proxy.extraParams, new_params);
    });
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: store,
        dock: 'bottom',
        // baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '规则名称',
        emptyText: '--请输入分规则名称--',
        labelWidth: 65,
        labelAlign: 'right',
        name: 'ruleName',
        width: '20%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var search_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        border: false,
        baseCls: 'customize_gray_back',
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            dock: 'top',
            border: false,
            items: [nameField, {
                text: '查询',
                textAlign: 'center',
                cls: 'Common_Btn',
                handler: function () {
                    pageBar.moveFirst();
                }
            }, {
                text: '清空',
                textAlign: 'center',
                cls: 'Common_Btn',
                handler: function () {
                    nameField.setValue();
                }
            }, {
                text: '增加',
                textAlign: 'center',
                cls: 'Common_Btn',
                handler: add
            }, {
                text: '保存',
                cls: 'Common_Btn',
                handler: saveParameterCheck
            }, {
                text: '删除',
                cls: 'Common_Btn',
                handler: deleteParameter
            }]
        }]
    });

    var paraColumn = [
        {
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        }, {
            text: 'iid',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        }, {
            text: '规则名称',
            dataIndex: 'ruleName',
            flex: 1,
            width: 150,
            editor: {
                allowBlank: true
            }
        }, {
            text: '验证规则',
            dataIndex: 'checkRule',
            flex: 1,
            width: 200,
            editor: {
                allowBlank: true
            }
        }, {
            text: '规则说明',
            dataIndex: 'ruleDes',
            width: 260,
            editor: {
                allowBlank: true
            }
        }];
    var paraCheckPanel = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        split: true,
        multiSelect: true,
        id: 'paraCheckPanel',
        emptyText: '没有分组',
        plugins: [cellEditing],
        padding: grid_space,
        width: 400,
        height: contentPanel.getHeight() - 35,
        cls: 'customize_panel_back',
        selModel: selModel,
        columnLines: true,
        ipageBaseCls: Ext.baseCSSPrefix
            + 'toolbar customize_toolbar',
        iqueryFun: function () {
            pageBar.moveFirst();
        },
        border: false,
        columns: paraColumn,
        store: store
    });


    var panel = Ext.create('Ext.panel.Panel', {
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        layout: 'border',
        header: false,
        border: false,
        items: [search_form, , paraCheckPanel],
        renderTo: "parametercheck"
    });
    contentPanel.on('resize', function () {
        panel.setHeight(contentPanel.getHeight() - modelHeigth);
        panel.setWidth(contentPanel.getWidth());
    });

    function add() {
        var store = paraCheckPanel.getStore();
        var p = {
            iid: -1,
            ruleName: '',
            checkRule: '',
            ruleDes: ''
        };
        store.insert(0, p);
        paraCheckPanel.getView().refresh();
    }

    /* 保存 */
    function saveParameterCheck() {
        // store.remove();
        var m = store.getModifiedRecords();
        if (m.length == 0) {
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var ruleName = m[i].get("ruleName").trim();
            var checkRule = m[i].get("checkRule").trim();
            var parameterDes = m[i].get("ruleDes");
            var iid = m[i].get("iid");
            if ("" == ruleName || null == ruleName) {
                setMessage('规则名称不能为空！');
                return;
            }
            if ("" == checkRule || null == checkRule) {
                setMessage('验证规则不能为空！');
                return;
            }
            if (i < m.length - 1) {
                for (var j = i + 1, len = m.length; j < len; j++) {
                    if (m[i].get("ruleName").trim() == m[j].get("ruleName").trim()) {
                        setMessage('分组名称' + ruleName + '有重复！');
                        return;
                    }
                }
            }
            if (fucCheckLength(ruleName) > 250) {
                setMessage('规则名称不能超过250字符！');
                return;
            }
            if (fucCheckLength(parameterDes) > 250) {
                setMessage('规则说明不能超过250字符！');
                return;
            }
            var isOk = false;
            // 检查分组名称是否有重复
            Ext.Ajax.request({
                url: 'checkParameterNameExist.do',
                method: 'POST',
                async: false,
                params: {
                    ruleName: ruleName,
                    iid: iid == '' || null == iid ? 0 : iid
                },
                success: function (response, request) {
                    isOk = Ext.decode(response.responseText).success;
                },
                failure: function (result, request) {
                    Ext.Msg.alert('提示', '保存失败！');
                }
            });

            if (isOk) {
                setMessage('分组名称' + ruleName + '有重复！');
                return;
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0)
                jsonData = jsonData + ss;
            else
                jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax
            .request({
                url: 'ParameterCheckSave.do',
                method: 'POST',
                params: {
                    jsonData: jsonData
                },
                success: function (response, request) {
                    var success = Ext.decode(response.responseText).success;
                    if (success) {
                        store.modified = [];
                        store.reload();
                        Ext.Msg.alert('提示', '保存成功');
                    } else {
                        Ext.Msg.alert('提示', '保存失败！');
                    }
                },
                failure: function (result, request) {
                    Ext.Msg.alert('提示', '保存失败！');
                }
            });
    }

    // 根据ID删除
    function deleteParameter() {
        var data = paraCheckPanel.getView().getSelectionModel()
            .getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg
                .confirm(
                    "请确认",
                    "是否真的要删除数据？",
                    function (button, text) {
                        if (button == "yes") {
                            var ids = [];
                            Ext.Array.each(data, function (
                                record) {
                                var paraIid = record.get('iid');
                                // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                                if (paraIid) {
                                    ids.push(paraIid);
                                } else {
                                    store.remove(record);
                                }
                            });

                            if (ids.length > 0) {
                                Ext.Ajax
                                    .request({
                                        url: 'parameterCheckDelete.do',
                                        params: {
                                            deleteIds: ids
                                                .join(',')
                                        },
                                        method: 'POST',
                                        success: function (
                                            response,
                                            opts) {
                                            var success = Ext
                                                .decode(response.responseText).success;
                                            // 当后台数据同步成功时
                                            if (success) {
                                                store
                                                    .reload();
                                                Ext.Msg
                                                    .alert(
                                                        '提示',
                                                        Ext
                                                            .decode(response.responseText).message);
                                            } else {
                                                Ext.Msg
                                                    .alert(
                                                        '提示',
                                                        '删除失败！');
                                            }
                                        }
                                    });
                            } else {
                                paraCheckPanel.getView().refresh();
                            }

                        }
                    });

        }

    }

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
});
