var fileCompareStroe;
var fileCompareGrid;

var cf_resultWindow;
Ext.onReady(function() {
// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.define('fileCompareModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'  ,type : 'long'}, 
		    {name : 'sourceip' ,type : 'string'},
		    {name : 'targetip' ,type : 'string'},
		    {name : 'isDf' ,type : 'string'},
		    {name : 'cftime' ,type : 'string'},
		    {name : 'context' ,type : 'string'}
	    ]
	});
	 fileCompareStroe = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'fileCompareModel',
		proxy : {
			type : 'ajax',
			url : 'filecompareList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var filecomareColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		},{
		    text : '比较时间',
		    dataIndex : 'cftime',
		    width : 90,flex:1
		},
		{
		    text : '源IP',
		    dataIndex : 'sourceip',
		    width : 90,flex:1
		}, 
		{
		    text : '目标IP',
		    dataIndex : 'targetip',
		    width : 90,flex:1
		}, 
		{
		    text : '是否相同',
		    dataIndex : 'isDf',
		    width : 90,flex:1
		}, /*
		{
			text : '类别名称',
		    dataIndex : 'bsName',
		  width : 200,flex:1,
		  editor: {allowBlank : false}
		},*/
		{ 
			text: '详请',  
			dataIndex: 'stepOperation',
			width:150,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid'); // 其实是requestID
				var isdf =  record.get('isDf');  
				if(isdf=='不同'){
					return '<span class="switch_span">'+
    			   	'<a href="javascript:void(0)" onclick="showcf_resultWindow('+iid+')">'+
    			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;详情'+
    			   	'</a>'+
    			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
				}else{
					return "";
				}
        		
			}
		}
	];
	// 分页工具
	var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: fileCompareStroe, 
        dock: 'bottom',
        displayInfo: true,
        afterPageText:' 页 共 {0} 页',
        beforePageText:'第 ',
        firstText:'第一页 ',
        prevText:'前一页',
        nextText:'下一页',
        lastText:'最后一页',
        refreshText:'刷新',
        displayMsg:'第{0}条 到 {1} 条数据  共找到{2}条记录',
        emptyMsg:'找不到任何记录'
	});

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	var selModel=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
	 fileCompareGrid = Ext.create('Ext.grid.Panel', {
		width : '100%',
	    height : contentPanel.getHeight() - 35,
	    store : fileCompareStroe,
//	    selModel : selModel,
	    plugins: [ cellEditing ],
	    bbar : pageBar,
	    border : false,
	    columnLines : true,
	    columns : filecomareColumns,
	    animCollapse : false,
	    dockedItems : [ {
	        xtype : 'toolbar',
	        items : ['->',{
	          text: '返回',
	          width:70,
	          height:30,
	          margin:'5',
	          textAlign:'center',
	          cls : 'Common_Btn',
	          handler : function() {
	    	    	contentPanel.getLoader().load({url: "scriptFileMonitor.do?cata=-1",scripts: true});
				}
	        }]
	      } ],
	    renderTo : "file_cpmpare_div"
	});
	 fileCompareStroe.on('beforeload', function (store, options) {
			var queryparams = {  
					flowid:flowid
			};
			Ext.apply(fileCompareStroe.proxy.extraParams, queryparams);
	    });
//	fileCompareGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
//		fileCompareGrid.down('#delete').setDisabled(selections.length === 0);
//	});

  
	/* 解决IE下trim问题 */
	String.prototype.trim=function(){
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function (){
		fileCompareGrid.setHeight (contentPanel.getHeight () - 25);
		fileCompareGrid.setWidth (contentPanel.getWidth () );
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(fileCompareGrid);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});
});

function showcf_resultWindow (bsManagerId)
{
	if (cf_resultWindow == undefined || !cf_resultWindow.isVisible ())
	{
		cf_resultWindow = Ext.create ('Ext.window.Window',
		{
		    title : '比较详情',
		    modal : true,
		    closeAction : 'destroy',
		    constrain : true,
		    autoScroll : true,
		    width : contentPanel.getWidth (),
		    height : contentPanel.getHeight (),
		    draggable : false,// 禁止拖动
		    resizable : false,// 禁止缩放
		    layout : 'fit',
		    loader :
		    {
		        url : 'cf_result_window.do',
		        params:{bsManagerId:bsManagerId},
		        autoLoad : true,
		        scripts : true
		    }
		});
	}
	cf_resultWindow.show ();
}
function setMessage(msg){
	Ext.Msg.alert('提示', msg);
}
