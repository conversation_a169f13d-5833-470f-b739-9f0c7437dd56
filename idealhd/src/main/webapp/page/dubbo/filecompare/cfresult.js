// 清理主面板的各种监听时间
Ext.onReady(function() {
	destroyRubbish();
	var resultsPanel = Ext.create('Ext.panel.Panel', {
		id : 'cf_result_panel',
		width : '100%',
		height : contentPanel.getHeight() - 35,
		renderTo : 'cf_result_div'/*
			    loader :
			    {
			        url : 'cf_result.do',
			        params:{bsManagerId:cfid},
			        autoLoad : true,
			        scripts : true
			    },*/
	});

	Ext.Ajax.request({
		url : 'filecompareResult.do',
		method : 'POST',
		params : {
			cfid : cfid
		},
		success : function(response, request) {
			var success = Ext.decode(response.responseText).success;
			var context = Ext.decode(response.responseText).context;
			Ext.getCmp('cf_result_panel').body.update(context);
			if (success) {
			} else {
				Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
			}
		},
		failure : function(result, request) {
			secureFilterRs(result, "操作失败！");
		}
	});

});
