//var DetailWinTi;
Ext.onReady(function () {
    destroyRubbish();

    var menuId = tempData.menuId;
    delete tempData;
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataData = [
        {"id": "-1", "name": "全部"},
        {"id": "sh", "name": "shell"},
        {"id": "bat", "name": "bat"},
        {"id": "py", "name": "python"},
        {"id": "perl", "name": "perl"},
        {"id": "sql", "name": "sql"},
        {"id": "ps1", "name": "powershell"}
    ];
    if (scriptServiceScriptFlowSwitch) {
        cataData.push({"id": "-2", "name": "组合"});
    }
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: cataData
    });
    var scriptFromStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        },
            {
                "id": "4",
                "name": "我的作业"
            },
            {
                "id": "2",
                "name": "共享作业"
            },
            {
                "id": "3",
                "name": "收藏作业"
            }]
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        width: '15%',
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '5',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 60,
        // columnWidth : .2,
        labelAlign: 'right',
        width: '15%',
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '5',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        emptyText: '-请选择一级分类-',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    /** 二级分类* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '5',
        labelWidth: 60,
        labelAlign: 'right',
        width: '15%',
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        padding: '5',
        labelWidth: 60,
        labelAlign: 'right',
        width: '15%',
        queryMode: 'local',
        fieldLabel: '脚本类型',
        hidden: true,
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '-请选择脚本类型-',
        store: cataStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    var centernameStore = Ext.create('Ext.data.JsonStore', {
        autoDestroy: true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getServiceName.do',
            reader: {
                type: 'json',
                root: 'itemNameList'
            }
        },
        fields: ['serviceName', 'serviceName']
    });
    centernameStore.on('beforeload',
        function (store, options) {
            var emSriptCb = (null != emScriptCb.getValue()) ? emScriptCb.getValue() : -1
            var new_params = {
                fromTable: search_form.getForm().findField("scriptFrom").getValue() || 1,
                fromType: 99,
                status: 1,
                isEMscript: emSriptCb,
                iappSysIds: appSysObj.getValue(),
                level: 2,
                onlyScript: 1
            };

            Ext.apply(centernameStore.proxy.extraParams, new_params);
        });
    var label = new Ext.form.TextField({
        name: 'label',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        //value : labelSwitch,
        hidden: !labelSwitch,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var sName = Ext.create('Ext.ux.ideal.form.ComboBox', {
        iname: 'serviceName',
        displayField: 'serviceName',
        valueField: 'serviceName',
        fieldLabel: '服务名称',
        labelWidth: 80,
        width: '20%',
        istore: centernameStore,
        labelAlign: 'right',
        forceSelection: false,
        emptyText: "--请选择服务名称--"
    });

    var scriptNameStore = Ext.create('Ext.data.JsonStore', {
        autoDestroy: true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getSscriptName.do',
            reader: {
                type: 'json',
                root: 'itemNameList'
            }
        },
        fields: ['scriptName', 'scriptName']
    });

    scriptNameStore.on('beforeload',
        function (store, options) {
            var emSriptCb = (null != emScriptCb.getValue()) ? emScriptCb.getValue() : -1
            var new_params = {
                fromTable: search_form.getForm().findField("scriptFrom").getValue() || 1,
                fromType: 99,
                status: 1,
                isEMscript: emSriptCb,
                iappSysIds: appSysObj.getValue(),
                level: 2,
                onlyScript: 1
            };

            Ext.apply(scriptNameStore.proxy.extraParams, new_params);
        });

    var scName = Ext.create('Ext.ux.ideal.form.ComboBox', {
        iname: 'scriptName',
        displayField: 'scriptName',
        valueField: 'scriptName',
        fieldLabel: '脚本名称',
        labelWidth: 80,
        width: '20%',
        istore: scriptNameStore,
        labelAlign: 'right',
        forceSelection: false,
        emptyText: "--请选择脚本名称--"
    });
//    var sName = new Ext.form.TextField({
//    	name: 'serviceName',
//    	fieldLabel: '服务名称',
//    	displayField: 'serverName',
//    	emptyText: '-请输入服务名称-',
//    	labelWidth: 60,
//    	padding: '5',
//    	labelAlign: 'right',
//    	width: '17.7%',
//    	listeners: {
//    		specialkey: function(field, e){
//    			if (e.getKey() == e.ENTER) {
//    				scriptservice_grid.ipage.moveFirst();
//    			}
//    		}
//    	}
//    });
    var scriptFrom = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptFrom',
        labelWidth: 60,
        labelAlign: 'right',
        width: '17%',
        queryMode: 'local',
        hidden: true,
        fieldLabel: '作业来源',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '-请选择作业来源-',
        store: scriptFromStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });
//    var scName = new Ext.form.TextField({
//        name: 'scriptName',
//        fieldLabel: '脚本名称',
//        displayField: 'scriptName',
//        emptyText: '--请输入脚本名称--',
//        labelWidth: 60,
//        padding: '5',
//        labelAlign: 'right',
//        width: '18%',
//        listeners: {
//            specialkey: function(field, e){
//                if (e.getKey() == e.ENTER) {
//                	scriptservice_grid.ipage.moveFirst();
//                }
//            }
//        }
//    });
    var emScriptStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "0", "name": "否"},
            {"id": "1", "name": "是"}
        ]
    });
    var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'emScript',
        labelWidth: 70,
        queryMode: 'local',
        hidden: !reviewSwitch,
        fieldLabel: '是否应急',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '-请选择是否应急-',
        store: emScriptStore,
        width: '17.3%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });
    Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var appSysObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'appSys',
        fieldLabel: '所属系统',
        hidden: !reviewSwitch,
        emptyText: '请选择所属系统',
        labelWidth: 60,
        labelAlign: 'right',
        width: '17%',
        store: appSysStore,
        padding: '0 0 5 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        mode: 'local',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    var keywords = new Ext.form.TextField({
        name: 'keywords',
        fieldLabel: '关键字',
        displayField: 'keywords',
        emptyText: '-请输入关键字-',
        value: filter_keywordsForWhite,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    var search_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        baseCls: 'customize_gray_back',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [appSysObj, emScriptCb]
        },
            {
                xtype: 'toolbar',
                dock: 'top',
                baseCls: 'customize_gray_back',
                border: false,
                items: [sName, scName, scriptTypeParam, groupNameCombo, bussCb, bussTypeCb, scriptFrom, keywords]
            },
            {
                xtype: 'toolbar',
                baseCls: 'customize_gray_back',
                dock: 'top',
                items: [label, {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        scriptservice_grid.ipage.moveFirst();
                    }
                },
                    {
                        xtype: 'button',
                        text: '清空',
                        cls: 'Common_Btn',
                        handler: function () {
                            clearQueryWhere();
                        }
                    }
                    , {
                        xtype: 'button',
                        text: '导出',
                        cls: 'Common_Btn',
                        hidden: true,
                        handler: function () {
                            exportExcel();
                        }
                    }, {
                        xtype: 'button',
                        text: '导入',
                        hidden: true,
                        cls: 'Common_Btn',
                        handler: function () {
                            importExcel();
                        }
                    }
                ]
            }]
    });

    Ext.define('scriptService', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
            {
                name: 'serviceName',
                type: 'string'
            },
            {
                name: 'scriptuuid',
                type: 'string'
            },
            {
                name: 'sysName',
                type: 'string'
            },
            {
                name: 'groupName',
                type: 'string'
            },
            {
                name: 'bussName',
                type: 'string'
            },
            {
                name: 'scriptType',
                type: 'string'
            },
            {
                name: 'scriptName',
                type: 'string'
            },
            {
                name: 'servicePara',
                type: 'string'
            },
            {
                name: 'platForm',
                type: 'string'
            },
            {
                name: 'keywords',
                type: 'string'
            },
            {
                name: 'status',
                type: 'int'
            },
            {
                name: 'content',
                type: 'string'
            },
            {
                name: 'version',
                type: 'string'
            },
            {
                name: 'timeout',
                type: 'int'
            },
            {
                name: 'bussId',
                type: 'int'
            },
            {
                name: 'scriptLevel',
                type: 'int'
            },
            {
                name: 'bussTypeId',
                type: 'int'
            },
            {
                name: 'label',
                type: 'string'
            },
            {
                name: 'fromTable',
                type: 'int'
            },
            {
                name: 'isCollected',
                type: 'int'
            },
            {
                name: 'isFlow',
                type: 'string'
            }, {
                name: 'isShare',
                type: 'int'
            },
            {
                name: 'createUserName',
                type: 'string'
            }, {name: 'isEMscript', type: 'string'},
            {name: 'iappSysIds', type: 'string'}]
    });

    var scriptservice_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptService',
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceListForTaskManage.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptservice_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            width: 170,
            minWidth: 170,
            flex: 1
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            width: 170
        },
        {
            text: '适用平台',
            dataIndex: 'platForm',
            width: 90
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            width: 90,
            hidden: !sdFunctionSortSwitch
        },
        {
            text: '一级分类',
            dataIndex: 'sysName',
            width: 90
        },
        {
            text: '二级分类',
            dataIndex: 'bussName',
            width: 90
        },
        {
            text: '标签',
            dataIndex: 'label',
            width: 100,
            hidden: !labelSwitch,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '脚本类型',
            dataIndex: 'scriptType',
            width: 70,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                } else if (value == "ps1") {
                    backValue = "powershell";
                }
                if (record.get('isFlow') == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        },
        {text: '创建人', dataIndex: 'createUserName', width: 100},
        {
            text: '版本',
            dataIndex: 'version',
            width: 70
        }, {
            text: '是否应急',
            dataIndex: 'isEMscript',
            hidden: !reviewSwitch,
            width: 75,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '否';
                } else if (value == 1) {
                    return '<font color="#F01024">是</font>';
                } else {
                    return '未知';
                }
            }
        },
        {
            text: '所属系统',
            dataIndex: 'iappSysIds',
            hidden: !reviewSwitch,
            width: 80,
            flex: 1
        },
        {
            text: '超时时间',
            dataIndex: 'timeout',
            hidden: true,
            width: 50
        },
        {
            text: 'uuid',
            dataIndex: 'scriptuuid',
            hidden: true,
            width: 50
        },
        {
            text: '风险级别',
            dataIndex: 'scriptLevel',
            hidden: true,
            width: 75,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 1) {
                    backValue = '<font color="#F01024">高级风险</font>';
                } else if (value == 2) {
                    backValue = '<font color="#FF7824">中级风险</font>';
                } else if (value == 3) {
                    backValue = '<font color="#FFA826">低级风险</font>';
                } else if (value == 0) {
                    backValue = '<font color="#FFA826">白名单</font>';
                }
                return backValue;
            }
        },
        {
            text: '来源',
            dataIndex: 'fromTable',
            hidden: true,
            width: 80,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 1) {
                    backValue = '我的作业';
                } else if (value == 2) {
                    backValue = '共享库';
                }
                return backValue;
            }
        },
        {

            text: '收藏',
            xtype: 'actioncolumn',
            hidden: true,
            width: 50,
            items: [{
                getClass: function (v, meta, rec) {
                    if (rec.get('isShare') == 1) {
                        if (rec.get('isCollected') == 0) { // 没有收藏
                            return 'uncollection-col';
                        } else { // 已经收藏
                            return 'collection-col';
                        }
                    }
                },
                getTip: function (v, meta, rec) {
                    if (rec.get('isShare') == 1) {
                        if (rec.get('isCollected') == 0) {
                            return '收藏';
                        } else {
                            return '取消收藏';
                        }
                    }
                },
                handler: function (grid, rowIndex, colIndex) {
                    var rec = grid.getStore().getAt(rowIndex);

                    Ext.Ajax.request({
                        url: 'collectScriptService.do',
                        method: 'POST',
                        params: {
                            iid: rec.get('iid')
                        },
                        success: function (response, request) {
                            scriptservice_store.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            }]
        },
        {
            text: '操作',
            xtype: 'actiontextcolumn',
            width: 180,
            align: 'center',
            items: [{
                text: '任务申请',
                iconCls: 'execute',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var platForm = grid.getStore().data.items[rowIndex].data.platForm;   //浦发需求
                    var timeout = grid.getStore().data.items[rowIndex].data.timeout;   // 邮储需求  脚本超时时间
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (isflow == '1') {
                        forwardScriptFlowExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, menuId);
                    } else {
                        if (noScriptConvertSwitch) {
                            forwardScriptExecAudiForTaskIndex(uuid, iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, menuId, platForm, timeout, label)
                        } else {
                            forwardScriptFlowExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, menuId);
                        }
                    }
                }
            }, {
                text: '查看',
                iconCls: 'script_search',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isflow;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (isflow == '1') {
                        viewDetailForTaskIndexForFlow(iid, serviceName, bussId, bussTypeId, scriptLevel, isflow, menuId);
                    } else {
                        if (noScriptConvertSwitch) {
                            viewDetailForTaskIndex(iid, label);
                        } else {
                            viewDetailForTaskIndexForFlow(iid, serviceName, bussId, bussTypeId, scriptLevel, isflow, menuId);
                        }
                    }
                }
            }]
        }
        //***************************************************
//    {
//        text: '操作',
//        dataIndex: 'xq',
//        width: 180,
//        align: 'center',
//        renderer: function(value, p, record) {
//            var iid = record.get('iid');
//            var status = record.get('status');
//            var isflow = record.get('isFlow');
//            var serviceName = record.get('serviceName');
//            var keywords = record.get('keywords');
//            var bussId = record.get('bussId');
//            var bussTypeId = record.get('bussTypeId');
//            var scriptLevel = record.get('scriptLevel');
//            var scriptType = record.get('scriptType');
//             var platForm = record.get('platForm'); //浦发需求
//            var execFuncName = "forwardScriptExecAudiForTaskIndex";
//            var viewDetailFuncName = "viewDetailForTaskIndex";
//            if (isflow == '1') {
//                execFuncName = "forwardScriptFlowExecAudi";
//                viewDetailFuncName = "viewDetailForTaskIndexForFlow";
//            }
//            var    flowCustomHy = '<span class="switch_span">'+
//   			   	'<a href="javascript:void(0)" onclick="customScriptFlowInTaskManager('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+',' + menuId+')">'+
//			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_template"></img>&nbsp;常用任务'+
//			   	'</a>'+
//			   '</span>';
//            if(noScriptConvertSwitch){
//            	return '<span class="switch_span">' +
//        		'<a href="javascript:void(0)" onclick="' + execFuncName + '(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ',\'' + scriptType+'\'' +','+ isflow+',' + menuId+',\''+platForm+'\')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;任务申请</a>' +
//        	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//        	   '<span class="switch_span">' +
//        		'<a href="javascript:void(0)" onclick="' + viewDetailFuncName + '(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ','+ isflow+',' + menuId + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;查看</a>' +
//        	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//
//            }else{
//            return '<span class="switch_span">' +
//            		'<a href="javascript:void(0)" onclick="forwardScriptFlowExecAudi(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ',\'' + scriptType+'\'' +','+ isflow+',' + menuId+ ' )" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;任务申请</a>' +
//            	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//            	   '<span class="switch_span">' +
//           		'<a href="javascript:void(0)" onclick="viewDetailForTaskIndexForFlow(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel+','+ isflow+',' + menuId + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_search"></img>&nbsp;查看</a>' +
//           	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//            }
//        }
//    }
    ];

    scriptservice_store.on('beforeload',
        function (store, options) {
            var emSriptCb = (null != emScriptCb.getValue()) ? emScriptCb.getValue() : -1
            var new_params = {
                serviceName: search_form.getForm().findField("serviceName").getValue(),
                bussId: bussCb.getValue() || 0,
                bussTypeId: search_form.getForm().findField("bussType").getValue() || 0,
                scriptName: search_form.getForm().findField("scriptName").getValue() || '',
                scriptType: search_form.getForm().findField("scriptTypeParam").getValue() || '',
                fromTable: search_form.getForm().findField("scriptFrom").getValue() || -1,
                keywords: search_form.getForm().findField("keywords").getValue(),
                fromType: 99,
                isEMscript: emSriptCb,
                iappSysIds: appSysObj.getValue(),
                status: 1,
                level: 2,
                onlyScript: 1,
                label: label.getValue(),
                groupName: groupNameCombo.getValue()
            };

            Ext.apply(scriptservice_store.proxy.extraParams, new_params);
        });

//    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//        store: scriptservice_store,
//        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//        dock: 'bottom',
//        displayInfo: true
//    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var scriptservice_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: scriptservice_store,
        cls: 'customize_panel_back',
        border: false,
        columnLines: true,
        columns: scriptservice_columns,
        selModel: selModel,
        padding: grid_space,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//        bbar: pageBar
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptexec_task_areaForWhite",
        layout: 'border',
        border: false,
//        bodyPadding: 5,
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        items: [search_form, scriptservice_grid]
    });

    function clearQueryWhere() {
        search_form.getForm().findField("sysName").setValue('');
        search_form.getForm().findField("bussType").setValue('');
        search_form.getForm().findField("scriptName").setValue('');
        search_form.getForm().findField("serviceName").setValue('');
        search_form.getForm().findField("scriptTypeParam").setValue('');
        search_form.getForm().findField("scriptFrom").setValue('');
        search_form.getForm().findField("appSys").setValue('');
        search_form.getForm().findField("emScript").setValue('-1');
        search_form.getForm().findField("keywords").setValue('');
        label.setValue('');
        bussTypeData.removeAll();
        groupNameCombo.setValue('');
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
    }

    // 将被选中的记录的flowid组织成json串，作为参数给后台处理
    function getSelectedJsonData(serviceState) {
        var flowIdList = scriptservice_grid.getSelectionModel().getSelection();
        if (flowIdList.length < 1) {
            return;
        }
        var jsonData = "[";
        var signNum = 0;
        for (var i = 0,
                 len = flowIdList.length; i < len; i++) {
            if (flowIdList[i].data.status == serviceState) {
                if (signNum == 0) {
                    jsonData = jsonData + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
                } else {
                    jsonData = jsonData + "," + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
                }
                signNum = signNum + 1;
            }
        }
        jsonData = jsonData + "]";
        return jsonData;
    }

    // 从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        var eValue = eval('jsonObj.' + key);
        return jsonObj['' + key + ''];
    }


    function forwardScriptExecAudiForTaskIndex(uuid, serviceId, a, b, c, scriptLevel, scriptType, isFlow, menuId, platmFrom, timeout, label) {
        /*
         * destroyRubbish(); //销毁本页垃圾 contentPanel.getLoader().load({ url:
         * "scriptExecStart.do", scripts: true, params : { serviceId:serviceId,
         * flag:1, from:2, url: 'scriptServicesTaskExec.do' } });
         */

        //    if (!auditingWin) {
        auditingWin = Ext.create('widget.window', {
            title: '执行审核',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() + 10,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForAuditing.do',
                params: {
                    iid: serviceId,
                    whiteScript: "whiteScript",
                    scriptType: scriptType,
                    scriptLevel: scriptLevel,
                    platmFrom: platmFrom,//适用平台
                    serviceName: a,
                    timeout: timeout,
                    uuid: uuid,
                    label: label
                },
                autoLoad: true,
                scripts: true
            }
        });
        //    }
        //    auditingWin.getLoader().load({});
        auditingWin.show();
    }

    contentPanel.on('resize',
        function () {
            mainPanel.setWidth(contentPanel.getWidth());
            mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
            if (auditingWin) {
                auditingWin.center();
            }
            // search_form.setWidth(contentPanel.getWidth()-300);
            // search_form.getForm().findField("serviceName").setWidth(contentPanel.getWidth()*0.3);
            // search_form.getForm().findField("status").setWidth(contentPanel.getWidth()*0.2);
        });

    //导出
    function exportExcel() {
        var record = scriptservice_grid.getSelectionModel().getSelection();
//		var instanceNames = [];
        var iidStr = "";
        Ext.Array.each(record, function (recordObj) {
            iidStr += "," + recordObj.get('iid');
//			instanceNames.push (iinstacneName);
        });
        if (iidStr.length <= 0) {
            Ext.Msg.alert('提示', '请选择要操作的行！');
            return;
        }
        iidStr = iidStr.substr(1);
//    	alert("iidStr="+iidStr);
        window.location.href = 'exportService.do?iidStr=' + iidStr;
    }

    function importExcel() {
        var uploadWindows;
        var uploadForm
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
                fieldLabel: '选择文件',
                labelWidth: 80,
                anchor: '90%',
//	    			labelAlign: 'right',
                margin: '10 10 0 40',
                buttonText: '浏览'
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    uploadWindows.close();
                }
            }]
        });
        uploadWindows = Ext.create('Ext.window.Window', {
            title: '任务导入',
            layout: 'fit',
            height: 140,
            width: 600,
            modal: true,
//	    		autoScroll : true,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            }
        });
        uploadWindows.show();

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("fileName").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        function uploadTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'importScriptForProduct.do',
                    success: function (form, action) {
                        var sumsg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', sumsg);
                        uploadWindows.close();
                        scriptservice_grid.ipage.moveFirst();
                        /*scriptServiceReleaseStore.reload();*/
                        return;
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
//	                     var mess = Ext.create('Ext.window.MessageBox', {
//	                     minHeight : 110,
//	                     minWidth : 500,
//	                     resizable : false
//	                   });
                        Ext.Msg.alert('提示', msg);
                        return;
                    }
                });
            }
        }
    }


    function viewDetailForTaskIndex(serviceId, label) {
//    if (!DetailWinTi) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForView.do',
                params: {
                    iid: serviceId,
                    flag: 1,
                    hideReturnBtn: 1,
                    label: label
                },
                autoLoad: true,
                scripts: true
            }
        });
//    }

//    DetailWinTi.getLoader().load({});
        DetailWinTi.show();
    }

    function viewDetailForTaskIndexForFlow(serviceId, serviceName, bussId, bussTypeId, scriptLevel, isFlow, menuId) {
//    if (!DetailWinTi) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() - 42,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'flowCustomizedInitScriptService.do',
                params: {
                    serviceId: serviceId,
                    iid: serviceId,
                    menuId: menuId,
                    status: 0,
                    serviceName: serviceName,
                    actionType: 'view',
                    bussId: bussId,
                    bussTypeId: bussTypeId,
                    submitType: 'tv',
                    flag: 0,
                    windowHeight: contentPanel.getHeight() - 42,
                    rootspace: 'view',
                    isShowInWindow: 1,
                    isScriptConvertToFlow: isFlow != '1'
                },


//			url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
//			params: {
//				iid:serviceId,
//				serviceName:serviceName,
//				actionType:'view',
//				bussId:bussId,
//				bussTypeId:bussTypeId,
//				flag:0,
//				isShowInWindow: 1
//			},
                autoLoad: true,
                scripts: true
            }
        });
//    }

//    DetailWinTi.getLoader().load({});
        DetailWinTi.show();
    }


    function forwardScriptFlowExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isFlow, menuId) {
        // alert(iid);
        destroyRubbish(); // 销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptServiceGFSSAUDI.do',
            params: {
                iid: iid,
                serviceName: serviceName,
                menuId: menuId,
                actionType: 'audi',
                bussId: bussId,
                bussTypeId: bussTypeId,
                scriptType: scriptType,
                scriptLevel: scriptLevel,
                submitType: 'ea',
                ifrom: 'scriptServicesTaskExecForWhite.do',
                flag: 1,
                isScriptConvertToFlow: isFlow != '1'
            },
            scripts: true
        });
    }


});

