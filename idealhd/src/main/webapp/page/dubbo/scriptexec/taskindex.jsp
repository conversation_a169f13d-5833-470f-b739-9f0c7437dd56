<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ConfigReader"%>
<%
	ConfigReader cr = ConfigReader.getInstance();
	boolean useTimesswitch = cr.getBooleanProperties(Environment.SS_SERVICEMANAGE_USETIMESWITCH, false);
	boolean winTimesswitch = cr.getBooleanProperties(Environment.SS_SERVICEMANAGE_WINTIMESSWITCH, false);
	boolean isHideTaskFrom = Environment.getInstance().getScriptHideTaskFromSwitch();
	boolean isHideCustom = Environment.getInstance().getScriptHideCustomSwitch();
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	boolean pageStyleSwitch = Environment.getInstance().getPageStyleSwitch();
 	boolean usageTimesSwitch = Environment.getInstance().getUsageTimesSwitch(); 
 	boolean scriptFlowSwitch = Environment.getInstance().getScriptFlowSwitch();
 	boolean bussCbSwitch = Environment.getInstance().getBussCbSwitch();
 	boolean showCollectSwitch = Environment.getInstance().getScriptTaskApplyShowCollectSwitch();
 	boolean topInformationSwitch = Environment.getInstance().getScriptserviceTopInformationSwitch();
 	boolean scriptPlatFromSwitch = Environment.getInstance().getScriptPlatFromSwitch();
	//浙商 投产人 开关
 	boolean scriptProductUserSwitch = Environment.getInstance().getScriptProductUserSwitch();
	//浙商 专有修改人 专有创建人开关
	boolean scriptCreateUserSwitch = Environment.getInstance().getScriptCreateUserSwitch();
	//标签
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	//bankCode001 脚本目录
	boolean gfScriptDirFunctionSwitch = Environment.getInstance().getGFScriptDirFunctionSwitch();
	//功能分类
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
	// 福建农信CI
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
%>
<html>
<head>
<style type="text/css">
.img_cursor .x-action-col-icon{
	width:16px;
	height:16px;
	cursor:default;
}
.img_cursor img{
}
</style>
<script type="text/javascript">
var tempData = {};
<%
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
var isHideTaskFrom = <%=isHideTaskFrom%>;
var isHideCustom = <%=isHideCustom%>;
var myTodoForserviceName =tempData.myTodoForserviceName;
var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
var showCollectSwitch = <%=showCollectSwitch%>;
var pageStyleSwitch = <%=pageStyleSwitch%>;
var usageTimesSwitch = <%=usageTimesSwitch%>;
var scriptFlowSwitch = <%=scriptFlowSwitch%>;
var bussCbSwitch = <%=bussCbSwitch%>;
var scriptPlatFromSwitch = <%=scriptPlatFromSwitch%>;
var topInformationSwitch = <%=topInformationSwitch%>;//顶部信息统计开关
var useTimesswitch = <%=useTimesswitch%>;
var winTimesswitch = <%=winTimesswitch%>;
var gfScriptDirFunctionSwitch = <%=gfScriptDirFunctionSwitch%>;

//脚本看板 跳转使用 
var requestFromC3CharForTaskIndex =  tempData.requestFromC3Char ==null ?false:tempData.requestFromC3Char;
var scriptIIDForTaskIndex  = tempData.scriptIID == null ? 0:tempData.scriptIID;
//脚本看板 跳转使用 

var filter_serverNameQuery = '<%=request.getParameter("filter_serverNameQuery")==null?"":request.getParameter("filter_serverNameQuery")%>';
var filter_scriptNameQuery  = '<%=request.getParameter("filter_scriptNameQuery")==null?"":request.getParameter("filter_scriptNameQuery")%>';
var filter_keywords  = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
var BStypeSize = '<%=request.getAttribute("BStypeSize")%>';
//浙商 投产人 开关
var scriptProductUserSwitch = <%=scriptProductUserSwitch%>;
//浙商 专有修改人 专有创建人开关
var scriptCreateUserSwitch = <%=scriptCreateUserSwitch%>;
var labelSwitch = <%=sdScriptLabelEditSwitch%>;
var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
//是否具有高权（判断是否为值班任务申请、值班人员）
var heightPermissionFlag=<%=request.getAttribute("heightPermissionFlag") == null ? false : request.getAttribute("heightPermissionFlag")%>
var fjnxCISwitch = <%=fjnxCISwitch%>
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptexec/taskindex.js"></script>
</head>
<body>
<div id="scriptexec_task_area" style="width: 100%;height: 100%">
</div>
</body>
</html>