<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script>
    var serviceId_fortest = '<%=request.getParameter("serviceId")%>';
    var url_fortest = '<%=request.getParameter("url")%>';
    var flag_fortest = '<%=request.getParameter("flag")%>'; // 0:测试     1:生成
	var from_fortest =<%=request.getParameter("from")%>	== null ? 1	:<%=request.getParameter("from")%>	; // 0:测试     1:生成
	var level_fortest = '<%=request.getAttribute("level") == null ? "3" : request.getAttribute("level")%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptexec/script_exec_start_fortest.js"></script>
</head>
<body>
	<table cellpadding="0" cellspacing="0" border="0" class="Script_table">
		<tr class="Script_tr1">
			<td class="Script_td1">脚本名称：</td>
			<td class="Script_td2">
				<div class="Sc_Leftcn"><%=request.getAttribute("serviceName") == null ? "" : request.getAttribute("serviceName")%></div>
				<div id="showMore_fortest" class="Blue_button"
					style="display: inline-block; float: right">更多</div>
				<div id="backto_fortest" class="Red_button"
					style="display: inline-block; float: right;margin-right:10px;">返回</div>
				<div id="execservice_fortest" class="Blue_button"
					style="display: inline-block; float: right;margin-right:5px;">
					<%="0".equals(String.valueOf(request.getParameter("flag"))) ? "执行脚本" : "执行服务"%></div>
					
				<span id="ipd_fortest"
				style="display: none;"><%=request.getAttribute("inputParamDesc") == null ? "" : request.getAttribute("inputParamDesc")%></span>
				<span id="fd_fortest" style="display: none;"><%=request.getAttribute("funcDesc") == null ? "" : request.getAttribute("funcDesc")%></span>
			</td>
		</tr>
		<tr class="Script_tr1">
			<td class="Script_td1">适用平台：</td>
			<td class="Script_td2">
				<div class="Sc_Leftcn"><%=request.getAttribute("platForm") == null ? "" : request.getAttribute("platForm")%></div>
			</td>
		</tr>
		<tr class="Script_tr1">
			<td class="Script_td1">当前版本：</td>
			<td class="Script_td2">
				<div class="Sc_Leftcn">
					<%=request.getAttribute("version") == null ? "无" : request.getAttribute("version")%>
				</div>
			</td>
		</tr>
		<!-- <tr>
			<td class="Script_td1">版本信息：</td>
			<td id="switch-version">
			</td>
		</tr> -->
		<tr class="Script_tr1">
    	<td class="Script_td1">执行用户：</td>
        <td class="Script_td2">
        	<input type="text" class="Script_text" name="execUser" id="execUser_fortest" value=''/>
        </td>
   		</tr>
		<tr class="Script_tr1">
			<td class="Script_td1">目标机器：</td>
			<td class="Script_td2">
				<div id="select-agent_fortest" class="Blue_button"
					style="display: inline-block;">选择服务器</div>
				<div id="clear-agent_fortest" class="Clean_IP" style="display: none">清空IP</div>
			</td>
		</tr>
		<tr>
			<td class="Script_td1"></td>
			<td class=""><div id="chosed-agent_fortest" style="padding: 0 5px;"></div></td>
		</tr>
		<%-- <tr class="Script_tr1">
			<td class="Script_td1">脚本参数：</td>
			<td class="Script_td2"><input type="text" class="Script_text"
				name="scriptPara"
				value='<%=request.getAttribute("servicePara") == null ? "" : request.getAttribute("servicePara")%>' /></td>
		</tr> --%>
		<tr>
			<td class="Script_td1" valign="top" >脚本参数：</td>
			<td class=""><div id="script-param-start_fortest" style="padding: 0 5px;"></div></td>
		</tr>
		<tr>
			<td class="Script_td1" valign="top" >脚本附件：</td>
			<td class=""><div id="script-attachment-start_fortest" style="padding: 0 5px;"></div></td>
		</tr>

		<tr class="Script_tr1" >
			<td class="Script_td1" valign="top" >脚本内容：</td>
			<td class="Script_td2">
				<div id="scriptcontent" class="entry-content Script_Content">
					<pre class="prettyprint linenums"> <%=request.getAttribute("scriptContent") == null ? "" : request.getAttribute("scriptContent")%>
					</pre>
				</div>
			</td>
		</tr>
		<%-- <tr class="Script_tr1">
			<td class="Script_td1"></td>
			<td align="center">
				<div id="execservice" class="Blue_button"
					style="display: inline-block">
					执行<%="0".equals(String.valueOf(request.getParameter("flag"))) ? "脚本" : "服务"%></div>
				<div id="backto" class="Red_button"
					style="display: inline-block; width: 60px;">返回</div>
			</td>
		</tr> --%>
	</table>
</body>
</html>