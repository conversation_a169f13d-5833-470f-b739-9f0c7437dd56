Ext.onReady(function () {
    //get
    destroyRubbish();
    if(heightPermissionFlag){
        contentPanel.setTitle("值班任务申请");
    }else{
        contentPanel.setTitle("任务申请");
    }

    var myTodoForserviceNameNoCount = 0;
    var itemsPerPage = 30;
    var menuId = tempData.menuId;
    var iidArray = new Array();
    delete tempData;
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: sdFunctionSortSwitch,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataData = [
        {"id": "-1", "name": "全部"},
        {"id": "sh", "name": "shell"},
        {"id": "bat", "name": "bat"},
        {"id": "py", "name": "python"},
        {"id": "perl", "name": "perl"},
        {"id": "sql", "name": "sql"},
        {"id": "ps1", "name": "powershell"}
    ];
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: cataData
    });
    var scriptFromStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        },
            {
                "id": "4",
                "name": "我的作业"
            },
            {
                "id": "2",
                "name": "共享作业"
            },
            {
                "id": "3",
                "name": "收藏作业"
            }]
    });
    var label = new Ext.form.TextField({
        name: 'label',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        //value : labelSwitch,
        hidden: !labelSwitch,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '17%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '0 5 0 0',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 60,
        // columnWidth : .2,
        labelAlign: 'right',
        width: reviewSwitch ? '16.7%' : '16.7%',
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '5',
        hidden: bussCbSwitch,
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '-请选择一级分类-',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });


    var pagesizeStart = 0;
    var pagesizelimit = 6;

    var topInformationbutSwitch = true;
    if (BStypeSize > 6 && !topInformationSwitch) {
        topInformationbutSwitch = false;
    }

    /** 二级分类* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '5',
        labelWidth: 60,
        labelAlign: 'right',
//        width: reviewSwitch?'16.7%':'16.7%',
        width: '16.7%',
        queryMode: 'local',
        fieldLabel: '二级分类',
        hidden: bussCbSwitch,
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '-请选择二级分类-',
        store: bussTypeData,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            },
            change: function () { // old is keyup
                threeBussTypeCb.clearValue();
                threeBussTypeCb.applyEmptyText();
                threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (!Ext.isEmpty(this.value)) {
                    threeBussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    //北京邮储 三级分类
    var threeBussTypeData = Ext.create('Ext.data.Store', {
        fields: ['threeBsTypeId', 'threeBsTypeName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getThreeBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    /*threeBussTypeData.on('load', function(store, options) {
        if(threeBsTypeId) {
            threeBussTypeCb.setValue(threeBsTypeId);
        }
    });*/
    var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'threeBussTypeCb',
        labelWidth: 60,
        padding: '5',
        queryMode: 'local',
        fieldLabel: '三级分类',
        displayField: 'threeBsTypeName',
        valueField: 'threeBsTypeId',
        editable: true,
        width: '16.7%',
        labelAlign: 'right',
        hidden: bussCbSwitch || !scriptThreeBstypeSwitch,
        emptyText: '--请选择三级分类--',
        store: threeBussTypeData,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    usePlantFormStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: "0"
        };
        Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
    });
    var platFromCombobox = Ext.create('Ext.form.field.ComboBox', {
        name: 'platFromCombobox',
        labelWidth: 60,
        // columnWidth : .2,
        labelAlign: 'right',
        width: '16.7%',
        queryMode: 'local',
        fieldLabel: '适用平台',
        hidden: scriptPlatFromSwitch,
        padding: '5',
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: true,
        emptyText: '-请选择适用平台-',
        store: usePlantFormStore,
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        padding: '5',
        labelWidth: 60,
        labelAlign: 'right',
        width: '16.7%',
        queryMode: 'local',
        fieldLabel: '脚本类型',
        // hidden: reviewSwitch,//bankCode001不显示
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '-请选择脚本类型-',
        store: cataStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });

    var createUser = new Ext.form.TextField({
        name: 'createUser',
        fieldLabel: '创建人',
        emptyText: '-请输入创建人-',
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '16.7%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });

    var sName = new Ext.form.TextField({
        name: 'serviceName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '-请输入服务名称-',
        value: filter_serverNameQuery,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '16.7%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });

    var keywords = new Ext.form.TextField({
        name: 'keywords',
        fieldLabel: '关键字',
        displayField: 'keywords',
        emptyText: '-请输入关键字-',
        value: filter_keywords,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '16.7%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });

    var scriptFrom = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptFrom',
        labelWidth: (!isHideTaskFrom && reviewSwitch) ? 72 : 65,
        labelAlign: 'right',
//        width: (!isHideTaskFrom && !reviewSwitch) ?'16.7%':'16.7%',
        width: '16.7%',
        queryMode: 'local',
        fieldLabel: '任务来源',
        displayField: 'name',
        valueField: 'id',
        hidden: isHideTaskFrom || heightPermissionFlag,
        editable: false,
        emptyText: '-请选择任务来源-',
        store: scriptFromStore,
        value: "-1",
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        //hidden:true,
        emptyText: '-请输入脚本名称-',
        value: filter_scriptNameQuery,
        labelWidth: 60,
        padding: '5',
        labelAlign: 'right',
        width: '16.7%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });
    var emScriptStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "0", "name": "否"},
            {"id": "1", "name": "是"}
        ]
    });
    var isShowVersion = Ext.create('Ext.form.field.Checkbox', {
        checked: true,
        boxLabel: '最新版本',
        name: 'newVersion',
        id: 'newVersion',
        labelAlign: 'right'
    });
    var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'emScript',
        labelWidth: 65,
        queryMode: 'local',
        hidden: !reviewSwitch,
        fieldLabel: '是否应急',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '-请选择是否应急-',
        store: emScriptStore,
        width: '16.7%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });
    var isScriptStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            {"id": "-1", "name": "全部"},
            {"id": "0", "name": "否"},
            {"id": "1", "name": "是"}
        ]
    });
    var filter_scriptStatus = 0;
    if (reviewSwitch) {
        filter_scriptStatus = -1;
    }
    var isFlowScript = Ext.create('Ext.form.field.ComboBox', {
        name: 'isFlowScript',
        labelWidth: 65,
        queryMode: 'local',
//		hidden : reviewSwitch,
        fieldLabel: '组合',
        displayField: 'name',
        valueField: 'id',
        hidden: !scriptFlowSwitch,
        editable: false,
        emptyText: '-请选择是否组合-',
        store: isScriptStore,
        width: '16.7%',
        labelAlign: 'right',
        listeners: {
            afterRender: function (combo) {
                if (filter_scriptStatus == '-1') {
                    combo.setValue(isScriptStore.getAt(0).data.id);
                } else if (filter_scriptStatus == '0') {
                    combo.setValue(isScriptStore.getAt(1).data.id);
                } else if (filter_scriptStatus == '1') {
                    combo.setValue(isScriptStore.getAt(2).data.id);
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });
    Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
            {
                name: 'name',
                type: 'string'
            }]
    });
    var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: reviewSwitch,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var appSysObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'appSys',
        fieldLabel: '所属系统',
        hidden: !reviewSwitch,
        emptyText: '-请选择所属系统-',
        labelWidth: 60,
        labelAlign: 'right',
        width: '16.7%',
        store: appSysStore,
        padding: '5',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable: true,
        queryModel: 'local',
        mode: 'local',
        listeners: {
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                    BSinfoloader();
                }
            }
        }
    });

    var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        iqueryFun: function () {
            var filter_scriptStatus = isFlowScript.getValue();
            scriptservice_grid.ipage.moveFirst();
            BSinfoloader();
        },
        baseCls: 'customize_gray_back',
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            baseCls: 'customize_gray_back',
            dock: 'top',
            items: [sName, scName, scriptTypeParam, createUser, keywords, platFromCombobox
            ]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            baseCls: 'customize_gray_back',
            border: false,
            items: [groupNameCombo, bussCb, bussTypeCb, threeBussTypeCb, isFlowScript, scriptFrom, appSysObj, emScriptCb, label]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            baseCls: 'customize_gray_back',
            border: false,
            items: ['->', isShowVersion,
                {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        var filter_scriptStatus = isFlowScript.getValue();
                        scriptservice_grid.ipage.moveFirst();
                        BSinfoloader();
                    }
                },
                {
                    xtype: 'button',
                    text: '清空',
                    cls: 'Common_Btn',
                    handler: function () {
                        clearQueryWhere();
                    }
                },
                {
                    text: '投产介质',
                    cls: 'Common_Btn',
                    hidden: fjnxCISwitch,
                    handler: function () {
                        if (iidArray.length == 0) {
                            Ext.MessageBox.alert("提示", "请选择要导出的任务！");
                            return;
                        } else {    	//window.location.href = 'exportServiceJsonFileForProduction.do?iidStr[]='+iidStr+'&exportType=1';

                            $.fileDownload('exportServiceJsonFileForProduction.do', {
                                httpMethod: 'POST',
                                traditional: true,
                                data: {
                                    iidStr: iidArray,
                                    exportType: "1"
                                },
                                successCallback: function (url) {

                                },
                                failCallback: function (html, url) {
                                    Ext.Msg.alert('提示', '导出失败！');
                                    return;
                                }
                            });
                        }
                    }
                }, {
                    xtype: 'button',
                    text: '下载脚本',
                    cls: 'Common_Btn',
                    hidden: fjnxCISwitch,
                    handler: function () {
                        downloadScript();
                    }
                }, {
                    xtype: 'button',
                    text: '导出',
                    cls: 'Common_Btn',
                    hidden: fjnxCISwitch,
                    handler: function () {
                        exportExcel();
                    }
                }, {
                    xtype: 'button',
                    text: '导入',
                    cls: 'Common_Btn',
                    hidden: true,
                    handler: function () {
                        importExcel();
                    }
                }, {
                    xtype: 'button',
                    text: '返回',
                    cls: 'Common_Btn',
                    hidden: !requestFromC3CharForTaskIndex,
                    handler: function () {
                        popNewTab('脚本看板', 'pandect1.do', {}, 10, true);
                    }
                }
            ]
        }]
    });

    Ext.define('scriptService', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
            {
                name: 'scriptuuid',
                type: 'string'
            },
            {
                name: 'serviceName',
                type: 'string'
            },
            {
                name: 'groupName',
                type: 'string'
            },
            {
                name: 'keywords',
                type: 'string'
            },
            {
                name: 'sysName',
                type: 'string'
            },
            {
                name: 'bussName',
                type: 'string'
            }, {name: 'threeBsTypeName', type: 'string'},
            {name: 'label', type: 'string'},
            {
                name: 'scriptType',
                type: 'string'
            },
            {
                name: 'scriptName',
                type: 'string'
            },
            {
                name: 'servicePara',
                type: 'string'
            },
            {
                name: 'platForm',
                type: 'string'
            },
            {
                name: 'execUser',
                type: 'string'
            },
            {
                name: 'status',
                type: 'int'
            },
            {
                name: 'content',
                type: 'string'
            },
            {
                name: 'version',
                type: 'string'
            },
            {
                name: 'timeout',
                type: 'int'
            },
            {
                name: 'bussId',
                type: 'int'
            },
            {
                name: 'scriptLevel',
                type: 'int'
            },
            {
                name: 'bussTypeId',
                type: 'int'
            },
            {
                name: 'fromTable',
                type: 'int'
            },
            {
                name: 'isCollected',
                type: 'int'
            }, {
                name: 'isShare',
                type: 'int'
            }, {name: 'useTimes', type: 'string'},
            {name: 'winTimes', type: 'string'},
            {
                name: 'isFlow',
                type: 'string'
            },
            {
                name: 'createUserName',
                type: 'string'
            }, {name: 'isEMscript', type: 'string'},
            {name: 'iappSysIds', type: 'string'},
            {name: 'ifuncdesc', type: 'string'},
            {name: 'createUserNameForCz', type: 'string'},//浙商创建人
            {name: 'updateUserName', type: 'string'},//浙商修改人
        ]
    });

    var scriptservice_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: itemsPerPage,
        remoteSort: true,
        model: 'scriptService',
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceListForTaskManage.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptservice_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            width: 180
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            //hidden: true,
//    	renderer:function(value,p,record){
//        	var backValue = "";
//        	if(value!='' && record.get('scriptType')!=''){
//        		backValue = value+"."+record.get('scriptType');
//        	}else{
//        		backValue= value;
//        	}
//        	return backValue;
//        },
            renderer: function (value, metadata, record) {
                var a = record.data.ifuncdesc;
                metadata.tdAttr = 'data-qtip="' + a + '"';
                return value;
            },
            width: 150,
            flex: 1
        }, {
            text: '启动用户',
            dataIndex: 'execUser',
            hidden: true,
            width: 100
        },
        {
            text: '适用平台',
            dataIndex: 'platForm',
            //hidden: true,
            width: 90
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            hidden: !sdFunctionSortSwitch,
            width: 90
        },
        {
            text: '一级分类',
            dataIndex: 'sysName',
            hidden: pageStyleSwitch,
            width: 90
        },
        {
            text: '二级分类',
            dataIndex: 'bussName',
            hidden: pageStyleSwitch,
            width: 90
        }, {
            text: '三级分类',
            dataIndex: 'threeBsTypeName',
            hidden: pageStyleSwitch || !scriptThreeBstypeSwitch,
            width: 90
        }, {
            text: '标签',
            dataIndex: 'label',
            hidden: !labelSwitch,
            width: 100,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '脚本类型',
            dataIndex: 'scriptType',
            hidden: true,
            width: 100,
            //hidden: true,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                } else if (value == "ps1") {
                    backValue = "powershell";
                }
                if (record.get('isFlow') == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        },
//    {
//		text : '使用次数',
//		//hidden : true,
//		hidden: useTimesswitch,//usageTimesSwitch
//		dataIndex : 'useTimes',
//		width : 80
//	},
//	{
//		text : '成功率',
//		dataIndex : 'winTimes',
//		hidden :winTimesswitch ,//true
//		width : 80
//	},
        {
            text: '成功率',
            width: 60,
            hidden: winTimesswitch,
            renderer: function (value, metaData, record, rowNum) {
                var displayValue;
                displayValue = "<button  style=\"text-align:center; buttonAlign: 'center';margin-left:3px;\" class=\"dbsourBtn\" type=\"button\">查看</button>";
                return displayValue;
            },
            listeners: {
                click: function (a, b, c, d, e, record) {
                    var iid = record.get('iid');
                    showTotalAndTotalrate(iid);
                }
            }
        },
        {
            text: '投产人',
            dataIndex: 'createUserName',
            hidden: !scriptProductUserSwitch,//浙商开启开关true后 展示
            width: 100
        },
        {text: '创建人', dataIndex: 'createUserName', hidden: scriptCreateUserSwitch, width: 100},
        {
            text: '创建者',
            dataIndex: 'createUserNameForCz',
            hidden: !scriptCreateUserSwitch,//浙商开启开关true后 展示
            width: 100
        },
        {
            text: '修改人',
            dataIndex: 'updateUserName',
            hidden: !scriptCreateUserSwitch,//浙商开启开关true后 展示
            width: 100
        },
        {
            text: '版本',
            dataIndex: 'version',
            width: 70
        }, {
            text: '是否应急',
            dataIndex: 'isEMscript',
            hidden: !reviewSwitch,
            width: 75,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '否';
                } else if (value == 1) {
                    return '<font color="#F01024">是</font>';
                } else {
                    return '未知';
                }
            }
        },
        {
            text: '所属系统',
            dataIndex: 'iappSysIds',
            hidden: !reviewSwitch,
            width: 70
        },
        {
            text: '超时时间',
            dataIndex: 'timeout',
            hidden: true,
            width: 50
        },
        {
            text: '风险级别',
            dataIndex: 'scriptLevel',
            width: 75,
            hidden: fjnxCISwitch,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 1) {
                    backValue = '<font color="#F01024">高级风险</font>';
                } else if (value == 2) {
                    backValue = '<font color="#FF7824">中级风险</font>';
                } else if (value == 3) {
                    backValue = '<font color="#FFA826">低级风险</font>';
                } else if (value == 0) {
                    backValue = '<font color="#8AD329">白名单</font>';
                }
                return backValue;
            }
        },
        {
            text: '来源',
            dataIndex: 'fromTable',
            width: 80,
            hidden: isHideTaskFrom,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 1) {
                    backValue = '我的作业';
                } else if (value == 2) {
                    backValue = '共享库';
                }
                return backValue;
            }
        },
        {

            text: '收藏',
            xtype: 'actioncolumn',
            width: 50,
            hidden: !showCollectSwitch,
            items: [{
                getClass: function (v, meta, rec) {

                    if (rec.get('isCollected') == 0) { // 没有收藏
                        return 'uncollection-col';
                    } else { // 已经收藏
                        return 'collection-col';
                    }

                },
                getTip: function (v, meta, rec) {

                    if (rec.get('isCollected') == 0) {
                        return '收藏';
                    } else {
                        return '取消收藏';
                    }

                },
                handler: function (grid, rowIndex, colIndex) {
                    var rec = grid.getStore().getAt(rowIndex);

                    Ext.Ajax.request({
                        url: 'collectScriptService.do',
                        method: 'POST',
                        params: {
                            scriptuuid: rec.get('scriptuuid')
                        },
                        success: function (response, request) {
                            scriptservice_store.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });

                }
            }]
        },
        {
            text: '共享状态',
            dataIndex: 'isShare',
            hidden: true,
            width: 80,
            renderer: function (value, p, record, rowIndex) {
                if (value == 0) {
                    return '<font color="">未共享</font>';
                } else if (value == 1) {
                    return '<font color="#0CBF47">已共享</font>';
                } else {
                    return '<font color="#CCCCCC">未知</font>';
                }
            }
        },
        {
            text: 'uuid',
            dataIndex: 'scriptuuid',
            hidden: true,
            width: 90
        },
        {
            text: '操作',
            xtype: 'actiontextcolumn',
            width: isHideCustom ? 200 : 280,
            align: 'center',
            items: [{
                text: '任务申请',
                iconCls: 'execute',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isFlow;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var scriptName = grid.getStore().data.items[rowIndex].data.scriptName; //浦发
                    var newTitle = serviceName + ' — ' + scriptName;//浦发需求
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var suUser = grid.getStore().data.items[rowIndex].data.execUser;
                    var platForm = grid.getStore().data.items[rowIndex].data.platForm;   //浦发需求
                    var timeout = grid.getStore().data.items[rowIndex].data.timeout;   // 邮储需求  脚本超时时间
                    var filter_serverNameQuery = search_form.getForm().findField("serviceName").getValue() == null ? '' : search_form.getForm().findField("serviceName").getValue();
                    var filter_scriptNameQuery = search_form.getForm().findField("scriptName").getValue() == null ? '' : search_form.getForm().findField("scriptName").getValue();
                    var filter_keywords = search_form.getForm().findField("keywords").getValue() == null ? '' : search_form.getForm().findField("keywords").getValue();
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (isflow == '1') {
                        forwardScriptFlowExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, menuId, newTitle, platForm, suUser, filter_serverNameQuery, filter_scriptNameQuery, filter_keywords);
                    } else {
                        if (noScriptConvertSwitch) {

                            forwardScriptExecAudiForTaskIndex(uuid, iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, menuId, newTitle, platForm, timeout, label);
                        } else {
                            forwardScriptFlowExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, menuId, newTitle, platForm, suUser, filter_serverNameQuery, filter_scriptNameQuery, filter_keywords);
                        }
                    }
                }
            }, {
                text: '查看',
                iconCls: 'execute',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var isflow = grid.getStore().data.items[rowIndex].data.isFlow;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (isflow == '1') {
                        viewDetailForTaskIndexForFlow(iid, serviceName, bussId, bussTypeId, scriptLevel, isflow, menuId);
                    } else {
                        if (noScriptConvertSwitch) {
                            viewDetailForTaskIndex(iid, label);
                        } else {
                            viewDetailForTaskIndexForFlow(iid, serviceName, bussId, bussTypeId, scriptLevel, isflow, menuId);
                        }
                    }
                }
            }, {
                text: '常用任务',
                iconCls: 'script_template',
                getClass: function (v, metadata, record) {
                    //值班任务申请隐藏常用任务按钮
                    if (isHideCustom || heightPermissionFlag) {
                        return 'x-hidden';
                    }
                },
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var status = grid.getStore().data.items[rowIndex].data.status;
                    var isflow = grid.getStore().data.items[rowIndex].data.isFlow;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var bussId = grid.getStore().data.items[rowIndex].data.bussId;
                    var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
                    var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var platForm = grid.getStore().data.items[rowIndex].data.platForm;   //浦发需求
                    var filter_serverNameQuery = search_form.getForm().findField("serviceName").getValue() == null ? '' : search_form.getForm().findField("serviceName").getValue();
                    var filter_scriptNameQuery = search_form.getForm().findField("scriptName").getValue() == null ? '' : search_form.getForm().findField("scriptName").getValue();
                    var filter_keywords = search_form.getForm().findField("keywords").getValue() == null ? '' : search_form.getForm().findField("keywords").getValue();
                    var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
                    var label = grid.getStore().data.items[rowIndex].data.label;
                    if (noScriptConvertSwitch) {
                        if (!isHideCustom) {
                            if (isflow == '1') {
                                customScriptFlowInTaskManager(iid, status, serviceName, bussId, bussTypeId, scriptLevel, scriptType, menuId, filter_serverNameQuery, filter_scriptNameQuery, filter_keywords);
                            } else {
                                customScriptInTaskManager(uuid, iid, status, serviceName, bussId, bussTypeId, scriptLevel, scriptType, menuId, platForm, label);
                            }
                        }
                    } else {
                        if (!isHideCustom) {
                            customScriptFlowInTaskManager(iid, status, serviceName, bussId, bussTypeId, scriptLevel, scriptType, menuId, filter_serverNameQuery, filter_scriptNameQuery, filter_keywords);
                        }
                    }
                }
            }]
        }
//    {/////////////////////////////////////////////////////////////////////////////////////////////////
//        text: '操作',
//        dataIndex: 'xq',
//        width:isHideCustom?200:280,
//        align: 'center',
//        renderer: function(value, p, record) {
//            var iid = record.get('iid');
//            var status = record.get('status');
//            var isflow = record.get('isFlow');
//            var serviceName = record.get('serviceName');
//            var keywords = record.get('keywords');
//            var scriptName =  record.get('scriptName');//浦发
//            var newTitle = serviceName+' — '+scriptName;//浦发需求
//            var bussId = record.get('bussId');
//            var bussTypeId = record.get('bussTypeId');
//            var scriptLevel = record.get('scriptLevel');
//            var scriptType = record.get('scriptType');
//            var suUser = record.get('execUser');
//            var platForm = record.get('platForm'); //浦发需求
//            var filter_serverNameQuery = search_form.getForm().findField("serviceName").getValue()==null?'':search_form.getForm().findField("serviceName").getValue();
//            var filter_scriptNameQuery = search_form.getForm().findField("scriptName").getValue()==null?'':search_form.getForm().findField("scriptName").getValue();
//            var filter_keywords = search_form.getForm().findField("keywords").getValue()==null?'':search_form.getForm().findField("keywords").getValue();
//            
//            var execFuncName = "forwardScriptExecAudiForTaskIndex";
//            var viewDetailFuncName = "viewDetailForTaskIndex";
//            var customScriptFuncName = "customScriptInTaskManager";
//            if (isflow == '1') {
//                execFuncName = "forwardScriptFlowExecAudi";
//                viewDetailFuncName = "viewDetailForTaskIndexForFlow";
//                customScriptFuncName = "customScriptFlowInTaskManager";
//            }
//            var    flowCustomHy = '<span class="switch_span">'+
//   			   	'<a href="javascript:void(0)" onclick="customScriptFlowInTaskManager('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+ scriptLevel + ',\'' + scriptType+'\''+',' + menuId+',\''+filter_serverNameQuery+'\',\''+filter_scriptNameQuery+'\''+')">'+
//			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_template"></img>&nbsp;常用任务'+
//			   	'</a>'+
//			   '</span>';
//            if(noScriptConvertSwitch){
//            	if(isHideCustom){
//            		return '<span class="switch_span">' +
//            		'<a href="javascript:void(0)" onclick="' + execFuncName + '(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ',\'' + scriptType+'\'' +','+ isflow+',' + menuId+',\''+newTitle+'\',\''+platForm+'\',\'' + suUser + '\',\''+filter_serverNameQuery+'\',\''+filter_scriptNameQuery+'\''+')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;任务申请</a>' +
//            	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//            	   '<span class="switch_span">' +
//            		'<a href="javascript:void(0)" onclick="' + viewDetailFuncName + '(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ','+ isflow+',' + menuId + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;查看</a>' +
//            	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//            	}else{
//            		return '<span class="switch_span">' +
//            		'<a href="javascript:void(0)" onclick="' + execFuncName + '(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ',\'' + scriptType+'\'' +','+ isflow+',' + menuId+',\''+newTitle+'\',\''+platForm+'\',\'' + suUser + '\',\''+filter_serverNameQuery+'\',\''+filter_scriptNameQuery+'\''+')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;任务申请</a>' +
//            	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//            	   '<span class="switch_span">' +
//            		'<a href="javascript:void(0)" onclick="' + viewDetailFuncName + '(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ','+ isflow+',' + menuId + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;查看</a>' +
//            	   '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;'+
//            	   '<span class="switch_span">'+
//      			   	'<a href="javascript:void(0)" onclick="' + customScriptFuncName + '('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+ scriptLevel + ',\'' + scriptType+'\''+',' + menuId+',\''+platForm+'\')">'+
//    			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_template"></img>&nbsp;常用任务'+
//    			   	'</a>'+
//    			   '</span>';
//            	}
//            }else{
//            	if(isHideCustom){
//            		return '<span class="switch_span">' +
//                	'<a href="javascript:void(0)" onclick="forwardScriptFlowExecAudi(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ',\'' + scriptType+'\'' +','+ isflow+',' + menuId+ ' )" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;任务申请</a>' +
//                	'</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//                	'<span class="switch_span">' +
//                	'<a href="javascript:void(0)" onclick="viewDetailForTaskIndexForFlow(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel+','+ isflow+',' + menuId + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_search"></img>&nbsp;查看</a>' +
//                	'</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//            	}else{
//            		return '<span class="switch_span">' +
//                	'<a href="javascript:void(0)" onclick="forwardScriptFlowExecAudi(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel + ',\'' + scriptType+'\'' +','+ isflow+',' + menuId+ ',\''+newTitle+'\',\''+platForm+'\',\'' + suUser +',\''+filter_serverNameQuery+'\',\''+filter_scriptNameQuery+'\''+' )" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;任务申请</a>' +
//                	'</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;' +
//                	'<span class="switch_span">' +
//                	'<a href="javascript:void(0)" onclick="viewDetailForTaskIndexForFlow(' + iid + ',\'' + serviceName + '\',' + bussId + ',' + bussTypeId + ',' + scriptLevel+','+ isflow+',' + menuId + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_search"></img>&nbsp;查看</a>' +
//                	'</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;'+
//                	flowCustomHy;
//            	}
//            }
//        }
//    }
    ];


    Ext.define('scriptDirModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iscriptDirName',
                type: 'string'
            }, {
                name: 'iscriptDirSort',
                type: 'long'
            }, {
                name: 'iid',
                type: 'long'
            }, {
                name: 'iscriptDirDiscript',
                type: 'string'
            }, {
                name: 'iscriptDirLevel',
                type: 'int'
            }, {
                name: 'iscriptDirRootId',
                type: 'string'
            }
        ]

    })

    var scriptDirTreeStore = Ext.create('Ext.data.TreeStore', {
        autoLoad: gfScriptDirFunctionSwitch,
        model: 'scriptDirModel',
        proxy: {
            type: 'ajax',
            url: 'scriptEdit/scriptDirList.do'
        },
        root: {
            expanded: gfScriptDirFunctionSwitch,
            iscriptDirName: '根目录',
            icon: 'ext/resources/ext-theme-neptune/images/tree/folder.png'
        }
    });

    scriptDirTreeStore.on("load", function (obj, node, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = treePanel.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(treePanel.getView().emptyText);
                treePanel.body.dom.childNodes[0].appendChild(doc);
            }
        }
    });


    var treePanel = Ext.create('Ext.tree.Panel', {
        // height : '80%',
        region: 'west',
        width: '16%',
        id: 'treeId',
        title: '脚本目录',
        autoScroll: true,
        collapsible: true,
        cls: 'customize_panel_back',
        animate: true,
        useArrows: true,
        hidden: !gfScriptDirFunctionSwitch,
        rootVisible: false,
        store: scriptDirTreeStore,
        hideHeaders: true,
        columns: [{
            xtype: 'treecolumn',
            text: '目录',
            flex: 1,
            dataIndex: 'iscriptDirName',
            sortable: false
        }],
        border: true,
        padding: grid_space,
        columnLines: true,
        listeners:{
            select:function(){
                var filter_scriptStatus = isFlowScript.getValue();
                scriptservice_grid.ipage.moveFirst();
                BSinfoloader();
            }
        },
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    });
    scriptDirTreeStore.on('load', function (store, records) {
        var selMode = Ext.getCmp('treeId').getSelectionModel();
        var root = store.getRootNode();
        selMode.select(root)
    })

    function getLastScriptDirId(record, scriptDirId) {
        if (record.childNodes.length > 0) {
            let m = record.childNodes;
            for (let i = 0; i < m.length; i++) {
                getLastScriptDirId(m[i], scriptDirId)
            }
        } else {
            let iid = record.data.iid;
            scriptDirId.push(iid);
        }
    }

    scriptservice_store.on('beforeload',
        function (store, options) {
            if (myTodoForserviceName != '' && myTodoForserviceName != null && myTodoForserviceNameNoCount == 0) {
                sName.setValue(myTodoForserviceName);//如果我的代办传递过来的工单内容参数不为空，那么进行赋值
            }
            var emSriptCb = (null != emScriptCb.getValue()) ? emScriptCb.getValue() : -1
            var defalutIsFlow = 0;
            if (reviewSwitch) {
                defalutIsFlow = -1;
            }
            var isFlowScriptValue = (null != isFlowScript.getValue()) ? isFlowScript.getValue() : defalutIsFlow

            var fromTableValue = 1;
            if (isHideTaskFrom) {
                fromTableValue = -1;
            }
            var scriptDirId = [];
            if (gfScriptDirFunctionSwitch) {
                var m = Ext.getCmp('treeId').getSelectionModel().getSelection();
                for (let i = 0; i < m.length; i++) {
                    let iid = m[i].data.iid;
                    let iscriptDirLevel = m[i].data.iscriptDirLevel;
                    // 0级目录 root根目录
                    if (iscriptDirLevel == 0) {
                        break;
                    }
                    getLastScriptDirId(m[i], scriptDirId);
                    //scriptDirId.push(iid)
                }
            }

            //浙商专有的创建人的开关
            if (scriptCreateUserSwitch) {
                var new_params = {
                    iid: scriptIIDForTaskIndex,
                    serviceName: search_form.getForm().findField("serviceName").getValue(),
                    bussId: bussCb.getValue() || 0,
                    keywords: search_form.getForm().findField("keywords").getValue(),
                    bussTypeId: search_form.getForm().findField("bussType").getValue() || 0,
                    threeBsTypeId: search_form.getForm().findField("threeBussTypeCb").getValue() || 0,
                    scriptName: search_form.getForm().findField("scriptName").getValue() || '',
                    scriptType: search_form.getForm().findField("scriptTypeParam").getValue() || '',
                    fromTable: search_form.getForm().findField("scriptFrom").getValue() || fromTableValue,
                    isEMscript: emSriptCb,
                    iappSysIds: appSysObj.getValue(),
                    status: 1,
                    /*onlyScript: 1*/
                    platForm: platFromCombobox.getValue(),
                    createUserNameForCz: createUser.getValue(),//浙商专有的创建人
                    showVersion: isShowVersion.getValue() ? 1 : 0,
                    isFlow: isFlowScriptValue,
                    scriptDir: Ext.encode(scriptDirId),
                    label: label.getValue(),
                    groupName: groupNameCombo.getValue(),
                    heightPermissionFlag: heightPermissionFlag
                };
            } else {
                var new_params = {
                    iid: scriptIIDForTaskIndex,
                    serviceName: search_form.getForm().findField("serviceName").getValue(),
                    bussId: bussCb.getValue() || 0,
                    keywords: search_form.getForm().findField("keywords").getValue(),
                    bussTypeId: search_form.getForm().findField("bussType").getValue() || 0,
                    threeBsTypeId: search_form.getForm().findField("threeBussTypeCb").getValue() || 0,
                    scriptName: search_form.getForm().findField("scriptName").getValue() || '',
                    scriptType: search_form.getForm().findField("scriptTypeParam").getValue() || '',
                    fromTable: search_form.getForm().findField("scriptFrom").getValue() || fromTableValue,
                    isEMscript: emSriptCb,
                    iappSysIds: appSysObj.getValue(),
                    status: 1,
                    /*onlyScript: 1*/
                    platForm: platFromCombobox.getValue(),
                    createUserName: createUser.getValue(),
                    showVersion: isShowVersion.getValue() ? 1 : 0,
                    isFlow: isFlowScriptValue,
                    scriptDir: Ext.encode(scriptDirId),
                    label: label.getValue(),
                    groupName: groupNameCombo.getValue(),
                    heightPermissionFlag: heightPermissionFlag
                };
            }


            Ext.apply(scriptservice_store.proxy.extraParams, new_params);
        });

    scriptservice_store.load({
        callback: function (records, operation, success) {
            // 选择后构建出的ID数组
            if (iidArray.length > 0) {
                var chosedRecords = []; //存放选中记录
                $.each(records,
                    function (index, record) {
                        if (iidArray.indexOf(record.get('iid')) > -1) {
                            chosedRecords.push(record);
                        }
                    });
                scriptservice_grid.getSelectionModel().select(chosedRecords, false, false); //选中记录
            }

            if (scriptservice_store.getCount() == 0 && myTodoForserviceName != '' && myTodoForserviceName != null) {
                sName.setValue();
                myTodoForserviceNameNoCount = 1;
                scriptservice_store.reload();
            }
        }
    });

//    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//        store: scriptservice_store,
//        dock: 'bottom',
//        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//        displayInfo: true
//    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var scriptservice_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: scriptservice_store,
        border: false,
        cls: 'customize_panel_back',
        columnLines: true,
        columns: scriptservice_columns,
        padding: grid_space,
        selModel: selModel,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        listeners: {
            select: function (e, record, index, eOpts) {
                if (iidArray.indexOf(record.get('iid')) == -1) {
                    iidArray.push(record.get('iid'));
                }
            },
            deselect: function (e, record, index, eOpts) {
                if (iidArray.indexOf(record.get('iid')) > -1) {
                    iidArray.remove(record.get('iid'));
                }
            }
        }
//        bbar: pageBar
    });
    if (myTodoForserviceName != '' && myTodoForserviceName != null && myTodoForserviceNameNoCount == 0) {
        sName.setValue(myTodoForserviceName);//如果我的代办传递过来的工单内容参数不为空，那么进行赋值
    }
    var emSriptCb = (null != emScriptCb.getValue()) ? emScriptCb.getValue() : -1
    var defalutIsFlow = 0;
    if (reviewSwitch) {
        defalutIsFlow = -1;
    }
    var isFlowScriptValue = (null != isFlowScript.getValue()) ? isFlowScript.getValue() : defalutIsFlow
    if (reviewSwitch) {
        isFlowScriptValue = -1;
    }
    var fromTableValue = 1;
    if (isHideTaskFrom) {
        fromTableValue = -1;
    }
    var northPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        hidden: topInformationSwitch,
        region: 'north',
        height: 125,
        loader: {
            url: 'getScriptServiceListForTaskManageBSinfo.do',
            autoLoad: true,
            scripts: true,
            params: {
                iid: scriptIIDForTaskIndex,
                serviceName: search_form.getForm().findField("serviceName").getValue(),
                bussId: bussCb.getValue() || 0,
                keywords: search_form.getForm().findField("keywords").getValue(),
                bussTypeId: search_form.getForm().findField("bussType").getValue() || 0,
                scriptName: search_form.getForm().findField("scriptName").getValue() || '',
                scriptType: search_form.getForm().findField("scriptTypeParam").getValue() || '',
                fromTable: search_form.getForm().findField("scriptFrom").getValue() || fromTableValue,
                isEMscript: emSriptCb,
                iappSysIds: appSysObj.getValue(),
                status: 1,
                /*onlyScript: 1*/
                platForm: platFromCombobox.getValue(),
                createUserName: createUser.getValue(),
                showVersion: isShowVersion.getValue() ? 1 : 0,
                isFlow: isFlowScriptValue,
                pagesizeStart: 0,
                pagesizelimit: 50,
                scriptDir: Ext.encode([])
            }
        }
    });

    function BSinfoloader() {
        northPanel.doLayout();
        if (myTodoForserviceName != '' && myTodoForserviceName != null && myTodoForserviceNameNoCount == 0) {
            sName.setValue(myTodoForserviceName);//如果我的代办传递过来的工单内容参数不为空，那么进行赋值
        }
        var emSriptCb = (null != emScriptCb.getValue()) ? emScriptCb.getValue() : -1
        var defalutIsFlow = 0;
        if (reviewSwitch) {
            defalutIsFlow = -1;
        }
        var isFlowScriptValue = (null != isFlowScript.getValue()) ? isFlowScript.getValue() : defalutIsFlow
        if (reviewSwitch) {
            isFlowScriptValue = -1;
        }
        var fromTableValue = 1;
        if (isHideTaskFrom) {
            fromTableValue = -1;
        }
        var scriptDirId = [];
        if (gfScriptDirFunctionSwitch) {
            var m = Ext.getCmp('treeId').getSelectionModel().getSelection();
            for (let i = 0; i < m.length; i++) {
                let iid = m[i].data.iid;
                let iscriptDirLevel = m[i].data.iscriptDirLevel;
                // 0级目录 root根目录
                if (iscriptDirLevel == 0) {
                    break;
                }
                scriptDirId.push(iid)
            }
        }
        northPanel.getLoader().load({
            url: 'getScriptServiceListForTaskManageBSinfo.do',
            params: {
                iid: scriptIIDForTaskIndex,
                serviceName: search_form.getForm().findField("serviceName").getValue(),
                bussId: bussCb.getValue() || 0,
                keywords: search_form.getForm().findField("keywords").getValue(),
                bussTypeId: search_form.getForm().findField("bussType").getValue() || 0,
                scriptName: search_form.getForm().findField("scriptName").getValue() || '',
                scriptType: search_form.getForm().findField("scriptTypeParam").getValue() || '',
                fromTable: search_form.getForm().findField("scriptFrom").getValue() || fromTableValue,
                isEMscript: emSriptCb,
                iappSysIds: appSysObj.getValue(),
                status: 1,
                /*onlyScript: 1*/
                platForm: platFromCombobox.getValue(),
                createUserName: createUser.getValue(),
                showVersion: isShowVersion.getValue() ? 1 : 0,
                isFlow: isFlowScriptValue,
                pagesizeStart: 0,
                pagesizelimit: 6,
                scriptDir: Ext.encode(scriptDirId)
            },
            scripts: true
        });
    }

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptexec_task_area",
        layout: 'border',
        border: false,
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        items: [northPanel, search_form, treePanel, scriptservice_grid]
    });

    function clearQueryWhere() {
        search_form.getForm().findField("groupName").setValue('');
        search_form.getForm().findField("sysName").setValue('');
        search_form.getForm().findField("bussType").setValue('');
        search_form.getForm().findField("scriptName").setValue('');
        search_form.getForm().findField("serviceName").setValue('');
        search_form.getForm().findField("scriptTypeParam").setValue('');
        search_form.getForm().findField("scriptFrom").setValue('-1');
        search_form.getForm().findField("appSys").setValue('');
        search_form.getForm().findField("emScript").setValue('-1');
        search_form.getForm().findField("platFromCombobox").setValue('');
        search_form.getForm().findField("createUser").setValue('');
        search_form.getForm().findField("keywords").setValue('');
        search_form.getForm().findField("isFlowScript").setValue();
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
        bussTypeData.removeAll();
        label.setValue('');
    }

    // 将被选中的记录的flowid组织成json串，作为参数给后台处理
    function getSelectedJsonData(serviceState) {
        var flowIdList = scriptservice_grid.getSelectionModel().getSelection();
        if (flowIdList.length < 1) {
            return;
        }
        var jsonData = "[";
        var signNum = 0;
        for (var i = 0,
                 len = flowIdList.length; i < len; i++) {
            if (flowIdList[i].data.status == serviceState) {
                if (signNum == 0) {
                    jsonData = jsonData + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
                } else {
                    jsonData = jsonData + "," + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
                }
                signNum = signNum + 1;
            }
        }
        jsonData = jsonData + "]";
        return jsonData;
    }

    // 从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        var eValue = eval('jsonObj.' + key);
        return jsonObj['' + key + ''];
    }

    function forwardScriptExecAudiForTaskIndex(uuid, serviceId, a, b, c, scriptLevel, scriptType, isFlow, menuId, newTitle, platmFrom, timeout, label) {
        var whiteScript = "";
        if (scriptLevel == 0) {
            whiteScript = "whiteScript";
        }
        var titleValue = '任务申请：';
        if(heightPermissionFlag){
            titleValue = '值班任务申请：';
        }

        auditingWin = Ext.create('widget.window', {
            title: titleValue + newTitle,
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() + 30,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForAuditing.do',
                params: {
                    iid: serviceId,
                    scriptType: scriptType,
                    scriptLevel: scriptLevel,
                    platmFrom: platmFrom,//适用平台
                    serviceName: a,
                    whiteScript: whiteScript,
                    timeout: timeout,
                    uuid: uuid,
                    label: label,
                    heightPermissionFlag: heightPermissionFlag
                },
                autoLoad: true,
                scripts: true
            }
        });
        auditingWin.show();
    }


    contentPanel.on('resize',
        function () {
            mainPanel.setWidth(contentPanel.getWidth());
            mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
            if (auditingWin) {
                auditingWin.center();
            }
            // search_form.setWidth(contentPanel.getWidth()-300);
            // search_form.getForm().findField("serviceName").setWidth(contentPanel.getWidth()*0.3);
            // search_form.getForm().findField("status").setWidth(contentPanel.getWidth()*0.2);
        });

    //导出
    function exportExcel() {
        var record = scriptservice_grid.getSelectionModel().getSelection();
//		var instanceNames = [];
        var iidStr = "";
        Ext.Array.each(record, function (recordObj) {
            iidStr += "," + recordObj.get('iid');
//			instanceNames.push (iinstacneName);
        });
        if (iidStr.length <= 0) {
            Ext.Msg.alert('提示', '请选择要操作的行！');
            return;
        }
        iidStr = iidStr.substr(1);
//    	alert("iidStr="+iidStr);
        window.location.href = 'exportService.do?iidStr=' + iidStr;
    }

    //下载脚本
    function downloadScript() {
        var record = scriptservice_grid.getSelectionModel().getSelection();
        var iidStr = "";
        let message = "";
        Ext.Array.each(record, function (recordObj) {
            let isFlow = recordObj.get('isFlow');
            let serviceName = recordObj.get('serviceName');
            if (isFlow == '1') {
                message += "、" + serviceName;
            }
            iidStr += "," + recordObj.get('iid');
        });
        if (iidStr.length <= 0) {
            Ext.Msg.alert('提示', '请选择要操作的行！');
            return;
        }

        if (message != "") {
            Ext.Msg.alert('提示', '选择的服务：' + message.substr(1) + '不支持下载，只支持下载脚本，不支持下载作业类型！');
            return;
        }

        iidStr = iidStr.substr(1);
        window.location.href = 'scriptDownload.do?iid=' + iidStr + '&questionType=2';
    }

    function importExcel() {
        var uploadWindows;
        var uploadForm
        uploadForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [{
                xtype: 'filefield',
                name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
                fieldLabel: '选择文件',
                labelWidth: 80,
                anchor: '90%',
//    			labelAlign: 'right',
                margin: '10 10 0 40',
                buttonText: '浏览'
            }],
            buttonAlign: 'center',
            buttons: [{
                text: '确定',
                handler: upExeclData
            }, {
                text: '取消',
                handler: function () {
                    uploadWindows.close();
                }
            }]
        });
        uploadWindows = Ext.create('Ext.window.Window', {
            title: '任务导入',
            layout: 'fit',
            height: 140,
            width: 600,
            modal: true,
//    		autoScroll : true,
            items: [uploadForm],
            listeners: {
                close: function (g, opt) {
                    uploadForm.destroy();
                }
            }
        });
        uploadWindows.show();

        function upExeclData() {
            var form = uploadForm.getForm();
            var hdupfile = form.findField("fileName").getValue();
            if (hdupfile == '') {
                Ext.Msg.alert('提示', "请选择文件...");
                return;
            }
            uploadTemplate(form);
        }

        function uploadTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'importScriptForProduct.do',
                    success: function (form, action) {
                        var sumsg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示', sumsg);
                        uploadWindows.close();
                        scriptservice_grid.ipage.moveFirst();
                        BSinfoloader();
                        /*scriptServiceReleaseStore.reload();*/
                        return;
                    },
                    failure: function (form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        var mess = Ext.create('Ext.window.MessageBox', {
                            minHeight: 110,
                            minWidth: 500,
                            resizable: false
                        });
                        Ext.Msg.alert('提示', msg);
                        return;
                    }
                });
            }
        }
    }


    function viewDetailForTaskIndex(serviceId, label) {
//    if (!DetailWinTi) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForView.do',
                params: {
                    iid: serviceId,
                    flag: 1,
                    hideReturnBtn: 1,
                    label: label
                },
                autoLoad: true,
                scripts: true
            }
        });
//    }

//    DetailWinTi.getLoader().load({});
        DetailWinTi.show();
    }

    function viewDetailForTaskIndexForFlow(serviceId, serviceName, bussId, bussTypeId, scriptLevel, isFlow, menuId) {
//    if (!DetailWinTi) {
        var DetailWinTi = Ext.create('widget.window', {
            title: '详细信息',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() - 42,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'flowCustomizedInitScriptService.do',
                params: {
                    serviceId: serviceId,
                    iid: serviceId,
                    menuId: menuId,
                    status: 0,
                    serviceName: serviceName,
                    actionType: 'view',
                    bussId: bussId,
                    bussTypeId: bussTypeId,
                    submitType: 'tv',
                    flag: 0,
                    windowHeight: contentPanel.getHeight() - 42,
                    rootspace: 'view',
                    isShowInWindow: 1,
                    isScriptConvertToFlow: isFlow != '1'
                },


//			url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
//			params: {
//				iid:serviceId,
//				serviceName:serviceName,
//				actionType:'view',
//				bussId:bussId,
//				bussTypeId:bussTypeId,
//				flag:0,
//				isShowInWindow: 1
//			},
                autoLoad: true,
                scripts: true
            }
        });
//    }

//    DetailWinTi.getLoader().load({});
        DetailWinTi.show();
    }

    function showTotalAndTotalrate(iid) {
        Ext.Ajax.request({
            url: 'getTotalAndTotalrate.do',
            method: 'post',
            params: {
                iid: iid
            },
            success: function (response, request) {
                var total = Ext.decode(response.responseText).total;
                var totalrate = Ext.decode(response.responseText).totalrate;
                Ext.Msg.alert('使用次数及成功率', '使用次数：' + total + '<br>成功率：' + totalrate + '%');
            },
            failure: function (result, request) {
                secureFilterRs(result, "请求返回失败！", request);
            }
        });
    }

    function forwardScriptFlowExecAudi(iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isFlow, menuId, newTitle, platmFrom, suUser, filter_serverNameQuery, filter_scriptNameQuery, filter_keywords) {
        // alert(iid);
        destroyRubbish(); // 销毁本页垃圾
        var url = 'treeCustomizedInitScriptServiceGFSSAUDI.do';
        if (isFlow == 0 || taskApplyForSPDBSwitch) {
            url = 'flowCustomizedInitScriptServiceGFSSAUDI.do';
        }
        contentPanel.getLoader().load({
            //url: 'flowCustomizedInitScriptServiceGFSSAUDI.do',
            url: url,
            params: {
                iid: iid,
                serviceName: serviceName,
                menuId: menuId,
                actionType: 'audi',
                bussId: bussId,
                bussTypeId: bussTypeId,
                scriptType: scriptType,
                platmFrom: platmFrom,
                scriptLevel: scriptLevel,
                submitType: 'ea',
                ifrom: 'scriptServicesTaskExec.do',
                flag: 1,
                isScriptConvertToFlow: isFlow != '1',
                taskType: 'taskCreate',
                filter_serverNameQuery: filter_serverNameQuery,
                filter_scriptNameQuery: filter_scriptNameQuery,
                filter_keywords: filter_keywords,
                suUser: suUser
            },
            scripts: true
        });
    }

    function customScriptFlowInTaskManager(iid, status, serviceName, bussId, bussTypeId, a, b, menuId, filter_serverNameQuery, filter_scriptNameQuery, filter_keywords) {
        destroyRubbish(); //销毁本页垃圾
        contentPanel.getLoader().load({
            url: 'flowCustomizedInitScriptServiceGFSSFLOWCUSTOMPRODUCTFROMTM.do',
            params: {
                iid: iid,
                serviceName: serviceName,
                menuId: menuId,
                bussId: bussId,
                bussTypeId: bussTypeId,
                actionType: 'model',
                submitType: 'tm',
                ifrom: 'scriptServicesTaskExec.do',
                filter_serverNameQuery: filter_serverNameQuery,
                filter_scriptNameQuery: filter_scriptNameQuery,
                filter_keywords: filter_keywords,
                flag: 1
            },
            scripts: true
        });
    }

    function customScriptInTaskManager(uuid, iid, status, serviceName, bussId, bussTypeId, scriptLevel, scriptType, menuId, platmFrom, label) {
        var coustomConfigWin = Ext.create('widget.window', {
            title: '配置常用任务',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight() + 30,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'queryOneServiceForCustom.do',
                params: {
                    iid: iid,
                    scriptType: scriptType,
                    scriptLevel: scriptLevel,
                    serviceName: serviceName,
                    platmFrom: platmFrom, //适用平台
                    uuid: uuid,
                    label: label
                },
                autoLoad: true,
                scripts: true
            }
        });
        coustomConfigWin.show();
    }

});


