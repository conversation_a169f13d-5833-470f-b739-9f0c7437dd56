//var agent_chosed_ids = [];
var agent_chosed_store_fortest;
var chosedResGroups_forest = new Array();

Ext.onReady(function(){
	destroyRubbish();
	var win;
	var moreInfoWin;
	var execAuditingSesWin;
	
	contentPanel.setAutoScroll(true);
	
	prettyPrint();
	
	var versionStore = Ext.create('Ext.data.Store', {
		fields : [ 'serviceId', 'version' ],
		autoLoad : false,
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceVersionList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var versionCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'versionCb',
		labelWidth : 70,
		width : contentPanel.getWidth()/2,
		queryMode : 'local',
		fieldLabel : '',
		padding : '5',
		displayField : 'version',
		valueField : 'serviceId',
		editable : false,
		queryMode : 'local',
		emptyText : '',
		store : versionStore,
//		renderTo: 'switch-version',
		listeners : {
			select: function( combo, records, eOpts ) { // old is keyup
				destroyRubbish(); //销毁本页垃圾
				contentPanel.getLoader().load({url: 'scriptExecStart.do?serviceId='+combo.getValue()+'&flag='+flag_fortest+'&url='+url_fortest,scripts: true});
			}
		}
	});
	
	versionStore.on('beforeload', function (store, options) {
	    var new_params = {  
	    	serviceId:serviceId_fortest,
	    	flag:flag_fortest
	    };
	    
	    Ext.apply(versionStore.proxy.extraParams, new_params);
    });
	
	versionStore.on('load', function (store, options) {
		versionCb.setValue(parseInt(serviceId_fortest));
    });
  
  
	
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    margin : '0 10 0 10',
			    labelAlign : 'right',
	            labelWidth : 70,
			    multiSelect: true,
			    hidden:removeAgentSwitch,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
			    	listeners: {
			    	      change: function( comb, newValue, oldValue, eOpts ) {
			    	    	  chosedResGroups_forest = new Array();
			    	    	  for(var i=0;i<newValue.length;i++) {
			    	    		  chosedResGroups_forest.push(newValue[i]);
			    	    	  }
			    	    	  agent_store.load();
			    	      }
			    	}
	});
	
	
	var search_form = Ext.create('Ext.form.Panel', {
    	layout : 'anchor',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
	    	layout:'form',
	    	anchor:'95%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,		    	
	    		items:[resourceGroupObj,{
		            fieldLabel: 'IP',
		            labelAlign : 'right',
		            labelWidth : 70,
		            margin : '0 10 0 10',
		            name: 'agentIp',
		            columnWidth:.45,
		            xtype: 'textfield'
		        },{
		            fieldLabel: 'Agent描述',
		            labelAlign : 'right',
		            labelWidth : 70,
		            margin : '0 10 0 10',
		            name: 'agentDesc',
		            columnWidth:.45,
		            xtype: 'textfield'
		        },{
					xtype : 'button',
					width:60,
					height:30,
					margin:'0 5',
					cls:'Common_Btn',
					text : '查询',
					handler : function() {
						pageBar.moveFirst();
					}
				},{
					xtype : 'button',
					width:60,
					height:30,
					margin:'0 5',
					cls:'Common_Btn',
					text : '清空',
					handler : function() {
						clearQueryWhere();
					}
				}]
	    	}]
	    }]
	});
	
    

	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
	Ext.define('agentChosedModel', {
		extend: 'Ext.data.Model',
		idProperty: 'iid',
		fields: [
		         {name: 'iid',     type: 'string'},
		         {name: 'agentIp',     type: 'string'},
		         {name: 'agentPort',     type: 'string'},
		         {name: 'agentDesc',     type: 'string'},
		         {name: 'agentState',     type: 'int'},
		         {name: 'agentParam',     type: 'string'},
		         {name: 'userName',     type: 'string'}
		         ]
	});
    
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
	agent_chosed_store_fortest = Ext.create('Ext.data.Store', {
//        pageSize: 50,
        model: 'agentChosedModel',
        storeId: 'iid'/*,
        proxy: {
            type: 'ajax',
            url: 'getAgentChosedList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }*/
    });

	
    var agent_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: 'IP',  dataIndex: 'agentIp',width:120},
                        { text: '端口号',  dataIndex: 'agentPort',width:100},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1},
		                { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ];
    
    var agent_chosed_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
                         { text: '主键',  dataIndex: 'iid',hidden:true},
                         { text: 'IP',  dataIndex: 'agentIp',flex:1},
                         { text: '端口号',  dataIndex: 'agentPort',width:100},
 		                { text: '描述',  dataIndex: 'agentDesc',width:250},
 		               {
 							dataIndex : 'agentParam',
 							text : '参数',
 							editor : true,
 							flex : true
 						}, {
 							dataIndex : 'userName',
 							text : '执行用户',
 							editor : true,
 							flex : true
 						},
 		                { text: '状态',  dataIndex: 'agentState',width:100,renderer:function(value,p,record){
 		                	var backValue = "";
 		                	if(value==0){
 		                		backValue = "Agent正常";
 		                	}else if(value==1){
 		                		backValue = "Agent异常";
 		                	}
 		                	return backValue;
 		                }},
 		               { text: '操作',  dataIndex: 'operation',width:150,align:'center',renderer:function(value,p,record){
		                	var iid =  record.get('iid');
		                	return '<span class="switch_span"><a href="javascript:void(0)" onclick="deleteChosedAgent('+iid+')" ><img src="images/monitor_bg.png" align="absmiddle" class="monitor_delete"></img>&nbsp;删除</a></span>';
		                }}
 		               ];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
    
    agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp:search_form.getForm().findField("agentIp").getValue(),
	    	agentDesc:search_form.getForm().findField("agentDesc").getValue(),
	    	rgIds:chosedResGroups_forest,
	    	flag: flag_fortest
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    /*agent_chosed_store_fortest.on('beforeload', function (store, options) {
    	var new_params = {  
    		agentIds: agent_chosed_ids
    	};
    	
    	Ext.apply(agent_chosed_store_fortest.proxy.extraParams, new_params);
    });*/
    
    
    var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });
    /*var pageBarChosed = Ext.create('Ext.PagingToolbar', {
    	store: agent_chosed_store_fortest,
    	dock: 'bottom',
    	displayInfo: true
    });*/
    
    var agent_grid = Ext.create('Ext.grid.Panel', {
    	height: contentPanel.getHeight()-205,
	    store:agent_store,
	    border:true,
	    columnLines : true,
//	    rowLines: false,
	    columns:agent_columns,
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    bbar: pageBar
	});
    
    var agent_chosed_grid = Ext.create('Ext.grid.Panel', {
    	height: 200,
	    store:agent_chosed_store_fortest,
	    border:true,
	    columnLines : true,
	    columns:agent_chosed_columns,
	    plugins : [ cellEditing ],
//	    bbar: pageBarChosed,
	    hidden: true,
	    renderTo: 'chosed-agent_fortest'
	});
    
    function clearQueryWhere(){
    	search_form.getForm().findField("agentIp").setValue('');
    	search_form.getForm().findField("agentDesc").setValue('');
    }
    
    
	$('body').off('click', '#select-agent_fortest').on('click', '#select-agent_fortest', function(e){
		pageBar.moveFirst();
		if(win) {
			win.show();
		} else {
			win = Ext.create('Ext.window.Window', {
		  		title : '选择服务器',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		closeAction : 'hide',
		  		width : contentPanel.getWidth()-250,
		  		height : contentPanel.getHeight()-60,
		  		items:[search_form,agent_grid],
		  		buttons: [{ 
		  			xtype: "button",
//		  			cls:'Common_Btn',
		  			text: "添加", 
		  			handler: function () { 
		  				var records = agent_grid.getSelectionModel().getSelection();
		  				var addedRecs = [];
//		  				var ids = [];
		  				Ext.each(records, function(record) {
		  					var isExist = agent_chosed_store_fortest.indexOfId(record.data.iid);
		  					if(isExist==-1) {
		  						addedRecs.push(record);
		  					}
//		  					ids.push(record.data.iid);
						});
		  				
//		  				agent_chosed_ids = Array.union(ids, agent_chosed_ids);
		  				agent_chosed_store_fortest.loadData(addedRecs, true);
		  				this.up("window").close();
//		  				pageBarChosed.moveFirst();
		  				$("#clear-agent_fortest").show().css("display","inline-block");;
		  				agent_chosed_grid.show();
			        }
		  		}, { 
		  			xtype: "button", 
//		  			cls:'Gray_button',
		  			text: "取消", 
		  			handler: function () {
		  				this.up("window").close();
		  			}
		  		}]
		  	}).show();
		}
		resourceGroupObj.setValue('');
		search_form.getForm().findField("agentIp").setValue('');
    	search_form.getForm().findField("agentDesc").setValue('');
    	agent_store.load();
	});
	
	$('body').off('click', '#clear-agent_fortest').on('click', '#clear-agent_fortest', function(e){
//		agent_chosed_ids = [];
//		pageBarChosed.moveFirst();
		agent_chosed_store_fortest.removeAll();
	});
	
	$('body').off('click', '#backto_fortest').on('click', '#backto_fortest', function(e){
		destroyRubbish();
		contentPanel.getLoader().load({url: url_fortest,scripts: true, params:{ serviceId:serviceId_fortest }});
	});
	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	var auditorStore_ses = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecuteAuditorList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	var auditorComBox_ses = Ext.create('Ext.form.ComboBox', {
	    editable: false,
	    fieldLabel: "审核人",
	    labelWidth: 60,
//	    padding: 5,
	    store: auditorStore_ses,
	    queryMode: 'local',
//	    width: 200,
	    columnWidth:.98,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    valueField: 'loginName'//,
	    //value: auditor
	  });
	
	var execDesc_ses = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '详细说明',
        emptyText: '',
        labelWidth: 60,
        margin : '10 0 0 0',
        height: 80,
        columnWidth:.98,
        autoScroll: true
    });
	
	var levelDis = '低级风险';
	if(level_fortest=='1') {
		levelDis = '高级风险';
	} else if(level_fortest=='2') {
		levelDis = '中级风险';
	} else if(level_fortest=='3') {
		levelDis = '低级风险';
	}else if(level_fortest=='0') {
		levelDis = '白名单';
	}
	var auditing_form_ses = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
	    	anchor:'98%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	            xtype: 'displayfield',
	            fieldLabel: '脚本风险',
	            name: 'home_score',
	            value: levelDis
	        }, {
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_ses]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[execDesc_ses]
	    	}]
	    }]
	});
	
	$('body').off('click', '#execservice_fortest').on('click', '#execservice_fortest', function(e){
		/*if(agent_chosed_ids.length==0) {
			Ext.Msg.alert('提示', '没有选择目标机器！');
			return;
		}*/
		
		if(agent_chosed_store_fortest.getCount()==0) {
			Ext.Msg.alert('提示', '没有选择目标机器！');
			return;
		}
		
		var jsonData = "[";
		for (var i = 0, len = agent_chosed_store_fortest.getCount(); i < len; i++) {
			var ss = Ext.JSON.encode(agent_chosed_store_fortest.getAt(i).data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
		
		var execUser = $('#execUser_fortest').val();//'root';//
		
		var $this = $(this);
		
		
		
//		var scriptPara = $('input[name="scriptPara"]').val();
		var scriptPara = '';
		paramStore.sort('paramOrder', 'ASC');
		var m = paramStore.getRange(0, paramStore.getCount()-1);   
        for (var i = 0, len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';

            if ((paramType == 'OUT-int'||paramType == 'IN-int')&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                	Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                    return;
                }
            }
            if ((paramType == 'OUT-float'||paramType == 'IN-float')&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                	Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                    return;
                }
            }
            scriptPara += paramDefaultValue + " ";
        }
        var jsonDataPara = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonDataPara = jsonDataPara + ss;
            else jsonDataPara = jsonDataPara + "," + ss;
        }
      jsonDataPara = jsonDataPara + "]";
//        if('0'==flag) { // 测试
        	$this.html('正在执行...');
    		
    		Ext.Ajax
    		.request({
    			url : 'execScriptService.do',
    			method : 'POST',
    			params : {
    				serviceId: serviceId_fortest,
    				execUser: execUser,
    				scriptPara: scriptPara,
    				from: from_fortest,
    				jsonDataPara:jsonDataPara,
//    				agentIds : agent_chosed_ids,
    				jsonData:jsonData,
    				flag: flag_fortest
    			},
    			success : function(response, request) {
    				$this.html('执行脚本');
    				var coatId = Ext.decode(response.responseText).coatId;
    				var flowId = Ext.decode(response.responseText).flowId;
    				if(flag_fortest=='0') {
    					contentPanel.getLoader().load({url: "forwardscriptserver.do",scripts: true,params : {flowId:flowId,coatid:coatId, flag:flag_fortest}});
    				} else if(flag_fortest=='1') {
    					contentPanel.getLoader().load({url: "forwardscriptserverForExec.do",scripts: true,params : {flowId:flowId,coatid:coatId, flag:flag_fortest}});
    				} 
    			},
    			failure : function(result, request) {
    				Ext.Msg.alert('提示', '执行失败！');
    				$this.html('执行脚本');
    			}
    		});
        /*} else if ('1'==flag) { // 生产
        	if (!execAuditingSesWin) {
				execAuditingSesWin = Ext.create('widget.window', {
	                title: '确认执行审核信息',
	                closable: true,
	                closeAction: 'hide',
	                modal: true,
	                width: 600,
	                minWidth: 350,
	                height: 350,
	                layout: {
	                    type: 'border',
	                    padding: 5
	                },
	                items: [auditing_form_ses],
	                buttons: [{ 
			  			xtype: "button",
			  			cls:'Common_Btn',
			  			width:60,
			  			height:30,
			  			margin:'5',
			  			text: "确定", 
			  			handler: function () { 
			  				var execDesc = execDesc_ses.getValue();
			  				var auditor = auditorComBox_ses.getValue();
			  				if(!execDesc) {
			  					Ext.Msg.alert('提示', "没有填写执行描述！");
			  					return;
			  				}
			  				if(!auditor) {
			  					Ext.Msg.alert('提示', "没有选择审核人！");
			  					return;
			  				}
			  				
			  				Ext.Ajax.request({
			  				    url : 'scriptExecuteAuditing.do',
			  				    method : 'POST',
			  				    params : {
			  				    	sId : serviceId,
			  				    	execDesc: execDesc,
			  				    	auditor: auditor
			  				    },
			  				    success: function(response, opts) {
			  				        var success = Ext.decode(response.responseText).success;
			  				        var message = Ext.decode(response.responseText).message;
			  				        if(!success) {
			  				        	Ext.MessageBox.alert("提示", message);
			  				        } else {
			  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
			  				        }
			  				      execAuditingSesWin.close();
			  				      
			  				    },
			  				    failure: function(result, request) {
			  				    	secureFilterRs(result,"操作失败！");
			  				    	execAuditingSesWin.close();
			  				    }
			  			    });
			  				
				        }
			  		}, { 
			  			xtype: "button", 
			  			cls:'Gray_button',
			  			width:60,
			  			height:30,
			  			margin:'5',
			  			text: "取消", 
			  			handler: function () {
			  				this.up("window").close();
			  			}
			  		}]
	            });
	            
	        }
			execAuditingSesWin.show();
			auditorStore_ses.load();
			execDesc_ses.setValue('');
			auditorComBox_ses.setValue('');
        }*/
        
        
	});
	
	contentPanel.on('resize',function(){
    });
	
	/*contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		contentPanel.setAutoScroll(false);
	});*/
	
	$('body').off('click', '#showMore_fortest').on('click', '#showMore_fortest', function(e) {
		if(moreInfoWin) {
			moreInfoWin.show();
		} else {
			moreInfoWin = Ext.create('Ext.window.Window', {
		  		title : '更多描述',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		closeAction : 'hide',
		  		width : contentPanel.getWidth()-250,
		  		height : contentPanel.getHeight()-60,
		  		html: "<div class='Sc_desc'>入参说明</div>" + 
		  		      "<div class='Sc_content'>"+$('#ipd_fortest').html()+"</div>"+
		  		      "<div class='Sc_desc'>功能概述</div>" + 
		  		      "<div class='Sc_content'>"+$('#fd_fortest').html()+"</div>"
		  	}).show();
		}
	});
	
	Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });
    
    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: serviceId_fortest
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });
    
    var paramColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '参数类型',
        dataIndex: 'paramType',
        width: 200
    },
    {
        text: '参数值',
        dataIndex: 'paramDefaultValue',
        width: 200,
        editor: {
            allowBlank: true
        }
    },
    {
        text: '参数描述',
        dataIndex: 'paramDesc',
        flex: 1
    },
    {
        text: '参数顺序',
        dataIndex: 'paramOrder',
        width: 200
    }];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var paramGrid = Ext.create('Ext.grid.Panel', {
        width: '100%',
        height: 200,
        store: paramStore,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns,
        renderTo: 'script-param-start_fortest'
    });
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: serviceId_fortest,
            ids: []
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    
    var attachmentColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        width: 200
    },
    {
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    }];
    
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
        width: '100%',
        height: 200,
        store: attachmentStore,
        border: true,
        columnLines: true,
        columns: attachmentColumns,
        renderTo: 'script-attachment-start_fortest'
    });
	
});

function deleteChosedAgent(agentId) {
	console.log('sdfsdfasd',agentId);
	console.log('age', agent_chosed_store_fortest.indexOfId(agentId+""));
	agent_chosed_store_fortest.removeAt(agent_chosed_store_fortest.indexOfId(agentId+""));
//	agent_chosed_ids.remove(agentId);
//	agent_chosed_store_fortest.reload();
}