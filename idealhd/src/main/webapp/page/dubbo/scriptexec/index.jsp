<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<html>
<head>
<script type="text/javascript">
var db_f_class_index = <%=request.getAttribute("db_f_class")%>;
var db_s_class_index = <%=request.getAttribute("db_s_class")%>;
var db_s_level_index = <%=request.getAttribute("db_s_level")%>;
var db_serviceType_index = <%=request.getAttribute("db_serviceType")%>;
var db_scriptType_index = <%=request.getAttribute("db_serviceType")%>;
var db_dbType_index = <%=request.getAttribute("db_dbType")%>;
var ss_pubssuer_index = <%=request.getAttribute("db_ssuer")%>;
var ss_tablename_index = <%=request.getAttribute("db_tablename")%>;
var db_projectFlag_index=<%=request.getAttribute("db_projectFlag")==null?0:request.getAttribute("db_projectFlag")%>;
var serviceIdSwitch_index=<%=request.getAttribute("serviceIdSwitch")%>;
var sqlexoprtswitch_index=<%=request.getAttribute("sqlexoprtswitch")%>;
var isuser=<%=request.getAttribute("isuser")%>;

var tempData = {};
<%
String menuId_index = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptexec/index.js"></script>
</head>
<body>
<div id="scriptexec_area" style="width: 100%;height: 100%">
</div>
</body>
</html>