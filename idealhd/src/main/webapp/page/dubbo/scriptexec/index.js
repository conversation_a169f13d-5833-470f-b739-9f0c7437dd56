var showConfService_window;
Ext.onReady(function() {
	destroyRubbish();
	var labels;
	// 将通过jsp跳转传递的变量全部进行转移和清理。要注意变量的值传递可以直接清理。变量如果传递的是引用，注意不要把真实值清理掉，清理引用即可。
	var menuId = tempData.menuId_index;
	var publishAuditingSMWin;
	var bussData = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'bsName' ],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsAll.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var bussTypeData = Ext.create('Ext.data.Store', {
		fields : [ 'sysTypeId', 'sysType' ],
		autoLoad : false,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsTypeByFk.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var cataStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "-1",
			"name" : "全部"
		}, {
			"id" : "sh",
			"name" : "shell"
		}, {
			"id" : "bat",
			"name" : "bat"
		}, {
			"id" : "py",
			"name" : "python"
		}, {
			"id" : "perl",
			"name" : "perl"
		},
		{
			"id" : "-2",
			"name" : "组合"
		}

		]
	});
    var serviceTypeStore= Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "0",
            "name": "应用"
        },
        {
            "id": "1",
            "name": "采集"
        }
        ]
    });
    var serviceS="";
    if(db_projectFlag_index==1){
    	serviceS=serviceTypeStore;
    }else{
    	serviceS=cataStore;
    }
    var serviceType = Ext.create('Ext.form.field.ComboBox', {
        name: 'serviceType',
        labelWidth: 70,
        labelAlign: 'right',
        width: '20%',
        queryMode: 'local',
        fieldLabel: '服务类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        hidden:db_serviceType_index,
        emptyText: '--请选择服务类型--',
        store: serviceS,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'sysName',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		queryMode : 'local',
		fieldLabel : '一级分类',
		displayField : 'bsName',
		valueField : 'iid',
		editable : true,
		hidden:db_f_class_index,
		emptyText : '--请选择一级分类--',
		store : bussData,
		listeners : {
			change : function() { // old is keyup
				bussTypeCb.clearValue();
				bussTypeCb.applyEmptyText();
				bussTypeCb.getPicker().getSelectionModel().doMultiSelect([],
						false);
				bussTypeData.load({
					params : {
						fk : this.value
					}
				});
			},
			 specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
		}
	});
	/** 二级分类* */
	var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussType',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		queryMode : 'local',
		fieldLabel : '二级分类',
		displayField : 'sysType',
		valueField : 'sysTypeId',
		editable : true,
		hidden : db_s_class_index,
		emptyText : '--请选择二级分类--',
		store : bussTypeData,
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            },
			change: function() { // old is keyup
				threeBussTypeCb.clearValue();
				threeBussTypeCb.applyEmptyText();
				threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
				if(this.value!==null){
					threeBussTypeData.load({
						params: {
							fk: this.value
						}
					});
				}
			},
			beforequery: function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = Ext.util.Format.trim(e.query);
					combo.store.filterBy(function(record, id) {
						var text = record.get(combo.displayField);
						return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
					});
					combo.expand();
					return false;
				}
			}
        }
	});

	//北京邮储 三级分类
	var threeBussTypeData = Ext.create('Ext.data.Store', {
		fields: ['threeBsTypeId', 'threeBsTypeName'],
		autoLoad: false,
		proxy: {
			type: 'ajax',
			url: 'bsManager/getThreeBsTypeByFk.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	/*threeBussTypeData.on('load', function(store, options) {
		if(threeBsTypeId) {
			threeBussTypeCb.setValue(threeBsTypeId);
		}
	});*/
	var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
		name: 'threeBussTypeCb',
		labelWidth: 65,
		queryMode: 'local',
		fieldLabel: '三级分类',
		displayField: 'threeBsTypeName',
		valueField: 'threeBsTypeId',
		editable: true,
		width : '20%',
		labelAlign : 'right',
		hidden:db_s_class_index || !scriptThreeBstypeSwitch,
		emptyText: '--请选择三级分类--',
		store: threeBussTypeData,
		listeners: {
			beforequery: function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = Ext.util.Format.trim(e.query);
					combo.store.filterBy(function(record, id) {
						var text = record.get(combo.displayField);
						return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
					});
					combo.expand();
					return false;
				}
			}
		}
	});
	/** 脚本类型* */
	var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptTypeParam',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		queryMode : 'local',
		fieldLabel : '脚本类型',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		hidden:db_scriptType_index,
		emptyText : '--请选择脚本类型--',
		store : cataStore,
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var sName = new Ext.form.TextField({
		name : 'serviceName',
		fieldLabel : '服务名称',
		displayField : 'serverName',
		emptyText : '--请输入服务名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	var serviceId = new Ext.form.TextField({
	    name : 'serviceId',
	    fieldLabel : '服务号',
	    displayField : 'serviceId',
	    emptyText : '--请输入服务号--',
	    hidden:serviceIdSwitch_index,
	    labelWidth : 70,
	    labelAlign : 'right',
	    width : '20%',
	    listeners: {
	    specialkey: function(field, e){
	    if (e.getKey() == e.ENTER) {
	        pageBar.moveFirst();
	    }
	}
	}
	});
	var scName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel : '脚本名称',
		displayField : 'scriptName',
		emptyText : '--请输入脚本名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	var statusStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "-1",
			"name" : "全部"
		}, {
			"id" : "1",
			"name" : "启用"
		}, {
			"id" : "2",
			"name" : "禁用"
		} ]
	});
	var emScriptStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "-1",
			"name" : "全部"
		}, {
			"id" : "0",
			"name" : "否"
		}, {
			"id" : "1",
			"name" : "是"
		} ]
	});

	var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'emScript',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '是否应急',
		hidden : !reviewSwitch,
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择是否应急--',
		store : emScriptStore,
		width : '20.3%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	Ext.define('AppSysModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		} ]
	});
	var appSysStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'AppSysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var appSysObj = Ext.create('Ext.form.field.ComboBox', {
		name : 'appSys',
		fieldLabel : '所属系统',
		emptyText : '--请选择所属系统--',
		hidden : !reviewSwitch,
		labelWidth : 70,
		labelAlign : 'right',
		width : '20.3%',
		store : appSysStore,
		displayField : 'name',
		valueField : 'id',
		triggerAction : 'all',
		queryMode : 'local',
		listeners : {
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = Ext.util.Format.trim(e.query);
					combo.store
							.filterBy(function(record, id) {
								var text = record.get(combo.displayField);
								return (text.toLowerCase().indexOf(
										value.toLowerCase()) != -1);
							});
					combo.expand();
					return false;
				}
			},
			specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
		}
	});

	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true
	});
	var searchItems=[ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : [ serviceType, scriptTypeParam, appSysObj, emScriptCb,{
				fieldLabel : '服务状态',
				labelWidth : 70,
				labelAlign : 'right',
				width : '20.3%',
				name : 'status',
				displayField : 'name',
				valueField : 'id',
				store : statusStore,
				queryMode : 'local',
				listeners : {
					afterRender : function(combo) {
						combo.setValue(statusStore.getAt(0).data.id);
					},
					 specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			            }

				},
				editable : false,
				xtype : 'combobox'
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function() {
					pageBar.moveFirst();
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			}]
		},{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items: [ sName, scName,bussCb, bussTypeCb,threeBussTypeCb]
		}];
	if(db_projectFlag_index==1){
		searchItems=[ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			dock : 'top',
			items : [ sName, serviceType,scName,serviceId, scriptTypeParam, bussCb, bussTypeCb,{
				fieldLabel : '状态',
				labelWidth : 40,
				// padding : '0 10 0 10',
				labelAlign : 'right',
				width : '15%',
				name : 'status',
				displayField : 'name',
				valueField : 'id',
				store : statusStore,
				queryMode : 'local',
				listeners : {
					afterRender : function(combo) {
						combo.setValue(statusStore.getAt(0).data.id);
					}
				},
				editable : false,
				xtype : 'combobox'
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function() {
					pageBar.moveFirst();
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				// columnWidth:.07,
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			},'->',{
				text : '启用',
				cls : 'Common_Btn',
				handler : startServiceRelease
			}, {
				text : '禁用',
				cls : 'Common_Btn',
				handler : deleteServiceRelease
			},{
				text : '删除',
				cls : 'Common_Btn',
				hidden:isuser,
				handler : deleteService
			} ,{
				text : '删除',
				cls : 'Common_Btn',
				hidden:isuser==false,
				handler : userManageDeleteService
			}, {
                text : '表结构导出',
                cls : 'Common_Btn',
                hidden : !isuser,
                handler : exportFun
            }]
		} ];
		scName.hide();scriptTypeParam.hide(); bussCb.hide();bussTypeCb.hide();
	}
	var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		baseCls:'customize_gray_back',
		border : false,
		bodyCls : 'x-docked-noborder-top',
		dockedItems : searchItems
	});
	Ext.define('scriptService', {
			extend : 'Ext.data.Model',
			fields : [ {
				name : 'iid',
				type : 'string'
			}, {
				name : 'serviceName',
				type : 'string'
			}, {
				name : 'sysName',
				type : 'string'
			}, {
				name : 'bussName',
				type : 'string'
			}, {name : 'threeBsTypeName'    ,type : 'string'},{
				name : 'serviceType',
				type : 'string'
			}, {
				name : 'dbType',
				type : 'string'
			}, {
				name : 'scriptType',
				type : 'string'
			}, {
				name : 'scriptName',
				type : 'string'
			}, {
				name : 'servicePara',
				type : 'string'
			}, {
				name : 'platForm',
				type : 'string'
			},{
				name : 'ssuer',
				type : 'string'
			}, {
				name : 'status',
				type : 'int'
			}, {
				name : 'content',
				type : 'string'
			}, {
				name : 'version',
				type : 'string'
			},{
				name : 'serviceTy',
				type : 'String'
			}, {
				name : 'startType',
				type : 'String'
			},{
				name : 'bussId',
				type : 'int'
			}, {
				name : 'bussTypeId',
				type : 'int'
			}, {
				name : 'scriptLevel',
				type : 'int'
			}, {
				name : 'isFlow',
				type : 'string'
			}, {
				name : 'isEMscript',
				type : 'string'
			}, {
				name : 'iappSysIds',
				type : 'string'
			}, {
				name : 'iappSysNames',
				type : 'string'
			} , {
				name : 'outTableName',
				type : 'string'
			}, {
				name : 'sqlModel',
				type : 'string'
			}, {
                name : 'serviceId',
                type : 'string'
            }]
		});
	var scriptservice_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'scriptService',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var isOrNotStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "0",
			"name" : "否"
		}, {
			"id" : "1",
			"name" : "是"
		} ]
	});

	var isOrNotCombo = Ext.create('Ext.form.ComboBox', {
		margin : '5',
		store : isOrNotStore,
		queryMode : 'local',
		width : 600,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'name',
		valueField : 'id',
		editable : false,
		triggerAction : "all"
	});

	Ext.define('AppSysModel1', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		} ]
	});
	var appSysStore1 = Ext.create('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		model : 'AppSysModel1',
		proxy : {
			type : 'ajax',
			url : 'getAppSysList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	appSysStore1.on('load', function() {
		var ins_rec = Ext.create('AppSysModel', {
			id : '-1',
			name : '未选系统'
		});
		appSysStore1.insert(0, ins_rec);

	});
	var clickGroudRow;
	var appSysCombo = Ext.create('Ext.form.field.ComboBox', {
		multiSelect : true,
		labelWidth : 70,
		labelAlign : 'right',
		width : '80%',
		store : appSysStore1,
		padding : '0 0 5 0',
		displayField : 'name',
		valueField : 'id',
		triggerAction : 'all',
		editable: false,
		queryMode : 'local',
		listeners : {
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = Ext.util.Format.trim(e.query);
					combo.store
							.filterBy(function(record, id) {
								var text = record.get(combo.displayField);
								return (text.toLowerCase().indexOf(
										value.toLowerCase()) != -1);
							});
					combo.expand();
					return false;
				}
			},
			select : function(combo, records, eOpts) {
				var record = scriptservice_store.getAt(clickGroudRow);
				var selectValue = combo.getValue();
				var backValue = "";
				for (var i = 0; i < selectValue.length; i++) {
					if (i == 0) {
						backValue = "" + selectValue[i];
					} else {
						backValue = backValue + "," + selectValue[i];
					}
				}
				record.data.iappSysIds = backValue;
			}
		}
	});

	var scriptservice_columns = [
                 {
                	 text : '序号',
                	 xtype : 'rownumberer',
                	 width : 70
                 },
                 {
                	 text : '主键',
                	 dataIndex : 'iid',
                	 hidden : true
                 },
                 {
                     text : '服务号',
                     dataIndex : 'serviceId',
                     width : 300,
                     flex:1,
                     hidden:serviceIdSwitch_index,
                     renderer : function(value, metadata) {
                     metadata.tdAttr = 'data-qtip="' + value + '"';
                     return value;
                 }	
                 },
                 {
                	 text : '服务名称',
                	 dataIndex : 'serviceName',
					 width : 300,
					 flex:1,
         			 renderer : function(value, metadata) {
        				metadata.tdAttr = 'data-qtip="' + value + '"';
        				return value;
        			}	
                 },
                 {
                	 text : '脚本名称',
                	 dataIndex : 'scriptName',
					 width : 300,
					  flex:1,
         			 renderer : function(value, metadata) {
            				metadata.tdAttr = 'data-qtip="' + value + '"';
            				return value;
            			}	
                 },
                 {
                	 text : '适用平台',
                	 dataIndex : 'platForm',
                	 hidden : true,
                	 width : 100
                 },
                 {
                	 text : '一级分类',
                	 dataIndex : 'sysName',
					 width : 150,
					  flex:1,
                	 hidden:db_f_class_index,
         			 renderer : function(value, metadata) {
            				metadata.tdAttr = 'data-qtip="' + value + '"';
            				return value;
            			}	
                 },
                 {
                	 text : '二级分类',
                	 dataIndex : 'bussName',
					 width : 150,
					  flex:1,
                	 hidden:db_s_class_index,
         			 renderer : function(value, metadata) {
            				metadata.tdAttr = 'data-qtip="' + value + '"';
            				return value;
            			}	
                 }, {
					text : '三级分类',
					dataIndex : 'threeBsTypeName',
					hidden:db_s_class_index||!scriptThreeBstypeSwitch,
					width :100
				 },{
                	 text : '服务类型',
                	 dataIndex : 'serviceType',
                	 hidden:db_serviceType_index,
                	 width : 80,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == 0) {
                			 backValue = "应用";
                		 } else if (value == 1) {
                			 backValue = "采集";
                		 }	
                		 return backValue;
                	 }
                 }, {
                	 text : '数据库类型',
                	 dataIndex : 'dbType',
                	 hidden:db_dbType_index,
                	 width : 100
                 },
                 {
                	 text : '脚本类型',
                	 dataIndex : 'scriptType',
                	 width : 80,
                	 hidden:db_scriptType_index,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == "sh") {
                			 backValue = "shell";
                		 } else if (value == "perl") {
                			 backValue = "perl";
                		 } else if (value == "py") {
                			 backValue = "python";
                		 } else if (value == "bat") {
                			 backValue = "bat";
                		 } else if (value == "sql") {
                			 backValue = "sql";
                		 } else if (value == "ps1") {
                			 backValue = "powershell";
                		 }
                		 if (record.get('isFlow') == '1') {
                			 backValue = "组合";
                		 }
                		 return backValue;
                	 }
                 }, {
         			text : '发布人',
        			dataIndex : 'ssuer',
        			hidden : ss_pubssuer_index,
        			width : 150,
         			 renderer : function(value, metadata) {
            				metadata.tdAttr = 'data-qtip="' + value + '"';
            				return value;
            			}	
        		},{
                	 text : '版本',
                	 dataIndex : 'version',
                	 width :db_projectFlag_index=='1'?60:150
                 },/*{
                	 text : '展示配置',
                	 width : 150,
                	 hidden:db_projectFlag_index=='1'?true:false,
                	 dataIndex : 'showConfig',
                	 renderer : function(value, p, record, rowIndex) {
     					var iid = record.get('iid');
     					var itname = record.get('serviceName');
     					return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showConfigService_window(' + iid + ',\''+itname+'\')">'
     							+ '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;展示配置' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
                	 }
                },*/{
                	 text : '风险级别',
                	 dataIndex : 'scriptLevel',
                	 width : 120,
                	 hidden:db_s_level_index,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == 1) {
                			 backValue = '<font color="#F01024">高级风险</font>';
                		 } else if (value == 2) {
                			 backValue = '<font color="#FF7824">中级风险</font>';
                		 } else if (value == 3) {
                			 backValue = '<font color="#FFA826">低级风险</font>';
                		 }else if (value == 0) {
                			 backValue = '<font color="#FFA826">白名单</font>';
                		 }
                		 return backValue;
                	 }
                 },
                 {
                	 text : '是否应急',
                	 dataIndex : 'isEMscript',
                	 hidden : !reviewSwitch,
                	 width : 75,
                	 editor : isOrNotCombo,
                	 renderer : function(value, metadata, record) {
                		 var index = isOrNotStore.find('id', value);
                		 if (index != -1) {
                			 return isOrNotStore.getAt(index).data.name;
                		 } else {
                			 return '';
                		 }
                	 }
                 },
                 {
                	 text : '所属系统1',
                	 dataIndex : 'iappSysIds',
                	 hidden : true,
                	 width : 50,
                	 editor : {
                		 
                	 }
                 },
                 {
                	 text : '所属系统',
                	 dataIndex : 'iappSysNames',
                	 hidden : !reviewSwitch,
                	 width : 50,
                	 flex : 1,
                	 editor : appSysCombo,
                	 renderer : function(value, metadata, record) {
                		 var iappSysIds = record.data.iappSysIds;
                		 var value1 = iappSysIds.split(',');
                		 var str = '';
                		 for (var i = 0, len = iappSysIds.length; i < len; i++) {
                			 var index = appSysStore1.find('id', value1[i]);
                			 if (index != -1) {
                				 if (str != '') {
                					 str = str + "|"
                					 + appSysStore1.getAt(index).data.name;
                				 } else {
                					 str = str + appSysStore1.getAt(index).data.name;
                				 }
                			 }
                		 }
                		 record.data.iappSysNames = str;
                		 return str;
                	 }
                 
                 },
                 {
                	 text : '状态',
                	 dataIndex : 'status',
                	 width :db_projectFlag_index=='1'?60:80, 
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == 1) {
                			 backValue = "启用";
                		 } else if (value == 2) {
                			 backValue = "禁用";
                		 }
                		 return backValue;
                	 }
                 }, {
         			text : '结果表名称',
        			dataIndex : 'outTableName',
        			hidden : ss_tablename_index,
					width : 150,
					editor : {
        				xtype : 'textfield'
        			},
        			renderer : function(value, metadata) {
        				metadata.tdAttr = 'data-qtip="' + value + '"';
        				return value;
        			}	
        		},
        		 {
         			text : '服务运行方式',
        			dataIndex : 'sqlModel',
        			hidden : true,
        			width : 20
        		},{
        			text : '操作',
        			xtype : 'actiontextcolumn',
        			dataIndex: 'xq',
        			width : 100,
        			items : [
        			{
        				text : '查看',
        				iconCls : 'monitor_search',
        				handler : function(grid, rowIndex) {
        					var iid = grid.getStore().data.items[rowIndex].data.iid; 
        					var isflow = grid.getStore().data.items[rowIndex].data.isFlow; 
        					var isflow1 = grid.getStore().data.items[rowIndex].data.isFlow; 
        					var serviceName = grid.getStore().data.items[rowIndex].data.serviceName; 
        					var bussId = grid.getStore().data.items[rowIndex].data.bussId; 
        					var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId; 
        					var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
							Ext.Ajax.request({
								url: 'queryScriptLabelsByServiceiid.do',
								method: 'POST',
								async: false,
								params: {
									iid:iid
								},
								success: function(response, request) {
									var lab = Ext.decode(response.responseText).dataList;
									labels = lab.join(',')
								},
								failure: function(result, request) {
								}
							});
        					if(!noScriptConvertSwitch){
        						viewDetailForTaskManagerForFlow(iid,serviceName,bussId,bussTypeId,scriptLevel,isflow1,menuId);
        					}else{
        						viewDetailForTaskManager(iid,labels,serviceName,bussId,bussTypeId,scriptLevel,isflow1,menuId);
        					}
        				}
        			}]
                 },{
         	        text: '分析算法',
        	        dataIndex: '',
        	        width: 100,
        	        hidden : true,
        	        renderer: function(value, p, record) {
        	            var iid = record.get('iid');
        	            return '<a href="javascript:void(0)" onclick="setAnalyzeFun(' + iid + ')" style="">算法定义</a>' ;
        	        }
        	    } ];

	scriptservice_store.on('beforeload',function(store, options) {
	var new_params = {
				serviceName : search_form.getForm()
						.findField("serviceName").getValue(),
				status : search_form.getForm().findField("status")
						.getValue(),
				bussId : bussCb.getValue() || 0,
				bussTypeId : search_form.getForm().findField("bussType")
						.getValue() || 0,
				threeBsTypeId:search_form.getForm().findField("threeBussTypeCb")
					.getValue()||0,
				scriptName : search_form.getForm().findField("scriptName")
						.getValue()
						|| '',
				scriptType : search_form.getForm().findField(
						"scriptTypeParam").getValue()
						|| '',
				isEMscript : (null != emScriptCb.getValue()) ? emScriptCb
						.getValue() : -1,
				iappSysIds : appSysObj.getValue(),
				serviceType : search_form.getForm().findField("serviceType").getValue()||-1,
//				serviceId : search_form.getForm().findField("serviceId").getValue(),
				switchFlag:db_projectFlag_index
			};
			Ext.apply(scriptservice_store.proxy.extraParams, new_params);
		});

	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : scriptservice_store,
		dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		border:false,
		displayInfo : true,
		emptyMsg: '找不到任何记录'
	});
	appSysStore1.load({
		callback : function(records, operation, success) {
			scriptservice_store.load();
		}
	});
	
	var scriptservice_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'center',
		store : scriptservice_store,
		cls:'customize_panel_back',
		plugins : [ Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 2
		}) ],
		border:true,	
		// padding : panel_margin,
//		bbar : pageBar,
		padding : grid_space,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		columnLines : true,
		columns : scriptservice_columns,
		selModel : selModel,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
//			baseCls:'customize_gray_back',
			layout: {pack: 'center'},
			items : [ '->',{
				text : '启用',
				cls : 'Common_Btn',
				hidden : db_projectFlag_index==1?true:false,
				handler : startServiceRelease
			}, {
				text : '禁用',
				cls : 'Common_Btn',
				hidden : db_projectFlag_index==1?true:false,
				handler : deleteServiceRelease
			}, {
				text : '删除',
				itemId : 'delete',
				cls : 'Common_Btn',
//				disabled : true,
				hidden : db_projectFlag_index==1?true:false,
				handler : deleteService
			},{
				text : '保存',
				cls : 'Common_Btn',
				hidden : !reviewSwitch,
				handler : saveFun
			}, {
				text : '解绑',
				cls : 'Common_Btn',
				hidden : !reviewSwitch,
				handler : deleteFun
			}]
	    }]
	});
	
	scriptservice_grid.on("celldblclick", function(obj, td, cellIndex, record,tr, rowIndex, e, eOpts) {
		clickGroudRow = rowIndex;
	})
    scriptservice_grid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        scriptservice_grid.down('#delete').setDisabled(selections.length === 0);
//        search_form.down('#del').setDisabled(selections.length === 0);
    });
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptexec_area",
		layout : 'border',
		cls:'customize_panel_back',
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		bodyPadding : grid_margin,
		border : true,
		bodyCls:'service_platform_bodybg',
		// bodyPadding : 5,
		items : [ search_form, scriptservice_grid ]
	});
	contentPanel.on('resize', function() {
		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.setHeight() - modelHeigth);
	});
	function clearQueryWhere() {
		search_form.getForm().findField("serviceName").setValue('');
		search_form.getForm().findField("status").setValue('-1');
		search_form.getForm().findField("sysName").setValue('');
		search_form.getForm().findField("threeBussTypeCb").setValue('');
		search_form.getForm().findField("bussType").setValue('');
		search_form.getForm().findField("scriptName").setValue('');
//		search_form.getForm().findField("serviceId").setValue('');
		search_form.getForm().findField("scriptTypeParam").setValue('');
		search_form.getForm().findField("appSys").setValue('');
		search_form.getForm().findField("emScript").setValue(-1);
		serviceType.clearValue();
	}
	function saveFun() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		var m = scriptservice_store.getModifiedRecords();
		if (m.length == 0) {
			Ext.MessageBox.alert("提示", "没有需要保存的条目！");
			return;
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0) {
				jsonData = jsonData + ss;
			} else {
				jsonData = jsonData + "," + ss;
			}
		}
		jsonData = jsonData + "]";
		Ext.Ajax.request({
			url : 'updateScriptAndRelation.do',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					scriptservice_store.load();
					Ext.Msg.alert("提示", message);
				} else {
					Ext.Msg.alert("提示", message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "请求返回失败！", request);
			}
		});
	}

	function deleteFun() {
		var seledCnt = selModel.getCount();
		if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要解绑的服务！");
			return;
		}
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg
				.confirm(
						"确认删除",
						"是否解绑选中的服务",
						function(id) {
							if (id == 'yes') {
								var signNum = 0;
								var ids = '';
								var flowIdList = scriptservice_grid
										.getSelectionModel().getSelection();
								for (var i = 0, len = flowIdList.length; i < len; i++) {
									if (signNum == 0) {
										ids = ids + flowIdList[i].data.iid;
									} else {
										ids = ids + ","
												+ flowIdList[i].data.iid;
									}
									signNum = signNum + 1;
								}
								Ext.Ajax
										.request({
											url : 'deleteScriptAndRelation.do',
											params : {
												ids : ids
											},
											success : function(response,
													request) {
												var success = Ext
														.decode(response.responseText).success;
												var message = Ext
														.decode(response.responseText).message;
												if (success) {
													scriptservice_store.load();
													Ext.Msg
															.alert("提示",
																	message);
												} else {
													Ext.Msg
															.alert("提示",
																	message);
												}
											},
											failure : function(result, request) {
												secureFilterRs(result,
														"请求返回失败！", request);
											}
										});
							}
						});

	}
function exportFun() {
    Ext.MessageBox.wait("数据处理中...", "进度条");
       Ext.Ajax.request({
                url : 'exportTablefromdb.do',
                params : {},
                success : function(response, request) {
                    var success = Ext .decode(response.responseText).success;
                    var message = Ext .decode(response.responseText).message;
                    Ext.Msg .alert("提示", message);
                },
                failure : function(result, request) {
                        secureFilterRs(result, "请求返回失败！", request);
                }
            });
	}

	function startServiceRelease() {
		var seledCnt = selModel.getCount();
		if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要启用的服务！");
			return;
		}
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认删除", "是否启用选中的服务", function(id) {
			if (id == 'yes')
				release(2);
		});
	}
	
	
	function userManageDeleteService(){
		var seledCnt = selModel.getCount();
		if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要删除的服务！");
			return;
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认删除", "是否删除选中的服务", function(id){if(id=='yes'){
			var message="服务删除成功";
			var errorMessage="服务删除失败";
			var flowIdList = scriptservice_grid.getSelectionModel().getSelection();
			var jsonData = "[";
			var signNum = 0;
			for (var i = 0, len = flowIdList.length; i < len; i++) {
					if (signNum == 0) {
						jsonData = jsonData + '{"iid":"'
								+ parsIIDJson('iid', flowIdList[i].data) + '"}';
					} else {
						jsonData = jsonData + "," + '{"iid":"'
								+ parsIIDJson('iid', flowIdList[i].data) + '"}';
					}
					signNum = signNum + 1;
			}
			jsonData = jsonData + "]";
			Ext.Ajax.request({
				url : 'scriptService/manageDeleteScript.do',
				method : 'POST',
				params : {
					jsonData : jsonData,
				},
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						Ext.MessageBox.show({
							title : "提示",
							msg : message,
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					} else {
						Ext.MessageBox.show({
							title : "提示",
							msg : errorMessage,
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					}
					scriptservice_store.reload();
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		} 
		});
	}
	
	
	
		
	function deleteService() {
		var seledCnt = selModel.getCount();
		if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要删除的服务！");
			return;
		}
		if(seledCnt > 1){
			Ext.MessageBox.alert("提示", "每次只能删除一个服务！");
			return ;
		}
		var warnningWin;
		var isAudiFlag=false;
		var ss = selModel.getSelection();
		var serviceId =ss[0].data.iid;
		Ext.Ajax.request({
            url: 'queryIsAuditing.do',
            method: 'POST',
            async: false,
            params: {
            	serviceId: serviceId
            },
            success: function(response, options) {
            	isAudiFlag = Ext.decode(response.responseText).isAudiFlag;
            },
            failure: function(result, request) {
            }
        });
		if(isAudiFlag){
			Ext.MessageBox.alert("提示", "该服务有任务没有完成，不能删除！");
			return ;
		}
		//控制任务权限删除常用任务脚本
		var serviceName = ss[0].data.serviceName.toString();
		var version = ss[0].data.version.toString();
		var isTemplateFlag=false;
		var commonTaskName = '';
		Ext.Ajax.request({
			url: 'getCommonTask.do',
			method: 'POST',
			async: false,
			params: {
				serviceName: serviceName,
				version: version
			},
			success: function(response, options) {
				//判断是否有常用任务标识
				isTemplateFlag = Ext.decode(response.responseText).isTemplateFlag;
				if(Ext.decode(response.responseText).commonTaskNameList != null){
					for(let i = 0;i < Ext.decode(response.responseText).commonTaskNameList.length;i++) {
						if (i === 0) {
							commonTaskName = Ext.decode(response.responseText).commonTaskNameList[i];
						} else {
							commonTaskName = commonTaskName + " , " + Ext.decode(response.responseText).commonTaskNameList[i];
						}
					}
				}
			},
			failure: function(result, request) {
			}
		});
		if(isTemplateFlag){
			Ext.MessageBox.alert("提示", "当前脚本此版本存在常用任务【常用任务名:" + commonTaskName + "】，需手动删除常用任务后才允许删除此脚本！");
			return ;
		}


		Ext.define('warnningModel', {
		    extend : 'Ext.data.Model',
		    fields : [{
		      name : 'iid',
		      type : 'int'
		    }, {
		          name : 'serviceName',
		          type : 'string'
		    }, {
		          name : 'sysName',
		          type : 'string'
		    }, {
			      name : 'bussName',
			      type : 'string'
			}, {
				  name : 'version',
				  type : 'string'
			}, {
				  name : 'user',
				  type : 'string'
			}]
		  });
		var warnningStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    autoDestroy: true,
		    model: 'warnningModel',
		    proxy: {
		      type: 'ajax',
		      url: 'scriptCallSearch.do',
		      reader: {
		        type: 'json',
		        root: 'dataList',
		        totalProperty: 'totalCount'
		      }
		    }
		  });
		warnningStore.on('beforeload', function (store, options) {
		    	var new_params = {  
		    			iid : serviceId
		    	};
		    	
		    	Ext.apply(warnningStore.proxy.extraParams, new_params);
		    });
		var warnningColumns = [{ text: '序号', xtype:'rownumberer', width: 40 },
			             		{ text: '主键',  dataIndex: 'iid',hidden:true},
			             		{ text: '服务名称',  dataIndex: 'serviceName',flex:1},
			             		{ text: '一级分类',  dataIndex: 'sysName',width:120},
			             		{ text: '二级分类',  dataIndex: 'bussName',width:100},
			                    { text: '版本',  dataIndex: 'version', width: 80,renderer:function(value,p,record,rowIndex){
			        				if(value) {
			        					return value;
			        				} else {
			        					return '无版本号';
			        				}
			        			}},
			                    { text: '创建用户',  dataIndex: 'user',width:120}
			             		];
		var warnningGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
			region: 'center',
			autoScroll: true,
		    store : warnningStore,
		    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		    border:false,
		    columnLines : true,
		    columns : warnningColumns
		});
		
		Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var auditorStore_sm = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getDeleteAuditorList.do?dbaasFlag='+db_projectFlag_index,
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		

	var	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
		    fieldLabel: "审核人",
		    labelWidth: 93,
//		    padding: 5,
		    store: auditorStore_sm,
		    queryMode: 'local',
//		    width: 200,
		    columnWidth:.98,
		    labelAlign : 'right',
		    margin : '10 0 0 0',
		    displayField: 'fullName',
		    editable : true,
		    valueField: 'loginName',//,
		    listeners: { //监听 
		        render : function(combo) {//渲染 
		            combo.getStore().on("load", function(s, r, o) { 
		                combo.setValue(r[0].get('loginName'));//第一个值 
		            });
		        },
		        select : function(combo, records, eOpts){ 
					var fullName = records[0].raw.fullName;
					combo.setRawValue(fullName);
				},
				beforequery: function(e) {
	                var combo = e.combo;
	                if (!e.forceAll) {
	                    var value = Ext.util.Format.trim(e.query);
	                    combo.store.filterBy(function(record, id) {
	                        var text = record.get(combo.displayField);
	                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
	                    });
	                    combo.expand();
	                    return false;
	                }
	            }
		    } 
		  });
	var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '删除申请说明',
        emptyText: '',
        labelWidth: 93,
        margin : '10 0 0 0',
         maxLength: 255, 
         labelAlign : 'right',
        height: 85,
        columnWidth:.98,
        autoScroll: true
    });
		var auditing_form_sm = Ext.create('Ext.form.Panel', {
			width: 560,
	    	layout : 'anchor',
	    	bodyCls : 'x-docked-noborder-top',
	    	baseCls:'customize_gray_back',
	    	buttonAlign : 'center',
	    	border : false,
		    items: [{
		    	anchor:'98%',
		    	padding : '5 0 5 5',
		    	border : false,
		    	items: [{
		    		layout:'column',
			    	border : false,
			    	items:[auditorComBox_sm]
		    	},{
		    		layout:'column',
			    	border : false,
			    	items:[pubDesc_sm]
		    	}]
		    }]
		});
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Ajax.request({
		    url : 'scriptCallSearch.do',
		    method : 'POST',
		    params : {
		    	iid : serviceId
		    },
		    success: function(response, opts) {
		        var success = Ext.decode(response.responseText).success;
		        if(success) {//有需要提示的内容
		        	if (!warnningWin) {
		        		warnningWin = Ext.create('widget.window', {
		                title: '提示信息：删除该记录,将影响以下作业!',
		                closable: true,
		                closeAction: 'hide',
		                modal: true,
		                width: 600,
		                minWidth: 350,
		                height: 300,
		                layout: {
		                    type: 'border',
		                    padding: 5
		                },
		                items: [warnningGrid1],
		                dockedItems : [ {
							xtype : 'toolbar',
							dock : 'bottom',
//							baseCls:'customize_gray_back',
							layout: {pack: 'center'},
							items : [ {
				  			xtype: "button",
				  			cls:'Common_Btn',
				  			text: "是", 
				  			handler: function () {
				  				this.up("window").close();
	  				        	if (!publishAuditingSMWin) {
	  								publishAuditingSMWin = Ext.create('widget.window', {
	  					                title: '确认审核信息',
	  					                closable: true,
	  					                closeAction: 'hide',
	  					                modal: true,
	  					                width: 600,
	  					                minWidth: 350,
	  					                height: reviewSwitch?330:250,
	  					                layout: {
	  					                    type: 'border',
	  					                    padding: 5
	  					                },
	  					                items: [auditing_form_sm],
	  					                dockedItems : [ {
	  										xtype : 'toolbar',
//	  										baseCls:'customize_gray_back',
	  										dock : 'bottom',
	  										layout: {pack: 'center'},
	  										items : [ {
	  							  			xtype: "button",
	  							  			cls:'Common_Btn',
	  							  			text: "确定", 
	  							  			handler: function () {
	  							  			Ext.Msg.confirm("确认删除", "是否删除选中的服务", function(id) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
	  											if (id == 'yes'){
	  										        	if (!publishAuditingSMWin) {
	  														publishAuditingSMWin = Ext.create('widget.window', {
	  											                title: '确认审核信息',
	  											                closable: true,
	  											                closeAction: 'hide',
	  											                modal: true,
	  											                width: 600,
	  											                minWidth: 350,
	  											                items: [auditing_form_sm],
	  											                dockedItems : [ {
	  																xtype : 'toolbar',
	  																dock : 'bottom',
	  																layout: {pack: 'center'},
	  																items : [ {
	  													  			xtype: "button",
	  													  			cls:'Common_Btn',
	  													  			text: "确定", 
	  													  			handler: function () { 
	  													  				
	  													  				//判断输入的审核人是否合法 start
	  												            	var auditorValue =auditorComBox_sm.getValue();
	  																if(!Ext.isEmpty(auditorValue)){
	  																	//判断输入是否合法标志，默认false，代表不合法
	  																	var flag = false;
	  																	//遍历下拉框绑定的store，获取displayField
	  																	auditorStore_sm.each(function (record) {
	  																		//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
	  																	    // var data_fullName = record.get('fullName');
	  																	    var loginName = record.get('loginName');
	  																	    if(auditorValue == loginName){
	  																	    	flag =true;
	  																	    	//combo.setValue(record.get('loginName'));
	  																	    }
	  																	});
	  																	if(!flag){
	  																	 	Ext.Msg.alert('提示', "输入的审核人非法");
	  																	 	auditorComBox_sm.setValue("");
	  																	 	return;
	  																	} 
	  																}
	  													  				var publishDesc = pubDesc_sm.getValue();
	  													  				var auditor = auditorComBox_sm.getValue();
	  													  				if(!publishDesc) {
	  													  					Ext.Msg.alert('提示', "没有填写删除申请说明！");
	  													  					return;
	  													  				}
	  													  				if(publishDesc.length > 255) {
	  													  					Ext.Msg.alert('提示', "删除申请说明内容长度超过255个字符！");
	  													  					return;
	  													  				}
	  													  				if(!auditor) {
	  													  					Ext.Msg.alert('提示', "没有选择审核人！");
	  													  					return;
	  													  				}
	  													  				
	  														    		var ss = selModel.getSelection();
	  													  				
	  													  				var sIds = new Array();
	  													  				sIds.push(ss[0].data.iid);
	  													  				Ext.Ajax.request({
	  													  				    url : 'scriptPublishAuditingDelete.do',
	  													  				    method : 'POST',
	  													  				    params : {
	  													  				    	sIds : sIds,
	  													  				    	publishDesc: publishDesc,
	  													  				    	auditor: auditor
	  													  				    },
	  													  				    success: function(response, opts) {
	  													  				        var success = Ext.decode(response.responseText).success;
	  													  				        var message = Ext.decode(response.responseText).message;
	  													  				        if(!success) {
	  													  				        	Ext.MessageBox.alert("提示", message);
	  													  				        } else {
	  													  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	  													  				        }
	  													  				      scriptservice_store.reload();
	  													  				      publishAuditingSMWin.close();
	  													  				      
	  													  				    },
	  													  				    failure: function(result, request) {
	  													  				    	secureFilterRs(result,"操作失败！");
	  													  				    	publishAuditingSMWin.close();
	  													  				    }
	  													  			    });
	  													  				
	  														        }
	  													  		}, { 
	  													  			xtype: "button", 
	  													  			cls:'Common_Btn',
	  													  			text: "取消", 
	  													  			handler: function () {
	  													  				this.up("window").close();
	  													  			}
	  													  		}]
	  														}]
	  											            });
	  											            
	  											        }
	  													publishAuditingSMWin.show();
	  													auditorStore_sm.load();
	  													pubDesc_sm.setValue('');
	  													auditorComBox_sm.setValue('');
	  											}
	  										        
	  										});
	  							  			}
	  							  		}, { 
	  							  			xtype: "button", 
	  							  			cls:'Common_Btn',
	  							  			text: "取消", 
	  							  			handler: function () {
	  							  				this.up("window").close();
	  							  			}
	  							  		}]
	  								}]
	  					            });
	  					            
	  					        }
	  				        	publishAuditingSMWin.show();
		    					auditorStore_sm.load();
		    					pubDesc_sm.setValue('');
		    					auditorComBox_sm.setValue('');
                                 }
				  		}, { 
				  			xtype: "button", 
				  			cls:'Common_Btn',
				  			text: "否", 
				  			handler: function () {
				  				this.up("window").close();
				  			}
				  		}]
					}]
		            });
		        	}
		      warnningWin.show();
		      warnningStore.load();
		        }else{
		        	Ext.Msg.confirm("确认删除", "是否删除选中的服务", function(id) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
		    			if (id == 'yes'){
		    		        	if (!publishAuditingSMWin) {
		    						publishAuditingSMWin = Ext.create('widget.window', {
		    			                title: '确认审核信息',
		    			                closable: true,
		    			                closeAction: 'hide',
		    			                modal: true,
		    			                width: 600,
		    			                minWidth: 350,
		    			                items: [auditing_form_sm],
		    			                dockedItems : [ {
		    								xtype : 'toolbar',
		    								dock : 'bottom',
		    								layout: {pack: 'center'},
		    								items : [ {
		    					  			xtype: "button",
		    					  			cls:'Common_Btn',
		    					  			text: "确定", 
		    					  			handler: function () { 
		    					  				
		    					  				//判断输入的审核人是否合法 start
												var auditorValue =auditorComBox_sm.getValue();
												if(!Ext.isEmpty(auditorValue)){
													//判断输入是否合法标志，默认false，代表不合法
													var flag = false;
													//遍历下拉框绑定的store，获取displayField
													auditorStore_sm.each(function (record) {
														//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
														// var data_fullName = record.get('fullName');
														var loginName = record.get('loginName');
														if(auditorValue == loginName){
															flag =true;
															//combo.setValue(record.get('loginName'));
														}
													});
													if(!flag){
														Ext.Msg.alert('提示', "输入的审核人非法");
														auditorComBox_sm.setValue("");
														return;
													}
												}
		    					  				var publishDesc = pubDesc_sm.getValue();
		    					  				var auditor = auditorComBox_sm.getValue();
		    					  				if(!publishDesc) {
		    					  					Ext.Msg.alert('提示', "没有填写删除申请说明！");
		    					  					return;
		    					  				}
		    					  				if(publishDesc.length > 255) {
		    					  					Ext.Msg.alert('提示', "删除申请说明内容长度超过255个字符！");
		    					  					return;
		    					  				}
		    					  				if(!auditor) {
		    					  					Ext.Msg.alert('提示', "没有选择审核人！");
		    					  					return;
		    					  				}
		    					  				
		    						    		var ss = selModel.getSelection();
		    					  				
		    					  				var sIds = new Array();
		    					  				sIds.push(ss[0].data.iid);
		    					  				Ext.Ajax.request({
		    					  				    url : 'scriptPublishAuditingDelete.do',
		    					  				    method : 'POST',
		    					  				    params : {
		    					  				    	sIds : sIds,
		    					  				    	publishDesc: publishDesc,
		    					  				    	auditor: auditor
		    					  				    },
		    					  				    success: function(response, opts) {
		    					  				        var success = Ext.decode(response.responseText).success;
		    					  				        var message = Ext.decode(response.responseText).message;
		    					  				        if(!success) {
		    					  				        	Ext.MessageBox.alert("提示", message);
		    					  				        } else {
		    					  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
		    					  				        }
		    					  				      scriptservice_store.reload();
		    					  				      publishAuditingSMWin.close();
		    					  				      
		    					  				    },
		    					  				    failure: function(result, request) {
		    					  				    	secureFilterRs(result,"操作失败！");
		    					  				    	publishAuditingSMWin.close();
		    					  				    }
		    					  			    });
		    					  				
		    						        }
		    					  		}, { 
		    					  			xtype: "button", 
		    					  			cls:'Common_Btn',
		    					  			text: "取消", 
		    					  			handler: function () {
		    					  				this.up("window").close();
		    					  			}
		    					  		}]
		    						}]
		    			            });
		    			            
		    			        }
		    					publishAuditingSMWin.show();
		    					auditorStore_sm.load();
		    					pubDesc_sm.setValue('');
		    					auditorComBox_sm.setValue('');
		    			}
		    		        
		    		});
		        }
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
	}
	function deleteServiceRelease() {
		var seledCnt = selModel.getCount();
		if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要禁用的服务！");
			return;
		}
		var flowIdList = scriptservice_grid.getSelectionModel().getSelection();
		for (var i = 0, len = flowIdList.length; i < len; i++) {
			var iidFordelete = flowIdList[i].data.iid;
			var serviceName = flowIdList[i].data.serviceName;
			var isAudiFlag=false;
			Ext.Ajax.request({
	            url: 'queryIsAuditing.do',
	            method: 'POST',
	            async: false,
	            params: {
	            	serviceId: iidFordelete
	            },
	            success: function(response, options) {
	            	isAudiFlag = Ext.decode(response.responseText).isAudiFlag;
	            },
	            failure: function(result, request) {
	            }
	        });
			if(isAudiFlag){
				Ext.MessageBox.alert("提示", "服务名为："+serviceName+"的任务没有完成，不能禁用！");
				return ;
			}
			
		}
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认禁用", "是否禁用选中的服务", function(id) {
			if (id == 'yes')
				release(3);
		});
	}

	function release(optionState) {
		var status = "2";
		var message = "服务启用成功";
		var signMessage = "选择的服务状态不符合【启用服务】操作，请重新选择";
		var errorMessage = "服务启用失败";
		if (optionState == 3) {
			message = "服务禁用成功";
			errorMessage = "服务禁用失败";
			signMessage = "选择的服务状态不符合【禁用服务】操作，请重新选择";
			status = 1;
		}
		var jsonData = getSelectedJsonData(status);

		if (jsonData == "[]") {
			Ext.MessageBox.alert("提示", signMessage);
			return;
		}
		Ext.Ajax.request({
			url : 'scriptService/serviceRelease.do',
			method : 'POST',
			params : {
				jsonData : jsonData,
				optionState : optionState
			},
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				// var message =
				// Ext.decode(response.responseText).message;
				if (success) {
					Ext.MessageBox.show({
						title : "提示",
						msg : message,
						buttonText : {
							yes : '确定'
						},
						buttons : Ext.Msg.YES
					});
				} else {
					Ext.MessageBox.show({
						title : "提示",
						msg : errorMessage,
						buttonText : {
							yes : '确定'
						},
						buttons : Ext.Msg.YES
					});
				}
				scriptservice_store.reload();
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}

	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedJsonData(serviceState) {
		var flowIdList = scriptservice_grid.getSelectionModel().getSelection();
		if (flowIdList.length < 1) {
			return;
		}
		var jsonData = "[";
		var signNum = 0;
		for (var i = 0, len = flowIdList.length; i < len; i++) {
			if (flowIdList[i].data.status == serviceState) {
				if (signNum == 0) {
					jsonData = jsonData + '{"iid":"'
							+ parsIIDJson('iid', flowIdList[i].data) + '"}';
				} else {
					jsonData = jsonData + "," + '{"iid":"'
							+ parsIIDJson('iid', flowIdList[i].data) + '"}';
				}
				signNum = signNum + 1;
			}
		}
		jsonData = jsonData + "]";
		return jsonData;
	}

	// 从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key, jsonObj) {
		var eValue = eval('jsonObj.' + key);
		return jsonObj['' + key + ''];
	}
	
	function viewDetailForTaskManagerForFlowGD(serviceId, serviceName, bussId, bussTypeId) {
		var DetailWinTi = Ext.create('widget.window', {
			title: '详细信息',
			closable: true,
			closeAction: 'destroy',
			width: contentPanel.getWidth(),
			minWidth: 350,
			height: contentPanel.getHeight(),
			draggable: false,
			resizable: false,
			modal: true,
			loader: {
				url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
				params: {
					iid:serviceId,
					serviceName:serviceName,
					actionType:'view',
					bussId:bussId,
					bussTypeId:bussTypeId,
					flag:0,
					isShowInWindow: 1
				},
				autoLoad: true,
				scripts: true
			}
		});
		DetailWinTi.show();
	}
	function setAnalyzeFun(iid){
		var mainP_Code = Ext.create('Ext.panel.Panel', {
			minHeight : 80,
			border : false,
			cls:'customize_panel_back',
			autoScroll : false,
			title : "分析算法定义框---",
			height : contentPanel.getHeight()-modelHeigth,
			html : '<textarea id="code_index" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
		});
		var afcenter = Ext.create('Ext.panel.Panel', {
			region : 'center',
		    renderTo   : Ext.getBody(),
			border : false,
			cls:'customize_panel_back',
			items : [ mainP_Code, {
		        xtype: 'hiddenfield',
		        name: 'flag',
		        value: '0'
		    }]
		});
		var editor = CodeMirror.fromTextArea(document.getElementById('code_index'), {
			mode : 'text/x-plsql',
			lineNumbers : true,
			matchBrackets : true,
			extraKeys : {
				"F11" : function(cm) {
					cm.setOption("fullScreen", !cm.getOption("fullScreen"));
				},
				"Esc" : function(cm) {
					if (cm.getOption("fullScreen"))
						cm.setOption("fullScreen", false);
				}
			}
		});
		
		var afStore = Ext.create('Ext.data.Store', {
	        fields: ['iid', 'ianlayzefuntext', 'ianalyzefunflag'],
	        autoLoad: true,
	        proxy: {
	            type: 'ajax',
	            url: 'queryAnalyzeFun.do',
	            reader: {
	                type: 'json',
	                root: 'dataList'
	            }
	        }
	    });
		
		afStore.on('beforeload', function(store, options) {
			var new_params = {
						iid : iid
			}
			Ext.apply(afStore.proxy.extraParams, new_params);
		});
		afStore.on('load', function(store, options) {
			if(null!=store.getAt(0).get('ianlayzefuntext') && store.getAt(0).get('ianlayzefuntext')!=''){
				editor.setOption('value', store.getAt(0).get('ianlayzefuntext'));
			}
//			afForm.getForm().findField("text").setValue(store.getAt(0).get('ianlayzefuntext'));
//			if(store.getAt(0).get('ianlayzefunflag') != '')
//				afForm.getForm().findField("flag").setValue(store.getAt(0).get('ianlayzefunflag'));
		});
		
		var afToolForm = Ext.create('Ext.form.Panel', {
			 region : 'north',
			 bodyCls : 'x-docked-noborder-top',
			 baseCls:'customize_gray_back',
			 border:false,
			 dockedItems :[ {
				xtype : 'toolbar',
				baseCls:'customize_gray_back',  
				dock : 'top',
				items : [ {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '保存',
				handler : function() {
					editor.save();
					var analyzefuntext=document.getElementById('code_index').value;//afForm.getForm().findField("text").getValue();
//					var analyzefunflag=afForm.getForm().findField("flag").getValue();
					//Ext.MessageBox.alert("提示", iid+","+analyzefuntext+","+analyzefunflag)
					if(analyzefuntext == ''){
						Ext.MessageBox.alert("提示","内容不能为空");
						return;
					}else{
						 Ext.Ajax.request({
					            url: 'saveAnalyzeFun.do',
					            method: 'POST',
					            params: {
					                iid : iid,
					                ianalyzefuntext : analyzefuntext
					            },
					            success: function(response, request) {
					                var success = Ext.decode(response.responseText).success;
					                var message = Ext.decode(response.responseText).message;
					                Ext.Msg.alert('提示', message);
					                if(success){
					                	afWin.close();
					                	afStore.reload();
					                }
					            }
					        });
					}
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '取消',
				handler : function() {
					afWin.close();
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '清除',
				handler : function() {
					 Ext.Msg.confirm("请确认", "是否清除算法内容?",
							 function(button, text) {
					                if (button == "yes") {
					                      Ext.Ajax.request({
					                        url: 'deleteAnalyzeFun.do',
					                        params: {
					                            iid: iid
					                        },
					                        method: 'POST',
					                        success: function(response, request) {
					                        	var success = Ext.decode(response.responseText).success;
								                var message = Ext.decode(response.responseText).message;
								                Ext.Msg.alert('提示', message);  
								                if(success){
									            	afWin.close();
									                afStore.reload();
								                }
								            }
					                    });
					                    }
					 })
				}
			}] 
			}]
		});
		editor.setSize('100%', mainP_Code.getHeight()-modelHeigth-160);
		var afPanel = Ext.create('Ext.panel.Panel', {
			layout : 'border',
			cls:'customize_panel_back',
			height : contentPanel.getHeight()-modelHeigth,
			width:'100%',
			bodyPadding : grid_margin,
			border : true,
			//bodyCls:'service_platform_bodybg',
			items : [ afToolForm,afcenter ]
		});
		var afWin = Ext.create('widget.window', {
			title: '分析算法',
			closable: true,
			closeAction: 'destroy',
			width: contentPanel.getWidth(),
			minWidth: 350,
			height: contentPanel.getHeight()-modelHeigth,
			draggable: false,
			resizable: false,
//			modal: true,
			items: [afPanel]
		});
		afWin.show();
	}
	function viewDetailForTaskManager(serviceId,label) {
//    if (!DetailWinTi) {
		var DetailWinTi = Ext.create('widget.window', {
			title: '详细信息',
			closable: true,
			closeAction: 'destroy',
			width: contentPanel.getWidth(),
			minWidth: 350,
			height: contentPanel.getHeight(),
			draggable: false,
			// 禁止拖动
			resizable: false,
			// 禁止缩放
			modal: true,
			loader: {
				url: 'queryOneServiceForView.do',
				params: {
					iid: serviceId,
					flag: 1,
					hideReturnBtn: 1,
					switchFlag:db_projectFlag_index,
					label:label
				},
				autoLoad: true,
				scripts: true
			}
		});
//    }
		
//    DetailWinTi.getLoader().load({});
		DetailWinTi.show();
	}
	function viewDetailForTaskManagerForFlow(serviceId, serviceName, bussId,
			bussTypeId, scriptLevel, isFlow, menuId) {
		var DetailWinTi = Ext.create('widget.window', {
			title : '详细信息',
			closable : true,
			closeAction : 'destroy',
			width : contentPanel.getWidth(),
			minWidth : 350,
			height : contentPanel.getHeight() - 42,
			draggable : false,
			resizable : false,
			modal : true,
			loader : {
				url : 'flowCustomizedInitScriptService.do',
				params : {
					serviceId : serviceId,
					iid : serviceId,
					menuId : menuId,
					status : 0,
					serviceName : serviceName,
					actionType : 'view',
					bussId : bussId,
					bussTypeId : bussTypeId,
					submitType : 'tv',
					flag : 0,
					windowHeight : contentPanel.getHeight() - 42,
					rootspace : 'view',
					isShowInWindow : 1,
					isScriptConvertToFlow : isFlow != '1'
				},
				autoLoad : true,
				scripts : true
			}
		});
		
		DetailWinTi.show();
	}
	
});


function showConfigService_window(sysId,itname){
	if(sysId == null || sysId == ''){
		Ext.Msg.alert('提示','请先保存后再配置服务。');
		return;
	}
	if(showConfService_window == undefined || !showConfService_window.isVisible()){
	   showConfService_window = Ext.create('Ext.window.Window',{
			title : itname+'_服务规则配置',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : contentPanel.getWidth(),
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			loader : {
				url : 'showConfigDetail.do',
				params : {
					tmpId : sysId
				},
				autoLoad : true,
				scripts : true
			}
		});
		
    }
	showConfService_window.show()
}
