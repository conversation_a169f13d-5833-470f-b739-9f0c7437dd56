<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
    //标签
    boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
    //功能分类
    boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
%>
<html>
<head>
<script type="text/javascript">
var tempData = {};
<%
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
var filter_keywordsForWhite  = '<%=request.getParameter("filter_keywords")==null?"":request.getParameter("filter_keywords")%>';
var hostNameForWhite = '<%=request.getAttribute("serviceName")==null?"":request.getAttribute("serviceName")%>';
var labelSwitch = <%=sdScriptLabelEditSwitch%>;
var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptexec/taskindexForWhite.js"></script>
</head>
<body>
<div id="scriptexec_task_areaForWhite" style="width: 100%;height: 100%">
</div>
</body>
</html>