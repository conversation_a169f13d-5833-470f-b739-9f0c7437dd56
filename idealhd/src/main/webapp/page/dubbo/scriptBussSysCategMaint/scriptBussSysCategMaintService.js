var scriptBussSysService2_window;
Ext.onReady(function() {
  var currentGroupId=-1;
  var curServer=-1;
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  
  Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'},
		    {name : 'itemClicked',type : 'boolean'},
		    {name : 'checked',	type : 'boolean'}
	    ]
	});
	
	var scriptServiceReleaseLColumns = [{
		text : '序号',
		xtype : 'rownumberer',
		width : 65
	},{
		xtype : 'checkcolumn',
		header : '选中',
		hidden : true,
		dataIndex : 'checked',
		width : 95,
		stopSelection : false,
		listeners : {
			checkchange : function(column, recordIndex, checked) {
				if (checked)
					ChosedServList.down('#view').setDisabled(
							false);
				else {
					var serverStore = ChosedServList.getStore();
					var storeCnt = serverStore.getCount();
					var isChecked = null;
					var cnt = 0;
					for (var i = 0; i < storeCnt; i++) {
						isChecked = serverStore.getAt(i).get(
								'checked');
						if (isChecked == true) {
							cnt++;
						}
					}
				}
			}
		}
	}, 
	{
	    text : '服务主键',
	    dataIndex : 'iid',
	    width : 40,
	    hidden : true
	}, 
	{
		text : '版本',
	    dataIndex : 'version',
	    width : 60,
	    renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, 
	{
		text : '服务名称',
	    dataIndex : 'serviceName',
	    width : 200,flex:1,
	    renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	},
	{
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 200,flex:1,
	    renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, 
	{
	    text : '一级分类',
	    dataIndex : 'buss',
	    hidden : db_sys_f_class,
	    width : 200,flex:1
	}, 
	{
		text : '二级分类',
		dataIndex : 'bussType',
		hidden : db_sys_s_class,
		width : 250,flex:1
	},
	{
	    text : '脚本类型',
	    dataIndex : 'scriptType',
	    width : 80,
	    flex:1,
	    renderer:function(value,p,record,rowIndex){
	    	var isflow = record.get('isflow');
	    	if(isflow=="1") {
	    		return "组合";
	    	} else {
	    		return value;
	    	}
	    }
	}, 
	{
		text : '适用平台',
		dataIndex : 'platForm',
		width : 90,
		renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	},
	{
		text : '模板配置',
		width : 100,
		hidden:null==tmpId?true:false,
		renderer : function(value, p, record,
				rowIndex) {
			var iid = record.get('iid');
			var serviceName = record.get('serviceName');
			return '<span class="switch_span">'
			+ '<a href="javascript:void(0)" onclick="showScriptBussSysService_window2('
			+ iid+','+tmpId+',\''+serviceName+'\''
			+ ')">'
			+ '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;服务配置'
			+ '</a>' + '</span>'
			+ '&nbsp;&nbsp;&nbsp;&nbsp;';
		}
	}
];
	var scriptServiceReleaseRColumns = [{
		text : '序号',
		xtype : 'rownumberer',
		width : 70
	},{
		xtype : 'checkcolumn',
		header : '选中',
		hidden : true,
		dataIndex : 'checked',
		width : 95,
		stopSelection : false,
		listeners : {
			checkchange : function(column, recordIndex, checked) {
				if (checked)
					unChosedServList.down('#view').setDisabled(
							false);
				else {
					var serverStore = unChosedServList.getStore();
					var storeCnt = serverStore.getCount();
					var isChecked = null;
					var cnt = 0;
					for (var i = 0; i < storeCnt; i++) {
						isChecked = serverStore.getAt(i).get(
						'checked');
						if (isChecked == true) {
							cnt++;
						}
					}
				}
			}
		}
	}, 
	{
		text : '服务主键',
		dataIndex : 'iid',
		width : 40,
		hidden : true
	}, 
	{
		text : '版本',
	    dataIndex : 'version',
	    width : 60,
	    renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, 
	{
		text : '服务名称',
		dataIndex : 'serviceName',
		width : 200,
		flex:1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	},
	{
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 200,
		flex:1,
	    renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, 
	{
		text : '一级分类',
		dataIndex : 'buss',
		hidden : db_sys_f_class,
		width : 200,flex:1
	}, 
	{
		text : '二级分类',
		dataIndex : 'bussType',
		hidden : db_sys_s_class,
		width : 250,flex:1
	},
	{
		text : '脚本类型',
		dataIndex : 'scriptType',
		width : 80,flex:1,
		renderer:function(value,p,record,rowIndex){
			var isflow = record.get('isflow');
			if(isflow=="1") {
				return "组合";
			} else {
				return value;
			}
		}
	}, 
	{
		text : '适用平台',
		dataIndex : 'platForm',
		width : 90,
		renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}
	];
  var store = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    pageSize: 50,
    model: 'scriptServiceReleaseModel',
    proxy: {
      type: 'ajax',
      url: 'queryAllServiceForBuss.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  store.addListener('load',function(){
    var records=[];//存放选中记录
    for(var i=0;i<store.getCount();i++){
      var record = store.getAt(i);
      if(record.data.checked){
        records.push(record);
      }
    }
    selModel.select(records);//选中记录
  });
  
  
  var choosedStore = Ext.create('Ext.data.Store', {
	    autoDestroy: true,
	    pageSize: 50,
	    model: 'scriptServiceReleaseModel',
	    proxy: {
	      type: 'ajax',
	      url: 'queryChoseServiceForBuss.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	  
  choosedStore.addListener('load',function(){
	    var records=[];//存放选中记录
	    for(var i=0;i<choosedStore.getCount();i++){
	      var record = choosedStore.getAt(i);
	      if(record.data.checked){
	        records.push(record);
	      }
	    }
	    chosedSelModel.select(records);//选中记录
	  });
  
	var pageBarRight = Ext.create('Ext.PagingToolbar', {
    	store: store, 
    	 dock: 'bottom',
         baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
 	     border:false,
         displayInfo: true
    });  
	var pageBarLeft = Ext.create('Ext.PagingToolbar', {
    	store: choosedStore, 
    	 dock: 'bottom',
         baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
 	     border:false,
         displayInfo: true
    }); 
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	var chosedSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	queryWhere();	  
  var unChosedServList = Ext.create('Ext.grid.Panel', {
    region : 'center',
    title : '待选择的服务',
    width : "50%",
    border:true,
    padding : panel_margin,
    bbar: pageBarRight,
    multiSelect: true,
    split : true,
	viewConfig : {
		plugins : {
			ptype : 'gridviewdragdrop',
			dragGroup : 'secondGridDDGroup',
			dropGroup : 'firstGridDDGroup'
		},
		listeners : {
			drop : function(node, data, dropRec, dropPosition) {
				var dropOn = dropRec ? ' ' + dropPosition + ' '
						+ dropRec.get('name') : ' on empty view';
			}
		}
	},
    columnLines : true,
    emptyText: '没有服务信息',
    store : store,
    columns : scriptServiceReleaseRColumns,		
    listeners : {
		itemclick : function(dv, record, item, index, e) {
			store.each(function(rec) {
				rec.set('itemClicked', false);
			});
			record.set('itemClicked', true);
		}
	},
    dockedItems: [{
	      xtype: 'toolbar',
	      items: [{
	          itemId : 'save_band',
	          text : '保存',
	          cls : 'Common_Btn',
//	          margin:'5',
	          //iconCls:'sc_save',
//	          height : 30,
//	        disabled : true,
	          handler : onSaveListener
	        }]
	    }]
  });
  
  var ChosedServList = Ext.create('Ext.grid.Panel', {
	    region : 'west',
	    title : '绑定服务',
	    width : "50%",
	    border:true,
	    padding : panel_margin,
	    bbar: pageBarLeft,
	    multiSelect: true,
	    split : true,
	    viewConfig : {
			plugins : {
				ptype : 'gridviewdragdrop',
				dragGroup : 'firstGridDDGroup',
				dropGroup : 'secondGridDDGroup'
			},
			listeners : {
				drop : function(node, data, dropRec, dropPosition) {
					var dropOn = dropRec ? ' ' + dropPosition + ' '
							+ dropRec.get('name') : ' on empty view';
				}
			}
		},
	    columnLines : true,
	    emptyText: '没有服务信息',
	    store : choosedStore,
	    columns : scriptServiceReleaseLColumns,
	    listeners : {
			itemclick : function(dv, record, item, index, e) {
				choosedStore.each(function(rec) {
					rec.set('itemClicked', false);
				});
				record.set('itemClicked', true);
			}
		},
	    dockedItems: [{
	      xtype: 'toolbar',
	      items: [{
	        itemId : 'savess',
	        text : '还原',
	        //iconCls:'sc_reduction',
	        cls : 'Common_Btn',
//	        margin:'5',
//	        height : 30,
	        handler : function(){
                store.reload();
                choosedStore.reload();
	        }
	      },'-',{
	          itemId : 'save_band',
	          text : '保存',
	          cls : 'Common_Btn',
//	          margin:'5',
	          //iconCls:'sc_save',
//	          height : 30,
//	        disabled : true,
	          handler : onSaveListener
	        }]
	    }]
	  });
	
  var mainPage_S = Ext.create('Ext.panel.Panel', {
    width : '100%',
    height : contentPanel.getHeight()-50,
    layout : 'border',
    header : false,
    bodyPadding : grid_margin,
    border : true,
    bodyCls:'service_platform_bodybg',
    items : [ ChosedServList, unChosedServList ],
    renderTo : "scriptBussSysCategMaintService_dev"
  });
  function queryWhere(){
		store.load({
		       params: { start: 0, limit: 50,sysId:sysIdforService,tmpId:tmpId}
		});
		choosedStore.load({
		       params: { start: 0, limit: 50,sysId:sysIdforService,tmpId:tmpId}
		});
	}
/*  resizePanel(function(){
    //server_config_panel.setWidth(contentPanel.getWidth()-15);
    //server_config_panel.setHeight(contentPanel.getHeight()-100);
  });*/
  
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };
  
  function onSaveListener(){
	  onSaveLeftScreen();
//	  recover();
  }
  
  
  /**
   * @desc 单击保存按钮后，对“左屏”中的数据进行保存。
   * 	   （1）左屏保存时，对服务器信息进行保存，
   * 	   （2）对左屏中所有的记录都进行保存。所以每条记录的checed属性必须全部为为true。
   * */
  function onSaveLeftScreen(){
	  var leftDrops = [];			   		//从左屏拽到右屏的数据,需要清理			
	  var rightDrops = [];			   		//从右屏拽到左屏的数据,需要保存	
	  store.each(function(rec) {
			if (true == rec.get("checked")) {
				leftDrops.push(rec.get("iid"));
			}
		});
	  choosedStore.each(function(rec) {
			if (true == rec.get("checked")) {
				rightDrops.push(rec.get("iid"));
			}
		});
    Ext.Ajax.request({
      url : 'scriptBussSysCategMaintServiceSave.do',
      method : 'POST',
      params : {
    	jsonLeftData : rightDrops.join (','),
    	jsonRightData: leftDrops.join (','),
        sysId: sysIdforService,
        tmpId:tmpId
      },
      success : function(response, request) {
        var success = Ext.decode(response.responseText).success;
        if (success) {
            store.reload();
            choosedStore.reload();
            Ext.Msg.alert('提示', '操作成功执行');
            parent.scriptBussSysService_window.close ();
          } else {
            Ext.Msg.alert('提示', '绑定操作失败！');
          }
      },
      failure : function(result, request) {
        Ext.Msg.alert('提示', '保存失败');
      }
    });
  
  }
  
  function recover(){
		curServer=null;
		serverCombo.setValue('');
        store.getProxy().setExtraParam("groupId",currentGroupId);
        store.getProxy().setExtraParam("isChoosed", false);
        store.getProxy().setExtraParam("curServer", curServer);
        store.load(); 
  }
  
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
});
function showScriptBussSysService_window2(iid,tmpId,serviceName) {
	/**
	 *  首先查询该模板的服务配置是否保存  只有保存完成后才能进行分组和统计的配置  
	 */
	Ext.Ajax.request({
		url : 'getGroupAndCountIsExist.do',
		method : 'POST',
		params : {
			iid : iid,
			tmpId :tmpId
		},
		success : function(response, request) {
             var count = Ext.decode(response.responseText).count;
             if(count<1){
            	 Ext.Msg.alert('提示', '请先保存业务系统后再配置服务。');
         		return;
             }else{
     		scriptBussSysService2_window = Ext.create('Ext.window.Window', {
     				title : serviceName+'_服务规则配置',
     				modal : true,
     				closeAction : 'destroy',
     				constrain : true,
     				autoScroll : true,
     				width : 1200,
     				height : 600,
     				draggable : false,// 禁止拖动
     				resizable : false,// 禁止缩放
     				layout : 'fit',
     				loader : {
     					url : 'gotoConfigServiceWithGroupAndCount.do',
     					params : {
     						serviceId:iid,
     						tmpId : tmpId
     					},
     					autoLoad : true,
     					scripts : true
     				}
     			});
     		scriptBussSysService2_window.show();
             }
		},
		failure : function(result, request) {
			Ext.Msg.alert('提示', '执行失败！');
		}
	});
}