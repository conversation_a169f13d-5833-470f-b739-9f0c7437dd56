Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	/** *********************Store********************* */
	var selModelp = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true
	});
	var roleStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		fields : [ 'roleName', 'iroleId', 'ischeck' ],
		proxy : {
			type : 'ajax',
			url : 'getScriptBussSysRoleCfgList.do?sysId=' + sysIdforRole,
			reader : {
				type : 'json',
				root : 'rolelist'
			}
		}
	});
	roleStore.addListener('load', function() {
		var records = [];// 存放选中记录
		for (var i = 0; i < roleStore.getCount(); i++) {
			var record = roleStore.getAt(i);
			if (record.data.ischeck != 0) {
				records.push(record);
			}
		}
		selModelp.select(records);// 选中记录
	});

	var roleGrid = Ext.create('Ext.grid.Panel', {
		height : 340,
		width : '100%',
		border : true,
		store : roleStore,
		columns : [ {
			text : 'ischeck',
			dataIndex : 'ischeck',
			hidden : true
		}, {
			text : 'iroleId',
			dataIndex : 'iroleId',
			hidden : true
		}, {
			text : '角色',
			dataIndex : 'roleName',
			flex : 1
		} ],
		forceFit : true,
		selModel : selModelp,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [ {
				xtype : 'button',
				width : 60,
				height : 30,
				margin : '5',
				textAlign : 'center',
				cls : 'Common_Btn',
				text : '保存',
				handler : saveRoleForBuss
			}, {
				xtype : 'button',
				width : 60,
				height : 30,
				margin : '5',
				textAlign : 'center',
				cls : 'Common_Btn',
				text : '关闭',
				handler : function() {
					parent.scriptBussSysRole_window.close();
				}
			} ]
		} ]
	});

	/** 主panel* */
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptBussSysRoleCfg_div",
		width : '100%',
		height : '100%',
		border : false,
		bodyPadding : 5,
		items : [ roleGrid ]
	});

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
	/** *********************方法********************* */
	// 保存记录
	function saveRoleForBuss() {
		var checkedRecords = roleGrid.getSelectionModel().getSelection();
		var ids = [];
		Ext.each(checkedRecords, function(item) {
			if ('undefined' == item.data.iroleId || '' == item.data.iroleId
					|| null == item.data.iroleId) {
				roleStore.remove(item);
			} else {
				ids.push(item.data.iroleId);
			}
		});
		Ext.Ajax.request({
			url : 'saveScriptBussSysRoleCfg.do',
			params : {
				ids : ids,
				sysId : sysIdforRole
			},
			success : function(response) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.show({
						title : '提示',
						msg : '保存成功！',
						buttons : Ext.Msg.OK,
						icon : Ext.Msg.INFO
					});
				} else {
					Ext.Msg.show({
						title : '提示',
						msg : message,
						buttons : Ext.Msg.OK,
						icon : Ext.Msg.INFO
					});
				}
				roleStore.reload();
			},
			failure : function() {
				Ext.Msg.show({
					title : '提示',
					msg : '保存失败！',
					buttons : Ext.Msg.OK,
					icon : Ext.Msg.WARNING
				});
			}
		});

	}
});
