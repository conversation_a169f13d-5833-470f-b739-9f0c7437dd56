
Ext.onReady(function() {
  var currentGroupId=-1;
  var curServer=-1;
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  
  Ext.define('dataModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'itype',
			type : 'string'
		}, {
			name : 'itemplatetype',
			type : 'string'
		}, {
			name : 'itemplatename',
			type : 'string'
		}, {
			name : 'iservename',
			type : 'string'
		}, {
			name : 'iplatform',
			type : 'string'
		}, {
			name : 'itooltype',
			type : 'string'
		}, {
			name : 'iauth',
			type : 'string'
		}, {
			name : 'iserveone',
			type : 'string'
		},{
			name : 'bsname',
			type : 'string'
		},{
			name : 'typename',
			type : 'string'
		},{
			name : 'iservetwo',
			type : 'string'
		},{
			name : 'itemplatedes',
			type : 'string'
		}, {
			name : 'itemplate',
			type : 'string'
		}, {
			name : 'updateTime',
			type : 'string'
		},  {
			name : 'iloginname',
			type : 'string'
		},
		{
			name : 'ifullname',
			type : 'string'
		},
		{
			name : 'checked',
			type : 'boolean'
		}
		]
	});
	
  var columns = [ {
		width : 40,
		resizable : true,
		xtype : 'rownumberer'
	}, {
		text : '表主键',
		width : 50,
		hidden:true,
		dataIndex : 'iid'
	},{
		xtype : 'checkcolumn',
		header : '选中',
		hidden : true,
		dataIndex : 'checked',
		width : 95,
		stopSelection : false
	},  {
		text : '业务类别',
		width: 80,
		hidden:true,
		dataIndex : 'itype',
		renderer : function(value, metadata, record) {
			var values = value;
			if(value==1)
			{
				values='作业调度';
			}
			else if(value==7){
				values='健康巡检';
			}
			else if(value==3){
				values='应用变更';
			}
			else if(value==4){
				values='灾备切换';
			}
			else if(value==5){
				values='定时任务';
			}
			else if(value==6){
				values='应急操作';
			}
			else if(value==100){
				values='平台管理';
			}
			else if(value==101){
				values='通用';
			}
			else if(value==8){
				values='日常操作';
			}
			
			return values;
		}
	},
	{
		text : '文档类型',
		hidden : true,
		width : 50,
		dataIndex : 'itemplatetype',
	}, 
	{
		text : '文档名称',
		width: 200,
		dataIndex : 'itemplatename',
	}, 
	{
		text : '服务名称',
		width: 100,
		dataIndex : 'iservename',
	}, 
	{
		text : '适用系统',
		width: 100,
		dataIndex : 'iplatform',
	}, 
	{
		text : '工具类型',
		width: 50,
		hidden:true,
		dataIndex : 'itooltype'
	},
	{
		text : '权限',
		width: 100,
		dataIndex : 'iauth',
		renderer : function(value, metadata, record) {
			var values = value;
			if(value==2)
			{
				values='DBA可见';
			}else if(value==3){
				values='项目经理可见';
			}
			else {
				values='普通用户可见';
			}
			return values;
		}
	},
	{
		text : '一级分类ID',
		width: 150,
		hidden : true,
		dataIndex : 'iserveone',
	},{
		text : '一级分类',
		width: 150,
		dataIndex : 'bsname'
	},{
		text : '二级分类ID',
		width: 150,
		hidden : true,
		dataIndex : 'iservetwo',
	},{
		text : '二级分类',
		width: 150,
		dataIndex : 'typename'
	},
	{
		text : '文档说明',
		width: 200,
		dataIndex : 'itemplatedes',
		renderer : function(value, metadata, record) {
			return value.replace(/\r\n/gi, '<br/>').replace(/\n/gi, '<br/>');
		}
	}, 
	{
		text : '更新时间',
		hidden : true,
		width: 150,
		dataIndex : 'updateTime'
	},	
	{
		text : '更新人',
		hidden : true,
		width: 100,
		dataIndex : 'ifullname'
	}];
  
	var scriptServiceReleaseRColumns = [
	];
  var store = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    pageSize: 50,
    model: 'dataModel',
    proxy: {
      type: 'ajax',
      url: 'queryAllServiceForDoc.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  store.addListener('load',function(){
    var records=[];//存放选中记录
    for(var i=0;i<store.getCount();i++){
      var record = store.getAt(i);
      if(record.data.checked){
        records.push(record);
      }
    }
    selModel.select(records);//选中记录
  });
  
  
  var choosedStore = Ext.create('Ext.data.Store', {
	    autoDestroy: true,
	    pageSize: 50,
	    model: 'dataModel',
	    proxy: {
	      type: 'ajax',
	      url: 'queryChoseServiceForDoc.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	  
  choosedStore.addListener('load',function(){
	    var records=[];//存放选中记录
	    for(var i=0;i<choosedStore.getCount();i++){
	      var record = choosedStore.getAt(i);
	      if(record.data.checked){
	        records.push(record);
	      }
	    }
	    chosedSelModel.select(records);//选中记录
	  });
  
	var pageBarRight = Ext.create('Ext.PagingToolbar', {
    	store: store, 
        dock: 'bottom',
        displayInfo: true
    });  
	var pageBarLeft = Ext.create('Ext.PagingToolbar', {
    	store: choosedStore, 
        dock: 'bottom',
        displayInfo: true
    }); 
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	var chosedSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	queryWhere();	  
  var unChosedServList = Ext.create('Ext.grid.Panel', {
    region : 'center',
    title : '待选择的文档',
    width : "50%",
    bbar: pageBarRight,
    multiSelect: true,
    split : true,
	viewConfig : {
		plugins : {
			ptype : 'gridviewdragdrop',
			dragGroup : 'secondGridDDGroup',
			dropGroup : 'firstGridDDGroup'
		},
		listeners : {
			drop : function(node, data, dropRec, dropPosition) {
				var dropOn = dropRec ? ' ' + dropPosition + ' '
						+ dropRec.get('name') : ' on empty view';
			}
		}
	},
    columnLines : true,
    emptyText: '没有服务信息',
    store : store,
    columns : columns,		
    listeners : {
		itemclick : function(dv, record, item, index, e) {
			store.each(function(rec) {
				rec.set('itemClicked', false);
			});
			record.set('itemClicked', true);
		}
	},
    dockedItems: [{
	      xtype: 'toolbar',
	      items: [{
	          itemId : 'save_band',
	          text : '保存',
	          cls : 'Common_Btn',
	          margin:'5',
	          //iconCls:'sc_save',
	          height : 30,
//	        disabled : true,
	          handler : onSaveListener
	        }]
	    }]
  });
  
  var ChosedServList = Ext.create('Ext.grid.Panel', {
	    region : 'west',
	    title : '绑定文档',
	    width : "50%",
	    bbar: pageBarLeft,
	    multiSelect: true,
	    split : true,
	    viewConfig : {
			plugins : {
				ptype : 'gridviewdragdrop',
				dragGroup : 'firstGridDDGroup',
				dropGroup : 'secondGridDDGroup'
			},
			listeners : {
				drop : function(node, data, dropRec, dropPosition) {
					var dropOn = dropRec ? ' ' + dropPosition + ' '
							+ dropRec.get('name') : ' on empty view';
				}
			}
		},
	    columnLines : true,
	    emptyText: '没有服务信息',
	    store : choosedStore,
	    columns : columns,
	    listeners : {
			itemclick : function(dv, record, item, index, e) {
				choosedStore.each(function(rec) {
					rec.set('itemClicked', false);
				});
				record.set('itemClicked', true);
			}
		},
	    dockedItems: [{
	      xtype: 'toolbar',
	      items: [{
	        itemId : 'savess',
	        text : '还原',
	       // iconCls:'sc_reduction',
	        cls : 'Common_Btn',
	        margin:'5',
	        height : 30,
	        handler : function(){
                store.reload();
                choosedStore.reload();
	        }
	      },'-',{
	          itemId : 'save_band',
	          text : '保存',
	          cls : 'Common_Btn',
	          margin:'5',
	          //iconCls:'sc_save',
	          height : 30,
//	        disabled : true,
	          handler : onSaveListener
	        }]
	    }]
	  });
	
  var mainPage_S = Ext.create('Ext.panel.Panel', {
    width : '100%',
    height : contentPanel.getHeight()-38,
    layout : 'border',
    header : false,
    border : false,
    items : [ ChosedServList, unChosedServList ],
    renderTo : "scriptBussSysCategMaintDoc_dev"
  });
  function queryWhere(){
		store.load({
		       params: { start: 0, limit: 50,sysId:sysIdforDoc}
		});
		choosedStore.load({
		       params: { start: 0, limit: 50,sysId:sysIdforDoc}
		});
	}
  
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };
  
  function onSaveListener(){
	  onSaveLeftScreen();
//	  recover();
  }
  
  
  /**
   * @desc 单击保存按钮后，对“左屏”中的数据进行保存。
   * 	   （1）左屏保存时，对服务器信息进行保存，
   * 	   （2）对左屏中所有的记录都进行保存。所以每条记录的checed属性必须全部为为true。
   * */
  function onSaveLeftScreen(){
	  var leftDrops = [];			   		//从左屏拽到右屏的数据,需要清理			
	  var rightDrops = [];			   		//从右屏拽到左屏的数据,需要保存	
	  store.each(function(rec) {
			if (true == rec.get("checked")) {
				leftDrops.push(rec.get("iid"));
			}
		});
	  choosedStore.each(function(rec) {
			if (false == rec.get("checked")) {
				rightDrops.push(rec.get("iid"));
			}
		});
    Ext.Ajax.request({
      url : 'scriptBussSysCategMaintDocSave.do',
      method : 'POST',
      params : {
    	jsonLeftData : rightDrops.join (','),
    	jsonRightData: leftDrops.join (','),
        sysId: sysIdforDoc
      },
      success : function(response, request) {
        var success = Ext.decode(response.responseText).success;
        if (success) {
            store.reload();
            choosedStore.reload();
            Ext.Msg.alert('提示', '操作成功执行');
          } else {
            Ext.Msg.alert('提示', '绑定操作失败！');
          }
      },
      failure : function(result, request) {
        Ext.Msg.alert('提示', '保存失败');
      }
    });
  
  }
  
  function recover(){
		curServer=null;
		serverCombo.setValue('');
        store.getProxy().setExtraParam("groupId",currentGroupId);
        store.getProxy().setExtraParam("isChoosed", false);
        store.getProxy().setExtraParam("curServer", curServer);
        store.load(); 
  }
  
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
});