<%@page contentType="text/html; charset=utf-8"%>
<%@ page import = "com.ideal.ieai.server.ieaikernel.*" %>
<%@ page import = "com.ideal.ieai.core.*" %>
<html>
<script type="text/javascript">
var sysIdforService = <%=request.getParameter("sysId")%>;
var tmpId = <%=request.getParameter("tmpId")%>;
var ss_project_flag = <%=request.getParameter("ss.project_flag")%>;
var db_sys_f_class = <%=request.getAttribute("db_f_class")%>;
var db_sys_s_class = <%=request.getAttribute("db_s_class")%>;
var db_sys_s_level = <%=request.getAttribute("db_s_level")%>;
var db_sys_serviceType = <%=request.getAttribute("db_serviceType")%>;
var db_sys_scriptType = <%=request.getAttribute("db_serviceType")%>;
var db_sys_dbType = <%=request.getAttribute("db_dbType")%>;
</script>
<head>
  <script type="text/javascript"
    src="<%=request.getContextPath()%>/page/dubbo/scriptBussSysCategMaint/scriptBussSysCategMaintService.js">
  </script>
   <%
ConfigReader cr = ConfigReader.getInstance();
cr.init();
long flag = Long.parseLong(cr.getProperties(Environment.ENTEGOR_PROJECT_FLAG,
    ServerEnv.ENTEGOR_PROJECT_FLAG_DEFAULT));
request.setAttribute("flag", flag);
%>
<script type="text/javascript">
var flag = ${flag};
</script>
</head>
<body>
  <div id="scriptBussSysCategMaintService_dev"></div>
</body>
</html>