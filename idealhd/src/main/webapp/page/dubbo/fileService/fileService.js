var flag;
var chosedResGroups = new Array();
var finalChosedAgents = new Array();
Ext.onReady(function() {
	destroyRubbish();
	var flagStore = Ext.create('Ext.data.Store', {
		fields : [ 'flagId', 'flagValue' ],
		data : [ {
			"flagId" : "0",
			"flagValue" : "测试"
		}, {
			"flagId" : "1",
			"flagValue" : "生产"
		} ]
	});
	Ext.define('ftpServiceGridModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'fid',
			type : 'string'
		}, {
			name : 'ftpip',
			type : 'string'
		} ]
	});

	var ftpServiceGridStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : false,
		model : 'ftpServiceGridModel',
		proxy : {
			type : 'ajax',
			url : 'getFtpList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var instanceName = new Ext.form.TextField({
		name : 'instanceName',
		fieldLabel : '文服实例名',
		labelWidth : 90,
		width : '75%',
		padding : '5',
		labelAlign : 'right'
	});

	var ftpsCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'ftps',
		labelWidth : 90,
		queryMode : 'local',
		fieldLabel : 'FTP服务器',
		displayField : 'ftpip',
		valueField : 'fid',
		editable : false,
		store : ftpServiceGridStore,
		width : '25%',
		padding : '5',
		labelAlign : 'right'
	});

	var sourcePath = new Ext.form.TextField({
		name : 'sourcePath',
		fieldLabel : '源文件路径',
		labelWidth : 90,
		width : '75%',
		padding : '5',
		labelAlign : 'right'
	});

	var sourceServer = new Ext.form.TextField({
		name : 'sourceServer',
		fieldLabel : '源服务器',
		labelWidth : 90,
		width : '25%',
		padding : '5',
		labelAlign : 'right',
		listeners : {
			render : function(p) {
				p.getEl().on(
						'click',
						function(p) {
							var flags = fileServiceForm1.getForm().findField(
									"flags").getValue();
							if (flags == '0' || flags == '1') {
								selectSourceServer();
							} else {
								Ext.Msg.alert('提示', '请先选择启动类型！');
								return;
							}
						});
			}
		}
	});

	var sourceServerId = new Ext.form.TextField({
		name : 'sourceServerId',
		hidden : true
	});

	var targetPath = new Ext.form.TextField({
		name : 'targetPath',
		fieldLabel : '目标文件路径',
		labelWidth : 90,
		width : '75.5%',
		padding : '5',
		labelAlign : 'right'
	});

	var flagsCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'flags',
		labelWidth : 90,
		queryMode : 'local',
		fieldLabel : '启动类型',
		displayField : 'flagValue',
		valueField : 'flagId',
		editable : false,
		store : flagStore,
		width : '18%',
		padding : '5',
		labelAlign : 'right',
		listeners : {
			select : function(combo) {
				flag = combo.value;
				sourceServer.setValue("");
				sourceServerId.setValue("");
				chosedResGroups = new Array();
				resourceGroupObj.setValue("");

			}
		}
	});

	var fileServiceForm1 = new Ext.form.FormPanel({
		region : 'north',
		layout : 'anchor',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ instanceName, ftpsCb ]
		}, {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ sourcePath, sourceServer ]
		}, {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ targetPath, flagsCb, {
				xtype : 'button',
				text : '启动',
				cls : 'Common_Btn',
				handler : function() {
					fileServiceStart();
				}
			} ]
		} ]
	});

	Ext.define('resourceGroupModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		}, {
			name : 'description',
			type : 'string'
		} ]
	});

	var resourceGroupStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'resourceGroupModel',
		proxy : {
			type : 'ajax',
			url : 'getResGroupForScriptService.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'totalCount'
			}
		}
	});
	resourceGroupStore.on('load', function() {
		var ins_rec = Ext.create('resourceGroupModel', {
			id : '-1',
			name : '未分组',
			description : ''
		});
		resourceGroupStore.insert(0, ins_rec);
	});
	var resourceGroupObj = Ext.create('Ext.form.field.ComboBox', {
		fieldLabel : '资源组',
		name : 'resourceGroupObj',
		multiSelect : true,
		width : '45%',
		padding : '5',
		store : resourceGroupStore,
		displayField : 'name',
		valueField : 'id',
		triggerAction : 'all',
		editable : false,
		mode : 'local',
		listeners : {
			change : function(comb, newValue, oldValue, eOpts) {
				chosedResGroups = new Array();
				for (var i = 0; i < newValue.length; i++) {
					chosedResGroups.push(newValue[i]);
				}
				fileServiceGridStore.load();
			}
		}

	});

	Ext.define('fileServiceGridModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'agentIp',
			type : 'string'
		}, {
			name : 'agentPort',
			type : 'string'
		}, {
			name : 'agentDesc',
			type : 'string'
		}, {
			name : 'agentState',
			type : 'int'
		}, {
			name : 'targetFilePath',
			type : 'string'
		} ]
	});
	var fileServiceGridStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 25,
		autoDestroy : false,
		model : 'fileServiceGridModel',
		proxy : {
			type : 'ajax',
			url : 'getAllAgentList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	// 分页工具

	// var pageBar = Ext.create('Ext.PagingToolbar',
	// { store : fileServiceGridStore,
	// dock : 'bottom',
	// displayInfo : true
	// });

	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true
	});
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	var fileServiceGrid = Ext.create('Ext.grid.Panel', {
		region : 'center',
		autoScroll : true,
		border : false,
		title : '选择服务器',
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		}, {
			hidden : true,
			dataIndex : "iid",
			text : "服务器主键",
			width : 20
		}, {
			dataIndex : 'agentIp',
			text : '服务器IP',
			width : 150
		}, {
			dataIndex : 'agentPort',
			text : '服务器端口',
			width : 80
		}, {
			dataIndex : 'agentDesc',
			hidden : true,
			text : '描述',
			width : 80
		}, {
			dataIndex : 'agentState',
			hidden : true,
			text : '状态',
			width : 80
		}, {
			dataIndex : 'targetFilePath',
			text : '目标文件路径',
			editor : true,
			flex : true
		} ],
		store : fileServiceGridStore,
		tbar : [ resourceGroupObj ],
		//        bbar:pageBar,
		plugins : [ cellEditing ],
		selModel : selModel,
		listeners : {
			select : function(t, record, index, eOpts) {
				if (!finalChosedAgents.contains(record.get('iid'))) {
					finalChosedAgents.push(record.get('iid'));
				}
			},
			deselect : function(t, record, index, eOpts) {
				finalChosedAgents.remove(record.get('iid'));
			}
		}
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		border : false,
		layout : 'border',
		items : [ fileServiceForm1, fileServiceGrid ],
		renderTo : "fileService_div"
	});
	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
		if (serviceFileWindow) {
			serviceFileWindow.center();
		}
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	// contentPanel.getLoader().on("beforeload", function(obj, options,
	// eOpts) {
	fileServiceGridStore.on("beforeload", function(obj, options, eOpts) {
		if (Ext.isIE) {
			CollectGarbage();
		}
		var new_params = {
			flag : flag,
			rgIds : chosedResGroups
		};

		Ext.apply(fileServiceGridStore.proxy.extraParams, new_params);
	});

	fileServiceGridStore.addListener('load', function() {
		console.log(finalChosedAgents);
		var chosedAgents = finalChosedAgents; // parent.cellObj.chosedAgents;
		if (chosedAgents) {
			var records = []; // 存放选中记录
			for (var i = 0; i < fileServiceGridStore.getCount(); i++) {
				var record = fileServiceGridStore.getAt(i);
				for (var ii = 0; ii < chosedAgents.length; ii++) {

					if (chosedAgents[ii] == record.data.iid) {
						records.push(record);
					}
				}
			}
			fileServiceGrid.getSelectionModel().select(records, false, true); // 选中记录
		}
	});

	var serviceFileWindow;
	function selectSourceServer() {
		fileStore.load();
		serviceFileWindow = Ext.create('Ext.window.Window', {
			title : '选择源服务器',
			modal : true,
			closeAction : 'hide',
			constrain : true,
			// autoScroll:true,
			width : 900,
			height : contentPanel.getHeight() - 20,
			draggable : true,
			// 禁止拖动
			resizable : false,
			// 禁止缩放
			items : [ search_form, fileGrid ]
		// layout : 'fit',
		// loader : {
		// url : 'fileServiceSourceOpen.do',
		// autoLoad : true,
		// scripts : true
		// }
		}).show();
	}

	var search_form = Ext.create('Ext.form.Panel', {
		layout : 'anchor',
		buttonAlign : 'center',
		border : false,
		items : [ {
			layout : 'form',
			anchor : '95%',
			padding : '5 0 5 0',
			border : false,
			items : [ {
				layout : 'column',
				border : false,
				items : [ {
					fieldLabel : 'IP',
					labelAlign : 'right',
					labelWidth : 70,
					margin : '0 10 0 10',
					name : 'agentIp',
					columnWidth : .8,
					xtype : 'textfield'
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function() {
						fileStore.load();
					}
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '添加',
					handler : function() {
						fileServiceCheck();
					}
				}, {
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function() {
						clearQueryWhere();
					}
				} ]
			} ]
		} ]
	});

	Ext.define('fileModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'agentIp',
			type : 'string'
		}, {
			name : 'agentPort',
			type : 'string'
		}, {
			name : 'agentDesc',
			type : 'string'
		}, {
			name : 'agentState',
			type : 'int'
		}, {
			name : 'targetFilePath',
			type : 'string'
		} ]
	});
	var fileStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		pageSize : 25,
		autoDestroy : false,
		model : 'fileModel',
		proxy : {
			type : 'ajax',
			url : 'getFtpAgentList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	// 分页工具
	var pageBarS = Ext.create('Ext.PagingToolbar', {
		store : fileStore,
		dock : 'bottom',
		displayInfo : true
	});
	var selModelS = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true
	});
	var cellEditingS = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});

	var fileGrid = Ext.create('Ext.grid.Panel', {
		height : contentPanel.getHeight() - 110,
		title : '目标服务器',
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		}, {
			hidden : true,
			dataIndex : "iid",
			text : "服务器主键",
			width : 20
		}, {
			dataIndex : 'agentIp',
			text : '服务器IP',
			width : 150
		}, {
			dataIndex : 'agentPort',
			text : '服务器端口',
			width : 80
		}, {
			dataIndex : 'agentDesc',
			hidden : true,
			text : '描述',
			width : 80
		}, {
			dataIndex : 'agentState',
			hidden : true,
			text : '状态',
			width : 80
		}, {
			dataIndex : 'targetFilePath',
			text : '目标文件路径',
			editor : true,
			flex : true
		} ],
		store : fileStore,
		bbar : pageBarS,
		border : false,
		plugins : [ cellEditingS ],
		selModel : selModelS
	});
	fileStore.on('beforeload', function(store, options) {
		var new_params = {
			agentIp : search_form.getForm().findField("agentIp").getValue(),
			flag : flag
		};

		Ext.apply(fileStore.proxy.extraParams, new_params);
	});
	function fileServiceCheck() {
		var records = fileGrid.getSelectionModel().getSelection();
		if (records.length > 1 || records.length == 0) {
			Ext.Msg.alert('提示', '选择且只能选择一台源服务器！');
			return;
		}
		var ids = records[0].data.iid;
		var ips = records[0].data.agentIp;
		var targetFilePath = records[0].data.targetFilePath;
		sourceServerId.setValue(ids);
		sourceServer.setValue(ips);
		if (null == sourcePath.getValue() || '' == sourcePath.getValue()) {
			sourcePath.setValue(targetFilePath);
		}
		search_form.getForm().findField("agentIp").setValue('');
		serviceFileWindow.close();
		serviceFileWindow = null;
	}
	function fileServiceStart() {
		var data = fileServiceGrid.getSelectionModel().getSelection();
		if (data == null || data == 'undefined') {
			Ext.Msg.alert('提示', '必须选择目标服务器才能进行启动操作！');
			return;
		}
		if (data.length == 0) {
			Ext.Msg.alert('提示', '必须选择目标服务器才能进行启动操作！');
			return;
		}
		var instanceNameValue = instanceName.getValue();
		var ftpId = ftpsCb.getValue();
		var sourcePathValue = sourcePath.getValue();
		var sourceIpId = sourceServerId.getValue();
		var sourceIp = sourceServer.getValue();
		var targetpath = targetPath.getValue();
		var ipids = [];
		// var jsonData = "[";
		// for (var i = 0, len = data.length; i < len; i++) {
		// var ss = Ext.JSON.encode(data[i]);
		// if (i == 0)
		// jsonData = jsonData + ss;
		// else
		// jsonData = jsonData + "," + ss;
		// }
		// jsonData = jsonData + "]";
		for (var i = 0; i < data.length; i++) {
			ipids.push(data[i].get('iid'));
		}
		if (!ftpId) {
			Ext.MessageBox.alert("提示", "请选择FTP服务器!");
			return;
		}
		if (!sourceIpId) {
			Ext.MessageBox.alert("提示", "请选择源服务器!");
			return;
		}
		if (instanceNameValue.trim() == '') {
			Ext.MessageBox.alert("提示", "文服实例名不能为空!");
			return;
		}
		if (sourcePathValue.trim() == '') {
			Ext.MessageBox.alert("提示", "源文件路径不能为空!");
			return;
		}
		if (targetpath.trim() == '') {
			Ext.MessageBox.alert("提示", "目标文件路径不能为空!");
			return;
		}
		Ext.MessageBox.wait("数据处理中...", "进度条");
		Ext.Ajax.request({
			url : 'startFileService.do',
			method : 'POST',
			params : {
				ipids : finalChosedAgents.join(','),
				instanceName : instanceNameValue,
				ftpId : ftpId,
				sourcePath : sourcePathValue,
				sourceIpId : sourceIpId,
				sourceIp : sourceIp,
				targetpath : targetpath,
				flag : flag
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					// Ext.Msg.alert('提示', '启动成功');
					Ext.MessageBox.hide();
					destroyRubbish(); //销毁本页垃圾
					contentPanel.getLoader().load({
						url : "monitorHomePageFileScriptService.do",
						scripts : true,
						params : {
							flagType : flag,
							contentPanelHeight : contentPanel.getHeight(),
							windowScHeight : window.screen.height
						}
					});
				} else {
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}
});