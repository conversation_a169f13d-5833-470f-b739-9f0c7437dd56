 Ext.onReady(function() {
     destroyRubbish();
	 var isjcForQuery = flagPoc==2?'':isjcForPoc;
	 var currentDate =flagPoc==2?Ext.Date.format( new Date(), 'Y-m-d'):'';
	 Ext.define('scriptIssueRecordModel', {
		extend : 'Ext.data.Model',
		fields : [ 
			{name: 'iid',type : 'int'},
			{name: 'iagentInfo_id',  type: 'int'},
			{name: 'isendPath',  type: 'string'},
			{name: 'iagent_ip',  type: 'string'},
			{name: 'iagent_port', type: 'string'},
	        {name: 'iservicesName', type: 'string'},
	        {name: 'iscriptName', type: 'string'},
	        {name: 'imessage', type: 'string'},
	        {name: 'istatus', type: 'string'},
	    	{name: 'times', type: 'string'}
     	]
	});
	//鏋勫缓store
	var scriptIssueRecordStore   = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    pageSize: 50,
	    model : 'scriptIssueRecordModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'queryScriptIssueRecordList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty: 'total'
	        }
	    }
	});
    scriptIssueRecordStore.on ('beforeload', function (store, options)
	{
		 var new_params = {
				sjc : isjcForQuery ,
				istatus : stateCombo.getValue(),
				idate:idateComment.getRawValue(),
				fileIssuedFlag:"1"
         };
		Ext.apply (scriptIssueRecordStore.proxy.extraParams, new_params);
	});
	
 
	var scriptIssueRecColumns = 	[{
		text : '搴忓彿',
		width : 50,
		xtype : 'rownumberer'
	},{
		text : 'iid',
		dataIndex : 'iid',
		flex : 1,
		hidden : true
	},{
		text : 'iagentInfo_id',
		dataIndex : 'iagentInfo_id',
		flex : 1,
		hidden : true
	},{
		text : '鏂囦欢璺緞',
		width : 110,
		flex:1,
		dataIndex : 'isendPath'
	},{
		text: 'AgentIP',
        dataIndex: 'iagent_ip',
        width: 90
	},{
		text : 'Agent绔彛',
		width : 80,
		dataIndex : 'iagent_port'
	},{
		text : '涓嬪彂鏃堕棿',
		width : 150,
		dataIndex : 'times'
	},{
		text : '涓嬪彂鐘舵��',
		width : 100,
		dataIndex : 'istatus',
		renderer:function(value,p,record,rowIndex){
			if(value=='0') {
				return '<span class="Abnormal_Operation_orange State_Color">寰呬笅鍙�</span>'; 
			} else if(value=='1'){
				return '<span class="Run_Green State_Color">涓嬪彂鎴愬姛</span>';
			}else if(value=='2'){
				return '<span class="Abnormal_yellow State_Color">姝ｅ湪涓嬪彂</span>';
			}else if(value=='3'){
				return '<span class="Kill_red State_Color">涓嬪彂澶辫触</span>';
			}
		}
	},{
		text : '涓嬪彂娑堟伅',
		width : 300,
		flex:2,
		dataIndex : 'imessage'
	}];
 	var  scriptIssueRecGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
	    store: scriptIssueRecordStore,
	    region: 'center',
//	    height: contentPanel.getHeight()-285,
//      	bbar : issueScriptRecBar,
	    border:false,
	    columnLines : true,
	    cls:'customize_panel_back',
	    columns : scriptIssueRecColumns,
	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true,mode:'SINGLE',allowDeselect:true})
	});
	
	var idateComment = new Ext.form.field.Date({
    	name: 'idate',
		fieldLabel: '涓嬪彂鏃ユ湡',
		labelWidth : 65,
		width : '25%',
        labelAlign : 'right',
        format: 'Y-m-d',
        value :currentDate,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	scriptIssueRecGrid.ipage.moveFirst();
                }
            }
        }
	});
	
	var stateStore = Ext.create('Ext.data.Store', {
			fields : ['id', 'name'],
			data : [{
						"id" : "-100",
						"name" : "鍏ㄩ儴"
					}, {
						"id" : "0",
						"name" : "寰呬笅鍙�"
					}, {
						"id" : "1",
						"name" : "涓嬪彂鎴愬姛"
					}, {
						"id" : "2",
						"name" : "姝ｅ湪涓嬪彂"
					}, {
						"id" : "3",
						"name" : "涓嬪彂寮傚父"
					}]
		});
	var stateCombo = Ext.create('Ext.form.field.ComboBox', {
			name : 'stateCombo',
			// padding : '5',
			labelWidth : 65,
			queryMode : 'local',
			fieldLabel : '涓嬪彂鐘舵��',
			displayField : 'name',
			valueField : 'id',
			editable : false,
			emptyText : '--璇烽�夋嫨鐘舵��--',
			store : stateStore,
			width : '25%',
			labelAlign : 'right'
		});
	
	var search_formForScriptIssueRec = Ext.create('Ext.ux.ideal.form.Panel', {
		layout : 'anchor',
		region:'north',
		buttonAlign : 'center',
		iqueryFun : function(){
			scriptIssueRecGrid.ipage.moveFirst();
		},
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ idateComment,stateCombo,{
				xtype: 'button',
				cls:'Common_Btn',
				margin: '0 5 0 0',
				text : '鏌ヨ',
				handler : function() {
					scriptIssueRecGrid.ipage.moveFirst();
				}
			},{
				xtype: 'button',
				cls:'Common_Btn',
				margin: '0 5 0 0',
				text : '鍒犻櫎',
				handler : function() {
			    	    var record = scriptIssueRecGrid.getSelectionModel ().getSelection ();
				        Ext.Ajax.request({
			                      url: 'deleteFileIssuedRecoredForPoc.do',
			                      method: 'POST',
			                      params: {
			                    	  iid: record[0].get('iid'),
			                    	  agentid:record[0].get('iagentInfo_id'),
			                    	  filePath:record[0].get('isendPath')
			                      },
			                      success: function(response, request) {
			                          var success = Ext.decode(response.responseText).success;
			                          var message = Ext.decode(response.responseText).message;
			                          if (success) {
			                        	  Ext.Msg.alert('鎻愮ず', "鍒犻櫎鎴愬姛锛�");
			                        	  scriptIssueRecGrid.ipage.moveFirst();
			                          } else {
			                              Ext.Msg.alert('鎻愮ず', message);
			                          }
			                      }
			         });
				}
			},{
				xtype: 'button',
				cls:'Common_Btn',
				margin: '0 5 0 0',
				text : '娓呯┖',
				handler : function() {
					idateComment.setValue(currentDate);
					stateCombo.setValue('');
					isjcForQuery = '';
				}
			}]
		}]
	});
	
	scriptIssueRecordStore.load();
	
	 var issuePanel = Ext.create('Ext.panel.Panel', {
        renderTo: "issuedRecord_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_formForScriptIssueRec,scriptIssueRecGrid]
    });
	
	contentPanel.on('resize', function() {
    	issuePanel.setHeight (contentPanel.getHeight () - modelHeigth);
    	issuePanel.setWidth (contentPanel.getWidth () );
    });
	
 
});