

var scriptIns;
Ext.onReady(function() {
	var datar = new Array();
	var issue_store;
    destroyRubbish();
    Ext.define('issueData', {
        extend: 'Ext.data.Model',
        fields: [{ name: 'iid',   type: 'string' },
        		 { name: 'state', type: 'int' },
        		 {name : 'filename' ,type:'string'},
        {
            name: 'ifullname',
            type: 'string'
        },
        {
            name: 'opertime',
            type: 'string'
        },
        {
        	name: 'has',
        	type: 'boolean'
        }]
    });
    issue_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 20,
        model: 'issueData',
        proxy: {
            type: 'ajax',
            url: 'listIssued.do',
            reader: {
                type: 'json',
                root: 'issueList',
                totalProperty: 'total'
            }
        }
    });
    issue_store.on('beforeload',function(store, options){
   		 var new_params = {
   		    fileName:fName.getValue().trim(),
   		    startTime: startTime.getRawValue(),
            endTime: endTime.getRawValue()
   		 };
     Ext.apply(issue_store.proxy.extraParams, new_params);
    });
//    issue_store.load({
//        params: { start: 0, limit: 20} 
//    });
    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if(value == 1){
            		backValue = '<span class="Run_Green State_Color">成功</span>';
            }else if(value == 2){
            		backValue = '<span class="Abnormal_yellow State_Color">正在下发</span>';
            }else if(value == 3){
            		backValue = '<span class="Kill_red State_Color">失败</span>';
            }
            
//            if (value == 1) {
//            	if(record.data.has){
//            		backValue = '<span class="Run_Green State_Color">已下发</span>';
//            	}else{
//            		backValue = '<span class="Run_Green State_Color">未下发</span>';
//            	}
//            } else if (value == -1) {
//                backValue = "<span class='Abnormal_yellow State_Color'>未下发</span>";
//            } else if (value == 2) {
//                backValue = '<span class="Abnormal_Complete_purple State_Color">下发</span>';
//            } else if (value == -2) {
//                backValue = '<span class="Kill_red State_Color">下发</span>';
//            } else{
//            	backValue = value;
//            }
            return backValue;
        }
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '文件名称',
        dataIndex: 'filename',
        width: 300
//        renderer: function(value, p, record) {
//        	var link = 
//        	'<span class="switch_span">'+
//			   	'<a href="javascript:void(0)" onclick="openIssuedFiles('+value+')">'+
//			   		'查看下发文件'+
//			   	'</a>'+
//			   '</span>';
//        	return link;
//        },
        
    },
   
    {
        text: '下发人',
        dataIndex: 'ifullname',
        width: 150
    },
    {
        text: '下发时间',
        dataIndex: 'opertime',
        width: 180
    }, {
    	text: '操作',
    	dataIndex: 'iid',
    	width: 70,
        renderer: function(value, p, record) {
//        	console.log(record.data);
        	if(record.data.has)
    		{
    		var link = 
            	'<span class="switch_span">'+
    			   	'<a href="javascript:void(0)" onclick="openHistory('+value+')">'+
    			   		'查看下发实例'+
    			   	'</a>'+
    			   '</span>';
    		return link;
    		}
        },
        flex: 1
    }];
    var issue_pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: issue_store,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });
    var fName = new Ext.form.TextField({
		id:'fName',
		fieldLabel : '文件名称',
		labelWidth : 100,
		width :'25%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	issue_pageBar.moveFirst();
                }
            }
        }
	}); 
	var startTime = new Ext.form.field.Date({
    	name: 'startTime',
    	id:'startTime',
		fieldLabel: '开始时间',
		labelWidth : 70,
		width : '25%',
        labelAlign : 'right',
        format: 'Y-m-d H:i:s',
        listeners:{
			change:function(dateField,newValue,oldValue,eOpts ){
				var EDate=Ext.getCmp('endTime').getValue();
				if(EDate != "" && EDate !=null && EDate < newValue){
					Ext.Msg.alert("提示","开始时间不能晚于结束时间！");
	 				startTime.setValue("");
				}
			},
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	issue_pageBar.moveFirst();
	                }
	            }
		}
	});
    
    var endTime = new Ext.form.field.Date({
    	name: 'endTime',
    	id:'endTime',
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '25%',
    	labelAlign : 'right',
    	format: 'Y-m-d H:i:s',
    	listeners:{
			change:function(dateField,newValue,oldValue,eOpts){
				var SDate =Ext.getCmp('startTime').getValue();
				if(SDate != "" && SDate !=null && SDate > newValue){
					Ext.Msg.alert("提示","结束时间不能早于开始时间！");
	 				endTime.setValue("");
				}
			},
			specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	issue_pageBar.moveFirst();
                }
            }
		}
    });
    
    var search_formForIssuedRecord = Ext.create('Ext.form.Panel',{
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        baseCls:'customize_gray_back',
        border: false,
         dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items: [fName,
			        startTime,
			        endTime,
            {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    issue_pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls : 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            },{
                xtype: 'button',
                text: '导出',
                cls: 'Common_Btn',
                handler: function() {
                    exportData();
                }
            }]
		}]
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
	});
    var issue_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	autoScroll: true,
        store: issue_store,
        border: false,
        cls:'customize_panel_back',
        selModel : selModel,
        columnLines: true,
		padding : grid_space,
        columns: scriptmonitor_columns,
//        bbar: issue_pageBar
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    });

    var issuePanel = Ext.create('Ext.panel.Panel', {
        renderTo: "issuedRecord_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_formForIssuedRecord,issue_grid]
    });

    contentPanel.on('resize', function() {
    	issuePanel.setHeight (contentPanel.getHeight () - modelHeigth);
    	issuePanel.setWidth (contentPanel.getWidth () );
    });
    
   function exportData(){
		var record = issue_grid.getSelectionModel().getSelection();
		var iidStr = "";
		Ext.Array.each(record, function(recordObj) {
					iidStr += "," + recordObj.get('iid');
				});
		if (iidStr.length <= 0) {
			Ext.Msg.alert('提示', '请选择要操作的行！');
			return;
		}
		iidStr = iidStr.substr(1);
		var url ='exportIssuedRecord.do?optidstr=' + iidStr;
		window.location.href = encodeURI(url);
   }
    
    
    function clearQueryWhere() {
    	search_formForIssuedRecord.getForm().findField("fName").setValue('');
        startTime.setValue('');
        endTime.setValue('');
	}
    function configScripts()
    {
    	Ext.define('configModel', {
    	    extend : 'Ext.data.Model',
            idProperty: 'iid',
    	    fields : [{
    	      name : 'iid',
    	      type : 'int',
    	      useNull : true
    	    }, {
    	      name : 'iname',
    	      type : 'string'
    	    }, {
    	      name : 'ivalue',
    	      type : 'string'
    	    }, {
    	      name : 'ikey',
    	      type : 'int'
    		}, {
    	      name : 'itype',
    	      type : 'int'
    	    }]
    	  });
    	
    	var config_store = Ext.create('Ext.data.Store', {
    	    autoLoad: true,
    	    autoDestroy: true,
    	    model: 'configModel',
    	    proxy: {
    		      type: 'ajax',
    		      url: 'getIssueConfigList.do',
    		      reader: {
    		        type: 'json',
    		        root: 'dataList'
    		      }
    		    }
    	});
    	
    	Ext.define('valueModel', {
    	    extend : 'Ext.data.Model',
            idProperty: 'ikey',
    	    fields : [{
    	      name : 'ikey',
    	      type : 'int',
    	      useNull : true
    	    }, {
    	      name : 'ivalue',
    	      type : 'string'
    	    }, {
    	      name : 'itype',
    	      type : 'int'
    	    }]
    	  });
    	
    	var value_store = Ext.create('Ext.data.Store', {
    		autoLoad: false,
    		autoDestroy: true,
    		model: 'valueModel',
    		
    		proxy: {
    			type: 'ajax',
    			url: 'listConfigValues.do',
    			reader: {
    				type: 'json',
    				root: 'dataList'
    			}
    		}
    	});
    	
    	value_store.load({
    		callback: function(records, operation, success) {
    	        datar.splice(0,datar.length);
    	        for (var i = 0; i < records.length; i++) {
    	            datar.push(records[i].data);
    	        }
    	    }
    	});
    	var configEdit = new Ext.form.field.ComboBox({
    		displayField: 'ivalue',
    	    valueField: 'ikey',
    	    hiddenName: 'ikey',
    	    name:'ikey',
            typeAhead: true,
            triggerAction: 'all',
            store: value_store
        });
    	
    	var config_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	    store: config_store,
    	    height: 300,
    	    cls:'customize_panel_back',
    	    plugins: [new Ext.grid.plugin.CellEditing({clicksToEdit: 1        })],
            listeners : {
                beforeedit : function(editor, e,aaa) {
                	var itype = e.record.data.itype;
                	value_store.clearFilter();
                	value_store.filter('itype', itype);
                }
            },
            
    	    columns: [
    	        { text: '配置项',  dataIndex: 'iname' },
    	        { text: '值', dataIndex: 'ikey', flex: 1,
    	        	editor: configEdit,renderer:function(value,p,record){
    	        		for(var j = 0; datar[j]!=null; j++) {
    	        			if(value==datar[j].ikey)
            				{
    	        				return datar[j].ivalue;
            				}
    	        		}
    	        		if(value==-1)
    	        			return '';
    	        		return value;
    	        	}
    	        },
//    	        { text: '版本',  dataIndex: 'iversion' },
    	        { text: '类型',  dataIndex: 'itype' ,renderer:function(value,p,record){
    	        	if(value==1)
    	        		return 'FTP';
    	        	if(value==2)
    	        		return 'SCRIPT';
            	}}
    	    ]
    	});
    	var chooseScriptsWin = Ext.create('Ext.window.Window', {
      		title : '下发配置',
      		autoScroll : true,
      		modal : true,
      		resizable : false,
      		closeAction : 'destroy',
      		width : 400,
      		height : 400,
      		items:[config_grid],
      		dockedItems : [ {
    			xtype : 'toolbar',
    			baseCls:'customize_gray_back',  
    			dock : 'bottom',
    			items : [ '->',{
      			xtype: "button",
      			text: "确定",
      			cls : 'Common_Btn',
      			handler: function () {
      				var records = config_store.getUpdatedRecords();
      				var jsonData = "[";
      				for(var i = 0, len = records.length; i < len; i++){
      					var ss = Ext.JSON.encode(records[i].data);
                        if (i == 0) jsonData = jsonData + ss;
                        else jsonData = jsonData + "," + ss;
      				}
      				 //jsonData = "[" + Ext.JSON.encode(records[0].data) + "]";
      				 jsonData = jsonData + "]";

      				Ext.Ajax.request({
    	      			url : 'updateIssueConfig.do',
    	      			method : 'POST',
    	      			params : {
    	      				jsonData:jsonData
    	      			},
    	      			success : function(response, request) {
    	      				chooseScriptsWin.close();
    		  				Ext.Msg.alert('提示', "配置成功！");
    	      			},
    	      			failure : function(result, request) {
    	      				Ext.Msg.alert('提示', '配置失败！');
    	      			}
    	      		});
      			}
      		}, { 
      			xtype: "button", 
      			text: "取消", 
      			cls : 'Common_Btn',
      			handler: function () {
      				this.up("window").close();
      			}
      		 }]
      		}]
      	}).show();
    	
    }




    function openIssuedFiles(operid)
    {
    	Ext.define('fileModel', { 
            extend : 'Ext.data.Model',
            idProperty: 'iid', fields : [
                { name : 'iid', type : 'int', useNull : true }, 
                { name : 'filename', type : 'string' }, 
                { name : 'othername', type : 'string' }
                ]
    	  });
    	
    	var file_store = Ext.create('Ext.data.Store', {
    	    autoLoad: false,
    	    autoDestroy: true,
    	    model: 'fileModel',
    	    proxy: {
    		      type: 'ajax',
    		      url: 'listIssuedFiles.do',
    		      reader: {
    		        type: 'json',
    		        root: 'fileList'
    		      }
    		    }
    	});
    	file_store.load({
    		 params: {
    			 operid: operid
             }
    	})
    	
//    	var  file_grid = Ext.create('Ext.grid.Panel', {
//    	    store: file_store,
//    	    region: 'center',
//    	    height: 300,
//    	    plugins: [new Ext.grid.plugin.CellEditing({
//                clicksToEdit: 1
//            })],
//            listeners : {
//                beforeedit : function(editor, e,aaa) {
//                	var itype = e.record.data.itype;
//                	value_store.clearFilter();
//                	value_store.filter('itype', itype);
//                }
//            },
//    	    columns: [
//    	        { text: '文件名',  dataIndex: 'filename',width: 280 },
//    	        { text: '文件名编码', dataIndex: 'othername', flex: 1}
//    	    ]
//    	});
    	
//    	var chooseScriptsWin = Ext.create('Ext.window.Window', {
//      		title : '下发文件',
//      		autoScroll : true,
//      		layout: 'border',
//      		modal : true,
//      		resizable : true,
//      		closeAction : 'destroy',
//      		width : 800,
//      		height : 400,
//      		items:[file_grid],
//      		dockedItems : [ {
//    			xtype : 'toolbar',
//    			dock : 'bottom',
//    			items : [ '->',{ 
//      			xtype: "button", 
//      			text: "关闭", 
//      			cls:'Common_Btn',
//      			handler: function () {
//      				this.up("window").close();
//      			}
//    			}]
//      		}]
//      	}).show();
    }
});




function openHistory(operid)
{
	Ext.define('fileModel', { 
        extend : 'Ext.data.Model',
        idProperty: 'iid', fields : [
            { name : 'sid', type : 'int', useNull : true }, 
            { name : 'istate', type : 'int' }, 
            { name : 'iflowid', type : 'int' }, 
            { name : 'idesc', type : 'string' }
            ]
	  });
	
	var file_store = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    autoDestroy: true,
	    model: 'fileModel',
	    proxy: {
		      type: 'ajax',
		      url: 'listIssuedScriptIns.do',
		      reader: {
		        type: 'json',
		        root: 'insList'
		      }
		    }
	});
	file_store.load({
		 params: {
			 operid: operid
         }
	})
	
	var  file_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
	    store: file_store,
	    region: 'center',
	    height: 300,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    cls:'customize_panel_back',
	    plugins: [new Ext.grid.plugin.CellEditing({
            clicksToEdit: 1
        })],
        listeners : {
            beforeedit : function(editor, e,aaa) {
            	var itype = e.record.data.itype;
            	value_store.clearFilter();
            	value_store.filter('itype', itype);
            }
        },
	    columns: [
	    	{ text: '序号',   xtype: 'rownumberer',width: 40 },
	        { text: '实例主键',  dataIndex: 'sid',width: 100 },
	        { text: '实例方向',  dataIndex: 'idesc',flex: 1 ,
	            renderer: function(value, p, record)  {
	            	var ret = '<span class="switch_span">'+
				   	'<a href="javascript:void(0)" onclick="openScriptIns('+record.data.sid+', '+record.data.iflowid+')">'+
				   	value +
				   	'</a>'+
				   '</span>';
	            	//menuClick(this,'脚本开发','测试历史','forwardscriptcoat.do?callback_function=forwardruninfo2(245, 0)','9','905','forwardruninfo2(245, 0)');
	            	return ret;
	            	}  
	            },
	        { text: '实例状态', dataIndex: 'istate', width: 100,
	            renderer: function(value, p, record) {
	                var backValue = "";
	                if (value == -1) {
	                    backValue = '<span class="Not_running State_Color">未运行</span>';
	                } else if (value == 10) {
	                    backValue = '<span class="Run_Green State_Color">运行</span>';
	                } else if (value == 20 || value == 5) {
	                    backValue = "<span class='Complete_Green State_Color'>完成</span>"
	                } else if (value == 30) {
	                    backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
	                } else if (value == 40) {
	                    backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
	                } else if (value == 50) {
	                    backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
	                } else if (value == 60) {
	                    backValue = '<span class="Kill_red State_Color">已终止</span>';
	                }
	                return backValue;
	            }}
	    ]
	});
	
	
	
	scriptIns = Ext.create('Ext.window.Window', {
  		title : '下发任务实例',
  		autoScroll : true,
  		layout: 'border',
  		modal : true,
  		resizable : true,
  		closeAction : 'destroy',
  		width : 650,
  		height : 500,
  		items:[file_grid],
  		buttons: [{ 
  			xtype: "button", 
  			text: "关闭", 
  			handler: function () {
  				this.up("window").close();
  			}
  		}]
  	}).show();

	
//	var ret = menuClick(this,'脚本开发','测试历史','forwardscriptcoat.do?callback_function=forwardruninfo2(245, 0)','9','905','forwardruninfo2(245, 0)');
}
 
function openScriptIns(sid)
{
//	menuClick(this,'脚本开发','测试历史','forwardscriptcoat.do?callback_function=forwardruninfo2('+ sid +', 0)','9','905','forwardruninfo2('+ sid +', 0)');
	scriptIns.close();
	popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: sid,flag: 0,fromMenu:'下发记录'},10, true);
}
