Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    //IEAI_SCRIPT_APPSYSTEM_PROP
    Ext.define('ApplicationSystemAttribute', {
        extend: 'Ext.data.Model',
        fields: [
        	{name: 'iid',		 type: 'long'},
        	{name: 'ipropname',	 type: 'string'},
        	{name: 'iorder',	 type: 'string'},
        	{name: 'icreatetime',type: 'long'},
        	{name: 'imodifytime',type: 'long'}
        	]
    });

    applicationSystemAttributeStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'ApplicationSystemAttribute',
        proxy: {
            type: 'ajax',
            url: 'getApplicationSystemAttributeList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var scriptTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [
        	{"name": "是"},
        	{"name": "否"}
            ]
    });
    var applicationSystemAttributeColumns = [
    	{text: '序号',xtype: 'rownumberer',width: 70,resizable: true},
    	{text: 'ID',dataIndex: 'iid',width: 40,hidden: true},
    	{text: '应用系统人员角色',dataIndex: 'ipropname',flex: 1,editor: {allowBlank: false}},
    	{text: '管理者',dataIndex: 'iorder',
    		editor: new Ext.form.field.ComboBox({
                allowBlank: true,
                triggerAction: 'all',
                // 用all表示把下拉框列表框的列表值全部显示出来
                editable: false,
                // 是否可输入编辑
                store: scriptTypeStore,
                queryMode: 'local',
                displayField: 'name',
                valueField: 'name'
            })
    	},
    	{text : '创建时间',dataIndex : 'icreatetime',width : 180},
    	{text : '修改时间',dataIndex : 'imodifytime',width : 180},
    ];

    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: applicationSystemAttributeStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false
    });
    
	var form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : 
				[
				'->',
				{text: '增加',cls: 'Common_Btn',handler: add},
				{text: '保存',cls: 'Common_Btn',handler: save},
				'-',
				{itemId: 'delete',text: '删除',cls: 'Common_Btn',disabled: true,handler: dele},
				{
		            text: '导入/导出',
		            cls:'Common_Btn',
		            menu: {
		                xtype: 'menu',
		                plain: true,
		                items: {
		                    xtype: 'buttongroup',
		                    columns: 2,
		                    defaults: {
		                        xtype: 'button'
		                    },
		                    items: [ {
								text : '导入',
								cls:'Common_Btn',
								handler : uploadExcel
							},
							{
								text : '导出',
								cls:'Common_Btn',
								handler: function() {
				                	var record = applicationSystemAttributeGrid.getSelectionModel().getSelection();
				                	var ids=[];
				            		if(record.length!=0){
				            			Ext.each(record,function(item){
				            				var iid = item.get('iid');
				            				if(iid != 0){
				            					ids.push(iid);
				            					ids.join(',')
				            				}
				            			});
				            		}
				            		window.location.href = 'exportApplicationSystemAttribute.do?ids='+ids;
				            	}
							}]
		                }
		            }
			}
				]
		}]
	});
	
    var applicationSystemAttributeGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        store: applicationSystemAttributeStore,
        cls:'customize_panel_back',
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        plugins: [cellEditing],
        padding : panel_margin,
		border:true,
		bbar: pageBar, 
        columnLines: true,
        columns: applicationSystemAttributeColumns,
        animCollapse: false,
        listeners:
        {
	         cellclick: function(view, td, cellIndex, record, tr, rowIndex, e, eOpts) {
	        	 var record = applicationSystemAttributeGrid.getStore().getAt(rowIndex);
	        	 if(cellIndex==3){
		    		 if(record.get('iid') == '1' || record.get('iid') == '2'){
		    			 return false;
		    		 }
	        	 }
	         }
       } 
    });

    applicationSystemAttributeGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "applicationSystemAttribute_grid_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,applicationSystemAttributeGrid]
    });

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
    // 导入
    function uploadExcel(){
    	var uploadWindows;
    	var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel',{
        	border : false,
        	items : [{
            	xtype: 'filefield',
    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
    			fieldLabel: '选择文件',
    			labelWidth: 80,
    			anchor: '90%',
    			margin: '10 10 0 40',
    			buttonText: '浏览'
            }],
            buttonAlign : 'center',
            buttons :[{
            	text : '确定',
            	handler :upExeclData
            },{
            	text : '取消',
            	handler : function(){
            		uploadWindows.close();
            	}
            }]
        });
        /**
         * Excel导入Agent信息窗体
         */
        uploadWindows = Ext.create('Ext.window.Window', {
    		title : 'Excel导入',
    		layout : 'fit',
    		height : 200,
    		width : 600,
    		modal : true,
//    		autoScroll : true,
    		items : [ uploadForm ],
    		listeners : {
    			close : function(g, opt) {
    				uploadForm.destroy();
    			}
    		}
    	});
        uploadWindows.show();
        function upExeclData(){
        	var form = uploadForm.getForm();
    		var hdupfile=form.findField("fileName").getValue();
    		if(hdupfile==''){
    			Ext.Msg.alert('提示',"请选择文件...");
    			return ;
    		}
    		uploadTemplate(form);
        }
        function uploadTemplate(form) {
      	   if (form.isValid()) {
             form.submit({
               url: 'importApplicationSystemAttribute.do',
                 success: function(form, action) {
                    var sumsg = Ext.decode(action.response.responseText).message;
                    Ext.Msg.alert('提示',sumsg);
            		uploadWindows.close();
            		applicationSystemAttributeStore.reload();
                    return;
                 },
                 failure: function(form, action) {
                     var msg = Ext.decode(action.response.responseText).message;
                     Ext.Msg.alert('提示',msg);
                   return;
                 }
             });
      	   }
      	 }
    }

    function add() {
        var store = applicationSystemAttributeGrid.getStore();
        var p = {
            iid: '',
            ipropname: '',
            iorder: '',
            icreatetime: '',
            imodifytime: ''
        };
        store.insert(0, p);
        applicationSystemAttributeGrid.getView().refresh();
    }

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function save() {
        var m = applicationSystemAttributeStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var ipropname = m[i].get("ipropname").trim();
            var iorder = m[i].get("iorder");
            if ("" == ipropname || null == ipropname) {
                setMessage('权限名称不能为空！');
                return;
            }
            if ("" == iorder || null == iorder) {
                setMessage('管理者选项不能为空！');
                return;
            }
           for (var k = 0; k < applicationSystemAttributeStore.getCount(); k++) {
						var record = applicationSystemAttributeStore.getAt(k);
						var ipropname = record.data.ipropname;
						var iorder = record.data.iorder;
					}
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveApplicationSystemAttribute.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					applicationSystemAttributeStore.modified = [];
					applicationSystemAttributeStore.reload();
					Ext.Msg.alert('提示', message);
				} else {
					Ext.Msg.alert('提示', message);
				}
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function dele() {
        var data = applicationSystemAttributeGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否真的要删除命令？",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    var biid = 0;
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('iid');
                        if(iid == 1 || iid==2){
                        	biid += 1;
                        }
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                            ids.push(iid);
                        }else{
                        	applicationSystemAttributeStore.remove(record);
                        	}
                        });
                    if(biid!=0){
                    	Ext.Msg.alert('提示', '要删除的数据中存在不可操作的项，请重新选择!');
                        return;
                    }
                    if(ids.length>0){
                    	Ext.Ajax.request({
                    		url: 'deleteApplicationSystemAttribute.do',
                    		params: {
                    			deleteIds: ids.join(',')
                    			},
                    		method: 'POST',
	                        success: function(response, opts) {
	                            var success = Ext.decode(response.responseText).success;
	                            // 当后台数据同步成功时
	                            if (success) {
	                                applicationSystemAttributeStore.reload();
	                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                            } else {
	                                Ext.Msg.alert('提示', '删除失败！');
	                            }
	                        },
	                        failure: function(result, request) {
	                            secureFilterRs(result, "操作失败！");
	                        }
	                    });
                    }else{
                    	applicationSystemAttributeGrid.getView().refresh();
                    }
                }
            });
        }
    }

    /** 窗口尺寸调节* */
    contentPanel.on('resize',
    function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(applicationSystemAttributeGrid);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
});