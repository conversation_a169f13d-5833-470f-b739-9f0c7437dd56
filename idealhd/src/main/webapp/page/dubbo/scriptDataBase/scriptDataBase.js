Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    var dataBaseStore;
    var dataBaseGrid;
    // 清理主面板的各种监听时间
   // destroyRubbish();
    Ext.define('dataBaseModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IDATABASEID',
            type: 'long'
        },
        {
            name: 'IDATABASENAME',
            type: 'string'
        },
        {
            name: 'IDATABASEIP',
            type: 'string'
        },
        {
            name: 'IDATABASETYPE',
            type: 'string'
        },
        {
            name: 'IDATABASEMEMO',
            type: 'string'
        },
        {
            name: 'IDATABASEDISK',
            type: 'string'
        },
        {
            name: 'IDATABASEDIRECTORY',
            type: 'string'
        },
        {
            name: 'IDATABASEVERSION',
            type: 'string'
        },
        {
            name: 'state',
            type: 'string'
        }]
    });
    
    dataBaseStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 5,
        model: 'dataBaseModel',
        proxy: {
            type: 'ajax',
            url: 'dataBaseList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var dataBaseTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "oracle"
        },
        {
            "name": "db2"
        },
        {
            "name": "mysql"
        },
        ]
    });
    
    dataBaseStore.on('beforeload', function(store, options) {
        var new_params = {
            bsName: nameField.getValue()
        };

        Ext.apply(dataBaseStore.proxy.extraParams, new_params);
    });
    
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IDATABASEID',
        width: 40,
        hidden: true
    },
    {
        text: '数据库名',
        dataIndex: 'IDATABASENAME',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '服务器IP',
        dataIndex: 'IDATABASEIP',
        width: 200,
//        flex: 1,
//        editor: {
//            allowBlank: false
//        }
        renderer:getAgentIP
    },
    {
    	text: '数据库类型',
        dataIndex: 'IDATABASETYPE',
        width: 150,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataBaseTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name'
        })
    },
    {
        text: '版本',
        dataIndex: 'IDATABASEVERSION',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '内存',
        dataIndex: 'IDATABASEMEMO',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '磁盘',
        dataIndex: 'IDATABASEDISK',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '数据库安装根目录',
        dataIndex: 'IDATABASEDIRECTORY',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    }/*,
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 150,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state'); // 其实是requestID
            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showbsTypeWindow(' + iid + ','+ state + ')">' + '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;配置类型' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
        }
    }*/];
    
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataBaseStore,
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '数据库名',
        labelWidth: 100,
        labelAlign: 'right',
        name: 'dataBaseNameParam',
        width: '30%'
    });

    dataBaseGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        id: 'dataBaseGrid',
        store: dataBaseStore,
        selModel: selModel,
        plugins: [cellEditing],
        bbar: pageBar,
        border: false,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        dockedItems: [{
            xtype: 'toolbar',
            items: [nameField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                	QueryMessage();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                text: '增加',
                cls: 'Common_Btn',
               // iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveDatabase
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
               // iconCls:'sc_delete',
                disabled: true,
                handler: deleteDataBase
            }]
        }]
    });

    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
		dataBaseStore.reload({
			params : {
				start : 0,
				limit : 5,
				baseName : nameField.getValue()
			}
		});
	}
    
    dataBaseGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        dataBaseGrid.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptService_grid_areaDataBase",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [dataBaseGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
   
    function clearQueryWhere() {
    	nameField.setValue('');
    }
    function add() {
        var store = dataBaseGrid.getStore();
        var p = {
    		IDATABASEIP: '',
    		IDATABASEID: '',
    		IDATABASENAME: '',
    		IDATABASEDISK: '',
    		IDATABASEDIRECTORY: '',
    		IDATABASEVERSION: '',
    		IDATABASEMEMO: '',
            IDATABASETYPE:''
        };
        store.insert(0, p);
        dataBaseGrid.getView().refresh();
    }
    function saveDatabase() {
        var m = dataBaseStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var IDatabasename = m[i].get("IDATABASENAME").trim();
            if ("" == IDatabasename || null == IDatabasename) {
                setMessage('数据库名不能为空！');
                return;
            }
            
//            var IDatabaseip = m[i].get("IDATABASEIP").trim();
//            if ("" == IDatabaseip || null == IDatabaseip) {
//                setMessage('服务器IP不能为空！');
//                return;
//            }
//            
//            if(!checkIp(IDatabaseip)){
//            	setMessage('IP格式不正确！');
//                return;
//            }
            
            var IDatabasetype = m[i].get("IDATABASETYPE").trim();
            if ("" == IDatabasetype || null == IDatabasetype) {
                setMessage('数据库类型不能为空！');
                return;
            }
            
            var IDATABASEMEMO = m[i].get("IDATABASEMEMO").trim();
            if ("" == IDATABASEMEMO || null == IDATABASEMEMO) {
                setMessage('内存不能为空！');
                return;
            }
            
            var IDatabaseversion = m[i].get("IDATABASEVERSION").trim();
            if ("" == IDatabaseversion || null == IDatabaseversion) {
                setMessage('版本不能为空！');
                return;
            }
            
            var IDatabasedisk = m[i].get("IDATABASEDISK").trim();
            if ("" == IDatabasedisk || null == IDatabasedisk) {
                setMessage('磁盘不能为空！');
                return;
            }
            
            var IDatabasedirectory = m[i].get("IDATABASEDIRECTORY").trim();
            if ("" == IDatabasedirectory || null == IDatabasedirectory) {
                setMessage('安装目录不能为空！');
                return;
            }
            
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveDatabase.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    dataBaseStore.modified = [];
                    dataBaseStore.reload();
                    Ext.Msg.alert('提示', message);
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function checkIp(val){
    	var regObj = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/   
    	 if (regObj.test(val)) {
             return true;
         } else {
             return false;
         }
    }
    
    function deleteDataBase() {
        var data = dataBaseGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据库服务器?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('IDATABASEID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }
                    });
                    if (ids.length == 0) {
                        dataBaseStore.reload();
                        return;
                    }
                    
                    Ext.Ajax.request({
                        url: 'deleteDataBase.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dataBaseStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                            	dataBaseStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            });
        }
    }
});

function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}

//AgentIP显示
function getAgentIP(value, p, record) 
{
	var displayValue = value;
	if(null==displayValue || ""==displayValue)
	{
		displayValue= "AgentIP";
	}
	return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"cpipSelectShow('"
	+record.get("IDATABASEID")+"','"+record.get("IDATABASEIP")+"');\">"
				+"<span class='abc'>"
				+displayValue
				+"</span>"
			+"</a>";
} 
//function cpipSelectShow(dsid,agentIp)
//{
//	alert('select cpip ... dsid='+dsid);return;
//}