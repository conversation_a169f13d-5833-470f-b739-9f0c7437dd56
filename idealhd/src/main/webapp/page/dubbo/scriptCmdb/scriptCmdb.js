Ext
		.onReady(function() {
			var agentFrom = 2; // 来自CMDB集成
			var upLoadformPane = '';
			var win;
			var upldWin = '';
			var ftpbindwin;
			destroyRubbish();
			Ext.create('Ext.window.MessageBox');
			Ext.tip.QuickTipManager.init();
			Ext.define('model', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'id',
					type : 'string'
				}, {
					name : 'aid',
					type : 'string'
				}, {
					name : 'sysname',
					type : 'string'
				}, {
					name : 'appname',
					type : 'string'
				}, {
					name : 'hostname',
					type : 'string'
				}, {
					name : 'cpstate',
					type : 'string'
				}, {
					name : 'cptype',
					type : 'string'
				}, {
					name : 'agentip',
					type : 'string'
				}, {
					name : 'port',
					type : 'string'
				}, {
					name : 'ostype',
					type : 'string'
				}, {
					name : 'osversion',
					type : 'string'
				}, {
					name : 'purpose',
					type : 'string'
				}, {
					name : 'state',
					type : 'string'
				}, {
					name : 'flag',
					type : 'string'
				},{
				      name : 'isource_path',
				      type : 'string'
				    },{
					  name : 'itarget_path',
					  type : 'string'
					}, {
				      name : 'ibackup_path',
				      type : 'string'
				    }, {
					    name : 'ioperation_user',
					    type : 'string'
					}, {
					    name : 'ioperation_password',
					    type : 'string'
					}, {
						name : 'ios_name',
						type : 'string'
					}, {
						name : 'udpateosname',
						type : 'string'
					}, {
						name : 'iconnect_type',
						type : 'string'
					}, {
						name : 'iconnect_port',
						type : 'string'
					}, {
				      name : 'itransmission_type',
				      type : 'string'
				    }, {
				      name : 'itransmission_ip',
				      type : 'string'
				    }, {
				      name : 'itransmission_prot',
				      type : 'string'
					}, {
				      name : 'itransmission_user',
				      type : 'string'
					} ]
			});
			var osStore = Ext.create('Ext.data.JsonStore', {
				fields : [ 'osname' ],
				autoDestroy : true,
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'osList.do',
					reader : {
						type : 'json',
						root : 'oslist'
					}
				}
			});
			var osCmdbStore = Ext.create('Ext.data.Store', {
				fields : [ 'ostypeId', 'ostype' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'osCmdbList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			// var os_type = Ext.create('Ext.form.field.ComboBox', {
			// name : 'ostype',
			// labelWidth : 58,
			// columnWidth : .2,
			// queryMode : 'local',
			// fieldLabel : '系统类别',
			// padding : '5',
			// displayField : 'ostype',
			// valueField : 'ostypeId',
			// editable : false,
			// queryMode : 'local',
			// emptyText : '--请选择系统类别--',
			// store : osCmdbStore,
			// listeners : {
			// change : function() {
			//						
			// }
			// }
			// });
			var envStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
					"id" : "0",
					"name" : "测试"
				}, {
					"id" : "1",
					"name" : "生产"
				} ]
			});

			var envCombo = Ext.create('Ext.form.field.ComboBox', {
				margin : '5',
				store : envStore,
				queryMode : 'local',
				labelAlign : 'right',
				width : '20%',
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				displayField : 'name',
				valueField : 'id',
				triggerAction : "all",
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var app_name = new Ext.form.TextField({
				name : 'appname',
				fieldLabel : '应用名称',
				displayField : 'appname',
				emptyText : '--请输入应用名称--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '28%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var agent_ip = new Ext.form.TextField({
				name : 'agentip',
				fieldLabel : 'AgentIp',
				displayField : 'agentip',
				emptyText : '--请输入agentip--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '28.5%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var host_name = new Ext.form.TextField({
				name : 'hostname',
				fieldLabel : '主机名称',
				displayField : 'hostname',
				emptyText : '--请输入主机名称--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '28%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var sys_name = new Ext.form.TextField({
				name : 'sysname',
				fieldLabel : '系统名称',
				displayField : 'sysname',
				emptyText : '--请输入系统名称--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '28%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var agentStatusStore = Ext.create('Ext.data.Store', {
				fields: ['id', 'name'],
				data : [
					{"id":"-10000", "name":"全部"},
					{"id":"0", "name":"正常"},
					{"id":"1", "name":"异常"},
					{"id":"2", "name":"升级中"}
				]
			});
			
			var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'agentStatus',
				labelWidth : 78,
				queryMode : 'local',
				fieldLabel : 'Agent状态',
				displayField : 'name',
				valueField : 'id',
				editable : false,
				emptyText : '--请选择Agent状态--',
				store : agentStatusStore,
				width : '29%',
				labelAlign : 'right',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var os_type = new Ext.form.TextField({
				name : 'ostype',
				fieldLabel : '系统类型',
				displayField : 'ostype',
				emptyText : '--系统类型--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '29%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var queryBtn = Ext.create('Ext.Button', {
				cls : 'Common_Btn',
				text : '查询',
				handler : queryCenterName
			});
			var saveBtn = Ext.create('Ext.Button', {
				text : '保存',
				cls : 'Common_Btn',
				handler : save
			});
			var addBtn = Ext.create('Ext.Button', {
//				disabled : true,
				cls : 'Common_Btn',
				text : '增加',
				handler : add
			});
			var delBtn = Ext.create('Ext.Button', {
				cls : 'Common_Btn',
				text : '删除',
				handler : del
			});
			// begin.added by manxi_zhao.2016-07-07.
			var importBtn = Ext.create('Ext.Button', {
				// disabled : true,
				text : '导入',
				iconCls:'sc_import',
				handler : importExcel
			});
			var syncBtn = Ext.create('Ext.Button', {
				// disabled : true,
				text : 'CMDB数据同步',
				cls : 'Common_Btn',
				handler : syncCmdb
			});
			var refreshBtn = Ext.create('Ext.Button', {
				// disabled : true,
				text : '状态刷新',
				cls : 'Common_Btn',
				handler : refreshState
			});
			var clearBtn = Ext.create('Ext.Button', {
				// disabled : true,
				text : '清空',
				cls : 'Common_Btn',
				handler : clearQuery
			});
			// end.added by manxi_zhao.2016-07-07.
			var store = Ext.create('Ext.data.Store', {
				model : 'model',
				pageSize : 30,
				proxy : {
					type : 'ajax',
					url : 'scriptCmdbList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				},
				autoLoad : true
			});
			store.on('beforeload', function(store, options) {
				var new_params = {
					appname : form.getForm().findField("appname").getValue(),
					sysname : form.getForm().findField("sysname").getValue(),
					hostname : form.getForm().findField("hostname").getValue(),
					ostype : form.getForm().findField("ostype").getValue(),
					agentip : form.getForm().findField("agentip").getValue(),
					agentstatus : form.getForm().findField("agentStatus").getValue()
				};
				Ext.apply(store.proxy.extraParams, new_params);
			});

			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
				store : store,
				dock : 'bottom',
				displayInfo : true
			});

			var checkBox = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});

			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});
			
			var query_form = Ext.create('Ext.form.Panel', {
		    	region : 'north',
		      	layout : 'anchor',
		      	buttonAlign : 'center',
		      	border : false,
		      	baseCls:'customize_gray_back',
		      	
		      	dockedItems:[{
		      		baseCls:'customize_gray_back',
		      		xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [
					         '->',addBtn, saveBtn, delBtn, syncBtn,refreshBtn,
					          {
		                xtype: 'splitbutton',
		                cls : 'Common_Btn2',
		                textAlign:'center',
		                text: '升级',
		                handler : function() {
		                	this.showMenu();
		    			},
		    			menu: new Ext.menu.Menu({
		    				items: [{
		    		            text: '配置',
		    		            handler: bindFTP
		    		        },/*{
		    		            text: '安装',
		    		            hidden: true,
		    		            handler: installAgent
		    		        },*/{
		    		            text: '升级',
		    		            handler: upgradeAgent
		    		        },{
		    		            text: '升级监控',
		    		            handler: upgradeAgentMonitor
		    		        },{
		    		            text: '清除升级标记',
		    		            handler: clearUpgradeFlag
		    		        }]
		    		    })
		            },{
		                xtype: 'splitbutton',
		                cls : 'Common_Btn2',
		                textAlign:'center',
		                text: '获取信息',
		                handler : function() {
		                	this.showMenu();
		    			},
		    			menu: new Ext.menu.Menu({
		    				items: [/*{
		    		            text: '获取VCS信息',
		    		            hidden: true,
		    		            handler: oneKey
		    		        },{
		    		            text: '获取全部VCS信息',
		    		            hidden: true,
		    		            handler: allKey
		    		        },*/{
		    		            text: '获取Agent信息',
		    		            handler: fetchAgentInfoQuery
		    		        },{
		    		            text: '获取全部Agent信息',
		    		            handler: fetchAgentInfo
		    		        }]
		    		    })
		            }    
					]
		      	}]
			});
			
			var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
				region : 'center',
				store : store,
				forceFit : true,
				loadMask : true,
				padding : grid_space,
				selModel : checkBox,
				disableSelection : false,
				cls:'customize_panel_back',
				ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				columnLines : true,
				border:false,
				columns : [ {
					xtype : "rownumberer",
					text : '序号',
					width : 30,
					resizable : true
				}, {
					dataIndex : 'id',
					text : 'ID',
					hidden : true
				}, {
					dataIndex : 'aid',
					text : 'AID',
					hidden : true
				}, {
					text : '系统名称',
					dataIndex : 'sysname',
					width : 150,
					editor : {
						xtype : 'textfield',
						maxLength : 255
					},
					renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
				}, {
					text : '应用名称',
					dataIndex : 'appname',
					width : 150,
					editor : {
						xtype : 'textfield',
						maxLength : 255
					},
					renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
				}, {
					text : '主机名称',
					dataIndex : 'hostname',
					width : 120,
					editor : {
						xtype : 'textfield',
						maxLength : 255
					},
					renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
				}, {
					text : '系统类型',
					dataIndex : 'ostype',
					width : 80,
					editor : {
						xtype : 'combobox',
						margin : '5',
						store : osStore,
						queryMode : 'local',
						forceSelection : true, // 要求输入值必须在列表中存在
						typeAhead : true, // 允许自动选择
						displayField : 'osname',
						valueField : 'osname',
						triggerAction : "all",
						renderer : function(value, metadata, record) {
							var index = osStore.find('osname', value);
							if (index != -1) {
								return osStore.getAt(index).data.osname;
							} else {
								return value;
							}
						}
					}
				}, {
					text : '系统版本',
					dataIndex : 'osversion',
					width : 80,
					editor : {
						xtype : 'textfield',
						maxLength : 255
					},
					renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
				}, {
					text : '环境',
					dataIndex : 'purpose',
					width : 60,
					editor : envCombo,
					renderer : function(value, metadata, record) {
						if (value == 0) {
							return "测试";
						} else {
							return "生产";
						}
					}
				}, {
					text : '设备状态',
					dataIndex : 'state',
					width : 70
				}, {
					text : '类型',
					dataIndex : 'cptype',
					width : 80,
					editor : {
						xtype : 'textfield',
						maxLength : 255
					},
					renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
				}, {
					text : 'Agent状态',
					dataIndex : 'cpstate',
					width : 80,
					renderer : function(value, metadata, record) {
						if (value == 0) {
							return "正常";
						} else {
							return "异常";
						}
					}
				}, {
					text : 'AgentIP',
					dataIndex : 'agentip',
					width : 100,
					editor : {
						xtype : 'textfield',
						maxLength : 15
					}
				}, {
					text : '端口',
					dataIndex : 'port',
					width : 60,
					editor : {
						xtype : 'numberfield',
						maxValue : 65535,
						minValue : 0
					}
				}, {
					text : '来源',
					dataIndex : 'flag',
					hidden : true,
					width : 60,
					renderer : function(value, metadata, record) {
						if (value == 0) {
							return "CMDB";
						} else {
							return "CREATE";
						}
					}
				},{
					xtype : 'actioncolumn',
					text : '升级配置详情',
					width : 100,
					align : 'center',
					sortable : false,
					menuDisabled : true,
					items : [ {
						//icon : 'images/permission.png',
						iconCls:'monitor_search',
						tooltip : '升级配置详情',
						handler : function (grid, rowIndex) {
							var agentInfo = grid.getStore().getAt(rowIndex);
							if (agentInfo.get('id')) {
								var msgContent = '<p><b>升级源文件路径:</b> '+agentInfo.get('isource_path')+'</p>'+
							                '<p><b>升级目标文件路径:</b> '+agentInfo.get('itarget_path')+'</p>'+
							                '<p><b>备份路径:</b> '+agentInfo.get('ibackup_path')+'</p><br>'+
							                /*'<p><b>操作用户名:</b> '+agentInfo.get('ioperation_user')+'</p>'+
							                '<p><b>操作系统:</b> '+agentInfo.get('udpateosname')+'</p>'+
							                '<p><b>连接方式:</b> '+agentInfo.get('iconnect_type')+'</p>'+
							                '<p><b>连接端口:</b> '+agentInfo.get('iconnect_port')+'</p><br>'+*/
							                '<p><b>文件传输类型:</b> '+agentInfo.get('itransmission_type')+'</p>'+
							                '<p><b>文件服务器地址:</b> '+agentInfo.get('itransmission_ip')+'</p>'+
							                '<p><b>文件服务器端口:</b> '+agentInfo.get('itransmission_prot')+'</p>'+
							                '<p><b>传输用户名:</b> '+agentInfo.get('itransmission_user')+'</p>';
					        	
					        	Ext.Msg.show({
					        		title : '升级配置详情信息',
					        		msg : msgContent,
					        		buttons : Ext.Msg.OK,
					        		icon : Ext.MessageBox.INFO,
					        		width : 500
					        	});
							} else {
								Ext.Msg.alert('详细信息', '该记录没有详细信息!');
							}
						}
					} ]
				} ],
				listeners : {
					// beforeedit : function(editor, e, eOpts) {
					// addBtn.setDisabled(false);
					// if (editor.context.colIdx == 2) {
					// var id = grid.getSelectionModel().getSelection()[0]
					// .get("id");
					// if (id == "") {
					// return true;
					// } else {
					// return false;
					// }
					// }
					// },
					beforeedit : function(editor, e, eOpts) {
						if (e.field == 'agentip' || e.field == 'sysname'
								|| e.field == 'appname'
								|| e.field == 'hostname' || e.field == 'ostype'
								|| e.field == 'osversion'
								|| e.field == 'cptype' || e.field == 'purpose')// 如果要编辑的列为业务系统名字则判断是否为增加，增加可以编辑，否则不可以
						{
							if (e.record.get("flag") == '1'
									|| e.record.get("id") == '') {
								return true;
							} else {
								return false;
							}
						}
					}
				},
				//bbar : pageBar,
				selType : 'cellmodel',
				plugins : [ cellEditing ]
				/*dockedItems : [ {
					baseCls:'customize_gray_back',
					xtype : 'toolbar',
					
					items : [ '->',addBtn, saveBtn, delBtn, syncBtn,refreshBtn,
					          {
		                xtype: 'splitbutton',
		                cls : 'Common_Btn2',
		                textAlign:'center',
		                text: '升级',
		                handler : function() {
		                	this.showMenu();
		    			},
		    			menu: new Ext.menu.Menu({
		    				items: [{
		    		            text: '配置',
		    		            handler: bindFTP
		    		        },{
		    		            text: '安装',
		    		            hidden: true,
		    		            handler: installAgent
		    		        },{
		    		            text: '升级',
		    		            handler: upgradeAgent
		    		        },{
		    		            text: '升级监控',
		    		            handler: upgradeAgentMonitor
		    		        },{
		    		            text: '清除升级标记',
		    		            handler: clearUpgradeFlag
		    		        }]
		    		    })
		            },{
		                xtype: 'splitbutton',
		                cls : 'Common_Btn2',
		                textAlign:'center',
		                text: '获取信息',
		                handler : function() {
		                	this.showMenu();
		    			},
		    			menu: new Ext.menu.Menu({
		    				items: [{
		    		            text: '获取VCS信息',
		    		            hidden: true,
		    		            handler: oneKey
		    		        },{
		    		            text: '获取全部VCS信息',
		    		            hidden: true,
		    		            handler: allKey
		    		        },{
		    		            text: '获取Agent信息',
		    		            handler: fetchAgentInfoQuery
		    		        },{
		    		            text: '获取全部Agent信息',
		    		            handler: fetchAgentInfo
		    		        }]
		    		    })
		            }]
				} ]*/
			});

			var form = Ext.create('Ext.form.Panel', {
				region : 'north',
				border : false,
				baseCls:'customize_gray_back',
				dockedItems : [ {
					xtype : 'toolbar',
					baseCls:'customize_gray_back',
					dock : 'top',
					items : [ sys_name, app_name, host_name ]
				},{
					xtype : 'toolbar',
					baseCls:'customize_gray_back',
					dock : 'top',
					items : [ os_type, agent_ip, agentStatusCb,'->', queryBtn, clearBtn ]
				} ]
			});
			var panel = Ext.create('Ext.panel.Panel', {
				border : false,
				layout : 'border',
				bodyCls:'customize_stbtn',
//				bodyPadding : 5,
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight()-modelHeigth,
				items : [ form,query_form, grid ],
				renderTo : "scriptCmdb_area"
			});
			grid.on('edit', function(editor, e) {
				addBtn.setDisabled(false);
			});
			contentPanel.on('resize', function() {
				panel.setHeight(contentPanel.getHeight()-modelHeigth);
				panel.setWidth(contentPanel.getWidth());
			});

			function queryCenterName() {
				pageBar.moveFirst();
				store.load({
					params : {
						start : 0,
						limit : 30
					}
				});// 数据加载
			}

			function add() {
				addBtn.setDisabled(false);
				var store = grid.getStore();
				var p = {
					id : '',
					sysname : '',
					appname : '',
					hostname : '',
					cpstate : '',
					agentip : '',
					port : '',
					ostype : '',
					osversion : '',
					purpose : '1',
					cptype : '固定IP-普通',
					state : '已投产',
					flag : '1'
				};
				store.insert(0, p);
				grid.getView().refresh();
			}

			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(panel);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
			function syncCmdb() {
				Ext.Msg.confirm('系统提示', '确定要同步CMDB数据吗?', function(btn) {
					if (btn == 'yes') {
						Ext.MessageBox.wait("数据处理中...", "进度条");
						Ext.Ajax.request({
							url : 'cmdbSyncData.do',
							method : "post",
							timeout : 1000000,
							success : function(response, opts) {
								var message = Ext.decode(response.responseText).message;
								Ext.Msg.alert('提示', message);
								store.reload();// 数据加载
							},
							failure : function(result, request) {
								secureFilterRs(result, "操作失败！");
							}
						});
					}

				}, this);
			}
			function refreshState() {
				var store = grid.getStore();
				var selModel = grid.getSelectionModel();
				if (selModel.hasSelection()) {
					var selected = selModel.getSelection();
					var ids = [];
					var delFlag = 0;
					Ext.each(selected, function(item) {
						if ('undefined' == item.data.id || '' == item.data.id
								|| null == item.data.id) {
						} else {
							ids.push(item.data.id);
						}
					});
					if (ids.length > 0) {
						Ext.Msg.confirm('系统提示', '确定要刷新Agent状态吗?', function(btn) {
							if (btn == 'yes') {
								Ext.Ajax.request({
									url : 'refreshAgentState.do',
									method : "post",
									success : function(response, opts) {
										Ext.Msg.alert('提示', '状态刷新成功');
										store.reload();// 数据加载
									},
									failure : function(result, request) {
										secureFilterRs(result, "操作失败！");
									},
									params : {
										ids : ids
									}
								});
							}
						}, this);
					}
				} else {
					Ext.Msg.alert('提示', '请选择要刷新Agent状态的记录');
				}
			}
			function clearQuery() {
				form.getForm().findField("appname").setValue('');
				form.getForm().findField("sysname").setValue('');
				form.getForm().findField("hostname").setValue('');
				form.getForm().findField("ostype").setValue('');
				form.getForm().findField("agentip").setValue('');
				form.getForm().findField("agentStatus").setValue('');
			}
			function del() {
				var store = grid.getStore();
				var selModel = grid.getSelectionModel();
				if (selModel.hasSelection()) {
					var selected = selModel.getSelection();
					var ids = [];
					var delFlag = 0;
					Ext.each(selected, function(item) {
						// if (item.data.cpstate != 0) {
						if (item.data.flag == 0) {
							delFlag = 1;
							return;
						}
						if ('undefined' == item.data.id || '' == item.data.id
								|| null == item.data.id) {
							store.remove(item);
						} else {
							ids.push(item.data.id);
						}
					});
					if (delFlag == 1) {
						Ext.Msg.alert('提示', 'CMDB集成数据不可删除!');
						// Ext.Msg.alert('提示', 'Agent状态为“正常”不能删除，需先停止Agent!');
						return;
					}
					if (ids.length > 0) {
						Ext.Msg.confirm('系统提示', '确定要删除吗?', function(btn) {
							if (btn == 'yes') {
								Ext.Ajax.request({
									url : 'cmdbComputerDelete.do',
									method : "post",
									success : function(response, opts) {
										Ext.Msg.alert('提示', '删除成功');
										store.reload();// 数据加载
									},
									failure : function(result, request) {
										secureFilterRs(result, "操作失败！");
									},
									params : {
										ids : ids
									}
								});
							}

						}, this);
					}
				} else {
					Ext.Msg.alert('提示', '请选择您要删除的行');
				}
			}

			function save() {
				var store = grid.getStore();
				var m = store.getModifiedRecords();
				if (m.length < 1) {
					Ext.Msg.alert('提示', '您没有进行任何修改，无需保存');
					return;
				}

				var jsonData = "[";
				for (var i = 0, len = m.length; i < len; i++) {
					var n = 0;
					var purpose = m[i].get("purpose").trim();
					var agentip = m[i].get("agentip").trim();
					var port = m[i].get("port").trim();
					if ('' == purpose) {
						Ext.Msg.alert('提示', '增加行环境不能为空');
						return;
					}
					if ('' != agentip) {
						if (!checkIP(agentip)) {
							Ext.Msg.alert('提示', '增加加行AgentIP格式不正确!');
							return;
						}
					}
					if ('' == port) {
						Ext.Msg.alert('提示', '增加行端口不能为空');
						return;
					}
					// for (var k = 0; k < store.getCount(); k++) {
					// var record = store.getAt(k);
					// var agent_ip = record.data.agentip.trim();
					// var pur_pose = record.data.purpose.trim();
					// if (agentip == agent_ip && purpose == pur_pose) {
					// n = n + 1;
					// }
					// }
					// if (n > 1) {
					// setMessage('AgentIp与环境不能存在同时重复记录！');
					// return;
					// }
					var ss = Ext.JSON.encode(m[i].data);
					console.log(m[i]);
					if (i == 0)
						jsonData = jsonData + ss;
					else
						jsonData = jsonData + "," + ss;
				}
				jsonData = jsonData + "]";
				Ext.MessageBox.wait("数据处理中...", "进度条");
				Ext.Ajax
						.request({
							url : 'savecmdbComputer.do',
							method : 'POST',
							params : {
								jsonData : jsonData
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								if (success) {
									store.modified = [];
									store.reload();
									Ext.Msg.alert('提示', '保存成功');
								} else {
									Ext.Msg.alert('提示', '保存失败！');
								}
							},
							failure : function(result, request) {
								secureFilterRs(result, "保存失败！");
							}
						});
			}
			// begin.added by manxi_zhao.2016-07-07.
			function importExcel() {
				// 销毁win窗口
				if (!(null == upldWin || undefined == upldWin || '' == upldWin)) {
					upldWin.destroy();
					upldWin = null;
				}

				if (!(null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
					upLoadformPane.destroy();
					upLoadformPane = null;
				}
				// 导入文件Panel
				upLoadformPane = Ext
						.create(
								'Ext.form.Panel',
								{
									id : 'upLoadpanel',
									width : 370,
									height : 170,
									frame : true,
									items : [ {
										id : 'fileFildId',
										xtype : 'filefield',
										name : 'file', // 设置该文件上传空间的name，也就是请求参数的名字
										fieldLabel : '选择文件',
										labelWidth : 80,
										msgTarget : 'side',
										anchor : '100%',
										buttonText : '浏览...',
										width : 370
									} ],
									buttons : [ {
										id : 'upldBtnId',
										text : '批量导入',
										handler : function() {
											var form = this.up('form')
													.getForm();
											var upfile = form.findField("file")
													.getValue();
											if (upfile == '') {
												Ext.Msg.alert('提示', "请选择文件...");
												return;
											}

											var hdtmpFilNam = form.findField(
													"file").getValue();
											if (!checkFile(hdtmpFilNam)) {
												form.findField("file")
														.setRawValue('');
												return;
											}

											if (form.isValid()) {
												Ext.MessageBox.wait("数据处理中...",
														"进度条");
												// alert("导入中。。。");
												// return;
												form
														.submit({
															url : 'importComputerExcel.do',
															success : function(
																	form,
																	action) {
																var msg = Ext
																		.decode(action.response.responseText).message;
																Ext.Msg.alert(
																		'提示',
																		msg);
																upldWin.close();
																// 重新加载数据
																var aGrid = Ext
																		.getCmp("cmpGridId");
																var aBar = Ext
																		.getCmp("pageBarId");
																aBar
																		.moveFirst();
																aGrid.store
																		.load({
																			params : {
																				start : 0,
																				limit : 30
																			}
																		});// 数据加载
																return;
															},
															failure : function(
																	form,
																	action) {
																secureFilterRsFrom(
																		form,
																		action);
															}
														});
											}
										}
									} ]
								});
				// 导入窗口
				upldWin = Ext.create('Ext.window.Window', {
					id : 'upldWinId',
					title : '设备信息批量导入',
					width : 400,
					height : 200,
					// layout: 'fit',
					modal : true,
					closeAction : 'destroy',
					items : [ upLoadformPane ]
				}).show();
				upldWin.on("beforeshow", function(self, eOpts) {
					var form = Ext.getCmp("upldBtnId").up('form').getForm();
					form.reset();
				});

				upldWin.on("destroy", function(self, eOpts) {
					upLoadformPane.destroy();
				});
			}

			function checkFile(fileName) {
				var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
				if (!file_reg.test(fileName)) {
					Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
					// Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)');
					return false;
				}
				return true;
			}

			// end.added by manxi_zhao.2016-07-07.

			function checkIP(ip) {
				var reg = /^(([01]?[\d]{1,2})|(2[0-4][\d])|(25[0-5]))(\.(([01]?[\d]{1,2})|(2[0-4][\d])|(25[0-5]))){3}$/;
				if (reg.test(ip)) {
					return true;
				} else {
					return false;
				}
			}
			
			Ext.define('UpgradeInfoModel', {
			    extend : 'Ext.data.Model',
			    fields : [ {
			      name : 'iagentup_id',
			      type : 'long'
			    },{
			      name : 'isource_path',
			      type : 'string'
			    },{
				  name : 'itarget_path',
				  type : 'string'
				}, {
			      name : 'ibackup_path',
			      type : 'string'
			    }, {
				    name : 'ioperation_user',
				    type : 'string'
				}, {
					name : 'ios_name',
					type : 'string'
				}, {
					name : 'iconnect_type',
					type : 'string'
				}, {
					name : 'iconnect_port',
					type : 'string'
				},{
			      name : 'itransmission_type',
			      type : 'string'
			    }, {
			      name : 'itransmission_ip',
			      type : 'string'
			    }, {
			      name : 'itransmission_prot',
			      type : 'string'
				}, {
			      name : 'itransmission_user',
			      type : 'string'
				}, {
			      name : 'itransmission_password',
			      type : 'string'
				}]
			});
			
			var upgradeInfoColumns = [ {
				  header: '序号', xtype: 'rownumberer',width:40},{
				    text : 'ID',
				    dataIndex : 'iagentup_id',
				    flex : 1,
				    hidden : true
				  },{
				    text : '升级源文件路径',
				    flex : 1,
					  dataIndex : 'isource_path'
				  },{
				    text : '升级目标文件路径',
				    flex : 1,
					  dataIndex : 'itarget_path'
				  },{
				    text : '备份路径',
				    flex : 1,
					  dataIndex : 'ibackup_path'
				  },{
					  text : '操作用户名',
					  width : 100,
					  hidden: true,
					  dataIndex : 'ioperation_user'
				  },{
					  text : '操作系统',
					  width : 80,
					  hidden: true,
					  dataIndex : 'ios_name'
				  },{
					  text : '连接方式',
					  width : 70,
					  hidden: true,
					  dataIndex : 'iconnect_type'
				  },{
					  text : '连接端口',
					  width : 70,
					  hidden: true,
					  dataIndex : 'iconnect_port'
				  },{
				    text : '文件传输类型',
				    width : 90,
					  dataIndex : 'itransmission_type'
				  },{
				      text : '文件服务器地址',
				      dataIndex : 'itransmission_ip',
				      width : 100
				  },{
				      text : '文件服务器端口',
				      dataIndex : 'itransmission_prot',
				      width : 100
				  }, {
				    text : '传输用户名',
				    dataIndex : 'itransmission_user',
				    width : 100
				  }];
			
			var upgradeInfoStore = Ext.create ('Ext.data.Store', {
			    autoLoad : false,
			    autoDestroy : true,
			    model : 'UpgradeInfoModel',
			    pageSize : 50,
			    proxy : {
			      type : 'ajax',
			      url : 'getAgentMaintainUpgradeInfo.do',
			      reader : {
			        type : 'json',
			        root : 'dataList',
			        totalProperty : 'total'
			      }
			    }
			});
			
			var bsUpgradeInfoPageBar = Ext.create('Ext.PagingToolbar', {
		        store: upgradeInfoStore,
		        dock: 'bottom',
		        displayInfo: true,
		        emptyMsg: "没有记录"
		    });
			
			var upgradeinfo_panel = Ext.create('Ext.ux.ideal.grid.Panel', {
		        store: upgradeInfoStore,
		        selModel: Ext.create('Ext.selection.CheckboxModel', {
		            checkOnly: true
		        }),
		        border: true,
		        cls:'customize_panel_back',
		        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		        columnLines: true,
		        columns: upgradeInfoColumns,
		        /*bbar: bsUpgradeInfoPageBar,*/
		        dockedItems: [{
		            xtype: 'toolbar',
		            items: [{
		                text: '绑定',
		                icon: '',
		                cls: 'Common_Btn',
		                handler: function () {
		                	var data = upgradeinfo_panel.getView().getSelectionModel().getSelection();
		                    if (data.length != 1) {
		                        Ext.Msg.alert('提示', '请选择且只能选择一条记录!');
		                        return;
		                    }
		                    var grid_data = grid.getView().getSelectionModel().getSelection();
		                    if (grid_data.length == 0) {
		                        Ext.Msg.alert('提示', '没有选择Agent记录!');
		                        return;
		                    }
		                    
		                    var agent_ids = [];
		                    var agent_upgrade_id;
		                    Ext.Array.each(grid_data, function(record) {
		                        var agentId = record.get('id');
		                        if (agentId) {
		                        	agent_ids.push(agentId);
		                        }
		                    });
		                    Ext.Array.each(data, function(record) {
		                        agent_upgrade_id = record.get('iagentup_id');
		                    });
		                    
		                    Ext.Ajax.request({
		                        url: 'agentBindUpgradeInfo.do',
		                        params: {
		                            agentIds: agent_ids.join(','),
		                            agentUpId: agent_upgrade_id,
		                            agentFrom: agentFrom 
		                        },
		                        method: 'POST',
		                        success: function(response, opts) {
		                            var success = Ext.decode(response.responseText).success;
		                            var message = Ext.decode(response.responseText).message;
		                            // 当后台数据同步成功时
		                            Ext.Msg.alert('提示', message);
		                            ftpbindwin.close();
		                            pageBar.moveFirst();
		                        },
		                        failure: function(result, request) {
		                            secureFilterRs(result, "操作失败！");
		                        }
		                    });
		                }
		            }]
		        }]
		    });
			
			function bindFTP() {
		    	var data = grid.getView().getSelectionModel().getSelection();
		        if (data.length == 0) {
		            Ext.Msg.alert('提示', '请先选择记录!');
		            return;
		        }
		    	if(ftpbindwin) {
		    		ftpbindwin.show();
		    	} else {
		    		ftpbindwin = Ext.create('Ext.window.Window', {
		          		title : 'Agent准备信息',
		          		layout : 'fit',
		          		autoScroll : true,
		          		modal : true,
		          		resizable : false,
		          		closeAction : 'hide',
		          		width : contentPanel.getWidth()-50,
		          		height : contentPanel.getHeight()-20,
		          		items: [upgradeinfo_panel]
		          	}).show();
		    	}
		    	bsUpgradeInfoPageBar.moveFirst();
		    }
			
			function upgradeAgent() {
		    	var data = grid.getView().getSelectionModel().getSelection();
		        if (data.length == 0) {
		            Ext.Msg.alert('提示', '请先选择您要升级的Agent!');
		            return;
		        } else {
		        	var msgContent = "";
		        	msgContent += "<ol>";
		        	msgContent += "<li>";
		        	msgContent += "升级Agent存在一定风险，请确认您已经了解所有存在的风险并且知道如何处理错误";
		        	msgContent += "</li>";
		        	msgContent += "<li>";
		        	msgContent += "Agent升级过程中，不接受Server发来的任何执行活动请求";
		        	msgContent += "</li>";
		        	msgContent += "<li>";
		        	msgContent += "Agent的升级日志存在于Agent安装目录下的log/agentUpgrade.log中，如果遇到升级出现错误，可以查看该日志";
		        	msgContent += "</li>";
		        	msgContent += "<li>";
		        	msgContent += "Agent备份路径必须存在且为文件夹。";
		        	msgContent += "</li>";
		        	msgContent += "<li>";
		        	msgContent += "Agent升级目标路径必须存在且为文件夹。";
		        	msgContent += "</li>";
		        	msgContent += "<li>";
		        	msgContent += "Agent升级补丁必须为zip文件。";
		        	msgContent += "</li>";
		        	msgContent += "<ol>";
		        	
		        	Ext.Msg.show({
		        		title : '请认真阅读以下提示',
		        		msg : msgContent,
		        		buttons : Ext.Msg.OKCANCEL,
		        		icon : Ext.MessageBox.INFO,
		        		width : 500,
		        		fn: function(btn) {
		        			if (btn == "ok") {
		                        var ids = [];
		                        Ext.Array.each(data, function(record) {
		                            var agentId = record.get('id');
		                            if (agentId) {
		                                ids.push(agentId);
		                            }
		                        });

		                        Ext.Ajax.request({
		                            url: 'upgradeAgentForCMDB.do',
		                            params: {
		                                ids: ids.join(','),
		                                agentFrom: agentFrom // Agent管理
		                            },
		                            method: 'POST',
		                            success: function(response, opts) {
		                                var success = Ext.decode(response.responseText).success;
		                                var message = Ext.decode(response.responseText).message;
		                                Ext.Msg.alert('提示', message);
		                            },
		                            failure: function(result, request) {
		                                secureFilterRs(result, "操作失败！");
		                            }
		                        });
		                    }
		        		}
		        	});
		        }
		    }
			
			function upgradeAgentMonitor() {
		    	win = Ext.create('Ext.window.Window', {
		      		title : 'Agent升级监控',
		      		autoScroll : true,
		      		modal : true,
		      		resizable : false,
		      		closeAction : 'destroy',
		      		width : contentPanel.getWidth()-50,
		      		height : contentPanel.getHeight()-20,
		      		loader : {
		      			url : "AgentUpgradeMonitor.do?agentFrom="+agentFrom,
		      			autoLoad : true,
		      			autoDestroy : true,
		      			scripts : true
		      		}
		      	}).show();
		    }
			
			function clearUpgradeFlag() {
		    	var data = grid.getView().getSelectionModel().getSelection();
		        if (data.length == 0) {
		            Ext.Msg.alert('提示', '请先选择记录!');
		            return;
		        } else {
		            Ext.Msg.confirm("请确认", "是否真的要清除选中Agent的升级标记？",
		            function(button, text) {
		                if (button == "yes") {
		                	Ext.Msg.confirm("请确认", "请再次确认 -- 是否真的要清除选中Agent的升级标记？", function(button, text) {
		                        if (button == "yes") {
		                        	var ids = [];
		                            Ext.Array.each(data, function(record) {
		                                var agentId = record.get('id');
		                                if (agentId) {
		                                    ids.push(agentId);
		                                }
		                            });

		                            Ext.Ajax.request({
		                                url: 'deleteUpgradeAgentFlagForCMDB.do',
		                                params: {
		                                    ids: ids.join(','),
		                                    agentFrom: agentFrom
		                                },
		                                method: 'POST',
		                                success: function(response, opts) {
		                                    var success = Ext.decode(response.responseText).success;
		                                    var message = Ext.decode(response.responseText).message;
		                                    Ext.Msg.alert('提示', message);
		                                    if(success) {
		                                    	pageBar.moveFirst();
		                                    }
		                                },
		                                failure: function(result, request) {
		                                    secureFilterRs(result, "操作失败！");
		                                }
		                            });
		                        }
		                    });
		                }
		            });
		        }
		    }
			
			function fetchAgentInfo() {
		    	commonMask.show();
		        Ext.Ajax.request({
		            url: 'fetchAgentInfo.do',
		            params: {
		            	agentFrom: agentFrom 
		            },
		            timeout: 300000,
		            method: 'POST',
		            success: function(response, opts) {
		                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
		                commonMask.hide();
		            },
		            failure: function(result, request) {
		                secureFilterRs(result, "同步出错！");
		                commonMask.hide();
		            }
		        });
		    }
		    function fetchAgentInfoQuery(){
		    	var data = grid.getView().getSelectionModel().getSelection();
		        if (data.length == 0) {
		        	Ext.Msg.alert('提示', '请至少选择一条要获取的记录！');
		            return;
		        } else {
		        	var ids = [];
		            Ext.Array.each(data, function(record) {
		                var agentId = record.get('id');
		                var agentState = record.get('iagentState');
		                if (agentId) {
		                	ids.push(agentId);
		                }
		            });
		            commonMask.show();
		            Ext.Ajax.request({
		                url: 'fetchAgentInfoQuery.do',
		                params: {
		                	agentIds: ids.join(','),
		                	agentFrom: agentFrom
		                },
		                timeout: 300000,
		                method: 'POST',
		                success: function(response, opts) {
		                	commonMask.hide();
		                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
		                },
		                failure: function(result, request) {
		                	commonMask.hide();
		                    secureFilterRs(result, "同步出错！");
		                }
		            });
		        }
		    }
		});