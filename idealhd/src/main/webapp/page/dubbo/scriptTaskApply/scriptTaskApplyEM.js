Ext.Loader.setConfig({
	enabled : true,
	disableCaching : false,
	paths : {
		'Go' : 'js/ux/gooo'
	}
});
Ext.onReady(function(){
	destroyRubbish();
	// 鼠标悬浮提醒用
	Ext.QuickTips.init();
	let scriptuuid="";
	let iidForTaskAudi = "";
	let submitTaskName="";
	let serviceNameForTaskAudi="";
	let serviceName="";
	let attachmentIds = [];
	let creatCronWin;
	let scriptLevelForTaskAudi=0;
	let scriptTypeForTaskAudi;
	//选择的agent数组
	let choosedAgentIds=[];
	let taskNameValue ='';
	let choosedAgentRecordsMap=new Map();
	let sysidGlobal;//业务系统id
	let protypeGlobal;//业务系统类型 为了区别查询时是查computerid还是agentid
	let globalParams={};
	let startData={};
	let execStartUser = "";
	let sysAgent =new Map();
	let sysAgentFlagMap =new Map();
	let cpdsMap = {};//<cpid,dsid>
	let selCpId = -1;
	let finalChosedAgentsAndDbSources = {};

	let sName = new Ext.form.TextField({
		name: 'serviceName',
		fieldLabel: '服务名称',
		displayField: 'serverName',
		emptyText: '-请输入服务名称-',
		labelWidth: 60,
		padding: '5',
		labelAlign: 'right',
		width: '100%',
        value:serviceName_history
	});
	let bussData = Ext.create('Ext.data.Store', {
		fields: ['iid', 'bsName'],
		autoLoad: true,
		proxy: {
			type: 'ajax',
			url: 'bsManager/getBsAll.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	let bussCb = Ext.create('Ext.form.field.ComboBox', {
		name: 'sysName',
		labelWidth: 60,
		// columnWidth : .2,
		labelAlign: 'right',
		width:  '100%',
		queryMode: 'local',
		fieldLabel: '一级分类',
		padding: '5',
		displayField: 'bsName',
		valueField: 'iid',
		editable: false,
		emptyText: '-请选择一级分类-',
		store: bussData,
		listeners: {
			change: function() { // old is keyup
				bussTypeCb.clearValue();
				bussTypeCb.applyEmptyText();
				bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
				if(this.value != null && this.value !=''){
					bussTypeData.load({
						params: {
							fk: this.value
						}
					});
				}
			},
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					ssScriptGrid.ipage.moveFirst();

				}
			}
		}
	});
	let bussTypeData = Ext.create('Ext.data.Store', {
		fields: ['sysTypeId', 'sysType'],
		autoLoad: false,
		proxy: {
			type: 'ajax',
			url: 'bsManager/getBsTypeByFk.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	/** 二级分类* */
	let bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
		name: 'bussType',
		padding: '5',
		labelWidth: 60,
		labelAlign: 'right',
//        width: reviewSwitch?'16.7%':'16.7%',
		width: '100%',
		queryMode: 'local',
		fieldLabel: '二级分类',
		displayField: 'sysType',
		valueField: 'sysTypeId',
		editable: false,
		emptyText: '-请选择二级分类-',
		store: bussTypeData,
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					ssScriptGrid.ipage.moveFirst();
				}
			}
		}
	});
	if(bussName_history!='-1' && bussName_history!='') {
		bussCb.setValue(parseInt(bussName_history));
	}
	if(bussTypeName_history!='-1' && bussTypeName_history!='') {
		bussTypeCb.setValue(parseInt(bussTypeName_history));
	}
	function reset() {
		return function () {
			execStartUser = '';
			scriptuuid = '';
			submitTaskName = '';
			serviceName = '';
			protypeGlobal = '';
			sysAgent.clear();
			cpdsMap = {};
			selCpId = -1;
			finalChosedAgentsAndDbSources = {};
			choosedAgentRecordsMap.clear();
			this.up('form').getForm().reset();
			ssScriptGrid.ipage.moveFirst();
			sysidGlobal = '';
			/*sysListGrid_store.removeAll();
			agentGrid_store.removeAll();*/
			paramStore.removeAll();
			attachmentStore.removeAll();
			sysListGrid.ipage.moveFirst();
			agentGrid.ipage.moveFirst();
			choosedAgentIds=[];
		};
	}

	let ssFormQuery = Ext.create('Ext.ux.ideal.form.Panel', {
		width: 330,
		border: false,
		name :'ssFormQuery',
		region : 'north',
		layout : 'anchor',
		iqueryFun : function(){
			ssScriptGrid.ipage.moveFirst();
			execStartUser = '';
			scriptuuid = '';
			submitTaskName = '';
			serviceName = '';
			protypeGlobal = '';
			sysAgent.clear();
			choosedAgentRecordsMap.clear();
			cpdsMap = {};
			selCpId = -1;
			finalChosedAgentsAndDbSources = {};
			/*sysListGrid.ipage.moveFirst();
			agentGrid.ipage.moveFirst();*/
			sysListGrid_store.removeAll();
			agentGrid_store.removeAll();
			choosedAgentIds=[];
		},
		dockedItems: [{xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [sName]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [bussCb]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [bussTypeCb]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function(){
					ssScriptGrid.ipage.moveFirst();
					execStartUser = '';
					scriptuuid = '';
					submitTaskName = '';
					serviceName = '';
					protypeGlobal = '';
					sysAgent.clear();
					choosedAgentRecordsMap.clear();
					cpdsMap = {};
					selCpId = -1;
					finalChosedAgentsAndDbSources = {};
					/*sysListGrid.ipage.moveFirst();
                    agentGrid.ipage.moveFirst();*/
					sysListGrid_store.removeAll();
					agentGrid_store.removeAll();
					choosedAgentIds=[];
				}
			},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '重置',
					handler : reset()
				}]
		}]
	});


	Ext.define('scriptService', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iid',
			type: 'string'
		},
			{
				name: 'scriptuuid',
				type: 'string'
			},
			{
				name: 'serviceName',
				type: 'string'
			},
			{
				name: 'keywords',
				type: 'string'
			},
			{
				name: 'sysName',
				type: 'string'
			},
			{
				name: 'bussName',
				type: 'string'
			},
			{
				name: 'scriptType',
				type: 'string'
			},
			{
				name: 'scriptName',
				type: 'string'
			},
			{
				name: 'servicePara',
				type: 'string'
			},
			{
				name: 'platForm',
				type: 'string'
			},
			{
				name: 'execUser',
				type: 'string'
			},
			{
				name: 'status',
				type: 'int'
			},
			{
				name: 'content',
				type: 'string'
			},
			{
				name: 'version',
				type: 'string'
			},
			{
				name: 'bussId',
				type: 'int'
			},
			{
				name: 'scriptLevel',
				type: 'int'
			},
			{
				name: 'bussTypeId',
				type: 'int'
			},
			{
				name: 'fromTable',
				type: 'int'
			},
			{
				name: 'isCollected',
				type: 'int'
			},{
				name: 'isShare',
				type: 'int'
			},{name : 'useTimes' ,type : 'string'},
			{name : 'winTimes' ,type : 'string'},
			{
				name: 'isFlow',
				type: 'string'
			},
			{
				name: 'createUserName',
				type: 'string'
			},{name : 'isEMscript' ,type : 'string'},
			{name : 'iappSysIds' ,type : 'string'},
			{name : 'ifuncdesc' ,type : 'string'}
		]
	});
	let scriptservice_columns = [{
		text: '序号',
		xtype: 'rownumberer',
		width: 40
	},
		{
			text: '主键',
			dataIndex: 'iid',
			hidden: true
		},
		{
			text: '服务名称',
			dataIndex: 'serviceName',
			width: 160
		},
		{
			text: '脚本名称',
			dataIndex: 'scriptName',
			hidden : true,
			renderer : function(value, metadata,record) {
				let a = record.data.ifuncdesc;
				metadata.tdAttr = 'data-qtip="' + a + '"';
				return value;
			},
			width: 150,
			flex:1
		},{
			text: '启动用户',
			dataIndex: 'execUser',
			hidden: true,
			width: 100
		},
		{
			text: '适用平台',
			dataIndex: 'platForm',
			hidden : true,
			width: 90
		},
		{
			text: '一级分类',
			dataIndex: 'sysName',
			hidden : true,
			width: 90
		},
		{
			text: '二级分类',
			dataIndex: 'bussName',
			hidden : true,
			width: 90
		},
		{
			text: '脚本类型',
			dataIndex: 'scriptType',
			hidden : true,
			width: 100,
			//hidden: true,
			renderer: function(value, p, record) {
				let backValue = "";
				if (value == "sh") {
					backValue = "shell";
				} else if (value == "perl") {
					backValue = "perl";
				} else if (value == "py") {
					backValue = "python";
				} else if (value == "bat") {
					backValue = "bat";
				} else if (value == "sql") {
					backValue = "sql";
				} else if (value == "ps1") {
					backValue = "powershell";
				}
				if (record.get('isFlow') == '1') {
					backValue = "组合";
				}
				return backValue;
			}
		},
		{
			text : '成功率',
			width : 60,
			hidden : true,
			renderer : function (value, metaData, record, rowNum)
			{
				let displayValue ;
				displayValue = "<button  style=\"text-align:center; buttonAlign: 'center';margin-left:3px;\" class=\"dbsourBtn\" type=\"button\">查看</button>";
				return displayValue;
			},
			listeners : {
				click : function(a, b, c, d, e, record) {
					let iid = record.get('iid');
					showTotalAndTotalrate(iid);
				}
			}
		},
		{ text: '创建人', hidden : true,dataIndex: 'createUserName',width:100},
		{
			text: '版本',
			dataIndex: 'version',
			// hidden : true,
			width: 50
		},{
			text : '是否应急',
			dataIndex : 'isEMscript',
			hidden : true,
			width : 75,
			renderer:function(value,p,record,rowIndex){
				if(value==0) {
					return '否';
				} else if (value==1) {
					return '<font color="#F01024">是</font>';
				} else {
					return '未知';
				}
			}
		},
		{
			text : '所属系统',
			dataIndex : 'iappSysIds',
			hidden : true,
			width : 50
		},
		{
			text: '风险级别',
			dataIndex: 'scriptLevel',
			width: 75,
			hidden : true,
			renderer: function(value, p, record) {
				let backValue = "";
				if (value == 1) {
					backValue = '<font color="#F01024">高级风险</font>';
				} else if (value == 2) {
					backValue = '<font color="#FF7824">中级风险</font>';
				} else if (value == 3) {
					backValue = '<font color="#FFA826">低级风险</font>';
				}else if (value == 0) {
					backValue = '<font color="#FFA826">白名单</font>';
				}
				return backValue;
			}
		},
		{
			text: '来源',
			dataIndex: 'fromTable',
			width: 80,
			hidden:true,
			renderer: function(value, p, record) {
				let backValue = "";
				if (value == 1) {
					backValue = '我的作业';
				} else if (value == 2) {
					backValue = '共享库';
				}
				return backValue;
			}
		},
		{
			text : '共享状态',
			dataIndex : 'isShare',
			hidden : true,
			width : 80,
			renderer:function(value,p,record,rowIndex){
				if(value==0) {
					return '<font color="">未共享</font>';
				} else if (value==1) {
					return '<font color="#0CBF47">已共享</font>';
				} else {
					return '<font color="#CCCCCC">未知</font>';
				}
			}
		},
		{
			text: 'uuid',
			dataIndex: 'scriptuuid',
			hidden: true,
			width: 90
		},
		{
			text : '操作',
			xtype : 'actiontextcolumn',
			flex: 1,
			// align : 'center',
			items : [ {
				text : '查看',
				handler : function(grid, rowIndex) {
					let iid = grid.getStore().data.items[rowIndex].data.iid;
					let isflow = grid.getStore().data.items[rowIndex].data.isFlow;
					let serviceName =grid.getStore().data.items[rowIndex].data.serviceName;
					let bussId = grid.getStore().data.items[rowIndex].data.bussId;
					let bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
					let scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
					if (isflow == '1') {
						viewDetailForTaskIndexForFlow(iid, serviceName, bussId, bussTypeId,scriptLevel,isflow,menuId);
					}else{
						if(noScriptConvertSwitch){
							viewDetailForTaskIndex(iid);
						}else{
							viewDetailForTaskIndexForFlow(iid, serviceName, bussId, bussTypeId,scriptLevel,isflow,menuId);
						}
					}
				}
			},{
				text : '历史',
				handler : function(grid, rowIndex) {
					let iid = grid.getStore().data.items[rowIndex].data.iid;
					let scriptuuida = grid.getStore().data.items[rowIndex].data.scriptuuid;
					forwardTaskMonitor(iid,scriptuuida,'scriptTaskApplySs');
				}
			} ]
		}

	];


	let ssScriptGrid_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 30,
		remoteSort: true,
		model: 'scriptService',
		proxy: {
			type: 'ajax',
			url: 'getScriptServiceListForTaskManage.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});
	let ssScriptGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region: 'center',
		store:ssScriptGrid_store,
		border:false,
		columnLines : true,
		columns:scriptservice_columns,
		idisplayInfo :false,
		ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true,mode : "SINGLE"}),
		listeners: {
			select: function( e, record, index, eOpts ){
				scriptuuid = record.get('scriptuuid');
				iidForTaskAudi = record.get('iid');
				execStartUser = record.get('execUser');
				submitTaskName = record.get('serviceName')+"——"+record.get('scriptName');
				serviceName = record.get('serviceName');
				serviceNameForTaskAudi = record.get('serviceName');
				taskNameValue = serviceName+'_'+CurentTime();
				// taskName.setValue(serviceName+'_'+CurentTime());
				// scriptLevelForTaskAudi = record.get('scriptLevel');
				scriptTypeForTaskAudi = record.get('scriptType');

				sysAgent.clear();
				choosedAgentRecordsMap.clear();
				cpdsMap = {};
				selCpId = -1;
				finalChosedAgentsAndDbSources = {};
				sysAgentFlagMap.clear();
				sysidGlobal = '';
				protypeGlobal = '';
				choosedAgentIds=[];

				//加载参数
				paramStore.load();
				//加载附件
				attachmentIds=[];
				attachmentStore.load();
				//加载业务系统
				sysListGrid_store.load();
				//加载agent
				agentGrid_store.load();
			}
		}
	});

	ssScriptGrid_store.on('beforeload',

		function(store, options) {

			let new_params = {
				serviceName: sName.getValue(),
				bussId: bussCb.getValue() || 0,
				bussTypeId: bussTypeCb.getValue() || 0,
				fromTable: -1,
				isEMscript:-1,
				status: 1,
				onlyScript: 1,
				fromType: 99,
				level:2//白名单脚本
			};

			Ext.apply(ssScriptGrid_store.proxy.extraParams, new_params);
		});

	Ext.define('paramModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iid',
			type: 'int'
		},
			{
				name: 'paramType',
				type: 'string'
			},
			{
				name: 'paramDefaultValue',
				type: 'string'
			},{
				name: 'paramValue',
				type: 'string'
			},
			{
				name: 'paramDesc',
				type: 'string'
			},
			{
				name: 'paramOrder',
				type: 'int'
			}]
	});

	let paramStore = Ext.create('Ext.data.Store', {
		autoLoad: false,
		autoDestroy: true,
		pageSize: 30,
		model: 'paramModel',
		proxy: {
			type: 'ajax',
			url: 'getAllScriptParams.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	paramStore.on('beforeload', function(store, options) {
		let new_params = {
			scriptId: scriptuuid
		};

		Ext.apply(paramStore.proxy.extraParams, new_params);
	});
	let defultEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true
		})
	});
	let passwordEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true,
			inputType : 'password'
		})
	});

	let paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
		{
			text: '主键',
			dataIndex: 'iid',
			width: 40,
			hidden: true
		},{
			text: '顺序',
			dataIndex: 'paramOrder',
			width: 50,
			renderer:function (value, metaData, record, rowIdx, colIdx, store){
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
				return value;
			}
		},
		{
			text: '类型',
			dataIndex: 'paramType',
			width: 60,
			renderer:function (value, metaData, record, rowIdx, colIdx, store){
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
				return value;
			}
		},
		{
			xtype : 'gridcolumn',
			dataIndex : 'paramDefaultValue',
			width: 80,
			text : '参数默认值',
			getEditor : function(record) {
				if (record.get('paramType') != 'IN-string(加密)' ) {
					return defultEditor;
				} else {
					return passwordEditor;
				}
			},
			renderer : function(value, metaData, record, rowIdx, colIdx, store){
				let backValue = "";
				if(record.get('paramType')== 'IN-string(加密)'){
					backValue = StringToPassword(value);
				}else{
					backValue = value;
				}
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';

				return backValue;
			}
		},
		{
			xtype : 'gridcolumn',
			dataIndex : 'paramValue',
			width: 80,
			text : '参数值',
			maxLength : 1000,
			allowBlank: true,
			getEditor : function(record) {
				if (record.get('paramType') != 'IN-string(加密)' ) {
					return defultEditor;
				} else {
					return passwordEditor;
				}
			},
			renderer : function(value, metaData, record, rowIdx, colIdx, store){
				let backValue = "";
				if(record.get('paramType')== 'IN-string(加密)'){
					backValue = StringToPassword(value);
				}else{
					backValue = value;
				}
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';

				return backValue;
			}
		},
		{
			text: '描述',
			dataIndex: 'paramDesc',
			flex: 1,
			renderer:function (value, metaData, record, rowIdx, colIdx, store){
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
				return value;
			}
		}
	];
	let cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	let paramGrid = Ext.create('Ext.grid.Panel', {
		title: "参数",
		//width: '50%',
		region: 'north',
		height:'50%',
		cls:'window_border panel_space_top  script_right',
//        height: 280,
//        margin: 10,
//        collapsible : true,
		store: paramStore,
		plugins: [cellEditing],
		// margin:'0 10 5 10',
		border: true,
		columnLines: true,
		columns: paramColumns
	});
	let attachmentColumns = [
		{
			text: '主键',
			dataIndex: 'iid',
			width: 40,
			hidden: true
		},
		{
			text: '附件名称',
			dataIndex: 'attachmentName',
			flex: 1,
			renderer:function (value, metaData, record, rowIdx, colIdx, store){
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
				return value;
			}
		},

		{
			menuDisabled: true,
			sortable: false,
			xtype: 'actioncolumn',
			width: 50,
			items: [{
				iconCls: 'attachment_delete',
				tooltip: '删除',
				handler: function(grid, rowIndex, colIndex) {
					let rec = attachmentStore.getAt(rowIndex);
					Ext.Msg.confirm("请确认", "是否真的要删除附件？", function(button, text) {
						if (button == "yes") {
							let a = [];
							a.push(rec.get('iid'));
							Ext.Ajax.request({
								url: 'deleteScriptAttachment.do',
								method: 'POST',
								sync: true,
								params: {
									iids: a
								},
								success: function(response, request) {
									let success = Ext.decode(response.responseText).success;
									if (success) {
										Ext.Msg.alert('提示', '删除成功！');
										removeByValue(attachmentIds, rec.get('iid'));
										attachmentStore.load();
									} else {
										Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
									}
								},
								failure: function(result, request) {
									secureFilterRs(result, "保存失败！");
								}
							});
						}
					});
				}
			},
				{
					iconCls: 'script_download',
					tooltip: '下载',
					handler: function(grid, rowIndex, colIndex) {
						let rec = attachmentStore.getAt(rowIndex);
						//window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
						window.location.href = 'downloadScriptAttachment.do?iid='+rec.get('iid');
					}
				}
			]
		}];

	let attachmentUploadWin = null;
	function selectAttachmentFun(){
		if(scriptuuid == undefined || scriptuuid == ''){
			Ext.Msg.alert('提示', '请先选择服务！');
			return ;
		}


		let uploadForm;
		uploadForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
				id : 'attachment_idauditing_new',
				fieldLabel: '选择文件',
				labelWidth: 65,
				anchor: '90%',
				margin: '10 10 0 40',
				buttonText: '浏览',
				multipleFn: function($this){

					let typeArray = ["application/x-shockwave-flash","text/plain","audio/MP3","image/*","flv-application/octet-stream"];

					let fileDom = $this.getEl().down('input[type=file]');

					fileDom.dom.setAttribute("multiple","multiple");

					fileDom.dom.setAttribute("accept",typeArray.join(","));

				},
				listeners:{
					afterrender: function(){
						this.multipleFn(this);
					},
					change: function(){
						let fileDom = this.getEl().down('input[type=file]');
						let files = fileDom.dom.files;
						let str = '';
						for(let i = 0;  i < files.length;  i++){
							str += files[i].name;
							str += ' ';
						}
						Ext.getCmp('attachment_idauditing_new').setRawValue(str);    //files为组件的id
						this.multipleFn(this);
					}
				}
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData
			},{
				text : '取消',
				handler : function(){
					this.up("window").close();
				}
			}]
		});

		attachmentUploadWin = Ext.create('Ext.window.Window', {
			title : '附件信息',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 600,
			height: 200,
			items : [ uploadForm ],
			listeners : {
				close : function(g, opt) {
					uploadForm.destroy();
				}
			},
			/*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout : 'fit'
		});

		function upExeclData(){
			let form = uploadForm.getForm();
			let hdupfile=form.findField("files").getValue();
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate(form);
		}

		/** 自定义遮罩效果* */
		let myUploadMask = new Ext.LoadMask (contentPanel,
			{
				msg : "附件上传中..."
			});
		function uploadTemplate(form) {
			if (form.isValid()) {
				form.submit({
					url: 'uploadScriptAttachmentFile.do',
					params:{
						scriptUuid: scriptuuid,
						attachmentIds:Ext.encode(attachmentIds)
					},
					success: function(form, action) {
						let success=Ext.decode(action.response.responseText).success;
						let msg = Ext.decode(action.response.responseText).message;
						if(success){
							let ids = Ext.decode(action.response.responseText).ids;
							attachmentIds.push.apply(attachmentIds,ids.split(","));
						}else{
							Ext.Msg.alert('提示',msg);
						}
						attachmentUploadWin.close();
						myUploadMask.hide();
						attachmentStore.load();
					},
					failure: function(form, action) {
						let msg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',msg);
						myUploadMask.hide();
					}
				});
			}
		}
		attachmentUploadWin.show();
	}
	let selectedAttachmentButton = Ext.create ("Ext.Button",
		{
			cls : 'Common_Btn',
			disabled : false,
			text : '添加附件',
			handler : selectAttachmentFun
		});


	Ext.define('attachmentModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iid',
			type: 'int'
		},
			{
				name: 'attachmentName',
				type: 'string'
			},
			{
				name: 'attachmentSize',
				type: 'string'
			},
			{
				name: 'attachmentUploadTime',
				type: 'string'
			}]
	});

	let attachmentStore = Ext.create('Ext.data.Store', {
		autoLoad: false,
		autoDestroy: true,
		pageSize: 30,
		model: 'attachmentModel',
		proxy: {
			type: 'ajax',
			url: 'getAllScriptAttachment.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	attachmentStore.on('beforeload', function(store, options) {
		let new_params = {
			scriptId: scriptuuid,
			ids: attachmentIds
		};

		Ext.apply(attachmentStore.proxy.extraParams, new_params);
	});

	attachmentStore.on('load', function(me, records, successful, eOpts) {
		attachmentIds = []
		$.each(records, function(index, record){
			attachmentIds.push(record.get('iid'));
		});
	});
	let attachmentGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		//cls: 'attachments customize_panel_back',
		//height: '40%',
//        height: contentPanel.getHeight()*0.25-80,
// 		title: '附件',
		store: attachmentStore,
		// cls:'window_border panel_space_top panel_space_left panel_space_right attachments customize_panel_back',
		// cls:'window_border panel_space_top  panel_space_right attachments panel_space_bottom',
		border: true,
		columnLines: true,
		autoScroll: true,
		// margin:'0 10 5 10',
//        emptyText: '没有附件',
		viewConfig: {
			emptyText: '没有附件',
			deferEmptyText:false
		},

		columns: attachmentColumns,
		dockedItems : [{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items:[
				'->',selectedAttachmentButton//添加附件按钮
			]
		}]
	});


	let ssLeftPanel = Ext.create('Ext.panel.Panel', {
		region: 'west',
		collapsible : false,
		border: false,
		// cls:'window_border panel_space_top panel_space_left panel_space_right panel_space_bottom',
		cls:'window_border panel_space_top script_left script_right panel_space_bottom',
		// height: contentPanel.getHeight()-40,
		width: contentPanel.getWidth()*0.28,
		layout: 'border',
		items: [ssFormQuery,ssScriptGrid ]
	});
	Ext.define('dbModel', {
		extend: 'Ext.data.Model',
		idProperty: 'iid',
		fields: [
			{name: 'iid',        type: 'string'},
			{name: 'driverClass',type: 'string'},
			{name: 'dbUrl',      type: 'string'},
			{name: 'dbUser',     type: 'string'},
			{name: 'dbType',     type: 'string'}
		]
	});
	// 定义复选框
	let DbSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		mode : "SINGLE",
		listeners : {
//			selectionchange : function(selModel, selections) {
//			},
//			beforedeselect:function(DbSelModel, record, index, eOpts) {
//				let len = DbSelModel.getSelection();
//				if(len==1)
//				{
//					Ext.MessageBox.alert("提示", "不能没有数据源信息!");
//					return false;
//				}
//			},
			select:function(DbSelModel, record, index, eOpts) {
				let dsid = record.get("iid");
				if(selCpId != -1)
				{
					cpdsMap[selCpId] = dsid;//绑定
				}
			},
			deselect:function(DbSelModel, record, index, eOpts) {
				cpdsMap[selCpId] = -1;//清空
			}
		}
	});
	let dbinfo_store = Ext.create('Ext.data.Store', {
		model:'dbModel',
		autoLoad: false,
		proxy: {
			type: 'ajax',
			url: 'getDbSqlDriverInfo.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	let dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
		{ text: '主键',  dataIndex: 'iid',hidden:true},
		{ text: '驱动类',  dataIndex: 'driverClass',width:120,renderer:function (value, metaData, record, rowIdx, colIdx, store){
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
				return value;
			}},
		{ text: 'DBURL',  dataIndex: 'dbUrl',flex:1,width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){
				metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
				return value;
			}},
		{ text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
		{ text: 'DB类型',  dataIndex: 'dbType',width:90}];
	let dbsource_grid = Ext.create('Ext.grid.Panel', {
		store:dbinfo_store,
//    	 region: 'center',
		// width: '40%',
		//width: '50%',
		border:true,
		//height:300,
		columnLines : true,
		//cls:'window_border panel_space_top panel_space_right',
		columns:dbsource_columns,
//    	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})
		selModel:DbSelModel
	});

	let dbAndParamTab = Ext.create ('Ext.tab.Panel',
		{
			tabPosition : 'top',
			//cls:'customize_panel_back',
			cls:'window_border panel_space_top script_right panel_space_bottom',
			region: 'center',
			activeTab : 0,
			//width : '100%',
			//height : contentPanel.getHeight()*0.25,
			border : false,
//			    autoScroll: true,
			items : [
				{
					title : '附件',
					// hidden:!taskApplyUploadAttachmentSwitch,
					layout:'fit',
					items:[attachmentGrid]
				},
				{
					title : '驱动',
					layout:'fit',
					items:[dbsource_grid]
				}
			]
		});
	let ssCenterPanel = Ext.create('Ext.panel.Panel', {
		collapsible : false,
		border: false,
		region: 'center',
		// height: contentPanel.getHeight()-40,
		layout: 'border',
		items: [paramGrid,dbAndParamTab/*attachmentGrid*/ ]
	});

	Ext.define('sysListGridModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'IID',
			type: 'string'
		},
			{
				name: 'INAME',
				type: 'string'
			},{
				name: 'PROTYPE',
				type: 'string'
			}
		]
	});
	let sysListGrid_store = Ext.create('Ext.data.Store', {
		autoLoad: false,
		autoDestroy: true,
		pageSize: 30,
		model: 'sysListGridModel',
		proxy: {
			type: 'ajax',
			url: 'getScriptBindSysList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	sysListGrid_store.on('beforeload',
		function(store, options) {
			let new_params = {
				scriptUuid: scriptuuid,
			};
			Ext.apply(sysListGrid_store.proxy.extraParams, new_params);
		});

	sysListGrid_store.on('load', function (store, options) {
		let records=[];//存放选中记录
		for(let i=0;i<sysListGrid_store.getCount();i++){
			let record = sysListGrid_store.getAt(i);
			//遍历 sysAgent key为业务系统iid
			for (const sysIidKey of sysAgent.keys()) {
				if(sysIidKey==record.data.IID)
				{
					sysidGlobal = sysIidKey;
					records.push(record);
				}
			}
		}
		if(records.length==0){
			sysidGlobal='';
		}
		agentGrid.ipage.moveFirst();
		// agentGrid_store.load();

		sysListGrid.getSelectionModel().select(records, false, true);//选中记录
	});

	let sysListGrid_columns = [{
		text: '序号',
		xtype: 'rownumberer',
		width: 40
	},
		{
			text: '主键',
			dataIndex: 'IID',
			hidden: true
		},
		{
			text: '业务系统名称',
			dataIndex: 'INAME',
			flex:1
		},{
			text: '业务系统类型',
			dataIndex: 'PROTYPE',
			hidden:true
		}
	];

	function getScriptSysAgentListIds(sysidArray ){
		let sysAgentIds={};
		Ext.Ajax.request({
			url : 'getScriptSysBindAgentListIds.do',
			params : {
				sysIids:sysidArray,
				scriptuuid:scriptuuid
			},
			async:false,
			method : 'post',
			success : function(response, text) {
				sysAgentIds =  Ext.decode(response.responseText);
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
		return sysAgentIds;
	}


	//业务系统
	let sysListGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		title: "业务系统",
		region: 'west',
		store:sysListGrid_store,
		cls:'window_border panel_space_top  script_right panel_space_bottom',
		border:false,
		columnLines : true,
		width:'50%',
		columns:sysListGrid_columns,
		ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		listeners: {
			select: function( e, record, index, eOpts ){
				sysidGlobal = record.get('IID');
				//业务系统类型 查询agent使用
				protypeGlobal = record.get('PROTYPE');
				sysAgent.set(sysidGlobal,[]);
				sysAgentFlagMap.set(sysidGlobal,"未选择");
				agentGrid.ipage.moveFirst();
				// agentGrid_store.load();
			},
			/*itemClick  : function(view, record, item, index, e, eOpts) {
				let iid = record.get('IID');
				alert(iid);
			},*/
			deselect: function( e, record, index, eOpts ){
				sysidGlobal = record.get('IID');
				//业务系统类型 查询agent使用
				protypeGlobal = record.get('PROTYPE');
				if(sysAgent.get(sysidGlobal).length>0){
					Ext.Array.each(sysAgent.get(sysidGlobal),function (agentid) {
						choosedAgentIds.remove(agentid);
					});
				}
				sysAgent.delete(sysidGlobal);
				sysAgentFlagMap.delete(sysidGlobal);
				sysidGlobal='';
				agentGrid.ipage.moveFirst();
				// agentGrid_store.removeAll();
				// sysAgent.remove(tempObject);
			}
		}
	});

	Ext.define('agentGrid', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iid',
			type: 'string'
		},
			{
				name: 'agentName',
				type: 'string'
			},{
				name: 'ip',
				type: 'string'
			}
		]
	});
	Ext.define('agentGridModel', {
		extend: 'Ext.data.Model',
		idProperty: 'IID',
		fields: [
			{name: 'AGENTIID',     type: 'string'},
			{name: 'AGENTIP',     type: 'string'},
			{name: 'AGENTPORT',     type: 'string'},
			{name: 'AGENTNAME',     type: 'string'},
			{name: 'ISYSTEMID',     type: 'string'},
		]
	});
	let agentGrid_store = Ext.create('Ext.data.Store', {
		autoLoad: false,
		autoDestroy: true,
		pageSize: 30,
		model: 'agentGridModel',
		proxy: {
			type: 'ajax',
			url: 'getScriptSysAgentList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	agentGrid_store.on('beforeload',
		function(store, options) {
			let new_params = {
				sysid: sysidGlobal,
				scriptuuid :scriptuuid,
				protype:protypeGlobal
			};

			Ext.apply(agentGrid_store.proxy.extraParams, new_params);
		});


	agentGrid_store.on('load', function (store, options) {
		let records=[];//存放选中记录
		for(let i=0;i<agentGrid_store.getCount();i++){
			let record = agentGrid_store.getAt(i);
			//遍历 sysAgent key为业务系统iid
			if(sysAgent.size>0){
				sysAgent.get(sysidGlobal).forEach(value=>{
					if(value==record.data.AGENTIID)
					{
						records.push(record);
					}
				});
			}

		}
		agentGrid.getSelectionModel().select(records, false, true);//选中记录
		dbinfo_store.removeAll();
	});


	let agentGrid_columns = [{
		text: '序号',
		xtype: 'rownumberer',
		width: 40
	},
		{
			text: '主键',
			dataIndex: 'AGENTIID',
			hidden: true
		},
		{
			text: 'ISYSTEMID',
			dataIndex: 'ISYSTEMID',
			hidden: true
		},
		{
			text: 'agent名称',
			dataIndex: 'AGENTNAME',
			flex:1
		},{
			text: 'IP',
			dataIndex: 'AGENTIP',
			width:110
		},
		{
			text: '端口',
			dataIndex: 'AGENTPORT',
			hidden: true,
			width:90
		}
	];
	//Agent
	let agentGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		title: "Agent",
		region: 'center',
		store:agentGrid_store,
		cls:'window_border panel_space_top  script_right panel_space_bottom',
		border:false,
		columnLines : true,
		columns:agentGrid_columns,
		ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		listeners: {
			select: function( e, record, index, eOpts ){
				let agentSystemid = record.get('ISYSTEMID');
				let agentiid = record.get('AGENTIID');
				let agentIp = record.get('AGENTIP');
				let agentPort = record.get('AGENTPORT');
				let agentIdArray = sysAgent.get(agentSystemid);
				if(sysAgentFlagMap.get(agentSystemid)==="未选择"){
					agentIdArray=[];
					sysAgentFlagMap.set(agentSystemid,"有选择");
					sysAgent.set(agentSystemid,[]);
				}
				if(agentIdArray.indexOf(agentiid)<0){
					agentIdArray.push(agentiid);
					sysAgent.set(agentSystemid,agentIdArray);
				}
				//sql类型store加载
				dbinfo_store.load({
					params: {
						agentId: agentiid,
						agentIp: agentIp,
						agentPort: agentPort
					}
				});
				//当前选中cpid
				selCpId = agentiid;
				choosedAgentRecordsMap.set(agentiid,record);
			},
			deselect: function( e, record, index, eOpts ){
				let agentSystemid = record.get('ISYSTEMID');
				let agentiid = record.get('AGENTIID');
				let agentIdArray = sysAgent.get(agentSystemid);
				if(agentIdArray.indexOf(agentiid)>=0){
					agentIdArray.remove(agentiid);
					choosedAgentIds.remove(agentiid);
				}
				if(agentIdArray.length===0){
					sysAgentFlagMap.set(agentSystemid,"未选择");
				}
				//sql类型store移除
				dbinfo_store.removeAll();
				cpdsMap[agentiid] = -1;//清空
//				 delete cpdsMap[cpid];
				selCpId = -1;
				choosedAgentRecordsMap.delete(agentiid);
			}
		}
	});



	let ssRightPanel = Ext.create('Ext.panel.Panel', {
		collapsible : false,
		border: false,
		region: 'east',
		// height: contentPanel.getHeight()-40,
		width: contentPanel.getWidth()*0.46,
		layout: 'border',
		items: [sysListGrid, agentGrid]
	});

	let outTopPanel = Ext.create('Ext.panel.Panel', {
		collapsible : false,
		border: false,
		flex :10,
		// region: 'center',
		// height: contentPanel.getHeight()-40,
		// width: contentPanel.getWidth()*0.4,
		layout: 'border',
		items: [ssLeftPanel,ssCenterPanel,ssRightPanel]
	});



	function CurentTime()
	{
		let now = new Date();

		let year = now.getFullYear();       //年
		let month = now.getMonth() + 1;     //月
		let day = now.getDate();            //日

		let hh = now.getHours();            //时
		let mm = now.getMinutes();          //分
		let ss = now.getSeconds();           //秒

		let clock = year;

		if(month < 10){

			clock += "0";
		}else{
			clock += "";
		}

		clock +=month;

		if(day < 10){
			clock += "0";
		}else{
			clock += "";
		}


		clock += day;

		if(hh < 10){
			clock += "0";
		}else{
			clock += "";
		}


		clock += hh;
		if (mm < 10)
		{
			clock += '0';
		}
		else{
			clock += "";
		}
		clock += mm ;

		if (ss < 10) {
			clock += '0';
		}
		else{
			clock += "";
		}
		clock += ss;
		return(clock);
	}








	function selectExecCron()
	{
		if (creatCronWin == undefined || !creatCronWin.isVisible()) {
			creatCronWin = Ext.create('Ext.window.Window', {
				title : '定时任务参数设置',
				modal : true,
				id : 'creatCronWin',
				closeAction : 'destroy',
				constrain : true,
				autoScroll : true,
				upperWin : creatCronWin,
				width : contentPanel.getWidth() - 350,
				height : contentPanel.getHeight() - 30,
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				layout : 'fit',
				loader : {
					url : 'cronMainForSpdb.do',
					//					params : {
					//						sysType : sysType,
					//						state : state,
					//						errorTaskId : errorTaskId,
					//						pageType : pageType
					//					},
					autoLoad : true,
					autoDestroy : true,
					scripts : true
				}
			});
		}
		creatCronWin.show();
	}








	function isPoneAvailable(pone){
		let myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
		if (!myreg.test(pone)) {
			return false;
		} else {
			return true;
		}
	}








	function setMessage(msg) {
		Ext.Msg.alert('提示', msg);
	}

	let toexec = "<a href='javascript:void(0)' style='color:#e93030; font-weight: bold; text-decoration:none;' onclick=forwardToScriptExecTask()>您有"+toExecCount+"个待处理的任务</a>";

	/**
	 * 遍历SysAgent Map对象 构建choosedAgentIds数组，分为两步操作
	 * 1.获取未选择agent的sysid数组，后台查询这些业务系统的所有agent，sysAgent中未选择agent的业务系统的agent数组重新赋值，再把返回的所有agent数组再插入choosedAgentIds数组
	 * 2.获取选择agent的业务系统对应的agent数组，合并到choosedAgentIds数组
	 * @returns {boolean}
	 */
	function foreachSysAgentMap() {
		//未选择设备的业务系统sysid数组
		let unChooseAgentSysidArray=[];
		//第一次遍历 获取 业务系统对应agent数组为空的，构建出新的临时业务系统id数组，为了后天只查询一次
		for (let [systemid, agentIidArray] of sysAgent) {
			//agentiid数组为空的代表未选择设备，构建这种sysid的数组，为了后台查询所有agent使用
			if (agentIidArray.length === 0) {
				unChooseAgentSysidArray.push(systemid);
			} else {
				//1.追加到choosedAgentIds
				choosedAgentIds.push(...agentIidArray);
			}
		}
		/*//构建出个未选择agent的新map,下面使用
		let unChooseAgentSysArray = [...sysAgent].filter(function(item) {
			  return item[1].length===0;
		});
		let unChooseAgentSysMap = new Map(unChooseAgentSysArray);
		*/
		// let scriptSysAgentListIds = [];
		if(unChooseAgentSysidArray.length>0){
			//2.获取未选择agent的业务系统对应的所有对象  key是systemid，value是agent数组，sysAgent中未选择agent的业务系统的agent数组重新赋值， 并把agent数组合并到 choosedAgentIds中
			let tempScriptSysAgentListObject = getScriptSysAgentListIds(unChooseAgentSysidArray);
			if(Object.keys(tempScriptSysAgentListObject).length===0){
				return false;
			}
			unChooseAgentSysidArray.forEach(unChooseAgentSysid => {
				sysAgent.set(unChooseAgentSysid,tempScriptSysAgentListObject[unChooseAgentSysid]);
				choosedAgentIds.push(...tempScriptSysAgentListObject[unChooseAgentSysid]);
			});

		}

		// choosedAgentIds.push(...scriptSysAgentListIds);

		return true;
	}

	let outBottomPanel = Ext.create('Ext.panel.Panel', {
		collapsible : false,
		border: false,
		flex:1,
		// region: '',
		// height: contentPanel.getHeight()-40,
		// width: contentPanel.getWidth()*0.4,
		layout: 'fit',
		dockedItems: [{xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : ['->',toexec,{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '提交',
				handler : function(){

					let countdown=10;
					function settime(){
						if (countdown == 0) {
							getValidCodeButton.setDisabled(false);
							countdown = 10;
							return false;
						} else {
							getValidCodeButton.setDisabled(true);
							countdown--;
						}
						//显示验证码框
						setTimeout(function() {
							settime();
						},1000);
					}
					let taskName = new Ext.form.TextField({
						name: 'taskName',
						fieldLabel: '任务名称',
						emptyText: '',
						labelWidth : 65,
						labelAlign : 'right',
						width: "24.1%",
						value:taskNameValue
					});
					function getValidCode(){
						// let phonenum = Ext.getCmp('myvalidCode').getValue();
						let phonenum = validCode.getValue();
						if(!isPoneAvailable(phonenum)){
							Ext.MessageBox.alert("提示", "请填写正确的手机号！");
							return;
						}
						let auditor  =auditorComBox_tap.getValue();
						if(!auditor) {
							Ext.Msg.alert('提示', "请选择审核人！");
							return;
						}



						let taskN = Ext.util.Format.trim(taskName.getValue());
						settime();
						//显示输入验证码框
						//Ext.getCmp('mauthCode').show();
						//验证码入库保存，组织验证码数据发送给短信接口，发送成功
						Ext.Ajax.request({
							url : 'sendAuthCode.do',
							params : {
								telephoneNum :phonenum ,
								iauditUser :auditor,
								stepId:'-1',
								planId:'-1',
								iscenceId:'-1',
								serviceId: iidForTaskAudi,
								isscript:'0',
								proType:'10',
								planName:serviceNameForTaskAudi,
								systemId:'-1',
								menuName: '任务申请',
								operationType: '提交双人复核',
								taskName:taskN
							},
							method : 'post',
							success : function(response, text) {
								let success = Ext.decode(response.responseText).success;
								let message = Ext.decode(response.responseText).message;
								if (success) {
									Ext.Msg.show({
										title : '提示',
										msg : " 验证码已发送至手机！ " ,
										buttons : Ext.MessageBox.OK
									});
								} else {
									Ext.Msg.show({
										title : '提示',
										msg : message,
										buttons : Ext.MessageBox.OK
									});
								}
							},
							failure : function(result, request) {
								secureFilterRs(result, "操作失败！");
							}
						});
					}

					Ext.define('AuditorModel', {
						extend: 'Ext.data.Model',
						fields : [ {
							name : 'loginName',
							type : 'string'
						}, {
							name : 'fullName',
							type : 'string'
						}, {
							name : 'phoneNum',
							type : 'string'
						}]
					});

					let auditorStore_tap = Ext.create('Ext.data.Store', {
						autoLoad: true,
						model: 'AuditorModel',
						proxy: {
							type: 'ajax',
							url: 'getExecAuditorList.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					auditorStore_tap.on('beforeload',
						function(store, options) {
							let new_params = {
								scriptLevel: scriptLevelForTaskAudi
							};

							Ext.apply(auditorStore_tap.proxy.extraParams, new_params);
						});

					auditorStore_tap.on("load", function(s, r, o) {
						auditorComBox_tap.setValue(r[0].get('loginName'));//第一个值
						let phoneNum = r[0].get('phoneNum');
						validCode.setValue(phoneNum);
					});

					let groupLeader ;
					Ext.Ajax.request({
						url: 'getGroupLeader.do',
						method: 'POST',
						async: false,
						success: function(response, options) {
							groupLeader = Ext.decode(response.responseText).leader;
						},
						failure: function(result, request) {
						}
					});




					let execStore = Ext.create('Ext.data.Store', {
						autoLoad: false,
						model: 'AuditorModel',
						proxy: {
							type: 'ajax',
							url: 'getExecUserList.do',
							reader: {
								type: 'json',
								root: 'dataList'
							}
						}
					});

					execStore.load({
						callback : function (records, operation, success)
						{
							execComBox_tap.setValue (loginUser);
						}
					});

					let immediatelyExecRadio = Ext.create('Ext.form.field.Radio', {
						width: 80,
						name:'execRadio',
						labelAlign : 'left',
						fieldLabel: '',
						boxLabel: '触发执行',
						checked: true,
						inputValue : 0
					});
					let timingExecRadio = Ext.create('Ext.form.field.Radio', {
						width: 80,
						name:'execRadio',
						labelAlign : 'left',
						fieldLabel: '',
						boxLabel: '定时执行',
						inputValue : 2
					});
					let cycleExecRadio = Ext.create('Ext.form.field.Radio', {
						width: 80,
						name:'execRadio',
						labelAlign : 'left',
						fieldLabel: '',
						boxLabel: '周期执行',
						inputValue : 1
					});


					let execComBox_tap = Ext.create('Ext.form.ComboBox', {
						fieldLabel: "执行人",
						store: execStore,
						queryMode: 'local',
						width: "15.3%",
						padding: '0 2 0 8',
						displayField: 'fullName',
						valueField: 'loginName',
						labelWidth : 58,
						editable : true,
						// hidden:!execUserSwitch || scriptLevelForTaskAudi==0?true:false,
						labelAlign : 'right'
					});
					let eachNum = new Ext.form.NumberField({
						name: 'eachNum',
						fieldLabel: '并发数量',
						labelWidth : 65,
						labelAlign : 'right',
						minValue:0,
						value:number,
						width: "12.6%"
					});


					/** 选择生成周期表达式按钮 **/
					let selectCronButton = Ext.create ("Ext.Button",
						{
							// id : 'selectCronButton_id',
							cls : 'Common_Btn',
							text : "选择",
							handler : selectExecCron
						});
					let cycleExecCronText = new Ext.form.TextField (
						{
							fieldLabel : '执行周期',
							labelWidth : 65,
							labelAlign : 'right',
							id : 'cycleExecCronText',
							name: 'cycleExecCronText',
							readOnly : true
						});

					let execTimeComponent = Ext.create('Go.form.field.DateTime',
						{
							fieldLabel: '执行时间:',
							// id : 'execTimeComponent',
							labelAlign: 'left',
							labelWidth : 60,
							width : 250,
							hidden : true,
							format : 'Y-m-d H:i:s'
						});
					let cycleExecCronPanel = Ext.create('Ext.form.Panel', {
						width: 330,
						border: false,
						hidden : true,
						name :'execTimeComponent',
//        layout: {
//	            type: 'hbox',
//	            align : 'stretch'
//	    },
						items: [{
							xtype : 'toolbar',
							border : false,
							dock : 'top',
							items : [ cycleExecCronText,selectCronButton]
						}]
					});


					let execModeGroup =  Ext.create('Ext.form.RadioGroup', {
						name:'cycleModeGroup',
						labelAlign : 'left',
						layout: 'column',
						width : '160',
						items:[immediatelyExecRadio,timingExecRadio,cycleExecRadio],
						listeners:{
							//通过change触发
							change: function(g , newValue , oldValue){
								if(newValue.execRadio == 0)//触发
								{
									execTimeComponent.hide();
									cycleExecCronPanel.hide();
								}
								else if(newValue.execRadio == 2)//定时
								{
									execTimeComponent.show();
									cycleExecCronPanel.hide();
								}
								else if(newValue.execRadio == 1)//周期
								{
									execTimeComponent.hide();
									cycleExecCronPanel.show(	);
								}
							}
						}
					});

					let validCode = Ext.create ('Ext.form.TextField',
						{
							fieldLabel:'手机号',
							labelWidth : 65,
							columnWidth: 150,
							// id : 'myvalidCode',
							labelAlign : 'right',
							readOnly: true,
							hidden :scriptLevelForTaskAudi===0,
							regex: /^[1][3,4,5,7,8][0-9]{9}$/,
							xtype : 'textfield'
						});
					let getValidCodeButton   = Ext.create('Ext.Button',
						{columnWidth: .23,height:33, name : 'getValidCodeButton',cls: 'Common_Btn',hidden : scriptLevelForTaskAudi===0, text : '获取验证码', handler :  getValidCode
						});

					let auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
						editable: true,
						fieldLabel: "审核人",
						store: auditorStore_tap,
						queryMode: 'local',
						width: "23.2%",
						displayField: 'fullName',
						valueField: 'loginName',
						hidden:scriptLevelForTaskAudi===0,
						labelWidth : 65,
						labelAlign : 'right',
						listeners: { //监听
							render : function(combo) {//渲染
								if(scriptLevelForTaskAudi==0){
									combo.setValue(groupLeader);
								}else{
									combo.getStore().on("load", function(s, r, o) {
										combo.setValue(r[0].get('loginName'));//第一个值
										let phoneNum = r[0].get('phoneNum');
										validCode.setValue(phoneNum);
									});
								}
							},
							select : function(combo, records, eOpts){
								let fullName = records[0].raw.fullName;
								combo.setRawValue(fullName);
								let phoneNum =records[0].raw.phoneNum;
								validCode.setValue(phoneNum);
							},
							blur:function(combo, records, eOpts){
//				let displayField =auditorComBox_tap.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					let flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_tap.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    let data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_tap.setValue("");
//					 	return;
//					}
//				}
							},
							beforequery: function(e) {
								let combo = e.combo;
								if (!e.forceAll) {
									let value = Ext.util.Format.trim(e.query);
									combo.store.filterBy(function(record, id) {
										let text = record.get(combo.displayField);
										return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
									});
									combo.expand();
									return false;
								}
							}
						}
					});
					let execUser = new Ext.form.TextField({
						name: 'execUser',
						fieldLabel: '启动用户',
						emptyText: '',
						labelWidth : 65,
						labelAlign : 'right',
						width: "17%"
						/* listeners:{
                                    blur: function(object,state,opt){
                                        if(taskApplyCheckSysAdminSwitch){
                                            let checkSysAdminMes = checkSysAdmin(object.getValue());
                                            if(checkSysAdminMes!=null && checkSysAdminMes !=""){
                                                execUser.setValue('');
                                                Ext.Msg.alert('提示', checkSysAdminMes);
                                                 return;
                                            }
                                        }
                                    }
                         }*/
					});



					let authCode = Ext.create ('Ext.form.TextField',
						{
							fieldLabel:'验证码',
							labelWidth: 65,
							// id : 'mauthCode',
							hidden :scriptLevelForTaskAudi===0,
							labelAlign : 'right',
							width: 200,
							emptyText : '--请填写验证码--',
							regex: /^\d{6}$/,
							xtype : 'textfield'
						});
					let execDesc = Ext.create('Ext.form.field.TextArea', {
						name: 'funcdesc',
						displayField: 'funcdesc',
						emptyText: '',
						columnWidth: 1,
						margin:'0 10 5 10',
						height: contentPanel.getHeight()*0.25-50,
						autoScroll: true
					});
					let execDescForm = Ext.create('Ext.form.Panel', {
						width: '100%',
						height: contentPanel.getHeight()*0.25,
						region: 'south',
						border: false,
						cls:'window_border panel_space_top panel_space_left panel_space_right',
						title: '执行描述',
						items: [{
							layout: 'column',
							border: false,
							items: [execDesc]
						}]
					});

					let topBar = Ext.create('Ext.form.Panel', {
						region: 'north',
						layout: 'anchor',
						width: '50%',
						buttonAlign: 'center',
						cls:'window_border panel_space_left panel_space_right',
						border: false,
						dockedItems: [{
							xtype: 'toolbar',
							dock: 'top',
							border:false,
							items: [ taskName, eachNum,execComBox_tap,/*isSaveTemplateCk,*/execModeGroup,execUser,validCode,getValidCodeButton]
						},{
							xtype: 'toolbar',
							dock: 'top',
							border:false,
							items: [auditorComBox_tap, execTimeComponent,cycleExecCronPanel,authCode]
						},{
							xtype: 'toolbar',
							dock: 'top',
							border:false,
							items: [execDescForm]
						}]
					});


					if(scriptuuid === ''){
						Ext.Msg.alert('提示', '请选择脚本！');
						return;
					}

					if (sysAgent.size===0) {
						Ext.Msg.alert('提示', '请选择业务系统！');
						return;
					}

					let getAgentFlag = foreachSysAgentMap();
					if(!getAgentFlag){
						Ext.Msg.alert('提示', '未选择agent的业务系统获取所有agent列表失败！');
						return;
					}
					//去重Agent 多个业务系统绑定同一个设备的情况
					choosedAgentIds = [...new Set(choosedAgentIds)];

					execUser.setValue(execStartUser);
					let  submitWindow = Ext.create('Ext.window.Window', {
						title: '任务申请：'+submitTaskName,
						autoScroll: true,
						modal: true,
						resizable: false,
						// closeAction : 'hidden',
						layout: 'border',
						width: contentPanel.getWidth(),
						height: contentPanel.getHeight()*0.63,
						items: [topBar],
						/*listeners : {
							close : function(g, opt) {
								execStartUser = '';
								scriptuuid = '';
								submitTaskName = '';
								serviceName = '';
								protypeGlobal = '';
								sysAgent.clear();
								sysAgentFlagMap.clear();
								sName.reset();
								ssScriptGrid.ipage.moveFirst();
								/!*sysListGrid.ipage.moveFirst();
                                agentGrid.ipage.moveFirst();*!/
								sysListGrid_store.removeAll();
								agentGrid_store.removeAll();
								choosedAgentIds=[];
							}
						},*/
						buttons: [{
							text: '确定',
							height: 38,
							cls: 'Common_Btn',
							handler: function () {

								if(choosedAgentIds.length===0 || sysAgent.size===0){
									Ext.Msg.alert('提示', "提交失败，请选择业务系统！");
									return;
								}


								//判断输入的审核人是否合法 start
								let displayField =auditorComBox_tap.getRawValue();
								if(!Ext.isEmpty(displayField)){
									//判断输入是否合法标志，默认false，代表不合法
									let flag = false;
									//遍历下拉框绑定的store，获取displayField
									auditorStore_tap.each(function (record) {
										//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
										let data_fullName = record.get('fullName');
										if(data_fullName == displayField){
											flag =true;
											//combo.setValue(record.get('loginName'));
										}
									});
									if(!flag){
										Ext.Msg.alert('提示', "输入的审核人非法");
										auditorComBox_tap.setValue("");
										return;
									}
								}
								//判断输入的审核人是否合法  end
								paramStore.sort('paramOrder', 'ASC');
								let m = paramStore.getRange(0, paramStore.getCount()-1);
								let jsonData = "[";
								for (let i = 0, len = m.length; i < len; i++) {
//                        let n = 0;
									let paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
									let paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
									let paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
									let paramValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';
									if(scriptTypeForTaskAudi=='bat') {
										if (paramDefaultValue.indexOf('"')>=0) {
											Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
											return;
										}
									}
									if ("" == paramType) {
										setMessage('参数类型不能为空！');
										return;
									}
									if (fucCheckLength(paramDesc) > 250) {
										setMessage('参数描述不能超过250字符！');
										return;
									}

									if (fucCheckLength(paramValue) > 3000 || fucCheckLength(paramDefaultValue) > 3000) {
										setMessage('参数不允许超过3000字符！');
										return;
									}
									if (paramValue.indexOf('"') != -1 || paramDefaultValue.indexOf('"') != -1) {
										setMessage('参数中不允许有双引号！');
										return;
									}



									if (paramType == 'IN-int' || paramType == 'int') {
										if (!checkIsInteger(paramDefaultValue)) {
											setMessage('参数类型为int，但参数默认值不是int类型！');
											return;
										}
									}
									if (paramType == 'IN-float' || paramType == 'float') {
										if (!checkIsDouble(paramDefaultValue)) {
											setMessage('参数类型为float，但参数默认值不是float类型！');
											return;
										}
									}
									if ( !Ext.isEmpty(Ext.util.Format.trim(paramValue)) ) {
										globalParams[m[i].get('iid')] = paramValue;
									} else if(Ext.isEmpty(Ext.util.Format.trim(paramValue)) && !Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))){
										globalParams[m[i].get('iid')] = paramDefaultValue;
									} else{
										delete globalParams[m[i].get('iid')];
									}
									let ss = Ext.JSON.encode(m[i].data);
									if (i == 0) jsonData = jsonData + ss;
									else jsonData = jsonData + "," + ss;
								}

								jsonData = jsonData + "]";

								let agents = [];
								if(scriptTypeForTaskAudi ==='sql'){
									if(choosedAgentRecordsMap.size > 0) {
										for (let record of choosedAgentRecordsMap.values()) {
											let cpidTmp = record.get('AGENTIID');
											let dsidTmp = cpdsMap[cpidTmp];
											if (null === dsidTmp || undefined === dsidTmp || "" === dsidTmp || -1 === dsidTmp) {
												let agentIp = record.get('AGENTIP');
												let agentPort = record.get('AGENTPORT');
												let dsErrMsg = "服务器【" + agentIp + ":" + agentPort + "】没有选择数据源！";
												Ext.Msg.alert('提示', dsErrMsg);
												return;
											}
											let tmpRec = {};
											tmpRec.cpid = cpidTmp;
											tmpRec.dsid = dsidTmp;
											agents.push(tmpRec);
											finalChosedAgentsAndDbSources[cpidTmp] = dsidTmp;
										}
									}else{
										Ext.Msg.alert('提示', "提交失败，sql脚本必须单独选择Agent！");
										return;
									}

								}else{
									finalChosedAgentsAndDbSources={};
									Ext.Array.each(choosedAgentIds,function(value){
										agents.push(value);
									});
								}

								let auditor = auditorComBox_tap.getValue();




								let execT; //corn表达式 周期执行使用
								let execTimeDisplay; //定时执行 执行时间 年月日时分秒展示

								let execRadioValue =  execModeGroup.getChecked()[0].inputValue; // 执行策略单选
								if(execRadioValue == 1){//周期执行
									execT =cycleExecCronText.getValue();
									execTimeDisplay = '';
									if(execT== '' || execT == null){
										Ext.Msg.alert('提示', "执行策略为周期执行，执行周期不能为空！");
										return;
									}
								}else if(execRadioValue == 2){//定时执行
									execTimeDisplay = execTimeComponent.getRawValue();
									execT ='';
									if(execTimeDisplay== '' || execTimeDisplay == null){
										Ext.Msg.alert('提示', "执行策略为定时执行，执行时间不能为空！");
										return;
									}
								}else if(execRadioValue == 0){//触发执行
									execTimeDisplay = '';
									execT ='';
								}


								let execDescForExec = Ext.util.Format.trim(execDesc.getValue());
								if(Ext.isEmpty(execDescForExec)) {
									Ext.Msg.alert('提示', "没有填写执行描述！");
									return;
								}
								if (fucCheckLength(execDescForExec) > 255) {
									setMessage('执行描述不能超过255字符！');
									return;
								}
								let taskN = Ext.util.Format.trim(taskName.getValue());
								if(Ext.isEmpty(taskN)) {
									Ext.Msg.alert('提示', "任务名称不能为空！");
									return;
								}

								if (fucCheckLength(taskN) > 255) {
									setMessage('任务名称不能超过255字符！');
									return;
								}
								let en = eachNum.getValue();
								if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
									if(en>eachNumForA) {
										setMessage('并发数量不能超过'+eachNumForA);
										return;
									}
								} else {
									setMessage('并发数量不合法！');
									return;
								}

								if(Ext.isEmpty(en)) {
									en = eachNumForA;
								}
								//如果输入验证码，校验验证码是否正确
								let authCodeForExec = Ext.util.Format.trim(authCode.getValue());
								let phonenum = validCode.getValue();
								if(!Ext.isEmpty(authCodeForExec)){
									if(!isPoneAvailable(phonenum)){
										Ext.MessageBox.alert("提示", "请填写正确的手机号！");
										return;
									}
								}

								let mxIid = iidForTaskAudi+":"+4;
								startData[mxIid] = {
									'actNo': 4,
									'actType': 0,
									'actName': '基础脚本1'
								};
								startData[mxIid]['isShutdown'] = false;
								startData[mxIid]['chosedResGroups'] = [];
								startData[mxIid]['chosedAgentIds'] = choosedAgentIds;
								startData[mxIid]['globalParams'] = globalParams;
								startData[mxIid]['globalConfigParams'] = {};
								startData[mxIid]['globalStartUser'] = Ext.util.Format.trim(execUser.getValue());
								startData[mxIid]['globalConfigStartUser'] = {};
								startData[mxIid]['finalChosedAgentsAndDbSources'] = finalChosedAgentsAndDbSources;
								startData[mxIid]['eachNum'] = en;
								startData[mxIid]['chosedAgentUser'] = [];
								let performUser = execComBox_tap.getValue();
								let isSaveTemplate = false;//暂时不支持常用任务
								//填写验证码了，去数据库校验。
								//校验验证码是否正确
								/*if(!Ext.isEmpty(authCodeForExec)){//填写验证码了，去数据库校验。
									Ext.Ajax.request({
										url : 'validAuthCode.do',
										method : 'POST',
										params : {
											serviceId: iidForTaskAudi ,
											stepId:'-1',
											planId:'-1',
											iscenceId:'-1',
											authCode : authCodeForExec,
											telephoneNum :phonenum ,
											iauditUser :auditor,
											isscript:'0',
											proType:'10',
											systemId:'-1'
										},
										success: function(response, opts) {
											let success = Ext.decode(response.responseText).success;
											let message = Ext.decode(response.responseText).message;
											if(success) {
												if(isSaveTemplate) {
													Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
														if(btn=='ok') {
															if(Ext.isEmpty(Ext.util.Format.trim(text))) {
																let newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
																Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));
															} else {
																Ext.MessageBox.wait("信息验证中...","提示");
																let customName = Ext.util.Format.trim(text);
																Ext.Ajax.request({
																	url: 'checkCustomTemplateNameIsExist.do',
																	params: {
																		customName: customName,
																		flag: 1
																	},
																	method: 'POST',
																	success: function(response, options) {
																		if (!Ext.decode(response.responseText).success) {
																			let newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
																			Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));
																		} else {
																			Ext.Ajax.request({
																				url : 'scriptExecAuditing.do',
																				method : 'POST',
																				params : {
																					serviceId: iidForTaskAudi,
																					execUser: execUser.getValue(),
																					agents: agents,

																					params: jsonData,
																					execDesc:execDescForExec,
																					auditor: auditor,
																					//isDelay:isdelay,
																					execTime:execT,
																					execTimeDisplay : execTimeDisplay,
																					execStrategy:execRadioValue,
																					taskName: taskN,
																					performUser:performUser,
																					// butterflyversion:butterflyV,
																					data: JSON.stringify(startData),
																					sysAgentData: mapToJson(sysAgent),
																					eachNum: en,
																					scriptLevel: scriptLevelForTaskAudi

																				},
																				success: function(response, opts) {
																					let success = Ext.decode(response.responseText).success;
																					let message = Ext.decode(response.responseText).message;
																					if(success) {
																						if(scriptLevelForTaskAudi!=0){

																							Ext.MessageBox.alert("提示", "请求已经发送到审核人");
																						}
																						let iworkItemid = Ext.decode(response.responseText).workItemId;
																						Ext.Ajax.request({
																							url : 'updateWokitemId.do',
																							method : 'POST',
																							params : {
																								workitemId : iworkItemid ,
																								serviceId: iidForTaskAudi ,
																								stepId:'-1',
																								planId:'-1',
																								iscenceId:'-1',
																								authCode : authCodeForExec,
																								telephoneNum :phonenum ,
																								iauditUser :auditor,
																								isscript:'0',
																								proType:'10',
																								systemId:'-1'
																							},
																							success : function (response, opts)
																							{

																							},
																							failure: function(result, request) {
																								secureFilterRs(result,"回更workitemid失败！");
																							}
																						});
																						Ext.Ajax.request ({
																							url : 'scriptExecForOneRecord.do',
																							method : 'POST',
																							params :
																								{
																									iworkItemid: iworkItemid
																								},
																							success : function (response, opts)
																							{
																								let success = Ext.decode (response.responseText).success;
																								if(success){
																									if(scriptLevelForTaskAudi==0){//白名单，直接调用执行方法
																										if(execRadioValue == 0){//不是定时任务就直接调用任务执行中的执行方法
																											let	uuid ='';
																											Ext.Ajax.request({
																												url: 'queryUuidById.do',
																												method: 'POST',
																												async: false,
																												params: {
																													serviceId: iidForTaskAudi
																												},
																												success: function(response, options) {
																													uuid = Ext.decode(response.responseText).serviceUuid;
																												},
																												failure: function(result, request) {
																												}
																											});
																											Ext.Ajax.request({
																												url : 'execScriptServiceStart.do',
																												method : 'POST',
																												params : {
																													serviceId : iidForTaskAudi,
																													uuid:uuid,
																													serviceName : serviceNameForTaskAudi,
																													scriptType:scriptTypeForTaskAudi,
																													workItemId : iworkItemid,
																													coatId : 0,
																													isFlow: 0
																												},
																												success : function(response, request) {
																													let success = Ext.decode(response.responseText).success;
																													if (success) {
																														let flowId = Ext.decode(response.responseText).content;
																														Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
																													}
																												},
																												failure : function(result, request) {
																													Ext.Msg.alert('提示', '执行失败！');
																												}
																											});
																										}
																									}else
																									{
																										Ext.Msg.alert('提示', '提交成功');
																									}
																								}
																							}
																						});

																						if(typeof(submitWindow)!="undefined" && submitWindow){
																							submitWindow.close();
																						}

																						Ext.Ajax.request({
																							url: 'saveFlowCustomTemplate.do',
																							method: 'POST',
																							params: {
																								customName: customName,
																								serviceId: iidForTaskAudi,
																								data: JSON.stringify(startData),
																								audiUserLoginName: auditor,
																								taskName: taskN,
																								flag: 1
																							},
																							success: function(response, options) {
																								let success1 = Ext.decode(response.responseText).success;
																								//let message1 = Ext.decode(response.responseText).message;
																								if (success1) {
																									Ext.MessageBox.show({
																										title: "提示",
																										msg: '请求已经发送到审核人<br>常用任务保存成功！',
																										buttonText: {
																											yes: '确定'
																										},
																										buttons: Ext.Msg.YES
																									});
																								}
																							},
																							failure: function(result, request) {
																								Ext.MessageBox.show({
																									title: "提示",
																									msg: "请求已经发送到审核人<br>模板保存失败",
																									buttonText: {
																										yes: '确定'
																									},
																									buttons: Ext.Msg.YES
																								});
																							}

																						});
																					} else {
																						Ext.MessageBox.alert("提示", message);
																					}
																				},
																				failure: function(result, request) {
																					secureFilterRs(result,"操作失败！");
																				}
																			});
																		}
																	},
																	failure: function(result, request) {}
																});
															}

														}

													});
												}else{
													Ext.MessageBox.wait("信息验证中...","提示");
													Ext.Ajax.request({
														url : 'scriptExecAuditing.do',
														method : 'POST',
														params : {
															serviceId: iidForTaskAudi,
															execUser: execUser.getValue(),
															agents: agents,

															params: jsonData,
															execDesc:execDescForExec,
															auditor: auditor,
															execTime:execT,
															execTimeDisplay : execTimeDisplay,
															execStrategy:execRadioValue,
															taskName: taskN,
															performUser:performUser,
															data: JSON.stringify(startData),
															sysAgentData: mapToJson(sysAgent),
															eachNum: en,
															scriptLevel: scriptLevelForTaskAudi

														},
														success: function(response, opts) {
															let success = Ext.decode(response.responseText).success;
															let message = Ext.decode(response.responseText).message;
															if(success) {
																let iworkItemid = Ext.decode(response.responseText).workItemId;

																Ext.Ajax.request ({
																	url : 'scriptExecForOneRecord.do',
																	method : 'POST',
																	params :
																		{
																			iworkItemid: iworkItemid
																		},
																	success : function (response, opts)
																	{
																		let success = Ext.decode (response.responseText).success;
																		if(success){
																			if(scriptLevelForTaskAudi==0){//白名单，直接调用双人复核中，同意执行方法
																				if(execRadioValue == 0){//不是定时任务就直接调用任务执行中的执行方法
																					let	uuid ='';
																					Ext.Ajax.request({
																						url: 'queryUuidById.do',
																						method: 'POST',
																						async: false,
																						params: {
																							serviceId: iidForTaskAudi
																						},
																						success: function(response, options) {
																							uuid = Ext.decode(response.responseText).serviceUuid;
																						},
																						failure: function(result, request) {
																						}
																					});
																					Ext.Ajax.request({
																						url : 'execScriptServiceStart.do',
																						method : 'POST',
																						params : {
																							serviceId : iidForTaskAudi,
																							uuid :uuid,
																							serviceName : serviceNameForTaskAudi,
																							scriptType:scriptTypeForTaskAudi,
																							workItemId : iworkItemid,
																							coatId : 0,
																							isFlow: 0
																						},
																						success : function(response, request) {
																							let success = Ext.decode(response.responseText).success;
																							if (success) {
																								let flowId = Ext.decode(response.responseText).content;
																								let coatIds = Ext.decode(response.responseText).coatIds;
																								//Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
																								Ext.MessageBox.close();
																								let winStep = Ext.create ('Ext.window.Window', {
																									title : '执行结果',
																									modal : true,
																									closeAction : 'destroy',
																									constrain : true,
																									autoScroll : true,
																									width : contentPanel.getWidth (),
																									height : contentPanel.getHeight (),
																									minWidth : 350,
																									draggable : true,// 禁止拖动
																									resizable : false,// 禁止缩放
																									layout : 'fit',
																									loader : {
																										url : 'forwardscriptserverForMonitorSingle.do',
																										params : {
																											flowId:flowId,
																											coatid:coatIds, //
																											flag:'',//
																											flagTypeForMonitorSingle : '1',
																											isWin: 1
																										},
																										autoLoad : true,
																										scripts : true
																									}
																								});
																								winStep.show();
																							}

																						},
																						failure : function(result, request) {
																							Ext.Msg.alert('提示', '执行失败！');
																						}
																					});
																				}
																			}else
																			{
																				Ext.Msg.alert('提示', '提交成功');
																			}
																		}
																	}
																});
																if(typeof(submitWindow)!="undefined" && submitWindow){
																	submitWindow.close();
																}
															} else {
																Ext.MessageBox.alert("提示", message);
															}
														},
														failure: function(result, request) {
															secureFilterRs(result,"操作失败！");
															if(typeof(submitWindow)!="undefined" && submitWindow){
																submitWindow.close();
															}
														}
													});
												}

											}else{
												Ext.MessageBox.alert("提示", message);
											}
										},
										failure: function(result, request) {
											secureFilterRs(result,"操作失败！");
										}

									});
								}else{
									if(isSaveTemplate) {
										functionIsSaveTemplate(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow);
									}else{
										functionIsNotSaveTemplate(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow);
									}
								}*/
								//白名单不需要验证码，直接审核通过
								if(isSaveTemplate) {
									functionIsSaveTemplate(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow);
								}else{
									functionIsNotSaveTemplate(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow);
								}
							}

						}],
						buttonAlign: 'center'
					}).show();
					function submitSuccessClose() {
						execStartUser = '';
						scriptuuid = '';
						submitTaskName = '';
						serviceName = '';
						protypeGlobal = '';
						sysAgent.clear();
						choosedAgentRecordsMap.clear();
						cpdsMap = {};
						selCpId = -1;
						finalChosedAgentsAndDbSources = {};
						sysAgentFlagMap.clear();
						sName.reset();
						ssScriptGrid.ipage.moveFirst();
						/*sysListGrid.ipage.moveFirst();
                        agentGrid.ipage.moveFirst();*/
						paramStore.removeAll();
						attachmentStore.removeAll();
						sysListGrid_store.removeAll();
						agentGrid_store.removeAll();
						dbinfo_store.removeAll();
						choosedAgentIds=[];
					}

					function scriptSqlTaskExec(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow, customName,saveTemplateFlag) {
						Ext.Ajax.request({
							url : 'scriptExecAuditingForSQL.do',
							method : 'POST',
							params : {
								serviceId: iidForTaskAudi,
								execUser: execUser.getValue(),
								agentsJson: Ext.encode(agents),
								// rgIds: rgIds,
								params: jsonData,
								execDesc:execDescForExec,
								auditor: auditor,
								// isDelay:isdelay,
 								execTime: execT,
								execTimeDisplay: execTimeDisplay,
								execStrategy: execRadioValue,
								taskName: taskN,
								performUser:performUser,
								sysAgentData: mapToJson(sysAgent),
								// butterflyversion:butterflyV,
								data: JSON.stringify(startData),
								eachNum: en,
								scriptLevel: scriptLevelForTaskAudi,
								// chosedAgentUsers:[],
								isFromCustom : saveTemplateFlag?1:null//为了后台判断是否走带图的，1是走不带图的
							},
							success: function(response, opts) {
								let success = Ext.decode(response.responseText).success;
								let message = Ext.decode(response.responseText).message;
								if(success) {

									let iworkItemid = Ext.decode(response.responseText).workItemId;
									Ext.Ajax.request ({
										url : 'scriptExecForOneRecord.do',
										method : 'POST',
										params :
											{
												iworkItemid: iworkItemid
											},
										success : function (response, opts)
										{
											let success = Ext.decode (response.responseText).success;
											if(success){
												if(scriptLevelForTaskAudi==0){//白名单，直接调用执行方法
													if(execRadioValue == 0){//不是定时任务就直接调用任务执行中的执行方法
														let	uuid ='';
														Ext.Ajax.request({
															url: 'queryUuidById.do',
															method: 'POST',
															async: false,
															params: {
																serviceId: iidForTaskAudi
															},
															success: function(response, options) {
																uuid = Ext.decode(response.responseText).serviceUuid;
															},
															failure: function(result, request) {
															}
														});
														Ext.Ajax.request({
															url : 'execScriptServiceStart.do',
															method : 'POST',
															params : {
																serviceId : iidForTaskAudi,
																uuid:uuid,
																serviceName : serviceNameForTaskAudi,
																scriptType:scriptTypeForTaskAudi,
																workItemId : iworkItemid,
																coatId : 0,
																isFlow: 0
															},
															success : function(response, request) {
																let success = Ext.decode(response.responseText).success;
																if (success) {
																	let flowId = Ext.decode(response.responseText).content;
																	let coatIds = Ext.decode(response.responseText).coatIds;
																	// Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
																	Ext.MessageBox.close();
																	let winStep = Ext.create('Ext.window.Window', {
																		title: '执行结果',
																		modal: true,
																		closeAction: 'destroy',
																		constrain: true,
																		autoScroll: true,
																		width: contentPanel.getWidth(),
																		height: contentPanel.getHeight(),
																		minWidth: 350,
																		draggable: true,// 禁止拖动
																		resizable: false,// 禁止缩放
																		layout: 'fit',
																		loader: {
																			url: 'forwardscriptserverForMonitorSingle.do',
																			params: {
																				flowId: flowId,
																				coatid: coatIds, //
																				flag: '',//
																				flagTypeForMonitorSingle: '1',
																				isWin: 1
																			},
																			autoLoad: true,
																			scripts: true
																		}
																	});
																	winStep.show();
																}
															},
															failure : function(result, request) {
																Ext.Msg.alert('提示', '执行失败！');
																return;
															}
														});
													}
												}else
												{
													Ext.Msg.alert('提示', '提交成功');
													submitSuccessClose();
													forwardToScriptExecTask();
												}
											}
										}
									});



									if(typeof(submitWindow)!="undefined" && submitWindow){
										submitWindow.close();
									}
									if(saveTemplateFlag){
										Ext.Ajax.request({
											url: 'saveFlowCustomTemplate.do',
											method: 'POST',
											params: {
												customName: customName,
												serviceId: iidForTaskAudi,
												data: JSON.stringify(startData),
												audiUserLoginName: auditor,
												taskName: taskN,
												flag: 1
											},
											success: function(response, options) {
												let success1 = Ext.decode(response.responseText).success;
												//let message1 = Ext.decode(response.responseText).message;
												if (success1) {
													Ext.MessageBox.show({
														title: "提示",
														msg: '请求已经发送到审核人<br>常用任务保存成功！',
														buttonText: {
															yes: '确定'
														},
														buttons: Ext.Msg.YES
													});
												}
											},
											failure: function(result, request) {
												Ext.MessageBox.show({
													title: "提示",
													msg: "请求已经发送到审核人<br>模板保存失败",
													buttonText: {
														yes: '确定'
													},
													buttons: Ext.Msg.YES
												});
											}

										});
									}else {
										Ext.Msg.alert('提示', '提交成功');
										submitSuccessClose();
									}
								} else {
									Ext.MessageBox.alert("提示", message);
								}
							},
							failure: function(result, request) {
								secureFilterRs(result,"操作失败！");
							}
						});
					}

					function scriptNormalTaskExec(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow, customName,saveTemplateFlag) {
						Ext.Ajax.request({
							url: 'scriptExecAuditing.do',
							method: 'POST',
							dataType:'json',
							jsonData:Ext.JSON.encode([]),
							params: {
								serviceId: iidForTaskAudi,
								execUser: execUser.getValue(),
								agents: agents,

								params: jsonData,
								execDesc: execDescForExec,
								auditor: auditor,
								//isDelay:isdelay,
								execTime: execT,
								execTimeDisplay: execTimeDisplay,
								execStrategy: execRadioValue,
								taskName: taskN,
								performUser: performUser,
								// butterflyversion:butterflyV,
								data: JSON.stringify(startData),
								sysAgentData: mapToJson(sysAgent),
								eachNum: en,
								scriptLevel: scriptLevelForTaskAudi,
								isFromCustom: saveTemplateFlag?1:null//为了后台判断是否走带图的，1是走不带图的
							},
							success: function (response, opts) {
								let success = Ext.decode(response.responseText).success;
								let message = Ext.decode(response.responseText).message;
								if (success) {
									let iworkItemid = Ext.decode(response.responseText).workItemId;
									Ext.Ajax.request({
										url: 'scriptExecForOneRecord.do',
										method: 'POST',
										params:
											{
												iworkItemid: iworkItemid
											},
										success: function (response, opts) {
											let success = Ext.decode(response.responseText).success;
											if (success) {
												if (execRadioValue == 0) {
													//不是定时任务就直接调用任务执行中的执行方法
													let uuid = '';
													Ext.Ajax.request({
														url: 'queryUuidById.do',
														method: 'POST',
														async: false,
														params: {
															serviceId: iidForTaskAudi
														},
														success: function (response, options) {
															uuid = Ext.decode(response.responseText).serviceUuid;
														},
														failure: function (result, request) {
														}
													});
													Ext.Ajax.request({
														url: 'execScriptServiceStart.do',
														method: 'POST',
														params: {
															serviceId: iidForTaskAudi,
															uuid: uuid,
															serviceName: serviceNameForTaskAudi,
															scriptType: scriptTypeForTaskAudi,
															workItemId: iworkItemid,
															coatId: 0,
															isFlow: 0
														},
														success: function (response, request) {
															let success = Ext.decode(response.responseText).success;
															if (success) {
																let flowId = Ext.decode(response.responseText).content;
																let coatIds = Ext.decode(response.responseText).coatIds;
																//Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
																Ext.MessageBox.close();
																let winStep = Ext.create('Ext.window.Window', {
																	title: '执行结果',
																	modal: true,
																	closeAction: 'destroy',
																	constrain: true,
																	autoScroll: true,
																	width: contentPanel.getWidth(),
																	height: contentPanel.getHeight(),
																	minWidth: 350,
																	draggable: true,// 禁止拖动
																	resizable: false,// 禁止缩放
																	layout: 'fit',
																	loader: {
																		url: 'forwardscriptserverForMonitorSingle.do',
																		params: {
																			flowId: flowId,
																			coatid: coatIds, //
																			flag: '',//
																			flagTypeForMonitorSingle: '1',
																			isWin: 1
																		},
																		autoLoad: true,
																		scripts: true
																	}
																});
																winStep.show();
															}

														},
														failure: function (result, request) {
															Ext.Msg.alert('提示', '执行失败！');
														}
													});

												} else {
													Ext.Msg.alert('提示', '提交成功');
												}
											}
										}
									});
									if (typeof (submitWindow) != "undefined" && submitWindow) {
										submitWindow.close();
									}
									if(saveTemplateFlag){
										Ext.Ajax.request({
											url: 'saveFlowCustomTemplate.do',
											method: 'POST',
											params: {
												customName: customName,
												serviceId: iidForTaskAudi,
												data: JSON.stringify(startData),
												audiUserLoginName: auditor,
												taskName: taskN,
												flag: 1
											},
											success: function (response, options) {
												let success1 = Ext.decode(response.responseText).success;
												//let message1 = Ext.decode(response.responseText).message;
												if (success1) {
													Ext.MessageBox.show({
														title: "提示",
														msg: '请求已经发送到审核人<br>常用任务保存成功！',
														buttonText: {
															yes: '确定'
														},
														buttons: Ext.Msg.YES
													});
												}
											},
											failure: function (result, request) {
												Ext.MessageBox.show({
													title: "提示",
													msg: "请求已经发送到审核人<br>模板保存失败",
													buttonText: {
														yes: '确定'
													},
													buttons: Ext.Msg.YES
												});
											}

										});
									}
								} else {
									Ext.MessageBox.alert("提示", message);
								}
							},
							failure: function (result, request) {
								secureFilterRs(result, "操作失败！");
							}
						});
					}

					function functionIsSaveTemplate(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow) {
						Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function (btn, text, cfg) {
							if (btn == 'ok') {
								if (Ext.isEmpty(Ext.util.Format.trim(text))) {
									let newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
									Ext.Msg.show(Ext.apply({}, {msg: newMsg}, cfg));
								} else {
									Ext.MessageBox.wait("信息验证中...", "提示");
									let customName = Ext.util.Format.trim(text);
									Ext.Ajax.request({
										url: 'checkCustomTemplateNameIsExist.do',
										params: {
											customName: customName,
											flag: 1
										},
										method: 'POST',
										success: function (response, options) {
											if (!Ext.decode(response.responseText).success) {
												let newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
												Ext.Msg.show(Ext.apply({}, {msg: newMsg}, cfg));
											} else {
												if(scriptTypeForTaskAudi==='sql'){
													scriptSqlTaskExec(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow, customName,true);
												}else{
													scriptNormalTaskExec(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow, customName,true);
												}
											}
										},
										failure: function (result, request) {
										}
									});
								}

							}

						});
					}

					function functionIsNotSaveTemplate(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow) {
						Ext.MessageBox.wait("信息验证中...", "提示");
						if(scriptTypeForTaskAudi==='sql'){
							scriptSqlTaskExec(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow, '',false);
						}else{
							scriptNormalTaskExec(execUser, agents, jsonData, execDescForExec, auditor, execT, execTimeDisplay, execRadioValue, taskN, performUser, en, submitWindow, '',false);
						}
					}


				}
			}]
		}]
	});


	let mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptTaskApplyEM",
		layout : {
			type : 'vbox',
			align : 'stretch'
		},
		border : false,
		cls:'customize_panel_back',
		padding : grid_space,
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight()-modelHeigth -30,
		items : [outTopPanel,outBottomPanel]
		// items : [ssLeftPanel,ssCenterPanel,ssRightPanel]
	});

	contentPanel.on('resize',
		function() {
			mainPanel.setWidth(contentPanel.getWidth());
			mainPanel.setHeight(contentPanel.getHeight()-modelHeigth -30);
		});

	function removeByValue(arr, val) {
		for(let i=0; i<arr.length; i++) {
			if(arr[i] == val) {
				arr.splice(i, 1);
				break;
			}
		}
	}
	function forwardTaskMonitor(serviceId,scriptUuid,fromMenu) {
		contentPanel.getLoader().load({
			url: "initTaskMonitor.do",
			scripts: true,
			params: {
				serviceId: serviceId,
				scriptUuid: scriptUuid,
				fromMenu:fromMenu
			}
		});

	}
	function viewDetailForTaskIndex(serviceId) {
//    if (!DetailWinTi) {
		let DetailWinTi = Ext.create('widget.window', {
			title: '详细信息',
			closable: true,
			closeAction: 'destroy',
			width: contentPanel.getWidth(),
			minWidth: 350,
			height: contentPanel.getHeight(),
			draggable: false,
			// 禁止拖动
			resizable: false,
			// 禁止缩放
			modal: true,
			loader: {
				url: 'queryOneServiceForView.do',
				params: {
					iid: serviceId,
					flag: 1,
					hideReturnBtn: 1
				},
				autoLoad: true,
				scripts: true
			}
		});
//    }

//    DetailWinTi.getLoader().load({});
		DetailWinTi.show();
	}

	function viewDetailForTaskIndexForFlow(serviceId, serviceName, bussId, bussTypeId,scriptLevel,isFlow,menuId) {
//    if (!DetailWinTi) {
		let DetailWinTi = Ext.create('widget.window', {
			title: '详细信息',
			closable: true,
			closeAction: 'destroy',
			width: contentPanel.getWidth() ,
			minWidth: 350,
			height: contentPanel.getHeight()- 42,
			draggable: false,
			// 禁止拖动
			resizable: false,
			// 禁止缩放
			modal: true,
			loader: {
				url: 'flowCustomizedInitScriptService.do',
				params: {
					serviceId: serviceId,
					iid: serviceId,
					menuId: menuId,
					status: 0,
					serviceName:serviceName,
					actionType:'view',
					bussId:bussId,
					bussTypeId:bussTypeId,
					submitType:'tv',
					flag:0,
					windowHeight : contentPanel.getHeight() - 42,
					rootspace: 'view',
					isShowInWindow: 1,
					isScriptConvertToFlow : isFlow != '1'
				},


//			url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
//			params: {
//				iid:serviceId,
//				serviceName:serviceName,
//				actionType:'view',
//				bussId:bussId,
//				bussTypeId:bussTypeId,
//				flag:0,
//				isShowInWindow: 1
//			},
				autoLoad: true,
				scripts: true
			}
		});
//    }

//    DetailWinTi.getLoader().load({});
		DetailWinTi.show();
	}


});
function forwardToScriptExecTask() {
	contentPanel.getLoader().load({
		url: "scriptTaskexecAllForWhite.do",
		scripts: true,
		params: {
			fromMenu:'scriptTaskApplySs'
		}
	});
}
