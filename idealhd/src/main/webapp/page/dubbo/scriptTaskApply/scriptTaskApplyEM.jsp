<%@ page import="com.ideal.common.utils.SessionData" %>
<%@ page import="com.ideal.ieai.core.Environment" %>
<%@page contentType="text/html; charset=utf-8"%>
<% SessionData sessionData = SessionData.getSessionData(request);
	String loginUser = sessionData.getLoginName();
	String configNum =  Environment.getInstance().getConfigNum();
%>

<html>
<head>
	<script  type="text/javascript">
		var loginUser = '<%=loginUser%>';
		var number = "<%=configNum%>";
		var eachNumForA = <%=request.getAttribute("eachNum")%>;
		var toExecCount = <%=request.getAttribute("toExecCount")%>;
		//corn表达式公共方法使用的参数
		var iworkItemid = 0;
		var serviceName_history ='<%=request.getParameter("serviceName")==null?"":request.getParameter("serviceName")%>';
	    var bussName_history ='<%=request.getParameter("bussName")==null?"":request.getParameter("bussName")%>';
	    var bussTypeName_history ='<%=request.getParameter("bussTypeName")==null?"":request.getParameter("bussTypeName")%>';
	</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/JsonUtils.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptTaskApply/scriptTaskApplyEM.js"></script>

</head>
<body>
<div id="scriptTaskApplyEM" style="width: 100%; height: 100%"></div>
</body>
</html>
