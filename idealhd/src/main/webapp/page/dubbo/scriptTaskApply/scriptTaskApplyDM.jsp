<%@ page import="com.ideal.common.utils.SessionData" %>
<%@ page import="com.ideal.ieai.core.Environment" %>
<%@page contentType="text/html; charset=utf-8"%>
<% SessionData sessionData = SessionData.getSessionData(request);
	String loginUser = sessionData.getLoginName();
	String configNum =  Environment.getInstance().getConfigNum();
	boolean isProject=Environment.getInstance().getScriptProjectSwitch();
	//批量查询开关
	boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
	//按照ip查询开关
	boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
%>

<html>
<head>
<script  type="text/javascript">
	var isProject=<%=isProject%>;
	var loginUser = '<%=loginUser%>';
	var number = "<%=configNum%>";
	var eachNumForA = <%=request.getAttribute("eachNum")%>;
	var toExecCount = <%=request.getAttribute("toExecCount")%>;
	//corn表达式公共方法使用的参数
	var iworkItemid = 0;
	//历史返回页面使用的参数，
	var serviceName_history ='<%=request.getParameter("serviceName")==null?"":request.getParameter("serviceName")%>';
	var bussName_history ='<%=request.getParameter("bussName")==null?"":request.getParameter("bussName")%>';
	var bussTypeName_history ='<%=request.getParameter("bussTypeName")==null?"":request.getParameter("bussTypeName")%>';
	//批量查询开关
	var batchQuerySwitch = <%=batchQuery%>;
	//按照ip查询还是按照计算机名查询开关（true为山东城商需求，按照ip查询；false为bankCode001需求，按照计算机名查询）
	var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/JsonUtils.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptTaskApply/scriptTaskApplyDM.js"></script>

</head>
<body>
	<div id="scriptTaskApplyDM" style="width: 100%; height: 100%"></div>
</body>
</html>
