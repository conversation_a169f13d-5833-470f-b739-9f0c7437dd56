var store;
Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	
	var queryButton = Ext.create("Ext.Button", {
		margin : '5',
		text : '查询',
		cls : 'Common_Btn',
		height : 22,
		width : 70,
		handler : function() {
			queryBtnFun();
		}
	});
	var resetButton = Ext.create("Ext.Button", {
		margin : '5',
		text : '重置',
		cls : 'Common_Btn',
		height : 22,
		width : 70,
		handler : function() {
			resetBtnFun();
		}
	});
	var delButton = Ext.create("Ext.Button", {
		margin : '5',
		text : '删除',
		cls : 'Common_Btn',
		buttonAlign : 'right',
		height : 22,
		width : 70,
		handler : function() {
			DeleteScripts();
		}
	});
	var saveButton = Ext.create("Ext.Button", {
		margin : '5',
		text : '保存',
		cls : 'Common_Btn',
		buttonAlign : 'right',
		height : 22,
		width : 70,
		handler : function() {
			saveScripts();
		}
	});
	var addButton = Ext.create("Ext.Button", {
		margin : '5',
		text : '增加',
		cls : 'Common_Btn',
		buttonAlign : 'right',
		height : 22,
		width : 70,
		handler : function() {
			addScript();
		}
	});
	/** 查询条件* */
	var inameQuery = Ext.create('Ext.form.TextField', {
			emptyText : '--请输入执行脚本--',
			width : 260,
			name : 'selName',
			xtype : 'textfield',
			padding : '0 0 0 5'
		});
	
	var scriptNameQuery = Ext.create('Ext.form.TextField', {
			emptyText : '--请输入脚本名称--',
			width : 260,
			name : 'selscriptName',
			xtype : 'textfield',
			padding : '0 0 0 5'
	});

	// 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			listeners : {
				selectionchange : function(selModel, selections) {
				}
			}
		});
	// 定义方案名称
	Ext.define('scriptManagementModel', {
			extend : 'Ext.data.Model',
			fields : [ {
				name : 'iid',
				type : 'string'
			}, {
				name : 'iname',
				type : 'string'
			}, {
				name : 'scriptDesc',
				type : 'string'
			}, {
				name :'scriptPath',
				type :'string'
			}, {
				name : 'scriptName',
				type : 'string'
			}, {
				name : 'scriptMessage',
				type : 'string'
			} , {
				name : 'scriptMessages',
				type : 'string'
			}]
		});

	store = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'scriptManagementModel',
			pageSize : 30,
			proxy : {
				type : 'ajax',
				url : 'scripsList.do',
				reader : {
					type : 'json',
					root : 'scripsList',
					totalProterty : 'total'
				}
			}
		});

		pageBar = Ext.create('Ext.PagingToolbar', {
			store : store,
			dock : 'bottom',
			displayInfo : true,
			emptyMsg : "没有记录"
		});

	var columns = [{
						xtype : 'rownumberer'
					},{
						text : 'id',
						dataIndex : 'iid',
						hidden : true
					},{
						text : '执行脚本',
						flex : 1,
						editor : {
							xtype : 'textfield'
						},
						dataIndex : 'iname'
					},{
						text : '描述',
						flex : 1,
						editor : {
							xtype : 'textfield'
						},
						dataIndex : 'scriptDesc'
					}/*,{
						text : '路径',
						flex : 1,
						editor :{
							xtype : 'textfield'
						},
						dataIndex : 'scriptPath'
					},{
						text : '脚本名称',
						flex : 1,
						editor :{
							xtype : 'textfield'
						},
						dataIndex : 'scriptName'
					}*/,{
						text : '脚本内容',
						width:100,
						dataIndex : 'scriptMessage',
						hidden:true,
						align : 'center',
						renderer : function(value, p, record, rowIndex) {
							var sid = record.get('iid');
							return '<span class="switch_span"><a href="javascript:void(0)" onclick="details('
								+ sid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>详情</a></span>';
						}
					},{
						text : '脚本内容',
						width:100,
						dataIndex : 'scriptMessages',
						align : 'center',
						renderer : function(value, p, record, rowIndex) {
							var sid = record.get('iid');
							return '<span class="switch_span"><a href="javascript:void(0)" onclick="detailss('
								+ sid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>详情</a></span>';
						}
					} ];

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 2
		});

	var form = Ext.create('Ext.form.FormPanel', {
			buttonAlign : 'right',
			layout : 'column',
			border : false,
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				items : [ inameQuery, /*scriptNameQuery,*/ resetButton, queryButton, {
					xtype : 'tbseparator'
				}, {
					xtype : 'tbfill'
				}, addButton, saveButton, delButton ]
			} ]
		});

	var grid = Ext.create('Ext.grid.Panel', {
			region : 'center',
			height : contentPanel.getHeight() - 65,
			flex : 2,
			store : store,
			autoScroll : true,
			border : true,
			columnLines : true,
			selModel : selModel,
			loadMask : {
				msg : " 数据加载中，请稍等 "
			},
			bbar : pageBar,
			columns : columns,
			plugins : [ cellEditing ]
		});

	grid.view.on('expandBody', function (rowNode, record, expandRow, eOpts) {
		loadShelloutputscript(record.get('iid'));
    }); 
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "scriptmanagement_area",
			border : false,
			bodyPadding : 5,
			items : [ form, grid ]
		});

	store.on('beforeload', function(store, options) {
			var new_params = {
				
				sname : inameQuery.getValue().trim()
//				scriptName : scriptNameQuery.getValue().trim()
			};
			Ext.apply(store.proxy.extraParams, new_params);
		});

	contentPanel.on('resize', function() {
			grid.setHeight(contentPanel.getHeight() - 65);
		});

	function queryBtnFun() {
			if (Ext.isIE) {
				CollectGarbage();
			}
			pageBar.moveFirst();
		}
	function resetBtnFun(){
			inameQuery.setValue('');
//			scriptNameQuery.setValue('');
			store.load();
		}
		//增加
	function addScript(){
			var scriptForm = Ext.create('Ext.form.FormPanel',{ 
			      border : false,
			      items: [  
			        /*{  
			            xtype : 'textfield',  
			            fieldLabel : '名称', 
			            name : 'iname',
			            anchor : '100%'
			        }, */{  
			            xtype : 'textfield',  
			            fieldLabel : '路径', 
			            labelWidth: 80,	
			            labelAlign:'right',
			            name : 'scriptPath',
			            anchor : '90%'
			        }, {
			        	xtype: 'filefield',
			        	fieldLabel: '选择文件',
						name: 'uploadFile', 
						msgTarget: 'side',
						labelWidth: 80,
						labelAlign:'right',
						anchor: '90%',
						buttonText: '选择文件'
						
			        }, {  
			            xtype : 'textarea',  
			            fieldLabel : '描述', 
			            labelWidth: 80,	
			            labelAlign:'right',
			            height:150,
			            name : 'scriptDesc',
			            anchor : '90%'  
			        } ],  
			      buttonAlign: 'center',  
			      buttons : [  
			        {  
			          text : '上传',  
			          handler : function() {  
			        			var form = scriptForm.getForm();
			        			var hdupfile=form.findField("uploadFile").getValue();
			        			var hdtmpFilNam=hdupfile;
			        			if(hdupfile==''){
			        				Ext.Msg.alert('提示',"请选择文件...");
			        				return ;
			        			}
	    			
			        			if(!checkFile(hdtmpFilNam)){
			        				form.findField("uploadFile").setRawValue('');
			        				return;
			        			}
			        						        			 
			        			var scriptDesc = scriptForm.getForm().findField('scriptDesc').getValue().trim(); 
			        			var scriptPath = scriptForm.getForm().findField('scriptPath').getValue().trim();
			        			
			        			if(scriptDesc.length>200) {
			        				Ext.Msg.alert('提示','描述不能超过200个字符！');
			        				return;
			        			}
			        			if("" == scriptPath || null == scriptPath){
			        				Ext.Msg.alert('提示','请填写路径！');
			        				return;
			        			}
			            uploadScripts(form);			            
			          }  
			        }, {  
			          text : '重置',  
			          handler : function() {  
			        	var form = this.up('form').getForm();
						form.reset();
						return;
			          }  
			        }  
			      ]  
			    }); 
			
		var scriptWin = new Ext.Window({  
			      title : '增加脚本',  
			      autoScroll : false,  
			      autoDestroy : true,
			      closeable : true,  
			      modal : true,  
			      width : 600,
			      height: 300,
			      resizable : false,  
			      plain : true,  
			      layout : 'form',  
			      draggable:true,
			      items : [scriptForm]  
			    });  
			scriptWin.show();
		}
	function checkFile(fileName){  
			    var file_reg = /\.([bB][aA][tT]){1}$|\.([sS][hH]){1}$/;  
			    if(!file_reg.test(fileName)){  
			        Ext.Msg.alert('提示','文件类型错误,请选择正确的脚本文件(bat/sh)'); 
			        return false;
			    }
			    return true;
			}
		
	function uploadScripts(form){
			if (form.isValid()) {
			       form.submit({
			         url: 'uploadScripts.do',
			           success: function(form, action) {
			    	   	if(action.result.success){
			    		   Ext.Msg.alert('提示', action.result.msg);
			    		   store.load();
			    	   	}else{
			    		   Ext.Msg.alert('提示', action.result.msg);			    		   
			    		   return;
			    	   	}			           			             
			           },
			           failure: function(form, action) {			             
			               Ext.Msg.alert('提示', action.result.msg);
			             return;
			           }
			       });
				   }
		}
		/*
		 * 删除
		 */
	function DeleteScripts() {
			if (Ext.isIE) {
				CollectGarbage();
			}
			var seleCount = grid.getSelectionModel().getSelection();
			if (seleCount.length < 1) {
				Ext.MessageBox.alert("提示", "请选择要删除的数据");
				return;
			}

			Ext.MessageBox.buttonText.yes = "确定";
			Ext.MessageBox.buttonText.no = "取消";
			Ext.Msg.confirm("确认删除", "确定删除选中的组记录", function(id) {
				if (id == 'yes')
					deleteScripts();
			});
		}

	function deleteScripts() {		
			var m = grid.getSelectionModel().getSelection();
			var jsonData = "[";
			for ( var i = 0; i < m.length; i++) {
				var ss = Ext.JSON.encode(m[i].data);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";
			Ext.Ajax.request( {
				url : 'deleteScripts.do',
				method : 'post',
				params : {
					jsonData : jsonData
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						pageBar.moveFirst();
						Ext.Msg.alert('提示', '删除成功！');
					} else {
						Ext.Msg.alert('提示', '删除失败！');
					}
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '操作失败');
				}
			});
		}
		/*
		 * 保存
		 */
	function saveScripts() {
			if (Ext.isIE) {
				CollectGarbage();
			}
			var m = store.getModifiedRecords();
			if (m.length == 0) {
				Ext.MessageBox.alert("提示", "没有需要保存的条目！");
				return;
			}

			for ( var j = 0; j < m.length; j++) {
				var n = 0;
				var iname = m[j].data['iname'].trim();
				var scriptDesc = m[j].data['scriptDesc'].trim();

				if ("" == iname || iname == null) {
					Ext.MessageBox.alert("提示", "执行脚本不能为空！");
					return;
				}
//				if(!checkFile(iname)){
//					Ext.MessageBox.alert("提示", "执行脚本文件类型为bat/sh！");
//    				return;
//    			}
				if (iname.length > 200) {
					Ext.MessageBox.alert("提示", "执行脚本不能超过200个字符！");
					return;
				}
				if (scriptDesc.length > 200) {
					Ext.MessageBox.alert("提示", "描述不能超过200个字符！");
					return;
				}
			}

			for ( var k = 0; k < store.getCount(); k++) {
				var record = store.getAt(k);
				var name = record.data.iname.trim();
				if (name == iname) {
					n = n + 1;
				}
			}
			if (n > 1) {
				Ext.MessageBox.alert("提示", "执行脚本不能重复！");
				return;
			}
			saveData();
		}
	function saveData() {
			var m = store.getModifiedRecords();
			var jsonData = "[";
			for ( var i = 0; i < m.length; i++) {
				var ss = Ext.JSON.encode(m[i].data);
				if (i == 0) {
					jsonData = jsonData + ss;
				} else {
					jsonData = jsonData + "," + ss;
				}
			}
			jsonData = jsonData + "]";
			Ext.Ajax.request( {
				url : 'saveScripts.do',
				method : 'post',
				params : {
					jsonData : jsonData
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						store.load();
						Ext.Msg.alert("提示", message);
					} else {
						Ext.Msg.alert("提示", message);
					}
				},
				failure : function(result, request) {
					Ext.Msg.alert("提示", "操作失败！");
				}
			});
		}

		// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
				function(obj, options, eOpts) {
					Ext.destroy(mainPanel);
					if (Ext.isIE) {
						CollectGarbage();
					}
				});
	String.prototype.trim = function() {
			return this.replace(/(^\s*)|(\s*$)/g, "");
		};
	});
	function details(sid, rowIndex) {
		var same = store.getAt(rowIndex).get('iname');
		Ext.Ajax.request( {
			url : 'queryscriptMessage.do',
			method : 'post',
			params : {
				sid : sid
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var scriptMessage = Ext.decode(response.responseText).scriptMessage;
				var sid = Ext.decode(response.responseText).sid;
				var messageForm = Ext.create('Ext.form.FormPanel',{
					labelAlign : 'top',  
				      frame : true,  
				      border : false,
				      bodyStyle : 'padding:5px 0px 5px 5px',  
				      layout : 'form',
				      items :[{
				            	  xtype : 'textarea',
				            	  name : 'smessgae',
				            	  height : contentPanel.getHeight() - 130,
				            	  anchor : '100%',
				            	  value : scriptMessage
				              }],
				      buttonAlign: 'center', 
				      buttons :[{
				    	  			text : '保存',
				    	  			handler : function() {  
				    	  					Ext.Ajax.request( {
				    	  						url : 'savescriptMessage.do',
				    	  						method : 'POST',
				    	  						params : {
				    	  							sid : sid,
				    	  							smessage : messageForm.getForm().findField('smessgae').getValue()
				    	  						},
				    	  						success : function(response,request) {
				    	  							var success = Ext.decode(response.responseText).success;
				    	  							if (success) {
				    	  								Ext.Msg.alert('提示','保存成功!');
				    	  								messageWin.close();
				    	  								store.load();
				    	  							} else {
				    	  								Ext.Msg.alert('提示','保存失败！');
				    	  							}
				    	  						},
				    	  						failure : function(result,request) {
				    	  							Ext.Msg.alert('提示','保存失败！');
				    	  						}
				    	  					});
							      }
				      		}]
				});									
				
				var messageWin =  Ext.create('Ext.window.Window', {
						title : same,
						autoScroll : true,//纵向滚动条
						closeable : true,
						modal : true,
						resizable : true,
						closeAction : 'destroy',
						width : contentPanel.getWidth()/2,
						height : contentPanel.getHeight() - 20, 
					    layout : 'form',
					    items : [messageForm]
					});
				messageWin.show();
			},
			failure : function(result, request) {
				Ext.Msg.alert("提示", "查询失败！");
			}
		});
	}
	function detailss(sid, rowIndex) {
		var same = store.getAt(rowIndex).get('iname');
		Ext.Ajax.request( {
			url : 'queryscriptMessage.do',
			method : 'post',
			params : {
				sid : sid
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var scriptMessage = Ext.decode(response.responseText).scriptMessage;
				var sid = Ext.decode(response.responseText).sid;
				var messageForm = Ext.create('Ext.form.FormPanel',{
					labelAlign : 'top',  
				      frame : true,  
				      border : false,
				      bodyStyle : 'padding:5px 0px 5px 5px',  
				      layout : 'form',
				      items :[{
				            	  xtype : 'textarea',
				            	  name : 'smessgae',
				            	  height : contentPanel.getHeight() - 130,
				            	  anchor : '100%',
				            	  value : scriptMessage
				              }] 
				});	
				var messageWin =  Ext.create('Ext.window.Window', {
						title : same,
						autoScroll : true,//纵向滚动条
						closeable : true,
						modal : true,
						resizable : true,
						closeAction : 'destroy',
						width : contentPanel.getWidth()/2,
						height : contentPanel.getHeight() - 20, 
					    layout : 'form',
					    items : [messageForm]
					});
				messageWin.show();
			},
			failure : function(result, request) {
				Ext.Msg.alert("提示", "查询失败！");
			}
		});
	}
