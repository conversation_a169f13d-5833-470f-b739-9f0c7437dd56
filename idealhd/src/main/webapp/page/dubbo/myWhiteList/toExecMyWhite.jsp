<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean sendSwitch = Environment.getInstance().getScriptServiceSendSwitch();
%>
<html>
<head>
<%-- 下拉选不自动换行 --%>
<style>
	.customize_body .x-unselectable {
		white-space: nowrap;
	}
</style>
<script>
var loginuser = '<%=request.getAttribute("loginName")==null?"":request.getAttribute("loginName")%>';
var filter_category = '<%=request.getParameter("filter_category")==null?"":request.getParameter("filter_category")%>';
var serviceNameSh = '<%=request.getParameter("serviceName")==null?"":request.getParameter("serviceName")%>';
var idSh_v = '<%=request.getAttribute("id")==null?"":request.getAttribute("id")%>';
var contentSh = '<%=request.getParameter("icontent")==null?"":request.getParameter("icontent")%>';
var parmFlag_v = '<%=request.getParameter("parmFlag")==null?"":request.getParameter("parmFlag")%>';
var bakicoatid='<%=request.getAttribute("bakicoatid")==null?"":request.getAttribute("bakicoatid")%>';
var  editingChosedAgentIdstr = '<%=request.getAttribute("editingChosedAgentIds")==null?"":request.getAttribute("editingChosedAgentIds")%>';
//查询历史 跳转带过来的查询agent条件，返回时使用  start
var hostNameForWhite = '<%=request.getAttribute("hostName")==null?"":request.getAttribute("hostName")%>';
var ipForWhite = '<%=request.getAttribute("ip")==null?"":request.getAttribute("ip")%>';
var sysAdminForWhite = '<%=request.getAttribute("sysAdmin")==null?"":request.getAttribute("sysAdmin")%>';
var centerNameForWhite ='<%=request.getAttribute("centerName")==null?"":request.getAttribute("centerName")%>';
var systemInfoForWhite ='<%=request.getAttribute("systemInfo")==null?"":request.getAttribute("systemInfo")%>';
var middlewareTypeForWhite ='<%=request.getAttribute("middlewareType")==null?"":request.getAttribute("middlewareType")%>';
var osTypeForWhite ='<%=request.getAttribute("osType")==null?"":request.getAttribute("osType")%>';
var dbTypeForWhite ='<%=request.getAttribute("dbType")==null?"":request.getAttribute("dbType")%>';
//查询历史  跳转带过来的查询agent条件，返回时使用  end


//alert(editingChosedAgentIdstr);
if(editingChosedAgentIdstr !=''){
	var editingChosedAgentIds_v = editingChosedAgentIdstr.split(',');	
}else{
	var editingChosedAgentIds_v = new Array();
}


</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/myWhiteList/toExecMyWhite.js"></script>
</head>
<body>
<div id="execMyWhiteList_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>