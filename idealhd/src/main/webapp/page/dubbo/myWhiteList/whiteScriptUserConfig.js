
Ext.onReady(function(){
	var win;
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	Ext.define('userGroupData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'buss'    ,type : 'string'}, //类别显示
		    {name : 'bussType'    ,type : 'string'}, //类型显示
		    {name : 'bussId'    ,type : 'int'},//类别码值
		    {name : 'bussTypeId'    ,type : 'int'},//类型码值
		    {name : 'platForm',type : 'string'}, //适用平台
		    {name : 'icontent'     ,type : 'string'},  //命令
		    {name : 'funcDesc' ,type : 'string'},//备注
			{name : 'paramFlag'    ,type : 'string'},//有无参数
            
        ]
    });
    
	var userGroup_store = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        autoDestroy : true,
	        pageSize: 30,
	        model: 'userGroupData',
	        proxy: {
	            type: 'ajax',
	            url: 'getWhiteScriptList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });

	var userGroup_columns = [ {
				text : '序号',
				xtype : 'rownumberer',
				width : 40
			}, {
				text : 'iid',
				dataIndex : 'iid',
				hidden : true
			}, 
			{
				text : '命令说明',
				dataIndex : 'serviceName',
				flex : 1
			}, {
				text : '命令',
				dataIndex : 'icontent',
				flex : 1
			}, {
				text : '有无参数',
				dataIndex : 'paramFlag',
				flex : 1
			}, {
				text : '适用平台',
				dataIndex : 'platForm',
				flex : 1
			}, {
				text : '命令类别',
				dataIndex : 'buss',
				flex : 1
			},{
				text : '命令类型',
				dataIndex : 'bussType',
				flex : 1
			},{
				text : '备注',
				dataIndex : 'funcDesc',
				flex : 1
			}];
    
    var selModel = Ext.create('Ext.selection.CheckboxModel');

    var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: userGroup_store,
        dock: 'bottom',
        displayInfo: true

    });
    
    var groupNameQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入命令说明--',
		labelWidth : 50,
		width : 150,
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var groupName = field.getValue() == null ? "" : field.getValue();
					userGroup_store.load( {
						params : {
							groupName : groupName.trim()
						}
					});
				}
			}
		}
	});
	var queryButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '查询',
		handler : queryWhere
	});
	
	var addButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '绑定',
		handler : bindUserAndWhite
	});
	/** 重置按钮* */
	var resetButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '重置',
		handler : resetWhere
	});
    
    var groupName_grid = Ext.create('Ext.grid.Panel', {
	    store:userGroup_store,
	    border:false,
	    selModel:selModel,
	    columnLines : true,
	    columns:userGroup_columns,
	    viewConfig:{  
            enableTextSelection:true  
        },
	    bbar: pageBar,
	    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
 	    dockedItems: [{
  	       	xtype: 'toolbar',
	  		items: [groupNameQuery,queryButtonForBSM,resetButtonForBSM,addButton]}
	  	]
	});  
    
    var panelleft = Ext.create('Ext.panel.Panel',{
    	title:'白名单命令',
    	layout : 'fit',
		border:true,
		region : 'west',
		split : true,
		width : '55%',
		items : [groupName_grid]
    });
    
    
	Ext.define('groupInfoData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'igroupid', type: 'string'},
            {name: 'iagentid', type: 'string'},
            {name: 'sysName',  type: 'string'},
            { name: 'appName',  type: 'string'},
            { name: 'hostName', type: 'string'},
            { name: 'osType',type: 'string'},
            { name: 'agentIp', type: 'string'},
            {name: 'agentPort',type: 'string'},
            {name: 'envType',type: 'string'},
            { name: 'agentState', type: 'int'}]
    });
	
	var groupInfo_store = Ext.create('Ext.data.Store', {
        autoLoad : false,
		autoDestroy : true,
        pageSize: 30,
        model: 'groupInfoData',
        proxy: {
            type: 'ajax',
            url: 'getUserGroupAgentInfoList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	
	groupName_grid.on("select",function(obj, record, index, eOpts){
    	var groupId = record.get('iid');
    	if(groupId<1){
    		return;
    	}
    	groupInfo_store.load({
    		params: {
    			groupId : groupId,
    		}
    	});
    });
    
	groupInfo_store.on('beforeload', function (store, options) {
    	var selDatas = groupName_grid.getSelectionModel().getSelection();
    	var ii  = selDatas.length;
    	if(ii<1){
    		var groupId = -1;
    	}else{
    		var groupId = selDatas[ii-1].get('iid');
    	}
		var new_params = {  
				groupId : groupId,
			};
     Ext.apply(groupInfo_store.proxy.extraParams, new_params);
  });
	
	
    var agentInfo_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
                           { text: 'iid',  dataIndex: 'iid',hidden:true},
                           { text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
                           { text: 'iuserid',  dataIndex: 'iagentid',hidden:true},
                           {
                               text: '用户名',
                               dataIndex: 'fullname',
                               width: 80,
                               flex:1
                           },{
                               text: '登录名',
                               dataIndex: 'loginname',
                               width: 180
                           }
                           
                       ];
    
    var selModelServer = Ext.create('Ext.selection.CheckboxModel');

    
    var pageBarServer = Ext.create('Ext.PagingToolbar', {
    	store: groupInfo_store,
        dock: 'bottom',
        displayInfo: true
    });
    
    var groupInfo_grid = Ext.create('Ext.grid.Panel', {
    	selModel:selModelServer,
	    store:groupInfo_store,
	    border:false,
	    columnLines : true,
	    columns:agentInfo_columns,
	    viewConfig:{  
            enableTextSelection:true  
        },
	    bbar: pageBarServer,
	    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
 	    dockedItems: [{
  	       	xtype: 'toolbar',
	  		items: [{
	  		            xtype: 'tbseparator'
	  		        },{
	  		            xtype: 'tbfill'
	  		        },{
	  		            xtype: 'button',
	 		            textAlign:'center',
	  		            text: '删除',
	  		            cls : 'Common_Btn',
	  		            handler:function(){
	  		            	deleteGroupInfo();
	  		            }
	  		        }
	  		]}
	  	]
	});
    
    var panelrigth = Ext.create('Ext.panel.Panel',{
    	layout : 'fit',
    	title : '用户信息',
		region : 'center',
		items : [groupInfo_grid]
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel',{
    	height:contentPanel.getHeight()-modelHeigth,
        renderTo : "whiteScriptUserConfig_grid_area",
        border : false,
        layout : 'border',
    	items : [panelleft,panelrigth]
    });
    
    contentPanel.on('resize',function(){
    	mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
    	if(win){
    		win.setWidth(contentPanel.getWidth()-500),
	  		win.setHeight(contentPanel.getHeight()-100);
    	}
    });
    
    
    //Agent信息删除
    function deleteGroupInfo(){
    	Ext.Msg.confirm('确认提示','您确定要进行此操作吗？',function(bn){
    		if(bn=='yes'){
    			var selDatas = groupInfo_grid.getSelectionModel().getSelection();
    			if(selDatas.length==0){
    				Ext.Msg.alert('消息提示','请选择记录进行操作！');
    				return;
    			}
    			var iids = [];
    			Ext.Array.each(selDatas, function(record) {
                    var iid = record.get('iid');
                    // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                    if(iid==0){
                    	userGroup_store.remove(record);
                    }else{
                    	iids.push(iid);
                    }
                });
    			Ext.Ajax.request({
    			    url: 'deleteUserGroupAgentInfo.do',
    			    params: {
    			    	iids: iids
    			    },
    			    success: function(response){
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
							if (success) {
								pageBarServer.moveFirst();
								Ext.Msg.alert('提示', message);
							} else {
								Ext.Msg.alert('提示', message);
							}
						},
    			    failure: function(result, request) {
    			    	secureFilterRs(result,"请求返回失败！",request);
    			    }
    			});
    		}
    	});
    }
    
    function queryWhere() {
    	var groupName = groupNameQuery.getValue()==null?"":groupNameQuery.getValue();
    	userGroup_store.load({
    			params: {serviceName:groupName.trim()}
    	});
	}
    function resetWhere ()
	{
    	groupNameQuery.setValue ('');
	}
    //用户信息增加
    function bindUserAndWhite(){
    	var selDatas = groupName_grid.getSelectionModel().getSelection();
    	
		if(selDatas.length==0){
			Ext.Msg.alert('消息提示','请选择记录进行操作！');
			return;
		}
		var iids = [];
		Ext.Array.each(selDatas, function(record) {
            var iid = record.get('iid');
            // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
            if(iid==0){
            	userGroup_store.remove(record);
            }else{
            	iids.push(iid);
            }
        });
		
		if(win){
			win.close();
		}
		 var win = Ext.create('Ext.window.Window', {
		  		title : '用户信息',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		closeAction : 'destroy',
		  		width : contentPanel.getWidth()-500,
		  		height : contentPanel.getHeight()-100,
		  		loader : {
			 			url : "bindUserWhiteList.do?groupId="+iids,
			 			params : {
			 				groupId : groupId
	  					},
	  					autoLoad: true,
	  					autoDestroy : true,
	  					scripts : true
		  			}
		  	}).show();
		 win.on("close",function(){
			 groupInfo_store.reload();
		 });
    }
});

String.prototype.trim = function ()
{
	return this.replace (/(^\s*)|(\s*$)/g, "");
};
