var myWhiteListStore;
var CreateCommWinForWhiteList;
var CreateCommFormForWhiteList;
Ext.onReady(function() {
  var shareGrid;
  var shareedGrid;
  var shareWin;
  var shareType=0; //共享类型  0 所有人  1 用户   2 用户组
  var chosedShareIds =[]; //共享用户、用户组  数组
  Ext.define('Ext.form.MultiSelect', {  
	    extend: 'Ext.form.ComboBox',  
	    alias: 'widget.multicombobox',  
	    xtype: 'multicombobox',  
	    initComponent: function(){  
	        this.multiSelect = true;  
	        this.listConfig = {  
	              itemTpl : Ext.create('Ext.XTemplate',  
	                    '<input type=checkbox>{[values.' + this.displayField + ']}'),  
	              onItemSelect: function(record) {      
	                  var node = this.getNode(record);  
	                  if (node) {  
	                     Ext.fly(node).addCls(this.selectedItemCls);  
	
	                     var checkboxs = node.getElementsByTagName("input");  
	                     if(checkboxs!=null)  
	                     {  
	                         var checkbox = checkboxs[0];  
	                         checkbox.checked = true;  
	                     }  
	                  }  
	              },  
	              onItemDeselect: function(record) {
	                var node = this.getNode(record);
	                if (node) {
	                    Ext.fly(node).removeCls(this.selectedItemCls);
	
	                    var checkboxs = node.getElementsByTagName("input");
	                    if (checkboxs != null)
	                    {
	                        var checkbox = checkboxs[0];
	                        checkbox.checked = false ;
	                    }
	                }
	            },
	              listeners:{  
	                  itemclick:function(view, record, item, index, e, eOpts ){  
	                      var isSelected = view.isSelected(item);  
	                      var checkboxs = item.getElementsByTagName("input");  
	                      if(checkboxs!=null)  
	                      {  
	                          var checkbox = checkboxs[0];  
	                          if(!isSelected)  
	                          {  
	                              checkbox.checked = true;  
	                          }else{  
	                              checkbox.checked = false;  
	                          }  
	                      }  
	                  }  
	              }         
	        }         
	        this.callParent();  
	    }  
}); 
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	//var filter;
	// var editCommForm;
	var publishAuditingSMWin;
	var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
	var auditing_form_sm;
	var auditorStore_sm;
	var auditorComBox_sm;
	var planTime_sm;
	var pubDesc_sm;
	var scriptLevelCb_sm;
	var chosedIds=new Array();
	var chosedAppSys = new Array();
//	var platformData = [{
//				"id" : "-1",
//				"name" : "全部"
//			}, {
//				"id" : "Windows",
//				"name" : "Windows"
//			}, {
//				"id" : "HP-UX",
//				"name" : "HP-UX"
//			}, {
//				"id" : "Unix",
//				"name" : "Unix"
//			}, {
//				"id" : "SUSE",
//				"name" : "SUSE"
//			}, {
//				"id" : "RedHat",
//				"name" : "RedHat"
//			}, {
//				"id" : "CentOS",
//				"name" : "CentOS"
//			}];
//	var platformStore = Ext.create('Ext.data.Store', {
//				fields : ['id', 'name'],
//				data : platformData
//			});
	
	var platformStore = Ext.create('Ext.data.JsonStore', {
		fields: ['INAME', 'ICODEVALUE'],
		//autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getScriptPlatformCode.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
	platformStore.on('beforeload', function(store, options) {
			var new_params = {
				gdSwitch :  '0'
			};
			Ext.apply(platformStore.proxy.extraParams, new_params);
	});

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});

	var stateStore = Ext.create('Ext.data.Store', {
				fields : ['id', 'name'],
				data : [{
							"id" : "-10000",
							"name" : "全部"
						}, {
							"id" : "-1",
							"name" : "新建"
						}, {
							"id" : "2",
							"name" : "审核中"
						}, {
							"id" : "1",
							"name" : "已审核"
						}]
			});
	var timeout = new Ext.form.TextField({
		name: 'timeout',
		fieldLabel: '超时(秒)',
		displayField: 'timeout',
		emptyText: '',
		hidden:!scriptTimeoutSwitch,
		labelWidth: 70,
		// padding: '0 5 0 0',
		width : '50%',
		labelAlign : 'right',
		columnWidth: .49,
		value:120
	});
	var paramFlagStore = Ext.create('Ext.data.Store', {
				fields : ['id', 'name'],
				data : [{
							"id" : "0",
							"name" : "无"
						}, {
							"id" : "1",
							"name" : "有"
						}]
			});

//	var visibleTypeStore = Ext.create('Ext.data.Store', {
//				fields : ['id', 'name'],
//				data : [{
//							"id" : "0",
//							"name" : "公有"
//						}, {
//							"id" : "1",
//							"name" : "私有"
//						}]
//			});

	var typeStore = Ext.create('Ext.data.Store', {
				fields : ['sysTypeId', 'sysType'],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

	var categoryStore = Ext.create('Ext.data.Store', {
				fields : ['iid', 'bsName'],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
	var newCategoryStore = Ext.create('Ext.data.Store', {
				fields : ['iid', 'bsName'],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
	});
	/** 命令名称* */
	var cName = new Ext.form.TextField({
		name : 'cNameParam',
		fieldLabel : '命令说明',
		emptyText : '--请输入命令说明--',
		labelWidth : 70,
		// padding : '5',
		width : '35%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	myWhiteListGrid.ipage.moveFirst();
                }
            }
        }
			// value: filter_serviceName

		});

	/** 命令名称* */
	var commd = new Ext.form.TextField({
		name : 'commd',
		fieldLabel : '命令',
		emptyText : '--请输入命令--',
		labelWidth : 65,
		padding : '5',
		// padding : '5',
		width : '35%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	myWhiteListGrid.ipage.moveFirst();
                }
            }
        }
			// value: filter_serviceName
		});

	/** 适应平台* */
	var platform = Ext.create('Ext.form.field.ComboBox', {
				name : 'platformParam',
				// padding : '5',
				labelWidth : 70,
				queryMode : 'local',
				fieldLabel : '适用平台',
				displayField: 'INAME',
		        valueField: 'ICODEVALUE',
				editable : false,
				emptyText : '--请选择适用平台--',
				store : platformStore,
				width : '16%',
				labelAlign : 'right',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	myWhiteListGrid.ipage.moveFirst();
		                }
		            }
		        }
			});

	var state = Ext.create('Ext.form.field.ComboBox', {
				name : 'stateParam',
				// padding : '5',
				labelWidth : 70,
				queryMode : 'local',
				fieldLabel : '状态',
				displayField : 'name',
				valueField : 'id',
				editable : false,
				emptyText : '--请选择状态--',
				store : stateStore,
				width : '12%',
				labelAlign : 'right',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	myWhiteListGrid.ipage.moveFirst();
		                }
		            }
		        }
			});

	/** 类别* */
	var category = Ext.create('Ext.form.field.ComboBox', {
				name : 'categoryParam',
				 padding : '0 7 0 0',
				labelWidth : 70,
				queryMode : 'local',
				fieldLabel : '命令类别',
				displayField : 'bsName',
				valueField : 'iid',
				editable : false,
				// hidden:true,
				emptyText : '--请选择类别--',
				store : categoryStore,
				width : '16%',
				labelAlign : 'right',
				listeners : {
					change : function() {
						type.clearValue();
						type.applyEmptyText();
						type.getPicker().getSelectionModel().doMultiSelect([],
								false);
						if(this.value != "" && this.value != null){
							typeStore.load({
								params : {
									fk : this.value
								}
							});
						}
					},
					afterRender : function(combo) {
						if (filter_category != '-1' && filter_category != '') {
							category.setValue(parseInt(filter_category));
						}
					},
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	myWhiteListGrid.ipage.moveFirst();
			                }
			            }
				}
			});

	categoryStore.on('load', function(store, options) {
				if (filter_category != '-1' && filter_category != '') {
					category.setValue(parseInt(filter_category));
				}
			});

	/** 类型* */
	var type = Ext.create('Ext.form.field.ComboBox', {
				name : 'typeParam',
				//padding : '12',
				labelWidth : 70,
				queryMode : 'local',
				fieldLabel : '命令类型',
				displayField : 'sysType',
				valueField : 'sysTypeId',
				editable : false,
				// hidden:true,
				emptyText : '--请选择类型--',
				store : typeStore,
				width : '15%',//'25.3%'
				labelAlign : 'right',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	myWhiteListGrid.ipage.moveFirst();
		                }
		            }
		        }
			});

	var newCommdName = new Ext.form.TextField({
				name : 'newCommdName',
				fieldLabel : '命令说明',
				afterLabelTextTpl : required,
				labelAlign : 'right',
				displayField : 'newCommdName',
				emptyText : '',
				labelWidth : 70,
				padding : '0 5 0 0',
				columnWidth : 1
			});

	var newCommd = new Ext.form.TextField({
				name : 'newCommd',
				fieldLabel : '命令',
				afterLabelTextTpl : required,
				displayField : 'newCommd',
				emptyText : '',
				labelWidth : 70,
				padding : '0 5 0 0',
				labelAlign : 'right',
				columnWidth : 1
			});

	// 创建命令窗口 类别下拉框
	var newCategory = Ext.create('Ext.form.field.ComboBox', {
				name : 'newCategory',
				labelWidth : 70,
				columnWidth : .5,
				queryMode : 'local',
				afterLabelTextTpl : required,
				fieldLabel : '命令类别',
				padding : '0 5 0 0',
				displayField : 'bsName',
				valueField : 'iid',
				// width:'40%',
				editable : true,
				labelAlign : 'right',
				emptyText : '--请选择类别--',
				store : newCategoryStore,
				listeners : {
					change : function() {
						newType.clearValue();
						newType.applyEmptyText();
						newType.getPicker().getSelectionModel().doMultiSelect(
								[], false);
						if (this.value != "" && this.value != null) {
							typeStore.load({
								params : {
									fk : this.value
								},callback: function(records, options, success) {
							        if (success) {
							            Ext.each(records,function(record){
											var sysTypeId =record.data.sysTypeId; 
											var sysType = record.data.sysType;
											if(sysType =='查询'){
												newType.setValue(sysTypeId);
												return false;
											}
											if(sysType =='默认'){
												newType.setValue(sysTypeId);
												return false;
											}
										});
							        }
							    }
							});
						}
					},
					afterRender : function(combo) {
						if (filter_category != '-1' && filter_category != '') {
							newCategory.setValue(parseInt(filter_category));
						}
					}
				}
			});
	//浦发 默认主机
	newCategoryStore.on('load', function(store, options, success) {
				Ext.each(store.data.items,function(record){
					var iid =record.data.iid; 
					var bsName = record.data.bsName;
					if(bsName =='主机'){
						newCategory.setValue(iid);
						return false;
					} 
				});
	 });
	/** 类型* */
	var newType = Ext.create('Ext.form.field.ComboBox', {
				name : 'newType',
				// padding : '5',
				labelWidth : 70,
				afterLabelTextTpl : required,
				queryMode : 'local',
				fieldLabel : '命令类型',
				displayField : 'sysType',
				valueField : 'sysTypeId',
				editable : false,
				// width:'40%',
				 hidden:true,
				emptyText : '--请选择类型--',
				store : typeStore,
				columnWidth : .49,
				labelAlign : 'right'
			});

	/** 有无参数* */
	var newParamFlag = Ext.create('Ext.form.field.ComboBox', {
				name : 'newParamFlag',
				// padding : '5',
				labelWidth : 70,
				// afterLabelTextTpl: required,
				queryMode : 'local',
				fieldLabel : '有无参数',
				displayField : 'name',
				valueField : 'id',
				editable : false,
				value : '0',
				columnWidth : .5,
				// hidden:true,
				emptyText : '--请选择有无参数--',
				store : paramFlagStore,
			    width : '50%',
				labelAlign : 'right'
			});

	Ext.override(Ext.form.CheckboxGroup, {
		setItemDisable : function(id, disabled, flag) {

			this.items.each(function(item) {
				if (item.getName() != id && flag == 0) {
					item.setDisabled(disabled);
				} else if (item.getName() == id && id == 'Windows' && flag == 1) {
					item.setDisabled(disabled);
				}
			});
		}
	});

	var newPlatform = Ext.create('Ext.form.field.ComboBox', {
				name : 'newPlatform',
				// padding : '5',
				labelWidth : 70,
				queryMode : 'local',
				fieldLabel : '适用平台',
				displayField: 'INAME',
		        valueField: 'ICODEVALUE',
		        multiSelect: true,//启用多选
				editable : false,
				emptyText : '--请选择适用平台--',
				store : platformStore,
				columnWidth : .49,
				listeners: {
					select :function(combobox,records,eOpts){
						if(records.length>1){
							var windowsFlag = false;
							var linuxFlag = false;
							Ext.each(records, function(record) {
								var iname1 = record.data.INAME;
								if(iname1 == 'Windows'){
									windowsFlag = true;
								}
								if(iname1 != 'Windows'){
									linuxFlag = true;
								}
								if(linuxFlag && windowsFlag){
									 Ext.Msg.alert('提示', 'Windows平台和非Windows平台不能同时选择！');
									 newPlatform.clearValue();
									 return;
								}
							});
						}
					}
				}, 
				labelAlign : 'right'
	});
//	var newPlatform = new Ext.form.CheckboxGroup({
//				fieldLabel : '适用平台',
//				width : 580,
//				afterLabelTextTpl : required,
//				checked : true,
//				columns : 3,
//				items : [{
//							name : 'Windows',
//							boxLabel : 'Windows',
//							inputValue : 'Windows',
//							listeners : { // 监听
//								change : function(el, checked) {
//									if (checked) {
//										newPlatform.setItemDisable(el.name,
//												true, 0);
//									} else {
//										newPlatform.setItemDisable(el.name,
//												false, 0);
//									}
//								}
//							}
//						}, {
//							name : 'HP-UX',
//							boxLabel : 'HP-UX',
//							inputValue : 'HP-UX',
//							listeners : { // 监听
//								change : function(el, checked) {
//									if (checked) {
//										newPlatform.setItemDisable('Windows',
//												true, 1);
//									} else {
//										var checkArray = newPlatform
//												.getChecked();
//										if (checkArray.length == 0) {
//											newPlatform.setItemDisable(
//													'Windows', false, 1);
//										}
//									}
//								}
//							}
//						}, {
//							name : 'Unix',
//							boxLabel : 'Unix',
//							inputValue : 'Unix',
//							listeners : { // 监听
//								change : function(el, checked) {
//									if (checked) {
//										newPlatform.setItemDisable('Windows',
//												true, 1);
//									} else {
//										var checkArray = newPlatform
//												.getChecked();
//										if (checkArray.length == 0) {
//											newPlatform.setItemDisable(
//													'Windows', false, 1);
//										}
//									}
//								}
//							}
//						}, {
//							name : 'SUSE',
//							boxLabel : 'SUSE',
//							inputValue : 'SUSE',
//							listeners : { // 监听
//								change : function(el, checked) {
//									if (checked) {
//										newPlatform.setItemDisable('Windows',
//												true, 1);
//									} else {
//										var checkArray = newPlatform
//												.getChecked();
//										if (checkArray.length == 0) {
//											newPlatform.setItemDisable(
//													'Windows', false, 1);
//										}
//									}
//								}
//							}
//						}, {
//							name : 'RedHat',
//							boxLabel : 'RedHat',
//							inputValue : 'RedHat',
//							listeners : { // 监听
//								change : function(el, checked) {
//									if (checked) {
//										newPlatform.setItemDisable('Windows',
//												true, 1);
//									} else {
//										var checkArray = newPlatform
//												.getChecked();
//										if (checkArray.length == 0) {
//											newPlatform.setItemDisable(
//													'Windows', false, 1);
//										}
//									}
//								}
//							}
//						}, {
//							name : 'CentOS',
//							boxLabel : 'CentOS',
//							inputValue : 'CentOS',
//							listeners : { // 监听
//								change : function(el, checked) {
//									if (checked) {
//										newPlatform.setItemDisable('Windows',
//												true, 1);
//									} else {
//										var checkArray = newPlatform
//												.getChecked();
//										if (checkArray.length == 0) {
//											newPlatform.setItemDisable(
//													'Windows', false, 1);
//										}
//									}
//								}
//							}
//						}]
//
//			});

	/** 可见类型* */
//	var newVisibleType = Ext.create('Ext.form.field.ComboBox', {
//				name : 'newVisibleType',
//				// padding : '5',
//				labelWidth : 70,
//				queryMode : 'local',
//				afterLabelTextTpl : required,
//				fieldLabel : '可见类型',
//				displayField : 'name',
//				valueField : 'id',
//				editable : false,
//				hidden : true,
//				emptyText : '--请选择可见类型--',
//				value : '0',
//				store : visibleTypeStore,
//				// width : '50%',
//				columnWidth : .5,
//				labelAlign : 'right'
//
//			});

	var commentInWin = Ext.create('Ext.form.field.TextArea', {
				name : 'commentInWin',
				fieldLabel : '备注',
				// afterLabelTextTpl: required,
				displayField : 'commentInWin',
				emptyText : '请输入备注...',
				labelAlign : 'right',
				labelWidth : 70,
				height : 136,
				columnWidth : 1,
				autoScroll : true
			});

	CreateCommFormForWhiteList = Ext.create('Ext.form.Panel', {
				width : 590,
				height : 350,
				border : false,
				layout : 'anchor',
				cls:'window_border',
				//id : 'CreateCommFormForWhiteList',
				collapsible : false,
				items : [{
					border : false,
					layout : 'column',
					margin : '5',
					items : [newCategory, newType,newPlatform]
						// 类别，类型
					}, {
					border : false,
					layout : 'column',
					margin : '5',
					items : [newCommdName]
						// 命令说明
					}, {
					layout : 'column',
					border : false,
					margin : '5',
					items : [newCommd]
						// 命令
					}, {
					layout : 'column',
					border : false,
					margin : '5',
					items : [newParamFlag,timeout]
						// 有无参数，适用平台
					}, 
					/**{
					layout : 'column',
					border : false,
					margin : '5',
					items : [newPlatform]
						// 使用平台
					},**/ 
					{
					layout : 'column',
					border : false,
					margin : '5',
					items : [commentInWin]
						// 备注
					}]
			});

	var search_form = Ext.create('Ext.form.Panel', {
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				baseCls:'customize_gray_back',
				border : false,
				dockedItems : [{
							xtype : 'toolbar',
							baseCls:'customize_gray_back',  
							border : false,
							dock : 'top',
							items : [commd, cName ]
						}, {
							xtype : 'toolbar',
							baseCls:'customize_gray_back',  
							border : false,
							dock : 'top',
							items : [platform, state,category, type, {
										xtype : 'button',
										text : '查询',
										cls : 'Common_Btn',
										handler : function() {
											myWhiteListGrid.ipage.moveFirst();
//											var filter_cName = cName.getValue();
//											var filter_platform = platform
//													.getValue();
//											var filter_state = state.getValue();
//											var filter_category = category
//													.getValue();
//											var filter_type = type.getValue();

//											filter = {
//												'filter_cName' : filter_cName,
//												'filter_platform' : filter_platform,
//												'filter_state' : filter_state,
//												'filter_category' : filter_category,
//												'filter_type' : filter_type
//											};
										}
									}, {
										xtype : 'button',
										text : '清空',
										cls : 'Common_Btn',
										handler : function() {
											clearQueryWhere();
											//filter = {};
										}
									}]
						}]
			});

	Ext.define('myWhiteListModel', {
				extend : 'Ext.data.Model',
				fields : [{
							name : 'iid',
							type : 'long'
						},{
							name : 'uuid',
							type : 'long'
						}, {
							name : 'serviceName',
							type : 'string'
						}, // 命令描述
						{
							name : 'buss',
							type : 'string'
						}, // 类别显示
						{
							name : 'bussType',
							type : 'string'
						}, // 类型显示
						{
							name : 'timeOut',
							type : 'string'
						}, // 超时时间
						{
							name : 'bussId',
							type : 'int'
						},// 类别码值
						{
							name : 'bussTypeId',
							type : 'int'
						},// 类型码值
						{
							name : 'platForm',
							type : 'string'
						}, // 适用平台
						{
							name : 'icontent',
							type : 'string'
						}, // 命令
						{
							name : 'status',
							type : 'int'
						},// 状态
						{
							name : 'funcDesc',
							type : 'string'
						},// 备注
						{
							name : 'paramFlag',
							type : 'string'
						},// 有无参数
						{
							name : 'visibleType',
							type : 'string'
						}// 可见类型
				// {name : 'execUser' ,type : 'string'}, //执行用户

				// {name : 'sysName' ,type : 'string'},
				// {name : 'bussName' ,type : 'string'},

				// {name : 'bussId' ,type : 'int'},
				// {name : 'bussTypeId' ,type : 'int'},
				// {name : 'scriptType' ,type : 'string'},
				// {name : 'servicePara' ,type : 'string'},

				]
			});

	myWhiteListStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : '30',
				model : 'myWhiteListModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryServiceForMySelfForWhite.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

	myWhiteListStore.on('beforeload', function(store, options) {
				var new_params = {
					bussId : category.getValue(),
					bussTypeId : type.getValue(),
					scriptName : cName.getValue().trim(),
					serviceName : cName.getValue().trim(),
					platform : platform.getValue() == -1 ? '' : platform
							.getValue(),
					scriptType : '',// 不加 iscripttype条件
					scriptStatus : state.getValue(),
					onlyScript : 2,// 只查询白名单 iisflow 白名单为2
					isEmScript : 0,
					content : commd.getValue(),// 命令
					appId : ''
				};

				Ext.apply(myWhiteListStore.proxy.extraParams, new_params);
			});
			
			
	myWhiteListStore.addListener('load', function(me, records, successful, eOpts) {
    	// 选择后构建出的ID数组
        if (chosedIds.length>0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (chosedIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            myWhiteListGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
 
    });

//	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//				store : myWhiteListStore,
//				baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//				dock : 'bottom',
//				displayInfo : true,
//				emptyMsg : '找不到任何记录'
//			});

	var myWhiteListColumns = [{
				text : '序号',
				xtype : 'rownumberer',
				align : 'center',
				width : 50
			}, {
				text : '主键',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			},{
				text : '主键uuid',
				dataIndex : 'uuid',
				width : 40,
				hidden : true
			}, {
				text : '命令说明',
				dataIndex : 'serviceName',
				width : 180,
				renderer : function(value, p, record, rowIndex) {
					return value.replace("<", "&lt");
				}
			}, {
				text : '命令',
				dataIndex : 'icontent',
				width : 180,
				renderer : function(value, p, record, rowIndex) {
					return value.replace("<", "&lt");
				}
			}, {
				text : '有无参数',
				dataIndex : 'paramFlag',
				width : 75,
				align : 'center',
				renderer : function(value, p, record, rowIndex) {
					if (value == 1) {
						return '有';
					} else if (value == 0) {
						return '无';
					} else {
						return '未知';
					}
				}
			}, {
				text : '适用平台',
				dataIndex : 'platForm',
				width : 90
			}, {
				text : 'bussId',
				dataIndex : 'bussId',
				width : 100,
				hidden : true
			}, {
				text : '命令类别',
				dataIndex : 'buss',
				width : 100
			}, {
				text : 'bussTypeId',
				dataIndex : 'bussTypeId',
				width : 100,
				hidden : true
			}, {
				text : '命令类型',
				dataIndex : 'bussType',
				hidden : true,
				width : 100
			},  {
				text : '超时时间(秒)',
				dataIndex : 'timeOut',
				hidden:!scriptTimeoutSwitch,
				width : 100,
				renderer : function(value, p, record, rowIndex) {
					if (value == '-1') {
						return '';
					} else {
						return value;
					}
				}
			},{
				text : '命令状态',
				dataIndex : 'status',
				width : 75,
				align : 'center',
				renderer : function(value, p, record, rowIndex) {
					if (value == -1) {
						return '<font color="#F01024">新建</font>';
					} else if (value == 2) {
						return '<font color="#FFA602">审核中</font>';
					} else if (value == 1) {
						return '<font color="#0CBF47">已审核</font>';
					} else {
						return '<font color="#CCCCCC">未知</font>';
					}
				}
			}, {
				text : '可见类型',
				dataIndex : 'visibleType',
				width : 75,
				align : 'center',
				renderer : function(value, p, record, rowIndex) {
					if (value == 0) {
						return '公有';
					} else if (value == 1) {
						return '私有';
					} else {
						return '未知';
					}
				}
			}, {
				text : '备注',
				dataIndex : 'funcDesc',
				width : 170,
				align : 'center',
				flex : 1
			}, {
				text : '操作',
				xtype : 'actiontextcolumn',
				width : 90,
				items : [{
					text : '编辑',
					iconCls : 'script_edit',
					getClass : function(v, metadata, record) {
						if (record.data.status != -1) {
							return 'x-hidden';
						}
					},
					handler : function(grid, rowIndex) {
						var map = {};
						// myWhiteListGrid.getStore().getAt(rowIndex).get('iid');

						var iid = grid.getStore().data.items[rowIndex].data.iid;
						map['iid'] = iid;
						var uuid = grid.getStore().data.items[rowIndex].data.uuid;
						map['uuid'] = uuid;
						var commdDescValue = grid.getStore().data.items[rowIndex].data.serviceName;
						map['commdDescValue'] = commdDescValue;
						var commdValue = grid.getStore().data.items[rowIndex].data.icontent;
						map['commdValue'] = commdValue;
						var paramFlagValue = grid.getStore().data.items[rowIndex].data.paramFlag;
						map['paramFlagValue'] = paramFlagValue;
						var platformValue = grid.getStore().data.items[rowIndex].data.platForm;
						map['platformValue'] = platformValue;
						var categoryDisValue = grid.getStore().data.items[rowIndex].data.buss;// 类别
																								// 显示值
						map['categoryDisValue'] = categoryDisValue;
						var categoryValue = grid.getStore().data.items[rowIndex].data.bussId;// 类别
																								// 码值
						map['categoryValue'] = categoryValue;
						var typeDisValue = grid.getStore().data.items[rowIndex].data.bussType;// 类型
																								// 显示值
						map['typeDisValue'] = typeDisValue;
						var typeValue = grid.getStore().data.items[rowIndex].data.bussTypeId;// 类型
																								// 码值
						map['typeValue'] = typeValue;
						var visibleTypeValue = grid.getStore().data.items[rowIndex].data.visibleType;
						map['visibleTypeValue'] = visibleTypeValue;
						var commentValue = grid.getStore().data.items[rowIndex].data.funcDesc;
						map['commentValue'] = commentValue;
						var timeOutValue = grid.getStore().data.items[rowIndex].data.timeOut;
						map['timeOutValue'] = timeOutValue==-1?'':timeOutValue;
						// var stateValue = record.data.status;
						editCommWinFunctionForWhiteList(map);
					}
				}
				/***************************************************************
				 * ,{ text:'执行', iconCls: 'execute', getClass : function(v,
				 * metadata, record){ if(record.data.status != 1){ return
				 * 'x-hidden'; } }, handler : function(grid,rowIndex){ var
				 * record=grid.getStore().data.items[rowIndex]; var jsonData =
				 * "["+Ext.JSON.encode(record.data)+"]"; console.log(jsonData);
				 * var myurl = "toExecMyWhite.do";
				 * contentPanel.getLoader().load({ url : myurl, scripts : true,
				 * params : { iid:record.data.iid, serviceName :
				 * record.data.serviceName, parmFlag : record.data.paramFlag,
				 * icontent:record.data.icontent //,jsonData:jsonData },
				 * callback : function(records, operation, success) {
				 * showImg(myurl); } });
				 *  } }
				 **************************************************************/
				]

			}];
	Ext.define('AuditorModel', {
				extend : 'Ext.data.Model',
				fields : [{
							name : 'loginName',
							type : 'string'
						}, {
							name : 'fullName',
							type : 'string'
						}]
			});

	auditorStore_sm = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'AuditorModel',
				proxy : {
					type : 'ajax',
					url : 'getPublishAuditorList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

	/*
	 * auditorStore.on('beforeload', function(store, options) { var new_params = {
	 * bussTypeIds : bussTypeIds }; Ext.apply(auditorStore.proxy.extraParams,
	 * new_params); }); auditorStore.on('load', function( store,records,
	 * successful, eOpts ) { var countNum=0; for( var i = 0; i <
	 * auditorStore.getCount(); i++) {
	 * if(auditorComBox.getValue()==auditorStore.getAt(i).data.loginName) {
	 * countNum++; } }
	 * 
	 * if(countNum==0) { auditorComBox.setValue(''); } });
	 */

	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
				editable : false,
				fieldLabel : "审核人",
				labelWidth : 93,
				// padding: 5,
				store : auditorStore_sm,
				queryMode : 'local',
				labelAlign : 'right',
				// width: 200,
				columnWidth : .98,
				margin : '10 0 0 0',
				displayField : 'fullName',
				valueField : 'loginName',// ,
				listeners : { // 监听
					render : function(combo) {// 渲染
						combo.getStore().on("load", function(s, r, o) {
									combo.setValue(r[0].get('loginName'));// 第一个值
								});
					}
				}
			});

	planTime_sm = Ext.create('Go.form.field.DateTime', {
				fieldLabel : '计划时间',
				format : 'Y-m-d H:i:s',
				labelWidth : 60,
				hidden : true,
				// width:200,
				columnWidth : .98,
				margin : '10 0 0 0'
			});

	pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
				name : 'pubdesc',
				fieldLabel : '发布申请说明',
				emptyText : '',
				labelWidth : 93,
				margin : '10 0 0 0',
				maxLength : 255,
				labelAlign : 'right',
				height : 85,
				columnWidth : .98,
				autoScroll : true
			});

	var levelStore_sm = Ext.create('Ext.data.Store', {
				fields : ['iid', 'scriptLevel'],
				data : [{
							"iid" : "0",
							"scriptLevel" : "白名单"
						}, {
							"iid" : "1",
							"scriptLevel" : "高级风险"
						}, {
							"iid" : "2",
							"scriptLevel" : "中级风险"
						}, {
							"iid" : "3",
							"scriptLevel" : "低级风险"
						}]
			});

	scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
				name : 'scriptLevel',
				labelWidth : 85,
				columnWidth : .97,
				queryMode : 'local',
				fieldLabel : '风险级别',
				margin : '10 0 0 0',
				displayField : 'scriptLevel',
				valueField : 'iid',
				editable : false,
				hidden : true,
				emptyText : '--请选择风险级别--',
				store : levelStore_sm
			});

	Ext.define('AppSysModel1', {
				extend : 'Ext.data.Model',
				fields : [{
							name : 'id',
							type : 'int',
							useNull : true
						}, {
							name : 'name',
							type : 'string'
						}]
			});
	var appSysStore1 = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'AppSysModel1',
				proxy : {
					type : 'ajax',
					url : 'getAppSysList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
	appSysStore1.on('load', function() {
		var ins_rec = Ext.create('AppSysModel1', {
					id : '-1',
					name : '未选系统'
				});
		appSysStore1.insert(0, ins_rec);
			// 字符串转数组
		});
	var appSysObj1 = Ext.create('Ext.form.field.ComboBox', {
		fieldLabel : '应用系统',
		emptyText : '--请选择应用系统--',
		hidden : true,
		multiSelect : true,
		labelWidth : 85,
		columnWidth : .98,
		store : appSysStore1,
		padding : '10 0 0 0',
		displayField : 'name',
		valueField : 'id',
		triggerAction : 'all',
		// editable: false,
		mode : 'local',
		listeners : {
			select : function(combo, records, eOpts) {
				if (records) {
					chosedAppSys = new Array();
					for (var i = 0; i < records.length; i++) {
						chosedAppSys.push(records[i].data.id);
					}
				}

			},
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = Ext.util.Format.trim(e.query);
					combo.store.filterBy(function(record, id) {
						var text = record.get(combo.displayField);
						return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
					});
					combo.expand();
					return false;
				}
			}
		}
	});

	var allUserStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'AuditorModel',
				proxy : {
					type : 'ajax',
					url : 'userdisplay.do?start=0&limit=1000',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
    
	var visiableUser = Ext.create('Ext.form.MultiSelect', {
				fieldLabel : '可见用户',
				emptyText : '--请选择可见用户--',
				labelWidth : 93,
				columnWidth : .98,
				labelAlign : 'right',
				editable : true,
				multiSelect: true,
				store : allUserStore,
				hidden:true,
				padding : '10 0 0 0',
				displayField : 'fullName',
				valueField : 'loginName',
				triggerAction : 'all',
				queryMode: 'local',
				listeners : {
						select : function(combo, records, eOpts) {
							if (records) {
								chosedAppSys = new Array();
								for (var i = 0; i < records.length; i++) {
									chosedAppSys.push(records[i].data.id);
								}
							}
						},
						 beforequery : function(e) {
				                var combo = e.combo;
				                if (!e.forceAll  ) {
				                	var visiableUserV  =  e.query;
									visiableUserV = visiableUserV.replace(/，/ig,",");
//				                	var curvalue =visiableUserV .substring(visiableUserV .lastIndexOf(", ") + 1, visiableUserV .length);
				                	var visiableUserArray = visiableUserV.split(",");
				                	if(visiableUserArray[visiableUserArray.length-1] ==""){
				                		visiableUserArray.pop();
				                	}
//				                    var value = Ext.util.Format.trim(curvalue);
				                	var visiableUserArray2 =[];
								     combo.store.filterBy(function(record, id) {
				                        var text = record.get(visiableUser.displayField);
//				                        visiableUserArray.forEach((tempvisiableUser, index, array) => {
//										     if((text.toLowerCase().indexOf(Ext.util.Format.trim(tempvisiableUser).toLowerCase()) != -1)){
//					                        	visiableUserArray2.push(text);
//					                        	return (text.toLowerCase().indexOf(Ext.util.Format.trim(tempvisiableUser).toLowerCase()) != -1);
//					                        }
//										});
				                         for (var tempvisiableUser of visiableUserArray) {
					                        if((text.toLowerCase().indexOf(Ext.util.Format.trim(tempvisiableUser).toLowerCase()) != -1)){
					                        	visiableUserArray2.push(text);
					                        	return (text.toLowerCase().indexOf(Ext.util.Format.trim(tempvisiableUser).toLowerCase()) != -1);
					                        }
				                         }
				                     });
									  combo.getStore().on("load", function(s, r, o) {
											combo.setValue(visiableUserArray2);// 第一个值
								  	  });
				                    combo.expand();
				                    return false;
				                }
				            } 
			       } 
			});
	var isEMscript = Ext.create('Ext.form.field.Checkbox', {
				boxLabel : '是否应急',
				hidden : true,
				inputValue : 1,
				width : 120,
				margin : '10 0 0 10'
			});

	auditing_form_sm = Ext.create('Ext.form.Panel', {
				width : 600,
				layout : 'anchor',
				buttonAlign : 'center',
				cls:'window_border',
				border : false,
				items : [{
							// layout:'form',
							anchor : '98%',
							padding : '5 0 5 0',
							border : false,
							items : [{
										layout : 'column',
										border : false,
										items : [planTime_sm]
									}, {
										layout : 'column',
										border : false,
										items : [isEMscript, scriptLevelCb_sm]
									}, /*{
										layout : 'column',
										border : false,
										items : [visiableUser]
									}, */{
										layout : 'column',
										border : false,
										items : [auditorComBox_sm]
									}, {
										layout : 'column',
										border : false,
										items : [pubDesc_sm]
									}]
						}]
			});
	function shareServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt <1){
			Ext.MessageBox.alert("提示", "请选择要私有的命令！");
			return ;
		}
		
		var checkMessage="";
		Ext.Ajax.request({
				url : 'checkScriptStatu.do',
				method : 'POST',
				async: false,
				params : {
					scriptiids: chosedIds,
					isWhite:'1'
				},
				success : function(response, request) { 
					checkMessage = Ext.decode(response.responseText).message;
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '校验脚本状态失败！');
				}
			});
		if(checkMessage !=null && checkMessage !=""){
			Ext.Msg.alert('提示', checkMessage);
			return;
		}

		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认私有", "是否私有选中的服务", function(id){
			if(id=='yes'  ) {
				 openShareWin();
			}
		});
	}
	
	function openShareWin(){
     Ext.define('shareClumnModel', {
		extend : 'Ext.data.Model',
		fields : [{
	      name : 'IID',
	      type : 'long'
	    }, {
	      name : 'IFULLNAME',//用户名
	      type : 'string'
	    }, {
	      name : 'ILOGINNAME',//用户登录ID
	      type : 'string'
	    }, {
	      name : 'IGROUPNAME',//组名称 
	      type : 'string'
	    },{
	      name : 'OBJECTNAME',//对象名称 已共享使用 
	      type : 'string'
	    },{ 
	      name : 'ISERVICESNAME',//服务名称 已共享使用 
	      type : 'string'
	    },{
	      name : 'SHARETYPE', // 共享类型 已共享使用
	      type : 'string'
	    },{
	      name : 'IGROUPDES', //组描述
	      type : 'string'
	    }]
	});
	//待共享展示列表 Store
	var shareColumnStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    autoDestroy: true,
	    model: 'shareClumnModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getScriptShareColumnObject.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	 shareColumnStore.on('beforeload', function (store, options) {
		var new_params = {  
				 shareType:shareType,
				 shareed:0, //0代表查询未共享的
				 scriptiids:chosedIds,
				 objectName:objectName.getValue(),
				 isWhiteCommd:'1'
		};
		
		Ext.apply(shareColumnStore.proxy.extraParams, new_params);
	});
	
	shareColumnStore.addListener('load', function(me, records, successful, eOpts) {
        if (chosedShareIds.length>0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (chosedShareIds.indexOf(record.get('IID')) > -1) {
                    chosedRecords.push(record);
                }
            });
            shareGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
 
    });
	
	
    
    //已共享展示列表 Store
	var shareedColumnStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'shareClumnModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getScriptShareColumnObject.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	 shareedColumnStore.on('beforeload', function (store, options) {
		var new_params = {  
				 shareType:shareType,
				 shareed:1, //已共享 参数  1代表查询已共享
				 scriptiids:chosedIds
		};
		
		Ext.apply(shareedColumnStore.proxy.extraParams, new_params);
	});
    
//	var shareColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		    store: shareColumnStore,
//			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//			border:false
//	    });
//	var shareedColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		    store: shareedColumnStore,
//			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//			border:false
//	    });
	var shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }
	                  ];
	var shareedColumns =[{ text: '序号', xtype:'rownumberer', width: 40 },
										{ text: '用户主键',  dataIndex: 'IID',hidden:true},
										{ text: '命令说明',  dataIndex: 'ISERVICESNAME',width:200},
										{ text: '类型',  dataIndex: 'SHARETYPE',width:200},
										{ text: '对象名称',  dataIndex: 'OBJECTNAME',width:200}];
	
	//已共享grid
	shareedGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : shareedColumnStore,
	    border:false,
//	    bbar:shareedColumnStorePageBar,
	    columnLines : true,
	    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    cls:'customize_panel_back',
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    columns : shareedColumns,
		listeners: {
	        select: function( e, record, index, eOpts ){ 
	        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
	        		chosedShareIds.push(record.get('IID'));
	        	}
	        },
	        deselect: function( e, record, index, eOpts ){ 
	        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
	        		chosedShareIds.remove(record.get('IID'));
	        	}
	        }
	    }
	});
	
	
	var shareUserRadio = Ext.create('Ext.form.field.Radio', {
		width: 80,
		name:'shareRadio',
		labelAlign : 'left',
		fieldLabel: '',
	    boxLabel: '用户',
	    inputValue : 1
	});
	var shareGroupRadio = Ext.create('Ext.form.field.Radio', {
		width: 90,
		name:'shareRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '用户组',
		inputValue : 2
	});
	
	var shareAllRadio = Ext.create('Ext.form.field.Radio', {
		width: 100,
		name:'shareRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '所有人',
		inputValue : 0
	});
	 var shareRadioComment =  Ext.create('Ext.form.RadioGroup', {
			name:'shareRadioComment',
			labelAlign : 'left',
			layout: 'column',
			width : '160',
			items:[shareUserRadio,shareGroupRadio/*,shareAllRadio*/],
			listeners:{
	            //通过change触发
	            change: function(g , newValue , oldValue){
	            	if(newValue.shareRadio == 0)//所有人
	            	{
	            		chosedShareIds=[];
	            		chosedShareIds.push(-1);
	            		shareType = 0;
	            		shareGrid.hide();
	            	} 
	            	else if(newValue.shareRadio == 1)//用户
	            	{
	            		 chosedShareIds=[];
	            		 shareType = 1;
	            		 shareColumns=[];
	            		 shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
	            		 shareColumns.push({ text: '主键',  dataIndex: 'IID',hidden:true});
	            		 shareColumns.push({ text: '用户名称',  dataIndex: 'IFULLNAME',width:200});
	            		 shareColumns.push({ text: '用户ID',  dataIndex: 'ILOGINNAME',width:200,flex:1});
				 
			          	 shareGrid.reconfigure(shareColumnStore,shareColumns);
			             shareColumnStore.load();
			             shareGrid.show();
	            	}
	            	else if(newValue.shareRadio == 2)//组
	            	{
	            		  chosedShareIds=[];
	            		  shareType = 2;
	            		  shareColumns=[];
						  shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
	            		  shareColumns.push({ text: '主键',  dataIndex: 'IID',hidden:true});
	            		  shareColumns.push({ text: '用户组名称',  dataIndex: 'IGROUPNAME',width:200});
	            		  shareColumns.push({ text: '用户组描述',  dataIndex: 'IGROUPDES',width:160,flex:1});
				          shareGrid.reconfigure(shareColumnStore,shareColumns);
				          shareColumnStore.load();
				          shareGrid.show();
	            	}
	            }
	        }
		}); 
 
		shareGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
			region: 'center',
			autoScroll: true,
		    store : shareColumnStore,
		    border:false,
//		    bbar:shareColumnStorePageBar,
		    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		    columnLines : true,
		    cls:'customize_panel_back',
		    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		    columns : shareColumns,
			listeners: {
	        select: function( e, record, index, eOpts ){ 
	        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
	        		chosedShareIds.push(record.get('IID'));
	        	}
	        },
	        deselect: function( e, record, index, eOpts ){ 
	        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
	        		chosedShareIds.remove(record.get('IID'));
	        	}
	        }
	    }
		});
			var objectName = new Ext.form.TextField({
				name : 'objectName',
				//fieldLabel : '对象名称',
				emptyText : '--对象名称--',
				//labelWidth : 65,
		//		padding : '5',
				width :'20%',
		        //labelAlign : 'right',
		     //  value: filter_serviceName,
		        listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	if(shareRadioComment.getChecked().length==0){
								Ext.MessageBox.alert("提示", "请选择类型");
								return;
							}else{
								var shareRadio =shareRadioComment.getChecked()[0].inputValue;
								if(shareRadio == 0  ){
									Ext.MessageBox.alert("提示", "只能查询类型为用户、用户组的对象");
									return;
								}
							}
		                	shareGrid.ipage.moveFirst();
		                }
		            }
		        }
			});
			
	     function release(optionState){
			var url = 'scriptService/serviceRelease.do';
		    var message="命令私有成功";
			var	errorMessage="命令私有失败";
			var jsonData = getSelectedJsonData();
			if(jsonData=="[]"){
				Ext.MessageBox.alert("提示", signMessage);
				return ;
			}	
		    Ext.Ajax.request({
			    url : url,
			    method : 'POST',
			    params : {
			  	  jsonData : jsonData,
			  	  optionState:optionState,
			  	  shareType:shareType,
			  	  chosedShareIds:chosedShareIds,
			  	  isflow:0,
			  	  isCustomTask:0
			    },
			    success: function(response, opts) {
			    	var success = Ext.decode(response.responseText).success;
				        //var message = Ext.decode(response.responseText).message;
			        if (success) {
			            Ext.MessageBox.show({
			                title : "提示",
			                msg : message,
			                buttonText: {
			                    yes: '确定'
			                },
			                buttons: Ext.Msg.YES
			              });
			        } else {
			            Ext.MessageBox.show({
			              title : "提示",
			              msg : errorMessage,
			              buttonText: {
			                  yes: '确定'
			              },
			              buttons: Ext.Msg.YES
			            });
			        }
			    	if(optionState==4){
			    		shareWin.close();
			    	}
			    	myWhiteListStore.reload();
			    },
			    failure: function(result, request) {
			    	secureFilterRs(result,"操作失败！");
			    }
		    });
		}	
		var share_form = Ext.create('Ext.ux.ideal.form.Panel', {
			//layout : 'anchor',
			region: 'north',
			cls:'customize_panel_back',
			//buttonAlign : 'center',
			//bodyCls : 'x-docked-noborder-top',
			border : false,
			 dockedItems:{	
				    	xtype: 'toolbar',
				        dock: 'top',
				        border:false,
				        items: [shareRadioComment,'->',objectName,{
					        			xtype : 'button',
										cls : 'Common_Btn',
										text : '查询',
										handler : function(){
											if(shareRadioComment.getChecked().length==0){
											
												Ext.MessageBox.alert("提示", "请选择类型");
															return;
											}else{
													var shareRadio =shareRadioComment.getChecked()[0].inputValue;
													if(shareRadio == 0  ){
														Ext.MessageBox.alert("提示", "只能查询类型为用户、用户组的对象");
														return;
													}
											}
											shareGrid.ipage.moveFirst();
										}
									},{
				        			xtype : 'button',
									cls : 'Common_Btn',
									text : '确定',
									handler : function(){
											if(shareRadioComment.getChecked().length==0){
												Ext.MessageBox.alert("提示", "请选择类型");
															return;
											}
									        var shareRadio =shareRadioComment.getChecked()[0].inputValue;
											if(shareRadio == 0 || shareRadio ==1 || shareRadio ==2){
												if(shareRadio ==0){//所有人
													shareType = 0;
													release(4);
												}else if(shareRadio ==1){//用户
													shareType = 1;
													if(chosedShareIds.length ==0){
														Ext.MessageBox.alert("提示", "请选择要的用户");
														return;
													}else{ 
														release(4);
													}
												}else{ //组 2
													shareType = 2;
													if(chosedShareIds.length ==0){
														Ext.MessageBox.alert("提示", "请选择要的用户组");
														return;
													}else{
														release(4);
													}
												}
											}else{
												Ext.MessageBox.alert("提示", "请选择模式！");
												return;
											}
									}
				        }]
					}
		});
		
		var shareed_form = Ext.create('Ext.ux.ideal.form.Panel', {
			//layout : 'anchor',
			region: 'north',
			cls:'customize_panel_back',
			//buttonAlign : 'center',
			//bodyCls : 'x-docked-noborder-top',
			border : false,
			 dockedItems:{	
				    	xtype: 'toolbar',
				        dock: 'top',
				        border:false,
				        items: ['->',{
					        			xtype : 'button',
										cls : 'Common_Btn',
										text : '取消私有',
										handler : function(){
											if(shareedGrid.getSelectionModel().getSelection().length==0){
												Ext.MessageBox.alert("提示", "请选择要取消的条目");
												return;
											}
											Ext.MessageBox.buttonText.yes = "确定"; 
											Ext.MessageBox.buttonText.no = "取消"; 
											Ext.Msg.confirm("确认取消私有", "是否取消私有", function(id){
												if(id=='yes'  ) {
//													alert(chosedShareIds);
													cancelShare(chosedShareIds);
												}
											});
										}
									}]
					}
		});
		
		function cancelShare(chosedShareIds){
			//获取服务主键
			var record = myWhiteListGrid.getSelectionModel().getSelection();
			var iidStr = [];
			Ext.Array.each (record, function (recordObj)
			{
				iidStr.push(recordObj.get ('iid'));
			});
			Ext.Ajax.request({
			    url : 'scriptService/cancelShare.do',
			    method : 'POST',
			    params : {
			  	  chosedShareIds:chosedShareIds,
			  	  iidStr:iidStr
			    },
			    success: function(response, opts) {
		    		var message = '取消私有成功！';
		    		Ext.MessageBox.show({
		                title : "提示",
		                msg : message,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		    		shareedColumnStore.reload();
		    		myWhiteListStore.reload();
			        
			    },
			    failure: function(result, request) {
			    	secureFilterRs(result,"操作失败！");
			    }
		    });
		}
		
			var pagetab = Ext.create ('Ext.tab.Panel',
			{
			    tabPosition : 'top',
			    cls:'customize_panel_back',
//			    cls:'window_border panel_space_top panel_space_left panel_space_right',
			    region : 'center',
			    activeTab : 0,
			    //width : '100%',
			    width: 700,
                minWidth: 350,
                height: 450,
			    border : false,
//			    autoScroll: true,
			    items : [
						{
							title : '待私有',
							layout:'border',
							items:[share_form,shareGrid]
						},
						{
							title : '已私有',
							layout:'border',
							items:[shareed_form,shareedGrid]
						}
			    ]
			});
		
		
    shareWin = Ext.create('widget.window', {
						                title: '私有列表',
						                closable: true,
						                closeAction : 'destroy',
						                modal: true,
						                width: 750,
						                minWidth: 350,
						                height: 550,
						                layout: {
						                    type: 'border',
						                    padding: 5
						                },
						                items: [pagetab]
						                
		});
		shareWin.show();		
	}
	
	var myWhiteListGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'center',
		autoScroll : true,
		store : myWhiteListStore,
		cls:'customize_panel_back',
		selModel : selModel,
		plugins : [cellEditing],
		//bbar : pageBar,
		ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		border : false,
		columnLines : true,
		padding : grid_space,
		columns : myWhiteListColumns,
		listeners: {
		        select: function( e, record, index, eOpts ){
		        	if( chosedIds.indexOf(record.get('iid'))==-1) {
	            		 chosedIds.push(record.get('iid'));
	            	}
		        },
		        deselect: function( e, record, index, eOpts ){
		        	 if(chosedIds.indexOf(record.get('iid'))>-1) {
	            		chosedIds.remove(record.get('iid'));
	            	}
	            }
		},
		dockedItems : [{
			xtype : 'toolbar',
			items : ['->',{
						text : '创建',
						cls : 'Common_Btn',
						handler : function() {
							createCommWinFunction();
						}
					}, '-', {
						text : '删除',
						cls : 'Common_Btn',
						handler : function() {
							var url = "scriptService/deleteScriptForTestWhiteCommd.do";
							var jsonData = getSelectedJsonData();
							if (jsonData == '') {
								Ext.MessageBox.alert("提示", "请选择要删除的数据！");
								return;
							}

							var ss = selModel.getSelection();
							for (var index = 0; index < ss.length; index++) {
								if (ss[index].data.status != -1
										&& ss[index].data.status != 1) {
									var rowindex = ss[index].index + 1;
									Ext.Msg.alert('提示', "第" + rowindex
													+ "行命令不是新建或已审核状态，不能删除！");
									return;
								}
							}

							Ext.Msg.confirm("提示", "确认删除?", function(btn) {
								if (btn == 'yes') {
									Ext.Ajax.request({
										url : url,
										method : 'POST',
										params : {
											jsonData : jsonData,
											optionState : '1'
										},
										success : function(response, request) {
											var success = Ext
													.decode(response.responseText).success;
											var message = Ext
													.decode(response.responseText).message;
											if (success) {
												Ext.Msg.alert('提示', '删除命令成功！');
												myWhiteListStore.reload();
												return;
											} else {
												myWhiteListStore.reload();
												Ext.Msg.alert('提示', message);
											}
										}
									});
								}
							});
						}
					}, '-', {
						text : '提交',
						cls : 'Common_Btn',
						handler : function() {
							var seledCnt = selModel.getCount();
							if (seledCnt < 1) {
								Ext.MessageBox.alert("提示", "请选择要提交的记录！");
								return;
							}
							if (seledCnt > 1) {
								Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
								return;
							}
							var ss = selModel.getSelection();
							if (ss[0].data.status != -1) {
								Ext.Msg.alert('提示', "只能选择新建命令！");
								return;
							}
							Ext.MessageBox.buttonText.yes = "确定";
							Ext.MessageBox.buttonText.no = "取消";
							Ext.Msg.confirm("确认发布", "是否确认提交该命令", function(id) {
								if (id == 'yes') {
									if (!publishAuditingSMWin) {
										publishAuditingSMWin = Ext.create(
												'widget.window', {
													title : '确认审核信息',
													closable : true,
													closeAction : 'hide',
													modal : true,
													width : 630,
													minWidth : 350,
													height : 320,
													layout : {
														type : 'border',
														padding : 5
													},
													items : [auditing_form_sm],
													dockedItems : [{
														xtype : 'toolbar',
														dock : 'bottom',
														layout : {
															pack : 'center'
														},
														items : [{
															xtype : "button",
															cls : 'Common_Btn',
															text : "确定",
															handler : function() {
																var planTime = planTime_sm
																		.getRawValue();
//																var scriptLevel = scriptLevelCb_sm
//																		.getValue();
																var scriptLevel = 100;
																var publishDesc = pubDesc_sm
																		.getValue();
																var auditor = auditorComBox_sm
																		.getValue();
																var visiUser = visiableUser
																		.getValue();
																if (!visiUser) {
																	visiUser = '';
																}
																var isEmScript = isEMscript
																		.getValue();
																if (isEmScript) {
																	isEmScript = 1;
																} else {
																	isEmScript = 0;
																}
																// if(!planTime)
																// {
																// Ext.Msg.alert('提示',
																// "没有填写计划时间！");
																// return;
																// }
 
																if (!publishDesc) {
																	Ext.Msg
																			.alert(
																					'提示',
																					"没有填写发布申请说明！");
																	return;
																}
																if (publishDesc.length > 255) {
																	Ext.Msg
																			.alert(
																					'提示',
																					"发布申请说明内容长度超过255个字符！");
																	return;
																}
																if (!auditor) {
																	Ext.Msg
																			.alert(
																					'提示',
																					"没有选择审核人！");
																	return;
																}

																var ss = selModel
																		.getSelection();

																var sIds = new Array();
																sIds
																		.push(ss[0].data.iid);
																Ext.Ajax
																		.request(
																				{
																					url : 'scriptPublishAuditingForWhiteCommd.do',
																					method : 'POST',
																					params : {
																						sIds : sIds,
																						planTime : planTime,
																						scriptLevel : scriptLevel,
																						publishDesc : publishDesc,
																						auditor : auditor,
																						flag : 0, // 0-来着个人脚本库
																						isEmScript : isEmScript,
																						appSysIds : chosedAppSys,
																						//visiableUser : visiUser,
																						whiteCommd:"1"
																					},
																					success : function(
																							response,
																							opts) {
																						var success = Ext
																								.decode(response.responseText).success;
																						var message = Ext
																								.decode(response.responseText).message;
																						if (!success) {
																							Ext.MessageBox
																									.alert(
																											"提示",
																											message);
																						} else {
																							Ext.MessageBox
																									.alert(
																											"提示",
																											"请求已经发送到审核人");
																						}
																						myWhiteListStore
																								.load();
																						publishAuditingSMWin
																								.close();

																					},
																					failure : function(
																							result,
																							request) {
																						secureFilterRs(
																								result,
																								"操作失败！");
																						publishAuditingSMWin
																								.close();
																					}
																				});

															}
														}, {
															xtype : "button",
															cls : 'Common_Btn',
															text : "取消",
															handler : function() {
																this
																		.up("window")
																		.close();
															}
														}]
													}]
												});

									}
									publishAuditingSMWin.show();
									auditorStore_sm.load();
									planTime_sm.setValue('');
									scriptLevelCb_sm.setValue('');
									pubDesc_sm.setValue('');
									auditorComBox_sm.setValue('');
									visiableUser.setValue('');
									isEMscript.setValue(0);
									appSysObj1.setValue('');
									chosedAppSys = '';

								}
							});
						}
					},{
							text : '私有',
							cls : 'Common_Btn',
							handler : shareServiceRelease
					}, '-', {
						text : '导入',
						cls : 'Common_Btn',
						handler : function() {
							importExcel();
						}
					}, '-', {
						text : '导出',
						cls : 'Common_Btn',
						handler : function() {
							exportExcel();
						}
					}]
		}]
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "myWhiteList_grid_area",
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				border : false,
				layout : 'border',
				items : [search_form, myWhiteListGrid]
			});

	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
			// myWhiteListGrid.setHeight (contentPanel.getHeight ()-55);
		});

	// myWhiteListGrid.getSelectionModel().on('selectionchange',
	// function(selModel, selections) {
	// myWhiteListGrid.down('#delete').setDisabled(selections.length === 0);
	// });

	 function setMessage(msg){
	 Ext.Msg.alert('提示', msg);
	 }

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
				Ext.destroy(mainPanel);
				if (Ext.isIE) {
					CollectGarbage();
				}
			});
	function clearQueryWhere() {
		cName.setValue('');
		commd.setValue('');
		platform.setValue('');
		state.setValue('');
		category.setValue('');
		type.setValue('');
		typeStore.removeAll();
	}
	function reset() {
		//CreateCommFormForWhiteList.getForm().reset();
		newCommdName.setValue();
		newCommd.setValue();
		newParamFlag.setValue("0");
		newPlatform.setValue();
		commentInWin.setValue();
		timeout.setValue(120);
		newCategoryStore.reload();
	}
 

	// 从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key, jsonObj) {
		//var eValue = eval('jsonObj.' + key);
		return jsonObj['' + key + ''];
	}

	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedJsonData() {
		var commdIdList = myWhiteListGrid.getSelectionModel().getSelection();
		if (commdIdList.length < 1) {
			return '';
		}
		var jsonData = "[";
		for (var i = 0, len = commdIdList.length; i < len; i++) {
			if (i == 0) {
				jsonData = jsonData + '{"iid":"'
						+ parsIIDJson('iid', commdIdList[i].data) + '"}';
			} else {
				jsonData = jsonData + "," + '{"iid":"'
						+ parsIIDJson('iid', commdIdList[i].data) + '"}';
			}
		}
		jsonData = jsonData + "]";
		return jsonData;
	}

	/**
	 * title:标题
	 * 
	 */
	function createCommWinFunction() {
		reset();
		var iid_f = -1;
		if (!CreateCommWinForWhiteList) {

			CreateCommWinForWhiteList = Ext.create('widget.window', {
				// id:'CreateCommWinForWhiteList',
				title : '创建命令',
				resizable : false,
				draggable : false,// 禁止拖动
				modal : true,
				// closable: true,
				width : 640,
				minWidth : 350,
				height : 455,
				layout : {
					type : 'border',
					padding : 5
				},
				closeAction : 'destory',
				listeners : {
						close : function(g, opt) {
							reset();
						}
				},
				items : [CreateCommFormForWhiteList],
				buttonAlign : 'center',
				buttons : [{
					xtype : "button",
					text : "保存",
					handler : function() {
						var categoryValue_f = newCategory.getValue();// 类别 码值
						var sysName = newCategory.getRawValue(); // 类别 显示值
						var typeValue_f = newType.getValue();// 类型 码值
						var typeDisValue = newType.getRawValue();// 类型 显示值
						var commdDescValue_f = newCommdName.getValue(); // 命令说明
						var commdValue_f = newCommd.getValue();// 命令
						var paramFlagValue_f = newParamFlag.getValue();// 有无参数
						// var platformValue_f = newPlatform.getValue();// 适用平台
						// if(tempForm.id ==
						// 'CreateCommFormForWhiteList'){//创建命令的FORM
						// var platformValue_f = []; // 适用平台
						// var cbitems = newPlatform.getChecked();
						// for (var i = 0; i < cbitems.length; i++) {
						// platformValue_f.push(cbitems[i].inputValue);
						// }
						// }else if(tempForm.id == 'editCommForm'){//编辑命令的FORM
						// var platformValue_f = comBoxPlatform.getValue();
						// }
						
//						var platformValue_f = []; // 适用平台
//						var cbitems = newPlatform.getChecked();
//						for (var i = 0; i < cbitems.length; i++) {
//							platformValue_f.push(cbitems[i].inputValue);
//						}
						var platformValue_f = newPlatform.getValue();

						var visibleTypeValue_f = 0; // 可见类型
						var commentValue_f = commentInWin.getValue();// 备注
						var timeoutValue = timeout.getValue()==null?-1:timeout.getValue();
						if(timeoutValue.length > 5){
							Ext.Msg.alert('提示', "超时时间不得超过5位！");
							return;
						}
						if(timeoutValue){
							if (!checkIsInteger(timeoutValue)) {
								setMessage('请输入数字类型的超时时间！');
								return;
							}
						}
						if (categoryValue_f == null || categoryValue_f == '') {
							Ext.Msg.alert('提示', "请选择类别！");
							return;
						}
						if (typeValue_f == null || typeValue_f == '') {
							Ext.Msg.alert('提示', "请选择类型！");
							return;
						}
						if (commdDescValue_f == null || commdDescValue_f == '') {
							Ext.Msg.alert('提示', "命令说明不能为空！");
							return;
						}
						if (commdValue_f == null || commdValue_f == '') {
							Ext.Msg.alert('提示', "命令不能为空！");
							return;
						}
						if (platformValue_f.length == 0) {
							Ext.Msg.alert('提示', "请选择适用平台！");
							return;
						}
						// if(visibleTypeValue_f==null || visibleTypeValue_f
						// =='' ){
						// Ext.Msg.alert('提示',"请选择可见类型！");
						// return ;
						// }
						Ext.Ajax.request({
							url : 'saveWhiteList.do',
							method : 'POST',
							sync : true,
							params : {
								iid : iid_f,
								sysId : categoryValue_f,// 类别 码值
								sysName : sysName, // 类别 显示值
								bussType : typeDisValue, // 类型 显示值
								bussTypeId : typeValue_f, // 类型 码值
								scriptName : commdDescValue_f,
								serverName : commdDescValue_f,
								// commdDescValueParm : commdDescValue_f,
								content : commdValue_f,// 命令
								paramFlagParm : paramFlagValue_f,// 有无参数
								usePlantForm : platformValue_f,// 适用平台
								visibleTypeParm : visibleTypeValue_f,// 可见类型
								funcDesc : commentValue_f,// 备注
								checkRadio : '',
								ignoreTipCmd : 0,
								params : '[]',
								timeout:timeoutValue
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								var commdExits = Ext
										.decode(response.responseText).commdExits;
								if (commdExits) {

									Ext.Msg.alert('提示', '命令重复！');
									return;
								}
								if (success) {
//									myWhiteListStore.reload();
									myWhiteListGrid.ipage.moveFirst();
									Ext.Msg.alert('提示', '保存成功！');
									CreateCommWinForWhiteList.close();
									// this.up("window").close();
									return;
								} else {
									var hasScreenKeyWord = Ext
											.decode(response.responseText).hasScreenKeyWord;
									var hasTipKeyWord = Ext
											.decode(response.responseText).hasTipKeyWord;
									if (hasScreenKeyWord || hasTipKeyWord) {
										Ext.Msg.alert('提示', "命令中有关键命令，无法保存！");
										return;
									}
									Ext.Msg.alert('提示', '保存失败！');
									return;
								}
							},
							failure : function(result, request) {
								secureFilterRs(result, "保存失败！");
							}
						});
					}
				}, {
					xtype : "button",
					text : "重置",
					handler : function() {
						reset();
					}
				}, {
					xtype : "button",
					text : "取消",
					handler : function() {
						CreateCommWinForWhiteList.close();
					}
				}]
			});
		}
		newCategoryStore.load({scope: this,
          callback:function(records, operation, success){
			Ext.each(records,function(record){
					var iid =record.data.iid; 
					var bsName = record.data.bsName;
					if(bsName =='主机'){
						newCategory.setValue(iid);
						return false;
					} 
				});
		}});
		CreateCommWinForWhiteList.show();
	}
	function exportExcel() {
		if (chosedIds.length <= 0) {
			Ext.Msg.alert('提示', '请选择要操作的行！');
			return;
		}
		var iidStr = "";
		Ext.Array.each(chosedIds, function(tempId) {
					iidStr += "," +tempId;
				});
		iidStr = iidStr.substr(1);
		window.location.href = 'exportWhiteList.do?iidStr=' + iidStr;
	}

	function importExcel() {
		var uploadWindows;
		var uploadForm
		uploadForm = Ext.create('Ext.form.FormPanel', {
					border : false,
					items : [{
								xtype : 'filefield',
								name : 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
								fieldLabel : '选择文件',
								labelWidth : 80,
								anchor : '90%',
								// labelAlign: 'right',
								margin : '10 10 0 40',
								buttonText : '浏览'
							}],
					buttonAlign : 'center',
					buttons : [{
								text : '确定',
								handler : upExeclData
							}, {
								text : '取消',
								handler : function() {
									uploadWindows.close();
								}
							},
							{
								text: '下载模板',
								handler: function() {
									window.location.href = 'downloadSsTemplate.do?fileName=7';
								}
							}]
				});
		uploadWindows = Ext.create('Ext.window.Window', {
					title : '导入命令文件',
					layout : 'fit',
					height : 200,
					width : 600,
					modal : true,
					// autoScroll : true,
					items : [uploadForm],
					listeners : {
						close : function(g, opt) {
							uploadForm.destroy();
						}
					}
				});
		uploadWindows.show();
		function upExeclData() {
			var form = uploadForm.getForm();
			var hdupfile = form.findField("fileName").getValue();
			if (hdupfile == '') {
				Ext.Msg.alert('提示', "请选择文件...");
				return;
			}
			uploadTemplate(form);
		}
		function uploadTemplate(form) {
			if (form.isValid()) {
				form.submit({
					url : 'importWhiteList.do',
					success : function(form, action) {
						var sumsg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示', sumsg);
						uploadWindows.close();
						myWhiteListGrid.ipage.moveFirst();
						return;
					},
					failure : function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
//						var mess = Ext.create('Ext.window.MessageBox', {
//									minHeight : 110,
//									minWidth : 500,
//									resizable : false
//								});
						Ext.Msg.alert('提示', msg);
						return;
					}
				});
			}
		}
	}

});