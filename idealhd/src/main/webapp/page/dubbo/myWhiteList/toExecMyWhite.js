Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
    var coatId;
	var itemsPerPage;
	var scriptType;
	var execContentIid;
	var execContentUuid;
 	var upldWin;
 	var upLoadformPane = '';
 	var chosedAgentWindow;
 	var equiSelectedIds="";
 	var idSh = idSh_v;
 	var parmFlag = parmFlag_v;
 	var editingChosedAgentIds = editingChosedAgentIds_v;
 	var platformArray = new Array();
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
    var selModel1=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
	 
	/**命令名称**/
//	var cName = new Ext.form.TextField({
//		name : 'cNameParam',
//		fieldLabel : '命令说明',
//		editable : false,
//		readOnly : true,
//		//emptyText : '--请输入命令说明--',
//		labelWidth : 70,
//		value:serviceNameSh,
//		width :'33%',
//        labelAlign : 'right'
//        //value: filter_serviceName
//        
//	}); 
	
	/**主机名称**/
//	var hostName = new Ext.form.TextField({
//		id:'hostNameId',
//		fieldLabel : '主机名称',
//		//emptyText : '--请输入主机名--',
//		labelWidth : 100,
//		width :'25%',
//        labelAlign : 'right',
//        value: ''
//        
//	});
	
	var getHostNameStore = Ext.create('Ext.data.JsonStore', {
				// fields : [ 'ctype' ],
				fields : [ 'hostName', 'hostName' ],
				autoLoad : true,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getHostNameForWhiteListSPDB.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
	});
	
	getHostNameStore.on ('beforeload', function (store, options)
	{ 
		 var new_params = {
				ienv_type : 1,//生产
				state : 0//状态
         };
		Ext.apply (getHostNameStore.proxy.extraParams, new_params);
	});
//	var hostName = Ext.create('Ext.form.field.ComboBox', {
//				id:'hostNameId',
//				store : getHostNameStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : '主机名称',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'hostName',
//				valueField : 'hostName',
//				labelWidth : 100,
//				labelAlign : 'right',
//				value:hostNameForWhite,
//				emptyText :'--请选择主机名称--',
//				triggerAction : "all"
//     });
       var hostName = Ext.create('Ext.ux.ideal.form.ComboBox', {
			iname : 'hostName',
			displayField : 'hostName',
			valueField : 'hostName',
			labelWidth : 100,
			width :'20%',
			istore : getHostNameStore,
			labelAlign : 'right',
			forceSelection : false,
			value:hostNameForWhite,
			emptyText : "--请选择计算机名--"
//			listeners: {
//	            specialkey: function(field, e){
//	                if (e.getKey() == e.ENTER) {
//	                	myWhiteListGrid.ipage.moveFirst();
//	                }
//	            }
//	        }
		});

	
//	var area =new Ext.form.TextField({
//		name : 'area',
//		fieldLabel : '所属区域',
//		//emptyText : '--请输入主机名--',
//		labelWidth : 70,
//		width :'25%',
//        labelAlign : 'right'
//        //value: filter_serviceName
//        
//	}); 
	
//	var userStore1 = Ext.create('Ext.data.JsonStore', {
//		fields : [ 'centername' ],
//		autoDestroy : true,
//		autoLoad : true,
//		proxy : {
//			type : 'ajax',
//			url : 'centerList.do',
//			reader : {
//				type : 'json',
//				root : 'centerlist'
//			}
//		}
//	});
//	var area = Ext.create('Ext.form.field.ComboBox', {
//		name : 'area',
//		fieldLabel : '所属区域',
//		store : userStore1,
//		queryMode : 'local',
//		labelWidth : 100,
//		width :'25%',
//		labelAlign : 'right',
//		hidden:true,
//		forceSelection : true, // 要求输入值必须在列表中存在
//		typeAhead : true, // 允许自动选择
//		displayField : 'centername',
//		valueField : 'centername',
//		triggerAction : "all",
//		emptyText : "--请选择数据中心--"
//	});
	
	
//	var serverIp = new Ext.form.TextField({
//		name : 'serverIp',
//		fieldLabel : 'IP地址',
//		//emptyText : '--请输入主机名--',
//		labelWidth : 100,
//		hidden:true,
//		width :'25%',
//        labelAlign : 'right'
//        //value: filter_serviceName
//        
//	}); 
	
 
   var getIpStore = Ext.create('Ext.data.JsonStore', {
				// fields : [ 'ctype' ],
				fields : [ 'ip', 'ip' ],
				autoLoad : true,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getIpForWhiteListSPDB.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
	});
	
	getIpStore.on ('beforeload', function (store, options)
	{ 
		 var new_params = {
				ienv_type : 1,//生产
				state : 0//状态
         };
		Ext.apply (getIpStore.proxy.extraParams, new_params);
	});
//	var agentIp = Ext.create('Ext.form.field.ComboBox', {
//				store : getIpStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : 'IP',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'ip',
//				valueField : 'ip',
//				labelWidth : 100,
//				labelAlign : 'right',
//				value :ipForWhite,
//				emptyText :'--请选择IP--',
//				triggerAction : "all"
//     });
     
     var agentIp = Ext.create('Ext.ux.ideal.form.ComboBox', {
			iname : 'ip',
			displayField : 'ip',
			valueField : 'ip',
			labelWidth : 100,
			width :'20%',
			istore : getIpStore,
			labelAlign : 'right',
			forceSelection : false,
			value:ipForWhite,
			emptyText : "--请选择IP--"
			 
		});
     
	
//	var agentIp = new Ext.form.TextField({
//		fieldLabel : 'IP',
//		//emptyText : '--请输入主机名--',
//		labelWidth : 100,
////		padding : '5',
//		width :'25%',
//        labelAlign : 'right',
//        value: ''
////        listeners : {
////        	'blur' : function(e,ev,opt){
////        		 if(e.getValue() != '' && !isYesIp(e.getValue())){
////        		 	Ext.Msg.alert('提示', "IP输入不合法");
////        		 	e.setValue();
////        		 	return;
////        		 }
////        	}
////        }
//	}); 
	
     
     var getsysAdminStore = Ext.create('Ext.data.JsonStore', {
				// fields : [ 'ctype' ],
				fields : [ 'sysAdmin', 'sysAdmin' ],
				autoLoad : true,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getSysAdminForWhiteListSPDB.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
	});

	var getsysAdminAStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'sysAdminA', 'sysAdminA' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdminAForWhiteListSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var getsysAdminBStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'sysAdminB', 'sysAdminB' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdminBForWhiteListSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var getBelongIpStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'belongIp', 'belongIp' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getBelongIpForWhiteListSPDB.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	getsysAdminStore.on ('beforeload', function (store, options)
	{ 
		 var new_params = {
				ienv_type : 1,//生产
				state : 0//状态
         };
		Ext.apply (getsysAdminStore.proxy.extraParams, new_params);
	});
//	var sysAdmin1 = Ext.create('Ext.form.field.ComboBox', {
//				id:'sysAdminId1',
//				store : getsysAdminStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : '系统管理员',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'sysAdmin',
//				valueField : 'sysAdmin',
//				labelWidth : 100,
//				labelAlign : 'right',
//				value : sysAdminForWhite,
//				emptyText :'--请选择系统管理员--',
//				triggerAction : "all"
//     });
  
     var sysAdmin1 = Ext.create('Ext.ux.ideal.form.ComboBox', {
			iname : 'sysAdmin',
			displayField : 'sysAdmin',
			valueField : 'sysAdmin',
			fieldLabel : '系统管理员',
			labelWidth : 100,
			width :'25%',
			istore : getsysAdminStore,
			labelAlign : 'right',
			forceSelection : false,
			value:sysAdminForWhite,
			emptyText : "--请选择系统管理员--"
		});


	var sysAdminA = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'sysAdminA',
		displayField : 'sysAdminA',
		valueField : 'sysAdminA',
		labelWidth : 100,
		width :'20%',
		istore : getsysAdminAStore,
		labelAlign : 'right',
		forceSelection : false,
		//value:sysAdminForWhite,
		emptyText : "--请选择系统管理员A角--"
	});

	var sysAdminB = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'sysAdminB',
		displayField : 'sysAdminB',
		valueField : 'sysAdminB',
		labelWidth : 100,
		width :'20%',
		istore : getsysAdminBStore,
		labelAlign : 'right',
		forceSelection : false,
		//value:sysAdminForWhite,
		emptyText : "--请选择系统管理员B角--"
	});

	var belongIp = Ext.create('Ext.ux.ideal.form.ComboBox', {
		iname : 'belongIp',
		displayField : 'belongIp',
		valueField : 'belongIp',
		labelWidth : 100,
		width :'20%',
		istore : getBelongIpStore,
		labelAlign : 'right',
		forceSelection : false,
		emptyText : "--请选择所属网段--"
	});
	
//	var sysAdmin1 = new Ext.form.TextField({
//		id:'sysAdminId1',
//		fieldLabel : '系统管理员',
//		labelWidth : 100,
//		width :'25%',
//        labelAlign : 'right',
//        value: ''
//        
//	}); 
	
//	var appAdmin4 = new Ext.form.TextField({
//		fieldLabel : '应用管理员',
//		labelWidth : 70,
//		width :'25%',
//        labelAlign : 'right',
//        value: ''
//	}); 
	
	var getSystemNameStore = Ext.create('Ext.data.JsonStore', {
				// fields : [ 'ctype' ],
				fields : [ 'systemName', 'systemName' ],
				autoLoad : true,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getSystemNameForWhiteListSPDB.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
	});
	
	getSystemNameStore.on ('beforeload', function (store, options)
	{ 
		 var new_params = {
				ienv_type : 1,//生产
				state : 0//状态
         };
		Ext.apply (getSystemNameStore.proxy.extraParams, new_params);
	});
//	var system4 = Ext.create('Ext.form.field.ComboBox', {
//				store : getSystemNameStore,
//				queryMode : 'local',
//				width :'25%',
//				fieldLabel : '信息系统名称',
//				forceSelection : false, // 要求输入值必须在列表中存在
//				typeAhead : true, // 允许自动选择
//				displayField : 'systemName',
//				valueField : 'systemName',
//				labelWidth : 100,
//				labelAlign : 'right',
//				value:systemInfoForWhite,
//				emptyText :'--请选择信息系统名称--',
//				triggerAction : "all"
//     });
     
     var system4 = Ext.create('Ext.ux.ideal.form.ComboBox', {
			iname : 'systemName',
			displayField : 'systemName',
			valueField : 'systemName',
			labelWidth : 100,
			width :'20%',
			istore : getSystemNameStore,
			labelAlign : 'right',
			forceSelection : false,
			value:systemInfoForWhite,
			emptyText : "--请选择信息系统名称--",
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myWhiteListGrid.ipage.moveFirst();
	                }
	            }
	        }
	 });
     
//	var system4 = new Ext.form.TextField({
//		fieldLabel : '信息系统名称',
//		//emptyText : '--请输入主机名--',
//		labelWidth : 100,
//		width :'25%',
//        labelAlign : 'right',
//        value: ''
//	});
	
	var agentPort = new Ext.form.NumberField({
		fieldLabel : 'Agent端口',
		//emptyText : '--请输入主机名--',
		labelWidth : 100,
		width :'33.3%',
        labelAlign : 'right',
        allowDecimals : false,
        //value: '',
        allowNegative : false,
        maxValue : 65535,
        maxText:'值太大',
        minValue : 1,
        minText:'值太小'
	}); 
	
	/**应用类型* */
	var ctStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'ctid', 'ctype' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'ctList.do',
			reader : {
				type : 'json',
				root : 'ctlist'
			}
		}
	});
	
	/** 应用类型条件 */
	var applicationType = Ext.create('Ext.form.field.ComboBox', {
		name : 'applicationType',
		fieldLabel : '应用类型',
		store : ctStore,
		hidden:true,
		queryMode : 'local',
		labelWidth : 100,
		emptyText : '---请选择应用类型---',
		width : '33.3%',
		labelAlign : 'right',
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'ctype',
		valueField : 'ctid',
		listeners : {
			'beforequery' : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var input = e.query;
					// 检索的正则
					var regExp = new RegExp(".*" + input + ".*");
					// 执行检索
					combo.store.filterBy(function(record, id) {
						// 得到每个record的项目名称值
						var text = record.get(combo.displayField);
						return regExp.test(text);
					});
					combo.expand();
					return false;
				}
			}
		}
	});
	
	
	/**中间件类型* */
	//暂时用信息采集过来的
//	var cmStore = Ext.create('Ext.data.JsonStore', {
//		fields : [ 'cmid', 'cname' ],
//		autoDestroy : true,
//		autoLoad : true,
//		proxy : {
//			type : 'ajax',
//			url : 'cmList.do',
//			reader : {
//				type : 'json',
//				root : 'cmlist'
//			}
//		}
//	});
	 //中间件类型
//	var middleType = new Ext.form.TextField({
//		name : 'middleType',
//		fieldLabel : '中间件类型',
//		editable : true,
//		labelWidth : 100,
//		width :'25%',
//        labelAlign : 'right'
//	});
	 
	
	//暂时用信息采集过来的
	/** 中间件条件  
	var middleType = Ext.create('Ext.form.field.ComboBox', {
		name : 'middleType',
		fieldLabel : '中间件类型',
		store : cmStore,
		queryMode : 'local',
		emptyText : '---请选择中间件---',
		labelWidth : 100,
		hidden:false,
		width : '33.3%',
		labelAlign : 'right',
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'cname',
		valueField : 'cmid',
		editable : false,
		listeners : {
			'beforequery' : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var input = e.query;
					// 检索的正则
					var regExp = new RegExp(".*" + input + ".*");
					// 执行检索
					combo.store.filterBy(function(record, id) {
						// 得到每个record的项目名称值
						var text = record.get(combo.displayField);
						return regExp.test(text);
					});
					combo.expand();
					return false;
				}
			}
		}
	});*/
	

	//命令
//	var content = new Ext.form.TextField({
//		name : 'content',
//		fieldLabel : '命令',
//		//readOnly : true,
//		editable : true,
//		//emptyText : '--请输入命令说明--',
//		labelWidth : 70,
//		value:contentSh,
//		width :'33.3%',
//        labelAlign : 'right'
//        //value: filter_serviceName
//        
//	});
	
	Ext.define('myWhiteListModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		     {name : 'iid'         ,type : 'long'}, 
		     {name : 'uuid'         ,type : 'string'}, 
		    //{name : 'serviceName' ,type : 'string'}, //命令描述
		    //{name : 'buss'    ,type : 'string'}, //类别显示
		    //{name : 'bussType'    ,type : 'string'}, //类型显示
		    //{name : 'bussId'    ,type : 'int'},//类别码值
		    // {name : 'bussTypeId'    ,type : 'int'},//类型码值
		    //{name : 'platForm',type : 'string'}, //适用平台
		    {name : 'icontent'     ,type : 'string'},  //命令
		    //{name : 'status'     ,type : 'int'},//状态
		    {name : 'funcDesc' ,type : 'string'},//备注
		    {name : 'servicesname' ,type : 'string'},//命令说明
			{name : 'paramFlag'    ,type : 'string'}//有无参数
			//{name : 'visibleType'  ,type : 'string'}//可见类型 
	    ]
	});
	
		var contentListStore = Ext.create('Ext.data.Store', {
			autoLoad : false,
			autoDestroy : true,
			//fields : [ 'iid', 'icontent' ],
			//pageSize : '30',
		   model : 'myWhiteListModel',
		   proxy : {
				type : 'ajax',
				url : 'scriptService/queryCommdForExecWhite.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			},
	       listeners: {
	       		load: function () {
						            var k, f, p, ss, repeat = [], state = {};
							        this.each(function (r) {
							                k = r.get('icontent');
							                f  = r.get('funcDesc');
							                p = r.get('paramFlag');
							                ss = k+'#@'+f+'#@'+p;
							                if (state[ss]) {
							                	repeat.push(r);
							                } else {
							                	state[ss] = true;
							                }
							         });
							         this.remove(repeat);
			    }
           }
		});

		var cName = new Ext.form.TextField({
			name : 'cmdDesName',
			fieldLabel : '命令说明',
			readOnly:true,
			labelWidth : 70,
			// padding : '5',
			width : '20%',
			labelAlign : 'right',
			listeners : {
				render: function (p) {
					p.getEl().on('mouseover', function(p1) {
						Ext.QuickTips.init();
						Ext.QuickTips.register({
							target: p.el,
							text: p.getValue()});
					});
				}
			}
		});

		var content = Ext.create('Ext.form.field.ComboBox', {
		id:'content',
		name : 'content',
		labelWidth : 35,
		queryMode : 'local',
		fieldLabel : '命令',
		editable : true,
		displayField : 'icontent',
		valueField : 'iid',

		emptyText : '--请选择命令--',
		store : contentListStore,
		width : '20%',
        labelAlign : 'left',
         
		listeners: { //监听 
			select : function(combo, records, eOpts){
				parmFlag = records[0].data.paramFlag;
				if(parmFlag != 0){ //有参数
		  				execparams.show();
				}else{
						execparams.hide();
				}
				execContentIid = records[0].data.iid;
				execContentUuid= records[0].data.uuid;
				var servicesname= records[0].data.servicesname; //命令说明
				cName.setValue(servicesname);
				var ircontent = records[0].raw.icontent.replace(/&lt/g, "<");//转义
				combo.setRawValue(ircontent);
			}, 
			blur:function(combo, records, eOpts){
				var displayField =content.getRawValue();
				if(!Ext.isEmpty(displayField)){
					//判断输入是否合法标志，默认false，代表不合法
					var flag = false;
					//遍历下拉框绑定的store，获取displayField
					contentListStore.each(function (record) {
						//获取数据集里的 icontent 进行比较 如果有匹配的，那么标志为true，代表合法
					    var data_icontent = record.get('icontent').replace(/&lt/g, "<");//转义 &lt 为 <
					    if(data_icontent == displayField){
					    	flag =true;
					    	execContentIid = record.get('iid');
					    	combo.setValue(execContentIid);
					    }
					});
					if(!flag){
					 	Ext.Msg.alert('提示', "输入的命令非法");
					 	content.setValue("");
					 	return;
					}
				}
			},
			beforequery: function(e) {
//                var combo = e.combo;
//                if (!e.forceAll) {
//                    var value = Ext.util.Format.trim(e.query);
//                    combo.store.filterBy(function(record, id) {
//                        var text = record.get(combo.displayField);
//                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
//                    });
//                    combo.expand();
//                    return false;
//                }
				var combo = e.combo;    
			    if(!e.forceAll){    
			        var input = e.query;    
			        // 检索的正则  
			        var regExp = new RegExp(".*" + input + ".*", 'i');  
			        // 执行检索  
			        combo.store.filterBy(function(record,id){    
			            // 得到每个record的项目名称值  
			            var text = record.get(combo.displayField);    
			            return regExp.test(text);   
			        });  
			        combo.expand();    
			        return false;  
			    }
            },
		    expand : function(combo, records, eOpts){
//            	var selectDatas = myWhiteListGrid.getSelectionModel().getSelection();
//				if(platformArray.length == 0){
//					Ext.Msg.alert('提示', "请选择Agent服务器！");
//					//content.setValue('');
//					return;
//				}
            	if(editingChosedAgentIds.length == 0){
            		Ext.Msg.alert('提示', "请选择Agent服务器！");
            		return;
            	}
            	
				//var osTypeValue =  selectDatas[0].data.osTypeAgentinfo;
            	
				//if(osTypeValue.toUpperCase().indexOf("WIN") != -1){
            	if(editingChosedAgentIds.indexOf('WIN') != -1 || platformArray.indexOf('Windows') != -1){
					scriptType ="bat";
				}else{
					scriptType ="sh";
				}
				
				//var platformArray = new Array();
//			    $.each(selectDatas, function(i,v){
//			    		var value =  v.data.osTypeAgentinfo;
//			    	    if (platformArray.indexOf(value) == -1) {
//		                    platformArray.push(value);
//		                }
//				});
				var platforms = "";
				Ext.Array.each(platformArray, function(v) {
					platforms = platforms+"'"+v+"',";	
				});
				
				contentListStore.load({
					params: {
						scriptType: scriptType,
						onlyScript: '2',
						scriptStatus: '1', //已提交的
						platform : platforms.slice(0,-1)
					}
				});
				
            }
//			change : function(combo, records, eOpts){
//				var selectDatas = myWhiteListGrid.getSelectionModel().getSelection();
//				if(selectDatas.length == 0){
//					Ext.Msg.alert('提示', "请选择Agent服务器！");
//					//content.setValue('');
//					return;
//				}
//				var osTypeValue =  selectDatas[0].data.osType;
//				var scriptType;
//				if(osTypeValue.indexOf("Linux") != -1){
//					scriptType ="sh";
//				}else{
//					scriptType ="bat";
//				}
//				contentListStore.load({
//					params: {
//						scriptType: scriptType
//					}
//				});
//			}
		}
	});
 
	
	
	
	//执行用户
	var execuser = new Ext.form.TextField({
		name : 'execuser',
		fieldLabel : '用户',
		editable : false,
		//emptyText : '--请输入命令说明--',
		labelWidth : 40,
		//value:loginuser,
		width :'10%',
        labelAlign : 'right'
        //value: filter_serviceName
        
	});
	//参数
	var execparams = new Ext.form.TextField({
		name : 'execparams',
		fieldLabel : '参数',
		emptyText : '--请输入执行参数--',
		labelWidth : 40,
		width :'14.5%',
        labelAlign : 'right',
        hidden : true,
        initEvents : function() {  
		    var keyPress = function(e){  
		        var blockchars = "'";          //限制为不允许输入单引号
		        var c = e.getCharCode();
		        if(blockchars.indexOf(String.fromCharCode(c)) != -1){  
		            e.stopEvent();  
		        }
		    };  
		    this.el.on("keypress", keyPress, this);  
		}
        //value: filter_serviceName
	});

	var agentGroupStore = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'iname' ],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'userGroup/getGroup.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var centerStore = Ext.create('Ext.data.Store', {
		fields : [ 'centername' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'centerList.do',
			reader : {
				type : 'json',
				root : 'centerlist'
			}
		}
	});
//   	var centerCombo = Ext.create('Ext.form.field.ComboBox', {
//		//margin : '5',
//		store : centerStore,
//		fieldLabel : '所属区域',  
//		queryMode : 'local',
//	    width : '25%',
//	    labelWidth : 100,
//	    labelAlign : 'right',
//		forceSelection : false, // 要求输入值必须在列表中存在
//		typeAhead : true, // 允许自动选择
//		displayField : 'centername',
//		valueField : 'centername',
//		triggerAction : "all",
//		value :centerNameForWhite,
//		emptyText : "--请选择所属区域--"
//	});
	 var centerCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
			iname : 'centername',
			displayField : 'centername',
			valueField : 'centername',
			labelWidth : 100,
			width :'20%',
			istore : centerStore,
			labelAlign : 'right',
			forceSelection : false,
			value:centerNameForWhite,
			emptyText : "--请选择所属区域--",
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myWhiteListGrid.ipage.moveFirst();
	                }
	            }
	        }
	 });
	
//	var agentGroup = Ext.create('Ext.form.field.ComboBox', {
//		name : 'agentGroup',
//		labelWidth : 70,
//		queryMode : 'local',
//		fieldLabel : '服务器组',
//		displayField : 'iname',
//		valueField : 'iid',
//		editable : false,
//		emptyText : '--请选择服务器组--',
//		store : agentGroupStore,
//		width : '25%',
//        labelAlign : 'right',
//		listeners: { //监听 
//			change : function(combo, records, eOpts){
//				var groupId = this.getValue();
//				agentListStore.load({
//					params: {
//						groupId: groupId
//					}
//				});
//			} 
//		} 
//	
//	});
	
	agentGroupStore.on('load', function (store, options) {
		
	});
	
	 
	var middleware_version_q = new Ext.form.TextField({
		fieldLabel: '中间件版本',
        labelAlign : 'right',
        labelWidth : 100,
        name: 'middleware_version_q',
        width:'33.3%',
        xtype: 'textfield'
	});
	var icreateTime_q = new Ext.form.field.Date ({
		fieldLabel: '纳管时间',
		labelAlign : 'right',
		labelWidth : 100,
		width:'33.3%',
		name: 'icreateTime_q',
		format : 'Y-m-d'
		//value: ''
	});
	
//	var osType_q = new Ext.form.TextField(
//	{
//        fieldLabel: '操作系统类型',
//        labelAlign : 'right',
//        labelWidth : 100,
//        name: 'osType_q',
//        width:'25%',
//        xtype: 'textfield'
//    });
    
    var osTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=OS_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
//	var osType_q = Ext.create('Ext.form.field.ComboBox', {
//		store : osTypeStore,
//		queryMode : 'local',
//		name : 'OS_type',
//		labelAlign : 'right',
//		width:'25%',
//		fieldLabel :'操作系统类型',
//		displayField : 'collectresult',
//		valueField : 'collectresult',
//		triggerAction : "all",
//		value:osTypeForWhite,
//		emptyText : "--请选择操作系统类型--"
//	});
	
   var osType_q = Ext.create('Ext.ux.ideal.form.ComboBox', {
			name : 'OS_type',
			displayField : 'collectresult',
			valueField : 'collectresult',
			labelWidth : 100,
			width :'20%',
			istore : osTypeStore,
			labelAlign : 'right',
			multiSelect :true,
			forceSelection : false,
			value:osTypeForWhite,
			emptyText : "--请选择操作系统类型--",
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myWhiteListGrid.ipage.moveFirst();
	                }
	            }
	        }
	 });
    
	var dbTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=db_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
//	var dbType_q = Ext.create('Ext.form.field.ComboBox', {
//		store : dbTypeStore,
//		queryMode : 'local',
//		fieldLabel :'数据库类型',
//		labelAlign : 'right',
//		width:'25%',
//		name : 'db_type',
//		displayField : 'collectresult',
//		valueField : 'collectresult',
//		triggerAction : "all",
//		value :dbTypeForWhite,
//		emptyText : "--请选择数据库类型--"
//	});
	
	var dbType_q = Ext.create('Ext.ux.ideal.form.ComboBox', {
			name : 'db_type',
			displayField : 'collectresult',
			valueField : 'collectresult',
			labelWidth : 100,
			width :'20%',
			istore : dbTypeStore,
			labelAlign : 'right',
			forceSelection : false,
			value:dbTypeForWhite,
			emptyText : "--请选择数据库类型--",
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myWhiteListGrid.ipage.moveFirst();
	                }
	            }
	        }
	 });
	
	var middlewareTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=middleware_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
//	var middleType = Ext.create('Ext.form.field.ComboBox', {
//		store : middlewareTypeStore,
//		queryMode : 'local',
//		fieldLabel :'中间件类型',
//		labelAlign : 'right',
//		width:'25%',
//		name : 'middleware_type',
//		displayField : 'collectresult',
//		valueField : 'collectresult',
//		triggerAction : "all",
//		value:middlewareTypeForWhite,
//		emptyText : "--请选择中间件类型--"
//	});
	var middleType = Ext.create('Ext.ux.ideal.form.ComboBox', {
			iname : 'collectresult',
			displayField : 'collectresult',
			valueField : 'collectresult',
			labelWidth : 100,
			width :'20%',
			istore : middlewareTypeStore,
			labelAlign : 'right',
			forceSelection : false,
			value:middlewareTypeForWhite,
			emptyText : "--请选择中间件类型--",
			listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	myWhiteListGrid.ipage.moveFirst();
	                }
	            }
	        }
	 });
    
    var osVersion_q = new Ext.form.TextField(
	{
        fieldLabel: '操作系统版本',
        labelAlign : 'right',
        labelWidth : 100,
        name: 'osVersion_q',
        width:'25%',
        xtype: 'textfield'
    });
    
//    var dbType_q = new Ext.form.TextField(
//	{
//        fieldLabel: '数据库类型',
//        labelAlign : 'right',
//        labelWidth : 100,
//        name: 'dbType_q',
//        width:'25%',
//        xtype: 'textfield'
//    });
//    
    var dbVersion_q = new Ext.form.TextField(
	{
        fieldLabel: '数据库版本',
        labelAlign : 'right',
        labelWidth : 100,
        name: 'dbVersion_q',
        width:'33.3%',
        xtype: 'textfield'
    });
	
	var search_form_hidden = Ext.create('Ext.form.Panel', {
			region : 'north',
		  	collapsible : true,//可收缩
		    collapsed : true,//默认收缩
		    border: false,
		    baseCls:'customize_gray_back',
	    	dockedItems : [
	    		{
	    			xtype : 'toolbar',
	    			 baseCls:'customize_gray_back',
	    			border : false,
	    			dock : 'top',
	    			items: [middleType, middleware_version_q,agentPort] //信息采集项
	    		},{
	    			xtype : 'toolbar',
	    			 baseCls:'customize_gray_back',
	    			border : false,
	    			dock : 'top',
	    			items: [icreateTime_q,osType_q,osVersion_q] //信息采集项
	    		},{
	    			xtype : 'toolbar',
	    			 baseCls:'customize_gray_back',
	    			border : false,
	    			dock : 'top',
	    			items: [dbType_q,dbVersion_q,applicationType] //信息采集项
	    		}]
	});
	
	var search_form_center = Ext.create('Ext.ux.ideal.form.Panel', {
			region : 'north',
		  	//collapsible : true,//可收缩
		    //collapsed : true,//默认收缩
			cls:'customize_panel_back',
			iqueryFun :function(){
				myWhiteListGrid.ipage.moveFirst();
			},
			//baseCls:'customize_gray_back',
			iselect : false,
		    border: false,
	    	dockedItems : [
				{
					xtype : 'toolbar',
//	    			 baseCls:'customize_gray_back',  
					border : false,
					dock : 'top',
					height:50,
					items: [/**applicationType**/hostName,agentIp,sysAdminA,sysAdminB,centerCombo]
				},
				{ //applicationType,middleType,appAdmin4,agentPort
					xtype : 'toolbar',
//					 baseCls:'customize_gray_back',
					border : false,
					height:50,
					dock : 'top',
					items: [system4,middleType,osType_q,dbType_q,belongIp/**middleType,appAdmin4,agentPort**/]
				},
				{
	    			xtype: 'toolbar',
//	    			 baseCls:'customize_gray_back',  
	    			border :false,
	    			dock :'top',
	    			cls:'whitelist',
	    			items:['->',{
		    			xtype : 'toolbar',
//		    			 baseCls:'customize_gray_back',  
						border : false,
						dock : 'top',
						items: [
							{
								xtype : 'button',
								text : '查询',
								cls : 'Common_Btn',
								handler :function() {
									if(!(search_form_hidden.getForm().isValid())){
										Ext.Msg.alert('提示', '查询条件存在非法输入！');
				  						return;
									}
									clearQueryWhere1();
									myWhiteListGrid.ipage.moveFirst();
									//agentListStore.load();
								}
							},
							{
								xtype : 'button',
								text : '重置',
								cls : 'Common_Btn',
								handler : function() {
									reset();
								}
							}
						]
		    		}]
	    		}
	    		
	    	]
		});
	
	
	
	
	var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
	    border: false,
	    cls:'whitelist',
	    baseCls:'customize_gray_back_whitelist',
    	dockedItems : [{
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back_whitelist',  
			dock : 'top',
			items: [content,cName,execuser,execparams ,'->',
				{
					xtype : 'button',
					text : '已选设备',
					cls : 'Common_Btn',
					handler : function() {
							//获取选择的agentId串
						getSelectedAgentIdStr();
						if(equiSelectedIds == ''){
							Ext.Msg.alert('提示', "未选择设备！");
							return
						}
						 //选择的agentStore
					     var chosedAgentStore = Ext.create ('Ext.data.Store',
						{
						    autoLoad : false,
						    autoDestroy : true,
						    pageSize: 500,
						    model : 'agentModel',
						    proxy :
						    {
						        type : 'ajax',
						        url : 'queryMyWhiteAgentList.do',
						        reader :
						        {
						            type : 'json',
						            root : 'dataList',
						            totalProperty: 'total'
						        }
						    }
						});
					    
						chosedAgentStore.on ('beforeload', function (store, options)
						{
							 var new_params = {
									equiSelectedIds : equiSelectedIds
					         };
							Ext.apply (chosedAgentStore.proxy.extraParams, new_params);
						});
//						var pageBar1 = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//						    store: chosedAgentStore,
//						    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//						    dock: 'bottom',
//						    displayInfo: true
//					     });
					     
				     	var  chosedAgentGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
						    store: chosedAgentStore,
						    cls:'window_border panel_space_left panel_space_right',
						    region: 'center',
						    height: 280,
						     ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
					//	    plugins: [new Ext.grid.plugin.CellEditing({
					//            clicksToEdit: 1
					//        })],
//					      	bbar : pageBar1,
						    border:false,
						    columnLines : true,
						    columns : agentColumns
						});
						
						chosedAgentStore.load();
						chosedAgentWindow = Ext.create('Ext.window.Window', {
							  		title : '选择的Agent列表',
							  		autoScroll : true,
							  		layout: 'border',
							  		modal : true,
							  		resizable : true,
							  		closeAction : 'destroy',
							  		width : 800,
							  		height : 600,
							  		items:[chosedAgentGrid]
							  	}); 
						chosedAgentWindow.show();
					}
				},
				{
					xtype : 'button',
					text : '立即执行',
					cls : 'Common_Btn',
					handler : function() {
						if(content.getRawValue() == '' || content.getRawValue() == null  ){
							Ext.Msg.alert('提示', "请输入命令！");
                            return;
						}
						//idSh = Ext.getCmp('content').value;
						idSh = execContentIid;
						if(idSh == null || idSh == '' || idSh == 'undefined'){
							Ext.Msg.alert('提示', "命令不允许执行，提示命令非法！");
							content.setValue();
                            return;
						}
						var tempAgentJson;
//						//获取选择的资源组所有agent  
//						//如果选择了资源组后，但未选择列表中数据，那么后台默认在该资源组上所有agent上执行
//						var agents = new Array();
//						var agentLists = agentListStore.getRange(0, agentListStore.getCount()-1);
//					    $.each(agentLists, function(i,v){
//					    		var value =  v.data.iid
//								var a = {
//										iid: value
//								};
//								agents.push(a);
//						});
					
						//获取选择的agent ID json串
	                	var selectagentJsonList = getSelectedJsonData();
		  		   
		  				//参数
		  				var scriptPara;
		  				if(parmFlag == 0){ //无参数
		  					 scriptPara = '';
		  				}else{//有参数
		  					if(execparams.getValue() == null  || execparams.getValue() == ''){
		  						Ext.Msg.alert('提示', "该命令必须录入参数！");
		  						return;
		  					}
		  					  //var scriptPara =  execparams.getValue()+'@@script@@service@@';
		  					  scriptPara =  execparams.getValue();
		  				}
			 
 				       if(selectagentJsonList != '' ){
		  					 tempAgentJson = selectagentJsonList;
		  				}else{ //如果选择的agent为空，那么后台传入选择资源组对应的agent
		  					Ext.Msg.alert('提示', "请选择服务器信息！");
	 					    return;
		  				}
						
		  				//***************************************************************************
		  				var execuserValue = execuser.getValue();//执行用户
						Ext.MessageBox.wait("数据处理中...", "进度条");
						Ext.Ajax.request({
			      			url : 'execCommdForMyWhiteList.do',
			      			method : 'POST',
			      			timeout : 1000000, 
			      			params : {
			      				commdId: idSh,
			      				uuid:execContentUuid,
			      				execUser: execuserValue,//执行用户
			      				scriptPara: scriptPara,//参数
			      				//groupId : groupId,//组ID
			      				jsonData:tempAgentJson,
			      				scriptType:scriptType,
			      				//jsonDataPara: jsonDataPara,
			      				dbsourceid:0,
			      				ifrom :3,
			      				flag: 0,
			      				ignore :0
			      			},
			      			success : function(response, request) {
	  		                	//me.up("window").close();
				    	  	    coatId =   Ext.decode(response.responseText).coatId;
				    	  	     
				    	  	    var message = Ext.decode(response.responseText).message;
				    	  	   
				    	  	    var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;//屏蔽
				    	  	     
			    	  	    	if(hasScreenKeyWord){//有屏蔽的命令
			    	  	    		Ext.Msg.alert('提示',  message);
			    	  	    		return;
			    	  	    	
				    	  	    }else{
				    	  	    	Ext.Msg.alert('提示', "命令已在指定服务器上运行！",function () {
										Ext.getCmp('lookResultButton').setDisabled(false);
										if(coatId != null && coatId !='' && coatId != 'undefined' && coatId != -1){
											forwardruninfo3White(coatId,2);
										}else{
											forwardToWhiteListExecHistory();
										}
									});

	    		  					//chosedAgentWindow.close();
				    	  	    }	 
			      			},
			      			failure : function(result, request) {
			      				Ext.Msg.alert('提示', '执行失败！');
			      			}
			      		});
		  				//***************************************************************************
					}
				},
				{
				xtype : 'button',
				text : '查看结果',
				//disabled:true,
				id:'lookResultButton',
				cls : 'Common_Btn',
				handler : function() {
					if(coatId != null && coatId !='' && coatId != 'undefined' && coatId != -1){
						forwardruninfo3White(coatId,2);
					}else{
						forwardToWhiteListExecHistory();
					}
				}
			},{
				xtype : 'button',
				text : '导入选择',
				cls : 'Common_Btn',
				handler : function() {
					importExcel();
				}
			},
			{
				xtype : 'button',
				text : '重置',
				cls : 'Common_Btn',
				handler : function() {
					clearQueryWhere1();
				}
			}
			/**{
				xtype : 'button',
				text : '返回',
				cls : 'Common_Btn',
				handler: function() {
  		        	var myurl = "myWhiteList.do";
  		        	contentPanel.getLoader().load({url: myurl,scripts: true,callback: function(records, operation, success) {
  		    	    	showImg(myurl);
  		    	    }});
  			    }
			}**/
			]
		},{
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back',  
			dock : 'top',
			items: [  ]
		}]
	});
	
	if(bakicoatid!=null && bakicoatid!=""){
		Ext.getCmp('lookResultButton').setDisabled(false); 
		coatId=bakicoatid;
	}
	
	 function reset () {
		//search_form_center.getForm().reset();
	 	//agentListStore.reload();
	 	//myWhiteListGrid.ipage.moveFirst();
	 	search_form_hidden.getForm().reset();
		execContentIid = '';
		execContentUuid = '';
		equiSelectedIds = '';
		hostName.setValue('');
//		agentPort.setValue('');
		agentIp.setValue('');
//		area.setValue('');
//		serverIp.setValue('');
//		applicationType.setValue('');
		middleType.setValue('');
		sysAdmin1.setValue('');
		system4.setValue('');
		centerCombo.setValue('');
		osType_q.setValue('');
		 sysAdminA.setValue();
		 sysAdminB.setValue();
		 belongIp.setValue();
		
		//appAdmin4.setValue('');
	 }
	 
	 
	 //执行完后直接点击的情况，直接跳转到明细页面
	 function forwardruninfo3White(coatid, flag) {
	 		contentPanel.setTitle("白名单执行结果");
		    contentPanel.getLoader().load({
		        url: "detailscriptserverForFlowForWhite.do",
		        scripts: true,
		        params: {
		            coatid: coatid,
		            flag: 1,
		            fromWhere:'fromWhite',
		            iid:idSh,
					serviceName : serviceNameSh,
					parmFlag : parmFlag,
					editingChosedAgentIds : editingChosedAgentIds,
					icontent:contentSh,
					 hostName: hostName.rawValue,
					ip: agentIp.rawValue,
					sysAdmin:sysAdmin1.rawValue,
					centerName:centerCombo.getValue(),
					systemInfo:system4.rawValue,
					middlewareType:middleType.getValue(),
					osType:osType_q.getValue(),
					dbType : dbType_q.getValue() 
		        }
		    });
		}
   	 //未执行命令，直接点击查看结果，跳转到 白名单执行历史主页面
	 function forwardToWhiteListExecHistory() {
	 		contentPanel.setTitle("白名单执行结果");
		    contentPanel.getLoader().load({
		        url: "forwardwhitescriptcoat.do",
		        params: {
					editingChosedAgentIds : editingChosedAgentIds,
					hostName: hostName.rawValue,
					ip: agentIp.rawValue,
					sysAdmin:sysAdmin1.rawValue,
					centerName:centerCombo.getValue(),
					systemInfo:system4.rawValue,
					middlewareType:middleType.getValue(),
					osType:osType_q.getValue(),
					dbType : dbType_q.getValue() 
		        },
		        scripts: true
		    });
		}
		
	 var agentColumns = [ {
			text : '序号',
			width : 50,
			xtype : 'rownumberer'
		}, {
			text : 'iid',
			dataIndex : 'iid',
			flex : 1,
			hidden : true
		},
		{ text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
        { text: 'iagentid',  dataIndex: 'iagentid',hidden:true},
        {
            text: '计算机名',
            dataIndex: 'hostName',
            width: 100
        },{
            text: 'IP',
            dataIndex: 'agentIp',
            width: 120
        },{
			 text: '所属网段',
			 dataIndex: 'belongIp',
			 width: 120
		 },{
            text: '所属区域',
            dataIndex: 'centerName',
            width: 100
        },
        //{text: '系统管理员',  dataIndex: 'sysAdmin',width: 100},
		 {text: '系统管理员A角',  dataIndex: 'userA',width: 100},
		 {text: '系统管理员B角',  dataIndex: 'userB',width: 100},
        {text: '应用管理员',  dataIndex: 'appAdmin',width: 100},
        {text: '信息系统名称',  dataIndex: 'systemInfo',width: 100},
        {
            text: '运行天数',
            dataIndex: 'runDays',
            width: 100
        },
        {
            text: '是否双机',
            dataIndex: 'isHa',
            width: 100
        },
        {
            text: 'AgentInfo系统类型',
            dataIndex: 'osTypeAgentinfo',
            width: 100,
            hidden:true
        },
        {
            text: '操作系统类型',
            dataIndex: 'osType',
            width: 100
        },
         {
            text: '操作系统版本',
            dataIndex: 'osVersion',
            width: 100
        }, 
        {
            text: '数据库类型',
            dataIndex: 'dbType',
            width: 100
        },
        {
            text: '数据库版本',
            dataIndex: 'dbVersion',
            width: 100
        },
        {text: '中间件类型',  dataIndex: 'middlewareType',width: 100},
        {
            text: '中间件版本',
            dataIndex: 'middlewareVersion',
            width: 100
        },
        {
            text: '纳管时间',
            dataIndex: 'icreateTime',
            width: 100
        }, 
         {
            text: '纳管用户',
            dataIndex: 'startUser',
            width: 100
        }, 
        {text: '应用类型',  dataIndex: 'ctype',hidden:true,width: 100},
        {
            text: '状态',
            dataIndex: 'agentState',
            //flex: 1,
            width: 110,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        },
        {
            text: 'Agent端口',
            dataIndex: 'agentPort',
            width: 100,
            hidden:true
        },
        {
            text: '环境',
            dataIndex: 'envType',
            hidden: true,
            width: 100,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = '<font">测试</font>';
                } else if (value == 1) {
                    backValue = '<font >生产</font>';
                }
                return backValue;
            }
        }];	
	 
		
	 
	 Ext.define('agentModel', {
			extend : 'Ext.data.Model',
			fields : [ 
				{name : 'iid',type : 'string'},
				{name: 'sysName',  type: 'string'},
				{name: 'centerName',  type: 'string'},
				{name: 'igroupid', type: 'string'},
		        {name: 'iagentid', type: 'string'},
		        {name: 'sysAdmin', type: 'string'},
				{name: 'userA', type: 'string'},
				{name: 'userB', type: 'string'},
				{name: 'belongIp', type: 'string'},
		        {name: 'appAdmin', type: 'string'},
		        {name: 'systemInfo', type: 'string'},
		        {name: 'ctype', type: 'string'},
		        {name: 'middlewareType', type: 'string'},
		        { name: 'appName',  type: 'string'},
		        { name: 'hostName', type: 'string'},
		        { name: 'osType',type: 'string'},
		        { name: 'osTypeAgentinfo',type: 'string'},
		        { name: 'agentIp', type: 'string'},
		        {name: 'agentPort',type: 'string'},
		        {name: 'envType',type: 'string'},
		        { name: 'agentState', type: 'int'},
		        {name: 'osVersion',type: 'string'},//系统版本
		        {name: 'runDays',type: 'int'},//运行天数
		        {name: 'isHa',type: 'string'},//是否双机
		        {name: 'dbType',type: 'string'},//数据库类型
		        {name: 'dbVersion',type: 'string'},//数据库版本
		        {name: 'middlewareVersion',type: 'string'},//中间件版本
		        {name: 'icreateTime',type: 'string'},//纳管时间
		        {name: 'startUser',type: 'string'}//纳管启动人
         	]
		});
	 
	 itemsPerPage = 50;
	 var agentListStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    pageSize: itemsPerPage,
	    model : 'agentModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'queryMyWhiteAgentList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty: 'total'
	        }
	    }
	});
	 agentListStore.on ('beforeload', function (store, options)
	{
		 var new_params = {  
				hostName: hostName.rawValue,
				agentIp: agentIp.rawValue,
				iagentPort: agentPort.value,
				appliType:applicationType.value,
				sysAdmin:sysAdmin1.rawValue,
				centerName:centerCombo.getValue(),
				systemInfo:system4.rawValue,
				//appAdmin:appAdmin4.value,
				middleType:middleType.value,
				icreateTime:  icreateTime_q.getRawValue(),
				osType:osType_q.getValue(),
				osVersion : osVersion_q.getValue(),
				dbtype : dbType_q.getValue(),
			    db_version : dbVersion_q.getValue(),
				userA : sysAdminA.getValue(),
				userB : sysAdminB.getValue(),
				belongIp : belongIp.getValue(),
			    middleware_version:middleware_version_q.getValue()
         };
		Ext.apply (agentListStore.proxy.extraParams, new_params);
	});
	
	agentListStore.addListener('load',
    function(me, records, successful, eOpts) {
    	//执行历史页面返回传过来的，或者选择后构建出的agentID数组
        if (editingChosedAgentIds) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            myWhiteListGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
   
    });
	
    
    
   
	
	
//	if(editingChosedAgentIds.length >0){
//		agentListStore.load();
//	}
	
//	 var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//	  	store: agentListStore,
//	  	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//	    dock: 'bottom',
//	    displayInfo: true,
//	    emptyMsg:'找不到任何记录'
//	  });
  
 
     var myWhiteListGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
			region: 'center',
			autoScroll: true,
			store:agentListStore,
			cls:'customize_panel_back',
		    selModel : selModel1,
			padding : grid_space,
		    plugins: [ cellEditing ],
//		    bbar : pageBar,
		    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    border:false,
		    columnLines : true,
		    columns : agentColumns,
		    listeners: {
		            select: function(t, record, index, eOpts) {
 					    //如果 选择的数据系统类型不一致，那么提示
		                //var selDatas = myWhiteListGrid.getSelectionModel().getSelection();
 
		            	
			     		
			     		var osTypeV = record.get('osTypeAgentinfo'); // 当前选择行的AgentInfo里的 系统类型
			     		var flag = true;
			  
//				 		Ext.Array.each(selDatas, function(recordi) {
//				                var osTypeEach = recordi.get('osTypeAgentinfo');
//				                if(osTypeEach!=osTypeV &&  (osTypeV.toUpperCase().indexOf('WIN') != -1 || osTypeEach.toUpperCase().indexOf('WIN')  != -1)){
//				                	Ext.Msg.alert('提示', "只允许选择相同系统类型的Agent信息");
//					    			myWhiteListGrid.getSelectionModel().deselect(record,false);
//					    			flag = false;
//					    			return flag;
//				                }
//				        });
			     		 //构建选择的AgentinfoOsType数组
	                    if(platformArray.indexOf(osTypeV) == -1){ 
	                			platformArray.push(osTypeV);
	                    }
			     		
				        if((editingChosedAgentIds.indexOf('WIN') > -1 &&  osTypeV.toUpperCase().indexOf('WIN') == -1) || (editingChosedAgentIds.indexOf('LINUX') > -1 &&  osTypeV.toUpperCase().indexOf('WIN') > -1)){
				             flag = false;
				             Ext.Msg.alert('提示', "只允许选择相同系统类型的Agent信息");
				             myWhiteListGrid.getSelectionModel().deselect(record,false);
				             platformArray.remove(osTypeV);
				        }
		                
	                	if (flag && editingChosedAgentIds.indexOf(record.get('iid')) == -1) {
	                		editingChosedAgentIds.push(record.get('iid'));
	                		if(osTypeV.toUpperCase().indexOf('WIN') != -1 && editingChosedAgentIds.indexOf('WIN') == -1 ){ //windows
	                			editingChosedAgentIds.push('WIN');
	                		}else if(osTypeV.toUpperCase().indexOf('WIN') == -1 && editingChosedAgentIds.indexOf('LINUX') == -1 ){
	                			editingChosedAgentIds.push('LINUX');
	                		}
	                	}
	                	
	                  
		            },
		            // 取消选择后，如果不存在选择的agent数据，那么把 命令，参数 条件框清空
		            deselect: function(t, record, index, eOpts) {
		                var selDatas = myWhiteListGrid.getSelectionModel().getSelection();
			     		//当前页不存在选择的数据了，那么需要判断 editingChosedAgentIds是否还存在不是'WIN','LINUX'的数据
		                if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
		                	editingChosedAgentIds.remove(record.get('iid'));
		                }
		                
		                var osTypeV = record.get('osTypeAgentinfo');
		                 //去掉未选择的AgentinfoOsType数组元素
	                    if(platformArray.indexOf(osTypeV) > -1){ 
	                			platformArray.remove(osTypeV);
	                    }


			     		if(selDatas.length == 0){
			     			content.setValue('');
			     			content.getStore().removeAll(true);
			     			execparams.setValue('');
			     			if(editingChosedAgentIds.length == 1){//如果存在只有一个元素，那么一定是 WIN 或者 LINUX 的元素 ，需要remove掉
			     				 if(editingChosedAgentIds.indexOf('WIN') != -1){
			     				 	editingChosedAgentIds.remove('WIN');
			     				 }else if(editingChosedAgentIds.indexOf('LINUX') != -1){
			     				 	editingChosedAgentIds.remove('LINUX');
			     				 }
			     			}
			     		}
		               
		            }
		    }
		});
//     myWhiteListGrid.on("select",function(obj, record, index, eOpts){
//  			var selDatas = myWhiteListGrid.getSelectionModel().getSelection();
//     		var osTypeV = record.get('osTypeAgentinfo'); // 当前选择行的AgentInfo里的 系统类型
//      	 
//	 		 Ext.Array.each(selDatas, function(recordi) {
//	                var osTypeEach = recordi.get('osTypeAgentinfo');
//	                if(osTypeEach!=osTypeV &&  (osTypeV.toUpperCase().indexOf('WIN') != -1 || osTypeEach.toUpperCase().indexOf('WIN')  != -1)){
//	                	Ext.Msg.alert('提示', "只允许选择相同系统类型的Agent信息");
//		    			myWhiteListGrid.getSelectionModel().deselect(record,true);
//		    			return;
//	                }
//	            });
//	    });
	    
	   // 取消选择后，如果不存在选择的agent数据，那么把 命令，参数 条件框清空
//     myWhiteListGrid.on("deselect",function(obj, record, index, eOpts){
//  			var selDatas = myWhiteListGrid.getSelectionModel().getSelection();
//     		if(selDatas.length == 0){
//     			content.setValue('');
//     			execparams.setValue('');
//     		}
//	    });
	    
 
     var centerPanel = Ext.create('Ext.panel.Panel', {
			region: 'center',
			layout : 'border',
			border:false,
			items : [/**search_form_hidden,**/search_form_center,myWhiteListGrid]
		});
     
	 var mainPanel = Ext.create('Ext.panel.Panel',{
		 renderTo : "execMyWhiteList_grid_area",
	        width : contentPanel.getWidth(),
		    height :contentPanel.getHeight()-modelHeigth,
	        border : false,
	        layout: 'border',
	        items : [search_form,centerPanel]
	});
	 
	
	 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function (){
			mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
//			myWhiteListGrid.setHeight (contentPanel.getHeight ()-55);
		});
  
	/* 解决IE下trim问题 */
	String.prototype.trim=function(){
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(mainPanel);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});
	function clearQueryWhere1(){
	 	editingChosedAgentIds = new Array();
	 	myWhiteListGrid.ipage.moveFirst();
	 	contentListStore.removeAll();
		content.setValue('');
		execContentIid = '';
		execContentUuid = '';
		execuser.setValue('');
		execparams.setValue('');
		platformArray = new Array();
	}
     //从一个json对象中，解析出key=iid的value,返回改val
//	 function parsIIDJson(key ,jsonObj){
//		 var eValue=eval('jsonObj.'+key);  
//		 return jsonObj[''+key+''];
//	 }
	 //获取选择的agentId串
	 function getSelectedAgentIdStr(){
		if (editingChosedAgentIds.length < 1) {
			 equiSelectedIds = '';
		}
		equiSelectedIds = '';
		Ext.Array.each(editingChosedAgentIds, function(v) {
				if(v !='WIN' && v !='LINUX'){
					equiSelectedIds =equiSelectedIds+ v+",";	
				}
	    });
	    equiSelectedIds = equiSelectedIds.substr(0,equiSelectedIds.length-1);
	}
	 
	 function getSelectedJsonData(){
		//var commdIdList = myWhiteListGrid.getSelectionModel().getSelection();
	 	
		if (editingChosedAgentIds.length < 1) {
			return '';
		}
		var jsonData = "[";
		for ( var i = 0, len = editingChosedAgentIds.length; i < len; i++) {
			if (i == 0 && editingChosedAgentIds[i] != 'WIN' && editingChosedAgentIds[i] != 'LINUX')
			{
				jsonData = jsonData + '{"iid":"'+ editingChosedAgentIds[i] + '"}';
			} else if(i !=0 && editingChosedAgentIds[i] != 'WIN' && editingChosedAgentIds[i] != 'LINUX'){
				if((editingChosedAgentIds[0] == 'WIN' || editingChosedAgentIds[0] == 'LINUX') && i == 1){
					jsonData = jsonData + '{"iid":"'+ editingChosedAgentIds[i] + '"}';
				}else{
					jsonData = jsonData + "," + '{"iid":"'+ editingChosedAgentIds[i]+ '"}';
				}
			}
		}
		jsonData = jsonData + "]";
		return jsonData ;
	}
	
	function checkFile(fileName){
	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
	    if(!file_reg.test(fileName)){  
	    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
	    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
	        return false;
	    }
	    return true;
	}
	
	function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:100,
		    frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 65,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...'
				}
			],
			buttonAlign: 'left',
			buttons: [
					{
						id:'upldBtnIdAudi',
						text: '导入Agent文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
							if (hdtmpFilNam.endsWith('.et')) {

							} else if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}
							if (form.isValid()) {
								// Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'importAgentForWhitelistExec.do',
									waitMsg:'正在提交数据，请稍候...',
                     				waitTitle:'提示',
									params:{
										envType:1//生产
				                	},
								    success: function(form, action) {
 										   var msg = Ext.decode(action.response.responseText).message;
// 										   var msg = Ext.decode(action.response.responseXML.activeElement.innerHTML).message;
								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
								    	   var osTypeList =  Ext.decode(action.response.responseText).osTypeList;
								    	   if(msg!='' && msg != null) {
								    		   if(matchAgentIds && matchAgentIds.length>0) {
									    			    var msgTextArea = Ext.create('Ext.form.field.TextArea', {
														        name: 'pubdesc',
														        fieldLabel: '提示',
														        //emptyText: '',
														        labelWidth: 37,
														        labelAlign : 'right',
														       // margin : '10 0 0 0',
														         //maxLength: 255, 
														        height: 200,
														       columnWidth:.98,
														       readOnly:true,
														        autoScroll: true
														});
													    msgTextArea.setValue(msg);
													    var messagePa = Ext.create('Ext.form.Panel', {
																width: 600,
														    	layout : 'anchor',
														    	baseCls:'customize_gray_back',
														    	bodyCls : 'x-docked-noborder-top',
														    	buttonAlign : 'center',
														    	border : false,
															    items: [{
														//	    	layout:'form',
															    	anchor:'98%',
															    	padding : '5 0 5 0',
															    	border : false,
															    	items: [ {
															    		layout:'column',
																    	border : false,
																    	items:[msgTextArea]
															    	}]
															    }],
															   bbar: ['->',{
																	xtype : 'button',
																	text : '确定',
																	cls : 'Common_Btn',
																	handler :function() {
																		this.up("window").close();
																		Ext.Msg.alert('提示', "导入成功！");
																		editingChosedAgentIds = matchAgentIds;
																		platformArray= osTypeList;
																		myWhiteListGrid.ipage.moveFirst();
																	}
																},{
																	xtype : 'button',
																	text : '取消',
																	cls : 'Common_Btn',
																	handler :function() {
																		 this.up("window").close();
																	}
																},'->']
															});
													    
														var messageWin = Ext.create('Ext.window.Window', {
														    title: '请确认',
														    width: 600,
														    height: 330,
														    modal:true,
														    resizable: false,
														    closeAction: 'destroy',
														    items:  [messagePa]
														});
														messageWin.show();
								    			    
								    		   } else {
								    			    // Ext.Msg.alert('提示-没有匹配项', msg);
								    		   	var msgTextArea2 = Ext.create('Ext.form.field.TextArea', {
													        name: 'pubdesc',
													        fieldLabel: '提示',
													        //emptyText: '',
													        labelWidth: 37,
													        labelAlign : 'right',
													       // margin : '10 0 0 0',
													         //maxLength: 255, 
													        height: 200,
													       columnWidth:.98,
													       readOnly:true,
													        autoScroll: true
													    });
													    msgTextArea2.setValue(msg);
													    var messagePa2 = Ext.create('Ext.form.Panel', {
																width: 600,
														    	layout : 'anchor',
														    	baseCls:'customize_gray_back',
														    	bodyCls : 'x-docked-noborder-top',
														    	buttonAlign : 'center',
														    	border : false,
															    items: [{
														//	    	layout:'form',
															    	anchor:'98%',
															    	padding : '5 0 5 0',
															    	border : false,
															    	items: [ {
															    		layout:'column',
																    	border : false,
																    	items:[msgTextArea2]
															    	}]
															    }],
															   bbar: ['->',{
																	xtype : 'button',
																	text : '确定',
																	cls : 'Common_Btn',
																	handler :function() {
																		this.up("window").close();
																	}
																},'->']
															});
													    
														var messageWin2 = Ext.create('Ext.window.Window', {
														    title: '请确认',
														    width: 600,
														    height: 330,
														    modal:true,
														    resizable: false,
														    closeAction: 'destroy',
														    items:  [messagePa2]
														});
														messageWin2.show();
								    		   }
								    	   } else {
								    		    Ext.Msg.alert('提示', "导入成功！");
										    	editingChosedAgentIds = matchAgentIds;
										    	platformArray= osTypeList;
									    	    myWhiteListGrid.ipage.moveFirst();
								    	   }
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}, {
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadAgentTemplate.do?fileName=WhiteListStartAgentImportMould.xls';
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '设备信息批量导入',
		    width: 400,
		    height: 200,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}
	 
});