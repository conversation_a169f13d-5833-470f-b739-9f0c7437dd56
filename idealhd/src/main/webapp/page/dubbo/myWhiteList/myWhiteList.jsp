<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean sendSwitch = Environment.getInstance().getScriptServiceSendSwitch();
%>
<html>
<head>
<script>
var filter_category = '<%=request.getParameter("filter_category")==null?"":request.getParameter("filter_category")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>  
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/myWhiteList/myWhiteList.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/myWhiteList/eiditCommWin.js"></script>
</head>
<body>
<div id="myWhiteList_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>