var editCommWinFunctionForWhiteList;
Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
 	 var EditCommWin;
	 var editCommForm;
     var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
	 var platformStore1 = Ext.create('Ext.data.JsonStore', {
		fields: ['INAME', 'ICODEVALUE'],
		//autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getScriptPlatformCode.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
	platformStore1.on('beforeload', function(store, options) {
			var new_params = {
				gdSwitch :  '0'
			};
			Ext.apply(platformStore1.proxy.extraParams, new_params);
	});
 
/**
  * 编辑命令 
  * map:参数 
  */

  editCommWinFunctionForWhiteList =function (map){
		   var typeStore1 = Ext.create('Ext.data.Store', {
				fields : [ 'sysTypeId', 'sysType' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			 });
		  var timeout = new Ext.form.TextField({
			  name: 'timeout',
			  fieldLabel: '超时(秒)',
			  displayField: 'timeout',
			  emptyText: '',
			  hidden:!scriptTimeoutSwitch,
			  labelWidth: 70,
			  // padding: '0 5 0 0',
			  width : '50%',
			  labelAlign : 'right',
			  columnWidth: .49
		  });
		   var categoryStore1 = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'bsName' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
		    
			
			
			var paramFlagStore1 = Ext.create('Ext.data.Store', {
				fields: ['id', 'name'],
				data : [
					{"id":"0", "name":"无"},
					{"id":"1", "name":"有"}
					]
			 });
				
				/**命令名称**/
				var newIidText = new Ext.form.TextField({
					name : 'newIidText',
					fieldLabel : 'iid',
					hidden :true,
					labelWidth : 70,
					width :'33%',
			        labelAlign : 'right'
				}); 
				
				var uuidText = new Ext.form.TextField({
					name : 'uuidText',
					fieldLabel : 'uuid',
					hidden :true,
					labelWidth : 70,
					width :'33%',
			        labelAlign : 'right'
				}); 
				
			    var newCommdName1 = new Ext.form.TextField({
					name: 'newCommdName1',
					fieldLabel: '命令说明',
					afterLabelTextTpl: required,
					labelAlign : 'right',
					displayField: 'newCommdName1',
					emptyText: '',
					labelWidth: 70,
					padding: '0 5 0 0',
					columnWidth: 1
				}); 
				
				 var newCommd1  = new Ext.form.TextField({
					name: 'newCommd1',
					fieldLabel: '命令',
					afterLabelTextTpl: required,
					displayField: 'newCommd1',
					emptyText: '',
					labelWidth: 70,
					padding: '0 5 0 0',
					labelAlign : 'right',
					 columnWidth: 1
				}); 
				
				//创建命令窗口 类别下拉框 
				var newCategory1 = Ext.create('Ext.form.field.ComboBox', {
			        name: 'newCategory1',
			        labelWidth: 70,
			        columnWidth: .5,
			        queryMode: 'local',
			        afterLabelTextTpl: required,
			        fieldLabel: '命令类别',
			        padding: '0 5 0 0',
			        displayField : 'bsName',
					valueField : 'iid',
					//width:'40%',
			        editable: true,
			        labelAlign : 'right',
			        emptyText: '--请选择类别--',
			        store:  categoryStore1,
			        listeners : {
						change : function() { 
							newType1.clearValue();
							newType1.applyEmptyText();
							newType1.getPicker().getSelectionModel().doMultiSelect([], false);
							if(this.value != ""){
								typeStore1.load({
									params : {
										fk : this.value
									}
								});
							}
						},
						afterRender: function(combo) {
							if(filter_category!='-1' && filter_category!='') {
								newCategory1.setValue(parseInt(filter_category));
							}
			            }
					}
			    });
			 
			    /** 类型* */
				var newType1 = Ext.create('Ext.form.field.ComboBox', {
					name : 'newType1',
			//		padding : '5',
					labelWidth : 70,
					afterLabelTextTpl: required,
					queryMode : 'local',
					fieldLabel : '命令类型',
					displayField : 'sysType',
					valueField : 'sysTypeId',
					editable : false,
					//width:'40%',
					//hidden:true,
					emptyText : '--请选择类型--',
					store : typeStore1,
					columnWidth: .49,
			        labelAlign : 'right'
				});	 
				
		 
			 
		    /**有无参数**/
			var newParamFlag1 = Ext.create('Ext.form.field.ComboBox', {
					name : 'newParamFlag1',
			//		padding : '5',
					labelWidth : 70,
					//afterLabelTextTpl: required,
					queryMode : 'local',
					fieldLabel : '有无参数',
					displayField : 'name',
					valueField : 'id',
					editable : false,
					//value:'0',
					columnWidth: .49,
					//hidden:true,
					emptyText : '--请选择有无参数--',
					store : paramFlagStore1,
					//width : '25%',
			        labelAlign : 'right'
		     });
			
			/** 适应平台 编辑命令用* */
//			var comBoxPlatform1 = Ext.create('Ext.form.field.ComboBox', {
//				name : 'comBoxPlatform1',
//		//		padding : '5',
//				labelWidth : 70,
//				queryMode : 'local',
//				fieldLabel : '适用平台',
//				displayField : 'name',
//				afterLabelTextTpl: required,
//				valueField : 'id',
//				editable : false,
//				emptyText : '--请选择适用平台--',
//				store : platformStore1,
//				columnWidth: 1,
//		        labelAlign : 'right'
//		 
//			});  
			var comBoxPlatform1 = Ext.create('Ext.form.field.ComboBox', {
							name : 'newPlatform',
							// padding : '5',
							labelWidth : 70,
							queryMode : 'local',
							fieldLabel : '适用平台',
							displayField: 'INAME',
					        valueField: 'ICODEVALUE',
					        //multiSelect: true,//启用多选
							editable : false,
							emptyText : '--请选择适用平台--',
							store : platformStore1,
							columnWidth : .5,
							listeners: {
								select :function(combobox,records,eOpts){
									if(records.length>1){
										var windowsFlag = false;
										var linuxFlag = false;
										Ext.each(records, function(record) {
											var iname1 = record.data.INAME;
											if(iname1 == 'Windows'){
												windowsFlag = true;
											}
											if(iname1 != 'Windows'){
												linuxFlag = true;
											}
											if(linuxFlag && windowsFlag){
												 Ext.Msg.alert('提示', 'Windows平台和非Windows平台不能同时选择！');
												 comBoxPlatform1.clearValue();
												 return;
											}
										});
									}
								}
							}, 
							labelAlign : 'right'
				});
			
			 
			
		      var visibleTypeStore1 = Ext.create('Ext.data.Store', {
				fields: ['id', 'name'],
				data : [
					{"id":"0", "name":"公有"},
					{"id":"1", "name":"私有"}
				]
			 });
		    /** 可见类型* */
			var newVisibleType1= Ext.create('Ext.form.field.ComboBox', {
				name : 'newVisibleType1',
		//		padding : '5',
				labelWidth : 70,
				queryMode : 'local',
				afterLabelTextTpl: required,
				fieldLabel : '可见类型',
				displayField : 'name',
				valueField : 'id',
				hidden:true,
				editable : false,
				emptyText : '--请选择可见类型--',
				//value:'0',
				store : visibleTypeStore1,
				//width : '50%',
				columnWidth: .5,
		        labelAlign : 'right'
		 
			}); 
			
		    var  commentInWin1 = Ext.create('Ext.form.field.TextArea', {
			    	name: 'commentInWin1',
			    	fieldLabel: '备注',
			    	//afterLabelTextTpl: required,
			    	displayField: 'commentInWin1',
			    	emptyText: '请输入备注...',
			    	labelAlign : 'right',
			    	labelWidth: 70,
			    	height: 136,
			        columnWidth: 1,
			    	autoScroll: true
			 });
			 
			 
			 function recoverySet(map){
			 		    var iid_f = map['iid'];//iid
			 		    var uuid_f = map['uuid'];//iid
				   	    var categoryValue_f = map['categoryValue'];//类别 码值
				   	    //var sysName = map['categoryDisValue'] // 类别 显示值
				   	    var typeValue_f = map['typeValue'];//类型  码值
				   	    //var typeDisValue = map['typeDisValue'];//类型 显示值
				   	    var commdDescValue_f = map['commdDescValue'];//命令说明 
				   	    var commdValue_f = map['commdValue'];//命令
				   	    var paramFlagValue_f = map['paramFlagValue'];//有无参数
				   	    var platformValue_f = map['platformValue'];// 适用平台
				   	    var visibleTypeValue_f = -1; //可见类型
				   	    var commentValue_f = map['commentValue'];//备注
				 		var timeOutValue_f = map['timeOutValue'];//超时时间
				   	   newCategory1.setValue(categoryValue_f);
					   newType1.setValue(typeValue_f);
					   newCommdName1.setValue(commdDescValue_f);
					   timeout.setValue(timeOutValue_f);
					   newCommd1.setValue(commdValue_f);
					   newParamFlag1.setValue(paramFlagValue_f);
					 
					   comBoxPlatform1.setValue(platformValue_f);
					   newVisibleType1.setValue(visibleTypeValue_f);
					   commentInWin1.setValue(commentValue_f);
			  		   newIidText.setValue(iid_f);
			  		   uuidText.setValue(uuid_f);
			 }
			    
			    
			 function editreset(){
			 	editCommForm.getForm().reset();
			 }
			 
	 	    //编辑命令用
		    editCommForm = Ext.create('Ext.form.Panel', {
		        width: 590,
		        height: 350,
		        border: false,
		        layout: 'anchor',
		        collapsible : false,
		        cls:'window_border',
		        id : 'editCommForm',
		        items: [{
		            border: false,
		            layout: 'column',
		            margin: '5',
		            items: [newCategory1, newType1 ] //类别，类型
		        },{
		            border: false,
		            layout: 'column',
		            margin: '5',
		            items: [newCommdName1]//命令说明
		        },
		        {
		            layout: 'column',
		            border: false,
		            margin: '5',
		            items: [newCommd1]//命令
		        },
		        {
		            layout: 'column',
		            border: false,
		            margin: '5',
		            items: [newParamFlag1,comBoxPlatform1,newVisibleType1]//有无参数，适用平台
		        },{
						layout: 'column',
						border: false,
						margin: '5',
						items: [timeout]//超时时间
				},
		        {
		            layout: 'column',
		            border: false,
		            margin: '5',
		            items: [commentInWin1,newIidText,uuidText]//备注
		        }]
		    });
//	 	    if(EditCommWin){
//	 	    	EditCommWin.destroy();
//	 	    }
	 	    editreset();
  		 	
			var iid_f = map['iid'];//iid
			var uuid_f = map['uuid'];
	   	    var categoryValue_f = map['categoryValue'];//类别 码值
	   	    //var sysName = map['categoryDisValue'] // 类别 显示值
	   	    var typeValue_f = map['typeValue'];//类型  码值
	   	    //var typeDisValue = map['typeDisValue'];//类型 显示值
	   	    var commdDescValue_f = map['commdDescValue'];//命令说明 
	   	    var commdValue_f = map['commdValue'];//命令
	   	    var paramFlagValue_f = map['paramFlagValue'];//有无参数
	   	    var platformValue_f = map['platformValue'];// 适用平台
	   	    var visibleTypeValue_f = -1; //可见类型
	   	    var commentValue_f = map['commentValue'];//备注
	  		var timeOutValue_f = map['timeOutValue'];//超时时间
	   	   newCategory1.setValue(categoryValue_f);
		   newType1.setValue(typeValue_f);
		   newCommdName1.setValue(commdDescValue_f);
		   newCommd1.setValue(commdValue_f);
		   newParamFlag1.setValue(paramFlagValue_f);
	       timeout.setValue(timeOutValue_f);
		   comBoxPlatform1.setValue(platformValue_f);
		   newVisibleType1.setValue(visibleTypeValue_f);
		   commentInWin1.setValue(commentValue_f);
  		   newIidText.setValue(iid_f);
  		   uuidText.setValue(uuid_f);
  		   //if(!EditCommWin){
  		   		
  		   		EditCommWin = Ext.create('widget.window', {
					//id:'createCommWin',
				    title: '编辑命令',
				    cls:'window_border',
				    resizable: false,
				    draggable : false,// 禁止拖动
		            modal: true,
		            //closable: true,
		            width: 635,
		            minWidth: 350,
		            height: 490,
		            layout: {
		                type: 'border',
		                padding: 5
		            },
				    closeAction: 'destory',
				    items:  [editCommForm],
				    buttonAlign: 'center',
				     listeners : {
			        	'close' : function(e,opt){
			        		  EditCommWin.destroy();
			        	}
			        },
				    buttons :[{
				    	xtype: "button",
						text: "保存",
						handler: function () {
			 						
							   	    var categoryValue_f = newCategory1.getValue();//类别 码值
							   	    var sysName = newCategory1.getRawValue(); // 类别 显示值
							   	    var typeValue_f = newType1.getValue();//类型  码值
							   	    var typeDisValue = newType1.getRawValue();//类型 显示值
							   	    var commdDescValue_f = newCommdName1.getValue(); //命令说明
							   	    var commdValue_f =  newCommd1.getValue();//命令
							   	    var paramFlagValue_f = newParamFlag1.getValue();//有无参数
							   	    //var platformValue_f = newPlatform.getValue();// 适用平台
 
							   	    var platformValue_f = comBoxPlatform1.getValue();
							   	    var visibleTypeValue_f = newVisibleType1.getValue(); //可见类型
		  							var commentValue_f = commentInWin1.getValue();//备注
		  							var newIid_f = newIidText.getValue();//iid
		  							var newUuid_f = uuidText.getValue();
		  							if(categoryValue_f == null || categoryValue_f == ''){
										Ext.Msg.alert('提示',"请选择类别！");
										return ;
									}
									if(typeValue_f==null || typeValue_f == ''){
										Ext.Msg.alert('提示',"请选择类型！");
										return ;
									}
									if(commdDescValue_f==null || commdDescValue_f == ''){
										Ext.Msg.alert('提示',"命令说明不能为空！");
										return ;
									}
									if(commdValue_f==null || commdValue_f ==''){
										Ext.Msg.alert('提示',"命令不能为空！");
										return ;
									}
									if(platformValue_f.length == 0){
										Ext.Msg.alert('提示',"请选择适用平台！");
										return ;
									}
		  							if(visibleTypeValue_f==null  || visibleTypeValue_f =='' ){
										Ext.Msg.alert('提示',"请选择可见类型！");
										return ;
									}
									let timeoutValue = timeout.getValue()==null?-1:timeout.getValue();
									if(timeoutValue){
										if (!checkIsInteger(timeoutValue)) {
//											setMessage('请输入数字类型的超时时间！');
											Ext.Msg.alert('提示','请输入正确格式的超时时间！');
											return;
										}
									}
								    Ext.Ajax.request({
							            url: 'updateEditCommdForWhiteList.do',
							            method: 'POST',
							            sync: true,
							            params: {
							            	iid: newIid_f,
							            	uuid:newUuid_f,
							            	sysId : categoryValue_f,//类别 码值
							            	sysName : sysName, //类别 显示值
							            	bussType : typeDisValue, //类型 显示值
							                bussTypeId : typeValue_f, //类型 码值
							                scriptName :commdDescValue_f,
							                serverName :commdDescValue_f,
							                //commdDescValueParm : commdDescValue_f,
							                content :  commdValue_f,//命令
							                paramFlagParm: paramFlagValue_f,//有无参数
							                usePlantForm: platformValue_f,//适用平台
							                visibleTypeParm: visibleTypeValue_f,//可见类型
							                funcDesc :commentValue_f,//备注
							                checkRadio : '',
							                ignoreTipCmd :0,
							                params: '[]',
											timeout:timeoutValue
//							                attachmentIds: '',
//							                excepResult: '',
//											errExcepResult: ''
						            },
						            success: function(response, request) {
						                var success = Ext.decode(response.responseText).success;
						                var commdExits = Ext.decode(response.responseText).commdExits;
						                if(commdExits) {
						                	 
						                	Ext.Msg.alert('提示', '命令重复！');
						                	return;
						                } 
						                if(success){
						                	myWhiteListStore.reload();
					                		Ext.Msg.alert('提示', '保存成功！');
					                		EditCommWin.close();
					                		//this.up("window").close();
					                		return;
						                }else{
						                	var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;
						                	if(hasScreenKeyWord) {
						                		Ext.Msg.alert('提示', "命令中有关键命令，无法保存！");
						                		return;
						                	}
						                	Ext.Msg.alert('提示', '保存失败！');
						                	return;
						                }	 	
						            },
						            failure: function(result, request) {
						                secureFilterRs(result, "保存失败！");
						            }
						        });
						}
				    },{
				    	xtype: "button", 
			  			text: "重置", 
			  			handler:  function () {
							//editreset();
			  				recoverySet(map);
						}
				    },{
				    	xtype: "button", 
			  			text: "取消", 
			  			handler: function () {
			  				 EditCommWin.close();
			  			}
				    }]
				});
  		   //}
	 	
	  EditCommWin.show();
	 }
 
});
 