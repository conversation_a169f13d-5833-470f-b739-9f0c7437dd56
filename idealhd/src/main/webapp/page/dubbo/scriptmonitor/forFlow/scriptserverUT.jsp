<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<%
    List listOper = (List) request.getAttribute("opers");
	List listShow = (List) request.getAttribute("shows");
%>
<html>
<head>
<script type="text/javascript">
	var coatId2ForFlow = '<%=request.getAttribute("actId")%>';
	var actName2ForFlow = '<%=request.getAttribute("actName")%>';
	var state2ForFlow = '<%=request.getAttribute("state")%>';
	var flag2ForFlow = '<%=request.getAttribute("flag")%>';
	var cnt2ForFlow=0;
   var shows2ForFlow =  [ {	fieldLabel : '人工提醒', labelWidth : 65, 	name : 'wt_C',	xtype : "displayfield",width:250,value : actName2ForFlow , padding:'0 10 10 0'}];
//var opers=[];
   <%if (null != listShow && listShow.size() > 0) {
				for (int i = 0; i < listShow.size(); i++) {
					Map map = (Map) listShow.get(i);%>
	   	var showInfo2ForFlow = {};
	   	showInfo2ForFlow.fieldLabel = '描述信息';
	   	showInfo2ForFlow.value = '<%=(String) map.get("value")%>';
	   	showInfo2ForFlow.labelWidth = 65;
	   	showInfo2ForFlow.width = 530;
	   	showInfo2ForFlow.name='<%=(String) map.get("name")%>';
	   	showInfo2ForFlow.xtype="textarea";
	    //showInfo.disabled=true;
	   	showInfo2ForFlow.height=230;
	   	shows2ForFlow.push(showInfo2ForFlow);
   			<%}
			}%>
   
	var operInfo2ForFlow =[];
   <%if (null != listOper && listOper.size() > 0) {
				for (int i = 0; i < listOper.size(); i++) {
					Map map = (Map) listOper.get(i);%>
					cnt=<%=listOper.size()%>;
		var aaa2ForFlow = {};			
		aaa2ForFlow.boxLabel = '<%=(String) map.get("name")%>';
		aaa2ForFlow.name = 'exec';
		aaa2ForFlow.padding = '0 10 0 10';
		aaa2ForFlow.checked=true;
		aaa2ForFlow.hidden=true;
		aaa2ForFlow.inputValue='<%=(String) map.get("id")%>';
	operInfo2ForFlow.push(aaa2ForFlow);
<%}
			}%>
	var opitem2ForFlow = {
		layout : 'column',
		xtype : 'checkboxgroup',
		name : 'chkexec',
		id : 'chkexec',
		columns : 2,
		items : operInfo2ForFlow
	};
	shows2ForFlow.push(opitem2ForFlow);
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/forFlow/scriptserverUT.js">
	
</script>
</head>
<body>
	<div id="sss2ForFlow"></div>
	<div id="scriptserverUT_area2ForFlow" style="width: 100%; height: 100%"></div>
</body>
</html>