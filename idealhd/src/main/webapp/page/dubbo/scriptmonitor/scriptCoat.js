var golbalParamName;
//定义workitemid传参
var paramWorkitemId = null;
var variableTab;
var functionTab;
Ext.onReady(function () {
    var scriptmonitor_store;
    var personExcute_window;
    var scsearch_form;
    var execUserConfigWindow;
    var execUserConfigForm = null;
    var execUserNameText = null;
    var labels;
    destroyRubbish();
    var flag = 0; // 测试
    /*Ext.define('startUserModel', {
		extend : 'Ext.data.Model',
		fields : [{
		  name : 'iid',
		  type : 'string'
		}, {
		  name : 'iusername',
		  type : 'string'
		}]

	});*/

    var stateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        },
            {
                "id": "101",
                "name": "完成"
            },
            /*		        {
		            "id": "20",
		            "name": "正常完成"
		        },
		        {
		            "id": "40",
		            "name": "异常完成"
		        },*/
            {
                "id": "102",
                "name": "运行"
            },
            /*		        {
		            "id": "10",
		            "name": "正常运行"
		        },
		        {
		            "id": "50",
		            "name": "异常运行"
		        },*/
            {
                "id": "30",
                "name": "异常"
            },
            {
                "id": "60",
                "name": "已终止"
            }]
    });


    var serviceName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '服务名称',
        emptyText: '--请输入脚本名称--',
        labelWidth: 65,
        padding: '5',
        width: '20%',
        value: filter_scriptName_forScriptCoat,
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var stateCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'state',
        padding: '5',
        labelWidth: 65,
        queryMode: 'local',
        fieldLabel: '执行状态',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择执行状态--',
        store: stateStore,
        width: '15%',
        labelAlign: 'right',
        listeners: {
            afterRender: function (combo) {
                if (filter_state_forScriptCoat == '-1') {
                    combo.setValue(stateStore.getAt(0).data.id);
                } else if (filter_state_forScriptCoat == '101') {
                    combo.setValue(stateStore.getAt(1).data.id);
                } else if (filter_state_forScriptCoat == '102') {
                    combo.setValue(stateStore.getAt(2).data.id);
                } else if (filter_state_forScriptCoat == '30') {
                    combo.setValue(stateStore.getAt(3).data.id);
                } else if (filter_state_forScriptCoat == '60') {
                    combo.setValue(stateStore.getAt(4).data.id);
                }
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
    var startTime = new Ext.form.field.Date({
        name: 'startTime',
        fieldLabel: '开始时间',
        labelWidth: 65,
        padding: '5',
        width: '22%',
        labelAlign: 'right',
        format: 'Y-m-d H:i:s',
//        value:Ext.util.Format.date(Ext.Date.add(date1,Ext.Date.DAY,-6),"Y-m-d H:i:s"),
        value: (filter_startTime_forScriptCoat != null && filter_startTime_forScriptCoat != undefined && filter_startTime_forScriptCoat != '') ? filter_startTime_forScriptCoat : Ext.util.Format.date(Ext.Date.add(date1, Ext.Date.DAY, -6), "Y-m-d H:i:s"),
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    var date2 = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1);
    var endTime = new Ext.form.field.Date({
        name: 'endTime',
        fieldLabel: '结束时间',
        labelWidth: 65,
        padding: '5',
        width: '22%',
        labelAlign: 'right',
        format: 'Y-m-d H:i:s',
        value: filter_endTime_forScriptCoat,
//    	value:Ext.util.Format.date(Ext.Date.add(date2,Ext.Date.DAY,-1),"Y-m-d H:i:s"),
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });

    scsearch_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls: 'customize_gray_back',
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            border: false,
            dock: 'top',
            items: [serviceName,
                stateCb,
                startTime,
                endTime,
                {
                    xtype: 'button',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        pageBar.moveFirst();
                    }
                },
                {
                    xtype: 'button',
                    text: '清空',
                    cls: 'Common_Btn',
                    handler: function () {
                        clearQueryWhere();
                    }
                },
                {
                    xtype: 'button',
                    text: '返回',
                    hidden: forScriptFlow_forScriptCoat != 1 && forMyScript_forScriptCoat != '脚本编写测试' && forMyScript_forScriptCoat != '脚本测试',
                    cls: 'Common_Btn',
                    handler: function () {
                        if (forMyScript_forScriptCoat == '脚本编写测试') {
                            forwardMyScript();
                        } else if (forMyScript_forScriptCoat == '脚本测试') {
                            forwardScriptTry();
                        } else {
                            forwardtestmain();
                        }
                    }
                }
            ]
        }]

    });

    Ext.define('scriptmonitorData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
            {
                name: 'serviceName',
                type: 'string'
            },
            {
                name: 'state',
                type: 'int'
            },
            {
                name: 'cata',
                type: 'int'
            },
            {
                name: 'flowId',
                type: 'int'
            },
            {
                name: 'actNo',
                type: 'int'
            },
            {
                name: 'startUser',
                type: 'string'
            },
            {
                name: 'startTime',
                type: 'string'
            },
            {
                name: 'endTime',
                type: 'string'
            },
            {
                name: 'actType',
                type: 'string'
            },
            {
                name: 'serverNum',
                type: 'int'
            },
            {//实际上是uuid
                name: 'scriptTestID',
                type: 'string'
            },
            {
                name: 'iscriptIID',
                type: 'string'
            },
            {
                name: 'scriptTestStatus',
                type: 'int'
            },
            {
                name: 'scriptType',
                type: 'string'
            },
            {
                name: 'scriptName',
                type: 'string'
            },
            {
                name : 'outputCount',
                type : 'int'
            }]
    });

    scriptmonitor_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptCoatList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    /**
     * 参数 start
     */

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [/*{
			name: 'iid',
			type: 'int'
		},*/{
            name: 'iorder',
            type: 'int'
        }, {
            name: 'ivalue',
            type: 'string'
        }]
    });

//增加参数信息显示
    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getTaskValParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function (store, options) {
        var new_params = {
            workitemid: paramWorkitemId
        };
        Ext.apply(paramStore.proxy.extraParams, new_params);
    });


    var paramColumns = [
        {
            text: '顺序',
            dataIndex: 'iorder',
            width: 40
        },
        {
            text: '参数值',
            dataIndex: 'ivalue',
            width: 150,
            flex: 1
        }
    ];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var paramGrid = Ext.create('Ext.grid.Panel', {
        //title: "参数",
        width: '90%',
        region: 'center',
        //cls:'window_border panel_space_top panel_space_left panel_space_right',
//        height: 280,
//        margin: 10,
//        collapsible : true,
        store: paramStore,
        plugins: [cellEditing],
        //margin:'0 10 5 10',
        border: true,
        columnLines: true,
        columns: paramColumns
    });

    function getParamsValToShow() {
        paramStore.reload();
        showParamsWin = Ext.create('Ext.window.Window', {
            title: '执行参数',
            //autoScroll : true,
            // modal : true,
            // resizable : false,
            closeAction: 'hide',
            layout: 'border',
            width: 500,
            height: 320,
            items: [paramGrid]
        });
        showParamsWin.show();

    }

    /**
     * 参数 end
     */


    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
        {
            text: '执行状态',
            dataIndex: 'state',
            width: 100,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == -1) {
                    backValue = '<span class="Not_running State_Color">未运行</span>';
                } else if (value == 10) {
                    backValue = '<span class="Run_Green State_Color">运行</span>';
                } else if (value == 20 || value == 5) {
                    backValue = "<span class='Complete_Green State_Color'>完成</span>"
                } else if (value == 30) {
                    backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
                } else if (value == 40) {
                    backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
                } else if (value == 50) {
                    backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
                } else if (value == 60) {
                    backValue = '<span class="Kill_red State_Color">已终止</span>';
                }
                return backValue;
            }
        },
        {
            text: '实例主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: 'actType',
            dataIndex: 'actType',
            hidden: true
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            width: 100,
            flex: 1
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            hidden: !scriptNameSwitch_forScriptCoat,
            flex: 1
        },
        {
            text: '启动人',
            dataIndex: 'startUser',
            width: 120
        },
        {
            text: '开始时间',
            dataIndex: 'startTime',
            width: 150
        },
        {
            text: '结束时间',
            dataIndex: 'endTime',
            width: 150
        }, {
            text: '脚本主键',
            dataIndex: 'iscriptIID',
            hidden: true
        }, {
            text: '脚本主键uuid',
            dataIndex: 'scriptTestID',
            hidden: true
        }, {
            text: '脚本状态',
            dataIndex: 'scriptTestStatus',
            width: 80,
            renderer: function (value, p, record, rowIndex) {
                if (value == -1) {
                    return '<font color="#F01024">草稿</font>';
                } else if (value == 1) {
                    return '<font color="#0CBF47">已上线</font>';
                } else if (value == 2) {
                    return '<font color="#FFA602">审核中</font>';
                } else if (value == 3) {
                    return '<font color="#13B1F5">已共享</font>';
                } else if (value == 9) {
                    return '<font color="">已共享未发布</font>';
                } else if (value == 0) {
                    return '<font color="#CCCCCC">已删除</font>';
                } else {
                    return '<font color="#CCCCCC">未知</font>';
                }
            }
        },
        //					                { text: '服务器个数',  dataIndex: 'serverNum',width:100},
        /*{ text: '类别',  dataIndex: 'cata',width:50,renderer:function(value,p,record){
					                	var backValue = "";
					                	if(value==0){
					                		backValue = "测试";
					                	}else if(value==1){
					                		backValue = "生产";
					                	}
					                	return backValue;
					                }},*/
        {
//        text: '操作',
//        dataIndex: 'sysOperation',
//        width: 255,
//        renderer: function(value, p, record) {
//            var coatid = record.get('iid');
//            var cata = 0; //record.get('cata');
//            var actType = record.get('actType');
//            var actNo = record.get('actNo');
//            var flowId = record.get('flowId');
//            var state = record.get('state');
//            var serviceId = record.get('iscriptIID');
//            var iscriptUuid = record.get('scriptTestID');
//            var scriptState = record.get('scriptTestStatus');
//            var scriptType = record.get('scriptType');
//            if (actType == '0') {
//            	if(showEditSwitch){
//            		return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardruninfo2(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' +
//        			'<a href="javascript:void(0)" onclick="scriptCoatLook('+coatid+')"><img src="images/monitor_bg.png" align="absmiddle" class="script_text"></img>脚本</a>&nbsp;&nbsp;'+
//        			'<a href="javascript:void(0)" onclick="resultExport2(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>' + '</span>';
//            	}else{
//            		if (scriptState == '-1' && scriptType != 'sql') {
//            			return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardruninfo2(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' +
//            			'<a href="javascript:void(0)" onclick="scriptCoatLook('+coatid+')"><img src="images/monitor_bg.png" align="absmiddle" class="script_text"></img>脚本</a>&nbsp;&nbsp;'+
//            			'<a href="javascript:void(0)" onclick="resultExport2(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>&nbsp;&nbsp;' +
//            			'<a href="javascript:void(0)" onclick="editScriptForScriptCoat('+coatid+', '+serviceId+',\''+iscriptUuid+'\')"><img src="images/monitor_bg.png" align="absmiddle" class="script_edit"></img>编辑</a>'+'</span>';
//            		} else{
//            			return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardruninfo2(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' +
//            			'<a href="javascript:void(0)" onclick="scriptCoatLook('+coatid+')"><img src="images/monitor_bg.png" align="absmiddle" class="script_text"></img>脚本</a>&nbsp;&nbsp;'+
//            			'<a href="javascript:void(0)" onclick="resultExport2(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>' + '</span>';
//            		}
//            	}
//            } else if ('2' == actType) {
//                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardUTruninfo(' + flowId + ', ' + coatid + ', '+ actNo + ', '+ state + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>' + '</span>';
//            }
//
//        }
            text: '操作',
            xtype: 'actiontextcolumn',
            dataIndex: 'sysOperation',
            width:430,
            items: [{
                text: '详情',
                iconCls: 'monitor_search',
                handler: function (grid, rowIndex) {
                    var coatid = grid.getStore().data.items[rowIndex].data.iid;
                    var cata = grid.getStore().data.items[rowIndex].data.cata;
                    var actType = grid.getStore().data.items[rowIndex].data.actType;
                    var actNo = grid.getStore().data.items[rowIndex].data.actNo;
                    var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                    var state = grid.getStore().data.items[rowIndex].data.state;
                    var serviceId = grid.getStore().data.items[rowIndex].data.iscriptIID;
                    var iscriptUuid = grid.getStore().data.items[rowIndex].data.scriptTestID;
                    var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    if (actType == '0') {
                        forwardruninfo2(coatid, cata, scriptType);
                    } else {
                        forwardUTruninfo(flowId, coatid, actNo, state, cata);
                    }
                }
            },
                {
                    text: '脚本',
                    iconCls: 'script_text',
                    getClass: function (v, metadata, record) {
                        if (record.data.actType == 2) {
                            return 'x-hidden';
                        }
                    },
                    handler: function (grid, rowIndex) {
                        var coatid = grid.getStore().data.items[rowIndex].data.iid;
                        var cata = grid.getStore().data.items[rowIndex].data.cata;
                        var actType = grid.getStore().data.items[rowIndex].data.actType;
                        var actNo = grid.getStore().data.items[rowIndex].data.actNo;
                        var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                        var state = grid.getStore().data.items[rowIndex].data.state;
                        var serviceId = grid.getStore().data.items[rowIndex].data.iscriptIID;
                        var iscriptUuid = grid.getStore().data.items[rowIndex].data.scriptTestID;
                        var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus;
                        var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                        Ext.Ajax.request({
                            url: 'scriptService/queryScriptLabels.do',
                            method: 'POST',
                            async: false,
                            params: {
                                uuid: iscriptUuid
                            },
                            success: function (response, request) {
                                var lab = Ext.decode(response.responseText).dataList;
                                labels = lab.join(',')
                            },
                            failure: function (result, request) {
                            }
                        });
                        scriptCoatLook(coatid,labels);
                    }
                }, {
                    text: '导出',
                    iconCls: 'monitor_export',
                    getClass: function (v, metadata, record) {
                        if (record.data.actType == 2) {
                            return 'x-hidden';
                        }
                    },
                    handler: function (grid, rowIndex) {
                        var coatid = grid.getStore().data.items[rowIndex].data.iid;
                        var cata = grid.getStore().data.items[rowIndex].data.cata;
                        var actType = grid.getStore().data.items[rowIndex].data.actType;
                        var actNo = grid.getStore().data.items[rowIndex].data.actNo;
                        var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                        var state = grid.getStore().data.items[rowIndex].data.state;
                        var serviceId = grid.getStore().data.items[rowIndex].data.iscriptIID;
                        var iscriptUuid = grid.getStore().data.items[rowIndex].data.scriptTestID;
                        var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus;
                        var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                        resultExport2(coatid, cata, scriptType);
                    }
                }, {
                    text: '编辑',
                    iconCls: 'script_edit',
                    getClass: function (v, metadata, record) {
                        if (record.data.actType != '0' || showEditSwitch_forScriptCoat || record.data.scriptTestStatus != '-1' || record.data.scriptType == 'sql' || forMyScript_forScriptCoat == '脚本测试') {
                            return 'x-hidden';
                        }
                    },
                    handler: function (grid, rowIndex) {
                        var coatid = grid.getStore().data.items[rowIndex].data.iid;
                        var cata = grid.getStore().data.items[rowIndex].data.cata;
                        var actType = grid.getStore().data.items[rowIndex].data.actType;
                        var actNo = grid.getStore().data.items[rowIndex].data.actNo;
                        var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                        var state = grid.getStore().data.items[rowIndex].data.state;
                        var serviceId = grid.getStore().data.items[rowIndex].data.iscriptIID;
                        var iscriptUuid = grid.getStore().data.items[rowIndex].data.scriptTestID;
                        var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus;
                        var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                        editScriptForScriptCoat(coatid, serviceId, iscriptUuid);
                    }
                }, {
                    text: '参数',
                    iconCls: 'monitor_search',
                    handler: function (grid, rowIndex, colIndex) {
                        //获取iwrokitemid
                        var iworkitemidparams = grid.getStore().data.items[rowIndex].data.iid;
                        paramWorkitemId = iworkitemidparams;
                        getParamsValToShow();
                    }
                }, {
                    text: '脚本输出',
                    iconCls: 'monitor_export',
                    getClass: function (v, metadata, record) {
                        var outputCount = record.get('outputCount');
                        if(scriptfunctionoutputswitch && outputCount>0){

                        }else {
                            return 'x-hidden';
                        }
                    },
                    handler: function (grid, rowIndex) {
                        var coatid = grid.getStore().data.items[rowIndex].data.iid;
                        var cata = grid.getStore().data.items[rowIndex].data.cata;
                        var actType = grid.getStore().data.items[rowIndex].data.actType;
                        var actNo = grid.getStore().data.items[rowIndex].data.actNo;
                        var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                        var state = grid.getStore().data.items[rowIndex].data.state;
                        var serviceId = grid.getStore().data.items[rowIndex].data.iscriptIID;
                        var iscriptUuid = grid.getStore().data.items[rowIndex].data.scriptTestID;
                        var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus;
                        var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                        var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;

                        var scriptName = scsearch_form.getForm().findField("scriptName").getValue();
                        var state = scsearch_form.getForm().findField("state").getValue();
                        var startTime = scsearch_form.getForm().findField("startTime").getRawValue();
                        var endTime = scsearch_form.getForm().findField("endTime").getRawValue();
                        contentPanel.getLoader().load({
                            url: "scriptOutputPage.do",
                            scripts: true,
                            params: {
                                flowId: flowId_forScriptCoat,
                                iflowId:flowId,
                                coatid: coatid,
                                forScriptFlow: forScriptFlow_forScriptCoat,
                                forMyScript: forMyScript_forScriptCoat,
                                flag: flag,
                                filter_scriptName: scriptName,
                                filter_state: state,
                                filter_startTime: startTime,
                                filter_endTime: endTime,
                                filter_serviceName: filter_serviceName_forScriptCoat,
                                filter_serviceState: filter_serviceState_forScriptCoat,
                                filter_serviceStartTime: filter_serviceStartTime_forScriptCoat,
                                filter_serviceEndTime: filter_serviceEndTime_forScriptCoat,
                                scriptType: scriptType,
                                serviceName: serviceName,
                                type:'testHistory'
                            }
                        });

                    }
                }]
        }];

    scriptmonitor_store.on('beforeload', function (store, options) {
        var new_params = {
            scriptName: scsearch_form.getForm().findField("scriptName").getValue(),
            flowId: flowId_forScriptCoat,
            iid: sprictCoatId_forScriptCoat, //主键id
            //	    	scriptName:scsearch_form.getForm().findField("scriptName").getValue(),
            //	    	startUser:scsearch_form.getForm().findField("startUser").getValue(),
            state: stateCb.getValue(),
            cata: 0,
            ifrom: 0,
            menuFrom: 'testHistory',
            //scsearch_form.getForm().findField("cata").getValue(),
            startTime: startTime.getValue(),
            endTime: endTime.getValue(),
            forScriptFlow: forScriptFlow_forScriptCoat
        };

        Ext.apply(scriptmonitor_store.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitor_store,
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });

    var scriptmonitor_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: scriptmonitor_store,
        cls: 'customize_panel_back',
        border: false,
        padding: grid_space,
        columnLines: true,
        columns: scriptmonitor_columns,
//        bbar: pageBar
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar'
        /*,
	    viewConfig:{
			getRowClass:function(record,rowIndex,rowParams,arriveStore){
				var cls = '';
				if(record.data.state==10){
					cls = 'row_Blue';
				}else if(record.data.state==20){
					cls = 'row_Green';
				}else if(record.data.state==30){
					cls = 'row_Red';
				}else if(record.data.state==40){
					cls = 'row_Red';
				}else if(record.data.state==50){
					cls = 'row_Red';
				}else if(record.data.state==60){
					cls = 'row_Gray';
				} else {
					cls = 'row_Gray';
				}
				return cls;
			}
		}*/
    });

    let renderTo = "scriptcoatmonitor_area_forScriptCoat";
    if(isTabSwitch){
        renderTo += scnow;
    }
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: renderTo,
        layout: 'border',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [scsearch_form, scriptmonitor_grid]
    });

    function clearQueryWhere() {
        scsearch_form.getForm().findField("scriptName").setValue('');
        //    	scsearch_form.getForm().findField("scriptName").setValue(''),
        //    	scsearch_form.getForm().findField("startUser").setValue(''),
        stateCb.setValue("-1");
        //    	scsearch_form.getForm().findField("cata").setValue("-1"),
        startTime.setValue('');
        endTime.setValue('');
    }

    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });

    function utStartSuccessCallback() {
        scriptmonitor_store.reload();
    }

    function forwardUTruninfo(flowId, coatid, actNo, state, cata) {
        personExcute_window = Ext.create('Ext.window.Window', {
            title: '人工提醒',
            autoScroll: true,
            modal: true,
            closeAction: 'destroy',
            buttonAlign: 'center',
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            width: 600,
            height: 400,
            loader: {
                url: 'scriptServiceUT.do',
                params: {
                    actNo: actNo,
                    coatid: coatid,
                    flowId: flowId,
                    flag: 0,
                    state: state
                },
                autoLoad: true,
//    			autoDestroy : true,
                scripts: true
            }
        }).show();
    }

    function forwardruninfo2(coatid, flag, scriptType) {
        var scriptName = scsearch_form.getForm().findField("scriptName").getValue();
        var state = scsearch_form.getForm().findField("state").getValue();
        var startTime = scsearch_form.getForm().findField("startTime").getRawValue();
        var endTime = scsearch_form.getForm().findField("endTime").getRawValue();
        contentPanel.getLoader().load({
            url: "forwardscriptserver.do",
            scripts: true,
            params: {
                flowId: flowId_forScriptCoat,
                coatid: coatid,
                forScriptFlow: forScriptFlow_forScriptCoat,
                forMyScript: forMyScript_forScriptCoat,
                flag: flag,
                filter_scriptName: scriptName,
                filter_state: state,
                filter_startTime: startTime,
                filter_endTime: endTime,
                filter_serviceName: filter_serviceName_forScriptCoat,
                filter_serviceState: filter_serviceState_forScriptCoat,
                filter_serviceStartTime: filter_serviceStartTime_forScriptCoat,
                filter_serviceEndTime: filter_serviceEndTime_forScriptCoat,
                scriptType: scriptType
            }
        });
    }

    function forwardtestmain() {
        contentPanel.getLoader().load({
            url: "scriptMonitorForFlowTest.do",
            scripts: true,
            params: {
                filter_serviceName: filter_serviceName_forScriptCoat,
                filter_serviceState: filter_serviceState_forScriptCoat,
                filter_serviceStartTime: filter_serviceStartTime_forScriptCoat,
                filter_serviceEndTime: filter_serviceEndTime_forScriptCoat
            }
        });
    }

    //跳转到我的脚本
    function forwardMyScript() {
        contentPanel.getLoader().load({
            url: "forwardScriptServiceRelease.do",
            scripts: true
        });
    }

    //跳转到脚本测试
    function forwardScriptTry() {
        contentPanel.getLoader().load({
            url: "scriptForTry.do"
        });
    }

    function resultExport2(coatid, flag, scriptType) {
        window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag=' + flag + "&scriptType=" + scriptType + "&workitemid=0";
    }

    function scriptCoatLook(coatid,labels) {
        Ext.Ajax.request({
            url: 'queryOneServiceForViewTest.do',
            method: 'POST',
            params: {
                iid: coatid
            },
            success: function (response, request) {
                var serviceId = Ext.decode(response.responseText).serviceId;

                var DetailWinTi = Ext.create('widget.window', {
                    title: '详细信息',
                    closable: true,
                    closeAction: 'destroy',
                    width: contentPanel.getWidth(),
                    minWidth: 350,
                    height: contentPanel.getHeight(),
                    draggable: false,
                    // 禁止拖动
                    resizable: false,
                    // 禁止缩放
                    modal: true,
                    loader: {
                        url: 'queryOneServiceForView.do',
                        params: {
                            iid: serviceId,
                            label:labels,
                            //flag: 0,
                            hideReturnBtn: 1
                        },
                        autoLoad: true,
                        scripts: true
                    }
                });
                DetailWinTi.show();
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function editScriptForScriptCoat(coatid, viewBaseScriptiid, iscriptUuid) {
        var attachmentIds = [];
        var scriptT;
        var upldWin;
        var upLoadformPane = '';
        var language;
        Ext.define('editScriptModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long'
            },
                {
                    name: 'serviceName',
                    type: 'string'
                },
                {
                    name: 'sysName',
                    type: 'string'
                },
                {
                    name: 'bussName',
                    type: 'string'
                },
                {
                    name: 'scriptType',
                    type: 'string'
                },
                {
                    name: 'scriptName',
                    type: 'string'
                },
                {
                    name: 'servicePara',
                    type: 'string'
                },
                {
                    name: 'serviceState',
                    type: 'string'
                },
                {
                    name: 'excepResult',
                    type: 'string'
                },
                {
                    name: 'errExcepResult',
                    type: 'string'
                },
                {
                    name: 'content',
                    type: 'string'
                }]
        });
        var editScriptStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            autoDestroy: true,
            pageSize: 20,
            model: 'editScriptModel',
            proxy: {
                type: 'ajax',
                url: 'scriptService/queryOneService.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        editScriptStore.on('beforeload', function (store, options) {
            var queryparams = {
                iid: viewBaseScriptiid
            };
            Ext.apply(editScriptStore.proxy.extraParams, queryparams);
        });
        editScriptStore.on('load', function (store, options, success) {
            var reader = store.getProxy().getReader();
            scriptT = reader.jsonData.scriptType;
            if (scriptT == 'sh') {
                mainP.setTitle("脚本内容 (脚本类型：shell)");
                language = '1'
                if (scriptEditBookSwitch){
                    functionTab.ipage.moveFirst();
                }
                editor.setOption("mode", 'shell');
            } else if (scriptT == 'bat') {
                mainP.setTitle("脚本内容 (脚本类型：bat)");
                language='all';
                if (scriptEditBookSwitch){
                    functionTab.ipage.moveFirst();
                }
                editor.setOption("mode", 'bat');
            } else if (scriptT == 'py') {
                mainP.setTitle("脚本内容 (脚本类型：python)");
                language='2';
                if (scriptEditBookSwitch){
                    functionTab.ipage.moveFirst();
                }
                editor.setOption("mode", 'python');
            } else if (scriptT == 'SQL') {
                mainP.setTitle("脚本内容 (脚本类型：sql)");
                language='all';
                if (scriptEditBookSwitch){
                    functionTab.ipage.moveFirst();
                }
                editor.setOption("mode", 'sql');
            } else if (scriptT == 'perl') {
                mainP.setTitle("脚本内容 (脚本类型：perl)");
                language='all';
                if (scriptEditBookSwitch){
                    functionTab.ipage.moveFirst();
                }
                editor.setOption("mode", 'text/x-perl');
            }
            editor.setOption('value', reader.jsonData.content);
        });

        var mainP = Ext.create('Ext.panel.Panel', {
            region: 'center',
            //cls:'customize_panel_back',
            cls: 'window_border panel_space_top panel_space_left panel_space_right',
            border: true,
            title: "脚本内容",
            collapsible: false,
            html: '<textarea id="codeEditViewForScriptCoat" value style="width: 100%;height:100%;"></textarea>'
        });

        Ext.define('paramModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            },
                {
                    name: 'paramType',
                    type: 'string'
                },
                {
                    name: 'parameterName',
                    type: 'string'
                },
                {
                    name: 'paramDefaultValue',
                    type: 'string'
                },
                {
                    name: 'ruleName',
                    type: 'string'
                },
                {
                    name: 'paramDesc',
                    type: 'string'
                },
                {
                    name: 'paramOrder',
                    type: 'int'
                }]
        });

        Ext.define('paramManangerModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'long'
            },
                {
                    name: 'parameterName',
                    type: 'string'
                },
                {
                    name: 'parameterValue',
                    type: 'string'
                },
                {
                    name: 'parameterDesc',
                    type: 'string'
                }]
        });

        Ext.define('paramManangerModel2', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'paravalue',
                    type: 'string'
                }]
        });

        var paramStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            autoDestroy: true,
            pageSize: 10,
            model: 'paramModel',
            proxy: {
                type: 'ajax',
                url: 'getAllScriptParams.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        paramStore.on('beforeload', function (store, options) {
            var new_params = {
                scriptId: iscriptUuid
            };

            Ext.apply(paramStore.proxy.extraParams, new_params);
        });
        var paramTypeStore = Ext.create('Ext.data.Store', {
            fields: ['name'],
            data: [{
                "name": "枚举"
            },
                {
                    "name": "IN-string"
                },
                {
                    "name": "IN-string(加密)"
                },
                {
                    "name": "IN-int"
                },
                {
                    "name": "IN-float"
                },
                {
                    "name": "OUT-string"
                },
                {
                    "name": "OUT-int"
                },
                {
                    "name": "OUT-float"
                }]
        });
        var enumValueStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            // autoDestroy: true,
            // pageSize: 10,
            model: 'paramManangerModel',
            proxy: {
                type: 'ajax',
                url: 'getParameterList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    // totalProperty: 'total'
                }
            }
        });

        var defaultValueStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            model: 'paramManangerModel2',
            proxy: {
                type: 'ajax',
                url: 'getScriptParameterList.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        defaultValueStore.on('beforeload', function (store, options) {
            var new_params = {
                paramName: golbalParamName
            };
            Ext.apply(defaultValueStore.proxy.extraParams, new_params);
        });

        var paramTypeCombo = Ext.create('Ext.form.field.ComboBox', {
            margin: '5',
            store: paramTypeStore,
            queryMode: 'local',
            width: 600,
            forceSelection: true,
            // 要求输入值必须在列表中存在
            typeAhead: true,
            // 允许自动选择
            displayField: 'name',
            valueField: 'name',
            triggerAction: "all",
            editable: false,
            listeners: {
                change: function (field, newValue, oldValue) {
                    if (oldValue == 'IN-string(加密)' && newValue != 'IN-string(加密)') {
                        var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                        paramDefaultValue.set("paramDefaultValue", "");
                        paramDefaultValue.set("parameterName", "");
                    }
                    if (oldValue != newValue) {
                        var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                        paramDefaultValue.set("paramDefaultValue", "");
                        paramDefaultValue.set("parameterName", "");
                        paramDefaultValue.set("ruleName", "");
                    }
                }
            }
        });
        Ext.define('parameterCheckModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            }, {
                name: 'ruleName',
                type: 'string'
            }, {
                name: 'checkRule',
                type: 'string'
            }, {
                name: 'ruleDes',
                type: 'string'
            }]
        });
        var store = Ext.create('Ext.data.Store', {
            autoLoad: true,
            model: 'parameterCheckModel',
            proxy: {
                type: 'ajax',
                url: 'getScriptParameterCheck.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        var paramColumns = [
            {
                text: '主键',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '类型',
                dataIndex: 'paramType',
                editor: paramTypeCombo,
                width: 80,
                listeners: {
                    change: function (field, newValue, oldValue) {
                        if (oldValue == 'IN-string(加密)' && newValue != 'IN-string(加密)') {
                            var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                            paramDefaultValue.set("paramDefaultValue", "");
                            paramDefaultValue.set("parameterName", "");
                        }
                        if (oldValue != newValue) {
                            var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
                            paramDefaultValue.set("paramDefaultValue", "");
                            paramDefaultValue.set("parameterName", "");
                            paramDefaultValue.set("ruleName", "");
                        }
                    }
                },
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    var coun = '';
                    if (value == 'IN-string(加密)') {
                        coun = StringToPassword(record.get('paramDefaultValue'));
                    } else {
                        coun = record.get('paramDefaultValue');
                    }
                    let ruleMsg = bhParameterCheckSwitch?"<br>验证规则：" + record.get('ruleName'):"";
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型：" + record.get('paramType')
                        + "<br>枚举名：" +  record.get('parameterName')
                        + "<br>默认值：" + coun
                        + ruleMsg
                        + "<br>排序：" + record.get('paramOrder')
                        + "<br>描述：" + record.get('paramDesc'))
                        + '"';
                    return value;
                }
            },
            {
                dataIndex: 'parameterName',
                width: 80,
                text: '枚举名称',
                editable: false,
                editor: {}
            },
            {
                text: '默认值',
                dataIndex: 'paramDefaultValue',
                editor: {
                    allowBlank: true
                },
                width: 70,
                renderer: function (value, metaData, record) {
                    //alert(value);
                    let showValue = value;

                    let paramType = record.get('paramType');

                    if (paramType == 'IN-string(加密)') {
                        let xing = "";
                        let len = value.length;
                        for (let i = 0; i < len; i++) {
                            xing += "*";
                        }
                        showValue = xing;
                    }
                    return showValue;
                }
            },

            {
                dataIndex: 'ruleName',
                width: 120,
                text: '验证规则',
                hidden: !bhParameterCheckSwitch,
                editor: {
                    xtype: 'combobox',
                    store: store,
                    queryMode: 'local',
                    displayField: 'ruleName',
                    valueField: 'ruleName',
                    editable: false,
                }
            },
            {
                text: '顺序',
                dataIndex: 'paramOrder',
                editor: {
                    allowBlank: false
                },
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                },
                width: 50
            },
            {
                text: '描述',
                dataIndex: 'paramDesc',
                editor: {
                    allowBlank: true
                },
                flex: 1
            }];

        var selModel = Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        });
        var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
            clicksToEdit: 2
        });
        var paramGrid = Ext.create('Ext.grid.Panel', {
            width: '100%',
            height: (contentPanel.getHeight() - 190) * 0.5,
            title: "脚本参数",
            cls: 'window_border panel_space_top ',
            emptyText: '没有脚本参数',
            store: paramStore,
            selModel: selModel,
            plugins: [cellEditing],
            border: false,
            columnLines: true,
            columns: paramColumns,
            tools: [{
                type: 'plus',
                tooltip: '增加',
                handler: function (event, toolEl, panelHeader) {
                    var store = paramGrid.getStore();
                    var ro = store.getCount();
                    var p = {
                        iid: '',
                        paramOrder: ro + 1,
                        paramType: 'IN-string',
                        parameterName: '',
                        paramDefaultValue: '',
                        paramDesc: ''
                    };
                    store.insert(0, p);
                }
            },
                {
                    type: 'minus',
                    tooltip: '删除',
                    callback: function (panel, tool, event) {
                        var data = paramGrid.getView().getSelectionModel().getSelection();
                        if (data.length == 0) {
                            Ext.Msg.alert('提示', '请先选择您要操作的行!');
                            return;
                        } else {
                            Ext.Msg.confirm("请确认", "是否真的要删除参数？", function (button, text) {
                                if (button == "yes") {
                                    var deleteIds = [];
                                    $.each(data, function (index, record) {

                                        if (record.data.iid > 0) {
                                            deleteIds.push(record.data.iid);
                                        }
                                        paramStore.remove(data);
                                    });

                                }
                            });
                        }
                    }
                }],
            listeners: {
                //监听函数，在点击之前进行监听
                beforeedit: function (editor, e, eOpts) {

                    var columnIndex = e.column.dataIndex;
                    // 点击的当前行数据
                    var recordData = e.record.data;

                    var paramType = recordData.paramType;           // 是否为枚举类型
                    var parameterName = recordData.parameterName;   // 参数名称

                    // 判断当前操作表格所在的列是否为需要进行从新设置Editor的列
                    var columnBoo = columnIndex == "parameterName" || columnIndex == "paramDefaultValue";
                    var columnBooParameterName = columnIndex == "parameterName";
                    var columnBooparamDefaultValue = columnIndex == "paramDefaultValue"
                    // 当参数类型为“枚举”并且编辑列为“默认值”列时，重新加载默认值列对应的下拉框内容
                    if (paramType == "枚举" && columnIndex == "paramDefaultValue") {
                        golbalParamName = parameterName;
                        defaultValueStore.load();
                    }
                    // 判断如果为枚举类型，并且当前操作列为“参数名称”，设置单元格为下拉框
                    if (paramType == "枚举" && columnBooParameterName) {
                        e.column.setEditor({
                            xtype: 'combobox',
                            valueField: "parameterName",
                            displayField: "parameterName",
                            store: enumValueStore,
                            editable: false
                        });
                    }
                    if (paramType == "枚举" && columnBooparamDefaultValue) {
                        e.column.setEditor({
                            xtype: 'combobox',
                            valueField: "paravalue",
                            displayField: "paravalue",
                            store: defaultValueStore,
                            editable: false
                        });
                    }
                    // 判断如果不是枚举类型，并且当前操作列为“参数名称”，设置单元格为文本框
                    if (paramType != "枚举" && columnBoo) {
                        e.column.setEditor({
                            xtype: 'textfield',
                            readOnly: columnIndex == "parameterName" ? true : false,

                        })
                    }

                    if (paramType == "IN-string(加密)" && columnIndex == "paramDefaultValue") {
                        let pass = new Ext.form.TextField({
                            inputType: 'password'
                        });

                        e.column.setEditor(pass)
                    }
                }
            }
        });

        Ext.define('attachmentModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            },
                {
                    name: 'attachmentName',
                    type: 'string'
                },
                {
                    name: 'attachmentSize',
                    type: 'string'
                },
                {
                    name: 'attachmentUploadTime',
                    type: 'string'
                }]
        });

        var attachmentStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            autoDestroy: true,
            pageSize: 10,
            model: 'attachmentModel',
            proxy: {
                type: 'ajax',
                url: 'getAllScriptAttachment.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        attachmentStore.on('beforeload', function (store, options) {
            var new_params = {
                scriptId: iscriptUuid,
                ids: attachmentIds
            };

            Ext.apply(attachmentStore.proxy.extraParams, new_params);
        });

        attachmentStore.on('load', function (me, records, successful, eOpts) {
            attachmentIds = [];
            $.each(records, function (index, record) {
                attachmentIds.push(record.get('iid'));
            });
        });

        function removeByValue(arr, val) {
            for (var i = 0; i < arr.length; i++) {
                if (arr[i] == val) {
                    arr.splice(i, 1);
                    break;
                }
            }
        }

        var attachmentColumns = [
            {
                text: '主键',
                dataIndex: 'iid',
                width: 40,
                hidden: true
            },
            {
                text: '附件名称',
                dataIndex: 'attachmentName',
                flex: 1,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                menuDisabled: true,
                sortable: false,
                xtype: 'actioncolumn',
                width: 30,
                items: [{
                    iconCls: 'attachment_delete',
                    tooltip: '删除',
                    handler: function (grid, rowIndex, colIndex) {
                        var rec = attachmentStore.getAt(rowIndex);
                        var a = [];
                        a.push(rec.get('iid'));
                        Ext.Msg.confirm("请确认", "是否真的要删除附件？", function (button, text) {
                            if (button == "yes") {
                                Ext.Ajax.request({
                                    url: 'deleteScriptAttachment.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                        iids: a
                                    },
                                    success: function (response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            removeByValue(attachmentIds, rec.get('iid'));
                                            attachmentStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function (result, request) {
                                        secureFilterRs(result, "操作失败！");
                                    }
                                });
                            }
                        });

                    }
                }]
            }];
        var selectedAttachmentButton = Ext.create("Ext.Button",
            {
                cls: 'Common_Btn',
                disabled: false,
                text: '添加附件',
                handler: selectAttachmentFun
            });
        var attachmentGrid = Ext.create('Ext.grid.Panel', {
            cls: 'attachments window_border panel_space_top',
            store: attachmentStore,
            border: false,
            columnLines: true,
            columns: attachmentColumns,
            width: '100%',
            height: (contentPanel.getHeight() - 190) * 0.5,
            title: '附件',
            emptyText: '没有附件',
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [
                    '->', selectedAttachmentButton//添加附件按钮
                ]
            }]
        });
        var chosedAgentWin;
        var chosedAgentIds = [];

        Ext.Ajax.request({
            url: 'getLastTestAgents.do',
            method: 'POST',
            async: false,
            params: {
                iid: coatid
            },
            success: function (response, request) {
                chosedAgentIds = Ext.decode(response.responseText).agentList;
            },
            failure: function (result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });

        Ext.define('resourceGroupModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'id',
                type: 'int',
                useNull: true
            }, {
                name: 'name',
                type: 'string'
            }, {
                name: 'description',
                type: 'string'
            }]
        });

        var resourceGroupStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            model: 'resourceGroupModel',
            proxy: {
                type: 'ajax',
                url: 'getResGroupForScriptService.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });
        var resourceGroupObj = Ext.create('Ext.form.field.ComboBox',
            {
                fieldLabel: '资源组',
                emptyText: '--请选择资源组--',
                labelAlign: 'right',
                labelWidth: 65,
                width: '25%',
                columnWidth: 1,
                multiSelect: true,
                hidden: removeAgentSwitch,
                store: resourceGroupStore,
                displayField: 'name',
                valueField: 'id',
                triggerAction: 'all',
                editable: true,
                queryMode: 'local',
                mode: 'local',
                listeners: {
                    change: function (comb, newValue, oldValue, eOpts) {
                        pageBarForAgent.moveFirst();
                    }
                }
            });

        var agentStatusStore = Ext.create('Ext.data.Store', {
            fields: ['id', 'name'],
            data: [
                {"id": "-10000", "name": "全部"},
                {"id": "0", "name": "正常"},
                {"id": "1", "name": "异常"},
                {"id": "2", "name": "升级中"}
            ]
        });

        var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
            name: 'agentStatus',
            labelWidth: 79,
            lableAlign: 'right',
            queryMode: 'local',
            fieldLabel: ' Agent状态',
            displayField: 'name',
            valueField: 'id',
            editable: false,
            emptyText: '--请选择Agent状态--',
            store: agentStatusStore,
            width: '25.8%',
            labelAlign: 'right'
        });

        Ext.define('appNameModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'appName',
                type: 'string'
            }]
        });

        var app_name_store = Ext.create('Ext.data.Store', {
            autoLoad: true,
            model: 'appNameModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentAppNameList.do?envType=0',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        var app_name = Ext.create('Ext.form.ComboBox', {
            name: 'appname',
            fieldLabel: "应用名称",
            emptyText: '--请选择应用名称--',
            hidden: !CMDBflag,
            store: app_name_store,
            queryMode: 'local',
            editable: true,
            width: "25%",
            displayField: 'appName',
            valueField: 'appName',
            labelWidth: 93,
            labelAlign: 'right',
            listeners: {
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = Ext.util.Format.trim(e.query);
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }
            }
        });

        var agent_ip = new Ext.form.TextField({
            name: 'agentip',
            fieldLabel: 'Agent IP',
            displayField: 'agentip',
            emptyText: '--请输入Agent IP--',
            labelWidth: 65,
            labelAlign: 'right',
            width: '25.7%'
        });
        var host_name = new Ext.form.TextField({
            name: 'hostname',
            fieldLabel: '计算机名',
            displayField: 'hostname',
            emptyText: '--请输入计算机名--',
            labelWidth: 65,
            labelAlign: 'right',
            width: '25%'
        });

        Ext.define('sysNameModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'sysName',
                type: 'string'
            }]
        });

        var sys_name_store = Ext.create('Ext.data.Store', {
            autoLoad: true,
            model: 'sysNameModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentSysNameList.do?envType=0',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        var sys_name = Ext.create('Ext.form.ComboBox', {
            name: 'sysname',
            fieldLabel: "名称",
            hidden: !CMDBflag,
            emptyText: '--请选择名称--',
            store: sys_name_store,
            queryMode: 'local',
            editable: true,
            width: "25%",
            displayField: 'sysName',
            valueField: 'sysName',
            labelWidth: 37,
            labelAlign: 'right',
            listeners: {
                beforequery: function (e) {
                    var combo = e.combo;
                    if (!e.forceAll) {
                        var value = Ext.util.Format.trim(e.query);
                        combo.store.filterBy(function (record, id) {
                            var text = record.get(combo.displayField);
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        });
                        combo.expand();
                        return false;
                    }
                }
            }
        });

        var os_type = new Ext.form.TextField({
            name: 'ostype',
            fieldLabel: '操作系统',
            displayField: 'ostype',
            emptyText: '--请输入操作系统--',
            labelWidth: 65,
            labelAlign: 'right',
            width: CMDBflag ? '25%' : '24.2%'
        });
        var search_ip_form = Ext.create('Ext.form.Panel', {
            region: 'north',
            border: false,
            baseCls: 'customize_gray_back',
            dockedItems: [{
                xtype: 'toolbar',
                border: false,
                baseCls: 'customize_gray_back',
                dock: 'top',
                items: [sys_name, app_name, host_name, os_type
                ]
            },
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    baseCls: 'customize_gray_back',
                    items: [agent_ip, resourceGroupObj, agentStatusCb,
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '查询',
                            handler: function () {
                                pageBarForAgent.moveFirst();
                            }
                        },
                        {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '清空',
                            handler: function () {
                                agent_ip.setValue('');
                                app_name.setValue('');
                                sys_name.setValue('');
                                host_name.setValue('');
                                os_type.setValue('');
                                resourceGroupObj.setValue('');
                                agentStatusCb.setValue('');
                            }
                        }, {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '导入',
                            handler: importExcel
                        }
                    ]
                }]
        });

        function checkFile(fileName) {
            var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
            if (!file_reg.test(fileName)) {
                Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
                //Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)');
                return false;
            }
            return true;
        }

        function importExcel() {
            //销毁win窗口
            if (!(null == upldWin || undefined == upldWin || '' == upldWin)) {
                upldWin.destroy();
                upldWin = null;
            }

            if (!(null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
                upLoadformPane.destroy();
                upLoadformPane = null;
            }
            //导入文件Panel
            upLoadformPane = Ext.create('Ext.form.Panel', {
                width: 370,
                height: 100,
                frame: true,
                baseCls: 'customize_gray_back',
                items: [
                    {
                        xtype: 'filefield',
                        name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
                        fieldLabel: '选择文件',
                        labelWidth: 65,
                        msgTarget: 'side',
                        anchor: '100%',
                        buttonText: '浏览...',
                        width: 370
                    }
                ],
                buttonAlign: 'left',
                buttons: [
                    {
                        id: 'upldBtnIdAudi',
                        text: '导入Agent文件',
                        handler: function () {
                            var form = this.up('form').getForm();
                            var upfile = form.findField("file").getValue();
                            if (upfile == '') {
                                Ext.Msg.alert('提示', "请选择文件...");
                                return;
                            }

                            var hdtmpFilNam = form.findField("file").getValue();
                            if (!checkFile(hdtmpFilNam)) {
                                form.findField("file").setRawValue('');
                                return;
                            }

                            if (form.isValid()) {
                                Ext.MessageBox.wait("数据处理中...", "进度条");
                                form.submit({
                                    url: 'importAgentForStart.do',
                                    params: {
                                        envType: 0
                                    },
                                    success: function (form, action) {
                                        var msg = Ext.decode(action.response.responseText).message;

                                        var status = Ext.decode(action.response.responseText).status;
                                        var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;

                                        if (status == 1) {
                                            if (matchAgentIds && matchAgentIds.length > 0) {
                                                Ext.MessageBox.buttonText.yes = "确定";
                                                Ext.MessageBox.buttonText.no = "取消";
                                                Ext.Msg.confirm("请确认", msg, function (id) {
                                                    if (id == 'yes') {
                                                        Ext.Msg.alert('提示', "导入成功！");
                                                        agent_ip.setValue('');
                                                        app_name.setValue('');
                                                        sys_name.setValue('');
                                                        host_name.setValue('');
                                                        os_type.setValue('');
                                                        resourceGroupObj.setValue('');
                                                        agentStatusCb.setValue('');
                                                        chosedAgentIds = matchAgentIds;
                                                        pageBarForAgent.moveFirst();
                                                    }
                                                });
                                            } else {
                                                Ext.Msg.alert('提示-没有匹配项', msg);
                                            }

                                        } else {
                                            Ext.Msg.alert('提示', "导入成功！");
                                            agent_ip.setValue('');
                                            app_name.setValue('');
                                            sys_name.setValue('');
                                            host_name.setValue('');
                                            os_type.setValue('');
                                            resourceGroupObj.setValue('');
                                            agentStatusCb.setValue('');
                                            chosedAgentIds = msg;
                                            pageBarForAgent.moveFirst();
                                        }

                                        upldWin.close();
                                        return;
                                    },
                                    failure: function (form, action) {
                                        secureFilterRsFrom(form, action);
                                    }
                                });
                            }
                        }
                    }, {
                        text: '下载模板',
                        handler: function () {
                            window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
                        }
                    }
                ]
            });
            //导入窗口
            upldWin = Ext.create('Ext.window.Window', {
                title: '设备信息批量导入',
                width: 400,
                height: 140,
                modal: true,
                resizable: false,
                closeAction: 'destroy',
                items: [upLoadformPane]
            }).show();
            upldWin.on("beforeshow", function (self, eOpts) {
                var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
                form.reset();
            });

            upldWin.on("destroy", function (self, eOpts) {
                upLoadformPane.destroy();
            });
        }

        Ext.define('agentModel', {
            extend: 'Ext.data.Model',
            idProperty: 'iid',
            fields: [
                {name: 'iid', type: 'string'},
                {name: 'sysName', type: 'string'},
                {name: 'appName', type: 'string'},
                {name: 'hostName', type: 'string'},
                {name: 'osType', type: 'string'},
                {name: 'agentIp', type: 'string'},
                {name: 'agentPort', type: 'string'},
                {name: 'agentDesc', type: 'string'},
                {name: 'agentDesc', type: 'string'},
                {name: 'agentState', type: 'int'}
            ]
        });

        var agent_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 30,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAllAgentList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        var agent_store_chosed = Ext.create('Ext.data.Store', {
            autoLoad: true,
            pageSize: 30,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentChosedList.do',
                actionMethods: {
                    create: 'POST',
                    read: 'POST', // by default GET
                    update: 'POST',
                    destroy: 'POST'
                },
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        function openExecUserConfigData(record) {
            if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
                if (isSumpAgentSwitch == true) {
                    var sumpAgentStore = Ext.create('Ext.data.Store', {
                        fields: ['iid', 'userName'],
                        autoLoad: true,
                        proxy: {
                            type: 'ajax',
                            url: 'getSumpAgentUserList.do',
                            reader: {
                                type: 'json',
                                root: 'dataList'
                            }
                        }
                    });

                    sumpAgentStore.on('beforeload', function (store, options) {
                        var queryparams = {
                            agentId: record.get('iid')
                        };
                        Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
                    });

                    execUserNameText = Ext.create('Ext.form.field.ComboBox', {
                        name: 'execUserName',
                        labelWidth: 65,
                        queryMode: 'local',
                        fieldLabel: '执行用户',
                        width: 320,
                        displayField: 'userName',
                        valueField: 'iid',
                        editable: true,
                        typeAhead: true,
                        emptyText: '--请选择执行用户--',
                        store: sumpAgentStore,
                        labelAlign: 'right'
                    });
                    if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                        var sumpAgentCount = sumpAgentStore.getRange();
                        var newExecUserName = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                        if (sumpAgentCount.length > 0) {
                            if (undefined == newExecUserName) {
                                execUserNameText.setRawValue(record.get('execuser'));
                            } else {
                                execUserNameText.setValue(newExecUserName);
                            }
                        } else {
                            if (undefined == newExecUserName) {
                                execUserNameText.setValue(record.get('execuser'));
                                execUserNameText.setRawValue(record.get('execuser'));
                            } else {
                                execUserNameText.setValue(newExecUserName);
                                execUserNameText.setRawValue(newExecUserName);
                            }
                        }
                    }
                } else {
                    execUserNameText = Ext.create('Ext.form.TextField',
                        {
                            fieldLabel: '执行用户',
                            labelAlign: 'right',
                            name: "execUserName",
                            labelWidth: 65,
                            emptyText: '--请输入执行用户--',
                            width: 320,
                            xtype: 'textfield'
                        });

                    if (null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execuser'))) {
                        var newExecUserName1 = $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"));
                        if (undefined == newExecUserName1) {
                            execUserNameText.setValue(record.get('execuser'));
                        } else {
                            execUserNameText.setValue(newExecUserName1);
                        }
                    }
                }

                execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel', {
                    region: 'north',
                    layout: 'anchor',
                    //iqueryFun : queryBtnFun,
                    buttonAlign: 'right',
                    baseCls: 'customize_gray_back',
                    collapsible: false,//可收缩
                    collapsed: false,//默认收缩
                    border: false,
                    dockedItems: [{
                        xtype: 'toolbar',
                        dock: 'top',
                        border: false,
                        baseCls: 'customize_gray_back',
                        items: [execUserNameText]
                    }, {
                        xtype: 'toolbar',
                        dock: 'top',
                        border: false,
                        baseCls: 'customize_gray_back',
                        items: ['->', {
                            text: '确定',
                            cls: 'Common_Btn',
                            icon: '',
                            handler: function () {
                                chosedExecUser(record);
                            }
                        }]
                    }]
                });

                var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
                    layout: 'border',
                    width: "100%",
                    height: "100%",
                    border: false,
                    items: [execUserConfigForm],
                    cls: 'customize_panel_bak'
                });

                execUserConfigWindow = Ext.create('Ext.window.Window', {
                    title: "配置执行用户",
                    modal: true,
                    closeAction: 'destroy',
                    constrain: true,
                    autoScroll: false,
                    //upperWin : errorTaskWin,
                    width: 330,
                    height: 150,
                    draggable: false,// 禁止拖动
                    resizable: false,// 禁止缩放
                    layout: 'fit',
                    items: [execUserConfig_mainPanel]
                });
            }
            execUserConfigWindow.show();
        }

        var agent_columns = [{text: '序号', xtype: 'rownumberer', width: 40},
            {text: '主键', dataIndex: 'iid', hidden: true},
            {text: '名称', dataIndex: 'sysName', flex: 1},
            {text: '应用名称', dataIndex: 'appName', hidden: !CMDBflag, flex: 1},
            {text: '计算机名', dataIndex: 'hostName', flex: 1},
            {text: 'IP', dataIndex: 'agentIp', width: 150},
            {text: '端口号', dataIndex: 'agentPort', width: 100},
            {text: '操作系统', dataIndex: 'osType', width: 140},
            {
                text: '描述', dataIndex: 'agentDesc', flex: 1, hidden: true,
                renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                    metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                    return value;
                }
            },
            {
                text: '状态', dataIndex: 'agentState', width: 80, renderer: function (value, p, record) {
                    var backValue = "";
                    if (value == 0) {
                        backValue = "<span class='Complete_Green State_Color'>正常</span>";
                    } else if (value == 1) {
                        backValue = "<span class='Abnormal_yellow State_Color'>异常</span>";
                    }
                    return backValue;
                }
            }
        ];

        agent_store.on('beforeload', function (store, options) {
            var new_params = {
                agentIp: Ext.util.Format.trim(agent_ip.getValue()),
                appName: app_name.getValue() == null ? '' : Ext.util.Format.trim(app_name.getValue() + ""),
                sysName: sys_name.getValue() == null ? '' : Ext.util.Format.trim(sys_name.getValue() + ""),
                hostName: Ext.util.Format.trim(host_name.getValue()),
                osType: Ext.util.Format.trim(os_type.getValue()),
                rgIds: resourceGroupObj.getValue(),
                agentState: agentStatusCb.getValue(),
                flag: 0
            };

            Ext.apply(agent_store.proxy.extraParams, new_params);
        });

        agent_store_chosed.on('beforeload', function (store, options) {
            var new_params = {
                agentIds: JSON.stringify(chosedAgentIds)
            };

            Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
        });

        agent_store.on('load', function (store, options) {
            var records = [];//存放选中记录
            for (var i = 0; i < agent_store.getCount(); i++) {
                var record = agent_store.getAt(i);
                for (var ii = 0; ii < chosedAgentIds.length; ii++) {

                    if ((+chosedAgentIds[ii]) == record.data.iid) {
                        records.push(record);
                    }
                }
            }
            agent_grid.getSelectionModel().select(records, false, true);//选中记录
        });

        var execUserForTry = new Ext.form.TextField({
            name: 'execUserForTry',
            id: 'execUserForTry',
            fieldLabel: '执行用户',
            emptyText: '-请输入执行用户-',
            labelWidth: 60,
            padding: '5',
            labelAlign: 'right',
            width: '20%'
        });

        var pageBarForAgent = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
            store: agent_store,
            baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            dock: 'bottom',
            displayInfo: true
        });


        var agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store: agent_store,
            border: false,
            columnLines: true,
            cls: 'customize_panel_back',
            columns: agent_columns,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//    		    bbar : pageBarForAgent,
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
            listeners: {
                select: function (e, record, index, eOpts) {
                    if (chosedAgentIds.indexOf(record.get('iid')) == -1) {
                        chosedAgentIds.push(record.get('iid'));
                    }
                },
                deselect: function (e, record, index, eOpts) {
                    if (chosedAgentIds.indexOf(record.get('iid')) > -1) {
                        chosedAgentIds.remove(record.get('iid'));
                    }
                }
            }
        });

        var pageBarForAgentChosedGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
            store: agent_store_chosed,
            baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            dock: 'bottom',
            displayInfo: true
        });

        var agent_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store: agent_store_chosed,
            border: true,
            cls: 'customize_panel_back',
            columnLines: true,
            emptyText: '没有选择服务器',
            columns: agent_columns,
            selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
//    	    	bbar : pageBarForAgentChosedGrid,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
            dockedItems: [{
                xtype: 'toolbar',
                //baseCls:'customize_gray_back',
                dock: 'top',
                items: [execUserForTry, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '删除',
                    handler: function () {
                        var records = agent_grid_chosed.getSelectionModel().getSelection();
                        if (records.length > 0) {
                            for (var i = 0, len = records.length; i < len; i++) {
                                chosedAgentIds.remove(records[i].get('iid'));
                            }
                            pageBarForAgentChosedGrid.moveFirst();
                            pageBarForAgent.moveFirst();
                        } else {
                            Ext.Msg.alert('提示', "请选择服务器！");
                            return;
                        }
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '增加服务器',
                    handler: function () {
                        if (!chosedAgentWin) {
                            chosedAgentWin = Ext.create('Ext.window.Window', {
                                title: '增加服务器',
                                autoScroll: true,
                                modal: true,
                                resizable: false,
                                closeAction: 'hide',
                                width: contentPanel.getWidth() - 190,
                                height: contentPanel.getHeight(),
                                layout: 'border',
                                items: [search_ip_form, agent_grid],
                                buttonAlign: 'center',
                                dockedItems: [{
                                    xtype: 'toolbar',
                                    baseCls: 'customize_gray_back',
                                    dock: 'bottom',
                                    layout: {pack: 'center'},
                                    items: [{
                                        xtype: "button",
                                        text: "确定",
                                        cls: 'Common_Btn',
                                        handler: function () {
                                            agent_store_chosed.load();
                                            this.up("window").close();
                                        }
                                    }, {
                                        xtype: "button",
                                        text: "关闭",
                                        cls: 'Common_Btn',
                                        handler: function () {
                                            this.up("window").close();
                                        }
                                    }]
                                }]
                            });
                        }
                        chosedAgentWin.show();
                        agent_store.load();
                    }
                }]
            }]
        });
        var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
            region: 'south',
            title: "已选服务器",
            border: true,
            layout: 'border',
            cls: 'customize_panel_back',
            height: contentPanel.getHeight() - 100,
            collapsible: true,
            collapsed: true,
            items: [agent_grid_chosed]
        });

        if (scriptEditBookSwitch){
            //关键字
            var keywordNameVar = new Ext.form.TextField({
                emptyText: '关键字：可输入变量名称、描述',
                labelWidth: 65,
//		padding : '5',
                width: 300,
                labelAlign: 'right',
                listeners: {
                    specialkey: function (field, e) {
                        if (e.getKey() == e.ENTER) {
                            variableTab.ipage.moveFirst();
                        }
                    }
                }
            });

            //关键字
            var keywordNameFunc = new Ext.form.TextField({
                emptyText: '关键字：可输入函数名称、说明',
                labelWidth: 65,
//		padding : '5',
                width: 300,
                labelAlign: 'right',
                listeners: {
                    specialkey: function (field, e) {
                        if (e.getKey() == e.ENTER) {
                            functionTab.ipage.moveFirst();
                        }
                    }
                }
            });

            Ext.define('gridListModelVariable', {
                extend: 'Ext.data.Model',
                fields: [{
                    name: 'iid',
                    type: 'long',
                }, {
                    name: 'iname',
                    type: 'string'
                }, {
                    name: 'itype',
                    type: 'string'
                }, {
                    name: 'ivalue',
                    type: 'string'
                }, {
                    name: 'idesc',
                    type: 'string'
                }, {
                    name: 'iattribute',
                    type: 'string'
                }]
            });

            //变量store
            var variableStore = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy: true,
                pageSize: 30,
                model: 'gridListModelVariable',
                proxy: {
                    type: 'ajax',
                    url: 'funcAndVarUseNoteBookQuery.do',
                    reader: {
                        type: 'json',
                        root: 'blList',
                        totalProperty: 'varCount'
                    }
                }
            });

            variableStore.on('beforeload', function (store, options) {
                var new_params = {
                    keywordName: keywordNameVar.getValue(),
                    getType:'0'
                };
                Ext.apply(variableStore.proxy.extraParams, new_params);
            });

            Ext.define('gridListModelFucntion', {
                extend: 'Ext.data.Model',
                fields: [{
                    name: 'iid',
                    type: 'long',
                }, {
                    name: 'iname',
                    type: 'string'
                }, {
                    name: 'ilanguagetype',
                    type: 'string'
                }, {
                    name: 'idesc',
                    type: 'string'
                }, {
                    name: 'iattribute',
                    type: 'string'
                }]
            });

            //函数store
            var functionStore = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy: true,
                pageSize: 30,
                model: 'gridListModelFucntion',
                proxy: {
                    type: 'ajax',
                    url: 'funcAndVarUseNoteBookQuery.do',
                    reader: {
                        type: 'json',
                        root: 'hsList',
                        totalProperty: 'funCount'
                    }
                }
            });

            functionStore.on('beforeload', function (store, options) {
                var new_params = {
                    keywordName: keywordNameFunc.getValue(),
                    language:language,
                    getType:'1'
                };
                Ext.apply(functionStore.proxy.extraParams, new_params);
            });

            //变量展示列
            var variableColumns = [{
                text: '序号',
                xtype: 'rownumberer',
                width:'5%'
            },
                {
                    text: '变量名称',
                    dataIndex: 'iname',
                    width: '35%',
                    renderer: function (value, metaData, record) {
                        var iid = record.get("iid");
                        var idesc = record.get("idesc")
                        var attribute = record.get('iattribute')
                        return '<a href="#" onclick="showVariable(\''+ value +'\', \'' + idesc + '\', \'' + attribute + '\')">' + value + '</a>';
                    }
                },
                {
                    text: '描述',
                    dataIndex: 'idesc',
                    width: '58%',
                    renderer: function (value, metaData, record) {
                        return '<span title="'+value+'">' + value + '</span>';
                    }
                },{
                    hidden:true,
                    text: '属性',
                    dataIndex: 'iattribute',
                    width: '5%',
                    renderer: function (value, p, record) {
                        var backValue = "";
                        if (value == '1') {
                            backValue = "自定义";
                        } else if (value == '2') {
                            backValue = "内置";
                        }
                        return backValue;
                    }
                }];

            //函数展示列
            var functionColumns = [{
                text: '序号',
                xtype: 'rownumberer',
                width:'5%'
            },
                {
                    text: '函数名称',
                    dataIndex: 'iname',
                    width: '35%',
                    renderer: function (value, metaData, record) {
                        var iid = record.get("iid");
                        return '<a href="#" onclick="showFunction('+iid+')">' + value + '</a>';
                    }
                },
                {
                    text: '说明',
                    dataIndex: 'idesc',
                    width: '58%',
                    renderer: function (value, metaData, record) {
                        return '<span title="'+value+'">' + value + '</span>';
                    }
                }];
            variableTab = Ext.create('Ext.ux.ideal.grid.Panel', {
                region: 'center',
                // overflowY: 'hidden',
                store: variableStore,
                width:640,
                margin:'0 0 0 10',
                height: contentPanel.getHeight()-200,
                // selModel: selModelvariable,
                // plugins: [cellEditingvariable],
                //dockedItems: scriptServiceitems,
                border: true,
                ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                // bbar : pageBar,
                columnLines: true,
                layout:'fit',
                cls: 'customize_panel_back',
                // padding: grid_space,
                columns: variableColumns,
                overflowX:false,
                listeners:{
                    itemdblclick:function(t, record){
                        editor.replaceSelection(record.raw.iname);
                        editor.focus();
                    }
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    items: [
                        keywordNameVar,
                        {
                            xtype:'button',
                            cls:'Common_Btn',
                            text:'查询',
                            handler:function(){
                                variableTab.ipage.moveFirst();
                            }
                        }
                    ]
                },{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    items: [{
                        xtype: 'label',
                        fieldLabel: '变量可在脚本中直接使用，支持脚本语言:shell、python、powershell。',
                        html: '变量可在脚本中直接使用，支持脚本语言:shell、python、powershell。',
                        margin: '0 0 0 0'
                    }
                    ]
                }]

            });
            functionTab = Ext.create('Ext.ux.ideal.grid.Panel', {
                region: 'center',
                store: functionStore,
                width:640,
                layout:'fit',
                margin:'0 0 0 10',
                height: contentPanel.getHeight()-200,
                // selModel: selModel,
                // plugins: [cellEditing],
                //dockedItems: scriptServiceitems,
                border: true,
                ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                // bbar : pageBar,
                columnLines: true,
                cls: 'customize_panel_back',
                // padding: grid_space,
                columns: functionColumns,
                overflowX:false,
                listeners:{
                    itemdblclick:function(t, record){
                        editor.replaceSelection(record.raw.iexample);
                        editor.focus();
                    }
                },
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'top',
                    border: false,
                    items: [
                        keywordNameFunc,
                        {
                            xtype:'button',
                            cls:'Common_Btn',
                            text:'查询',
                            handler:function(){
                                functionTab.ipage.moveFirst();
                            }
                        }
                    ]
                }]
            });
        }

        var scriptGridPanel = Ext.create('Ext.panel.Panel', {
            region: 'east',
            width: '100%',
            autoScroll:false,
            // cls: 'customize_panel_back',
            // layout:'border',
            items: [paramGrid, attachmentGrid]
        });

        var infoPanel = new Ext.tab.Panel({
            region: 'east',
            border: false,
            width: 660,
            height: contentPanel.getHeight(),
            layout: 'border',
            tabPosition: 'top',
            style:'background-color:white',
            // cls: 'normatab no_verticaltab',
            activeTab: 0,
            defaults:
                {
                    autoScroll: true
                },
            items: [
                {
                    title: '脚本属性',
                    items: scriptGridPanel,
                    listeners:
                        {
                            activate: function (tab) {
                                // paramGrid.setHeight(infoPanel.getHeight() - 420);
                            }
                        }
                },
                {
                    title: '变量',
                    items: [variableTab],
                    hidden:!scriptEditBookSwitch,
                    listeners:
                        {
                            activate: function (tab) {
                                // variableTab.ipage.moveFirst();
                            }
                        }
                },
                {
                    title: '函数',
                    items: [functionTab],
                    hidden:!scriptEditBookSwitch,
                    listeners:
                        {
                            activate: function (tab) {
                                // functionTab.ipage.moveFirst();
                            }
                        }
                }
            ]
        })



        var scriptPanel = Ext.create('Ext.panel.Panel', {
            layout: 'border',
            width: contentPanel.getWidth(),
            // cls: 'customize_panel_back',
            height: contentPanel.getHeight() - 100,
            border: false,
            items: [mainP, infoPanel, chooseAgentPanel]
        });

        function setMessage(msg) {
            Ext.Msg.alert('提示', msg);
        }

        var EditWin = Ext.create('widget.window', {
            title: '编辑脚本',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth() + 50,
            minWidth: 350,
            height: contentPanel.getHeight() - 5,
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            items: [scriptPanel],
            dockedItems: [{
                xtype: 'toolbar',
                baseCls: 'customize_gray_back',
                dock: 'bottom',
                layout: {pack: 'center'},
                items: [{
                    xtype: "button",
                    text: "测试",
                    cls: 'Common_Btn',
                    margin: '6',
                    handler: function () {
                        var me = this;
                        var agents = new Array();
                        if (chosedAgentIds.length <= 0) {
                            setMessage('请选择服务器！');
                            return;
                        }

                        var isOk = false;
                        var agentStateMsg = "";

                        // 检查agent状态
                        Ext.Ajax.request({
                            url: 'checkAgentState.do',
                            method: 'POST',
                            async: false,
                            params: {
                                agentIds: chosedAgentIds
                            },
                            success: function (response, request) {
                                isOk = Ext.decode(response.responseText).isOk;
                                agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
                            },
                            failure: function (result, request) {
                                agentStateMsg = "检查Agent状态出错！";
                            }
                        });

                        function realTest() {
                            $.each(chosedAgentIds, function (i, v) {
                                var a = {
                                    iid: v
                                };
                                agents.push(a);
                            });

                            paramStore.sort('paramOrder', 'ASC');
                            var m = paramStore.getRange(0, paramStore.getCount() - 1);
                            var jsonDataPara = "[";
                            for (var i = 0, len = m.length; i < len; i++) {
                                var ss = Ext.JSON.encode(m[i].data);
                                if (i == 0) jsonDataPara = jsonDataPara + ss;
                                else jsonDataPara = jsonDataPara + "," + ss;
                            }
                            jsonDataPara = jsonDataPara + "]";
                            var aaaa = new Array();
                            var jsonData = "[";
                            for (var j = 0, lenj = m.length; j < lenj; j++) {
                                var n = 0;
                                var paramType = m[j].get("paramType") ? m[j].get("paramType").trim() : '';
                                var paramDefaultValue = m[j].get("paramDefaultValue") ? m[j].get("paramDefaultValue").trim() : '';
                                var paramDesc = m[j].get("paramDesc") ? m[j].get("paramDesc").trim() : '';
                                var iorder = m[j].get("paramOrder");
                                if ("" == paramType) {
                                    setMessage('参数类型不能为空！');
                                    return;
                                }
                                if (fucCheckLength(paramDesc) > 250) {
                                    setMessage('参数描述不能超过250字符！');
                                    return;
                                }

                                if ((paramType == 'OUT-int' || paramType == 'IN-int') && paramDefaultValue) {
                                    if (!checkIsInteger(paramDefaultValue)) {
                                        setMessage('参数类型为int，但参数默认值不是int类型！');
                                        return;
                                    }
                                }
                                if ((paramType == 'OUT-float' || paramType == 'IN-float') && paramDefaultValue) {
                                    if (!checkIsDouble(paramDefaultValue)) {
                                        setMessage('参数类型为float，但参数默认值不是float类型！');
                                        return;
                                    }
                                }
                                for (var k = 0; k < paramStore.getCount(); k++) {
                                    var record = paramStore.getAt(k);
                                    var order = record.data.paramOrder;
                                    if (order == iorder) {
                                        n = n + 1;
                                    }
                                }
                                if (n > 1) {
                                    Ext.MessageBox.alert("提示", "参数顺序不能重复！");
                                    return;
                                }
                                aaaa.push(paramDefaultValue);
                                var sss = Ext.JSON.encode(m[j].data);
                                if (j == 0) jsonData = jsonData + sss;
                                else jsonData = jsonData + "," + sss;
                            }
                            jsonData = jsonData + "]";
                            var dsid = 0;
                            editor.save();
                            var content = document.getElementById('codeEditViewForScriptCoat').value;

                            function SaveAndTest() {
                                Ext.Ajax.request({
                                    url: 'updateScriptCoatpage.do',
                                    method: 'POST',
                                    params: {
                                        iid: viewBaseScriptiid,
                                        uuid: iscriptUuid,
                                        content: content,
                                        params: jsonData,
                                        attachmentIds: attachmentIds
                                    },
                                    success: function (response, request) {
                                        var scriptPara = aaaa.join("@@script@@service@@");
                                        Ext.Ajax.request({
                                            url: 'execScriptServiceForSync.do',
                                            method: 'POST',
                                            params: {
                                                serviceId: viewBaseScriptiid,
                                                execUser: execUserForTry.getValue(),
                                                scriptPara: scriptPara,
                                                jsonDataPara: jsonDataPara,
                                                dbsourceid: dsid,
                                                jsonData: JSON.stringify(agents),
                                                ifrom: 0,
                                                flag: 0
                                            },
                                            success: function (response, request) {
                                                me.up("window").close();
                                                Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
                                            },
                                            failure: function (result, request) {
                                                Ext.Msg.alert('提示', '执行失败！');
                                            }
                                        });
                                    },
                                    failure: function (result, request) {
                                        Ext.Msg.alert('提示', '执行失败！');
                                    }
                                });
                            }

                            Ext.Ajax.request({
                                url: 'checkHasKeywords.do',
                                method: 'POST',
                                params: {
                                    content: content,
                                    checkRadio: scriptT
                                },
                                success: function (response, request) {
                                    var hasTipKeyWord = Ext.decode(response.responseText).hasTipKeyWord;
                                    var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;
                                    console.log(hasTipKeyWord);
                                    console.log(hasScreenKeyWord);
                                    if (hasScreenKeyWord) {
                                        Ext.Msg.alert('提示', "脚本中存在屏蔽命令，无法保存！");
                                        return;
                                    } else if (hasTipKeyWord) {
                                        Ext.Msg.confirm("请确认", "脚本中存在提醒命令，是否继续保存脚本？", function (button, text) {
                                            if (button == "yes") {
                                                SaveAndTest();
                                            }
                                        });
                                    } else {
                                        SaveAndTest();
                                    }
                                },
                                failure: function (result, request) {
                                    Ext.Msg.alert('提示', '操作失败！');
                                }
                            });
                        }

                        if (isOk) {
                            realTest();
                        } else {
                            Ext.Msg.confirm("请确认", agentStateMsg + "<br>选择的代理状态为异常，是否仍然进行测试？", function (id) {
                                if (id == 'yes') {
                                    realTest();
                                }
                            });
                        }

                    }
                }, {
                    xtype: "button",
                    text: "保存",
                    cls: 'Common_Btn',
                    handler: function () {
                        paramStore.sort('paramOrder', 'ASC');
                        var m = paramStore.getRange(0, paramStore.getCount() - 1);
                        var aaaa = new Array();
                        var jsonData = "[";
                        for (var i = 0, len = m.length; i < len; i++) {
                            var n = 0;
                            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
                            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
                            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
                            var iorder = m[i].get("paramOrder");
                            var parameterName = m[i].get("parameterName");
                            var ruleName = m[i].get("ruleName");
                            if (paramType == '枚举') {
                                if (paramDefaultValue == "" || paramDefaultValue == null) {
                                    setMessage('默认值不能为空！');
                                    return;
                                }
                            }
                            //根据选中的验证规则名拿到对应的正则表达式
                            if (bhParameterCheckSwitch) {
                                if (ruleName != "" && ruleName != null) {
                                    Ext.Ajax.request({
                                        url: 'queryParameterCheckRule.do',
                                        method: 'POST',
                                        async: false,
                                        params: {
                                            ruleName: ruleName
                                        },
                                        success: function (response, request) {
                                            dataList = Ext.decode(response.responseText).dataList;
                                        },
                                        failure: function (result, request) {
                                            Ext.Msg.alert('提示', '获取验证规则失败！');
                                        }
                                    });
                                    //用拿到的正则去校验默认值
                                    var patt = new RegExp(dataList);
                                    if (patt.exec(paramDefaultValue) == null) {
                                        setMessage('顺序为' + "“" + iorder + "”" + '的默认值校验不通过请检查！');
                                        return;
                                    }
                                }
                            }
                            if ("" == paramType) {
                                setMessage('参数类型不能为空！');
                                return;
                            }
                            if (fucCheckLength(paramDesc) > 250) {
                                setMessage('参数描述不能超过250字符！');
                                return;
                            }

                            if ((paramType == 'OUT-int' || paramType == 'IN-int') && paramDefaultValue) {
                                if (!checkIsInteger(paramDefaultValue)) {
                                    setMessage('参数类型为int，但参数默认值不是int类型！');
                                    return;
                                }
                            }
                            if ((paramType == 'OUT-float' || paramType == 'IN-float') && paramDefaultValue) {
                                if (!checkIsDouble(paramDefaultValue)) {
                                    setMessage('参数类型为float，但参数默认值不是float类型！');
                                    return;
                                }
                            }
                            for (var k = 0; k < paramStore.getCount(); k++) {
                                var record = paramStore.getAt(k);
                                var order = record.data.paramOrder;
                                if (order == iorder) {
                                    n = n + 1;
                                }
                            }
                            if (n > 1) {
                                Ext.MessageBox.alert("提示", "参数顺序不能重复！");
                                return;
                            }
                            if (paramType == '枚举') {
                                if (parameterName == "" || parameterName == null) {
                                    setMessage('枚举名不能为空！');
                                    return;
                                }
                            }
                            aaaa.push(paramDefaultValue);
                            var ss = Ext.JSON.encode(m[i].data);
                            if (i == 0) jsonData = jsonData + ss;
                            else jsonData = jsonData + "," + ss;
                        }
                        jsonData = jsonData + "]";

                        editor.save();
                        var content = document.getElementById('codeEditViewForScriptCoat').value;
                        Ext.Ajax.request({
                            url: 'checkHasKeywords.do',
                            method: 'POST',
                            params: {
                                content: content,
                                checkRadio: scriptT
                            },
                            success: function (response, request) {
                                var hasTipKeyWord = Ext.decode(response.responseText).hasTipKeyWord;
                                var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;
                                console.log(hasTipKeyWord);
                                console.log(hasScreenKeyWord);
                                if (hasScreenKeyWord) {
                                    Ext.Msg.alert('提示', "脚本中存在屏蔽命令，无法保存！");
                                    return;
                                } else if (hasTipKeyWord) {
                                    Ext.Msg.confirm("请确认", "脚本中存在提醒命令，是否继续保存脚本？", function (button, text) {
                                        if (button == "yes") {
                                            Ext.Ajax.request({
                                                url: 'updateScriptCoatpage.do',
                                                method: 'POST',
                                                params: {
                                                    iid: viewBaseScriptiid,
                                                    uuid: iscriptUuid,
                                                    content: content,
                                                    params: jsonData,
                                                    attachmentIds: attachmentIds
                                                },
                                                success: function (response, request) {
                                                    var message = Ext.decode(response.responseText).message;
                                                    if (scriptShellSyntaxValidateSwitch){
                                                        var syntaxError = Ext.decode(response.responseText).syntaxError;
                                                        var syntaxInfo = Ext.decode(response.responseText).syntaxInfo;
                                                        // 展示脚本语法检验错误
                                                        if (syntaxError){
                                                            Ext.Msg.close();
                                                            var descTextArea = null;

                                                            descTextArea = Ext.create('Ext.form.field.TextArea', {
                                                                fieldLabel: '',
                                                                // afterLabelTextTpl: required,
                                                                labelWidth: 70,
                                                                height: 370,
                                                                readOnly:true,
                                                                width:'864px',
                                                                columnWidth: .99,
                                                                autoScroll: true
                                                            });
                                                            descTextArea.setValue(syntaxInfo);

                                                            var syntaxWin = Ext.create('Ext.window.Window', {
                                                                title: '语法检查失败',
                                                                border:false,
                                                                modal: true,
                                                                cls:"instruction_box",
                                                                width: 900,
                                                                height: 500,
                                                                draggable: false,
                                                                items:[descTextArea],
                                                                closable:false,
                                                                buttonAlign:'center',
                                                                buttons:[
                                                                    {
                                                                        xtype:'button',
                                                                        cls:'Common_Btn',
                                                                        text:'跳过',
                                                                        handler:function (){
                                                                            syntaxWin.hide();
                                                                            Ext.Ajax.request({
                                                                                url: 'updateScriptCoatpage.do',
                                                                                method: 'POST',
                                                                                params: {
                                                                                    iid: viewBaseScriptiid,
                                                                                    uuid: iscriptUuid,
                                                                                    content: content,
                                                                                    params: jsonData,
                                                                                    attachmentIds: attachmentIds,
                                                                                    ignoreSyntaxFlag:1
                                                                                },
                                                                                success: function (response, request) {
                                                                                    var message = Ext.decode(response.responseText).message;
                                                                                    Ext.Msg.alert('提示', message);
                                                                                    paramStore.load();
                                                                                },
                                                                                failure: function (result, request) {
                                                                                    Ext.Msg.alert('提示', '保存失败！');
                                                                                }
                                                                            });
                                                                        }
                                                                    },
                                                                    {
                                                                        xtype:'button',
                                                                        cls:'Common_Btn',
                                                                        text:'去修改',
                                                                        handler:function (){
                                                                            syntaxWin.hide();
                                                                        }
                                                                    }
                                                                ]
                                                            }).show()
                                                            return;
                                                        }
                                                    }
                                                    Ext.Msg.alert('提示', message);
                                                },
                                                failure: function (result, request) {
                                                    Ext.Msg.alert('提示', '保存失败！');
                                                }
                                            });
                                        }
                                    });
                                } else {
                                    Ext.Ajax.request({
                                        url: 'updateScriptCoatpage.do',
                                        method: 'POST',
                                        params: {
                                            iid: viewBaseScriptiid,
                                            uuid: iscriptUuid,
                                            content: content,
                                            params: jsonData,
                                            attachmentIds: attachmentIds
                                        },
                                        success: function (response, request) {
                                            var message = Ext.decode(response.responseText).message;
                                            if (scriptShellSyntaxValidateSwitch){
                                                var syntaxError = Ext.decode(response.responseText).syntaxError;
                                                var syntaxInfo = Ext.decode(response.responseText).syntaxInfo;
                                                // 校验语法错误
                                                if (syntaxError){
                                                    Ext.Msg.close();
                                                    var descTextArea = null;

                                                    descTextArea = Ext.create('Ext.form.field.TextArea', {
                                                        fieldLabel: '',
                                                        // afterLabelTextTpl: required,
                                                        labelWidth: 70,
                                                        height: 370,
                                                        readOnly:true,
                                                        width:'864px',
                                                        columnWidth: .99,
                                                        autoScroll: true
                                                    });
                                                    descTextArea.setValue(syntaxInfo);

                                                    var syntaxWin = Ext.create('Ext.window.Window', {
                                                        title: '语法检查失败',
                                                        border:false,
                                                        modal: true,
                                                        cls:"instruction_box",
                                                        width: 900,
                                                        height: 500,
                                                        draggable: false,
                                                        items:[descTextArea],
                                                        closable:false,
                                                        buttonAlign:'center',
                                                        buttons:[
                                                            {
                                                                xtype:'button',
                                                                cls:'Common_Btn',
                                                                text:'跳过',
                                                                handler:function (){
                                                                    syntaxWin.hide();
                                                                    Ext.Ajax.request({
                                                                        url: 'updateScriptCoatpage.do',
                                                                        method: 'POST',
                                                                        params: {
                                                                            iid: viewBaseScriptiid,
                                                                            uuid: iscriptUuid,
                                                                            content: content,
                                                                            params: jsonData,
                                                                            attachmentIds: attachmentIds,
                                                                            ignoreSyntaxFlag:1
                                                                        },
                                                                        success: function (response, request) {
                                                                            var message = Ext.decode(response.responseText).message;
                                                                            Ext.Msg.alert('提示', message);
                                                                            paramStore.load();
                                                                        },
                                                                        failure: function (result, request) {
                                                                            Ext.Msg.alert('提示', '保存失败！');
                                                                        }
                                                                    });
                                                                }
                                                            },
                                                            {
                                                                xtype:'button',
                                                                cls:'Common_Btn',
                                                                text:'去修改',
                                                                handler:function (){
                                                                    syntaxWin.hide();
                                                                }
                                                            }
                                                        ]
                                                    }).show()
                                                    return;
                                                }
                                            }
                                            Ext.Msg.alert('提示', message);
                                            paramStore.load();
                                        },
                                        failure: function (result, request) {
                                            Ext.Msg.alert('提示', '保存失败！');
                                        }
                                    });
                                }
                            },
                            failure: function (result, request) {
                                Ext.Msg.alert('提示', '操作失败！');
                            }
                        });
                    }
                }, {
                    xtype: "button",
                    text: "取消",
                    cls: 'Common_Btn',
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            }]
        });
        EditWin.show();
        var editor = CodeMirror.fromTextArea(document.getElementById('codeEditViewForScriptCoat'), {
            mode: 'shell',
            lineNumbers: true,
            matchBrackets: true
        });
        editor.setSize(mainP.getWidth(), mainP.getHeight() - 32);
        mainP.on('resize', function () {
            editor.setSize(mainP.getWidth(), mainP.getHeight() - 32);
        });
        editScriptStore.load();
        paramStore.load();
        attachmentStore.load();

        var attachmentUploadWin = null;

        function selectAttachmentFun() {
            var uploadForm;
            uploadForm = Ext.create('Ext.form.FormPanel', {
                border: false,
                items: [{
                    xtype: 'filefield',
                    name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
                    id: 'attachment_idbasic',
                    fieldLabel: '选择文件',
                    labelWidth: 65,
                    anchor: '90%',
                    margin: '10 10 0 40',
                    buttonText: '浏览',
                    multipleFn: function ($this) {

                        var typeArray = ["application/x-shockwave-flash", "audio/MP3", "image/*", "flv-application/octet-stream"];

                        var fileDom = $this.getEl().down('input[type=file]');

                        fileDom.dom.setAttribute("multiple", "multiple");

                        fileDom.dom.setAttribute("accept", typeArray.join(","));

                    },
                    listeners: {
                        afterrender: function () {
                            this.multipleFn(this);
                        },
                        change: function () {
                            var fileDom = this.getEl().down('input[type=file]');
                            var files = fileDom.dom.files;
                            var str = '';
                            for (var i = 0; i < files.length; i++) {
                                str += files[i].name;
                                str += ' ';
                            }
                            Ext.getCmp('attachment_idbasic').setRawValue(str);    //files为组件的id
                            this.multipleFn(this);
                        }
                    }
                }],
                buttonAlign: 'center',
                buttons: [{
                    text: '确定',
                    handler: upExeclData
                }, {
                    text: '取消',
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            });

            attachmentUploadWin = Ext.create('Ext.window.Window', {
                title: '附件信息',
                modal: true,
                closeAction: 'destroy',
                constrain: true,
                autoScroll: true,
                width: 600,
                height: 230,
                items: [uploadForm],
                listeners: {
                    close: function (g, opt) {
                        uploadForm.destroy();
                    }
                },
                /*
    			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
    			 */layout: 'fit'
            });

            function upExeclData() {
                var form = uploadForm.getForm();
                var hdupfile = form.findField("files").getValue();
                if (hdupfile == '') {
                    Ext.Msg.alert('提示', "请选择文件...");
                    return;
                }
                uploadTemplate(form);
            }

            /** 自定义遮罩效果* */
            var myUploadMask = new Ext.LoadMask(contentPanel,
                {
                    msg: "附件上传中..."
                });

            function uploadTemplate(form) {
                if (form.isValid()) {
                    form.submit({
                        url: 'uploadScriptAttachmentFile.do',
                        params:{
                            attachmentIds:Ext.encode(attachmentIds)
                        },
                        success: function (form, action) {
                            var success = Ext.decode(action.response.responseText).success;
                            var msg = Ext.decode(action.response.responseText).message;
                            if (success) {
                                var ids = Ext.decode(action.response.responseText).ids;
                                attachmentIds.push.apply(attachmentIds, ids.split(","));
                            } else {
                                Ext.Msg.alert('提示', msg);
                            }
                            attachmentUploadWin.close();
                            myUploadMask.hide();
                            attachmentStore.load();
                        },
                        failure: function (form, action) {
                            var msg = Ext.decode(action.response.responseText).message;
                            Ext.Msg.alert('提示', msg);
                            myUploadMask.hide();
                        }
                    });
                }
            }

            attachmentUploadWin.show();
        }
    }

    function scriptCoatStop(coatid, flag) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
            function (btn) {
                if (btn == 'yes') {
                    Ext.Ajax.request({
                        url: 'scriptCoatStop.do',
                        method: 'POST',
                        params: {
                            coatid: coatid,
                            flag: flag
                        },
                        success: function (response, request) {
                            var message = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', message);
                            scriptmonitor_store.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            })

    }

    function chosedExecUser(record) {
        $("#scriptManagePageExecUserNameText").attr("scriptManagePageExecUserNameText" + record.get("iid"), execUserConfigForm.getForm().findField('execUserName').getRawValue());
        if (isSumpAgentSwitch == true) {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getRawValue());
        } else {
            record.set('execuser', execUserConfigForm.getForm().findField('execUserName').getValue());
        }
        record.commit();
        execUserConfigWindow.hide();
    }

    function checkIsNotEmptyAndUndefined(str) {
        if (trim(str) == "" && trim(str) == "undefined")
            return false;
        else
            return true;
    }

});

function StringToPassword(strs) {
    if (strs && strs != null & strs != '') {
        var password = '';
        for (var i = 0; i < strs.length; i++) {
            password = password + '●';
        }
        return password;
    } else {
        return '';
    }
}