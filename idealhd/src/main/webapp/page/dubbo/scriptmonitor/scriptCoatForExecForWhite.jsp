<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment,com.ideal.common.utils.SessionData,java.util.ArrayList,java.util.Arrays"%>
  
<%
	String highPermissionUser = Environment.getInstance().getScriptTaskExecHistoryHighpermissionUser();
	SessionData sessionData = SessionData.getSessionData(request);
	String loginUserName = sessionData.getLoginName();
	ArrayList<String> arlist = new ArrayList<String>(Arrays.asList(highPermissionUser.split(",")));
	boolean showStartUser =true;
	 if (arlist.contains(loginUserName))// 如果包含，代表是高权限用户，展示执行人
     {
	     showStartUser =false;
     }
 %>
<html>
<head>
<script type="text/javascript">
	var flowIdFor2LevelWhiteScriptHistory = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var forScriptFlowFor2LevelWhiteScriptHistory = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	var filter_scriptNameFor2LevelWhiteScriptHistory = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_IpWhiteScriptHistory = '<%=request.getParameter("filter_Ip")==null?"":request.getParameter("filter_Ip")%>';
	var filter_stateFor2LevelWhiteScriptHistory  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeFor2LevelWhiteScriptHistory ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeFor2LevelWhiteScriptHistory = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameFor2LevelWhiteScriptHistory = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateFor2LevelWhiteScriptHistory  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeFor2LevelWhiteScriptHistory ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeFor2LevelWhiteScriptHistory = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var projectFlagFor2LevelWhiteScriptHistory = '<%=request.getAttribute("projectFlag")==null?"":request.getAttribute("projectFlag")%>';
	var monitorFlagFor2LevelWhiteScriptHistory= '<%=request.getAttribute("monitorFlag")==null?"":request.getAttribute("monitorFlag")%>';
	var showStartUserFor2LevelWhiteScriptHistory = <%=showStartUser%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptParamsPublic.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptCoatForExecForWhite.js"></script>
</head>
<body>
<div id="scriptcoatmonitorForWhiteSSExec_area" style="width: 100%;height: 100%">
</div>
</body>
</html>