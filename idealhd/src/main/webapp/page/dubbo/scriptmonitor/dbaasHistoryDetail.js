var failureOperStore;
Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   //destroyRubbish();
	var resultColumns = [{ text: '序号', xtype:'rownumberer', width: 60 }];
	var resultfieldsData = [];	
	  var tablewidth=0;	
	  if(columns.length>0){
			for(var i = 0;i<columns.length;i++){  
		 		var fieldsRow_R = {};
		    	fieldsRow_R.name = columns[i].paramRuleOut;
		    	fieldsRow_R.type = 'string';
		    	resultfieldsData.push(fieldsRow_R);
		    	var columnsRow_R = {};
		    	columnsRow_R.text = columns[i].paramRuleOut;
		    	columnsRow_R.dataIndex = columns[i].paramRuleOut;
		    	tablewidth=tablewidth+columns[i].colWidth;
		    	if(columns.length<7){
		    		columnsRow_R.flex = 1;
		    	}
		    	if(columns.length>=7  && i==columns.length-1){
		    	   columnsRow_R.flex = 1;
		    	}    
		    	columnsRow_R.width = columns[i].colWidth;
		    	resultColumns.push(columnsRow_R);	  	    		    	
			}
			Ext.define('resultData', {
			       extend: 'Ext.data.Model',
			       fields: resultfieldsData
			});
			   var store = Ext.create('Ext.data.Store', {
			        autoLoad: true,
			        pageSize: 50,
			        model: 'resultData',
			        proxy: {
			            type: 'ajax',
			            url: 'dbaasgetHistoryDtailData.do',
			            reader: {
			                type: 'json',
			                root: 'dataList',
			                totalProperty: 'total'
			            }
			        },
			        listeners: {
			            load: function() {}
			        }
			    });	
				store.on('beforeload', function (store, options) {
				    var new_params = {  
				    		resId:resId,
				    		flowId:flowId,
				    		scriptuuid:scriptuuid,
				    		tableName:tableName
				    };
				    Ext.apply(store.proxy.extraParams, new_params);
			    });
			    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
			        store: store,
			        dock: 'bottom',
			        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			        displayInfo: true,
			        border:false,
			        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
			        emptyMsg: "没有记录"
			    });
			   
	  }
    
	var resultDetailGrid = Ext.create('Ext.grid.Panel',{
		region:'center',
		bbar: pageBar,
        columns:resultColumns,	        
        border:false,
        store:store,
        columnLines : true,
        listeners: {                                    
            celldblclick : function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
            	 var map=record.data;
                 var contenHtml='  <div class="table_content1">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
                 contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
                 for(var k in map) {
                 	contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+k+"</span></td><td><span class=\"wt05\">"+map[k]+"</span></td></tr>"
                  //   ss=ss+k+':'+map[k]+ '\n';
                 }
                 contenHtml=contenHtml+"</tbody></table></div></div>";
                 showWindowDetail(contenHtml);
               }
           }
   });	

  
	var mainPanel = Ext.create('Ext.panel.Panel', {
	 	title:servicesName+'在'+agentIp+'上的执行历史',
        renderTo: "operResultdetail_div",
        layout: 'border',
        cls:'customize_panel_back add_border   add_border',// panel_space_right
        width : width,
        height :height,
        items: [resultDetailGrid]
	});
 
/* 解决IE下trim问题 */
String.prototype.trim = function() {
    return this.replace(/(^\s*)|(\s*$)/g, "");
};   
     
	function showWindowDetail(contenHtml){
    var resultDesc = Ext.create('Ext.panel.Panel', {
           region:'center',
           border:false,
           readOnly: true,
       });
 //      resultDesc.setValue(content);
       resultDesc.html=contenHtml; 
    var detailWindow = Ext.create('widget.window', {
       title: '详情',
       closable: true,
       closeAction: 'hide',
       resizable: false,
       modal: true,
       width : 600,
       height :500,
       layout: {
           type: 'border',
           padding: 5
       },
       items: [resultDesc],
       }, { 
           xtype: "button", 
           cls:'Common_Btn',
           text: "取消", 
           handler: function () {
               this.up("window").close();
           }
   });
   detailWindow.show ();
   }
});

