<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var instanceId = '<%=request.getParameter("instanceId")==null?"":request.getParameter("instanceId")%>';
	var agentIp = '<%=request.getParameter("agentIp")==null?"":request.getParameter("agentIp")%>';
	var agentPort = '<%=request.getParameter("agentPort")==null?"":request.getParameter("agentPort")%>';
	var flowId = '<%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>';
	var coatId = '<%=request.getParameter("coatId")==null?"":request.getParameter("coatId")%>';
	var flag = '<%=request.getParameter("flag")%>'; // 0:测试     1:生成
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptserverLogger.js"></script>
</head>
<body>
<div id="show-logger"></div>
<span class="switch_span">自动刷新 
  <input type="text" value="5" style="width:35px;" id="rowFreshId" name="rowFreshId" >&nbsp;秒
</span>
<button class="Blue_button Monitor_Btn" id="refresh-this">刷新</button>
<button class="Red_button Monitor_Btn" id="back-to">返回</button>
</body>
</html>