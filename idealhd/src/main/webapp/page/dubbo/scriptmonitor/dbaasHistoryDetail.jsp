<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>历史结果详情展示</title>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<link rel="stylesheet" href="css/analysisstyle_new.css">
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/ext/resources/css/ext-all.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Rewrite_style_small.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Rewrite_style.css" />
<script type="text/javascript" src="<%=request.getContextPath()%>/ext/ext-all.js"></script>	
<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/extsetting.js"></script> 
<script type="text/javascript">
var servicesName = '<%=request.getParameter("servicesName")==null?"":request.getParameter("servicesName")%>';
var resId ='<%=request.getParameter("resId")==null?"":request.getParameter("resId")%>';
var flowId ='<%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>' ;
var scriptuuid = '<%=request.getParameter("scriptuuid")==null?"":request.getParameter("scriptuuid")%>';
var agentIp ='<%=request.getParameter("agentIp")==null?"":request.getParameter("agentIp")%>' ;
var tableName ='<%=request.getParameter("tablename")==null?"":request.getParameter("tablename")%>' ;
var columns =<%=request.getAttribute("columns")==null?"":request.getAttribute("columns")%>;
console.log(11111111111111,columns);
var width=document.documentElement.clientWidth;
var height=document.documentElement.clientHeight;

</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/dbaasHistoryDetail.js"></script>
</head>
<body>
<div id="operResultdetail_div" style="width: 100%;height: 100%"></div>
</body>
</html>