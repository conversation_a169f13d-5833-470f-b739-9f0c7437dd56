//定义workitemid传参
var paramWorkitemId = null;
var baseDecode = false;
Ext.define('paramModel', {
	extend: 'Ext.data.Model',
	fields: [/*{
			name: 'iid',
			type: 'int'
		},*/{
		name: 'iorder',
		type: 'int'
	},{
		name: 'itype',
		type: 'string'
	},{
		name: 'ivalue',
		type: 'string'
	},{
		name: 'idesc',
		type: 'string'
	}]
});

//增加参数信息显示
var paramStore = Ext.create('Ext.data.Store', {
	autoLoad: false,
	autoDestroy: true,
	pageSize: 10,
	model: 'paramModel',
	proxy: {
		type: 'ajax',
		url: 'getTaskParams.do',
		reader: {
			type: 'json',
			root: 'dataList',
			totalProperty: 'total'
		}
	}
});

paramStore.on('beforeload', function(store, options) {
	var new_params = {
		workitemid: paramWorkitemId
	};
	Ext.apply(paramStore.proxy.extraParams, new_params);
});



var paramColumns = [
	// {
	// 	text: '主键',
	// 	dataIndex: 'iid',
	// 	width: 40,
	// 	hidden:true
	// },
	{
		text: '顺序',
		dataIndex: 'iorder',
		width: 40
	},
	// {
	// 	text: '类型',
	// 	dataIndex: 'itype',
	// 	width: 40
	// },
	{
		text: '参数值',
		dataIndex: 'ivalue',
		width: 150,
		renderer: function(val, metadata, record) {
			if (baseDecode) {
				try {
					val = Base64.decode(val, false);
				} catch (e) {
					console.log('参数值解密失败', e)
				}

			}
			var a = val;
			metadata.tdAttr = 'data-qtip="' + a + '"';
			return val;
		}
	},
	{
		text: '描述',
		dataIndex: 'idesc',
		flex : 1
	}
];

var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
	clicksToEdit: 2
});

var paramGrid = Ext.create('Ext.grid.Panel', {
	//title: "参数",
	width: '90%',
	region: 'center',
	//cls:'window_border panel_space_top panel_space_left panel_space_right',
//        height: 280,
//        margin: 10,
//        collapsible : true,
	store: paramStore,
	plugins: [cellEditing],
	//margin:'0 10 5 10',
	border: true,
	columnLines: true,
	columns: paramColumns
});

function getParamsToShow(){
	paramStore.reload();
	showParamsWin = Ext.create('Ext.window.Window', {
		title : '执行参数',
		//autoScroll : true,
		modal : true,
		// resizable : false,
		closeAction : 'hide',
		layout: 'border',
		width : 500,
		height : 320,
		items:[paramGrid]
	});
	showParamsWin.show();

}

//展示时序图
function timingDiagramShow(){
	var timingDiagramWin = Ext.create('Ext.window.Window', {
		title : '任务时序图',
		modal : true,
		closeAction : 'hide',
		layout: 'border',
		width : 1850,
		height : 800,
		items: [
			{
				xtype: 'container',
				layout: 'auto',
				autoScroll: true,
				items:[
					{
						xtype: 'image',
						src: 'images/execLink_timingDiagram.png'}
				]
			}
		]
	});
	timingDiagramWin.show();

}

//启动参数展示弹窗
function getStartParamsToShow(startParams){
	var paramsJson = Ext.create('Ext.form.field.TextArea', {
		name: 'paramsJson',
		displayField: 'paramsJson',
		emptyText: '启动参数...',
		columnWidth: 1,
		readOnly: true,
		width : 600,
		height : 670,
		value: startParams,
		autoScroll: true
	});

	var scriptForm = Ext.create('Ext.ux.ideal.form.Panel', {
		bodyCls: 'x-docked-noborder-top',
		border: false,
		collapsible: false,
		items: [paramsJson],
		autoScroll:true
	});
	var showStartParamsWin = Ext.create('Ext.window.Window', {
		title : '启动参数',
		//autoScroll : true,
		modal : true,
		// resizable : false,
		closeAction : 'hide',
		layout: 'border',
		width : 650,
		height : 780,
		items:[scriptForm]
	});
	showStartParamsWin.show();

}




