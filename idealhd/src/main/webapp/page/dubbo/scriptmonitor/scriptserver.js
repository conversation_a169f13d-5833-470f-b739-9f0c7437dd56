Ext.onReady(function() {
    destroyRubbish();
    var rowExpanderLogForTestExec;
    var scriptmonitorinfoins_storeForTestExec;
    var scriptmonitorinfoins_gridForTestExec;
    var interVForTestExec = 10;
    var interPVForTestExec = 20;
    var lastIdForTestExec;
    var lastRowIndexForTestExec;
    var lastrequestIdForTestExec;
    var lastiipForTestExec;
    var lastiportForTestExec;
    var flagForTestExec = 0; // 0:测试     1:生成
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        },
        {name: 'iexecuser',     type: 'string'},
        {name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'}]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    scriptmonitorinfoins_storeForTestExec = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flagForTestExec + '&coatid=' + coatidForTestExec,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
                flowMesshisRefreshForTestExec(lastIdForTestExec, lastRowIndexForTestExec);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatidForTestExec
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime);
                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },{ text: '系统名称',  dataIndex: 'sysName',hidden: !CMDBflag,width:80},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,width:80},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    }, { 
    	text: '执行用户',  
    	dataIndex: 'iexecuser',
    	width:80
    }, { text: '计算机名',  dataIndex: 'hostName',width:80},
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        xtype : 'actiontextcolumn',
        width: 220,
		align : 'left',
        items : [{
			text : '详情',
			iconCls : 'monitor_search',
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
            	var state =  grid.getStore().data.items[rowIndex].data.state;
            	var agentIp = grid.getStore().data.items[rowIndex].data.agentIp;
            	var agentPort =  grid.getStore().data.items[rowIndex].data.agentPort;
            	var agentPort =  grid.getStore().data.items[rowIndex].data.agentPort;
            	if (state == '30' || state == '40' || state == '50') {
            		if(script_showGridSwitch && scriptType=='sql'){
            			openSqlActWindow(iid);
                	}else{
                		openActWindow(iid,state,agentIp,agentPort);
                	}
                }else if (state == '-1' || state == '1') {
                	flowMesshisForTestExec(iid,rowIndex);
                }else{
                	if(script_showGridSwitch && scriptType=='sql'){
                		openSqlActWindow(iid);
                	}else{
                		openActWindow(iid,state,agentIp,agentPort);
                	}
                }
			}
		},{
			text : '放大',
			iconCls : 'monitor_search',
			getClass : function(v, metadata, record) {
				if (isWinForTestExec == 1 || isWinForTestExec == 'null') {
					return 'x-hidden';
				}
			},
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
            	var agentIp = grid.getStore().data.items[rowIndex].data.agentIp;
            	var agentPort =  grid.getStore().data.items[rowIndex].data.agentPort;
            	if (isWinForTestExec != 1) {
                    loggerDetailForTestExec(iid,agentIp,agentPort);
                }
			}
		},{
			text : '重试',
			iconCls : 'monitor_search',
			getClass : function(v, metadata, record) {
				var state=record.data.state;
				if (state != '30' || state != '40' || state != '50') {
					return 'x-hidden';
				}
			},
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
            	var state =  grid.getStore().data.items[rowIndex].data.state;
            	if (state == '30' || state == '40' || state == '50') {
            		reTryScriptServerForTestExec(iid,state);
                }
			}
		},{
			text : '忽略',
			iconCls : 'monitor_search',
			getClass : function(v, metadata, record) {
				var state=record.data.state;
				if (state != '30' || state != '40' || state != '50') {
					return 'x-hidden';
				}else if (state != '-1' || state != '1') {
					return 'x-hidden';
				}
			},
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
            	var state =  grid.getStore().data.items[rowIndex].data.state;
            	if (state == '30' || state == '40' || state == '50') {
            		skipScriptServerForTestExec(iid,state);
                } else if (state == '-1' || state == '1') {
                	skipScriptServerForTestExec(iid,state);
                }
			}
		}]
        
        
        
//        renderer: function(value, p, record, rowIndex) {
//            var iid = record.get('iid'); // 其实是requestID
//            var state = record.get('state');
//            var zoomStr = "";
////            if (isWinForTestExec != 1) {
////                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetailForTestExec(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
////            }
////            zoomStr = '';
//            if (state == '30' || state == '40' || state == '50') {
////                return '<span class="switch_span">' + 
////                '<a href="javascript:void(0)" onclick="openActWindow(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情1</a>&nbsp;&nbsp;' + zoomStr + 
////                '<a href="javascript:void(0)" onclick="reTryScriptServerForTestExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试1</a>&nbsp;&nbsp;' + 
////                '<a href="javascript:void(0)" onclick="skipScriptServerForTestExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略1</a>&nbsp;&nbsp;' + '</span>';
//            } else if (state == '-1' || state == '1') {
////                return '<span class="switch_span">' + 
////                '<a href="javascript:void(0)" onclick="flowMesshisForTestExec(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情3</a>&nbsp;&nbsp;' + zoomStr + 
////                '<a href="javascript:void(0)" onclick="skipScriptServerForTestExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略3</a>&nbsp;&nbsp;' + '</span>';
//            } else {
////                return '<span class="switch_span">' + 
////                '<a href="javascript:void(0)" onclick="openActWindow(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情2</a>&nbsp;&nbsp;' + zoomStr + '</span>';
//            }
//        }
    }];

    rowExpanderLogForTestExec = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForTestExec{iid}">', 
        '<pre  onselectstart="return true" id="steptextareahisForTestExec{iid}"  class="monitor_desc"></pre>', 
        '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span" {state:this.toShow}>自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshIdForTestExec" name="rowFreshIdForTestExec" >&nbsp;秒</span>', 
        '&nbsp;&nbsp;&nbsp;<input {state:this.toShow} type="button" value="刷新" onclick="loadShelloutputhisForTestExec({iid},\'{agentIp}\',{agentPort})" class="Common_Btn Monitor_Btn">',
        '&nbsp;&nbsp;&nbsp;<input {state:this.toShow} type="button" value="终止" onclick="scriptServerStopForTestExec({iid},{state})" class="Common_Btn Monitor_Btn">', '</div>',
        	{
            toShow: function(state){
             var flag = '';
              if(state==20 || state==60){
                flag = 'style="display: none;"';
              }
              return flag;
           }
	     }]
    });
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        hidden: stateCode==20 || stateCode==60,
        name: "pageFreshTime",
        value: interPVForTestExec
    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_gridForTestExec.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right',
        value: serviceNameForTestExec
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserForTestExec
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startTimeForTestExec
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: endTimeForTestExec
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        cls:'sc_tlbar_height',
        border: false,
        dockedItems : [{
			xtype : 'toolbar',
			/*baseCls:'customize_gray_back',  */
			border : false,
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			/*baseCls:'customize_gray_back',*/  
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    var oam_act_form = Ext.create('Ext.form.Panel', {
		frame : true,
		border : false,
		bodyCls:'fm-spinner',
		layout : {
			type : 'hbox',
			align : 'middle'
		},
		defaults : {
			anchor : '100%'
		},
		items : [
				{
            xtype: "label",
            hidden: stateCode==20 || stateCode==60,
            text: '自动刷新'
        },pageFreshTime , {
					xtype : 'label',
					text : '    秒',
				} ]
	});
    
    scriptmonitorinfoins_gridForTestExec = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	store: scriptmonitorinfoins_storeForTestExec,
    	autoScroll: true,
        border: false,
        columnLines: true,
        padding : grid_space,
        columns: scriptmonitorinfoins_columns,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
      //  bbar: pageBar,
        ipageItems : [/*{
            xtype: "label",
            hidden: stateCode==20 || stateCode==60,
            text: "自动刷新"
        },
        pageFreshTime,
        {
            xtype: "label",
            hidden: stateCode==20 || stateCode==60,
            text: "  秒"
        }*/oam_act_form,
        {
            xtype: 'button',
            cls: 'Common_Btn',
            hidden: stateCode==20 || stateCode==60,
            text: '刷新',
            listeners: {
                click: function() {
                    if (refreshObjForTestExec) {
                        clearInterval(refreshObjForTestExec);
                    }
                    refreshPage();
                    // var interValue =
                    // document.getElementById('pageFreshTime').value;
                    var interValue = pageFreshTime.getValue();
                    interPVForTestExec = interValue;
                    if (interPVForTestExec < 20) {
                        interPVForTestExec = 20;
                    }
                    refreshObjForTestExec = setInterval(refreshPage, interPVForTestExec * 1000);
                }
            }
        },
        {
            xtype: 'button',
            cls : 'Common_Btn',
            hidden: stateCode==20 || stateCode==60,
            text: '终止',
            listeners: {
                click: function() {
                    var data = getCHKBoxIds();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                        function(button, text) {
                            if (button == "yes") {
                                if (data == '-1') {
                                    Ext.Msg.alert('提示', '操作执行成功!');
                                    scriptmonitorinfoins_storeForTestExec.reload();
                                    return;
                                }
                                Ext.MessageBox.wait("数据处理中...", "提示");
                                Ext.Ajax.request({
                                    url: 'scriptServiceShellKill.do',
                                    params: {
                                        flag: flagForTestExec,
                                        insIds: data
                                    },
                                    method: 'POST',
                                    success: function(response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        // 当后台数据同步成功时
                                        if (success) {
                                        	 if (refreshObjShellOutputForTestExec) {
                                                 clearInterval(refreshObjShellOutputForTestExec);
                                             }
                                            scriptmonitorinfoins_storeForTestExec.reload();
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }
        },
        {
            xtype: 'button',
            cls : 'Common_Btn',
            text: '返回',
            listeners: {
                click: function() {
                	if(forMyScript == '脚本编写测试'){
                  	  forwardMyScript();
                  	}else if(forMyScript == '脚本测试'){
                  		forwardScriptTry();
                  	}else{
                  		returnBackForTestExec();
                  	}
                }
            }
        }],
        selModel: selModel,
        plugins: [rowExpanderLogForTestExec],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                /*
						 * var cls = ''; if(record.data.state==10){ cls =
						 * 'row_Blue'; }else if(record.data.state==20){ cls =
						 * 'row_Green'; }else if(record.data.state==30){ cls =
						 * 'row_Red'; }else if(record.data.state==60){ cls =
						 * 'row_Gray'; } else { cls = 'row_Gray'; } return cls;
						 */
                return 'norowexpandblah';
            }
        }/*,

        listeners: {
            itemclick: function(a, record, item, index, e, eOpts) {
                rowExpanderLogForTestExec.toggleRow(index, record);
            }
        }*/
    });

    scriptmonitorinfoins_gridForTestExec.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
        interVForTestExec = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshIdForTestExec').innerText = interVForTestExec;
        } else {
            document.getElementById('rowFreshIdForTestExec').innerHTML = interVForTestExec;
        }
        loadShelloutputhisForTestExec(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
        // refreshObjShellOutputForTestExec = setInterval(function() {
        // loadShelloutputhisForTestExec(record.get('iid'),
        // record.get('agentIp'), record.get('agentPort'));
        // }, 1000);
    });
    scriptmonitorinfoins_gridForTestExec.view.on('collapsebody', function(rowNode, record, expandRow, eOpts) {
        lastIdForTestExec = 0;
        lastRowIndexForTestExec = 0;
        if (refreshObjShellOutputForTestExec) {
            clearInterval(refreshObjShellOutputForTestExec);
        }
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoins_div",
        cls:'customize_panel_back',
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        bodyPadding: 5,
        items: [info_form, scriptmonitorinfoins_gridForTestExec]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

    function forwardMyScript() {
 	   contentPanel.getLoader().load({
 	        url: "forwardScriptServiceRelease.do",
 	        scripts: true
 	    });
 }
 
//跳转到脚本测试
 function forwardScriptTry() {
 	contentPanel.getLoader().load({
 		url: "scriptForTry.do"
 	});
 }
    function refreshPage() {
        // if (refreshObjShellOutputForTestExec) {
        // clearInterval(refreshObjShellOutputForTestExec);
        // }
//    	if(contentPanel.getLoader().url=='forwardscriptcoat.do') {
    		scriptmonitorinfoins_storeForTestExec.reload();
//    	}
    }

    if (refreshObjForTestExec) {
        clearInterval(refreshObjForTestExec);
    }
    refreshObjForTestExec = setInterval(refreshPage, interPVForTestExec * 1000);
    
    
    
    function returnBackForTestExec() {
        lastIdForTestExec = '';
        if (refreshObjShellOutputForTestExec) {
            clearInterval(refreshObjShellOutputForTestExec);
        }
        if (refreshObjForTestExec) {
            if (refreshObjForTestExec) {
                clearInterval(refreshObjForTestExec);
            }
            clearInterval(refreshObjForTestExec);
        }

        contentPanel.getLoader().load({
            url: "forwardscriptcoat.do",
            scripts: true,
            params: {
    			flowId: flowId,
    			forScriptFlow: forScriptFlow,
    			coatId: forMyScript !=""?coatidForTestExec:'',//如果是我的脚本跳转过来后 点击返回跳回到历史页面只查询当前这条记录
            	forMyScript : forMyScript, //如果是我的脚本跳转过来后 点击返回跳回到历史页面只查询当前这条记录
    			flag : 0,
    			filter_scriptName:filter_scriptName,
			    filter_state:filter_state,
			    filter_startTime:filter_startTime,
			    filter_endTime:filter_endTime,
			    filter_serviceName:filter_serviceName,
				filter_serviceState:filter_serviceState,
				filter_serviceStartTime:filter_serviceStartTime,
				filter_serviceEndTime:filter_serviceEndTime
            }
        });
    }



function flowMesshisForTestExec(iruninfoinsid, rowIndex) {
    lastIdForTestExec = iruninfoinsid;
    lastRowIndexForTestExec = rowIndex;
    
    var records = scriptmonitorinfoins_storeForTestExec.getRange(0, scriptmonitorinfoins_storeForTestExec.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLogForTestExec.recordsExpanded[records[i].internalId]) {
            rowExpanderLogForTestExec.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_storeForTestExec.getAt(rowIndex);
     rowExpanderLogForTestExec.toggleRow(rowIndex, record);
}
function loggerDetailForTestExec(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForTestExec) {
        clearInterval(refreshObjShellOutputForTestExec);
    }
    if (refreshObjForTestExec) {
        if (refreshObjForTestExec) {
            clearInterval(refreshObjForTestExec);
        }
        clearInterval(refreshObjForTestExec);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
//            flowId: flowId,
            coatId: coatidForTestExec,
            flag: flagForTestExec
        }
    });
}

function flowMesshisRefreshForTestExec(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
//    var record = scriptmonitorinfoins_storeForTestExec.getAt(rowIndex);
    var records = scriptmonitorinfoins_storeForTestExec.getRange(0, scriptmonitorinfoins_storeForTestExec.getCount());
    var rowFreshValue = document.getElementById('rowFreshIdForTestExec').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForTestExec = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForTestExec').innerText = interVForTestExec;
    } else {
        document.getElementById('rowFreshIdForTestExec').innerHTML = interVForTestExec;
    }

    rowExpanderLogForTestExec.toggleRow(lastRowIndexForTestExec, records[lastRowIndexForTestExec]);
    if(refreshObjShellOutputForTestExec) {
    	clearInterval(refreshObjShellOutputForTestExec);
    }
    refreshObjShellOutputForTestExec = setInterval(function() {
    	if(contentPanel.getLoader().url=='forwardscriptcoat.do') {
    		loadShelloutputhisInfoForTestExec(lastrequestIdForTestExec, lastiipForTestExec, lastiportForTestExec);
    	}
    },
    rowFreshValue * 1000);
    // var record = scriptmonitorinfoins_storeForTestExec.getAt(rowIndex);
    // rowExpanderLogForTestExec.toggleRow(rowIndex, record);
}

function loadShelloutputhisForTestExec(requestId, iip, iport) {
    lastrequestIdForTestExec = requestId;
    lastiipForTestExec = iip;
    lastiportForTestExec = iport;
    if (refreshObjShellOutputForTestExec) {
        clearInterval(refreshObjShellOutputForTestExec);
    }
    var rowFreshValue = document.getElementById('rowFreshIdForTestExec').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForTestExec = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForTestExec').innerText = interVForTestExec;
    } else {
        document.getElementById('rowFreshIdForTestExec').innerHTML = interVForTestExec;
    }
    // document.getElementById('rowFreshIdForTestExec').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfoForTestExec(requestId, iip, iport);
    refreshObjShellOutputForTestExec = setInterval(function() {
        loadShelloutputhisInfoForTestExec(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfoForTestExec(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForTestExec' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: 0
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = msg.message;
            } else {
                document.getElementById(desc).innerText = msg.message;
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerText = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStopForTestExec(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
                Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_storeForTestExec.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
//                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    scriptmonitorinfoins_storeForTestExec.reload();
                    if (refreshObjShellOutputForTestExec) {
                        clearInterval(refreshObjShellOutputForTestExec);
                    }
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServerForTestExec(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
//                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    scriptmonitorinfoins_storeForTestExec.reload();
                    if (refreshObjShellOutputForTestExec) {
                        clearInterval(refreshObjShellOutputForTestExec);
                    }
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServerForTestExec(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
//                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    scriptmonitorinfoins_storeForTestExec.reload();
                    if (refreshObjShellOutputForTestExec) {
                        clearInterval(refreshObjShellOutputForTestExec);
                    }
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    });
}
function openSqlActWindow(requestId) {
	window.open('gotoOperResultData.do?operId='+requestId,"_blank");
}
function openActWindow(requestId, state,agentIp,agentPort) {
	var runningWindow = null;
	var fp2 = null;
	var cmdStr = null;
	var surl = "getScriptExecOutput.do";
	var h = window.innerHeight || document.documentElement.clientHeight
			|| document.body.clientHeight;
	function getData(surl,state, requestId, agentIp, agentPort, cmdValue) {
		Ext.Ajax.request({
			url : surl,
			params : {
				requestId : requestId,
				agentIp : agentIp,
				agentPort : agentPort,
				flag:0,
				input:cmdValue
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
//				var oldValue = Ext.getCmp('baseinfo_form').getForm().findField(
//						"actOutPut").getValue();
				var output = msg.message;

				var json = {
					actName :serviceNameForTestExec+"-"+agentIp+"-"+agentPort ,
					actInsName : serviceNameForTestExec,
					actOutPut : output
				};
				Ext.getCmp('baseinfo_form').getForm().setValues(json);
				actName.getEl().dom.innerHTML='&nbsp;&nbsp;&nbsp;活动名：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:80%;height:35px ;margin-bottom:10px"><pre>'+actName.getValue()+'</pre></div>';
				actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:80%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+actOutPut.getValue()+'</pre></div>';
			},
			failure : function(response, opts) {

			}

		});
	}
	  cmdStr = new Ext.form.TextField({
		anchor : '100%',
		labelWidth : 70,
		fieldLabel : 'CMD',
		disabled:(state==10)?false:true,
		listeners : {
			specialkey : function(textfield, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var cmdV = cmdStr.getValue();
					cmdStr.setValue("");
					getData(surl,state, requestId, agentIp, agentPort, cmdV);
				}
			}
		}
	});
	  var actName= Ext.create ('Ext.form.field.Text',
				{
					fieldLabel : '活动名',
					labelWidth : 70,
					name : 'actName',
					margin :'10 0 5 0',
					anchor : '95%' // anchor width by percentage
				});
		var actOutPut = Ext.create ('Ext.form.field.TextArea',
		{
			fieldLabel : '活动日志',
			labelWidth : 70,
			grow : true,
			height : h-200,
			name : 'actOutPut',
			margin :'15 0 5 0',
			anchor : '95%'
		});
	  fp2 = new Ext.form.Panel({
		border : false,
		id : 'baseinfo_form',
		height : '100%',
		padding : 5,
		fieldDefaults : {
			labelWidth : 60,
			labelAlign : 'right'
		},
		defaultType : 'textfield',
		items : [  actName,{
			fieldLabel : '实例名',
			labelWidth : 70,
			name : 'actInsName',
			hidden : true,
			anchor : '100%' // anchor width by percentage
		},actOutPut,cmdStr ]
	});

	if (runningWindow == undefined || !runningWindow.isVisible()) {
		runningWindow = Ext.create('Ext.window.Window', {
			title : serviceNameForTestExec,
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 600,
			height : h - 95,
			items : [ fp2 ],
			dockedItems : [ {
				xtype : 'toolbar',
				// baseCls:'customize_gray_back',
				dock : 'bottom',
				layout: {pack: 'center'},
				items : [
				 {
					text : '刷新',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						getData(surl,state, requestId, agentIp, agentPort, null);
						cmdStr.setValue("");
					}
				}, {
					text : '终止',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						scriptServerStopForTestExec(requestId,state)
					}
				} ]
			} ],
			layout : 'fit'
		});
	}
	runningWindow.show();
	getData(surl,state, requestId, agentIp, agentPort, null);
}
});