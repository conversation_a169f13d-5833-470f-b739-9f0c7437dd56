Ext.onReady(function() {
	destroyRubbish();
	var scriptmonitor_storeForExec;
	var personExcute_window;
	var flag = 1; // 生产
	var agentiids = new Array();
	let selectedAgent = new Set();
	var showParamsWin = null;
	/*
	 * Ext.define('startUserModel', { extend : 'Ext.data.Model', fields : [{
	 * name : 'iid', type : 'string' }, { name : 'iusername', type :
	 * 'string' }]
	 * 
	 * });
	 */
 	var search_form;
	var stateStore;
	if (projectFlag2LevelScriptHistory == 1) {//光大-数据库云
		stateStore = Ext.create('Ext.data.Store', {
			fields : [ 'id', 'name' ],
			data : [ {
				"id" : "-1",
				"name" : "全部"
			}, {
				"id" : "1",
				"name" : "未运行"
			},
			{
				"id" : "102",
				"name" : "运行"
			},
			{
				"id" : "30",
				"name" : "异常"
			}, {
				"id" : "101",
				"name" : "完成"
			},
			{
				"id" : "60",
				"name" : "终止"
			}]
		});
	}else{
		stateStore= Ext.create('Ext.data.Store', {
			fields : [ 'id', 'name' ],
			data : [ {
				"id" : "-1",
				"name" : "全部"
			}, {
				"id" : "101",
				"name" : "完成"
			},
			/*		        {
			 "id": "20",
			 "name": "正常完成"
			 },
			 {
			 "id": "40",
			 "name": "异常完成"
			 },*/
			{
				"id" : "102",
				"name" : "运行"
			},
			/*		        {
			 "id": "10",
			 "name": "正常运行"
			 },
			 {
			 "id": "50",
			 "name": "异常运行"
			 },*/
			{
				"id" : "30",
				"name" : "异常"
			}, {
				"id" : "60",
				"name" : "终止"
			} ]
		});
	}
	/*
	 * var cataStore = Ext.create('Ext.data.Store', { fields: ['id',
	 * 'name'], data : [ {"id":"-1", "name":"全部"}, {"id":"0",
	 * "name":"测试"}, {"id":"1", "name":"生产"} ] });
	 */

	/*
	 * var startUserStore = Ext.create('Ext.data.Store', { autoLoad :
	 * true, autoDestroy : true, model : 'startUserModel', proxy : {
	 * type : 'ajax', url : 'getStartUser.do', reader : { type : 'json',
	 * root : 'dataList' } } });
	 */

	var date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
	var date2 = new Date(new Date(new Date().toLocaleDateString()).getTime()+24*60*60*1000-1);
	//是否超时下拉内容
	var isTimeOutStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"all", "name":"全部" },
			{"id":"1", "name":"是"},
			{"id":"0", "name":"否"}
		]
	});

	var startTime = Ext.create('Go.form.field.DateTime',
		{
			fieldLabel : '开始时间',
			xtype : 'datefield',
			labelAlign : 'right',
			width : 250,
			labelWidth : 70,
			name : 'startTime',
			format : 'Y-m-d H:i:s',
//				value : filter_startTime
			value:(returnBtn != '')?filter_startTime2LevelScriptHistory:Ext.util.Format.date(Ext.Date.add(date1,Ext.Date.DAY,-1),"Y-m-d H:i:s"),
			listeners: {
				change: function () {
					// if(this.getValue() > Ext.getCmp('startTimeEnd').getValue()){
					// 	Ext.getCmp('startTimeEnd').setValue("");
					// 	Ext.getCmp('endTime').setValue("");
					// 	Ext.getCmp('endTimeEnd').setValue("");
					// }
					startTimeEnd.setMinValue(this.getValue());
					endTime.setMinValue(this.getValue());
					endTimeEnd.setMinValue(this.getValue());
				}
			}
		});

	var startTimeEnd = Ext.create('Go.form.field.DateTime',
		{
			xtype : 'datefield',
			labelAlign : 'right',
			width : 180,
			name : 'startTimeEnd',
			format : 'Y-m-d H:i:s',
			value:(startTimeEnd1 != null && startTimeEnd1 != undefined && startTimeEnd1 != '')?startTimeEnd1:"",
			listeners: {
				change: function () {
					// if(this.getValue() > Ext.getCmp('endTime').getValue()){
					// 	Ext.getCmp('endTime').setValue("");
					// 	Ext.getCmp('endTimeEnd').setValue("");
					// }
					endTime.setMinValue(this.getValue());
					endTimeEnd.setMinValue(this.getValue());
				}
			}
		});

	var endTime = Ext.create('Go.form.field.DateTime',
		{
			fieldLabel : '结束时间',
			xtype : 'datefield',
			labelAlign : 'right',
			width : 250,
			labelWidth : 70,
			name : 'endTime',
			format : 'Y-m-d H:i:s',
			value : filter_endTime2LevelScriptHistory,
			listeners: {
				change: function () {
					// if(this.getValue() > Ext.getCmp('endTimeEnd').getValue()){
					// 	Ext.getCmp('endTimeEnd').setValue("");
					// }
					endTimeEnd.setMinValue(this.getValue());
				}
			}
		});

	var endTimeEnd = Ext.create('Go.form.field.DateTime',
		{
			xtype : 'datefield',
			labelAlign : 'right',
			width : 180,
			name : 'endTimeEnd',
			format : 'Y-m-d H:i:s',
			value:(endTimeEnd1 != null && endTimeEnd1 != undefined && endTimeEnd1 != '')?endTimeEnd1:""
//				value : filter_startTime
// 				value:(filter_startTime2LevelScriptHistory != null && filter_startTime2LevelScriptHistory != undefined && filter_startTime2LevelScriptHistory != '')?filter_startTime2LevelScriptHistory:Ext.util.Format.date(Ext.Date.add(date1,Ext.Date.DAY,-6),"Y-m-d H:i:s")
		});
	search_form = Ext.create('Ext.ux.ideal.form.Panel', {
		layout : 'anchor',
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		iqueryFun : function(){
			    if(!search_form.isValid()){
							Ext.Msg.alert('提示', '查询条件不符合要求！');
							return;
			    }
				scriptmonitor_grid.ipage.moveFirst();
		},
		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : [ {
				fieldLabel : '执行人',
				labelWidth : 70,
				name : 'startUser',
				labelAlign : 'right',
				value:  returnBtn==''?userName:user1,
				width : '16%',
				xtype : 'textfield',
				hidden:fjnxCISwitch //showStartUser2LevelScriptHistory
			}, {
				fieldLabel : '服务名称',
				labelWidth : 70,
				name : 'scriptName',
				labelAlign : 'right',
				width : '16%',
				xtype : 'textfield',
				value : filter_scriptName2LevelScriptHistory
			}, {
				fieldLabel : 'IP',
				labelWidth : 30,
				name : 'agentIp',
				labelAlign : 'right',
				width : '16%',
				regex:/^([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(\.([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])){3}$/,
				regexText:"查询条件不符合要求！",
				xtype : 'textfield',
				value:filter_Ip2LevelScriptHistory,
				hidden:fjnxCISwitch
			},{
				fieldLabel : '执行状态',
				labelAlign : 'right',
				width : '16%',
				labelWidth : 70,
				name : 'state',
				displayField : 'name',
				valueField : 'id',
				store : stateStore,
				queryMode : 'local',
				listeners : {
					afterRender : function(combo) {
						if (filter_state2LevelScriptHistory == '-1') {
							combo.setValue(stateStore.getAt(0).data.id);
						} else if (filter_state2LevelScriptHistory == '101') {
							combo.setValue(stateStore.getAt(1).data.id);
						} else if (filter_state2LevelScriptHistory == '102') {
							combo.setValue(stateStore.getAt(2).data.id);
						} else if (filter_state2LevelScriptHistory == '30') {
							combo.setValue(stateStore.getAt(3).data.id);
						} else if (filter_state2LevelScriptHistory == '60') {
							combo.setValue(stateStore.getAt(4).data.id);
						}
					}
				},
				// editable:false,
				xtype : 'combobox'
			},{
				fieldLabel : '任务名称',
				labelWidth : 70,
				name : 'taskName',
				labelAlign : 'right',
				width : '16%',
				xtype : 'textfield'
			}, {
				fieldLabel : '单号',
				labelWidth : 70,
				hidden: !scriptSpdbExecHisSwitch && !scriptHisBufferNumberSwitch,
				name : 'butterflyversions',
				labelAlign : 'right',
				width : '16%',
				xtype : 'textfield'
			}
				,{
					fieldLabel : '是否超时',
					labelAlign : 'right',
					width : '16%',
					labelWidth : 70,
					hidden: !execHisTimeOutSwitch,
					name : 'isTimeOut',
					displayField : 'name',
					valueField : 'id',
					store : isTimeOutStore,
					queryMode : 'local',
					// editable:false,
					xtype : 'combobox',
					listeners : {
						afterRender : function(combo) {
							combo.setValue(isTimeOutStore.getAt(0).data.id);
						}
					}
				}

			]
		},
		{
		xtype : 'toolbar',
		baseCls:'customize_gray_back',
		border : false,
		dock : 'top',
		items : [startTime,{
			xtype: 'label',
			text:'-'
		},startTimeEnd, endTime,{
				xtype: 'label',
				text:'-'
			},endTimeEnd
		,'->',{
			xtype : 'button',
			// columnWidth:.07,
			text : '查询',
			cls : 'Common_Btn',
			handler : function() {
				if(!search_form.isValid()){
						Ext.Msg.alert('提示', '查询条件不符合要求！');
						return;
				}
				scriptmonitor_grid.ipage.moveFirst();
			}
		}, {
			xtype : 'button',
			// columnWidth:.07,
			text : '清空',
			cls : 'Common_Btn',
			handler : function() {
				clearQueryWhere();
			}
		},{
			xtype : 'button',
			// columnWidth:.07,
			text : '导出',
			cls : 'Common_Btn',
			handler : exportExcel/*,
			handler : function() {
				clearQueryWhere();
			}*/
		},{
			xtype : 'button',
			// columnWidth:.07,
			text : '总览导出',
			hidden: !scriptSpdbExecHisSwitch,
			cls : 'Common_Btn',
			//handler : exportExcel/*,
			handler : function() {
				var iidsStr = "";
				if(agentiids.length < 1){
					Ext.Msg.alert('提示',"请选择数据!");
				}else{
					iidsStr = agentiids.toString();
					//var url = "multiExportCoatResult.do?data="+JSON.stringify(array)+"&itemIds="+itemIds+"&flowIds="+flowIds;
					var url = "overViewExportResult.do?data="+iidsStr;
					window.location.href = encodeURI(url);
				}
			}
		}
		, {
			xtype : 'button',
			text : '返回',
			hidden : forScriptFlow2LevelScriptHistory != 1 || filter_fromMenu2LevelScriptHistory!=='taskCustomPage',
			cls : 'Common_Btn',
			handler : function() {
				forwardtestmainExec();
			}
		}]
	} ]
	});

	Ext.define('scriptmonitorData', {
		extend : 'Ext.data.Model',
		fields : [
		{
			name : 'datauuid',
			type : 'string'
		},
		{
			name : 'iworkitemid',
			type : 'string'
		},{
			name : 'finisheFlag',
			type : 'string'
		},{
			name : 'iid',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		}, {
			name : 'taskName',
			type : 'string'
		}, {
			name : 'state',
			type : 'int'
		}, {
			name : 'cata',
			type : 'int'
		}, {
			name : 'flowId',
			type : 'int'
		}, {
			name : 'actNo',
			type : 'int'
		},
		{
			name : 'timeOut',
			type : 'string'
		},
		{
			name : 'startUser',
			type : 'string'
		},
		{
			name : 'butterflyversionp',
			type : 'string'
		},
		{
			name : 'itasktimep',
			type : 'string'
		}
		, {
			name : 'startTime',
			type : 'string'
		}, {
			name : 'endTime',
			type : 'string'
		}, {
			name : 'actType',
			type : 'string'
		}, {
			name : 'serverNum',
			type : 'int'
		}, {
			name : 'groupName',
			type : 'string'
		}, {
			name : 'serviceGId',
			type : 'string'
		},{
			name : 'totalNum',
			type : 'int'
		},{
			name : 'runNum',
			type : 'int'
		},{
			name : 'operNum',
			type : 'String'
		},{
			name : 'outputCount',
			type : 'int'
		},{
			name : 'runTime',
			type : 'int'
		} ]
	});

	scriptmonitor_storeForExec = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'scriptmonitorData',
		proxy : {
			type : 'ajax',
			url : 'getScriptCoatList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			},
			timeout: 600000
		}
	});

	var operationCol = [{
		text : '详情',
		iconCls : 'monitor_search',
		handler : function(grid, rowIndex) {
			var coatid = grid.getStore().data.items[rowIndex].data.iid;
			var cata = 1;// record.get('cata');
			var actType =grid.getStore().data.items[rowIndex].data.actType;
			var actNo =grid.getStore().data.items[rowIndex].data.actNo;
			var flowId = grid.getStore().data.items[rowIndex].data.flowId;
			var state = grid.getStore().data.items[rowIndex].data.state;
			var workitemid = grid.getStore().data.items[rowIndex].data.iworkitemid;
			if (actType == '2') {
				forwardUTruninfo(flowId, coatid, actNo, state, cata);
			}else if(actType == '0'){
				forwardruninfoForExec(coatid, cata ,workitemid);
			}
		}
	},{
		text : '导出',
		iconCls : 'monitor_export',
		getClass : function(v, metadata, record) {
			var actType = record.get('actType');
			if(actType!=0 || fjnxCISwitch){
				return 'x-hidden';
			}
		},
		handler : function(grid, rowIndex) {
			var coatid = grid.getStore().data.items[rowIndex].data.iid;
			var cata = 1;
			var iworkitemidparams = grid.getStore().data.items[rowIndex].data.iworkitemid;
			resultExportForExec(coatid, cata,iworkitemidparams);
		}
	},{
		text : '参数',
		iconCls: 'monitor_search',
		getClass : function(v, metadata, record) {
			if(fjnxCISwitch){
				return 'x-hidden';
			}
		},
		handler: function(grid, rowIndex, colIndex) {
			//获取iwrokitemid
			var iworkitemidparams = grid.getStore().data.items[rowIndex].data.iworkitemid;
			paramWorkitemId = iworkitemidparams;
			getParamsToShow();
		}
	}];

	if(taskApplyAttachment){
		operationCol = [{
			text : '详情',
			iconCls : 'monitor_search',
			handler : function(grid, rowIndex) {
				var coatid = grid.getStore().data.items[rowIndex].data.iid;
				var cata = 1;// record.get('cata');
				var actType =grid.getStore().data.items[rowIndex].data.actType;
				var actNo =grid.getStore().data.items[rowIndex].data.actNo;
				var flowId = grid.getStore().data.items[rowIndex].data.flowId;
				var state = grid.getStore().data.items[rowIndex].data.state;
				var workitemid = grid.getStore().data.items[rowIndex].data.iworkitemid;
				if (actType == '2') {
					forwardUTruninfo(flowId, coatid, actNo, state, cata);
				}else if(actType == '0'){
					forwardruninfoForExec(coatid, cata , workitemid);
				}
			}
		},{
			text : '导出',
			iconCls : 'monitor_export',
			getClass : function(v, metadata, record) {
				var actType = record.get('actType');
				if(actType!=0 || fjnxCISwitch){
					return 'x-hidden';
				}
			},
			handler : function(grid, rowIndex) {
				var coatid = grid.getStore().data.items[rowIndex].data.iid;
				var cata = 1;
				var iworkitemidparams = grid.getStore().data.items[rowIndex].data.iworkitemid;
				resultExportForExec(coatid, cata,iworkitemidparams);
			}
		},{
			text : '下载',
			iconCls: 'script_download',
			getClass : function(v, metadata, record) {
				if(fjnxCISwitch){
					return 'x-hidden';
				}
			},
			handler: function(grid, rowIndex, colIndex) {
				//获取uuid和iwrokitemid
				var uuid = grid.getStore().data.items[rowIndex].data.datauuid;
				var iworkitemid = grid.getStore().data.items[rowIndex].data.iworkitemid;
				var taskName = grid.getStore().data.items[rowIndex].data.taskName;
				//执行下载
				window.location.href = 'downloadExecuteHisAttachment.do?uuid='+uuid+'&iworkitemid='+iworkitemid+'&taskName='+taskName;
			}
		},{
			text : '参数',
			iconCls: 'monitor_search',
			getClass : function(v, metadata, record) {
				if(fjnxCISwitch){
					return 'x-hidden';
				}
			},
			handler: function(grid, rowIndex, colIndex) {
				//获取iwrokitemid
				var iworkitemidparams = grid.getStore().data.items[rowIndex].data.iworkitemid;
				paramWorkitemId = iworkitemidparams;
				getParamsToShow();
			}
		}]
	}
	if (scriptfunctionoutputswitch){
		operationCol.push({
			text : '脚本输出',
			iconCls : 'monitor_search',
			getClass : function(v, metadata, record) {
				var outputCount = record.get('outputCount');
				if(outputCount==0 || fjnxCISwitch){
					return 'x-hidden';
				}
			},
			handler : function(grid, rowIndex) {
				var coatid = grid.getStore().data.items[rowIndex].data.iid;
				var cata = 1;// record.get('cata');
				var actType =grid.getStore().data.items[rowIndex].data.actType;
				var actNo =grid.getStore().data.items[rowIndex].data.actNo;
				var flowId = grid.getStore().data.items[rowIndex].data.flowId;
				var state = grid.getStore().data.items[rowIndex].data.state;
				var workitemid = grid.getStore().data.items[rowIndex].data.iworkitemid;
				var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
				var taskName = grid.getStore().data.items[rowIndex].data.taskName;

				var scriptName = search_form.getForm().findField("scriptName").getValue();
				var state = search_form.getForm().findField("state").getValue();
				var startTime = search_form.getForm().findField("startTime").getRawValue();
				var endTime = search_form.getForm().findField("endTime").getRawValue();
				var agentip = search_form.getForm().findField("agentIp").getValue();
				contentPanel.getLoader().load({
					url : "scriptOutputPage.do",
					scripts : true,
					params : {
						flowId : flowIdFor2LevelScriptHistory,
						forScriptFlow : forScriptFlow2LevelScriptHistory,
						iflowId:flowId,
						coatid : coatid,
						flag : flag,
						filter_scriptName : scriptName,
						filter_Ip:agentip,
						filter_state : state,
						filter_startTime : startTime,
						filter_endTime : endTime,
						filter_serviceName : filter_serviceName2LevelScriptHistory,
						filter_serviceState : filter_serviceState2LevelScriptHistory,
						filter_serviceStartTime : filter_serviceStartTime2LevelScriptHistory,
						filter_serviceEndTime : filter_serviceEndTime2LevelScriptHistory,
						switchFlag:projectFlag2LevelScriptHistory,
						queryFlag:monitorFlag2LevelScriptHistory,
						hidereturn:'1',
						workitemid:workitemid,
						type:'execHistory',
						serviceName:serviceName,
						taskName:taskName
					}
				});
			}
		})
	}

	var scriptmonitor_columns = [
			{
				text : 'datauuid',
				dataIndex : 'datauuid',
				hidden : true
			},
			{
				text : 'iworkitemid',
				dataIndex : 'iworkitemid',
				hidden : true
			},
			{
				text : '序号',
				xtype : 'rownumberer',
				width : 70
			},
			{
				text : '执行状态',
				dataIndex : 'state',
				width : 100,
				renderer : function(value, p, record) {
					//agent全部正常完成显示绿色，否则显示红色
					if(record.get('finisheFlag') == 'green'){
						return "<span style='color: green'>完成</span>";
					}else if(record.get('finisheFlag') == 'red'){
						return "<span style='color: red'>完成</span>";
					}else{
						var backValue = "";
						if (value == -1) {
							backValue = '<span class="Not_running State_Color">未运行</span>';
						} else if (value == 1) {
							backValue = '<span class="Not_running State_Color">初始化/span>';
						} else if (value == 10 || value == 11) {
							backValue = '<span class="Run_Green State_Color">运行</span>';
						} else if (value == 20 || value == 5) {
							backValue = "<span class='Complete_Green State_Color'>完成</span>"
						} else if (value == 30) {
							backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
						} else if (value == 40) {
							backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
						} else if (value == 50) {
							backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
						} else if (value == 60) {
							backValue = '<span class="Kill_red State_Color">已终止</span>';
						}
						return backValue;
					}
				}
			},{
				text : '设备数量',
				dataIndex : 'operNum',
				width : 80,
				hidden: fjnxCISwitch,
				renderer : function(value, p, record) {
					var backValue = "";
					var totalNum = record.get('totalNum');
					var runNum = record.get('runNum');
					backValue =  runNum+"/"+totalNum;
					return backValue;
				}
			},{
				text : 'gid',
				dataIndex : 'serviceGId',
				hidden : true
			},
			{
				text : '实例主键',
				dataIndex : 'iid',
				hidden : true
			},
			{
				text : 'actType',
				dataIndex : 'actType',
				hidden : true
			},
			{
				text : '服务名称',
				dataIndex : 'serviceName',
				flex : 1
			},
			{
				text : '任务名称',
				dataIndex : 'taskName',
				width : 150
			},
			{
				text : '脚本名称',
				dataIndex : 'scriptName',
				width : 150
			},
			{
				text : fjnxCISwitch?'启动人':'执行人',
				dataIndex : 'startUser',
				width : 150
			},
			{
				text : '单号',
				hidden:!scriptSpdbExecHisSwitch	&& !scriptHisBufferNumberSwitch,
				dataIndex : 'butterflyversionp',
				width : 150
			},
			{
				text : '执行策略',
				dataIndex : 'itasktimep',
				hidden:!scriptSpdbExecHisSwitch,
				width : 150
			},
			{
				text : '开始时间',
				dataIndex : 'startTime',
				width : 150
			},
			{
				text : '结束时间',
				dataIndex : 'endTime',
				width : 150
			},
			{
				text : '超时状态',
				dataIndex : 'timeOut',
				hidden : !execHisTimeOutSwitch,
				renderer: function(value, p, record) {
					var backValue = "";
					if (value <= 0) {
						backValue = '<span class="Ignore State_Color">否</span>';
					} else{
						backValue = '<span class="Kill_red State_Color">是</span>';
					}
					return backValue;
				}
			},
			{
				text : '服务组名称',
				dataIndex : 'groupName',
				width : 150,
				hidden:true//projectFlag2LevelScriptHistory==1?true:false
			},
			// { text: '服务器个数', dataIndex: 'serverNum',width:100},
			/*
			 * { text: '类别', dataIndex:
			 * 'cata',width:50,renderer:function(value,p,record){ var
			 * backValue = ""; if(value==0){ backValue = "测试"; }else
			 * if(value==1){ backValue = "生产"; } return backValue; }},
			 */
			{
				text : '运行时长(秒)',
				dataIndex : 'runTime',
				width : 150
			},
			 {
				text : '操作',
				xtype : 'actiontextcolumn',
				width : 300,
				align : 'left',
	//						menuDisabled : true,
				items : operationCol
			}
			];

	scriptmonitor_storeForExec
			.on('beforeload',
					function(store, options) {
						var new_params = {
							flowId : flowIdFor2LevelScriptHistory,
							agentIp:search_form.getForm().findField("agentIp").getValue(),
							scriptName : search_form.getForm().findField(
									"scriptName").getValue(),
							// startUser:search_form.getForm().findField("startUser").getValue(),
							state : search_form.getForm().findField("state")
									.getValue(),
							cata : 1,// search_form.getForm().findField("cata").getValue(),
							startTime : search_form.getForm().findField(
									"startTime").getValue(),
							startTimeEnd : search_form.getForm().findField(
								"startTimeEnd").getValue(),
							endTime : search_form.getForm()
									.findField("endTime").getValue(),
							endTimeEnd : search_form.getForm()
								.findField("endTimeEnd").getValue(),
							forScriptFlow : forScriptFlow2LevelScriptHistory,
							switchFlag:projectFlag2LevelScriptHistory,
							butterflyversion:search_form.getForm().findField(
								"butterflyversions").getValue(),
							isTimeOut:search_form.getForm().findField("isTimeOut").getValue(),
							groupUserIIDs: "his",
							taskName: search_form.getForm().findField("taskName").getValue()
						};
						if (!fjnxCISwitch) {
							new_params.startUserNameValue = search_form.getForm().findField("startUser").getValue()
						} else {
							new_params.startUserNameValue = ''
						}
						Ext.apply(scriptmonitor_storeForExec.proxy.extraParams,
								new_params);
					});
	scriptmonitor_storeForExec.addListener('load', function(me, records, successful, eOpts) {
		if (agentiids.length>0) {
			var chosedRecords = []; //存放选中记录
			$.each(records,
				function(index, record) {
					if (agentiids.indexOf(record.get('iid')) > -1) {
						chosedRecords.push(record);
					}
				});
			scriptmonitor_grid.getSelectionModel().select(chosedRecords, false, false); //选中记录
		}
	});
//	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		store : scriptmonitor_storeForExec,
//		dock : 'bottom',
//		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//	    border:false,
//		displayInfo : true
//	});
	
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
//		id:'selModel',
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
	
	var scriptmonitor_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'center',
		store : scriptmonitor_storeForExec,
		cls:'customize_panel_back',
		 ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		selModel:selModel,
		selType: 'cellmodel',
		border : true,
		columnLines : true,
		// padding : panel_margin,
		padding : grid_space,
//		bbar : pageBar,
		columns : scriptmonitor_columns,
		listeners: {
			select: function( e, record, index, eOpts ){
				if(agentiids.indexOf(record.get('iid'))==-1) {
					agentiids.push(record.get('iid'));
				}
				if (!selectedAgent.has(record.data)) {
					selectedAgent.add(record.data);
				}
			},
			deselect: function( e, record, index, eOpts ){
				if(agentiids.indexOf(record.get('iid'))>-1) {
					agentiids.remove(record.get('iid'));
				}
				if (selectedAgent.has(record.data)) {
					selectedAgent.delete(record.data);
				}
			}
		}
	/*
	 * , viewConfig:{
	 * getRowClass:function(record,rowIndex,rowParams,arriveStore){ var
	 * cls = ''; if(record.data.state==10){ cls = 'row_Blue'; }else
	 * if(record.data.state==20){ cls = 'row_Green'; }else
	 * if(record.data.state==30){ cls = 'row_Red'; }else
	 * if(record.data.state==40){ cls = 'row_Red'; }else
	 * if(record.data.state==50){ cls = 'row_Red'; }else
	 * if(record.data.state==60){ cls = 'row_Gray'; } else { cls =
	 * 'row_Gray'; } return cls; } }
	 */
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptcoatmonitorForExec_area",
		layout : 'border',
		bodyCls:'service_platform_bodybg',
		bodyPadding : grid_margin,
		border : true,
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		items : [ search_form, scriptmonitor_grid ]
	/*
	 * , buttonAlign:'center', buttons: [ { text: '返回', handler :
	 * function() { contentPanel.getLoader().load({url:
	 * "scriptMonitor.do?cata="+flag,scripts: true}); } }]
	 */
	});
	
	function exportExcel(){
		var records = scriptmonitor_grid.getSelectionModel().getSelection();
		var array = [];
		var flowIds = [];
		var itemIds = [];
		if(records.length != 0){
			Ext.Array.each(records, function(recordObj) {
				var item = {};
				var flowid = recordObj.get('flowId');
				var taskName = recordObj.get('taskName');
				var itemid = recordObj.get('iid');
				item.flowid = flowid;
				item.taskName = taskName;
				array.push(item);
				flowIds.push(flowid);
				itemIds.push(itemid);
			});
		}else{
			/*records = scriptmonitor_grid.getStore().getRange(0,scriptmonitor_storeForExec.getCount());
			Ext.Array.each(records, function(recordObj) {
				var item = {};
				var flowid = recordObj.get('flowId');
				var taskName = recordObj.get('taskName');
				var itemid = recordObj.get('iid');
				item.flowid = flowid;
				item.taskName = taskName;
				array.push(item);
				flowIds.push(flowid);
				itemIds.push(itemid);
			});*/
			Ext.Msg.alert('提示',"请选择行!");
			return ;
		}
		var url = "multiExportCoatResult.do?data="+JSON.stringify(array)+"&itemIds="+itemIds+"&flowIds="+flowIds;
		window.location.href = encodeURI(url);
	}

	function clearQueryWhere() {
		flowIdFor2LevelScriptHistory=0;
		search_form.getForm().findField("agentIp").setValue(''),
		search_form.getForm().findField("scriptName").setValue(''),
		 search_form.getForm().findField("startUser").setValue(''),
		search_form.getForm().findField("state").setValue("-1"),
			search_form.getForm().findField("isTimeOut").setValue('all'),
		// search_form.getForm().findField("cata").setValue("-1"),
		search_form.getForm().findField("startTime").setValue(''), search_form
				.getForm().findField("endTime").setValue('');
		search_form
			.getForm().findField("endTimeEnd").setValue('');
		search_form
			.getForm().findField("startTimeEnd").setValue('');
		search_form.getForm().findField(
			"butterflyversions").setValue('');
		search_form.getForm().findField("taskName").setValue('');
	}

	contentPanel.on('resize', function() {
		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.setHeight() - modelHeigth);
	});
	
	
	
	
	
	//----------------------------------
	
//	function utStartSuccessCallback() {
//	scriptmonitor_storeForExec.reload();
//}

function forwardUTruninfo(flowId, coatid, actNo, state, cata) {
	personExcute_window = Ext.create('Ext.window.Window', {
		title : '人工提醒',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : 600,
		height : 400,
		loader : {
			url : 'scriptServiceUT.do',
			params : {
				actNo : actNo,
				coatid : coatid,
				flowId : flowId,
				flag : cata,
				state : state
			},
			autoLoad : true,
			//			autoDestroy : true,
			scripts : true
		}
	}).show();
}

function forwardtestmainExec() {
	//常用任务白名单脚本执行完跳转过来的话返回常用任务页面
	if(filter_fromMenu2LevelScriptHistory==='taskCustomPage'){
		contentPanel.setTitle('常用任务');
		contentPanel.getLoader().load({
			url : "flowCustomizedTemplateForProduct.do",
			scripts : true
		});
	}else {
		contentPanel.getLoader().load({
			url : "scriptMonitorForFlowProduct.do",
			scripts : true,
			params : {
				filter_serviceName : filter_serviceName2LevelScriptHistory,
				filter_serviceState : filter_serviceState2LevelScriptHistory,
				filter_serviceStartTime : filter_serviceStartTime2LevelScriptHistory,
				filter_serviceEndTime : filter_serviceEndTime2LevelScriptHistory
			}
		});
	}

}
function forwardruninfoForExec(coatid, flag , workitemid) {
	var scriptName = search_form.getForm().findField("scriptName").getValue();
	var state = search_form.getForm().findField("state").getValue();
	var startTime = search_form.getForm().findField("startTime").getRawValue();
	var endTime = search_form.getForm().findField("endTime").getRawValue();
	var agentip = search_form.getForm().findField("agentIp").getValue();
	var startTimeEnd = search_form.getForm().findField("startTimeEnd").getRawValue();
	var endTimeEnd = search_form.getForm().findField("endTimeEnd").getRawValue();
	var user = search_form.getForm().findField("startUser").getRawValue();
	contentPanel.getLoader().load({
		url : "forwardscriptserverForFlowForExec.do",
		scripts : true,
		params : {
			flowId : flowIdFor2LevelScriptHistory,
			forScriptFlow : forScriptFlow2LevelScriptHistory,
			coatid : coatid,
			flag : flag,
			filter_scriptName : scriptName,
			filter_Ip:agentip,
			filter_state : state,
			filter_startTime : startTime,
			filter_endTime : endTime,
			filter_serviceName : filter_serviceName2LevelScriptHistory,
			filter_serviceState : filter_serviceState2LevelScriptHistory,
			filter_serviceStartTime : filter_serviceStartTime2LevelScriptHistory,
			filter_serviceEndTime : filter_serviceEndTime2LevelScriptHistory,
			switchFlag:projectFlag2LevelScriptHistory,
			queryFlag:monitorFlag2LevelScriptHistory,
			hidereturn:'1',
			workitemid:workitemid,
			startTimeEnd:startTimeEnd,
			endTimeEnd:endTimeEnd,
			user:user
		}
	});
}
function resultExportForExec(coatid, flag,workitemid) {
	window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag='
			+ flag + '&workitemid=' + workitemid;
}

//function scriptCoatStopForExec(coatid, flag) {
//	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
//		if (btn == 'yes') {
//			Ext.Ajax.request({
//				url : 'scriptCoatStop.do',
//				method : 'POST',
//				params : {
//					coatid : coatid,
//					flag : flag
//				},
//				success : function(response, request) {
//					var success = Ext.decode(response.responseText).success;
//					var message = Ext.decode(response.responseText).message;
//					if (success) {
//						Ext.Msg.alert('提示', message);
//					} else {
//						Ext.Msg.alert('提示', message);
//					}
//					scriptmonitor_storeForExec.reload();
//				},
//				failure : function(result, request) {
//					secureFilterRs(result, "操作失败！");
//				}
//			});
//		}
//	})
//
//}

	//开始时间初始化校验，限制结束时间
	var startTimeVal = startTime.getValue();
	startTimeEnd.setMinValue(startTimeVal);
	endTime.setMinValue(startTimeVal);
	endTimeEnd.setMinValue(startTimeVal);

});

