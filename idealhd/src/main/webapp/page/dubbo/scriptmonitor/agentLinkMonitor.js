var queryData = {}
var flag3ForExecForFlow = 1
var refreshObjShellOutputForExecForFlow
var refreshObjForExecForFlow
var interPV3ForExecForFlow = 20
Ext.onReady(function () {

    var pageFreshTime = new Ext.form.field.Number({
        width: 50,
        minValue: 20,
        name: "pageFreshTime",
        value: interPV3ForExecForFlow
    });

    var oam_act_form = Ext.create('Ext.form.Panel', {
        frame: true,
        border: false,
        bodyCls: 'fm-spinner',
        layout: {
            type: 'hbox',
            align: 'middle'
        },
        defaults: {
            anchor: '100%'
        },
        items: [
            {
                xtype: "label",
                text: "自动刷新"
            }, pageFreshTime, {
                xtype: 'label',
                text: '    秒',
            }]
    });

    var queryIP = Ext.create('Ext.form.TextField', {
        fieldLabel: 'IP地址',
        labelWidth: 60,
        regex: /(^\d{1,3}\.(\d{1,3}\.){0,2}(\d{1,3})?$)|(^([0-9a-fA-F]{1,4}:){1,7}([0-9a-fA-F]{1,4})?$)|(^[0-9a-zA-Z]+(\.com)?$)/,
        regexText: '不符合IPv4、IPv6、域名校验',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    getData()
                }
            }
        }
    })

    var columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    }, {
        dataIndex: 'agentInsStatus',
        text: 'agent执行状态',
        width: 100,
        renderer: function (value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    }, {
        dataIndex: 'agentIp',
        text: 'IP地址',
        width: 60
    }, {
        dataIndex: 'agentSendMs',
        text: '任务下发耗时',
        width: 100,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        dataIndex: 'sendAgentRpcTime',
        text: '给proxy下发时间',
        width: 170
    }, {
        dataIndex: 'sendAgentFinishRpcTime',
        text: '给proxy下发完成时间',
        width: 170
    }, {
        dataIndex: 'sendAgentRpcResponseTime',
        text: '下发proxy耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        width: 160
    }, {
        dataIndex: 'proxySendAgentStartTime',
        text: 'proxy给agent下发开始时间',
        width: 200
    }, {
        dataIndex: 'proxySendAgentEndTime',
        text: 'proxy给agent下发结束时间',
        width: 200
    }, {
        dataIndex: 'proxySendAgentResponseTime',
        text: 'proxy给agent下发耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        width: 150
    }, {
        dataIndex: 'agentStartTime',
        text: 'agent任务开始时间',
        width: 170
    }, {
        dataIndex: 'agentEndTime',
        text: 'agent任务结束时间',
        width: 170
    }, {
        dataIndex: 'agentExecMs',
        text: 'agent任务执行耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        width: 130,
    }, {
        dataIndex: 'proxyPutReqTime',
        text: 'Proxy处理开始时间',
        width: 170
    }, {
        dataIndex: 'proxyReturnTime',
        text: 'Proxy处理结束时间',
        width: 170
    }, {
        dataIndex: 'proxyExecMs',
        text: 'Proxy处理耗时',
        width: 110,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        dataIndex: 'providerReceiveResultTime',
        text: 'provider收到proxy返回请求时间',
        width: 220
    },{
        dataIndex: 'providerReceiveResultCostTime',
        text: 'proxy回调provider网络耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        width: 220
    }, {
        dataIndex: 'updateInsTime',
        text: '回显完成时间',
        width: 120
    }, {
        dataIndex: 'updateInsExecMs',
        text: '回显耗时',
        width: 80,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        dataIndex: 'updateInsToRedisMs',
        text: '回显推送处理耗时',
        width: 120,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        dataIndex: 'agentInstanceId',
        text: 'agentInstanceId',
        flex: 1,
        hidden: true
    }, {
        dataIndex: 'agentReturnResultResponseTime',
        text: 'agent执行结束返回proxy网络耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        hidden: true,
        width: 220
    }, {
        dataIndex: 'redisStartTime',
        text: 'redis推送时间',
        width: 120
    }, {
        dataIndex: 'redisEndTime',
        text: 'redis接收时间',
        width: 120
    }, {
        dataIndex: 'redisExecMs',
        text: 'redis接收耗时',
        width: 120,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        text: '操作',
        xtype: 'actiontextcolumn',
        width: 220,
        align: 'left',
        //						menuDisabled : true,
        items: [{
            text: '详情',
            iconCls: 'monitor_search',
            handler: function (grid, rowIndex) {
                var state = grid.getStore().data.items[rowIndex].data.agentInsStatus;
                var instanceId = grid.getStore().data.items[rowIndex].data.agentInstanceId;
                var agentIp = grid.getStore().data.items[rowIndex].data.agentIp;

                openActWindowForExecForFlow(instanceId, state, agentIp)
            }
        }, {
            text: '重试',
            iconCls: 'monitor_execute',
            getClass: function (v, metadata, record) {
                var state = record.get("agentInsStatus");
                //如果任务为终态（外层为已终止或者完成），里层异常agent在操作列不展示重试、终止、忽略
                if (hiddenOperateButton) {
                    return 'x-hidden';
                }
                if (state != '30' && state != '40' && state != '50') {
                    return 'x-hidden';
                }
            },
            handler: function (grid, rowIndex) {
                var iid = grid.getStore().data.items[rowIndex].data.agentInstanceId;
                var state = grid.getStore().data.items[rowIndex].data.agentInsStatus;
                reTryScriptServer3ForExecForFlow(iid, state);
            }
        }, {
            text: '终止',
            iconCls: 'monitor_skip',
            getClass: function (v, metadata, record) {
                var state = record.get("agentInsStatus");
                //如果任务为终态（外层为已终止或者完成），里层异常agent在操作列不展示重试、终止、忽略
                if (hiddenOperateButton) {
                    return 'x-hidden';
                }
                if (state != '-1' && state != '1') {
                    return 'x-hidden';
                }
            },
            handler: function (grid, rowIndex) {
                var iid = grid.getStore().data.items[rowIndex].data.agentInstanceId;
                var state = grid.getStore().data.items[rowIndex].data.agentInsStatus;
                scriptServerStop3ForExecForFlow(iid, state);
            }
        }, {
            text: '忽略',
            iconCls: 'monitor_skip',
            getClass: function (v, metadata, record) {
                var state = record.get("agentInsStatus");
                //如果任务为终态（外层为已终止或者完成），里层异常agent在操作列不展示重试、终止、忽略
                if (hiddenOperateButton) {
                    return 'x-hidden';
                }
                if (state != '30' && state != '40' && state != '50' && state != '-1' && state != '1') {
                    return 'x-hidden';
                }
            },
            handler: function (grid, rowIndex) {
                var iid = grid.getStore().data.items[rowIndex].data.agentInstanceId;
                var state = grid.getStore().data.items[rowIndex].data.agentInsStatus;
                skipScriptServer3ForExecForFlow(iid, state);
            }
        }]
    }]

    function scriptServerStop3ForExecForFlow(requestId, state) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
            function (btn) {
                if (btn == 'yes') {
                    if (state == '5' || state == '20' || state == '40' || state == '60') {
                        Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                        store.reload();
                        return;
                    }
                    Ext.MessageBox.wait("数据处理中...", "提示");
                    Ext.Ajax.request({
                        url: 'scriptServiceShellKill.do',
                        method: 'POST',
                        params: {
                            insIds: requestId,
                            flag: flag3ForExecForFlow
                        },
                        success: function (response, request) {
                            var message = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', message);
                            store.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            })
    }

    function reTryScriptServer3ForExecForFlow(requestId, state) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
            function (btn) {
                if (btn == 'yes') {
                    Ext.MessageBox.wait("数据处理中...", "提示");
                    Ext.Ajax.request({
                        url: 'retryScriptServiceShell.do',
                        method: 'POST',
                        params: {
                            insIds: requestId,
                            flag: flag3ForExecForFlow
                        },
                        success: function (response, request) {
                            var message = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', message);
                            store.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            })
    }

    function exportAgentMessageForExcel(iid){
        window.location.href="exportAgentMessageForExcel.do?iid="+iid+"&flowId="+flowId+"&state="+taskState.getValue();
    }

    function skipScriptServer3ForExecForFlow(requestId, state) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
            function (btn) {
                if (btn == 'yes') {
                    Ext.MessageBox.wait("数据处理中...", "提示");
                    Ext.Ajax.request({
                        url: 'skipScriptServiceShell.do',
                        method: 'POST',
                        params: {
                            insIds: requestId,
                            flag: flag3ForExecForFlow
                        },
                        success: function (response, request) {
                            var message = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', message);
                            store.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                }
            })
    }

    Ext.define('execLinkModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [{
            name: 'iid',
            type: 'long'
        }, {
            name: 'agentInsStatus',
            type: 'number'
        }, {
            name: 'sendAgentRpcTime',
            type: 'string'
        }, {
            name: 'agentInstanceId',
            type: 'number'
        }, {
            name: 'agentIp',
            type: 'string'
        }, {
            name: 'agentSendMs',
            type: 'number'
        }, {
            name: 'proxyPutReqTime',
            type: 'string'
        }, {
            name: 'proxyReturnTime',
            type: 'string'
        }, {
            name: 'proxyExecMs',
            type: 'number'
        }, {
            name: 'agentStartTime',
            type: 'string'
        }, {
            name: 'agentEndTime',
            type: 'string'
        }, {
            name: 'agentExecMs',
            type: 'number'
        }, {
            name: 'updateInsTime',
            type: 'string'
        }, {
            name: 'updateInsExecMs',
            type: 'number'
        }, {
            name: 'updateInsToRedisMs',
            type: 'number'
        }, {
            name: 'redisStartTime',
            type: 'string'
        }, {
            name: 'redisEndTime',
            type: 'string'
        }, {
            name: 'redisExecMs',
            type: 'number'
        }, {
            name: 'sendAgentFinishRpcTime',
            type: 'string'
        }, {
            name: 'sendAgentRpcResponseTime',
            type: 'number'
        }, {
            name: 'proxySendAgentStartTime',
            type: 'string'
        }, {
            name: 'proxySendAgentEndTime',
            type: 'string'
        }, {
            name: 'proxySendAgentResponseTime',
            type: 'number'
        }, {
            name: 'agentReturnResultResponseTime',
            type: 'number'
        }, {
            name: 'providerReceiveResultTime',
            type: 'string'
        }, {
            name: 'providerReceiveResultCostTime',
            type: 'number'
        }
        ]
    })

    var store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'execLinkModel',
        proxy: {
            url: 'getAgentLinkMonitorData.do',
            type: 'ajax',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    })

    store.on('beforeload', function () {
        endNum = (store.lastOptions.page) * store.lastOptions.limit
        if (endNum > 10000) {
            Ext.Msg.alert('提示', '不允许翻页查询数据超过10000条，请加过滤查询条件！');
            store.removeAll();
            return false;
        }
        queryData.flowId = flowId
        Ext.apply(store.proxy.extraParams, queryData)
    })

    var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        columns: columns,
        store: store,
        width: '100%',
        height: '100%' - 60,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel', {}),
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        dockedItems: [
            {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: ['->', {
                    xtype: 'button',
                    text: '重试',
                    hidden: hiddenOperateButton,
                    cls: 'Common_Btn',
                    handler: function () {
                        var data = getCHKBoxIds();
                        if (data == undefined || data == '') {
                            Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                            return;
                        } else {
                            reTryScriptServer3ForExecForFlow(data);
                        }
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '终止',
                    hidden: hiddenOperateButton,
                    listeners: {
                        click: function () {
                            var data = getCHKBoxIds();
                            if (data == undefined || data == '') {
                                Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                                return;
                            } else {
                                Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                                    function (button, text) {
                                        if (button == "yes") {
                                            if (data == '-1') {
                                                Ext.Msg.alert('提示', '该步骤已经结束，无需终止!');
                                                store.reload();
                                                return;
                                            }
                                            Ext.MessageBox.wait("数据处理中...", "提示");
                                            Ext.Ajax.request({
                                                url: 'scriptServiceShellKill.do',
                                                params: {
                                                    flag: flag3ForExecForFlow,
                                                    insIds: data
                                                },
                                                method: 'POST',
                                                success: function (response, opts) {
                                                    var success = Ext.decode(response.responseText).success;
                                                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                    // 当后台数据同步成功时
                                                    if (success) {
                                                        store.reload();
                                                    }
                                                }
                                            });
                                        }
                                    });
                            }
                        }
                    }
                }, {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '忽略',
                    hidden: hiddenOperateButton,
                    listeners: {
                        click: function () {
                            var checkFlag = checkSelectState();
                            if (!checkFlag) {
                                Ext.Msg.alert('提示', '只允许忽略异常状态的记录！');
                                return;
                            }
                            var data = getCHKBoxIdsArray(0);
                            if (data.length == 0) {
                                Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                                return;
                            } else {
                                Ext.Msg.confirm("请确认", "是否真的要进行<忽略>操作？",
                                    function (button, text) {
                                        if (button == "yes") {
                                            Ext.MessageBox.wait("数据处理中...", "提示");
                                            Ext.Ajax.request({
                                                url: 'skipScriptServiceShell.do',
                                                params: {
                                                    flag: flag3ForExecForFlow,
                                                    insIds: data
                                                },
                                                method: 'POST',
                                                success: function (response, opts) {
                                                    var success = Ext.decode(response.responseText).success;
                                                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                                    // 当后台数据同步成功时
                                                    if (success) {
                                                        store.reload();
                                                    }
                                                }
                                            });
                                        }
                                    });
                            }
                        }
                    }
                },{
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '导出',
                    listeners: {
                        click: function () {
                            var iid = getInstanceIdsArray();
                            // var state = grid.getStore().data.items[rowIndex].data.agentInsStatus;
                            exportAgentMessageForExcel(iid);
                        }
                    }
                }]
            }
        ],
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        ipageItems: [
            oam_act_form,
            {
                xtype: 'button',
                cls: 'Common_Btn',
                //											id : 'button2',
                text: '刷  新',
                listeners: {
                    click: function () {
                        if (refreshObjForExecForFlow) {
                            clearInterval(refreshObjForExecForFlow);
                        }
                        refreshPage3ForExecForFlow();
                        var interValue = pageFreshTime.getValue();
                        interPV3ForExecForFlow = interValue;
                        if (interPV3ForExecForFlow < 20) {
                            interPV3ForExecForFlow = 20;
                        }
                        refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);
                    }
                }
            }
        ],
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    })

    function refreshPage3ForExecForFlow() {
        if (getDivId(contentPanel.getLoader().target.html) == 'agentLinkMonitor') {
            store.reload();
        }
    }

    function getCHKBoxIdsArray(flag) {
        var records = grid.getView().getSelectionModel().getSelection();
        var idsArray = new Array();
        Ext.Array.each(records,
            function (rec) {
                var state = rec.get('agentInsStatus'); // -1 60 20
                if (flag == 0 && state == '30') {
                    idsArray.push(rec.get('agentInstanceId'));
                } else if (flag == 1) {
                    idsArray.push(rec.get('agentInstanceId'));
                }
            });
        return idsArray;
    }

    function getInstanceIdsArray() {
        var records = grid.getView().getSelectionModel().getSelection();
        var idsArray = new Array();
        Ext.Array.each(records,
            function (rec) {
                idsArray.push(rec.get('agentInstanceId'));
            });
        return idsArray;
    }

    function checkSelectState() {
        var records = grid.getView().getSelectionModel().getSelection();
        var returnFlag = true;
        Ext.Array.each(records,
            function (rec) {
                var state = rec.get('agentInsStatus'); // -1 60 20
                if (state != '30') {
                    returnFlag = false;
                    return false;
                }
            });
        return returnFlag;
    }

    function openActWindowForExecForFlow(requestId, state, ip) {
        var runningWindow = null;
        //	var fp2 = null;
        //	var cmdStr = null;
        var surl = "getScriptExecOutput.do";
        var h = window.innerHeight || document.documentElement.clientHeight
            || document.body.clientHeight;
        function getData(surl,state, requestId, ip, cmdValue) {
            Ext.Ajax.request({
                url : surl,
                params : {
                    requestId : requestId,
                    ip : ip,
                    flag:flag3ForExecForFlow,
                    input:cmdValue
                },
                success : function(response, opts) {
                    var msg = Ext.decode(response.responseText);
                    //var oldValue = Ext.getCmp('baseinfo_form_forflowtest').getForm().findField("actOutPut").getValue();
                    var output = msg.message;
                    var json = {
                        actName :serviceName+"-"+ip,
                        actInsName : serviceName,
                        actOutPut : output
                    };
                    // Ext.getCmp('baseinfo_form_forflowtest').getForm().setValues(json);
                    // actName.getEl().dom.innerHTML='活动名：    <div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:90%;height:35px ;margin-bottom:10px"><pre>'+actName.getValue()+'</pre></div>';
                    //actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:90%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+actOutPut.getValue()+'</pre></div>';
                    actName.getEl().dom.innerHTML='活动名：    <div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:90%;height:35px ;margin-bottom:10px"><pre>'+serviceName+"-"+ip+'</pre></div>';
                    actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:90%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+output+'</pre></div>';
                },
                failure : function(response, opts) {

                }

            });
        }
        var cmdStr = new Ext.form.TextField({
            anchor : '100%',
            labelWidth : 70,
            fieldLabel : 'CMD',
            disabled:(state==10)?false:true,
            listeners : {
                specialkey : function(textfield, e) {
                    if (e.getKey() == Ext.EventObject.ENTER) {
                        var cmdV = cmdStr.getValue();
                        cmdStr.setValue("");
                        getData(surl,state, requestId, agentIp, agentPort, cmdV);
                    }
                }
            }
        });
        var actName= Ext.create ('Ext.form.field.Text',
            {
                fieldLabel : '活动名',
                labelWidth : 70,
                name : 'actName',
                margin :'10 0 5 0',
                anchor : '95%' // anchor width by percentage
            });
        var actOutPut = Ext.create ('Ext.form.field.TextArea',
            {
                fieldLabel : '活动日志',
                labelWidth : 70,
                grow : true,
                height : h-200,
                name : 'actOutPut',
                margin :'15 0 5 0',
                anchor : '95%'
            });
        var fp2 = new Ext.form.Panel({
            border : false,
            id : 'baseinfo_form_forflowtest',
            height : '100%',
            padding : 5,
            fieldDefaults : {
                labelWidth : 60,
                labelAlign : 'right'
            },
            defaultType : 'textfield',
            items : [  actName, {
                fieldLabel : '实例名',
                labelWidth : 70,
                name : 'actInsName',
                hidden : true,
                anchor : '100%' // anchor width by percentage
            },actOutPut,cmdStr ]
        });

        if (runningWindow == undefined || !runningWindow.isVisible()) {
            runningWindow = Ext.create('Ext.window.Window', {
                title : serviceName,
                modal : true,
                closeAction : 'destroy',
                constrain : true,
                autoScroll : true,
                maximizable : true,
                width : 1200,
                height : h - 95,
                items : [ fp2 ],
                dockedItems : [ {
                    xtype : 'toolbar',
                    dock : 'bottom',
                    layout: {pack: 'center'},
                    items : [
                        {
                            text : '刷新',
                            textAlign : 'center',
                            cls : 'Common_Btn',
                            handler : function() {
                                getData(surl,state, requestId, ip, null);
                                cmdStr.setValue("");
                            }
                        }, {
                            text : '终止',
                            textAlign : 'center',
                            hidden : hiddenOperateButton,
                            cls : 'Common_Btn',
                            handler : function() {
                                scriptServerStop3ForExecForFlow(requestId,state)
                            }
                        } ]
                } ],
                layout : 'fit'
            });
        }
        runningWindow.show();
        getData(surl,state, requestId, ip, null);



    }

    function getCHKBoxIds() {
        var ids = "";
        var records = grid.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
            function (rec) {
                cnum = 1;
                var state = rec.get('agentInsStatus'); // -1 60 20
                if (state != '60' && state != '20' && state != '5') {
                    if (ids == '') {
                        ids = rec.get('agentInstanceId');
                    } else {
                        ids = ids + "," + rec.get('agentInstanceId');
                    }
                }
            });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }

    var stateStore= Ext.create('Ext.data.Store', {
        fields : [ 'id', 'name' ],
        data : [ {
            "id" : "-1",
            "name" : "全部"
        }, {
            "id" : "20",
            "name" : "完成"
        },{
            "id" : "5",
            "name" : "忽略"
        },
        {
            "id" : "10",
            "name" : "运行"
        },
        {
            "id" : "30",
            "name" : "异常"
        }, {
            "id" : "60",
            "name" : "终止"
        } ]
    });

    var taskState = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel : '任务状态',
        labelAlign : 'right',
        width : '16%',
        labelWidth : 70,
        name : 'taskState',
        displayField : 'name',
        valueField : 'id',
        store : stateStore,
        queryMode : 'local',
        // editable:false,
        xtype : 'combobox',
        listeners: {
            change: function () { // old is keyup
                queryData = {
                    queryIP: queryIP.value,
                    instanceState: taskState.getValue(),
                    flowId: flowId
                }
                grid.ipage.moveFirst();
            }
        }
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        bodyPadding: grid_margin,
        border: true,
        layout: 'border',
        renderTo: 'agentLinkMonitor',
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        items: [grid],
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            items: [taskState,queryIP, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    getData()
                }
            }, {
                xtype: 'button',
                text: '重置',
                cls: 'Common_Btn',
                handler: function () {
                    taskState.setValue('');
                    queryIP.setValue('');
                    getData()
                }
            }, {
                xtype: 'button',
                text: '返回',
                cls: 'Common_Btn',
                handler: function () {
                    contentPanel.setTitle('任务链路监控');
                    contentPanel.getLoader().load({
                        url: 'execLinkMonitor.do',
                        sync: true,
                        params: {
                            form_serviceName: lastPage_form_serviceName,
                            form_flowId: lastPage_form_flowId,
                            form_queryIP: lastPage_form_queryIP,
                            form_startTime: lastPage_form_startTime,
                            form_endTime: lastPage_form_endTime
                        },
                        scripts: true
                    });
                }
            },{
                xtype: 'button',
                text: '时序图',
                cls: 'Common_Btn',
                handler: function () {
                    timingDiagramShow()
                }
            },]
        }]
    })

    function getData() {
        if (!queryIP.isValid()) {
            Ext.Msg.alert('提示', '查询条件不符合要求，请调整！');
            return;
        }
        queryData = {
            queryIP: queryIP.value,
            instanceState: taskState.getValue(),
            flowId: flowId
        }
        grid.ipage.moveFirst();
    }

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });

    //进入页面激活定时刷新
    if (refreshObjForExecForFlow) {
        clearInterval(refreshObjForExecForFlow);
    }
    refreshPage3ForExecForFlow();
    var interValueSec = pageFreshTime.getValue();
    interPV3ForExecForFlow = interValueSec;
    if (interPV3ForExecForFlow < 20) {
        interPV3ForExecForFlow = 20;
    }
    refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);


})