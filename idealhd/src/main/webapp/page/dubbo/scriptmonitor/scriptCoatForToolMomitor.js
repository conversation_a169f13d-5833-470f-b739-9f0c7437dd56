Ext.onReady(function() {
	var search_form2ForToolMonitor;
	var scriptmonitor_store2ForToolMonitor;
    destroyRubbish();
    var flag = 0; // 测试
    var stateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [ {
		            "id": "-1",
		            "name": "全部"
		        },
		        {
		            "id": "101",
		            "name": "完成"
		        },
		        {
		            "id": "102",
		            "name": "运行"
		        },
		        {
					"id" : "30",
					"name" : "异常"
				},
		        {
		            "id": "60",
		            "name": "已终止"
		        } ]
    });
    
//    var sName = new Ext.form.TextField({
//		name : 'scriptName',
//		fieldLabel: '服务名称',
//		emptyText : '--请输入服务名称--',
//		labelWidth : 70,
//		width : '20%',
//		value: filter_scriptNameForToolMonitor,
//        labelAlign : 'right'
//	});
    var sNameStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [ {
		            "id": "1",
		            "name": "文件下发"
		        },
		        {
		            "id": "2",
		            "name": "文件提取"
		        } ]
    });
    var sName = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptName',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '服务名称',
		displayField : 'name',
		valueField : 'name',
		editable : false,
		emptyText : '--请选择服务名称--',
		store : sNameStore,
		width : '20%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var stateCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'state',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '执行状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择执行状态--',
		store : stateStore,
		width : '15%',
        labelAlign : 'right',
        listeners: {
        	 specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            },
            afterRender: function(combo) {
               if(filter_stateForToolMonitor=='-1') {
					combo.setValue(stateStore.getAt(0).data.id);
				} else if(filter_stateForToolMonitor=='101'){
					combo.setValue(stateStore.getAt(1).data.id);
				}  else if(filter_stateForToolMonitor=='102'){
					combo.setValue(stateStore.getAt(2).data.id);
				} else if(filter_stateForToolMonitor=='30'){
					combo.setValue(stateStore.getAt(3).data.id);
				} else if(filter_stateForToolMonitor=='60'){
					combo.setValue(stateStore.getAt(4).data.id);
				}
            }
        }
	});
    
    var startTime = new Ext.form.field.Date({
    	name: 'startTime',
		fieldLabel: '开始时间',
		labelWidth : 70,
		width : '22%',
        labelAlign : 'right',
        format: 'Y-m-d',
        value: filter_startTimeForToolMonitor,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var endTime = new Ext.form.field.Date({
    	name: 'endTime',
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '22%',
    	labelAlign : 'right',
    	format: 'Y-m-d',
    	value: filter_endTimeForToolMonitor,
    	listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });

    search_form2ForToolMonitor = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls:'customize_gray_back',
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items: [sName,
			        stateCb,
			        startTime,
			        endTime,
            {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls : 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                xtype: 'button',
                text: '返回',
                hidden: true,
                cls: 'Common_Btn',
                handler: function() {
                	forwardtestmain2ForToolMonitor();
                }
            }
            ]
		}]
        
    });

    Ext.define('scriptmonitorData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'cata',
            type: 'int'
        },
        {
        	name: 'flowId',
        	type: 'int'
        },
        {
        	name: 'actNo',
        	type: 'int'
        },
        {
            name: 'startUser',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'actType',
            type: 'string'
        },
        {
            name: 'serverNum',
            type: 'int'
        },
        {
            name: 'scriptTestID',
            type: 'string'
        },
        {
            name: 'scriptTestStatus',
            type: 'int'
        }]
    });

    scriptmonitor_store2ForToolMonitor = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 30,
        model: 'scriptmonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptCoatList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) {
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            }
            return backValue;
        }
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: 'actType',
        dataIndex: 'actType',
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'scriptName',
        flex: 1
    },
    {
        text: '启动人',
        dataIndex: 'startUser',
        width: 150
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 150
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 150
    },
    {
        text: '脚本主键',
        dataIndex: 'scriptTestID',
        width: 50,
        hidden: true
    },
    {
        text: '脚本状态',
        dataIndex: 'scriptTestStatus',
        width: 50,
        hidden: true
    },
    {
//        text: '操作',
//        dataIndex: 'sysOperation',
//        width: 210,
//        renderer: function(value, p, record) {
//            var coatid = record.get('iid');
//            var cata = 0; 
//            var actType = record.get('actType');
//            var actNo = record.get('actNo');
//            var flowId = record.get('flowId');
//            var state = record.get('state');
//            var serviceId = record.get('scriptTestID');
//            var scriptState = record.get('scriptTestStatus');
//            	 return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardruninfo2ForToolMonitor(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' +
//                 '<a href="javascript:void(0)" onclick="scriptCoatLookForToolMonitor('+coatid+')"><img src="images/monitor_bg.png" align="absmiddle" class="script_text"></img>脚本</a>&nbsp;&nbsp;'+
//                 '<a href="javascript:void(0)" onclick="resultExport2ForToolMonitor(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>' + '</span>';
//        }
    	text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'sysOperation',
		width : 210,
		items : [
     			{
     				text : '详情',
     				iconCls : 'monitor_search',
     				handler : function(grid, rowIndex) {
     					var coatid = grid.getStore().data.items[rowIndex].data.iid; 
     					var cata = 0; 
     					var actType = grid.getStore().data.items[rowIndex].data.actType; 
     					var actNo = grid.getStore().data.items[rowIndex].data.actNo; 
     					var flowId = grid.getStore().data.items[rowIndex].data.flowId; 
     					var state = grid.getStore().data.items[rowIndex].data.state; 
     					var serviceId = grid.getStore().data.items[rowIndex].data.scriptTestID; 
     					var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus; 
     					forwardruninfo2ForToolMonitor(coatid,cata);
     				}
     			},{
     				text : '脚本',
     				iconCls : 'script_text',
     				handler : function(grid, rowIndex) {
     					var coatid = grid.getStore().data.items[rowIndex].data.iid; 
     					var cata = 0; 
     					var actType = grid.getStore().data.items[rowIndex].data.actType; 
     					var actNo = grid.getStore().data.items[rowIndex].data.actNo; 
     					var flowId = grid.getStore().data.items[rowIndex].data.flowId; 
     					var state = grid.getStore().data.items[rowIndex].data.state; 
     					var serviceId = grid.getStore().data.items[rowIndex].data.scriptTestID; 
     					var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus; 
     					scriptCoatLookForToolMonitor(coatid);
     				}
     			},{
     				text : '导出',
     				iconCls : 'monitor_export',
     				handler : function(grid, rowIndex) {
     					var coatid = grid.getStore().data.items[rowIndex].data.iid; 
     					var cata = 0; 
     					var actType = grid.getStore().data.items[rowIndex].data.actType; 
     					var actNo = grid.getStore().data.items[rowIndex].data.actNo; 
     					var flowId = grid.getStore().data.items[rowIndex].data.flowId; 
     					var state = grid.getStore().data.items[rowIndex].data.state; 
     					var serviceId = grid.getStore().data.items[rowIndex].data.scriptTestID; 
     					var scriptState = grid.getStore().data.items[rowIndex].data.scriptTestStatus; 
     					resultExport2ForToolMonitor(coatid,cata);
     				}
     			}]
    }];

    scriptmonitor_store2ForToolMonitor.on('beforeload', function(store, options) {
        var new_params = {
        	scriptName:search_form2ForToolMonitor.getForm().findField("scriptName").getValue(),
            flowId:flowId2ForToolMonitor,
            state: stateCb.getValue(),
            cata: 0,
            ifrom: 1,
            startTime: startTime.getValue(),
            endTime: endTime.getValue()
        };
console.log('asdf', new_params);
        Ext.apply(scriptmonitor_store2ForToolMonitor.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitor_store2ForToolMonitor,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });

    var scriptmonitor_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	autoScroll: true,
        store: scriptmonitor_store2ForToolMonitor,
        cls:'customize_panel_back',
        border: false,
        columnLines: true,
        padding : grid_space,
        columns: scriptmonitor_columns,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar'
//        bbar: pageBar
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptCoatForToolMomitor_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_form2ForToolMonitor, scriptmonitor_grid]
    });

    function clearQueryWhere() {
    	search_form2ForToolMonitor.getForm().findField("scriptName").setValue(''),
    	stateCb.setValue("-1"),
        startTime.setValue(''),
        endTime.setValue('');
    }

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    if(callback_functionForToolMonitor)
	{
    	console.log(callback_functionForToolMonitor);
    	eval(callback_functionForToolMonitor);
	}
    function forwardtestmain2ForToolMonitor() {
 	   contentPanel.getLoader().load({
 	        url: "scriptMonitorForFlowTest.do",
 	        scripts: true,
 	         params: {
     			filter_serviceName:filter_serviceNameForToolMonitor,
 			    filter_serviceState:filter_serviceStateForToolMonitor,
 				filter_serviceStartTime:filter_serviceStartTimeForToolMonitor,
 				filter_serviceEndTime:filter_serviceEndTimeForToolMonitor
             }
 	    });
 }
    
    function scriptCoatStop2ForToolMonitor(coatid, flag) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
        function(btn) {
            if (btn == 'yes') {
                Ext.Ajax.request({
                    url: 'scriptCoatStop.do',
                    method: 'POST',
                    params: {
                        coatid: coatid,
                        flag: flag
                    },
                    success: function(response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                            Ext.Msg.alert('提示', message);
                        } else {
                            Ext.Msg.alert('提示', message);
                        }
                        scriptmonitor_store2ForToolMonitor.reload();
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }
        })

    }
    function forwardruninfo2ForToolMonitor(coatid, flag) {
    	var scriptName=search_form2ForToolMonitor.getForm().findField("scriptName").getValue();
    	var state =search_form2ForToolMonitor.getForm().findField("state").getValue();
    	var startTime = search_form2ForToolMonitor.getForm().findField("startTime").getRawValue();
    	var endTime =search_form2ForToolMonitor.getForm().findField("endTime").getRawValue();
    	contentPanel.getLoader().load({
    		url: "forwardscriptserverForToolMonitor.do",
    		scripts: true,
    		params: {
    			flowId:flowId2ForToolMonitor,
    			coatid: coatid,
    			flag: flag,
    			filter_scriptName:scriptName,
    			filter_state:state,
    			filter_startTime:startTime,
    			filter_endTime:endTime,
    			filter_serviceName:filter_serviceNameForToolMonitor,
    			filter_serviceState:filter_serviceStateForToolMonitor,
    			filter_serviceStartTime:filter_serviceStartTimeForToolMonitor,
    			filter_serviceEndTime:filter_serviceEndTimeForToolMonitor
    		}
    	});
    }
    
    function resultExport2ForToolMonitor(coatid, flag) {
    	window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag=' + flag + '&workitemid=0';
    }
    function scriptCoatLookForToolMonitor(coatid) {
    	Ext.Ajax.request({
    		url: 'queryOneServiceForViewTest.do',
    		method: 'POST',
    		params: {
    			iid: coatid
    		},
    		success: function(response, request) {
    			var serviceId = Ext.decode(response.responseText).serviceId;
    			
    			var DetailWinTi = Ext.create('widget.window', {
    				title: '详细信息',
    				closable: true,
    				closeAction: 'destroy',
    				width: contentPanel.getWidth(),
    				minWidth: 350,
    				height: contentPanel.getHeight(),
    				draggable: false,
    				// 禁止拖动
    				resizable: false,
    				// 禁止缩放
    				modal: true,
    				loader: {
    					url: 'queryOneServiceForView.do',
    					params: {
    						iid: serviceId,
    						flag: 0,
    						hideReturnBtn: 1
    					},
    					autoLoad: true,
    					scripts: true
    				}
    			});
    			DetailWinTi.show();
    		},
    		failure: function(result, request) {
    			secureFilterRs(result, "操作失败！");
    		}
    	});
    }
});



