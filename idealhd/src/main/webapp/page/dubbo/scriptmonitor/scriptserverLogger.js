Ext.onReady(function() {
	destroyRubbish();
	
	$('#back-to').off('click').on('click', function(e){
		clearInterval(refreshObj);
		contentPanel.getLoader().load({
			url : "forwardscriptserver.do",
			scripts : true,
			params : {
				flowId:flowId,
				coatid:coatId,
				flag:flag
			}
		});
	});
	
	var de = Ext.create('Ext.form.FormPanel', {
//	    width      : 400,
		border: false,
	    height: contentPanel.getHeight()-90,
	    margin: '0 0 10 0',
	    bodyPadding: 10,
	    renderTo   : 'show-logger',
	    items: [{
	        xtype     : 'textareafield',
	        grow      : true,
	        name      : 'message',
	        fieldLabel: '日志详情',
	        anchor    : '100%',
	        height: contentPanel.getHeight()-105,
	    }]
	});
	
	function loadShelloutputhisInfo() {
		var surl = "getScriptExecOutput.do";
		de.mask("数据重新加载中，请稍等");    
		Ext.Ajax.request({
			url : surl,
			params : {
				requestId : instanceId,
				agentIp : agentIp,
				agentPort : agentPort,
				flag : flag
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				de.getForm().findField("message").setValue(msg.message);
//				$('#show-logger').html(msg.message);
				de.unmask();
			},
			failure : function(response, opts) {
//				$('#show-logger').html('获取执行信息失败');
				de.getForm().findField("message").setValue('获取执行信息失败');
				de.unmask();
			}

		});
	}
	
	loadShelloutputhisInfo();
	
	refreshObj = setInterval(loadShelloutputhisInfo, $('#rowFreshId').val()*1000);
	
	$('#refresh-this').off('click').on('click', function(e){
		clearInterval(refreshObj);
		loadShelloutputhisInfo();
		refreshObj = setInterval(loadShelloutputhisInfo, $('#rowFreshId').val()*1000);
	});
	
});