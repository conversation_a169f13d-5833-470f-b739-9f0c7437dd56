var queryData = {}
Ext.onReady(function () {

    var queryServiceName = Ext.create('Ext.form.TextField', {
        fieldLabel: '任务名称',
        labelWidth: 60,
        value: form_serviceName,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    getData()
                }
            }
        }
    })

    var queryID = Ext.create('Ext.form.TextField', {
        fieldLabel: '任务ID',
        labelWidth: 60,
        value: form_flowId,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    getData()
                }
            }
        }
    })

    var queryIP = Ext.create('Ext.form.TextField', {
        fieldLabel: 'IP地址',
        labelWidth: 60,
        value: form_queryIP,
        regex: /(^\d{1,3}\.(\d{1,3}\.){0,2}(\d{1,3})?$)|(^([0-9a-fA-F]{1,4}:){1,7}([0-9a-fA-F]{1,4})?$)|(^[0-9a-zA-Z]+(\.com)?$)/,
        regexText: '不符合IPv4、IPv6、域名校验',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    getData()
                }
            }
        }
    })

    var date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
    var startTime = Ext.create('Go.form.field.DateTime',
        {
            fieldLabel : '任务开始时间',
            xtype : 'datefield',
            labelAlign : 'right',
            width : 290,
            labelWidth : 105,
            name : 'startTime',
            id: 'startTime',
            format : 'Y-m-d H:i:s',
            value: form_startTime === '' ? Ext.util.Format.date(Ext.Date.add(date1, Ext.Date.DAY, -6), "Y-m-d H:i:s") : form_startTime,
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        getData()
                    }
                }
            }
        });

    var date2 = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1);

    var endTime = Ext.create('Go.form.field.DateTime',
        {
            fieldLabel: '任务结束时间',
            xtype : 'datefield',
            labelAlign : 'right',
            width : 290,
            labelWidth : 105,
            name : 'endTime',
            id: 'endTime',
            format : 'Y-m-d H:i:s',
            // value: form_endTime === '' ? Ext.util.Format.date(Ext.Date.add(date2, Ext.Date.DAY), "Y-m-d H:i:s") : form_endTime,
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        getData()
                    }
                },
                change: function () {
                    if(endTime.getRawValue() < startTime.getRawValue()){
                        Ext.Msg.alert("提示","任务结束时间必须大于任务开始时间!");
                        endTime.setValue("");
                    }
                }
            }
        });


    var columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    }, {
        dataIndex: 'iid',
        text: 'IID',
        hidden: true
    }, {
        dataIndex: 'taskStatus',
        text: '执行状态',
        width: 80,
        renderer: function (value, p, record) {
            var backValue = "";
            if (value == 10 || value == 11) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) { // 5 is 忽略
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">初始化</span>';
            }
            return backValue;
        }
    }, {
        dataIndex: 'scriptServiceName',
        text: '服务名称',
        width: 80,
        hidden: true
    }, {
        dataIndex: 'taskName',
        text: '任务名称',
        width: 80
    }, {
        dataIndex: 'flowId',
        text: '任务ID',
        width: 80
    }, {
        dataIndex: 'scriptVersion',
        text: '脚本版本号',
        width: 100
    }, {
        dataIndex: 'apiStartTime',
        text: '接口请求开始时间',
        width: 150
    }, {
        dataIndex: 'apiEndTime',
        text: '接口请求结束时间',
        width: 150
    }, {
        dataIndex: 'apiExecMs',
        text: '接口请求总耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        width: 110
    }, {
        dataIndex: 'taskStartTime',
        text: '任务开始时间',
        width: 120
    }, {
        dataIndex: 'taskEndTime',
        text: '任务结束时间',
        width: 120
    }, {
        dataIndex: 'restApiStartTime',
        text: '启动接口开始时间',
        width: 150
    }, {
        dataIndex: 'restApiEndTime',
        text: '启动接口结束时间',
        width: 150
    }, {
        dataIndex: 'restApiCostTime',
        text: '启动接口耗时',
        width: 120,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        dataIndex: 'taskExecMs',
        text: '任务总耗时',
        width: 100,
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        }
    }, {
        dataIndex: 'taskRedisExecStartTime',
        text: 'redis开始推送时间',
        width: 140
    },{
        dataIndex: 'taskRedisExecEndTime',
        text: 'redis推送结束时间',
        width: 140
    }, {
        dataIndex: 'taskRedisExecTotalTime',
        text: 'redis接收总耗时',
        renderer: function (value) {
            if (undefined === value) {
                return ''
            }
            return value + 'ms'
        },
        width: 140
    }, {
        dataIndex: 'agentNum',
        text: 'agent数量',
        width: 80
    }, {
        text: '操作',
        xtype: 'actiontextcolumn',
        width: 300,
        align: 'left',
        //						menuDisabled : true,
        items: [{
            text: '详情',
            iconCls: 'monitor_search',
            handler: function (grid, rowIndex) {
                var serviceName = grid.getStore().data.items[rowIndex].data.scriptServiceName;
                var taskName = grid.getStore().data.items[rowIndex].data.taskName;
                var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                var title = '任务链路监控————【服务名称】:' + serviceName + ',【任务名称】:' + taskName;
                contentPanel.setTitle(title);
                contentPanel.getLoader().load({
                    url: 'agentLinkMonitor.do',
                    sync: true,
                    params: {
                        flowId: flowId,
                        form_serviceName: queryData.serviceName,
                        form_flowId: queryData.flowId,
                        form_queryIP: queryData.queryIP,
                        form_startTime: startTime.getRawValue(),
                        form_endTime: endTime.getRawValue(),
                        serviceName: serviceName
                    },
                    scripts: true
                });
            }
        }, {
            text: '参数',
            iconCls: 'script_test',
            handler: function (grid, rowIndex) {
                var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                Ext.Ajax.request({
                    url: 'getWorkItemIdByFlowId.do',
                    method: 'POST',
                    params: {
                        flowId: flowId
                    },
                    success: function (response) {
                        var success = Ext.decode(response.responseText)
                        paramWorkitemId = success.workItemId;
                        baseDecode=true;
                        getParamsToShow()
                    }
                })
            }
        },{
            text: '启动参数',
            iconCls: 'monitor_version',
            handler: function (grid, rowIndex) {
                var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                Ext.Ajax.request({
                    url: 'getStartParamsByFlowId.do',
                    method: 'POST',
                    params: {
                        flowId: flowId
                    },
                    success: function (response) {
                        var success = Ext.decode(response.responseText)
                        if(success){
                            var startParams = Ext.decode(response.responseText).params;
                            getStartParamsToShow(startParams);
                        }

                    }
                })
            }
        },{
            text: '导出',
            iconCls: 'script_download',
            handler: function (grid, rowIndex) {
                var flowId = grid.getStore().data.items[rowIndex].data.flowId;
                window.location.href="exportAgentMessageForExcel.do?iid=&flowId="+flowId+"&state=";
            }
        }]
    }]

    Ext.define('execLinkModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [{
            name: 'iid',
            type: 'long'
        }, {
            name: 'taskStatus',
            type: 'string'
        }, {
            name: 'scriptServiceName',
            type: 'string'
        }, {
            name: 'taskName',
            type: 'string'
        }, {
            name: 'flowId',
            type: 'string'
        }, {
            name: 'scriptVersion',
            type: 'string'
        }, {
            name: 'apiStartTime',
            type: 'string'
        }, {
            name: 'apiEndTime',
            type: 'string'
        }, {
            name: 'apiExecMs',
            type: 'number'
        }, {
            name: 'taskStartTime',
            type: 'string'
        }, {
            name: 'taskEndTime',
            type: 'string'
        }, {
            name: 'taskExecMs',
            type: 'number'
        }, {
            name: 'taskRedisExecTotalTime',
            type: 'number'
        }, {
            name: 'agentNum',
            type: 'number'
        }, {
            name: 'restApiStartTime',
            type: 'string'
        }, {
            name: 'restApiEndTime',
            type: 'string'
        }, {
            name: 'restApiCostTime',
            type: 'number'
        }, {
            name: 'taskRedisExecStartTime',
            type: 'string'
        }, {
            name: 'taskRedisExecEndTime',
            type: 'string'
        }]
    })

    var store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'execLinkModel',
        proxy: {
            url: 'getExecLinkMonitorData.do',
            type: 'ajax',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    })

    store.on('beforeload', function () {
        endNum = (store.lastOptions.page) * store.lastOptions.limit
        if (endNum > 10000) {
            Ext.Msg.alert('提示', '不允许翻页查询数据超过10000条，请加过滤查询条件！');
            store.removeAll();
            return false;
        }
        //第一次进入页面，没有开始时间、结束时间，后台会把es所有数据都查出来，在这里设置一周的时间
        if(queryData.startTime == null || queryData.startTime == ''){
            startTime.setValue(Ext.util.Format.date(Ext.Date.add(date1, Ext.Date.DAY, -6), "Y-m-d H:i:s"));
            queryData.startTime = startTime.value;
        }
        if(queryData.endTime == null || queryData.endTime == ''){
            // endTime.setValue(Ext.util.Format.date(Ext.Date.add(date2, Ext.Date.DAY), "Y-m-d H:i:s"));
            queryData.endTime = endTime.value;
        }

        Ext.apply(store.proxy.extraParams, queryData)
    })

    var grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        columns: columns,
        store: store,
        width: '100%',
        height: '100%' - 60,
        layout: 'fit',
        region: 'center',
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    })

    var mainPanel = Ext.create('Ext.panel.Panel', {
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        bodyPadding: grid_margin,
        border: true,
        layout: 'border',
        renderTo: 'execLinkMonitor',
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        items: [grid],
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            items: [queryServiceName, queryID, queryIP, startTime, endTime, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    getData()
                }
            }, {
                xtype: 'button',
                text: '重置',
                cls: 'Common_Btn',
                handler: function () {
                    queryServiceName.setValue('')
                    queryID.setValue('')
                    queryIP.setValue('')
                    startTime.setValue(Ext.util.Format.date(Ext.Date.add(date1, Ext.Date.DAY, -6), "Y-m-d H:i:s"))
                    endTime.setValue('')
                    getData()
                }
            }]
        }]
    })

    function getData() {
        if (!queryIP.isValid() || !startTime.isValid()) {
            Ext.Msg.alert('提示', '查询条件不符合要求，请调整！');
            return;
        }

        if (startTime.value == null) {
            Ext.Msg.alert('提示', '任务开始时间查询条件为必填项');
            return;
        }
        queryData = {
            serviceName: queryServiceName.value,
            flowId: queryID.value,
            queryIP: queryIP.value,
            startTime: startTime.value,
            endTime: endTime.value
        }
        grid.ipage.moveFirst();
    }

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });
})