<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%> 
<% 
	boolean showActNameSwitch = Environment.getInstance().getScriptExecHistoryActName();
%>
<html>
<head>
<script type="text/javascript">
   var  showActNameSwitch =<%=showActNameSwitch%>;
	var flowId2ForExecForFlow = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var filter_scriptNameForExecForFlow = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForExecForFlow  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeForExecForFlow ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForExecForFlow = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForExecForFlow = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForExecForFlow  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeForExecForFlow ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForExecForFlow = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var filter_serviceTaskName = '<%=request.getParameter("filter_serviceTaskName")==null?"":request.getParameter("filter_serviceTaskName")%>';
	var filter_audiUser ='<%=request.getParameter("filter_audiUser")==null?"":request.getParameter("filter_audiUser")%>';
	var planId='<%=request.getParameter("planId")==null?"":request.getParameter("planId")%>';
	var scenceId='<%=request.getParameter("scenceId")==null?"":request.getParameter("scenceId")%>';
	var stepId='<%=request.getParameter("stepId")==null?"":request.getParameter("stepId")%>';
	var taskNameSwitch = <%=request.getAttribute("taskNameSwitch")%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/dbaasscriptFlowCoatForExec.js"></script>
</head>
<body>
<div id="dbaasscriptflowcoatmonitorForExec_area" style="width: 100%;height: 100%">
</div>
</body>
</html>