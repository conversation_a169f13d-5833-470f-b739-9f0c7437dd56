<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<%
    List listOper = (List) request.getAttribute("opers");
	List listShow = (List) request.getAttribute("shows");
%>
<html>
<head>
<script type="text/javascript">
	var coatId2ForExecForFlow = '<%=request.getAttribute("actId")%>';
	var actName2ForExecForFlow = '<%=request.getAttribute("actName")%>';
	var state2ForExecForFlow = '<%=request.getAttribute("state")%>';
	var flag2ForExecForFlow = '<%=request.getAttribute("flag")%>';
	var cnt2ForExecForFlow=0;
   var shows2ForExecForFlow =  [ {	fieldLabel : '人工提醒', labelWidth : 80, 	name : 'wt_C',	cls:'sc_tlbar_height',xtype : "displayfield",width:250,value : actName2ForExecForFlow , padding:'5 5 5 3'}];
//var opers=[];
   <%if (null != listShow && listShow.size() > 0) {
				for (int i = 0; i < listShow.size(); i++) {
					Map map = (Map) listShow.get(i);%>
	   	var showInfo2ForExecForFlow = {};
	   	showInfo2ForExecForFlow.fieldLabel = '描述信息';
	   	showInfo2ForExecForFlow.value = '<%=(String) map.get("value")%>';
	   	showInfo2ForExecForFlow.labelWidth = 80;
	   	showInfo2ForExecForFlow.width = 544;
	   	showInfo2ForExecForFlow.name='<%=(String) map.get("name")%>';
	   	showInfo2ForExecForFlow.xtype="textarea";
	    //showInfo.disabled=true;
	   	showInfo2ForExecForFlow.height=180;
	   	shows2ForExecForFlow.push(showInfo2ForExecForFlow);
   			<%}
			}%>
   
	var operInfo2ForExecForFlow =[];
   <%if (null != listOper && listOper.size() > 0) {
				for (int i = 0; i < listOper.size(); i++) {
					Map map = (Map) listOper.get(i);%>
					cnt=<%=listOper.size()%>;
		var aaa2ForExecForFlow = {};			
		aaa2ForExecForFlow.boxLabel = '<%=(String) map.get("name")%>';
		aaa2ForExecForFlow.name = 'exec';
		aaa2ForExecForFlow.padding = '0 5 5 8';
		aaa2ForExecForFlow.checked=true;
		aaa2ForExecForFlow.hidden=true;
		aaa2ForExecForFlow.inputValue='<%=(String) map.get("id")%>';
	operInfo2ForExecForFlow.push(aaa2ForExecForFlow);
<%}
			}%>
	var opitem2ForExecForFlow = {
		layout : 'column',
		xtype : 'checkboxgroup',
		name : 'chkexec',
		id : 'chkexec',
		columns : 2,
		items : operInfo2ForExecForFlow
	};
	shows2ForExecForFlow.push(opitem2ForExecForFlow);
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/forExecForFlow/scriptserverUT.js">
	
</script>
</head>
<body>
	<div id="sss2ForExecForFlow"></div>
	<div id="scriptserverUT_area2ForExecForFlow" style="width: 100%; height: 100%"></div>
</body>
</html>