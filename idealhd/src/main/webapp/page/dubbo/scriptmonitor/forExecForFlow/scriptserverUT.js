Ext.onReady(function() {
	var form;

	if (state2ForExecForFlow == '40' || state2ForExecForFlow == '-1' || state2ForExecForFlow == '1' || state2ForExecForFlow == '20'
			|| state2ForExecForFlow == '60' || state2ForExecForFlow == '5') {
		form = Ext.create('Ext.form.Panel', {
			layout : 'column',
			border : false,
			buttonAlign : 'center',
			width : '100%',
			height : '100%',
			items : [ {
				layout : 'anchor',
				width : '100%',
				border : false,
				items : shows2ForExecForFlow
			} ],
			buttons : [ {
				text : '返回',
				handler : function() {
					personExcute_window2ForExecForFlow.close();
				}
			}]
		});
	} else {
		form = Ext.create('Ext.form.Panel', {
			layout : 'column',
			border : false,
			buttonAlign : 'center',
			width : '100%',
			height : '100%',
			items : [ {
				layout : 'anchor',
				width : '100%',
				border : false,
				items : shows2ForExecForFlow
			} ],
			buttons : [ {
				text : '确认执行',
				handler : function() {
					PersonExceptionExec();
				}
			},
			{
				text : '返回',
				handler : function() {
					personExcute_window2ForExecForFlow.close();
				}
			}]
		});
	}

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptserverUT_area2ForExecForFlow",
		border : false,
		width : '100%',
		height : '100%',
		bodyPadding : 5,
		autoScroll : true,// 滚动条
		items : [ form ]
	});

	function PersonExceptionExec(execModel) {
		var hobbyValue = form.getForm().findField("chkexec").getChecked();
		if (cnt2ForExecForFlow != 0) {
			if (!hobbyValue || hobbyValue == '') {
				Ext.Msg.alert('提示', '没有选择处理方式');
				// parent.pageBarRefresh();
				// personExcute_window.close();
				return;
			}
		}
		var ids = '';
		Ext.Array.each(hobbyValue, function(item) {
			ids += item.inputValue + ",";
		});
		ids = ids.substring(0, (ids.length - 1))
		Ext.MessageBox.wait("数据处理中...", "进度条");
		Ext.Ajax.request({
			url : 'startScriptServiceUT.do',
			method : 'post',
			sync : true,
			params : {
				coatId : coatId2ForExecForFlow,
				flag : flag2ForExecForFlow,
				ids : ids
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					Ext.Msg.alert('提示', '操作成功!');
					personExcute_window2ForExecForFlow.close();
				} else {
					Ext.Msg.alert('提示', '操作失败!');
				}
			},
			failure : function(result, request) {
				Ext.Msg.alert('提示', '操作失败!');
			}
		});
	}
});