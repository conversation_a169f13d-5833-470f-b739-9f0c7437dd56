<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var whiteScriptFlowId3ForExecForFlow = '<%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>';
	var whiteScriptCoatid3ForExecForFlow = '<%=request.getParameter("coatid")==null?"":request.getParameter("coatid")%>';
	var whiteScriptIsWin3ForExecForFlow = '<%=request.getParameter("isWin")%>';
	var whiteScriptServiceName3ForExecForFlow = '<%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%>';
	var whiteScriptStartUser3ForExecForFlow = '<%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%>';
	var whiteScriptStartUserFullName3ForExecForFlow = '<%=request.getAttribute("startUserFullName")==null?"":request.getAttribute("startUserFullName")%>';
	var whiteScriptStartTime3ForExecForFlow = '<%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%>';
	var whiteScriptEndTime3ForExecForFlow = '<%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%>';
	var whiteScriptFilter_scriptNameForExecForFlow = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var whiteScriptFilter_stateForExecForFlow  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var whiteScriptFilter_startTimeForExecForFlow ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var whiteScriptFilter_endTimeForExecForFlow = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var whiteScriptFilter_serviceNameForExecForFlow = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var whiteScriptFilter_serviceCustomNameForExecForFlow = '<%=request.getParameter("filter_serviceCustomName")==null?"":request.getParameter("filter_serviceCustomName")%>';
	var whiteScriptFilter_serviceStateForExecForFlow  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var whiteScriptFilter_serviceStartTimeForExecForFlow ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var whiteScriptFilter_serviceEndTimeForExecForFlow = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var whiteScriptPlanId='<%=request.getParameter("planId")==null?"":request.getParameter("planId")%>';
	var whiteScriptScenceId='<%=request.getParameter("scenceId")==null?"":request.getParameter("scenceId")%>';
	var whiteScriptStepId='<%=request.getParameter("stepId")==null?"":request.getParameter("stepId")%>';
	var whiteScriptHidereturn='<%=request.getAttribute("hidereturn")==null?"":request.getAttribute("hidereturn")%>';
	var whiteScriptTaskNameSwitch = <%=request.getAttribute("taskNameSwitch")%>;
	var timeOutSwitchForTaskExec = <%=request.getAttribute("timeOutSwitch")%>;
	var agentPortSwitchForTaskExec = <%=request.getAttribute("agentPortSwitch")%>;
	var whiteScriptHostNameSwitch = <%=request.getAttribute("hostNameSwitch")%>;
	var whiteScriptFromMenu = '<%=request.getParameter("fromMenu")==null?"":request.getParameter("fromMenu")%>';
	var whiteScriptFilter_IpForExecForFlow = '<%=request.getParameter("filter_Ip")==null?"":request.getParameter("filter_Ip")%>';
	var workitemidFor = '<%=request.getParameter("workitemid")==null?"":request.getParameter("workitemid")%>';
	//终止、忽略等操作性按钮显隐
	var hiddenOperateButton = <%=request.getAttribute("hiddenOperateButton")%>;
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptFlowserverForExecForWhite.js"></script>
</head>
<body>
	<div id="switchruninfoins_div_exec_ForFlowWhiteScript" style="width: 100%; height: 75%">
	</div>
</body>
</html>