<%@page contentType="text/html; charset=utf-8"%>
<html>
<% 
String serviceNameForTestExecForWhite =request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName").toString();
if(serviceNameForTestExecForWhite!=null&&!"".equals(serviceNameForTestExecForWhite)){
    serviceNameForTestExecForWhite=serviceNameForTestExecForWhite.replace("\\", "\\\\").replace("\"", "\\\"");
}

%>
<head>
<script type="text/javascript">
	var flowIdForWhite = '<%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>';
	
	var forScriptFlowForWhite = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	var coatidForTestExecForWhite = '<%=request.getParameter("coatid")==null?"":request.getParameter("coatid")%>';
	<%-- var flag = '<%=request.getParameter("flag")%>'; // 0:测试     1:生成 --%>
	var isWinForTestExecForWhite = '<%=request.getParameter("isWin")%>';
	var stateCodeForWhite = '<%=request.getAttribute("stateCode")==null?-100:request.getAttribute("stateCode")%>';
	var serviceNameForTestExecForWhite = "<%=serviceNameForTestExecForWhite%>";
 
	var startUserForTestExecForWhite = '<%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%>';
	var startTimeForTestExecForWhite = '<%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%>';
	var endTimeForTestExecForWhite = '<%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%>';
	var filter_scriptNameForWhite = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForWhite  = '<%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>';
	var filter_startTimeForWhite ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForWhite = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForWhite = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForWhite  = '<%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>';
	var filter_serviceStartTimeForWhite ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForWhite = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var from_where ='<%=request.getAttribute("fromWhere")==null?"":request.getAttribute("fromWhere")%>';
	var white_coatid='<%=request.getAttribute("whitecoatid")==null?"":request.getAttribute("whitecoatid")%>';
	var white_flag='<%=request.getAttribute("whiteflag")==null?"":request.getAttribute("whiteflag")%>';
	var white_iid='<%=request.getAttribute("whiteiid")==null?"":request.getAttribute("whiteiid")%>';
	var white_serviceName='<%=request.getAttribute("whiteserviceName")==null?"":request.getAttribute("whiteserviceName")%>';
	var white_Flag='<%=request.getAttribute("whiteparmFlag")==null?"":request.getAttribute("whiteparmFlag")%>';
	var white_content='<%=request.getAttribute("whiteicontent")==null?"":request.getAttribute("whiteicontent")%>';
	var editingChosedAgentIds = '<%=request.getAttribute("editingChosedAgentIds")==null?"":request.getAttribute("editingChosedAgentIds")%>';
	var filter_IpForWhite = '<%=request.getParameter("filter_Ip")==null?"":request.getParameter("filter_Ip")%>';

	//白名单执行跳转带过来的查询agent条件，返回时使用  start
	var hostNameForWhite = '<%=request.getAttribute("hostName")==null?"":request.getAttribute("hostName")%>';
	var ipForWhite = '<%=request.getAttribute("ip")==null?"":request.getAttribute("ip")%>';
	var sysAdminForWhite = '<%=request.getAttribute("sysAdmin")==null?"":request.getAttribute("sysAdmin")%>';
	var centerNameForWhite ='<%=request.getAttribute("centerName")==null?"":request.getAttribute("centerName")%>';
	var systemInfoForWhite ='<%=request.getAttribute("systemInfo")==null?"":request.getAttribute("systemInfo")%>';
	var middlewareTypeForWhite ='<%=request.getAttribute("middlewareType")==null?"":request.getAttribute("middlewareType")%>';
	var osTypeForWhite ='<%=request.getAttribute("osType")==null?"":request.getAttribute("osType")%>';
	var dbTypeForWhite ='<%=request.getAttribute("dbType")==null?"":request.getAttribute("dbType")%>';
	//白名单执行跳转带过来的查询agent条件，返回时使用  end
 </script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptserverForExecWhite.js"></script>
</head>
<body>
	<div id="scriptserverForExecWhite_area" style="width: 100%; height: 75%">
	</div>
</body>
</html>