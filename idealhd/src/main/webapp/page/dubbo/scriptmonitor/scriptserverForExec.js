var rowExpanderLog_exec;
var scriptmonitorinfoins_store_exec;
var scriptmonitorinfoins_grid_exec;
var interVForExec = 10;
var interPVForExec = 20;
var lastIdForExec;
var lastRowIndexForExec;
var lastrequestIdForExec;
var lastiipForExec;
var lastiportForExec;
var flag_exec = 1; // 0:测试     1:生成
Ext.onReady(function() {
    destroyRubbish();

    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        },
        {name: 'hostName',     type: 'string'}
        ]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        listeners: {
            select: function(me, record, index, eOpts) { // 选择复选框事件
                flowMesshisForExec(record.data.iid, index);
            },
            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
                flowMesshisForExec(record.data.iid, index);
            }
            // ,
            // selectionchange : function(me, selected, record, index,eOpts)
            // {
            // alert(record.iid);
            // alert(index);
            // flowMesshisForExec(record.get('iid'),index);
            // }
        }
    });
    scriptmonitorinfoins_store_exec = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flag_exec + '&coatid=' + coatid_exec,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
                flowMesshisRefreshForExec(lastIdForExec, lastRowIndexForExec);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatid_exec
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        itaskName.setValue(res.itaskName);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime);
//                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
//                        $("#scriptState").html(res.state);
//                        $("#scriptEndTime").html(res.endTime);
//                        $("#scriptRunTime").html(res.runTime + " 秒");
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },
    { text: '计算机名',  dataIndex: 'hostName',width:80},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 200,
        menuDisabled : true,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state');
            var zoomStr = "";
            if (isWin_exec != 1) {
                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetailForExec(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
            }
            zoomStr = '';
            if(dbaas_f_prjType==1||dbaas_f_prjType=='1'){
            	return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExec(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServerForExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            }else{
	            if (state == '30' || state == '40' || state == '50') {
	                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExec(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServerForExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="skipScriptServerForExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
	            } else if (state == '-1' || state == '1') {
	                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExec(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServerForExec(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
	            } else {
	                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExec(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
	            }
            }
        }
    }];

    rowExpanderLog_exec = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: [
        	'<div id="stephisForExec{iid}">',
        		'<pre  onselectstart="return true" id="steptextareahisForExec{iid}"  class="monitor_desc"></pre>',
        		'&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span" {state:this.toShow}>自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshIdForExec" name="rowFreshIdForExec" >&nbsp;秒</span>',
        		'&nbsp;&nbsp;&nbsp;<input {state:this.toShow} type="button" value="刷新" onclick="loadShelloutputhisForExec({iid},\'{agentIp}\',{agentPort})" class="Common_Btn Monitor_Btn">',
        		'&nbsp;&nbsp;&nbsp;<input {state:this.toShow} type="button" value="终止" onclick="scriptServerStopForExec({iid},{state})" class="Common_Btn Monitor_Btn">',
        	'</div>',
        	{
            toShow: function(state){
             var flag = '';
              if(state==20 || state==60){
                flag = 'style="display: none;"';
              }
              return flag;
           }
	     }]
    });
    // 展开符合某个条件的行
    // function expendRow() {
    // var i;// 循环临时变量
    // var arr = [];// 要展开的行的数组
    // for (i = 0; i < scriptmonitorinfoins_store_exec.data.length; i++)//
    // ProdRequireInfoStore是gridpanel的数据源
    // {
    // var record = scriptmonitorinfoins_store_exec.getAt(i);// 循环遍历每一行
    // arr.push(i);
    // }
    // for (var j = 0; j < arr.length; j++) {//
    // 遍历数组展开调用toggleRow(index)方法展开某一行
    // expander.toggleRow(arr[j]);
    // }
    // }
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        hidden: stateCode==20 || stateCode==60,
        name: "pageFreshTime",
        value: interPVForExec
    });
    var oam_act_form = Ext.create('Ext.form.Panel', {
		frame : true,
		border : false,
		bodyCls:'fm-spinner',
		layout : {
			type : 'hbox',
			align : 'middle'
		},
		defaults : {
			anchor : '100%'
		},
		items : [
				{
            xtype: "label",
            hidden: stateCode==20 || stateCode==60,
            text: "自动刷新"
        },pageFreshTime , {
					xtype : 'label',
					text : '    秒',
				} ]
	});
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitorinfoins_store_exec,
        dock: 'bottom',
        displayInfo: true,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        items: [oam_act_form,
        {
            xtype: 'button',
            width: 70,
            height: 30,
            cls: 'Common_Btn',
            //											id : 'button2',
            text: '刷  新',
            hidden: stateCode==20 || stateCode==60,
            listeners: {
                click: function() {
                    if (refreshObjForExec) {
                        clearInterval(refreshObjForExec);
                    }
                    refreshPage();
                    // var interValue =
                    // document.getElementById('pageFreshTime').value;
                    var interValue = pageFreshTime.getValue();
                    interPVForExec = interValue;
                    if (interPVForExec < 20) {
                        interPVForExec = 20;
                    }
                    refreshObjForExec = setInterval(refreshPage, interPVForExec * 1000);
                }
            }
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '终  止',
            hidden: stateCode==20 || stateCode==60,
            listeners: {
                click: function() {
                    var data = getCHKBoxIds();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                        function(button, text) {
                            if (button == "yes") {
                                if (data == '-1') {
                                    Ext.Msg.alert('提示', '该步骤已经结束，无需终止!');
                                    scriptmonitorinfoins_store_exec.reload();
                                    return;
                                }
                                Ext.MessageBox.wait("数据处理中...", "提示");
                                Ext.Ajax.request({
                                    url: 'scriptServiceShellKill.do',
                                    params: {
                                        flag: flag_exec,
                                        insIds: data
                                    },
                                    method: 'POST',
                                    success: function(response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        // 当后台数据同步成功时
                                        if (success) {
                                        	 if (refreshObjShellOutputForExec) {
                                                 clearInterval(refreshObjShellOutputForExec);
                                             }
                                            scriptmonitorinfoins_store_exec.reload();
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '返  回',
            listeners: {
                click: function() {
                	returnBackForExec();
                }
            }
        }]
    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_grid_exec.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right',
        value: serviceNameForExec
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		padding : '5',
		width : '33%',
		hidden :true,
        labelAlign : 'right'
    });
    
    var itaskName = new Ext.form.field.Display({
    	fieldLabel: '任务名称',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right'
      });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserForExec
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startTimeForExec
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: endTimeForExec
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        cls:'sc_tlbar_height',
        dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [sName, stateField, itaskName,startUserField]
		},
		{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    scriptmonitorinfoins_grid_exec = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: scriptmonitorinfoins_store_exec,
        border: true,
        columnLines: true,
        columns: scriptmonitorinfoins_columns,
        bbar: pageBar,
        selModel: selModel,
        enableTextSelection:true,
        plugins: [rowExpanderLog_exec],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                /*
						 * var cls = ''; if(record.data.state==10){ cls =
						 * 'row_Blue'; }else if(record.data.state==20){ cls =
						 * 'row_Green'; }else if(record.data.state==30){ cls =
						 * 'row_Red'; }else if(record.data.state==60){ cls =
						 * 'row_Gray'; } else { cls = 'row_Gray'; } return cls;
						 */
                return 'norowexpandblah';
            }
        }
/*    ,

        listeners: {
            itemclick: function(a, record, item, index, e, eOpts) {
                rowExpanderLog_exec.toggleRow(index, record);
            }
        }*/
    });

    scriptmonitorinfoins_grid_exec.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
        interVForExec = 10;
    	if (Ext.isIE) {
        	document.getElementById('rowFreshIdForExec').innerText = interVForExec;
        } else {
            document.getElementById('rowFreshIdForExec').innerHTML = interVForExec;
        }
        loadShelloutputhisForExec(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
        // refreshObjShellOutputForExec = setInterval(function() {
        // loadShelloutputhisForExec(record.get('iid'),
        // record.get('agentIp'), record.get('agentPort'));
        // }, 1000);
    });
    scriptmonitorinfoins_grid_exec.view.on('collapsebody',
    function(rowNode, record, expandRow, eOpts) {
        lastIdForExec = 0;
        lastRowIndexForExec = 0;
        if (refreshObjShellOutputForExec) {
            clearInterval(refreshObjShellOutputForExec);
        }
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoins_div_exec",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        bodyPadding: 5,
        items: [info_form, scriptmonitorinfoins_grid_exec]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

    function refreshPage() {
//    	if(contentPanel.getLoader().url=='forwardscriptcoatforexec.do') {
    		scriptmonitorinfoins_store_exec.reload();
//    	}
    }

    if (refreshObjForExec) {
        clearInterval(refreshObjForExec);
    }
    refreshObjForExec = setInterval(refreshPage, interPVForExec * 1000);
    
    function returnBackForExec() {
        lastIdForExec = '';
        if (refreshObjShellOutputForExec) {
            clearInterval(refreshObjShellOutputForExec);
        }
        if (refreshObjForExec) {
            if (refreshObjForExec) {
                clearInterval(refreshObjForExec);
            }
            clearInterval(refreshObjForExec);
        }

        contentPanel.getLoader().load({
            url: "forwardscriptcoatforexec.do",
            scripts: true,
            params: {
    			flowId : flowId_exec,
    			flag : flag_exec,
            	forScriptFlow: forScriptFlow,
            	filter_scriptName:filter_scriptName,
			    filter_state:filter_state,
			    filter_startTime:filter_startTime,
			    filter_endTime:filter_endTime,
			    filter_serviceName:filter_serviceName,
				filter_serviceState:filter_serviceState,
				filter_serviceStartTime:filter_serviceStartTime,
				filter_serviceEndTime:filter_serviceEndTime
            }
        });
    }

});
function flowMesshisForExec(iruninfoinsid, rowIndex) {
    lastIdForExec = iruninfoinsid;
    lastRowIndexForExec = rowIndex;
    var record = scriptmonitorinfoins_store_exec.getAt(rowIndex);
    var records = scriptmonitorinfoins_store_exec.getRange(0, scriptmonitorinfoins_store_exec.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLog_exec.recordsExpanded[records[i].internalId]) {
            rowExpanderLog_exec.toggleRow(i, records[i]);
        }
    }
//     var record = scriptmonitorinfoins_store_exec.getAt(rowIndex);
     rowExpanderLog_exec.toggleRow(rowIndex, record);
}
function loggerDetailForExec(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForExec) {
        clearInterval(refreshObjShellOutputForExec);
    }
    if (refreshObjForExec) {
        if (refreshObjForExec) {
            clearInterval(refreshObjForExec);
        }
        clearInterval(refreshObjForExec);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
//            flowId: flowId_exec,
            coatId: coatid_exec,
            flag: flag_exec
        }
    });
}

function flowMesshisRefreshForExec(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    //var record = scriptmonitorinfoins_store_exec.getAt(rowIndex);
    var records = scriptmonitorinfoins_store_exec.getRange(0, scriptmonitorinfoins_store_exec.getCount());
    var rowFreshValue = document.getElementById('rowFreshIdForExec').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForExec = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForExec').innerText = interVForExec;
    } else {
        document.getElementById('rowFreshIdForExec').innerHTML = interVForExec;
    }

    rowExpanderLog_exec.toggleRow(lastRowIndexForExec, records[lastRowIndexForExec]);
    if(refreshObjShellOutputForExec) {
    	clearInterval(refreshObjShellOutputForExec);
    }
    refreshObjShellOutputForExec = setInterval(function() {
    	if(contentPanel.getLoader().url=='forwardscriptcoatforexec.do') {
    		loadShelloutputhisInfoForExec(lastrequestIdForExec, lastiipForExec, lastiportForExec);
    	}
    },
    rowFreshValue * 1000);
    // var record = scriptmonitorinfoins_store_exec.getAt(rowIndex);
    // rowExpanderLog_exec.toggleRow(rowIndex, record);
}

function loadShelloutputhisForExec(requestId, iip, iport) {
    lastrequestIdForExec = requestId;
    lastiipForExec = iip;
    lastiportForExec = iport;
    if (refreshObjShellOutputForExec) {
        clearInterval(refreshObjShellOutputForExec);
    }
    var rowFreshValue = document.getElementById('rowFreshIdForExec').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForExec = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForExec').innerText = interVForExec;
    } else {
        document.getElementById('rowFreshIdForExec').innerHTML = interVForExec;
    }
    // document.getElementById('rowFreshIdForExec').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfoForExec(requestId, iip, iport);
    refreshObjShellOutputForExec = setInterval(function() {
        loadShelloutputhisInfoForExec(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfoForExec(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForExec' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: flag_exec
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerText = msg.message;
                } else {
                    document.getElementById(desc).innerText = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerText = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStopForExec(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
                Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_store_exec.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag_exec
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                    	if (refreshObjShellOutputForExec) {
                    		clearInterval(refreshObjShellOutputForExec);
                    	}
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store_exec.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServerForExec(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag_exec
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store_exec.reload();
                    if (refreshObjShellOutputForExec) {
                        clearInterval(refreshObjShellOutputForExec);
                    }
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServerForExec(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag_exec
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store_exec.reload();
                    if (refreshObjShellOutputForExec) {
                        clearInterval(refreshObjShellOutputForExec);
                    }
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
