<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment,com.ideal.common.utils.SessionData,java.util.ArrayList,java.util.Arrays"%>
  
<%
	String highPermissionUser = Environment.getInstance().getScriptTaskExecHistoryHighpermissionUser();
	SessionData sessionData = SessionData.getSessionData(request);
	String loginUserName = sessionData.getLoginName();
	ArrayList<String> arlist = new ArrayList<String>(Arrays.asList(highPermissionUser.split(",")));
	boolean showStartUser =true;
	 if (arlist.contains(loginUserName))// 如果包含，代表是高权限用户，展示执行人
     {
	     showStartUser =false;
     }
	 boolean scriptSpdbExecHisSwitch = Environment.getInstance().getScriptSpdbExecHisSwitch();
	 //是否展示单号列与查询条件
	boolean scriptHisBufferNumberSwitch = Environment.getInstance().scriptHisBufferNumberSwitch();
	//超时查询条件开关
	boolean execHisTimeOut = Environment.getInstance().getScriptExecHisTimeoutSwitch();
	//邮储 任务申请 支持 脚本附件参数化 每次可以上传不同的脚本附件，不改变原有脚本绑定的脚本附件 默认false true开启后支持该功能
	//此处控制下载按钮显示/隐藏
	boolean taskApplyAttachment = Environment.getInstance().getScriptTaskApplyAttachmentParamsSwitch();
	// 福建农信CI
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
%>
<html>
<head>
<script type="text/javascript">
	var flowIdFor2LevelScriptHistory = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var forScriptFlow2LevelScriptHistory = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	var filter_scriptName2LevelScriptHistory = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_state2LevelScriptHistory  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTime2LevelScriptHistory ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTime2LevelScriptHistory = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';

	var startTimeEnd1 ='<%=request.getParameter("startTimeEnd")==null?"":request.getParameter("startTimeEnd")%>';
	var endTimeEnd1 = '<%=request.getParameter("endTimeEnd")==null?"":request.getParameter("endTimeEnd")%>';
	var user1 = '<%=request.getParameter("user")==null?"":request.getParameter("user")%>';

	var filter_serviceName2LevelScriptHistory = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState2LevelScriptHistory  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime2LevelScriptHistory ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime2LevelScriptHistory = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var projectFlag2LevelScriptHistory = '<%=request.getAttribute("projectFlag")==null?"":request.getAttribute("projectFlag")%>';
	var monitorFlag2LevelScriptHistory = '<%=request.getAttribute("monitorFlag")==null?"":request.getAttribute("monitorFlag")%>';
	var showStartUser2LevelScriptHistory = <%=showStartUser%>;
	var filter_Ip2LevelScriptHistory = '<%=request.getParameter("filter_Ip")==null?"":request.getParameter("filter_Ip")%>';
	//常用任务白名单脚本跳转执行历史2层时候使用的参数
	var filter_fromMenu2LevelScriptHistory = '<%=request.getParameter("fromMenu")==null?"":request.getParameter("fromMenu")%>';
	var scriptfunctionoutputswitch = <%=Environment.getInstance().getScriptFunctionOutputSwitch()%>;
	//浦发，单号查询条件，总览导出按钮开关
	var scriptSpdbExecHisSwitch = <%=scriptSpdbExecHisSwitch%>;
	//是否展示单号列与查询条件
	var scriptHisBufferNumberSwitch = <%=scriptHisBufferNumberSwitch%>;
	//邮储 超时查询条件开关
	var execHisTimeOutSwitch = <%=execHisTimeOut%>;
	//此处控制下载按钮显示/隐藏
	var taskApplyAttachment = <%=taskApplyAttachment%>;
	//当前登录用户为组长、副组长时组下成员（字符串ifullname：name1,name2,name3,name4）
	var groupUserIIDs = '<%=request.getAttribute("groupUserIIDs")==null?"":request.getAttribute("groupUserIIDs")%>';
	//当前登录用户fullName
	var userName = '<%=request.getAttribute("userName")==null?"":request.getAttribute("userName")%>';
	// 用来判断 当前加载页面是首次加载还是 从其他页面返回(返回则不为空)
	var returnBtn = '<%=request.getParameter("returnBtn")==null?"":request.getParameter("returnBtn")%>';
	var fjnxCISwitch = <%=fjnxCISwitch%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptParamsPublic.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptCoatForExec.js"></script>
</head>
<body>
<div id="scriptcoatmonitorForExec_area" style="width: 100%;height: 100%">
</div>
</body>
</html>