<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var flowId2ForExecForWhite = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var filter_scriptNameForExecForWhite = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForExecForWhite  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeForExecForWhite ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForExecForWhite = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForExecForWhite = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForExecForWhtie  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeForExecForWhite ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForExecForWhite = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptFlowCoatForExecWhite.js"></script>
</head>
<body>
<div id="scriptflowcoatmonitorForExecWhite_area" style="width: 100%;height: 100%">
</div>
</body>
</html>