<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<link rel="stylesheet"
	href="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/abnormal.css" />
<script type="text/javascript">
var requestId = "<%=request.getParameter("requestId")%>";
var agentIp = "<%=request.getParameter("agentIp")%>";
var agentPort = "<%=request.getParameter("agentPort")%>";
var flag3ForExecForFlow = "<%=request.getParameter("flag3ForExecForFlow")%>";
var isAutoSub= "<%=request.getParameter("isAutoSub")%>";
	var out = "";
	function getData() {
		Ext.Ajax.request({
			url : 'dbaasgetScriptExecOutput.do',
			params : {
				requestId : requestId,
				agentIp : agentIp,
				agentPort : agentPort,
				flag : flag3ForExecForFlow,
				isAutoSub:isAutoSub
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				out = msg.message;
				$('#dbaaspageoutputid').html(out);
			},
			failure : function(response, opts) {
			}
		});
	}
	getData();
</script>
</head>
<body>
	<div class="abnormal_center">
		<div class="abnormal_body">
			<div class="table_content">
				<div class="ab_table_chart" id="dbaaspageoutputid"></div>
			</div>
		</div>
	</div>
</body>
</html>