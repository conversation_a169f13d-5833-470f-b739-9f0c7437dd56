<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
<%-- var flowId = <%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>;
	var flag = <%=request.getParameter("flag")==null?"":request.getParameter("flag")%>; --%>
	
	var flowId2ForFlow = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var filter_scriptNameForFlow = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForFlow  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeForFlow ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForFlow = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForFlow = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForFlow  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeForFlow ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForFlow = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptFlowCoat.js"></script>
</head>
<body>
<div id="scriptflowcoatmonitor_area" style="width: 100%;height: 100%">
</div>
</body>
</html>