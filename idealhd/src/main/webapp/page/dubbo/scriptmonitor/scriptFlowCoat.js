var scriptmonitor_store2ForFlow;
var personExcute_window2ForFlow;
var search_form2ForFlow;
Ext.onReady(function() {
    destroyRubbish();
    var flag = 0; // 测试

    var stateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [ {
		            "id": "-1",
		            "name": "全部"
		        },
		        {
		            "id": "101",
		            "name": "完成"
		        },
		        {
		            "id": "20",
		            "name": "正常完成"
		        },
		        {
		            "id": "40",
		            "name": "异常完成"
		        },
		        {
		            "id": "102",
		            "name": "运行"
		        },
		        {
		            "id": "10",
		            "name": "正常运行"
		        },
		        {
		            "id": "50",
		            "name": "异常运行"
		        },
		        {
					"id" : "30",
					"name" : "异常"
				},
		        {
		            "id": "60",
		            "name": "终止"
		        } ]
    });
    
    var sName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel: '服务名称',
		emptyText : '--请输入脚本名称--',
		labelWidth : 70,
		width : '20%',
		value: filter_scriptNameForFlow,
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var stateCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'state',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '执行状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择执行状态--',
		store : stateStore,
		width : '15%',
        labelAlign : 'right',
        listeners: {
            afterRender: function(combo) {
               if(filter_stateForFlow=='-1') {
					combo.setValue(stateStore.getAt(0).data.id);
				} else if(filter_stateForFlow=='101'){
					combo.setValue(stateStore.getAt(1).data.id);
				} else if(filter_stateForFlow=='20'){
					combo.setValue(stateStore.getAt(2).data.id);
				} else if(filter_stateForFlow=='40'){
					combo.setValue(stateStore.getAt(3).data.id);
				} else if(filter_stateForFlow=='102'){
					combo.setValue(stateStore.getAt(4).data.id);
				} else if(filter_stateForFlow=='10'){
					combo.setValue(stateStore.getAt(5).data.id);
				} else if(filter_stateForFlow=='50'){
					combo.setValue(stateStore.getAt(6).data.id);
				} else if(filter_stateForFlow=='30'){
					combo.setValue(stateStore.getAt(7).data.id);
				} else if(filter_stateForFlow=='60'){
					combo.setValue(stateStore.getAt(8).data.id);
				}
            },
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
        }
	});
    
    var startTime = new Ext.form.field.Date({
    	name: 'startTime',
		fieldLabel: '开始时间',
		labelWidth : 70,
		width : '22%',
        labelAlign : 'right',
        format: 'Y-m-d',
        value: filter_startTimeForFlow,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var endTime = new Ext.form.field.Date({
    	name: 'endTime',
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '22%',
    	labelAlign : 'right',
    	format: 'Y-m-d',
    	value: filter_endTimeForFlow,
    	listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });

     search_form2ForFlow = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        baseCls:'customize_gray_back',
        border: false,
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',    
			border : false,
			dock : 'top',
			items: [sName,
			        stateCb,
			        startTime,
			        endTime,
            {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls : 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                xtype: 'button',
                text: '返回',
                cls: 'Common_Btn',
                handler: function() {
                	forwardtestmain2ForFlow();
                }
            }
            ]
		}]
        
    });

    Ext.define('scriptmonitorData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'cata',
            type: 'int'
        },
        {
        	name: 'flowId',
        	type: 'int'
        },
        {
        	name: 'actNo',
        	type: 'int'
        },
        {
            name: 'startUser',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'actType',
            type: 'string'
        },
        {
        	name: 'actName',
        	type: 'string'
        },
        {
        	name: 'childFlowId',
        	type: 'int'
        },
        {
            name: 'serverNum',
            type: 'int'
        }]
    });

    scriptmonitor_store2ForFlow = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptCoatList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) {
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            }
            return backValue;
        }
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: 'actType',
        dataIndex: 'actType',
        hidden: true
    },
    {
    	text: '活动名称',
    	dataIndex: 'actName',
    	flex: 1
    },
    {
        text: '服务名称',
        dataIndex: 'scriptName',
        flex: 1
    },
    {
        text: '启动人',
        dataIndex: 'startUser',
        width: 150
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 150
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 150
    },
    {
        text: '操作',
        dataIndex: 'sysOperation',
        width: 160,
        renderer: function(value, p, record) {
            var coatid = record.get('iid');
            var cata = 0; //record.get('cata');
            var actType = record.get('actType');
            var actNo = record.get('actNo');
            var flowId = record.get('flowId');
            var state = record.get('state');
            var childFlowId = record.get('childFlowId');
            if (actType == '0') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardruninfo2ForFlow(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' +
                //'<a href="javascript:void(0)" onclick="scriptCoatStop2ForFlow('+coatid+', '+ cata +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_termination"></img>终止</a>'+
                '<a href="javascript:void(0)" onclick="resultExport2ForFlow(' + coatid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>' + '</span>';
            } else if ('2' == actType) {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardUTruninfo2ForFlow(' + flowId + ', ' + coatid + ', '+ actNo + ', '+ state + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>' + '</span>';
            } else if ('3' == actType) {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardCallflowruninfo2ForFlow(' + childFlowId + ', ' + coatid + ', '+ actNo + ', '+ state + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>' + '</span>';
            }

        }
    }];

    scriptmonitor_store2ForFlow.on('beforeload', function(store, options) {
        var new_params = {
        		scriptName:search_form2ForFlow.getForm().findField("scriptName").getValue(),
            	    	flowId:flowId2ForFlow,
            //	    	scriptName:search_form2ForFlow.getForm().findField("scriptName").getValue(),
            //	    	startUser:search_form2ForFlow.getForm().findField("startUser").getValue(),
            state: stateCb.getValue(),
            cata: 0,
            //search_form2ForFlow.getForm().findField("cata").getValue(),
            startTime: startTime.getValue(),
            endTime: endTime.getValue(),
            forScriptFlow: 1
        };

        Ext.apply(scriptmonitor_store2ForFlow.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitor_store2ForFlow,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });

    var scriptmonitor_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	autoScroll: true,
        store: scriptmonitor_store2ForFlow,
        cls:'customize_panel_back',
        border: false,
        cls:'sc_tab_height',
        columnLines: true,
        columns: scriptmonitor_columns,
        bbar: pageBar
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptflowcoatmonitor_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : true,
        items: [search_form2ForFlow, scriptmonitor_grid]
    });

    function clearQueryWhere() {
    	search_form2ForFlow.getForm().findField("scriptName").setValue(''),
        //    	search_form2ForFlow.getForm().findField("scriptName").setValue(''),
        //    	search_form2ForFlow.getForm().findField("startUser").setValue(''),
    	stateCb.setValue("-1"),
        //    	search_form2ForFlow.getForm().findField("cata").setValue("-1"),
        startTime.setValue(''),
        endTime.setValue('');
    }

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    
});

function utStartSuccessCallback() {
	scriptmonitor_store2ForFlow.reload();
}

function forwardUTruninfo2ForFlow(flowId, coatid, actNo, state, cata) {
	personExcute_window2ForFlow = Ext.create('Ext.window.Window', {
		title : '人工提醒',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : 600,
		height : 400,
		loader : {
			url : 'scriptServiceUT2ForFlow.do',
			params : {
				actNo : actNo,
				coatid:coatid,
				flowId:flowId,
				flag : 0,
				state:state 
			},
			autoLoad : true,
//			autoDestroy : true,
			scripts : true
		}
	}).show();
}

function forwardCallflowruninfo2ForFlow(childFlowId, coatid, actNo, state, cata) {
	contentPanel.getLoader().load({
		url: "forwardscriptflowcoat.do",
		scripts: true,
		params : {
			flowId:childFlowId, 
			flag:cata, 
			forScriptFlow: 1
		}
    });
}

function forwardruninfo2ForFlow(coatid, flag) {
	var scriptName=search_form2ForFlow.getForm().findField("scriptName").getValue();
	var state =search_form2ForFlow.getForm().findField("state").getValue();
	var startTime = search_form2ForFlow.getForm().findField("startTime").getRawValue();
	var endTime =search_form2ForFlow.getForm().findField("endTime").getRawValue();
    contentPanel.getLoader().load({
        url: "forwardscriptserverForFlow.do",
        scripts: true,
        params: {
            flowId:flowId2ForFlow,
            coatid: coatid,
            forScriptFlow: 1,
            flag: flag,
            filter_scriptName:scriptName,
			filter_state:state,
			filter_startTime:startTime,
			filter_endTime:endTime,
			filter_serviceName:filter_serviceNameForFlow,
			filter_serviceState:filter_serviceStateForFlow,
			filter_serviceStartTime:filter_serviceStartTimeForFlow,
			filter_serviceEndTime:filter_serviceEndTimeForFlow
        }
    });
}
function forwardtestmain2ForFlow() {
	   contentPanel.getLoader().load({
	        url: "scriptMonitorForFlowTest.do",
	        scripts: true,
	         params: {
    			filter_serviceName:filter_serviceNameForFlow,
			    filter_serviceState:filter_serviceStateForFlow,
				filter_serviceStartTime:filter_serviceStartTimeForFlow,
				filter_serviceEndTime:filter_serviceEndTimeForFlow
            }
	    });
}
function resultExport2ForFlow(coatid, flag) {
    window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag=' + flag;
}

function scriptCoatStop2ForFlow(coatid, flag) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.Ajax.request({
                url: 'scriptCoatStop.do',
                method: 'POST',
                params: {
                    coatid: coatid,
                    flag: flag
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitor_store2ForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })

}