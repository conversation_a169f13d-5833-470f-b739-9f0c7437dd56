<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var flowId2ForToolMonitor = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var filter_scriptNameForToolMonitor = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForToolMonitor  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeForToolMonitor ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForToolMonitor = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForToolMonitor = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForToolMonitor  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeForToolMonitor ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForToolMonitor = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var callback_functionForToolMonitor = '<%=request.getParameter("callback_function")==null?"":request.getParameter("callback_function")%>';
	var sessionIdForScriptCoatForToolMonitor = '<%=request.getSession().getId()%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptCoatForToolMomitor.js"></script>
</head>
<body>
<div id="scriptCoatForToolMomitor_area" style="width: 100%;height: 100%">
</div>
</body>
</html>