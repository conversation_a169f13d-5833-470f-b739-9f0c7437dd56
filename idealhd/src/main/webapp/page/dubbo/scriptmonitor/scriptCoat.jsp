<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean scriptNameSwitch_forScriptCoat = Environment.getInstance().getScriptNameSwitch();
	boolean showEditSwitch_forScriptCoat = Environment.getInstance().getScriptHideEditSwitch();
	//渤海参数验证
	boolean bhParameterCheckSwitch = Environment.getInstance().bhParameterCheckSwitch();
%>
<html>
<head>
	<script type="text/javascript">
		var scnow="";
		if(isTabSwitch){
			$(document).ready(function(){
				$("#scriptcoatmonitor_area_forScriptCoat").attr('id',$("#scriptcoatmonitor_area_forScriptCoat").attr('id')+scnow)
			});
		}
	</script>
<script type="text/javascript">
<%-- var flowId = <%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>;
	var flag = <%=request.getParameter("flag")==null?"":request.getParameter("flag")%>; --%>

	var flowId_forScriptCoat = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	//跳转  我的脚本用
	var sprictCoatId_forScriptCoat = <%=request.getAttribute("sprictCoatId")==null?0:request.getAttribute("sprictCoatId")%>;
	var forScriptFlow_forScriptCoat = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	//返回 我的脚本用
	var forMyScript_forScriptCoat = '<%=request.getAttribute("forMyScript")==null?"":request.getAttribute("forMyScript")%>';
	var filter_scriptName_forScriptCoat = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_state_forScriptCoat  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTime_forScriptCoat ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTime_forScriptCoat = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceName_forScriptCoat = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState_forScriptCoat  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime_forScriptCoat ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime_forScriptCoat = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var sessionIdForScriptCoat_forScriptCoat = '<%=request.getSession().getId()%>';
	var scriptNameSwitch_forScriptCoat = <%=scriptNameSwitch_forScriptCoat%>;
	var showEditSwitch_forScriptCoat = <%=showEditSwitch_forScriptCoat%>;
var bhParameterCheckSwitch=<%=bhParameterCheckSwitch%>
	var scriptfunctionoutputswitch = <%=Environment.getInstance().getScriptFunctionOutputSwitch()%>
	var scriptEditBookSwitch = <%=Environment.getInstance().getScriptEditBookSwitch()%>
	var scriptShellSyntaxValidateSwitch=<%=Environment.getInstance().getScriptShellSyntaxValidate()%>;
</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/functionAndVariableCommon.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptCoat.js"></script>
</head>
<body>
<input type="hidden" id="scriptManagePageExecUserNameText" />
<div id="scriptcoatmonitor_area_forScriptCoat" style="width: 100%;height: 100%">
</div>
</body>
</html>