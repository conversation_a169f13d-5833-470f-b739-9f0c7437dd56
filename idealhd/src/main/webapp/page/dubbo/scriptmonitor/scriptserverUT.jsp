<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<%
    List listOper = (List) request.getAttribute("opers");
	List listShow = (List) request.getAttribute("shows");
%>
<html>
<head>

<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
</script>
<script type="text/javascript">
	var coatId = '<%=request.getAttribute("actId")%>';
	var actName = '<%=request.getAttribute("actName")%>';
	var state = '<%=request.getAttribute("state")%>';
	var flag = '<%=request.getAttribute("flag")%>';
	var cnt=0;
   var shows =  [ {	fieldLabel : '人工提醒', labelWidth : 80, 	name : 'wt_C',cls:'sc_tlbar_height',	xtype : "displayfield",width:250,value : actName}];
   //var opers=[];
   <%if (null != listShow && listShow.size() > 0) {
				for (int i = 0; i < listShow.size(); i++) {
					Map map = (Map) listShow.get(i);%>
	   	var showInfo = {};
	   	showInfo.fieldLabel = '描述信息';
	   	showInfo.value = '<%=(String) map.get("value")%>';
	   	showInfo.labelWidth = 80;
	   	showInfo.width = 500;
	   	showInfo.name='<%=(String) map.get("name")%>';
	   	showInfo.xtype="textarea";
	    //showInfo.disabled=true;
	   	showInfo.height=140;
	   	shows.push(showInfo);
   			<%}
			}%>
   
	var operInfo =[];
   <%if (null != listOper && listOper.size() > 0) {
				for (int i = 0; i < listOper.size(); i++) {
					Map map = (Map) listOper.get(i);%>
					cnt=<%=listOper.size()%>;
		var aaa = {};			
		aaa.boxLabel = '<%=(String) map.get("name")%>';
		aaa.name = 'exec';
		aaa.padding = '0 10 0 10';
		aaa.checked=true;
		aaa.hidden=true;
		aaa.inputValue='<%=(String) map.get("id")%>';
	operInfo.push(aaa);
<%}
			}%>
	var opitem = {
		layout : 'column',
		xtype : 'checkboxgroup',
		name : 'chkexec',
		id : 'chkexec',
		columns : 2,
		items : operInfo
	};
	shows.push(opitem);
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptserverUT.js">
	
</script>
</head>
<body>
	<div id="sss"></div>
	<div id="scriptserverUT_area" style="width: 100%; height: 100%"></div>
</body>
</html>