Ext.onReady(function() {
	var scriptmonitor_storeWhite;
	var search_formWhite;
    destroyRubbish();
    var flag = 0; // 测试
   

    var stateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [ {
		            "id": "-1",
		            "name": "全部"
		        },
		        {
		            "id": "101",
		            "name": "完成"
		        },
/*		        {
		            "id": "20",
		            "name": "正常完成"
		        },
		        {
		            "id": "40",
		            "name": "异常完成"
		        },*/
		        {
		            "id": "102",
		            "name": "运行"
		        },
/*		        {
		            "id": "10",
		            "name": "正常运行"
		        },
		        {
		            "id": "50",
		            "name": "异常运行"
		        },*/
		        {
					"id" : "30",
					"name" : "异常"
				},
		        {
		            "id": "60",
		            "name": "已终止"
		        } ]
    });
    /*var cataStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
		        {"id":"-1", "name":"全部"},
		        {"id":"0", "name":"测试"},
		        {"id":"1", "name":"生产"}
		        ]
	});*/

    /*var startUserStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'startUserModel',
		proxy : {
			type : 'ajax',
			url : 'getStartUser.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
	    }
	});*/
    
    var sName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel: '命令说明',
		emptyText : '--请输入命令说明--',
		labelWidth : 70,
		padding : '5',
		width : '20%',
		value: filter_scriptNameWhite,
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
            	 
                if (e.getKey() == e.ENTER) {
                	if(!search_formWhite.isValid()){
								Ext.Msg.alert('提示', 'IP不符合要求！');
								return;
				    }
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var stateCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'state',
		padding : '5',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '执行状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择执行状态--',
		store : stateStore,
		width : '15%',
        labelAlign : 'right',
        listeners: {
            afterRender: function(combo) {
               if(filter_stateWhite=='-1') {
					combo.setValue(stateStore.getAt(0).data.id);
				} else if(filter_stateWhite=='101'){
					combo.setValue(stateStore.getAt(1).data.id);
				}else if(filter_stateWhite=='102'){
					combo.setValue(stateStore.getAt(2).data.id);
				}else if(filter_stateWhite=='30'){
					combo.setValue(stateStore.getAt(3).data.id);
				}else if(filter_stateWhite=='60'){
					combo.setValue(stateStore.getAt(4).data.id);
				}
            },
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	 if(!search_formWhite.isValid()){
									Ext.Msg.alert('提示', 'IP不符合要求！');
									return;
					    }
	                	pageBar.moveFirst();
	                }
	            }
        }
	});
    
    var startTime = new Ext.form.field.Date({
    	name: 'startTime',
		fieldLabel: '开始时间',
		labelWidth : 70,
		padding : '5',
		width : '22%',
        labelAlign : 'right',
        format: 'Y-m-d H:i:s',
        value: filter_startTimeWhite,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	if(!search_formWhite.isValid()){
								Ext.Msg.alert('提示', 'IP不符合要求！');
								return;
				    }
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var endTime = new Ext.form.field.Date({
    	name: 'endTime',
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '22%',
    	labelAlign : 'right',
    	format: 'Y-m-d H:i:s',
    	value: filter_endTimeWhite,
    	listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
        	        if(!search_formWhite.isValid()){
						Ext.Msg.alert('提示', 'IP不符合要求！');
						return;
				    }
                	pageBar.moveFirst();
                }
            }
        }
    });
	var agentIp = new Ext.form.TextField({
		name : 'agentIp',
		fieldLabel: 'IP',
		emptyText : '--请输入IP--',
		labelWidth : 40,
		padding : '5',
		regex:/^([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])(\.([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])){3}$/,
		regexText:"IP不符合要求！",
		width : '20%',
        labelAlign : 'right',
        value:filter_IpWhite,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	if(!search_formWhite.isValid()){
						Ext.Msg.alert('提示', 'IP不符合要求！');
						return;
				    }
                	pageBar.moveFirst();
                }
            }
        }
	});
    search_formWhite = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items: [sName,
					agentIp,
			        stateCb,
			        startTime,
			        endTime ]
		},{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : ['->',{
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                	if(!search_formWhite.isValid()){
						Ext.Msg.alert('提示', 'IP不符合要求！');
						return;
				    }
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls : 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            },{
				xtype : 'button',
				text : '返回',
				cls : 'Common_Btn',
				handler: function() {
  		        	var myurl = "toExecMyWhite.do";
  		        	contentPanel.setTitle("白名单执行");
  		        	contentPanel.getLoader().load({url: myurl,
  		        		scripts: true,
  		        		params: {
							editingChosedAgentIds : editingChosedAgentIds,
							hostName:hostNameForWhite,
							ip:ipForWhite,    
							sysAdmin:sysAdminForWhite,    
							centerName:centerNameForWhite,    
							systemInfo:systemInfoForWhite,  
							middlewareType: middlewareTypeForWhite,
							osType:osTypeForWhite,       
							dbType:dbTypeForWhite
				        },
  		        		callback: function(records, operation, success) {
  		    	    		showImg(myurl);
  		    	    }});
  			    }
			}]
		}]
        
    });

    Ext.define('scriptmonitorData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },{
            name: 'idata',
            type: 'string'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'cata',
            type: 'int'
        },
        {
        	name: 'flowId',
        	type: 'int'
        },
        {
        	name: 'actNo',
        	type: 'int'
        },
        {
            name: 'startUser',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'actType',
            type: 'string'
        },
        {
            name: 'serverNum',
            type: 'int'
        },
        {
            name: 'scriptTestID',
            type: 'string'
        },
        {
            name: 'scriptTestStatus',
            type: 'int'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },{name: 'scriptContent',     type: 'string'}]
    });
    
    scriptmonitor_storeWhite = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptCoatList.do',
            timeout:180000,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) {
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            }
            return backValue;
        }
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: 'actType',
        dataIndex: 'actType',
        hidden: true
    },
    {
        text: '命令说明',
        dataIndex: 'serviceName',
        width: 100,
        renderer:function(value,p,record,rowIndex){
	    	return value.replace("<", "&lt");
	    }
    },{
        text: '命令',
        dataIndex: 'scriptContent',
        width: 100,
        flex: 1,
        renderer:function(value,p,record,rowIndex){
	    	return value.replace("<", "&lt");
	    }
    },
    {
        text: '参数',
        dataIndex: 'idata',
        width : 100
    },
    {
        text: '脚本名称',
        dataIndex: 'scriptName',
        hidden: true,
        flex: 1
    },
    {
        text: '启动人',
        dataIndex: 'startUser',
        hidden: true,
        width: 120
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 150
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 150
    },{
        text: '脚本状态',
        dataIndex: 'scriptTestStatus',
        width: 50,
        hidden: true
    },
    //					                { text: '服务器个数',  dataIndex: 'serverNum',width:100},
    /*{ text: '类别',  dataIndex: 'cata',width:50,renderer:function(value,p,record){
					                	var backValue = "";
					                	if(value==0){
					                		backValue = "测试";
					                	}else if(value==1){
					                		backValue = "生产";
					                	}
					                	return backValue;
					                }},*/
    {
			text : '操作',
			xtype : 'actiontextcolumn',
			width : 180,
			items : [{
				text : '详情',
				iconCls : 'monitor_search',
				handler : function(grid, rowIndex) {
					var coatid = grid.getStore().data.items[rowIndex].data.iid;
		            var cata = 0; //record.get('cata');
		            forwardruninfo2White(coatid,cata);
				}
			},{
				text : '导出',
				iconCls : 'monitor_export',
				handler : function(grid, rowIndex) {
					var coatid = grid.getStore().data.items[rowIndex].data.iid;
		            var cata = 0;  
		            resultExport2White(coatid,cata);
				}
			}]
	}];

    scriptmonitor_storeWhite.on('beforeload', function(store, options) {
        var new_params = {
        		scriptName:search_formWhite.getForm().findField("scriptName").getValue(),
            	    	flowId:flowIdWhite,
            	    	agentIp:agentIp.getValue(),
            //	    	scriptName:search_formWhite.getForm().findField("scriptName").getValue(),
            //	    	startUser:search_formWhite.getForm().findField("startUser").getValue(),
            state: stateCb.getValue(),
            cata: 0,
            ifrom: 3,
            isWhite:1,
            //search_formWhite.getForm().findField("cata").getValue(),
            startTime: startTime.getValue(),
            endTime: endTime.getValue(),
            forScriptFlow: forScriptFlowWhite
        };

        Ext.apply(scriptmonitor_storeWhite.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitor_storeWhite,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true
    });

    var scriptmonitor_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	autoScroll: true,
        store: scriptmonitor_storeWhite,
        cls:'customize_panel_back',
        border: false,
        columnLines: true,
        padding : grid_space,
        columns: scriptmonitor_columns,
        bbar: pageBar
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptcoatmonitorWhite_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_formWhite, scriptmonitor_grid]
    });

    function clearQueryWhere() {
    	search_formWhite.getForm().findField("scriptName").setValue('');
    	agentIp.setValue('');
        //    	search_formWhite.getForm().findField("scriptName").setValue(''),
        //    	search_formWhite.getForm().findField("startUser").setValue(''),
    	stateCb.setValue("-1");
        //    	search_formWhite.getForm().findField("cata").setValue("-1"),
        startTime.setValue('');
        endTime.setValue('');
    }

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    function forwardruninfo2White(coatid, flag) {
		var scriptName=search_formWhite.getForm().findField("scriptName").getValue();
		var state =search_formWhite.getForm().findField("state").getValue();
        var agentIp =search_formWhite.getForm().findField("agentIp").getValue();
        var startTime = search_formWhite.getForm().findField("startTime").getRawValue();
		var endTime =search_formWhite.getForm().findField("endTime").getRawValue();
	    contentPanel.getLoader().load({
	        url: "forwardscriptserverForFlowForWhite.do",
	        scripts: true,
	        params: {
	            flowId:flowIdWhite,
	            coatid: coatid,
	            forScriptFlow: forScriptFlowWhite,
	            flag: 1,
	            filter_scriptName:scriptName,
				filter_state:state,
				filter_startTime:startTime,
				filter_Ip:agentIp,
				filter_endTime:endTime,
				filter_serviceName:filter_endTimeWhite,
				filter_serviceState:filter_endTimeWhite,
				filter_serviceStartTime:filter_serviceStartTimeWhite,
				filter_serviceEndTime:filter_serviceEndTimeWhite
	        }
	    });
	}
	function resultExport2White(coatid, flag) {
	    window.location.href = 'exportCoatWhiteResult.do?coatId=' + coatid + '&flag=' + 1;
	}
});