var rowExpanderLog3ForToolMonitor;
var scriptmonitorinfoins_store3ForToolMonitor;
var scriptmonitorinfoins_grid3ForToolMonitor;
var interV3ForToolMonitor = 10;
var interPV3ForToolMonitor = 20;
var lastId3ForToolMonitor;
var lastRowIndex3ForToolMonitor;
var lastrequestId3ForToolMonitor;
var lastiip3ForToolMonitor;
var lastiport3ForToolMonitor;
var flag3ForToolMonitor = 0; // 0:测试     1:生成
Ext.onReady(function() {
    destroyRubbish();

    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }, {name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'}]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        listeners: {
            select: function(me, record, index, eOpts) { // 选择复选框事件
            	flowMesshis3ForToolMonitor(record.data.iid, index);
            },
            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
            	flowMesshis3ForToolMonitor(record.data.iid, index);
            }
        }
    });
    scriptmonitorinfoins_store3ForToolMonitor = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flag3ForToolMonitor + '&coatid=' + coatid3ForToolMonitor,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
            	flowMesshisRefresh3ForToolMonitor(lastId3ForToolMonitor, lastRowIndex3ForToolMonitor);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatid3ForToolMonitor
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime);
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },{ text: '系统名称',  dataIndex: 'sysName',hidden: !CMDBflag,width:80},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,width:80},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    }, { text: '计算机名',  dataIndex: 'hostName',width:80},
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 200,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state');
            var zoomStr = "";
            if (isWin3ForToolMonitor != 1) {
                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetail3ForToolMonitor(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
            }
            zoomStr = '';
            if (state == '30' || state == '40' || state == '50') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshis3ForToolMonitor(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServer3ForToolMonitor(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="skipScriptServer3ForToolMonitor(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else if (state == '-1' || state == '1') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshis3ForToolMonitor(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServer3ForToolMonitor(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshis3ForToolMonitor(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
            }
        }
    }];

    rowExpanderLog3ForToolMonitor = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForTestExec{iid}">', '<pre  onselectstart="return true" id="steptextareahisForTestExec{iid}"  class="monitor_desc"></pre>', '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span">自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshId3ForToolMonitor" name="rowFreshId3ForToolMonitor" >&nbsp;秒</span>', '&nbsp;&nbsp;&nbsp;<input type="button" value="刷新" onclick="loadShelloutputhis3ForToolMonitor({iid},\'{agentIp}\',{agentPort})" class="Common_Btn">', '&nbsp;&nbsp;&nbsp;<input type="button" value="终止" onclick="scriptServerStop3ForToolMonitor({iid},{state})" class="Common_Btn">', '</div>']
    });
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        name: "pageFreshTime",
        value: interPV3ForToolMonitor
    });
    

    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_grid3ForToolMonitor.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right',
        value: serviceName3ForToolMonitor
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserFullName3ForToolMonitor
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: startTime3ForToolMonitor
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: endTime3ForToolMonitor
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls:'customize_gray_back',
        cls:'sc_tlbar_height',
        dockedItems : [{
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back', 
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back', 
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    var oam_act_form = Ext.create('Ext.form.Panel', {
        frame : true,
        border : false,
        bodyCls:'fm-spinner',
        layout : {
            type : 'hbox',
            align : 'middle'
        },
        defaults : {
            anchor : '100%'
        },
        items : [
            {
                xtype: "label",
                text: "自动刷新"
            },pageFreshTime , {
                xtype : 'label',
                text : '    秒'
            } ]
    });
    scriptmonitorinfoins_grid3ForToolMonitor = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	store: scriptmonitorinfoins_store3ForToolMonitor,
    	autoScroll: true,
        border: false,
        cls:'customize_panel_back',
        padding : grid_space,
        ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        columnLines: true,
        ipageItems:[ oam_act_form,
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '刷  新',
                listeners: {
                    click: function() {
                        if (refreshObjForToolMonitor) {
                            clearInterval(refreshObjForToolMonitor);
                        }
                        refreshPage();
                        var interValue = pageFreshTime.getValue();
                        interPV3ForToolMonitor = interValue;
                        if (interPV3ForToolMonitor < 20) {
                            interPV3ForToolMonitor = 20;
                        }
                        refreshObjForToolMonitor = setInterval(refreshPage, interPV3ForToolMonitor * 1000);
                    }
                }
            }],
        columns: scriptmonitorinfoins_columns,
        // bbar: pageBar,
        selModel: selModel,
        plugins: [rowExpanderLog3ForToolMonitor],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                return 'norowexpandblah';
            }
        }
    });

    scriptmonitorinfoins_grid3ForToolMonitor.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
    	interV3ForToolMonitor = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshId3ForToolMonitor').innerText = interV3ForToolMonitor;
        } else {
            document.getElementById('rowFreshId3ForToolMonitor').innerHTML = interV3ForToolMonitor;
        }
        loadShelloutputhis3ForToolMonitor(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
    });
    scriptmonitorinfoins_grid3ForToolMonitor.view.on('collapsebody', function(rowNode, record, expandRow, eOpts) {
    	lastId3ForToolMonitor = 0;
    	lastRowIndex3ForToolMonitor = 0;
        if (refreshObjShellOutputForToolMonitor) {
            clearInterval(refreshObjShellOutputForToolMonitor);
        }
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoinsForToolMonitor_div",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: true,
        items: [info_form, scriptmonitorinfoins_grid3ForToolMonitor]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

    function refreshPage() {
    	var REG_BODY = /<body[^>]*>([\s\S]*)<\/body>/;
    	function getBody(content){
            var result = REG_BODY.exec(content);
            if(result && result.length === 2)
                return result[1];
            return content;
        }
    	if($(getBody(contentPanel.getLoader().target.html)).attr('id')=='switchruninfoinsForToolMonitor_div') {
    		scriptmonitorinfoins_store3ForToolMonitor.reload();
    	}
    }

    if (refreshObjForToolMonitor) {
        clearInterval(refreshObjForToolMonitor);
    }
    refreshObjForToolMonitor = setInterval(refreshPage, interPV3ForToolMonitor * 1000);

});

function flowMesshis3ForToolMonitor(iruninfoinsid, rowIndex) {
	lastId3ForToolMonitor = iruninfoinsid;
	lastRowIndex3ForToolMonitor = rowIndex;
    var record = scriptmonitorinfoins_store3ForToolMonitor.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForToolMonitor.getRange(0, scriptmonitorinfoins_store3ForToolMonitor.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLog3ForToolMonitor.recordsExpanded[records[i].internalId]) {
        	rowExpanderLog3ForToolMonitor.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_store3ForToolMonitor.getAt(rowIndex);
     rowExpanderLog3ForToolMonitor.toggleRow(rowIndex, record);
}
function loggerDetail3ForToolMonitor(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForToolMonitor) {
        clearInterval(refreshObjShellOutputForToolMonitor);
    }
    if (refreshObjForToolMonitor) {
        if (refreshObjShellOutputForToolMonitor) {
            clearInterval(refreshObjShellOutputForToolMonitor);
        }
        clearInterval(refreshObjForToolMonitor);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
            coatId: coatid3ForToolMonitor,
            flag: flag3ForToolMonitor
        }
    });
}

function flowMesshisRefresh3ForToolMonitor(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    var record = scriptmonitorinfoins_store3ForToolMonitor.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForToolMonitor.getRange(0, scriptmonitorinfoins_store3ForToolMonitor.getCount());
    var rowFreshValue = document.getElementById('rowFreshId3ForToolMonitor').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForToolMonitor = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForToolMonitor').innerText = interV3ForToolMonitor;
    } else {
        document.getElementById('rowFreshId3ForToolMonitor').innerHTML = interV3ForToolMonitor;
    }

    rowExpanderLog3ForToolMonitor.toggleRow(lastRowIndex3ForToolMonitor, records[lastRowIndex3ForToolMonitor]);
    if(refreshObjShellOutputForToolMonitor) {
    	clearInterval(refreshObjShellOutputForToolMonitor);
    }
    refreshObjShellOutputForToolMonitor = setInterval(function() {
    	loadShelloutputhisInfo3ForToolMonitor(lastrequestId3ForToolMonitor, lastiip3ForToolMonitor, lastiport3ForToolMonitor);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhis3ForToolMonitor(requestId, iip, iport) {
	lastrequestId3ForToolMonitor = requestId;
	lastiip3ForToolMonitor = iip;
	lastiport3ForToolMonitor = iport;
    if (refreshObjShellOutputForToolMonitor) {
        clearInterval(refreshObjShellOutputForToolMonitor);
    }
    var rowFreshValue = document.getElementById('rowFreshId3ForToolMonitor').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForToolMonitor = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForToolMonitor').innerText = interV3ForToolMonitor;
    } else {
        document.getElementById('rowFreshId3ForToolMonitor').innerHTML = interV3ForToolMonitor;
    }
    loadShelloutputhisInfo3ForToolMonitor(requestId, iip, iport);
    refreshObjShellOutputForToolMonitor = setInterval(function() {
    	loadShelloutputhisInfo3ForToolMonitor(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfo3ForToolMonitor(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForTestExec' + requestId;

    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: 0
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStop3ForToolMonitor(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
                Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_store3ForToolMonitor.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForToolMonitor.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServer3ForToolMonitor(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForToolMonitor.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServer3ForToolMonitor(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForToolMonitor.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    });
}
