var scriptmonitor_storeFileFile;

Ext.onReady(function() {
    destroyRubbish();

    /*Ext.define('startUserModel', {
		extend : 'Ext.data.Model',
		fields : [{
		  name : 'iid',
		  type : 'string'
		}, {
		  name : 'iusername',
		  type : 'string'
		}]
		  
	});*/

    var stateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-2",
            "name": "全部"
        },
        {
            "id": "101",
            "name": "完成"
        },
        {
            "id": "20",
            "name": "正常完成"
        },
        {
            "id": "40",
            "name": "异常完成"
        },
        {
            "id": "102",
            "name": "运行"
        },
        {
            "id": "10",
            "name": "正常运行"
        },
        {
            "id": "50",
            "name": "异常运行"
        },
        {
            "id": "60",
            "name": "终止"
        }]
    });
    /*var cataStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
		        {"id":"-1", "name":"全部"},
		        {"id":"0", "name":"测试"},
		        {"id":"1", "name":"生产"}
		        ]
	});*/

    /*var startUserStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'startUserModel',
		proxy : {
			type : 'ajax',
			url : 'getStartUser.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
	    }
	});*/

    var serviceName = new Ext.form.TextField({
        name: 'serviceName',
        fieldLabel: '服务名称',
        labelWidth: 70,
        width: '20%',
        labelAlign: 'right'
    });

    var stateCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'state',
        padding: '5',
        labelWidth: 70,
        queryMode: 'local',
        fieldLabel: '执行状态',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择状态--',
        store: stateStore,
        width: '15%',
        labelAlign: 'right',
        listeners: {
            afterRender: function(combo) {
                combo.setValue(stateStore.getAt(0).data.id);
            }
        }
    });

    var startTime = new Ext.form.field.Date({
        name: 'startTime',
        fieldLabel: '开始时间',
        labelWidth: 70,
        padding: '5',
        width: '22%',
        labelAlign: 'right',
        format: 'Y-m-d H:i:s'
    });

    var endTime = new Ext.form.field.Date({
        name: 'endTime',
        fieldLabel: '结束时间',
        labelWidth: 70,
        padding: '5',
        width: '22%',
        labelAlign: 'right',
        format: 'Y-m-d H:i:s'
    });

    var search_form = Ext.create('Ext.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [serviceName, stateCb, startTime, endTime, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            }]
        }]
    });

    Ext.define('scriptFlowMonitorData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'flowName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'cata',
            type: 'int'
        },
        {
            name: 'startUser',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'runNums',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }]
    });

    scriptflowmonitor_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptFlowMonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptFlowList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptflowmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) { // 5 is 忽略
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">初始化</span>';
            }
            return backValue;
        }
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        flex: 1
    },
    {
        text: '作业名称',
        dataIndex: 'flowName',
        flex: 1,
        hidden: true
    },
    {
        text: '启动人',
        dataIndex: 'startUser',
        width: 80,
        hidden: true
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 150
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 150
    },
    //					                { text: '服务器个数',  dataIndex: 'serverNum',width:100},
    {
        text: '运行时长(秒)',
        dataIndex: 'runTime',
        width: 100
    },
    /*					                { text: '类别',  dataIndex: 'cata',width:50,renderer:function(value,p,record){
					                	var backValue = "";
					                	if(value==0){
					                		backValue = "测试";
					                	}else if(value==1){
					                		backValue = "生产";
					                	}
					                	return backValue;
					                }},*/
    {
        text: '操作',
        dataIndex: 'sysOperation',
        width: 160,
        align: 'center',
        renderer: function(value, p, record) {
            var flowid = record.get('iid');
            var cata = record.get('cata');
            var serviceName = record.get('serviceName');
            var forwardFunction = 'forwardruninfo';
            if (serviceName == 'IDEAL_Aa_文件比对_aA_IDEAL') {
                forwardFunction = 'forwardFileResult';
            }
            //					                	
            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="' + forwardFunction + '(' + flowid + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' +
            //'<a href="javascript:void(0)" onclick="scriptCoatStop('+flowid+', '+ cata +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_termination"></img>终止</a>'+
            '</span>';
        }
    }];

    scriptflowmonitor_store.on('beforeload', function(store, options) {
        var new_params = {
            serviceName: serviceName.getValue(),
            state: stateCb.getValue(),
            cata: cata,
            startTime: startTime.getValue(),
            endTime: endTime.getValue(),
            flowType: flowType
        };

        Ext.apply(scriptflowmonitor_store.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: scriptflowmonitor_store,
        dock: 'bottom',
        displayInfo: true
    });

    var scriptflowmonitor_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: scriptflowmonitor_store,
        border: false,
        columnLines: true,
        columns: scriptflowmonitor_columns,
        bbar: pageBar
        /*,
	    viewConfig:{
			getRowClass:function(record,rowIndex,rowParams,arriveStore){
				var cls = '';
				if(record.data.state==10){
					cls = 'row_Blue';
				}else if(record.data.state==20){
					cls = 'row_Green';
				}else if(record.data.state==30){
					cls = 'row_Red';
				}else if(record.data.state==40){
					cls = 'row_Red';
				}else if(record.data.state==50){
					cls = 'row_Red';
				}else if(record.data.state==60){
					cls = 'row_Gray';
				} else {
					cls = 'row_Gray';
				}
				return cls; 
			}
		}*/
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptflowmonitor_areaindexFile",
	    layout: 'border',
	    width : contentPanel.getWidth(),
	    height :contentPanel.getHeight() - modelHeigth,
	    border : false,
	    items: [search_form, scriptflowmonitor_grid]
    });

    function clearQueryWhere() {
        serviceName.setValue(''),
        stateCb.setValue("102"),
        startTime.setValue(''),
        endTime.setValue('');
    }

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
});

function forwardruninfo(flowid, flag) {
    contentPanel.getLoader().load({
        url: "forwardscriptcoat.do",
        scripts: true,
        params: {
            flowId: flowid,
            flag: flag
        }
    });
}
function forwardFileResult(flowid, flag) {
    contentPanel.getLoader().load({
        url: "filecompare.do",
        scripts: true,
        params: {
            flowid: flowid,
            flag: flag
        }
    });
}

function scriptCoatStop(coatid, flag) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
        if (btn == 'yes') {
            Ext.Ajax.request({
                url: 'scriptCoatStop.do',
                method: 'POST',
                params: {
                    coatid: coatid,
                    flag: flag
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitor_storeFile.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })

}