<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<%-- <link rel="stylesheet"
	href="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/abnormal.css" /> --%>
<script type="text/javascript">
	var coatId = "<%=request.getParameter("coatId")%>";

	var out = "";
	function getData() {
		Ext.Ajax.request({
			url : 'dbaassearchAnalyzeResultByCoatId.do',
			params : {
				coatId : coatId,
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				out = msg.message;
				out = out.replace(/\r\n/g,"<br>")
				out = out.replace(/\n/g,"<br>");

				
				$('#dbaaspageoutputid').html(out);
			},
			failure : function(response, opts) {
			}
		});
		
	
	}
	
	getData();

</script>
</head>
<body>
	<div style="overflow-y:auto; overflow-x:auto; width:800px; height:450px;"  id="dbaaspageoutputid"></div>

</body>
</html>