<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
<%-- var flowId = <%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>;
	var flag = <%=request.getParameter("flag")==null?"":request.getParameter("flag")%>; --%>
	
	var flowIdWhite = '<%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>';
	var forScriptFlowWhite = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	var filter_scriptNameWhite = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateWhite  = '<%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>';
	var filter_startTimeWhite ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeWhite = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameWhite = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateWhite  = '<%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>';
	var filter_serviceStartTimeWhite ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeWhite = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var sessionIdForScriptCoatWhite = '<%=request.getSession().getId()%>';
    var filter_IpWhite = '<%=request.getParameter("filter_Ip")==null?"":request.getParameter("filter_Ip")%>';

    var editingChosedAgentIds = '<%=request.getAttribute("editingChosedAgentIds")==null?"":request.getAttribute("editingChosedAgentIds")%>';
 
	//白名单执行跳转带过来的查询agent条件，返回时使用  start
	var hostNameForWhite = '<%=request.getAttribute("hostName")==null?"":request.getAttribute("hostName")%>';
	var ipForWhite = '<%=request.getAttribute("ip")==null?"":request.getAttribute("ip")%>';
	var sysAdminForWhite = '<%=request.getAttribute("sysAdmin")==null?"":request.getAttribute("sysAdmin")%>';
	var centerNameForWhite ='<%=request.getAttribute("centerName")==null?"":request.getAttribute("centerName")%>';
	var systemInfoForWhite ='<%=request.getAttribute("systemInfo")==null?"":request.getAttribute("systemInfo")%>';
	var middlewareTypeForWhite ='<%=request.getAttribute("middlewareType")==null?"":request.getAttribute("middlewareType")%>';
	var osTypeForWhite ='<%=request.getAttribute("osType")==null?"":request.getAttribute("osType")%>';
	var dbTypeForWhite ='<%=request.getAttribute("dbType")==null?"":request.getAttribute("dbType")%>';
	//白名单执行跳转带过来的查询agent条件，返回时使用  end
	
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptCoatForWhite.js"></script>
</head>
<body>
<div id="scriptcoatmonitorWhite_area" style="width: 100%;height: 100%">
</div>
</body>
</html>