<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<html>
<head>
<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
</script>
 <script type="text/javascript">
 <%--var flowType=<%=request.getAttribute("flowType")%>==null?1:<%=request.getAttribute("flowType")%>;
var cata=<%=request.getParameter("cata")%>==null?0:<%=request.getParameter("cata")%>;--%>
    var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
</script> 
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/index.js"></script>
</head>
<body>
<div id="scriptflowmonitor_area" style="width: 100%;height: 100%">
</div>
</body>
</html>