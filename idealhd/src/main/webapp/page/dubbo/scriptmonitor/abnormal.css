@charset "utf-8";
/* CSS Document */
body{
	padding:0;
	margin:0;
	background-color:#f7f8fa;
	font-family:Microsoft Yahei;
	color:#585a69;
	font-size:14px;
	}
.abnormal_center{
	width:1240px; 
	margin:0 auto
	}
.abnormal_body{
	width:1240px;
	height:auto;
	float:left;
	}
.headline{
	width:100%;
	height:65px;
	line-height:65px;
	background-color:red;
	background: url(../images/dbaas/headline_icon.png) no-repeat 20px;
	}
.headline h1{
	font-size:22px;
	color:#303340;
	padding:0;
	margin:0 0 0 55px;
	}
.table_content{
	background-color:#fff;
	height:auto;
	width:1200px;
	padding:20px;
	float:left;
	}
.table_content h2{
	font-size:16px;
	padding:0;
	margin:0;
	color:#303340
	}
.table_query{
	width:250px;
	margin:20px 0 0 0;
	padding:20px;
	height:680px;
	border:1px solid #dddfeb;
	float:left;
	}
.table_query ul{
	padding:0;
	margin:0;
	list-style:none;
	}
.table_query ul li:nth-child(odd){
	height:36px;
	line-height:36px;
	}
.table_query ul li:nth-child(even){
	}
.pull_down{
	width:250px;
	line-height:36px;
	height:36px;
	color:#3d404d;
	border:#dddfed 1px solid;
	border-radius:2px;
	box-sizing:border-box;
	appearance:none;
  	-moz-appearance:none;
 	-webkit-appearance:none;
  	background:url(../images/dbaas/select_icon.png) no-repeat scroll right center #f7f8fa; 
	padding-right:10px;
	padding-left:10px;
	}
.pull_down::-ms-expand { 
	display: none;
	}
.pull_down option{ 
	background:#ffffff;
    color:#303340;
	} 
.pull_down option:hover{
	background:#ffffff; 
    color:#303340; 
	} 
.pull_down option:checked{ 
    background:#ffffff; 
	}
.text_field{
	width:100%;
	height:290px;
	background-color:#f7f8fa;
	border:1px solid #dddfeb;
	}
.try_btn{
	width:76px;
	height:34px;
	line-height:34px;
	text-align:center;
	border:1px solid #3d55f6;
	border-radius:2px;
	background-color:#5168fc;
	color:#fff;
	font-size:12px;
	margin:20px auto;
	cursor:pointer
	}
.try_btn:hover{
	background-color:#6c8bff;
  	border:1px solid #3d55f6;
  	color:#fff;
	}
.right_chart{
	width:888px;
	float:left;
	margin:20px 0 0 20px;
	}
.pie_chart{
	width:886px;
	border:1px solid #dddfeb;
	height:306px;
	}
.table_chart{
	margin:10px 0 0 0;
	width:886px;
	height:405px;
	display:block;
	overflow:auto;
	}
.table_chart table{
	border-collapse:collapse;
	display:block;
	width:880px;
	border-left:1px solid #dddfeb;
	}
.table_chart table thead {
	display:block;
	}
.table_chart table thead tr{
	height:34px;
	background-color:#f7f8fa;
	}
.table_chart table thead tr th{
	border-bottom:1px solid #dddfeb;
	border-top:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	padding:0 0 0 5px;
	}
.table_chart table .table_tbody{
	display:block;
	}
.table_chart table .table_tbody tr{
	height:34px;
	}
.table_chart table .table_tbody tr td{
	border-right:1px solid #dddfeb;
	border-bottom:1px solid #dddfeb;
	text-align:center;
	padding:0 0 0 5px;
	}
.table_chart table .table_tbody tr:nth-child(odd){
	background-color:#fff;
	}
.table_chart table .table_tbody tr:nth-child(even){
	background-color:#f7f8fa;
	}
.normal{
	color:#64b548;
	}
.abnormal{
	color:#f63508;
	}
.wt01{
	display:block;
	width:80px;
	}
.wt02{
	display:block;
	width:200px;
	word-break:break-all;
	text-align:left;
	}
	
.exception_detail{
	width:1160px;
	float:right;
	margin:0 20px 0 15px;
	}
.triangle_border_up{
    width:0;
    height:0;
    border-width:0 10px 10px;
    border-style:solid;
    border-color:transparent transparent #6377fc;
	position:relative;
	left:825px;
	top:1px;
}
.detail_header{
	background-color:#6377fc;
	border-radius:2px 2px 0 0;
	height:32px;
	line-height:32px;
	color:#fff;
	font-size:16px;
	font-weight:bold;
	background-color:#6377fc;
	width:100%;
	text-align:center
	}
.close_icon{
	background-image:url(../images/dbaas/close_icon.png);
	width:20px;
	height:20px;
	display:block;
	float:right;
	cursor:pointer;
	margin:6px 15px 0 0;
	}
.detail_table{
	width:100%;
	height:200px;
	display:block;
	overflow:auto;
	}
.detail_table table{
	border-collapse:collapse;
	/*display:block;*/
	width:100%;
	border-left:1px solid #dddfeb;
	}
.detail_table table thead {
	/*display:block;*/
	}
.detail_table table thead tr{
	height:34px;
	background-color:#ffffff;
	}
.detail_table table thead tr th{
	border-bottom:1px solid #6377fc;
	border-top:1px solid #6377fc;
	border-right:1px solid #6377fc;
	padding:0 0 0 5px;
	color:#5168fc;
	}
.detail_table table .detail_tbody{
	/*display:block;*/
	}
.detail_table table .detail_tbody tr{
	height:34px;
	}
.detail_table table .detail_tbody tr td{
	border-right:1px solid #6377fc;
	border-bottom:1px solid #6377fc;
	text-align:center;
	padding:0 0 0 5px;
	color:#303340;
	}
.detail_table table .detail_tbody tr:nth-child(odd){
	background-color:#f1f5ff;
	}
.detail_table table .detail_tbody tr:nth-child(even){
	background-color:#f1f5ff;
	}
.d_wt01{
	display:block;
	width:86px;
	}
.d_wt02{
	display:block;
	width:145px;
	word-break:break-all;
	text-align:left;
	}
.ab_table_content{
	background-color: #fff;
	height: auto;
	width: 1200px;
	padding: 20px;
	margin: 0 auto;
}
.ab_table_chart{
	width:100%;
	height:500px;
	overflow:auto;
	}
.ab_table_chart table{
	border-collapse:collapse;
	display:block;
	border-left:1px solid #dddfeb;
	}
.ab_table_chart table thead {
	display:block;
	}
.ab_table_chart table thead tr{
	height:34px;
	background-color:#f7f8fa;
	}
.ab_table_chart table thead tr th{
	border-bottom:1px solid #dddfeb;
	border-top:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	padding:0 0 0 5px;
	}
.ab_table_chart table .ab_table_tbody{
	display:block;
	}
.ab_table_chart table .ab_table_tbody tr{
	height:34px;
	}
.ab_table_chart table .ab_table_tbody tr td{
	border-right:1px solid #dddfeb;
	border-bottom:1px solid #dddfeb;
	text-align:center;
	padding:0 0 0 5px;
	}
.ab_table_chart table .ab_table_tbody tr:nth-child(odd){
	background-color:#fff;
	}
.ab_table_chart table .ab_table_tbody tr:nth-child(even){
	background-color:#f7f8fa;
	}
.ab_normal{
	color:#64b548;
	}
.ab_abnormal{
	color:#f63508;
	}
.ab_wt01{
	display:block;
	width:80px;
	}
.ab_wt02{
	display:block;
	width:200px;
	word-break:break-all;
	text-align:left;
	}
.page_table_content{
	background-color:#fff;
	height:auto;
	width:1200px;
	padding:20px;
	float:left;
	}
.page_table_content h2{
	font-size:16px;
	padding:0;
	margin:0;
	color:#303340
	}
.page_chart{
	width:1200px;
	float:left;
	margin:0 0 0 0;
	}
.page_table_chart{
	margin:0 0 0 0;
	width:100%;
	height:405px;
	}
.page_table_breadth{
	width:1200px;
	border-left:1px solid #dddfeb;
	}
.page_table_chart table thead {
	width:100%;
	}
.page_table_chart table thead tr{
	height:34px;
	background-color:#f7f8fa;
	}
.page_table_chart table thead tr th{
	border-bottom:1px solid #dddfeb;
	border-top:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	padding:0 0 0 5px;
	text-align:left;
	}
.page_table_chart table .table_tbody{
	}
.page_table_chart table .table_tbody tr{
	height:34px;
	}
.page_table_chart table .table_tbody tr td{
	border-right:1px solid #dddfeb;
	border-bottom:1px solid #dddfeb;
	text-align:center;
	padding:0 0 0 5px;
	text-align:left;
	}
.page_table_chart table .table_tbody tr:nth-child(odd){
	background-color:#fff;
	}
.page_table_chart table .table_tbody tr:nth-child(even){
	background-color:#f7f8fa;
	}
	
.suspension_frame{
	cursor:pointer;
	position:absolute;
	line-height:0;
}
.suspension_frame:hover .frame_property{
	display:block;
	transform-origin: 100% 0%;
	-webkit-animation: fadeIn 0.3s ease-in-out;
	animation: fadeIn 0.3s ease-in-out;

}
.suspension_frame .frame_property{
	display: none;
	text-align: left;
	padding: 0;
	width:0;
	position:relative;
	border-radius: 0px;
	right: 0;
	color: #FFF;
	font-size: 13px;
	line-height:2;
	z-index:1500;
}
.frame_position{
	margin:19px 0 0 360px;
	}
.suspension_frame .frame_property:before{
	position: absolute;
	content: '';
	width:0;
	height: 0;
	opacity:0.7;
}

.suspension_frame .frame_property:after{
	width:100%;
	height:40px;
	content:'';
	position: absolute;
	top:0px;
	left:0;
}

@-webkit-keyframes fadeIn {
	0% { 
		opacity:0; 
		transform: scale(0.6);
	}

	100% {
		opacity:100%;
		transform: scale(1);
	}
}

@keyframes fadeIn {
	0% { opacity:0; }
	100% { opacity:100%; }
}