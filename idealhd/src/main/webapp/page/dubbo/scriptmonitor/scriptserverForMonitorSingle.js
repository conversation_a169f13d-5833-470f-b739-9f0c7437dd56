var rowExpanderLog3ForMonitorSingle;
var scriptmonitorinfoins_store3ForMonitorSingle;
var scriptmonitorinfoins_grid3ForMonitorSingle;
var interV3ForMonitorSingle = 10;
var interPV3ForMonitorSingle = 20;
var lastId3ForMonitorSingle;
var lastRowIndex3ForMonitorSingle;
var lastrequestId3ForMonitorSingle;
var lastiip3ForMonitorSingle;
var lastiport3ForMonitorSingle;
var flag3ForMonitorSingle = flagTypeMonitorSingle; // 0:测试     1:生成
Ext.onReady(function() {
    destroyRubbish();
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }, {name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'}]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
//        listeners: {
//            select: function(me, record, index, eOpts) { // 选择复选框事件
//            	flowMesshis3ForMonitorSingle(record.data.iid, index);
//            },
//            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
//            	flowMesshis3ForMonitorSingle(record.data.iid, index);
//            }
//        }
    });
    scriptmonitorinfoins_store3ForMonitorSingle = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flag3ForMonitorSingle + '&coatid=' + coatid3ForMonitorSingle,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
            	flowMesshisRefresh3ForMonitorSingle(lastId3ForMonitorSingle, lastRowIndex3ForMonitorSingle);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatid3ForMonitorSingle
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime);
                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },{ text: '系统名称',  dataIndex: 'sysName',hidden: !CMDBflag,width:80},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,width:80},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    }, { text: '计算机名',  dataIndex: 'hostName',width:180},
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 200,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state');
            var zoomStr = "";
            if (isWin3ForMonitorSingle != 1) {
                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetail3ForMonitorSingle(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
            }
            zoomStr = '';
            if (state == '30' || state == '40' || state == '50') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openActWindowForMonitorSingle(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServer3ForMonitorSingle(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="skipScriptServer3ForMonitorSingle(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else if (state == '-1' || state == '1') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="scriptServerStop3ForMonitorSingle(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_termination"></img>&nbsp;终止</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServer3ForMonitorSingle(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openActWindowForMonitorSingle(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
            }
        }
    }];

    rowExpanderLog3ForMonitorSingle = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForTestExec{iid}">', '<pre  onselectstart="return true" id="steptextareahisForTestExec{iid}"  class="monitor_desc"></pre>', '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span">自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshId3ForMonitorSingle" name="rowFreshId3ForMonitorSingle" >&nbsp;秒</span>', '&nbsp;&nbsp;&nbsp;<input type="button" value="刷新" onclick="loadShelloutputhis3ForMonitorSingle({iid},\'{agentIp}\',{agentPort})" class="Common_Btn Monitor_Btn">', '&nbsp;&nbsp;&nbsp;<input type="button" value="终止" onclick="scriptServerStop3ForMonitorSingle({iid},{state})" class="Common_Btn Monitor_Btn">', '</div>']
    });
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        name: "pageFreshTime",
        value: interPV3ForMonitorSingle
    });
    
//    var pageBar = Ext.create('Ext.PagingToolbar', {
//        store: scriptmonitorinfoins_store3ForMonitorSingle,
//        dock: 'bottom',
//        displayInfo: true,
//        items: [{
//            xtype: "label",
//            text: "自动刷新"
//        },
//        pageFreshTime,
//        {
//            xtype: "label",
//            text: "  秒"
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            text: '刷新',
//            listeners: {
//                click: function() {
//                    if (refreshObjForTestExecMonitorSingle) {
//                        clearInterval(refreshObjForTestExecMonitorSingle);
//                    }
//                    refreshPage();
//                    var interValue = pageFreshTime.getValue();
//                    interPV3ForMonitorSingle = interValue;
//                    if (interPV3ForMonitorSingle < 20) {
//                    	interPV3ForMonitorSingle = 20;
//                    }
//                    refreshObjForTestExecMonitorSingle = setInterval(refreshPage, interPV3ForMonitorSingle * 1000);
//                }
//            }
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            text: '终止',
//            listeners: {
//                click: function() {
//                    var data = getCHKBoxIds();
//                    if (data.length == 0) {
//                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
//                        return;
//                    } else {
//                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
//                        function(button, text) {
//                            if (button == "yes") {
//                                if (data == '-1') {
//                                    Ext.Msg.alert('提示', '该步骤已经结束，无需终止!');
//                                    scriptmonitorinfoins_store3ForMonitorSingle.reload();
//                                    return;
//                                }
//                                Ext.MessageBox.wait("数据处理中...", "提示");
//                                Ext.Ajax.request({
//                                    url: 'scriptServiceShellKill.do',
//                                    params: {
//                                        flag: flag3ForMonitorSingle,
//                                        insIds: data
//                                    },
//                                    method: 'POST',
//                                    success: function(response, opts) {
//                                        var success = Ext.decode(response.responseText).success;
//                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
//                                        // 当后台数据同步成功时
//                                        if (success) {
//                                        	scriptmonitorinfoins_store3ForMonitorSingle.reload();
//                                        }
//                                    }
//                                });
//                            }
//                        });
//                    }
//                }
//            }
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            text: '返回',
//            hidden: true,
//            listeners: {
//                click: function() {
//                	returnBack3ForMonitorSingle();
//                }
//            },
//            handler : function() {
//            	 winStep.close();
//        	}
//           
//        }]
//    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_grid3ForMonitorSingle.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if ( state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right',
        value: serviceName3ForMonitorSingle
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserFullName3ForMonitorSingle
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: startTime3ForMonitorSingle
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: endTime3ForMonitorSingle
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        cls:'sc_tlbar_height',
        dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    var oam_act_form = Ext.create('Ext.form.Panel', {
		frame : true,
		border : false,
		bodyCls:'fm-spinner',
		layout : {
			type : 'hbox',
			align : 'middle'
		},
		defaults : {
			anchor : '100%'
		},
		items : [
				{
            xtype: "label",
            text: "自动刷新"
        },,pageFreshTime , {
					xtype : 'label',
					text : '    秒'
				} ]
	});
    
    scriptmonitorinfoins_grid3ForMonitorSingle = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	store: scriptmonitorinfoins_store3ForMonitorSingle,
    	forceFit: true,
		autoScroll:false,
    	//autoScroll: true,
        border: false,
        cls:'sc_tab_height',
        columnLines: true,
        columns: scriptmonitorinfoins_columns,
       // bbar: pageBar,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        ipageItems : [oam_act_form,
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '刷新',
            listeners: {
                click: function() {
                    if (refreshObjForTestExecMonitorSingle) {
                        clearInterval(refreshObjForTestExecMonitorSingle);
                    }
                    refreshPage();
                    var interValue = pageFreshTime.getValue();
                    interPV3ForMonitorSingle = interValue;
                    if (interPV3ForMonitorSingle < 20) {
                    	interPV3ForMonitorSingle = 20;
                    }
                    refreshObjForTestExecMonitorSingle = setInterval(refreshPage, interPV3ForMonitorSingle * 1000);
                }
            }
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '终止',
            listeners: {
                click: function() {
                    var data = getCHKBoxIds();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                        function(button, text) {
                            if (button == "yes") {
                                if (data == '-1') {
                                    Ext.Msg.alert('提示', '该步骤已经结束，无需终止!');
                                    scriptmonitorinfoins_store3ForMonitorSingle.reload();
                                    return;
                                }
                                Ext.MessageBox.wait("数据处理中...", "提示");
                                Ext.Ajax.request({
                                    url: 'scriptServiceShellKill.do',
                                    params: {
                                        flag: flag3ForMonitorSingle,
                                        insIds: data
                                    },
                                    method: 'POST',
                                    success: function(response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        // 当后台数据同步成功时
                                        if (success) {
                                        	scriptmonitorinfoins_store3ForMonitorSingle.reload();
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '返回',
            hidden: true,
            listeners: {
                click: function() {
                	returnBack3ForMonitorSingle();
                }
            },
            handler : function() {
            	 winStep.close();
        	}
           
        }],
        selModel: selModel,
        plugins: [rowExpanderLog3ForMonitorSingle],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                return 'norowexpandblah';
            }
        }
    });

    scriptmonitorinfoins_grid3ForMonitorSingle.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
    	interV3ForMonitorSingle = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshId3ForMonitorSingle').innerText = interV3ForMonitorSingle;
        } else {
            document.getElementById('rowFreshId3ForMonitorSingle').innerHTML = interV3ForMonitorSingle;
        }
        loadShelloutputhis3ForMonitorSingle(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
    });
    scriptmonitorinfoins_grid3ForMonitorSingle.view.on('collapsebody', function(rowNode, record, expandRow, eOpts) {
    	lastId3ForMonitorSingle = 0;
    	lastRowIndex3ForMonitorSingle = 0;
        if (refreshObjShellOutputForTestExecMonitorSingle) {
            clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
        }
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoinsForMonitorSingle_div",
        layout: 'border',
        width : contentPanel.getWidth()-3-135,
        height :contentPanel.getHeight() - 70,
        border: true,
        items: [info_form, scriptmonitorinfoins_grid3ForMonitorSingle]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - 70);
		mainPanel.setWidth (contentPanel.getWidth ()-3 );
    });

    function refreshPage() {
    	scriptmonitorinfoins_store3ForMonitorSingle.reload();
    }

    if (refreshObjForTestExecMonitorSingle) {
        clearInterval(refreshObjForTestExecMonitorSingle);
    }
    refreshObjForTestExecMonitorSingle = setInterval(refreshPage, interPV3ForMonitorSingle * 1000);

});

function flowMesshis3ForMonitorSingle(iruninfoinsid, rowIndex) {
	lastId3ForMonitorSingle = iruninfoinsid;
	lastRowIndex3ForMonitorSingle = rowIndex;
    var record = scriptmonitorinfoins_store3ForMonitorSingle.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForMonitorSingle.getRange(0, scriptmonitorinfoins_store3ForMonitorSingle.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLog3ForMonitorSingle.recordsExpanded[records[i].internalId]) {
        	rowExpanderLog3ForMonitorSingle.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_store3ForMonitorSingle.getAt(rowIndex);
     rowExpanderLog3ForMonitorSingle.toggleRow(rowIndex, record);
}

function openActWindowForMonitorSingle(requestId, state,agentIp,agentPort) {
	var runningWindow = null;
	var fp2 = null;
	var cmdStr = null;
	var surl = "getScriptExecOutput.do";
	var h = window.innerHeight || document.documentElement.clientHeight
			|| document.body.clientHeight;
	function getData(surl,state, requestId, agentIp, agentPort, cmdValue) {
		Ext.Ajax.request({
			url : surl,
			params : {
				requestId : requestId,
				agentIp : agentIp,
				agentPort : agentPort,
				flag:flag3ForMonitorSingle,
				input:cmdValue
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				var oldValue = Ext.getCmp('baseinfo_form_forflowtest').getForm().findField(
						"actOutPut").getValue();
				var output = msg.message;

				var json = {
					actName :serviceName3ForMonitorSingle+"-"+agentIp+"-"+agentPort ,
					actInsName : serviceName3ForMonitorSingle,
					actOutPut : output
				};
				Ext.getCmp('baseinfo_form_forflowtest').getForm().setValues(json);
				actName.getEl().dom.innerHTML='活动名：    <div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:80%;height:35px ;margin-bottom:10px"><pre>'+actName.getValue()+'</pre></div>';
				actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:80%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+actOutPut.getValue()+'</pre></div>';
			},
			failure : function(response, opts) {

			}

		});
	}
	var cmdStr = new Ext.form.TextField({
		anchor : '100%',
		labelWidth : 70,
		fieldLabel : 'CMD',
		disabled:(state==10)?false:true,
		listeners : {
			specialkey : function(textfield, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var cmdV = cmdStr.getValue();
					cmdStr.setValue("");
					getData(surl,state, requestId, agentIp, agentPort, cmdV);
				}
			}
		}
	});
	var actName= Ext.create ('Ext.form.field.Text',
			{
				fieldLabel : '活动名',
				labelWidth : 70,
				name : 'actName',
				margin :'10 0 5 0',
				anchor : '95%' // anchor width by percentage
			});
	var actOutPut = Ext.create ('Ext.form.field.TextArea',
	{
		fieldLabel : '活动日志',
		labelWidth : 70,
		grow : true,
		height : h-200,
		name : 'actOutPut',
		margin :'15 0 5 0',
		anchor : '95%'
	});
	var fp2 = new Ext.form.Panel({
		border : false,
		id : 'baseinfo_form_forflowtest',
		height : '100%',
		padding : 5,
		fieldDefaults : {
			labelWidth : 60,
			labelAlign : 'right'
		},
		defaultType : 'textfield',
		items : [actName, {
			fieldLabel : '实例名',
			labelWidth : 70,
			name : 'actInsName',
			hidden : true,
			anchor : '100%' // anchor width by percentage
		},actOutPut, cmdStr ]
	});

	if (runningWindow == undefined || !runningWindow.isVisible()) {
		runningWindow = Ext.create('Ext.window.Window', {
			title : serviceName3ForMonitorSingle,
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 850,
            maximizable : true,
			height : h - 95,
			items : [ fp2 ],
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'bottom',
				layout: {pack: 'center'},
				items : [
				 {
					text : '刷新',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						getData(surl,state, requestId, agentIp, agentPort, null);
						cmdStr.setValue("");
					}
				}, {
					text : '终止',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						scriptServerStop3ForMonitorSingle(requestId,state)
					}
				} ]
			} ],
			layout : 'fit'
		});
	}
	runningWindow.show();
	getData(surl,state, requestId, agentIp, agentPort, null);

		
	
}
function loggerDetail3ForMonitorSingle(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForTestExecMonitorSingle) {
        clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
    }
    if (refreshObjForTestExecMonitorSingle) {
        if (refreshObjShellOutputForTestExecMonitorSingle) {
            clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
        }
        clearInterval(refreshObjForTestExecMonitorSingle);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
            coatId: coatid3ForMonitorSingle,
            flag: flag3ForMonitorSingle
        }
    });
}

function flowMesshisRefresh3ForMonitorSingle(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    var record = scriptmonitorinfoins_store3ForMonitorSingle.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForMonitorSingle.getRange(0, scriptmonitorinfoins_store3ForMonitorSingle.getCount());
    var rowFreshValue = document.getElementById('rowFreshId3ForMonitorSingle').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForMonitorSingle = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForMonitorSingle').innerText = interV3ForMonitorSingle;
    } else {
        document.getElementById('rowFreshId3ForMonitorSingle').innerHTML = interV3ForMonitorSingle;
    }

    rowExpanderLog3ForMonitorSingle.toggleRow(lastRowIndex3ForMonitorSingle, records[lastRowIndex3ForMonitorSingle]);
//    refreshObjShellOutputForTestExecMonitorSingle = setInterval(function() {
//    	loadShelloutputhisInfo3ForMonitorSingle(lastrequestId3ForMonitorSingle, lastiip3ForMonitorSingle, lastiport3ForMonitorSingle);
//    },
//    rowFreshValue * 1000);
}

function loadShelloutputhis3ForMonitorSingle(requestId, iip, iport) {
	lastrequestId3ForMonitorSingle = requestId;
	lastiip3ForMonitorSingle = iip;
	lastiport3ForMonitorSingle = iport;
    if (refreshObjShellOutputForTestExecMonitorSingle) {
        clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
    }
    var rowFreshValue = document.getElementById('rowFreshId3ForMonitorSingle').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForMonitorSingle = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForMonitorSingle').innerText = interV3ForMonitorSingle;
    } else {
        document.getElementById('rowFreshId3ForMonitorSingle').innerHTML = interV3ForMonitorSingle;
    }
    loadShelloutputhisInfo3ForMonitorSingle(requestId, iip, iport);
//    refreshObjShellOutputForTestExecMonitorSingle = setInterval(function() {
//    	loadShelloutputhisInfo3ForMonitorSingle(requestId, iip, iport);
//    },
//    rowFreshValue * 1000);
}

function loadShelloutputhisInfo3ForMonitorSingle(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForTestExec' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: flag3ForMonitorSingle,
            input:null
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStop3ForMonitorSingle(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
            	Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_store3ForMonitorSingle.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag3ForMonitorSingle
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForMonitorSingle.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServer3ForMonitorSingle(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag3ForMonitorSingle
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForMonitorSingle.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServer3ForMonitorSingle(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag3ForMonitorSingle
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForMonitorSingle.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    });
}
function returnBack3ForMonitorSingle() {
	lastId3ForMonitorSingle = '';
    if (refreshObjShellOutputForTestExecMonitorSingle) {
        clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
    }
    if (refreshObjForTestExecMonitorSingle) {
        if (refreshObjShellOutputForTestExecMonitorSingle) {
            clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
        }
        clearInterval(refreshObjForTestExecMonitorSingle);
    }
    
}