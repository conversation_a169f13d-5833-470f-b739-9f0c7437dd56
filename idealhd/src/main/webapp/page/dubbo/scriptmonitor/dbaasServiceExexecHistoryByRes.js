var scriptmonitor_storeindexExecindexExec;
var search_form1ForExecForFlow;
var dbaasscriptflowmonitor_store
Ext
		.onReady(function() {

			destroyRubbish();

			var stateStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
					"id" : "-2",
					"name" : "全部"
				}, {
					"id" : "101",
					"name" : "完成"
				}, {
					"id" : "102",
					"name" : "运行"
				}, {
					"id" : "60",
					"name" : "终止"
				} ]
			});

			search_form1ForExecForFlow = Ext.create('Ext.form.Panel',{
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				// height:80,
				border : false,
				baseCls : 'customize_gray_back',
				dockedItems : [
						{
							xtype : 'toolbar',
							baseCls : 'customize_gray_back',
							border : false,
							dock : 'top',
							items : [
									{
										fieldLabel : '服务名称',
										labelAlign : 'right',
										labelWidth : 80,
										name : 'serviceName',
										width : '25%',
										xtype : 'textfield',
										value : "",
										listeners : {
											specialkey : function(field, e) {
												if (e.getKey() == e.ENTER) {
													pageBar.moveFirst();
												}
											}
										}
									},
									{
										fieldLabel : '服务执行状态',
										labelAlign : 'right',
										labelWidth : 100,
										name : 'state',
										width : '15%',
										displayField : 'name',
										valueField : 'id',
										store : stateStore,
										queryMode : 'local',
										listeners : {
											afterRender : function(combo) {
												combo.setValue(stateStore.getAt(0).data.id);
												
											},
											specialkey : function(field, e) {
												if (e.getKey() == e.ENTER) {
													pageBar.moveFirst();
												}
											}
										},
										xtype : 'combobox'
									},
									{
										xtype : 'button',
										text : '查询',
										cls : 'Common_Btn',
										handler : function() {
											pageBar.moveFirst();
										}
									}]
						} ]
			});

			Ext.define('scriptFlowMonitorData', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'iserviceid',
					type : 'string'
				}, {
					name : 'serviceName',
					type : 'string'
				}, {
					name : 'state',
					type : 'int'
				}, {
					name : 'startUser',
					type : 'string'
				}, {
					name : 'startTime',
					type : 'string'
				}, {
					name : 'resultType',
					type : 'int'
				}]
			});

			dbaasscriptflowmonitor_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 50,
				model : 'scriptFlowMonitorData',
				proxy : {
					type : 'ajax',
					url : 'getServiceExecuteHistoryByResource.do',
					timeout : 6000000,
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			var scriptflowmonitor_columns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '发起时间',
						dataIndex : 'startTime',
						width : 150
					},
					{
						text : '发起人',
						dataIndex : 'startUser',
						width : 180,
					},
					{
						text : '服务ID',
						dataIndex : 'iserviceid',
						minWidth : 100,
						flex : 1
					},
					{
						text : '服务名称',
						dataIndex : 'serviceName',
						minWidth : 100,
						flex : 1
					},
					{
						text : '服务执行状态',
						dataIndex : 'state',
						width : 120,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == 1) {
								backValue = '<span class="Run_Green State_Color">初始化</span>';
							} else if (value == 10 || value == 11) {
								backValue = '<span class="Run_Green State_Color">运行</span>';
							} else if (value == 20 || value == 5) { // 5 is 忽略
								backValue = "<span class='Complete_Green State_Color'>完成</span>"
							} else if (value == 30) {
								backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
							} else if (value == 40) {
								backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
							} else if (value == 50) {
								backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
							} else if (value == 60) {
								backValue = '<span class="Kill_red State_Color">已终止</span>';
							} else if (value == -1) {
								backValue = '<span class="Not_running State_Color">初始化</span>';
							}
							return backValue;
						}
					},
					{
						text : '分析结果',
						dataIndex : 'resultType',
						width : 120,
						renderer : function(value, p, record) {
							var backValue = value;
								if(value==1){
									backValue = '<span class="Run_Green State_Color">正常</span>';
								}else if(value==0){
									backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
								}else if(value==2){
									backValue = '暂无结果';
								}else if(value==-1){
									backValue = '无分析算法';
								}
								
							return backValue;
						}
					}
				];

			dbaasscriptflowmonitor_store.on('beforeload', function(store, options) {
				var new_params = {
					serviceName : search_form1ForExecForFlow.getForm()
							.findField("serviceName").getValue(),
					state : search_form1ForExecForFlow.getForm().findField(
							"state").getValue() ,
					resId : resId
//					startTime : search_form1ForExecForFlow.getForm().findField(
//							"startTime").getValue(),
//					endTime : search_form1ForExecForFlow.getForm().findField(
//							"endTime").getValue(),
//					flowType : flowTypeExec
				};

				Ext.apply(dbaasscriptflowmonitor_store.proxy.extraParams,new_params);
			});

			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
				store : dbaasscriptflowmonitor_store,
				dock : 'bottom',
				displayInfo : true
			});
			var scriptflowmonitor_grid = Ext.create('Ext.ux.ideal.grid.Panel',
					{
						region : 'center',
						store : dbaasscriptflowmonitor_store,
						selType : 'cellmodel',
						ipageBaseCls : Ext.baseCSSPrefix
								+ 'toolbar customize_toolbar',
						border : false,
						columnLines : true,
						cls : 'customize_panel_back',
						columns : scriptflowmonitor_columns
					});
			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "dbaasscriptflowmonitor_areaindexExec",
				border : false,
				layout : 'border',
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				items : [ search_form1ForExecForFlow, scriptflowmonitor_grid ]
			});
		  
			contentPanel.setTitle(contentPanel.title+'<a href="#" onclick="back()"><span>--执行历史-->返回</span></a>');

			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
			});
		});


function back() {
    destroyRubbish();
    contentPanel.getLoader().load({
        url : 'gotoNewResource.do',
        scripts : true
    });
}


