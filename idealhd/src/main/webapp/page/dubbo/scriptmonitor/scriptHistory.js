Ext.onReady(function() {
	var scriptmonitor_store;
	var search_form;
    destroyRubbish();
    var stateStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [ {
		            "id": "-1",
		            "name": "全部"
		        },
		        {
		            "id": "101",
		            "name": "完成"
		        },
		        {
		            "id": "102",
		            "name": "运行"
		        },
		        {
					"id" : "30",
					"name" : "异常"
				},
		        {
		            "id": "60",
		            "name": "已终止"
		        } ]
    });
    
    
    var sName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel: '服务名称',
		emptyText : '--请输入脚本名称--',
		labelWidth : 65,
		padding : '5',
		width : '20%',
		value: filter_scriptName_forScriptCoat,
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var stateCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'state',
		padding : '5',
		labelWidth : 65,
		queryMode : 'local',
		fieldLabel : '执行状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择执行状态--',
		store : stateStore,
		width : '15%',
        labelAlign : 'right',
        listeners: {
            afterRender: function(combo) {
               if(filter_state_forScriptCoat=='-1') {
					combo.setValue(stateStore.getAt(0).data.id);
				} else if(filter_state_forScriptCoat=='101'){
					combo.setValue(stateStore.getAt(1).data.id);
				}else if(filter_state_forScriptCoat=='102'){
					combo.setValue(stateStore.getAt(2).data.id);
				}else if(filter_state_forScriptCoat=='30'){
					combo.setValue(stateStore.getAt(3).data.id);
				}else if(filter_state_forScriptCoat=='60'){
					combo.setValue(stateStore.getAt(4).data.id);
				}
            },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
    var startTime = new Ext.form.field.Date({
    	name: 'startTime',
		fieldLabel: '开始时间',
		labelWidth : 65,
		padding : '5',
		width : '22%',
        labelAlign : 'right',
        format: 'Y-m-d H:i:s',
//        value:Ext.util.Format.date(Ext.Date.add(date1,Ext.Date.DAY,-6),"Y-m-d H:i:s"),
        value:(filter_startTime_forScriptCoat != null && filter_startTime_forScriptCoat != undefined && filter_startTime_forScriptCoat != '')?filter_startTime_forScriptCoat:Ext.util.Format.date(Ext.Date.add(date1,Ext.Date.DAY,-6),"Y-m-d H:i:s"),
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
    
    var endTime = new Ext.form.field.Date({
    	name: 'endTime',
    	fieldLabel: '结束时间',
    	labelWidth : 65,
    	padding : '5',
    	width : '22%',
    	labelAlign : 'right',
    	format: 'Y-m-d H:i:s',
    	value: filter_endTime_forScriptCoat,
    	listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });

     search_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls:'customize_gray_back',
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items: [sName,
			        stateCb,
			        startTime,
			        endTime,
            {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls : 'Common_Btn',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                xtype: 'button',
                text: '返回',
                hidden: forScriptFlow_forScriptCoat!=1 && forMyScript_forScriptCoat !='脚本编写测试',
                cls: 'Common_Btn',
                handler: function() {
                	if(forMyScript_forScriptCoat == '脚本编写测试'){
                	  forwardMyScript();
                	}else{
                	  forwardtestmain();
                	}
                }
            }
            ]
		}]
        
    });

    Ext.define('scriptmonitorData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'cata',
            type: 'int'
        },
        {
        	name: 'flowId',
        	type: 'int'
        },
        {
        	name: 'actNo',
        	type: 'int'
        },
        {
            name: 'startUser',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'actType',
            type: 'string'
        },
        {
            name: 'serverNum',
            type: 'int'
        },
        {//实际上是uuid
            name: 'scriptTestID',
            type: 'string'
        },
        {
            name: 'iscriptIID',
            type: 'string'
        },
        {
            name: 'scriptTestStatus',
            type: 'int'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        }]
    });
    
    scriptmonitor_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptCoatList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
   

    var scriptmonitor_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) {
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            }
            return backValue;
        }
    },
    {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: 'actType',
        dataIndex: 'actType',
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        width: 100,
        flex: 1
    },
    {
        text: '脚本名称',
        dataIndex: 'scriptName',
        hidden: !scriptNameSwitch_forScriptCoat,
        flex: 1
    },
    {
        text: '启动人',
        dataIndex: 'startUser',
        width: 120
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 150
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 150
    },{
        text: '脚本主键',
        dataIndex: 'iscriptIID',
        hidden: true
    },{
        text: '脚本主键uuid',
        dataIndex: 'scriptTestID',
        hidden: true
    },{
        text: '脚本状态',
        dataIndex: 'scriptTestStatus',
        width: 80,
    	renderer:function(value,p,record,rowIndex){
	    	if(value==-1) {
	    		return '<font color="#F01024">草稿</font>';
	    	} else if (value==1) {
	    		return '<font color="#0CBF47">已上线</font>';
	    	} else if (value==2) {
	    		return '<font color="#FFA602">审核中</font>';
	    	} else if (value==3) {
	    		return '<font color="#13B1F5">已共享</font>';
	    	} else if (value==9) {
	    		return '<font color="">已共享未发布</font>';
	    	} else if (value==0) {
	    		return '<font color="#CCCCCC">已删除</font>';
	    	} else {
	    		return '<font color="#CCCCCC">未知</font>';
	    	}
	    }
    },
    {
    	text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'sysOperation',
		width : 255,
		items : [{
			text : '详情',
			iconCls : 'monitor_search',
			handler : function(grid, rowIndex) {
				var coatid = grid.getStore().data.items[rowIndex].data.iid; 
				var cata = grid.getStore().data.items[rowIndex].data.cata; 
				var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
				forwardruninfo2(coatid,cata,scriptType);
				
			}
		}]
    }];

    scriptmonitor_store.on('beforeload', function(store, options) {
        var new_params = {
        		scriptName:search_form.getForm().findField("scriptName").getValue(),
        		        scriptId:iid_history,
            	    	iid:sprictCoatId_forScriptCoat, //主键id
            //	    	scriptName:search_form.getForm().findField("scriptName").getValue(),
            //	    	startUser:search_form.getForm().findField("startUser").getValue(),
            state: stateCb.getValue(),
            cata: 0,
            ifrom: 0,
            menuFrom:'testHistory',
            //search_form.getForm().findField("cata").getValue(),
            startTime: startTime.getValue(),
            endTime: endTime.getValue(),
            forScriptFlow: forScriptFlow_forScriptCoat
        };

        Ext.apply(scriptmonitor_store.proxy.extraParams, new_params);
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitor_store,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });

    var scriptmonitor_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	autoScroll: true,
        store: scriptmonitor_store,
        cls:'customize_panel_back',
        border: false,
        columnLines: true,
        columns: scriptmonitor_columns,
//        bbar: pageBar
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar'
        
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptcoatmonitor_area_forScriptHistory",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_form, scriptmonitor_grid]
    });

    function clearQueryWhere() {
    	search_form.getForm().findField("scriptName").setValue('');
    	stateCb.setValue("-1");
        startTime.setValue('');
        endTime.setValue('');
    }
    
    function forwardruninfo2(coatid, flag ,scriptType) {
    	var scriptViewWin;
    	var scriptName=search_form.getForm().findField("scriptName").getValue();
    	var state =search_form.getForm().findField("state").getValue();
    	var startTime = search_form.getForm().findField("startTime").getRawValue();
    	var endTime =search_form.getForm().findField("endTime").getRawValue();
    	scriptViewWin = Ext.create('widget.window', {
			title : '测试详情',
			closable : true,
			closeAction : 'destroy',
			width : contentPanel.getWidth(),
			minWidth : 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			modal : true,
			loader : {
				url: 'forwardscriptserverhistory.do',
				params: {
					 flowId:flowId_forScriptCoat,
		                coatid: coatid,
		                forScriptFlow: forScriptFlow_forScriptCoat,
		                forMyScript :forMyScript_forScriptCoat,
		                flag: flag,
		                filter_scriptName:scriptName,
		    			filter_state:state,
		    			filter_startTime:startTime,
		    			filter_endTime:endTime,
		    			filter_serviceName:filter_serviceName_forScriptCoat,
		    			filter_serviceState:filter_serviceState_forScriptCoat,
		    			filter_serviceStartTime:filter_serviceStartTime_forScriptCoat,
		    			filter_serviceEndTime:filter_serviceEndTime_forScriptCoat,
		    			scriptType:scriptType
				},
				autoLoad : true,
				scripts : true
			},
			buttonAlign: 'center',
			buttons: [{ 
	  			xtype: "button", 
//	  			cls:'Gray_button',
	  			text: "关闭", 
	  			handler: function () {
	  				this.up("window").close();
	  			}
	  		}]
		});
    	scriptViewWin.show();
    }

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
});

