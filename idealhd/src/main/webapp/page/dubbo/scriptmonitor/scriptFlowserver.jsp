<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var flowId3ForFlow = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	
	var coatid3ForFlow = '<%=request.getParameter("coatid")==null?"":request.getParameter("coatid")%>';
	var isWin3ForFlow = '<%=request.getParameter("isWin")%>'; // 
	var serviceName3ForFlow = '<%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%>';
	var startUser3ForFlow = '<%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%>';
	var startUserFullName3ForFlow = '<%=request.getAttribute("startUserFullName")==null?"":request.getAttribute("startUserFullName")%>';
	var startTime3ForFlow = '<%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%>';
	var endTime3ForFlow = '<%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%>';
	
	var filter_scriptNameForTestExecForFlow = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForTestExecForFlow  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeForTestExecForFlow ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForTestExecForFlow = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForTestExecForFlow = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForTestExecForFlow  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeForTestExecForFlow ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForTestExecForFlow = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptFlowserver.js"></script>
</head>
<body>
	<div id="switchruninfoins_div_ForFlow" style="width: 100%; height: 75%"></div>
</body>
</html>