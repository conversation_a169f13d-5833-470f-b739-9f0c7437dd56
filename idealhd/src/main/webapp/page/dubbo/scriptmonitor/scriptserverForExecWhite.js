
Ext.onReady(function() {
	var rowExpanderLogForTestExecForWhite;
	var scriptmonitorinfoins_storeForTestExecForWhite;
	var scriptmonitorinfoins_gridForTestExecForWhite;
	var interVForTestExecForWhite = 10;
	var interPVForTestExecForWhite = 20;
	var lastIdForTestExecForWhite;
	var lastRowIndexForTestExecForWhite;
	var lastrequestIdForTestExecForWhite;
	var lastiipForTestExecForWhite;
	var lastiportForTestExecForWhite;
	var flagForTestExecForWhite = 1; // 0:测试     1:生成
	
    destroyRubbish();
    var itemsPerPage = 30;
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        },
        {name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'},
        {
            name: 'timeOut',
            type: 'int'
        }]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
//        listeners: {
//            select: function(me, record, index, eOpts) { // 选择复选框事件
//                flowMesshisForTestExecForWhite(record.data.iid, index);
//            },
//            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
//                flowMesshisForTestExecForWhite(record.data.iid, index);
//            }
//        }
    });
    scriptmonitorinfoins_storeForTestExecForWhite = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: itemsPerPage,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flagForTestExecForWhite + '&coatid=' + coatidForTestExecForWhite,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
            	flowMesshisRefreshForTestExecForWhite(lastIdForTestExecForWhite, lastRowIndexForTestExecForWhite);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatidForTestExecForWhite
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime);
                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
//                        $("#scriptState").html(res.state);
//                        $("#scriptEndTime").html(res.endTime);
//                        $("#scriptRunTime").html(res.runTime + " 秒");
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });
    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },{
            text: '超时状态',
            dataIndex: 'timeOut',
            width: 80,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = '<span class="Ignore State_Color">否</span>';
                } else{
                    backValue = '<span class="Kill_red State_Color">是</span>';
                }
                return backValue;
            }
     },{ text: '系统名称',  dataIndex: 'sysName',hidden: !CMDBflag,width:80},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,width:80},
    { text: '计算机名',  dataIndex: 'hostName',width:80},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    }, 
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
			text : '操作',
			xtype : 'actiontextcolumn',
			width : 180,
			flex:1,
			items : [{
				text : '详情',
				iconCls : 'monitor_search',
				handler : function(grid, rowIndex) {
					var iid = grid.getStore().data.items[rowIndex].data.iid;
		            var agentIp =grid.getStore().data.items[rowIndex].data.agentIp;
		            var agentPort =grid.getStore().data.items[rowIndex].data.agentPort;
		            var state = grid.getStore().data.items[rowIndex].data.state;
		            if(state == '-1' || state == '1'){
		            	flowMesshisForTestExecForWhite(iid,rowIndex);
		            }else{
		            	openActWindowForWhite(iid,state,agentIp,agentPort);
		            }
				}
			},/**{
				text : '放大',
				iconCls : 'monitor_Enlarge',
	 			getClass : function(v, metadata, record) {
						if (isWinForTestExecForWhite == 1) {
							return 'x-hidden';
						}
				},
				handler : function(grid, rowIndex) {
					var iid = grid.getStore().data.items[rowIndex].data.iid;
		            var agentIp =grid.getStore().data.items[rowIndex].data.agentIp;
		            var agentPort =grid.getStore().data.items[rowIndex].data.agentPort;
		            loggerDetailForTestExecForWhite(iid,agentIp,agentPort);
				}
			},**/{
				text : '重试',
				iconCls : 'monitor_execute',
	 			getClass : function(v, metadata, record) {
	 				    var state = record.get("state");
						if (state != '30' && state != '40' && state != '50') {
							return 'x-hidden';
						}
				},
				handler : function(grid, rowIndex) {
					var iid = grid.getStore().data.items[rowIndex].data.iid;
		            var state = grid.getStore().data.items[rowIndex].data.state;
		            reTryScriptServerForTestExecForWhite(iid,state);
				}
			},{
				text : '忽略',
				iconCls : 'monitor_skip',
	 			getClass : function(v, metadata, record) {
	 				    var state = record.get("state");
						if (state != '30' && state != '40' && state != '50'  && state != '-1' && state != '1') {
							return 'x-hidden';
						}
				},
				handler : function(grid, rowIndex) {
					var iid = grid.getStore().data.items[rowIndex].data.iid;
		            var state = grid.getStore().data.items[rowIndex].data.state;
		            skipScriptServerForTestExecForWhite(iid,state);
				}
			}]
    } ];

    rowExpanderLogForTestExecForWhite = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForTestExec{iid}">', 
        '<pre  onselectstart="return true" id="steptextareahisForTestExec{iid}"  class="monitor_desc"></pre>', 
        '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span" {state:this.toShow}>自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshIdForTestExec" name="rowFreshIdForTestExec" >&nbsp;秒</span>', 
        '&nbsp;&nbsp;&nbsp;<input {state:this.toShow} type="button" value="刷新" onclick="loadShelloutputhisForTestExecForWhite({iid},\'{agentIp}\',{agentPort})" class="Common_Btn Monitor_Btn">',
        '&nbsp;&nbsp;&nbsp;<input {state:this.toShow} type="button" value="终止" onclick="scriptServerStopForTestExecForWhite({iid},{state})" class="Common_Btn Monitor_Btn">', '</div>',
        	{
            toShow: function(state){
             var flag = '';
              if(state==20 || state==60){
                flag = 'style="display: none;"';
              }
              return flag;
           }
	     }]
    });
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        hidden: stateCodeForWhite==20 || stateCodeForWhite==60,
        name: "pageFreshTime",
        value: interPVForTestExecForWhite
    });
    var stop_btn = Ext.create('Ext.Button', {
		text : '终止',
		baseCls : 'Common_Btn',
		handler : function() {
            var data = getCHKBoxIds();
            if (data.length == 0) {
                Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                return;
            } else {
                Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                function(button, text) {
                    if (button == "yes") {
                        if (data == '-1') {
                            Ext.Msg.alert('提示', '操作执行成功!');
                            scriptmonitorinfoins_storeForTestExecForWhite.reload();
                            return;
                        }
                        Ext.MessageBox.wait("数据处理中...", "提示");
                        Ext.Ajax.request({
                            url: 'scriptServiceShellKill.do',
                            params: {
                                flag: flagForTestExecForWhite,
                                insIds: data
                            },
                            method: 'POST',
                            success: function(response, opts) {
                                var success = Ext.decode(response.responseText).success;
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                // 当后台数据同步成功时
                                if (success) {
                                	 if (refreshObjShellOutputForTestExec) {
                                         clearInterval(refreshObjShellOutputForTestExec);
                                     }
                                	 scriptmonitorinfoins_storeForTestExecForWhite.reload();
                                }
                            }
                        });
                    }
                });
            }
        }
	});
    
    var return_btn = Ext.create('Ext.Button', {
		text : '返回',
		baseCls : 'Common_Btn',
		handler : function() {
			returnBackForTestExec();
            }
	});
    
    var refresh_btn = Ext.create('Ext.Button', {
		text : '刷新',
		baseCls : 'Common_Btn',
		handler : function() {
            if (refreshObjForTestExec) {
                clearInterval(refreshObjForTestExec);
            }
            refreshPage();
            // var interValue =
            // document.getElementById('pageFreshTime').value;
            var interValue = pageFreshTime.getValue();
            interPVForTestExecForWhite = interValue;
            if (interPVForTestExecForWhite < 20) {
            	interPVForTestExecForWhite = 20;
            }
            refreshObjForTestExec = setInterval(refreshPage, interPVForTestExecForWhite * 1000);
        }
	});
	var combo = Ext.create('Ext.form.ComboBox', {
		name : 'pagesize',
		hiddenName : 'pagesize',
		store : new Ext.data.ArrayStore({
			fields : [ 'text', 'value' ],
			data : [ [ '30', 30 ], [ '50', 50 ], [ '100', 100 ],
					[ '150', 150 ], [ '200', 200 ] ]
		}),
		valueField : 'value',
		displayField : 'text',
		emptyText : 30,
		width : 60
	});
	// 添加下拉显示条数菜单选中事件
	combo.on("select", function(comboBox) {
		var pagingToolbar = Ext.getCmp('pagingbar');
		pagingToolbar.pageSize = parseInt(comboBox.getValue());
		itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
		scriptmonitorinfoins_storeForTestExecForWhite.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
		pageBar.moveFirst();
	});
	// 保留修改页数限制 刷新数据
	// combo.on("blur", function(comboBox) {
	// 	var pagingToolbar = Ext.getCmp('pagingbar');
	// 	pagingToolbar.pageSize = parseInt(comboBox.getValue());
	// 	itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
	// 	scriptmonitorinfoins_storeForTestExecForWhite.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
	// 	pageBar.moveFirst();
	// });
	var oam_act_form = Ext.create('Ext.form.Panel', {
		frame : true,
		border : false,
		bodyCls:'fm-spinner',
		layout : {
			type : 'hbox',
			align : 'middle'
		},
		defaults : {
			anchor : '100%'
		},
		items : [
				{
					text  : '自动刷新',
					xtype : 'label'
				},pageFreshTime , {
					xtype : 'label',
					text : '    秒'
				} ]
	});
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: scriptmonitorinfoins_storeForTestExecForWhite,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true,
        id:'pagingbar',
        items: [combo,oam_act_form, refresh_btn,stop_btn,return_btn]
    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_gridForTestExecForWhite.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right',
        value: serviceNameForTestExecForWhite.replace("<", "&lt")
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserForTestExecForWhite
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startTimeForTestExecForWhite
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: endTimeForTestExecForWhite
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        cls:'sc_tlbar_height',
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back', 
			border : false,
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			baseCls:'customize_gray_back', 
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    scriptmonitorinfoins_gridForTestExecForWhite = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	store: scriptmonitorinfoins_storeForTestExecForWhite,
    	autoScroll: true,
    	 cls:'customize_panel_back',
        border: false,
        columnLines: true,
        padding : grid_space,
        columns: scriptmonitorinfoins_columns,
        bbar: pageBar,
        selModel: selModel,
        plugins: [rowExpanderLogForTestExecForWhite],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                /*
						 * var cls = ''; if(record.data.state==10){ cls =
						 * 'row_Blue'; }else if(record.data.state==20){ cls =
						 * 'row_Green'; }else if(record.data.state==30){ cls =
						 * 'row_Red'; }else if(record.data.state==60){ cls =
						 * 'row_Gray'; } else { cls = 'row_Gray'; } return cls;
						 */
                return 'norowexpandblah';
            }
        }/*,

        listeners: {
            itemclick: function(a, record, item, index, e, eOpts) {
                rowExpanderLogForTestExecForWhite.toggleRow(index, record);
            }
        }*/
    });

    scriptmonitorinfoins_gridForTestExecForWhite.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
    	interVForTestExecForWhite = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshIdForTestExec').innerText = interVForTestExecForWhite;
        } else {
            document.getElementById('rowFreshIdForTestExec').innerHTML = interVForTestExecForWhite;
        }
        loadShelloutputhisForTestExecForWhite(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
        // refreshObjShellOutputForTestExec = setInterval(function() {
        // loadShelloutputhisForTestExecForWhite(record.get('iid'),
        // record.get('agentIp'), record.get('agentPort'));
        // }, 1000);
    });
    scriptmonitorinfoins_gridForTestExecForWhite.view.on('collapsebody', function(rowNode, record, expandRow, eOpts) {
    	lastIdForTestExecForWhite = 0;
    	lastRowIndexForTestExecForWhite = 0;
        if (refreshObjShellOutputForTestExec) {
            clearInterval(refreshObjShellOutputForTestExec);
        }
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptserverForExecWhite_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        bodyPadding: 5,
        items: [info_form, scriptmonitorinfoins_gridForTestExecForWhite]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

    function refreshPage() {
        // if (refreshObjShellOutputForTestExec) {
        // clearInterval(refreshObjShellOutputForTestExec);
        // }
//    	if(contentPanel.getLoader().url=='forwardscriptcoat.do') {
    	scriptmonitorinfoins_storeForTestExecForWhite.reload();
//    	}
    }

    if (refreshObjForTestExec) {
        clearInterval(refreshObjForTestExec);
    }
    refreshObjForTestExec = setInterval(refreshPage, interPVForTestExecForWhite * 1000);
    
    
    
    function returnBackForTestExec() {
    	lastIdForTestExecForWhite = '';
        if (refreshObjShellOutputForTestExec) {
            clearInterval(refreshObjShellOutputForTestExec);
        }
        if (refreshObjForTestExec) {
            if (refreshObjForTestExec) {
                clearInterval(refreshObjForTestExec);
            }
            clearInterval(refreshObjForTestExec);
        }
        if(from_where!=null && from_where!="" && from_where=="fromWhite"){
        	contentPanel.setTitle("白名单执行");
        	contentPanel.getLoader().load({
                url: "toExecMyWhite.do",
                scripts: true,
                params: {
//                	iid:white_iid,
//					serviceName : white_serviceName,
//					parmFlag : white_Flag,
//					icontent:white_content,
                	editingChosedAgentIds : editingChosedAgentIds,
                	hostName:hostNameForWhite,
					ip:ipForWhite,    
					sysAdmin:sysAdminForWhite,    
					centerName:centerNameForWhite,    
					systemInfo:systemInfoForWhite,  
					middlewareType: middlewareTypeForWhite,
					osType:osTypeForWhite,       
					dbType:dbTypeForWhite,
					icoatid:-1
                }
            });
        }else{
        	contentPanel.getLoader().load({
                url: "forwardwhitescriptcoat.do",
                scripts: true,
                params: {
        			flowId: flowIdForWhite,
        			forScriptFlow: forScriptFlowForWhite,
        			flag : 0,
        			filter_scriptName:filter_scriptNameForWhite,
    			    filter_state:filter_stateForWhite,
    			    filter_startTime:filter_startTimeForWhite,
    			    filter_endTime:filter_endTimeForWhite,
    			    filter_serviceName:filter_serviceNameForWhite,
                    filter_Ip:filter_IpForWhite,
    				filter_serviceState:filter_serviceStateForWhite,
    				filter_serviceStartTime:filter_serviceStartTimeForWhite,
    				filter_serviceEndTime:filter_serviceEndTimeForWhite
                }
            });
        }
    }
	
    
    function reTryScriptServerForTestExecForWhite(requestId, state) {
	    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
	    function(btn) {
	        if (btn == 'yes') {
	            Ext.MessageBox.wait("数据处理中...", "提示");
	            Ext.Ajax.request({
	                url: 'retryScriptServiceShell.do',
	                method: 'POST',
	                params: {
	                    insIds: requestId,
	                    flag: flagForTestExecForWhite
	                },
	                success: function(response, request) {
	                    var success = Ext.decode(response.responseText).success;
	                    var message = Ext.decode(response.responseText).message;
	                    if (success) {
	                        Ext.Msg.alert('提示', message);
	                    } else {
	                        Ext.Msg.alert('提示', message);
	                    }
	                    scriptmonitorinfoins_storeForTestExecForWhite.reload();
	                    if (refreshObjShellOutputForTestExec) {
	                        clearInterval(refreshObjShellOutputForTestExec);
	                    }
	                },
	                failure: function(result, request) {
	                    secureFilterRs(result, "操作失败！");
	                }
	            });
	        }
	    })
	}
	function skipScriptServerForTestExecForWhite(requestId, state) {
	    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
	    function(btn) {
	        if (btn == 'yes') {
	            Ext.MessageBox.wait("数据处理中...", "提示");
	            Ext.Ajax.request({
	                url: 'skipScriptServiceShell.do',
	                method: 'POST',
	                params: {
	                    insIds: requestId,
	                    flag: flagForTestExecForWhite
	                },
	                success: function(response, request) {
	                    var success = Ext.decode(response.responseText).success;
	                    var message = Ext.decode(response.responseText).message;
	                    if (success) {
	                        Ext.Msg.alert('提示', message);
	                    } else {
	                        Ext.Msg.alert('提示', message);
	                    }
	                    scriptmonitorinfoins_storeForTestExecForWhite.reload();
	                    if (refreshObjShellOutputForTestExec) {
	                        clearInterval(refreshObjShellOutputForTestExec);
	                    }
	                },
	                failure: function(result, request) {
	                    secureFilterRs(result, "操作失败！");
	                }
	            });
	        }
	    });
	}
	function openActWindowForWhite(requestId, state,agentIp,agentPort) {
		var runningWindow = null;
		var fp2 = null;
		var cmdStr = null;
		var surl = "getScriptExecOutput.do";
		var h = window.innerHeight || document.documentElement.clientHeight
				|| document.body.clientHeight;
		function getData(surl,state, requestId, agentIp, agentPort, cmdValue) {
			Ext.Ajax.request({
				url : surl,
				params : {
					requestId : requestId,
					agentIp : agentIp,
					agentPort : agentPort,
					flag:flagForTestExecForWhite,
					input:cmdValue
				},
				success : function(response, opts) {
					var msg = Ext.decode(response.responseText);
					var oldValue = Ext.getCmp('baseinfo_form').getForm().findField(
							"actOutPut").getValue();
					var output = msg.message;
	
					var json = {
						actName :serviceNameForTestExecForWhite+"-"+agentIp+"-"+agentPort ,
						actInsName : serviceNameForTestExecForWhite,
						actOutPut : output
					};
					Ext.getCmp('baseinfo_form').getForm().setValues(json);
					actName.getEl().dom.innerHTML='活动名：    <div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:80%;height:35px ;margin-bottom:10px"><pre>'+actName.getValue()+'</pre></div>';
					actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:80%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+actOutPut.getValue()+'</pre></div>';
				},
				failure : function(response, opts) {
	
				}
	
			});
		}
		var cmdStr = new Ext.form.TextField({
			anchor : '100%',
			labelWidth : 70,
			fieldLabel : 'CMD',
			disabled:(state==10)?false:true,
			listeners : {
				specialkey : function(textfield, e) {
					if (e.getKey() == Ext.EventObject.ENTER) {
						var cmdV = cmdStr.getValue();
						cmdStr.setValue("");
						getData(surl,state, requestId, agentIp, agentPort, cmdV);
					}
				}
			}
		});
		var actName= Ext.create ('Ext.form.field.Text',
				{
					fieldLabel : '活动名',
					labelWidth : 70,
					name : 'actName',
					margin :'10 0 5 0',
					anchor : '95%' // anchor width by percentage
				});
		var actOutPut = Ext.create ('Ext.form.field.TextArea',
		{
			fieldLabel : '活动日志',
			labelWidth : 70,
			grow : true,
			height : h-200,
			name : 'actOutPut',
			margin :'15 0 5 0',
			anchor : '95%'
		});
		var fp2 = new Ext.form.Panel({
			border : false,
			id : 'baseinfo_form',
			height : '100%',
			padding : 5,
			fieldDefaults : {
				labelWidth : 60,
				labelAlign : 'right'
			},
			defaultType : 'textfield',
			items : [actName, {
				fieldLabel : '实例名',
				labelWidth : 70,
				name : 'actInsName',
				hidden : true,
				anchor : '100%' // anchor width by percentage
			}, actOutPut, cmdStr ]
		});
	
		if (runningWindow == undefined || !runningWindow.isVisible()) {
			runningWindow = Ext.create('Ext.window.Window', {
				title : serviceNameForTestExecForWhite,
				modal : true,
				closeAction : 'destroy',
				constrain : true,
				autoScroll : true,
                maximizable : true,
				width : 600,
				height : h - 95,
				items : [ fp2 ],
				dockedItems : [ {
					xtype : 'toolbar',
					// baseCls:'customize_gray_back',
					dock : 'bottom',
					layout: {pack: 'center'},
					items : [
					 {
						text : '刷新',
						textAlign : 'center',
						cls : 'Common_Btn',
						handler : function() {
							getData(surl,state, requestId, agentIp, agentPort, null);
							cmdStr.setValue("");
						}
					}, {
						text : '终止',
						textAlign : 'center',
						cls : 'Common_Btn',
						handler : function() {
							scriptServerStopForTestExecForWhite(requestId,state)
						}
					} ]
				} ],
				layout : 'fit'
			});
		}
		runningWindow.show();
		getData(surl,state, requestId, agentIp, agentPort, null);
	}
    
	function flowMesshisForTestExecForWhite(iruninfoinsid, rowIndex) {
		lastIdForTestExecForWhite = iruninfoinsid;
		lastRowIndexForTestExecForWhite = rowIndex;
	    var record = scriptmonitorinfoins_storeForTestExecForWhite.getAt(rowIndex);
	    var records = scriptmonitorinfoins_storeForTestExecForWhite.getRange(0, scriptmonitorinfoins_storeForTestExecForWhite.getCount());
	    for (var i = 0; i < records.length; i++) {
	        if (i != rowIndex && rowExpanderLogForTestExecForWhite.recordsExpanded[records[i].internalId]) {
	        	rowExpanderLogForTestExecForWhite.toggleRow(i, records[i]);
	        }
	    }
	     var record = scriptmonitorinfoins_storeForTestExecForWhite.getAt(rowIndex);
	     rowExpanderLogForTestExecForWhite.toggleRow(rowIndex, record);
	}
    
	
	function loggerDetailForTestExecForWhite(iid, agentIp, agentPort) {
	    if (refreshObjShellOutputForTestExec) {
	        clearInterval(refreshObjShellOutputForTestExec);
	    }
	    if (refreshObjForTestExec) {
	        if (refreshObjForTestExec) {
	            clearInterval(refreshObjForTestExec);
	        }
	        clearInterval(refreshObjForTestExec);
	    }
	    contentPanel.getLoader().load({
	        url: "forwardscriptserverLogger.do",
	        scripts: true,
	        params: {
	            instanceId: iid,
	            agentIp: agentIp,
	            agentPort: agentPort,
	//            flowId: flowIdForWhite,
	            coatId: coatidForTestExecForWhite,
	            flag: flagForTestExecForWhite
	        }
	    });
	}
	
});



function flowMesshisRefreshForTestExecForWhite(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    var record = scriptmonitorinfoins_storeForTestExecForWhite.getAt(rowIndex);
    var records = scriptmonitorinfoins_storeForTestExecForWhite.getRange(0, scriptmonitorinfoins_storeForTestExecForWhite.getCount());
    var rowFreshValue = document.getElementById('rowFreshIdForTestExec').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForTestExecForWhite = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForTestExec').innerText = interVForTestExecForWhite;
    } else {
        document.getElementById('rowFreshIdForTestExec').innerHTML = interVForTestExecForWhite;
    }

    rowExpanderLogForTestExecForWhite.toggleRow(lastRowIndexForTestExecForWhite, records[lastRowIndexForTestExecForWhite]);
    if(refreshObjShellOutputForTestExec) {
    	clearInterval(refreshObjShellOutputForTestExec);
    }
    refreshObjShellOutputForTestExec = setInterval(function() {
    	if(contentPanel.getLoader().url=='forwardscriptcoat.do') {
    		loadShelloutputhisInfoForTestExecForWhite(lastrequestIdForTestExecForWhite, lastiipForTestExecForWhite, lastiportForTestExecForWhite);
    	}
    },
    rowFreshValue * 1000);
    // var record = scriptmonitorinfoins_storeForTestExecForWhite.getAt(rowIndex);
    // rowExpanderLogForTestExecForWhite.toggleRow(rowIndex, record);
}

function loadShelloutputhisForTestExecForWhite(requestId, iip, iport) {
	lastrequestIdForTestExecForWhite = requestId;
	lastiipForTestExecForWhite = iip;
	lastiportForTestExecForWhite = iport;
    if (refreshObjShellOutputForTestExec) {
        clearInterval(refreshObjShellOutputForTestExec);
    }
    var rowFreshValue = document.getElementById('rowFreshIdForTestExec').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForTestExecForWhite = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForTestExec').innerText = interVForTestExecForWhite;
    } else {
        document.getElementById('rowFreshIdForTestExec').innerHTML = interVForTestExecForWhite;
    }
    // document.getElementById('rowFreshIdForTestExec').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfoForTestExecForWhite(requestId, iip, iport);
    refreshObjShellOutputForTestExec = setInterval(function() {
    	loadShelloutputhisInfoForTestExecForWhite(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfoForTestExecForWhite(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForTestExec' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: flagForTestExecForWhite
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerText = msg.message;
                } else {
                    document.getElementById(desc).innerText = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerText = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStopForTestExecForWhite(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
                Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_storeForTestExecForWhite.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flagForTestExecForWhite
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_storeForTestExecForWhite.reload();
                    if (refreshObjShellOutputForTestExec) {
                        clearInterval(refreshObjShellOutputForTestExec);
                    }
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}

