<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var dbaas_prjType = <%=request.getAttribute("projectFlag")==null?0:request.getAttribute("projectFlag")%>;
	var dbaas_monitorFlag = <%=request.getAttribute("monitorFlag")==null?0:request.getAttribute("monitorFlag")%>;
	var flowId = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	var forScriptFlow = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_state  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTime ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTime = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptCoatForExecForMonitor.js"></script>
</head>
<body>
<div id="scriptCoatForExecForMonitor_area" style="width: 100%;height: 100%">
</div>
</body>
</html>