<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var flowId = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	//是否是从我的脚本测试点击过来的 
	var forMyScript = '<%=request.getParameter("forMyScript")==null?"":request.getParameter("forMyScript")%>';
	var forScriptFlow = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	var coatidForTestExec = '<%=request.getParameter("coatid")==null?"":request.getParameter("coatid")%>';
	<%-- var flag = '<%=request.getParameter("flag")%>'; // 0:测试     1:生成 --%>
	var isWinForTestExec = '<%=request.getParameter("isWin")%>'; // 
	var stateCode = <%=request.getAttribute("stateCode")==null?-100:request.getAttribute("stateCode")%>;
	var serviceNameForTestExec = '<%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%>';
	var startUserForTestExec = '<%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%>';
	var startTimeForTestExec = '<%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%>';
	var endTimeForTestExec = '<%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%>';
	var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_state  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTime ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTime = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var script_showGridSwitch = <%=request.getAttribute("showGridSwitch")==null?false:request.getAttribute("showGridSwitch")%>;
	var scriptType = '<%=request.getAttribute("scriptType")==null?false:request.getAttribute("scriptType")%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/scriptserverhistory.js"></script>
</head>
<body>
<%-- <div class="Im_detail">
	<div class="Im_detail_title">
		<div class="Title_info"><img src="images/IM_detail_common.png" align="absmiddle" class="IM_detail_ic2"/>基本信息</div>
		<% if(request.getParameter("isWin")==null || !"1".equals(request.getParameter("isWin"))) { %>
		<div id="returnback" onclick="javaScript:returnBackForTestExec();" class="Blue_button Return">返回</div>
		<% } %>
	</div>
    <div class="Im_detail_content">
    	<table cellpadding="0" cellspacing="0" border="0" class="Im_detail_table">
        	<tr>
            	<td class="Im_table1_td1">服务名称：</td>
                <td class="Im_table1_td2"><%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%></td>
                <td class="Im_table1_td1">执行结果：</td>
                <td class="Im_table1_td2"><div id="scriptState" class="Im_<%=request.getAttribute("stateFlag")==null?"":request.getAttribute("stateFlag")%>"><%=request.getAttribute("state")==null?"":request.getAttribute("state")%></div></td>
                <td class="Im_table1_td1">启动人：</td>
                <td class=""><%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%></td>
            </tr>
            <tr>
            	<td class="Im_table1_td1">开始时间：</td>
                <td class="Im_table1_td2"><%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%></td>
                <td class="Im_table1_td1">结束时间：</td>
                <td class="Im_table1_td2" id="scriptEndTime"><%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%></td>
                <td class="Im_table1_td1">总耗时：</td>
                <td id="scriptRunTime"><%=request.getAttribute("runTime")==null?"":request.getAttribute("runTime")%> 秒</td>
            </tr>
        </table>
    </div>
</div> --%>
	<div id="switchruninfoins_div" style="width: 100%; height: 75%">
	</div>
</body>
</html>