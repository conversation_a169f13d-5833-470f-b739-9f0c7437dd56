<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment,com.ideal.common.utils.SessionData,java.util.ArrayList,java.util.Arrays"%>

<%
	boolean scriptNameSwitch = Environment.getInstance().getScriptNameSwitch();
    boolean scriptExeUserSwitch = Environment.getInstance().getScriptExecUserSwitch ();
    boolean isHideCustom = Environment.getInstance().getScriptHideCustomSwitch();
	boolean logAnalizeSwitch =  Environment.getInstance().getScriptLogAnalizeSwitch();
	boolean commonTaskNameSwitch =  Environment.getInstance().getScriptCommonTaskNameSwitch();
	boolean serviceNameSwitchForHis =  Environment.getInstance().getServiceNameSwitchForHis();
	
	String highPermissionUser = Environment.getInstance().getScriptTaskExecHistoryHighpermissionUser();
	SessionData sessionData = SessionData.getSessionData(request);
	String loginUserName = sessionData.getLoginName();
	ArrayList<String> arlist = new ArrayList<String>(Arrays.asList(highPermissionUser.split(",")));
	boolean showStartUser =true;
	 if (arlist.contains(loginUserName))// 如果包含，代表是高权限用户，展示执行人
     {
	     showStartUser =false;
     }
%>

	
<html>
<head>

 <script type="text/javascript">
 <%--var flowTypeExec=<%=request.getAttribute("flowType")%>==null?1:<%=request.getAttribute("flowType")%>;
     var cataExec=<%=request.getParameter("cata")%>==null?1:<%=request.getParameter("cata")%>;--%>
     var whiteScriptFilter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
     var whiteScriptFilter_state  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
     var whiteScriptFilter_startTime ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
     var whiteScriptFilter_endTime = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
     var whiteScriptFilter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
     var whiteScriptFilter_serviceState  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
     var whiteScriptFilter_serviceStartTime ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
     var whiteScriptFilter_serviceEndTime = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
     var whiteScriptFilter_serviceTaskName ='<%=request.getParameter("filter_serviceTaskName")==null?"":request.getParameter("filter_serviceTaskName")%>';
     var whiteScriptFilter_serviceCustomName ='<%=request.getParameter("filter_serviceCustomName")==null?"":request.getParameter("filter_serviceCustomName")%>';
     var whiteScriptFilter_audiUser ='<%=request.getParameter("filter_audiUser")==null?"":request.getParameter("filter_audiUser")%>';
     var whiteScriptScriptNameSwitchForHis = <%=scriptNameSwitch%>;
     var whiteScriptScriptExeUserSwitch = <%=scriptExeUserSwitch%>;
     var whiteScriptIsHideCustomSwitch = <%=isHideCustom%>;
     var whiteScriptLogAnalizeSwitch = <%=logAnalizeSwitch%>;
     var whiteScriptShowStartUser = <%=showStartUser%>;
     var whiteScriptTaskNameSwitch = <%=request.getAttribute("taskNameSwitch")%>;
     var whiteScriptCommonTaskNameSwitch = <%=commonTaskNameSwitch%>;
     var whiteScriptServiceNameSwitchForHis = <%=serviceNameSwitchForHis%>;
</script> 
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/indexExecForWhite.js"></script>
</head>
<body>
<div id="scriptflowmonitor_areaindexExecWhiteScript" style="width: 100%;height: 100%">
</div>
</body>
</html>