
Ext.onReady(function(){
//	var scriptmonitor_storeindexExecindexExec;
	var search_form1ForExecForFlow;
	var scriptflowmonitor_store;
	var flowTypeExec = -1;
	var cataExec = 1;
		
	destroyRubbish();
	
	Ext.define('startUserModel', {
		extend : 'Ext.data.Model',
		fields : [{
		  name : 'iid',
		  type : 'string'
		}, {
		  name : 'iusername',
		  type : 'string'
		}, {
			  name : 'iuserfullname',
			  type : 'string'
			}]
		  
	});
	
	var stateStore = Ext.create('Ext.data.Store', {
	    fields: ['id', 'name'],
	    data : [
	        {"id":"-2", "name":"全部"},
	        {"id":"101", "name":"完成"},
//	        {"id":"20", "name":"正常完成"},
//	        {"id":"40", "name":"异常完成"},
	        {"id":"102", "name":"运行"},
//	        {"id":"10", "name":"正常运行"},
//	        {"id":"50", "name":"异常运行"},
	        {"id":"60", "name":"终止"}
	    ]
	});
	/*var cataStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
		        {"id":"-1", "name":"全部"},
		        {"id":"0", "name":"测试"},
		        {"id":"1", "name":"生产"}
		        ]
	});*/
	
	
	var startUserStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'startUserModel',
		proxy : {
			type : 'ajax',
			url : 'getStartUser.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
	    }
	});
	
	
	search_form1ForExecForFlow = Ext.create('Ext.form.Panel', {
		region:'north',
    	layout : 'anchor',
    	buttonAlign : 'center',
    	//height:80,
    	border : false,
    	baseCls:'customize_gray_back',
    	dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items: [
				{
		            fieldLabel: '服务名称',
		            labelAlign : 'right',
		            labelWidth : 80,
		            name: 'serviceName',
		            width:'30%',
		            xtype: 'textfield',
		            value: whiteScriptFilter_serviceName,
		            listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			            }
			        }
		        },{
		            fieldLabel: '执行状态',
		            labelAlign : 'right',
		            labelWidth : 80,
		            name: 'state',
		            width:'30%',
		            displayField: 'name',
				    valueField: 'id',
				    store: stateStore,
				    queryMode: 'local',
				    listeners : {
				        afterRender : function(combo) {
				          if(whiteScriptFilter_serviceState=='-2') {
										combo.setValue(stateStore.getAt(0).data.id);
									} else if(whiteScriptFilter_serviceState=='101'){
										combo.setValue(stateStore.getAt(1).data.id);
									}else if(whiteScriptFilter_serviceState=='102'){
										combo.setValue(stateStore.getAt(2).data.id);
									}else if(whiteScriptFilter_serviceState=='60'){
										combo.setValue(stateStore.getAt(3).data.id);
									}
				        },
				            specialkey: function(field, e){
				                if (e.getKey() == e.ENTER) {
				                	pageBar.moveFirst();
				                }
				            }
				     },
				    //editable:false,
			        xtype: 'combobox'
		        },{
		            fieldLabel: '任务名称',
		            labelAlign : 'right',
		            labelWidth : 90,
		            name: 'taskName',
		            width:'20%',
		            xtype: 'textfield',
		            hidden:whiteScriptTaskNameSwitch,
		            value:whiteScriptFilter_serviceTaskName,
		            listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			            }
			        }
		        },{
		            fieldLabel: '常用任务名称',
		            labelAlign : 'right',
		            labelWidth : 90,
		            name: 'customName',
		            width:'20%',
		            xtype: 'textfield',
		            value:whiteScriptFilter_serviceCustomName,
		            listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			            }
			        }
		        },{
					fieldLabel : '执行人',
					labelWidth : 70,
					name : 'startUser',
					labelAlign : 'right',
					width : '20%',
					xtype : 'textfield',
					hidden:whiteScriptShowStartUser
				}
			]
		},{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items: [
				 {
	        	fieldLabel: '开始时间',
	        	xtype : 'datefield',
	            labelAlign : 'right',
	            labelWidth : 80,
	            width:'31%',
	            name: 'startTime',
	            format : 'Y-m-d',
	            value: whiteScriptFilter_serviceStartTime =='' ? Ext.util.Format.date(Ext.Date.add(new Date(),Ext.Date.DAY,-30),"Y-m-d"):whiteScriptFilter_serviceStartTime,
        		listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
	        },{
	        	fieldLabel : '结束时间',
	        	xtype : 'datefield',
	            labelAlign : 'right',
	            labelWidth : 80,
	            width:'31%',
	            name: 'endTime',	           
	            format : 'Y-m-d',
	            value:  whiteScriptFilter_serviceEndTime,
	            listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
	            //value:  filter_serviceEndTime =='' ? Ext.util.Format.date(new Date(),"Y-m-d"):filter_serviceEndTime
	        },{
	            fieldLabel: '审核人',
	            labelAlign : 'right',
	            labelWidth : 90,
	            width:'20.6%',
	            //margin : '0 10 0 10',
            	displayField: 'iuserfullname',
			    valueField: 'iusername',
			    store: startUserStore,
			    editable : true,
			    queryMode: 'local',
		        name: 'audiUser',
		        value:whiteScriptFilter_audiUser,
		        xtype: 'combobox',
		        listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
	        },{
				xtype : 'button',
				text : '查询',
				cls : 'Common_Btn',
				handler : function() {
					pageBar.moveFirst();
				}
			},{
				xtype : 'button',
				text : '清空',
				cls : 'Common_Btn',
				handler : function() {
					clearQueryWhere();
				}
			},{
				xtype : 'button',
				text : '导出',
				cls : 'Common_Btn',
				handler : function() {
					exportExcel();
				}
			}
			]
		}
    	]
	});
	
	
    

	Ext.define('scriptFlowMonitorData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'serviceName',     type: 'string'},
            {name: 'flowName',     type: 'string'},
            {name: 'flowType',     type: 'int'},
            {name: 'state',     type: 'int'},
            {name: 'cata',     type: 'int'},
            {name: 'startUser',     type: 'string'},
            {name: 'workItemId',     type: 'int'},
            {name: 'audiUser',     type: 'string'},
            {name: 'audiUserFullName',     type: 'string'},
            {name: 'taskName',     type: 'string'},
            {name: 'startTime',     type: 'string'},
            {name: 'endTime',     type: 'string'},
            {name: 'runNums',     type: 'int'},
            {name : 'scriptstatus'     ,type : 'int'},
		    {name: 'scriptTestID',type: 'string'},
		    {name: 'scriptIid',type: 'string'},
            {name: 'runTime',     type: 'int'},
            {name: 'butterflyVersion',type: 'string'},
            {name: 'scriptName',     type: 'string'},
            {name: 'customName',     type: 'string'}
        ]
    });
    
	 scriptflowmonitor_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptFlowMonitorData',
        proxy: {
            type: 'ajax',
            url: 'getScriptFlowList.do',
            timeout: 6000000, 
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var scriptflowmonitor_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
                                 { text: '执行状态',  dataIndex: 'state',width:100,renderer:function(value,p,record){
					                	var backValue = "";
					                	if(value==10 || value==11){
					                		backValue = '<span class="Run_Green State_Color">运行</span>';
					                	}else if(value==20 || value==5){ // 5 is 忽略
					                		backValue = "<span class='Complete_Green State_Color'>完成</span>"
					                	}else if(value==30){
					                		backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
					                	}else if(value==40){
					                		backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
					                	}else if(value==50){
					                		backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
					                	}else if(value==60){
					                		backValue = '<span class="Kill_red State_Color">已终止</span>';
					                	}else if(value==-1){
					                		backValue = '<span class="Not_running State_Color">初始化</span>';
					                	}
					                	return backValue;
					                }},
					                { text: '常用任务名称',  dataIndex: 'customName',hidden:!whiteScriptCommonTaskNameSwitch},
		                            { text: '实例主键',  dataIndex: 'iid',hidden:true},
		                            { text: '服务名称',  dataIndex: 'serviceName',width:100,flex:1,hidden:!whiteScriptServiceNameSwitchForHis},
		                            { text: '脚本名称',  dataIndex: 'scriptName',flex:1,hidden:!whiteScriptScriptNameSwitchForHis},
		                            { text: '作业名称',  dataIndex: 'flowName',flex:1,hidden:true},
		                            { text: '启动人',  dataIndex: 'startUser',width:80,hidden:true},
		                            { text: 'workItemId',  dataIndex: 'workItemId',width:80,hidden:true},
		                            { text: '任务名称',  dataIndex: 'taskName',width:100,flex:1,hidden:whiteScriptTaskNameSwitch},
		                            { text: '审核人',  dataIndex: 'audiUserFullName',width:100},
					                { text: '开始时间',  dataIndex: 'startTime',flex:1,width:150},
					                { text: '结束时间',  dataIndex: 'endTime',flex:1,width:150},
//					                { text: '服务器个数',  dataIndex: 'serverNum',width:100},
					                { text: '运行时长(秒)',  dataIndex: 'runTime',width:100},
					                { text: '类型',  dataIndex: 'flowType',hidden: true,width:80,renderer:function(value,p,record){
					                	var backValue = "";
					                	if(value==0){
					                		backValue = "脚本";
					                	}else if(value==1){
					                		backValue = "作业";
					                	} else {
					                		backValue = "未知";
					                	}
					                	return backValue;
					                }},
					                {text : '单号', sortable : true, dataIndex : 'butterflyVersion', width : 100,hidden:  !scriptOddNumberSwitch ,renderer: function(value,metaData,record){
					       				var state = record.data.state;
					       				var iid = record.data.workItemId;
					       				if(state==5||state==20||state==40||state==60||state==30){
					       					if(value==null||value==""){
					       						try{
					       						   var time = record.data.startTime;
					       				           var dateBegin = new Date(time.replace(/-/g, "/"));
					       				           var dateEnd = new Date();
					       				           var dateDiff = dateEnd.getTime() - dateBegin.getTime();
					       				           var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000));
					       				           if(dayDiff<=3){
					       				        	  return "<a onclick='writeVersion(\""+iid+"\")'>填写单号</a>"
					       				           }else{
					       				        	  return value;
					       				           }
					       						}catch(e){
					       						}
					       					}
					       				}
					       				   return value;
					       			  }
					       		   },
/*					                { text: '类别',  dataIndex: 'cata',width:50,renderer:function(value,p,record){
					                	var backValue = "";
					                	if(value==0){
					                		backValue = "测试";
					                	}else if(value==1){
					                		backValue = "生产";
					                	}
					                	return backValue;
					                }},*/
					       		   {
					       		   	
										text : '操作',
										xtype : 'actiontextcolumn',
										width : 220,
										align : 'left',
				//						menuDisabled : true,
										items : [{
													text : '详情',
													iconCls : 'monitor_search',
													handler : function(grid, rowIndex) {
														var flowid = grid.getStore().data.items[rowIndex].data.iid;
									                	var serviceName =  grid.getStore().data.items[rowIndex].data.serviceName;
									                	if(serviceName=='IDEAL_Aa_文件比对_aA_IDEAL'){
									                		forwardFileResult1ForExecForFlow(flowid, cataExec);
									                	}else{
									                		forwardscriptcoat1ForExecForFlow(flowid, cataExec);
									                	}
													}
												}, {
													text : '历史',
													iconCls : 'monitor_search',
													getClass : function(v, metadata, record) {
														if(!whiteScriptLogAnalizeSwitch){
															return 'x-hidden';
														}
													},
													handler : function(grid, rowIndex) {
														var iid = grid.getStore().data.items[rowIndex].data.scriptTestID;
									                	var scriptIid =  grid.getStore().data.items[rowIndex].data.scriptIid;
														openLogAnalize(scriptIid,iid);
													}
												},{
													text : '常用任务',
													iconCls : 'script_collect',
													getClass : function(v, metadata, record) {
										 				    if(whiteScriptIsHideCustomSwitch){
										 				    	return 'x-hidden';
										 				    }
													},
													handler : function(grid, rowIndex) {
														var flowId = grid.getStore().data.items[rowIndex].data.iid;
									                	var iid = grid.getStore().data.items[rowIndex].data.scriptTestID;
									                	var serviceName =  grid.getStore().data.items[rowIndex].data.serviceName;
									                	var audiUser =   grid.getStore().data.items[rowIndex].data.audiUser;
														collectScriptFlowForProductCoat(flowId,iid,serviceName,audiUser);
													}
												},{
													text : '监控',
													iconCls : 'script_monitor',
													getClass : function(v, metadata, record) {
														  	var state =record.get('state');
										 				    if(state != '10' && state != '11'){
										 				    	return 'x-hidden';
										 				    }
													},
													handler : function(grid, rowIndex) {
														var flowId =  grid.getStore().data.items[rowIndex].data.iid;
														var iid = grid.getStore().data.items[rowIndex].data.scriptTestID;
														var type = 1;
														monitorScriptFlowForProductCoat(flowId,iid,type);
													}
												}]
					       		   }
//					                { 
//					                text: '操作',  dataIndex: 'sysOperation',width:220,renderer:function(value,p,record){
//						                	var flowid =  record.get('iid');
//						                	var cata =  record.get('cata');
//						                	var state = record.get('state');
//						                	var iid = record.get('scriptTestID');
//						                	var scriptIid = record.get('scriptIid');
//						                	var type = 1;
//						                	var serviceName =  record.get('serviceName');
//						                	var audiUser =  record.get('audiUser');
//						                	var forwardFunction='forwardscriptcoat1ForExecForFlow';
//						                	if(serviceName=='IDEAL_Aa_文件比对_aA_IDEAL'){
//						                		forwardFunction='forwardFileResult1ForExecForFlow';
//						                	}
//						                	var openLogAnalize = 'openLogAnalize';
//						                	if(whiteScriptLogAnalizeSwitch){
//						                		var logAnalizeWindow = '<a href="javascript:void(0)" onclick="'+openLogAnalize+'('+scriptIid+',\''+iid+'\')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;历史</a>&nbsp;&nbsp;';
//						                	}else{
//						                		var logAnalizeWindow = '';
//						                	}
//						                	if(whiteScriptIsHideCustomSwitch){
//						                		return '<span class="switch_span">'+
//					                			'<a href="javascript:void(0)" onclick="'+forwardFunction+'('+flowid+', '+ cataExec +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;'+
//					                			logAnalizeWindow;
//						                	}else{
//						                		if(state == '10' || state == '11'){
//						                			return '<span class="switch_span">'+
//						                			'<a href="javascript:void(0)" onclick="'+forwardFunction+'('+flowid+', '+ cataExec +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;'+
//						                			logAnalizeWindow+
//						                			'<a href="javascript:void(0)" onclick="collectScriptFlowForProductCoat('+flowid+', \''+iid+'\',\''+serviceName+'\',\''+audiUser+'\')"><img src="images/monitor_bg.png" align="absmiddle" class="script_collect"></img>常用任务</a>&nbsp;&nbsp;'+
//						                			'<a href="javascript:void(0)" onclick="monitorScriptFlowForProductCoat('+flowid+',\''+iid+'\','+type+')"><img src="images/monitor_bg.png" align="absmiddle" class="script_monitor"></img>监控</a>'+
//						                			'</span>';
//						                		}else{
//						                			return '<span class="switch_span">'+
//						                			'<a href="javascript:void(0)" onclick="'+forwardFunction+'('+flowid+', '+ cataExec +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;'+
//						                			logAnalizeWindow+
//						                			'<a href="javascript:void(0)" onclick="collectScriptFlowForProductCoat('+flowid+', \''+iid+'\',\''+serviceName+'\',\''+audiUser+'\')"><img src="images/monitor_bg.png" align="absmiddle" class="script_collect"></img>常用任务</a>&nbsp;&nbsp;'+
//						                			//'<a href="javascript:void(0)" onclick="scriptCoatStop('+flowid+', '+ cata +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_termination"></img>终止</a>'+
//						                			'</span>';
//						                		}
//						                	}
//						                }
//					                }
					               ];
    
    scriptflowmonitor_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	serviceName:search_form1ForExecForFlow.getForm().findField("serviceName").getValue(),
	    	taskName:search_form1ForExecForFlow.getForm().findField("taskName").getValue(),
	    	customName:search_form1ForExecForFlow.getForm().findField("customName").getValue(),
//	    	startUser:search_form1ForExecForFlow.getForm().findField("startUser").getValue(),
	    	audiUser:search_form1ForExecForFlow.getForm().findField("audiUser").getValue(),
    		state:search_form1ForExecForFlow.getForm().findField("state").getValue() == null ? -2:search_form1ForExecForFlow.getForm().findField("state").getValue(),
    		cata:cataExec,//search_form1ForExecForFlow.getForm().findField("cata").getValue(),
    		startTime:search_form1ForExecForFlow.getForm().findField("startTime").getValue(),
    		endTime:search_form1ForExecForFlow.getForm().findField("endTime").getValue(),
    		startUserNameValue : search_form1ForExecForFlow.getForm().findField("startUser").getValue(),
    		isWhite : '1',
    		flowType:flowTypeExec
	    };
	    
	    Ext.apply(scriptflowmonitor_store.proxy.extraParams, new_params);
    });
    
    
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: scriptflowmonitor_store,
        dock: 'bottom',
        displayInfo: true
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
//		id:'selModel',
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
    var scriptflowmonitor_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region:'center',
//    	height: contentPanel.getHeight()-100,
	    store:scriptflowmonitor_store,
	    selModel:selModel,
		selType: 'cellmodel',
	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    border:false,
	    columnLines : true,
	    cls:'customize_panel_back',
	    columns:scriptflowmonitor_columns
	    /*bbar: pageBar,
	    viewConfig:{
			getRowClass:function(record,rowIndex,rowParams,arriveStore){
				var cls = '';
				if(record.data.state==10){
					cls = 'row_Blue';
				}else if(record.data.state==20){
					cls = 'row_Green';
				}else if(record.data.state==30){
					cls = 'row_Red';
				}else if(record.data.state==40){
					cls = 'row_Red';
				}else if(record.data.state==50){
					cls = 'row_Red';
				}else if(record.data.state==60){
					cls = 'row_Gray';
				} else {
					cls = 'row_Gray';
				}
				return cls; 
			}
		}*/
	});

    var mainPanel = Ext.create('Ext.panel.Panel',{
        renderTo : "scriptflowmonitor_areaindexExecWhiteScript",
        border : false,
//        bodyPadding : 5,
        layout:'border',
        width : contentPanel.getWidth(),
	    height :contentPanel.getHeight()-modelHeigth,
        items : [search_form1ForExecForFlow,scriptflowmonitor_grid]
    });
    
    function exportExcel(){
		var records = scriptflowmonitor_grid.getSelectionModel().getSelection();
		var array = [];
		var flowIds = [];
//		var itemIds = [];
		if(records.length != 0){
			Ext.Array.each(records, function(recordObj) {
				var item = {};
				var flowid = recordObj.get('iid');
				var taskName = recordObj.get('taskName');
				//var itemid = recordObj.get('iid');
				item.flowid = flowid;
				item.taskName = taskName;
				array.push(item);
				flowIds.push(flowid);
				//itemIds.push(itemid);
			});
		}else{
			/*records = scriptflowmonitor_grid.getStore().getRange(0,scriptflowmonitor_store.getCount());
			Ext.Array.each(records, function(recordObj) {
				var item = {};
				var flowid = recordObj.get('iid');
				var taskName = recordObj.get('taskName');
				//var itemid = recordObj.get('iid');
				item.flowid = flowid;
				item.taskName = taskName;
				array.push(item);
				flowIds.push(flowid);
				//itemIds.push(itemid);
			});*/
			Ext.Msg.alert('提示',"请选择行!");
			return ;
		}
		var url = "multiExportCoatResult.do?data="+JSON.stringify(array)+"&flowIds="+flowIds;
		window.location.href = url;
	}
    
    function clearQueryWhere(){
    	search_form1ForExecForFlow.getForm().findField("serviceName").setValue('');
    	search_form1ForExecForFlow.getForm().findField("startUser").setValue('');
    	search_form1ForExecForFlow.getForm().findField("audiUser").setValue('');
//    	search_form1ForExecForFlow.getForm().findField("state").setValue("-2");
//    	search_form1ForExecForFlow.getForm().findField("cata").setValue("-1"),
    	var startTimes = whiteScriptFilter_serviceStartTime =='' ? Ext.util.Format.date(Ext.Date.add(new Date(),Ext.Date.DAY,-30),"Y-m-d"):whiteScriptFilter_serviceStartTime;
    	//var endTimes =  filter_serviceEndTime =='' ? Ext.util.Format.date(new Date(),"Y-m-d H:i:s"):filter_serviceEndTime;
    	search_form1ForExecForFlow.getForm().findField("startTime").setValue(startTimes);
    	search_form1ForExecForFlow.getForm().findField("taskName").setValue('');
    	search_form1ForExecForFlow.getForm().findField("customName").setValue('');
    	search_form1ForExecForFlow.getForm().findField("endTime").setValue('');
    	//search_form1ForExecForFlow.getForm().reset();
    	search_form1ForExecForFlow.getForm().findField("state").setValue("-2");
    }

    
    contentPanel.on('resize',function(){
//    	scriptflowmonitor_grid.setHeight(contentPanel.getHeight()-100);
    	mainPanel.setHeight (contentPanel.getHeight () -modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    
    
function monitorScriptFlowForProductCoat(flowId,iid,type){
//	alert(iid);
	var serviceName=search_form1ForExecForFlow.getForm().findField("serviceName").getValue();
	var state =search_form1ForExecForFlow.getForm().findField("state").getValue();
	var startTime = search_form1ForExecForFlow.getForm().findField("startTime").getRawValue();
	var endTime =search_form1ForExecForFlow.getForm().findField("endTime").getRawValue();
	popNewTab('图形监控', 'monitorSingleFlowPageScriptService.do', {
		flowId:flowId,
		serviceId:iid,
		flagType:type, 
		filter_serviceName:serviceName,
		filter_serviceState:state,
		filter_serviceStartTime:startTime,
		filter_serviceEndTime:endTime,
		contentPanelHeight:contentPanel.getHeight(),
		windowScHeight:window.screen.height,
		divID : ''
	},10, true);

}

function collectScriptFlowForProductCoat(flowId,iid,serviceName,audiUser){
		Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
	        if(btn=='ok') {
	        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
	        		var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
	                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	        	} else {
	        		var customName = Ext.util.Format.trim(text);
	        		Ext.Ajax.request({
	        	        url: 'checkCustomTemplateNameIsExist.do',
	        	        params: {
	        	            customName: customName,
	        	            flag: 1
	        	        },
	        	        method: 'POST',
	        	        success: function(response, options) {
	        	            if (!Ext.decode(response.responseText).success) {
//	        	                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
	        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换名称！</span>';
			                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	        	            } else {
	        	            	var scriptIid;
	        	            	//查询iid
	        	            	Ext.Ajax.request({
					                url: 'queryIidByUuid.do',
					                method: 'POST',
					                async: false,
					                params: {
					                    uuid: iid //uuid
					                },
					                success: function(response, options) {
					                    scriptIid = Ext.decode(response.responseText).serviceId;
					                    Ext.Ajax.request({
		        	    				url: 'getFlowTestData.do',
		        	    				method: 'POST',
		        	    				params: {
		        	    					iid: flowId,
		        	    					flag: 1
		        	    				},
		        	    				success: function(response, options) {
		        	    					var dataS = Ext.decode(response.responseText).data;
				  	  							  Ext.Ajax.request({
				        	        	                    url: 'saveFlowCustomTemplateFromCoat.do',
				        	        	                    method: 'POST',
				        	        	                    params: {
				        	        	                    	customName: customName,
				        	        	                    	serviceId: scriptIid,
				        	        	                        data: dataS,
				        	        	                        audiUserLoginName: audiUser,
			  				  	  							    taskName: serviceName,
				        	        	                        flag: 1
				        	        	                    },
				        	        	                    success: function(response, options) {
				        	        	                        var success1 = Ext.decode(response.responseText).success;
				        	        	                        //var message1 = Ext.decode(response.responseText).message;
				        	        	                        if (success1) {
				        	        	                            Ext.MessageBox.show({
				        	        	                                title: "提示",
				        	        	                                msg: '已添加到常用任务中！',
				        	        	                                buttonText: {
				        	        	                                    yes: '确定'
				        	        	                                },
				        	        	                                buttons: Ext.Msg.YES
				        	        	                            });
				        	        	                        }
				        	        	                    },
				        	        	                    failure: function(result, request) {
				        	        	                        Ext.MessageBox.show({
				        	        	                            title: "提示",
				        	        	                            msg: "添加到常用任务失败！",
				        	        	                            buttonText: {
				        	        	                                yes: '确定'
				        	        	                            },
				        	        	                            buttons: Ext.Msg.YES
				        	        	                        });
				        	        	                    }
			
				        	        	                });
					  	  						},
					  	  						failure: function(result, request) {
					  	  							secureFilterRs(result,"操作失败！");
					  	  						}
					  	  					});
						                },
						                failure: function(result, request) {
						                	secureFilterRs(result,"操作失败！");
						                }
					            });
	        	            }
	        	        },
	        	        failure: function(result, request) {}
	        	    });
	        	}
	        	
	        }
	    });
	
}


//function forwardscriptcoatforexec(flowid, flag){
//	var serviceName=search_form1ForExecForFlow.getForm().findField("serviceName").getValue();
//	var state =search_form1ForExecForFlow.getForm().findField("state").getValue();
//	var startTime = search_form1ForExecForFlow.getForm().findField("startTime").getRawValue();
//	var endTime =search_form1ForExecForFlow.getForm().findField("endTime").getRawValue();
//	contentPanel.getLoader().load({
//	url: "forwardscriptcoatforexec.do",
//	scripts: true,
//	params : {
//			flowId:flowid,
//			flag:flag, 
//			forScriptFlow: 1,
//			filter_serviceName:serviceName,
//			filter_serviceState:state,
//			filter_serviceStartTime:startTime,
//			filter_serviceEndTime:endTime
//			}
//		});
//}
/**
 * @description bankCode001历史记录需求 弹出日志解析页面
 * @param  iid :脚本主键  uuid: 脚本uuid
 */
function openLogAnalize(iid,uuid){
	var 	historyWin = Ext.create('Ext.window.Window', {
					  		 title : '日志解析',
						    modal : true,
						    closeAction : 'destroy',
						    constrain : true,
						    autoScroll : true,
						    width : contentPanel.getWidth()+10,
						    height : contentPanel.getHeight(),
						    draggable : true,// 禁止拖动
						    resizable : true,// 禁止缩放
						    layout : 'fit',
				            loader: {
				                url: 'forwardLogAnalize.do',
				                params: {
				                   serviceiid:iid,
				                   scriptuuid:uuid
				                },
				                autoLoad: true,
				                scripts: true
				            }
					  	});
					historyWin.show();
}

function forwardscriptcoat1ForExecForFlow(flowid, flag){
	var serviceName=search_form1ForExecForFlow.getForm().findField("serviceName").getValue();
	var state =search_form1ForExecForFlow.getForm().findField("state").getValue();
	var startTime = search_form1ForExecForFlow.getForm().findField("startTime").getRawValue();
	var endTime =search_form1ForExecForFlow.getForm().findField("endTime").getRawValue();
	var taskName = search_form1ForExecForFlow.getForm().findField("taskName").getRawValue();
	var customName = search_form1ForExecForFlow.getForm().findField("customName").getRawValue();
	var filter_audiUser = search_form1ForExecForFlow.getForm().findField("audiUser").getValue();
	contentPanel.getLoader().load({
	url: "forwardscriptflowcoatforexecforwhite.do",
	scripts: true,
	params : {
			flowId:flowid,
			flag:flag, 
			forScriptFlow: 1,
			filter_serviceName:serviceName,
			filter_serviceState:state,
			filter_serviceStartTime:startTime,
			filter_serviceEndTime:endTime,
			filter_serviceTaskName : taskName,
			filter_serviceCustomName : customName,
			filter_audiUser : filter_audiUser
			}
		});
}

function forwardFileResult1ForExecForFlow(flowid, flag){
	contentPanel.getLoader().load({url: "filecompare.do",scripts: true,params : {flowid:flowid, flag:flag}});
}
//function forwardFileResult(flowid, flag){
//	contentPanel.getLoader().load({url: "filecompare.do",scripts: true,params : {flowid:flowid, flag:flag}});
//}
    
    
});

//function scriptCoatStop(coatid, flag){
//	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
//		if (btn == 'yes') {
//			Ext.Ajax.request({
//				url : 'scriptCoatStop.do',
//				method : 'POST',
//				params : {
//					coatid:coatid, 
//					flag:flag
//				},
//				success : function(response, request) {
//					var success = Ext.decode(response.responseText).success;
//					var message = Ext.decode(response.responseText).message;
//					if (success) {
//						Ext.Msg.alert('提示', message);
//					} else {
//						Ext.Msg.alert('提示', message);
//					}
//					scriptmonitor_storeindexExec.reload();
//				},
//				failure : function(result, request) {
//					secureFilterRs(result,"操作失败！");
//				}
//			});
//		}
//	})
//	
//}

var listStore = Ext.create("Ext.data.Store", {
    fields: ["listName", "listValue"],
    data: [
        { listName: "变更", listValue: 0 },
        { listName: "维护", listValue: 1 }
    ]
});
var listComBox = Ext.create ('Ext.form.ComboBox',
{
    editable : false,
    fieldLabel : "类别",
    labelWidth : 50,
    //hidden:hmmedStart,
    store : listStore,
    value : '变更',
    queryMode : 'local',
    margin:'10 0 0 0',
    width : 150,
    displayField : 'listName',
    valueField : 'listValue'
});

var flowId = "";
//单号输入窗口
var butterflyVerisonPanel = Ext.create('Ext.form.Panel', {
border: false,
width : '100%',
height: 200,
bodyPadding: 5,
layout: 'form',
items: [{
layout:'column',
border : false,
items: [
{
  labelWidth : 50,
  width: 240,
  xtype: 'textfield',
  fieldLabel: '单号',
  name: 'butterflyversion',
  padding : '0 10 0 0' 
},listComBox]
},{
	      layout:'column',
	      padding : '0 0 0 60' ,
	      border : false,
	      items: [
			{
			    xtype: 'button',
			    cls : 'Common_Btn',
			    text: '确定',
			    handler: function() {
			    	 WriteButterflyVerison();
			    }
			  },{
			        xtype: 'button',
			        cls : 'Common_Btn',
			        text: '取消',
			        handler: function() {
			        	butterflyVerisonPanel.getForm().findField("butterflyversion").setValue('');
			        	butterflyVerisonwin.hide();
			        }
			  }]
	    }
]
});
var butterflyVerisonwin = Ext.create('Ext.window.Window', {
	title : '单号',
	closeAction : 'hide',
	draggable : false,// 禁止拖动
	resizable : false,// 禁止缩放
	width : 270,
	height : 200,
	modal : true,
	items : [ butterflyVerisonPanel ]
})

function WriteButterflyVerison(){
	Ext.MessageBox.wait("信息验证中...","提示");
	var butterflyVerison =  butterflyVerisonPanel.getForm().findField("butterflyversion").getValue();
	if(butterflyVerison==null||butterflyVerison.trim()==""){
		Ext.Msg.alert('提示', '请填写单号!');
		return;
	}
	if(butterflyVerison.length>255){
		Ext.Msg.alert('提示', '单号长度不能大于255个字符!');
		return;
	}
	var listComBoxValue = listComBox.getValue();
	var oddNumbersType = 0;//单号类型
	if(listComBoxValue == 0||listComBoxValue=="变更")
	{
		oddNumbersType = 0;
	} else {//维护
		oddNumbersType = 1
	} 
	Ext.Ajax.request({
		url : 'updateScriptButterflyVersion.do',
		method : 'POST',
		params : {
			butterflyversion : butterflyVerison,
			iid : flowId,
			oddNumbersType:oddNumbersType
		},
		success : function(response, options) {
			var message = Ext.decode(response.responseText).message;
			Ext.Msg.alert('提示', message, function() {
				if (Ext.isIE) {
					CollectGarbage();
				}
				scriptflowmonitor_store.reload();
			});
		},
		failure : function(result, request) {
			Ext.Msg.alert('提示', "操作失败！");
		}

	});
	butterflyVerisonPanel.getForm().findField("butterflyversion").setValue('');
	butterflyVerisonwin.hide();
}
function writeVersion(iid){
	flowId = iid;
	butterflyVerisonwin.show();
}