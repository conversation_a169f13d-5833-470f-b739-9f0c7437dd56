<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var flowId3ForExecForFlow = '<%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>';
	
	var coatid3ForExecForFlow = '<%=request.getParameter("coatid")==null?"":request.getParameter("coatid")%>';
	
	
	var isWin3ForExecForFlow = '<%=request.getParameter("isWin")%>'; // 
	var serviceName3ForExecForFlow = '<%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%>';
	var startUser3ForExecForFlow = '<%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%>';
	var startUserFullName3ForExecForFlow = '<%=request.getAttribute("startUserFullName")==null?"":request.getAttribute("startUserFullName")%>';
	var startTime3ForExecForFlow = '<%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%>';
	var endTime3ForExecForFlow = '<%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%>';
	
	var filter_scriptNameForExecForFlow = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_stateForExecForFlow  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTimeForExecForFlow ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTimeForExecForFlow = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceNameForExecForFlow = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceStateForExecForFlow  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTimeForExecForFlow ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTimeForExecForFlow = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var planId='<%=request.getParameter("planId")==null?"":request.getParameter("planId")%>';
	var scenceId='<%=request.getParameter("scenceId")==null?"":request.getParameter("scenceId")%>';
	var stepId='<%=request.getParameter("stepId")==null?"":request.getParameter("stepId")%>';
	var hidereturn='<%=request.getAttribute("hidereturn")==null?"":request.getAttribute("hidereturn")%>';
	var switchFlag = '<%=request.getParameter("switchFlag")%>';
	var isGroupService='<%=request.getParameter("isGroupService")==null?"":request.getParameter("isGroupService")%>';
	var hegui='<%=request.getAttribute("hegui")%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptmonitor/dbaasscriptFlowserverForExec.js"></script>

</head>
<body>
	<div id="dbaasswitchruninfoins_div_exec_ForFlow" style="width: 100%; height: 75%">
	</div>
</body>
</html>