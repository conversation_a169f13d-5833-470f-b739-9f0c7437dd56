Ext.onReady(function() {
    destroyRubbish();
    var rowExpanderLog3ForExecForFlow;
	var scriptmonitorinfoins_store3ForExecForFlow;
	var scriptmonitorinfoins_grid_execForFlow;
	var interV3ForExecForFlow = 10;
	var interPV3ForExecForFlow = 20;
	var lastId3ForExecForFlow;
	var lastRowIndex3ForExecForFlow;
	var refreshObjForExecForFlow;
	var refreshObjShellOutputForExecForFlow
	var lastrequestId3ForExecForFlow;
	var lastiip3ForExecForFlow;
	var lastiport3ForExecForFlow;
	var flag3ForExecForFlow = 1; // 0:测试     1:生成
	var oldTitleExecHistory = contentPanel.title;
	var setTitleFlag = false;
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'scriptName',
            type: 'string'
        },{
            name: 'servicesName',
            type: 'string'
        },
        {
            name: 'taskName',
            type: 'string'
        },{
            name: 'startUser',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }, {name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'},
        {
            name: 'timeOut',
            type: 'int'
        }]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
//        listeners: {
//            select: function(me, record, index, eOpts) { // 选择复选框事件
//                flowMesshis3ForExecForFlow(record.data.iid, index);
//            },
//            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
//                flowMesshis3ForExecForFlow(record.data.iid, index);
//            }
//        }
    });
    
    var hosName = new Ext.form.TextField({
		name : 'hosName',
		fieldLabel : '计算机名',
		emptyText : '--请输入计算机名--',
		labelWidth : 70,
//		padding : '5',
		width :'20%',
        labelAlign : 'right'
	});
	
	var ip = new Ext.form.TextField({
		name : 'ip',
		fieldLabel : 'Agent地址',
		emptyText : '--请输入Agent地址--',
		labelWidth : 100,
//		padding : '5',
		width :'20%',
        labelAlign : 'right'
	});
    var execStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"1000", "name":"全部"},
			{"id":"-1", "name":"未运行"},
			{"id":"5", "name":"忽略"},
			{"id":"10", "name":"运行"},
			{"id":"20", "name":"完成"},
			{"id":"30", "name":"异常"},
			{"id":"60", "name":"已终止"}
		]
	});
	var execStatus = Ext.create('Ext.form.field.ComboBox', {
		name : 'execStatus',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : '执行状态',
		displayField : 'name',
		valueField : 'id',
//		padding : '5',
		editable : false,
		emptyText : '--请选择执行状态--',
		store : execStatusStore,
		width :'20%',
		value:'1000',
		labelAlign : 'right'
	});
	
    var startTime = Ext.create('Go.form.field.DateTime', {
    	name: 'startTime',
    	id:'startTime',
		fieldLabel: '开始时间',
		labelWidth : 70,
		width : '20%',
        labelAlign : 'right',
        format: 'Y-m-d H:i:s',
        listeners:{
			change:function(dateField,newValue,oldValue,eOpts ){
				var EDate=Ext.getCmp('endTime').getValue();
				if(EDate != "" && EDate !=null && EDate < newValue){
					Ext.Msg.alert("提示","开始时间不能晚于结束时间！");
	 				startTime.setValue("");
				}
			},
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	scriptmonitorinfoins_grid_execForFlow.ipage.moveFirst();
                }
            }
		}
	});
	
	var endTime = Ext.create('Go.form.field.DateTime', {
    	name: 'endTime',
    	id:'endTime',
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '20%',
    	labelAlign : 'right',
    	format: 'Y-m-d H:i:s',
    	listeners:{
			change:function(dateField,newValue,oldValue,eOpts){
				var SDate =Ext.getCmp('startTime').getValue();
				if(SDate != "" && SDate !=null && SDate > newValue){
					Ext.Msg.alert("提示","结束时间不能早于开始时间！");
	 				endTime.setValue("");
				}
			},
			specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	scriptmonitorinfoins_grid_execForFlow.ipage.moveFirst();
                }
            }
		}
    });
	 
	
	/*var pageFreshTime =Ext.create('Ext.form.field.Number', {
		fieldLabel :'自动刷新',
		labelWidth : 70,
        minValue: 20,
        name: "pageFreshTime",
        width :50,//'12.5%'
        labelAlign : 'right',
        value: interPV3ForExecForFlow
    });*/
	 var pageFreshTime = new Ext.form.field.Number({
	    	width: 50,
	        minValue: 20,
	        name: "pageFreshTime",
	        value: interPV3ForExecForFlow
	    });
	 
	 var oam_act_form = Ext.create('Ext.form.Panel', {
			frame : true,
			border : false,
			bodyCls:'fm-spinner',
			layout : {
				type : 'hbox',
				align : 'middle'
			},
			defaults : {
				anchor : '100%'
			},
			items : [
					{
	            xtype: "label",
	            text: "自动刷新"
	        },pageFreshTime , {
						xtype : 'label',
						text : '    秒'
					} ]
		});
	
    var info_form = Ext.create('Ext.ux.ideal.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        //cls:'sc_tlbar_height',
        baseCls:'customize_gray_back',
        iqueryFun : function(){
	    	 scriptmonitorinfoins_grid_execForFlow.ipage.moveFirst();
	    },
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
//			items: [sName, stateField,itaskName, startUserField]
			items: [hosName, ip,execStatus, startTime,endTime]
		},
		{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
//			items: [startTimeField, endTimeField, runTimeField]
			items:[/*pageFreshTime, {
		            xtype: "label",
		            text: "  秒"
		        }*//*{
		            xtype: 'button',
		            cls: 'Common_Btn',
		            //											id : 'button2',
		            text: '刷  新',
		            listeners: {
		                click: function() {
		                    if (refreshObjForExecForFlow) {
		                        clearInterval(refreshObjForExecForFlow);
		                    }
		                    refreshPage3ForExecForFlow();
		                    // var interValue =
		                    // document.getElementById('pageFreshTime').value;
		                    var interValue = pageFreshTime.getValue();
		                    interPV3ForExecForFlow = interValue;
		                    if (interPV3ForExecForFlow < 20) {
		                        interPV3ForExecForFlow = 20;
		                    }
		                    refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);
		                }
		            }
		        },*/'->',{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						 scriptmonitorinfoins_grid_execForFlow.ipage.moveFirst();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						 info_form.getForm().reset();
					}
				},{
		            xtype: 'button',
		            cls: 'Common_Btn',
		            text: '终止',
					hidden: hiddenOperateButton,
		            listeners: {
		                click: function() {
		                    var data = getCHKBoxIds();
		                    if (data.length == 0) {
		                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
		                        return;
		                    } else {
		                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
		                        function(button, text) {
		                            if (button == "yes") {
		                                if (data == '-1') {
		                                    Ext.Msg.alert('提示', '该步骤已经结束，无需终止!');
		                                    scriptmonitorinfoins_store3ForExecForFlow.reload();
		                                    return;
		                                }
		                                Ext.MessageBox.wait("数据处理中...", "提示");
		                                Ext.Ajax.request({
		                                    url: 'scriptServiceForWhiteShellKill.do',
		                                    params: {
		                                        flag: flag3ForExecForFlow,
		                                        insIds: data
		                                    },
		                                    method: 'POST',
		                                    success: function(response, opts) {
		                                        var success = Ext.decode(response.responseText).success;
		                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
		                                        // 当后台数据同步成功时
		                                        if (success) {
		                                            scriptmonitorinfoins_store3ForExecForFlow.reload();
		                                        }
		                                    }
		                                });
		                            }
		                        });
		                    }
		                }
		            }
		        },{
		            xtype: 'button',
		            cls: 'Common_Btn',
		            text: '忽略',
					hidden: hiddenOperateButton,
		            listeners: {
		                click: function() {
		                	var checkFlag = checkSelectState();
		                	if(!checkFlag){
		                		Ext.Msg.alert('提示', '只允许忽略异常状态的记录！');
		                        return;
		                	}
		                    var data = getCHKBoxIdsArray(0);
		                    if (data.length == 0) {
		                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
		                        return;
		                    } else {
		                        Ext.Msg.confirm("请确认", "是否真的要进行<忽略>操作？",
		                        function(button, text) {
		                            if (button == "yes") {
		                                Ext.MessageBox.wait("数据处理中...", "提示");
		                                Ext.Ajax.request({
		                                    url: 'skipScriptForWhiteServiceShell.do',
		                                    params: {
		                                        flag: flag3ForExecForFlow,
		                                        insIds: data
		                                    },
		                                    method: 'POST',
		                                    success: function(response, opts) {
		                                        var success = Ext.decode(response.responseText).success;
		                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
		                                        // 当后台数据同步成功时
		                                        if (success) {
		                                            scriptmonitorinfoins_store3ForExecForFlow.reload();
		                                        }
		                                    }
		                                });
		                            }
		                        });
		                    }
		                }
		            }
		        },
		        {
					xtype : 'button',
					text : '导出',
					cls : 'Common_Btn',
					handler : function() {
						var idJsonArray = getCHKBoxIdsArray(1);
						if ('' == workitemidFor) {
							workitemidFor = 0
						}
						$.fileDownload('exportCoatResult.do',{
							  httpMethod: 'POST',
							  traditional: true,
							  data:{ insIds :idJsonArray,
		 								coatId : whiteScriptCoatid3ForExecForFlow,
		 								flag : 1,
								  		workitemid:workitemidFor
		 					  },
							  successCallback: function(url){
							     console.log("执行历史——选择agent导出，idarray："+idJsonArray);
							  },
							  failCallback: function (html, url) {
							   		 Ext.Msg.alert('提示', '导出失败！');
		                       		  return;
							   }
						  });   
						
//						 Ext.Ajax.request({
//							url : 'exportCoatResult.do',
//							params : {
//								insIds :data,
//								coatId : whiteScriptCoatid3ForExecForFlow,
//								flag : 1
//							},
//							success : function(response, opts) {
//								
//							},
//							failure : function(response, opts) {
//								Ext.Msg.alert('提示', '导出失败！');
//		                        return;
//							}
//						});
					}
				},
		        {
		            xtype: 'button',
		            cls: 'Common_Btn',
		            text: '返回',
		            listeners: {
		                click: function() {
		                	if(whiteScriptHidereturn=="1"){
		                		returnBackForExec();
		                	}else if(whiteScriptHidereturn=="taskMonitor"){//返回任务监控
		                		returnBackForTaskMonitor();
		                	}else{
		                		returnBack3ForExecForFlow();
		                	}
		                }
		            }
		        }
			]
		}]
        
    });
    
    
    scriptmonitorinfoins_store3ForExecForFlow = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flag3ForExecForFlow + '&coatid=' + whiteScriptCoatid3ForExecForFlow,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
                flowMesshisRefresh3ForExecForFlow(lastId3ForExecForFlow, lastRowIndex3ForExecForFlow);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: whiteScriptCoatid3ForExecForFlow
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        //stateField.setValue(res.state);
                        //itaskName.setValue(res.itaskName);
                        //endTimeField.setValue(res.endTime);
                        //runTimeField.setValue(res.runTime);
                        
                         //contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
						// info_form.setTitle(contentPanel.title+"——【服务名称】："+whiteScriptServiceName3ForExecForFlow+"，"+"【任务名称】："+res.itaskName);//将contentPanel标题显示在查询Form上
                        if(whiteScriptTaskNameSwitch){
                        	contentPanel.setTitle(contentPanel.title+"——【服务名称】："+whiteScriptServiceName3ForExecForFlow);
                        	setTitleFlag = true;
                        }else{
                        	if(!setTitleFlag){
                            	contentPanel.setTitle(contentPanel.title+"——【服务名称】："+whiteScriptServiceName3ForExecForFlow+"，"+"【任务名称】："+res.itaskName);
                            	setTitleFlag = true;
                            }
                        }
                        
//                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
//                        $("#scriptState").html(res.state);
//                        $("#scriptEndTime").html(res.endTime);
//                        $("#scriptRunTime").html(res.runTime + " 秒");
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });
    
    scriptmonitorinfoins_store3ForExecForFlow.on('beforeload', function (store, options) {
	    var new_params = {  
	    		 hosName:hosName.getValue(),
	    		 ip:ip.getValue(),
	    		 execStatus:execStatus.getValue(),
	    		 startTime:startTime.getValue() == null ?0:startTime.getValue().getTime(),
	    		 endTime:endTime.getValue() == null ?0:endTime.getValue().getTime()
	    };
	    Ext.apply(scriptmonitorinfoins_store3ForExecForFlow.proxy.extraParams, new_params);
    });
    
    var scriptmonitorinfoins_columns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		},{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },{
        text: '超时状态',
        dataIndex: 'timeOut',
        width: 80,
        hidden : timeOutSwitchForTaskExec,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 0) {
                backValue = '<span class="Ignore State_Color">否</span>';
            } else{
                backValue = '<span class="Kill_red State_Color">是</span>';
            } 
            return backValue;
        }
    },{ text: '系统名称',  dataIndex: 'sysName',hidden: !CMDBflag,width:80},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,width:80},
    { text: '计算机名',  dataIndex: 'hostName',width:100,hidden:whiteScriptHostNameSwitch},
    { text: '服务名称',  dataIndex: 'servicesName',width:150,flex:1},
    { text: '脚本名称',  dataIndex: 'scriptName',width:150,flex:1},
    { text: '任务名称',  dataIndex: 'taskName',width:140,flex:1,hidden:whiteScriptTaskNameSwitch},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        width: 100
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100,
        hidden: agentPortSwitchForTaskExec
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 150
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 150
    },
    {
        text: '启动人',
        dataIndex: 'startUser',
        width: 100
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
		text : '操作',
		xtype : 'actiontextcolumn',
		width : 190,
		menuDisabled : true,
//			flex:1,
		items : [{
			text : '详情',
			iconCls : 'monitor_search',
			getClass : function(v, metadata, record) {
 				    var state = record.get("state");
					if (state == '-1' || state == '1'  ) {
						return 'x-hidden';
					}
			},
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
	            var agentIp =grid.getStore().data.items[rowIndex].data.agentIp;
	            var agentPort =grid.getStore().data.items[rowIndex].data.agentPort;
	            var state = grid.getStore().data.items[rowIndex].data.state;
	            openActWindowForExecForFlow(iid,state,agentIp,agentPort);
			}
		}, {
			text : '重试',
			iconCls : 'monitor_execute',
 			getClass : function(v, metadata, record) {
 				    var state = record.get("state");
					if (state != '30' && state != '40' && state != '50') {
						return 'x-hidden';
					}
			},
			handler : function(grid, rowIndex) {
				var state = grid.getStore().data.items[rowIndex].data.state;
				if(10 == state){
					Ext.Msg.alert('提示', '运行中的实例无法重试!');
					return;
				}
				if(20 == state){
					Ext.Msg.alert('提示', '完成的实例无法重试!');
					return;
				}
				if(60 == state){
					Ext.Msg.alert('提示', '已终止的实例无法重试!');
					return;
				}
				var iid = grid.getStore().data.items[rowIndex].data.iid;
	            var state = grid.getStore().data.items[rowIndex].data.state;
	            reTryScriptServer3ForExecForFlow(iid,state);
			}
		},{
			text : '终止',
			iconCls : 'monitor_skip',
 			getClass : function(v, metadata, record) {
 				    var state = record.get("state");
					if (state != '-1' && state != '1' ) {
						return 'x-hidden';
					}
			},
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
	            var state = grid.getStore().data.items[rowIndex].data.state;
	            scriptServerStop3ForExecForFlow(iid,state);
			}
		},{
			text : '忽略',
			iconCls : 'monitor_skip',
 			getClass : function(v, metadata, record) {
 				    var state = record.get("state");
					if (state != '30' && state != '40' && state != '50'  && state != '-1' && state != '1') {
						return 'x-hidden';
					}
			},
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
	            var state = grid.getStore().data.items[rowIndex].data.state;
	            skipScriptServer3ForExecForFlow(iid,state);
			}
		}]
    }
    //************************/
//    {
//        text: '操作',
//        dataIndex: 'stepOperation',
//        width: 190,
//        menuDisabled : true,
//        renderer: function(value, p, record, rowIndex) {
//            var iid = record.get('iid'); // 其实是requestID
//            var state = record.get('state');
//            var zoomStr = "";
//            if (whiteScriptIsWin3ForExecForFlow != 1) {
//                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetail3ForExecForFlow(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
//            }
//            zoomStr = '';
//            if (state == '30' || state == '40' || state == '50') {
//                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openActWindowForExecForFlow(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' 
//                + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServer3ForExecForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' +
//                '<a href="javascript:void(0)" onclick="skipScriptServer3ForExecForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
//            } else if (state == '-1' || state == '1') {
//                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="scriptServerStop3ForExecForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_termination"></img>&nbsp;终止</a>&nbsp;&nbsp;' + 
//                zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServer3ForExecForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
//            } else {
//                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openActWindowForExecForFlow(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
//            }
//        }
//    }
    ];

    rowExpanderLog3ForExecForFlow = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForExec{iid}">', '<pre  onselectstart="return true" id="steptextareahisForExec{iid}"  class="monitor_desc"></pre>', '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span">自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshId3ForExecForFlow" name="rowFreshId3ForExecForFlow" >&nbsp;秒</span>', '&nbsp;&nbsp;&nbsp;<input type="button" value="刷新" onclick="loadShelloutputhis3ForExecForFlow({iid},\'{agentIp}\',{agentPort})" class="Common_Btn">', '&nbsp;&nbsp;&nbsp;<input type="button" value="终止" onclick="scriptServerStop3ForExecForFlow({iid},{state})" class="Common_Btn">', '</div>']
    });
    
//    var pageFreshTime = new Ext.form.field.Number({
//    	width: 50,
//        minValue: 20,
//        name: "pageFreshTime",
//        value: interPV3ForExecForFlow
//    });
    
//    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//        store: scriptmonitorinfoins_store3ForExecForFlow,
//        dock: 'bottom',
//        displayInfo: true,
//        items: [{
//            xtype: "label",
//            text: "自动刷新"
//        },
//        pageFreshTime,
//        {
//            xtype: "label",
//            text: "  秒"
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            //											id : 'button2',
//            text: '刷  新',
//            listeners: {
//                click: function() {
//                    if (refreshObjForExecForFlow) {
//                        clearInterval(refreshObjForExecForFlow);
//                    }
//                    refreshPage3ForExecForFlow();
//                    // var interValue =
//                    // document.getElementById('pageFreshTime').value;
//                    var interValue = pageFreshTime.getValue();
//                    interPV3ForExecForFlow = interValue;
//                    if (interPV3ForExecForFlow < 20) {
//                        interPV3ForExecForFlow = 20;
//                    }
//                    refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);
//                }
//            }
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            text: '终  止',
//            listeners: {
//                click: function() {
//                    var data = getCHKBoxIds();
//                    if (data.length == 0) {
//                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
//                        return;
//                    } else {
//                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
//                        function(button, text) {
//                            if (button == "yes") {
//                                if (data == '-1') {
//                                    Ext.Msg.alert('提示', '该步骤已经结束，无需终止!');
//                                    scriptmonitorinfoins_store3ForExecForFlow.reload();
//                                    return;
//                                }
//                                Ext.MessageBox.wait("数据处理中...", "提示");
//                                Ext.Ajax.request({
//                                    url: 'scriptServiceShellKill.do',
//                                    params: {
//                                        flag: flag3ForExecForFlow,
//                                        insIds: data
//                                    },
//                                    method: 'POST',
//                                    success: function(response, opts) {
//                                        var success = Ext.decode(response.responseText).success;
//                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
//                                        // 当后台数据同步成功时
//                                        if (success) {
//                                            scriptmonitorinfoins_store3ForExecForFlow.reload();
//                                        }
//                                    }
//                                });
//                            }
//                        });
//                    }
//                }
//            }
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            text: '返  回',
//            listeners: {
//                click: function() {
//                	returnBack3ForExecForFlow();
//                }
//            }
//        }]
//    });
	/**
	 * @param flag : 0 忽略使用 1 导出使用
	 */
	function getCHKBoxIdsArray(flag) {
        var records = scriptmonitorinfoins_grid_execForFlow.getView().getSelectionModel().getSelection();
        var idsArray = new Array();
        Ext.Array.each(records,
        function(rec) {
            var state = rec.get('state'); // -1 60 20
            if (flag ==0 &&  state =='30') {
            	idsArray.push(rec.get('iid'));
            }else if(flag ==1){
            	idsArray.push(rec.get('iid'));
            }
        });
        return  idsArray;
    }
    
    /**
     * @description 校验选择的记录是否可以忽略
     */
    function checkSelectState() {
        var records = scriptmonitorinfoins_grid_execForFlow.getView().getSelectionModel().getSelection();
        var returnFlag = true;
        Ext.Array.each(records,
        function(rec) {
            var state = rec.get('state'); // -1 60 20
            if (state !='30') {
    			returnFlag = false;
                return false;
            }
        });
        return  returnFlag;
    }
    
    
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_grid_execForFlow.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if ( state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
 
    
    scriptmonitorinfoins_grid_execForFlow = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
        store: scriptmonitorinfoins_store3ForExecForFlow,
        cls:'customize_panel_back sc_tab_height',
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		padding : grid_space,
        ipageItems : [
                      oam_act_form,
                      {
      		            xtype: 'button',
      		            cls: 'Common_Btn',
      		            //											id : 'button2',
      		            text: '刷  新',
      		            listeners: {
      		                click: function() {
      		                    if (refreshObjForExecForFlow) {
      		                        clearInterval(refreshObjForExecForFlow);
      		                    }
      		                    refreshPage3ForExecForFlow();
      		                    // var interValue =
      		                    // document.getElementById('pageFreshTime').value;
      		                    var interValue = pageFreshTime.getValue();
      		                    interPV3ForExecForFlow = interValue;
      		                    if (interPV3ForExecForFlow < 20) {
      		                        interPV3ForExecForFlow = 20;
      		                    }
      		                    refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);
      		                }
      		            }
      		        }
                      ],
        border: false,
        columnLines: true,
        columns: scriptmonitorinfoins_columns,
      //  bbar: pageBar,
//        ipageItems : [{
//            xtype: "label",
//            text: "自动刷新"
//        },
//        pageFreshTime,
//        {
//            xtype: "label",
//            text: "  秒"
//        },
//        {
//            xtype: 'button',
//            cls: 'Common_Btn',
//            //											id : 'button2',
//            text: '刷  新',
//            listeners: {
//                click: function() {
//                    if (refreshObjForExecForFlow) {
//                        clearInterval(refreshObjForExecForFlow);
//                    }
//                    refreshPage3ForExecForFlow();
//                    // var interValue =
//                    // document.getElementById('pageFreshTime').value;
//                    var interValue = pageFreshTime.getValue();
//                    interPV3ForExecForFlow = interValue;
//                    if (interPV3ForExecForFlow < 20) {
//                        interPV3ForExecForFlow = 20;
//                    }
//                    refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);
//                }
//            }
//        }
//        ],
        selModel: selModel,
        plugins: [rowExpanderLog3ForExecForFlow],
        viewConfig: {
        	enableTextSelection:true,
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                return 'norowexpandblah';
            }
        }
    });

    scriptmonitorinfoins_grid_execForFlow.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
        interV3ForExecForFlow = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshId3ForExecForFlow').innerText = interV3ForExecForFlow;
        } else {
            document.getElementById('rowFreshId3ForExecForFlow').innerHTML = interV3ForExecForFlow;
        }
        loadShelloutputhis3ForExecForFlow(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
    });
    scriptmonitorinfoins_grid_execForFlow.view.on('collapsebody',
    function(rowNode, record, expandRow, eOpts) {
        lastId3ForExecForFlow = 0;
        lastRowIndex3ForExecForFlow = 0;
        if (refreshObjShellOutputForExecForFlow) {
            clearInterval(refreshObjShellOutputForExecForFlow);
        }
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoins_div_exec_ForFlowWhiteScript",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: true,
        items: [info_form, scriptmonitorinfoins_grid_execForFlow]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

    function refreshPage3ForExecForFlow() {
    	if(getDivId(contentPanel.getLoader().target.html)=='switchruninfoins_div_exec_ForFlowWhiteScript') {
    		scriptmonitorinfoins_store3ForExecForFlow.reload();
    	}
    }

    if (refreshObjForExecForFlow) {
        clearInterval(refreshObjForExecForFlow);
    }
    refreshObjForExecForFlow = setInterval(refreshPage3ForExecForFlow, interPV3ForExecForFlow * 1000);
	
    
    /*************************************/
function flowMesshis3ForExecForFlow(iruninfoinsid, rowIndex) {
    lastId3ForExecForFlow = iruninfoinsid;
    lastRowIndex3ForExecForFlow = rowIndex;
    //var record = scriptmonitorinfoins_store3ForExecForFlow.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForExecForFlow.getRange(0, scriptmonitorinfoins_store3ForExecForFlow.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLog3ForExecForFlow.recordsExpanded[records[i].internalId]) {
            rowExpanderLog3ForExecForFlow.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_store3ForExecForFlow.getAt(rowIndex);
     rowExpanderLog3ForExecForFlow.toggleRow(rowIndex, record);
}

function openActWindowForExecForFlow(requestId, state,agentIp,agentPort) {
	var runningWindow = null;
//	var fp2 = null;
//	var cmdStr = null;
	var surl = "getScriptExecOutput.do";
	var h = window.innerHeight || document.documentElement.clientHeight
			|| document.body.clientHeight;
	function getData(surl,state, requestId, agentIp, agentPort, cmdValue) {
		Ext.Ajax.request({
			url : surl,
			params : {
				requestId : requestId,
				agentIp : agentIp,
				agentPort : agentPort,
				flag:flag3ForExecForFlow,
				input:cmdValue
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				//var oldValue = Ext.getCmp('baseinfo_form_forflowtest').getForm().findField("actOutPut").getValue();
				var output = msg.message;
				var json = {
					actName :whiteScriptServiceName3ForExecForFlow+"-"+agentIp+"-"+agentPort ,
					actInsName : whiteScriptServiceName3ForExecForFlow,
					actOutPut : output
				};
				Ext.getCmp('baseinfo_form_forflowtest').getForm().setValues(json);
				actName.getEl().dom.innerHTML='活动名：    <div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:80%;height:35px ;margin-bottom:10px"><pre>'+actName.getValue()+'</pre></div>';
				actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:80%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+actOutPut.getValue()+'</pre></div>';
			},
			failure : function(response, opts) {

			}

		});
	}
	var cmdStr = new Ext.form.TextField({
		anchor : '100%',
		labelWidth : 70,
		fieldLabel : 'CMD',
		disabled:(state==10)?false:true,
		listeners : {
			specialkey : function(textfield, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var cmdV = cmdStr.getValue();
					cmdStr.setValue("");
					getData(surl,state, requestId, agentIp, agentPort, cmdV);
				}
			}
		}
	});
	var actName= Ext.create ('Ext.form.field.Text',
			{
				fieldLabel : '活动名',
				labelWidth : 70,
				name : 'actName',
				margin :'10 0 5 0',
				anchor : '95%' // anchor width by percentage
			});
	var actOutPut = Ext.create ('Ext.form.field.TextArea',
	{
		fieldLabel : '活动日志',
		labelWidth : 70,
		grow : true,
		height : h-200,
		name : 'actOutPut',
		margin :'15 0 5 0',
		anchor : '95%'
	});
	var fp2 = new Ext.form.Panel({
		border : false,
		id : 'baseinfo_form_forflowtest',
		height : '100%',
		padding : 5,
		fieldDefaults : {
			labelWidth : 60,
			labelAlign : 'right'
		},
		defaultType : 'textfield',
		items : [  actName, {
			fieldLabel : '实例名',
			labelWidth : 70,
			name : 'actInsName',
			hidden : true,
			anchor : '100%' // anchor width by percentage
		},actOutPut,cmdStr ]
	});

	if (runningWindow == undefined || !runningWindow.isVisible()) {
		runningWindow = Ext.create('Ext.window.Window', {
			title : whiteScriptServiceName3ForExecForFlow,
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 600,
			height : h - 95,
			items : [ fp2 ],
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'bottom',
				layout: {pack: 'center'},
				items : [
				 {
					text : '刷新',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						getData(surl,state, requestId, agentIp, agentPort, null);
						cmdStr.setValue("");
					}
				}, {
					text : '终止',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						scriptServerStop3ForExecForFlow(requestId,state)
					}
				} ]
			} ],
			layout : 'fit'
		});
	}
	runningWindow.show();
	getData(surl,state, requestId, agentIp, agentPort, null);

		
	
}

function loggerDetail3ForExecForFlow(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForExecForFlow) {
        clearInterval(refreshObjShellOutputForExecForFlow);
    }
    if (refreshObjForExecForFlow) {
        if (refreshObjShellOutputForExecForFlow) {
            clearInterval(refreshObjShellOutputForExecForFlow);
        }
        clearInterval(refreshObjForExecForFlow);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
//            flowId: whiteScriptFlowId3ForExecForFlow,
            coatId: whiteScriptCoatid3ForExecForFlow,
            flag: flag3ForExecForFlow
        }
    });
}

function flowMesshisRefresh3ForExecForFlow(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    //var record = scriptmonitorinfoins_store3ForExecForFlow.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForExecForFlow.getRange(0, scriptmonitorinfoins_store3ForExecForFlow.getCount());
    var rowFreshValue = document.getElementById('rowFreshId3ForExecForFlow').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForExecForFlow = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForExecForFlow').innerText = interV3ForExecForFlow;
    } else {
        document.getElementById('rowFreshId3ForExecForFlow').innerHTML = interV3ForExecForFlow;
    }

    rowExpanderLog3ForExecForFlow.toggleRow(lastRowIndex3ForExecForFlow, records[lastRowIndex3ForExecForFlow]);
}

function loadShelloutputhis3ForExecForFlow(requestId, iip, iport) {
    lastrequestId3ForExecForFlow = requestId;
    lastiip3ForExecForFlow = iip;
    lastiport3ForExecForFlow = iport;
    if (refreshObjShellOutputForExecForFlow) {
        clearInterval(refreshObjShellOutputForExecForFlow);
    }
    var rowFreshValue = document.getElementById('rowFreshId3ForExecForFlow').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForExecForFlow = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForExecForFlow').innerText = interV3ForExecForFlow;
    } else {
        document.getElementById('rowFreshId3ForExecForFlow').innerHTML = interV3ForExecForFlow;
    }
    // document.getElementById('rowFreshId3ForExecForFlow').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfo3ForExecForFlow(requestId, iip, iport);
    refreshObjShellOutputForExecForFlow = setInterval(function() {
        loadShelloutputhisInfo3ForExecForFlow(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfo3ForExecForFlow(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForExec' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: flag3ForExecForFlow
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
	        document.getElementById(desc).innerHTML = msg.message; 
        },
        failure: function(response, opts) {
             document.getElementById(desc).innerHTML = '获取执行信息失败';
        }

    });
}
function scriptServerStop3ForExecForFlow(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
            	Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_store3ForExecForFlow.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceForWhiteShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag3ForExecForFlow
                },
                success: function(response, request) {
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    scriptmonitorinfoins_store3ForExecForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServer3ForExecForFlow(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag3ForExecForFlow
                },
                success: function(response, request) {
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    scriptmonitorinfoins_store3ForExecForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServer3ForExecForFlow(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptForWhiteServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag3ForExecForFlow
                },
                success: function(response, request) {
                    var message = Ext.decode(response.responseText).message;
                    Ext.Msg.alert('提示', message);
                    scriptmonitorinfoins_store3ForExecForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function returnBackForExec() {
    var lastIdForExec = '';
    if (refreshObjShellOutputForExec) {
        clearInterval(refreshObjShellOutputForExec);
    }
    if (refreshObjForExec) {
        if (refreshObjForExec) {
            clearInterval(refreshObjForExec);
        }
        clearInterval(refreshObjForExec);
    }

    contentPanel.getLoader().load({
        url: "forwardscriptcoatforexecforwhite.do",
        scripts: true,
        params: {
			flowId : whiteScriptFlowId3ForExecForFlow,
			flag : 1,
        	forScriptFlow: "",
        	filter_scriptName:whiteScriptFilter_scriptNameForExecForFlow,
		    filter_state:whiteScriptFilter_stateForExecForFlow,
		    filter_startTime:whiteScriptFilter_startTimeForExecForFlow,
		    filter_endTime:whiteScriptFilter_endTimeForExecForFlow,
		    filter_serviceName:whiteScriptFilter_serviceNameForExecForFlow,
			filter_Ip:whiteScriptFilter_IpForExecForFlow,
		    filter_serviceCustomName :whiteScriptFilter_serviceCustomNameForExecForFlow,
			filter_serviceState:whiteScriptFilter_serviceStateForExecForFlow,
			filter_serviceStartTime:whiteScriptFilter_serviceStartTimeForExecForFlow,
			filter_serviceEndTime:whiteScriptFilter_serviceEndTimeForExecForFlow
        }
    });
    contentPanel.setTitle(oldTitleExecHistory);
} 
/**
 * 返回任务监控页面
 */
function returnBackForTaskMonitor() {
    
    contentPanel.getLoader().load({
        url: "initTaskMonitor.do",
        scripts: true
    });
    contentPanel.setTitle(oldTitleExecHistory);
    
    //contentPanel.getHeader().show();
}
function returnBack3ForExecForFlow() {
    lastId3ForExecForFlow = '';
    if (refreshObjShellOutputForExecForFlow) {
        clearInterval(refreshObjShellOutputForExecForFlow);
    }
    if (refreshObjForExecForFlow) {
        if (refreshObjShellOutputForExecForFlow) {
            clearInterval(refreshObjShellOutputForExecForFlow);
        }
        clearInterval(refreshObjForExecForFlow);
    }

    contentPanel.getLoader().load({
        url: "forwardscriptflowcoatforexecforwhite.do",
        scripts: true,
        params: {
        	flowId : whiteScriptFlowId3ForExecForFlow,
			flag : 1,
			forScriptFlow: 1,
			fromMenu:whiteScriptFromMenu,
        	filter_scriptName:whiteScriptFilter_scriptNameForExecForFlow,
		    filter_state:whiteScriptFilter_stateForExecForFlow,
		    filter_startTime:whiteScriptFilter_startTimeForExecForFlow,
		    filter_endTime:whiteScriptFilter_endTimeForExecForFlow,
		    filter_serviceName:whiteScriptFilter_serviceNameForExecForFlow,
			filter_Ip:whiteScriptFilter_IpForExecForFlow,
			filter_serviceState:whiteScriptFilter_serviceStateForExecForFlow,
			filter_serviceCustomName :whiteScriptFilter_serviceCustomNameForExecForFlow,
			filter_serviceStartTime:whiteScriptFilter_serviceStartTimeForExecForFlow,
			filter_serviceEndTime:whiteScriptFilter_serviceEndTimeForExecForFlow,
			planId:whiteScriptPlanId,
			scenceId:whiteScriptScenceId,
			stepId:whiteScriptStepId
        }
    });
    contentPanel.setTitle(oldTitleExecHistory);
    //contentPanel.getHeader().show();
}
});

//function resultExport2ForExecForFlow(coatid, flag) {
////	window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag='
////			+ flag;
//	
//	
//}

