var rowExpanderLog3ForFlow;
var scriptmonitorinfoins_store3ForFlow;
var scriptmonitorinfoins_grid3ForFlow;
var interV3ForFlow = 10;
var interPV3ForFlow = 20;
var lastId3ForFlow;
var lastRowIndex3ForFlow;
var lastrequestId3ForFlow;
var lastiip3ForFlow;
var lastiport3ForFlow;
var flag3ForFlow = 0; // 0:测试     1:生成
Ext.onReady(function() {
    destroyRubbish();
    var itemsPerPage = 30;
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }, {name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'}]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
//        listeners: {
//            select: function(me, record, index, eOpts) { // 选择复选框事件
//                flowMesshis3ForFlow(record.data.iid, index);
//            },
//            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
//                flowMesshis3ForFlow(record.data.iid, index);
//            }
//        }
    });
    scriptmonitorinfoins_store3ForFlow = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize : itemsPerPage,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flag3ForFlow + '&coatid=' + coatid3ForFlow,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
                flowMesshisRefresh3ForFlow(lastId3ForFlow, lastRowIndex3ForFlow);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatid3ForFlow
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime);
//                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
//                        $("#scriptState").html(res.state);
//                        $("#scriptEndTime").html(res.endTime);
//                        $("#scriptRunTime").html(res.runTime + " 秒");
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },{ text: '名称',  dataIndex: 'sysName',hidden: !CMDBflag,width:80},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,width:80},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    }, { text: '计算机名',  dataIndex: 'hostName',width:80},
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 200,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state');
            var zoomStr = "";
            if (isWin3ForFlow != 1) {
                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetail3ForFlow(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
            }
            zoomStr = '';
            if (state == '30' || state == '40' || state == '50') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openActWindowForFlowTest(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServer3ForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="skipScriptServer3ForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else if (state == '-1' || state == '1') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="scriptServerStop3ForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_termination"></img>&nbsp;终止</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServer3ForFlow(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openActWindowForFlowTest(' + iid + ',' + state + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
            }
        }
    }];

    rowExpanderLog3ForFlow = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForTestExec{iid}">', '<pre  onselectstart="return true" id="steptextareahisForTestExec{iid}"  class="monitor_desc"></pre>', '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span">自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshId3ForFlow" name="rowFreshId3ForFlow" >&nbsp;秒</span>', '&nbsp;&nbsp;&nbsp;<input type="button" value="刷新" onclick="loadShelloutputhis3ForFlow({iid},\'{agentIp}\',{agentPort})" class="Common_Btn">', '&nbsp;&nbsp;&nbsp;<input type="button" value="终止" onclick="scriptServerStop3ForFlow({iid},{state})" class="Common_Btn">', '</div>']
    });
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        name: "pageFreshTime",
        value: interPV3ForFlow
    });
    var stop_btn = Ext.create('Ext.Button', {
		text : '终止',
		baseCls : 'Common_Btn',
		handler : function() {
            var data = getCHKBoxIds();
            if (data.length == 0) {
                Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                return;
            } else {
                Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                function(button, text) {
                    if (button == "yes") {
                        if (data == '-1') {
                            Ext.Msg.alert('提示', '该活动无需终止!');
                            scriptmonitorinfoins_store3ForFlow.reload();
                            return;
                        }
                        Ext.MessageBox.wait("数据处理中...", "提示");
                        Ext.Ajax.request({
                            url: 'scriptServiceShellKill.do',
                            params: {
                                flag: flag3ForFlow,
                                insIds: data
                            },
                            method: 'POST',
                            success: function(response, opts) {
                                var success = Ext.decode(response.responseText).success;
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                // 当后台数据同步成功时
                                if (success) {
                                    scriptmonitorinfoins_store3ForFlow.reload();
                                }
                            }
                        });
                    }
                });
            }
        }
	});
    
    var return_btn = Ext.create('Ext.Button', {
		text : '返回',
		baseCls : 'Common_Btn',
		handler : function() {
        	returnBack3ForFlow();
            }
	});
    
    var refresh_btn = Ext.create('Ext.Button', {
		text : '刷新',
		baseCls : 'Common_Btn',
		handler : function() {
            if (refreshObjForTestExecForFlow) {
                clearInterval(refreshObjForTestExecForFlow);
            }
            refreshPage3ForFlow();
            // var interValue =
            // document.getElementById('pageFreshTime').value;
            var interValue = pageFreshTime.getValue();
            interPV3ForFlow = interValue;
            if (interPV3ForFlow < 20) {
                interPV3ForFlow = 20;
            }
            refreshObjForTestExecForFlow = setInterval(refreshPage3ForFlow, interPV3ForFlow * 1000);
        }
	});
	var combo = Ext.create('Ext.form.ComboBox', {
		name : 'pagesize',
		hiddenName : 'pagesize',
		store : new Ext.data.ArrayStore({
			fields : [ 'text', 'value' ],
			data : [ [ '30', 30 ], [ '50', 50 ], [ '100', 100 ],
					[ '150', 150 ], [ '200', 200 ] ]
		}),
		valueField : 'value',
		displayField : 'text',
		emptyText : 30,
		width : 60
	});
	// 添加下拉显示条数菜单选中事件
	combo.on("select", function(comboBox) {
		var pagingToolbar = Ext.getCmp('pagingbar');
		pagingToolbar.pageSize = parseInt(comboBox.getValue());
		itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
		scriptmonitorinfoins_store3ForFlow.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
		pageBar.moveFirst();
	});
	combo.on("blur", function(comboBox) {
		var pagingToolbar = Ext.getCmp('pagingbar');
		pagingToolbar.pageSize = parseInt(comboBox.getValue());
		itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
		scriptmonitorinfoins_store3ForFlow.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
		pageBar.moveFirst();
	});
	var oam_act_form = Ext.create('Ext.form.Panel', {
		frame : true,
		border : false,
		bodyCls:'fm-spinner',
		layout : {
			type : 'hbox',
			align : 'middle'
		},
		defaults : {
			anchor : '100%'
		},
		items : [
				{
					text  : '自动刷新',
					xtype : 'label',
				},pageFreshTime , {
					xtype : 'label',
					text : '    秒',
				} ]
	});
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: scriptmonitorinfoins_store3ForFlow,
        id : 'pagingbar',
        baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true,
        items: [combo,oam_act_form, refresh_btn,stop_btn,return_btn]
    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_grid3ForFlow.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (/*state != '-1' &&*/ state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right',
        value: serviceName3ForFlow
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserFullName3ForFlow
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: startTime3ForFlow
    });
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right',
    	value: endTime3ForFlow
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls:'customize_gray_back',
        cls:'sc_tlbar_height',
        dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',    
			border : false,
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',    
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    scriptmonitorinfoins_grid3ForFlow = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	store: scriptmonitorinfoins_store3ForFlow,
    	autoScroll: true,
        border: false,
        columnLines: true,
        cls:'sc_tab_height',
        columns: scriptmonitorinfoins_columns,
        bbar: pageBar,
        selModel: selModel,
        plugins: [rowExpanderLog3ForFlow],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                /*
						 * var cls = ''; if(record.data.state==10){ cls =
						 * 'row_Blue'; }else if(record.data.state==20){ cls =
						 * 'row_Green'; }else if(record.data.state==30){ cls =
						 * 'row_Red'; }else if(record.data.state==60){ cls =
						 * 'row_Gray'; } else { cls = 'row_Gray'; } return cls;
						 */
                return 'norowexpandblah';
            }
        }/*,

        listeners: {
            itemclick: function(a, record, item, index, e, eOpts) {
                rowExpanderLog3ForFlow.toggleRow(index, record);
            }
        }*/
    });

    scriptmonitorinfoins_grid3ForFlow.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
        interV3ForFlow = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshId3ForFlow').innerText = interV3ForFlow;
        } else {
            document.getElementById('rowFreshId3ForFlow').innerHTML = interV3ForFlow;
        }
        loadShelloutputhis3ForFlow(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
        // refreshObjShellOutputForTestExecForFlow = setInterval(function() {
        // loadShelloutputhis3ForFlow(record.get('iid'),
        // record.get('agentIp'), record.get('agentPort'));
        // }, 1000);
    });
    scriptmonitorinfoins_grid3ForFlow.view.on('collapsebody', function(rowNode, record, expandRow, eOpts) {
        lastId3ForFlow = 0;
        lastRowIndex3ForFlow = 0;
        if (refreshObjShellOutputForTestExecForFlow) {
            clearInterval(refreshObjShellOutputForTestExecForFlow);
        }
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoins_div_ForFlow",
        layout: 'border',
        cls:'customize_panel_back',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: true,
        items: [info_form, scriptmonitorinfoins_grid3ForFlow]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

    function refreshPage3ForFlow() {
    	//获取div的id
    	var REG_BODY = /<body[^>]*>([\s\S]*)<\/body>/;
    	function getBody(content){
            var result = REG_BODY.exec(content);
            if(result && result.length === 2)
                return result[1];
            return content;
        }
    	if($(getBody(contentPanel.getLoader().target.html)).attr('id')=='switchruninfoins_div_ForFlow') {
    		scriptmonitorinfoins_store3ForFlow.reload();
    	}
    }

    if (refreshObjForTestExecForFlow) {
        clearInterval(refreshObjForTestExecForFlow);
    }
    refreshObjForTestExecForFlow = setInterval(refreshPage3ForFlow, interPV3ForFlow * 1000);

});

function flowMesshis3ForFlow(iruninfoinsid, rowIndex) {
    lastId3ForFlow = iruninfoinsid;
    lastRowIndex3ForFlow = rowIndex;
    var record = scriptmonitorinfoins_store3ForFlow.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForFlow.getRange(0, scriptmonitorinfoins_store3ForFlow.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLog3ForFlow.recordsExpanded[records[i].internalId]) {
            rowExpanderLog3ForFlow.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_store3ForFlow.getAt(rowIndex);
     rowExpanderLog3ForFlow.toggleRow(rowIndex, record);
}
function openActWindowForFlowTest(requestId, state,agentIp,agentPort) {
	var runningWindow = null;
	var fp2 = null;
	var cmdStr = null;
	var surl = "getScriptExecOutput.do";
	var h = window.innerHeight || document.documentElement.clientHeight
			|| document.body.clientHeight;
	function getData(surl,state, requestId, agentIp, agentPort, cmdValue) {
		Ext.Ajax.request({
			url : surl,
			params : {
				requestId : requestId,
				agentIp : agentIp,
				agentPort : agentPort,
				flag:0,
				input:cmdValue
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				var oldValue = Ext.getCmp('baseinfo_form_forflowtest').getForm().findField(
						"actOutPut").getValue();
				var output = msg.message;

				var json = {
					actName :serviceName3ForFlow+"-"+agentIp+"-"+agentPort ,
					actInsName : serviceName3ForFlow,
					actOutPut : output
				};
				Ext.getCmp('baseinfo_form_forflowtest').getForm().setValues(json);
				actName.getEl().dom.innerHTML='活动名：    <div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px;  left:75px;right:0px; width:80%;height:35px ;margin-bottom:10px"><pre>'+actName.getValue()+'</pre></div>';
				actOutPut.getEl().dom.innerHTML='活动日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:35px; left:75px;bottom:10px;right:0px; width:80%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+actOutPut.getValue()+'</pre></div>';
			},
			failure : function(response, opts) {

			}

		});
	}
	var cmdStr = new Ext.form.TextField({
		anchor : '100%',
		labelWidth : 70,
		fieldLabel : 'CMD',
		disabled:(state==10)?false:true,
		listeners : {
			specialkey : function(textfield, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var cmdV = cmdStr.getValue();
					cmdStr.setValue("");
					getData(surl,state, requestId, agentIp, agentPort, cmdV);
				}
			}
		}
	});
	var actName= Ext.create ('Ext.form.field.Text',
			{
				fieldLabel : '活动名',
				labelWidth : 70,
				name : 'actName',
				margin :'10 0 5 0',
				anchor : '95%' // anchor width by percentage
			});
	var actOutPut = Ext.create ('Ext.form.field.TextArea',
	{
		fieldLabel : '活动日志',
		labelWidth : 70,
		grow : true,
		height : h-200,
		name : 'actOutPut',
		margin :'15 0 5 0',
		anchor : '95%'
	});
	var fp2 = new Ext.form.Panel({
		border : false,
		id : 'baseinfo_form_forflowtest',
		height : '100%',
		padding : 5,
		fieldDefaults : {
			labelWidth : 60,
			labelAlign : 'right'
		},
		defaultType : 'textfield',
		items : [ actName, {
			fieldLabel : '实例名',
			labelWidth : 70,
			name : 'actInsName',
			hidden : true,
			anchor : '100%' // anchor width by percentage
		}, actOutPut, cmdStr ]
	});

	if (runningWindow == undefined || !runningWindow.isVisible()) {
		runningWindow = Ext.create('Ext.window.Window', {
			title : serviceName3ForFlow,
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 600,
			height : h - 95,
			items : [ fp2 ],
			dockedItems : [ {
				xtype : 'toolbar',
				baseCls:'customize_gray_back',    
				dock : 'bottom',
				layout: {pack: 'center'},
				items : [
				 {
					text : '刷新',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						getData(surl,state, requestId, agentIp, agentPort, null);
						cmdStr.setValue("");
					}
				}, {
					text : '终止',
					textAlign : 'center',
					cls : 'Common_Btn',
					handler : function() {
						scriptServerStop3ForFlow(requestId,state)
					}
				} ]
			} ],
			layout : 'fit'
		});
	}
	runningWindow.show();
	getData(surl,state, requestId, agentIp, agentPort, null);

		
	
}
function loggerDetail3ForFlow(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForTestExecForFlow) {
        clearInterval(refreshObjShellOutputForTestExecForFlow);
    }
    if (refreshObjForTestExecForFlow) {
        if (refreshObjShellOutputForTestExecForFlow) {
            clearInterval(refreshObjShellOutputForTestExecForFlow);
        }
        clearInterval(refreshObjForTestExecForFlow);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
//            flowId: flowId,
            coatId: coatid3ForFlow,
            flag: flag3ForFlow
        }
    });
}

function flowMesshisRefresh3ForFlow(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    var record = scriptmonitorinfoins_store3ForFlow.getAt(rowIndex);
    var records = scriptmonitorinfoins_store3ForFlow.getRange(0, scriptmonitorinfoins_store3ForFlow.getCount());
    var rowFreshValue = document.getElementById('rowFreshId3ForFlow').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForFlow = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForFlow').innerText = interV3ForFlow;
    } else {
        document.getElementById('rowFreshId3ForFlow').innerHTML = interV3ForFlow;
    }

    rowExpanderLog3ForFlow.toggleRow(lastRowIndex3ForFlow, records[lastRowIndex3ForFlow]);
//    refreshObjShellOutputForTestExecForFlow = setInterval(function() {
////    	if(contentPanel.getLoader().url=='forwardscriptcoat.do') {
//    		loadShelloutputhisInfo3ForFlow(lastrequestId3ForFlow, lastiip3ForFlow, lastiport3ForFlow);
////    	}
//    },
//    rowFreshValue * 1000);
    // var record = scriptmonitorinfoins_store3ForFlow.getAt(rowIndex);
    // rowExpanderLog3ForFlow.toggleRow(rowIndex, record);
}

function loadShelloutputhis3ForFlow(requestId, iip, iport) {
    lastrequestId3ForFlow = requestId;
    lastiip3ForFlow = iip;
    lastiport3ForFlow = iport;
    if (refreshObjShellOutputForTestExecForFlow) {
        clearInterval(refreshObjShellOutputForTestExecForFlow);
    }
    var rowFreshValue = document.getElementById('rowFreshId3ForFlow').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interV3ForFlow = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshId3ForFlow').innerText = interV3ForFlow;
    } else {
        document.getElementById('rowFreshId3ForFlow').innerHTML = interV3ForFlow;
    }
    // document.getElementById('rowFreshId3ForFlow').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfo3ForFlow(requestId, iip, iport);
    refreshObjShellOutputForTestExecForFlow = setInterval(function() {
        loadShelloutputhisInfo3ForFlow(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfo3ForFlow(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForTestExec' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: 0
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStop3ForFlow(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
            	Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_store3ForFlow.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServer3ForFlow(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServer3ForFlow(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store3ForFlow.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    });
}
function returnBack3ForFlow() {
    lastId3ForFlow = '';
    if (refreshObjShellOutputForTestExecForFlow) {
        clearInterval(refreshObjShellOutputForTestExecForFlow);
    }
    if (refreshObjForTestExec) {
        if (refreshObjShellOutputForTestExecForFlow) {
            clearInterval(refreshObjShellOutputForTestExecForFlow);
        }
        clearInterval(refreshObjForTestExecForFlow);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptflowcoat.do",
        scripts: true,
        params: {
        	flowId: flowId3ForFlow,
			flag : 0,
			forScriptFlow: 1,
			filter_scriptName:filter_scriptNameForTestExecForFlow,
		    filter_state:filter_stateForTestExecForFlow,
		    filter_startTime:filter_startTimeForTestExecForFlow,
		    filter_endTime:filter_endTimeForTestExecForFlow,
		    filter_serviceName:filter_serviceNameForTestExecForFlow,
			filter_serviceState:filter_serviceStateForTestExecForFlow,
			filter_serviceStartTime:filter_serviceStartTimeForTestExecForFlow,
			filter_serviceEndTime:filter_serviceEndTimeForTestExecForFlow
        }
    });
}