
Ext.onReady(function() {
    destroyRubbish();
    
    var fileids=new Array();
    var temp_filenames=new Array();
    var temp_othernames=new Array();
    var issuedid = -1;
    var editingAgentIds = new Array();
    var globalAgentIds = new Array();
    var chosedAgentWinForSee;
    
    if (typeof String.prototype.endsWith != 'function') {
        String.prototype.endsWith = function(suffix) {
            return this.indexOf(suffix, this.length - suffix.length) !== -1;
        };
    }
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	Ext.define('osTypeModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'name',
	      type : 'string'
	    }]
	});
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox', {
	    fieldLabel : '资源组',
	    emptyText: '--请选择资源组--',
	    margin : '0 10 0 10',
	    labelAlign : 'right',
        labelWidth : 65,
        width:'24%',
	    multiSelect: true,
	    hidden:removeAgentSwitch,
	    store : resourceGroupStore,
	    displayField : 'name',
	    valueField : 'id',
	    triggerAction : 'all',
	    editable : true,
	    mode : 'local',
    	listeners: {
	      change: function( comb, newValue, oldValue, eOpts ) {
	    	  agent_store.load();
	      }
    	}
	});
	var osTypeStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'osTypeModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getOsTypeForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	var osTypeComb=Ext.create ('Ext.form.field.ComboBox', {
		fieldLabel : '操作系统',
		emptyText: '--请选择操作系统--',
		margin : '0 0 0 10',
		labelAlign : 'right',
		labelWidth : 70,
		width:'22%',
		store : osTypeStore,
		displayField : 'name',
		valueField : 'name',
		triggerAction : 'all',
		editable : true,
		mode : 'local',
		listeners: {
			change: function( comb, newValue, oldValue, eOpts ) {
				agent_store.load();
			}
		}
	});
	var search_form = Ext.create('Ext.form.Panel', {
		region:'north',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [{
		            fieldLabel: '执行用户',
		            labelAlign : 'right',
		            labelWidth : 65,
		            margin : '0 0 0 10',
		            name: 'execUser',
		            width:'26%',
		            xtype: 'textfield'
		        },{
	            	xtype: 'textfield',
	            	id: 'sy-label',
	            	text: '文件名',
	            	fieldLabel: '文件名 ',
	            	labelAlign : 'right',
	            	width:'68.2%',
	            	labelWidth: 80
	            	
	            },'->',{
	                xtype: 'tbspacer',
	                id: 'uploadify-file',
	            }]
			},{
				xtype : 'toolbar',
				border : false,
				dock : 'top',	    	
	    		items:[{
		            fieldLabel: '下发路径',
		            labelAlign : 'right',
		            labelWidth : 65,
		            margin : '0 10 0 10',
		            name: 'targetPath',
		            width:'94.8%',
		            xtype: 'textfield'
		        },'->',{ 
		  			xtype: "button",
		  			text: "下发", 
		  			cls : 'Common_Btn',
		  			id: 'issue_btn',
		  			disabled : true,
		  			handler: function () {
		  				if(globalAgentIds.length<1) {
		  					Ext.Msg.alert('提示', '请选择下发服务器！');
		  					return;
		  				}
		  				 var filepath = search_form.getForm().findField("targetPath").getValue();
		  				if(filepath!="" && !filepath.endsWith('/')) {
                        	Ext.Msg.alert('提示', '下发路径需以"/"结尾！');
                            return;
                        }
//		  				getScriptIdForIssuedByOsType.do
//	      				m[3].paramDefaultValue=temp_othernames.join("---");
//	      				m[4].paramDefaultValue=temp_filenames.join("---");
	      				Ext.Ajax.request({
			      			url : 'execScriptServiceForIssue.do',
			      			method : 'POST',
			      			params : {
			      				execUser: search_form.getForm().findField("execUser").getValue(),
			      				filepath: search_form.getForm().findField("targetPath").getValue(),
			      				issuedid: issuedid,
			      				othernames: temp_othernames,
			      				filenames: temp_filenames,
			      				agentIds: globalAgentIds,
			      				ifrom :1
			      			},
			      			success : function(response, request) {
			      				var success = Ext.decode(response.responseText).success;
			      				var one = Ext.decode(response.responseText).one;
			      				var message = Ext.decode(response.responseText).message;
			      				if(success) {
			      					if(one) {
			      						Ext.Msg.alert('提示', "文件已在执行服务器上下发！",function(){
		                                	 var flowId = message.flowId;
		                                     var coatId = message.coatId;
		                                     popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0},10, true);
		                                });
			      					} else {
			      						Ext.Msg.confirm("请确认", "本次下发有多种操作系统类型的代理，" +
			      								"所以执行历史记录有多条，默认将跳转到最后一条执行记录监控，是否继续？", function(id){
							    			if(id=='yes') {
							    				var flowId = message.flowId;
			                                    var coatId = message.coatId;
			                                    popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0,},10, true);
							    			}
							    		});
			      					}
			      				} else {
			      					Ext.Msg.alert('提示', message);
			      				}
			      			},
			      			failure : function(result, request) {
			      				Ext.Msg.alert('提示', '下发失败！');
			      			}
			      		});
			        }
		  		}]
	    	}]
	    }]
	});
	
	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'appName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
	
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListForIssued.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp:agent_ip.getValue(),
	    	rgIds:resourceGroupObj.getValue(),
	    	osType: osTypeComb.getValue(),
	    	flags:2,
	    	ctype:'new'
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
	agent_store.addListener('load', function(me, records, successful, eOpts) {
        if (editingAgentIds) {
            var chosedRecords = []; //存放选中记录
            $.each(records, function(index, record) {
                if (editingAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            agent_grid.getSelectionModel().select(chosedRecords, false, true); //选中记录
        }
    });
	
	var agent_chosed_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 50,
		model: 'agentModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentChosedList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});
	
	agent_chosed_store.on('beforeload', function(store, options) {
        var new_params = {
            agentIds: JSON.stringify(chosedAgentIds)
        };
        Ext.apply(agent_chosed_store.proxy.extraParams, new_params);
    });
    
    var agent_columns = [
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: '名称',  dataIndex: 'sysName',hidden: !CMDBflag,flex:1},
                        { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,flex:1},
                        { text: '计算机名',  dataIndex: 'hostName',flex:1},
                        { text: 'IP',  dataIndex: 'agentIp',width:150},
                        { text: '端口号',  dataIndex: 'agentPort',width:100},
                        { text: '操作系统',  dataIndex: 'osType',width:140},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden:true},
		                { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ];
    
    
    var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });
    var chosedPageBar = Ext.create('Ext.PagingToolbar', {
    	store: agent_chosed_store,
    	dock: 'bottom',
    	displayInfo: true
    });
    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'Agent IP',
        displayField: 'agentip',
        emptyText: '--请输入Agent IP--',
        labelWidth: 80,
        labelAlign: 'right',
        width: '22%'
    });
    
    var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {

    	//var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {
    		region : 'north',
    	  	layout : 'anchor',
    	  	buttonAlign : 'center',
    	  	cls:'window_border panel_space_top panel_space_left panel_space_right panel_space_bottom',
    	  	iselect : false,
    	  	bodyCls : 'x-docked-noborder-top',
    	    border: false,
    	    dockedItems : [
{
	xtype : 'toolbar',
	border : false,
	dock : 'top',		    	
	items:[resourceGroupObj,osTypeComb,agent_ip, {
		xtype: 'button',
		cls: 'Common_Btn',
		text : '查询',
		handler : function() {
			pageBar.moveFirst();
		}
	},{
		xtype: 'button',
		cls: 'Common_Btn',
		text : '清空',
		handler : function() {
			clearQueryWhere();
		}
	}]
}
    		]
    	});
    
    var agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
	    store:agent_store,
	    border:false,
	    columnLines : true,
	    columns:agent_columns,
	    cls:'window_border panel_space_left panel_space_right panel_space_bottom',
	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    //bbar: pageBar,
	    /*dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',		    	
    		items:[resourceGroupObj,osTypeComb,agent_ip, {
				xtype: 'button',
				cls: 'Common_Btn',
				text : '查询',
				handler : function() {
					pageBar.moveFirst();
				}
			},{
				xtype: 'button',
				cls: 'Common_Btn',
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			}]
    	}],*/
    	listeners: {
            select: function(t, record, index, eOpts) {
                if (editingAgentIds.indexOf(record.get('iid')) == -1) {
                	editingAgentIds.push(record.get('iid'));
                }
            },
            deselect: function(t, record, index, eOpts) {
                if (editingAgentIds.indexOf(record.get('iid')) > -1) {
                	editingAgentIds.remove(record.get('iid'));
                }
            }
        }
	});
    var agent_chosed_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	title: '已选服务器',
    	store:agent_chosed_store,
    	border:false,
    	columnLines : true,
    	columns:agent_columns,
    	ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
    	//bbar: chosedPageBar,
    	dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',		    	
    		items:[{
				xtype: 'button',
				cls: 'Common_Btn',
				text : '增加服务器',
				handler : function() {
					if (!chosedAgentWinForSee) {
                        chosedAgentWinForSee = Ext.create('Ext.window.Window', {
                            title: '增加服务器',
                            autoScroll: true,
                            modal: true,
                            resizable: false,
                            closeAction: 'hide',
                            width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
                            layout: 'border',
                            items: [formMainPanel,agent_grid],
                            buttonAlign: 'center',
                            dockedItems: [{
                                xtype: 'toolbar',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    text: "确定",
                                    cls: 'Common_Btn',
                                    margin: '6',
                                    handler: function() {
                                    	globalAgentIds = editingAgentIds.slice(0);
                                    	chosedPageBar.moveFirst();
                                        this.up("window").close();
                                    }
                                },
                                {
                                    xtype: "button",
                                    text: "关闭",
                                    cls: 'Common_Btn',
                                    handler: function() {
                                        this.up("window").close();
                                    }
                                }]
                            }]
                        });
                    }
					editingAgentIds = globalAgentIds.slice(0);
                    chosedAgentWinForSee.show();
                    pageBar.moveFirst();
				}
			},{
				xtype: 'button',
				margin: '0 5 0 0',
				cls: 'Common_Btn',
				text : '删除服务器',
				handler : function() {
					var records = agent_chosed_grid.getSelectionModel().getSelection();
                    if (records.length > 0) {
                        for (var i = 0, len = records.length; i < len; i++) {
                        	globalAgentIds.remove(records[i].get('iid'));
                        }
                        chosedPageBar.moveFirst();
                    } else {
                        Ext.Msg.alert('提示', "请选择服务器！");
                        return;
                    }
				}
			}]
    	}]
    });
    function clearQueryWhere(){
    	osTypeComb.setValue('');
    	resourceGroupObj.setValue('');
    	agent_ip.setValue('');
    }
    var issuePanel = Ext.create('Ext.panel.Panel', {
        renderTo: "issued_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_form, agent_chosed_grid]
    });

    contentPanel.on('resize', function() {
    	issuePanel.setHeight (contentPanel.getHeight () - modelHeigth);
    	issuePanel.setWidth (contentPanel.getWidth () );
    });
    
    var uploadFiles = setInterval(function(){
        if($('#uploadify-file').length == 0){
          
        } else {
          $('#uploadify-file').uploadify({
        	  'buttonClass' : '',
              'fileObjName' : 'files',
              'buttonCursor': 'hand',
              'buttonText' : '添加附件',
              'height': 30,
              'width': 70,
              'fileSizeLimit':'500MB',
              'successTimeout': 600,
        	  'debug' : false,
              'queueID'  : 'uploadify-process',
              'swf'      : 'js/uploadify/uploadify.swf',
              'uploader' : 'uploadIssuedFile.do;jsessionid='+sessionId,
              'method'   : 'post',
              'onInit'   : function(instance) {
            	  uploadProcessWin = Ext.create("Ext.Window",{
                      title : '文件上传进度显示',
                      width : 300,     
                      height: 300,   
                      autoScroll: true,
                      closable : false,
                      modal:true,
                      html : '<div id="uploadify-process"></div>'                      
                    });
              },
              'onFallback' : function() {
                alert('没有检查到Flash插件，不能使用文件上传功能');
              },
              'onDialogOpen' : function() {
            	  uploadProcessWin.show();
              },
              'onDialogClose'  : function(queueData) {
            	  if(queueData.queueLength>0){
            		  console.log('清空页面保存的临时数据');
            		  fileids.splice(0,fileids.length);
            		  temp_filenames.splice(0,temp_filenames.length);
            		  temp_othernames.splice(0,temp_othernames.length);
            		  issuedid = -1;
                      uploadProcessWin.show();
                    }else{
                      uploadProcessWin.hide();
                    }
              },
              'onUploadSuccess' : function(file, data, response) {
            	  if(response){
                      var res = Ext.JSON.decode(data);
                      if(res.success){
                    	  fileids.push(res.ids);
                    	  temp_filenames.push(res.filename);
                    	  temp_othernames.push(res.othername);
                    	  
                          Ext.getCmp('sy-label').setValue(temp_filenames.join(","));
                      }else {
                    	  
                    	  Ext.Msg.alert('提示', '上传失败。' + res.message);
                      }
            	  }
              },
              'onUploadError' : function(file, errorCode, errorMsg, errorString) {
            	  Ext.Msg.alert('提示', '上传失败');
              },
              'onQueueComplete' : function(queueData) {
            	  uploadProcessWin.hide();
                 
            	  Ext.Ajax.request({
                      url: 'insertIssued.do',
                      method: 'POST',
                      params: {
                    	  ids: fileids
                      },
                      success: function(response, request) {
                          var success = Ext.decode(response.responseText).success;
                          var message = Ext.decode(response.responseText).message;
                          issuedid = Ext.decode(response.responseText).issuedid;
                          if (success) {
                        	  Ext.getCmp('issue_btn').enable();
                          } else {
                              Ext.Msg.alert('提示', message);
                          }
                      }
                  });
                  //fileids.splice(0,fileids.length);//清空数组 
//            	  attachmentStore.load();
              }
            });
          $('#uploadify-file').addClass('pull-right').css('margin','5px 12px 0 0 ');
          clearInterval(uploadFiles);
        }
      },100);
});


