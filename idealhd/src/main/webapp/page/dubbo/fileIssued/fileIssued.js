
Ext.onReady(function() {
    destroyRubbish();
    var uploadProcessWin;
    var fileids=new Array();
    var temp_filenames=new Array();
    var temp_othernames=new Array();
    var issuedid = -1;
    var editingAgentIds = new Array();
    var globalAgentIds = new Array();
    var chosedAgentWinForSee;
    var upLoadformPane = '';
    var upldWin;
    if (typeof String.prototype.endsWith != 'function') {
        String.prototype.endsWith = function(suffix) {
            return this.indexOf(suffix, this.length - suffix.length) !== -1;
        };
    }
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	Ext.define('osTypeModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'name',
	      type : 'string'
	    }]
	});
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox', {
	    fieldLabel : '资源组',
	    emptyText: '--请选择资源组--',
	    margin : '0 10 0 10',
	    labelAlign : 'right',
        labelWidth : 65,
        width:'24%',
	    multiSelect: true,
	    hidden:removeAgentSwitch,
	    store : resourceGroupStore,
	    displayField : 'name',
	    valueField : 'id',
	    triggerAction : 'all',
	    queryMode:'local',
	    editable : true,
	    mode : 'local',
    	listeners: {
	      change: function( comb, newValue, oldValue, eOpts ) {
	    	  agent_store.load();
	      },
	      specialkey: function(field, e){
              if (e.getKey() == e.ENTER) {
              	pageBar.moveFirst();
              }
          }
    	}
	});
	var osTypeStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'osTypeModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getOsTypeForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	var osTypeComb=Ext.create ('Ext.form.field.ComboBox', {
		fieldLabel : '操作系统',
		emptyText: '--请选择操作系统--',
		margin : '0 0 0 10',
		labelAlign : 'right',
		labelWidth : 70,
		width:'22%',
		store : osTypeStore,
		displayField : 'name',
		valueField : 'name',
		triggerAction : 'all',
		queryMode:'local',
		editable : true,
		mode : 'local',
		listeners: {
			change: function( comb, newValue, oldValue, eOpts ) {
				agent_store.load();
			},
			 specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
		}
	});
	var userPermission =  new Ext.form.TextField({
		name : 'userPermission',
		fieldLabel : '所属用户',
		emptyText : '--请输入所属用户--',
		labelWidth : 90,
		labelAlign : 'right',
		width:'21%'
	});
	var groupPermission =  new Ext.form.TextField({
		name : 'groupPermission',
		fieldLabel : '所属组',
		emptyText : '--请输入所属用户组--',
		labelWidth : 70,
		labelAlign : 'right',
		width:'21%'
	});
	
	var parameterPermission =  new Ext.form.TextField({
		name : 'parameterPermission',
		fieldLabel : '权限参数',
		emptyText : '--请输入权限参数--',
		labelWidth : 65,
		labelAlign : 'right',
		width:'21.1%',
		regex:/^[0-7]{1,4}$/, // 四位数字 0-7
        regexText:"权限参数不符合要求" //定义不符合正则表达式的提示信息
	});
	var selectedFileButton = Ext.create ("Ext.Button",
			{
				cls : 'Common_Btn',
				disabled : false,
				text : '添加附件',
				handler : selectFileFun
			});
	var search_form = Ext.create('Ext.form.Panel', {
		region:'north',
    	buttonAlign : 'center',
    	border : false,
    	baseCls:'customize_gray_back',
	    items: [{
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back',  
				border : false,
				dock : 'top',
				items: [{
		            fieldLabel: '执行用户',
		            labelAlign : 'right',
		            labelWidth : 65,
		            margin : '0 0 0 10',
		            name: 'execUser',
		            width:'26%',
		            xtype: 'textfield'
		        },{
	            	xtype: 'textfield',
	            	id: 'sy-label',
	            	text: '文件名',
	            	fieldLabel: '文件名 ',
	            	labelAlign : 'right',
	            	width:'68.2%',
	            	labelWidth: 65
	            	
	            },'->',selectedFileButton]
			},{
				xtype : 'toolbar',
				baseCls:'customize_gray_back',  
				border : false,
				dock : 'top',	    	
	    		items:[{
		            fieldLabel: '下发路径',
		            labelAlign : 'right',
		            labelWidth : 65,
		            margin : '0 0 0 10',
		            name: 'targetPath',
		            width:'26.4%',
		            xtype: 'textfield'
		        },parameterPermission,userPermission,groupPermission,'->',{ 
		  			xtype: "button",
		  			text: "下发", 
		  			cls : 'Common_Btn',
		  			id: 'issue_btn',
		  			disabled : true,
		  			handler: function () {
		  				if(globalAgentIds.length<1) {
		  					Ext.Msg.alert('提示', '请选择下发服务器！');
		  					return;
		  				}
		  				 var filepath = search_form.getForm().findField("targetPath").getValue();
		  				if(filepath!="" && !filepath.endsWith('/')) {
                        	Ext.Msg.alert('提示', '下发路径需以"/"结尾！');
                            return;
                        }
		  				var parameterPermissionValue =  parameterPermission.getValue();
						var groupPermissionValue = groupPermission.getValue();
						var userPermissionValue = userPermission.getValue();
						
						if((groupPermissionValue != '' && groupPermissionValue != null) && (userPermissionValue == '' || userPermissionValue == null)){
							Ext.Msg.alert('提示', '所属用户和所属组必须同时填写！');
							return
						}
						if((groupPermissionValue == '' || groupPermissionValue == null) && (userPermissionValue != '' && userPermissionValue != null)){
							Ext.Msg.alert('提示', '所属用户和所属组必须同时填写！');
							return
						}
						
						if(parameterPermissionValue == '' || parameterPermissionValue == null){
							  parameterPermissionValue = 755;
						}
	      				Ext.Ajax.request({
			      			url : 'execScriptServiceForIssue.do',
			      			method : 'POST',
			      			params : {
			      				execUser: search_form.getForm().findField("execUser").getValue(),
			      				filepath: search_form.getForm().findField("targetPath").getValue(),
			      				issuedid: issuedid,
			      				othernames: temp_othernames,
			      				filenames: temp_filenames,
			      				agentIds: globalAgentIds,
			      				ifrom :1,
			      				parameterPermission :parameterPermissionValue,//权限 
								groupPermission :groupPermissionValue,//所属用户组
								userPermission :userPermissionValue// 所属用户
			      			},
			      			success : function(response, request) {
			      				var success = Ext.decode(response.responseText).success;
			      				var one = Ext.decode(response.responseText).one;
			      				var message = Ext.decode(response.responseText).message;
			      				if(success) {
			      					if(one) {
			      						Ext.Msg.alert('提示', "文件已在执行服务器上下发！",function(){
		                                
		                                     var coatId = message.coatId;
//		                                     popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0},10, true);
		                                     popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0,fromMenu:'文件下发'},10, true);
		                                });
			      					} else {
			      						Ext.Msg.confirm("请确认", "本次下发有多种操作系统类型的代理，" +
			      								"所以执行历史记录有多条，默认将跳转到最后一条执行记录监控，是否继续？", function(id){
							    			if(id=='yes') {
							    			 
			                                    var coatId = message.coatId;
//			                                    popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0},10, true);
			                                    popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0,fromMenu:'文件下发'},10, true);
							    			}
							    		});
			      					}
			      				} else {
			      					Ext.Msg.alert('提示', message);
			      				}
			      			},
			      			failure : function(result, request) {
			      				Ext.Msg.alert('提示', '下发失败！');
			      			}
			      		});
			        }
		  		}]
	    	}]
	    }]
	});
	
	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'appName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });

	agent_grid_url = 'getAllAgentForIpSearch.do';
	agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListForIssued.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	agent_store.on('beforeload', function (store, options) {
	    new_params_agent = {
	    	agentIp:agent_ip.getValue(),
	    	rgIds:resourceGroupObj.getValue(),
	    	osType: osTypeComb.getValue(),
	    	flags:2,
	    	agentIds : editingAgentIds,
			batchComputerName:ipOrNameSwitch == 'true' ? pubDesc_sm_ipsearch.getValue() : ""
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params_agent);
    });
	agent_store.addListener('load', function(me, records, successful, eOpts) {
        if (editingAgentIds) {
            var chosedRecords = []; //存放选中记录
            $.each(records, function(index, record) {
                if (editingAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            agent_grid.getSelectionModel().select(chosedRecords, false, true); //选中记录
        }
    });
	
	var agent_chosed_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 50,
		model: 'agentModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentChosedList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});
	
	agent_chosed_store.on('beforeload', function(store, options) {
        var new_params = {
            agentIds: JSON.stringify(globalAgentIds)
        };
        Ext.apply(agent_chosed_store.proxy.extraParams, new_params);
    });
    
    var agent_columns = [
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: '名称',  dataIndex: 'sysName',flex:1},
                        { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,flex:1},
                        { text: '计算机名',  dataIndex: 'hostName',flex:1},
                        { text: 'IP',  dataIndex: 'agentIp',width:150},
                        { text: '端口号',  dataIndex: 'agentPort',width:100},
                        { text: '操作系统',  dataIndex: 'osType',width:140},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden:true},
		                { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ];
    
    
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: agent_store,
    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });
    var chosedPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: agent_chosed_store,
    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
    	dock: 'bottom',
    	displayInfo: true
    });
    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'Agent IP',
        displayField: 'agentip',
        emptyText: '--请输入Agent IP--',
        labelWidth: 65,
        labelAlign: 'right',
        width: '22%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
	    store:agent_store,
	    cls:'customize_panel_back',
	    border:false,
	    columnLines : true,
	    columns:agent_columns,
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//	    bbar: pageBar,
	    dockedItems : [{
			xtype : 'toolbar',
//			 baseCls:'customize_gray_back',    
			border : false,
			dock : 'top',		    	
    		items:[resourceGroupObj,osTypeComb,agent_ip, {
				xtype: 'button',
				cls: 'Common_Btn',
				text : '查询',
				handler : function() {
					pageBar.moveFirst();
				}
			},{
				xtype: 'button',
				cls: 'Common_Btn',
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			},{
				xtype : 'button',
				text : '导入',
				cls : 'Common_Btn',
				handler : function() {
					importExcel();
				}
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				hidden: batchQuerySwitch == 'false',
				text : 'IP批量查询',
				handler : function (){
					batchQueryForIp();
				}
			}]
    	}],
    	listeners: {
            select: function(t, record, index, eOpts) {
            	var osTypeV = record.get('osType'); // 当前选择行的AgentInfo里的 系统类型
			    var flag = true;
			  
            	 if((editingAgentIds.indexOf('WIN') > -1 &&  osTypeV.toUpperCase().indexOf('WIN') == -1) || (editingAgentIds.indexOf('LINUX') > -1 &&  osTypeV.toUpperCase().indexOf('WIN') > -1)){
		             flag = false;
		             Ext.Msg.alert('提示', "只允许选择相同系统类型的Agent信息");
		             agent_grid.getSelectionModel().deselect(record,false);
		        }
		        
		        if (flag && editingAgentIds.indexOf(record.get('iid')) == -1) {
	                		editingAgentIds.push(record.get('iid'));
	                		if(osTypeV.toUpperCase().indexOf('WIN') != -1 && editingAgentIds.indexOf('WIN') == -1 ){ //windows
	                			editingAgentIds.push('WIN');
	                		}else if(osTypeV.toUpperCase().indexOf('WIN') == -1 && editingAgentIds.indexOf('LINUX') == -1 ){
	                			editingAgentIds.push('LINUX');
	                		}
            	}
            },
            deselect: function(t, record, index, eOpts) {
                var selDatas = agent_grid.getSelectionModel().getSelection();
                if (editingAgentIds.indexOf(record.get('iid')) > -1) {
                	editingAgentIds.remove(record.get('iid'));
                }
                if(selDatas.length == 0 && editingAgentIds.length == 1){//如果存在只有一个元素，那么一定是 WIN 或者 LINUX 的元素 ，需要remove掉
                	if(editingAgentIds.indexOf('WIN') != -1){
			     		editingAgentIds.remove('WIN');
     				 }else if(editingAgentIds.indexOf('LINUX') != -1){
     				 	editingAgentIds.remove('LINUX');
     				 }
                }
                
            }
        }
	});
    var agent_chosed_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	title: '已选服务器',
    	store:agent_chosed_store,
    	cls:'customize_panel_back',
    	border:false,
    	columnLines : true,
    	columns:agent_columns,
		padding : grid_space,
    	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
    	ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//    	bbar: chosedPageBar,
    	dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',		    	
    		items:['->',{
				xtype: 'button',
				cls: 'Common_Btn',
				text : '增加服务器',
				handler : function() {
					if (!chosedAgentWinForSee) {
                        chosedAgentWinForSee = Ext.create('Ext.window.Window', {
                            title: '增加服务器',
                            autoScroll: true,
                            modal: true,
                            resizable: false,
//                            closeAction: 'hide',
                            closeAction: 'destory',
                            width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
                            layout: 'border',
                            items: [agent_grid],
                            buttonAlign: 'center',
                            dockedItems: [{
                                xtype: 'toolbar',
                                // baseCls:'customize_gray_back',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    text: "确定",
                                    cls: 'Common_Btn',
                                    margin: '6',
                                    handler: function() {
                                    	globalAgentIds = editingAgentIds.slice(0);
                                    	if(globalAgentIds.indexOf('WIN') > -1){
                                    		globalAgentIds.remove('WIN');
                                    	}
                                    	if(globalAgentIds.indexOf('LINUX') > -1){
                                    		globalAgentIds.remove('LINUX');
                                    	}
                                    	chosedPageBar.moveFirst();
                                        this.up("window").close();
                                    }
                                },
                                {
                                    xtype: "button",
                                    text: "关闭",
                                    cls: 'Common_Btn',
                                    handler: function() {
                                        this.up("window").close();
                                    }
                                }]
                            }]
                        });
                    }
					editingAgentIds = globalAgentIds.slice(0);
                    chosedAgentWinForSee.show();
                    pageBar.moveFirst();
				}
			},{
				xtype: 'button',
				margin: '0 5 0 0',
				cls: 'Common_Btn',
				text : '删除服务器',
				handler : function() {
					var records = agent_chosed_grid.getSelectionModel().getSelection();
                    if (records.length > 0) {
                        for (var i = 0, len = records.length; i < len; i++) {
                        	globalAgentIds.remove(records[i].get('iid'));
                        }
                        chosedPageBar.moveFirst();
                    } else {
                        Ext.Msg.alert('提示', "请选择服务器！");
                        return;
                    }
				}
			}]
    	}]
    });
    function clearQueryWhere(){
    	editingAgentIds = new Array();
    	osTypeComb.setValue('');
    	resourceGroupObj.setValue('');
    	agent_ip.setValue('');
		pubDesc_sm_ipsearch.setValue('');
    }
    var issuePanel = Ext.create('Ext.panel.Panel', {
        renderTo: "fileIssued_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border : false,
        items: [search_form, agent_chosed_grid]
    });

    contentPanel.on('resize', function() {
    	issuePanel.setHeight (contentPanel.getHeight () - modelHeigth);
    	issuePanel.setWidth (contentPanel.getWidth () );
    });
    
    
    var attachmentUploadWin = null;
    function selectFileFun(){
		var uploadForm;
		uploadForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
				id : 'file_id',
				fieldLabel: '选择文件',
				labelWidth: 65,
				anchor: '90%',
				// margin: '10 10 0 40',
				buttonText: '浏览',
				multipleFn: function($this){

			         var typeArray = ["application/x-shockwave-flash","audio/MP3","image/*","flv-application/octet-stream"];

			         var fileDom = $this.getEl().down('input[type=file]');

			         fileDom.dom.setAttribute("multiple","multiple");

			         fileDom.dom.setAttribute("accept",typeArray.join(","));

			},
			    listeners:{
			    	   afterrender: function(){
			    		this.multipleFn(this);
			    		},
			    		change: function(){
			    			var fileDom = this.getEl().down('input[type=file]'); 
			    			var files = fileDom.dom.files; 
			    			var str = ''; 
			    			for(var i = 0;  i < files.length;  i++){
			    			 str += files[i].name;
			    			 str += ' ';
			    			} 
			    			 Ext.getCmp('file_id').setRawValue(str);    //files为组件的id
			    			 this.multipleFn(this);
			    			}
			    	}
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData
			},{
				text : '取消',
				handler : function(){
					this.up("window").close();
				}
			}]
		});

		attachmentUploadWin = Ext.create('Ext.window.Window', {
			title : '附件信息',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 600,
			height: 180,
			items : [ uploadForm ],
			listeners : {
				close : function(g, opt) {
					uploadForm.destroy();
				}
			},
			/*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout : 'fit'
		});

		function upExeclData(){
			var form = uploadForm.getForm();
			var hdupfile=form.findField("files").getValue();
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate(form);
		}

		/** 自定义遮罩效果* */
		var myUploadMask = new Ext.LoadMask (contentPanel,
		{
			msg : "附件上传中..."
		});
		function uploadTemplate(form) {
			if (form.isValid()) {
				form.submit({
					url: 'uploadIssuedFile.do',
					success: function(form, action) {
						var success=Ext.decode(action.response.responseText).success;
						var msg = Ext.decode(action.response.responseText).message;
                      if(success){
                    	  var ids = Ext.decode(action.response.responseText).ids;
                    	  fileids.push.apply(fileids,ids.split(","));
                    	  var filename = Ext.decode(action.response.responseText).filename;
                    	  temp_filenames.push.apply(temp_filenames,filename.split(","));
                    	  var othername = Ext.decode(action.response.responseText).othername;
                    	  temp_othernames.push.apply(temp_othernames,othername.split(","));
                        	  
                              Ext.getCmp('sy-label').setValue(temp_filenames.join(","));
                        	  Ext.Ajax.request({
                                  url: 'insertIssued.do',
                                  method: 'POST',
                                  params: {
                                	  ids: fileids
                                  },
                                  success: function(response, request) {
                                      var success = Ext.decode(response.responseText).success;
                                      var message = Ext.decode(response.responseText).message;
                                      issuedid = Ext.decode(response.responseText).issuedid;
                                      if (success) {
                                    	  Ext.getCmp('issue_btn').enable();
                                      } else {
                                          Ext.Msg.alert('提示', message);
                                      }
                                  }
                              });
                      }else{
                    	  Ext.Msg.alert('提示',msg);
                      }
                      attachmentUploadWin.close();
				      myUploadMask.hide();
					},
					failure: function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',msg);
						myUploadMask.hide();
					}
				});
			}
		}
		attachmentUploadWin.show();
	}

	function checkFile(fileName){
	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
	    if(!file_reg.test(fileName)){  
	    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
	    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
	        return false;
	    }
	    return true;
	}
	
	function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:100,
		    frame: true,
		    // baseCls:'customize_gray_back',
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 80,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: [
					{
						id:'upldBtnIdAudi',
						text: '导入Agent文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
			    			if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}
							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'importAgentForFileIssued.do',
									params:{
										envType:0//生产
				                	},
								    success: function(form, action) {
								           var msg = Ext.decode(action.response.responseText).message;
								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
								    	   
								    	   if(msg!='' && msg != null) {
								    		   if(matchAgentIds && matchAgentIds.length>0) {
								    			    Ext.MessageBox.buttonText.yes = "确定"; 
								    				Ext.MessageBox.buttonText.no = "取消"; 
								    			    Ext.Msg.confirm("请确认", msg, function(id){
										  				 if(id=='yes'){
									  						Ext.Msg.alert('提示', "导入成功！");
													    	editingAgentIds = matchAgentIds;
												    	    pageBar.moveFirst();
										  				 }
										    		   });
								    		   } else {
								    			   Ext.Msg.alert('提示-没有匹配项', msg);
								    		   }
								    	   } else {
								    		    Ext.Msg.alert('提示', "导入成功！");
										    	editingAgentIds = matchAgentIds;
									    	    pageBar.moveFirst();
								    	   }
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}, {
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadAgentTemplate.do?fileName=FileIssuedAgentImportMould.xls';
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '设备信息批量导入',
		    width: 430,
		    height: 200,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}
	
});


