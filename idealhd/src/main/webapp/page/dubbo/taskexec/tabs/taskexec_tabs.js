var taskexectabs;
Ext.onReady(function() {
	taskexectabs = Ext.createWidget('tabpanel', {
		renderTo : 'grid_taskexec_tabs_area',
		resizeTabs : true,
		enableTabScroll : true,
		border : false,
		height : contentPanel.getHeight(),
		width:'100%',
		plain : true,
		defaults : {
			autoScroll : true,
			bodyPadding : '0 0 0 0'
		},
		items : []
	});

	Ext.Ajax.request({
		url : 'getRunTablsInfo.do',
		method : 'GET',
		 params : {
			    serviceName : serviceName_taskload,
				bussId : bussId_taskload,
				bussTypeId : bussTypeId_taskload,
				scriptName : scriptName_taskload,
				taskName : taskName_taskload,
				scriptType : scriptType_taskload,
				status : 1,
				onlyScript : 1
		 },
		success : function(response, request) {
			var data = Ext.decode(response.responseText);
			for (var i = 0; i < data.length; i++) {
				var tab = taskexectabs.add({
					title : data[i].taskName + ":(" + data[i].serviceName+")",
					border : false,
					loader : {
						url : 'getTabsloadAgentInfo.do',
						params : {
							coatId : data[i].coatId,
							serviceName : data[i].serviceName
						},
						contentType : 'html',
						autoLoad : i == 0 ? true : false,
						loadMask : false,
						scripts : true
					},
					listeners : {
						activate : function(tab) {
							execActivTabs = tab.title;
							tab.loader.load();
							lastRowIndex = -1;
							lastrequestId = -1;
							lastiip = "";
							lastiport = "";
							if (refreshObj) {
								clearInterval(refreshObj);
							}
							if (refreshTabObj) {
								clearInterval(refreshTabObj);
							}
							if (refreshObjShellOutput) {
								clearInterval(refreshObjShellOutput);
							}
						},
						'tabchange' : function(tab, newc, oldc) {
							execActivTabs = tab;
							taskexectabs.setActiveTab(execActivTabs);
							tab.loader.load();
							lastRowIndex = -1;
							lastrequestId = -1;
							lastiip = "";
							lastiport = "";
							if (refreshObj) {
								clearInterval(refreshObj);
							}
							if (refreshTabObj) {
								clearInterval(refreshTabObj);
							}
							if (refreshObjShellOutput) {
								clearInterval(refreshObjShellOutput);
							}
						}
					}

				});
			}
			if (data.length > 0) {
				taskexectabs.setActiveTab(0);
			}
			if (execActivTabs) {
				taskexectabs.items.each(function(item) {
					if (item.title == execActivTabs) {
						taskexectabs.setActiveTab(item);

					}
				});
			}
		},
		failure : function(result, request) {

		}
	});

});
