<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	//任务执行 跳转开关，浦发需求 开启后 直接跳转到最后一层展示，不展示图形 
	boolean taskExecShowSwitch = Environment.getInstance().getTaskExecShowSwitch();
	//显示适用平台开关
	boolean planFormSwitch = Environment.getInstance().getPlantFormSwitch();
	boolean firstClassSwitch = Environment.getInstance().getFirstclassShow();
	boolean secondClassSwitch = Environment.getInstance().getSecondclassShow();
	boolean versionSwitch = Environment.getInstance().getVersionSwitch();
	boolean scriptLevelSwitch = Environment.getInstance().getSScriptLevelSwitch();
	boolean butterflyVersionSwitch = Environment.getInstance().getButterflyVersionSwitch();
	boolean performUserSwitch = Environment.getInstance().getPerformUserSwitch();
	//标签
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	//功能分类
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
	String taskStateForm = request.getParameter("taskStateForm")==null? "undefined" :request.getParameter("taskStateForm");
	if(taskStateForm.isEmpty()){
		taskStateForm="undefined";
	}
	// 福建农信CI
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
%>
<html>
<head>
<script>
    var tbsexec_count = '<%=request.getAttribute("count")%>';
    var whichDoForTaskexec = '<%=request.getAttribute("whichDo")%>';
    var taskExecShowSwitchForTaskexec = <%=taskExecShowSwitch%>;
    var taskNameForTaskexec = '<%=request.getParameter("taskName")==null?"":request.getParameter("taskName")%>';
    var planFormSwitchForTaskexec=<%=planFormSwitch%>;
    var firstClassSwitchForTaskexec=<%=firstClassSwitch%>;
    var secondClassSwitchForTaskexec=<%=secondClassSwitch%>;
    var versionSwitchForTaskexec=<%=versionSwitch%>;
    var scriptLevelSwitchForTaskexec=<%=scriptLevelSwitch%>;
    var butterflyVersionSwitchForTaskexec=<%=butterflyVersionSwitch%>;
    var performUserSwitchForTaskexec=<%=performUserSwitch%>;
	var scriptTaskApplySs= '<%=request.getParameter("scriptTaskApplySs")==null?"":request.getParameter("scriptTaskApplySs")%>';
	var labelSwitch = <%=sdScriptLabelEditSwitch%>;
	var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
	//当前登录用户为组长、副组长时组下成员（字符串ifullname：name1,name2,name3,name4）
	var groupUserIIDs = '<%=request.getAttribute("groupUserIIDs")==null?"":request.getAttribute("groupUserIIDs")%>';
	var loginNames = '<%=request.getAttribute("loginNames")==null?"":request.getAttribute("loginNames")%>';
	//登录用户名称，为申请人默认查询条件
	var userTrueName = '<%=request.getAttribute("userName")%>';

	var serviceName = '<%=request.getParameter("serviceName")==null?"":request.getParameter("serviceName")%>';
	var platForm1 = '<%=request.getParameter("platForm")==null?"":request.getParameter("platForm")%>';
	var taskName = '<%=request.getParameter("taskName")==null?"":request.getParameter("taskName")%>';
	var taskStateForm1 = <%=taskStateForm%>;
	var scriptName = '<%=request.getParameter("scriptName")==null?"":request.getParameter("scriptName")%>';
	var bsName ='<%=request.getParameter("bsName")==null?"":request.getParameter("bsName")%>';
	var bussType ='<%=request.getParameter("bussType")==null?"":request.getParameter("bussType")%>';
	var fjnxCISwitch = <%=fjnxCISwitch%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/taskexec/taskexec.js"></script>

<style type="text/css">
		 .script_exec_gray{
		 color: #b6b6b6 !important;
		}
</style>
</head>
<body>
<div id="scriptexec_taskexec_area" style="width: 100%;height: 100%">
</div>
</body>
</html>