//var scripts_store;

var auditingWin;
Ext.onReady(function() {
			Ext.tip.QuickTipManager.init();
			destroyRubbish();
			
function trim(str) {
	if(str!=null){
		str = str.toString().replace(/^(\s|\u00A0)+/, '');
		for (var i = str.length - 1; i >= 0; i--) {
			if (/\S/.test(str.charAt(i))) {
				str = str.substring(0, i + 1);
				break;
			}
		}
	}
	return str;
}
String.prototype.trim = function ()
{
	return this.replace (/(^\s*)|(\s*$)/g, "");
}
var jmz = {};
jmz.GetLength = function(str) {
  var realLength = 0, len = str.length, charCode = -1;
  for (var i = 0; i < len; i++) {
    charCode = str.charCodeAt(i);
    if (charCode >= 0 && charCode <= 128) realLength += 1;
    else realLength += 2;
  }
  return realLength;
};

	function forwardScriptExecAudiForTaskIndex(uuid, serviceId, a, b, c, scriptLevel, scriptType, isFlow, menuId, newTitle, platmFrom, timeout, label, taskName,workItemId,isTimetask, taskTime) {
		var whiteScript = "";
		if (scriptLevel == 0) {
			whiteScript = "whiteScript";
		}
		auditingWin = Ext.create('widget.window', {
			title: '编辑：' + newTitle,
			closable: true,
			closeAction: 'destroy',
			width: contentPanel.getWidth(),
			minWidth: 350,
			height: contentPanel.getHeight() + 30,
			draggable: false,
			// 禁止拖动
			resizable: false,
			// 禁止缩放
			modal: true,
			loader: {
				url: 'queryOneServiceForAuditing.do',
				params: {
					iid: serviceId,
					scriptType: scriptType,
					scriptLevel: scriptLevel,
					platmFrom: platmFrom,//适用平台
					serviceName: a,
					whiteScript: whiteScript,
					timeout: timeout,
					uuid: uuid,
					label: label,
					editDelay: 'edit',
					taskName: taskName,
					workItemId: workItemId,
					isTimetask: isTimetask,
					taskTime: taskTime,
					// heightPermissionFlag: heightPermissionFlag
				},
				autoLoad: true,
				scripts: true
			}
		});
		auditingWin.show();
	}

			
			var bussData = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'bsName' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var bussTypeData = Ext.create('Ext.data.Store', {
				fields : [ 'sysTypeId', 'sysType' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var cataStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
					"id" : "-1",
					"name" : "全部"
				}, {
					"id" : "sh",
					"name" : "shell"
				}, {
					"id" : "bat",
					"name" : "bat"
				}, {
					"id" : "py",
					"name" : "python"
				}, {
					"id" : "perl",
					"name" : "perl"
				} /*
					 * , {"id":"-2", "name":"组合"},
					 */
				]
			});
			var bussCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'bsName',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25.5%',
				queryMode : 'local',
				fieldLabel : '一级分类',
				padding : '5',
				displayField : 'bsName',
				valueField : 'iid',
				hidden : !firstClassSwitchForTaskexecDelay,
				editable : true,
				emptyText : '--请选择一级分类--',
				store : bussData,
				listeners : {
					change : function() { // old is keyup
						bussTypeCb.clearValue();
						bussTypeCb.applyEmptyText();
						bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
						if(!Ext.isEmpty(this.value)){
							bussTypeData.load({
								params : {
									fk : this.value
								}
							});
						}
					},
					 specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	scriptservice_grid.ipage.moveFirst();
			                }
			            }
				}
			});

			/** 操作类型* */
			var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'bussType',
				padding : '5',
				labelWidth : 70,
				labelAlign : 'right',
				width : '24.5%',
				queryMode : 'local',
				fieldLabel : '二级分类',
				displayField : 'sysType',
				valueField : 'sysTypeId',
				hidden : !secondClassSwitchForTaskexecDelay,
				editable : true,
				emptyText : '--请选择二级分类--',
				store : bussTypeData,
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            },
					change: function() { // old is keyup
						threeBussTypeCb.clearValue();
						threeBussTypeCb.applyEmptyText();
						threeBussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
						if(!Ext.isEmpty(this.value)){
							threeBussTypeData.load({
								params: {
									fk: this.value
								}
							});
						}
					},
					beforequery: function(e) {
						var combo = e.combo;
						if (!e.forceAll) {
							var value = Ext.util.Format.trim(e.query);
							combo.store.filterBy(function(record, id) {
								var text = record.get(combo.displayField);
								return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
							});
							combo.expand();
							return false;
						}
					}
		        }
			});

			//北京邮储 三级分类
			var threeBussTypeData = Ext.create('Ext.data.Store', {
				fields: ['threeBsTypeId', 'threeBsTypeName'],
				autoLoad: false,
				proxy: {
					type: 'ajax',
					url: 'bsManager/getThreeBsTypeByFk.do',
					reader: {
						type: 'json',
						root: 'dataList'
					}
				}
			});

			/*threeBussTypeData.on('load', function(store, options) {
				if(threeBsTypeId) {
					threeBussTypeCb.setValue(threeBsTypeId);
				}
			});*/
			var threeBussTypeCb = Ext.create('Ext.form.field.ComboBox', {
				name: 'threeBussTypeCb',
				labelWidth: 70,
				queryMode: 'local',
				fieldLabel: '三级分类',
				displayField: 'threeBsTypeName',
				valueField: 'threeBsTypeId',
				editable: true,
				width : '24.8%',
				labelAlign : 'right',
				hidden:!secondClassSwitchForTaskexec || !scriptThreeBstypeSwitch,
				emptyText: '--请选择三级分类--',
				store: threeBussTypeData,
				listeners: {
					beforequery: function(e) {
						var combo = e.combo;
						if (!e.forceAll) {
							var value = Ext.util.Format.trim(e.query);
							combo.store.filterBy(function(record, id) {
								var text = record.get(combo.displayField);
								return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
							});
							combo.expand();
							return false;
						}
					}
				}
			});
			var tsName = new Ext.form.TextField({
				name : 'taskName',
				fieldLabel : '任务名称',
				displayField : 'taskName',
				emptyText : '--请输入任务名称--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            }
		        }
			});
			/** 脚本类型* */
			var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
				name : 'scriptTypeParam',
				padding : '5',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25%',
				queryMode : 'local',
				fieldLabel : '脚本类型',
				displayField : 'name',
//				hidden : true,
				hidden : scriptTypeSwitchForTaskexecDelay,
				valueField : 'id',
				editable : false,
				emptyText : '--请选择脚本类型--',
				store : cataStore,
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            }
		        }
			});

			var sName = new Ext.form.TextField({
				name : 'serviceName',
				fieldLabel : '服务名称',
				displayField : 'serverName',
				emptyText : '--请输入服务名称--',
				labelWidth : 70,
				padding : '5',
				labelAlign : 'right',
				width : '24%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            }
		        }
			});
			var scName = new Ext.form.TextField({
				name : 'scriptName',
				fieldLabel : '脚本名称',
				displayField : 'scriptName',
				emptyText : '--请输入脚本名称--',
				labelWidth : 70,
				padding : '5',
				labelAlign : 'right',
				width : '25%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            }
		        }
			});
			
			var bfVersion = new Ext.form.TextField({
				name : 'butterflyVersion',
				fieldLabel : '单号',
				displayField : 'butterflyVersion',
				emptyText : '--请输入单号--',
				labelWidth : 70,
				padding : '5',
				hidden : !butterflyVersionSwitchForTaskexecDelay,
				labelAlign : 'right',
				width : '24%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            }
		        }
			});
			
			var pUser = new Ext.form.TextField({
				name : 'performUser',
				fieldLabel : '申请人',
				displayField : 'performUser',
				emptyText : '--请输入申请人--',
				labelWidth : 70,
				value:userTrueName,
				padding : '5',
				hidden : !performUserSwitchForTaskexecDelay,
				labelAlign : 'right',
				width : '24%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	scriptservice_grid.ipage.moveFirst();
		                }
		            }
		        }
			});
			
			 var platformStore = Ext.create('Ext.data.JsonStore', {
			fields: ['INAME', 'ICODEVALUE'],
			//autoDestroy : true,
			autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getScriptPlatformCode.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
	    
		platformStore.on('beforeload', function(store, options) {
				var new_params = {
					gdSwitch :  '0'
				};
				Ext.apply(platformStore.proxy.extraParams, new_params);
		});
			var platForm = Ext.create('Ext.form.field.ComboBox', {
					name : 'platForm',
					// padding : '5',
					labelWidth : 70,
					queryMode : 'local',
					fieldLabel : '适用平台',
					displayField: 'INAME',
			        valueField: 'ICODEVALUE',
			        hidden : !planFormSwitchForTaskexecDelay,
					editable : true,
					emptyText : '--请选择适用平台--',
					store : platformStore,
					width : '24%',
					labelAlign : 'right',
					listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	scriptservice_grid.ipage.moveFirst();
			                }
			            }
			        }
				});

			/** 保存按钮* */
			var saveBtn = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    text : "保存",
				hidden: fjnxCISwitch,
			    handler : updateScriptTimeTask
//			    handler : function(){
//			    	alert('保存。。。');
//			    }
			});
			var search_form = Ext.create('Ext.form.Panel', {
				layout : 'anchor',
				region : 'north',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [sName, scName,tsName, scriptTypeParam]
				},  {
					xtype : 'toolbar',
					dock : 'top',
					items :	[  platForm, bussCb, bussTypeCb,threeBussTypeCb ]
				},{
					xtype : 'toolbar',
					dock : 'top',
					items :	[bfVersion,pUser,'->',saveBtn,{
						xtype : 'button',
						id:'queryBtn2',
						text : '查询',
						cls : 'Common_Btn',
						handler : function(){
//									Ext.getCmp('queryBtn2').setDisabled(true);
//									  setTimeout(function() {
//										  Ext.getCmp('queryBtn2').setDisabled(false);
//										  }, 3000);
							scripts_store.reload();
						}
					}, {
						xtype : 'button',
						text : '清空',
						cls : 'Common_Btn',
						handler : function() {
							clearQueryWhere();
						}
					}]
				}]
			});

			Ext.define('scriptService', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'coatId',
					type : 'string'
				},{
					name : 'planId',
					type : 'string'
				}, {
					name : 'workItemId',
					type : 'string'
				}, {
					name : 'taskName',
					type : 'string'
				}, {
					name : 'serviceName',
					type : 'string'
				}, {
					name : 'sysName',
					type : 'string'
				}, {
					name : 'bussName',
					type : 'string'
				},{name : 'threeBsTypeName'    ,type : 'string'},{
					name : 'scriptType',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'bfVersion',
					type : 'string'
				}, {
					name : 'pUser',
					type : 'string'
				},{
					name : 'servicePara',
					type : 'string'
				}, {
					name : 'platForm',
					type : 'string'
				}, {
					name : 'content',
					type : 'string'
				}, {
					name : 'version',
					type : 'string'
				}, {
					name : 'taskTime',
					type : 'string'
				}, {
					name : 'nextStartTime',
					type : 'string'
				}, {
					name : 'bussId',
					type : 'int'
				}, {
					name : 'scriptLevel',
					type : 'int'
				}, {
					name : 'bussTypeId',
					type : 'int'
				}, {
					name : 'isFlow',
					type : 'string'
				}, {
					name : 'state',
					type : 'string'
				}, {
					name : 'performUser',
					type : 'string'
				}, {
					name : 'isTimetask',
					type : 'number'
				}, {
					name : 'runstate',
					type : 'number'
				}]
			});

			var scripts_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				pageSize : 50,
				model : 'scriptService',
				proxy : {
					type : 'ajax',
					url : 'getScriptServiceAudiListDelay.do',
					actionMethods: {
						create: 'POST',
						read: 'POST', // by default GET
						update: 'POST',
						destroy: 'POST'
					},
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			scripts_store.on('beforeload', function(store, options) {
				var new_params = {
					serviceName : search_form.getForm().findField("serviceName").getValue().trim(),
					bussId : search_form.getForm().findField("bsName").getValue() || 0,
					bussTypeId : search_form.getForm().findField("bussType").getValue() || 0,
					threeBsTypeId:search_form.getForm().findField("threeBussTypeCb").getValue()||0,
					scriptName : search_form.getForm().findField("scriptName").getValue().trim() || '',
					scriptType : search_form.getForm().findField("scriptTypeParam").getValue() || '',
					platForm:platForm.getValue(),
					taskName : search_form.getForm().findField("taskName").getValue().trim() || '',
					bfVersion : search_form.getForm().findField("butterflyVersion")
							.getValue().trim()|| '',
					pUser : search_form.getForm().findField("performUser")
							.getValue().trim()|| '',
					status : 1,
					onlyScript : 1
				};

				Ext.apply(scripts_store.proxy.extraParams, new_params);
			});

	scripts_store.on('load', function(store, options) {
		var elems = Array.prototype.slice.call(document.querySelectorAll('.js-switch-small'));
		$(elems).change(function(e) {
			var workItemId = $(e.target).attr('workItemId');
			var checkFlag = e.target.checked
			if (checkFlag) {
				console.log(1)
				toggleSwitchFunc(workItemId, 'start')
			} else {
				console.log(2)
				toggleSwitchFunc(workItemId, 'stop')
			}
		})
		for (var i = 0; i < elems.length; i++) {
			$(elems[i]).attr('workItemId', store.data.items[i].data.workItemId);
			var switchery = new Switchery(elems[i], { size: 'small' });
		}

	});

	scripts_store.on('columnhide', function(ct, column) {
		scripts_store.reload();
		var elemsInit = Array.prototype.slice.call(document.querySelectorAll('.js-switch-small'));
		$(elemsInit).change(function(e) {
			console.log(e.target.checked);
		})
		for (var i = 0; i < elemsInit.length; i++) {
			$(elemsInit[i]).attr('workItemId', scripts_store.data.items[i].data.workItemId);
			var switchery = new Switchery(elemsInit[i], { size: 'small' });
		}
	});
	scripts_store.on('columnshow', function(ct, column) {
		scripts_store.reload();
		var elemsInit = Array.prototype.slice.call(document.querySelectorAll('.js-switch-small'));
		$(elemsInit).change(function(e) {
			console.log(e.target.checked);
		})
		for (var i = 0; i < elemsInit.length; i++) {
			$(elemsInit[i]).attr('workItemId', scripts_store.data.items[i].data.workItemId);
			var switchery = new Switchery(elemsInit[i], { size: 'small' });
		}
	});
	scripts_store.on('sortchange', function(ct, column) {
		scripts_store.reload();
		var elemsInit = Array.prototype.slice.call(document.querySelectorAll('.js-switch-small'));
		$(elemsInit).change(function(e) {
			console.log(e.target.checked);
		})
		for (var i = 0; i < elemsInit.length; i++) {
			$(elemsInit[i]).attr('workItemId', scripts_store.data.items[i].data.workItemId);
			var switchery = new Switchery(elemsInit[i], { size: 'small' });
		}
	});

	function toggleSwitchFunc(workItemId, func) {
		Ext.Ajax.request({
			url: 'toggleScriptTaskDelay.do',
			method: 'POST',
			params: {
				workItemId:workItemId,
				func: func,
			},
			success: function (response) {
				var data = Ext.decode(response.responseText);
				if (data.success) {
					scripts_store.reload();
				}
				Ext.Msg.alert('提示', data.message)
			}
		})
	}
			
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			var scriptservice_columns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '主键',
						dataIndex : 'iid',
						hidden : true
					},
					{
						text : 'cId',
						dataIndex : 'coatId',
						hidden : true
					},
					{
						text : 'planId',
						dataIndex : 'planId',
						hidden : true
					},
					{
						text : 'wId',
						dataIndex : 'workItemId',
						hidden : true
					},
					{
						text : '状态',
						dataIndex : 'state',
						hidden : true,
						width : 100,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == "10") {
								backValue = '<span class="Not_running State_Color">未运行</span>';
							}else if (value == '12') {
								backValue = '<span class="Run_Green State_Color">Delay运行</span>';
							}else if (value == "30"){
								backValue = '<span class="Abnormal_yellow State_Color">出现异常</span>';
							} else {
								backValue = '<span class="Run_Green State_Color">NO_'+value+'</span>';
							}
							return backValue;
						}
					},
					{
						text : '任务名称',
						dataIndex : 'taskName',
						flex : 1,
						minWidth : 140,
						width : 140,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '服务名称',
						dataIndex : 'serviceName',
						flex : 1,
						minWidth : 140,
						width : 140,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '脚本名称',
						dataIndex : 'scriptName',
						flex : 1,
						minWidth : 130,
						width : 130,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '单号',
						dataIndex : 'bfVersion',
						width : 100,
						hidden : !butterflyVersionSwitchForTaskexecDelay
					},
					{
						text : '申请人',
						dataIndex : 'pUser',
						width : 100,
						hidden : !(performUserSwitchForTaskexecDelay || fjnxCISwitch)
					},
					{
						text : '适用平台',
						dataIndex : 'platForm',
						width : 80,
						hidden : !planFormSwitchForTaskexecDelay
					},
					{
						text : '一级分类',
						dataIndex : 'sysName',
						width : 90,
						hidden : !firstClassSwitchForTaskexecDelay
					},
					{
						text : '二级分类',
						dataIndex : 'bussName',
						width : 90,
						hidden : !secondClassSwitchForTaskexecDelay
					},{
						text : '三级分类',
						dataIndex : 'threeBsTypeName',
						hidden:!secondClassSwitchForTaskexecDelay||!scriptThreeBstypeSwitch,
						width :100
					},
					{
						text : '脚本类别',
						dataIndex : 'scriptType',
						width : 80,
						hidden : scriptTypeSwitchForTaskexecDelay,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == "sh") {
								backValue = "shell";
							} else if (value == "perl") {
								backValue = "perl";
							} else if (value == "py") {
								backValue = "python";
							} else if (value == "bat") {
								backValue = "bat";
							} else if (value == "sql") {
								backValue = "sql";
							}
							if (record.get('isFlow') == '1') {
								backValue = "组合";
							}
							return backValue;
						}
					},
					// { text: '创建者', dataIndex: 'createdUser',width:130},
					{
						text : '版本',
						dataIndex : 'version',
						width : 40,
						hidden : !versionSwitchForTaskexecDelay
					},
					{
						text : '周期',
						dataIndex : 'taskTime',
						editor :{},
						width : 100
					},
					{
						text : '下次执行时间',
						dataIndex : 'nextStartTime',
						width : 144,
						renderer: function(value, metaData, record) {
							if (fjnxCISwitch) {
								var runstate = record.get('runstate');
								if (runstate == 0) { //禁用
									return '';
								}
							}
							return value;
						}
					},
					{
//						text : '脚本级别',
						text : '风险级别',
						dataIndex : 'scriptLevel',
						width : 70,
						hidden : !scriptLevelSwitchForTaskexecDelay,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == 1) {
								backValue = '<font color="#F01024">高级风险</font>';
							} else if (value == 2) {
								backValue = '<font color="#FF7824">中级风险</font>';
							} else if (value == 3) {
								backValue = '<font color="#FFA826">低级风险</font>';
							}else if (value == 0) {
								backValue = '<font color="#FFA826">白名单</font>';
							}
							return backValue;
						}
					},{
						text: '启用',
						dataIndex: 'runstate',
						hidden: !fjnxCISwitch,
						renderer: function(value, metaData, record) {
							var runstate = record.get('runstate');
							var returnMess = "";
							if (runstate == 0) { //禁用
								returnMess = '<input type="checkbox" class="js-switch-small" />';
							} else if (runstate > 0) {//已启用
								returnMess = '<input type="checkbox" class="js-switch-small"  checked />';
							} else {
								returnMess = '';
							}
							return returnMess;
						}
					},
					{
						text : '操作',
						xtype : 'actiontextcolumn',
						width : 420,
						align : 'left',
//						menuDisabled : true,
						items : [
							{
							text: '编辑',
							iconCls: 'script_edit',
							getClass: function (v, metadata, record) {
								if (!fjnxCISwitch) {
									return 'x-hidden';
								}
							},
							handler: function (grid, rowIndex) {
								var runState = grid.getStore().data.items[rowIndex].data.runstate;
								if (runState > 0) {
									Ext.Msg.alert('提示', '启动状态下，不允许编辑');
									return;
								}

								var taskName  = grid.getStore().data.items[rowIndex].data.taskName;
								var iid = grid.getStore().data.items[rowIndex].data.iid;
								var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
								var isflow = grid.getStore().data.items[rowIndex].data.isFlow;
								var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
								var scriptName = grid.getStore().data.items[rowIndex].data.scriptName; //浦发
								var newTitle = serviceName + ' — ' + scriptName;//浦发需求
								var bussId = grid.getStore().data.items[rowIndex].data.bussId;
								var bussTypeId = grid.getStore().data.items[rowIndex].data.bussTypeId;
								var scriptLevel = grid.getStore().data.items[rowIndex].data.scriptLevel;
								var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
								var platForm = grid.getStore().data.items[rowIndex].data.platForm;   //浦发需求
								var timeout = grid.getStore().data.items[rowIndex].data.timeout;   // 邮储需求  脚本超时时间
								var label = grid.getStore().data.items[rowIndex].data.label;
								var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
								var isTimetask = grid.getStore().data.items[rowIndex].data.isTimetask;
								var taskTime = grid.getStore().data.items[rowIndex].data.taskTime;
								if (isTimetask == 2) {
									taskTime = grid.getStore().data.items[rowIndex].data.nextStartTime;
								}
								forwardScriptExecAudiForTaskIndex(uuid, iid, serviceName, bussId, bussTypeId, scriptLevel, scriptType, isflow, 0, newTitle, platForm, timeout, label, taskName, workItemId, isTimetask, taskTime);
							}
						},{
									text : '终止任务',
									iconCls : 'script_termina',
									getClass : function(v, metadata, record) {
										    var user =record.get('performUser');
						 				    if(user != loginNameForTaskexecDelay){
						 				    	return 'x-hidden';
						 				    }
									},
									handler : function(grid, rowIndex) {
										var iid = grid.getStore().data.items[rowIndex].data.iid;
										var serviceName =grid.getStore().data.items[rowIndex].data.serviceName;
										var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
										var cid = grid.getStore().data.items[rowIndex].data.coatId;
										forwardScriptTimeTaskKill(iid, serviceName, workItemId, cid);
									}
								}, {
									text : '任务详情',
									iconCls : 'script_termina',
									getClass : function(v, metadata, record) {
						 				     
									},
									handler : function(grid, rowIndex) {
										var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
										forwordAudiDetail(workItemId);
									}
								},{
									text : '执行历史',
									iconCls : 'execute',
									getClass : function(v, metadata, record) {
										  	var user =record.get('performUser');
						 				    if(user != loginNameForTaskexecDelay){
						 				    	return 'x-hidden';
						 				    }
									},
									handler : function(grid, rowIndex) {
										var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
										var user = grid.getStore().data.items[rowIndex].data.performUser;
										  if(user == loginNameForTaskexecDelay){
											flowHistoryFun(workItemId);
										}
									}
								}]
				    }
//					{
//						text : '操作',
//						dataIndex : 'xq',
//						width : 420,
//						align : 'left',
//						renderer : function(value, p, record) {
//							var iid = record.get('iid');
//							var serviceName = record.get('serviceName');
//							var workItemId = record.get('workItemId');
////							var execFuncName = "forwardScriptTaskExecStart";
////							var execFuncNameStop = "forwardScriptTaskExecStop";
//							var execFuncNameCancel = "forwardScriptTimeTaskCancel";
//							var execScriptServiceDelayKill = "forwardScriptTimeTaskKill";
//							var wid = record.get('workItemId');
//							var cid = record.get('coatId');
//							var state = record.get('state');
//							var itaskTime = record.get('taskTime');
//							var user = record.get('performUser');
//							var    flowHistory = '<span class="switch_span"><a href="javascript:void(0)" onclick="flowHistoryFun('+wid+')" style=""><img src="images/monitor_bg.png"  align="absmiddle" class="execute"></img>&nbsp;执行历史</a></span>';
//							var    forwordAudi =	 '<span class="switch_span"><a href="javascript:void(0)" onclick="forwordAudiDetail('+ wid+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;任务详情</a></span>'
//							if(user == loginNameForTaskexecDelay){
//								return '<span class="switch_span" hidden><a href="javascript:void(0)" onclick="'
//								+ execFuncNameCancel
//								+ '('
//								+ iid
//								+ ',\''
//								+ serviceName
//								+ '\','
//								+ workItemId
//								+ ','
//								+ cid
//								+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_cancel"></img>&nbsp;取消</a></span>&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="'
//								+ execScriptServiceDelayKill
//								+ '('
//								+ iid
//								+ ',\''
//								+ serviceName
//								+ '\','
//								+ workItemId
//								+ ','
//								+ cid
//								+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;终止任务</a></span>'+ '&nbsp;&nbsp;&nbsp;&nbsp;'+forwordAudi+ '&nbsp;&nbsp;&nbsp;&nbsp;'+flowHistory;
//							
//							}else{
//								return '<span class="switch_span" hidden>'+
//							       '<a href="javascript:void(0)" onclick="'+ execFuncNameCancel
//									+ '('
//									+ iid
//									+ ',\''
//									+ serviceName
//									+ '\','
//									+ workItemId
//									+ ','
//									+ cid
//									+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_cancel"></img>&nbsp;取消'+
//							   	   '</a>'+
//							   '</span>'+'&nbsp'+forwordAudi;
//							}
//							
//						}
//					} 
					];
//			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//			  	store: scripts_store,
//			    dock: 'bottom',
//			    displayInfo: true,
//			    emptyMsg:'找不到任何记录'
//		    });
			// 定义可编辑grid组件
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});
			var scriptservice_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
				id:'scriptservice_grid',
				region : 'center',
//				height : contentPanel.getHeight() * 0.4,
//				width : contentPanel.getWidth() - 18,
				store : scripts_store,
				 border : false,
				padding : grid_space,
				 ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				columnLines : true,
				//selModel:selModel,
				columns : scriptservice_columns,
				plugins : [ cellEditing ],
				listeners : {
					beforeedit: function () {
						if (fjnxCISwitch) {
							return false
						}
						return true
					}
				}
			});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "scriptexec_taskexecDelay_area",
				layout : 'border',
				border : false,
				height : isTabSwitch?contentPanel.getHeight() - modelHeigth -40:contentPanel.getHeight() - modelHeigth,
//				width : '100%',
				width : contentPanel.getWidth (),
				items : [ search_form, scriptservice_grid ]
			});

			function clearQueryWhere() {
				search_form.getForm().findField("bsName").setValue('');
				search_form.getForm().findField("bussType").setValue('');
				search_form.getForm().findField("scriptName").setValue('');
				search_form.getForm().findField("serviceName").setValue('');
				search_form.getForm().findField("scriptTypeParam").setValue('');
				search_form.getForm().findField("taskName").setValue('');
				search_form.getForm().findField("performUser").setValue('');
				search_form.getForm().findField("butterflyVersion").setValue('');
				platForm.setValue();
			}

			contentPanel.on('resize', function() {
				mainPanel.setHeight (contentPanel.getHeight () );
				mainPanel.setWidth (contentPanel.getWidth ()-modelHeigth );
			});
			
			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
			{
				Ext.destroy (mainPanel);
				destroyRubbish();
				if (Ext.isIE)
				{
					CollectGarbage ();
				}
			});
			
			/** 修改定时任务周期 */
			function updateScriptTimeTask (serviceId, serviceName, workItemId, itaskTime,coatId)
			{
				var records = scripts_store.getModifiedRecords();
				//没有修改
				if(records.length==0)
				{
					Ext.Msg.show({
					     title:'提示',
					     msg: '周期没有任何修改',
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.INFO
					});
					return;
				}else{
					Ext.Msg.confirm('系统提示', '确认修改定时任务周期吗?', function(btn) {
						if (btn == 'yes') {
							var lstAddRecord = new Array(); 
							var flag = true;
							Ext.each(records, function(record) {

								//周期为空
								if(record.data.taskTime.trim()==""){
									Ext.Msg.show({
									     title:'提示',
									     msg: '周期不能为空',
									     buttons: Ext.Msg.OK,
									     icon: Ext.Msg.INFO
									});
									flag = false;
									return;
								}
								//周期长度超过255
								if(jmz.GetLength(record.data.taskTime.trim())>255){
									Ext.Msg.show({
									     title:'提示',
									     msg: '周期不能超过255个字符',
									     buttons: Ext.Msg.OK,
									     icon: Ext.Msg.INFO
									});
									flag = false;
									return;
								}

								lstAddRecord.push(record.data);
							});
							if(flag)
							{
								Ext.Ajax.request({
									url: 'updateTaskTimeScriptServiceDelay.do',
									params : {
										jsonData : Ext.encode(lstAddRecord)
									},
									success: function(resp,opts) 
									{			
										 var respText = Ext.JSON.decode(resp.responseText);
										 var success =  respText.success;
										 var msg =  respText.message;
										 if(success){
											 Ext.Msg.alert('提示',msg);
											 Ext.getCmp("scriptservice_grid").store.reload();
										 }else{
											 Ext.Msg.alert('提示', msg);
										 }
									 },
									 failure : function(result, request) {
										 secureFilterRs(result,"操作失败！");
									 } 		
								});
							}
						}
					});
				}
			}
		
			
			
		//取消
function forwardScriptTimeTaskCancel(serviceId, serviceName, workItemId, coatId) {
	Ext.Msg.confirm('系统提示', '取消任务，请确认?', function(btn) {
	if (btn == 'yes') {
		destroyRubbish(); // 销毁本页垃圾
		Ext.Ajax.request({
//				url : 'execScriptServiceStop.,
			url : 'execScriptServiceDelayCancel.do',
			method : 'POST',
			params : {
				serviceId : serviceId,
				serviceName : serviceName,
				workItemId : workItemId,
				coatId : coatId
			},
			success : function(response, request) {
				Ext.Msg.alert('提示', '操作成功！');
				Ext.getCmp("scriptservice_grid").store.reload();
				// execTabsPanel.setHeight(contentPanel.getHeight() * 0.85);
				// scriptservice_grid.setHeight(contentPanel.getHeight() *
				// 0.6);
				// mainPanel.setHeight(contentPanel.getHeight() - 61
				// + contentPanel.getHeight() * 0.8);
			},
			failure : function(result, request) {
				Ext.Msg.alert('提示', '执行失败！');
				}
			});
		}
	});
}

function forwordAudiDetail(workItemId) {
    var auditingWin = Ext.create('widget.window', {
    title: '任务详情',
    closable: true,
    closeAction: 'destroy',
    maximizable : true,
    width: contentPanel.getWidth(),
    minWidth: 350,
    height: contentPanel.getHeight(),
    draggable: false,
    // 禁止拖动
    resizable: true,
    // 禁止缩放
    modal: true,
    loader: {
        url: 'initReviewForSsPublishExec.do',
        params: {
        	iworkItemid : workItemId,
        	from :'100',
        	submitType : 'tc',
            	istate : 1,
            	btnCode : 25
            },
            autoLoad: true,
            scripts: true
        },
        listeners : {
        	maximize:function(){
        		Ext.getCmp("taskexecmainpanel").setHeight(contentPanel.getHeight());
        		Ext.getCmp("taskexecmainpanel").setWidth(contentPanel.getWidth());
        		Ext.getCmp("execDescForm").setHeight(contentPanel.getHeight()*0.83*0.4);
        		Ext.getCmp("funcdesc").setHeight(contentPanel.getHeight()*0.83*0.4);
        	},
        	
        	restore:function(){
        		Ext.getCmp("taskexecmainpanel").setHeight(contentPanel.getHeight()*0.9);
        		Ext.getCmp("taskexecmainpanel").setWidth(contentPanel.getWidth()*0.95);
        		Ext.getCmp("execDescForm").setHeight(contentPanel.getHeight()*0.83*0.3);
        	}
        	
        }
    });
auditingWin.show();
}

function flowHistoryFun(wid){
	var serviceName='';
var state =-2;
var startTime ='';
var endTime ='';
var taskName = '';
var filter_audiUser = '';
var flowid;
Ext.Ajax.request({
    url: 'queryMaxFlowId.do',
    method: 'POST',
    async: false,
    params: {
        workItemId: wid
    },
    success: function(response, options) {
    	flowid = Ext.decode(response.responseText).flowid;
    		if(flowid>0){
    			contentPanel.getLoader().load({
    				url: "forwardscriptflowcoatforexec.do",
    				scripts: true,
    				params : {
    					flowId:flowid,
    					flag:1, 
    					forScriptFlow: 1,
    					filter_serviceName:serviceName,
    					filter_serviceState:state,
    					filter_serviceStartTime:startTime,
    					filter_serviceEndTime:endTime,
    					filter_serviceTaskName : taskName,
    					filter_audiUser : filter_audiUser,
						scriptTaskApplySs:scriptTaskApplySsForTaskexecDelay,
    					fromMenu:'taskexec',
						workitemid: wid
    				}
    			});
    		}else{
    			Ext.Msg.alert('提示', '该任务没有执行历史！');
        			return;
        		}
        	
        },
        failure: function(result, request) {
        }
    });
}

function forwardScriptTimeTaskKill(serviceId, serviceName, workItemId, coatId) {
	Ext.Msg.confirm('系统提示', '您确定要进行终止操作吗?', function(btn) {
	if (btn == 'yes') {
		destroyRubbish(); // 销毁本页垃圾
		Ext.Ajax.request({
			url : 'execScriptServiceDelayKill.do',
			method : 'POST',
			params : {
				serviceId : serviceId,
				serviceName : serviceName,
				workItemId : workItemId,
				coatId : coatId
			},
			success : function(response, request) {
				Ext.Msg.alert('提示', '操作成功！');
				Ext.getCmp("scriptservice_grid").store.reload();
			},
			failure : function(result, request) {
				Ext.Msg.alert('提示', '执行失败！');
				}
			});
		}
	});
}
			
			
	});



////终止
//function forwardScriptTaskExecStop(serviceId, serviceName, workItemId, coatId) {
//	Ext.Msg.confirm('系统提示', '您确定要进行终止操作吗?', function(btn) {
//		if (btn == 'yes') {
//			destroyRubbish(); // 销毁本页垃圾
//			Ext.Ajax.request({
//				url : 'execScriptServiceStop.do',
//				method : 'POST',
//				params : {
//					serviceId : serviceId,
//					serviceName : serviceName,
//					workItemId : workItemId,
//					coatId : coatId
//				},
//				success : function(response, request) {
//					scripts_store.reload();
//					// execTabsPanel.setHeight(contentPanel.getHeight() * 0.85);
//					// scriptservice_grid.setHeight(contentPanel.getHeight() *
//					// 0.6);
//					// mainPanel.setHeight(contentPanel.getHeight() - 61
//					// + contentPanel.getHeight() * 0.8);
//				},
//				failure : function(result, request) {
//					Ext.Msg.alert('提示', '执行失败！');
//				}
//			});
//		}
//	});
//}