<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<html>
<head>
<script type="text/javascript">
var loginNameForTaskexecDelay = '<%=request.getAttribute("loginName")%>';
<%
	boolean planFormSwitch = Environment.getInstance().getPlantFormSwitch();
	boolean firstClassSwitch = Environment.getInstance().getFirstclassShow();
	boolean secondClassSwitch = Environment.getInstance().getSecondclassShow();
	boolean versionSwitch = Environment.getInstance().getVersionSwitch();
	boolean scriptLevelSwitch = Environment.getInstance().getSScriptLevelSwitch();
	boolean butterflyVersionSwitch = Environment.getInstance().getButterflyVersionSwitch();
	boolean performUserSwitch = Environment.getInstance().getPerformUserSwitch();
	boolean scriptTypeSwitch = Environment.getInstance().getScriptTypeSwitch();
    // 福建农信CI
	boolean fjnxCISwitch = Environment.getInstance().getBankSwitchIsFjnx();
%>
</script>
<link href="<%=request.getContextPath()%>/css/mon_index.css" rel="stylesheet" type="text/css"/>
<link rel="stylesheet" href="css/switchery.min.css" />
<script type="text/javascript" src="js/switchery.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/taskexec/taskexecDelay.js"></script>
<script>
    var secondClassSwitchForTaskexec=<%=secondClassSwitch%>;
	var planFormSwitchForTaskexecDelay=<%=planFormSwitch%>;
	var firstClassSwitchForTaskexecDelay=<%=firstClassSwitch%>;
	var secondClassSwitchForTaskexecDelay=<%=secondClassSwitch%>;
	var versionSwitchForTaskexecDelay=<%=versionSwitch%>;
	var scriptLevelSwitchForTaskexecDelay=<%=scriptLevelSwitch%>;
	var butterflyVersionSwitchForTaskexecDelay=<%=butterflyVersionSwitch%>;
	var performUserSwitchForTaskexecDelay=<%=performUserSwitch%>;
	var scriptTypeSwitchForTaskexecDelay=<%=scriptTypeSwitch%>;
    var scriptTaskApplySsForTaskexecDelay= '<%=request.getParameter("scriptTaskApplySs")==null?"":request.getParameter("scriptTaskApplySs")%>';
    //登录用户名称，为申请人默认查询条件
    var userTrueName = '<%=request.getAttribute("userName")%>';
    var fjnxCISwitch = <%=fjnxCISwitch%>
</script>
</head>
<body>
<div id="scriptexec_taskexecDelay_area" style="width: 100%;height: 100%">
</div>
</body>
</html>