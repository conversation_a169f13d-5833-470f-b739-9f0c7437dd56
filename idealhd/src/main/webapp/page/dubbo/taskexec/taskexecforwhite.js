Ext.onReady(function () {
    Ext.tip.QuickTipManager.init();
    destroyRubbish();
    var scriptsTabs_store;
    var tablessearch_form;
//			contentPanel.setTitle("任务执行");
    var countdown = 3;
    Ext.define('groupNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'GNAME', // 名称
            type: 'string'
        }, {
            name: 'IID', // ID
            type: 'long'
        }]
    });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model: 'groupNameModel',
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'queryComboGroupName.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: !sdFunctionSortSwitch ? true : false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        }, {
            "id": "sh",
            "name": "shell"
        }, {
            "id": "bat",
            "name": "bat"
        }, {
            "id": "py",
            "name": "python"
        }, {
            "id": "perl",
            "name": "perl"
        }, {
            "id": "sql",
            "name": "sql"
        }, {
            "id": "ps1",
            "name": "powershell"
        } /*
					 * , {"id":"-2", "name":"组合"},
					 */
        ]
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name: 'groupName',
        labelWidth: 70,
        width: '19%',
        queryMode: 'local',
        fieldLabel: '功能分类',
        padding: '5',
        hidden: !sdFunctionSortSwitch,
        displayField: 'GNAME',
        valueField: 'IID',
        editable: true,
        emptyText: '--请选功能分类-',
        store: groupNameStore,
        listeners: {
            change: function () { // old is keyup
                bussCb.clearValue();
                bussCb.applyEmptyText();
                bussCb.getPicker().getSelectionModel().doMultiSelect([], false);
                if (this.value != null && this.value != '') {
                    bussData.load({
                        params: {
                            fk: this.value
                        }
                    });
                }
            },
            beforequery: function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bsName',
        labelWidth: 70,
        labelAlign: 'right',
        width: !sdFunctionSortSwitch ? '25.5%' : '19%',
        queryMode: 'local',
        fieldLabel: '一级分类',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function () { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel()
                    .doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    /** 操作类型* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        labelWidth: 70,
        labelAlign: 'right',
        width: !sdFunctionSortSwitch ? '25.5%' : '19%',
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });
    var label = new Ext.form.TextField({
        name: 'label',
        fieldLabel: '标签',
        emptyText: '-请输入标签-',
        //value : labelSwitch,
        hidden: !labelSwitch,
        labelWidth: 70,
        padding: '5',
        //labelAlign: 'right',
        width: '16%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
    /** 脚本类型* */
    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        queryMode: 'local',
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });

    var tsName = new Ext.form.TextField({
        name: 'taskName',
        fieldLabel: '任务名称',
        displayField: 'taskName',
        emptyText: '--请输入任务名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });
    var sName = new Ext.form.TextField({
        name: 'serviceName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '--请输入服务名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '--请输入脚本名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });


    var platformStore = Ext.create('Ext.data.JsonStore', {
        fields: ['INAME', 'ICODEVALUE'],
        //autoDestroy : true,
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getScriptPlatformCode.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    platformStore.on('beforeload', function (store, options) {
        var new_params = {
            gdSwitch: '0'
        };
        Ext.apply(platformStore.proxy.extraParams, new_params);
    });


    /** 适应平台* */
    var platForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'platForm',
        // padding : '5',
        labelWidth: 70,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'INAME',
        valueField: 'ICODEVALUE',
        editable: false,
        emptyText: '--请选择适用平台--',
        store: platformStore,
        width: !sdFunctionSortSwitch ? '25%' : '19%',
        labelAlign: 'right',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == e.ENTER) {
                    scriptservice_grid.ipage.moveFirst();
                }
            }
        }
    });


    tablessearch_form = Ext.create('Ext.form.Panel', {
        layout: 'anchor',
        region: 'north',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [sName, tsName, scName, scriptTypeParam]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            items: [platForm, groupNameCombo, bussCb,
                bussTypeCb, label, {
                    xtype: 'button',
//								id:'queryBtn',
                    text: '查询',
                    cls: 'Common_Btn',
                    handler: function () {
                        /*Ext.getCmp('queryBtn').setDisabled(true);
                          setTimeout(function() {
                              Ext.getCmp('queryBtn').setDisabled(false);
                              }, 3000);*/
                        //		settime(this, '查询');
//									scriptsTabs_store.reload();
                        queryWhere();
//									execTabsPanel.loader.load({
//										url : 'taskexec_tabs.do',
//										params : {
//											serviceName : tablessearch_form.getForm().findField("serviceName").getValue(),
//											bussId : tablessearch_form.getForm().findField("bsName").getValue() || 0,
//											bussTypeId : tablessearch_form.getForm().findField("bussType").getValue() || 0,
//											scriptName : tablessearch_form.getForm().findField("scriptName").getValue()|| '',
//											taskName : tablessearch_form.getForm().findField("taskName").getValue()|| '',
//											scriptType : tablessearch_form.getForm().findField("scriptTypeParam").getValue()|| '',
//											status : 1,
//											onlyScript : 1
//										},
//										scripts : true,
//										autoLoad : true
//									});
                    }
                }, {
                    xtype: 'button',
                    text: '清空',
                    cls: 'Common_Btn',
                    handler: function () {
                        clearQueryWhere();
                    }
                }]
        }]
    });

    Ext.define('scriptService', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        }, {
            name: 'scriptuuid',
            type: 'string'
        }, {
            name: 'coatId',
            type: 'string'
        }, {
            name: 'workItemId',
            type: 'string'
        }, {
            name: 'taskName',
            type: 'string'
        }, {
            name: 'taskType',
            type: 'string'
        }, {
            name: 'serviceName',
            type: 'string'
        }, {
            name: 'sysName',
            type: 'string'
        }, {
            name: 'groupName',
            type: 'string'
        }, {
            name: 'bussName',
            type: 'string'
        }, {
            name: 'scriptType',
            type: 'string'
        }, {
            name: 'scriptName',
            type: 'string'
        }, {
            name: 'servicePara',
            type: 'string'
        }, {
            name: 'platForm',
            type: 'string'
        }, {
            name: 'content',
            type: 'string'
        }, {
            name: 'version',
            type: 'string'
        }, {
            name: 'bussId',
            type: 'int'
        }, {
            name: 'label',
            type: 'string'
        }, {
            name: 'scriptLevel',
            type: 'int'
        }, {
            name: 'bussTypeId',
            type: 'int'
        }, {
            name: 'isFlow',
            type: 'string'
        }, {
            name: 'state',
            type: 'string'
        }, {
            name: 'insRun',
            type: 'string'
        }, {
            name: 'ignore',
            type: 'string'
        }]
    });

    scriptsTabs_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 30,
        model: 'scriptService',
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceAudiListForWhite.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var scriptservice_columns = [
        {
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: 'cId',
            dataIndex: 'coatId',
            hidden: true
        },
        {
            text: 'wId',
            dataIndex: 'workItemId',
            hidden: true
        }, {
            text: 'isFlow',
            dataIndex: 'isFlow',
            hidden: true
        },
        {
            text: '状态',
            dataIndex: 'state',
            width: 100,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == "0") {
                    backValue = '<span class="Not_running State_Color">未运行</span>';
                } else if (value == "30") {
                    backValue = '<span class="Abnormal_yellow State_Color">出现异常</span>';
                } else {
                    backValue = '<span class="Run_Green State_Color">运行</span>';
                }
                return backValue;
            }
        },
        {
            text: '任务名称',
            dataIndex: 'taskName',
            width: 110
        },
        {
            text: '任务类型',
            dataIndex: 'taskType',
            width: 110,
            hidden: true,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == "0") {
                    backValue = '普通任务';
                } else {
                    backValue = '定时任务';
                }
                return backValue;
            }
        },
        {
            text: '服务名称',
            dataIndex: 'serviceName',
            flex: 1
        },
        {
            text: '脚本名称',
            dataIndex: 'scriptName',
            flex: 1
        },
        {
            text: '适用平台',
            dataIndex: 'platForm',
            width: 100
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            width: 100,
            hidden: !sdFunctionSortSwitch
        },
        {
            text: '一级分类',
            dataIndex: 'sysName',
            width: 100
        },
        {
            text: '二级分类',
            dataIndex: 'bussName',
            width: 100
        }, {
            text: '标签',
            dataIndex: 'label',
            width: 100,
            hidden: !labelSwitch,
            renderer: function (value, metadata) {
                metadata.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },
        /*{
            text : '脚本类别',
            dataIndex : 'scriptType',
            width : 80,
            renderer : function(value, p, record) {
                var backValue = "";
                if (value == "sh") {
                    backValue = "shell";
                } else if (value == "perl") {
                    backValue = "perl";
                } else if (value == "py") {
                    backValue = "python";
                } else if (value == "bat") {
                    backValue = "bat";
                } else if (value == "sql") {
                    backValue = "sql";
                }
                if (record.get('isFlow') == '1') {
                    backValue = "组合";
                }
                return backValue;
            }
        },*/
        // { text: '创建者', dataIndex: 'createdUser',width:130},
        {
            text: '版本',
            dataIndex: 'version',
            width: 80
        },
        {
            text: '忽略异常',
            hidden: true,
            dataIndex: 'ignore',
            width: 80
        },
        {
            text: '风险级别',
            dataIndex: 'scriptLevel',
            width: 100,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 1) {
                    backValue = '<font color="#F01024">高级风险</font>';
                } else if (value == 2) {
                    backValue = '<font color="#FF7824">中级风险</font>';
                } else if (value == 3) {
                    backValue = '<font color="#FFA826">低级风险</font>';
                } else if (value == 0) {
                    backValue = '<font color="#FFA826">白名单</font>';
                }
                return backValue;
            }
        },
        {

            text: '操作',
            xtype: 'actiontextcolumn',
            width: 420,
            align: 'left',
//						menuDisabled : true,
            items: [{
                text: '执行',
                iconCls: 'execute',
                getClass: function (v, metadata, record) {
                    var state = record.get("state");
                    var cid = record.get('coatId');
                    var ignoreValue = record.get('ignore');
                    var taskType = record.get('taskType');
                    if ('0' != cid) {
                        return 'x-hidden';
                    } else {
                        if ('0' != taskType) {
                            return 'x-hidden';
                        }
                    }
                },
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
                    var cid = grid.getStore().data.items[rowIndex].data.coatId;
                    var state = grid.getStore().data.items[rowIndex].data.state;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var taskType = grid.getStore().data.items[rowIndex].data.taskType;
                    var ignoreValue = grid.getStore().data.items[rowIndex].data.ignore;
                    var ignore = 0;
                    var isFlowValue = grid.getStore().data.items[rowIndex].data.isFlow;
                    var isFlow = 1;
                    var resGroupFlag = grid.getStore().data.items[rowIndex].data.resGroupFlag;
                    var butterflyversion = grid.getStore().data.items[rowIndex].data.bfVersion;
                    var oddNumbersType = grid.getStore().data.items[rowIndex].data.oddNumbersType;
                    if ('0' != cid) {

                    } else {
                        if ('0' == taskType) {
                            if (noScriptConvertSwitch) {
                                if (isFlowValue == '1') {
                                    isFlow = 1;
                                    forwardScriptTaskExecStart(iid, uuid, serviceName, scriptType, workItemId, cid, isFlow, ignore);
                                } else {
                                    isFlow = 0;
                                    var msg = "";
                                    if (resGroupFlag == 'true') {
                                        //如果是资源组类型先保存参数及agent数据
                                        msg = saveAuditingipsAndParams(workItemId);
                                    }
                                    if (msg == "") {
                                        forwardScriptTaskExecStartForAtom(iid, uuid, serviceName, scriptType, workItemId, cid, isFlow, ignore, butterflyversion, oddNumbersType);
                                    } else {
                                        Ext.Msg.alert('提示', msg);
                                        return;
                                    }
                                }
                            } else {
                                forwardScriptTaskExecStart(iid, uuid, serviceName, scriptType, workItemId, cid, isFlow, ignore);
                            }
                        }
                    }
                }
            }, {
                text: '继续',
                iconCls: 'script_continue',
                getClass: function (v, metadata, record) {
                    var state = record.get("state");
                    var insRun = record.get("insRun");
                    var cid = record.get('coatId');
                    var ignoreValue = record.get('ignore');
                    var isTimeTask = record.get('isTimetask');
                    //判断，如果执行的任务为运行状态，且有agent正在运行，则不允许继续执行
                    //运行状态为两种：1、正在运行；2、分批执行异常处理结束，任务的状态由异常变为运行，这种运行状态允许继续按钮显示

                    //存在正在运行的agent
                    if(insRun > 0){
                        return 'x-hidden';
                    }
                    //不存在正在运行的agent后，
                    if ('0' != cid) {
                        //非运行中的任务隐藏继续按钮
                        if(state != '10' && state != '11'){
                            return 'x-hidden';
                        }
                        //忽略异常分批不展示继续按钮
                        if(ignoreValue == '1'){
                            return 'x-hidden';
                        }
                        //队列执行不显示继续按钮
                        if(isTimeTask == '3'){
                            return 'x-hidden';
                        }
                    } else {
                        return 'x-hidden';
                    }
                },
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var uuid = grid.getStore().data.items[rowIndex].data.scriptuuid;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
                    var cid = grid.getStore().data.items[rowIndex].data.coatId;
                    var state = grid.getStore().data.items[rowIndex].data.state;
                    var scriptType = grid.getStore().data.items[rowIndex].data.scriptType;
                    var ignoreValue = grid.getStore().data.items[rowIndex].data.ignore;
                    var ignore = 0;
                    var isFlowValue = grid.getStore().data.items[rowIndex].data.isFlow;
                    var isFlow = 1;
                    var butterflyversion = grid.getStore().data.items[rowIndex].data.bfVersion;
                    var oddNumbersType = grid.getStore().data.items[rowIndex].data.oddNumbersType;
                    if ('0' != cid) {
                        if ('11' == state && (ignoreValue == '0' || ignoreValue == '')) {
                            if (noScriptConvertSwitch) {
                                if (isFlowValue == '1') {
                                    isFlow = 1;
                                    forwardScriptTaskExecStart(iid, uuid, serviceName, scriptType, workItemId, cid, isFlow, ignore);
                                } else {
                                    isFlow = 0;
                                    forwardScriptTaskExecStartForAtom(iid, uuid, serviceName, scriptType, workItemId, cid, isFlow, ignore, butterflyversion, oddNumbersType);
                                }
                            } else {
                                forwardScriptTaskExecStart(iid, uuid, serviceName, scriptType, workItemId, cid, isFlow, ignore);
                            }
                        }
                    }
                }
            }, {
                text: '终止',
                iconCls: 'script_termina',
                handler: function (grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.iid;
                    var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
                    var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
                    var cid = grid.getStore().data.items[rowIndex].data.coatId;
                    forwardScriptTaskExecStop(iid, serviceName, workItemId, cid);
                }
            }, {
                text: '任务详情',
                iconCls: 'script_termina',
                getClass: function (v, metadata, record) {
                    var cid = record.get('coatId');
                    var taskType = record.get('taskType');
                    if ('0' == cid) {
                        if ('0' != taskType) {
                            return 'x-hidden';
                        }
                    }
                },
                handler: function (grid, rowIndex) {
                    var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
                    forwordAudiDetail(workItemId);
                }
            }/*,{
									text : '执行历史',
									iconCls : 'execute',
									getClass : function(v, metadata, record) {
						 				    var cid = record.get('coatId');
											if ('0' == cid) {
											    	return 'x-hidden';
											}
									},
									handler : function(grid, rowIndex) {
										var cid = grid.getStore().data.items[rowIndex].data.coatId;
										var flowId = grid.getStore().data.items[rowIndex].data.flowId;
										if('0' != cid){
											flowHistoryFun(flowId);
										}
									}
								}*/]
        }

        //****************************************
//					{
//						text : '操作',
//						dataIndex : 'xq',
//						width : 320,
//						align : 'left',
//						renderer : function(value, p, record) {
//							var iid = record.get('iid');
//							var uuid = record.get('scriptuuid');
//							var serviceName = record.get('serviceName');
//							var workItemId = record.get('workItemId');
//							var execFuncName = "forwardScriptTaskExecStart";
//							var execFuncNameStop = "forwardScriptTaskExecStop";
//							var cid = record.get('coatId');
//							var state = record.get('state');
//							var scriptType = record.get('scriptType');
//							var taskType = record.get('taskType');
//							var ignoreValue = record.get('ignore');
//							var isFlow = 1;
//							if(noScriptConvertSwitch){
//								if (record.get('isFlow') == '1') {
//									isFlow = 1;
//								} else {
//									isFlow = 0;
//									execFuncName = "forwardScriptTaskExecStartForAtom";
//								}
//							}
//							var ignore = 0;
//							var    forwordAudi =	 '<span class="switch_span"><a href="javascript:void(0)" onclick="forwordAudiDetail('+ workItemId+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;任务详情</a></span>'
//							if ('0' != cid) {
//								if ('11' == state && (ignoreValue=='0' ||ignoreValue=='')) {
//									return '<span class="switch_span"><a href="javascript:void(0)" style="color: #b6b6b6;"><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;执行</a></span>&nbsp;&nbsp;&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="'
//									+ execFuncName+ '('+ iid+ ',\''+ uuid+ '\',\''+ serviceName+ '\',\''+ scriptType+ '\','+ workItemId+ ','+ cid+ ','+ isFlow+ ','+ ignore+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_continue"></img>&nbsp;继续</a></span>&nbsp;&nbsp;&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="'
//									+ execFuncNameStop+ '('+ iid+ ',\''+ serviceName+ '\','+ workItemId	+ ','	+ cid	+ ','+ isFlow	+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;终止</a></span>' + '&nbsp;&nbsp;&nbsp;&nbsp;'+forwordAudi;
//								}else{
//									return '<span class="switch_span"><a href="javascript:void(0)" onclick="'
//									+ execFuncNameStop + '(' + iid + ',\'' + serviceName + '\',' + workItemId + ',' + cid + ',' + isFlow
//									+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;终止</a></span>' + '&nbsp;&nbsp;&nbsp;&nbsp;'+forwordAudi;
//								}
//							} else {
//								if('0'==taskType){
//									return '<span class="switch_span"><a href="javascript:void(0)" onclick="'
//									+ execFuncName+ '('+ iid+ ',\''+ uuid+ '\',\''+ serviceName+ '\',\''+ scriptType + '\',' + workItemId	+ ','	+ cid + ','	+ isFlow + ','+ ignore+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="execute"></img>&nbsp;执行</a></span>&nbsp;&nbsp;&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="'
//									+ execFuncNameStop + '(' + iid + ',\'' + serviceName + '\',' + workItemId 	+ ',' 	+ cid 	+ ',' + isFlow
//									+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;终止</a></span>' + '&nbsp;&nbsp;&nbsp;&nbsp;'+forwordAudi;
//								}else{
//									return '<span class="switch_span"><a href="javascript:void(0)" onclick="" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_time"></img>&nbsp;定时</a></span>&nbsp;&nbsp;&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="'
//									+ execFuncNameStop + '(' 	+ iid + ',\'' 	+ serviceName 	+ '\',' 	+ workItemId 	+ ',' 	+ cid 	+ ',' + isFlow
//									+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;终止</a></span>';
//								}
//
//							}
//						}
//					}
    ];
    //我的未结跳转过来的任务名称 进行赋值
    tsName.setValue(taskNameForWhiteScriptExec);
    scriptsTabs_store.on('beforeload', function (store, options) {
        var new_params = {
            serviceName: tablessearch_form.getForm()
                .findField("serviceName").getValue().trim(),
            bussId: tablessearch_form.getForm().findField("bsName").getValue() || 0,
            bussTypeId: tablessearch_form.getForm().findField("bussType")
                .getValue() || 0,
            scriptName: tablessearch_form.getForm().findField("scriptName")
                    .getValue()
                || '',
            taskName: tablessearch_form.getForm().findField("taskName")
                    .getValue().trim()
                || '',
            scriptType: tablessearch_form.getForm().findField(
                "scriptTypeParam").getValue()
                || '',
            platForm: platForm.getValue(),
            status: 1,
            onlyScript: 0,
            label: label.getValue(),
            groupName: groupNameCombo.getValue()
        };

        Ext.apply(scriptsTabs_store.proxy.extraParams, new_params);
    });

//			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//			  	store: scriptsTabs_store,
//			    dock: 'bottom',
//			    displayInfo: true,
//			    emptyMsg:'找不到任何记录'
//		    });

    var scriptservice_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        height: contentPanel.getHeight() - 40,
        width: contentPanel.getWidth(),
        ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        store: scriptsTabs_store,
        border: false,
        padding: grid_space,
        columnLines: true,
        // selModel:selModel,
        columns: scriptservice_columns
        //bbar : pageBar //[ '->', pageBar ]
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptexec_taskexec_areaforwhite",
        layout: 'border',
        border: false,
        height: contentPanel.getHeight() - modelHeigth,
        width: contentPanel.getWidth(),
        items: [tablessearch_form, scriptservice_grid]
    });

    function clearQueryWhere() {
        tablessearch_form.getForm().findField("bsName").setValue('');
        tablessearch_form.getForm().findField("bussType").setValue('');
        tablessearch_form.getForm().findField("scriptName").setValue('');
        tablessearch_form.getForm().findField("taskName").setValue('');
        tablessearch_form.getForm().findField("serviceName").setValue('');
        tablessearch_form.getForm().findField("scriptTypeParam").setValue('');
        platForm.setValue('');
        label.setValue('');
        bussTypeData.removeAll();
        groupNameCombo.setValue('');
        if (sdFunctionSortSwitch) {
            bussData.removeAll();
        }
    }

    function queryWhere() {
        scriptservice_grid.ipage.moveFirst();
        scriptsTabs_store.load({
            params: {
                start: 0,
                limit: 30
            }
        });
    }

    contentPanel.on('resize', function () {
        mainPanel.setWidth(contentPanel.getWidth());
    });

    function settime(btn, text) {
        if (countdown == 0) {
            btn.setDisabled(false);
            btn.setText(text);
            countdown = 3;
        } else {
            btn.setDisabled(true);
            btn.setText(text + "(" + countdown + ")");
            countdown--;
            setTimeout(function () {
                settime(btn, text)
            }, 1000)
        }

    }

    function refreshPageTaskTop() {
        if (contentPanel.getLoader().url == 'scriptTaskexec.do') {
            scriptsTabs_store.reload();
        }
    }

    if (refreshObj) {
        clearInterval(refreshObj);
    }
    refreshObj = setInterval(refreshPageTaskTop, interExecPV * 1000);


    function forwordAudiDetail(workItemId) {
        var auditingWin = Ext.create('widget.window', {
            title: '任务详情',
            closable: true,
            closeAction: 'destroy',
            width: contentPanel.getWidth(),
            minWidth: 350,
            height: contentPanel.getHeight(),
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'initReviewForSsPublishExec.do',
                params: {
                    iworkItemid: workItemId,
                    from: '100',
                    submitType: 'tc',
                    istate: 1,
                    btnCode: 25
                },
                autoLoad: true,
                scripts: true
            }
        });
        auditingWin.show();
    }

    function saveAuditingipsAndParams(workItemId) {
        var msg = "";
        Ext.Ajax.request({
            url: 'saveAuditingipsAndParams.do',
            method: 'POST',
            async: false,
            params: {
                workItemId: workItemId
            },
            success: function (response, request) {
                if (Ext.decode(response.responseText).success != "true") {
                    msg = Ext.decode(response.responseText).msg;
                }
            },
            failure: function (result, request) {
                Ext.Msg.alert('提示', '获取资源组数据失败！');
            }
        });
        return msg;
    }

    function forwardScriptTaskExecStartForAtom(serviceId, uuid, serviceName, scriptType, workItemId, coatId, isFlow, ignore) {
        var editingChosedAgentIds = new Array();
        Ext.define('agentModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            },
                {
                    name: 'sysName',
                    type: 'string'
                },
                {
                    name: 'appName',
                    type: 'string'
                },
                {
                    name: 'hostName',
                    type: 'string'
                },
                {
                    name: 'osType',
                    type: 'string'
                },
                {
                    name: 'agentIp',
                    type: 'string'
                },
                {
                    name: 'agentPort',
                    type: 'string',
                    defaultValue: 1500
                },
                {
                    name: 'agentDesc',
                    type: 'string'
                },
                {
                    name: 'resGroup',
                    type: 'string'
                },
                {
                    name: 'agentState',
                    type: 'int'
                },
                {
                    name: 'agentParam',
                    type: 'string'
                },
                {
                    name: 'agentStartUser',
                    type: 'string'
                }]
        });
        var agent_store = Ext.create('Ext.data.Store', {
            autoLoad: true,
            pageSize: 50,
            model: 'agentModel',
            proxy: {
                type: 'ajax',
                url: 'getAgentListForAtom.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        agent_store.on('beforeload', function (store, options) {
            var new_params = {
                agentIp: Ext.util.Format.trim(agent_ip.getValue()),
                agentName: Ext.util.Format.trim(os_name.getValue()),
                hostName: Ext.util.Format.trim(host_name.getValue()),
                iosName: Ext.util.Format.trim(os_type.getValue())
            };
            Ext.apply(agent_store.proxy.extraParams, new_params);
        });
//	 var pageBarForAtom = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//	        store: agent_store,
//	        dock: 'bottom',
//	        displayInfo: true
//	    });
        agent_store.on('beforeload', function (store, options) {
            var new_params = {
                workItemId: workItemId
            };

            Ext.apply(agent_store.proxy.extraParams, new_params);
        });
        agent_store.addListener('load',
            function (me, records, successful, eOpts) {
                if (editingChosedAgentIds) {
                    var chosedRecords = []; //存放选中记录
                    $.each(records,
                        function (index, record) {
                            if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                                chosedRecords.push(record);
                            }
                        });
                    chosedServList.getSelectionModel().select(chosedRecords, false, true); //选中记录
                }
            });
        var selModel = Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        });
        var host_name = new Ext.form.TextField({
            name: 'hostname',
            fieldLabel: '计算机名',
            displayField: 'hostname',
            emptyText: '--请输入计算机名--',
            labelWidth: 70,
            labelAlign: 'right',
            width: '25%',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        chosedServList.ipage.moveFirst();
                    }
                }
            }
        });
        var agent_ip = new Ext.form.TextField({
            name: 'agentip',
            fieldLabel: 'AgentIp',
            displayField: 'agentip',
            emptyText: '--请输入Agent IP--',
            labelWidth: 70,
            labelAlign: 'right',
            width: '25%',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        chosedServList.ipage.moveFirst();
                    }
                }
            }
        });
        var os_type = new Ext.form.TextField({
            name: 'ostype',
            fieldLabel: '操作系统',
            displayField: 'ostype',
            emptyText: '--请输入操作系统--',
            labelWidth: 70,
            labelAlign: 'right',
            width: '25%',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        chosedServList.ipage.moveFirst();
                    }
                }
            }
        });
        var os_name = new Ext.form.TextField({
            name: 'ostype',
            fieldLabel: '名称',
            displayField: 'osname',
            emptyText: '--请输入名称--',
            labelWidth: 70,
            labelAlign: 'right',
            width: '25%',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == e.ENTER) {
                        chosedServList.ipage.moveFirst();
                    }
                }
            }
        });
        var check = 0;
        var selfRadio = Ext.create('Ext.form.field.Radio', {
            width: 100,
            name: 'partRadio',
            labelAlign: 'left',
            fieldLabel: '',
            boxLabel: '按选择执行',
            inputValue: 0,
            checked: true
        });
        var partRadio = Ext.create('Ext.form.field.Radio', {
            width: 100,
            name: 'partRadio',
            labelAlign: 'left',
            fieldLabel: '',
            boxLabel: '自动分批',
            inputValue: 1
        });
        var autopartRadio = Ext.create('Ext.form.field.Radio', {
            width: 100,
            name: 'partRadio',
            labelAlign: 'left',
            fieldLabel: '',
            boxLabel: '忽略异常分批',
            inputValue: 2
        });
        var autopartComment = Ext.create('Ext.form.RadioGroup', {
            name: 'partRadioComment',
            labelAlign: 'left',
            layout: 'column',
            width: '160',
            items: [selfRadio, partRadio, autopartRadio],
            listeners: {
                //通过change触发
                change: function (g, newValue, oldValue) {
                    if (newValue.partRadio == 0)//自己选择
                    {
                        ignore = 0;
                        check = 0;
                        chosedServList.show();
                    } else if (newValue.partRadio == 1)//自动分批
                    {
                        ignore = 0;
                        check = 1;
                        chosedServList.hide();
                    } else if (newValue.partRadio == 2)//忽略异常分批
                    {
                        ignore = 1;
                        check = 1;
                        chosedServList.hide();
                    }
                }
            }
        });
        var search_form = Ext.create('Ext.form.Panel', {
            region: 'north',
            layout: 'anchor',
            buttonAlign: 'center',
            border: false,
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'top',
                items: [os_name, host_name, os_type, agent_ip]
            }, {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: [autopartComment, '->', {
                    xtype: 'button',
                    cls: 'Common_Btn',
                    text: '查询',
                    handler: function () {
                        chosedServList.ipage.moveFirst();
                    }
                },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '清空',
                        handler: function () {
                            agent_ip.setValue('');
                            host_name.setValue('');
                            os_type.setValue('');
                            os_name.setValue('');
                        }
                    }]
            }]
        });
        var chosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            autoScroll: true,
            multiSelect: true,
            split: true,
            columnLines: true,
            store: agent_store,
            selModel: selModel,
//	        bbar:pageBarForAtom,
            ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
            listeners: {
                select: function (t, record, index, eOpts) {
                    if (editingChosedAgentIds.indexOf(record.get('iid')) == -1) {
                        editingChosedAgentIds.push(record.get('iid'));
                    }
                },
                deselect: function (t, record, index, eOpts) {
                    if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                        editingChosedAgentIds.remove(record.get('iid'));
                    }
                }
            },
            columns: [{text: '序号', xtype: 'rownumberer', width: 40},
                {
                    text: '主键',
                    dataIndex: 'iid',
                    hidden: true
                },
                {
                    text: '名称',
                    dataIndex: 'sysName',
                    flex: 1
                },
                {
                    text: '计算机名',
                    dataIndex: 'hostName',
                    flex: 1
                },
                {
                    text: 'IP',
                    dataIndex: 'agentIp',
                    width: 150
                },
                {
                    text: '端口号',
                    dataIndex: 'agentPort',
                    width: 100
                },
                {
                    text: '操作系统',
                    dataIndex: 'osType',
                    width: 140
                },
                {
                    text: '描述',
                    dataIndex: 'agentDesc',
                    flex: 1,
                    hidden: true
                },
                {
                    text: '状态',
                    dataIndex: 'agentState',
                    width: 130,
                    renderer: function (value, p, record) {
                        var backValue = "";
                        if (value == 0) {
                            backValue = "Agent正常";
                        } else if (value == 1) {
                            backValue = "Agent异常";
                        }
                        return backValue;
                    }
                }]
        });

        var chosedAgentWinForSeeForAtom = Ext.create('Ext.window.Window', {
            title: '选择执行服务器',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            width: contentPanel.getWidth() - 190,
            height: contentPanel.getHeight(),
            layout: 'border',
            items: [search_form, chosedServList],
            buttonAlign: 'center',
            dockedItems: [{
                xtype: 'toolbar',
                dock: 'bottom',
                layout: {pack: 'center'},
                items: [{
                    xtype: "button",
                    text: "确定",
                    id: 'SureBtn',
                    cls: 'Common_Btn',
                    margin: '6',
                    handler: function () {
                        var isRunning = false;
                        if (coatId > 0) {
                            Ext.Ajax.request({
                                url: 'queryHasRunning.do',
                                method: 'POST',
                                async: false,
                                params: {
                                    workItemId: workItemId
                                },
                                success: function (response, options) {
                                    isRunning = Ext.decode(response.responseText).isRunning;
                                },
                                failure: function (result, request) {
                                }
                            });
                            if (isRunning) {
                                Ext.Msg.alert('消息提示', '有实例正在运行，不能继续！');
                                return;
                            }
                        }
                        if (check == 1) {
                            forwardScriptTaskExecStart(serviceId, uuid, serviceName, scriptType, workItemId, coatId, isFlow, ignore);
                            chosedAgentWinForSeeForAtom.close();
                        } else {
                            Ext.Msg.confirm('系统提示', '您确定要进行执行操作吗?', function (btn) {
                                if (btn == 'yes') {
                                    destroyRubbish(); // 销毁本页垃圾
                                    var selDatas = editingChosedAgentIds.slice(0);
                                    if (selDatas.length == 0) {
                                        Ext.Msg.alert('消息提示', '请选择记录进行操作！');
                                        return;
                                    }
                                    var eachNum;
                                    Ext.Ajax.request({
                                        url: 'queryEachNum.do',
                                        method: 'POST',
                                        async: false,
                                        params: {
                                            workItemId: workItemId
                                        },
                                        success: function (response, options) {
                                            eachNum = Ext.decode(response.responseText).eachNum;
                                        },
                                        failure: function (result, request) {
                                        }
                                    });
                                    if (selDatas.length > eachNum) {
                                        Ext.Msg.alert('提示', '选择的记录条数不能超过并发数量值：' + eachNum);
                                        return;
                                    }
                                    Ext.Ajax.request({
                                        url: 'execScriptServiceStartForAtom.do',
                                        method: 'POST',
                                        params: {
                                            serviceId: serviceId,
                                            uuid: uuid,
                                            serviceName: serviceName,
                                            scriptType: scriptType,
                                            workItemId: workItemId,
                                            coatId: coatId,
                                            iids: editingChosedAgentIds
                                        },
                                        success: function (response, request) {
                                            var success = Ext.decode(response.responseText).success;
                                            var message = Ext.decode(response.responseText).message;
                                            chosedAgentWinForSeeForAtom.close();
                                            if (success) {
                                                var flowId = Ext.decode(response.responseText).content;
                                                var coatIds = Ext.decode(response.responseText).coatIds;
                                                scriptsTabs_store.reload({
                                                    callback: function (records, operation, success) {
                                                        //任务执行 跳转开关，浦发需求 开启后 直接跳转到最后一层展示，不展示图形
                                                        if (!taskExecShowSwitchForWhiteScript || isFlow == 1) {
                                                            popNewTab('图形监控', 'monitorSingleFlowPageScriptService.do', {
                                                                flowId: flowId,
                                                                serviceId: serviceId,
                                                                flagType: 1,
                                                                isFromExec: 1,
                                                                contentPanelHeight: contentPanel.getHeight(),
                                                                windowScHeight: window.screen.height,
                                                                url: whichDoForWhiteScriptExec,
                                                                divID: ''
                                                            }, 10, true);
                                                        } else {
                                                            var winStep = Ext.create('Ext.window.Window', {
                                                                title: '执行结果',
                                                                modal: true,
                                                                closeAction: 'destroy',
                                                                constrain: true,
                                                                autoScroll: true,
                                                                width: contentPanel.getWidth(),
                                                                height: contentPanel.getHeight(),
                                                                minWidth: 350,
                                                                draggable: true,// 禁止拖动
                                                                resizable: false,// 禁止缩放
                                                                layout: 'fit',
                                                                loader: {
                                                                    url: 'forwardscriptserverForMonitorSingle.do',
                                                                    params: {
                                                                        flowId: flowId,
                                                                        coatid: coatIds, //
                                                                        flag: '',//
                                                                        flagTypeForMonitorSingle: '1',
                                                                        isWin: 1
                                                                    },
                                                                    autoLoad: true,
                                                                    scripts: true
                                                                }
                                                            });
                                                            winStep.show();
                                                        }
                                                    }
                                                });
                                            }
                                        },
                                        failure: function (result, request) {
                                            Ext.Msg.alert('提示', '执行失败！');
                                        }
                                    });
                                }
                            });
                        }
                        //			this.up("window").close();
                    }
                },
                    {
                        xtype: "button",
                        text: "关闭",
                        cls: 'Common_Btn',
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
            }]
        }).show();
        //}
    }

    function forwardScriptTaskExecStart(serviceId, uuid, serviceName, scriptType, workItemId, coatId, isFlow, ignore) {
        Ext.Msg.confirm('系统提示', '您确定要进行执行操作吗?', function (btn) {
            if (btn == 'yes') {
                destroyRubbish(); // 销毁本页垃圾
                Ext.Ajax.request({
                    url: 'execScriptServiceStart.do',
                    method: 'POST',
                    params: {
                        serviceId: serviceId,
                        uuid: uuid,
                        serviceName: serviceName,
                        scriptType: scriptType,
                        workItemId: workItemId,
                        coatId: coatId,
                        isFlow: isFlow,
                        ignore: ignore
                    },
                    success: function (response, request) {
                        var success = Ext.decode(response.responseText).success;
                        if (success) {
                            var flowId = Ext.decode(response.responseText).content;
                            var coatIds = Ext.decode(response.responseText).coatIds;
                            scriptsTabs_store.reload({
                                callback: function (records, operation, success) {
                                    //任务执行 跳转开关，浦发需求 开启后 直接跳转到最后一层展示，不展示图形
                                    if (!taskExecShowSwitchForWhiteScript || isFlow == 1) {
                                        popNewTab('图形监控', 'monitorSingleFlowPageScriptService.do', {
                                            flowId: flowId,
                                            serviceId: serviceId,
                                            flagType: 1,
                                            isFromExec: 1,
                                            contentPanelHeight: contentPanel.getHeight(),
                                            windowScHeight: window.screen.height,
                                            url: whichDoForWhiteScriptExec,
                                            divID: ''
                                        }, 10, true);
                                    } else {
                                        var winStep = Ext.create('Ext.window.Window', {
                                            title: '执行结果',
                                            modal: true,
                                            closeAction: 'destroy',
                                            constrain: true,
                                            autoScroll: true,
                                            width: contentPanel.getWidth(),
                                            height: contentPanel.getHeight(),
                                            minWidth: 350,
                                            draggable: true,// 禁止拖动
                                            resizable: false,// 禁止缩放
                                            layout: 'fit',
                                            loader: {
                                                url: 'forwardscriptserverForMonitorSingle.do',
                                                params: {
                                                    flowId: flowId,
                                                    coatid: coatIds, //
                                                    flag: '',//
                                                    flagTypeForMonitorSingle: '1',
                                                    isWin: 1
                                                },
                                                autoLoad: true,
                                                scripts: true
                                            }
                                        });
                                        winStep.show();
                                    }
                                }
                            });
                        }
                    },
                    failure: function (result, request) {
                        Ext.Msg.alert('提示', '执行失败！');
                    }
                });
            }
        });
    }

    /*function flowHistoryFun(flowId){
                var serviceName='';
                var state =-2;
                var startTime ='';
                var endTime ='';
                var taskName = '';
                var filter_audiUser = '';
                contentPanel.getLoader().load({
                url: "forwardscriptflowcoatforexec.do",
                scripts: true,
                params : {
                        flowId:flowId,
                        flag:1,
                        forScriptFlow: 1,
                        filter_serviceName:serviceName,
                        filter_serviceState:state,
                        filter_serviceStartTime:startTime,
                        filter_serviceEndTime:endTime,
                        filter_serviceTaskName : taskName,
                        filter_audiUser : filter_audiUser,
                        fromMenu:'taskexec'
                        }
                    });
            }*/
    function forwardScriptTaskExecStop(serviceId, serviceName, workItemId, coatId) {
        Ext.Msg.confirm('系统提示', '您确定要进行终止操作吗?', function (btn) {
            if (btn == 'yes') {
                destroyRubbish(); // 销毁本页垃圾
                Ext.Ajax.request({
                    url: 'execScriptServiceStop.do',
                    method: 'POST',
                    params: {
                        serviceId: serviceId,
                        serviceName: serviceName,
                        workItemId: workItemId,
                        coatId: coatId
                    },
                    success: function (response, request) {
                        scriptsTabs_store.reload();
                    },
                    failure: function (result, request) {
                        Ext.Msg.alert('提示', '执行失败！');
                    }
                });
            }
        });
    }
});





