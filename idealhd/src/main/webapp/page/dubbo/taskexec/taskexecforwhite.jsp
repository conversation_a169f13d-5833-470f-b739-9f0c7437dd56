<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	//任务执行 跳转开关，浦发需求 开启后 直接跳转到最后一层展示，不展示图形 
	boolean taskExecShowSwitchForWhiteScript = Environment.getInstance().getTaskExecShowSwitch();
	//标签
	boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
	//功能分类
	boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
%>
<html>
<head>
<script>
    var tbsexec_count = '<%=request.getAttribute("count")%>';
    var whichDoForWhiteScriptExec = '<%=request.getAttribute("whichDo")%>';
    var taskExecShowSwitchForWhiteScript = <%=taskExecShowSwitchForWhiteScript%>;
    var taskNameForWhiteScriptExec = '<%=request.getParameter("taskName")==null?"":request.getParameter("taskName")%>';
	var labelSwitch = <%=sdScriptLabelEditSwitch%>;
	var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/taskexec/taskexecforwhite.js"></script>
<style type="text/css">
	.script_exec_gray{
		color: #b6b6b6 !important;
	}
</style>
</head>
<body>
<div id="scriptexec_taskexec_areaforwhite" style="width: 100%;height: 100%">
</div>
</body>
</html>