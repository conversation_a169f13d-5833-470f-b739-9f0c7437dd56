//var scripts_store;
Ext.onReady(function() {
			Ext.tip.QuickTipManager.init();
			destroyRubbish();
			
			function trim(str) {
				if(str!=null){
					str = str.toString().replace(/^(\s|\u00A0)+/, '');
					for (var i = str.length - 1; i >= 0; i--) {
						if (/\S/.test(str.charAt(i))) {
							str = str.substring(0, i + 1);
							break;
						}
					}
				}
				return str;
			}
			String.prototype.trim = function ()
			{
				return this.replace (/(^\s*)|(\s*$)/g, "");
			}
			var jmz = {};
			jmz.GetLength = function(str) {
			  var realLength = 0, len = str.length, charCode = -1;
			  for (var i = 0; i < len; i++) {
			    charCode = str.charCodeAt(i);
			    if (charCode >= 0 && charCode <= 128) realLength += 1;
			    else realLength += 2;
			  }
			  return realLength;
			};
			var bussData = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'bsName' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsAll.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var bussTypeData = Ext.create('Ext.data.Store', {
				fields : [ 'sysTypeId', 'sysType' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'bsManager/getBsTypeByFk.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var cataStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
					"id" : "-1",
					"name" : "全部"
				}, {
					"id" : "sh",
					"name" : "shell"
				}, {
					"id" : "bat",
					"name" : "bat"
				}, {
					"id" : "py",
					"name" : "python"
				}, {
					"id" : "perl",
					"name" : "perl"
				},{
					"id" : "sql",
					"name" : "sql"
				},{
					"id" : "ps1",
					"name" : "powershell"
				} /*
					 * , {"id":"-2", "name":"组合"},
					 */
				]
			});
			var bussCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'bsName',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25.5%',
				queryMode : 'local',
				fieldLabel : '一级分类',
				padding : '5',
				displayField : 'bsName',
				valueField : 'iid',
				editable : false,
				queryMode : 'local',
				emptyText : '--请选择一级分类--',
				store : bussData,
				listeners : {
					change : function() { // old is keyup
						bussTypeCb.clearValue();
						bussTypeCb.applyEmptyText();
						bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
						bussTypeData.load({
							params : {
								fk : this.value
							}
						});
					},
					 specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			            }
				}
			});

			/** 操作类型* */
			var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
				name : 'bussType',
				padding : '5',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25.5%',
				queryMode : 'local',
				fieldLabel : '二级分类',
				displayField : 'sysType',
				valueField : 'sysTypeId',
				editable : false,
				emptyText : '--请选择二级分类--',
				store : bussTypeData,
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var tsName = new Ext.form.TextField({
				name : 'taskName',
				fieldLabel : '任务名称',
				displayField : 'taskName',
				emptyText : '--请输入任务名称--',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			/** 脚本类型* */
			var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
				name : 'scriptTypeParam',
				padding : '5',
				labelWidth : 70,
				labelAlign : 'right',
				width : '25%',
				queryMode : 'local',
				fieldLabel : '脚本类型',
				displayField : 'name',
				valueField : 'id',
				editable : false,
				emptyText : '--请选择脚本类型--',
				store : cataStore,
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});

			var sName = new Ext.form.TextField({
				name : 'serviceName',
				fieldLabel : '服务名称',
				displayField : 'serverName',
				emptyText : '--请输入服务名称--',
				labelWidth : 70,
				padding : '5',
				labelAlign : 'right',
				width : '24%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			var scName = new Ext.form.TextField({
				name : 'scriptName',
				fieldLabel : '脚本名称',
				displayField : 'scriptName',
				emptyText : '--请输入脚本名称--',
				labelWidth : 70,
				padding : '5',
				labelAlign : 'right',
				width : '25%',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		            }
		        }
			});
			
			 var platformStore = Ext.create('Ext.data.JsonStore', {
			fields: ['INAME', 'ICODEVALUE'],
			//autoDestroy : true,
			autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getScriptPlatformCode.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
	    
		platformStore.on('beforeload', function(store, options) {
				var new_params = {
					gdSwitch :  '0'
				};
				Ext.apply(platformStore.proxy.extraParams, new_params);
		});
			var platForm = Ext.create('Ext.form.field.ComboBox', {
					name : 'platForm',
					// padding : '5',
					labelWidth : 70,
					queryMode : 'local',
					fieldLabel : '适用平台',
					displayField: 'INAME',
			        valueField: 'ICODEVALUE',
					editable : false,
					emptyText : '--请选择适用平台--',
					store : platformStore,
					width : '25%',
					labelAlign : 'right',
					listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			            }
			        }
				});

			/** 保存按钮* */
			var saveBtn = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    text : "保存",
			    handler : updateScriptTimeTask
//			    handler : function(){
//			    	alert('保存。。。');
//			    }
			});
			var search_form = Ext.create('Ext.form.Panel', {
				layout : 'anchor',
				region : 'north',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [sName, scName,tsName, scriptTypeParam]
				},  {
					xtype : 'toolbar',
					dock : 'top',
					items :	[  platForm, bussCb,
							bussTypeCb,saveBtn,{
								xtype : 'button',
								id:'queryBtn2',
								text : '查询',
								cls : 'Common_Btn',
								handler : function(){
//									Ext.getCmp('queryBtn2').setDisabled(true);
//									  setTimeout(function() {
//										  Ext.getCmp('queryBtn2').setDisabled(false);
//										  }, 3000);
									scripts_store.reload();
								}
							}, {
								xtype : 'button',
								text : '清空',
								cls : 'Common_Btn',
								handler : function() {
									clearQueryWhere();
								}
							} ]
				} ]
			});

			Ext.define('scriptService', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'coatId',
					type : 'string'
				},{
					name : 'planId',
					type : 'string'
				}, {
					name : 'workItemId',
					type : 'string'
				}, {
					name : 'taskName',
					type : 'string'
				}, {
					name : 'serviceName',
					type : 'string'
				}, {
					name : 'sysName',
					type : 'string'
				}, {
					name : 'bussName',
					type : 'string'
				}, {
					name : 'scriptType',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'servicePara',
					type : 'string'
				}, {
					name : 'platForm',
					type : 'string'
				}, {
					name : 'content',
					type : 'string'
				}, {
					name : 'version',
					type : 'string'
				}, {
					name : 'taskTime',
					type : 'string'
				}, {
					name : 'nextStartTime',
					type : 'string'
				}, {
					name : 'bussId',
					type : 'int'
				}, {
					name : 'scriptLevel',
					type : 'int'
				}, {
					name : 'bussTypeId',
					type : 'int'
				}, {
					name : 'isFlow',
					type : 'string'
				}, {
					name : 'state',
					type : 'string'
				}, {
					name : 'performUser',
					type : 'string'
				}]
			});

			var scripts_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				pageSize : 50,
				model : 'scriptService',
				proxy : {
					type : 'ajax',
					url : 'getScriptServiceAudiListDelayForWhite.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			scripts_store.on('beforeload', function(store, options) {
				var new_params = {
					serviceName : search_form.getForm().findField("serviceName").getValue().trim(),
					bussId : search_form.getForm().findField("bsName").getValue() || 0,
					bussTypeId : search_form.getForm().findField("bussType").getValue() || 0,
					scriptName : search_form.getForm().findField("scriptName").getValue().trim() || '',
					scriptType : search_form.getForm().findField("scriptTypeParam").getValue() || '',
					platForm:platForm.getValue(),
					taskName : search_form.getForm().findField("taskName").getValue().trim() || '',
					status : 1,
					onlyScript : 1
				};

				Ext.apply(scripts_store.proxy.extraParams, new_params);
			});
			
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			var scriptservice_columns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '主键',
						dataIndex : 'iid',
						hidden : true
					},
					{
						text : 'cId',
						dataIndex : 'coatId',
						hidden : true
					},
					{
						text : 'planId',
						dataIndex : 'planId',
						hidden : true
					},
					{
						text : 'wId',
						dataIndex : 'workItemId',
						hidden : true
					},
					{
						text : '状态',
						dataIndex : 'state',
						hidden : true,
						width : 100,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == "10") {
								backValue = '<span class="Not_running State_Color">未运行</span>';
							}else if (value == '12') {
								backValue = '<span class="Run_Green State_Color">Delay运行</span>';
							}else if (value == "30"){
								backValue = '<span class="Abnormal_yellow State_Color">出现异常</span>';
							} else {
								backValue = '<span class="Run_Green State_Color">NO_'+value+'</span>';
							}
							return backValue;
						}
					},
					{
						text : '任务名称',
						dataIndex : 'taskName',
						width : 110,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '服务名称',
						dataIndex : 'serviceName',
						flex : 1,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '脚本名称',
						dataIndex : 'scriptName',
						flex : 1,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '适用平台',
						dataIndex : 'platForm',
						width : 100
					},
					{
						text : '一级分类',
						dataIndex : 'sysName',
						width : 100
					},
					{
						text : '二级分类',
						dataIndex : 'bussName',
						width : 100
					},
					{
						text : '脚本类别',
						dataIndex : 'scriptType',
						width : 80,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == "sh") {
								backValue = "shell";
							} else if (value == "perl") {
								backValue = "perl";
							} else if (value == "py") {
								backValue = "python";
							} else if (value == "bat") {
								backValue = "bat";
							} else if (value == "sql") {
								backValue = "sql";
							} else if (value == "ps1") {
								backValue = "powershell";
							}
							if (record.get('isFlow') == '1') {
								backValue = "组合";
							}
							return backValue;
						}
					},
					// { text: '创建者', dataIndex: 'createdUser',width:130},
					{
						text : '版本',
						dataIndex : 'version',
						width : 50
					},
					{
						text : '周期',
						dataIndex : 'taskTime',
						editor :{},
						width : 100
					},
					{
						text : '下次执行时间',
						dataIndex : 'nextStartTime',
						width : 150
					},
					{
//						text : '脚本级别',
						text : '风险级别',
						dataIndex : 'scriptLevel',
						width : 100,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == 1) {
								backValue = '<font color="#F01024">高级风险</font>';
							} else if (value == 2) {
								backValue = '<font color="#FF7824">中级风险</font>';
							} else if (value == 3) {
								backValue = '<font color="#FFA826">低级风险</font>';
							}else if (value == 0) {
								backValue = '<font color="#FFA826">白名单</font>';
							}
							return backValue;
						}
					},
					{
						text : '操作',
						xtype : 'actiontextcolumn',
						width : 420,
						align : 'left',
//						menuDisabled : true,
						items : [
//									{
//									text : '取消',
//									iconCls : 'script_termina',
//									handler : function(grid, rowIndex) {
//											var iid = grid.getStore().data.items[rowIndex].data.iid;
//											var serviceName =grid.getStore().data.items[rowIndex].data.serviceName;
//											var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
//											var cid = grid.getStore().data.items[rowIndex].data.coatId;
//											forwardScriptTimeTaskCancel(iid, serviceName, workItemId, cid);
//										}
//									},
									{
										text : '终止',
										iconCls : 'script_termina',
										getClass : function(v, metadata, record) {
											    var user =record.get('performUser');
							 				    if(user != loginNameForWhiteSSTaskexecDelay){
							 				    	return 'x-hidden';
							 				    }
										},
										handler : function(grid, rowIndex) {
											var iid = grid.getStore().data.items[rowIndex].data.iid;
											var serviceName =grid.getStore().data.items[rowIndex].data.serviceName;
											var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
											var cid = grid.getStore().data.items[rowIndex].data.coatId;
											forwardScriptTimeTaskKill(iid, serviceName, workItemId, cid);
										}
									},
									
									{
										text : '任务详情',
										iconCls : 'script_termina',
										getClass : function(v, metadata, record) {
							 				     
										},
										handler : function(grid, rowIndex) {
											var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
											forwordAudiDetail(workItemId);
										}
									},{
										text : '执行历史',
										iconCls : 'execute',
										getClass : function(v, metadata, record) {
										    var user =record.get('performUser');
						 				    if(user != loginNameForWhiteSSTaskexecDelay){
						 				    	return 'x-hidden';
						 				    }
										},
										handler : function(grid, rowIndex) {
											var workItemId = grid.getStore().data.items[rowIndex].data.workItemId;
											flowHistoryFun(workItemId);
										}
									}
									
									]
					}
					/***************************************************************************/
//					{
//						text : '操作',
//						dataIndex : 'xq',
//						width : 130,
//						align : 'left',
//						renderer : function(value, p, record) {
//							var iid = record.get('iid');
//							var serviceName = record.get('serviceName');
//							var workItemId = record.get('workItemId');
////							var execFuncName = "forwardScriptTaskExecStart";
////							var execFuncNameStop = "forwardScriptTaskExecStop";
//							var execFuncNameCancel = "forwardScriptTimeTaskCancel";
//							var execScriptServiceDelayKill = "forwardScriptTimeTaskKill";
//							var wid = record.get('workItemId');
//							var cid = record.get('coatId');
//							var state = record.get('state');
//							var itaskTime = record.get('taskTime');
//							var user = record.get('performUser');
//							if(user == loginNameForWhiteSSTaskexecDelay){
//								return '<span class="switch_span"><a href="javascript:void(0)" onclick="' + execFuncNameCancel + '(' + iid + ',\'' + serviceName + '\',' + workItemId + ',' + cid
//								+ ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_cancel"></img>&nbsp;取消</a></span>&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="'
//								+ execScriptServiceDelayKill + '(' + iid + ',\'' + serviceName + '\',' 	+ workItemId 	+ ',' + cid + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_termina"></img>&nbsp;终止</a></span>';
//							}else{
//								return '<span class="switch_span">'+
//							       '<a href="javascript:void(0)" onclick="'+ execFuncNameCancel + '(' 	+ iid 	+ ',\'' + serviceName + '\',' 	+ workItemId + ',' 	+ cid + ')" style=""><img src="images/monitor_bg.png" align="absmiddle" class="script_cancel"></img>&nbsp;取消'+'</a>'+  '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
//							}
//							
//						}
//					} 
					];
			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
			  	store: scripts_store,
			    dock: 'bottom',
			    displayInfo: true,
			    emptyMsg:'找不到任何记录'
		    });
			// 定义可编辑grid组件
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});
			var scriptservice_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
				id:'scriptservice_grid',
				region : 'center',
//				height : contentPanel.getHeight() * 0.4,
//				width : contentPanel.getWidth() - 18,
				store : scripts_store,
				padding : grid_space,
				 border : false,
				 ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				columnLines : true,
				//selModel:selModel,
				columns : scriptservice_columns,
				plugins : [ cellEditing ]
			});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "scriptexec_taskexecDelay_areaForWhite",
				layout : 'border',
				border : false,
				height : contentPanel.getHeight()-modelHeigth,
//				width : '100%',
				width : contentPanel.getWidth (),
				items : [ search_form, scriptservice_grid ]
			});

			function clearQueryWhere() {
				search_form.getForm().findField("bsName").setValue('');
				search_form.getForm().findField("bussType").setValue('');
				search_form.getForm().findField("scriptName").setValue('');
				search_form.getForm().findField("serviceName").setValue('');
				search_form.getForm().findField("scriptTypeParam").setValue('');
				search_form.getForm().findField("taskName").setValue('');
				platForm.setValue();
			}

			contentPanel.on('resize', function() {
				mainPanel.setHeight (contentPanel.getHeight () );
				mainPanel.setWidth (contentPanel.getWidth ()-modelHeigth );
			});
			
			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
			{
				Ext.destroy (mainPanel);
				destroyRubbish();
				if (Ext.isIE)
				{
					CollectGarbage ();
				}
			});
			
			/** 修改定时任务周期 */
			function updateScriptTimeTask (serviceId, serviceName, workItemId, itaskTime,coatId)
			{
				var records = scripts_store.getModifiedRecords();
				//没有修改
				if(records.length==0)
				{
					Ext.Msg.show({
					     title:'提示',
					     msg: '周期没有任何修改',
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.INFO
					});
					return;
				}else{
					Ext.Msg.confirm('系统提示', '确认修改定时任务周期吗?', function(btn) {
						if (btn == 'yes') {
							var lstAddRecord = new Array(); 
							var flag = true;
							Ext.each(records, function(record) {

								//周期为空
								if(record.data.taskTime.trim()==""){
									Ext.Msg.show({
									     title:'提示',
									     msg: '周期不能为空',
									     buttons: Ext.Msg.OK,
									     icon: Ext.Msg.INFO
									});
									flag = false;
									return;
								}
								//周期长度超过255
								if(jmz.GetLength(record.data.taskTime.trim())>255){
									Ext.Msg.show({
									     title:'提示',
									     msg: '周期不能超过255个字符',
									     buttons: Ext.Msg.OK,
									     icon: Ext.Msg.INFO
									});
									flag = false;
									return;
								}

								lstAddRecord.push(record.data);
							});
							if(flag)
							{
								Ext.Ajax.request({
									url: 'updateTaskTimeScriptServiceDelay.do',
									params : {
										jsonData : Ext.encode(lstAddRecord)
									},
									success: function(resp,opts) 
									{			
										 var respText = Ext.JSON.decode(resp.responseText);
										 var success =  respText.success;
										 var msg =  respText.message;
										 if(success){
											 Ext.Msg.alert('提示',msg);
											 Ext.getCmp("scriptservice_grid").store.reload();
										 }else{
											 Ext.Msg.alert('提示', msg);
										 }
									 },
									 failure : function(result, request) {
										 secureFilterRs(result,"操作失败！");
									 } 		
								});
							}
						}
					});
				}
			}
		
			//取消
function forwardScriptTimeTaskCancel(serviceId, serviceName, workItemId, coatId) {
	Ext.Msg.confirm('系统提示', '取消任务，请确认?', function(btn) {
		if (btn == 'yes') {
			destroyRubbish(); // 销毁本页垃圾
			Ext.Ajax.request({
//				url : 'execScriptServiceStop.,
				url : 'execScriptServiceDelayCancel.do',
				method : 'POST',
				params : {
					serviceId : serviceId,
					serviceName : serviceName,
					workItemId : workItemId,
					coatId : coatId
				},
				success : function(response, request) {
					Ext.Msg.alert('提示', '操作成功！');
					Ext.getCmp("scriptservice_grid").store.reload();
					// execTabsPanel.setHeight(contentPanel.getHeight() * 0.85);
					// scriptservice_grid.setHeight(contentPanel.getHeight() *
					// 0.6);
					// mainPanel.setHeight(contentPanel.getHeight() - 61
					// + contentPanel.getHeight() * 0.8);
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '执行失败！');
				}
			});
		}
	});
}
function forwardScriptTimeTaskKill(serviceId, serviceName, workItemId, coatId) {
	Ext.Msg.confirm('系统提示', '您确定要进行终止操作吗?', function(btn) {
		if (btn == 'yes') {
			destroyRubbish(); // 销毁本页垃圾
			Ext.Ajax.request({
				url : 'execScriptServiceDelayKill.do',
				method : 'POST',
				params : {
					serviceId : serviceId,
					serviceName : serviceName,
					workItemId : workItemId,
					coatId : coatId
				},
				success : function(response, request) {
					Ext.Msg.alert('提示', '操作成功！');
					Ext.getCmp("scriptservice_grid").store.reload();
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '执行失败！');
				}
			});
		}
	});
}
function forwordAudiDetail(workItemId) {
    var auditingWin = Ext.create('widget.window', {
    title: '任务详情',
    closable: true,
    closeAction: 'destroy',
    width: contentPanel.getWidth(),
    minWidth: 350,
    height: contentPanel.getHeight(),
    draggable: false,
    // 禁止拖动
    resizable: false,
    // 禁止缩放
    modal: true,
    loader: {
        url: 'initReviewForSsPublishExec.do',
        params: {
        	iworkItemid : workItemId,
        	from :'100',
        	submitType : 'tc',
            	istate : 1,
            	btnCode : 25
            },
            autoLoad: true,
            scripts: true
        }
    });
auditingWin.show();
}

function flowHistoryFun(wid){
	var serviceName='';
var state =-2;
var startTime ='';
var endTime ='';
var taskName = '';
var filter_audiUser = '';
var flowid;
Ext.Ajax.request({
    url: 'queryMaxFlowId.do',
    method: 'POST',
    async: false,
    params: {
        workItemId: wid
    },
    success: function(response, options) {
    	flowid = Ext.decode(response.responseText).flowid;
    		if(flowid>0){
    			contentPanel.getLoader().load({
    				url: "forwardscriptflowcoatforexecforwhite.do",
    				scripts: true,
    				params : {
    					flowId:flowid,
    					flag:1, 
    					forScriptFlow: 1,
    					filter_serviceName:serviceName,
    					filter_serviceState:state,
    					filter_serviceStartTime:startTime,
    					filter_serviceEndTime:endTime,
    					filter_serviceTaskName : taskName,
    					filter_audiUser : filter_audiUser,
    					fromMenu:'taskexec'
    				}
    			});
    		}else{
    			Ext.Msg.alert('提示', '该任务没有执行历史！');
        			return;
        		}
        	
        },
        failure: function(result, request) {
        }
    });
}		

			
	});



////终止
//function forwardScriptTaskExecStop(serviceId, serviceName, workItemId, coatId) {
//	Ext.Msg.confirm('系统提示', '您确定要进行终止操作吗?', function(btn) {
//		if (btn == 'yes') {
//			destroyRubbish(); // 销毁本页垃圾
//			Ext.Ajax.request({
//				url : 'execScriptServiceStop.do',
//				method : 'POST',
//				params : {
//					serviceId : serviceId,
//					serviceName : serviceName,
//					workItemId : workItemId,
//					coatId : coatId
//				},
//				success : function(response, request) {
//					scripts_store.reload();
//					// execTabsPanel.setHeight(contentPanel.getHeight() * 0.85);
//					// scriptservice_grid.setHeight(contentPanel.getHeight() *
//					// 0.6);
//					// mainPanel.setHeight(contentPanel.getHeight() - 61
//					// + contentPanel.getHeight() * 0.8);
//				},
//				failure : function(result, request) {
//					Ext.Msg.alert('提示', '执行失败！');
//				}
//			});
//		}
//	});
//}