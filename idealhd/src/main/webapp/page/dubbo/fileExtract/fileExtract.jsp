<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	//增加服务器 ip批量查询
	boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
	boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
%>
<html>
	<head>
		<script type="text/javascript">
			var batchQuerySwitch = "<%=batchQuery%>";
			var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
		</script>
		<script    type="text/javascript"
				   src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageIPSearch.js"></script>
		<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/fileExtract/fileExtract.js"></script>
	</head>
	<body>
		<div id="file_extract_area" style="width: 100%;height: 100%"></div>
	</body>
</html>