Ext.onReady(function() {
    destroyRubbish();
    
    var editingAgentIds = new Array();
    var globalAgentIds = new Array();
    var chosedAgentWinForSee;
    var globalConfigPaths = {};
    
    if (typeof String.prototype.endsWith != 'function') {
        String.prototype.endsWith = function(suffix) {
            return this.indexOf(suffix, this.length - suffix.length) !== -1;
        };
    }

    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        }]
    });

    Ext.define('osTypeModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'name',
            type: 'string'
        }]
    });

    
    
    var osTypeStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'osTypeModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getOsTypeForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	
	var osTypeComb=Ext.create ('Ext.form.field.ComboBox', {
		fieldLabel : '操作系统',
		emptyText: '--请选择操作系统--',
		margin : '0 0 0 10',
		labelAlign : 'right',
		labelWidth : 70,
		width:'22%',
		store : osTypeStore,
		displayField : 'name',
		valueField : 'name',
		triggerAction : 'all',
		queryMode:'local',
		editable : true,
		mode : 'local',
		listeners: {
			change: function( comb, newValue, oldValue, eOpts ) {
				agent_store.load();
			},
			 specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBarWin.moveFirst();
	                }
	            }
		}
	});
    
    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'Agent IP',
        displayField: 'agentip',
        emptyText: '--请输入Agent IP--',
        labelWidth: 65,
        labelAlign: 'right',
        width: '22%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBarWin.moveFirst();
                }
            }
        }
    });

    
    
    var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox', {
	    fieldLabel : '资源组',
	    emptyText: '--请选择资源组--',
	    margin : '0 10 0 10',
	    labelAlign : 'right',
        labelWidth : 65,
        width:'24%',
	    multiSelect: true,
	    hidden:removeAgentSwitch,
	    store : resourceGroupStore,
	    displayField : 'name',
	    valueField : 'id',
	    triggerAction : 'all',
	    queryMode:'local',
	    editable : true,
	    mode : 'local',
    	listeners: {
	      change: function( comb, newValue, oldValue, eOpts ) {
	    	  agent_store.load();
	      },
	      specialkey: function(field, e){
              if (e.getKey() == e.ENTER) {
            	  pageBarWin.moveFirst();
              }
          }
    	}
	});

    var search_form = Ext.create('Ext.form.Panel', {
		region:'north',
    	buttonAlign : 'center',
    	baseCls:'customize_gray_back',
    	border : false,
	    items: [{
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back', 
				border : false,
				dock : 'top',	    	
	    		items:[{
                    fieldLabel: '提取路径',
                    labelAlign: 'right',
                    labelWidth: 65,
                    name: 'targetPath',
                    width:'29%',
                    xtype: 'textfield'
                },{
                    fieldLabel: '提取文件名',
                    labelAlign: 'right',
                    labelWidth: 79,
                    name: 'targetFile',
                    width:'29%',
                    xtype: 'textfield'
                },{
                    fieldLabel: 'FTP存储路径',
                    labelAlign: 'right',
                    labelWidth: 93,
                    name: 'ftpPath',
                    width:'35.5%',
                    xtype: 'textfield'
                },{
                    xtype: "button",
                    cls : 'Common_Btn',
                    text: "提取",
                    handler: function() {
                        if(globalAgentIds.length<1) {
		  					Ext.Msg.alert('提示', '请选择文件提取服务器！');
		  					return;
		  				}
                        
                        var filepath = search_form.getForm().findField("targetPath").getValue();
                        var fileName = search_form.getForm().findField("targetFile").getValue();
                        var ftppath = search_form.getForm().findField("ftpPath").getValue();
                        if(!filepath) {
                        	Ext.Msg.alert('提示', '请填写提取路径！');
                            return;
                        }
                        
                        if(!filepath.endsWith('/')) {
                        	Ext.Msg.alert('提示', '提取路径需以"/"结尾！');
                            return;
                        }
                        
                        if(!ftppath) {
                        	Ext.Msg.alert('提示', '请填写FTP存储路径！');
                        	return;
                        }
                        if(!ftppath.endsWith('/')) {
                        	Ext.Msg.alert('提示', 'FTP存储路径需以"/"结尾！');
                            return;
                        }
                        
                        if(filepath.indexOf("\\")>-1) {
                        	Ext.Msg.alert('提示', '提取路径的分隔符请使用 "/"！');
                            return;
                        }
                        if(ftppath.indexOf("\\")>-1) {
                        	Ext.Msg.alert('提示', 'FTP存储路径的分隔符请使用 "/"！');
                            return;
                        }
                        
                        /*var jsonData = "[";
          				for(var i = 0, len = records.length; i < len; i++){
          					var tfilePath = records[i].data.agentTargetPath;
          					var tftpPath = records[i].data.agentFtpPath;
          					if(tfilePath.indexOf("\\")>-1) {
                            	Ext.Msg.alert('提示', '个性化提取路径的分隔符请使用 "/"！');
                                return;
                            }
          					if(tfilePath && !tfilePath.endsWith('/')) {
                            	Ext.Msg.alert('提示', '个性化提取路径需以"/"结尾！');
                                return;
                            }
                            if(tftpPath.indexOf("\\")>-1) {
                            	Ext.Msg.alert('提示', '个性化FTP存储路径的分隔符请使用 "/"！');
                                return;
                            }
          					
          					var ss = Ext.JSON.encode(records[i].data);
                            if (i == 0)
                            	jsonData = jsonData + ss;
                            else
                            	jsonData = jsonData + "," + ss;
          				}
          				jsonData = jsonData + "]";*/
          				
                        Ext.Ajax.request({
                            url: 'execScriptServiceForExtract.do',
                            method: 'POST',
                            params: {
                                agentIds: globalAgentIds,
                                configPaths: JSON.stringify(globalConfigPaths),
                                filepath: filepath,
                                fileName: fileName,
                                ftppath: ftppath,
                                ifrom :2,
                                flag: 0
                            },
                            success: function(response, request) {
                            	var success = Ext.decode(response.responseText).success;
			      				var one = Ext.decode(response.responseText).one;
			      				var message = Ext.decode(response.responseText).message;
			      				if(success) {
			      					if(one) {
			      						Ext.Msg.alert('提示', "已在执行服务器上执行提取！",function(){
		                                	 var flowId = message.flowId;
		                                     var coatId = message.coatId;
		                                     popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0,fromMenu:'文件提取'},10, true);
		                                });
			      					} else {
			      						Ext.Msg.confirm("请确认", "本次提取有多种操作系统类型的代理，" +
			      								"所以执行历史记录有多条，默认将跳转到最后一条执行记录监控，是否继续？", function(id){
							    			if(id=='yes') {
							    				var flowId = message.flowId;
			                                    var coatId = message.coatId;
			                                    popNewTab('工具监控', 'forwardscriptserverForToolMonitor.do', {coatid: coatId,flag: 0,fromMenu:'文件提取'},10, true);
							    			}
							    		});
			      					}
			      				} else {
			      					Ext.Msg.alert('提示', message);
			      				}
                            },
                            failure: function(result, request) {
                                Ext.Msg.alert('提示', '执行失败！');
                            }
                        });
                    }
                }]
            
	    	}]
	    }]
	});

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [{
            name: 'iid',
            type: 'int'
        },{name: 'sysName',     type: 'string'},
        {name: 'appName',     type: 'string'},
        {name: 'hostName',     type: 'string'},
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'osType',
            type: 'string'
        },
        {
            name: 'agentDesc',
            type: 'string'
        },
        {
            name: 'agentState',
            type: 'int'
        },
        {
            name: 'agentTargetPath',
            type: 'string'
        },
        {
        	name: 'agentTargetFile',
        	type: 'string'
        },
        {
            name: 'agentFtpPath',
            type: 'string'
        }]
    });
    agent_grid_url = 'getAllAgentForIpSearch.do';
    agent_store_win = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListForIssued.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    agent_store_win.on('beforeload', function(store, options) {
        new_params_agent = {
            agentIp: agent_ip.getValue(),
            rgIds: resourceGroupObj.getValue(),
            osType: osTypeComb.getValue(),
            flags: 2,
            batchComputerName:pubDesc_sm_ipsearch.getValue()
        };

        Ext.apply(agent_store_win.proxy.extraParams, new_params_agent);
    });
    
    agent_store_win.addListener('load', function(me, records, successful, eOpts) {
        if (editingAgentIds) {
            var chosedRecords = []; //存放选中记录
            $.each(records, function(index, record) {
                if (editingAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            agent_grid_win.getSelectionModel().select(chosedRecords, false, true); //选中记录
        }
    });

    agent_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 50,
		model: 'agentModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentChosedList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});
	
    agent_store.on('beforeload', function(store, options) {
        var new_params = {
            agentIds: JSON.stringify(globalAgentIds)
        };
        Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    agent_store.addListener('load', function(me, records, successful, eOpts) {
        for (var prop in globalConfigPaths) {
            if (agent_store.findRecord('iid', prop)) {
            	if(globalConfigPaths[prop].hasOwnProperty('agentTargetPath')) {
            		agent_store.findRecord('iid', prop).set('agentTargetPath', globalConfigPaths[prop]['agentTargetPath']);
            	}
            	if(globalConfigPaths[prop].hasOwnProperty('agentTargetFile')) {
            		agent_store.findRecord('iid', prop).set('agentTargetFile', globalConfigPaths[prop]['agentTargetFile']);
            	}
            	if(globalConfigPaths[prop].hasOwnProperty('agentTargetPath')) {
            		agent_store.findRecord('iid', prop).set('agentFtpPath', globalConfigPaths[prop]['agentFtpPath']);
            	}
            }
        }
    });

    var agent_columns = [
    {
        text: '主键',
        dataIndex: 'iid',
        hidden: true
    },
    { text: '系统',  dataIndex: 'sysName',width:150},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,flex:1},
    { text: '计算机名',  dataIndex: 'hostName',width:150},
    { text: 'IP',  dataIndex: 'agentIp',width:150},
    {
        text: '端口号',
        dataIndex: 'agentPort',
        width: 100
    },
    {
        text: '操作系统',
        dataIndex: 'osType',
        width: 140
    },
    {
        text: '描述',
        dataIndex: 'agentDesc',
        flex: 1,
        hidden: true
    },
    {
        text: '状态',
        dataIndex: 'agentState',
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 0) {
                backValue = "Agent正常";
            } else if (value == 1) {
                backValue = "Agent异常";
            }
            return backValue;
        }
    },{
		text : '提取路径（个性化）',
		dataIndex : 'agentTargetPath',
		width : 150,
		editor : {
			xtype : 'textfield'
		}
	},{
		text : '提取文件名（个性化）',
		dataIndex : 'agentTargetFile',
		width : 150,
		editor : {
			xtype : 'textfield'
		}
	},{
		text : 'FTP存储路径（个性化）',
		dataIndex : 'agentFtpPath',
		width : 150,
		flex:1,
		editor : {
			xtype : 'textfield'
		}
	}];
    
    var agent_columns_win = [{
    	text: '主键',
    	dataIndex: 'iid',
    	hidden: true
    },
    { text: '名称',  dataIndex: 'sysName',flex:1},
    { text: '应用名称',  dataIndex: 'appName',hidden: !CMDBflag,flex:1},
    { text: '计算机名',  dataIndex: 'hostName',flex:1},
    { text: 'IP',  dataIndex: 'agentIp',width:150},
    {
    	text: '端口号',
    	dataIndex: 'agentPort',
    	width: 100
    },
    {
    	text: '操作系统',
    	dataIndex: 'osType',
    	flex: 1,
    	width: 140
    },
    {
    	text: '描述',
    	dataIndex: 'agentDesc',
    	flex: 1,
    	hidden: true
    },
    {
    	text: '状态',
    	dataIndex: 'agentState',
    	width: 130,
    	renderer: function(value, p, record) {
    		var backValue = "";
    		if (value == 0) {
    			backValue = "Agent正常";
    		} else if (value == 1) {
    			backValue = "Agent异常";
    		}
    		return backValue;
    	}
    }];

    
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agent_store,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });
    var pageBarWin = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: agent_store_win,
    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
    	dock: 'bottom',
    	displayInfo: true
    });
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
    agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	title: '已选服务器',
        store: agent_store,
        cls:'customize_panel_back',
        border: false,
        padding : grid_space,
        columnLines: true,
        columns: agent_columns,
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
//        bbar: pageBar,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        plugins : [ cellEditing ],
        dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',	
    		items:['->',{
				xtype: 'button',
				cls: 'Common_Btn',
				text : '增加服务器',
				handler : function() {
					if (!chosedAgentWinForSee) {
                        chosedAgentWinForSee = Ext.create('Ext.window.Window', {
                            title: '增加服务器',
                            autoScroll: true,
                            modal: true,
                            resizable: false,
                            closeAction: 'destory',
                            width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
                            layout: 'border',
                            items: [agent_grid_win],
                            buttonAlign: 'center',
                            dockedItems: [{
                                xtype: 'toolbar',
                                // baseCls:'customize_gray_back',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    text: "确定",
                                    cls: 'Common_Btn',
                                    margin: '6',
                                    handler: function() {
                                    	globalAgentIds = editingAgentIds.slice(0);
                                    	pageBar.moveFirst();
                                        this.up("window").close();
                                    }
                                },
                                {
                                    xtype: "button",
                                    text: "关闭",
                                    cls: 'Common_Btn',
                                    handler: function() {
                                        this.up("window").close();
                                    }
                                }]
                            }]
                        });
                    }
					editingAgentIds = globalAgentIds.slice(0);
                    chosedAgentWinForSee.show();
                    pageBarWin.moveFirst();
				}
			},{
				xtype: 'button',
				cls: 'Common_Btn',
				text : '删除服务器',
				handler : function() {
					var records = agent_grid.getSelectionModel().getSelection();
                    if (records.length > 0) {
                        for (var i = 0, len = records.length; i < len; i++) {
                        	globalAgentIds.remove(records[i].get('iid'));
                        }
                        pageBar.moveFirst();
                    } else {
                        Ext.Msg.alert('提示', "请选择服务器！");
                        return;
                    }
				}
			}]
    	}]
    });
    
    agent_grid.on('edit', function(editor, e) {
        var record = e.record;
        var iid = record.get('iid');
        var c_agentTargetPath = record.get('agentTargetPath');
        var c_agentTargetFile = record.get('agentTargetFile');
        var c_agentFtpPath = record.get('agentFtpPath');
        
		if(c_agentTargetPath.indexOf("\\")>-1) {
			Ext.Msg.alert('提示', '个性化提取路径的分隔符请使用 "/"！');
            return;
        }
		if(c_agentTargetPath && !c_agentTargetPath.endsWith('/')) {
        	Ext.Msg.alert('提示', '个性化提取路径需以"/"结尾！');
            return;
        }
        if(c_agentFtpPath.indexOf("\\")>-1) {
        	Ext.Msg.alert('提示', '个性化FTP存储路径的分隔符请使用 "/"！');
            return;
        }
        
        if (!Ext.isEmpty(Ext.util.Format.trim(c_agentTargetPath)) || !Ext.isEmpty(Ext.util.Format.trim(c_agentTargetFile)) || !Ext.isEmpty(Ext.util.Format.trim(c_agentFtpPath))) {
        	globalConfigPaths[iid] = {
        			iid: iid,
        			agentTargetPath: c_agentTargetPath,
        			agentTargetFile: c_agentTargetFile,
        			agentFtpPath: c_agentFtpPath
        	}
        } else {
            delete globalConfigPaths[iid];
        }
    });

    flagWin = 'fileExtract';
    agent_grid_win = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	store: agent_store_win,
    	border: false,
    	columnLines: true,
    	columns: agent_columns_win,
    	cls:'customize_panel_back',
    	selModel: Ext.create('Ext.selection.CheckboxModel', {
    		checkOnly: true
    	}),
//    	bbar: pageBarWin,
    	ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    	dockedItems : [{
			xtype : 'toolbar',
//			baseCls:'customize_gray_back',    
			border : false,
			dock : 'top',		    	
    		items:[resourceGroupObj,osTypeComb,agent_ip, {
				xtype: 'button',
				margin: '0 5 0 0',
				cls: 'Common_Btn',
				text : '查询',
				handler : function() {
					pageBarWin.moveFirst();
				}
			},{
				xtype: 'button',
				margin: '0 5 0 0',
				cls: 'Common_Btn',
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			},{
                xtype : 'button',
                cls : 'Common_Btn',
                hidden: batchQuerySwitch == 'false',
                text : 'IP批量查询',
                handler : function (){
                    batchQueryForIp();
                }
            }]
    	}],
    	listeners: {
            select: function(t, record, index, eOpts) {
                if (editingAgentIds.indexOf(record.get('iid')) == -1) {
                	editingAgentIds.push(record.get('iid'));
                }
            },
            deselect: function(t, record, index, eOpts) {
                if (editingAgentIds.indexOf(record.get('iid')) > -1) {
                	editingAgentIds.remove(record.get('iid'));
                }
            }
        }
    });
    function clearQueryWhere() {
    	osTypeComb.setValue('');
    	resourceGroupObj.setValue('');
    	agent_ip.setValue('');
        pubDesc_sm_ipsearch.setValue('');
    }

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "file_extract_area",
        layout: 'border',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        border: true,
        items: [search_form,  agent_grid]
    });

    contentPanel.on('resize',
    function() {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });
   
});
