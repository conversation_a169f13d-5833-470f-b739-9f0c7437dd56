<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
 <% 
	String shutdownTaskName = Environment.getInstance().getScriptShutdownServicename();
	String shutdownCheckName = Environment.getInstance().getScriptShutdownCheckServicename();

%>
<html>
<head>
 <script type="text/javascript"> 
 var shutdownTaskName = '<%=shutdownTaskName%>';
 var shutdownCheckName = '<%=shutdownCheckName%>';
 var loginUserShutdownM = '<%=request.getAttribute("loginUser")%>';
 var eachNumForShutdown = '<%=request.getAttribute("eachNum")%>';
 var shutdownServiceIId = <%=request.getAttribute("shutdownServiceIId")%>;
 var checkServiceIIdForShutdown = <%=request.getAttribute("checkServiceIId")%>;
 var shutdownServiceUuid = '<%=request.getAttribute("shutdownServiceUuid")%>';
 var shutDownScriptType = '<%=request.getAttribute("shutDownScriptType")%>';
 var checkServiceUuidForShutdown = '<%=request.getAttribute("checkServiceUuid")%>'; 
 var scriptLevel1ForShutdown = <%=request.getAttribute("scriptLevel1")%>;
 var scriptLevel2ForShutdown = <%=request.getAttribute("scriptLevel2")%>;
 </script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/shutdownMaintenance/shutdownMaintenance.js"></script>
</head>
<body>
<div id="shutdownMaintenanceMain" style="width: 100%;height: 100%">
</div>
</body>
</html>