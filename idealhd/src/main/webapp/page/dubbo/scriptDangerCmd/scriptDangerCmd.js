Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.define('DangerCmd', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'cmd',
            type: 'string'
        },
        {
            name: 'checktype',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptCmdLevel',
            type: 'string'
        },
        {
            name: 'scriptCmdRemark',
            type: 'string'
        }]
    });

    dangerCmdStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'DangerCmd',
        proxy: {
            type: 'ajax',
            url: 'getDangerCmdList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    dangerCmdStore.on('beforeload', function(store, options) {
        var new_params = {
            cmdQuery : commdQuery.getValue()
        };

        Ext.apply(dangerCmdStore.proxy.extraParams, new_params);
    });

    var scriptTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "shell"
        },
        {
            "name": "bat"
        },
        {
            "name": "perl"
        },
        {
            "name": "python"
        },
         {"name":"sql"},
         {
             "name": "powershell"
         }
        ]
    });

    let checkTypeStore = Ext.create('Ext.data.Store', {
        fields: ['id','name'],
        data: [{
            "id":'1',
            "name": "黑名单"
        },
        {
            "id":'2',
            "name": "白名单"
        }
        ]
    });

    var scriptLevelStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            'id': '0',
            'name': dangerCmdCheckTypeSwitch?'脚本':'提醒'
        },
        {
            'id': '1',
            'name': dangerCmdCheckTypeSwitch?'白名单命令':'屏蔽'
        }]
    });
    var dangerCmdColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: 'ID',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '命令',
        dataIndex: 'cmd',
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '校验类型',// 黑白两种情况
        dataIndex: 'checktype',
        flex: 1,
        hidden:!dangerCmdCheckTypeSwitch, //false 隐藏，true显示 浦发需求
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: checkTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'id'
        }),
        renderer: function(value) {
            let index = checkTypeStore.find('id', value);
            let record = checkTypeStore.getAt(index);
            if (record != null) {
                return record.data.name;
            } else {
                return value;
            }
        }
    },
    {
        text: '脚本类型',
        dataIndex: 'scriptType',
        width: 150,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: scriptTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name'
        })
    },
    {
        text: '校验级别',
        dataIndex: 'scriptCmdLevel',
        width: 150,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: scriptLevelStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'id'
        }),
        renderer: function(value) {
            var index = scriptLevelStore.find('id', value);
            var record = scriptLevelStore.getAt(index);
            if (record != null) {
                return record.data.name;
            } else {
                return value;
            }
        }

    },
    {
        text: '命令说明',
        dataIndex: 'scriptCmdRemark',
        flex: 1,
        editor: {
            allowBlank: false
        }
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: dangerCmdStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false
    });
    let commdQuery = new Ext.form.TextField({
        name : 'commdQuery',
        fieldLabel : '命令',
        emptyText : '--请输入命令--',
        labelWidth : 65,
        padding : '5',
        // padding : '5',
        width : '25%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    dangerCmdGrid.ipage.moveFirst();
                }
            }
        }
        // value: filter_serviceName
    });
	var form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : [ commdQuery,{
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    dangerCmdGrid.ipage.moveFirst();
                }
            },{
                text: '重置',
                cls: 'Common_Btn',
                handler: function () {
                    commdQuery.setValue("");
                }
            },'->',{
                text: '导入',
                cls: 'Common_Btn',
                hidden:!dangerCmdImportSwitch,
                handler: importExcel
            },{
                text: '导出',
                cls: 'Common_Btn',
                hidden:!dangerCmdImportSwitch,
                handler: exportExcel
            },{
                text: '帮助',
                cls: 'Common_Btn',
                hidden:!scriptDangercmdHelpdocSwitch,
                //iconCls:'sc_add',
                handler: help
            },{
		        text: '增加',
		        cls: 'Common_Btn',
		        //iconCls:'sc_add',
		        handler: add
		    },
		    {
		        text: '保存',
		        cls: 'Common_Btn',
		        //iconCls:'sc_save',
		        handler: saveDangerCmd
		    },
		    '-', {
		        itemId: 'delete',
		        text: '删除',
		        cls: 'Common_Btn',
		       //iconCls:'sc_delete',
//		        disabled: true,
		        handler: deleteDangerCmd
		    }]
		} ]
	});
	
    var dangerCmdGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: dangerCmdStore,
        cls:'customize_panel_back',
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        padding : grid_space,
        plugins: [cellEditing],
		border:true,
//		bbar: pageBar, 
        columnLines: true,
        columns: dangerCmdColumns,
        animCollapse: false
    });

/*    dangerCmdGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });*/

    if(isTabSwitch){
        var scriptDangerCmdPanel = Ext.create('Ext.panel.Panel', {
            renderTo: "scriptDangerCmd_grid_area"+scriptDangerCmdnow,
            layout: 'border',
            width : contentPanel.getWidth(),
            height :contentPanel.getHeight() - modelHeigth,
            bodyPadding : grid_margin,
            border : true,
            bodyCls:'service_platform_bodybg',
            items: [form,dangerCmdGrid]
        });
    }else{
        var scriptDangerCmdPanel = Ext.create('Ext.panel.Panel', {
            renderTo: "scriptDangerCmd_grid_area",
            layout: 'border',
            width : contentPanel.getWidth(),
            height :contentPanel.getHeight() - modelHeigth,
            bodyPadding : grid_margin,
            border : true,
            bodyCls:'service_platform_bodybg',
            items: [form,dangerCmdGrid]
        });
    }

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    function add() {
        var store = dangerCmdGrid.getStore();
        var p = {
            iid: '',
            cmd: '',
            scriptType: '',
            scriptCmdLevel: '',
            scriptCmdRemark: ''
        };
        store.insert(0, p);
        dangerCmdGrid.getView().refresh();
    }
    function help() {
        window.location.href = 'downloadAgentTemplate.do?fileName=DangerousCmdHelp.docx';
    }
    function importExcel(){
        var uploadWindows;
        var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel',{
            border : false,
            items : [{
                xtype: 'filefield',
                name: 'importFile', // 设置该文件上传空间的name，也就是请求参数的名字
                fieldLabel: '选择文件',
                labelWidth: 80,
                anchor: '90%',
                margin: '10 10 0 40',
                buttonText: '浏览',
                labelStyle:'margin-top:12px;'
            }],
            buttonAlign : 'center',
            buttons :[{
                text : '确定',
                handler :upExeclData
            },{
                text : '取消',
                handler : function(){
                    uploadWindows.close();
                }
            },{
				text: '下载模板',
				handler: function() {
                    let fileName = "";
                    if(dangerCmdCheckTypeSwitch){
                        fileName  = "8";
                    }else{
                        fileName  = "9";
                    }
					window.location.href = 'downloadSsTemplate.do?fileName='+fileName;
				}
			}]
        });
        /**
         * Excel导入信息窗体
         */
        uploadWindows = Ext.create('Ext.window.Window', {
            title : '关键命令导入',
            layout : 'fit',
            height : 180,
            width : 600,
            modal : true,
            items : [ uploadForm ],
            listeners : {
                close : function(g, opt) {
                    uploadForm.destroy();
                }
            }
        });
        uploadWindows.show();
        function upExeclData(){
            var form = uploadForm.getForm();
            var hdupfile=form.findField("importFile").getValue();
            if(hdupfile==''){
                Ext.Msg.alert('提示',"请选择文件...");
                return ;
            }
            uploadTemplate(form);
        }
        var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
        function uploadTemplate(form) {
            checkMask.show();
            uploadWindows.hide();
            if (form.isValid()) {
                form.submit({
                    url: 'importScriptDangerCmd.do',
                    success: function(form, action) {
                        var sumsg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示',sumsg);
                        checkMask.hide();
                        dangerCmdStore.reload();
                        checkMask.hide();
                        return;
                    },
                    failure: function(form, action) {
                        var msg = Ext.decode(action.response.responseText).message;
                        Ext.Msg.alert('提示',msg);
                        checkMask.hide();
                        return;
                    }
                });
            }
            checkMask.hide();
        }
    }





    function exportExcel() {
        var cmdRecord=[];
        var records  = dangerCmdGrid.getView().getSelectionModel().getSelection();

        if(records.length===0){
            Ext.Msg.alert('提示', '请选择要导出的数据！');
        }else{
            Ext.each(records, function(record) {

                if(dangerCmdCheckTypeSwitch){
                    if(record.data.checktype==="1")record.data.checktype='黑名单';
                    if(record.data.checktype==="2")record.data.checktype='白名单';
                    if(record.data.scriptCmdLevel==="0")record.data.scriptCmdLevel='脚本';
                    if(record.data.scriptCmdLevel==="1")record.data.scriptCmdLevel='白名单命令';
                }else {
                    if(record.data.scriptCmdLevel==="0")record.data.scriptCmdLevel='提醒';
                    if(record.data.scriptCmdLevel==="1")record.data.scriptCmdLevel='屏蔽';
                }
                cmdRecord.push(record.data);
            });
            $.fileDownload('exportScriptDangerCmd.do',{
                httpMethod: 'POST',
                traditional: true,
                data:{
                    jsonData:Ext.encode(cmdRecord)
                },
                successCallback: function(url){

                },
                failCallback: function (html, url) {
                    Ext.Msg.alert('提示', '导出失败！');
                    return;
                }
            });
        }
    }
    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"保存中..."});
    function saveDangerCmd() {
        var m = dangerCmdStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var cmd = m[i].get("cmd");
            var scriptType = m[i].get("scriptType").trim();
            var scriptLevel = m[i].get("scriptCmdLevel").trim();
            let checkType = m[i].get("checktype").trim();
            if (dangerCmdCheckTypeSwitch) {
                if (""===checkType || null== checkType) {
                    setMessage('校验类型不能为空！');
                    return;
                }
            }

            if ("" == cmd || null == cmd) {
                setMessage('命令不能为空！');
                return;
            }
            if ("" == scriptLevel || null == scriptLevel) {
                setMessage('校验级别不能为空！');
                return;
            }
            if (fucCheckLength(cmd) > 1000) {
                setMessage('命令不能超过1000字符！');
                return;
            }

            if ("" == scriptType || null == scriptType) {
                setMessage('脚本类型不能为空！');
                return;
            }
           for (var k = 0; k < dangerCmdStore.getCount(); k++) {
						var record = dangerCmdStore.getAt(k);
						var cname = record.data.cmd;
						var type = record.data.scriptType;
						var level = record.data.scriptCmdLevel;
						let checkTypes = record.data.checktype;
                       if (dangerCmdCheckTypeSwitch) {
                           if (cname == cmd && type.trim() == scriptType && level.trim()==scriptLevel && checkTypes.trim()==checkType) {
                               n = n + 1;
                           }
                       }else{
                           if (cname == cmd && type.trim() == scriptType && level.trim()==scriptLevel) {
                               n = n + 1;
                           }
                       }

					}
					if (n > 1) {
						setMessage('命令不能有重复！');
						return;
					}
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        checkMask.show();
        Ext.Ajax.request({
            url: 'saveDangerCmd.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                checkMask.hide();
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    dangerCmdStore.modified = [];
                    dangerCmdStore.reload();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', '命令内容正则表达式不合法！');
                }
            },
            // failure : function(result, request) {
            // Ext.Msg.alert('提示', '保存失败！');
            // }
            failure: function(result, request) {
                checkMask.hide();
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function deleteDangerCmd() {
        var data = dangerCmdGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否真的要删除命令？",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                            ids.push(iid);
                        }else{
                        	 dangerCmdStore.remove(record);
                             }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteDangerCmd.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dangerCmdStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    dangerCmdGrid.getView().refresh();
                    }
                  
                }
            });
        }
    }

    /** 窗口尺寸调节* */
    contentPanel.on('resize',
    function() {
        scriptDangerCmdPanel.setHeight (contentPanel.getHeight () - modelHeigth);
        scriptDangerCmdPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(dangerCmdGrid);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
});