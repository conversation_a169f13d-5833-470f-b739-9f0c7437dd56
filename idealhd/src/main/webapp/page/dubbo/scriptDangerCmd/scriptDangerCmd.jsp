<%@ page import="com.ideal.ieai.core.Environment" %>
<%@page contentType="text/html; charset=utf-8"%>
<% boolean dangerCmdCheckTypeSwitch= Environment.getInstance().getScriptDangerCmdCheckTypeSwitch();
   boolean scriptDangercmdHelpdocSwitch= Environment.getInstance().getScriptDangercmdHelpdocSwitch();
   boolean dangerCmdImportSwitch= Environment.getInstance().getScriptDangerCmdImportSwitch();
%>
<html>
<head>
   <script type="text/javascript">
      var scriptDangerCmdnow="";
      if(isTabSwitch){
         $(document).ready(function(){
            $("#scriptDangerCmd_grid_area").attr('id',$("#scriptDangerCmd_grid_area").attr('id')+scriptDangerCmdnow)
         });
      }
   </script>
<script>
    var dangerCmdCheckTypeSwitch =<%=dangerCmdCheckTypeSwitch%>;
    var scriptDangercmdHelpdocSwitch =<%=scriptDangercmdHelpdocSwitch%>;
    var dangerCmdImportSwitch =<%=dangerCmdImportSwitch%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptDangerCmd/scriptDangerCmd.js"></script>
</head>
<body>
<div id="scriptDangerCmd_grid_area" style="width: 100%;height: 100%">
</div>
</body>
</html>