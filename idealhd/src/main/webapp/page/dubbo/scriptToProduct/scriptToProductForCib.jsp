<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
    boolean sendSwitch = Environment.getInstance().getScriptServiceToProductSwitch();
    boolean cibSwitch = Environment.getInstance().getBankSwitchIscib();
%>
<html>
<head>
<script>
    var sendSwitch = <%=sendSwitch%>;
    var cibSwitch = <%=cibSwitch%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptToProduct/scriptToProductForCib.js"></script>
</head>
<body>
<div id="scriptToProductForCib_area" style="width: 100%;height: 100%">
</div>
</body>
</html>