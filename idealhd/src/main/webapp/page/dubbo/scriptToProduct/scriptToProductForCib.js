Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	var	toProductWin;
	var submitItsmWin;
	var iidArray =[];
	var upldWin;
	var upLoadformPane = '';
	let checkUserPasswordWin;
	var initTime = new Date();
			initTime.setDate(initTime.getDate() );
			initTime.setHours(0);
			initTime.setMinutes(0);
			initTime.setSeconds(0);
	var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
	var istartTime = Ext.create('Go.form.field.DateTime', {
				fieldLabel : '开始时间',
				format : 'Y-m-d H:i:s',
				labelWidth : 80,
				name: 'istartTime',
				labelAlign : 'right',
				width : '20%',
				value : initTime
			});
	var iendTime = Ext.create('Go.form.field.DateTime', {
				fieldLabel : '结束时间',
				format : 'Y-m-d H:i:s',
				labelWidth : 80,
				name: 'iendTime',
				labelAlign : 'right',
				width : '20%'
//				value : initTime
			});
   var iscriptuserQuery = new Ext.form.TextField({
		name: 'iscriptuserQuery',
		fieldLabel: '所属人',
//		emptyText: '',
		labelWidth : 65,
//		padding : '5 0 5 0',
		labelAlign : 'right',
//		value: serviceName+'_'+CurentTime(),
		width: '20%'
	});
//	var istartTime ={
//	        	fieldLabel: '开始时间',
//	        	xtype : 'datefield',
//	            labelAlign : 'right',
//	            labelWidth : 80,
//	            width:'31%',
//	            name: 'istartTime',
//	            format : 'Y-m-d',
//	            value: filter_serviceStartTime =='' ? Ext.util.Format.date(Ext.Date.add(new Date(),Ext.Date.DAY,-6),"Y-m-d"):filter_serviceStartTime,
//        		listeners: {
//		            specialkey: function(field, e){
//		                if (e.getKey() == e.ENTER) {
//		                	scriptflowmonitor_grid.ipage.moveFirst();
//		                }
//		            }
//		        }
//	        }
	Ext.define('iscriptuserModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'ifullname',
	      type : 'string'
	    }, {
	      name : 'iid',
	      type : 'int'
	    }]
	  });

	var iscriptuserStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'iscriptuserModel',
	    params :{start:0},
	    remoteSort : true,
	    proxy: {
	      type: 'ajax',
	      url: 'getUserToScriptProductForCib.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'total'	
	      }
	    }
	  });
	
    var　iscriptuser = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "所属人",
	    labelWidth: 93,
	    afterLabelTextTpl: required,
//	    padding: 5,
	    labelAlign:'right',
	    store: iscriptuserStore,
	    name:"iscriptuser",
	    mode: 'remote',
	    columnWidth:0.98,
	    margin : '10 0 0 0',
	    displayField: 'ifullname',
//	    editable : true,
	    valueField: 'iid',
	    triggerAction :'all',
//	    typeAhead:true,
//	    anyMatch :true,
	    forceSelection:true,
	    pageSize:25,
//	    minChars:2,
	    queryDelay:1000,
	    listeners:{
//	    	'beforequery':function(e){
//	    		var combo = e.combo;
//	    		var store = combo.getStore;
//	    		if(!e.forceAll){	 
//	    			store.getProxy().setExtraParam("start",0);
//	    			store.getProxy().setExtraParam("limit",combo.pageSize);
//	    			store.getProxy().setExtraParam("input",combo.getRawValue());
//	    			combo.expand();
//					return false;
//	    		}
//	    	},
	    	specialkey: function(field,e){
	    		if (e.getKey() == e.ENTER){
	    			iscriptuserStore.load({
	    				params : {
	    					start : 0,
	    					limit : 25,
	    					input:iscriptuser.getValue()
	    				}
	    			});
	    			iscriptuserStore.loadPage(1);
	    			iscriptuser.expand();
	    		}
	    	}
	    }
    });
    
    iscriptuserStore.on('beforeload', function (store, options) {
		var new_params = {
				input:iscriptuser.getValue()
		};
		Ext.apply(iscriptuserStore.proxy.extraParams, new_params);
	});


	var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
		 	region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	baseCls:'customize_gray_back',
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back',
				border : false,
				dock : 'top',
				items: [istartTime, iendTime,iscriptuserQuery,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						var endTimeS = istartTime.getValue();
						var endTimeE = iendTime.getValue();
						if(endTimeS != null && endTimeS!='' && endTimeE != null && endTimeE !='' && endTimeS > endTimeE){
							Ext.Msg.alert('提示', "结束的开始时间不能大于结束的终止时间!");
							return;
						}
						productRecordGrid.ipage.moveFirst();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
					}
				}]
			}]
		});
    Ext.define('productModel', {
	    extend: 'Ext.data.Model',
	    fields : [{
	      name : 'iid',
	      type : 'int'
	    },{
			name : 'iuuid',
			type : 'string'
		},{
	      name : 'fileName',
	      type : 'string'
	    }, {
	      name : 'idatetime',
	      type : 'string'
	    },{
	      name : 'toproductUserName',
	      type : 'string'
	    },/*{
			name : 'iresult',
			type : 'int'
		},*/{
	      name : 'iscriptUserName',
	      type : 'string'
	    },{
	      name : 'idescription',
	      type : 'string'
	    }]
	  });
    var productStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
//		pageSize : 30,
		model : 'productModel',
		proxy : {
			type : 'ajax',
			url : 'getIeaiScriptToproductList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	productStore.on('beforeload', function (store, options) {
	    var new_params = {
	    		starttime: istartTime.getRawValue() !='' ? Date.parse(new Date(istartTime.getRawValue())):'',
	    		endtime:  iendTime.getRawValue()!=''? Date.parse(new Date(iendTime.getRawValue())):'',
	    		iscriptUserName: iscriptuserQuery.getValue()
	    };

	    Ext.apply(productStore.proxy.extraParams, new_params);
    });
    var productionRecordColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		},{
		    text : '文件名',
		    dataIndex : 'fileName',
		    width : 380
		},
		{
			text : 'uuid',
			dataIndex : 'iuuid',
			width : 100,
			hidden:true
		},
		{
			text : 'iid',
		    dataIndex : 'iid',
		    width : 100,
		    hidden:true
		},
		{
		    text : '投产时间',
		    dataIndex : 'idatetime',
		    width : 180
		},
		/*{
			text : '审批状态',
			dataIndex : 'iresult',
			width : 100,
			hidden:!sendSwitch,
			renderer:function(value,p,record,rowIndex){
				var backValue = "";
				if (value == -1) {
					backValue = "未提交";
				} else if (value == 0) {
					backValue = "已通过";
				} else if (value == 1) {
					backValue = "审批拒绝";
				}else if (value == 2) {
					backValue = "审批中";
				}
				return backValue;
			}
		},*/
		{
		    text : '投产人',
		    dataIndex : 'toproductUserName',
		    width : 150
		},
		{
		    text : '所属人',
		    dataIndex : 'iscriptUserName',
		    width : 150
		},{
		    text : '投产说明',
		    dataIndex : 'idescription',
//		    width : 280,
		    flex:1
		}];

	/**
	 * 发起提交itsm前，根据json文件解析成html，只解析一次，后台会判断是否已经解析过，根据文件夹名
	 */
	function createHtmlFile(fileName,productIid,uuid) {

		let msg='';
		Ext.Ajax.request({
			url : 'createScriptHtmlFileForSubmitItsm.do',
			method : 'POST',
			async: false,
			params : {
				fileName : fileName,
				iid :productIid,
				iuuid:uuid
			},
			success : function(response, request) {
				msg = Ext.decode(response.responseText).msg;
			},
			failure : function(result, request) {
				msg = '解析json文件成html文件失败！';
				// Ext.Msg.alert('提示', '解析json文件成html文件失败！');
			}
		});

		return msg;
	}

	let userName = new Ext.form.TextField({
		name: 'userName',
		fieldLabel: '用户名',
		afterLabelTextTpl: required,
		labelAlign:'right',
		emptyText: '--请填写用户名--',
		labelWidth: 93,
		margin : '10 0 0 0',
		maxLength: 255,
		// height: 55,
		columnWidth:.98,
		autoScroll: true
	});

	let passWord = new Ext.form.TextField({
		name: 'passWord',
		fieldLabel: '密码',
		afterLabelTextTpl: required,
		inputType:'password',
		labelAlign:'right',
		emptyText: '--请填写密码--',
		labelWidth: 93,
		margin : '10 0 0 0',
		maxLength: 255,
		// height: 55,
		columnWidth:.98,
		autoScroll: true
	});

	let checkUserPassword_form = Ext.create('Ext.form.Panel', {
		width: 600,
		layout : 'anchor',
		bodyCls : 'x-docked-noborder-top',
		buttonAlign : 'center',
		border : false,
		items: [{
			//	    	layout:'form',
			anchor:'98%',
			padding : '5 0 5 0',
			border : false,
			items: [{
				layout:'column',
				border : false,
				items:[userName]
			},{
				layout:'column',
				border : false,
				items:[passWord]
			}]
		}
		]
	});
	function Base64() {
		// private property
		_keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
		// public method for encoding
		this.encode = function (input) {
			var output = "";
			var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
			var i = 0;
			input = _utf8_encode(input);
			while (i < input.length) {
				chr1 = input.charCodeAt(i++);
				chr2 = input.charCodeAt(i++);
				chr3 = input.charCodeAt(i++);
				enc1 = chr1 >> 2;
				enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
				enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
				enc4 = chr3 & 63;
				if (isNaN(chr2)) {
					enc3 = enc4 = 64;
				} else if (isNaN(chr3)) {
					enc4 = 64;
				}
				output = output +
					_keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
					_keyStr.charAt(enc3) + _keyStr.charAt(enc4);
			}
			return output;
		}
		// public method for decoding
		this.decode = function (input) {
			var output = "";
			var chr1, chr2, chr3;
			var enc1, enc2, enc3, enc4;
			var i = 0;
			input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
			while (i < input.length) {
				enc1 = _keyStr.indexOf(input.charAt(i++));
				enc2 = _keyStr.indexOf(input.charAt(i++));
				enc3 = _keyStr.indexOf(input.charAt(i++));
				enc4 = _keyStr.indexOf(input.charAt(i++));
				chr1 = (enc1 << 2) | (enc2 >> 4);
				chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
				chr3 = ((enc3 & 3) << 6) | enc4;
				output = output + String.fromCharCode(chr1);
				if (enc3 != 64) {
					output = output + String.fromCharCode(chr2);
				}
				if (enc4 != 64) {
					output = output + String.fromCharCode(chr3);
				}
			}
			output = _utf8_decode(output);
			return output;
		}
		// private method for UTF-8 encoding
		_utf8_encode = function (string) {
			string = string.replace(/\r\n/g,"\n");
			var utftext = "";
			for (var n = 0; n < string.length; n++) {
				var c = string.charCodeAt(n);
				if (c < 128) {
					utftext += String.fromCharCode(c);
				} else if((c > 127) && (c < 2048)) {
					utftext += String.fromCharCode((c >> 6) | 192);
					utftext += String.fromCharCode((c & 63) | 128);
				} else {
					utftext += String.fromCharCode((c >> 12) | 224);
					utftext += String.fromCharCode(((c >> 6) & 63) | 128);
					utftext += String.fromCharCode((c & 63) | 128);
				}
			}
			return utftext;
		}
		// private method for UTF-8 decoding
		_utf8_decode = function (utftext) {
			var string = "";
			var i = 0;
			var c = c1 = c2 = 0;
			while ( i < utftext.length ) {
				c = utftext.charCodeAt(i);
				if (c < 128) {
					string += String.fromCharCode(c);
					i++;
				} else if((c > 191) && (c < 224)) {
					c2 = utftext.charCodeAt(i+1);
					string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
					i += 2;
				} else {
					c2 = utftext.charCodeAt(i+1);
					c3 = utftext.charCodeAt(i+2);
					string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
					i += 3;
				}
			}
			return string;
		}
	}

	function checkUserPassword() {
		if (!checkUserPasswordWin) {
			checkUserPasswordWin = Ext.create('widget.window', {
				title: '验证用户',
				closable: true,
				closeAction: 'hide',
				modal: true,
				width: 600,
				minWidth: 350,
				height: 220,
				layout: {
					type: 'border',
					padding: 5
				},
				items: [checkUserPassword_form],
				dockedItems : [{
					xtype : 'toolbar',
//				  	 	baseCls:'customize_gray_back',
					dock : 'bottom',
					layout: {pack: 'center'},
					items : [{
						xtype: "button",
						cls:'Common_Btn',
						text: "提交",
						handler: function (){
							if(userName.getValue()==='' || userName.getValue()==null || userName.getValue() === undefined){
								Ext.Msg.alert('提示',"请输入用户名!");
								return;
							}
							if(passWord.getValue()==='' || passWord.getValue()==null || passWord.getValue() === undefined){
								Ext.Msg.alert('提示',"请输入密码!");
								return;
							}

							let base64_p1 =new Base64();
							let passWordBase64 =base64_p1.encode(passWord.getValue());
							Ext.Ajax.request({
								url : 'validateUserInfo.do',
								method : 'POST',
								async: false,
								params : {
									loginName:userName.getValue(),
									passWord:passWordBase64
								},
								success : function(response, request) {
									let success = Ext.decode(response.responseText).success;
									let message = Ext.decode(response.responseText).message;
									if(!success){
										Ext.Msg.alert('提示', message);
									}else{
										checkUserPasswordWin.close();
										importProduction();
									}
								},
								failure : function(result, request) {
									Ext.Msg.alert('提示', '验证用户失败！');
								}
							});
						}
					}
					]
				}],
				listeners:{
					'close':function(){
						checkUserPassword_form.getForm().reset();
					}
				}
			});

		}
		checkUserPasswordWin.show();
	}

	var productRecordGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
			region: 'center',
			autoScroll: true,
		    store : productStore,
		    cls:'customize_panel_back',
		    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    border:false,
		    columnLines : true,
			padding : grid_space,
		    columns : productionRecordColumns,
		    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true, mode: "SINGLE"}),
		     listeners: {
		        select: function( e, record, index, eOpts ){
		        	if(iidArray.indexOf(record.get('iid'))==-1) {
	            		iidArray.push(record.get('iid'));
	            	}
		        },
		         deselect: function( e, record, index, eOpts ){
	            	if(iidArray.indexOf(record.get('iid'))>-1) {
	            		iidArray.remove(record.get('iid'));
	            	}
	            }
		     },
		    dockedItems : [{xtype : 'toolbar',
			    	items : ['->',{
						text: '提交ITSM',
						hidden:!sendSwitch,
						cls : 'Common_Btn',
						handler: function() {
							let productData = productRecordGrid.getSelectionModel().getSelection();
							if (productData.length===0){
								Ext.Msg.alert('提示', "请选择一条记录进行提交！");
								return;
							}
							let fileName=productData[0].data.fileName;
							let productIid =productData[0].data.iid;
							let uuid =productData[0].data.iuuid;
							let msg = createHtmlFile(fileName,productIid,uuid);
							if (msg !== ''){
								Ext.Msg.alert('提示', msg);
								return;
							}else{
								submitItsm(fileName,productIid,uuid);
							}
						}
					},{
						text: '投产导入',
						cls : 'Common_Btn',
						handler: function() {
							//光大校验用户名密码，通过后才继续下一步操作
							/*if (cibSwitch) {
							    checkUserPassword();
							}else{
							}*/
							importProduction();

						}
			    	}]
		    	}]
		});
	function clearQueryWhere(){
		  search_form.getForm().findField("istartTime").setValue(initTime);
		  search_form.getForm().findField("iendTime").setValue('');
		  search_form.getForm().findField("iscriptuserQuery").setValue('');
	}
	var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '投产说明',
        afterLabelTextTpl: required,
        labelAlign:'right',
        emptyText: '',
        labelWidth: 93,
        margin : '10 0 0 0',
        maxLength: 255,
        height: 55,
        columnWidth:.98,
        autoScroll: true
    });

	var taskName = new Ext.form.TextField({
		name: 'taskName',
		fieldLabel: '标题',
		emptyText: '--请填写标题--',
		labelWidth: 93,
		margin : '10 0 0 0',
		afterLabelTextTpl: required,
		allowBlank:false, //不允许为空
		blankText:"不能为空",
		labelAlign: 'right',
		columnWidth:.5
	});

	// var  approverUser = new Ext.form.TextField({
	// 	name: 'approverUser',
	// 	fieldLabel: '审批人',
	// 	emptyText: '--请填写审批人--',
	// 	labelWidth: 93,
	// 	margin : '10 0 0 0',
	// 	afterLabelTextTpl: required,
	// 	allowBlank:false, //不允许为空
	// 	blankText:"不能为空",
	// 	labelAlign: 'right',
	// 	width: '50%'
	// });
	var approverStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		storeId : 'approver_store',
		fields : [ 'iid', 'iname' ],
		proxy : {
			type : 'ajax',
			url : 'getItsmapprover.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	approverStore.on('beforeload', function (store, options) {
		var new_params = {
			isFlag:1
		};
		Ext.apply(approverStore.proxy.extraParams, new_params);
	});
	var urgencyStore = Ext.create('Ext.data.Store', {
		fields: ['iid', 'name'],
		data : [
			{"iid":"0", "name":"特急"},
			{"iid":"1", "name":"高"},
			{"iid":"2", "name":"中"},
			{"iid":"3", "name":"低"}
		]
	});
	var urgencyComboBox = Ext.create('Ext.form.field.ComboBox', {
		fieldLabel: '紧急程度',
		labelWidth : 93,
		name: 'urgency',
		margin : '10 0 0 0',
		labelAlign : 'right',
		allowBlank:false, //不允许为空
		blankText:"不能为空",
		displayField: 'name',
		valueField: 'name',
		afterLabelTextTpl: required,
		store: urgencyStore,
		queryMode: 'local',
		editable:false,
		columnWidth:.5,
		// width: '50%',
		listeners: {
			afterRender: function(combo) {
				var firstValue = urgencyStore.getAt(1).get("name");
				combo.setValue(firstValue);//同时下拉框会将与name为firstValue值对应的 text显示
			}
		}
	});
	var approverUser = Ext.create('Ext.form.field.ComboBox', {
		name : 'approver',
		store : approverStore,
		fieldLabel: '审批人',
		labelWidth: 93,
		  columnWidth:.5,
		afterLabelTextTpl: required,
		// width: '45%',
		labelAlign: 'right',
		allowBlank:false, //不允许为空
		blankText:"不能为空",
		margin : '10 0 0 0',
		displayField : 'iname',
		valueField : 'iname',
		listConfig : {
			maxHeight : 200
		},
		editable : true,
		listeners : {
			select : function() {
			},
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = e.query;
					combo.store.filterBy(function(record, id) {
						var text = record.get(combo.displayField);
						return (text.toLowerCase().indexOf(
							value.toLowerCase()) != -1);
					});
					combo.expand();
					return false;
				}
			}
		}
	});

	var flagStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"1", "name":"否"},
			{"id":"0", "name":"是"}
		]
	});
	var stateCombo = Ext.create('Ext.form.field.ComboBox', {
		name : 'stateCombo',
		// padding : '5',
		labelWidth : 93,
		queryMode : 'local',
		fieldLabel : '应急处置',
		displayField : 'name',
		valueField : 'id',
		margin : '10 0 0 0',
		afterLabelTextTpl: required,
		allowBlank:false, //不允许为空
		blankText:"不能为空",
		editable : false,
		emptyText : '--请选择状态--',
		store : flagStore,
		columnWidth:.5,
		// width: '50%',
		// value:"1",
		labelAlign : 'right',
		listeners: {
			change : function(combo, records, eOpts) {
				approverUser.setValue('');
				var isFlag = this.getValue();
				if(this.value != null && this.value != ""){
					approverStore.load({
						params: {
							isFlag : isFlag
						}
					});
				}
			},
			afterRender: function(combo) {
				var firstValue = flagStore.getAt(0).get("name");
				var id = flagStore.getAt(0).get("id");
				combo.setRawValue(firstValue);//同时下拉框会将与name为firstValue值对应的 text显示
				combo.setValue(id);
			}
		}
	});


	// var  vresionInfo = new Ext.form.TextField({
	// 	name: 'vresionInfo',
	// 	fieldLabel: '版本信息',
	// 	readOnly:true,
	// 	labelWidth: 134,
	// 	margin : '10 0 0 0',
	// 	labelAlign: 'right',
	// 	width: '50%'
	// });

	var taskDesc = Ext.create('Ext.form.field.TextArea', {
		name: 'taskDesc',
		fieldLabel: '申请理由',
 		labelAlign:'right',
		emptyText: '',
		labelWidth: 93,
		margin : '10 0 0 0',
		maxLength: 255,
		height: 55,
		afterLabelTextTpl: required,
		allowBlank:false, //不允许为空
		blankText:"不能为空",
		columnWidth:1,
		autoScroll: true
	});

	var handlingOpinions = Ext.create('Ext.form.field.TextArea', {
		name: 'handlingOpinions',
		fieldLabel: '审批意见',
		afterLabelTextTpl: required,
		allowBlank:false, //不允许为空
		blankText:"不能为空",
		labelAlign:'right',
		emptyText: '',
 		labelWidth: 93,
		margin : '10 0 0 0',
		maxLength: 255,
		height: 55,
		columnWidth:1,
		autoScroll: true
	});


	var submitItsmWin_form = Ext.create('Ext.ux.ideal.form.Panel', {
		width: 600,
		layout : 'anchor',
		bodyCls : 'x-docked-noborder-top',
		buttonAlign : 'center',
		border : false,
		items: [{
			//	    	layout:'form',
			anchor:'98%',
			padding : '5 0 5 0',
			border : false,
			items: [{
				layout:'column',
				border : false,
				items:[taskName,approverUser]//标题 审批人
			},{
				layout:'column',
				border : false,
				items:[stateCombo,urgencyComboBox]//是否应急
			},{
				layout:'column',
				border : false,
				items:[taskDesc]//申请理由
			},{
				layout:'column',
				border : false,
				items:[handlingOpinions]//处理意见
			}]
		}
		]
	});

	var toProductWin_form = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	bodyCls : 'x-docked-noborder-top',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
	//	    	layout:'form',
		    	anchor:'98%',
		    	padding : '5 0 5 0',
		    	border : false,
		    	items: [{
			    		layout:'column',
				    	border : false,
			    		items:[iscriptuser]//所属人
			    	},{
			    		layout:'column',
				    	border : false,
				    	items:[pubDesc_sm]//说明
			    	}]
		    	}
	    	]
	    });

    function importButton(){
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}

		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:120,
		    frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
					labelAlign:'right',
					fieldLabel: '选择文件',
					labelWidth: 80,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: ['->',
					{
						id:'upldBtnIdAudi',
						text: '导入文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("files").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}

//			    			var hdtmpFilNam=form.findField("file").getValue();
//			    			if(!checkFile(hdtmpFilNam)){
//				    			  form.findField("file").setRawValue('');
//				    			  return;
//				    		}

							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'saveIeaiScriptToproduct.do',
									params:{
										iscriptuserid:iscriptuser.getValue(),
										idescription:pubDesc_sm.getValue()
				                	},
								    success: function(form, action) {
								       var msg = Ext.decode(action.response.responseText).message;
//								       var success = Ext.decode(action.response.responseText).success;
								      Ext.Msg.alert('提示',msg);
								       upldWin.close();
								       toProductWin.close();
								       productRecordGrid.ipage.moveFirst();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '投产介质文件导入',
		    width: 400,
		    height: 200,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});

		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
    }



	/*
	 兴业银行 提交itsm方法
	 */
	function submitItsm(fileName,productIid,uuid){
		taskName.setValue(fileName);
		if (!submitItsmWin) {
			submitItsmWin = Ext.create('widget.window', {
				title: '提交ITSM',
				closable: true,
				closeAction: 'hide',
				modal: true,
				width: 680,
				minWidth: 350,
				height: 400,
				layout: {
					type: 'border',
					padding: 5
				},
				items: [submitItsmWin_form],
				dockedItems : [{
					xtype : 'toolbar',
//				  	 	baseCls:'customize_gray_back',
					dock : 'bottom',
					layout: {pack: 'center'},
					items : [{
						xtype: "button",
						cls:'Common_Btn',
						text: "提交",
						handler: function (){
							if (!submitItsmWin_form.getForm().isValid()){
								Ext.Msg.alert('提示',"请输入必填项!!");
								return;
							}
							var taskNameValue=taskName.getValue();
							var taskDescValue = taskDesc.getValue();
							var approverUserValue = approverUser.getValue();
							var stateValue = stateCombo.getValue();
							var urgencyValue = urgencyComboBox.getValue();
							var handlingOpinionsValue = handlingOpinions.getValue();

							// let productData = productRecordGrid.getSelectionModel().getSelection();
							//
							// let fileName=productData[0].data.fileName;
							// let productIid =productData[0].data.iid;

							Ext.Ajax.request({
								url : 'scriptToProductSubmitToItsm.do',
								method : 'POST',
								async: false,
								params : {
									iid:productIid,
									itaskid:uuid,
									fileName:fileName,
									itaskname : taskNameValue,
									itaskdesc :taskDescValue,//申请理由
									iapprover:approverUserValue,
									isflag:stateValue,//应急处置
									iappidea:handlingOpinionsValue,//审批意见
									iurgency:urgencyValue,//紧急程度,
									iapprovestep:'提交'
								},
								success : function(response, request) {
									let msg = Ext.decode(response.responseText).msg;
									Ext.Msg.alert('提示', msg);
									productStore.reload();
									submitItsmWin.close();
									return;
								},
								failure : function(result, request) {
									Ext.Msg.alert('提示', '提交itsm失败！');
									return;
								}
							});
						}
					}
					]
				}],
				listeners:{
					'close':function(){
						submitItsmWin_form.getForm().reset();
						stateCombo.setValue("1");
						urgencyComboBox.setValue("高");
					}
				}
			});

		}
		submitItsmWin.show();
	}



	function importProduction(){
	if (!toProductWin) {
		  	 toProductWin = Ext.create('widget.window', {
	                title: '确认投产信息',
	                closable: true,
	                closeAction: 'hide',
	                modal: true,
	                width: 630,
	                minWidth: 350,
	                height: 250,
	                layout: {
	                    type: 'border',
	                    padding: 5
	                },
	                items: [toProductWin_form],
	                dockedItems : [{
						xtype : 'toolbar',
//				  	 	baseCls:'customize_gray_back',
						dock : 'bottom',
						layout: {pack: 'center'},
						items : [{
				  			xtype: "button",
				  			cls:'Common_Btn',
				  			text: "选择文件",
				  			handler: function (){
				  				if(iscriptuser.getValue()=='' || iscriptuser.getValue()==null || iscriptuser.getValue() == undefined){
				  					Ext.Msg.alert('提示',"请选择所属人!");
				  					return;
				  				}
				  				if(pubDesc_sm.getValue()=='' || pubDesc_sm.getValue()==null || pubDesc_sm.getValue() == undefined){
				  					Ext.Msg.alert('提示',"请输入投产说明!");
				  					return;
				  				}
								importButton();
					        }
			  			}
			  		]
				}],
				listeners:{
						'close':function(){
							toProductWin_form.getForm().reset();
						}
					}
	            });

	        }
		  toProductWin.show();
	}

	var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "scriptToProductForCib_area",
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				border : false,
				layout : 'border',
				items : [search_form, productRecordGrid]
			});

	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
		});
	
	
	
	
	
});