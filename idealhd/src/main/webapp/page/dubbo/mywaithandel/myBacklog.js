//------------------------part0: Declare vars and objs-----------------------------
/**@desc  页面加载数据总函数*/
var queryUnFinished=1;
var queryFinished=2;
var gropQuery=""; 
var gropQuery2="";
var tcwcqkQuery="";
/**
 *   @desc   “未完成页面”的分页对象
 * **/
var unFinishedPageObj={
		start:0,
		limit:50,
		total:0,
		pageNum:1,
		dataList:[],
		pageUp:function (){   		// 上一页
			try{
				this.start=this.start-this.limit;
				if(this.start<0){
					this.start=0;
				}
				this.load();
			}catch(e){
				console.log(e)
			}
		},
		pageDown:function (){	   // 下一页
			try{
				if(this.start+this.limit<this.total){//不是最后一页才能往后翻
					this.start=this.start+this.limit;
					this.load();					
				}else{
				    window.wxc.xcConfirm("已经是最后一页了",{title:"消息提示"});
				}
			}catch(e){
				console.log(e)
			}
		},
		load:function(){
//			load_unFinished_grid();
			load_unFinished_grid_noGrouping();
		},
		toSting:function(){
			var ss='start'+this.start +',limit:'+this.limit+',total:'+this.total+',pageNum:'+this.pageNum+'dataList.length:'+this.dataList.length;
			window.wxc.xcConfirm(ss,{title:"消息提示"});
		}
		
}; 

/**
 * @desc “已完成页面”的分页对象
 * **/
var FinishedPageObj     ={
		start:0,
		limit:50,
		total:0,
		pageNum:1,
		dataList:[],
		pageUp:function (){   		// 上一页
			try{
				this.start=this.start-this.limit;
				if(this.start<0){
					this.start=0;
				}
				this.load();
			}catch(e){
				console.log(e)
			}
		},
		pageDown:function (){	   // 下一页
			try{
				if(this.start+this.limit<this.total){
					this.start=this.start+this.limit;
					this.load();					
				}else{
					window.wxc.xcConfirm('已经是最后一页了',{title:"消息提示"});
				}
			}catch(e){
				console.log(e)
			}
		},
		load:function(){
//			load_Finished_grid();
			load_Finished_grid_noGrouping();
		},
		toSting:function(){
			var ss='start'+this.start +',limit:'+this.limit+',total:'+this.total+',pageNum:'+this.pageNum+'dataList.length:'+this.dataList.length;
			window.wxc.xcConfirm(ss,{title:"消息提示"});
		}
		
};

//----------------------part0: When page loading ,the conponents  add listeners.-----------------------------
$(document).ready(function(){
	//下拉选_"未完成"表格的页码切换值时
	$('#page_number_unfininehed').on('change', function() {
		unFinishedPageObj.start=0;
		unFinishedPageObj.limit=parseInt(this.value);
		unFinishedPageObj.load();
	});
	
	//下拉选_"已完成"表格的页码切换值时
	$('#page_number_fininehed').on('change', function() {
		FinishedPageObj.start=0;
		FinishedPageObj.limit=parseInt(this.value);
		FinishedPageObj.load();
	});
	
	
	//-----------------责任组1-----------------------
	$('#myBacklog_grop_1').on('change', function() {
		FinishedPageObj.start=0;
		var unFGroup=$("#myBacklog_grop_1").val();
		if(null!=unFGroup&& ''!=unFGroup){
			gropQuery=unFGroup
		}else{
			if(managerFlag=="1"){
				gropQuery='';
			}else{				
				gropQuery=myGroup;
			}
		}
		unFinishedPageObj.load();
	});
	
	//-----------------责任组2-----------------------
	$('#myBacklog_grop_2').on('change', function() {
		FinishedPageObj.start=0;
		var unFGroup=$("#myBacklog_grop_2").val();
		if(null!=unFGroup&& ''!=unFGroup){
			gropQuery2=unFGroup
		}else{
			if(managerFlag=="1"){
				gropQuery2='';
			}else{				
				gropQuery2=myGroup;
			}
		}
		FinishedPageObj.load();
	});
	
	$('#myBacklog_tcwcqkSel').on('change', function() {
		FinishedPageObj.start=0;
		tcwcqkQuery=$("#myBacklog_tcwcqkSel").val();
		FinishedPageObj.load();
	});
});
//----------------------part1:加载数据-----------------------------
/**
 * @desc 页面加载后，自动调用的第一个函数,为表格和饼状体加载数据
 * **/
function load(){	
	try{
		if(managerFlag=="1"){	//值班经理不用填组别，直接查全部
			gropQuery2='';
		}else{				
			gropQuery=myGroup;	//个人查自己组
			gropQuery2=myGroup;
		}
		
		getWindowData();
		getUserHour();
		//load_unFinished_grid();
		load_unFinished_grid_noGrouping();
		//load_Finished_grid();
		load_Finished_grid_noGrouping();
		load_pie_char();
		getScheuleGrop();
	}catch(e){
		console.log(e)
	}
}
/**获取投产窗口时间**/
function getWindowData()
{
$.getJSON('getPieDataAll.do',  {queryWindowOnly:1}, function(res){
 /**投产窗口显示**/
 var windowNameString=res.windowNameString;
	if(null!= windowNameString&&''!=windowNameString){
		//$('#windowspan').html(windowNameString);
		//判断投产窗口时间是否过长，过长的话就去掉秒
		var bt1=res.beginTimeString;
		var bt2=res.endTimeString;
		if (res.beginTimeString.length > 15)
		{
			bt1 = res.beginTimeString.substring (0, 16);
		}
		if (res.endTimeString.length > 15)
		{
			bt2 = res.endTimeString.substring (0, 16);
		}
		
		$('#windowspan').html(bt1+"~"+bt2);
	}else
		{
		  $('#windowspan').html('无投产窗口数据');
		}
});
}
/**@desc 加载_未完成工单数据*/
function load_unFinished_grid(){
	try{
		var tableHtml="";
			$.ajax({
				type:"post",
				url: path+"/getBacklogUnFinished.do",
				data : {
				    start:unFinishedPageObj.start,
				    limit:unFinishedPageObj.limit,
					managerFlag: managerFlag,
					igroupTop : gropQuery,
					dataType:queryUnFinished,
					orderString:orderString,
					orderColumn:orderColumn
				},
				async:true,
				dataType:"json",
				success: function(jsonData){
					var dataList = jsonData.dataList;
					var total=jsonData.total;
					
					//Update new result to the page obj's properties.
					unFinishedPageObj.dataList=dataList;
					unFinishedPageObj.total=total;
					//unFinishedPageObj.toSting();
					
					var sysListHTML = "";
					var jsonArr=null;
					var bean=null;
					var idx_x=0;
					var rowNum=unFinishedPageObj.start+1;
					for(var i=0;i<dataList.length;i++){				//循环生成组 001
						jsonArr=dataList[i].dataList;
						 tableHtml      += '<div class="extend_area" >'+
									  						'<div class="expand extend_pos"  id="et_'+i+'" ></div>'+
									  						'<span class="extend_text" id="extend_text_'+i+'" onclick="unfinishedSpanClick(this)" name="'+jsonArr.length+'" >变更起始时间:('+dataList[i].igroup+')</span>'+
										             '</div>';
						for(var j =0;j<jsonArr.length;j++){		    //循环生成bean 002
							bean=jsonArr[j];
							tableHtml      += '<table cellpadding="0" cellspacing="0" border="0" class="second_border" id="extend_text_'  +i+ '_' +j+'" >'+
//							'<tr  onclick=tr_changeSelected(this)>'+
							'<tr class="table_selection" >'+
							'<td width="3%">'+rowNum+'</td>'+
							//'<td><div class="w_order_td">'+bean.ichgchrreleaseId+'</div></td>'+      //工单
							'<td><div class="w_order_td2">'+bean.iapplicationname+'</div></td>'+	//业务系统
							'<td><div class="w_order_td2">'+bean.ichgchrreleaseId+'</div></td>'+    //工单号
							'<td width="15%"><span class="implement_stauts stauts_common">'+bean.itcwcqk+'</span></td><!-- implement_stauts实施中  details_stauts投产成功-->'+
							'<td width="15%">'+bean.iexecutive+'</td>'+
							'<td width="15%">'+bean.ioperator+'</td>'+
							'<td width="12%">'+bean.ijhkstime+'</td>'+
							'<td width="12%">'+bean.ijhjstime+'</td>'+
							'<td width="5%"><a href="javascript:void(0);" style="text-decoration:underline;" onclick="openSeqInsQuery(\''+bean.ichgchrreleaseId+'\')"><span class="time_ic ic_pos"></span></a></td>'+
					/*		'<td width="5%"><span class="details_stauts stauts_common">详情</span></td>'+*/
							/*'<td width="5%" '+strIsHidden+'><span class="details_stauts stauts_common" onclick="openWin(\''+bean.ichgchrreleaseId +'\'' +', '+'\'' + bean.iexecutive+'\')">转派</span></td>'+*/
/*							'<td width="5%"><span class="time_ic ic_pos"></span></td>'+
							'<td width="5%"><span class="time_ic ic_pos2"></span></td>'+*/
							'</tr>'+
							'</table>';
							rowNum++;
						}
						idx_x++
					}
					$(".pending_scroll").html(tableHtml);
					//div重新加载数据后，该div滚动条线上滚动1000个像素
					try{						
						if(!$(".pending_scroll").is(":animated")){
							$(".pending_scroll").animate({ scrollTop  : "-=1000" } , 400);
						}
					}catch(e){
						window.wxc.xcConfirm(e,{title:"消息提示"});
					}
				}
			});			
	}catch(e){
		console.log(e)
	}
}
/**@desc 加载_未完成工单数据_无分组*/
function load_unFinished_grid_noGrouping(){
	try{
		var tableHtml="";
			$.ajax({
				type:"post",
				url: path+"/getBacklogUnFinished.do",
				data : {
				    start:unFinishedPageObj.start,
				    limit:unFinishedPageObj.limit,
					managerFlag: managerFlag,
					igroupTop : gropQuery,
					dataType:queryUnFinished,
					orderString:orderString,
					orderColumn:orderColumn
				},
				async:true,
				dataType:"json",
				success: function(jsonData){
					var dataList = jsonData.dataList;
					var dataListOld = jsonData.dataListOld;
					var total=jsonData.total;
					
					//Update new result to the page obj's properties.
					unFinishedPageObj.dataList=dataList;
					unFinishedPageObj.total=total;
					//unFinishedPageObj.toSting();
					$("#page_pre_unfininehed_count").html('总计：'+total);
					var sysListHTML = "";
					var jsonArr=null;
					var bean=null;
					var idx_x=0;
					var rowNum=unFinishedPageObj.start+1;
//					for(var i=0;i<dataListOld.length;i++){				//循环生成组 001
//						jsonArr=dataListOld[i].dataList;
//						 tableHtml      += '<div class="extend_area" >'+
//									  						'<div class="expand extend_pos"  id="et_'+i+'" ></div>'+
//									  						'<span class="extend_text" id="extend_text_'+i+'" onclick="unfinishedSpanClick(this)" name="'+jsonArr.length+'" >变更起始时间:('+dataList[i].igroup+')</span>'+
//										             '</div>';
//					 console.log(dataListOld);
						for(var j =0;j<dataListOld.length;j++){		    //循环生成bean 002
							bean=dataListOld[j];
							//前置任务状态显示
							var relyStateStringCss="";
							if(bean.relyStateString=='未运行')
								{
								relyStateStringCss="details_stauts stauts_common";
								}else if(bean.relyStateString=='运行中')
								{
								relyStateStringCss="details_stauts stauts_common";
								}else if(bean.relyStateString=='已完成')
								{
								relyStateStringCss="completed_stauts stauts_common";
								}else if(bean.relyStateString=='待执行')
								{
								relyStateStringCss="wait_stauts stauts_common";
								}else if(bean.relyStateString=='')
								{
								relyStateStringCss="details_stauts stauts_common";
								}
							//后置任务状态显示
							var triStateStringCss="";
							if(bean.triStateString=='未运行')
								{
								triStateStringCss="details_stauts stauts_common";
								}else if(bean.triStateString=='运行中')
								{
									triStateStringCss="details_stauts stauts_common";
								}else if(bean.triStateString=='已完成')
								{
									triStateStringCss="completed_stauts stauts_common";
								}else if(bean.triStateString=='待执行')
								{
									triStateStringCss="wait_stauts stauts_common";
								}else if(bean.triStateString=='')
								{
									triStateStringCss="details_stauts stauts_common";
									}
							//时序状态按钮
							var selfStateStringCss="";
							var onclickFuntionStr="";
							var selfButtonText="";
							if(bean.selfStateString=='未运行')
								{
								selfStateStringCss="execute_btn stauts_common";
								onclickFuntionStr='onclick="openSeqInsQuery2(\''+bean.ichgchrreleaseId+'\',\''+bean.relyStateString+'\')"';
								selfButtonText="确认执行";
	                    		
								}else if(bean.selfStateString=='运行中')
								{
									selfStateStringCss="firm_complete__btn stauts_common";
									onclickFuntionStr='onclick="openSeqInsQuery2(\''+bean.ichgchrreleaseId+'\',\''+bean.relyStateString+'\')"';
									selfButtonText="确认完成";
		                    		
								}else if(bean.selfStateString=='已完成')
								{
									selfStateStringCss="complete_fail_btn stauts_common";
									selfButtonText="确认完成";
								}else if(bean.selfStateString=='待执行')
								{
									selfStateStringCss="execute_btn stauts_common";
									onclickFuntionStr='onclick="openSeqInsQuery2(\''+bean.ichgchrreleaseId+'\',\''+bean.relyStateString+'\')"';
									selfButtonText="确认执行";
		                    		
								}else if(bean.selfStateString=='1')
								{
									//无关联时序确认开始
									selfStateStringCss="execute_btn stauts_common";
									onclickFuntionStr='onclick="confirmRunSequentialInfoFunAlone(\''+bean.ichgchrreleaseId+'\',1)"';
									selfButtonText="确认开始";
								}else if(bean.selfStateString=='2')
								{
									//无关联时序确认结束
									selfStateStringCss="firm_complete__btn stauts_common";
									onclickFuntionStr='onclick="confirmRunSequentialInfoFunAlone(\''+bean.ichgchrreleaseId+'\',2)"';
									selfButtonText="确认完成";
								}else if(bean.selfStateString=='3')
								{
									//无关联时序确认完毕
									selfStateStringCss="complete_fail_btn stauts_common";
									onclickFuntionStr='';
									selfButtonText="确认完成";
								}
							//弹前置任务列表窗口
							var onclickFuntion3Str='onclick="openSeqInsQuery3(\''+bean.ichgchrreleaseId+'\')"';
							if(bean.relyStateString=='')
								{
								onclickFuntion3Str="";
								}
							//弹后置任务列表窗口
							var onclickFuntion4Str='onclick="openSeqInsQuery4(\''+bean.ichgchrreleaseId+'\')"';
							if(bean.triStateString=='')
								{
								onclickFuntion4Str="";
								}
							
									
									/*var ywFlag=bean.ywFlag;
									var csFlag=bean.csFlag;
									//超时或者延误，一行变红
									var trCssStr="table_selection";
									if(ywFlag=='1'||csFlag=='1')
									{
										trCssStr="table_alarm";
									}
									//超时或者延误转化为汉字
									if(ywFlag=='1')
										{
										ywFlag='是';
										}else
										{
										ywFlag='否';
										}
									
									if(csFlag=='1')
										{
										csFlag='是';
										}else
										{
										csFlag='否';
										}*/
									
									var warnFlag=bean.warnFlag;
									//超时或者延误，一行变红
									var trCssStr="table_selection";
									var changeRedStr="";
									if(warnFlag=='1')
									{
										//trCssStr="table_alarm";
										changeRedStr='style="color:red" title="该时序实际开始时间和实际结束时间不在工单预计开始时间和结束时间范围内"';
									}
									
									var forwordDTStr=' onclick="forwordDT(\''+ dtloginUUID + '\',\''+ bean.ichgchrreleaseId+ '\')"';
									
									
							tableHtml      += '<table cellpadding="0" cellspacing="0" border="0" class="second_border" id="extend_text_'  +0+ '_' +j+'" >'+
//							'<tr  onclick=tr_changeSelected(this)>'+
							'<tr class="'+trCssStr+'" >'+
							'<td width="3%">'+rowNum+'</td>'+
							//'<td><div class="w_order_td">'+bean.ichgchrreleaseId+'</div></td>'+      //工单
							'<td width="10%"><div class="w_order_td2" title="'+bean.iapplicationname+'">'+bean.iapplicationname+'</div></td>'+	//业务系统
							'<td width="10%"><div class="w_order_td2" title="'+bean.idetails+'"><span '+forwordDTStr+' class="approval_hover">'+bean.ichgchrreleaseId+'</span></div></td>'+    //工单号
							'<td width="8%"><span class="details_stauts stauts_common" title="单击可跳转至运维管理平台" '+forwordDTStr+'>'+bean.itcwcqk+'</span></td>'+
							'<td width="10%"><span class="'+relyStateStringCss+'" title="单击查看前置任务" '+onclickFuntion3Str+' >'+(bean.relyStateString==''?"未关联":bean.relyStateString)+'</span></td>'+
							'<td width="8%"><span class="'+selfStateStringCss+'"  '+onclickFuntionStr+' >'+(selfButtonText==''?"未关联":selfButtonText)+'</span></td>'+
							'<td width="8%">'+bean.iexecutive+'</td>'+
							'<td width="8%">'+bean.ioperator+'</td>'+
							'<td width="12%"><span '+changeRedStr+'>'+bean.ijhkstime+'</span></td>'+
							'<td width="12%"><span '+changeRedStr+'>'+bean.ijhjstime+'</span></td>'+
							'<td width="8%"><span class="'+triStateStringCss+'" title="单击查看后续任务"  '+onclickFuntion4Str+' >'+(bean.triStateString==''?"未关联":bean.triStateString)+'</span></td>'+
							/*'<td width="5%"><a href="javascript:void(0);" style="text-decoration:underline;" onclick="openSeqInsQuery(\''+bean.ichgchrreleaseId+'\')"><span class="time_ic ic_pos"></span></a></td>'+
							'<td width="5%"><span class="details_stauts stauts_common">详情</span></td>'+*/
							/*'<td width="5%" '+strIsHidden+'><span class="details_stauts stauts_common" onclick="openWin(\''+bean.ichgchrreleaseId +'\'' +', '+'\'' + bean.iexecutive+'\')">转派</span></td>'+*/
/*							'<td width="5%"><span class="time_ic ic_pos"></span></td>'+
							'<td width="5%"><span class="time_ic ic_pos2"></span></td>'+*/
							//'<td width="5%">'+ywFlag+'</td>'+
							//'<td width="5%">'+csFlag+'</td>'+
							'</tr>'+
							'</table>';
							rowNum++;
						}
//						idx_x++
					//}
					$(".pending_scroll").html(tableHtml);
					$(".pending_scroll").niceScroll({
						autohidemode : true,
						cursorcolor : "#cccccc",
						cursorborder : "0px solid #cccccc"
					});
					//div重新加载数据后，该div滚动条线上滚动1000个像素
					try{						
						if(!$(".pending_scroll").is(":animated")){
							$(".pending_scroll").animate({ scrollTop  : "-=1000" } , 400);
						}
					}catch(e){
						window.wxc.xcConfirm(e,{title:"消息提示"});
					}
				}
				
			});			
	}catch(e){
		console.log(e)
	}
}
/**@desc 加载_完成工单数据*/
function load_Finished_grid(){
	try{
		var tableHtml="";
			$.ajax({
				type:"post",
				url: path+"/getBacklogUnFinished.do",
				data : {
				    start:FinishedPageObj.start,
				    limit:FinishedPageObj.limit,
					managerFlag: managerFlag,
					igroupTop : gropQuery2,
					itcwcqk    :tcwcqkQuery,
					dataType:queryFinished
				},
				async:true,
				dataType:"json",
				success: function(jsonData){
					var dataList = jsonData.dataList;
					var total=jsonData.total;
					
					//Update new result to the page obj's properties.
					FinishedPageObj.dataList=dataList;
					FinishedPageObj.total=total;
					
					var sysListHTML = "";
					var jsonArr=null;
					var bean=null;
					var rowNum=FinishedPageObj.start+1;
					for(var i=0;i<dataList.length;i++){				//循环生成组 001
						jsonArr=dataList[i].dataList;
						 tableHtml      += '<div class="extend_area">'+
								  						'<div class="expand extend_pos" id="ft_'+i+'" ></div>'+
								  						'<span class="extend_text" id="un_extend_text_'+i+'" onclick="finishedSpanClick(this)" name="'+jsonArr.length+'" >变更起始时间:('+dataList[i].igroup+')</span>'+
									             '</div>';
						for(var j =0;j<jsonArr.length;j++){		    //循环生成bean 002
							bean=jsonArr[j];
							tableHtml      += '<table cellpadding="0" cellspacing="0" border="0" class="second_border" id="un_extend_text_'  +i+ '_' +j+'" >'+
//							'<tr  onclick=tr_changeSelected(this)>'+
							'<tr >'+
							'<td width="3%">'+rowNum+'</td>'+
							//'<td><div class="w_order_td">'+bean.ichgchrreleaseId+'</div></td>'+      //工单
							'<td><div class="w_order_td2">'+bean.iapplicationname+'</div></td>'+	//业务系统
							'<td><div class="w_order_td2">'+bean.ichgchrreleaseId+'</div></td>'+    //工单号
							'<td width="15%"><span class="details_stauts stauts_common">'+bean.itcwcqk+'</span></td><!-- implement_stauts实施中  details_stauts投产成功-->'+
							'<td width="15%">'+bean.iexecutive+'</td>'+
							'<td width="15%">'+bean.ioperator+'</td>'+
							'<td width="12%">'+bean.ijhkstime+'</td>'+
							'<td width="12%">'+bean.ijhjstime+'</td>'+
							'<td width="10%"></td>'+
					/*		'<td width="5%"><span class="details_stauts stauts_common">详情</span></td>'+*/
	/*						'<td width="5%"><span class="time_ic ic_pos"></span></td>'+
							'<td width="5%"><span class="time_ic ic_pos2"></span></td>'+*/
							'</tr>'+
							'</table>';
							rowNum++;
						}
					}
					$("#finished_myback").html(tableHtml);
					$(".fh_cn_height").niceScroll({
						autohidemode : true,
						cursorcolor : "#cccccc",
						cursorborder : "0px solid #cccccc"
				    });
				}
			});			
	}catch(e){
		console.log(e)
	}
}
/**@desc 加载_完成工单数据_无分组*/
function load_Finished_grid_noGrouping(){
	try{
		var tableHtml="";
			$.ajax({
				type:"post",
				url: path+"/getBacklogUnFinished.do",
				data : {
				    start:FinishedPageObj.start,
				    limit:FinishedPageObj.limit,
					managerFlag: managerFlag,
					igroupTop : gropQuery2,
					itcwcqk    :tcwcqkQuery,
					dataType:queryFinished,
					orderString:orderString_finish,
					orderColumn:orderColumn_finish
				},
				async:true,
				dataType:"json",
				success: function(jsonData){
					var dataList = jsonData.dataList;
					var dataListOld = jsonData.dataListOld;
					var total=jsonData.total;
					
					//Update new result to the page obj's properties.
					FinishedPageObj.dataList=dataList;
					FinishedPageObj.total=total;
					$("#page_pre_unfininehed_count2").html('总计：'+total);
					var sysListHTML = "";
					var jsonArr=null;
					var bean=null;
					var rowNum=FinishedPageObj.start+1;
//					for(var i=0;i<dataList.length;i++){				//循环生成组 001
//						jsonArr=dataList[i].dataList;
//						 tableHtml      += '<div class="extend_area">'+
//								  						'<div class="expand extend_pos" id="ft_'+i+'" ></div>'+
//								  						'<span class="extend_text" id="un_extend_text_'+i+'" onclick="finishedSpanClick(this)" name="'+jsonArr.length+'" >变更起始时间:('+dataList[i].igroup+')</span>'+
//									             '</div>';
						for(var j =0;j<dataListOld.length;j++){		    //循环生成bean 002
							bean=dataListOld[j];
							var forwordDTStr=' onclick="forwordDT(\''+ dtloginUUID + '\',\''+ bean.ichgchrreleaseId+ '\')"';
							tableHtml      += '<table cellpadding="0" cellspacing="0" border="0" class="second_border" id="un_extend_text_'  +0+ '_' +j+'" >'+
//							'<tr  onclick=tr_changeSelected(this)>'+
							'<tr >'+
							'<td width="3%">'+rowNum+'</td>'+
							//'<td><div class="w_order_td">'+bean.ichgchrreleaseId+'</div></td>'+      //工单
							'<td width="10%"><div class="w_order_td2" title="'+bean.iapplicationname+'">'+bean.iapplicationname+'</div></td>'+	//业务系统
							'<td width="10%"><div class="w_order_td2" '+forwordDTStr+' title="'+bean.idetails+'">'+bean.ichgchrreleaseId+'</div></td>'+    //工单号
							'<td width="15%"><span class="implement_stauts stauts_common" '+forwordDTStr+'>'+bean.itcwcqk+'</span></td><!-- implement_stauts实施中  details_stauts投产成功-->'+
							'<td width="15%">'+bean.iexecutive+'</td>'+
							'<td width="15%">'+bean.ioperator+'</td>'+
							'<td width="12%">'+bean.ijhkstime+'</td>'+
							'<td width="12%">'+bean.ijhjstime+'</td>'+
							'<td width="10%"></td>'+
					/*		'<td width="5%"><span class="details_stauts stauts_common">详情</span></td>'+*/
	/*						'<td width="5%"><span class="time_ic ic_pos"></span></td>'+
							'<td width="5%"><span class="time_ic ic_pos2"></span></td>'+*/
							'</tr>'+
							'</table>';
							rowNum++;
						}
					//}
					$("#finished_myback").html(tableHtml);
					$(".fh_cn_height").niceScroll({
						autohidemode : true,
						cursorcolor : "#cccccc",
						cursorborder : "0px solid #cccccc"
				    });
				}
			});			
	}catch(e){
		console.log(e)
	}
}
/**@desc 加载_饼图数据*/
function load_pie_char(){
	try{
		  $.getJSON('getSchedulePieData.do',  {igroupTop:myGroup,managerFlag: managerFlag}, function(res){
			  var res4=res.bfb4;
			  $('#pie_finished').html(res4+"%");
			   $('#pie_unfinished').html((100-res4) +"%");
				$(".circleChart#circleChartDIV1").circleChart({
		            size: 166,
//					color: "#d9534f",
		            color: "#0e8df5",
					  value:res4	,
					  startAngle:75,//设置圆形进度条动画的开始角度。
//					 backgroundColor: "#e6e6e6",
					  backgroundColor: "#fb5c51",
					lineCap:'butt',
					widthRatio:0.3,
		            //text: 0,
		            onDraw: function(el, circle) {
		               // circle.text("<font color='#d9534f'>"+Math.round(circle.value) + "%"+"</font>");
		            }
		        });
		  });
	}catch(e){
		console.log(e)
	}
}
//------------------------part2: 动画------------------------------
/**@desc tr标签被被单击后*/
function tr_changeSelected(ob){
	$(ob).toggleClass("table_selection");
}
/**@desc 未完成*/
function unfinishedSpanClick(ob){
	var span_id      =$(ob).attr("id");
	var _name=$(ob).attr("name");
	try{
		var idxArr =span_id.split("extend_text_");
		if(null!=idxArr){
			if(idxArr.length==2){
				var idx=idxArr[1];
				var srcClass=$('#et_'+ idx).attr("class");
				if(-1!= srcClass.indexOf("extend_pos2")){
					$('#et_'+ idx+'').removeClass("extend_pos2");
					$('#et_'+ idx+'').addClass("extend_pos");
				}else{
					$('#et_'+ idx+'').removeClass("extend_pos");
					$('#et_'+ idx+'').addClass("extend_pos2");
				}
			}
		}
	}catch(e){
		console.log(e);
	}
	for(var i=0;i<_name;i++){
		$('#'+span_id +'_'+i+'').toggleClass("table_selection");
		$('#'+span_id +'_'+i+'').toggle();
	}

}


function finishedSpanClick(ob){
	var span_id      =$(ob).attr("id");
	var _name=$(ob).attr("name");
	try{
		var idxArr =span_id.split("extend_text_");
		if(null!=idxArr){
			if(idxArr.length==2){
				var idx=idxArr[1];
				var srcClass=$('#et_'+ idx).attr("class");
				if(-1!= srcClass.indexOf("extend_pos2")){
					$('#ft_'+ idx+'').removeClass("extend_pos2");
					$('#ft_'+ idx+'').addClass("extend_pos");
				}else{
					$('#ft_'+ idx+'').removeClass("extend_pos");
					$('#ft_'+ idx+'').addClass("extend_pos2");
				}
			}
		}
	}catch(e){
		console.log(e);
	}
	for(var i=0;i<_name;i++){
		$('#'+span_id +'_'+i+'').toggleClass("table_selection");
		$('#'+span_id +'_'+i+'').toggle();
	}

}
function openWin(ichgchrreleaseId,iexecutive){
	window.open (path+'/accessmyBforword.do?ichgchrreleaseId='+ichgchrreleaseId+'&userid='+userid+'&loginName='+loginName+'&managerFlag='+managerFlag+'&myGroup='+myGroup+'&iexecutive='+iexecutive, 'newwindow', 'height=380, width=700, top=200, left=300, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
}
function openSeqInsQuery(ichgchrreleaseId){
	window.open (path+'/initMyBacklogSequential.do?releaseId='+ichgchrreleaseId, 'newwindow', 'height='+(screen.availHeight-300)+',width='+(screen.availWidth-200)+', top=200, left=150, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
}
/**时序确认操作方法**/
function openSeqInsQuery2(ichgchrreleaseId,relyStateString){
	if("已完成"!=relyStateString && ""!=relyStateString)
		{
		window.wxc.xcConfirm('前置任务未完成,是否确认执行此操作？', window.wxc.xcConfirm.typeEnum.confirm,{
			onOk:function(v){
				 $.getJSON('getAboutBychgCchrReleaseid.do',  {chgCchrReleaseid:ichgchrreleaseId}, function(res){
					 
					  if(res.dataList.length>1)
						  {
						  window.open (path+'/initMyBacklogSequential2.do?releaseId='+ichgchrreleaseId, 'newwindow', 'height='+(screen.availHeight-300)+',width='+(screen.availWidth-200)+', top=200, left=150, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
						  }else
							  {
							  confirmFlowFun(res.dataList[0].iid,res.dataList[0].iid2,res.dataList[0].iid3,ichgchrreleaseId);
							  }
				  });
		}
	    });
		}else
			{
			$.getJSON('getAboutBychgCchrReleaseid.do',  {chgCchrReleaseid:ichgchrreleaseId}, function(res){
				 
				  if(res.dataList.length>1)
					  {
					  window.open (path+'/initMyBacklogSequential2.do?releaseId='+ichgchrreleaseId, 'newwindow', 'height='+(screen.availHeight-300)+',width='+(screen.availWidth-200)+', top=200, left=150, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
					  }else
						  {
						  confirmFlowFun(res.dataList[0].iid,res.dataList[0].iid2,res.dataList[0].iid3,ichgchrreleaseId);
						  }
			  });
			}
	 
	
	}
/**前置任务列表**/
function openSeqInsQuery3(ichgchrreleaseId){
	 window.open (path+'/initMyBacklogSequential3.do?itype=3&releaseId='+ichgchrreleaseId, 'newwindow3', 'height='+(screen.availHeight-300)+',width='+(screen.availWidth-200)+', top=200, left=150, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
	}
/**后置任务列表**/
function openSeqInsQuery4(ichgchrreleaseId){
	 window.open (path+'/initMyBacklogSequential3.do?itype=4&releaseId='+ichgchrreleaseId, 'newwindow3', 'height='+(screen.availHeight-300)+',width='+(screen.availWidth-200)+', top=200, left=150, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
	}



function openBacklog_infoList(){
	  var fulls = "left=0,screenX=0,top=0,screenY=0,scrollbars=1";    //定义弹出窗口的参数
	  if (window.screen) {
	     var ah = screen.availHeight - 30;
	     var aw = screen.availWidth - 10;
	     fulls += ",height=" + ah;
	     fulls += ",innerHeight=" + ah;
	     fulls += ",width=" + aw;
	     fulls += ",innerWidth=" + aw;
	     fulls += ",resizable"
	 } else {
	     fulls += ",resizable"; // 对于不支持screen属性的浏览器，可以手工进行最大化。 manually
	 }
	  window.open(path+'/accessmyBacklog_infoList.do?'+'userid='+userid+'&loginName='+loginName+'&managerFlag='+managerFlag+'&myGroup='+myGroup, 'newwindow', fulls);
	//window.open (path+'/accessmyBacklog_infoList.do?'+'userid='+userid+'&loginName='+loginName+'&managerFlag='+managerFlag+'&myGroup='+myGroup, 'newwindow', 'top=0, left=0 toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
}
//------------------------part3: 翻页操作 handler-----------------
/**
 * @desc  (未完成)表格上一页
 * **/
function unFinished_pageUpHandler(){
	try{
		unFinishedPageObj.pageUp();
	}catch(e){
		console.log(e)
	}
}

/**
 * @desc  (未完成)表格下一页
 * **/
function unFinished_pageDownHandler(){
	try{
		unFinishedPageObj.pageDown();
	}catch(e){
		console.log(e)
	}
}


/**
 * @desc  (已完成)表格上一页
 * **/
function finished_pageUpHandler(){
	try{
		FinishedPageObj. pageUp();
	}catch(e){
		console.log(e)
	}
}

/**
 * @desc  (已完成)表格下一页
 * **/
function finished_pageDownHandler(){
	try{
		FinishedPageObj.pageDown();
	}catch(e){
		console.log(e)
	}
}

/**
 * @desc 获得用户责任人列表，作为 实施人
 * */
function getScheuleGrop(){
	$.ajax({
		type:"post",
		url: path+"/getScheuleGrop.do",
		data : {
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			var dataList = jsonData.dataList;
			$.each(dataList, function (i, item) {
			    $('#myBacklog_grop_1').append($('<option>', { 
			        value: item.name,
			        text : item.name 
			    }));
			    $('#myBacklog_grop_2').append($('<option>', { 
			        value: item.name,
			        text : item.name 
			    }));
			});
		}
	});		
}
function getUserHour(){
	try{
		$.ajax({
			type:"post",
			url: path+"/getUserHour.do",
			data : {
				managerFlag: 0
			},
			async:true,
			dataType:"json",
			success: function(jsonData){
				var dataList=	jsonData.dataList;
				for(var i=0;i<dataList.length;i++){
					bean=dataList[i];
					$("#mybacklog_timeLength").html(bean.inumber1+"h");
					$("#mybacklog_totalCnt").html(bean.inumber2 );
				}
			}
		});		
	}catch(e){
		console.log(e)
	}

}
String.prototype.endWith=function(str){
	if(str==null||str==""||this.length==0||str.length>this.length)
	  return false;
	if(this.substring(this.length-str.length)==str)
	  return true;
	else
	  return false;
	return true;
	}
/** 未完成列表表格列头排序方法**/
function orderFunction(idIn)
{
	var htmlText=$("#"+idIn + "").html();
	var newhtmlText=htmlText.replace(/↑|↓/g, "");
	if(htmlText.endWith("↑"))
		{
    		orderString="DESC";
    		newhtmlText=newhtmlText+"↓";
		}else if(htmlText.endWith("↓"))
		{
			orderString="ASC";
			newhtmlText=newhtmlText+"↑";
		}else
		{
    		orderString="ASC";
    		newhtmlText=newhtmlText+"↑";
    		resetHtmlTextAll('u_td',10);
		}
	orderColumn=idIn;
	$("#"+idIn + "").html(newhtmlText);
	unFinishedPageObj.start=0;
	unFinishedPageObj.load();
}
/** 完成列表表格列头排序方法**/
function orderFunction_finish(idIn)
{
	var htmlText=$("#"+idIn + "").html();
	var newhtmlText=htmlText.replace(/↑|↓/g, "");
	if(htmlText.endWith("↑"))
		{
    		orderString_finish="DESC";
    		newhtmlText=newhtmlText+"↓";
		}else if(htmlText.endWith("↓"))
		{
			orderString_finish="ASC";
			newhtmlText=newhtmlText+"↑";
		}else
		{
    		orderString_finish="ASC";
    		newhtmlText=newhtmlText+"↑";
    		resetHtmlTextAll('f_td',10);
		}
	orderColumn_finish=idIn;
	$("#"+idIn + "").html(newhtmlText);
	FinishedPageObj.start=0;
	FinishedPageObj.load();
}
/**将所有列的↑↓标志清空**/
function resetHtmlTextAll(idIn,inNumber)
{
//	var idIn="td";
	for(var i=1;i<inNumber;i++)
		{
		var htmlText=$("#"+idIn+i + "").html();
		if(htmlText!=undefined)
			{
			$("#"+idIn+i + "").html(htmlText.replace(/↑|↓/g, ""));
			}
		}
}

/**确认执行方法**/
function confirmFlowFun (infoId,istate,ijhkstime,ichgchrreleaseId)
{
	var textTemp="";
	var timestampN = parseInt(new Date().getTime());    // 当前时间戳
	if(ijhkstime>timestampN)
		{
		textTemp="未到达计划开始时间，";
		}
	window.wxc.xcConfirm(textTemp+"是否确认执行此操作？", window.wxc.xcConfirm.typeEnum.confirm,{
		onOk:function(v){
			confirmFlowFun2(infoId,istate,ichgchrreleaseId);
		}
    });
}
/**确认执行方法**/
function confirmFlowFun2 (infoId,istate,ichgchrreleaseId){
	//当前状态运行中，确认为确认完成操作
	if(istate==1)
		{
		window.wxc.xcConfirm('是否同步关闭工单操作？', window.wxc.xcConfirm.typeEnum.confirm,{
			onOk:function(v){
				confirmRunSequentialInfoFun (infoId,istate,1);
				forwordDT(dtloginUUID,ichgchrreleaseId);
				return;
			}
	    });
	     confirmRunSequentialInfoFun (infoId,istate,0);  
		}else
			{
			confirmRunSequentialInfoFun (infoId,istate,0);  
			}
}
/**确认执行方法-修改工单状态方法**/
function confirmRunSequentialInfoFun (infoId,istate,sync)
{
	$.ajax({
		type:"post",
		url: path+"/confirmRunSequentialInfo.do",
		data : {
			infoId :infoId,
			istate:istate,
			sync:sync
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			var success =jsonData.success;
			if (success) {
				//如果是未运行的工单，则自动进行两次确认操作
				if(istate==0)
					{
					confirmRunSequentialInfoFun (infoId,3,sync);
					return;
					}
				unFinishedPageObj.load();
				window.wxc.xcConfirm(jsonData.message,{title:"消息提示"});
				if(istate==3)
					{
					window.wxc.xcConfirm('是否打开变更发起页面？', window.wxc.xcConfirm.typeEnum.confirm,{
						onOk:function(v){
							forwardHisForMG();
						}
				    });
					}
				
			} else {
				window.wxc.xcConfirm(jsonData.message,{title:"消息提示"});
			}
			
		}});
	
}
/**
 * 跳转到监控页面
 */
function forwardHisForMG(){
	 var fulls = "left=0,screenX=0,top=0,screenY=0,scrollbars=1";    //定义弹出窗口的参数
	  if (window.screen) {
	     var ah = screen.availHeight - 30;
	     var aw = screen.availWidth - 10;
	     fulls += ",height=" + ah;
	     fulls += ",innerHeight=" + ah;
	     fulls += ",width=" + aw;
	     fulls += ",innerWidth=" + aw;
	     fulls += ",resizable"
	 } else {
	     fulls += ",resizable"; // 对于不支持screen属性的浏览器，可以手工进行最大化。 manually
	 }
	  window.open(path+'/initMyFocusSequentialInstance.do', 'newwindow2', fulls);
	
}

/**单独确认工单**/
function confirmRunSequentialInfoFunAlone (releaseid,operFlag)
{
	$.ajax({
		type:"post",
		url: path+"/operationRunSequentialAlone.do",
		data : {
			releaseid:releaseid,
			operFlag:operFlag
		},
		async:true,
		dataType:"json",
		success: function(jsonData){
			var success =jsonData.success;
			if (success) {
				unFinishedPageObj.load();
				//window.wxc.xcConfirm(jsonData.message,{title:"消息提示"});
				window.wxc.xcConfirm(jsonData.message, window.wxc.xcConfirm.typeEnum.confirm,{
					onOk:function(v){
						if(operFlag==1)
						{
							window.wxc.xcConfirm('是否打开变更发起页面？', window.wxc.xcConfirm.typeEnum.confirm,{
								onOk:function(v){
									forwardHisForMG();
								}
							});
						}else
							{
							window.wxc.xcConfirm('是否同步关闭工单操作？', window.wxc.xcConfirm.typeEnum.confirm,{
								onOk:function(v){
									forwordDT(dtloginUUID,releaseid);
									return;
								}
						    });
							}
					}
			    });
				
				
			} else {
				window.wxc.xcConfirm(jsonData.message,{title:"消息提示"});
			}
			
		}});
	
}

/**
 * @param uuid 登录到dt的UUID
 * @param pid 工单号
 */
function forwordDT(uuid,pid){
	window.open (dtQueryURL+'ser='+uuid+'&pid='+pid, 'forwordDTnewwindow', 'height=380, width=700, top=200, left=300, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no')
}