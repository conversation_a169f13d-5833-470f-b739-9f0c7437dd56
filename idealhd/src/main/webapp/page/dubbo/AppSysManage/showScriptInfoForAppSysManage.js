Ext.onReady(function(){
	 // 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'},
			{name : 'iuuid' ,type : 'string'},
			{name : 'serviceName' ,type : 'string'},
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'emScript', type: 'string'},//是否应急脚本
		    {name : 'status'     ,type : 'int'}
	    ]
	});
	
	var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'getAllScriptInfoForAppSysManage.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	/** *********************组件********************* */
	/** 查询按钮* */
	var queryButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '查询',
	    handler : queryWhere
	});
	/** 重置按钮* */
	var resetButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '重置',
	    handler : resetWhere
	});
	/** 绑定按钮* */
	var bindButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '绑定',
	    handler : bindService
	});
	
	var scriptForQuery = Ext.create ('Ext.form.TextField',
	{
	    emptyText : '--请输入服务名称--',
	    labelWidth : 50,
	    width : 300,
	    xtype : 'textfield',
	    listeners: {  
              specialkey: function(field,e){    
                  if (e.getKey()==Ext.EventObject.ENTER){    
                    var scriptName = field.getValue()==null?"":field.getValue();
                    scriptServiceReleaseStore.load({
                    			params: {scriptName:scriptName.trim()}
                    	});
                  }  
              }  
          }
	});
	
	var Form1 = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',	border : false,
			items : [scriptForQuery, queryButton, resetButton, '->', bindButton]
		} ]
	});
	
	var selModel = Ext.create('Ext.selection.CheckboxModel');
	/** 列表Columns* */
	var gridColumns = [ {
			text : '序号',
			xtype : 'rownumberer',
			width : 65
		}, 
		{
			 text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		},
		{text : 'uuid',
		dataIndex : 'iuuid',
		width : 40,
		hidden : true
		},
		{
			text : '服务名称',
		    dataIndex : 'serviceName',
		    width : 180,flex:1
		},
		{
		    text : '一级分类',
		    dataIndex : 'buss',
		    width : 70,flex:1
		}, 
		{
			text : '二级分类',
			dataIndex : 'bussType',
			width : 100,flex:1
		},
		{
		    text : '脚本类型',
		    dataIndex : 'scriptType',
		    width : 80,flex:1,
		    renderer:function(value,p,record,rowIndex){
		    	var isflow = record.get('isflow');
		    	if(isflow=="1") {
		    		return "";
		    	} else {
		    		return value;
		    	}
		    }
		},
		{
		    text : '应急脚本',
		    dataIndex : 'emScript',
		    width : 40,
		    flex:1,
		    renderer:function(value,p,record,rowIndex){
		    	if(value==0) {
		    		return '否';
		    	} else if (value==1) {
		    		return '<font color="#F01024">是</font>';
		    	} else {
		    		return '未知';
		    	}
		    }
		}];
	//工具栏
	 var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: scriptServiceReleaseStore,
        dock: 'bottom',
        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border : false
     });
	/** *********************Panel********************* */
	/** 业务系统类型列表panel* */
	var grid_panel = Ext.create ('Ext.grid.Panel',
	{
	    store : scriptServiceReleaseStore,
	    selModel : selModel,
	    region : 'center',
	    padding : panel_margin,
		border:true,
		bbar: pageBar,
	    columnLines : true,
	    columns : gridColumns,
	    collapsible : false,
	    viewConfig:{  
            enableTextSelection:true  
        }
	});

	/** 主Panel* */
	var userquery_mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "showScriptInfoForAppSysManage_area",
	    layout : 'border',
	    width : contentPanel.getWidth ()-502,
	    height : contentPanel.getHeight () - 140,
	    bodyPadding : grid_margin,
	    border : true,
	    bodyCls:'service_platform_bodybg',
	    items : [Form1,grid_panel]
	});

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (userquery_mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	scriptServiceReleaseStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			scriptName : scriptForQuery.getValue().trim(),
			sysIdForScript : sysIdForScript
		};
		Ext.apply (scriptServiceReleaseStore.proxy.extraParams, new_params);
	});
	
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	
	function queryWhere ()
	{
		var scriptName = scriptForQuery.getValue()==null?"":scriptForQuery.getValue();
		scriptServiceReleaseStore.load({
    			params: {scriptName:scriptName.trim()}
    	});
	}
	
	function resetWhere ()
	{
		scriptForQuery.setValue ('');
	}
	
	function bindService ()
	{
		var m = grid_panel.getSelectionModel().getSelection();
		if (m.length < 1)
		{
			Ext.Msg.alert ('提示', '请选择要绑定的服务信息！');
			return;
		}else{
			Ext.Msg.confirm('系统提示','确定要绑定',function(btn){
				if(btn=='yes'){
					var ids=[];
					Ext.each(m,function(item){
						ids.push(item.data.iuuid); //脚本、作业uuid
					});
					Ext.Ajax.request({
						method:"POST",
						url:"bindServiceList.do",
						// params:'servicesIds='+encodeURIComponent(Ext.encode(jsonArray))+'&sysId='+sysIdForScript,
						params : {
    								ids : ids,
    								sysId : sysIdForScript
    					},
						success:function(response){
							var text = Ext.JSON.decode(response.responseText);
							if(text.success){
								Ext.Msg.alert('提示', '绑定成功！');
								scriptServiceReleaseStore.load();
							}else{
	    						Ext.Msg.alert('提示', text.msg);
							}
						},
						failure:function(form, action){
							switch (action.failureType) {
	    					case Ext.form.action.Action.CLIENT_INVALID:
	    						Ext.Msg.alert('提示', '连接异常！');
	    						break;
	    					case Ext.form.action.Action.SERVER_INVALID:
	    						Ext.Msg.alert('提示', action.result.msg);
	    					}
						}
					});	
				}
			});
		}
	}
});