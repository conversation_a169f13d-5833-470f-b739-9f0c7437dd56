Ext.onReady(function(){
	 var win;
	 destroyRubbish();
	 Ext.tip.QuickTipManager.init();
	 Ext.define('sysNameData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},//主键
            {name: 'sysName',    type: 'string'},//应用系统名称
            {name: 'isEnable',     type: 'string'},//是否可用
            {name: 'syncDate',    type: 'string'},//同步时间
            {name: 'syncUser',   type: 'string'},//同步人
            {name: 'syncUserid',   type: 'string'},//同步人主键
            {name: 'dataFrom',   type: 'string'},//数据来源
            {name : 'groupS',type : 'string'}, 
            {name : 'sriid',type : 'string'}, 
            {name : 'admin',type : 'string'}, 
            {name : 'updateDate',type : 'string'},
            {name : 'updateUser',type : 'string'},
            {name : 'updateUserId',type : 'string'},
            {name : 'sysNameAbb',type : 'string'},//简称
            {name : 'manage',type : 'string'},
            {name : 'sysIden',type : 'string'},//系统唯一标识
            {name : 'ipropname',type : 'string'}//系统类别

        ]
     });
	 var selModel = Ext.create('Ext.selection.CheckboxModel');
	 //左侧应用系统数据
     var isEnableStore = Ext.create('Ext.data.Store', {
		fields: ['id','name'],
		data : [
		        {"id":"1","name":"是"},
		        {"id":"2","name":"否"}]
	 });
     
     
	 var isEnableCombo = Ext.create('Ext.form.field.ComboBox', {
			margin : '5',
			store : isEnableStore,
			queryMode : 'local',
			width : 600,
			forceSelection : true, // 要求输入值必须在列表中存在
			typeAhead : true, // 允许自动选择
			displayField : 'name',
			valueField : 'id',
			editable : false,
			triggerAction : "all"
		});
		var groupStoreAll = Ext.create('Ext.data.Store', {
			autoLoad : false,
			proxy : {
				type : 'ajax',
				url : 'queryUserGroupStoreAll.do',
				actionMethods : 'post',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			},
			fields : [ 'typeId', 'typeName' ]
		});
		var groupStore = Ext.create('Ext.data.Store', {
			autoLoad : false,
			proxy : {
				type : 'ajax',
				url : 'queryUserGroupStore.do',
				actionMethods : 'post',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			},
			fields : [ 'typeId', 'typeName' ]
		});
		 var sysName_store = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        autoDestroy : true,
//			    remoteSort: true,
		        pageSize: 30,
		        model: 'sysNameData',
		        proxy: {
		            type: 'ajax',
		            url: 'getAppSysManageList.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
		 
		 var iname_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getApplicationSystemCategoryList.do',
					actionMethods : 'post',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				},
				fields : ['iname' ]
			});
		var groupCombo = Ext.create('Ext.form.field.ComboBox', {
			margin : '5',
			//store : groupStore,
			store : db_isManager?groupStoreAll:groupStore,
			queryMode : 'local',
			width : '15%',
			forceSelection : true, // 要求输入值必须在列表中存在
			typeAhead : true, // 允许自动选择
			displayField : 'typeName',
			valueField : 'typeId',
			triggerAction : "all"
		});
		var groupComboForm = Ext.create('Ext.form.field.ComboBox', {
			margin : '5',
			emptyText : '--所属组名称--',
			store : db_isManager?groupStoreAll:groupStore,
			queryMode : 'local',
			width : '15%',
			hidden:true,
			forceSelection : true, // 要求输入值必须在列表中存在
			typeAhead : true, // 允许自动选择
			displayField : 'typeName',
			valueField : 'typeId',
			triggerAction : "all",
			listeners : {
				select : function(combo, records, options) {
				}
			}
		});

	 var sysName_columns = [{
						text : '序号',
						xtype : 'rownumberer',
						width : 70
					}, {
						text : 'iid',
						dataIndex : 'iid',
						hidden : true
					}, {
						text : '应用系统名称',
						dataIndex : 'sysName',
						editor : {
							xtype : 'textfield'
						},
						flex:1,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					}, {
						text : '系统唯一标识',
						dataIndex : 'sysIden',
						editor : {
							xtype : 'textfield'
						},
						flex:1,
						hidden : !db_sysIden_show,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},{
						text : '系统简称',
						dataIndex : 'sysNameAbb',
						editor : {
							xtype : 'textfield'
						},
						flex:1,
						hidden : !db_sysNameAbb_show,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					}, {
						text : '系统类别',
						dataIndex : 'ipropname',
						width : 80,
						hidden : db_sysNameAbb_show,
			    		editor: new Ext.form.field.ComboBox({
			                allowBlank: true,
			                triggerAction: 'all',
			                // 用all表示把下拉框列表框的列表值全部显示出来
			                editable: false,
			                // 是否可输入编辑
			                store: iname_store,
			                displayField: 'iname',
			                valueField: 'iname'
			            })
					}, {
						text : '有效',
						dataIndex : 'isEnable',
						editor : isEnableCombo,
						width:60,
						renderer : function(value, metadata, record) {
       							var index = isEnableStore.find('id', value);
       							if (index != -1) {
       								return isEnableStore.getAt(index).data.name;
       							} else {
       								return '';
       							}
       						}
					}, {
						text : '录入时间',
						dataIndex : 'syncDate',
						width : 160,
						hidden:true
					}, {
						text : '录入人名称',
						dataIndex : 'syncUser',
						width:120,
						hidden:true,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					}, {
						text : '录入人主键',
						dataIndex : 'syncUserid',
						hidden : true,
						editor : {
							xtype : 'textfield'
						},
						flex : 1
					}, {
						text : '数据来源',
						dataIndex : 'dataFrom',
						//	hidden : true,
						renderer : function(value, metadata, record) {
							if (value == 1) {
								return "手动录入";
							} else if(value == 2){
								return "同步";
							} else if(value == 3){
								return "导入";
							}
						},
						width:70
					},  {
						text : '修改时间',
						dataIndex : 'updateDate',
						flex:1
					},  {
						text : '修改用戶',
						dataIndex : 'updateUser',
						width : 120,
						hidden:true,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},  {
						text : '修改用戶ID',
						hidden:true,
						dataIndex : 'updateUserId',
						width : 160
					}
					];   
	 //左侧页数工具栏
	 var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: sysName_store,
        dock: 'bottom',
        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border : false
     });
     
     var sysNameQuery = Ext.create('Ext.form.TextField', {
		emptyText : db_sysNameAbb_show?'--请输入应用系统名称或简称--':'--请输入应用系统名称--',
		labelWidth : 50,
		width : '25%',
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var sysName = field.getValue() == null ? "" : field.getValue();
					sysName_store.load( {
						params : {
							sysName : sysName.trim()
						}
					});
				}
			}
		}
	});
	var queryButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '查询',
		handler : queryWhere
	});
	 
	var resetButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '重置',
		handler : resetWhere
	});
	/**导入导出按钮**/
	var importOrExportButton = Ext.create("Ext.Button",{
            text: '导入/导出',
            cls:'Common_Btn',
            menu: {
                xtype: 'menu',
                plain: true,
                items: {
                    xtype: 'buttongroup',
                    columns: 2,
                    defaults: {
                        xtype: 'button'
                    },
                    items: [ {
						text : '导入',
						cls:'Common_Btn',
                        hidden : !db_isManager,
						handler : uploadExcel
					},
					{
						text : '导出',
						cls:'Common_Btn',
						handler: function() {
		                	var record = sysName_grid.getSelectionModel().getSelection();
		                	var ids=[];
		            		if(record.length!=0){
		            			Ext.each(record,function(item){
		            				var iid = item.get('iid');
		            				if(iid != 0){
		            					ids.push(iid);
		            					ids.join(',')
		            				}
		            			});
		            		}
		            		window.location.href = 'exportAppSys.do?ids='+ids+'&projectFlag='+projectFlag;
		            	}
					}]
                }
            }
	});
	 var sysName_grid = Ext.create('Ext.grid.Panel', {
    	selModel:selModel,
	    store:sysName_store,
	    padding : grid_space,
	    cls:'customize_panel_back',
		border:true,
		bbar: pageBar,
		region:'center',
	    columnLines : true,
	    columns:sysName_columns,
	    viewConfig:{  
            enableTextSelection:true  
        },
	    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
 	    dockedItems: [{
  	    xtype: 'toolbar',
  	  	border : false,
	  		items: [sysNameQuery,groupComboForm,queryButtonForBSM,
	  		        resetButtonForBSM,'->',
	  		        {
	  		        	xtype: 'button',
	  		            text: '增加',
	  		            hidden : !db_isManager,
	  		            textAlign:'center',
	  		            cls : 'Common_Btn',
	  		            handler:function(){
	  		            	var data={iid:'0',
		  		            			sysName:'',
		  		            			ipropname:'',
		  		            			isEnable:'1',
		  		            			groupS : '',
		  		            			dataFrom:'1',
		  		            			syncUser:''
	  		            	};
	  		            	sysName_store.insert(0,data);
	  		            }
	  		        },{
	  		            xtype: 'button',
	 		            textAlign:'center',
	  		            text: '保存',
	  		            hidden : !db_isManager,
	  		            cls : 'Common_Btn',
	  		         	handler:function(){
	  		         		saveSys();
	  		            }
	  		        },{
	  		            xtype: 'button',
	 		            textAlign:'center',
	  		            text: '删除',
	  		            hidden : !db_isManager,
	  		            itemId : 'delete',
	  		            disabled : true,
	  		            cls : 'Common_Btn',
	  		            handler:function(){
	  		            	deleteSys();
	  		            }
	  		        },importOrExportButton
	  		]}
	  	]
	});
	 sysName_store.on('beforeload',function(store, options) {
			var sysName = sysNameQuery.getValue()==null?"":sysNameQuery.getValue();
			var new_params = {
						sysName:sysName.trim(),
   				     	groupCombo : groupComboForm.getValue(),
   				     	projectFlag:projectFlag
					};
					Ext.apply(sysName_store.proxy.extraParams, new_params);
				});
		sysName_grid.getSelectionModel().on('selectionchange', function(selModel, selections) {
			sysName_grid.down('#delete').setDisabled(selections.length === 0);
	    });
	//导入
	    function uploadExcel(){
	    	var uploadWindows;
	    	var uploadForm;
	        uploadForm = Ext.create('Ext.form.FormPanel',{
	        	border : false,
	        	items : [{
	            	xtype: 'filefield',
	    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
	    			fieldLabel: '选择文件',
	    			labelWidth: 80,
	    			anchor: '90%',
	    			margin: '10 10 0 40',
	    			buttonText: '浏览'
	            }],
	            buttonAlign : 'center',
	            buttons :[{
	            	text : '确定',
	            	handler :upExeclData
	            },{
	            	text : '取消',
	            	handler : function(){
	            		uploadWindows.close();
	            	}
	            }]
	        });
	        /**
	         * Excel导入Agent信息窗体
	         */
	        uploadWindows = Ext.create('Ext.window.Window', {
	    		title : 'Excel导入',
	    		layout : 'fit',
	    		height : 180,
	    		width : 600,
	    		modal : true,
//	    		autoScroll : true,
	    		items : [ uploadForm ],
	    		listeners : {
	    			close : function(g, opt) {
	    				uploadForm.destroy();
	    			}
	    		}
	    	});
	        uploadWindows.show();
	        function upExeclData(){
	        	var form = uploadForm.getForm();
	    		var hdupfile=form.findField("fileName").getValue();
	    		if(hdupfile==''){
	    			Ext.Msg.alert('提示',"请选择文件...");
	    			return ;
	    		}
	    		uploadTemplate(form);
	        }
	        function uploadTemplate(form) {
	      	   if (form.isValid()) {
	             form.submit({
	               url: 'importAppSysManage.do?projectFlag='+projectFlag,
	                 success: function(form, action) {
	                    var sumsg = Ext.decode(action.response.responseText).message;
	                    Ext.Msg.alert('提示',sumsg);
	            		uploadWindows.close();
	            		sysName_store.reload();
	                    return;
	                 },
	                 failure: function(form, action) {
	                     var msg = Ext.decode(action.response.responseText).message;
	                     Ext.Msg.alert('提示',msg);
	                   return;
	                 }
	             });
	      	   }
	      	 }
	    }
	 var panelleft = Ext.create('Ext.panel.Panel',{
    	title:'应用系统信息',
    	layout : 'border',
    	border:false,
		region : 'west',
		split : true,
		width : db_userbind_show?'60%':'100%',
		bodyCls : 'x-docked-noborder-top',
		bodyPadding : grid_space,
		cls:'customize_panel_back',
		items : [sysName_grid]
    });
	sysName_grid.on("select",function(obj, record, index, eOpts){
    	var sysId = record.get('iid'); //应用系统表IEAI_SCRIPT_BUSINESS_SYSTEM主键
    	if(sysId<1){
    		return;
    	}
    	userInfo_store.load({
    	    params: {
    	    sysId : sysId
    	}
    	});
    });

   Ext.define('userInfoData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'isysid', type: 'string'},
            {name: 'iuserid', type: 'string'},
            {name: 'fullname',     type: 'string'},
            {name: 'duty',   type: 'string'},
            {name: 'department',   type: 'string'},
            {name: 'email',   type: 'string'},
            {name: 'telephone',     type: 'string'}
        ]
    });
     Ext.define('dutyModel', {
         extend : 'Ext.data.Model',
         fields : [ {
             name : 'id',
             type : 'string'
         }, {
             name : 'name',
             type : 'string'
         } ]
     });
     var dutyStore = Ext.create('Ext.data.Store', {
         autoDestroy : true,
         autoLoad : true,
         model : 'dutyModel',
         proxy : {
             type : 'ajax',
             url : 'getDutyDataType.do',
             reader : {
                 type : 'json',
                 root : 'dataList'
             }
         }
     });     
    var dytyCombo = Ext.create('Ext.form.field.ComboBox', {
        margin : '5',
        store : dutyStore,
        queryMode : 'local',
        width : 600,
        forceSelection : true, // 要求输入值必须在列表中存在
        typeAhead : true, // 允许自动选择
        displayField : 'name',
        valueField : 'id',
        editable : false,
        triggerAction : "all"
    });
    var userInfo_columns = [{ text: '序号', xtype:'rownumberer', width: 65 },
                            { text: 'iid',  dataIndex: 'iid',hidden:true},
                            { text: 'isysid',  dataIndex: 'isysid',hidden:true},
                            { text: 'iuserid',  dataIndex: 'iuserid',hidden:true},
                            { text: '用户名',  dataIndex: 'fullname'},
                            { text: '职务',  dataIndex: 'duty',flex:1,editor : dytyCombo,
                                renderer : function(value, metadata, record) {
                                 var index = dutyStore.find('id', value);
                                 if (index != -1) {
                                     return dutyStore.getAt(index).data.name;
                                 } else {
                                     return '成员';
                                 }
                             }
                            },
                            { text: '部门',  dataIndex: 'department',hidden:true},
                            { text: '电子邮件',  dataIndex: 'email',flex:1,hidden:true},
                            { text: '电话',  dataIndex: 'telephone',flex:1}
                        ];
	    var userInfo_store = Ext.create('Ext.data.Store', {
	        autoLoad : false,
	        autoDestroy : true,
	        pageSize: 100,
//	        remoteSort: true,
	        model: 'userInfoData',
	        proxy: {
	            type: 'ajax',
	            url: 'getUserInfoForSysList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	    
	    userInfo_store.on('beforeload', function (store, options) {
	        var selDatas = sysName_grid.getSelectionModel().getSelection();
	        var ii  = selDatas.length;
	        var sysId=-1;
	        if(ii<1){
	            sysId = -1;
	        }else{
	            sysId = selDatas[ii-1].get('iid');
	        }
	        var new_params = {  
	                sysId : sysId,
	            };
	     Ext.apply(userInfo_store.proxy.extraParams, new_params);
	  });
    var pageUserBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: userInfo_store,
        dock: 'bottom',
        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border : false
    });
    var userInfo_grid = Ext.create('Ext.grid.Panel', {
        selModel:Ext.create('Ext.selection.CheckboxModel',{checkOnly: true}),
        cls:'customize_panel_back',
        region:'center',
        store:userInfo_store,
        padding : grid_space,
        border:true,
        bbar: pageUserBar,  
        columnLines : true,
        columns:userInfo_columns,
        viewConfig:{  
            enableTextSelection:true  
        },
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
        dockedItems: [{
            xtype: 'toolbar',border:false,
            items: [{
                        xtype: 'tbseparator'
                    },{
                        xtype: 'tbfill'
                    },{
                        xtype: 'button',
                        text: '增加',
                        hidden : (!db_isManager)&&(!user_manage),
                        textAlign:'center',
                        cls : 'Common_Btn',
                        handler:adduserInfo
                    },{
                        xtype: 'button',
                        textAlign:'center',
                        text: '保存',
                        hidden : (!db_isManager)&&(!user_manage) ,
                        cls : 'Common_Btn',
                        handler:function(){
                            saveUserInfo();
                        }
                    },{
                        xtype: 'button',
                        textAlign:'center',
                        text: '解绑',
                        hidden : (!db_isManager)&&(!user_manage) ,
                        cls : 'Common_Btn',
                        handler:function(){
                            unbinduserInfo();
                        }
                    },{
                        xtype: 'button',
                        textAlign:'center',
                        text: '权限转移',
                        hidden :!user_manage ,
                        cls : 'Common_Btn',
                        handler:function(){
                            permTrance();
                        }
                    }
            ]}
        ]
    });
	var rigthUser = Ext.create('Ext.panel.Panel',{ 
	    title : '权限配置',
	    layout : 'border',
	    bodyCls : 'x-docked-noborder-top',
	    cls:'customize_panel_back',
	    bodyPadding : grid_space,
	    region : 'center',
	    border : false,
	    items : [userInfo_grid]
	});

	   Ext.define('scriptInfoData', {
	        extend: 'Ext.data.Model',
	        fields: [
	            {name: 'iid',     type: 'string'}, //服务主键
	            {name: 'sysRelationId', type: 'string'}, //关系主键
	            {name: 'serviceName', type: 'string'}, //服务名称
	            //{name: 'scriptName', type: 'string'}, //脚本名称
	            {name: 'buss', type: 'string'},//一级分类
	            {name: 'bussType', type: 'string'},//二级分类
	            {name: 'emScript', type: 'string'}//是否应急脚本
	            
	        ]
	    });

		var scriptInfo_store = Ext.create('Ext.data.Store', {
//				remoteSort : true,
				autoLoad : true,
				autoDestroy : true,
		        pageSize: 30,
		        model: 'scriptInfoData',
		        proxy: {
		            type: 'ajax',
		            url: 'getScirptInfoListBySysManage.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
			
			sysName_grid.on("select",function(obj, record, index, eOpts){
		    	var sysId = record.get('iid'); //应用系统表IEAI_SCRIPT_BUSINESS_SYSTEM主键
		    	if(sysId<1){
		    		return;
		    	}
		    	//根据sysId载入右侧脚本作业数据
		    	scriptInfo_store.load({
		    		params: {
		    			sysId : sysId
		    		}
		    	});
		    	userInfo_store.load({
		    	    params: {
		    	    sysId : sysId
		    	}
		    	});
		    });
		    
			scriptInfo_store.on('beforeload', function (store, options) {
		    	var selDatas = sysName_grid.getSelectionModel().getSelection();
		    	var ii  = selDatas.length;
		    	var sysId;
		    	if(ii<1){
		    		sysId = -1;
		    	}else{
		    		sysId = selDatas[ii-1].get('iid');
		    	}
				var new_params = {  
						sysId : sysId
					};
		     Ext.apply(scriptInfo_store.proxy.extraParams, new_params);
		  });
	     
		  
		 var scriptInfo_columns = [{
				text : '序号',
				xtype : 'rownumberer',
				width : 70
			},
			{
			    text : 'sysRelationId',
			    dataIndex : 'sysRelationId',
			    width : 40,
			    hidden : true
			},
			{
			    text : '服务主键',
			    dataIndex : 'iid',
			    width : 40,
			    hidden : true
			}, 
			{
				text : '服务名称',
			    dataIndex : 'serviceName',
			    width : 250,
			    flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},
			{
			    text : '一级分类',
			    dataIndex : 'buss',
			    width : 100,
			    flex:1
			},
			{
			    text : '二级分类',
			    dataIndex : 'bussType',
			    width : 70,
			    flex:1
			},
			{
			    text : '应急脚本',
			    dataIndex : 'emScript',
			    width : 40,
			    flex:1,
			    renderer:function(value,p,record,rowIndex){
			    	if(value==0) {
			    		return '否';
			    	} else if (value==1) {
			    		return '<font color="#F01024">是</font>';
			    	} else {
			    		return '未知';
			    	}
			    }
			}];
		var pageBarServer = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	    	store: scriptInfo_store,
	        dock: 'bottom',
	        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border : false
	    });
		 var selModelServer = Ext.create('Ext.selection.CheckboxModel',{checkOnly: true});
	     var scriptInfo_grid = Ext.create('Ext.grid.Panel', {
	    	selModel:selModelServer,
	    	cls:'customize_panel_back',
		    store:scriptInfo_store,
		    padding : grid_space,
			border:true,
			bbar: pageBarServer,  
			region:'center',
		    columnLines : true,
		    columns:scriptInfo_columns,
		    viewConfig:{  
	            enableTextSelection:true  
	        },
		    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
	 	    dockedItems: [{
	  	       	xtype: 'toolbar',border:false,
		  		items: [{
		  		            xtype: 'tbseparator'
		  		        },{
		  		            xtype: 'tbfill'
		  		        },{
		  		        	xtype: 'button',
		  		            text: '增加',
		  		            textAlign:'center',
		  		            cls : 'Common_Btn',
		  		            handler:addScriptInfo
		  		        },{
		  		            xtype: 'button',
		 		            textAlign:'center',
		  		            text: '解绑',
		  		            cls : 'Common_Btn',
		  		            handler:function(){
		  		            	unbindScriptInfo();
		  		            }
		  		        }
		  		]}
		  	]
		});
		var panelrigth = Ext.create('Ext.panel.Panel',{
	    	layout : 'border',
	    	title : '脚本及作业',
	    	bodyCls : 'x-docked-noborder-top',
	    	cls:'customize_panel_back',
	    	  bodyPadding : grid_space,
	    	region : 'south',
	    	height:'50%',
	    	border : false,
			items : [scriptInfo_grid]
	    });
		var rigthP = Ext.create('Ext.panel.Panel',{
			layout : 'border',
	    	bodyCls : 'x-docked-noborder-top',
	    	cls:'customize_panel_back',
	    	region : 'center',
	    	border : false,
			items : [rigthUser,panelrigth]
	    });
	var mainPanel = Ext.create('Ext.panel.Panel',{
    	height:contentPanel.getHeight()-modelHeigth,
        renderTo : "dbaas_AppSysManage_area",
        border : true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',	
        layout : 'border',
    	items : db_userbind_show?[panelleft,db_ss_bind?rigthP:rigthUser]:[panelleft]
    });

    contentPanel.on('resize',function(){    	
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    function addScriptInfo(){
    	var selDatas = sysName_grid.getSelectionModel().getSelection();
    	
		if(selDatas==""){
			Ext.Msg.alert('消息提示','请选择已保存的应用系统,再进行此操作！');
			return;
		}
		
		var m = sysName_store.getModifiedRecords();
		if(m.length>0){
			Ext.Msg.alert('消息提示','请先保存应用系统信息,再进行此操作！');
			return;
		}
		if(win){
			win.close();
		}
		if(selDatas.length!=1){
				Ext.Msg.alert('消息提示','请选择一条应用系统信息再进行此操作！');
				return;
		}
		var sysId = selDatas[0].get('iid');
		var win = Ext.create('Ext.window.Window', {
		  		title : '脚本及作业信息',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		closeAction : 'destroy',
		  		width : contentPanel.getWidth()-495,
		  		height : contentPanel.getHeight()-modelHeigth-40,
		  		loader : {
			 			url : "showScriptInfoForAppSysManage.do?sysIdForScript="+sysId,
			 			params : {
			 				sysIdForScript : sysId
	  					},
	  					autoLoad: true,
	  					autoDestroy : true,
	  					scripts : true
		  	    }
		  	}).show();
		 win.on("close",function(){
			 //sysName_store.reload();
			 scriptInfo_store.reload();
		 });
    }
	//解绑按钮
    function unbindScriptInfo(){
    	Ext.Msg.confirm('确认提示','您确定要进行此操作吗？',function(bn){
    		if(bn=='yes'){
    			var selDatas = scriptInfo_grid.getSelectionModel().getSelection();
    			if(selDatas.length==0){
    				Ext.Msg.alert('消息提示','请选择记录进行操作！');
    				return;
    			}
    			var sysRelationIds = [];
    			Ext.Array.each(selDatas, function(record) {
                    var iid = record.get('iid');
                    // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                    if(iid==0){
                    	scriptInfo_store.remove(record);
                    }else{
                    	sysRelationIds.push(record.get('sysRelationId'));
                    }
                });
    			Ext.Ajax.request({
    			    url: 'unbindServiceList.do',
    			    params: {
    			    	sysRelationIds: sysRelationIds
    			    },
    			    success: function(response){
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
							if (success) {
								//sysName_store.reload();
	    			        	//scriptInfo_store.reload();
								pageBarServer.moveFirst();
								Ext.Msg.alert('提示', message);
							} else {
								Ext.Msg.alert('提示', message);
							}
						},
    			    failure: function(result, request) {
    			    	secureFilterRs(result,"请求返回失败！",request);
    			    }
    			});
    		}
    	});
    }
    //保存应用系统数据
    function saveSys(){
    	if (Ext.isIE) {
			CollectGarbage();
		}
    	var m = sysName_store.getModifiedRecords();
		if (m.length == 0) {
			Ext.MessageBox.alert("提示", "没有需要保存的条目！");
			return;
		}
		for ( var j = 0; j < m.length; j++) {
			var n = 0;
			var n1=0;
			var sysName = m[j].data['sysName'].trim();
			var sysIden_1 = m[j].data['sysIden'].trim();
			if ("" == sysName || sysName == null) {
				Ext.MessageBox.alert("提示", "应用系统名称不能为空！");
				return;
			}
			if(sysName.length>255){
				Ext.MessageBox.alert("提示", "应用系统名称过长，请少于255字节！");
				return;
			}
			if(db_sysIden_show){
				if ("" == sysIden_1 || sysIden_1 == null) {
					Ext.MessageBox.alert("提示", "系统唯一标识不能为空！");
					return;
				}
				if(sysIden_1.length>25){
					Ext.MessageBox.alert("提示", "系统唯一标识过长，请少于25字节！");
					return;
				}
			}
			var sysNameAbb = m[j].data['sysNameAbb'].trim();
			if(sysNameAbb.length>25){
				Ext.MessageBox.alert("提示", "应用系统简称过长，请少于25字！");
				return;
			}
			for ( var k = 0; k < sysName_store.getCount(); k++) {
				var record = sysName_store.getAt(k);
				var cname = record.data.sysName.trim();
				var sysIden_2 = record.data.sysIden.trim();
				if (cname == sysName) {
					n = n + 1;
				}
				if (sysIden_2 == sysIden_1) {
					n1 = n1 + 1;
				}
			}
			if (n > 1) {
				Ext.MessageBox.alert("提示", "应用系统名称不能重复！");
				return;
			}
			if(db_sysIden_show){
				if (n1 > 1) {
					Ext.MessageBox.alert("提示", "系统唯一标识不能重复！");
					return;
				}
			}
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0){
				jsonData = jsonData + ss;
			}
			else{
				jsonData = jsonData + "," + ss;
			}
		}
		jsonData = jsonData + "]";
		Ext.Ajax.request({
		    url: 'saveAppSysManage.do',
			params : {
				jsonData : jsonData,
				gridswitch:false,
				isManager:db_isManager,
				projectFlag:projectFlag
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					sysName_store.load();
					Ext.Msg.alert("提示", message);
				} else {
					Ext.Msg.alert("提示", message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result,"请求返回失败！",request);
			}
		});
    }
    
    //删除系统数据
    function deleteSys(){
    	if (Ext.isIE) {
			CollectGarbage();
		}
		var data = sysName_grid.getView().getSelectionModel().getSelection();
		if (data.length < 1) {
			Ext.MessageBox.alert("提示", "请选择要删除的数据");
			return;
		}
        var ids = [];
        Ext.Array.each(data, function(record) {
            var sysId = record.get('iid');
            // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
            if(sysId==0){
            	sysName_store.remove(record);
            }else{
            	ids.push(sysId);
            }
        });
		Ext.Ajax.request( {
			url : 'deleteAppSysManageisok.do',
			method : 'post',
			params : {
						ids : ids
					},
			success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var msg="您确定要进行删除操作吗？";
					if(success){
						 msg="您删除的系统资源纳管中已经使用，删除将同时删除资源纳管的资源，确认删除？";
					}
					Ext.Msg.confirm('确认提示',msg,function(bn){
			    		if(bn=='yes'){
			                if(ids.length>0){
			                	Ext.Ajax.request( {
			    					url : 'deleteAppSysManage.do',
			    					method : 'post',
			    					params : {
			    								ids : ids
			    							},
			    					success : function(response, request) {
			    							var success = Ext.decode(response.responseText).success;
			    							var message = Ext.decode(response.responseText).message;
			    								if (success) {
			    									userInfo_store.reload();
			    									sysName_store.reload();
			    									pageBar.moveFirst();
			    									pageBarServer.moveFirst();
			    									Ext.Msg.alert('提示', message);
			    								} else {
			    									Ext.Msg.alert('提示', message);
			    								}
			    							},
			    							failure : function(result, request) {
			    								secureFilterRs(result,"请求返回失败！",request);
			    							}
			    						});
			                }
			    		}
			    	});
					},
					failure : function(result, request) {
						secureFilterRs(result,"请求返回失败！",request);
					}
				});
    }
    function queryWhere() {
    	var sysName = sysNameQuery.getValue()==null?"":sysNameQuery.getValue();
    	sysName_store.load({
    			params: {sysName:sysName.trim(),
    				     groupCombo : groupComboForm.getValue()
    			}    				    			
    	});
	}
    function syncUserGroup() {
		Ext.Msg.confirm('系统提示', '确定要同步应用系统数据吗?', function(btn) {
			if (btn == 'yes') {
				Ext.MessageBox.wait("数据处理中...", "进度条");
				Ext.Ajax.request({
					url : 'syncAppSysManage.do',
					method : "post",
					timeout : 1000000,
					success : function(response, opts) {
						var message = Ext.decode(response.responseText).message;
						Ext.Msg.alert('提示', message);
						sysName_store.reload();// 数据加载
					},
					failure : function(result, request) {
						secureFilterRs(result, "操作失败！");
					}
				});
			}

		}, this);
	}
	function issueAppSysManage(){
		Ext.Msg.confirm('系统提示', '确定要发布测试环境下应用系统数据到生产环境吗?', function(btn) {
			if (btn == 'yes') {
				Ext.MessageBox.wait("数据处理中...", "进度条");
				Ext.Ajax.request({
					url : 'issueAppSysManage.do',
					method : "post",
					timeout : 1000000,
					success : function(response, opts) {
						var message = Ext.decode(response.responseText).message;
						Ext.Msg.alert('提示', message);
						sysName_store.reload();// 数据加载
					},
					failure : function(result, request) {
						secureFilterRs(result, "操作失败！");
					}
				});
			}

		}, this);
	}
    function resetWhere ()
	{
    	sysNameQuery.setValue ('');
    	groupComboForm.setValue('');
	}
    function adduserInfo () {
        var selDatas = sysName_grid.getSelectionModel().getSelection();
        
        if(selDatas==""){
            Ext.Msg.alert('消息提示','请选择已保存的应用系统,再进行此操作！');
            return;
        }
        
        var m = sysName_store.getModifiedRecords();
        if(m.length>0){
            Ext.Msg.alert('消息提示','请先保存应用系统信息,再进行此操作！');
            return;
        }
        if(win){
            win.close();
        }
        if(selDatas.length!=1){
                Ext.Msg.alert('消息提示','请选择一条应用系统信息再进行此操作！');
                return;
        }
        var manage = selDatas[0].get('manage');
        if(!db_isManager && manage==0 && !user_manage) {
            Ext.Msg.alert('消息提示','当前用户无权管理成员！'+manage);
            return;
        }
        var sysId = selDatas[0].get('iid');
		  Ext.Ajax.request({
	         url: 'checkUserForSysPerm.do',
	         params : {
	         	sysId:sysId
	         },  
	         success : function(response, request) {
	             var success = Ext.decode(response.responseText).success;
	             var message = Ext.decode(response.responseText).message;
	             if(success){
			        var win = Ext.create('Ext.window.Window', {
			                title : '用户信息',
			                autoScroll : true,
			                modal : true,
			                resizable : false,
			                closeAction : 'destroy',
			                width : contentPanel.getWidth()-495,
			                height : contentPanel.getHeight()-modelHeigth-40,
			                loader : {
			                        url : "showUserInfoForSys.do",
			                        params : {
			                            sysId: sysId
			                        },
			                        autoLoad: true,
			                        autoDestroy : true,
			                        scripts : true
			                }
			            }).show();
			         win.on("close",function(){
			             userInfo_store.reload();
			         }); 
			         } else {
			             Ext.Msg.alert("提示", message);
			         }
			     },
			     failure : function(result, request) {
			         secureFilterRs(result,"请求返回失败！",request);
			     }
			 });
    }
    //用户信息保存
    function saveUserInfo(){
        if (Ext.isIE) {
            CollectGarbage();
        }
        var selDatas = sysName_grid.getSelectionModel().getSelection();
        var manage = selDatas[0].get('manage');
        if(!db_isManager && manage==0  && !user_manage) {
            Ext.Msg.alert('消息提示','当前用户无权管理成员！');
            return;
        }
        var m = userInfo_store.getModifiedRecords();
        if (m.length == 0) {
            Ext.MessageBox.alert("提示", "没有需要保存的条目！");
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var record = m[i];
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0)
                jsonData = jsonData + ss;
            else
                jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        var str = jsonData;
        Ext.Ajax.request({
            url: 'checkUserForSysPerm.do',
            params : {
            	sysId:selDatas[0].get('iid')
            },  
            success : function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    Ext.Ajax.request({
                        url: 'saveUserInfoForSys.do',
                        params : {
                            jsonData : jsonData
                        },  
                        success : function(response, request) {
                            var success = Ext.decode(response.responseText).success;
                            var message = Ext.decode(response.responseText).message;
                            if (success) {
                                userInfo_store.reload();
                                Ext.Msg.alert("提示", message);
                            } else {
                                Ext.Msg.alert("提示", message);
                            }
                        },
                        failure : function(result, request) {
                            secureFilterRs(result,"请求返回失败！",request);
                        }
                    });
                } else {
                    Ext.Msg.alert("提示", message);
                }
            },
            failure : function(result, request) {
                secureFilterRs(result,"请求返回失败！",request);
            }
        });

    }
    //解绑
    function unbinduserInfo () {
    	 var selDatas = sysName_grid.getSelectionModel().getSelection();
    	  Ext.Ajax.request({
              url: 'checkUserForSysPerm.do',
              params : {
              	sysId:selDatas[0].get('iid')
              },  
              success : function(response, request) {
                  var success = Ext.decode(response.responseText).success;
                  var message = Ext.decode(response.responseText).message;
                  if (success) {
				        Ext.Msg.confirm('确认提示','您确定要进行此操作吗？',function(bn){
				            if(bn=='yes'){
				                var selDatas = userInfo_grid.getSelectionModel().getSelection();
				                if(selDatas.length==0){
				                    Ext.Msg.alert('消息提示','请选择记录进行操作！');
				                    return;
				                }
				                var sysRelationIds = [];
				                Ext.Array.each(selDatas, function(record) {
				                    var iid = record.get('iid');
				                    // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
				                    sysRelationIds.push(record.get('iid'));
				                });
				                Ext.Ajax.request({
				                    url: 'unbindUserForSys.do',
				                    params: {
				                        ids: sysRelationIds
				                    },
				                    success: function(response){
				                        var success = Ext.decode(response.responseText).success;
				                        var message = Ext.decode(response.responseText).message;
				                            if (success) {
				                                pageBar.moveFirst();
				                                Ext.Msg.alert('提示', message);
				                            } else {
				                                Ext.Msg.alert('提示', message);
				                            }
				                        },
				                    failure: function(result, request) {
				                        secureFilterRs(result,"请求返回失败！",request);
				                    }
				                });
				            }
				        });
                  } else {
                      Ext.Msg.alert("提示", message);
                  }
              },
              failure : function(result, request) {
                  secureFilterRs(result,"请求返回失败！",request);
              }
          });
    }
    //权限转移
    function permTrance () {
    	var selSysDatas = sysName_grid.getSelectionModel().getSelection();
    	var selDatas = userInfo_grid.getSelectionModel().getSelection();
    	Ext.Ajax.request({
            url: 'checkUserForSysPerm.do',
            params : {
            	sysId:selSysDatas[0].get('iid')
            },  
            success : function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
			    	Ext.Msg.confirm('确认提示','您确定要进行权限转移吗？',function(bn){
			    		if(bn=='yes'){   
			    			if(selDatas.length==0){
			    				Ext.Msg.alert('消息提示','请选择记录进行操作！');
			    				return;
			    			}
			    			var sysRelationIds = [];
			    			Ext.Array.each(selDatas, function(record) {
			    				var iid = record.get('iid');
			    				// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
			    				sysRelationIds.push(record.get('iid'));
			    			});
			    			Ext.Ajax.request({
			    				url: 'bindUserForSysPerm.do',
			    				params: {
			    					sysId:selSysDatas[0].get('iid'),
			    					ids: sysRelationIds
			    				},
			    				success: function(response){
			    					var success = Ext.decode(response.responseText).success;
			    					var message = Ext.decode(response.responseText).message;
			    					if (success) {
			    						queryWhere();
			    						pageBar.moveFirst();
			    						Ext.Msg.alert('提示', message);
			    					} else {
			    						Ext.Msg.alert('提示', message);
			    					}
			    				},
			    				failure: function(result, request) {
			    					secureFilterRs(result,"请求返回失败！",request);
			    				}
			    			});
			    		}
			    	});
                } else {
                    Ext.Msg.alert("提示", message);
                }
            },
            failure : function(result, request) {
                secureFilterRs(result,"请求返回失败！",request);
            }
        });
    }
});