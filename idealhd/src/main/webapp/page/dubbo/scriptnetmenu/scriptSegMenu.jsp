<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
    String bankFlag_SegMenu = Environment.getInstance().getBankSwitch();
%>
<html>
<head>
<title>access control</title>
<script type="text/javascript">
    	var bankFlag_SegMenu = '<%=bankFlag_SegMenu%>';
    	var userId_SegMenu="<%=request.getAttribute("userId")%>";
    	var funTypeSwitch=<%=request.getAttribute("funTypeSwitch")%>;
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptnetmenu/scriptSegMenu.js"></script>
</head>

<body>
	<div id="netmenu_grid_area" style="width: 100%; height: 100%"></div>

</body>

</html>
