Ext.onReady(function() {
    destroyRubbish();
    var itemsPerPage = 30;
    var titleArray = new Array();

    Ext.define('scriptService', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
            },
            {
                name: 'userName',
                type: 'string'
            },
            {
                name: 'uploadTime',
                type: 'string'
            },
            {
                name: 'fileName',
                type: 'string'
            }
	    ]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        //checkOnly: true
    });

    var scriptCompare_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: itemsPerPage,
        model: 'scriptService',
        proxy: {
            type: 'ajax',
            url: 'getScriptCompareList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptservice_columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: 'iid',
        dataIndex: 'iid',
        hidden: true,
        width: 90
    },
    {
        text: '文件名',
        dataIndex: 'fileName',
        width: 250,
        flex:1
    },
    {
        text: '比对人',
        dataIndex: 'userName',
        width: 120
    },
    {
        text: '比对时间',
        dataIndex: 'uploadTime',
        width: 250
    },
	{
		text : '操作',
		xtype : 'actiontextcolumn',
		width:280,
		align : 'center',
		items : [
            {
                text : '删除',
                iconCls: 'attachment_delete',
                handler : function(grid, rowIndex) {
                    Ext.Msg.confirm("请确认", "是否删除该数据？", function(button, text) {
                        if (button == "yes") {
                            var rec = scriptCompare_store.getAt(rowIndex);
                            Ext.Ajax.request({
                                url: 'deleteCompareFile.do',
                                method: 'POST',
                                sync: true,
                                params: {
                                    iid: rec.get('iid')
                                },
                                success: function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    if (success) {
                                        Ext.Msg.alert('提示', '删除成功！');
                                        scriptCompare_store.load();
                                    } else {
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                    }
                                },
                                failure: function(result, request) {
                                    secureFilterRs(result, "删除失败！");
                                }
                            });
                        }
                    });
                }
            },
            {
                text : '原文件下载',
                iconCls: 'script_download',
                handler : function(grid, rowIndex) {
                    var rec = scriptCompare_store.getAt(rowIndex);
                    window.location.href = 'downloadExcelAttachment.do?iid='+rec.get('iid')+"&type=old";
                }
            },
		    {
                text : '比对结果下载',
                iconCls: 'script_download',
                handler : function(grid, rowIndex) {
                    var rec = scriptCompare_store.getAt(rowIndex);
                    window.location.href = 'downloadExcelAttachment.do?iid='+rec.get('iid')+"&type=new";
                }
		    }
		]
	}
    ];

    var scriptCompareGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store: scriptCompare_store,
        border: false,
        cls:'customize_panel_back',
        columnLines: true,
        columns: scriptservice_columns,
        ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar'
    });

    var search_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        baseCls:'customize_gray_back',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            baseCls:'customize_gray_back',
            border : false,
            items:['->',{
                xtype: 'button',
                text: '比对文件上传',
                cls: 'Common_Btn',
                handler: function()
                {
                    importExcel();
                }
            }
            ]
        }]
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptcompare_area",
        layout: 'border',
        border: false,
        bodyCls:'service_platform_bodybg',
        cls:'customize_panel_back',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight()-modelHeigth ,
        items: [search_form,scriptCompareGrid]
    });



    contentPanel.on('resize',
        function() {
            mainPanel.setWidth(contentPanel.getWidth());
            mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
        }
    );

    function compareTitle(dataArray){

        var selModel = Ext.create('Ext.selection.CheckboxModel', {
            //checkOnly: true
        });

        var titleColumns = [{ text: '序号', xtype:'rownumberer', width: 40 },
            { text: '表头名称',  dataIndex: 'excelTitle',width:120,flex:1}];

        var titleData = dataArray;
        var titleStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            data: titleData,
            fields: ["excelTitle"]
        });
        var  titleGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'east',
            autoScroll: true,
            store: titleStore,
            width: 280,
            height: 400,
            selModel: selModel,
            title:'表头信息',
            border:true,
            columnLines : true,
            columns : titleColumns,
            listeners: {
                select: function (e, record, index, eOpts) {
                    if (titleArray.indexOf(record.get('excelTitle')) == -1) {
                        titleArray.push(record.get('excelTitle'));
                    }
                },
                deselect: function( e, record, index, eOpts ){
                    if(titleArray.indexOf(record.get('excelTitle'))>-1) {
                        titleArray.remove(record.get('excelTitle'));
                    }
                }
            }
        });
        var compareTitleWindows;
        var compareTitleForm
        compareTitleForm = Ext.create('Ext.panel.Panel',{
            border : false,
            items : [titleGrid],
            buttonAlign : 'center',
            buttons :[{
                text : '确定',
                handler :function(){
                    compareTitleWindows.close();
                }
            },{
                text : '取消',
                handler : function() {
                    compareTitleWindows.close();
                    titleArray = [];
                }
            }]
        });
        compareTitleWindows = Ext.create('widget.window', {
            title : '选择比对表头',
            layout : 'fit',
            height : 480,
            closeAction : 'destroy',
            width : 300,
            modal : true,
//    		autoScroll : true,
            items : [ compareTitleForm ]
        });
        compareTitleWindows.show();
    }

    function importExcel(){
    	var uploadWindows;
        var uploadForm;
        uploadForm = Ext.create('Ext.form.FormPanel',{
        	border : false,
        	items : [{
                frame:false,
            	xtype: 'filefield',
    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
                id: 'fileName',
    			fieldLabel: '选择文件',
    			labelWidth: 80,
                allowBlank : true,
    			anchor: '90%',
    			margin: '10 10 0 40',
    			buttonText: '浏览'
            }],
            buttonAlign : 'center',
            buttons :[{
            	text : '提交文件',
            	handler :upExeclData
            },{
            	text : '取消',
            	handler : function() {
                    uploadWindows.close();
                }
            },{
                text : '选择比对表头',
                handler :function (){
                    upExeclTitleData();
                }
            }
            ]
        });
        uploadWindows = Ext.create('Ext.window.Window', {
    		title : '比对文件导入',
    		layout : 'fit',
    		height : 300,
    		width : 600,
    		modal : true,
//    		autoScroll : true,
    		items : [ uploadForm ],
    		listeners : {
    			close : function(g, opt) {
    				uploadForm.destroy();
    			}
    		}
    	});
        uploadWindows.show();

        //获取表头
        function upExeclTitleData(){
            var form = uploadForm.getForm();
            var hdupfile=form.findField("fileName").getValue();
            if(hdupfile==''){
                Ext.Msg.alert('提示',"请选择文件...");
                return ;
            }
            uploadTitleTemplate(form);
        }
        function uploadTitleTemplate(form) {
            if (form.isValid()) {
                form.submit({
                    url: 'getCompareTitle.do',
                    method : 'POST',
                    success: function(form, action) {
                        var dataList = Ext.decode(action.response.responseText).data;
                        var dataArray = [];
                        dataList.forEach( function(item){
                            var storeData = {
                                "excelTitle": item.excelTitle
                            }
                            dataArray.push(storeData);
                        });
                        compareTitle(dataArray);
                    }
                });
            }
        }
        //提交excel
        function upExeclData(){
        	var form = uploadForm.getForm();
    		var hdupfile=form.findField("fileName").getValue();
    		if(hdupfile==''){
    			Ext.Msg.alert('提示',"请选择文件...");
    			return ;
    		}
    		uploadTemplate(form);
        }
        function uploadTemplate(form) {
      	   if (form.isValid()) {
             form.submit({
                 url: 'uploadCompareFile.do',
                 params:{titles:titleArray},
                 success: function(form, action) {
                    var getMessage = Ext.decode(action.response.responseText).message;
                    Ext.Msg.alert('提示',getMessage);
                    uploadWindows.close();
                    scriptCompareGrid.ipage.moveFirst();
                    scriptCompare_store.reload();
                     titleArray = [];
                 },
                 failure: function(form, action) {
                     var msg = Ext.decode(action.response.responseText).message;
                     var mess = Ext.create('Ext.window.MessageBox', {
                     minHeight : 110,
                     minWidth : 500,
                     resizable : false
                   });
                     Ext.Msg.alert('提示',msg);
                 }
             });
      	   }
      	 }
    }
});


