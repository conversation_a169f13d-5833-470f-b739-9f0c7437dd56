var ss_datasource_pageBar;
Ext.onReady(function() {
	var globalSelectRecord;
	Ext.tip.QuickTipManager.init();
    var dataSourceStore;
    var dataSourceGrid;
    // 清理主面板的各种监听时间
    destroyRubbish();
      var dataTypeModel = Ext.create('Ext.data.Store', {
        fields: ['id','name'],
        autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getDatabaseType.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
        
    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        } , {
	      name : 'iid',
	      type : 'string'
	    }]
    });
    var sysDataStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageByUserId.do?projectFlag=1',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },{
            name: 'icpId',
            type: 'string'
        },{
            name: 'IDBIP',
            type: 'string'
        },{
            name: 'ISYSID',
            type: 'long'
        },
        {
            name: 'IDSNAME',
            type: 'string'
        },
        {
            name: 'IDSIP',
            type: 'string'
        },
        {
            name: 'IDSDRIVER',
            type: 'string'
        },
        {
        	name: 'IDSDRIVER_ORI',
        	type: 'string'
        },
        {
            name: 'IDSPORT',
            type: 'string'
        },
        {
            name: 'IDSUSER',
            type: 'string'
        },
        {
            name: 'IDSPWD',
            type: 'string'
        },
        {
            name: 'rIDSPWD',
            type: 'string'
        },
        {
            name: 'IDSROLE',
            type: 'string'
        },
        {
            name: 'IDSINSTANCE',
            type: 'string'
        },
        {
            name: 'IDBURL',
            type: 'string'
        },
        {
        	name: 'IDBURL_ORI',
        	type: 'string'
        },
        {
            name: 'IDBTYPE',
            type: 'string'
        },
        {
        	name: 'IDBTYPE_ORI',
        	type: 'string'
        },
        {
        	name : 'permission',
        	type : 'boolean'
        },
        {
        	name : 'ICOLLECT',
        	type : 'string'
        },
        {
        	name : 'IDBENCODE',
        	type : 'string'
        },
        {
        	name : 'ICLIENTENCODE',
        	type : 'string'
        },
        {
        	name : 'suUser',
        	type : 'string'
        }
        ]
    });
    var sysField =  Ext.create("Ext.form.field.ComboBox",{
        fieldLabel: '应用系统',
        labelWidth: 110,
        labelAlign: 'right',
        name: 'sysField',
    	triggerAction: 'all',
    	editable : true,
        queryMode: 'local',
        emptyText: "--请选择应用系统--",
        displayField: 'sysName',
        valueField: 'iid',
        store: sysDataStore,
        padding: '0 5 0 0',
        columnWidth: .98,
        listeners : {
            beforequery : function(e) {  
                var combo = e.combo;     
                if(!e.forceAll){     
                    var value = e.query;     
                    combo.store.filterBy(function(record, id){     
                        var text = record.get(combo.displayField);     
                        return (text.toUpperCase().indexOf(value.toUpperCase())!=-1);     
                    });  
                    combo.expand();     
                    return false;     
                }  
            }  
        }
    });
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 30,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'dataSourceList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var dataBaseTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "oracel"
        },
        {
            "name": "sysdba"
        },
        {
            "name": "system"
        },
        {
            "name": "nomal"
        }
        ]
    });
    
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
            baseName: sysField.getRawValue(),
            dataType:1
        };

        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
  
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        hidden: true
    },{
        text: '主键',
        dataIndex: 'ISYSID',
        hidden: true
    },{
        text: 'A主键',
        dataIndex: 'icpId',
        hidden: true
    },
    {
        text: 'Agent地址',
        dataIndex: 'IDSIP',
        width: 100,
        renderer:getAgentIP
    },
    {
        text: '数据库别名',
        dataIndex: 'ICOLLECT',
        minWidth: 120,
        flex: 1,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
          	metadata.tdAttr = 'data-qtip="' + value + '"';
    			return value;
    		}
    },
    {
        text: '应用系统名称',
        dataIndex: 'IDSNAME',
        minWidth: 180,
        flex: 1,
        editor : new Ext.form.field.ComboBox (
            {
                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
                editable : false,// 是否可输入编辑
                store : sysDataStore,
                queryMode : 'local',
                displayField : 'sysName',
                valueField : 'sysName'
            }),
			renderer : function(value, p, record) {
				var ruleIndex = sysDataStore.find("iid", value,0,true,true);
				if (ruleIndex != -1) {
					return sysDataStore.getAt(ruleIndex).data.sysName;
				}else{
					return value;
				}
			}
    },{
    	text: '数据库文件字符集',
        dataIndex: 'IDBENCODE',
        width: 120,
        editor : {
			allowBlank : true
		}
    },{
    	text: '数据库应用用户',
        dataIndex: 'suUser',
        width: 120,
        editor : {
			allowBlank : true
		}
    },{
    	text: '数据库环境配置',
        dataIndex: 'ICLIENTENCODE',
        width: 110,
        renderer: function(value, p, record, rowIndex) {
        	var iid = record.get('IID');
        	if(value >'0'){
        		return '<div><a href="javascript:void(0)" onclick="envcfg('+iid+')">' + '&nbsp;修改配置' + '</a></div>';
        	}else{
        		return '<div><a href="javascript:void(0)" onclick="envcfg('+iid+')">' + '&nbsp;维护配置' + '</a></div>';
        	}
        }
    },
    {
        text: '数据库IP',
        dataIndex: 'IDBIP',
        width: 110,
        editor: {
            xtype: 'textfield',
            maxLength: 15
        }
    },
    {
        text: '数据库端口',
        dataIndex: 'IDSPORT',
        width: 90,
        editor : {
			allowBlank : false,
			xtype : 'numberfield',
			maxValue : 65535,
			minValue : 1
		}
    },{
        text: '数据库服务名',
        dataIndex: 'IDSINSTANCE',
        width: 100,
        editor: {
            allowBlank: false
        }
    },
    {
    	text: 'DB类型',
        dataIndex: 'IDBTYPE',
        width: 80,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataTypeModel,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            listeners : {
            	'change' : function(combo,nv,ov) {
//            		var sel = dataSourceGrid.getSelectionModel().getSelection();
            		if(nv.toUpperCase()=='DB2'){
            			globalSelectRecord.data.IDSDRIVER = "com.ibm.db2.jcc.DB2Driver";
                   	}else if (nv.toUpperCase()=='ORACLE'){
                   		globalSelectRecord.data.IDSDRIVER = "oracle.jdbc.driver.OracleDriver";
                   	}else if (nv.toUpperCase()=='MYSQL'){
                   		globalSelectRecord.data.IDSDRIVER = "com.mysql.jdbc.Driver";
                   	}else if (nv.toUpperCase()=='POSTGRESQL'){
                   		globalSelectRecord.data.IDSDRIVER = "org.postgresql.Driver";
                   	}else if (nv.toUpperCase()=='REDIS'){
                        globalSelectRecord.data.IDSDRIVER = "redis.Driver";
                    }else if (nv.toUpperCase()=='MONGODB'){
                        globalSelectRecord.data.IDSDRIVER = "MongoDB.Driver";
                    }
            		if(nv.toUpperCase()==globalSelectRecord.data.IDBTYPE_ORI) {
            			globalSelectRecord.data.IDSDRIVER = globalSelectRecord.data.IDSDRIVER_ORI;
            		}
            		dataSourceGrid.getView().refresh();
            	}
            }
        })
    },{
    	text: '驱动类',
        dataIndex: 'IDSDRIVER',
        width: 220
    },
    {
    	text: 'DBURL参数',
        dataIndex: 'IDBURL',
        minWidth: 150,
        flex: 1,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
          	metadata.tdAttr = 'data-qtip="' + value + '"';
    			return value;
    		}
    },
    {
        text: '用户名',
        dataIndex: 'IDSUSER',
        width: 100,
        editor: {
            allowBlank: false
        }
    },{
	    xtype: 'checkcolumn',
	    text: '堡垒机获取密码',
	    dataIndex: 'permission'
	},
    {
        header: '密码',
        hidden:true,
        dataIndex: 'IDSPWD',
        width: 100,
        editor: new Ext.form.TextField({
            inputType: 'password',
            allowBlank: true,
            allowNegative: true
        }),
        renderer: retNotView
    },
    {
        header: '确认密码',
        hidden:true,
        dataIndex: 'rIDSPWD',
        width: 100,
        editor: new Ext.form.TextField({
            inputType: 'password',
            //设置输入类型为password
            allowBlank: true,
            //			 			minLength :8,
            //			 			maxLength :20,
            allowNegative: true
        }),
        renderer: retNotView
    },
    {
        text: '用户角色',
        dataIndex: 'IDSROLE',
        width: 80,
        hidden: true,
//        flex: 1,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataBaseTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name'
        })
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 80,
        renderer: function(value, p, record, rowIndex) {
        	var ip=record.get('IDSIP');
        	var iid = record.get('IID');
            return '<div>' + '<a href="javascript:void(0)" onclick="testvalid('+iid+',\''+ip+'\')">' + '&nbsp;测试连通性' + '</a>' + '</div>';
        }
    }];
    
    
    function retNotView(value) {
        var coun = "";
        if (value.trim().length > 0) {
            for (var i = 0; i < value.length; i++) {
                coun = coun + "*";
            }
        }
        if (value.trim() == "") {
            coun = "";
        }
        return coun;
    }
	
    function checkIP(ip) {
	var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
		if (reg.test(ip)) {
			return true;
		} else {
			return false;
		}
    }
    // 分页工具
   ss_datasource_pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    
    var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		padding : '5 0 5 0',
		dockedItems : [ {
            xtype: 'toolbar',
            border : false,
            baseCls:'customize_gray_back',  
            items: [sysField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                	QueryMessage();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },'->',
            {
                text: '增加',
                cls: 'Common_Btn',
                //iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveDatabase
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                //iconCls:'sc_delete',
                disabled: true,
                handler: deleteDataBase
            }]
        }]
	});
    dataSourceGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
        selModel: selModel,
        cls:'customize_panel_back',
        plugins: [cellEditing],
        padding : panel_margin,
        border: true,
//        bbar: ss_datasource_pageBar,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'cellclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	},
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	}
        }
    });

    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		ss_datasource_pageBar.moveFirst();
		/*dataSourceStore.reload({
			params : {
				start : 0,
				limit : 16,
				baseName : nameField.getValue()
			}
		});*/
	}
    
    dataSourceGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "grid_areaDataSourceDc",
        layout: 'border',
        width : contentPanel.getWidth(),
        baseCls:'customize_panel_back',
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function clearQueryWhere() {
    	sysField.setValue('');
    }
    function add() {
        var store = dataSourceGrid.getStore();
        var p = {
        		IDSIP: '',
        		IDBIP: '',
        		ICOLLECT: '',
        		IDSNAME: '',
        		IDSDRIVER: '',
        		IDSPORT: '50000',
        		IDSUSER: '',
        		IDSPWD: '',
        		rIDSPWD:'',
        		IDSROLE: 'sysdba',
        		ISYSID: '0',
        		permission: true,
        		IDSINSTANCE:'',
        		IDBENCODE:''
        };
        store.insert(0, p);
        dataSourceGrid.getView().refresh();
    }
    function saveDatabase() {
        var m = dataSourceStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,len = m.length; i < len; i++) {
        	var IDBENCODE = m[i].get("IDBENCODE").trim();
        	if ("" != IDBENCODE && null != IDBENCODE && fucCheckLength(IDBENCODE)>200) {
        		setMessage('数据库文件字符集长度不能超过200字符！');
        		return;
        	}
            var ICOLLECT = m[i].get("ICOLLECT").trim();
            if ("" != ICOLLECT && null != ICOLLECT && fucCheckLength(ICOLLECT)>200) {
                setMessage('数据库别名长度不能超过200字符！');
                return;
            }
            var suUser = m[i].get("suUser").trim();
            if ("" != suUser && null != suUser && fucCheckLength(suUser)>200) {
            	setMessage('数据库应用用户长度不能超过200字符！');
            	return;
            }
        	
            var IDSNAME = m[i].get("IDSNAME");
            if ("" == IDSNAME || null == IDSNAME) {
                setMessage('数据源名不能为空！');
                return;
            }
            var ruleIndex = sysDataStore.find("sysName", IDSNAME,0,true,true);
            var sysId=0;
			if (ruleIndex != -1) {
				sysId= sysDataStore.getAt(ruleIndex).data.iid;
			}
            m[i].data.IDSNAME=IDSNAME;
            m[i].data.ISYSID=sysId;
            var IDBIP = m[i].get("IDBIP").trim();
            if ("" == IDBIP || null == IDBIP) {
                setMessage('数据库IP不能为空！');
                return;
            }
            if(!checkIP(IDBIP)){
            	setMessage('数据库IP格式不正确！');
                return;
            }
            var IDSPORT = m[i].get("IDSPORT").trim();
            if ("" == IDSPORT || null == IDSPORT) {
                setMessage('数据库端口不能为空！');
                return;
            }
            
            if(!isNumber(IDSPORT)){
            	setMessage('数据库端口号格式不正确！');
                return;
            }
            var IDBTYPE = m[i].get("IDBTYPE").trim();
            if ("" == IDBTYPE || null == IDBTYPE) {
                setMessage('DB类型不能为空！');
                return;
            }
            var IDBURL = m[i].get("IDBURL").trim();
//            if ("" == IDBURL || null == IDBURL) {
//                setMessage('DBURL不能为空！');
//                return;
//            }
            
            var IDSDRIVER = m[i].get("IDSDRIVER").trim();
            if ("" == IDSDRIVER || null == IDSDRIVER) {
                setMessage('驱动不能为空！');
                return;
            }
//            if(IDBTYPE=="redis" && IDSDRIVER !="redis.Driver"){
//                setMessage('redis类型的驱动类必须是redis.Driver！');
//                return;
//            }
//            if(IDBTYPE=="MongoDB" && IDSDRIVER !="MongoDB.Driver"){
//                setMessage('MongoDB类型的驱动类必须是MongoDB.Driver！');
//                return;
//            }
            var IDSUSER = m[i].get("IDSUSER").trim();
            if (("" == IDSUSER || null == IDSUSER) && IDBTYPE!='redis') {
                setMessage('用户名不能为空！');
                return;
            }
            var permission = m[i].get("permission");
            if(permission!=true){
            	var IDSPWD = m[i].get("IDSPWD").trim();
            	if ("" == IDSPWD || null == IDSPWD) {
            		setMessage('密码不能为空！');
            		return;
            	}
            	var rIDSPWD = m[i].get("rIDSPWD").trim();
            	if (IDSPWD != rIDSPWD) {
            		setMessage('两次密码输入不一致！');
            		return;
            	}
            }
            
            var IDSROLE = m[i].get("IDSROLE").trim();
            if ("" == IDSROLE || null == IDSROLE) {
                setMessage('用户角色不能为空！');
                return;
            }
            var IDSINSTANCE = m[i].get("IDSINSTANCE").trim();
            if ("" == IDSINSTANCE || null == IDSINSTANCE) {
                setMessage('数据库服务名不能为空！');
                return;
            }

            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveDatasource.do',
            method: 'POST',
            params: {
                jsonData: jsonData,
                dcType:'1'
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    dataSourceStore.reload();
                } 
                Ext.Msg.alert('提示', message);
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function isNumber(val){
    	if (val == null) return true;
        var regObj = /^\d*$/g;
        if (regObj.test(val)) {
            return true;
        } else {
            return false;
        }
    }
    
    function deleteDataBase() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                             }
                    });
                    
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteDataSource.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                            	dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }
    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }


    //AgentIP显示
    function getAgentIP(value, p, record) 
    {
    	var displayValue = value;
    	if(null==displayValue || ""==displayValue)
    	{
    		displayValue= "AgentIP";
    	}
    	return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"cpipSelectShow('"
    	+record.get("IID")+"','"+record.get("IAGENTIP")+"');\">"
    				+"<span class='abc'>"
    				+displayValue
    				+"</span>"
    			+"</a>";
    } 
    
    function closeWin(){
    	QueryMessage();
    }
});
function envcfg(iid) {
	if(iid=='' || iid=='0'){
		Ext.Msg.alert('提示', "请先保存后在进行数据库环境变量配置！");
		return ;
	}
	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',{});
	/** AgentInfo列表Model* */
	Ext.define ('cfgModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	    		{
	    			name : 'iid',
	    			type : 'long'
	    		},
	            {
	                name : 'idsId',
	                type : 'string'
	            },
	            {
	                name : 'iorder',
	                type : 'string'
	            },
	            {
	                name : 'iname',
	                type : 'string'
	            },
	            {
	                name : 'ivalue',
	                type : 'string'
	            },
	            {
	                name : 'dataDesc',
	                type : 'string'
	            }
	    ]
	});
	/** 所属设备列表Store* */
	var cfgStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'cfgModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getDatabaseCfgList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	cfgStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			dsId: iid
		};
		Ext.apply (cfgStore.proxy.extraParams, new_params);
	});

	/** add按钮* */
	var addButtonForBSMSelected = Ext.create ("Ext.Button",
			{
				cls : 'Common_Btn',
				text : '新增',
				hidden:true,
				handler : addBtnSel
			});
	/** 确定按钮* */
	var saveButtonForBSMSelected = Ext.create ("Ext.Button",
			{
				cls : 'Common_Btn',
				text : '保存',
				handler : onOkBtnSel
			});
	/** del按钮* */
	var delButtonForBSMSelected = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    disabled : true,
			    itemId: 'delete',
				hidden:true,
			    text : '删除',
			    handler : delOkBtnSel
			});
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 1
	});
	/** cp列表GridPanel* */
	var cfgGrid = Ext.create ('Ext.grid.Panel',
	{
	    width : '100%',
	    height: '100%',
	    store : cfgStore,
	    cls:'customize_panel_back',
	    selModel : selModel,
	    plugins: [cellEditing],
	    region : 'center',
	    border : true,
	    columnLines : true,
	    columns : [
				{
				    text : 'ID',
				    hidden:true,
				    dataIndex : 'iid'
				},
	            {
	                text : '数据库ID',
	                hidden:true,
//	                hideable: false,
	                dataIndex : 'idsId'
	            },
	            {
	                text : '属性名称',
	                dataIndex : 'iname',
	                width : 200
	            },
	            {
	                text : '属性值',
	                dataIndex : 'ivalue',
	                minWidth:150,
	                flex : 1,
	                editor: {
	                    allowBlank: false
	                }
	            },{
	                text : '顺序号',
	                dataIndex : 'iorder',
	                width:80
	            }, {
	                text : '参照值',
	                dataIndex : 'dataDesc',
	                minWidth:150,
	                flex : 1,
	                editor: {
	                    allowBlank: false
	                }
	            }
	    ],
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        baseCls:'customize_gray_back',  
		        items : [
		        	addButtonForBSMSelected,saveButtonForBSMSelected,delButtonForBSMSelected
		        ]
		    }
	    ],
	    collapsible : false
	});
	
	/** 判断删除按钮是否可用* */
	cfgGrid.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		cfgGrid.down('#delete').setDisabled(selections.length === 0);
	});
	
	/*********** IP选择窗口  ****************************************************************/
	/** 设备变更窗口* */
	var win = Ext.create ('widget.window',
	{
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    modal : true,
	    title : '配置环境变量属性值',
	    closable : true,
	    closeAction : 'destroy',
	    width : contentPanel.getWidth ()*0.6,
	    height : contentPanel.getHeight ()*0.8,
	    layout : 'border',
	    items : [ cfgGrid ]
	});
	win.show();
	
	/************* 方法 ********************************************************************/
	
   function addBtnSel() {
        var store = cfgGrid.getStore();
        var ro = store.getCount();
        var p = {
        		iid: '0',
        		idsId: iid,
        		iname: '',
        		ivalue: '',
        		iorder: ro+1,
        };
        store.insert(0, p);
        cfgGrid.getView().refresh();
    }
	
	/** 确定按钮* */
   function onOkBtnSel (btn)
   {
       var jsonData = "[";
       for (var i = 0,len = cfgStore.getCount(); i < len; i++) {
    	   var record = cfgStore.getAt (i);
    	   var n = 0;
           var iorder = record.data.iorder;
           var iname = record.data.iname;
           var ivalue = record.data.ivalue;
           if ("" == iorder || null == iorder) {
        	   Ext.Msg.alert('提示', '顺序号不能为空！');
               return;
           }
           if (fucCheckLength(iname) > 250) {
        	   Ext.Msg.alert('提示', '属性名称不能超过250字符！');
        	   return;
           }
           if (fucCheckLength(ivalue) > 250) {
        	   Ext.Msg.alert('提示', '属性值不能超过250字符！');
               return;
           }
//           for ( var k = 0; k < cfgStore.getCount(); k++) {
//   			var record = cfgStore.getAt(k);
//   			var order = record.data.iorder;
//   			if (order == iorder) {
//   					n = n + 1;
//   				}
//   			}
//	   		if (n > 1) {
//	   			Ext.MessageBox.alert("提示", "顺序号不能重复！");
//	   			return;
//	   		}
           var ss = Ext.JSON.encode(record.data);
           if (i == 0) jsonData = jsonData + ss;
           else jsonData = jsonData + "," + ss;
       }
       jsonData = jsonData + "]";
       Ext.Ajax.request({
           url: 'saveDatasourceEnv.do',
           method: 'POST',
           params: {
               jsonData: jsonData
           },
           success: function(response, request) {
               var success = Ext.decode(response.responseText).success;
               var message = Ext.decode(response.responseText).message;
               Ext.Msg.alert('提示', message);
               if (success) {
            	   cfgStore.reload();
               }
               ss_datasource_pageBar.moveFirst();
           },
           failure: function(result, request) {
               secureFilterRs(result, "操作失败！");
           }
       });
   
   }
	function delOkBtnSel (btn)
	{
		var records = cfgGrid.getSelectionModel().getSelection ();
		if (records.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
        Ext.Msg.confirm("请确认", "是否要删除数据源?",
        function(button, text) {
            if (button == "yes") {
                var ids = [];
                Ext.Array.each(records,
                function(record) {
                    var iid = record.get('iid');
                    // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                    if (iid) {
                    	ids.push(iid);
                    }else{
                    	 cfgStore.remove(record);
                    }
                });
                
                if(ids.length>0){
                  Ext.Ajax.request({
                    url: 'deleteDataSourceEnv.do',
                    params: {
                        deleteIds: ids.join(',')
                    },
                    method: 'POST',
                    success: function(response, opts) {
                        var success = Ext.decode(response.responseText).success;
                        // 当后台数据同步成功时
                        cfgStore.reload();
                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                        ss_datasource_pageBar.moveFirst();
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
                } else{
                	cfgGrid.getView().refresh();
                }
            }
        });
    
	}
}
function testvalid(iid,ip) {
	
	if(ip=='' || ip=='AgentIP'){
		 Ext.Msg.alert('提示', "请先绑定AgentIP,在进行连通性验证");
		return ;
	}
	
    Ext.Msg.wait('处理中，请稍后...', '提示');  
    Ext.Ajax.request({
       method:"POST",
       timeout:300000,// 5分钟
       url:"chkDbResourceValid.do",
       params:{iid:iid},
       success: function(response, request) {
           var message = Ext.decode(response.responseText).message;
               Ext.Msg.alert('提示', message);
       },
       failure:function(form, action){
           switch (action.failureType) {
           case Ext.form.action.Action.CLIENT_INVALID:
               Ext.Msg.alert('提示', '连接异常！');
               break;
           case Ext.form.action.Action.SERVER_INVALID:
               Ext.Msg.alert('提示', action.result.message);
           }
       }
   });
}
function cpipSelectShow(dsid,agentIp)
{
//	var systemId = groupId;
	if(null==dsid || ""==dsid || dsid==undefined)
	{
		Ext.Msg.show({
		     title:'提示',
		     msg: '请先保存记录成功后，再选择设备！',
		     buttons: Ext.Msg.OK,
		     icon: Ext.Msg.INFO
		});
		return;
	}
	var isAble = 1;//默认可编辑
//	alert('select cpip ...');return;
	
	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',
    {
//	    checkOnly : true
		mode : 'SIMPLE',
		listeners : {
			beforeselect:function( me, record, index, eOpts ){
				me.deselectAll();
			}
		}
    });
	/** AgentInfo列表Model* */
	Ext.define ('cpModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	    		{
	    			name : 'icpId',
	    			type : 'long'
	    		},
	            {
	                name : 'icpName',
	                type : 'string'
	            },
	            {
	                name : 'icpIp',
	                type : 'string'
	            },
	            {
	                name : 'iagentIp',
	                type : 'string'
	            },
	            {
	                name : 'icpPort',
	                type : 'long'
	            },
	            {
	                name : 'popular',
	                type : 'string'
	            },
	            {
	                name : 'checked',
	                type : 'boolean'
	            }
	    ]
	});
	/** 所属设备列表Store* */
	var cpStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
//	    remoteSort: true,
	    model : 'cpModel',
	    pageSize : limit,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'cpinfo/getCpInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	cpStore.on ('beforeload', function (store, options)
	{
		var ipBeginStr = "";
		var ipEndStr = "";
		if (null != ipBetween.getValue ())
		{
			ipBeginStr = ipBetween.getValue ().trim ();
		}
		if (null != ipEnd.getValue ())
		{
			ipEndStr = ipEnd.getValue ().trim ();
		}
		var new_params =
		{
			dsidStr: dsid,
		    ipBegin : ipBeginStr,
		    ipEnd : ipEndStr
		};
		Ext.apply (cpStore.proxy.extraParams, new_params);
	});
	cpStore.on ('load', function (store, options)
	{
		//选中ip，复选框勾选
		for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			if(true==record.data.checked){
				selModel.select(record,true,false);
			}
		}
	});
	/** 起始ip* */
	var ipBetween = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 50,
	    // fieldLabel : '设备查询',
	    emptyText : '--请输入查询IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5',
	    listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	bsPageBar.moveFirst();
                }
            }
        }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
		hidden:true,
	    labelWidth : 19,
	    fieldLabel : '至',
	    emptyText : '--请输入截止IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	/** 查询按钮* */
	var queryButtonForBSMSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '查询',
	    handler : queryWhereSel
	});
	/** 确定按钮* */
	var saveButtonForBSMSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    disabled : true,
	    itemId: 'save',
	    text : '确定',
	    handler : onOkBtnSel
	});
	/** 分页工具栏* */
	var bsPageBar = Ext.create ('Ext.ux.ideal.grid.PagingToolbar',
	{
	    store : cpStore,
	    pageSize: limit,
	    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	/** cp列表GridPanel* */
	var cpGrid = Ext.create ('Ext.grid.Panel',
	{
	    width : '100%',
	    height: '100%',
	    store : cpStore,
	    cls:'customize_panel_back',
	    selModel : selModel,
	    region : 'center',
	    border : true,
	    columnLines : true,
	    flex : 2,
	    columns : [
				{
				    text : '设备ID',
				    sortable : true,
				    dataIndex : 'icpId',
				    flex : 1
				},
	            {
	                text : '设备名称',
	                sortable : true,
	                hidden:true,
	                hideable: false,
	                dataIndex : 'icpName',
	                flex : 1
	            },
	            {
	                text : '设备IP',
	                sortable : true,
	                dataIndex : 'icpIp',
	                hidden:true,
	                hideable: false,
	                flex : 1
	            },
	            {
	                text : 'AgentIP',
	                sortable : true,
	                dataIndex : 'iagentIp',
	                flex : 1
	            },{
	                text : '环境',
	                sortable : true,
	                dataIndex : 'popular',
	                flex : 1,
	                renderer:function(value, p, record, rowIndex) {
	                	if(value=='0'){return "测试";}
	                	if(value=='1'){return "生产";}
	                }
	            },
	            {
	                text : '端口',
	                sortable : true,
	                dataIndex : 'icpPort',
	                flex : 1
	            }
	    ],
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        baseCls:'customize_gray_back',  
		        items : [
		                ipBetween, ipEnd, queryButtonForBSMSelected, saveButtonForBSMSelected
		        ]
		    }
	    ],
	    collapsible : false,
	    bbar : bsPageBar
	});
	
	/** 判断删除按钮是否可用* */
	cpGrid.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		cpGrid.down('#save').setDisabled(selections.length === 0);
	});
	
	/*********** IP选择窗口  ****************************************************************/
	/** 设备变更窗口* */
	var win = Ext.create ('widget.window',
	{
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    modal : true,
	    title : 'Agent选择',
	    closable : true,
//	    closeAction : 'hide',
	    closeAction : 'destroy',
	    // animateTarget: this,
	    width : 800,
	    height : 542,
	    layout : 'border',
	    items : [ cpGrid ]
	});
	win.show();
	//关闭窗口刷新任务信息grid
	win.on("close",function(self, eOpts){
//		Ext.getCmp("cmpGridId").store.reload();
		bsPageBar.moveFirst();
	});
	
	/************* 方法 ********************************************************************/
	/** 查询 * */
	function queryWhereSel ()
	{
		var ipBetweenValue = ipBetween.getValue ().trim ();
		var ipEndValue = ipEnd.getValue ().trim ();
		
		if(checkIsNotEmpty (ipBetweenValue) && checkIsNotEmpty (ipEndValue) )
		{
			if (checkIsNotEmpty (ipBetweenValue) && !isYesIp (ipBetweenValue))
			{
				Ext.Msg.alert ('提示', '请输入合法开始IP进行查询！');
				return;
			}
			if (checkIsNotEmpty (ipEndValue) && !isYesIp (ipEndValue))
			{
				Ext.Msg.alert ('提示', '请输入合法结束IP进行查询！');
				return;
			}
		}
		bsPageBar.moveFirst ();
	}
	
	/** 确定按钮，选择Agent* */
	function onOkBtnSel (btn)
	{
//		alert('确定');return;
		var record = cpGrid.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		if (record.length > 1)
		{
			Ext.Msg.alert ('提示', "只能选择一个设备!");
			return;
		}
		//组织拼接勾选Ip数据
		var cpid = record[0].get('icpId');
//		alert('<dsid,cpid>='+"<"+dsid+","+cpid+">");
		//请求后台
		Ext.Ajax.request (
		{
			url : 'cpinfo/selectCp.do',
		    params :
		    {
		    	dsid : dsid,
		    	cpid : cpid
		    },
		    method : 'POST',
		    success : function (response, opts)
		    {
			    var success = Ext.decode (response.responseText).success;
			    // 当后台数据同步成功时
			    if (success)
			    {
			    	win.close();
			    	ss_datasource_pageBar.moveFirst();
			    }
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
		    }
		    ,failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
//		window.parent.refresh();
//		parent.refresh();
	}
}
//function cpipSelectShow(dsid,agentIp)
//{
//	alert('select cpip ... dsid='+dsid);return;
//}