var ss_datasource_pageBar;
Ext.onReady(function() {
	var globalSelectRecord;
	var limit = 30;
	Ext.tip.QuickTipManager.init();
    var dataSourceStore;
    var dataSourceGrid;
    // 清理主面板的各种监听时间
   //destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IDSNAME',
            type: 'string'
        },
        {
            name: 'IDSIP',
            type: 'string'
        },
        {
            name: 'IDSDRIVER',
            type: 'string'
        },
        {
        	name: 'IDSDRIVER_ORI',
        	type: 'string'
        },
        {
            name: 'IDSPORT',
            type: 'string'
        },
        {
            name: 'IDSUSER',
            type: 'string'
        },
        {
            name: 'IDSPWD',
            type: 'string'
        },
        {
            name: 'rIDSPWD',
            type: 'string'
        },
        {
            name: 'IDSROLE',
            type: 'string'
        },
        {
            name: 'IDSINSTANC<PERSON>',
            type: 'string'
        }
        ,
        {
            name: 'IDBUR<PERSON>',
            type: 'string'
        },
        {
        	name: 'IDBURL_ORI',
        	type: 'string'
        },
        {
            name: 'IDBTYPE',
            type: 'string'
        },
        {
        	name: 'IDBTYPE_ORI',
        	type: 'string'
        }
        ]
    });
    
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 16,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'dataSourceList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var dataBaseTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "oracel"
        },
        {
            "name": "sysdba"
        },
        {
            "name": "system"
        },
        {
            "name": "nomal"
        }
        ]
    });
    
    var dataBaseTypeStore2 = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "oracle"
        },
        {
    		"name": "db2"
    	},
    	{
    		"name": "mysql"
    	}
        ]
    });
    var dataStore = Ext.create('Ext.data.Store', {
    	fields: ['name'],
    	data: [{
    		"name": "com.ibm.db2.jcc.DB2Driver"
    	}, {
            "name": "oracle.jdbc.driver.OracleDriver"
        },
        {
            "name": "com.mysql.jdbc.Driver"
        }
    	]
    });
    var dataStore2 = Ext.create('Ext.data.Store', {
    	fields: ['name'],
    	data: [{
    		"name": "**********************"
    	}, {
    		"name": "*******************************"
    	},
    	{
    		"name": "************************"
    	}
    	]
    });
    
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
            baseName: nameField.getValue()
        };

        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
//   var olddbtypefordriver=""; 
//   var olddbtypeforUrl=""; 
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '数据源名称',
        dataIndex: 'IDSNAME',
        width: 180,
        flex: 1,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: 'IP地址',
        dataIndex: 'IDSIP',
        width: 100,
//        flex: 1,
//        editor: {
//            allowBlank: false
//        }
        renderer:getAgentIP
    },
    {
        text: '端口',
        dataIndex: 'IDSPORT',
        width: 80,
        hidden: true,
//        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
    	text: 'DB类型',
        dataIndex: 'IDBTYPE',
        width: 70,
//        editor: {
//            allowBlank: false
//        }
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataBaseTypeStore2,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            listeners : {
            	'change' : function(combo,nv,ov) {
//            		var sel = dataSourceGrid.getSelectionModel().getSelection();
            		if(nv=='db2'){
            			globalSelectRecord.data.IDSDRIVER = "com.ibm.db2.jcc.DB2Driver";
            			globalSelectRecord.data.IDBURL = '***********************';
                   	}else if (nv=='oracle'){
                   		globalSelectRecord.data.IDSDRIVER = "oracle.jdbc.driver.OracleDriver";
                   		globalSelectRecord.data.IDBURL = '*******************************';
                   	}else if (nv=='mysql'){
                   		globalSelectRecord.data.IDSDRIVER = "com.mysql.jdbc.Driver";
                   		globalSelectRecord.data.IDBURL = '************************';
                   	}
            		if(nv==globalSelectRecord.data.IDBTYPE_ORI) {
            			globalSelectRecord.data.IDSDRIVER = globalSelectRecord.data.IDSDRIVER_ORI;
            			globalSelectRecord.data.IDBURL = globalSelectRecord.data.IDBURL_ORI;
            		}
            		dataSourceGrid.getView().refresh();
            	}
            }
        })
    },{
    	text: '驱动类',
        dataIndex: 'IDSDRIVER',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: true
        }
    },
    {
    	text: 'DBURL',
        dataIndex: 'IDBURL',
        width: 300,
        flex: 1,
        editor: {
            allowBlank: true
        }
    },
    {
        text: '用户名',
        dataIndex: 'IDSUSER',
        width: 100,
//        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        header: '密码',
        dataIndex: 'IDSPWD',
        width: 100,
        editor: new Ext.form.TextField({
            inputType: 'password',
            allowBlank: true,
            allowNegative: true,
            listeners : {
                focus: function (combo, nv, ov) {
                    // alert(combo.getValue(), 11)
                    if (combo.getValue() == '********') {
                        combo.setValue('')
                    }
                },
                blur: function (combo, nv, ov) {
                    if (combo.getValue() == '') {
                        combo.setValue('********')
                    }
                }
            }
        }),
        renderer: retNotView
    },
    {
        header: '确认密码',
        dataIndex: 'rIDSPWD',
        width: 100,
        editor: new Ext.form.TextField({
            inputType: 'password',
            //设置输入类型为password
            allowBlank: true,
            //			 			minLength :8,
            //			 			maxLength :20,
            allowNegative: true,
            listeners : {
                focus: function (combo, nv, ov) {
                    // alert(combo.getValue(), 11)
                    if (combo.getValue() == '********') {
                        combo.setValue('')
                    }
                },
                blur: function (combo, nv, ov) {
                    if (combo.getValue() == '') {
                        combo.setValue('********')
                    }
                }
            }
        }),
        renderer: retNotView
    },
    {
        text: '用户角色',
        dataIndex: 'IDSROLE',
        width: 80,hidden: true,
//        flex: 1,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataBaseTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name'
        })
    },
    {
        text: '数据库实例',
        dataIndex: 'IDSINSTANCE',
        width: 100, hidden: true,
//        flex: 1,
        editor: {
            allowBlank: false
        }
    }/*,
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 150,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state'); // 其实是requestID
            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showbsTypeWindow(' + iid + ','+ state + ')">' + '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;配置类型' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
        }
    }*/];
    
    
    function retNotView(value) {
        var coun = "";
        if (value.trim().length > 0) {
            for (var i = 0; i < value.length; i++) {
                coun = coun + "*";
            }
        }
        if (value.trim() == "") {
            coun = "";
        }
        return coun;
    }
    
    // 分页工具
   ss_datasource_pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '数据源名称',
        labelWidth: 100,
        labelAlign: 'right',
        name: 'dataBaseNameParam',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	ss_datasource_pageBar.moveFirst();
                }
            }
        }
    });
    
    var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		padding : '5 0 5 0',
		dockedItems : [ {
            xtype: 'toolbar',
            border : false,
            baseCls:'customize_gray_back',  
            items: [nameField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                	QueryMessage();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },'->',
            {
                text: '增加',
                cls: 'Common_Btn',
                //iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveDatabase
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                //iconCls:'sc_delete',
//                disabled: true,
                handler: deleteDataBase
            }]
        }]
	});
    dataSourceGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
        selModel: selModel,
        cls:'customize_panel_back',
        plugins: [cellEditing],
        padding : panel_margin,
        border: true,
//        bbar: ss_datasource_pageBar,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	}
        }
    });

    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		ss_datasource_pageBar.moveFirst();
		/*dataSourceStore.reload({
			params : {
				start : 0,
				limit : 16,
				baseName : nameField.getValue()
			}
		});*/
	}
    
  /*  dataSourceGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });
*/
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptService_grid_areaDataSource_DataSource",
        layout: 'border',
        width : contentPanel.getWidth(),
        baseCls:'customize_panel_back',
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function clearQueryWhere() {
    	nameField.setValue('');
    }
    function add() {
        var store = dataSourceGrid.getStore();
        var p = {
        		IDSIP: '',
        		IDSNAME: '',
        		IDSDRIVER: '',
        		IDSPORT: '50000',
        		IDSUSER: '',
        		IDSPWD: '',
        		rIDSPWD:'',
        		IDSROLE: 'sysdba',
        		IDSINSTANCE:'entegor'
        };
        store.insert(0, p);
        dataSourceGrid.getView().refresh();
    }
    function saveDatabase() {
        var m = dataSourceStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var IDSNAME = m[i].get("IDSNAME").trim();
            if ("" == IDSNAME || null == IDSNAME) {
                setMessage('数据源名不能为空！');
                return;
            }
            
//            var IDSIP = m[i].get("IDSIP").trim();
//            if ("" == IDSIP || null == IDSIP) {
//                setMessage('IP不能为空！');
//                return;
//            }
//            if(!checkIp(IDSIP)){
//            	setMessage('IP格式不正确！');
//                return;
//            }
//            
            var IDSPORT = m[i].get("IDSPORT").trim();
            if ("" == IDSPORT || null == IDSPORT) {
                setMessage('端口不能为空！');
                return;
            }
            
            if(!isNumber(IDSPORT)){
            	setMessage('端口号格式不正确！');
                return;
            }
            var IDBTYPE = m[i].get("IDBTYPE").trim();
            if ("" == IDBTYPE || null == IDBTYPE) {
                setMessage('DB类型不能为空！');
                return;
            }
            var IDBURL = m[i].get("IDBURL").trim();
            if ("" == IDBURL || null == IDBURL) {
                setMessage('DBURL不能为空！');
                return;
            }
            
            var IDSDRIVER = m[i].get("IDSDRIVER").trim();
            if ("" == IDSDRIVER || null == IDSDRIVER) {
                setMessage('驱动不能为空！');
                return;
            }
            
            var IDSUSER = m[i].get("IDSUSER").trim();
            if ("" == IDSUSER || null == IDSUSER) {
                setMessage('用户名不能为空！');
                return;
            }
            
            var IDSPWD = m[i].get("IDSPWD").trim();
            if ("" == IDSPWD || null == IDSPWD) {
                setMessage('密码不能为空！');
                return;
            }
            
            var rIDSPWD = m[i].get("rIDSPWD").trim();
            if (IDSPWD != rIDSPWD) {
                setMessage('两次密码输入不一致！');
                return;
            }
            
            var IDSROLE = m[i].get("IDSROLE").trim();
            if ("" == IDSROLE || null == IDSROLE) {
                setMessage('用户角色不能为空！');
                return;
            }
            var IDSINSTANCE = m[i].get("IDSINSTANCE").trim();
            if ("" == IDSINSTANCE || null == IDSINSTANCE) {
                setMessage('数据库实例不能为空！');
                return;
            }
            
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveDatasource.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    dataSourceStore.modified = [];
                    dataSourceStore.reload();
                    Ext.Msg.alert('提示', message);
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function isNumber(val){
    	if (val == null) return true;
        var regObj = /^\d*$/g;
        if (regObj.test(val)) {
            return true;
        } else {
            return false;
        }
    }
    
    function checkIp(val){
    	var regObj = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/   
    	 if (regObj.test(val)) {
             return true;
         } else {
             return false;
         }
    }
    
    function deleteDataBase() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                             }
                    });
                    
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteDataSource.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                            	dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }
    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }


    //AgentIP显示
    function getAgentIP(value, p, record) 
    {
    	var displayValue = value;
    	if(null==displayValue || ""==displayValue)
    	{
    		displayValue= "AgentIP";
    	}
    	return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"cpipSelectShow('"
    	+record.get("IID")+"','"+record.get("IDSIP")+"');\">"
    				+"<span class='abc'>"
    				+displayValue
    				+"</span>"
    			+"</a>";
    } 
    
    function closeWin(){
    	QueryMessage();
    }
});

function cpipSelectShow(dsid,agentIp)
{
//	var systemId = groupId;
	if(null==dsid || ""==dsid || dsid==undefined)
	{
		Ext.Msg.show({
		     title:'提示',
		     msg: '请先保存记录成功后，再选择设备！',
		     buttons: Ext.Msg.OK,
		     icon: Ext.Msg.INFO
		});
		return;
	}
	var isAble = 1;//默认可编辑
//	alert('select cpip ...');return;
	
	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',
    {
//	    checkOnly : true
		mode : 'SIMPLE',
		listeners : {
			beforeselect:function( me, record, index, eOpts ){
				me.deselectAll();
			}
		}
    });
	/** AgentInfo列表Model* */
	Ext.define ('cpModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	    		{
	    			name : 'icpId',
	    			type : 'long'
	    		},
	            {
	                name : 'icpName',
	                type : 'string'
	            },
	            {
	                name : 'icpIp',
	                type : 'string'
	            },
	            {
	                name : 'iagentIp',
	                type : 'string'
	            },
	            {
	                name : 'icpPort',
	                type : 'long'
	            },
	            {
	                name : 'popular',
	                type : 'string'
	            },
	            {
	                name : 'checked',
	                type : 'boolean'
	            }
	    ]
	});
	/** 所属设备列表Store* */
	var cpStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
//	    remoteSort: true,
	    model : 'cpModel',
	    pageSize : limit,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'cpinfo/getCpInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	cpStore.on ('beforeload', function (store, options)
	{
		var ipBeginStr = "";
		var ipEndStr = "";
		if (null != ipBetween.getValue ())
		{
			ipBeginStr = ipBetween.getValue ().trim ();
		}
		if (null != ipEnd.getValue ())
		{
			ipEndStr = ipEnd.getValue ().trim ();
		}
		var new_params =
		{
			dsidStr: dsid,
		    ipBegin : ipBeginStr,
		    ipEnd : ipEndStr
		};
		Ext.apply (cpStore.proxy.extraParams, new_params);
	});
	cpStore.on ('load', function (store, options)
	{
		//选中ip，复选框勾选
		for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			if(true==record.data.checked){
				selModel.select(record,true,false);
			}
		}
	});
	/** 起始ip* */
	var ipBetween = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 50,
	    // fieldLabel : '设备查询',
	    emptyText : '--请输入查询IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5',
	    listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	bsPageBar.moveFirst();
                }
            }
        }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
		hidden:true,
	    labelWidth : 19,
	    fieldLabel : '至',
	    emptyText : '--请输入截止IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	/** 查询按钮* */
	var queryButtonForBSMSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '查询',
	    handler : queryWhereSel
	});
	/** 确定按钮* */
	var saveButtonForBSMSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    disabled : true,
	    text : '确定',
	    handler : onOkBtnSel
	});
	/** 分页工具栏* */
	var bsPageBar = Ext.create ('Ext.ux.ideal.grid.PagingToolbar',
	{
	    store : cpStore,
	    pageSize: limit,
	    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	/** cp列表GridPanel* */
	var cpGrid = Ext.create ('Ext.grid.Panel',
	{
	    width : '100%',
	    height: '100%',
	    store : cpStore,
	    cls:'customize_panel_back',
	    selModel : selModel,
	    region : 'center',
	    border : true,
	    columnLines : true,
	    flex : 2,
	    columns : [
				{
				    text : '设备ID',
				    sortable : true,
				    dataIndex : 'icpId',
				    flex : 1
				},
	            {
	                text : '设备名称',
	                sortable : true,
	                hidden:true,
	                hideable: false,
	                dataIndex : 'icpName',
	                flex : 1
	            },
	            {
	                text : '设备IP',
	                sortable : true,
	                dataIndex : 'icpIp',
	                hidden:true,
	                hideable: false,
	                flex : 1
	            },
	            {
	                text : 'AgentIP',
	                sortable : true,
	                dataIndex : 'iagentIp',
	                flex : 1
	            },{
	                text : '环境',
	                sortable : true,
	                dataIndex : 'popular',
	                flex : 1,
	                renderer:function(value, p, record, rowIndex) {
	                	if(value=='0'){return "测试";}
	                	if(value=='1'){return "生产";}
	                }
	            },
	            {
	                text : '端口',
	                sortable : true,
	                dataIndex : 'icpPort',
	                flex : 1
	            }
	    ],
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        items : [
		                ipBetween, ipEnd, queryButtonForBSMSelected, saveButtonForBSMSelected
		        ]
		    }
	    ],
	    collapsible : false,
	    bbar : bsPageBar
	});
	
	/** 判断删除按钮是否可用* */
	cpGrid.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
//		if(1!=isAble){
//			saveButtonForBSMSelected.setDisabled (selections.length === 0);
//		}
		saveButtonForBSMSelected.setDisabled(false);
	});
	
	/*********** IP选择窗口  ****************************************************************/
	/** 设备变更窗口* */
	var win = Ext.create ('widget.window',
	{
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    modal : true,
	    title : 'Agent选择',
	    closable : true,
//	    closeAction : 'hide',
	    closeAction : 'destroy',
	    // animateTarget: this,
	    width : 800,
	    height : 542,
	    layout : 'border',
	    items : [ cpGrid ]
	});
	win.show();
	//关闭窗口刷新任务信息grid
	win.on("close",function(self, eOpts){
//		Ext.getCmp("cmpGridId").store.reload();
		bsPageBar.moveFirst();
	});
	
	/************* 方法 ********************************************************************/
	/** 查询 * */
	function queryWhereSel ()
	{
		var ipBetweenValue = ipBetween.getValue ().trim ();
		var ipEndValue = ipEnd.getValue ().trim ();
		
		if(checkIsNotEmpty (ipBetweenValue) && checkIsNotEmpty (ipEndValue) )
		{
			if (checkIsNotEmpty (ipBetweenValue) && !isYesIp (ipBetweenValue))
			{
				Ext.Msg.alert ('提示', '请输入合法开始IP进行查询！');
				return;
			}
			if (checkIsNotEmpty (ipEndValue) && !isYesIp (ipEndValue))
			{
				Ext.Msg.alert ('提示', '请输入合法结束IP进行查询！');
				return;
			}
		}
		bsPageBar.moveFirst ();
	}
	
	/** 确定按钮，选择Agent* */
	function onOkBtnSel (btn)
	{
//		alert('确定');return;
		var record = cpGrid.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		if (record.length > 1)
		{
			Ext.Msg.alert ('提示', "只能选择一个设备!");
			return;
		}
		//组织拼接勾选Ip数据
		var cpid = record[0].get('icpId');
//		alert('<dsid,cpid>='+"<"+dsid+","+cpid+">");
		//请求后台
		Ext.Ajax.request (
		{
			url : 'cpinfo/selectCp.do',
		    params :
		    {
		    	dsid : dsid,
		    	cpid : cpid
		    },
		    method : 'POST',
		    success : function (response, opts)
		    {
			    var success = Ext.decode (response.responseText).success;
			    // 当后台数据同步成功时
			    if (success)
			    {
			    	win.close();
			    }
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    ss_datasource_pageBar.moveFirst()
		    }
		    ,failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
//		window.parent.refresh();
//		parent.refresh();
	}
}
//function cpipSelectShow(dsid,agentIp)
//{
//	alert('select cpip ... dsid='+dsid);return;
//}