var limit = 30;
//设备选择窗口
function cpipSelectShow1(dsid,agentIp)
{
//	var systemId = groupId;
	if(null==dsid || ""==dsid || dsid==undefined)
	{
		Ext.Msg.show({
		     title:'提示',
		     msg: '请先保存记录成功后，再选择设备！',
		     buttons: Ext.Msg.OK,
		     icon: Ext.Msg.INFO
		});
		return;
	}
	var isAble = 1;//默认可编辑
//	alert('select cpip ...');return;
	
	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',
    {
//	    checkOnly : true
		mode : 'SIMPLE',
		listeners : {
			beforeselect:function( me, record, index, eOpts ){
				me.deselectAll();
			}
		}
    });
	/** AgentInfo列表Model* */
	Ext.define ('cpModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	    		{
	    			name : 'icpId',
	    			type : 'long'
	    		},
	            {
	                name : 'icpName',
	                type : 'string'
	            },
	            {
	                name : 'icpIp',
	                type : 'string'
	            },
	            {
	                name : 'iagentIp',
	                type : 'string'
	            },
	            {
	                name : 'icpPort',
	                type : 'long'
	            },
	            {
	                name : 'popular',
	                type : 'string'
	            },
	            {
	                name : 'checked',
	                type : 'boolean'
	            }
	    ]
	});
	/** 所属设备列表Store* */
	var cpStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
//	    remoteSort: true,
	    model : 'cpModel',
	    pageSize : limit,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'cpinfo/getCpInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	cpStore.on ('beforeload', function (store, options)
	{
		var ipBeginStr = "";
		var ipEndStr = "";
		if (null != ipBetween.getValue ())
		{
			ipBeginStr = ipBetween.getValue ().trim ();
		}
		if (null != ipEnd.getValue ())
		{
			ipEndStr = ipEnd.getValue ().trim ();
		}
		var new_params =
		{
			dsidStr: dsid,
		    ipBegin : ipBeginStr,
		    ipEnd : ipEndStr
		};
		Ext.apply (cpStore.proxy.extraParams, new_params);
	});
	cpStore.on ('load', function (store, options)
	{
		//选中ip，复选框勾选
		for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			if(true==record.data.checked){
				selModel.select(record,true,false);
			}
		}
	});
	/** 起始ip* */
	var ipBetween = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 50,
	    // fieldLabel : '设备查询',
	    emptyText : '--请输入查询IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5',
	    listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	bsPageBar.moveFirst();
                }
            }
        }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
		hidden:true,
	    labelWidth : 19,
	    fieldLabel : '至',
	    emptyText : '--请输入截止IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	/** 查询按钮* */
	var queryButtonForBSMSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '查询',
	    handler : queryWhereSel
	});
	/** 确定按钮* */
	var saveButtonForBSMSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    disabled : true,
	    text : '确定',
	    handler : onOkBtnSel
	});
	/** 分页工具栏* */
	var bsPageBar = Ext.create ('Ext.ux.ideal.grid.PagingToolbar',
	{
	    store : cpStore,
	    pageSize: limit,
	    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	/** cp列表GridPanel* */
	var cpGrid = Ext.create ('Ext.grid.Panel',
	{
	    width : '100%',
	    height: '100%',
	    store : cpStore,
	    cls:'customize_panel_back',
	    selModel : selModel,
	    region : 'center',
	    border : true,
	    columnLines : true,
	    flex : 2,
	    columns : [
				{
				    text : '设备ID',
				    sortable : true,
				    dataIndex : 'icpId',
				    flex : 1
				},
	            {
	                text : '设备名称',
	                sortable : true,
	                hidden:true,
	                hideable: false,
	                dataIndex : 'icpName',
	                flex : 1
	            },
	            {
	                text : '设备IP',
	                sortable : true,
	                dataIndex : 'icpIp',
	                hidden:true,
	                hideable: false,
	                flex : 1
	            },
	            {
	                text : 'AgentIP',
	                sortable : true,
	                dataIndex : 'iagentIp',
	                flex : 1
	            },{
	                text : '环境',
	                sortable : true,
	                dataIndex : 'popular',
	                flex : 1,
	                renderer:function(value, p, record, rowIndex) {
	                	if(value=='0'){return "测试";}
	                	if(value=='1'){return "生产";}
	                }
	            },
	            {
	                text : '端口',
	                sortable : true,
	                dataIndex : 'icpPort',
	                flex : 1
	            }
	    ],
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        baseCls:'customize_gray_back',  
		        items : [
		                ipBetween, ipEnd, queryButtonForBSMSelected, saveButtonForBSMSelected
		        ]
		    }
	    ],
	    collapsible : false,
	    bbar : bsPageBar
	});
	
	/** 判断删除按钮是否可用* */
	cpGrid.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
//		if(1!=isAble){
//			saveButtonForBSMSelected.setDisabled (selections.length === 0);
//		}
		saveButtonForBSMSelected.setDisabled(false);
	});
	
	/*********** IP选择窗口  ****************************************************************/
	/** 设备变更窗口* */
	var win = Ext.create ('widget.window',
	{
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    modal : true,
	    title : 'Agent选择',
	    closable : true,
//	    closeAction : 'hide',
	    closeAction : 'destroy',
	    // animateTarget: this,
	    width : 800,
	    height : 542,
	    layout : 'border',
	    items : [ cpGrid ]
	});
	win.show();
	//关闭窗口刷新任务信息grid
	win.on("close",function(self, eOpts){
//		Ext.getCmp("cmpGridId").store.reload();
		bsPageBar.moveFirst();
	});
	
	/************* 方法 ********************************************************************/
	/** 查询 * */
	function queryWhereSel ()
	{
		var ipBetweenValue = ipBetween.getValue ().trim ();
		var ipEndValue = ipEnd.getValue ().trim ();
		
		if(checkIsNotEmpty (ipBetweenValue) && checkIsNotEmpty (ipEndValue) )
		{
			if (checkIsNotEmpty (ipBetweenValue) && !isYesIp (ipBetweenValue))
			{
				Ext.Msg.alert ('提示', '请输入合法开始IP进行查询！');
				return;
			}
			if (checkIsNotEmpty (ipEndValue) && !isYesIp (ipEndValue))
			{
				Ext.Msg.alert ('提示', '请输入合法结束IP进行查询！');
				return;
			}
		}
		bsPageBar.moveFirst ();
	}
	
	/** 确定按钮，选择Agent* */
	function onOkBtnSel (btn)
	{
//		alert('确定');return;
		var record = cpGrid.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		if (record.length > 1)
		{
			Ext.Msg.alert ('提示', "只能选择一个设备!");
			return;
		}
		//组织拼接勾选Ip数据
		var cpid = record[0].get('icpId');
//		alert('<dsid,cpid>='+"<"+dsid+","+cpid+">");
		//请求后台
		Ext.Ajax.request (
		{
			url : 'cpinfo/selectCp.do',
		    params :
		    {
		    	dsid : dsid,
		    	cpid : cpid
		    },
		    method : 'POST',
		    success : function (response, opts)
		    {
			    var success = Ext.decode (response.responseText).success;
			    // 当后台数据同步成功时
			    if (success)
			    {
			    	win.close();
			    }
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
		    }
		    ,failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
//		window.parent.refresh();
//		parent.refresh();
	}
}

/* 解决IE下trim问题 */
String.prototype.trim = function ()
{
	return this.replace (/(^\s*)|(\s*$)/g, "");
};
