Ext.onReady(function() {
	var bsTypeWindow;
    var agBsManagerStore;
    var agBsManagerGrid;
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.define('agBsManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'bsName',
            type: 'string'
        }]
    });
    
    agBsManagerStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 15,
        model: 'agBsManangerModel',
        proxy: {
            type: 'ajax',
            url: 'agBsManager/queryAgBsModel.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    agBsManagerStore.on('beforeload', function(store, options) {
        var new_params = {
            bsName: nameField.getValue()
        };

        Ext.apply(agBsManagerStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '类别主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类别名称',
        dataIndex: 'bsName',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    }, 
    {
//        text: '操作',
//        dataIndex: 'stepOperation',
//        width: 150,
//        renderer: function(value, p, record, rowIndex) {
//            var iid = record.get('iid'); // 其实是requestID
//            if(!iid) {
//            	iid = -1;
//            }
//            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showbsTypeWindow('+iid+')">' + '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;配置二级分类' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//        }
    	text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'stepOperation',
		width : 150,
		items : [{
			text : '配置二级分类',
			iconCls : 'script_set',					 
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid; 
				if(!iid) {
	            	iid = -1;
	            }
					showbsTypeWindow(iid);
			}
		}]
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agBsManagerStore,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '类别名称',
        labelWidth: 100,
        labelAlign: 'right',
        name: 'bsNameParam',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			dock : 'top',
			border:false,
			items : [nameField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                text: '增加',
                cls: 'Common_Btn',
                //iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveBsManager
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                //iconCls:'sc_delete',
//                disabled: true,
                handler: deleteBsManager
            }, '-',{
                itemId: 'recover',
                text: '返回',
                cls: 'Common_Btn',
                hidden : !requestFromC3CharForAgentGroup,
                //iconCls:'sc_return',
                handler: function(){
                	 popNewTab('脚本看板', 'pandect1.do', {},10, true);
                }
            }
			]
		} ]
	});
    agBsManagerGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
//        id: 'bsmGrid',
        store: agBsManagerStore,
        cls:'customize_panel_back',
        selModel: selModel,
        plugins: [cellEditing],
        padding : grid_space,
//        bbar: pageBar,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border: true,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        dockedItems: [{
            border:false,
            items: [search_form]
        }]
    });

//    agBsManagerGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
//        agBsManagerGrid.down('#delete').setDisabled(selections.length === 0);
//    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptService_grid_areaAgentGroupBS",
        layout: 'border',
        width : contentPanel.getWidth(),
        cls:'customize_panel_back',
        bodyCls:'service_platform_bodybg',
        height :contentPanel.getHeight() - modelHeigth,
        border : true,
        bodyPadding : grid_margin,
        items: [search_form,agBsManagerGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
		if(bsTypeWindow) {
			bsTypeWindow.center();
		}
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
 
    function clearQueryWhere() {
        search_form.getForm().findField("bsNameParam").setValue('');
    }
    function add() {
        var store = agBsManagerGrid.getStore();
        var p = {
            iid: '',
            bsName: '',
            stepOperation: ''
        };
        store.insert(0, p);
        agBsManagerGrid.getView().refresh();
    }
    function saveBsManager() {
        var m = agBsManagerStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var n = 0;
            var bsName = m[i].get("bsName").trim();

            if ("" == bsName || null == bsName) {
                setMessage('一级分类不能为空！');
                return;
            }
            if (fucCheckLength(bsName) > 200) {
                setMessage('一级分类不能超过200字符！');
                return;
            }
            
            for (var k = 0; k < agBsManagerStore.getCount(); k++) {
				var record = agBsManagerStore.getAt(k);
				var cname = record.data.bsName;
				if (cname.trim() == bsName) {
					n = n + 1;
				}
			}
			if (n > 1) {
				setMessage('类别名称不能有重复！');
				return;
			}

            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveAgBsManager.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    agBsManagerStore.modified = [];
                    agBsManagerStore.reload();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                }
            },
            // failure : function(result, request) {
            // Ext.Msg.alert('提示', '保存失败！');
            // }
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function deleteBsManager() {
        var data = agBsManagerGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除一级分类?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
//                    var flags=0;
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
//                        	if (iid!=1 && iid!=2&& iid!=3&& iid!=4&& iid!=5&& iid!=6){
                        		ids.push(iid);
//                        	}else{
//                        		flags=1;
//                        	}
                        }else{
                        	 agBsManagerStore.remove(record);
                        }
                    });
 
                    if(ids.length>0){
						//校验要删除的分类是否绑定了分组
                    	var returnflag = checkIsExists(ids);
                    	if(!returnflag){
                    		return;
                    	}
                        Ext.Ajax.request({
                        url: 'deleteAgBsModel.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                agBsManagerStore.reload();
//                                if(flags==1){
//                                	Ext.Msg.alert('提示', "特定类别不可删除,其他类别"+Ext.decode(response.responseText).message);
//                                }else{
                                	Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
//                                }
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    agBsManagerGrid.getView().refresh();
                    }
                }
            });
        }
    }
    
      /**
     * @description 检查要删除的数据是否绑定过agent分组
     * @return false 绑定过分组， true 未绑定过
     */
    function checkIsExists(ids){
    	var flag = "";
    	Ext.Ajax.request({
                        url: 'checkAgIsExistsAgroup.do',
                        params: {
                            deleteIds: ids,
                            level:"1"
                        },
                        async : false,
                        method: 'POST',
                        success: function(response, opts) {
                              var message   = Ext.decode(response.responseText).message;
                               if(message !=""){
                               		Ext.Msg.alert('提示',message);
                              	 	flag = false;
                               }else{
                               		flag= true;
                               }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    return flag;
    }
    
    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
    
    function showbsTypeWindow(agBsManagerId) {
    	if (agBsManagerId == -1) {
    		Ext.Msg.alert('提示', '请先保存类别后再配置类型。');
    		return;
    	}
    	if (bsTypeWindow == undefined || !bsTypeWindow.isVisible()) {
    		bsTypeWindow = Ext.create('Ext.window.Window', {
    			title: '二级分类',
    			modal: true,
    			closeAction: 'destroy',
    			constrain: true,
    			autoScroll: true,
    			width: contentPanel.getWidth(),
    			height: contentPanel.getHeight(),
    			draggable: false,
    			// 禁止拖动
    			resizable: false,
    			// 禁止缩放
    			layout: 'fit',
    			loader: {
    				url: 'forwardAgBsType.do',
    				params: {
    					agBsManagerId: agBsManagerId
    				},
    				autoLoad: true,
    				scripts: true
    			}
    		});
    	}
    	bsTypeWindow.show();
    }
});


