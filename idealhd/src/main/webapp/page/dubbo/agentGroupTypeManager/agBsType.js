Ext.onReady(function() {
    // 清理主面板的各种监听时间
//    destroyRubbish();
    Ext.define('agBsTypeModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'bsTypeName',
            type: 'string'
        }]
    });
    var agBsTypeStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 15,
        model: 'agBsTypeModel',
        proxy: {
            type: 'ajax',
            url: 'agBsManager/queryAgBsType.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var bsTypeColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '类型主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型名称',
        dataIndex: 'bsTypeName',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store : agBsTypeStore,
    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
    	dock : 'bottom',
    	displayInfo : true,
    	emptyMsg : '找不到任何记录'
    	});

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var agBsTypeGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        id: 'agBsTypeGrid',
        store: agBsTypeStore,
        selModel: selModel,
        plugins: [cellEditing],
        padding : grid_space,
        bbar: pageBar,
        border: false,
        columnLines: true,
        columns: bsTypeColumns,
        animCollapse: false,
        dockedItems: [{
            xtype: 'toolbar',
//            baseCls:'customize_gray_back',  
            items: [{
                text: '增加',
                cls: 'Common_Btn',
               // iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveBsType
            },
            '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
               // iconCls:'sc_delete',
//                disabled: true,
                handler: deleteBsType
            }]
        }]
    });
    agBsTypeStore.on('beforeload', function(store, options) {
        var queryparams = {
            agBsid: agBsId
        };
        Ext.apply(agBsTypeStore.proxy.extraParams, queryparams);
    });
   /* agBsTypeGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        agBsTypeGrid.down('#delete').setDisabled(selections.length === 0);
    });*/
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "agBsType_div",
        layout: 'border',
        width : '100%',
        height :contentPanel.getHeight()-50,
        border : false,
        items: [agBsTypeGrid]
    });

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
    function add() {
        var store = agBsTypeGrid.getStore();
        var p = {
            iid: '',
            bsTypeName: ''
        };
        store.insert(0, p);
        agBsTypeGrid.getView().refresh();
    }

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        agBsTypeGrid.setHeight(contentPanel.getHeight() - 25);
        agBsTypeGrid.setWidth(contentPanel.getWidth());
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(agBsTypeGrid);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

 
    function saveBsType() {
        var m = agBsTypeStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var n = 0;
            var bsTypeName = m[i].get("bsTypeName").trim();

            if ("" == bsTypeName || null == bsTypeName) {
                setMessage('二级分类名称不能为空！');
                return;
            }
            if (fucCheckLength(bsTypeName) > 200) {
                setMessage('二级分类名称不能超过200字符！');
                return;
            }
            for (var k = 0; k < agBsTypeStore.getCount(); k++) {
				var record = agBsTypeStore.getAt(k);
				var cname = record.data.bsTypeName;
				if (cname.trim() == bsTypeName) {
					n = n + 1;
				}
			}
			if (n > 1) {
				setMessage('类型名称不能有重复！');
				return;
			}

            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveAgBsType.do',
            method: 'POST',
            params: {
                jsonData: jsonData,
                agBsId: agBsId
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    agBsTypeStore.modified = [];
                    agBsTypeStore.load();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                }
            },
            // failure : function(result, request) {
            // Ext.Msg.alert('提示', '保存失败！');
            // }
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function deleteBsType() {
        var data = agBsTypeGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除二级分类?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                            ids.push(iid);
                        }else{
                        	 agBsTypeStore.remove(record);
                             }
                    });
                    if (ids.length>0) {
                    	//校验要删除的分类是否绑定了分组
                    	var returnflag = checkIsExists(ids);
                    	if(!returnflag){
                    		return;
                    	}
                        Ext.Ajax.request({
                        url: 'deleteAgBsType.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                agBsTypeStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    agBsTypeGrid.getView().refresh();
                    }
                }
            });
        }
    }
    
     /**
     * @description 检查要删除的数据是否绑定过agent分组
     * @return false 绑定过分组， true 未绑定过
     */
    function checkIsExists(ids){
    	var flag = "";
    	Ext.Ajax.request({
                        url: 'checkAgIsExistsAgroup.do',
                        params: {
                            deleteIds: ids,
                            level:"2"
                        },
                        async : false,
                        method: 'POST',
                        success: function(response, opts) {
                              var message   = Ext.decode(response.responseText).message;
                               if(message !=""){
                               		Ext.Msg.alert('提示',message);
                              	 	flag = false;
                               }else{
                               		flag= true;
                               }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    return flag;
    }
   
});