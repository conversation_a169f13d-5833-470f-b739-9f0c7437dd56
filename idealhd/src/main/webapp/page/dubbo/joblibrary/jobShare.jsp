<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<html>
<head>
<script type="text/javascript">
var userId = <%=request.getAttribute("userId")%>;
</script>
<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/joblibrary/jobShare.js"></script>
</head>
<body>
<div id="scriptService_jobshare" style="width: 100%;height: 100%">
</body>
</html>