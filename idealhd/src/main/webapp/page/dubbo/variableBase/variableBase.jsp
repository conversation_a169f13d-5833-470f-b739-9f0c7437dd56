<%@ page import="com.ideal.ieai.core.Environment" %>
<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
    <%
        boolean batchQuery = Environment.getInstance().getBatchQuerySwitch();
        boolean ipOrComNameQuery = Environment.getInstance().getQueryIpOrComNameSwitch();
        //4A密码类型变量展示开关  true展示，false不展示
        boolean fourAShowSwitch = Environment.getInstance().getScriptFourASwitch();
        //分类目录
        boolean JlnxScriptClassSwitch = Environment.getInstance().getJlnxScriptClassSwitch();
        boolean scriptEditBookSwitch = Environment.getInstance().getScriptEditBookSwitch();
    %>
    <script>
        //按照ip查询还是按照计算机名查询开关（true为山东城商需求，按照ip查询；false为bankCode001需求，按照计算机名查询）
        var ipOrNameSwitch = "<%=ipOrComNameQuery%>";
        //任务管理批量查询开关
        var batchQuerySwitch = "<%=batchQuery%>";
        //4A密码类型变量展示开关  true展示，false不展示
        var fourAShowSwitch = <%=fourAShowSwitch%>;
        var JlnxScriptClassSwitch=<%=JlnxScriptClassSwitch%>;
        var scriptEditBookSwitch = <%=scriptEditBookSwitch%>;
    </script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/variableBase/variableBase.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/taskAuditingPageIPSearch.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/basicScript/setScope.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/webService/aes.js"></script>
</head>
<body>
<div id="variablePanel" style="width: 100%;height: 100%"></div>
</body>
</html>