var width = 270;
var width1 = 250;
var typeParam=undefined;
var nameParam=undefined;
var attributeParam=undefined;
var scopeParam=undefined;
var statusParam=undefined;
var classStatusValue=undefined;
var scopeSearchEmptyText = '请输入Agent IP、资源组名称、用户名、用户组名';
if (scriptEditBookSwitch) {
    scopeSearchEmptyText = '请输入用户名、用户组名';
}
var store;
var checkItems = [];
var grid;
//正则表达式增加特殊字符校验   !@#%^&,()[]{}|;=+`~
//var regex = /^[\w\\/\-_:!@#%^&,;=+`~\(\)\[\]\{\}|.*?$\u4e00-\u9fa5][\w\\/\-_:!@#%^&,;=+`~\(\)\[\]\{\}|.*?$\u4e00-\u9fa5]*$/;
// var regex = /['"\s]/;
var regex = /^((?![=,;'"—\s]).)*$/;
var keyRegex = /^[^'‘’\s\u4e00-\u9fa5][^'‘’\s\u4e00-\u9fa5]*$/;
//var regexText = '仅支持输入数字字母\\/$*?.!@#%^&,()[]{}|;=+`~:-_';
var regexText = '不支持单引号、双引号、空格、中文横杠、逗号、分号、等号';
var keyRegexText = '字典的键不支持输入中文,中英文格式单引号';
//必填样式
var dataForm;
var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
//4A密码弹窗数据iid，后台判断insert or update 使用
var variableSetIID = "";
var queryName;
var queryAttribute;
var queryType;
var queryStatus;
var queryScope;
var classStatusV;
Ext.onReady(function () {
    destroyRubbish();
    var classIdV;
    var parentiid;
    var variableBaseWin;
    var treeStore = Ext.create('Ext.data.TreeStore', {
        autoLoad: JlnxScriptClassSwitch,
        fields:['iid', 'iname', 'iparentid'],
        proxy: {
            type: 'ajax',
            url: 'variableBase/getClassList.do?classType=1'
        },
        root: {
            expanded: JlnxScriptClassSwitch
        }
    });
    if(JlnxScriptClassSwitch){
        treeStore.on('load',function(thisStore, node, records, successful, eOpts )
        {
            if(classIdV!=null){
                for (let i = 0; i <records.length ; i++) {
                    selectNext(records[i]);
                }
            }else{
                treePanel.getSelectionModel().select(records[0]);
                classIdV=records[0].get("iid");
            }
            // treePanel.getSelectionModel().select(records[0]);
            // classId=records[0].get("iid");
            store.load();
        });
    }
    function selectNext(record) {
        if (record.raw.iid == classIdV) {
            treePanel.getSelectionModel().select(record);
        } else {
            var children = record.childNodes
            if (children.length == 0) {

            } else {
                for (let i = 0; i < children.length; i++) {
                    selectNext(children[i])
                }
            }
        }
    }

    var treePanel = Ext.create('Ext.tree.Panel', {
        store:treeStore,
        region : 'west',
        width : 350,
        split : true,
        title:'变量分类',
        border : true,
        rootVisible : false,
        viewConfig : {},
        autoScroll:true,
        bodyCls:'customize_tree',
        hidden: !JlnxScriptClassSwitch,
        cls:'customize_panel_back panel_space_right_zb',
        hideHeaders:true,
        columns: [{
            dataIndex: 'iid',
            text: 'iid',
            width: 80,
            flex: 1,
            hidden:true
        },{
            dataIndex: 'iname',
            text: '分类名称',
            width: 80,
            flex: 1,
            renderer : function (value, metaData, record, colIndex, store, view)
            {
                metaData.tdAttr = 'data-qtip="' + value + '"';
                return value;
            },
            xtype: 'treecolumn' // this is so we know which column will show the tree
        },{
            dataIndex: 'iparentid',
            text: '父级ID',
            width: 80,
            flex: 1,
            hidden: true
        }],
        dockedItems : [
            {
                xtype : 'toolbar',
                items : ['->', {
                    xtype : 'button',
                    cls : 'Common_Btn',
                    text : '增加',
                    handler : addorgManagement
                }]
            } ],
        listeners :{
            'itemclick': function(gird, record, item, index, e, eOpts) {
                classIdV = record.get('iid');
                checkItems = [];
                store.load();
            },
            'containercontextmenu':function( grid, e, eOpts){
                //禁用浏览器的右键相应事件
                e.preventDefault();
                e.stopEvent();
                var iid=-1;
                var menu = new Ext.menu.Menu({
                    //控制右键菜单位置
                    float:true,
                    items:[{
                        text:"添加",
                        //icon:'images/addSys.png',
                        handler:function(){
                            //当点击时隐藏右键菜单
                            this.up("menu").close();
                            addOrgType(iid);
                        }
                    }
                    ]
                }).showAt(e.getXY());//让右键菜单跟随鼠标位置
            },
            'itemcontextmenu':function(view,record,item,index,e,eOpts){
                //禁用浏览器的右键相应事件
                e.preventDefault();
                e.stopEvent();
                var iid=record.get('iid');
                if(record.parentNode){
                    parentiid = record.parentNode.get('iid');
                }
                var menu = new Ext.menu.Menu({
                    //控制右键菜单位置
                    float:true,
                    items:[{
                        text:"添加",
                        handler:function(){
                            //当点击时隐藏右键菜单
                            this.up("menu").close();
                            addOrgType(iid,parentiid);
                        }
                    },{
                        text:"修改",
                        handler:function(){
                            this.up("menu").close();
                            editOrgType(iid,parentiid);
                        }
                    },{
                        text:"删除",
                        handler:function(){
                            this.up("menu").close();
                            deleteOrgType(iid,parentiid);
                        }
                    }
                    ]
                }).showAt(e.getXY());//让右键菜单跟随鼠标位置
            }
        }
    });
    //添加按钮只添加分类
    function addorgManagement(){
        var addManamentWin;
        var addManamentForm = Ext.create('Ext.form.FormPanel',{
            border : false,
            items: [ {
                xtype : 'textfield',
                fieldLabel : '名称',
                labelWidth : 80,
                labelAlign : 'right',
                name : 'iname',
                anchor : '90%',
                padding : '5 5 5 5',
                allowBlank : false,
                listeners:{
                    blur:function (e){
                        if (checkLength(e.getValue()) > 50){
                            Ext.Msg.alert('提示', '分类名称最大50个字节');
                            e.setValue('');
                        }
                    }
                }
            }],
            buttonAlign: 'center',
            buttons : [{
                text : '保存',
                handler : function() {
                    var form = addManamentForm.getForm();

                    var typename = form.findField("iname").getValue().trim();

                    if(fucCheckLength(typename)==0){
                        Ext.Msg.alert('提示', '名称不能为空！');
                        return;
                    }

                    if(form.isValid()){
                        Ext.Ajax.request({
                            url : 'variableBase/saveClass.do',
                            method : 'POST',
                            params : {
                                iid:-1,
                                iname:typename,
                                iparentid:0,
                                classType:1
                            },
                            success : function(response, request) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if (success) {
                                    Ext.Msg.alert('提示', message);
                                    addManamentWin.close();
                                    treeStore.load();
                                } else {
                                    Ext.Msg.alert('提示', message);
                                }
                            },
                            failure : function(result, request) {
                                secureFilterRs(result, '保存失败！');
                            }
                        });
                    }
                }
            }, {
                text : '取消',
                handler : function() {
                    addManamentWin.close(this);
                }
            }
            ]
        });
        //添加组织分类
        addManamentWin = new Ext.Window({
            title : '添加变量分类',
            modal : true,
            width : 500,
            height : 180,
            resizable : false,
            plain : true,
            layout : 'form',
            draggable : false,
            items : [ addManamentForm ]
        });
        addManamentWin.show();
    }
//添加
    function addOrgType(iid,parentiid){
        console.log(iid)
        if(iid==-1){
            var addManamentWin;
            var addManamentForm = Ext.create('Ext.form.FormPanel',{
                border : false,
                items: [ {
                    xtype : 'textfield',
                    fieldLabel : '名称',
                    labelWidth : 80,
                    labelAlign : 'right',
                    name : 'iname',
                    anchor : '90%',
                    padding : '5 5 5 5',
                    allowBlank : false,
                    listeners:{
                        blur:function (e){
                            if (checkLength(e.getValue()) > 50){
                                Ext.Msg.alert('提示', '分类名称最大50个字节');
                                e.setValue('');
                            }
                        }
                    }
                }],
                buttonAlign: 'center',
                buttons : [{
                    text : '保存',
                    handler : function() {
                        var form = addManamentForm.getForm();
                        if(form.isValid()){
                            var typename = form.findField("iname").getValue().trim();
                            if(fucCheckLength(typename)==0){
                                Ext.Msg.alert('提示', '名称不能为空！');
                                return;
                            }
                            Ext.Ajax.request({
                                url : 'variableBase/saveClass.do',
                                method : 'POST',
                                params : {
                                    iid:iid,
                                    iname:typename,
                                    iparentid:0,
                                    classType:1
                                },
                                success : function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if (success) {
                                        Ext.Msg.alert('提示', message);
                                        addManamentWin.close();
                                        treeStore.load();
                                    } else {
                                        Ext.Msg.alert('提示', message);
                                    }
                                },
                                failure : function(result, request) {
                                    secureFilterRs(result, '保存失败！');
                                }
                            });
                        }
                    }
                }, {
                    text : '取消',
                    handler : function() {
                        addManamentWin.close(this);
                    }
                }
                ]
            });
            //添加组织分类
            addManamentWin = new Ext.Window({
                title : '添加变量分类',
                modal : true,
                width : 500,
                height : 180,
                resizable : false,
                plain : true,
                layout : 'form',
                draggable : false,
                items : [ addManamentForm ]
            });
            addManamentWin.show();
        }else{
            var addTypeWin;
            var addOrgTypeForm = Ext.create('Ext.form.FormPanel', {
                border : false,
                items : [{
                    xtype : 'textfield',
                    fieldLabel : '名称',
                    labelWidth : 80,
                    labelAlign : 'right',
                    name : 'iname',
                    anchor : '90%',
                    padding : '5 5 5 5',
                    allowBlank : false,
                    maxLength : 100
                }],
                buttonAlign: 'center',
                buttons : [{
                    text : '保存',
                    handler : function() {
                        var form = addOrgTypeForm.getForm();
                        if(form.isValid()){

                            var typename = form.findField("iname").getValue().trim();

                            if(fucCheckLength(typename)==0){
                                Ext.Msg.alert('提示', '名称不能为空！');
                                return;
                            }

                            Ext.Ajax.request({
                                url : 'variableBase/saveClass.do',
                                method : 'POST',
                                params : {
                                    iid:-1,
                                    iname:typename,
                                    iparentid:iid,
                                    classType:1
                                },
                                success : function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if (success) {
                                        Ext.Msg.alert('提示', message);
                                        addTypeWin.close();
                                        treeStore.load({callback : function(){
                                                expandParent(iid,parentiid,'add');
                                            }});
                                    } else {
                                        Ext.Msg.alert('提示', message);
                                    }
                                },
                                failure : function(result, request) {
                                    secureFilterRs(result, '保存失败！');
                                }
                            });
                        }
                    }
                }, {
                    text : '取消',
                    handler : function() {
                        addTypeWin.close(this);
                    }
                }
                ]
            });
            // 添加模型
            addTypeWin = new Ext.Window({
                title : '添加变量分类',
                modal : true,
                width : 500,
                height : 180,
                resizable : false,
                plain : true,
                layout : 'form',
                draggable : false,
                items : [ addOrgTypeForm ]
            });
            addTypeWin.show();
        }
    }
//修改
    function editOrgType(iid,parentiid){
        Ext.Ajax.request({
            url : 'variableBase/getClassName.do',
            params : {
                iid : iid,
                classType:1
            },
            method : 'POST',
            success : function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var iname = Ext.decode(response.responseText).iname;
                console.log(iname);
                if(success){
                    var editOrgTypeWin;
                    var editOrgTypeForm = Ext.create('Ext.form.FormPanel',{
                        border : false,
                        items: [ {
                            xtype : 'textfield',
                            fieldLabel : '名称',
                            labelWidth : 80,
                            labelAlign : 'right',
                            name : 'iname',
                            anchor : '90%',
                            padding : '5 5 5 5',
                            allowBlank : false,
                            maxLength : 100,
                            value :iname
                        }],
                        buttonAlign: 'center',
                        buttons : [{
                            text : '保存',
                            handler : function() {
                                var form = editOrgTypeForm.getForm();
                                if(form.isValid()){
                                    var typename = form.findField("iname").getValue().trim();
                                    if(fucCheckLength(typename)==0){
                                        Ext.Msg.alert('提示', '名称不能为空！');
                                        return;
                                    }
                                    Ext.Ajax.request({
                                        url : 'variableBase/updateClass.do',
                                        method : 'POST',
                                        params : {
                                            iid:iid,
                                            iname:typename,
                                            iparentid:parentiid == ""?0:parentiid,
                                            classType:1
                                        },
                                        success : function(response, request) {
                                            var success = Ext.decode(response.responseText).success;
                                            var message = Ext.decode(response.responseText).message;
                                            if (success) {
                                                Ext.Msg.alert('提示', message);
                                                editOrgTypeWin.close();
                                                treeStore.load({callback : function(){
                                                        expandParent(iid,parentiid,'edit');
                                                    }});
                                            } else {
                                                Ext.Msg.alert('提示', message);
                                            }
                                        },
                                        failure : function(result, request) {
                                            secureFilterRs(result, '保存失败！');
                                        }
                                    });
                                }
                            }
                        }, {
                            text : '取消',
                            handler : function() {
                                editOrgTypeWin.close(this);
                            }
                        }
                        ]
                    });
                    editOrgTypeWin= new Ext.Window({
                        title : '修改变量分类名称',
                        modal : true,
                        width : 500,
                        height : 180,
                        resizable : false,
                        plain : true,
                        layout : 'form',
                        draggable : false,
                        items : [ editOrgTypeForm ]
                    });
                    editOrgTypeWin.show();
                }
            }
        });
    };
    function expandParent(iid,parentiid,operation){
        var expandiid;
        if(operation){
            if(operation=='add'){
                expandiid = iid;
            }else if(operation=='edit'||operation=='delete'){
                expandiid = parentiid;
            }
        }else{
            return;
        }

        var rootnode = treeStore.getRootNode();
        rootnode.eachChild (function (node)
        {
            if(expandiid==node.get('iid')){
                node.expand();
                expandParentNode(node.parentNode);
            }else{
                findSonNode(node,expandiid);
            }

        })
    }
    function expandParentNode(node){
        if(node){
            node.expand();
            expandParentNode(node.parentNode);
        }
    }
    function findSonNode(node,iid){
        node.eachChild (function (nodeSon)
        {
            if(iid==nodeSon.get('iid')){
                nodeSon.expand();
                expandParentNode(nodeSon.parentNode);
            }else{
                findSonNode(nodeSon,iid);
            }
        })
    }

//修改
    function editOrgType(iid,parentiid){
        Ext.Ajax.request({
            url : 'variableBase/getClassName.do',
            params : {
                iid : iid,
                classType:1
            },
            method : 'POST',
            success : function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var iname = Ext.decode(response.responseText).iname;
                console.log(iname);
                if(success){
                    var editOrgTypeWin;
                    var editOrgTypeForm = Ext.create('Ext.form.FormPanel',{
                        border : false,
                        items: [ {
                            xtype : 'textfield',
                            fieldLabel : '名称',
                            labelWidth : 80,
                            labelAlign : 'right',
                            name : 'iname',
                            anchor : '90%',
                            padding : '5 5 5 5',
                            allowBlank : false,
                            maxLength : 100,
                            value :iname
                        }],
                        buttonAlign: 'center',
                        buttons : [{
                            text : '保存',
                            handler : function() {
                                var form = editOrgTypeForm.getForm();
                                if(form.isValid()){
                                    var typename = form.findField("iname").getValue().trim();
                                    if(fucCheckLength(typename)==0){
                                        Ext.Msg.alert('提示', '名称不能为空！');
                                        return;
                                    }
                                    Ext.Ajax.request({
                                        url : 'variableBase/updateClass.do',
                                        method : 'POST',
                                        params : {
                                            iid:iid,
                                            iname:typename,
                                            iparentid:parentiid == ""?0:parentiid,
                                            classType:1
                                        },
                                        success : function(response, request) {
                                            var success = Ext.decode(response.responseText).success;
                                            var message = Ext.decode(response.responseText).message;
                                            if (success) {
                                                Ext.Msg.alert('提示', message);
                                                editOrgTypeWin.close();
                                                treeStore.load({callback : function(){
                                                        expandParent(iid,parentiid,'edit');
                                                    }});
                                            } else {
                                                Ext.Msg.alert('提示', message);
                                            }
                                        },
                                        failure : function(result, request) {
                                            secureFilterRs(result, '保存失败！');
                                        }
                                    });
                                }
                            }
                        }, {
                            text : '取消',
                            handler : function() {
                                editOrgTypeWin.close(this);
                            }
                        }
                        ]
                    });
                    editOrgTypeWin= new Ext.Window({
                        title : '修改变量分类名称',
                        modal : true,
                        width : 500,
                        height : 180,
                        resizable : false,
                        plain : true,
                        layout : 'form',
                        draggable : false,
                        items : [ editOrgTypeForm ]
                    });
                    editOrgTypeWin.show();
                }
            }
        });
    };

    function deleteOrgType(iid,parentiid){
        Ext.Msg.confirm("请确认","是否选择删除分类，分类下已存在变量则无法删除，是否继续？",
            function(button, text) {
                if (button == "yes") {
                    Ext.Ajax.request({
                        url : 'variableBase/delClass.do',
                        params : {
                            iid : iid,
                            classType:1
                        },
                        method : 'POST',
                        success : function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            var msg = Ext.decode(response.responseText).message;
                            if (success) {
                                treeStore.load({callback : function(){
                                        expandParent(iid,parentiid,'delete');
                                    }});
                                Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
                            } else {
                                Ext.Msg.alert('提示',msg);
                            }
                        },
                        failure : function(result, request) {
                            secureFilterRs(result,'操作失败！');
                        }
                    });
                }
            }
        );
    }
    Ext.define('variableBaseModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [{
            name: 'iid',
            type: 'long'
        }, {
            name: 'iname',
            type: 'string'
        }, {
            name: 'itype',
            type: 'int'
        }, {
            name: 'ivalue',
            type: 'string'
        }, {
            name: 'idesc',
            type: 'string'
        }, {
            name: 'iattribute',
            type: 'int'
        }, {
            name: 'iisGlobal',
            type: 'int'
        }, {
            name: 'icreateuser',
            type: 'string'
        }, {
            name: 'icreatetime',
            type: 'string'
        }, {
            name: 'imodifyuser',
            type: 'string'
        }, {
            name: 'imodifytime',
            type: 'string'
        }, {
            name: 'imd5',
            type: 'string'
        },{
            name: 'autoObtain',
            type: 'int'
        }, {
            name: 'istatus',
            type: 'int'
        }, {
            name: 'iuserGlobal',
            type: 'int'
        }, {
            name: 'classId',
            type: 'long'
        },{
            name: 'classType',
            type: 'int'
        }]
    })

    var typeStore = null;
    var dataTypeStore = null;
    if(fourAShowSwitch){
        typeStore = Ext.create('Ext.data.Store', {
            fields: ['typeName', 'typeId'],
            data: [{
                'typeName': '全部',
                'typeId': 0
            }, {
                'typeName': '数值',
                'typeId': 1
            }, {
                'typeName': '字符串',
                'typeId': 2
            }, {
                'typeName': '字符串(加密)',
                'typeId': 3
            }, {
                'typeName': '数组',
                'typeId': 4
            }, {
                'typeName': '字典',
                'typeId': 5
            }, {
                'typeName': '4A密码类型',
                'typeId': 6
            }]
        })

        dataTypeStore = Ext.create('Ext.data.Store', {
            fields: ['typeName', 'id'],
            data: [{
                'typeName': '数值',
                'id': 1
            }, {
                'typeName': '字符串',
                'id': 2
            }, {
                'typeName': '字符串(加密)',
                'id': 3
            }, {
                'typeName': '数组',
                'id': 4
            }, {
                'typeName': '字典',
                'id': 5
            }, {
                'typeName': '4A密码类型',
                'id': 6
            }]
        })
    }else {
        typeStore = Ext.create('Ext.data.Store', {
            fields: ['typeName', 'typeId'],
            data: [{
                'typeName': '全部',
                'typeId': 0
            }, {
                'typeName': '数值',
                'typeId': 1
            }, {
                'typeName': '字符串',
                'typeId': 2
            }, {
                'typeName': '字符串(加密)',
                'typeId': 3
            }, {
                'typeName': '数组',
                'typeId': 4
            }, {
                'typeName': '字典',
                'typeId': 5
            }]
        })

        dataTypeStore = Ext.create('Ext.data.Store', {
            fields: ['typeName', 'id'],
            data: [{
                'typeName': '数值',
                'id': 1
            }, {
                'typeName': '字符串',
                'id': 2
            }, {
                'typeName': '字符串(加密)',
                'id': 3
            }, {
                'typeName': '数组',
                'id': 4
            }, {
                'typeName': '字典',
                'id': 5
            }]
        })
    }



    var attributeStore = Ext.create('Ext.data.Store', {
        fields: ['attrName', 'attrId'],
        data: [{
            'attrName': '全部',
            'attrId': 0
        }, {
            'attrName': '内置',
            'attrId': 2
        }, {
            'attrName': '自定义',
            'attrId': 1
        }]
    })

    var statusStore = Ext.create('Ext.data.Store', {
        fields: ['statusName', 'statusId'],
        data: [{
            'statusName': '全部',
            'statusId': 0
        }, {
            'statusName': '已发布',
            'statusId': 2
        }, {
            'statusName': '草稿',
            'statusId': 1
        }]
    })
    var classStore = Ext.create('Ext.data.Store', {
        fields: ['className', 'classId'],
        data: [{
            'className': '已绑定',
            'classId': 1
        }, {
            'className': '未绑定',
            'classId': 2
        }]
    })
    store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'variableBaseModel',
        proxy: {
            url: 'variableBase/getList.do',
            type: 'ajax',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    })
    queryType = Ext.create('Ext.form.ComboBox', {
        fieldLabel: '变量类型',
        emptyText: '-请选择变量类型-',
        store: typeStore,
        displayField: 'typeName',
        valueField: 'typeId',
        editable: false,
        width: JlnxScriptClassSwitch?width1:width,
        labelWidth: 60,
        listeners: {
            change:function (){
                getData();
            }
        }
    })

    queryName = Ext.create('Ext.form.TextField', {
        fieldLabel: '变量名称',
        emptyText: '-请输入变量名称-',
        width: JlnxScriptClassSwitch?width1:width,
        labelWidth: 60,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    getData();
                }
            }
        }
    })

    queryAttribute = Ext.create('Ext.form.ComboBox', {
        fieldLabel: '变量属性',
        emptyText: '-请选择变量属性-',
        store: attributeStore,
        displayField: 'attrName',
        valueField: 'attrId',
        editable: false,
        width:JlnxScriptClassSwitch?width1:width,
        labelWidth: 60,
        listeners: {
            change:function (){
                getData();
            }
        }
    })

    queryStatus = Ext.create('Ext.form.ComboBox', {
        fieldLabel: '状态',
        emptyText: '-请选择状态-',
        store: statusStore,
        displayField: 'statusName',
        valueField: 'statusId',
        editable: false,
        width:JlnxScriptClassSwitch?width1:width,
        labelWidth: 40,
        listeners: {
            change:function (){
                getData();
            }
        }
    })

    classStatusV = Ext.create('Ext.form.ComboBox', {
        fieldLabel: '分类',
        emptyText: '-请选择分类-',
        store: classStore,
        displayField: 'className',
        valueField: 'classId',
        editable: false,
        width: 200,
        hidden:!JlnxScriptClassSwitch,
        labelWidth: 40,
        listeners: {
            change:function (){
                getData();
            }
        }
    })
    queryScope = Ext.create('Ext.form.TextField', {
        fieldLabel: '作用域',
        emptyText: scopeSearchEmptyText,
        flex: 430,
        labelWidth: 60,
        listeners: {
            specialkey: function(field, e){
                // e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,
                // e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN
                if (e.getKey() == e.ENTER) {
                    getData();
                }
            }
        }
    })

    var columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    }, {
        dataIndex: 'iid',
        text: 'IID',
        hidden: true
    }, {
        dataIndex: 'iname',
        text: '变量名称*',
        flex: 1,
        editor: {
            listeners:{
                blur:function (t, even, ops) {
                    if (checkLength(t.value) > 50){
                        Ext.Msg.alert('提示', '变量名称字符长度最大50');
                        Ext.getCmp(t.id).setValue('');
                    }
                }
            }
        },
        renderer:function (value, metaData) {
            metaData.tdAttr = 'data-qtip="' + value + '"';
            return value;
        }
    }, {
        dataIndex: 'itype',
        text: '变量类型*',
        width: 100,
        editor: {
            xtype: 'combobox',
            store: dataTypeStore,
            displayField: 'typeName',
            valueField: 'id',
            editable: false
        },
        renderer: function (value) {
            return dataTypeStore.getById(value).data.typeName;
        }
    }, {
        dataIndex: 'ivalue',
        text: '变量值*',
        flex: 1,
        editor: {},
        renderer: function (value, metaData, record) {
            var showValue = value;
            let itype = record.get('itype');
            if (itype == 3) {
                let xing = "";
                let len = value.length;
                for (let i = 0; i < len; i++) {
                    xing += "*";
                }
                showValue = xing;
            }
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(showValue) + '"';
            return showValue;
        }
    }, {
        dataIndex: 'idesc',
        text: '描述',
        flex: 1,
        editor: {},
        renderer:function (value, metaData) {
            metaData.tdAttr = 'data-qtip="' + value + '"';
            return value;
        }
    }, {
        dataIndex: 'iattribute',
        text: '属性',
        width: 100,
        renderer: function (value) {
            if (value == 2) {
                return '内置';
            } else if (value == 1) {
                return '自定义';
            }
        }
    }, {
        dataIndex: 'istatus',
        text: '状态',
        renderer: function (value) {
            var str = ''
            switch (value) {
                case 1:
                    str = '<span style="color:#F01024;">草稿</span>';
                    break;
                case 3:
                    str = '<span style="color:#F01024;">草稿</span>';
                    break;
                case 2:
                    str = '<span style="color:#0CBF47;">已发布</span>';
                    break;
                default:
                    break;
            }
            return str;
        }
    }, {
        dataIndex: 'iisGlobal',
        text: '全域有效',
        width: 100,
        hidden: scriptEditBookSwitch,
        renderer: function (value) {
            if (value == 1) {
                return '是';
            } else if (value == 0) {
                return '否';
            }
        }
    },{
        dataIndex: 'iuserGlobal',
        text: '用户全部有效',
        width: 100,
        renderer: function (value) {
            if (value == 1) {
                return '是';
            } else if (value == 0) {
                return '否';
            }
        }
    }, {
        dataIndex: 'icreateuser',
        text: '创建人',
        flex: 1,
        hidden: true
    }, {
        dataIndex: 'icreatetime',
        text: '创建时间',
        flex: 1,
        hidden: true
    }, {
        dataIndex: 'imodifyuser',
        text: '修改人',
        flex: 1,
        hidden: true
    }, {
        dataIndex: 'imodifytime',
        text: '修改时间',
        flex: 1,
        hidden: true
    }, {
        dataIndex: 'imd5',
        text: 'md5',
        flex: 1,
        hidden: true
    }, {
        dataIndex: '4A密码是否自动获取',
        text: 'autoObtain',
        flex: 1,
        hidden: true
    },{
        dataIndex: 'iid',
        text: '操作',
        flex: 1,
        renderer: function (value, metaData, record) {
            if (value == 0 || record.dirty) {
                return '<span style="color:red;">请先保存再进行操作</span>';
            } else {
                var type = record.get('itype');
                var autoObtain = record.get('autoObtain');
                var str = '';
                /**
                 * edit start 张伟建 修改，4A密码编辑，因为数组跟字典类型弹窗均为列表形式，editParam复用复杂，故操作列使用单独的编辑超链接
                 */
                //属性  自定义 或者 内置（对4A密码而言，当为自定义且为4A密码类型时操作列显示编辑）6为4A密码类型
                var varPro = record.get('iattribute');
                if (varPro == 1 && type == 6) {
                    var variableIID = record.get('iid');
                    str += '<a href="#" onclick="editParamFourA(' + variableIID + ')"><img src="images/monitor_bg.png" align="absmiddle" class="script_edit">编辑</a>&nbsp;&nbsp;&nbsp;'
                }

                /**
                 * edit end 张伟建 修改
                 */
                if (type == 4 || type == 5) {
                    str += '<a href="#" onclick="editParam(' + type + ', ' + value + ')"><img src="images/monitor_bg.png" align="absmiddle" class="script_edit">编辑</a>&nbsp;&nbsp;&nbsp;'
                }
                if (!(scriptEditBookSwitch && varPro == 2)) {
                    str += '<a href="#" onclick="getActiveScope([' + value + '],1,false)"><img src="images/monitor_bg.png" align="absmiddle" class="script_set">作用域</a>&nbsp;&nbsp;&nbsp;';
                }
                if (type == 6 && autoObtain == 0) {
                    var variableIID = record.get('iid');
                    str += '<a href="#" onclick="getParamFourA(' + variableIID + ')"><img src="images/monitor_bg.png" align="absmiddle" class="script_edit">获取密码</a>';
                }

                return str;
            }
        }
    }]

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        id: 'selModelSS',
        checkOnly: true
    });

    grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        columns: columns,
        store: store,
        width: '100%',
        height: '100%' - 60,
        layout: 'fit',
        region: 'center',
        selModel: selModel,
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        plugins: [
            Ext.create('Ext.grid.plugin.CellEditing', {
                clicksToEdit: 2
            })
        ],
        dockedItems: [{
            xtype: 'toolbar',
            items: ['->', {
                text: '分类修改',
                cls: 'Common_Btn',
                hidden: !JlnxScriptClassSwitch,
                handler: function () {
                    var m = checkItems;
                    if(classIdV==null||classIdV==undefined){
                        Ext.Msg.alert('提示', "请添加函数分类");
                        return;
                    }
                    if (m.length == 0) {
                        Ext.Msg.alert('提示', "请选择要修改绑定分类的变量");
                        return;
                    } else {
                        if (variableBaseWin != null) {
                            variableBaseWin.close();
                        }
                        var iids = [];
                        checkItems.forEach((selectedRecord) => {
                            iids.push(selectedRecord.data.iid);
                        });
                        var winTreestore=Ext.create('Ext.data.TreeStore', {
                            autoLoad: JlnxScriptClassSwitch,
                            fields: ['iparentid', 'iname'],
                            root: {
                                expanded: JlnxScriptClassSwitch
                            },
                            proxy: {
                                type: 'ajax',
                                url: 'variableBase/getClassList.do?classType=1'
                            }
                        })
                        var formPanel = Ext.create('Ext.form.Panel', {
                            width: '100%',
                            height: '100%',
                            items: [{
                                border: false,
                                layout: 'column',
                                margin: '5',
                                items: [{
                                    width: 400,
                                    minPickerHeight: 20,
                                    id: 'variableDirId',
                                    displayField: 'iname',
                                    valueField: 'iparentid',
                                    editable: false,
                                    xtype: 'treepicker',
                                    value: '',
                                    store:winTreestore,
                                    listeners: {
                                        // select: function (picker, record) {
                                        //     // 校验选择 不是末级目录，无法选择
                                        //     if (record.childNodes.length > 0) {
                                        //         picker.setValue('')
                                        //     }
                                        // }
                                    }
                                }]
                            }],
                            buttons: [
                                {
                                    xtype: 'button',
                                    text: '保存',
                                    cls: 'Common_Btn',
                                    handler: function () {
                                        var paraid = Ext.getCmp('variableDirId').getValue();
                                        if (paraid == null || paraid == '') {
                                            Ext.Msg.alert('提示', "请选择要修改的分类");
                                            return;
                                        }
                                        // if (scriptDirId == '') {
                                        //     Ext.Msg.alert('提示', '请选择要修改的末级目录');
                                        // } else {
                                            Ext.Ajax.request({
                                                url: 'variableBase/updateVariableClass.do',
                                                method: 'POST',
                                                params: {
                                                    iids: Ext.encode(iids),
                                                    paraid: paraid
                                                },
                                                success: function (response) {
                                                    var success = Ext.decode(response.responseText).success;
                                                    var message = Ext.decode(response.responseText).message;
                                                    if (success) {
                                                        variableBaseWin.close();
                                                    }
                                                    store.reload();
                                                    Ext.Msg.alert('提示', message);
                                                },
                                                failure: function () {
                                                    Ext.Msg.alert('提示', '网络连接失败');
                                                }
                                            })
                                        // }
                                    }
                                },
                                {
                                    xtype: 'button',
                                    text: '取消',
                                    cls: 'Common_Btn',
                                    handler: function () {
                                        variableBaseWin.close();
                                    }
                                }
                            ]
                        })

                        variableBaseWin = Ext.create('Ext.window.Window', {
                            title: '批量修改',
                            layout: 'border',
                            height: 180,
                            width: 450,
                            modal: true,
                            items: [formPanel]
                        }).show();
                    }
                }
            },{
                type: 'button',
                text: '设置作用域',
                cls: 'Common_Btn',
                handler: function () {
                    if (checkItems.length == 0) {
                        Ext.Msg.alert('提示', '请选择数据！');
                    } else {
                        var checkItemIds = [];
                        for (let i = 0; i < checkItems.length; i++) {
                            if (scriptEditBookSwitch) {
                                if (checkItems[i].data.iattribute == 2) {
                                    Ext.Msg.alert('提示', '内置变量不能设置作用域');
                                    return;
                                }
                            }
                            if (checkItems[i].data.iid == 0 || checkItems[i].dirty == true) {
                                Ext.Msg.alert('提示', '请先保存新增和修改数据在进行设置作用域');
                                return;
                            }
                            checkItemIds.push(checkItems[i].data.iid);
                        }
                        setScope(checkItemIds, 1, true);
                    }
                }
            }
                , {
                    type: 'button',
                    text: '发布',
                    cls: 'Common_Btn',
                    handler: function () {
                        var m = checkItems;
                        var warn = [];
                        if (m.length == 0) {
                            Ext.Msg.alert('提示', '请选择要发布的数据！');
                            return;
                        }
                        var iids = [];
                        var unAutoNames = "";
                        for (let i = 0; i < m.length; i++) {
                            if (m[i].data.istatus == 2) {
                                Ext.Msg.alert('提示', '发布状态数据无法再次发布');
                                return;
                            }
                            if (m[i].data.iisGlobal == 1) {
                                warn.push(m[i].data.iname);
                            }
                            if (m[i].data.iid != 0) {
                                iids.push(m[i].data.iid);
                            }
                            /**
                             *  modify by 张伟建 4A密码类型，非自动获取的4A密码，未获取密码时禁止发布
                             */
                            //如果是4A密码类型，非自动获取的4A密码，并且没有获取密码，禁止发布
                            if ((m[i].data.itype == 6) && (m[i].data.ivalue == null || m[i].data.ivalue == '') && (m[i].data.autoObtain == 0 || m[i].data.autoObtain == null || m[i].data.autoObtain == '')) {
                                unAutoNames = unAutoNames + m[i].data.iname + "、";
                            }
                        }
                        //如果是4A密码类型，非自动获取的4A密码，并且没有获取密码，禁止发布
                        if (unAutoNames != '') {
                            unAutoNames = unAutoNames.substring(0, unAutoNames.length - 1);
                            Ext.Msg.alert('提示', '非自动获取的4A密码类型变量' + unAutoNames + '未获取密码，禁止发布！');
                            return;
                        }

                        if (!scriptEditBookSwitch) {
                            Ext.Msg.confirm('警告!!!', '请严格检查变量名称、类型、值是否正确，错误发布可能会对脚本执行结果造成影响，是否继续执行发布？', function (btn) {
                                if (btn == 'yes') {
                                    if (warn.length > 0) {
                                        Ext.Msg.confirm('警告!!!', '选择变量"' + warn.join(',') + '"未设置作用域，将全域有效，会下发到平台内所有agent，是否继续发布？', function (btn) {
                                            if (btn == 'yes') {
                                                publish(iids);
                                            } else if (btn == 'no') {
                                                checkItems = [];
                                                store.load();
                                                return;
                                            }
                                        })
                                    } else {
                                        if (iids.length == 0) {
                                            store.load();
                                        } else {
                                            publish(iids);
                                        }
                                    }
                                } else if (btn == 'no') {
                                    checkItems = [];
                                    store.load();
                                    return;
                                }
                            })
                        } else {
                            publish(iids);
                        }
                    }
                }
                , {
                    type: 'button',
                    text: '导入',
                    cls: 'Common_Btn',
                    handler: function () {
                        upload();
                    }
                }, {
                    type: 'button',
                    text: '导出',
                    cls: 'Common_Btn',
                    handler: function () {
                        var m = checkItems;
                        if (m.length == 0) {
                            Ext.Msg.alert('提示', '请选择要导出的数据！');
                            return;
                        }
                        var iids = [];
                        for (let i = 0; i < m.length; i++) {
                            if (m[i].data.iattribute == 2) {
                                Ext.Msg.alert('提示', '内置变量不允许导出！');
                                return;
                            }
                            if (m[i].data.istatus != 2) {
                                Ext.Msg.alert('提示', '仅支持导出发布状态变量');
                                return;
                            }
                            if (m[i].data.iid != 0) {
                                iids.push(m[i].data.iid);
                            }
                        }
                        downloadFile({iids: Ext.encode(iids)}, '/aoms/variableBase/export.do');
                    }
                }, {
                    type: 'button',
                    text: '增加',
                    cls: 'Common_Btn',
                    handler: function () {
                        if(JlnxScriptClassSwitch){
                            if(classIdV==undefined){
                                Ext.Msg.alert('提示', "请先添加分类！");
                                return ;
                            }
                        }

                        var newRecord = Ext.create('variableBaseModel', {
                            iname: '',
                            itype: 1,
                            istatus: 1,
                            ivalue: '',
                            idesc: '',
                            iattribute: 1,    // 2为内置 1为自定义
                            iparentid:parentiid,
                            classId:classIdV,
                            classType:1
                        })
                        store.insert(0, newRecord);
                    }
                }, {
                    type: 'button',
                    text: '保存',
                    cls: 'Common_Btn',
                    handler: function () {
                        var m = store.getModifiedRecords();
                        for (let i = 0; i < m.length; i++) {
                            if (m[i].data.iname == '') {
                                Ext.Msg.alert('提示', '变量名称不能为空');
                                return;
                            }
                            if (m[i].data.itype != 4 && m[i].data.itype !=5 && m[i].data.itype !=6 && m[i].data.iattribute == 1) {
                                if (m[i].data.ivalue == ''){
                                    Ext.Msg.alert('提示', '变量值不能为空');
                                    return;
                                }
                            }
                        }
                        var newRecords = [];
                        var n = store.getNewRecords();
                        for (let i = 0; i < n.length; i++) {
                            n[i].classId=classIdV
                            newRecords.push(n[i].data);
                        }
                        var updateRecords = [];
                        var o = store.getUpdatedRecords();
                        for (let i = 0; i < o.length; i++) {
                            if (o[i].modified['ivalue']!=undefined){
                                o[i].data['modifyValue']=1;
                            }else {
                                o[i].data['modifyValue']=0;
                            }
                            updateRecords.push(o[i].data);
                        }
                        if (m.length == 0) {
                            Ext.Msg.alert('提示', '没有要保存数据');
                            return;
                        } else {
                            Ext.Ajax.request({
                                url: 'variableBase/save.do',
                                method: 'POST',
                                params: {
                                    newRecord: Ext.encode(newRecords),
                                    updateRecord: Ext.encode(updateRecords)
                                },
                                success: function (response) {
                                    var success = Ext.decode(response.responseText).success;
                                    var msg = Ext.decode(response.responseText).message;
                                    Ext.Msg.alert('提示', msg);
                                    if (success) {
                                        store.load();
                                        checkItems = [];
                                    }
                                },
                                failure: function () {
                                    Ext.Msg.alert('提示', '网络连接失败');
                                }
                            })
                        }
                    }
                }, {
                    type: 'button',
                    text: '删除',
                    cls: 'Common_Btn',
                    handler: function () {
                        var m = checkItems;
                        if (m.length == 0) {
                            Ext.Msg.alert('提示', '请选择要删除的数据！');
                            return;
                        }
                        var n = store.getNewRecords();
                        // 幻影数据
                        var delList = [];
                        var iids = [];
                        for (let i = 0; i < m.length; i++) {
                            if (m[i].data.iattribute == 2) {
                                Ext.Msg.alert('提示', '内置变量不允许删除！');
                                return;
                            }
                            // if (m[i].data.istatus == 2) {
                            //     Ext.Msg.alert('提示', '已发布的数据不允许删除，请重新选择。');
                            //     return;
                            // }
                            var todelete = true;
                            for (let j = 0; j < n.length; j++) {
                                if (n[j].data == m[i].data){
                                    todelete = false;
                                    break;
                                }
                            }
                            if (todelete) {
                                iids.push(m[i].data.iid);
                            }else {
                                delList.push(m[i]);
                            }
                        }
                        //清除幻影数据
                        for (let i = 0; i < delList.length; i++) {
                            if (checkItems.indexOf(delList[i]) > -1){
                                checkItems.remove(delList[i]);
                            }
                            store.remove(delList[i]);
                        }
                        if (iids.length > 0) {
                            Ext.Msg.confirm('警告!!!', '删除已发布变量可能造成现有脚本执行发生异常，是否确定删除选中的数据？', function (btn) {
                                if (btn == 'yes') {
                                    Ext.Ajax.request({
                                        url: 'variableBase/del.do',
                                        method: 'POST',
                                        params: {
                                            iids: Ext.encode(iids)
                                        },
                                        success: function (response) {
                                            var success = Ext.decode(response.responseText).success;
                                            var msg = Ext.decode(response.responseText).message;
                                            Ext.Msg.alert('提示', msg);
                                            if (success) {
                                                store.load();
                                                checkItems = [];
                                            }
                                        },
                                        failure: function () {
                                            Ext.Msg.alert('提示', '网络连接失败');
                                        }
                                    })
                                } else if (btn == 'no') {
                                    checkItems=[];
                                    store.load();
                                    return;
                                }
                            })
                        }
                    }
                }]
        }],
        listeners: {
            beforeedit: function (editor, e) {
                // 内置变量所有都不可编辑
                if (e.record.data.iattribute == 2) {
                    return false;
                }
                var status = e.record.get('istatus');
                if (e.field == 'ivalue') {
                    switch (e.record.data.itype) {
                        case 1:
                            e.column.setEditor({
                                xtype: 'numberfield',
                                anchor: '100%',
                                maxValue: 9999999999999.99,
                                maxText: '最大数值为9999999999999.99',
                                enforceMaxLength: true,
                                maxLength: 16,
                                minValue: -100000000000.00,
                                minText: '最小数值为-100000000000.00',
                                value: 0
                            })
                            break;
                        case 2:
                            e.column.setEditor({
                                xtype: 'textfield',
                                anchor: '100%',
                                regex: regex,
                                regexText: regexText,
                                listeners: {
                                    blur: function (t, even, ops) {
                                        if (checkLength(t.value) > 255) {
                                            Ext.Msg.alert('提示', '最大输入字符长度255');
                                            Ext.getCmp(t.id).setValue('');
                                        }
                                    }
                                }
                            })
                            break;
                        case 3:
                            let pass = new Ext.form.TextField({
                                inputType: 'password',
                                regex: regex,
                                regexText: regexText,
                                enableKeyEvents: true,
                                listeners: {
                                    keypress: function (t, even, ops) {
                                        if (t.value.indexOf('********') > -1) {
                                            Ext.getCmp(t.id).setValue(t.value.replace('********', ''));
                                        }
                                    },
                                    blur: function (t, even, ops) {
                                        if (checkLength(t.value) > 100) {
                                            Ext.Msg.alert('提示', '加密字符串最大输入字符长度100');
                                            Ext.getCmp(t.id).setValue('');
                                        }
                                    }
                                }
                            });
                            e.column.setEditor(pass)
                            break;
                        case 4:
                        case 5:
                            return false;
                            break;
                        case 6://4A密码类型，不允许编辑
                            return false;
                            break;
                        default:
                            break;
                    }
                    return true;
                }else if(e.field == 'idesc'){
                    return true;
                } else {
                    switch (status) {
                        case 1:
                            return true;
                            break;
                        case 2:
                        case 3:
                            return false;
                            break;
                        default:
                            break;
                    }
                }
            },
            edit: function (editor, e) {
                if (e.field == 'iname'){
                    if (e.value.length > 50){
                        Ext.Msg.alert('提示', '变量名称字符长度最大50');
                        store.getById(e.rowId).set('iname', '');
                        return;
                    }
                }
                if (e.field == 'itype') {
                    if ('' != e.record.get('ivalue') && e.originalValue != e.value) {
                        Ext.Msg.confirm('提示', '变量值已存在，切换变量类型变量值将清空！', function (btn) {
                            if (btn == 'yes') {
                                // 新数据不用后台 清字典/数组数据
                                if (0 == e.record.get('iid')) {
                                    store.getById(e.rowId).set('ivalue', '');
                                } else {
                                    Ext.Ajax.request({
                                        url: 'variableBase/clearParam.do',
                                        method: 'POST',
                                        params: {
                                            iid: e.record.get('iid'),
                                            type: e.value
                                        },
                                        success: function (response) {
                                            var success = Ext.decode(response.responseText).success;
                                            if (success) {
                                                store.load();
                                            }
                                            store.getById(e.rowId).set('ivalue', '');
                                        },
                                        failure: function () {
                                            Ext.Msg.alert('提示', '网络连接失败');
                                        }
                                    })
                                }
                            } else if (btn == 'no') {
                                store.getById(e.rowId).set('itype', e.originalValue);
                                return;
                            }
                        })
                    }
                }
            },
            select: function (t, record, index) {
                if (checkItems.indexOf(record) == -1) {
                    checkItems.push(record);
                }
            },
            deselect: function (t, record, index) {
                if (checkItems.indexOf(record) > -1) {
                    checkItems.remove(record);
                }
            }
        },
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        emptyText: '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>'
    })

    var mainPanel = Ext.create('Ext.panel.Panel', {
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        bodyPadding: grid_margin,
        border: true,
        layout: 'border',
        renderTo: 'variablePanel',
        bodyCls: 'service_platform_bodybg',
        cls: 'customize_panel_back',
        items: [treePanel,grid],
        dockedItems: [{
            xtype: 'toolbar',
            baseCls: 'customize_gray_back',
            items: [queryType, queryName, queryStatus, queryAttribute, queryScope,classStatusV, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function () {
                    getData();
                }
            }, {
                xtype: 'button',
                text: '重置',
                cls: 'Common_Btn',
                handler: function () {
                    typeParam = undefined;
                    nameParam = undefined;
                    scopeParam = undefined;
                    attributeParam = undefined;
                    statusParam = undefined;
                    queryName.setRawValue('');
                    queryType.setRawValue('');
                    queryScope.setRawValue('');
                    queryAttribute.setRawValue('');
                    queryStatus.setRawValue('');
                    classStatusV.setValue('');
                    grid.ipage.moveFirst();
                    checkItems = [];
                }
            }]
        }]
    })

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });

    store.on('beforeload', function () {
        Ext.apply(store.proxy.extraParams, {
            type: typeParam,
            name: nameParam,
            scope: scopeParam,
            attribute: attributeParam,
            status: statusParam,
            classId:classIdV == undefined?-1:classIdV,
            classStatus:classStatusValue
        })
    })

    store.on("load", function (obj, records, successful, eOpts) {
        if (records == '') {
            var flag = true;
            var treeViewDiv = grid.body.dom.childNodes[0].childNodes;
            for (var i = 0; i < treeViewDiv.length; i++) {
                if (treeViewDiv[i].className == 'x-grid-empty') {
                    flag = false;
                }
            }
            if (flag) {
                var doc = document.createRange().createContextualFragment(grid.getView().emptyText);
                grid.body.dom.childNodes[0].appendChild(doc);
            }
        } else {
            grid.getSelectionModel().select(checkItems);
        }
    });
})
//获取4A密码
function getParamFourA(variableIID){
    Ext.Ajax.request({
        url: 'variableBase/getFourAPwdForInterface.do',
        method: 'POST',
        async: false,
        params: {
            variableIID: variableIID/*,
            iusername:loginName.getValue(),
            ipassword:loginPwd.getValue(),
            iaccount:accountName.getValue(),
            iresourcename:resourcesName.getValue()*/
        },
        success: function (response, request) {
            //获取到的4A密码赋值，为接下来保存使用
            // if(Ext.decode(response.responseText).success){
            //     fourAData.setValue(Ext.decode(response.responseText).password);
            // }
            //关闭弹窗
            // paramFourAWin.close();
            //grid变量值赋值刷新
            store.load();
            checkItems = [];
            //提示信息
            Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
        },
        failure: function (result, request) {
            Ext.Msg.alert('提示','获取4A密码失败');
        }
    });
}

/**
 * edit start 张伟建 修改：4A密码类型编辑弹窗
 */
function editParamFourA(variableIID){

    var loginName = new Ext.form.TextField({
        name: 'loginName',
        id: 'loginName',
        fieldLabel: '登录用户名',
        afterLabelTextTpl: required,
        readOnly:true,
        emptyText: '',
        labelWidth: 80,
        // labelAlign:'right',
        maxLength: 25,
        padding: '0 5 0 0',
        columnWidth: .9,
        listeners:{
            focus:function(t){
                if(Ext.getCmp("loginName").readOnly){
                    t.setReadOnly(false);
                    //dataForm.getForm().reset();
                }
            }/*,
            blur:function(t){
                t.setReadOnly(true);
                dataForm.getForm().reset();
            }*/
        }
    });
    //获取的4A密码，隐藏输入区域
    var fourAData = new Ext.form.TextField({
        name: 'loginPwd',
        id:'loginPwd',
        fieldLabel: '登录密码',
        inputType : 'password',
        afterLabelTextTpl: required,
        hidden:true,
        emptyText: '',
        labelWidth: 80,
        padding: '0 5 0 0',
        columnWidth: .9
    });

    var loginPwd = new Ext.form.TextField({
        name: 'loginPwd',
        id:'loginPwd',
        fieldLabel: '登录密码',
        maxLength: 25,
        inputType : 'password',
        afterLabelTextTpl: required,
        emptyText: '',
        labelWidth: 80,
        padding: '0 5 0 0',
        columnWidth: .9,
        listeners: {
            focus: function (t) {
                if (Ext.getCmp("loginPwd").readOnly) {
                    t.setReadOnly(false);
                    //dataForm.getForm().reset();
                }
            }
        }
    });

    var resourcesName= new Ext.form.TextField({
        name: 'resourcesName',
        fieldLabel: '资源名',
        emptyText: '',
        labelWidth: 80,
        maxLength: 25,
        padding: '0 5 0 0',
        columnWidth: .9
    });

    var accountName= new Ext.form.TextField({
        name: 'accountName',
        fieldLabel: '账号名',
        afterLabelTextTpl: required,
        displayField: 'accountName',
        maxLength: 25,
        emptyText: '',
        labelWidth: 80,
        padding: '0 5 0 0',
        columnWidth: .9
    });

    var autoObtain = Ext.create('Ext.form.field.Checkbox', {
        checked: false,
        boxLabel: '自动获取',
        name: 'autoObtain',
        id: 'autoObtain',
        // margin: '0 0 0 60',
        labelAlign: 'right'
    });

    //获取密码按钮
    var getPwdButton = Ext.create("Ext.Button",
        {
            id: 'getPwdButton_id',
            cls: 'Common_Btn',
            text: "获取密码",
            handler: function () {
                //非空校验
                if(loginName.getValue().trim() == null || loginName.getValue().trim() == ''){
                    Ext.Msg.alert("提示","登录用户名不能为空");
                    return;
                }
                if(loginPwd.getValue().trim() == null || loginPwd.getValue().trim() == ''){
                    Ext.Msg.alert("提示","登录密码不能为空");
                    return;
                }
                if(accountName.getValue().trim() == null || accountName.getValue().trim() == ''){
                    Ext.Msg.alert("提示","账号名不能为空");
                    return;
                }
                //最大长度校验
                if(loginName.getValue().length > 25){
                    Ext.Msg.alert("提示","登录用户名最大长度为25");
                    return;
                }
                if(loginPwd.getValue().length > 25){
                    Ext.Msg.alert("提示","登录密码最大长度为25");
                    return;
                }
                if(accountName.getValue().length > 25){
                    Ext.Msg.alert("提示","账号名最大长度为25");
                    return;
                }
                if(resourcesName.getValue().length > 25){
                    Ext.Msg.alert("提示","资源名最大长度为25");
                    return;
                }
                Ext.Ajax.request({
                    url: 'variableBase/getFourAPwdForInterface.do',
                    method: 'POST',
                    async: false,
                    params: {
                        variableIID: variableIID,
                        iusername:loginName.getValue(),
                        ipassword:loginPwd.getValue(),
                        iaccount:accountName.getValue(),
                        iresourcename:resourcesName.getValue()
                    },
                    success: function (response, request) {
                        //获取到的4A密码赋值，为接下来保存使用
                        if(Ext.decode(response.responseText).success){
                            fourAData.setValue(Ext.decode(response.responseText).password);
                        }
                        //关闭弹窗
                        // paramFourAWin.close();
                        //grid变量值赋值刷新
                        store.load();
                        checkItems = [];
                        //提示信息
                        Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
                    },
                    failure: function (result, request) {
                        Ext.Msg.alert('提示','获取4A密码失败');
                    }
                });
            }
        });

    //确定按钮
    var qdButton = Ext.create("Ext.Button",
        {
            id: 'qdButton_id',
            cls: 'Common_Btn',
            text: "确定",
            handler: function () {
                //非空校验
                if(loginName.getValue().trim() == null || loginName.getValue().trim() == ''){
                    Ext.Msg.alert("提示","登录用户名不能为空");
                    return;
                }
                if(loginPwd.getValue().trim() == null || loginPwd.getValue().trim() == ''){
                    Ext.Msg.alert("提示","登录密码不能为空");
                    return;
                }
                if(accountName.getValue().trim() == null || accountName.getValue().trim() == ''){
                    Ext.Msg.alert("提示","账号名不能为空");
                    return;
                }
                //最大长度校验
                if(loginName.getValue().length > 25){
                    Ext.Msg.alert("提示","登录用户名最大长度为25");
                    return;
                }
                if(loginPwd.getValue().length > 25){
                    Ext.Msg.alert("提示","登录密码最大长度为25");
                    return;
                }
                if(accountName.getValue().length > 25){
                    Ext.Msg.alert("提示","账号名最大长度为25");
                    return;
                }
                if(resourcesName.getValue().length > 25){
                    Ext.Msg.alert("提示","资源名最大长度为25");
                    return;
                }

                Ext.Ajax.request({
                    url: 'variableBase/saveVariableFourAData.do',
                    method: 'POST',
                    async: false,
                    params: {
                        variableSetIID:variableSetIID,
                        variableIID: variableIID,
                        iusername:loginName.getValue(),
                        ipassword:loginPwd.getValue(),
                        iaccount:accountName.getValue(),
                        iresourcename:resourcesName.getValue(),
                        ivalue:'',
                        autoObtain:autoObtain.getValue() ? 1 : 0
                    },
                    success: function (response, request) {
                        //关闭弹窗
                        paramFourAWin.close();
                        //grid变量值赋值刷新
                        store.load();
                        checkItems = [];
                        Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
                    },
                    failure: function (result, request) {
                        paramFourAWin.close();
                        grid.ipage.moveFirst();
                        Ext.Msg.alert('提示','保存4A密码数据失败');
                    }
                });
            }
        });

    //取消按钮
    var qxButton = Ext.create("Ext.Button",
        {
            id: 'qxButton_id',
            cls: 'Common_Btn',
            text: "取消",
            handler: function () {
                paramFourAWin.close();
            }
        });

    var formItems = [{
        border: false,
        layout: 'column',
        margin: '5',
        items: [loginName]
    },{
        border: false,
        layout: 'column',
        margin: '5',
        items: [loginPwd]
    },{
        border: false,
        layout: 'column',
        margin: '5',
        items: [resourcesName]
    },{
        border: false,
        layout: 'column',
        margin: '5',
        items: [accountName]
    },{
        border: false,
        layout: 'column',
        margin: '5',
        items: [autoObtain]
    }/*,{
        margin: '5',
        layout: {pack: 'center'},
        items: [getPwdButton,qdButton,qxButton]
    }*/
    ];


    dataForm = Ext.create('Ext.form.Panel', {
        height: 250,
        width: 400,
        border: false,
        frame:true,
        layout: 'anchor',
        collapsible: false,
        items: formItems,
        buttons:[/*getPwdButton,*/qdButton,qxButton],
        buttonAlign: 'center'
    });

    //根据变量id查询4A密码类型变量的相关数据回显
    Ext.Ajax.request({
        url: 'variableBase/getFourADetailsData.do',
        method: 'POST',
        async: false,
        params: {
            variableIID: variableIID
        },
        success: function (response, request) {
            //获取4A密码类型变量的相关数据
            if(Ext.decode(response.responseText).success){
                variableSetIID = Ext.decode(response.responseText).iid;
                loginName.setValue(Ext.decode(response.responseText).iusername);
                loginPwd.setValue(desDecode(Ext.decode(response.responseText).ipassword));
                resourcesName.setValue(Ext.decode(response.responseText).iresourcename);
                accountName.setValue(Ext.decode(response.responseText).iaccount);
                if(1 == Ext.decode(response.responseText).autoObtain){
                    autoObtain.setValue(true);
                }
            }
        },
        failure: function (result, request) {
            Ext.Msg.alert('提示','查询4A密码数据失败');
        }
    });

    //弹窗
    var paramFourAWin = Ext.create('Ext.window.Window', {
        title: '4A密码',
        height: 330,
        width: 450,
        layout: 'border',
        modal: true,
        border: false,
        region: 'center',
        items: [dataForm]
    }).show();

}
/**
 * edit end
 */

function desDecode(ciphertext) {//key需和后台保持一致
    var key = CryptoJS.enc.Utf8.parse("0123456789abcdef");
    var decrypt = CryptoJS.AES.decrypt(ciphertext, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7});
    var password = CryptoJS.enc.Utf8.stringify(decrypt).toString();
    return password;
}

function editParam(type, iid) {
    var paramStore = null;
    var paramGrid = null;
    var cols = null;
    var title = '';
    // 数组
    if (type == 4) {
        title = '数组值';
        cols = [{
            text: '索引',
            dataIndex: 'iorder'
        }, {
            text: '值*',
            dataIndex: 'ivalue',
            editor: {
                regex: regex,
                regexText: regexText,
                listeners:{
                    blur:function (t, even, ops) {
                        if (checkLength(t.value) > 255){
                            Ext.Msg.alert('提示','最大输入字符长度255');
                            Ext.getCmp(t.id).setValue('');
                        }
                    }
                }
            },
            flex: 1
        }];
        // 字典
    } else if (type == 5) {
        title = '字典项';
        cols = [{
            text: '顺序',
            dataIndex: 'iorder',
            width: 40
        }, {
            text: 'iid',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        }, {
            text: '键*',
            dataIndex: 'ikey',
            editor: {
                regex: keyRegex,
                regexText: keyRegexText,
                listeners:{
                    blur:function (t, even, ops) {
                        if (checkLength(t.value) > 255){
                            Ext.Msg.alert('提示','最大输入字符长度255');
                            Ext.getCmp(t.id).setValue('');
                        }
                    }
                }
            },
            flex: 1
        }, {
            text: '值*',
            dataIndex: 'ivalue',
            editor: {
                regex: regex,
                regexText:regexText,
                listeners:{
                    blur:function (t, even, ops) {
                        if (checkLength(t.value) > 255){
                            Ext.Msg.alert('提示','最大输入字符长度255');
                            Ext.getCmp(t.id).setValue('');
                        }
                    }
                }
            },
            flex: 1
        }];
    }
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        }, {
            name: 'iorder',
            type: 'int'
        }, {
            name: 'ikey',
            type: 'string'
        }, {
            name: 'ivalue',
            type: 'string'
        }]
    })

    paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'variableBase/getParamList.do?iid=' + iid
        }
    })

    paramGrid = Ext.create('Ext.grid.Panel', {
        padding: grid_space,
        columnLines: true,
        cls: 'customize_panel_back',
        width: '100%',
        height: '100%' - 60,
        layout: 'fit',
        region: 'center',
        selModel: Ext.create('Ext.selection.CheckboxModel'),
        plugins: [
            Ext.create('Ext.grid.plugin.CellEditing', {
                clicksToEdit: 1
            })
        ],
        columns: cols,
        store: paramStore,
        buttonAlign: 'center',
        overflowY: true,
        buttons: [{
            type: 'button',
            text: '增加',
            cls: 'Common_Btn',
            handler: function () {
                var m = paramStore.data.items;
                var max = 0;
                for (let i = 0; i < m.length; i++) {
                    if (m[i].data.iorder > max) {
                        max = m[i].data.iorder;
                    }
                }
                if (type == 4) {
                    var newRecord = Ext.create('paramModel', {
                        iid: 0,
                        iorder: max + 1,
                        ivalue: ''
                    })
                    paramStore.insert(0, newRecord);
                } else if (type == 5) {
                    var newRecord = Ext.create('paramModel', {
                        iid: 0,
                        iorder: max + 1,
                        ikey: '',
                        ivalue: ''
                    })
                    // store.data.items.length
                    paramStore.insert(0, newRecord);
                }
            }
        }, {
            type: 'button',
            text: '保存',
            cls: 'Common_Btn',
            handler: function () {
                var m = paramStore.getModifiedRecords();
                for (let i = 0; i < m.length; i++) {
                    if (type == 5) {
                        if (m[i].data.ikey == '') {
                            Ext.Msg.alert('提示', '键不能为空');
                            return;
                        }
                    }
                    if (m[i].data.ivalue == '') {
                        Ext.Msg.alert('提示', '值不能为空');
                        return;
                    }
                }
                var newRecords = [];
                var n = paramStore.getNewRecords();
                for (let i = 0; i < n.length; i++) {
                    newRecords.push(n[i].data);
                }
                var updateRecords = [];
                var o = paramStore.getUpdatedRecords();
                for (let i = 0; i < o.length; i++) {
                    updateRecords.push(o[i].data);
                }
                if (m.length == 0) {
                    Ext.Msg.alert('提示', '没有要保存的数据');
                } else {
                    Ext.Ajax.request({
                        url: 'variableBase/saveParam.do',
                        method: 'POST',
                        params: {
                            newRecord: Ext.encode(newRecords),
                            updateRecord: Ext.encode(updateRecords),
                            iid: iid,
                            type: type
                        },
                        success: function (response) {
                            var success = Ext.decode(response.responseText).success;
                            var msg = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', msg);
                            if (success) {
                                store.load();
                                paramStore.load();
                                checkItems = [];
                            }
                        },
                        failure: function () {
                            Ext.Msg.alert('提示', '网络连接失败');
                        }
                    })
                }
            }
        }, {
            type: 'button',
            text: '删除',
            cls: 'Common_Btn',
            handler: function () {
                var m = paramGrid.getSelectionModel().getSelection();
                if (m.length == 0) {
                    Ext.Msg.alert('提示', '请选择要删除的数据！');
                    return;
                }
                var iids = [];
                for (let i = 0; i < m.length; i++) {
                    if (m[i].data.iid != 0) {
                        iids.push(m[i].data.iid);
                    }
                }
                if (iids.length == 0) {
                    paramStore.load();
                } else {
                    Ext.Msg.confirm('提示', '确定删除选中的数据吗？', function (btn) {
                        if (btn == 'yes') {
                            Ext.Ajax.request({
                                url: 'variableBase/delParam.do',
                                method: 'POST',
                                params: {
                                    iids: Ext.encode(iids),
                                    iid: iid
                                },
                                success: function (response) {
                                    var success = Ext.decode(response.responseText).success;
                                    var msg = Ext.decode(response.responseText).message;
                                    Ext.Msg.alert('提示', msg);
                                    if (success) {
                                        store.load();
                                        paramStore.load();
                                        checkItems = [];
                                    }
                                },
                                failure: function () {
                                    Ext.Msg.alert('提示', '网络连接失败');
                                }
                            })
                        } else if (btn == 'no') {
                            return;
                        }
                    })
                }
            }
        }, {
            type: 'button',
            text: '取消',
            cls: 'Common_Btn',
            handler: function () {
                paramWin.destroy();
            }
        }]
    })

    var paramWin = Ext.create('Ext.window.Window', {
        title: title,
        height: 500,
        width: 700,
        layout: 'border',
        modal: true,
        border: false,
        region: 'center',
        items: [paramGrid]
    }).show();
}

function downloadFile(params, url) {
    var form = document.createElement("form");
    form.style = "display:none;";
    form.action = url;
    form.method = "post";
    form.target = "form_iframe"
    document.body.appendChild(form);
    if (!document.getElementById("form_iframe")) {
        var ifreame = document.createElement("iframe")
        ifreame.id = "form_iframe";
        ifreame.name = "form_iframe";
        ifreame.style = "display:none;"
        document.body.appendChild(ifreame);
    }
    // 动态创建input并给value赋值
    for (var key in params) {
        var input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = params[key];
        form.appendChild(input);
    }
    form.submit();
    form.remove();
    checkItems = [];
    grid.ipage.moveFirst();
}

function upload() {
    var uploadForm = Ext.create('Ext.form.Panel', {
        width: 400,
        bodyPadding: 10,
        frame: true,
        items: [{
            xtype: 'filefield',
            name: 'importJson',
            fieldLabel: '文件',
            labelWidth: 50,
            msgTarget: 'side',
            allowBlank: false,
            anchor: '100%',
            buttonText: '选择导入文件'
        }, {
            xtype: 'label',
            html: '<span style="color: red">*已经存在变量会强制更新</span>',
            margin: '0 0 0 10'
        }],
        buttonAlign: 'center',
        buttons: [{
            text: '导入',
            handler: function () {
                var form = this.up('form').getForm();
                if (form.isValid()) {
                    form.submit({
                        url: 'variableBase/import.do',
                        waitMsg: '数据处理中...',
                        params: {},
                        success: function (fp, o) {
                            var success = o.result.success;
                            var message = o.result.message
                            Ext.Msg.alert('提示', message);
                            if (success) {
                                uploadWin.close();
                                store.load();
                            }
                        },
                        failure: function (form, action) {
                            var msg = '';
                            if (action.result.message.length == 0) {
                                msg = '网络连接失败';
                            } else {
                                msg = action.result.message;
                            }
                            Ext.Msg.alert('提示', msg);
                        }
                    });
                }
            }
        }, {
            text: '取消',
            handler: function () {
                uploadWin.close();
            }
        }]
    });

    var uploadWin = Ext.create('Ext.window.Window', {
        title: '导入',
        width: 460,
        height: 200,
        modal: true,
        items: [uploadForm]
    }).show();
}

function checkArrayValue(strTemp) {
    for (i = 0; i < strTemp.length; i++) {
        if (strTemp.charCodeAt(i) == 44 || strTemp.charCodeAt(i) == 65292)
            return false;
        else {
            continue;
        }
    }
    return true;
}

function fucCheckLength(strTemp) {
    var i, sum;
    sum = 0;
    for (i = 0; i < strTemp.length; i++) {
        if ((strTemp.charCodeAt(i) >= 0) && (strTemp.charCodeAt(i) <= 255))
            sum = sum + 1;
        else
            sum = sum + 2;
    }
    return sum;
}

function publish(iids) {
    Ext.Ajax.request({
        url: 'variableBase/publish.do',
        method: 'POST',
        params: {
            iid: Ext.encode(iids)
        },
        success: function (response) {
            var success = Ext.decode(response.responseText).success;
            var msg = Ext.decode(response.responseText).message;
            var flag = Ext.decode(response.responseText).flag;
            if (success && flag) {
                var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
                    grow: true,
                    readOnly: true,
                    value: msg
                });

                var textErrorMsg = Ext.create('Ext.window.Window', {
                    title: '错误信息',
                    width: 900,
                    height: 300,
                    layout: 'fit',

                    items: [pubDesc_sm]
                });

                textErrorMsg.show();
            }else{
                Ext.Msg.alert('提示', msg);
                if (success) {
                    store.load();
                    checkItems = [];
                }
            }

        },
        failure: function () {
            Ext.Msg.alert('提示', '网络连接失败');
        }
    })
}
function checkLength(strTemp)
{
    var i,sum;
    sum=0;
    for(i=0;i<strTemp.length;i++)
    {
        if ((strTemp.charCodeAt(i)>=0) && (strTemp.charCodeAt(i)<=255))
            sum=sum+1;
        else
            sum=sum+2;
    }
    return sum;
}
function getData(){
    if (queryType.getValue() == 0) {
        typeParam = 0;
    } else {
        typeParam = queryType.getValue();
    }
    if (queryAttribute.getValue() == 0) {
        attributeParam = '';
    } else {
        attributeParam = queryAttribute.getValue();
    }
    nameParam = queryName.getValue();
    scopeParam = queryScope.getValue();
    statusParam = queryStatus.getValue();
    classStatusValue=classStatusV.getValue();
    grid.ipage.moveFirst();
}