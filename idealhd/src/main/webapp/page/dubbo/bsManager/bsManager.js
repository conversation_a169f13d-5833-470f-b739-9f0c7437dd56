Ext.onReady(function() {
	var bsTypeWindow_bs;
    var bsManagerStore;
    var bsManagerGrid;
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.define('bsManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        }, {
                name: 'groupId',
                type: 'long'
            },
            {
                name: 'groupName',
                type: 'string'
            },
        {
            name: 'bsName',
            type: 'string'
        },
            {
                name: 'groupName',
                type: 'string'
            },
        {
            name: 'state',
            type: 'string'
        }]
    });


    bsManagerStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 15,
        model: 'bsManangerModel',
        proxy: {
            type: 'ajax',
            url: 'bsManager/queryBsModel.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    bsManagerStore.on('beforeload', function(store, options) {
        var new_params = {
            bsName: nameField.getValue(),
            groupName:bsmGroupNameCBox.getValue()
        };

        Ext.apply(bsManagerStore.proxy.extraParams, new_params);
    });
    Ext.define('groupNameModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'GNAME', // 名称
            type : 'string'
        }, {
            name : 'IID', // ID
            type : 'long'
        } ]
    });
    // var groupNameStore = Ext.create('Ext.data.Store', {
    //     model : 'groupNameModel',
    //     autoLoad : true,
    //     proxy : {
    //         type : 'ajax',
    //         url : 'queryGroupName.do',
    //         reader : {
    //             type : 'json',
    //             root : 'dataList'
    //         }
    //     }
    // });
    var groupNameStore = Ext.create('Ext.data.Store', {
        model : 'groupNameModel',
        autoLoad : sdFunctionSortSwitch,
        proxy : {
            type : 'ajax',
            url : 'queryComboGroupName.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });
    var groupNameStoreQuery = Ext.create('Ext.data.Store', {
        model : 'groupNameModel',
        autoLoad : sdFunctionSortSwitch,
        proxy : {
            type : 'ajax',
            url : 'queryComboGroupName.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });
    var bsmGroupNameCBox = Ext.create('Ext.form.field.ComboBox', {
        name : 'bsmGroupNameCBox',
        queryMode : 'local',
        id : 'bsmGroupNameCBox',
        fieldLabel : '功能分类',
        hidden: !sdFunctionSortSwitch,
        labelWidth : 65,
        displayField : 'GNAME',
        valueField : 'IID',
        editable : true,
        emptyText : '--请选择功能分类--',
        store : groupNameStoreQuery,
        width : '20%',
        triggerAction : "all",
        labelAlign : 'right',
    });
    var groupNameCombo = Ext.create('Ext.form.field.ComboBox', {
        name : 'GROUPNAME1',
        queryMode : 'local',
        id : 'GROUPNAME1',
        displayField : 'GNAME',
        valueField : 'IID',
        editable : false,
        emptyText : '--请选择功能分类--',
        store : groupNameStore,
        width : '25%',
        triggerAction : "all",
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '类别主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
        {
            text: 'groupId',
            dataIndex: 'groupId',
            width: 40,
            hidden: true
        },
        {
            text: '功能分类',
            dataIndex: 'groupName',
            width: 200,
            flex: 1,
            hidden: !sdFunctionSortSwitch,
            editor:groupNameCombo,
            renderer : function(value,
                                metadata, record) {
                groupNameStore.reload();
                var groupName = value;
                var str = '';
                var index = groupNameStore.find(
                    'IID', groupName, 0,
                    false, true, true);
                if (index != -1) {
                    str = str
                        + groupNameStore
                            .getAt(index).data.GNAME;
                } else {
                    str = value;
                }
                record.data.GNAME = str;
                record.data.groupName = str;
                return str;
            }
        },
    {
        text: '类别名称',
        dataIndex: 'bsName',
        width: 150,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },{
        text: '状态',
        dataIndex: 'state',
        width: 200,
        renderer:function(value,p,record){
        	var backValue = "";
        	if(value==0){
        		backValue = "<span class='Complete_Green State_Color'>使用中</span>";
        	}else if(value==1){
        		backValue = "<span class='Kill_red State_Color'>已删除</span>";
        	}
        	return backValue;
        }
    },
//    {
//        text: '操作',
//        dataIndex: 'stepOperation',
//        width: 150,
//        renderer: function(value, p, record, rowIndex) {
//            var iid = record.get('iid'); // 其实是requestID
//            var state = record.get('state'); // 其实是requestID
//            console.log('asdf',iid);
//            console.log('state',state);
//            if(!iid) {
//            	iid = -1;
//            	state = -1;
//            }
//            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showbsTypeWindow(' + iid + ','+ state + ')">' + '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;配置类型' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
//        }
//    }
    {
		text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'stepOperation',
		width : 150,
		items : [{
			text : '配置类型',
			iconCls : 'script_set',					 
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid; 
	            var state = grid.getStore().data.items[rowIndex].data.state;
	            if(!iid) {
	            	iid = -1;
	            	state = -1;
	            }
	            showbsTypeWindow(iid,state);
			}
		}]
	} 
    ];
    // 分页工具
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: bsManagerStore,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '类别名称',
        labelWidth: 100,
        labelAlign: 'right',
        name: 'bsNameParam',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var bmsearch_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		layout : 'anchor',
		baseCls:'customize_gray_back',
		bodyCls : 'x-docked-noborder-top',
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			dock : 'top',
			border:false,
			items : [bsmGroupNameCBox,nameField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },'->',
            {
				text : '导入',
				cls : 'Common_Btn',
				handler : importBsNames
			}, {
				text : '导出',
				cls : 'Common_Btn',
				handler : exportBsNames
			},
            {
                text: '增加',
                cls: 'Common_Btn',
                //iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveBsManager
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                //iconCls:'sc_delete',
//                disabled: true,
                handler: deleteBsManager
            },
            {
            	itemId: 'resume',
                text: '恢复',
                cls: 'Common_Btn',
//                disabled: true,
                //iconCls:'sc_add',
                handler: resume
            },
            {
                itemId: 'recover',
                text: '状态恢复',
                cls: 'Common_Btn',
                //iconCls:'sc_return',
                handler: updateBs
            },'-',{
                itemId: 'recover',
                text: '返回',
                cls: 'Common_Btn',
                hidden : !requestFromC3Char_bs,
                //iconCls:'sc_return',
                handler: function(){
                	 popNewTab('脚本看板', 'pandect1.do', {},10, true);
                }
            }
			]
		} ]
	});
    
    function importBsNames() {
		var uploadWindows;
		var uploadForm
		uploadForm = Ext.create('Ext.form.FormPanel', {
			border : false,
			items : [ {
				xtype : 'filefield',
				name : 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel : '选择文件',
				labelWidth : 80,
				anchor : '90%',
				// labelAlign: 'right',
				margin : '10 10 0 40',
				buttonText : '浏览'
			} ],
			buttonAlign : 'center',
			buttons : [ {
				text : '确定',
				handler : upExeclData
			}, {
				text : '取消',
				handler : function() {
					uploadWindows.close();
				}
			},
			{
				text: '下载模板',
				handler: function() {
					window.location.href = 'downloadSsTemplate.do?fileName=4';
				}
			}]
		});
		uploadWindows = Ext.create('Ext.window.Window', {
			title : '导入文件',
			layout : 'fit',
			height : 200,
			width : 600,
			modal : true,
			// autoScroll : true,
			items : [ uploadForm ],
			listeners : {
				close : function(g, opt) {
					uploadForm.destroy();
				}
			}
		});
		uploadWindows.show();
		function upExeclData() {
			var form = uploadForm.getForm();
			var hdupfile = form.findField("fileName").getValue();
			if (hdupfile == '') {
				Ext.Msg.alert('提示', "请选择文件...");
				return;
			}
			uploadTemplate(form);
		}
		function uploadTemplate(form) {
			if (form.isValid()) {
				form
						.submit({
							url : 'importBsName.do',
							params : {
								importType : 2
							},
							success : function(form, action) {
								var sumsg = Ext
										.decode(action.response.responseText).message;
								Ext.Msg.alert('提示', sumsg);
								uploadWindows.close();
								pageBar.moveFirst();
								return;
							},
							failure : function(form, action) {
								var msg = Ext
										.decode(action.response.responseText).message;
								Ext.Msg.alert('提示', msg);
								return;
							}
						});
			}
		}
	}
    
    function exportBsNames()
    {
    	var record = bsManagerGrid.getSelectionModel ().getSelection ();
    	var iidStr = "";
		Ext.Array.each (record, function (recordObj)
		{
			iidStr += "," + recordObj.get ('iid');
		});
    	if(iidStr.length<=0)
    	{
    		Ext.Msg.alert ('提示','请选择要操作的行！');
    		return;
    	}
    	iidStr = iidStr.substr(1);
    	window.location.href = 'exportBsName.do?iidStr='+iidStr;
    }
    
    bsManagerGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
//        id: 'bsmGrid',
        store: bsManagerStore,
        split : true,
        multiSelect : true,
        cls:'customize_panel_back',
        selModel: selModel,
        plugins: [cellEditing],
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//        bbar: pageBar,
        border: false,
        padding : grid_space,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        dockedItems: [{
            border:false,
            items: [bmsearch_form]
        }]
    });

//    bsManagerGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
//        bsManagerGrid.down('#delete').setDisabled(selections.length === 0);
//    });
//    
//    bsManagerGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
//        bsManagerGrid.down('#resume').setDisabled(selections.length === 0);
//    });

    let renderTo = "scriptService_grid_areaBS";
    if(isTabSwitch){
        renderTo += bsnow;
    }
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: renderTo,
//        cls:'customize_panel_back',
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [bmsearch_form,bsManagerGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
		if(bsTypeWindow_bs) {
			bsTypeWindow_bs.center();
		}
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    // 从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        var eValue = eval('jsonObj.' + key);
        return jsonObj['' + key + ''];
    }
    function clearQueryWhere() {
        bmsearch_form.getForm().findField("bsNameParam").setValue('');
        bsmGroupNameCBox.setValue('');
    }
    function add() {
        var store = bsManagerGrid.getStore();
        var p = {
            groupName:'',
            iid: '',
            bsName: '',
            stepOperation: ''
        };
        store.insert(0, p);
        bsManagerGrid.getView().refresh();
    }
    
    function resume(){
    	var data = bsManagerGrid.getView().getSelectionModel().getSelection();
    	if (data.length == 0) {
    		Ext.Msg.alert('提示', '请先选择您要操作的行!');
    		return;
    	} else {
    		Ext.Msg.confirm("请确认", "是否要恢复一级分类?",
    				function(button, text) {
    			if (button == "yes") {
    				var ids = [];
    				Ext.Array.each(data,
    						function(record) {
    					var iid = record.get('iid');
    					var status = record.get('state');
                        if(0==status){
                        Ext.Msg.alert('提示', '该类别已经是使用中状态！');	
                        return;
                        }
    					if (iid) {
    						ids.push(iid);
    					}
    				});
    				if (ids.length == 0) {
    					bsManagerStore.reload();
    					return;
    				}
    				Ext.Ajax.request({
    					url: 'updateBs.do',
    					params: {
    						updateIds: ids.join(',')
    					},
    					method: 'POST',
    					success: function(response, opts) {
    						var success = Ext.decode(response.responseText).success;
    						// 当后台数据同步成功时
    						if (success) {
    							bsManagerStore.reload();
    							Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    						} else {
    							Ext.Msg.alert('提示', '恢复失败！');
    						}
    					},
    					failure: function(result, request) {
    						secureFilterRs(result, "操作失败！");
    					}
    				});
    			}
    		});
    	}
    }
    
    function saveBsManager() {
        var m = bsManagerStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var n = 0;
            var bsName = m[i].get("bsName").trim();
            var groupName=m[i].get("groupName").trim();
            if ("" == bsName || null == bsName) {
                setMessage('一级分类不能为空！');
                return;
            }
            if (fucCheckLength(bsName) > 200) {
                setMessage('一级分类不能超过200字符！');
                return;
            }
            if(sdFunctionSortSwitch){
                for (var k = 0; k < bsManagerStore.getCount(); k++) {
                    var record = bsManagerStore.getAt(k);
                    var cname = record.data.bsName;
                    var gname=record.data.groupName;
                    if ((cname.trim() == bsName)&&gname.trim()==groupName) {
                        n = n + 1;
                    }
                }
                if (n > 1) {
                    setMessage('功能分类和类别名称不能有重复！');
                    return;
                }
            }else {
                for (var k = 0; k < bsManagerStore.getCount(); k++) {
                    var record = bsManagerStore.getAt(k);
                    var cname = record.data.bsName;
                    if (cname.trim() == bsName) {
                        n = n + 1;
                    }
                }
                if (n > 1) {
                    setMessage('类别名称不能有重复！');
                    return;
                }
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveBsManager.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    bsManagerStore.modified = [];
                    bsManagerStore.reload();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                }
            },
            // failure : function(result, request) {
            // Ext.Msg.alert('提示', '保存失败！');
            // }
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function updateBs() {
    	var data = bsManagerGrid.getView().getSelectionModel().getSelection();
    	if (data.length == 0) {
    		Ext.Msg.alert('提示', '请先选择您要操作的行!');
    		return;
    	} else {
    		Ext.Msg.confirm("请确认", "是否要恢复一级分类?",
    				function(button, text) {
    			if (button == "yes") {
    				var ids = [];
    				Ext.Array.each(data,
    						function(record) {
    					var iid = record.get('iid');
    					var status = record.get('state');
                        if(0==status){
                        Ext.Msg.alert('提示', '该类别已经是使用中状态！');	
                        return;
                        }
    					// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
    					if (iid) {
    						ids.push(iid);
    					}
    				});
    				if (ids.length == 0) {
    					bsManagerStore.reload();
    					return;
    				}
    				Ext.Ajax.request({
    					url: 'updateBs.do',
    					params: {
    						updateIds: ids.join(',')
    					},
    					method: 'POST',
    					success: function(response, opts) {
    						var success = Ext.decode(response.responseText).success;
    						// 当后台数据同步成功时
    						if (success) {
    							bsManagerStore.reload();
    							Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    						} else {
    							Ext.Msg.alert('提示', '恢复失败！');
    						}
    					},
    					failure: function(result, request) {
    						secureFilterRs(result, "操作失败！");
    					}
    				});
    			}
    		});
    	}
    }
    function deleteBsManager() {
        var data = bsManagerGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除一级分类?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
//                    var flags=0;
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        var status = record.get('state');
                        if(1==status){
                        Ext.Msg.alert('提示', '该类别已经被删除！');	
                        return;
                        }
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
//                        	if (iid!=1 && iid!=2&& iid!=3&& iid!=4&& iid!=5&& iid!=6){
                        		ids.push(iid);
//                        	}else{
//                        		flags=1;
//                        	}
                        }else{
                        	 bsManagerStore.remove(record);
                             }
                    });
//                    if (ids.length == 0) {
//                    	/*if(flags==1){
//                    		 Ext.Msg.alert('提示', '特定类别不可删除！');
//                    	}*/
//                        bsManagerStore.reload();
//                        return;
//                    }
                    if(ids.length>0){
                        Ext.Ajax.request({
                        url: 'deleteBsModel.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            var message = Ext.decode(response.responseText).message;
                            // 当后台数据同步成功时
                            if (success) {
                                bsManagerStore.reload();
//                                if(flags==1){
//                                	Ext.Msg.alert('提示', "特定类别不可删除,其他类别"+Ext.decode(response.responseText).message);
//                                }else{
                                	Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
//                                }
                            } else {
                                if(null!=message){
                                    Ext.Msg.alert('提示', message);
                                }else {
                                    Ext.Msg.alert('提示', '删除失败！');
                                }
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    bsManagerGrid.getView().refresh();
                    }
                }
            });
        }
    }
    
    function setMessage(msg) {
    	Ext.Msg.alert('提示', msg);
    }

    function showbsTypeWindow(bsManagerId,state) {
    	if (bsManagerId == -1) {
    		Ext.Msg.alert('提示', '请先保存类别后再配置类型。');
    		return;
    	}
    	if (state==1){
    		setMessage("状态为“已删除”时，不可配置类型!");
    	}else{
    		if (bsTypeWindow_bs == undefined || !bsTypeWindow_bs.isVisible()) {
    			bsTypeWindow_bs = Ext.create('Ext.window.Window', {
    				title: '二级分类',
    				modal: true,
    				closeAction: 'destroy',
    				constrain: true,
    				autoScroll: true,
    				width: contentPanel.getWidth(),
    				height: contentPanel.getHeight(),
    				draggable: false,
    				// 禁止拖动
    				resizable: false,
    				// 禁止缩放
    				layout: 'fit',
    				loader: {
    					url: 'forwardBsType.do',
    					params: {
    						bsManagerId: bsManagerId
    					},
    					autoLoad: true,
    					scripts: true
    				}
    			});
    		}
    		bsTypeWindow_bs.show();
    	}
    }
});
