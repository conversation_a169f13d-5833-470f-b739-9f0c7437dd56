Ext.onReady(function() {
    // 清理主面板的各种监听时间
//    destroyRubbish();
    Ext.define('bsThreeTypeModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },{
            name: 'twoBsTypeId',
            type: 'long'
        },
        {
            name: 'threeTypeName',
            type: 'string'
        },
        {
            name: 'idel',
            type: 'string'
        }]
    });
    var threeBsTypeStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 15,
        model: 'bsThreeTypeModel',
        proxy: {
            type: 'ajax',
            url: 'bsManager/queryThreeBsType.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var bsTypeColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '类型主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型名称',
        dataIndex: 'threeTypeName',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },{
        text: '二级分类id',
        dataIndex: 'twoBsTypeId',
        width: 40,
        flex: 1,
        hidden: true
    },{
        text: '状态',
        dataIndex: 'idel',
        width: 200,
        renderer:function(value,p,record){
        	var backValue = "";
        	if(value==0){
        		backValue = "<span class='Complete_Green State_Color'>使用中</span>";
        	}else if(value==1){
        		backValue = "<span class='Kill_red State_Color'>已删除</span>";
        	}
        	return backValue;
        }
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: threeBsTypeStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        afterPageText: ' 页 共 {0} 页',
        beforePageText: '第 ',
        firstText: '第一页 ',
        prevText: '前一页',
        nextText: '下一页',
        lastText: '最后一页',
        refreshText: '刷新',
        displayMsg: '第{0}条 到 {1} 条数据  共找到{2}条记录',
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var threeBsTypeGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
        cls:'customize_panel_back',
        store: threeBsTypeStore,
        selModel: selModel,
        plugins: [cellEditing],
//        bbar: pageBar,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border: false,
        columnLines: true,
        columns: bsTypeColumns,
        animCollapse: false,
        dockedItems: [{
            xtype: 'toolbar',
//            baseCls:'customize_gray_back',  
            items: [{
                text: '增加',
                cls: 'Common_Btn',
               // iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveBsType
            },
            '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
               // iconCls:'sc_delete',
                disabled: true,
                handler: deleteBsType
            },'-', {
                itemId: 'recover',
                text: '状态恢复',
                cls: 'Common_Btn',
                //iconCls:'sc_return',
                handler: updateBsType
            },'-',{
				text : '导入',
				cls : 'Common_Btn',
				handler : importBsTypeNames
			},'-',{
				text : '导出',
				cls : 'Common_Btn',
				handler : exportBsTypeNames
			}]
        }]
    });
    function importBsTypeNames() {
		var uploadWindows;
		var uploadForm
		uploadForm = Ext.create('Ext.form.FormPanel', {
			border : false,
			items : [ {
				xtype : 'filefield',
				name : 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel : '选择文件',
				labelWidth : 80,
				anchor : '90%',
				// labelAlign: 'right',
				margin : '10 10 0 40',
				buttonText : '浏览'
			} ],
			buttonAlign : 'center',
			buttons : [ {
				text : '确定',
				handler : upExeclData
			}, {
				text : '取消',
				handler : function() {
					uploadWindows.close();
				}
			},
			{
				text: '下载模板',
				handler: function() {
					window.location.href = 'downloadSsTemplate.do?fileName=6';
				}
			}]
		});
		uploadWindows = Ext.create('Ext.window.Window', {
			title : '导入文件',
			layout : 'fit',
			height : 200,
			width : 600,
			modal : true,
			// autoScroll : true,
			items : [ uploadForm ],
			listeners : {
				close : function(g, opt) {
					uploadForm.destroy();
				}
			}
		});
		uploadWindows.show();
		function upExeclData() {
			var form = uploadForm.getForm();
			var hdupfile = form.findField("fileName").getValue();
			if (hdupfile == '') {
				Ext.Msg.alert('提示', "请选择文件...");
				return;
			}
			uploadTemplate(form);
		}
		function uploadTemplate(form) {
			if (form.isValid()) {
				form
						.submit({
							url : 'importThreeBsTypeName.do',
							params : {
                                twoBsTypeId : twoBsTypeId
							},
							success : function(form, action) {
								var sumsg = Ext.decode(action.response.responseText).message;
								Ext.Msg.alert('提示', sumsg);
								uploadWindows.close();
								pageBar.moveFirst();
								return;
							},
							failure : function(form, action) {
								var msg = Ext
										.decode(action.response.responseText).message;
								Ext.Msg.alert('提示', msg);
								return;
							}
						});
			}
		}
	
    	
    }
    function exportBsTypeNames() {
    	var record = threeBsTypeGrid.getSelectionModel ().getSelection ();
    	var iidStr = "";
		Ext.Array.each (record, function (recordObj)
		{
			iidStr += "," + recordObj.get ('iid');
		});
		if(iidStr.length<=0)
    	{
    		Ext.Msg.alert ('提示','请选择要操作的行！');
    		return;
    	}
    	iidStr = iidStr.substr(1);
    	window.location.href = 'exportThreeBsTypeName.do?iidStr='+iidStr;
    }
    threeBsTypeStore.on('beforeload', function(store, options) {
        var queryparams = {
            twoBsTypeId: twoBsTypeId
        };
        Ext.apply(threeBsTypeStore.proxy.extraParams, queryparams);
    });
    threeBsTypeGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        threeBsTypeGrid.down('#delete').setDisabled(selections.length === 0);
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "threeType_div",
        layout: 'border',
        width : '100%',
        height :contentPanel.getHeight()- modelHeigth,
        border : false,
        items: [threeBsTypeGrid]
    });

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
    function add() {
        var store = threeBsTypeGrid.getStore();
        var p = {
            iid: 0,
            threeTypeName: '',
            twoBsTypeId :twoBsTypeId
        };
        store.insert(0, p);
        threeBsTypeGrid.getView().refresh();
    }

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        threeBsTypeGrid.setHeight(contentPanel.getHeight() - 25);
        threeBsTypeGrid.setWidth(contentPanel.getWidth());
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(threeBsTypeGrid);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    // 将被选中的记录的flowid组织成json串，作为参数给后台处理
    function getSelectedJsonData() {
        var flowIdList = Ext.getCmp('releaseGrid').getSelectionModel().getSelection();
        if (flowIdList.length < 1) {
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = flowIdList.length; i < len; i++) {
            if (i == 0) {
                jsonData = jsonData + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
            } else {
                jsonData = jsonData + "," + '{"iid":"' + parsIIDJson('iid', flowIdList[i].data) + '"}';
            }
        }
        jsonData = jsonData + "]";
        return jsonData;
    }

    // 从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        var eValue = eval('jsonObj.' + key);
        return jsonObj['' + key + ''];
    }
    function saveBsType() {
        var m = threeBsTypeStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var threeTypeName = m[i].get("threeTypeName").trim();

            if ("" == threeTypeName || null == threeTypeName) {
                setMessage('三级分类名称不能为空！');
                return;
            }
            if (fucCheckLength(threeTypeName) > 200) {
                setMessage('三级分类名称不能超过200字符！');
                return;
            }
            for (var k = 0; k < threeBsTypeStore.getCount(); k++) {
				var record = threeBsTypeStore.getAt(k);
				var cname = record.data.threeTypeName;
				if (cname.trim() == threeTypeName) {
					n = n + 1;
				}
			}
			if (n > 1) {
				setMessage('类型名称不能有重复！');
				return;
			}

            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveThreeBsType.do',
            method: 'POST',
            params: {
                jsonData: jsonData,
                twoBsTypeId: twoBsTypeId
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    threeBsTypeStore.modified = [];
                    threeBsTypeStore.load();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                }
            },
            // failure : function(result, request) {
            // Ext.Msg.alert('提示', '保存失败！');
            // }
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function deleteBsType() {
        var data = threeBsTypeGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除三级分类?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        var state = record.get('idel');
                        if(1==state){
                        Ext.Msg.alert('提示', '该类型已经是被删除状态！');	
                        return;
                        }
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                            ids.push(iid);
                        }else{
                        	 threeBsTypeStore.remove(record);
                             }
                    });
                    if (ids.length>0) {
                        Ext.Ajax.request({
                        url: 'deleteThreeBsType.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                threeBsTypeStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    threeBsTypeGrid.getView().refresh();
                    }
                }
            });
        }
    }
    function updateBsType() {
    	var data = threeBsTypeGrid.getView().getSelectionModel().getSelection();
    	if (data.length == 0) {
    		Ext.Msg.alert('提示', '请先选择您要操作的行!');
    		return;
    	} else {
    		Ext.Msg.confirm("请确认", "是否要恢复三级分类?",
    				function(button, text) {
    			if (button == "yes") {
    				var ids = [];
    				Ext.Array.each(data,
    						function(record) {
    					var iid = record.get('iid');
    					var state = record.get('idel');
    					if(0==state){
                        Ext.Msg.alert('提示', '该类型已经是使用中状态！');	
                        return;
                        }
    					if (iid) {
    						ids.push(iid);
    					}
    				});
    				if (ids.length == 0) {
    					threeBsTypeStore.reload();
    					return;
    				}
    				Ext.Ajax.request({
    					url: 'updateThreeBsType.do',
    					params: {
    						updateIds: ids.join(',')
    					},
    					method: 'POST',
    					success: function(response, opts) {
    						var success = Ext.decode(response.responseText).success;
    						// 当后台数据同步成功时
    						if (success) {
    							threeBsTypeStore.reload();
    							Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    						} else {
    							Ext.Msg.alert('提示', '恢复失败！');
    						}
    					},
    					failure: function(result, request) {
    						secureFilterRs(result, "操作失败！");
    					}
    				});
    			}
    		});
    	}
    }
});