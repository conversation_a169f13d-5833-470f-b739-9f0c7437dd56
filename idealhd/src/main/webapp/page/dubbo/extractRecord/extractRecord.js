
Ext.onReady(function() {
	var detailWindowsForFileExtract;
	var fileExtractInstanceDetailStore;
	var fileExtractInstanceId;
	var fileExtractInstanceFtpId;
    destroyRubbish();

    Ext.define('FileExtractHistoryModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },{
            name: 'time',
            type: 'string'
        },{
            name: 'userName',
            type: 'string'
        },{
            name: 'state',
            type: 'int'
        },{
            name: 'ftpId',
            type: 'int'
        }]
    });
    
    Ext.define('FileExtractHistoryDetailModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },{
            name: 'instanceId',
            type: 'int'
        },{
            name: 'agentIp',
            type: 'string'
        },{
            name: 'agentPort',
            type: 'int'
        },{
            name: 'fileName',
            type: 'string'
        },{
            name: 'ftpPath',
            type: 'string'
        },{
            name: 'state',
            type: 'int'
        },{
            name: 'hostName',
            type: 'string'
        },{
            name: 'osType',
            type: 'string'
        }]
    });
    var fileExtractStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 20,
        model: 'FileExtractHistoryModel',
        proxy: {
            type: 'ajax',
            url: 'fileExtractHistory.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    fileExtractInstanceDetailStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 20,
        model: 'FileExtractHistoryDetailModel',
        proxy: {
            type: 'ajax',
            url: 'fileExtractDetailHistory.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    fileExtractInstanceDetailStore.on('beforeload', function(store, options) {
        var new_params = {
            instanceId: fileExtractInstanceId
        };
        Ext.apply(fileExtractInstanceDetailStore.proxy.extraParams, new_params);
    });

    var fileExtractColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    }, {
        text: '实例主键',
        dataIndex: 'iid',
        hidden: true
    },{
        text: '状态',
        dataIndex: 'state',
        width: 100,
        renderer: function(value, p, record) {
        	var backValue = "";
            if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20 || value == 5) {
                backValue = "<span class='Complete_Green State_Color'>完成</span>"
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 40) {
                backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
            } else if (value == 50) {
                backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            }
            return backValue;
        }
    },{
        text: '时间标识',
        dataIndex: 'time',
        width: 200
    },{
        text: '提取执行用户',
        dataIndex: 'userName',
        width: 200
    },{
//        text: '操作',
//        dataIndex: 'sysOperation',
//        width: 160,
//        renderer: function(value, p, record) {
//            var iid = record.get('iid');
//            var ftpId = record.get('ftpId');
//            return '<span class="switch_span">' +
//            			'<a href="javascript:void(0)" onclick="openDetailWindowsForFileExtract('+iid+', '+ ftpId +')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"/>详情</a>&nbsp;&nbsp;' +
//            			'<a href="javascript:void(0)" onclick="downloadForFileExtract('+iid+', '+ ftpId +')"><img src="images/monitor_bg.png" align="absmiddle" class="script_download"/>打包下载</a>' +
//            	   '</span>';
//
//        },
    	text : '操作',
		xtype : 'actiontextcolumn',
		dataIndex: 'sysOperation',
		width : 160,
		items : [{
			text : '详情',
			iconCls : 'monitor_search',					 
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid; 
	            var ftpId = grid.getStore().data.items[rowIndex].data.ftpId;
	            openDetailWindowsForFileExtract(iid,ftpId);
			}
		},{
			text : '打包下载',
			iconCls : 'script_download',					 
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid; 
	            var ftpId = grid.getStore().data.items[rowIndex].data.ftpId;
	            downloadForFileExtract(iid,ftpId);
			}
		}],
        flex: 1
    }];
    var fileExtractPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: fileExtractStore,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        dock: 'bottom',
        displayInfo: true
    });
    var fileExtractGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        store: fileExtractStore,
        cls:'customize_panel_back',
        border: false,
        padding : grid_space,
        columnLines: true,
        columns: fileExtractColumns,
//        bbar: fileExtractPageBar
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar'
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "extractRecord_area",
        layout: 'border',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [fileExtractGrid]
    });

    contentPanel.on('resize',
    function() {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });

    var fileExtractInstanceDetailColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '状态',
        dataIndex: 'state',
        width: 130,
        renderer: function(value, p, record) {
        	var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },
    {
        text: 'IP',
        dataIndex: 'agentIp',
        width: 120
    },
    {
        text: '端口号',
        dataIndex: 'agentPort',
        flex: 1
    },
    {
        text: '主机名',
        dataIndex: 'hostName',
        width: 120
    },{
        text: 'FTP路径',
        dataIndex: 'ftpPath',
        width: 120
    },{
        text: '文件名',
        dataIndex: 'fileName',
        width: 120
    },
    {
        text: '操作系统',
        dataIndex: 'osType',
        width: 120
    }];
    
    var fileExtractInstanceDetailGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        height: contentPanel.getHeight() - 100,
        store: fileExtractInstanceDetailStore,
        cls:'customize_panel_back',
        border: true,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        columnLines: true,
        columns: fileExtractInstanceDetailColumns,
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        dockedItems: [{
            xtype: 'toolbar',
            // baseCls:'customize_gray_back',
            height: 45,
            items: [{
                cls: 'Common_Btn',
                xtype: 'button',
                text: '下载',
                handler: function() {
                	var records = fileExtractInstanceDetailGrid.getSelectionModel().getSelection();
                	if (records.length < 1) {
                        Ext.Msg.alert('提示', '请选择记录！');
                        return;
                    }
                	var ids = new Array();
                	for(var i = 0, len = records.length; i < len; i++){
	  					var iiid = records[i].data.iid;
	  					ids.push(iiid);
                	}
                	window.location.href = 'downloadFileExtract.do?detailIds='+ids.join(',')+'&ftpId='+fileExtractInstanceFtpId;
                }
            }]
        }]
    });
    
    if (!detailWindowsForFileExtract) {
		detailWindowsForFileExtract = Ext.create('Ext.window.Window', {
            title: '详情',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'hide',
            width: contentPanel.getWidth() ,
            height: contentPanel.getHeight()  ,
            items: [fileExtractInstanceDetailGrid]
        });
	}
    function openDetailWindowsForFileExtract(iid, ftpId) {
    	fileExtractInstanceId = iid;
    	fileExtractInstanceFtpId = ftpId;
    	detailWindowsForFileExtract.show();
    	fileExtractInstanceDetailStore.load();
    }
    
    function downloadForFileExtract(iid, ftpId) {
    	var url = 'downloadFileExtractPack.do?insId='+iid+'&ftpId='+ftpId;
    	window.location.href = encodeURI(url);
    }
});
