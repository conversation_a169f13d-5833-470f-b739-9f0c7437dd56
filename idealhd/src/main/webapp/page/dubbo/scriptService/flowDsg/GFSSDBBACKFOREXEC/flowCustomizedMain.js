/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSDBBACKFOREXEC;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-60,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSDBBACKFOREXEC.do',
	        params: {
	        	flag:flagGFSSDBBACKFOREXEC,
	        	actionType:actionTypeGFSSDBBACKFOREXEC
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSDBBACKFOREXEC.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea',
		{
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        padding : '5 0 5 5',
	        labelWidth: 70,
	        height : 50,
	        maxLength : 2000,
	        labelSepartor : "：",
	        readOnly: actionTypeGFSSDBBACKFOREXEC=='dbback',
	        value: backInfoContentGFSSDBBACKFOREXEC,
	        width : contentPanel.getWidth () - 300
		});
		
	 var noWayButton = Ext.create('Ext.Button', {
		    text : '打回',
		    margin : '0 0 0 5',
		    textAlign : 'center',
//			    cls : 'Common_Btn',
		    handler : function() {
		    	var scriptAudiFrom = 1;
		    	if(actionTypeGFSSDBBACKFOREXEC=='dbcheckForExec') {
		    		scriptAudiFrom = 2;
		    	}
		    	Ext.MessageBox.wait ("数据处理中...", "进度条");
				var backI = backInfo.getValue();
				if (fucCheckLength (backI.trim ()) < 1)
				{
					Ext.Msg.alert ('提示', "请输入打回原因!");
					return;
					
				}
				if (!illegalCharPassForTextArea (backI))
				{
					Ext.MessageBox.alert ("提示", "打回原因只能由汉字、字母、数字、下划线、冒号以及减号组成！");
					return;
				}
				if (fucCheckLength (backI.trim ()) > 4000)
				{
					Ext.Msg.alert ('提示', "打回原因长度最大为4000个字符!");
					return;
				}
				Ext.Ajax.request (
				{
				    url : 'operWorkitemByiidForSsPublish.do',
				    method : 'POST',
				    params :
				    {
				        istateForQuery: 2,
				        scriptAudiFrom: scriptAudiFrom,
				        iidForQuery: workItemidGFSSDBBACKFOREXEC,
				        ibackInfo: backI
				    },
				    success : function (response, opts)
				    {
					    var success = Ext.decode (response.responseText).success;
					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
					    {
					    	if(fromGFSSDBBACKFOREXEC==1) {
					    		messageWindow1.close();
					    		destroyRubbish(); //销毁本页垃圾
					    		contentPanel.getLoader().load({
					    			url: 'pandect1.do',
					    			scripts: true});
					    	} else {
					    		messageWindow.getLoader ().load (
										{
										    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
										    autoLoad : true,
										    scripts : true
										});
										messageWindow.setTitle ('待办事项');
					    	}
					    	
					    });
				    }
				});
		    }
	  });
	 
	 var backToDbCheckButton = Ext.create('Ext.Button', {
		    text : '返回',
		    margin : '0 0 0 5',
		    textAlign : 'center',
//			    cls : 'Common_Btn',
		    handler : function() {
		    	if(fromGFSSDBBACKFOREXEC==1) {
		    		messageWindow1.close();
		    	} else {
					messageWindow.getLoader ().load (
							{
								url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
								autoLoad : true,
								scripts : true
							});
					messageWindow.setTitle ('待办事项');
				}
		    }
	  });
	 
	 var dbtermiButton = Ext.create('Ext.Button', {
		 text : '终止',
		 margin : '0 0 0 5',
		 textAlign : 'center',
//			    cls : 'Common_Btn',
		 handler : function() {
			 Ext.Ajax.request (
						{
						    url : 'operWorkitemByiidForSsPublish.do',
						    method : 'POST',
						    params :
						    {
						        istateForQuery : 6,
						        iidForQuery : workItemidGFSSDBBACKFOREXEC
						    },
						    success : function (response, opts)
						    {
							    var success = Ext.decode (response.responseText).success;
							    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
							    {
							    	if(fromGFSSDBBACKFOREXEC==1) {
							    		messageWindow1.close();
							    		destroyRubbish(); //销毁本页垃圾
							    		contentPanel.getLoader().load({
							    			url: 'pandect1.do',
							    			scripts: true});
							    	} else {
							    		messageWindow.getLoader ().load (
											{
											    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
											    autoLoad : true,
											    scripts : true
											});
											messageWindow.setTitle ('待办事项');
							    	}
							    });
						    }
						});
		 }
	 });
	
	var sendAudiButton = Ext.create("Ext.Button", {
		text: "提交",
		disabled : false,
		handler:function(){
			sendAudiGFSSDBBACKFOREXEC(this);
		}
	});
	
	var submitFromPanel = Ext.create('Ext.form.Panel', {
		width : '100%',
		frame : true,
		buttonAlign : "left",
		height: 170,
		items:[backInfo],
		buttons:[sendAudiButton,dbtermiButton,backToDbCheckButton]
	});
	
	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSDBBACKFOREXEC",
			width : '100%',
			height : contentPanel.getHeight (), 
			autoScroll: true,
			border : false,
			bodyPadding : 5,
			items : [ mainTabs,submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSDBBACKFOREXEC();

		function initGFSSDBBACKFOREXEC()
		{
		}
		
		contentPanel.on('resize', function() {
		});
});

function sendAudiGFSSDBBACKFOREXEC(btn) {
	var isReady = checkDataIsReadyGFSSDBBACKFOREXEC();
	
	if(isReady) {
		Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var auditorStore_tap = Ext.create('Ext.data.Store', {
		    autoLoad: false,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getExecAuditorList.do?scriptLevel='+scriptFlowLevelForTaskAudiGFSSDBBACKFOREXEC,
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
		    editable: false,
		    fieldLabel: "审核人",
		    store: auditorStore_tap,
		    queryMode: 'local',
		    width: 390,
		    displayField: 'fullName',
		    valueField: 'loginName',
		    labelWidth : 58,
			labelAlign : 'right'
		  });
		var taskName = new Ext.form.TextField({
			name: 'taskName',
			fieldLabel: '任务名称',
			emptyText: '',
			labelWidth : 58,
			labelAlign : 'right',
			value: taskNameForDbCheckGFSSDBBACKFOREXEC,
			width: 390
		});
		
		Ext.create('Ext.window.Window', {
	  		title : '配置双人复核信息',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'destroy',
	  		width : 400,
	  		height : 150,
	  		items:[taskName, auditorComBox_tap],
	  		buttonAlign: 'center',
	  		buttons: [{ 
	  			xtype: "button", 
	  			text: "确定", 
	  			handler: function () {
	  				var self = this;
	  				var auditor = auditorComBox_tap.getValue();
	  				if(!auditor) {
	  					Ext.Msg.alert('提示', "没有选择审核人！");
	  					return;
	  				}
	  				
	  				var taskN = Ext.util.Format.trim(taskName.getValue());
	  				if(Ext.isEmpty(taskN)) {
	  					Ext.Msg.alert('提示', "任务名称不能为空！");
	  					return;
	  				}
	  				
	  				if (fucCheckLength(taskN) > 255) {
                        Ext.Msg.alert('提示', "任务名称不能超过255字符！");
                        return;
                    }
	  				
	  				var startData = orgStartDataGFSSDBBACKFOREXEC();
	  				Ext.Ajax.request({
	  				    url : 'scriptFlowExecAuditing.do',
	  				    method : 'POST',
	  				    params : {
	  				    	iidForQuery : workItemidGFSSDBBACKFOREXEC,
	  				    	serviceId: iidGFSSDBBACKFOREXEC,
	  				    	auditor: auditor,
	  				    	taskName: taskN,
	  				    	startData: JSON.stringify(startData),
	  				    	scriptLevel: scriptFlowLevelForTaskAudiGFSSDBBACKFOREXEC
	  				    },
	  				    success: function(response, opts) {
	  				        var success = Ext.decode(response.responseText).success;
	  				        var message = Ext.decode(response.responseText).message;
	  				        if(success) {
	  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	  				        	self.up("window").close();
	  				        	if(workItemidGFSSDBBACKFOREXEC) {
	  				        		if(fromGFSSDBBACKFOREXEC==1) {
	  						    		messageWindow1.close();
	  						    	} else {
	  									messageWindow.getLoader ().load (
	  											{
	  												url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
	  												autoLoad : true,
	  												scripts : true
	  											});
	  									messageWindow.setTitle ('待办事项');
	  								}
	  				        	}
	  				        } else {
	  				        	Ext.MessageBox.alert("提示", message);
	  				        }
	  				    },
	  				    failure: function(result, request) {
	  				    	secureFilterRs(result,"操作失败！");
	  				    }
	  			    });
	  			}
	  		}]
	  	}).show();
		auditorStore_tap.load();
	}
}

