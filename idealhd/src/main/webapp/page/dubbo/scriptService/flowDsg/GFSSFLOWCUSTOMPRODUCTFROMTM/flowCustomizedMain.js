/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var instanceNameObjOutSideGFSSFLOWCUSTOMPRODUCTFROMTM;
var customNameObjOutSideGFSSFLOWCUSTOMPRODUCTFROMTM;
var editorSonGFSSFLOWCUSTOMPRODUCTFROMTM;

Ext.onReady(function() {
    //清理各种监听
    destroyRubbish();
    
    var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSFLOWCUSTOMPRODUCTFROMTM
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });

    //0.1 Graph 
    var mainTabs = Ext.widget('tabpanel', {
        tabPosition: 'top',
        activeTab: 0,
        width: '100%',
        height: contentPanel.getHeight() - 52,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        items: [{
            title: '图形化显示',
            loader: {
                url: 'flowCustomizedImgScriptServiceGFSSFLOWCUSTOMPRODUCTFROMTM.do',
                params: {
                    flag: flagGFSSFLOWCUSTOMPRODUCTFROMTM,
                    actionType: actionTypeGFSSFLOWCUSTOMPRODUCTFROMTM
                },
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        },
        {
            hidden: true,
            title: '列表显示',
            loader: {
                url: 'flowCustomizedListScriptServiceGFSSFLOWCUSTOMPRODUCTFROMTM.do',
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        }]
    });

    customNameObjOutSideGFSSFLOWCUSTOMPRODUCTFROMTM = Ext.create('Ext.form.field.Text', {
        fieldLabel: '模板名称',
        labelWidth: 70,
        width: 300
    });
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
//        cls: 'Common_Btn',
        margin:'0 5 0 0',
        textAlign: 'center',
        handler: function() {
        	saveCustomTemplateGFSSFLOWCUSTOMPRODUCTFROMTM();
        }
    });
    var backButton = Ext.create('Ext.Button', {
        text: '返回',
//        cls: 'Common_Btn',
         margin:'0 5 0 0',
        textAlign: 'center',
        handler: function() {
            destroyRubbish();
            contentPanel.getLoader().load({
                url: 'scriptServicesTaskExec.do',
                params: {
                    'filter_bussId': filter_bussIdGFSSFLOWCUSTOMPRODUCTFROMTM,
                    'filter_bussTypeId': filter_bussTypeIdGFSSFLOWCUSTOMPRODUCTFROMTM,
                    'filter_scriptName': filter_scriptNameGFSSFLOWCUSTOMPRODUCTFROMTM,
                    'filter_serviceName': filter_serviceNameGFSSFLOWCUSTOMPRODUCTFROMTM,
                    'filter_scriptType': filter_scriptTypeGFSSFLOWCUSTOMPRODUCTFROMTM
                }
            });
            if (Ext.isIE) {
                CollectGarbage();
            }
        }
    });

    
    var viewBasicInfoButton = Ext.create("Ext.Button", {
//		cls: 'Common_Btn',
    	margin:'0 5 0 0',
		text: "基本信息",
		disabled : false,
		handler:function(){
			Ext.create('Ext.window.Window', {
	            title: '基本信息',
	            autoScroll: true,
	            modal: true,
	            closeAction: 'destroy',
	            buttonAlign: 'center',
	            draggable: true,
	            resizable: false,
	            width: 500,
	            height: 328,
	            loader: {
	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
	            	params: {
	            		creatorFullName: creatorFullName,
		                bussName: bussName,
		                bussTypeName:bussTypeName,
		                funcDescText: funcDescText,
		                serviceName:serviceName
	            	},
	            	autoLoad: true
	            },
	            dockedItems: [{
	                xtype: 'toolbar',
	                border: false,
	                dock: 'bottom',
	                margin: '0 0 5 0',
	                layout: {pack: 'center'},
	                items: [{
	                    xtype: 'button',
	                    text: '关闭',
	                    cls: 'Common_Btn',
	                    handler: function() {
	                        this.up("window").close();
	                    }
	                }]
	            }]
	        }).show();
		}
	});
    
    var submitFromPanel = Ext.create('Ext.form.Panel', {
        width: '100%',
        layout: {
            type: 'hbox',
            padding:'5',
            align:'top'
        },
        items: [customNameObjOutSideGFSSFLOWCUSTOMPRODUCTFROMTM,{
                xtype:'tbspacer',
                flex:1
            },saveButton, viewBasicInfoButton, backButton,{
                xtype:'tbspacer',
                flex:1
            },{
                xtype:'tbspacer',
                width:310
            }]
        
        /*dockedItems: [{
            xtype: 'toolbar',
            dock: 'bottom',
            items: [customNameObjOutSideGFSSFLOWCUSTOMPRODUCTFROMTM, '->', saveButton, viewBasicInfoButton, backButton]
        }]*/
    });

    var MainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "flowCustomizedMainDivGFSSFLOWCUSTOMPRODUCTFROMTM",
        width: '100%',
        height: contentPanel.getHeight(),
        autoScroll: true,
        border: false,
        bodyPadding: 5,
        items: [mainTabs, submitFromPanel]
    });
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(MainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        mainTabs.setWidth('100%');
    });
    initGFSSFLOWCUSTOMPRODUCTFROMTM();
    function initGFSSFLOWCUSTOMPRODUCTFROMTM() {
    }
});