/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSDBCHECKFOREXEC;
var cellObjGFSSDBCHECKFOREXEC;
var phaseDataListGFSSDBCHECKFOREXEC;
var editorGFSSDBCHECKFOREXEC;
var graphGFSSDBCHECKFOREXEC;
var modelGFSSDBCHECKFOREXEC;
var cmdNameCountGFSSDBCHECKFOREXEC = 1;
var actNameCountMapGFSSDBCHECKFOREXEC = {};
actStartInfo['GFSSDBCHECKFOREXEC'] = {};

Ext.onReady (function ()
{
	try
	{
		if (configwindowFlowGFSSDBCHECKFOREXEC != null)
		{
			configwindowFlowGFSSDBCHECKFOREXEC.destroy ();
		}
		if (cellObjGFSSDBCHECKFOREXEC != null)
		{
			cellObjGFSSDBCHECKFOREXEC.destroy ();
		}
		if (phaseDataListGFSSDBCHECKFOREXEC != null)
		{
			phaseDataListGFSSDBCHECKFOREXEC= null;
		}
		if (editorGFSSDBCHECKFOREXEC != null)
		{
			editorGFSSDBCHECKFOREXEC.destroy ();
		}
		if (graphGFSSDBCHECKFOREXEC != null)
		{
			graphGFSSDBCHECKFOREXEC.destroy ();
		}
		if (modelGFSSDBCHECKFOREXEC != null)
		{
			modelGFSSDBCHECKFOREXEC=null;
		}
	}
	catch(err)
	{
	}
	
	getPhaseGFSSDBCHECKFOREXEC ();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSDBCHECKFOREXEC (container, outline, toolbar, sidebar, status)
{
	
	if (!mxClient.isBrowserSupported ())
	{
		Ext.Msg.alert ('提示', '当前浏览器不支持此功能!');
	}
	else
	{
		//自定义连线图标
		mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
		mxConstants.MIN_HOTSPOT_SIZE = 16;
		mxConstants.DEFAULT_HOTSPOT = 1;
		
		// Enables guides
		mxGraphHandler.prototype.guidesEnabled = true;
		
		// Alt disables guides
		mxGuide.prototype.isEnabledForEvent = function (evt)
		{
			return !mxEvent.isAltDown (evt);
		};
		
		// Enables snapping waypoints to terminals
		mxEdgeHandler.prototype.snapToTerminals = true;
		
		// Workaround for Internet Explorer ignoring certain CSS directives
		if (mxClient.IS_QUIRKS)
		{
			document.body.style.overflow = 'hidden';
			new mxDivResizer (container);
			new mxDivResizer (outline);
			new mxDivResizer (toolbar);
			new mxDivResizer (sidebar);
			new mxDivResizer (status);
		}
		
		 editorGFSSDBCHECKFOREXEC = new mxEditor ();
		 parent.editorSonGFSSDBCHECKFOREXEC=editorGFSSDBCHECKFOREXEC;
		 graphGFSSDBCHECKFOREXEC = editorGFSSDBCHECKFOREXEC.graph;
		 modelGFSSDBCHECKFOREXEC = graphGFSSDBCHECKFOREXEC.getModel ();
		 
		 graphGFSSDBCHECKFOREXEC.setTooltips(false);
		
		// Disable highlight of cells when dragging from toolbar
		graphGFSSDBCHECKFOREXEC.setDropEnabled (false);
		
		// Centers the port icon on the target port
		graphGFSSDBCHECKFOREXEC.connectionHandler.targetConnectImage = true;
		
		// Does not allow dangling edges
		//连线是否必须连接至节点
		graphGFSSDBCHECKFOREXEC.setAllowDanglingEdges (false);
		
		// Sets the graph container and configures the editor
		editorGFSSDBCHECKFOREXEC.setGraphContainer (container);
		//键盘热键控制
		var config = mxUtils.load ('mxgraph-master/examples/editors/config/keyhandler-commons.xml')
		        .getDocumentElement ();
		editorGFSSDBCHECKFOREXEC.configure (config);
		
		//不允许重复连线
		graphGFSSDBCHECKFOREXEC.setMultigraph(false);
		//不允许自己连自己
		graphGFSSDBCHECKFOREXEC.setAllowLoops(false);
		
		var group = new mxCell ('Group', new mxGeometry (), 'group');
		group.setVertex (true);
		group.setConnectable (false);
		editorGFSSDBCHECKFOREXEC.defaultGroup = group;
		editorGFSSDBCHECKFOREXEC.groupBorderSize = 20;
		
		// Disables drag-and-drop into non-swimlanes.
		graphGFSSDBCHECKFOREXEC.isValidDropTarget = function (cell, cells, evt)
		{
			return this.isSwimlane (cell);
		};
		
		// Disables drilling into non-swimlanes.
		graphGFSSDBCHECKFOREXEC.isValidRoot = function (cell)
		{
			return this.isValidDropTarget (cell);
		}

		// Does not allow selection of locked cells
		graphGFSSDBCHECKFOREXEC.isCellSelectable = function (cell)
		{
			return !this.isCellLocked (cell);
		};
		
		// Returns a shorter label if the cell is collapsed and no
		// label for expanded groups
		graphGFSSDBCHECKFOREXEC.getLabel = function(cell)
		{
			var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
			var geometry = this.model.getGeometry(cell);
			
			if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null ||
				(geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) &&
				geometry.width >= 2)
			{
				var style = this.getCellStyle(cell);
				var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
				var max = geometry.width / (fontSize * 1.5);
				
				if (max < label.length)
				{
					return label.substring(0, max) + '...';
				}
			}
			
			return label;
		};
		
		// Enables wrapping for vertex labels
		graphGFSSDBCHECKFOREXEC.isWrapping = function(cell)
		{
			return this.model.isCollapsed(cell);
		};

		// Disables HTML labels for swimlanes to avoid conflict
		// for the event processing on the child cells. HTML
		// labels consume events before underlying cells get the
		// chance to process those events.
		//
		// NOTE: Use of HTML labels is only recommended if the specific
		// features of such labels are required, such as special label
		// styles or interactive form fields. Otherwise non-HTML labels
		// should be used by not overidding the following function.
		// See also: configureStylesheet.
		graphGFSSDBCHECKFOREXEC.isHtmlLabel = function (cell)
		{
			return !this.isSwimlane (cell);
		}

		// Shows a "modal" window when double clicking a vertex.
		graphGFSSDBCHECKFOREXEC.dblClick = function (evt, cell)
		{
			
			// Do not fire a DOUBLE_CLICK event here as mxEditor will
			// consume the event and start the in-place editor.
			if (this.isEnabled () && !mxEvent.isConsumed (evt) && cell != null && this.isCellEditable (cell))
			{
				if (this.model.isEdge (cell)) //|| !this.isHtmlLabel (cell))
				{
					//连线编辑
//					this.startEditingAtCell (cell);
				}
				else
				{
					cellObjGFSSDBCHECKFOREXEC = cell;
					if(cell.style!='beginStyle'&&cell.style!='endStyle')
						{
						if(cell.style=='scriptServiceStyle')
						{
							if(parent.actionTypeGFSSDBCHECKFOREXEC=='exec' || parent.actionTypeGFSSDBCHECKFOREXEC=='audi'|| parent.actionTypeGFSSDBCHECKFOREXEC=='dbcheckForExec'|| parent.actionTypeGFSSDBCHECKFOREXEC=='dbbackForExec') {
								openExecScriptWindwGFSSDBCHECKFOREXEC ();
							} else {
								openEditScriptWindwGFSSDBCHECKFOREXEC ();
							}
						} else if(cell.style=='usertaskStyle') {
							openUTWindwGFSSDBCHECKFOREXEC ();
						} else if(cell.style=='callflowStyle') {
							graphViewStack = new Array();
							stackFlowView = Ext.create('widget.window', {
					            title: '详细信息',
					            closable: true,
					            closeAction: 'destroy',
					            width: contentPanel.getWidth(),
					            minWidth: 350,
					            height: contentPanel.getHeight(),
					            draggable: true,
					            resizable: false,
					            modal: true,
					            loader: {
					            	url : 'flowWinViewer.do', 
					                params: {
					                    iid: cell.scriptId,
					                    flag: 1,
					                    actionType:'exec',
					                    readOnly:'1',
					                    pageFrom: 'GFSSDBCHECKFOREXEC',
										isShowInWindow: 1,
										parentMxIid: cell.mxIid,
										isStack: true
					                },
					                autoLoad: true,
					                scripts: true
					            }
					        }).show();
		    				return ;
						} else  {
							openWindwGFSSDBCHECKFOREXEC ();
						}
					}
				}
			}
			
			// Disables any default behaviour for the double click
			mxEvent.consume (evt);
		};
		
		// Enables new connections
		//节点之间可以连接
		graphGFSSDBCHECKFOREXEC.setConnectable (true);
		
		// Adds all required styles to the graph (see below)
		//增加样式
		configureStylesheetGFSSDBCHECKFOREXEC (graphGFSSDBCHECKFOREXEC);
		
		actNameCountMapGFSSDBCHECKFOREXEC['scriptServiceStyle'] = 0;
		actNameCountMapGFSSDBCHECKFOREXEC['usertaskStyle'] = 0;
		actNameCountMapGFSSDBCHECKFOREXEC['callflowStyle'] = 0;
		if(parent.actionTypeGFSSDBCHECKFOREXEC!='exec'&&parent.actionTypeGFSSDBCHECKFOREXEC!='audi'&&parent.actionTypeGFSSDBCHECKFOREXEC!='view'&&parent.actionTypeGFSSDBCHECKFOREXEC!='dbcheck'&&parent.actionTypeGFSSDBCHECKFOREXEC!='dbback'&&parent.actionTypeGFSSDBCHECKFOREXEC!='dbcheckForExec'&&parent.actionTypeGFSSDBCHECKFOREXEC!='dbbackForExec') {
			addSidebarIconGFSSDBCHECKFOREXEC (graphGFSSDBCHECKFOREXEC, sidebar, '基础脚本', 'images/mxgraphImages/basescript.png', 'scriptServiceStyle',0);
			addSidebarIconGFSSDBCHECKFOREXEC (graphGFSSDBCHECKFOREXEC, sidebar, '人工提醒', 'images/mxgraphImages/ut.png', 'usertaskStyle',1);
			addSidebarIconGFSSDBCHECKFOREXEC (graphGFSSDBCHECKFOREXEC, sidebar, '作业调用', 'images/mxgraphImages/callflow.png', 'callflowStyle',3);
		}
		
		// Creates a new DIV that is used as a toolbar and adds
		// toolbar buttons.
		var spacer = document.createElement ('div');
		spacer.style.display = 'inline';
		spacer.style.padding = '8px';
		
		// Defines a new export action
		editorGFSSDBCHECKFOREXEC.addAction ('export', function (editor, cell)
		{
			var textarea = document.createElement ('textarea');
			textarea.style.width = '400px';
			textarea.style.height = '400px';
			var enc = new mxCodec (mxUtils.createXmlDocument ());
			var node = enc.encode (editor.graph.getModel ());
			textarea.value = mxUtils.getPrettyXml (node);
			showModalWindowGFSSDBCHECKFOREXEC (graphGFSSDBCHECKFOREXEC, 'XML', textarea, 410, 440);
		});
		
		editorGFSSDBCHECKFOREXEC.addAction ('deleteBefore', function (editor, cell)
				{
    			var cells=editor.graph.getSelectionCells();
    			for(i=0;i<cells.length;i++)
				{
    				if(cells[i].style=='beginStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<开始>节点！');
    				return false;
    				}
    				if(cells[i].style=='endStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<结束>节点！');
    				return false;
    				}
				}
					editor.execute ('delete');
				});
		editorGFSSDBCHECKFOREXEC.addAction ('save', function (editor, cell)
		{
			var bussId = parent.bussCbOutSideGFSSDBCHECKFOREXEC.getValue ();
			var bussTypeId = parent.bussTypeCbOutSideGFSSDBCHECKFOREXEC.getValue ();
			var instanceName = parent.instanceNameObjOutSideGFSSDBCHECKFOREXEC.getValue ();
			if (bussId == null)
			{
				Ext.Msg.alert ('提示', '请选择一级分类!');
				return null;
			}
			if (bussTypeId == null)
			{
				Ext.Msg.alert ('提示', '请选择二级分类!');
				return null;
			}
			if (instanceName.trim() == '')
			{
				Ext.Msg.alert ('提示', '请填写服务名称!');
				return null;
			}
			checkInstanceNameExisitInNewVersionGFSSDBCHECKFOREXEC();
		});
		
		addToolbarButtonGFSSDBCHECKFOREXEC (editorGFSSDBCHECKFOREXEC, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
		addToolbarButtonGFSSDBCHECKFOREXEC (editorGFSSDBCHECKFOREXEC, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
		addToolbarButtonGFSSDBCHECKFOREXEC (editorGFSSDBCHECKFOREXEC, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
		addToolbarButtonGFSSDBCHECKFOREXEC (editorGFSSDBCHECKFOREXEC, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);
		
		var outln = new mxOutline (graphGFSSDBCHECKFOREXEC, outline);
		
		var splash = document.getElementById ('splashGFSSDBCHECKFOREXEC');
		if (splash != null)
		{
			try
			{
				mxEvent.release (splash);
				mxEffects.fadeOut (splash, 100, true);
			}
			catch (e)
			{
				
				// mxUtils is not available (library not loaded)
				splash.parentNode.removeChild (splash);
			}
		}
		
		graphGFSSDBCHECKFOREXEC.popupMenuHandler.factoryMethod = function(menu, cell, evt)
		{
			return createPopupMenuGFSSDBCHECKFOREXEC(graphGFSSDBCHECKFOREXEC, menu, cell, evt);
		};
		
		initFunGFSSDBCHECKFOREXEC(graphGFSSDBCHECKFOREXEC);
	}
	
};

/**增加右键删除菜单**/
function createPopupMenuGFSSDBCHECKFOREXEC(graph, menu, cell, evt)
{
	if (cell != null)
	{
		menu.addItem('删除', 'images/delete.png', function()
		{
			editorGFSSDBCHECKFOREXEC.execute ('deleteBefore');
		});
	}
};
/**
 * 初始化方法
 */
function initFunGFSSDBCHECKFOREXEC(graph)
{
	if(parent.iidGFSSDBCHECKFOREXEC>0)
	{
		//修改将原图加载
		loadGraphGFSSDBCHECKFOREXEC(graph);
		var root2FlowWindow = modelGFSSDBCHECKFOREXEC.getRoot ();
		var count = modelGFSSDBCHECKFOREXEC.getChildCount (root2FlowWindow);
		for (var i = 0; i < count; i++)
		{
			var cells = root2FlowWindow.getChildAt (i);
			var counts = cells.getChildCount ();
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				if (!modelGFSSDBCHECKFOREXEC.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
				{
					cellss.mxIid = parent.iidGFSSDBCHECKFOREXEC+":"+cellss.id;
					if(!actStartInfo['GFSSDBCHECKFOREXEC'].hasOwnProperty(cellss.mxIid)) {
						if(parseInt(cellss.phaseId)==0) {
							actStartInfo['GFSSDBCHECKFOREXEC'][cellss.mxIid] = {
									'actNo': cellss.id,
									'actType': parseInt(cellss.phaseId),
									'actName': cellss.value,
									'isShutdown': cellss.isShutdown
							};
						} else if(parseInt(cellss.phaseId)==1) {
							actStartInfo['GFSSDBCHECKFOREXEC'][cellss.mxIid] = {
									'actNo': cellss.id,
									'actType': parseInt(cellss.phaseId),
									'actName': cellss.value,
									'message': cellss.ireminfo
							};
						}
					}
				}
			}
		}
		
		Ext.Ajax.request({
            url: 'getDbcheckForExecData.do',
            method: 'POST',
            params: {
            	workItemId: parent.workItemidGFSSDBCHECKFOREXEC
            },
            success: function(response, options) {
                var success = Ext.decode(response.responseText).success;
                if(success) {
                	var dataS = Ext.decode(response.responseText).data;
                	actStartInfo['GFSSDBCHECKFOREXEC'] = JSON.parse(dataS);
                } else {
                	Ext.Msg.alert ('提示', '获取双人复核作业执行审核参数信息失败!');
                }
            },
            failure: function(result, request) {
            	Ext.Msg.alert ('提示', '获取双人复核作业执行审核参数信息失败!');
            }
        });
	}else {
		addBeginEndCellGFSSDBCHECKFOREXEC (graph, "开始","beginStyle",1,1);
		addBeginEndCellGFSSDBCHECKFOREXEC (graph, "结束","endStyle",150,150);
	}
	
};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSDBCHECKFOREXEC (editor, toolbar, action, label, image, isTransparent)
{
	if (image != null)
	{
		var img = document.createElement ('img');
		img.setAttribute ('src', image);
		img.style.width = '74px';
		img.style.height = '30px';
		img.style.verticalAlign = 'middle';
		img.title = label;
		img.style.marginRight = '10px';
		img.style.marginTop = '2px';
	}
	mxEvent.addListener (img, 'click', function (evt)
	{
		if('delete'==action)
			{
			var cells=editor.graph.getSelectionCells();
			for(i=0;i<cells.length;i++)
				{
				if('开始'==cells[i].value)
					{
					Ext.Msg.alert ('提示', '不删除<开始>节点！');
					return false;
					}
				}
			}
		
		editor.execute (action);
	});
	toolbar.appendChild (img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSDBCHECKFOREXEC (graph, title, content, width, height)
{
	var background = document.createElement ('div');
	background.style.position = 'absolute';
	background.style.left = '0px';
	background.style.top = '0px';
	background.style.right = '0px';
	background.style.bottom = '0px';
	background.style.background = 'black';
	mxUtils.setOpacity (background, 50);
	document.body.appendChild (background);
	
	if (mxClient.IS_IE)
	{
		new mxDivResizer (background);
	}
	
	var x = Math.max (0, document.body.scrollWidth / 2 - width / 2);
	var y = Math.max (10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
	var wnd = new mxWindow (title, content, x, y, width, height, false, true);
	wnd.setClosable (true);
	
	// Fades the background out after after the window has been closed
	wnd.addListener (mxEvent.DESTROY, function (evt)
	{
		graph.setEnabled (true);
		mxEffects.fadeOut (background, 50, true, 10, 30, true);
	});
	
	graph.setEnabled (false);
	graph.tooltipHandler.hide ();
	wnd.setVisible (true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSDBCHECKFOREXEC (graph, label, styleName,w,h)
{
	var parent = graph.getDefaultParent ();
	var model = graph.getModel ();
	
	var v1 = null;
	
	model.beginUpdate ();
	try
	{
		v1 = graph.insertVertex (parent, null, label, w, h, 50, 25, styleName);
		v1.setConnectable (true);
	}
	finally
	{
		model.endUpdate ();
	}
};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSDBCHECKFOREXEC (graph, sidebar, label, image, styleName,phaseId)
{
	var funct = function (graph, evt, cell, x, y)
	{
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			actNameCountMapGFSSDBCHECKFOREXEC[styleName]= ++actNameCountMapGFSSDBCHECKFOREXEC[styleName];
			v1 = graph.insertVertex (parent, null, label+actNameCountMapGFSSDBCHECKFOREXEC[styleName], x, y, 90, 32, styleName);
			v1.setConnectable (true);
			v1.phaseId=phaseId;
			
		}
		finally
		{
			model.endUpdate ();
		}
		graph.setSelectionCell (v1);
	}

	var img = document.createElement ('img');
	img.setAttribute ('src', image);
	img.style.width = '80px';
	img.style.height = '32px';
	img.title = label;
	img.style.marginLeft = '10px';
	img.style.marginBottom = '15px';
	
	sidebar.appendChild (img);
	
	var dragElt = document.createElement ('div');
	dragElt.style.border = 'dashed black 1px';
	dragElt.style.width = '120px';
	dragElt.style.height = '120px';
	
	var ds = mxUtils.makeDraggable (img, graph, funct, dragElt, 0, 0, true, true);
	ds.setGuidesEnabled (true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSDBCHECKFOREXEC (graph)
{
	
	var style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
	style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
	style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTSTYLE] = 0;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
	style[mxConstants.STYLE_RESIZABLE] = '0';//不可缩放
	graph.getStylesheet ().putDefaultVertexStyle (style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('beginStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('endStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet ().putCellStyle ('cmdStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
	style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
	style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_STARTSIZE] = '30';
	style[mxConstants.STYLE_FONTSIZE] = '16';
	style[mxConstants.STYLE_FONTSTYLE] = 1;
	graph.getStylesheet ().putCellStyle ('group', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_FONTCOLOR] = '#774400';
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_FONTSIZE] = '10';
	style[mxConstants.STYLE_FONTSTYLE] = 2;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
	graph.getStylesheet ().putCellStyle ('port', style);
	
	style = graph.getStylesheet ().getDefaultEdgeStyle ();
	style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_STROKEWIDTH] = '1';
	style[mxConstants.STYLE_STROKECOLOR] = '#595758';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_EDGE] =mxConstants.EDGESTYLE_ELBOW;
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('scriptServiceStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('usertaskStyle', style);

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSDBCHECKFOREXEC() {
	graphGFSSDBCHECKFOREXEC.getModel().beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag="+flagGFSSDBCHECKFOREXEC+"&instanceID="+parent.iidGFSSDBCHECKFOREXEC));
		var dec = new mxCodec(doc);
		dec.decode(doc.getDocumentElement(), graphGFSSDBCHECKFOREXEC.getModel());
	} finally {
		graphGFSSDBCHECKFOREXEC.getModel().endUpdate();
	}
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSDBCHECKFOREXEC ()
{
	configwindowFlowGFSSDBCHECKFOREXEC = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/mxgraph/flowCustomizedWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSDBCHECKFOREXEC ()
{
	configwindowFlowGFSSDBCHECKFOREXEC = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : false,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/GFSSDBCHECKFOREXEC/flowCustomizedExecScriptWindow.jsp",
	        params :
	        {
	            flag : flagGFSSDBCHECKFOREXEC
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function openEditScriptWindwGFSSDBCHECKFOREXEC ()
{
	var h = contentPanel.getHeight ();
	var w = contentPanel.getWidth ();
	if(parent.actionTypeGFSSDBCHECKFOREXEC=='view'||parent.actionTypeGFSSDBCHECKFOREXEC=='exec'||parent.actionTypeGFSSDBCHECKFOREXEC=='audi'||parent.actionTypeGFSSDBCHECKFOREXEC=='dbcheck'||parent.actionTypeGFSSDBCHECKFOREXEC=='dbback'||parent.actionTypeGFSSDBCHECKFOREXEC=='dbcheckForExec'||parent.actionTypeGFSSDBCHECKFOREXEC=='dbbackForExec') {
		h = 130;
		w = 600;
	}
	configwindowFlowGFSSDBCHECKFOREXEC = Ext.create ('Ext.window.Window', {
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : w,
	    height : h,
	    loader : {
	        url : "page/dubbo/scriptService/flowDsg/GFSSDBCHECKFOREXEC/flowCustomizedEditScriptWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSDBCHECKFOREXEC ()
{
	configwindowFlowGFSSDBCHECKFOREXEC = Ext.create ('Ext.window.Window',
	{
	    title : '人工提醒配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : 580,
	    height : 215,
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/GFSSDBCHECKFOREXEC/scriptServiceUTWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSDBCHECKFOREXEC ()
{
	//更新名后续刷新才能显示
	graphGFSSDBCHECKFOREXEC.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSDBCHECKFOREXEC ()
{
	mainGFSSDBCHECKFOREXEC (document.getElementById ('graphContainerGFSSDBCHECKFOREXEC'), document.getElementById ('outlineContainerGFSSDBCHECKFOREXEC'), document
            .getElementById ('toolbarContainer'), document.getElementById ('sidebarContainerGFSSDBCHECKFOREXEC'), document
            .getElementById ('statusContainerGFSSDBCHECKFOREXEC'));
	
}
