/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSFLOWCUSTOMEDITPRODUCT;
var cellObjGFSSFLOWCUSTOMEDITPRODUCT;
var phaseDataListGFSSFLOWCUSTOMEDITPRODUCT;
var editorGFSSFLOWCUSTOMEDITPRODUCT;
var graphGFSSFLOWCUSTOMEDITPRODUCT;
var modelGFSSFLOWCUSTOMEDITPRODUCT;
var cmdNameCountGFSSFLOWCUSTOMEDITPRODUCT = 1;
var actNameCountMapGFSSFLOWCUSTOMEDITPRODUCT = {};
actStartInfo['G<PERSON><PERSON>LOWCUSTOMEDITPRODUCT'] = {};

Ext.onReady(function() {
    try {
        if (configwindowFlowGFSSFLOWCUSTOMEDITPRODUCT != null) {
            configwindowFlowGFSSFLOWCUSTOMEDITPRODUCT.destroy();
        }
        if (cellObjGFSSFLOWCUSTOMEDITPRODUCT != null) {
            cellObjGFSSFLOWCUSTOMEDITPRODUCT.destroy();
        }
        if (phaseDataListGFSSFLOWCUSTOMEDITPRODUCT != null) {
            phaseDataListGFSSFLOWCUSTOMEDITPRODUCT = null;
        }
        if (editorGFSSFLOWCUSTOMEDITPRODUCT != null) {
            editorGFSSFLOWCUSTOMEDITPRODUCT.destroy();
        }
        if (graphGFSSFLOWCUSTOMEDITPRODUCT != null) {
            graphGFSSFLOWCUSTOMEDITPRODUCT.destroy();
        }
        if (modelGFSSFLOWCUSTOMEDITPRODUCT != null) {
            modelGFSSFLOWCUSTOMEDITPRODUCT = null;
        }
    } catch(err) {}

    getPhaseGFSSFLOWCUSTOMEDITPRODUCT();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSFLOWCUSTOMEDITPRODUCT(container, outline, toolbar, sidebar, status) {

    if (!mxClient.isBrowserSupported()) {
        Ext.Msg.alert('提示', '当前浏览器不支持此功能!');
    } else {
        //自定义连线图标
        mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
        mxConstants.MIN_HOTSPOT_SIZE = 16;
        mxConstants.DEFAULT_HOTSPOT = 1;

        mxGraphHandler.prototype.guidesEnabled = true;

        mxGuide.prototype.isEnabledForEvent = function(evt) {
            return ! mxEvent.isAltDown(evt);
        };

        mxEdgeHandler.prototype.snapToTerminals = true;

        if (mxClient.IS_QUIRKS) {
            document.body.style.overflow = 'hidden';
            new mxDivResizer(container);
            new mxDivResizer(outline);
            new mxDivResizer(toolbar);
            new mxDivResizer(sidebar);
            new mxDivResizer(status);
        }

        editorGFSSFLOWCUSTOMEDITPRODUCT = new mxEditor();
        parent.editorSonGFSSFLOWCUSTOMEDITPRODUCT = editorGFSSFLOWCUSTOMEDITPRODUCT;
        graphGFSSFLOWCUSTOMEDITPRODUCT = editorGFSSFLOWCUSTOMEDITPRODUCT.graph;
        modelGFSSFLOWCUSTOMEDITPRODUCT = graphGFSSFLOWCUSTOMEDITPRODUCT.getModel();
        
        graphGFSSFLOWCUSTOMEDITPRODUCT.setTooltips(false);

        graphGFSSFLOWCUSTOMEDITPRODUCT.setDropEnabled(false);

        graphGFSSFLOWCUSTOMEDITPRODUCT.connectionHandler.targetConnectImage = true;

        //连线是否必须连接至节点
        graphGFSSFLOWCUSTOMEDITPRODUCT.setAllowDanglingEdges(false);

        editorGFSSFLOWCUSTOMEDITPRODUCT.setGraphContainer(container);
        //键盘热键控制
        var config = mxUtils.load('mxgraph-master/examples/editors/config/keyhandler-commons.xml').getDocumentElement();
        editorGFSSFLOWCUSTOMEDITPRODUCT.configure(config);

        //不允许重复连线
        graphGFSSFLOWCUSTOMEDITPRODUCT.setMultigraph(false);
        //不允许自己连自己
        graphGFSSFLOWCUSTOMEDITPRODUCT.setAllowLoops(false);

        var group = new mxCell('Group', new mxGeometry(), 'group');
        group.setVertex(true);
        group.setConnectable(false);
        editorGFSSFLOWCUSTOMEDITPRODUCT.defaultGroup = group;
        editorGFSSFLOWCUSTOMEDITPRODUCT.groupBorderSize = 20;

        graphGFSSFLOWCUSTOMEDITPRODUCT.isValidDropTarget = function(cell, cells, evt) {
            return this.isSwimlane(cell);
        };

        graphGFSSFLOWCUSTOMEDITPRODUCT.isValidRoot = function(cell) {
            return this.isValidDropTarget(cell);
        }

        graphGFSSFLOWCUSTOMEDITPRODUCT.isCellSelectable = function(cell) {
            return ! this.isCellLocked(cell);
        };

        graphGFSSFLOWCUSTOMEDITPRODUCT.getLabel = function(cell) {
            var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
            var geometry = this.model.getGeometry(cell);

            if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null || (geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) && geometry.width >= 2) {
                var style = this.getCellStyle(cell);
                var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
                var max = geometry.width / (fontSize * 1.5);

                if (max < label.length) {
                    return label.substring(0, max) + '...';
                }
            }

            return label;
        };

        graphGFSSFLOWCUSTOMEDITPRODUCT.isWrapping = function(cell) {
            return this.model.isCollapsed(cell);
        };

        graphGFSSFLOWCUSTOMEDITPRODUCT.isHtmlLabel = function(cell) {
            return ! this.isSwimlane(cell);
        }

        graphGFSSFLOWCUSTOMEDITPRODUCT.dblClick = function(evt, cell) {

            if (this.isEnabled() && !mxEvent.isConsumed(evt) && cell != null && this.isCellEditable(cell)) {
                if (this.model.isEdge(cell)) {} else {
                    cellObjGFSSFLOWCUSTOMEDITPRODUCT = cell;
                    if (cell.style != 'beginStyle' && cell.style != 'endStyle') {
                        if (cell.style == 'scriptServiceStyle') {
                            //直接调用脚本
                            openExecScriptWindwGFSSFLOWCUSTOMEDITPRODUCT();
                        } else if (cell.style == 'usertaskStyle') {
                            //UT提醒任务
                            openUTWindwGFSSFLOWCUSTOMEDITPRODUCT();
                        } else if(cell.style=='callflowStyle') {
                    		graphViewStack = new Array();
							stackFlowView = Ext.create('widget.window', {
					            title: '详细信息',
					            closable: true,
					            closeAction: 'destroy',
					            width: contentPanel.getWidth(),
					            minWidth: 350,
					            height: contentPanel.getHeight(),
					            draggable: true,
					            resizable: false,
					            modal: true,
					            loader: {
					            	url : 'flowWinViewer.do', 
					                params: {
					                    iid: cell.scriptId,
					                    flag: 1,
					                    actionType:'exec',
					                    pageFrom: 'GFSSFLOWCUSTOMEDITPRODUCT',
										isShowInWindow: 1,
										parentMxIid: cell.mxIid,
										isStack: true
					                },
					                autoLoad: true,
					                scripts: true
					            }
					        }).show();
						} else {
                            //模板
                            openWindwGFSSFLOWCUSTOMEDITPRODUCT();
                        }
                    }
                }
            }
            mxEvent.consume(evt);
        };

        //节点之间可以连接
        graphGFSSFLOWCUSTOMEDITPRODUCT.setConnectable(true);

        //增加样式
        configureStylesheetGFSSFLOWCUSTOMEDITPRODUCT(graphGFSSFLOWCUSTOMEDITPRODUCT);

        actNameCountMapGFSSFLOWCUSTOMEDITPRODUCT['scriptServiceStyle'] = 0;
        actNameCountMapGFSSFLOWCUSTOMEDITPRODUCT['usertaskStyle'] = 0;

        var spacer = document.createElement('div');
        spacer.style.display = 'inline';
        spacer.style.padding = '8px';

        editorGFSSFLOWCUSTOMEDITPRODUCT.addAction('export',
        function(editor, cell) {
            var textarea = document.createElement('textarea');
            textarea.style.width = '400px';
            textarea.style.height = '400px';
            var enc = new mxCodec(mxUtils.createXmlDocument());
            var node = enc.encode(editor.graph.getModel());
            textarea.value = mxUtils.getPrettyXml(node);
            showModalWindowGFSSFLOWCUSTOMEDITPRODUCT(graphGFSSFLOWCUSTOMEDITPRODUCT, 'XML', textarea, 410, 440);
        });

        editorGFSSFLOWCUSTOMEDITPRODUCT.addAction('deleteBefore',
        function(editor, cell) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if (cells[i].style == 'beginStyle') {
                    Ext.Msg.alert('提示', '不能删除<开始>节点！');
                    return false;
                }
                if (cells[i].style == 'endStyle') {
                    Ext.Msg.alert('提示', '不能删除<结束>节点！');
                    return false;
                }
            }
            editor.execute('delete');
        });

        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCT(editorGFSSFLOWCUSTOMEDITPRODUCT, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCT(editorGFSSFLOWCUSTOMEDITPRODUCT, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCT(editorGFSSFLOWCUSTOMEDITPRODUCT, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCT(editorGFSSFLOWCUSTOMEDITPRODUCT, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);

        var outln = new mxOutline(graphGFSSFLOWCUSTOMEDITPRODUCT, outline);

        var splash = document.getElementById('splashGFSSFLOWCUSTOMEDITPRODUCT');
        if (splash != null) {
            try {
                mxEvent.release(splash);
                mxEffects.fadeOut(splash, 100, true);
            } catch(e) {

                splash.parentNode.removeChild(splash);
            }
        }

        graphGFSSFLOWCUSTOMEDITPRODUCT.popupMenuHandler.factoryMethod = function(menu, cell, evt) {
            return createPopupMenuGFSSFLOWCUSTOMEDITPRODUCT(graphGFSSFLOWCUSTOMEDITPRODUCT, menu, cell, evt);
        };

        initFunGFSSFLOWCUSTOMEDITPRODUCT(graphGFSSFLOWCUSTOMEDITPRODUCT);
    }

};

/**增加右键删除菜单**/
function createPopupMenuGFSSFLOWCUSTOMEDITPRODUCT(graph, menu, cell, evt) {
    if (cell != null) {
        menu.addItem('删除', 'images/delete.png',
        function() {
            editorGFSSFLOWCUSTOMEDITPRODUCT.execute('deleteBefore');
        });
    }
};

/**
 * 初始化方法
 */
function initFunGFSSFLOWCUSTOMEDITPRODUCT(graph) {
    if (parent.iidGFSSFLOWCUSTOMEDITPRODUCT > 0) {
        loadGraphGFSSFLOWCUSTOMEDITPRODUCT(graph);
        Ext.Ajax.request({
            url: 'getFlowCustomTemplateData.do',
            method: 'POST',
            params: {
            	iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCT,
                flag: parent.flagGFSSFLOWCUSTOMEDITPRODUCT
            },
            success: function(response, options) {
                var dataS = Ext.decode(response.responseText).data;
                actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT'] = JSON.parse(dataS);
                
                var root2FlowWindow = modelGFSSFLOWCUSTOMEDITPRODUCT.getRoot ();
        		var count = modelGFSSFLOWCUSTOMEDITPRODUCT.getChildCount (root2FlowWindow);
        		for (var i = 0; i < count; i++)
        		{
        			var cells = root2FlowWindow.getChildAt (i);
        			var counts = cells.getChildCount ();
        			for (var j = 0; j < counts; j++)
        			{
        				var cellss = cells.getChildAt (j);
        				if (!modelGFSSFLOWCUSTOMEDITPRODUCT.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
        				{
        					cellss.mxIid = parent.iidGFSSFLOWCUSTOMEDITPRODUCT+":"+cellss.id;
        					if(!actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT'].hasOwnProperty(cellss.mxIid)) {
        						if(parseInt(cellss.phaseId)==0) {
        							actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT'][cellss.mxIid] = {
        									'actNo': cellss.id,
        									'actType': parseInt(cellss.phaseId),
        									'actName': cellss.value,
        									'isShutdown': cellss.isShutdown
        							};
        						} else if(parseInt(cellss.phaseId)==1) {
        							actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT'][cellss.mxIid] = {
        									'actNo': cellss.id,
        									'actType': parseInt(cellss.phaseId),
        									'actName': cellss.value,
        									'message': cellss.ireminfo
        							};
        						}
        					}
        				}
        			}
        		}
            },
            failure: function(result, request) {
                Ext.MessageBox.show({
                    title: "提示",
                    msg: "启动失败",
                    buttonText: {
                        yes: '确定'
                    },
                    buttons: Ext.Msg.YES
                });
            }

        });
    } else {
        addBeginEndCellGFSSFLOWCUSTOMEDITPRODUCT(graph, "开始", "beginStyle", 1, 1);
        addBeginEndCellGFSSFLOWCUSTOMEDITPRODUCT(graph, "结束", "endStyle", 150, 150);
    }

};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCT(editor, toolbar, action, label, image, isTransparent) {
    if (image != null) {
        var img = document.createElement('img');
        img.setAttribute('src', image);
        img.style.width = '74px';
        img.style.height = '30px';
        img.style.verticalAlign = 'middle';
        img.title = label;
        img.style.marginRight = '10px';
        img.style.marginTop = '2px';
    }
    mxEvent.addListener(img, 'click',
    function(evt) {
        if ('delete' == action) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if ('开始' == cells[i].value) {
                    Ext.Msg.alert('提示', '不删除<开始>节点！');
                    return false;
                }
            }
        }

        editor.execute(action);
    });
    toolbar.appendChild(img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSFLOWCUSTOMEDITPRODUCT(graph, title, content, width, height) {
    var background = document.createElement('div');
    background.style.position = 'absolute';
    background.style.left = '0px';
    background.style.top = '0px';
    background.style.right = '0px';
    background.style.bottom = '0px';
    background.style.background = 'black';
    mxUtils.setOpacity(background, 50);
    document.body.appendChild(background);

    if (mxClient.IS_IE) {
        new mxDivResizer(background);
    }

    var x = Math.max(0, document.body.scrollWidth / 2 - width / 2);
    var y = Math.max(10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
    var wnd = new mxWindow(title, content, x, y, width, height, false, true);
    wnd.setClosable(true);

    // Fades the background out after after the window has been closed
    wnd.addListener(mxEvent.DESTROY,
    function(evt) {
        graph.setEnabled(true);
        mxEffects.fadeOut(background, 50, true, 10, 30, true);
    });

    graph.setEnabled(false);
    graph.tooltipHandler.hide();
    wnd.setVisible(true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSFLOWCUSTOMEDITPRODUCT(graph, label, styleName, w, h) {

    var parent = graph.getDefaultParent();
    var model = graph.getModel();

    var v1 = null;

    model.beginUpdate();
    try {
        v1 = graph.insertVertex(parent, null, label, w, h, 50, 25, styleName);
        v1.setConnectable(true);
    } finally {
        model.endUpdate();
    }

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSFLOWCUSTOMEDITPRODUCT(graph, sidebar, label, image, styleName, phaseId) {
    var funct = function(graph, evt, cell, x, y) {
        var parent = graph.getDefaultParent();
        var model = graph.getModel();

        var v1 = null;

        model.beginUpdate();
        try {
            actNameCountMapGFSSFLOWCUSTOMEDITPRODUCT[styleName] = ++actNameCountMapGFSSFLOWCUSTOMEDITPRODUCT[styleName];
            v1 = graph.insertVertex(parent, null, label + actNameCountMapGFSSFLOWCUSTOMEDITPRODUCT[styleName], x, y, 90, 32, styleName);
            v1.setConnectable(true);
            v1.phaseId = phaseId;

        } finally {
            model.endUpdate();
        }
        graph.setSelectionCell(v1);
    }

    var img = document.createElement('img');
    img.setAttribute('src', image);
    img.style.width = '80px';
    img.style.height = '32px';
    img.title = label;
    img.style.marginLeft = '10px';
    img.style.marginBottom = '15px';

    sidebar.appendChild(img);

    var dragElt = document.createElement('div');
    dragElt.style.border = 'dashed black 1px';
    dragElt.style.width = '120px';
    dragElt.style.height = '120px';

    var ds = mxUtils.makeDraggable(img, graph, funct, dragElt, 0, 0, true, true);
    ds.setGuidesEnabled(true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSFLOWCUSTOMEDITPRODUCT(graph) {

    var style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
    style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
    style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTSTYLE] = 0;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
    style[mxConstants.STYLE_RESIZABLE] = '0'; //不可缩放
    graph.getStylesheet().putDefaultVertexStyle(style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('beginStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('endStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
    graph.getStylesheet().putCellStyle('cmdStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
    style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
    style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_STARTSIZE] = '30';
    style[mxConstants.STYLE_FONTSIZE] = '16';
    style[mxConstants.STYLE_FONTSTYLE] = 1;
    graph.getStylesheet().putCellStyle('group', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_FONTCOLOR] = '#774400';
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_FONTSIZE] = '10';
    style[mxConstants.STYLE_FONTSTYLE] = 2;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
    graph.getStylesheet().putCellStyle('port', style);

    style = graph.getStylesheet().getDefaultEdgeStyle();
    style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_STROKEWIDTH] = '1';
    style[mxConstants.STYLE_STROKECOLOR] = '#595758';
    style[mxConstants.STYLE_ROUNDED] = false;
    style[mxConstants.STYLE_EDGE] = mxConstants.EDGESTYLE_ELBOW;

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('scriptServiceStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('usertaskStyle', style);
    
    style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSFLOWCUSTOMEDITPRODUCT() {
    graphGFSSFLOWCUSTOMEDITPRODUCT.getModel().beginUpdate();
    try {
        var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag=" + flagGFSSFLOWCUSTOMEDITPRODUCT + "&instanceID=" + parent.iidGFSSFLOWCUSTOMEDITPRODUCT));
        var dec = new mxCodec(doc);
        dec.decode(doc.getDocumentElement(), graphGFSSFLOWCUSTOMEDITPRODUCT.getModel());
    } finally {
        graphGFSSFLOWCUSTOMEDITPRODUCT.getModel().endUpdate();
    }
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSFLOWCUSTOMEDITPRODUCT() {
    configwindowFlowGFSSFLOWCUSTOMEDITPRODUCT = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/mxgraph/flowCustomizedWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSFLOWCUSTOMEDITPRODUCT() {
    configwindowFlowGFSSFLOWCUSTOMEDITPRODUCT = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: false,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITPRODUCT/flowCustomizedExecScriptWindow.jsp",
            params: {
                flag: flagGFSSFLOWCUSTOMEDITPRODUCT
            },
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSFLOWCUSTOMEDITPRODUCT() {
    configwindowFlowGFSSFLOWCUSTOMEDITPRODUCT = Ext.create('Ext.window.Window', {
        title: '人工提醒配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: 580,
        height: 215,
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITPRODUCT/scriptServiceUTWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSFLOWCUSTOMEDITPRODUCT() {
    //更新名后续刷新才能显示
    graphGFSSFLOWCUSTOMEDITPRODUCT.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSFLOWCUSTOMEDITPRODUCT() {
    mainGFSSFLOWCUSTOMEDITPRODUCT(document.getElementById('graphContainerGFSSFLOWCUSTOMEDITPRODUCT'), document.getElementById('outlineContainerGFSSFLOWCUSTOMEDITPRODUCT'), document.getElementById('toolbarContainer'), document.getElementById('sidebarContainerGFSSFLOWCUSTOMEDITPRODUCT'), document.getElementById('statusContainerGFSSFLOWCUSTOMEDITPRODUCT'));
}

function isJobConfigOk (serviceId, allStartParams) {
	var isJobConfigOk = true;
	Ext.Ajax.request({
		url :'checkJobConfigIsOK.do',
		method: 'POST',
		async: false,
		params:{
			serviceId: serviceId,
			data:JSON.stringify(allStartParams),
			flag: 0
		},
		success: function ( response, options) 
		{
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (!success) {
				isJobConfigOk = false;
				Ext.Msg.alert('提示', message);
			}
		},
		failure: function ( result, request){
			isJobConfigOk = false;
			Ext.Msg.alert('提示', "检查作业配置出现问题！");
		}
	});
	return isJobConfigOk;
}

function saveCustomTemplateGFSSFLOWCUSTOMEDITPRODUCT() {
	var allStartParams = actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT'];
	if(!isJobConfigOk(iidGFSSFLOWCUSTOMEDITPRODUCT, allStartParams)) {
		return;
	}
	
    var customName = parent.customNameObjOutSideGFSSFLOWCUSTOMEDITPRODUCT.getValue();
    if (customName.trim() == '') {
		Ext.Msg.alert ('提示', '请填写模板名称!');
		return null;
	}

    Ext.Ajax.request({
        url: 'checkCustomTemplateNameIsExist.do',
        params: {
            iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCT,
        	customName: customName,
            flag: flagGFSSFLOWCUSTOMEDITPRODUCT
        },
        method: 'POST',
        success: function(response, options) {
            if (!Ext.decode(response.responseText).success) {
                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
            } else {
                var serviceId = parent.iidGFSSFLOWCUSTOMEDITPRODUCT;
                Ext.Ajax.request({
                    url: 'saveFlowCustomTemplate.do',
                    method: 'POST',
                    params: {
                    	iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCT,
                    	customName: customName,
                    	serviceId: serviceId,
                        data: JSON.stringify(allStartParams),
                        flag: parent.flagGFSSFLOWCUSTOMEDITPRODUCT
                    },
                    success: function(response, options) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                        	Ext.Msg.alert('提示', "模板保存成功！");
                        } else {
                        	Ext.Msg.alert ('提示', "模板保存失败！");
                        }
                    },
                    failure: function(result, request) {
                    	Ext.Msg.alert('提示', "模板保存出现问题！");
                    }
                });
            }
        },
        failure: function(result, request) {
        	Ext.Msg.alert('提示', "检查模板名称是否存在失败！");
        }
    });
}

function checkDataIsReadyGFSSFLOWCUSTOMEDITPRODUCT () {
	return isJobConfigOk(iidGFSSFLOWCUSTOMEDITPRODUCT, actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT']);
}

function orgStartDataGFSSFLOWCUSTOMEDITPRODUCT () {
	return actStartInfo['GFSSFLOWCUSTOMEDITPRODUCT'];
}
