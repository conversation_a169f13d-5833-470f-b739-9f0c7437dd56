/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/
Ext.onReady (function ()
{
	var scriptNameObj;
	var scriptContentObj;
	var stepNameObj;
	var paramObj;
	var startUserObj;
	var resourceGroupObj;
	var chosedServList;
	var agent_store;
	var cellScriptType = '';
	var chosedResGroups = new Array();
	var finalChosedAgents = new Array();
	var finalChosedAgentsAndParams = {};
	var editingChosedAgentsAndParams = {};

	var editingGlobalParams = {};
	var finalGlobalParams = {};

	var editingConfigParams = {};
	var finalConfigParams = {};

	var finalChosedAgentsAndStartUsers = {};
	var editingChosedAgentsAndStartUsers = {};

	var finalChosedAgentsAndDbSources = {};
	var editingChosedAgentsAndDbSources = {};
	var chosedAgentWin;
	var agentParamsWin;
	
	stepNameObj = Ext.create ('Ext.form.field.Text', {
	    fieldLabel : '步骤名称',
	    readOnly: true
	});
	paramObj = Ext.create ('Ext.form.field.Text', {
		fieldLabel : '执行参数',
		hidden : true,
		readOnly: true
	});
	startUserObj = Ext.create('Ext.form.field.Text', {
		fieldLabel : '启动用户',
		readOnly: parent.actionTypeGFSSDBCHECK=='dbcheckForExec'
	});
	scriptNameObj = Ext.create('Ext.form.field.Text', {
		fieldLabel : '脚本服务名称',
		readOnly: true,
		width: contentPanel.getWidth ()-140
	});
	
	scriptContentObj = Ext.create ('Ext.form.field.TextArea', {
		fieldLabel : '脚本内容',
		height: 300,
		readOnly:true,
//	    padding : '5 5 5 5',
	    autoScroll : true
	});
	
	var prevButton = Ext.create('Ext.Button', {
	    text : '上一步',
	    margin : '0 0 0 60',
	    textAlign : 'center',
//	    cls : 'Common_Btn',
	    handler : function() {
	    	var res = getCellFun("before");
	    	if(res) {
	    		parent.cellObjGFSSDBCHECK = res;
	    		initFun ();
	    		resourceGroupStore.load();
//	    		agent_store.load();
	    		
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为第一个步骤");
	    	}
	      }
	  });
			
	var nextButton = Ext.create('Ext.Button', {
		text : '下一步',
		margin : '0 0 0 5',
		textAlign : 'center',
//	    cls : 'Common_Btn',
		handler : function() {
			var res = getCellFun("after");
	    	if(res) {
	    		parent.cellObjGFSSDBCHECK = res;
	    		initFun ();
	    		resourceGroupStore.load();
//	    		agent_store.load();
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为最后一个步骤");
	    	}
		}
	});
	
	var viewChosedScriptButton = Ext.create('Ext.Button', {
		text : '查看脚本详情',
		margin : '0 0 0 10',
		textAlign : 'center',
		handler : function() {
			if (parent.cellObjGFSSDBCHECK.scriptId!=null&&typeof (parent.cellObjGFSSDBCHECK.scriptId) != "undefined") {
				Ext.create('widget.window', {
		            title: '详细信息',
		            closable: true,
		            closeAction: 'destroy',
		            width: contentPanel.getWidth(),
		            minWidth: 350,
		            height: contentPanel.getHeight(),
		            draggable: false,
		            // 禁止拖动
		            resizable: false,
		            // 禁止缩放
		            modal: true,
		            loader: {
		                url: 'queryOneServiceForView.do',
		                params: {
		                    iid: parent.cellObjGFSSDBCHECK.scriptId,
		                    flag: 0,
		                    hideReturnBtn: 1
		                },
		                autoLoad: true,
		                scripts: true
		            }
		        }).show();
			} else {
				Ext.Msg.alert('提示',"没有脚本服务！");
			}
		}
	});
	
			
	var formPanel=Ext.create ('Ext.form.Panel',
	{
		border : false,
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        width : contentPanel.getWidth ()-30,
	        labelWidth : 90
	    },
	    buttonAlign : 'center',
	    items : [
	    	stepNameObj,{
				layout : 'column',
				anchor : '100%',
				height : 35,
				border : false,
				items : [scriptNameObj, viewChosedScriptButton]
			}, paramObj,startUserObj //,stageNameObj, templateNameObj, templateParamObj, resourceGroupObj, serverListObj
	    ],
	    buttons : [{
            text : '保存',
            hidden: parent.actionTypeGFSSDBCHECK=='dbcheckForExec',
            handler : saveFun
        }, {
            text : '关闭',
            handler : function () {
            	parent.cellObjGFSSDBCHECK = null;
            	parent.configwindowFlowGFSSDBCHECK.close ();
            }
        }, prevButton, nextButton]
	});
	
	/** 树数据Model* */
	Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
        	name: 'paramValue',
        	type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });
	
	/** 树数据源* */
	var paramStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getAllScriptParams.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	paramStore.on ('beforeload', function (store, options) {
		var new_params = {
				scriptId: parent.cellObjGFSSDBCHECK.scriptId
		};
		Ext.apply (paramStore.proxy.extraParams, new_params);
	});
	
	var configParamStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getAllScriptParams.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	configParamStore.on ('beforeload', function (store, options) {
		var new_params = {
				scriptId: parent.cellObjGFSSDBCHECK.scriptId
		};
		Ext.apply (configParamStore.proxy.extraParams, new_params);
	});
	/*configParamStore.addListener('load',function(){
		
		for(var prop in editingConfigParams){
		    if(editingConfigParams.hasOwnProperty(prop)){
		        configParamStore.findRecord('iid', prop+"").set('paramValue', editingConfigParams[prop]);
		    }
		}
      });*/
	/** 树列表columns* */
	var paramColumns = [
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 100,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '默认参数值',
        dataIndex: 'paramDefaultValue',
        width: 100,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
    	text: '参数值',
    	dataIndex: 'paramValue',
    	width: 100,
    	editor: {
    		allowBlank: true
    	},
    	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
    		metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
    		return value;  
    	}
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing_for_config_params = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
	/** 树列表panel* */
	var paramGrid = Ext.create ('Ext.grid.Panel',
	{
		width: '39%',
		height: contentPanel.getHeight() - 250,
	    store : paramStore,
	    margin: '0 0 0 10',
	    title: '参数信息',
	    border : true,
	    columnLines : true,
	    columns : paramColumns,
	    plugins: [cellEditing],
	    collapsible : false
	});
	var paramGrid_for_config_params = Ext.create ('Ext.grid.Panel',
	{
		width: '50%',
		height: contentPanel.getHeight() - 250,
	    store : configParamStore,
	    margin: '0 0 0 10',
	    title: '参数信息',
	    border : true,
	    columnLines : true,
	    columns : paramColumns,
	    plugins: [cellEditing_for_config_params],
	    collapsible : false
	});
	
	paramGrid_for_config_params.on('edit', function(editor, e) {
		var ipRecord = agent_grid_chosed_for_config_param.getSelectionModel().getSelection()[0];
		if(ipRecord) {
			var configParams = {};
			for (var i = 0; i <  e.grid.getStore().getCount(); i++) {
				var record =  e.grid.getStore().getAt(i);
				var iid = record.get('iid');
				var paramType = record.get('paramType');
				var paramValue = record.get('paramValue');
				
				if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramValue) {
	                if (!checkIsInteger(paramValue)) {
	                	Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
	                    return;
	                }
	            }
	            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramValue) {
	                if (!checkIsDouble(paramValue)) {
	                	Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
	                    return;
	                }
	            }
	            if (paramValue.indexOf('"')>=0) {
	            	if(cellScriptType=='bat') {
	            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
	                    return;
	            	}
	            }
	            
				configParams[iid] = paramValue;
			}
			editingConfigParams[ipRecord.get('iid')] = configParams;//绑定
		}
	});
	
	
	Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	resourceGroupObj=Ext.create ('Ext.form.field.ComboBox', {
	    fieldLabel : '资源组',
	    multiSelect: true,
	    labelWidth : 58,
	    store : resourceGroupStore,
	    displayField : 'name',
	    valueField : 'id',
	    triggerAction : 'all',
	    editable : false,
	    mode : 'local',
    	listeners: {
			select: function( combo, records, eOpts ) {
				if(records) {
					chosedResGroups = new Array();
					for(var i=0;i<records.length;i++) {
						chosedResGroups.push(records[i].data.id);
					}
					agent_store.load();
				} else {
					agent_store.load();
				}
				
			}
    	}
	});
	
	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string', defaultValue: 1500},
            {name: 'agentDesc',     type: 'string'},
            {name: 'resGroup',     type: 'string'},
            {name: 'agentState',     type: 'int'},
            {name: 'agentParam',     type: 'string'},
            {name: 'agentStartUser',     type: 'string'}
        ]
    });
	agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
//        pageSize: 50,
        model: 'agentModel',
        groupField:'resGroup', //确定哪一项分组  
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	
/*	var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });*/
	
	agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
    		flag:flagGFSSDBCHECK,
    		hasPage: false,
    		rgIds:chosedResGroups
	    };
	    
	    if(parent.actionTypeGFSSDBCHECK=='dbcheckForExec') {
	    	new_params['agentIds'] = finalChosedAgents;
	    }
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
		  
	agent_store.addListener('load',function(){
		
		for(var prop in editingChosedAgentsAndParams){
		    if(editingChosedAgentsAndParams.hasOwnProperty(prop)){
		        agent_store.findRecord('iid', prop+"").set('agentParam', editingChosedAgentsAndParams[prop]);
		    }
		}
		for(var prop in editingChosedAgentsAndStartUsers){
			if(editingChosedAgentsAndStartUsers.hasOwnProperty(prop)){
				agent_store.findRecord('iid', prop+"").set('agentStartUser', editingChosedAgentsAndStartUsers[prop]);
			}
		}
		
		  var chosedAgents=finalChosedAgents;//parent.cellObjGFSSDBCHECK.chosedAgents;
		  if(chosedAgents) {
			  var records=[];//存放选中记录
			  for(var i=0;i<agent_store.getCount();i++){
			      var record = agent_store.getAt(i);
			      for (ii=0;ii<chosedAgents.length;ii++ )   
		    	    {   
			    	  
			    	  if(chosedAgents[ii]==record.data.iid)
			    		  {
			    		  records.push(record);
			    		  }
		    	    }   
			  }
			  chosedServList.getSelectionModel().select(records, false, true);//选中记录
		  }
      });
	  
	if(parent.actionTypeGFSSDBCHECK=='dbcheckForExec') {
		var selModel = null;
	} else {
		var selModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true
		});
	}
	  
	  var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 2
		});
	  
	  var agent_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: false,
			model: 'agentModel',
			pageSize: 50,
			proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
	  
	  agent_store_chosed.on('beforeload', function (store, options) {
	    	var new_params = {  
	    			agentIds : JSON.stringify(chosedAgentIds)
	    	};
	    	
	    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
	    });
	  agent_store_chosed.on('load', function () {
		  agent_grid_chosed_for_config_param.getSelectionModel().select(0);
	  });
	  
	  
	  var pageBarForAgentChosedToSetParamsGrid = Ext.create('Ext.PagingToolbar', {
	    	store : agent_store_chosed,
	    	dock : 'bottom',
	    	displayInfo : true
	    });
	 
	  var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
		  title: '已选服务器列表',
	    	store:agent_store_chosed,
	    	border:true,
	    	columnLines : true,
	    	width: '49%',
	    	height : contentPanel.getHeight () -150,
	    	autoScroll : true,
	    	emptyText: '没有选择服务器',
	    	columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
                { text: '主键',  dataIndex: 'iid',hidden:true},
                { text: 'IP',  dataIndex: 'agentIp',width:120},
                { text: '端口号',  dataIndex: 'agentPort',width:100},
	            { text: '描述',  dataIndex: 'agentDesc',flex:1},
	            { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
	            	var backValue = "";
	            	if(value==0){
	            		backValue = "Agent正常";
	            	}else if(value==1){
	            		backValue = "Agent异常";
	            	}
	            	return backValue;
	            }}
           ],
			listeners : {
       			select:function(self, record, index, eOpts) {
       				 dbinfo_store.load({
   			            params: {
   			            	agentId: record.get("iid"),
   			                agentIp: record.get("agentIp"),
   			                agentPort: record.get("agentPort")
   			            },
   			            callback: function(records, operation, success) {
   			            	if(editingChosedAgentsAndDbSources.hasOwnProperty(record.get("iid"))) {
   			            		$.each(records, function(index, val){
   			            			if(val.get('iid')==editingChosedAgentsAndDbSources[record.get("iid")]) {
   			            				selModel2.select(val,false, true);
   			            			}
   			            		});
	   			 			}
   			            }
   			        });
       			 }
       		}
	    });
	    
	    var agent_grid_chosed_for_config_param = Ext.create('Ext.grid.Panel', {
		  title: '已选服务器列表',
	    	store:agent_store_chosed,
	    	border:true,
	    	columnLines : true,
	    	bbar: pageBarForAgentChosedToSetParamsGrid,
	    	width: '49%',
	    	height : contentPanel.getHeight () -150,
	    	autoScroll : true,
	    	emptyText: '没有选择服务器',
	    	columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
                { text: '主键',  dataIndex: 'iid',hidden:true},
                { text: 'IP',  dataIndex: 'agentIp',width:120},
                { text: '端口号',  dataIndex: 'agentPort',width:100},
	            { text: '描述',  dataIndex: 'agentDesc',flex:1},
	            { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
	            	var backValue = "";
	            	if(value==0){
	            		backValue = "Agent正常";
	            	}else if(value==1){
	            		backValue = "Agent异常";
	            	}
	            	return backValue;
	            }}
           ],
			listeners : {
       			select:function(self, record, index, eOpts) {
       				var ipId = record.get('iid');
       				if(editingConfigParams.hasOwnProperty(ipId)){
       					var cp = editingConfigParams[ipId];
       					configParamStore.each(function(record){
       						record.set('paramValue', cp[record.get('iid')])
       					});
       				} else {
       					configParamStore.each(function(record){
       						record.set('paramValue', '')
       					});
       				}
//       				for(var prop in editingConfigParams){
//					    if(editingConfigParams.hasOwnProperty(prop)){
//					        configParamStore.findRecord('iid', prop+"").set('paramValue', editingConfigParams[prop]);
//					    }
//					}
//       				configParamStore.load();
//       				 configParamStore.load({});
       			 }
       		}
	    });
	  
	  Ext.define('dbModel', {
	        extend: 'Ext.data.Model',
	        idProperty: 'iid',
	        fields: [
	            {name: 'iid',        type: 'string'},
	            {name: 'dsName',        type: 'string'},
	            {name: 'driverClass',type: 'string'},
	            {name: 'dbUrl',      type: 'string'},
	            {name: 'dbUser',     type: 'string'},
	            {name: 'dbType',     type: 'string'}
	        ]
	    });
	  
	  var dbinfo_store = Ext.create('Ext.data.Store', {
	    	model:'dbModel',
	    	autoLoad: false,
	    	proxy: {
	    		type: 'ajax',
	    		url: 'getDbSqlDriverInfo.do',
	    		reader: {
	    			type: 'json',
	    			root: 'dataList'
	    		}
	    	}
	    });
	  
	  var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
          { text: '主键',  dataIndex: 'iid',hidden:true},
          { text: '数据源名称',  dataIndex: 'dsName',width:100,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
              metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
              return value;  
          }},
          { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
        	  metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
        	  return value;  
          }},
          { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
              metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
              return value;  
          }},
          { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
          { text: 'DB类型',  dataIndex: 'dbType',width:110}];
	  
	  var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			mode : "SINGLE",
			listeners : {
				select:function(self, record, index, eOpts) {
					if(parent.actionTypeGFSSDBCHECK=='dbcheckForExec') {
						
					} else {
						var ipRecord = agent_grid_chosed.getSelectionModel().getSelection()[0];
						var dsid = record.get("iid");
						editingChosedAgentsAndDbSources[ipRecord.get('iid')] = dsid;//绑定
					}
				},
				deselect:function(self, record, index, eOpts) {
					if(parent.actionTypeGFSSDBCHECK=='dbcheckForExec') {
						
					} else {
						var ipRecord = agent_grid_chosed.getSelectionModel().getSelection()[0];
						delete editingChosedAgentsAndDbSources[ipRecord.get('iid')];  
					}
				}
			}
		});
	  
	  var dbsource_grid = Ext.create('Ext.grid.Panel', {
		  title: parent.actionTypeGFSSDBCHECK=='dbcheckForExec'?'已选数据源':'选择数据源',
	    	store:dbinfo_store,
	    	 width: '50%',
	    	 height : contentPanel.getHeight ()-150,
	    	 autoScroll : true,
	    	 margin: '0 0 0 10',
	    	border:true,
	    	columnLines : true,
	    	columns:dbsource_columns,
	    	selModel:selModel2
	    });
	  
	  var choseAgentWrapper = Ext.create('Ext.panel.Panel',{
	        border : false,
	        height : contentPanel.getHeight ()-100,
	        layout: {
	            type: 'hbox',
	            padding:'5',
	            align:'stretch'
	        },
	        items : [agent_grid_chosed, dbsource_grid]
		});
		
		var choseAgentWrapper_for_config_params = Ext.create('Ext.panel.Panel',{
	        border : false,
	        height : contentPanel.getHeight ()-100,
	        layout: {
	            type: 'hbox',
	            padding:'5',
	            align:'stretch'
	        },
	        items : [agent_grid_chosed_for_config_param,paramGrid_for_config_params]
		});
	  
	var choseDbSourceButton = Ext.create('Ext.Button', {
		text : parent.actionTypeGFSSDBCHECK=='dbcheckForExec'?'已选数据源':'选择数据源',
		cls : 'Common_Btn',
		hidden: true,
		handler : function() {
			if(!chosedAgentWin) {
				chosedAgentWin = Ext.create('Ext.window.Window', {
			  		title : '配置数据源',
			  		autoScroll : true,
			  		modal : true,
			  		resizable : false,
			  		closeAction : 'hide',
			  		width : contentPanel.getWidth(),
			  		height : contentPanel.getHeight (),
			  		items:[choseAgentWrapper],
			  		buttonAlign: 'center',
			  		buttons: [{ 
			  			xtype: "button", 
			  			text: "确定", 
			  			handler: function () {
			  				this.up("window").close();
			  			}
			  		}]
			  	});
			}
			chosedAgentWin.show();
			dbinfo_store.removeAll();
			agent_store_chosed.removeAll();
			agent_store_chosed.loadRecords(chosedServList.getSelectionModel().getSelection());
			agent_grid_chosed.getSelectionModel().select(0);
		}
	});
	
		var parmsButton = Ext.create('Ext.Button', {
		text : '配置参数',
		cls : 'Common_Btn',
		//hidden: true,
		handler : function() {
			if(!agentParamsWin) {
				agentParamsWin = Ext.create('Ext.window.Window', {
			  		title : '配置参数',
			  		autoScroll : true,
			  		modal : true,
			  		resizable : false,
			  		closeAction : 'hide',
			  		width : contentPanel.getWidth(),
			  		height : contentPanel.getHeight (),
			  		items:[choseAgentWrapper_for_config_params],
			  		buttonAlign: 'center',
			  		buttons: [{ 
			  			xtype: "button", 
			  			text: "确定", 
			  			handler: function () {
			  				//
			  				
			  				this.up("window").close();
			  			}
			  		}]
			  	});
			}
			agentParamsWin.show();
//			agent_store_chosed.removeAll();
			pageBarForAgentChosedToSetParamsGrid.moveFirst();

		}
	});

	 chosedServList = Ext.create('Ext.grid.Panel', {
		 title: parent.actionTypeGFSSDBCHECK=='dbcheckForExec'?'已选服务器':'选择服务器', 
			width: '60%',
			height: contentPanel.getHeight()-250,
			autoScroll : true,
		    multiSelect: true,
		    split : true,
		    columnLines : true,
		    store : agent_store,
		    dockedItems : [{
				xtype : 'toolbar',
				dock : 'top',
				padding:'5',
				items : [ resourceGroupObj, '->', choseDbSourceButton,parmsButton]
			}],
		    selModel: selModel,
		    plugins : [ cellEditing ],
		    listeners: {
		    	select: function(t, record, index, eOpts) {
		    		if(!finalChosedAgents.contains(record.get('iid'))) {
		    			finalChosedAgents.push(record.get('iid'));
		    		}
		    	},
				 deselect: function(t, record, index, eOpts) {
					 finalChosedAgents.remove(record.get('iid'));
				 }
		    },
		    columns : [{ text: '序号', xtype:'rownumberer', width: 40 },
                       { text: '主键',  dataIndex: 'iid',hidden:true},
                       { text: 'IP',  dataIndex: 'agentIp',width:120},
                       { text: '端口号',  dataIndex: 'agentPort',width:100},
                       { text: '资源组',  dataIndex: 'resGroup',width:100,hidden:true},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1},
		                {
 							dataIndex : 'agentParam',
 							text : '参数',
 							hidden: true,
 							editor : {
 								allowBlank : true,
 								listeners: {
 									blur: function(me, The, eOpts){
 							            var plugin = chosedServList.findPlugin('cellediting');
 										editingChosedAgentsAndParams[plugin.context.record.get('iid')] = me.getValue();
 									}
 								}
 							},
 							flex : true
 						},
 						{
 							dataIndex : 'agentStartUser',
 							text : '启动用户',
 							editor : {
 								allowBlank : true,
 								listeners: {
 									blur: function(me, The, eOpts){
 										var plugin = chosedServList.findPlugin('cellediting');
 										editingChosedAgentsAndStartUsers[plugin.context.record.get('iid')] = me.getValue();
 									}
 								}
 							},
 							flex : true
 						},
		                { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ]
		  });
	
	var scriptContentformPanel = Ext.create ('Ext.form.Panel',
	{
		border : false,
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        width : contentPanel.getWidth () /2 - 20,
	        labelWidth : 90
	    },
	    width: '100%',
	    items : [scriptContentObj]
	});
	var scriptDetailPanel = Ext.create('Ext.panel.Panel',{
//        width : contentPanel.getWidth(),
        border : false,
        layout: {
            type: 'hbox',
            padding:'5',
            align:'stretch'
        },
        items : [chosedServList, paramGrid]
	});
	
    // 主Panel
    var MainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "flowCustomizedScriptWindowDivGFSSDBCHECK",
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight ()-35,
		autoScroll: true,
		border : false,
		bodyPadding : 5,
		items : [ formPanel,scriptDetailPanel/*,scriptContentformPanel*/]
	});
	/** 初始化方法* */
	function initFun ()
	{
		if (parent.cellObjGFSSDBCHECK.scriptParam!=null&&typeof (parent.cellObjGFSSDBCHECK.scriptParam) != "undefined") {
			paramObj.setValue(parent.cellObjGFSSDBCHECK.scriptParam);
		} else {
			paramObj.setValue('');
		}
		stepNameObj.setValue(parent.cellObjGFSSDBCHECK.value);
		startUserObj.setValue(parent.cellObjGFSSDBCHECK.startUser);
		
		if(parent.cellObjGFSSDBCHECK.chosedAgents) {
			finalChosedAgents = parent.cellObjGFSSDBCHECK.chosedAgents;
		} else {
			finalChosedAgents = new Array();
		}
		
		if(parent.cellObjGFSSDBCHECK.chosedResGroups) {
			chosedResGroups = parent.cellObjGFSSDBCHECK.chosedResGroups;
		} else {
			chosedResGroups = new Array();
		}
		
		if(parent.cellObjGFSSDBCHECK.chosedResGroups) {
			resourceGroupObj.setValue(parent.cellObjGFSSDBCHECK.chosedResGroups);
		} else {
			resourceGroupObj.setValue('');
		}
		resourceGroupObj.fireEvent ('select');
		
		if(parent.cellObjGFSSDBCHECK.chosedAgentsAndParams) {
			finalChosedAgentsAndParams = parent.cellObjGFSSDBCHECK.chosedAgentsAndParams;
			editingChosedAgentsAndParams = parent.cellObjGFSSDBCHECK.chosedAgentsAndParams;
		} else {
			finalChosedAgentsAndParams = {}; 
			editingChosedAgentsAndParams = {}; 
		}
		
		if(parent.cellObjGFSSDBCHECK.editingChosedAgentsAndParams) {
			editingChosedAgentsAndParams = parent.cellObjGFSSDBCHECK.editingChosedAgentsAndParams;
		}
		
		if(parent.cellObjGFSSDBCHECK.chosedAgentsAndStartUsers) {
			finalChosedAgentsAndStartUsers = parent.cellObjGFSSDBCHECK.chosedAgentsAndStartUsers;
			editingChosedAgentsAndStartUsers = parent.cellObjGFSSDBCHECK.chosedAgentsAndStartUsers;
		} else {
			finalChosedAgentsAndStartUsers = {}; 
			editingChosedAgentsAndStartUsers = {}; 
		}
		
		if(parent.cellObjGFSSDBCHECK.chosedAgentsAndDbSources) {
			finalChosedAgentsAndDbSources = parent.cellObjGFSSDBCHECK.chosedAgentsAndDbSources;
			editingChosedAgentsAndDbSources = parent.cellObjGFSSDBCHECK.chosedAgentsAndDbSources;
		} else {
			finalChosedAgentsAndDbSources = {}; 
			editingChosedAgentsAndDbSources = {}; 
		}
		
		if(parent.cellObjGFSSDBCHECK.chosedConfigParams) {
			finalConfigParams = parent.cellObjGFSSDBCHECK.chosedConfigParams;
			editingConfigParams = parent.cellObjGFSSDBCHECK.chosedConfigParams;
		} else {
			finalConfigParams = {}; 
			editingConfigParams = {}; 
		}
		
		if(parent.cellObjGFSSDBCHECK.editingConfigParams) {
			editingConfigParams = parent.cellObjGFSSDBCHECK.editingConfigParams;
		}
		
		if(parent.cellObjGFSSDBCHECK.scriptParam) {
			finalGlobalParams = parent.cellObjGFSSDBCHECK.scriptParam;
			editingGlobalParams = parent.cellObjGFSSDBCHECK.scriptParam;
		} else {
			finalGlobalParams = {}; 
			editingGlobalParams = {}; 
		}
		
		if(parent.cellObjGFSSDBCHECK.editingGlobalParams) {
			editingGlobalParams = parent.cellObjGFSSDBCHECK.editingGlobalParams;
		}
		
		if (parent.cellObjGFSSDBCHECK.scriptId!=null&&typeof (parent.cellObjGFSSDBCHECK.scriptId) != "undefined") {
			paramStore.load({
				callback: function(records, operation, success){
					if ($.isEmptyObject(editingGlobalParams)) {
						if (records.length > 0) {
							var pas = [];
				        	$.each(records, function(index, record) {
				        		var newP = record.get('paramValue');
				        		if(newP==null || typeof newP =="undefined" || Ext.isEmpty(Ext.util.Format.trim(newP))) {
				        			newP = record.get('paramDefaultValue');
				        		}
				        		pas.push(newP);
				        	});
				        	paramObj.setValue(pas.join(" "));
				        }
					} else {
						if (records.length > 0) {
							$.each(records, function(index, record) {
								record.set('paramValue', editingGlobalParams[record.get('iid')]);
						    //paramObj.setValue(sps[record.get('iid')]);
						    });
						}
				  }
				}
			});
			configParamStore.load();
			Ext.Ajax.request ({
			    url : 'getScritContentScriptService.do',
			    method : 'POST',
			    params :
			    {
			        iid : parent.cellObjGFSSDBCHECK.scriptId,
			        from: 2,
			        flag: "0"
			    },
			    success : function (response, options)
			    {
				    var content= Ext.decode (response.responseText).content;
				    var serviceName= Ext.decode (response.responseText).serviceName;
				    cellScriptType = Ext.decode (response.responseText).scriptType;
				    if(cellScriptType=='sql') {
				    	choseDbSourceButton.show();
				    } else {
				    	choseDbSourceButton.hide();
				    }
				    scriptContentObj.setValue(content);
				    scriptNameObj.setValue(serviceName);
			    },
				failure : function(result, request) {
					scriptContentObj.setValue(''); 
					scriptNameObj.setValue("");
				}
			});
		}
	}
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
	function saveFun ()
	{
		if (stepNameObj.getValue ().trim () == '')
		{
			Ext.Msg.alert ('提示', '步骤名称不允许为空!');
			return null;
		}
		if ('开始' == stepNameObj.getValue ().trim ())
		{
			Ext.Msg.alert ('提示', '步骤名称不可以为<开始>！');
			return null;
		}
		if ('结束' == stepNameObj.getValue ().trim ())
		{
			Ext.Msg.alert ('提示', '步骤名称不可以为<结束>！');
			return null;
		}
		
		if(finalChosedAgents.length<1) {
			Ext.MessageBox.alert ("提示", "请选择服务器!");
			return null;
		}
		
		var m = paramStore.getRange(0, paramStore.getCount()-1);   
        for (var i = 0, len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';

            if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                	Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                    return;
                }
            }
            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                	Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"')>=0) {
            	if(cellScriptType=='bat') {
            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
            	}
            }
        }
		
		finalChosedAgentsAndParams = {};
		finalChosedAgentsAndStartUsers = {};
		finalChosedAgentsAndDbSources = {};
		finalConfigParams = {};
		finalGlobalParams = {};
		
		for(var i=0;i<finalChosedAgents.length;i++){
			var para = "";
			if(editingChosedAgentsAndParams.hasOwnProperty(finalChosedAgents[i])) {
				para = editingChosedAgentsAndParams[finalChosedAgents[i]];
			}
			finalChosedAgentsAndParams[finalChosedAgents[i]] = para;
			
			if(editingConfigParams.hasOwnProperty(finalChosedAgents[i])) {
				var configParas = editingConfigParams[finalChosedAgents[i]];
				finalConfigParams[finalChosedAgents[i]] = configParas;
			}
			
			var StartUser = "";
			if(editingChosedAgentsAndStartUsers.hasOwnProperty(finalChosedAgents[i])) {
				StartUser = editingChosedAgentsAndStartUsers[finalChosedAgents[i]];
			}
			finalChosedAgentsAndStartUsers[finalChosedAgents[i]] = StartUser;
			
			if(cellScriptType=='sql') { 
				if(editingChosedAgentsAndDbSources.hasOwnProperty(finalChosedAgents[i])) {
					var dbId = editingChosedAgentsAndDbSources[finalChosedAgents[i]];
					if(dbId>0) {
						finalChosedAgentsAndDbSources[finalChosedAgents[i]] = dbId;
					} else {
						Ext.MessageBox.alert ("提示", "有服务器没有配置数据源!");
						return;
					}
				} else {
					Ext.MessageBox.alert ("提示", "有服务器没有配置数据源!");
					return;
				}
			}
		}
		
		var pas = [];
		paramStore.each(function(record){
			var newP = record.get('paramValue');
    		if(newP==null || typeof newP =="undefined" || Ext.isEmpty(Ext.util.Format.trim(newP))) {
    			newP = record.get('paramDefaultValue');
    			finalGlobalParams[record.get('iid')] = record.get('paramDefaultValue');
    		}else{
    			finalGlobalParams[record.get('iid')] = record.get('paramValue');
    			editingGlobalParams[record.get('iid')] = record.get('paramValue');
    		}
			pas.push(newP);
		});
		paramObj.setValue(pas.join(" "));
		parent.cellObjGFSSDBCHECK.scriptParam=finalGlobalParams;
		parent.cellObjGFSSDBCHECK.startUser=startUserObj.getValue();
		parent.cellObjGFSSDBCHECK.chosedAgents=finalChosedAgents;
		parent.cellObjGFSSDBCHECK.chosedResGroups=chosedResGroups;
		parent.cellObjGFSSDBCHECK.chosedAgentsAndParams=finalChosedAgentsAndParams;
		parent.cellObjGFSSDBCHECK.chosedAgentsAndStartUsers=finalChosedAgentsAndStartUsers;
		parent.cellObjGFSSDBCHECK.chosedAgentsAndDbSources=finalChosedAgentsAndDbSources;
		parent.cellObjGFSSDBCHECK.chosedConfigParams=finalConfigParams;
		
		parent.cellObjGFSSDBCHECK.editingConfigParams=editingConfigParams;
		parent.cellObjGFSSDBCHECK.editingChosedAgentsAndParams=editingChosedAgentsAndParams;
		parent.cellObjGFSSDBCHECK.editingGlobalParams=editingGlobalParams;
		
		parent.cellObjGFSSDBCHECK.value = stepNameObj.getValue();
		parent.callbackWindwGFSSDBCHECK ();
		Ext.Msg.alert ('提示', '当前步骤保存成功!');
//		parent.configwindowFlowGFSSDBCHECK.close ();
	}
	initFun ();
	
	/**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
	function getCellFun (inflag)
	{
		// 遍历所有节点
		var rootObj = modelGFSSDBCHECK.getRoot ();
		var count = modelGFSSDBCHECK.getChildCount (rootObj);
		for (var i = 0; i < count; i++)
		{
			var cells = rootObj.getChildAt (i);
			var counts = cells.getChildCount ();
			var beforeCell = null;// 上一个节点
			var afterCell = null;// 下一个节点
			var selfCell = null;// 自己
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				// 判断循环至的节点样式是否与传入的样式一致
				if (cellss.style == parent.cellObjGFSSDBCHECK.style)
				{
					if (cellss == parent.cellObjGFSSDBCHECK)
					{
						// 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
						selfCell = parent.cellObjGFSSDBCHECK;
					}
					else
					{
						// 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
						selfCell == null ? beforeCell = cellss : afterCell = cellss;
					}
					// 如果获取到了想要的节点，则跳出循环
					if (selfCell != null && ((inflag == 'after' && afterCell != null)
					        || (inflag == 'before' && beforeCell != null)))
					{
						break;
					}
				}
			}
			// 返回指定节点
			return inflag == 'after' ? afterCell : beforeCell;
		}
	}
});
