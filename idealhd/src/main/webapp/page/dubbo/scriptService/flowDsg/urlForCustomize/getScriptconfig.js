Ext.require('Ext.tab.*');
Ext.onReady(function() {

	var jspParms = tempData;
	delete tempData;
	
	if(jspParms.divID.match("null")){
		jspParms.divID.replace('null','');
	}
	
	var cell = this.GRAPHS[currentEDITOR].currentCell;
	
	var config = cell.getBean();
	
	var tab = Ext.widget('tabpanel', {
		renderTo : 'act_config_div_' + jspParms.divID,
		id : 'act_config' + jspParms.divID,
		activeTab : 0,
		width : '100%',
		height : '100%',
		plain : true,
		defaults : {
			autoScroll : true,
			bodyPadding : 10
		},
	});
	
	if(!cell.bean.normalclose)  
	{
		var normalPage = {
				xtype : 'ipanel',
				title : '通用配置',
				id : tab.id + '_normal',
				acturl : 'page/grapheditor/webstudio/act/base/normal.jsp?parentId=' + tab.id + '_normal'
		};
		tab.add(normalPage);
	}
	
	if(config.pages!=undefined)
		for (var i = 0; i < config.pages.length; i++) {
			var tmpUrl = "";
			var parms = jspParms;
			if(cell.bean.adaptertype=='2'&&config.pages[i].pageId!=undefined&&config.pages[i].pageId=='function')
			{
//				var needAgentValue = Ext.getCmp(jspParms.parentId).down('radiogroup').getValue().needAgent;
				
				if(jspParms.actionType == '')
					jspParms.actionType = 'edit';
				
				var operType = '';
				
//				if(jspParms.actionType == 'exec'||jspParms.actionType == 'edit')
//				{
//					if(needAgentValue=='1')
//					{
//						operType = cell.bean.pages[0].configurl.edit;
//					}else{
//						operType = cell.bean.pages[0].configurl.exec;
//					}
//				}
				
				if(jspParms.actionType == 'edit'||jspParms.actionType == 'create')
				{
					operType = cell.bean.pages[0].configurl.edit;
				}
				
				if(jspParms.actionType == 'exec'||jspParms.actionType == 'audi'||jspParms.actionType == 'test'||jspParms.actionType == 'dbbackForExec'||jspParms.actionType == 'dbcheckForExec')
				{
					operType = cell.bean.pages[0].configurl.exec;
				}
				
				
				if(jspParms.actionType == 'view')
				{
					operType = cell.bean.pages[0].configurl.view;
				}
				
				if(jspParms.actionType == 'model')
				{
					operType = cell.bean.pages[0].configurl.model;
				}
				if (jspParms.isForUrl=='1' || jspParms.isForUrl=='3') {
					tmpUrl = operType.url.replace('page/dubbo/scriptService/flowDsg/f','page/dubbo/scriptService/flowDsg/urlForCustomize/getF');
				}
				if(tmpUrl.match("GFSSVIEW/flowCustomizedEditScriptWindow")){
					tmpUrl = operType.url.replace('GFSSVIEW/flowCustomizedEditScriptWindow','urlForCustomize/getFlowCustomizedExecScriptWindow');
				}
				if(tmpUrl.match("GFSSVIEW")){
					tmpUrl = operType.url.replace('GFSSVIEW/f','GFSSVIEW/getF');
				}
			}else{
				if(jspParms.actionType!='exec')
				{
					break;
				}else{
					tmpUrl = config.pages[i].configurl;
				}
			}
			
			if(tmpUrl.indexOf("?") > 0)
				tmpUrl = tmpUrl + "&parentId=" + tab.id + '_' + config.pages[i].pageId + '&saveBtnShow=true&operColHide=true';
			else
				tmpUrl = tmpUrl + "?parentId=" + tab.id + '_' + config.pages[i].pageId + '&saveBtnShow=true&operColHide=true';
			
			
			var configPage = {
					xtype : 'ipanel',
					title : config.pages[i].pageTitle,
					id : tab.id + '_' + config.pages[i].pageId,
					reInit : false,
					acturl : tmpUrl,
					params : parms
				};
			tab.add(configPage);
		}
	
	if(cell.bean.input!=undefined&&cell.bean.input!='')
	{
		var inputPage = {
				xtype : 'ipanel',
				title : '活动输入',
				id : tab.id + '_input',
				acturl : 'page/grapheditor/webstudio/act/input/input.jsp?parentId=' + tab.id + '_input'
			};
		tab.add(inputPage);
	}
	if(cell.bean.output!=undefined&&cell.bean.output!='')
	{
		var outputPage = {
				xtype : 'ipanel',
				title : '活动输出',
				reInit : true,
				id : tab.id + '_output',
				acturl : 'page/grapheditor/webstudio/act/output/output.jsp?parentId=' + tab.id + '_output'
			};
		tab.add(outputPage);
	}
	if((cell.bean.output!=undefined&&cell.bean.output!='')&&(cell.bean.input!=undefined&&cell.bean.input!=''))
	{
		var branchPage = {
				xtype : 'ipanel',
				title : '分支条件',
				id : tab.id + '_branch',
				acturl : 'page/grapheditor/webstudio/act/branch/branch.jsp?parentId=' + tab.id + '_branch',
				oper : 0,
				branvalue : '',
				winHandler : ''
		}
		tab.add(branchPage);
	}
	
});