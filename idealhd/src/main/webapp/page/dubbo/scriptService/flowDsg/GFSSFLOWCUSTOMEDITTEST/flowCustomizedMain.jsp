<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSFLOWCUSTOMEDITTEST=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSFLOWCUSTOMEDITTEST=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSFLOWCUSTOMEDITTEST=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSFLOWCUSTOMEDITTEST=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSFLOWCUSTOMEDITTEST=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSFLOWCUSTOMEDITTEST=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var customIdGFSSFLOWCUSTOMEDITTEST=<%=request.getParameter("customId")%>;

var customNameGFSSFLOWCUSTOMEDITTEST='<%=request.getParameter("customName")%>';

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSFLOWCUSTOMEDITTEST='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSFLOWCUSTOMEDITTEST='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSFLOWCUSTOMEDITTEST=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSFLOWCUSTOMEDITTEST=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSFLOWCUSTOMEDITTEST='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSFLOWCUSTOMEDITTEST='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSFLOWCUSTOMEDITTEST = <%=request.getAttribute("fromType")%>;
var workItemidGFSSFLOWCUSTOMEDITTEST = <%=request.getAttribute("workItemid")%>;
var fromGFSSFLOWCUSTOMEDITTEST = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSFLOWCUSTOMEDITTEST = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSFLOWCUSTOMEDITTEST = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSFLOWCUSTOMEDITTEST = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSFLOWCUSTOMEDITTEST = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSFLOWCUSTOMEDITTEST=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSFLOWCUSTOMEDITTEST=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSFLOWCUSTOMEDITTEST='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSFLOWCUSTOMEDITTEST='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSFLOWCUSTOMEDITTEST='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSFLOWCUSTOMEDITTEST=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSFLOWCUSTOMEDITTEST=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSFLOWCUSTOMEDITTEST=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITTEST='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITTEST='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITTEST='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSFLOWCUSTOMEDITTEST = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSFLOWCUSTOMEDITTEST = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSFLOWCUSTOMEDITTEST = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSFLOWCUSTOMEDITTEST = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSFLOWCUSTOMEDITTEST = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSFLOWCUSTOMEDITTEST = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITTEST/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSFLOWCUSTOMEDITTEST" style="width: 100%;height: 100%"></div>
</body>
</html>