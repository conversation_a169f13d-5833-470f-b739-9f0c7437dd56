<%@page contentType="text/html; charset=utf-8"%>
<html>
<script type="text/javascript">
var flagGFSSFLOWCUSTOMPRODUCT = <%=request.getParameter("flag")%>;
var actionTypeGFSSFLOWCUSTOMPRODUCT = '<%=request.getParameter("actionType")%>';
</script>
<!-- <PERSON> passes the container for the graph to the program -->
<body  style="margin:0px;">
	
	<!-- Creates a container for the splash screen -->
	<div id="splashGFSSFLOWCUSTOMPRODUCT"
		style="position:absolute;top:0px;left:0px;width:100%;height:100%;background:white;z-index:1;">
		<center id="splashGFSSFLOWCUSTOMPRODUCT" style="padding-top:230px;">
			<img src="mxgraph-master/examples/editors/images/loading.gif">
		</center>
	</div>
	
	<!-- Creates a container for the sidebar -->
	<div id="sidebarContainerG<PERSON>SFLOWCUSTOMPRODUCT"
		style="position:absolute;white-space:nowrap;overflow:hidden;top:0px;max-height:60px;height:60px;right:0px;padding:6px;">
	</div>
	<!-- Creates a container for the toolboox -->
<!-- 	<div id="sidebarContainer"
		style="position:absolute;overflow-y :auto;top:0px;left:0px;bottom:5px;max-width:80px;width:100px;padding-top:10px;padding-left:4px;">
	</div> -->

	<!-- Creates a container for the graph -->
	<div id="graphContainerGFSSFLOWCUSTOMPRODUCT"
		style="position:absolute;overflow:hidden;top:50px;left:0px;bottom:0px;right:0px;cursor:default;background-image:url(images/mxgraphImages/mxgraph_grid.png);">
	</div>

	<!-- Creates a container for the outline -->
	<div id="outlineContainerGFSSFLOWCUSTOMPRODUCT"
		style="position:absolute;overflow:hidden;top:65px;right:5px;width:200px;height:140px;background:transparent;border-style:solid;border-color:#9fa0a0;">
	</div>
		
	<!-- Creates a container for the sidebar -->
	<div id="statusContainerGFSSFLOWCUSTOMPRODUCT"
		style="text-align:left;position:absolute;overflow:hidden;left:0px;max-height:60px;height:60px;color:white;padding:6px;">
	</div>
	<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMPRODUCT/flowCustomizedImg.js"></script>
</body>
<script type="text/javascript">
</script>
</html>