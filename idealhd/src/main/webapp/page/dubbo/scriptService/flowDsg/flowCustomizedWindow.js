/*******************************************************************************
 * 流程定制维护窗口-阶段模板
 ******************************************************************************/
var graphFlowWindow;
var modelFlowWindow;
var editorFlowWindow;
var stageNameObj=null;
var templateNameObj=null;
var chosedServList=null;
var cellObjFlowWindow=null;
var resourceGroupObj=null;
var formPanel2=null;
var loadGetParentValue=false;
Ext.onReady(function () {
	var currentEDITOR = namespace;
	namespace = ''; // 该变量在jsp中声明，用于临时转存变量域名
	/** 获取内外对接的参数 **/
	var graph = parent.GRAPHS[currentEDITOR];
	var cell = graph.currentCell;
	var configWindow = graph.configWindow;
	
	
	
	/** 模板下拉框数据Model* */
	Ext.define ('templateModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'instanceName',
		        type : 'string'
		    },{
		        name : 'iid',
		        type : 'long'
		    }
	    ]
	});
	/** 模板下拉框据源* */
	var templateNameStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad :true,
	    autoDestroy : true,
	    model : 'templateModel',
	    pageSize : 1000,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getTemplateList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	templateNameStore.on ('beforeload', function (store, options)
			{
				var new_params =
				{
						suspahaseId : cell.phaseId
				};
				Ext.apply (templateNameStore.proxy.extraParams, new_params);
			});
	stageNameObj= Ext.create ('Ext.form.field.Text',
			{
				fieldLabel : '阶段名称',
				margin : '5 0 0 0',
				readOnly:true
			
			});
	templateNameObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '模板名称',
			    margin : '5 0 5 0',
			    store : templateNameStore,
			    displayField : 'instanceName',
			    valueField : 'iid',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
			    listeners :
	            {
		            select : function ( combo, record, eOpts )
		            {
//		            	loadGraph(graph,record[0].get("id")) ;
		            	if(this.value==cell.modelId)
	            		{
	            		loadGetParentValue=true;
	            		}
		            	cellClickFun(null,'资源组服务器配置');
		            	loadGraph(this.value);
		            	
		            }
	            
	            }
			});
	var formPanel =Ext.create ('Ext.form.Panel',
			{
			region: "north",
			    // title: 'Basic Form',
				border:false,
			    fieldDefaults :
			    {
			        labelAlign : 'right',
//			        width : 475,
			        width : contentPanel.getWidth ()-30,
			        labelWidth : 60
			    },
//			    bodyPadding : 5,
			    width : '100%',
			    buttonAlign : 'center',
			    items : [
			            stageNameObj, templateNameObj
			            //, templateParamObj, resourceGroupObj, serverListObj
			    ],
			    
			    buttons : [
			            {
			                text : '保存',
			                handler : saveCallbackFun 
			            },
			            {
			                text : '返回',
			                handler : function ()
			                {
			                	configWindow.close ();
			                }
			            }
			    ]
			});
	Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'resourceGroup/groups.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    margin : '5 0 0 0',
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    hidden:removeAgentSwitch,
			    mode : 'local',
			    	listeners: {
			    	      select: function() {
			    	    	  choosedStore.getProxy().setExtraParam("groupId", this.getValue());
			    	    	    choosedStore.getProxy().setExtraParam("isChoosed", true);
			    	    	    choosedStore.load();
			    	      }
	}
			    	
			    	
			    	
			});
	var saveButtonFlowWindow = Ext.create('Ext.Button', {
	    text : '保存',
	    width : 70,
	    height : 22,
	    margin : '5 0 0 5',
	    textAlign : 'center',
//	    cls : 'Common_Btn',
	    handler : function() {
//	        editorSon.execute ('save');
	    	saveServerData();
	      }
	  });
	
	
	Ext.define('serverModel', {
	    extend : 'Ext.data.Model',
	    fields : [ {
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'hostName',
	      type : 'string'
	    }, {
	      name : 'ip',
	      type : 'string'
	    }, {
	      name : 'port',
	      type : 'string',
	      defaultValue: 1500
	    },{
	      name : 'priority',
	      type : 'int',
	      defaultValue: 5
	    }, {
	      name : 'systemType',
	      type : 'string'
	    }, {
	      name : 'checked',
	      type : 'boolean'
	    }, {
	      name : 'itemClicked',
	      type : 'boolean'
	    }, {
	        name : 'isleftScreen',
	        type : 'boolean',
	        defaultValue: false
	      } ]
	  });
	  var choosedStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    autoDestroy: true,
		    model: 'serverModel',
		    proxy: {
		      type: 'ajax',
		      url: 'resourceServer/servers.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		  
	  choosedStore.addListener('load',function(){
		  var str= new Array();   
		  if (cellObjFlowWindow!=null&&typeof (cellObjFlowWindow.serverList) != "undefined")
			{
	    	  var datastr=cellObjFlowWindow.serverList.servers;
	    	  str=(datastr+',').split(",");      
			}
		    var records=[];//存放选中记录
		    if(cellObjFlowWindow!=null&&typeof (cellObjFlowWindow.serverList) != "undefined"&&resourceGroupObj.getValue()==cellObjFlowWindow.serverList.groupId * 1)
		    	{
		    	for(var i=0;i<choosedStore.getCount();i++){
				      var record = choosedStore.getAt(i);
//				      alert(record.data.id);
				      for (ii=0;ii<str.length ;ii++ )   
			    	    {   
				    	  
				    	  if(str[ii]==record.data.id)
				    		  {
				    		  records.push(record);
				    		  }
			    	    }   
				    }
				   
		    	}
		    chosedServList.getSelectionModel().select(records);//选中记录
		  });
	  
	  var selModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true
		});

	 chosedServList = Ext.create('Ext.grid.Panel', {
//		    region : 'west',
//		    title : '绑定服务器',
//		    width : "50%",
		    multiSelect: true,
		    split : true,
//		    xtype: 'cell-editing',
//		    frame : true,
		    columnLines : true,
//		    emptyText: '没有服务器信息',
		    store : choosedStore,
		    selModel: selModel,
//		    selModel: {
//		      selType: 'cellmodel'
//		    },
		    columns : [{
		      text: '编号',
		      width: 40,
		      sortable: true,
		      hidden: false,
		      dataIndex: 'id'
		    }, {text: '序号', xtype: 'rownumberer', width: 40},{
		      text: '服务器名',
		      dataIndex: 'hostName',
		      flex: 1,
		      sortable: true
		    }, {
		      text: 'IP',
		      dataIndex: 'ip',
		      flex: 1,
		      sortable: true
		    }, {
		      text: '端口号',
		      dataIndex: 'port',
		      sortable: true
		    },{
		      text : '优先级',
		      dataIndex : 'priority',
		      sortable : true
		    }, {
		      text: '应用标识',
		      dataIndex: 'systemType',
		      flex: 1,
		      sortable: true
		    }]
		  });
	
	
	
	var middlePanel=Ext.create('Ext.Panel', {
//		title:'提示信息',
//		width:'100%',
		height : contentPanel.getHeight ()-195,
		split: true,
//		region: 'south',
		layout:'fit',
		border : false,	
//		titleAlign : 'left',
		items : [
chosedServList
	            ]
	});
	formPanel2 =Ext.create ('Ext.form.Panel',
			{
		region: "center",
		margin : '0 0 0 5',
		title: '资源组服务器配置', 
				border:true,

			    fieldDefaults :
			    {
			        labelAlign : 'right',
//			        width : 475,
			        width : contentPanel.getWidth ()/2-200,
			        labelWidth : 60
			    },
//			    bodyPadding : 5,
			    width : '100%',
			    buttonAlign : 'center',
			    items : [
				    {
				        xtype : "container",
				        layout : "hbox",
				        margin : 2,
				        items : [
resourceGroupObj,saveButtonFlowWindow
				        ]
				    },middlePanel
			    
			    ]
			    
			});
	var westPanel = Ext.create('Ext.panel.Panel', {
		title: '模板图', 
		region: "west",
		width: '50%',
		height : contentPanel.getHeight ()-65,
		border : true,
		html:'<div id="flowCustomizedWindowDiv2" style="position:absolute;overflow:hidden;top:0px;left:0px;bottom:0px;right:0px;cursor:default;background-image:url(images/mxgraphImages/mxgraph_grid.png);"></div>',
		bodyPadding : 5
		
	});
	var MainPanel = Ext.create('Ext.panel.Panel', {
		layout: "border",
		renderTo : "flowCustomizedWindowDiv",
		width : '100%',
		height : contentPanel.getHeight ()-32,
		border : false,
		bodyPadding : 5,
			 items: [
	                    westPanel,
	                    formPanel,
	                    formPanel2
	                ]
	});
	/** 窗口尺寸调节* */
	 contentPanel.on ('resize', function ()
	 {
		 MainPanel.setWidth('100%');
	 })
	
});
/**
 * 加载模板图形
 * @param instanceId
 */
function loadGraph(instanceId) {
	modelFlowWindow.beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("getTemplateXml.do?instanceID="+instanceId));
		var dec = new mxCodec(doc);
		dec.decode(doc.getDocumentElement(), modelFlowWindow);
	} finally {
		// Updates the display
		modelFlowWindow.endUpdate();
	}
//	graph.graphToCenter();
	//是否将父页面xml中的服务器信息填写至当前模板
	if(loadGetParentValue)
		{
		setCellServers()
		}
}
/**
 * 是否将父页面xml中的服务器信息填写至当前模板
 */
function setCellServers()
{
	var gas=cell.groupAndServer;
	if(gas!=null&&gas.length>0)
		{
		//遍历所有节点
		var root2FlowWindow = modelFlowWindow.getRoot ();
		var count = modelFlowWindow.getChildCount (root2FlowWindow);
		for (var i = 0; i < count; i++)
		{
			var cells = root2FlowWindow.getChildAt (i);
			var counts = cells.getChildCount ();
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				//如果不是连线，则是节点
				if (!modelFlowWindow.isEdge (cellss))
				{
					for(var z=0;z<gas.length;z++)
					{
					
						if(gas[z].serner==cellss.id)
							{
							cellss.serverList=gas[z];
							}
					}
				}

			}
		}
		}
	//将标识重置
	loadGetParentValue=false;
}
/**
 * 将模板资源组服务器回填至父页面节点中
 */
function saveCallbackFun ()
{
	if(templateNameObj.getValue()<1)
	{
	Ext.Msg.alert('提示', '模板名称不允许为空!');	
	return null;
	}
	//遍历所有节点
	var root2FlowWindow = modelFlowWindow.getRoot ();
	var count = modelFlowWindow.getChildCount (root2FlowWindow);
	var myArray=new Array();
	for (var i = 0; i < count; i++)
	{
		var cells = root2FlowWindow.getChildAt (i);
		var counts = cells.getChildCount ();
		for (var j = 0; j < counts; j++)
		{
			var cellss = cells.getChildAt (j);
			//如果不是连线，则是节点
			if (!modelFlowWindow.isEdge (cellss))
			{
				if(null!=cellss.serverList)
				{
//				alert(cellss.serverList.servers);
				myArray.push(cellss.serverList);
				}
			
			}
//			if (cellss.id < 68 && cellss.edge != '1')
//			{
//				cellss.state = 0;
//				graph.setCellWarning (cellss, '完成', new mxImage ('images/uthome/finished.gif', 16, 16), true);
//			}
//			if (cellss.id == 68)
//			{
//				cellss.state = 1;
//				graph.setCellWarning (cellss, '运行', new mxImage ('images/uthome/running.gif', 16, 16), true);
//			}
		}
	}
	cell.groupAndServer=myArray;
	cell.modelId=templateNameObj.getValue();
//	parent.callbackWindw();
	configWindow.close ();
}
/**
 * 双击图形中的模板节点触发事件
 * @param cellIn
 * @param formPanelTitle
 */
function cellClickFun(cellIn,formPanelTitle)
{
	cellObjFlowWindow=cellIn;
	if(cellObjFlowWindow!=null)
		{
		if (typeof (cellObjFlowWindow.serverList) == "undefined")
		{
			resourceGroupObj.setValue ('');
		}
		else
		{
			resourceGroupObj.setValue (cellObjFlowWindow.serverList.groupId * 1);
		}
		}else
			{
			resourceGroupObj.setValue ('');
			}
	
	resourceGroupObj.fireEvent ('select');
	formPanel2.setTitle(formPanelTitle);
}
/**
 * 保存模板的资源组服务器
 * @returns
 */
function saveServerData() {
	var m = chosedServList.getSelectionModel().getSelection();
	if (m.length < 1) {
		Ext.MessageBox.alert("提示", "请选择服务器!");
		return;
	}
	if(cellObjFlowWindow==null)
	{
	Ext.Msg.alert('提示', '请选择模板步骤!');	
	return null;
	}
	var jsonData ='{"scriptId":"'+cellObjFlowWindow.scriptId+'","serner":"'+cellObjFlowWindow.id+'","groupId":'+'"'+resourceGroupObj.getValue()+'","servers":"';
	for (var i = 0, len = m.length; i < len; i++) {
		var ss = Ext.JSON.encode(m[i].data.id);
		if (i == 0)
			jsonData = jsonData + ss;
		else
			jsonData = jsonData + "," + ss;
	}
	jsonData = jsonData + '"}';
//	alert(jsonData);return;
//	alert( JSON.parse(jsonData));
	
	cellObjFlowWindow.serverList=JSON.parse(jsonData);
	Ext.MessageBox.alert("提示", "操作成功!");
//	
}
