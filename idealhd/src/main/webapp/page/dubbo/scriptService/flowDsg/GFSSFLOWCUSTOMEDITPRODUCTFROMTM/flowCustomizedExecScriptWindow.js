/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/
Ext.onReady (function ()
{
	var scriptNameObj;
	var scriptContentObj;
	var stepNameObj;
	var paramObj;
	var startUserObj;
	var resourceGroupObj;
	var chosedServList;
	var agent_store;
	var cellScriptType = '';
	var chosedResGroups = new Array();
	var finalChosedAgents = new Array();
	var chosedAgentIds = [];
	var finalChosedAgentsAndParams = {};
	var editingChosedAgentsAndParams = {};

	var editingGlobalParams = {};
	var finalGlobalParams = {};

	var editingConfigParams = {};
	var finalConfigParams = {};

	var finalChosedAgentsAndStartUsers = {};
	var editingChosedAgentsAndStartUsers = {};

	var finalChosedAgentsAndDbSources = {};
	var editingChosedAgentsAndDbSources = {};
	var chosedAgentWin;
	var chosedAgentWinForSee;
	var agentParamsWin;
	var scriptServiceId = -1;
	
	stepNameObj = Ext.create ('Ext.form.field.Text', {
	    fieldLabel : '步骤名称',
	    readOnly: true
	});
	paramObj = Ext.create ('Ext.form.field.Text', {
		fieldLabel : '执行参数',
		hidden : true,
		readOnly: true
	});
	startUserObj = Ext.create('Ext.form.field.Text', {
		fieldLabel : '启动用户',
		readOnly: parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec'
	});
	scriptNameObj = Ext.create('Ext.form.field.Text', {
		fieldLabel : '脚本服务名称',
		readOnly: true,
		width: contentPanel.getWidth ()-140
	});
	
	scriptContentObj = Ext.create ('Ext.form.field.TextArea', {
		fieldLabel : '脚本内容',
		height: 300,
		readOnly:true,
//	    padding : '5 5 5 5',
	    autoScroll : true
	});
	
	var prevButton = Ext.create('Ext.Button', {
	    text : '上一步',
	    margin : '0 0 0 60',
	    textAlign : 'center',
//	    cls : 'Common_Btn',
	    handler : function() {
	    	var res = getCellFun("before");
	    	if(res) {
	    		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM = res;
	    		initFun ();
	    		resourceGroupStore.load();
//	    		agent_store.load();
	    		
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为第一个步骤");
	    	}
	      }
	  });
			
	var nextButton = Ext.create('Ext.Button', {
		text : '下一步',
		margin : '0 0 0 5',
		textAlign : 'center',
//	    cls : 'Common_Btn',
		handler : function() {
			var res = getCellFun("after");
	    	if(res) {
	    		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM = res;
	    		initFun ();
	    		resourceGroupStore.load();
//	    		agent_store.load();
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为最后一个步骤");
	    	}
		}
	});
	
	var viewChosedScriptButton = Ext.create('Ext.Button', {
		text : '查看脚本详情',
		margin : '0 0 0 10',
		textAlign : 'center',
		handler : function() {
			if (parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId!=null&&typeof (parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId) != "undefined") {
				Ext.create('widget.window', {
		            title: '详细信息',
		            closable: true,
		            closeAction: 'destroy',
		            width: contentPanel.getWidth(),
		            minWidth: 350,
		            height: contentPanel.getHeight(),
		            draggable: false,
		            // 禁止拖动
		            resizable: false,
		            // 禁止缩放
		            modal: true,
		            loader: {
		                url: 'queryOneServiceForView.do',
		                params: {
		                    iid: parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId,
		                    flag: 0,
		                    hideReturnBtn: 1
		                },
		                autoLoad: true,
		                scripts: true
		            }
		        }).show();
			} else {
				Ext.Msg.alert('提示',"没有脚本服务！");
			}
		}
	});
	
			
	var formPanel=Ext.create ('Ext.form.Panel',
	{
		border : false,
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        width : contentPanel.getWidth ()-30,
	        labelWidth : 90
	    },
	    buttonAlign : 'center',
	    items : [
	    	stepNameObj,{
				layout : 'column',
				anchor : '100%',
				height : 35,
				border : false,
				items : [scriptNameObj, viewChosedScriptButton]
			}, paramObj,startUserObj //,stageNameObj, templateNameObj, templateParamObj, resourceGroupObj, serverListObj
	    ],
	    buttons : [{
            text : '保存',
            hidden: parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec',
            handler : saveFun
        }, {
            text : '关闭',
            handler : function () {
            	parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM = null;
            	parent.configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM.close ();
            }
        }, prevButton, nextButton]
	});
	
	/** 树数据Model* */
	Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
        	name: 'paramValue',
        	type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });
	
	/** 树数据源* */
	var paramStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getAllScriptParams.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	paramStore.on ('beforeload', function (store, options) {
		var new_params = {
				scriptId: parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId
		};
		Ext.apply (paramStore.proxy.extraParams, new_params);
	});
	
	var configParamStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getAllScriptParams.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	configParamStore.on ('beforeload', function (store, options) {
		var new_params = {
				scriptId: parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId
		};
		Ext.apply (configParamStore.proxy.extraParams, new_params);
	});
	/*configParamStore.addListener('load',function(){
		
		for(var prop in editingConfigParams){
		    if(editingConfigParams.hasOwnProperty(prop)){
		        configParamStore.findRecord('iid', prop+"").set('paramValue', editingConfigParams[prop]);
		    }
		}
      });*/
	/** 树列表columns* */
	var paramColumns = [
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 100,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '默认参数值',
        dataIndex: 'paramDefaultValue',
        width: 100,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
    	text: '参数值',
    	dataIndex: 'paramValue',
    	width: 100,
    	editor: {
    		allowBlank: true
    	},
    	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
    		metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
    		return value;  
    	}
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing_for_config_params = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
	/** 树列表panel* */
	var paramGrid = Ext.create ('Ext.grid.Panel',
	{
		width: '39%',
		height: contentPanel.getHeight() - 200,
	    store : paramStore,
	    margin: '0 0 0 10',
	    title: '参数信息',
	    border : true,
	    columnLines : true,
	    columns : paramColumns,
	    plugins: [cellEditing],
	    collapsible : false
	});
	var paramGrid_for_config_params = Ext.create ('Ext.grid.Panel',
	{
		width: '50%',
		height: contentPanel.getHeight() - 250,
	    store : configParamStore,
	    margin: '0 0 0 10',
	    title: '参数信息',
	    border : true,
	    columnLines : true,
	    columns : paramColumns,
	    plugins: [cellEditing_for_config_params],
	    collapsible : false
	});
	
	paramGrid_for_config_params.on('edit', function(editor, e) {
		var ipRecord = agent_grid_chosed_for_config_param.getSelectionModel().getSelection()[0];
		if(ipRecord) {
			var configParams = {};
			for (var i = 0; i <  e.grid.getStore().getCount(); i++) {
				var record =  e.grid.getStore().getAt(i);
				var iid = record.get('iid');
				var paramType = record.get('paramType');
				var paramValue = record.get('paramValue');
				
				if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramValue) {
	                if (!checkIsInteger(paramValue)) {
	                	Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
	                    return;
	                }
	            }
	            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramValue) {
	                if (!checkIsDouble(paramValue)) {
	                	Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
	                    return;
	                }
	            }
	            if (paramValue.indexOf('"')>=0) {
	            	if(cellScriptType=='bat') {
	            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
	                    return;
	            	}
	            }
	            
				configParams[iid] = paramValue;
			}
			editingConfigParams[ipRecord.get('iid')] = configParams;//绑定
		}
	});
	
	
	Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	resourceGroupObj=Ext.create ('Ext.form.field.ComboBox', {
	    fieldLabel : '资源组',
	    multiSelect: true,
	    labelWidth : 58,
	    store : resourceGroupStore,
	    displayField : 'name',
	    valueField : 'id',
	    triggerAction : 'all',
	    editable : false,
	    mode : 'local',
    	listeners: {
			select: function( combo, records, eOpts ) {
				if(records) {
					chosedResGroups = new Array();
					for(var i=0;i<records.length;i++) {
						chosedResGroups.push(records[i].data.id);
					}
					agent_store.load();
				} else {
					agent_store.load();
				}
				
			}
    	}
	});
	//begin
	var app_name = new Ext.form.TextField({
		name : 'appname',
		fieldLabel : '应用名称',
		displayField : 'appname',
		emptyText : '--请输入应用名称--',
		labelWidth : 58,
		hidden : true,
		labelAlign : 'right',
		width : '20%'
	});
	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'AgentIp',
		displayField : 'agentip',
		emptyText : '--请输入agentip--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '25%'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '主机名称',
		displayField : 'hostname',
		emptyText : '--请输入主机名称--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '25%'
	});
	Ext.define('sysNameModel', {
    	extend: 'Ext.data.Model',
    	fields : [ {
    		name : 'sysName',
    		type : 'string'
    	}]
    });
	
	var sys_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'sysNameModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentSysNameList.do?envType=1',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var sys_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
		name : 'sysname',
	    fieldLabel: "系统名称",
	    store: sys_name_store,
	    queryMode: 'local',
	    width: "25%",
	    displayField: 'sysName',
	    valueField: 'sysName',
	    labelWidth : 58,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
	
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '系统类型',
		displayField : 'ostype',
		emptyText : '--系统类型--',
		labelWidth : 58,
		labelAlign : 'right',
		width : '25%'
	});
	
	//end
	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string', defaultValue: 1500},
            {name: 'agentDesc',     type: 'string'},
            {name: 'resGroup',     type: 'string'},
            {name: 'agentState',     type: 'int'},
            {name: 'agentParam',     type: 'string'},
            {name: 'agentStartUser',     type: 'string'}
        ]
    });
	agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        groupField:'resGroup', //确定哪一项分组  
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	
	var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });
	
	agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
    		flag:flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
    		hasPage: false,
    		rgIds:chosedResGroups,
    		agentIp : Ext.util.Format.trim(agent_ip.getValue()),
	    	appName : Ext.util.Format.trim(app_name.getValue()),
			sysName : sys_name.getValue()==null?'':Ext.util.Format.trim(sys_name.getValue()+""),
			hostName : Ext.util.Format.trim(host_name.getValue()),
			osType : Ext.util.Format.trim(os_type.getValue())
	    };
	    
	    if(parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec') {
	    	new_params['agentIds'] = finalChosedAgents;
	    }
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
		  
	agent_store.addListener('load',function(){
		
		for(var prop in editingChosedAgentsAndParams){
		    if(editingChosedAgentsAndParams.hasOwnProperty(prop)){
		    	if(agent_store.findRecord('iid', prop+"")) {
		        agent_store.findRecord('iid', prop+"").set('agentParam', editingChosedAgentsAndParams[prop]);
		    	}
		    }
		}
		for(var prop in editingChosedAgentsAndStartUsers){
			if(editingChosedAgentsAndStartUsers.hasOwnProperty(prop)){
				if(agent_store.findRecord('iid', prop+"")) {
				agent_store.findRecord('iid', prop+"").set('agentStartUser', editingChosedAgentsAndStartUsers[prop]);
				}
			}
		}
		
		  var chosedAgents=finalChosedAgents;//parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.chosedAgents;
		  chosedAgentIds = finalChosedAgents;
		  if(chosedAgents) {
			  var records=[];//存放选中记录
			  for(var i=0;i<agent_store.getCount();i++){
			      var record = agent_store.getAt(i);
			      for (ii=0;ii<chosedAgents.length;ii++ )   
		    	    {   
			    	  
			    	  if(chosedAgents[ii]==record.data.iid)
			    		  {
			    		  records.push(record);
			    		  }
		    	    }   
			  }
			  chosedServList.getSelectionModel().select(records, false, true);//选中记录
		  }
      });
	  
	if(parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec') {
		var selModel = null;
	} else {
		var selModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true
		});
	}
	  
	  var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 2
		});
	  
	  var agent_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: false,
			model: 'agentModel',
			pageSize: 50,
			proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
	  agent_store_chosed.on('beforeload', function (store, options) {
	    	var new_params = {  
	    			agentIds : JSON.stringify(chosedAgentIds)
	    	};
	    	
	    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
	    });
	  agent_store_chosed.on('load', function () {
		  agent_grid_chosed_for_config_param.getSelectionModel().select(0);
	  });
	  
	  
	  var pageBarForAgentChosedToSetParamsGrid = Ext.create('Ext.PagingToolbar', {
	    	store : agent_store_chosed,
	    	dock : 'bottom',
	    	displayInfo : true
	    });
	  
	  var agent_store_chosedForSee = Ext.create('Ext.data.Store', {
			autoLoad: false,
			pageSize: 50,
			model: 'agentModel',
			proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
	  agent_store_chosedForSee.on('beforeload', function (store, options) {
	    	var new_params = {  
	    			agentIds : JSON.stringify(chosedAgentIds)
	    	};
	    	
	    	Ext.apply(agent_store_chosedForSee.proxy.extraParams, new_params);
	    });

	  var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
		  title: '已选服务器列表',
	    	store:agent_store_chosed,
	    	border:true,
	    	columnLines : true,
	    	width: '49%',
	    	height : contentPanel.getHeight () -150,
	    	autoScroll : true,
	    	emptyText: '没有选择服务器',
	    	columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
                { text: '主键',  dataIndex: 'iid',hidden:true},
                { text: 'IP',  dataIndex: 'agentIp',width:120},
                { text: '端口号',  dataIndex: 'agentPort',width:100},
	            { text: '描述',  dataIndex: 'agentDesc',flex:1},
	            { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
	            	var backValue = "";
	            	if(value==0){
	            		backValue = "Agent正常";
	            	}else if(value==1){
	            		backValue = "Agent异常";
	            	}
	            	return backValue;
	            }}
           ],
			listeners : {
       			select:function(self, record, index, eOpts) {
       				 dbinfo_store.load({
   			            params: {
   			            	agentId: record.get("iid"),
   			                agentIp: record.get("agentIp"),
   			                agentPort: record.get("agentPort")
   			            },
   			            callback: function(records, operation, success) {
   			            	if(editingChosedAgentsAndDbSources.hasOwnProperty(record.get("iid"))) {
   			            		$.each(records, function(index, val){
   			            			if(val.get('iid')==editingChosedAgentsAndDbSources[record.get("iid")]) {
   			            				selModel2.select(val,false, true);
   			            			}
   			            		});
	   			 			}
   			            }
   			        });
       			 }
       		}
	    });
	    
	    var agent_grid_chosed_for_config_param = Ext.create('Ext.grid.Panel', {
		  title: '已选服务器列表',
	    	store:agent_store_chosed,
	    	border:true,
	    	columnLines : true,
	    	bbar: pageBarForAgentChosedToSetParamsGrid,
	    	width: '49%',
	    	height : contentPanel.getHeight () -150,
	    	autoScroll : true,
	    	emptyText: '没有选择服务器',
	    	columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
                { text: '主键',  dataIndex: 'iid',hidden:true},
                { text: 'IP',  dataIndex: 'agentIp',width:120},
                { text: '端口号',  dataIndex: 'agentPort',width:100},
	            { text: '描述',  dataIndex: 'agentDesc',flex:1},
	            { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
	            	var backValue = "";
	            	if(value==0){
	            		backValue = "Agent正常";
	            	}else if(value==1){
	            		backValue = "Agent异常";
	            	}
	            	return backValue;
	            }}
           ],
			listeners : {
       			select:function(self, record, index, eOpts) {
       				var ipId = record.get('iid');
       				if(editingConfigParams.hasOwnProperty(ipId)){
       					var cp = editingConfigParams[ipId];
       					configParamStore.each(function(record){
       						record.set('paramValue', cp[record.get('iid')])
       					});
       				} else {
       					configParamStore.each(function(record){
       						record.set('paramValue', '')
       					});
       				}
//       				for(var prop in editingConfigParams){
//					    if(editingConfigParams.hasOwnProperty(prop)){
//					        configParamStore.findRecord('iid', prop+"").set('paramValue', editingConfigParams[prop]);
//					    }
//					}
//       				configParamStore.load();
//       				 configParamStore.load({});
       			 }
       		}
	    });
	  
	  Ext.define('dbModel', {
	        extend: 'Ext.data.Model',
	        idProperty: 'iid',
	        fields: [
	            {name: 'iid',        type: 'string'},
	            {name: 'dsName',        type: 'string'},
	            {name: 'driverClass',type: 'string'},
	            {name: 'dbUrl',      type: 'string'},
	            {name: 'dbUser',     type: 'string'},
	            {name: 'dbType',     type: 'string'}
	        ]
	    });
	  
	  var dbinfo_store = Ext.create('Ext.data.Store', {
	    	model:'dbModel',
	    	autoLoad: false,
	    	proxy: {
	    		type: 'ajax',
	    		url: 'getDbSqlDriverInfo.do',
	    		reader: {
	    			type: 'json',
	    			root: 'dataList'
	    		}
	    	}
	    });
	  
	  var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
          { text: '主键',  dataIndex: 'iid',hidden:true},
          { text: '数据源名称',  dataIndex: 'dsName',width:100,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
              metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
              return value;  
          }},
          { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
        	  metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
        	  return value;  
          }},
          { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
              metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
              return value;  
          }},
          { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
          { text: 'DB类型',  dataIndex: 'dbType',width:110}];
	  
	  var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			mode : "SINGLE",
			listeners : {
				select:function(self, record, index, eOpts) {
					if(parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec') {
						
					} else {
						var ipRecord = agent_grid_chosed.getSelectionModel().getSelection()[0];
						var dsid = record.get("iid");
						editingChosedAgentsAndDbSources[ipRecord.get('iid')] = dsid;//绑定
					}
				},
				deselect:function(self, record, index, eOpts) {
					if(parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec') {
						
					} else {
						var ipRecord = agent_grid_chosed.getSelectionModel().getSelection()[0];
						delete editingChosedAgentsAndDbSources[ipRecord.get('iid')];  
					}
				}
			}
		});
	  
	  var dbsource_grid = Ext.create('Ext.grid.Panel', {
		  title: parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec'?'已选数据源':'选择数据源',
	    	store:dbinfo_store,
	    	 width: '50%',
	    	 height : contentPanel.getHeight ()-150,
	    	 autoScroll : true,
	    	 margin: '0 0 0 10',
	    	border:true,
	    	columnLines : true,
	    	columns:dbsource_columns,
	    	selModel:selModel2
	    });
	  
	  var choseAgentWrapper = Ext.create('Ext.panel.Panel',{
	        border : false,
	        height : contentPanel.getHeight ()-100,
	        layout: {
	            type: 'hbox',
	            padding:'5',
	            align:'stretch'
	        },
	        items : [agent_grid_chosed, dbsource_grid]
		});
		
		var choseAgentWrapper_for_config_params = Ext.create('Ext.panel.Panel',{
	        border : false,
	        height : contentPanel.getHeight ()-100,
	        layout: {
	            type: 'hbox',
	            padding:'5',
	            align:'stretch'
	        },
	        items : [agent_grid_chosed_for_config_param,paramGrid_for_config_params]
		});
	  
	var choseDbSourceButton = Ext.create('Ext.Button', {
		text : parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec'?'已选数据源':'选择数据源',
		cls : 'Common_Btn',
		hidden: true,
		handler : function() {
			if(!chosedAgentWin) {
				chosedAgentWin = Ext.create('Ext.window.Window', {
			  		title : '配置数据源',
			  		autoScroll : true,
			  		modal : true,
			  		resizable : false,
			  		closeAction : 'hide',
			  		width : contentPanel.getWidth(),
			  		height : contentPanel.getHeight (),
			  		items:[choseAgentWrapper],
			  		buttonAlign: 'center',
			  		buttons: [{ 
			  			xtype: "button", 
			  			text: "确定", 
			  			handler: function () {
			  				this.up("window").close();
			  			}
			  		}]
			  	});
			}
			chosedAgentWin.show();
			dbinfo_store.removeAll();
			agent_store_chosed.removeAll();
			agent_store_chosed.loadRecords(chosedServList.getSelectionModel().getSelection());
			agent_grid_chosed.getSelectionModel().select(0);
		}
	});
	
		var parmsButton = Ext.create('Ext.Button', {
		text : '配置参数',
		cls : 'Common_Btn',
		//hidden: true,
		handler : function() {
			if(!agentParamsWin) {
				agentParamsWin = Ext.create('Ext.window.Window', {
			  		title : '配置参数',
			  		autoScroll : true,
			  		modal : true,
			  		resizable : false,
			  		closeAction : 'hide',
			  		width : contentPanel.getWidth(),
			  		height : contentPanel.getHeight (),
			  		items:[choseAgentWrapper_for_config_params],
			  		buttonAlign: 'center',
			  		buttons: [{ 
			  			xtype: "button", 
			  			text: "确定", 
			  			handler: function () {
			  				//
			  				
			  				this.up("window").close();
			  			}
			  		}]
			  	});
			}
			agentParamsWin.show();
//			agent_store_chosed.removeAll();
			pageBarForAgentChosedToSetParamsGrid.moveFirst();

		}
	});

	 chosedServList = Ext.create('Ext.grid.Panel', {
		 title: parent.actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM=='dbcheckForExec'?'已选服务器':'选择服务器', 
			width: '60%',
			height: contentPanel.getHeight()-200,
			autoScroll : true,
		    multiSelect: true,
		    split : true,
		    columnLines : true,
		    store : agent_store,
		    dockedItems : [{
				xtype : 'toolbar',
				dock : 'top',
				padding:'5',
				items : [ sys_name, app_name, agent_ip, host_name, os_type]
			},{
				xtype : 'toolbar',
				dock : 'top',
				padding:'5',
				items : [ resourceGroupObj,choseDbSourceButton,parmsButton,{
					xtype : 'button',
					cls : 'Blue_button',
					text : '查询',
					handler : function(){
						pageBar.moveFirst();
					}
				},
					{
						xtype : 'button',
						cls : 'Blue_button',
						text : '清空',
						handler : function(){
							agent_ip.setValue(''),
					    	app_name.setValue(''),
							sys_name.setValue(''),
							host_name.setValue(''),
							os_type.setValue(''),
					    	resourceGroupObj.setValue('')
						}
					},	{
						xtype : 'button',
						cls : 'Blue_button',
						text : '查看已选择服务器',
						handler : function(){
							if(!chosedAgentWinForSee) {
								chosedAgentWinForSee = Ext.create('Ext.window.Window', {
							  		title : '已选择服务器',
							  		autoScroll : true,
							  		modal : true,
							  		resizable : false,
							  		closeAction : 'hide',
							  		width : contentPanel.getWidth()-250,
							  		height : 530,
							  		items:[agent_grid_chosedForSee],
							  		buttonAlign: 'center',
							  		buttons: [{ 
							  			xtype: "button", 
							  			text: "关闭", 
							  			handler: function () {
							  				this.up("window").close();
							  			}
							  		}]
							  	});
							}
							chosedAgentWinForSee.show();
							agent_store_chosedForSee.load();
						}
					}]
			}],
		    selModel: selModel,
		    plugins : [ cellEditing ],
		    listeners: {
		    	select: function(t, record, index, eOpts) {
		    		if(!finalChosedAgents.contains(record.get('iid'))) {
		    			finalChosedAgents.push(record.get('iid'));
		    		}
		    		if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
	            		chosedAgentIds.push(record.get('iid'));
	            	}
		    	},
				 deselect: function(t, record, index, eOpts) {
					 finalChosedAgents.remove(record.get('iid'));
					 if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
		            		chosedAgentIds.remove(record.get('iid'));
		            	}
				 }
		    },
		    columns : [{ text: '序号', xtype:'rownumberer', width: 40 },
                       { text: '主键',  dataIndex: 'iid',hidden:true},
                       { text: 'IP',  dataIndex: 'agentIp',width:120},
                       { text: '端口号',  dataIndex: 'agentPort',width:100},
                       { text: '资源组',  dataIndex: 'resGroup',width:100,hidden:true},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1},
		                {
 							dataIndex : 'agentParam',
 							text : '参数',
 							hidden: true,
 							editor : {
 								allowBlank : true,
 								listeners: {
 									blur: function(me, The, eOpts){
 							            var plugin = chosedServList.findPlugin('cellediting');
 										editingChosedAgentsAndParams[plugin.context.record.get('iid')] = me.getValue();
 									}
 								}
 							},
 							flex : true
 						},
 						{
 							dataIndex : 'agentStartUser',
 							text : '启动用户',
 							editor : {
 								allowBlank : true,
 								listeners: {
 									blur: function(me, The, eOpts){
 										var plugin = chosedServList.findPlugin('cellediting');
 										editingChosedAgentsAndStartUsers[plugin.context.record.get('iid')] = me.getValue();
 									}
 								}
 							},
 							flex : true
 						},
		                { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ],
		               bbar : pageBar
		  });
	 var pageBarForAgentChosedGrid = Ext.create('Ext.PagingToolbar', {
	    	store : agent_store_chosedForSee,
	    	dock : 'bottom',
	    	displayInfo : true
	    });
	 var agent_grid_chosedForSee = Ext.create('Ext.grid.Panel', {
	    	store:agent_store_chosedForSee,
	    	border:true,
	    	columnLines : true,
	    	height: 450,
	    	emptyText: '没有选择服务器',
	    	columns:[/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
                  { text: '主键',  dataIndex: 'iid',hidden:true},
                  { text: '系统名称',  dataIndex: 'sysName',width:160},
                  { text: 'IP',  dataIndex: 'agentIp',width:110},
                  { text: '主机名称',  dataIndex: 'hostName',width:150},
                  { text: '系统类型',  dataIndex: 'osType',width:110},
                  { text: '端口号',  dataIndex: 'agentPort',width:60},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,
                  	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                          metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                          return value;  
                      }
                  },
		                { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ],
	    	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    	bbar : pageBarForAgentChosedGrid,
	    	dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				items : [ {
					xtype : 'button',
					cls : 'Blue_button',
					text : '删除',
					handler : function() {
						var records = agent_grid_chosedForSee.getSelectionModel().getSelection();
						if(records.length>0) {
		  					for(var i = 0, len = records.length; i < len; i++){
		  						chosedAgentIds.remove(records[i].get('iid'));
		  						finalChosedAgents.remove(records[i].get('iid'));
		  					}
		  					pageBarForAgentChosedGrid.moveFirst();
		  					pageBar.moveFirst();
		  				} else {
		  					Ext.Msg.alert('提示', "请选择服务器！");
	                        return;
		  				}
					}
				} ]
			}]
	    });

	var scriptContentformPanel = Ext.create ('Ext.form.Panel',
	{
		border : false,
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        width : contentPanel.getWidth () /2 - 20,
	        labelWidth : 90
	    },
	    width: '100%',
	    items : [scriptContentObj]
	});
	var scriptDetailPanel = Ext.create('Ext.panel.Panel',{
//        width : contentPanel.getWidth(),
        border : false,
        layout: {
            type: 'hbox',
            padding:'5',
            align:'stretch'
        },
        items : [chosedServList, paramGrid]
	});
	
    // 主Panel
    var MainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "flowCustomizedScriptWindowDivGFSSFLOWCUSTOMEDITPRODUCTFROMTM",
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight (),
		autoScroll: true,
		border : false,
		bodyPadding : 5,
		items : [ formPanel,scriptDetailPanel/*,scriptContentformPanel*/]
	});
	/** 初始化方法* */
	function initFun ()
	{
		if(parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId==null || typeof (parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId) == "undefined") {
			scriptServiceId = iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
		} else {
			scriptServiceId = parseInt(parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.scriptId);
			if(isNaN(scriptServiceId)) {
				scriptServiceId = -1;
			}
		}
		
		if(actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'].hasOwnProperty(parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid)) {
			var _actStartInfo = actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid];
			
			stepNameObj.setValue(_actStartInfo['actName']);
			shutdownCheckboxObj.setValue(_actStartInfo['isShutdown']);
			
			if(_actStartInfo.hasOwnProperty('eachNum')) {
				eachNum.setValue(_actStartInfo['eachNum']);
			}
			if(_actStartInfo.hasOwnProperty('chosedAgentIds')) {
				chosedAgentIds = _actStartInfo['chosedAgentIds'];
			}
			
			if(_actStartInfo.hasOwnProperty('chosedResGroups')) {
				chosedResGroups = _actStartInfo['chosedResGroups'];
				resourceGroupObj.setValue(chosedResGroups);
			} else {
				chosedResGroups = new Array();
				resourceGroupObj.setValue('');
			}
			
			resourceGroupObj.fireEvent ('select');
			
			if(_actStartInfo.hasOwnProperty('globalParams')) {
				globalParams = _actStartInfo['globalParams'];
			}
			
			if(_actStartInfo.hasOwnProperty('globalConfigParams')) {
				globalConfigParams = _actStartInfo['globalConfigParams'];
			}
			
			if(_actStartInfo.hasOwnProperty('globalStartUser')) {
				startUserObj.setValue(_actStartInfo['globalStartUser']);
			}
			
			if(_actStartInfo.hasOwnProperty('globalConfigStartUser')) {
				globalConfigStartUser = _actStartInfo['globalConfigStartUser'];
			}
			
			if (scriptServiceId>0) {
				paramStore.load();
				configParamStore.load();
				Ext.Ajax.request ({
					url : 'getScritContentScriptService.do',
					method : 'POST',
					params :
					{
						iid : scriptServiceId,
						from: 2,
						flag: "0"
					},
					success : function (response, options)
					{
						var content= Ext.decode (response.responseText).content;
						var serviceName= Ext.decode (response.responseText).serviceName;
						scriptContentObj.setValue(content);
						scriptNameObj.setValue(serviceName);
					},
					failure : function(result, request) {
						scriptContentObj.setValue(''); 
						scriptNameObj.setValue("");
					}
				});
			}
		}
	}
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
	function saveFun ()
	{
		var en = 0;
		if(isScriptConvertToFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM) {
			en = eachNum.getValue();
			if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
				if(en>concurrentNumber) {
					Ext.MessageBox.alert ("提示", '并发数量不能超过'+concurrentNumber);
					return;
				} else if(en==0) {
					Ext.MessageBox.alert ("提示", '并发数量不能为0');
					return;
				}
			} else {
				Ext.MessageBox.alert ("提示", '并发数量不合法！');
				return;
			}
			
			if(Ext.isEmpty(en)) {
				en = 0;
			}
		}
		
		if (stepNameObj.getValue ().trim () == '')
		{
			Ext.Msg.alert ('提示', '步骤名称不允许为空!');
			return null;
		}
		if ('开始' == stepNameObj.getValue ().trim ())
		{
			Ext.Msg.alert ('提示', '步骤名称不可以为<开始>！');
			return null;
		}
		if ('结束' == stepNameObj.getValue ().trim ())
		{
			Ext.Msg.alert ('提示', '步骤名称不可以为<结束>！');
			return null;
		}
		
		if(chosedAgentIds.length<1) {
			Ext.MessageBox.alert ("提示", "请选择服务器!");
			return null;
		}
		
		var m = paramStore.getRange(0, paramStore.getCount()-1);   
        for (var i = 0, len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';

            if (paramType == 'int'&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                	Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                    return;
                }
            }
            if (paramType == 'float'&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                	Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"')>=0) {
            	if(cellScriptType=='bat') {
            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
            	}
            }
            if(!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
            	globalParams[m[i].get('iid')] = paramDefaultValue;
            }
        }
        
        var closedAgents = chosedServList.getSelectionModel().getSelection();
        $.each(closedAgents, function(index, closedAgent) {
        	if(!Ext.isEmpty(Ext.util.Format.trim(closedAgent.data.agentStartUser))) {
        		globalConfigStartUser[closedAgent.data.iid] = closedAgent.data.agentStartUser;
            }
        });
		
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.chosedResGroups=chosedResGroups;
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.chosedAgentIds=chosedAgentIds;
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.globalParams=globalParams;
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.globalConfigParams=globalConfigParams;
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.globalStartUser=Ext.util.Format.trim(startUserObj.getValue());
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.globalConfigStartUser=globalConfigStartUser;
		
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.value = stepNameObj.getValue();
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isShutdown = shutdownCheckboxObj.getValue();
		parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.eachNum = en;
		parent.callbackWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM ();
		Ext.Msg.alert ('提示', '当前步骤保存成功!');
//		parent.configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM.close ();
		
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['chosedResGroups'] = chosedResGroups;
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['chosedAgentIds'] = chosedAgentIds;
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['globalParams'] = globalParams;
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['globalConfigParams'] = globalConfigParams;
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['globalStartUser'] = Ext.util.Format.trim(startUserObj.getValue());
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['globalConfigStartUser'] = globalConfigStartUser;
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['isShutdown'] = shutdownCheckboxObj.getValue();
		actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.mxIid]['eachNum'] = en;
	}
	initFun ();
	
	/**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
	function getCellFun (inflag)
	{
		// 遍历所有节点
		var rootObj = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getRoot ();
		var count = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getChildCount (rootObj);
		for (var i = 0; i < count; i++)
		{
			var cells = rootObj.getChildAt (i);
			var counts = cells.getChildCount ();
			var beforeCell = null;// 上一个节点
			var afterCell = null;// 下一个节点
			var selfCell = null;// 自己
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				// 判断循环至的节点样式是否与传入的样式一致
				if (cellss.style == parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.style)
				{
					if (cellss == parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM)
					{
						// 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
						selfCell = parent.cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
					}
					else
					{
						// 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
						selfCell == null ? beforeCell = cellss : afterCell = cellss;
					}
					// 如果获取到了想要的节点，则跳出循环
					if (selfCell != null && ((inflag == 'after' && afterCell != null)
					        || (inflag == 'before' && beforeCell != null)))
					{
						break;
					}
				}
			}
			// 返回指定节点
			return inflag == 'after' ? afterCell : beforeCell;
		}
	}
});
