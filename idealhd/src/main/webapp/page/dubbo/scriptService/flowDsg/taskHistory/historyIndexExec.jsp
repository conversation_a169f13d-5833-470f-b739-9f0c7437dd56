<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean scriptNameSwitch = Environment.getInstance().getScriptNameSwitch();
    boolean scriptExeUserSwitch = Environment.getInstance().getScriptExecUserSwitch ();
    boolean isHideCustom = Environment.getInstance().getScriptHideCustomSwitch();
%>
<html>
<head>

 <script type="text/javascript">
 <%--var flowTypeExec=<%=request.getAttribute("flowType")%>==null?1:<%=request.getAttribute("flowType")%>;
     var cataExec=<%=request.getParameter("cata")%>==null?1:<%=request.getParameter("cata")%>;--%>
    var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_state  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTime ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTime = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	var filter_serviceTaskName ='<%=request.getParameter("filter_serviceTaskName")==null?"":request.getParameter("filter_serviceTaskName")%>';
	var filter_audiUser ='<%=request.getParameter("filter_audiUser")==null?"":request.getParameter("filter_audiUser")%>';
	var iServiceId ='<%=request.getParameter("iServiceId")==null?"":request.getParameter("iServiceId")%>';
	var scriptNameSwitchForHis = <%=scriptNameSwitch%>;
	var scriptExeUserSwitch = <%=scriptExeUserSwitch%>;
	var isHideCustomSwitch = <%=isHideCustom%>;
</script> 
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/taskHistory/historyIndexExec.js"></script>
</head>
<body>
<div id="scriptflowmonitor_areaindexExec" style="width: 100%;height: 100%">
</div>
</body>
</html>