/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.btn.baseInfoBtn', {
    extend: 'Ext.button.Button',
    alias: 'widget.baseInfoBtn',

    text: '基本信息',

    serviceInfo:{},
    
    handler: function(button, e) {
    	
    	Ext.create('Ext.window.Window', {
            title: '基本信息',
            autoScroll: true,
            modal: true,
            closeAction: 'destroy',
            buttonAlign: 'center',
            draggable: true,
            resizable: false,
            width: 500,
            height: 328,
            loader: {
            	url: 'page/dubbo/fragment/_basicInfo.jsp',
            	params: {
            		creatorFullName: 	this.serviceInfo.creatorFullName,
	                bussName: 			this.serviceInfo.bussName,
	                bussTypeName:		this.serviceInfo.bussTypeName,
	                funcDescText: 		this.serviceInfo.funcDescText,
	                serviceName:		this.serviceInfo.serviceName
            	},
            	autoLoad: true
            },
            dockedItems: [{
                xtype: 'toolbar',
                border: false,
                dock: 'bottom',
                margin: '0 0 5 0',
                layout: {pack: 'center'},
                items: [{
                    xtype: 'button',
                    text: '关闭',
                    cls: 'Common_Btn',
                    handler: function() {
                        this.up("window").close();
                    }
                }]
            }]
        }).show();
    },

    initComponent: function() {
        var me = this;
        me.callParent(arguments);
    }

});