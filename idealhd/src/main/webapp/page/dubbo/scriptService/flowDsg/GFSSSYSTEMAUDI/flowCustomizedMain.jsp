<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSSYSTEMAUDI=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSSYSTEMAUDI=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSSYSTEMAUDI=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSSYSTEMAUDI=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSSYSTEMAUDI=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSSYSTEMAUDI=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSSYSTEMAUDI = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSSYSTEMAUDI='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSSYSTEMAUDI='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSSYSTEMAUDI=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSSYSTEMAUDI=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSSYSTEMAUDI='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSSYSTEMAUDI='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSSYSTEMAUDI = <%=request.getAttribute("fromType")%>;
var workItemidGFSSSYSTEMAUDI = <%=request.getAttribute("workItemid")%>;
var fromGFSSSYSTEMAUDI = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSSYSTEMAUDI = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSSYSTEMAUDI = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSSYSTEMAUDI = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSSYSTEMAUDI = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSSYSTEMAUDI=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSSYSTEMAUDI=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSSYSTEMAUDI='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSSYSTEMAUDI='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSSYSTEMAUDI='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSSYSTEMAUDI=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSSYSTEMAUDI=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSSYSTEMAUDI=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSSYSTEMAUDI='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSSYSTEMAUDI='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSSYSTEMAUDI='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSSYSTEMAUDI = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSSYSTEMAUDI = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSSYSTEMAUDI = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSSYSTEMAUDI = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSSYSTEMAUDI = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSSYSTEMAUDI = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSSYSTEMAUDI/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSSYSTEMAUDI" style="width: 100%;height: 100%"></div>
</body>
</html>