var contentPanel;
var execTabsPanel;
var execActivTabs;
var treePanel;
var refreshObj; // 任务执行一级监控使用
var refreshTabObj; // 任务执行tab标签监控使用
var refreshObjShellOutput; // 任务执行最后一级监控使用
var lbjilu='';
var refreshTryForBasic;//创建脚本，控制台使用
var refreshTryForUpdate;//脚本管理，编辑时使用
 var refreshTryForVersion;//脚本管理，版本查看使用

var refreshObjForExec; // 任务执行历史最后一级监控使用
var refreshObjShellOutputForExec; // 任务执行历史最后一级监控使用
var refreshObjForExecWhite; // 白名单执行历史最后一级监控使用
var refreshObjShellOutputForExecWhite; // 白名单执行历史最后一级监控使用
var refreshObjForTestExec; // 测试历史最后一级监控使用
var refreshObjShellOutputForTestExec; // 测试历史最后一级监控使用

var refreshObjForTestExecGraph; // 测试历史最后一级监控使用
var refreshObjShellOutputForTestExecGraph; // 测试历史最后一级监控使用

var refreshObjForExecGraph; // 任务执行历史最后一级监控使用
var refreshObjShellOutputForExecGraph; // 任务执行历史最后一级监控使用

var refreshObjForTestExecMonitorSingle; // 作业测试历史监控使用
var refreshObjShellOutputForTestExecMonitorSingle; // 作业测试历史监控使用
var refreshObjForToolMonitor; // 脚本服务化，工具监控最后一级监控使用
var refreshObjShellOutputForToolMonitor; //脚本服务化， 工具监控最后一级监控使用
var refreshObjForExecForFlow; // 脚本服务化，任务执行历史最后一级监控使用
var refreshObjForTestExecForFlow; // 脚本服务化，测试历史最后一级监控使用
var refreshObjShellOutputForTestExecForFlow; // 脚本服务化，测试历史最后一级监控使用
var refreshObjShellOutputForExecForFlow; // 脚本服务化，任务执行历史最后一级监控使用



var refreshObjForGraph;
var refreshObjForGraphExec;
var refreshObjForGraphFile;

var staticRecord;
var jiediantree = -1;
var hdGraphRecord;
var lastLiObj;
var overview;
// grid-rowExp上次记录
var lastRowIndex = -1;// 任务执行最后选择行号
var lastrequestId = -1;// 任务执行最后选择行记录reqId
var lastiip;// 任务执行最后选择行ip
var lastiport;// 任务执行最后选择行port
var interV = 5;// 任务执行rowExpl内部刷新时间
var interPV = 20;// 任务执行grid表格刷新
var interExecPV = 30;// 任务执行整个页面刷新

var auditingWin;

var modelHeigth = 50;
var commonMask;
var treePanelSmall;
var refreshTopoTrank;
var refreshTopoGanttShow;
var refreshTopoShow;
var refreshTopoGanttBussShow;
var refreshTopoGanttTranckShow;

var GRAPHS = {}; // 保存多个mxgraph对象
var currentEDITOR;// 标识当前mxgraph对象
var actStartInfo = {};

Ext.Loader.setConfig({
	enabled : true,
	disableCaching : false,
	paths : {
		'Go' : 'js/ux/gooo'
	}
});
var tsSysAct = [{"projectName":"ECAS_核心业务系统","acts":[{"actName":"ECAS_F110_010_SH"},{"actName":"ECAS_F220_010_SH"},{"actName":"ECAS_F240_010_SH"}]},
    {"projectName":"PLMS_个贷流程系统","acts":[{"actName":"PLMS_F040_010_SH"}]}];
var warnShow_window;
var warnMoreShow_window;
var menuOnclickSmall;
var scrollMenu;
var scrollSmallMenu;
var menuHide;

Ext.onReady(function() {
	//统一调整按钮下拉菜单去掉竖线
	Ext.override(Ext.menu.Menu, {
		plain : true
	});
	Ext.define('ActionTextColumn', {
	    extend: 'Ext.grid.column.Action',
	    alias: ['widget.actiontextcolumn'],
	    
	    defaultRenderer: function(v, meta, record, rowIdx, colIdx, store, view){
	        var me = this,
	            prefix = Ext.baseCSSPrefix,
	            scope = me.origScope || me,
	            items = me.items,
	            len = items.length,
	            i = 0,
	            item, ret, disabled, tooltip;
	        	ret = Ext.isFunction(me.origRenderer) ? me.origRenderer.apply(scope, arguments) || '' : '';
	        	meta.tdCls += ' ' + Ext.baseCSSPrefix + 'action-col-cell';
	        	
	        for (; i < len; i++) {
	            item = items[i];
	            disabled = item.disabled || (item.isDisabled ? item.isDisabled.call(item.scope || scope, view, rowIdx, colIdx, item, record) : false);
	            tooltip = disabled ? null : (item.tooltip || (item.getTip ? item.getTip.apply(item.scope || scope, arguments) : null));
	            if (!item.hasActionConfiguration) {
	                item.stopSelection = me.stopSelection;
	                item.disable = Ext.Function.bind(me.disableAction, me, [i], 0);
	                item.enable = Ext.Function.bind(me.enableAction, me, [i], 0);
	                item.hasActionConfiguration = true;
	            }
            	ret += '<a href="javascript:void(0);"' + 
            	' style="margin:0 15px 0 0" class="' + Ext.baseCSSPrefix + 'action-col-icon ' + Ext.baseCSSPrefix + 'action-col-' + String(i) + ' ' + (item.disabled ? Ext.baseCSSPrefix + 'item-disabled' : ' ') + (item.cls || '') +
            	' ' + (Ext.isFunction(item.getClass) ? item.getClass.apply(item.scope||me.scope||me, arguments) : (me.iconCls || '')) + '"' +
            	((item.tooltip) ? ' data-qtip="' + item.tooltip + '"' : '') + '><img src="images/monitor_bg.png" align="absmiddle" class="'+(item.iconCls || me.iconCls || '')+'"></img>' + (item.text || me.text) + '</a>';
	        }
	        return ret;    
	    }
	});

	Ext.tip.QuickTipManager.init();

	contentPanel = Ext.create('Ext.panel.Panel', {
		id : 'main-content-panel',
		title : '',
		region : 'center',
		header : {
			border : false,
			style : {
				background: 'none'
			},
			//icon : 'images/cmdb/small/01_00icon_business_16x16.png'
			iconCls:'head_icon'
		},
		activeItem : 0,
		bodyCls : 'x-docked-noborder-top',
		border : false,
		loader : {
//			url : 'historyForwardscriptcoatforexec.do',//'pandectnew.do','pandect1.do','forwardScriptLibrary.do'
			url : 'historyScriptMonitorForFlowProduct.do',//'pandectnew.do','pandect1.do','forwardScriptLibrary.do'
			params : {
				iServiceId : iServiceId
			},
			autoLoad : true,
			scripts : true
		}
	});
	
	commonMask = new Ext.LoadMask(Ext.getBody(), {msg:"操作中..."});

	Ext.define('treemodel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'string'
		}, {
			name : 'text',
			type : 'string'
		}, {
			name : 'url',
			type : 'string'
		}, {
			name : 'leaf',
			type : 'boolean'
		}, {
			name : 'groupId',
			type : 'string'
		} ]
	});

	var store = Ext.create('Ext.data.TreeStore', {
		model : 'treemodel',
		root : {
			expanded : true
		},
		proxy : {
			type : 'ajax',
			url : url
		}
	});

	treePanel = Ext.create('Ext.panel.Panel', {
		id : 'main-tree-panel',
		region : 'center',
		layout: {
	        type: 'accordion',
	        itemCls : [Ext.baseCSSPrefix + 'box-item','customize-accordion-item'],
	        fill : false
	    },
		//split : true,
		border : false,
		autoScroll : true,
		//minSize : 150,
		bodyCls:'tree_panelmain tree_scroll',
		items: []
	});
	
	var westPanelSmall = Ext.create('Ext.panel.Panel', {
		region : 'north',
		border : false,
		height : 68,
		html : '<div class="extend"><a href="javascript:void(0)" style="color:#b5b5b6; text-decoration:none" onclick="forword(\''+welcomePageUrl+'\',\'\')"><div class="tp_logo"></div></a></div>'
	})
	
	treePanelSmall = Ext.create('Ext.toolbar.Toolbar', {
		region : 'center',
		cls:'tree_panel',
		vertical : true,
		items: []  
	});
	
	var westPanelSmallMain = Ext.create('Ext.panel.Panel', {
		layout : 'border',
		border : false,
		width : 65,
		items : [westPanelSmall,treePanelSmall]
	})
	
	
	store.on("load",function( obj, node, records, successful, eOpts ){
		//创建图表菜单
		if(node.childNodes.length>0){
			//lbChange190628
			$("#main-layout-browser-body").css('left','0px')
			$("#main-layout-browser-body").css('top','0px')
			$("#main-layout-browser-body").append("<div class='lbIconsMenuText' style='width:130px;height:100%;background:black;display:none;position:fixed;top:"+getMainMenuPosition('main-layout-browser-body','top')+";left:"+getMainMenuPosition('main-layout-browser-body','right')+";padding-bottom:80px;z-index:999;'></div>");
			var lbMenuHeight=getMainMenuPosition('main-layout-browser-body','top');
			for(var lbi=0;lbi<node.childNodes.length;lbi++){
				(function(){
					var InlbIconsMenuIcon=node.childNodes[lbi].data.icon;
					$("#main-layout-browser-body").css('background','black').css('position','relative').css('zIndex','999').append("<div class='lbIconsMenu' style='width:100%;height:45px;background: url("+node.childNodes[lbi].data.icon+") center center no-repeat'></div>");
				    $(".lbIconsMenu").eq(lbi).hover(function(lbi){
				    	$('.lbIconsMenuText').css('left',getMainMenuPosition('main-layout-browser-body','right'));
				    	
				    	$('.lbIconsMenuText').css('display','block');
				    	$(this).attr("style",'width:100%;height:45px;background:#007bf5 url('+InlbIconsMenuIcon+') center center no-repeat;').css('transition','0.1s');
				    	$(this).css('cursor','pointer');
				    	$(this).siblings('.lbIconsMenuText').children('.lbIconsMenuTextChild').eq($(this).index()-1).css('background','#007bf5').css('transition','0.1s');
				    },function(){
				    	$(this).attr("style",'width:100%;height:45px;background:url('+InlbIconsMenuIcon+') center center no-repeat;').css('transition','0.1s');
				    	$(this).siblings('.lbIconsMenuText').children('.lbIconsMenuTextChild').eq($(this).index()-1).css('background','#000').css('transition','0.1s');
				    });
				    $(".lbIconsMenuText").hover(function(){
				    	$(".lbIconsMenuText").css('left',getMainMenuPosition('main-layout-browser-body','right'));
				    	$('.lbIconsMenuText').css('top',getMainMenuPosition('main-layout-browser-body','top'));
				    },function(){
				    	$('.lbIconsMenuText').css('display','none');
				    });
				    
				    $(".lbIconsMenuText").append('<div class="lbIconsMenuTextChild" style="padding-left: 16px;line-height:45px;color:rgb(239, 240, 240)">'+node.childNodes[lbi].data.text+'</div>');
				})();
				//创建一级菜单
				if(node.childNodes[lbi].childNodes.length>0){
					var lbMenu2Height=$("#main-layout-browser").height();
					$(".lbIconsMenuTextChild:eq("+lbi+")").append("<div class='lbIconsMenuOneFa' style='padding-bottom:0px;width:200px;height:"+lbMenu2Height+"px;overflow:auto;background:rgba(255, 255, 255, 0.94);color:#777;position:fixed;top:"+getMainMenuPosition('main-layout-browser-body','top')+";left:"+getMainMenuPosition('lbIconsMenuText','right')+";z-index:2;'></div>");
					$(".lbIconsMenuTextChild:eq("+lbi+")").children('.lbIconsMenuOneFa').append('<div class="lbSearch" style="    padding-left: 16px;width:100%;height:50px;box-sizing:border-box;"><svg class="icon" style="color:#969696;width: 25px; height: 32px;vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1921"><path d="M460.132702 63.620993c-217.467917 0-395.984706 178.508603-395.984706 395.980613 0 217.48122 178.51679 395.984706 395.984706 395.984706 217.4628 0 395.980613-178.503487 395.980613-395.984706 0-217.47201-180.744527-395.980613-398.230863-395.980613l0 0L460.132702 63.620993zM689.300079 688.773076c-61.130266 61.143569-142.523017 94.815459-229.17147 94.815459-86.657662 0-168.05553-33.67189-229.180679-94.815459-61.126172-61.121056-94.802156-142.518924-94.802156-229.17147s33.663704-168.041204 94.802156-229.180679c61.126172-61.134359 142.523017-94.802156 229.180679-94.802156 86.648452 0 168.041204 33.667797 229.17147 94.802156 61.139475 61.139475 94.824669 142.527111 94.824669 229.180679S750.439554 627.638717 689.300079 688.773076L689.300079 688.773076 689.300079 688.773076zM947.05556 889.569288l-94.955652-96.215342c-14.610766-17.266245-41.398876-17.266245-58.434877 0-17.041118 14.800078-17.041118 41.943274 0 59.217706l94.964862 96.192829c14.59337 14.82259 41.380456 14.82259 58.444087 0C964.114075 933.986917 964.114075 906.830417 947.05556 889.569288L947.05556 889.569288 947.05556 889.569288z" p-id="1922"></path></svg><input id="lbSearchInput"style="color:#777;outline-style: none ;outline-width: 0px ;border: none ;border-style: none ;text-shadow: none ;-webkit-appearance: none ;-webkit-user-select: text ;outline-color: transparent ;box-shadow: none;width:130px;height: 32px;border-bottom: 1px solid #98999a;background:rgba(255, 255, 255, 0);    line-height: 40px;"placeholder=" 请输入关键词"type="text" name="LbSearchName" oninput="lbSearch(this)"/></div>');
					$(".lbIconsMenuTextChild:eq("+lbi+")").children('.lbIconsMenuOneFa').children('.lbSearch').append('<style>.menu1_btn{cursor:pointer;}.lbSearch input{font-family: arial, Microsoft Yahei, helvetica, verdana, sans-serif;}input::-webkit-input-placeholder { /* WebKit browsers */ color: #abafb2; font-family: arial, Microsoft Yahei, helvetica, verdana, sans-serif;} input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */ color: red;font-family: arial, Microsoft Yahei, helvetica, verdana, sans-serif; } input::-moz-placeholder { /* Mozilla Firefox 19+ */ color: #abafb2;font-family: arial, Microsoft Yahei, helvetica, verdana, sans-serif; } input:-ms-input-placeholder { /* Internet Explorer 10+ */ color: #abafb2;font-family: arial, Microsoft Yahei, helvetica, verdana, sans-serif; } </style>');
					$(".lbIconsMenuOneFa").css('display','none');
					for(var lbii=0;lbii<node.childNodes[lbi].childNodes.length;lbii++){
						(function(){
							$(".lbIconsMenuTextChild:eq("+lbi+")").children().append("<div class='lbIconsMenuOne' style='width:100%;padding-left: 16px;color:#777;line-height:45px;height:45px;' dataUrl="+node.childNodes[lbi].childNodes[lbii].data.url+">"+node.childNodes[lbi].childNodes[lbii].data.text+"</div>");
							var nowDataUrl=$(".lbIconsMenuTextChild:eq("+lbi+")").children().children('.lbIconsMenuOne').eq(lbii).attr('dataUrl');
							if(nowDataUrl!='#'){
								if(nowDataUrl=='forwardScriptServiceRelease.do') {
									$(".lbIconsMenuTextChild:eq("+lbi+")").children().children('.lbIconsMenuOne').eq(lbii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].data.id+'")');
									//html = html + "<li id='menu-script-service-manager' onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
								} else if(nowDataUrl=='viewXunJianMonitor.do') {
									$(".lbIconsMenuTextChild:eq("+lbi+")").children().children('.lbIconsMenuOne').eq(lbii).attr('onclick','menuClickForHyperlink(this,"viewXunJianMonitor.do"');
									//html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
									//html = html + "<li onclick=\"menuClickForHyperlink(this,'viewXunJianMonitor.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
								}else if(nowDataUrl=='accessmyBacklog.do') {
									$(".lbIconsMenuTextChild:eq("+lbi+")").children().children('.lbIconsMenuOne').eq(lbii).attr('onclick','menuClickForHyperlink(this,"accessmyBacklog.do"');
									//html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
									//html = html + "<li onclick=\"menuClickForHyperlink(this,'accessmyBacklog.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
								} else {
									if(!node.childNodes[lbi].childNodes[lbii].data.textleaf){
										//var jsonNode = getJsonNode(node.childNodes[lbi].childNodes[lbii]);
										$(".lbIconsMenuTextChild:eq("+lbi+")").children().children('.lbIconsMenuOne').eq(lbii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].data.id+'")');
										
										//html = html + "<li id=\"menu_son_son_li\" onmouseover=\"onItemClick(this)\" nodecls=\"moremenucls\" nodeObj="+jsonNode+" ><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\" style=\"width:'100px';\">"+nodeSonSon.data.text+"</span><span id=\"menuposition"+nodeSonSon.data.id+"\" style=\"float:right;padding-right:20px;\"></span><span id=\"menuposition\" style=\"float:right;padding-right:20px;\"></span></li>";
									}else{
										$(".lbIconsMenuTextChild:eq("+lbi+")").children().children('.lbIconsMenuOne').eq(lbii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].data.id+'")');
										//html = html + "<li onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
									}
								}
							}
						})();
						//创建二级菜单
						if(node.childNodes[lbi].childNodes[lbii].childNodes.length>0){
							$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").append("<svg class='icon' style='width: 1em; height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden; float:right;   margin:14px 6px 0 0px;font-size: 17px;color:#969696;' viewbox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='1461'><path d='M353.743138 127.382179c7.761778 0 15.522532 2.910283 21.598924 8.731872l365.782847 352.429725c6.100952 5.871731 9.573029 14.003945 9.573029 22.428825 0 8.463766-3.473101 16.594957-9.573029 22.404266L375.343085 885.804545c-12.407588 11.949146-32.117488 11.642155-44.066635-0.816598-11.921517-12.356422-11.56643-32.118512 0.816598-44.00319l342.496491-330.013179L332.093049 181.009564c-12.383028-11.949146-12.739139-31.659047-0.816598-44.078914C337.404008 130.59843 345.574085 127.382179 353.743138 127.382179' p-id='1462'></path></svg>");
							$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").append("<div class='lbIconsMenuOneFa2' style='padding-bottom:80px;width:218px;height:"+lbMenu2Height+"px;overflow:auto;background:rgba(255, 255, 255, 0.94);color:#777;position:fixed;top:"+getMainMenuPosition('main-layout-browser-body','top')+";left:"+(parseInt(getMainMenuPosition('lbIconsMenuOneFa','right'))-18)+"px"+";z-index:2;'></div>");
							$(".lbIconsMenuOneFa2").css('display','none');
							for(var lbiii=0;lbiii<node.childNodes[lbi].childNodes[lbii].childNodes.length;lbiii++){
								$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').append("<div class='lbIconsMenuTwo' style='padding-left: 25px;'dataUrl="+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.url+">"+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.text+"</div>");
								
								var nowDataUrl2=$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').children('.lbIconsMenuTwo').eq(lbiii).attr('dataurl');
								
								if(nowDataUrl2!='#'){
									if(nowDataUrl2=='forwardScriptServiceRelease.do') {
										$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').children('.lbIconsMenuTwo').eq(lbiii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.id+'")');
										//html = html + "<li id='menu-script-service-manager' onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
									} else if(nowDataUrl2=='viewXunJianMonitor.do') {
										$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').children('.lbIconsMenuTwo').eq(lbiii).attr('onclick','menuClickForHyperlink(this,"viewXunJianMonitor.do"');
										//html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
										//html = html + "<li onclick=\"menuClickForHyperlink(this,'viewXunJianMonitor.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
									}else if(nowDataUrl2=='accessmyBacklog.do') {
										$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').children('.lbIconsMenuTwo').eq(lbiii).attr('onclick','menuClickForHyperlink(this,"accessmyBacklog.do"');
										//html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
										//html = html + "<li onclick=\"menuClickForHyperlink(this,'accessmyBacklog.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
									} else {
										if(!node.childNodes[lbi].childNodes[lbii].data.textleaf){
											//var jsonNode = getJsonNode(node.childNodes[lbi].childNodes[lbii]);
											$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').children('.lbIconsMenuTwo').eq(lbiii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.id+'")');
											//html = html + "<li id=\"menu_son_son_li\" onmouseover=\"onItemClick(this)\" nodecls=\"moremenucls\" nodeObj="+jsonNode+" ><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\" style=\"width:'100px';\">"+nodeSonSon.data.text+"</span><span id=\"menuposition"+nodeSonSon.data.id+"\" style=\"float:right;padding-right:20px;\"></span><span id=\"menuposition\" style=\"float:right;padding-right:20px;\"></span></li>";
											
										}else{
											$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").children('.lbIconsMenuOneFa2').children('.lbIconsMenuTwo').eq(lbiii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].data.id+'")');
											//html = html + "<li onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
										}
									}
								}
								
								//创建三级菜单
								if(node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes.length>0){
									$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").append("<div class='lbIconsMenuOneFa3' style='padding-bottom:80px;width:218px;height:"+lbMenu2Height+"px;overflow:auto;background:rgba(255, 255, 255, 0.94);color:#777;position:fixed;top:"+getMainMenuPosition('main-layout-browser-body','top')+";left:"+(parseInt(getMainMenuPosition('lbIconsMenuOneFa2','right'))-18)+"px"+";z-index:2;'></div>");
									$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").append("<svg class='icon' style='width: 1em; height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden; float:right;   margin:14px 6px 0 0px;font-size: 17px;color:#969696;' viewbox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='1461'><path d='M353.743138 127.382179c7.761778 0 15.522532 2.910283 21.598924 8.731872l365.782847 352.429725c6.100952 5.871731 9.573029 14.003945 9.573029 22.428825 0 8.463766-3.473101 16.594957-9.573029 22.404266L375.343085 885.804545c-12.407588 11.949146-32.117488 11.642155-44.066635-0.816598-11.921517-12.356422-11.56643-32.118512 0.816598-44.00319l342.496491-330.013179L332.093049 181.009564c-12.383028-11.949146-12.739139-31.659047-0.816598-44.078914C337.404008 130.59843 345.574085 127.382179 353.743138 127.382179' p-id='1462'></path></svg>");
									$(".lbIconsMenuOneFa3").css('display','none');
									for(var lbiiii=0;lbiiii<node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes.length;lbiiii++){
										$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().append("<div class='lbIconsMenuThree' style='padding-left:25px;'dataUrl='"+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.text+"'>"+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.text+"</div>");
										
										var nowDataUrl3=$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().children('.lbIconsMenuThree').eq(lbiiii).attr('dataUrl');

										if(nowDataUrl3!='#'){
											if(nowDataUrl3=='forwardScriptServiceRelease.do') {
												$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().children('.lbIconsMenuThree').eq(lbiiii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.id+'")');
												//html = html + "<li id='menu-script-service-manager' onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
											} else if(nowDataUrl3=='viewXunJianMonitor.do') {
												$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().children('.lbIconsMenuThree').eq(lbiiii).attr('onclick','menuClickForHyperlink(this,"viewXunJianMonitor.do"');
												//html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
												//html = html + "<li onclick=\"menuClickForHyperlink(this,'viewXunJianMonitor.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
											}else if(nowDataUrl3=='accessmyBacklog.do') {
												$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().children('.lbIconsMenuThree').eq(lbiiii).attr('onclick','menuClickForHyperlink(this,"accessmyBacklog.do"');
												//html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
												//html = html + "<li onclick=\"menuClickForHyperlink(this,'accessmyBacklog.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
											} else {
												if(!node.childNodes[lbi].childNodes[lbii].data.textleaf){
													//var jsonNode = getJsonNode(node.childNodes[lbi].childNodes[lbii]);
												$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().children('.lbIconsMenuThree').eq(lbiiii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.id+'")');
													//html = html + "<li id=\"menu_son_son_li\" onmouseover=\"onItemClick(this)\" nodecls=\"moremenucls\" nodeObj="+jsonNode+" ><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\" style=\"width:'100px';\">"+nodeSonSon.data.text+"</span><span id=\"menuposition"+nodeSonSon.data.id+"\" style=\"float:right;padding-right:20px;\"></span><span id=\"menuposition\" style=\"float:right;padding-right:20px;\"></span></li>";
													
												}else{
													$(".lbIconsMenuTextChild:eq("+lbi+")").find(".lbIconsMenuOne:eq("+lbii+")").find(".lbIconsMenuTwo:eq("+lbiii+")").children().children('.lbIconsMenuThree').eq(lbiiii).attr('onclick','menuClick(this,"'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.text+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.url+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.groupId+'","'+node.childNodes[lbi].childNodes[lbii].childNodes[lbiii].childNodes[lbiiii].data.id+'")');
													//html = html + "<li onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
												}
											}
										}
									}
								}
							}
						}
					}	
				}
			}
		}//图表菜单结束
		
		//添加hover事件
		$(".lbIconsMenuTextChild").hover(function(e){
	    	$(this).css('background','#007bf5').css('cursor','pointer').css('transition','0.1s');
	    	$(this).children('.lbIconsMenuOneFa').css('display','block');
	    	var arr=[];
	    	for(var i=0;i<node.childNodes.length;i++){
	    		arr.push(node.childNodes[i].data.icon);
	    	}
	    	$(this).parent().siblings('.lbIconsMenu').eq($(this).index()).css('background','#007bf5 url("'+arr[$(this).index()]+'") center center no-repeat').css('transition','0.1s');
	    	
	    	
	    },function(){
	    	var arr=[];
	    	for(var i=0;i<node.childNodes.length;i++){
	    		arr.push(node.childNodes[i].data.icon);
	    	}
	    	$(this).css('background','#000').css('transition','0.1s');
	    	$(this).children().css('display','none');
	    	$(this).parent().siblings('.lbIconsMenu').eq($(this).index()).css('background','#000 url("'+arr[$(this).index()]+'") center center no-repeat').css('transition','0.1s');
	    	if($(this).attr("lbClick")=='1'){//已经点击
		    	$(this).parent().siblings('.lbIconsMenu').eq($(this).index()).css('background','#007bf5 url("'+arr[$(this).index()]+'") center center no-repeat').css('transition','0.1s');
		    	//console.log(typeof $(this).attr("lbClick"))
	    	}
	    });
		
		
		$(".lbIconsMenuOne").mouseenter(function(e){
	    	$(this).css('background','#e3f1fb').css('cursor','pointer').css('color','#007bf5');
	    	$('.lbIconsMenuOneFa2').css('display','none');
	    	$(this).children('.lbIconsMenuOneFa2').css('display','block');
	    	
	    	
	    	if($(this).parent()[0].scrollHeight > $(this).parent()[0].clientHeight){
	    		
	    	}else{
	    		$(this).children('.lbIconsMenuOneFa2').css('left',(parseInt($(this).children('.lbIconsMenuOneFa2').css('left'))+18)+'px');
	    	}
	    });
		$(".lbIconsMenuOne").mouseleave(function(e){
			//增加兼容ie11
			if(!!window.ActiveXObject || "ActiveXObject" in window){
				$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777');
		    	//$(this).children('.lbIconsMenuOneFa2').css('display','none');
				if(e.clientX >= 374){//像右滑
					$(this).children('.lbIconsMenuOneFa2').css('display','block');
				}else{//上下滑
					$('.lbIconsMenuOneFa2').css('display','none').css('zIndex','2');
				}
			}else{
				$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777');
		    	$(this).children('.lbIconsMenuOneFa2').css('display','none');
		    	$(this).children('.lbIconsMenuOneFa2').css('zIndex','2');
		    	if($(this).parent()[0].scrollHeight > $(this).parent()[0].clientHeight){
		    		
		    	}else{
		    		
		    		$(this).children('.lbIconsMenuOneFa2').css('left',(parseInt($(this).children('.lbIconsMenuOneFa2').css('left'))-18)+'px');
		    		
		    	}
			}
			
			
	    	
	    });
	    
	    /*$(".lbIconsMenuOne").hover(function(e){
	    	$(this).css('background','#e3f1fb').css('cursor','pointer').css('color','#007bf5').css('transition','0.1s');
	    	$(this).css('background','rgb(227, 241, 251)').css('color','#777');
	    	//增加兼容ie11
			if(!!window.ActiveXObject || "ActiveXObject" in window){
				$('.lbIconsMenuOneFa2').css('display','block');
				$(this).children('.lbIconsMenuOneFa2').css('zIndex','3');
				if($(this).children('.lbIconsMenuOneFa2').length==0){console.log('s')
					console.log($(this).children('.lbIconsMenuOneFa2').length)
					$('.lbIconsMenuOneFa2').css('zIndex','2');
					$('.lbIconsMenuOneFa2').css('display','none');
				}
				var left=parseInt($('.lbIconsMenuOneFa2').css('left'));
				$('.lbIconsMenuOneFa2').css('left',left-18+'px')
				$(this).children('.lbIconsMenuOneFa2').css('display','block');
				$(this).children('.lbIconsMenuOneFa2').css('zIndex','3');
			}else{
				$(this).children('.lbIconsMenuOneFa2').css('display','block');
			}
	    	
	    	if($(this).parent()[0].scrollHeight > $(this).parent()[0].clientHeight){
	    		
	    	}else{
	    		$(this).children('.lbIconsMenuOneFa2').css('left',(parseInt($(this).children('.lbIconsMenuOneFa2').css('left'))+18)+'px');
	    	}
	    },function(e){
	    	$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777').css('transition','0.1s');
	    	//增加兼容ie11
			if(!!window.ActiveXObject || "ActiveXObject" in window){
				//var left=parseInt($('.lbIconsMenuOneFa2').css('left'));
				//$('.lbIconsMenuOneFa2').css('left',left-10+'px')
				//$('.lbIconsMenuOneFa2').css('zIndex','-22');
				
				$('.lbIconsMenuOneFa2').css('zIndex','2');
				if($(this).children('.lbIconsMenuOneFa2').length==0){console.log('s')
					console.log($(this).children('.lbIconsMenuOneFa2').length)
					$('.lbIconsMenuOneFa2').css('zIndex','2');
					$('.lbIconsMenuOneFa2').css('display','none');
				}
				var left=parseInt($('.lbIconsMenuOneFa2').css('left'));
				$('.lbIconsMenuOneFa2').css('left',left+18+'px')
				if($(this).children('.lbIconsMenuOneFa2').length==0){
					$('.lbIconsMenuOneFa2').css('display','none');
				}else{
					$(this).children('.lbIconsMenuOneFa2').css('display','none');
				}
				
				$(this).children('.lbIconsMenuOneFa2').css('display','none');
				
				
			}else{
				$(this).children('.lbIconsMenuOneFa2').css('display','none');
			}
	    	//$(this).children('.lbIconsMenuOneFa2').css('display','none');
	    	if($(this).parent()[0].scrollHeight > $(this).parent()[0].clientHeight){
	    		
	    	}else{
	    		$(this).children('.lbIconsMenuOneFa2').css('left',(parseInt($(this).children('.lbIconsMenuOneFa2').css('left'))-18)+'px');
	    		
	    	}
	    });*/
	    
	    
	    
	    $(".lbIconsMenuTwo").hover(function(e){
	    	$(this).css('background','#e3f1fb').css('cursor','pointer').css('color','#007bf5');
	    	$('.lbIconsMenuOneFa3').css('display','none');
	    	$(this).children('.lbIconsMenuOneFa3').css('display','block');
	    	if($(this).parent()[0].scrollHeight > $(this).parent()[0].clientHeight){
	    		
	    	}else{
	    		$(this).children('.lbIconsMenuOneFa3').css('left',(parseInt($(this).children('.lbIconsMenuOneFa3').css('left'))+18)+'px');
	    	}
	    },function(e){
	    	$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777');
	    	$(this).children('.lbIconsMenuOneFa3').css('display','none');
	    	//增加兼容ie11
			if(!!window.ActiveXObject || "ActiveXObject" in window){
				$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777');
				if(e.clientX >= 374){//像右滑
					$(this).children('.lbIconsMenuOneFa3').css('display','block');
				}else{//上下滑
					$('.lbIconsMenuOneFa3').css('display','none').css('zIndex','2');
				}
			}else{
				$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777');
		    	$(this).children('.lbIconsMenuOneFa3').css('display','none');
		    	$(this).children('.lbIconsMenuOneFa3').css('zIndex','2');
		    	if($(this).parent()[0].scrollHeight > $(this).parent()[0].clientHeight){
		    		
		    	}else{
		    		$(this).children('.lbIconsMenuOneFa3').css('left',(parseInt($(this).children('.lbIconsMenuOneFa3').css('left'))-18)+'px');
		    		
		    	}
			}
	    	
	    });
	    $(".lbIconsMenuThree").hover(function(e){
	    	$(this).css('background','#e3f1fb').css('cursor','pointer').css('color','#007bf5');
	    	$(this).children('.lbIconsMenuOneFa3').css('display','block');
	    },function(){
	    	$(this).css('background','rgba(255, 255, 255, 0)').css('color','#777');
	    	$(this).children('.lbIconsMenuOneFa3').css('display','none');
	    });
	  //选中菜单背景变色
		var lbstyle=[];
		for(var lbi=0;lbi<$(".lbIconsMenu").length;lbi++){
			lbstyle.push($(".lbIconsMenu").eq(lbi).attr('style'));
		}
		
		$(".lbIconsMenuOne").click(function(){
			for(var lbi=0;lbi<lbstyle.length;lbi++){
				$(".lbIconsMenu").eq(lbi).attr('style',lbstyle[lbi])
			}
			var str=$("#main-layout-browser-body").children(".lbIconsMenu").eq($(this).parent().parent().index()).attr('style');
		    $("#main-layout-browser-body").children(".lbIconsMenu").eq($(this).parent().parent().index()).attr('style',str+' #007bf5');
		    for(var i=0;i<$(".lbIconsMenuTextChild").length;i++){
		    	$(".lbIconsMenuTextChild").eq(i).attr("lbClick",'0');
		    }
		    $(this).parent().parent().attr("lbClick",'1');
		    
		});
		
		$(".lbIconsMenuTwo").click(function(){
			for(var lbi=0;lbi<lbstyle.length;lbi++){
				$(".lbIconsMenu").eq(lbi).attr('style',lbstyle[lbi])
			}
			var str=$("#main-layout-browser-body").children(".lbIconsMenu").eq($(this).parent().parent().index()).attr('style');
		    $("#main-layout-browser-body").children(".lbIconsMenu").eq($(this).parent().parent().index()).attr('style',str+' #007bf5');
		});
		
		$(".lbIconsMenuThree").click(function(){
			for(var lbi=0;lbi<lbstyle.length;lbi++){
				$(".lbIconsMenu").eq(lbi).attr('style',lbstyle[lbi])
			}
			var str=$("#main-layout-browser-body").children(".lbIconsMenu").eq($(this).parent().parent().index()).attr('style');
		    $("#main-layout-browser-body").children(".lbIconsMenu").eq($(this).parent().parent().index()).attr('style',str+' #007bf5');	
		});
		//新增加功能
		$("#main-layout-browser-body").mouseenter(function(){
			  if(lbjilu==''){
				  
			  }else{
				  for(var i=0;i<$('.lbIconsMenuTextChild').length;i++){
					  if($('.lbIconsMenuTextChild').eq(i).attr('lbclick')=='1'){
						  $('.lbIconsMenuTextChild').eq(i).parent().css('display','block')
						  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').css('display','block')
						  $('.lbIconsMenuTextChild').eq(i).css('background','#007bf5')
						  for(var ii=0;ii<$('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').length;ii++){
							  if($('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).attr('dataurl')==lbjilu){
								  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).siblings().css('background','rgba(255, 255, 255, 0)').css('color','rgb(119, 119, 119)')
								  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).css('background','rgb(227, 241, 251)').css('color','rgb(0, 123, 245)')
								  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').css('display','block')
								  for(var iii=0;iii<$('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').length;iii++){
									  if($('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').children().eq(iii).attr('dataurl')==lbjilu){
										  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').children().eq(iii).siblings().css('background','rgba(255, 255, 255, 0)').css('color','rgb(119, 119, 119)');
										  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').children().eq(iii).css('background','rgb(227, 241, 251)').css('color','rgb(0, 123, 245)')
									  
									  }
								  }
							  }
							  if($('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.Menu_L_Hover').eq(ii).attr('dataurl')==lbjilu){
								  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).siblings().css('background','rgba(255, 255, 255, 0)').css('color','rgb(119, 119, 119)')

								  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.Menu_L_Hover').eq(ii).css('background','rgb(227, 241, 251)').css('color','rgb(0, 123, 245)')
								  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.Menu_L_Hover').eq(ii).children('.lbIconsMenuOneFa2').css('display','block')
								  for(var jjj=0;jjj<$('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').length;jjj++){
									  if($('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').children().eq(iii).attr('dataurl')==lbjilu){
										  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').children().eq(iii).siblings().css('background','rgba(255, 255, 255, 0)').css('color','rgb(119, 119, 119)');
										  $('.lbIconsMenuTextChild').eq(i).children('.lbIconsMenuOneFa').children('.lbIconsMenuOne').eq(ii).children('.lbIconsMenuOneFa2').children().eq(iii).css('background','rgb(227, 241, 251)').css('color','rgb(0, 123, 245)')
									  
									  }
								  }
							  }
						  }
					  }
				  }
			  }
		});
		
	    //end
		node.eachChild( function(nodeSon){
			var menuButton = Ext.create('Ext.button.Split', {
	        	border : false,
	        	margin : '10 0 10 0',
	        	icon : nodeSon.data.icon,
	        	cls : 'Common_Btn5',
	        	menuAlign : 'tr-tl?',
	        	menu: {
	        		shadow : false,
	        		bodyCls:'menu_item_body',
	        		//bodyStyle: 'background:#6e3998;border-color: #6e3998;',
//	        		border: 1,
//	        		style: {
//	        		    borderColor: '#6e3998',
//	        		    borderStyle: 'solid'
//	        		},
	        		showSeparator : false,
                    items: [],
                    listeners : {
                    	mouseleave : function(menu, e, eOpts){
                    		menu.hide()
                    	}
                    }
                },
                listeners :{
                	mouseover : function(obj, e, eOpts){
                		obj.showMenu();
                	}
                }
	        });
			var html = "<ul class=\"Menu_List\">";
			nodeSon.eachChild(function(nodeSonSon){
				var menuSonButton;
				if(nodeSonSon.data.leaf){
					menuSonButton = {
							icon : nodeSonSon.data.icon,
							text: '<font>'+nodeSonSon.data.text+'</font>',
							margin : '3 0 3 0',
							cls:'menu_font',
							width:200,
							handler : function(){
								menuClickSmall(nodeSonSon.data.text,nodeSonSon.data.url,nodeSonSon.data.groupId,nodeSonSon.data.id);
							}
					};
				} else {
					var smallSonMenu = creatSmallMenu(nodeSonSon);
					menuSonButton = {
							icon : nodeSonSon.data.icon,
							text: '<font>'+nodeSonSon.data.text+'</font>',
							margin : '3 0 3 0',
							cls:'menu_font',
							width:200,
							menu : smallSonMenu
					};
				}
				(menuButton.menu).add(menuSonButton);
				if(nodeSonSon.data.url=='forwardScriptServiceRelease.do') {
					html = html + "<li id='menu-script-service-manager' onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
				} else if(nodeSonSon.data.url=='viewXunJianMonitor.do') {
//					html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
					html = html + "<li onclick=\"menuClickForHyperlink(this,'viewXunJianMonitor.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
				} else if(nodeSonSon.data.url=='accessmyBacklog.do') {
//					html = html + "<li><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><a href='viewXunJianMonitor.do' target='_blank' style='color: #fff;text-decoration: none;'>"+nodeSonSon.data.text+"</a></li>";
					html = html + "<li onclick=\"menuClickForHyperlink(this,'accessmyBacklog.do')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
				}else {
					if(!nodeSonSon.data.leaf){
						var jsonNode = getJsonNode(nodeSonSon);
						html = html + "<li id=\"menu_son_son_li\" onmouseover=\"onItemClick(this)\" nodecls=\"moremenucls\" nodeObj="+jsonNode+" ><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\" style=\"width:'100px';\">"+nodeSonSon.data.text+"</span><span id=\"menuposition"+nodeSonSon.data.id+"\" style=\"float:right;padding-right:20px;\"></span><span id=\"menuposition\" style=\"float:right;padding-right:20px;\"></span></li>";
					}else{
						html = html + "<li onclick=\"menuClick(this,'"+nodeSonSon.data.text+"','"+nodeSonSon.data.url+"','"+nodeSonSon.data.groupId+"','"+nodeSonSon.data.id+"')\"><span class=\"Menu_Span1\" style=\"background-image:url("+nodeSonSon.data.icon+");\"></span><span class=\"Menu_Span2\">"+nodeSonSon.data.text+"</span></li>";
					}
				}
			})
			html = html + "</ul>";
			treePanelSmall.add(menuButton);
			treePanel.add({
                xtype: 'panel',
                title: nodeSon.data.text,
                bodyCls:'tree_panelmain',
                autoScroll : true,
                icon : nodeSon.data.icon,
                html:html
            });
		});
		$(".tree_scroll").niceScroll({autohidemode:true,cursorcolor: "#c6cbd6",cursorborder: "1px solid #c6cbd6"});
	})
	
	var westPanel =Ext.create('Ext.panel.Panel', {
		region : 'north',
		border : false,
		height : 68,
		html : '<div class="unfold"><a href="javascript:void(0)" style="color:#b5b5b6; text-decoration:none" onclick="forword(\''+welcomePageUrl+'\',\'\')"><div class="tp_logo"></div></a></div>'
	})

	Ext.create('Ext.Viewport', {
		layout : 'border',
		items : [ {
			border : false,
			xtype : 'box',
			id : 'main-header',
			region : 'north',
			hidden : true,
			loader : {
				url : 'page/common/top_new.jsp',
				autoLoad : true,
				scripts : true
			},
			height : 50
		},{
			region : 'center',
			layout : 'border',
			border : false,
			bodyCls:'customize_center',
			items : [{
				region : 'west',
				layout : 'border',
				collapsible : true,
				preventHeader:true,
				border:false,
				hidden : true,
				//title : '导航菜单',
				id : 'main-layout-browser',
				placeholder : westPanelSmallMain,
//				split : true,
				width : 65,
				items : []
				//items : [westPanel,treePanel]
			},{
				layout : 'fit',
				region : 'center',
				border :false,
				bodyPadding : 20,
				autoScroll : false,
				items : [ contentPanel ]
			} ]
		}, {
			xtype : 'box',
			id : 'main-bottom',
			border : true,
			hidden : false,
			region : 'south',
			loader : {
				url : 'page/common/bottom.jsp',
				autoLoad : true,
				scripts : true
			},
			height : 0
		} ],
		renderTo : Ext.getBody()
	});
});

/**脚本服务化历史刷新功能使用： 从contentPanel.getLoader().target.html中取出divid **/
var REG_BODY = /<body[^>]*>([\s\S]*)<\/body>/;
function getBody(content){
    var result = REG_BODY.exec(content);
    if(result && result.length === 2)
        return result[1];
    return content;
    
}
function getDivId(html) {
	return $(getBody(html)).attr('id');
}

/** 首页缩进时创建多层菜单的方法 **/
function creatSmallMenu(node){
	var newSmallMenu = Ext.create('Ext.menu.Menu', {
	    width: 200,
	    bodyCls:'menu_item_three',
	    cls : 'menu_item_three'
	});
	
	node.eachChild(function(nodeSon){
		var sonText = nodeSon.data.text;
		var sonUrl = nodeSon.data.url;
		var sonGroupId = nodeSon.data.groupId;
		var sonId = nodeSon.data.id;
		var icon = nodeSon.data.icon;
		var leaf = nodeSon.data.leaf;
		if(leaf){
			newSmallMenu.add({
				icon : icon,
				text : '<font>' + sonText +'</font>',
				cls:'menu_font',
				//text : sonText,
				url : sonUrl,
				titleAlign : 'left',
				//menuAlign : 'tr-tl?',
				groupId : sonGroupId,
				sonId : sonId,
				shadow : false,
				showSeparator : false,
				pressed: false,
		        handler: function(item){
		        	menuSonClick(item,'');
				}
		    });
		} else {
			var newSmallSonMenu = creatSmallMenu(nodeSon);
			newSmallMenu.add({
				icon : icon,
				text : '<font>' + sonText +'</font>',
				cls:'menu_font',
				//text : sonText,
				url : sonUrl,
				titleAlign : 'left',
				//menuAlign : 'tr-tl?',
				groupId : sonGroupId,
				sonId : sonId,
				shadow : false,
				showSeparator : false,
				pressed: false,
				menu : newSmallSonMenu,
				listeners:{  
			       "mouseover":function(menu)  
			       {  
			    	   menu.show();  
			       },
			       "mouseleave":function(menu)  
			       {  
			    	   menu.hide();  
			       } 
			    }     
		    });
		}
		
	});
	
	/** 鼠标移开菜单后，自动隐藏菜单 **/
	newSmallMenu.on("mouseleave",function(e){
		newSmallMenu.hide();
	});
	
	return newSmallMenu;
}

/** 鼠标移动到有三级节点的二级节点上时，显示menu菜单 **/
function onItemClick(e) {
	var nodeObj = $(e).attr("nodeObj");
	//var nodeStr = JSON.parse(nodeObj);
	var node = JSON.parse(nodeObj);
	scrollMenu = Ext.create('Ext.menu.Menu', {
	    width: 200,
	    bodyCls:'menu_item_three',
	    cls : 'menu_item_three'
	});
	var nodeSon = node.children;
	for(var i=0;i < nodeSon.length;i++){
		var sonText = nodeSon[i].text;
		var sonUrl = nodeSon[i].url;
		var sonGroupId = nodeSon[i].groupId;
		var sonId = nodeSon[i].id;
		var icon = nodeSon[i].icon;
		var leaf = nodeSon[i].leaf;
		if(leaf == "true"){
			scrollMenu.add({
				icon : icon,
				text : '<font>' + sonText +'</font>',
				cls:'menu_font',
				//text : sonText,
				url : sonUrl,
				titleAlign : 'left',
				//menuAlign : 'tr-tl?',
				groupId : sonGroupId,
				sonId : sonId,
				shadow : false,
				showSeparator : false,
				pressed: false,
		        handler: function(item){
		        	menuSonClick(item,e);
				}
		    });
		} else {
			var newMenu = createSonMenu(nodeSon[i],e);
			scrollMenu.add({
				icon : icon,
				text : '<font>' + sonText +'</font>',
				cls:'menu_font',
				//text : sonText,
				url : sonUrl,
				titleAlign : 'left',
				//menuAlign : 'tr-tl?',
				groupId : sonGroupId,
				sonId : sonId,
				shadow : false,
				showSeparator : false,
				pressed: false,
				menu : newMenu,
				listeners:{  
			       "mouseover":function(menu)  
			       {  
			    	   menu.show();  
			       },
			       "mouseleave":function(menu)  
			       {  
			    	   menu.hide();  
			       } 
			    }     
		    });
		}
	}
	
	/** 鼠标移开菜单后，自动隐藏菜单 **/
	scrollMenu.on("mouseleave",function(e){
		scrollMenu.hide();
	});
	
    //获取div在body中的绝对位置
    var x1 = $("#main-tree-panel").width();
    var y1 = $('#menuposition' + node.id).offset().top;
    scrollMenu.showAt(x1,y1);
}

function createSonMenu(node,e){
	var newScrollMenu = Ext.create('Ext.menu.Menu', {
	    width: 200,
	    bodyCls:'menu_item_three',
	    cls : 'menu_item_three'
	});
	
	var nodeSon = node.children;
	for(var i=0;i < nodeSon.length;i++){
		var nodeSonSon = nodeSon[i].children;
		for(var j=0;j < nodeSonSon.length;j++){
			var sonText = nodeSonSon[j].text;
			var sonUrl = nodeSonSon[j].url;
			var sonGroupId = nodeSonSon[j].groupId;
			var sonId = nodeSonSon[j].id;
			var icon = nodeSonSon[j].icon;
			var leaf = nodeSonSon[j].leaf;
			if(leaf == "true"){
				newScrollMenu.add({
					icon : icon,
					text : '<font>' + sonText +'</font>',
					cls:'menu_font',
					//text : sonText,
					url : sonUrl,
					titleAlign : 'left',
					//menuAlign : 'tr-tl?',
					groupId : sonGroupId,
					sonId : sonId,
					shadow : false,
					showSeparator : false,
					pressed: false,
			        handler: function(item){
			        	menuSonClick(item,e);
					}
			    });
			} else {
				var newMenu = createSonMenu(nodeSonSon[j],e);
				newScrollMenu.add({
					icon : icon,
					text : '<font>' + sonText +'</font>',
					cls:'menu_font',
					//text : sonText,
					url : sonUrl,
					titleAlign : 'left',
					//menuAlign : 'tr-tl?',
					groupId : sonGroupId,
					sonId : sonId,
					shadow : false,
					showSeparator : false,
					pressed: false,
					//menu : newMenu,
					menu : newMenu,
					listeners:{  
				       "mouseover":function(menu)  
				       {  
				    	   menu.show();  
				       },
				       "mouseleave":function(menu)  
				       {  
				    	   menu.hide();  
				       }  
				    }     
			    });
			}
		}
	}
	/** 鼠标移开菜单后，自动隐藏菜单 **/
	newScrollMenu.on("mouseleave",function(e){
		newScrollMenu.hide();
	});
	return newScrollMenu;
}

/** 重新根据node节点封装menu所需的数据，封装成json格式 **/
function getJsonNode(node){
	var nodeJson = "";
	nodeJson += '{';
	nodeJson += '"text":' +'"'+node.data.text+'",';
	nodeJson += '"url":' +'"'+node.data.url+'",';
	nodeJson += '"groupId":' +'"'+node.data.groupId+'",';
	nodeJson += '"id":' +'"'+node.data.id+'",';
	nodeJson += '"icon":' +'"'+node.data.icon+'",';
	nodeJson += '"leaf":' +'"'+node.data.leaf+'",';
	nodeJson += '"children":[';
	node.eachChild(function(nodeSon){
		nodeJson += '{';
		nodeJson += '"text":' +'"'+nodeSon.data.text+'",';
		nodeJson += '"url":' +'"'+nodeSon.data.url+'",';
		nodeJson += '"groupId":' +'"'+nodeSon.data.groupId+'",';
		nodeJson += '"id":' +'"'+nodeSon.data.id+'",';
		nodeJson += '"icon":' +'"'+nodeSon.data.icon+'",';
		nodeJson += '"leaf":' +'"'+nodeSon.data.leaf+'"';
		if(!nodeSon.data.leaf){
			nodeJson += ',';
			nodeJson += '"children":[';
			nodeJson += getJsonNode(nodeSon);
			nodeJson += ']';
		}
		nodeJson += '},';
	});
	nodeJson = nodeJson.substr(0,nodeJson.length-1);
	nodeJson += ']}';
	return nodeJson;
}

/** 生成点击菜单所需参数，并传给点击菜单跳转生成页面的方法，点击后，隐藏菜单 **/
function menuSonClick(item,e){
	//alert($(e).attr("onmouseover"));
	menuClickSmall(item.text,item.url,item.groupId,item.sonId,e);
}

function menuSwitch(){
	forword('pandectnew.do','');
	/*if(Ext.getCmp('main-layout-browser').getCollapsed()){
		Ext.getCmp('main-layout-browser').expand(); 
	}else{
		Ext.getCmp('main-layout-browser').collapse();
	}*/
	
}

function destroyRubbish() {
	contentPanel.clearListeners();
	contentPanel.getLoader().clearListeners();
}
function menuBlur(obj){
	lastLiObj.className = "";
}
function menuClickForHyperlink(obj,url) {
	window.open(url);
}
function menuClick(obj,text,url,groupId,id){
	lbjilu=text;
	//console.log(obj+'----------------'+text+'----------------'+url+'----------------'+groupId+'----------------'+id)
	if(lastLiObj){
		lastLiObj.className = "";
	}
	/*if(groupId==7||groupId==8){
		groupId=2;
	}*/
	obj.className = "Menu_L_Hover";
	$("#main-tree-panel").find("li[nodecls='moremenucls']").removeAttr("class");
	lastLiObj = obj;
	if (refreshObj) {
		clearInterval(refreshObj);
	}
	if (refreshTryForBasic) {
		clearInterval(refreshTryForBasic);
	}
	if (refreshTryForUpdate) {
		clearInterval(refreshTryForUpdate);
	}
	if (refreshTryForVersion) {
		clearInterval(refreshTryForVersion);
	}
	if (refreshObjShellOutput) {
		clearInterval(refreshObjShellOutput);
	}
	if (refreshObjForTestExec) {
		clearInterval(refreshObjForTestExec);
	}
	if (refreshObjShellOutputForTestExec) {
		clearInterval(refreshObjShellOutputForTestExec);
	}
	if (refreshObjForExec) {
		clearInterval(refreshObjForExec);
	}
	if (refreshObjForExecWhite) {
		clearInterval(refreshObjForExecWhite);
	}
	if (refreshObjShellOutputForExec) {
		clearInterval(refreshObjShellOutputForExec);
	}
	if (refreshObjShellOutputForExecWhite) {
		clearInterval(refreshObjShellOutputForExecWhite);
	}
	if(refreshTopoTrank){
		clearInterval(refreshTopoTrank);
	}
	if(refreshTopoGanttShow){
		clearInterval(refreshTopoGanttShow);
	}
	if(refreshTopoShow){
		clearInterval(refreshTopoShow);
	}
	if(refreshTopoGanttBussShow){
		clearInterval(refreshTopoGanttBussShow);
	}
	if(refreshTopoGanttTranckShow){
		clearInterval(refreshTopoGanttTranckShow);
	}
	if (refreshObjForTestExecMonitorSingle) {
		clearInterval(refreshObjForTestExecMonitorSingle);
	}
	if (refreshObjShellOutputForTestExecMonitorSingle) {
		clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
	}
	if (refreshObjForToolMonitor) {
		clearInterval(refreshObjForToolMonitor);
	}
	if (refreshObjShellOutputForToolMonitor) {
		clearInterval(refreshObjShellOutputForToolMonitor);
	}
	if (refreshObjForExecForFlow) {
		clearInterval(refreshObjForExecForFlow);
	}
	if (refreshObjForTestExecForFlow) {
		clearInterval(refreshObjForTestExecForFlow);
	}
	if (refreshObjShellOutputForTestExecForFlow) {
		clearInterval(refreshObjShellOutputForTestExecForFlow);
	}
	if (refreshObjShellOutputForExecForFlow) {
		clearInterval(refreshObjShellOutputForExecForFlow);
	}
	if(url=='drOverview.do'){
		window.open("drOverview.do","_blank")
	}else if(url=='drOverviewNew.do'){
		window.open("drOverviewNew.do","_blank")
	}else if(url=='drOverviewNewFJ.do'){
		window.open("drOverviewNewFJ.do","_blank")
	}else if(url=='ipadStartForward.do'){
		window.open("ipadStartForward.do","_blank")
	}else if(url=='ipadStartForwardFJ.do'){
		window.open("ipadStartForwardFJ.do","_blank")
	}else if(url=='initSwitchMonitorOverview.do'){
		window.open("initSwitchMonitorOverview.do","_blank")
	}else if(url=='newSwitchMonitorInit.do'){
		window.open("newSwitchMonitorInit.do","_blank")
	}else if(url.indexOf("topoScreenDisplay.do")>-1 || url.indexOf("topoScreenDisplayFir.do")>-1){
		window.open(url, "_blank");
	}else if(url.indexOf("screenShow.do")>-1 || url.indexOf("screenShow_1440.do")>-1){
		window.open(url, "_blank");
	}else if(url=='proLogicSwitchMonitor.do'){
		window.open("proLogicSwitchMonitor.do","_blank")
	}else if(url=='susbigScreen.do'){
		window.open("susbigScreen.do","_blank")
	}else{
		var menuTitle = text;
		contentPanel.setTitle(menuTitle);
		contentPanel.getHeader().show();//让contentPanel显示标题头
		Ext.Ajax.request({
			url : 'dbresource.do',
			method : 'POST',
			sync : true,
			params : {
				groupId : groupId
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var mess = Ext.decode(response.responseText).message;
				if (!success) {
					Ext.Msg.alert("消息提示", mess);
				}
			},
			failure : function(result, request) {
				Ext.Msg.alert("消息提示", mess);
				
			}
		});
		
		var mId = id;
		contentPanel.getLoader().load({
			url : url,
			sync : true,
			params : {
				menuId : mId,
				contentPanelHeight:contentPanel.getHeight(),
				windowScHeight:window.screen.height
			},
			scripts : true,
			failure : function(result) {
				secureFilterRs(result,"没有操作权限！");
		    },
			callback : function(records, operation, success) {
				showImg(url);
				showDateCenterOperationImg(url);
				showGraphMonitorImg(url);
				showTopoGraphImg(url);
//			showresGroupMonitorImg(url);
			}
		});
	}
	if (Ext.isIE) {
		CollectGarbage();
	}
}

function menuClickSmall(text,url,groupId,id,e){
	
	if (refreshObj) {
		clearInterval(refreshObj);
	}
	if (refreshTryForBasic) {
		clearInterval(refreshTryForBasic);
	}
	if (refreshTryForUpdate) {
		clearInterval(refreshTryForUpdate);
	}
	if (refreshTryForVersion) {
		clearInterval(refreshTryForVersion);
	}
	if (refreshObjShellOutput) {
		clearInterval(refreshObjShellOutput);
	}
	if (refreshObjForTestExec) {
		clearInterval(refreshObjForTestExec);
	}
	if (refreshObjShellOutputForTestExec) {
		clearInterval(refreshObjShellOutputForTestExec);
	}
	if (refreshObjForExec) {
		clearInterval(refreshObjForExec);
	}
	if (refreshObjForExecWhite) {
		clearInterval(refreshObjForExecWhite);
	}
	if (refreshObjShellOutputForExec) {
		clearInterval(refreshObjShellOutputForExec);
	}
	if (refreshObjShellOutputForExecWhite) {
		clearInterval(refreshObjShellOutputForExecWhite);
	}
	if(refreshTopoTrank){
		clearInterval(refreshTopoTrank);
	}
	if(refreshTopoGanttShow){
		clearInterval(refreshTopoGanttShow);
	}
	if(refreshTopoShow){
		clearInterval(refreshTopoShow);
	}
	if(refreshTopoGanttBussShow){
		clearInterval(refreshTopoGanttBussShow);
	}
	if(refreshTopoGanttTranckShow){
		clearInterval(refreshTopoGanttTranckShow);
	}
	if (refreshObjForTestExecMonitorSingle) {
		clearInterval(refreshObjForTestExecMonitorSingle);
	}
	if (refreshObjShellOutputForTestExecMonitorSingle) {
		clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
	}
	if (refreshObjForToolMonitor) {
		clearInterval(refreshObjForToolMonitor);
	}
	if (refreshObjShellOutputForToolMonitor) {
		clearInterval(refreshObjShellOutputForToolMonitor);
	}
	if (refreshObjForExecForFlow) {
		clearInterval(refreshObjForExecForFlow);
	}
	if (refreshObjForTestExecForFlow) {
		clearInterval(refreshObjForTestExecForFlow);
	}
	if (refreshObjShellOutputForTestExecForFlow) {
		clearInterval(refreshObjShellOutputForTestExecForFlow);
	}
	if (refreshObjShellOutputForExecForFlow) {
		clearInterval(refreshObjShellOutputForExecForFlow);
	}
	if(url=='drOverview.do'){
		window.open("drOverview.do","_blank")
	}else if(url=='drOverviewNew.do'){
		window.open("drOverviewNew.do","_blank")
	}else if(url=='drOverviewNewFJ.do'){
		window.open("drOverviewNewFJ.do","_blank")
	}else if(url=='ipadStartForward.do'){
		window.open("ipadStartForward.do","_blank")
	}else if(url=='ipadStartForwardFJ.do'){
		window.open("ipadStartForwardFJ.do","_blank")
	}else if(url=='initSwitchMonitorOverview.do'){
		window.open("initSwitchMonitorOverview.do","_blank")
	}else if(url.indexOf("topoScreenDisplay.do")>-1  || url.indexOf("topoScreenDisplayFir.do")>-1){
		window.open(url, "_blank");
	}else if(url=='proLogicSwitchMonitor.do'){
		window.open("proLogicSwitchMonitor.do","_blank")
	}else{
		var menuTitle = text;
		menuTitle = menuTitle.replace("white","#595757");
		contentPanel.setTitle(menuTitle);
		contentPanel.getHeader().show();//让contentPanel显示标题头
		Ext.Ajax.request({
			url : 'dbresource.do',
			method : 'POST',
			sync : true,
			params : {
				groupId : groupId
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				if (!success) {
					var mess = Ext.decode(response.responseText).message;
					Ext.Msg.alert("消息提示", mess);
				}
			},
			failure : function(result, request) {
				Ext.Msg.alert("消息提示", mess);
				
			}
		});
		
		var mId = id;
		contentPanel.getLoader().load({
			url : url,
			sync : true,
			params : {
				menuId : mId,
				contentPanelHeight:contentPanel.getHeight(),
				windowScHeight:window.screen.height
			},
			scripts : true,
			failure : function(result) { 
				secureFilterRs(result,"没有操作权限！");
		    },
			callback : function(records, operation, success) {
				showImg(url);
				showDateCenterOperationImg(url);
				showGraphMonitorImg(url);
				showTopoGraphImg(url);
			}
		});
	}
	// 当二级菜单下的子菜单点击时的样式修改
	if(e != null && e != ''){
		$("#main-tree-panel").find("li[class='Menu_L_Hover']").removeClass("Menu_L_Hover");
		$(e).attr("class","Menu_L_Hover");
	}
	if (Ext.isIE) {
		CollectGarbage();
	}
}

function popNewTab(title, url, params, groupId) {
	
	if (refreshObj) {
		clearInterval(refreshObj);
	}
	if (refreshTryForBasic) {
		clearInterval(refreshTryForBasic);
	}
	if (refreshTryForUpdate) {
		clearInterval(refreshTryForUpdate);
	}
	if (refreshTryForVersion) {
		clearInterval(refreshTryForVersion);
	}
	if (refreshObjShellOutput) {
		clearInterval(refreshObjShellOutput);
	}
	if (refreshObjForTestExec) {
		clearInterval(refreshObjForTestExec);
	}
	if (refreshObjShellOutputForTestExec) {
		clearInterval(refreshObjShellOutputForTestExec);
	}
	if (refreshObjForExec) {
		clearInterval(refreshObjForExec);
	}
	if (refreshObjForExecWhite) {
		clearInterval(refreshObjForExecWhite);
	}
	if (refreshObjShellOutputForExec) {
		clearInterval(refreshObjShellOutputForExec);
	}
	if (refreshObjShellOutputForExecWhite) {
		clearInterval(refreshObjShellOutputForExecWhite);
	}
	if(refreshTopoTrank){
		clearInterval(refreshTopoTrank);
	}
	if(refreshTopoGanttShow){
		clearInterval(refreshTopoGanttShow);
	}
	if(refreshTopoShow){
		clearInterval(refreshTopoShow);
	}
	if(refreshTopoGanttBussShow){
		clearInterval(refreshTopoGanttBussShow);
	}
	if(refreshTopoGanttTranckShow){
		clearInterval(refreshTopoGanttTranckShow);
	}
	if (refreshObjForTestExecMonitorSingle) {
		clearInterval(refreshObjForTestExecMonitorSingle);
	}
	if (refreshObjShellOutputForTestExecMonitorSingle) {
		clearInterval(refreshObjShellOutputForTestExecMonitorSingle);
	}
	if (refreshObjForToolMonitor) {
		clearInterval(refreshObjForToolMonitor);
	}
	if (refreshObjShellOutputForToolMonitor) {
		clearInterval(refreshObjShellOutputForToolMonitor);
	}
	if (refreshObjForExecForFlow) {
		clearInterval(refreshObjForExecForFlow);
	}
	if (refreshObjForTestExecForFlow) {
		clearInterval(refreshObjForTestExecForFlow);
	}
	if (refreshObjShellOutputForTestExecForFlow) {
		clearInterval(refreshObjShellOutputForTestExecForFlow);
	}
	if (refreshObjShellOutputForExecForFlow) {
		clearInterval(refreshObjShellOutputForExecForFlow);
	}
	var menuTitle = title;
	contentPanel.setTitle(menuTitle);
	contentPanel.getHeader().show();//让contentPanel显示标题头
	Ext.Ajax.request({
		url : 'dbresource.do',
		method : 'POST',
		sync : true,
		params : {
			groupId : groupId
		},
		success : function(response, request) {
			var success = Ext.decode(response.responseText).success;
			if (!success) {
				var mess = Ext.decode(response.responseText).message;
				Ext.Msg.alert("消息提示", mess);
			}
		},
		failure : function(result, request) {
			Ext.Msg.alert("消息提示", mess);
			
		}
	});
	
	contentPanel.getLoader().load({
		url : url,
		sync : true,
		params :params ,
		scripts : true,
		callback : function(records, operation, success) {
			showImg(url);
			showDateCenterOperationImg(url);
			showGraphMonitorImg(url);
		}
	});
}

function urlLink(url){
	
	//alert(url);
}
//lb

function createGridButton (divId){
	//监听元素宽度
	(function($,h,c){var a=$([]),e=$.resize=$.extend($.resize,{}),i,k="setTimeout",j="resize",d=j+"-special-event",b="delay",f="throttleWindow";e[b]=250;e[f]=true;$.event.special[j]={setup:function(){if(!e[f]&&this[k]){return false}var l=$(this);a=a.add(l);$.data(this,d,{w:l.width(),h:l.height()});if(a.length===1){g()}},teardown:function(){if(!e[f]&&this[k]){return false}var l=$(this);a=a.not(l);l.removeData(d);if(!a.length){clearTimeout(i)}},add:function(l){if(!e[f]&&this[k]){return false}var n;function m(s,o,p){var q=$(this),r=$.data(this,d);r.w=o!==c?o:q.width();r.h=p!==c?p:q.height();n.apply(this,arguments)}if($.isFunction(l)){n=l;return m}else{n=l.handler;l.handler=m}}};function g(){i=h[k](function(){a.each(function(){var n=$(this),m=n.width(),l=n.height(),o=$.data(this,d);if(m!==o.w||l!==o.h){n.trigger(j,[o.w=m,o.h=l])}});g()},e[b])}})(jQuery,this);

	var c=0;
	var container = document.querySelector("body");
	var btnLeft="<div class='buttonLeftDiv' style='background:#fff;	border-right:2px solid white;min-width:20px;height:34px;z-index:2;'><svg class='icon' style='width: 1em; height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;margin:11px 0 0 1px;font-size: 14px;color:#969696;' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='834'><path d='M256 512l512 512 0-135.776-376.224-376.224 376.224-376.256 0-135.745z' p-id='835'></path></svg></div>";
	var btnRight="<div class='buttonRightDiv' style='background:#fff;margin-left:-20px;border-left:2px solid white;min-width:20px;height:34px;z-index:2;'><svg class='icon' style='width: 1em; height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;    margin:8px 0 0 1px;font-size: 17px;color:#969696;' viewbox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='1461'><path d='M353.743138 127.382179c7.761778 0 15.522532 2.910283 21.598924 8.731872l365.782847 352.429725c6.100952 5.871731 9.573029 14.003945 9.573029 22.428825 0 8.463766-3.473101 16.594957-9.573029 22.404266L375.343085 885.804545c-12.407588 11.949146-32.117488 11.642155-44.066635-0.816598-11.921517-12.356422-11.56643-32.118512 0.816598-44.00319l342.496491-330.013179L332.093049 181.009564c-12.383028-11.949146-12.739139-31.659047-0.816598-44.078914C337.404008 130.59843 345.574085 127.382179 353.743138 127.382179' p-id='1462'></path></svg></div>";
	var i=0;
	container.addEventListener('DOMNodeInserted', function (i) {//当页面游元素被创建执行
		for(var i=0;i<$('.x-grid-header-ct').length;i++){//几个grid
			$('.x-grid-header-ct').eq(i).next().css('overflowX','hidden').children().css('overflowX','hidden');
			//如果当前grid有滚动条
			if($('.x-grid-header-ct').eq(i).find('.buttonLeftDiv').length==0&&$('.x-grid-header-ct').eq(i).find('.buttonRightDiv').length==0&&($('.x-grid-header-ct').eq(i).next().children('.x-grid-view').children('.x-grid-table').width()-$('.x-grid-header-ct').eq(i).next().children('.x-grid-view').width())>10){//显示按钮
				//lb修复
				$(".x-grid-header-ct").eq(i).resize(function(){
					  if($('.x-box-inner').length>0){
						  $('.x-box-inner').css('marginLeft','0px');
					  }
					  if($(".x-grid-view").length>0){
					      for(var moon=0;moon<$(".x-grid-view").length;moon++){
				    		  if($(".x-grid-view")[moon].scrollLeft){ 
						    	  $(".x-grid-view")[moon].scrollLeft=0;
						    	  
						      }
					      }
					  }
					  
				      
				});
				$('.x-grid-header-ct').eq(i).prepend(btnLeft);
				$('.x-grid-header-ct').eq(i).append(btnRight);
				$('.x-grid-header-ct').css({"display":"flex","flex-direction": "row"/*,"justify-content":"space-between"*/});
				//添加点击滑动事件
				$('.x-grid-header-ct').eq(i).find(".buttonLeftDiv").click(function(){
					$(this).parent().next().children(".x-grid-view")[0].scrollLeft-=200;
					var newLeft=parseInt($(this).siblings('.x-box-inner').css('marginLeft'))+200;
					if(parseInt($(this).siblings('.x-box-inner').css('marginLeft'))!=$(this).parent().next().children(".x-grid-view")[0].scrollLeft){
						parseInt($(this).siblings('.x-box-inner').css('marginLeft',($(this).parent().next().children(".x-grid-view")[0].scrollLeft)+'px'));
					}
					parseInt($(this).siblings('.x-box-inner').css('marginLeft',-($(this).parent().next().children(".x-grid-view")[0].scrollLeft)+'px'));
					//$(this).siblings('.x-box-inner').css('marginLeft',newLeft+'px');
    			});
				$('.x-grid-header-ct').eq(i).find(".buttonRightDiv").click(function(){
					$(this).parent().next().children(".x-grid-view")[0].scrollLeft+=200;
					var newLeft=parseInt($(this).siblings('.x-box-inner').css('marginLeft'))-200; 
					if(parseInt($(this).siblings('.x-box-inner').css('marginLeft'))!=$(this).parent().next().children(".x-grid-view")[0].scrollLeft){
						parseInt($(this).siblings('.x-box-inner').css('marginLeft',-($(this).parent().next().children(".x-grid-view")[0].scrollLeft)+'px'));
					}
					parseInt($(this).siblings('.x-box-inner').css('marginLeft',-($(this).parent().next().children(".x-grid-view")[0].scrollLeft)+'px'));
					//$(this).siblings('.x-box-inner').css('marginLeft',newLeft+'px');
					
    			});
			}
			if($('.x-grid-header-ct').eq(i).find('.buttonLeftDiv').length==1&&$('.x-grid-header-ct').eq(i).find('.buttonRightDiv').length==1){
				$('.x-grid-header-ct').eq(i).siblings(".x-panel-body").children(".x-grid-view").children("table").css({'paddingLeft':'20px','background':'repeating-linear-gradient(#fafafa 0px,#fafafa 31px, #fff 39px)'}); 
			}
			
		}
		//按钮hover样式
		$(".buttonLeftDiv").hover(function(){
		    $(this).css("background-color","#EBEFF5");
		    $(this).css("cursor","pointer");
		},function(){
			$(this).css("background-color","#fff");
		});
		$(".buttonRightDiv").hover(function(){
			$(this).css("background-color","#EBEFF5");
			$(this).css("cursor","pointer");
		},function(){
			$(this).css("background-color","#fff");
		});
		
	    
	}, false);
}
//lb
function lbSearch(e){
	var x = document.getElementById("lbSearchInput").value;
	var inputValue=$(e)[0].value
	var lbChangeArr=[];
	var newLbChangeArr=[];
	//console.log($(e).parent().parent())
	//console.log($(e).parent().parent().find('.lbIconsMenuOne').length)
	if(inputValue=='' || inputValue==' ' || inputValue==null || typeof inputValue =="undefined"){
		return 
	}
	for(var i=0;i<$(e).parent().parent().find('.lbIconsMenuOne').length;i++){
		lbChangeArr.push($(e).parent().parent().find('.lbIconsMenuOne').eq(i).clone().children().remove().end().text());
		if(lbChangeArr[i].indexOf(inputValue)>=0){//如果包含
			newLbChangeArr.push(lbChangeArr[i])
		}
	}
	
	for(var i=0;i<newLbChangeArr.length;i++){
		for(var ii=0;ii<$(e).parent().parent().find('.lbIconsMenuOne').length;ii++){
			if($(e).parent().parent().find('.lbIconsMenuOne').eq(ii).clone().children().remove().end().text().indexOf(newLbChangeArr[i])>=0){
				var clone=$(e).parent().parent().find('.lbIconsMenuOne').eq(ii).clone(true);
				$(e).parent().parent().find('.lbIconsMenuOne').eq(ii).remove();
				$(e).parent().parent().children('.lbSearch').after(clone);
			}
		}
	}
}
//lbAdd190628重置菜单子菜单位置
function getMainMenuPosition(el,direction){//参数说明：1元素class(唯一)或id，2想要获取的元素位置的方向top或right(字符串)
	if($('#'+el).length==1){
		var menuDiv=$('#'+el);
		var top=menuDiv[0].getBoundingClientRect().top+'px';
		var right=parseInt(menuDiv.css('left'))+parseInt(menuDiv.css('width'))+'px';
		if(direction=='top'){
			return top
		}else if(direction=='right'){
			return right
		}else{
			return
		}
	}else if($('.'+el)){
		var menuDiv=$('.'+el);
		var top=menuDiv[0].getBoundingClientRect().top+'px';
		var right=parseInt(menuDiv.css('left'))+parseInt(menuDiv.css('width'))+'px';
		if(direction=='top'){
			return top
		}else if(direction=='right'){
			return right
		}else{
			return
		}
	}else{
		return
	}
}
//end