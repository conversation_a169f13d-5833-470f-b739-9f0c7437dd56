/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.warnSure', {
    extend: 'Ext.form.Panel',
    alias: 'widget.taskCheck',

    requires: [
        'Ext.form.ComboBox',
        'Ext.form.TextArea',
        'Ext.button.Button'
    ],

    height: 150,
    title: '',
    frame : true,
	buttonAlign : "left",
	
	layout: 'hbox',
	
	serviceInfo : {},
	initOperBtn: function(n){
		var me = this;
//		n = parseInt(n);
		var editerName = me.editerName;
		var jspParms = me.jspParms;
		if(GRAPHS[editerName]!=undefined)
			GRAPHS[editerName].controlSidbar('view');
		if(GRAPHS[jspParms.rootEditer]!=undefined)
		{
			GRAPHS[jspParms.rootEditer].actionType = 'view';
		}
	},
	
    initComponent: function() {
        var me = this;
        var jspParms = me.jspParms;
        var serviceInfo = me.serviceInfo;
        if(jspParms.scriptLevelDisplay==undefined)
        {
        	jspParms.scriptLevelDisplay = '';
        }
        
        function publishBtnFn(){}
        
        function nowayBtnFn(){} 
        
        function backToDbCheckBtnFn(){}
        function viewBasicInfoBtnFn(serviceInfo){
   				Ext.create('Ext.window.Window', {
   		            title: '基本信息',
   		            autoScroll: true,
   		            modal: true,
   		            closeAction: 'destroy',
   		            buttonAlign: 'center',
   		            draggable: true,
   		            resizable: false,
   		            width: 500,
   		            height: 328,
   		            loader: {
   		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
   		            	params: {
   		            		creatorFullName: 	serviceInfo.creatorFullName,
   			                bussName: 			serviceInfo.bussName,
   			                bussTypeName:		serviceInfo.bussTypeName,
   			                funcDescText: 		serviceInfo.funcDescText,
   			                serviceName:		serviceInfo.serviceName
   		            	},
   		            	autoLoad: true
   		            },
   		            dockedItems: [{
   		                xtype: 'toolbar',
   		                border: false,
   		                dock: 'bottom',
   		                margin: '0 0 5 0',
   		                layout: {pack: 'center'},
   		                items: [{
   		                    xtype: 'button',
   		                    text: '关闭',
   		                    cls: 'Common_Btn',
   		                    handler: function() {
   		                        this.up("window").close();
   		                    }
   		                }]
   		            }]
   		        }).show();
   			}
        
        Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var recheckAuditorStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getPublishRecheckAuditorList.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		recheckAuditorStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		iworkItemid : jspParms.workItemid
		    };
		    Ext.apply(recheckAuditorStore.proxy.extraParams, new_params);
	    });
		
    	
   	 var levelStore = Ext.create('Ext.data.Store', {
   		    fields: ['iid', 'scriptLevel'],
   		    data : [
   		        {"iid":"0", "scriptLevel":"白名单"},
   		    	{"iid":"1", "scriptLevel":"高级风险"},
   		    	{"iid":"2", "scriptLevel":"中级风险"},
   		    	{"iid":"3", "scriptLevel":"低级风险"}
   		    ]
   		});
   	 
   	 var scriptLevelCb = Ext.create('Ext.form.field.ComboBox', {
   	        name: 'scriptLevel',
   	        labelWidth: 85,
   	        width : '100%',
   	        queryMode: 'local',
   	        fieldLabel: '风险级别',
   	        labelAlign:'right',
   	        displayField: 'scriptLevel',
   	        valueField: 'iid',
   	        editable: false,
   	        value: jspParms.scriptLevelDisplay,
   	        emptyText: '--请选择风险级别--',
   	        store: levelStore
   	    });
   		
   	 var noWayButton = Ext.create('Ext.Button', {
   		    text : '打回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    hidden:jspParms.from==100?true:false,
   		    handler : nowayBtnFn
   	  });
   	 
   	 var backToDbCheckButton = Ext.create('Ext.Button', {
   		    text : '返回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    hidden:jspParms.from==100?true:false,
   		    handler : backToDbCheckBtnFn
   	  });
   	 
   	 var viewBasicInfoButton = Ext.create("Ext.Button", {
   			text: "基本信息",
   			disabled : false,
   			handler: function () {
   				viewBasicInfoBtnFn(serviceInfo)
   			}
   		});
   	 var publishButton = Ext.create('Ext.Button', {
	    text : '同意执行',
	    margin : '0 0 0 5',
	    textAlign : 'center',
	    hidden:jspParms.from==100?true:false,
	    handler : publishBtnFn
	  });
   	 
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea', {
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        flex: 1,
	        labelAlign:'right',
	        padding: '0 2 0 8',
	        width: 480,
	        labelWidth: 60,
	        labelSepartor : "：",
	        readOnly: jspParms.actionType=='dbbackForExec',
	        value: jspParms.backInfo
		});
		var isTimerTaskCheck = Ext.create('Ext.form.field.Checkbox', {
        	checked : jspParms.isTimetask==1?true:false,
        	fieldLabel: '定时任务',
        	labelAlign : 'right',
        	labelWidth : 60,
        	readOnly:true,
    		margin:'5 0 5 0',
    		hidden: !(jspParms.isScriptConvertToFlow=='true')
        });
		
        var execTimeCheck = new Ext.form.TextField ({
		    fieldLabel : '执行时间',
		    labelWidth : 60,
		    labelAlign : 'right',
		    width: 200,
		    readOnly:true,
		    value:jspParms.taskTime,
		    hidden: !(jspParms.isScriptConvertToFlow=='true')
		});
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
    var execStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecUserList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
    var execUser = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "执行人",
	    store: execStore,
	    queryMode: 'local',
	    width: 200,
	    displayField: 'fullName',
	    valueField: 'loginName',
	    labelWidth : 60,
	    editable : false,
	    readOnly:true,
	    hidden:!(jspParms.execUserSwitch=='true'),
		labelAlign : 'right'
	  });
    execStore.load({
	    callback : function (records, operation, success)
	    {
	    	execUser.setValue (jspParms.performUser);
	    }
    });
        var execDesc = Ext.create('Ext.form.field.TextArea', {
	        fieldLabel: '执行描述',
	        labelWidth: 60,
	        padding: '0 2 0 8',
	       // height: 60,
	        maxLength: 2000,
	        value: jspParms.publishDesc,
	        width: 480,
	        readOnly:true,
	        autoScroll: true
	    });
        Ext.applyIf(me, {
            items: [{
	        	border: false,
	            flex: 2,
	            layout: 'vbox',
	            items: [isTimerTaskCheck,execTimeCheck,execUser]
	        },{
	            border: false,
	            flex: 4.5,
	            layout: 'vbox',
	            items: [execDesc]
	        },{
	            border: false,
	            flex: 4.5,
	            layout: 'vbox',
	            items: [backInfo]
	        }],
            buttons: [viewBasicInfoButton]
        });
    	
        
        me.callParent(arguments);
        me.initOperBtn(25);
    }
});