<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.common.utils.SessionData" %>
<html>
<head>
<style>

.scriptCustomTask_indigo{
	background:#dcf8ff;
}
.scriptCustomTask .scriptCustomTask_indigo .x-grid-cell{

	background:#dcf8ff;
}
.scriptCustomTask .scriptCustomTask_indigo .x-grid-row-over .x-grid-td {
	background:#dcf8ff;
}
</style>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript">
var tempDataFlowTemplateManager = {};
<%
String menuid = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempDataFlowTemplateManager.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
<%
boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
boolean showConfigSwitch = Environment.getInstance().getScriptShowConfigSwitch();
boolean pageStyleSwitch = Environment.getInstance().getPageStyleSwitch();
boolean analysisButtonSwitch = Environment.getInstance().getScriptCommontaskAnalysisButtonSwitch();
boolean topInformationSwitch = Environment.getInstance().getScriptserviceTopInformationSwitch();
//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
boolean firstUserSelected = Environment.getInstance().getSelectedFirstUserSwitch();
SessionData sessionData = SessionData.getSessionData(request);
String loginUser = sessionData.getLoginName();
//标签
boolean sdScriptLabelEditSwitch = Environment.getInstance().sdScriptLabelEditSwitch ();
//功能分类
boolean sdFunctionSortSwitch = Environment.getInstance().sdFunctionSortSwitch();
%>

var filter_templateNameQueryFlowTemplateManager ='<%=request.getParameter("filter_templateNameQuery")==null?"":request.getParameter("filter_templateNameQuery")%>';
var filter_serverNameQueryFlowTemplateManager ='<%=request.getParameter("filter_serverNameQuery")==null?"":request.getParameter("filter_serverNameQuery")%>';

var execUserSwitchFlowTemplateManager = <%=execUserSwitch%>;
var showConfigSwitchFlowTemplateManager = <%=showConfigSwitch%>;
var pageStyleSwitchFlowTemplateManager = <%=pageStyleSwitch%>;
var analysisButtonSwitchFlowTemplateManager = <%=analysisButtonSwitch%>;//解析按钮开关
var loginUserFlowTemplateManager = '<%=loginUser%>';
var taskTypeFlowTemplateManager = '<%=request.getAttribute("taskType")%>';
var BStypeSize = '<%=request.getAttribute("BStypeSize")%>';
var loginUserName = '<%=request.getAttribute("userName")%>';
var topInformationSwitch = <%=topInformationSwitch%>;//顶部信息统计开关
//邮储  任务申请、常用任务提交时审核人默认置空，开关控制是否默认选择第一个人
var firstUserSelected = <%=firstUserSelected%>;
var labelSwitch = <%=sdScriptLabelEditSwitch%>;
var sdFunctionSortSwitch=<%=sdFunctionSortSwitch%>
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/flowTemplateManagerForProduct.js"></script>
</head>
<body>
<div id="flowTemplateManagerForProduct_area" style="width: 100%;height: 100%"></div>
</body>
</html>