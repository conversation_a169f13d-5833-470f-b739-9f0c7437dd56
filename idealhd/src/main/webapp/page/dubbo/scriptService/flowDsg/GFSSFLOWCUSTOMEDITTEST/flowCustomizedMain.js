/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var customNameObjOutSideGFSSFLOWCUSTOMEDITTEST;
var editorSonGFSSFLOWCUSTOMEDITTEST;


Ext.onReady(function() {
    //清理各种监听
    destroyRubbish();
    var goToTestPage = false;
    
    var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSFLOWCUSTOMEDITTEST
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });

    //0.1 Graph 
    var mainTabs = Ext.widget('tabpanel', {
        tabPosition: 'top',
        activeTab: 0,
        width: '100%',
        height: contentPanel.getHeight() - 48,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        items: [{
            title: '图形化显示',
            loader: {
                url: 'flowCustomizedImgScriptServiceGFSSFLOWCUSTOMEDITTEST.do',
                params: {
                    flag: flagGFSSFLOWCUSTOMEDITTEST,
                    actionType: actionTypeGFSSFLOWCUSTOMEDITTEST
                },
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        },
        {
            hidden: true,
            title: '列表显示',
            loader: {
                url: 'flowCustomizedListScriptServiceGFSSFLOWCUSTOMEDITTEST.do',
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        }]
    });

    customNameObjOutSideGFSSFLOWCUSTOMEDITTEST = Ext.create('Ext.form.field.Text', {
        padding: '0 5 0 0',
        fieldLabel: '模板名称',
        labelWidth: 70,
        width: 200,
        value: customNameGFSSFLOWCUSTOMEDITTEST
    });
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
        margin: '0 0 0 5',
       // cls: 'Common_Btn',
        textAlign: 'center',
        handler: function() {
        	if(actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
        		saveCustomTemplateGFSSFLOWCUSTOMEDITTEST();//调用保存方法
        	} else {
        		Ext.Ajax.request({
        			url: 'getFlowRunningState.do',
        			method: 'POST',
        			async: false,
        			params: {
        				flowServiceId: iidGFSSFLOWCUSTOMEDITTEST,
        			},
        			success: function(response, options) {
        				var success = Ext.decode(response.responseText).success;
        				var message = Ext.decode(response.responseText).message;
        				if(success) {
        					saveFlowGFSSFLOWCUSTOMEDITTEST(true);//调用保存方法
                		} else {
                			Ext.Msg.alert('提示', message);
                			return;
                		}
        			},
        			failure: function(result, request) {
        				Ext.Msg.alert('提示', '获取信息失败');
        			}
        		});
        	}
        }
    });
    var startButton = Ext.create('Ext.Button', {
    	text: '启动',
    	//cls: 'Common_Btn',
    	margin: '0 0 0 5',
    	textAlign: 'center',
    	handler: function() {
    		Ext.MessageBox.buttonText.yes = "确定"; 
    		Ext.MessageBox.buttonText.no = "取消"; 
    		Ext.Msg.confirm("请确认", "是否确定启动该模板", function(id){
    			if(id=='yes') {
    				if(actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
    					startCustomTemplateGFSSFLOWCUSTOMEDITTEST();
    				} else {
    					var isOk = false;
    					Ext.Ajax.request({
    	        			url: 'validFlowCustomTemplateData.do',
    	        			method: 'POST',
    	        			async: false,
    	        			params: {
    	        				iid: customIdGFSSFLOWCUSTOMEDITTEST,
    	        				flag: 0
    	        			},
    	        			success: function(response, options) {
    	        				var success = Ext.decode(response.responseText).success;
    	        				if(success) {
    	        					isOk = true;
    	        				} else {
    	        					var errorMessage = Ext.decode(response.responseText).message;
    	        					Ext.Msg.alert('提示', errorMessage);
    	        				}
    	        			},
    	        			failure: function(result, request) {
    	        				Ext.Msg.alert('提示', message+'<br>校验模板启动数据信息失败！');
    	        			}
    	        		});
    					
    					if(!isOk) {
    						return;
    					}
    					Ext.Ajax.request({
	        				url: 'startFlowCustomTemplate.do',
	        				method: 'POST',
	        				params: {
	        					iid: customIdGFSSFLOWCUSTOMEDITTEST,
	        					flag: 0
	        				},
	        				success: function(response, options) {
	        					var success = Ext.decode(response.responseText).success;
	        					var message = Ext.decode(response.responseText).message;
	        					Ext.Msg.alert('提示', message);
	        				},
	        				failure: function(result, request) {
	        					Ext.Msg.alert('提示', '模板启动失败！');
	        				}
	        			});
    				}
    			}
    		});
    	}
    });
    var backButton = Ext.create('Ext.Button', {
        text: '返回',
        margin: '0 0 0 5',
     //   cls: 'Common_Btn',
        textAlign: 'center',
        handler: function() {
            destroyRubbish();
            contentPanel.getLoader().load({
                url: 'flowCustomizedTemplateForTest.do',
                params: {
                    'filter_bussId': filter_bussIdGFSSFLOWCUSTOMEDITTEST,
                    'filter_bussTypeId': filter_bussTypeIdGFSSFLOWCUSTOMEDITTEST,
                    'filter_scriptName': filter_scriptNameGFSSFLOWCUSTOMEDITTEST,
                    'filter_serviceName': filter_serviceNameGFSSFLOWCUSTOMEDITTEST,
                    'filter_scriptType': filter_scriptTypeGFSSFLOWCUSTOMEDITTEST
                }
            });
            if (Ext.isIE) {
                CollectGarbage();
            }
        }
    });
    
    var viewBasicInfoButton = Ext.create("Ext.Button", {
		//cls: 'Common_Btn',
		text: "基本信息",
		margin: '0 0 0 5',
		disabled : false,
		handler:function(){
			Ext.create('Ext.window.Window', {
	            title: '基本信息',
	            autoScroll: true,
	            modal: true,
	            closeAction: 'destroy',
	            buttonAlign: 'center',
	            draggable: true,
	            resizable: false,
	            width: 500,
	            height: 328,
	            loader: {
	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
	            	params: {
	            		creatorFullName: creatorFullName,
		                bussName: bussName,
		                bussTypeName:bussTypeName,
		                funcDescText: funcDescText,
		                serviceName:serviceName
	            	},
	            	autoLoad: true
	            },
	            dockedItems: [{
	                xtype: 'toolbar',
	                border: false,
	                dock: 'bottom',
	                margin: '0 0 5 0',
	                layout: {pack: 'center'},
	                items: [{
	                    xtype: 'button',
	                    text: '关闭',
	                    cls: 'Common_Btn',
	                    handler: function() {
	                        this.up("window").close();
	                    }
	                }]
	            }]
	        }).show();
		}
	});
    
    if(actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
		 var radioGrTest = Ext.create('Ext.form.RadioGroup', {
			 anchor: 'none',
	         layout: {
	             autoFlex: false
	         },
	         defaults: {
	             margin: '0 5 0 0'
	         },
	         cls: 'customer-radio-group',
	         items: [
	             {boxLabel: '编辑', name: 'needAgent', checked: false, inputValue: '1'},
	             {boxLabel: '测试', name: 'needAgent', checked: true, inputValue: '2'},
	         ],
	         listeners: {
	             change: function (field, newValue, oldValue) {
	             	var nowValue = parseInt(newValue['needAgent']);
	             	if(nowValue==1) {
	             		destroyRubbish();
	        	        contentPanel.getLoader().load({
	        	          url : 'flowCustomizedInitScriptServiceGFSSFLOWCUSTOMEDITTEST.do',
	        	          params: {
	        					iid:iidGFSSFLOWCUSTOMEDITTEST,
	        					customId: customIdGFSSFLOWCUSTOMEDITTEST,
	        					customName:customNameGFSSFLOWCUSTOMEDITTEST,
	        					actionType:'',
	        					flag:0
	        				},
	        				scripts: true});
	        	        if (Ext.isIE) {
	        	          CollectGarbage();
	        	        }
	             	}
	             }
	         }
		 });
		 var submitFromPanel = Ext.create('Ext.form.Panel', {
			width : '100%',
			border : true,
//			dockedItems : [{
//				xtype : 'toolbar',
//				dock : 'bottom',
//				items: [radioGrTest,customNameObjOutSideGFSSFLOWCUSTOMEDITTEST, '->', saveButton, startButton, viewBasicInfoButton, backButton]
//			}]
			layout: {
                type: 'hbox',
                padding:'5',
                align:'top'
            },
            defaults:{margin:'0 5 0 0'},
            items:[
            	radioGrTest,customNameObjOutSideGFSSFLOWCUSTOMEDITTEST
            ,{
                xtype:'tbspacer',
                flex:1
            },saveButton, startButton, viewBasicInfoButton, backButton,{
                xtype:'tbspacer',
                flex:1
            },{
                xtype:'tbspacer',
                width:300
            }]
		});
	 } else {
		 var radioGrEdit = Ext.create('Ext.form.RadioGroup', {
			 anchor: 'none',
			 layout: {
				 autoFlex: false
			 },
			 defaults: {
				 margin: '0 5 0 0'
			 },
			 cls: 'customer-radio-group',
			 items: [
				 {boxLabel: '编辑', name: 'needAgent', checked: true, inputValue: '1'},
				 {boxLabel: '测试', name: 'needAgent', checked: false, inputValue: '2'},
				 ],
				 listeners: {
					 change: function (field, newValue, oldValue) {
						 var nowValue = parseInt(newValue['needAgent']);
						 if (nowValue==2) {
				    		 Ext.MessageBox.confirm("请确认" ,"切换到测试视图，是否对当前作业流程图进行保存？",function( button,text ){  
			                    if( button == 'yes'){
			                    	Ext.Ajax.request({
			                			url: 'getFlowRunningState.do',
			                			method: 'POST',
			                			async: false,
			                			params: {
			                				flowServiceId: iidGFSSFLOWCUSTOMEDITTEST,
			                			},
			                			success: function(response, options) {
			                				var success = Ext.decode(response.responseText).success;
			                				var message = Ext.decode(response.responseText).message;
			                				if(success) {
					                    		var res = saveFlowGFSSFLOWCUSTOMEDITTEST(false);
					                    		if(res) {
					                    			destroyRubbish();
					                    			contentPanel.getLoader().load({
					                    				url : 'flowCustomizedInitScriptServiceGFSSFLOWCUSTOMEDITTEST.do',
					                    				params: {
					                    					iid:iidGFSSFLOWCUSTOMEDITTEST,
					                    					customId: customIdGFSSFLOWCUSTOMEDITTEST,
					                    					customName:customNameGFSSFLOWCUSTOMEDITTEST,
					                    					actionType:'exec',
					                    					flag:0
					                    				},
					                    				scripts: true
					                    			});
					                    			if (Ext.isIE) {
					                    				CollectGarbage();
					                    			}
					                    		} else {
					                    			Ext.Msg.alert('提示', '保存作业流程图失败！');
					                    			radioGrEdit.setValue({needAgent: "1"});
					                    		}
			                        		} else {
					                    		Ext.MessageBox.confirm("请确认" ,"该作业正在运行，无法保存！<br>是否继续切换到测试视图？", function(button){
					                    			if( button == 'yes'){
					                    				destroyRubbish();
							    	        	        contentPanel.getLoader().load({
							    	        	          url : 'flowCustomizedInitScriptServiceGFSSFLOWCUSTOMEDITTEST.do',
							    	        	          params: {
							    	        					iid:iidGFSSFLOWCUSTOMEDITTEST,
							    	        					customId: customIdGFSSFLOWCUSTOMEDITTEST,
							    	        					customName:customNameGFSSFLOWCUSTOMEDITTEST,
							    	        					actionType:'exec',
							    	        					flag:0
							    	        				},
							    	        				scripts: true
							    	        			});
							    	        	        if (Ext.isIE) {
							    	        	          CollectGarbage();
							    	        	        }
					                    			} else {
					                    				radioGrEdit.setValue({needAgent: "1"});
					                    			}
					                    		});
					                    	
			                        		}
			                			},
			                			failure: function(result, request) {
			                				Ext.Msg.alert('提示', '获取信息失败');
			                			}
			                		});
			                    } else {
			                    	destroyRubbish();
		    	        	        contentPanel.getLoader().load({
		    	        	          url : 'flowCustomizedInitScriptServiceGFSSFLOWCUSTOMEDITTEST.do',
		    	        	          params: {
		    	        					iid:iidGFSSFLOWCUSTOMEDITTEST,
		    	        					customId: customIdGFSSFLOWCUSTOMEDITTEST,
		    	        					customName:customNameGFSSFLOWCUSTOMEDITTEST,
		    	        					actionType:'exec',
		    	        					flag:0
		    	        				},
		    	        				scripts: true
		    	        			});
		    	        	        if (Ext.isIE) {
		    	        	          CollectGarbage();
		    	        	        }
			                    }
				             });
						 }
					 }
				 }
		 });
		 var submitFromPanel = Ext.create('Ext.form.Panel', {
				width : '100%',
				border : true,
//				dockedItems : [{
//					xtype : 'toolbar',
//					dock : 'bottom',
//					items: [radioGrEdit,customNameObjOutSideGFSSFLOWCUSTOMEDITTEST, '->', saveButton,startButton, viewBasicInfoButton, backButton]
//				}]
		 layout: {
             type: 'hbox',
             padding:'5',
             align:'top'
         },
         defaults:{margin:'0 5 0 0'},
         items:[
         	radioGrEdit,customNameObjOutSideGFSSFLOWCUSTOMEDITTEST
         ,{
             xtype:'tbspacer',
             flex:1
         },saveButton,startButton, viewBasicInfoButton, backButton,{
             xtype:'tbspacer',
             flex:1
         },{
             xtype:'tbspacer',
             width:300
         }]
			});
	 }

    /*var submitFromPanel = Ext.create('Ext.form.Panel', {
        width: '100%',
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'bottom',
            items: [customNameObjOutSideGFSSFLOWCUSTOMEDITTEST, '->', startButton, saveButton, viewBasicInfoButton, backButton]
        }]
    });*/

    var MainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "flowCustomizedMainDivGFSSFLOWCUSTOMEDITTEST",
        width: '100%',
        height: contentPanel.getHeight(),
        autoScroll: true,
        border: false,
        bodyPadding: '5 0 0 0',
        items: [mainTabs, submitFromPanel]
    });
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(MainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        mainTabs.setWidth('100%');
    });
    initGFSSFLOWCUSTOMEDITTEST();
    function initGFSSFLOWCUSTOMEDITTEST() {
    }
});