/*******************************************************************************
 * 流程监控首页
 ******************************************************************************/

Ext.onReady (function ()
{
	var parmObj = oldTempData;
	delete oldTempData;
	
	//var releaseMonitorStepStore;
	var autoRefreshTime = null;
	var autoRefreshCheck = null;
	var refreshButton = null;
	var killButton = null;
	var backButton = null;
	var R_TIME = 30;
	var winStep;
	//var globalProjectInstanceId = 0;
	//var globalSysId = 0;
	var imageId=0;

	var editor = null; 
	var graph = null;  
	var model = null; 
	var isRefresh=true;
	//var treeLeft;
	//var treeLeftStore;
	//var phaseDataList;
	// 清理主面板的各种监听时间
	destroyRubbish ();
	contentPanel.setAutoScroll (true);
	
	
	// 刷新时间输入框
	autoRefreshTime = Ext.create ('Ext.form.NumberField',
	{
	    fieldLabel : '刷新时间（秒）',
	    margin : '5',
	    labelWidth : 110,
	    width : 170,
	    value : '30',
	    allowDecimals : false,
	    minValue : R_TIME,
	    listeners :
	    {
		    blur : function ()
		    {
			    refreshTime = this.getValue ();
			    refreshTime = (refreshTime == '' || refreshTime == null) ? R_TIME : refreshTime;
			    try
			    {
				    refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
				    this.setValue (refreshTime);
			    }
			    catch (e)
			    {
				    refreshTime = R_TIME;
				    this.setValue (refreshTime);
			    }
			    if (autoRefreshCheck.checked)
			    {
				    clearInterval (refreshObjForMonitorSingle);
				    refreshObjForMonitorSingle = setInterval (reload, refreshTime * 1000);
			    }
		    }
	    }
	});
	// 是否自动刷新复选框
	autoRefreshCheck = Ext.create ('Ext.form.Checkbox',
	{
	    fieldLabel : '自动刷新',
	    //margin : '0',
	    labelWidth : 60,
	    width : 90,
	    height:30,
	    checked : true,
	    listeners :
	    {
		    change : function ()
		    {
			    if (this.checked)
			    {
				    refreshTime = autoRefreshTime.getValue ();
				    refreshTime = refreshTime == '' ? R_TIME : refreshTime;
				    try
				    {
					    refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
					    autoRefreshTime.setValue (refreshTime);
				    }
				    catch (e)
				    {
					    refreshTime = R_TIME;
					    autoRefreshTime.setValue (refreshTime);
				    }
				    clearInterval (refreshObjForMonitorSingle);
				    refreshObjForMonitorSingle = setInterval (reload, refreshTime * 1000);
			    }
			    else
			    {
				    clearInterval (refreshObjForMonitorSingle);
			    }
		    }
	    }
	});
	// 首次加载定时刷新
	if (autoRefreshCheck.checked == true)
	{
		refreshObjForMonitorSingle = setInterval (reload, autoRefreshTime.getValue () * 1000);
	}
	/** 刷新按钮* */
	refreshButton = Ext.create ("Ext.Button",
	{
	    text : '刷新',
	    textAlign : 'center',
	    cls : 'Common_Btn',
	    listeners :
	    {
		    "click" : function ()
		    {
		    	GRAPHS[parmObj.namespace].freshStatus();
		    }
	    }
	});
	
	/** 终止按钮* */
	killButton = Ext.create ("Ext.Button",
	{
	    text : '终止',
	    textAlign : 'center',
	    cls : 'Common_Btn',
	    hidden: parmObj.flagType==1?true:false,
	    listeners :
	    {
		    "click" : function ()
		    {
		    	killProject();
		    }
	    }
	});
	

	/** 返回按钮* */
	backButton = Ext.create ("Ext.Button",
	{
	    text : '返回',
	    textAlign : 'center',
	    hidden:parmObj.hidebutton=="true",
	    cls : 'Common_Btn',
	    listeners :
	    {
		    "click" : function ()
		    {
		    	console.log('parmObj',parmObj);
		    	console.log('graph',graph);
		    	if(parmObj.isFromExec==1){
		    		if(parmObj.url=='scriptTaskexecAll.do'){//任务执行和定时任务tab页形式
		    			popNewTab('执行任务', 'scriptTaskexecAll.do', {},10, false);
		    		}else{//单个的任务执行
		    			popNewTab('任务执行', 'scriptTaskexec.do', {},10, false);
		    		}
		    	}else{
			    	if(parmObj.flagType==1){
			    		if(null!=parmObj.planId && parmObj.planId!='' && parmObj.planId!='undefined' && null!=parmObj.scenceId && parmObj.scenceId!='' && parmObj.scenceId!='undefined' ){
			    			//跳转到应急预案监控面
//			    			contentPanel.getLoader().load({ 
//			    				url: "forwardPlanEmMonitor.do",
//			    		        scripts: true,
//			    		         params: {
//			    		        }
//			    			});
			    			popNewTab('应急监控', 'forwardPlanEmMonitor.do', {},10, false);
			    			
			    		}else{
			    			popNewTab('执行历史', 'historyScriptMonitorForFlowProduct.do', {
				    			filter_serviceName: 	parmObj.filter_serviceName,
				    			filter_serviceState: 	parmObj.filter_serviceState,
				    			filter_serviceStartTime:parmObj.filter_serviceStartTime,
				    			filter_serviceEndTime:	parmObj.filter_serviceEndTime
				    			},10, false);
			    		}
			    		
			    	}else{
			    		popNewTab('作业测试历史', 'scriptMonitorForFlowTest.do', {
			    			filter_serviceName: 	parmObj.filter_serviceName,
			    			filter_serviceState: 	parmObj.filter_serviceState,
			    			filter_serviceStartTime:parmObj.filter_serviceStartTime,
			    			filter_serviceEndTime:	parmObj.filter_serviceEndTime
			    			},10,false);
			    	}
		    }
		    }
	    }
	});
	/** 底部按钮操作栏* */
	var graphToolbar = Ext.create ('Ext.toolbar.Toolbar',
	{
	    renderTo :  'package_bottomMonitorSingle_' + parmObj.divID,
	    items : [{
	        xtype: 'tbtext',
	        text: '<div class="step-status notrun"></div> 未运行'
	      },{
		        xtype: 'tbtext',
		        text: '<div class="step-status running"></div> 运行中'
		      },{
			        xtype: 'tbtext',
			        text: '<div class="step-status finish"></div> 完成'
			      },{
				        xtype: 'tbtext',
				        text: '<div class="step-status fail-running"></div> 异常运行'
				  },{
				        xtype: 'tbtext',
				        text: '<div class="step-status fail-finish"></div> 异常完成'
				  },{
				        xtype: 'tbtext',
				        text: '<div class="step-status kill"></div> 已终止'
				  }, '','','','','','','','','->',
				  autoRefreshCheck,autoRefreshTime,  refreshButton,killButton,backButton
	    ]
	});
	
	var heightValue = 0;
	heightValue = contentPanel.getHeight () -modelHeigth- 185;
	
	contentPanel.on ('resize', function ()
	{
		var heightValue2 = contentPanel.getHeight ()- modelHeigth - 185;
		$ (".Package_Common").height (heightValue2);
		
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{ 
		contentPanel.setAutoScroll (false);
		Ext.destroy (winStep);
		Ext.destroy (graphToolbar);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});


/** 刷新* */
function reload ()
{
	GRAPHS[parmObj.namespace].freshStatus();
}

function killProject ()
{
	var dataArray = [{
		"iid": parmObj.flowId
	}];
	Ext.MessageBox.buttonText.yes = "确定";
	Ext.MessageBox.buttonText.no = "取消";
	Ext.Msg.confirm("确认终止", "是否终止选中的记录", function(id) {
		if (id == 'yes')
		Ext.Ajax.request({
			url : 'stopBySelect.do',
			method : 'POST',
			params : {
				jsonData : JSON.stringify(dataArray)
			},
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				 var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.alert('提示', "终止成功！");
				} else {
					Ext.Msg.alert('提示', message);
					return;
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
		reload();
	});
}

});