/*******************************************************************************
 * 流程监控首页
 ******************************************************************************/
var autoRefreshTimeFileFile = null;
var autoRefreshCheckFileFile = null;
var refreshButtonFile = null;
var R_TIMEFile = 30;
var winStepFile;
var globalProjectInstanceIdFile = 0;
var globalSysIdFile = 0;
var imageIdFile = 0;
var editorFile = null;
var graphFile = null;
var model = null;
var isRefreshFile = true;
var treeLeftFile;
var treeLeftFileStoreFile;
var phaseDataListFile;
Ext
		.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			contentPanel.setAutoScroll(true);
			Ext.define('treeModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'id',
					type : 'long'
				}, {
					name : 'flowId', // iid
					type : 'long'
				}, {
					name : 'serviceId', // isysid
					type : 'long'
				}, {
					name : 'flowName',
					type : 'string'
				} ]
			});
			treeLeftFileStoreFile = Ext.create('Ext.data.TreeStore', {
				model : 'treeModel',
				proxy : {
					type : 'ajax',
					url : 'getFLowRunningList.do'
				},
				folderSort : true
			});
			treeLeftFileStoreFile.on('beforeload', function(store, options) {
				var new_params = {
						flag: 1,
					flowType : 2,
					flowName : $('#flowNameForFile').val(),
					state : 102
				};

				Ext.apply(treeLeftFileStoreFile.proxy.extraParams, new_params);
			});
			treeLeftFileStoreFile.on('load', function(store, options) {
				// 刷新后选中刷新前节点
				if (globalProjectInstanceIdFile > 0) {
					var record = treeLeftFile.getStore().getNodeById(
							globalProjectInstanceIdFile);
					if (record != null) {
						treeLeftFile.getSelectionModel().deselect(record);
						treeLeftFile.getSelectionModel().select(record);
					} else {
						// 如果之前显示的流程已经结束，则显示当前列表的第一条流程的流程图
						var selectNode = eachChildNew(treeLeftFile.getRootNode());
						treeLeftFile.getSelectionModel().select(selectNode);
					}

				} else {
					// alert('load==0');
					// 刷新前节点未选中，则选中第一条
					var selectNode = eachChildNew(treeLeftFile.getRootNode());
					treeLeftFile.getSelectionModel().select(selectNode);
				}

			});
			$('#searchflowForFile').off('click').on('click', function(e) {
				refreshProjectListForGraphFile();
			});
			var treeLeftFileColumns = [ {
				xtype : 'treecolumn',
				dataIndex : 'flowName',
				sortable : false, // 设置这个属性进行排序
				flex : 1
			} ];
			treeLeftFile = Ext.create('Ext.tree.Panel', {
				hideHeaders : true,
				store : treeLeftFileStoreFile,
				rootVisible : false,
				useArrows : true,
				frame : true,
				columns : treeLeftFileColumns,
				renderTo : 'Change_Left_divFile',
				width : 240,
				height : contentPanel.getHeight() - 100,
				listeners : {
					select : function(combo, record, eOpts) {
						if (record.data.flowId > 0) {
							globalSysIdFile = record.data.serviceId;
							globalProjectInstanceIdFile = record.data.flowId;
							// 判断是否刷新流程图
							if (imageIdFile > 0 && imageIdFile == globalSysIdFile) {
								isRefreshFile = false;
							} else {
								isRefreshFile = true;
							}
							if (graphFile != null) {
								loadGraphForGraphFile(globalSysIdFile);
							}
						}

					}
				}
			});

			// 刷新时间输入框
			autoRefreshTimeFile = Ext
					.create(
							'Ext.form.NumberField',
							{
								fieldLabel : '刷新时间（秒）',
								margin : '5',
								labelWidth : 110,
								width : 170,
								value : '30',
								allowDecimals : false,
								minValue : R_TIMEFile,
								listeners : {
									blur : function() {
										refreshTime = this.getValue();
										refreshTime = (refreshTime == '' || refreshTime == null) ? R_TIMEFile
												: refreshTime;
										try {
											refreshTime = refreshTime < R_TIMEFile ? R_TIMEFile
													: refreshTime;
											this.setValue(refreshTime);
										} catch (e) {
											refreshTime = R_TIMEFile;
											this.setValue(refreshTime);
										}
										if (autoRefreshCheckFile.checked) {
											clearInterval(refreshObjForGraphFile);
											refreshObjForGraphFile = setInterval(reloadForGraphFile,
													refreshTime * 1000);
										}
									}
								}
							});
			// 是否自动刷新复选框
			autoRefreshCheckFile = Ext
					.create(
							'Ext.form.Checkbox',
							{
								fieldLabel : '自动刷新',
								labelWidth : 60,
								width : 90,
								height : 30,
								checked : true,
								listeners : {
									change : function() {
										if (this.checked) {
											refreshTime = autoRefreshTimeFile
													.getValue();
											refreshTime = refreshTime == '' ? R_TIMEFile
													: refreshTime;
											try {
												refreshTime = refreshTime < R_TIMEFile ? R_TIMEFile
														: refreshTime;
												autoRefreshTimeFile
														.setValue(refreshTime);
											} catch (e) {
												refreshTime = R_TIMEFile;
												autoRefreshTimeFile
														.setValue(refreshTime);
											}
											clearInterval(refreshObjForGraphFile);
											refreshObjForGraphFile = setInterval(reloadForGraphFile,
													refreshTime * 1000);
										} else {
											clearInterval(refreshObjForGraphFile);
										}
									}
								}
							});
			// 首次加载定时刷新
			if (autoRefreshCheckFile.checked == true) {
				refreshObjForGraphFile = setInterval(reloadForGraphFile,
						autoRefreshTimeFile.getValue() * 1000);
			}
			/** 刷新按钮* */
			refreshButtonFile = Ext.create("Ext.Button", {
				text : '刷新',
				width : 70,
				height : 30,
				textAlign : 'center',
				cls : 'Common_Btn',
				listeners : {
					"click" : function() {
						refreshProjectListForGraphFile();
					}
				}
			});
			/** 底部按钮操作栏* */
			var graphToolbar = Ext
					.create(
							'Ext.toolbar.Toolbar',
							{
								renderTo : package_bottomFile,
								items : [
										'->',
										{
											xtype : 'tbtext',
											text : '<div class="step-status notrun"></div> 未运行'
										},
										{
											xtype : 'tbtext',
											text : '<div class="step-status running"></div> 运行中'
										},
										{
											xtype : 'tbtext',
											text : '<div class="step-status finish"></div> 完成'
										},
										{
											xtype : 'tbtext',
											text : '<div class="step-status fail-running"></div> 异常运行'
										},
										{
											xtype : 'tbtext',
											text : '<div class="step-status fail-finish"></div> 异常完成'
										},
										{
											xtype : 'tbtext',
											text : '<div class="step-status kill"></div> 已终止'
										},
										'',
										'',
										'',
										'',
										'',
										'',
										'',
										'',
										autoRefreshTimeFile,
										autoRefreshCheckFile,
										refreshButtonFile,
										{
											text : '终止',
											width : 70,
											height : 30,
											textAlign : 'center',
											cls : 'Common_Btn',
											handler : function() {
												var data = getCHKBoxIdsForGraphFile();
												if (data.length == 0) {
													Ext.Msg.alert('提示',
															'请先选择您要操作的记录!');
													return;
												} else {
													Ext.Msg
															.confirm(
																	"请确认",
																	"是否真的要进行<终止>操作？",
																	function(
																			button,
																			text) {
																		if (button == "yes") {
																			Ext.MessageBox
																					.wait(
																							"数据处理中...",
																							"提示");
																			Ext.Ajax
																					.request({
																						url : 'scriptServiceFlowKill.do',
																						params : {
																							flag : 1,
																							insIds : data
																						},
																						method : 'POST',
																						success : function(
																								response,
																								opts) {
																							var success = Ext
																									.decode(response.responseText).success;
																							Ext.Msg
																									.alert(
																											'提示',
																											Ext
																													.decode(response.responseText).message);
																							// 当后台数据同步成功时
																							if (success) {
																								reloadForGraphFile();
																							}
																						}
																					});
																		}
																	});
												}
											}
										}
								/*
								 * { text : '暂停', width : 70, height : 22,
								 * textAlign : 'center', cls : 'Common_Btn',
								 * handler : function () { var data =
								 * getCHKBoxIdsForGraphFile (); if (data.length == 0) {
								 * Ext.Msg.alert ('提示', '请先选择您要操作的记录!'); return; }
								 * else { Ext.Msg.confirm ("请确认", "是否真的要进行<暂停>操作？",
								 * function (button, text) { if (button ==
								 * "yes") { Ext.Ajax.request ( { url :
								 * 'rmPause.do', params : { insIds : data },
								 * method : 'POST', success : function
								 * (response, opts) { var success = Ext.decode
								 * (response.responseText).success;
								 * Ext.Msg.alert ('提示', Ext.decode
								 * (response.responseText).message); //
								 * 当后台数据同步成功时 if (success) { reloadForGraphFile (); } } }); }
								 * }); } } }, { text : '继续', width : 70, height :
								 * 22, textAlign : 'center', cls : 'Common_Btn',
								 * handler : function () { var data =
								 * getCHKBoxIdsForGraphFile (); if (data.length == 0) {
								 * Ext.Msg.alert ('提示', '请先选择您要操作的记录!'); return; }
								 * else { Ext.Msg.confirm ("请确认", "是否真的要进行<继续>操作？",
								 * function (button, text) { if (button ==
								 * "yes") { Ext.Ajax.request ( { url :
								 * 'rmResume.do', params : { insIds : data },
								 * method : 'POST', success : function
								 * (response, opts) { var success = Ext.decode
								 * (response.responseText).success;
								 * Ext.Msg.alert ('提示', Ext.decode
								 * (response.responseText).message); //
								 * 当后台数据同步成功时 if (success) { reloadForGraphFile (); } } }); }
								 * }); } } }, { text : '部署失败', width : 70,
								 * height : 22, textAlign : 'center', cls :
								 * 'Common_Btn', handler : function () { var
								 * data = getCHKBoxIdsForGraphFile (); if (data.length == 0) {
								 * Ext.Msg.alert ('提示', '请先选择您要操作的记录!'); return; }
								 * else { Ext.Msg.confirm ("请确认", "是否真的要进行<部署失败>操作，部署失败后整个部署将会被停止？",
								 * function (button, text) { if (button ==
								 * "yes") { Ext.Ajax.request ( { url :
								 * 'rmKill.do?flag=4', params : { insIds : data },
								 * method : 'POST', success : function
								 * (response, opts) { var success = Ext.decode
								 * (response.responseText).success; //
								 * 当后台数据同步成功时 // Ext.Msg.alert('提示', //
								 * Ext.decode(response.responseText).message);
								 * if (success) { globalProjectInstanceIdFile = 0;
								 * setTimeout (refreshProjectListForGraphFile, 1000); } else {
								 * Ext.Msg.alert ('提示', Ext.decode
								 * (response.responseText).message); } } }); }
								 * }); } } }, { text : '手工部署', width : 70,
								 * height : 22, textAlign : 'center', cls :
								 * 'Common_Btn', handler : function () { var
								 * data = getCHKBoxIdsForGraphFile (); if (data.length == 0) {
								 * Ext.Msg.alert ('提示', '请先选择您要操作的记录!'); return; }
								 * else { Ext.Msg.confirm ("请确认", "是否真的要进行<手工部署>操作？",
								 * function (button, text) { if (button ==
								 * "yes") { Ext.Ajax.request ( { url :
								 * 'rmKill.do?flag=2', params : { insIds : data },
								 * method : 'POST', success : function
								 * (response, opts) { var success = Ext.decode
								 * (response.responseText).success; //
								 * 当后台数据同步成功时 // Ext.Msg.alert('提示', //
								 * Ext.decode(response.responseText).message);
								 * if (success) { globalProjectInstanceIdFile = 0;
								 * setTimeout (refreshProjectListForGraphFile, 1000); } else {
								 * Ext.Msg.alert ('提示', Ext.decode
								 * (response.responseText).message); } } }); }
								 * }); } } }
								 */
								]
							});

			var heightValue = 0;
			heightValue = contentPanel.getHeight() - 185;
			document.getElementById("Change_Left_divFile").style.height = heightValue
					+ 55 + "px";
			// if (wsh == 1024)
			// {
			// heightValue = contentPanel.getHeight () - 185;
			// document.getElementById ("Change_Left_div").style.height =
			// heightValue + 66 + "px";
			// }
			// else
			// {
			// heightValue = contentPanel.getHeight () - 185;
			// document.getElementById ("Change_Left_div").style.height =
			// heightValue + 66 + "px";
			// }

			/** 左侧实例名栏增加单击事件处理方法* */
			// $ ('body').off ('click', '.Change_Left li').on (
			// 'click',
			// '.Change_Left li',
			// function (e)
			// {
			//		        
			// var $this = $ (this);
			// var $allLi = $ ('.Change_Left li');
			// var instanceId = $this.data ('instance-id');
			// var isysId = $this.data ('instance-isysid');
			// $allLi.removeClass ('C_Leftmenu_over_btn').removeClass
			// ('Leftmenu_over_btn')
			// .addClass ('C_Leftmenu_btn').addClass ('Leftmenu_btn');
			// $this.removeClass ('C_Leftmenu_btn').removeClass
			// ('Leftmenu_btn').addClass ('C_Leftmenu_over_btn')
			// .addClass ('Leftmenu_over_btn');
			// globalProjectInstanceIdFile = instanceId;
			// globalSysIdFile = isysId;
			// var istate = $this.data ('instance-istate');
			// if (graph != null)
			// {
			// loadGraphForGraphFile (isysId);
			// }
			// // alert(res);
			// // 获得包数量
			// // packageCount=res.length;
			// // if(res.length>0) {
			// // $('.CR_Content').html(templateForGraphFileFunc("runInstanceConcurrent",
			// // {runInstanceConcurrents:res}));
			// // //$(".Package_Common").niceScroll({cursorborder: "1px solid
			// // #777"});//每一个包加滚动条
			// // //将滚动条定位到当前运行步骤
			// // var divId='';
			// // for(i=0;i<res.length;i++)
			// // {
			// // var resListObj=res[i].resList;
			// // for(ii=0;ii<resListObj.length;ii++)
			// // {
			// // if(resListObj[ii].runningCount>0)
			// // {
			// // var sTopN=$(".concurrent-step[data-step =
			// // "+resListObj[ii].conner+" ]").position().top-200;
			// // divId='div'+(i+1);
			// // $('#' + divId).scrollTop(sTopN);
			// // }
			// // }
			// // }
			// // //滚动条位置还原至刷新前位置
			// // scrollReset(divId);
			// // }
			// // });
			//		        
			// });
			// $(".Change_Left").niceScroll({cursorborder: "1px solid
			// #777"});//左侧树加滚动条
			// $(".Package_Common").niceScroll({cursorborder: "1px solid
			// #777"});//每一个包加滚动条
			// $(".CR_Content").niceScroll({cursorborder: "1px solid
			// #777"});//所有包的div加滚动条
			// $(".CR_Content").scroll(function(event){//监听有包的div滚动条
			// $(".Package_Common").getNiceScroll().resize();
			// });
			/** 返回树的第一个节点* */
			function eachChildNew(inObj) {
				if (inObj.childNodes == null || inObj.childNodes == '') {
					// 无运行的流程，则清空页面节点
					if (graphFile != null) {
						removeCellsFunForGraphFile();
					}
					return inObj;
				}
				var thisNode = inObj.childNodes[0];

				if (thisNode.data.flowId == null || thisNode.data.flowId == '') {
					return eachChildNew(thisNode);
				} else {
					return thisNode;
				}
			}
			contentPanel
					.on(
							'resize',
							function() {
								// alert(contentPanel.getHeight());
								// $(".CR_Content").width(contentPanel.getWidth()-$(".Change_Left").width()-50);
								var heightValue2 = contentPanel.getHeight() - 185;
								document.getElementById("Change_Left_div").style.height = heightValue2
										+ 55 + "px";
								$(".Package_Common").height(heightValue2);

							});
			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						contentPanel.setAutoScroll(false);
						Ext.destroy(winStepFile);
						Ext.destroy(graphToolbar);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
			// refreshProjectListForGraphFile ();
			// getPhaseForGraphFile ();
			mainForGraphFile(document.getElementById('graphContainerFile'), document
					.getElementById('outlineContainerFile'), document
					.getElementById('toolbarContainerFile'), document
					.getElementById('sidebarContainerFile'), document
					.getElementById('statusContainerFile'));
		});
/** 左侧实例名栏模板* */
var templatesForGraphFile = {
	'projects' : ''
			+ '<ul>'
			+ '<% _.each(projects, function (project) { %>'
			+ '<li class="C_Leftmenu_btn Leftmenu_btn" data-instance-id="<%= project.iid %>" data-instance-istate="<%= project.istate %>" data-instance-istartuser="<%= project.istartuser %>" data-instance-isysid="<%= project.isysid %>">'
			+ '<span class="System_info"><input type="checkbox" name="checkboxForLeft" value="<%= project.iid %>"/>&nbsp;&nbsp;<%= project.isysname %></span>'
			+ '<% if (project.stateColor) { %>'
			+ '<span class="System_<%= project.stateColor %> System"></span>'
			+ '<% } %>' + '</li>' + '<% }); %>' + '</ul>'
};

function templateForGraphFileFunc(templateName, data) {
	var html = templatesForGraphFile[templateName];
	return data ? _.template(html, data) : html;
}
/** 刷新* */
function reloadForGraphFile() {
	// isRefreshFile=true;
	if(contentPanel.getLoader().url=='monitorHomePageFileScriptService.do') {
		refreshProjectListForGraphFile();
	}
}
// function findchildnode(node){
// var childnodes = node.childNodes;
// alert(node[0]);
// for(var i=0;i<childnodes.length;i++){ //从节点中取出子节点依次遍历
// var rootnode = roonodes[i];
// alert(rootnode.text);
// if(rootnode.childNodes.length>0){ //判断子节点下是否存在子节点
// findchildnode(rootnode); //如果存在子节点 递归
// }
// }
// }
/** 刷新左侧列表* */
function refreshProjectListForGraphFile() {
	treeLeftFile.getStore().reload();
	// alert(treeLeftFile.getStore().getCount());

	// var nodeT = treeLeftFile.getRootNode();
	// nodeT.eachChild(function (child) {
	// console.log(child);
	// //child.getUI().toggleCheck(true);
	// //child.attributes.checked = true;
	// //treeCheckTrue(child);
	// });
	// var roonodes = treeLeftFile.getRootNode().childNodes;
	// findchildnode(roonodes); //开始递归
	// treeLeftFile.getSelectionModel().select(treeStore);//选中记录
	// alert();
	// var idsIn = getCHKBoxIdsForGraphFile ().split (",");
	// $.getJSON ('getRunInstanceListForSUS.do', function (res)
	// {
	// if (res.length > 0)
	// {
	// $ ('.Change_Left').html (templateForGraphFileFunc ("projects",
	// {
	// 'projects' : res
	// }));
	// // 判断globalProjectInstanceIdFile是否还包含在左侧树中
	// var hasId = false;
	// if (globalProjectInstanceIdFile != 0)
	// {
	// for (var i = 0; i < res.length; i++)
	// {
	// if (res[i].iid == globalProjectInstanceIdFile)
	// {
	// hasId = true;
	// break;
	// }
	// }
	// if (hasId == false)
	// {
	// globalProjectInstanceIdFile = 0;
	// }
	// }
	// if (globalProjectInstanceIdFile == 0)
	// {
	// $ ('.Change_Left li:first-child').trigger ('click');
	// }
	// else
	// {
	// $ ('.Change_Left li[data-instance-id="' + globalProjectInstanceIdFile +
	// '"]').trigger ('click');
	// }
	// // $('.Change_Left
	// // li[data-instance-id="'+globalProjectInstanceIdFile+'"]').trigger('click');
	// // 左侧树复选框选中还原至刷新前
	// cHKBoxIdsSetCheckedForGraphFile (idsIn);
	// }
	// else
	// {
	// $ ('.Change_Left').html ("");
	// if(graph!=null)
	// {
	// removeCellsFunForGraphFile();
	// }
	//			
	// }
	// });
}

/**
 * 获得左侧树选中的复选框id
 * 
 * @returns {String}
 */
function getCHKBoxIdsForGraphFile() {
	var ids = "";
	// var ck = document.getElementsByName ("checkboxForLeft");
	// var num = 0;
	// for (i = 0; i < ck.length; i++)
	// {
	// if (ck[i].checked)
	// {
	// if (num == 0)
	// {
	// ids = ck[i].value;
	// }
	// else
	// {
	// ids = ids + "," + ck[i].value;
	// }
	// num = num + 1;
	// }
	// }

	var records = treeLeftFile.getView().getChecked(), names = [];
	Ext.Array.each(records, function(rec) {
		if (ids == '') {
			ids = rec.get('flowId');
		} else {
			ids = ids + "," + rec.get('flowId');
		}

	});
	return ids;
}
/**
 * 页面刷新复选框选中
 * 
 * @param idsIn
 */
function cHKBoxIdsSetCheckedForGraphFile(idsIn) {
	var ck = document.getElementsByName("checkboxForLeft");
	for (i = 0; i < ck.length; i++) {
		if (idsIn.indexOf(ck[i].value) > -1) {
			ck[i].checked = true;
		}
	}
}

/**
 * 获取阶段信息
 */
function getPhaseForGraphFile() {
	Ext.Ajax.request({
		url : 'queryPhase.do',
		method : 'POST',
		params : {
			start : 0,
			limit : 0
		},
		success : function(response, options) {
			phaseDataListFile = Ext.decode(response.responseText).dataList;
			// 获取完阶段信息后开始加载mxgraph相关内容
			mainForGraphFile(document.getElementById('graphContainerFile'), document
					.getElementById('outlineContainerFile'), document
					.getElementById('toolbarContainerFile'), document
					.getElementById('sidebarContainerFile'), document
					.getElementById('statusContainerFile'));
		},
		failure : function(result, request) {
		}

	});
}

/**
 * 图形化界面初始化方法
 */
function mainForGraphFile(container, outline, toolbar, sidebar, status) {

	// Checks if the browser is supported
	if (!mxClient.isBrowserSupported()) {
		// Displays an error message if the browser is not supported.
		// mxUtils.error ('Browser is not supported!', 200, false);
		Ext.Msg.alert('提示', '当前浏览器不支持此功能!');
	} else {
		// 自定义连线图标
		mxConnectionHandler.prototype.connectImage = new mxImage(
				'mxgraph-master/examples/images/connector.gif', 14, 14);
		// Assigns some global constants for general behaviour, eg. minimum
		// size (in pixels) of the active region for triggering creation of
		// new connections, the portion (100%) of the cell area to be used
		// for triggering new connections, as well as some fading options for
		// windows and the rubberband selection.
		mxConstants.MIN_HOTSPOT_SIZE = 16;
		mxConstants.DEFAULT_HOTSPOT = 1;

		// Enables guides
		mxGraphHandler.prototype.guidesEnabled = true;

		// Alt disables guides
		mxGuide.prototype.isEnabledForEvent = function(evt) {
			return !mxEvent.isAltDown(evt);
		};

		// Enables snapping waypoints to terminals
		mxEdgeHandler.prototype.snapToTerminals = true;

		// Workaround for Internet Explorer ignoring certain CSS directives
		if (mxClient.IS_QUIRKS) {
			document.body.style.overflow = 'hidden';
			new mxDivResizer(container);
			new mxDivResizer(outline);
			// new mxDivResizer (toolbar);
			// new mxDivResizer (sidebar);
			// new mxDivResizer (status);
		}

		// Creates a wrapper editor with a graph inside the given container.
		// The editor is used to create certain functionality for the
		// graph, such as the rubberband selection, but most parts
		// of the UI are custom in this example.
		editorFile = new mxEditor();
		parent.editorSon = editorFile;
		graphFile = editorFile.graph;
		model = graphFile.getModel();

		// Disable highlight of cells when dragging from toolbar
		graphFile.setDropEnabled(false);
		// graph.setTooltips(false);

		// Uses the port icon while connections are previewed
		/*
		 * //连线样式与图标相同 graph.connectionHandler.getConnectImage = function
		 * (state) { return new mxImage (state.style[mxConstants.STYLE_IMAGE],
		 * 16, 16); };
		 */

		// Centers the port icon on the target port
		graphFile.connectionHandler.targetConnectImage = true;

		// Does not allow dangling edges
		// 连线是否必须连接至节点
		graphFile.setAllowDanglingEdges(false);

		// Sets the graph container and configures the editorFile
		editorFile.setGraphContainer(container);
		// 键盘热键控制
		/*
		 * var config = mxUtils.load
		 * ('mxgraph-master/examples/editors/config/keyhandler-commons.xml')
		 * .getDocumentElement (); editor.configure (config);
		 */

		// Defines the default group to be used for grouping. The
		// default group is a field in the mxEditor instance that
		// is supposed to be a cell which is cloned for new cells.
		// The groupBorderSize is used to define the spacing between
		// the children of a group and the group bounds.
		// 不允许重复连线
		graphFile.setMultigraph(false);
		// 不允许自己连自己
		graphFile.setAllowLoops(false);

		var group = new mxCell('Group', new mxGeometry(), 'group');
		group.setVertex(true);
		group.setConnectable(false);
		editorFile.defaultGroup = group;
		editorFile.groupBorderSize = 20;

		// Disables drag-and-drop into non-swimlanes.
		graphFile.isValidDropTarget = function(cell, cells, evt) {
			return this.isSwimlane(cell);
		};

		// Disables drilling into non-swimlanes.
		graphFile.isValidRoot = function(cell) {
			return this.isValidDropTarget(cell);
		};

		// Does not allow selection of locked cells
		graphFile.isCellSelectable = function(cell) {
			return !this.isCellLocked(cell);
		};

		// Returns a shorter label if the cell is collapsed and no
		// label for expanded groups
		/*
		 * graph.getLabel = function (cell) { var tmp =
		 * mxgraphFile.prototype.getLabel.apply (this, arguments); // "supercall"
		 * 
		 * if (this.isCellLocked (cell)) { // Returns an empty label but makes
		 * sure an HTML // element is created for the label (for event //
		 * processing wrt the parent label) return ''; } else if
		 * (this.isCellCollapsed (cell)) { var index = tmp.indexOf ('</h1>');
		 * 
		 * if (index > 0) { tmp = tmp.substring (0, index + 5); } }
		 * 
		 * return tmp; }
		 */

		graphFile.getLabel = function(cell) {
			var label = (this.labelsVisible) ? this.convertValueToString(cell)
					: '';
			var geometry = this.model.getGeometry(cell);

			if (!this.model.isCollapsed(cell)
					&& geometry != null
					&& (geometry.offset == null || (geometry.offset.x == 0 && geometry.offset.y == 0))
					&& this.model.isVertex(cell) && geometry.width >= 2) {
				var style = this.getCellStyle(cell);
				var fontSize = style[mxConstants.STYLE_FONTSIZE]
						|| mxConstants.DEFAULT_FONTSIZE;
				var max = geometry.width / (fontSize * 1.5);

				if (max < label.length) {
					return label.substring(0, max) + '...';
				}
			}

			return label;
		};

		// Disables HTML labels for swimlanes to avoid conflict
		// for the event processing on the child cells. HTML
		// labels consume events before underlying cells get the
		// chance to process those events.
		//
		// NOTE: Use of HTML labels is only recommended if the specific
		// features of such labels are required, such as special label
		// styles or interactive form fields. Otherwise non-HTML labels
		// should be used by not overidding the following function.
		// See also: configureStylesheetForGraphFile.
		graphFile.isHtmlLabel = function(cell) {
			return !this.isSwimlane(cell);
		};

		// To disable the folding icon, use the following code:
		/*
		 * graph.isCellFoldable = function(cell) { return false; }
		 */

		// Shows a "modal" window when double clicking a vertex.
		graphFile.dblClick = function(evt, cell) {
			// Do not fire a DOUBLE_CLICK event here as mxEditor will
			// consume the event and start the in-place editor.
			if (this.isEnabled() && !mxEvent.isConsumed(evt) && cell != null
					&& this.isCellEditable(cell)) {
				if (this.model.isEdge(cell)) // || !this.isHtmlLabel (cell))
				{
					// 连线编辑
					// this.startEditingAtCell (cell);
				} else {
					cellObj = cell;
					if (cell.style != 'beginStyle' && cell.style != 'endStyle') {
						if (cell.style == 'scriptServiceStyle'
								|| cell.style == 'ftpDownloadStyle') {
							winStepFile = Ext.create('Ext.window.Window', {
								title : '步骤详情',
								modal : true,
								closeAction : 'destroy',
								constrain : true,
								autoScroll : true,
								width : contentPanel.getWidth(),
								height : contentPanel.getHeight(),
								minWidth : 350,
								draggable : false,// 禁止拖动
								resizable : false,// 禁止缩放
								layout : 'fit',
								listeners : {
									"beforedestroy" : function(obj) {
										// globalProjectInstanceIdFile=0;
										reloadForGraphFile();
									}
								},
								loader : {
									url : 'forwardscriptserver.do',
									params : {
										flowId : globalProjectInstanceIdFile,
										coatid : cell.coatId,
										flag : cell.flag,
										isWin : 1
									/*
									 * insId : globalProjectInstanceIdFile, serner :
									 * cell.id
									 */
									},
									autoLoad : true,
									scripts : true
								}
							}).show();
						} else if (cell.style == 'usertaskStyle') {
							cellObj = cell;
							personExcute_window = Ext.create('Ext.window.Window', {
										title : '人工提醒',
										autoScroll : true,
										modal : true,
										closeAction : 'destroy',
										buttonAlign : 'center',
										draggable : false,// 禁止拖动
										resizable : false,// 禁止缩放
										width : 600,
										height : 400,
										loader : {
											url : 'scriptServiceUT.do',
											params : {
												actNo : cell.id,
												coatid:cell.coatId,
												flowId:globalProjectInstanceIdFile,
												flag : 1,
												state:cell.state 
											},
											autoLoad : true,
//											autoDestroy : true,
											scripts : true
										}
									}).show();
						} else {
							winStepFile = Ext
									.create(
											'Ext.window.Window',
											{
												title : '步骤详情',
												autoScroll : true,
												modal : true,
												closeAction : 'destroy',
												buttonAlign : 'center',
												draggable : false,// 禁止拖动
												resizable : false,// 禁止缩放
												width : contentPanel.getWidth(),
												height : contentPanel
														.getHeight(),
												listeners : {
													"beforedestroy" : function(
															obj) {
														reloadForGraphFile();
													}
												},
												loader : {
													url : "page/mxgraph/monitorHomePageTemplateSUSwindow.jsp",
													autoLoad : true,
													autoDestroy : true,
													scripts : true
												}
											}).show();
							// openWindw ();
						}

					}
					// var content = document.createElement('div');
					// content.innerHTML = this.convertValueToString(cell);
					// showModalWindow(this, 'Properties', content, 400, 300);
				}
			}

			// Disables any default behaviour for the double click
			mxEvent.consume(evt);
		};

		// Enables new connections
		// 节点之间可以连接
		graphFile.setConnectable(false);

		// Adds all required styles to the graph (see below)
		// 增加样式
		configureStylesheetForGraphFile(graphFile);

		// Creates a new DIV that is used as a toolbar and adds
		// toolbar buttons.
		var spacer = document.createElement('div');
		spacer.style.display = 'inline';
		spacer.style.padding = '8px';

		// toolbar.appendChild (spacer.cloneNode (true));
		// addToolbarButton (editor, toolbar, 'save', '保存',
		// 'mxgraph-master/examples/images/export1.png');

		// ---

		// Adds toolbar buttons into the status bar at the bottom
		// of the window.
		// addToolbarButton(editor, status, 'collapseAll', 'Collapse All',
		// 'mxgraph-master/examples/images/navigate_minus.png', true);
		// addToolbarButton(editor, status, 'expandAll', 'Expand All',
		// 'mxgraph-master/examples/images/navigate_plus.png', true);

		// status.appendChild (spacer.cloneNode (true));

		// addToolbarButton(editor, status, 'enterGroup', 'Enter',
		// 'mxgraph-master/examples/images/view_next.png', true);
		// addToolbarButton(editor, status, 'exitGroup', 'Exit',
		// 'mxgraph-master/examples/images/view_previous.png', true);

		// status.appendChild (spacer.cloneNode (true));
		// 放大缩小功能
		/*
		 * addToolbarButton (editor, status, 'zoomIn', '',
		 * 'mxgraph-master/examples/images/zoom_in.png', true); addToolbarButton
		 * (editor, status, 'zoomOut', '',
		 * 'mxgraph-master/examples/images/zoom_out.png', true);
		 * addToolbarButton (editor, status, 'actualSize', '',
		 * 'mxgraph-master/examples/images/view_1_1.png', true);
		 * addToolbarButton (editor, status, 'fit', '',
		 * 'mxgraph-master/examples/images/fit_to_size.png', true);
		 */
		// Creates the outline (navigator, overview) for moving
		// around the graph in the top, right corner of the window.
		var outln = new mxOutline(graphFile, outline);

		// To show the images in the outline, uncomment the following code
		// outln.outline.labelsVisible = true;
		// outln.outline.setHtmlLabels(true);

		// Fades-out the splash screen after the UI has been loaded.
		var splash = document.getElementById('splash');
		if (splash != null) {
			try {
				mxEvent.release(splash);
				mxEffects.fadeOut(splash, 100, true);
			} catch (e) {

				// mxUtils is not available (library not loaded)
				splash.parentNode.removeChild(splash);
			}
		}
		initFunForGraphFile(graphFile);
	}

};
/**
 * 初始化方法
 */
function initFunForGraphFile() {
	// 修改将原图加载
	if (globalSysIdFile > 0) {
		loadGraphForGraphFile(globalSysIdFile);
	}

};
/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphForGraphFile(instanceID) {
	if (isRefreshFile) {
		graphFile.getModel().beginUpdate();
		try {
			var doc = mxUtils
					.load(encodeURI("getFlowXmlScriptService.do?instanceID="
							+ instanceID));
			var dec = new mxCodec(doc);
			dec.decode(doc.getDocumentElement(), graphFile.getModel());
		} finally {
			graphFile.getModel().endUpdate();
			imageIdFile = instanceID;
		}
	}
	setStatusForGraphFile();
}
/**
 * TODO:设置状态
 */
function setStatusForGraphFile() {
	// 遍历所有节点
	var root2 = graphFile.getModel().getRoot();
	var count = graphFile.model.getChildCount(root2);

	$
			.getJSON(
					'getMonitorStepStatisticForScriptService.do',
					{
						instanceId : globalProjectInstanceIdFile
					},
					function(res) {
						for (var i = 0; i < count; i++) {
							var cells = root2.getChildAt(i);
							var counts = cells.getChildCount();
							for (var j = 0; j < counts; j++) {
								var cellss = cells.getChildAt(j);
								if (cellss.style == 'beginStyle'
										|| cellss.style == 'endStyle'
										|| cellss.value == '') {
									// cellss.state = 0;
									// setCellWarningFunForGraphFile(graph,cellss,'完成','images/uthome/finished.gif');
									continue;
								}
								var ismatching = false;
								for (i = 0; i < res.length; i++) {
									if (cellss.id == res[i].actNo) {
										ismatching = true;
										cellss.coatId = res[i].coatId;
										cellss.flag = res[i].flag;
										cellss.state = res[i].state;
										if (res[i].state == 30) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'异常',
													'images/mxgraphImages/step-fail-running.png');
											break;
										} else if (res[i].state == 50) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'异常运行',
													'images/mxgraphImages/step-fail-running.png');
											break;
										} else if (res[i].state == 0) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'人工提醒',
													'images/mxgraphImages/remind.png');
											break;
										} else if (res[i].state == 10) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'运行',
													'images/mxgraphImages/step-running.png');
											break;
										} else if (res[i].state == 20) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'已完成',
													'images/mxgraphImages/step-finish.png');
											break;
										} else if (res[i].state == 40) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'异常完成',
													'images/mxgraphImages/step-fail-finish.png');
											break;
										} else if (res[i].state == 60) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'已终止',
													'images/mxgraphImages/step-kill.png');
											break;
										}else if (res[i].state == 5) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'已略过',
													'images/mxgraphImages/step-fail-finish.png');
											break;
										} else if (res[i].state == -1) {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'未运行',
													'images/mxgraphImages/step-notrun.png');
											break;
										} else {
											setCellWarningFunForGraphFile(graphFile, cellss,
													'未运行',
													'images/mxgraphImages/step-notrun.png');
											break;
										}

									}

								}
								if (cellss.value != '' && !ismatching) {
									setCellWarningFunForGraphFile(graphFile, cellss, '未运行',
											'images/mxgraphImages/step-notrun.png');
								}
							}
						}

					});

}
/**
 * 清除页面所有节点
 */
function removeCellsFunForGraphFile() {
	var records = [];// 存放选中记录
	// 遍历所有节点
	var root2 = graphFile.getModel().getRoot();
	var count = graphFile.model.getChildCount(root2);
	for (var i = 0; i < count; i++) {
		var cells = root2.getChildAt(i);
		var counts = cells.getChildCount();
		for (var j = 0; j < counts; j++) {
			var cellss = cells.getChildAt(j);
			records.push(cellss);
		}
	}
	graphFile.removeCells(records);
}
/**
 * 设置节点状态图标
 */
function setCellWarningFunForGraphFile(graphIn, cellssIn, titleNameIn, imgUrl) {
	// 清除原状态图标
	graphIn.setCellWarning(cellssIn, null);
	// 增加新状态图标
	graphIn.setCellWarning(cellssIn, titleNameIn, new mxImage(imgUrl, 20, 20),
			true);
}
/**
 * 初始化页面节点样式
 */
function configureStylesheetForGraphFile(graph) {

	var style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
	style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
	style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTSTYLE] = 0;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
	style[mxConstants.STYLE_RESIZABLE] = '0';// 不可缩放
	style[mxConstants.STYLE_MOVABLE] = '0';// 不可拖动
	graph.getStylesheet().putDefaultVertexStyle(style);

	/*
	 * style = new Object (); style[mxConstants.STYLE_SHAPE] =
	 * mxConstants.SHAPE_IMAGE; style[mxConstants.STYLE_PERIMETER] =
	 * mxPerimeter.RectanglePerimeter; style[mxConstants.STYLE_IMAGE] =
	 * 'images/mxgraphImages/begin.png'; style[mxConstants.STYLE_FONTCOLOR] =
	 * '#000000'; style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	 * graph.getStylesheet ().putCellStyle ('beginStyle', style);
	 * 
	 * style = new Object(); style[mxConstants.STYLE_SHAPE] =
	 * mxConstants.SHAPE_IMAGE; style[mxConstants.STYLE_PERIMETER] =
	 * mxPerimeter.RectanglePerimeter; style[mxConstants.STYLE_IMAGE]
	 * ='images/mxgraphImages/end.png'; style[mxConstants.STYLE_FONTCOLOR] =
	 * '#000000'; style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	 * graph.getStylesheet().putCellStyle('endStyle', style);
	 */

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('beginStyle', style);

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('endStyle', style);

	style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet().putCellStyle('cmdStyle', style);

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('scriptServiceStyle', style);

	// style = new Object ();
	// style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	// style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	// style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/ut.png';
	// style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	// style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	// graph.getStylesheet ().putCellStyle ('usertaskStyle', style);

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('usertaskStyle', style);

	style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/ftpDownload.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet().putCellStyle('ftpDownloadStyle', style);

	/*
	 * for (var i = 0; i < phaseDataListFile.length; i++) { var objL =
	 * phaseDataListFile[i]; var styleName = objL.iid + 'Style'; var imagesUrl =
	 * "images/mxgraphImages/" + objL.iid + ".jpg";
	 * 
	 * style = new Object (); style[mxConstants.STYLE_SHAPE] =
	 * mxConstants.SHAPE_IMAGE; style[mxConstants.STYLE_PERIMETER] =
	 * mxPerimeter.RectanglePerimeter; style[mxConstants.STYLE_IMAGE] =
	 * imagesUrl; style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	 * style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	 * graph.getStylesheet ().putCellStyle (styleName, style); }
	 */

	style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
	style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
	style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_STARTSIZE] = '30';
	style[mxConstants.STYLE_FONTSIZE] = '16';
	style[mxConstants.STYLE_FONTSTYLE] = 1;
	graph.getStylesheet().putCellStyle('group', style);

	style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_FONTSIZE] = '10';
	style[mxConstants.STYLE_FONTSTYLE] = 2;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
	graph.getStylesheet().putCellStyle('port', style);

	style = graph.getStylesheet().getDefaultEdgeStyle();
	style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_STROKEWIDTH] = '1';
	style[mxConstants.STYLE_STROKECOLOR] = '#595758';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_EDGE] = mxConstants.EDGESTYLE_ELBOW;
	// style[mxConstants.STYLE_EDGE] = mxEdgeStyle.EntityRelation;
};
/** 打开提醒任务处理窗口* */
function openUTWindowForGraphFile(iflowid) {
	personExcute_window = Ext.create('Ext.window.Window', {
		title : '人工处理',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : 600,
		height : 400,
		loader : {
			url : "scriptServiceUT.do",
			params : {
				coatid : iflowid,
				flag : flag
			},
			autoLoad : true,
			autoDestroy : true,
			scripts : true
		}
	}).show();
}
/** 提醒任务处理后刷新当前页面状态* */
function reloadGroupForGraphFile() {
	setTimeout(reloadForGraphFile, 1500);
}