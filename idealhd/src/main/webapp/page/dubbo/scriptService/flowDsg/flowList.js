/*******************************************************************************
 * 流程定制列表
 ******************************************************************************/
var scriptNameObj;
var scriptNameStore;
var scriptContentObj;
var stepNameObj;
var flowStore;
Ext.onReady (function ()
{
	 // 清理主面板的各种监听时间
	  destroyRubbish();
	/** 树数据Model* */
	Ext.define ('flowModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'instanceName',
		        type : 'string'
		    },{
		        name : 'iid',
		        type : 'long'
		    }
	    ]
	});
	/** 树数据源* */
	flowStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad :true,
	    autoDestroy : true,
	    model : 'flowModel',
	    pageSize : 50,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getFlowListScriptService.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
//	templateStore.on ('beforeload', function (store, options)
//			{
//				var new_params =
//				{
//						ieai_sus_script_iid : scriptNameObj.getValue()
//				};
//				Ext.apply (templateStore.proxy.extraParams, new_params);
//			});
	/** 树列表columns* */
	var flowColumns = [
	        {
	            text : '序号',
	            // align : 'center',
	            width : 35,
	            xtype : 'rownumberer'
	        }, 
	        {
	            text : '实例名',
	            dataIndex : 'instanceName',
	            flex : 1,
	            renderer: function(value,metaData,record){
	            	return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlow('+ record.get('iid') +',\''+value+'\',1)">'+ value +'</a>';
	            }
	        },
            {
                text : '操作',
                dataIndex : 'instanceName',
                sortable: false,
                hideable:false,//是否可以手动隐藏
                // flex : 1,
                width : 150,
                align : 'center',
                renderer: function(value,metaData,record){
	            	return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlow('+ record.get('iid') +',\''+value+'\',1)">查看</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlowForUpdate('+ record.get('iid') +',\''+value+'\',0)">编辑</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="deleteFun('+ record.get('iid') +')">删除</a>';
	            }
            }
	];
	var bsPageBar = Ext.create ('Ext.PagingToolbar',
			{
			    store : flowStore,
			    dock : 'bottom',
			    displayInfo : true,
			    emptyMsg : "没有记录"
			});
	var insertButton = Ext.create('Ext.Button', {
	    text : '新增',
	    width : 70,
	    height : 22,
	    margin : '0 0 0 5',
	    textAlign : 'center',
	    cls : 'Common_Btn',
	    handler : function() {
	    	forwardRMFlowForCreate(0,'',0);
	      }
	  });
	/** 树列表panel* */
	var flowGrid = Ext.create ('Ext.grid.Panel',
	{
		width:'100%',
	    store : flowStore,
	    border : true,
	    columnLines : true,
	    flex : 2,
	    columns : flowColumns,
	    bbar : bsPageBar,
	    collapsible : false,
	    dockedItems : [
	       		    {
	       		        xtype : 'toolbar',
	       		        height:40,
	       		        items : [insertButton
	       		        ]
	       		    }
	       	    ]
	});
	var middlePanel=Ext.create('Ext.Panel', {
//		title:'提示信息',
		width:'100%',
		height : contentPanel.getHeight ()-35,
		split: true,
//		region: 'south',
		layout:'fit',
		border : false,	
//		titleAlign : 'left',
		items : [
		         flowGrid
	            ]
	});
	
    // 主Panel
    var MainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "mainpanelE",
		width : '100%',
		height : '100%',
//		overflowY:'scroll',
		border : false,
		bodyPadding : 5,
		items : [ middlePanel ]
	});
    contentPanel.on('resize', function() {
    	middlePanel.setWidth('100%');
    	middlePanel.setHeight(contentPanel.getHeight ()-35);
      });
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(MainPanel);
        if (Ext.isIE) {
          CollectGarbage();
        }
      });
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
});
/**
 * 编辑或查看流程
 * @param iidIn
 * @param instanceNameIn
 * @param showOnly
 */
function forwardRMFlow(iidIn,instanceNameIn,showOnly){
	  destroyRubbish();
	  contentPanel.getLoader().load (
				{
				    url : 'flowCustomizedInitScriptService.do',
				    params :
				    {
				    	iid : iidIn,
				    	instanceName:instanceNameIn,
				    	showOnly:showOnly
				    },
				    scripts : true
				});
	  if(Ext.isIE){
	      CollectGarbage(); 
	  }
	}
/**
 * 判断是否有新建模板权限
 * @param iidIn
 * @param instanceNameIn
 * @param showOnly
 */
	function forwardRMFlowForCreate(iidIn,instanceNameIn,showOnly){
	Ext.Ajax.request (
			{
			    url : 'checkCreateFlowRoleScriptService.do',
			    params :
			    {
			        instanceId:iidIn
			    },
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
			    	if(Ext.decode (response.responseText).success==true)
			    		{
			    		forwardRMFlow(iidIn,instanceNameIn,showOnly);
			    		}
				    
			    },
				failure : function(result, request) {
					secureFilterRs(result,"保存失败！");
				}
			});
	}
	/**
	 * 判断是否有编辑模板权限
	 * @param iidIn
	 * @param instanceNameIn
	 * @param showOnly
	 */
function forwardRMFlowForUpdate(iidIn,instanceNameIn,showOnly){
	Ext.Ajax.request (
			{
			    url : 'checkModifyFlowRoleScriptService.do',
			    params :
			    {
			        instanceId:iidIn
			    },
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
			    	if(Ext.decode (response.responseText).success==true)
			    		{
			    		forwardRMFlow(iidIn,instanceNameIn,showOnly);
			    		}
				    
			    },
				failure : function(result, request) {
					secureFilterRs(result,"保存失败！");
				}
			});
	}
/**
 * 删除流程
 * @param iidIn
 */
function deleteFun(iidIn){
	  Ext.Ajax.request (
				{
				    url : 'deleteFlowInstanceVersionForScriptService.do',
				    params :
				    {
				        instanceId:iidIn
				    },
				    method : 'POST',
				    async : false,
				    success : function (response, options)
				    {
				    	Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
				    	if(Ext.decode (response.responseText).success==true)
				    		{
				    		flowStore.reload();
				    		}
					    
				    },
				    failure : function(result, request) {
						secureFilterRs(result,"保存失败！");
					}
				});
	}