/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSFLOWCUSTOMEDITTEST;
var cellObjGFSSFLOWCUSTOMEDITTEST;
var phaseDataListGFSSFLOWCUSTOMEDITTEST;
var editorGFSSFLOWCUSTOMEDITTEST;
var graphGFSSFLOWCUSTOMEDITTEST;
var modelGFSSFLOWCUSTOMEDITTEST;
var cmdNameCountGFSSFLOWCUSTOMEDITTEST = 1;
var actNameCountMapGFSSFLOWCUSTOMEDITTEST = {};
actStartInfo['GFSSFLOWCUSTOMEDITTEST'] = {};

Ext.onReady(function() {
    try {
        if (configwindowFlowGFSSFLOWCUSTOMEDITTEST != null) {
            configwindowFlowGFSSFLOWCUSTOMEDITTEST.destroy();
        }
        if (cellObjGFSSFLOWCUSTOMEDITTEST != null) {
            cellObjGFSSFLOWCUSTOMEDITTEST.destroy();
        }
        if (phaseDataListGFSSFLOWCUSTOMEDITTEST != null) {
            phaseDataListGFSSFLOWCUSTOMEDITTEST = null;
        }
        if (editorGFSSFLOWCUSTOMEDITTEST != null) {
            editorGFSSFLOWCUSTOMEDITTEST.destroy();
        }
        if (graphGFSSFLOWCUSTOMEDITTEST != null) {
            graphGFSSFLOWCUSTOMEDITTEST.destroy();
        }
        if (modelGFSSFLOWCUSTOMEDITTEST != null) {
            modelGFSSFLOWCUSTOMEDITTEST = null;
        }
    } catch(err) {}

    getPhaseGFSSFLOWCUSTOMEDITTEST();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSFLOWCUSTOMEDITTEST(container, outline, toolbar, sidebar, status) {

    if (!mxClient.isBrowserSupported()) {
        Ext.Msg.alert('提示', '当前浏览器不支持此功能!');
    } else {
        //自定义连线图标
        mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
        mxConstants.MIN_HOTSPOT_SIZE = 16;
        mxConstants.DEFAULT_HOTSPOT = 1;

        mxGraphHandler.prototype.guidesEnabled = true;

        mxGuide.prototype.isEnabledForEvent = function(evt) {
            return ! mxEvent.isAltDown(evt);
        };

        mxEdgeHandler.prototype.snapToTerminals = true;

        if (mxClient.IS_QUIRKS) {
            document.body.style.overflow = 'hidden';
            new mxDivResizer(container);
            new mxDivResizer(outline);
            new mxDivResizer(toolbar);
            new mxDivResizer(sidebar);
            new mxDivResizer(status);
        }

        editorGFSSFLOWCUSTOMEDITTEST = new mxEditor();
        parent.editorSonGFSSFLOWCUSTOMEDITTEST = editorGFSSFLOWCUSTOMEDITTEST;
        graphGFSSFLOWCUSTOMEDITTEST = editorGFSSFLOWCUSTOMEDITTEST.graph;
        modelGFSSFLOWCUSTOMEDITTEST = graphGFSSFLOWCUSTOMEDITTEST.getModel();
        
        graphGFSSFLOWCUSTOMEDITTEST.setTooltips(false);

        graphGFSSFLOWCUSTOMEDITTEST.setDropEnabled(false);

        graphGFSSFLOWCUSTOMEDITTEST.connectionHandler.targetConnectImage = true;

        //连线是否必须连接至节点
        graphGFSSFLOWCUSTOMEDITTEST.setAllowDanglingEdges(false);

        editorGFSSFLOWCUSTOMEDITTEST.setGraphContainer(container);
        //键盘热键控制
        var config = mxUtils.load('mxgraph-master/examples/editors/config/keyhandler-commons.xml').getDocumentElement();
        editorGFSSFLOWCUSTOMEDITTEST.configure(config);

        //不允许重复连线
        graphGFSSFLOWCUSTOMEDITTEST.setMultigraph(false);
        //不允许自己连自己
        graphGFSSFLOWCUSTOMEDITTEST.setAllowLoops(false);

        var group = new mxCell('Group', new mxGeometry(), 'group');
        group.setVertex(true);
        group.setConnectable(false);
        editorGFSSFLOWCUSTOMEDITTEST.defaultGroup = group;
        editorGFSSFLOWCUSTOMEDITTEST.groupBorderSize = 20;

        graphGFSSFLOWCUSTOMEDITTEST.isValidDropTarget = function(cell, cells, evt) {
            return this.isSwimlane(cell);
        };

        graphGFSSFLOWCUSTOMEDITTEST.isValidRoot = function(cell) {
            return this.isValidDropTarget(cell);
        }

        graphGFSSFLOWCUSTOMEDITTEST.isCellSelectable = function(cell) {
            return ! this.isCellLocked(cell);
        };

        graphGFSSFLOWCUSTOMEDITTEST.getLabel = function(cell) {
            var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
            var geometry = this.model.getGeometry(cell);

            if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null || (geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) && geometry.width >= 2) {
                var style = this.getCellStyle(cell);
                var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
                var max = geometry.width / (fontSize * 1.5);

                if (max < label.length) {
                    return label.substring(0, max) + '...';
                }
            }

            return label;
        };

        graphGFSSFLOWCUSTOMEDITTEST.isWrapping = function(cell) {
            return this.model.isCollapsed(cell);
        };

        graphGFSSFLOWCUSTOMEDITTEST.isHtmlLabel = function(cell) {
            return ! this.isSwimlane(cell);
        }

        graphGFSSFLOWCUSTOMEDITTEST.dblClick = function(evt, cell) {

            if (this.isEnabled() && !mxEvent.isConsumed(evt) && cell != null && this.isCellEditable(cell)) {
                if (this.model.isEdge(cell)) {} else {
                    cellObjGFSSFLOWCUSTOMEDITTEST = cell;
                    if (cell.style != 'beginStyle' && cell.style != 'endStyle') {
                        if (cell.style == 'scriptServiceStyle') {
                            
                            if(parent.actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
                            	// 首先检查是否有新版本
    							Ext.Ajax.request (
    							{
    							    url : 'scriptServiceHasNewVersion.do',
    							    params : {
    							        oldServiceId:cell.scriptId
    							    },
    							    method : 'POST',
    							    async : false,
    							    success : function (response, options)
    							    {
    								    if(Ext.decode (response.responseText).success && Ext.decode (response.responseText).hasNewVersion) {
    								    	Ext.MessageBox.buttonText.yes = "确定"; 
    							    		Ext.MessageBox.buttonText.no = "取消"; 
    							    		Ext.Msg.confirm("请确认", "该活动绑定的原子脚本有新版本，是否更新？", function(id){
    							    			if(id=='yes') {
    							    				openNewVersionWindowGFSSFLOWCUSTOMEDITTEST (cell.scriptId, Ext.decode (response.responseText).version);
    							    			} else {
    							    				openExecScriptWindwGFSSFLOWCUSTOMEDITTEST();
    							    			}
    							    		});
    								    } else {
    								    	openExecScriptWindwGFSSFLOWCUSTOMEDITTEST();
    								    }
    							    },
    							    failure : function (result, request)
    							    {
    							    }
    							});
                            	
							} else {
								openEditScriptWindwGFSSFLOWCUSTOMEDITTEST (false);
								
							}
                        } else if (cell.style == 'usertaskStyle') {
                            //UT提醒任务
                            openUTWindwGFSSFLOWCUSTOMEDITTEST();
                        } else if(cell.style=='callflowStyle') {
                        	if(actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
                        		graphViewStack = new Array();
    							stackFlowView = Ext.create('widget.window', {
    					            title: '详细信息',
    					            closable: true,
    					            closeAction: 'destroy',
    					            width: contentPanel.getWidth(),
    					            minWidth: 350,
    					            height: contentPanel.getHeight(),
    					            draggable: true,
    					            resizable: false,
    					            modal: true,
    					            loader: {
    					            	url : 'flowWinViewer.do', 
    					                params: {
    					                    iid: cell.scriptId,
    					                    flag: 0,
    					                    actionType:'exec',
    					                    pageFrom: 'GFSSFLOWCUSTOMEDITTEST',
    										isShowInWindow: 1,
    										parentMxIid: cell.mxIid,
    										isStack: true
    					                },
    					                autoLoad: true,
    					                scripts: true
    					            }
    					        }).show();
                        	} else {
                        		openEditScriptWindwGFSSFLOWCUSTOMEDITTEST (true);
                        	}
                        	
		    				return ;
						} else {
                            //模板
                            openWindwGFSSFLOWCUSTOMEDITTEST();
                        }

                    }
                }
            }

            mxEvent.consume(evt);
        };

        //节点之间可以连接
        graphGFSSFLOWCUSTOMEDITTEST.setConnectable(true);

        //增加样式
        configureStylesheetGFSSFLOWCUSTOMEDITTEST(graphGFSSFLOWCUSTOMEDITTEST);

        actNameCountMapGFSSFLOWCUSTOMEDITTEST['scriptServiceStyle'] = 0;
        actNameCountMapGFSSFLOWCUSTOMEDITTEST['usertaskStyle'] = 0;
        actNameCountMapGFSSFLOWCUSTOMEDITTEST['callflowStyle'] = 0;
        if(actionTypeGFSSFLOWCUSTOMEDITTEST!='exec'){
        	addSidebarIconGFSSFLOWCUSTOMEDITTEST (graphGFSSFLOWCUSTOMEDITTEST, sidebar, '基础脚本', 'images/mxgraphImages/basescript.png', 'scriptServiceStyle',0);
        	addSidebarIconGFSSFLOWCUSTOMEDITTEST (graphGFSSFLOWCUSTOMEDITTEST, sidebar, '人工提醒', 'images/mxgraphImages/ut.png', 'usertaskStyle',1);
        	addSidebarIconGFSSFLOWCUSTOMEDITTEST (graphGFSSFLOWCUSTOMEDITTEST, sidebar, '作业调用', 'images/mxgraphImages/callflow.png', 'callflowStyle',3);
        }

        var spacer = document.createElement('div');
        spacer.style.display = 'inline';
        spacer.style.padding = '8px';

        editorGFSSFLOWCUSTOMEDITTEST.addAction('export',
        function(editor, cell) {
            var textarea = document.createElement('textarea');
            textarea.style.width = '400px';
            textarea.style.height = '400px';
            var enc = new mxCodec(mxUtils.createXmlDocument());
            var node = enc.encode(editor.graph.getModel());
            textarea.value = mxUtils.getPrettyXml(node);
            showModalWindowGFSSFLOWCUSTOMEDITTEST(graphGFSSFLOWCUSTOMEDITTEST, 'XML', textarea, 410, 440);
        });

        editorGFSSFLOWCUSTOMEDITTEST.addAction('deleteBefore',
        function(editor, cell) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if (cells[i].style == 'beginStyle') {
                    Ext.Msg.alert('提示', '不能删除<开始>节点！');
                    return false;
                }
                if (cells[i].style == 'endStyle') {
                    Ext.Msg.alert('提示', '不能删除<结束>节点！');
                    return false;
                }
            }
            editor.execute('delete');
        });

        addToolbarButtonGFSSFLOWCUSTOMEDITTEST(editorGFSSFLOWCUSTOMEDITTEST, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITTEST(editorGFSSFLOWCUSTOMEDITTEST, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITTEST(editorGFSSFLOWCUSTOMEDITTEST, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITTEST(editorGFSSFLOWCUSTOMEDITTEST, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);

        var outln = new mxOutline(graphGFSSFLOWCUSTOMEDITTEST, outline);

        var splash = document.getElementById('splashGFSSFLOWCUSTOMEDITTEST');
        if (splash != null) {
            try {
                mxEvent.release(splash);
                mxEffects.fadeOut(splash, 100, true);
            } catch(e) {

                splash.parentNode.removeChild(splash);
            }
        }

        graphGFSSFLOWCUSTOMEDITTEST.popupMenuHandler.factoryMethod = function(menu, cell, evt) {
            return createPopupMenuGFSSFLOWCUSTOMEDITTEST(graphGFSSFLOWCUSTOMEDITTEST, menu, cell, evt);
        };

        initFunGFSSFLOWCUSTOMEDITTEST(graphGFSSFLOWCUSTOMEDITTEST);
    }

};

/**增加右键删除菜单**/
function createPopupMenuGFSSFLOWCUSTOMEDITTEST(graph, menu, cell, evt) {
    if (cell != null) {
        menu.addItem('删除', 'images/delete.png',
        function() {
            editorGFSSFLOWCUSTOMEDITTEST.execute('deleteBefore');
        });
    }
};

/**
 * 初始化方法
 */
function initFunGFSSFLOWCUSTOMEDITTEST(graph) {
    if (parent.iidGFSSFLOWCUSTOMEDITTEST > 0) {
        loadGraphGFSSFLOWCUSTOMEDITTEST(graph);
        
        if(actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
        	Ext.Ajax.request({
                url: 'getFlowCustomTemplateData.do',
                method: 'POST',
                params: {
                	iid: parent.customIdGFSSFLOWCUSTOMEDITTEST,
                    flag: 0
                },
                success: function(response, options) {
                    var dataS = Ext.decode(response.responseText).data;
                    actStartInfo['GFSSFLOWCUSTOMEDITTEST'] = JSON.parse(dataS);
                    
                    
                    var root2FlowWindow = modelGFSSFLOWCUSTOMEDITTEST.getRoot ();
            		var count = modelGFSSFLOWCUSTOMEDITTEST.getChildCount (root2FlowWindow);
            		for (var i = 0; i < count; i++)
            		{
            			var cells = root2FlowWindow.getChildAt (i);
            			var counts = cells.getChildCount ();
            			for (var j = 0; j < counts; j++)
            			{
            				var cellss = cells.getChildAt (j);
            				if (!modelGFSSFLOWCUSTOMEDITTEST.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
            				{
            					cellss.mxIid = parent.iidGFSSFLOWCUSTOMEDITTEST+":"+cellss.id;
            					if(!actStartInfo['GFSSFLOWCUSTOMEDITTEST'].hasOwnProperty(cellss.mxIid)) {
            						if(parseInt(cellss.phaseId)==0) {
            							actStartInfo['GFSSFLOWCUSTOMEDITTEST'][cellss.mxIid] = {
            									'actNo': cellss.id,
            									'actType': parseInt(cellss.phaseId),
            									'actName': cellss.value,
            									'isShutdown': cellss.isShutdown
            							};
            						} else if(parseInt(cellss.phaseId)==1) {
            							actStartInfo['GFSSFLOWCUSTOMEDITTEST'][cellss.mxIid] = {
            									'actNo': cellss.id,
            									'actType': parseInt(cellss.phaseId),
            									'actName': cellss.value,
            									'message': cellss.ireminfo
            							};
            						}
            					}
            				}
            			}
            		}
                },
                failure: function(result, request) {
                	Ext.Msg.alert ('提示', '获取模板数据失败!');
                }
            });
        }
    } else {
        addBeginEndCellGFSSFLOWCUSTOMEDITTEST(graph, "开始", "beginStyle", 1, 1);
        addBeginEndCellGFSSFLOWCUSTOMEDITTEST(graph, "结束", "endStyle", 150, 150);
    }

};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSFLOWCUSTOMEDITTEST(editor, toolbar, action, label, image, isTransparent) {
    if (image != null) {
        var img = document.createElement('img');
        img.setAttribute('src', image);
        img.style.width = '74px';
        img.style.height = '30px';
        img.style.verticalAlign = 'middle';
        img.title = label;
        img.style.marginRight = '10px';
        img.style.marginTop = '2px';
    }
    mxEvent.addListener(img, 'click',
    function(evt) {
        if ('delete' == action) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if ('开始' == cells[i].value) {
                    Ext.Msg.alert('提示', '不删除<开始>节点！');
                    return false;
                }
            }
        }

        editor.execute(action);
    });
    toolbar.appendChild(img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSFLOWCUSTOMEDITTEST(graph, title, content, width, height) {
    var background = document.createElement('div');
    background.style.position = 'absolute';
    background.style.left = '0px';
    background.style.top = '0px';
    background.style.right = '0px';
    background.style.bottom = '0px';
    background.style.background = 'black';
    mxUtils.setOpacity(background, 50);
    document.body.appendChild(background);

    if (mxClient.IS_IE) {
        new mxDivResizer(background);
    }

    var x = Math.max(0, document.body.scrollWidth / 2 - width / 2);
    var y = Math.max(10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
    var wnd = new mxWindow(title, content, x, y, width, height, false, true);
    wnd.setClosable(true);

    // Fades the background out after after the window has been closed
    wnd.addListener(mxEvent.DESTROY,
    function(evt) {
        graph.setEnabled(true);
        mxEffects.fadeOut(background, 50, true, 10, 30, true);
    });

    graph.setEnabled(false);
    graph.tooltipHandler.hide();
    wnd.setVisible(true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSFLOWCUSTOMEDITTEST(graph, label, styleName, w, h) {

    var parent = graph.getDefaultParent();
    var model = graph.getModel();

    var v1 = null;

    model.beginUpdate();
    try {
        v1 = graph.insertVertex(parent, null, label, w, h, 50, 25, styleName);
        v1.setConnectable(true);
    } finally {
        model.endUpdate();
    }

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSFLOWCUSTOMEDITTEST(graph, sidebar, label, image, styleName, phaseId) {
    var funct = function(graph, evt, cell, x, y) {
        var parent = graph.getDefaultParent();
        var model = graph.getModel();

        var v1 = null;

        model.beginUpdate();
        try {
            actNameCountMapGFSSFLOWCUSTOMEDITTEST[styleName] = ++actNameCountMapGFSSFLOWCUSTOMEDITTEST[styleName];
            v1 = graph.insertVertex(parent, null, label + actNameCountMapGFSSFLOWCUSTOMEDITTEST[styleName], x, y, 90, 32, styleName);
            v1.setConnectable(true);
            v1.phaseId = phaseId;

        } finally {
            model.endUpdate();
        }
        graph.setSelectionCell(v1);
    }

    var img = document.createElement('img');
    img.setAttribute('src', image);
    img.style.width = '80px';
    img.style.height = '32px';
    img.title = label;
    img.style.marginLeft = '10px';
    img.style.marginBottom = '15px';

    sidebar.appendChild(img);

    var dragElt = document.createElement('div');
    dragElt.style.border = 'dashed black 1px';
    dragElt.style.width = '120px';
    dragElt.style.height = '120px';

    var ds = mxUtils.makeDraggable(img, graph, funct, dragElt, 0, 0, true, true);
    ds.setGuidesEnabled(true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSFLOWCUSTOMEDITTEST(graph) {

    var style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
    style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
    style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTSTYLE] = 0;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
    style[mxConstants.STYLE_RESIZABLE] = '0'; //不可缩放
    graph.getStylesheet().putDefaultVertexStyle(style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('beginStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('endStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
    graph.getStylesheet().putCellStyle('cmdStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
    style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
    style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_STARTSIZE] = '30';
    style[mxConstants.STYLE_FONTSIZE] = '16';
    style[mxConstants.STYLE_FONTSTYLE] = 1;
    graph.getStylesheet().putCellStyle('group', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_FONTCOLOR] = '#774400';
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_FONTSIZE] = '10';
    style[mxConstants.STYLE_FONTSTYLE] = 2;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
    graph.getStylesheet().putCellStyle('port', style);

    style = graph.getStylesheet().getDefaultEdgeStyle();
    style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_STROKEWIDTH] = '1';
    style[mxConstants.STYLE_STROKECOLOR] = '#595758';
    style[mxConstants.STYLE_ROUNDED] = false;
    style[mxConstants.STYLE_EDGE] = mxConstants.EDGESTYLE_ELBOW;

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('scriptServiceStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('usertaskStyle', style);
    
    style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSFLOWCUSTOMEDITTEST() {
    graphGFSSFLOWCUSTOMEDITTEST.getModel().beginUpdate();
    try {
        var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag=" + flagGFSSFLOWCUSTOMEDITTEST + "&instanceID=" + parent.iidGFSSFLOWCUSTOMEDITTEST));
        var dec = new mxCodec(doc);
        dec.decode(doc.getDocumentElement(), graphGFSSFLOWCUSTOMEDITTEST.getModel());
    } finally {
        graphGFSSFLOWCUSTOMEDITTEST.getModel().endUpdate();
    }
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSFLOWCUSTOMEDITTEST() {
    configwindowFlowGFSSFLOWCUSTOMEDITTEST = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/mxgraph/flowCustomizedWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSFLOWCUSTOMEDITTEST() {
    configwindowFlowGFSSFLOWCUSTOMEDITTEST = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: false,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITTEST/flowCustomizedExecScriptWindow.jsp",
            params: {
                flag: flagGFSSFLOWCUSTOMEDITTEST
            },
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}

function openEditScriptWindwGFSSFLOWCUSTOMEDITTEST (type)
{
	configwindowFlowGFSSFLOWCUSTOMEDITTEST = Ext.create ('Ext.window.Window', {
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
	    loader : {
	        url : "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITTEST/flowCustomizedEditScriptWindow.jsp",
	        params :
	        {
	            actType : type
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function openNewVersionWindowGFSSFLOWCUSTOMEDITTEST (oldServiceId, version)
{
	Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'isshare',type : 'string'},
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'}
	    ]
	});
		
	var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 15,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'scriptService/queryServiceForMySelfForNewVersion.do?serviceId='+oldServiceId,
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var scriptServiceReleaseColumns = [{
		text : '序号',
		xtype : 'rownumberer',
		width : 40
	}, 
	{
	    text : '服务主键',
	    dataIndex : 'iid',
	    width : 40,
	    hidden : true
	}, 
	{
		text : '服务名称',
	    dataIndex : 'serviceName',
	    width : 200,flex:1
	},
	{
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 260,flex:1
	}, 
	{
	    text : '一级分类',
	    dataIndex : 'buss',
	    width : 200,flex:1
	}, 
	{
		text : '二级分类',
		dataIndex : 'bussType',
		width : 250,flex:1
	},
	{
	    text : '脚本类型',
	    dataIndex : 'scriptType',
	    width : 80,
	    renderer:function(value,p,record,rowIndex){
	    	var isflow = record.get('isflow');
	    	var backValue = "";
			if (value == "sh") {
				backValue = "shell";
			} else if (value == "perl") {
				backValue = "perl";
			} else if (value == "py") {
				backValue = "python";
			} else if (value == "bat") {
				backValue = "bat";
			} else if (value == "sql") {
				backValue = "sql";
			}
			if (isflow == '1') {
				backValue = "组合";
			}
			return backValue;
	    }
	}, 
	{
		text : '适用平台',
		dataIndex : 'platForm',
		width : 100
	}, 
	{
		text : '版本',
		dataIndex : 'version',
		width : 100
	}, 
	{
		text : '脚本状态',
		dataIndex : 'status',
		width : 100,
		renderer:function(value,p,record,rowIndex){
	    	if(value==-1) {
	    		return '<font color="#F01024">草稿</font>';
	    	} else if (value==1) {
	    		return '<font color="#0CBF47">已上线</font>';
	    	} else if (value==2) {
	    		return '<font color="#FFA602">审核中</font>';
	    	} else if (value==3) {
	    		return '<font color="#13B1F5">已共享</font>';
	    	} else if (value==9) {
	    		return '<font color="">已共享未发布</font>';
	    	} else {
	    		return '<font color="#CCCCCC">未知</font>';
	    	}
	    }
	},{
		menuDisabled: true,
        sortable: false,
        xtype: 'actioncolumn',
        width: 50,
        items: [{
            iconCls: 'monitor_search',
            tooltip: '查看脚本详情',
            handler: function(grid, rowIndex, colIndex) {
                var rec = grid.getStore().getAt(rowIndex);
                Ext.create('widget.window', {
		            title: '详细信息',
		            closable: true,
		            closeAction: 'destroy',
		            width: contentPanel.getWidth(),
		            minWidth: 350,
		            height: contentPanel.getHeight(),
		            resizable: false,
		            modal: true,
		            loader: {
		                url: 'queryOneServiceForView.do',
		                params: {
		                    iid: rec.get('iid'),
		                    flag: 0,
		                    hideReturnBtn: 1
		                },
		                autoLoad: true,
		                scripts: true
		            }
		        }).show();
            }
        }]
	}];

	var pageBar = Ext.create('Ext.PagingToolbar', {
	  	store: scriptServiceReleaseStore,
	    dock: 'bottom',
	    displayInfo: true,
	    emptyMsg:'找不到任何记录'
	});
	
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		autoScroll: true,
	    store : scriptServiceReleaseStore,
	    bbar : pageBar,
	    height: contentPanel.getHeight ()-40,
	    columnLines : true,
	    columns : scriptServiceReleaseColumns,
	    dockedItems: [{
            xtype: 'toolbar',
            height: 45,
            items: [{
                xtype: 'button',
                text: '选择',
                cls: 'Common_Btn',
                handler: function() {
                	var ss = scriptServiceReleaseGrid.getSelectionModel().getSelection();
            		if(ss.length !=1){
            			Ext.MessageBox.alert("提示", "请选择一条记录！");
            			return ;
            		}
            		var chosedServiceId = ss[0].data.iid;
            		var actId = cellObjGFSSFLOWCUSTOMEDITTEST.id;
            		Ext.Ajax.request({
            		    url : 'scriptHasVersion.do',
            		    method : 'POST',
            		    params : {
            		    	serviceId: iidGFSSFLOWCUSTOMEDITTEST
            		    },
            		    success: function(response, opts) {
            		        var hasVersion = Ext.decode(response.responseText).hasVersion;
            		        if(hasVersion==1) {
            		        	Ext.Msg.confirm("请确认", "该作业已经上线，如果修改该活动绑定的原子脚本，该作业将会生成无版本号版本！", function(id){
            		    			if(id=='yes'){
            		    				Ext.Ajax.request({
                    		    		    url : 'copyFlowServiceWithChangeActService.do',
                    		    		    method : 'POST',
                    		    		    params : {
                    		    		    	flowServiceId: iidGFSSFLOWCUSTOMEDITTEST,
                    		    		    	actId: actId,
                    		    		    	newActServiceId: chosedServiceId
                    		    		    },
                    		    		    success: function(response, opts) {
                    		    		    	var success = Ext.decode(response.responseText).success;
                    		    		    	if(success) {
                    		    		    		iidGFSSFLOWCUSTOMEDITTEST = Ext.decode(response.responseText).newFlowServiceId;
                    		    		    		cellObjGFSSFLOWCUSTOMEDITTEST.scriptId = chosedServiceId;
                    		    		    	}
                    		    		    	var message = Ext.decode(response.responseText).message;
                    		    		    	Ext.Msg.alert ('提示', message);
                    		    		    	sssss.close();
                    		    		    },
                    		    		    failure: function(result, request) {
                    		    		    	secureFilterRs(result,"操作失败！");
                    		    		    	return;
                    		    		    }
                    		    	    });
            		    			}
            		        	});
            		    	} else {
            		    		Ext.Ajax.request({
            		    		    url : 'updateFlowActScriptService.do',
            		    		    method : 'POST',
            		    		    params : {
            		    		    	flowServiceId: iidGFSSFLOWCUSTOMEDITTEST,
            		    		    	actId: actId,
            		    		    	newActServiceId: chosedServiceId
            		    		    },
            		    		    success: function(response, opts) {
            		    		    	var message = Ext.decode(response.responseText).message;
            		    		    	Ext.Msg.alert ('提示', message);
            		    		    	cellObjGFSSFLOWCUSTOMEDITTEST.scriptId = chosedServiceId;
            		    		    	sssss.close();
            		    		    },
            		    		    failure: function(result, request) {
            		    		    	secureFilterRs(result,"操作失败！");
            		    		    	return;
            		    		    }
            		    	    });
            		    	}
            		    },
            		    failure: function(result, request) {
            		    	secureFilterRs(result,"操作失败！");
            		    	return;
            		    }
            	    });
                }
            },{
                xtype: 'button',
                text: '刷新',
                cls: 'Common_Btn',
                handler: function() {
                	pageBar.moveFirst();
                }
            }, '->', {
            	xtype: 'tbtext',
            	text: '当前版本：' + version
            },{
                xtype: 'button',
                text: '当前版本详情',
                cls: 'Common_Btn',
                handler: function() {
                	Ext.create('widget.window', {
    		            title: '详细信息',
    		            closable: true,
    		            closeAction: 'destroy',
    		            width: contentPanel.getWidth(),
    		            minWidth: 350,
    		            height: contentPanel.getHeight(),
    		            resizable: false,
    		            modal: true,
    		            loader: {
    		                url: 'queryOneServiceForView.do',
    		                params: {
    		                    iid: oldServiceId,
    		                    flag: 0,
    		                    hideReturnBtn: 1
    		                },
    		                autoLoad: true,
    		                scripts: true
    		            }
    		        }).show();
                }
            }]
        }]
	});
	
	var sssss = Ext.create ('Ext.window.Window', {
		title : '选择原子脚本',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : contentPanel.getWidth (),
		height : contentPanel.getHeight (),
		items: [scriptServiceReleaseGrid]
	}).show ();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSFLOWCUSTOMEDITTEST() {
    configwindowFlowGFSSFLOWCUSTOMEDITTEST = Ext.create('Ext.window.Window', {
        title: '人工提醒配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: 580,
        height: 215,
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITTEST/scriptServiceUTWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSFLOWCUSTOMEDITTEST() {
    //更新名后续刷新才能显示
    graphGFSSFLOWCUSTOMEDITTEST.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSFLOWCUSTOMEDITTEST() {
    mainGFSSFLOWCUSTOMEDITTEST(document.getElementById('graphContainerGFSSFLOWCUSTOMEDITTEST'), document.getElementById('outlineContainerGFSSFLOWCUSTOMEDITTEST'), document.getElementById('toolbarContainer'), document.getElementById('sidebarContainerGFSSFLOWCUSTOMEDITTEST'), document.getElementById('statusContainerGFSSFLOWCUSTOMEDITTEST'));
}

function saveFlowGFSSFLOWCUSTOMEDITTEST(showSuccessMsg)
{
	var enc = new mxCodec (mxUtils.createXmlDocument ());
	var node = enc.encode (editorGFSSFLOWCUSTOMEDITTEST.graph.getModel ());
	var bussId;
	var bussTypeId;
	var bussName;
	var bussTypeName;
	var serviceName;
	var funcDescText;
	var serviceId = parent.iidGFSSFLOWCUSTOMEDITTEST;
	
	Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: serviceId
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                serviceName = data.serviceName;
                funcDescText = data.funcDesc;
            }
        },
        failure: function(result, request) {}
    });
	
	var customName = parent.customNameObjOutSideGFSSFLOWCUSTOMEDITTEST.getValue();
    if (customName.trim() == '') {
		Ext.Msg.alert ('提示', '请填写模板名称!');
		return false;
	}
    var res = false;
    Ext.Ajax.request({
        url: 'checkCustomTemplateNameIsExist.do',
        params: {
            iid: parent.customIdGFSSFLOWCUSTOMEDITTEST,
        	customName: customName,
            flag: flagGFSSFLOWCUSTOMEDITTEST
        },
        async: false,
        method: 'POST',
        success: function(response, options) {
            if (!Ext.decode(response.responseText).success) {
                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
            } else {
            	Ext.Ajax.request({
                    url: 'scriptStatus.do',
                    params: {
                        serviceId: serviceId
                    },
                    async: false,
                    method: 'POST',
                    success: function(response, options) {
                    	var status = Ext.decode(response.responseText).status;
        		        if(status==2) { // 审核中
        		        	Ext.Msg.alert ('提示', '该作业处于审核中，作业流程图不可以修改！');
        		    	} else {
        		    		Ext.Ajax.request({
        	                    url: 'saveFlowCustomTemplateOnlyCustomName.do',
        	                    method: 'POST',
        	                    async: false,
        	                    params: {
        	                    	iid: parent.customIdGFSSFLOWCUSTOMEDITTEST,
        	                    	customName: customName,
        	                        flag: parent.flagGFSSFLOWCUSTOMEDITTEST
        	                    },
        	                    success: function(response, options) {
        	                    	
        	                    },
        	                    failure: function(result, request) {
        	                        
        	                    }
        	                });
        		    		
        		    		Ext.Ajax.request ({
        		    		    url : 'mxgraphSaveForFlowScriptService.do',
        		    		    params : {
        		    		        xmlString : mxUtils.getPrettyXml (node),
        		    		        bussId:bussId,
        		    		        bussTypeId:bussTypeId,
        		    		        bussName:bussName,
        		    		        bussTypeName:bussTypeName,
        		    		        serviceName : serviceName,
        		    		        serviceId:serviceId,
        		    		        funcDesc: funcDescText
        		    		    },
        		    		    method : 'POST',
        		    		    async : false,
        		    		    success : function (response, options)
        		    		    {
        		    			    if(Ext.decode (response.responseText).success) {
        		    			    	parent.iidGFSSFLOWCUSTOMEDITTEST=Ext.decode (response.responseText).serviceId;
        		    			    	var message = '作业保存成功！';
        		    			    	res = true;
        		    		        	Ext.Ajax.request({
        		    	        			url: 'validFlowCustomTemplateData.do',
        		    	        			method: 'POST',
        		    	        			async: false,
        		    	        			params: {
        		    	        				iid: customIdGFSSFLOWCUSTOMEDITTEST,
        		    	        				flag: 0
        		    	        			},
        		    	        			success: function(response, options) {
        		    	        				var success = Ext.decode(response.responseText).success;
        		    	        				if(success) {
        		    	        					if(showSuccessMsg) {
        		    	                				Ext.Msg.alert('提示', message);
        		    	                			}
        		    	        				} else {
        		    	        					var errorMessage = Ext.decode(response.responseText).message;
        		    	        					Ext.Msg.alert('提示', message+'<br>由于作业流程图有变更，会造成模板启动出错，问题如下：<br>'+errorMessage);
        		    	                			return;
        		    	        				}
        		    	        			},
        		    	        			failure: function(result, request) {
        		    	        				Ext.Msg.alert('提示', message+'<br>校验模板启动数据信息失败！');
        		    	        			}
        		    	        		});
        		    			    } else {
        		    			    	Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
        		    			    }
        		    		    },
        		    		    failure : function (result, request) {
        		    		    }
        		    		});
        		    	}
                    }
            	});
            }
        },
        failure: function(result, request) {}
    });
    
    return res;
}

function isJobConfigOk (serviceId, allStartParams) {
	var isJobConfigOk = true;
	Ext.Ajax.request({
		url :'checkJobConfigIsOK.do',
		method: 'POST',
		async: false,
		params:{
			serviceId: serviceId,
			data:JSON.stringify(allStartParams),
			flag: 0
		},
		success: function ( response, options) 
		{
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (!success) {
				isJobConfigOk = false;
				Ext.Msg.alert('提示', message);
			}
		},
		failure: function ( result, request){
			isJobConfigOk = false;
			Ext.Msg.alert('提示', "检查作业配置出现问题！");
		}
	});
	return isJobConfigOk;
}

function saveCustomTemplateGFSSFLOWCUSTOMEDITTEST() {
	var allStartParams = actStartInfo['GFSSFLOWCUSTOMEDITTEST'];
	if(!isJobConfigOk(iidGFSSFLOWCUSTOMEDITTEST, allStartParams)) {
		return;
	}
	
    var customName = parent.customNameObjOutSideGFSSFLOWCUSTOMEDITTEST.getValue();
    if (customName.trim() == '')
	{
		Ext.Msg.alert ('提示', '请填写模板名称!');
		return null;
	}

    Ext.Ajax.request({
        url: 'checkCustomTemplateNameIsExist.do',
        params: {
            iid: parent.customIdGFSSFLOWCUSTOMEDITTEST,
        	customName: customName,
            flag: flagGFSSFLOWCUSTOMEDITTEST
        },
        method: 'POST',
        success: function(response, options) {
            if (!Ext.decode(response.responseText).success) {
                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
            } else {
                Ext.Ajax.request({
                    url: 'saveFlowCustomTemplate.do',
                    method: 'POST',
                    params: {
                    	iid: parent.customIdGFSSFLOWCUSTOMEDITTEST,
                    	customName: customName,
                    	serviceId: iidGFSSFLOWCUSTOMEDITTEST,
                        data: JSON.stringify(allStartParams),
                        flag: parent.flagGFSSFLOWCUSTOMEDITTEST
                    },
                    success: function(response, options) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                        	Ext.Msg.alert('提示', "模板保存成功！");
                        } else {
                        	Ext.Msg.alert('提示', "模板保存失败！");
                        }
                    },
                    failure: function(result, request) {
                    	Ext.Msg.alert('提示', "模板保存失败！");
                    }
                });
            }
        },
        failure: function(result, request) {
        	Ext.Msg.alert('提示', "检查模板名称是否存在失败！");
        }
    });
}

function startCustomTemplateGFSSFLOWCUSTOMEDITTEST() {
	var serviceId = parent.iidGFSSFLOWCUSTOMEDITTEST;
	var allStartParams = actStartInfo['GFSSFLOWCUSTOMEDITTEST'];
	
	if(!isJobConfigOk(serviceId, allStartParams)) {
		return;
	}
	
	Ext.Ajax.request({
		 url :'startScriptServiceFlow.do',
			method: 'POST',
			params:{
				serviceId:serviceId,
				data:JSON.stringify(allStartParams),
				flag:parent.flagGFSSFLOWCUSTOMEDITTEST
			},
			success: function ( response, options) 
			{
		        var success = Ext.decode(response.responseText).success;
		        var message = Ext.decode(response.responseText).message;
		        if (success) {
		        	Ext.Msg.alert('提示', message);
		        }
			},
			failure: function ( result, request){
				Ext.Msg.alert('提示', "启动模板失败！");
			}
	 });
}