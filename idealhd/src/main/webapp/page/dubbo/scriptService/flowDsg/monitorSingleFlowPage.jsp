<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/monitorSingleFlowPage.js"></script>
<script type="text/javascript">
var tempData = {};
var oldTempData = {};
<%
String menuid = request.getParameter("menuId");
String divID = request.getParameter("divID");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
	oldTempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>

tempData.namespace = 'monitorSingleFlow'+ tempData.divID;
oldTempData.namespace = 'monitorSingleFlow'+ oldTempData.divID;
tempData.load = true;
</script>

<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptWebView.js"></script>
<body>
<div class="Change_Panel customize_panel_back" id="monitorSingleFlowPage_m">
<div>
<table cellpadding="0" cellspacing="0" border="0" class="Change_Body">
	<tr >
		<td align="left">
			<div id="scriptViewDiv_<%=divID%>" class="monitor_graph"></div>
		</td>
	</tr>
	<tr>
		<td colspan="2"><div id="package_bottomMonitorSingle_<%=divID%>"></div></td>
	</tr>
</table>
</div>
</div>
</body>