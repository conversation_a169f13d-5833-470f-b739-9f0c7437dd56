/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.audiFormPanel', {
    extend: 'Ext.form.Panel',
    alias: 'widget.audiFormPanel',

    requires: [
        'Ext.button.Button',
        'Ext.toolbar.Spacer'
    ],

    height: '',
    title: '',

    layout: {
		type: 'hbox',
		padding:'5',
		align:'top'
	},
	
    initComponent: function() {
        var me = this;
       
        var jspParms = me.jspParms;
        
        var isSaveTemplateCk = Ext.create('Ext.form.field.Checkbox', {
    		checked : false,
    		boxLabel: '是否保存为常用任务',
    		margin:'3 5 0 5'
    	});
        var isTimerTask = Ext.create('Ext.form.field.Checkbox', {
        	checked : false,
        	boxLabel: '定时任务',
    		margin:'3 5 0 5',
    		hidden: !(jspParms.isScriptConvertToFlow=='true'),
    		listeners: { //监听 
				       change:function(el,checked){
				             if(checked){
				             	cycleExecCronText.show(); 
				             	selectCronButton.show();
				             }else{
				             	cycleExecCronText.hide();
				             	selectCronButton.hide();
				             }
				        }
		    }
        });
         
         /** 选择生成周期表达式按钮 **/
		var selectCronButton = Ext.create ("Ext.Button",
		{
			id : 'selectCronButton_id',
		    //cls : 'Common_Btn',
		    text : "选择",
		    hidden: !(jspParms.isScriptConvertToFlow=='true') || !(isTimerTask.getValue()),
		    handler : selectExecCron
		});
		
		function selectExecCron()
		{
			var creatCronWin;
			if (creatCronWin == undefined || !creatCronWin.isVisible()) {
				creatCronWin = Ext.create('Ext.window.Window', {
					title : '定时任务参数设置',
					modal : true,
					id : 'creatCronWin',
					closeAction : 'destroy',
					constrain : true,
					autoScroll : true,
					upperWin : creatCronWin,
					width : contentPanel.getWidth() - 350,
					height : contentPanel.getHeight() - 30,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					loader : {
						url : 'cronMainForSpdb.do',
	//					params : {
	//						sysType : sysType,
	//						state : state,
	//						errorTaskId : errorTaskId,
	//						pageType : pageType
	//					},
						autoLoad : true,
						autoDestroy : true,
						scripts : true
					}
				});
			}
			creatCronWin.show();
		}
		
        var cycleExecCronText = new Ext.form.TextField (
    			{
    			    fieldLabel : '执行时间',
    			    labelWidth : 58,
    			    labelAlign : 'right',
    			    id : 'cycleExecCronText',
					name: 'cycleExecCronText',
    			    width : '20%',
    			    readOnly : true,
    			    hidden: !(jspParms.isScriptConvertToFlow=='true') || !(isTimerTask.getValue())
    			});
        
    	var submitButton   = Ext.create('Ext.Button', { name : 'submitButton', text : '提交', handler :  submitFn });
    	var baseInfoButton = Ext.create('Ext.Button', { name : 'baseInfoButton', text : '基本信息' , handler: me.baseInfoFn});
    	var backButton     = Ext.create('Ext.Button', { name : 'backButton', text : '返回', handler : backFn});
    	
    	function checkJobConfigIsOK(editerName,serviceId,flag){
    		var isJobConfigOk = true;
    		Ext.Ajax.request({
    			url :'checkJobConfigIsOK.do',
    			method: 'POST',
    			async: false,
    			params:{
    				serviceId:serviceId,
    				data:JSON.stringify(actStartInfo[editerName]),
    				flag: parseInt(flag)
    			},
    			success: function ( response, options) 
    			{
    				var success = Ext.decode(response.responseText).success;
    				var message = Ext.decode(response.responseText).message;
    				if (!success) {
    					isJobConfigOk = false;
    					Ext.Msg.alert('提示', message);
    				}
    			},
    			failure: function ( result, request){
    				isJobConfigOk = false;
    				Ext.Msg.alert('提示', "检查作业配置出现问题！");
    			}
    		});
    		return isJobConfigOk;
    	}
    	
    	
    	function submitFn(){
    		var phoneNum;
    		var isReady = checkJobConfigIsOK(jspParms.editerName,jspParms.serviceId,jspParms.flag);
			var version_flag = true;//单号显示标识
			if(typeof taskType === 'undefined' || !scriptOddNumberSwitch){
				version_flag = true;
		    }else if(jspParms.execUserSwitch=='true'&&taskType=='taskCreate'){
		    	version_flag = false;
		    }
    		if(isReady) {
    			Ext.define('AuditorModel', {
    			    extend: 'Ext.data.Model',
    			    fields : [ {
    			      name : 'loginName',
    			      type : 'string'
    			    }, {
    			      name : 'fullName',
    			      type : 'string'
    			    }, {
    				      name : 'phoneNum',
    				      type : 'string'
    				 }]
    			  });
    			
    			var auditorStore_tap = Ext.create('Ext.data.Store', {
    			    autoLoad: false,
    			    model: 'AuditorModel',
    			    proxy: {
    			      type: 'ajax',
    			      url: 'getExecAuditorList.do?scriptLevel=' + jspParms.scriptLevel,
    			      reader: {
    			        type: 'json',
    			        root: 'dataList'
    			      }
    			    }
    			  });
    			var execStore = Ext.create('Ext.data.Store', {
    			    autoLoad: false,
    			    model: 'AuditorModel',
    			    proxy: {
    			      type: 'ajax',
    			      url: 'getExecUserList.do',
    			      reader: {
    			        type: 'json',
    			        root: 'dataList'
    			      }
    			    }
    			  });
    			var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
    			    fieldLabel: "审核人",
    			    store: auditorStore_tap,
    			    queryMode: 'local',
    			    width: 385,
    			    padding: '0 2 0 8',
    			    displayField: 'fullName',
    			    valueField: 'loginName',
    			    hidden:jspParms.scriptLevel==0?true:false,
    			    labelWidth : 65,
    			    editable : true,
    				labelAlign : 'right',
    				listeners: { //监听 
    			        render : function(combo) {//渲染 
    			            combo.getStore().on("load", function(s, r, o) { 
    			                combo.setValue(r[0].get('loginName'));//第一个值 
    			                phoneNum = r[0].get('phoneNum');
    			        	    validCode.setValue(phoneNum);
    			            }); 
    			        },
    			        select : function(combo, records, eOpts){ 
    						var fullName = records[0].raw.fullName;
    						combo.setRawValue(fullName);
    						 phoneNum =records[0].raw.phoneNum;
    						 validCode.setValue(phoneNum);
    					},
//    			        blur:function(combo, records, eOpts){
//							var displayField =auditorComBox_tap.getRawValue();
//							if(!Ext.isEmpty(displayField)){
//								//判断输入是否合法标志，默认false，代表不合法
//								var flag = false;
//								//遍历下拉框绑定的store，获取displayField
//								auditorStore_tap.each(function (record) {
//									//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//								    var data_fullName = record.get('fullName');
//								    if(data_fullName == displayField){
//								    	flag =true;
//								    	combo.setValue(record.get('loginName'));
//								    }
//								});
//								if(!flag){
//								 	Ext.Msg.alert('提示', "输入的审核人非法");
//								 	auditorComBox_tap.setValue("");
//								 	return;
//								} 
//							}
//							
//						},
						beforequery: function(e) {
			                var combo = e.combo;
			                if (!e.forceAll) {
			                    var value = Ext.util.Format.trim(e.query);
			                    combo.store.filterBy(function(record, id) {
			                        var text = record.get(combo.displayField);
			                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
			                    });
			                    combo.expand();
			                    return false;
			                }
			            }
    			    } 
    			  });
    			var execComBox_tap = Ext.create('Ext.form.ComboBox', {
    			    fieldLabel: "执行人",
    			    store: execStore,
    			    queryMode: 'local',
    			    width: 385,
    			    padding: '0 2 0 8',
    			    displayField: 'fullName',
    			    valueField: 'loginName',
    			    labelWidth : 65,
    			    editable : true,
    			    hidden:!(jspParms.execUserSwitch=='true')||jspParms.scriptLevel==0?true:false,
    				labelAlign : 'right'
    			  });
    			execStore.load({
				    callback : function (records, operation, success)
				    {
				    	execComBox_tap.setValue (jspParms.loginUser);
				    }
			    });
    			function CurentTime()
    		    { 
    		        var now = new Date();
    		        
    		        var year = now.getFullYear();       //年
    		        var month = now.getMonth() + 1;     //月
    		        var day = now.getDate();            //日
    		        
    		        var hh = now.getHours();            //时
    		        var mm = now.getMinutes();          //分
    		        var ss = now.getSeconds();           //秒
    		        
    		        var clock = year;
    		        
    		        if(month < 10){
    		        	
    		        	clock += "0";
    		        }else{
    		        	clock += "";
    		        }
    		        
    		        clock +=month;
    		        
    		        if(day < 10){
    		        	clock += "0";
    		        }else{
    		        	clock += "";
    		        }
    		            
    		            
    		        clock += day;
    		        
    		        if(hh < 10){
    		        	clock += "0";
    		        }else{
    		        	clock += "";
    		        }
    		            
    		            
    		        clock += hh;
    		        if (mm < 10) 
    		        {
    		        	clock += '0'; 
    		        	}
    		        else{
    		        	clock += "";
    		        }
    		        clock += mm ; 
    		         
    		        if (ss < 10) {
    		        	clock += '0'; 
    		        }
    		        else{
    		        	clock += "";
    		        }
    		        clock += ss; 
    		        return(clock); 
    		    }    			var taskName = new Ext.form.TextField({
    				name: 'taskName',
    				fieldLabel: '任务名称',
    				emptyText: '',
    				padding: '3 2 0 8',
    				labelWidth : 65,
    				labelAlign : 'right',
    				value: jspParms.taskName==''||jspParms.taskName==undefined?jspParms.serviceName+'_'+CurentTime():jspParms.taskName,
    				width: 385
    			});
    			var execDesc = Ext.create('Ext.form.field.TextArea', {
    		        fieldLabel: '执行描述',
    		        labelWidth: 65,
    		        padding: '0 2 0 8',
    		        height: 60,
    		        maxLength: 2000,
    		       // value: publishDescText,
    		        width: 385,
    		        labelAlign : 'right',
    		        autoScroll: true
    		    });
    			var butterflyVerison = new Ext.form.TextField({
    				name: 'butterflyversion',
    				fieldLabel: '单号',
    				padding: '3 2 0 8',
    				hidden:version_flag,
    				emptyText: '',
    				labelWidth : 65,
    				labelAlign : 'right',
    				width: 385
    			});
    			var checkVersion=new  Ext.form.Checkbox({   
    	            id:"checkVersion",               
		    	    name:"checkVersion",
		    	    boxLabel:"单号补添:" ,
		    	    boxLabelAlign:"before",
		    	    labelWidth:65,
		    	    padding: '0 2 0 10',
		    	    hidden:version_flag
    			});
    			var validCode = Ext.create ('Ext.form.TextField',
    					{
    						fieldLabel:'手机号',
    						labelWidth : 75,
    						width: 270,
    						margin:'5 0 0 0',
    						id : 'myvalidCode',
    						readOnly: true,
    						labelAlign : 'right',
    						hidden :(jspParms.scriptLevel!=0 && audiCodeSwitch && smsSwitchNum!='0')?false:true,
    						regex: /^[1][3,4,5,7,8][0-9]{9}$/,
    					    xtype : 'textfield'
    					});
    				
    			var authCode = Ext.create ('Ext.form.TextField',
    				{
    					fieldLabel:'验证码',
    					labelWidth: 80,
    					id : 'mauthCode',
    					hidden :(jspParms.scriptLevel!=0 && audiCodeSwitch && smsSwitchNum!='0')?false:true,
    					labelAlign : 'right',
    					width: 385,
    					emptyText : '--请填写验证码--',
    					regex: /^\d{6}$/,
    				    xtype : 'textfield'
    				});
    		 			
    			var getValidCodeButton   = Ext.create('Ext.Button', 
    				{columnWidth: .23,height:33, name : 'getValidCodeButton',cls: 'Common_Btn',hidden :(jspParms.scriptLevel!=0 && audiCodeSwitch && smsSwitchNum!='0')?false:true,id:'getValidCodeButton', text : '获取验证码', handler :  getValidCode
    			 });
    			function isPoneAvailable(pone){
    				var myreg = /^[1][3,4,5,7,8][0-9]{9}$/;
    			    if (!myreg.test(pone)) {
    			      return false;
    			    } else {
    			      return true;
    			    }
    			}
    			var countdown=10;
    			function settime(){
    			 if (countdown == 0) {
    				 	Ext.getCmp('getValidCodeButton').setDisabled(false);
    			        countdown = 10;
    			        return false;
    			    } else {
    			    	 Ext.getCmp('getValidCodeButton').setDisabled(true);
    			    	 countdown--;
    			    }
    			 //显示验证码框
    			    setTimeout(function() {
    			    	settime();
    			    },1000);
    			}
    			function getValidCode(){
    				var phonenum = Ext.getCmp('myvalidCode').getValue();
    				if(!isPoneAvailable(phonenum)){
    					Ext.MessageBox.alert("提示", "请填写正确的手机号！");
    					return;
    				}
    				var auditor  =auditorComBox_tap.getValue();
    				if(!auditor) {
    					Ext.Msg.alert('提示', "请选择审核人！");
    					return;
    				}
    				var taskN = Ext.util.Format.trim(taskName.getValue());
    				settime();
    				//显示输入验证码框
    				//Ext.getCmp('mauthCode').show();
    				//验证码入库保存，组织验证码数据发送给短信接口，发送成功
    				Ext.Ajax.request({
    					url : 'sendAuthCode.do',
    					params : {
    						telephoneNum :phonenum ,
    						iauditUser :auditor,
    						stepId:'-1',
    						planId:'-1',
    						iscenceId:'-1',
    						serviceId: jspParms.iid,
    						planName:jspParms.serviceName,
    						isscript:'0',
    						proType:'10',
    						systemId:'-1',
						menuName: '双人复核-待办事宜-执行审核',
						operationType: '提交配置信息',
						taskName:taskN
    					},
    					method : 'post',
    					success : function(response, text) {
    						    var success = Ext.decode(response.responseText).success;
    						    var message = Ext.decode(response.responseText).message;
    						if (success) {
    							Ext.Msg.show({
    								title : '提示',
    								msg : " "+message ,
    								buttons : Ext.MessageBox.OK
    							});
    						} else {
    							Ext.Msg.show({
    								title : '提示',
    								msg : message,
    								buttons : Ext.MessageBox.OK
    							});
    						}
    					},
    					failure : function(result, request) {
    						secureFilterRs(result, "操作失败！");
    					}
    				});
    			}

    			Ext.create('Ext.window.Window', {
    		  		title : '提交配置信息',
    		  		autoScroll : true,
    		  		modal : true,
    		  		resizable : false,
    		  		closeAction : 'destroy',
    		  		width : 420,
    		  		height : jspParms.scriptLevel==0?310:340,
    		  				layout: 'vbox',
    		  		items:[taskName, auditorComBox_tap,execComBox_tap,butterflyVerison,checkVersion,execDesc,{
    		            border: false,
    		            layout: 'column',
    		            padding : '0 2 0 8',
    		            items: [validCode,getValidCodeButton] 
    	  			},authCode],
    		  		buttonAlign: 'center',
    		  		buttons: [{ 
    		  			xtype: "button", 
    		  			text: "确定", 
    		  			handler: function () {
    		  				
    		  				
    		  				//判断输入的审核人是否合法 start
		                	var displayField =auditorComBox_tap.getRawValue();
							if(!Ext.isEmpty(displayField)){
								//判断输入是否合法标志，默认false，代表不合法
								var flag = false;
								//遍历下拉框绑定的store，获取displayField
								auditorStore_tap.each(function (record) {
									//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
								    var data_fullName = record.get('fullName');
								    if(data_fullName == displayField){
								    	flag =true;
								    	//combo.setValue(record.get('loginName'));
								    }
								});
								if(!flag){
								 	Ext.Msg.alert('提示', "输入的审核人非法");
								 	auditorComBox_tap.setValue("");
								 	return;
								} 
							}
							//判断输入的审核人是否合法  end
    		  				
    		  				var self = this;
    		  				var auditor = auditorComBox_tap.getValue();
    		  				if(!auditor) {
    		  					Ext.Msg.alert('提示', "没有选择审核人！");
    		  					return;
    		  				}
    		  				var execUser = execComBox_tap.getValue();
    		  				var taskN = Ext.util.Format.trim(taskName.getValue());
    		  				if(Ext.isEmpty(taskN)) {
    		  					Ext.Msg.alert('提示', "任务名称不能为空！");
    		  					return;
    		  				}
    		  				
    		  				if (fucCheckLength(taskN) > 255) {
    	                        Ext.Msg.alert('提示', "任务名称不能超过255字符！");
    	                        return;
    	                    }
    		  				var isdelay = false;
		  					var execT = "";
    		  				if(jspParms.isScriptConvertToFlow=='true'){
    		  					if(isTimerTask.getValue()){
    		  						isdelay = isTimerTask.getValue();
    		  						execT =  cycleExecCronText.getRawValue();
        			  				if(isdelay && ""==execT)
        			  				{
        			  					Ext.Msg.alert('提示', "没有填写执行时间！");
        			  					return;
        			  				}
    		  					}
    		  				}
    		  			//如果输入验证码，校验验证码是否正确
    		  				var authCodeForExec = Ext.util.Format.trim(authCode.getValue());
    		  				var phonenum = Ext.getCmp('myvalidCode').getValue();
    		  				if(!Ext.isEmpty(authCodeForExec)){
    			  	    		if(!isPoneAvailable(phonenum)){
    			  	    			Ext.MessageBox.alert("提示", "请填写正确的手机号！");
    			  	    			return;
    			  	    		}
    		  				}
    		  				var butterflyV = "";
    		  				var check =Ext.getCmp("checkVersion").getValue();
    		  				if(jspParms.execUserSwitch=='true'&&!check&&!butterflyVerison.hidden){
    		  					butterflyV=Ext.util.Format.trim(butterflyVerison.getValue());
    		  					if(butterflyV==null||butterflyV==""){
    		  						Ext.Msg.alert('提示', "单号不能为空！");
        		  					return;
    		  					}
    		  					if (fucCheckLength(butterflyV) > 255) {
        	                        Ext.Msg.alert('提示', "单号不能超过255字符！");
        	                        return;
        	                    }
    		  				}
    		  				var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
    		  				if(Ext.isEmpty(execDescForExec)) {
    		  					Ext.Msg.alert('提示', "没有填写执行描述！");
    		  					return;
    		  				}
    		  				if(fucCheckLength(execDesc.getValue() > 2000)) {
    		  					Ext.Msg.alert('提示', "执行描述内容长度超过2000个字符！");
    		  					return;
    		  			    }
    		  				var isSaveTemplate = isSaveTemplateCk.getValue();
    		  			//填写验证码了，去数据库校验。
    	  					//校验验证码是否正确
    		  				if(!Ext.isEmpty(authCodeForExec)){//填写验证码了，去数据库校验。
    	  					Ext.Ajax.request({
    	  						url : 'validAuthCode.do',
    	  						method : 'POST',
    	  						params : {
    	  							serviceId: jspParms.iid ,
    	  							stepId:'-1',
    	  							planId:'-1',
    	  							iscenceId:'-1',
    	  							authCode : authCodeForExec,
    	  							telephoneNum :phonenum ,
    	  							iauditUser :auditor,
    	  							isscript:'0',
    	  							proType:'10',
    	  							systemId:'-1'
    	  						},
    	  						success: function(response, opts) {
    	  							var success = Ext.decode(response.responseText).success;
    								var message = Ext.decode(response.responseText).message;
    	  							if(success) {
    	  	    		  				if(isSaveTemplate) {
    	  	    		  					Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
    	  	    		  				        if(btn=='ok') {
    	  	    		  				        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
    	  	    		  				        		var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
    	  	    		  				                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    	  	    		  				        	} else {
    	  	    		  				        		var customName = Ext.util.Format.trim(text);
    	  	    		  				        		Ext.Ajax.request({
    	  	    		  				        	        url: 'checkCustomTemplateNameIsExist.do',
    	  	    		  				        	        params: {
    	  	    		  				        	            customName: customName,
    	  	    		  				        	            flag: jspParms.flag
    	  	    		  				        	        },
    	  	    		  				        	        method: 'POST',
    	  	    		  				        	        success: function(response, options) {
    	  	    		  				        	            if (!Ext.decode(response.responseText).success) {
    	  	    		  				        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
    	  	    		  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    	  	    		  				        	            } else {
    	  	    		  				        	            	Ext.Ajax.request({
    	  	    			  				  	  						url : 'scriptFlowExecAuditing.do',
    	  	    			  				  	  						method : 'POST',
    	  	    			  				  	  						params : {
//    	  	    			  				  	  							iidForQuery : parseInt(jspParms.iworkItemid),
    	  	    			  				  	  							serviceId: jspParms.iid,
    	  	    			  				  	  							auditor: auditor,
    	  	    			  				  	  							taskName: taskN,
    	  	    			  				  	  							startData: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    	  	    			  				  	  							scriptLevel: jspParms.scriptLevel,
    	  	    				  				  	  						isDelay: isdelay,
    	  	    				  					  				    	execTime: execT,
    	  	    				  					  				    	execUser:execUser,
    	  	    				  					  				    	execDesc:execDescForExec
    	  	    			  				  	  						},
    	  	    			  				  	  						success: function(response, opts) {
    	  	    			  				  	  							var success = Ext.decode(response.responseText).success;
    	  	    			  				  	  							var message = Ext.decode(response.responseText).message;
    	  	    			  				  	  							if(success) {
    	  	    			  				  	  							var iworkItemid = Ext.decode(response.responseText).workItemId;
		    	  	    			  				  	  					Ext.Ajax.request({
		    	  	    				  										url : 'updateWokitemId.do',
		    	  	    		  		    		  						method : 'POST',
		    	  	    		  		    		  						params : {
		    	  	    		  		    		  							workitemId : iworkItemid ,
		    	  	    		  		    		  							serviceId: jspParms.iid ,
		    	  	    		  		    		  							stepId:'-1',
		    	  	    		  		    		  							planId:'-1',
		    	  	    		  		    		  							iscenceId:'-1',
		    	  	    		  		    		  							authCode : authCodeForExec,
		    	  	    		  		    		  							telephoneNum :phonenum ,
		    	  	    		  		    		  							iauditUser :auditor,
			    	  	    		  		    		  						isscript:'0',
			  				    		    		  							proType:'10',
			  				    		    		  							systemId:'-1'
		    	  	    		  		    		  						},
		    	  	    						  	  								 success : function (response, opts)
		    	  	    					  	  								 {
		    	  	    							  	  								
		    	  	    					  	  								 },
		    	  	    				    		    		  					failure: function(result, request) {
		    	  	    				    		    		  					secureFilterRs(result,"回更workitemid失败！");
		    	  	    				    		    		  				}
		    	  	    				  									});
    	  		    			  				  	  							Ext.MessageBox.alert("提示", "请求提交成功！");
    	  	    			  				  	  								self.up("window").close();
    	  		   			  				  	  								 Ext.Ajax.request ({
    	  		   			  				  	  								 url : 'scriptExecForOneRecord.do',
    	  		   			  				  	  								 method : 'POST',
    	  		   			  				  	  								 params :
    	  		   			  				  	  								 {
    	  		   			  				  	  									 iworkItemid: iworkItemid
    	  		   			  				  	  								 },
    	  		   			  				  	  								 success : function (response, opts)
    	  		   			  				  	  								 {
    	  		   			  				  	  									 var success = Ext.decode (response.responseText).success;
    	  		   			  				  	  									 if(success && jspParms.isScriptConvertToFlow=='true') {
    	  		   			  				  	  										 Ext.Ajax.request ({
    	  		   			  				  	  											 url : 'scriptExecForScriptConvertFlowToOrgData.do',
    	  		   			  				  	  											 method : 'POST',
    	  		   			  				  	  											 params :
    	  		   			  				  	  											 {
    	  		   			  				  	  												 iworkItemid:iworkItemid,
    	  		   			  				  	  												 serviceId: jspParms.iid 
    	  		   			  				  	  											 },
    	  		   			  				  	  											 success : function (response, opts)
    	  		   			  				  	  											 {
    	  		   			  				  	  												 var success = Ext.decode (response.responseText).success;
    	  	    			  				  	  												 if(success){
    	  	    			  				  	  												if(jspParms.scriptLevel==0){ 
    	  	    			  				  	  													 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
    	  	    			  				  	  													var	uuid ='';
    	  	    					  					  											Ext.Ajax.request({
    	  	    					  					  								                url: 'queryUuidById.do',
    	  	    					  					  								                method: 'POST',
    	  	    					  					  								                async: false,
    	  	    					  					  								                params: {
    	  	    					  					  								                	serviceId: jspParms.iid,
    	  	    					  					  								                },
    	  	    					  					  								                success: function(response, options) {
    	  	    					  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
    	  	    					  					  								                },
    	  	    					  					  								                failure: function(result, request) {
    	  	    					  					  								                }
    	  	    					  					  								            });
    	  	    			  				  	  													Ext.Ajax.request({
    	  	    			  				  	  													url : 'execScriptServiceStart.do',
    	  	    			  				  	  													method : 'POST',
    	  	    			  				  	  													params : {
    	  	    			  				  	  														serviceId : jspParms.iid,
    	  	    			  				  	  														uuid : uuid,
    	  	    			  				  	  														serviceName : jspParms.serviceName,
    	  	    			  				  	  														scriptType:0,
    	  	    			  				  	  														workItemId : iworkItemid,
    	  	    			  				  	  														coatId : 0,
    	  	    			  				  	  														isFlow: 0
    	  	    			  				  	  													},
    	  	    			  				  	  													success : function(response, request) {
    	  	    			  				  	  														var success = Ext.decode(response.responseText).success;
    	  	    			  				  	  														if (success) {
    	  	    			  				  	  															var flowId = Ext.decode(response.responseText).content;
    	  	    			  				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
    	  	    			  				  	  														}
    	  	    			  				  	  													},
    	  	    			  				  	  													failure : function(result, request) {
    	  	    			  				  	  														Ext.Msg.alert('提示', '执行失败！');
    	  	    			  				  	  													}
    	  	    			  				  	  												}); 
    	  	    			  				  	  													 }
    	  	    			  				  	  													 }else{
    	  	    			 				  	  														Ext.MessageBox.alert("提示", "白名单任务提交成功！");
    	  	    							  	  													 }
    	  	    			  				  	  												 }

    	  		   			  				  	  											 }
    	  		   			  				  	  										 });
    	  		   			  				  	  									 }else if(success && jspParms.isScriptConvertToFlow=='false'){
    	  		   			  				  	  										if(jspParms.scriptLevel==0){ 
    	  				   			  				  	  								 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
    	  							   			  				  	  							var	uuid ='';
    	  						  					  											Ext.Ajax.request({
    	  						  					  								                url: 'queryUuidById.do',
    	  						  					  								                method: 'POST',
    	  						  					  								                async: false,
    	  						  					  								                params: {
    	  						  					  								                	serviceId: jspParms.iid,
    	  						  					  								                },
    	  						  					  								                success: function(response, options) {
    	  						  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
    	  						  					  								                },
    	  						  					  								                failure: function(result, request) {
    	  						  					  								                }
    	  						  					  								            });
    	  				  				  	  													Ext.Ajax.request({
    	  				  				  	  													url : 'execScriptServiceStart.do',
    	  				  				  	  													method : 'POST',
    	  				  				  	  													params : {
    	  				  				  	  														serviceId : jspParms.iid,
    	  				  				  	  														uuid :uuid,
    	  				  				  	  														serviceName : jspParms.serviceName,
    	  				  				  	  														scriptType:0,
    	  				  				  	  														workItemId : iworkItemid,
    	  				  				  	  														coatId : 0,
    	  				  				  	  														isFlow: 1
    	  				  				  	  													},
    	  				  				  	  													success : function(response, request) {
    	  				  				  	  														var success = Ext.decode(response.responseText).success;
    	  				  				  	  														if (success) {
    	  				  				  	  															var flowId = Ext.decode(response.responseText).content;
    	  				  				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
    	  				  				  	  														}
    	  				  				  	  													},
    	  				  				  	  													failure : function(result, request) {
    	  				  				  	  														Ext.Msg.alert('提示', '执行失败！');
    	  				  				  	  													}
    	  				  				  	  												}); 
    	  				  				  	  													 }
    	  		   			  				  	  									   }
    	  		   			  				  	  									 }
    	  		   			  				  	  								 }
    	  		   			  				  	  							 });
    	  	    				  				  	  							Ext.Ajax.request({
    	  	    				  		        	        	                    url: 'saveFlowCustomTemplate.do',
    	  	    				  		        	        	                    method: 'POST',
    	  	    				  		        	        	                    params: {
    	  	    				  		        	        	                    	customName: customName,
    	  	    				  		        	        	                    	serviceId: jspParms.iid,
    	  	    				  		        	        	                        data: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    	  	    				  		        	        	                        audiUserLoginName: auditor,
    	  	    						  				  	  							taskName: taskN,
    	  	    				  		        	        	                        flag: jspParms.flag
    	  	    				  		        	        	                    },
    	  	    				  		        	        	                    success: function(response, options) {
    	  	    				  		        	        	                        var success1 = Ext.decode(response.responseText).success;
    	  	    				  		        	        	                        var message1 = Ext.decode(response.responseText).message;
    	  	    				  		        	        	                        if (success1) {
    	  	    				  		        	        	                            Ext.MessageBox.show({
    	  	    				  		        	        	                                title: "提示",
    	  	    				  		        	        	                                msg: '请求已经发送到审核人<br>常用任务保存成功！',
    	  	    				  		        	        	                                buttonText: {
    	  	    				  		        	        	                                    yes: '确定'
    	  	    				  		        	        	                                },
    	  	    				  		        	        	                                buttons: Ext.Msg.YES
    	  	    				  		        	        	                            });
    	  	    				  		        	        	                        }
    	  	    				  		        	        	                    },
    	  	    				  		        	        	                    failure: function(result, request) {
    	  	    				  		        	        	                        Ext.MessageBox.show({
    	  	    				  		        	        	                            title: "提示",
    	  	    				  		        	        	                            msg: "请求已经发送到审核人<br>模板保存失败",
    	  	    				  		        	        	                            buttonText: {
    	  	    				  		        	        	                                yes: '确定'
    	  	    				  		        	        	                            },
    	  	    				  		        	        	                            buttons: Ext.Msg.YES
    	  	    				  		        	        	                        });
    	  	    				  		        	        	                    }
    	  	    		
    	  	    				  		        	        	                });
    	  	    			  				  	  							} else {
    	  	    			  				  	  								Ext.MessageBox.alert("提示", message);
    	  	    			  				  	  							}
    	  	    			  				  	  						},
    	  	    			  				  	  						failure: function(result, request) {
    	  	    			  				  	  							secureFilterRs(result,"操作失败！");
    	  	    			  				  	  						}
    	  	    			  				  	  					});
    	  	    		  				        	            }
    	  	    		  				        	        },
    	  	    		  				        	        failure: function(result, request) {}
    	  	    		  				        	    });
    	  	    		  				        	}
    	  	    		  				        	
    	  	    		  				        }
    	  	    		  				        
    	  	    		  				    });
    	  	    		  				} else {
    	  	    		  					Ext.MessageBox.wait("信息验证中...","提示");
    	  	    		  					Ext.Ajax.request({
    	  	    		  						url : 'scriptFlowExecAuditing.do',
    	  	    		  						method : 'POST',
    	  	    		  						params : {
    	  	    		  							iidForQuery : jspParms.iworkItemid ,
    	  	    		  							serviceId: jspParms.iid ,
    	  	    		  							auditor: auditor,
    	  	    		  							taskName: taskN,
    	  	    		  							startData: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    	  	    		  							scriptLevel: jspParms.scriptLevel,
    	  	    		  							isDelay:isdelay,
    	  	    			  				    	execTime:execT,
    	  	    			  				    	execUser:execUser,
    	  	    			  				    	execDesc:execDescForExec,
    	  	    			  				    	butterflyversion:butterflyV
    	  	    		  						},
    	  	    		  						success: function(response, opts) {
    	  	    		  							var success = Ext.decode(response.responseText).success;
    	  	    		  							var message = Ext.decode(response.responseText).message;
    	  	    		  							if(success) {
    	  	    		  								var iworkItemid = Ext.decode(response.responseText).workItemId;
	    	  	    		  							Ext.Ajax.request({
	  				  										url : 'updateWokitemId.do',
	  		  		    		  						method : 'POST',
	  		  		    		  						params : {
	  		  		    		  							workitemId : iworkItemid ,
	  		  		    		  							serviceId: jspParms.iid ,
	  		  		    		  							stepId:'-1',
	  		  		    		  							planId:'-1',
	  		  		    		  							iscenceId:'-1',
	  		  		    		  							authCode : authCodeForExec,
	  		  		    		  							telephoneNum :phonenum ,
	  		  		    		  							iauditUser :auditor,
	  		  		    		  							isscript:'0',
		    		    		  							proType:'10',
		    		    		  							systemId:'-1'
	  		  		    		  						},
	  						  	  								 success : function (response, opts)
	  					  	  								 {
	  							  	  								
	  					  	  								 },
	  				    		    		  					failure: function(result, request) {
	  				    		    		  					secureFilterRs(result,"回更workitemid失败！");
	  				    		    		  				}
	  				  									});
    	  	    		  								Ext.MessageBox.alert("提示", "请求提交成功！");
    	  	    		  								self.up("window").close();
    	  					  	  								 Ext.Ajax.request ({
    	  					  	  								 url : 'scriptExecForOneRecord.do',
    	  					  	  								 method : 'POST',
    	  					  	  								 params :
    	  					  	  								 {
    	  					  	  									 iworkItemid: iworkItemid
    	  					  	  								 },
    	  					  	  								 success : function (response, opts)
    	  					  	  								 {
    	  					  	  									 var success = Ext.decode (response.responseText).success;
    	  					  	  									 if(success && jspParms.isScriptConvertToFlow=='true') {
    	  					  	  										 Ext.Ajax.request ({
    	  					  	  											 url : 'scriptExecForScriptConvertFlowToOrgData.do',
    	  					  	  											 method : 'POST',
    	  					  	  											 params :
    	  					  	  											 {
    	  					  	  												 iworkItemid:iworkItemid,
    	  					  	  												 serviceId: jspParms.iid 
    	  					  	  											 },
    	  					  	  											 success : function (response, opts)
    	  					  	  											 {
    	  					  	  												 var success = Ext.decode (response.responseText).success;
    	  						  	  											 if(success){
    	  						  	  											 if(jspParms.scriptLevel==0){
    	  					  	  													 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
    	  					  	  														var	uuid ='';
    	  				  					  											Ext.Ajax.request({
    	  				  					  								                url: 'queryUuidById.do',
    	  				  					  								                method: 'POST',
    	  				  					  								                async: false,
    	  				  					  								                params: {
    	  				  					  								                	serviceId: jspParms.iid,
    	  				  					  								                },
    	  				  					  								                success: function(response, options) {
    	  				  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
    	  				  					  								                },
    	  				  					  								                failure: function(result, request) {
    	  				  					  								                }
    	  				  					  								            });
    	  					  	  													Ext.Ajax.request({
    	  					  	  													url : 'execScriptServiceStart.do',
    	  					  	  													method : 'POST',
    	  					  	  													params : {
    	  					  	  														serviceId : jspParms.iid,
    	  					  	  														uuid:uuid,
    	  					  	  														serviceName : jspParms.serviceName,
    	  					  	  														scriptType:0,
    	  					  	  														workItemId : iworkItemid,
    	  					  	  														coatId : 0,
    	  					  	  														isFlow: 0
    	  					  	  													},
    	  					  	  													success : function(response, request) {
    	  					  	  														var success = Ext.decode(response.responseText).success;
    	  					  	  														if (success) {
    	  					  	  															var flowId = Ext.decode(response.responseText).content;
    	  					  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
    	  					  	  														}
    	  					  	  													},
    	  					  	  													failure : function(result, request) {
    	  					  	  														Ext.Msg.alert('提示', '执行失败！');
    	  					  	  													}
    	  					  	  												}); 
    	  					  	  													 }else{
    	  					  	  														Ext.MessageBox.alert("提示", "白名单任务提交成功！");
    	  					  	  													 }
    	  						  	  											  }
    	  					  	  												 }

    	  					  	  											 }
    	  					  	  										 });
    	  					  	  									 }else if(success && jspParms.isScriptConvertToFlow=='false'){
    	  					  	  									   if(jspParms.scriptLevel==0){
    	  					  	  										 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
    	  						  				  	  								var	uuid ='';
    	  			  					  											Ext.Ajax.request({
    	  			  					  								                url: 'queryUuidById.do',
    	  			  					  								                method: 'POST',
    	  			  					  								                async: false,
    	  			  					  								                params: {
    	  			  					  								                	serviceId: jspParms.iid,
    	  			  					  								                },
    	  			  					  								                success: function(response, options) {
    	  			  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
    	  			  					  								                },
    	  			  					  								                failure: function(result, request) {
    	  			  					  								                }
    	  			  					  								            });
    	  					  	  													Ext.Ajax.request({
    	  					  	  													url : 'execScriptServiceStart.do',
    	  					  	  													method : 'POST',
    	  					  	  													params : {
    	  					  	  														serviceId : jspParms.iid,
    	  					  	  														uuid:uuid,
    	  					  	  														serviceName : jspParms.serviceName,
    	  					  	  														scriptType:0,
    	  					  	  														workItemId : iworkItemid,
    	  					  	  														coatId : 0,
    	  					  	  														isFlow: 1
    	  					  	  													},
    	  					  	  													success : function(response, request) {
    	  					  	  														var success = Ext.decode(response.responseText).success;
    	  					  	  														if (success) {
    	  					  	  															var flowId = Ext.decode(response.responseText).content;
    	  					  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
    	  					  	  														}
    	  					  	  													},
    	  					  	  													failure : function(result, request) {
    	  					  	  														Ext.Msg.alert('提示', '执行失败！');
    	  					  	  													}
    	  					  	  												}); 
    	  					  	  													 }
    	  					  	  									              }
    	  		  				  	  									 }
    	  					  	  								 }
    	  					  	  							 });
    	  	    		  								if(jspParms.iworkItemid) {
    	  	    		  									if(jspParms.from==1) {
    	  	    		  										messageWindow1.close();
    	  	    		  									} else {
    	  	    		  										messageWindow.getLoader ().load (
    	  	    		  												{
    	  	    		  													url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
    	  	    		  													autoLoad : true,
    	  	    		  													scripts : true
    	  	    		  												});
    	  	    		  										messageWindow.setTitle ('待办事项');
    	  	    		  									}
    	  	    		  								}
    	  	    		  							} else {
    	  	    		  								Ext.MessageBox.alert("提示", message);
    	  	    		  							}
    	  	    		  						},
    	  	    		  						failure: function(result, request) {
    	  	    		  							secureFilterRs(result,"操作失败！");
    	  	    		  						}
    	  	    		  					});    		  				    
    	  	    		  				}
    	  							}else{
    	  								Ext.MessageBox.alert("提示", message);
    	  							}
    	  						},
    		  					failure: function(result, request) {
    		  						secureFilterRs(result,"操作失败！");
    		  					}
    	  						
    	  					});
    		  				}else{
    		  				   if(isSaveTemplate) {
    		  					Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
    		  				        if(btn=='ok') {
    		  				        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
    		  				        		var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
    		  				                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    		  				        	} else {
    		  				        		var customName = Ext.util.Format.trim(text);
    		  				        		Ext.Ajax.request({
    		  				        	        url: 'checkCustomTemplateNameIsExist.do',
    		  				        	        params: {
    		  				        	            customName: customName,
    		  				        	            flag: jspParms.flag
    		  				        	        },
    		  				        	        method: 'POST',
    		  				        	        success: function(response, options) {
    		  				        	            if (!Ext.decode(response.responseText).success) {
    		  				        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
    		  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    		  				        	            } else {
    		  				        	            	Ext.Ajax.request({
    			  				  	  						url : 'scriptFlowExecAuditing.do',
    			  				  	  						method : 'POST',
    			  				  	  						params : {
//    			  				  	  							iidForQuery : parseInt(jspParms.iworkItemid),
    			  				  	  							serviceId: jspParms.iid,
    			  				  	  							auditor: auditor,
    			  				  	  							taskName: taskN,
    			  				  	  							startData: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    			  				  	  							scriptLevel: jspParms.scriptLevel,
    				  				  	  						isDelay: isdelay,
    				  					  				    	execTime: execT,
    				  					  				    	execUser:execUser,
    				  					  				    	execDesc:execDescForExec
    			  				  	  						},
    			  				  	  						success: function(response, opts) {
    			  				  	  							var success = Ext.decode(response.responseText).success;
    			  				  	  							var message = Ext.decode(response.responseText).message;
    			  				  	  							if(success) {
    			  				  	  							var iworkItemid = Ext.decode(response.responseText).workItemId;
	    			  				  	  						if(jspParms.scriptLevel==0){
				  				  	  							}else{
	    			  				  	  							Ext.MessageBox.alert("提示", "请求已经发送到审核人");
				  				  	  							}
    			  				  	  								self.up("window").close();
    			  				  	  							if(jspParms.scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
	   			  				  	  								 Ext.Ajax.request ({
	   			  				  	  								 url : 'scriptExecForOneRecord.do',
	   			  				  	  								 method : 'POST',
	   			  				  	  								 params :
	   			  				  	  								 {
	   			  				  	  									 iworkItemid: iworkItemid
	   			  				  	  								 },
	   			  				  	  								 success : function (response, opts)
	   			  				  	  								 {
	   			  				  	  									 var success = Ext.decode (response.responseText).success;
	   			  				  	  									 if(success && jspParms.isScriptConvertToFlow=='true') {
	   			  				  	  										 Ext.Ajax.request ({
	   			  				  	  											 url : 'scriptExecForScriptConvertFlowToOrgData.do',
	   			  				  	  											 method : 'POST',
	   			  				  	  											 params :
	   			  				  	  											 {
	   			  				  	  												 iworkItemid:iworkItemid,
	   			  				  	  												 serviceId: jspParms.iid 
	   			  				  	  											 },
	   			  				  	  											 success : function (response, opts)
	   			  				  	  											 {
	   			  				  	  												 var success = Ext.decode (response.responseText).success;
    			  				  	  												 if(success){
    			  				  	  													 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
    			  				  	  													var	uuid ='';
    					  					  											Ext.Ajax.request({
    					  					  								                url: 'queryUuidById.do',
    					  					  								                method: 'POST',
    					  					  								                async: false,
    					  					  								                params: {
    					  					  								                	serviceId: jspParms.iid,
    					  					  								                },
    					  					  								                success: function(response, options) {
    					  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
    					  					  								                },
    					  					  								                failure: function(result, request) {
    					  					  								                }
    					  					  								            });
    			  				  	  													Ext.Ajax.request({
    			  				  	  													url : 'execScriptServiceStart.do',
    			  				  	  													method : 'POST',
    			  				  	  													params : {
    			  				  	  														serviceId : jspParms.iid,
    			  				  	  														uuid : uuid,
    			  				  	  														serviceName : jspParms.serviceName,
    			  				  	  														scriptType:0,
    			  				  	  														workItemId : iworkItemid,
    			  				  	  														coatId : 0,
    			  				  	  														isFlow: 0
    			  				  	  													},
    			  				  	  													success : function(response, request) {
    			  				  	  														var success = Ext.decode(response.responseText).success;
    			  				  	  														if (success) {
    			  				  	  															var flowId = Ext.decode(response.responseText).content;
    			  				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
    			  				  	  														}
    			  				  	  													},
    			  				  	  													failure : function(result, request) {
    			  				  	  														Ext.Msg.alert('提示', '执行失败！');
    			  				  	  													}
    			  				  	  												}); 
    			  				  	  													 }else{
    			 				  	  														Ext.MessageBox.alert("提示", "白名单任务提交成功！");
    							  	  													 }
    			  				  	  												 }

	   			  				  	  											 }
	   			  				  	  										 });
	   			  				  	  									 }else if(success && jspParms.isScriptConvertToFlow=='false'){
			   			  				  	  								 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
						   			  				  	  							var	uuid ='';
					  					  											Ext.Ajax.request({
					  					  								                url: 'queryUuidById.do',
					  					  								                method: 'POST',
					  					  								                async: false,
					  					  								                params: {
					  					  								                	serviceId: jspParms.iid,
					  					  								                },
					  					  								                success: function(response, options) {
					  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
					  					  								                },
					  					  								                failure: function(result, request) {
					  					  								                }
					  					  								            });
			  				  	  													Ext.Ajax.request({
			  				  	  													url : 'execScriptServiceStart.do',
			  				  	  													method : 'POST',
			  				  	  													params : {
			  				  	  														serviceId : jspParms.iid,
			  				  	  														uuid :uuid,
			  				  	  														serviceName : jspParms.serviceName,
			  				  	  														scriptType:0,
			  				  	  														workItemId : iworkItemid,
			  				  	  														coatId : 0,
			  				  	  														isFlow: 1
			  				  	  													},
			  				  	  													success : function(response, request) {
			  				  	  														var success = Ext.decode(response.responseText).success;
			  				  	  														if (success) {
			  				  	  															var flowId = Ext.decode(response.responseText).content;
			  				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
			  				  	  														}
			  				  	  													},
			  				  	  													failure : function(result, request) {
			  				  	  														Ext.Msg.alert('提示', '执行失败！');
			  				  	  													}
			  				  	  												}); 
			  				  	  													 }
	   			  				  	  									 }
	   			  				  	  								 }
	   			  				  	  							 });
			  				  	  							    }
    				  				  	  							Ext.Ajax.request({
    				  		        	        	                    url: 'saveFlowCustomTemplate.do',
    				  		        	        	                    method: 'POST',
    				  		        	        	                    params: {
    				  		        	        	                    	customName: customName,
    				  		        	        	                    	serviceId: jspParms.iid,
    				  		        	        	                        data: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    				  		        	        	                        audiUserLoginName: auditor,
    						  				  	  							taskName: taskN,
    				  		        	        	                        flag: jspParms.flag
    				  		        	        	                    },
    				  		        	        	                    success: function(response, options) {
    				  		        	        	                        var success1 = Ext.decode(response.responseText).success;
    				  		        	        	                        var message1 = Ext.decode(response.responseText).message;
    				  		        	        	                        if (success1) {
    				  		        	        	                            Ext.MessageBox.show({
    				  		        	        	                                title: "提示",
    				  		        	        	                                msg: '请求已经发送到审核人<br>常用任务保存成功！',
    				  		        	        	                                buttonText: {
    				  		        	        	                                    yes: '确定'
    				  		        	        	                                },
    				  		        	        	                                buttons: Ext.Msg.YES
    				  		        	        	                            });
    				  		        	        	                        }
    				  		        	        	                    },
    				  		        	        	                    failure: function(result, request) {
    				  		        	        	                        Ext.MessageBox.show({
    				  		        	        	                            title: "提示",
    				  		        	        	                            msg: "请求已经发送到审核人<br>模板保存失败",
    				  		        	        	                            buttonText: {
    				  		        	        	                                yes: '确定'
    				  		        	        	                            },
    				  		        	        	                            buttons: Ext.Msg.YES
    				  		        	        	                        });
    				  		        	        	                    }
    		
    				  		        	        	                });
    			  				  	  							} else {
    			  				  	  								Ext.MessageBox.alert("提示", message);
    			  				  	  							}
    			  				  	  						},
    			  				  	  						failure: function(result, request) {
    			  				  	  							secureFilterRs(result,"操作失败！");
    			  				  	  						}
    			  				  	  					});
    		  				        	            }
    		  				        	        },
    		  				        	        failure: function(result, request) {}
    		  				        	    });
    		  				        	}
    		  				        	
    		  				        }
    		  				        
    		  				    });
    		  				} else {
    		  					Ext.MessageBox.wait("信息验证中...","提示");
    		  					Ext.Ajax.request({
    		  						url : 'scriptFlowExecAuditing.do',
    		  						method : 'POST',
    		  						params : {
    		  							iidForQuery : jspParms.iworkItemid ,
    		  							serviceId: jspParms.iid ,
    		  							auditor: auditor,
    		  							taskName: taskN,
    		  							startData: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    		  							scriptLevel: jspParms.scriptLevel,
    		  							isDelay:isdelay,
    			  				    	execTime:execT,
    			  				    	execUser:execUser,
    			  				    	execDesc:execDescForExec,
    			  				    	butterflyversion:butterflyV
    		  						},
    		  						success: function(response, opts) {
    		  							var success = Ext.decode(response.responseText).success;
    		  							var message = Ext.decode(response.responseText).message;
    		  							if(success) {
    		  								var iworkItemid = Ext.decode(response.responseText).workItemId;
    		  								if(jspParms.scriptLevel==0){
				  	  							}else{
    		  								Ext.MessageBox.alert("提示", "请求已经发送到审核人");
				  	  							}
    		  								self.up("window").close();
	    		  								if(jspParms.scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
				  	  								 Ext.Ajax.request ({
				  	  								 url : 'scriptExecForOneRecord.do',
				  	  								 method : 'POST',
				  	  								 params :
				  	  								 {
				  	  									 iworkItemid: iworkItemid
				  	  								 },
				  	  								 success : function (response, opts)
				  	  								 {
				  	  									 var success = Ext.decode (response.responseText).success;
				  	  									 if(success && jspParms.isScriptConvertToFlow=='true') {
				  	  										 Ext.Ajax.request ({
				  	  											 url : 'scriptExecForScriptConvertFlowToOrgData.do',
				  	  											 method : 'POST',
				  	  											 params :
				  	  											 {
				  	  												 iworkItemid:iworkItemid,
				  	  												 serviceId: jspParms.iid 
				  	  											 },
				  	  											 success : function (response, opts)
				  	  											 {
				  	  												 var success = Ext.decode (response.responseText).success;
					  	  											 if(success){
				  	  													 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
				  	  														var	uuid ='';
			  					  											Ext.Ajax.request({
			  					  								                url: 'queryUuidById.do',
			  					  								                method: 'POST',
			  					  								                async: false,
			  					  								                params: {
			  					  								                	serviceId: jspParms.iid,
			  					  								                },
			  					  								                success: function(response, options) {
			  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
			  					  								                },
			  					  								                failure: function(result, request) {
			  					  								                }
			  					  								            });
				  	  													Ext.Ajax.request({
				  	  													url : 'execScriptServiceStart.do',
				  	  													method : 'POST',
				  	  													params : {
				  	  														serviceId : jspParms.iid,
				  	  														uuid:uuid,
				  	  														serviceName : jspParms.serviceName,
				  	  														scriptType:0,
				  	  														workItemId : iworkItemid,
				  	  														coatId : 0,
				  	  														isFlow: 0
				  	  													},
				  	  													success : function(response, request) {
				  	  														var success = Ext.decode(response.responseText).success;
				  	  														if (success) {
				  	  															var flowId = Ext.decode(response.responseText).content;
				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
				  	  														}
				  	  													},
				  	  													failure : function(result, request) {
				  	  														Ext.Msg.alert('提示', '执行失败！');
				  	  													}
				  	  												}); 
				  	  													 }else{
				  	  														Ext.MessageBox.alert("提示", "白名单任务提交成功！");
				  	  													 }
				  	  												 }

				  	  											 }
				  	  										 });
				  	  									 }else if(success && jspParms.isScriptConvertToFlow=='false'){
			  				  	  								 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
					  				  	  								var	uuid ='';
		  					  											Ext.Ajax.request({
		  					  								                url: 'queryUuidById.do',
		  					  								                method: 'POST',
		  					  								                async: false,
		  					  								                params: {
		  					  								                	serviceId: jspParms.iid,
		  					  								                },
		  					  								                success: function(response, options) {
		  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
		  					  								                },
		  					  								                failure: function(result, request) {
		  					  								                }
		  					  								            });
				  	  													Ext.Ajax.request({
				  	  													url : 'execScriptServiceStart.do',
				  	  													method : 'POST',
				  	  													params : {
				  	  														serviceId : jspParms.iid,
				  	  														uuid:uuid,
				  	  														serviceName : jspParms.serviceName,
				  	  														scriptType:0,
				  	  														workItemId : iworkItemid,
				  	  														coatId : 0,
				  	  														isFlow: 1
				  	  													},
				  	  													success : function(response, request) {
				  	  														var success = Ext.decode(response.responseText).success;
				  	  														if (success) {
				  	  															var flowId = Ext.decode(response.responseText).content;
				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
				  	  														}
				  	  													},
				  	  													failure : function(result, request) {
				  	  														Ext.Msg.alert('提示', '执行失败！');
				  	  													}
				  	  												}); 
				  	  													 }
	  				  	  									 }
				  	  								 }
				  	  							 });
		  	  							    }
    		  								if(jspParms.iworkItemid) {
    		  									if(jspParms.from==1) {
    		  										messageWindow1.close();
    		  									} else {
    		  										messageWindow.getLoader ().load (
    		  												{
    		  													url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
    		  													autoLoad : true,
    		  													scripts : true
    		  												});
    		  										messageWindow.setTitle ('待办事项');
    		  									}
    		  								}
    		  							} else {
    		  								Ext.MessageBox.alert("提示", message);
    		  							}
    		  						},
    		  						failure: function(result, request) {
    		  							secureFilterRs(result,"操作失败！");
    		  						}
    		  					});    		  				    
    		  				}
    		  			}
    		  			}
    		  		}]
    		  	}).show();
    			auditorStore_tap.load();
    		}

    	}
    	
    	function backFn(){
            destroyRubbish();
            contentPanel.getLoader().load({
              url : jspParms.ifrom,
              params: {
	                menuId: jspParms.menuId,
	            	iid:jspParms.iid,
	            	serviceName: jspParms.serviceName,
	            	actionType: 'audi',
		            bussId: jspParms.bussId,
		            bussTypeId: jspParms.bussTypeId,
		            scriptType: jspParms.scriptType,
		            platmFrom:jspParms.platmFrom,
		            scriptLevel: jspParms.scriptLevel,
		            flag: 1,
		            isScriptConvertToFlow : jspParms.isFlow != '1',
		            taskType:'taskCreate',
		            suUser:jspParms.suUser
              },
              scripts: true
            });
            if (Ext.isIE) {
              CollectGarbage();
            }
    	};
        
    	
        Ext.applyIf(me, {
            items: [ 
            	isSaveTemplateCk,isTimerTask, cycleExecCronText,selectCronButton,
            	{
                    xtype:'tbspacer',
                    flex:1
                },
            	  submitButton,baseInfoButton, backButton, 
            	{
                    xtype:'tbspacer',
                    flex:1
                },{
                    xtype:'tbspacer',
                    width:235
                }]
        });
    	
        me.callParent(arguments);
        
    }

});