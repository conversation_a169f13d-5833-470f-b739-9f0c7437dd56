<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSFLOWCUSTOMEDITPRODUCTFROMTM=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var customIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getParameter("customId")%>;

var customNameGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getParameter("customName")%>';

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM = <%=request.getAttribute("fromType")%>;
var workItemidGFSSFLOWCUSTOMEDITPRODUCTFROMTM = <%=request.getAttribute("workItemid")%>;
var fromGFSSFLOWCUSTOMEDITPRODUCTFROMTM = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSFLOWCUSTOMEDITPRODUCTFROMTM=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSFLOWCUSTOMEDITPRODUCTFROMTM=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCTFROMTM='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCTFROMTM='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSFLOWCUSTOMEDITPRODUCTFROMTM = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSFLOWCUSTOMEDITPRODUCTFROMTM = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITPRODUCTFROMTM/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSFLOWCUSTOMEDITPRODUCTFROMTM" style="width: 100%;height: 100%"></div>
</body>
</html>