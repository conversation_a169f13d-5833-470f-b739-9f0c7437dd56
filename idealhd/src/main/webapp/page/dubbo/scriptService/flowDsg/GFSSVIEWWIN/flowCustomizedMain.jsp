<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSVIEWWIN=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSVIEWWIN=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSVIEWWIN=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSVIEWWIN=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSVIEWWIN=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSVIEWWIN=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSVIEWWIN = <%=request.getAttribute("isScriptConvertToFlow")%>;
var pageFromGFSSVIEWWIN = '<%=request.getParameter("pageFrom")%>';
var parentMxIidGFSSVIEWWIN = '<%=request.getParameter("parentMxIid")%>';

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSVIEWWIN='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSVIEWWIN='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSVIEWWIN=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSVIEWWIN=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSVIEWWIN='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSVIEWWIN='<%=request.getParameter("flag")%>';
<% } %>


<% if (null==request.getParameter("isStack")) { %>
var isStackGFSSVIEWWIN= false
<% } else { %>
var isStackGFSSVIEWWIN = <%=request.getParameter("isStack")%>;
<% } %>

var fromTypeGFSSVIEWWIN = <%=request.getAttribute("fromType")%>;
var workItemidGFSSVIEWWIN = <%=request.getAttribute("workItemid")%>;
var fromGFSSVIEWWIN = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSVIEWWIN = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSVIEWWIN = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSVIEWWIN = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSVIEWWIN = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSVIEWWIN=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSVIEWWIN=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSVIEWWIN='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSVIEWWIN='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSVIEWWIN='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>

<% if (null==request.getParameter("readOnly") && null==request.getAttribute("readOnly")) { %>
	var readOnlyGFSSVIEWWIN='0';
<% } else { %>
	<% if(null!=request.getParameter("readOnly")) { %>
	  var readOnlyGFSSVIEWWIN='<%=request.getParameter("readOnly")%>';
	<% } else { %>
	  var readOnlyGFSSVIEWWIN='<%=request.getAttribute("readOnly")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSVIEWWIN=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSVIEWWIN=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSVIEWWIN=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSVIEWWIN='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSVIEWWIN='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSVIEWWIN='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSVIEWWIN = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSVIEWWIN = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSVIEWWIN = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSVIEWWIN = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSVIEWWIN = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSVIEWWIN = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSVIEWWIN/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSVIEWWIN" style="width: 100%;height: 100%"></div>
</body>
</html>