var rowExpanderLog_execGraph;
var scriptmonitorinfoins_store_execGraph;
var scriptmonitorinfoins_grid_execGraph;
var interVForExecGraph = 10;
var interPVForExecGraph = 20;
var lastIdForExecGraph;
var lastRowIndexForExecGraph;
var lastrequestIdForExecGraph;
var lastiipForExecGraph;
var lastiportForExecGraph;
var flag_execGraph = 1; // 0:测试     1:生成
Ext.onReady(function() {
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        listeners: {
            select: function(me, record, index, eOpts) { // 选择复选框事件
                flowMesshisForExecGraph(record.data.iid, index);
            },
            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
                flowMesshisForExecGraph(record.data.iid, index);
            }
            // ,
            // selectionchange : function(me, selected, record, index,eOpts)
            // {
            // alert(record.iid);
            // alert(index);
            // flowMesshisForExecGraph(record.get('iid'),index);
            // }
        }
    });
    scriptmonitorinfoins_store_execGraph = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flag_execGraph + '&coatid=' + coatid_execGraph,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
                flowMesshisRefreshForExecGraph(lastIdForExecGraph, lastRowIndexForExecGraph);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatid_execGraph
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime + " 秒");
//                        $("#scriptState").attr("class", "Im_" + res.stateFlag);
//                        $("#scriptState").html(res.state);
//                        $("#scriptEndTime").html(res.endTime);
//                        $("#scriptRunTime").html(res.runTime + " 秒");
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 200,
        menuDisabled : true,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state');
            var zoomStr = "";
            if (isWin_execGraph != 1) {
                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetailForExecGraph(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
            }
            zoomStr = '';
            if (state == '30' || state == '40' || state == '50') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExecGraph(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServerForExecGraph(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="skipScriptServerForExecGraph(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else if (state == '-1' || state == '1') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExecGraph(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServerForExecGraph(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForExecGraph(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
            }
        }
    }];

    rowExpanderLog_execGraph = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForExecGraph{iid}">', '<pre  onselectstart="return true" id="steptextareahisForExecGraph{iid}"  class="monitor_desc"></pre>', '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span">自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshIdForExecGraph" name="rowFreshIdForExecGraph" >&nbsp;秒</span>', '&nbsp;&nbsp;&nbsp;<input type="button" value="刷新" onclick="loadShelloutputhisForExecGraph({iid},\'{agentIp}\',{agentPort})" class="Common_Btn Monitor_Btn">', '&nbsp;&nbsp;&nbsp;<input type="button" value="终止" onclick="scriptServerStopForExecGraph({iid},{state})" class="Common_Btn Monitor_Btn">', '</div>']
    });
    // 展开符合某个条件的行
    // function expendRow() {
    // var i;// 循环临时变量
    // var arr = [];// 要展开的行的数组
    // for (i = 0; i < scriptmonitorinfoins_store_execGraph.data.length; i++)//
    // ProdRequireInfoStore是gridpanel的数据源
    // {
    // var record = scriptmonitorinfoins_store_execGraph.getAt(i);// 循环遍历每一行
    // arr.push(i);
    // }
    // for (var j = 0; j < arr.length; j++) {//
    // 遍历数组展开调用toggleRow(index)方法展开某一行
    // expander.toggleRow(arr[j]);
    // }
    // }
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        name: "pageFreshTime",
        value: interPVForExecGraph
    });
    
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: scriptmonitorinfoins_store_execGraph,
        dock: 'bottom',
        displayInfo: true,
        items: [{
            xtype: "label",
            text: "自动刷新"
        },
        pageFreshTime,
        {
            xtype: "label",
            text: "  秒"
        },
        {
            xtype: 'button',
            width: 70,
            height: 30,
            cls: 'Common_Btn',
            text: '刷  新',
            listeners: {
                click: function() {
                    if (refreshObjForExecGraph) {
                        clearInterval(refreshObjForExecGraph);
                    }
                    refreshPage();
                    // var interValue =
                    // document.getElementById('pageFreshTime').value;
                    var interValue = pageFreshTime.getValue();
                    interPVForExecGraph = interValue;
                    if (interPVForExecGraph < 20) {
                        interPVForExecGraph = 20;
                    }
                    refreshObjForExecGraph = setInterval(refreshPage, interPVForExecGraph * 1000);
                }
            }
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '终  止',
            listeners: {
                click: function() {
                    var data = getCHKBoxIds();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                        function(button, text) {
                            if (button == "yes") {
                                if (data == '-1') {
                                    Ext.Msg.alert('提示', '操作执行成功!');
                                    scriptmonitorinfoins_store_execGraph.reload();
                                    return;
                                }
                                Ext.MessageBox.wait("数据处理中...", "提示");
                                Ext.Ajax.request({
                                    url: 'scriptServiceShellKill.do',
                                    params: {
                                        flag: flag_execGraph,
                                        insIds: data
                                    },
                                    method: 'POST',
                                    success: function(response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        // 当后台数据同步成功时
                                        if (success) {
                                            scriptmonitorinfoins_store_execGraph.reload();
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }
        }]
    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_grid_execGraph.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right',
        value: serviceNameForExecGraph
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserForExecGraph
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startTimeForExecGraph
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: endTimeForExecGraph
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    scriptmonitorinfoins_grid_execGraph = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: scriptmonitorinfoins_store_execGraph,
        border: true,
        columnLines: true,
        columns: scriptmonitorinfoins_columns,
        bbar: pageBar,
        selModel: selModel,
        plugins: [rowExpanderLog_execGraph],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                /*
						 * var cls = ''; if(record.data.state==10){ cls =
						 * 'row_Blue'; }else if(record.data.state==20){ cls =
						 * 'row_Green'; }else if(record.data.state==30){ cls =
						 * 'row_Red'; }else if(record.data.state==60){ cls =
						 * 'row_Gray'; } else { cls = 'row_Gray'; } return cls;
						 */
                return 'norowexpandblah';
            }
        }
/*    ,

        listeners: {
            itemclick: function(a, record, item, index, e, eOpts) {
                rowExpanderLog_execGraph.toggleRow(index, record);
            }
        }*/
    });

    scriptmonitorinfoins_grid_execGraph.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
        interVForExecGraph = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshIdForExecGraph').innerText = interVForExecGraph;
        } else {
            document.getElementById('rowFreshIdForExecGraph').innerHTML = interVForExecGraph;
        }
        loadShelloutputhisForExecGraph(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
        // refreshObjShellOutputForExecGraph = setInterval(function() {
        // loadShelloutputhisForExecGraph(record.get('iid'),
        // record.get('agentIp'), record.get('agentPort'));
        // }, 1000);
    });
    scriptmonitorinfoins_grid_execGraph.view.on('collapsebody',
    function(rowNode, record, expandRow, eOpts) {
        lastIdForExecGraph = 0;
        lastRowIndexForExecGraph = 0;
        if (refreshObjShellOutputForExecGraph) {
            clearInterval(refreshObjShellOutputForExecGraph);
        }
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoins_div_exec_graph",
        layout: 'border',
        width : '100%',
        height :contentPanel.getHeight() - 38,
        border: false,
        bodyPadding: 5,
        items: [info_form, scriptmonitorinfoins_grid_execGraph]
    });

    function refreshPage() {
    	if(contentPanel.getLoader().url=='monitorHomePageScriptServiceForExec.do') {
    		scriptmonitorinfoins_store_execGraph.reload();
    	}
    }

    if (refreshObjForExecGraph) {
        clearInterval(refreshObjForExecGraph);
    }
    refreshObjForExecGraph = setInterval(refreshPage, interPVForExecGraph * 1000);

});
function flowMesshisForExecGraph(iruninfoinsid, rowIndex) {
    lastIdForExecGraph = iruninfoinsid;
    lastRowIndexForExecGraph = rowIndex;
    var record = scriptmonitorinfoins_store_execGraph.getAt(rowIndex);
    var records = scriptmonitorinfoins_store_execGraph.getRange(0, scriptmonitorinfoins_store_execGraph.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLog_execGraph.recordsExpanded[records[i].internalId]) {
            rowExpanderLog_execGraph.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_store_execGraph.getAt(rowIndex);
     rowExpanderLog_execGraph.toggleRow(rowIndex, record);
}
function loggerDetailForExecGraph(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForExecGraph) {
        clearInterval(refreshObjShellOutputForExecGraph);
    }
    if (refreshObjForExecGraph) {
        if (refreshObjShellOutputForExecGraph) {
            clearInterval(refreshObjShellOutputForExecGraph);
        }
        clearInterval(refreshObjForExecGraph);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
//            flowId: flowId_execGraph,
            coatId: coatid_execGraph,
            flag: flag_execGraph
        }
    });
}

function flowMesshisRefreshForExecGraph(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    var record = scriptmonitorinfoins_store_execGraph.getAt(rowIndex);
    var records = scriptmonitorinfoins_store_execGraph.getRange(0, scriptmonitorinfoins_store_execGraph.getCount());
    var rowFreshValue = document.getElementById('rowFreshIdForExecGraph').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForExecGraph = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForExecGraph').innerText = interVForExecGraph;
    } else {
        document.getElementById('rowFreshIdForExecGraph').innerHTML = interVForExecGraph;
    }

    rowExpanderLog_execGraph.toggleRow(lastRowIndexForExecGraph, records[lastRowIndexForExecGraph]);
//    refreshObjShellOutputForExecGraph = setInterval(function() {
//    	if(contentPanel.getLoader().url=='monitorHomePageScriptServiceForExec.do') {
//    		loadShelloutputhisInfoForExecGraph(lastrequestIdForExecGraph, lastiipForExecGraph, lastiportForExecGraph);
//    	}
//    },
//    rowFreshValue * 1000);
    // var record = scriptmonitorinfoins_store_execGraph.getAt(rowIndex);
    // rowExpanderLog_execGraph.toggleRow(rowIndex, record);
}

function loadShelloutputhisForExecGraph(requestId, iip, iport) {
    lastrequestIdForExecGraph = requestId;
    lastiipForExecGraph = iip;
    lastiportForExecGraph = iport;
    if (refreshObjShellOutputForExecGraph) {
        clearInterval(refreshObjShellOutputForExecGraph);
    }
    var rowFreshValue = document.getElementById('rowFreshIdForExecGraph').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForExecGraph = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForExecGraph').innerText = interVForExecGraph;
    } else {
        document.getElementById('rowFreshIdForExecGraph').innerHTML = interVForExecGraph;
    }
    // document.getElementById('rowFreshIdForExecGraph').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfoForExecGraph(requestId, iip, iport);
    refreshObjShellOutputForExecGraph = setInterval(function() {
        loadShelloutputhisInfoForExecGraph(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfoForExecGraph(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForExecGraph' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: flag_execGraph
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStopForExecGraph(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
            	Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_store_execGraph.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag_execGraph
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store_execGraph.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServerForExecGraph(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag_execGraph
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store_execGraph.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServerForExecGraph(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: flag_execGraph
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_store_execGraph.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}