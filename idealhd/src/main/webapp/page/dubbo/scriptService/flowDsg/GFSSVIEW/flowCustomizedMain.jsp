<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSVIEW=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSVIEW=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSVIEW=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSVIEW=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSVIEW=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSVIEW=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSVIEW = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("whichPageFrom")) { %>
var whichPageFromGFSSVIEW = '';
<% } else { %>
var serviceNameGFSSVIEW='request.getParameter("whichPageFrom")';
<% } %>

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSVIEW='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSVIEW='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSVIEW=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSVIEW=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSVIEW='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSVIEW='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSVIEW = <%=request.getAttribute("fromType")%>;
var workItemidGFSSVIEW = <%=request.getAttribute("workItemid")%>;
var fromGFSSVIEW = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSVIEW = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSVIEW = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSVIEW = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSVIEW = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSVIEW=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSVIEW=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSVIEW='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSVIEW='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSVIEW='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSVIEW=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSVIEW=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSVIEW=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSVIEW='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSVIEW='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSVIEW='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSVIEW = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSVIEW = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSVIEW = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSVIEW = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSVIEW = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSVIEW = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSVIEW" style="width: 100%;height: 100%"></div>
</body>
</html>