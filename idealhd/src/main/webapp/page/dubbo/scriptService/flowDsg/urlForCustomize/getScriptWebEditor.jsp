<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<html>
<head>
<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
String divID = request.getParameter("divID");
if(divID==null)
{
    divID = menuid;
}
String winDivID = request.getParameter("winDivID");
if(winDivID!=null&&!"".equals(winDivID))
{
    divID = winDivID;
}
	Enumeration<String> paramNames = request.getParameterNames();
	while( paramNames.hasMoreElements() )
	{
	    String paramName = paramNames.nextElement();
%>
		tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
	};
%>

tempData.projectTypeId = 10;
tempData.load = false
if(tempData.serviceId>0)
{
	tempData.load = true;
}
console.log('tempData',tempData);
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/grapheditor/webstudio/act/common/iadaptergrid.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/grapheditor/webstudio/act/common/iuserrolegrid.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/grapheditor/webstudio/act/common/iuserrolewin.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/grapheditor/webstudio/act/common/ipanel.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomize/getScriptWebEditor.js"></script>
<script type="text/javascript" src="/editor/js/adaptor/configbean.js"></script>
</head>
<body>
	<div id="scriptEditorDiv_<%=divID%>" style="width: 100%; height: 100%"></div>
</body>
</html>