/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/
Ext.onReady (function ()
{
	var jspParms = tempData;
	delete tempData;
	
	/** 获取内外对接的参数 **/
	var editor = this.GRAPHS[jspParms.namespace];
	var model = editor.editorUi.editor.graph.getModel();
	var cell = editor.currentCell;
	var configWindow = editor.configWindow;
	
	var isScriptConvertToFlowGFSSVIEW = jspParms.isScriptConvertToFlow=='true';
	var scriptServiceId = jspParms.scriptId;
	if(isScriptConvertToFlowGFSSVIEW)
	{
		scriptServiceId = jspParms.serviceId;
	}
	var stepNameObj;
	
	stepNameObj = Ext.create ('Ext.form.field.Text', {
	    fieldLabel : '步骤名称',
	    labelWidth : 73,
	    labelAlign : 'right',
	    width: 570
	});
	
	var shutdownCheckboxObj = Ext.create ('Ext.form.field.Checkbox', {
		boxLabel : '关机维护',
		margin: '0 0 0 15',
		inputValue: 1,
		hidden: isScriptConvertToFlowGFSSVIEW
	});
	
	var viewChosedScriptButton = Ext.create('Ext.Button', {
		text : '查看已选脚本',
		margin : '0 0 0 15',
		textAlign : 'center',
		handler : function() {
			if(scriptServiceId>0) {
				Ext.create('widget.window', {
		            title: '详细信息',
		            closable: true,
		            closeAction: 'destroy',
		            width: contentPanel.getWidth(),
		            minWidth: 350,
		            height: contentPanel.getHeight(),
		            draggable: false,
		            // 禁止拖动
		            resizable: false,
		            // 禁止缩放
		            modal: true,
		            loader: {
		                url: 'queryOneServiceForView.do',
		                params: {
		                    iid: scriptServiceId,
		                    flag: 0,
		                    hideReturnBtn: 1
		                },
		                autoLoad: true,
		                scripts: true
		            }
		        }).show();
			} else {
				Ext.Msg.alert('提示',"没有选择脚本服务！");
			}
			
		}
	});
	
	var backButton = Ext.create('Ext.Button', {
		text : '关闭',
		margin : '0 0 0 5',
		textAlign : 'center',
		handler : function() {
			configWindow.close ();
		}
	});
	
	var prevButton = Ext.create('Ext.Button', {
	    text : '上一步',
	    margin : '0 0 0 30',
	    textAlign : 'center',
	    handler : function() {
	    	var res = getCellFun("before");
	    	if(res) {
	    		cell = res;
	    		initFun ();
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为第一个步骤");
	    	}
	      }
	  });
			
	var nextButton = Ext.create('Ext.Button', {
		text : '下一步',
		margin : '0 0 0 5',
		textAlign : 'center',
		handler : function() {
			var res = getCellFun("after");
	    	if(res) {
	    		cell = res;
	    		initFun ();
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为最后一个步骤");
	    	}
		}
	});
	
	Ext.create ('Ext.form.Panel',
	{
		border : false,
		renderTo : "flowCustomizedEditScriptWindowDivGFSSVIEW",
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        labelWidth : 90
	    },
	    width : '100%',
	    buttonAlign : 'center',
	    items : [stepNameObj, shutdownCheckboxObj],
	    buttons: [viewChosedScriptButton, backButton, prevButton, nextButton]
	});

	/** 初始化方法* */
	function initFun ()
	{
		stepNameObj.setValue(cell.value);
		shutdownCheckboxObj.setValue(cell.isShutdown);
		if(isScriptConvertToFlowGFSSVIEW)
		{
			scriptServiceId = jspParms.serviceId;
		} else if(cell.scriptId==null || typeof (cell.scriptId) == "undefined") {
			scriptServiceId = iidGFSSVIEW;
		} else {
			var scriptUuid = cell.scriptId;
			Ext.Ajax.request({
		        url: 'queryIidByUuid.do',
		        method: 'POST',
		        params: {
		            uuid: scriptUuid
		        },
		        success: function(response, request) {
		        	scriptServiceId = Ext.decode(response.responseText).serviceId;
		        },
		        failure: function(result, request) {
		            secureFilterRs(result, "获取脚本IID失败！");
		        }
		    });
			if(isNaN(scriptServiceId)) {
				scriptServiceId = -1;
			}
		}
	}
	
	initFun ();
	
	/**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
	function getCellFun (inflag)
	{
		// 遍历所有节点
		var rootObj = model.getRoot ();
		var count = model.getChildCount (rootObj);
		for (var i = 0; i < count; i++)
		{
			var cells = rootObj.getChildAt (i);
			var counts = cells.getChildCount ();
			var beforeCell = null;// 上一个节点
			var afterCell = null;// 下一个节点
			var selfCell = null;// 自己
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				// 判断循环至的节点样式是否与传入的样式一致
				if (cellss.style == cell.style)
				{
					if (cellss == cell)
					{
						// 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
						selfCell = cell;
					}
					else
					{
						// 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
						selfCell == null ? beforeCell = cellss : afterCell = cellss;
					}
					// 如果获取到了想要的节点，则跳出循环
					if (selfCell != null && ((inflag == 'after' && afterCell != null)
					        || (inflag == 'before' && beforeCell != null)))
					{
						break;
					}
				}
			}
			// 返回指定节点
			return inflag == 'after' ? afterCell : beforeCell;
		}
	}

});
