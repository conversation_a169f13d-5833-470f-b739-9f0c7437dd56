<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSDBCHECK=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSDBCHECK=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSDBCHECK=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSDBCHECK=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSDBCHECK=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSDBCHECK=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>


<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSDBCHECK='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSDBCHECK='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSDBCHECK=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSDBCHECK=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSDBCHECK='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSDBCHECK='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSDBCHECK = <%=request.getAttribute("fromType")%>;
var workItemidGFSSDBCHECK = <%=request.getAttribute("workItemid")%>;
var isReCheckGFSSDBCHECK='<%=request.getAttribute("isReCheck") %>';
var fromGFSSDBCHECK = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSDBCHECK = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSDBCHECK = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSDBCHECK = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSDBCHECK = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSDBCHECK=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSDBCHECK=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSDBCHECK='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSDBCHECK='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSDBCHECK='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSDBCHECK=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSDBCHECK=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSDBCHECK=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevelDisplay") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSDBCHECK='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevelDisplay")) { %>
	  var scriptFlowLevelForTaskAudiGFSSDBCHECK='<%=request.getParameter("scriptLevelDisplay")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSDBCHECK='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSDBCHECK = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;
    scriptFlowLevelForTaskAudiGFSSDBCHECK='<%=request.getAttribute("scriptLevelDisplay") %>';
var publishDescTextGFSSDBCHECK='<%=request.getAttribute("publishDesc") %>';
var filter_bussIdGFSSDBCHECK = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSDBCHECK = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSDBCHECK = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSDBCHECK = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSDBCHECK = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSDBCHECK/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSDBCHECK" style="width: 100%;height: 100%"></div>
</body>
</html>