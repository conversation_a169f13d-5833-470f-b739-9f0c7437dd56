<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSCOLLECTAUDI=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSCOLLECTAUDI=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSCOLLECTAUDI=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSCOLLECTAUDI=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSCOLLECTAUDI=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSCOLLECTAUDI=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSCOLLECTAUDI = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSCOLLECTAUDI='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSCOLLECTAUDI='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSCOLLECTAUDI=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSCOLLECTAUDI=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSCOLLECTAUDI='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSCOLLECTAUDI='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSCOLLECTAUDI = <%=request.getAttribute("fromType")%>;
var workItemidGFSSCOLLECTAUDI = <%=request.getAttribute("workItemid")%>;
var fromGFSSCOLLECTAUDI = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSCOLLECTAUDI = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSCOLLECTAUDI = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSCOLLECTAUDI = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSCOLLECTAUDI = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSCOLLECTAUDI=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSCOLLECTAUDI=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSCOLLECTAUDI='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSCOLLECTAUDI='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSCOLLECTAUDI='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSCOLLECTAUDI=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSCOLLECTAUDI=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSCOLLECTAUDI=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSCOLLECTAUDI='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSCOLLECTAUDI='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSCOLLECTAUDI='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSCOLLECTAUDI = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSCOLLECTAUDI = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSCOLLECTAUDI = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSCOLLECTAUDI = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSCOLLECTAUDI = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSCOLLECTAUDI = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSCOLLECTAUDI/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSCOLLECTAUDI" style="width: 100%;height: 100%"></div>
</body>
</html>