Ext.onReady(function() {
	
	destroyRubbish();
	// 将通过jsp跳转传递的变量全部进行转移和清理。要注意变量的值传递可以直接清理。变量如果传递的是引用，注意不要把真实值清理掉，清理引用即可。
	var menuId = tempDataFlowTemplateManager.menuId;
	delete tempDataFlowTemplateManager;
    var shareWin;
    var coustomConfigWin;
	var shareType=0; //共享类型  0 所有人  1 用户   2 用户组
	var chosedShareIds =[]; //共享用户、用户组  数组
	var shareGrid;
	var shareedGrid;
	var iidArray = new Array();//解析常用任务使用
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		value : filter_serverNameQueryFlowTemplateManager,
		labelWidth : 65,
		width : '22%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	flowTemplateCustomGrid.ipage.moveFirst();
                	BSinfoloader();
                }
            }
        }
	});
	
	var pagesizeStart = 0;
	var pagesizelimit = 6;
	 /** 视图滚动按钮* */
	var rightBtn = Ext.create ("Ext.Button",
	{
		baseCls : 'Common_Btn',
	    text : ">",
	    handler : function(){
	    	pagesizeStart = pagesizeStart+6;
	    	pagesizelimit = pagesizelimit+6;
	    	if(pagesizeStart>BStypeSize){
	    		pagesizeStart = 0;
	    		pagesizelimit = 6;
	    	}
	    	northPanel.getLoader().load({
				url: 'newlyFlowTemplateManagerForProductBSinfo.do',
				params: {
					sName:sName.getValue(),
		    		templateName: templateName.getValue(),
		    		isEMscript: (null!=emScriptCb.getValue())?emScriptCb.getValue():-1,
		    	    iappSysIds:appSysObj.getValue(),
		    	    pagesizeStart:pagesizeStart,
		    		pagesizelimit:pagesizelimit
				},
				scripts: true
			});
	    }
	});
	
	var templateName = new Ext.form.TextField({
		name : 'templateName',
		fieldLabel : '模板名称',
		emptyText : '--请输入模板名称--',
		value : filter_templateNameQueryFlowTemplateManager,
		labelWidth : 65,
		width : '22%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	flowTemplateCustomGrid.ipage.moveFirst();
                	BSinfoloader();
                }
            }
        }
	});
	var emScriptStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-1", "name":"全部"},
			{"id":"0", "name":"否"},
			{"id":"1", "name":"是"}
		]
	});
	
	var emScriptCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'emScript',
		labelWidth : 65,
		queryMode : 'local',
		fieldLabel : '是否应急',
		displayField : 'name',
		hidden : !reviewSwitch,
		valueField : 'id',
		editable : false,
		emptyText : '--请选择是否应急--',
		store : emScriptStore,
		width : '20%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	flowTemplateCustomGrid.ipage.moveFirst();
                	BSinfoloader();
                }
            }
        }
	});
	Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	var appSysObj = Ext.create('Ext.form.field.ComboBox', {
		name: 'appSys',
        fieldLabel: '所属系统',
        emptyText : '--请选择所属系统--',
        labelWidth: 65,
        //hidden : !reviewSwitch,
        labelAlign: 'right',
        width: '20%',
        store: appSysStore,
        padding: '0 0 5 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        queryMode: 'local',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	flowTemplateCustomGrid.ipage.moveFirst();
                	BSinfoloader();
                }
            }
        }
    });
	
	
	var search_form = Ext.create('Ext.form.Panel', {
		 region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	baseCls:'customize_gray_back',
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back',  
				border : false,
				dock : 'top',
				items: [templateName, sName,appSysObj,emScriptCb,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						BSinfoloader();
						flowTemplateCustomGrid.ipage.moveFirst();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
					}
				},'->',rightBtn]
			}]
		});
	
	Ext.define('FlowTemplateCustomModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceId'         ,type : 'long'}, 
		    {name : 'scriptLevel'         ,type : 'int'}, 
		    {name : 'scriptVersion'         ,type : 'string'}, 
		    {name : 'customName' ,type : 'string'}, 
		    {name : 'serviceName' ,type : 'string'},
		    {name : 'iplatFrom' ,type : 'string'},
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'bussTypeName'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'userName',type : 'string'},
		    {name : 'userFullName',type : 'string'},
		    {name : 'taskName',type : 'string'}, 
		    {name : 'isFlow',type : 'string'},
		    {name : 'scriptType',type : 'string'},
		    {name : 'audiLoginName',type : 'string'}, 
		    {name : 'createTime'     ,type : 'string'},
		    {name : 'isEMscript' ,type : 'string'},
		    {name : 'isShare' ,type : 'string'},
		    {name : 'iappSysIds' ,type : 'string'},
		    {name : 'iappSysNames' ,type : 'string'}
	    ]
	});
	
	var flowTemplateCustomStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		pageSize : 30,
		model : 'FlowTemplateCustomModel',
		proxy : {
			type : 'ajax',
			url : 'flowTemplateCustomList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	flowTemplateCustomStore.on('beforeload', function (store, options) {
	    var new_params = {  
	    		sName:sName.getValue(),
	    		templateName: templateName.getValue(),
	    		isEMscript: (null!=emScriptCb.getValue())?emScriptCb.getValue():-1,
	    	    iappSysIds:appSysObj.getRawValue(),
	    		flag: 1
	    };
	    
	    Ext.apply(flowTemplateCustomStore.proxy.extraParams, new_params);
    });
    
    
    flowTemplateCustomStore.addListener('load', function(me, records, successful, eOpts) {
    	// 选择后构建出的ID数组
        if (iidArray.length>0) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (iidArray.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            flowTemplateCustomGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
        }
 
    });
    
	
	var isOrNotStore = Ext.create('Ext.data.Store', {
		fields: ['id','name'],
		data : [
		        {"id":"0","name":"否"},
		        {"id":"1","name":"是"}
		        ]
	});
	
	var isOrNotCombo = Ext.create('Ext.form.ComboBox', {
		margin : '5',
		store : isOrNotStore,
		queryMode : 'local',
		width : 600,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'name',
		valueField : 'id',
		editable : false,
		triggerAction : "all"
	});
	
	Ext.define('AppSysModel1', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore1 = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'AppSysModel1',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	
	appSysStore1.on('load', function() {
        var ins_rec = Ext.create('AppSysModel', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore1.insert(0, ins_rec);
        
    });
	
	var clickGroudRow;
    var appSysCombo = Ext.create('Ext.form.field.ComboBox', {
        multiSelect: true,
        labelWidth: 70,
        labelAlign: 'right',
        width: '80%',
        store: appSysStore1,
        padding: '0 0 5 0',
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        queryMode: 'local',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            } ,
		    select: function(combo, records, eOpts ) {
		    	var record = flowTemplateCustomStore.getAt( clickGroudRow );
		    	var selectValue = combo.getValue();
		    	var backValue = "";
		    	for(var i=0;i<selectValue.length;i++){
		    		if(i==0){
		    			backValue = ""+selectValue[i];
		    		}else{
		    			backValue = backValue + ","+selectValue[i];
		    		}
		    	}
		    	record.data.iappSysIds = backValue;
		    }
        }
    });
	
	var flowTemplateCustomColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		}, 
		{
			text : '常用任务',
		    dataIndex : 'customName',
		    width : 60,
		    flex:1
		}, 
		{
		    text : '任务ID',
		    dataIndex : 'iid',
		    width : 60,
		    //hidden : reviewSwitch
		    flex:1,
		    hidden : true
		}, 
		{
			text : '组合ID',
			dataIndex : 'serviceId',
			width : 40,
			hidden : true
		},
		{
			text : '服务名称',
			dataIndex : 'serviceName',
			flex:1,
			width : 180
		},
		{
			text : '适用平台',
			//hidden :!pageStyleSwitchFlowTemplateManager,
			dataIndex : 'iplatFrom',
			width : 180,
			hidden : true
		},
		{
		    text : '一级分类',
		    dataIndex : 'bussName',
		    //hidden :pageStyleSwitchFlowTemplateManager,
		    width : 95,
		    hidden : true
		}, 
		{
			text : '二级分类',
			dataIndex : 'bussTypeName',
			//hidden :pageStyleSwitchFlowTemplateManager,
			width : 95,
			hidden : true
		},
		{
			text : '是否为组合',
			dataIndex : 'isFlow',
			hidden:true,
			width : 95
		},
		{
			text : '版本',
			dataIndex : 'scriptVersion',
			hidden : true,
			width : 70
		},{
			text : '是否应急',
			dataIndex : 'isEMscript',
			width : 75,
			//hidden : !reviewSwitch,
			hidden : true,
			editor : isOrNotCombo,
			  renderer : function(value, metadata, record) {
						var index = isOrNotStore.find('id', value);
						if (index != -1) {
							return isOrNotStore.getAt(index).data.name;
						} else {
							return '';
						}
					}
		},{
			text : '所属系统1',
			dataIndex : 'iappSysIds',
			hidden : true,
			width : 50,
			editor:{
				
			}
		},
		{
			text : '所属系统',
			dataIndex : 'iappSysNames',
			width : 100,
			flex:1,
			hidden : true,
			editor : appSysCombo,
			//hidden : !reviewSwitch,
			renderer : function(value, metadata, record) {
				   var iappSysIds = record.data.iappSysIds;
	               var value1 = iappSysIds.split(',');
	               var str='';
	               for (var i = 0, len = iappSysIds.length; i < len; i++) {
	               	var index = appSysStore1.find('id', value1[i]);
	               	if (index != -1) {
		               		if(str!=''){
		               		   str= str +"|"+ appSysStore1.getAt(index).data.name;
		               		}else{
		               			str= str + appSysStore1.getAt(index).data.name;
		               		}
						} 
	               }
	               record.data.iappSysNames = str;
					return str;
					}
		},
		{
			text : '共享状态',
			dataIndex : 'isShare',
			flex:1,
			width : 95,
			renderer:function(value,p,record,rowIndex){
					if(value==0) {
				    		return '<font color="">未共享</font>';
				    } else if (value==1) {
				    		return '<font color="#0CBF47">已共享</font>';
				    } else {
				    		return '<font color="#CCCCCC">未知</font>';
				    }
			}
		},
		{
			text : '创建者',
			dataIndex : 'userFullName',
			flex:1,
			width : 150
		},
		{
			text : '创建时间',
			dataIndex : 'createTime',
			flex:1,
			width : 150
		},
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			flex:1,
			xtype : 'actiontextcolumn',
			width:160,
			items : [{
						text : '编辑',
						iconCls : 'script_edit',
						getClass : function(v, metadata, record) {
							var createUserName = record.data.userName;//创建者 登录名
							if (createUserName != loginUserFlowTemplateManager) {
								return 'x-hidden';
							}
						},
						handler : function(grid, rowIndex) {
							var iid =  grid.getStore().data.items[rowIndex].data.iid;
							var serviceId =  grid.getStore().data.items[rowIndex].data.serviceId;
							var customName = grid.getStore().data.items[rowIndex].data.customName;
							var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
							var bussId =grid.getStore().data.items[rowIndex].data.bussId;
							var bussTypeId  =grid.getStore().data.items[rowIndex].data.bussTypeId;
							var scriptLevel  =grid.getStore().data.items[rowIndex].data.scriptLevel;
							var scriptType=grid.getStore().data.items[rowIndex].data.scriptType;
//							var taskName  =grid.getStore().data.items[rowIndex].data.taskName;
//							var audiLoginName  =grid.getStore().data.items[rowIndex].data.audiLoginName;
							var isFlow  =grid.getStore().data.items[rowIndex].data.isFlow;
							var platForm = grid.getStore().data.items[rowIndex].data.iplatFrom;  //浦发需求
						 
							var filter_serverNameQuery = search_form.getForm().findField("serverName").getValue()==null?'':search_form.getForm().findField("serverName").getValue();
							var filter_templateNameQuery = search_form.getForm().findField("templateName").getValue()==null?'':search_form.getForm().findField("templateName").getValue();
							if(noScriptConvertSwitch && scriptType!='sql'){//去掉图，且不是sql类型的
								if (isFlow !='1'){
									editFlowCustomTemplateForNoConvert(iid,serviceId,customName,serviceName,bussId,bussTypeId,scriptLevel,menuId,isFlow,scriptType,platForm)
								}else{
									editFlowCustomTemplateForProduct(iid,serviceId,customName,serviceName,bussId,bussTypeId,scriptLevel,menuId,isFlow,scriptType,platForm,filter_serverNameQuery,filter_templateNameQuery);
								}
							}else{
								editFlowCustomTemplateForProduct(iid,serviceId,customName,serviceName,bussId,bussTypeId,scriptLevel,menuId,isFlow,scriptType,platForm,filter_serverNameQuery,filter_templateNameQuery);
							}
						}
					},{
						text : '提交',
						iconCls : 'script_submit',
//						getClass : function(v, metadata, record) {
//							if (record.data.status != -1) {
//								return 'x-hidden';
//							}
//						},
						handler : function(grid, rowIndex) {
							var iid =  grid.getStore().data.items[rowIndex].data.iid;
							var serviceId =  grid.getStore().data.items[rowIndex].data.serviceId;
							var customName = grid.getStore().data.items[rowIndex].data.customName;
							var serviceName = grid.getStore().data.items[rowIndex].data.serviceName;
							var bussId =grid.getStore().data.items[rowIndex].data.bussId;
							var bussTypeId  =grid.getStore().data.items[rowIndex].data.bussTypeId;
							var scriptLevel  =grid.getStore().data.items[rowIndex].data.scriptLevel;
							var scriptType=grid.getStore().data.items[rowIndex].data.scriptType;
							var taskName  =grid.getStore().data.items[rowIndex].data.taskName;
							var audiLoginName  =grid.getStore().data.items[rowIndex].data.audiLoginName;
							var isFlow  =grid.getStore().data.items[rowIndex].data.isFlow;
							var platForm = grid.getStore().data.items[rowIndex].data.iplatFrom;  //浦发需求
							submitFlowCustomTemplateForProduct(iid,scriptLevel,taskName,audiLoginName,isFlow,serviceName,serviceId,customName,bussId,bussTypeId,menuId,scriptType,platForm);
						}
					}]
//			renderer:function(value,p,record,rowIndex){
//				var iid =  record.get('iid');
//				var serviceId =  record.get('serviceId');
//				var customName = record.get('customName');
//				var serviceName = record.get('serviceName');
//				var bussId = record.get('bussId');
//				var bussTypeId  =record.get('bussTypeId');
//				var scriptLevel  =record.get('scriptLevel');
//				var scriptType=record.get('scriptType');
//				var taskName  =record.get('taskName');
//				var audiLoginName  =record.get('audiLoginName');
//				var isFlow  =record.get('isFlow');
//				var platForm = record.get('iplatFrom'); //浦发需求
//				var editFlowCustomFun="editFlowCustomTemplateForNoConvert";
//				if (isFlow == '1') {
//					editFlowCustomFun="editFlowCustomTemplateForProduct"; editFlowCustomTemplateForProduct
//	            }
//				if(noScriptConvertSwitch && scriptType!='sql'){//去掉图，且不是sql类型的
//					return '<span class="switch_span">'+
//					'<a href="javascript:void(0)" onclick="' + editFlowCustomFun + '('+iid+','+serviceId+',\''+customName+'\',\''+serviceName+'\','+bussId+','+bussTypeId+','+scriptLevel+',' + menuId + ','+isFlow+',\'' + scriptType+'\',\''+platForm+'\')">'+
//					'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"/>'+'编辑'+
//					'</a>'+
//					'</span>&nbsp;&nbsp;&nbsp;&nbsp;'+
//					
//					'<span class="switch_span">'+
//					'<a href="javascript:void(0)" onclick="submitFlowCustomTemplateForProduct('+iid+','+scriptLevel+',\''+taskName+'\',\''+audiLoginName+'\' ,'+isFlow+',\''+serviceName+'\','+serviceId+',\''+customName+'\','+bussId+','+bussTypeId+',' + menuId + ',\'' + scriptType+'\',\''+platForm+'\')">'+
//					'<img src="images/monitor_bg.png" align="absmiddle" class="script_submit"/>'+'提交'+
//					'</a>'+
//					'</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'
//					;
//				}else{
//					return '<span class="switch_span">'+
//					'<a href="javascript:void(0)" onclick="editFlowCustomTemplateForProduct('+iid+','+serviceId+',\''+customName+'\',\''+serviceName+'\','+bussId+','+bussTypeId+','+scriptLevel+',' + menuId + ','+isFlow+',\'' + scriptType+'\',\''+platForm+'\')">'+
//					'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"/>'+'编辑'+
//					'</a>'+
//					'</span>&nbsp;&nbsp;&nbsp;&nbsp;'+
//					
//					'<span class="switch_span">'+
//					'<a href="javascript:void(0)" onclick="submitFlowCustomTemplateForProduct('+iid+','+scriptLevel+',\''+taskName+'\',\''+audiLoginName+'\' ,'+isFlow+',\''+serviceName+'\','+serviceId+',\''+customName+'\','+bussId+','+bussTypeId+',' + menuId + ',\'' + scriptType+'\',\''+platForm+'\')">'+
//					'<img src="images/monitor_bg.png" align="absmiddle" class="script_submit"/>'+'提交'+
//					'</a>'+
//					'</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'
//					;
//				}
//			}
		}];

//	  var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		  	store: flowTemplateCustomStore,
//		  	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//		    dock: 'bottom',
//		    displayInfo: true,
//		    emptyMsg:'找不到任何记录'
//		  });
	  
	  var selModel=Ext.create('Ext.selection.CheckboxModel', {
			checkOnly: true
		});
	  appSysStore1.load({
			callback: function(records, operation, success) {
				flowTemplateCustomStore.load();
	    	}
		});
	  
	  var pluginsEdit = Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })
	  var flowTemplateCustomGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
			region: 'center',
			autoScroll: true,
		    store : flowTemplateCustomStore,
		    cls:'customize_panel_back',
		    plugins: [pluginsEdit],
//		    bbar : pageBar,
		    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    border:false,
		    columnLines : true,
		    columns : flowTemplateCustomColumns,
		    selModel : selModel,
		     listeners: {
		        select: function( e, record, index, eOpts ){ 
		        	if(iidArray.indexOf(record.get('iid'))==-1) {
	            		iidArray.push(record.get('iid'));
	            	}
		        },
		         deselect: function( e, record, index, eOpts ){
	            	if(iidArray.indexOf(record.get('iid'))>-1) {
	            		iidArray.remove(record.get('iid'));
	            	}
	            }
		     },
		    dockedItems : [{
		    	xtype : 'toolbar',
		    	items : ['->',{
			        text: '删除',
			        cls : 'Common_Btn',
			        handler: function() {
			        	var seledCnt = selModel.getCount();
			    		if(seledCnt < 1){
			    			Ext.MessageBox.alert("提示", "请选择要删除的记录！");
			    			return ;
			    		}
			    		var ss = selModel.getSelection();
			    		var ids = new Array();
			    		for ( var i = 0, len = ss.length; i < len; i++) {
			    			ids.push(ss[i].data.iid);
			    		}
			    		Ext.MessageBox.buttonText.yes = "确定"; 
			    		Ext.MessageBox.buttonText.no = "取消"; 
			    		Ext.Msg.confirm("确认删除", "是否删除选中的记录", function(id){
			    			if(id=='yes') {
			    				Ext.Ajax.request({
			                        url: 'deleteFlowCustomTemplateForTask.do',
			                        method: 'POST',
			                        params: {
			                        	ids: ids,
			                            flag: 1
			                        },
			                        success: function(response, options) {
			                            var success = Ext.decode(response.responseText).success;
			                            if (success) {
			                                Ext.MessageBox.show({
			                                    title: "提示",
			                                    msg: '删除成功！',
			                                    buttonText: {
			                                        yes: '确定'
			                                    },
			                                    buttons: Ext.Msg.YES
			                                });
			                                flowTemplateCustomStore.load();
			                            } else {
			                            	Ext.Msg.alert ('提示', "删除失败！");
			                            }
			                        },
			                        failure: function(result, request) {
			                            Ext.MessageBox.show({
			                                title: "提示",
			                                msg: "失败",
			                                buttonText: {
			                                    yes: '确定'
			                                },
			                                buttons: Ext.Msg.YES
			                            });
			                        }

			                    });
			    			}
			    		});
			        }
		    	}, {
			        text : '共享',
			        cls : 'Common_Btn',
			        handler : shareCustomTask
		    	},{
			        text : '解析',
			        cls : 'Common_Btn',
			        hidden : !analysisButtonSwitchFlowTemplateManager,
			        handler :  function() {
			        		 
								$.fileDownload('analysisFlowCustomTemplateForTask.do',{
									  httpMethod: 'POST',
									  traditional: true,
									  data:{ iids :iidArray
				 					  },
									  successCallback: function(url){
//									      Ext.MessageBox.show({
//								                title : "提示",
//								                msg : "操作成功",
//								                buttonText: {
//								                    yes: '确定'
//								                },
//								                buttons: Ext.Msg.YES
//								              });
									  },
									  failCallback: function (html, url) {
									   		 Ext.Msg.alert('提示', '导出失败！');
				                       		  return;
									   }
								  });   
			        		 
			        }
		    	},{
					text : '保存',
					cls : 'Common_Btn',
					hidden : !reviewSwitch,
					handler : saveFun
				}, {
					text : '解绑',
					cls : 'Common_Btn',
					hidden : !reviewSwitch,
					handler : deleteFun
				}]
		    }]
		});
	  flowTemplateCustomGrid.on("celldblclick",function( obj, td, cellIndex, record, tr, rowIndex, e, eOpts ){
		  clickGroudRow = rowIndex;
	  })
	    function clearQueryWhere(){
		  search_form.getForm().findField("serverName").setValue('');
		  search_form.getForm().findField("templateName").setValue('');
		  search_form.getForm().findField("appSys").setValue('');
	      search_form.getForm().findField("emScript").setValue('-1');
	    }
	    function shareCustomTask() {
			var seledCnt = selModel.getCount();
			if(seledCnt <1){
				Ext.MessageBox.alert("提示", "请选择要共享的常用任务！");
				return ;
			}
			if(iidArray.length >1){
				Ext.MessageBox.alert("提示", "每次只能选择一条记录！");
				return ;
			}
		   var ss = flowTemplateCustomGrid.getSelectionModel().getSelection()[0];
//		   if(ss.data.isShare==1) {
//				Ext.MessageBox.alert("提示", "该任务已共享！");
//				return;
//			}
			Ext.MessageBox.buttonText.yes = "确定"; 
			Ext.MessageBox.buttonText.no = "取消"; 
			Ext.Msg.confirm("确认共享", "是否共享选中的常用任务", function(id){
				if(id=='yes'  ) {
					openShareWin(ss.data.isFlow);
				}
			});
		}
		
		function openShareWin(isflow){
		     Ext.define('shareClumnModel', {
				extend : 'Ext.data.Model',
				fields : [{
			      name : 'IID',
			      type : 'long'
			    }, {
			      name : 'IFULLNAME',//用户名
			      type : 'string'
			    }, {
			      name : 'ILOGINNAME',//用户登录ID
			      type : 'string'
			    }, {
			      name : 'IGROUPNAME',//组名称 
			      type : 'string'
			    },{
				   name : 'OBJECTNAME',//对象名称 已共享使用 
				   type : 'string'
				},{ 
				   name : 'INAME',//常用任务名称 已共享使用 
				   type : 'string'
				},{
				   name : 'SHARETYPE', // 共享类型 已共享使用
				   type : 'string'
			    },{
			      name : 'IGROUPDES', //组描述
			      type : 'string'
			    }]
			});
			//共享展示列表 Store
			var shareColumnStore = Ext.create('Ext.data.Store', {
			    autoLoad: false,
			    autoDestroy: true,
			    model: 'shareClumnModel',
			    proxy: {
			      type: 'ajax',
			      url: 'getScriptShareColumnObject.do',
			      reader: {
			        type: 'json',
			        root: 'dataList',
			        totalProperty: 'totalCount'
			      }
			    }
			  });
			 shareColumnStore.on('beforeload', function (store, options) {
				var new_params = {  
//						shareType:shareType,
//						scriptiids:iidArray,
//						shareed:0
						isTemplate:'1',
						 shareType:shareType,
						 shareed:0, //0代表查询未共享的
						 scriptiids:iidArray[0],
						 objectName:objectName.getValue()
				};			
				
				Ext.apply(shareColumnStore.proxy.extraParams, new_params);
			});
			
			shareColumnStore.addListener('load', function(me, records, successful, eOpts) {
		        if (chosedShareIds.length>0) {
		            var chosedRecords = []; //存放选中记录
		            $.each(records,
		            function(index, record) {
		                if (chosedShareIds.indexOf(record.get('IID')) > -1) {
		                    chosedRecords.push(record);
		                }
		            });
		            shareGrid.getSelectionModel().select(chosedRecords, false, false); //选中记录
		        }
		 
		    });
			//已共享展示列表 Store
			var shareedColumnStore = Ext.create('Ext.data.Store', {
			    autoLoad: true,
			    autoDestroy: true,
			    model: 'shareClumnModel',
			    proxy: {
			      type: 'ajax',
			      url: 'getScriptShareColumnObject.do',
			      reader: {
			        type: 'json',
			        root: 'dataList',
			        totalProperty: 'totalCount'
			      }
			    }
			  });
			
			 shareedColumnStore.on('beforeload', function (store, options) {
				var new_params = {  
						 shareType:shareType,
						 shareed:1, //已共享 参数  1代表查询已共享
						 isTemplate:'1',
						 scriptiids:iidArray[0]
				};
				
				Ext.apply(shareedColumnStore.proxy.extraParams, new_params);
			});
			
//			var shareColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//				    store: shareColumnStore,
//					baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//					border:false
//			    });
			
			var shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
			
//			var shareColumnStorePageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//				    store: shareColumnStore,
//					baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//					border:false
//			    });
			
			var shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
			var shareedColumns =[{ text: '序号', xtype:'rownumberer', width: 40 },
										{ text: '用户主键',  dataIndex: 'IID',hidden:true},
										{ text: '常用任务名称',  dataIndex: 'INAME',width:200},
										{ text: '共享类型',  dataIndex: 'SHARETYPE',width:200},
										{ text: '对象名称',  dataIndex: 'OBJECTNAME',width:200}];
										
			//已共享grid
			shareedGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
				region: 'center',
				autoScroll: true,
			    store : shareedColumnStore,
			    border:false,
		//	    bbar:shareedColumnStorePageBar,
			    columnLines : true,
			    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			    cls:'customize_panel_back',
			    //selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
			    columns : shareedColumns
		//		listeners: {
		//	        select: function( e, record, index, eOpts ){ 
		//	        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
		//	        		chosedShareIds.push(record.get('IID'));
		//	        	}
		//	        },
		//	        deselect: function( e, record, index, eOpts ){ 
		//	        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
		//	        		chosedShareIds.remove(record.get('IID'));
		//	        	}
		//	        }
		//	    }
			});


			var shareUserRadio = Ext.create('Ext.form.field.Radio', {
				width: 80,
				name:'shareRadio',
				labelAlign : 'left',
				fieldLabel: '',
			    boxLabel: '用户共享',
			    inputValue : 1
			});
			var shareGroupRadio = Ext.create('Ext.form.field.Radio', {
				width: 90,
				name:'shareRadio',
				labelAlign : 'left',
				fieldLabel: '',
				boxLabel: '用户组共享',
				inputValue : 2
			});
			
			var shareAllRadio = Ext.create('Ext.form.field.Radio', {
				width: 100,
				name:'shareRadio',
				labelAlign : 'left',
				fieldLabel: '',
				boxLabel: '所有人共享',
				inputValue : 0
			});
			 var shareRadioComment =  Ext.create('Ext.form.RadioGroup', {
					name:'shareRadioComment',
					labelAlign : 'left',
					layout: 'column',
					width : '160',
					items:[shareUserRadio,shareGroupRadio,shareAllRadio],
					listeners:{
			            //通过change触发
			            change: function(g , newValue , oldValue){
			            	if(newValue.shareRadio == 0)//所有人
			            	{
			            		chosedShareIds=[];
			            		chosedShareIds.push(-1);
			            		shareType = 0;
			            		shareGrid.hide();
			            	} 
			            	else if(newValue.shareRadio == 1)//用户
			            	{
			            		 chosedShareIds=[];
			            		 shareType = 1;
			            		 shareColumns=[];
			            		 shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
			            		 shareColumns.push({ text: '主键',  dataIndex: 'IID',hidden:true});
			            		 shareColumns.push({ text: '用户名称',  dataIndex: 'IFULLNAME',width:200});
			            		 shareColumns.push({ text: '用户ID',  dataIndex: 'ILOGINNAME',width:200,flex:1});
						 
					          	 shareGrid.reconfigure(shareColumnStore,shareColumns);
					             shareColumnStore.load();
					             shareGrid.show();
			            	}
			            	else if(newValue.shareRadio == 2)//组
			            	{
			            		  chosedShareIds=[];
			            		  shareType = 2;
			            		  shareColumns=[];
								  shareColumns =[{ text: '序号', xtype:'rownumberer', width: 40 }];
			            		  shareColumns.push({ text: '主键',  dataIndex: 'IID',hidden:true});
			            		  shareColumns.push({ text: '用户组名称',  dataIndex: 'IGROUPNAME',width:200});
			            		  shareColumns.push({ text: '用户组描述',  dataIndex: 'IGROUPDES',width:160,flex:1});
						          shareGrid.reconfigure(shareColumnStore,shareColumns);
						          shareColumnStore.load();
						          shareGrid.show();
			            	}
			            }
			        }
				}); 
				shareGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
					region: 'center',
					autoScroll: true,
				    store : shareColumnStore,
				    border:false,
//				    bbar:shareColumnStorePageBar,
				    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				    cls:'customize_panel_back',
				    columnLines : true,
				    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
				    columns : shareColumns,
					listeners: {
			        select: function( e, record, index, eOpts ){ 
			        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
			        		chosedShareIds.push(record.get('IID'));
			        	}
			        },
			        deselect: function( e, record, index, eOpts ){ 
			        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
			        		chosedShareIds.remove(record.get('IID'));
			        	}
			        }
			    }
				});
			shareGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
				region: 'center',
				autoScroll: true,
			    store : shareColumnStore,
			    border:false,
//			    bbar:shareColumnStorePageBar,
			    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			    columnLines : true,
			    cls:'customize_panel_back',
			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
			    columns : shareColumns,
				listeners: {
		        select: function( e, record, index, eOpts ){ 
		        	if(chosedShareIds.indexOf(record.get('IID'))==-1) {
		        		chosedShareIds.push(record.get('IID'));
		        	}
		        },
		        deselect: function( e, record, index, eOpts ){ 
		        	if(chosedShareIds.indexOf(record.get('IID'))>-1) {
		        		chosedShareIds.remove(record.get('IID'));
		        	}
		        }
		    }
			});
				var objectName = new Ext.form.TextField({
					name : 'objectName',
					//fieldLabel : '对象名称',
					emptyText : '--对象名称--',
					//labelWidth : 65,
			//		padding : '5',
					width :'20%',
			        //labelAlign : 'right',
			     //  value: filter_serviceName,
			        listeners: {
			            specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	if(shareRadioComment.getChecked().length==0){
									Ext.MessageBox.alert("提示", "请选择共享类型");
									return;
								}else{
									var shareRadio =shareRadioComment.getChecked()[0].inputValue;
									if(shareRadio == 0  ){
										Ext.MessageBox.alert("提示", "只能查询共享类型为用户、用户组的对象");
										return;
									}
								}
			                	shareGrid.ipage.moveFirst();
			                }
			            }
			        }
				});
			var share_form = Ext.create('Ext.ux.ideal.form.Panel', {
				//layout : 'anchor',
				region: 'north',
				cls:'customize_panel_back',
				//buttonAlign : 'center',
				//bodyCls : 'x-docked-noborder-top',
				border : false,
				 dockedItems:{	
					    	xtype: 'toolbar',
					        dock: 'top',
					        border:false,
					        items: [shareRadioComment,'->',objectName,{
						        			xtype : 'button',
											cls : 'Common_Btn',
											text : '查询',
											handler : function(){
												if(shareRadioComment.getChecked().length==0){
												
													Ext.MessageBox.alert("提示", "请选择共享类型");
																return;
												}else{
														var shareRadio =shareRadioComment.getChecked()[0].inputValue;
														if(shareRadio == 0  ){
															Ext.MessageBox.alert("提示", "只能查询共享类型为用户、用户组的对象");
															return;
														}
												}
												shareGrid.ipage.moveFirst();
											}
										},{
					        			xtype : 'button',
										cls : 'Common_Btn',
										text : '确定',
										handler : function(){
												if(shareRadioComment.getChecked().length==0){
													Ext.MessageBox.alert("提示", "请选择共享类型");
																return;
												}
										        var shareRadio =shareRadioComment.getChecked()[0].inputValue;
												if(shareRadio == 0 || shareRadio ==1 || shareRadio ==2){
													if(shareRadio ==0){//所有人
														shareType = 0;
														confirmShare(isflow);
													}else if(shareRadio ==1){//用户
														shareType = 1;
														if(chosedShareIds.length ==0){
															Ext.MessageBox.alert("提示", "请选择要共享的用户");
															return;
														}else{ 
															confirmShare(isflow);
														}
													}else{ //组 2
														shareType = 2;
														if(chosedShareIds.length ==0){
															Ext.MessageBox.alert("提示", "请选择要共享的用户组");
															return;
														}else{
															confirmShare(isflow);
														}
													}
												}else{
													Ext.MessageBox.alert("提示", "请选择共享模式！");
													return;
												}
										}
					        }]
						}
			});
				var pagetab = Ext.create ('Ext.tab.Panel',
				{
				    tabPosition : 'top',
				    cls:'customize_panel_back',
//				    cls:'window_border panel_space_top panel_space_left panel_space_right',
				    region : 'center',
				    activeTab : 0,
				    //width : '100%',
				    width: 700,
	                minWidth: 350,
	                height: 450,
				    border : false,
//				    autoScroll: true,
				    items : [
							{
								title : '待共享',
								layout:'border',
								items:[share_form,shareGrid]
							},
							{
								title : '已共享',
								layout:'fit',
								items:[shareedGrid]
							}
				    ]
				});
				
				shareWin = Ext.create('widget.window', {
	                title: '共享列表',
	                closable: true,
	                closeAction : 'destroy',
	                modal: true,
	                width: 700,
	                minWidth: 350,
	                height: 550,
	                layout: {
	                    type: 'border',
	                    padding: 5
	                },
	                items: [pagetab]
	                
				});
				shareWin.show();		
		    }
		
	    //从一个json对象中，解析出key=iid的value,返回改val
		function parsIIDJson(key ,jsonObj){
			 //var eValue=eval('jsonObj.'+key);  
			 return jsonObj[''+key+''];
		}    
		    
	    // 将被选中的记录的flowid组织成json串，作为参数给后台处理
		function getSelectedJsonData(){
			var flowIdList = flowTemplateCustomGrid.getSelectionModel().getSelection();
			if (flowIdList.length < 1) {
				return;
			}
			var jsonData = "[";
			for ( var i = 0, len = flowIdList.length; i < len; i++) {
				if (i == 0)
				{
					jsonData = jsonData + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
				} else {
					jsonData = jsonData + "," + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
				}
			}
			jsonData = jsonData + "]";
			return jsonData ;
		}
		function confirmShare(isflow){
			var url = 'scriptService/serviceRelease.do';
			var message="常用任务共享成功";
			var errorMessage="常用任务共享失败";
			 
			var jsonData = getSelectedJsonData();
			if(jsonData=="[]"){
				Ext.MessageBox.alert("提示", "获取json失败");
				return ;
			}	
			 
		    Ext.Ajax.request({
			    url : url,
			    method : 'POST',
			    params : {
			  	  jsonData : jsonData,
			  	  optionState:5, //常用任务共享
			  	  shareType:shareType,
			  	  isflow:isflow,
			  	  isCustomTask:1,
			  	  chosedShareIds:chosedShareIds
			    },
			    success: function(response, opts) {
			    	 var success = Ext.decode(response.responseText).success;
			         if (success) {
			            Ext.MessageBox.show({
			                title : "提示",
			                msg : message,
			                buttonText: {
			                    yes: '确定'
			                },
			                buttons: Ext.Msg.YES
			              });
			          } else {
			            Ext.MessageBox.show({
			              title : "提示",
			              msg : errorMessage,
			              buttonText: {
			                  yes: '确定'
			              },
			              buttons: Ext.Msg.YES
			            });
			          } 
			    	shareWin.close();
			    	flowTemplateCustomStore.reload();
			    },
			    failure: function(result, request) {
			    	secureFilterRs(result,"操作失败！");
			    }
		    });
		}
	    function saveFun(){
	    	if (Ext.isIE) {
				CollectGarbage();
			}
	    	var m = flowTemplateCustomStore.getModifiedRecords();
			if (m.length == 0) {
				Ext.MessageBox.alert("提示", "没有需要保存的条目！");
				return;
			}
			var jsonData = "[";
			for (var i = 0, len = m.length; i < len; i++) {
				var ss = Ext.JSON.encode(m[i].data);
				if (i == 0){
					jsonData = jsonData + ss;
				}
				else{
					jsonData = jsonData + "," + ss;
				}
			}
			jsonData = jsonData + "]";
			console.log(jsonData);
			Ext.Ajax.request({
			    url: 'updateTemplateAndRelation.do',
				params : {
					jsonData : jsonData
				},	
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						flowTemplateCustomStore.load();
						Ext.Msg.alert("提示", message);
					} else {
						Ext.Msg.alert("提示", message);
					}
				},
				failure : function(result, request) {
					secureFilterRs(result,"请求返回失败！",request);
				}
			});
	    }
	    
	    function deleteFun(){
			var seledCnt = selModel.getCount();
			if (seledCnt < 1) {
				Ext.MessageBox.alert("提示", "请选择要解绑的常用任务！");
				return;
			}
			Ext.MessageBox.buttonText.yes = "确定";
			Ext.MessageBox.buttonText.no = "取消";
			Ext.Msg.confirm("确认删除", "是否解绑选中的常用任务", function(id) {
				if (id == 'yes'){
					var signNum = 0;
					var ids = '';
					var flowIdList = flowTemplateCustomGrid.getSelectionModel().getSelection();
					for (var i = 0, len = flowIdList.length; i < len; i++) {
						if (signNum == 0) {
						ids = ids +  flowIdList[i].data.iid;
						} else {
							ids = ids + "," +  flowIdList[i].data.iid;
						}
					signNum = signNum + 1;
					}
					Ext.Ajax.request({
				    url: 'deleteTemplateAndRelation.do',
					params : {
						ids : ids
					},	
					success : function(response, request) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if (success) {
							flowTemplateCustomStore.load();
							Ext.Msg.alert("提示", message);
					} else {
						Ext.Msg.alert("提示", message);
						}
					},
					failure : function(result, request) {
						secureFilterRs(result,"请求返回失败！",request);
							}
					});
			  }
			});
			
		}
	    
		var northPanel = Ext.create('Ext.panel.Panel', {
			border : false,
			region: 'north',
			height : 125,
			loader : {
				url : 'newlyFlowTemplateManagerForProductBSinfo.do',
				autoLoad : true,
				scripts : true,
				params: {
					sName:sName.getValue(),
		    		templateName: templateName.getValue(),
		    		isEMscript: (null!=emScriptCb.getValue())?emScriptCb.getValue():-1,
		    	    iappSysIds:appSysObj.getValue(),
		    		flag: 1,
		    		pagesizeStart:0,
		    		pagesizelimit:6
				}
			}
		});
		function BSinfoloader(){
			northPanel.doLayout();
			northPanel.getLoader().load({
				url: 'newlyFlowTemplateManagerForProductBSinfo.do',
				params: {
					sName:sName.getValue(),
		    		templateName: templateName.getValue(),
		    		isEMscript: (null!=emScriptCb.getValue())?emScriptCb.getValue():-1,
		    	    iappSysIds:appSysObj.getValue(),
		    	    pagesizeStart:0,
		    		pagesizelimit:6
				},
				scripts: true
			});
		}			
		 var mainPanel = Ext.create('Ext.panel.Panel',{
			 renderTo : "newlyFlowTemplateManagerForProduct_area",
		        width : contentPanel.getWidth(),
			    height :contentPanel.getHeight() - modelHeigth,
		        border : false,
		        layout: 'border',
		        items : [northPanel,search_form,flowTemplateCustomGrid]
		});
		 
			contentPanel.on ('resize', function (){
				mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
				mainPanel.setWidth (contentPanel.getWidth () );
			});
			
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
			Ext.destroy(mainPanel);
			if(Ext.isIE){
				CollectGarbage(); 
			}
		});
	
		
		
function editFlowCustomTemplateForProduct(iid, serviceId, customName, serviceName, bussId, bussTypeId, scriptLevel,menuId,isFlow,scriptType,platForm,filter_serverNameQuery,filter_templateNameQuery) {
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: 'flowCustomizedInitScriptService.do',
	params: {
		iid:serviceId,
		serviceId:serviceId,
		customId: iid,
		customName:customName,
		serviceName:serviceName,
		bussId:bussId,
		bussTypeId:bussTypeId,
		scriptLevel: scriptLevel,
		submitType : 'te',
		actionType : 'model',
		isScriptConvertToFlow:isFlow==0,
		menuId: menuId,
		filter_templateNameQuery:filter_templateNameQuery,
		filter_serverNameQuery:filter_serverNameQuery,
		ifrom: 'flowCustomizedTemplateForProduct.do',
		flag:1
	},
	scripts: true});
}
 
function editFlowCustomTemplateForNoConvert(iid, serviceId, customName, serviceName, bussId, bussTypeId, scriptLevel,menuId,isFlow,scriptType,platmFrom){
	 coustomConfigWin = Ext.create('widget.window', {
       title: '配置常用任务',
       closable: true,
       closeAction: 'destroy',
       width: contentPanel.getWidth(),
       minWidth: 350,
       height: contentPanel.getHeight(),
       draggable: false,
       // 禁止拖动
       resizable: false,
       // 禁止缩放
       modal: true,
       loader: {
           url: 'queryOneServiceForCustom.do',
           params: {
        	   customId:iid,
               iid: serviceId,
               scriptType: scriptType,
               scriptLevel: scriptLevel,
               serviceName:serviceName,
               platmFrom :platmFrom//适用平台
           },
           autoLoad: true,
           scripts: true
       }
   });
	 coustomConfigWin.show();
}

function submitFlowCustomTemplateForProduct(iid, scriptLevel,taskNameText, audiLoginName,isFlow,serviceName,serviceId,customName,bussId, bussTypeId,menuId,scriptType,platForm) {
	if(showConfigSwitchFlowTemplateManager && isFlow==0){
		editFlowCustomTemplateForNoConvert(iid, serviceId, customName, serviceName, bussId, bussTypeId, scriptLevel,menuId,isFlow,scriptType,platForm);
	}else{
	var isOk = false;
	var version_flag = true;//单号显示标识
	if(typeof taskTypeFlowTemplateManager === 'undefined' || !scriptOddNumberSwitch){
		version_flag = true;
    }else if(execUserSwitchFlowTemplateManager&&taskTypeFlowTemplateManager=='taskNormal'){
    	version_flag = false;
    }
	Ext.Ajax.request({
		url: 'validFlowCustomTemplateData.do',
		method: 'POST',
		async: false,
		params: {
			iid: iid,
			flag: 1
		},
		success: function(response, options) {
			var success = Ext.decode(response.responseText).success;
			if(success) {
				isOk = true;
			} else {
				var errorMessage = Ext.decode(response.responseText).message;
				Ext.Msg.alert('提示', errorMessage);
			}
		},
		failure: function(result, request) {
			Ext.Msg.alert('提示', '校验模板启动数据信息失败！');
		}
	});
	
	if(!isOk) {
		return;
	}
	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	var auditorStore_tap = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecAuditorList.do?scriptLevel='+scriptLevel,
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    },
	    listeners : {
            load : function(store, records, success, eOpts) {  
            	$.each(records, function(index, record){
            		if(record.get('loginName')==audiLoginName) {
            			auditorComBox_tap.select(record);    
            		}
            	});
            }  
        }  
	});
	var execStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecUserList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });	
	var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
	    fieldLabel: "审核人",
	    store: auditorStore_tap,
	    queryMode: 'local',
	    width: 390,
	    displayField: 'fullName',
	    valueField: 'loginName',
	    hidden:scriptLevel==0?true:false,
	    labelWidth : 65,
		labelAlign : 'right',
		listeners: { //监听 
	        render : function(combo) {//渲染 
	            combo.getStore().on("load", function(s, r, o) { 
	                combo.setValue(r[0].get('loginName'));//第一个值 
	            }); 
	        } 
	    } 
	  });
	
	var execComBox_tap = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "执行人",
	    store: execStore,
	    queryMode: 'local',
	    width: 390,
	    displayField: 'fullName',
	    valueField: 'loginName',
	    labelWidth : 65,
	    editable : true,
	    hidden:!execUserSwitchFlowTemplateManager|| scriptLevel==0?true:false,
		labelAlign : 'right'
	  });
	execStore.load({
	    callback : function (records, operation, success)
	    {
	    	execComBox_tap.setValue (loginUserFlowTemplateManager);
	    }
    });
	function CurentTime()
    { 
        var now = new Date();
        
        var year = now.getFullYear();       //年
        var month = now.getMonth() + 1;     //月
        var day = now.getDate();            //日
        
        var hh = now.getHours();            //时
        var mm = now.getMinutes();          //分
        var ss = now.getSeconds();           //秒
        
        var clock = year;
        
        if(month < 10){
        	
        	clock += "0";
        }else{
        	clock += "";
        }
        
        clock +=month;
        
        if(day < 10){
        	clock += "0";
        }else{
        	clock += "";
        }
            
            
        clock += day;
        
        if(hh < 10){
        	clock += "0";
        }else{
        	clock += "";
        }
            
            
        clock += hh;
        if (mm < 10) 
        {
        	clock += '0'; 
        	}
        else{
        	clock += "";
        }
        clock += mm ; 
         
        if (ss < 10) {
        	clock += '0'; 
        }
        else{
        	clock += "";
        }
        clock += ss; 
        return(clock); 
    }
	var taskName = new Ext.form.TextField({
		name: 'taskName',
		fieldLabel: '任务名称',
		emptyText: '',
		labelWidth : 65,
		padding : '5 0 5 0',
		labelAlign : 'right',
		value: serviceName+'_'+CurentTime(),
		width: 390
	});
	var execDesc = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '执行描述',
        labelWidth: 65,
        padding: '0 2 0 4',
        height: 60,
        maxLength: 2000,
        width: 385,
        autoScroll: true
    });
	var butterflyVerison = new Ext.form.TextField({
		name: 'butterflyversion',
		fieldLabel: '单号',
		padding: '0 2 0 8',
		hidden:version_flag,
		emptyText: '',
		labelWidth : 65,
		width: 382
	});
	var checkVersion=new  Ext.form.Checkbox({   
        id:"checkVersion",               
	    name:"checkVersion",
	    boxLabel:"单号补添:" ,
	    boxLabelAlign:"before",
	    labelWidth:65,
	    padding: '0 2 0 4',
	    hidden:version_flag
	});
	Ext.create('Ext.window.Window', {
  		title : '提交配置信息',
  		autoScroll : true,
  		modal : true,
  		resizable : false,
  		closeAction : 'destroy',
  		width : 420,
  		height : scriptLevel==0?(version_flag?235:305):(version_flag?290:365),
  		items:[taskName, auditorComBox_tap,execComBox_tap,butterflyVerison,checkVersion,execDesc],
  		buttonAlign: 'center',
  		buttons: [{ 
  			xtype: "button", 
  			text: "确定", 
  			handler: function () {
  				var self = this;
  				var auditor = auditorComBox_tap.getValue();
  				if(!auditor) {
  					Ext.Msg.alert('提示', "没有选择审核人！");
  					return;
  				}
  				var execUser = execComBox_tap.getValue();
  				var taskN = Ext.util.Format.trim(taskName.getValue());
  				if(Ext.isEmpty(taskN)) {
  					Ext.Msg.alert('提示', "任务名称不能为空！");
  					return;
  				}
  				
  				if (fucCheckLength(taskN) > 255) {
                    Ext.Msg.alert('提示', "任务名称不能超过255字符！");
                    return;
                }
  				var butterflyV = "";
  				var check =Ext.getCmp("checkVersion").getValue();
  				if(execUserSwitchFlowTemplateManager&&!check&&!butterflyVerison.hidden){
  					butterflyV=Ext.util.Format.trim(butterflyVerison.getValue());
  					if(butterflyV==null||butterflyV==""){
  						Ext.Msg.alert('提示', "单号不能为空！");
	  					return;
  					}
  					if (fucCheckLength(butterflyV) > 255) {
                        Ext.Msg.alert('提示', "单号不能超过255字符！");
                        return;
                    }
  				}
  				var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
  				if(Ext.isEmpty(execDescForExec)) {
  					Ext.Msg.alert('提示', "没有填写执行描述！");
  					return;
  				}
  				if(fucCheckLength(execDesc.getValue() > 2000)) {
  					Ext.Msg.alert('提示', "执行描述内容长度超过2000个字符！");
  					return;
  			    }
  				if(noScriptConvertSwitch && isFlow==0){
//先查出数据再组织数据
  					Ext.Ajax.request({
							url : 'getCustomTemplateData.do',
							method : 'POST',
							params : {
								customId : iid
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								if (success) {
									var startData = Ext.decode(response.responseText).startData;
									var execUser1 = Ext.decode(response.responseText).execUser;
									var agents = Ext.decode(response.responseText).agents;
									var agents1 = [];
									agents1.push(agents);
									var isSql = Ext.decode(response.responseText).isSql;
									var en = Ext.decode(response.responseText).eachNum;
									var params = Ext.decode(response.responseText).params;
									if(!isSql){
										Ext.Ajax.request({
					  						url :'scriptExecAuditing.do' ,
					  						method : 'POST',
					  						params : {
					  							serviceId: serviceId,
					  							execUser: execUser1,
					  							agents: agents1,
					  							rgIds: '',
					  							params: params,
					  							execDesc:execDescForExec,
					  							auditor: auditor,
					  							isDelay:false,
					  							execTime:'',
					  							execStrategy:0,
					  							taskName: taskN,
					  							performUser:execUser,
					  							butterflyversion:butterflyV,
					  							data: startData,
					  							eachNum: en,
					  							scriptLevel: scriptLevel
					  						},
					  						success: function(response, opts) {
					  							var success = Ext.decode(response.responseText).success;
					  							var message = Ext.decode(response.responseText).message;
					  							if(success) {
					  								if(scriptLevel!=0){
					  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
					  								} 
					  								var iworkItemid = Ext.decode(response.responseText).workItemId;
					  								if(scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
					  									Ext.Ajax.request ({
					  										url : 'scriptExecForOneRecord.do',
					  										method : 'POST',
					  										params :
					  										{
					  											iworkItemid: iworkItemid
					  										},
					  										success : function (response, opts)
					  										{
					  											var success = Ext.decode (response.responseText).success;
					  											if(success){
					  													var	uuid ='';
		  					  											Ext.Ajax.request({
		  					  								                url: 'queryUuidById.do',
		  					  								                method: 'POST',
		  					  								                async: false,
		  					  								                params: {
		  					  								                	serviceId: serviceId
		  					  								                },
		  					  								                success: function(response, options) {
		  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
		  					  								                },
		  					  								                failure: function(result, request) {
		  					  								                }
		  					  								            });
					  													Ext.Ajax.request({
					  														url : 'execScriptServiceStart.do',
					  														method : 'POST',
					  														params : {
					  															serviceId : serviceId,
					  															uuid:uuid,
					  															serviceName : serviceName,
					  															scriptType:0,
					  															workItemId : iworkItemid,
					  															coatId : 0,
					  															isFlow: 1
					  														},
					  														success : function(response, request) {
					  															var success = Ext.decode(response.responseText).success;
					  															if (success) {
					  																var flowId = Ext.decode(response.responseText).content;
					  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
					  															}
					  														},
					  														failure : function(result, request) {
					  															Ext.Msg.alert('提示', '执行失败！');
					  														}
					  													}); 
					  												
					  											}
					  										}
					  									});
					  								}
					  								self.up("window").close();
					  							} else {
					  								Ext.MessageBox.alert("提示", message);
					  							}
					  						},
					  						failure: function(result, request) {
					  							secureFilterRs(result,"操作失败！");
					  							self.up("window").close();
					  						}
					  					});
									}else{
										Ext.Ajax.request({
						  						url : 'scriptExecAuditingForSQL.do',
						  						method : 'POST',
						  						params : {
						  							serviceId: serviceId,
						  							execUser: execUser1,
						  							agentsJson:agents,
						  							rgIds: '',
						  							params: params,
						  							execDesc:execDescForExec,
						  							auditor: auditor,
						  							isDelay:false,
						  							execTime:'',
						  							taskName: taskN,
						  							performUser:execUser,
						  							butterflyversion:butterflyV,
						  							data: startData,
						  							eachNum: en,
						  							scriptLevel: scriptLevel
						  						},
						  						success: function(response, opts) {
						  							var success = Ext.decode(response.responseText).success;
						  							var message = Ext.decode(response.responseText).message;
						  							if(success) {
						  								if(scriptLevel!=0){
						  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
						  								}
						  								var iworkItemid = Ext.decode(response.responseText).workItemId;
						  								if(scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
						  									Ext.Ajax.request ({
						  										url : 'scriptExecForOneRecord.do',
						  										method : 'POST',
						  										params :
						  										{
						  											iworkItemid: iworkItemid
						  										},
						  										success : function (response, opts)
						  										{
						  											var success = Ext.decode (response.responseText).success;
						  											if(success){
						  												if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
						  													var	uuid ='';
			  					  											Ext.Ajax.request({
			  					  								                url: 'queryUuidById.do',
			  					  								                method: 'POST',
			  					  								                async: false,
			  					  								                params: {
			  					  								                	serviceId: serviceId
			  					  								                },
			  					  								                success: function(response, options) {
			  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
			  					  								                },
			  					  								                failure: function(result, request) {
			  					  								                }
			  					  								            });
						  													Ext.Ajax.request({
						  														url : 'execScriptServiceStart.do',
						  														method : 'POST',
						  														params : {
						  															serviceId : serviceId,
						  															uuid:uuid,
						  															serviceName : serviceName,
						  															scriptType:'sql',
						  															workItemId : iworkItemid,
						  															coatId : 0,
						  															isFlow: 0
						  														},
						  														success : function(response, request) {
						  															var success = Ext.decode(response.responseText).success;
						  															if (success) {
						  																var flowId = Ext.decode(response.responseText).content;
						  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
						  															}
						  														},
						  														failure : function(result, request) {
						  															Ext.Msg.alert('提示', '执行失败！');
						  														}
						  													}); 
						  												}
						  											}
						  										}
						  									});
						  								}
						  								self.up("window").close();
						  							} else {
						  								Ext.MessageBox.alert("提示", message);
						  							}
						  						},
						  						failure: function(result, request) {
						  							secureFilterRs(result,"操作失败！");
						  							self.up("window").close();
						  						}
						  					});
									}
								
								}else{
									Ext.Msg.alert('提示', '获取审核参数失败！');
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示', '执行失败！');
							}
						}); 
  				
  				}else{
  				Ext.Ajax.request({
  				    url : 'submitFlowCustomTemplateForProduct.do',
  				    method : 'POST',
  				    params : {
  				    	iid:iid,
  				    	auditor: auditor,
  				    	taskName: taskN,
  				    	execUser:execUser,
	  				    execDesc:execDescForExec,
	  				    butterflyVersion:butterflyV
  				    },
  				    success: function(response, opts) {
  				        var success = Ext.decode(response.responseText).success;
  				        var message = Ext.decode(response.responseText).message;
  				        if(success) {
  				        	var iworkItemid = Ext.decode(response.responseText).workItemId;
  				        	if(scriptLevel!=0){
  								Ext.MessageBox.alert("提示", "请求已经发送到审核人");
  							}
  				        	self.up("window").close();
  				        	if(scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
									 Ext.Ajax.request ({
									 url : 'scriptExecForOneRecord.do',
									 method : 'POST',
									 params :
									 {
										 iworkItemid: iworkItemid
									 },
									 success : function (response, opts)
									 {
										 var success = Ext.decode (response.responseText).success;
										 if(success && isFlow==0) {
											 Ext.Ajax.request ({
												 url : 'scriptExecForScriptConvertFlowToOrgData.do',
												 method : 'POST',
												 params :
												 {
													 iworkItemid:iworkItemid,
													 serviceId: serviceId 
												 },
												 success : function (response, opts)
												 {
													 var success = Ext.decode (response.responseText).success;
													 if(success){
														var	uuid ='';
			  											Ext.Ajax.request({
			  								                url: 'queryUuidById.do',
			  								                method: 'POST',
			  								                async: false,
			  								                params: {
			  								                	serviceId: serviceId
			  								                },
			  								                success: function(response, options) {
			  								                    uuid = Ext.decode(response.responseText).serviceUuid;
			  								                },
			  								                failure: function(result, request) {
			  								                }
			  								            });
  	  													Ext.Ajax.request({
  	  													url : 'execScriptServiceStart.do',
  	  													method : 'POST',
  	  													params : {
  	  														serviceId : serviceId,
  	  														uuid:uuid,
  	  														serviceName :serviceName,
  	  														scriptType:0,
  	  														workItemId : iworkItemid,
  	  														coatId : 0,
  	  														isFlow: 0
  	  													},
  	  													success : function(response, request) {
  	  														var success = Ext.decode(response.responseText).success;
  	  														if (success) {
  	  															var flowId = Ext.decode(response.responseText).content;
  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
  	  														}
  	  													},
  	  													failure : function(result, request) {
  	  														Ext.Msg.alert('提示', '执行失败！');
  	  													}
  	  												}); 
  	  													 
  	  												 }
												 }
											 });
										 }else if(success && isFlow!=0){
														 var	uuid ='';
				  											Ext.Ajax.request({
				  								                url: 'queryUuidById.do',
				  								                method: 'POST',
				  								                async: false,
				  								                params: {
				  								                	serviceId: serviceId
				  								                },
				  								                success: function(response, options) {
				  								                    uuid = Ext.decode(response.responseText).serviceUuid;
				  								                },
				  								                failure: function(result, request) {
				  								                }
				  								            });
	  	  													Ext.Ajax.request({
	  	  													url : 'execScriptServiceStart.do',
	  	  													method : 'POST',
	  	  													params : {
	  	  														serviceId : serviceId,
	  	  														uuid:uuid,
	  	  														serviceName : serviceName,
	  	  														scriptType:0,
	  	  														workItemId : iworkItemid,
	  	  														coatId : 0,
	  	  														isFlow: 1
	  	  													},
	  	  													success : function(response, request) {
	  	  														var success = Ext.decode(response.responseText).success;
	  	  														if (success) {
	  	  															var flowId = Ext.decode(response.responseText).content;
	  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
	  	  														}
	  	  													},
	  	  													failure : function(result, request) {
	  	  														Ext.Msg.alert('提示', '执行失败！');
	  	  													}
	  	  												}); 
			  	  									 }
									 }
								 });
					    }
  				        } else {
  				        	Ext.MessageBox.alert("提示", message);
  				        }
  				    },
  				    failure: function(result, request) {
  				    	secureFilterRs(result,"操作失败！");
  				    }
  			    });
  				}
  			}
	        }]
  		
  	}).show();
	auditorStore_tap.load();
}
}		

});

