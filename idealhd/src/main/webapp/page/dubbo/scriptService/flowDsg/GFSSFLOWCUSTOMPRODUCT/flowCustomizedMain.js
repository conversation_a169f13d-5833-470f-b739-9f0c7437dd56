/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var instanceNameObjOutSideGFSSFLOWCUSTOMPRODUCT;
var customNameObjOutSideGFSSFLOWCUSTOMPRODUCT;
var editorSonGFSSFLOWCUSTOMPRODUCT;

var bussCbOutSideGFSSFLOWCUSTOMPRODUCT;
var bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT;

Ext.onReady(function() {
    //清理各种监听
    destroyRubbish();

    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    bussCbOutSideGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        queryMode: 'local',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT.clearValue();
                bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT.applyEmptyText();
                bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    bussData.on('load',
    function() {
        if (bussIdGFSSFLOWCUSTOMPRODUCT != null && typeof(bussIdGFSSFLOWCUSTOMPRODUCT) != "undefined") {
            bussCbOutSideGFSSFLOWCUSTOMPRODUCT.setValue(bussIdGFSSFLOWCUSTOMPRODUCT);
            bussCbOutSideGFSSFLOWCUSTOMPRODUCT.fireEvent('select');
            bussCbOutSideGFSSFLOWCUSTOMPRODUCT.setReadOnly(true);
        }
    });
    bussTypeData.on('load',
    function() {
        if (bussTypeIdGFSSFLOWCUSTOMPRODUCT != null && typeof(bussTypeIdGFSSFLOWCUSTOMPRODUCT) != "undefined") {
            bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT.setValue(bussTypeIdGFSSFLOWCUSTOMPRODUCT);
            bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT.setReadOnly(true);
        }
    });

    //0.1 Graph 
    var mainTabs = Ext.widget('tabpanel', {
        tabPosition: 'top',
        activeTab: 0,
        width: '100%',
        height: contentPanel.getHeight() - 104,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        items: [{
            title: '图形化显示',
            loader: {
                url: 'flowCustomizedImgScriptServiceGFSSFLOWCUSTOMPRODUCT.do',
                params: {
                    flag: flagGFSSFLOWCUSTOMPRODUCT,
                    actionType: actionTypeGFSSFLOWCUSTOMPRODUCT
                },
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        },
        {
            hidden: true,
            title: '列表显示',
            loader: {
                url: 'flowCustomizedListScriptServiceGFSSFLOWCUSTOMPRODUCT.do',
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        }]
    });

    //1.1  实例信息  相关控件	
    instanceNameObjOutSideGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.form.field.Text', {
        padding: '0 5 0 0',
        fieldLabel: '服务名称'
    });
    customNameObjOutSideGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.form.field.Text', {
        padding: '0 5 0 0',
        fieldLabel: '模板名称'
    });
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
        margin: '0 0 0 5',
        textAlign: 'center',
        handler: function() {
        	saveCustomTemplateGFSSFLOWCUSTOMPRODUCT();
        }
    });
    var backButton = Ext.create('Ext.Button', {
        text: '返回',
        margin: '0 0 0 5',
        textAlign: 'center',
        handler: function() {
            destroyRubbish();
            contentPanel.getLoader().load({
                url: 'forwardScriptServiceReleaseFlow.do',
                params: {
                    'filter_bussId': filter_bussIdGFSSFLOWCUSTOMPRODUCT,
                    'filter_bussTypeId': filter_bussTypeIdGFSSFLOWCUSTOMPRODUCT,
                    'filter_scriptName': filter_scriptNameGFSSFLOWCUSTOMPRODUCT,
                    'filter_serviceName': filter_serviceNameGFSSFLOWCUSTOMPRODUCT,
                    'filter_scriptType': filter_scriptTypeGFSSFLOWCUSTOMPRODUCT
                }
            });
            if (Ext.isIE) {
                CollectGarbage();
            }
        }
    });

    var queryForm = Ext.create('Ext.form.Panel', {
        border: false,
        frame: true,
        width: '100%',
        fieldDefaults: {
            labelWidth: 60,
            labelAlign: "left",
            width: '25%'
        },
        items: [{
            xtype: "container",
            layout: "hbox",
            margin: 1,
            items: [customNameObjOutSideGFSSFLOWCUSTOMPRODUCT, bussCbOutSideGFSSFLOWCUSTOMPRODUCT, bussTypeCbOutSideGFSSFLOWCUSTOMPRODUCT, instanceNameObjOutSideGFSSFLOWCUSTOMPRODUCT]
        }]
    });

    var submitFromPanel = Ext.create('Ext.form.Panel', {
        buttonAlign: 'center',
        width: '100%',
        frame: true,
        buttons: [saveButton, backButton]
    });

    var MainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "flowCustomizedMainDivGFSSFLOWCUSTOMPRODUCT",
        width: '100%',
        height: contentPanel.getHeight(),
        autoScroll: true,
        border: false,
        bodyPadding: 5,
        items: [queryForm, mainTabs, submitFromPanel]
    });
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(MainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        mainTabs.setWidth('100%');
    });
    initGFSSFLOWCUSTOMPRODUCT();

});
function initGFSSFLOWCUSTOMPRODUCT() {
    if (null != serviceNameGFSSFLOWCUSTOMPRODUCT && '' != serviceNameGFSSFLOWCUSTOMPRODUCT) {
        instanceNameObjOutSideGFSSFLOWCUSTOMPRODUCT.setValue(serviceNameGFSSFLOWCUSTOMPRODUCT);
        instanceNameObjOutSideGFSSFLOWCUSTOMPRODUCT.setReadOnly(true);
    }
}