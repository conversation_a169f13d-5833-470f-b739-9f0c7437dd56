/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlow;
var cellObj;
var phaseDataList;
var editor;
var graph;
var model;
var cmdNameCount = 1;
var actNameCountMap = {};
if(!actStartInfo.hasOwnProperty('GFSS'+iid)) {
	actStartInfo['GFSS'+iid] = {};
}


//var instanceNameObj = null;
Ext.onReady (function ()
{
	try
	{
		if (configwindowFlow != null)
		{
			configwindowFlow.destroy ();
		}
		if (cellObj != null)
		{
			cellObj.destroy ();
		}
		if (phaseDataList != null)
		{
			phaseDataList= null;
		}
		if (editor != null)
		{
			editor.destroy ();
		}
		if (graph != null)
		{
			graph.destroy ();
		}
		if (model != null)
		{
			model=null;
		}
	}
	catch(err)
	{
	}
	getPhase ();
});

/**
 * 图形化界面初始化方法
 */
function main (container, outline, toolbar, sidebar, status)
{
	
	// Checks if the browser is supported
	if (!mxClient.isBrowserSupported ())
	{
		// Displays an error message if the browser is not supported.
//		mxUtils.error ('Browser is not supported!', 200, false);
		Ext.Msg.alert ('提示', '当前浏览器不支持此功能!');
	}
	else
	{
		//自定义连线图标
		mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
		// Assigns some global constants for general behaviour, eg. minimum
		// size (in pixels) of the active region for triggering creation of
		// new connections, the portion (100%) of the cell area to be used
		// for triggering new connections, as well as some fading options for
		// windows and the rubberband selection.
		mxConstants.MIN_HOTSPOT_SIZE = 16;
		mxConstants.DEFAULT_HOTSPOT = 1;
		
		// Enables guides
		mxGraphHandler.prototype.guidesEnabled = true;
		
		// Alt disables guides
		mxGuide.prototype.isEnabledForEvent = function (evt)
		{
			return !mxEvent.isAltDown (evt);
		};
		
		// Enables snapping waypoints to terminals
		mxEdgeHandler.prototype.snapToTerminals = true;
		
		// Workaround for Internet Explorer ignoring certain CSS directives
		if (mxClient.IS_QUIRKS)
		{
			document.body.style.overflow = 'hidden';
			new mxDivResizer (container);
			new mxDivResizer (outline);
			new mxDivResizer (toolbar);
			new mxDivResizer (sidebar);
			new mxDivResizer (status);
		}
		
		// Creates a wrapper editor with a graph inside the given container.
		// The editor is used to create certain functionality for the
		// graph, such as the rubberband selection, but most parts
		// of the UI are custom in this example.
		 editor = new mxEditor ();
		 parent.editorSon=editor;
		 graph = editor.graph;
		 model = graph.getModel ();
		 
		 graph.setTooltips(false);
		
		// Disable highlight of cells when dragging from toolbar
		graph.setDropEnabled (false);
		
		// Uses the port icon while connections are previewed
		/*//连线样式与图标相同
		graph.connectionHandler.getConnectImage = function (state)
		{
			return new mxImage (state.style[mxConstants.STYLE_IMAGE], 16, 16);
		};*/
		
		// Centers the port icon on the target port
		graph.connectionHandler.targetConnectImage = true;
		
		// Does not allow dangling edges
		//连线是否必须连接至节点
		graph.setAllowDanglingEdges (false);
		
		// Sets the graph container and configures the editor
		editor.setGraphContainer (container);
		//键盘热键控制
		var config = mxUtils.load ('mxgraph-master/examples/editors/config/keyhandler-commons.xml')
		        .getDocumentElement ();
		editor.configure (config);
		
		// Defines the default group to be used for grouping. The
		// default group is a field in the mxEditor instance that
		// is supposed to be a cell which is cloned for new cells.
		// The groupBorderSize is used to define the spacing between
		// the children of a group and the group bounds.
		//不允许重复连线
		graph.setMultigraph(false);
		//不允许自己连自己
		graph.setAllowLoops(false);
		
		var group = new mxCell ('Group', new mxGeometry (), 'group');
		group.setVertex (true);
		group.setConnectable (false);
		editor.defaultGroup = group;
		editor.groupBorderSize = 20;
		
		// Disables drag-and-drop into non-swimlanes.
		graph.isValidDropTarget = function (cell, cells, evt)
		{
			return this.isSwimlane (cell);
		};
		
		// Disables drilling into non-swimlanes.
		graph.isValidRoot = function (cell)
		{
			return this.isValidDropTarget (cell);
		}

		// Does not allow selection of locked cells
		graph.isCellSelectable = function (cell)
		{
			return !this.isCellLocked (cell);
		};
		
		// Returns a shorter label if the cell is collapsed and no
		// label for expanded groups
		graph.getLabel = function(cell)
		{
			var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
			var geometry = this.model.getGeometry(cell);
			
			if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null ||
				(geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) &&
				geometry.width >= 2)
			{
				var style = this.getCellStyle(cell);
				var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
				var max = geometry.width / (fontSize * 1.5);
				
				if (max < label.length)
				{
					return label.substring(0, max) + '...';
				}
			}
			
			return label;
		};
		
		// Enables wrapping for vertex labels
		graph.isWrapping = function(cell)
		{
			return this.model.isCollapsed(cell);
		};

		// Disables HTML labels for swimlanes to avoid conflict
		// for the event processing on the child cells. HTML
		// labels consume events before underlying cells get the
		// chance to process those events.
		//
		// NOTE: Use of HTML labels is only recommended if the specific
		// features of such labels are required, such as special label
		// styles or interactive form fields. Otherwise non-HTML labels
		// should be used by not overidding the following function.
		// See also: configureStylesheet.
		graph.isHtmlLabel = function (cell)
		{
			return !this.isSwimlane (cell);
		}

		// To disable the folding icon, use the following code:
		/*graph.isCellFoldable = function(cell)
		{
			return false;
		}*/

		// Shows a "modal" window when double clicking a vertex.
		graph.dblClick = function (evt, cell)
		{
			
			// Do not fire a DOUBLE_CLICK event here as mxEditor will
			// consume the event and start the in-place editor.
			if (this.isEnabled () && !mxEvent.isConsumed (evt) && cell != null && this.isCellEditable (cell))
			{
				if (this.model.isEdge (cell)) //|| !this.isHtmlLabel (cell))
				{
				}
				else
				{
					cellObj = cell;
					if(cell.style!='beginStyle'&&cell.style!='endStyle')
						{
						if(cell.style=='scriptServiceStyle')
						{
							if(parent.actionType=='exec') {
								openExecScriptWindw ();
							} else {
								openEditScriptWindw (false);
							}
						}
						else if(cell.style=='usertaskStyle')
							{
							openUTWindw ();
							}
						 else if(cell.style=='callflowStyle') {
							 if(actionType=='exec') {
								 graphViewStack = new Array();
									stackFlowView = Ext.create('widget.window', {
							            title: '详细信息',
							            closable: true,
							            closeAction: 'destroy',
							            width: contentPanel.getWidth(),
							            minWidth: 350,
							            height: contentPanel.getHeight(),
							            draggable: true,
							            resizable: false,
							            modal: true,
							            loader: {
							            	url : 'flowWinViewer.do', 
							                params: {
							                    iid: cell.scriptId,
							                    flag: flag,
							                    actionType:'exec',
							                    pageFrom: 'GFSS'+iid,
												isShowInWindow: 1,
												parentMxIid: cell.mxIid,
												isStack: true
							                },
							                autoLoad: true,
							                scripts: true
							            }
							        }).show();
							 } else {
								 openEditScriptWindw (true);
							 }
			    				return ;
							} else
							{
							openWindw ();
							}
						
						}
				}
			}
			
			mxEvent.consume (evt);
		};
		
		// Enables new connections
		//节点之间可以连接
		graph.setConnectable (true);
		
		// Adds all required styles to the graph (see below)
		//增加样式
		configureStylesheet (graph);
		
		// Adds sidebar icons.
		//
		// NOTE: For non-HTML labels a simple string as the third argument
		// and the alternative style as shown in configureStylesheet should
		// be used. For example, the first call to addSidebar icon would
		// be as follows:
		// addSidebarIcon(graph, sidebar, 'Website', 'images/icons48/earth.png');
		actNameCountMap['scriptServiceStyle'] = 0;
		actNameCountMap['usertaskStyle'] = 0;
		actNameCountMap['callflowStyle'] = 0;
		if(parent.actionType!='exec'&&parent.actionType!='audi'&&parent.actionType!='view'&&parent.actionType!='dbcheck'&&parent.actionType!='dbback'&&parent.actionType!='dbcheckForExec'&&parent.actionType!='dbbackForExec') {
			addSidebarIcon (graph, sidebar, '基础脚本', 'images/mxgraphImages/basescript.png', 'scriptServiceStyle',0);
			addSidebarIcon (graph, sidebar, '人工提醒', 'images/mxgraphImages/ut.png', 'usertaskStyle',1);
			addSidebarIcon (graph, sidebar, '作业调用', 'images/mxgraphImages/callflow.png', 'callflowStyle',3);
		}
		
		var spacer = document.createElement ('div');
		spacer.style.display = 'inline';
		spacer.style.padding = '8px';
		
		// Defines a new export action
		editor.addAction ('export', function (editor, cell)
		{
			var textarea = document.createElement ('textarea');
			textarea.style.width = '400px';
			textarea.style.height = '400px';
			var enc = new mxCodec (mxUtils.createXmlDocument ());
			var node = enc.encode (editor.graph.getModel ());
			textarea.value = mxUtils.getPrettyXml (node);
			showModalWindow (graph, 'XML', textarea, 410, 440);
		});
		
//		addToolbarButton (editor, toolbar, 'export', '结构', 'mxgraph-master/examples/images/export1.png');
		editor.addAction ('deleteBefore', function (editor, cell)
				{
    			var cells=editor.graph.getSelectionCells();
    			for(i=0;i<cells.length;i++)
				{
    				if(cells[i].style=='beginStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<开始>节点！');
    				return false;
    				}
    				if(cells[i].style=='endStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<结束>节点！');
    				return false;
    				}
				}
					editor.execute ('delete');
				});
//		toolbar.appendChild (spacer.cloneNode (true));
//		addToolbarButton (editor, toolbar, 'save', '保存', 'mxgraph-master/examples/images/export1.png');
		
		// ---
		
		// Adds toolbar buttons into the status bar at the bottom
		// of the window.
		//				addToolbarButton(editor, status, 'collapseAll', 'Collapse All', 'mxgraph-master/examples/images/navigate_minus.png', true);
		//				addToolbarButton(editor, status, 'expandAll', 'Expand All', 'mxgraph-master/examples/images/navigate_plus.png', true);
		
//		status.appendChild (spacer.cloneNode (true));
		
		//				addToolbarButton(editor, status, 'enterGroup', 'Enter', 'mxgraph-master/examples/images/view_next.png', true);
		//				addToolbarButton(editor, status, 'exitGroup', 'Exit', 'mxgraph-master/examples/images/view_previous.png', true);
		
//		status.appendChild (spacer.cloneNode (true));
		//放大缩小功能
		
		addToolbarButton (editor, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
		addToolbarButton (editor, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
		addToolbarButton (editor, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
		addToolbarButton (editor, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);
		
		// Creates the outline (navigator, overview) for moving
		// around the graph in the top, right corner of the window.
		var outln = new mxOutline (graph, outline);
		
		// To show the images in the outline, uncomment the following code
		//outln.outline.labelsVisible = true;
		//outln.outline.setHtmlLabels(true);
		
		// Fades-out the splash screen after the UI has been loaded.
		var splash = document.getElementById ('splash');
		if (splash != null)
		{
			try
			{
				mxEvent.release (splash);
				mxEffects.fadeOut (splash, 100, true);
			}
			catch (e)
			{
				
				// mxUtils is not available (library not loaded)
				splash.parentNode.removeChild (splash);
			}
		}
		
		graph.popupMenuHandler.factoryMethod = function(menu, cell, evt)
		{
			return createPopupMenu(graph, menu, cell, evt);
		};
		
		initFun(graph);
	}
	
};

/**增加右键删除菜单**/
function createPopupMenu(graph, menu, cell, evt)
{
	if (cell != null)
	{
		menu.addItem('删除', 'images/delete.png', function()
		{
			editor.execute ('deleteBefore');
		});
	}
};


/**
 * 初始化方法
 */
function initFun(graph)
{
	if(parent.iid>0)
	{
		//修改将原图加载
		loadGraph(parent.iid);
		
		var root2FlowWindow = model.getRoot ();
		var count = model.getChildCount (root2FlowWindow);
		for (var i = 0; i < count; i++)
		{
			var cells = root2FlowWindow.getChildAt (i);
			var counts = cells.getChildCount ();
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				if (!model.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
				{
					cellss.mxIid = parent.iid+":"+cellss.id;
					if(!actStartInfo['GFSS'+iid].hasOwnProperty(cellss.mxIid)) {
						if(parseInt(cellss.phaseId)==0) {
							actStartInfo['GFSS'+iid][cellss.mxIid] = {
									'actNo': cellss.id,
									'actType': parseInt(cellss.phaseId),
									'actName': cellss.value,
									'isShutdown': cellss.isShutdown
							};
						} else if(parseInt(cellss.phaseId)==1) {
							actStartInfo['GFSS'+iid][cellss.mxIid] = {
									'actNo': cellss.id,
									'actType': parseInt(cellss.phaseId),
									'actName': cellss.value,
									'message': cellss.ireminfo
							};
						}
					}
				}
			}
		}
	} else if(parent.iid==0 && oldJobId>0){
		loadGraph(oldJobId);
	}else
		{
		//新建增加开始节点
		addBeginEndCell (graph, "开始","beginStyle",1,1);
		//新建增加结束节点
		addBeginEndCell (graph, "结束","endStyle",150,150);
		}
	
};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButton (editor, toolbar, action, label, image, isTransparent)
{
	if (image != null)
	{
		var img = document.createElement ('img');
		img.setAttribute ('src', image);
		img.style.width = '74px';
		img.style.height = '30px';
		img.style.verticalAlign = 'middle';
		img.title = label;
		img.style.marginRight = '10px';
		img.style.marginTop = '2px';
	}
	mxEvent.addListener (img, 'click', function (evt)
	{
		if('delete'==action)
			{
			var cells=editor.graph.getSelectionCells();
			for(i=0;i<cells.length;i++)
				{
				if('开始'==cells[i].value)
					{
					Ext.Msg.alert ('提示', '不删除<开始>节点！');
					return false;
					}
				}
			}
		
		editor.execute (action);
	});
	//			mxUtils.write(img, label);
	toolbar.appendChild (img);
};
/**
 * 显示xml结构
 */
function showModalWindow (graph, title, content, width, height)
{
	var background = document.createElement ('div');
	background.style.position = 'absolute';
	background.style.left = '0px';
	background.style.top = '0px';
	background.style.right = '0px';
	background.style.bottom = '0px';
	background.style.background = 'black';
	mxUtils.setOpacity (background, 50);
	document.body.appendChild (background);
	
	if (mxClient.IS_IE)
	{
		new mxDivResizer (background);
	}
	
	var x = Math.max (0, document.body.scrollWidth / 2 - width / 2);
	var y = Math.max (10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
	var wnd = new mxWindow (title, content, x, y, width, height, false, true);
	wnd.setClosable (true);
	
	// Fades the background out after after the window has been closed
	wnd.addListener (mxEvent.DESTROY, function (evt)
	{
		graph.setEnabled (true);
		mxEffects.fadeOut (background, 50, true, 10, 30, true);
	});
	
	graph.setEnabled (false);
	graph.tooltipHandler.hide ();
	wnd.setVisible (true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCell (graph, label, styleName,w,h)
{
	
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			v1 = graph.insertVertex (parent, null, label, w, h, 50, 25, styleName);
			v1.setConnectable (true);
		}
		finally
		{
			model.endUpdate ();
		}
//		graph.setSelectionCell (v1);

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIcon (graph, sidebar, label, image, styleName,phaseId)
{
	// Function that is executed when the image is dropped on
	// the graph. The cell argument points to the cell under
	// the mousepointer if there is one.
	var funct = function (graph, evt, cell, x, y)
	{
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			actNameCountMap[styleName]= ++actNameCountMap[styleName];
			v1 = graph.insertVertex (parent, null, label+actNameCountMap[styleName], x, y, 90, 32, styleName);
			v1.setConnectable (true);
			v1.phaseId=phaseId;
			
		}
		finally
		{
			model.endUpdate ();
		}
		graph.setSelectionCell (v1);
	}

	// Creates the image which is used as the sidebar icon (drag source)
	var img = document.createElement ('img');
	img.setAttribute ('src', image);
	img.style.width = '80px';
	img.style.height = '32px';
	img.title = label;
	//			img.style.marginRight = '4px';
	img.style.marginLeft = '10px';
//	img.style.marginTop = '15px';
	img.style.marginBottom = '15px';
	
	sidebar.appendChild (img);
	
	var dragElt = document.createElement ('div');
	dragElt.style.border = 'dashed black 1px';
	dragElt.style.width = '120px';
	dragElt.style.height = '120px';
	
	// Creates the image which is used as the drag icon (preview)
	var ds = mxUtils.makeDraggable (img, graph, funct, dragElt, 0, 0, true, true);
	ds.setGuidesEnabled (true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheet (graph)
{
	
	var style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
	style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
	style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTSTYLE] = 0;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
	style[mxConstants.STYLE_RESIZABLE] = '0';//不可缩放
	graph.getStylesheet ().putDefaultVertexStyle (style);
	
	/*style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] ='images/mxgraphImages/begin.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet().putCellStyle('beginStyle', style);*/
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('beginStyle', style);
	
	/*style = new Object();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] ='images/mxgraphImages/end.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet().putCellStyle('endStyle', style);*/
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('endStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet ().putCellStyle ('cmdStyle', style);
	

	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
	style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
	style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_STARTSIZE] = '30';
	style[mxConstants.STYLE_FONTSIZE] = '16';
	style[mxConstants.STYLE_FONTSTYLE] = 1;
	graph.getStylesheet ().putCellStyle ('group', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_FONTCOLOR] = '#774400';
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_FONTSIZE] = '10';
	style[mxConstants.STYLE_FONTSTYLE] = 2;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
	graph.getStylesheet ().putCellStyle ('port', style);
	
	style = graph.getStylesheet ().getDefaultEdgeStyle ();
	style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_STROKEWIDTH] = '1';
	style[mxConstants.STYLE_STROKECOLOR] = '#595758';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_EDGE] =mxConstants.EDGESTYLE_ELBOW;
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('scriptServiceStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('usertaskStyle', style);

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};
/**
 * 回显xml文件信息至图形化界面
 */
function loadGraph(loadId) {
	graph.getModel().beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag="+flag+"&instanceID="+loadId));
		var dec = new mxCodec(doc);
		dec.decode(doc.getDocumentElement(), graph.getModel());
	} finally {
		// Updates the display
		graph.getModel().endUpdate();
	}
//	graph.graphToCenter();
}
/**
 * 显示模板详细配置窗口
 */
function openWindw ()
{
	configwindowFlow = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/mxgraph/flowCustomizedWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindw ()
{
	configwindowFlow = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : false,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/getFlowCustomizedExecScriptWindow.jsp",
	        params :
	        {
	            flag : flag
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function openEditScriptWindw (type)
{
	var h = contentPanel.getHeight ();
	var w = contentPanel.getWidth ();
	if(parent.actionType=='view'||parent.actionType=='exec'||parent.actionType=='audi'||parent.actionType=='dbcheck'||parent.actionType=='dbback'||parent.actionType=='dbcheckForExec'||parent.actionType=='dbbackForExec') {
		h = 130;
		w = 600;
	}
	configwindowFlow = Ext.create ('Ext.window.Window', {
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : true,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : w,
	    height : h,
	    loader : {
	        url : "page/dubbo/scriptService/flowDsg/getFlowCustomizedEditScriptWindow.jsp",
	        params :
	        {
	            actType : type
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

/**提醒任务信息编辑页面**/
function openUTWindw ()
{
	configwindowFlow = Ext.create ('Ext.window.Window',
	{
	    title : '人工提醒配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : 580,
	    height : 215,
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/scriptServiceUTWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 详细信息保存回填
 */
function callbackWindw ()
{
//	cellObj.scriptId = scriptIdin;
//	cellObj.value = stepNamein;
	//更新名后续刷新才能显示
	graph.view.refresh()
//	cellObj = null;

}
/**
 * 获取阶段信息
 */
function getPhase ()
{
	main (document.getElementById ('graphContainer'), document.getElementById ('outlineContainer'), document
            .getElementById ('toolbarContainer'), document.getElementById ('sidebarContainer'), document
            .getElementById ('statusContainer'));
}
/**
 * 判断新表中是否存在作业名
 */
function checkInstanceNameExisitInNewVersion()
{
	var bussId = parent.bussCbOutSide.getValue ();
	var bussTypeId = parent.bussTypeCbOutSide.getValue ();
	var instanceName = parent.instanceNameObjOutSide.getValue ();
	if(parent.iid>0) {
		Ext.Ajax.request ( {
		    url : 'checkCanUpdateFlowInfo.do',
		    params : {
		    	bussId: bussId, 
		    	bussTypeId: bussTypeId, 
		    	serviceName : instanceName,
		    	serviceId: parent.iid
		    },
		    method : 'POST',
		    async : false,
		    success : function (response, options)
		    {
		    	if(!Ext.decode (response.responseText).success)
	    		{
		    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
	    		}
		    	else
	    		{
		    		saveFlow();
	    		}
			    
		    },
		    failure : function (result, request)
		    {
		    }
		});
	} else {
		Ext.Ajax.request ( {
		    url : 'checkInstanceNameExisitInNewVersionScriptService.do',
		    params : {
		    	bussId: bussId, 
		    	bussTypeId: bussTypeId, 
		    	serviceName : instanceName
		    },
		    method : 'POST',
		    async : false,
		    success : function (response, options)
		    {
		    	if(!Ext.decode (response.responseText).success)
	    		{
		    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
	    		}
		    	else
	    		{
		    		saveFlow();
	    		}
			    
		    },
		    failure : function (result, request)
		    {
		    }
		});
	}
	

}
/**
 * 判断旧表中是否存在模板名
 */
function checkInstanceNameExisitInOldVersion()
{
	Ext.Ajax.request (
			{
			    url : 'checkInstanceNameExisitInOldVersionScriptService.do',
			    params :
			    {
			    	instanceName : parent.instanceNameObjOutSide.getValue ()
			    },
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
			    	// warnPath = Ext.decode(response.responseText).warnPath;
			    	if(!Ext.decode (response.responseText).success)
			    		{
			    		Ext.Msg.alert ('提示', "实例名已存在,请更换实例名！");
			    		}
			    	else
			    		{
			    		saveFlow();
			    		}
			    },
			    failure : function (result, request)
			    {
			    }
			});

}
/**
 * 保存流程
 */
function saveFlow() {
	var enc = new mxCodec (mxUtils.createXmlDocument ());
	var node = enc.encode (editor.graph.getModel ());
	var bussId = parent.bussCbOutSide.getValue ();
	var bussTypeId = parent.bussTypeCbOutSide.getValue ();
	var bussName = parent.bussCbOutSide.getRawValue ();
	var bussTypeName = parent.bussTypeCbOutSide.getRawValue ();
	var serviceName = parent.instanceNameObjOutSide.getValue();
	var serviceId = parent.iid;
	Ext.Ajax.request ({
	    url : 'mxgraphSaveForFlowScriptService.do',
	    params : {
	        xmlString : mxUtils.getPrettyXml (node),
	        bussId:bussId,
	        bussTypeId:bussTypeId,
	        bussName:bussName,
	        bussTypeName:bussTypeName,
	        serviceName : parent.instanceNameObjOutSide.getValue(),
	        funcDesc: parent.funcDescOutSide.getValue(),
	        serviceId:parent.iid
	    },
	    method : 'POST',
	    async : false,
	    success : function (response, options) {
		    if(Ext.decode (response.responseText).success==true) {
		    	parent.iid=Ext.decode (response.responseText).serviceId;
			    parent.serviceName=parent.instanceNameObjOutSide.getValue();
			    /*parent.instanceNameObjOutSide.setReadOnly(true);
			    parent.bussCbOutSide.setReadOnly(true);
			    parent.bussTypeCbOutSide.setReadOnly(true);*/
		    } else {
		    	Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
		    }
	    },
	    failure : function (result, request) {
	    	Ext.Msg.alert ('提示', "保存作业失败！");
	    }
	});
}

function startFlow() {
	var serviceId = parent.iid;
	var allStartParams = actStartInfo['GFSS'+iid];
	var isJobConfigOk = true;
	Ext.Ajax.request({
		url :'checkJobConfigIsOK.do',
		method: 'POST',
		async: false,
		params:{
			serviceId:serviceId,
			data:JSON.stringify(allStartParams),
			flag:0
		},
		success: function ( response, options) 
		{
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (!success) {
				isJobConfigOk = false;
				Ext.Msg.alert('提示', message);
			}
		},
		failure: function ( result, request){
			isJobConfigOk = false;
			Ext.Msg.alert('提示', "检查作业配置出现问题！");
		}
	});
	
	if(!isJobConfigOk) {
		return;
	}

	var isSaveTemplate = isSaveTemplateCk.getValue();
	if(isSaveTemplate) {
		Ext.MessageBox.prompt('提示', '请输入流程模板名称:', function(btn, text, cfg){
	        if(btn=='ok') {
	        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
	        		var newMsg = '<span style="color:red">流程模板名称不能为空！</span>';
	                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	        	} else {
	        		var customName = Ext.util.Format.trim(text);
	        		Ext.Ajax.request({
	        	        url: 'checkCustomTemplateNameIsExist.do',
	        	        params: {
	        	            customName: customName,
	        	            flag: flag
	        	        },
	        	        method: 'POST',
	        	        success: function(response, options) {
	        	            if (!Ext.decode(response.responseText).success) {
	        	                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
	        	            } else {
	        	            	Ext.Ajax.request({
	        						url :'startScriptServiceFlow.do',
	        						method: 'POST',
	        						params:{
	        							serviceId:serviceId,
	        							data:JSON.stringify(allStartParams),
	        							flag:parent.flag
	        						},
	        						success: function ( response, options) {
	        							var success = Ext.decode(response.responseText).success;
	        							var message = Ext.decode(response.responseText).message;
	        							if (success) {
	        								Ext.Msg.alert ('提示', message);
	        								Ext.Ajax.request({
	        	        	                    url: 'saveFlowCustomTemplate.do',
	        	        	                    method: 'POST',
	        	        	                    params: {
	        	        	                    	customName: customName,
	        	        	                    	serviceId: serviceId,
	        	        	                        data: JSON.stringify(allStartParams),
	        	        	                        flag: parent.flag
	        	        	                    },
	        	        	                    success: function(response, options) {
	        	        	                        var success1 = Ext.decode(response.responseText).success;
	        	        	                        var message1 = Ext.decode(response.responseText).message;
	        	        	                        if (success1) {
	        	        	                        	Ext.Msg.alert ('提示', message +'<br>模板保存成功！');
	        	        	                        }
	        	        	                    },
	        	        	                    failure: function(result, request) {
	        	        	                    	Ext.Msg.alert ('提示', message + "<br>模板保存失败");
	        	        	                    }
	        	        	                });
	        							}
	        						},
	        						failure: function ( result, request){
	        							Ext.Msg.alert ('提示', "作业启动失败！");
	        						}
	        					});
	        	            }
	        	        },
	        	        failure: function(result, request) {
	        	        	Ext.Msg.alert ('提示', "检查模板名称是否存在出现错误！");
	        	        }
	        	    });
	        	}
	        }
	    });
	} else {
		Ext.Msg.confirm("确认启动", "确定启动该作业？", function(id){
			if(id=='yes'){
				Ext.Ajax.request({
					url :'startScriptServiceFlow.do',
					method: 'POST',
					params:{
						serviceId:serviceId,
						data:JSON.stringify(allStartParams),
						flag:parent.flag
					},
					success: function ( response, options) 
					{
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if (success) {
							Ext.Msg.alert ('提示', message);
						}
					},
					failure: function ( result, request){
						Ext.Msg.alert ('提示', "作业启动失败！");
					}
				});
			}
		});
	}
}
