/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/
Ext.onReady(function() {
	
	var jspParms = tempData;
	delete tempData;
    var resGroupFlag;
    var chosedGroupIds =[];
    var chosedGroupNames =[];
    var chosedGroupWin;
	/** 获取内外对接的参数 **/
	var editor = parent.GRAPHS[jspParms.namespace];
	var model = editor.editorUi.editor.graph.getModel();
	var cell = editor.currentCell;
	var iid = editor.iid;
	
	var isDbcheckForExec = jspParms.actionType == 'dbcheckForExec';
	
    var scriptNameObj;
    var scriptContentObj;
    var stepNameObj;
    var paramObj;
    var startUserObj;
    var resourceGroupObj;
    var chosedServList;
    var agent_store;
    var cellScriptType = '';
    var chosedResGroups = new Array();
    
    var editingChosedAgentIds = new Array();

    var globalParams = {};
    var globalConfigParams = {};

    var globalConfigStartUser = {};

    var chosedAgentWinForSee;
    var agentParamsWin;
    var chosedAgentWin;
    var upldWin;
    var upLoadformPane = '';
    var scriptServiceId = cell.scriptId;
    var scriptUuid =  cell.scriptId;
    if(jspParms.isScriptConvertToFlow=='true'){
    	scriptUuid = jspParms.scriptUuid;
    }
    if(jspParms.isScriptConvertToFlow=='true')
	{
		scriptServiceId = jspParms.serviceId;
	}
	function StringToPassword(strs){
		if(strs&&strs!=null&strs!=''){
			var password = '';
			for(var i=0;i<strs.length;i++){
				password = password + '●';
			}
			return password;
		}else{
			return '';
		}
	}
    var finalChosedAgentsAndDbSources = {};
	var editingChosedAgentsAndDbSources = {};
    stepNameObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '步骤名称',
        readOnly: true
    });
    var shutdownCheckboxObj = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '关机维护',
        inputValue: 1,
        width: 80,
        margin: '3 0 0 30'
    });
    
    var eachNum = new Ext.form.NumberField({
		name: 'eachNum',
		fieldLabel: '并发数量',
		labelAlign : 'right',
		minValue:0,
		value:100,
		width:260,
		readOnly : isDbcheckForExec,
		hidden: !(jspParms.isScriptConvertToFlow=='true')
	});
    
    paramObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '执行参数',
        hidden: true,
        readOnly: true
    });
    startUserObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '启动用户',
        value:jspParms.suUser,
        readOnly : isDbcheckForExec
    });
    scriptNameObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '服务名称',
        readOnly: true
    });

    scriptContentObj = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '脚本内容',
        height: 300,
        readOnly: true,
        autoScroll: true
    });

    var prevButton = Ext.create('Ext.Button', {
        text: '上一步',
        handler: function() {
            var res = getCellFun("before");
            if (res) {
                cell = res;
                initFun();
                resourceGroupStore.load();
            } else {
                Ext.Msg.alert('提示', "当前步骤为第一个步骤");
            }
        }
    });

    var nextButton = Ext.create('Ext.Button', {
        text: '下一步',
        handler: function() {

            if (chosedAgentIds.length < 1&& chosedGroupIds.length< 1) {
                Ext.MessageBox.alert("提示", "请选择服务器或资源组!");
                return null;
            }

            var isOk = false;
            var agentStateMsg = "";
            if (chosedAgentIds.length >0) {
                // 检查agent状态
                Ext.Ajax.request({
                    url: 'checkAgentState.do',
                    method: 'POST',
                    async: false,
                    params: {
                        agentIds: chosedAgentIds
                    },
                    success: function (response, request) {
                        isOk = Ext.decode(response.responseText).isOk;
                        agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
                    },
                    failure: function (result, request) {
                        agentStateMsg = "检查Agent状态出错！";
                    }
                });
            }
            if (isOk) {
                saveFun();
            } else {
                Ext.Msg.confirm("请确认", agentStateMsg + "<br>选择的代理状态为异常，是否仍然保存？",
                function(id) {
                    if (id == 'yes') {
                        saveFun();
                    }
                });
            }
        
            var res = getCellFun("after");
            if (res) {
                cell = res;
                initFun();
                resourceGroupStore.load();
            } else {
                Ext.Msg.alert('提示', "当前步骤为最后一个步骤");
            }
        }
    });

    var viewChosedScriptButton = Ext.create('Ext.Button', {
        text: '查看脚本详情',
        handler: function() {
            if (scriptServiceId > 0) {
                Ext.create('widget.window', {
                    title: '详细信息',
                    closable: true,
                    closeAction: 'destroy',
                    width: contentPanel.getWidth(),
                    minWidth: 350,
                    height: contentPanel.getHeight(),
                    draggable: true,
                    resizable: false,
                    modal: true,
                    loader: {
                        url: 'queryOneServiceForView.do',
                        params: {
                            iid: scriptServiceId,
                            flag: 0,
                            hideReturnBtn: 1
                        },
                        autoLoad: true,
                        scripts: true
                    }
                }).show();
            } else {
                Ext.Msg.alert('提示', "没有脚本服务！");
            }
        }
    });

    var exportAgentButton = Ext.create('Ext.Button', {
        text: '导出已选服务器',
        cls: 'Common_Btn',
        hidden:!agentExportSwitch,
        handler: function(){
        	if(agent_grid_chosedForSee.getStore().getCount() < 1){
        		Ext.Msg.alert('提示', '该任务没有选择服务器，为资源组类型！');
        	}else{
				$.fileDownload('exportChooseAgent.do',{
					  httpMethod: 'POST',
					  traditional: true,
					  data:{ 
						  workItemId:iworkItemid_forexport,
						  dbaasSwitch:0,
						  scriptType:scriptType,
						  agentIds:chosedAgentIds.join(','),
						  isFlow:'2'
 					  },
					  successCallback: function(url){
					     console.log("脚本服务化双人复核——选择设备清单导出成功！");
					  },
					  failCallback: function (html, url) {
					   		 Ext.Msg.alert('提示', '导出失败！');
                       		  return;
					   }
				  });  
            	}
        }
    });
    
    var formPanel = Ext.create('Ext.form.Panel', {
        border: false,
        fieldDefaults: {
            labelAlign: 'right',
            labelWidth: 80,
            width: '23%'
        },
        layout: 'hbox',
        padding: '8 0 0 0',
        items: [stepNameObj, scriptNameObj, paramObj, startUserObj, shutdownCheckboxObj,eachNum,exportAgentButton]
    });

    /** 树数据Model* */
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    paramStore.on('beforeload',
    function(store, options) {
        var new_params = {
            scriptId: scriptUuid
        };
        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    paramStore.on('load',
    function(me, records, successful, eOpts) {
        $.each(records,
        function(index, record) {
            if (globalParams.hasOwnProperty(record.get('iid'))) {
                record.set('paramValue', globalParams[record.get('iid')]);
            }
        });
    });

    var configParamStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    configParamStore.on('beforeload',
    function(store, options) {
        var new_params = {
            scriptId: scriptUuid
        };
        Ext.apply(configParamStore.proxy.extraParams, new_params);
    });
	
   var defultEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true
		})
	});
	var passwordEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true,
			inputType : 'password'
		})
	});
    /** 树列表columns* */
    var paramColumns = [{
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 80,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '默认参数值',
        dataIndex: 'paramDefaultValue',
        width: 100,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            var backValue = "";
        	if(record.get('paramType')== 'IN-string(加密)'){
        		backValue = StringToPassword(value);
        	}else{
        		backValue = value;
        	}
        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
        	
        	return backValue;
        }
    },
     {
		xtype : 'gridcolumn',
		dataIndex : 'paramValue',
		width: 100,
		text : '参数值',
		maxLength : 1000,
		allowBlank: true,
		getEditor : function(record) {
			if (record.get('paramType') != 'IN-string(加密)' ) {
				return defultEditor;
			} else {
				return passwordEditor;
			}
		},
		renderer : function(value, metaData, record, rowIdx, colIdx, store){  
        	var backValue = "";
        	if(record.get('paramType')== 'IN-string(加密)'){
        		backValue = StringToPassword(value);
        	}else{
        		backValue = value;
        	}
        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
        	
        	return backValue;
        }
	},
    {
        text: '描述',
        dataIndex: 'paramDesc',
        width: 80,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing_for_config_params = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    if(isDbcheckForExec){
    	var paramGrid = Ext.create('Ext.grid.Panel', {
	        width: '45%',
	        cls:'window_border panel_space_top',
	        height: contentPanel.getHeight() - 300,
	        store: paramStore,
	        //margin: '0 5 0 5',
	        title: '参数信息',
	        border: true,
	        columnLines: true,
	        columns: paramColumns,
	        collapsible: false
	    });
    }else{
	    var paramGrid = Ext.create('Ext.grid.Panel', {
	        width: '45%',
	        cls:'window_border panel_space_top',
	        height: contentPanel.getHeight() - 300,
	        store: paramStore,
	        //margin: '0 5 0 5',
	        title: '参数信息',
	        border: true,
	        columnLines: true,
	        columns: paramColumns,
	        plugins: [cellEditing],
	        collapsible: false
	    });
    }
    var paramGrid_for_config_params = Ext.create('Ext.grid.GridPanel', {
        width: '45%',
        cls:'window_border panel_space_top panel_space_right panel_space_bottom  ',
        height: contentPanel.getHeight() - 250,
        store: configParamStore,
        //margin: '0 0 0 10',
        title: '参数信息',
        border: true,
        columnLines: true,
        columns: paramColumns,
        plugins: [cellEditing_for_config_params],
        collapsible: false
    });

    paramGrid_for_config_params.on('edit',
    function(editor, e) {
        var ipRecord = agent_grid_chosed_for_config_param.getSelectionModel().getSelection()[0];
        if (ipRecord) {
            var configParams = {};
            for (var i = 0; i < e.grid.getStore().getCount(); i++) {
                var record = e.grid.getStore().getAt(i);
                var iid = record.get('iid');
                var paramType = record.get('paramType');
                var paramValue = record.get('paramValue');

                if (paramType == 'int' && paramValue) {
                    if (!checkIsInteger(paramValue)) {
                        Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                        return;
                    }
                }
                if (paramType == 'float' && paramValue) {
                    if (!checkIsDouble(paramValue)) {
                        Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                        return;
                    }
                }
                if (paramValue.indexOf('"') >= 0) {
                    if (cellScriptType == 'bat') {
                        Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                        return;
                    }
                }
                if (!Ext.isEmpty(Ext.util.Format.trim(paramValue))) {
                    configParams[iid] = paramValue;
                }
            }
            globalConfigParams[ipRecord.get('iid')] = configParams; //绑定
        }
    });

    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    resourceGroupStore.on('load',
    function() {
        var ins_rec = Ext.create('resourceGroupModel', {
            id: '-1',
            name: '未分组',
            description: ''
        });
        resourceGroupStore.insert(0, ins_rec);
    });
    resourceGroupObj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '资源组',
        emptyText : '--请选择资源组--',
        multiSelect: true,
        hidden:removeAgentSwitch,
        labelWidth: 70,
        labelAlign: 'right',
        width : '25%',
        store: resourceGroupStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable: false,
        mode: 'local',
        listeners: {
            select: function(combo, records, eOpts) {
                if (records) {
                    chosedResGroups = new Array();
                    for (var i = 0; i < records.length; i++) {
                        chosedResGroups.push(records[i].data.id);
                    }
                    agent_store.load();
                } else {
                    agent_store.load();
                }

            }
        }
    });
    
    var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 79,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '24%',
		labelAlign : 'right',
		 listeners:{
	        specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		    }
	    }
	});
	
    //begin
//    var app_name = new Ext.form.TextField({
//        name: 'appname',
//        fieldLabel: '应用名称',
//        displayField: 'appname',
//        emptyText: '--请输入应用名称--',
//        labelWidth: 70,
//        labelAlign: 'right',
//        width: '25%'
//    });
	Ext.define('appNameModel', {
    	extend: 'Ext.data.Model',
    	fields : [ {
    		name : 'appName',
    		type : 'string'
    	}]
    });
	var appUrl='getAgentAppNameList.do?envType='+jspParms.flag;
    var app_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'appNameModel',
		proxy: {
			type: 'ajax',
			url: appUrl,
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var app_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
		name : 'appname',
	    fieldLabel: "应用名称",
	    emptyText : '--请选择应用名称--',
	    hidden: !CMDBflag,
	    store: app_name_store,
	    queryMode: 'local',
	    width: "25%",
	    displayField: 'appName',
	    valueField: 'appName',
	    labelWidth : 70,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'AgentIp',
        displayField: 'agentip',
        emptyText: '--请输入Agent IP--',
        labelWidth: 70,
        hidden :true,
        labelAlign: 'right',
        width : CMDBflag?'25.4%':'26.2%'
    });
    var ipStart = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 79,
	     fieldLabel : '起始IP',
	    emptyText : '--请输入开始IP--',
	    //labelSeparator : '',
	    width : '23%',
	    labelAlign : 'right',
	    listeners:{
		    specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			    }
	    }
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 58,
	    fieldLabel : '终止IP',
	    emptyText : '--请输入截止IP--',
	    //labelSeparator : '',
	    labelAlign : 'right',
	    width : '23%',
	    listeners:{
		    specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			    }
	    }
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '计算机名',
        displayField: 'hostname',
        emptyText: '--请输入计算机名--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
         listeners:{
	        specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		    }
	    }
    });
    Ext.define('sysNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'sysName',
            type: 'string'
        }]
    });
    var sysUrl='getAgentSysNameList.do?envType='+jspParms.flag;
    var sys_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'sysNameModel',
        proxy: {
            type: 'ajax',
            url: sysUrl,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var sys_name = Ext.create('Ext.form.ComboBox', {
        name: 'sysname',
        fieldLabel: "名称",
        emptyText : '--请选择名称--',
        store: sys_name_store,
        hidden: !CMDBflag,
        queryMode: 'local',
        width: "25%",
        displayField: 'sysName',
        valueField: 'sysName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '操作系统',
        displayField: 'ostype',
        emptyText: '--请输入操作系统--',
        labelWidth: 70,
        labelAlign: 'right',
        width : CMDBflag?'25.5%':'23.8%',
         listeners:{
		    specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	pageBar.moveFirst();
			                }
			    }
	    }
    });
    
    var sysName1 = new Ext.form.TextField({
        name: 'sysName1',
        fieldLabel: '名称',
        displayField: 'sysName1',
        emptyText: '--请输入名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width :'26%',
        listeners:{
	        specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	pageBar.moveFirst();
		                }
		    }
	    }
    });

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'appName',
            type: 'string'
        },
        {
            name: 'hostName',
            type: 'string'
        },
        {
            name: 'osType',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string',
            defaultValue: 1500
        },
        {
            name: 'agentDesc',
            type: 'string'
        },
        {
            name: 'resGroup',
            type: 'string'
        },
        {
            name: 'agentState',
            type: 'int'
        },
        {
            name: 'agentParam',
            type: 'string'
        },
        {
            name: 'agentStartUser',
            type: 'string'
        }]
    });
    var url = 'getAllAgentList.do';
    if(scriptWhiteAgentSwitch){
    	if(jspParms.scriptLevel==0){
    		url = 'getAllAgentListForWhite.do';
    	}
    }
    agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        groupField: 'resGroup',
        //确定哪一项分组  
        proxy: {
            type: 'ajax',
            url: url,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });

    agent_store.on('beforeload', function(store, options) {
        var new_params = {
            flag: jspParms.flag,
            hasPage: false,
//            rgIds: chosedResGroups,
            agentState: agentStatusCb.getValue(),
            agentIp: Ext.util.Format.trim(agent_ip.getValue()),
            startIp :ipStart.getValue().trim(),
	    	endIp :ipEnd.getValue().trim(),
            appName : app_name.getValue()==null?'':Ext.util.Format.trim(app_name.getValue()+""),
            sysName: CMDBflag?(sys_name.getValue() == null ? '': Ext.util.Format.trim(sys_name.getValue() + "")):Ext.util.Format.trim(sysName1.getValue()),
            hostName: Ext.util.Format.trim(host_name.getValue()),
            osType: Ext.util.Format.trim(os_type.getValue())
        };

        Ext.apply(agent_store.proxy.extraParams, new_params);
    });

    agent_store.addListener('load',
    function(me, records, successful, eOpts) {
        if (editingChosedAgentIds) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            chosedServList.getSelectionModel().select(chosedRecords, false, true); //选中记录
        }
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
	//浦发agent展示样式特殊开关
	if(!taskApplyForSPDBSwitch){//不开展示原有样式
	    agent_store_chosed = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        model: 'agentModel',
	        pageSize: 50,
	        proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	}else{//开了后展示白名单执行样式
		agent_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: false,
			pageSize: 50,
			model: 'page.dubbo.scriptService.spdb.agent.agentModel',
			proxy: {
	            type: 'ajax',
	            url: 'getAllAgentListForSPDB.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
	}
    agent_store_chosed.on('beforeload',
    function(store, options) {
       var new_params;	
       if(taskApplyForSPDBSwitch){
    		  new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds),
    			flag :1,
    			from : 'inAgentChosedList'
    		};
    	}else{
    		  new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds),
    			flag :1
    		};
    	}
        Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    agent_store_chosed.on('load',
    function() {
        agent_grid_chosed_for_config_param.getSelectionModel().select(0);
    });

    var pageBarForAgentChosedToSetParamsGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agent_store_chosed,
        dock: 'bottom',
        displayInfo: true
    });
    var pageBarForAgentChosedToSetParamsGrid1 = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: agent_store_chosed,
        dock: 'bottom',
        displayInfo: true
    });
	//浦发agent展示样式特殊开关
	if(!taskApplyForSPDBSwitch){//不开展示原有样式
	      agent_store_chosedForSee = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        pageSize: 50,
	        model: 'agentModel',
	        proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	}else{//开了后展示白名单执行样式
		 agent_store_chosedForSee = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        pageSize: 50,
	        model: 'page.dubbo.scriptService.spdb.agent.agentModel',
	        proxy: {
	            type: 'ajax',
	            url: 'getAllAgentListForSPDB.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	}
    agent_store_chosedForSee.on('beforeload',
    function(store, options) {
        var new_params;	
    	if(taskApplyForSPDBSwitch){
    		 new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds),
    			flag :1,
    			from : 'inAgentChosedList'
    		};
    	}else{
    		 new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds),
    			flag :1
    		};
    	}

        Ext.apply(agent_store_chosedForSee.proxy.extraParams, new_params);
    });

    agent_store_chosedForSee.addListener('load',
    function(me, records, successful, eOpts) {
        for (var prop in globalConfigStartUser) {
            if (agent_store_chosedForSee.findRecord('iid', prop)) {
                agent_store_chosedForSee.findRecord('iid', prop).set('agentStartUser', globalConfigStartUser[prop]);
            }
        }
    });
	var agent_columns =[{
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '名称',
            dataIndex: 'sysName',
            width: 80
        },
        {
            text: '应用名称',
            dataIndex: 'appName',
            hidden: !CMDBflag,
            width: 80
        },
        {
            text: 'IP',
            dataIndex: 'agentIp',
            width: 80
        },
        {
            text: '计算机名',
            dataIndex: 'hostName',
            width: 100,
            flex: 1
        },
        {
            text: '操作系统',
            dataIndex: 'osType',
            width: 100
        },
        {
            text: '端口号',
            dataIndex: 'agentPort',
            width: 100
        },
        {
            text: '描述',
            dataIndex: 'agentDesc',
            flex: 1,
            hidden: true
        },
        {
            text: '状态',
            dataIndex: 'agentState',
            width: 130,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }];
    var agent_grid_chosed_for_config_param = Ext.create('Ext.ux.ideal.grid.Panel', {
        title: '已选服务器列表',
        store: agent_store_chosed,
        border: true,
        columnLines: true,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        cls:'window_border panel_space_top panel_space_left panel_space_right panel_space_bottom  ',
        //bbar: pageBarForAgentChosedToSetParamsGrid,
        width: '54%',
        height: contentPanel.getHeight() - 150,
        autoScroll: true,
        emptyText: '没有选择服务器',
        columns: taskApplyForSPDBSwitch == true ? agentColumnsForSPDB:agent_columns,
        listeners: {
            select: function(self, record, index, eOpts) {
                var ipId = record.get('iid');
                if (globalConfigParams.hasOwnProperty(ipId)) {
                    var cp = globalConfigParams[ipId];
                    configParamStore.each(function(record) {
                        record.set('paramValue', cp[record.get('iid')])
                    });
                } else {
                    configParamStore.each(function(record) {
                        record.set('paramValue', '');
                    });
                }
            }
        }
    });

    var choseAgentWrapper_for_config_params = Ext.create('Ext.panel.Panel', {
        border: false,
        height: contentPanel.getHeight() - 100,
        layout: {
            type: 'hbox',
            padding: '5',
            align: 'stretch'
        },
        items: [agent_grid_chosed_for_config_param, paramGrid_for_config_params]
    });
    
    var parmsButton = Ext.create('Ext.Button', {
        text: '配置参数',
        cls: 'Common_Btn',
        handler: function() {
            if (!agentParamsWin) {
            	if (isDbcheckForExec)
        		{
//        			var selectRecord = agent_grid_chosedForSee.getStore().getRange();
//            		var selectRecord = chosedServList.getSelectionModel().getSelection();
            		agentParamsWin = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.agentParamsWin', {
        				jspParms : jspParms,
        				width: contentPanel.getWidth(),
                        height: contentPanel.getHeight(),
//                        selectRecord : selectRecord,
                        chosedAgentIds:chosedAgentIds,
                        globalConfigParams:globalConfigParams
        			});
        		}else{
        			agentParamsWin = Ext.create('Ext.window.Window', {
                         title: '配置参数',
                         autoScroll: true,
                         modal: true,
                         resizable: false,
                         closeAction: 'hide',
                         width: contentPanel.getWidth(),
                         height: contentPanel.getHeight(),
                         items: [choseAgentWrapper_for_config_params],
                         buttonAlign: 'center',
                         buttons: [{
                             xtype: "button",
                             text: "确定",
                             handler: function() {
                                 //
                                 this.up("window").close();
                             }
                         }]
                     });
        		}
               
            }
            agentParamsWin.show();
            //			agent_store_chosed.removeAll();
            pageBarForAgentChosedToSetParamsGrid.moveFirst();

        }
    });
    if (isDbcheckForExec)
	{
    	parmsButton.setText('查看参数');
	}
    var selectAll = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '全选',
        inputValue: 1,
        width: 50,
        hidden:taskApplyForSPDBSwitch,
        margin: '10 0 0 10'
    });
    
    var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {

    	//var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {
    		region : 'north',
    	  	layout : 'anchor',
    	  	/*buttonAlign : 'center',*/
    	  	cls:'window_border panel_space_top panel_space_right panel_space_left panel_space_bottom',
/*    	  	iselect : false,
    	  	bodyCls : 'x-docked-noborder-top',
    	  	baseCls:'customize_gray_back',*/
    	  	/**collapsible : true,//可收缩
    	    collapsed : true,*///默认收缩
/*    	  	iqueryFun : query,*/
    	    border: false,
    	    dockedItems : [
    		{
    			xtype : 'toolbar',
    			border : false,
    			dock : 'top',
    			/*baseCls:'customize_gray_back',*/
    			items : [
    			         sys_name, app_name, host_name,os_type,ipStart,ipEnd
    			]
    		},
    		{
    			xtype : 'toolbar',
    			border : false,
    			dock : 'top',
    			/*baseCls:'customize_gray_back',*/
    			items : [
    				
    				sysName1, resourceGroupObj, agentStatusCb, selectAll,{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    agent_ip.setValue('');
                    app_name.setValue('');
                    sys_name.setValue('');
					ipEnd.setValue('');
					ipStart.setValue('');
                    host_name.setValue('');
                    os_type.setValue('');
                    sysName1.setValue('');
                    resourceGroupObj.setValue('');
                    agentStatusCb.setValue('');
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '导入',
                handler: importExcel
            }
    				
    			]
    		}
    		]
    	});
    
    chosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        autoScroll: true,
        multiSelect: true,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        cls:'window_border  panel_space_right panel_space_left panel_space_bottom',
        split: true,
        columnLines: true,
        store: agent_store,
        
        selModel: selModel,
        listeners: {
            select: function(t, record, index, eOpts) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) == -1) {
                	editingChosedAgentIds.push(record.get('iid'));
                }
            },
            deselect: function(t, record, index, eOpts) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                	editingChosedAgentIds.remove(record.get('iid'));
                }
                /*if (globalConfigParams.hasOwnProperty(record.get('iid'))) {
                    delete globalConfigParams[record.get('iid')];
                    Ext.Msg.alert('提示', "该服务器个性化的参数已删除！");
                }*/
            }
        },
        columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '名称',
            dataIndex: 'sysName',
            flex: 1
        },
        {
            text: '应用名称',
            hidden: !CMDBflag,
            dataIndex: 'appName',
            flex: 1
        },
        {
            text: '计算机名',
            dataIndex: 'hostName',
            flex: 1
        },
        {
            text: 'IP',
            dataIndex: 'agentIp',
            width: 150
        },
        {
            text: '端口号',
            dataIndex: 'agentPort',
            width: 100
        },
        {
            text: '操作系统',
            dataIndex: 'osType',
            width: 140
        },
        {
            text: '资源组',
            dataIndex: 'resGroup',
            width: 100,
            hidden: true
        },
        {
            text: '描述',
            dataIndex: 'agentDesc',
            flex: 1,
            hidden: true
        },
        {
            text: '状态',
            dataIndex: 'agentState',
            width: 130,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }]
        //bbar: pageBar
    });
    Ext.define('dbModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',        type: 'string'},
            {name: 'driverClass',type: 'string'},
            {name: 'dbUrl',      type: 'string'},
            {name: 'dbUser',     type: 'string'},
            {name: 'dbType',     type: 'string'}
        ]
    });
   
    var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
                            { text: '主键',  dataIndex: 'iid',hidden:true},
                            { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }},
                            { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }},
                            { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
                            { text: 'DB类型',  dataIndex: 'dbType',width:110}];
       
    var dbinfo_store = Ext.create('Ext.data.Store', {
		autoLoad: false,
		pageSize: 50,
		model: 'dbModel',
		proxy: {
			type: 'ajax',
			url: 'getDbSqlDriverInfo.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
 // 定义复选框
    var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		mode : "SINGLE",
		listeners : {
			select:function(self, record, index, eOpts) {
					var ipRecord = agent_grid_chosed.getSelectionModel().getSelection()[0];
					var dsid = record.get("iid");
					editingChosedAgentsAndDbSources[ipRecord.get('iid')] = dsid;//绑定
			},
			deselect:function(self, record, index, eOpts) {
					var ipRecord = agent_grid_chosed.getSelectionModel().getSelection()[0];
					delete editingChosedAgentsAndDbSources[ipRecord.get('iid')];  
			}
		}
	});
    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
	    store:dbinfo_store,
	    width:'70%',
	    
	    region : 'center',
	    border:true,
	    columnLines : true,
	    columns:dbsource_columns,
	    selModel:selModel2
	});
    
    dbinfo_store.on('load', function(store, options) {
    	  $.each(chosedAgentIds,
			        function(index, closedAgentId) {
			            	if(finalChosedAgentsAndDbSources.hasOwnProperty(closedAgentId)) {
			            		dbinfo_store.each(function(record) {   
			     			       if(finalChosedAgentsAndDbSources[closedAgentId]==record.get('iid')) {
			     			    	  selModel2.select(record,false, true);
			     		    	   }
			     			    });
			            	
			            }else{
		    				db_soucre_grid.getSelectionModel().select(0);  

		    			}
			        });
    });
 
    var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
		  title: '已选服务器列表',
	    	store:agent_store_chosed,
	    	border:true,
	    	columnLines : true,
	    	width: '49%',
	    	bbar: pageBarForAgentChosedToSetParamsGrid1,
	    	height : contentPanel.getHeight () -150,
	    	autoScroll : true,
	    	emptyText: '没有选择服务器',
	    	columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
            { text: '主键',  dataIndex: 'iid',hidden:true},
            { text: 'IP',  dataIndex: 'agentIp',width:120},
            { text: '端口号',  dataIndex: 'agentPort',width:100},
	            { text: '描述',  dataIndex: 'agentDesc',flex:1},
	            { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
	            	var backValue = "";
	            	if(value==0){
	            		backValue = "Agent正常";
	            	}else if(value==1){
	            		backValue = "Agent异常";
	            	}
	            	return backValue;
	            }}
       ],
			listeners : {
   			select:function(self, record, index, eOpts) {
   				 dbinfo_store.load({
			            params: {
			            	agentId: record.get("iid"),
			                agentIp: record.get("agentIp"),
			                agentPort: record.get("agentPort")
			            },
			            callback: function(records, operation, success) {
			            	if(editingChosedAgentsAndDbSources.hasOwnProperty(record.get("iid"))) {
			            		$.each(records, function(index, val){
			            			if(val.get('iid')==editingChosedAgentsAndDbSources[record.get("iid")]) {
			            				selModel2.select(val,false, true);
			            			}
			            		});
	   			 			}
			            }
			        });
   			 }
   		}
	    });
	    
  var choseAgentWrapper = Ext.create('Ext.panel.Panel',{
      border : false,
      height : contentPanel.getHeight ()-100,
      layout: {
          type: 'hbox',
          padding:'5',
          align:'stretch'
      },
      items : [agent_grid_chosed, db_soucre_grid]
	});
  var choseDbSourceButton = Ext.create('Ext.Button', {
		text : '选择数据源',
		cls : 'Common_Btn',
		hidden: true,
		handler : function() {
			if(!chosedAgentWin) {
				chosedAgentWin = Ext.create('Ext.window.Window', {
			  		title : '配置数据源',
			  		autoScroll : true,
			  		modal : true,
			  		resizable : false,
			  		closeAction : 'hide',
			  		width : contentPanel.getWidth(),
			  		height : contentPanel.getHeight (),
			  		items:[choseAgentWrapper],
			  		buttonAlign: 'center',
			  		buttons: [{ 
			  			xtype: "button", 
			  			text: "确定", 
			  			handler: function () {
			  				this.up("window").close();
			  			}
			  		}]
			  	});
			}
			chosedAgentWin.show();
			pageBarForAgentChosedToSetParamsGrid1.moveFirst();
			//dbinfo_store.removeAll();
			//agent_store_chosed.removeAll();
			//agent_store_chosed.loadRecords(chosedServList.getSelectionModel().getSelection());
			//agent_grid_chosed.getSelectionModel().select(0);
		}
	});
	var agent_columnsForSee = [{ text: '序号', xtype:'rownumberer', width: 40 },{
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '名称',
            dataIndex: 'sysName',
            flex: 1
        },
        {
            text: '应用名称',
            hidden: !CMDBflag,
            dataIndex: 'appName',
            flex: 1
        },
        {
            text: '计算机名',
            dataIndex: 'hostName',
            flex: 1
        },
        {
            text: 'IP',
            dataIndex: 'agentIp',
            width: 120
        },
        {
            text: '端口号',
            dataIndex: 'agentPort',
            width: 60
        },
        {
            text: '操作系统',
            dataIndex: 'osType',
            width: 110
        },
        {
            dataIndex: 'agentStartUser',
            text: '启动用户',
            editor: {
                allowBlank: true
            },
            flex: true
        },
        {
            text: '描述',
            dataIndex: 'agentDesc',
            flex: 1,
            hidden: true,
            renderer: function(value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '状态',
            dataIndex: 'agentState',
            width: 80,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }];
	Ext.define('agentModel1', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
	var agent_storeAll = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'agentModel1',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentListAll.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var agent_grid_chosedForSee = Ext.create('Ext.ux.ideal.grid.Panel', {
        width: '50%',
        height: contentPanel.getHeight() - 300,
        emptyText: '没有选择服务器',
        cls:'window_border panel_space_top panel_space_left panel_space_right',
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        title : '已选服务器列表',
        store: agent_store_chosedForSee,
        border: true,
        columnLines: true,
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit: 2})],
        //浦发特殊需求  agent选择特殊展示样式 判断 带启动用户的
    	columns: taskApplyForSPDBSwitch == true ? agentColumnsHasStartUser:agent_columnsForSee,
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        listeners : {
            activate:function(tab){
                if(!isDbcheckForExec){
                    resGroupFlag='false';
                    agent_store_chosedForSee.load();
                    chosedGroupIds.splice(0,chosedGroupIds.length);
                    chosedGroupNames.splice(0,chosedGroupNames.length);
                }
            }
        },
        //bbar: pageBarForAgentChosedGrid,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '删除',
                hidden : isDbcheckForExec,
                handler: function() {
                    var records = agent_grid_chosedForSee.getSelectionModel().getSelection();
                    if (records.length > 0) {
                        for (var i = 0,
                        len = records.length; i < len; i++) {
                            chosedAgentIds.remove(records[i].get('iid'));
                        }
                        if(chosedAgentIds.length ==0 && chosedAgentFlagArray.indexOf('WIN') != -1){
	     				 	chosedAgentFlagArray.remove('WIN');
	     				 }else if(chosedAgentIds.length ==0 &&  chosedAgentFlagArray.indexOf('非WIN') != -1){
	     				 	chosedAgentFlagArray.remove('非WIN');
	     				 }
                        agent_grid_chosedForSee.ipage.moveFirst();
                        pageBar.moveFirst();
                    } else {
                        Ext.Msg.alert('提示', "请选择服务器！");
                        return;
                    }
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '增加服务器',
                hidden : isDbcheckForExec,
                handler: function() {
                   //浦发银行 选择agent同白名单执行一致的特殊需求，通过开关控制
					if(!chosedAgentWinForSee && !taskApplyForSPDBSwitch) {
                        chosedAgentWinForSee = Ext.create('Ext.window.Window', {
                            title: '增加服务器',
                            autoScroll: true,
                            modal: true,
                            resizable: false,
                            closeAction: 'hide',
                            width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
                            layout: 'border',
                            items: [formMainPanel,chosedServList],
                            buttonAlign: 'center',
                            dockedItems: [{
                                xtype: 'toolbar',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    text: "确定",
                                    cls: 'Common_Btn',
                                    margin: '6',
                                    handler: function() {
                                    	var isSelectAll = selectAll.getValue();
						  				if(isSelectAll){
						  					agent_storeAll.load({
						  						 params : {  
						  							agentIp : agent_ip.getValue(),
						  					    	startIp :ipStart.getValue().trim(),
						  					    	agentState: agentStatusCb.getValue(),
						  					    	endIp :ipEnd.getValue().trim(),
						  					    	appName : app_name.getValue(),
						  					    	sysName: CMDBflag?(sys_name.getValue() == null ? '': Ext.util.Format.trim(sys_name.getValue() + "")):Ext.util.Format.trim(sysName1.getValue()),
						  							hostName : host_name.getValue(),
						  							osType : os_type.getValue(),
						  					    	rgIds:resourceGroupObj.getValue()
						  						 },
						  						callback : function(r, options, success) {
						  							agent_storeAll.each(function(record) { 
								  						chosedAgentIds.push(record.get('iid'));
								  					}); 
								  					agent_store_chosedForSee.load();
								  					chosedAgentWinForSee.close();
						  						}
						  					});
						  				}else{
						  					chosedAgentIds = editingChosedAgentIds.slice(0);
						  					agent_store_chosedForSee.load();
						  					this.up("window").close();
						  				}
                                    }
                                },
                                {
                                    xtype: "button",
                                    text: "关闭",
                                    cls: 'Common_Btn',
                                    handler: function() {
                                        this.up("window").close();
                                    }
                                }]
                            }]
                        });
                    }else if(!chosedAgentWinForSee && taskApplyForSPDBSwitch){
                    	chosedAgentWinForSee = chosedAgentWinForSPDB;
                    }
                    editingChosedAgentIds = chosedAgentIds.slice(0);
                    chosedAgentWinForSee.show();
                    agent_store.load();
                }
            },
            parmsButton,choseDbSourceButton]
        }]
    });

    agent_grid_chosedForSee.on('edit',
    		function(editor, e) {
        		var record = e.record;
        		var iid = record.get('iid');
        		var agentStartUser = record.get('agentStartUser');

        		if (!Ext.isEmpty(Ext.util.Format.trim(agentStartUser))) {
        			globalConfigStartUser[iid] = agentStartUser
        		} else {
        			delete globalConfigStartUser[iid];
        		}
    	});
    agent_grid_chosedForSee.on('beforeedit',
    		function(editor, e) {
    	return !isDbcheckForExec; 
    });




    //*********************************************增加资源组相关控件 start***************************************************************************

    Ext.define('groupModel', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id',     type: 'long'},
            {name: 'name',     type: 'string'},
            {name: 'execUserName',     type: 'string'},
            {name: 'description',     type: 'string'}
        ]
    });
    var group_columns = [
        { text: '序号', xtype: 'rownumberer', width: 40 },
        { text: '主键', dataIndex: 'id', hidden: true },
        { text: '组名称', dataIndex: 'name', width: 160 },
        { text: '启动用户', dataIndex: 'execUserName', width: 160 },
        { text: '描述', dataIndex: 'description', width: 280 },
        {
            text : '操作',
            xtype : 'actiontextcolumn',
            flex:1,
            align : 'left',
            items : [{
                text : '详情',
                iconCls : 'execute',
                handler : function(grid, rowIndex) {
                    var iid = grid.getStore().data.items[rowIndex].data.id;
                    getAgentInfoByGroupId(iid);
                }}]
        }];
    /**
     * 获取资源组下的agent
     */
    function getAgentInfoByGroupId(iid){
        Ext.define('agentModelByGroup', {
            extend: 'Ext.data.Model',
            idProperty: 'id',
            fields: [
                {name: 'id',     type: 'long'},
                {name: 'ip',     type: 'string'},
                {name: 'port',     type: 'string'},
                {name: 'hostName',  type: 'string'}
            ]
        });

        var agentinfo_group_store = Ext.create('Ext.data.Store', {
            autoLoad: false,
            pageSize: 50,
            model: 'agentModelByGroup',
            proxy: {
                type: 'ajax',
                url: 'agentGroup/getServersForTaskApply.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        agentinfo_group_store.on('beforeload', function (store, options) {
            var new_params = {
                groupId:iid
            };

            Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
        });
        var  agentinfo_columns_group = [
            { text: '序号', xtype: 'rownumberer', width: 40 },
            { text: '主键', dataIndex: 'id', hidden: true },
            { text: 'Agent名称', dataIndex: 'hostName', flex:1},
            { text: 'IP', dataIndex: 'ip', width: 160 },
            { text: '端口', dataIndex: 'port', width: 160 }];


        var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
            region: 'center',
            store:agentinfo_group_store,
            border:false,
            columnLines : true,
            cls:'customize_panel_back',
            columns:agentinfo_columns_group,
            ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        });
        agentinfo_group_store.load();
        var  agentinfoGroupWin = Ext.create('Ext.window.Window', {
            title: '增加资源组',
            autoScroll: true,
            modal: true,
            resizable: false,
            closeAction: 'hide',
            layout: 'border',
            width: contentPanel.getWidth() - 190,
            height: contentPanel.getHeight(),
            items: [agentinfo_group_grid]
        });
        agentinfoGroupWin.show();
    }


    var group_columns_chosed = [
        { text: '序号', xtype: 'rownumberer', width: 40 },
        { text: '主键', dataIndex: 'id', hidden: true },
        { text: '组名称', dataIndex: 'name', width: 160 },
        { text: '启动用户', dataIndex: 'execUserName', width: 160 },
        { text: '描述', dataIndex: 'description', flex:1 }];
    var group_store_chosed = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 30,
        model: 'groupModel',
        proxy: {
            type: 'ajax',
            url: 'agentGroup/groups.do',
            actionMethods: {
                create : 'POST',
                read   : 'POST', // by default GET
                update : 'POST',
                destroy: 'POST'
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var group_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'groupModel',
        proxy: {
            type: 'ajax',
            url: 'agentGroup/groups.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    group_store_chosed.on('beforeload', function(store, options) {
        var   new_params = {
            groupIids: chosedGroupIds,
            agentGroupNames :chosedGroupNames,
            from :"doublePersonFlowExec"
        }
        Ext.apply(group_store_chosed.proxy.extraParams, new_params);
    });

    group_store.on('beforeload', function (store, options) {
        var new_params = {
            agentGroupName : iresName.getValue()
//		    	iexecUser:iexecUser.getValue()
        };
        Ext.apply(group_store.proxy.extraParams, new_params);
    });
    group_store.on('load', function(store, options) {
        var records = [];//存放选中记录
        for (var i = 0; i < group_store.getCount(); i++) {
            var record = group_store.getAt(i);
            for (var ii = 0; ii < chosedGroupIds.length; ii++) {
                if (+chosedGroupIds[ii] == record.data.id) {
                    records.push(record);
                }
            }
        }
        group_grid.getSelectionModel().select(records, false, true); //选中记录
    });


    var group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
        region: 'center',
        store:group_store,
        border:false,
        columnLines : true,
        cls:'customize_panel_back',
        columns:group_columns,
        ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
        listeners: {
            select: function( e, record, index, eOpts ){
                if(chosedGroupIds.indexOf(record.get('id'))==-1) {
                    chosedGroupIds.push(record.get('id'));
                    chosedGroupNames.push(record.get('name'));
                }
            },
            deselect: function( e, record, index, eOpts ){
                if(chosedGroupIds.indexOf(record.get('id'))>-1) {
                    chosedGroupIds.remove(record.get('id'));
                    chosedGroupNames.remove(record.get('name'));
                }
            }
        }
    });

    var iresName = new Ext.form.TextField({
        name : 'iresName',
        fieldLabel : '组名称',
        displayField : 'iresName',
        emptyText : '--请输入组名称--',
        labelWidth : 70,
        labelAlign : 'right',
        width : '25%'
    });
    var iexecUser = new Ext.form.TextField({
        name : 'iexecUser',
        fieldLabel : '启动用户',
        displayField : 'iexecUser',
        emptyText : '--启动用户--',
        hidden:true,
        labelWidth : 70,
        labelAlign : 'right',
        width : '25%'
    });

    var search_group_form = Ext.create('Ext.ux.ideal.form.Panel', {
        region : 'north',
        border : false,
        iqueryFun : function(){
            group_grid.ipage.moveFirst();
        },
        bodyCls : 'x-docked-noborder-top',
        dockedItems : [ {
            xtype : 'toolbar',
            dock : 'top',
            border: false,
            items : [ iresName, iexecUser,
                {
                    xtype : 'button',
                    cls : 'Common_Btn',
                    text : '查询',
                    handler : function(){
                        group_grid.ipage.moveFirst();
                    }
                },
                {
                    xtype : 'button',
                    cls : 'Common_Btn',
                    text : '清空',
                    handler : function(){
                        iresName.setValue('');
                        iexecUser.setValue('');
                    }
                }]
        }]
    });


    var group_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
        title: '已选资源组',
        region: 'west',
        cls: 'window_border panel_space_top panel_space_left panel_space_right',
        store: group_store_chosed,
        border: true,
        width: '60%',
        columnLines: true,
        height: contentPanel.getHeight() * 0.68,
        ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        emptyText: '没有选择资源组',
        columns: group_columns_chosed,
        selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
        listeners : {
            activate:function(tab){
                if(!isDbcheckForExec){
                    resGroupFlag='true';
                    group_store_chosed.load();
                    chosedAgentIds.splice(0,chosedAgentIds.length);
                }
            }
        },
        dockedItems: [
            {
                xtype: 'toolbar',
                dock: 'top',
                items: [
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        hidden:isDbcheckForExec,
                        text: '删除',
                        handler: function() {
                            var records = group_grid_chosed.getSelectionModel().getSelection();
                            if (records.length > 0) {
                                for (var i = 0, len = records.length; i < len; i++) {
                                    chosedGroupIds.remove(records[i].get('id'));
                                    chosedGroupNames.remove(records[i].get('name'));
                                }
                                group_grid_chosed.ipage.moveFirst();
                                group_grid.ipage.moveFirst();
                            } else {
                                Ext.Msg.alert('提示', '请选择资源组！')
                                return
                            }
                        }
                    },
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        hidden:isDbcheckForExec,
                        text: '增加资源组',
                        handler: function() {
                            if (!chosedGroupWin) {
                                chosedGroupWin = Ext.create('Ext.window.Window', {
                                    title: '增加资源组',
                                    autoScroll: true,
                                    modal: true,
                                    resizable: false,
                                    closeAction: 'hide',
                                    layout: 'border',
                                    width: contentPanel.getWidth() - 190,
                                    height: contentPanel.getHeight(),
                                    items: [search_group_form, group_grid],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'bottom',
                                            layout: { pack: 'center' },
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    text: '确定',
                                                    cls: 'Common_Btn',
                                                    margin: '6',
                                                    handler: function() {
                                                        group_store_chosed.load();
                                                        this.up('window').close();
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    text: '关闭',
                                                    cls: 'Common_Btn',
                                                    margin: '6',
                                                    handler: function() {
                                                        this.up('window').close();
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                });
                            }

                            chosedGroupWin.show();
                            group_store.load();
                        }
                    }
                ]
            }
        ]
    });



    //*********************************************增加资源组相关控件 end***************************************************************************
    initFun();


    /** 已选窗口 资源组、单独agent tabPanel* */
    var tabPanelForChosedDevice = Ext.create ('Ext.tab.Panel',
        {
            tabPosition : 'top',
//	    region : 'center',
            region : 'west',
            activeTab :  resGroupFlag=='true'?1:0,
//	    cls:'customize_panel_back',
            cls:'window_border panel_space_top panel_space_left panel_space_right',
//	    width : '100%',
            width:'60%',
            height: contentPanel.getHeight()-300,
//	    height : contentPanel.getHeight (),
            border : false,
            defaults :
                {
                    autoScroll : false
                },
            items : [agent_grid_chosedForSee,group_grid_chosed]
        });



    var scriptDetailPanel = Ext.create('Ext.panel.Panel', {
        margin: '8 0 0 0',
        border: false,
        layout: {
            type: 'hbox',
            align: 'stretch'
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'bottom',
            border: false,
            ui: 'footer',
            margin: '5 0 5 0',
            layout: { pack: 'center' },
            items: [{
                text: '保存',
                hidden:isDbcheckForExec,
                handler: function() {
                    if (chosedAgentIds.length < 1&& chosedGroupIds.length< 1) {
                        Ext.MessageBox.alert("提示", "请选择服务器或资源组!");
                        return null;
                    }

                    var isOk = false;
                    var agentStateMsg = "";
                    if (chosedAgentIds.length >0){
                        // 检查agent状态
                        Ext.Ajax.request({
                            url: 'checkAgentState.do',
                            method: 'POST',
                            async: false,
                            params: {
                                agentIds: chosedAgentIds
                            },
                            success: function(response, request) {
                                isOk = Ext.decode(response.responseText).isOk;
                                agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
                            },
                            failure: function(result, request) {
                                agentStateMsg = "检查Agent状态出错！";
                            }
                        });
                    }

                    if (isOk||chosedGroupNames.length>0) {
                        saveFun();
                    } else {
                        Ext.Msg.confirm("请确认", agentStateMsg + "<br>选择的代理状态为异常，是否仍然保存？",
                        function(id) {
                            if (id == 'yes') {
                                saveFun();
                            }
                        });
                    }
                }
            },
            {
                text: '关闭',
                hidden: true,
                handler: function() {
                    cell = null;
                    parent.configwindowFlow.close();
                }
            },
            prevButton, nextButton, viewChosedScriptButton]
        }],
        items: [tabPanelForChosedDevice, paramGrid]
    });

    // 主Panel
    var MainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "flowCustomizedScriptWindowDiv",
        width: contentPanel.getWidth()-30,
        height: contentPanel.getHeight()-110,
        autoScroll: true,
        border: true,
        items: [formPanel, scriptDetailPanel]
    });

    function checkFile(fileName) {
        var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
        if (!file_reg.test(fileName)) {
            Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
            //Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
            return false;
        }
        return true;
    }

    function importExcel() {
        //销毁win窗口
        if (! (null == upldWin || undefined == upldWin || '' == upldWin)) {
            upldWin.destroy();
            upldWin = null;
        }

        if (! (null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
            upLoadformPane.destroy();
            upLoadformPane = null;
        }
        //导入文件Panel
        upLoadformPane = Ext.create('Ext.form.Panel', {
            width: 370,
            height: 100,
            frame: true,
            items: [{
                xtype: 'filefield',
                name: 'file',
                // 设置该文件上传空间的name，也就是请求参数的名字
                fieldLabel: '选择文件',
                labelWidth: 80,
                msgTarget: 'side',
                anchor: '100%',
                buttonText: '浏览...',
                width: 370
            }],
            buttonAlign: 'left',
            buttons: [{
                id: 'upldBtnIdAudi',
                text: '导入Agent文件',
                handler: function() {
                    var form = this.up('form').getForm();
                    var upfile = form.findField("file").getValue();
                    if (upfile == '') {
                        Ext.Msg.alert('提示', "请选择文件...");
                        return;
                    }

                    var hdtmpFilNam = form.findField("file").getValue();
                    if (!checkFile(hdtmpFilNam)) {
                        form.findField("file").setRawValue('');
                        return;
                    }
                    if (form.isValid()) {
                        Ext.MessageBox.wait("数据处理中...", "进度条");
                        form.submit({
                            url: 'importAgentForStart.do',
                            params: {
                                envType: jspParms.flag
                            },
                            success: function(form, action) {
                                var msg = Ext.decode(action.response.responseText).message;

                                var status = Ext.decode(action.response.responseText).status;
                                var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;

                                if (status == 1) {
                                    if (matchAgentIds && matchAgentIds.length > 0) {
                                        Ext.MessageBox.buttonText.yes = "确定";
                                        Ext.MessageBox.buttonText.no = "取消";
                                        Ext.Msg.confirm("请确认", msg,
                                        function(id) {
                                            if (id == 'yes') {
                                                Ext.Msg.alert('提示', "导入成功！");
                                                agent_ip.setValue('');
                                                app_name.setValue('');
                                                sys_name.setValue('');
                                                host_name.setValue('');
                                                os_type.setValue('');
                                                resourceGroupObj.setValue('');
                                                agentStatusCb.setValue('');
                                                editingChosedAgentIds = matchAgentIds.slice(0);
                                                pageBar.moveFirst();
                                            }
                                        });
                                    } else {
                                        Ext.Msg.alert('提示-没有匹配项', msg);
                                    }

                                } else {
                                    Ext.Msg.alert('提示', "导入成功！");
                                    agent_ip.setValue('');
                                    app_name.setValue('');
                                    sys_name.setValue('');
                                    host_name.setValue('');
                                    os_type.setValue('');
                                    resourceGroupObj.setValue('');
                                    agentStatusCb.setValue('');
                                    editingChosedAgentIds = matchAgentIds.slice(0);
                                    pageBar.moveFirst();
                                    chosedAgentIds = editingChosedAgentIds.slice(0);
                                    agent_store_chosedForSee.load();
                                    chosedAgentWinForSee.close();
                                }

                                upldWin.close();
                                return;
                            },
                            failure: function(form, action) {
                                secureFilterRsFrom(form, action);
                            }
                        });
                    }
                }
            },
            {
                text: '下载模板',
                handler: function() {
                    window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
                }
            }]
        });
        //导入窗口
        upldWin = Ext.create('Ext.window.Window', {
            title: '设备信息批量导入',
            width: 400,
            height: 140,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            items: [upLoadformPane]
        }).show();
        upldWin.on("beforeshow",
        function(self, eOpts) {
            var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
            form.reset();
        });

        upldWin.on("destroy",
        function(self, eOpts) {
            upLoadformPane.destroy();
        });
    }

    
    function getAgentIidsByGroupNames (groupResNames){
    	var agentiids = [];
    	Ext.Ajax.request({
                url: 'agentGroup/getAgentIidsByGroupNames.do',
                method: 'POST',
                async: false,
                params: {
                    groupResNames: groupResNames
                },
                success: function(response, options) {
                  agentiids  = Ext.decode(response.responseText).iids;
                },
                failure: function(result, request) {
                  
                }
            });
            return agentiids;
    }

    function getChosedGroupIdsByNames() {
        Ext.Ajax.request({
            url: 'agentGroup/getGroupIidsByGroupNames.do',
            method: 'POST',
            async: false,
            params: {
                groupResNames: chosedGroupNames
            },
            success: function(response, options) {
                chosedGroupIds = Ext.decode(response.responseText).iids;
            },
            failure: function(result, request) {
                Ext.MessageBox.alert ("提示", '获取资源组iid失败');
                return;
            }
        });
    }

    /** 初始化方法* */
    function initFun() {
    	if(jspParms.isScriptConvertToFlow=='true')
		{
			scriptServiceId = jspParms.serviceId;
		}else if(scriptServiceId==null || typeof (scriptServiceId) == "undefined") {
			scriptServiceId = iid;
		} else {
			if(isNaN(scriptServiceId)) {
				Ext.Ajax.request({
	                url: 'queryIidByUuid.do',
	                method: 'POST',
	                async: false,
	                params: {
	                    uuid: scriptServiceId
	                },
	                success: function(response, options) {
	                    	scriptServiceId = Ext.decode(response.responseText).serviceId;
	                },
	                failure: function(result, request) {
	                }
	            });
			}
		}
    	
    	
    	initCellInArray(actStartInfo[jspParms.rootEditer], cell);
    	
        var _actStartInfo = actStartInfo[jspParms.rootEditer][cell.mxIid];
        if(_actStartInfo == undefined)
        {
        	_actStartInfo = {};
        }
//        var _actStartInfo = cell;
        //actStartInfo['GFSSFLOWCUSTOMEDITTEST'][parent.cellObjGFSSFLOWCUSTOMEDITTEST.mxIid]

        stepNameObj.setValue(cell.value);
        shutdownCheckboxObj.setValue(_actStartInfo['isShutdown']);

        if (_actStartInfo.hasOwnProperty('eachNum')) {
			eachNum.setValue(_actStartInfo['eachNum']);
		}
        
        if (_actStartInfo.hasOwnProperty('chosedAgentIds') ) {
            chosedAgentIds = _actStartInfo['chosedAgentIds'];
        } else {
        	chosedAgentIds = [];
        }

        if (_actStartInfo.hasOwnProperty('chosedResGroups')) {
            chosedResGroups = _actStartInfo['chosedResGroups'];
            chosedGroupNames = _actStartInfo['chosedResGroups'];
//            resourceGroupObj.setValue(chosedResGroups);
        } else {
            chosedResGroups = new Array();
            resourceGroupObj.setValue('');
        }
        if(chosedGroupNames !=null && chosedGroupNames.length>0){
            getChosedGroupIdsByNames();
            resGroupFlag = 'true';
            group_store_chosed.load();
            group_store.load();
        }

        resourceGroupObj.fireEvent('select');

        if (_actStartInfo.hasOwnProperty('globalParams')) {
            globalParams = _actStartInfo['globalParams'];
        } else {
        	globalParams = {};
        }

        if (_actStartInfo.hasOwnProperty('globalConfigParams')) {
            globalConfigParams = _actStartInfo['globalConfigParams'];
        }else {
        	globalConfigParams = {};
        }

        if (_actStartInfo.hasOwnProperty('globalStartUser')) {
            startUserObj.setValue(_actStartInfo['globalStartUser']);
        }

        if (_actStartInfo.hasOwnProperty('globalConfigStartUser')) {
            globalConfigStartUser = _actStartInfo['globalConfigStartUser'];
        }else {
        	globalConfigStartUser = {};
        }
        
        if (_actStartInfo.hasOwnProperty('finalChosedAgentsAndDbSources')) {
        	finalChosedAgentsAndDbSources = _actStartInfo['finalChosedAgentsAndDbSources'];
        }else {
        	finalChosedAgentsAndDbSources = {};
        
        }
        if (scriptServiceId > 0) {
            paramStore.load();
            configParamStore.load();
            Ext.Ajax.request({
                url: 'getScritContentScriptService.do',
                method: 'POST',
                async: false,
                params: {
                    iid: scriptServiceId,
                    from: 2,
                    flag: "0"
                },
                success: function(response, options) {
                    var content = Ext.decode(response.responseText).content;
                    var serviceName = Ext.decode(response.responseText).serviceName;
                    cellScriptType = Ext.decode (response.responseText).scriptType;
                    scriptTypeForTaskAudi = cellScriptType;
				    if(cellScriptType=='sql') {
				    	choseDbSourceButton.show();
				    } else {
				    	choseDbSourceButton.hide();
				    }
                    scriptContentObj.setValue(content);
                    scriptNameObj.setValue(serviceName);
                },
                failure: function(result, request) {
                    scriptContentObj.setValue('');
                    scriptNameObj.setValue("");
                }
            });
        }
        agent_store_chosedForSee.load();
    }
    function trim(t) {
        t = t.replace(/(^\s*)|(\s*$)/g, "");
        return t.replace(/(^ *)|( *$)/g, "");
    }
    function saveFun() {
    	
    	var en = 0;
		if(jspParms.isScriptConvertToFlow=='true') {
			en = eachNum.getValue();
			
			if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
				if(en>concurrentNumber) {
					Ext.MessageBox.alert ("提示", '并发数量不能超过'+concurrentNumber);
					return;
				} else if(en==0) {
					Ext.MessageBox.alert ("提示", '并发数量不能为0');
					return;
				}
			} else {
				Ext.MessageBox.alert ("提示", '并发数量不合法！');
				return;
			}
			
			if(Ext.isEmpty(en)) {
				en = 0;
			}
		}
    	
        if (stepNameObj.getValue().trim() == '') {
            Ext.Msg.alert('提示', '步骤名称不允许为空!');
            return null;
        }
        if ('开始' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<开始>！');
            return null;
        }
        if ('结束' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<结束>！');
            return null;
        }

        var m = paramStore.getRange(0, paramStore.getCount() - 1);
        for (var i = 0,
        len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';

            if (paramType == 'int' && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                    return;
                }
            }
            if (paramType == 'float' && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"') >= 0) {
                if (cellScriptType == 'bat') {
                    Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
                }
            }
            if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
                globalParams[m[i].get('iid')] = paramDefaultValue;
            } else {
            	delete globalParams[m[i].get('iid')];
            }
        }

        var tempConfigStartUser = {};
        var tempConfigParams = {};
        var tempReturn;
        $.each(chosedAgentIds,
        function(index, closedAgentId) {
            if (globalConfigStartUser.hasOwnProperty(closedAgentId)) {
                tempConfigStartUser[closedAgentId] = globalConfigStartUser[closedAgentId];
            }
            
            if (globalConfigParams.hasOwnProperty(closedAgentId)) {
        		tempConfigParams[closedAgentId] = globalConfigParams[closedAgentId];
            }
            if(cellScriptType=='sql'){
            	if(editingChosedAgentsAndDbSources.hasOwnProperty(closedAgentId)) {
            		var dbId = editingChosedAgentsAndDbSources[closedAgentId];
            		if(dbId>0) {
            			finalChosedAgentsAndDbSources[closedAgentId] = dbId;
            		} else {
            			tempReturn =true;
            			Ext.MessageBox.alert ("提示", "有服务器没有配置数据源!");
            		}
            	} else if(finalChosedAgentsAndDbSources.hasOwnProperty(closedAgentId)){
            		//不做操作,因为最终要保存的json串已经有了
            	}else
            	{
            		tempReturn =true;
            		Ext.MessageBox.alert ("提示", "有服务器没有配置数据源!");
            	}
            }
        });
        if(tempReturn){
        	return;
        }
        globalConfigStartUser = tempConfigStartUser;
        globalConfigParams = tempConfigParams;

        cell.chosedResGroups = chosedGroupNames;
        cell.chosedAgentIds = chosedAgentIds;
        cell.globalParams = globalParams;
        cell.globalConfigParams = globalConfigParams;
        cell.globalStartUser = Ext.util.Format.trim(startUserObj.getValue());
        cell.globalConfigStartUser = globalConfigStartUser;
        cell.chosedAgentsAndDbSources = finalChosedAgentsAndDbSources;
        
        
        actStartInfo[jspParms.rootEditer][cell.mxIid]['chosedResGroups'] = chosedGroupNames;
        actStartInfo[jspParms.rootEditer][cell.mxIid]['chosedAgentIds'] = chosedAgentIds;
        actStartInfo[jspParms.rootEditer][cell.mxIid]['globalParams'] = globalParams;
        actStartInfo[jspParms.rootEditer][cell.mxIid]['globalConfigParams'] = globalConfigParams;
        actStartInfo[jspParms.rootEditer][cell.mxIid]['globalStartUser'] = Ext.util.Format.trim(startUserObj.getValue());
        actStartInfo[jspParms.rootEditer][cell.mxIid]['globalConfigStartUser'] = globalConfigStartUser;
        actStartInfo[jspParms.rootEditer][cell.mxIid]['finalChosedAgentsAndDbSources'] = finalChosedAgentsAndDbSources;
        actStartInfo[jspParms.rootEditer][cell.mxIid]['isShutdown'] = shutdownCheckboxObj.getValue();
        actStartInfo[jspParms.rootEditer][cell.mxIid]['eachNum'] = en;
        
        cell.value = stepNameObj.getValue();
        cell.isShutdown = shutdownCheckboxObj.getValue();

        Ext.Msg.alert('提示', '当前步骤保存成功!');
    }


    function initCellInArray(asi, cell){
		if(!asi.hasOwnProperty(cell.mxIid)) {
			if(cell.bean.adapter=='scriptServiceStyle') {
				asi[cell.mxIid] = {
						'actNo': cell.id,
						'actType': 0,
						'actName': cell.value,
						'isShutdown': cell.isShutdown
				};
			} else if(cell.bean.adapter=='usertaskStyle') {
				asi[cell.mxIid] = {
						'actNo': cell.id,
						'actType': 1,
						'actName': cell.value,
						'message': cell.ireminfo
				};
			} else if(cell.bean.adapter=='callflowStyle') {
				asi[cell.mxIid] = {
						'actNo': cell.id,
						'actType': 2,
						'actName': cell.value
				};
			}
		}
    }
    /**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
    function getCellFun(inflag) {
        // 遍历所有节点
        var rootObj = model.getRoot();
        var count = model.getChildCount(rootObj);
        for (var i = 0; i < count; i++) {
            var cells = rootObj.getChildAt(i);
            var counts = cells.getChildCount();
            var beforeCell = null; // 上一个节点
            var afterCell = null; // 下一个节点
            var selfCell = null; // 自己
            for (var j = 0; j < counts; j++) {
                var cellss = cells.getChildAt(j);
                // 判断循环至的节点样式是否与传入的样式一致
                if (cellss.style == cell.style) {
                    if (cellss == cell) {
                        // 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
                        selfCell = cell;
                    } else {
                        // 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
                        selfCell == null ? beforeCell = cellss: afterCell = cellss;
                    }
                    // 如果获取到了想要的节点，则跳出循环
                    if (selfCell != null && ((inflag == 'after' && afterCell != null) || (inflag == 'before' && beforeCell != null))) {
                        break;
                    }
                }
            }
            // 返回指定节点
            return inflag == 'after' ? afterCell: beforeCell;
        }
    }
});