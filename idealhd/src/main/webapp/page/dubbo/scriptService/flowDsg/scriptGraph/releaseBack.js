/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.releaseBack', {
    extend: 'Ext.form.Panel',
    alias: 'widget.releaseBack',

    requires: [
        'Ext.form.ComboBox',
        'Ext.form.TextArea',
        'Ext.button.Button'
    ],

    height: 150,
    title: '',
    frame : true,
	buttonAlign : "left",
	
	layout: 'anchor',
	
	serviceInfo : {},
	initOperBtn: function(n){
		//"dbcheckForExec"
		
	},
	
    initComponent: function() {
        var me = this;
        var jspParms = me.jspParms;
        var serviceInfo = me.serviceInfo;
        if(jspParms.scriptLevelDisplay==undefined)
        {
        	jspParms.scriptLevelDisplay = '';
        }
        
        
   	 
        var viewBasicInfoButton = Ext.create("page.dubbo.scriptService.flowDsg.scriptGraph.btn.baseInfoBtn", {
        	serviceInfo : serviceInfo
        });
        
        var backToDoubleCheckBtn = Ext.create("page.dubbo.scriptService.flowDsg.scriptGraph.btn.backToDoubleCheckBtn", {
        	backUrl 		: 'initGetWorkitemRecordsList.do?activeTabNum=6',
        	backTitle 		: '待办事项',
        	hidden:  jspParms.from ==66?true:false,
        	from			: 0,
        	messageWindow 	: messageWindow
        });
        
        var dbSubButton = Ext.create('Ext.Button', {
        	text : '提交',
        	margin : '0 0 0 5',
        	textAlign : 'center',
        	handler : function() {
	        	Ext.Ajax.request ({
				    url : 'getScriptServiceStatusByWorkItemId.do',
				    method : 'POST',
				    params :
				    {
				    	workItemId : jspParms.iworkItemid
				    },
				    success : function (response, opts)
				    {
				    	//判断输入的审核人是否合法 start
		            	var displayField =auditorComBox_sm.getRawValue();
						if(!Ext.isEmpty(displayField)){
							//判断输入是否合法标志，默认false，代表不合法
							var flag = false;
							//遍历下拉框绑定的store，获取displayField
							auditorStore_sm.each(function (record) {
								//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
							    var data_fullName = record.get('fullName');
							    if(data_fullName == displayField){
							    	flag =true;
							    	//combo.setValue(record.get('loginName'));
							    }
							});
							if(!flag){
							 	Ext.Msg.alert('提示', "输入的审核人非法！");
							 	auditorComBox_sm.setValue("");
							 	return;
							} 
						}
						//判断输入的审核人是否合法  end
					    var status = Ext.decode (response.responseText).status;
					    if (status != -1 ) { // 已经不是草稿状态，处于审核中或者已经上线
							Ext.Msg.alert('提示', "该脚本已经不是草稿状态,请终止该服务！");
							return;
						}
					    var e = auditorComBox_sm.getValue();
		            	if(!e) {
		            		Ext.Msg.alert ('提示','没有选择审核人！');
		            		return;
		            	}
				    	var des = publishDesc.getValue();
				    	if(!des) {
		            		Ext.Msg.alert ('提示','没有填写发布申请说明！');
		            		return;
		            	}
				    	if(des.length > 255) {
						  	Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
						  	return;
						 }
				    	Ext.Ajax.request ({
						    url : 'operWorkitemByiidForSsPublish.do',
						    method : 'POST',
						    params :
						    {
						        istateForQuery : 1,
						        iidForQuery : jspParms.iworkItemid,
						        execUser: e,
						        des:des
						    },
						    success : function (response, opts)
						    {
							    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
							    {
							    	if(jspParms.from==1) {
							    		messageWindow1.close();
							    		destroyRubbish(); //销毁本页垃圾
							    		contentPanel.getLoader().load({
							    			url: 'pandect1.do',
							    			scripts: true});
							    		
							    	 } else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
		    					    		messageWindow_ssq.close();
		    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
		    					     }else {
							    		messageWindow.getLoader ().load (
											{
											    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
											    autoLoad : true,
											    scripts : true
											});
											messageWindow.setTitle ('待办事项');
							    	}
							    });
						    }
						});
				    }
				});
        	}
	  });
        var dbtermiButton = Ext.create('Ext.Button', {
        	text : '终止',
        	margin : '0 0 0 5',
        	textAlign : 'center',
        	handler : function() {
        		Ext.Ajax.request ({
        			url : 'operWorkitemByiidForSsPublish.do',
					    method : 'POST',
					    params :
					    {
					        istateForQuery : 6,
					        iidForQuery : jspParms.iworkItemid
					    },
					    success : function (response, opts)
					    {
						    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
						    {
						    	if(jspParms.from==1) {
						    		messageWindow1.close();
						    		destroyRubbish(); //销毁本页垃圾
						    		contentPanel.getLoader().load({
						    			url: 'pandect1.do',
						    			scripts: true});
						    	}else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
		    					    		messageWindow_ssq.close();
		    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
		    			    	} else {
						    		messageWindow.getLoader ().load (
										{
										    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
										    autoLoad : true,
										    scripts : true
										});
										messageWindow.setTitle ('待办事项');
						    	}
						    });
					    }
					});
	 }
 });
 Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	var auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    editable: true,
	    fieldLabel: "审核人",
	    labelWidth: 85,
	    store: auditorStore_sm,
	    queryMode: 'local',
	    labelAlign:'right',
	    padding : '0 0 5 0',
	    hidden : jspParms.actionType=='dbback',
	    displayField: 'fullName',
	    valueField: 'loginName',
	    listeners: { //监听 
	        render : function(combo) {//渲染 
	            combo.getStore().on("load", function(s, r, o) { 
	                combo.setValue(r[0].get('loginName'));//第一个值 
	            }); 
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
//	        blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_sm.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_sm.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_sm.setValue("");
//					 	return;
//					} 
//				}
//			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
	    } 
	  });
	var publishDesc = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '发布申请说明',
        labelAlign:'right',
        padding : '0 0 5 0',
        labelWidth: 93,
        width : 400,
        height :60,
        value: jspParms.publishDesc,
        autoScroll: true
    });	
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea', {
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        labelAlign:'right',
	        padding : '0 0 5 0',
	        width : 400,
	        labelWidth: 85,
	        labelSepartor : "：",
	        readOnly: true,
	        height: 60,
	        value: jspParms.backInfo
		});
		
        Ext.applyIf(me, {
            items: [{
	            border: false,
	            flex: 1,
	            layout: 'vbox',
	            items: [auditorComBox_sm]
	        }, {
	            border: false,
	            flex: 1,
	            layout: 'vbox',
	            items: [publishDesc]
	        }, {
	            border: false,
	            flex: 1,
	            layout: 'vbox',
	            items: [backInfo]
	        }],
            buttons: [viewBasicInfoButton,dbSubButton,dbtermiButton,backToDoubleCheckBtn]
        });
    	
        me.callParent(arguments);
    }
});