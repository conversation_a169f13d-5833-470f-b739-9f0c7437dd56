<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSFLOWCUSTOMPRODUCTFROMTM=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSFLOWCUSTOMPRODUCTFROMTM=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSFLOWCUSTOMPRODUCTFROMTM = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSFLOWCUSTOMPRODUCTFROMTM = <%=request.getAttribute("fromType")%>;
var workItemidGFSSFLOWCUSTOMPRODUCTFROMTM = <%=request.getAttribute("workItemid")%>;
var fromGFSSFLOWCUSTOMPRODUCTFROMTM = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSFLOWCUSTOMPRODUCTFROMTM='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSFLOWCUSTOMPRODUCTFROMTM=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSFLOWCUSTOMPRODUCTFROMTM=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMPRODUCTFROMTM='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMPRODUCTFROMTM='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSFLOWCUSTOMPRODUCTFROMTM = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSFLOWCUSTOMPRODUCTFROMTM = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMPRODUCTFROMTM/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSFLOWCUSTOMPRODUCTFROMTM" style="width: 100%;height: 100%"></div>
</body>
</html>