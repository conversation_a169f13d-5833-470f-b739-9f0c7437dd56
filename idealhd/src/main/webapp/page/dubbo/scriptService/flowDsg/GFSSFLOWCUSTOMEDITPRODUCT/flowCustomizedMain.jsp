<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSFLOWCUSTOMEDITPRODUCT=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSFLOWCUSTOMEDITPRODUCT=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSFLOWCUSTOMEDITPRODUCT = <%=request.getAttribute("isScriptConvertToFlow")%>;

var customIdGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getParameter("customId")%>;

var customNameGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getParameter("customName")%>';

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSFLOWCUSTOMEDITPRODUCT = <%=request.getAttribute("fromType")%>;
var workItemidGFSSFLOWCUSTOMEDITPRODUCT = <%=request.getAttribute("workItemid")%>;
var fromGFSSFLOWCUSTOMEDITPRODUCT = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSFLOWCUSTOMEDITPRODUCT='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSFLOWCUSTOMEDITPRODUCT=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSFLOWCUSTOMEDITPRODUCT=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCT='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCT='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSFLOWCUSTOMEDITPRODUCT = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSFLOWCUSTOMEDITPRODUCT = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITPRODUCT/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSFLOWCUSTOMEDITPRODUCT" style="width: 100%;height: 100%"></div>
</body>
</html>