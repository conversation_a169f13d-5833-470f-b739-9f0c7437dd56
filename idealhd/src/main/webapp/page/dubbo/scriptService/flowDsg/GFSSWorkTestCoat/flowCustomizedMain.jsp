<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSWorkTestCoat=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSWorkTestCoat=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSWorkTestCoat=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSWorkTestCoat=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSWorkTestCoat=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSWorkTestCoat=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSWorkTestCoat = <%=request.getAttribute("isScriptConvertToFlow")%>;

var flowIdGFSSWorkTestCoat = <%=request.getParameter("flowId")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSWorkTestCoat='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSWorkTestCoat='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSWorkTestCoat=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSWorkTestCoat=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSWorkTestCoat='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSWorkTestCoat='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSWorkTestCoat = <%=request.getAttribute("fromType")%>;
var workItemidGFSSWorkTestCoat = <%=request.getAttribute("workItemid")%>;
var fromGFSSWorkTestCoat = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSWorkTestCoat = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSWorkTestCoat = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSWorkTestCoat = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSWorkTestCoat = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSWorkTestCoat=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSWorkTestCoat=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSWorkTestCoat='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSWorkTestCoat='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSWorkTestCoat='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSWorkTestCoat=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSWorkTestCoat=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSWorkTestCoat=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSWorkTestCoat='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSWorkTestCoat='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSWorkTestCoat='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSWorkTestCoat = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSWorkTestCoat = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSWorkTestCoat = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSWorkTestCoat = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSWorkTestCoat = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSWorkTestCoat = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSWorkTestCoat/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSWorkTestCoat" style="width: 100%;height: 100%"></div>
</body>
</html>