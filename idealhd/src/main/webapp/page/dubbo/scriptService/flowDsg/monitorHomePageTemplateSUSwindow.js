/*******************************************************************************
 * 流程定制维护窗口-阶段模板
 ******************************************************************************/
var graphFlowWindow;
var modelFlowWindow;
var editorFlowWindow;
var stageNameObj=null;
var templateNameObj=null;
var cellObjFlowWindow=null;
var formPanel2=null;
//var loadGetParentValue=false;


var releaseMonitorStepStore;
Ext.onReady(function () {
	/** 模板下拉框数据Model* */
	Ext.define ('templateModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'instanceName',
		        type : 'string'
		    },{
		        name : 'iid',
		        type : 'long'
		    }
	    ]
	});
	/** 模板下拉框据源* */
	var templateNameStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad :true,
	    autoDestroy : true,
	    model : 'templateModel',
	    pageSize : 100,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getTemplateList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	templateNameStore.on ('beforeload', function (store, options)
			{
				var new_params =
				{
						suspahaseId : parent.cellObj.phaseId
				};
				Ext.apply (templateNameStore.proxy.extraParams, new_params);
			});
	stageNameObj= Ext.create ('Ext.form.field.Text',
			{
				fieldLabel : '阶段名称',
				margin : '5 0 0 0',
				readOnly:true
			
			});
	templateNameObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '模板名称',
			    margin : '5 0 5 0',
			    store : templateNameStore,
			    displayField : 'instanceName',
			    valueField : 'iid',
			    triggerAction : 'all',
			    editable : false,
			    readOnly:true,
			    mode : 'local',
			    listeners :
	            {
		            select : function ( combo, record, eOpts )
		            {
//		            	if(this.value==parent.cellObj.modelId)
//	            		{
//	            		loadGetParentValue=true;
//	            		}
		            	cellClickFun(null,'步骤详细');
		            	loadGraphTemplate(this.value);
		            }
	            
	            }
			});
	var formPanel =Ext.create ('Ext.form.Panel',
			{
			region: "north",
			    // title: 'Basic Form',
				border:false,
			    fieldDefaults :
			    {
			        labelAlign : 'right',
//			        width : 475,
			        width : contentPanel.getWidth ()-30,
			        labelWidth : 60
			    },
//			    bodyPadding : 5,
			    width : '100%',
			    buttonAlign : 'center',
			    items : [
			            stageNameObj, templateNameObj
			            //, templateParamObj, resourceGroupObj, serverListObj
			    ],
			    
			    buttons : [
                        {
                            text : '刷新',
                            handler : function ()
                            {
                            	//步骤详细未展开过，不进行刷新
                            	if(releaseMonitorStepStore.getCount()>0)
                            		{
                            		releaseMonitorStepStore.reload();
                            		}
                            	setStustSon();
                            }
                        },
			            {
			                text : '返回',
			                handler : function ()
			                {
			                	parent.winStep.close ();
			                }
			            }
			    ]
			});
	
	var saveButtonFlowWindow = Ext.create('Ext.Button', {
	    text : '保存',
	    width : 70,
	    height : 22,
	    margin : '5 0 0 5',
	    textAlign : 'center',
//	    cls : 'Common_Btn',
	    handler : function() {
//	        editorSon.execute ('save');
	      }
	  });
	  
	  Ext.define('releaseMonitorStepData', {
		    extend : 'Ext.data.Model',
		    fields : [ {
		      name : 'iid',
		      type : 'long'
		    },{
		      name : 'flowId',
		      type : 'long'
		    }, {
		      name : 'conner', // 顺序步骤
		      type : 'long'
		    }, {
		      name : 'serner', // 步骤标识
		      type : 'long'
		    }, {
		      name : 'runInsName', // 任务实例名称
		      type : 'string'
		    }, {
		      name : 'actName', // 任务名称，也叫步骤名称
		      type : 'string'
		    }, {
		      name : 'sysName', // 系统名称
		      type : 'string'
		    }, {
		      name : 'shellPath', // 脚本路径
		      type : 'string'
		    }, {
		      name : 'execUser', // 执行用户
		      type : 'string'
		    }, {
		      name : 'dependentStepName', // 依赖步骤名称
		      type : 'string'
		    }, {
		      name : 'ip', // 代理服务器
		      type : 'string'
		    }, {
		      name : 'startTime',
		      type : 'string'
		    }, {
		      name : 'endTime',
		      type : 'string'
		    }, {
		      name : 'runLength',
		      type : 'long'
		    }, {
		      name : 'actType',
		      type : 'long'
		    }, {
		      name : 'state',
		      type : 'string'
		    }, {
		      name : 'orignalState',
		      type : 'int'
		    }, {
		      name : 'isFail',
		      type : 'int'
		    }, {
				name : 'modelType',
				type : 'long'
			}, {
				name : 'redoable',
				type : 'long'
			} ]
		  });
		releaseMonitorStepStore = Ext.create('Ext.data.Store', {
		    autoLoad: false,
//		    pageSize: 50,
		    model: 'releaseMonitorStepData',
		    groupField : 'sysName',
		    proxy: {
		        type: 'ajax',
		        url: 'getReleaseMonitorGroupListForSUS.do',
		        reader: {
		            type: 'json',
		            root: 'dataList',
		            totalProperty: 'total'
		        }
		    }
		  });
		var releaseMonitorStepColumns = [{
		    text : 'ID',
		    dataIndex : 'iid',
		    width : 80,
		    hidden : true
		  }, {
		    text : '工作流ID',
		    dataIndex : 'flowId',
		    width : 150,
		    hidden: true
		  }, {
		    text : '系统名称',
		    dataIndex : 'sysName',
		    width : 150,
		    hidden: true
		  }, {
		    text : '步骤',
		    dataIndex : 'serner',
		    width : 40
		  },{
			text : 'model',
			dataIndex : 'modelType',
			hidden : true
		  },{
			text : 'redo',
			dataIndex : 'redoable',
			hidden : true
		  }, {
		    text : '顺序',
		    dataIndex : 'conner',
		    width : 50
		  }, {
		    text : '任务实例名称',
		    dataIndex : 'runInsName',
//		    flex : 1,
		    width : 150,
		    hidden: true
		  }, {
		    text : '步骤名称', // 也叫步骤名称
		    dataIndex : 'actName',
//		    flex : 1,
		    width : 150,
		    renderer: function(value,metaData,record){
		      if(record.get('actType')==2) { // 提醒任务
		        return "<span style='background: #f0ad4e;color:#fff;padding: 2px;'>"+ value +"</span>";
		      } else {
		        return value;
		      }
		    }
		  }, {
		    text : '依赖步骤',
		    dataIndex : 'dependentStepName',
//		    flex : 1,
		    width : 150,
		    renderer : function(value, metadata) {
		      if(value) {
		        var n = value.replace(new RegExp(',','gm'),'<br>');
		        metadata.tdAttr = 'data-qtip="' + n + '"';
		      }
		      return value;
		    }
		  }, {
		    text : '代理服务器',
		    dataIndex : 'ip',
		    width : 100,
		    hidden: false
		  }, {
		    text : '脚本路径',
		    dataIndex : 'shellPath',
//		    flex : 1,
		    width : 150,
		    renderer : function(value, metadata) {
		      if(value) {
		        metadata.tdAttr = 'data-qtip="' + value + '"';
		      }
		      return value;
		    }
		  }, {
		    text : '执行用户',
		    dataIndex : 'execUser',
//		    flex : 1
		    width : 100
		  }, {
		    text : '开始时间',
		    dataIndex : 'startTime',
//		    flex : 1
		    width : 150
		  }, {
		    text : '结束时间',
		    dataIndex : 'endTime',
//		    flex : 1
		    width : 150
		  },{ 
		    text: '运行时长',
		    dataIndex: 'runLength',
//		    flex : 1,
		    width : 100,
		    renderer: function(value,metaData,record){
		      if(value>0){
		        return secondsToString(Math.round(value / 1000));
		      } else {
		        return;
		      }
		    }
		  }, {
		    text : '状态',
		    dataIndex : 'state',
//		    width : 60,
		    width : 100,
		    renderer: function(value,metaData,record){
		      if(value=="异常") {
		        return "<span style='background: #fd808a;color:#fff;padding: 2px;'>"+ value +"</span>";
		      } else if(value=="异常略过") {
		        return "<span style='background: #8e44ad;color:#fff;padding: 2px;'>"+ value +"</span>";
		      }else {
		        return value;
		      }
		    }
		  },{ 
		    text: '操作',
		    dataIndex: 'stepOperation',
//		    flex : 1,
		    width:300,
		    align:'center',
		    renderer:function(value,p,record,rowIndex){
		      var state = record.get('state');
		      var stepName = record.get('actName');
		      var insName = record.get('runInsName');
		      var flowId = record.get('flowId');
		      var sysName=record.get('sysName');
		      var stepId=record.get('iid');
		      var detailFunc = "";
		      var finalA = "";
		      var finalB = "";
		      if(state=='异常' ) {
		    	var redoable = record.get('redoable');
		        detailFunc = "openWindowsErr('getShellOutPutFinish.do','"+ sysName +"','"+ stepName +"','"+ insName +"',"+ flowId +","+ stepId +","+ 0 + ","+ redoable+ ")";
		      }
		      else if(state=='运行中') {
		        if(record.get('actType')==2) { // 人工
		          detailFunc = "openUTWindow("+flowId+")";
		        } else {
		          detailFunc = "openWindowsRunning('getShellOutPutRunning.do', "+ flowId +", '"+ stepName +"', '"+ insName +"')";
		        }
		      }
		      else if(state=='已完成'|| state=='异常略过'){
		        detailFunc = "openWindowsFinish('getShellOutPutFinish.do','"+ sysName +"','"+ stepName +"','"+ insName +"',"+ stepId +")";
		      } 
		      if(detailFunc){
		        finalA += '<a href="javascript:'+ detailFunc +';"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>详情</a>&nbsp;&nbsp;';
		      }
		      
//		      if(state!='已完成' && state!='略过') {
//		        finalB = '<a href="javascript:void(0);" onclick="skipStep('+ flowId +','+ record.get('iid') +');"><img src="images/monitor_skip.png" align="absmiddle"></img>&nbsp;忽略</a>';
//		      }
		      if (record.get('modelType') == 'pub') {
					var flowId = record.get('flowId');
					finalB = '<span class="switch_span"><a href="javascript:void(0);" onclick="reTryStep('
							+ flowId
							+ ','
							+ record.get('iid')
							+ ');"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重新执行&nbsp;&nbsp;</a><a href="javascript:void(0);" onclick="DeploymentFail('
							+ flowId
							+ ','
							+ record.get('iid')
							+ ');"><img src="images/monitor_bg.png" align="absmiddle" class="Deployment_failure"></img>部署失败&nbsp;&nbsp;</a></span>';
				} else {
					if (record.get('state') == '运行中'
							|| record.get('state') == '异常') {
						var flowId = record.get('flowId');
						finalB =  '<span class="switch_span"><a href="javascript:void(0);" onclick="reTryStep('
							+ flowId
							+ ','
							+ record.get('iid')
							+ ');"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重新执行&nbsp;&nbsp;</a><span class="switch_span"><a href="javascript:void(0);" onclick="DeploymentOK('
								+ flowId
								+ ','
								+ record.get('iid')
								+ ');"><img src="images/monitor_bg.png" align="absmiddle" class="Manual_deployment"></img>手工部署&nbsp;&nbsp;</a><a href="javascript:void(0);" onclick="DeploymentFail('
								+ flowId
								+ ','
								+ record.get('iid')
								+ ');"><img src="images/monitor_bg.png" align="absmiddle" class="Deployment_failure"></img>部署失败&nbsp;&nbsp;</a></span>';
					} else if (record.get('state') == '未开始') {
						var flowId = record.get('flowId');
						finalB =  '<span class="switch_span"><a href="javascript:void(0);" onclick="skipStep('
								+ flowId
								+ ','
								+ record.get('iid')
								+ ');"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>略  过&nbsp;&nbsp;</a></span>';
					}
				}
		      return '<span class="switch_span">'+ finalA + finalB +'</span>';
		      
		    }
		  }];
		/**	详细步骤列表**/
		var releaseMonitorStepGrid = Ext.create('Ext.grid.Panel', {
		    height: contentPanel.getHeight()-40,
		    store:releaseMonitorStepStore,
		    border:true,
		    columnLines : true,
		    columns:releaseMonitorStepColumns,
//			dockedItems : [ {
//				dock : 'bottom',
//				xtype : 'toolbar',
//				items : ['->', {
//					text : '刷新',
//	                width : 70,
//	                height : 22,
//	                textAlign : 'center',
//	                cls : 'Common_Btn',
//			        handler  : function(){
//			        	releaseMonitorStepStore.reload();
//			        }
//			      } ]
//			} ],
//		    features : [ Ext.create('Ext.grid.feature.Grouping', {
//		          enableGroupingMenu : false,// 是否在表头菜单中进行分组控制，默认为true
//		          groupHeaderTpl : '{name}', // 分组显示的模板
//		          startCollapsed : false
//		        }) ],
		    viewConfig:{
		      enableRowBody : true,
		      getRowClass:function(record,rowIndex,rowParams,arriveStore){
		        var cls = '';
		        if(record.data.isFail=='0'){
		          if(record.data.orignalState=='0'){
		            cls = 'row_Blue';
		          }else if(record.data.orignalState=='1'){
		            cls = '';
		          }else if(record.data.orignalState=='2'){
		            cls = 'row_Green';
		          }else if(record.data.orignalState=='3'){
		            cls = 'row_Gray';
		          }
		        }else if(record.data.isFail=='1'||record.data.isFail=='2'){
		          cls = 'row_Red';
		        }else if(record.data.isFail=='3'||record.data.isFail=='4'){
		          cls = 'row_Orage';
		        }
		        return cls; 
		      }
		    }
		  });
	  

	
	var middlePanel=Ext.create('Ext.Panel', {
//		title:'提示信息',
//		width:'100%',
		height : contentPanel.getHeight ()-165,
		split: true,
//		region: 'south',
		layout:'fit',
		border : false,	
//		titleAlign : 'left',
		items : [
	releaseMonitorStepGrid
	            ]
	});
	formPanel2 =Ext.create ('Ext.form.Panel',
			{
		region: "center",
		margin : '0 0 0 5',
		title: '步骤详细', 
				border:true,

			    fieldDefaults :
			    {
			        labelAlign : 'right',
//			        width : 475,
			        width : contentPanel.getWidth ()/2-200,
			        labelWidth : 60
			    },
//			    bodyPadding : 5,
			    width : '100%',
			    buttonAlign : 'center',
			    items : [
				    {
				        xtype : "container",
				        layout : "hbox",
				        margin : 2
				    }
				    ,middlePanel
			    
			    ]
			    
			});
	var westPanel = Ext.create('Ext.panel.Panel', {
		title: '模板图', 
		region: "west",
		width: '50%',
		height : contentPanel.getHeight ()-65,
		border : true,
		collapsible:true,
		html:'<div id="flowCustomizedWindowDiv2" style="position:absolute;overflow:hidden;top:0px;left:0px;bottom:0px;right:0px;cursor:default;background-image:url(images/mxgraphImages/mxgraph_grid.png);"></div>',
		bodyPadding : 5
		
	});
	var MainPanel = Ext.create('Ext.panel.Panel', {
		layout: "border",
		renderTo : "flowCustomizedWindowDiv",
		width : '100%',
		height : contentPanel.getHeight ()-32,
		border : false,
		bodyPadding : 5,
			 items: [
	                    westPanel,
	                    formPanel,
	                    formPanel2
	                ]
	});
	/** 窗口尺寸调节* */
	 contentPanel.on ('resize', function ()
	 {
		 MainPanel.setWidth('100%');
	 })
	
	 initFun ();
});
/** 初始化方法* */
function initFun ()
{
	stageNameObj.setValue (parent.cellObj.value);
	editorFlowWindow = new mxEditor ();
	graphFlowWindow = editorFlowWindow.graph;
	modelFlowWindow = graphFlowWindow.getModel ();
	editorFlowWindow.setGraphContainer (document.getElementById('flowCustomizedWindowDiv2'));
		//设置启用,就是允不允许你改变CELL的形状内容。
	graphFlowWindow.setEnabled(true);
		//连线是否必须连接至节点
	graphFlowWindow.setAllowDanglingEdges (false);
	graphFlowWindow.dblClick = function (evt, cell)
		{
			
			// Do not fire a DOUBLE_CLICK event here as mxEditor will
			// consume the event and start the in-place editor.
			if (this.isEnabled () && !mxEvent.isConsumed (evt) && cell != null && this.isCellEditable (cell))
			{
				
				if (this.model.isEdge (cell))
				{
					//连线编辑
//					this.startEditingAtCell (cell);
				}
				else
				{
					if(cell.style!='beginStyle'&&cell.style!='endStyle')
					{
						cellClickFun(cell,'步骤详细-'+cell.value);
						releaseMonitorStepStore.load({
					       params: {
					         insId : parent.cellObj.ichildinstanceId,
					         serner : cell.id,
					         showFinishFlow: 1,
					         isHistory: 0
					       }
					     });
					}
					
//					openServerWindw(1,1);
					//							alert(cell.id);
					//							cell.secondValue='22222';
					//cellObj = cell;
					//openWindw (cell.value);
					//							var content = document.createElement('div');
					//							content.innerHTML = this.convertValueToString(cell);
					//							showModalWindow(this, 'Properties', content, 400, 300);
				}
			}
			
			// Disables any default behaviour for the double click
			mxEvent.consume (evt);
		};
		
		// Enables rubberband selection
//		new mxRubberband(graph);
		
		// Gets the default parent for inserting new cells. This
		// is normally the first child of the root (ie. layer 0).
//		var parent = graph.getDefaultParent();
						
		// Adds cells to the model in a single step
		configureStylesheet(graphFlowWindow);
//		loadGraph(graph);
//		$("#mainpanelE2").niceScroll({cursorborder: "1px solid #777"});
		if(parent.cellObj.modelId!=null)
		{
//		loadGetParentValue=true;
		templateNameObj.setValue(parent.cellObj.modelId);
		templateNameObj.fireEvent ('select');
		}
}
/**
 * 加载模板图形
 * @param instanceId
 */
function loadGraphTemplate(instanceId) {
	modelFlowWindow.beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("getTemplateXml.do?instanceID="+instanceId));
		var dec = new mxCodec(doc);
		dec.decode(doc.getDocumentElement(), modelFlowWindow);
	} finally {
		// Updates the display
		modelFlowWindow.endUpdate();
	}
	setStustSon();
//	graph.graphToCenter();
	/*//是否将父页面xml中的服务器信息填写至当前模板
	if(loadGetParentValue)
		{
		}*/
}


/**
 * TODO:设置状态
 */
function setStustSon()
{
	//遍历所有节点
	var root2 = graphFlowWindow.getModel ().getRoot ();
	var count = graphFlowWindow.model.getChildCount (root2);
	if(!parent.cellObj.ichildinstanceId)
	{//模板流程未产生,将节点状态全部置为未运行
		for (var i = 0; i < count; i++)
		{
			var cells = root2.getChildAt (i);
			var counts = cells.getChildCount ();
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				//			              alert(cellss.id);
					if(cellss.style=='beginStyle'||cellss.style=='endStyle'||cellss.value=='')
					{
					continue;
					}
				//未运行/已跳过
				setCellWarningFun(graphFlowWindow,cellss,'未运行/已跳过','images/mxgraphImages/skip.png');
			}
		}
	return;
	}
	 $.getJSON('getMonitorStepStatisticForSUS.do', {instanceId:parent.cellObj.ichildinstanceId, istate:0}, function(res){
		 for (var i = 0; i < count; i++)
			{
				var cells = root2.getChildAt (i);
				var counts = cells.getChildCount ();
				for (var j = 0; j < counts; j++)
				{
					var cellss = cells.getChildAt (j);
					//			              alert(cellss.id);
					if(cellss.style=='beginStyle'||cellss.style=='endStyle'||cellss.value=='')
						{
//						cellss.state = 0;
//						setCellWarningFun(graphFlowWindow,cellss,'完成','images/uthome/finished.gif');
						continue;
						}
					for(i=0;i<res.length;i++)
		         	{
						if(cellss.id==res[i].iserner)
							{
							
							if(res[i].stateColor=='red')
								{
								setCellWarningFun(graphFlowWindow,cellss,'错误','images/mxgraphImages/fail.gif');
								break;
								}
							else if(res[i].stateColor=='purple')
							{
								setCellWarningFun(graphFlowWindow,cellss,'超时','images/mxgraphImages/timeout.gif');
								break;
							}
							else if(res[i].stateColor=='orange')
							{
								setCellWarningFun(graphFlowWindow,cellss,'人工提醒','images/mxgraphImages/remind.png');
								break;
							}
							else if(res[i].stateColor=='blue')
							{
								setCellWarningFun(graphFlowWindow,cellss,'运行','images/mxgraphImages/run.gif');
								break;
							}
							else if(res[i].stateColor=='green')
							{
								setCellWarningFun(graphFlowWindow,cellss,'已完成','images/mxgraphImages/complete.png');
								break;
							}
							else if(res[i].stateColor=='gray')
							{
								//未运行/已跳过
								setCellWarningFun(graphFlowWindow,cellss,'未运行/已跳过','images/mxgraphImages/skip.png');
								break;
								
							}else 
							{
								//未运行/已跳过
								setCellWarningFun(graphFlowWindow,cellss,'未运行/已跳过','images/mxgraphImages/skip.png');
								break;
							}
							
							}
					 
		         	}
				}
			}
		 
	 });
	
}
/**
 * 双击图形中的模板节点触发事件
 * @param cellIn
 * @param formPanelTitle
 */
function cellClickFun(cellIn,formPanelTitle)
{
	cellObjFlowWindow=cellIn;
	formPanel2.setTitle(formPanelTitle);
}
/** 打开人工处理窗口*/
function openUTWindow(iflowid){
	  personExcute_window = Ext.create('Ext.window.Window', {
	    title : '人工处理',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : 600,
	    height : 400,
	    loader : {
	      url : "switchUT_SUS.do",
	      params : {
	        iflowid : iflowid
	      },
	      autoLoad : true,
	      autoDestroy : true,
	      scripts : true
	    }
	  }).show();
	}
/** 略过*/
function skipStep(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<略过>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'rmSkipStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						releaseMonitorStepStore.reload();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
/** 手工部署*/
function DeploymentOK(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<手工部署>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'deploymentOKStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						releaseMonitorStepStore.reload();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
/** 部署失败*/
function DeploymentFail(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<部署失败>操作，部署失败后整个部署将会被停止？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'deploymentFailStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						releaseMonitorStepStore.reload();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
/** 重试*/
function reTryStep(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<重新执行>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'reTryStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						releaseMonitorStepStore.reload();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
/** 终止*/
function killStep(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<步骤终止>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'killStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						releaseMonitorStepStore.reload();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
function secondsToString(seconds) {
    function numberEnding (number) {
      return (number > 1) ? '' : '';
    }
    var result = "";
    var numyears = Math.floor(seconds / 31536000);
    var numdays = Math.floor((seconds % 31536000) / 86400); 
    var numhours = Math.floor(((seconds % 31536000) % 86400) / 3600);
    var numminutes = Math.floor((((seconds % 31536000) % 86400) % 3600) / 60);
    var numseconds = (((seconds % 31536000) % 86400) % 3600) % 60;
    if(numyears){
      result += numyears + ' 年' + numberEnding(numyears) + ' ';
    }
    if(numdays){
      result += numdays + ' 天' + numberEnding(numdays) + ' ';
    }
    if(numhours){
      result += numhours + ' 小时' + numberEnding(numhours) + ' ';
    }
    if(numminutes){
      result += numminutes + ' 分' + numberEnding(numminutes) + ' ';
    }
    if(numseconds){
      result += numseconds + ' 秒' + numberEnding(numseconds)
    }
    if(result===''){
      result = '0 秒';
    }
    return result;
  }

