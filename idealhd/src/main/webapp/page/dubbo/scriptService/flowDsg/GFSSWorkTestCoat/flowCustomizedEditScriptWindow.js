/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/

Ext.onReady(function() {
    var scriptContent = "";
    var canEditScript = false;
    var scriptServiceId = -1;
    var scriptServiceLastId = -1;
    var stepNameObj;

    var allScriptServiceWin;

    stepNameObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '步骤名称',
        labelWidth: 73,
        labelAlign: 'right',
        width: 570
    });
    var shutdownCheckboxObj = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '关机维护',
        margin: '0 0 0 10',
        hidden: actTypeGFSSWorkTestCoat,
        inputValue: 1
    });

    var scriptVersionStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'onlyVersion'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceVersionList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var scriptVersionCbObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptVersion',
        width: 380,
        labelAlign: 'right',
        labelWidth: 83,
        queryMode: 'local',
        fieldLabel: '选择版本',
        displayField: 'onlyVersion',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择版本--',
        store: scriptVersionStore,
        listeners: {
            change: function(self, newValue, oldValue, eOpts) {
                if (actTypeGFSSWorkTestCoat) {
                    graphPanel.getLoader().load({
                        url: 'flowCustomizedInitScriptServiceGFSSVIEW.do',
                        scripts: true,
                        autoLoad: true,
                        params: {
                            iid: newValue,
                            flag: 0,
                            actionType: 'view',
                            isShowInWindow: 1
                        }
                    });
                } else {
                    Ext.Ajax.request({
                        url: 'getScritContentScriptService.do',
                        method: 'POST',
                        params: {
                            iid: newValue,
                            flag: 0
                        },
                        success: function(response, options) {
                            var content = Ext.decode(response.responseText).content;
                            scriptContentObj.setValue(content);
                        },
                        failure: function(result, request) {
                            scriptContentObj.setValue('');
                        }

                    });
                }
            }
        }
    });

    var bussCbObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        queryMode: 'local',
        width: '18%',
        labelWidth: 60,
        fieldLabel: '一级分类',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCbObj.clearValue();
                bussTypeCbObj.applyEmptyText();
                bussTypeCbObj.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCbObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        width: '18%',
        labelWidth: 60,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        },
        {
            "id": "sh",
            "name": "shell"
        },
        {
            "id": "bat",
            "name": "bat"
        },
        {
            "id": "py",
            "name": "python"
        },
        {
            "id": "perl",
            "name": "perl"
        },
        {
            "id": "sql",
            "name": "sql"
        }]
    });

    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        padding: '5',
        labelWidth: 60,
        queryMode: 'local',
        hidden: actTypeGFSSWorkTestCoat,
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        width: '17%',
        labelAlign: 'right'
    });

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        emptyText: '--请输入服务名称--',
        labelWidth: 60,
        padding: '5',
        width: '18%',
        labelAlign: 'right'

    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        emptyText: '--请输入脚本名称--',
        labelWidth: 60,
        hidden: actTypeGFSSWorkTestCoat,
        padding: '5',
        width: '18%',
        labelAlign: 'right'
    });
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
        cls: 'Common_Btn',
        hidden: (parent.actionTypeGFSSWorkTestCoat == 'view' || parent.actionTypeGFSSWorkTestCoat == 'exec' || parent.actionTypeGFSSWorkTestCoat == 'dbcheck' || parent.actionTypeGFSSWorkTestCoat == 'dbback'),
        handler: function() {
            saveFun();
        }
    });
    var backButton = Ext.create('Ext.Button', {
        text: '关闭',
        cls: 'Common_Btn',
        handler: function() {
            parent.cellObjGFSSWorkTestCoat = null;
            parent.configwindowFlowGFSSWorkTestCoat.close();
        }
    });

    var prevButton = Ext.create('Ext.Button', {
        text: '上一步',
        cls: 'Common_Btn',
        handler: function() {
            var res = getCellFun("before");
            if (res) {
                parent.cellObjGFSSWorkTestCoat = res;
                canEditScript = false;
                initFun();
            } else {
                Ext.Msg.alert('提示', "当前步骤为第一个步骤");
            }
        }
    });

    var nextButton = Ext.create('Ext.Button', {
        text: '下一步',
        cls: 'Common_Btn',
        handler: function() {
            var res = getCellFun("after");
            if (res) {
                parent.cellObjGFSSWorkTestCoat = res;
                canEditScript = false;
                initFun();
            } else {
                Ext.Msg.alert('提示', "当前步骤为最后一个步骤");
            }
        }
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'buss',
            type: 'string'
        },
        {
            name: 'bussType',
            type: 'string'
        },
        {
            name: 'bussId',
            type: 'int'
        },
        {
            name: 'bussTypeId',
            type: 'int'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'isflow',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'isshare',
            type: 'string'
        },
        {
            name: 'platForm',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        },
        {
            name: 'version',
            type: 'string'
        },
        {
            name: 'status',
            type: 'int'
        }]
    });

    var closedScriptServiceStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 15,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMySelfForOneList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
    	autoLoad: false,
    	autoDestroy: true,
    	pageSize: 15,
    	model: 'scriptServiceReleaseModel',
    	proxy: {
    		type: 'ajax',
    		url: 'scriptService/queryServiceExceptMySelf.do',
    		reader: {
    			type: 'json',
    			root: 'dataList',
    			totalProperty: 'total'
    		}
    	}
    });

    closedScriptServiceStore.on('beforeload', function(store, options) {
        var new_params = {
        		iid: scriptServiceLastId
        };

        Ext.apply(closedScriptServiceStore.proxy.extraParams, new_params);
    });
    closedScriptServiceStore.on('load', function() {
    	chosedScriptServiceGrid.getSelectionModel().select(0);
    });
    
    scriptServiceReleaseStore.on('beforeload',
    		function(store, options) {
    	var new_params = {
    			bussId: bussCbObj.getValue(),
    			bussTypeId: bussTypeCbObj.getValue(),
    			scriptName: scName.getValue(),
    			serviceName: sName.getValue(),
    			scriptType: scriptTypeParam.getValue(),
    			onlyScript: actTypeGFSSWorkTestCoat ? 0 : 1,
    			serviceId : scriptServiceId
    	};
    	
    	Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });

    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '服务主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        width: 200,
        flex: 1
    },
    {
        text: '脚本名称',
        dataIndex: 'scriptName',
        hidden: actTypeGFSSWorkTestCoat,
        width: 260,
        flex: 1
    },
    {
        text: '一级分类',
        dataIndex: 'buss',
        width: 200,
        flex: 1
    },
    {
        text: '二级分类',
        dataIndex: 'bussType',
        width: 250,
        flex: 1
    },
    {
        text: '脚本类型',
        dataIndex: 'scriptType',
        hidden: actTypeGFSSWorkTestCoat,
        width: 80,
        renderer: function(value, p, record, rowIndex) {
            var isflow = record.get('isflow');
            var backValue = "";
            if (value == "sh") {
                backValue = "shell";
            } else if (value == "perl") {
                backValue = "perl";
            } else if (value == "py") {
                backValue = "python";
            } else if (value == "bat") {
                backValue = "bat";
            } else if (value == "sql") {
                backValue = "sql";
            }
            if (isflow == '1') {
                backValue = "组合";
            }
            return backValue;
        }
    },
    {
        text: '适用平台',
        dataIndex: 'platForm',
        hidden: actTypeGFSSWorkTestCoat,
        width: 100
    },
    {
        text: '脚本状态',
        dataIndex: 'status',
        width: 100,
        renderer: function(value, p, record, rowIndex) {
            if (value == -1) {
                return '<font color="#F01024">草稿</font>';
            } else if (value == 1) {
                return '<font color="#0CBF47">已上线</font>';
            } else if (value == 2) {
                return '<font color="#FFA602">审核中</font>';
            } else if (value == 3) {
                return '<font color="#13B1F5">已共享</font>';
            } else if (value == 9) {
                return '<font color="">已共享未发布</font>';
            } else {
                return '<font color="#CCCCCC">未知</font>';
            }
        }
    }];

    var chosedPageBar = Ext.create('Ext.PagingToolbar', {
        store: closedScriptServiceStore,
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });
    
    var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: scriptServiceReleaseStore,
    	dock: 'bottom',
    	displayInfo: true,
    	emptyMsg: '找不到任何记录'
    });

    var chosedScriptServiceGrid = Ext.create('Ext.grid.Panel', {
        region: 'west',
        title: '已选'+(actTypeGFSSWorkTestCoat ? '作业': '脚本'),
        width: actTypeGFSSWorkTestCoat ? 480 : 600,
        autoScroll: true,
        margin: '5 0 5 0',
        store: closedScriptServiceStore,
        bbar: chosedPageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
            'select': function(self, record, index, eOpts) {
                scriptVersionCbObj.setRawValue('');
                scriptVersionStore.load({
                    params: {
                        serviceId: record.get('iid'),
                        flag: 0
                    },
                    callback: function(records, operation, success) {
                    	if (records.length > 0) {
                        	var flag = false;
                        	$.each(records, function(index, record){
                        		if(record.get('iid')==scriptServiceId) {
                        			flag = true;
                        			return false;
                        		}
                        	});

                        	if(flag) {
                        		scriptVersionCbObj.setValue(scriptServiceId);
                        	} else {
                        		scriptVersionCbObj.select(scriptVersionStore.getAt(0));
                        	}
                        }
                    }
                });
            }
        }
    });
    
    var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
		autoScroll: true,
		store: scriptServiceReleaseStore,
		bbar: pageBar,
		columnLines: true,
		columns: scriptServiceReleaseColumns,
		dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [sName, scName, scriptTypeParam, bussCbObj, bussTypeCbObj, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                    scriptContentObj.setValue('');
                    scriptVersionCbObj.setValue('');
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function() {
                    bussCbObj.setValue('');
                    bussTypeCbObj.setValue('');
                    scName.setValue('');
                    sName.setValue('');
                    scriptTypeParam.setValue('');
                }
            }]
        }]
    });

    var scriptContentObj = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '脚本内容',
        readOnly: true,
        height: contentPanel.getHeight() - 165,
        labelAlign: 'right',
        labelWidth: 90,
        hidden: actTypeGFSSWorkTestCoat,
        autoScroll: true
    });

    var showDButton = Ext.create('Ext.Button', {
        text: '显示详情',
        margin: '0 0 0 5',
        cls: 'Common_Btn',
        hidden: actTypeGFSSWorkTestCoat,
        handler: function() {
            var i = parseInt(scriptVersionCbObj.getValue());
            if (i > 0) {
                Ext.create('widget.window', {
                    title: '详细信息',
                    closable: true,
                    closeAction: 'destroy',
                    width: contentPanel.getWidth(),
                    minWidth: 350,
                    height: contentPanel.getHeight(),
                    draggable: false,
                    resizable: false,
                    modal: true,
                    loader: {
                        url: 'queryOneServiceForView.do',
                        params: {
                            iid: i,
                            flag: 0,
                            hideReturnBtn: 1
                        },
                        autoLoad: true,
                        scripts: true
                    }
                }).show();
            } else {
                Ext.Msg.alert('提示', '请选择版本!');
            }
        }
    });
    var graphPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        hidden: !actTypeGFSSWorkTestCoat,
        autoScroll: true,
        height: 640,
        loader: {}
    });
    var scriptContentPanel = Ext.create('Ext.form.Panel', {
        region: 'center',
        layout: 'form',
        border: false,
        autoScroll: true,
        items: [scriptContentObj, graphPanel],
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [scriptVersionCbObj, showDButton]
        }]
    });

    Ext.create('Ext.form.Panel', {
        border: false,
        layout: 'border',
        renderTo: "flowCustomizedEditScriptWindowDivGFSSWorkTestCoat",
        height: contentPanel.getHeight() - 40,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [stepNameObj, shutdownCheckboxObj, {
                xtype: 'button',
                text: '选择'+(actTypeGFSSWorkTestCoat ? '作业': '脚本'),
                cls: 'Common_Btn',
                margin: '0 0 0 30',
                handler: function() {
                	if (!allScriptServiceWin) {
                		allScriptServiceWin = Ext.create('Ext.window.Window', {
                            title: '选择'+(actTypeGFSSWorkTestCoat ? '作业': '脚本'),
                            autoScroll: true,
                            modal: true,
                            resizable: false,
                            closeAction: 'hide',
                            width: contentPanel.getWidth(),
                            height: 530,
                            layout: 'border',
                            items: [scriptServiceReleaseGrid],
                            buttonAlign: 'center',
                            dockedItems: [{
                                xtype: 'toolbar',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    text: "确定",
                                    cls: 'Common_Btn',
                                    margin: '6',
                                    handler: function() {
                                    	var ss = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                                    	if(ss.length==1) {
                                    		scriptServiceLastId = ss[0].data.iid;
                                    		closedScriptServiceStore.load();
                                    		this.up("window").close();
                                    	} else {
                                    		Ext.Msg.alert('提示', '请选择记录!');
                                    	}
                                    }
                                },
                                {
                                    xtype: "button",
                                    text: "关闭",
                                    cls: 'Common_Btn',
                                    handler: function() {
                                        this.up("window").close();
                                    }
                                }]
                            }]
                        });
                    }
                	allScriptServiceWin.show();
                	scriptServiceReleaseStore.load();
                }
            }]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'bottom',
            layout: { pack: 'center' },
            items: [saveButton, backButton, prevButton, nextButton]
        }],
        items: [chosedScriptServiceGrid, scriptContentPanel]
    });

    /** 初始化方法* */
    function initFun() {
        stepNameObj.setValue(parent.cellObjGFSSWorkTestCoat.value);
        shutdownCheckboxObj.setValue(parent.cellObjGFSSWorkTestCoat.isShutdown);
        if (parent.cellObjGFSSWorkTestCoat.scriptId != null && typeof(parent.cellObjGFSSWorkTestCoat.scriptId) != "undefined") {
            scriptServiceId = parseInt(parent.cellObjGFSSWorkTestCoat.scriptId);
            if (isNaN(scriptServiceId)) {
                scriptServiceId = -1;
            }
        }
        if (parent.cellObjGFSSWorkTestCoat.canEditScript != null && typeof(parent.cellObjGFSSWorkTestCoat.canEditScript) != "undefined") {
            canEditScript = parent.cellObjGFSSWorkTestCoat.canEditScript;
        }
        if (parent.cellObjGFSSWorkTestCoat.scriptContent != null && typeof(parent.cellObjGFSSWorkTestCoat.scriptContent) != "undefined") {
            scriptContent = parent.cellObjGFSSWorkTestCoat.scriptContent;
        }
        
        if(scriptServiceId>0) {
        	// 查找upperId
        	Ext.Ajax.request({
                url: 'getScritServiceLastId.do',
                method: 'POST',
                async: false,
                params: {
                    iid: scriptServiceId,
                },
                success: function(response, options) {
                    var success = Ext.decode(response.responseText).success;
                    if(success) {
                    	scriptServiceLastId = Ext.decode(response.responseText).scriptServiceLastId;
                    	closedScriptServiceStore.load();
                    }
                },
                failure: function(result, request) {
                }
            });
        }

    }
    function trim(t) {
        t = t.replace(/(^\s*)|(\s*$)/g, "");
        return t.replace(/(^ *)|( *$)/g, "");
    }
    function saveFun() {
        if (stepNameObj.getValue().trim() == '') {
            Ext.Msg.alert('提示', '步骤名称不允许为空!');
            return null;
        }
        if ('开始' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<开始>！');
            return null;
        }
        if ('结束' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<结束>！');
            return null;
        }

        var ssId = parseInt(scriptVersionCbObj.getValue());
        if (isNaN(ssId) || ssId <= 0) {
            if (scriptServiceId <= 0) {
                Ext.Msg.alert('提示', '请先选择脚本服务，然后选择版本!');
                return;
            }
        } else {
            scriptServiceId = ssId;
        }

        parent.cellObjGFSSWorkTestCoat.scriptId = scriptServiceId;
        parent.cellObjGFSSWorkTestCoat.canEditScript = canEditScript;
        parent.cellObjGFSSWorkTestCoat.value = stepNameObj.getValue();
        parent.cellObjGFSSWorkTestCoat.isShutdown = shutdownCheckboxObj.getValue();
        parent.callbackWindwGFSSWorkTestCoat();
        Ext.Msg.alert('提示', '当前步骤保存成功!');
    }

    initFun();

    /**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
    function getCellFun(inflag) {
        // 遍历所有节点
        var rootObj = modelGFSSWorkTestCoat.getRoot();
        var count = modelGFSSWorkTestCoat.getChildCount(rootObj);
        for (var i = 0; i < count; i++) {
            var cells = rootObj.getChildAt(i);
            var counts = cells.getChildCount();
            var beforeCell = null; // 上一个节点
            var afterCell = null; // 下一个节点
            var selfCell = null; // 自己
            for (var j = 0; j < counts; j++) {
                var cellss = cells.getChildAt(j);
                // 判断循环至的节点样式是否与传入的样式一致
                if (cellss.style == parent.cellObjGFSSWorkTestCoat.style) {
                    if (cellss == parent.cellObjGFSSWorkTestCoat) {
                        // 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
                        selfCell = parent.cellObjGFSSWorkTestCoat;
                    } else {
                        // 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
                        selfCell == null ? beforeCell = cellss: afterCell = cellss;
                    }
                    // 如果获取到了想要的节点，则跳出循环
                    if (selfCell != null && ((inflag == 'after' && afterCell != null) || (inflag == 'before' && beforeCell != null))) {
                        break;
                    }
                }
            }
            // 返回指定节点
            return inflag == 'after' ? afterCell: beforeCell;
        }
    }

});