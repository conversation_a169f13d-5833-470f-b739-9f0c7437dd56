<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSDBBACK=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSDBBACK=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSDBBACK=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSDBBACK=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSDBBACK=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSDBBACK=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>


<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSDBBACK='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSDBBACK='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSDBBACK=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSDBBACK=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSDBBACK='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSDBBACK='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSDBBACK = <%=request.getAttribute("fromType")%>;
var workItemidGFSSDBBACK = <%=request.getAttribute("workItemid")%>;
var fromGFSSDBBACK = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSDBBACK = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSDBBACK = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSDBBACK = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSDBBACK = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSDBBACK=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSDBBACK=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSDBBACK='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSDBBACK='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSDBBACK='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSDBBACK=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSDBBACK=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSDBBACK=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSDBBACK='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSDBBACK='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSDBBACK='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSDBBACK = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSDBBACK = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSDBBACK = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSDBBACK = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSDBBACK = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSDBBACK = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSDBBACK/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSDBBACK" style="width: 100%;height: 100%"></div>
</body>
</html>