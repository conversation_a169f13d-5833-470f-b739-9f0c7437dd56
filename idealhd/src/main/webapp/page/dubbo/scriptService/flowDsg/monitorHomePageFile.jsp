<%@page contentType="text/html; charset=utf-8"%>
<% 
	int cph=Integer.parseInt(request.getParameter("contentPanelHeight"));
	int wsh=Integer.parseInt(request.getParameter("windowScHeight"));
	int cph2=0;
	if(wsh==1024)
	{
	  cph2=cph-100; 
	}
	else
	{
	    cph2=cph-100;  
	}
	%>
<script type="text/javascript">
var wsh=<%=wsh%>;


</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/common/shellinfoerroper.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/common/shellinfofinish.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/common/shellinforuning.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/monitorHomePageFile.js"></script>
<div class="Change_Panel">
<div class="">
<table cellpadding="0" cellspacing="0" border="0" class="Change_Body">
	<tr>
		<td align="left" width="100%" height="100%">
			<div class="Change_Right2 Right_Common">
				<table cellpadding="0" cellspacing="0" border="0" height="100%"
					width="100%">
					<tr  style="height:<%=cph2%>px">
						<td width="225" valign="top">
							<div class="MH_Search">
								<input type="text" name="search" id="flowNameForFile" />
								<a href="javascript:void(0);" id="searchflowForFile"><img src="images/MH_Search.png"  align="absmiddle"/></a>
							</div>
							<div id="Change_Left_divFile" ></div>
						</td>
						<td>
							<div id="graphContainerFile" style="position:absolute;overflow:hidden;top:11px;left:249px;bottom:59px;right:15px;cursor:default;background-image:url(images/mxgraphImages/mxgraph_grid.png);border:1px solid #dcdddd;">
							</div>
							<div id="outlineContainerFile" style="position:absolute;overflow:hidden;top:26px;right:15px;width:200px;height:140px;background:transparent;border-style:solid;border-color:#9fa0a0;">
							</div>
						</td>
					</tr>
				</table>
			</div>
		</td>
	</tr>
	<tr>
		<td colspan="2"><div id="package_bottomFile"></div></td>
	</tr>
</table>
</div>
</div>