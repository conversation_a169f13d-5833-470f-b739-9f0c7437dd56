/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSAUDI;
var isSaveTemplateCkGFSSAUDI;
var isTimerTaskGFSSAUDI;
var execTime_smGFSSAUDI;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSAUDI
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
	
	isSaveTemplateCkGFSSAUDI = Ext.create('Ext.form.field.Checkbox', {
		checked : false,
		boxLabel: '是否保存为流程模板',
		margin:'3 0 0 0'
	});
  execTime_smGFSSAUDI = new Ext.form.TextField (
			{
			    fieldLabel : '执行时间',
			    labelWidth : 58,
			    labelAlign : 'right',
			    hidden:!ssTimerTaskSwitch || !isScriptConvertToFlowGFSSAUDI,
			    width : '20%'
			});
	isTimerTaskGFSSAUDI = Ext.create('Ext.form.field.Checkbox', {
		checked : false,
		boxLabel: '定时任务',
		hidden:!ssTimerTaskSwitch || !isScriptConvertToFlowGFSSAUDI,
		margin:'3 5 0 5'
	});
	
	
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-48,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSAUDI.do',
	        params: {
	        	flag:flagGFSSAUDI,
	        	actionType:actionTypeGFSSAUDI
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSAUDI.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	 var backButton = Ext.create('Ext.Button', {
		    text : '返回',
		    textAlign : 'center',
		    margin: '0 5 0 0',
//		    cls : 'Common_Btn',
		    handler : function() {
		        destroyRubbish();
		        contentPanel.getLoader().load({
		          url : 'scriptServicesTaskExec.do',
		          params: {
      				'filter_bussId': filter_bussIdGFSSAUDI,
					'filter_bussTypeId': filter_bussTypeIdGFSSAUDI,
					'filter_scriptName': filter_scriptNameGFSSAUDI,
					'filter_serviceName': filter_serviceNameGFSSAUDI,
					'filter_scriptType': filter_scriptTypeGFSSAUDI
      				}
		        });
		        if (Ext.isIE) {
		          CollectGarbage();
		        }
		      }
		  });
	 
	 var viewBasicInfoButton = Ext.create("Ext.Button", {
//			cls: 'Common_Btn',
		 margin:'0 5 0 0',
			text: "基本信息",
			disabled : false,
			handler:function(){
				Ext.create('Ext.window.Window', {
		            title: '基本信息',
		            autoScroll: true,
		            modal: true,
		            closeAction: 'destroy',
		            buttonAlign: 'center',
		            draggable: true,
		            resizable: false,
		            width: 500,
		            height: 328,
		            loader: {
		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
		            	params: {
		            		creatorFullName: creatorFullName,
			                bussName: bussName,
			                bussTypeName:bussTypeName,
			                funcDescText: funcDescText,
			                serviceName:serviceName
		            	},
		            	autoLoad: true
		            },
		            dockedItems: [{
		                xtype: 'toolbar',
		                border: false,
		                dock: 'bottom',
		                margin: '0 0 5 0',
		                layout: {pack: 'center'},
		                items: [{
		                    xtype: 'button',
		                    text: '关闭',
		                    cls: 'Common_Btn',
		                    handler: function() {
		                        this.up("window").close();
		                    }
		                }]
		            }]
		        }).show();
			}
		});
	
	var sendAudiButton = Ext.create("Ext.Button", {
		text: "提交",
//		cls : 'Common_Btn',
		margin:'0 5 0 0',
		disabled : false,
		handler:function(){
			sendAudiGFSSAUDI(this);
		}
	});
	
	var submitFromPanel = Ext.create('Ext.form.Panel', {
		width : '100%',
		layout: {
            type: 'hbox',
            padding:'5',
            align:'top'
        },
        items: [isSaveTemplateCkGFSSAUDI,isTimerTaskGFSSAUDI, execTime_smGFSSAUDI,{
                xtype:'tbspacer',
                flex:1
            },sendAudiButton,viewBasicInfoButton,backButton,{
                xtype:'tbspacer',
                flex:1
            },{
                xtype:'tbspacer',
                width:140
            }]
		/*dockedItems: [{
            xtype: 'toolbar',
            dock: 'bottom',
            items: [isSaveTemplateCkGFSSAUDI, '->', sendAudiButton,viewBasicInfoButton,backButton]
        }]*/
	});
	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSAUDI",
			width : '100%',
			height : contentPanel.getHeight (), 
			autoScroll: true,
			border : false,
			bodyPadding : '5 0 0 0',
			items : [mainTabs,submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSAUDI();

		function initGFSSAUDI()
		{
		}
		contentPanel.on('resize', function() {
		});
});

function sendAudiGFSSAUDI(btn) {
	var isReady = checkDataIsReadyGFSSAUDI();
	
	if(isReady) {
		Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var auditorStore_tap = Ext.create('Ext.data.Store', {
		    autoLoad: false,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getExecAuditorList.do?scriptLevel='+scriptFlowLevelForTaskAudiGFSSAUDI,
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
		    fieldLabel: "审核人",
		    store: auditorStore_tap,
		    queryMode: 'local',
		    width: 390,
		    displayField: 'fullName',
		    valueField: 'loginName',
		    labelWidth : 58,
		    editable : true,
			labelAlign : 'right'
		  });
		var taskName = new Ext.form.TextField({
			name: 'taskName',
			fieldLabel: '任务名称',
			emptyText: '',
			labelWidth : 58,
			labelAlign : 'right',
			value: taskNameForDbCheckGFSSAUDI,
			width: 390
		});
		
		Ext.create('Ext.window.Window', {
	  		title : '配置双人复核信息',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'destroy',
	  		width : 400,
	  		height : 150,
	  		items:[taskName, auditorComBox_tap],
	  		buttonAlign: 'center',
	  		buttons: [{ 
	  			xtype: "button", 
	  			text: "确定", 
	  			handler: function () {
	  				var self = this;
	  				var auditor = auditorComBox_tap.getValue();
	  				if(!auditor) {
	  					Ext.Msg.alert('提示', "没有选择审核人！");
	  					return;
	  				}
	  				
	  				var taskN = Ext.util.Format.trim(taskName.getValue());
	  				if(Ext.isEmpty(taskN)) {
	  					Ext.Msg.alert('提示', "任务名称不能为空！");
	  					return;
	  				}
	  				
	  				if (fucCheckLength(taskN) > 255) {
                        Ext.Msg.alert('提示', "任务名称不能超过255字符！");
                        return;
                    }
	  				if(ssTimerTaskSwitch && isScriptConvertToFlowGFSSAUDI) {
	  					//执行时间校验,勾选了就必须填
		  				var isdelay = isTimerTaskGFSSAUDI.getValue();
		  				var execT =  execTime_smGFSSAUDI.getRawValue();
		  				if(isdelay && ""==execT)
		  				{
		  					Ext.Msg.alert('提示', "没有填写执行时间！");
		  					return;
		  				}
	  				} else {
	  					var isdelay = false;
	  					var execT = "";
	  				}
	  				
	  				var startData = orgStartDataGFSSAUDI();
	  				var isSaveTemplate = isSaveTemplateCkGFSSAUDI.getValue();
	  				if(isSaveTemplate) {
	  					Ext.MessageBox.prompt('提示', '请输入流程模板名称:', function(btn, text, cfg){
	  				        if(btn=='ok') {
	  				        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
	  				        		var newMsg = '<span style="color:red">流程模板名称不能为空！</span>';
	  				                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	  				        	} else {
	  				        		var customName = Ext.util.Format.trim(text);
	  				        		Ext.Ajax.request({
	  				        	        url: 'checkCustomTemplateNameIsExist.do',
	  				        	        params: {
	  				        	            customName: customName,
	  				        	            flag: flagGFSSAUDI
	  				        	        },
	  				        	        method: 'POST',
	  				        	        success: function(response, options) {
	  				        	            if (!Ext.decode(response.responseText).success) {
	  				        	                var newMsg = '<span style="color:red">模板名已存在,请更换模板名！</span>';
	  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	  				        	            } else {
	  				        	            	Ext.Ajax.request({
		  				  	  						url : 'scriptFlowExecAuditing.do',
		  				  	  						method : 'POST',
		  				  	  						params : {
		  				  	  							iidForQuery : workItemidGFSSAUDI,
		  				  	  							serviceId: iidGFSSAUDI,
		  				  	  							auditor: auditor,
		  				  	  							taskName: taskN,
		  				  	  							startData: JSON.stringify(startData),
		  				  	  							scriptLevel: scriptFlowLevelForTaskAudiGFSSAUDI,
			  				  	  						isDelay:isdelay,
			  					  				    	execTime:execT
		  				  	  						},
		  				  	  						success: function(response, opts) {
		  				  	  							var success = Ext.decode(response.responseText).success;
		  				  	  							var message = Ext.decode(response.responseText).message;
		  				  	  							if(success) {
		  				  	  								Ext.MessageBox.alert("提示", "请求已经发送到审核人");
		  				  	  								self.up("window").close();
			  				  	  							Ext.Ajax.request({
			  		        	        	                    url: 'saveFlowCustomTemplate.do',
			  		        	        	                    method: 'POST',
			  		        	        	                    params: {
			  		        	        	                    	customName: customName,
			  		        	        	                    	serviceId: iidGFSSAUDI,
			  		        	        	                        data: JSON.stringify(startData),
			  		        	        	                        audiUserLoginName: auditor,
					  				  	  							taskName: taskN,
			  		        	        	                        flag: parent.flagGFSSAUDI
			  		        	        	                    },
			  		        	        	                    success: function(response, options) {
			  		        	        	                        var success1 = Ext.decode(response.responseText).success;
			  		        	        	                        var message1 = Ext.decode(response.responseText).message;
			  		        	        	                        if (success1) {
			  		        	        	                            Ext.MessageBox.show({
			  		        	        	                                title: "提示",
			  		        	        	                                msg: '请求已经发送到审核人<br>模板保存成功！',
			  		        	        	                                buttonText: {
			  		        	        	                                    yes: '确定'
			  		        	        	                                },
			  		        	        	                                buttons: Ext.Msg.YES
			  		        	        	                            });
			  		        	        	                        }
			  		        	        	                    },
			  		        	        	                    failure: function(result, request) {
			  		        	        	                        Ext.MessageBox.show({
			  		        	        	                            title: "提示",
			  		        	        	                            msg: "请求已经发送到审核人<br>模板保存失败",
			  		        	        	                            buttonText: {
			  		        	        	                                yes: '确定'
			  		        	        	                            },
			  		        	        	                            buttons: Ext.Msg.YES
			  		        	        	                        });
			  		        	        	                    }
	
			  		        	        	                });
		  				  	  							} else {
		  				  	  								Ext.MessageBox.alert("提示", message);
		  				  	  							}
		  				  	  						},
		  				  	  						failure: function(result, request) {
		  				  	  							secureFilterRs(result,"操作失败！");
		  				  	  						}
		  				  	  					});
	  				        	            }
	  				        	        },
	  				        	        failure: function(result, request) {}
	  				        	    });
	  				        	}
	  				        	
	  				        }
	  				        
	  				    });
	  				} else {
	  					Ext.Ajax.request({
	  						url : 'scriptFlowExecAuditing.do',
	  						method : 'POST',
	  						params : {
	  							iidForQuery : workItemidGFSSAUDI,
	  							serviceId: iidGFSSAUDI,
	  							auditor: auditor,
	  							taskName: taskN,
	  							startData: JSON.stringify(startData),
	  							scriptLevel: scriptFlowLevelForTaskAudiGFSSAUDI,
	  							isDelay:isdelay,
		  				    	execTime:execT
	  						},
	  						success: function(response, opts) {
	  							var success = Ext.decode(response.responseText).success;
	  							var message = Ext.decode(response.responseText).message;
	  							if(success) {
	  								Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	  								self.up("window").close();
	  								if(workItemidGFSSAUDI) {
	  									if(fromGFSSAUDI==1) {
	  										messageWindow1.close();
	  									} else {
	  										messageWindow.getLoader ().load (
	  												{
	  													url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
	  													autoLoad : true,
	  													scripts : true
	  												});
	  										messageWindow.setTitle ('待办事项');
	  									}
	  								}
	  							} else {
	  								Ext.MessageBox.alert("提示", message);
	  							}
	  						},
	  						failure: function(result, request) {
	  							secureFilterRs(result,"操作失败！");
	  						}
	  					});
	  				}
	  			}
	  		}]
	  	}).show();
		auditorStore_tap.load();
	}
}

