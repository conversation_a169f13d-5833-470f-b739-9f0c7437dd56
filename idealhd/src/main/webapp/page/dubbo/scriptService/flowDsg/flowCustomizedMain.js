/*******************************************************************************
 * 流程定制主tab页
 * 整个页面分为三个区域：1.图形展示区 2.操作区 3.基本信息区
 ******************************************************************************/
Ext.onReady(function() {
	if(Ext.isIE){
		CollectGarbage();
	}else{
		destroyRubbish();
	}
	// 将通过jsp跳转传递的变量全部进行转移和清理。要注意变量的值传递可以直接清理。变量如果传递的是引用，注意不要把真实值清理掉，清理引用即可。
	
	/**
	 * jspParms 参数说明
	 * 由于不确定变量作用域，无法确保后续变量变更位置，暂时保留原有变量，同时新增域变量备用
	 * 
	 * scope : 标识mx弹出层级，无定义默认为-1级，根级为0级，每次页面初始化进行自加。
	 * actionType : 区分是编辑还是测试，编辑为'edit',测试为'exec'
	 * flag : 用于区分是测试还是生产，0是测试,1是生产。目前认为作业管理是测试，任务管理是生产
	 */
	var jspParms = tempData;
	delete tempData;
	
	//初始化默认参数
	jspParms.menuId = jspParms.menuId == undefined?'':jspParms.menuId;
	jspParms.flag = jspParms.flag == undefined?0:jspParms.flag;
	jspParms.serviceId = jspParms.iid == null ? 0 : jspParms.iid; //整理一下iid，统一为serviceId，后续条件查询用。
	if(isNaN(jspParms.serviceId)) {
		Ext.Ajax.request({
	        url: 'queryIidByUuid.do',
	        method: 'POST',
	        async: false,
	        params: {
	            uuid: jspParms.serviceId
	        },
	        success: function(response, request) {
	        	jspParms.serviceId = Ext.decode(response.responseText).serviceId;
	        	jspParms.iid = Ext.decode(response.responseText).serviceId;
	        },
	        failure: function(result, request) {
	            secureFilterRs(result, "获取脚本IID失败！");
	        }
	    });
	}
	jspParms.actionType = jspParms.actionType == undefined?'edit':jspParms.actionType;
	jspParms.heightOffset = parseInt('0' + jspParms.heightOffset);
	jspParms.windowHeight = parseInt('0' + jspParms.windowHeight);
	jspParms.isShowInWindow = parseInt('0' + jspParms.isShowInWindow);
	jspParms.iscenceId = jspParms.iscenceId == undefined ?'':jspParms.iscenceId;
	
	if(jspParms.scope == undefined){
		jspParms.scope = 0;
	}else{
		if(jspParms.isShowInWindow == 1)
			jspParms.scope = parseInt(jspParms.scope) + 1;
	}
//	if(jspParms.isShowInWindow == 1)
//		jspParms.scope = jspParms.scope == undefined ? 0 : parseInt(jspParms.scope) + 1;
	if(jspParms.scope == 0)
	{
		jspParms.rootEditer = 'scriptEditor_' + jspParms.menuId;
	}
	if(jspParms.scope != 0 && jspParms.winDivID != undefined && jspParms.winDivID != '')
	{
		jspParms.divID = jspParms.winDivID;
	}
	if(jspParms.divID == undefined)
	{
		jspParms.divID = '';
	}
	
//	// 根据mxgraph的层级不同，传入的参数需要进行调整。
//	if(jspParms.scope!='0')
//	{
//		jspParms.menuId = jspParms.menuId + '_' + jspParms.cellid;
//	}
	
	// mxgraph 标识名称
	var editerName = 'scriptEditor_' + jspParms.divID;
	
	if(actStartInfo.hasOwnProperty(editerName)) {
		actStartInfo[editerName] = {};
	}
	if(!actStartInfo.hasOwnProperty(jspParms.rootEditer)) {
		actStartInfo[jspParms.rootEditer] = {};
	}
	
	if(jspParms.actionType == undefined)
		jspParms.actionType = 'edit';
	if(jspParms.actionType == 'edit')
		jspParms.needAgent = '1';
	else if(jspParms.actionType == 'exec')
		jspParms.needAgent = '2';
//	jspParms.needAgent = jspParms.actionType == 'edit'?'1':'2';
	
	var serviceInfo = initServiceInfo(); // 初始化脚本服务的基础信息数据，如果iid是>0 就从后台获取
	
	/** 基本信息区 **/
	//脚本分类
	var bussData = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'bsName' ],
		autoLoad : true,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'bsManager/getBsAll.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	var tmpname="一级分类";
	var tmptext="--请选择一级分类--";
	if(projectFlag==1){
		tmpname="脚本分类";
		tmptext="--请选择脚本分类--";
	}
	var bussCbOutSide = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussId',
		queryMode : 'local',
		fieldLabel : tmpname,
		padding : '0 5 0 0',
		displayField : 'bsName',
		valueField : 'iid',
		editable : false,
		emptyText : tmptext,
		store : bussData,
		listeners: {
			change: function() { // old is keyup
	            bussTypeCbOutSide.clearValue();
	            bussTypeCbOutSide.applyEmptyText();
	            bussTypeCbOutSide.getPicker().getSelectionModel().doMultiSelect([], false);
	            bussTypeData.load({params:{fk: this.value}});
	        }
		}
	});
	
	/** 工程类型下拉框* */
	var bussTypeData = Ext.create('Ext.data.Store', {
		fields : [ 'sysTypeId', 'sysType' ],
		autoLoad : false,
	    proxy : {
	        type : 'ajax',
	        url : 'bsManager/getBsTypeByFk.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	var tmpname1="二级分类";
	var tmptext1="--请选择二级分类--";
	if(projectFlag==1){
		tmpname1="操作类型";
		tmptext1="--请选择操作类型--";
	}
	var bussTypeCbOutSide = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussTypeId',
		padding : '0 5 0 0',
		queryMode : 'local',
		fieldLabel : tmpname1,
		displayField : 'sysType',
		valueField : 'sysTypeId',
		editable : false,
		emptyText : tmptext1,
		allowBlank :false,
		store : bussTypeData
	});
	
	// 分类如果加载
	bussData.on('load', function() { 
		 if (serviceInfo.bussId>0)
		{
			 bussCbOutSide.setValue(serviceInfo.bussId);  
			 bussCbOutSide.fireEvent ('select');
		}
	});
	
	bussTypeData.on('load', function(me, records, successful, eOpts) { 
		if (serviceInfo.bussTypeId>0)
		{
			$.each(records, function(index, record){
				if(parseInt(record.get('sysTypeId'))==serviceInfo.bussTypeId) {
					bussTypeCbOutSide.setValue(serviceInfo.bussTypeId);  
				}
			});
		}
	});
	var funcDescOutSide = Ext.create('Ext.form.field.TextArea', {
        name: 'funcDesc',
        fieldLabel: '功能说明',
        displayField: 'funcDesc',
        emptyText: '请输入功能说明...',
        labelWidth: 70,
        height: 136,
        value: serviceInfo.funcDescText,
        autoScroll: true
    });
	
	var instanceNameObjOutSide = Ext.create('Ext.form.field.Text', {
		name : 'serviceName',
		padding : '0 5 0 0',
		fieldLabel : '服务名称',
		value: serviceInfo.serviceName
	});
	
	var queryForm = Ext.create('Ext.form.Panel', {
        border: false,
        frame: true,
        width: '100%',
        layout: 'form',
        fieldDefaults: {
            labelWidth: 60,
            labelAlign: "left",
            width: '100%'
        },
        items: [bussCbOutSide, bussTypeCbOutSide, instanceNameObjOutSide, funcDescOutSide]
    });
	
	
	var saveWinObjOutSide = null;

	function winclose(win){
		var closeValue = win.closeValue;
		console.log(closeValue);
//		submitFromPanel.closeValue = closeValue;
		submitFromPanel.initOperBtn(closeValue);
	}
	if (!saveWinObjOutSide) {
		saveWinObjOutSide = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.saveWin', {
			serviceInfo : serviceInfo,
			jspParms : jspParms,
			editerName : editerName,
			fitRadioGroup : fitRadioGroup,
			closeValue : -1,
			listeners   : {'close':{fn: winclose}},
		});
    }
	

	
	function fitRadioGroup()
	{
		if(saveWinObjOutSide.reqText=='switch')
		{
			submitFromPanel.closeValue = 1;
			submitFromPanel.items.get(0).setValue({ needAgent: '1'} );
		}
	}
	

	
	function submitFn(btn){

		var isReady = checkDataIsReadyGFSSFLOWCUSTOMEDITPRODUCTFROMTM();
		
		if(isReady) {
			Ext.define('AuditorModel', {
			    extend: 'Ext.data.Model',
			    fields : [ {
			      name : 'loginName',
			      type : 'string'
			    }, {
			      name : 'fullName',
			      type : 'string'
			    }]
			  });
			
			var auditorStore_tap = Ext.create('Ext.data.Store', {
			    autoLoad: false,
			    model: 'AuditorModel',
			    proxy: {
			      type: 'ajax',
			      url: 'getExecAuditorList.do?scriptLevel='+scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
			      reader: {
			        type: 'json',
			        root: 'dataList'
			      }
			    }
			  });
			
			var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
			    editable: false,
			    fieldLabel: "审核人",
			    store: auditorStore_tap,
			    queryMode: 'local',
			    width: 390,
			    displayField: 'fullName',
			    valueField: 'loginName',
			    labelWidth : 58,
				labelAlign : 'right'
			  });
			var taskName = new Ext.form.TextField({
				name: 'taskName',
				fieldLabel: '任务名称',
				emptyText: '',
				labelWidth : 58,
				labelAlign : 'right',
				value: taskNameForDbCheckGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
				width: 390
			});
			
			Ext.create('Ext.window.Window', {
		  		title : '配置双人复核信息',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		closeAction : 'destroy',
		  		width : 400,
		  		height : 150,
		  		items:[taskName, auditorComBox_tap],
		  		buttonAlign: 'center',
		  		buttons: [{ 
		  			xtype: "button", 
		  			text: "确定", 
		  			handler: function () {
		  				var self = this;
		  				var auditor = auditorComBox_tap.getValue();
		  				if(!auditor) {
		  					Ext.Msg.alert('提示', "没有选择审核人！");
		  					return;
		  				}
		  				
		  				var taskN = Ext.util.Format.trim(taskName.getValue());
		  				if(Ext.isEmpty(taskN)) {
		  					Ext.Msg.alert('提示', "任务名称不能为空！");
		  					return;
		  				}
		  				
		  				if (fucCheckLength(taskN) > 255) {
	                        Ext.Msg.alert('提示', "任务名称不能超过255字符！");
	                        return;
	                    }
		  				
		  				var startData = orgStartDataGFSSFLOWCUSTOMEDITPRODUCTFROMTM();
		  				Ext.Ajax.request({
		  				    url : 'scriptFlowExecAuditing.do',
		  				    method : 'POST',
		  				    params : {
		  				    	iidForQuery : workItemidGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
		  				    	serviceId: iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
		  				    	auditor: auditor,
		  				    	taskName: taskN,
		  				    	startData: startData,
		  				    	scriptLevel: scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCTFROMTM
		  				    },
		  				    success: function(response, opts) {
		  				        var success = Ext.decode(response.responseText).success;
		  				        var message = Ext.decode(response.responseText).message;
		  				        if(success) {
		  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
		  				        	self.up("window").close();
		  				        	if(workItemidGFSSFLOWCUSTOMEDITPRODUCTFROMTM) {
		  				        		if(fromGFSSFLOWCUSTOMEDITPRODUCTFROMTM==1) {
		  						    		messageWindow1.close();
		  						    	} else {
		  									messageWindow.getLoader ().load (
		  											{
		  												url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
		  												autoLoad : true,
		  												scripts : true
		  											});
		  									messageWindow.setTitle ('待办事项');
		  								}
		  				        	}
		  				        } else {
		  				        	Ext.MessageBox.alert("提示", message);
		  				        }
		  				    },
		  				    failure: function(result, request) {
		  				    	secureFilterRs(result,"操作失败！");
		  				    }
		  			    });
		  			}
		  		}]
		  	}).show();
			auditorStore_tap.load();
		}
	}
	
	function startFn(){
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("请确认", "是否确定启动该模板", function(id){
			if(id=='yes') {
				if(actionTypeGFSSFLOWCUSTOMEDITTEST=='exec') {
					startCustomTemplateGFSSFLOWCUSTOMEDITTEST();
				} else {
					var isOk = false;
					Ext.Ajax.request({
	        			url: 'validFlowCustomTemplateData.do',
	        			method: 'POST',
	        			async: false,
	        			params: {
	        				iid: customIdGFSSFLOWCUSTOMEDITTEST,
	        				flag: jspParms.flag
	        			},
	        			success: function(response, options) {
	        				var success = Ext.decode(response.responseText).success;
	        				if(success) {
	        					isOk = true;
	        				} else {
	        					var errorMessage = Ext.decode(response.responseText).message;
	        					Ext.Msg.alert('提示', errorMessage);
	        				}
	        			},
	        			failure: function(result, request) {
	        				Ext.Msg.alert('提示', message+'<br>校验模板启动数据信息失败！');
	        			}
	        		});
					
					if(!isOk) {
						return;
					}
					Ext.Ajax.request({
        				url: 'startFlowCustomTemplate.do',
        				method: 'POST',
        				params: {
        					iid: customIdGFSSFLOWCUSTOMEDITTEST,
        					flag: jspParms.flag
        				},
        				success: function(response, options) {
        					var success = Ext.decode(response.responseText).success;
        					var message = Ext.decode(response.responseText).message;
        					Ext.Msg.alert('提示', message);
        				},
        				failure: function(result, request) {
        					Ext.Msg.alert('提示', '模板启动失败！');
        				}
        			});
				}
			}
		});
	
	}
	
	function baseInfoFn (){
		Ext.create('Ext.window.Window', {
            title: '基本信息',
            autoScroll: true,
            modal: true,
            closeAction: 'destroy',
            buttonAlign: 'center',
            draggable: true,
            resizable: false,
            width: 500,
            height: 328,
            loader: {
            	url: 'page/dubbo/fragment/_basicInfo.jsp',
            	params: {
            		creatorFullName: serviceInfo.creatorFullName,
	                bussName: serviceInfo.bussName,
	                bussTypeName: serviceInfo.bussTypeName,
	                funcDescText: serviceInfo.funcDescText,
	                serviceName: serviceInfo.serviceName
            	},
            	autoLoad: true
            },
            dockedItems: [{
                xtype: 'toolbar',
                border: false,
                dock: 'bottom',
                margin: '0 0 5 0',
                layout: {pack: 'center'},
                items: [{
                    xtype: 'button',
                    text: '关闭',
                    cls: 'Common_Btn',
                    handler: function() {
                        this.up("window").close();
                    }
                }]
            }]
        }).show();
	}
	
	
	var submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.submitFormPanel';
	var submitPanelHeight = 40;
	if(jspParms.submitType=='dc')//作业发布审核页面
	{
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.releaseCheck';
		submitPanelHeight = 150;
	}else if(jspParms.submitType=='dk')//作业发布打回
	{
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.releaseBack';
		submitPanelHeight = 150;
	}else if(jspParms.submitType=='ea'||jspParms.submitType=='ms')//任务申请、系统权限任务发起     
	{
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.audiFormPanel';
		iworkItemid = 0; // cron表达式公共方法用的参数
	}else if(jspParms.submitType=='plan'){//新增应急预案场景
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.planFormPanel';
	}
	else if(jspParms.submitType=='tc')//任务执行双人复核审批
	{
		submitPanelHeight = 150;
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.taskCheck';
	}else if(jspParms.submitType=='tb')//任务执行双人复核打回
	{
		submitPanelHeight = 150;
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.taskBack';
		iworkItemid = jspParms.iworkItemid; // cron表达式公共方法用的参数
	}else if(jspParms.submitType=='warnSure')//任务执行双人复核打回
	{
		submitPanelName = 'page.dubbo.scriptService.flowDsg.scriptGraph.warnSure';
	}
	var submitFromPanel;
	if(projectFlag==1){
		submitFromPanel = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.submitFormPanel', {
			name : 'submitFromPanel',
			width : '100%',
			layout: {
				type: 'hbox',
				padding:'5',
				align:'top'
			},
			defaults : {margin:'0 5 0 0'},
			jspParms : jspParms,
			editerName : editerName,
			saveButtonFunc : saveButtonFunc
			});
	}else{
		submitFromPanel = Ext.create(submitPanelName, {
			name : 'submitFromPanel',
			width : '100%',
			layout: {
				type: 'hbox',
				padding:'5',
				align:'top'
			},
			height : submitPanelHeight,
			defaults : { margin : '0 5 0 0' },
			jspParms : jspParms,
			editerName : editerName,
			baseInfoFn : baseInfoFn,
			serviceInfo : serviceInfo,
			saveButtonFunc : saveButtonFunc,
			resetMainPanel : resetMainPanel,
			isOnlyShow : isOnlyShow,
			iworkItemid : iworkItemid,
			studioValues : {}
		});
	}
	var saveWinObjOutSide = null;
	if (!saveWinObjOutSide) {
		saveWinObjOutSide = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.saveWin', {
			serviceInfo : serviceInfo,
			jspParms : jspParms,
			editerName : editerName,
			fitRadioGroup : fitRadioGroup,
			saveCustomTemplateForExec : saveCustomTemplateForExec,
			closeValue : submitFromPanel.studioValues.closeValue,
			listeners   : {'close':{fn: winclose}},
		});
    }
	
	/** 图形展示区 **/
	var mainPanelId = 'flowCustomizedMainDiv_id_' + jspParms.divID ;
	
	jspParms.parentId = mainPanelId;
	if(jspParms.scope == 0)
	{
		jspParms.rootId = jspParms.parentId;
	}
	var mainPanelHeight = contentPanel.getHeight() - modelHeigth;
	if(jspParms.isShowInWindow==1)
	{
		mainPanelHeight = jspParms.height;
	}
	if(jspParms.windowHeight > 0 ){
		mainPanelHeight = jspParms.windowHeight - modelHeigth; 
	}
	
	
	var mainTabsHeight = mainPanelHeight - jspParms.heightOffset;
	if(jspParms.submitHide != 'true')
	{
		mainTabsHeight = mainTabsHeight - submitFromPanel.height;
	}else{
		submitFromPanel.hide();
	}
	
	jspParms.height = mainTabsHeight;
	if(jspParms.isShowInWindow==1&&jspParms.submitHide != 'true')
	{
		jspParms.height = mainTabsHeight - 40 ; // 如果是弹出窗，还要将window的title高度去掉。没有在api中找到title的盖度属性，暂定高度为40。
	}
	
	jspParms.editerName = editerName;
	var mainTabs = Ext.create('Ext.panel.Panel', {
		width : '100%',
		id : 'mainTabs_id_' + jspParms.divID,
		border : false,
		height : mainTabsHeight,
		autoScroll: true,
		loader : {
	        url : 'flowCustomizedImgScriptService.do',
	        params: jspParms,
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	    }
	});
		
	// 4.1 主Panel
    var mainPanel = Ext.create('Ext.panel.Panel', {
    	id : mainPanelId,
		renderTo : "flowCustomizedMainDiv_" + jspParms.divID,
		width : '100%',
		height : mainPanelHeight, 
		border : false,
		items : [ mainTabs,submitFromPanel]
	});
    mainTabs.loader.load();
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	function resetMainPanel(){
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	}
	
	function saveButtonFunc (a,b,c,d,e) {
		
		// 如果是编辑场景，保存按钮进行提示判断并保存
		if(jspParms.actionType=='edit'||jspParms.actionType=='create')
		{
			saveWinObjOutSide.reqText = a;
			saveWinObjOutSide.show();
		}else if(jspParms.actionType=='model')// 如果是模板场景，弹出模板保存
		{
			checkAndSaveTemplate();
		}else if(a == 'test')
		{
			saveCustomTemplateForExec();
		}
	}

	function saveCustomTemplateForExec() {
		var allStartParams = actStartInfo[jspParms.rootEditer] ;
		
	    var customName = submitFromPanel.items.get(2).getValue();
	    if (customName.trim() == '')
		{
			Ext.Msg.alert ('提示', '请填写模板名称!');
			return null;
		}
	    
	    if(!checkTemplate(jspParms.serviceId, allStartParams)) {
	    	return;
	    }

	    saveTemplate(allStartParams, customName, jspParms.customId);
	}

	
	
	function checkAndSaveTemplate(){
		var allStartParams = actStartInfo[jspParms.rootEditer] ;
		var customName = submitFromPanel.items.get(2).getValue();
	    if (customName.trim() == '') {
			Ext.Msg.alert ('提示', '请填写模板名称!');
			return null;
		}
		
		if(!checkTemplate(jspParms.serviceId, allStartParams))
		{
			return;
		}
		saveTemplate(allStartParams,customName,jspParms.customId);
		
	}
	
	function checkTemplate(serviceId,allStartParams)
	{
		var isJobConfigOk = true;
		Ext.Ajax.request({
			url :'checkJobConfigIsOK.do',
			method: 'POST',
			async: false,
			params:{
				serviceId: serviceId,
				data:JSON.stringify(allStartParams),
				flag: 0
			},
			success: function ( response, options) 
			{
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (!success) {
					isJobConfigOk = false;
					Ext.Msg.alert('提示', message);
				}
			},
			failure: function ( result, request){
				isJobConfigOk = false;
				Ext.Msg.alert('提示', "检查作业配置出现问题！");
			}
		});
		
		return isJobConfigOk;
	}
	
	function saveFlowCustomTemplate(customId, customName, serviceId, data, flag,ifrom)
	{
        Ext.Ajax.request({
            url: 'saveFlowCustomTemplate.do',
            method: 'POST',
            params: {
            	iid: customId,
            	customName: customName,
            	serviceId: serviceId,
                data: data,
                flag: flag
            },
            success: function(response, options) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                
                if (success) {
//                    	var customId = Ext.decode(response.responseText).customId;
                	if(customId==undefined || customId ==''){
                		Ext.Msg.alert('提示', "模板保存成功！<br>如需修改，请使用流程模板管理功能！");
                		destroyRubbish();
                		contentPanel.getLoader().load({
                			url: ifrom
                		});
                		if (Ext.isIE) {
                			CollectGarbage();
                		}
                	}else{
                		Ext.Msg.alert('提示', "模板保存成功！");
                	}
                }else{
                	Ext.Msg.alert('提示', "模板保存失败！");
                }
            },
            failure: function(result, request) {
            	Ext.Msg.alert('提示', "保存模板失败！");
            }
        });
	}
	
	function checkCustomTemplateNameIsExist(customName, flag,customId){
		var ret = false;
	    Ext.Ajax.request({
	        url: 'checkCustomTemplateNameIsExist.do',
	        params: {
	            customName: customName,
	            flag: flag,
	            iid:customId
	        },
	        async: false,
	        method: 'POST',
	        success: function(response, options) {
	        	ret =  !(Ext.decode(response.responseText).success);
	        },
	        failure: function(result, request) {
	        	Ext.Msg.alert('提示', "检查模板名称是否存在出现错误！");
	        }
	    });
	    return ret;
	}
	
	function saveTemplate(allStartParams, customName, customId)
	{
		if(checkCustomTemplateNameIsExist(customName, jspParms.flag,customId))
		{
			Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
		}else{
			saveFlowCustomTemplate(customId,customName,jspParms.serviceId,JSON.stringify(allStartParams),jspParms.flag,jspParms.ifrom);
		}
	}
	
	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.getHeight());
		
	});
	
	
	/**
	 * 初始化service对象，如果是编辑已有服务，则获取已有服务对象相关信息
	 */
	function initServiceInfo(){
		var serviceInfo = {}; // 定义基本
		serviceInfo.bussId = 0;
	    serviceInfo.bussTypeId = 0;
	    serviceInfo.bussName = '';
	    serviceInfo.bussTypeName = '';
	    serviceInfo.funcDescText = '';
	    serviceInfo.serviceName = '';
	    serviceInfo.creatorFullName = '';
	    
	    if(jspParms.serviceId>0) { // 如果是回显编辑，需要获取一下服务的基本信息
		    Ext.Ajax.request({
		        url: 'scriptService/queryOneService.do',
		        params: {
		            iid: jspParms.serviceId
		        },
		        method: 'POST',
		        async: false,
		        success: function(response, options) {
		            var data = Ext.decode(response.responseText);
		            if (data.success) {
		            	serviceInfo.bussId = parseInt(data.sysName);
		            	serviceInfo.bussTypeId = parseInt(data.bussName);
		            	serviceInfo.bussName = data.bussN;
		            	serviceInfo.bussTypeName = data.bussT;
		            	serviceInfo.funcDescText = data.funcDesc;
		            	jspParms.scriptUuid = data.scriptuuid;
		            	serviceInfo.serviceName = data.serviceName;
		            	serviceInfo.creatorFullName = data.fullName;
		            }
		        },
		        failure: function(result, request) {}
		    });
		}
	    return serviceInfo;
	}
	
	if(jspParms.isShowInWindow==1&&jspParms.actionType=='view')
	{
		// 暂不确定该修正影响范围，需要集成测试进行进一步确认。
//			if(GRAPHS[jspParms.rootEditer] == undefined)
//			{
		if(jspParms.submitHide != 'true')
			mainTabs.height = mainTabs.height - 40;
		submitFromPanel.initOperBtn(10);
//			}else{
//				submitFromPanel.initOperBtn(-1);
//				submitFromPanel.height = 0;
//			}
	}
	
	if(jspParms.customId!=undefined&&jspParms.customId!=''&&jspParms.actionType=='edit')
	{
		submitFromPanel.initOperBtn(7);
		submitFromPanel.height = 0;
	}
	
	if(jspParms.btnCode!=undefined)
	{
		submitFromPanel.initOperBtn(jspParms.btnCode);
	}
});
