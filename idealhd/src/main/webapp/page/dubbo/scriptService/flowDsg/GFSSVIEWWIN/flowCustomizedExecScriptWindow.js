/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/
Ext.onReady(function() {
	var readOnly = readOnlyGFSSVIEWWIN=='1';
	var scriptNameObj;
    var scriptContentObj;
    var stepNameObj;
    var paramObj;
    var startUserObj;
    var resourceGroupObj;
    var chosedServList;
    var agent_store;
    var cellScriptType = '';
    var chosedResGroups = new Array();
    var chosedAgentIds = new Array();
    var editingChosedAgentIds = new Array();

    var globalParams = {};
    var globalConfigParams = {};

    var globalConfigStartUser = {};

    var chosedAgentWinForSee;
    var agentParamsWin;
    var upldWin;
    var upLoadformPane = '';
    var scriptServiceId = parent.cellObjGFSSVIEWWIN.scriptId;

    stepNameObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '步骤名称',
        readOnly: true
    });
    var shutdownCheckboxObj = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '关机维护',
        inputValue: 1,
        width: 120,
        margin: '0 0 0 60'
    });
    paramObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '执行参数',
        hidden: true,
        readOnly: true
    });
    startUserObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '启动用户',
        readOnly: readOnly
    });
    scriptNameObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '服务名称',
        readOnly: true
    });

    scriptContentObj = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '脚本内容',
        height: 300,
        readOnly: true,
        autoScroll: true
    });

    var prevButton = Ext.create('Ext.Button', {
        text: '上一步',
        handler: function() {
            var res = getCellFun("before");
            if (res) {
                parent.cellObjGFSSVIEWWIN = res;
                initFun();
                resourceGroupStore.load();
            } else {
                Ext.Msg.alert('提示', "当前步骤为第一个步骤");
            }
        }
    });

    var nextButton = Ext.create('Ext.Button', {
        text: '下一步',
        handler: function() {
            var res = getCellFun("after");
            if (res) {
                parent.cellObjGFSSVIEWWIN = res;
                initFun();
                resourceGroupStore.load();
            } else {
                Ext.Msg.alert('提示', "当前步骤为最后一个步骤");
            }
        }
    });

    var viewChosedScriptButton = Ext.create('Ext.Button', {
        text: '查看脚本详情',
        handler: function() {
            if (scriptServiceId > 0) {
                Ext.create('widget.window', {
                    title: '详细信息',
                    closable: true,
                    closeAction: 'destroy',
                    width: contentPanel.getWidth(),
                    minWidth: 350,
                    height: contentPanel.getHeight(),
                    draggable: true,
                    resizable: false,
                    modal: true,
                    loader: {
                        url: 'queryOneServiceForView.do',
                        params: {
                            iid: scriptServiceId,
                            flag: 0,
                            hideReturnBtn: 1
                        },
                        autoLoad: true,
                        scripts: true
                    }
                }).show();
            } else {
                Ext.Msg.alert('提示', "没有脚本服务！");
            }
        }
    });

    var formPanel = Ext.create('Ext.form.Panel', {
        border: false,
        fieldDefaults: {
            labelAlign: 'right',
            labelWidth: 80,
            width: '24%'
        },
        layout: 'hbox',
        padding: '8 0 0 0',
        items: [stepNameObj, scriptNameObj, paramObj, startUserObj, shutdownCheckboxObj]
    });

    /** 树数据Model* */
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    paramStore.on('beforeload',
    function(store, options) {
        var new_params = {
            scriptId: scriptServiceId
        };
        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    paramStore.on('load',
    function(me, records, successful, eOpts) {
        $.each(records,
        function(index, record) {
            if (globalParams.hasOwnProperty(record.get('iid'))) {
                record.set('paramValue', globalParams[record.get('iid')]);
            }
        });
    });

    var configParamStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    configParamStore.on('beforeload',
    function(store, options) {
        var new_params = {
            scriptId: scriptServiceId
        };
        Ext.apply(configParamStore.proxy.extraParams, new_params);
    });

    /** 树列表columns* */
    var paramColumns = [{
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 100,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '默认参数值',
        dataIndex: 'paramDefaultValue',
        width: 100,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '参数值',
        dataIndex: 'paramValue',
        width: 100,
        editor: {
            allowBlank: true
        },
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
            return value;
        }
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var cellEditing_for_config_params = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var paramGrid = Ext.create('Ext.grid.Panel', {
        width: '35%',
        height: contentPanel.getHeight() - 130,
        store: paramStore,
        margin: '0 0 0 5',
        title: '参数信息',
        border: true,
        columnLines: true,
        columns: paramColumns,
        plugins: [cellEditing],
        collapsible: false
    });
    var paramGrid_for_config_params = Ext.create('Ext.grid.Panel', {
        width: '40%',
        height: contentPanel.getHeight() - 250,
        store: configParamStore,
        margin: '0 0 0 10',
        title: '参数信息',
        border: true,
        columnLines: true,
        columns: paramColumns,
        plugins: [cellEditing_for_config_params],
        collapsible: false
    });

    paramGrid_for_config_params.on('edit',
    function(editor, e) {
        var ipRecord = agent_grid_chosed_for_config_param.getSelectionModel().getSelection()[0];
        if (ipRecord) {
            var configParams = {};
            for (var i = 0; i < e.grid.getStore().getCount(); i++) {
                var record = e.grid.getStore().getAt(i);
                var iid = record.get('iid');
                var paramType = record.get('paramType');
                var paramValue = record.get('paramValue');

                if (paramType == 'int' && paramValue) {
                    if (!checkIsInteger(paramValue)) {
                        Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                        return;
                    }
                }
                if (paramType == 'float' && paramValue) {
                    if (!checkIsDouble(paramValue)) {
                        Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                        return;
                    }
                }
                if (paramValue.indexOf('"') >= 0) {
                    if (cellScriptType == 'bat') {
                        Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                        return;
                    }
                }
                if (!Ext.isEmpty(Ext.util.Format.trim(paramValue))) {
                    configParams[iid] = paramValue;
                }
            }
            globalConfigParams[ipRecord.get('iid')] = configParams; //绑定
        }
    });

    Ext.define('resourceGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        }]
    });

    var resourceGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'resourceGroupModel',
        proxy: {
            type: 'ajax',
            url: 'getResGroupForScriptService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    resourceGroupStore.on('load',
    function() {
        var ins_rec = Ext.create('resourceGroupModel', {
            id: '-1',
            name: '未分组',
            description: ''
        });
        resourceGroupStore.insert(0, ins_rec);
    });
    resourceGroupObj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '资源组',
        emptyText : '--请选择资源组--',
        multiSelect: true,
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.9%',
        store: resourceGroupStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable: false,
        mode: 'local',
        listeners: {
            select: function(combo, records, eOpts) {
                if (records) {
                    chosedResGroups = new Array();
                    for (var i = 0; i < records.length; i++) {
                        chosedResGroups.push(records[i].data.id);
                    }
                    agent_store.load();
                } else {
                    agent_store.load();
                }

            }
        }
    });
    var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '25.9%',
		labelAlign : 'right'
	});
	
    //begin
//    var app_name = new Ext.form.TextField({
//        name: 'appname',
//        fieldLabel: '应用名称',
//        displayField: 'appname',
//        emptyText: '--请输入应用名称--',
//        labelWidth: 70,
//        labelAlign: 'right',
//        width: '25%'
//    });
	Ext.define('appNameModel', {
    	extend: 'Ext.data.Model',
    	fields : [ {
    		name : 'appName',
    		type : 'string'
    	}]
    });
    var app_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'appNameModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentAppNameList.do?envType='+flagGFSSVIEWWIN,
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var app_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
		name : 'appname',
	    fieldLabel: "应用名称",
	    emptyText : '--请选择应用名称--',
	    store: app_name_store,
	    queryMode: 'local',
	    width: "25%",
	    displayField: 'appName',
	    valueField: 'appName',
	    labelWidth : 70,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
    var agent_ip = new Ext.form.TextField({
        name: 'agentip',
        fieldLabel: 'AgentIp',
        displayField: 'agentip',
        emptyText: '--请输入Agent IP--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.8%'
    });
    var host_name = new Ext.form.TextField({
        name: 'hostname',
        fieldLabel: '主机名称',
        displayField: 'hostname',
        emptyText: '--请输入主机名称--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%'
    });
    Ext.define('sysNameModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'sysName',
            type: 'string'
        }]
    });

    var sys_name_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'sysNameModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentSysNameList.do?envType='+flagGFSSVIEWWIN,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var sys_name = Ext.create('Ext.form.ComboBox', {
        name: 'sysname',
        fieldLabel: "系统名称",
        emptyText : '--请选择系统名称--',
        store: sys_name_store,
        queryMode: 'local',
        width: "25%",
        displayField: 'sysName',
        valueField: 'sysName',
        labelWidth: 70,
        labelAlign: 'right',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var os_type = new Ext.form.TextField({
        name: 'ostype',
        fieldLabel: '系统类型',
        displayField: 'ostype',
        emptyText: '--请输入系统类型--',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25.5%'
    });

    Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'appName',
            type: 'string'
        },
        {
            name: 'hostName',
            type: 'string'
        },
        {
            name: 'osType',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string',
            defaultValue: 1500
        },
        {
            name: 'agentDesc',
            type: 'string'
        },
        {
            name: 'resGroup',
            type: 'string'
        },
        {
            name: 'agentState',
            type: 'int'
        },
        {
            name: 'agentParam',
            type: 'string'
        },
        {
            name: 'agentStartUser',
            type: 'string'
        }]
    });
    agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        groupField: 'resGroup',
        //确定哪一项分组  
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });

    agent_store.on('beforeload',
    function(store, options) {
        var new_params = {
            flag: flagGFSSVIEWWIN,
            hasPage: false,
            rgIds: chosedResGroups,
            agentState: agentStatusCb.getValue(),
            agentIp: Ext.util.Format.trim(agent_ip.getValue()),
            appName : app_name.getValue()==null?'':Ext.util.Format.trim(app_name.getValue()+""),
            sysName: sys_name.getValue() == null ? '': Ext.util.Format.trim(sys_name.getValue() + ""),
            hostName: Ext.util.Format.trim(host_name.getValue()),
            osType: Ext.util.Format.trim(os_type.getValue())
        };

        Ext.apply(agent_store.proxy.extraParams, new_params);
    });

    agent_store.addListener('load',
    function(me, records, successful, eOpts) {
        if (editingChosedAgentIds) {
            var chosedRecords = []; //存放选中记录
            $.each(records,
            function(index, record) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                    chosedRecords.push(record);
                }
            });
            chosedServList.getSelectionModel().select(chosedRecords, false, true); //选中记录
        }
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var agent_store_chosed = Ext.create('Ext.data.Store', {
        autoLoad: false,
        model: 'agentModel',
        pageSize: 50,
        proxy: {
            type: 'ajax',
            url: 'getAgentChosedList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    agent_store_chosed.on('beforeload',
    function(store, options) {
        var new_params = {
            agentIds: JSON.stringify(chosedAgentIds)
        };

        Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    agent_store_chosed.on('load',
    function() {
        agent_grid_chosed_for_config_param.getSelectionModel().select(0);
    });

    var pageBarForAgentChosedToSetParamsGrid = Ext.create('Ext.PagingToolbar', {
        store: agent_store_chosed,
        dock: 'bottom',
        displayInfo: true
    });

    var agent_store_chosedForSee = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAgentChosedList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    agent_store_chosedForSee.on('beforeload',
    function(store, options) {
        var new_params = {
            agentIds: JSON.stringify(chosedAgentIds)
        };

        Ext.apply(agent_store_chosedForSee.proxy.extraParams, new_params);
    });

    agent_store_chosedForSee.addListener('load',
    function(me, records, successful, eOpts) {
        for (var prop in globalConfigStartUser) {
            if (agent_store_chosedForSee.findRecord('iid', prop)) {
                agent_store_chosedForSee.findRecord('iid', prop).set('agentStartUser', globalConfigStartUser[prop]);
            }
        }
    });

    var agent_grid_chosed_for_config_param = Ext.create('Ext.grid.Panel', {
        title: '已选服务器列表',
        store: agent_store_chosed,
        border: true,
        columnLines: true,
        bbar: pageBarForAgentChosedToSetParamsGrid,
        width: '59%',
        height: contentPanel.getHeight() - 150,
        autoScroll: true,
        emptyText: '没有选择服务器',
        columns: [{
            text: '序号',
            xtype: 'rownumberer',
            width: 40
        },
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '系统名称',
            dataIndex: 'sysName',
            width: 80
        },
        {
            text: '应用名称',
            dataIndex: 'appName',
            width: 80
        },
        {
            text: 'IP',
            dataIndex: 'agentIp',
            width: 80
        },
        {
            text: '主机名称',
            dataIndex: 'hostName',
            width: 100
        },
        {
            text: '系统类型',
            dataIndex: 'osType',
            width: 100
        },
        {
            text: '端口号',
            dataIndex: 'agentPort',
            width: 100
        },
        {
            text: '描述',
            dataIndex: 'agentDesc',
            flex: 1,
            hidden: true
        },
        {
            text: '状态',
            dataIndex: 'agentState',
            width: 130,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }],
        listeners: {
            select: function(self, record, index, eOpts) {
                var ipId = record.get('iid');
                if (globalConfigParams.hasOwnProperty(ipId)) {
                    var cp = globalConfigParams[ipId];
                    configParamStore.each(function(record) {
                        record.set('paramValue', cp[record.get('iid')])
                    });
                } else {
                    configParamStore.each(function(record) {
                        record.set('paramValue', '');
                    });
                }
            }
        }
    });

    var choseAgentWrapper_for_config_params = Ext.create('Ext.panel.Panel', {
        border: false,
        height: contentPanel.getHeight() - 100,
        layout: {
            type: 'hbox',
            padding: '5',
            align: 'stretch'
        },
        items: [agent_grid_chosed_for_config_param, paramGrid_for_config_params]
    });

    var parmsButton = Ext.create('Ext.Button', {
        text: '配置参数',
        cls: 'Common_Btn',
        handler: function() {
            if (!agentParamsWin) {
                agentParamsWin = Ext.create('Ext.window.Window', {
                    title: '配置参数',
                    autoScroll: true,
                    modal: true,
                    resizable: false,
                    closeAction: 'hide',
                    width: contentPanel.getWidth(),
                    height: contentPanel.getHeight(),
                    items: [choseAgentWrapper_for_config_params],
                    buttonAlign: 'center',
                    buttons: [{
                        xtype: "button",
                        text: "确定",
                        handler: function() {
                            //
                            this.up("window").close();
                        }
                    }]
                });
            }
            agentParamsWin.show();
            //			agent_store_chosed.removeAll();
            pageBarForAgentChosedToSetParamsGrid.moveFirst();

        }
    });

    chosedServList = Ext.create('Ext.grid.Panel', {
        region: 'center',
        autoScroll: true,
        multiSelect: true,
        split: true,
        columnLines: true,
        store: agent_store,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [sys_name, app_name, host_name,os_type]
        },
        {
            xtype: 'toolbar',
            dock: 'top',
            items: [agent_ip, resourceGroupObj, agentStatusCb, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    agent_ip.setValue('');
                    app_name.setValue('');
                    sys_name.setValue('');
                    host_name.setValue('');
                    os_type.setValue('');
                    resourceGroupObj.setValue('');
                    agentStatusCb.setValue('');
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '导入',
                handler: importExcel
            }]
        }],
        selModel: selModel,
        listeners: {
            select: function(t, record, index, eOpts) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) == -1) {
                	editingChosedAgentIds.push(record.get('iid'));
                }
            },
            deselect: function(t, record, index, eOpts) {
                if (editingChosedAgentIds.indexOf(record.get('iid')) > -1) {
                	editingChosedAgentIds.remove(record.get('iid'));
                }
                /*if (globalConfigParams.hasOwnProperty(record.get('iid'))) {
                    delete globalConfigParams[record.get('iid')];
                    Ext.Msg.alert('提示', "该服务器个性化的参数已删除！");
                }*/
            }
        },
        columns: [
        {
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '系统名称',
            dataIndex: 'sysName',
            flex: 1
        },
        {
            text: '应用名称',
            dataIndex: 'appName',
            flex: 1
        },
        {
            text: '主机名称',
            dataIndex: 'hostName',
            flex: 1
        },
        {
            text: 'IP',
            dataIndex: 'agentIp',
            width: 150
        },
        {
            text: '端口号',
            dataIndex: 'agentPort',
            width: 100
        },
        {
            text: '系统类型',
            dataIndex: 'osType',
            width: 140
        },
        {
            text: '资源组',
            dataIndex: 'resGroup',
            width: 100,
            hidden: true
        },
        {
            text: '描述',
            dataIndex: 'agentDesc',
            flex: 1,
            hidden: true
        },
        {
            text: '状态',
            dataIndex: 'agentState',
            width: 130,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }],
        bbar: pageBar
    });
    var pageBarForAgentChosedGrid = Ext.create('Ext.PagingToolbar', {
        store: agent_store_chosedForSee,
        dock: 'bottom',
        displayInfo: true
    });
    var agent_grid_chosedForSee = Ext.create('Ext.grid.Panel', {
        title: readOnly?'已选服务器':null,
    	width: '65%',
        height: contentPanel.getHeight() - 130,
        emptyText: '没有选择服务器',
        store: agent_store_chosedForSee,
        border: true,
        columnLines: true,
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit: 2})],
        columns: [{
            text: '主键',
            dataIndex: 'iid',
            hidden: true
        },
        {
            text: '系统名称',
            dataIndex: 'sysName',
            flex: 1
        },
        {
            text: '应用名称',
            dataIndex: 'appName',
            flex: 1
        },
        {
            text: '主机名称',
            dataIndex: 'hostName',
            flex: 1
        },
        {
            text: 'IP',
            dataIndex: 'agentIp',
            width: 120
        },
        {
            text: '端口号',
            dataIndex: 'agentPort',
            width: 60
        },
        {
            text: '系统类型',
            dataIndex: 'osType',
            width: 110
        },
        {
            dataIndex: 'agentStartUser',
            text: '启动用户',
            editor: readOnly?null:{
                allowBlank: true
            },
            flex: true
        },
        {
            text: '描述',
            dataIndex: 'agentDesc',
            flex: 1,
            hidden: true,
            renderer: function(value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '状态',
            dataIndex: 'agentState',
            width: 80,
            renderer: function(value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = "Agent正常";
                } else if (value == 1) {
                    backValue = "Agent异常";
                }
                return backValue;
            }
        }],
        selModel: readOnly?null:Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        bbar: pageBarForAgentChosedGrid,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [readOnly?null:{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '删除',
                handler: function() {
                    var records = agent_grid_chosedForSee.getSelectionModel().getSelection();
                    if (records.length > 0) {
                        for (var i = 0,
                        len = records.length; i < len; i++) {
                            chosedAgentIds.remove(records[i].get('iid'));
                        }
                        pageBarForAgentChosedGrid.moveFirst();
                        pageBar.moveFirst();
                    } else {
                        Ext.Msg.alert('提示', "请选择服务器！");
                        return;
                    }
                }
            },
            readOnly?null:{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '增加服务器',
                handler: function() {
                    if (!chosedAgentWinForSee) {
                        chosedAgentWinForSee = Ext.create('Ext.window.Window', {
                            title: '增加服务器',
                            autoScroll: true,
                            modal: true,
                            resizable: false,
                            closeAction: 'hide',
                            width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
                            layout: 'border',
                            items: [chosedServList],
                            buttonAlign: 'center',
                            dockedItems: [{
                                xtype: 'toolbar',
                                dock: 'bottom',
                                layout: {pack: 'center'},
                                items: [{
                                    xtype: "button",
                                    text: "确定",
                                    cls: 'Common_Btn',
                                    margin: '6',
                                    handler: function() {
                                    	chosedAgentIds = editingChosedAgentIds.slice(0);
                                        agent_store_chosedForSee.load();
                                        this.up("window").close();
                                    }
                                },
                                {
                                    xtype: "button",
                                    text: "关闭",
                                    cls: 'Common_Btn',
                                    handler: function() {
                                        this.up("window").close();
                                    }
                                }]
                            }]
                        });
                    }
                    editingChosedAgentIds = chosedAgentIds.slice(0);
                    chosedAgentWinForSee.show();
                    agent_store.load();
                }
            },
            parmsButton]
        }]
    });

    agent_grid_chosedForSee.on('edit',
    function(editor, e) {
        var record = e.record;
        var iid = record.get('iid');
        var agentStartUser = record.get('agentStartUser');

        if (!Ext.isEmpty(Ext.util.Format.trim(agentStartUser))) {
            globalConfigStartUser[iid] = agentStartUser
        } else {
            delete globalConfigStartUser[iid];
        }
    });

    var scriptDetailPanel = Ext.create('Ext.panel.Panel', {
        margin: '8 0 0 0',
        border: false,
        layout: {
            type: 'hbox',
            align: 'stretch'
        },
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'bottom',
            border: false,
            ui: 'footer',
            margin: '5 0 5 0',
            layout: { pack: 'center' },
            items: [{
                text: '保存',
                handler: function() {
                    if (chosedAgentIds.length < 1) {
                        Ext.MessageBox.alert("提示", "请选择服务器!");
                        return null;
                    }

                    var isOk = false;
                    var agentStateMsg = "";

                    // 检查agent状态
                    Ext.Ajax.request({
                        url: 'checkAgentState.do',
                        method: 'POST',
                        async: false,
                        params: {
                            agentIds: chosedAgentIds
                        },
                        success: function(response, request) {
                            isOk = Ext.decode(response.responseText).isOk;
                            agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
                        },
                        failure: function(result, request) {
                            agentStateMsg = "检查Agent状态出错！";
                        }
                    });

                    if (isOk) {
                        saveFun();
                    } else {
                        Ext.Msg.confirm("请确认", agentStateMsg + "<br>选择的代理状态为异常，是否仍然保存？",
                        function(id) {
                            if (id == 'yes') {
                                saveFun();
                            }
                        });
                    }
                }
            },
            {
                text: '关闭',
                hidden: true,
                handler: function() {
                    parent.cellObjGFSSVIEWWIN = null;
                    parent.configwindowFlowGFSSVIEWWIN.close();
                }
            },
            prevButton, nextButton, viewChosedScriptButton]
        }],
        items: [agent_grid_chosedForSee, paramGrid]
    });

    // 主Panel
    var MainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "flowCustomizedScriptWindowDivGFSSVIEWWIN",
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        autoScroll: true,
        border: true,
        items: [formPanel, scriptDetailPanel]
    });

    function checkFile(fileName) {
        var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
        if (!file_reg.test(fileName)) {
            Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
            //Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
            return false;
        }
        return true;
    }

    function importExcel() {
        //销毁win窗口
        if (! (null == upldWin || undefined == upldWin || '' == upldWin)) {
            upldWin.destroy();
            upldWin = null;
        }

        if (! (null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
            upLoadformPane.destroy();
            upLoadformPane = null;
        }
        //导入文件Panel
        upLoadformPane = Ext.create('Ext.form.Panel', {
            width: 370,
            height: 85,
            frame: true,
            items: [{
                xtype: 'filefield',
                name: 'file',
                // 设置该文件上传空间的name，也就是请求参数的名字
                fieldLabel: '选择文件',
                labelWidth: 80,
                msgTarget: 'side',
                anchor: '100%',
                buttonText: '浏览...',
                width: 370
            }],
            buttonAlign: 'left',
            buttons: [{
                id: 'upldBtnIdAudi',
                text: '导入Agent文件',
                handler: function() {
                    var form = this.up('form').getForm();
                    var upfile = form.findField("file").getValue();
                    if (upfile == '') {
                        Ext.Msg.alert('提示', "请选择文件...");
                        return;
                    }

                    var hdtmpFilNam = form.findField("file").getValue();
                    if (!checkFile(hdtmpFilNam)) {
                        form.findField("file").setRawValue('');
                        return;
                    }

                    if (form.isValid()) {
                        Ext.MessageBox.wait("数据处理中...", "进度条");
                        form.submit({
                            url: 'importAgentForStart.do',
                            params: {
                                envType: flagGFSSVIEWWIN
                            },
                            success: function(form, action) {
                                var msg = Ext.decode(action.response.responseText).message;

                                var status = Ext.decode(action.response.responseText).status;
                                var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;

                                if (status == 1) {
                                    if (matchAgentIds && matchAgentIds.length > 0) {
                                        Ext.MessageBox.buttonText.yes = "确定";
                                        Ext.MessageBox.buttonText.no = "取消";
                                        Ext.Msg.confirm("请确认", msg,
                                        function(id) {
                                            if (id == 'yes') {
                                                Ext.Msg.alert('提示', "导入成功！");
                                                agent_ip.setValue('');
                                                app_name.setValue('');
                                                sys_name.setValue('');
                                                host_name.setValue('');
                                                os_type.setValue('');
                                                resourceGroupObj.setValue('');
                                                agentStatusCb.setValue('');
                                                editingChosedAgentIds = matchAgentIds.slice(0);
                                                pageBar.moveFirst();
                                            }
                                        });
                                    } else {
                                        Ext.Msg.alert('提示-没有匹配项', msg);
                                    }

                                } else {
                                    Ext.Msg.alert('提示', "导入成功！");
                                    agent_ip.setValue('');
                                    app_name.setValue('');
                                    sys_name.setValue('');
                                    host_name.setValue('');
                                    os_type.setValue('');
                                    resourceGroupObj.setValue('');
                                    agentStatusCb.setValue('');
                                    editingChosedAgentIds = msg.slice(0);
                                    pageBar.moveFirst();
                                }

                                upldWin.close();
                                return;
                            },
                            failure: function(form, action) {
                                secureFilterRsFrom(form, action);
                            }
                        });
                    }
                }
            },
            {
                text: '下载模板',
                handler: function() {
                    window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
                }
            }]
        });
        //导入窗口
        upldWin = Ext.create('Ext.window.Window', {
            title: '设备信息批量导入',
            width: 400,
            height: 120,
            modal: true,
            resizable: false,
            closeAction: 'destroy',
            items: [upLoadformPane]
        }).show();
        upldWin.on("beforeshow",
        function(self, eOpts) {
            var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
            form.reset();
        });

        upldWin.on("destroy",
        function(self, eOpts) {
            upLoadformPane.destroy();
        });
    }

    /** 初始化方法* */
    function initFun() {
        if (actStartInfo[pageFromGFSSVIEWWIN].hasOwnProperty(parent.cellObjGFSSVIEWWIN.mxIid)) {
            var _actStartInfo = actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid];

            stepNameObj.setValue(_actStartInfo['actName']);
            shutdownCheckboxObj.setValue(_actStartInfo['isShutdown']);

            if (_actStartInfo.hasOwnProperty('chosedAgentIds')) {
                chosedAgentIds = _actStartInfo['chosedAgentIds'];
            }

            if (_actStartInfo.hasOwnProperty('chosedResGroups')) {
                chosedResGroups = _actStartInfo['chosedResGroups'];
                resourceGroupObj.setValue(chosedResGroups);
            } else {
                chosedResGroups = new Array();
                resourceGroupObj.setValue('');
            }

            resourceGroupObj.fireEvent('select');

            if (_actStartInfo.hasOwnProperty('globalParams')) {
                globalParams = _actStartInfo['globalParams'];
            }

            if (_actStartInfo.hasOwnProperty('globalConfigParams')) {
                globalConfigParams = _actStartInfo['globalConfigParams'];
            }

            if (_actStartInfo.hasOwnProperty('globalStartUser')) {
                startUserObj.setValue(_actStartInfo['globalStartUser']);
            }

            if (_actStartInfo.hasOwnProperty('globalConfigStartUser')) {
                globalConfigStartUser = _actStartInfo['globalConfigStartUser'];
            }

            if (scriptServiceId > 0) {
                paramStore.load();
                configParamStore.load();
                Ext.Ajax.request({
                    url: 'getScritContentScriptService.do',
                    method: 'POST',
                    params: {
                        iid: scriptServiceId,
                        from: 2,
                        flag: "0"
                    },
                    success: function(response, options) {
                        var content = Ext.decode(response.responseText).content;
                        var serviceName = Ext.decode(response.responseText).serviceName;
                        scriptContentObj.setValue(content);
                        scriptNameObj.setValue(serviceName);
                    },
                    failure: function(result, request) {
                        scriptContentObj.setValue('');
                        scriptNameObj.setValue("");
                    }
                });
            }
            agent_store_chosedForSee.load();
        }
    }
    function trim(t) {
        t = t.replace(/(^\s*)|(\s*$)/g, "");
        return t.replace(/(^ *)|( *$)/g, "");
    }
    function saveFun() {
        if (stepNameObj.getValue().trim() == '') {
            Ext.Msg.alert('提示', '步骤名称不允许为空!');
            return null;
        }
        if ('开始' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<开始>！');
            return null;
        }
        if ('结束' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<结束>！');
            return null;
        }

        var m = paramStore.getRange(0, paramStore.getCount() - 1);
        for (var i = 0,
        len = m.length; i < len; i++) {
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';

            if (paramType == 'int' && paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
                    return;
                }
            }
            if (paramType == 'float' && paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"') >= 0) {
                if (cellScriptType == 'bat') {
                    Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
                }
            }
            if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
                globalParams[m[i].get('iid')] = paramDefaultValue;
            } else {
            	delete globalParams[m[i].get('iid')];
            }
        }

        var tempConfigStartUser = {};
        var tempConfigParams = {};
        $.each(chosedAgentIds,
        function(index, closedAgentId) {
            if (globalConfigStartUser.hasOwnProperty(closedAgentId)) {
                tempConfigStartUser[closedAgentId] = globalConfigStartUser[closedAgentId];
            }
            
            if (globalConfigParams.hasOwnProperty(closedAgentId)) {
        		tempConfigParams[closedAgentId] = globalConfigParams[closedAgentId];
            }
        });
        globalConfigStartUser = tempConfigStartUser;
        globalConfigParams = tempConfigParams;

        parent.cellObjGFSSVIEWWIN.chosedResGroups = chosedResGroups;
        parent.cellObjGFSSVIEWWIN.chosedAgentIds = chosedAgentIds;
        parent.cellObjGFSSVIEWWIN.globalParams = globalParams;
        parent.cellObjGFSSVIEWWIN.globalConfigParams = globalConfigParams;
        parent.cellObjGFSSVIEWWIN.globalStartUser = Ext.util.Format.trim(startUserObj.getValue());
        parent.cellObjGFSSVIEWWIN.globalConfigStartUser = globalConfigStartUser;

        parent.cellObjGFSSVIEWWIN.value = stepNameObj.getValue();
        parent.cellObjGFSSVIEWWIN.isShutdown = shutdownCheckboxObj.getValue();
        parent.callbackWindwGFSSVIEWWIN();

        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['chosedResGroups'] = chosedResGroups;
        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['chosedAgentIds'] = chosedAgentIds;
        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['globalParams'] = globalParams;
        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['globalConfigParams'] = globalConfigParams;
        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['globalStartUser'] = Ext.util.Format.trim(startUserObj.getValue());
        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['globalConfigStartUser'] = globalConfigStartUser;
        actStartInfo[pageFromGFSSVIEWWIN][parent.cellObjGFSSVIEWWIN.mxIid]['isShutdown'] = shutdownCheckboxObj.getValue();

        Ext.Msg.alert('提示', '当前步骤保存成功!');
        //		parent.configwindowFlowGFSSVIEWWIN.close ();
    }
    initFun();

    /**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
    function getCellFun(inflag) {
        // 遍历所有节点
        var rootObj = modelGFSSVIEWWIN.getRoot();
        var count = modelGFSSVIEWWIN.getChildCount(rootObj);
        for (var i = 0; i < count; i++) {
            var cells = rootObj.getChildAt(i);
            var counts = cells.getChildCount();
            var beforeCell = null; // 上一个节点
            var afterCell = null; // 下一个节点
            var selfCell = null; // 自己
            for (var j = 0; j < counts; j++) {
                var cellss = cells.getChildAt(j);
                // 判断循环至的节点样式是否与传入的样式一致
                if (cellss.style == parent.cellObjGFSSVIEWWIN.style) {
                    if (cellss == parent.cellObjGFSSVIEWWIN) {
                        // 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
                        selfCell = parent.cellObjGFSSVIEWWIN;
                    } else {
                        // 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
                        selfCell == null ? beforeCell = cellss: afterCell = cellss;
                    }
                    // 如果获取到了想要的节点，则跳出循环
                    if (selfCell != null && ((inflag == 'after' && afterCell != null) || (inflag == 'before' && beforeCell != null))) {
                        break;
                    }
                }
            }
            // 返回指定节点
            return inflag == 'after' ? afterCell: beforeCell;
        }
    }
});