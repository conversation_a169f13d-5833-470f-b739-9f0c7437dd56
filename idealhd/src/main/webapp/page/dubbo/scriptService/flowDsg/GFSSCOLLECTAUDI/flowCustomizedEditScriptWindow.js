/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/


Ext.onReady (function ()
{
	var scriptNameObj;
	var scriptNameStore;
	var scriptContent = "";
	var canEditScript = false;
	var scriptServiceId = -1;
	var stepNameObj;

	var bussCb = '';
	var bussTypeCb = '';
	
	stepNameObj = Ext.create ('Ext.form.field.Text', {
	    fieldLabel : '步骤名称',
	    labelWidth : 73,
	    labelAlign : 'right',
	    width: 570
	});
	
	var viewChosedScriptButton = Ext.create('Ext.Button', {
		text : '查看已选脚本',
		margin : '0 0 0 30',
		textAlign : 'center',
		handler : function() {
			if(scriptServiceId>0) {
				Ext.create('widget.window', {
		            title: '详细信息',
		            closable: true,
		            closeAction: 'destroy',
		            width: contentPanel.getWidth(),
		            minWidth: 350,
		            height: contentPanel.getHeight(),
		            draggable: false,
		            // 禁止拖动
		            resizable: false,
		            // 禁止缩放
		            modal: true,
		            loader: {
		                url: 'queryOneServiceForView.do',
		                params: {
		                    iid: scriptServiceId,
		                    flag: 0,
		                    hideReturnBtn: 1
		                },
		                autoLoad: true,
		                scripts: true
		            }
		        }).show();
			} else {
				Ext.Msg.alert('提示',"没有选择脚本服务！");
			}
			
		}
	});
	

	var scriptVersionStore = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'onlyVersion' ],
		autoLoad : false,
	    proxy : {
	        type : 'ajax',
	        url : 'getScriptServiceVersionList.do',
	        reader : {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	var bussData = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'bsName' ],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsAll.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var bussTypeData = Ext.create('Ext.data.Store', {
		fields : [ 'sysTypeId', 'sysType' ],
		autoLoad : false,
	    proxy : {
	        type : 'ajax',
	        url : 'bsManager/getBsTypeByFk.do',
	        reader : {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});

	var scriptVersionCbObj = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptVersion',
		width: 380,
		queryMode : 'local',
		fieldLabel : '选择版本',
		displayField : 'onlyVersion',
		valueField : 'iid',
		editable : false,
		queryMode : 'local',
		emptyText : '--请选择版本--',
		store : scriptVersionStore,
		listeners: {
			change: function(self, newValue, oldValue, eOpts) { 
				Ext.Ajax.request ({
    			    url : 'getScritContentScriptService.do',
    			    method : 'POST',
    			    params :
    			    {
    			        iid : newValue,
    			        flag: 0
    			    },
    			    success : function (response, options)
    			    {
    				    var content= Ext.decode (response.responseText).content;
    				    scriptContentObj.setValue(content);
    			    },
    				failure : function(result, request) {
    					scriptContentObj.setValue(''); 
    				}
    			
    			});
			}
		}
	});
	
	var bussCbObj = Ext.create('Ext.form.field.ComboBox', {
		name : 'sysName',
		queryMode : 'local',
		width : '18%',
		labelWidth : 60,
		fieldLabel : '一级分类',
		displayField : 'bsName',
		valueField : 'iid',
		editable : false,
		queryMode : 'local',
		emptyText : '--请选择一级分类--',
		store : bussData,
		listeners: {
			change: function() { // old is keyup
				bussTypeCbObj.clearValue();
				bussTypeCbObj.applyEmptyText();
				bussTypeCbObj.getPicker().getSelectionModel().doMultiSelect([], false);
				bussTypeData.load({params:{fk: this.value}});
			}
		}
	});

	/** 工程类型下拉框* */
	var bussTypeCbObj = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussType',
		width : '18%',
		labelWidth : 60,
		queryMode : 'local',
		fieldLabel : '二级分类',
		displayField : 'sysType',
		valueField : 'sysTypeId',
		editable : false,
		emptyText : '--请选择二级分类--',
		store : bussTypeData
	});
	
	var cataStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
	        {"id":"-1", "name":"全部"},
	        {"id":"sh", "name":"shell"},
	        {"id":"bat", "name":"bat"},
	        {"id":"py", "name":"python"},
	        {"id":"perl", "name":"perl"},
	        {"id":"sql", "name":"sql"}
	    ]
	});
	
	var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptTypeParam',
		padding : '5',
		labelWidth : 60,
		queryMode : 'local',
		fieldLabel : '脚本类型',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择脚本类型--',
		store : cataStore,
		width : '17%',
        labelAlign : 'right'
	});
	
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		labelWidth : 60,
		padding : '5',
		width : '18%',
        labelAlign : 'right'
        
	});
	var scName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel : '脚本名称',
		emptyText : '--请输入脚本名称--',
		labelWidth : 60,
		padding : '5',
		width : '18%',
        labelAlign : 'right'
	});
	
	var search_form = Ext.create('Ext.form.Panel', {
		 region:'north',
		 layout : 'anchor',
		 buttonAlign: 'center',
		 
    	border : false,
    	dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [sName,scName,scriptTypeParam,bussCbObj,bussTypeCbObj,{
				xtype : 'button',
				text : '查询',
				cls : 'Common_Btn',
				handler : function() {
					pageBar.moveFirst();
					scriptContentObj.setValue('');
					scriptVersionCbObj.setValue('');
				}
			},{
				xtype : 'button',
				text : '清空',
				cls : 'Common_Btn',
				handler : function() {
					bussCbObj.setValue('');
					bussTypeCbObj.setValue('');
					scName.setValue('');
					sName.setValue('');
					scriptTypeParam.setValue('');
				}
			}]
		}]
	});
	 
	 Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'isshare',type : 'string'},
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'}
	    ]
	});
		
	var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 15,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'scriptService/queryServiceForMySelf.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	scriptServiceReleaseStore.on('beforeload', function (store, options) {
	    var new_params = {  
	    		bussId:bussCbObj.getValue(),
	    		bussTypeId:bussTypeCbObj.getValue(),
	    		scriptName:scName.getValue(),
	    		serviceName:sName.getValue(),
	    		scriptType:scriptTypeParam.getValue(),
	    		onlyScript : 1
	    };
	    
	    Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });
	
	var scriptServiceReleaseColumns = [{
		text : '序号',
		xtype : 'rownumberer',
		width : 40
	}, 
	{
	    text : '服务主键',
	    dataIndex : 'iid',
	    width : 40,
	    hidden : true
	}, 
	{
		text : '服务名称',
	    dataIndex : 'serviceName',
	    width : 200,flex:1
	},
	{
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 260,flex:1
	}, 
	{
	    text : '一级分类',
	    dataIndex : 'buss',
	    width : 200,flex:1
	}, 
	{
		text : '二级分类',
		dataIndex : 'bussType',
		width : 250,flex:1
	},
	{
	    text : '脚本类型',
	    dataIndex : 'scriptType',
	    width : 80,
	    renderer:function(value,p,record,rowIndex){
	    	var isflow = record.get('isflow');
	    	var backValue = "";
			if (value == "sh") {
				backValue = "shell";
			} else if (value == "perl") {
				backValue = "perl";
			} else if (value == "py") {
				backValue = "python";
			} else if (value == "bat") {
				backValue = "bat";
			} else if (value == "sql") {
				backValue = "sql";
			}
			if (isflow == '1') {
				backValue = "组合";
			}
			return backValue;
	    }
	}, 
	{
		text : '适用平台',
		dataIndex : 'platForm',
		width : 100
	}, 
	{
		text : '脚本状态',
		dataIndex : 'status',
		width : 100,
		renderer:function(value,p,record,rowIndex){
	    	if(value==-1) {
	    		return '<font color="#F01024">草稿</font>';
	    	} else if (value==1) {
	    		return '<font color="#0CBF47">已上线</font>';
	    	} else if (value==2) {
	    		return '<font color="#FFA602">审核中</font>';
	    	} else if (value==3) {
	    		return '<font color="#13B1F5">已共享</font>';
	    	} else if (value==9) {
	    		return '<font color="">已共享未发布</font>';
	    	} else {
	    		return '<font color="#CCCCCC">未知</font>';
	    	}
	    }
	}];

	var pageBar = Ext.create('Ext.PagingToolbar', {
	  	store: scriptServiceReleaseStore,
	    dock: 'bottom',
	    displayInfo: true,
	    emptyMsg:'找不到任何记录'
	});
	
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		autoScroll: true,
	    store : scriptServiceReleaseStore,
	    bbar : pageBar,
	    width: '50%',
	    columnLines : true,
	    columns : scriptServiceReleaseColumns,
	    listeners: {
	    	'select': function(self, record, index, eOpts){
	    		scriptVersionCbObj.setRawValue('');
	    		scriptVersionStore.load({
	    			params:{
	    				serviceId: record.get('iid'),
	    				flag: 0
	    			},
	    			callback: function (records, operation, success) {
	    	            if (records.length > 0) {
	    	            	scriptVersionCbObj.select(scriptVersionStore.getAt(0));
	    	            }
	    	        }
	    		});
	    	}
	    }
	});
	
	var scriptContentObj = Ext.create ('Ext.form.field.TextArea', {
		fieldLabel : '脚本内容',
		readOnly:true,
		height: contentPanel.getHeight ()-185,
	    autoScroll : true
	});
	
	var canEditScriptObj = Ext.create('Ext.form.Checkbox', {
		fieldLabel  : '脚本可否编辑',
	    labelWidth: 90,
	    handler: function() {
	      if(this.checked==true) {
	    	  scriptContentObj.setReadOnly(false);
	      } else {
	    	  scriptContentObj.setReadOnly(true);
	      }
	    }
	  });
	
	var showDButton = Ext.create('Ext.Button', {
		text : '显示详情',
		margin : '0 0 0 5',
		textAlign : 'center',
		handler : function() {
			var i = parseInt(scriptVersionCbObj.getValue());
			if(i>0) {
				Ext.create('widget.window', {
		            title: '详细信息',
		            closable: true,
		            closeAction: 'destroy',
		            width: contentPanel.getWidth(),
		            minWidth: 350,
		            height: contentPanel.getHeight(),
		            draggable: false,
		            resizable: false,
		            modal: true,
		            loader: {
		                url: 'queryOneServiceForView.do',
		                params: {
		                    iid: i,
		                    flag: 0,
		                    hideReturnBtn: 1
		                },
		                autoLoad: true,
		                scripts: true
		            }
		        }).show();
			} else {
				Ext.Msg.alert ('提示', '请选择版本!');
			}
		}
	});
	
	var scriptContentPanel = Ext.create ('Ext.form.Panel',
	{
		border : false,
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        width : contentPanel.getWidth () /2 - 20,
	        labelWidth : 90
	    },
	    width: '50%',
	    buttonAlign : 'center',
	    items : [{
			layout : 'column',
			anchor : '100%',
			height : 38,
			border : false,
			items : [scriptVersionCbObj, showDButton]
		}, /*canEditScriptObj, */scriptContentObj]
	});
	
	var scriptDetailPanel = Ext.create('Ext.panel.Panel',{
		region: 'center',
        width : contentPanel.getWidth(),
        border : false,
        layout: {
            type: 'hbox',
            padding:'5',
            align:'stretch'
        },
        items : [scriptServiceReleaseGrid, scriptContentPanel]
	});
	
	var saveButton = Ext.create('Ext.Button', {
		text : '保存',
		margin : '0 0 0 30',
		textAlign : 'center',
		hidden: (parent.actionTypeGFSSCOLLECTAUDI=='view'||parent.actionTypeGFSSCOLLECTAUDI=='exec'||parent.actionTypeGFSSCOLLECTAUDI=='dbcheck'||parent.actionTypeGFSSCOLLECTAUDI=='dbback'),
		handler : function() {
			saveFun();
		}
	});
	var backButton = Ext.create('Ext.Button', {
		text : '关闭',
		margin : '0 0 0 5',
		textAlign : 'center',
		handler : function() {
			parent.cellObjGFSSCOLLECTAUDI = null;
			parent.configwindowFlowGFSSCOLLECTAUDI.close ();
		}
	});
	
	var prevButton = Ext.create('Ext.Button', {
	    text : '上一步',
	    margin : '0 0 0 30',
	    textAlign : 'center',
	    handler : function() {
	    	var res = getCellFun("before");
	    	if(res) {
	    		parent.cellObjGFSSCOLLECTAUDI = res;
	    		canEditScript = false;
	    		initFun ();
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为第一个步骤");
	    	}
	      }
	  });
			
	var nextButton = Ext.create('Ext.Button', {
		text : '下一步',
		margin : '0 0 0 5',
		textAlign : 'center',
		handler : function() {
			var res = getCellFun("after");
	    	if(res) {
	    		parent.cellObjGFSSCOLLECTAUDI = res;
	    		canEditScript = false;
	    		initFun ();
	    	} else {
	    		Ext.Msg.alert('提示',"当前步骤为最后一个步骤");
	    	}
		}
	});
	
	var absScriptDetailPanel = Ext.create('Ext.panel.Panel',{
        border : true,
        title: '已选脚本基本信息',
        hidden: true,
        html: ""
	});
	
	
	var choseSSPanel = Ext.create('Ext.panel.Panel',{
		border : false,
		items : [search_form,scriptDetailPanel, absScriptDetailPanel]
	});
	
	var mainPanelItems = [ {
		layout : 'column',
		margin: '10 0 0 0',
		anchor : '100%',
//		height : 50,
		border : false,
		items : [stepNameObj,viewChosedScriptButton, saveButton, backButton, prevButton, nextButton]
	},choseSSPanel];
	
	if((parent.actionTypeGFSSCOLLECTAUDI=='view'||parent.actionTypeGFSSCOLLECTAUDI=='exec'||parent.actionTypeGFSSCOLLECTAUDI=='dbcheck'||parent.actionTypeGFSSCOLLECTAUDI=='dbback')) {
		mainPanelItems = [stepNameObj, {
			layout : 'column',
			margin: '12 0 0 0',
			anchor : '100%',
			height : 50,
			border : false,
			items : [viewChosedScriptButton, saveButton, backButton, prevButton, nextButton]
		}];
	}
	
	var formPanel = Ext.create ('Ext.form.Panel',
	{
		border : false,
		renderTo : "flowCustomizedEditScriptWindowDivGFSSCOLLECTAUDI",
	    fieldDefaults :
	    {
	        labelAlign : 'right',
	        labelWidth : 90
	    },
	    width : '100%',
	    buttonAlign : 'center',
	    items : mainPanelItems
	});

	/** 初始化方法* */
	function initFun ()
	{
		stepNameObj.setValue(parent.cellObjGFSSCOLLECTAUDI.value);
		if(parent.cellObjGFSSCOLLECTAUDI.scriptId!=null && typeof (parent.cellObjGFSSCOLLECTAUDI.scriptId) != "undefined") {
			scriptServiceId = parseInt(parent.cellObjGFSSCOLLECTAUDI.scriptId);
			if(isNaN(scriptServiceId)) {
				scriptServiceId = -1;
			}
		}
		if (parent.cellObjGFSSCOLLECTAUDI.canEditScript!=null && typeof (parent.cellObjGFSSCOLLECTAUDI.canEditScript) != "undefined")
		{
			canEditScript = parent.cellObjGFSSCOLLECTAUDI.canEditScript;
		}
		if (parent.cellObjGFSSCOLLECTAUDI.scriptContent!=null && typeof (parent.cellObjGFSSCOLLECTAUDI.scriptContent) != "undefined")
		{
			scriptContent = parent.cellObjGFSSCOLLECTAUDI.scriptContent;
		}
	}
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
	function saveFun ()
	{
		if (stepNameObj.getValue ().trim () == '')
		{
			Ext.Msg.alert ('提示', '步骤名称不允许为空!');
			return null;
		}
		if ('开始' == stepNameObj.getValue ().trim ())
		{
			Ext.Msg.alert ('提示', '步骤名称不可以为<开始>！');
			return null;
		}
		if ('结束' == stepNameObj.getValue ().trim ())
		{
			Ext.Msg.alert ('提示', '步骤名称不可以为<结束>！');
			return null;
		}
		
		var ssId = parseInt(scriptVersionCbObj.getValue());
		if(isNaN(ssId) || ssId <= 0) {
			if(scriptServiceId<=0) {
				Ext.Msg.alert ('提示', '请先选择脚本服务，然后选择版本!');
				return;
			}
		} else {
			scriptServiceId = ssId;
		}
		
//		parent.cellObjGFSSCOLLECTAUDI.bussId=bussCb;
//		parent.cellObjGFSSCOLLECTAUDI.bussTypeId=bussTypeCb;
		parent.cellObjGFSSCOLLECTAUDI.scriptId = scriptServiceId;
		parent.cellObjGFSSCOLLECTAUDI.canEditScript = canEditScript;
//		if(canEditScript) {
//			parent.cellObjGFSSCOLLECTAUDI.scriptContent=scriptContent;
//		}
		parent.cellObjGFSSCOLLECTAUDI.value = stepNameObj.getValue();
		parent.callbackWindwGFSSCOLLECTAUDI ();
		Ext.Msg.alert ('提示', '当前步骤保存成功!');
	}
	
	initFun ();
	
	/**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
	function getCellFun (inflag)
	{
		// 遍历所有节点
		var rootObj = modelGFSSCOLLECTAUDI.getRoot ();
		var count = modelGFSSCOLLECTAUDI.getChildCount (rootObj);
		for (var i = 0; i < count; i++)
		{
			var cells = rootObj.getChildAt (i);
			var counts = cells.getChildCount ();
			var beforeCell = null;// 上一个节点
			var afterCell = null;// 下一个节点
			var selfCell = null;// 自己
			for (var j = 0; j < counts; j++)
			{
				var cellss = cells.getChildAt (j);
				// 判断循环至的节点样式是否与传入的样式一致
				if (cellss.style == parent.cellObjGFSSCOLLECTAUDI.style)
				{
					if (cellss == parent.cellObjGFSSCOLLECTAUDI)
					{
						// 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
						selfCell = parent.cellObjGFSSCOLLECTAUDI;
					}
					else
					{
						// 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
						selfCell == null ? beforeCell = cellss : afterCell = cellss;
					}
					// 如果获取到了想要的节点，则跳出循环
					if (selfCell != null && ((inflag == 'after' && afterCell != null)
					        || (inflag == 'before' && beforeCell != null)))
					{
						break;
					}
				}
			}
			// 返回指定节点
			return inflag == 'after' ? afterCell : beforeCell;
		}
	}

});
