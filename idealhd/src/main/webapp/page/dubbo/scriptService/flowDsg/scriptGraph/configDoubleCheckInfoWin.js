/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.configDoubleCheckInfoWin', {
    extend: 'Ext.window.Window',

    title: '提交配置信息',
    autoScroll: true,
    modal: true,
    closeAction: 'destroy',
    buttonAlign: 'center',
    draggable: true,
    resizable: false,
	width : 400,
	height :355,
    
    initComponent: function() {
        var me = this;

        var jspParms = me.jspParms;
        
		Ext.define('AuditorModel', {
		    extend : 'Ext.data.Model',
		    fields : [ {
		    	name : 'loginName',
		    	type : 'string'
		    }, {
		    	name : 'fullName',
		    	type : 'string'
		    }]
		});
		var auditorStore_tap = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getExecAuditorList.do?scriptLevel=' + jspParms.scriptLevel,
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
		    fieldLabel: "审核人",
		    store: auditorStore_tap,
		    queryMode: 'local',
		    width: 390,
		    hidden:jspParms.scriptLevel==0?true:false,
		    displayField: 'fullName',
		    valueField: 'loginName',
		    labelWidth : 65,
		    editable : true,
			labelAlign : 'right',
			 listeners: { //监听 
			        render : function(combo) {//渲染 
			            combo.getStore().on("load", function(s, r, o) { 
			                combo.setValue(r[0].get('loginName'));//第一个值 
			            }); 
			        },
			        select : function(combo, records, eOpts){ 
						var fullName = records[0].raw.fullName;
						combo.setRawValue(fullName);
					},
					beforequery: function(e) {
		                var combo = e.combo;
		                if (!e.forceAll) {
		                    var value = Ext.util.Format.trim(e.query);
		                    combo.store.filterBy(function(record, id) {
		                        var text = record.get(combo.displayField);
		                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
		                    });
		                    combo.expand();
		                    return false;
		                }
		            }
			    } 
		  });
		var execStore = Ext.create('Ext.data.Store', {
		    autoLoad: false,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getExecUserList.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		var execComBox_tap = Ext.create('Ext.form.ComboBox', {
		    fieldLabel: "执行人",
		    store: execStore,
		    queryMode: 'local',
		    width: 390,
		    displayField: 'fullName',
		    valueField: 'loginName',
		    labelWidth : 65,
		    editable : true,
		    hidden:!(jspParms.execUserSwitch=='true')||jspParms.scriptLevel==0?true:false,
			labelAlign : 'right'
		  });
		execStore.load({
		    callback : function (records, operation, success)
		    {
		    	execComBox_tap.setValue (jspParms.loginUser);
		    }
	    });
		function CurentTime()
	    { 
	        var now = new Date();
	        
	        var year = now.getFullYear();       //年
	        var month = now.getMonth() + 1;     //月
	        var day = now.getDate();            //日
	        
	        var hh = now.getHours();            //时
	        var mm = now.getMinutes();          //分
	        var ss = now.getSeconds();           //秒
	        
	        var clock = year;
	        
	        if(month < 10){
	        	
	        	clock += "0";
	        }else{
	        	clock += "";
	        }
	        
	        clock +=month;
	        
	        if(day < 10){
	        	clock += "0";
	        }else{
	        	clock += "";
	        }
	            
	            
	        clock += day;
	        
	        if(hh < 10){
	        	clock += "0";
	        }else{
	        	clock += "";
	        }
	            
	            
	        clock += hh;
	        if (mm < 10) 
	        {
	        	clock += '0'; 
	        	}
	        else{
	        	clock += "";
	        }
	        clock += mm ; 
	         
	        if (ss < 10) {
	        	clock += '0'; 
	        }
	        else{
	        	clock += "";
	        }
	        clock += ss; 
	        return(clock); 
	    }
		var taskName = new Ext.form.TextField({
			name: 'taskName',
			fieldLabel: '任务名称',
			emptyText: '',
			labelWidth : 65,
			labelAlign : 'right',
			value: jspParms.serviceName+'_'+CurentTime(),
			width: 390
		});
		
		var execDesc = Ext.create('Ext.form.field.TextArea', {
	        fieldLabel: '执行描述',
	        labelWidth: 65,
	                                              height: 60,
	        maxLength: 2000,
	        value: jspParms.publishDescText,
	        width: 385,
	        autoScroll: true
	    });
		var butterflyVerison = new Ext.form.TextField({
			name: 'butterflyversion',
			fieldLabel: '单号',
			hidden:((typeof version_flag == "undefined"||!scriptOddNumberSwitch)?true:((jspParms.execUserSwitch=='true'&&version_flag==1))?false:true),
			emptyText: '',
			labelWidth : 65,
			labelAlign : 'right',
			value:butterflyV,
			readOnly:true,
			editable : false,
			width: 390
		});
        Ext.applyIf(me, {
        	  dockedItems: [{
        	        xtype: 'toolbar',
        	        border: false,
        	        dock: 'bottom',
        	        margin: '0 0 5 0',
        	        layout: {pack: 'center'},
        	        items: [{
        	            xtype: 'button',
        	            text: '确定',
        	            cls: 'Common_Btn',
        	            handler: function() {
    		  				var self = this;
    		  				var auditor = auditorComBox_tap.getValue();
    		  				if(!auditor) {
    		  					Ext.Msg.alert('提示', "没有选择审核人！");
    		  					return;
    		  				}
							//判断输入的审核人是否合法 start
		                	var displayField =auditorComBox_tap.getRawValue();
							if(!Ext.isEmpty(displayField)){
								//判断输入是否合法标志，默认false，代表不合法
								var flag = false;
								//遍历下拉框绑定的store，获取displayField
								auditorStore_tap.each(function (record) {
									//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
								    var data_fullName = record.get('fullName');
								    if(data_fullName == displayField){
								    	flag =true;
								    	//combo.setValue(record.get('loginName'));
								    }
								});
								if(!flag){
								 	Ext.Msg.alert('提示', "输入的审核人非法");
								 	auditorComBox_tap.setValue("");
								 	return;
								} 
							}
							//判断输入的审核人是否合法  end
    		  				var execUser = execComBox_tap.getValue();
    		  				var taskN = Ext.util.Format.trim(taskName.getValue());
    		  				if(Ext.isEmpty(taskN)) {
    		  					Ext.Msg.alert('提示', "任务名称不能为空！");
    		  					return;
    		  				}
    		  				
    		  				if (fucCheckLength(taskN) > 255) {
    	                        Ext.Msg.alert('提示', "任务名称不能超过255字符！");
    	                        return;
    	                    }
    		  				var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
    		  				if(Ext.isEmpty(execDescForExec)) {
    		  					Ext.Msg.alert('提示', "没有填写执行描述！");
    		  					return;
    		  				}
    		  				if(fucCheckLength(execDesc.getValue() > 2000)) {
    		  					Ext.Msg.alert('提示', "执行描述内容长度超过2000个字符！");
    		  					return;
    		  			    }
    		  				var isdelay = false;
		  					var execT = "";
    		  				if(jspParms.isScriptConvertToFlow=='true'){
    		  					if(jspParms.isTimetask==true){
    		  						isdelay = true;
    		  						execT = jspParms.taskTime;
        			  				if(isdelay && ""==execT)
        			  				{
        			  					Ext.Msg.alert('提示', "没有填写执行时间！");
        			  					return;
        			  				}
    		  					}
    		  				}
    		  				var isSaveTemplate = jspParms.isSaveTemplate;
    		  				if(isSaveTemplate) {
    		  					Ext.MessageBox.prompt('提示', '请输入常用任务名称:', function(btn, text, cfg){
    		  				        if(btn=='ok') {
    		  				        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
    		  				        		var newMsg = '<span style="color:red">常用任务名称不能为空！</span>';
    		  				                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    		  				        	} else {
    		  				        		var customName = Ext.util.Format.trim(text);
    		  				        		Ext.Ajax.request({
    		  				        	        url: 'checkCustomTemplateNameIsExist.do',
    		  				        	        params: {
    		  				        	            customName: customName,
    		  				        	            flag: jspParms.flag
    		  				        	        },
    		  				        	        method: 'POST',
    		  				        	        success: function(response, options) {
    		  				        	            if (!Ext.decode(response.responseText).success) {
    		  				        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换名称！</span>';
    		  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    		  				        	            } else {
    		  				        	            	Ext.Ajax.request({
    			  				  	  						url : 'scriptFlowExecAuditing.do',
    			  				  	  						method : 'POST',
    			  				  	  						params : {
    			  				  	  							iidForQuery : jspParms.iworkItemid,
    			  				  	  							serviceId: jspParms.iid,
    			  				  	  							auditor: auditor,
    			  				  	  							taskName: taskN,
    			  				  	  							startData: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    			  				  	  							scriptLevel: jspParms.scriptLevel,
    				  				  	  						isDelay: isdelay,
    				  					  				    	execTime: execT,
    				  					  				    	execUser:execUser,
    				  					  				    	execDesc:execDescForExec
    			  				  	  						},
    			  				  	  						success: function(response, opts) {
    			  				  	  							var success = Ext.decode(response.responseText).success;
    			  				  	  							var message = Ext.decode(response.responseText).message;
    			  				  	  							if(success) {
	    			  				  	  							if(jspParms.scriptLevel!=0){
	    			  				  	  								Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	    			  				  	  							} 
    			  				  	  								var iworkItemid = Ext.decode(response.responseText).workItemId;
    			  				  	  								self.up("window").close();
    			  				  	  							if(jspParms.scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
	   			  				  	  								 Ext.Ajax.request ({
	   			  				  	  								 url : 'scriptExecForOneRecord.do',
	   			  				  	  								 method : 'POST',
	   			  				  	  								 params :
	   			  				  	  								 {
	   			  				  	  									 iworkItemid: iworkItemid
	   			  				  	  								 },
	   			  				  	  								 success : function (response, opts)
	   			  				  	  								 {
	   			  				  	  									 var success = Ext.decode (response.responseText).success;
	   			  				  	  									 if(success && jspParms.isScriptConvertToFlow=='true') {
	   			  				  	  										 Ext.Ajax.request ({
	   			  				  	  											 url : 'scriptExecForScriptConvertFlowToOrgData.do',
	   			  				  	  											 method : 'POST',
	   			  				  	  											 params :
	   			  				  	  											 {
	   			  				  	  												 iworkItemid:iworkItemid,
	   			  				  	  												 serviceId: jspParms.iid 
	   			  				  	  											 },
	   			  				  	  											 success : function (response, opts)
	   			  				  	  											 {
	   			  				  	  												 var success = Ext.decode (response.responseText).success;
	   			 				  	  											 if(success){
	   						  	  													 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
	   						  	  													var	uuid ='';
	   				  					  											Ext.Ajax.request({
	   				  					  								                url: 'queryUuidById.do',
	   				  					  								                method: 'POST',
	   				  					  								                async: false,
	   				  					  								                params: {
	   				  					  								                	serviceId: jspParms.iid,
	   				  					  								                },
	   				  					  								                success: function(response, options) {
	   				  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
	   				  					  								                },
	   				  					  								                failure: function(result, request) {
	   				  					  								                }
	   				  					  								            });
	   						  	  												    Ext.Ajax.request({
	   						  	  													url : 'execScriptServiceStart.do',
	   						  	  													method : 'POST',
	   						  	  													params : {
	   						  	  														serviceId : jspParms.iid,
	   						  	  														uuid:uuid,
	   						  	  														serviceName : jspParms.serviceName,
	   						  	  														scriptType:0,
	   						  	  														workItemId : iworkItemid,
	   						  	  														coatId : 0,
	   						  	  														isFlow: 0
	   						  	  													},
	   						  	  													success : function(response, request) {
	   						  	  														var success = Ext.decode(response.responseText).success;
	   						  	  														if (success) {
	   						  	  															var flowId = Ext.decode(response.responseText).content;
	   						  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
	   						  	  														}
	   						  	  													},
	   						  	  													failure : function(result, request) {
	   						  	  														Ext.Msg.alert('提示', '执行失败！');
	   						  	  													}
	   						  	  												}); 
	   						  	  													 }else{
	   						  	  														Ext.MessageBox.alert("提示", "白名单任务提交成功！");
	   						  	  													 }
	   						  	  												 }

	   			  				  	  											 }
	   			  				  	  										 });
	   			  				  	  									 }else if(success && jspParms.isScriptConvertToFlow=='false'){
			   			  				  	  								 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
					   			  				  	  							var	uuid ='';
					  					  											Ext.Ajax.request({
					  					  								                url: 'queryUuidById.do',
					  					  								                method: 'POST',
					  					  								                async: false,
					  					  								                params: {
					  					  								                	serviceId: jspParms.iid,
					  					  								                },
					  					  								                success: function(response, options) {
					  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
					  					  								                },
					  					  								                failure: function(result, request) {
					  					  								                }
					  					  								            });
			  				  	  													Ext.Ajax.request({
			  				  	  													url : 'execScriptServiceStart.do',
			  				  	  													method : 'POST',
			  				  	  													params : {
			  				  	  														serviceId : jspParms.iid,
			  				  	  														uuid:uuid,
			  				  	  														serviceName : jspParms.serviceName,
			  				  	  														scriptType:0,
			  				  	  														workItemId : iworkItemid,
			  				  	  														coatId : 0,
			  				  	  														isFlow: 1
			  				  	  													},
			  				  	  													success : function(response, request) {
			  				  	  														var success = Ext.decode(response.responseText).success;
			  				  	  														if (success) {
			  				  	  															var flowId = Ext.decode(response.responseText).content;
			  				  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
			  				  	  														}
			  				  	  													},
			  				  	  													failure : function(result, request) {
			  				  	  														Ext.Msg.alert('提示', '执行失败！');
			  				  	  													}
			  				  	  												}); 
			  				  	  													 }
	   			  				  	  									 }
	   			  				  	  								 }
	   			  				  	  							 });
				  				  	  							    }
    				  				  	  							Ext.Ajax.request({
    				  		        	        	                    url: 'saveFlowCustomTemplate.do',
    				  		        	        	                    method: 'POST',
    				  		        	        	                    params: {
    				  		        	        	                    	customName: customName,
    				  		        	        	                    	serviceId: jspParms.iid,
    				  		        	        	                        data: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    				  		        	        	                        audiUserLoginName: auditor,
    						  				  	  							taskName: taskN,
    				  		        	        	                        flag: jspParms.flag
    				  		        	        	                    },
    				  		        	        	                    success: function(response, options) {
    				  		        	        	                        var success1 = Ext.decode(response.responseText).success;
    				  		        	        	                        //var message1 = Ext.decode(response.responseText).message;
    				  		        	        	                        if (success1) {
    				  		        	        	                            Ext.MessageBox.show({
    				  		        	        	                                title: "提示",
    				  		        	        	                                msg: '请求已经发送到审核人<br>模板保存成功！',
    				  		        	        	                                buttonText: {
    				  		        	        	                                    yes: '确定'
    				  		        	        	                                },
    				  		        	        	                                buttons: Ext.Msg.YES
    				  		        	        	                            });
    				  		        	        	                        }
    				  		        	        	                    },
    				  		        	        	                    failure: function(result, request) {
    				  		        	        	                        Ext.MessageBox.show({
    				  		        	        	                            title: "提示",
    				  		        	        	                            msg: "请求已经发送到审核人<br>模板保存失败",
    				  		        	        	                            buttonText: {
    				  		        	        	                                yes: '确定'
    				  		        	        	                            },
    				  		        	        	                            buttons: Ext.Msg.YES
    				  		        	        	                        });
    				  		        	        	                    }
    		
    				  		        	        	                });
    			  				  	  							} else {
    			  				  	  								Ext.MessageBox.alert("提示", message);
    			  				  	  							}
    			  				  	  						},
    			  				  	  						failure: function(result, request) {
    			  				  	  							secureFilterRs(result,"操作失败！");
    			  				  	  						}
    			  				  	  					});
    		  				        	            }
    		  				        	        },
    		  				        	        failure: function(result, request) {}
    		  				        	    });
    		  				        	}
    		  				        	
    		  				        }
    		  				        
    		  				    });
    		  				} else {
    		  					Ext.Ajax.request({
    		  						url : 'scriptFlowExecAuditing.do',
    		  						method : 'POST',
    		  						params : {
    		  							iidForQuery : jspParms.iworkItemid ,
    		  							serviceId: jspParms.iid ,
    		  							auditor: auditor,
    		  							taskName: taskN,
    		  							startData: JSON.stringify(actStartInfo[jspParms.rootEditer]),
    		  							scriptLevel: jspParms.scriptLevel,
    		  							isDelay:isdelay,
    			  				    	execTime:execT,
    			  				    	execUser:execUser,
    			  				    	execDesc:execDescForExec
    		  						},
    		  						success: function(response, opts) {
    		  							var success = Ext.decode(response.responseText).success;
    		  							var message = Ext.decode(response.responseText).message;
    		  							if(success) {
    		  								var iworkItemid = Ext.decode(response.responseText).workItemId;
    		  								if(jspParms.scriptLevel!=0){
    		  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
			  	  							}
    		  								self.up("window").close();
    		  								if(jspParms.scriptLevel==0){//白名单，直接调用双人复核中，同意执行方法
			  	  								 Ext.Ajax.request ({
			  	  								 url : 'scriptExecForOneRecord.do',
			  	  								 method : 'POST',
			  	  								 params :
			  	  								 {
			  	  									 iworkItemid: iworkItemid
			  	  								 },
			  	  								 success : function (response, opts)
			  	  								 {
			  	  									 var success = Ext.decode (response.responseText).success;
			  	  									 if(success && jspParms.isScriptConvertToFlow=='true') {
			  	  										 Ext.Ajax.request ({
			  	  											 url : 'scriptExecForScriptConvertFlowToOrgData.do',
			  	  											 method : 'POST',
			  	  											 params :
			  	  											 {
			  	  												 iworkItemid:iworkItemid,
			  	  												 serviceId: jspParms.iid 
			  	  											 },
			  	  											 success : function (response, opts)
			  	  											 {
			  	  												 var success = Ext.decode (response.responseText).success;
				  	  											 if(success){
			  	  													 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
			  	  														var	uuid ='';
		  					  											Ext.Ajax.request({
		  					  								                url: 'queryUuidById.do',
		  					  								                method: 'POST',
		  					  								                async: false,
		  					  								                params: {
		  					  								                	serviceId: jspParms.iid,
		  					  								                },
		  					  								                success: function(response, options) {
		  					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
		  					  								                },
		  					  								                failure: function(result, request) {
		  					  								                }
		  					  								            });
			  	  													Ext.Ajax.request({
			  	  													url : 'execScriptServiceStart.do',
			  	  													method : 'POST',
			  	  													params : {
			  	  														serviceId : jspParms.iid,
			  	  														uuid:uuid,
			  	  														serviceName : jspParms.serviceName,
			  	  														scriptType:0,
			  	  														workItemId : iworkItemid,
			  	  														coatId : 0,
			  	  														isFlow: 0
			  	  													},
			  	  													success : function(response, request) {
			  	  														var success = Ext.decode(response.responseText).success;
			  	  														if (success) {
			  	  															var flowId = Ext.decode(response.responseText).content;
			  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
			  	  														}
			  	  													},
			  	  													failure : function(result, request) {
			  	  														Ext.Msg.alert('提示', '执行失败！');
			  	  													}
			  	  												}); 
			  	  													 }else{
				  	  														Ext.MessageBox.alert("提示", "白名单任务提交成功！");
			  	  													 }
			  	  												 }

			  	  											 }
			  	  										 });
			  	  									 }else if(success && jspParms.isScriptConvertToFlow=='false'){
		  				  	  								 if(!isdelay){//不是定时任务就直接调用任务执行中的执行方法
				  				  	  								var	uuid ='';
	 					  											Ext.Ajax.request({
	 					  								                url: 'queryUuidById.do',
	 					  								                method: 'POST',
	 					  								                async: false,
	 					  								                params: {
	 					  								                	serviceId: jspParms.iid,
	 					  								                },
	 					  								                success: function(response, options) {
	 					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
	 					  								                },
	 					  								                failure: function(result, request) {
	 					  								                }
	 					  								            });
			  	  													Ext.Ajax.request({
			  	  													url : 'execScriptServiceStart.do',
			  	  													method : 'POST',
			  	  													params : {
			  	  														serviceId : jspParms.iid,
			  	  														uuid:uuid,
			  	  														serviceName : jspParms.serviceName,
			  	  														scriptType:0,
			  	  														workItemId : iworkItemid,
			  	  														coatId : 0,
			  	  														isFlow: 1
			  	  													},
			  	  													success : function(response, request) {
			  	  														var success = Ext.decode(response.responseText).success;
			  	  														if (success) {
			  	  															var flowId = Ext.decode(response.responseText).content;
			  	  															Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
			  	  														}
			  	  													},
			  	  													failure : function(result, request) {
			  	  														Ext.Msg.alert('提示', '执行失败！');
			  	  													}
			  	  												}); 
			  	  													 }
  				  	  									 }
			  	  								 }
			  	  							 });
	  	  							    }
    		  								if(jspParms.iworkItemid) {
    		  									if(jspParms.from==1) {
    		  										messageWindow1.close();
    		  									} else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
							    					    		messageWindow_ssq.close();
							    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
							    			    }else {
    		  										messageWindow.getLoader ().load (
    		  												{
    		  													url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
    		  													autoLoad : true,
    		  													scripts : true
    		  												});
    		  										messageWindow.setTitle ('待办事项');
    		  									}
    		  								}
    		  							} else {
    		  								Ext.MessageBox.alert("提示", message);
    		  							}
    		  						},
    		  						failure: function(result, request) {
    		  							secureFilterRs(result,"操作失败！");
    		  						}
    		  					});
    		  				}
    		  			}
        	        }]
        	    }],
        	    items:[taskName, auditorComBox_tap,execComBox_tap,butterflyVerison,execDesc]
        });
        
        me.callParent(arguments);
    }

});