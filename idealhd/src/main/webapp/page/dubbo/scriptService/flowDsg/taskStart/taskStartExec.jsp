<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	//任务执行 跳转开关，浦发需求 开启后 直接跳转到最后一层展示，不展示图形 
	boolean taskExecShowSwitch = Environment.getInstance().getTaskExecShowSwitch();
%>
<html>
<head>
<script>
    var tbsexec_count = '<%=request.getAttribute("count")%>';
    var whichDo = '<%=request.getAttribute("whichDo")%>';
    var taskExecShowSwitch = <%=taskExecShowSwitch%>;
    var taskName = '<%=request.getParameter("taskName")==null?"":request.getParameter("taskName")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/taskStart/taskStartExec.js"></script>
</head>
<body>
<div id="scriptexec_taskexec_area" style="width: 100%;height: 100%">
</div>
</body>
</html>