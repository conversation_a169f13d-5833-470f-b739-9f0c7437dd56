/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.submitFormPanel', {
    extend: 'Ext.form.Panel',
    alias: 'widget.submitForm',

    requires: [
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.button.Button',
        'Ext.toolbar.Spacer'
    ],

    height: '',
    title: '',

    layout: {
		type: 'hbox',
		padding:'5',
		align:'top'
	},
	
	studioValues: {
		closeValue : 1
	},
	
	initOperBtn: function(n){
		var me = this;
		console.log(me);
		n = parseInt(n);
		var editerName = me.editerName;
		var jspParms = me.jspParms;

		if(jspParms.customId!=undefined&&jspParms.customId!=''&&n==1)
		{
			n = 7;
		}
		if(jspParms.customId!=undefined&&jspParms.customId!=''&&n==2)
		{
			n = 8;
		}
		
		var radioGroup     		= me.items.get(0);
		var isSaveTemplateCk	= me.items.get(1);
		var customNameTemplate	= me.items.get(2);
		var saveButton     		= me.items.get(4);
    	var resetButton    		= me.items.get(5);
    	var startButton    		= me.items.get(6);
    	var submitButton   		= me.items.get(7);
    	var testButton     		= me.items.get(8);
    	var versionButton  		= me.items.get(9);
    	var baseInfoButton 		= me.items.get(10);
    	var backButton     		= me.items.get(11);
    	var helpButton     		= me.items.get(12);
    	var warnButton 		= me.items.get(13);
		
		if(me.jspParms.ifrom==null||me.jspParms.ifrom==undefined||me.jspParms.ifrom=='null'||me.jspParms.ifrom=="")
		{
			backButton.hide();
		}else
		{
			backButton.show();
		}
		
		
		switch(n)
		{
		case 1: // 作业编排——编辑
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('create');
			}
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].actionType = 'create';
			}
			radioGroup.setValue({ needAgent: '1'} );
			radioGroup.show();
			isSaveTemplateCk.hide();
			customNameTemplate.hide();
			saveButton.show();
			submitButton.hide();
			testButton.hide();
			resetButton.show();
			baseInfoButton.hide();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 2:// 作业编排——测试
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('exec');
			}
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].actionType = 'exec';
			}
			radioGroup.show();
			isSaveTemplateCk.show();
			customNameTemplate.hide();
			saveButton.hide();
			submitButton.hide();
			testButton.show();
			resetButton.hide();
			baseInfoButton.hide();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			if(GRAPHS[editerName]!=undefined)
				GRAPHS[editerName].init(GRAPHS[editerName].editorUi);
		  break;
		case 3: // 我的作业——编辑
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('edit');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'edit';
			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			customNameTemplate.hide();
			saveButton.show();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			warnButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			break;
		case 4: // 我的作业——测试
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('model');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'model';
			}
			radioGroup.hide();
			isSaveTemplateCk.show();
			customNameTemplate.hide();
//			isSaveTemplateCk.disable();
			saveButton.hide();
			submitButton.hide();
			testButton.show();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 5: // 我的作业——查看
//			执行代码块 2
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('view');
//				GRAPHS[jspParms.rootEditer].actionType = 'view';
			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			customNameTemplate.hide();
//			isSaveTemplateCk.disable();
			saveButton.hide();
			submitButton.hide();
			testButton.show();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.show();//版本回退，Store内容暂时没法获取
			warnButton.hide();
			break;
		case 6: // 我的作业——模板
//			执行代码块 2
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('model');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'model';
			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			isSaveTemplateCk.setValue(true);
			customNameTemplate.show();
//			isSaveTemplateCk.disable();
			saveButton.show();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 66: // 六、全部作业——查看：版本列表，基本信息、返回
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('view');
				GRAPHS[jspParms.rootEditer].actionType = 'view';
			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			isSaveTemplateCk.setValue(true);
			customNameTemplate.hide();
			saveButton.hide();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 7:
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('edit');
				GRAPHS[jspParms.rootEditer].actionType = 'edit';
			}
			radioGroup.show();
			isSaveTemplateCk.hide();
			isSaveTemplateCk.setValue(true);
			customNameTemplate.show();
//			isSaveTemplateCk.disable();
			saveButton.show();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.show();
			versionButton.hide();
			warnButton.hide();
			break;
		case 8:
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('exec');
				GRAPHS[jspParms.rootEditer].actionType = 'exec';
			}
			radioGroup.show();
			isSaveTemplateCk.hide();
			isSaveTemplateCk.setValue(true);
			customNameTemplate.show();
//			isSaveTemplateCk.disable();
			saveButton.show();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.show();
			versionButton.hide();
			warnButton.hide();
			break;
//		case 9:
//			执行代码块 2
//			break;
		case 10:
			radioGroup.hide();
			isSaveTemplateCk.hide();
			isSaveTemplateCk.setValue(true);
			customNameTemplate.hide();
			saveButton.hide();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
//		case 11:
//			执行代码块 2
//			break;
		case 12:
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('audi');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'audi';
			}
			radioGroup.hide();
			isSaveTemplateCk.show();
			customNameTemplate.hide();
			saveButton.hide();
			submitButton.show();
			testButton.hide();
			resetButton.show();
			baseInfoButton.hide();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 13:
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('audi');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'audi';
			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			customNameTemplate.hide();
			saveButton.hide();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 14:// 十四、任务申请——定制模板：模板名称、保存、基本信息、返回
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('audi');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'audi';
			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			customNameTemplate.show();
			saveButton.show();
			submitButton.hide();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 15:// 十五、常用任务——编辑：模板名称、提交、保存、基本信息、返回
//			if(GRAPHS[editerName]!=undefined)
//			{
//				GRAPHS[editerName].controlSidbar('audi');
//			}
//			if(GRAPHS[jspParms.rootEditer]!=undefined)
//			{
//				GRAPHS[jspParms.rootEditer].actionType = 'audi';
//			}
			radioGroup.hide();
			isSaveTemplateCk.hide();
			customNameTemplate.show();
			saveButton.show();
			submitButton.show();
			testButton.hide();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		case 22:
			if(GRAPHS[editerName]!=undefined)
			{
				GRAPHS[editerName].controlSidbar('exec');
			}
			if(GRAPHS[jspParms.rootEditer]!=undefined)
			{
				GRAPHS[jspParms.rootEditer].actionType = 'exec';
			}
			radioGroup.hide();
			isSaveTemplateCk.show();
			customNameTemplate.hide();
			saveButton.hide();
			submitButton.hide();
			testButton.show();
			resetButton.hide();
			baseInfoButton.show();
			helpButton.hide();
			startButton.hide();
			versionButton.hide();
			warnButton.hide();
			break;
		default:
			me.hide();
		};
		me.studioValues.closeValue = n;
//		if(jspParms.isShowInWindow==1)
//		{
//			me.hide();
//		}
	},
	
    initComponent: function() {
        var me = this;
        
        var jspParms = me.jspParms;
        
        var saveButtonFunc = me.saveButtonFunc;
//        var baseInfoFn = me.baseInfoFn;
        
        var radioGroup = Ext.create('Ext.form.RadioGroup', {
    		anchor: 'none',
            layout: { autoFlex: false },
            defaults: { margin: '3 5 0 0' },
            cls: 'customer-radio-group',
            items: [
                {boxLabel: '编辑', name: 'needAgent', inputValue: '1'},
                {boxLabel: '测试', name: 'needAgent', inputValue: '2'}
            ],
            listeners: {
                change: function (field, newValue, oldValue) {
                	if(oldValue.needAgent=='1' && newValue.needAgent=='2')
                	{
                		saveButtonFunc('switch');
                		jspParms.actionType = 'test';
                	}
                	if(oldValue.needAgent=='2' && newValue.needAgent=='1')
                	{
                		jspParms.actionType = 'create';
                		me.initOperBtn(1);// 调整为作业编排——编辑模式
                	}
                	
                	// 调用切换编辑和测试状态的方法
                	// 编辑状态到测试的切换过程
                	// 1.追加提示并保存步骤。要显示
                	// 2.修改保存url
                	// 3.显示测试按钮
                	// 测试到编辑状态直接进行切换
                	// 1.修改保存url
                	// 2.显示测试按钮
                	// 3.
                	// 一、作业编排：编辑、测试、保存、重置
                	// 二、我的作业——编辑：保存、返回
                	// 三、我的作业——测试：是否保存为模板、启动、基本信息、返回
                	// 四、我的作业——查看：版本列表，测试、版本回退、基本信息、返回
                	// 五、我的作业——定制模板：模板名称、保存、基本信息、返回
                	// 六、全部作业——查看：版本列表，基本信息、返回
                	// 七、测试模板——编辑：编辑、测试、模板名称、保存、启动、基本信息、返回
                	// 八、作业历史——编辑：编辑、测试、是否保存为模板、保存、基本信息、返回
                	// 九、作业历史——监控：单独监控工具条
                	// 十、作业库——查看：基本信息
                	// 十一、作业库——复制：编辑、测试、保存、重置
                	// 十二、任务申请——创建任务：是否保存为模板、提交、基本信息、返回
                	// 十三、任务申请——查看： 基本信息
                	// 十四、任务申请——定制模板：模板名称、保存、基本信息、返回
                	// 十五、常用任务——编辑：模板名称、提交、保存、基本信息、返回
                	// 十六、我的收藏——创建任务：是否保存为模板、提交、基本信息、返回
                	// 十七、我的收藏——查看：基本信息
                	// 十八、任务权限——查看：基本信息
                	// 十九、工作流调用活动弹出页——一级
                	// 二十、工作流调用活动弹出页——末级：基本信息、返回
                	// 二十一、工作流调用活动弹出页——中间级
                	// 二十二、总览——测试
                	// 二十三、总览——查看
                	// 二十四、总览——作业编辑
                	// 二十五、双人复核——任务审核
                	// 二十六、双人复核——任务打回
                }
            }
    	});
        
        radioGroup.setValue({ needAgent: jspParms.needAgent} );
        var isSaveTemplateCk = Ext.create('Ext.form.field.Checkbox', {
    		checked : false,
    		boxLabel: jspParms.flag=='0'?'是否保存为模板':'是否保存为常用任务'
    	});
        
        var customNameTemplate = Ext.create('Ext.form.field.Text', {
            padding: '0 5 0 0',
            fieldLabel: jspParms.flag=='0'?'模板名称':'常用任务名称',
            labelWidth: 90,
            width: 300,
            value : jspParms.customName
        });
        
    	var saveButton     = Ext.create('Ext.Button', { name : 'saveButton', text : '保存' , handler : saveFn });
    	var resetButton    = Ext.create('Ext.Button', { name : 'resetButton', text : '重置' , handler : resetFn });
    	var startButton    = Ext.create('Ext.Button', { name : 'startButton', text : '启动', handler : startFn  });
    	var submitButton   = Ext.create('Ext.Button', { name : 'submitButton', text : '提交', handler : sendAudi });
    	var testButton     = Ext.create('Ext.Button', { name : 'testButton', text : '测试', handler : testFn});
    	var versionButton  = Ext.create('Ext.Button', { name : 'versionButton', text : '版本回退' , handler : versionBack});
    	var baseInfoButton = Ext.create('Ext.Button', { name : 'baseInfoButton', text : '基本信息' , handler: me.baseInfoFn});
    	var warnButton 	   = Ext.create('Ext.Button', { name : 'warnButton', text : '影响信息' , handler: warnningInfoFn});
    	var backButton     = Ext.create('Ext.Button', { name : 'backButton', text : '返回', handler : backFn});
    	var helpButton     = Ext.create('Ext.Button', { name : 'helpButton', text : '帮助' });
    	
    	function sendAudi(){
    		var isReady = checkJobConfigIsOK();
    		if(isReady) {
    			
    			var templateData = ScriptUtils.getFlowCustomTemplateData(jspParms.customId,jspParms.flag);
    			var taskNameText = templateData.taskNameText;
    			var audiLoginUser = templateData.audiLoginUser;
    			
    			jspParms.taskName = taskNameText;
    			jspParms.audiLoginUser = audiLoginUser;
    			var configDoubleCheckInfoWin = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.configDoubleCheckInfoWin', {
    				jspParms : jspParms
    			});
    			configDoubleCheckInfoWin.show();
    		}

    	}
    	
    	function startFn(){
    		Ext.MessageBox.buttonText.yes = "确定"; 
    		Ext.MessageBox.buttonText.no = "取消"; 
    		Ext.Msg.confirm("请确认", "是否确定启动该模板", function(id){
    			if(id=='yes') {
    				if(jspParms.actionType=='test') {
    					testFn();
    				} else {
    					var isOk = false;
    					Ext.Ajax.request({
    	        			url: 'validFlowCustomTemplateData.do',
    	        			method: 'POST',
    	        			async: false,
    	        			params: {
    	        				iid: jspParms.customId,
    	        				flag: jspParms.flag
    	        			},
    	        			success: function(response, options) {
    	        				var success = Ext.decode(response.responseText).success;
    	        				if(success) {
    	        					isOk = true;
    	        				} else {
    	        					var errorMessage = Ext.decode(response.responseText).message;
    	        					Ext.Msg.alert('提示', errorMessage);
    	        				}
    	        			},
    	        			failure: function(result, request) {
    	        				Ext.Msg.alert('提示', message+'<br>校验模板启动数据信息失败！');
    	        			}
    	        		});
    					
    					if(!isOk) {
    						return;
    					}
    					
    					Ext.Ajax.request({
            				url: 'startFlowCustomTemplate.do',
            				method: 'POST',
            				params: {
            					iid: jspParms.customId,
            					flag: jspParms.flag
            				},
            				success: function(response, options) {
            					var success = Ext.decode(response.responseText).success;
            					var message = Ext.decode(response.responseText).message;
            					Ext.Msg.alert('提示', message);
            				},
            				failure: function(result, request) {
            					Ext.Msg.alert('提示', '模板启动失败！');
            				}
            			});
    				}
    			}
    		});
    	
    	}
    	
    	function saveFn(){
    		saveButtonFunc(jspParms.actionType);
    	};
    	
    	function baseFn(){
    		baseInfoFn();
    	};
    	
    	
    	function backFn(){
            destroyRubbish();
            contentPanel.getLoader().load({
              url : jspParms.ifrom,
              params: {
            	  filter_templateNameQuery:filter_templateNameQuery,
          		  filter_serverNameQuery:filter_serverNameQuery,
          		  filter_scriptStateQuery,filter_scriptStateQuery,
          		  filter_scriptNameQuery:filter_scriptNameQuery,
            	  menuId: jspParms.menuId
              },
              scripts: true
            });
            if (Ext.isIE) {
              CollectGarbage();
            }
    	};
    	
    	
    	
    	function warnningInfoFn(){
    		Ext.define('warnningModel', {
    		    extend : 'Ext.data.Model',
    		    fields : [{
    		      name : 'iid',
    		      type : 'int'
    		    }, {
    		          name : 'serviceName',
    		          type : 'string'
    		    }, {
    		          name : 'sysName',
    		          type : 'string'
    		    }, {
    			      name : 'bussName',
    			      type : 'string'
    			}, {
    				  name : 'version',
    				  type : 'string'
    			}, {
    				  name : 'user',
    				  type : 'string'
    			}]
    		  });
    		var warnningStore = Ext.create('Ext.data.Store', {
    		    autoLoad: true,
    		    autoDestroy: true,
    		    model: 'warnningModel',
    		    proxy: {
    		      type: 'ajax',
    		      url: 'scriptCallSearch.do',
    		      reader: {
    		        type: 'json',
    		        root: 'dataList',
    		        totalProperty: 'totalCount'
    		      }
    		    }
    		  });
    		warnningStore.on('beforeload', function (store, options) {
    		    	var new_params = {  
    		    			iid:jspParms.serviceId
    		    	};
    		    	
    		    	Ext.apply(warnningStore.proxy.extraParams, new_params);
    		    });
    		var warnningColumns = [{ text: '序号', xtype:'rownumberer', width: 40 },
    		             		{ text: '主键',  dataIndex: 'iid',hidden:true},
    		             		{ text: '服务名称',  dataIndex: 'serviceName',flex:1},
    		             		{ text: '一级分类',  dataIndex: 'sysName',width:120},
    		             		{ text: '二级分类',  dataIndex: 'bussName',width:100},
    		                    { text: '版本',  dataIndex: 'version', width: 80,renderer:function(value,p,record,rowIndex){
    		        				if(value) {
    		        					return value;
    		        				} else {
    		        					return '无版本号';
    		        				}
    		        			}},
    		                    { text: '创建用户',  dataIndex: 'user',width:120}
    		             		];
    		var warnningGrid1 = Ext.create('Ext.grid.Panel', {
    			region: 'center',
    			autoScroll: true,
    		    store : warnningStore,
    		    border:false,
    		    columnLines : true,
    		    columns : warnningColumns
    		});
    		Ext.create('Ext.window.Window', {
                title: '提示信息,发布该作业将影响以下作业！',
                closable: true,
                closeAction: 'hide',
                modal: true,
                width: 600,
                minWidth: 350,
                height: 300,
                layout: {
                    type: 'border',
                    padding: 5
                },
                items: [warnningGrid1],
                dockedItems : [ {
					xtype : 'toolbar',
					dock : 'bottom',
					layout: {pack: 'center'},
					items : [  { 
		  			xtype: "button", 
		  			cls:'Common_Btn',
		  			text: "确定", 
		  			handler: function () {
		  				this.up("window").close();
		  			}
		  		}]
			}]
            }).show();
    	}
    	
    	function resetFn (){
            destroyRubbish();
            Ext.MessageBox.buttonText.yes = "确定";
            Ext.MessageBox.buttonText.no = "取消";
            if(me.jspParms.serviceId==0){
            	Ext.Msg.confirm("请确认", "是否确认重置所有内容？", function(id) {
                    if (id == 'yes') {
                    	if("undefined" != typeof(saveWinObjOutSide))
                    	{
                    		saveWinObjOutSide.destroy();
                    		saveWinObjOutSide = null;
                    	}
                    	me.resetMainPanel();
    			        contentPanel.getLoader().load({
    				          url : 'flowCustomizedCreateScriptService.do',
    				          params: me.jspParms,
    	        				scripts: true});
                    };
                });
            }else{
            	Ext.Msg.confirm("请确认", "是否确认重置所有内容？<br>将恢复到最后一次保存时的状态！", function(id) {
                    if (id == 'yes') {
                    	destroyRubbish();
                    	me.jspParms.load = true;
            	        contentPanel.getLoader().load({
            	          url : 'flowCustomizedInitScriptService.do',
            	          params: {
            					iid: me.jspParms.serviceId,
            					actionType:'edit',
            					flag: me.jspParms.flag,
            					ifrom: me.jspParms.ifrom
            				},
            				scripts: true});
            	       
                    };
                });
            
            }
           
            if (Ext.isIE) {
              CollectGarbage();
            }
          }
    	
        Ext.applyIf(me, {
            items: [ 
            	radioGroup, isSaveTemplateCk,customNameTemplate, //编辑、测试；是否保存为模板；模板名称；
            	{
                    xtype:'tbspacer',
                    flex:1
                },
            	saveButton, resetButton, startButton,submitButton,testButton ,versionButton,baseInfoButton, backButton, helpButton,warnButton,
            	//保存、重置、启动、提交、测试、版本回退、基本信息、返回、影响信息
            	{
                    xtype:'tbspacer',
                    flex:1
                },{
                    xtype:'tbspacer',
                    width:235
                }]
        });
    	
        me.callParent(arguments);
        
        // 只有在控件已经渲染后，才能从items中获取到目标控件。
        if(jspParms.needAgent=='1')
    		me.initOperBtn(1);
    	else if(jspParms.needAgent=='2')
    		me.initOperBtn(2);
    	
    	if(jspParms.actionType=='model')
    	{
    		me.initOperBtn(6);
    	}else if(jspParms.actionType=='create')
    	{
    		me.initOperBtn(1);
    	}else if(jspParms.actionType=='edit')
    	{
    		me.initOperBtn(3);
    	}else if(jspParms.actionType=='exec')
    	{
    		me.initOperBtn(2);
    	}else if(jspParms.actionType=='test')
    	{
    		me.initOperBtn(4);
    	}else if(jspParms.actionType=='view')
    	{
    		me.initOperBtn(5);
    	}else if(jspParms.actionType=='view1')
    	{
    		me.initOperBtn(66);
    	}
    	
    	if(jspParms.submitType=='ea')
    	{
    		me.initOperBtn(12);
    	}else if(jspParms.submitType=='tv')
    	{
    		me.initOperBtn(13);
    	}else if(jspParms.submitType=='tm')
    	{
    		me.initOperBtn(14);
    	}else if(jspParms.submitType=='te')
    	{
    		me.initOperBtn(15);
    	}else if(jspParms.submitType=='ms')
    	{
    		me.initOperBtn(12);
    	}else if(jspParms.submitType=='ah')
    	{
    		me.initOperBtn(6);
    	}else if(jspParms.submitType=='cf')
    	{
    		me.hide();
    	}
    	
    	
    	
    	function checkCustomTemplateNameIsExist(text,flag,cfg){
    		var customName = Ext.util.Format.trim(text);
    		var ret = false;
    		Ext.Ajax.request({
      	        url: 'checkCustomTemplateNameIsExist.do',
      	        params: {
      	            customName: customName,
      	            flag: flag
      	        },
      	        async: false,
      	        method: 'POST',
      	        success: function(response, options) {
      	        	 if (!Ext.decode(response.responseText).success) {
       	                var newMsg = '<span style="color:red">模板名已存在,请更换模板名！</span>';
			            Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
			            ret = false;
       	            } else {
       	            	ret = true;
       	            }
      	        },
      	        failure: function(result, request) { ret = false}
    		});
    		return ret;
    	}
    	
    	
    	function startScriptServiceFlow(serviceId,allStartParams,flag){
    		var ret = {
    			message : '',
    			success : ''
    		};
			Ext.Ajax.request({
				url :'startScriptServiceFlow.do',
				method: 'POST',
				async: false,
				params:{
					serviceId: serviceId,
					data:JSON.stringify(allStartParams),
					flag:flag
				},
				success: function ( response, options) {
					ret = Ext.decode(response.responseText);
				},
				failure: function ( result, request){
					Ext.Msg.alert ('提示', "作业启动失败！");
				}
			});
            return ret;
    	}
    	
    	function saveFlowCustomTemplate(message,customName,serviceId,allStartParams,flag){
			Ext.Msg.alert ('提示', message);
			Ext.Ajax.request({
                url: 'saveFlowCustomTemplate.do',
                method: 'POST',
                params: {
                	customName: customName,
                	serviceId: serviceId,
                    data: JSON.stringify(allStartParams),
                    flag: flag
                },
                success: function(response, options) {
                    var success1 = Ext.decode(response.responseText).success;
                    var message1 = Ext.decode(response.responseText).message;
                    if (success1) {
                    	Ext.Msg.alert ('提示', message +'<br>模板保存成功！');
                    }
                },
                failure: function(result, request) {
                	Ext.Msg.alert ('提示', message + "<br>模板保存失败");
                }
            });
		
    	}
    	
    	function testFn(){
    		
    		var editerName = me.editerName;
    		var jspParms = me.jspParms;
    		if(!checkJobConfigIsOK())
    		{
    			return;
    		}
    		console.log('testFn',1);
        	Ext.Msg.confirm("请确认", "是否进行测试？", function(id) {
    			if (id == 'yes') {
    				console.log('testFn',2);
    	    		var isSaveTemplate = isSaveTemplateCk.getValue();
    	    		if(isSaveTemplate) {
    	    			Ext.MessageBox.prompt('提示', '请输入流程模板名称:', function(btn, text, cfg){
    	    				if(btn=='ok') {
    	    					console.log('testFn',3);
    	    					if(Ext.isEmpty(Ext.util.Format.trim(text))) {
    	    						var newMsg = '<span style="color:red">流程模板名称不能为空！</span>';
    					                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
    					        	} else {
    					        		console.log('testFn',4);
    					        		if(checkCustomTemplateNameIsExist(text,jspParms.flag,cfg)){
    					        			console.log('testFn',5);
    					        			var startRet = startScriptServiceFlow(jspParms.serviceId,actStartInfo[jspParms.rootEditer],jspParms.flag);
    					        			if(startRet.success)
    				        				{
    					        				console.log('testFn',6);
    					        				saveFlowCustomTemplate(startRet.message,text,jspParms.serviceId,actStartInfo[jspParms.rootEditer],jspParms.flag);
    				        				}else{
    				        					Ext.Msg.alert ('提示', "作业启动失败！<br>模板尚未保存！");
    				        				}
    					        		}
    					        	}
    						 }
    					});
    	    		}else{
    	    			var startRet = startScriptServiceFlow(jspParms.serviceId,actStartInfo[jspParms.rootEditer],jspParms.flag);
    	    			if(startRet.success)
        				{
    	    				Ext.Msg.alert ('提示', "作业启动成功！");
        				}else{
        					Ext.Msg.alert ('提示', "作业启动失败！");
        				}
    	    		}
    			}
    		});
    	};
    	
	function versionBack(){
    		var editerName = me.editerName;
    		var jspParms = me.jspParms;
    			var selectedRows = parent.versionGrid.getSelectionModel().getSelection();
    			if (selectedRows.length ==1) {
    				var noVersionId = -1;
    				var noVersionUuid = "";
    				var selectedRow = selectedRows[0];
    				if("无版本号"==selectedRow.data.onlyVersion) {
    					Ext.Msg.alert('提示', '该版本无版本号，无法回退！');
    					return;
    				} else {
    					var canRoll = false;
    					var hasNoVersion = false;
    					versionStore.each(function(record) {   
    				       if(record.get('onlyVersion')=="无版本号") {
    				            hasNoVersion = true;
    				            noVersionId = record.get('iid');
    				            noVersionUuid = record.get('uuid');
    				            return;
    				       }
    				    });  
    				    
    				    versionStore.each(function(record) {   
    				       if(selectedRow.data.iid!=record.get('iid')) {
    			    		   if(parseFloat(selectedRow.data.onlyVersion)<parseFloat(record.get('onlyVersion'))) {
    				    		   canRoll = true;
    				    		   return;
    					       }
    			    	   }
    				    }); 
    				    if(hasNoVersion) {
    						 Ext.MessageBox.buttonText.yes = "确定"; 
    	                     Ext.MessageBox.buttonText.no = "取消"; 
    				       	 Ext.Msg.confirm("请确认", "是否要回退该版本，如强制回退将会覆盖现在无版本号中内容！",
    	                     function(button, text) {
    	                     if (button == "yes") {
    					    	Ext.Ajax.request({
    							url : 'hasVersionRollBack.do',
    							method : 'POST',
    							sync : true,
    							params : {
    								iid : selectedRow.data.iid,
    								oldId : noVersionId,
    								oldUuid:noVersionUuid,
    								uuid:selectedRow.data.uuid
    							},
    							success : function(response, request) {
    								var success = Ext.decode(response.responseText).success;
    								if (success) {
    									Ext.Msg.alert('提示', '版本回退成功！');
    									var lastId = Ext.decode(response.responseText).lastId;
    									versionStore.load();
    								} else {
    									Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
    								}
    							},
    							failure : function(result, request) {
    								secureFilterRs(result, "版本回退失败！");
    							}
    						});  
    	                     }else{
    	                     	return;
    	                     }
    	                      });
    					
    				    } else {
    				    	if(canRoll) {
    							Ext.Ajax.request({
    								url : 'versionRollBackForFlow.do',
    								method : 'POST',
    								sync : true,
    								params : {
    									iid : iid,
    									uuid:uuid
    								},
    								success : function(response, request) {
    									var success = Ext.decode(response.responseText).success;
    									if (success) {
    										Ext.Msg.alert('提示', '版本回退成功！');
    										var lastId = Ext.decode(response.responseText).lastId;
    										versionStore.load();
    									} else {
    										Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
    									}
    								},
    								failure : function(result, request) {
    									secureFilterRs(result, "版本回退失败！");
    								}
    							});
    				    	} else{
    							Ext.Msg.alert('提示', '选择的版本是最新版本，无法回退！');
    							return;
    						}
    				    }
    					
    				}
    			} else {
    				return;
    			}
    			
    		
    	};
    	
    	function checkJobConfigIsOK(){
    		var editerName = me.editerName;
    		var jspParms = me.jspParms;
    		var serviceId = jspParms.serviceId;
    		var isJobConfigOk = true;
    		Ext.Ajax.request({
    			url :'checkJobConfigIsOK.do',
    			method: 'POST',
    			async: false,
    			params:{
    				serviceId:serviceId,
    				data:JSON.stringify(actStartInfo[editerName]),
    				flag:0
    			},
    			success: function ( response, options) 
    			{
    				var success = Ext.decode(response.responseText).success;
    				var message = Ext.decode(response.responseText).message;
    				if (!success) {
    					isJobConfigOk = false;
    					Ext.Msg.alert('提示', message);
    				}
    			},
    			failure: function ( result, request){
    				isJobConfigOk = false;
    				Ext.Msg.alert('提示', "检查作业配置出现问题！");
    			}
    		});
    		return isJobConfigOk;
    	}
    }

});