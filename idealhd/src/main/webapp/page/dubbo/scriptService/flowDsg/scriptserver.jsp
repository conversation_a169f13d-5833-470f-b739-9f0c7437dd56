<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	<%-- var flowId = '<%=request.getParameter("flowId")==null?"":request.getParameter("flowId")%>'; --%>
	var coatidForTestExecGraph = '<%=request.getParameter("coatid")==null?"":request.getParameter("coatid")%>';
	<%-- var flag = '<%=request.getParameter("flag")%>'; // 0:测试     1:生成 --%>
	var isWinForTestExecGraph = '<%=request.getParameter("isWin")%>'; // 
	
	var serviceNameForTestExecGraph = '<%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%>';
	var startUserForTestExecGraph = '<%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%>';
	var startTimeForTestExecGraph = '<%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%>';
	var endTimeForTestExecGraph = '<%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptserver.js"></script>
</head>
<body>
<%-- <div class="Im_detail">
	<div class="Im_detail_title">
		<div class="Title_info"><img src="images/IM_detail_common.png" align="absmiddle" class="IM_detail_ic2"/>基本信息</div>
		<% if(request.getParameter("isWin")==null || !"1".equals(request.getParameter("isWin"))) { %>
		<div id="returnback" onclick="javaScript:returnBackForTestExec();" class="Blue_button Return">返回</div>
		<% } %>
	</div>
    <div class="Im_detail_content">
    	<table cellpadding="0" cellspacing="0" border="0" class="Im_detail_table">
        	<tr>
            	<td class="Im_table1_td1">服务名称：</td>
                <td class="Im_table1_td2"><%=request.getAttribute("scriptName")==null?"":request.getAttribute("scriptName")%></td>
                <td class="Im_table1_td1">执行结果：</td>
                <td class="Im_table1_td2"><div id="scriptState" class="Im_<%=request.getAttribute("stateFlag")==null?"":request.getAttribute("stateFlag")%>"><%=request.getAttribute("state")==null?"":request.getAttribute("state")%></div></td>
                <td class="Im_table1_td1">启动人：</td>
                <td class=""><%=request.getAttribute("startUser")==null?"":request.getAttribute("startUser")%></td>
            </tr>
            <tr>
            	<td class="Im_table1_td1">开始时间：</td>
                <td class="Im_table1_td2"><%=request.getAttribute("startTime")==null?"":request.getAttribute("startTime")%></td>
                <td class="Im_table1_td1">结束时间：</td>
                <td class="Im_table1_td2" id="scriptEndTime"><%=request.getAttribute("endTime")==null?"":request.getAttribute("endTime")%></td>
                <td class="Im_table1_td1">总耗时：</td>
                <td id="scriptRunTime"><%=request.getAttribute("runTime")==null?"":request.getAttribute("runTime")%> 秒</td>
            </tr>
        </table>
    </div>
</div> --%>
	<div id="switchruninfoins_div_for_test_exec_graph" style="width: 100%; height: 75%">
	</div>
</body>
</html>