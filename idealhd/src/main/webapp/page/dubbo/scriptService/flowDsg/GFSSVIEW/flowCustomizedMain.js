/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSVIEW = null;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSVIEW
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
    
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-80,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSVIEW.do',
	        params: {
	        	flag:flagGFSSVIEW,
	        	actionType:actionTypeGFSSVIEW
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSVIEW.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	
		var viewBasicInfoButton = Ext.create("Ext.Button", {
			cls: 'Common_Btn',
			text: "基本信息",
			disabled : false,
			handler:function(){
				Ext.create('Ext.window.Window', {
		            title: '基本信息',
		            autoScroll: true,
		            modal: true,
		            closeAction: 'destroy',
		            buttonAlign: 'center',
		            draggable: true,
		            resizable: false,
		            width: 500,
		            height: 328,
		            loader: {
		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
		            	params: {
		            		creatorFullName: creatorFullName,
			                bussName: bussName,
			                bussTypeName:bussTypeName,
			                funcDescText: funcDescText,
			                serviceName:serviceName
		            	},
		            	autoLoad: true
		            },
		            dockedItems: [{
		                xtype: 'toolbar',
		                border: false,
		                dock: 'bottom',
		                margin: '0 0 5 0',
		                layout: {pack: 'center'},
		                items: [{
		                    xtype: 'button',
		                    text: '关闭',
		                    cls: 'Common_Btn',
		                    handler: function() {
		                        this.up("window").close();
		                    }
		                }]
		            }]
		        }).show();
			}
		});
		
		var submitFromPanel = Ext.create('Ext.form.Panel', {
			width : '100%',
			border: false,
			dockedItems : [{
				xtype : 'toolbar',
				dock : 'bottom',
				layout: {pack: 'center'},
				items: [viewBasicInfoButton]
			}]
		});
	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSVIEW",
			width : '100%',
			height : contentPanel.getHeight (), 
//			overflowY:'scroll',
			autoScroll: true,
			border : false,
			bodyPadding : '5 0 0 0',
			items : [ mainTabs,submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSVIEW();

		function initGFSSVIEW()
		{
			
		}
		
		contentPanel.on('resize', function() {
		});
});
