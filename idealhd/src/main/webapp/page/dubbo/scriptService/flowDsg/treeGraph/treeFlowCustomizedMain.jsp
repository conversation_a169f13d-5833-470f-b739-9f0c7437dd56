<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="java.util.Enumeration"%>
<%
	boolean scriptNameSwitch = Environment.getInstance().getScriptNameSwitch();
	boolean showEditSwitch = Environment.getInstance().getScriptHideEditSwitch();
	boolean equipSwitch = Environment.getInstance().getScriptTaskapplyAddAgentSwitch();
%>
<html>
<head>
<script type="text/javascript">
var loginUser  = '<%=request.getAttribute("loginUser")==null?"":request.getAttribute("loginUser")%>';
	var flowId = <%=request.getParameter("flowId")==null?0:request.getParameter("flowId")%>;
	//跳转  我的脚本用 
	var sprictCoatId = <%=request.getAttribute("sprictCoatId")==null?0:request.getAttribute("sprictCoatId")%>;
	var forScriptFlow = '<%=request.getParameter("forScriptFlow")==null?"":request.getParameter("forScriptFlow")%>';
	//返回 我的脚本用 
	var forMyScript = '<%=request.getAttribute("forMyScript")==null?"":request.getAttribute("forMyScript")%>'; 
	var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_state  = <%=request.getParameter("filter_state")==null?-1:request.getParameter("filter_state")%>;
	var filter_startTime ='<%=request.getParameter("filter_startTime")==null?"":request.getParameter("filter_startTime")%>';
	var filter_endTime = '<%=request.getParameter("filter_endTime")==null?"":request.getParameter("filter_endTime")%>';
	var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_serviceState  = <%=request.getParameter("filter_serviceState")==null?-2:request.getParameter("filter_serviceState")%>;
	var filter_serviceStartTime ='<%=request.getParameter("filter_serviceStartTime")==null?"":request.getParameter("filter_serviceStartTime")%>';
	var filter_serviceEndTime = '<%=request.getParameter("filter_serviceEndTime")==null?"":request.getParameter("filter_serviceEndTime")%>';
	//返回 任务申请用
	var filter_serverNameQuery = '<%=request.getParameter("filter_serverNameQuery")==null?"":request.getParameter("filter_serverNameQuery")%>';
	var filter_scriptNameQuery  = '<%=request.getParameter("filter_scriptNameQuery")==null?"":request.getParameter("filter_scriptNameQuery")%>';
	var equipSwitch = <%=equipSwitch%>;
	var sessionIdForScriptCoat = '<%=request.getSession().getId()%>';
	var scriptNameSwitch = <%=scriptNameSwitch%>;
	var showEditSwitch = <%=showEditSwitch%>;
	var tempData = {};
	<%
	String menuid = request.getParameter("menuId");
	boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
	Enumeration<String> paramNames = request.getParameterNames();
	while( paramNames.hasMoreElements() )
	{
	    String paramName = paramNames.nextElement();
	%>
		tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
	<%
	};
	%>
	//适用平台
	var scriptPlatmFrom = '<%=request.getParameter("platmFrom")%>'; 
	//浦发特殊需求  展示agent数据 特殊样式 开关 
	var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
	var agent_store_chosed = Ext.create('Ext.data.Store', {
	    model: ' ',
	    proxy: {
	    },
	    autoLoad: false
	});
	var agent_store_chosedForSee = Ext.create('Ext.data.Store', {
	    model: ' ',
	    proxy: {
	    },
	    autoLoad: false
	});
	var chosedAgentIds = new Array();
	var chosedAgentFlagArray = new Array();//判断是否Windows和非Windows一起选择
	var chosedAgentWinForSPDB = new Ext.window.Window();
	var agentColumnsForSPDB = [];
	var scriptTypeForTaskAudi;// 浦发需求 校验非Python脚本时不允许选择 不同平台的agent 用 
	var butterflyVerison2 = new Ext.form.TextField({});
	var listComBox2 = Ext.create ('Ext.form.ComboBox',{});
	var  checkInumber=true;
</script>
<script async type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/agentModelAndColumnForSPDB.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/queryAgentInfoForSPDB.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/treeGraph/treeFlowCustomizedMain.js"></script>
</head>
<body>
<div id="treeFlow_area" style="width: 100%;height: 100%">
</div>
</body>
</html>