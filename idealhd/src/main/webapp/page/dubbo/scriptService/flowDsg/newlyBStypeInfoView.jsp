<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="java.util.*"%>
<%@ page import="java.lang.*"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>常用任务统计视图</title>

</head>
<body>
	<% 
		Map<String,String> BSinfomap = (Map<String,String>)request.getAttribute("BSINFO");
		int leinum = (Integer) request.getAttribute("NUM");
		int mapSize  = (Integer)request.getAttribute("bsinMapSize");
		boolean rflowBoo  = (Boolean)request.getAttribute("rflowBoo");
		int rflow = 1;
		if(leinum!=0){
		   rflow =  mapSize/6-leinum;
		}
	%>
	
	
	
<table  cellpadding="0" cellspacing="0" border="0" class="Overview_table" id="Overview_table_id">
	
	<tr>
	<%
	if(leinum!=0){%> 
		<td><div id="leftbtn_pos" class="leftbtn_pos btnimg" onclick='leftbtn_posclick()'></div></td>
	<% }%>
	
	<%
	int a = 0;
	for (Map.Entry<String, String> entry : BSinfomap.entrySet())
    {
	    a++;
	    if(a>6){
	        break;
	    }
	%> 
	    <td class="script_module_td">
			<div class="Script">
				<div class="Color_Common">
			    	<div class="script_module_img"></div>
			        	<div class="utility_line"></div>
			            	<div class="Script_font">
			                    <%=entry.getValue()%><span class="Script_special" > <%=entry.getKey()%></span>
			                </div>
			            </div>
			 </div>
		</td>
	<%
    }
	%>
		<% if(rflow>0 && rflowBoo){%> 
		<td align="right"><div id="rightbtn_pos" class="rightbtn_pos btnimg" onclick='rightbtn_posclick()'></div></td>
		<% }%>
	</tr>
</table>
<script type="text/javascript">
	var num = <%=leinum%>;
	function leftbtn_posclick(){
		num--;
		if(num<0){
			num = 0;
		}
		$("#Overview_table_id").load(
				"${pageContext.request.contextPath }/FlowTemp/leftbtnPosclickServlet.do?num="+num,function(){
			}
		)
	}
	
	function rightbtn_posclick(){
		num++;
		$("#Overview_table_id").load(
				"${pageContext.request.contextPath }/FlowTemp/leftbtnPosclickServlet.do?num="+num,function(){
			}
		)
	}
</script>

</body>
</html>
