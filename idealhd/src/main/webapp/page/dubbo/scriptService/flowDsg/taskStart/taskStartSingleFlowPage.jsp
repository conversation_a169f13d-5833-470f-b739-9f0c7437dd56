<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/taskStart/taskStartSingleFlowPage.js"></script>
<script type="text/javascript">
var tempData = {};
var oldTempData = {};
<%
String menuid = request.getParameter("menuId");
String divID = request.getParameter("divID")==null?"": request.getParameter("divID");

String iServiceId = request.getParameter("iServiceId");
String iid = request.getParameter("iid");
String customId = request.getParameter("customId");

Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
	oldTempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
var flowId='<%=request.getAttribute("flowId")==null?"":request.getAttribute("flowId")%>';
var serviceId='<%=request.getAttribute("serviceId")==null?"":request.getAttribute("serviceId")%>';
var divID ='<%=divID%>';
tempData.divID =divID; 
oldTempData.divID =divID;
tempData.namespace = 'monitorSingleFlow'+ tempData.divID;
oldTempData.namespace = 'monitorSingleFlow'+ oldTempData.divID;
oldTempData.flowId = flowId;
tempData.serviceId =serviceId; 
 
tempData.load = true;
</script>

<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/taskStart/taskStartScriptWebView.js"></script>
<body>
<div class="Change_Panel customize_panel_back" id="monitorSingleFlowPage_m">
<div>
<table cellpadding="0" cellspacing="0" border="0" class="Change_Body">
	<tr >
		<td align="left">
			<div id="scriptViewDiv_<%=divID%>" class="monitor_graph"></div>
		</td>
	</tr>
	<tr>
		<td colspan="2"><div id="package_bottomMonitorSingle_<%=divID%>"></div></td>
	</tr>
</table>
</div>
</div>
</body>