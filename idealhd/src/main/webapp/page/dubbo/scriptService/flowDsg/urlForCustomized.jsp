<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="com.ideal.common.utils.SessionData"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.commons.Constants"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<%
	SessionData sessionData = SessionData.getSessionData(request);
	String bankFlag = Environment.getInstance().getBankSwitch();
	String eachNum = Environment.getInstance().getEachNum();
	boolean isGdb =ServerEnv.getServerEnv().isGDBBank();
	boolean reviewSwitch = Environment.getInstance().getScriptReviewSwitch();
	boolean noScriptConvertSwitch = Environment.getInstance().getScriptConvertFlowSwitch();
	boolean scriptOddNumberSwitch = Environment.getInstance().getScriptOddNumberSwitch();
	boolean emCommonTaskSwitch = Environment.getInstance().getEMCommonTaskSwitch();
	boolean removeAgentSwitch=ServerEnv.getInstance().getBooleanConfig("remove.agentGroup.switch", false);
	String skinFlag=(String)request.getSession().getAttribute("skinFlag"); 
	boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
        Environment.CMDB_FLAG_DEFAULT);
	String bankCssSwitch = ServerEnv.getInstance().getBankCssSwitch();
	if(skinFlag==null||"".equals(skinFlag))
	{
	    skinFlag="red";
	}
	String departmentFlag = Environment.getInstance().getDepartmentFlag();
	
	String title ="";
	String bankTitle = Environment.getInstance().getBankTitle();

	if(isGdb){
	    title="应用变更自动化平台";
	}else if((Constants.BANK_CMB).equals(bankFlag)){
	    title="招商银行运维管理平台";
	}else  if(Constants.BANK_CCB.equals(bankFlag)){
	    title="中信银行自动化运维管理系统";
	}else  if(Constants.BANK_GDB.equals(bankFlag)){
	    title=bankTitle;
	}else if(Constants.BANK_JPRCB.equals(bankFlag)){
	    title="吉林农信自动化运维管理系统";
	}else if(Constants.BANK_CEB.equals(bankFlag)){
	    title="数据库平台云系统";
	}else if(Constants.BANK_JZ.equals(bankFlag)){
	    title="锦州银行批量自动化调度系统";
	}else{
	    title="自动化运维管理系统";
	}
	 String sysBindingComputerFlag = Environment.getInstance().getSysConfig(Environment.SYS_BINDING_COMPUTER_FLAG);
	 boolean pageSortZhFlag =Environment.getInstance().getBooleanConfig(Environment.PAGE_SORT_ZH_SWITCH, false);
	 boolean signFlag= ServerEnv.getInstance().getBooleanConfig(ServerEnv.PROLOGIC_SWITCH_SIGN, ServerEnv.FALSE_BOOLEAN);
%>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
<!--[if lt IE 9]>
<?import namespace="v" implementation="#default#VML" ?>
<![endif]-->
<%-- <%
    SessionData sessionData = SessionData.getSessionData(request);
%> --%>

	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></meta>
	<script type="text/javascript">
	var signFlag=<%=signFlag%>;
	var grid_space=null;
	var ext_variables = {};
	ext_variables.enterDaly = 0;
	
	var sysBindingComputerFlag='<%="true".equals(sysBindingComputerFlag)?"1":"0" %>';
		var departmentFlag='<%=departmentFlag%>';
		<%-- var url = ""
		var usersa = '<%=sessionData.getLoginName() == null ? "" : sessionData.getLoginName()%>';
		if (usersa == 'sa') {
			url = 'page/common/tree-data.json';
		} else {
			url = 'menu.do';
		} --%>
		var  url='menu.do';
	
		var rolePermisson = '<%=sessionData.getRolePermisson()%>';
		var logintype = '<%=sessionData.getCitUser()%>';
		var skinFlag='<%= skinFlag%>';
		var envname='<%=Environment.getInstance().getEnvname() %>';
		var concurrentNumber = <%=eachNum%>;
		var welcomePageUrl='<%=request.getAttribute("welcomePageUrl")%>';
		//我的关注 任务名称 bankCode001
		var myBackLogtaskName='<%=request.getParameter("myBackLogtaskName")%>';
		//我的未结 任务名称 bankCode001
		var myUnfinshTaskName='<%=request.getParameter("taskName")%>';
		var welcomeTitle='<%=Environment.getInstance().getWelcomeTitle() %>';
		var reviewSwitch = <%=reviewSwitch%>;
		var noScriptConvertSwitch  = <%=noScriptConvertSwitch%>;
		var scriptOddNumberSwitch  = <%=scriptOddNumberSwitch%>;
		var emCommonTaskSwitch  = <%=emCommonTaskSwitch%>;
		var removeAgentSwitch=<%=removeAgentSwitch%>;
		var CMDBflag = <%=CMDBflag%>;
		var bankCssSwitch = "<%=bankCssSwitch%>";
		var grid_margin='0';
		var panel_margin='0';
		if(logintype == 'sequentialInstance'){//我的待办 我的关注 跳转任务申请页面
			welcomePageUrl="scriptServicesTaskExec.do";
		}else if(logintype == 'toExecScriptTask'){//我的待办 我的未结 跳转任务执行页面
			welcomePageUrl="scriptTaskexec.do";
		}
	</script>
<%  
    HttpSession sess = request.getSession();  
    String message = (String)sess.getAttribute("mes");  
%>  
	<script>
	 	var pwdExpiredMessage = '<%=message==null||"".equals(message)?"":message%>';
	 	if(pwdExpiredMessage!=''){
	 		alert(pwdExpiredMessage);
	 	}
	</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/skinObj.js"></script>
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/js/uploadify/uploadify.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/css/custom.css?_dc=<%=new Date().getTime() %>" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/js/codemir/codemirror.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/js/codemir/fullscreen.css" />
	<link rel="stylesheet" type="text/css" href="css/colpick.css"/>
	
	<script type="text/javascript" src="<%=request.getContextPath()%>/ext/ext-all.js"></script>	
	<script type="text/javascript" src="<%=request.getContextPath()%>/ext/TreePicker.js"></script>	
<!-- 招商特殊显示 -->
<%if((Constants.BANK_CMB).equals(bankFlag)){ %>
	<style type="text/css">
		.x-tree-node-text {
		    font-size: 14px;
		    line-height: 25px;
		    padding-left: 3px;
		}
		.x-tree-icon{
			margin-top:0px;
			margin-bottom:0px
			}
		
	</style>
<% } %>
	<script type="text/javascript">
		var webshell_endpoint = '<%=request.getAttribute("webshell_endpoint")%>';
		var scriptServiceScriptFlowSwitch = <%=request.getAttribute("scriptServiceScriptFlowSwitch")%>;
	</script>
	
	<script type="text/javascript" src="<%=request.getContextPath()%>/ext/locale/ext-lang-zh_CN.js"></script>

<% if(Environment.getInstance().getMainPageIsTab()){ %>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/mainPage_tab.js"></script>
<%}else if(bankCssSwitch.equals("red")){ %>
    <script type="text/javascript">
	grid_margin='10 20 10 20';
	panel_margin='10 0 0 0';
	</script>
    <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/red_skin/ext/resources/css/ext-all.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/red_skin/css/Style.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/red_skin/css/Rewrite_style.css" />
	
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomized.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/extsetting.js"></script> 
<%}else if(bankCssSwitch.equals("blue")){ %>
    <script type="text/javascript">
	grid_margin='0 0 0 0';
	grid_space='20 20 0 20';
	panel_margin='0 0 0 0';
	</script>
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/ext/resources/css/ext-all.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Style.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Rewrite_style.css" />
	
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomized.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/extsetting.js"></script> 
<%}else{ %>
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/ext/resources/css/ext-all.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Style.css" />
	<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Rewrite_style.css" />
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomized.js"></script>
<%} %>

	<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/secureFilter.js"></script>	
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/validate.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/jquery-3.4.1.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/underscore.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/jquery.webui-popover.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/jquery.nicescroll.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/OverrideTitle.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
	<!-- 代码高亮插件 -->
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/codemirror.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/shell.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/python.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/perl.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/sql.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/bat.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/fullscreen.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/codemir/javascript.js"></script>	 
	 <!-- Sets the basepath for the library if not in same directory -->
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/uploadify/jquery.uploadify.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/colpick.js"></script>	
	<script type="text/javascript">
		//mxBasePath = '../src';
		mxBasePath = "<%=request.getContextPath()%>"+'/mxgraph-master/src';
	</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/mxgraph-master/mxClient.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/prettify.js"></script>	 
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/jquery-ui-1.9.2.custom.min.js"></script>
	<%-- <script src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/jquery-migrate-1.2.1.min.js"></script> --%>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/modernizr.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/easypiechart/easypiechart.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/c3-chart/d3.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/c3-chart/c3.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/releaseMonitor/new/js/scripts.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/webssh/term.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/webssh/ws.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/lookmonitor/Ecalendar.jquery.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/soundmanager/script/soundmanager2-jsmin.js"></script>   
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/charts/echarts.min.js"></script>
	<script>
		var messageSound;
		var alermSound;
		soundManager.setup({
		  url: '<%=request.getContextPath()%>/js/soundmanager/swf',
		  onready: function() {
		    messageSound = soundManager.createSound({
		        id: 'messageSound',
		        url: '<%=request.getContextPath()%>/js/soundmanager/mp3/msg.mp3'
		      });
		    alermSound = soundManager.createSound({
		        id: 'alermSound',
		        url: '<%=request.getContextPath()%>/js/soundmanager/mp3/alerm.mp3'
		      });
		  },
		  ontimeout: function() {
		    // Hrmm, SM2 could not start. Missing SWF? Flash blocked? Show an error, etc.?
		  }
		});
	</script>

<% 
	boolean poc =ServerEnv.getServerEnv().getPocJsSwitch();
	if(poc){
%>
	<!--  以下为POC涉及功能的页面js开关 -->
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/export-all.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/Exporter.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/Button.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/Base64.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/downloadify.min.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/Formatter.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/swfobject.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/excelFormatter/Cell.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/excelFormatter/ExcelFormatter.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/excelFormatter/Style.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/excelFormatter/Workbook.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/excelFormatter/Worksheet.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/poc/export/exporter/csvFormatter/CsvFormatter.js"></script>

	<script>
		var mySound;
		soundManager.setup({
		  url: '<%=request.getContextPath()%>/js/soundmanager/swf',
		  onready: function() {
		    mySound = soundManager.createSound({
		      id: 'aSound',
		      url: '<%=request.getContextPath()%>/js/soundmanager/mp3/ok.mp3'
		    });
		  },
		  ontimeout: function() {
		    // Hrmm, SM2 could not start. Missing SWF? Flash blocked? Show an error, etc.?
		  }
		});
	</script>
<% } %>

<%if(pageSortZhFlag){ %>
    <script type="text/javascript">
	    var rep_ch = new RegExp("^[\u4e00-\u9fa5]");
	    Ext.data.Store.prototype.createComparator = function(sorters){//页面中文排序  
	        return function(r1, r2){  
	            var s = sorters[0], f=s.property;  
	            var v1 = r1.data[f], v2 = r2.data[f];       
	            var result = 0;  
	            if(typeof(v1) == "string"){
	            	if(v1&&v2&&rep_ch.test(v1)&&rep_ch.test(v2))
	            	{
	                    result = v1.localeCompare(v2,'zh-Hans-CN', {sensitivity: 'accent'});  
	            	}else{
	            		result = v1.localeCompare(v2);  
	            	}
	                if(s.direction == 'DESC'){  
	                    result *=-1;  
	                }  
	            } else {  
	                result =sorters[0].sort(r1, r2);  
	            }                
	            var length = sorters.length;  
	            for(var i = 1; i<length; i ++){  
	                s = sorters[i];  
	                f = s.property;  
	                v1 = r1.data[f];  
	                v2 = r2.data[f];  
	                if(typeof(v1) == "string"){
	                	var compare;
	                	if(v1&&v2&&rep_ch.test(v1)&&rep_ch.test(v2))
	                	{
	                		compare = v1.localeCompare(v2,'zh-Hans-CN', {sensitivity: 'accent'});  
	                	}else{
	                		compare = v1.localeCompare(v2);  
	                	}
	                    result = result || compare;  
	                    if(s.direction == 'DESC'){  
	                        result *=-1;  
	                    }  
	                } else {  
	                    result = result || s.sort.call(this, r1, r2);  
	                }  
	            }  
	            return result;  
	        }  
	    }  
     </script>
  <% } %>
<title><%=title %></title>	
</head>
<body class="customize_body">
</body>
<script>
Ext.onReady(function(){   
createGridButton();
});
</script>
<script>
if(signFlag){
    Ext.Ajax.request({
        url : 'getNeedSignList.do',
        method : 'POST',
        sync : false,
        success : function (response, request) {
            var sysWin;
            var success = Ext.decode(response.responseText).success;
            if (success) {
                var dataList = Ext.decode(response.responseText).dataList;
                if (dataList.length>0) {
                    sysWin = Ext.create('Ext.window.Window', {
                        title : '灾备切换，任务签到',
                        width : 800,
                        height : 400,
                        upperWin : sysWin,
                        autoScroll : true,
                        modal : true,
                        constrain : true,
                        resizable : false,
                        frame : true,
                        loader : {
                            url : 'initSignPage.do',
                            autoLoad : true,
                            scripts : true
                        }
                    });
                    sysWin.show();
                }
            }
        },
        failure : function(result, request) {
        }
    });
}
</script>
</html>