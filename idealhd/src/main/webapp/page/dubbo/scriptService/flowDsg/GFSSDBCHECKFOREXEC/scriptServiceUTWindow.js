/*******************************************************************************
 * 流程定制维护窗口-提醒任务
 ******************************************************************************/
Ext.onReady(function() {
	var scriptNameStore;
	var utContentObj;
	var stepNameObj;
	
	stepNameObj = Ext.create('Ext.form.field.Text', {
		fieldLabel : '步骤名称'
	});
	utContentObj = Ext.create('Ext.form.field.TextArea', {
		fieldLabel : '提醒内容',
		labelAlign : 'right',
		autoScroll : true
	});

	var formPanel = Ext.create('Ext.form.Panel', {
		region : "north",
		border : false,
		fieldDefaults : {
			labelAlign : 'right',
			width : 560,
			labelWidth : 60
		},
		width : 560 ,
		buttonAlign : 'center',
		items : [ stepNameObj, utContentObj ],

		buttons : [{
			text : '返回',
			handler : function() {
				parent.configwindowFlowGFSSDBCHECKFOREXEC.close();
			}
		}]
	});
	// 主Panel
	var MainPanel = Ext.create('Ext.panel.Panel', {
		layout : "border",
		renderTo : "scriptServiceUT_DivGFSSDBCHECKFOREXEC",
		width : '100%',
		height : '100%',
		border : false,
		bodyPadding : 5,
		items : [ formPanel ]
	});
	/** 初始化方法* */
	function initFun() {
		stepNameObj.setValue(actStartInfo['GFSSDBCHECKFOREXEC'][parent.cellObjGFSSDBCHECKFOREXEC.mxIid]['actName']);
		utContentObj.setValue(actStartInfo['GFSSDBCHECKFOREXEC'][parent.cellObjGFSSDBCHECKFOREXEC.mxIid]['message']);
	}
	initFun();
});
