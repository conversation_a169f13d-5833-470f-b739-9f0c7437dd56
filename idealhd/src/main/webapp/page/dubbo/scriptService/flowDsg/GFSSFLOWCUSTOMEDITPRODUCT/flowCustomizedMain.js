/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var customNameObjOutSideGFSSFLOWCUSTOMEDITPRODUCT;
var editorSonGFSSFLOWCUSTOMEDITPRODUCT;

Ext.onReady(function() {
    //清理各种监听
    destroyRubbish();
    
    var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSFLOWCUSTOMEDITPRODUCT
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });

    //0.1 Graph 
    var mainTabs = Ext.widget('tabpanel', {
        tabPosition: 'top',
        activeTab: 0,
        width: '100%',
        height: contentPanel.getHeight() - 48,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        items: [{
            title: '图形化显示',
            loader: {
                url: 'flowCustomizedImgScriptServiceGFSSFLOWCUSTOMEDITPRODUCT.do',
                params: {
                    flag: flagGFSSFLOWCUSTOMEDITPRODUCT,
                    actionType: actionTypeGFSSFLOWCUSTOMEDITPRODUCT
                },
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        },
        {
            hidden: true,
            title: '列表显示',
            loader: {
                url: 'flowCustomizedListScriptServiceGFSSFLOWCUSTOMEDITPRODUCT.do',
                contentType: 'html',
                autoLoad: false,
                loadMask: true,
                scripts: true
            },
            listeners: {
                activate: function(tab) {
                    tab.loader.load();
                }
            }
        }]
    });

    customNameObjOutSideGFSSFLOWCUSTOMEDITPRODUCT = Ext.create('Ext.form.field.Text', {
        padding: '0 5 0 0',
        fieldLabel: '模板名称',
        value: customNameGFSSFLOWCUSTOMEDITPRODUCT
    });
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
     //   cls: 'Common_Btn',
        margin: '0 0 0 5',
        textAlign: 'center',
        handler: function() {
        	saveCustomTemplateGFSSFLOWCUSTOMEDITPRODUCT();
        }
    });
    var submitButton = Ext.create('Ext.Button', {
    	text: '提交',
    	//cls: 'Common_Btn',
    	margin: '0 0 0 5',
    	textAlign: 'center',
    	handler: function() {
    		sendAudiGFSSFLOWCUSTOMEDITPRODUCT(this);
    	}
    });
    var backButton = Ext.create('Ext.Button', {
        text: '返回',
     //   cls: 'Common_Btn',
        margin: '0 0 0 5',
        textAlign: 'center',
        handler: function() {
            destroyRubbish();
            contentPanel.getLoader().load({
                url: 'flowCustomizedTemplateForProduct.do',
                params: {
                    'filter_bussId': filter_bussIdGFSSFLOWCUSTOMEDITPRODUCT,
                    'filter_bussTypeId': filter_bussTypeIdGFSSFLOWCUSTOMEDITPRODUCT,
                    'filter_scriptName': filter_scriptNameGFSSFLOWCUSTOMEDITPRODUCT,
                    'filter_serviceName': filter_serviceNameGFSSFLOWCUSTOMEDITPRODUCT,
                    'filter_scriptType': filter_scriptTypeGFSSFLOWCUSTOMEDITPRODUCT
                }
            });
            if (Ext.isIE) {
                CollectGarbage();
            }
        }
    });
    
    var viewBasicInfoButton = Ext.create("Ext.Button", {
	//	cls: 'Common_Btn',
		text: "基本信息",
		margin: '0 0 0 5',
		disabled : false,
		handler:function(){
			Ext.create('Ext.window.Window', {
	            title: '基本信息',
	            autoScroll: true,
	            modal: true,
	            closeAction: 'destroy',
	            buttonAlign: 'center',
	            draggable: true,
	            resizable: false,
	            width: 500,
	            height: 328,
	            loader: {
	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
	            	params: {
	            		creatorFullName: creatorFullName,
		                bussName: bussName,
		                bussTypeName:bussTypeName,
		                funcDescText: funcDescText,
		                serviceName:serviceName
	            	},
	            	autoLoad: true
	            },
	            dockedItems: [{
	                xtype: 'toolbar',
	                border: false,
	                dock: 'bottom',
	                margin: '0 0 5 0',
	                layout: {pack: 'center'},
	                items: [{
	                    xtype: 'button',
	                    text: '关闭',
	                    cls: 'Common_Btn',
	                    handler: function() {
	                        this.up("window").close();
	                    }
	                }]
	            }]
	        }).show();
		}
	});

    var submitFromPanel = Ext.create('Ext.form.Panel', {
        width: '100%',
        border : true,
//        dockedItems : [{
//			xtype : 'toolbar',
//			
//			dock : 'bottom',
//			items: [customNameObjOutSideGFSSFLOWCUSTOMEDITPRODUCT, '->', submitButton, saveButton, viewBasicInfoButton, backButton]
//		}]
        layout: {
            type: 'hbox',
            padding:'5',
            align:'top'
        },
        items: [customNameObjOutSideGFSSFLOWCUSTOMEDITPRODUCT,{
                xtype:'tbspacer',
                flex:1
            },submitButton, saveButton, viewBasicInfoButton, backButton,{
                xtype:'tbspacer',
                flex:1
            },{
                xtype:'tbspacer',
                width:240
            }]
    });

    var MainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "flowCustomizedMainDivGFSSFLOWCUSTOMEDITPRODUCT",
        width: '100%',
        height: contentPanel.getHeight(),
        autoScroll: true,
        border: false,
        bodyPadding: '5 0 0 0',
        items: [mainTabs, submitFromPanel]
    });
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(MainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        mainTabs.setWidth('100%');
    });
    initGFSSFLOWCUSTOMEDITPRODUCT();
    function initGFSSFLOWCUSTOMEDITPRODUCT() {
    }
});

function sendAudiGFSSFLOWCUSTOMEDITPRODUCT(btn) {
	var isReady = checkDataIsReadyGFSSFLOWCUSTOMEDITPRODUCT();
	if(isReady) {
		var taskNameText = "";
		var audiLoginName = "";
		Ext.Ajax.request({
	        url: 'getFlowCustomTemplateData.do',
	        method: 'POST',
	        async: false,
	        params: {
	        	iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCT,
	            flag: parent.flagGFSSFLOWCUSTOMEDITPRODUCT
	        },
	        success: function(response, options) {
	        	taskNameText = Ext.decode(response.responseText).taskName;
	            audiLoginUser = Ext.decode(response.responseText).audiLoginUser;
	            
	        },
	        failure: function(result, request) {}

	    });
		
		Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var auditorStore_tap = Ext.create('Ext.data.Store', {
		    autoLoad: false,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getExecAuditorList.do?scriptLevel='+scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCT,
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    },
		    listeners : {  
	            load : function(store, records, success, eOpts) {  
	            	$.each(records, function(index, record){
	            		if(record.get('loginName')==audiLoginUser) {
	            			auditorComBox_tap.select(record);    
	            		}
	            	});
	            }  
	        }  
		  });
		
		var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
		    editable: false,
		    fieldLabel: "审核人",
		    store: auditorStore_tap,
		    queryMode: 'local',
		    width: 390,
		    displayField: 'fullName',
		    valueField: 'loginName',
		    labelWidth : 58,
			labelAlign : 'right'
		  });
		var taskName = new Ext.form.TextField({
			name: 'taskName',
			fieldLabel: '任务名称',
			emptyText: '',
			labelWidth : 58,
			labelAlign : 'right',
			value: taskNameText,
			width: 390
		});
		
		Ext.create('Ext.window.Window', {
	  		title : '配置双人复核信息',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'destroy',
	  		width : 400,
	  		height : 150,
	  		items:[taskName, auditorComBox_tap],
	  		buttonAlign: 'center',
	  		buttons: [{ 
	  			xtype: "button", 
	  			text: "确定", 
	  			handler: function () {
	  				var self = this;
	  				var auditor = auditorComBox_tap.getValue();
	  				if(!auditor) {
	  					Ext.Msg.alert('提示', "没有选择审核人！");
	  					return;
	  				}
	  				
	  				var taskN = Ext.util.Format.trim(taskName.getValue());
	  				if(Ext.isEmpty(taskN)) {
	  					Ext.Msg.alert('提示', "任务名称不能为空！");
	  					return;
	  				}
	  				
	  				if (fucCheckLength(taskN) > 255) {
                        Ext.Msg.alert('提示', "任务名称不能超过255字符！");
                        return;
                    }
	  				
	  				var startData = orgStartDataGFSSFLOWCUSTOMEDITPRODUCT();
	  				Ext.Ajax.request({
	  				    url : 'scriptFlowExecAuditing.do',
	  				    method : 'POST',
	  				    params : {
	  				    	iidForQuery : workItemidGFSSFLOWCUSTOMEDITPRODUCT,
	  				    	serviceId: iidGFSSFLOWCUSTOMEDITPRODUCT,
	  				    	auditor: auditor,
	  				    	taskName: taskN,
	  				    	startData: JSON.stringify(startData),
	  				    	scriptLevel: scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMEDITPRODUCT
	  				    },
	  				    success: function(response, opts) {
	  				        var success = Ext.decode(response.responseText).success;
	  				        var message = Ext.decode(response.responseText).message;
	  				        if(success) {
	  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	  				        	self.up("window").close();
	  				        	if(workItemidGFSSFLOWCUSTOMEDITPRODUCT) {
	  				        		if(fromGFSSFLOWCUSTOMEDITPRODUCT==1) {
	  						    		messageWindow1.close();
	  						    	} else {
	  									messageWindow.getLoader ().load (
	  											{
	  												url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
	  												autoLoad : true,
	  												scripts : true
	  											});
	  									messageWindow.setTitle ('待办事项');
	  								}
	  				        	}
	  				        } else {
	  				        	Ext.MessageBox.alert("提示", message);
	  				        }
	  				    },
	  				    failure: function(result, request) {
	  				    	secureFilterRs(result,"操作失败！");
	  				    }
	  			    });
	  			}
	  		}]
	  	}).show();
		auditorStore_tap.load();
	}
}