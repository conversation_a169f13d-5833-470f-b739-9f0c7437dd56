/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSDBCHECK;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSDBCHECK
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
	
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-60,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSDBCHECK.do',
	        params: {
	        	flag:flagGFSSDBCHECK,
	        	actionType:actionTypeGFSSDBCHECK
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSDBCHECK.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	 var levelStore = Ext.create('Ext.data.Store', {
		    fields: ['iid', 'scriptLevel'],
		    data : [
		        {"iid":"0", "scriptLevel":"白名单"},
		    	{"iid":"1", "scriptLevel":"高级风险"},
		    	{"iid":"2", "scriptLevel":"中级风险"},
		    	{"iid":"3", "scriptLevel":"低级风险"}
		    ]
		});
	 
	 var scriptLevelCb = Ext.create('Ext.form.field.ComboBox', {
	        name: 'scriptLevel',
	        labelWidth: 85,
	        //columnWidth:.49,
	        width:'48.5%',
	        queryMode: 'local',
	        fieldLabel: '风险级别',
	        labelAlign:'right',
	        padding: 5,
	        displayField: 'scriptLevel',
	        valueField: 'iid',
	        editable: false,
	        value: scriptFlowLevelForTaskAudiGFSSDBCHECK,
	        queryMode: 'local',
	        emptyText: '--请选择风险级别--',
	        store: levelStore
	    });
	 Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var recheckAuditorStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getPublishRecheckAuditorList.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		recheckAuditorStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		iworkItemid : workItemidGFSSDBCHECK
		    };
		    Ext.apply(recheckAuditorStore.proxy.extraParams, new_params);
	    });
		
		var recheckAuditorComBox = Ext.create('Ext.form.ComboBox', {
		    editable: false,
		    fieldLabel: "复审人",
		    labelWidth: 85,
		    labelAlign:'right',
		    store: recheckAuditorStore,
		    queryMode: 'local',
		   // columnWidth:.49,
		    width:'48.5%',
		    padding: '0 0 5 0',
		    displayField: 'fullName',
		    valueField: 'loginName',
		    listeners: { //监听 
		        render : function(combo) {//渲染 
		            combo.getStore().on("load", function(s, r, o) { 
		                combo.setValue(r[0].get('loginName'));//第一个值 
		            }); 
		        } 
		    } 
		  });
	 var publishButton = Ext.create('Ext.Button', {
	    text : '发布',
	    margin : '0 0 0 5',
	    textAlign : 'center',
//		    cls : 'Common_Btn',
	    handler : function() {
	    	var scriptLevel = scriptLevelCb.getValue();
			if(!scriptLevel) {
				Ext.Msg.alert('提示', "没有选择风险级别！");
				return;
			}
			if(scriptLevel=='高级风险'){
  			    scriptLevel = 1;
  			}else if(scriptLevel=='中级风险'){
  				scriptLevel = 2;
  			}else if(scriptLevel=='低级风险'){
  				scriptLevel = 3;
  			}else if(scriptLevel=='白名单'){
  				scriptLevel = 0;
  			}
			if(scriptLevel==1 || scriptLevel==2 || scriptLevel==3){
	    	Ext.Ajax.request (
					{
					    url : 'scriptPublishForOneRecord.do',
					    method : 'POST',
					    params :
					    {
					        iid : iidGFSSDBCHECK,
					        flag : fromTypeGFSSDBCHECK,
					        iworkItemid:workItemidGFSSDBCHECK,
					        scriptLevel: scriptLevel
					    },
					    success : function (response, opts)
					    {
						    var success = Ext.decode (response.responseText).success;
						    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
						    {
						    	if(fromGFSSDBCHECK==1) {
						    		messageWindow1.close();
						    		destroyRubbish(); //销毁本页垃圾
						    		contentPanel.getLoader().load({
						    			url: 'forwardScriptLibrary.do',
						    			scripts: true});
						    		
						    	} else {
						    		messageWindow.getLoader ().load (
										{
										    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
										    autoLoad : true,
										    scripts : true
										});
										messageWindow.setTitle ('待办事项');
						    	}
						    });
					    }
					});
			}else{
				Ext.Msg.alert('提示', "请从下拉菜单中选择风险级别！");
					return;
			}
	    }
	  });
	 var publishDesc = Ext.create('Ext.form.field.TextArea', {
	        fieldLabel: '发布申请说明',
	        labelAlign:'right',
	        padding : '5 0 5 5',
	        labelWidth: 85,
	        height: 50,
	        value: publishDescTextGFSSDBCHECK,
	        columnWidth: .47,
	        readOnly: true,
	        autoScroll: true
	    });
	 
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea',
		{
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        padding : '5 0 5 5',
	        labelAlign:'right',
	        labelWidth: 85,
	        height : 50,
	        maxLength : 2000,
	        labelSepartor : "：",
	        readOnly: actionTypeGFSSDBCHECK=='dbback',
	        value: backInfoContentGFSSDBCHECK,
	        columnWidth: .47
		});
		
	 var noWayButton = Ext.create('Ext.Button', {
		    text : '打回',
		    margin : '0 0 0 5',
		    textAlign : 'center',
		    handler : function() {
		    	var scriptAudiFrom = 1;
		    	if(actionTypeGFSSDBCHECK=='dbcheckForExec') {
		    		scriptAudiFrom = 2;
		    	}
		    	Ext.MessageBox.wait ("数据处理中...", "进度条");
				var backI = backInfo.getValue();
				if (fucCheckLength (backI.trim ()) < 1)
				{
					Ext.Msg.alert ('提示', "请输入打回原因!");
					return;
					
				}
				if (!illegalCharPassForTextArea (backI))
				{
					Ext.MessageBox.alert ("提示", "打回原因只能由汉字、字母、数字、下划线、冒号以及减号组成！");
					return;
				}
				if (fucCheckLength (backI.trim ()) > 4000)
				{
					Ext.Msg.alert ('提示', "打回原因长度最大为4000个字符!");
					return;
				}
				Ext.Ajax.request (
				{
				    url : 'operWorkitemByiidForSsPublish.do',
				    method : 'POST',
				    params :
				    {
				        istateForQuery: 2,
				        scriptAudiFrom: scriptAudiFrom,
				        iidForQuery: workItemidGFSSDBCHECK,
				        ibackInfo: backI
				    },
				    success : function (response, opts)
				    {
					    var success = Ext.decode (response.responseText).success;
					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
					    {
					    	if(fromGFSSDBCHECK==1) {
					    		messageWindow1.close();
					    		destroyRubbish(); //销毁本页垃圾
					    		contentPanel.getLoader().load({
					    			url: 'pandect1.do',
					    			scripts: true});
					    	} else {
					    		messageWindow.getLoader ().load (
										{
										    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
										    autoLoad : true,
										    scripts : true
										});
										messageWindow.setTitle ('待办事项');
					    	}
					    	
					    });
				    }
				});
		    }
	  });
	 
	 var backToDbCheckButton = Ext.create('Ext.Button', {
		    text : '返回',
		    margin : '0 0 0 5',
		    textAlign : 'center',
		    handler : function() {
		    	if(fromGFSSDBCHECK==1) {
		    		messageWindow1.close();
		    	} else {
					messageWindow.getLoader ().load (
							{
								url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
								autoLoad : true,
								scripts : true
							});
					messageWindow.setTitle ('待办事项');
				}
		    }
	  });
	 var viewBasicInfoButton = Ext.create("Ext.Button", {
			text: "基本信息",
			disabled : false,
			handler:function(){
				Ext.create('Ext.window.Window', {
		            title: '基本信息',
		            autoScroll: true,
		            modal: true,
		            closeAction: 'destroy',
		            buttonAlign: 'center',
		            draggable: true,
		            resizable: false,
		            width: 500,
		            height: 328,
		            loader: {
		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
		            	params: {
		            		creatorFullName: creatorFullName,
			                bussName: bussName,
			                bussTypeName:bussTypeName,
			                funcDescText: funcDescText,
			                serviceName:serviceName
		            	},
		            	autoLoad: true
		            },
		            dockedItems: [{
		                xtype: 'toolbar',
		                border: false,
		                dock: 'bottom',
		                margin: '0 0 5 0',
		                layout: {pack: 'center'},
		                items: [{
		                    xtype: 'button',
		                    text: '关闭',
		                    cls: 'Common_Btn',
		                    handler: function() {
		                        this.up("window").close();
		                    }
		                }]
		            }]
		        }).show();
			}
		});
	 
	 var submitFromPanel = Ext.create('Ext.form.Panel', {
			width : '100%',
			frame : true,
			layout: 'anchor',
			buttonAlign : "left",
			items:[{
	            border: false,
	            layout: 'column',
	            items: [scriptLevelCb,recheckAuditorComBox]
	        }, {
	            border: false,
	            layout: 'column',
	            items: [publishDesc,backInfo]
	        }],
			height: 150,
			buttons:[viewBasicInfoButton, publishButton,noWayButton,{
	            text: '申请复审',
	            margin : '0 0 0 5',
	            hidden:isReCheckGFSSDBCHECK=='true'?true:false,
	            handler: function(){
	            	var scriptLevel = scriptLevelCb.getValue();
	  				if(!scriptLevel) {
	  					Ext.Msg.alert('提示', "没有选择风险级别！");
	  					return;
	  				}
	  				var auditor = recheckAuditorComBox.getValue();
	  				if(!auditor) {
	  					Ext.Msg.alert('提示', "没有选择复审人！");
	  					return;
	  				}
	  				if(scriptLevel=='高级风险'){
		  			    scriptLevel = 1;
		  			}else if(scriptLevel=='中级风险'){
		  				scriptLevel = 2;
		  			}else if(scriptLevel=='低级风险'){
		  				scriptLevel = 3;
		  			}else if(scriptLevel=='白名单'){
		  				scriptLevel = 0;
		  			}
	  				if(scriptLevel==1 || scriptLevel==2 || scriptLevel==3){
	            	Ext.Ajax.request (
	        				{
	        				    url : 'scriptRePublishAuditing.do',
	        				    method : 'POST',
	        				    params :
	        				    {
	        			            auditor:auditor,
	        				        iworkItemid:workItemidGFSSDBCHECK,
	        				        fromId : iidGFSSDBCHECK,
	        				        planTime: '',
	        				        scriptLevel: scriptLevel,
			  				    	publishDesc: publishDescTextGFSSDBCHECK
	        				    },
	        				    success : function (response, opts)
	        				    {
	        				    	 var success = Ext.decode(response.responseText).success;
				  				        var message = Ext.decode(response.responseText).message;
				  				        if(success) {
				  				        	message="请求已经发送到复审人";
				  				        } 
				  				      Ext.Msg.alert ('提示', message, function ()
				        					    {
				        					    	if(fromGFSSDBCHECK==1) {
				        					    		messageWindow1.close();
				        					    		destroyRubbish(); //销毁本页垃圾
				        					    		contentPanel.getLoader().load({
				        					    			url: 'forwardScriptLibrary.do',
				        					    			scripts: true});
				        					    		
				        					    	} else {
				        					    		messageWindow.setWidth(contentPanel.getWidth ());
				        					    		messageWindow.setHeight(contentPanel.getHeight ());
				        					    		messageWindow.center();
				        					    		messageWindow.getLoader ().load (
				        									{
				        									    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
				        									    autoLoad : true,
				        									    scripts : true
				        									});
				        									messageWindow.setTitle ('待办事项');
				        					    	}
				        					    });
	        				    }
	        				});
	  				}else{
	  					Ext.Msg.alert('提示', "请从下拉菜单中选择风险级别！");
	  					return;
	  				}
	            }
	        },backToDbCheckButton]
		});

	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSDBCHECK",
			width : '100%',
			height : contentPanel.getHeight (), 
			autoScroll: true,
			border : false,
			bodyPadding : 5,
			items : [ mainTabs, submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSDBCHECK();
		function initGFSSDBCHECK()
		{
		}
		
		contentPanel.on('resize', function() {
		});
});
