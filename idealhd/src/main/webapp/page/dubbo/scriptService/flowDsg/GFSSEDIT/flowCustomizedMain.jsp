<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSEDIT=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSEDIT=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSEDIT=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSEDIT=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSEDIT=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSEDIT=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>


<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSEDIT='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSEDIT='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSEDIT=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSEDIT=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSEDIT='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSEDIT='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSEDIT = <%=request.getAttribute("fromType")%>;
var workItemidGFSSEDIT = <%=request.getAttribute("workItemid")%>;
var fromGFSSEDIT = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSEDIT = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSEDIT = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSEDIT = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSEDIT = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSEDIT=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSEDIT=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSEDIT='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSEDIT='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSEDIT='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSEDIT=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSEDIT=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSEDIT=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSEDIT='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSEDIT='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSEDIT='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSEDIT = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSEDIT = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSEDIT = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSEDIT = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSEDIT = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSEDIT = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSEDIT/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSEDIT" style="width: 100%;height: 100%"></div>
</body>
</html>