/*******************************************************************************
 * 流程定制维护窗口-基础脚本
 ******************************************************************************/

Ext.onReady(function() {
	
	var jspParms = tempData;
	delete tempData;
	
	/** 获取内外对接的参数 **/
	var editor = this.GRAPHS[jspParms.namespace];
	var model = editor.editorUi.editor.graph.getModel();
	var cell = editor.currentCell;
	var configWindow = editor.configWindow;
	var childGraphHeight = jspParms.height - 131;  // 优先计算出来。如果在加载下一级graph的时候进行计算。多次调用，就会重复计算。
	
	var actType = (jspParms.actType!="true"&&jspParms.actType!="1")?false:true;
    var scriptContent = "";
    var canEditScript = false;
    var scriptServiceId = '';
    var scriptServiceLastId = -1;
    var allScriptServiceWin;

    var stepNameObj = Ext.create('Ext.form.field.Text', {
        fieldLabel: '步骤名称',
        labelWidth: 73,
        labelAlign: 'right',
        width: 570
    });
    
    var shutdownCheckboxObj = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '关机维护',
        margin: '0 0 0 10',
        hidden: actType,
        inputValue: 1
    });

    var scriptVersionStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'onlyVersion','uuid'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'getScriptServiceVersionList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var scriptVersionCbObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptVersion',
        width: 380,
        labelAlign: 'right',
        labelWidth: 83,
        queryMode: 'local',
        fieldLabel: '选择版本',
        displayField: 'onlyVersion',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择版本--',
        store: scriptVersionStore,
        listeners: {
            change: function(self, newValue, oldValue, eOpts) {
                jspParms.actionType = 'view';
                jspParms.iid = newValue;
                if(newValue) {
                	if (actType) {
                		jspParms.height = childGraphHeight;
                		jspParms.submitHide = 'true';
                		jspParms.submitType = 'cf';
                		graphPanel.getLoader().load({
                			url: 'flowCustomizedInitScriptService.do',
                			scripts: true,
                			autoLoad: true,
                			params: jspParms
                		});
                	} else {
                		Ext.Ajax.request({
                			url: 'getScritContentScriptService.do',
                			method: 'POST',
                			params: {
                				iid: newValue,
                				flag: 0
                			},
                			success: function(response, options) {
                				var content = Ext.decode(response.responseText).content;
                				scriptContentObj.setValue(content);
                			},
                			failure: function(result, request) {
                				scriptContentObj.setValue('');
                			}
                			
                		});
                	}
                }
            }
        }
    });

    var bussCbObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        queryMode: 'local',
        width: '18%',
        labelWidth: 60,
        fieldLabel: '一级分类',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCbObj.clearValue();
                bussTypeCbObj.applyEmptyText();
                bussTypeCbObj.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            },
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCbObj = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        width: '18%',
        labelWidth: 60,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        emptyText: '--请选择二级分类--',
        store: bussTypeData,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });

    var cataStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "-1",
            "name": "全部"
        },
        {
            "id": "sh",
            "name": "shell"
        },
        {
            "id": "bat",
            "name": "bat"
        },
        {
            "id": "py",
            "name": "python"
        },
        {
            "id": "perl",
            "name": "perl"
        },
        {
            "id": "sql",
            "name": "sql"
        }]
    });

    var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptTypeParam',
        padding: '5',
        labelWidth: 60,
        queryMode: 'local',
        hidden: actType,
        fieldLabel: '脚本类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择脚本类型--',
        store: cataStore,
        width: '17%',
        labelAlign: 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        emptyText: '--请输入服务名称--',
        labelWidth: 60,
        padding: '5',
        width: '18%',
        labelAlign: 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        emptyText: '--请输入脚本名称--',
        labelWidth: 60,
        hidden: actType,
        padding: '5',
        width: '18%',
        labelAlign: 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var saveButton = Ext.create('Ext.Button', {
        text: '保存',
        cls: 'Common_Btn',
        handler: function() {
            saveFun();
        }
    });
    var backButton = Ext.create('Ext.Button', {
        text: '关闭',
        cls: 'Common_Btn',
        handler: function() {
            cell = null;
            configWindow.close();
        }
    });

    var prevButton = Ext.create('Ext.Button', {
        text: '上一步',
        cls: 'Common_Btn',
        handler: function() {
            var res = getCellFun("before");
            if (res) {
                cell = res;
                canEditScript = false;
                initFun();
            } else {
                Ext.Msg.alert('提示', "当前步骤为第一个步骤");
            }
        }
    });

    var nextButton = Ext.create('Ext.Button', {
        text: '下一步',
        cls: 'Common_Btn',
        handler: function() {
        	saveFun();
            var res = getCellFun("after");
            if (res) {
                cell = res;
                canEditScript = false;
                initFun();
            } else {
                Ext.Msg.alert('提示', "当前步骤为最后一个步骤");
            }
        }
    });

    Ext.define('scriptServiceReleaseModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'buss',
            type: 'string'
        },
        {
            name: 'bussType',
            type: 'string'
        },
        {
            name: 'bussId',
            type: 'int'
        },
        {
            name: 'bussTypeId',
            type: 'int'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'isflow',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'isshare',
            type: 'string'
        },
        {
            name: 'platForm',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        },
        {
            name: 'version',
            type: 'string'
        },
        {
            name: 'status',
            type: 'int'
        }]
    });

    var closedScriptServiceStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 15,
        model: 'scriptServiceReleaseModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryServiceForMySelfForOneList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var url = 'scriptService/queryServiceForMySelf.do';
    if(actType){
    	url = 'scriptService/queryServiceExceptMySelf.do';
    }
    var scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
    	autoLoad: false,
    	autoDestroy: true,
    	pageSize: 15,
    	model: 'scriptServiceReleaseModel',
    	proxy: {
    		type: 'ajax',
    		url: url,
    		reader: {
    			type: 'json',
    			root: 'dataList',
    			totalProperty: 'total'
    		}
    	}
    });

    closedScriptServiceStore.on('beforeload', function(store, options) {
        var new_params = {
        		iid: scriptServiceLastId
//        				iid:(scriptServiceId>0)?scriptServiceId: scriptServiceLastId
        };

        Ext.apply(closedScriptServiceStore.proxy.extraParams, new_params);
    });
    closedScriptServiceStore.on('load', function() {
    	chosedScriptServiceGrid.getSelectionModel().select(0);
    });
    
    scriptServiceReleaseStore.on('beforeload',
    		function(store, options) {
    	var new_params = {
    			bussId: bussCbObj.getValue(),
    			bussTypeId: bussTypeCbObj.getValue(),
    			scriptName: scName.getValue(),
    			serviceName: sName.getValue(),
    			scriptType: scriptTypeParam.getValue(),
    			onlyScript: actType ? 0 : 1
    	};
		if(actType){
			new_params = {
	    			bussId: bussCbObj.getValue(),
	    			bussTypeId: bussTypeCbObj.getValue(),
	    			scriptName: scName.getValue(),
	    			serviceName: sName.getValue(),
	    			scriptType: scriptTypeParam.getValue(),
	    			onlyScript: actType ? 0 : 1,
	    			serviceId : scriptServiceLastId
	    	};
		    	}
    	
    	Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
    });

    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '服务主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        width: 200,
        flex: 1
    },
    {
        text: '脚本名称',
        dataIndex: 'scriptName',
        hidden: actType,
        width: 260,
        flex: 1
    },
    {
        text: '一级分类',
        dataIndex: 'buss',
        width: 200,
        flex: 1
    },
    {
        text: '二级分类',
        dataIndex: 'bussType',
        width: 250,
        flex: 1
    },
    {
        text: '脚本类型',
        dataIndex: 'scriptType',
        hidden: actType,
        width: 80,
        renderer: function(value, p, record, rowIndex) {
            var isflow = record.get('isflow');
            var backValue = "";
            if (value == "sh") {
                backValue = "shell";
            } else if (value == "perl") {
                backValue = "perl";
            } else if (value == "py") {
                backValue = "python";
            } else if (value == "bat") {
                backValue = "bat";
            } else if (value == "sql") {
                backValue = "sql";
            }
            if (isflow == '1') {
                backValue = "组合";
            }
            return backValue;
        }
    },
    {
        text: '适用平台',
        dataIndex: 'platForm',
        hidden: actType,
        width: 100
    },
    {
        text: '脚本状态',
        dataIndex: 'status',
        width: 100,
        renderer: function(value, p, record, rowIndex) {
            if (value == -1) {
                return '<font color="#F01024">草稿</font>';
            } else if (value == 1) {
                return '<font color="#0CBF47">已上线</font>';
            } else if (value == 2) {
                return '<font color="#FFA602">审核中</font>';
            } else if (value == 3) {
                return '<font color="#13B1F5">已共享</font>';
            } else if (value == 9) {
                return '<font color="">已共享未发布</font>';
            } else {
                return '<font color="#CCCCCC">未知</font>';
            }
        }
    }];

    var chosedPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: closedScriptServiceStore,
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });
    
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: scriptServiceReleaseStore,
    	dock: 'bottom',
    	displayInfo: true,
    	emptyMsg: '找不到任何记录'
    });

    var chosedScriptServiceGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	cls:'window_border panel_space_right panel_space_left panel_space_bottom ',
        region: 'west',
        title: '已选'+(actType ? '作业': '脚本'),
        width: actType ? 480 : 600,
        autoScroll: true,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        //margin: '5 0 5 0',
        store: closedScriptServiceStore,
        //bbar: chosedPageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
            'select': function(self, record, index, eOpts) {
                scriptVersionCbObj.setRawValue('');
                scriptVersionStore.load({
                    params: {
                        serviceId: record.get('iid'),
                        flag: 0
                    },
                    callback: function(records, operation, success) {
                    	if (records.length > 0) {
                        	var flag = false;
                        	var aaat = 0;
                        	$.each(records, function(index, record){
                        		if(record.get('uuid')==scriptServiceId) {
                        			flag = true;
                        			aaat = index;
                        			return false;
                        		}
                        	});

                        	if(flag) {
//                        		scriptVersionCbObj.setValue(scriptServiceId);
                        		scriptVersionCbObj.select(scriptVersionStore.getAt(aaat));
                        	} else {
                        		scriptVersionCbObj.select(scriptVersionStore.getAt(0));
                        	}
                        }
                    }
                });
            }
        }
    });
    
    var query_form = Ext.create('Ext.form.Panel', {
  	  region: "north",
  	  layout: 'anchor',
  	  border: false,
  	  cls:'window_border panel_space_top panel_space_right panel_space_bottom panel_space_left',
  	  items: [{
          xtype: 'toolbar',
          border: false,
          dock: 'top',
          items: [sName, scName, scriptTypeParam, bussCbObj, bussTypeCbObj, {
              xtype: 'button',
              text: '查询',
              cls: 'Common_Btn',
              handler: function() {
                  pageBar.moveFirst();
                  scriptContentObj.setValue('');
                  scriptVersionCbObj.setValue('');
              }
          },
          {
              xtype: 'button',
              text: '清空',
              cls: 'Common_Btn',
              handler: function() {
                  bussCbObj.setValue('');
                  bussTypeCbObj.setValue('');
                  scName.setValue('');
                  sName.setValue('');
                  scriptTypeParam.setValue('');
              }
          }]
      }]
    })
    
    var scriptServiceReleaseGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
		autoScroll: true,
		store: scriptServiceReleaseStore,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		//bbar: pageBar,
		columnLines: true,
		columns: scriptServiceReleaseColumns,
		cls:'window_border panel_space_right panel_space_left panel_space_bottom '
		/*dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [sName, scName, scriptTypeParam, bussCbObj, bussTypeCbObj, {
                xtype: 'button',
                text: '查询',
                cls: 'Common_Btn',
                handler: function() {
                    pageBar.moveFirst();
                    scriptContentObj.setValue('');
                    scriptVersionCbObj.setValue('');
                }
            },
            {
                xtype: 'button',
                text: '清空',
                cls: 'Common_Btn',
                handler: function() {
                    bussCbObj.setValue('');
                    bussTypeCbObj.setValue('');
                    scName.setValue('');
                    sName.setValue('');
                    scriptTypeParam.setValue('');
                }
            }]
        }]*/
    });

    var scriptContentObj = Ext.create('Ext.form.field.TextArea', {
        fieldLabel: '脚本内容',
        readOnly: true,
//        height: contentPanel.getHeight() - 165,
        //height:200,
        //width:200,
        labelAlign: 'right',
        labelWidth: 90,
        hidden: actType,
        //autoScroll: true
    });

    var showDButton = Ext.create('Ext.Button', {
        text: '显示详情',
        margin: '0 0 0 5',
        cls: 'Common_Btn',
        hidden: actType,
        handler: function() {
            var i = parseInt(scriptVersionCbObj.getValue());
            if (i > 0) {
                Ext.create('widget.window', {
                    title: '详细信息',
                    closable: true,
                    closeAction: 'destroy',
                    width: contentPanel.getWidth(),
                    minWidth: 350,
                    height: contentPanel.getHeight(),
                    draggable: false,
                    resizable: false,
                    modal: true,
                    loader: {
                        url: 'queryOneServiceForView.do',
                        params: {
                            iid: i,
                            flag: 0,
                            hideReturnBtn: 1
                        },
                        autoLoad: true,
                        scripts: true
                    }
                }).show();
            } else {
                Ext.Msg.alert('提示', '请选择版本!');
            }
        }
    });
    var graphPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        hidden: !actType,
        //autoScroll: true,
//        height: 640,
        loader: {}
    });
    var scriptContentPanel = Ext.create('Ext.form.Panel', {
        region: 'center',
        layout: 'fit',
        cls:'window_border panel_space_right panel_space_left  panel_space_bottom ',
        border: false,
        //autoScroll: true,
        items: [scriptContentObj, graphPanel],
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [scriptVersionCbObj, showDButton]
        }]
    });
    
    var formtopPanel = Ext.create('Ext.form.Panel', {

    	//var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {
    		region : 'north',
    	  	layout : 'anchor',
    	  	buttonAlign : 'center',
    	  	iselect : false,
    	  	/*bodyCls : 'x-docked-noborder-top',*/
    	  	/*baseCls:'customize_gray_back',*/
    	  	/**collapsible : true,//可收缩
    	    collapsed : true,*///默认收缩
    	  	//iqueryFun : query,
    	    border: true,
    	    cls:'window_border panel_space_top panel_space_right panel_space_bottom panel_space_left',
    	    dockedItems : [{
				xtype: 'toolbar',
				 cls:'window_border panel_space_top panel_space_right panel_space_bottom panel_space_left',
				border : false,
				dock : 'top',  
				items : [
stepNameObj, shutdownCheckboxObj,{
    xtype: 'button',
    text: '选择'+(actType ? '作业': '脚本'),
    cls: 'Common_Btn',
    margin: '0 0 0 30',
    handler: function() {
    	if (!allScriptServiceWin) {
    		allScriptServiceWin = Ext.create('Ext.window.Window', {
                title: '选择'+(actType ? '作业': '脚本'),
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                width: contentPanel.getWidth(),
                height: 530,
                layout: 'border',
                items: [query_form,scriptServiceReleaseGrid],
                buttonAlign: 'center',
                dockedItems: [{
                    xtype: 'toolbar',
                    dock: 'bottom',
                    layout: { pack: 'center' },
                    items: [{
                        xtype: "button",
                        text: "确定",
                        cls: 'Common_Btn',
                        margin: '6',
                        handler: function() {
                        	var ss = scriptServiceReleaseGrid.getSelectionModel().getSelection();
                        	if(ss.length==1) {
                        		scriptServiceLastId = ss[0].data.iid;
                        		closedScriptServiceStore.load();
                        		this.up("window").close();
                        	} else {
                        		Ext.Msg.alert('提示', '请选择记录!');
                        	}
                        }
                    }]
                }]
            });
        }
    	allScriptServiceWin.show();
    	scriptServiceReleaseStore.load();
    }
}
				]
    	    }
    		]
    	});

    Ext.create('Ext.form.Panel', {
        border: false,
        layout: 'border',
        renderTo: "flowCustomizedEditScriptWindowDiv",
        height: contentPanel.getHeight() - 40 - 65,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'bottom',
            layout: { pack: 'center' },
            items: [saveButton, backButton, prevButton, nextButton]
        }],
        items: [formtopPanel,chosedScriptServiceGrid, scriptContentPanel]
    });

    /** 初始化方法* */
    function initFun() {
        stepNameObj.setValue(cell.value);
        shutdownCheckboxObj.setValue(cell.isShutdown);
        if (cell.scriptId != null && typeof(cell.scriptId) != "undefined") {
            scriptServiceId = cell.scriptId;
        } else {
        	scriptServiceId = '';
        }
        
        if (cell.canEditScript != null && typeof(cell.canEditScript) != "undefined") {
            canEditScript = cell.canEditScript;
        }
        if (cell.scriptContent != null && typeof(cell.scriptContent) != "undefined") {
            scriptContent = cell.scriptContent;
        }
        
        if(scriptServiceId!='') {
        	// 查找upperId
        	Ext.Ajax.request({
                url: 'getScritServiceLastId.do',
                method: 'POST',
                async: false,
                params: {
                    uuid: scriptServiceId,
                },
                success: function(response, options) {
                    var success = Ext.decode(response.responseText).success;
                    if(success) {
                    	scriptServiceLastId = Ext.decode(response.responseText).scriptServiceLastId;
                    	closedScriptServiceStore.load();
                    }
                },
                failure: function(result, request) {
                }
            });
        } else {
        	closedScriptServiceStore.removeAll();
        	scriptVersionCbObj.setRawValue('');
        	scriptVersionCbObj.reset();
            scriptVersionStore.removeAll();
            scriptContentObj.setValue('');
            graphPanel.removeAll(true);
            
            graphPanel.getLoader().load({
                url: 'noFlowPage.do',
                scripts: true,
                autoLoad: true
            });
        }

    }
    function trim(t) {
        t = t.replace(/(^\s*)|(\s*$)/g, "");
        return t.replace(/(^ *)|( *$)/g, "");
    }
    function saveFun() {
        if (stepNameObj.getValue().trim() == '') {
            Ext.Msg.alert('提示', '步骤名称不允许为空!');
            return null;
        }
        if ('开始' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<开始>！');
            return null;
        }
        if ('结束' == stepNameObj.getValue().trim()) {
            Ext.Msg.alert('提示', '步骤名称不可以为<结束>！');
            return null;
        }
        var ssId = scriptVersionStore.findRecord( 'iid', scriptVersionCbObj.getValue()).get('uuid');
        if (ssId =='') {
            if (scriptServiceId == '') {
                Ext.Msg.alert('提示', '请先选择脚本服务，然后选择版本!');
                return;
            }
        } else {
            scriptServiceId = ssId;
        }

        cell.scriptId = scriptServiceId;
        cell.canEditScript = canEditScript;
        cell.value = stepNameObj.getValue();
        cell.isShutdown = shutdownCheckboxObj.getValue();
//        parent.callbackWindw();
        Ext.Msg.alert('提示', '当前步骤保存成功!');
    }

    initFun();

    /**
	 * 获取指定位置节点
	 * 
	 * @param inflag 'after'获取下一个节点 'before'获取上一个节点
	 */
    function getCellFun(inflag) {
        // 遍历所有节点
        var rootObj = model.getRoot();
        var count = model.getChildCount(rootObj);
        for (var i = 0; i < count; i++) {
            var cells = rootObj.getChildAt(i);
            var counts = cells.getChildCount();
            var beforeCell = null; // 上一个节点
            var afterCell = null; // 下一个节点
            var selfCell = null; // 自己
            for (var j = 0; j < counts; j++) {
                var cellss = cells.getChildAt(j);
                // 判断循环至的节点样式是否与传入的样式一致
                if (cellss.style == cell.style) {
                    if (cellss == cell) {
                        // 如果本次循环的节点与当前节点一致，则为变量“selfCell”赋值
                        selfCell = cell;
                    } else {
                        // 如果变量“selfCell”为空，则当为变量“beforeCell”赋值，否则为变量“afterCell”赋值
                        selfCell == null ? beforeCell = cellss: afterCell = cellss;
                    }
                    // 如果获取到了想要的节点，则跳出循环
                    if (selfCell != null && ((inflag == 'after' && afterCell != null) || (inflag == 'before' && beforeCell != null))) {
                        break;
                    }
                }
            }
            // 返回指定节点
            return inflag == 'after' ? afterCell: beforeCell;
        }
    }

});