/**
 * 任务申请-双人复核-审核时使用的参数查看弹出窗口
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.agentParamsWin', {
    extend: 'Ext.window.Window',

    title: '查看参数',
    autoScroll: true,
    modal: true,
    closeAction : 'hide',
    buttonAlign: 'center',
    draggable: true,
    resizable: false,
	width : 400,
	height : 160,
    
    initComponent: function() {
        var me = this;

        var jspParms = me.jspParms;
        var scriptuuid = jspParms.iid;
//        var selectRecord = me.selectRecord;
        var chosedAgentIds = me.chosedAgentIds;
        var globalConfigParams=me.globalConfigParams;
        function StringToPassword(strs){
			if(strs&&strs!=null&strs!=''){
				var password = '';
				for(var i=0;i<strs.length;i++){
					password = password + '●';
				}
				return password;
			}else{
				return '';
			}
		}
       var defultEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true
			})
		});
		var passwordEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true,
				inputType : 'password'
			})
		});
        
        /** 树列表columns* */
        var paramColumns = [{
            text: '主键',
            dataIndex: 'iid',
            width: 40,
            hidden: true
        },
        {
            text: '类型',
            dataIndex: 'paramType',
            width: 100,
            renderer: function(value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
	        text: '默认参数值',
	        dataIndex: 'paramDefaultValue',
	        width: 100,
	        renderer: function(value, metaData, record, rowIdx, colIdx, store) {
	            var backValue = "";
	        	if(record.get('paramType')== 'IN-string(加密)'){
	        		backValue = StringToPassword(value);
	        	}else{
	        		backValue = value;
	        	}
	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
	        	
	        	return backValue;
	        }
    	},
        {
			xtype : 'gridcolumn',
			dataIndex : 'paramValue',
			width: 100,
			text : '参数值',
			maxLength : 1000,
			allowBlank: true,
			getEditor : function(record) {
				if (record.get('paramType') != 'IN-string(加密)' ) {
					return defultEditor;
				} else {
					return passwordEditor;
				}
			},
			renderer : function(value, metaData, record, rowIdx, colIdx, store){  
	        	var backValue = "";
	        	if(record.get('paramType')== 'IN-string(加密)'){
	        		backValue = StringToPassword(value);
	        	}else{
	        		backValue = value;
	        	}
	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
	        	
	        	return backValue;
	        }
        },
        {
            text: '描述',
            dataIndex: 'paramDesc',
            flex: 1,
            renderer: function(value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
        {
            text: '顺序',
            dataIndex: 'paramOrder',
            width: 50,
            renderer: function(value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        }];
        
    	Ext.define('agentModel', {
            extend: 'Ext.data.Model',
            fields: [
                {name: 'iid',     			type: 'int'},
                {name: 'agentIp',     		type: 'string'},
                {name: 'agentPort',     	type: 'string', defaultValue: 15000},
                {name: 'agentDesc',     	type: 'string'},
                {name: 'resGroup',     		type: 'string'},
                {name: 'agentState',     	type: 'int'},
                {name: 'agentParam',     	type: 'string'},
                {name: 'agentStartUser',    type: 'string'}
            ]
        });
        
          var configParamStore = Ext.create ('Ext.data.Store',
        		{
        		    autoLoad : true,
        		    autoDestroy : true,
        		    model : 'paramModel',
        		    proxy :
        		    {
        		        type : 'ajax',
        		        url : 'getAllScriptParams.do',
        		        reader :
        		        {
        		            type : 'json',
        		            root : 'dataList'
        		        }
        		    }
        		});
        		configParamStore.on ('beforeload', function (store, options) {
        			var new_params = {
        					scriptId:scriptuuid
        			};
        			Ext.apply (configParamStore.proxy.extraParams, new_params);
        		});
        		
        
        var agent_store_chosed = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        pageSize: 50,
	        model: 'agentModel',
	        proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
        
		
		 agent_store_chosed.on('beforeload',
		    function(store, options) {
		        var new_params = {  
		    			agentIds : JSON.stringify(chosedAgentIds),
		    			flag :1
		    		};
		        Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
		    });
		
		agent_store_chosed.on('load',
		    function() {
		        agent_grid_chosed_for_config_param.getSelectionModel().select(0);
		  });
        var agent_grid_chosed_for_config_param = Ext.create('Ext.ux.ideal.grid.Panel', {
  		  title: '已选服务器列表',
  	    	store:agent_store_chosed,
  	    	border:true,
  	    	columnLines : true,
  	    	cls:'window_border panel_space_top panel_space_left panel_space_right',
        	ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
  	    	width: '49%',
  	    	height : contentPanel.getHeight () -150,
  	    	autoScroll : true,
  	    	emptyText: '没有选择服务器',
  	    	columns: [{ text: '序号', xtype:'rownumberer', width: 40 },
                  { text: '主键',  dataIndex: 'iid',hidden:true},
                  { text: 'IP',  dataIndex: 'agentIp',width:120},
                  { text: '端口号',  dataIndex: 'agentPort',width:100},
  	            { text: '描述',  dataIndex: 'agentDesc',flex:1},
  	            { text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
  	            	var backValue = "";
  	            	if(value==0){
  	            		backValue = "Agent正常";
  	            	}else if(value==1){
  	            		backValue = "Agent异常";
  	            	}
  	            	return backValue;
  	            }}
             ],
  			listeners : {
         			select:function(self, record, index, eOpts) {
         				var ipId = record.get('iid');
         				if(globalConfigParams.hasOwnProperty(ipId)){
         					var cp = globalConfigParams[ipId];
         					configParamStore.each(function(record){
         						record.set('paramValue', cp[record.get('iid')])
         					});
         				} else {
         					configParamStore.each(function(record){
         						record.set('paramValue', '');
         					});
         				}
         			 }
         		}
  	    });

        /** 树数据Model* */
    	Ext.define('paramModel', {
            extend: 'Ext.data.Model',
            fields: [{
                name: 'iid',
                type: 'int'
            },
            {
                name: 'paramType',
                type: 'string'
            },
            {
                name: 'paramDefaultValue',
                type: 'string'
            },
            {
            	name: 'paramValue',
            	type: 'string'
            },
            {
                name: 'paramDesc',
                type: 'string'
            },
            {
                name: 'paramOrder',
                type: 'int'
            }]
        });
    	
      
        
    	var paramGrid_for_config_params = Ext.create ('Ext.grid.Panel',
    			{
    				width: '50%',
    				height: contentPanel.getHeight() - 250,
    			    store : configParamStore,
    			    margin: '0 0 0 10',
    			    title: '参数信息',
    			    border : true,
    			    columnLines : true,
    			    columns : paramColumns,
    			    collapsible : false
    			});
    	
    	paramGrid_for_config_params.on('edit', function(editor, e) {
    		var ipRecord = agent_grid_chosed_for_config_param.getSelectionModel().getSelection()[0];
    		if(ipRecord) {
    			var configParams = {};
    			for (var i = 0; i <  e.grid.getStore().getCount(); i++) {
    				var record =  e.grid.getStore().getAt(i);
    				var iid = record.get('iid');
    				var paramType = record.get('paramType');
    				var paramValue = record.get('paramValue');
    				
    				if (paramType == 'int'&&paramValue) {
    	                if (!checkIsInteger(paramValue)) {
    	                	Ext.Msg.alert('提示', '参数类型为int，但参数值不是int类型！');
    	                    return;
    	                }
    	            }
    	            if (paramType == 'float'&&paramValue) {
    	                if (!checkIsDouble(paramValue)) {
    	                	Ext.Msg.alert('提示', '参数类型为float，但参数值不是float类型！');
    	                    return;
    	                }
    	            }
    	            if (paramValue.indexOf('"')>=0) {
    	            	if(cellScriptType=='bat') {
    	            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
    	                    return;
    	            	}
    	            }
    	            
    	            if(!Ext.isEmpty(Ext.util.Format.trim(paramValue))) {
    	            	configParams[iid] = paramValue;
    	            }
    			}
    			globalConfigParams[ipRecord.get('iid')] = configParams;//绑定
    		}
    	});
		var choseAgentWrapper_for_config_params = Ext.create('Ext.panel.Panel',{
	        border : false,
	        height : contentPanel.getHeight ()-100,
	        layout: {
	            type: 'hbox',
	            padding:'5',
	            align:'stretch'
	        },
	        items : [agent_grid_chosed_for_config_param,paramGrid_for_config_params]
		});
        
		Ext.applyIf(me, {
      	    items:[choseAgentWrapper_for_config_params]
      });
		
        me.callParent(arguments);
        
//        agent_store_chosed.removeAll();
//		agent_store_chosed.loadRecords(selectRecord);
//        agent_store_chosed.load();
//		agent_grid_chosed_for_config_param.getSelectionModel().select(0);
    }

});