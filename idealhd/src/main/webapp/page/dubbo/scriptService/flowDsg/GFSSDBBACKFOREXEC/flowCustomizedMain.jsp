<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSDBBACKFOREXEC=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSDBBACKFOREXEC=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSDBBACKFOREXEC=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSDBBACKFOREXEC=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSDBBACKFOREXEC=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSDBBACKFOREXEC=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSDBBACKFOREXEC = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSDBBACKFOREXEC='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSDBBACKFOREXEC='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSDBBACKFOREXEC=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSDBBACKFOREXEC=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSDBBACKFOREXEC='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSDBBACKFOREXEC='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSDBBACKFOREXEC = <%=request.getAttribute("fromType")%>;
var workItemidGFSSDBBACKFOREXEC = <%=request.getAttribute("workItemid")%>;
var fromGFSSDBBACKFOREXEC = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSDBBACKFOREXEC = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSDBBACKFOREXEC = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSDBBACKFOREXEC = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSDBBACKFOREXEC = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSDBBACKFOREXEC=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSDBBACKFOREXEC=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSDBBACKFOREXEC='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSDBBACKFOREXEC='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSDBBACKFOREXEC='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSDBBACKFOREXEC=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSDBBACKFOREXEC=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSDBBACKFOREXEC=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSDBBACKFOREXEC='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSDBBACKFOREXEC='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSDBBACKFOREXEC='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSDBBACKFOREXEC = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSDBBACKFOREXEC = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSDBBACKFOREXEC = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSDBBACKFOREXEC = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSDBBACKFOREXEC = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSDBBACKFOREXEC = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSDBBACKFOREXEC/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSDBBACKFOREXEC" style="width: 100%;height: 100%"></div>
</body>
</html>