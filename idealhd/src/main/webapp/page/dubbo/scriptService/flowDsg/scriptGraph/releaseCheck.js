/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.releaseCheck', {
    extend: 'Ext.form.Panel',
    alias: 'widget.releaseCheck',

    requires: [
        'Ext.form.ComboBox',
        'Ext.form.TextArea',
        'Ext.button.Button'
    ],

    height: 150,
    title: '',
    frame : true,
	buttonAlign : "left",
	
	layout: 'hbox',
	
	serviceInfo : {},
	initOperBtn: function(n){
		
	},
	
    initComponent: function() {
        var me = this;
        var jspParms = me.jspParms;
        var serviceInfo = me.serviceInfo;
        if(jspParms.scriptLevelDisplay==undefined)
        {
        	jspParms.scriptLevelDisplay = '';
        }
        var isHide =false;
    	if(!(jspParms.doubleLevelSwitch=='true')){
    		isHide= true;
    	}else{
    		if(jspParms.isReCheck=='true'){
    			isHide= true;
    		} 
    	}
        function publishBtnFn(){
	    	var scriptLevel = scriptLevelCb.getValue();
			if(!scriptLevel) {
				Ext.Msg.alert('提示', "没有选择风险级别！");
				return;
			}
			if(scriptLevel=='高级风险'){
  			    scriptLevel = 1;
  			}else if(scriptLevel=='中级风险'){
  				scriptLevel = 2;
  			}else if(scriptLevel=='低级风险'){
  				scriptLevel = 3;
  			}
			var isEmScript = isEMscript.getValue();
				if(isEmScript) {
					isEmScript = 1;
				}else{
					isEmScript = 0;
				}
			var appIds = appSysObj.getValue();
			if(scriptLevel==1 || scriptLevel==2 || scriptLevel==3){
	    	Ext.Ajax.request (
					{
					    url : 'scriptPublishForOneRecord.do',
					    method : 'POST',
					    params :
					    {
					        iid : jspParms.iid,
					        scriptUuid:jspParms.scriptUuid,
					        flag : jspParms.fromType,
					        iworkItemid: jspParms.iworkItemid,
					        scriptLevel: scriptLevel,
					        isEmScript: isEmScript,
							appSysIds:appIds
					    },
					    success : function (response, opts)
					    {
						    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
						    {
						    	if(jspParms.from==1) {
						    		messageWindow1.close();
						    		destroyRubbish(); //销毁本页垃圾
						    		contentPanel.getLoader().load({
						    			url: 'forwardScriptLibrary.do',
						    			scripts: true});
						    		
						    	}else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
		    					    		messageWindow_ssq.close();
		    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
	    					     } else {
					    		messageWindow.getLoader ().load (
									{
									    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
									    autoLoad : true,
									    scripts : true
									});
									messageWindow.setTitle ('待办事项');
					    	}
						    });
					    }
					});
			}else{
				Ext.Msg.alert('提示', "请从下拉菜单中选择风险级别！");
					return;
			}
	    
        }
        
        function nowayBtnFn(){
		    	var scriptAudiFrom = 1;
		    	if(jspParms.actionType=='dbcheckForExec') {
		    		scriptAudiFrom = 2;
		    	}
		    	Ext.MessageBox.wait ("数据处理中...", "进度条");
				var backI = backInfo.getValue();
				if (fucCheckLength (backI.trim ()) < 1)
				{
					Ext.Msg.alert ('提示', "请输入打回原因!");
					return;
					
				}
				if (!illegalCharPassForTextArea (backI))
				{
					Ext.MessageBox.alert ("提示", "打回原因只能由汉字、字母、数字、下划线、冒号以及减号组成！");
					return;
				}
				if (fucCheckLength (backI.trim ()) > 4000)
				{
					Ext.Msg.alert ('提示', "打回原因长度最大为4000个字符!");
					return;
				}
				Ext.Ajax.request (
				{
				    url : 'operWorkitemByiidForSsPublish.do',
				    method : 'POST',
				    params :
				    {
				        istateForQuery: 2,
				        scriptAudiFrom: scriptAudiFrom,
				        iidForQuery: jspParms.iworkItemid,
				        ibackInfo: backI
				    },
				    success : function (response, opts)
				    {
					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
					    {
					    	if(jspParms.from==1) {
					    		messageWindow1.close();
					    		destroyRubbish(); //销毁本页垃圾
					    		contentPanel.getLoader().load({
					    			url: 'pandect1.do',
					    			scripts: true});
					    	} else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
		    					    		messageWindow_ssq.close();
		    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
		    			    }else {
					    		messageWindow.getLoader ().load (
										{
										    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
										    autoLoad : true,
										    scripts : true
										});
										messageWindow.setTitle ('待办事项');
					    	}
					    	
					    });
				    }
				});
        } 
        
        function backToDbCheckBtnFn(){
	    	if(jspParms.from==1) {
	    		messageWindow1.close();
	    	} else {
				messageWindow.getLoader ().load (
						{
							url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
							autoLoad : true,
							scripts : true
						});
				messageWindow.setTitle ('待办事项');
			}
        }
        function viewBasicInfoBtnFn(serviceInfo){
   				Ext.create('Ext.window.Window', {
   		            title: '基本信息',
   		            autoScroll: true,
   		            modal: true,
   		            closeAction: 'destroy',
   		            buttonAlign: 'center',
   		            draggable: true,
   		            resizable: false,
   		            width: 500,
   		            height: 328,
   		            loader: {
   		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
   		            	params: {
   		            		creatorFullName: 	serviceInfo.creatorFullName,
   			                bussName: 			serviceInfo.bussName,
   			                bussTypeName:		serviceInfo.bussTypeName,
   			                funcDescText: 		serviceInfo.funcDescText,
   			                serviceName:		serviceInfo.serviceName
   		            	},
   		            	autoLoad: true
   		            },
   		            dockedItems: [{
   		                xtype: 'toolbar',
   		                border: false,
   		                dock: 'bottom',
   		                margin: '0 0 5 0',
   		                layout: {pack: 'center'},
   		                items: [{
   		                    xtype: 'button',
   		                    text: '关闭',
   		                    cls: 'Common_Btn',
   		                    handler: function() {
   		                        this.up("window").close();
   		                    }
   		                }]
   		            }]
   		        }).show();
   			}
        
        Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var recheckAuditorStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getPublishRecheckAuditorList.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		recheckAuditorStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		iworkItemid : jspParms.workItemid
		    };
		    Ext.apply(recheckAuditorStore.proxy.extraParams, new_params);
	    });
		
		var recheckAuditorComBox = Ext.create('Ext.form.ComboBox', {
		    editable: false,
		    fieldLabel: "复审人",
		    labelWidth: 85,
		    labelAlign:'right',
		    store: recheckAuditorStore,
		    hidden : isHide,
		    queryMode: 'local',
		    width:'100%',
		    displayField: 'fullName',
		    valueField: 'loginName',
		    listeners: { //监听 
		        render : function(combo) {//渲染 
		            combo.getStore().on("load", function(s, r, o) { 
		                combo.setValue(r[0].get('loginName'));//第一个值 
		            }); 
		        } 
		    } 
		  });
    	
   	 var levelStore = Ext.create('Ext.data.Store', {
   		    fields: ['iid', 'scriptLevel'],
   		    data : [
   		    	{"iid":"1", "scriptLevel":"高级风险"},
   		    	{"iid":"2", "scriptLevel":"中级风险"},
   		    	{"iid":"3", "scriptLevel":"低级风险"}
   		    ]
   		});
   	 
   	 var scriptLevelCb = Ext.create('Ext.form.field.ComboBox', {
   	        name: 'scriptLevel',
   	        labelWidth: 93,
   	        width : '100%',
   	        queryMode: 'local',
   	        fieldLabel: '风险级别',
   	        labelAlign:'right',
   	        displayField: 'scriptLevel',
   	        valueField: 'iid',
   	        editable: false,
   	        value: jspParms.scriptLevelDisplay,
   	        emptyText: '--请选择风险级别--',
   	        store: levelStore
   	    });
   	 var noWayButton = Ext.create('Ext.Button', {
   		    text : '打回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    handler : nowayBtnFn
   	  });
   	 
   	 var backToDbCheckButton = Ext.create('Ext.Button', {
   		    text : '返回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    hidden:  jspParms.from ==66?true:false,
   		    handler : backToDbCheckBtnFn
   	  });
   	 
   	 var viewBasicInfoButton = Ext.create("Ext.Button", {
   			text: "基本信息",
   			disabled : false,
   			handler: function () {
   				viewBasicInfoBtnFn(serviceInfo)
   			}
   		});
   	 
   	 var publishButton = Ext.create('Ext.Button', {
	    text : '发布',
	    margin : '0 0 0 5',
	    textAlign : 'center',
	    handler : publishBtnFn
	  });
   	 
   	 var reCheckButton = Ext.create('Ext.Button', {
         text: '申请复审',
         margin : '0 0 0 5',
         hidden : isHide,
         handler: function(){
         	var scriptLevel = scriptLevelCb.getValue();
				if(!scriptLevel) {
					Ext.Msg.alert('提示', "没有选择风险级别！");
					return;
				}
				var auditor = recheckAuditorComBox.getValue();
				if(!auditor) {
					Ext.Msg.alert('提示', "没有选择复审人！");
					return;
				}
				if(scriptLevel=='高级风险'){
	  			    scriptLevel = 1;
	  			}else if(scriptLevel=='中级风险'){
	  				scriptLevel = 2;
	  			}else if(scriptLevel=='低级风险'){
	  				scriptLevel = 3;
	  			}
				var isEmScript = isEMscript.getValue();
  				if(isEmScript) {
  					isEmScript = 1;
  				}else{
  					isEmScript = 0;
  				}
  				var appIds = appSysObj.getValue();
				if(scriptLevel==1 || scriptLevel==2 || scriptLevel==3){
         	Ext.Ajax.request (
     				{
     				    url : 'scriptRePublishAuditing.do',
     				    method : 'POST',
     				    params :
     				    {
     			            auditor: auditor,
     				        iworkItemid: jspParms.iworkItemid,
     				        fromId : jspParms.iid,
     				        planTime: '',
     				        scriptLevel: scriptLevel,
		  				    publishDesc: jspParms.publishDesc,
		  				    isEmScript: isEmScript,
							appSysIds:appIds
     				    },
     				    success : function (response, opts)
     				    {
     				    	 var success = Ext.decode(response.responseText).success;
			  				        var message = Ext.decode(response.responseText).message;
			  				        if(success) {
			  				        	message="请求已经发送到复审人";
			  				        } 
			  				      Ext.Msg.alert ('提示', message, function ()
			        					    {
			        					    	if(jspParms.from==1) {
			        					    		messageWindow1.close();
			        					    		destroyRubbish(); //销毁本页垃圾
			        					    		contentPanel.getLoader().load({
			        					    			url: 'forwardScriptLibrary.do',
			        					    			scripts: true});
			        					    		
			        					    	} else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
							    					    		messageWindow_ssq.close();
							    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
							    			    }else {
			        					    		messageWindow.setWidth(contentPanel.getWidth ());
			        					    		messageWindow.setHeight(contentPanel.getHeight ());
			        					    		messageWindow.center();
			        					    		messageWindow.getLoader ().load (
			        									{
			        									    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
			        									    autoLoad : true,
			        									    scripts : true
			        									});
			        									messageWindow.setTitle ('待办事项');
			        					    	}
			        					    });
     				    }
     				});
				}else{
					Ext.Msg.alert('提示', "请从下拉菜单中选择风险级别！");
					return;
				}
         }
     });
   	Ext.define('AppSysModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'int',
            useNull: true
        },
        {
            name: 'name',
            type: 'string'
        }]
    });
	var appSysStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'AppSysModel',
        proxy: {
            type: 'ajax',
            url: 'getAppSysList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	appSysStore.on('load', function() {
        var ins_rec = Ext.create('AppSysModel', {
            id: '-1',
            name: '未选系统'
        });
        appSysStore.insert(0, ins_rec);
        //字符串转数组
        if(jspParms.appSysIds){
        	appSysObj.setValue(jspParms.appSysIds.split(',').map(Number));
        }
    });
	var appSysObj = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '应用系统',
        emptyText : '--请选择应用系统--',
        multiSelect: true,
        labelWidth: 85,
        labelAlign: 'right',
        width: '100%',
        hidden : !reviewSwitch,
        store: appSysStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        //editable: false,
        mode: 'local',
        listeners: {
            beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });
	var isEMscript = Ext.create('Ext.form.field.Checkbox', {
        boxLabel: '是否应急',
        inputValue: 1,
        hidden :!reviewSwitch,
        width: 120,
        margin: '0 0 0 10'
    });
	isEMscript.setValue(jspParms.isEMscriptText);
	 var publishDesc = Ext.create('Ext.form.field.TextArea', {
	        fieldLabel: '发布申请说明',
	        labelAlign:'right',
	        padding : '5 0 5 0',
	        labelWidth: 93,
	        width : '100%',
	        height : reviewSwitch?20:40,
	        value: jspParms.publishDesc,
	        readOnly: true,
	        autoScroll: true
	    });
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea', {
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        flex: 1,
	        labelAlign:'right',
	        padding : '2 0 0 0',
	        width : '100%',
	        height : reviewSwitch?30:60,
	        labelWidth: 85,
	        labelSepartor : "：",
	        readOnly: jspParms.actionType=='dbback',
	        value: jspParms.backInfo
		});
		
        Ext.applyIf(me, {
            items: [{
	            border: false,
	            flex: 0.5,
	            layout: 'vbox',
	            
	            items: [scriptLevelCb,isEMscript,publishDesc]
	        }, {
	            border: false,
	            flex: 0.5,
	            layout: 'vbox',
	            items: [recheckAuditorComBox,appSysObj,backInfo]
	        }],
            buttons: [viewBasicInfoButton,publishButton,noWayButton,reCheckButton,backToDbCheckButton]
        });
    	
        me.callParent(arguments);
    }
});