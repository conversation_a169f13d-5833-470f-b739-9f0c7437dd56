/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSDBBACK;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSDBBACK
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
	
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-60,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSDBBACK.do',
	        params: {
	        	flag:flagGFSSDBBACK,
	        	actionType:actionTypeGFSSDBBACK
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSDBBACK.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	 
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea',
		{
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        padding : '5 0 5 5',
	        labelWidth: 70,
	        height : 50,
	        maxLength : 2000,
	        labelSepartor : "：",
	        readOnly: actionTypeGFSSDBBACK=='dbback',
	        value: backInfoContentGFSSDBBACK,
	        width : contentPanel.getWidth () - 300
		});
		
	 var backToDbCheckButton = Ext.create('Ext.Button', {
		    text : '返回',
		    margin : '0 0 0 5',
		    textAlign : 'center',
		    handler : function() {
		    	if(fromGFSSDBBACK==1) {
		    		messageWindow1.close();
		    	} else {
					messageWindow.getLoader ().load (
							{
								url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
								autoLoad : true,
								scripts : true
							});
					messageWindow.setTitle ('待办事项');
				}
		    }
	  });
	 
	 var dbSubButton = Ext.create('Ext.Button', {
		    text : '提交',
		    margin : '0 0 0 5',
		    textAlign : 'center',
		    handler : function() {
		    	Ext.Ajax.request ({
				    url : 'getScriptServiceStatusByWorkItemId.do',
				    method : 'POST',
				    params :
				    {
				    	workItemId : workItemidGFSSDBBACK,
				    },
				    success : function (response, opts)
				    {
					    var status = Ext.decode (response.responseText).status;
					    if (status != -1 ) { // 已经不是草稿状态，处于审核中或者已经上线
							Ext.Msg.alert('提示', "该脚本已经不是草稿状态,请终止该服务！");
							return;
						}
				    	var e = auditorComBox_sm.getValue();
		            	if(!e) {
		            		Ext.Msg.alert ('提示','没有选择审核人！');
		            		return;
		            	}
				    	Ext.Ajax.request ({
						    url : 'operWorkitemByiidForSsPublish.do',
						    method : 'POST',
						    params :
						    {
						        istateForQuery : 1,
						        iidForQuery : workItemidGFSSDBBACK,
						        execUser: e
						    },
						    success : function (response, opts)
						    {
							    var success = Ext.decode (response.responseText).success;
							    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
							    {
							    	if(fromGFSSDBBACK==1) {
							    		messageWindow1.close();
							    		destroyRubbish(); //销毁本页垃圾
							    		contentPanel.getLoader().load({
							    			url: 'pandect1.do',
							    			scripts: true});
							    		
							    	} else {
							    		messageWindow.getLoader ().load (
											{
											    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
											    autoLoad : true,
											    scripts : true
											});
											messageWindow.setTitle ('待办事项');
							    	}
							    });
						    }
						});
				    }
				});
		    }
		  });
	 var dbtermiButton = Ext.create('Ext.Button', {
		 text : '终止',
		 margin : '0 0 0 5',
		 textAlign : 'center',
		 handler : function() {
			 Ext.Ajax.request (
						{
						    url : 'operWorkitemByiidForSsPublish.do',
						    method : 'POST',
						    params :
						    {
						        istateForQuery : 6,
						        iidForQuery : workItemidGFSSDBBACK
						    },
						    success : function (response, opts)
						    {
							    var success = Ext.decode (response.responseText).success;
							    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
							    {
							    	if(fromGFSSDBBACK==1) {
							    		messageWindow1.close();
							    		destroyRubbish(); //销毁本页垃圾
							    		contentPanel.getLoader().load({
							    			url: 'pandect1.do',
							    			scripts: true});
							    	} else {
							    		messageWindow.getLoader ().load (
											{
											    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
											    autoLoad : true,
											    scripts : true
											});
											messageWindow.setTitle ('待办事项');
							    	}
							    });
						    }
						});
		 }
	 });
	 
	 var viewBasicInfoButton = Ext.create("Ext.Button", {
			text: "基本信息",
			disabled : false,
			handler:function(){
				Ext.create('Ext.window.Window', {
		            title: '基本信息',
		            autoScroll: true,
		            modal: true,
		            closeAction: 'destroy',
		            buttonAlign: 'center',
		            draggable: true,
		            resizable: false,
		            width: 500,
		            height: 328,
		            loader: {
		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
		            	params: {
		            		creatorFullName: creatorFullName,
			                bussName: bussName,
			                bussTypeName:bussTypeName,
			                funcDescText: funcDescText,
			                serviceName:serviceName
		            	},
		            	autoLoad: true
		            },
		            dockedItems: [{
		                xtype: 'toolbar',
		                border: false,
		                dock: 'bottom',
		                margin: '0 0 5 0',
		                layout: {pack: 'center'},
		                items: [{
		                    xtype: 'button',
		                    text: '关闭',
		                    cls: 'Common_Btn',
		                    handler: function() {
		                        this.up("window").close();
		                    }
		                }]
		            }]
		        }).show();
			}
		});
	 
	 Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		var auditorStore_sm = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getPublishAuditorList.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
		    editable: false,
		    fieldLabel: "审核人",
		    labelWidth: 70,
//		    padding: 5,
		    store: auditorStore_sm,
		    queryMode: 'local',
//		    width: 200,
		    columnWidth:.98,
		    padding: 5,
		    displayField: 'fullName',
		    valueField: 'loginName'//,
		    //value: auditor
		  });
	
	var submitFromPanel = Ext.create('Ext.form.Panel', {
		width : '100%',
		frame : true,
		buttonAlign : "left",
		height: 170,
		items:[backInfo, auditorComBox_sm],
		buttons:[viewBasicInfoButton, dbSubButton,dbtermiButton,backToDbCheckButton]
	});
	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSDBBACK",
			width : '100%',
			height : contentPanel.getHeight (), 
			autoScroll: true,
			border : false,
			bodyPadding : 5,
			items : [ mainTabs,submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSDBBACK();

		function initGFSSDBBACK()
		{
		}
		contentPanel.on('resize', function() {
		});
});
