<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSDBCHECKFOREXEC=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSDBCHECKFOREXEC=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSDBCHECKFOREXEC=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSDBCHECKFOREXEC=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSDBCHECKFOREXEC=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSDBCHECKFOREXEC=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSDBCHECKFOREXEC = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSDBCHECKFOREXEC='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSDBCHECKFOREXEC='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSDBCHECKFOREXEC=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSDBCHECKFOREXEC=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSDBCHECKFOREXEC='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSDBCHECKFOREXEC='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSDBCHECKFOREXEC = <%=request.getAttribute("fromType")%>;
var workItemidGFSSDBCHECKFOREXEC = <%=request.getAttribute("workItemid")%>;
var fromGFSSDBCHECKFOREXEC = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSDBCHECKFOREXEC = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSDBCHECKFOREXEC = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSDBCHECKFOREXEC = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSDBCHECKFOREXEC = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSDBCHECKFOREXEC=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSDBCHECKFOREXEC=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSDBCHECKFOREXEC='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSDBCHECKFOREXEC='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSDBCHECKFOREXEC='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSDBCHECKFOREXEC=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSDBCHECKFOREXEC=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSDBCHECKFOREXEC=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSDBCHECKFOREXEC='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSDBCHECKFOREXEC='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSDBCHECKFOREXEC='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSDBCHECKFOREXEC = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSDBCHECKFOREXEC = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSDBCHECKFOREXEC = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSDBCHECKFOREXEC = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSDBCHECKFOREXEC = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSDBCHECKFOREXEC = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSDBCHECKFOREXEC/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSDBCHECKFOREXEC" style="width: 100%;height: 100%"></div>
</body>
</html>