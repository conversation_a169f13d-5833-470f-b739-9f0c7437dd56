/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSVIEWVERSION = null;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSVIEWVERSION
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
    
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-46,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSVIEWVERSION.do',
	        params: {
	        	flag:flagGFSSVIEWVERSION,
	        	actionType:actionTypeGFSSVIEWVERSION
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSVIEWVERSION.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSVIEWVERSION",
			width : '100%',
			height : contentPanel.getHeight (), 
//			overflowY:'scroll',
			autoScroll: true,
			border : false,
			bodyPadding : '5 0 0 0',
			items : [ mainTabs]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSVIEWVERSION();

		function initGFSSVIEWVERSION()
		{
			
		}
		
		contentPanel.on('resize', function() {
		});
});
