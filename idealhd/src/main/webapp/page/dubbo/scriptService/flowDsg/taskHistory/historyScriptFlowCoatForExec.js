var scriptmonitor_store2ForExecForFlow;
var personExcute_window2ForExecForFlow;
var search_form2ForExecForFlow;
Ext
		.onReady(function() {
			destroyRubbish();
			var flag = 1; // 生产
			/*
			 * Ext.define('startUserModel', { extend : 'Ext.data.Model', fields : [{
			 * name : 'iid', type : 'string' }, { name : 'iusername', type :
			 * 'string' }]
			 * 
			 * });
			 */

			var stateStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
		            "id": "-1",
		            "name": "全部"
		        },
		        {
		            "id": "101",
		            "name": "完成"
		        },
		        {
		            "id": "20",
		            "name": "正常完成"
		        },
		        {
		            "id": "40",
		            "name": "异常完成"
		        },
		        {
		            "id": "102",
		            "name": "运行"
		        },
		        {
		            "id": "10",
		            "name": "正常运行"
		        },
		        {
		            "id": "50",
		            "name": "异常运行"
		        },
		        {
					"id" : "30",
					"name" : "异常"
				},
		        {
		            "id": "60",
		            "name": "终止"
		        } ]
			});
			/*
			 * var cataStore = Ext.create('Ext.data.Store', { fields: ['id',
			 * 'name'], data : [ {"id":"-1", "name":"全部"}, {"id":"0",
			 * "name":"测试"}, {"id":"1", "name":"生产"} ] });
			 */

			/*
			 * var startUserStore = Ext.create('Ext.data.Store', { autoLoad :
			 * true, autoDestroy : true, model : 'startUserModel', proxy : {
			 * type : 'ajax', url : 'getStartUser.do', reader : { type : 'json',
			 * root : 'dataList' } } });
			 */

			search_form2ForExecForFlow = Ext.create('Ext.form.Panel', {
				layout : 'anchor',
				region : 'north',
				bodyCls:'x-docked-noborder-top',
				baseCls:'customize_gray_back',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					baseCls:'customize_gray_back',
					dock : 'top',
					items : [ {
						fieldLabel : '服务名称',
						labelWidth : 65,
						name : 'scriptName',
						labelAlign : 'right',
						width : '22%',
						xtype : 'textfield',
						value: filter_scriptNameForExecForFlow,
						listeners: {
				            specialkey: function(field, e){
				                if (e.getKey() == e.ENTER) {
				                	pageBar.moveFirst();
				                }
				            }
				        }
					}, {
						fieldLabel : '执行状态',
						labelAlign : 'right',
						width : '16%',
						labelWidth : 65,
						name : 'state',
						displayField : 'name',
						valueField : 'id',
						store : stateStore,
						queryMode : 'local',
						listeners : {
							afterRender : function(combo) {
					               if(filter_stateForExecForFlow=='-1') {
										combo.setValue(stateStore.getAt(0).data.id);
									} else if(filter_stateForExecForFlow=='101'){
										combo.setValue(stateStore.getAt(1).data.id);
									} else if(filter_stateForExecForFlow=='20'){
										combo.setValue(stateStore.getAt(2).data.id);
									} else if(filter_stateForExecForFlow=='40'){
										combo.setValue(stateStore.getAt(3).data.id);
									} else if(filter_stateForExecForFlow=='102'){
										combo.setValue(stateStore.getAt(4).data.id);
									} else if(filter_stateForExecForFlow=='10'){
										combo.setValue(stateStore.getAt(5).data.id);
									} else if(filter_stateForExecForFlow=='50'){
										combo.setValue(stateStore.getAt(6).data.id);
									} else if(filter_stateForExecForFlow=='30'){
										combo.setValue(stateStore.getAt(7).data.id);
									} else if(filter_stateForExecForFlow=='60'){
										combo.setValue(stateStore.getAt(8).data.id);
									}
					            },
						            specialkey: function(field, e){
						                if (e.getKey() == e.ENTER) {
						                	pageBar.moveFirst();
						                }
						            }
						},
						// editable:false,
						xtype : 'combobox'
					}, {
						fieldLabel : '开始时间',
						xtype : 'datefield',
						labelAlign : 'right',
						width : '22%',
						labelWidth : 65,
						name : 'startTime',
						format : 'Y-m-d',
						value: filter_startTimeForExecForFlow,
						listeners: {
				            specialkey: function(field, e){
				                if (e.getKey() == e.ENTER) {
				                	pageBar.moveFirst();
				                }
				            }
				        }
					}, {
						fieldLabel : '结束时间',
						xtype : 'datefield',
						labelAlign : 'right',
						width : '22%',
						labelWidth : 65,
						name : 'endTime',
						format : 'Y-m-d',
						value: filter_endTimeForExecForFlow,
						listeners: {
				            specialkey: function(field, e){
				                if (e.getKey() == e.ENTER) {
				                	pageBar.moveFirst();
				                }
				            }
				        }
					}, {
						xtype : 'button',
						// columnWidth:.07,
						text : '查询',
						baseCls : 'Common_Btn',
						handler : function() {
							pageBar.moveFirst();
						}
					}, {
						xtype : 'button',
						// columnWidth:.07,
						text : '清空',
						baseCls : 'Common_Btn',
						handler : function() {
							clearQueryWhere();
						}
					},
		            {
		                xtype: 'button',
		                text: '返回',
		                baseCls: 'Common_Btn',
		                handler: function() {
		                	forwardtestmainExec2ForExecForFlow();
		                }
		            } 
					]
				} ]
			});

			Ext.define('scriptmonitorData', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				},{
					name : 'serviceName',
					type : 'string'
				}, {
					name : 'taskName',
					type : 'string'
				}, {
					name : 'state',
					type : 'int'
				}, {
					name : 'cata',
					type : 'int'
				}, {
		        	name: 'flowId',
		        	type: 'int'
		        },
		        {
		        	name: 'actNo',
		        	type: 'int'
		        }, {
					name : 'startUser',
					type : 'string'
				}, {
					name : 'startTime',
					type : 'string'
				}, {
					name : 'endTime',
					type : 'string'
				}, {
					name : 'actType',
					type : 'string'
				},
				{
		        	name: 'actName',
		        	type: 'string'
		        },
		        {
		        	name: 'childFlowId',
		        	type: 'int'
		        },
		        {
					name : 'serverNum',
					type : 'int'
				} ]
			});

			scriptmonitor_store2ForExecForFlow = Ext.create('Ext.data.Store', {
				autoLoad : true,
				pageSize : 50,
				model : 'scriptmonitorData',
				proxy : {
					type : 'ajax',
					url : 'getHistoryScriptCoatList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			var scriptmonitor_columns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '执行状态',
						dataIndex : 'state',
						width : 100,
						renderer : function(value, p, record) {
							var backValue = "";
							if (value == -1) {
				                backValue = '<span class="Not_running State_Color">未运行</span>';
				            } else if (value == 10 || value == 11) {
								backValue = '<span class="Run_Green State_Color">运行</span>';
							} else if (value == 20 || value == 5) {
								backValue = "<span class='Complete_Green State_Color'>完成</span>"
							} else if (value == 30) {
								backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
							} else if (value == 40) {
								backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
							} else if (value == 50) {
								backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
							} else if (value == 60) {
								backValue = '<span class="Kill_red State_Color">已终止</span>';
							}
							return backValue;
						}
					},
					{
						text : '实例主键',
						dataIndex : 'iid',
						hidden : true
					},
					{
						text : 'actType',
						dataIndex : 'actType',
						hidden : true
					},
					{
				    	text: '活动名称',
				    	dataIndex: 'actName',
				    	hidden :showActNameSwitch,
				    	flex: 1
				    },
					{
						text : '服务名称',
						dataIndex : 'serviceName',
						flex : 1
					},
					{
						text : '任务名称',
						dataIndex : 'taskName',
						width : 150
					},
					{
						text : '启动人',
						dataIndex : 'startUser',
						width : 150
					},
					{
						text : '开始时间',
						dataIndex : 'startTime',
						width : 150
					},
					{
						text : '结束时间',
						dataIndex : 'endTime',
						width : 150
					},
					// { text: '服务器个数', dataIndex: 'serverNum',width:100},
					/*
					 * { text: '类别', dataIndex:
					 * 'cata',width:50,renderer:function(value,p,record){ var
					 * backValue = ""; if(value==0){ backValue = "测试"; }else
					 * if(value==1){ backValue = "生产"; } return backValue; }},
					 */
					{
						text : '操作',
						dataIndex : 'sysOperation',
						width : 160,
						renderer : function(value, p, record) {
							var coatid = record.get('iid');
							var cata = 1;// record.get('cata');
							var actType = record.get('actType');
							var actNo = record.get('actNo');
				            var flowId = record.get('flowId');
				            var state = record.get('state');
				            var childFlowId = record.get('childFlowId');
							if (actType == '0') {
								return '<span class="switch_span">'
										+ '<a href="javascript:void(0)" onclick="forwardruninfo2ForExecForFlow('
										+ coatid
										+ ', '
										+ cata
										+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;'
										+
										// '<a href="javascript:void(0)"
										// onclick="scriptCoatStop2ForExecForFlow('+coatid+',
										// '+ cata +')"><img
										// src="images/monitor_bg.png"
										// align="absmiddle"
										// class="monitor_termination"></img>终止</a>'+
										'<a href="javascript:void(0)" onclick="resultExport2ForExecForFlow('
										+ coatid
										+ ', '
										+ cata
										+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>'
										+ '</span>';
							} else if ('2' == actType) {
								return '<span class="switch_span">'
										+ '<a href="javascript:void(0)" onclick="forwardUTruninfo2ForExecForFlow(' + flowId + ', ' + coatid + ', '+ actNo + ', '+ state + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>'
										+ '</span>';
							} else if ('3' == actType) {
				                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="forwardCallflowruninfo2ForExecForFlow(' + childFlowId + ', ' + coatid + ', '+ actNo + ', '+ state + ', ' + cata + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>' + '</span>';
				            }

						}
					} ];

			scriptmonitor_store2ForExecForFlow.on('beforeload',
					function(store, options) {
						var new_params = {
							 flowId:flowId2ForExecForFlow,
							 scriptName:search_form2ForExecForFlow.getForm().findField("scriptName").getValue(),
							// startUser:search_form2ForExecForFlow.getForm().findField("startUser").getValue(),
							state : search_form2ForExecForFlow.getForm().findField("state").getValue(),
							cata : 1,// search_form2ForExecForFlow.getForm().findField("cata").getValue(),
							startTime :search_form2ForExecForFlow.getForm().findField("startTime").getValue(),
							endTime : search_form2ForExecForFlow.getForm().findField("endTime").getValue()/*,
							forScriptFlow: 1*/
						};

						Ext.apply(scriptmonitor_store2ForExecForFlow.proxy.extraParams,
								new_params);
					});

			var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
				store : scriptmonitor_store2ForExecForFlow,
				baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				border:false,
				dock : 'bottom',
				displayInfo : true
			});

			var scriptmonitor_grid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : scriptmonitor_store2ForExecForFlow,
				cls:'customize_panel_back sc_tab_height',
				padding:panel_margin,
				border : true,
				columnLines : true,
				columns : scriptmonitor_columns,
				/*cls:'sc_tab_height',*/
				bbar : pageBar
			/*
			 * , viewConfig:{
			 * getRowClass:function(record,rowIndex,rowParams,arriveStore){ var
			 * cls = ''; if(record.data.state==10){ cls = 'row_Blue'; }else
			 * if(record.data.state==20){ cls = 'row_Green'; }else
			 * if(record.data.state==30){ cls = 'row_Red'; }else
			 * if(record.data.state==40){ cls = 'row_Red'; }else
			 * if(record.data.state==50){ cls = 'row_Red'; }else
			 * if(record.data.state==60){ cls = 'row_Gray'; } else { cls =
			 * 'row_Gray'; } return cls; } }
			 */
			});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "scriptflowcoatmonitorForExec_area",
				border : true,
				layout : 'border',
				bodyPadding : grid_margin,
				bodyCls: 'service_platform_bodybg',
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight()-modelHeigth,
				items : [ search_form2ForExecForFlow, scriptmonitor_grid ]
			/*
			 * , buttonAlign:'center', buttons: [ { text: '返回', handler :
			 * function() { contentPanel.getLoader().load({url:
			 * "scriptMonitor.do?cata="+flag,scripts: true}); } }]
			 */
			});

			function clearQueryWhere() {
				 search_form2ForExecForFlow.getForm().findField("scriptName").setValue('');
				// search_form2ForExecForFlow.getForm().findField("startUser").setValue(''),
				search_form2ForExecForFlow.getForm().findField("state").setValue("-1");
				// search_form2ForExecForFlow.getForm().findField("cata").setValue("-1"),
				search_form2ForExecForFlow.getForm().findField("startTime").setValue('');
						search_form2ForExecForFlow.getForm().findField("endTime").setValue('');
			}

			contentPanel.on('resize', function() {
				mainPanel.setWidth(contentPanel.getWidth());
				mainPanel.setHeight(contentPanel.setHeight()-modelHeigth);
			});
		});

function utStartSuccessCallback() {
	scriptmonitor_store2ForExecForFlow.reload();
}

function forwardUTruninfo2ForExecForFlow(flowId, coatid, actNo, state, cata) {
	personExcute_window2ForExecForFlow = Ext.create('Ext.window.Window', {
		title : '人工提醒',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : 600,
		height : 353,
		loader : {
			url : 'scriptServiceUT2ForExecForFlow.do',
			params : {
				actNo : actNo,
				coatid:coatid,
				flowId:flowId,
				flag : cata,
				state:state 
			},
			autoLoad : true,
//			autoDestroy : true,
			scripts : true
		}
	}).show();
}

function forwardtestmainExec2ForExecForFlow() {
	if(null!=planId && planId!='' && planId!='undefined' && null!=scenceId && scenceId!='' && scenceId!='undefined' ){
		//跳转到应急预案监控面
		contentPanel.getLoader().load({ 
			url: "forwardPlanEmMonitor.do",
	        scripts: true,
	         params: {
				
	        }
		});
	}else{
	   contentPanel.getLoader().load({
	        url: "historyScriptMonitorForFlowProduct.do",
	        scripts: true,
	         params: {
				filter_serviceName:filter_serviceNameForExecForFlow,
			    filter_serviceState:filter_serviceStateForExecForFlow,
				filter_serviceStartTime:filter_serviceStartTimeForExecForFlow,
				filter_serviceEndTime:filter_serviceEndTimeForExecForFlow,
				filter_serviceTaskName :filter_serviceTaskName,
				filter_audiUser :filter_audiUser
	        }
	    });
	}
	
}
function forwardruninfo2ForExecForFlow(coatid, flag) {
	var scriptName=search_form2ForExecForFlow.getForm().findField("scriptName").getValue();
	var state =search_form2ForExecForFlow.getForm().findField("state").getValue();
	var startTime = search_form2ForExecForFlow.getForm().findField("startTime").getRawValue();
	var endTime =search_form2ForExecForFlow.getForm().findField("endTime").getRawValue();
	contentPanel.getLoader().load({
		url : "historyForwardscriptserverForFlowForExec.do",
		scripts : true,
		params : {
			flowId:flowId2ForExecForFlow, 
			forScriptFlow: 1,
			coatid : coatid,
			flag : flag,
			filter_scriptName:scriptName,
			filter_state:state,
			filter_startTime:startTime,
			filter_endTime:endTime,
			filter_serviceName:filter_serviceNameForExecForFlow,
			filter_serviceState:filter_serviceStateForExecForFlow,
			filter_serviceStartTime:filter_serviceStartTimeForExecForFlow,
			filter_serviceEndTime:filter_serviceEndTimeForExecForFlow,
			planId:planId,
			scenceId:scenceId,
			stepId:stepId
		}
	});
}
function resultExport2ForExecForFlow(coatid, flag) {
	window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag='
			+ flag;
}

function forwardCallflowruninfo2ForExecForFlow(childFlowId, coatid, actNo, state, cata) {
	contentPanel.getLoader().load({
		url: "historyForwardscriptflowcoatforexec.do",
		scripts: true,
		params : {
			flowId:childFlowId, 
			flag:cata, 
			forScriptFlow: 1,
			planId:planId,
			scenceId:scenceId,
			stepId:stepId
		}
    });
}

function scriptCoatStop2ForExecForFlow(coatid, flag) {
	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
		if (btn == 'yes') {
			Ext.Ajax.request({
				url : 'scriptCoatStop.do',
				method : 'POST',
				params : {
					coatid : coatid,
					flag : flag
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', message);
					} else {
						Ext.Msg.alert('提示', message);
					}
					scriptmonitor_store2ForExecForFlow.reload();
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		}
	})

}