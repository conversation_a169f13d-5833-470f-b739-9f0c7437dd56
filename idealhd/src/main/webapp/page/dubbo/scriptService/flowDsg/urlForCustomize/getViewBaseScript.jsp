<%@page contentType="text/html; charset=utf-8"%>
<% 
	long iid_viewBaseScript=Long.parseLong(request.getParameter("iid"));
%>
<html>
<head>
<script type="text/javascript">
	var viewBaseScriptiid="<%=iid_viewBaseScript%>";
	var viewBaseScriptscriptuuid="<%=request.getAttribute("scriptuuid")%>";
	var sFormForShare;
	var checkRadioForShare = 0;
	var fromTypeForShare = <%=request.getAttribute("fromType") %>;
	var ssprjflag = <%=request.getAttribute("ss.prj.flag")==null?0:request.getAttribute("ss.prj.flag")%>;
	if(fromTypeForShare==3) {
		var url = 'forwardScriptShare.do';
	}else if(fromTypeForShare==2) {
		var url = 'forwardScriptMyToolBox.do';
	}else if(fromTypeForShare==1) {
		var url = 'scriptServices.do';
	}else if(fromTypeForShare==0) {
		var url = 'forwardScriptServiceRelease.do';
	}
	
	var hideReturnBtn = <%=request.getParameter("hideReturnBtn") %>;
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomize/getViewBaseScript.js"></script>
</head>
<body>
	<div id="viewBaseScript_area" style="width: 100%; height: 25%;"></div>
</body>
</html>
<%-- <%@page contentType="text/html; charset=utf-8"%>
<html>
<title>CodeMirror: Shell mode</title>
<meta charset="utf-8"/>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<h2>Shell mode</h2>
<textarea id=code>
#!/bin/bash

# clone the repository
git clone http://github.com/garden/tree

# generate HTTPS credentials
cd tree
openssl genrsa -aes256 -out https.key 1024
openssl req -new -nodes -key https.key -out https.csr
openssl x509 -req -days 365 -in https.csr -signkey https.key -out https.crt
cp https.key{,.orig}
openssl rsa -in https.key.orig -out https.key

# start the server in HTTPS mode
cd web
sudo node ../server.js 443 'yes' &gt;&gt; ../node.log &amp;

# here is how to stop the server
for pid in `ps aux | grep 'node ../server.js' | awk '{print $2}'` ; do
  sudo kill -9 $pid 2&gt; /dev/null
done

exit 0</textarea>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
    mode: 'shell',
    lineNumbers: true,
    matchBrackets: true
  });
</script>
</html> --%>