Ext.onReady(function() {
	var sysID;
	var busID;
	var DetailWinTi;
	var resGroupFlag;	
    // 清理主面板的各种监听时间
//    destroyRubbish();
    var startData={};
    var globalParams={};
    var attachmentIds = [];
    var chosedAgentWin;
    var chosedGroupIds =[];
    var chosedGroupNames =[];
	var upldWin;
	var upLoadformPane = '';
	var cpdsMap = {};//<cpid,dsid>
    var selCpId = -1;
    var enValue=100;
    var customName1='';
    var chosedGroupWin;
    var execUser1='';
    var	uuid ='';
	Ext.Ajax.request({
        url: 'queryUuidById.do',
        method: 'POST',
        async: false,
        params: {
        	serviceId: iidForTaskAudi
        },
        success: function(response, options) {
            uuid = Ext.decode(response.responseText).serviceUuid;
        },
        failure: function(result, request) {
        }
    });
    Ext.tip.QuickTipManager.init();
    
   
    if(customIid>0){
    	//先查出数据再组织数据
    	  					Ext.Ajax.request({
    								url : 'getCustomTemplateData.do',
    								async: false,
    								method : 'POST',
    								params : {
    									customId : customIid
    								},
    								success : function(response, request) {
    									var success = Ext.decode(response.responseText).success;
    									var message =  Ext.decode(response.responseText).message;
    									if (success) {
    										execUser1 = Ext.decode(response.responseText).execUser;
    										var agents = Ext.decode(response.responseText).agentIds;
    										var agents1 = [];
    										agents1.push(agents);
    										var arrayId = agents.split(',');
    										for(var j=0;j<arrayId.length;j++){
    											chosedAgentIds.push(arrayId[j]);
    										}
    										chosedGroupIds = Ext.decode(response.responseText).chosedGroupIds; 
		            						chosedGroupNames =  Ext.decode(response.responseText).chosedGroupNames; 
		            						resGroupFlag = Ext.decode(response.responseText).resGroupFlag; 
    										enValue = Ext.decode(response.responseText).eachNum;
    										customName1= Ext.decode(response.responseText).name;
    										globalParams = Ext.decode(response.responseText).globalParams;
    										
    									}else{
    										Ext.Msg.alert('提示', message);
    									}
    								},
    								failure : function(result, request) {
    									Ext.Msg.alert('提示', '执行失败！');
    								}
    							}); 
    	  				
    	  				
    }
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        queryMode: 'local',
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    bussData.on('load', function(store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load', function(store, options) {
        bussTypeCb.setValue(busID);
    });
    

    Ext.define('editScriptModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'excepResult',
            type: 'string'
        },
        {
            name: 'errExcepResult',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        }]
    });
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
            iid: iidForTaskAudi,
            fromType: 1 // 查ieai_script_services表
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
     
    });
   

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        fieldLabel: '功能概述',
        displayField: 'funcdesc',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1,
        height: 130,
        autoScroll: true
    });
    var scriptForm = Ext.create('Ext.form.Panel', {
        width: '20%',
        height: 230,
        border: true,
        layout: 'anchor',
        title: '基本信息',
        items: [{
            border: false,
            layout: 'column',
            margin: '5',
            items: [bussCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [bussTypeCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [sName]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [scName]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [errExcepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [funcDesc]
        }]
    });

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },{
            name: 'paramValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: uuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });
    paramStore.on('load',
    	    function(me, records, successful, eOpts) {
    	        $.each(records,
    	        function(index, record) {
    	            if (globalParams.hasOwnProperty(record.get('iid'))) {
    	                record.set('paramValue', globalParams[record.get('iid')]);
    	            }
    	        });
    	    });
    	    
	var defultEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true
		})
	});
	var passwordEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true,
			inputType : 'password'
		})
	});
    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 60,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
     {
		xtype : 'gridcolumn',
		dataIndex : 'paramDefaultValue',
		width: 80,
		text : '参数默认值',
		getEditor : function(record) {
			if (record.get('paramType') != 'IN-string(加密)' && record.get('paramType') != 'IN-string') {
				return defultEditor;
			} else {
				return passwordEditor;
			}
		},
		renderer : function(value, metaData, record, rowIdx, colIdx, store){  
        	var backValue = "";
        	if(record.get('paramType')== 'IN-string(加密)' || record.get('paramType')== 'IN-string'){
        		backValue = StringToPassword(value);
        	}else{
        		backValue = value;
        	}
        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
        	
        	return backValue;
        }
	},
	  {
		xtype : 'gridcolumn',
		dataIndex : 'paramValue',
		width: 100,
		text : '参数值',
		maxLength : 1000,
		allowBlank: true,
		getEditor : function(record) {
			if (record.get('paramType') != 'IN-string(加密)' && record.get('paramType') != 'IN-string') {
				return defultEditor;
			} else {
				return passwordEditor;
			}
		},
		renderer : function(value, metaData, record, rowIdx, colIdx, store){  
        	var backValue = "";
        	if(record.get('paramType')== 'IN-string(加密)' || record.get('paramType')== 'IN-string'){
        		backValue = StringToPassword(value);
        	}else{
        		backValue = value;
        	}
        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(backValue) + '"';
        	
        	return backValue;
        }
	},
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    
    var paramGrid = Ext.create('Ext.grid.Panel', {
    	title: "参数",
        width: '100%',
//        height: 280,
//        margin: 10,
//        collapsible : true,
        store: paramStore,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: uuid,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    
    var attachmentColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }/*,
    {
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    }*/];
    
    
    
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	title: "附件",
        width: '50%',
//        height: contentPanel.getHeight()-362,
//        margin: 10,
        store: attachmentStore,
        border: true,
        columnLines: true,
        columns: attachmentColumns
    });
    
    var paramsAndAttachmentsPanel = Ext.create('Ext.panel.Panel', {
         collapsible : false,
        border: false,
        height: 190,
        cls:'window_border panel_space_left panel_space_right',
        layout: {
            type: 'hbox',
            align:'stretch'
        },
        items: [paramGrid]
    });

    
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    labelAlign : 'right',
			    labelWidth : 65,
			    width : '23.5%',
	            columnWidth:1,
			    multiSelect: true,
			    hidden:removeAgentSwitch,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
		    	listeners: {
	    	      change: function( comb, newValue, oldValue, eOpts ) {
	    	    	  agent_store.load();
	    	      }
		    	}
	});
	
	var app_name = new Ext.form.TextField({
		name : 'appname',
		fieldLabel : '应用名称',
		displayField : 'appname',
		emptyText : '--请输入应用名称--',
		labelWidth : 65,
		hidden : true,
		labelAlign : 'right',
		width : '20%'
	});
	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'AgentIp',
		displayField : 'agentip',
		emptyText : '--请输入agentip--',
		labelWidth : 65,
		hidden :true,
		labelAlign : 'right',
		width : '23%'
	});
	var ipStart = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 79,
	     fieldLabel : '起始IP',
	    emptyText : '--请输入开始IP--',
	    //labelSeparator : '',
	    width : '23%',
	    labelAlign : 'right'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 65,
	    fieldLabel : '终止IP',
	    emptyText : '--请输入截止IP--',
	    //labelSeparator : '',
	    labelAlign : 'right',
	    width : '23%'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '计算机名',
		displayField : 'hostname',
		emptyText : '--请输入计算机名--',
		labelWidth : 65,
		labelAlign : 'right',
		width : '23%'
	});
	var sys_name = new Ext.form.TextField({
		name : 'sysname',
		fieldLabel : '名称',
		displayField : 'sysname',
		emptyText : '--请输入名称--',
		hidden:cmdbFlag,
		labelWidth : 65,
		labelAlign : 'right',
		width : '23%'
	});
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '操作系统',
		displayField : 'ostype',
		emptyText : '--操作系统--',
		labelWidth : 65,
		labelAlign : 'right',
		width : '23%'
	});
	  var sysName1 = new Ext.form.TextField({
	        name: 'sysName1',
	        fieldLabel: '名称',
	        displayField: 'sysName1',
	        emptyText: '--请输入名称--',
	        labelWidth: 65,
	        labelAlign: 'right',
	        width :'24%',
	        listeners:{
		        specialkey: function(field, e){
			                if (e.getKey() == e.ENTER) {
			                	agent_grid.ipage.moveFirst();
			                }
			    }
		    }
	    });
	var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 79,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '23.7%',
		labelAlign : 'right'
	});
	var search_ip_form = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		border : false,
		iqueryFun : function(){
			agent_grid.ipage.moveFirst();
	    },
		dockedItems : [ {
			xtype : 'toolbar',
//			baseCls:'customize_gray_back',    
			dock : 'top',
			border: false,
			items : [  sys_name, app_name,  host_name, os_type,ipStart,ipEnd
			]
		},
		{
			xtype : 'toolbar',
//			baseCls:'customize_gray_back',    
			dock : 'top',
			border: false,
			items : [ /*agent_ip,*/ sysName1, resourceGroupObj, agentStatusCb,
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function(){
						agent_grid.ipage.moveFirst();
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function(){
						agent_ip.setValue('');
						ipEnd.setValue('');
						ipStart.setValue('');
				    	app_name.setValue('');
						sys_name.setValue('');
						sysName1.setValue('');
						host_name.setValue('');
						os_type.setValue('');
				    	resourceGroupObj.setValue('');
				    	agentStatusCb.setValue('');
					}
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '导入',
					handler : importExcel
				}
			]
		}]
	});
	 function checkFile(fileName){
		    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
		    if(!file_reg.test(fileName)){  
		    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
		    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
		        return false;
		    }
		    return true;
		}
	function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:100,
		    frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 65,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: [
					{
						id:'upldBtnIdAudi',
						text: '导入Agent文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
			    			if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}

							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'importAgentForStart.do',
									params:{
										envType:1
				                	},
								    success: function(form, action) {
								       var msg = Ext.decode(action.response.responseText).message;
								       
								    	   var status = Ext.decode(action.response.responseText).status;
								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
								    	   
								    	   if(status==1) {
								    		   if(matchAgentIds && matchAgentIds.length>0) {
								    			   Ext.MessageBox.buttonText.yes = "确定"; 
								    				Ext.MessageBox.buttonText.no = "取消"; 
								    			   Ext.Msg.confirm("请确认", msg, function(id){
										  				 if(id=='yes'){
									  						Ext.Msg.alert('提示', "导入成功！");
									  						agent_ip.setValue('');
													    	app_name.setValue('');
															sys_name.setValue('');
															sysName1.setValue('');
															host_name.setValue('');
															os_type.setValue('');
													    	resourceGroupObj.setValue('');
													    	agentStatusCb.setValue('');
													    	chosedAgentIds = matchAgentIds;
												    	   agent_grid.ipage.moveFirst();
										  				 }
										    		   });
								    		   } else {
								    			   Ext.Msg.alert('提示-没有匹配项', msg);
								    		   }
								    		   
								    	   } else {
								    		   Ext.Msg.alert('提示', "导入成功！");
									    	   agent_ip.setValue('');
										    	app_name.setValue('');
												sys_name.setValue('');
												sysName1.setValue('');
												host_name.setValue('');
												os_type.setValue('');
										    	resourceGroupObj.setValue('');
										    	agentStatusCb.setValue('');
										    	chosedAgentIds =  matchAgentIds;
									    	   agent_grid.ipage.moveFirst();
								    	   }
								    	   
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}, {
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '设备信息批量导入',
		    width: 400,
		    height: 140,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}

	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'execUserNmae', type: 'string'},
            {name: 'execuser', type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
    
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
//	var agent_store_chosed = Ext.create('Ext.data.Store', {
//		autoLoad: true,
//		pageSize: 30,
//		model: 'agentModel',
//		proxy: {
//            type: 'ajax',
//            url: 'getAgentChosedList.do',
//            actionMethods: {  
//                create : 'POST',  
//                read   : 'POST', // by default GET  
//                update : 'POST',  
//                destroy: 'POST'  
//            },
//            reader: {
//                type: 'json',
//                root: 'dataList',
//                totalProperty: 'total'
//            }
//        }
//	});
	 
	if(!taskApplyForSPDBSwitch){//不开展示原有样式
		agent_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: true,
			pageSize: 30,
			model: 'agentModel',
			proxy: {
	            type: 'ajax',
	            url: 'getAgentChosedList.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
		agent_store_chosed.on('beforeload', function(store, options) {
			var new_params = {
				workItemId : iidForTaskAudi,
				customName : customTaskName,
				isCommonTasks : true
			};
			Ext.apply(store.proxy.extraParams, new_params);
		});
	}else if(!chosedAgentWin && taskApplyForSPDBSwitch){//开了后展示白名单执行样式
		agent_store_chosed = Ext.create('Ext.data.Store', {
			autoLoad: true,
			pageSize: 30,
			model: 'page.dubbo.scriptService.spdb.agent.agentModel',
			proxy: {
	            type: 'ajax',
	            url: 'getAllAgentListForSPDB.do',
	            actionMethods: {  
	                create : 'POST',  
	                read   : 'POST', // by default GET  
	                update : 'POST',  
	                destroy: 'POST'  
	            },
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
		});
	}
		
	 var agent_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
         { text: '主键',  dataIndex: 'iid',hidden:true},
         { text: '名称',  dataIndex: 'sysName',width:160},
         { text: 'IP',  dataIndex: 'agentIp',width:110},
         { text: '计算机名',  dataIndex: 'hostName',width:150},
         { text: '操作系统',  dataIndex: 'osType',width:110},
         { text: '端口号',  dataIndex: 'agentPort',width:60},
         { text: '描述',  dataIndex: 'agentDesc',flex:1,
         	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                 metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                 return value;  
             }
         },
         { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
         	var backValue = "";
         	if(value==0){
         		backValue = "Agent正常";
         	}else if(value==1){
         		backValue = "Agent异常";
         	}
         	return backValue;
         }}
        ];
	 
    var agent_columns_chosed = [{ text: '序号', xtype:'rownumberer', width: 40 },
        { text: '主键',  dataIndex: 'iid',hidden:true},
        { text: '名称',  dataIndex: 'sysName',width:160},
        { text: 'IP',  dataIndex: 'agentIp',width:110},
        { text: '计算机名',  dataIndex: 'hostName',width:150},
        { text: '操作系统',  dataIndex: 'osType',width:110},
        { text: '端口号',  dataIndex: 'agentPort',width:60},
        { text: '描述',  dataIndex: 'agentDesc',flex:1,
        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                return value;  
            }
        },
        {
    		text : '执行用户',
    		dataIndex : 'execUserNmae',
    		width : 90,
    		align: 'left',//整体左对齐
    		renderer : function (value, metaData, record, rowNum)
    	    {
    			var displayValue = value;
    			var recordedData = $("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+record.get("iid"));
    			if(undefined == recordedData){
    				if("" == value || undefined == value){
    					displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
    				} else {
    					displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + displayValue + "</a>";
    				}
    			} else {
    				if("" == recordedData){
    					displayValue = "<button  style=\"text-align:center; margin-left:5px;\" class=\"dbsourBtn\" type=\"button\">配置</button>";
    				} else {
    					displayValue = "<a style=\"text-align:center; margin-left:5px;\">" + recordedData + "</a>";
    				}
    			}
    	        return displayValue;
    	    },
    	    listeners : {
    			click : function(a, b, c, d, e, record) {
    				openExecUserConfigData(record);
    			}
    		}
    	},
        { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
        	var backValue = "";
        	if(value==0){
        		backValue = "Agent正常";
        	}else if(value==1){
        		backValue = "Agent异常";
        	}
        	return backValue;
        }}
       ];
    
    agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp : agent_ip.getValue(),
	    	startIp :ipStart.getValue().trim(),
	    	endIp :ipEnd.getValue().trim(),
	    	appName : app_name.getValue(),
	    	agentState: agentStatusCb.getValue(),
	    	sysName: CMDBflag?(sys_name.getValue() == null ? '': Ext.util.Format.trim(sys_name.getValue() + "")):Ext.util.Format.trim(sysName1.getValue()),
			hostName : host_name.getValue(),
			osType : os_type.getValue(),
	    	rgIds:resourceGroupObj.getValue(),
	    	flag: 1
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    agent_store_chosed.on('beforeload', function (store, options) {
    	if(taskApplyForSPDBSwitch){
    		var new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds),
    			flag :1,
    			from : 'inAgentChosedList'
    		};
    	}else{
    		var new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds),
    			flag :1
    		};
    	}
    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    agent_store.on('load', function (store, options) {
    	var records=[];//存放选中记录
	  for(var i=0;i<agent_store.getCount();i++){
	      var record = agent_store.getAt(i);
	      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
    	    {   
	    	  
	    	  if((+chosedAgentIds[ii])==record.data.iid)
	    		  {
	    		  records.push(record);
	    		  }
    	    }   
	  }
	  agent_grid.getSelectionModel().select(records, false, true);//选中记录
    });
//    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//		store : agent_store,
//		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//		dock : 'bottom',
//		displayInfo : true
//	});
    var agent_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
	    store:agent_store,
	    border:false,
//	    bbar : pageBar,
	    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    columnLines : true,
//	    rowLines: false,
	    columns:agent_columns,
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    listeners: {
	        select: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.push(record.get('iid'));
            	}
            },
	        deselect: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
            		chosedAgentIds.remove(record.get('iid'));
            	}
            }
	    }
	});
//    var pageBarForAgentChosedGrid = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
//    	store : agent_store_chosed,
//    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//    	dock : 'bottom',
//    	displayInfo : true
//    });
    var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			select:function(selModel, record, index, eOpts) {
				 //当前选中cpid
				 selCpId = record.get("iid");
			 },
			 deselect:function(selModel, record, index, eOpts) {
				 var cpid = record.get("iid");
				 cpdsMap[cpid] = -1;//清空
				 selCpId = -1;
			 }
		}
	});
	
	
		//*********************************************增加资源组相关控件 start***************************************************************************
	
	 Ext.define('groupModel', {
        extend: 'Ext.data.Model',
        idProperty: 'id',
        fields: [
            {name: 'id',     type: 'long'},
            {name: 'name',     type: 'string'},
            {name: 'execUserName',     type: 'string'},
            {name: 'description',     type: 'string'}
        ]
    }); 
	 var group_columns = [
	    { text: '序号', xtype: 'rownumberer', width: 40 },
	    { text: '主键', dataIndex: 'id', hidden: true },
	    { text: '组名称', dataIndex: 'name', width: 160 },
	    { text: '启动用户', dataIndex: 'execUserName',   width: 160 },
	    { text: '描述', dataIndex: 'description', width: 280 },
	    {
		text : '操作',
		xtype : 'actiontextcolumn',
		flex:1,
		align : 'left',
		items : [{
					text : '详情',
					iconCls : 'execute',
					handler : function(grid, rowIndex) {
						var iid = grid.getStore().data.items[rowIndex].data.id;
						getAgentInfoByGroupId(iid);
					}}]
	    }];
	    /**
	     * 获取资源组下的agent
	     */
	    function getAgentInfoByGroupId(iid){
	    	Ext.define('agentModelByGroup', {
		        extend: 'Ext.data.Model',
		        idProperty: 'id',
		        fields: [
		            {name: 'id',     type: 'long'},
		            {name: 'ip',     type: 'string'},
		            {name: 'port',     type: 'string'},
		            {name: 'hostName',  type: 'string'}
		        ]
		    });
		    
	    	var agentinfo_group_store = Ext.create('Ext.data.Store', {
		        autoLoad: false,
		        pageSize: 50,
		        model: 'agentModelByGroup',
		        proxy: {
		            type: 'ajax',
		            url: 'agentGroup/getServersForTaskApply.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
		    
		    agentinfo_group_store.on('beforeload', function (store, options) {
			    var new_params = {  
			     	groupId:iid
			 };
			    
			   Ext.apply(agentinfo_group_store.proxy.extraParams, new_params);
		    });
			 var  agentinfo_columns_group = [
			     { text: '序号', xtype: 'rownumberer', width: 40 },
			     { text: '主键', dataIndex: 'id', hidden: true },
			     { text: 'Agent名称', dataIndex: 'hostName', flex:1},
			     { text: 'IP', dataIndex: 'ip', width: 160 },
			     { text: '端口', dataIndex: 'port', width: 160 }];
			     
			
	    	var agentinfo_group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		    	region: 'center',
			    store:agentinfo_group_store,
			    border:false,
			    columnLines : true,
			    cls:'customize_panel_back',
			    columns:agentinfo_columns_group,
			    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar'
//			    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		    });
		    agentinfo_group_store.load();
            var  agentinfoGroupWin = Ext.create('Ext.window.Window', {
                title: '增加资源组',
                autoScroll: true,
                modal: true,
                resizable: false,
                closeAction: 'hide',
                layout: 'border',
                width: contentPanel.getWidth() - 190,
                height: contentPanel.getHeight(),
                items: [agentinfo_group_grid]
              });
              agentinfoGroupWin.show();
	    }
	    
	    
	  
    var group_store = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        pageSize: 50,
	        model: 'groupModel',
	        proxy: {
	            type: 'ajax',
	            url: 'agentGroup/groups.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	    
	  var group_columns_chosed = [
	     { text: '序号', xtype: 'rownumberer', width: 40 },
	    { text: '主键', dataIndex: 'id', hidden: true },
	    { text: '组名称', dataIndex: 'name', width: 160 },
	    { text: '启动用户', dataIndex: 'execUserName',  width: 160 },
	    { text: '描述', dataIndex: 'description', flex:1}];
	 var group_store_chosed = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 30,
		model: 'groupModel',
		proxy: {
            type: 'ajax',
            url: 'agentGroup/groups.do',
            actionMethods: {  
                create : 'POST',  
                read   : 'POST', // by default GET  
                update : 'POST',  
                destroy: 'POST'  
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
	});
	 group_store_chosed.on('beforeload', function(store, options) {
	   var   new_params = {
	        groupIids: chosedGroupIds,
	        from :"taskApplayChosed"
	      }
	    Ext.apply(group_store_chosed.proxy.extraParams, new_params);
	  });
	
		group_store.on('beforeload', function (store, options) {
		    var new_params = {  
		    	agentGroupName : iresName.getValue()
//		    	iexecUser:iexecUser.getValue()
		    };
		    Ext.apply(group_store.proxy.extraParams, new_params);
		});
	  group_store.on('load', function(store, options) {
	    var records = [];//存放选中记录
	    for (var i = 0; i < group_store.getCount(); i++) {
	      var record = group_store.getAt(i);
	      for (var ii = 0; ii < chosedGroupIds.length; ii++) {
	        if (+chosedGroupIds[ii] == record.data.id) {
	          records.push(record);
	        }
	      }
	    }
	    group_grid.getSelectionModel().select(records, false, true); //选中记录
	  });
	
	 
	   var group_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
	    	region: 'center',
		    store:group_store,
		    border:false,
		    columnLines : true,
		    cls:'customize_panel_back',
		    columns:group_columns,
		    ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
			listeners: {
			        select: function( e, record, index, eOpts ){ 
		            	if(chosedGroupIds.indexOf(record.get('id'))==-1) {
		            		chosedGroupIds.push(record.get('id'));
		            		chosedGroupNames.push(record.get('name'));
		            	}
		            },
			        deselect: function( e, record, index, eOpts ){ 
		            	if(chosedGroupIds.indexOf(record.get('id'))>-1) {
		            		chosedGroupIds.remove(record.get('id'));
		            		chosedGroupNames.remove(record.get('name'));
		            	}
		            }
			    }
	    });
	  
	var iresName = new Ext.form.TextField({
			name : 'iresName',
			fieldLabel : '组名称',
			displayField : 'iresName',
			emptyText : '--请输入组名称--',
			labelWidth : 70,
			labelAlign : 'right',
			width : '25%'
		});
		var iexecUser = new Ext.form.TextField({
			name : 'iexecUser',
			fieldLabel : '启动用户',
			displayField : 'iexecUser',
			emptyText : '--启动用户--',
			labelWidth : 70,
			hidden:true,
			labelAlign : 'right',
			width : '25%'
		});
	    
	var search_group_form = Ext.create('Ext.ux.ideal.form.Panel', {
			region : 'north',
			border : false,
			iqueryFun : function(){
				group_grid.ipage.moveFirst();
		    },
			bodyCls : 'x-docked-noborder-top',
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				border: false,
				items : [ iresName, iexecUser,
				  {
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function(){
							group_grid.ipage.moveFirst();
						}
					},
					{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '清空',
						handler : function(){
							iresName.setValue('');
							iexecUser.setValue('');
						}
					}]
			}]
		});
	    
	   
	var group_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
	  title: '已选资源组',
	  region: 'west',
	  cls: 'window_border panel_space_top panel_space_left panel_space_right',
	  store: group_store_chosed,
	  border: true,
	  width: '60%',
	  columnLines: true,
	  height: contentPanel.getHeight() * 0.68,
	  ipageBaseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	  emptyText: '没有选择资源组',
	  columns: group_columns_chosed,
	  selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
	   listeners : {
			  	activate:function(tab){
		  			  resGroupFlag='true';
		  			  group_store_chosed.load();
		  			  for(var y = 0, yLen = chosedAgentIds.length; y < yLen; y++){
						 $("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+chosedAgentIds[y],"");	  	
  					  }
			  		  chosedAgentIds.splice(0,chosedAgentIds.length);
				}
		},
	  dockedItems: [
	    {
	      xtype: 'toolbar',
	      dock: 'top',
	      items: [
	        {
	          xtype: 'button',
	          cls: 'Common_Btn',
	          text: '删除',
	          handler: function() {
	            var records = group_grid_chosed.getSelectionModel().getSelection();
	            if (records.length > 0) {
	              for (var i = 0, len = records.length; i < len; i++) {
	                chosedGroupIds.remove(records[i].get('id'));
	                chosedGroupNames.remove(records[i].get('name'));
	              }
	              group_grid_chosed.ipage.moveFirst();
	              group_grid.ipage.moveFirst();
	            } else {
	              Ext.Msg.alert('提示', '请选择资源组！')
	              return
	            }
	          }
	        },
	        {
	          xtype: 'button',
	          cls: 'Common_Btn',
	          text: '增加资源组',
	          handler: function() {
	            if (!chosedGroupWin) {
	              chosedGroupWin = Ext.create('Ext.window.Window', {
	                title: '增加资源组',
	                autoScroll: true,
	                modal: true,
	                resizable: false,
	                closeAction: 'hide',
	                layout: 'border',
	                width: contentPanel.getWidth() - 190,
	                height: contentPanel.getHeight(),
	                items: [search_group_form, group_grid],
	                dockedItems: [
	                  {
	                    xtype: 'toolbar',
	                    dock: 'bottom',
	                    layout: { pack: 'center' },
	                    items: [
	                      {
	                        xtype: 'button',
	                        text: '确定',
	                        cls: 'Common_Btn',
	                        margin: '6',
	                        handler: function() {
	                         	group_store_chosed.load();
	                            this.up('window').close();
	                        }
	                      },
	                      {
	                        xtype: 'button',
	                        text: '关闭',
	                        cls: 'Common_Btn',
	                        margin: '6',
	                        handler: function() {
	                          this.up('window').close();
	                        }
	                      }
	                    ]
	                  }
	                ]
	              });
	            }
	            chosedGroupWin.show();
	            group_store.load();
	          }
	        }
	      ]
	    }
	  ]
	});

	
	
	//*********************************************增加资源组相关控件 end***************************************************************************
	
    var agent_grid_chosed = Ext.create('Ext.ux.ideal.grid.Panel', {
    	title: '已选服务器',
    	region : 'west',
    	store:agent_store_chosed,
    	border:true,
    	width:'100%',
//    	cls:'window_border panel_space_left panel_space_right',
    	 cls: 'window_border panel_space_top panel_space_left panel_space_right',
    	columnLines : true,
    	height: 350,
    	emptyText: '没有选择服务器',
    	columns: taskApplyForSPDBSwitch == true ? agentColumnsForSPDB:agent_columns_chosed,
    	selModel:selModelForagent_grid_chosed,
    	listeners : {
			  	activate:function(tab){
			  			 resGroupFlag='false';
				  		 agent_store_chosed.load();
				  		 chosedGroupIds.splice(0,chosedGroupIds.length);
				  		 chosedGroupNames.splice(0,chosedGroupNames.length);
				}
		},
//    	bbar : pageBarForAgentChosedGrid,
    	ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
    	dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [{
				xtype : 'button',
				cls :'Common_Btn',
				text : '删除',
				handler : function() {
					var records = agent_grid_chosed.getSelectionModel().getSelection();
					if(records.length>0) {
	  					for(var i = 0, len = records.length; i < len; i++){
	  						chosedAgentIds.remove(records[i].get('iid'));
	  						$("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+records[i].get('iid'),"");	 
	  					}
	  					if(chosedAgentIds.length ==0 && chosedAgentFlagArray.indexOf('WIN') != -1){
	     				 	chosedAgentFlagArray.remove('WIN');
	     				 }else if(chosedAgentIds.length ==0 &&  chosedAgentFlagArray.indexOf('非WIN') != -1){
	     				 	chosedAgentFlagArray.remove('非WIN');
	     				 }
	  					agent_grid_chosed.ipage.moveFirst();
	  					agent_grid.ipage.moveFirst();
	  				} else {
	  					Ext.Msg.alert('提示', "请选择服务器！");
                        return;
	  				}
				}
			},
			{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加服务器',
				handler : function(){
					if(!chosedAgentWin && !taskApplyForSPDBSwitch) {
			 
						chosedAgentWin = Ext.create('Ext.window.Window', {
					  		title : '增加服务器',
					  		autoScroll : true,
					  		modal : true,
					  		resizable : false,
					  		closeAction : 'hide',
					  		layout: 'border',
					  		width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
					  		items:[search_ip_form, agent_grid],
					  		dockedItems: [{
					            xtype: 'toolbar',
//					            baseCls:'customize_gray_back',    
					            dock:'bottom',
					            layout: {pack: 'center'},
						        items: [{ 
						  			xtype: "button",
						  			text: "确定", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				var agent_grid_chosedRecord = agent_grid_chosed.getStore().getRange();
					  					if(agent_grid_chosedRecord.length>0) {
					 	  					for(var x = 0, xLen = agent_grid_chosedRecord.length; x < xLen; x++){
					 	  						var flag = false;
					 	  						var chosedRecordIid = agent_grid_chosedRecord[x].get('iid');
					 	  						for(var y = 0, yLen = chosedAgentIds.length; y < yLen; y++){
					 	  							var chosedNewIid = chosedAgentIds[y];
					 	  							if(chosedRecordIid == chosedNewIid){
					 	  								flag = true;
					 	  								break;
					 	  							}
					 	  						}
					 	  						if(!flag){
					 	  							$("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+chosedRecordIid,"");	
					 	  						}
					 	  					}
		  								}
					  					agent_store_chosed.load();
						  				this.up("window").close();
						  			}
						  		 },{ 
						  			xtype: "button",
						  			text: "关闭", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				this.up("window").close();
						  			}
						  		 }]
					  		}]
					  	});
				}else if(!chosedAgentWin && taskApplyForSPDBSwitch){
					chosedAgentWin = chosedAgentWinForSPDB;
				}
					agentListStore.load();
					chosedAgentWin.show();
					agent_store.load();
				} 
			} ]
		}]
    });

    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
    	title: "选择服务器",
        border: true,
        layout : 'border',
        height: 350,
        items: [search_ip_form, agent_grid]
    });
    
	
	var execTime_sm = new Ext.form.TextField (
	{
	    fieldLabel : '执行时间',
	    labelWidth : 65,
	    labelAlign : 'right',
	    width : '23%'
	});
	
	var execUser = new Ext.form.TextField({
        name: 'execUser',
        fieldLabel: '启动用户',
        emptyText: '',
        labelWidth : 65,
		labelAlign : 'right',
		value:execUser1,
        width: "23%"
    });
	var customName = new Ext.form.TextField({
		name: 'taskName',
		fieldLabel: '常用任务名称',
		emptyText: '',
		labelWidth : 93,
		labelAlign : 'right',
		value:customName1,
		width: "23%"
	});
	
	var eachNum = new Ext.form.NumberField({
		name: 'eachNum',
		fieldLabel: '并发数量',
		labelWidth : 65,
		labelAlign : 'right',
		minValue:0,
		value:enValue,
		width: "23%"
	});
	
	function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
	
		var topBar = [{
	        xtype: 'toolbar',
	        dock: 'top',
	        border:false,
	        padding:'0 5 0 5',
	        cls:'window_border  panel_space_left panel_space_right',
	        items: [customName, eachNum,execUser,
	        	{
	        	xtype : 'button',
	        	text: '查看脚本',
	            cls: 'Common_Btn',
	            handler: function(){
	            	viewDetail(iidForTaskAudi)
	            }
	        }]
	    }];
	
	    
	 /** 已选窗口 资源组、单独agent tabPanel* */
	var tabPanelForChosedDevice = Ext.create ('Ext.tab.Panel',
	{
	    tabPosition : 'top',
//	    region : 'center',
	    region : 'west',
	    activeTab : resGroupFlag=="true"?1:0,
//	    cls:'customize_panel_back',
	    cls:'window_border panel_space_top panel_space_left panel_space_right',
	    width : '100%',
//	    width:'60%',
	    height: contentPanel.getHeight()*0.58,
//	    height : contentPanel.getHeight (),
	    border : false,
	    defaults :
	    {
		    autoScroll : false
	    },
	    items : [agent_grid_chosed,group_grid_chosed]
	   
	});
	
	 var centerPanel = Ext.create('Ext.panel.Panel', {
	        collapsible : false,
	        border: false,
	        region: 'center',
	        height: contentPanel.getHeight()*0.58,
	        layout: 'border',
	        items: [tabPanelForChosedDevice/*attachmentGrid*/]
	    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "taskCustomPage_area",
        layout: {
            type: 'vbox',
            align : 'stretch'
        },
        border: false,
        autoScroll: true,
        height: contentPanel.getHeight()-38,
        items: [/*agent_grid_chosed*/centerPanel, paramsAndAttachmentsPanel/* execDescForm, scriptForm, mainP*/],
        buttons: [
//        	{
//                text: '保存',
//                margin:'6',
//                cls: 'Common_Btn',
//                handler: function(){
//                	paramStore.sort('paramOrder', 'ASC');
//                	var m = paramStore.getRange(0, paramStore.getCount()-1);
//                    var jsonData = "[";
//                    for (var i = 0, len = m.length; i < len; i++) {
//                        //var n = 0;
//                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
//                        var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
//                        var paramValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';
//                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
//                        if(scriptTypeForTaskAudi=='bat') {
//                        	if (paramDefaultValue.indexOf('"')>=0) {
//	  		            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
//	  		                    return;
//		  		            }
//                        }
//                        if ("" == paramType) {
//                            setMessage('参数类型不能为空！');
//                            return;
//                        }
//                        if (fucCheckLength(paramDesc) > 250) {
//                            setMessage('参数描述不能超过250字符！');
//                            return;
//                        }
//
//                        if (paramType == 'IN-int' || paramType == 'int') {
//                            if (!checkIsInteger(paramDefaultValue)) {
//                                setMessage('参数类型为int，但参数默认值不是int类型！');
//                                return;
//                            }
//                            if (!checkIsInteger(paramValue) && !Ext.isEmpty(paramValue)) {
//                                setMessage('参数类型为int，但参数值不是int类型！');
//                                return;
//                            }
//                        }
//                        if (paramType == 'IN-float' || paramType == 'float') {
//                            if (!checkIsDouble(paramDefaultValue)) {
//                                setMessage('参数类型为float，但参数默认值不是float类型！');
//                                return;
//                            }
//                            if (!checkIsDouble(paramValue) && !Ext.isEmpty(paramValue)) {
//                                setMessage('参数类型为float，但参数值不是float类型！');
//                                return;
//                            }
//                        }
//                        if(!Ext.isEmpty(Ext.util.Format.trim(paramValue))){
//                        	globalParams[m[i].get('iid')] = paramValue;
//                        }else if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
//                            globalParams[m[i].get('iid')] = paramDefaultValue;
//                        } else {
//                        	delete globalParams[m[i].get('iid')];
//                        }
//                        var ss = Ext.JSON.encode(m[i].data);
//                        if (i == 0) jsonData = jsonData + ss;
//                        else jsonData = jsonData + "," + ss;
//                    }
//
//                    jsonData = jsonData + "]";
//	  				if(chosedAgentIds.length<=0 && chosedGroupNames.length<=0) {
//	  					Ext.Msg.alert('提示', "请选择服务器！");
//                        return;
//	  				}
//	  				var rgIds = resourceGroupObj.getValue();
//	  				
//	  				var taskN = Ext.util.Format.trim(customName.getValue());
//	  				if(Ext.isEmpty(taskN)) {
//	  					Ext.Msg.alert('提示', "常用任务名称不能为空！");
//	  					return;
//	  				}
//	  				
//	  				if (fucCheckLength(taskN) > 255) {
//                        setMessage('常用任务名称不能超过255字符！');
//                        return;
//                    }
//	  				
//	  				var chosedAgentUsers = [];
//                    var chosedExecUsers =  [];
//                    if(chosedAgentIds.length>0) {
//                    	for(var x = 0, xLen = chosedAgentIds.length; x < xLen; x++){
//                    		var execUserValue = $("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+chosedAgentIds[x]);
//                    		if(undefined == execUserValue){
//                    			execUserValue = "";
//                    		}
//                    		chosedAgentUsers.push(chosedAgentIds[x]+"#;#"+execUserValue);
//        					chosedExecUsers.push("{\""+chosedAgentIds[x] + "\":\"" + execUserValue+"\"}");
//                    	}
//        			}
//	  				
//	  				var en = eachNum.getValue();
//	  				if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
//	  					if(en>eachNumForA) {
//	  						setMessage('并发数量不能超过'+eachNumForA);
//	                        return;
//	  					}
//	  				} else {
//	  					setMessage('并发数量不合法！');
//                        return;
//	  				}
//	  				
//	  				if(Ext.isEmpty(en)) {
//	  					en = eachNumForA;
//	  				}
//	  				var mxIid = iidForTaskAudi+":"+4;
//	  				startData[mxIid] = {
//							'actNo': 4,
//							'actType': 0,
//							'actName': '基础脚本1',
//							'isShutdown':false
//					};
//	  				startData[mxIid]['chosedResGroups'] = chosedGroupNames;
//	  			    startData[mxIid]['chosedAgentIds'] = chosedAgentIds;
//	  			    startData[mxIid]['globalParams'] = globalParams;
//	  			    startData[mxIid]['globalConfigParams'] = {};
//	  			    startData[mxIid]['globalStartUser'] = Ext.util.Format.trim(execUser.getValue());
//	  			    startData[mxIid]['globalConfigStartUser'] = {};
//	  			    startData[mxIid]['finalChosedAgentsAndDbSources'] = {};
//	  			    startData[mxIid]['eachNum'] = en;
//	  			    startData[mxIid]['chosedExecUsers'] = chosedExecUsers;
//	  			    
//	  				        		Ext.Ajax.request({
//	  				        	        url: 'checkCustomTemplateNameIsExist.do',
//	  				        	        params: {
//	  				        	        	iid:customIid,
//	  				        	            customName: taskN,
//	  				        	            flag: 1
//	  				        	        },
//	  				        	        method: 'POST',
//	  				        	        success: function(response, options) {
//	  				        	            if (!Ext.decode(response.responseText).success) {
//	  				        	                var newMsg = '<span style="color:red">常用任务名已存在,请更换常用任务名！</span>';
////	  		  				                	Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
//	  		  				                	Ext.Msg.alert('提示', newMsg);
//	  				        	            } else {
//	  				        	            	Ext.Ajax.request({
//  		        	        	                    url: 'saveFlowCustomTemplate.do',
//  		        	        	                    method: 'POST',
//  		        	        	                    params: {
//  		        	        	                    	iid:customIid,
//  		        	        	                    	customName: taskN,
//  		        	        	                    	serviceId: iidForTaskAudi,
//  		        	        	                        data: JSON.stringify(startData),
//  		        	        	                        flag: 1,
//  		        	        	                        resGroupFlag:resGroupFlag
//  		        	        	                    },
//  		        	        	                    success: function(response, options) {
//  		        	        	                        var success1 = Ext.decode(response.responseText).success;
//  		        	        	                        var message1 = Ext.decode(response.responseText).message;
//  		        	        	                        if (success1) {
//  		        	        	                            Ext.MessageBox.show({
//  		        	        	                                title: "提示",
//  		        	        	                                msg: '模板保存成功！<br>如需修改，请使用常用任务功能！',
//  		        	        	                                buttonText: {
//  		        	        	                                    yes: '确定'
//  		        	        	                                },
//  		        	        	                                buttons: Ext.Msg.YES
//  		        	        	                            });
//  		        	        	                        }
//  		        	        	                    },
//  		        	        	                    failure: function(result, request) {
//  		        	        	                        Ext.MessageBox.show({
//  		        	        	                            title: "提示",
//  		        	        	                            msg: "模板保存失败！",
//  		        	        	                            buttonText: {
//  		        	        	                                yes: '确定'
//  		        	        	                            },
//  		        	        	                            buttons: Ext.Msg.YES
//  		        	        	                        });
//  		        	        	                    }
//
//  		        	        	                });
//	  				        	            	if(typeof(coustomConfigWin)!="undefined" && coustomConfigWin){
//	  				        	            		coustomConfigWin.close();
//					  								}
//	  				        	            }
//	  				        	        },
//	  				        	        failure: function(result, request) {}
//	  				        	    });
//                }
//            },
//        	{
//                text: '提交',
//                margin:'6',
//                hidden:customIid>0 ? false:true,
//                cls: 'Common_Btn',
//                handler: function(){
//                	paramStore.sort('paramOrder', 'ASC');
//                	var m = paramStore.getRange(0, paramStore.getCount()-1);
//                    var jsonData = "[";
//                    for (var i = 0, len = m.length; i < len; i++) {
//                        //var n = 0;
//                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
//                        var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
//                        var paramValue = m[i].get("paramValue") ? m[i].get("paramValue").trim() : '';
//                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
//                        if(scriptTypeForTaskAudi=='bat') {
//                        	if (paramDefaultValue.indexOf('"')>=0) {
//	  		            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
//	  		                    return;
//		  		            }
//                        }
//                        if ("" == paramType) {
//                            setMessage('参数类型不能为空！');
//                            return;
//                        }
//                        if (fucCheckLength(paramDesc) > 250) {
//                            setMessage('参数描述不能超过250字符！');
//                            return;
//                        }
//
//                        if (paramType == 'IN-int' || paramType == 'int') {
//                            if (!checkIsInteger(paramDefaultValue)) {
//                                setMessage('参数类型为int，但参数默认值不是int类型！');
//                                return;
//                            }
//                            if (!checkIsInteger(paramValue)) {
//                                setMessage('参数类型为int，但参数值不是int类型！');
//                                return;
//                            }
//                        }
//                        if (paramType == 'IN-float' || paramType == 'float') {
//                            if (!checkIsDouble(paramDefaultValue)) {
//                                setMessage('参数类型为float，但参数默认值不是float类型！');
//                                return;
//                            }
//                            if (!checkIsDouble(paramValue)) {
//                                setMessage('参数类型为float，但参数值不是float类型！');
//                                return;
//                            }
//                        }
//                        if(!Ext.isEmpty(Ext.util.Format.trim(paramValue))){
//                        	globalParams[m[i].get('iid')] = paramValue;
//                        }else if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
//                            globalParams[m[i].get('iid')] = paramDefaultValue;
//                        } else {
//                        	delete globalParams[m[i].get('iid')];
//                        }
//                        var ss = Ext.JSON.encode(m[i].data);
//                        if (i == 0) jsonData = jsonData + ss;
//                        else jsonData = jsonData + "," + ss;
//                    }
//
//                    jsonData = jsonData + "]";
//                    
//	  				if(chosedAgentIds.length<=0 && chosedGroupNames.length<=0){
//	  					Ext.Msg.alert('提示', "请选择服务器！");
//                        return;
//	  				} 
//	  				var rgIds = resourceGroupObj.getValue();
//	  				
//	  				var taskN = Ext.util.Format.trim(customName.getValue());
//	  				if(Ext.isEmpty(taskN)) {
//	  					Ext.Msg.alert('提示', "常用任务名称不能为空！");
//	  					return;
//	  				}
//	  				
//	  				if (fucCheckLength(taskN) > 255) {
//                        setMessage('常用任务名称不能超过255字符！');
//                        return;
//                    }
//	  				
//	  				var chosedAgentUsers = [];
//                    var chosedExecUsers =  [];
//                    if(chosedAgentIds.length>0) {
//                    	for(var x = 0, xLen = chosedAgentIds.length; x < xLen; x++){
//                    		var execUserValue = $("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+chosedAgentIds[x]);
//                    		if(undefined == execUserValue){
//                    			execUserValue = "";
//                    		}
//                    		chosedAgentUsers.push(chosedAgentIds[x]+"#;#"+execUserValue);
//        					chosedExecUsers.push("{\""+chosedAgentIds[x] + "\":\"" + execUserValue+"\"}");
//                    	}
//        			}
//	  				
//	  				var en = eachNum.getValue();
//	  				if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
//	  					if(en>eachNumForA) {
//	  						setMessage('并发数量不能超过'+eachNumForA);
//	                        return;
//	  					}
//	  				} else {
//	  					setMessage('并发数量不合法！');
//                        return;
//	  				}
//	  				
//	  				if(Ext.isEmpty(en)) {
//	  					en = eachNumForA;
//	  				}
//	  				var mxIid = iidForTaskAudi+":"+4;
//	  				startData[mxIid] = {
//							'actNo': 4,
//							'actType': 0,
//							'actName': '基础脚本1',
//							'isShutdown':false
//					};
//	  				startData[mxIid]['chosedResGroups'] = chosedGroupNames;
//	  			    startData[mxIid]['chosedAgentIds'] = chosedAgentIds;
//	  			    startData[mxIid]['globalParams'] = globalParams;
//	  			    startData[mxIid]['globalConfigParams'] = {};
//	  			    startData[mxIid]['globalStartUser'] = Ext.util.Format.trim(execUser.getValue());
//	  			    startData[mxIid]['globalConfigStartUser'] = {};
//	  			    startData[mxIid]['finalChosedAgentsAndDbSources'] = {};
//	  			    startData[mxIid]['eachNum'] = en;
//	  			    startData[mxIid]['chosedExecUsers'] = chosedExecUsers;
//	  				        		
//	  			    
//
//	  				var version_flag = true;//单号显示标识
//	  				if(typeof taskType === 'undefined' || !scriptOddNumberSwitch){
//	  					version_flag = true;
//	  			    }else if(execUserSwitch&&taskType=='taskNormal'){
//	  			    	version_flag = false;
//	  			    }
//	  				
//	  				Ext.define('AuditorModel', {
//	  				    extend: 'Ext.data.Model',
//	  				    fields : [ {
//	  				      name : 'loginName',
//	  				      type : 'string'
//	  				    }, {
//	  				      name : 'fullName',
//	  				      type : 'string'
//	  				    }]
//	  				  });
//	  				
//	  				var auditorStore_tap = Ext.create('Ext.data.Store', {
//	  				    autoLoad: false,
//	  				    model: 'AuditorModel',
//	  				    proxy: {
//	  				      type: 'ajax',
//	  				      url: 'getExecAuditorList.do?scriptLevel='+scriptLevelForTaskAudi,
//	  				      reader: {
//	  				        type: 'json',
//	  				        root: 'dataList'
//	  				      }
//	  				    } 
//	  				});
//	  				var execStore = Ext.create('Ext.data.Store', {
//	  				    autoLoad: false,
//	  				    model: 'AuditorModel',
//	  				    proxy: {
//	  				      type: 'ajax',
//	  				      url: 'getExecUserList.do',
//	  				      reader: {
//	  				        type: 'json',
//	  				        root: 'dataList'
//	  				      }
//	  				    }
//	  				  });	
//	  				var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
////	  				    editable: false,
//	  				    fieldLabel: "审核人",
//	  				    store: auditorStore_tap,
//	  				    queryMode: 'local',
//	  				    width: 390,
//	  				    displayField: 'fullName',
//	  				    valueField: 'loginName',
//	  				    hidden:scriptLevelForTaskAudi==0?true:false,
//	  				    labelWidth : 65,
//	  					labelAlign : 'right',
//	  					listeners: { //监听 
//	  				        render : function(combo) {//渲染 
//	  				            combo.getStore().on("load", function(s, r, o) { 
//	  				                combo.setValue(r[0].get('loginName'));//第一个值 
//	  				            }); 
//	  				        } 
//	  				    } 
//	  				  });
//	  				
//	  				var execComBox_tap = Ext.create('Ext.form.ComboBox', {
//	  				    fieldLabel: "执行人",
//	  				    store: execStore,
//	  				    queryMode: 'local',
//	  				    width: 390,
//	  				    displayField: 'fullName',
//	  				    valueField: 'loginName',
//	  				    labelWidth : 65,
//	  				    editable : true,
//	  				    hidden:!execUserSwitch|| scriptLevelForTaskAudi==0?true:false,
//	  					labelAlign : 'right'
//	  				  });
//	  				execStore.load({
//	  				    callback : function (records, operation, success)
//	  				    {
//	  				    	execComBox_tap.setValue (loginUserFlowTemplateManager);
//	  				    }
//	  			    });
//	  				function CurentTime()
//	  			    { 
//	  			        var now = new Date();
//	  			        
//	  			        var year = now.getFullYear();       //年
//	  			        var month = now.getMonth() + 1;     //月
//	  			        var day = now.getDate();            //日
//	  			        
//	  			        var hh = now.getHours();            //时
//	  			        var mm = now.getMinutes();          //分
//	  			        var ss = now.getSeconds();           //秒
//	  			        
//	  			        var clock = year;
//	  			        
//	  			        if(month < 10){
//	  			        	
//	  			        	clock += "0";
//	  			        }else{
//	  			        	clock += "";
//	  			        }
//	  			        
//	  			        clock +=month;
//	  			        
//	  			        if(day < 10){
//	  			        	clock += "0";
//	  			        }else{
//	  			        	clock += "";
//	  			        }
//	  			            
//	  			            
//	  			        clock += day;
//	  			        
//	  			        if(hh < 10){
//	  			        	clock += "0";
//	  			        }else{
//	  			        	clock += "";
//	  			        }
//	  			            
//	  			            
//	  			        clock += hh;
//	  			        if (mm < 10) 
//	  			        {
//	  			        	clock += '0'; 
//	  			        	}
//	  			        else{
//	  			        	clock += "";
//	  			        }
//	  			        clock += mm ; 
//	  			         
//	  			        if (ss < 10) {
//	  			        	clock += '0'; 
//	  			        }
//	  			        else{
//	  			        	clock += "";
//	  			        }
//	  			        clock += ss; 
//	  			        return(clock); 
//	  			    }
//	  				var taskName = new Ext.form.TextField({
//	  					name: 'taskName',
//	  					fieldLabel: '任务名称',
//	  					emptyText: '',
//	  					labelWidth : 65,
//	  					padding : '5 0 5 0',
//	  					labelAlign : 'right',
//	  					value: serviceNameForTaskAudi+'_'+CurentTime(),
//	  					width: 390
//	  				});
//	  				var execDesc = Ext.create('Ext.form.field.TextArea', {
//	  			        fieldLabel: '执行描述',
//	  			        labelWidth: 65,
//	  			        padding: '0 2 0 4',
//	  			        height: 60,
//	  			        maxLength: 2000,
//	  			        width: 385,
//	  			        autoScroll: true
//	  			    });
//	  				var butterflyVerison = new Ext.form.TextField({
//	  					name: 'butterflyversion',
//	  					fieldLabel: '单号',
//	  					padding: '0 2 0 8',
//	  					hidden:version_flag,
//	  					emptyText: '',
//	  					labelWidth : 65,
////	  					labelAlign : 'right',
//	  					width: 382
//	  				});
//	  				var checkVersion=new  Ext.form.Checkbox({   
//	  			        id:"checkVersion",               
//	  				    name:"checkVersion",
//	  				    boxLabel:"单号补添:" ,
//	  				    boxLabelAlign:"before",
//	  				    labelWidth:65,
//	  				    padding: '0 2 0 4',
//	  				    hidden:version_flag
//	  				});
//	  				Ext.create('Ext.window.Window', {
//	  			  		title : '提交配置信息',
//	  			  		autoScroll : true,
//	  			  		modal : true,
//	  			  		resizable : false,
//	  			  		closeAction : 'destroy',
//	  			  		width : 420,
////	  			  		height : scriptLevelForTaskAudi==0?(version_flag?215:350):(version_flag?270:380),
//	  			  		height : scriptLevelForTaskAudi==0?(version_flag?235:305):(version_flag?300:365),
//	  			  		items:[taskName, auditorComBox_tap,execComBox_tap,butterflyVerison,checkVersion,execDesc],
//	  			  		buttonAlign: 'center',
//	  			  		buttons: [{ 
//	  			  			xtype: "button", 
//	  			  			text: "确定", 
//	  			  			handler: function () {
//	  			  				var self = this;
//	  			  				var auditor = auditorComBox_tap.getValue();
//	  			  				if(!auditor) {
//	  			  					Ext.Msg.alert('提示', "没有选择审核人！");
//	  			  					return;
//	  			  				}
//	  			  				var execute = execComBox_tap.getValue();
//		  			  			if(!execute) {
//	  			  					Ext.Msg.alert('提示', "没有选择执行人！");
//	  			  					return;
//	  			  				}
//	  			  				var performUser = execComBox_tap.getValue();
//	  			  				var taskN = Ext.util.Format.trim(taskName.getValue());
//	  			  				if(Ext.isEmpty(taskN)) {
//	  			  					Ext.Msg.alert('提示', "任务名称不能为空！");
//	  			  					return;
//	  			  				}
//	  			  				
//	  			  				if (fucCheckLength(taskN) > 255) {
//	  			                    Ext.Msg.alert('提示', "任务名称不能超过255字符！");
//	  			                    return;
//	  			                }
//	  			  				var butterflyV = "";
//	  			  				var check =Ext.getCmp("checkVersion").getValue();
//	  			  				if(execUserSwitch&&!check&&!butterflyVerison.hidden){
//	  			  					butterflyV=Ext.util.Format.trim(butterflyVerison.getValue());
//	  			  					if(butterflyV==null||butterflyV==""){
//	  			  						Ext.Msg.alert('提示', "单号不能为空！");
//	  				  					return;
//	  			  					}
//	  			  					if (fucCheckLength(butterflyV) > 255) {
//	  			                        Ext.Msg.alert('提示', "单号不能超过255字符！");
//	  			                        return;
//	  			                    }
//	  			  				}
//	  			  				var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
//	  			  				if(Ext.isEmpty(execDescForExec)) {
//	  			  					Ext.Msg.alert('提示', "没有填写执行描述！");
//	  			  					return;
//	  			  				}
//	  			  				if(fucCheckLength(execDesc.getValue() > 2000)) {
//	  			  					Ext.Msg.alert('提示', "执行描述内容长度超过2000个字符！");
//	  			  					return;
//	  			  			    }
//	  			  			var isFromCustom = 0;
//	  			      	    if(showConfigSwitch){
//	  			      		isFromCustom=1;
//	  			      	    }
//	  			  			Ext.Ajax.request({
//			  						url :'scriptExecAuditing.do' ,
//			  						method : 'POST',
//			  						params : {
//			  							serviceId: iidForTaskAudi,
//			  							execUser:  execUser.getValue(),
//			  							agents: chosedAgentIds,
//			  							rgIds: '',
//			  							params: jsonData,
//			  							execDesc:execDescForExec,
//			  							auditor: auditor,
//			  							isDelay:false,
//			  							execTime:'',
//			  							execStrategy:0,
//			  							taskName: taskN,
//			  							performUser:performUser,
//			  							butterflyversion:butterflyV,
//			  							data: JSON.stringify(startData),
//			  							eachNum: en,
//			  							scriptLevel: scriptLevelForTaskAudi,
//			  							chosedAgentUsers:chosedAgentUsers,
//			  							isFromCustom:isFromCustom,
//			  							resGroupFlag:resGroupFlag
//			  						},
//			  						success: function(response, opts) {
//			  							var success = Ext.decode(response.responseText).success;
//			  							var message = Ext.decode(response.responseText).message;
//			  							if(success) {
//			  								if(scriptLevelForTaskAudi!=0){
//			  									Ext.MessageBox.alert("提示", "请求已经发送到审核人");
//			  								}
//			  								var iworkItemid = Ext.decode(response.responseText).workItemId;
//			  								if(scriptLevelForTaskAudi==0){//白名单，直接调用双人复核中，同意执行方法
//			  									Ext.Ajax.request ({
//			  										url : 'scriptExecForOneRecord.do',
//			  										method : 'POST',
//			  										params :
//			  										{
//			  											iworkItemid: iworkItemid
//			  										},
//			  										success : function (response, opts)
//			  										{
//			  											var success = Ext.decode (response.responseText).success;
//			  											if(success){
//				  												var	uuid ='';
//					  											Ext.Ajax.request({
//					  								                url: 'queryUuidById.do',
//					  								                method: 'POST',
//					  								                async: false,
//					  								                params: {
//					  								                	serviceId: iidForTaskAudi
//					  								                },
//					  								                success: function(response, options) {
//					  								                    uuid = Ext.decode(response.responseText).serviceUuid;
//					  								                },
//					  								                failure: function(result, request) {
//					  								                }
//					  								            });
//			  													Ext.Ajax.request({
//			  														url : 'execScriptServiceStart.do',
//			  														method : 'POST',
//			  														params : {
//			  															serviceId : iidForTaskAudi,
//			  															uuid : uuid,
//			  															serviceName : serviceNameForTaskAudi,
//			  															scriptType:0,
//			  															workItemId : iworkItemid,
//			  															coatId : 0,
//			  															isFlow: 1
//			  														},
//			  														success : function(response, request) {
//			  															var success = Ext.decode(response.responseText).success;
//			  															if (success) {
//			  																var flowId = Ext.decode(response.responseText).content;
//			  																Ext.MessageBox.alert("提示", "白名单任务启动成功，请在执行历史中查看！flowId："+flowId);
//			  															}
//			  														},
//			  														failure : function(result, request) {
//			  															Ext.Msg.alert('提示', '执行失败！');
//			  														}
//			  													}); 
//			  												
//			  											}
//			  										}
//			  									});
//			  								}
//			  								self.up("window").close();
//			  							} else {
//			  								Ext.MessageBox.alert("提示", message);
//			  							}
//			  						},
//			  						failure: function(result, request) {
//			  							secureFilterRs(result,"操作失败！");
//			  							self.up("window").close();
//			  						}
//			  					});
//	  			  				
//	  			  				
//	  			  			}
//	  				        }]
//	  			  		
//	  			  	}).show();
//	  				auditorStore_tap.load();
//
//	  				if(typeof(coustomConfigWin)!="undefined" && coustomConfigWin){
//  	            		coustomConfigWin.close();
//							}    
//	  			    
//                }
//            }
        
        ],
        buttonAlign: 'center',
        dockedItems: topBar
    });
    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
    });
    
    
    function viewDetail(serviceId) {
    		DetailWinTi = Ext.create('widget.window', {
    			title : '详细信息',
    			closable : true,
    			closeAction : 'destroy',
    			width : contentPanel.getWidth(),
    			minWidth : 350,
    			height : contentPanel.getHeight(),
    			draggable : false,// 禁止拖动
    			resizable : false,// 禁止缩放
    			modal : true,
    			loader : {}
    		});
    	
    	DetailWinTi.getLoader().load(
    			{
    				url : 'getQueryOneServiceForView.do',
    				params : {
    					iid:serviceId,
    					flag: 1,
    					hideReturnBtn: 1
    				},
    				autoLoad : true,
    				scripts : true
    			});
    	DetailWinTi.show();
    }
    
	 function StringToPassword(strs){
			if(strs&&strs!=null&strs!=''){
				var password = '';
				for(var i=0;i<strs.length;i++){
					password = password + '●';
				}
				return password;
			}else{
				return '';
			}
	 }
	 
	var execUserConfigWindow;
	var execUserConfigForm = null;
	var execUserNameText = null;
	function openExecUserConfigData(record){
		if (execUserConfigWindow == undefined || !execUserConfigWindow.isVisible()) {
			if(isSumpAgentSwitch == true) {
    			var sumpAgentStore = Ext.create('Ext.data.Store', {
    				fields : [ 'iid', 'userName' ],
    				autoLoad : true,
    				proxy : {
    					type : 'ajax',
    					url : 'getSumpAgentUserList.do',
    					reader : {
    						type : 'json',
    						root : 'dataList'
    					}
    				}
    			});
    			
    			sumpAgentStore.on('beforeload', function(store, options) {
    		        var queryparams = {
    		        	agentId: record.get('iid')
    		        };
    		        Ext.apply(sumpAgentStore.proxy.extraParams, queryparams);
    		    });
    			
    			execUserNameText = Ext.create('Ext.form.field.ComboBox', {
    				name : 'execUserName',
    				labelWidth : 65,
    				queryMode : 'local',
    				fieldLabel : '执行用户',
    				width : 320,
    				displayField : 'userName',
    				valueField : 'iid',
    				editable : true,
    				typeAhead : true,
    				emptyText : '--请选择执行用户--',
    				store : sumpAgentStore,
    		        labelAlign : 'right'
    			});
    			if(null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execUserNmae'))){
            		var sumpAgentCount = sumpAgentStore.getRange();
            		var newExecUserName = $("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+record.get("iid"));
                    if(sumpAgentCount.length>0) {
        				if(undefined == newExecUserName){
        					execUserNameText.setRawValue(record.get('execUserNmae'));
        				} else {
        					execUserNameText.setValue(newExecUserName);
        				}
                    } else {
                    	if(undefined == newExecUserName){
                    		execUserNameText.setValue(record.get('execUserNmae'));
                        	execUserNameText.setRawValue(record.get('execUserNmae'));
        				} else {
        					execUserNameText.setValue(newExecUserName);
                        	execUserNameText.setRawValue(newExecUserName);
        				}
                    }
            	}
    		} else {
    			execUserNameText =  Ext.create ('Ext.form.TextField',
				{
					fieldLabel: '执行用户',
					labelAlign: 'right',
					name : "execUserName",
				    labelWidth : 65,
				    emptyText : '--请输入执行用户--',
				    width : 320,
				    xtype : 'textfield'
				});
    			if(null != execUserNameText && checkIsNotEmptyAndUndefined(record.get('execUserNmae'))){
    				var newExecUserName1 = $("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+record.get("iid"));
    				if(undefined == newExecUserName1){
    					execUserNameText.setValue(record.get('execUserNmae'));
    				} else {
    					execUserNameText.setValue(newExecUserName1);
    				}
            	}
    		}
	 		
	 		execUserConfigForm = Ext.create('Ext.ux.ideal.form.Panel',{
					region : 'north',
				  	layout : 'anchor',
				  	//iqueryFun : queryBtnFun,
				  	buttonAlign : 'right',
				  	baseCls:'customize_gray_back',
					collapsible : false,//可收缩
					collapsed : false,//默认收缩
				  	border : false,
				  	dockedItems : [{
							xtype : 'toolbar',
							dock : 'top',
							border : false,
							baseCls:'customize_gray_back',
							items:[execUserNameText]
				  	},{
						xtype : 'toolbar',
						dock : 'top',
						border : false,
						baseCls:'customize_gray_back',
						items:['->',{
							text : '确定',
							cls : 'Common_Btn',
							icon : '',
							handler: function(){  
								chosedExecUser(record);  
		                    }  
						}]
				  	}]
				});
	 		
	 		var execUserConfig_mainPanel = Ext.create("Ext.panel.Panel", {
					layout : 'border',
			        width : "100%",
			        height : "100%",
					border : false,
					items : [ execUserConfigForm ],
					cls:'customize_panel_bak'
				});
	 		
	 		execUserConfigWindow = Ext.create('Ext.window.Window', {
					title : "配置执行用户",
					modal : true,
					closeAction : 'destroy',
					constrain : true,
					autoScroll : false,
					//upperWin : errorTaskWin,
					width : 330,
					height : 150,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					items:  [execUserConfig_mainPanel]
				});
			}
	 	execUserConfigWindow.show();
	}
	 
	function chosedExecUser(record){
		$("#taskCustomPageExecUserNameText").attr("taskCustomPageExecUserNameText"+record.get("iid"),execUserConfigForm.getForm().findField('execUserName').getRawValue());
		if(isSumpAgentSwitch == true) {
    		record.set('execUserNmae',execUserConfigForm.getForm().findField('execUserName').getRawValue());
    	} else {
    		record.set('execUserNmae',execUserConfigForm.getForm().findField('execUserName').getValue());
    	}
	 	record.commit();
	 	execUserConfigWindow.hide();
	}
	 
	function checkIsNotEmptyAndUndefined(str)
	{
	     if(trim(str)== "" && trim(str)== "undefined"){
	    	 return false;
	     } else {
	    	 return true; 
	     }
	         
	}
});