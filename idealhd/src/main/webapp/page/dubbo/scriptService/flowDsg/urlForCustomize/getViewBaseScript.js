Ext.onReady(function() {
	var sysID;
	var busID;
    // 清理主面板的各种监听时间
    destroyRubbish();
    var uploadProcessWin; 
    var attachmentIds = [];

    Ext.tip.QuickTipManager.init();

    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });

    bussData.on('load',
    function(store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load',
    function(store, options) {
        bussTypeCb.setValue(busID);
    });

    Ext.define('editScriptModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'excepResult',
            type: 'string'
        },
        {
            name: 'errExcepResult',
            type: 'string'
        },
        {
        	name: 'versionText',
        	type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        },
        {
            name: 'paramRuleOrder',
            type: 'long'
        },
        {
            name: 'paramRuleIn',
            type: 'string'
        },
        {
            name: 'paramRuleOut',
            type: 'string'
        },
        {
            name: 'paramRuleDesc',
            type: 'string'
        },
        {
            name: 'paramRuleType',
            type: 'string'
        },
        {
            name: 'paramRuleLen',
            type: 'string'
        }]
    });
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
            iid: viewBaseScriptiid
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
        var reader = store.getProxy().getReader();
        scName.setValue(reader.jsonData.scriptName);
        sName.setValue(reader.jsonData.serviceName);
        viewBaseScriptscriptuuid=reader.jsonData.scriptuuid;
        excepResult.setValue(reader.jsonData.excepResult);
        errExcepResult.setValue(reader.jsonData.errExcepResult);
        versionText.setValue(reader.jsonData.version);
        timeout.setValue(reader.jsonData.timeout==-1?null:reader.jsonData.timeout);
        usePlantForm.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);
        suUser.setValue(reader.jsonData.suUser);
        dbType.setValue(reader.jsonData.dbType);
        serviceType.setValue(reader.jsonData.serviceType);
        isExam.setValue(reader.jsonData.iisExam);
        var scriptT = reader.jsonData.scriptType;
        var scriptTypeDisplay = scriptT;
//		if(scriptTypeDisplay=='sh') {
//			scriptTypeDisplay = "shell";
//		} else if(scriptTypeDisplay=='py'){
//			scriptTypeDisplay = "python";
//		}
//        Ext.getCmp("s_type").setBoxLabel( scriptTypeDisplay );//added.
        
        if (scriptT == 'sh') {
        	FieldContainer.items.items[0].setValue(true);
            checkRadioForShare = 0;
            editor.setOption("mode", 'shell');
        } else if (scriptT == 'bat') {
        	FieldContainer.items.items[1].setValue(true);
            checkRadioForShare = 1;
            editor.setOption("mode", 'bat');
        } else if (scriptT == 'py') {
        	FieldContainer.items.items[3].setValue(true);
            checkRadioForShare = 3;
            editor.setOption("mode", 'python');
        } else if (scriptT == 'sql') {
        	FieldContainer.items.items[4].setValue(true);
            checkRadioForShare = 4;
            editor.setOption("mode", 'sql');
        } else if (scriptT == 'perl') {
        	FieldContainer.items.items[2].setValue(true);
            checkRadioForShare = 2;
            editor.setOption("mode", 'text/x-perl');
        } else if (scriptT == 'ps1') {
        	FieldContainer.items.items[5].setValue(true);
            checkRadioForShare = 6;
            editor.setOption("mode", 'powershell');
        }
        editor.setOption('value', reader.jsonData.content);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        bussData.load();
        attachmentStore.load();
        paramStore.load();
    });
    /** *********************Panel********************* */
    var FieldContainer = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 65,
        name: 'ra_s_type',
        padding: '0 5 10 5',
        items: [{
            name: 'ra_s_type',
            width: 80,
            inputValue: 'sh',
            boxLabel: 'shell',
            checked: true,
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        if (checkRadioForShare != 0) {
                            editor.setOption("mode", 'shell');
                            checkRadioForShare = 0;
                        }
                    }
                }
            }
        },
        {
            name: 'ra_s_type',
            width: 80,
            inputValue: '1',
            boxLabel: 'bat',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        if (checkRadioForShare != 1) {
                            checkRadioForShare = 1;
                            editor.setOption("mode", 'bat');
                        }
                    }
                }
            }
        },
        {
            name: 'ra_s_type',
            width: 80,
            inputValue: '2',
            boxLabel: 'perl',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForShare = 2;
                        editor.setOption("mode", 'text/x-perl');
                    }
                }
            }
        },
        {
            name: 'ra_s_type',
            width: 80,
            inputValue: '3',
            boxLabel: 'python',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForShare = 3;
                        editor.setOption("mode", 'python');
                    }
                }
            }
        },
        {
            name: 'ra_s_type',
            width: 80,
            inputValue: '4',
            boxLabel: 'sql',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForShare = 4;
                        editor.setOption("mode", 'sql');
                    }
                }
            }
        },{
            name: 'ra_s_type',
            width: 100,
            inputValue: '6',
            boxLabel: 'powershell',
            listeners: {
                click: {
                    element: 'el',
                    // bind to the
                    fn: function(value) {
                        checkRadioForShare = 6;
                        editor.setOption("mode", 'powershell');
                    }
                }
            }
        }]
    });

    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth:65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var suUser = new Ext.form.TextField({
        name: 'suUser',
        fieldLabel: '启动用户',
        displayField: 'suUser',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var versionText = new Ext.form.TextField({
    	name: 'versionText',
    	fieldLabel: '版本',
    	displayField: 'versionText',
    	emptyText: '',
    	labelWidth: 65,
    	readOnly: true,
    	padding: '0 5 0 0',
    	columnWidth: 1
    });
    var timeout = new Ext.form.TextField({
		        name: 'timeout',
		        fieldLabel: '超时(秒)',
		        displayField: 'timeout',
		        emptyText: '',
		        hidden:!scriptTimeoutSwitch,
		        labelWidth: 65,
		        padding: '0 5 0 0',
		        columnWidth:1
		    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
//        fieldLabel: '功能概述',
//        displayField: 'funcdesc',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        columnWidth: 1,
        height: 130,
        autoScroll: true
    });
    var dbType = Ext.create('Ext.form.field.ComboBox', {
		name : 'dbType',
		labelWidth : 65,
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '数据库类型',
		padding : '0 5 0 0',
		editable : false,
		readOnly: true,
		displayField : 'text',
		valueField : 'value',
		value : '1',
		emptyText : '--请选择数据库类型--',
		store : new Ext.data.SimpleStore({
			fields : [ 'value', 'text' ],
			data : [ [ '1', 'ORACLE' ], [ '2', 'DB2' ],
					[ '3', 'MYSQL' ] ]
		}),
        columnWidth: 1
	});
	// 服务类型
	var serviceType = Ext.create('Ext.form.field.ComboBox', {
		name : 'serviceType',
		labelWidth : 65,
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '服务类型',
		padding : '0 5 0 0',
		editable : false,
		readOnly: true,
		displayField : 'text',
		valueField : 'value',
		value : '1',
		emptyText : '--请选择服务类型--',
		store : new Ext.data.SimpleStore({
			fields : [ 'value', 'text' ],
			data : [ [ '0', '应用' ], [ '1', '采集' ] ]
		}),
        columnWidth: 1
	});
	// 发起审核
	var isExam = Ext.create('Ext.form.field.ComboBox', {
		name : 'isExam',
		labelWidth : 65,
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '发起审核',
		padding : '0 5 0 0',
		editable : false,
		readOnly: true,
		displayField : 'text',
		valueField : 'value',
		emptyText : '--请选择--',
		store : new Ext.data.SimpleStore({
			fields : [ 'value', 'text' ],
			data : [ [ '1', '是' ], [ '0', '否' ] ]
		}),
        columnWidth: 1
	});
    var scriptForm = Ext.create('Ext.form.Panel', {
    	region: 'west',
        width: '20%',
        height: 230,
        border: false,
        collapsible : true,
        collapsed: true,
        layout: 'anchor',
        title: '基本信息',
        cls:'window_border panel_space_top panel_space_left panel_space_right',
        style:'background-color:white',
//        margin: 10,
        items: [{
            border: false,
            layout: 'column',
            margin: '50 5 5 5',
            items: [bussCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [bussTypeCb]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [sName]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [scName]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [dbType]
        },
        {
            border: false,
            margin: '5',
            layout: 'column',
            items: [serviceType]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [isExam]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [usePlantForm]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [excepResult]
        },
        {
            layout: 'column',
            border: false,
            margin: '5',
            items: [errExcepResult]
        },{
            layout: 'column',
            border: false,
            margin: '5',
            items: [suUser]
        },{
            layout: 'column',
            border: false,
            margin: '5',
            items: [versionText]
        },{
				layout : 'column',
				border : false,
				margin : '5',
				items : [ timeout ]
		}]
    });
    
    var funcDescForm = Ext.create('Ext.form.Panel', {
        width: '100%',
        height: 162,
        border: true,
        layout: 'anchor',
        margin: '0 0 5 0',
        collapsible : false,
        baseCls:'customize_gray_back',
        title: '功能说明',
        items: [{
            layout: 'column',
            border: false,
            items: [funcDesc]
        }]
    });

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });
	
   var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: viewBaseScriptscriptuuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });
    
    var paramRuleTypeStore = Ext.create('Ext.data.Store', {
		fields : [ 'name','id' ],
		data : [{
			"name" : "VARCHAR",
			"id" : 0
		}, {
			"name" : "INTEGER",
			"id" : 1
		}, {
			"name" : "DECIMAL",
			"id" : 2
		}, {
			"name" : "TIMESTAMP",
			"id" : 3
		}, {
			"name" : "CLOB",
			"id" : 4
		}]
	});
    
	var paramRuleCombo = Ext.create('Ext.form.field.ComboBox', {
		store : paramRuleTypeStore,
		queryMode : 'local',
		forceSelection : true,
		// 要求输入值必须在列表中存在
		typeAhead : true,
		// 允许自动选择
		displayField : 'name',
		valueField : 'id',
		triggerAction : "all"
	});
    var p4Columns = [
		{
			text : '主键',
			dataIndex : 'iid',
			width : 40,
			hidden : true
		},
		{
			text : '顺序',
			dataIndex : 'paramRuleOrder',
			width : 50,
			editor : {
				allowBlank : false,
				xtype : 'numberfield',
				maxValue : 30,
				minValue : 1
			},
			renderer : function(value, metaData, record, rowIdx, colIdx, store) {
				metaData.tdAttr = 'data-qtip="'
						+ Ext.String.htmlEncode(+"输出列名称：" + record.get('paramRuleOut') + "<br>排序：" + record.get('paramRuleOrder') + "<br>描述：" + record.get('paramRuleDesc'))
						+ '"';
				return value;
			}
		}, {
			text : '分隔符',
			dataIndex : 'paramRuleIn',
			width : 85,
			editor : {},
			hidden:true
			
		}, {
			text : '输出列名称',
			dataIndex : 'paramRuleOut',
			width : 140,
			editor : {
				allowBlank : false
			}
		},
		{
			text : '类型',
			dataIndex : 'paramRuleType',
			width : 85,
//			editor : paramRuleCombo,
			renderer : function(value, metaData, record, rowIdx, colIdx, store) {
				metaData.tdAttr = 'data-qtip="'
						+ Ext.String.htmlEncode(" 类型：" + record.get('paramRuleType')) + '"';
				if(value==0){
					value="VARCHAR";
				}else if(value==1){
					value="INTEGER";
				}else if(value==2){
					value="NUMBER";
				}else if(value==3){
					value="DATE";
				}else if(value==4){
					value="CLOB";
				}else{
					value="VARCHAR"
				}
				return value;
			}
		}, {
			text : '长度',
			dataIndex : 'paramRuleLen',
			width : 85,
			editor : {}
		}, {
			text : '别名',
			dataIndex : 'paramRuleDesc',
//			flex : 1,
			width : 85,
			editor : {
				allowBlank : true
			}
		} ];
    var ruleparamStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    ruleparamStore.on('beforeload', function(store, options) {
    	console.log(viewBaseScriptscriptuuid);
        var new_params = {
            scriptId: viewBaseScriptscriptuuid,
            iflag:0
        };

        Ext.apply(ruleparamStore.proxy.extraParams, new_params);
    });
    var p4Grid = Ext.create('Ext.grid.Panel', {
		region: 'south',
		height:'50%',
		store: ruleparamStore,
		cls:'customize_panel_back',
	//	ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		columnLines: true,
		columns: p4Columns
	});
    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 80
    },
   {
        text: '默认值',
        dataIndex: 'paramDefaultValue',
        width: 70,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            var backValue = "";
        	if(record.get('paramType')== 'IN-string(加密)' || record.get('paramType')== 'IN-string'){
        		backValue = StringToPassword(value);
        	}else{
        		backValue = value;
        	}
        	return backValue;
        } 
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50
    }];
    
    var paramGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls:'window_border panel_space_top  panel_space_right',
        store: paramStore,
        title:'参数',
        border: true,
        emptyText: '没有脚本参数',
    //    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        columnLines: true,
        columns: paramColumns
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: viewBaseScriptscriptuuid,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    
    function removeByValue(arr, val) {
  	  for(var i=0; i<arr.length; i++) {
  	    if(arr[i] == val) {
  	      arr.splice(i, 1);
  	      break;
  	    }
  	  }
    }
    
    var attachmentColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },{
		        menuDisabled: true,
		        sortable: false,
		        xtype: 'actioncolumn',
		        width: 50,
		        items: [ 
			        {
			            iconCls: 'script_download',
			            tooltip: '下载',
			            handler: function(grid, rowIndex, colIndex) {
			                  var rec = attachmentStore.getAt(rowIndex);
			                  //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
			            	  window.location.href = 'downloadScriptAttachment.do?iid='+rec.get('iid');
			            }
			        }
			     ]
	 }/*,
    {
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    }*/];
    
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	region: 'south',
    	cls:'window_border panel_space_top  panel_space_right',
    	height: contentPanel.getHeight() * 0.4,
        weigth:350,
        store: attachmentStore,
    //    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        title:'附件',
        border: true,
        emptyText: '没有附件',
        columnLines: true,
        columns: attachmentColumns
    });

    var mainP = Ext.create('Ext.panel.Panel', {
    	region: 'center',
        border: true,
        title: "脚本内容",
        collapsible : false,
        cls:'window_border panel_space_top  panel_space_right',
        height: contentPanel.getHeight()-100,
        html: '<textarea id="codeEditView" value style="width: 100%;height:100%;"></textarea>',
        tbar: [FieldContainer]
    });
    if(ssprjflag=='1'){
   	 	attachmentGrid.hidden=true;
		 bussCb.hidden=true;
		 bussTypeCb.hidden=true;
		 excepResult.hidden=true;
		 errExcepResult.hidden=true;
		 suUser.hide();
   }else{
   		p4Grid.hidden=true;
   		dbType.hidden=true;
		serviceType.hidden=true;
		isExam.hidden=true;
   }
    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
    	region: 'east',
//        width: '100%',
        collapsible : false,
        border: false,
        width: 350,
        minWidth: 120,
        //minHeight: 140,
        height:contentPanel.getHeight()-100,
        layout: {
            type: 'border'
        },
        items: [ paramGrid,p4Grid,attachmentGrid]
    });
    
    var bigCenterPanel = Ext.create('Ext.panel.Panel', {
    	region: 'center',
    	cls:'customize_panel_back',
    	layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
//        margin: 10,
//         collapsible : true,
        border: false,
        height: 438,
        items: [mainP, paramsAndFuncDescPanel]
//        items: [attachmentGrid, mainP, paramsAndFuncDescPanel]
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "viewBaseScript_area",
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        border: false,
        autoScroll: true,
        cls:'customize_panel_back',
        height: contentPanel.getHeight()-68,
        items: [scriptForm, bigCenterPanel],
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            hidden:hideReturnBtn,
            baseCls:'customize_gray_back',
            items: [{
                text: '返回',
                height: 30,
                cls: 'Common_Btn',
                handler: function(){
                	destroyRubbish(); // 销毁本页垃圾
                    contentPanel.getLoader().load({
                        url: url,
                        scripts: true
                    });
                }
            }]
        }]
    });
    
    
    
    var editor = CodeMirror.fromTextArea(document.getElementById('codeEditView'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        readOnly: true
    });
    editor.setSize(mainP.getWidth()-3, mainP.getHeight()-100);
    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainP.getWidth(), mainP.getHeight());
    });
    
    
     function StringToPassword(strs){
		if(strs&&strs!=null&strs!=''){
			var password = '';
			for(var i=0;i<strs.length;i++){
				password = password + '●';
			}
			return password;
		}else{
			return '';
		}
	}
});