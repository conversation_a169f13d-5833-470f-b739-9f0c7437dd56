Ext.onReady(function() {
	
	destroyRubbish();
	
	// 将通过jsp跳转传递的变量全部进行转移和清理。要注意变量的值传递可以直接清理。变量如果传递的是引用，注意不要把真实值清理掉，清理引用即可。
	var menuId = tempData.menuId;
	delete tempData;
	
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		labelWidth : 70,
		width : '22%',
        labelAlign : 'right',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var templateName = new Ext.form.TextField({
		name : 'templateName',
		fieldLabel : '模板名称',
		emptyText : '--请输入模板名称--',
		labelWidth : 70,
		width : '22%',
		labelAlign : 'right',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	
	var search_form = Ext.create('Ext.form.Panel', {
		 region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	border : false,
	    	 baseCls:'customize_gray_back',
	    	dockedItems : [{
				xtype : 'toolbar',
				baseCls:'customize_gray_back',    
				border : false,
				dock : 'top',
				items: [templateName, sName,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						pageBar.moveFirst();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
					}
				}]
			}]
		});
	
	Ext.define('FlowTemplateCustomModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceId'         ,type : 'long'}, 
		    {name : 'customName' ,type : 'string'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'bussTypeName'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'userName',type : 'string'}, 
		    {name : 'userFullName',type : 'string'},
		    {name : 'scriptVersion',type : 'string'}, 
		    {name : 'createTime'     ,type : 'string'}
	    ]
	});
	
	var flowTemplateCustomStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'FlowTemplateCustomModel',
		proxy : {
			type : 'ajax',
			url : 'flowTemplateCustomList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	flowTemplateCustomStore.on('beforeload', function (store, options) {
	    var new_params = {  
	    		sName:sName.getValue(),
	    		templateName: templateName.getValue(),
	    		isEMscript: -1,
	    	    iappSysIds:'',
	    		flag: 0
	    };
	    
	    Ext.apply(flowTemplateCustomStore.proxy.extraParams, new_params);
    });
	
	var flowTemplateCustomColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 40
		}, 
		{
		    text : '主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
			text : '组合ID',
			dataIndex : 'serviceId',
			width : 40,
			hidden : true
		}, 
		{
			text : '模板名称',
		    dataIndex : 'customName',
		    flex:1
		},
		{
			text : '服务名称',
			dataIndex : 'serviceName',
			width : 200,flex:1
		},
		{
		    text : '一级分类',
		    dataIndex : 'bussName',
		    width : 200,flex:1
		}, 
		{
			text : '二级分类',
			dataIndex : 'bussTypeName',
			width : 250,flex:1
		},
		{
			text : '版本',
			dataIndex : 'scriptVersion',
			width : 150,
			renderer:function(value,p,record,rowIndex){
				if(value) {
					return value;
				} else {
					return '无版本号';
				}
			}
		},
		{
			text : '创建者',
			dataIndex : 'userFullName',
			width : 150,
		},
		{
			text : '创建时间',
			dataIndex : 'createTime',
			width : 150,
		},
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			width:155,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid');
				var serviceId =  record.get('serviceId');
				var customName = record.get('customName');
				var serviceName = record.get('serviceName');
				var bussId = record.get('bussId');
				var bussTypeId  =record.get('bussTypeId');
				var scriptVersion = record.get('scriptVersion');
				if(scriptVersion){
					return '<span class="switch_span">'+
       			   	'<a href="javascript:void(0)" onclick="startFlowCustomTemplateForTest('+iid+','+serviceId+')">'+
   			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_start"/>'+'启动'+
   			   	'</a>'+
   			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'
	   			   ;
				}else{
					return '<span class="switch_span">'+
	   			   	'<a href="javascript:void(0)" onclick="editFlowCustomTemplateForTest('+iid+','+serviceId+',\''+customName+'\',\''+serviceName+'\','+bussId+','+bussTypeId+','+ menuId +')">'+
	   			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"/>'+'编辑'+
	   			   	'</a>'+
	   			   '</span>&nbsp;&nbsp;&nbsp;&nbsp;'+
	   			   
		   			'<span class="switch_span">'+
       			   	'<a href="javascript:void(0)" onclick="startFlowCustomTemplateForTest('+iid+','+serviceId+')">'+
       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_start"/>'+'启动'+
       			   	'</a>'+
       			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'
	   			   ;
				}
				
			}
		}
	];

	  var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: flowTemplateCustomStore,
		  	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		    dock: 'bottom',
		    displayInfo: true,
		    emptyMsg:'找不到任何记录'
		  });
	  
	  var selModel=Ext.create('Ext.selection.CheckboxModel', {
			checkOnly: true
		});
	  var flowTemplateCustomGrid = Ext.create('Ext.grid.Panel', {
			region: 'center',
			autoScroll: true,
		    store : flowTemplateCustomStore,
		    cls:'customize_panel_back',
		    bbar : pageBar,
		    border:false,
		    columnLines : true,
		    columns : flowTemplateCustomColumns,
		    selModel : selModel,
		    dockedItems : [{
		    	xtype : 'toolbar',
		    	items : ['->',{
			        text: '删除',
			        cls : 'Common_Btn',
			        handler: function() {
			        	var seledCnt = selModel.getCount();
			    		if(seledCnt < 1){
			    			Ext.MessageBox.alert("提示", "请选择要删除的记录！");
			    			return ;
			    		}
			    		var ss = selModel.getSelection();
			    		var ids = new Array();
			    		for ( var i = 0, len = ss.length; i < len; i++) {
			    			ids.push(ss[i].data.iid);
			    		}
			    		Ext.MessageBox.buttonText.yes = "确定"; 
			    		Ext.MessageBox.buttonText.no = "取消"; 
			    		Ext.Msg.confirm("确认删除", "是否删除选中的记录", function(id){
			    			if(id=='yes') {
			    				Ext.Ajax.request({
			                        url: 'deleteFlowCustomTemplate.do',
			                        method: 'POST',
			                        params: {
			                        	ids: ids,
			                            flag: 0
			                        },
			                        success: function(response, options) {
			                            var success = Ext.decode(response.responseText).success;
			                            if (success) {
			                                Ext.MessageBox.show({
			                                    title: "提示",
			                                    msg: '删除成功！',
			                                    buttonText: {
			                                        yes: '确定'
			                                    },
			                                    buttons: Ext.Msg.YES
			                                });
			                                flowTemplateCustomStore.load();
			                            } else {
			                            	Ext.Msg.alert ('提示', "删除失败！");
			                            }
			                        },
			                        failure: function(result, request) {
			                            Ext.MessageBox.show({
			                                title: "提示",
			                                msg: "失败",
			                                buttonText: {
			                                    yes: '确定'
			                                },
			                                buttons: Ext.Msg.YES
			                            });
			                        }

			                    });
			    			}
			    		});
			        }
		    	}]
		    }]
		});
	  function clearQueryWhere(){
		  search_form.getForm().findField("serverName").setValue(''),
		  search_form.getForm().findField("templateName").setValue('')
	    }
		 var mainPanel = Ext.create('Ext.panel.Panel',{
			 renderTo : "flowTemplateManagerForTest_area",
		        width : contentPanel.getWidth(),
			    height :contentPanel.getHeight() - modelHeigth,
		        border : true,
		        layout: 'border',
		        items : [search_form,flowTemplateCustomGrid]
		});
		 
			contentPanel.on ('resize', function (){
				mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
				mainPanel.setWidth (contentPanel.getWidth () );
			});
			
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
			Ext.destroy(mainPanel);
			if(Ext.isIE){
				CollectGarbage(); 
			}
		});
});

function editFlowCustomTemplateForTest(iid, serviceId, customName, serviceName, bussId, bussTypeId,menuId) {
	destroyRubbish(); //销毁本页垃圾
	
	contentPanel.getLoader().load({url:  'flowCustomizedInitScriptService.do',
		params: {
			serviceId: serviceId,
			iid: serviceId,
			customId: iid,
			menuId: menuId,
			customName: customName,
			serviceName:serviceName,
			actionType:'edit',
			bussId:bussId,
			bussTypeId:bussTypeId,
			flag:0,
			rootspace: 'model',
			ifrom : 'flowCustomizedTemplateForTest.do'
		},
		scripts: true});
	
}

function orgDataFromOldDataCustomTemplateForTest (serviceId, oldData) {
    var dataInfo = {};
    var dataMap = {};
    var newData = [];
    $.each(oldData, function(index, value){
		dataMap[parseInt(value["actNo"])] = {
				"data": value,
				"actType": value['actType']
		};
    });
    var isOk = true;
    Ext.Ajax.request({
		url: 'getFlowGraphActions.do',
		method: 'POST',
		async: false,
		params: {
			iid: serviceId,
            flag: 0
		},
		success: function(response, options) {
			var data = Ext.decode(response.responseText);
			$.each(data, function(index, act){
				var actType=act.type;
				if(actType==2) {
					actType = 1;
				}
				if(dataMap.hasOwnProperty(act.id) && dataMap[act.id]['actType']==actType) {
					newData.push(dataMap[act.id]['data']);
				} else {
					if(actType==1) {
						newData.push({
	            			"actNo": act.id,
	            			"actType": 1,
	            			"actName": act.name,
	            			"message": act.scriptContent
	            		});
//						newData.push(act.id + "++!!++" + act.scriptContent + "++!!++++!!++++!!++++!!++++!!++++!!++" + 1 + "++!!++" + act.name);
					} else {
						isOk = false;
						dataInfo['success'] = false;
						dataInfo['errMsg'] = "步骤：" + act.name + " 配置有问题，请更正后重试！";
					}
				}
			});
		},
		failure: function(result, request) {
			isOk = false;
			dataInfo['success'] = false;
			dataInfo['errMsg'] = "获取信息失败";
		}
	});
    if(isOk) {
    	dataInfo['success'] = true;
    	dataInfo['data'] = newData;
    }
    return dataInfo;
}

function startFlowCustomTemplateForTest(iid, serviceId) {
	Ext.MessageBox.buttonText.yes = "确定"; 
	Ext.MessageBox.buttonText.no = "取消"; 
	Ext.Msg.confirm("确认启动", "是否确定启动该模板？", function(id){
		if(id=='yes') {
			var isOk = false;
			Ext.Ajax.request({
    			url: 'validFlowCustomTemplateData.do',
    			method: 'POST',
    			async: false,
    			params: {
    				iid: iid,
    				flag: 0
    			},
    			success: function(response, options) {
    				var success = Ext.decode(response.responseText).success;
    				if(success) {
    					isOk = true;
    				} else {
    					var errorMessage = Ext.decode(response.responseText).message;
    					Ext.Msg.alert('提示', errorMessage);
    				}
    			},
    			failure: function(result, request) {
    				Ext.Msg.alert('提示', '校验模板启动数据信息失败！');
    			}
    		});
			
			if(!isOk) {
				return;
			}
			
			Ext.Ajax.request({
				 url :'startFlowCustomTemplate.do',
					method: 'POST',
					params:{
						iid:iid,
						flag: 0
					},
					success: function ( response, options) 
					{
				        var message = Ext.decode(response.responseText).message;
				        Ext.Msg.alert('提示', message);
					},
					failure: function ( result, request){
						Ext.Msg.alert('提示', "启动失败");
					}
			 });
		}
	});
}