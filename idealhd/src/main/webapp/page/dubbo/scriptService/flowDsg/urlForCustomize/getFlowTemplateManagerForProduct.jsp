<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.common.utils.SessionData" %>
<html>
<head>

<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
<%
boolean execUserSwitch = Environment.getInstance().getScriptExecUserSwitch();
boolean showConfigSwitch = Environment.getInstance().getScriptShowConfigSwitch();
SessionData sessionData = SessionData.getSessionData(request);
String loginUser = sessionData.getLoginName();
%>
var execUserSwitch = <%=execUserSwitch%>;
var showConfigSwitch = <%=showConfigSwitch%>;
var loginUser = '<%=loginUser%>';
var taskType = '<%=request.getAttribute("taskType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomize/getFlowTemplateManagerForProduct.js"></script>
</head>
<body>
<div id="flowTemplateManagerForProduct_area" style="width: 100%;height: 100%"></div>
</body>
</html>