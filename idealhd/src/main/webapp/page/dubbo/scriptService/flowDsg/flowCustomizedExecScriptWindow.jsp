<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean scriptWhiteAgentSwitch = Environment.getInstance().getScriptWhiteAgentSwitch();
	boolean agentExportSwitch = Environment.getInstance().getExportAgentSwitch();
	boolean equipSwitch = Environment.getInstance().getScriptTaskapplyAddAgentSwitch();
%>
<html>
<head>
<script type="text/javascript">
var tempData = {};
<%
String menuid = request.getParameter("menuId");
boolean taskApplyForSPDBSwitch = Environment.getInstance().getScriptTaskApplyAddAgentSwitch();
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
var scriptWhiteAgentSwitch = <%=scriptWhiteAgentSwitch%>;
var agentExportSwitch = <%=agentExportSwitch%>;
//适用平台
var scriptPlatmFrom = '<%=request.getParameter("platmFrom")%>'; 
var iworkItemid_forexport = '<%=request.getParameter("workItemid")%>'; 
var scriptType = '<%=request.getParameter("scriptType")%>'; 
//浦发特殊需求  展示agent数据 特殊样式 开关 
var taskApplyForSPDBSwitch = <%=taskApplyForSPDBSwitch%>;
var agent_store_chosed = Ext.create('Ext.data.Store', {
    model: 'page.dubbo.scriptService.spdb.agent.agentModel',
    proxy: {
    },
    autoLoad: false
});
var agent_store_chosedForSee = Ext.create('Ext.data.Store', {
    model: 'page.dubbo.scriptService.spdb.agent.agentModel',
    proxy: {
    },
    autoLoad: false
});
var equipSwitch = <%=equipSwitch%>;
var chosedAgentIds = new Array();
var chosedAgentFlagArray = new Array();//判断是否Windows和非Windows一起选择
var chosedAgentWinForSPDB = new Ext.window.Window();
var agentColumnsForSPDB = [];
var  checkInumber=false;
var scriptTypeForTaskAudi;// 浦发需求 校验非Python脚本时不允许选择 不同平台的agent 用 
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script async type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/agentModelAndColumnForSPDB.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/basicScript/queryAgentInfoForSPDB.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/flowCustomizedExecScriptWindow.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/fileDownload/jquery.fileDownload.js"></script>
</head>
<body id="hbody">
	<div id="flowCustomizedScriptWindowDiv"></div>
</body>
</html>