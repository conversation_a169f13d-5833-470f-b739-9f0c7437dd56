/*******************************************************************************
 * 模板步骤列表
 ******************************************************************************/
var scriptNameObj;
var scriptNameStore;
var scriptContentObj;
var stepNameObj;
Ext.onReady (function ()
{
	 // 清理主面板的各种监听时间
	/** 树数据Model* */
	Ext.define ('templateModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'instanceName',
		        type : 'string'
		    },{
		        name : 'iid',
		        type : 'long'
		    },{
		        name : 'iserner',
		        type : 'long'
		    },{
		        name : 'iprener',
		        type : 'string'
		    },{
		        name : 'iactName',
		        type : 'string'
		    },{
		        name : 'iscriptName',
		        type : 'string'
		    }
	    ]
	});
	
	/** 树数据源* */
	var templateStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad :true,
	    autoDestroy : true,
	    model : 'templateModel',
	    pageSize : 50,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getTemplateSonList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	templateStore.on ('beforeload', function (store, options)
			{
				var new_params =
				{
						instanceId : parent.iid
				};
				Ext.apply (templateStore.proxy.extraParams, new_params);
			});
	/** 树列表columns* */
	var templateColumns = [
	        {
	            text : '序号',
	            // align : 'center',
	            width : 35,
	            xtype : 'rownumberer'
	        }, 
	        {
	            text : '步骤标识',
	            dataIndex : 'iserner',
	            flex : 1
	        }, 
	        {
	            text : '作业名',
	            dataIndex : 'iactName',
	            flex : 1
	        }, 
	        {
	            text : '依赖',
	            dataIndex : 'iprener',
	            flex : 1
	        }, 
	        {
	            text : '脚本名',
	            dataIndex : 'iscriptName',
	            flex : 1
	        }
	];
	var bsPageBar = Ext.create ('Ext.PagingToolbar',
			{
			    store : templateStore,
			    dock : 'bottom',
			    displayInfo : true,
			    emptyMsg : "没有记录"
			});
	/** 树列表panel* */
	var templateGrid = Ext.create ('Ext.grid.Panel',
	{
		width:'100%',
	    store : templateStore,
	    border : true,
	    columnLines : true,
	    flex : 2,
	    columns : templateColumns,
	    bbar : bsPageBar,
	    collapsible : false
	});
	var middlePanel=Ext.create('Ext.Panel', {
//		title:'提示信息',
		width:'100%',
		height : contentPanel.getHeight ()-125,
		split: true,
//		region: 'south',
		layout:'fit',
		border : false,	
//		titleAlign : 'left',
		items : [
		         templateGrid
	            ]
	});
	
    // 主Panel
    var MainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "mainpanelE",
		width : '100%',
		height : '100%',
//		overflowY:'scroll',
		border : false,
		bodyPadding : 5,
		items : [ middlePanel ]
	});
    contentPanel.on('resize', function() {
    	middlePanel.setWidth('100%');
    	middlePanel.setHeight(contentPanel.getHeight ()-125);
      });
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
});
function forwardRMFlow(iidIn,instanceNameIn){
	  destroyRubbish();
	  contentPanel.getLoader().load (
				{
				    url : 'templateCustomizedInit.do',
				    params :
				    {
				    	iid : iidIn,
				    	instanceName:instanceNameIn
				    },
				    scripts : true
				});
	  if(Ext.isIE){
	      CollectGarbage(); 
	  }
	}