/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSWorkTestCoat;
var isSaveTemplateCkGFSSWorkTestCoat;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	isSaveTemplateCkGFSSWorkTestCoat = Ext.create('Ext.form.field.Checkbox', {
		checked : false,
		boxLabel: '是否保存为流程模板'
	});
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSWorkTestCoat
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
	
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-48,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSWorkTestCoat.do',
	        params: {
	        	flag:flagGFSSWorkTestCoat,
	        	actionType:actionTypeGFSSWorkTestCoat
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSWorkTestCoat.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	
	var saveButton = Ext.create('Ext.Button', {
	    text : '保存',
	   // cls: 'Common_Btn',
	    margin: '0 0 0 5',
	    hidden:(1==showOnlyGFSSWorkTestCoat?true:false),
	    handler : function() {
	    	//先校验是否有运行中的任务
	    	Ext.Ajax.request({
    			url: 'getFlowRunningState.do',
    			method: 'POST',
    			async: false,
    			params: {
    				flowServiceId: iidGFSSWorkTestCoat,
    			},
    			success: function(response, options) {
    				var success = Ext.decode(response.responseText).success;
    				var message = Ext.decode(response.responseText).message;
    				if(success) {
    					saveFlowGFSSWorkTestCoat();//调用保存方法
            		} else {
            			Ext.Msg.alert('提示', message);
            			return;
            		}
    			},
    			failure: function(result, request) {
    				Ext.Msg.alert('提示', '获取信息失败');
    			}
    		});
	       
	      }
	  });
	var viewBasicInfoButton = Ext.create("Ext.Button", {
		//cls: 'Common_Btn',
		text: "基本信息",
		margin: '0 0 0 5',
		disabled : false,
		handler:function(){
			Ext.create('Ext.window.Window', {
	            title: '基本信息',
	            autoScroll: true,
	            modal: true,
	            closeAction: 'destroy',
	            buttonAlign: 'center',
	            draggable: true,
	            resizable: false,
	            width: 500,
	            height: 328,
	            loader: {
	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
	            	params: {
	            		creatorFullName: creatorFullName,
		                bussName: bussName,
		                bussTypeName:bussTypeName,
		                funcDescText: funcDescText,
		                serviceName:serviceName
	            	},
	            	autoLoad: true
	            },
	            dockedItems: [{
	                xtype: 'toolbar',
	                border: false,
	                dock: 'bottom',
	                margin: '0 0 5 0',
	                layout: {pack: 'center'},
	                items: [{
	                    xtype: 'button',
	                    text: '关闭',
	                    cls: 'Common_Btn',
	                    handler: function() {
	                        this.up("window").close();
	                    }
	                }]
	            }]
	        }).show();
		}
	});
	 var backButton = Ext.create('Ext.Button', {
		    text : '返回',
		    margin: '0 0 0 5',
		    //cls: 'Common_Btn',
		    handler : function() {
		        destroyRubbish();
		        contentPanel.getLoader().load({
		          url : 'scriptMonitorForFlowTest.do',
		          params: {
      				'filter_bussId': filter_bussIdGFSSWorkTestCoat,
					'filter_bussTypeId': filter_bussTypeIdGFSSWorkTestCoat,
					'filter_scriptName': filter_scriptNameGFSSWorkTestCoat,
					'filter_serviceName': filter_serviceNameGFSSWorkTestCoat,
					'filter_scriptType': filter_scriptTypeGFSSWorkTestCoat
      				}
		        });
		        if (Ext.isIE) {
		          CollectGarbage();
		        }
		      }
		  });
	 var testButton = Ext.create('Ext.Button', {
		    text : '测试',
		    //cls: 'Common_Btn',
		    margin: '0 0 0 5',
		    handler : function() {
		    	Ext.Msg.confirm("请确认", "是否进行测试？", function(id) {
					if (id == 'yes') {
			        	Ext.Ajax.request({
		        			url: 'getFlowTestData.do',
		        			method: 'POST',
		        			async: false,
		        			params: {
		        				iid: flowIdGFSSWorkTestCoat,
		        				flag: 0
		        			},
		        			success: function(response, options) {
		        				var dataS = Ext.decode(response.responseText).data;
		        				Ext.Ajax.request({
    					    		url :'checkJobConfigIsOK.do',
    					    		method: 'POST',
    					    		async: false,
    					    		params:{
    					    			serviceId:iidGFSSWorkTestCoat,
    					    			data:dataS,
    					    			flag:0
    					    		},
    					    		success: function ( response, options) {
    					    			var success = Ext.decode(response.responseText).success;
    					    			if (success) {
    					    				Ext.Ajax.request({
    					        				url: 'startScriptServiceFlow.do',
    					        				method: 'POST',
    					        				params: {
    					        					serviceId: iidGFSSWorkTestCoat,
    					        					data: JSON.stringify(dataS),
    					        					flag: 0
    					        				},
    					        				success: function(response, options) {
    					        					var success = Ext.decode(response.responseText).success;
    					        					var message = Ext.decode(response.responseText).message;
    					        					if (success) {
    					        						Ext.MessageBox.buttonText.yes = "是";
    					        						Ext.MessageBox.buttonText.no = "否";
    					        						Ext.Msg.confirm("请确认", message+"<br>是否返回“作业测试历史”?", function(id) {
    					        							if (id == 'yes') {
    					        								destroyRubbish();
    					        								contentPanel.getLoader().load({
    					        									url: 'scriptMonitorForFlowTest.do',
    					        									params: {}
    					        								});
    					        								if (Ext.isIE) {
    					        									CollectGarbage();
    					        								}
    					        							}
    					        						});
    					        					} else {
    					        						Ext.Msg.alert('提示', message);
    					        					}
    					        				},
    					        				failure: function(result, request) {
    					        					Ext.Msg.alert('提示', '启动失败');
    					        				}
    					        			});
    					    			} else {
    					    				Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    					    			}
    					    		},
    					    		failure: function ( result, request){
    					    			Ext.Msg.alert('提示', "检查作业配置出现问题！");
    					    		}
    					    	});
		        			},
		        			failure: function(result, request) {
		        				Ext.Msg.alert('提示', '获取信息失败');
		        			}
		        		});
					}
				});
		      }
		  });
	
	 
	//3.1启动 相关控件
	var submitButton = Ext.create("Ext.Button", {
		id : 'startFlowBtn',
	//	cls: 'Common_Btn',
		margin: '0 0 0 5',
	    text: "测试",
	    disabled : false,
	    handler:function(){
	    	startFlowGFSSWorkTestCoat();
	    }
	});
	
	if(actionTypeGFSSWorkTestCoat=='edit') {
		var submitFromPanel = Ext.create('Ext.form.Panel', {
			buttonAlign : 'center',
			width : '100%',
			frame : false,
			layout: {
                type: 'hbox',
                padding:'5',
                align:'top'
            },
            items: [{
                xtype: 'radiogroup',
                anchor: 'none',
                layout: {
                    autoFlex: false
                },
                defaults: {
                    margin: '0 5 0 0'
                },
                cls: 'customer-radio-group',
                items: [
                    {boxLabel: '编辑', name: 'needAgent',id:'needAgent1', checked: true, inputValue: '1'},
                    {boxLabel: '测试', name: 'needAgent',id:'needAgent2', checked: false, inputValue: '2'},
                ],
                listeners: {
                    change: function (field, newValue, oldValue) {
                    	var nowValue = parseInt(newValue['needAgent']);
                    	if(nowValue==1) {
                    		clickHandler(1);
                    		
                    	} else if (nowValue==2) {
                    		clickHandler(2);
                    	}
                    }
                }
            },isSaveTemplateCkGFSSWorkTestCoat,{
                    xtype:'tbspacer',
                    flex:1
                },saveButton, testButton,viewBasicInfoButton,backButton,{
                    xtype:'tbspacer',
                    flex:1
                },{
                    xtype:'tbspacer',
                    width:245
                }]
		});
	} else if(actionTypeGFSSWorkTestCoat=='exec') {
		var submitFromPanel = Ext.create('Ext.form.Panel', {
			buttonAlign : 'center',
			width : '100%',
			frame : false,
			layout: {
                type: 'hbox',
                padding:'5',
                align:'top'
            },
            items: [{
            	 xtype: 'radiogroup',
	                anchor: 'none',
	                layout: {
	                    autoFlex: false
	                },
	                defaults: {
	                    margin: '0 5 0 0'
	                },
	                cls: 'customer-radio-group',
	                items: [
	                    {boxLabel: '编辑', name: 'needAgent',id:'needAgent1', checked: false, inputValue: '1'},
	                    {boxLabel: '测试', name: 'needAgent',id:'needAgent2', checked: true, inputValue: '2'},
	                ],
	                listeners: {
	                    change: function (field, newValue, oldValue) {
	                    	var nowValue = parseInt(newValue['needAgent']);
	                    	if(nowValue==1) {
	                    		clickHandler(1);
	                    		
	                    	} else if (nowValue==2) {
	                    		clickHandler(2);
	                    	}
	                    }
	                }
	            },isSaveTemplateCkGFSSWorkTestCoat,{
                    xtype:'tbspacer',
                    flex:1
                },submitButton,viewBasicInfoButton,backButton,{
                    xtype:'tbspacer',
                    flex:1
                },{
                    xtype:'tbspacer',
                    width:245
                }]
		});
	}
	
	function clickHandler(value){
		if(value==2){
	        destroyRubbish();
	        contentPanel.getLoader().load({
	          url : 'flowCustomizedInitScriptServiceGFSSWorkTestCoat.do',
	          params: {
					iid:iidGFSSWorkTestCoat,
					serviceName:serviceNameGFSSWorkTestCoat,
					actionType:'exec',
					bussId:bussIdGFSSWorkTestCoat,
					bussTypeId: bussTypeIdGFSSWorkTestCoat,
					flowId: flowIdGFSSWorkTestCoat,
					flag:0
				},
				scripts: true});
	        if (Ext.isIE) {
	          CollectGarbage();
	        }
	      
		}
		if(value==1){

	        destroyRubbish();
	        contentPanel.getLoader().load({
	          url : 'flowCustomizedInitScriptServiceGFSSWorkTestCoat.do',
	          params: {
					iid:iidGFSSWorkTestCoat,
					serviceName:serviceNameGFSSWorkTestCoat,
					actionType:'edit',
					bussId:bussIdGFSSWorkTestCoat,
					bussTypeId: bussTypeIdGFSSWorkTestCoat,
					flowId: flowIdGFSSWorkTestCoat,
					flag:0
				},
				scripts: true});
	        if (Ext.isIE) {
	          CollectGarbage();
	        }
		}
	}
	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSWorkTestCoat",
			width : '100%',
			height : contentPanel.getHeight (), 
			autoScroll: true,
			border : false,
			bodyPadding : '5 0 0 0',
			items : [ mainTabs,submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		});
		initGFSSWorkTestCoat();

		function initGFSSWorkTestCoat()
		{
			
		}
		contentPanel.on('resize', function() {
			
		});
});
