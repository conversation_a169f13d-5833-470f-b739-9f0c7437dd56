/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.taskCheck', {
    extend: 'Ext.form.Panel',
    alias: 'widget.taskCheck',

    requires: [
        'Ext.form.ComboBox',
        'Ext.form.TextArea',
        'Ext.button.Button'
    ],

    height: 150,
    title: '',
    frame : true,
	buttonAlign : "left",
	
	layout: 'hbox',
	
	serviceInfo : {},
	initOperBtn: function(n){
		var me = this;
//		n = parseInt(n);
//		var editerName = me.editerName;
//		var jspParms = me.jspParms;
//		if(GRAPHS[editerName]!=undefined)
//			GRAPHS[editerName].controlSidbar('view');
//		if(GRAPHS[jspParms.rootEditer]!=undefined)
//		{
//			GRAPHS[jspParms.rootEditer].actionType = 'view';
//		}
	},
	
    initComponent: function() {
        var me = this;
        var jspParms = me.jspParms;
        var serviceInfo = me.serviceInfo;
        if(jspParms.scriptLevelDisplay==undefined)
        {
        	jspParms.scriptLevelDisplay = '';
        }
        
        function publishBtnFn(){
			 Ext.Msg.confirm("确认", "确定同意执行该作业？", function(id){
				 
				 var url ='scriptExecForOneRecord.do';
				 if(null!=jspParms.planId && jspParms.planId!='' && jspParms.planId!='undefined' && null!=jspParms.scenceId && jspParms.scenceId!='' && jspParms.scenceId!='undefined' && null!=jspParms.stepId && jspParms.stepId!='' && jspParms.stepId!='undefined'){               
					 url = 'scriptExecForEmerOneRecord.do';
				 }
				 if(id=='yes'){
					 Ext.Ajax.request ({
						 url : url,
						 method : 'POST',
						 params :
						 {
							 iworkItemid: jspParms.iworkItemid,
							 iplanId:(jspParms.planId==null || jspParms.planId==undefined )?'':jspParms.planId
						 },
						 success : function (response, opts)
						 {
							 var success = Ext.decode (response.responseText).success;
							 if(success && jspParms.isScriptConvertToFlow=='true') {
								 Ext.Ajax.request ({
									 url : 'scriptExecForScriptConvertFlowToOrgData.do',
									 method : 'POST',
									 params :
									 {
										 iworkItemid:jspParms.iworkItemid,
										 serviceId: jspParms.iid 
									 },
									 success : function (response, opts)
									 {
										 
									 }
								 });
							 }
	   					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
	   					    {
	   					    	if(jspParms.from !=66) {
	   					    		messageWindow.setWidth(contentPanel.getWidth ());
	   					    		messageWindow.setHeight(contentPanel.getHeight ());
	   					    		messageWindow.center();
	   					    		messageWindow.getLoader ().load (
	   									{
	   									    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
	   									    autoLoad : true,
	   									    scripts : true
	   									});
	   									messageWindow.setTitle ('待办事项');
	   					    	}else  { // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
						    		messageWindow_ssq.close();
//						    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
					    	    }
//	   					    	messageWindow_ssq.close();
	   					    });
						 }
					 });
				 }
			 });
			 
			 
		 
        }
        
        function nowayBtnFn(){
		    	var scriptAudiFrom = 1;
		    	if(jspParms.actionType=='dbcheckForExec') {
		    		scriptAudiFrom = 2;
		    	}
		    	Ext.MessageBox.wait ("数据处理中...", "进度条");
				var backI = backInfo.getValue();
				if (fucCheckLength (backI.trim ()) < 1)
				{
					Ext.Msg.alert ('提示', "请输入打回原因!");
					return;
					
				}
				if (!illegalCharPassForTextArea (backI))
				{
					Ext.MessageBox.alert ("提示", "打回原因只能由汉字、字母、数字、下划线、冒号以及减号组成！");
					return;
				}
				if (fucCheckLength (backI.trim ()) > 4000)
				{
					Ext.Msg.alert ('提示', "打回原因长度最大为4000个字符!");
					return;
				}
				Ext.Ajax.request (
				{
				    url : 'operWorkitemByiidForSsPublish.do',
				    method : 'POST',
				    params :
				    {
				        istateForQuery: 2,
				        scriptAudiFrom: scriptAudiFrom,
				        iidForQuery: jspParms.iworkItemid,
				        ibackInfo: backI
				    },
				    success : function (response, opts)
				    {
					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
					    {
					    	if(jspParms.from==1) {
					    		messageWindow1.close();
					    		destroyRubbish(); //销毁本页垃圾
					    		contentPanel.getLoader().load({
					    			url: 'pandect1.do',
					    			scripts: true});
					    	} else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
					    		messageWindow_ssq.close();
//					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
				    	    }else {
					    		messageWindow.getLoader ().load (
										{
										    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
										    autoLoad : true,
										    scripts : true
										});
										messageWindow.setTitle ('待办事项');
					    	}
//					    	messageWindow_ssq.close();
					    	
					    });
				    }
				});
        } 
        
        function backToDbCheckBtnFn(){
	    	if(jspParms.from==1) {
	    		messageWindow1.close();
	    	} else {
				messageWindow.getLoader ().load (
						{
							url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
							autoLoad : true,
							scripts : true
						});
				messageWindow.setTitle ('待办事项');
			}
//        	messageWindow_ssq.close();
        }
        function viewBasicInfoBtnFn(serviceInfo){
   				Ext.create('Ext.window.Window', {
   		            title: '基本信息',
   		            autoScroll: true,
   		            modal: true,
   		            closeAction: 'destroy',
   		            buttonAlign: 'center',
   		            draggable: true,
   		            resizable: false,
   		            width: 500,
   		            height: 328,
   		            loader: {
   		            	url: 'page/dubbo/fragment/_basicInfo.jsp',
   		            	params: {
   		            		creatorFullName: 	serviceInfo.creatorFullName,
   			                bussName: 			serviceInfo.bussName,
   			                bussTypeName:		serviceInfo.bussTypeName,
   			                funcDescText: 		serviceInfo.funcDescText,
   			                serviceName:		serviceInfo.serviceName
   		            	},
   		            	autoLoad: true
   		            },
   		            dockedItems: [{
   		                xtype: 'toolbar',
   		                border: false,
   		                dock: 'bottom',
   		                margin: '0 0 5 0',
   		                layout: {pack: 'center'},
   		                items: [{
   		                    xtype: 'button',
   		                    text: '关闭',
   		                    cls: 'Common_Btn',
   		                    handler: function() {
   		                        this.up("window").close();
   		                    }
   		                }]
   		            }]
   		        }).show();
   			}
        
        Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
		var recheckAuditorStore = Ext.create('Ext.data.Store', {
		    autoLoad: true,
		    model: 'AuditorModel',
		    proxy: {
		      type: 'ajax',
		      url: 'getPublishRecheckAuditorList.do',
		      reader: {
		        type: 'json',
		        root: 'dataList'
		      }
		    }
		  });
		
		recheckAuditorStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		iworkItemid : jspParms.workItemid
		    };
		    Ext.apply(recheckAuditorStore.proxy.extraParams, new_params);
	    });
		
    	
   	 var levelStore = Ext.create('Ext.data.Store', {
   		    fields: ['iid', 'scriptLevel'],
   		    data : [
   		        {"iid":"0", "scriptLevel":"白名单"},
   		    	{"iid":"1", "scriptLevel":"高级风险"},
   		    	{"iid":"2", "scriptLevel":"中级风险"},
   		    	{"iid":"3", "scriptLevel":"低级风险"}
   		    ]
   		});
   	 
   	 var scriptLevelCb = Ext.create('Ext.form.field.ComboBox', {
   	        name: 'scriptLevel',
   	        labelWidth: 85,
   	        width : '100%',
   	        queryMode: 'local',
   	        fieldLabel: '风险级别',
   	        labelAlign:'right',
   	        displayField: 'scriptLevel',
   	        valueField: 'iid',
   	        editable: false,
   	        value: jspParms.scriptLevelDisplay,
   	        emptyText: '--请选择风险级别--',
   	        store: levelStore
   	    });
   		
   	 var noWayButton = Ext.create('Ext.Button', {
   		    text : '打回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    hidden:jspParms.from==100||isOnlyShow!=1?true:false,
   		    handler : nowayBtnFn
   	  });
   	 
   	 var backToDbCheckButton = Ext.create('Ext.Button', {
   		    text : '返回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    hidden:jspParms.from==100 || jspParms.from ==66?true:false,
   		    handler : backToDbCheckBtnFn
   	  });
   	 
   	 var viewBasicInfoButton = Ext.create("Ext.Button", {
   			text: "基本信息",
   			disabled : false,
   			handler: function () {
   				viewBasicInfoBtnFn(serviceInfo)
   			}
   		});
   	 var publishButton = Ext.create('Ext.Button', {
	    text : '同意执行',
	    margin : '0 0 0 5',
	    textAlign : 'center',
	    hidden:jspParms.from==100||isOnlyShow!=1?true:false,
	    handler : publishBtnFn
	  });
   	 
   	var cancelButton = Ext.create('Ext.Button',{
        text: '撤销',
        cls: 'Common_Btn',
        hidden:isOnlyShow==2?false:true,
        //hidden:from_rspe==100?true:false,
        handler: function(){
        	Ext.Msg.confirm("请确认", "是否撤销？", function(button, text) {
					if (button == "yes") {
						teminate(iworkItemid,1);
					}
				});
        }
    })
   	 
    function teminate(iid,iaudiType) {
    	Ext.Ajax.request (
		{
		    url : 'operWorkitemByiidForSsPublish.do',
		    method : 'POST',
		    params :
		    {
		        istateForQuery : 8,
		        scriptAudiFrom:iaudiType, // 执行审核
		        iidForQuery : iid
		    },
		    success : function (response, opts)
		    {
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message,function(){
			    	messageWindow_ssq.close();
			    });
		    },
		    failure : function(result, request) {
			    Ext.Msg.alert('提示', "操作失败！");
		    }
		});
	}
    
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea', {
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        flex: 1,
	        labelAlign:'right',
	        padding: '0 2 0 8',
	        width: 480,
	        labelWidth: 60,
	        labelSepartor : "：",
	        readOnly: jspParms.actionType=='dbbackForExec',
	        value: jspParms.backInfo
		});
		var isTimerTaskCheck = Ext.create('Ext.form.field.Checkbox', {
        	checked : jspParms.isTimetask==1?true:false,
        	fieldLabel: '定时任务',
        	labelAlign : 'right',
        	labelWidth : 60,
        	readOnly:true,
    		margin:'5 0 5 0',
    		hidden: !(jspParms.isScriptConvertToFlow=='true')
        });
		
        var execTimeCheck = new Ext.form.TextField ({
		    fieldLabel : '执行时间',
		    labelWidth : 60,
		    labelAlign : 'right',
		    width: 200,
		    readOnly:true,
		    value:jspParms.taskTime,
		    hidden: !(jspParms.isScriptConvertToFlow=='true')
		});
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
    var execStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecUserList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
    var execUser = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "执行人",
	    store: execStore,
	    queryMode: 'local',
	    width: 200,
	    displayField: 'fullName',
	    valueField: 'loginName',
	    labelWidth : 60,
	    editable : false,
	    readOnly:true,
	    hidden:!(jspParms.execUserSwitch=='true'),
		labelAlign : 'right'
	  });
    execStore.load({
	    callback : function (records, operation, success)
	    {
	    	execUser.setValue (jspParms.performUser);
	    }
    });
        var execDesc = Ext.create('Ext.form.field.TextArea', {
	        fieldLabel: '执行描述',
	        labelWidth: 60,
	        padding: '0 2 0 8',
	       // height: 60,
	        maxLength: 2000,
	        value: jspParms.publishDesc,
	        width: 480,
	        readOnly:true,
	        autoScroll: true
	    });
    	/** butterfly单号 */
    	var butterflyversionShow = Ext.create ('Ext.form.DisplayField',
    	{
    	    fieldLabel : '单号',
    	    labelWidth : 30,
    	    hidden : !(jspParms.execUserSwitch=='true'),
    	    value : butterflyV
    	});
        Ext.applyIf(me, {
            items: [{
	        	border: false,
	            flex: 2,
	            layout: 'vbox',
	            items: [isTimerTaskCheck,execTimeCheck,execUser]
	        },{
	            border: false,
	            flex: 4.5,
	            layout: 'vbox',
	            items: [execDesc]
	        },{
	            border: false,
	            flex: 4.5,
	            layout: 'vbox',
	            items: [backInfo]
	        }],
            buttons: [viewBasicInfoButton,publishButton,noWayButton,cancelButton,backToDbCheckButton]
        });
    	
        
        me.callParent(arguments);
        me.initOperBtn(25);
    }
});