Ext.onReady(function() {
	
	if(Ext.isIE){
		CollectGarbage();
	}else{
		destroyRubbish();
	}
	//接收上下文传递的参数，并销毁临时对象
	var parmObj = tempData;
	delete tempData; // 在jsp中声明的该变量，此刻已经置空，避免影响其他页面。如果此处出现异常提示，需要做后续确认：如果jsp中没有该对象，则删除这段代码，如果jsp中存在该对象，则忽略异常提示。
	
	if(parmObj.scope == undefined)
	{
		parmObj.scope = 0;
	}
	
	if(parmObj.scope != 0 && parmObj.winDivID != undefined && parmObj.winDivID != '')
	{
		parmObj.divID = parmObj.winDivID;
	}
	
	if(parmObj.iscenceId == undefined)
	{
		parmObj.iscenceId = '';
	}
	
	var namespace = 'scriptEditor_' + parmObj.divID;
	if(parmObj.rootEditer == undefined)
	{
		parmObj.rootEditer = namespace;
	}
	
	if (parmObj.oldJobId > 0 || parmObj.serviceId > 0)
	{
		parmObj.load = true;
	}
	
	var graphHeight = parmObj.height ;
	var CREATOR = {
		iid : parmObj.serviceId,
		oldJobId : parmObj.oldJobId,
		customId : parmObj.customId,
		iworkItemid : parmObj.iworkItemid,
		showMFBar : false,
		namespace: namespace,
		configWindow : null,
		readOnly : false,
		currentCell : null,
		graphPanel : null,
		destroy : function() {
			if (this.configWindow) {
				this.configWindow.destroy();
			}
			this.graphPanel.destroy();
		},
		editorUi : null,
		loadXML : parmObj.load,
		actionType : parmObj.actionType,
//		offsetPath : parmObj.offsetPath,
		parentMxIid : parmObj.parentMxIid
	};
	
	if (GRAPHS.hasOwnProperty(namespace)) {
		GRAPHS[namespace].destroy();
	}
	
	GRAPHS[namespace] = CREATOR;
	
	CREATOR.parmObj = {
			bussId : '',
			bussTypeId : '',
			instanceName : ''
	};
	
	CREATOR.controlSidbar = function(action){
		if('view1' == action || 'view' == action||'exec' == action||'test' == action||'model' == action||'audi' == action||'dbbackForExec' == action||'dbcheckForExec'==action)
		{
			CREATOR.tmp_hsplitPosition = CREATOR.editorUi.hsplitPosition;
			CREATOR.tmp_hsplit_width = CREATOR.editorUi.hsplit.style.width;
			CREATOR.editorUi.hsplit.style.width = '0px';
			CREATOR.editorUi.hsplitPosition = 0;
			CREATOR.editorUi.refresh();
		}else{
			if(CREATOR.tmp_hsplitPosition == undefined)
				CREATOR.tmp_hsplitPosition =  208;
			if(CREATOR.tmp_hsplit_width == undefined)
				CREATOR.tmp_hsplit_width = 12;
			CREATOR.editorUi.hsplit.style.width = CREATOR.tmp_hsplit_width;
			CREATOR.editorUi.hsplitPosition = CREATOR.tmp_hsplitPosition;
			CREATOR.editorUi.refresh();
		}
	
	};
	
	// 获取界面图形对应的xml内容
	CREATOR.getXML = function() {
		var xml = "";
		var loadID = parmObj.serviceId;
		if(parmObj.oldJobId!=undefined)
			loadID = parmObj.oldJobId;
		
		Ext.Ajax.request({
			url : 'getFlowXmlScriptServiceForCreator.do',
			params : { instanceID : loadID, flag : parmObj.flag },
			method : 'POST',
			async : false,
			success : function(response, options) {
				if (Ext.decode(response.responseText).success) {
					xml = Ext.decode(response.responseText).xml;
				}
			},
			failure : function(result, request) {
				CREATOR.loadXML = false;
			}
		});
		return xml;
	};
				
	CREATOR.callbackWindw = function() {};
	
	CREATOR.init = function(e){
		e.setPageVisible(false);
		try
    	{	
			if(CREATOR.iid > 0 || CREATOR.oldJobId > 0)
			{
				CREATOR.loadXML = true;

				
				if(CREATOR.customId > 0 && Object.keys(actStartInfo[parmObj.rootEditer]).length==0)
				{
					if(Ext.isEmpty(CREATOR.parentMxIid))
					{
						Ext.Ajax.request({
			                url: 'getFlowCustomTemplateData.do',
			                method: 'POST',
			                params: {
			                	iid: CREATOR.customId,
			                    flag: parmObj.flag
			                },
			                success: function(response, options) {
			                    var dataS = Ext.decode(response.responseText).data;
			                    if(dataS !=''){
//			                		if(Ext.Object.isEmpty(actStartInfo[parmObj.rootEditer]))
			                			actStartInfo[parmObj.rootEditer] = JSON.parse(dataS);
			                	}else{
			                		actStartInfo[parmObj.rootEditer] = '';
			                	}
			                	
			                },
			                failure: function(result, request) {
			                	Ext.Msg.alert ('提示', '获取模板数据失败!');
			                }
			            });
					}
					
				}else if(CREATOR.iworkItemid>0 && Object.keys(actStartInfo[parmObj.rootEditer]).length==0)
				{
					Ext.Ajax.request({
			            url: 'getDbcheckForExecData.do',
			            method: 'POST',
			            params: {
			            	workItemId: parseInt(CREATOR.iworkItemid)   
			            },
			            success: function(response, options) {
			                var success = Ext.decode(response.responseText).success;
			                if(success) {
			                	var dataS = Ext.decode(response.responseText).data;
			                	if(dataS !=''){
		                			actStartInfo[parmObj.rootEditer] = JSON.parse(dataS);
			                	}else{
			                		actStartInfo[parmObj.rootEditer] = '';
			                	}
			                	
			                } else {
			                	Ext.MessageBox.show({
			                        title: "提示",
			                        msg: "获取双人复核作业执行审核参数信息失败",
			                        buttonText: {
			                            yes: '确定'
			                        },
			                        buttons: Ext.Msg.YES
			                    });
			                }
			            },
			            failure: function(result, request) {
			                Ext.MessageBox.show({
			                    title: "提示",
			                    msg: "获取双人复核作业执行审核参数信息失败",
			                    buttonText: {
			                        yes: '确定'
			                    },
			                    buttons: Ext.Msg.YES
			                });
			            }

			        });
				} else{
					if(actStartInfo[parmObj.rootEditer]==undefined)
						actStartInfo[parmObj.rootEditer] = {};
				}
				
				
				
				
				var graph = CREATOR.editorUi.editor.graph;
				var model = graph.getModel();
				var root = model.getRoot();
				var count = model.getChildCount (root);
				
				for (var i = 0; i < count; i++)
				{
					var cells = root.getChildAt (i);
					var counts = cells.getChildCount ();
					for (var j = 0; j < counts; j++)
					{
						var cellss = cells.getChildAt (j);
						if (!model.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
						{
							if(CREATOR.parentMxIid==undefined||CREATOR.parentMxIid=='')
								cellss.mxIid = CREATOR.iid+":"+cellss.id;
							else 
								cellss.mxIid = CREATOR.parentMxIid + '-' + CREATOR.iid + ":" + cellss.id;
							
							if(!actStartInfo[parmObj.rootEditer].hasOwnProperty(cellss.mxIid)) {
//								if(cellss.bean.adapter=='scriptServiceStyle') {
//									actStartInfo[parmObj.rootEditer][cellss.mxIid] = {
//											'actNo': cellss.id,
//											'actType': 0,
//											'actName': cellss.value,
//											'isShutdown': cellss.isShutdown
//									};
//								} else 
									if(cellss.bean.adapter=='usertaskStyle') {
									actStartInfo[parmObj.rootEditer][cellss.mxIid] = {
											'actNo': cellss.id,
											'actType': 1,
											'actName': cellss.value,
											'message': cellss.ireminfo
									};
								} else if(cellss.bean.adapter=='callflowStyle') {
									actStartInfo[parmObj.rootEditer][cellss.mxIid] = {
											'actNo': cellss.id,
											'actType': 2,
											'actName': cellss.value
									};
								}
							}
						}
					}
				}
			}else // 增加开始节点和结束节点
			{
				var hasCells = e.editor.graph.getAllCells();
				if (hasCells.length == 0 && CREATOR.iid <= 0)
				{
					var style_pre = 'shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;imageAspect=1;aspect=fixed;image=/editor/images/adaptor';
					var beginCell = e.editor.graph.insertVertex(e.editor.graph.getDefaultParent(), null, '开始', 1, 1, 52, 61, style_pre + '/01.png');
					beginCell.bean = e.defaultBeans[0];
					beginCell.adapter = e.defaultBeans[0].adapter;
					var endCell = e.editor.graph.insertVertex(e.editor.graph.getDefaultParent(), null, '结束', 200, 200, 52, 61, style_pre + '/02.png');
					endCell.bean = e.defaultBeans[1];
					endCell.adapter = e.defaultBeans[1].adapter;
					hasCells = [ beginCell, endCell];
					e.editor.graph.fireEvent(new mxEventObject('cellsInserted', 'cells', hasCells));
				}
			}
			
			
    	}finally
    	{
    		e.editor.graph.getModel().endUpdate();
    	}
	};
	// 保存图形
	CREATOR.saveFlow = function(xml,parms) {
		parms.xmlString = xml;
		var serviceId = parms.serviceId;
		Ext.Ajax.request ({
		    url : 'mxgraphSaveForFlowScriptService.do',
		    params : parms,
		    method : 'POST',
		    async : false,
		    success : function (response, options) {
			    if(Ext.decode (response.responseText).success==true) {
			    	serviceId = Ext.decode (response.responseText).serviceId;
			    } else {
			    	Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    	serviceId = -1;
			    }
			    
		    },
		    failure : function (result, request) {
		    	Ext.Msg.alert ('提示', "保存作业失败！");
		    	serviceId = -2;
		    }
		});
		
		this.iid = serviceId;
		return serviceId;
		
	}
			
	CREATOR.back = function()
	{
		var iurl = 'projectPropertyEdit.do?projectId='+parmObj.projectId + '&flowId='+parmObj.flowId + '&projectTypeId=' +parmObj.projectTypeId;
		contentPanel.getLoader ().load (
		{
		    url :  iurl,
		    scripts : true
		});
	}
			
	CREATOR.openActConfig = function(cell) {
		CREATOR.currentCell = cell; // 将当前双击的cell节点，设置为当前GRAPH对象的全局当前节点
		
		if(cell.bean.pages==undefined) // 如果双击节点的结构定义中没有设置任何page，说明该节点不是entegor的web定义节点
			return;
		
		var width = 700;
		var height = 495;
		
		var url = "page/grapheditor/webstudio/act/actconfig.jsp?namespace=" + CREATOR.namespace;
		var params = parmObj;
		
		if(cell.bean.adaptertype=='2')
		{
			var needAgentValue = 3;
			
			//Ext.getCmp('flowCustomizedMainDiv_id_10025').down('radiogroup').getValue().needAgent
			if(Ext.getCmp(parmObj.parentId)!=undefined)
			{
				var radiogroup = Ext.getCmp(parmObj.parentId).down('radiogroup');
				if(radiogroup!=undefined)
					needAgentValue = radiogroup.getValue().needAgent;
			}
			
			width = contentPanel.getWidth();
//			if(params.height == undefined)
			height = contentPanel.getHeight();
//			else
//				height = parseInt(params.height);
			
			var operType = '';
			
			params.submitHide = 'true';
			if(GRAPHS[parmObj.editerName].actionType == 'exec'||GRAPHS[parmObj.editerName].actionType == 'test'||GRAPHS[parmObj.editerName].actionType == 'dbcheckForExec')
			{
				operType = cell.bean.pages[0].configurl.exec;
				params.actionType = GRAPHS[parmObj.editerName].actionType;
//				params.height = params.height - 40; 
			}else if(GRAPHS[parmObj.editerName].actionType == 'model')
			{
				operType = cell.bean.pages[0].configurl.model;
				params.actionType = 'model';
//				params.height = params.height - 40;
			}else if(GRAPHS[parmObj.editerName].actionType == 'edit'||GRAPHS[parmObj.editerName].actionType == 'create')
			{
				console.log(needAgentValue);
				if(parmObj.scope == 0)
				{
					operType = cell.bean.pages[0].configurl.edit;
					params.actionType = 'edit';
				}else{
					operType = cell.bean.pages[0].configurl.view;
					params.actionType = 'view';
				}
			}else if(GRAPHS[parmObj.editerName].actionType == 'view')
			{
				operType = cell.bean.pages[0].configurl.view;
				params.actionType = 'view';
			}else if(GRAPHS[parmObj.editerName].actionType == 'audi')
			{
				operType = cell.bean.pages[0].configurl.exec;
				params.actionType = 'audi';
			} 
			
			
			url = "page/grapheditor/webstudio/act/scriptconfig.jsp";
			if(operType.width != undefined)
				width = operType.width;
			if(operType.height != undefined)
				height = operType.height;
			url = url 	+ '?namespace=' + CREATOR.namespace 
						+ '&actType=' + cell.bean.actType 
						+ '&needAgentValue=' + needAgentValue 
						+ '&parentMxIid=' + cell.mxIid 
						+ '&actionType=' +  parmObj.actionType;
			params.needAgentValue = needAgentValue;
		}
		
		
		params.iid = cell.scriptId;
		params.winDivID = params.divID + '_' + cell.id 
		params.isShowInWindow = 1;
			
			
		CREATOR.configWindow = Ext.create('Ext.window.Window', {
			title : '活动配置 - ' + cell.value,
			autoScroll : true,
			cell : cell,
			modal : true,
			closeAction : 'destroy',
			buttonAlign : 'center',
			width : width,
			height : height,
			loader : {
				url : url,
				params: params,
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		});
		CREATOR.configWindow.on({
			close : function(panel, eOpts) {
//				console.log('panel.cell,panel.cell.value:', panel.cell,panel.cell.value);
				if(CREATOR.editorUi)
					CREATOR.editorUi.editor.graph.cellLabelChanged(panel.cell,panel.cell.value);
			}
		})
		CREATOR.configWindow.show();
	}
	var ihtml = '<iframe name="webStudio" src="/editor/webFlow.jsp?namespace='+CREATOR.namespace
		+'&creator_projectId=' + parmObj.projectId 
		+ '&creator_flowId=' + parmObj.flowId 
		+ '&creator_xmlId=' + parmObj.serviceId 
		+ '&projectTypeId=' + parmObj.projectTypeId 
		+ '&action=' + parmObj.actionType 
		+ '&editEnable=true' 
		+ '" width="100%" height="'+graphHeight+'px" frameborder="no">';
	
	var padding = 5;
	CREATOR.graphPanel = Ext.widget('panel',{
		width : '100%',
		region : "center",
		layout : "fit",
		height : parseInt(graphHeight) - padding ,
		plain : true,
		defaults : {
			autoScroll : true,
			bodyPadding : padding
		},
		html : ihtml
	});

	CREATOR.dblClick = function(evt, cell) {
		console.log('dblClick:');
		CREATOR.openActConfig(cell);
	}
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptEditorDiv_" + parmObj.divID,
		width : '100%',
		height : parseInt(graphHeight) - padding,
		autoScroll : true,
		layout : 'border',
		border : true,
		bodyPadding : padding,
		items : [ CREATOR.graphPanel ]
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
			function(obj, options, eOpts) {
				// Ext.destroy(MainPanel);
				if (Ext.isIE) {
					CollectGarbage();
				}
			});
//	/** 窗口尺寸调节* */
//	contentPanel.on('resize', function() {
//		mainPanel.setWidth(contentPanel.getWidth());
//		mainPanel.setHeight(contentPanel.getHeight());
//	});
});