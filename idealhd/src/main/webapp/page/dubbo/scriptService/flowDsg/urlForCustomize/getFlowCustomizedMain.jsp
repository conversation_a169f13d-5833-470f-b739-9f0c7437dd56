<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.Enumeration"%>
<%@ page import="com.ideal.ieai.commons.Constants"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<html>
<head>
<script type="text/javascript">
var tempData = {};
var iworkItemid; //cron表达式公共方法需要的参数
var dbType = '<%=Constants.IEAI_SCRIPT_SERVICE%>';
<%
String menuid = request.getParameter("menuId");
String divID = request.getParameter("divID");
if(divID==null)
{
    divID = menuid;
}
String winDivID = request.getParameter("winDivID");
if(winDivID!=null&&!"".equals(winDivID))
{
    divID = winDivID;
}
Enumeration<String> paramNames = request.getParameterNames();
while( paramNames.hasMoreElements() )
{
    String paramName = paramNames.nextElement();
%>
	tempData.<%=paramName%> = '<%=request.getParameter(paramName)%>';
<%
};
%>
tempData.divID = '<%=divID%>';

// var attributeData = {};
<%
String regex = "^[a-zA-Z0-9,\\?\\*/ \u4e00-\u9fa5]+$";
Enumeration<String> attributeNames = request.getAttributeNames();
while(attributeNames.hasMoreElements())
{
    String attributeName = attributeNames.nextElement();
    if(attributeName.matches(regex))
    {
        String value = request.getAttribute(attributeName).toString();
        System.out.println(attributeName + ":" +value);
        if(value.matches(regex))
        {
            System.out.println(attributeName + ":" +value);
%>
<%-- 			if(tempData.<%=attributeName%> == undefined||tempData.<%=attributeName%> == ''||tempData.<%=attributeName%> == 'null') --%>
// 			
<%-- 				if('<%=value%>' != '' && '<%=value%>' == 'null') --%>
					tempData.<%=attributeName%> = '<%=value%>';
// 			
<%
			if("divID".equals(attributeName)&&divID==null)
			{
			    divID = value;
			}
        }
    }
};
System.out.println(" divID :" +divID);
%>
tempData.publishDesc='<%=request.getAttribute("publishDesc")==null?"":request.getAttribute("publishDesc") %>';
tempData.backInfo='<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo") %>';
var butterflyV = '<%=request.getAttribute("butterflyversion") %>';
if(butterflyV==null||butterflyV=='null'){
	butterflyV = "";
}
var taskType = '<%=request.getParameter("taskType") %>';
var version_flag = '<%=request.getAttribute("version_flag") %>';
var iid = '<%=request.getAttribute("iid") %>';
var customId = '<%=request.getAttribute("customId") %>';
var iServiceId = '<%=request.getAttribute("iServiceId") %>';
var isImmediate='<%=ServerEnv.getInstance().getBooleanConfig(Environment.DOUBLE_CHECK_EMER_PLANSTART_IMMEDIATE_SWITCH, false)%>';
</script>

<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/scriptUtils.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/saveWin.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/configDoubleCheckInfoWin.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/taskBack.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/taskCheck.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/releaseCheck.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/audiFormPanel.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/submitFormPanel.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/submitFormPanelDoubleCheck.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/urlForCustomize/getFlowCustomizedMain.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/planFormPanel.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/scriptGraph/warnSure.js"></script>
<script type="text/javascript">
	var projectFlag = '<%=request.getAttribute("projectFlag")==null?"0":request.getAttribute("projectFlag")%>';
</script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id='flowCustomizedMainDiv_<%=divID%>' style="width: 100%;height: 100%"></div>
</body>
</html>