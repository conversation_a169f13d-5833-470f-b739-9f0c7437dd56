/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.saveWin', {
    extend: 'Ext.window.Window',

    title: '填写基本信息',
    autoScroll: true,
    modal: true,
    closeAction: 'hide',
    buttonAlign: 'center',
    draggable: true,
    resizable: false,
    width: 500,
    reqText : 'save',
    height: 360,
    
    initComponent: function() {
        var me = this;

        var serviceInfo = me.serviceInfo;
        var jspParms = me.jspParms;
        var editerName = me.editerName;
        var fitRadioGroup = me.fitRadioGroup;
        
        var bussData = Ext.create('Ext.data.Store', {
    		fields : [ 'iid', 'bsName' ],
    		autoLoad : true,
    	    proxy :
    	    {
    	        type : 'ajax',
    	        url : 'bsManager/getBsAll.do',
    	        reader :
    	        {
    	            type : 'json',
    	            root : 'dataList'
    	        }
    	    }
    	});
        
    	var bussCbOutSide = Ext.create('Ext.form.field.ComboBox', {
    		name : 'bussId',
    		queryMode : 'local',
    		fieldLabel : '一级分类',
    		padding : '0 5 0 0',
    		displayField : 'bsName',
    		valueField : 'iid',
    		editable : false,
    		emptyText : '--请选择一级分类--',
    		store : bussData,
    		listeners: {
    			change: function() { // old is keyup
    	            bussTypeCbOutSide.clearValue();
    	            bussTypeCbOutSide.applyEmptyText();
    	            bussTypeCbOutSide.getPicker().getSelectionModel().doMultiSelect([], false);
    	            bussTypeData.load({params:{fk: this.value}});
    	        }
    		}
    	});
        
        /** 工程类型下拉框* */
    	var bussTypeData = Ext.create('Ext.data.Store', {
    		fields : [ 'sysTypeId', 'sysType' ],
    		autoLoad : false,
    	    proxy : {
    	        type : 'ajax',
    	        url : 'bsManager/getBsTypeByFk.do',
    	        reader :
    	        {
    	            type : 'json',
    	            root : 'dataList'
    	        }
    	    }
    	});
    	var bussTypeCbOutSide = Ext.create('Ext.form.field.ComboBox', {
    		name : 'bussTypeId',
    		padding : '0 5 0 0',
    		queryMode : 'local',
    		fieldLabel : '二级分类',
    		displayField : 'sysType',
    		valueField : 'sysTypeId',
    		editable : false,
    		emptyText : '--请选择二级分类--',
    		allowBlank :false,
    		store : bussTypeData
    	});
    	
    	// 分类如果加载
    	bussData.on('load', function() { 
    		 if (serviceInfo.bussId>0)
    		{
    			 bussCbOutSide.setValue(serviceInfo.bussId);  
    			 bussCbOutSide.fireEvent ('select');
    		}
    	});
    	
    	bussTypeData.on('load', function(me, records, successful, eOpts) { 
    		if (serviceInfo.bussTypeId>0)
    		{
    			$.each(records, function(index, record){
    				if(parseInt(record.get('sysTypeId'))==serviceInfo.bussTypeId) {
    					bussTypeCbOutSide.setValue(serviceInfo.bussTypeId);  
    				}
    			});
    		}
    	});
    	
    	var funcDescOutSide = Ext.create('Ext.form.field.TextArea', {
            name: 'funcDesc',
            fieldLabel: '功能说明',
            displayField: 'funcDesc',
            emptyText: '请输入功能说明...',
            labelWidth: 70,
            height: 116,
            value: serviceInfo.funcDescText,
            autoScroll: true
        });
    	
    	var instanceNameObjOutSide = Ext.create('Ext.form.field.Text', {
    		name : 'serviceName',
    		padding : '0 5 0 0',
    		fieldLabel : '服务名称',
    		value: serviceInfo.serviceName
    	});
        
        var queryForm = Ext.create('Ext.form.Panel', {
            border: false,
            frame: true,
            width: '100%',
            layout: 'form',
            fieldDefaults: {
                labelWidth: 60,
                labelAlign: "left",
                width: '100%'
            },
            items: [bussCbOutSide, bussTypeCbOutSide, instanceNameObjOutSide, funcDescOutSide]
        });
        
        function baseInfoIsOk () {
			var bussId = bussCbOutSide.getValue ();
			var bussTypeId = bussTypeCbOutSide.getValue ();
			var instanceName = instanceNameObjOutSide.getValue ();
			var funcD = funcDescOutSide.getValue ();
			var baseInfoIsOk = true;
			var errorMsg = "";
			if (bussId == null)
			{
				baseInfoIsOk = false;
				errorMsg = "请选择一级分类!";
			} else if (bussTypeId == null)
			{
				baseInfoIsOk = false;
				errorMsg = "请选择二级分类!";
			} else if (instanceName.trim() == '')
			{
				baseInfoIsOk = false;
				errorMsg = '请填写服务名称!';
			} else if (funcD.trim() == '') {
				baseInfoIsOk = false;
				errorMsg = '请填写功能说明!';
	        }
			return {
				'isOk': baseInfoIsOk,
				'message': errorMsg
			}
		}
        
        function checkAndSave()
        {
			console.log('queryForm.getValues():',queryForm.getValues());
			var queryBean = queryForm.getValues();// 直接从form中获取参数对象
			queryBean.serviceId = jspParms.serviceId; // 补充请求缺失的serviceId
			
			var url = 'checkInstanceNameExisitInNewVersionScriptService.do';
			if(jspParms.serviceId > 0) {
				url = 'checkCanUpdateFlowInfo.do';
			}
			Ext.Ajax.request ( {
			    url : url,
			    params : queryBean,
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
			    	if(!Ext.decode (response.responseText).success)
		    		{
			    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    		
		    		}
			    	else
		    		{
			    		var ret = saveFlow();
			    		
			    		if(ret>=0)
		    			{
			    			if (jspParms.customId > 0)
		    				{
			    				me.saveCustomTemplateForExec();
		    				}
			    			
			    			jspParms.serviceId = ret; 
			    			if(me.reqText=='switch')
			    			{
			    				console.log(parent);
			    				me.closeValue = 2;
//			    				btnInit(2);// 调整为作业编排——测试模式
			    			}
			    			else if(me.reqText=='edit')
		    				{
			    				me.closeValue = 1;
//			    				btnInit(1);// 调整为作业编排——编辑模式
		    				}
			    			Ext.Msg.alert ('提示', "保存成功！");
		    			}else{
		    				me.closeValue = 1;
		    				fitRadioGroup();
		    			}
		    		}
				    
			    }
			});
		
        }
        
        /**
		 * 保存流程
		 */
		function saveFlow(xml) {
			var parms = {
				bussId : bussCbOutSide.getValue (),
				bussTypeId : bussTypeCbOutSide.getValue (),
				bussName : bussCbOutSide.getRawValue (),
				bussTypeName:bussTypeCbOutSide.getRawValue (),
				serviceName: instanceNameObjOutSide.getValue(),
				serviceId : jspParms.serviceId,
				funcDesc: funcDescOutSide.getValue ()
			};
			return GRAPHS[editerName].editorUi.save(serviceInfo.bussName,parms);
		}
        
        Ext.applyIf(me, {
        	  dockedItems: [{
        	        xtype: 'toolbar',
        	        border: false,
        	        dock: 'bottom',
        	        margin: '0 0 5 0',
        	        layout: {pack: 'center'},
        	        items: [{
        	            xtype: 'button',
        	            text: '确定',
        	            cls: 'Common_Btn',
        	            handler: function() {
        	            	var validres = baseInfoIsOk();
        	    			if(validres['isOk']) {
        	    				checkAndSave();
        	    				this.up("window").close();
        	    			} else {
        	    				Ext.Msg.alert('提示', validres['message']);
        	    			}
        	            }
        	        },
        	        {
        	            xtype: 'button',
        	            text: '取消',
        	            cls: 'Common_Btn',
        	            handler: function() {
        	            	me.closeValue = 1;
        	            	fitRadioGroup();
        	                this.up("window").close();
        	            }
        	        }]
        	    }],
            items: [queryForm]
        });
        
        
        
        me.callParent(arguments);
    }

});