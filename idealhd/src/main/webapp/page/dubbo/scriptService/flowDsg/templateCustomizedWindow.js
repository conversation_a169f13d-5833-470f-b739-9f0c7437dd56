/*******************************************************************************
 * 模板步骤窗口
 ******************************************************************************/
Ext.onReady(function() {

			Ext.define('scriptNameModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'iscriptname',
					type : 'string'
				} ]
			});
			
			var scriptNameStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'scriptNameModel',
				proxy : {
					type : 'ajax',
					url : 'getScriptNameList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			
			scriptNameStore.on('load', function() {
				if (scriptId != '') {
					scriptNameObj.setValue(scriptId);
					scriptNameObj.fireEvent('select');
				}
			});
			
			var stepNameObj = Ext.create('Ext.form.field.Text', {
				fieldLabel : '步骤名称'
			});
			
			var scriptNameObj = Ext.create('Ext.form.field.ComboBox',{
				fieldLabel : '脚本名称',
				store : scriptNameStore,
				displayField : 'iscriptname',
				valueField : 'iid',
				triggerAction : 'all',
				editable : false,
				mode : 'local',
				listeners : {
					select : function(combo, record, eOpts) {
						paramStore.load();
						Ext.Ajax.request({
									url : 'getScritContent.do',
									method : 'POST',
									params : {
										iid : this.getValue()
									},
									success : function(
											response, options) {
										var success = Ext
												.decode(response.responseText).success;
										var message = Ext
												.decode(response.responseText).message;
										var icontent = Ext
												.decode(response.responseText).icontent;

										if (success) {
											scriptContentObj
													.setValue(icontent);
										} else {
											scriptContentObj
													.setValue('');
										}
									},
									failure : function(result,
											request) {
										scriptContentObj
												.setValue('');
									}

								});
					}

				}
			});

			var scriptContentObj = Ext.create('Ext.form.field.TextArea', {
				fieldLabel : '脚本内容',
				labelAlign : 'right',
				labelWidth : 60,
				readOnly : true,
				// padding : '5 5 5 5',
				autoScroll : true
			});

			var formPanel = Ext.create('Ext.form.Panel', {
				border : false,
				fieldDefaults : {
					labelAlign : 'right',
					width : contentPanel.getWidth() - 524,
					labelWidth : 60
				},
				width : '100%',
				buttonAlign : 'center',
				items : [ stepNameObj, scriptNameObj ],

				buttons : [ {
					text : '保存',
					handler : saveFun
				}, {
					text : '返回',
					handler : function() {
						parent.configwindow.close();
					}
				} ]
			});
			/** 树数据Model* */
			Ext.define('paramModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'ieai_sus_script_iid',
					type : 'string'
				}, {
					name : 'iparm_name',
					type : 'string'
				}, {
					name : 'iparm_type',
					type : 'string'
				}, {
					name : 'iparm_value',
					type : 'string'
				} ]
			});
			/** 树数据源* */
			var paramStore = Ext.create('Ext.data.Store', {
				autoLoad : false,
				autoDestroy : true,
				model : 'paramModel',
				// pageSize : 50,
				proxy : {
					type : 'ajax',
					url : 'getScriptparam.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			paramStore.on('beforeload', function(store, options) {
				var new_params = {
					ieai_sus_script_iid : scriptNameObj.getValue()
				};
				Ext.apply(paramStore.proxy.extraParams, new_params);
			});
			/** 树列表columns* */
			var paramColumns = [ {
				text : '序号',
				// align : 'center',
				width : 35,
				xtype : 'rownumberer'
			}, {
				text : '参数名',
				dataIndex : 'iparm_name',
				flex : 1
			}, {
				text : '参数类型',
				dataIndex : 'iparm_type',
				flex : 1
			}, {
				text : '参数值',
				dataIndex : 'iparm_value',
				flex : 1
			} ];
			/** 树列表panel* */
			var paramGrid = Ext.create('Ext.grid.Panel', {
				store : paramStore,
				region : 'center',
				border : false,
				columnLines : true,
				flex : 2,
				columns : paramColumns,
				// bbar : bsPageBar,
				collapsible : false
			});
			var paramPanel = Ext.create('Ext.Panel', {
				// title:'提示信息',
				width : '100%',
				height : 200,
				split : true,
				// region: 'south',
				layout : 'fit',
				border : true,
				titleAlign : 'left',
				items : [ paramGrid ]
			});

			var alarmPanel = Ext.create('Ext.Panel', {
				// title:'提示信息',
				width : '100%',
				height : contentPanel.getHeight() - 328,
				split : true,
				// region: 'south',
				padding : '5 0 0 0',
				layout : 'fit',
				border : true,
				titleAlign : 'left',
				items : [ scriptContentObj ]
			});
			// 主Panel
			var MainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "mainpanelWindowDiv",
				width : '100%',
				height : contentPanel.getHeight() - 35,
				// overflowY:'scroll',
				border : false,
				bodyPadding : 5,
				items : [ formPanel, paramPanel, alarmPanel ]
			});
			/** 初始化方法* */
			function initFun() {
				stepNameObj.setValue(stepName);
				// scriptParamObj.setValue(scriptParam);
			}
			function trim(t) {
				t = t.replace(/(^\s*)|(\s*$)/g, "");
				return t.replace(/(^ *)|( *$)/g, "");
			}
			function saveFun() {
				if ('开始' == stepNameObj.getValue().trim()) {
					Ext.Msg.alert('提示', '步骤名称不可以为<开始>！');
					return;
				}
				if ('结束' == stepNameObj.getValue().trim()) {
					Ext.Msg.alert('提示', '步骤名称不可以为<结束>！');
					return;
				}
				if ('' == stepNameObj.getValue().trim()) {
					Ext.Msg.alert('提示', '步骤名称不可以为空！');
					return;
				}
				parent.callbackWindw(scriptNameObj.getValue(), stepNameObj
						.getValue());
				parent.configwindow.close();
			}
			initFun();
		});
