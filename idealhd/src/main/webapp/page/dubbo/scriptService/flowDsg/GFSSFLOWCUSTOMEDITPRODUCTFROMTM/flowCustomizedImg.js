/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
var cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
var phaseDataListGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
var editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
var graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
var modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
var cmdNameCountGFSSFLOWCUSTOMEDITPRODUCTFROMTM = 1;
var actNameCountMapGFSSFLOWCUSTOMEDITPRODUCTFROMTM = {};
actStartInfo['G<PERSON><PERSON><PERSON><PERSON>CUSTOMEDITPRODUCTFROMTM'] = {};

Ext.onReady(function() {
    try {
        if (configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM != null) {
            configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM.destroy();
        }
        if (cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM != null) {
            cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM.destroy();
        }
        if (phaseDataListGFSSFLOWCUSTOMEDITPRODUCTFROMTM != null) {
            phaseDataListGFSSFLOWCUSTOMEDITPRODUCTFROMTM = null;
        }
        if (editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM != null) {
            editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.destroy();
        }
        if (graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM != null) {
            graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.destroy();
        }
        if (modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM != null) {
            modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM = null;
        }
    } catch(err) {}

    getPhaseGFSSFLOWCUSTOMEDITPRODUCTFROMTM();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSFLOWCUSTOMEDITPRODUCTFROMTM(container, outline, toolbar, sidebar, status) {

    if (!mxClient.isBrowserSupported()) {
        Ext.Msg.alert('提示', '当前浏览器不支持此功能!');
    } else {
        //自定义连线图标
        mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
        mxConstants.MIN_HOTSPOT_SIZE = 16;
        mxConstants.DEFAULT_HOTSPOT = 1;

        mxGraphHandler.prototype.guidesEnabled = true;

        mxGuide.prototype.isEnabledForEvent = function(evt) {
            return ! mxEvent.isAltDown(evt);
        };

        mxEdgeHandler.prototype.snapToTerminals = true;

        if (mxClient.IS_QUIRKS) {
            document.body.style.overflow = 'hidden';
            new mxDivResizer(container);
            new mxDivResizer(outline);
            new mxDivResizer(toolbar);
            new mxDivResizer(sidebar);
            new mxDivResizer(status);
        }

        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM = new mxEditor();
        parent.editorSonGFSSFLOWCUSTOMEDITPRODUCTFROMTM = editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM = editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.graph;
        modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM = graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getModel();
        
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setTooltips(false);

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setDropEnabled(false);

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.connectionHandler.targetConnectImage = true;

        //连线是否必须连接至节点
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setAllowDanglingEdges(false);

        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setGraphContainer(container);
        //键盘热键控制
        var config = mxUtils.load('mxgraph-master/examples/editors/config/keyhandler-commons.xml').getDocumentElement();
        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.configure(config);

        //不允许重复连线
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setMultigraph(false);
        //不允许自己连自己
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setAllowLoops(false);

        var group = new mxCell('Group', new mxGeometry(), 'group');
        group.setVertex(true);
        group.setConnectable(false);
        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.defaultGroup = group;
        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.groupBorderSize = 20;

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isValidDropTarget = function(cell, cells, evt) {
            return this.isSwimlane(cell);
        };

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isValidRoot = function(cell) {
            return this.isValidDropTarget(cell);
        }

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isCellSelectable = function(cell) {
            return ! this.isCellLocked(cell);
        };

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getLabel = function(cell) {
            var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
            var geometry = this.model.getGeometry(cell);

            if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null || (geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) && geometry.width >= 2) {
                var style = this.getCellStyle(cell);
                var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
                var max = geometry.width / (fontSize * 1.5);

                if (max < label.length) {
                    return label.substring(0, max) + '...';
                }
            }

            return label;
        };

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isWrapping = function(cell) {
            return this.model.isCollapsed(cell);
        };

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isHtmlLabel = function(cell) {
            return ! this.isSwimlane(cell);
        }

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.dblClick = function(evt, cell) {

            if (this.isEnabled() && !mxEvent.isConsumed(evt) && cell != null && this.isCellEditable(cell)) {
                if (this.model.isEdge(cell)) {} else {
                    cellObjGFSSFLOWCUSTOMEDITPRODUCTFROMTM = cell;
                    if (cell.style != 'beginStyle' && cell.style != 'endStyle') {
                        if (cell.style == 'scriptServiceStyle') {
                            //直接调用脚本
                            openExecScriptWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM();
                        } else if (cell.style == 'usertaskStyle') {
                            //UT提醒任务
                            openUTWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM();
                        } else if(cell.style=='callflowStyle') {
                        	graphViewStack = new Array();
							stackFlowView = Ext.create('widget.window', {
					            title: '详细信息',
					            closable: true,
					            closeAction: 'destroy',
					            width: contentPanel.getWidth(),
					            minWidth: 350,
					            height: contentPanel.getHeight(),
					            draggable: true,
					            resizable: false,
					            modal: true,
					            loader: {
					            	url : 'flowWinViewer.do', 
					                params: {
					                    iid: cell.scriptId,
					                    flag: 1,
					                    actionType:'exec',
					                    pageFrom: 'GFSSFLOWCUSTOMEDITPRODUCTFROMTM',
										isShowInWindow: 1,
										parentMxIid: cell.mxIid,
										isStack: true
					                },
					                autoLoad: true,
					                scripts: true
					            }
					        }).show();
		    				return ;
						} else {
                            //模板
                            openWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM();
                        }

                    }
                }
            }

            mxEvent.consume(evt);
        };

        //节点之间可以连接
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.setConnectable(true);

        //增加样式
        configureStylesheetGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM);

        actNameCountMapGFSSFLOWCUSTOMEDITPRODUCTFROMTM['scriptServiceStyle'] = 0;
        actNameCountMapGFSSFLOWCUSTOMEDITPRODUCTFROMTM['usertaskStyle'] = 0;

        var spacer = document.createElement('div');
        spacer.style.display = 'inline';
        spacer.style.padding = '8px';

        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.addAction('export',
        function(editor, cell) {
            var textarea = document.createElement('textarea');
            textarea.style.width = '400px';
            textarea.style.height = '400px';
            var enc = new mxCodec(mxUtils.createXmlDocument());
            var node = enc.encode(editor.graph.getModel());
            textarea.value = mxUtils.getPrettyXml(node);
            showModalWindowGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM, 'XML', textarea, 410, 440);
        });

        editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.addAction('deleteBefore',
        function(editor, cell) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if (cells[i].style == 'beginStyle') {
                    Ext.Msg.alert('提示', '不能删除<开始>节点！');
                    return false;
                }
                if (cells[i].style == 'endStyle') {
                    Ext.Msg.alert('提示', '不能删除<结束>节点！');
                    return false;
                }
            }
            editor.execute('delete');
        });

        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCTFROMTM(editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCTFROMTM(editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCTFROMTM(editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
        addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCTFROMTM(editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);

        var outln = new mxOutline(graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM, outline);

        var splash = document.getElementById('splashGFSSFLOWCUSTOMEDITPRODUCTFROMTM');
        if (splash != null) {
            try {
                mxEvent.release(splash);
                mxEffects.fadeOut(splash, 100, true);
            } catch(e) {

                splash.parentNode.removeChild(splash);
            }
        }

        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.popupMenuHandler.factoryMethod = function(menu, cell, evt) {
            return createPopupMenuGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM, menu, cell, evt);
        };

        initFunGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM);
    }

};

/**增加右键删除菜单**/
function createPopupMenuGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph, menu, cell, evt) {
    if (cell != null) {
        menu.addItem('删除', 'images/delete.png',
        function() {
            editorGFSSFLOWCUSTOMEDITPRODUCTFROMTM.execute('deleteBefore');
        });
    }
};

/**
 * 初始化方法
 */
function initFunGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph) {
    if (parent.iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM > 0) {
        loadGraphGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph);
        Ext.Ajax.request({
            url: 'getFlowCustomTemplateData.do',
            method: 'POST',
            params: {
            	iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
                flag: parent.flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM
            },
            success: function(response, options) {
                var dataS = Ext.decode(response.responseText).data;
                actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'] = JSON.parse(dataS);
                
                var root2FlowWindow = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getRoot ();
        		var count = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getChildCount (root2FlowWindow);
        		for (var i = 0; i < count; i++)
        		{
        			var cells = root2FlowWindow.getChildAt (i);
        			var counts = cells.getChildCount ();
        			for (var j = 0; j < counts; j++)
        			{
        				var cellss = cells.getChildAt (j);
        				if (!modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
        				{
        					cellss.mxIid = parent.iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM+":"+cellss.id;
        					if(!actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'].hasOwnProperty(cellss.mxIid)) {
        						if(parseInt(cellss.phaseId)==0) {
        							actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][cellss.mxIid] = {
        									'actNo': cellss.id,
        									'actType': parseInt(cellss.phaseId),
        									'actName': cellss.value,
        									'isShutdown': cellss.isShutdown
        							};
        						} else if(parseInt(cellss.phaseId)==1) {
        							actStartInfo['GFSSFLOWCUSTOMEDITPRODUCTFROMTM'][cellss.mxIid] = {
        									'actNo': cellss.id,
        									'actType': parseInt(cellss.phaseId),
        									'actName': cellss.value,
        									'message': cellss.ireminfo
        							};
        						}
        					}
        				}
        			}
        		}
            },
            failure: function(result, request) {
            	Ext.Msg.alert ('提示', '获取模板数据失败!');
            }
        });
    } else {
        addBeginEndCellGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph, "开始", "beginStyle", 1, 1);
        addBeginEndCellGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph, "结束", "endStyle", 150, 150);
    }

};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSFLOWCUSTOMEDITPRODUCTFROMTM(editor, toolbar, action, label, image, isTransparent) {
    if (image != null) {
        var img = document.createElement('img');
        img.setAttribute('src', image);
        img.style.width = '74px';
        img.style.height = '30px';
        img.style.verticalAlign = 'middle';
        img.title = label;
        img.style.marginRight = '10px';
        img.style.marginTop = '2px';
    }
    mxEvent.addListener(img, 'click',
    function(evt) {
        if ('delete' == action) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if ('开始' == cells[i].value) {
                    Ext.Msg.alert('提示', '不删除<开始>节点！');
                    return false;
                }
            }
        }

        editor.execute(action);
    });
    toolbar.appendChild(img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph, title, content, width, height) {
    var background = document.createElement('div');
    background.style.position = 'absolute';
    background.style.left = '0px';
    background.style.top = '0px';
    background.style.right = '0px';
    background.style.bottom = '0px';
    background.style.background = 'black';
    mxUtils.setOpacity(background, 50);
    document.body.appendChild(background);

    if (mxClient.IS_IE) {
        new mxDivResizer(background);
    }

    var x = Math.max(0, document.body.scrollWidth / 2 - width / 2);
    var y = Math.max(10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
    var wnd = new mxWindow(title, content, x, y, width, height, false, true);
    wnd.setClosable(true);

    // Fades the background out after after the window has been closed
    wnd.addListener(mxEvent.DESTROY,
    function(evt) {
        graph.setEnabled(true);
        mxEffects.fadeOut(background, 50, true, 10, 30, true);
    });

    graph.setEnabled(false);
    graph.tooltipHandler.hide();
    wnd.setVisible(true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph, label, styleName, w, h) {

    var parent = graph.getDefaultParent();
    var model = graph.getModel();

    var v1 = null;

    model.beginUpdate();
    try {
        v1 = graph.insertVertex(parent, null, label, w, h, 50, 25, styleName);
        v1.setConnectable(true);
    } finally {
        model.endUpdate();
    }

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph, sidebar, label, image, styleName, phaseId) {
    var funct = function(graph, evt, cell, x, y) {
        var parent = graph.getDefaultParent();
        var model = graph.getModel();

        var v1 = null;

        model.beginUpdate();
        try {
            actNameCountMapGFSSFLOWCUSTOMEDITPRODUCTFROMTM[styleName] = ++actNameCountMapGFSSFLOWCUSTOMEDITPRODUCTFROMTM[styleName];
            v1 = graph.insertVertex(parent, null, label + actNameCountMapGFSSFLOWCUSTOMEDITPRODUCTFROMTM[styleName], x, y, 90, 32, styleName);
            v1.setConnectable(true);
            v1.phaseId = phaseId;

        } finally {
            model.endUpdate();
        }
        graph.setSelectionCell(v1);
    }

    var img = document.createElement('img');
    img.setAttribute('src', image);
    img.style.width = '80px';
    img.style.height = '32px';
    img.title = label;
    img.style.marginLeft = '10px';
    img.style.marginBottom = '15px';

    sidebar.appendChild(img);

    var dragElt = document.createElement('div');
    dragElt.style.border = 'dashed black 1px';
    dragElt.style.width = '120px';
    dragElt.style.height = '120px';

    var ds = mxUtils.makeDraggable(img, graph, funct, dragElt, 0, 0, true, true);
    ds.setGuidesEnabled(true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSFLOWCUSTOMEDITPRODUCTFROMTM(graph) {

    var style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
    style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
    style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTSTYLE] = 0;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
    style[mxConstants.STYLE_RESIZABLE] = '0'; //不可缩放
    graph.getStylesheet().putDefaultVertexStyle(style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('beginStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('endStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
    graph.getStylesheet().putCellStyle('cmdStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
    style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
    style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_STARTSIZE] = '30';
    style[mxConstants.STYLE_FONTSIZE] = '16';
    style[mxConstants.STYLE_FONTSTYLE] = 1;
    graph.getStylesheet().putCellStyle('group', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_FONTCOLOR] = '#774400';
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_FONTSIZE] = '10';
    style[mxConstants.STYLE_FONTSTYLE] = 2;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
    graph.getStylesheet().putCellStyle('port', style);

    style = graph.getStylesheet().getDefaultEdgeStyle();
    style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_STROKEWIDTH] = '1';
    style[mxConstants.STYLE_STROKECOLOR] = '#595758';
    style[mxConstants.STYLE_ROUNDED] = false;
    style[mxConstants.STYLE_EDGE] = mxConstants.EDGESTYLE_ELBOW;

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('scriptServiceStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('usertaskStyle', style);
    style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getModel().beginUpdate();
    try {
        var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag=" + flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM + "&instanceID=" + parent.iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM));
        var dec = new mxCodec(doc);
        dec.decode(doc.getDocumentElement(), graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getModel());
    } finally {
        graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getModel().endUpdate();
    }
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/mxgraph/flowCustomizedWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: false,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITPRODUCTFROMTM/flowCustomizedExecScriptWindow.jsp",
            params: {
                flag: flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM
            },
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    configwindowFlowGFSSFLOWCUSTOMEDITPRODUCTFROMTM = Ext.create('Ext.window.Window', {
        title: '人工提醒配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: 580,
        height: 215,
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMEDITPRODUCTFROMTM/scriptServiceUTWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    //更新名后续刷新才能显示
    graphGFSSFLOWCUSTOMEDITPRODUCTFROMTM.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    mainGFSSFLOWCUSTOMEDITPRODUCTFROMTM(document.getElementById('graphContainerGFSSFLOWCUSTOMEDITPRODUCTFROMTM'), document.getElementById('outlineContainerGFSSFLOWCUSTOMEDITPRODUCTFROMTM'), document.getElementById('toolbarContainer'), document.getElementById('sidebarContainerGFSSFLOWCUSTOMEDITPRODUCTFROMTM'), document.getElementById('statusContainerGFSSFLOWCUSTOMEDITPRODUCTFROMTM'));

}

function saveCustomTemplateGFSSFLOWCUSTOMEDITPRODUCTFROMTM() {
    var customName = parent.customNameObjOutSideGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getValue();
    if (customName.trim() == '')
	{
		Ext.Msg.alert ('提示', '请填写模板名称!');
		return null;
	}

    Ext.Ajax.request({
        url: 'checkCustomTemplateNameIsExist.do',
        params: {
            iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
        	customName: customName,
            flag: flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM
        },
        method: 'POST',
        success: function(response, options) {
            if (!Ext.decode(response.responseText).success) {
                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
            } else {
                var root2FlowWindow = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getRoot();
                var count = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getChildCount(root2FlowWindow);
                var serviceId = parent.iidGFSSFLOWCUSTOMEDITPRODUCTFROMTM;
                var data = [];
                for (var i = 0; i < count; i++) {
                    var cells = root2FlowWindow.getChildAt(i);
                    var counts = cells.getChildCount();
                    for (var j = 0; j < counts; j++) {
                        var cellss = cells.getChildAt(j);
                        // 如果不是连线，则是节点
                        if (!modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isEdge(cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle') {
                        	var actType=cellss.phaseId;
            				var actName=cellss.value;
            				if('1'==actType){
            					
            					var messinfo = cellss.ireminfo;
            					if(messinfo==''){
            						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
            						return;
            					}else{
            						var s = cellss.id + "++!!++"+messinfo+"++!!++++!!++++!!++++!!++++!!++++!!++"+actType+"++!!++"+actName;
            						data.push(s);
            					}
            				}else{
            					var chosedAgents = cellss.chosedAgents;
            					if(chosedAgents) {
            						var scriptParam = cellss.scriptParam;
            						var chosedConfigParams = cellss.chosedConfigParams;
            						for(var key in chosedConfigParams){
            							for(var keyIn in chosedConfigParams[key]){
            								if(chosedConfigParams[key][keyIn]==""){
            									chosedConfigParams[key][keyIn] = scriptParam[keyIn];
            								}
            							}
            						}
            						var startUser = cellss.startUser;
            						//var chosedAgentsAndParams = cellss.chosedAgentsAndParams;
            						var finalParamters = {};
            						for(var i=0;i<chosedAgents.length;i++){
            							if(chosedConfigParams.hasOwnProperty(chosedAgents[i])) {
            								finalParamters[chosedAgents[i]]=chosedConfigParams[chosedAgents[i]];
            							}else{
            								finalParamters[chosedAgents[i]]=scriptParam;
            							}
            						}
            						var chosedAgentsAndStartUsers = cellss.chosedAgentsAndStartUsers;
            						var chosedAgentsAndDbSources = cellss.chosedAgentsAndDbSources;
            						var s = cellss.id + "++!!++" + JSON.stringify(scriptParam) + "++!!++" + chosedAgents.join('pp') + "++!!++" + JSON.stringify(finalParamters) + "++!!++" + JSON.stringify(chosedAgentsAndStartUsers)+ "++!!++" + JSON.stringify(chosedAgentsAndDbSources) + "++!!++" +startUser+"++!!++"+actType+"++!!++"+actName;
            						console.log("最终数据："+s);
            						data.push(s);
            					} else {
            						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
            						return;
            					}
            				}

                        }
                    }
                }
                data.push("");
                Ext.Ajax.request({
                    url: 'saveFlowCustomTemplate.do',
                    method: 'POST',
                    params: {
                    	iid: parent.customIdGFSSFLOWCUSTOMEDITPRODUCTFROMTM,
                    	customName: customName,
                    	serviceId: serviceId,
                        data: data,
                        flag: parent.flagGFSSFLOWCUSTOMEDITPRODUCTFROMTM
                    },
                    success: function(response, options) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                            Ext.MessageBox.show({
                                title: "提示",
                                msg: '模板保存成功！',
                                buttonText: {
                                    yes: '确定'
                                },
                                buttons: Ext.Msg.YES
                            });
                        } else {
                        	Ext.Msg.alert ('提示', "保存失败！");
                        }
                    },
                    failure: function(result, request) {
                        Ext.MessageBox.show({
                            title: "提示",
                            msg: "启动失败",
                            buttonText: {
                                yes: '确定'
                            },
                            buttons: Ext.Msg.YES
                        });
                    }

                });

            }
        },
        failure: function(result, request) {}
    });

}

function checkDataIsReadyGFSSFLOWCUSTOMEDITPRODUCTFROMTM () {
	var root2FlowWindow = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getRoot ();
	var count = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getChildCount (root2FlowWindow);
	for (var i = 0; i < count; i++)
	{
		var cells = root2FlowWindow.getChildAt (i);
		var counts = cells.getChildCount ();
		for (var j = 0; j < counts; j++)
		{
			var cellss = cells.getChildAt (j);
			// 如果不是连线，则是节点
			if (!modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
			{
				var chosedAgents = cellss.chosedAgents;
				var actType=cellss.phaseId;
				if('1'==actType){
					var messinfo = cellss.ireminfo;
					if(messinfo==''){
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
						return false;
					}
				}else{
					if(!chosedAgents) {
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
						return false;
					}
				}
			}
		}
	}
	return true;
}

function orgStartDataGFSSFLOWCUSTOMEDITPRODUCTFROMTM () {
	// 遍历所有节点
	var root2FlowWindow = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getRoot ();
	var count = modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.getChildCount (root2FlowWindow);
	var data = [];
	for (var i = 0; i < count; i++)
	{
		var cells = root2FlowWindow.getChildAt (i);
		var counts = cells.getChildCount ();
		for (var j = 0; j < counts; j++)
		{
			var cellss = cells.getChildAt (j);
			// 如果不是连线，则是节点
			if (!modelGFSSFLOWCUSTOMEDITPRODUCTFROMTM.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
			{
				var actType=cellss.phaseId;
				var actName=cellss.value;
				if('1'==actType){
					
					var messinfo = cellss.ireminfo;
					if(messinfo==''){
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
						return;
					}else{
						var s = cellss.id + "++!!++"+messinfo+"++!!++++!!++++!!++++!!++++!!++++!!++"+actType+"++!!++"+actName;
						data.push(s);
					}
				}else{
					var chosedAgents = cellss.chosedAgents;
					if(chosedAgents) {
						var scriptParam = cellss.scriptParam;
						var chosedConfigParams = cellss.chosedConfigParams;
						for(var key in chosedConfigParams){
							for(var keyIn in chosedConfigParams[key]){
								if(chosedConfigParams[key][keyIn]==""){
									chosedConfigParams[key][keyIn] = scriptParam[keyIn];
								}
							}
						}
						var startUser = cellss.startUser;
						//var chosedAgentsAndParams = cellss.chosedAgentsAndParams;
						var finalParamters = {};
						for(var i=0;i<chosedAgents.length;i++){
							if(chosedConfigParams.hasOwnProperty(chosedAgents[i])) {
								finalParamters[chosedAgents[i]]=chosedConfigParams[chosedAgents[i]];
							}else{
								finalParamters[chosedAgents[i]]=scriptParam;
							}
						}
						var chosedAgentsAndStartUsers = cellss.chosedAgentsAndStartUsers;
						var chosedAgentsAndDbSources = cellss.chosedAgentsAndDbSources;
						var s = cellss.id + "++!!++" + JSON.stringify(scriptParam) + "++!!++" + chosedAgents.join('pp') + "++!!++" + JSON.stringify(finalParamters) + "++!!++" + JSON.stringify(chosedAgentsAndStartUsers)+ "++!!++" + JSON.stringify(chosedAgentsAndDbSources) + "++!!++" +startUser+"++!!++"+actType+"++!!++"+actName;
						console.log("最终数据："+s);
						data.push(s);
					} else {
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
						return;
					}
				}
				
			}
		}
	}
	data.push("");
	return data;
}
