/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSVIEW;
var cellObjGFSSVIEW;
var phaseDataListGFSSVIEW;
var editorGFSSVIEW;
var graphGFSSVIEW;
var modelGFSSVIEW;
var cmdNameCountGFSSVIEW = 1;
var actNameCountMapGFSSVIEW = {};

Ext.onReady (function ()
{
	try
	{
		if (configwindowFlowGFSSVIEW != null)
		{
			configwindowFlowGFSSVIEW.destroy ();
		}
		if (cellObjGFSSVIEW != null)
		{
			cellObjGFSSVIEW.destroy ();
		}
		if (phaseDataListGFSSVIEW != null)
		{
			phaseDataListGFSSVIEW= null;
		}
		if (editorGFSSVIEW != null)
		{
			editorGFSSVIEW.destroy ();
		}
		if (graphGFSSVIEW != null)
		{
			graphGFSSVIEW.destroy ();
		}
		if (modelGFSSVIEW != null)
		{
			modelGFSSVIEW=null;
		}
	}
	catch(err)
	{
	}
	
	getPhaseGFSSVIEW ();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSVIEW (container, outline, toolbar, sidebar, status)
{
	
	if (!mxClient.isBrowserSupported ())
	{
		Ext.Msg.alert ('提示', '当前浏览器不支持此功能!');
	}
	else
	{
		//自定义连线图标
		mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
		mxConstants.MIN_HOTSPOT_SIZE = 16;
		mxConstants.DEFAULT_HOTSPOT = 1;
		
		// Enables guides
		mxGraphHandler.prototype.guidesEnabled = true;
		
		// Alt disables guides
		mxGuide.prototype.isEnabledForEvent = function (evt)
		{
			return !mxEvent.isAltDown (evt);
		};
		
		// Enables snapping waypoints to terminals
		mxEdgeHandler.prototype.snapToTerminals = true;
		
		// Workaround for Internet Explorer ignoring certain CSS directives
		if (mxClient.IS_QUIRKS)
		{
			document.body.style.overflow = 'hidden';
			new mxDivResizer (container);
			new mxDivResizer (outline);
			new mxDivResizer (toolbar);
			new mxDivResizer (sidebar);
			new mxDivResizer (status);
		}
		
		 editorGFSSVIEW = new mxEditor ();
		 parent.editorSonGFSSVIEW=editorGFSSVIEW;
		 graphGFSSVIEW = editorGFSSVIEW.graph;
		 modelGFSSVIEW = graphGFSSVIEW.getModel ();
		 
		 graphGFSSVIEW.setTooltips(false);
		
		// Disable highlight of cells when dragging from toolbar
		graphGFSSVIEW.setDropEnabled (false);
		
		// Centers the port icon on the target port
		graphGFSSVIEW.connectionHandler.targetConnectImage = true;
		
		// Does not allow dangling edges
		//连线是否必须连接至节点
		graphGFSSVIEW.setAllowDanglingEdges (false);
		
		// Sets the graph container and configures the editor
		editorGFSSVIEW.setGraphContainer (container);
		//键盘热键控制
		var config = mxUtils.load ('mxgraph-master/examples/editors/config/keyhandler-commons.xml')
		        .getDocumentElement ();
		editorGFSSVIEW.configure (config);
		
		//不允许重复连线
		graphGFSSVIEW.setMultigraph(false);
		//不允许自己连自己
		graphGFSSVIEW.setAllowLoops(false);
		
		var group = new mxCell ('Group', new mxGeometry (), 'group');
		group.setVertex (true);
		group.setConnectable (false);
		editorGFSSVIEW.defaultGroup = group;
		editorGFSSVIEW.groupBorderSize = 20;
		
		// Disables drag-and-drop into non-swimlanes.
		graphGFSSVIEW.isValidDropTarget = function (cell, cells, evt)
		{
			return this.isSwimlane (cell);
		};
		
		// Disables drilling into non-swimlanes.
		graphGFSSVIEW.isValidRoot = function (cell)
		{
			return this.isValidDropTarget (cell);
		}

		// Does not allow selection of locked cells
		graphGFSSVIEW.isCellSelectable = function (cell)
		{
			return !this.isCellLocked (cell);
		};
		
		// Returns a shorter label if the cell is collapsed and no
		// label for expanded groups
		graphGFSSVIEW.getLabel = function(cell)
		{
			var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
			var geometry = this.model.getGeometry(cell);
			
			if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null ||
				(geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) &&
				geometry.width >= 2)
			{
				var style = this.getCellStyle(cell);
				var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
				var max = geometry.width / (fontSize * 1.5);
				
				if (max < label.length)
				{
					return label.substring(0, max) + '...';
				}
			}
			
			return label;
		};
		
		// Enables wrapping for vertex labels
		graphGFSSVIEW.isWrapping = function(cell)
		{
			return this.model.isCollapsed(cell);
		};

		// Disables HTML labels for swimlanes to avoid conflict
		// for the event processing on the child cells. HTML
		// labels consume events before underlying cells get the
		// chance to process those events.
		//
		// NOTE: Use of HTML labels is only recommended if the specific
		// features of such labels are required, such as special label
		// styles or interactive form fields. Otherwise non-HTML labels
		// should be used by not overidding the following function.
		// See also: configureStylesheet.
		graphGFSSVIEW.isHtmlLabel = function (cell)
		{
			return !this.isSwimlane (cell);
		}

		// Shows a "modal" window when double clicking a vertex.
		graphGFSSVIEW.dblClick = function (evt, cell)
		{
			
			// Do not fire a DOUBLE_CLICK event here as mxEditor will
			// consume the event and start the in-place editor.
			if (this.isEnabled () && !mxEvent.isConsumed (evt) && cell != null && this.isCellEditable (cell))
			{
				if (this.model.isEdge (cell)) //|| !this.isHtmlLabel (cell))
				{
					//连线编辑
//					this.startEditingAtCell (cell);
				}
				else
				{
					cellObjGFSSVIEW = cell;
					if(cell.style!='beginStyle'&&cell.style!='endStyle')
						{
						if(cell.style=='scriptServiceStyle') {
							//直接调用脚本
							openEditScriptWindwGFSSVIEW ();
						}
						else if(cell.style=='usertaskStyle') {
							//UT提醒任务
							openUTWindwGFSSVIEW ();
							}
						else if(cell.style=='callflowStyle') {
//							Ext.Msg.alert ('提示', '该活动为工作流调用活动，不支持查看！');
							graphViewStack = new Array();
							stackFlowView = Ext.create('widget.window', {
					            title: '详细信息',
					            closable: true,
					            closeAction: 'destroy',
					            width: contentPanel.getWidth(),
					            minWidth: 350,
					            height: contentPanel.getHeight(),
					            draggable: true,
					            resizable: false,
					            modal: true,
					            loader: {
					            	url : 'flowWinViewer.do', 
					                params: {
					                    iid: cell.scriptId,
					                    flag: 0,
					                    actionType:'view',
										isShowInWindow: 1,
										isStack: true
					                },
					                autoLoad: true,
					                scripts: true
					            }
					        }).show();
		    				return ;
						}
						else 
							{
							//模板
							openWindwGFSSVIEW ();
							}
						
						}
				}
			}
			
			// Disables any default behaviour for the double click
			mxEvent.consume (evt);
		};
		
		// Enables new connections
		//节点之间可以连接
		graphGFSSVIEW.setConnectable (true);
		
		// Adds all required styles to the graph (see below)
		//增加样式
		configureStylesheetGFSSVIEW (graphGFSSVIEW);
		
		actNameCountMapGFSSVIEW['scriptServiceStyle'] = 0;
		actNameCountMapGFSSVIEW['usertaskStyle'] = 0;
		
		// Creates a new DIV that is used as a toolbar and adds
		// toolbar buttons.
		var spacer = document.createElement ('div');
		spacer.style.display = 'inline';
		spacer.style.padding = '8px';
		
		// Defines a new export action
		editorGFSSVIEW.addAction ('export', function (editor, cell)
		{
			var textarea = document.createElement ('textarea');
			textarea.style.width = '400px';
			textarea.style.height = '400px';
			var enc = new mxCodec (mxUtils.createXmlDocument ());
			var node = enc.encode (editor.graph.getModel ());
			textarea.value = mxUtils.getPrettyXml (node);
			showModalWindowGFSSVIEW (graphGFSSVIEW, 'XML', textarea, 410, 440);
		});
		
		editorGFSSVIEW.addAction ('deleteBefore', function (editor, cell)
				{
    			var cells=editor.graph.getSelectionCells();
    			for(i=0;i<cells.length;i++)
				{
    				if(cells[i].style=='beginStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<开始>节点！');
    				return false;
    				}
    				if(cells[i].style=='endStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<结束>节点！');
    				return false;
    				}
				}
					editor.execute ('delete');
				});
		editorGFSSVIEW.addAction ('save', function (editor, cell)
		{
			var bussId = parent.bussCbOutSideGFSSVIEW.getValue ();
			var bussTypeId = parent.bussTypeCbOutSideGFSSVIEW.getValue ();
			var instanceName = parent.instanceNameObjOutSideGFSSVIEW.getValue ();
			if (bussId == null)
			{
				Ext.Msg.alert ('提示', '请选择一级分类!');
				return null;
			}
			if (bussTypeId == null)
			{
				Ext.Msg.alert ('提示', '请选择二级分类!');
				return null;
			}
			if (instanceName.trim() == '')
			{
				Ext.Msg.alert ('提示', '请填写服务名称!');
				return null;
			}
			checkInstanceNameExisitInNewVersionGFSSVIEW();
		});
		
		addToolbarButtonGFSSVIEW (editorGFSSVIEW, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
		addToolbarButtonGFSSVIEW (editorGFSSVIEW, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
		addToolbarButtonGFSSVIEW (editorGFSSVIEW, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
		addToolbarButtonGFSSVIEW (editorGFSSVIEW, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);
		
		var outln = new mxOutline (graphGFSSVIEW, outline);
		
		var splash = document.getElementById ('splashGFSSVIEW');
		if (splash != null)
		{
			try
			{
				mxEvent.release (splash);
				mxEffects.fadeOut (splash, 100, true);
			}
			catch (e)
			{
				
				// mxUtils is not available (library not loaded)
				splash.parentNode.removeChild (splash);
			}
		}
		
		graphGFSSVIEW.popupMenuHandler.factoryMethod = function(menu, cell, evt)
		{
			return createPopupMenuGFSSVIEW(graphGFSSVIEW, menu, cell, evt);
		};
		
		initFunGFSSVIEW(graphGFSSVIEW);
	}
	
};

/**增加右键删除菜单**/
function createPopupMenuGFSSVIEW(graph, menu, cell, evt)
{
	if (cell != null)
	{
		menu.addItem('删除', 'images/delete.png', function()
		{
			editorGFSSVIEW.execute ('deleteBefore');
		});
	}
};


/**
 * 初始化方法
 */
function initFunGFSSVIEW(graph)
{
	if(parent.iidGFSSVIEW>0)
	{
		//修改将原图加载
		loadGraphGFSSVIEW(graph);
		
		if(parent.actionTypeGFSSVIEW=='dbcheckForExec' || parent.actionTypeGFSSVIEW=='dbbackForExec') {
			var root2FlowWindow = modelGFSSVIEW.getRoot ();
			var count = modelGFSSVIEW.getChildCount (root2FlowWindow);
			var data = execStartDataGFSSVIEW.split("plpa");
			var dataMap = {};
			$.each(data, function(index, value) {
				var aa = value.split("++!!++");
				dataMap[aa[0]] = aa;
			});
			for (var i = 0; i < count; i++)
			{
				var cells = root2FlowWindow.getChildAt (i);
				var counts = cells.getChildCount ();
				for (var j = 0; j < counts; j++)
				{
					var cellss = cells.getChildAt (j);
					console.log('asdf', cellss);
					// 如果不是连线，则是节点
					if (!modelGFSSVIEW.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
					{
						if(dataMap.hasOwnProperty((cellss.id+""))) {
							var aa = dataMap[(cellss.id+"")];
							console.log(aa);
							if('1'==cellss.phaseId) {
								cellss.ireminfo =aa[1];
							} else {
								cellss.scriptParam = JSON.parse(aa[1]);
								cellss.chosedAgents = aa[2].split('pp');
//								cellss.chosedAgentsAndParams = JSON.parse(aa[3]);
								cellss.chosedConfigParams = JSON.parse(aa[3]);
								cellss.chosedAgentsAndStartUsers = JSON.parse(aa[4]);
								cellss.chosedAgentsAndDbSources = JSON.parse(aa[5]);
								cellss.startUser = aa[6];
								
							}
						}
					}
				}
			}
		}
	}else
		{
		//新建增加开始节点
		addBeginEndCellGFSSVIEW (graph, "开始","beginStyle",1,1);
		//新建增加结束节点
		addBeginEndCellGFSSVIEW (graph, "结束","endStyle",150,150);
		}
	
};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSVIEW (editor, toolbar, action, label, image, isTransparent)
{
	if (image != null)
	{
		var img = document.createElement ('img');
		img.setAttribute ('src', image);
		img.style.width = '74px';
		img.style.height = '30px';
		img.style.verticalAlign = 'middle';
		img.title = label;
		img.style.marginRight = '10px';
		img.style.marginTop = '2px';
	}
	mxEvent.addListener (img, 'click', function (evt)
	{
		if('delete'==action)
			{
			var cells=editor.graph.getSelectionCells();
			for(i=0;i<cells.length;i++)
				{
				if('开始'==cells[i].value)
					{
					Ext.Msg.alert ('提示', '不删除<开始>节点！');
					return false;
					}
				}
			}
		
		editor.execute (action);
	});
	toolbar.appendChild (img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSVIEW (graph, title, content, width, height)
{
	var background = document.createElement ('div');
	background.style.position = 'absolute';
	background.style.left = '0px';
	background.style.top = '0px';
	background.style.right = '0px';
	background.style.bottom = '0px';
	background.style.background = 'black';
	mxUtils.setOpacity (background, 50);
	document.body.appendChild (background);
	
	if (mxClient.IS_IE)
	{
		new mxDivResizer (background);
	}
	
	var x = Math.max (0, document.body.scrollWidth / 2 - width / 2);
	var y = Math.max (10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
	var wnd = new mxWindow (title, content, x, y, width, height, false, true);
	wnd.setClosable (true);
	
	// Fades the background out after after the window has been closed
	wnd.addListener (mxEvent.DESTROY, function (evt)
	{
		graph.setEnabled (true);
		mxEffects.fadeOut (background, 50, true, 10, 30, true);
	});
	
	graph.setEnabled (false);
	graph.tooltipHandler.hide ();
	wnd.setVisible (true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSVIEW (graph, label, styleName,w,h)
{
	
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			v1 = graph.insertVertex (parent, null, label, w, h, 50, 25, styleName);
			v1.setConnectable (true);
		}
		finally
		{
			model.endUpdate ();
		}
//		graph.setSelectionCell (v1);

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSVIEW (graph, sidebar, label, image, styleName,phaseId)
{
	// Function that is executed when the image is dropped on
	// the graph. The cell argument points to the cell under
	// the mousepointer if there is one.
	var funct = function (graph, evt, cell, x, y)
	{
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			// NOTE: For non-HTML labels the image must be displayed via the style
			// rather than the label markup, so use 'image=' + image for the style.
			// as follows: v1 = graph.insertVertex(parent, null, label,
			// pt.x, pt.y, 120, 120, 'image=' + image);
			actNameCountMapGFSSVIEW[styleName]= ++actNameCountMapGFSSVIEW[styleName];
			v1 = graph.insertVertex (parent, null, label+actNameCountMapGFSSVIEW[styleName], x, y, 90, 32, styleName);
			v1.setConnectable (true);
			v1.phaseId=phaseId;
			
		}
		finally
		{
			model.endUpdate ();
		}
		graph.setSelectionCell (v1);
	}

	// Creates the image which is used as the sidebar icon (drag source)
	var img = document.createElement ('img');
	img.setAttribute ('src', image);
	img.style.width = '80px';
	img.style.height = '32px';
	img.title = label;
	img.style.marginLeft = '10px';
	img.style.marginBottom = '15px';
	
	sidebar.appendChild (img);
	
	var dragElt = document.createElement ('div');
	dragElt.style.border = 'dashed black 1px';
	dragElt.style.width = '120px';
	dragElt.style.height = '120px';
	
	// Creates the image which is used as the drag icon (preview)
	var ds = mxUtils.makeDraggable (img, graph, funct, dragElt, 0, 0, true, true);
	ds.setGuidesEnabled (true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSVIEW (graph)
{
	
	var style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
	style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
	style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTSTYLE] = 0;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
	style[mxConstants.STYLE_RESIZABLE] = '0';//不可缩放
	graph.getStylesheet ().putDefaultVertexStyle (style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('beginStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('endStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet ().putCellStyle ('cmdStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
	style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
	style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_STARTSIZE] = '30';
	style[mxConstants.STYLE_FONTSIZE] = '16';
	style[mxConstants.STYLE_FONTSTYLE] = 1;
	graph.getStylesheet ().putCellStyle ('group', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_FONTCOLOR] = '#774400';
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_FONTSIZE] = '10';
	style[mxConstants.STYLE_FONTSTYLE] = 2;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
	graph.getStylesheet ().putCellStyle ('port', style);
	
	style = graph.getStylesheet ().getDefaultEdgeStyle ();
	style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_STROKEWIDTH] = '1';
	style[mxConstants.STYLE_STROKECOLOR] = '#595758';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_EDGE] =mxConstants.EDGESTYLE_ELBOW;
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('scriptServiceStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('usertaskStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);


};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSVIEW() {
	graphGFSSVIEW.getModel().beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag="+flagGFSSVIEW+"&instanceID="+parent.iidGFSSVIEW));
		var dec = new mxCodec(doc);
		dec.decode(doc.getDocumentElement(), graphGFSSVIEW.getModel());
	} finally {
		// Updates the display
		graphGFSSVIEW.getModel().endUpdate();
	}
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSVIEW ()
{
	configwindowFlowGFSSVIEW = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/mxgraph/flowCustomizedWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSVIEW ()
{
	configwindowFlowGFSSVIEW = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : false,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedExecScriptWindow.jsp",
	        params :
	        {
	            flag : flagGFSSVIEW
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function openEditScriptWindwGFSSVIEW ()
{
	var h = 160;
	var w = 600;
	if(isScriptConvertToFlowGFSSVIEW) {
		h = 130;
	}
	
	configwindowFlowGFSSVIEW = Ext.create ('Ext.window.Window', {
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    padding: '15 0 0 0',
	    width : w,
	    height : h,
	    loader : {
	        url : "page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSVIEW ()
{
	configwindowFlowGFSSVIEW = Ext.create ('Ext.window.Window',
	{
	    title : '人工提醒配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : 580,
	    height : 215,
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/GFSSVIEW/scriptServiceUTWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSVIEW ()
{
	//更新名后续刷新才能显示
	graphGFSSVIEW.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSVIEW ()
{
	mainGFSSVIEW (document.getElementById ('graphContainerGFSSVIEW'), document.getElementById ('outlineContainerGFSSVIEW'), document
            .getElementById ('toolbarContainer'), document.getElementById ('sidebarContainerGFSSVIEW'), document
            .getElementById ('statusContainerGFSSVIEW'));
	
}
/**
 * 判断新表中是否存在作业名
 */
function checkInstanceNameExisitInNewVersionGFSSVIEW()
{
	var bussId = parent.bussCbOutSideGFSSVIEW.getValue ();
	var bussTypeId = parent.bussTypeCbOutSideGFSSVIEW.getValue ();
	var instanceName = parent.instanceNameObjOutSideGFSSVIEW.getValue ();
	if(parent.iidGFSSVIEW>0) {
		saveFlowGFSSVIEW();
	} else {
		Ext.Ajax.request (
				{
				    url : 'checkInstanceNameExisitInNewVersionScriptService.do',
				    params :
				    {
				    	bussId: bussId, 
				    	bussTypeId: bussTypeId, 
				    	serviceName : instanceName
				    },
				    method : 'POST',
				    async : false,
				    success : function (response, options)
				    {
				    	if(!Ext.decode (response.responseText).success)
			    		{
			    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    		}
			    	else
			    		{
			    		saveFlowGFSSVIEW();
			    		
			    		}
					    
				    },
				    failure : function (result, request)
				    {
				    }
				});
	}
	

}
/**
 * 判断旧表中是否存在模板名
 */
function checkInstanceNameExisitInOldVersionGFSSVIEW()
{
	Ext.Ajax.request (
			{
			    url : 'checkInstanceNameExisitInOldVersionScriptService.do',
			    params :
			    {
			    	instanceName : parent.instanceNameObjOutSideGFSSVIEW.getValue ()
			    },
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
			    	// warnPath = Ext.decode(response.responseText).warnPath;
			    	if(!Ext.decode (response.responseText).success)
			    		{
			    		Ext.Msg.alert ('提示', "实例名已存在,请更换实例名！");
			    		}
			    	else
			    		{
			    		saveFlowGFSSVIEW();
			    		}
			    },
			    failure : function (result, request)
			    {
			    }
			});

}
/**
 * 保存流程
 */
function saveFlowGFSSVIEW()
{
	var enc = new mxCodec (mxUtils.createXmlDocument ());
	var node = enc.encode (editorGFSSVIEW.graph.getModel ());
	var bussId = parent.bussCbOutSideGFSSVIEW.getValue ();
	var bussTypeId = parent.bussTypeCbOutSideGFSSVIEW.getValue ();
	var bussName = parent.bussCbOutSideGFSSVIEW.getRawValue ();
	var bussTypeName = parent.bussTypeCbOutSideGFSSVIEW.getRawValue ();
	var serviceName = parent.instanceNameObjOutSideGFSSVIEW.getValue();
	var serviceId = parent.iidGFSSVIEW;
	Ext.Ajax.request (
	{
	    url : 'mxgraphSaveForFlowScriptService.do',
	    params :
	    {
	        xmlString : mxUtils.getPrettyXml (node),
	        bussId:bussId,
	        bussTypeId:bussTypeId,
	        bussName:bussName,
	        bussTypeName:bussTypeName,
	        serviceName : parent.instanceNameObjOutSideGFSSVIEW.getValue(),
	        serviceId:parent.iidGFSSVIEW
	    },
	    method : 'POST',
	    async : false,
	    success : function (response, options)
	    {
		    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
		    if(Ext.decode (response.responseText).success==true)
		    	{
		    	parent.iidGFSSVIEW=Ext.decode (response.responseText).serviceId;
			    parent.serviceNameGFSSVIEW=parent.instanceNameObjOutSideGFSSVIEW.getValue();
			    parent.instanceNameObjOutSideGFSSVIEW.setReadOnly(true);
			    parent.bussCbOutSideGFSSVIEW.setReadOnly(true);
			    parent.bussTypeCbOutSideGFSSVIEW.setReadOnly(true);
		    	}
	    },
	    failure : function (result, request)
	    {
	    }
	});
}

function checkDataIsReadyGFSSVIEW () {
	var root2FlowWindow = modelGFSSVIEW.getRoot ();
	var count = modelGFSSVIEW.getChildCount (root2FlowWindow);
	for (var i = 0; i < count; i++)
	{
		var cells = root2FlowWindow.getChildAt (i);
		var counts = cells.getChildCount ();
		for (var j = 0; j < counts; j++)
		{
			var cellss = cells.getChildAt (j);
			// 如果不是连线，则是节点
			if (!modelGFSSVIEW.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
			{
				var chosedAgents = cellss.chosedAgents;
				var actType=cellss.phaseId;
				if('1'==actType){
					var messinfo = cellss.ireminfo;
					if(messinfo==''){
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
						return false;
					}
				}else{
					if(!chosedAgents) {
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
						return false;
					}
				}
			}
		}
	}
	return true;
}

function orgStartDataGFSSVIEW () {
	// 遍历所有节点
	var root2FlowWindow = modelGFSSVIEW.getRoot ();
	var count = modelGFSSVIEW.getChildCount (root2FlowWindow);
	var data = [];
	for (var i = 0; i < count; i++)
	{
		var cells = root2FlowWindow.getChildAt (i);
		var counts = cells.getChildCount ();
		for (var j = 0; j < counts; j++)
		{
			var cellss = cells.getChildAt (j);
			// 如果不是连线，则是节点
			if (!modelGFSSVIEW.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
			{
				var actType=cellss.phaseId;
				var actName=cellss.value;
				if('1'==actType){
					
					var messinfo = cellss.ireminfo;
					if(messinfo==''){
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
						return;
					}else{
						var s = cellss.id + "++!!++"+messinfo+"++!!++++!!++++!!++++!!++++!!++++!!++"+actType+"++!!++"+actName;
						data.push(s);
					}
				}else{
					var chosedAgents = cellss.chosedAgents;
					if(chosedAgents) {
						var scriptParam = cellss.scriptParam;
						var chosedConfigParams = cellss.chosedConfigParams;
						for(var key in chosedConfigParams){
							for(var keyIn in chosedConfigParams[key]){
								if(chosedConfigParams[key][keyIn]==""){
									chosedConfigParams[key][keyIn] = scriptParam[keyIn];
								}
							}
						}
						var startUser = cellss.startUser;
						//var chosedAgentsAndParams = cellss.chosedAgentsAndParams;
						var finalParamters = {};
						for(var i=0;i<chosedAgents.length;i++){
							if(chosedConfigParams.hasOwnProperty(chosedAgents[i])) {
								finalParamters[chosedAgents[i]]=chosedConfigParams[chosedAgents[i]];
							}else{
								finalParamters[chosedAgents[i]]=scriptParam;
							}
						}
						var chosedAgentsAndStartUsers = cellss.chosedAgentsAndStartUsers;
						var chosedAgentsAndDbSources = cellss.chosedAgentsAndDbSources;
						var s = cellss.id + "++!!++" + JSON.stringify(scriptParam) + "++!!++" + chosedAgents.join('pp') + "++!!++" + JSON.stringify(finalParamters) + "++!!++" + JSON.stringify(chosedAgentsAndStartUsers)+ "++!!++" + JSON.stringify(chosedAgentsAndDbSources) + "++!!++" +startUser+"++!!++"+actType+"++!!++"+actName;
						console.log("最终数据："+s);
						data.push(s);
					} else {
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
						return;
					}
				}
				
			}
		}
	}
	data.push("");
	return data;
}

function startFlowGFSSVIEW() {
	// 遍历所有节点
	var root2FlowWindow = modelGFSSVIEW.getRoot ();
	var count = modelGFSSVIEW.getChildCount (root2FlowWindow);
	var serviceId = parent.iidGFSSVIEW;
	var data = [];
	for (var i = 0; i < count; i++)
	{
		var cells = root2FlowWindow.getChildAt (i);
		var counts = cells.getChildCount ();
		for (var j = 0; j < counts; j++)
		{
			var cellss = cells.getChildAt (j);
			// 如果不是连线，则是节点
			if (!modelGFSSVIEW.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
			{
				var actType=cellss.phaseId;
				var actName=cellss.value;
				if('1'==actType){
					var messinfo = cellss.ireminfo;
					if(messinfo==''){
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
						return;
					}else{
						var s = cellss.id + "++!!++"+messinfo+"++!!++++!!++++!!++++!!++++!!++++!!++"+actType+"++!!++"+actName;
						data.push(s);
					}
				}else{
					var chosedAgents = cellss.chosedAgents;
					if(chosedAgents) {
						var scriptParam = cellss.scriptParam;
						var chosedConfigParams = cellss.chosedConfigParams;
						for(var key in chosedConfigParams){
							for(var keyIn in chosedConfigParams[key]){
								if(chosedConfigParams[key][keyIn]==""){
									chosedConfigParams[key][keyIn] = scriptParam[keyIn];
								}
							}
						}
						var startUser = cellss.startUser;
						var finalParamters = {};
						for(var i=0;i<chosedAgents.length;i++){
							if(chosedConfigParams.hasOwnProperty(chosedAgents[i])) {
								finalParamters[chosedAgents[i]]=chosedConfigParams[chosedAgents[i]];
							}else{
								finalParamters[chosedAgents[i]]=scriptParam;
							}
						}
						//var chosedAgentsAndParams = cellss.chosedAgentsAndParams;
						var chosedAgentsAndStartUsers = cellss.chosedAgentsAndStartUsers;
						var chosedAgentsAndDbSources = cellss.chosedAgentsAndDbSources;
						var s = cellss.id + "++!!++" + scriptParam + "++!!++" + chosedAgents.join('pp') + "++!!++" + JSON.stringify(finalParamters) + "++!!++" + JSON.stringify(chosedAgentsAndStartUsers)+ "++!!++" + JSON.stringify(chosedAgentsAndDbSources) + "++!!++" +startUser+"++!!++"+actType+"++!!++"+actName;
						data.push(s);
					} else {
						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
						return;
					}
				}
				
			}
		}
	}
	data.push("");
	Ext.Ajax.request({
		 url :'startScriptServiceFlow.do',
			method: 'POST',
			params:{
				serviceId:serviceId,
				data:data,
				flag:parent.flagGFSSVIEW
			},
			success: function ( response, options) 
			{
		        var success = Ext.decode(response.responseText).success;
		        var message = Ext.decode(response.responseText).message;
		        if (success) {
		             Ext.MessageBox.show({
		                 title : "提示",
		                 msg : message,
		                 buttonText: {
		                     yes: '确定'
		                 },
		                 buttons: Ext.Msg.YES
		               });
		             
		           }
			},
			failure: function ( result, request){
	             Ext.MessageBox.show({
	                 title : "提示",
	                 msg : "启动失败",
	                 buttonText: {
	                     yes: '确定'
	                 },
	                 buttons: Ext.Msg.YES
	               });
			}
		
	 });

}
