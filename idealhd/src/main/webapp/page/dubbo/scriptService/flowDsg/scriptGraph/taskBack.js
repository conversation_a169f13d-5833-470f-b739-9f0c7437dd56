/**
 * 
 */
Ext.define('page.dubbo.scriptService.flowDsg.scriptGraph.taskBack', {
    extend: 'Ext.form.Panel',
    alias: 'widget.taskBack',

    requires: [
        'Ext.form.ComboBox',
        'Ext.form.TextArea',
        'Ext.button.Button'
    ],

    height: 150,
    title: '',
    frame : true,
	buttonAlign : "left",
	
	layout: 'hbox',
	
	serviceInfo : {},
	initOperBtn: function(n){
		var me = this;
//		n = parseInt(n);
		var editerName = me.editerName;
		var jspParms = me.jspParms;
		if(GRAPHS[editerName]!=undefined)
			GRAPHS[editerName].controlSidbar('view');
		if(GRAPHS[jspParms.rootEditer]!=undefined && GRAPHS[jspParms.rootEditer].actionType !='dbbackForExec')
		{
			GRAPHS[jspParms.rootEditer].actionType = 'view';
		}
	},
	
    initComponent: function() {
        var me = this;
        var jspParms = me.jspParms;
        var serviceInfo = me.serviceInfo;
         
        if(jspParms.scriptLevelDisplay==undefined)
        {
        	jspParms.scriptLevelDisplay = '';
        }
        
        function backToDbCheckBtnFn(){
	    	if(jspParms.from==1) {
	    		messageWindow1.close();
	    	} else {
				messageWindow.getLoader ().load (
						{
							url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
							autoLoad : true,
							scripts : true
						});
				messageWindow.setTitle ('待办事项');
			}
        }
        Ext.define('AuditorModel', {
		    extend: 'Ext.data.Model',
		    fields : [ {
		      name : 'loginName',
		      type : 'string'
		    }, {
		      name : 'fullName',
		      type : 'string'
		    }]
		  });
		
   	 var backToDbCheckButton = Ext.create('Ext.Button', {
   		    text : '返回',
   		    margin : '0 0 0 5',
   		    textAlign : 'center',
   		    hidden: jspParms.from ==66?true:false,
   		    handler : backToDbCheckBtnFn
   	  });
   	 
   	 var dbtermiButton = Ext.create('Ext.Button', {
		 text : '终止',
		 margin : '0 0 0 5',
		 textAlign : 'center',
		 handler : function() {
			 Ext.Ajax.request (
						{
						    url : 'operWorkitemByiidForSsPublish.do',
						    method : 'POST',
						    params :
						    {
						        istateForQuery : 6,
						        iidForQuery : jspParms.iworkItemid
						    },
						    success : function (response, opts)
						    {
							    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
							    {
							    	if(jspParms.from==1) {
							    		messageWindow1.close();
							    		destroyRubbish(); //销毁本页垃圾
							    		contentPanel.getLoader().load({
							    			url: 'pandect1.do',
							    			scripts: true});
							    	} else if(jspParms.from==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
				    					    		messageWindow_ssq.close();
				    					    		forword('initScriptDoublePersonQueryMenu.do',jspParms.title);
				    			    }else {
							    		messageWindow.getLoader ().load (
											{
											    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
											    autoLoad : true,
											    scripts : true
											});
											messageWindow.setTitle ('待办事项');
							    	}
							    });
						    }
						});
		 }
	 });
   	 
     var viewBasicInfoButton = Ext.create("page.dubbo.scriptService.flowDsg.scriptGraph.btn.baseInfoBtn", {
     	serviceInfo : serviceInfo
     });
   	 
	 /** 打回原因输入框* */
		var backInfo = Ext.create ('Ext.form.field.TextArea', {
			fieldLabel : '打回原因',
	        name : 'backInfo',
	        flex: 1,
	        labelAlign:'right',
	        padding : '5 0 5 0',
	        height:60,
	        width : '100%',
	        labelWidth: 85,
	        labelSepartor : "：",
	        readOnly: jspParms.actionType=='dbbackForExec',
	        value: jspParms.backInfo
		});
		
		var isTimerTaskBack = Ext.create('Ext.form.field.Checkbox', {
        	checked : jspParms.isTimetask==1?true:false,
        	fieldLabel: '定时任务',
        	labelAlign : 'right',
        	labelWidth : 60,
    		margin:'5 0 5 0',
    		hidden: !(jspParms.isScriptConvertToFlow=='true'),
    		change:function(el,checked){
				             if(checked){
				             	cycleExecCronText.show(); 
				             	selectCronButton.show();
				             }else{
				             	cycleExecCronText.hide();
				             	selectCronButton.hide();
				             }
				        }
        });
		  /** 选择生成周期表达式按钮 **/
		var selectCronButton = Ext.create ("Ext.Button",
		{
			id : 'selectCronButton_id',
		    //cls : 'Common_Btn',
		    text : "选择",
		    // padding : '5 0 5 0',
		    hidden: !(jspParms.isScriptConvertToFlow=='true') || !(isTimerTaskBack.getValue()),
		    handler : selectExecCron
		});
//		var sHiddenPanel1 =  Ext.create('Ext.panel.Panel', {
//		    region : 'north',
//	　		id : 'sHiddenPanel1'   
//		});
//		var sHiddenPanel2 = Ext.create('Ext.panel.Panel', {
//			region : 'south',
//	　		id : 'sHiddenPanel2'   
//		});
		
		function selectExecCron()
		{
			var creatCronWin;
			if (creatCronWin == undefined || !creatCronWin.isVisible()) {
				creatCronWin = Ext.create('Ext.window.Window', {
					title : '定时任务参数设置',
					modal : true,
					id : 'creatCronWin',
					closeAction : 'destroy',
					constrain : true,
					autoScroll : true,
					upperWin : creatCronWin,
					width : contentPanel.getWidth() - 350,
					height : contentPanel.getHeight() - 30,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					loader : {
						url : 'cronMainForSpdb.do',
	//					params : {
	//						sysType : sysType,
	//						state : state,
	//						errorTaskId : errorTaskId,
	//						pageType : pageType
	//					},
						autoLoad : true,
						autoDestroy : true,
						scripts : true
					}
				});
			}
			creatCronWin.show();
		}
		
        var cycleExecCronText = new Ext.form.TextField ({
		    fieldLabel : '执行时间',
		    labelWidth : 60,
		    labelAlign : 'right',
		    id : 'cycleExecCronText',
			name: 'cycleExecCronText',
			readOnly : true,
		    value:jspParms.taskTime,
		    padding : '5 0 5 0',
		    width : 'auto',
		    hidden: !(jspParms.isScriptConvertToFlow=='true')
		});
		
		
        var isSaveTemplateCk = Ext.create('Ext.form.field.Checkbox', {
    		checked : false,
    		boxLabel: '是否保存为常用任务',
    		margin:'3 5 0 5'
    	});
		var dbSubButton = Ext.create('Ext.Button', {

		    text : '提交',
		    margin : '0 0 0 5',
		    textAlign : 'center',
		    handler : function() {
		    	
		    	var isReady = ScriptUtils.checkJobConfigIsOK(jspParms.editerName, jspParms.serviceId);
		    	
		    	if(isTimerTaskBack.getValue()==false){
		    		jspParms.isTimetask=false;
		    		jspParms.taskTime='';
		    	}else{
		    		if(isTimerTaskBack.getValue()==true){
		    			if(cycleExecCronText.getValue()==null||''==cycleExecCronText.getValue()){
		    				Ext.Msg.alert ('提示', '执行时间不能为空!')
		    			}else{
		    				jspParms.isTimetask=isTimerTaskBack.getValue();
		    		    	jspParms.taskTime=cycleExecCronText.getValue();
		    			}
		    		}
		    	}
		    	jspParms.isSaveTemplate=isSaveTemplateCk.getValue();
		    	
		    	if(isReady) {
//		    		var templateData = ScriptUtils.getFlowCustomTemplateData(jspParms.customId,jspParms.flag);
//	    			var taskNameText = templateData.taskNameText;
//	    			var audiLoginUser = templateData.audiLoginUser;
//	    			
//	    			jspParms.taskName = taskNameText;
//	    			jspParms.audiLoginUser = audiLoginUser;
	    			
	    			if(null!=jspParms.planId && jspParms.planId!='' && jspParms.planId!='undefined' && null!=jspParms.scenceId && jspParms.scenceId!='' && jspParms.scenceId!='undefined' && null!=jspParms.stepId && jspParms.stepId!='' && jspParms.stepId!='undefined'){               
	    				var planFormInfoWin = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.planFormInfoWin', {
		    				jspParms : jspParms
		    			});
	    				planFormInfoWin.show();
	    				
					}else{
						var configDoubleCheckInfoWin = Ext.create('page.dubbo.scriptService.flowDsg.scriptGraph.configDoubleCheckInfoWin', {
		    				jspParms : jspParms
		    			});
		    			configDoubleCheckInfoWin.show();
					}
		    	}
		    }
		});
		
		if(null!=jspParms.planId && jspParms.planId!='' && jspParms.planId!='undefined' && null!=jspParms.scenceId && jspParms.scenceId!='' && jspParms.scenceId!='undefined' && null!=jspParms.stepId && jspParms.stepId!='' && jspParms.stepId!='undefined'){
			isSaveTemplateCk.hide();
		}
		
	  
		
        Ext.applyIf(me, {
            items: [{
	        	border: false,
	            flex: 2.5,
	            layout: 'vbox',// 垂直
	            items: [isTimerTaskBack,                           
		            {
			            border: false,
			           // flex: 2.5,
			            layout: 'hbox',
			            items: [cycleExecCronText,selectCronButton]
		        	},isSaveTemplateCk] 
	        },
	        {
	            border: false,
	            flex: 3.5,
	            layout: 'vbox',
	            items: [backInfo]
	        }],
            buttons: [viewBasicInfoButton,dbSubButton,dbtermiButton,backToDbCheckButton]
        });
    	
        me.callParent(arguments);
        me.initOperBtn(25);
    }
});