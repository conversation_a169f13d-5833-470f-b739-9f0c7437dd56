Ext.require('Ext.tab.*');

var objUtils = {
		VERSION: '1.0.0',
		myFunction :function (item, index) {
			console.log(index,item);

		},
		renameForFollowCellBranch : function(cell, name){
			var branch = cell.obj.branch;
			branch.forEach(objUtils.myFunction);
			var edges = cell.edges;
			edges.forEach(objUtils.myFunction);
		}
		
	}

Ext.onReady(function() {

	var valuebean = {
		base : {},
		normal : { exp: 'AM', remark:'', retryInterval:'', retryTime: '', timeout: ''},
		input : {},
		output : {text:''},
		branch : {
			branchs : []
		},
		actBean : {}
	};
	
	var cell = parent.GRAPHS[currentEDITOR].currentCell;
	
	var config = cell.getBean();
	
	var tab = Ext.widget('tabpanel', {
		renderTo : 'act_config_div',
		id : 'act_config',
		activeTab : 0,
		width : '100%',
		height : '100%',
		plain : true,
		defaults : {
			autoScroll : true,
			bodyPadding : 10
		},
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'bottom',
			margin : '0 0 5 0',
			items : [ '->', 
				{ xtype : 'button', text : '确定', cls : 'Common_Btn', handler : saveObj , disabled : !actconfig_editEnable}, 
				{ xtype : 'button', text : '应用', cls : 'Common_Btn', handler : applyObj, disabled : !actconfig_editEnable}, 
				{ xtype : 'button', text : '关闭', cls : 'Common_Btn', handler : closeObj }
			]
		} ]
	});
	
	var inputPage = {
			xtype : 'ipanel',
			title : '活动输入',
			id : tab.id + '_input',
			acturl : 'page/grapheditor/webstudio/act/input/input.jsp?parentId=' + tab.id + '_input'
		};
	
	var basePage = {
			xtype : 'ipanel',
			title : '基础信息',
			id : tab.id + '_base',
			isLoaded : true,
			acturl : 'page/grapheditor/webstudio/act/base/base.jsp?parentId=' + tab.id + '_base'
	};
	var normalPage = {
			xtype : 'ipanel',
			title : '通用配置',
			id : tab.id + '_normal',
			acturl : 'page/grapheditor/webstudio/act/base/normal.jsp?parentId=' + tab.id + '_normal'
	};
	
	var outputPage = {
			xtype : 'ipanel',
			title : '活动输出',
			reInit : true,
			id : tab.id + '_output',
			acturl : 'page/grapheditor/webstudio/act/output/output.jsp?parentId=' + tab.id + '_output'
		};
	
	var branchPage = {
			xtype : 'ipanel',
			title : '分支条件',
			id : tab.id + '_branch',
			acturl : 'page/grapheditor/webstudio/act/branch/branch.jsp?parentId=' + tab.id + '_branch',
			oper : 0,
			branvalue : '',
			winHandler : ''
	}
	
	// 保持原有活动tab页面的顺序
	tab.add(basePage);
	if(!cell.bean.normalclose)
		tab.add(normalPage);
	
	if(config.pages!=undefined)
		for (var i = 0; i < config.pages.length; i++) {
			
			var tmpUrl = config.pages[i].configurl;
			if(tmpUrl.indexOf("?") > 0)
				tmpUrl = tmpUrl + "&parentId=" + tab.id + '_' + config.pages[i].pageId;
			else
				tmpUrl = tmpUrl + "?parentId=" + tab.id + '_' + config.pages[i].pageId;
			var configPage = {
					xtype : 'ipanel',
					title : config.pages[i].pageTitle,
					id : tab.id + '_' + config.pages[i].pageId,
					reInit : false,
					acturl : tmpUrl
				};
			tab.add(configPage);
		}
	
	if(cell.bean.input!=undefined&&cell.bean.input!='')
		tab.add(inputPage);
	if(cell.bean.output!=undefined&&cell.bean.output!='')
		tab.add(outputPage);
	if((cell.bean.output!=undefined&&cell.bean.output!='')&&(cell.bean.input!=undefined&&cell.bean.input!=''))
		tab.add(branchPage);
	
	// 设置弹出界面的title
	parent.GRAPHS[currentEDITOR].configWindow.setTitle(cell.value + ' - '
			+ basePage.title);

	var tempBean = parent.GRAPHS[currentEDITOR].currentCell.obj;
	
	
	if (tempBean != undefined && tempBean != 0 && tempBean != '') {
		console.log('tempBean~~~~~~' ,tempBean);
		valuebean = tempBean;
	}
	
	
	function closeObj(){
		parent.GRAPHS[currentEDITOR].configWindow.close();
	}
	
	function saveObj() {
		applyObj();
		closeObj();
	}
	
	function reInitOutput() {
		console.log('bean',bean);
		if(config.pages!=undefined)
		{
			for (var i = 0; i < config.pages.length; i++) {
				var t = tab.getComponent(tab.id + '_' + config.pages[i].pageId);
				if(t.id == 'act_config_ut_property'|| t.id == 'act_config_datamapping' || t.id == 'act_config_outputdefinition' 
					|| t.id == 'act_config_callflowconfig' || t.id == 'act_config_querysql' || t.id == 'act_config_updatesql' )
				{
					//打印活动可以动态定义活动输出，因此只能特殊处理
					var bean = t.childSave();
					if(bean == undefined)
					{
						return ; 
					}
					var adds = bean.gridvalues;
					var nodes = new Array();
					for(var q = 0;q<adds.length;q++)
					{ 
						nodes.push(new INode(adds[q].name+':'+ adds[q].type,true))
					}
					
					if(cell.obj.input == undefined || cell.obj.input.children == undefined)
					{
						console.log('cell.bean:',cell.bean);
						cell.obj.input = clone(cell.bean.input);
					}
					
					if(cell.obj.output == undefined || cell.obj.output.children == undefined)
					{
						cell.obj.output = clone(cell.bean.output);
					}
						
					
					if(t.id == 'act_config_ut_property')
					{
						cell.obj.output.children[cell.obj.output.children.length - 2].children = updateObj(cell.obj.output.children[cell.obj.output.children.length - 1].children,nodes);
						cell.obj.input.children[2].children[0].children = updateObj(cell.obj.input.children[2].children[0].children,nodes);
					}
					if(t.id == 'act_config_datamapping')
					{
						cell.obj.output.children[0].children = updateObj(cell.obj.output.children[0].children,nodes);
						cell.obj.input.children[2].children[0].children = updateObj(cell.obj.input.children[2].children[0].children,nodes);
					}
					if(t.id == 'act_config_outputdefinition')
					{
						cell.obj.output.children[0].children = updateObj(cell.obj.output.children[0].children,nodes);
						cell.obj.input.children[2].children[0].children = updateObj(cell.obj.input.children[2].children[0].children,nodes);
					}
					if(t.id == 'act_config_callflowconfig')
					{
//						cell.obj.output.children = nodes;
						cell.obj.input.children[2].children[8].children = updateObj(cell.obj.input.children[2].children[8].children,nodes);
					}
					if(t.id == 'act_config_querysql' || t.id == 'act_config_updatesql')
					{
						cell.obj.input.children[2].children[1].children = updateObj(cell.obj.input.children[2].children[1].children,nodes);
					}
					
					break; // 每个活动仅有一个能够动态调整活动输入输出参数的位置，所以这里进行break目前看没什么问题
				}
			}
		}
	}
	
	/**
	 * 处理逻辑为：
	 * 1、将新结构作为返回对象
	 * 2、遍历新节点，如果就结构中有，就将就结构中的数据赋值给新节点
	 * 
	 */
	function updateObj(oldobj, newobj){
		if(oldobj == undefined)
			return newobj;
		for(var i=0;i<newobj.length;i++)
		{
			var name = newobj[i].name;
			console.log('name~~~:',name);
			for(var j=0;j<oldobj.length;j++)
			{
				if(oldobj[j].name == name)
				{
					newobj[i].value = oldobj[j].value;
				}
			}
		}
		return newobj;
	}
	
	function isArray(object){
		 return object && typeof object==='object' && 
		   typeof object.length==='number' && 
		   typeof object.splice==='function' && 
		    //判断length属性是否是可枚举的 对于数组 将得到false 
		   !(object.propertyIsEnumerable('length'));
		}
	function clone(obj) {
		var o;
		if (typeof obj == "object") {
			if (obj === null) {
				o = null;
			} else {
				if (isArray(obj)) {
					o = new Array();
					for (var i = 0, len = obj.length; i < len; i++) {
						o.push(clone(obj[i]));
					}
				} else {
					if(obj.constructor.name == 'InputSupper')
						o = new InputSupper();
					else if(obj.constructor.name == 'INode')
						o = new INode();
					else 
						o = new Object();
					for ( var j in obj) {
						o[j] = clone(obj[j]);
					}
				}
			}
		} else {
			o = obj;
		}
		return o;
	}
	
	function applyObj() {
		
		// 更新title
//		var title = parent.GRAPHS[currentEDITOR].configWindow.title;
//		var titles = title.split('-');
//		titles[0] = nameField.getValue();
//		parent.GRAPHS[currentEDITOR].configWindow.setTitle(titles.join("-")); 
		
		var oldname=parent.GRAPHS[currentEDITOR].currentCell.value;
		// 调用各子页的保存方法，获取子页数据对象
		if(config.pages!=undefined)
		{
			
			for (var i = 0; i < config.pages.length; i++) {
				var t = tab.getComponent(tab.id + '_' + config.pages[i].pageId);
				if(t.isLoaded)
				{
					var bean = t.childSave();
					if (typeof (bean) != "undefined" && bean != 0 && bean != '') {
						valuebean.actBean[tab.id + '_' + config.pages[i].pageId] = bean;
					}
				}else{
					if('act_config_taskconfig'== tab.id + '_' + config.pages[i].pageId)
					{
						var ret =  { 
								formvalues:{ tasklevel: 3 ,tasktype: 1},
								gridvalues: [{opername: "FINISH", usernum: "1", operdesc: ""}]
							};
						valuebean.actBean[tab.id + '_' + config.pages[i].pageId] = ret;
					}
				}
			}
		}
		
		// 处理base页的数据
		var basePage = tab.getComponent(tab.id + '_base');
		valuebean.base = basePage.childSave();
		if(cell.value != valuebean.base.name)
		{
			cell.value = valuebean.base.name;
			// 处理后续所有使用该活动的分支条件
			//objUtils.renameForFollowCellBranch(cell);
		}
		
		
		
		if(!cell.bean.normalclose)
		{
			var normalPage = tab.getComponent(tab.id + '_normal');
			if(normalPage.isLoaded)
			{
				valuebean.normal = normalPage.childSave();
			}
		}
		
		// 处理分支页的数据
		var branchPage = tab.getComponent(tab.id + '_branch');
		if(branchPage != undefined && branchPage.isLoaded)
			valuebean.branch = branchPage.childSave();
		
		var inputPage = tab.getComponent(tab.id + '_input');
		if(inputPage != undefined && inputPage.isLoaded)
		{
			valuebean.input = inputPage.childSave();
			inputPage.reInit = true;
		}
		
		var outputPage = tab.getComponent(tab.id + '_output');
		if(outputPage != undefined && outputPage.isLoaded)
		{
			valuebean.output = outputPage.childSave();
			outputPage.reInit = true;
		}
		
//		valuebean.branch = outputPage.childSave();
		
		
		var newname=valuebean.base.name;
		if(oldname!=newname)
		{
			var parCell = parent.GRAPHS[currentEDITOR].currentCell.edges;
			if(parCell){
				//更新存在分支条件的上级节点后续活动名称
				for(var j=0;j<parCell.length;j++){
					var currentCell = parCell[j];
					if(currentCell.source.obj){
						for(var i=0;i<currentCell.source.obj.branch.length;i++){
							var branch = currentCell.source.obj.branch[i];
							if(branch.actid==parent.GRAPHS[currentEDITOR].currentCell.id){
								branch.actname=valuebean.base.name;
							}
						}
					}
				}
				
				var idlist=new Array();
				updateAfterCellRelatedName(parent.GRAPHS[currentEDITOR].currentCell.id,true,parCell,idlist,oldname,newname);
			}
			
			//var aftCell = parent.GRAPHS[currentEDITOR].currentCell.edges;
			// 保存图形上的节点名
			parent.GRAPHS[currentEDITOR].currentCell.value = valuebean.base.name;
			// 使弹出框的title与活动名一致
			parent.GRAPHS[currentEDITOR].configWindow.setTitle(valuebean.base.name+' - 基础信息'); 
			
		}
		
		// 更新父页节点上数据
		parent.GRAPHS[currentEDITOR].currentCell.obj = valuebean;
		
		reInitOutput();
	}
	
	//修改后续节点输入和分支中引用的当前节点名称
	function updateAfterCellRelatedName(curid,isfirst,parCell,idlist,oldname,newname)
	{
		for(var i=0;i<parCell.length;i++){
			var currentCell = parCell[i];
			if(currentCell.target)
			{
				if(!isfirst)
				{
					if(currentCell.target.id==curid)
					{
						continue;
					}
					//continue;
				}

					
				//console.log('update~~~~'+i,currentCell.target.value);
				if(idlist.indexOf(currentCell.target.id)==-1)
				{
					idlist.push(currentCell.target.id);
				}
				else
				{
					if(!isfirst)
					{
						console.log('error~~~~','repeat cell !!!!!!!!!!!!!');
						return;
					}
				}
				
				if(currentCell.target.obj){
					//修改分支条件中名称
					for(var j=0;j<currentCell.target.obj.branch.length;j++){
						var branch = currentCell.target.obj.branch[j];
						branch.condition=branch.condition.split('$'+oldname).join('$'+newname);//.replace(new RegExp("$"+oldname,'g'),"$"+newname);
/*						if(branch.actid==parent.GRAPHS[currentEDITOR].currentCell.id){
							branch.actname=valuebean.base.name;
						}*/
					}
					//修改活动输入中的名称
					if(isfirst&&i==0)
					{
						continue;
					}
					else
					{
						var inputlist=currentCell.target.obj.input;
						if(inputlist.children)
						{
							updateInputRelatedValue(inputlist.children,oldname,newname);
						}
						
					}
				}
				
				var tmpParCell=currentCell.target.edges;
				if(tmpParCell)
				{
					updateAfterCellRelatedName(currentCell.target.id,false,tmpParCell,idlist,oldname,newname);
				}
			}
		}
		
	}
	
	//更新活动输入相关名称
	function updateInputRelatedValue(inputlist,oldname,newname)
	{
		for(var i=0;i<inputlist.length;i++){
			var input = inputlist[i];
			if(input.children)
			{
				updateInputRelatedValue(input.children,oldname,newname);
			}
			else
			{
				if(input.leaf)
				{
					if(input.leaf==1)
					{
						input.value=input.value.split('$'+oldname).join('$'+newname);
					}
				}
			}
		}
	}
});
