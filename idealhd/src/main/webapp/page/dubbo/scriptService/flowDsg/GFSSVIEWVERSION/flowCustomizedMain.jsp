<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSVIEWVERSION=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSVIEWVERSION=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSVIEWVERSION=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSVIEWVERSION=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSVIEWVERSION=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSVIEWVERSION=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>

var isScriptConvertToFlowGFSSVIEWVERSION = <%=request.getAttribute("isScriptConvertToFlow")%>;

<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSVIEWVERSION='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSVIEWVERSION='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSVIEWVERSION=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSVIEWVERSION=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSVIEWVERSION='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSVIEWVERSION='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSVIEWVERSION = <%=request.getAttribute("fromType")%>;
var workItemidGFSSVIEWVERSION = <%=request.getAttribute("workItemid")%>;
var fromGFSSVIEWVERSION = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSVIEWVERSION = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSVIEWVERSION = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSVIEWVERSION = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSVIEWVERSION = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSVIEWVERSION=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSVIEWVERSION=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSVIEWVERSION='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSVIEWVERSION='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSVIEWVERSION='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSVIEWVERSION=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSVIEWVERSION=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSVIEWVERSION=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSVIEWVERSION='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSVIEWVERSION='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSVIEWVERSION='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSVIEWVERSION = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSVIEWVERSION = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSVIEWVERSION = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSVIEWVERSION = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSVIEWVERSION = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSVIEWVERSION = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSVIEWVERSION/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSVIEWVERSION" style="width: 100%;height: 100%"></div>
</body>
</html>