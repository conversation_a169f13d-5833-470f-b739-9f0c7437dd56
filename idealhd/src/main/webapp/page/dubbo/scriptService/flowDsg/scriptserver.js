var rowExpanderLogForTestExecGraph;
var scriptmonitorinfoins_storeForTestExecGraph;
var scriptmonitorinfoins_gridForTestExecGraph;
var interVForTestExecGraph = 10;
var interPVForTestExecGraph = 20;
var lastIdForTestExecGraph;
var lastRowIndexForTestExecGraph;
var lastrequestIdForTestExecGraph;
var lastiipForTestExecGraph;
var lastiportForTestExecGraph;
var flagForTestExecGraph = 0; // 0:测试     1:生成
Ext.onReady(function() {
    Ext.define('scriptmonitorinfoinsData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'int'
        }]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true,
        listeners: {
            select: function(me, record, index, eOpts) { // 选择复选框事件
                flowMesshisForTestExec(record.data.iid, index);
            },
            deselect: function(me, record, index, eOpts) { // 取消选择复选框事件
                flowMesshisForTestExec(record.data.iid, index);
            }
        }
    });
    scriptmonitorinfoins_storeForTestExecGraph = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'scriptmonitorinfoinsData',
        proxy: {
            type: 'ajax',
            url: 'getScriptExecList.do?flag=' + flagForTestExecGraph + '&coatid=' + coatidForTestExecGraph,
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        listeners: {
            load: function() {
                flowMesshisRefreshForTestExecGraph(lastIdForTestExecGraph, lastRowIndexForTestExecGraph);
                Ext.Ajax.request({
                    url: "getScriptCoatInfo.do",
                    params: {
                        coatId: coatidForTestExecGraph
                    },
                    success: function(response, opts) {
                        var res = Ext.decode(response.responseText);
                        stateField.setValue(res.state);
                        endTimeField.setValue(res.endTime);
                        runTimeField.setValue(res.runTime + " 秒");
//                        $("#scriptState").html(res.state);
//                        $("#scriptEndTime").html(res.endTime);
//                        $("#scriptRunTime").html(res.runTime + " 秒");
                    },
                    failure: function(response, opts) {

                    }
                });
            }
        }
    });

    var scriptmonitorinfoins_columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 5) {
                backValue = '<span class="Ignore State_Color">忽略</span>';
            } else if (value == 10) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 20) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 30) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 60) {
                backValue = '<span class="Kill_red State_Color">已终止</span>';
            } else if (value == -1) {
                backValue = '<span class="Not_running State_Color">未运行</span>';
            }
            return backValue;
        }
    },
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        flex: 1
    },
    {
        text: 'Agent端口号',
        dataIndex: 'agentPort',
        width: 100
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 180
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 180
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 200,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state');
            var zoomStr = "";
            if (isWinForTestExecGraph != 1) {
                zoomStr = '<a href="javascript:void(0)" onclick="loggerDetailForTestExecGraph(' + iid + ', \'' + record.get('agentIp') + '\', ' + record.get('agentPort') + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_Enlarge"></img>放大</a>&nbsp;&nbsp;';
            }
            zoomStr = '';
            if (state == '30' || state == '40' || state == '50') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForTestExecGraph(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="reTryScriptServerForTestExecGraph(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>重试</a>&nbsp;&nbsp;' + '<a href="javascript:void(0)" onclick="skipScriptServerForTestExecGraph(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else if (state == '-1' || state == '1') {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForTestExecGraph(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '<a href="javascript:void(0)" onclick="skipScriptServerForTestExecGraph(' + iid + ',' + state + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_skip"></img>忽略</a>&nbsp;&nbsp;' + '</span>';
            } else {
                return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="flowMesshisForTestExecGraph(' + iid + ',' + rowIndex + ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;' + zoomStr + '</span>';
            }
        }
    }];

    rowExpanderLogForTestExecGraph = Ext.create('Ext.grid.plugin.RowExpander', {
        expandOnDblClick: false,
        expandOnEnter: false,
        rowBodyTpl: ['<div id="stephisForTestExecGraph{iid}">', '<pre  onselectstart="return true" id="steptextareahisForTestExecGraph{iid}"  class="monitor_desc"></pre>', '&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;<span class="switch_span">自动刷新 &nbsp;<input type="text" value="10" style="width:35px;" id="rowFreshIdForTestExecGraph" name="rowFreshIdForTestExecGraph" >&nbsp;秒</span>', '&nbsp;&nbsp;&nbsp;<input type="button" value="刷新" onclick="loadShelloutputhisForTestExecGraph({iid},\'{agentIp}\',{agentPort})" class="Common_Btn Monitor_Btn">', '&nbsp;&nbsp;&nbsp;<input type="button" value="终止" onclick="scriptServerStopForTestExecGraph({iid},{state})" class="Common_Btn Monitor_Btn">', '</div>']
    });
    
    var pageFreshTime = new Ext.form.field.Number({
    	width: 50,
        minValue: 20,
        name: "pageFreshTime",
        value: interPVForTestExecGraph
    });
    
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: scriptmonitorinfoins_storeForTestExecGraph,
        dock: 'bottom',
        displayInfo: true,
        items: [{
            xtype: "label",
            text: "自动刷新"
        },
        pageFreshTime,
        {
            xtype: "label",
            text: "  秒"
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '刷新',
            listeners: {
                click: function() {
                    if (refreshObjForTestExecGraph) {
                        clearInterval(refreshObjForTestExecGraph);
                    }
                    refreshPage();
                    // var interValue =
                    // document.getElementById('pageFreshTime').value;
                    var interValue = pageFreshTime.getValue();
                    interPVForTestExecGraph = interValue;
                    if (interPVForTestExecGraph < 20) {
                        interPVForTestExecGraph = 20;
                    }
                    refreshObjForTestExecGraph = setInterval(refreshPage, interPVForTestExecGraph * 1000);
                }
            }
        },
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '终止',
            listeners: {
                click: function() {
                    var data = getCHKBoxIds();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                        function(button, text) {
                            if (button == "yes") {
                                if (data == '-1') {
                                    Ext.Msg.alert('提示', '操作执行成功!');
                                    scriptmonitorinfoins_storeForTestExecGraph.reload();
                                    return;
                                }
                                Ext.MessageBox.wait("数据处理中...", "提示");
                                Ext.Ajax.request({
                                    url: 'scriptServiceShellKill.do',
                                    params: {
                                        flag: flagForTestExecGraph,
                                        insIds: data
                                    },
                                    method: 'POST',
                                    success: function(response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        // 当后台数据同步成功时
                                        if (success) {
                                            scriptmonitorinfoins_storeForTestExecGraph.reload();
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }
        }]
    });
    function getCHKBoxIds() {
        var ids = "";
        var records = scriptmonitorinfoins_gridForTestExecGraph.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20'  && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    
    var sName = new Ext.form.field.Display({
		fieldLabel: '服务名称',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right',
        value: serviceNameForTestExecGraph
	});
    
    var stateField = new Ext.form.field.Display({
    	fieldLabel: '执行结果',
		labelWidth : 70,
		padding : '5',
		width : '33%',
        labelAlign : 'right'
    });
    
    var startUserField = new Ext.form.field.Display({
    	fieldLabel: '启动人',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startUserForTestExecGraph
    });
    
    var startTimeField = new Ext.form.field.Display({
    	fieldLabel: '开始时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: startTimeForTestExecGraph
    });
    
    var endTimeField = new Ext.form.field.Display({
    	fieldLabel: '结束时间',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right',
    	value: endTimeForTestExecGraph
    });
    
    var runTimeField = new Ext.form.field.Display({
    	fieldLabel: '总耗时',
    	labelWidth : 70,
    	padding : '5',
    	width : '33%',
    	labelAlign : 'right'
    });
    
    var info_form = Ext.create('Ext.form.Panel', {
    	region:'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [sName, stateField, startUserField]
		},
		{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [startTimeField, endTimeField, runTimeField]
		}]
        
    });
    
    scriptmonitorinfoins_gridForTestExecGraph = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	store: scriptmonitorinfoins_storeForTestExecGraph,
    	autoScroll: true,
        border: true,
        columnLines: true,
        columns: scriptmonitorinfoins_columns,
        bbar: pageBar,
        selModel: selModel,
        plugins: [rowExpanderLogForTestExecGraph],

        viewConfig: {
            getRowClass: function(record, rowIndex, rowParams, arriveStore) {
                /*
						 * var cls = ''; if(record.data.state==10){ cls =
						 * 'row_Blue'; }else if(record.data.state==20){ cls =
						 * 'row_Green'; }else if(record.data.state==30){ cls =
						 * 'row_Red'; }else if(record.data.state==60){ cls =
						 * 'row_Gray'; } else { cls = 'row_Gray'; } return cls;
						 */
                return 'norowexpandblah';
            }
        }/*,

        listeners: {
            itemclick: function(a, record, item, index, e, eOpts) {
                rowExpanderLogForTestExec.toggleRow(index, record);
            }
        }*/
    });

    scriptmonitorinfoins_gridForTestExecGraph.view.on('expandBody',
    function(rowNode, record, expandRow, eOpts) {
        interVForTestExecGraph = 10;
        if (Ext.isIE) {
            document.getElementById('rowFreshIdForTestExecGraph').innerText = interVForTestExecGraph;
        } else {
            document.getElementById('rowFreshIdForTestExecGraph').innerHTML = interVForTestExecGraph;
        }
        loadShelloutputhisForTestExecGraph(record.get('iid'), record.get('agentIp'), record.get('agentPort'));
        // refreshObjShellOutputForTestExecGraph = setInterval(function() {
        // loadShelloutputhisForTestExecGraph(record.get('iid'),
        // record.get('agentIp'), record.get('agentPort'));
        // }, 1000);
    });
    scriptmonitorinfoins_gridForTestExecGraph.view.on('collapsebody', function(rowNode, record, expandRow, eOpts) {
        lastIdForTestExecGraph = 0;
        lastRowIndexForTestExecGraph = 0;
        if (refreshObjShellOutputForTestExecGraph) {
            clearInterval(refreshObjShellOutputForTestExecGraph);
        }
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "switchruninfoins_div_for_test_exec_graph",
        layout: 'border',
        width : '100%',
        height :contentPanel.getHeight() - 38,
        border: false,
        bodyPadding: 5,
        items: [info_form, scriptmonitorinfoins_gridForTestExecGraph]
    });

    function refreshPage() {
        // if (refreshObjShellOutputForTestExec) {
        // clearInterval(refreshObjShellOutputForTestExec);
        // }
    	if(contentPanel.getLoader().url=='monitorHomePageScriptService.do') {
    		scriptmonitorinfoins_storeForTestExecGraph.reload();
    	}
    }

    if (refreshObjForTestExecGraph) {
        clearInterval(refreshObjForTestExecGraph);
    }
    refreshObjForTestExecGraph = setInterval(refreshPage, interPVForTestExecGraph * 1000);

});

function flowMesshisForTestExecGraph(iruninfoinsid, rowIndex) {
    lastIdForTestExecGraph = iruninfoinsid;
    lastRowIndexForTestExecGraph = rowIndex;
    var record = scriptmonitorinfoins_storeForTestExecGraph.getAt(rowIndex);
    var records = scriptmonitorinfoins_storeForTestExecGraph.getRange(0, scriptmonitorinfoins_storeForTestExecGraph.getCount());
    for (var i = 0; i < records.length; i++) {
        if (i != rowIndex && rowExpanderLogForTestExecGraph.recordsExpanded[records[i].internalId]) {
            rowExpanderLogForTestExecGraph.toggleRow(i, records[i]);
        }
    }
     var record = scriptmonitorinfoins_storeForTestExecGraph.getAt(rowIndex);
     rowExpanderLogForTestExecGraph.toggleRow(rowIndex, record);
}
function loggerDetailForTestExecGraph(iid, agentIp, agentPort) {
    if (refreshObjShellOutputForTestExecGraph) {
        clearInterval(refreshObjShellOutputForTestExecGraph);
    }
    if (refreshObjForTestExecGraph) {
        if (refreshObjShellOutputForTestExecGraph) {
            clearInterval(refreshObjShellOutputForTestExecGraph);
        }
        clearInterval(refreshObjForTestExecGraph);
    }
    contentPanel.getLoader().load({
        url: "forwardscriptserverLogger.do",
        scripts: true,
        params: {
            instanceId: iid,
            agentIp: agentIp,
            agentPort: agentPort,
//            flowId: flowId,
            coatId: coatidForTestExecGraph,
            flag: flagForTestExecGraph
        }
    });
}

function flowMesshisRefreshForTestExecGraph(iruninfoinsid, rowIndex) {
    if (iruninfoinsid == null || iruninfoinsid == '') return;
    var record = scriptmonitorinfoins_storeForTestExecGraph.getAt(rowIndex);
    var records = scriptmonitorinfoins_storeForTestExecGraph.getRange(0, scriptmonitorinfoins_storeForTestExecGraph.getCount());
    var rowFreshValue = document.getElementById('rowFreshIdForTestExecGraph').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForTestExecGraph = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForTestExecGraph').innerText = interVForTestExecGraph;
    } else {
        document.getElementById('rowFreshIdForTestExecGraph').innerHTML = interVForTestExecGraph;
    }

    rowExpanderLogForTestExecGraph.toggleRow(lastRowIndexForTestExecGraph, records[lastRowIndexForTestExecGraph]);
//    refreshObjShellOutputForTestExecGraph = setInterval(function() {
//    	if(contentPanel.getLoader().url=='monitorHomePageScriptService.do') {
//    		loadShelloutputhisInfoForTestExecGraph(lastrequestIdForTestExecGraph, lastiipForTestExecGraph, lastiportForTestExecGraph);
//    	}
//    },
//    rowFreshValue * 1000);
    // var record = scriptmonitorinfoins_storeForTestExec.getAt(rowIndex);
    // rowExpanderLogForTestExec.toggleRow(rowIndex, record);
}

function loadShelloutputhisForTestExecGraph(requestId, iip, iport) {
    lastrequestIdForTestExecGraph = requestId;
    lastiipForTestExecGraph = iip;
    lastiportForTestExecGraph = iport;
    if (refreshObjShellOutputForTestExecGraph) {
        clearInterval(refreshObjShellOutputForTestExecGraph);
    }
    var rowFreshValue = document.getElementById('rowFreshIdForTestExecGraph').value;
    if (isPositiveNum(rowFreshValue)) {
        if (rowFreshValue <= 10) {
            rowFreshValue = 10;
        }
        interVForTestExecGraph = rowFreshValue;
    }
    if (Ext.isIE) {
        document.getElementById('rowFreshIdForTestExecGraph').innerText = interVForTestExecGraph;
    } else {
        document.getElementById('rowFreshIdForTestExecGraph').innerHTML = interVForTestExecGraph;
    }
    // document.getElementById('rowFreshIdForTestExecGraph').setValue(rowFreshValue / 1000);
    loadShelloutputhisInfoForTestExecGraph(requestId, iip, iport);
    refreshObjShellOutputForTestExecGraph = setInterval(function() {
        loadShelloutputhisInfoForTestExecGraph(requestId, iip, iport);
    },
    rowFreshValue * 1000);
}

function loadShelloutputhisInfoForTestExecGraph(requestId, iip, iport) {
    var surl = "getScriptExecOutput.do";
    var desc = 'steptextareahisForTestExecGraph' + requestId;
    Ext.Ajax.request({
        url: surl,
        params: {
            requestId: requestId,
            agentIp: iip,
            agentPort: iport,
            flag: 0
        },
        success: function(response, opts) {
            var msg = Ext.decode(response.responseText);
            //alert("<html>"+msg.message+"</html>");
            if (Ext.isIE) {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            } else {
                if (msg.success) {
                    document.getElementById(desc).innerHTML = msg.message;
                } else {
                    document.getElementById(desc).innerHTML = msg.message;
                }
            }
        },
        failure: function(response, opts) {
            if (Ext.isIE) {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            } else {
                document.getElementById(desc).innerHTML = '获取执行信息失败';
            }
        }

    });
}
function scriptServerStopForTestExecGraph(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            if (state == '5' || state == '20' || state == '40' || state == '60') {
            	Ext.Msg.alert('提示', "该步骤已经结束，无需终止!");
                scriptmonitorinfoins_storeForTestExecGraph.reload();
                return;
            }
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'scriptServiceShellKill.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_storeForTestExecGraph.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function reTryScriptServerForTestExecGraph(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'retryScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_storeForTestExecGraph.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    })
}
function skipScriptServerForTestExecGraph(requestId, state) {
    Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
    function(btn) {
        if (btn == 'yes') {
            Ext.MessageBox.wait("数据处理中...", "提示");
            Ext.Ajax.request({
                url: 'skipScriptServiceShell.do',
                method: 'POST',
                params: {
                    insIds: requestId,
                    flag: 0
                },
                success: function(response, request) {
                    var success = Ext.decode(response.responseText).success;
                    var message = Ext.decode(response.responseText).message;
                    if (success) {
                        Ext.Msg.alert('提示', message);
                    } else {
                        Ext.Msg.alert('提示', message);
                    }
                    scriptmonitorinfoins_storeForTestExecGraph.reload();
                },
                failure: function(result, request) {
                    secureFilterRs(result, "操作失败！");
                }
            });
        }
    });
}
