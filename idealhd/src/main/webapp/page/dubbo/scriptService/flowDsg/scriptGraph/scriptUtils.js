/**
 * 
 */

var ScriptUtils = {};

ScriptUtils.checkJobConfigIsOK = function(editerName, serviceId) {
	var isJobConfigOk = true;
	Ext.Ajax.request({
		url : 'checkJobConfigIsOK.do',
		method : 'POST',
		async : false,
		params : {
			serviceId : serviceId,
			data : JSON.stringify(actStartInfo[editerName]),
			flag : 0
		},
		success : function(response, options) {
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (!success) {
				isJobConfigOk = false;
				Ext.Msg.alert('提示', message);
			}
		},
		failure : function(result, request) {
			isJobConfigOk = false;
			Ext.Msg.alert('提示', "检查作业配置出现问题！");
		}
	});
	return isJobConfigOk;
}

ScriptUtils.getFlowCustomTemplateData = function(customId,flag){
	var ret = {};
	ret.taskNameText = "";
	ret.audiLoginUser = "";
	Ext.Ajax.request({
        url: 'getFlowCustomTemplateData.do',
        method: 'POST',
        async: false,
        params: {
        	iid: customId,
            flag: flag
        },
        success: function(response, options) {
        	ret.taskNameText = Ext.decode(response.responseText).taskName;
        	ret.audiLoginUser = Ext.decode(response.responseText).audiLoginUser;
            
        },
        failure: function(result, request) {}
    });
	return ret;
}
