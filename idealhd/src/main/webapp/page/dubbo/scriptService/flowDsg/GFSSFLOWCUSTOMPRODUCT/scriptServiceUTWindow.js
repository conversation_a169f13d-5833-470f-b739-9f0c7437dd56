/*******************************************************************************
 * 流程定制维护窗口-提醒任务
 ******************************************************************************/
Ext.onReady(function() {
	var scriptNameStore;
	var utContentObj;
	var stepNameObj;
	
	stepNameObj = Ext.create('Ext.form.field.Text', {
		fieldLabel : '步骤名称'
	});
	utContentObj = Ext.create('Ext.form.field.TextArea', {
		fieldLabel : '提醒内容',
		labelAlign : 'right',
		autoScroll : true
	});

	var formPanel = Ext.create('Ext.form.Panel', {
		region : "north",
		border : false,
		fieldDefaults : {
			labelAlign : 'right',
			width : 560,
			labelWidth : 60
		},
		width : 560 ,
		buttonAlign : 'center',
		items : [ stepNameObj, utContentObj ],

		buttons : [ {
			text : '保存',
			handler : saveFun
		}, {
			text : '返回',
			handler : function() {
				parent.configwindowFlowGFSSFLOWCUSTOMPRODUCT.close();
			}
		} ]
	});
	// 主Panel
	var MainPanel = Ext.create('Ext.panel.Panel', {
		layout : "border",
		renderTo : "scriptServiceUT_DivGFSSFLOWCUSTOMPRODUCT",
		width : '100%',
		height : '100%',
		border : false,
		bodyPadding : 5,
		items : [ formPanel ]
	});
	/** 初始化方法* */
	function initFun() {
		stepNameObj.setValue(parent.cellObjGFSSFLOWCUSTOMPRODUCT.value);
		utContentObj.setValue(parent.cellObjGFSSFLOWCUSTOMPRODUCT.ireminfo);
	}
	function trim(t) {
		t = t.replace(/(^\s*)|(\s*$)/g, "");
		return t.replace(/(^ *)|( *$)/g, "");
	}
	function saveFun() {
		if (stepNameObj.getValue().trim() == '') {
			Ext.Msg.alert('提示', '步骤名称不允许为空!');
			return null;
		}
		if ('开始' == stepNameObj.getValue().trim()
				|| '结束' == stepNameObj.getValue().trim()) {
			Ext.Msg.alert('提示', '步骤名称不可以为<开始>或者<结束>！');
			return null;
		}
		if (utContentObj.getValue().trim() == '') {
			Ext.Msg.alert('提示', '提醒内容不允许为空!');
			return null;
		}
		if (fucCheckLength(utContentObj.getValue().trim()) > 255) {
			Ext.Msg.alert('提示', '提醒内容不允许超过255个字符!');
			return null;
		}
		parent.cellObjGFSSFLOWCUSTOMPRODUCT.ireminfo = utContentObj.getValue().trim();
		parent.cellObjGFSSFLOWCUSTOMPRODUCT.value = stepNameObj.getValue().trim();
		parent.callbackWindwGFSSFLOWCUSTOMPRODUCT();
		parent.configwindowFlowGFSSFLOWCUSTOMPRODUCT.close();
	}
	initFun();
});
