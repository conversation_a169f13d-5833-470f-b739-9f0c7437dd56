/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSWorkTestCoat;
var cellObjGFSSWorkTestCoat;
var phaseDataListGFSSWorkTestCoat;
var editorGFSSWorkTestCoat;
var graphGFSSWorkTestCoat;
var modelGFSSWorkTestCoat;
var cmdNameCountGFSSWorkTestCoat = 1;
var actNameCountMapGFSSWorkTestCoat = {};
actStartInfo['GFSSWorkTestCoat'] = {};

Ext.onReady (function ()
{
	try
	{
		if (configwindowFlowGFSSWorkTestCoat != null)
		{
			configwindowFlowGFSSWorkTestCoat.destroy ();
		}
		if (cellObjGFSSWorkTestCoat != null)
		{
			cellObjGFSSWorkTestCoat.destroy ();
		}
		if (phaseDataListGFSSWorkTestCoat != null)
		{
			phaseDataListGFSSWorkTestCoat= null;
		}
		if (editorGFSSWorkTestCoat != null)
		{
			editorGFSSWorkTestCoat.destroy ();
		}
		if (graphGFSSWorkTestCoat != null)
		{
			graphGFSSWorkTestCoat.destroy ();
		}
		if (modelGFSSWorkTestCoat != null)
		{
			modelGFSSWorkTestCoat=null;
		}
	}
	catch(err)
	{
	}
	
	getPhaseGFSSWorkTestCoat ();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSWorkTestCoat (container, outline, toolbar, sidebar, status)
{
	
	if (!mxClient.isBrowserSupported ())
	{
		Ext.Msg.alert ('提示', '当前浏览器不支持此功能!');
	}
	else
	{
		//自定义连线图标
		mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
		mxConstants.MIN_HOTSPOT_SIZE = 16;
		mxConstants.DEFAULT_HOTSPOT = 1;
		
		// Enables guides
		mxGraphHandler.prototype.guidesEnabled = true;
		
		// Alt disables guides
		mxGuide.prototype.isEnabledForEvent = function (evt)
		{
			return !mxEvent.isAltDown (evt);
		};
		
		// Enables snapping waypoints to terminals
		mxEdgeHandler.prototype.snapToTerminals = true;
		
		// Workaround for Internet Explorer ignoring certain CSS directives
		if (mxClient.IS_QUIRKS)
		{
			document.body.style.overflow = 'hidden';
			new mxDivResizer (container);
			new mxDivResizer (outline);
			new mxDivResizer (toolbar);
			new mxDivResizer (sidebar);
			new mxDivResizer (status);
		}
		
		 editorGFSSWorkTestCoat = new mxEditor ();
		 parent.editorSonGFSSWorkTestCoat=editorGFSSWorkTestCoat;
		 graphGFSSWorkTestCoat = editorGFSSWorkTestCoat.graph;
		 modelGFSSWorkTestCoat = graphGFSSWorkTestCoat.getModel ();
		 
		 graphGFSSWorkTestCoat.setTooltips(false);
		
		// Disable highlight of cells when dragging from toolbar
		graphGFSSWorkTestCoat.setDropEnabled (false);
		
		// Centers the port icon on the target port
		graphGFSSWorkTestCoat.connectionHandler.targetConnectImage = true;
		
		// Does not allow dangling edges
		//连线是否必须连接至节点
		graphGFSSWorkTestCoat.setAllowDanglingEdges (false);
		
		// Sets the graph container and configures the editor
		editorGFSSWorkTestCoat.setGraphContainer (container);
		//键盘热键控制
		var config = mxUtils.load ('mxgraph-master/examples/editors/config/keyhandler-commons.xml')
		        .getDocumentElement ();
		editorGFSSWorkTestCoat.configure (config);
		
		//不允许重复连线
		graphGFSSWorkTestCoat.setMultigraph(false);
		//不允许自己连自己
		graphGFSSWorkTestCoat.setAllowLoops(false);
		
		var group = new mxCell ('Group', new mxGeometry (), 'group');
		group.setVertex (true);
		group.setConnectable (false);
		editorGFSSWorkTestCoat.defaultGroup = group;
		editorGFSSWorkTestCoat.groupBorderSize = 20;
		
		// Disables drag-and-drop into non-swimlanes.
		graphGFSSWorkTestCoat.isValidDropTarget = function (cell, cells, evt)
		{
			return this.isSwimlane (cell);
		};
		
		// Disables drilling into non-swimlanes.
		graphGFSSWorkTestCoat.isValidRoot = function (cell)
		{
			return this.isValidDropTarget (cell);
		}

		// Does not allow selection of locked cells
		graphGFSSWorkTestCoat.isCellSelectable = function (cell)
		{
			return !this.isCellLocked (cell);
		};
		
		// Returns a shorter label if the cell is collapsed and no
		// label for expanded groups
		graphGFSSWorkTestCoat.getLabel = function(cell)
		{
			var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
			var geometry = this.model.getGeometry(cell);
			
			if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null ||
				(geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) &&
				geometry.width >= 2)
			{
				var style = this.getCellStyle(cell);
				var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
				var max = geometry.width / (fontSize * 1.5);
				
				if (max < label.length)
				{
					return label.substring(0, max) + '...';
				}
			}
			
			return label;
		};
		
		// Enables wrapping for vertex labels
		graphGFSSWorkTestCoat.isWrapping = function(cell)
		{
			return this.model.isCollapsed(cell);
		};

		// Disables HTML labels for swimlanes to avoid conflict
		// for the event processing on the child cells. HTML
		// labels consume events before underlying cells get the
		// chance to process those events.
		//
		// NOTE: Use of HTML labels is only recommended if the specific
		// features of such labels are required, such as special label
		// styles or interactive form fields. Otherwise non-HTML labels
		// should be used by not overidding the following function.
		// See also: configureStylesheet.
		graphGFSSWorkTestCoat.isHtmlLabel = function (cell)
		{
			return !this.isSwimlane (cell);
		}

		// Shows a "modal" window when double clicking a vertex.
		graphGFSSWorkTestCoat.dblClick = function (evt, cell)
		{
			
			// Do not fire a DOUBLE_CLICK event here as mxEditor will
			// consume the event and start the in-place editor.
			if (this.isEnabled () && !mxEvent.isConsumed (evt) && cell != null && this.isCellEditable (cell))
			{
				if (this.model.isEdge (cell)) //|| !this.isHtmlLabel (cell))
				{
					//连线编辑
//					this.startEditingAtCell (cell);
				}
				else
				{
					cellObjGFSSWorkTestCoat = cell;
					if(cell.style!='beginStyle'&&cell.style!='endStyle')
						{
						if(cell.style=='scriptServiceStyle')
						{
							if(parent.actionTypeGFSSWorkTestCoat=='exec' || parent.actionTypeGFSSWorkTestCoat=='audi'|| parent.actionTypeGFSSWorkTestCoat=='dbcheckForExec'|| parent.actionTypeGFSSWorkTestCoat=='dbbackForExec') {
								openExecScriptWindwGFSSWorkTestCoat ();
							} else {
								openEditScriptWindwGFSSWorkTestCoat (false);
							}
						} else if(cell.style=='usertaskStyle') {
							openUTWindwGFSSWorkTestCoat ();
						} else if(cell.style=='callflowStyle'){
							if(actionTypeGFSSWorkTestCoat=='exec') {
                        		graphViewStack = new Array();
    							stackFlowView = Ext.create('widget.window', {
    					            title: '详细信息',
    					            closable: true,
    					            closeAction: 'destroy',
    					            width: contentPanel.getWidth(),
    					            minWidth: 350,
    					            height: contentPanel.getHeight(),
    					            draggable: true,
    					            resizable: false,
    					            modal: true,
    					            loader: {
    					            	url : 'flowWinViewer.do', 
    					                params: {
    					                    iid: cell.scriptId,
    					                    flag: 0,
    					                    actionType:'exec',
    					                    pageFrom: 'GFSSWorkTestCoat',
    										isShowInWindow: 1,
    										parentMxIid: cell.mxIid,
    										isStack: true
    					                },
    					                autoLoad: true,
    					                scripts: true
    					            }
    					        }).show();
                        	} else {
                        		openEditScriptWindwGFSSWorkTestCoat (true);
                        	}
		    				return ;
						} else {
							openWindwGFSSWorkTestCoat ();
						}
					}
				}
			}
			
			// Disables any default behaviour for the double click
			mxEvent.consume (evt);
		};
		
		// Enables new connections
		//节点之间可以连接
		graphGFSSWorkTestCoat.setConnectable (true);
		
		// Adds all required styles to the graph (see below)
		//增加样式
		configureStylesheetGFSSWorkTestCoat (graphGFSSWorkTestCoat);
		
		actNameCountMapGFSSWorkTestCoat['scriptServiceStyle'] = 0;
		actNameCountMapGFSSWorkTestCoat['usertaskStyle'] = 0;
		actNameCountMapGFSSWorkTestCoat['callflowStyle'] = 0;
		if(parent.actionTypeGFSSWorkTestCoat!='exec'&&parent.actionTypeGFSSWorkTestCoat!='audi'&&parent.actionTypeGFSSWorkTestCoat!='view'&&parent.actionTypeGFSSWorkTestCoat!='dbcheck'&&parent.actionTypeGFSSWorkTestCoat!='dbback'&&parent.actionTypeGFSSWorkTestCoat!='dbcheckForExec'&&parent.actionTypeGFSSWorkTestCoat!='dbbackForExec') {
			addSidebarIconGFSSWorkTestCoat (graphGFSSWorkTestCoat, sidebar, '基础脚本', 'images/mxgraphImages/basescript.png', 'scriptServiceStyle',0);
			addSidebarIconGFSSWorkTestCoat (graphGFSSWorkTestCoat, sidebar, '人工提醒', 'images/mxgraphImages/ut.png', 'usertaskStyle',1);
			addSidebarIconGFSSWorkTestCoat (graphGFSSWorkTestCoat, sidebar, '作业调用', 'images/mxgraphImages/callflow.png', 'callflowStyle',3);
		}
		
		// Creates a new DIV that is used as a toolbar and adds
		// toolbar buttons.
		var spacer = document.createElement ('div');
		spacer.style.display = 'inline';
		spacer.style.padding = '8px';
		
		// Defines a new export action
		editorGFSSWorkTestCoat.addAction ('export', function (editor, cell)
		{
			var textarea = document.createElement ('textarea');
			textarea.style.width = '400px';
			textarea.style.height = '400px';
			var enc = new mxCodec (mxUtils.createXmlDocument ());
			var node = enc.encode (editor.graph.getModel ());
			textarea.value = mxUtils.getPrettyXml (node);
			showModalWindowGFSSWorkTestCoat (graphGFSSWorkTestCoat, 'XML', textarea, 410, 440);
		});
		
		editorGFSSWorkTestCoat.addAction ('deleteBefore', function (editor, cell)
				{
    			var cells=editor.graph.getSelectionCells();
    			for(i=0;i<cells.length;i++)
				{
    				if(cells[i].style=='beginStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<开始>节点！');
    				return false;
    				}
    				if(cells[i].style=='endStyle')
    				{
    				Ext.Msg.alert ('提示', '不能删除<结束>节点！');
    				return false;
    				}
				}
					editor.execute ('delete');
				});
		
		addToolbarButtonGFSSWorkTestCoat (editorGFSSWorkTestCoat, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
		addToolbarButtonGFSSWorkTestCoat (editorGFSSWorkTestCoat, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
		addToolbarButtonGFSSWorkTestCoat (editorGFSSWorkTestCoat, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
		addToolbarButtonGFSSWorkTestCoat (editorGFSSWorkTestCoat, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);
		
		var outln = new mxOutline (graphGFSSWorkTestCoat, outline);
		
		var splash = document.getElementById ('splashGFSSWorkTestCoat');
		if (splash != null)
		{
			try
			{
				mxEvent.release (splash);
				mxEffects.fadeOut (splash, 100, true);
			}
			catch (e)
			{
				
				// mxUtils is not available (library not loaded)
				splash.parentNode.removeChild (splash);
			}
		}
		
		graphGFSSWorkTestCoat.popupMenuHandler.factoryMethod = function(menu, cell, evt)
		{
			return createPopupMenuGFSSWorkTestCoat(graphGFSSWorkTestCoat, menu, cell, evt);
		};
		
		initFunGFSSWorkTestCoat(graphGFSSWorkTestCoat);
	}
	
};

/**增加右键删除菜单**/
function createPopupMenuGFSSWorkTestCoat(graph, menu, cell, evt)
{
	if (cell != null)
	{
		menu.addItem('删除', 'images/delete.png', function()
		{
			editorGFSSWorkTestCoat.execute ('deleteBefore');
		});
	}
};


/**
 * 初始化方法
 */
function initFunGFSSWorkTestCoat(graph)
{
	if(parent.iidGFSSWorkTestCoat>0)
	{
		//修改将原图加载
		loadGraphGFSSWorkTestCoat(graph);
		if(actionTypeGFSSWorkTestCoat=='exec') {
			Ext.Ajax.request({
				url: 'getFlowTestData.do',
				method: 'POST',
				params: {
					iid: flowIdGFSSWorkTestCoat,
					flag: 0
				},
				success: function(response, options) {
					var dataS = Ext.decode(response.responseText).data;
					actStartInfo['GFSSWorkTestCoat'] = JSON.parse(dataS);
					
					var root2FlowWindow = modelGFSSWorkTestCoat.getRoot ();
            		var count = modelGFSSWorkTestCoat.getChildCount (root2FlowWindow);
            		for (var i = 0; i < count; i++)
            		{
            			var cells = root2FlowWindow.getChildAt (i);
            			var counts = cells.getChildCount ();
            			for (var j = 0; j < counts; j++)
            			{
            				var cellss = cells.getChildAt (j);
            				if (!modelGFSSWorkTestCoat.isEdge (cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle')
            				{
            					cellss.mxIid = parent.iidGFSSWorkTestCoat+":"+cellss.id;
            					if(!actStartInfo['GFSSWorkTestCoat'].hasOwnProperty(cellss.mxIid)) {
            						if(parseInt(cellss.phaseId)==0) {
            							actStartInfo['GFSSWorkTestCoat'][cellss.mxIid] = {
            									'actNo': cellss.id,
            									'actType': parseInt(cellss.phaseId),
            									'actName': cellss.value,
            									'isShutdown': cellss.isShutdown
            							};
            						} else if(parseInt(cellss.phaseId)==1) {
            							actStartInfo['GFSSWorkTestCoat'][cellss.mxIid] = {
            									'actNo': cellss.id,
            									'actType': parseInt(cellss.phaseId),
            									'actName': cellss.value,
            									'message': cellss.ireminfo
            							};
            						}
            					}
            				}
            			}
            		}
				},
				failure: function(result, request) {
					Ext.MessageBox.show({
						title: "提示",
						msg: "获取初始化数据出错！",
						buttonText: {
							yes: '确定'
						},
						buttons: Ext.Msg.YES
					});
				}
				
			});
		}
	}else
		{
		//新建增加开始节点
		addBeginEndCellGFSSWorkTestCoat (graph, "开始","beginStyle",1,1);
		//新建增加结束节点
		addBeginEndCellGFSSWorkTestCoat (graph, "结束","endStyle",150,150);
		}
	
};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSWorkTestCoat (editor, toolbar, action, label, image, isTransparent)
{
	if (image != null)
	{
		var img = document.createElement ('img');
		img.setAttribute ('src', image);
		img.style.width = '74px';
		img.style.height = '30px';
		img.style.verticalAlign = 'middle';
		img.title = label;
		img.style.marginRight = '10px';
		img.style.marginTop = '2px';
	}
	mxEvent.addListener (img, 'click', function (evt)
	{
		if('delete'==action)
			{
			var cells=editor.graph.getSelectionCells();
			for(i=0;i<cells.length;i++)
				{
				if('开始'==cells[i].value)
					{
					Ext.Msg.alert ('提示', '不删除<开始>节点！');
					return false;
					}
				}
			}
		
		editor.execute (action);
	});
	toolbar.appendChild (img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSWorkTestCoat (graph, title, content, width, height)
{
	var background = document.createElement ('div');
	background.style.position = 'absolute';
	background.style.left = '0px';
	background.style.top = '0px';
	background.style.right = '0px';
	background.style.bottom = '0px';
	background.style.background = 'black';
	mxUtils.setOpacity (background, 50);
	document.body.appendChild (background);
	
	if (mxClient.IS_IE)
	{
		new mxDivResizer (background);
	}
	
	var x = Math.max (0, document.body.scrollWidth / 2 - width / 2);
	var y = Math.max (10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
	var wnd = new mxWindow (title, content, x, y, width, height, false, true);
	wnd.setClosable (true);
	
	// Fades the background out after after the window has been closed
	wnd.addListener (mxEvent.DESTROY, function (evt)
	{
		graph.setEnabled (true);
		mxEffects.fadeOut (background, 50, true, 10, 30, true);
	});
	
	graph.setEnabled (false);
	graph.tooltipHandler.hide ();
	wnd.setVisible (true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSWorkTestCoat (graph, label, styleName,w,h)
{
	
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			v1 = graph.insertVertex (parent, null, label, w, h, 50, 25, styleName);
			v1.setConnectable (true);
		}
		finally
		{
			model.endUpdate ();
		}
//		graph.setSelectionCell (v1);

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSWorkTestCoat (graph, sidebar, label, image, styleName,phaseId)
{
	// Function that is executed when the image is dropped on
	// the graph. The cell argument points to the cell under
	// the mousepointer if there is one.
	var funct = function (graph, evt, cell, x, y)
	{
		var parent = graph.getDefaultParent ();
		var model = graph.getModel ();
		
		var v1 = null;
		
		model.beginUpdate ();
		try
		{
			// NOTE: For non-HTML labels the image must be displayed via the style
			// rather than the label markup, so use 'image=' + image for the style.
			// as follows: v1 = graph.insertVertex(parent, null, label,
			// pt.x, pt.y, 120, 120, 'image=' + image);
			actNameCountMapGFSSWorkTestCoat[styleName]= ++actNameCountMapGFSSWorkTestCoat[styleName];
			v1 = graph.insertVertex (parent, null, label+actNameCountMapGFSSWorkTestCoat[styleName], x, y, 90, 32, styleName);
			v1.setConnectable (true);
			v1.phaseId=phaseId;
			
		}
		finally
		{
			model.endUpdate ();
		}
		graph.setSelectionCell (v1);
	}

	// Creates the image which is used as the sidebar icon (drag source)
	var img = document.createElement ('img');
	img.setAttribute ('src', image);
	img.style.width = '80px';
	img.style.height = '32px';
	img.title = label;
	img.style.marginLeft = '10px';
	img.style.marginBottom = '15px';
	
	sidebar.appendChild (img);
	
	var dragElt = document.createElement ('div');
	dragElt.style.border = 'dashed black 1px';
	dragElt.style.width = '120px';
	dragElt.style.height = '120px';
	
	// Creates the image which is used as the drag icon (preview)
	var ds = mxUtils.makeDraggable (img, graph, funct, dragElt, 0, 0, true, true);
	ds.setGuidesEnabled (true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSWorkTestCoat (graph)
{
	
	var style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
	style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
	style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTSTYLE] = 0;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
	style[mxConstants.STYLE_RESIZABLE] = '0';//不可缩放
	graph.getStylesheet ().putDefaultVertexStyle (style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('beginStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('endStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
	graph.getStylesheet ().putCellStyle ('cmdStyle', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
	style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
	style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
	style[mxConstants.STYLE_FONTCOLOR] = '#000000';
	style[mxConstants.STYLE_ROUNDED] = true;
	style[mxConstants.STYLE_OPACITY] = '100';
	style[mxConstants.STYLE_STARTSIZE] = '30';
	style[mxConstants.STYLE_FONTSIZE] = '16';
	style[mxConstants.STYLE_FONTSTYLE] = 1;
	graph.getStylesheet ().putCellStyle ('group', style);
	
	style = new Object ();
	style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
	style[mxConstants.STYLE_FONTCOLOR] = '#774400';
	style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
	style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
	style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
	style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
	style[mxConstants.STYLE_FONTSIZE] = '10';
	style[mxConstants.STYLE_FONTSTYLE] = 2;
	style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
	style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
	graph.getStylesheet ().putCellStyle ('port', style);
	
	style = graph.getStylesheet ().getDefaultEdgeStyle ();
	style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_STROKEWIDTH] = '1';
	style[mxConstants.STYLE_STROKECOLOR] = '#595758';
	style[mxConstants.STYLE_ROUNDED] = false;
	style[mxConstants.STYLE_EDGE] =mxConstants.EDGESTYLE_ELBOW;
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('scriptServiceStyle', style);
	
	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('usertaskStyle', style);

	style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSWorkTestCoat() {
	graphGFSSWorkTestCoat.getModel().beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag="+flagGFSSWorkTestCoat+"&instanceID="+parent.iidGFSSWorkTestCoat));
		var dec = new mxCodec(doc);
		dec.decode(doc.getDocumentElement(), graphGFSSWorkTestCoat.getModel());
	} finally {
		// Updates the display
		graphGFSSWorkTestCoat.getModel().endUpdate();
	}
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSWorkTestCoat ()
{
	configwindowFlowGFSSWorkTestCoat = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/mxgraph/flowCustomizedWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSWorkTestCoat ()
{
	configwindowFlowGFSSWorkTestCoat = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : false,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/GFSSWorkTestCoat/flowCustomizedExecScriptWindow.jsp",
	        params :
	        {
	            flag : flagGFSSWorkTestCoat
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function openEditScriptWindwGFSSWorkTestCoat (type)
{
	var h = contentPanel.getHeight ();
	var w = contentPanel.getWidth ();
	if(parent.actionTypeGFSSWorkTestCoat=='view'||parent.actionTypeGFSSWorkTestCoat=='exec'||parent.actionTypeGFSSWorkTestCoat=='audi'||parent.actionTypeGFSSWorkTestCoat=='dbcheck'||parent.actionTypeGFSSWorkTestCoat=='dbback'||parent.actionTypeGFSSWorkTestCoat=='dbcheckForExec'||parent.actionTypeGFSSWorkTestCoat=='dbbackForExec') {
		h = 130;
		w = 600;
	}
	configwindowFlowGFSSWorkTestCoat = Ext.create ('Ext.window.Window', {
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : w,
	    height : h,
	    loader : {
	        url : "page/dubbo/scriptService/flowDsg/GFSSWorkTestCoat/flowCustomizedEditScriptWindow.jsp",
	        params :
	        {
	            actType : type
	        },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSWorkTestCoat ()
{
	configwindowFlowGFSSWorkTestCoat = Ext.create ('Ext.window.Window',
	{
	    title : '人工提醒配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : 580,
	    height : 215,
	    loader :
	    {
	        url : "page/dubbo/scriptService/flowDsg/GFSSWorkTestCoat/scriptServiceUTWindow.jsp",
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSWorkTestCoat ()
{
	//更新名后续刷新才能显示
	graphGFSSWorkTestCoat.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSWorkTestCoat ()
{
	mainGFSSWorkTestCoat (document.getElementById ('graphContainerGFSSWorkTestCoat'), document.getElementById ('outlineContainerGFSSWorkTestCoat'), document
            .getElementById ('toolbarContainer'), document.getElementById ('sidebarContainerGFSSWorkTestCoat'), document
            .getElementById ('statusContainerGFSSWorkTestCoat'));
	
}

function isJobConfigOk (serviceId, allStartParams) {
	var isJobConfigOk = true;
	Ext.Ajax.request({
		url :'checkJobConfigIsOK.do',
		method: 'POST',
		async: false,
		params:{
			serviceId:serviceId,
			data:JSON.stringify(allStartParams),
			flag: 0
		},
		success: function ( response, options) 
		{
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (!success) {
				isJobConfigOk = false;
				Ext.Msg.alert('提示', message);
			}
		},
		failure: function ( result, request){
			isJobConfigOk = false;
			Ext.Msg.alert('提示', "检查作业配置出现问题！");
		}
	});
	return isJobConfigOk;
}

/**
 * 保存流程
 */
function saveFlowGFSSWorkTestCoat() {
	var enc = new mxCodec (mxUtils.createXmlDocument ());
	var node = enc.encode (editorGFSSWorkTestCoat.graph.getModel ());
	var bussId;
	var bussTypeId;
	var bussName;
	var bussTypeName;
	var serviceName;
	var funcDescText;
	var serviceId = parent.iidGFSSWorkTestCoat;
	
	Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: serviceId
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                serviceName = data.serviceName;
                funcDescText = data.funcDesc;
            }
        },
        failure: function(result, request) {}
    });
	
	var isSaveTemplate = isSaveTemplateCkGFSSWorkTestCoat.getValue();
	if(isSaveTemplate) {
		Ext.MessageBox.prompt('提示', '请输入流程模板名称:', function(btn, text, cfg){
	        if(btn=='ok') {
	        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
	        		var newMsg = '<span style="color:red">流程模板名称不能为空！</span>';
	                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	        	} else {
	        		var customName = Ext.util.Format.trim(text);
	        		Ext.Ajax.request({
	        	        url: 'checkCustomTemplateNameIsExist.do',
	        	        params: {
	        	            customName: customName,
	        	            flag: flagGFSSWorkTestCoat
	        	        },
	        	        async: false,
	        	        method: 'POST',
	        	        success: function(response, options) {
	        	            if (!Ext.decode(response.responseText).success) {
	        	                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
	        	            } else {
	        	            	Ext.Ajax.request ({
    	        				    url : 'mxgraphSaveForFlowScriptService.do',
    	        				    params : {
    	        				        xmlString : mxUtils.getPrettyXml (node),
    	        				        bussId:bussId,
    	        				        bussTypeId:bussTypeId,
    	        				        bussName:bussName,
    	        				        bussTypeName:bussTypeName,
    	        				        serviceName : serviceName,
    	        				        serviceId:serviceId,
    	        				        funcDesc: funcDescText
    	        				    },
    	        				    method : 'POST',
    	        				    async : false,
    	        				    success : function (response, options)
    	        				    {
    	        					    if(Ext.decode (response.responseText).success==true) {
    	        					    	parent.iidGFSSWorkTestCoat=Ext.decode (response.responseText).serviceId;
    	        					    	var message = "作业保存成功！";
    	        					    	
    	        					    	Ext.Ajax.request({
    	    	        	    				url: 'getFlowTestData.do',
    	    	        	    				method: 'POST',
    	    	        	    				async: false,
    	    	        	    				params: {
    	    	        	    					iid: flowIdGFSSWorkTestCoat,
    	    	        	    					flag: 0
    	    	        	    				},
    	    	        	    				success: function(response, options) {
    	    	        	    					var dataS = Ext.decode(response.responseText).data;
    	    	        					    	Ext.Ajax.request({
    	    	        					    		url :'checkJobConfigIsOK.do',
    	    	        					    		method: 'POST',
    	    	        					    		async: false,
    	    	        					    		params:{
    	    	        					    			serviceId:serviceId,
    	    	        					    			data:dataS,
    	    	        					    			flag:0
    	    	        					    		},
    	    	        					    		success: function ( response, options) {
    	    	        					    			var success = Ext.decode(response.responseText).success;
    	    	        					    			if (success) {
    	    	        					    				Ext.Ajax.request({
    	    	    	    		        	                    url: 'saveFlowCustomTemplate.do',
    	    	    	    		        	                    method: 'POST',
    	    	    	    		        	                    params: {
    	    	    	    		        	                    	customName: customName,
    	    	    	    		        	                    	serviceId: serviceId,
    	    	    	    		        	                        data: JSON.stringify(dataS),
    	    	    	    		        	                        flag: parent.flagGFSSWorkTestCoat
    	    	    	    		        	                    },
    	    	    	    		        	                    success: function(response, options) {
    	    	    	    		        	                        var success1 = Ext.decode(response.responseText).success;
    	    	    	    		        	                        var message1 = Ext.decode(response.responseText).message;
    	    	    	    		        	                        if (success1) {
    	    	    	    		        	                        	Ext.Msg.alert('提示', message +'<br>模板保存成功！');
    	    	    	    		        	                        }
    	    	    	    		        	                    },
    	    	    	    		        	                    failure: function(result, request) {
    	    	    	    		        	                    	Ext.Msg.alert('提示', message + "<br>模板保存失败");
    	    	    	    		        	                    }
    	    	    	    		        	                });
    	    	        					    			} else {
    	    	        					    				Ext.Msg.alert('提示', message+"<br>模板保存失败：<br>"+Ext.decode(response.responseText).message);
    	    	        					    			}
    	    	        					    		},
    	    	        					    		failure: function ( result, request){
    	    	        					    			Ext.Msg.alert('提示', "检查作业配置出现问题！");
    	    	        					    		}
    	    	        					    	});
    	    	        	    				},
    	    	        	    				failure: function(result, request) {
    	    	        	    					Ext.Msg.alert('提示', "获取数据失败！");
    	    	        	    				}
    	    	        	    			});
    	        					    } else {
    	        					    	Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
    	        					    }
    	        				    },
    	        				    failure : function (result, request)
    	        				    {
    	        				    }
    	        				});
	        	            }
	        	        },
	        	        failure: function(result, request) {}
	        	    });
	        	}
	        }
	    });
	} else {
		Ext.Ajax.request ({
		    url : 'mxgraphSaveForFlowScriptService.do',
		    params : {
		        xmlString : mxUtils.getPrettyXml (node),
		        bussId:bussId,
		        bussTypeId:bussTypeId,
		        bussName:bussName,
		        bussTypeName:bussTypeName,
		        serviceName : serviceName,
		        serviceId:serviceId,
		        funcDesc: funcDescText
		    },
		    method : 'POST',
		    async : false,
		    success : function (response, options) {
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    if(Ext.decode (response.responseText).success==true) {
			    	parent.iidGFSSWorkTestCoat=Ext.decode (response.responseText).serviceId;
			    }
		    },
		    failure : function (result, request) {
		    	Ext.Msg.alert ('提示', "保存作业失败！");
		    }
		});
	}
}

function startFlowGFSSWorkTestCoat() {
	var serviceId = parent.iidGFSSWorkTestCoat;
	var allStartParams = actStartInfo['GFSSWorkTestCoat'];
	
	if(!isJobConfigOk(serviceId, allStartParams)) {
		return;
	}
	
	var isSaveTemplate = isSaveTemplateCkGFSSWorkTestCoat.getValue();
	if(isSaveTemplate) {
		Ext.MessageBox.prompt('确认启动？', '请输入流程模板名称:', function(btn, text, cfg){
	        if(btn=='ok') {
	        	if(Ext.isEmpty(Ext.util.Format.trim(text))) {
	        		var newMsg = '<span style="color:red">流程模板名称不能为空！</span>';
	                Ext.Msg.show(Ext.apply({}, { msg: newMsg }, cfg));  
	        	} else {
	        		var customName = Ext.util.Format.trim(text);
	        		Ext.Ajax.request({
	        	        url: 'checkCustomTemplateNameIsExist.do',
	        	        params: {
	        	            customName: customName,
	        	            flag: flagGFSSWorkTestCoat
	        	        },
	        	        method: 'POST',
	        	        success: function(response, options) {
	        	            if (!Ext.decode(response.responseText).success) {
	        	                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
	        	            } else {
	        	            	Ext.Ajax.request({
	        						url :'startScriptServiceFlow.do',
	        						method: 'POST',
	        						params:{
	        							serviceId:serviceId,
	        							data:JSON.stringify(allStartParams),
	        							flag:parent.flagGFSSWorkTestCoat
	        						},
	        						success: function ( response, options) 
	        						{
	        							var success = Ext.decode(response.responseText).success;
	        							var message = Ext.decode(response.responseText).message;
	        							if (success) {
	        								Ext.Msg.alert('提示', message);
	        								Ext.Ajax.request({
	        	        	                    url: 'saveFlowCustomTemplate.do',
	        	        	                    method: 'POST',
	        	        	                    params: {
	        	        	                    	customName: customName,
	        	        	                    	serviceId: serviceId,
	        	        	                        data: JSON.stringify(allStartParams),
	        	        	                        flag: parent.flagGFSSWorkTestCoat
	        	        	                    },
	        	        	                    success: function(response, options) {
	        	        	                        var success1 = Ext.decode(response.responseText).success;
	        	        	                        var message1 = Ext.decode(response.responseText).message;
	        	        	                        if (success1) {
	        	        	                        	Ext.Msg.alert('提示', message +'<br>模板保存成功！');
	        	        	                        }
	        	        	                    },
	        	        	                    failure: function(result, request) {
	        	        	                    	Ext.Msg.alert('提示', message + "<br>模板保存失败");
	        	        	                    }
	        	        	                });
	        							}
	        						},
	        						failure: function ( result, request){
	        							Ext.Msg.alert('提示', '启动失败');
	        						}
	        					});
	        	            }
	        	        },
	        	        failure: function(result, request) {}
	        	    });
	        	}
	        }
	    });
	} else {
		Ext.Msg.confirm("确认启动", "确定启动该作业？", function(id){
			if(id=='yes'){
				Ext.Ajax.request({
					url :'startScriptServiceFlow.do',
					method: 'POST',
					params:{
						serviceId:serviceId,
						data:JSON.stringify(allStartParams),
						flag:parent.flagGFSSWorkTestCoat
					},
					success: function ( response, options) 
					{
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if (success) {
							Ext.Msg.alert('提示', message);
						}
					},
					failure: function ( result, request){
						Ext.Msg.alert('提示', '启动失败');
					}
				});
			}
		});
	}
}
