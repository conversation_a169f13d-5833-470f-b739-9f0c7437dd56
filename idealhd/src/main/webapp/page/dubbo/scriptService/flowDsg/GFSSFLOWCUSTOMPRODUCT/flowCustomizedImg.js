/*******************************************************************************
 * 流程定制画板页
 ******************************************************************************/
var configwindowFlowGFSSFLOWCUSTOMPRODUCT;
var cellObjGFSSFLOWCUSTOMPRODUCT;
var phaseDataListGFSSFLOWCUSTOMPRODUCT;
var editorGFSSFLOWCUSTOMPRODUCT;
var graphGFSSFLOWCUSTOMPRODUCT;
var modelGFSSFLOWCUSTOMPRODUCT;
var cmdNameCountGFSSFLOWCUSTOMPRODUCT = 1;
var actNameCountMapGFSSFLOWCUSTOMPRODUCT = {};

Ext.onReady(function() {
    try {
        if (configwindow<PERSON>low<PERSON><PERSON>SFLOWCUSTOMPRODUCT != null) {
            configwindow<PERSON>low<PERSON><PERSON>SFLOWCUSTOMPRODUCT.destroy();
        }
        if (cellObjGFSSFLOWCUSTOMPRODUCT != null) {
            cellObjGFSSFLOWCUSTOMPRODUCT.destroy();
        }
        if (phaseDataListGFSSFLOWCUSTOMPRODUCT != null) {
            phaseDataListGFSSFLOWCUSTOMPRODUCT = null;
        }
        if (editorGFSSFLOWCUSTOMPRODUCT != null) {
            editorGFSSFLOWCUSTOMPRODUCT.destroy();
        }
        if (graphGFSSFLOWCUSTOMPRODUCT != null) {
            graphGFSSFLOWCUSTOMPRODUCT.destroy();
        }
        if (modelGFSSFLOWCUSTOMPRODUCT != null) {
            modelGFSSFLOWCUSTOMPRODUCT = null;
        }
    } catch(err) {}

    getPhaseGFSSFLOWCUSTOMPRODUCT();
});

/**
 * 图形化界面初始化方法
 */
function mainGFSSFLOWCUSTOMPRODUCT(container, outline, toolbar, sidebar, status) {

    if (!mxClient.isBrowserSupported()) {
        Ext.Msg.alert('提示', '当前浏览器不支持此功能!');
    } else {
        //自定义连线图标
        mxConnectionHandler.prototype.connectImage = new mxImage('mxgraph-master/examples/images/connector.gif', 16, 16);
        mxConstants.MIN_HOTSPOT_SIZE = 16;
        mxConstants.DEFAULT_HOTSPOT = 1;

        mxGraphHandler.prototype.guidesEnabled = true;

        mxGuide.prototype.isEnabledForEvent = function(evt) {
            return ! mxEvent.isAltDown(evt);
        };

        mxEdgeHandler.prototype.snapToTerminals = true;

        if (mxClient.IS_QUIRKS) {
            document.body.style.overflow = 'hidden';
            new mxDivResizer(container);
            new mxDivResizer(outline);
            new mxDivResizer(toolbar);
            new mxDivResizer(sidebar);
            new mxDivResizer(status);
        }

        editorGFSSFLOWCUSTOMPRODUCT = new mxEditor();
        parent.editorSonGFSSFLOWCUSTOMPRODUCT = editorGFSSFLOWCUSTOMPRODUCT;
        graphGFSSFLOWCUSTOMPRODUCT = editorGFSSFLOWCUSTOMPRODUCT.graph;
        modelGFSSFLOWCUSTOMPRODUCT = graphGFSSFLOWCUSTOMPRODUCT.getModel();
        
        graphGFSSFLOWCUSTOMPRODUCT.setTooltips(false);

        graphGFSSFLOWCUSTOMPRODUCT.setDropEnabled(false);

        graphGFSSFLOWCUSTOMPRODUCT.connectionHandler.targetConnectImage = true;

        //连线是否必须连接至节点
        graphGFSSFLOWCUSTOMPRODUCT.setAllowDanglingEdges(false);

        editorGFSSFLOWCUSTOMPRODUCT.setGraphContainer(container);
        //键盘热键控制
        var config = mxUtils.load('mxgraph-master/examples/editors/config/keyhandler-commons.xml').getDocumentElement();
        editorGFSSFLOWCUSTOMPRODUCT.configure(config);

        //不允许重复连线
        graphGFSSFLOWCUSTOMPRODUCT.setMultigraph(false);
        //不允许自己连自己
        graphGFSSFLOWCUSTOMPRODUCT.setAllowLoops(false);

        var group = new mxCell('Group', new mxGeometry(), 'group');
        group.setVertex(true);
        group.setConnectable(false);
        editorGFSSFLOWCUSTOMPRODUCT.defaultGroup = group;
        editorGFSSFLOWCUSTOMPRODUCT.groupBorderSize = 20;

        graphGFSSFLOWCUSTOMPRODUCT.isValidDropTarget = function(cell, cells, evt) {
            return this.isSwimlane(cell);
        };

        graphGFSSFLOWCUSTOMPRODUCT.isValidRoot = function(cell) {
            return this.isValidDropTarget(cell);
        }

        graphGFSSFLOWCUSTOMPRODUCT.isCellSelectable = function(cell) {
            return ! this.isCellLocked(cell);
        };

        graphGFSSFLOWCUSTOMPRODUCT.getLabel = function(cell) {
            var label = (this.labelsVisible) ? this.convertValueToString(cell) : '';
            var geometry = this.model.getGeometry(cell);

            if (!this.model.isCollapsed(cell) && geometry != null && (geometry.offset == null || (geometry.offset.x == 0 && geometry.offset.y == 0)) && this.model.isVertex(cell) && geometry.width >= 2) {
                var style = this.getCellStyle(cell);
                var fontSize = style[mxConstants.STYLE_FONTSIZE] || mxConstants.DEFAULT_FONTSIZE;
                var max = geometry.width / (fontSize * 1.5);

                if (max < label.length) {
                    return label.substring(0, max) + '...';
                }
            }

            return label;
        };

        graphGFSSFLOWCUSTOMPRODUCT.isWrapping = function(cell) {
            return this.model.isCollapsed(cell);
        };

        graphGFSSFLOWCUSTOMPRODUCT.isHtmlLabel = function(cell) {
            return ! this.isSwimlane(cell);
        }

        graphGFSSFLOWCUSTOMPRODUCT.dblClick = function(evt, cell) {

            if (this.isEnabled() && !mxEvent.isConsumed(evt) && cell != null && this.isCellEditable(cell)) {
                if (this.model.isEdge(cell)) {} else {
                    cellObjGFSSFLOWCUSTOMPRODUCT = cell;
                    if (cell.style != 'beginStyle' && cell.style != 'endStyle') {
                        if (cell.style == 'scriptServiceStyle') {
                            //直接调用脚本
                            openExecScriptWindwGFSSFLOWCUSTOMPRODUCT();
                        } else if (cell.style == 'usertaskStyle') {
                            //UT提醒任务
                            openUTWindwGFSSFLOWCUSTOMPRODUCT();
                        } else {
                            //模板
                            openWindwGFSSFLOWCUSTOMPRODUCT();
                        }

                    }
                }
            }

            mxEvent.consume(evt);
        };

        //节点之间可以连接
        graphGFSSFLOWCUSTOMPRODUCT.setConnectable(true);

        //增加样式
        configureStylesheetGFSSFLOWCUSTOMPRODUCT(graphGFSSFLOWCUSTOMPRODUCT);

        actNameCountMapGFSSFLOWCUSTOMPRODUCT['scriptServiceStyle'] = 0;
        actNameCountMapGFSSFLOWCUSTOMPRODUCT['usertaskStyle'] = 0;

        var spacer = document.createElement('div');
        spacer.style.display = 'inline';
        spacer.style.padding = '8px';

        editorGFSSFLOWCUSTOMPRODUCT.addAction('export',
        function(editor, cell) {
            var textarea = document.createElement('textarea');
            textarea.style.width = '400px';
            textarea.style.height = '400px';
            var enc = new mxCodec(mxUtils.createXmlDocument());
            var node = enc.encode(editor.graph.getModel());
            textarea.value = mxUtils.getPrettyXml(node);
            showModalWindowGFSSFLOWCUSTOMPRODUCT(graphGFSSFLOWCUSTOMPRODUCT, 'XML', textarea, 410, 440);
        });

        editorGFSSFLOWCUSTOMPRODUCT.addAction('deleteBefore',
        function(editor, cell) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if (cells[i].style == 'beginStyle') {
                    Ext.Msg.alert('提示', '不能删除<开始>节点！');
                    return false;
                }
                if (cells[i].style == 'endStyle') {
                    Ext.Msg.alert('提示', '不能删除<结束>节点！');
                    return false;
                }
            }
            editor.execute('delete');
        });

        addToolbarButtonGFSSFLOWCUSTOMPRODUCT(editorGFSSFLOWCUSTOMPRODUCT, status, 'zoomIn', '', 'mxgraph-master/examples/images/zoom_in.png', true);
        addToolbarButtonGFSSFLOWCUSTOMPRODUCT(editorGFSSFLOWCUSTOMPRODUCT, status, 'zoomOut', '', 'mxgraph-master/examples/images/zoom_out.png', true);
        addToolbarButtonGFSSFLOWCUSTOMPRODUCT(editorGFSSFLOWCUSTOMPRODUCT, status, 'actualSize', '', 'mxgraph-master/examples/images/view_1_1.png', true);
        addToolbarButtonGFSSFLOWCUSTOMPRODUCT(editorGFSSFLOWCUSTOMPRODUCT, status, 'fit', '', 'mxgraph-master/examples/images/fit_to_size.png', true);

        var outln = new mxOutline(graphGFSSFLOWCUSTOMPRODUCT, outline);

        var splash = document.getElementById('splashGFSSFLOWCUSTOMPRODUCT');
        if (splash != null) {
            try {
                mxEvent.release(splash);
                mxEffects.fadeOut(splash, 100, true);
            } catch(e) {

                splash.parentNode.removeChild(splash);
            }
        }

        graphGFSSFLOWCUSTOMPRODUCT.popupMenuHandler.factoryMethod = function(menu, cell, evt) {
            return createPopupMenuGFSSFLOWCUSTOMPRODUCT(graphGFSSFLOWCUSTOMPRODUCT, menu, cell, evt);
        };

        initFunGFSSFLOWCUSTOMPRODUCT(graphGFSSFLOWCUSTOMPRODUCT);
    }

};

/**增加右键删除菜单**/
function createPopupMenuGFSSFLOWCUSTOMPRODUCT(graph, menu, cell, evt) {
    if (cell != null) {
        menu.addItem('删除', 'images/delete.png',
        function() {
            editorGFSSFLOWCUSTOMPRODUCT.execute('deleteBefore');
        });
    }
};

/**
 * 初始化方法
 */
function initFunGFSSFLOWCUSTOMPRODUCT(graph) {
    if (parent.iidGFSSFLOWCUSTOMPRODUCT > 0) {
        loadGraphGFSSFLOWCUSTOMPRODUCT(graph);
    } else {
        addBeginEndCellGFSSFLOWCUSTOMPRODUCT(graph, "开始", "beginStyle", 1, 1);
        addBeginEndCellGFSSFLOWCUSTOMPRODUCT(graph, "结束", "endStyle", 150, 150);
    }

};
/**
 * 向顶部工具条增加工具图标
 */
function addToolbarButtonGFSSFLOWCUSTOMPRODUCT(editor, toolbar, action, label, image, isTransparent) {
    if (image != null) {
        var img = document.createElement('img');
        img.setAttribute('src', image);
        img.style.width = '74px';
        img.style.height = '30px';
        img.style.verticalAlign = 'middle';
        img.title = label;
        img.style.marginRight = '10px';
        img.style.marginTop = '2px';
    }
    mxEvent.addListener(img, 'click',
    function(evt) {
        if ('delete' == action) {
            var cells = editor.graph.getSelectionCells();
            for (i = 0; i < cells.length; i++) {
                if ('开始' == cells[i].value) {
                    Ext.Msg.alert('提示', '不删除<开始>节点！');
                    return false;
                }
            }
        }

        editor.execute(action);
    });
    toolbar.appendChild(img);
};
/**
 * 显示xml结构
 */
function showModalWindowGFSSFLOWCUSTOMPRODUCT(graph, title, content, width, height) {
    var background = document.createElement('div');
    background.style.position = 'absolute';
    background.style.left = '0px';
    background.style.top = '0px';
    background.style.right = '0px';
    background.style.bottom = '0px';
    background.style.background = 'black';
    mxUtils.setOpacity(background, 50);
    document.body.appendChild(background);

    if (mxClient.IS_IE) {
        new mxDivResizer(background);
    }

    var x = Math.max(0, document.body.scrollWidth / 2 - width / 2);
    var y = Math.max(10, (document.body.scrollHeight || document.documentElement.scrollHeight) / 2 - height * 2 / 3);
    var wnd = new mxWindow(title, content, x, y, width, height, false, true);
    wnd.setClosable(true);

    // Fades the background out after after the window has been closed
    wnd.addListener(mxEvent.DESTROY,
    function(evt) {
        graph.setEnabled(true);
        mxEffects.fadeOut(background, 50, true, 10, 30, true);
    });

    graph.setEnabled(false);
    graph.tooltipHandler.hide();
    wnd.setVisible(true);
};
/**
 * 增加开始节点,结束节点
 */
function addBeginEndCellGFSSFLOWCUSTOMPRODUCT(graph, label, styleName, w, h) {

    var parent = graph.getDefaultParent();
    var model = graph.getModel();

    var v1 = null;

    model.beginUpdate();
    try {
        v1 = graph.insertVertex(parent, null, label, w, h, 50, 25, styleName);
        v1.setConnectable(true);
    } finally {
        model.endUpdate();
    }

};
/**
 * 向左侧工具栏增加图标
 */
function addSidebarIconGFSSFLOWCUSTOMPRODUCT(graph, sidebar, label, image, styleName, phaseId) {
    var funct = function(graph, evt, cell, x, y) {
        var parent = graph.getDefaultParent();
        var model = graph.getModel();

        var v1 = null;

        model.beginUpdate();
        try {
            actNameCountMapGFSSFLOWCUSTOMPRODUCT[styleName] = ++actNameCountMapGFSSFLOWCUSTOMPRODUCT[styleName];
            v1 = graph.insertVertex(parent, null, label + actNameCountMapGFSSFLOWCUSTOMPRODUCT[styleName], x, y, 90, 32, styleName);
            v1.setConnectable(true);
            v1.phaseId = phaseId;

        } finally {
            model.endUpdate();
        }
        graph.setSelectionCell(v1);
    }

    var img = document.createElement('img');
    img.setAttribute('src', image);
    img.style.width = '80px';
    img.style.height = '32px';
    img.title = label;
    img.style.marginLeft = '10px';
    img.style.marginBottom = '15px';

    sidebar.appendChild(img);

    var dragElt = document.createElement('div');
    dragElt.style.border = 'dashed black 1px';
    dragElt.style.width = '120px';
    dragElt.style.height = '120px';

    var ds = mxUtils.makeDraggable(img, graph, funct, dragElt, 0, 0, true, true);
    ds.setGuidesEnabled(true);
};
/**
 * 初始化页面节点样式
 */
function configureStylesheetGFSSFLOWCUSTOMPRODUCT(graph) {

    var style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_RECTANGLE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#41B9F5';
    style[mxConstants.STYLE_FILLCOLOR] = '#8CCDF5';
    style[mxConstants.STYLE_STROKECOLOR] = '#1B78C8';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTSTYLE] = 0;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '48';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '48';
    style[mxConstants.STYLE_RESIZABLE] = '0'; //不可缩放
    graph.getStylesheet().putDefaultVertexStyle(style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('beginStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#a6a6a6';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('endStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_IMAGE] = 'images/mxgraphImages/cmd.png';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_VERTICAL_LABEL_POSITION] = 'bottom';
    graph.getStylesheet().putCellStyle('cmdStyle', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_SWIMLANE;
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_CENTER;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_TOP;
    style[mxConstants.STYLE_FILLCOLOR] = '#FF9103';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '#F8C48B';
    style[mxConstants.STYLE_STROKECOLOR] = '#E86A00';
    style[mxConstants.STYLE_FONTCOLOR] = '#000000';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_OPACITY] = '100';
    style[mxConstants.STYLE_STARTSIZE] = '30';
    style[mxConstants.STYLE_FONTSIZE] = '16';
    style[mxConstants.STYLE_FONTSTYLE] = 1;
    graph.getStylesheet().putCellStyle('group', style);

    style = new Object();
    style[mxConstants.STYLE_SHAPE] = mxConstants.SHAPE_IMAGE;
    style[mxConstants.STYLE_FONTCOLOR] = '#774400';
    style[mxConstants.STYLE_PERIMETER] = mxPerimeter.RectanglePerimeter;
    style[mxConstants.STYLE_PERIMETER_SPACING] = '6';
    style[mxConstants.STYLE_ALIGN] = mxConstants.ALIGN_LEFT;
    style[mxConstants.STYLE_VERTICAL_ALIGN] = mxConstants.ALIGN_MIDDLE;
    style[mxConstants.STYLE_FONTSIZE] = '10';
    style[mxConstants.STYLE_FONTSTYLE] = 2;
    style[mxConstants.STYLE_IMAGE_WIDTH] = '16';
    style[mxConstants.STYLE_IMAGE_HEIGHT] = '16';
    graph.getStylesheet().putCellStyle('port', style);

    style = graph.getStylesheet().getDefaultEdgeStyle();
    style[mxConstants.STYLE_LABEL_BACKGROUNDCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_STROKEWIDTH] = '1';
    style[mxConstants.STYLE_STROKECOLOR] = '#595758';
    style[mxConstants.STYLE_ROUNDED] = false;
    style[mxConstants.STYLE_EDGE] = mxConstants.EDGESTYLE_ELBOW;

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#13b1f5';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('scriptServiceStyle', style);

    style = new Object();
    style[mxConstants.STYLE_FILLCOLOR] = '#ffa602';
    style[mxConstants.STYLE_GRADIENTCOLOR] = '';
    style[mxConstants.STYLE_ROUNDED] = true;
    style[mxConstants.STYLE_STROKECOLOR] = '';
    style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
    style[mxConstants.STYLE_FONTSIZE] = '12';
    style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
    graph.getStylesheet().putCellStyle('usertaskStyle', style);
    
    style = new Object();
	style[mxConstants.STYLE_FILLCOLOR] = '#00d3d5';
	style[mxConstants.STYLE_GRADIENTCOLOR] = '';
	style[mxConstants.STYLE_ROUNDED] = true; 
	style[mxConstants.STYLE_STROKECOLOR] = '';
	style[mxConstants.STYLE_FONTCOLOR] = '#FFFFFF';
	style[mxConstants.STYLE_FONTSIZE] = '12';
	style[mxConstants.STYLE_FONTFAMILY] = 'Microsoft YaHei';
	graph.getStylesheet().putCellStyle('callflowStyle', style);
};

/**
 * 回显xml文件信息至图形化界面
 */
function loadGraphGFSSFLOWCUSTOMPRODUCT() {
    graphGFSSFLOWCUSTOMPRODUCT.getModel().beginUpdate();
    try {
        var doc = mxUtils.load(encodeURI("getFlowXmlScriptService.do?flag=" + flagGFSSFLOWCUSTOMPRODUCT + "&instanceID=" + parent.iidGFSSFLOWCUSTOMPRODUCT));
        var dec = new mxCodec(doc);
        dec.decode(doc.getDocumentElement(), graphGFSSFLOWCUSTOMPRODUCT.getModel());
    } finally {
        graphGFSSFLOWCUSTOMPRODUCT.getModel().endUpdate();
    }
}
/**
 * 显示模板详细配置窗口
 */
function openWindwGFSSFLOWCUSTOMPRODUCT() {
    configwindowFlowGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/mxgraph/flowCustomizedWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 显示脚本详细配置窗口
 */
function openExecScriptWindwGFSSFLOWCUSTOMPRODUCT() {
    configwindowFlowGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.window.Window', {
        title: '详细配置',
        autoScroll: false,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight(),
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMPRODUCT/flowCustomizedExecScriptWindow.jsp",
            params: {
                flag: flagGFSSFLOWCUSTOMPRODUCT
            },
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}

/**提醒任务信息编辑页面**/
function openUTWindwGFSSFLOWCUSTOMPRODUCT() {
    configwindowFlowGFSSFLOWCUSTOMPRODUCT = Ext.create('Ext.window.Window', {
        title: '人工提醒配置',
        autoScroll: true,
        modal: true,
        closeAction: 'destroy',
        buttonAlign: 'center',
        draggable: false,
        // 禁止拖动
        resizable: false,
        // 禁止缩放
        width: 580,
        height: 215,
        loader: {
            url: "page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMPRODUCT/scriptServiceUTWindow.jsp",
            autoLoad: true,
            autoDestroy: true,
            scripts: true
        }
    }).show();
}
/**
 * 详细信息保存回填
 */
function callbackWindwGFSSFLOWCUSTOMPRODUCT() {
    //更新名后续刷新才能显示
    graphGFSSFLOWCUSTOMPRODUCT.view.refresh()

}
/**
 * 获取阶段信息
 */
function getPhaseGFSSFLOWCUSTOMPRODUCT() {
    mainGFSSFLOWCUSTOMPRODUCT(document.getElementById('graphContainerGFSSFLOWCUSTOMPRODUCT'), document.getElementById('outlineContainerGFSSFLOWCUSTOMPRODUCT'), document.getElementById('toolbarContainer'), document.getElementById('sidebarContainerGFSSFLOWCUSTOMPRODUCT'), document.getElementById('statusContainerGFSSFLOWCUSTOMPRODUCT'));

}

function saveCustomTemplateGFSSFLOWCUSTOMPRODUCT() {
    var customName = parent.customNameObjOutSideGFSSFLOWCUSTOMPRODUCT.getValue();
    if (customName.trim() == '')
	{
		Ext.Msg.alert ('提示', '请填写模板名称!');
		return null;
	}

    Ext.Ajax.request({
        url: 'checkCustomTemplateNameIsExist.do',
        params: {
            customName: customName,
            flag: flagGFSSFLOWCUSTOMPRODUCT
        },
        method: 'POST',
        success: function(response, options) {
            if (!Ext.decode(response.responseText).success) {
                Ext.Msg.alert('提示', "模板名已存在,请更换模板名！");
            } else {
                var root2FlowWindow = modelGFSSFLOWCUSTOMPRODUCT.getRoot();
                var count = modelGFSSFLOWCUSTOMPRODUCT.getChildCount(root2FlowWindow);
                var serviceId = parent.iidGFSSFLOWCUSTOMPRODUCT;
                var data = [];
                for (var i = 0; i < count; i++) {
                    var cells = root2FlowWindow.getChildAt(i);
                    var counts = cells.getChildCount();
                    for (var j = 0; j < counts; j++) {
                        var cellss = cells.getChildAt(j);
                        // 如果不是连线，则是节点
                        if (!modelGFSSFLOWCUSTOMPRODUCT.isEdge(cellss) && cellss.style != 'beginStyle' && cellss.style != 'endStyle') {
                        	var actType=cellss.phaseId;
            				var actName=cellss.value;
            				var isShutdown=cellss.isShutdown;
            				if('1'==actType){
            					
            					var messinfo = cellss.ireminfo;
            					if(messinfo==''){
            						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 沒有提醒內容！");
            						return;
            					}else{
            						var s = cellss.id + "++!!++"+messinfo+"++!!++++!!++++!!++++!!++++!!++++!!++"+actType+"++!!++"+actName;
            						data.push(s);
            					}
            				}else{
            					var chosedAgents = cellss.chosedAgents;
            					if(chosedAgents) {
            						var scriptParam = cellss.scriptParam;
            						var chosedConfigParams = cellss.chosedConfigParams;
            						for(var key in chosedConfigParams){
            							for(var keyIn in chosedConfigParams[key]){
            								if(chosedConfigParams[key][keyIn]==""){
            									chosedConfigParams[key][keyIn] = scriptParam[keyIn];
            								}
            							}
            						}
            						var startUser = cellss.startUser;
            						//var chosedAgentsAndParams = cellss.chosedAgentsAndParams;
            						var finalParamters = {};
            						for(var i=0;i<chosedAgents.length;i++){
            							if(chosedConfigParams.hasOwnProperty(chosedAgents[i])) {
            								finalParamters[chosedAgents[i]]=chosedConfigParams[chosedAgents[i]];
            							}else{
            								finalParamters[chosedAgents[i]]=scriptParam;
            							}
            						}
            						var chosedAgentsAndStartUsers = cellss.chosedAgentsAndStartUsers;
            						var chosedAgentsAndDbSources = cellss.chosedAgentsAndDbSources;
            						var s = cellss.id + "++!!++" + JSON.stringify(scriptParam) + "++!!++" + chosedAgents.join('pp') + "++!!++" + JSON.stringify(finalParamters) + "++!!++" + JSON.stringify(chosedAgentsAndStartUsers)+ "++!!++" + JSON.stringify(chosedAgentsAndDbSources) + "++!!++" +startUser+"++!!++"+actType+"++!!++"+actName+"++!!++"+isShutdown;
            						console.log("最终数据："+s);
            						data.push(s);
            					} else {
            						Ext.Msg.alert ('提示', "步骤："+cellss.value + " 没有选择服务器！");
            						return;
            					}
            				}

                        }
                    }
                }
                data.push("");
                Ext.Ajax.request({
                    url: 'saveFlowCustomTemplate.do',
                    method: 'POST',
                    params: {
                    	customName: customName,
                    	serviceId: serviceId,
                        data: data,
                        flag: parent.flagGFSSFLOWCUSTOMPRODUCT
                    },
                    success: function(response, options) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                        	var customId = Ext.decode(response.responseText).customId;
                            Ext.MessageBox.show({
                                title: "提示",
                                msg: '模板保存成功！<br>如需修改，请使用流程模板管理（生产）功能！',
                                buttonText: {
                                    yes: '确定'
                                },
                                buttons: Ext.Msg.YES
                            });
                            destroyRubbish();
                            contentPanel.getLoader().load({
                                url: 'forwardScriptServiceRelease.do'
                            });
                            if (Ext.isIE) {
                                CollectGarbage();
                            }

                        }
                    },
                    failure: function(result, request) {
                        Ext.MessageBox.show({
                            title: "提示",
                            msg: "启动失败",
                            buttonText: {
                                yes: '确定'
                            },
                            buttons: Ext.Msg.YES
                        });
                    }

                });

            }
        },
        failure: function(result, request) {}
    });

}