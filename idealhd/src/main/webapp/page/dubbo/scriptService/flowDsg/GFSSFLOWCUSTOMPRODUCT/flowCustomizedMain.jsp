<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
<% if (null==request.getParameter("activeTabNum") && null==request.getAttribute("activeTabNum")) { %>
  var activeTabNumGFSSFLOWCUSTOMPRODUCT=0;
<% } else { %>
  <% if(null!=request.getParameter("activeTabNum")) { %>
    var activeTabNumGFSSFLOWCUSTOMPRODUCT=<%=request.getParameter("activeTabNum")%>;
  <% } else { %>
    var activeTabNumGFSSFLOWCUSTOMPRODUCT=<%=request.getAttribute("activeTabNum")%>;
  <% } %>
<% } %>

<% if (null==request.getParameter("iid") && null==request.getAttribute("iid")) { %>
  	var iidGFSSFLOWCUSTOMPRODUCT=0;
<% } else { %>
	<% if(null!=request.getParameter("iid")) { %>
	  var iidGFSSFLOWCUSTOMPRODUCT=<%=request.getParameter("iid")%>;
	<% } else { %>
	  var iidGFSSFLOWCUSTOMPRODUCT=<%=request.getAttribute("iid")%>;
	<% } %>
<% } %>


<% if (null==request.getParameter("serviceName")) { %>
var serviceNameGFSSFLOWCUSTOMPRODUCT='<%=request.getAttribute("serviceName")%>';
<% } else { %>
var serviceNameGFSSFLOWCUSTOMPRODUCT='<%=request.getParameter("serviceName")%>';
<% } %>

<% if (null==request.getParameter("bussId")) { %>
var bussIdGFSSFLOWCUSTOMPRODUCT=<%=request.getAttribute("bussId")%>;
<% } else { %>
var bussIdGFSSFLOWCUSTOMPRODUCT=<%=request.getParameter("bussId")%>;
<% } %>

<% if (null==request.getParameter("flag")) { %>
var flagGFSSFLOWCUSTOMPRODUCT='<%=request.getAttribute("flag")%>';
<% } else { %>
var flagGFSSFLOWCUSTOMPRODUCT='<%=request.getParameter("flag")%>';
<% } %>


var fromTypeGFSSFLOWCUSTOMPRODUCT = <%=request.getAttribute("fromType")%>;
var workItemidGFSSFLOWCUSTOMPRODUCT = <%=request.getAttribute("workItemid")%>;
var fromGFSSFLOWCUSTOMPRODUCT = <%=request.getAttribute("from")%>==null?2:<%=request.getAttribute("from")%>;

var backInfoContentGFSSFLOWCUSTOMPRODUCT = '<%=request.getAttribute("backInfo")==null?"":request.getAttribute("backInfo")%>';
var taskNameForDbCheckGFSSFLOWCUSTOMPRODUCT = '<%=request.getAttribute("taskName")==null?"":request.getAttribute("taskName")%>';
var istatusGFSSFLOWCUSTOMPRODUCT = '<%=request.getAttribute("scriptStatus") %>';
var execStartDataGFSSFLOWCUSTOMPRODUCT = '<%=request.getAttribute("execStartData")==null?"":request.getAttribute("execStartData")%>';

<% if (null==request.getParameter("bussTypeId")) { %>
var bussTypeIdGFSSFLOWCUSTOMPRODUCT=<%=request.getAttribute("bussTypeId")%>;
<% } else { %>
var bussTypeIdGFSSFLOWCUSTOMPRODUCT=<%=request.getParameter("bussTypeId")%>;
<% } %>

<% if (null==request.getParameter("actionType") && null==request.getAttribute("actionType")) { %>
	var actionTypeGFSSFLOWCUSTOMPRODUCT='';
<% } else { %>
	<% if(null!=request.getParameter("actionType")) { %>
	  var actionTypeGFSSFLOWCUSTOMPRODUCT='<%=request.getParameter("actionType")%>';
	<% } else { %>
	  var actionTypeGFSSFLOWCUSTOMPRODUCT='<%=request.getAttribute("actionType")%>';
	<% } %>
<% } %>


<% if (null==request.getParameter("showOnly") && null==request.getAttribute("showOnly")) { %>
	var showOnlyGFSSFLOWCUSTOMPRODUCT=0;
<% } else { %>
	<% if(null!=request.getParameter("showOnly")) { %>
	  var showOnlyGFSSFLOWCUSTOMPRODUCT=<%=request.getParameter("showOnly")%>;
	<% } else { %>
	  var showOnlyGFSSFLOWCUSTOMPRODUCT=<%=request.getAttribute("showOnly")%>;
	<% } %>
<% } %>

<% if (null==request.getParameter("scriptLevel") && null==request.getAttribute("scriptLevelCode")) { %>
	var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMPRODUCT='';
<% } else { %>
	<% if(null!=request.getParameter("scriptLevel")) { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMPRODUCT='<%=request.getParameter("scriptLevel")%>';
	<% } else { %>
	  var scriptFlowLevelForTaskAudiGFSSFLOWCUSTOMPRODUCT='<%=request.getAttribute("scriptLevelCode")%>';
	<% } %>
<% } %>
var isShowInWindowGFSSFLOWCUSTOMPRODUCT = <%=request.getParameter("isShowInWindow")==null?0:request.getParameter("isShowInWindow")%>;

var filter_bussIdGFSSFLOWCUSTOMPRODUCT = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeIdGFSSFLOWCUSTOMPRODUCT = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptNameGFSSFLOWCUSTOMPRODUCT = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceNameGFSSFLOWCUSTOMPRODUCT = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptTypeGFSSFLOWCUSTOMPRODUCT = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/flowstart/Notification.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dubbo/scriptService/flowDsg/GFSSFLOWCUSTOMPRODUCT/flowCustomizedMain.js"></script>
<style type="text/css">
	.x-mask{filter:alpha(opacity=0);opacity:.0;background:#ccc}
</style>
</head>
<body>
<div id="flowCustomizedMainDivGFSSFLOWCUSTOMPRODUCT" style="width: 100%;height: 100%"></div>
</body>
</html>