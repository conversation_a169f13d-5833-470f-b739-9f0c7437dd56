var scriptmonitor_storeForExec;
var personExcute_window;
var search_form;
Ext.onReady(function() {
	destroyRubbish();
	var flag = 1; // 生产
	var stateStore;
	if (projectFlag == 1) {//光大-数据库云
		stateStore = Ext.create('Ext.data.Store', {
			fields : [ 'id', 'name' ],
			data : [ {
				"id" : "-1",
				"name" : "全部"
			}, {
				"id" : "1",
				"name" : "未运行"
			},
			{
				"id" : "102",
				"name" : "运行"
			},
			{
				"id" : "30",
				"name" : "异常"
			}, {
				"id" : "101",
				"name" : "完成"
			},
			{
				"id" : "60",
				"name" : "终止"
			}]
		});
	}else{
		stateStore= Ext.create('Ext.data.Store', {
			fields : [ 'id', 'name' ],
			data : [ {
				"id" : "-1",
				"name" : "全部"
			}, {
				"id" : "101",
				"name" : "完成"
			},
			{
				"id" : "102",
				"name" : "运行"
			},
			{
				"id" : "30",
				"name" : "异常"
			}, {
				"id" : "60",
				"name" : "终止"
			} ]
		});
	}

	search_form = Ext.create('Ext.form.Panel', {
		layout : 'anchor',
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',  
			border : false,
			dock : 'top',
			items : [ {
				fieldLabel : '服务名称',
				labelWidth : 70,
				name : 'scriptName',
				labelAlign : 'right',
				width : '22%',
				xtype : 'textfield',
				value : filter_scriptName
			}, {
				fieldLabel : '执行状态',
				labelAlign : 'right',
				width : '16%',
				labelWidth : 70,
				name : 'state',
				displayField : 'name',
				valueField : 'id',
				store : stateStore,
				queryMode : 'local',
				listeners : {
					afterRender : function(combo) {
						if (filter_state == '-1') {
							combo.setValue(stateStore.getAt(0).data.id);
						} else if (filter_state == '101') {
							combo.setValue(stateStore.getAt(1).data.id);
						} else if (filter_state == '102') {
							combo.setValue(stateStore.getAt(2).data.id);
						} else if (filter_state == '30') {
							combo.setValue(stateStore.getAt(3).data.id);
						} else if (filter_state == '60') {
							combo.setValue(stateStore.getAt(4).data.id);
						}
					}
				},
				xtype : 'combobox'
			}, {
				fieldLabel : '开始时间',
				xtype : 'datefield',
				labelAlign : 'right',
				width : '22%',
				labelWidth : 70,
				name : 'startTime',
				format : 'Y-m-d H:i:s',
				value : filter_startTime
			}, {
				fieldLabel : '结束时间',
				xtype : 'datefield',
				labelAlign : 'right',
				width : '22%',
				labelWidth : 70,
				name : 'endTime',
				format : 'Y-m-d H:i:s',
				value : filter_endTime
			}, {
				xtype : 'button',
				text : '查询',
				cls : 'Common_Btn',
				handler : function() {
					pageBar.moveFirst();
				}
			}, {
				xtype : 'button',
				text : '清空',
				cls : 'Common_Btn',
				handler : function() {
					clearQueryWhere();
				}
			},{
				xtype : 'button',
				text : '导出',
				cls : 'Common_Btn',
				handler : exportExcel
			}, {
				xtype : 'button',
				text : '返回',
				hidden : forScriptFlow != 1,
				cls : 'Common_Btn',
				handler : function() {
					forwardtestmainExec();
				}
			} ]
		} ]
	});

	Ext.define('scriptmonitorData', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		}, {
			name : 'taskName',
			type : 'string'
		}, {
			name : 'state',
			type : 'int'
		}, {
			name : 'cata',
			type : 'int'
		}, {
			name : 'flowId',
			type : 'int'
		}, {
			name : 'actNo',
			type : 'int'
		}, {
			name : 'startUser',
			type : 'string'
		}, {
			name : 'startTime',
			type : 'string'
		}, {
			name : 'endTime',
			type : 'string'
		}, {
			name : 'actType',
			type : 'string'
		}, {
			name : 'serverNum',
			type : 'int'
		}, {
			name : 'groupName',
			type : 'string'
		}, {
			name : 'serviceGId',
			type : 'string'
		},{
			name : 'totalNum',
			type : 'int'
		},{
			name : 'runNum',
			type : 'int'
		},{
			name : 'operNum',
			type : 'String'
		} ]
	});

	scriptmonitor_storeForExec = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'scriptmonitorData',
		proxy : {
			type : 'ajax',
			url : 'getTaskHistoryList.do?iServiceId=' + iServiceId,
			params : {
				iServiceId : iServiceId
			},
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	var scriptmonitor_columns = [
			{
				text : '序号',
				xtype : 'rownumberer',
				width : 70
			},
			{
				text : '执行状态',
				dataIndex : 'state',
				width : 100,
				renderer : function(value, p, record) {
					var backValue = "";
					if (value == -1) {
						backValue = '<span class="Not_running State_Color">未运行</span>';
					} else if (value == 1) {
						backValue = '<span class="Not_running State_Color">初始化/span>';
					} else if (value == 10 || value == 11) {
						backValue = '<span class="Run_Green State_Color">运行</span>';
					} else if (value == 20 || value == 5) {
						backValue = "<span class='Complete_Green State_Color'>完成</span>"
					} else if (value == 30) {
						backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
					} else if (value == 40) {
						backValue = '<span class="Abnormal_Complete_purple State_Color">异常完成</span>';
					} else if (value == 50) {
						backValue = '<span class="Abnormal_Operation_orange State_Color">异常运行</span>';
					} else if (value == 60) {
						backValue = '<span class="Kill_red State_Color">已终止</span>';
					}
					return backValue;
				}
			},{
				text : '设备数量',
				dataIndex : 'operNum',
				width : 80,
				renderer : function(value, p, record) {
					var backValue = "";
					var totalNum = record.get('totalNum');
					var runNum = record.get('runNum');
					backValue =  runNum+"/"+totalNum;
					return backValue;
				}
			},{
				text : 'gid',
				dataIndex : 'serviceGId',
				hidden : true
			},
			{
				text : '实例主键',
				dataIndex : 'iid',
				hidden : true
			},
			{
				text : 'actType',
				dataIndex : 'actType',
				hidden : true
			},
			{
				text : '服务名称',
				dataIndex : 'serviceName',
				flex : 1
			},
			{
				text : '任务名称',
				dataIndex : 'taskName',
				width : 150
			},
			{
				text : '启动人',
				dataIndex : 'startUser',
				width : 150
			},
			{
				text : '开始时间',
				dataIndex : 'startTime',
				width : 150
			},
			{
				text : '结束时间',
				dataIndex : 'endTime',
				width : 150
			},{
				text : '服务组名称',
				dataIndex : 'groupName',
				width : 150,
				hidden:true,//projectFlag==1?true:false
			},
			{
				text : '操作',
				dataIndex : 'sysOperation',
				width : 160,
				renderer : function(value, p, record) {
					var coatid = record.get('iid');
					var cata = 1;// record.get('cata');
					var actType = record.get('actType');
					var actNo = record.get('actNo');
					var flowId = record.get('flowId');
					var state = record.get('state');
//					if (actType == '0') {
						return '<span class="switch_span">'
								+ '<a href="javascript:void(0)" onclick="forwardruninfoForExec('
								+ coatid
								+ ', '
								+ cata
								+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;'
								+
								'<a href="javascript:void(0)" onclick="resultExportForExec('
								+ coatid
								+ ', '
								+ cata
								+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_export"></img>导出</a>'
								+ '</span>';
//					} else if ('2' == actType) {
//						return '<span class="switch_span">'
//								+ '<a href="javascript:void(0)" onclick="forwardUTruninfo('
//								+ flowId
//								+ ', '
//								+ coatid
//								+ ', '
//								+ actNo
//								+ ', '
//								+ state
//								+ ', '
//								+ cata
//								+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>'
//								+ '</span>';
//					}

				}
			} ];

	scriptmonitor_storeForExec
			.on('beforeload',
					function(store, options) {
						var new_params = {
							flowId : flowId,
							scriptName : search_form.getForm().findField(
									"scriptName").getValue(),
							state : search_form.getForm().findField("state")
									.getValue(),
							cata : 1,// search_form.getForm().findField("cata").getValue(),
							startTime : search_form.getForm().findField(
									"startTime").getValue(),
							endTime : search_form.getForm()
									.findField("endTime").getValue(),
							forScriptFlow : forScriptFlow,
							switchFlag:projectFlag
						};

						Ext.apply(scriptmonitor_storeForExec.proxy.extraParams,
								new_params);
					});

	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : scriptmonitor_storeForExec,
		dock : 'bottom',
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
		displayInfo : true
	});
	
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
	
	var scriptmonitor_grid = Ext.create('Ext.grid.Panel', {
		region : 'center',
		store : scriptmonitor_storeForExec,
		cls:'customize_panel_back',
		selModel:selModel,
		selType: 'cellmodel',
		border : true,
		columnLines : true,
		padding : panel_margin,
		bbar : pageBar,
		columns : scriptmonitor_columns
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "scriptcoatmonitorForExec_area",
		layout : 'border',
		bodyCls:'service_platform_bodybg',
		bodyPadding : grid_margin,
		border : true,
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		items : [ search_form, scriptmonitor_grid ]
	});
	
	function exportExcel(){
		var records = scriptmonitor_grid.getSelectionModel().getSelection();
		var array = [];
		var flowIds = [];
		var itemIds = [];
		if(records.length != 0){
			Ext.Array.each(records, function(recordObj) {
				var item = {};
				var flowid = recordObj.get('flowId');
				var taskName = recordObj.get('taskName');
				var itemid = recordObj.get('iid');
				item.flowid = flowid;
				item.taskName = taskName;
				array.push(item);
				flowIds.push(flowid);
				itemIds.push(itemid);
			});
		}else{
			records = scriptmonitor_grid.getStore().getRange(0,scriptmonitor_storeForExec.getCount());
			Ext.Array.each(records, function(recordObj) {
				var item = {};
				var flowid = recordObj.get('flowId');
				var taskName = recordObj.get('taskName');
				var itemid = recordObj.get('iid');
				item.flowid = flowid;
				item.taskName = taskName;
				array.push(item);
				flowIds.push(flowid);
				itemIds.push(itemid);
			});
		}
		var url = "multiExportCoatResult.do?data="+JSON.stringify(array)+"&itemIds="+itemIds+"&flowIds="+flowIds;
		window.location.href = url;
	}

	function clearQueryWhere() {
		search_form.getForm().findField("scriptName").setValue(''),
		search_form.getForm().findField("state").setValue("-1"),
		search_form.getForm().findField("startTime").setValue(''), search_form
				.getForm().findField("endTime").setValue('');
	}

	contentPanel.on('resize', function() {
		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.setHeight() - modelHeigth);
	});
});

function utStartSuccessCallback() {
	scriptmonitor_storeForExec.reload();
}

function forwardUTruninfo(flowId, coatid, actNo, state, cata) {
	personExcute_window = Ext.create('Ext.window.Window', {
		title : '人工提醒',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : 600,
		height : 400,
		loader : {
			url : 'scriptServiceUT.do',
			params : {
				actNo : actNo,
				coatid : coatid,
				flowId : flowId,
				flag : cata,
				state : state
			},
			autoLoad : true,
			scripts : true
		}
	}).show();
}

function forwardtestmainExec() {
	contentPanel.getLoader().load({
		url : "scriptMonitorForFlowProduct.do",
		scripts : true,
		params : {
			filter_serviceName : filter_serviceName,
			filter_serviceState : filter_serviceState,
			filter_serviceStartTime : filter_serviceStartTime,
			filter_serviceEndTime : filter_serviceEndTime
		}
	});
}
function forwardruninfoForExec(coatid, flag) {
	var scriptName = search_form.getForm().findField("scriptName").getValue();
	var state = search_form.getForm().findField("state").getValue();
	var startTime = search_form.getForm().findField("startTime").getRawValue();
	var endTime = search_form.getForm().findField("endTime").getRawValue();
	contentPanel.getLoader().load({
		url : "historyForwardscriptserverForExec.do",
		scripts : true,
		params : {
			flowId : flowId,
			forScriptFlow : forScriptFlow,
			coatid : coatid,
			flag : flag,
			filter_scriptName : scriptName,
			filter_state : state,
			filter_startTime : startTime,
			filter_endTime : endTime,
			filter_serviceName : filter_serviceName,
			filter_serviceState : filter_serviceState,
			filter_serviceStartTime : filter_serviceStartTime,
			filter_serviceEndTime : filter_serviceEndTime,
			switchFlag:projectFlag,
			queryFlag:monitorFlag
		}
	});
}
function resultExportForExec(coatid, flag) {
	window.location.href = 'exportCoatResult.do?coatId=' + coatid + '&flag='
			+ flag;
}

function scriptCoatStopForExec(coatid, flag) {
	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
		if (btn == 'yes') {
			Ext.Ajax.request({
				url : 'scriptCoatStop.do',
				method : 'POST',
				params : {
					coatid : coatid,
					flag : flag
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', message);
					} else {
						Ext.Msg.alert('提示', message);
					}
					scriptmonitor_storeForExec.reload();
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		}
	})

}