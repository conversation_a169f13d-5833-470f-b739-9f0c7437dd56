/*******************************************************************************
 * 流程定制主tab页
 ******************************************************************************/
Ext.require('Ext.tab.*');
var editorSonGFSSVIEWWINFORMONITOR = null;

Ext.onReady(function() {
	//清理各种监听
	destroyRubbish();
	
	var bussId = 0;
    var bussTypeId = 0;
    var bussName = '';
    var bussTypeName = '';
    var serviceName = '';
    var funcDescText = '';
    var creatorFullName = '';
    var R_TIME = 30;
    var autoRefreshTime = null;
	var autoRefreshCheck = null;
	var refreshButton = null;
	var killButton = null;
    Ext.Ajax.request({
        url: 'scriptService/queryOneService.do',
        params: {
            iid: iidGFSSVIEWWINFORMONITOR
        },
        method: 'POST',
        async: false,
        success: function(response, options) {
            var data = Ext.decode(response.responseText);
            if (data.success) {
                bussId = parseInt(data.sysName);
                bussTypeId = parseInt(data.bussName);
                bussName = data.bussN;
                bussTypeName = data.bussT;
                funcDescText = data.funcDesc;
                serviceName = data.serviceName;
                creatorFullName = data.fullName;
            }
        },
        failure: function(result, request) {}
    });
    
	//0.1 Graph 
	var mainTabs = Ext.widget('tabpanel', {
	    tabPosition : 'top',
	    activeTab : 0,
	    width : '100%',
	    height : contentPanel.getHeight()-80,
	    plain : true,
	    defaults : {
	      autoScroll : true,
	      bodyPadding : 5
	    },
	    items : [ {
	      title : '图形化显示',
	      loader : {
	        url : 'flowCustomizedImgScriptServiceGFSSVIEWWINFORMONITOR.do',
	        params: {
	        	flag:flagGFSSVIEWWINFORMONITOR,
	        	actionType:actionTypeGFSSVIEWWINFORMONITOR
	        },
	        contentType : 'html',
	        autoLoad : false,
	        loadMask : true,
	        scripts : true
	      },
	      listeners : {
	        activate : function(tab) {
	          tab.loader.load();
	        }
	      }
	    },{
	    	hidden: true,
		      title : '列表显示',
		      loader : {
		        url : 'flowCustomizedListScriptServiceGFSSVIEWWINFORMONITOR.do',
		        contentType : 'html',
		        autoLoad : false,
		        loadMask : true,
		        scripts : true
		      },
		      listeners : {
		        activate : function(tab) {
		          tab.loader.load();
		        }
		      }
		    }]
	  });
	var viewBasicInfoButton = Ext.create("Ext.Button", {
		cls: 'Common_Btn',
		text: "基本信息",
		disabled : false,
		handler:function(){
			Ext.create('Ext.window.Window', {
	            title: '基本信息',
	            autoScroll: true,
	            modal: true,
	            closeAction: 'destroy',
	            buttonAlign: 'center',
	            draggable: true,
	            resizable: false,
	            width: 500,
	            height: 328,
	            loader: {
	            	url: 'page/dubbo/fragment/_basicInfo.jsp',
	            	params: {
	            		creatorFullName: creatorFullName,
		                bussName: bussName,
		                bussTypeName:bussTypeName,
		                funcDescText: funcDescText,
		                serviceName:serviceName
	            	},
	            	autoLoad: true
	            },
	            dockedItems: [{
	                xtype: 'toolbar',
	                border: false,
	                dock: 'bottom',
	                margin: '0 0 5 0',
	                layout: {pack: 'center'},
	                items: [{
	                    xtype: 'button',
	                    text: '关闭',
	                    cls: 'Common_Btn',
	                    handler: function() {
	                        this.up("window").close();
	                    }
	                }]
	            }]
	        }).show();
		}
	});
	
	var backButton = Ext.create("Ext.Button", {
		cls: 'Common_Btn',
		text: "返回",
		disabled : false,
		hidden: !isStackGFSSVIEWWINFORMONITOR,
		handler:function(){
			function backFunc (){
				stackFlowViewForMonitor.close();
				if(graphViewStackFormonitor.length!=0) {
					stackFlowViewForMonitor = Ext.create('widget.window', {
						title: '详细信息',
						closable: true,
						closeAction: 'destroy',
						width: contentPanel.getWidth(),
						minWidth: 350,
						height: contentPanel.getHeight(),
						draggable: false,
						resizable: false,
						modal: true,
						loader: {
							url : 'flowWinViewerForMonitor.do', 
							params: {
								iid: graphViewStackFormonitor.pop(),
								flag: flagGFSSVIEWWINFORMONITOR,
								flowId: graphViewStackFlowIdFormonitor.pop(),
								actionType:actionTypeGFSSVIEWWINFORMONITOR,
								isShowInWindow: 1,
								isStack: true
							},
							autoLoad: true,
							scripts: true
						}
					}).show();
				}
			}
			if(actionTypeGFSSVIEWWINFORMONITOR=='exec') {
				var res = checkDataIsReadyGFSSVIEWWINFORMONITOR();
				if(res['status']=='fail') {
					Ext.Msg.confirm("请确认", res['message']+"<br>是否返回？", function(id){
						if(id=='yes') {
							backFunc();
						}
					});
				} else {
					backFunc();
				}
			} else {
				backFunc();
			}
		}
	});
	autoRefreshTime = Ext.create ('Ext.form.NumberField',
			{
			    fieldLabel : '刷新时间（秒）',
			    margin : '5',
			    labelWidth : 110,
			    width : 170,
			    value : '30',
			    allowDecimals : false,
			    minValue : R_TIME,
			    listeners :
			    {
				    blur : function ()
				    {
					    refreshTime = this.getValue ();
					    refreshTime = (refreshTime == '' || refreshTime == null) ? R_TIME : refreshTime;
					    try
					    {
						    refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
						    this.setValue (refreshTime);
					    }
					    catch (e)
					    {
						    refreshTime = R_TIME;
						    this.setValue (refreshTime);
					    }
					    if (autoRefreshCheck.checked)
					    {
						    clearInterval (refreshObjForMonitorSingle);
						    refreshObjForMonitorSingle = setInterval (reload, refreshTime * 1000);
					    }
				    }
			    }
			});
			// 是否自动刷新复选框
			autoRefreshCheck = Ext.create ('Ext.form.Checkbox',
			{
			    fieldLabel : '自动刷新',
			    //margin : '0',
			    labelWidth : 60,
			    width : 90,
			    height:30,
			    checked : false,
			    listeners :
			    {
				    change : function ()
				    {
					    if (this.checked)
					    {
						    refreshTime = autoRefreshTime.getValue ();
						    refreshTime = refreshTime == '' ? R_TIME : refreshTime;
						    try
						    {
							    refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
							    autoRefreshTime.setValue (refreshTime);
						    }
						    catch (e)
						    {
							    refreshTime = R_TIME;
							    autoRefreshTime.setValue (refreshTime);
						    }
						    clearInterval (refreshObjForMonitorSingle);
						    refreshObjForMonitorSingle = setInterval (reload, refreshTime * 1000);
					    }
					    else
					    {
						    clearInterval (refreshObjForMonitorSingle);
					    }
				    }
			    }
			});
			// 首次加载定时刷新
			if (autoRefreshCheck.checked == true)
			{
				refreshObjForMonitorSingle = setInterval (reload, autoRefreshTime.getValue () * 1000);
			}
			/** 刷新按钮* */
			refreshButton = Ext.create ("Ext.Button",
			{
			    text : '刷新',
			    cls : 'Common_Btn',
			    listeners :
			    {
				    "click" : function ()
				    {
				    	refreshProjectList ();
				    }
			    }
			});
			
			/** 终止按钮* */
			killButton = Ext.create ("Ext.Button",
			{
			    text : '终止',
			    cls : 'Common_Btn',
			    hidden:flagTypeForMonitorSingle==1?true:false,
			    listeners :
			    {
				    "click" : function ()
				    {
				    	killProject();
				    }
			    }
			});
			/** 刷新* */
			function reload ()
			{
				refreshProjectList ();
			}

			/** 刷新左侧列表* */
			function refreshProjectList ()
			{
				initFunGFSSVIEWWINFORMONITOR(graphGFSSVIEWWINFORMONITOR);
			}

	var submitFromPanel = Ext.create('Ext.form.Panel', {
		width : '100%',
		border: false,
		dockedItems : [{
			xtype : 'toolbar',
			dock : 'bottom',
			items: ['->',{
		        xtype: 'tbtext',
		        text: '<div class="step-status notrun"></div> 未运行'
		      },{
			        xtype: 'tbtext',
			        text: '<div class="step-status running"></div> 运行中'
			      },{
				        xtype: 'tbtext',
				        text: '<div class="step-status finish"></div> 完成'
				      },{
					        xtype: 'tbtext',
					        text: '<div class="step-status fail-running"></div> 异常运行'
					  },{
					        xtype: 'tbtext',
					        text: '<div class="step-status fail-finish"></div> 异常完成'
					  },{
					        xtype: 'tbtext',
					        text: '<div class="step-status kill"></div> 已终止'
					  },  '','','','','','','','',
					  /*autoRefreshTime, autoRefreshCheck,*/ refreshButton,killButton
		    , viewBasicInfoButton, backButton]
		}]
	});
	function killProject ()
	{
		var dataArray = [{
			"iid": flowIdGFSSVIEWWINFORMONITOR
		}];
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认终止", "是否终止选中的记录", function(id) {
			if (id == 'yes')
			Ext.Ajax.request({
				url : 'stopBySelect.do',
				method : 'POST',
				params : {
					jsonData : JSON.stringify(dataArray)
				},
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					 var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', "终止成功！");
					} else {
						Ext.Msg.alert('提示', message);
						return;
					}
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
			refreshProjectList ();
		});
	}

	
	  // 4.1 主Panel
	    var MainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "flowCustomizedMainDivGFSSVIEWWINFORMONITOR",
			width : '100%',
			height : contentPanel.getHeight (), 
//			overflowY:'scroll',
			autoScroll: true,
			border : false,
			bodyPadding : '5 0 0 0',
			items : [ mainTabs,submitFromPanel]
		});
		// 当页面即将离开的时候清理掉自身页面生成的组建
		contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
			Ext.destroy (MainPanel);
			if (Ext.isIE)
			{
				CollectGarbage ();
			}
		});
		 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function ()
		{
			mainTabs.setWidth ('100%');
		})
		initGFSSVIEWWINFORMONITOR();

		function initGFSSVIEWWINFORMONITOR() {
			
		}
		
		contentPanel.on('resize', function() {
		});
});
