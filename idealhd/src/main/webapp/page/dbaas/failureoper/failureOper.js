var failureOperStore;
Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   //destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'IDBUSER',
            type: 'string'
        },
        {
            name: 'IDBP<PERSON>',
            type: 'string'
        },
        {
            name: 'ICOPYNUM',
            type: 'string'
        },
        {
            name: 'IAPPLYUSER',
            type: 'string'
        },
        {
            name: 'ISID',
            type: 'string'
        },
        {
            name: 'ITYPE',
            type: 'long'
        },
        {
            name: 'IENV',
            type: 'long'
        },
        {
            name: 'IDBID',
            type: 'string'
        },
        {
            name: 'IDBVERSION',
            type: 'string'
        },       
        {
            name: 'ISTATUS',
            type: 'string'
        },
        {
            name: 'IPOSITION',
            type: 'int'
        },
        {
        	name: 'IALTERLOGNAME',
        	type: 'string'
        },
        {
            name: 'ICPU',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY',
            type: 'string'
        }
        ,
        {
            name: 'IDISK',
            type: 'string'
        } ,
        {
            name: 'IMODEL',
            type: 'string'
        },{
            name: 'IBUSINESSTYPE',
            type: 'string'
        },{
            name: 'continueCount',
            type: 'long'
        },{
            name: 'cumulateCount',
            type: 'long'
        },{
            name: 'lastcheck',
            type: 'string'
        },{
            name: 'rac',
            type: 'string'
        },{
        	name: 'istartuptime',
        	type: 'string'
        },{
        	name: 'iqdbuser',
        	type: 'string'
        },{
        	name: 'iqdbpwd',
        	type: 'string'
        },{
        	name: 'sysNameAbb',
        	type: 'string'
        },{
        	name: 'instancename',
        	type: 'string'
        },
        ]
    });
    
    failureOperStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceManageList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    failureOperStore.on('beforeload', function(store, options) {
        var new_params = {
        	baseBusiness: nameField.getValue(),
			baseSid: sidField.getValue()
//			state:ss_state,
        };

        Ext.apply(failureOperStore.proxy.extraParams, new_params);
    });

    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        },{
            name: 'sysNameAbb',
            type: 'string'
        }        ]
    });

    
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70, 
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },{
        text: '系统名称',
        dataIndex: 'IBUSINESS',
        width: 100,
        flex:1,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '系统简称',
        dataIndex: 'sysNameAbb',
        width: 100,
        flex:1,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        width: 100,
        flex:1,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        header: '实例名',
        dataIndex: 'instancename',
        width: 140,
        flex:1,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: 'IP',
        dataIndex: 'IIP',
        width: 100,
        flex:1,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '端口号',
        dataIndex: 'IDBPORT',
        width: 100,
        flex:1,
        editor: {
            xtype: 'numberfield',
            maxValue: 65535,
            minValue: 1
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },   
    {
        text: '查询帐号',
        dataIndex: 'IDBUSER',
        width: 100,
        flex:1,
        hidden:true,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '查询帐号密码',
        dataIndex: 'IDBPWD',
        width: 100,
        hidden:true,
        flex:1,
        editor: new Ext.form.TextField({ 						
			inputType:'password', //设置输入类型为password
			allowBlank: false,
			allowNegative: true
		 }),
		renderer:retNotView
    },   {
        text: '操作帐号',
        dataIndex: 'iqdbuser',
        width: 100,
        hidden:true,
        flex:1,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '操作帐号密码',
        dataIndex: 'iqdbpwd',
        width: 100,
        hidden:true,
        flex:1,
        editor: new Ext.form.TextField({ 						
			inputType:'password', //设置输入类型为password
			allowBlank: false,
			allowNegative: true
		 }),
		renderer:retNotView
    },  
    {
        header: '状态',
        dataIndex: 'ISTATUS',
        flex:1,
        renderer: function(value, p, record, rowIndex) {
        	 var IFLAG = record.get('ISTATUS');
             if(IFLAG=='0'){
             	return "<span class='Complete_Green State_Color'>有效</span>";
             }else if(IFLAG=='1'){
             	return "<span class='Abnormal_yellow State_Color'>查询用户失效</span>";
             }else if(IFLAG=='2'){
             	return "<span class='Abnormal_yellow State_Color'>操作用户失效</span>";
             }else if(IFLAG=='3'){
             	return "<span class='Abnormal_yellow State_Color'>全部失效</span>";
             }
        }
    },{
        header: '操作',
        dataIndex: 'ISTATUS',
        flex:1,
        renderer: function(value, p, record, rowIndex) {
        	var type= record.get("IRSTYPE");
        	var iqdbuser= record.get("iqdbuser");
        	var iqdbpwd= record.get("iqdbpwd");
        	var resId=record.get("IID");
             if(value=='0'&&type.toLowerCase()=='oracle'){
             	return '<a href="javascript:void(0)" onclick="toAwrWindow(\''+iqdbuser+'\',\''+iqdbpwd+'\','+resId+');">AWR报告</a>';
             }
        }
    }];
    // 分页工具
//    var pageBar = Ext.create('Ext.PagingToolbar', {
//        store: failureOperStore,
//        dock: 'bottom',
//        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
//        displayInfo: true,
//        border:false,
//        emptyMsg: '找不到任何记录'
//    });
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: failureOperStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
        emptyMsg: "没有记录"
});
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
    	 //       fieldLabel: '服务名',
    	        labelWidth: 50,
    	        labelAlign: 'left',
    	        emptyText: "--请输入系统名称或简称--",
    	        name: 'nameField',
    	        width: '15%',
    	        listeners: {
    	            specialkey: function(field, e){
    	                if (e.getKey() == e.ENTER) {
    	                	pageBar.moveFirst();
    	                }
    	            }
    	        }
    	    });
    var sidField = Ext.create("Ext.form.field.Text", {
 //       fieldLabel: '服务名',
        labelWidth: 50,
        labelAlign: 'left',
        emptyText: "--请输入实例名--",
        name: 'dataBaseNameParam',
        width: '15%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    
    var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			baseCls:'customize_gray_back',
			border : false,
			items : [nameField,sidField,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            },'->',{
	                itemId: 'oper',
	                text: '一键处置',
	                cls: 'Common_Btn',
	                handler: operManage
	            }]
			}]
	});
    
    
    
    
    var dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: failureOperStore,
        selModel: selModel,
      //  plugins: [cellEditing],
        padding : panel_margin,
		border: false,
		cls:'customize_panel_back',
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {                                    
            celldblclick : function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
               
               getDataRecord(record);
//                var map=record.data;
//                var contenHtml='  <div class="table_content2">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
//                contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
//                for(var k in map) {
//                	if(k=='iqdbuser' || k=='iqdbpwd' || k=='IDBUSER' || k=='IDBPWD'){
//                	}else{
//                		contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+k+"</span></td><td><span class=\"wt05\">"+map[k]+"</span></td></tr>"
//                	}
//                }
//                contenHtml=contenHtml+"</tbody></table></div></div>";
//                showWindow(contenHtml);
            }
        }
    });
    
        function getDataRecord(record){
    		var map=record.data;
    		var contenHtml='  <div class="table_content2">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
    		contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
    		for(var k in map) {
    			var val='';
    			var value=map[k];
    			if(k=='IBUSINESS'){
    				val='业务系统'; 
    			}
    			if(k=='IRSTYPE'){
    				val='数据源类型'; 
    			}
    			if(k=='IIP'){
    				val='数据库IP'; 
    			}
    			if(k=='instancename'){
    				val='实例名'; 
    			}
    			if(k=='IDBPORT'){
    				val='端口号'; 
    			}
    			if(k=='ISID'){
    				val='服务名'; 
    			}
    			if(k=='IDBVERSION'){
    				val='数据库版本'; 
    			}
    			if(k=='rac'){
    				val='Rac'; 
    			}
    			if(k=='INAME'){
    				val='设备名称'; 
    			}
    			if(k=='IDBUSER'){
    				if(qdbuserSwitch){
    					val='数据库用户'; 
    				}else{
    					val='查询用户'; 
    				}
    			}
    			if(k=='iqdbuser'){
    				if(qdbuserSwitch){
    					val='操作用户'; 
    				}
    			}
    			if(k=='IDBID'){
    				val='IDBID'; 
    			}
    			if(k=='IRACGROUP'){
    				val='分组名称'; 
    			}
    			if(k=='istartuptime'){
    				val='设备启动时间'; 
    			}
    			if(k=='continueCount'){
    				val='连续错误'; 
    			}
    			if(k=='cumulateCount'){
    				val='累计错误'; 
    			}
    			if(k=='cumulateCount'){
    				val='累计错误'; 
    			}
    			if(k=='ISTATUS'){
    				val='状态'; 
    				if(value==0){
    					value='有效'; 
    				}
    				if(value==1){
    					value='查询用户异常'; 
    				}
    				if(value==2){
    					value='操作用户异常'; 
    				}
    				if(value==3){
    					value='全部异常';  
    				}
    			}
    			if(k=='lastcheck'){
    				val='最近检测时间'; 
    			}
    			if(k=='sysNameAbb'){
    				val='系统简称'; 
    			}
    			if(val!=''){
    				contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+val+"</span></td><td><span class=\"wt05\">"+value+"</span></td></tr>"
    			}
    		}
    		contenHtml=contenHtml+"</tbody></table></div></div>";
    		showWindow(contenHtml);
    }
    
    function showWindow(contenHtml){
        var resultDesc = Ext.create('Ext.panel.Panel', {
               region:'center',
               border:false,
               readOnly: true,
           }); 
        resultDesc.html=contenHtml;     
        var detailWindow = Ext.create('widget.window', {
           title: '详情',
           closable: true,
           closeAction: 'hide',
           resizable: false,
           modal: true,
           width : 600,
           height :500,
           layout: {
               type: 'border',
               padding: 5
           },
           items: [resultDesc]
           
       });
       detailWindow.show ();
   }
    function retNotView(value){
    	var coun ="";
    	if (value && value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value && value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }

    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}    

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "failureOper_div",
        layout: 'border',
        bodyCls:'service_platform_bodybg customize_stbtn',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function checkIP(ip) {
    	var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
    	if (reg.test(ip)) {
    		return true;
    	} else {
    		return false;
    	}
    }   
    function clearQueryWhere() {
    	nameField.setValue('');
    	sidField.setValue('');
    }


    function isNumber(val){
    	if (val == null) return true;
        var regObj = /^\d*$/g;
        if (regObj.test(val)) {
            return true;
        } else {
            return false;
        } 
    }
    
    
    function operManage() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
        	var ids = [];
        	var statusFlag=0;
        	for(var i=0;i<data.length;i++){
        		var status=data[i].data.ISTATUS;
        		var iid=data[i].data.IID;
                if (iid) {
                 	ids.push(iid);
                }
        		if(status!=0){
        			statusFlag=1;
        		}
        	}     
        	if(statusFlag==1){
                Ext.Msg.confirm("请确认", "所选数据中包含失效的数据库，是否继续?",
                        function(button, text) {
                            if (button == "yes") {
                            	operWin(ids.join(','));
                            }
                        });
            	
        	}else{
        		operWin(ids.join(','));
        	}       	
        	
        	
        }
    }
        
 

});

function toAwrWindow(iqdbuser,iqdbpwd,resId){
	if(iqdbuser==""||iqdbuser=="null"||iqdbuser==null){
		Ext.Msg.alert('提示', '操作用户为空，不能生成awr报告');
		return false;
	}
	if(iqdbpwd==""||iqdbpwd=="null"||iqdbpwd==null){
		Ext.Msg.alert('提示', '操作用户密码为空，不能生成awr报告');
		return false;
	}
	Ext.create('Ext.window.Window', {
  		title : 'awr报告',
  		autoScroll : true,
  		modal : true,
  		resizable : false,
  		closeAction : 'destroy',
  		width : contentPanel.getWidth()*0.9,
  		height : contentPanel.getHeight()*0.9,
  		loader : {
	 			url : "toAwrPage.do",
	 			params : {
	 				resId : resId
					},
					autoLoad: true,
					autoDestroy : true,
					scripts : true
  			},
  		listeners : {
  			'close':function(){
  				
  				}
  			}
  	}).show();
	 win.on("close",function(){
	 });
}



//编辑按钮执行一个.do跳转到编辑页面
function operWin(ids){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({
		url: 'operManage.do?ids='+ids+"&ipStatus="+ipStatus,
		scripts: true
	});

}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}