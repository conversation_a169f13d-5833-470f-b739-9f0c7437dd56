var operserviceType;
var operManage_centerSounthPanel;
var operLinesSelectWin;
var from =0;
var operColumnConfigWin;
var isclose_operColumnConfigWin;

Ext.onReady(function() {	
	var sqlContent='';
	var userType=0;
	var verificationUserWin;	
	var scriptGrid; 
	var reservePlanId;
	var uuid;
	var checkUserName;
	var isUserState;
	var isExam;//是否复核
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{ name: 'IID', type: 'long' },
        { name: 'INAME', type: 'string'},
        { name: 'IIP',type: 'string'},
        { name: 'IDBPORT',type: 'long' },
        { name: 'IRSTYPE',type: 'string'},
        {name: 'IFLAG',type: 'string'},
        {name: 'IBUSINESS',type: 'string'},
        {name: 'IDBUSER', type: 'string'},
        {name: 'IDBPWD',type: 'string'},        
        {name: 'ISID',type: 'string'},
        {name: 'ITYPE',type: 'long'},
        {name: 'IDBID',type: 'string'},
        {name: 'IDBVERSION',type: 'string'},       
        {name: 'ISTATUS',type: 'string'},
        {name: 'IPOSITION',type: 'int'},
        {name: 'IALTERLOGNAME',type: 'string'},
        {name: 'IMODEL',type: 'string'},
        {name: 'IBUSINESSTYPE',type: 'string'},
        {name: 'continueCount', type: 'long'},
        {name: 'cumulateCount',type: 'long'},
        {name: 'isUserState',type: 'long'},
        {name: 'istartUpTime',type: 'long'}  
        ]
    });
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{ name: 'iid',type: 'int'},
        { name: 'paramType',type: 'string' },
        {name: 'paramDefaultValue',type: 'string'},
        { name: 'paramDesc',type: 'string'},
        {name: 'paramOrder',type: 'int'}]
    });
	 Ext.define('scriptModel', {
		    extend : 'Ext.data.Model',
		    fields : [ 
			    {name : 'iid'         ,type : 'long'}, 
			    {name : 'uuid'         ,type : 'String'}, 
			    {name : 'scriptType'  ,type : 'string'}, 
			    {name : 'scriptName'  ,type : 'string'}, 
			    {name : 'platForm',type : 'string'}, 
			    {name : 'content'     ,type : 'string'},
			    {name : 'version'     ,type : 'string'},
			    {name : 'status'     ,type : 'int'},
			    {name : 'dbType' ,type : 'string'},
			    {name : 'serviceType' ,type : 'string'},
			    {name : 'isExam' ,type : 'int'},
			    {name : 'sqlModel' ,type : 'string'},
			    {name : 'serviceId' ,type : 'string'},
			    {name : 'createUserName' ,type : 'string'},
			    {name : 'updateUserName' ,type : 'string'},
			    {name : 'isAutoSub' ,type : 'string'},
			    {name : 'userType' ,type : 'string'},
			    {name : 'catalogName' ,type : 'string'},
			    {name : 'lableName' ,type : 'string'},
			    {name : 'content' ,type : 'string'},
			    {name : 'funcdesc' ,type : 'string'}
		    ]
		});
	Ext.define ('reservePlanModel',{
			    extend : 'Ext.data.Model',
			    fields : [
			            { name : 'iName', type : 'string'},
			            {name : 'iId',type : 'long'},
			            { name : 'iCreateUser',type : 'string'},
			            {name : 'iCreateTime',type : 'string'}, 
			            { name : 'iModifyUser', type : 'string'},
			            {name : 'iModifyTime', type : 'string'},
			            { name : 'iParentId',type : 'string'},
			            { name : 'iServiceId', type : 'long'},
			            {name : 'servicesName',type : 'string'}			            
			    ]
			});
	
    Ext.define('resultData', {
        extend: 'Ext.data.Model',
        fields: ['name']
    });
   
        var resourcesStore = Ext.create('Ext.data.Store', {
            autoLoad: false,
            autoDestroy: true,       
            pageSize: 30,
            model: 'dataSourceModel',
            proxy: {
                type: 'ajax',
                async:false,
                url: 'queryResourceByIds.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        
        var scriptStore = Ext.create('Ext.data.Store', {
            autoLoad : true,
            autoDestroy : true,
            pageSize : 30,
            model : 'scriptModel',
            proxy : {
                type : 'ajax',
                url : 'queryDbaasScriptOnline.do',
                reader : {
                    type : 'json',
                    root : 'dataList',
                    totalProperty : 'total'
                }
            }
        });
        var reservePlanStore = Ext.create ('Ext.data.TreeStore',
        {
            model : 'reservePlanModel',
            proxy :
            {
                type : 'ajax',
                url : 'findPlanTreeList.do?from=1&dbType='+operManage_resType
            },
            root : {
                expanded : false,
                leaf : false
            },
            autoLoad:true
        });
        Ext.define('resultData1', {
	        extend: 'Ext.data.Model',
	        fields: []
		});
	    var isid = new Ext.form.field.Display({
	        name: 'isid',
	        fieldLabel: '实例名',
	        displayField: 'isid',
	        labelWidth: 95,
	        labelAlign:'right',
	        columnWidth: 1,
	        padding: 5,
	        value:operManage_sid
	    });
	    var status = new Ext.form.field.Display({
	        name: 'status',
	        fieldLabel: '运行状态',
	        displayField: 'status',
	        labelAlign:'right',
	        emptyText: '',
	        padding: 5,
	        labelWidth: 95,
	        columnWidth: 1
	    });   
	    var endStart_time = new Ext.form.field.Display({
	        name: 'endStartTime',
	        fieldLabel: '上次启动时间',
	        labelAlign:'right',
	        displayField: 'endStartTime',
	        emptyText: '',
	        padding: 5,
	        labelWidth: 95,
	        columnWidth: 1
	    });
	    var userProcessNum = new Ext.form.field.Display({
	        name: 'userProcessNum',
	        fieldLabel: '使用进程数',
	        labelAlign:'right',
	        displayField: 'userProcessNum',
	        emptyText: '',
	        padding: 5,
	        labelWidth: 95,
	        columnWidth: 1
	    });

	    resourcesStore.on('beforeload', function(store, options) {
	        var queryparams = {
	        		id: resId
	        };
	        Ext.apply(resourcesStore.proxy.extraParams, queryparams);
	    });
	 
	    resourcesStore.on('load', function(store, options, success) {
	    	var dataList = store.getProxy().getReader().jsonData;
	        var datas=dataList['dataList'];
	        isUserState=datas[0].isUserState;
	        isid.setValue(datas[0].ISID);
	        if(isUserState==0){
	        	status.setValue('正常');
	        }else if(isUserState==1){
	        	status.setValue('查询用户异常');
	        }else if(isUserState==2){
	        	status.setValue('操作用户异常');
	        }else if(isUserState==3){
	        	status.setValue('全部异常');
	        }
	        endStart_time.setValue(datas[0].istartUpTime);    
	    });
	    var scriptForm = Ext.create('Ext.form.Panel', {
	        height: '55%',
	        baseCls:'customize_panel_back',// panel_space_bottom
//	        bodyCls : 'x-docked-noborder-top',
	        border: true,
	        region:'north',	
	        split: true,
//	        cls:'review_font',
//	        padding:'0 10 10 10',
	        items: [{
	            border: false,
	            layout: 'column',
	            items: [isid]
	        },{
	            border: false,
	            layout: 'column',
	            items: [status]
	        },{
	            border: false,
	            layout: 'column',
	            items: [endStart_time]
	        },{
	            border: false,
	            layout: 'column',
	            items: [userProcessNum]
	        }
	        ]
	    }); 
    
	  //创建标签页内容--east--end
	    
	  //创建标签页内容--center--start
	    var mainPId='code-for-operManage'+resId+time;
	    var mainP = Ext.create('Ext.panel.Panel', {
	    	region: 'center',
//	    	width:'100%',
//	    	height:'55%',
//	    	margins : '0 5 0 0',    
//	        title: "脚本内容",
	        html: '<textarea id="'+mainPId+'" value  style="width: 100%;height:100%;"></textarea>',	        
	        minHeight: 80,
	        border: true,
//	        split:true,
	        autoScroll: true,
	        items: [
	        	{
		        	xtype: 'radiofield',
		    		fieldLabel: '脚本类型',
		    		margin:'5 5 5 10',
		            boxLabel  : 'sql',
	                name      : 's_type',
	                hidden:true,
//	                id:'s_type'+iid,
	                checked : true
	        	}
	    	]
	    });   
		var defultEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true
			})
		});
		var passwordEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true,
				inputType : 'password'
			})
		});
	    	      	     	    
	    var paramColumns = [
                    {
                    	text: '主键',
                    	dataIndex: 'iid',
                    	width: 40,
                    	hidden: true
                    },
                  {
                	  text: '顺序',
                	  dataIndex: 'paramOrder',                	 
                	  width: 50,
                      renderer:function (value, metaData, record, rowIdx, colIdx, store){ 
                      	 var backValue = "";
              	        	if(record.get('paramType')== 'IN-string(加密)'){
              	        		backValue = StringToPassword(record.get('paramDefaultValue'));
              	        	}else{
              	        		backValue = record.get('paramDefaultValue');
              	        	}
              	//        	var typeC=typeChange(record.get('paramType'));
              	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
              		             		+"<br>参数值："+backValue
              		             		+"<br>排序："+record.get('paramOrder')
              		             		+"<br>描述："+record.get('paramDesc')) 
              		             		+ '"';  
              	        	
              	        	return value; 
                      }
                 },                
                 {
                     text: '类型',
                     dataIndex: 'paramType',
                     width: 60,
//                     flex:1,
        		     renderer:function (value, metaData, record, rowIdx, colIdx, store){  
        	        	var coun ='';
        	        	if(value == 'IN-string(加密)'){
        					 coun =  StringToPassword(record.get('paramDefaultValue'));
        	        	} else{
        	        		 coun = record.get('paramDefaultValue');
        	        	} 
        	//        	var typeC=typeChange(value);
        	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
        		             		+"<br>参数值："+coun
        		             		+"<br>排序："+record.get('paramOrder')
        		             		+"<br>描述："+record.get('paramDesc')) 
        		             		+ '"';  
        	            return value;
        	        }
                 },
                 {
                     text: '参数值',
                     dataIndex: 'paramDefaultValue',
                     minWidth: 70,
                     flex:1,
                     getEditor : function(record) {
     					if (record.get('paramType') != 'IN-string(加密)' ) {
     						return defultEditor;
     					} else {
     						return passwordEditor;
     					}
     				},
     				renderer : function(value, metaData, record, rowIdx, colIdx, store){  
     		        	var backValue = "";
     		        	if(record.get('paramType')== 'IN-string(加密)'){
     		        		backValue = StringToPassword(value);
     		        	}else{
     		        		backValue = value;
     		        	}
     		//        	var typeC=typeChange(record.get('paramType') );
     		        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
     			             		+"<br>参数值："+backValue
     			             		+"<br>排序："+record.get('paramOrder')
     			             		+"<br>描述："+record.get('paramDesc')) 
     			             		+ '"';  
     		        	
     		        	return backValue;
     		        }
                 },     		    
                 {
     		        text: '描述',
     		        dataIndex: 'paramDesc',
     		        width: 60,
     		        editor: {
     		            allowBlank: true
     		        },
     		        renderer:function (value, metaData, record, rowIdx, colIdx, store){
     		        	 var backValue = "";
     			        	if(record.get('paramType')== 'IN-string(加密)'){
     			        		backValue = StringToPassword(record.get('paramDefaultValue'));
     			        	}else{
     			        		backValue = record.get('paramDefaultValue');
     			        	}
     			        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
     				             		+"<br>默认值："+backValue
     				             		+"<br>排序："+record.get('paramOrder')
     				             		+"<br>描述："+record.get('paramDesc')) 
     				             		+ '"';  
     			        	
     			        	return value; 
     		        }
     		    }
        ];
	    var paramStore = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        autoDestroy: true,
	        pageSize: 30,
	        model: 'paramModel',
	        proxy: {
	            type: 'ajax',
	            url: 'getAllScriptParamsLast.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });

	    paramStore.on('beforeload', function(store, options) {
	        var new_params = {
	            scriptId: uuid,
	            mainPanelId:mainPId
	        };
	        Ext.apply(paramStore.proxy.extraParams, new_params);
	    });
	    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
	        clicksToEdit: 2
	    });
	    var paramGrid = Ext.create('Ext.grid.Panel', {
	    	title: "参数",
//	    	height: '35%',
	        region: 'center',
	        store: paramStore,
	        bodyCls:'customize_panel_back',
	        border: false,
	        columnLines: true,
	        split:true,
	        columns: paramColumns,
	        plugins: [cellEditing]
	    });
	    var execDesc = Ext.create('Ext.form.field.TextArea', {
	    	title:"脚本说明",
	        name: 'funcdesc',
	        emptyText : '脚本说明',
	        displayField: 'funcdesc',
	        columnWidth: 1,
	        region:'south',
	        height:'15%',
	        margin:'5 5 5 0',
	        border:false,
	        readOnly: true,
	        autoScroll: true
	    });
	    
		var tablePanel=Ext.create('Ext.tab.Panel', {
			region : 'center',
//			width : contentPanel.getWidth()*0.8,
//			height : contentPanel.getHeight()*0.9-40,		
//			minHeight: 80,
			layout: {
	            type: 'border'
	        },
	        items: [paramGrid,execDesc]
		}); 

//	    var radiogroup= new Ext.form.RadioGroup({
//	    	xtype: 'radiogroup',
//            fieldLabel: '执行类型',
//            labelWidth: 60,
//            vertical: true,
//            items : [
//                     {boxLabel: '查询', name: 'operType',inputValue:0,checked:true},
//                     {boxLabel: '操作', name: 'operType',inputValue:1}
//                     ],
//            listeners: {
//            	change: function( t,newValue, oldValue,eOpts ){ 
//            		userType=newValue['operType'];
//                  }
//           }      
//        });
	    
	    var operexectype = Ext.create('Ext.data.Store', {
	        fields: ['iid', 'name'],
	        data : [
	            {"iid":"0", "name":"查询"},
	            {"iid":"1", "name":"操作"}
	        ]
	    });
	    var chooseOperexectype = Ext.create('Ext.form.field.ComboBox', {
	        name: 'chooseOperexectype',
	        displayField: 'name',
	        valueField: 'iid',
	        editable: false,
	        fieldLabel: '执行类型',
	        queryMode: 'local',
	        value:'0',
	        emptyText: '执行类型',
	        labelWidth: 65,
	        width:'60%',
	        store: operexectype,
	        listeners: {//监听事件
	            select: function () {
	            userType = this.getValue();
	         }
	        }
	    });
	    
		var queryB = Ext.create ("Ext.Button",
				{
				    cls : 'execution_blue',
				   // text : '执行',
				    tooltip :'Execute/执行',
				    handler : execute
				});
		var killB = Ext.create ("Ext.Button",
				{
				    cls : 'finish_blue',
//				    text : '终止',
				    tooltip :'break/终止',
				    disabled:true,
				    handler : killexecute
				});
	    var operTypeRadioFrom=Ext.create('Ext.form.Panel', {
	        region:'north',
//	        height: 60,
	        dockedItems: [{
                xtype : 'toolbar',
                border : false,
                dock: 'top',
                items: [chooseOperexectype, '->',
                	queryB,'-', '-','-',killB, '-', '-', '-', '-'
               ]
	        }]
	    });
	    
	    var centerEastPanel=Ext.create('Ext.panel.Panel', {    	    	
	    	layout: 'border',
	        region:'east',
	        cls:'customize_panel_back',//panel_space_right
	        width: '25%',
	        collapsible : false,
	        border:false,
	        items: [tablePanel]
	    });
        
//	    var resultColumns = [{ text: '序号', xtype:'rownumberer', width: 70 }];
//	    var operManageResultGrid = Ext.create('Ext.grid.Panel',{ 
////			title: '脚本输出',
//			activeItem: 0,
//			autoScroll: true,
////			height: '41%',
//	       // region: 'south',
//			region:'center',
////	        cls:'customize_panel_back panel_space_right',
////	        collapsible : true,	
////	        collapsed: true,
////	        titleCollapse : true,
//	        columns:resultColumns,	        
//	        border:false,
//	        store:resultStore,
//	        selModel:selModelR
//	   });
	    operManage_centerSounthPanel = Ext.create('Ext.panel.Panel', {  
	    	title: '执行输出',
	    	height: '75%',
	    	region: 'south',
	        border: false,
	        layout: 'border',
            split: true,
	        cls:'customize_panel_back add_border   add_border',// panel_space_right
	        collapsible : true,	
	        collapsed: true
	    });
	    
	  //创建标签页内容--center--end
	  //创建标签页内容--westPanel--star 
	    function showTip(data, metadata, record_start, rowindex, columnindex, store) {
	        var rowdata=record_start.data;
	        if(rowdata != ''){ // 此处对空白文本做过滤。
	            if(rowdata.iParentId!=0 && rowdata.iServiceId==0) {
	                metadata.tdAttr = "data-qtip='预案“" +rowdata.iName+"”没有关联执行脚本'";
	            }else {
	                metadata.tdAttr = "data-qtip='" + rowdata.iName + "'";
	            }
	        }
	        return data;
	    }
		var gridColumns = [
       	        {
       	            text : 'ID',
       	            dataIndex : 'iId',
       	            flex : 1,
       	            hidden : true
       	        },
       	        {
       	        	
       	        	xtype: 'treecolumn', 
       	            text : '预案名称',
       	            // sortable : true,
       	            dataIndex : 'iName',
       	            flex : 1,
       	            editor :
       	            {
       		            allowBlank : false
       	            },
       	            renderer : showTip
       	        },
                {
                    text : '脚本',
                    dataIndex : 'iServiceId',
                    width:40,
                    align:'center',
                    renderer : function(value,metadata,record){
       	            if(record.data.iParentId!=0){
           	             if(record.data.iServiceId==0){
           	                 return "N";
           	             }else {
                             return "Y";
                         }
           	         }else{
           	             return "";
           	         }
       	           }
                }
       	];
	      var nameForQuery = Ext.create ('Ext.form.TextField',
	          {
	              emptyText : '--请输入名称--',
	              labelWidth : 65,
	              width :'50%',
	              xtype : 'textfield',
//	              fieldLabel:'预案名称',
//	              regex: /^[0-9a-zA-Z_]{1,}$/,
//	              regexText:'只允许输入数字、字母、下划线', //^\w+$/gi, 
	              listeners: {
	                  specialkey: function(field,e){ 
	                          if (e.getKey()==Ext.EventObject.ENTER){
	                              reservePlanStore.load();
	                          }
	                      }
	              }
	          });
       
		/** 查询按钮* */
		var queryButtonForBSM = Ext.create ("Ext.Button",
		{
		    cls : 'Common_Btn',
		    textAlign : 'center',
		    text : '查询',
		    handler : queryWhere
		});
		/** 重置按钮* */
		var resetButtonForBSM = Ext.create ("Ext.Button",
		{
		    cls : 'Common_Btn',
		    textAlign : 'center',
		    text : '重置',
		    handler : resetWhere
		});
		
		var Form1 = Ext.create('Ext.form.FormPanel', {
//			height:'15%',
			region: 'north',
			padding : '5 0 5 0',
//			baseCls:'customize_gray_back',
			border : false,
			dockedItems : [ {
				xtype : 'toolbar',
//				baseCls:'customize_gray_back',
				border : false,
				dock : 'top',
				items : [nameForQuery, queryButtonForBSM, resetButtonForBSM]
			} ]
		});
		
		 reservePlanStore.on ('beforeload', function (store, options)
             {
                 var new_params =
                 {
                     name : nameForQuery.getValue().trim ()
                 };
                 Ext.apply (reservePlanStore.proxy.extraParams, new_params);
             });
		var grid_panel = Ext.create ('Ext.tree.Panel',
		{			
		    store : reservePlanStore,
		    region : 'center',
		    padding : panel_margin,
	        useArrows: true,  
	        rootVisible: false,  
			columns : gridColumns,
			cls:'customize_panel_back',
	        columnLines : true,
	        border : true,
		    listeners: {
		    	select: function(t,record, index, eOpts) {
		    		reservePlanId=record.data.iId;
		    		if(record.data.iParentId!=0){
		                    Ext.Ajax.request (
		                        {
		                            url : 'findScriptInfoById.do',
		                            method : 'POST',
		                            params :
		                            {
		                                serviceId:record.data.iServiceId
		                            },
		                            success : function (response, opts)
		                            {
		                                var success = Ext.decode (response.responseText).success;
		                                var dataList= Ext.decode (response.responseText).dataList;
		                                uuid=dataList[0].uuid;
		                                sqlContent=dataList[0].content;
		                                editor.setOption('value',sqlContent);                                                       
		                                execDesc.setValue(dataList[0].funcdesc);
		                              
		                                operserviceType=dataList[0].serviceType
		                                userType=dataList[0].userType;   
		                                isExam=dataList[0].isExam;  
		                                paramStore.load();
		                                scriptGrid.getSelectionModel().deselectAll();
		                                resultColumns = [{ text: '序号', xtype:'rownumberer', width: 70}];
		                                resultfieldsData_R = [];
		            		    	    var sss=Ext.getCmp(operManage_centerSounthPanel.items.keys[0]); 		    	    
		            		    	    operManage_centerSounthPanel.remove( sss ) ;	
		            		    	    operManage_centerSounthPanel.setTitle( '执行输出');
//		            		    	    var value={};
//		            		    	    value['operType']=userType;
		            		    	    chooseOperexectype.setValue(userType);		
		                            }
		                        });
		    		    }
		    	    }
		        }
		});

		var scriptColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 70
		}, 
		{
		    text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
		    text : '服务主键uuid',
		    dataIndex : 'uuid',
		    width : 40,
		    hidden : true
		},
		{
		    text : '服务号',
		    dataIndex : 'serviceId',
		    width : 100,
		    hidden:true,
		    renderer : function(value, metadata) {
		    metadata.tdAttr = 'data-qtip="' + value + '"';
		    return value;
		    }
		},
		{
			text : '脚本名称',
			dataIndex : 'scriptName',
			width : 100,
			flex:1
		},{
 	    	    text : '操作',
				xtype : 'actioncolumn',
				width : 50,
				align : 'center',
				sortable : false,
				menuDisabled : true,
				items : [ {
					iconCls:'rollback',
					tooltip : '脚本还原',
					handler : rollScriptFun
				} ]
			},{
            text : '执行类型',
            dataIndex : 'userType',
            width : 100,
            hidden:true,
            renderer : function(value, metadata) {
                if(value=='0') {
                    return "查询";
                }else {
                    return "操作";
                }
             },
             flex:1
        },{
			text : '脚本类型',
		    dataIndex : 'serviceType',
		    width : 100,
		    hidden:true,
		    flex:1
		},
		{
			text : '脚本状态',
			dataIndex : 'status',
			width :80,
			hidden:true,
			renderer:function(value,p,record,rowIndex){
		    	if(value==-1) {
		    		return '<font color="#F01024">草稿</font>';
		    	} else if (value==1) {
		    		return '<font color="#0CBF47">已上线</font>';
		    	} else if (value==2) {
		    		return '<font color="#FFA602">审核中</font>';
		    	} else if (value==3) {
		    		return '<font color="#13B1F5">已共享</font>';
		    	} else if (value==9) {
		    		return '<font color="">已共享未发布</font>';
		    	} else {
		    		return '<font color="#CCCCCC">未知</font>';
		    	}
		    }
		}
	];
		
		scriptStore.on('beforeload', function (store, options) {
	    var new_params = {  
	    		serviceName:sName.getValue().trim(),
	    		onlyScript: 1,
	    		dbType:operManage_resType

	    };
	    Ext.apply(scriptStore.proxy.extraParams, new_params);
	    });
		
	   var westsouthpageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: scriptStore,
		  	dock: 'bottom',
			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			displayInfo: true,
			border:false,
			displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
			emptyMsg: "没有记录"
	    });
	   
       var sName = new Ext.form.TextField({
           name : 'serverName',
//         fieldLabel : '脚本名称',
           emptyText : '--请输入查询条件--',
           labelWidth : 65,
           width :'50%',
           labelAlign : 'right',
//           regex: /^[0-9a-zA-Z_]{1,}$/,
//           regexText:'只允许输入数字、字母、下划线', //^\w+$/gi,  
           listeners: {
               specialkey: function(field, e){
                   if (e.getKey() == e.ENTER) {
                       westsouthpageBar.moveFirst();
                   }
               }
           }
       });
       
       var searchForm = Ext.create('Ext.form.FormPanel', {
           region: 'north',
//         height:'15%',
           padding : '5 0 5 0',
//           baseCls:'customize_gray_back',
           border : false,
           dockedItems : [ {
               xtype : 'toolbar',
//             baseCls:'customize_gray_back',
               border : false,
               dock : 'top',
               items: [sName,{
                   xtype : 'button',
                   text : '查询',
                   cls : 'Common_Btn',
                   handler : function() { 
                   queryData();
                   }
               },{
                   xtype : 'button',
                   text : '重置',
                   cls : 'Common_Btn',
                   handler : function() {
                       clearQueryWhere();
                   }
               }]
           } ]
       });
	   
	   scriptGrid = Ext.create('Ext.grid.Panel', {			
			region: 'center',
//			overflowY:'hidden',
		    store : scriptStore,
		    border:true,
		    bbar : westsouthpageBar,
		    padding : panel_margin,
		    columnLines : true,
		    columns : scriptColumns,
		    listeners: {
		    	select: function(t,record, index, eOpts) {
		    		from=1;
		    		uuid=record.data.uuid;
		    		editor.setOption('value', record.data.content);		    		
		    		sqlContent=record.data.content;
		    		execDesc.setValue(record.data.funcdesc);
		    		userType=record.data.userType;		    		
		    		isExam=record.data.isExam;  
    		    	paramStore.load();
    		    	grid_panel.getSelectionModel().deselectAll();
    		    	reservePlanId=-1;
    		    	resultColumns = [{ text: '序号', xtype:'rownumberer', width: 70 }];
    		    	resultfieldsData_R = [];
		    	    var sss=Ext.getCmp(operManage_centerSounthPanel.items.keys[0]); 		    	    
		    	    operManage_centerSounthPanel.remove( sss ) ;	
		    	    operManage_centerSounthPanel.setTitle( '执行输出');	    	   
		    	    chooseOperexectype.setValue(userType);	
		    	    operserviceType=record.data.serviceType;
		    	   
		    	  
		    	}
		    }
		});

//    	    //创建标签页内容--westPanel--end
	    var westNorthPanel=Ext.create('Ext.panel.Panel', {   
	    	layout: 'border',
	        region:'north',
	        height: '50%',
	        collapsible : false,
	        border: false,
//	        cls:'panel_space_bottom',
	        cls:'customize_panel_back',
	        split: true,
//	        collapsible : true,
	        items: [Form1,grid_panel]
	    });
	    var westSouthPanel=Ext.create('Ext.panel.Panel', { 
	    	layout: 'border',
	        region:'center',
//	        height: '10%',
//	        collapsible : false,
	        cls:'customize_panel_back',
	        split: true,
	        border: true,
	        items: [searchForm,scriptGrid]
	    });

	    var no = Ext.create('Ext.form.field.TextArea', {
	        name: 'funcdesc',
	        displayField: 'funcdesc',
	        emptyText: '',
	        columnWidth: 1,
	        width: '40%',
	        height: '54%',
	        readOnly: true,
	        autoScroll: true,
	        split: true,
	        region: 'south'
	    });
	    var eastNoPanel = Ext.create('Ext.panel.Panel', {  
	    	title: '预案分析',
	    	cls:'customize_panel_back',
	        region:'center',
	        margin:'5 0 0 0',
	        collapsible : false,
	        border: true,
	        items: []//no
	        
	    });
//	 var eastParticularPanel = Ext.create('Ext.panel.Panel', {  
//	    	height:'55%',
//	    	cls:'customize_panel_back',
//	    	id:'eastParticularPanel_'+resId,
//	        region:'north',
//	        border: true,
//	        split: true        
//	    });
	    var eastPanel = Ext.create('Ext.panel.Panel', {  
//	        title: '数据信息',
	        layout: 'border',
	        region:'east',
	        width: '18%',
	        collapsible : true,
	        border: true,
	        titleCollapse : true,
	        hideCollapseTool : true,
	        items: [/*scriptForm*/eastParticularPanel,eastNoPanel]
	    });
        var westPanel=Ext.create('Ext.panel.Panel', {               
            layout: 'border',
            region:'west',
            width: '20%',
            cls:'customize_panel_back',//panel_space_right
            collapsible : true,
            border: true,
            titleCollapse : true,
	        hideCollapseTool : true,
//			split : true,
	        bodyCls:'service_platform_bodybg',
            items: [westNorthPanel,westSouthPanel]
        });
        var centercentercenterPanel=Ext.create('Ext.panel.Panel', {             
            layout: 'border',
            region:'center',
            cls:'customize_panel_back',//panel_space_right
//            bodyCls:'service_platform_bodybg',
            collapsible : false,
            border:false,
            items: [mainP,operTypeRadioFrom]
            });
        
        var centercenterPanel=Ext.create('Ext.panel.Panel', {             
            layout: 'border',
            region:'center',
            cls:'customize_panel_back',//panel_space_right
//            bodyCls:'service_platform_bodybg',
            collapsible : false,
            border:false,
            items: [centercentercenterPanel,centerEastPanel]
            });
        var centerPanel=Ext.create('Ext.panel.Panel', {             
            layout: 'border',
            region:'center',
            cls:'customize_panel_back',//panel_space_right
            collapsible : false,
            border:false,
            items: [centercenterPanel,operManage_centerSounthPanel]
            });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "operManage_div_"+resId+"_"+time,
        layout: 'border',
        border: false,
        autoScroll: true,
        height: contentPanel.getHeight()-65,
        width:contentPanel.getWidth(),
        defaults: {
            split: true
        },
        bodyCls:'service_platform_bodybg',
    	items: [centerPanel,eastPanel,westPanel],   	
    });

    var editor = CodeMirror.fromTextArea(document.getElementById(mainPId), {
       	mode: 'sql',
       	theme: "lesser-dark", // 主题
        keyMap: "sublime", // 快键键风格
        extraKeys: {
        	"Ctrl-Q": "autocomplete",
        	"Ctrl-D":"deleteLine"
        },
        lineNumbers: true, // 显示行号
        smartIndent: true, // 智能缩进
        indentUnit: 4, // 智能缩进单位为4个空格长度
        indentWithTabs: true, // 使用制表符进行智能缩进
        lineWrapping: true, // 
        // 在行槽中添加行号显示器、折叠器、语法检测器
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"], 
        foldGutter: true, // 启用行槽中的代码折叠
        autofocus: true, // 自动聚焦
        matchBrackets: true, // 匹配结束符号，比如"]、}"
        autoCloseBrackets: true, // 自动闭合符号
        styleActiveLine: true // 显示选中行的样式
   //     readOnly: true
    });

    editor.setSize(mainP.getWidth()-2, centerEastPanel.getHeight()-60);
    mainP.on('resize', function() {
        editor.setSize(mainP.getWidth()-2, centerEastPanel.getHeight()-60);
    });

    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - 65);
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainP.getWidth()-2, centerEastPanel.getHeight()-60);
    });
    
	/** 查询业务系统* */
	function queryWhere ()
	{
		reservePlanStore.load();
	}
	/** 重置业务系统类别查询条件* */
	function resetWhere ()
	{
		nameForQuery.setValue ('');
	}
	function queryData(){
		westsouthpageBar.moveFirst();
	}
	function clearQueryWhere(){
		sName.setValue('');
	}
	var userNameText = new Ext.form.TextField({
		name : 'userName',
		fieldLabel : '用户名',
		displayField : 'userName',
		labelAlign : 'right',
		emptyText : '',
		margin : '10 0 0 0',
		labelWidth : 93,
		columnWidth : .98,
		allowBlank: false
	});
	var passWordText = new Ext.form.TextField({
		name : 'passWord',
		fieldLabel : '密码',
		displayField : 'passWord',
		labelAlign : 'right',
		emptyText : '',
		margin : '10 0 0 0',
		labelWidth : 93,
		columnWidth : .98,
		inputType:'password', //设置输入类型为password
		allowBlank: false,
		allowNegative: true
	});
	
	var  verificationUser = Ext.create('Ext.ux.ideal.form.Panel', {
		width : 300,
		layout : 'anchor',
		bodyCls : 'x-docked-noborder-top',
		buttonAlign : 'center',
		border : false,
		items : [ {
			anchor : '98%',
			padding : '5 0 5 0',
			border : false,
			items : [
					{
						layout : 'column',
						border : false,
						items : [ userNameText ,passWordText]
					}
				]
		} ]
	});		
	function killexecute (){
		var execusertype=chooseOperexectype.getValue();
		if(operManage_ipStatus==0 && userType==1){
			Ext.Msg.alert('提示',"所使用客户端，没有“操作类型”脚本的执行权限！");
			return;
		}
		
		if(operManage_ipStatus==0 && execusertype==1){
			Ext.Msg.alert('提示',"所使用客户端，没有“操作类型”脚本的执行权限！");
			return;
		}
		Ext.Ajax.request({
		    url : 'killResourceConnct.do',
		    method : 'POST',
		    async:false,
		    params : {
		    	resId: resId,
		    	resFlag: mainPId
		    },
		    success: function(response, opts) {
		        var res = Ext.decode(response.responseText).success;
		        if(res){
		        	Ext.Msg.alert('提示',"任务终止成功...");
		        	queryB.enable();
		            chooseOperexectype.enable();
		            killB.disable();
		        }
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	});
	}
	function execute(){
		var statusNow;
		var execusertype=chooseOperexectype.getValue();
		if(operManage_ipStatus==0 && userType==1){
			Ext.Msg.alert('提示',"所使用客户端，没有“操作类型”脚本的执行权限！");
			return;
		}
		
		if(operManage_ipStatus==0 && execusertype==1){
			Ext.Msg.alert('提示',"所使用客户端，没有“操作类型”脚本的执行权限！");
			return;
		}
		
		userNameText.setValue("");
		passWordText.setValue("");		
		Ext.Ajax.request({
			    url : 'queryResourceByIds.do',
			    method : 'POST',
			    async:false,
			    params : {
			    	id: failureOperResId
			    },
			    success: function(response, opts) {
			        var res = Ext.decode(response.responseText).dataList;
			        statusNow=res[0].isUserState;		
			    },
			    failure: function(result, request) {
			    	secureFilterRs(result,"操作失败！");
			    }
		});
		if((statusNow==1 || statusNow==3) && userType==0 ){
			Ext.Msg.alert('提示',"查询账户异常，无法执行");
				return;
		}
		if((statusNow==2 || statusNow==3) && userType==1 ){
			Ext.Msg.alert('提示',"操作账户异常，无法执行");
				return;
		}
        editor.save();
        sqlContent = document.getElementById(mainPId).value;
        if(sqlContent==''){
            Ext.Msg.alert('提示',"请填入脚本内容！");
            return;
        }
         var shieldCount=0;
         var remindCount=0
         var keyCheckMessage;
         var keyCheckSuccess;
         Ext.Ajax.request({
             async: false,
             url : 'operCheckKeyWord.do',
             method:"POST",
             params : {
                     content : sqlContent,
                     scriptType:'sql'
              },
             success : function(response, request) {
                  keyCheckSuccess = Ext.decode (response.responseText).success;
                  keyCheckMessage = Ext.decode (response.responseText).message;
                  shieldCount = Ext.decode (response.responseText).shieldCount;
                  remindCount = Ext.decode (response.responseText).remindCount;
             },
             failure : function(result, request) {
             }
         });
         if(keyCheckSuccess){
             if(shieldCount>0){
                 Ext.Msg.alert('提示',keyCheckMessage);
                    return;
             }
             if(shieldCount==0 && remindCount>0)
             {
                 isExam=0;
             }           
         }else{
             Ext.Msg.alert('提示',keyCheckMessage);
                return;
         }
		if(isExam==0){
			//需要复核
			if (!verificationUserWin) {
				verificationUserWin = Ext.create('widget.window', {
		            title: '复核用户验证',
		            closable: true,
		            closeAction: 'hide',
		            resizable: false,
		            modal: true,
		            width: 400,
		            minWidth: 350,
		            height: 200,
		            layout: {
		                type: 'border',
		                padding: 5
		            },
		            items: [verificationUser],
		            dockedItems : [ {
						xtype : 'toolbar',
						dock : 'bottom',
						layout: {pack: 'center'},
						items : [ { 
			  			xtype: "button",
			  			cls:'Common_Btn',
			  			text: "确定", 
			  			handler: function () { 
			  				checkUserName= userNameText.getValue();
							var passWord=passWordText.getValue();
							if (!checkUserName) {
					  				Ext.Msg.alert('提示',"请填写用户名");
					  				return;
					  		}
							if (checkUserName==operManage_loginName) {
				  				Ext.Msg.alert('提示',"请非当前登录账号的其他复核账号");
				  				return;
							}
							if (!passWord) {
				  				Ext.Msg.alert('提示',"请填写密码");
				  				return;
							}
			  				Ext.Ajax.request({
			  				    url : 'userIsExist.do',
			  				    method : 'POST',
			  				    params : {
			  				    	userName : checkUserName,
			  				    	passWord: passWord
			  				    },
			  				    success: function(response, opts) {
			  				        var result = Ext.decode(response.responseText).result;
			  				        if(!result) {
			  				        	Ext.MessageBox.alert("提示", "用户校验失败");
			  				        	verificationUserWin.close();
			  				        } else{
				  				      verificationUserWin.close();
				  				    getScriptFlag();
			  				        }
			  				      
			  				    },
			  				    failure: function(result, request) {
			  				    	secureFilterRs(result,"操作失败！");
			  				    	verificationUserWin.close();
			  				    }
			  			    });
				        }
			  		}, { 
			  			xtype: "button", 
			  			cls:'Common_Btn',
			  			text: "取消", 
			  			handler: function () {
			  				this.up("window").close();
			  			}
			  		}]
		            }]
		        });
			}
			verificationUserWin.show();	
		}else{			
			//不需要复核
			getScriptFlag();
		}
	}
	function getScriptFlag(){
		killB.enable();
		queryB.disable();
		chooseOperexectype.disable();
		 Ext.Ajax.request (
	    {
	        url : 'getResultDirData.do',
	        method : 'POST',
	    	        timeout : 600000000,
	    	        params :
	    	        {
	    	            resId:failureOperResId,
	    	            sqlContent:sqlContent,  
//	    	            params: jsonData,
	    	            userType:userType,
	    	            scriptUUID:uuid,
	    	            checkUser:checkUserName,
	    	            reservePlanId:reservePlanId,
	    	            from:1,
	    	            resFlag:mainPId
	    	        },
	    	        success : function (response, opts)
	    	        {                
	    	            var success = Ext.decode (response.responseText).success;
	    	            var message = Ext.decode (response.responseText).message; 	            
	    	            var oneflag = Ext.decode (response.responseText).oneflag;
	    	            var resId = Ext.decode (response.responseText).resId;
	    	            if(oneflag==1){
	    	            	getResult(queryB,killB);
	    	            }else if(oneflag==2){
	    	                var linesData = Ext.decode (response.responseText).linesData;
    	                	var jsonData = "[";
    	                    for (var i = 0, len = linesData.length; i < len; i++) {                                 
    	                         var ss = Ext.JSON.encode(linesData[i]);
    	                         if (i == 0) jsonData = jsonData + ss;
    	                         else jsonData = jsonData + "," + ss;
    	                     }
    	                    jsonData = jsonData + "]";    	                     
    	                	operLinesSelectWin=Ext.create ('Ext.window.Window',
    	    				{
    	    				    title : '链路选择',
    	    				    modal : true,
    	    				    closeAction : 'destroy',
    	    				    constrain : true,
    	    				    autoScroll : true,
    	    				    width : contentPanel.getWidth()*0.6,
    	    				    height :contentPanel.getHeight()*0.6,
    	    				    draggable : false,// 禁止拖动
    	    				    resizable : false,// 禁止缩放
    	    				    layout : 'fit',
    	    				    loader :
    	    				    {
    	    				        url : 'operLinesSelect.do',
    	    				        params :
    	    				        {
    	    				        	linesData : jsonData,	
    	    				        	resId:resId,
    	    				        	checkUser:checkUserName,
    	    				        	 scriptUUID:uuid,
    	    				        	resFlag:mainPId
    	    				        },
    	    				        autoLoad : true,
    	    				        scripts : true
    	    				    },
    	    				    listeners: {
    	    				    	beforedestroy(t,eOpts){
    	    				    		killB.disable();
    	    				    		queryB.enable();
    	    				    		chooseOperexectype.enable();
    	    				    		 Ext.Msg.hide();
    	    				    	}
    	    				    }
    	    				    
    	    				});
    	                	operLinesSelectWin.show ();
	    	                
	    	            }else if(oneflag==3){
	    	                var lineRelationIds=Ext.decode (response.responseText).lineRelationId;
	    	            	startForRelation(queryB,killB,chooseOperexectype,resId,lineRelationIds,checkUserName,mainPId);
	    	            }
	    	        }
	    	    })
	}
	
   
	
	function getResult(queryB,killB){
        paramStore.sort('paramOrder', 'ASC');
        var m = paramStore.getRange();
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
             var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
             var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
             if ("" == paramDefaultValue) {
                 setMessage('参数值不能为空！');
                 queryB.enable();
                 chooseOperexectype.enable();
                 killB.disable();
                 return;
             }

            if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramDefaultValue) {
                 if (!checkIsInteger(paramDefaultValue)) {
                     setMessage('参数类型为int，但参数默认值不是int类型！');
                     queryB.enable();
                     chooseOperexectype.enable();
                     killB.disable();
                     return;
                 }
             }
             if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramDefaultValue) {
                 if (!checkIsDouble(paramDefaultValue)) {
                     setMessage('参数类型为float，但参数默认值不是float类型！');
                     queryB.enable();
                     chooseOperexectype.enable();
                     killB.disable();
                     return;
                 }
             } 
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }

        jsonData = jsonData + "]";
        editor.save(); 
        getResult2(queryB,killB,chooseOperexectype,sqlContent,jsonData,userType,uuid,checkUserName,reservePlanId,operManage_centerSounthPanel,false,from,mainPId)
    }	

    function typeChange(value){
    	if(value=='IN-string(加密)' || value=='IN-string' ||value=='OUT-string' ){
    		value='varchar';
    	}
    	if(value=='IN-int' || value=='OUT-int'  ){
    		value='int';
    	}
    	if(value=='IN-float' || value=='OUT-float'  ){
    		value='float';
    	}
    	return value;
    }
    
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function rollScriptFun(grid, rowIndex){
		var mess="确认后，文本域将恢复所选脚本内容！";
		Ext.MessageBox.buttonText.yes = "确认"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("脚本内容还原", mess, function(id){
			if(id=='yes'){
				var uuidcontent = grid.getStore().getAt(rowIndex).get('content');
		    	var funcdesc = grid.getStore().getAt(rowIndex).get('funcdesc');
		    	editor.setOption('value', '');		
		    	editor.setOption('value', uuidcontent);		
		    	from=1;
				uuid=grid.getStore().getAt(rowIndex).get('uuid');
				sqlContent=uuidcontent;
				execDesc.setValue(funcdesc);
				userType=grid.getStore().getAt(rowIndex).get('userType');	    		
				isExam=grid.getStore().getAt(rowIndex).get('isExam');
		    	//paramStore.load();
		    	grid_panel.getSelectionModel().deselectAll();
		    	reservePlanId=-1;
		    	resultColumns = [{ text: '序号', xtype:'rownumberer', width: 70 }];
		    	resultfieldsData_R = [];
			    var sss=Ext.getCmp(operManage_centerSounthPanel.items.keys[0]); 		    	    
			    operManage_centerSounthPanel.remove( sss ) ;	
			    operManage_centerSounthPanel.setTitle( '执行输出');	    	   
			    chooseOperexectype.setValue(userType);	
			    operserviceType=grid.getStore().getAt(rowIndex).get('serviceType');
			}});
    }
    
});

function getResult2(queryB,killB,chooseOperexectype,sqlContent,jsonData,userType,uuid,checkUserName,reservePlanId,resultPanel,ignoreMsg,from,mainPId){
    if(!ignoreMsg){
//        Ext.Msg.wait('正在处理数据，请稍候','提示');  
    }
    Ext.Ajax.request (
    {
        url : 'getResultDirData.do',
        method : 'POST',
        timeout : 600000000,
        params :
        {
            resId:failureOperResId,
            sqlContent:sqlContent,  
            params: jsonData,
            userType:userType,
            scriptUUID:uuid,
            checkUser:checkUserName,
            reservePlanId:reservePlanId,
            from:0,
            resFlag:mainPId
        },
        success : function (response, opts)
        {                
            var success = Ext.decode (response.responseText).success;
            var message = Ext.decode (response.responseText).message;
            if(!success){
            	queryB.enable();
            	chooseOperexectype.enable();
            	killB.disable();
            	setMessage(message);
            	return;
            }
        	var listCol_R= Ext.decode (response.responseText).listCol;
		    var allData= Ext.decode (response.responseText).allData;
		    var firstSqlDesc=Ext.decode (response.responseText).firstSqlDesc;
		    var showType= Ext.decode (response.responseText).showType;
		    var operId=Ext.decode (response.responseText).operId;
    		finallFunc(queryB,killB,chooseOperexectype,success,message,listCol_R,allData,firstSqlDesc,showType,operId,resultPanel);
                                        	
        }                  
    });

}

function startForRelation(queryB,killB,chooseOperexectype,resId,lineRelationIds,checkUserName,mainPId){
//	Ext.Msg.wait('正在处理数据，请稍候','提示'); 
    Ext.Ajax.request (
    {
        url : 'getResultForRelation.do',
        method : 'POST',
        timeout : 600000000,
        params :
        {
            resId:resId,
            checkUser: checkUserName,  
            lineRelationIds:lineRelationIds,
            startFlag:0,
            resFlag:mainPId
        },
        success : function (response, opts)
        {                
            var success = Ext.decode (response.responseText).success;
            var message = Ext.decode (response.responseText).message;                       
            var flag = Ext.decode (response.responseText).flag; 
            if(success){         	
            	 if(flag==1){
        		     var listCol_R= Ext.decode (response.responseText).listCol;
        		     var allData= Ext.decode (response.responseText).allData;
        		     var firstSqlDesc=Ext.decode (response.responseText).firstSqlDesc;
        		     var showType= Ext.decode (response.responseText).showType;
        			 var operId=Ext.decode (response.responseText).operId;
        	    	 finallFunc(queryB,killB,chooseOperexectype,success,message,listCol_R,allData,firstSqlDesc,showType,operId,operManage_centerSounthPanel);
            	 }else{
                	 var operKey = Ext.decode (response.responseText).operKey; 
                     var relationId = Ext.decode (response.responseText).relationId; 
                     var checkUser = Ext.decode (response.responseText).checkUser; 
                     var retLineRelationIds = Ext.decode (response.responseText).lineRelationIds; 
                     var resId = Ext.decode (response.responseText).resId; 
                     var serviceId = Ext.decode (response.responseText).serviceId;
                     var scriptName=Ext.decode (response.responseText).scriptName;
                     isclose_operColumnConfigWin=false;
                 	 operColumnConfigWin=Ext.create ('Ext.window.Window',
     				 {
     				    title : scriptName+'参数配置',
     				    modal : true,
     				    closeAction : 'destroy',
     				    constrain : true,
     				    autoScroll : true,
     				    width : contentPanel.getWidth(),
     				    height :contentPanel.getHeight(),
     				    draggable : false,// 禁止拖动
     				    resizable : false,// 禁止缩放
     				    layout : 'fit',
     				    loader :
     				    {
     				        url : 'operColumnConfig.do',
     				        params :
     				        {
     				        	operKey : operKey,
     				        	relationId:relationId,
     				        	checkUser:checkUser,
     				        	lineRelationIds:retLineRelationIds,
     				        	resId:resId,
     				        	serviceId:serviceId
     				        },
     				        autoLoad : true,
     				        scripts : true
     				    },
     				    listeners: {
     				    	beforeclose( panel, eOpts ){    
     				    		if(isclose_operColumnConfigWin){
     				    			return true;
     				    		}
     				    		var mess="关闭窗口将终止服务执行，是否关闭";
 				    			Ext.MessageBox.buttonText.yes = "是"; 
 				    			Ext.MessageBox.buttonText.no = "否"; 
 				    			Ext.Msg.confirm("保存", mess, function(id){
 				    				if(id=='yes'){
 				    					isclose_operColumnConfigWin=true;
 				    					operColumnConfigWin.close();
 				    					operColumnConfigWin=null;
 				    				}});

     				    		return false;             				    			
     				    	}
     				    }
     				    
     				});
                 	operColumnConfigWin.show ();
                 }
            }else{
            	Ext.Msg.alert('提示', message);
            	queryB.enable();
            	chooseOperexectype.enable();
            	killB.disable();
            }
                
        },
        failure: function(result, request) {
            secureFilterRs(result, "操作失败！");
        }               

    });
}

function finallFunc(queryB,killB,chooseOperexectype,success,message,listCol_R,allData,firstSqlDesc,showType,operId,resultPanel){
    queryB.enable();
    chooseOperexectype.enable();
    killB.disable();
    var resultFlag=0;
    for(var key in allData){                        
        if(resultFlag>0 && allData[key].length>0){
            resultFlag++;
        }
        if(resultFlag==0){
            resultFlag++;
        }
          
    }
  
    var operManageResultGrid;
    if(success){
        if(showType==0){
            if(listCol_R==null){
                 if(!ignoreMsg){
                     Ext.Msg.alert('提示',"执行成功，但没有数据");
                 }
                 return;
            }
            resultColumns = [{ text: '序号', xtype:'rownumberer', width: 50 ,locked:true}];
            var resultfieldsData_R=[];
            //.replace(/\./g," ")
            for(var i = 0;i<listCol_R.length;i++){
                var colname=listCol_R[i].colName.replace(/\./g," ");
                var colv=listCol_R[i].colValue.replace(/\./g," ");
                var fieldsRow_R = {};
                fieldsRow_R.name = colname;
                fieldsRow_R.type = 'string';
                resultfieldsData_R.push(fieldsRow_R);
                var columnsRow_R = {};
                columnsRow_R.text = colname;
                columnsRow_R.dataIndex = colv;
                
                if(listCol_R.length<7){                                     
                    columnsRow_R.flex = 1;
                }                                   
                columnsRow_R.width = listCol_R[i].colWidth;
                resultColumns.push(columnsRow_R);       
            }

            Ext.define('resultData', {
                extend: 'Ext.data.Model',
                fields: resultfieldsData_R
            });
            var resultStore = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy: true,
                pageSize: 30,
                model: 'resultData',
                proxy: {
                    type: 'ajax',
                    url: 'getOperResult.do',
                    reader: {
                        type: 'json',
                        root : 'dataList',
                        totalProperty : 'total'
                    }
                }
            });                        
            resultStore.on('beforeload', function(store, options) {
                var new_params = {
                        operId:operId,
                        key:firstSqlDesc
                };                              
                Ext.apply(resultStore.proxy.extraParams, new_params);
            });
                                    
            var resultpageBar=Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
                store: resultStore,
                dock: 'bottom',
                baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                displayInfo: true,
                border:false,
                displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
                emptyMsg: "没有记录"
            });
            
            if(resultFlag>1){
                 resultPanel.setTitle( '<a href="gotoOperResultDetail.do?operId='+operId+'"  target="_Blank">详情...更多</a>&nbsp;&nbsp;||&nbsp;&nbsp;'
                		 + '<a href="javascript:void(0)" onclick=importDesc('+operId+')>Exp-导出</a>');
            }else{
                 resultPanel.setTitle( '<a href="gotoOperResultDetail.do?operId='+operId+'"  target="_Blank">详情</a>&nbsp;&nbsp;||&nbsp;&nbsp;'
                		 + '<a href="javascript:void(0)" onclick=importDesc('+operId+')>Exp-导出</a>');
            }
           
            if(operManageResultGrid!=undefined){
                resultPanel.remove( operManageResultGrid) ;    
            }
            var selModelR=Ext.create('Ext.selection.CheckboxModel', {
            });
            operManageResultGrid = Ext.create('Ext.grid.Panel',{ 
               
                autoScroll: true,
                region: 'center',
                columns:resultColumns,          
                border:false,
                store:resultStore,
//                selModel:selModelR,
                bbar:resultpageBar, 
                columnLines : true,
                listeners: {                                    
                celldblclick : function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
                	
                            var map=record.data;
                            var contenHtml='  <div class="table_content1">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
                            contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
                            for(var k in map) {
                            	contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+k+"</span></td><td><span class=\"wt05\">"+map[k]+"</span></td></tr>"
                             //   ss=ss+k+':'+map[k]+ '\n';
                            }
                            contenHtml=contenHtml+"</tbody></table></div></div>";
                            showWindow(contenHtml);
                        }
                    }
           });  
       
            resultPanel.add( operManageResultGrid ) ;
            resultPanel.expand();
//            resultStore.reload();
            resultpageBar.moveFirst();
        }else{                        	
            var result= Ext.decode (response.responseText).result;
            if(operserviceType=='应用'){
        		result='执行成功';
        	}
             var resultDesc = Ext.create('Ext.form.field.TextArea', {
                name: 'funcdesc',
                emptyText : '脚本说明',
                displayField: 'funcdesc',
                columnWidth: 1,
                region:'center',
                border:false,
                readOnly: true,
                autoScroll: true
            });
            
             resultDesc.setValue("");
             resultDesc.setValue(result);
             resultPanel.add( resultDesc ) ;
             resultPanel.expand();                        
        }
    }else{
         if(!ignoreMsg)Ext.Msg.alert('提示',message);
        return;
    }               

}



function getParticularScriptResult(ueryB,killB,chooseOperexectype,eastParticularPanel){
    Ext.Ajax.request (
            {
                url : 'getParticularScriptResult.do',
                method : 'POST',
                success : function (response, opts)
                {
                    var sqlContent= Ext.decode (response.responseText).content;
                    var uuid= Ext.decode (response.responseText).uuid;
                    if(sqlContent==null||sqlContent==''||uuid==null||uuid==''){
                        return;
                    }
                    getResult2(queryB,killB,chooseOperexectype,sqlContent,"[]",1,uuid,null,-1,eastParticularPanel,true,mainPId);
                }
            });
}

function showWindow(contenHtml){
     var resultDesc = Ext.create('Ext.panel.Panel', {
            region:'center',
            border:false,
            readOnly: true,
    //        autoScroll: true
        }); 
     resultDesc.html=contenHtml;     
     var detailWindow = Ext.create('widget.window', {
        title: '详情',
        closable: true,
        closeAction: 'hide',
        resizable: false,
        modal: true,
        width : 600,
        height :500,
        layout: {
            type: 'border',
            padding: 5
        },
        items: [resultDesc]
        
    });
    detailWindow.show ();
}

function importDesc(operId){
	window.location.href = 'exportOperResultDetailExcel.do?operId='+operId;
}