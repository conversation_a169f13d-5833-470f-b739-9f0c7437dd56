var failureOperStore;
Ext.onReady(function() {
    var choice=-1;
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   //destroyRubbish();
    var panel = Ext.create('Ext.panel.Panel', {  
        border: false,
        layout: 'vbox',
        cls:'customize_panel_back',// panel_space_right
        region: 'center',
        items: []
    });
    var tablewidth=0;	
	$.each(listCol, function(key, colsArray) {
		var titleKey=key;
		if(null!=listTitl && listTitl[key]!=null){
			titleKey='<table><tr><td>'+key+":</td><td>"+strLength(key,listTitl[key],30)+'</td></tr></table>';
		}
		var resultColumns = [{ text: '序号', xtype:'rownumberer', width: 60 }];
		var resultfieldsData = [];	
		for(var i = 0;i<colsArray.length;i++){
	 		var fieldsRow_R = {};
	    	fieldsRow_R.name = colsArray[i].colName;
	    	fieldsRow_R.type = 'string';
	    	resultfieldsData.push(fieldsRow_R);
	    	var columnsRow_R = {};
	    	columnsRow_R.text = colsArray[i].colName;
	    	columnsRow_R.dataIndex = colsArray[i].colValue;
	    	tablewidth=tablewidth+colsArray[i].colWidth;
 	    	if(colsArray.length<7){	    		
 	    		columnsRow_R.flex = 1;
 	    	}
 	    	if(colsArray.length>=7 && tablewidth<width && i==colsArray.length-1){   
 	    	   columnsRow_R.flex = 1;
 	    	}    
	    	columnsRow_R.width = colsArray[i].colWidth;
	    	resultColumns.push(columnsRow_R);	 	    		    	
		}
		
		Ext.define('resultData', {
		       extend: 'Ext.data.Model',
		       fields: resultfieldsData
		});
	    var resultStore = Ext.create('Ext.data.Store', {
	    	model: 'resultData',
	        data: listData[key]
	    });
	    var sqlDesc = Ext.create('Ext.form.field.TextArea', {
	        name: 'sqlDesc',
	        displayField: 'sqlDesc',
	        columnWidth: 1,
	        margin:'5 5 5 0',
	        border:false,
	    	height:noDataCount-55,
	        readOnly: true
	    });
	    sqlDesc.setValue(listTitl[key]);
	    var forms= Ext.create('Ext.form.Panel', {
	        baseCls:'customize_panel_back',// panel_space_bottom
	        region:'center',	
//	        split: true,
	        autoScroll:true,
//			titleCollapse : false,
	        items: [{
	            border: false,
	            layout: 'fit',
	            items: [sqlDesc]
	        }
	        ]
	    }); 
//	    forms.hide();
		var resultDetailGrid = Ext.create('Ext.grid.Panel',{
	        columns:resultColumns,	        
	        border:false,
	        region:'center',
	        store:resultStore,
	        width : width-20,
	        columnLines : true,
	        height :heightHaveDate,
	        listeners: {                                    
                celldblclick : function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
                	 var map=record.data;
                     var contenHtml='  <div class="table_content1">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
                     contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
                     for(var k in map) {
                     	contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+k+"</span></td><td><span class=\"wt05\">"+map[k]+"</span></td></tr>"
                      //   ss=ss+k+':'+map[k]+ '\n';
                     }
                     contenHtml=contenHtml+"</tbody></table></div></div>";
                     showWindowDetail(contenHtml);
                   }
               }
	   });	
		var formPanel = Ext.create('Ext.panel.Panel', {
			layout: 'border',
			region:'north',
			width : width,
			height:noDataCount,
			title: titleKey,
			collapsible : true,// 可收缩
			collapsed : true,// 默认收缩
			items: [forms]
		});
	   	 var sonPanel = Ext.create('Ext.panel.Panel', {
		        layout: 'border',
		        width : width-20,
		        height :heightHaveDate,
		        items: [formPanel,resultDetailGrid]
		   });
    	panel.add(sonPanel);
	})   
	var resTitle = new Ext.form.field.Display({
	        name: 'resTitle',
	        cls:'customize_panel_back',
	        fieldLabel: '',
	        displayField: 'resTitle',
	        labelAlign:'right',
	        columnWidth: 1,
	        padding: 5,
	        value:title
	    });
   	 var mainPanel = Ext.create('Ext.panel.Panel', {
//   		 	title:title,
	        renderTo: "operResultdetail_div",
	        layout: 'border',
	      //  bodyCls:'service_platform_bodybg customize_stbtn    add_border',
	        cls:'customize_panel_back add_border ',// panel_space_right
	        width : width-20,
	        height :height,
//	        bodyPadding : grid_margin,
//	        border : true,
	        items: [panel],
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				border : false,
				items : [ resTitle,'->',
		            {
						text : '导出',
						cls:'Common_Btn',
						handler: function() {
			            		window.location.href = 'exportOperResultDetailExcel.do?operId='+operResultdetail_operId+"&title="+title;
			            	}
					}]
				}]
	   });
   	 
    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };   
    function strLength(key,value,len){
    	var nameLength = 0;
    	var cutIndex = 0;
    	var spl="";
    	for(var i=0; i<value.length; i++) {
    	    if(escape(value[i]).indexOf("%u")<0){//不是中文
    	        nameLength += 1;
    	    }else{//中文
    	        nameLength += 2;
    	    }
    	    if(nameLength > len){
    	        cutIndex = i;
    	        spl="...";
    	        break;
    	    }
    	}
    	if(nameLength <= len){
    		cutIndex = value.length;
    	}
    	var retv=value.slice(0, cutIndex)+spl
//    	if(spl!=''){
//    		return '<div onclick="showTitle(\''+key+'\');"> '+retv+'</div>';
//    	}else{
//        	return retv;
//    	}
    	return retv;
    }
    function showWindowDetail(contenHtml){
        var resultDesc = Ext.create('Ext.panel.Panel', {
               region:'center',
               border:false,
               readOnly: true,
           });
     //      resultDesc.setValue(content);
           resultDesc.html=contenHtml; 
        var detailWindow = Ext.create('widget.window', {
           title: '详情',
           closable: true,
           closeAction: 'hide',
           resizable: false,
           modal: true,
           width : 600,
           height :500,
           layout: {
               type: 'border',
               padding: 5
           },
           items: [resultDesc],
           }, { 
               xtype: "button", 
               cls:'Common_Btn',
               text: "取消", 
               handler: function () {
                   this.up("window").close();
               }
       });
       detailWindow.show ();
   }

});


function showTitle(key){
	var resultSql = Ext.create('Ext.panel.Panel', {
		region:'center',
		border:false,
		readOnly: true,
	});
	var contenHtml='';
	if(null!=listTitl && listTitl[key]!=null){
		contenHtml=listTitl[key];
	}
	resultSql.html=contenHtml; 
	var sqlDetailWindow = Ext.create('widget.window', {
		title: 'SQL详情',
		closable: true,
		closeAction: 'hide',
		resizable: false,
		modal: true,
		width : 600,
		height :500,
		layout: {
			type: 'border',
			padding: 5
		},
		items: [resultSql],
	}, { 
		xtype: "button", 
		cls:'Common_Btn',
		text: "取消", 
		handler: function () {
			this.up("window").close();
		}
	});
	sqlDetailWindow.show ();
}

