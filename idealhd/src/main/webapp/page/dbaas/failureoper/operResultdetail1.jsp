<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html id="htmlid">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>结果详情展示</title>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="js/Base64.js"></script>
<link rel="stylesheet" href="css/analysisstyle_new.css">

<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/ext/resources/css/ext-all.css" />
<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/extsetting.js"></script> 
<%
    String operId = request.getParameter("operId");
%>
<script type="text/javascript">
var operId="<%=operId%>";
var keyname = new Array();
var keydata = new Array();
$(function() {

	$.ajax({
		type : "post",
		url : "getOperResultDetail.do",
		data : {
			operId : operId
		},
		dataType : "json",
		success : function(data) {
			$("#contains_new").html('');
			$("#tab").html('');
			var tables = "";
			$.each(data, function(i, resource) {
				//每个服务的表格
				//var tablelist = resource.bodys;			
				$.each(resource, function(index, item) {
					if(index=='tabs'){
						$("#tab").append(item);
					}
					if(index=='bodys'){
						tables=tables+item;
						//$("#contains_new").append(item);
					}
				});
			});
			$("#contains_new").append(tables);
			$("#tab1").attr('style','font-weight:bold;text-decoration:underline');
			$.each(data, function(i, resource) {
				//每个服务的表格
				//var tablelist = resource.bodys;
				$.each(resource, function(index, item) {
					if(index=='datas'&&item!=''){
						var arrs=item.split(";;;");
						for(var i=0;i<arrs.length;i++){
							var arr=arrs[i].split(","); 
							var a1=arr[0].substring(1,arr[0].length-1);
							var a2=arr[1].substring(1,arr[1].length-1);
							var a3=arr[2].substring(1,arr[2].length-1);
							var a4=arr[3].substring(1,arr[3].length-1);
							var a5=arr[4].substring(1,arr[4].length-1);
							var a6=arr[5].substring(1,arr[5].length-1);
							var a7=arr[6].substring(1,arr[6].length-1);
							getChart(a1,a2,a3,a4,a5,a6,a7);
						}
					}
				});
			});

		}
	});
	
});

	function imp(i){
/* 		$("#tab"+i).attr('style','font-weight:bold;text-decoration:underline').siblings("li").removeAttr("style");
		$("#content"+i).attr('style','z-index:1').siblings("div").removeAttr("style"); */
		alert('你好，世界!'+i);
		console.log(111111,$("#contains_new").prop("outerHTML"))
//		console.log('#htmlid',$("#htmlid").prop("outerHTML"))
		
		var content=utf8Encode($("#htmlid").prop("outerHTML"));
/* //		export_raw('test.html', $("#contains_new").prop("outerHTML"))
		var folder=BrowseFolder();
		SaveInfoToFile(folder, 'test.html',$("#contains_new").prop("outerHTML")); */
		console.log(content);
		window.location.href = 'exportOperResultdetail.do?content='+content;
	}
	
    function utf8Encode(string) {
        string = string.replace(/\r\n/g,"\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            }
            else if((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            }
            else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    }
</script>
<style type="text/css">
*{
margin:0;
padding: 0;
}
#tab li{
float: left;
list-style: none;
width: 240px;
height: 40px;
line-height: 40px;
cursor: pointer;
text-align: center;
background-color: #f7f8fa;
}
#contains_new{
position: relative;
}
.cont{
background-color: #f7f8fa;
padding:30px;
position: absolute;
top: 40px;
left: 0;
}


@charset "utf-8";
/* CSS Document */
body{
	padding:0;
	margin:0;
	background-color:#f7f8fa;
	font-family:Microsoft Yahei;
	color:#585a69;
	font-size:14px;
	}
.abnormal_center{
	width:1240px; 
	margin:0 auto
	}
.abnormal_body{
	width:1240px;
	height:auto;
	float:left;
	}
.headline{
	width:100%;
	height:65px;
	line-height:65px;
	background-color:red;
	background: url(../images/dbaas/headline_icon.png) no-repeat 20px;
	}
.headline h1{
	font-size:22px;
	color:#303340;
	padding:0;
	margin:0 0 0 55px;
	}
.table_content{
	background-color:#fff;
	height:auto;
	width:1200px;
	padding:20px;
	float:left;
	}
.table_content h2{
	font-size:16px;
	padding:0;
	margin:0;
	color:#303340
	}
.table_query{
	width:250px;
	margin:20px 0 0 0;
	padding:20px;
	height:680px;
	border:1px solid #dddfeb;
	float:left;
	}
.table_query ul{
	padding:0;
	margin:0;
	list-style:none;
	}
.table_query ul li:nth-child(odd){
	
	}
.table_query ul li:nth-child(even){
	
	}
.table_query_li{
	height:36px;
	line-height:36px;
	}
.pull_down{
	width:250px;
	line-height:36px;
	height:36px;
	color:#3d404d;
	border:#dddfed 1px solid;
	border-radius:2px;
	box-sizing:border-box;
	appearance:none;
  	-moz-appearance:none;
 	-webkit-appearance:none;
  	background:url(../images/dbaas/select_icon.png) no-repeat scroll right center #f7f8fa; 
	padding-right:10px;
	padding-left:10px;
	}
.pull_down::-ms-expand { 
	display: none;
	}
.pull_down option{ 
	background:#ffffff;
    color:#303340;
	} 
.pull_down option:hover{
	background:#ffffff; 
    color:#303340; 
	} 
.pull_down option:checked{ 
    background:#ffffff; 
	}
.start_date{
	width:250px;
	line-height:36px;
	height:36px;
	color:#3d404d;
	border:#dddfed 1px solid;
	border-radius:2px;
	background-color:#f7f8fa;
	}
.text_field{
	width:100%;
	height:200px;
	background-color:#f7f8fa;
	border:1px solid #dddfeb;
	}
.try_btn{
	width:76px;
	height:34px;
	line-height:34px;
	text-align:center;
	border:1px solid #3d55f6;
	border-radius:2px;
	background-color:#5168fc;
	color:#fff;
	font-size:12px;
	margin:12px auto;
	cursor:pointer
	}
.try_btn:hover{
	background-color:#6c8bff;
  	border:1px solid #3d55f6;
  	color:#fff;
	}
.right_chart{
	width:888px;
	float:left;
	margin:20px 0 0 20px;
	}
.pie_chart{
	width:886px;
	border:1px solid #dddfeb;
	height:306px;
	}
.table_chart{
	margin:10px 0 0 0;
	width:'100%';
	height:405px;
	display:block;
	overflow:auto;
	}
.table_chart table{
	border-collapse:collapse;
	display:block;
	width:880px;
	border-left:1px solid #dddfeb;
	}
.table_chart table thead {
	display:block;
	}
.table_chart table thead tr{
	height:34px;
	background-color:#f7f8fa;
	}
.table_chart table thead tr th{
	border-bottom:1px solid #dddfeb;
	border-top:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	padding:0 0 0 5px;
	}
.table_chart table .table_tbody{
	display:block;
	}
.table_chart table .table_tbody tr{
	height:34px;
	}
.table_chart table .table_tbody tr td{
	border-right:1px solid #dddfeb;
	border-bottom:1px solid #dddfeb;
	text-align:center;
	padding:0 0 0 5px;
	}
.table_chart table .table_tbody tr:nth-child(odd){
	background-color:#fff;
	}
.table_chart table .table_tbody tr:nth-child(even){
	background-color:#f7f8fa;
	}
	
.table_chart_shell{
	margin:10px 0 0 0;
	width:'100%';
	height:705px;
	display:block;
	overflow:auto;
	}
.table_chart_shell table{
	border-collapse:collapse;
	display:block;
	width:880px;
	border-left:1px solid #dddfeb;
	}
.table_chart_shell table thead {
	display:block;
	}
.table_chart_shell table thead tr{
	height:34px;
	background-color:#f7f8fa;
	}
.table_chart_shell table thead tr th{
	border-bottom:1px solid #dddfeb;
	border-top:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	padding:0 0 0 5px;
	}
.table_chart_shell table .table_tbody{
	display:block;
	}
.table_chart_shell table .table_tbody tr{
	height:34px;
	}
.table_chart_shell table .table_tbody tr td{
	border-right:1px solid #dddfeb;
	border-bottom:1px solid #dddfeb;
	text-align:center;
	padding:0 0 0 5px;
	}
.table_chart_shell table .table_tbody tr:nth-child(odd){
	background-color:#fff;
	}
.table_chart_shell table .table_tbody tr:nth-child(even){
	background-color:#f7f8fa;
	}	
	
	
.normal{
	color:#64b548;
	}
.abnormal{
	color:#f63508;
	}
.wt01{
	display:block;
	width:80px;
	}
.wt02{
	display:block;
	width:200px;
	word-break:break-all;
	text-align:left;
	}
.wt03{
	display:block;
	width:150px;
	word-break:break-all;
	text-align:left;
	}
.wt04{
	display:block;
	width:300px;
	word-break:break-all;
	text-align:left;
	}
.exception_detail{
	width:848px;
	float:right;
	margin:0 20px 20px 20px;
	}
.triangle_border_up{
    width:0;
    height:0;
    border-width:0 10px 10px;
    border-style:solid;
    border-color:transparent transparent #6377fc;
	position:relative;
	left:812px;
	top:1px;
}
.detail_header{
	background-color:#6377fc;
	border-radius:2px 2px 0 0;
	height:32px;
	line-height:32px;
	color:#fff;
	font-size:16px;
	font-weight:bold;
	background-color:#6377fc;
	width:100%;
	}
.close_icon{
	background-image:url(../images/dbaas/close_icon.png);
	width:20px;
	height:20px;
	display:block;
	float:right;
	cursor:pointer;
	margin:6px 15px 0 0;
	}
.detail_table{
	width:848px;
	height:200px;
	display:block;
	overflow:auto;
	}
.detail_table table{
	border-collapse:collapse;
	display:block;
	width:848px;
	border-left:1px solid #6377fc;
	}
.detail_table table thead {
	display:block;
	}
.detail_table table thead tr{
	height:34px;
	background-color:#ffffff;
	}
.detail_table table thead tr th{
	border-bottom:1px solid #6377fc;
	border-top:1px solid #6377fc;
	border-right:1px solid #6377fc;
	padding:0 0 0 5px;
	color:#5168fc;
	}
.detail_table table .detail_tbody{
	display:block;
	}
.detail_table table .detail_tbody tr{
	height:34px;
	}
.detail_table table .detail_tbody tr td{
	border-right:1px solid #6377fc;
	border-bottom:1px solid #6377fc;
	text-align:center;
	padding:0 0 0 5px;
	color:#303340;
	}
.detail_table table .detail_tbody tr:nth-child(odd){
	background-color:#f1f5ff;
	}
.detail_table table .detail_tbody tr:nth-child(even){
	background-color:#f1f5ff;
	}
.d_wt01{
	display:block;
	width:86px;
	}
.d_wt02{
	display:block;
	width:145px;
	word-break:break-all;
	text-align:left;
	}
.ab_table_content{
	background-color: #fff;
	height: auto;
	width: 1200px;
	padding: 20px;
	margin: 0 auto;
}
.ab_table_chart{
	width:100%;
	height:auto;
	overflow:auto;
	}
.ab_table_chart table{
	border-collapse:collapse;
	display:block;
	border-left:1px solid #dddfeb;
	}
.ab_table_chart table thead {
	display:block;
	}
.ab_table_chart table thead tr{
	height:34px;
	background-color:#f7f8fa;
	}
.ab_table_chart table thead tr th{
	border-bottom:1px solid #dddfeb;
	border-top:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	padding:0 0 0 5px;
	}
.ab_table_chart table .ab_table_tbody{
	display:block;
	}
.ab_table_chart table .ab_table_tbody tr{
	height:34px;
	}
.ab_table_chart table .ab_table_tbody tr td{
	border-right:1px solid #dddfeb;
	border-bottom:1px solid #dddfeb;
	text-align:center;
	padding:0 0 0 5px;
	}
.ab_table_chart table .ab_table_tbody tr:nth-child(odd){
	background-color:#fff;
	}
.ab_table_chart table .ab_table_tbody tr:nth-child(even){
	background-color:#f7f8fa;
	}
.ab_normal{
	color:#64b548;
	}
.ab_abnormal{
	color:#f63508;
	}
.ab_wt01{
	display:block;
	width:80px;
	}
.ab_wt02{
	display:block;
	width:200px;
	word-break:break-all;
	text-align:left;
	}


</style>

</head>
<body >

	<ul id="tab">
<!-- 		<li id="tab1"  onclick="changeTab(1)">192.168.0.118:3306:mysql.root</li>
		<li id="tab2"  onclick="changeTab(2)">192.168.0.162:1521:entegor.dbaas</li>
		<li id="tab3" value="3">资源3</li> -->
	</ul>
	<div id="contains_new">
<!-- 		<div id="content1" class="cont" style="z-index: 1;">
			<div >1111</div>
			<div >11-11</div>
		</div>
		<div id="content2" class="cont">
			<div >2222</div>
			<div >22-22</div>
		</div>
		<div id="content3" class="cont">
			内容3
		</div> -->
		
	</div>
<button type="button" onclick="imp(1)">下载</button>
</body>
</html>