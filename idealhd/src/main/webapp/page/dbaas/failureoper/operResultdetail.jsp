<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ page isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>结果详情展示</title>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<link rel="stylesheet" href="css/analysisstyle_new.css">
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/ext/resources/css/ext-all.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Rewrite_style_small.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Rewrite_style.css" />
<script type="text/javascript" src="<%=request.getContextPath()%>/ext/ext-all.js"></script>	
<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/extsetting.js"></script> 
<script type="text/javascript">
var mapData =<%=request.getAttribute("mapData")==null?"":request.getAttribute("mapData")%>;
var title='<%=request.getAttribute("title")==null?"":request.getAttribute("title")%>';
var operResultdetail_operId = <%=request.getParameter("operId")==null?"":request.getParameter("operId")%>;
/* console.log(mapData);
var jsarr=JSON.parse( mapData ); */
var listTitl=mapData['listTitl'];
var listCol=mapData['listCol'];
var listData=mapData['listData'];
var width=document.documentElement.clientWidth;
var height=document.documentElement.clientHeight;
var count=0;
var noDataCount=0;
var heightHaveDate=0;
$.each(listData, function(key, dataArray) {
	count++;
	if(count>1){
		height=(count*600)<height?height:(count*600);
		heightHaveDate=600;
		noDataCount=300;
	}else{
		heightHaveDate=height-50;
		noDataCount=heightHaveDate/2;
	}
});
//heightHaveDate=(height-130*noDataCount-80)/(count-noDataCount);
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/failureoper/operResultdetail.js"></script>
</head>
<body>
<div id="operResultdetail_div" style="width: 100%;height: 100%"></div>
</body>
</html>