Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	var lineRelationIds;
	/** *********************Model********************* */
	 Ext.define('lineData', {
	    extend: 'Ext.data.Model',
	    fields: [
	        {name: 'lineName',     type: 'string'},
	        {name: 'lineServiceId', type: 'string'},
	        {name: 'lineRelationId', type: 'string'},	        	        
	    ]
    });     

	/** *********************组件********************* */
			
    var line_store = Ext.create('Ext.data.Store', {
        model: 'lineData'        
    });
    for(var i=0;i<operLines_linesData.length;i++){
    	line_store.add(operLines_linesData[i]);
    }
	
    var line_columns = [
		{
		    text: '服务号集合',
		    dataIndex: 'lineServiceId',
		    width: 40,
		    hidden: true
		},{
		    text: '节点集合',
		    dataIndex: 'lineRelationId',
		    width: 40,
		    hidden: true
		},{
		    text: '路线',
		    dataIndex: 'lineName',
		    flex:1,
		    
		}
   ];
	var defineButton = Ext.create ("Ext.Button",
	{
		cls : 'Common_Btn',
        textAlign:'center',
        text: '确定',
	    handler : definefun

	 });
	var Form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '0 0 0 0',
//		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items : [ '->',defineButton]
		} ]
	});
	/** *********************Panel********************* */
    var line_grid = Ext.create('Ext.grid.Panel', {
        selModel:Ext.create('Ext.selection.CheckboxModel',{mode : "SINGLE"}),
        store:line_store,
        padding : panel_margin,
        border:true,
        columnLines : true,
        columns:line_columns,
        region : 'center',
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
	    listeners: {
    	select( t, record, index, eOpts ){	
    		lineRelationIds=record.data.lineRelationId

    	}
    }
    });
	var centerPanel = Ext.create('Ext.panel.Panel',{ 
//	    title : '参数配置',
	    region: 'center',
        border: false,
        layout: 'border',
	    items : [Form,line_grid]
	});
	/** 主Panel* */
	var mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "operLines_area",
	    layout : 'border',
	    width : contentPanel.getWidth()*0.6-5,
	    height :contentPanel.getHeight()*0.6-45,
	    bodyPadding : grid_margin,
	    border : true,
//	    bodyCls:'service_platform_bodybg',
	    defaults: {
            split: true
        },
        padding: 5,
	    items : [centerPanel]
	});
		
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
	    function(obj, options, eOpts) {
	        Ext.destroy(mainPanel);
	        if (Ext.isIE) {
	            CollectGarbage();
	        }
	});		
	
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};	
	function definefun(jsonData,userType,uuid,checkUserName,reservePlanId,resultPanel,serviceId){       
	    Ext.Msg.wait('正在处理数据，请稍候','提示');  	    
        Ext.Ajax.request (
                {
                    url : 'getResultForRelation.do',
                    method : 'POST',
                    timeout : 600000000,
                    params :
                    {
                        resId:operLines_resId,
                        checkUser: operLines_checkUser,  
                        lineRelationIds:lineRelationIds,
                        startFlag:0,
                        scriptuuid:operLines_scriptuuid,
                        resFlag:operLines_resFlag
                    },
                    success : function (response, opts)
                    {                
                        var success = Ext.decode (response.responseText).success;
                        var message = Ext.decode (response.responseText).message;                       
                        var flag = Ext.decode (response.responseText).flag; 
                        if(success){
                        	operLinesSelectWin.close();
                        	operLinesSelectWin=null;
                        	
                        	 if(flag==1){
                                 Ext.Msg.hide();                 
                                 var listCol_R= Ext.decode (response.responseText).listCol;
                                 var allData= Ext.decode (response.responseText).allData;
                                 var firstSqlDesc=Ext.decode (response.responseText).firstSqlDesc;
                                 var resultFlag=0;
                                 for(var key in allData){                        
                                     if(resultFlag>0 && allData[key].length>0){
                                         resultFlag++;
                                     }
                                     if(resultFlag==0){
                                         resultFlag++;
                                     }
                                       
                                 }
                                 var showType= Ext.decode (response.responseText).showType;
                                 var operId=Ext.decode (response.responseText).operId;
                                 var operManageResultGrid;
                                 if(success){
                                     if(showType==0){
                                         if(listCol_R==null){
                                              if(!ignoreMsg){
                                                  Ext.Msg.alert('提示',"执行成功，但没有数据");
                                              }
                                              return;
                                         }
                                         resultColumns = [{ text: '序号', xtype:'rownumberer', width: 70 ,locked:true}];
                                         var resultfieldsData_R=[];
                                         //.replace(/\./g," ")
                                         for(var i = 0;i<listCol_R.length;i++){
                                             var colname=listCol_R[i].colName.replace(/\./g," ");
                                             var colv=listCol_R[i].colValue.replace(/\./g," ");
                                             var fieldsRow_R = {};
                                             fieldsRow_R.name = colname;
                                             fieldsRow_R.type = 'string';
                                             resultfieldsData_R.push(fieldsRow_R);
                                             var columnsRow_R = {};
                                             columnsRow_R.text = colname;
                                             columnsRow_R.dataIndex = colv;
                                             
                                             if(listCol_R.length<7){                                     
                                                 columnsRow_R.flex = 1;
                                             }                                   
                                             columnsRow_R.width = listCol_R[i].colWidth;
                                             resultColumns.push(columnsRow_R);       
                                         }

                                         Ext.define('resultData', {
                                             extend: 'Ext.data.Model',
                                             fields: resultfieldsData_R
                                         });
                                         var resultStore = Ext.create('Ext.data.Store', {
                                             autoLoad: false,
                                             autoDestroy: true,
                                             pageSize: 30,
                                             model: 'resultData',
                                             proxy: {
                                                 type: 'ajax',
                                                 url: 'getOperResult.do',
                                                 reader: {
                                                     type: 'json',
                                                     root : 'dataList',
                                                     totalProperty : 'total'
                                                 }
                                             }
                                         });                        
                                         resultStore.on('beforeload', function(store, options) {
                                             var new_params = {
                                                     operId:operId,
                                                     key:firstSqlDesc
                                             };                              
                                             Ext.apply(resultStore.proxy.extraParams, new_params);
                                         });
                                         resultStore.reload({
                                             params: {operId:operId} 
                                         });
                                                                 
                                         var resultpageBar=Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
                                             store: resultStore,
                                             dock: 'bottom',
                                             baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
                                             displayInfo: true,
                                             border:false,
                                             displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
                                             emptyMsg: "没有记录"
                                         });
                                         
                                         if(resultFlag>1){
                                              resultPanel.setTitle( '<a href="gotoOperResultDetail.do?operId='+operId+'"  target="_Blank">查看详情...查看更多</a>&nbsp;&nbsp;&nbsp;'
                                             		 + '<a href="javascript:void(0)" onclick=importDesc('+operId+')>导出</a>');
                                         }else{
                                              resultPanel.setTitle( '<a href="gotoOperResultDetail.do?operId='+operId+'"  target="_Blank">查看详情</a>&nbsp;&nbsp;&nbsp;'
                                             		 + '<a href="javascript:void(0)" onclick=importDesc('+operId+')>导出</a>');
                                         }
                                        
                                         if(operManageResultGrid!=undefined){
                                             resultPanel.remove( operManageResultGrid) ;    
                                         }
                                         var selModelR=Ext.create('Ext.selection.CheckboxModel', {
                                         });
                                         operManageResultGrid = Ext.create('Ext.grid.Panel',{ 
                                            
                                             autoScroll: true,
                                             region: 'center',
                                             columns:resultColumns,          
                                             border:false,
                                             store:resultStore,
                                             selModel:selModelR,
                                             bbar:resultpageBar, 
                                             columnLines : true,
                                             listeners: {                                    
                                             celldblclick : function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
                                             	
                                                         var map=record.data;
                                                         var contenHtml='  <div class="table_content1">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
                                                         contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
                                                         for(var k in map) {
                                                         	contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+k+"</span></td><td><span class=\"wt05\">"+map[k]+"</span></td></tr>"
                                                          //   ss=ss+k+':'+map[k]+ '\n';
                                                         }
                                                         contenHtml=contenHtml+"</tbody></table></div></div>";
                                                         showWindow(contenHtml);
                                                     }
                                                 }
                                        });  
                                    
                                         resultPanel.add( operManageResultGrid ) ;
                                         resultPanel.expand();
                                     }else{                        	
                                         var result= Ext.decode (response.responseText).result;
                                         if(operserviceType=='应用'){
                                     		result='执行成功';
                                     	}
                                         console.log(111,result);
                                          var resultDesc = Ext.create('Ext.form.field.TextArea', {
                                             name: 'funcdesc',
                                             emptyText : '脚本说明',
                                             displayField: 'funcdesc',
                                             columnWidth: 1,
                                             region:'center',
                                             border:false,
                                             readOnly: true,
                                             autoScroll: true
                                         });
                                         
                                          resultDesc.setValue("");
                                          resultDesc.setValue(result);
                                          resultPanel.add( resultDesc ) ;
                                          resultPanel.expand();
                                          
                                     }
                                 }else{
                                      if(!ignoreMsg)Ext.Msg.alert('提示',message);
                                     return;
                                 }
                             
                             }else{
	                        	 var operKey = Ext.decode (response.responseText).operKey; 
	                             var relationId = Ext.decode (response.responseText).relationId; 
	                             var checkUser = Ext.decode (response.responseText).checkUser; 
	                             var retLineRelationIds = Ext.decode (response.responseText).lineRelationIds; 
	                             var resId = Ext.decode (response.responseText).resId; 
	                             var serviceId = Ext.decode (response.responseText).serviceId;
	                             var scriptName=Ext.decode (response.responseText).scriptName;

	                             isclose_operColumnConfigWin=false;
                             	 operColumnConfigWin=Ext.create ('Ext.window.Window',
                 				 {
                 				    title : scriptName+'参数配置',
                 				    modal : true,
                 				    closeAction : 'destroy',
                 				    constrain : true,
                 				    autoScroll : true,
                 				    width : contentPanel.getWidth(),
                 				    height :contentPanel.getHeight(),
                 				    draggable : false,// 禁止拖动
                 				    resizable : false,// 禁止缩放
                 				    layout : 'fit',
                 				    loader :
                 				    {
                 				        url : 'operColumnConfig.do',
                 				        params :
                 				        {
                 				        	operKey : operKey,
                 				        	relationId:relationId,
                 				        	checkUser:checkUser,
                 				        	lineRelationIds:retLineRelationIds,
                 				        	resId:resId,
                 				        	serviceId:serviceId,
                 				        	scriptName:scriptName,
                 				        	upName:scriptName
                 				        },
                 				        autoLoad : true,
                 				        scripts : true
                 				    },
                 				    listeners: {
                 				    	beforeclose( panel, eOpts ){    
                 				    		if(isclose_operColumnConfigWin){
                 				    			return true;
                 				    		}
                 				    		var mess="关闭窗口将终止服务执行，是否关闭";
             				    			Ext.MessageBox.buttonText.yes = "是"; 
             				    			Ext.MessageBox.buttonText.no = "否"; 
             				    			Ext.Msg.confirm("保存", mess, function(id){
             				    				if(id=='yes'){
             				    					isclose_operColumnConfigWin=true;
             				    					operColumnConfigWin.close();
             				    					operColumnConfigWin=null;
             				    				}});

                 				    		return false;             				    			
                 				    	}
                 				    }
                 				    
                 				});
                             	operColumnConfigWin.show ();
                             }
                        }else{
                        	Ext.Msg.alert('提示', message);
                        }
                            
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "操作失败！");
                    }               

                });


	}


	
});


