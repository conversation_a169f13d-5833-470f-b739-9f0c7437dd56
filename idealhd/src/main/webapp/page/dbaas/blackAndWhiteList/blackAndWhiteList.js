Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.define('DangerCmd', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'cmd',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptCmdLevel',
            type: 'string'
        },
        {
            name: 'scriptCmdRemark',
            type: 'string'
        }]
    });

    dangerCmdStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'DangerCmd',
        proxy: {
            type: 'ajax',
            url: 'getDangerCmdList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var scriptTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "shell"
        },
        {
            "name": "bat"
        },
        {
            "name": "perl"
        },
        {
            "name": "python"
        }
        // ,
        // {"name":"sql"}
        ]
    });
    var scriptLevelStore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            'id': '0',
            'name': '提醒'
        },
        {
            'id': '1',
            'name': '屏蔽'
        }]
    });
    var dangerCmdColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: 'ID',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '命令',
        dataIndex: 'cmd',
        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        text: '脚本类型',
        dataIndex: 'scriptType',
        width: 150,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: scriptTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name'
        })
    },
    {
        text: '命令级别',
        dataIndex: 'scriptCmdLevel',
        width: 150,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: scriptLevelStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'id'
        }),
        renderer: function(value) {
            var index = scriptLevelStore.find('id', value);
            var record = scriptLevelStore.getAt(index);
            if (record != null) {
                return record.data.name;
            } else {
                return value;
            }
        }

    },
    {
        text: '命令说明',
        dataIndex: 'scriptCmdRemark',
        flex: 1,
        editor: {
            allowBlank: false
        }
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dangerCmdStore,
        dock: 'bottom',
        displayInfo: true
    });

    var dangerCmdGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        store: dangerCmdStore,
        selModel: Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true
        }),
        plugins: [cellEditing],
        bbar: pageBar,
        border: false,
        columnLines: true,
        columns: dangerCmdColumns,
        animCollapse: false,
        dockedItems: [{
            xtype: 'toolbar',
            items: [{
                text: '增加',
                cls: 'Common_Btn',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                handler: saveDangerCmd
            },
            '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                disabled: true,
                handler: deleteDangerCmd
            }]
        }]
    });

    dangerCmdGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        dangerCmdGrid.down('#delete').setDisabled(selections.length === 0);
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "blackAndWhiteList_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [dangerCmdGrid]
    });

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    function add() {
        var store = dangerCmdGrid.getStore();
        var p = {
            iid: '',
            cmd: '',
            scriptType: '',
            scriptCmdLevel: '',
            scriptCmdRemark: ''
        };
        store.insert(0, p);
        dangerCmdGrid.getView().refresh();
    }

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function saveDangerCmd() {
        var m = dangerCmdStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var n = 0;
            var cmd = m[i].get("cmd").trim();
            var scriptType = m[i].get("scriptType").trim();
            var scriptLevel = m[i].get("scriptCmdLevel").trim();

            if ("" == cmd || null == cmd) {
                setMessage('命令不能为空！');
                return;
            }
            if ("" == scriptLevel || null == scriptLevel) {
                setMessage('命令级别不能为空！');
                return;
            }
            if (fucCheckLength(cmd) > 1000) {
                setMessage('命令不能超过1000字符！');
                return;
            }

            if ("" == scriptType || null == scriptType) {
                setMessage('脚本类型不能为空！');
                return;
            }
          
        }
        Ext.Msg.alert('提示', '保存成功');
     
    }

    function deleteDangerCmd() {
        var data = dangerCmdGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否真的要删除命令？",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                            ids.push(iid);
                        }else{
                        	 dangerCmdStore.remove(record);
                             }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteDangerCmd.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dangerCmdStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    dangerCmdGrid.getView().refresh();
                    }
                  
                }
            });
        }
    }

    /** 窗口尺寸调节* */
    contentPanel.on('resize',
    function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(dangerCmdGrid);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
});