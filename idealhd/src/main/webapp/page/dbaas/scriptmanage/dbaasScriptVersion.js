Ext.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			Ext.tip.QuickTipManager.init();
			var radio=0;
			var mainPanel;
		    var newUuid = scriptuid;
		    var newServiceId = scriptdbaasId;
			var editor;
			var scriptDesc = "";
			Ext.define('versionModel', {
			    extend : 'Ext.data.Model',
			    fields : [ 
				    {name : 'iid'         ,type : 'long'}, 
				    {name : 'lastid'         ,type : 'long'}, 
				    {name : 'uuid'     ,type : 'string'},
				    {name : 'createTime' ,type : 'string'}, 
				    {name : 'version'     ,type : 'string'},
				    {name : 'onlyVersion'     ,type : 'string'},
				    {name : 'status',type : 'long'},
				    {name : 'updateUserId',type : 'string'},
				    {name : 'updateUserName',type : 'string'}
			    ]
			});
			var editScriptStore = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy: true,
                model: 'editScriptModel',
                proxy: {
                    type: 'ajax',
                    url: 'queryScriptInfo.do',
                    reader: {
                        type: 'json',
                        root: 'dataList'
                    }
                }
            });
			var versionStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'versionModel',
				proxy : {
					type : 'ajax',
					url : 'getScriptVersionListForDbaas.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			
			versionStore.on('beforeload', function (store, options) {
				    var new_params = {  
				    		serviceId:newServiceId
				    };
				    Ext.apply(versionStore.proxy.extraParams, new_params);
			    });
			
			versionStore.on('load', function (store, options) {
				versionGrid.getSelectionModel().select(0, true);
		    });
			if(scriptTypeForVersion=='sql') {
				radio=4;	
			}
			var versionColumns = [{
					text : '序号',
					xtype : 'rownumberer',
					width : 70,
					hidden : true
				}, 
				{
				    text : '服务主键',
				    dataIndex : 'iid',
				    width : 40,
				    hidden : true
				},{
                    text : '服务l主键',
                    dataIndex : 'lastid',
                    width : 40,
                    hidden : true
                }, {
				    text : '服务主键uuid',
				    dataIndex : 'uuid',
				    width : 40,
				    hidden : true
				}, {
					text : '版本',
					dataIndex : 'onlyVersion',
					flex:1,
					renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}, 
				{
					text : '创建时间',
				    dataIndex : 'createTime',
				    width:150,
				    renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}, 
				{
					text : '修改者id',
				    dataIndex : 'updateUserId',
				    width:100,
				    hidden:true,
				    renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}, 
				{
					text : '修改人',
				    dataIndex : 'updateUserName',
				    width:100,
				//    hidden:true,
				    renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}
				
			];
	         
            var paramStore = Ext.create('Ext.data.Store', {
                autoLoad: false,
                autoDestroy: true,
                pageSize: 10,
                model: 'paramModel',
                proxy: {
                    type: 'ajax',
                    url: 'getAllScriptParams.do',
                    reader: {
                        type: 'json',
                        root: 'dataList',
                        totalProperty: 'total'
                    }
                }
            });
			
		   var selModel = Ext.create('Ext.selection.CheckboxModel', {
		        checkOnly: true
		    });
			var versionGrid = Ext.create('Ext.grid.Panel', {
				width : '25%',
			    store : versionStore,
			    selModel : selModel,
			    cls:'customize_panel_back panel_space_right',
			    border:true,
			    columnLines : true,
			    columns : versionColumns,
			    region: 'west',
			    listeners: {
			        select: function( me, record, index, eOpts ) {
			        	newServiceId = record.get('iid');
			        	newUuid = record.get('uuid');
			        	editScriptStore.load();
			        }
			    },
			    dockedItems:[
								{
									xtype : 'toolbar',
									border : false,
									dock : 'top',
									items: [
										'->',
									{
										text: '脚本比对',
										cls : 'Common_Btn',
										handler: compareScript
									}]
							}]
			});

		    /** *********************Panel********************* */
		    var scriptRadio = new Ext.form.RadioGroup({
		        fieldLabel: '脚本类型',
		        labelWidth: 70,
		        name: 'ra_s_view_type',
		        padding: '0 5 10 5',
		        items: [{
		            name: 'ra_s_view_type',
		            width: 80,
		            inputValue: '0',
		            hidden:true,
		            boxLabel: 'shell',
		            listeners: {
		                click: {
		                    element: 'el',
		                    fn: function(value) {
		                            editor.setOption("mode", 'shell');
		                            editor.isEmpty("");
		                    }
		                }
		            }
		        },
		        {
		            name: 'ra_s_view_type',
		            width: 80,
		            inputValue: '1',
		            hidden:true,
		            boxLabel: 'bat',
		            listeners: {
		                click: {
		                    element: 'el',
		                    // bind to the
		                    fn: function(value) {
		                            editor.setOption("mode", 'bat');
		                            editor.isEmpty("");
		                    }
		                }
		            }
		        },
		        {
		            name: 'ra_s_view_type',
		            width: 80,
		            inputValue: '2',
		            hidden:true,
		            boxLabel: 'perl',
		            listeners: {
		                click: {
		                    element: 'el',
		                    // bind to the
		                    fn: function(value) {
		                        editor.setOption("mode", 'text/x-perl');
		                    }
		                }
		            }
		        },
		        {
		            name: 'ra_s_view_type',
		            width: 80,
		            inputValue: '3',
		            hidden:true,
		            boxLabel: 'python',
		            listeners: {
		                click: {
		                    element: 'el',
		                    fn: function(value) {
		                        editor.setOption("mode", 'python');
		                    }
		                }
		            }
		        },
		        {
		            name: 'ra_s_view_type',
		            width: 80,
		            inputValue: '4',
		            boxLabel: 'sql',
		            checked: true,
		            listeners: {
		                click: {
		                    element: 'el',
		                    // bind to the
		                    fn: function(value) {
		                        editor.setOption("mode", 'text/x-plsql');
		                    }
		                }
		            }
		        }]
		    });
		    
		    var catalogSelectId = new Ext.form.TextField({
		        name: 'catalogSelectId',
		        fieldLabel: '选择编目Id',
		        hidden:true,
		        displayField: 'catalogSelectId'
		        });
		    var catalogName = new Ext.form.TextField({
		        name: 'catalogName',
		        fieldLabel: '选择编目',
		        displayField: 'catalogName',
		        emptyText: '--点击选择编目--',
		        labelWidth: 80,
		        labelAlign: 'right',
		        padding: '0 5 0 0',
		        columnWidth: .5,
		        readOnly:true
		    });
		    var lableSelectId = new Ext.form.TextField({
		        name: 'lableSelectId',
		        fieldLabel: '选择标签Id',
		        hidden:true,
		        displayField: 'lableSelectId'
		           });
		    var lableName = new Ext.form.TextField({
		        name: 'lableName',
		        fieldLabel: '选择标签',
		        displayField: 'lableName',
		        emptyText: '--点击选择标签--',
		        labelWidth: 80,
		        labelAlign: 'right',
		        padding: '0 5 0 0',
		        columnWidth: .5
		    });
		    
		    var scName = new Ext.form.TextField({
		        name: 'scriptName',
		        fieldLabel: '脚本名称',
		        displayField: 'scriptName',
		        emptyText: '',
		        labelWidth: 80,
		        labelAlign: 'right',
		        padding: '0 5 0 0',
		        columnWidth: .5,
		        regex: /^[0-9a-zA-Z_]{1,}$/,
		        regexText:'只允许输入数字、字母、下划线' //^\w+$/gi,  
		    });

		    var funcDesc = Ext.create('Ext.form.field.TextArea', {
		        name: 'funcdesc',
		        displayField: 'funcdesc',
		        emptyText: '请输入脚本说明...',
		        columnWidth: .5,
		        height: 300,
		        fieldLabel: '脚本说明',
		        labelWidth: 80,
		        labelAlign: 'right',
		        autoScroll: true,
		        listeners: {
		            'blur': function( me, e, eOpts) {
		                scriptDesc = me.getValue();
		                funcDescInWin.setValue(scriptDesc);
		            }
		        }
		    });
		    
		    var funcScriptDesc = Ext.create('Ext.form.field.TextArea', {
		        name: 'funcScriptDesc',
		        displayField: 'funcScriptDesc',
		        fieldLabel: '脚本输出说明',
		        emptyText: '请输入脚本输出说明...',
		        columnWidth: .5,
		        height: 300,
		        labelWidth: 85,
		        labelAlign: 'right',
		        autoScroll: true,
		        listeners: {
		            'blur': function( me, e, eOpts) {
		                scriptDesc = me.getValue();
		            }
		        }
		    });
		    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
		        name: 'funcDescInWin',
		        fieldLabel: '功能说明',
		        displayField: 'funcDescInWin',
		        emptyText: '请输入功能说明...',
		        labelWidth: 80,
		        labelAlign: 'right',
		        height: 300,
		        columnWidth: .99,
		        autoScroll: true
		    });
		    Ext.define('dbModel', {
		        extend : 'Ext.data.Model',
		        fields : [ {
		            name : 'id',
		            type : 'string'
		        }, {
		            name : 'name',
		            type : 'string'
		        } ]
		    });
		    var dbStore = Ext.create('Ext.data.Store', {
		        autoDestroy : true,
		        autoLoad : true,
		        model : 'dbModel',
		        proxy : {
		            type : 'ajax',
		            url : 'getDatabaseType.do',
		            reader : {
		                type : 'json',
		                root : 'dataList'
		            }
		        }
		    });
		 // 数据库类型
		    var dbType = Ext.create('Ext.form.field.ComboBox', {
		        name : 'dbType',
		        labelWidth : 80,
		         labelAlign: 'right',
		        columnWidth : .5,
		        queryMode : 'local',
		        fieldLabel : '数据库类型',
		        padding : '0 5 0 0',
		        editable : false,
		        displayField : 'name',
		        valueField : 'id',
		        emptyText : '--请选择数据库类型--',
		        store : dbStore
		    });
		    
		    // 服务类型
		    var serviceType = Ext.create('Ext.form.field.ComboBox', {
		        name : 'serviceType',
		        labelWidth : 80,
		         labelAlign: 'right',
		        columnWidth : .5,
		        queryMode : 'local',
		        fieldLabel : '脚本类型',
		        padding : '0 5 0 0',
		        editable : false,
		        displayField : 'text',
		        valueField : 'value',
		        value : '1',
		        emptyText : '--请选择脚本类型--',
		        store : new Ext.data.SimpleStore({
		            fields : [ 'value', 'text' ],
		            data : [ [ '0', '应用' ], [ '1', '采集' ] ]
		        }),
		        listeners : {
		            select : function(combo, record, opts) {
		                },
		            change : function(nf, newv, oldv) {
		            }
		        }
		    });
		    var isPerm = Ext.create('Ext.form.field.ComboBox', {
		        name : 'isPerm',
		        labelWidth : 80,
		        labelAlign: 'right',
		        columnWidth : .5,
		        queryMode : 'local',
		        fieldLabel : '是否复核',
		        padding : '0 5 0 0',
		        editable : false,
		        displayField : 'text',
		        valueField : 'value',
		        value : '1',
		        emptyText : '--请选择是否复核--',
		        store : new Ext.data.SimpleStore({
		            fields : [ 'value', 'text' ],
		            data : [ [ '0', '是' ], [ '1', '否' ] ]
		        }),
		        listeners : {
		        select : function(combo, record, opts) {
		        },
		        change : function(nf, newv, oldv) {
		        }
		        }
		    });
		       var userType = Ext.create('Ext.form.field.ComboBox', {
		            name : 'userType',
		            labelWidth : 80,
		            labelAlign: 'right',
		            columnWidth : .5,
		            queryMode : 'local',
		            fieldLabel : '执行账号',
		            padding : '0 5 0 0',
		            editable : false,
		            displayField : 'text',
		            valueField : 'value',
		            value : '1',
		            emptyText : '--请选择执行账号--',
		            store : new Ext.data.SimpleStore({
		                fields : [ 'value', 'text' ],
		                data : [ [ '0', '查询' ], [ '1', '操作' ] ]
		            }),
		            listeners : {
    		            select : function(combo, record, opts) {
    		            },
    		            change : function(nf, newv, oldv) {
    		            }
		            }
		        });
		       
		    var formItems = [ {
		            border : false,
		            layout : 'column',
		            margin : '10',
		            items : [scName,userType]
		        }, {
		            border : false,
		            layout : 'column',
		            margin : '10',
		            items : [ dbType, serviceType ]
		        }, {
		            border : false,
		            layout : 'column',
		            margin : '10',
		            items : [ isPerm ]//
		        },{
		            layout : 'column',
		            border : false,
		            margin : '10',
		            items : [ catalogName,lableName]
		        }, {
		            layout : 'column',
		            border : false,
		            margin : '10',
		            items : [ funcDesc,funcScriptDesc]
		        } ];
		    
		    var scriptForm = Ext.create('Ext.form.Panel', {
		        region: 'center',
		        border: false,
		        layout: 'anchor',
		        collapsible : false,
		        items: formItems
		    });
		    
		    Ext.define('paramModel', {
		        extend: 'Ext.data.Model',
		        fields: [{
		            name: 'iid',
		            type: 'int'
		        },
		        {
		            name: 'paramType',
		            type: 'string'
		        },
		        {
		            name: 'paramDefaultValue',
		            type: 'string'
		        },
		        {
		            name: 'paramDesc',
		            type: 'string'
		        },
		        {
		            name: 'paramOrder',
		            type: 'int'
		        }]
		    });
		    
		    paramStore.on('beforeload', function(store, options) {
		        var new_params = {
		            scriptId: newUuid
		        };

		        Ext.apply(paramStore.proxy.extraParams, new_params);
		    });
		    
    	var defultEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true
			})
		});
		var passwordEditor = Ext.create('Ext.grid.CellEditor',{
			field : Ext.create('Ext.form.field.Text',{
				selectOnFocus : true,
				inputType : 'password'
			})
		});
		    
		    var paramColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
		    {
		        text: '主键',
		        dataIndex: 'iid',
		        width: 40,
		        hidden: true
		    },
		   {
		        text: '类型',
		        dataIndex: 'paramType',
		        width: 80,
		        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		        	var coun ='';
		        	if(value == 'IN-string(加密)'){
						 coun =  StringToPassword(record.get('paramDefaultValue'));
		        	} else{
		        		 coun = record.get('paramDefaultValue');
		        	} 
		        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+value 
		            		+"<br>默认值："+coun
		            		+"<br>排    序："+record.get('paramOrder')
		            		+"<br>描    述："+record.get('paramDesc')) 
		            		+ '"';  
	                return value;  
	            }
		    },
		    {
					xtype : 'gridcolumn',
					dataIndex : 'paramDefaultValue',
					width: 150,
					flex: 1,
					text : '默认值',
					getEditor : function(record) {
						if (record.get('paramType') != 'IN-string(加密)' ) {
							return defultEditor;
						} else {
							return passwordEditor;
						}
					},
					renderer : function(value, metaData, record, rowIdx, colIdx, store){  
			        	var backValue = "";
			        	if(record.get('paramType')== 'IN-string(加密)'){
			        		backValue = StringToPassword(value);
			        	}else{
			        		backValue = value;
			        	}
			        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
				             		+"<br>默认值："+backValue
				             		+"<br>排    序："+record.get('paramOrder')
				             		+"<br>描    述："+record.get('paramDesc')) 
				             		+ '"';  
			        	
			        	return backValue;
			        }
				},
		    {
		        text: '描述',
		        dataIndex: 'paramDesc',
		        flex: 1,
		        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	        		var coun ='';
		        	if(record.get('paramType') == 'IN-string(加密)'){
						 coun =  StringToPassword(record.get('paramDefaultValue'));
		        	} else{
		        		 coun = record.get('paramDefaultValue');
		        	} 
		        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+value 
		            		+"<br>默认值："+coun
		            		+"<br>排    序："+record.get('paramOrder')
		            		+"<br>描    述："+record.get('paramDesc')) 
		            		+ '"';
	                return value;  
	            }
		    },
		    {
		        text: '顺序',
		        dataIndex: 'paramOrder',
		        width: 60,
		        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		        	 var backValue = "";
			        	if(record.get('paramType')== 'IN-string(加密)'){
			        		backValue = StringToPassword(record.get('paramDefaultValue'));
			        	}else{
			        		backValue = record.get('paramDefaultValue');
			        	}
			        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
				             		+"<br>默认值："+backValue
				             		+"<br>排序："+record.get('paramOrder')
				             		+"<br>描述："+record.get('paramDesc')) 
				             		+ '"';  
			        	
			        	return value; 
	            }
		    }];
		    
		    var paramGrid = Ext.create('Ext.grid.Panel', {
		        region: 'center',
		        margin: '-3 0 0 0',
		        title: "脚本参数",
		        cls:'customize_panel_back',
		        store: paramStore,
		        border: true,
		        columnLines: true,
		        columns: paramColumns
		    });
		    
		    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
		    	region: 'east',
		        collapsible : false,
		        border: false,
		        width: '30%',
		        layout: {
		            type: 'border'
		        },
		        items: [paramGrid]
		    });
		    
		    var mainP = Ext.create('Ext.panel.Panel', {
		        minHeight: 80,
		        border: false,
		        autoScroll: true,
		        region:'center',
		        cls:'customize_panel_back',
		        height: contentPanel.getHeight(),
		        html: '<textarea id="code-edit-view" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>',
		        tbar: [scriptRadio,],
		        tools:[{
		           xtype: 'tool',
		           type: 'restore',
		           handler: function(event, toolEl, owner, tool) {
		           if(!owner.maximize)
		           {
		               owner.restoreSize = owner.getSize();
		               owner.maximize = true;
		               editor.setSize(mainP.getWidth()-2, mainP.getHeight()-120 );
		           }else{
		               owner.maximize = false;
		               owner.setSize(owner.restoreSize);
		               editor.setSize(mainP.getWidth()-2, mainP.getHeight()-160 );
		           }
		          }
		      }]
		    });

		    
		    var preStepbtn = Ext.create('Ext.Button', {
		        text: '上一步',
		        cls: 'Common_Btn',
		        handler: function() {
		        tabsCenter.setActiveTab(0);
		        }
		    });
		    var basicPanel = Ext.create('Ext.panel.Panel', {
                border: false,
                autoScroll: true,
                title: "脚本信息",
                layout: 'border',
                items: [scriptForm],
                buttonAlign: 'center',
                buttons: [preStepbtn,{
                    text : '版本回退',
                    handler : versionRollBack
                }, {
                    text : '返回',
                    handler : fanhui
                }]
          });
		    var maimPanels = Ext.create('Ext.panel.Panel', {
		          border: false,
		          layout:{
		              type:'border'
		          },
		          title: "编辑框---sql",
		          items: [mainP],
		            buttonAlign: 'center',
		            buttons: [{
		                text : '下一步',
		                handler : nextStep
		            },{
	                    text : '版本回退',
	                    handler : versionRollBack
	                }, {
	                    text : '返回',
	                    handler : fanhui
	                }]
		  });

            var tabsCenter = Ext.createWidget('tabpanel', {
                // activeTab: 1, //指定默认的活动tab
                region: 'center',
                minHeight: 80,
                height: contentPanel.getHeight(),
                plain: true,                        // True表示tab候选栏上没有背景图片（默认为false）
                enableTabScroll: true,              // 选项卡过多时，允许滚动
                defaults: { autoScroll: true },
                border:true,
                layout: {
                    type: 'border'
                },
                items: [ maimPanels,basicPanel]
            });
		    
		    var westPanel = Ext.create('Ext.panel.Panel', {
		    	region: 'center',
		        layout: {
		            type: 'border'
		        },
		        defaults: {
		            split: true
		        },
		        autoScroll: true,
		        border: false,
		        cls:'customize_panel_back panel_space_right',
		        items: [tabsCenter],
		        buttonAlign : 'center'
		    });
		    
		    var rightPanel = Ext.create('Ext.panel.Panel', {
		    	region: 'center',
		        layout: {
		            type: 'border'
		        },
		        defaults: {
		            split: true
		        },
		        autoScroll: true,
		        border: false,
		        height: contentPanel.getHeight(),
		        items: [westPanel, paramsAndFuncDescPanel]
		    });
			
			
			mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "dbaasScriptVersion_area",
				layout: {
		            type: 'border'
		        },
				border : false,
				height : contentPanel.getHeight()-modelHeigth,
				items : [ versionGrid, rightPanel]
			});
			
			editor = CodeMirror.fromTextArea(document.getElementById('code-edit-view'), {
		        mode: 'shell',
		        lineNumbers: true,
		        matchBrackets: true,
		        readOnly: true
		    });
		    editor.setSize(mainP.getWidth()-2, mainP.getHeight()-125);
		    contentPanel.on('resize',
		    function() {
		        editor.getDoc().clearHistory();
		        mainPanel.setHeight(contentPanel.getHeight());
		        mainPanel.setWidth(contentPanel.getWidth());
		        editor.setSize(mainP.getWidth()-2, mainP.getHeight() -125);
		    });
			
			function fanhui() {				
				destroyRubbish(); // 销毁本页垃圾
				contentPanel.getLoader().load({
					url : 'myDbaasScriptManage.do',
					params: {
				    'filterScriptName': filter_serviceNameForViewVersion,
                    'filterScriptType': filter_scriptTypeForViewVersion,
                    'filterScriptStatus':filter_scriptStatusForViewVersion
        			},
					scripts : true
				});
			}
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};
			function versionRollBack() {
				var selectedRows = versionGrid.getSelectionModel().getSelection();
				if (selectedRows.length ==1) {
				    var lastid=selectedRows[0].data.lastid;
					var maxId = selectedRows[0].data.iid;
					if(lastid==maxId) {
					    Ext.Msg.alert('提示', "版本回退，所选版本记录已是最新版本，不需回退！");
                        return;
					}
					Ext.Ajax.request({
					    url : 'scriptStatus.do',
					    method : 'POST',
					    params : {
					    	serviceId: maxId
					    },
					    success: function(response, opts) {
					        var status = Ext.decode(response.responseText).status;
					        if (status == 2 ) { // 处于审核中
								Ext.Msg.alert('提示', "该脚本正在审核中，请等待审核结束后回退！");
								return;
							}else{
								var noVersionId = -1;
								var noVersionUuid = "";
								var selectedRow = selectedRows[0];
								if("无版本号"==selectedRow.data.onlyVersion) {
									Ext.Msg.alert('提示', '该版本无版本号，无法回退！');
									return;
								} else {
									var canRoll = false;
									var hasNoVersion = false;
									versionStore.each(function(record) {   
								       if(record.get('onlyVersion')=="无版本号") {
								            hasNoVersion = true;
								            noVersionId = record.get('iid');
								            noVersionUuid = record.get('uuid');
								            return;
								       }
								    });  
								    
								    versionStore.each(function(record) {   
								       if(selectedRow.data.iid!=record.get('iid')) {
							    		   if(parseFloat(selectedRow.data.onlyVersion)<parseFloat(record.get('onlyVersion'))) {
								    		   canRoll = true;
								    		   return;
									       }
							    	   }
								    }); 
								    if(hasNoVersion) {
										 Ext.MessageBox.buttonText.yes = "确定"; 
					                     Ext.MessageBox.buttonText.no = "取消"; 
								       	 Ext.Msg.confirm("请确认", "是否要回退该版本，如强制回退将会覆盖现在无版本号中内容！",
			                             function(button, text) {
			                             if (button == "yes") {
									    	Ext.Ajax.request({
											url : 'hasVersionRollBackForDbaas.do',
											method : 'POST',
											sync : true,
											params : {
												iid : newServiceId,
												oldId : noVersionId,
												oldUuid :noVersionUuid,
												uuid:newUuid
											},
											success : function(response, request) {
												var success = Ext.decode(response.responseText).success;
												if (success) {
													Ext.Msg.alert('提示', '版本回退成功！');
//													var lastId = Ext.decode(response.responseText).lastId;
													versionStore.load();
												} else {
													Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
												}
											},
											failure : function(result, request) {
												secureFilterRs(result, "版本回退失败！");
											}
										});  
			                             }else{
			                             	return;
			                             }
			                              });
									
								    } else {
								    	if(canRoll) {
											Ext.Ajax.request({
												url : 'versionRollBack.do',
												method : 'POST',
												sync : true,
												params : {
													iid : newServiceId,
													uuid:newUuid
												},
												success : function(response, request) {
													var success = Ext.decode(response.responseText).success;
													if (success) {
														Ext.Msg.alert('提示', '版本回退成功！');
//														var lastId = Ext.decode(response.responseText).lastId;
														versionStore.load();
													} else {
														Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
													}
												},
												failure : function(result, request) {
													secureFilterRs(result, "版本回退失败！");
												}
											});
								    	} else{
											Ext.Msg.alert('提示', '选择的版本是最新版本，无法回退！');
											return;
										}
								    }
									
								}
							}
					    },
					    failure: function(result, request) {
					    	secureFilterRs(result,"操作失败！");
					    	return;
					    }
					});
				} else {
				    Ext.Msg.alert('提示', '只能选择一个版本进行版本回退！');
					return;
				}
			}
			
			function compareScript() {
				var seledCnt = selModel.getCount();
				if(seledCnt != 2){
					Ext.MessageBox.alert("提示", "请选择两个不同版本的脚本进行比对！");
					return ;
				}
				var ss = selModel.getSelection();
				var ids = [];
				for ( var i = 0; i < 2; i++) {
					var version1 =ss[i].data.iid;
					ids.push(version1);
				}
				  if(ids.length>0){
	                	Ext.Ajax.request( {
	    					url : 'compareScript.do',
	    					method : 'post',
	    					params : {
	    								ids : ids
	    							},
	    					success : function(response, request) {
	    							var success = Ext.decode(response.responseText).success;
	    							var message = Ext.decode(response.responseText).message;
	    								if (success) {
	    									var leftVersion = Ext.decode(response.responseText).leftVersion;
	    									var rightVersion = Ext.decode(response.responseText).rightVersion;
	    									var leftContent = Ext.decode(response.responseText).leftContent;
	    									var rightContent = Ext.decode(response.responseText).rightContent;
	    								    var compareWin = Ext.create('widget.window', {
	    								        title: '对比详情',
	    								        closable: true,
	    								        closeAction: 'destroy',
	    								        width: contentPanel.getWidth()-200,
	    								        minWidth: 350,
	    								        height: contentPanel.getHeight(),
	    								        draggable: false,
	    								        // 禁止拖动
	    								        resizable: false,
	    								        // 禁止缩放
	    								        modal: true,
	    								        loader: {
	    								            url: 'showCompare.do',
	    								            params: {
	    								            	leftVersion : leftVersion,
	    								            	rightVersion : rightVersion,
	    								            	leftContent : leftContent,
	    								            	rightContent : rightContent
	    								            },
	    								            autoLoad: true,
	    								            scripts: true
	    								        }
	    								    });
	    								    compareWin.show();
	    								} else {
	    									Ext.Msg.alert('提示', message);
	    								}
	    							},
	    							failure : function(result, request) {
	    								secureFilterRs(result,"请求返回失败！",request);
	    							}
	    						});
	                }
			}
			
		    editScriptStore.on('beforeload', function(store, options) {
		        var queryparams = {
		            iid: newServiceId
		        };
		        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
		    });
		    editScriptStore.on('load', function(store, options, success) {
		        var reader = store.getProxy().getReader();
		        if(reader.jsonData.ISCRIPTNAME) {
                    maimPanels.setTitle("脚本编辑_"+reader.jsonData.ISCRIPTNAME);
                } else {
                    maimPanels.setTitle("编辑框");
                }
		        newServiceId=reader.jsonData.IID;
		        newUuid=reader.jsonData.ISCRIPTUUID;
                scName.setValue(reader.jsonData.ISCRIPTNAME);
                funcDesc.setValue(reader.jsonData.IFUNCDESC);
                userType.setValue(reader.jsonData.IUSERTYPE);
                dbType.setValue(reader.jsonData.IDBTYPE);
                serviceType.setValue(reader.jsonData.ISERVICETYPE);
                isPerm.setValue(reader.jsonData.ISPERM);
                funcScriptDesc.setValue(reader.jsonData.IINPUTDESC);
                catalogSelectId.setValue(reader.jsonData.catalogSelectId);
                catalogName.setValue(reader.jsonData.catalogName);
                lableSelectId.setValue(reader.jsonData.lableSelectId);
                lableName.setValue(reader.jsonData.lableName);
                var scriptT = reader.jsonData.ISCRIPTTYPE;
                if (scriptT == 'sh') {
                    editor.setOption("mode", 'shell');
                } else if (scriptT == 'bat') {
                    editor.setOption("mode", 'bat');
                } else if (scriptT == 'py') {
                    editor.setOption("mode", 'python');
                } else if (scriptT == 'sql') {
                    editor.setOption("mode", 'sql');
                } else if (scriptT == 'perl') {
                    editor.setOption("mode", 'text/x-perl');
                }
                editor.setOption('value', '');
                editor.setOption('value', reader.jsonData.ICONTENT);
                editor.refresh();
		        paramStore.load();
		    });
		    
		   function StringToPassword(strs){
				if(strs&&strs!=null&strs!=''){
					var password = '';
					for(var i=0;i<strs.length;i++){
						password = password + '●';
					}
					return password;
				}else{
					return '';
				}
			}
           function nextStep() {
               tabsCenter.setActiveTab(1);
           }
});
