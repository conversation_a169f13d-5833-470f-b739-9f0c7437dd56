<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<% 
	long serviceId=Long.parseLong(request.getParameter("serviceId"));
%>
<html>
<head>
<script type="text/javascript">
	var scriptdbaasId="<%=serviceId%>";
	var scriptTypeForVersion="<%=request.getParameter("scriptType")==null?"":request.getParameter("scriptType")%>";
	var filter_scriptNameForViewVersion = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
	var filter_serviceNameForViewVersion = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
	var filter_scriptTypeForViewVersion = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
	var filter_scriptStatusForViewVersion = '<%=request.getParameter("filter_scriptStatus")==null?-10000:request.getParameter("filter_scriptStatus")%>';
	var scriptuid = '<%=request.getParameter("uuid")%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dbaas/scriptmanage/dbaasScriptVersion.js"></script>
	<link type="text/css" rel="stylesheet" href="<%=request.getContextPath()%>/css/mergely.css"  />
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/mergely.js" ></script>
</head>
<body>
	<div id="dbaasScriptVersion_area"></div>
</body>
</html>