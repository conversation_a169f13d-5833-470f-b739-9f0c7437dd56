Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    /** *********************Panel********************* */
    var lableWindow;
    var lableGrid;
    var lableIds = [];
	var lableNames= []; 
	var lableLoadCount=0;
    var scriptRadio = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 60,
        hidden:true,
        labelCls :Ext.baseCSSPrefix + 'form-item-label label_space',
        name: 'ra_s_type_win',
        items: [{
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '0',
            boxLabel: 'shell',
            hidden:true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                        editor.setOption("mode", 'shell');
                        maimPanels.setTitle('编辑框---shell');
                        agentPullChosedStore.load(
   						{
   							url:"getTryAgentList.do"
						 });
                    }
                }
            }
        },
        {
            name: 'ra_s_type_win',
            width: 50,
            inputValue: '1',
            boxLabel: 'bat',
            hidden:true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                            editor.setOption("mode", 'bat');
                        maimPanels.setTitle('编辑框---bat');
                        agentPullChosedStore.load(
   						{
   							url:"getTryAgentList.do"
						 });
                    }
                }
            }
        },
        {
            name: 'ra_s_type_win',
            width: 50,
            inputValue: '2',
            boxLabel: 'perl',
            hidden:true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                        editor.setOption("mode", 'text/x-perl');
                        maimPanels.setTitle('编辑框---perl');
                        agentPullChosedStore.load(
   						{
   							url:"getTryAgentList.do"
						 });
                    }
                }
            }
        },
        {
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '3',
            hidden:true,
            boxLabel: 'python',
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                        editor.setOption("mode", 'python');
                        maimPanels.setTitle('编辑框---python');
                        agentPullChosedStore.load(
   						{
   							url:"getTryAgentList.do"
						 });
                    }
                }
            }
        },{
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '4',
            boxLabel: 'sql',
            checked:true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                            editor.setOption("mode", 'text/x-plsql');
                            maimPanels.setTitle('编辑框---sql');
                            if(chooseSqlExecModel.getValue() == '2'){
                            	agentPullChosedStore.load(
           						{
           							url:"getTryAgentListByResource.do"
        						 });
                            }
                    }
                }
            }
        }]
    });
    
    var agentPullChosedStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getTryAgentListByResource.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var sqlexectype = Ext.create('Ext.data.Store', {
        fields: ['iid', 'name'],
        data : [
            {"iid":"1", "name":"Agent"},
            {"iid":"2", "name":"JDBC"}
        ]
    });
    
    var chooseSqlExecModel = Ext.create('Ext.form.field.ComboBox', {
    	name: 'chooseSqlExecModel',
    	width: 100,
    	displayField: 'name',
    	valueField: 'iid',
    	editable: false,
    	queryMode: 'local',
    	value:'2',
    	emptyText: '执行模式',
    	store: sqlexectype,
	    listeners: {//监听事件
	    	select: function () {
            	if(chooseSqlExecModel.getValue() == '2'){
            		agentPullChosedStore.load(
					{
						url:"getTryAgentListByResource.do"
					 });                              	
                }else{
                	agentPullChosedStore.load(
					{
						url:"getTryAgentList.do"
					 });
                  }
            	}          	            	
         }
    });

    var catalogSelectId = new Ext.form.TextField({
        name: 'catalogSelectId',
        fieldLabel: '选择编目Id',
        hidden:true,
        displayField: 'catalogSelectId'
           });
    var catalogName = new Ext.form.TextField({
        name: 'catalogName',
        fieldLabel: '选择编目',
        displayField: 'catalogName',
        emptyText: '--点击选择编目--',
        labelWidth: 80,
        labelAlign: 'right',
        padding: '0 5 0 0',
        columnWidth: .5,
//        readOnly:true,
        listeners: {
            render: function(p) {  
              p.getEl().on('click', function(p){
                  Ext.define ('catalogingModel',
                      {
                          extend : 'Ext.data.Model',
                          fields : [
                                  {
                                      name : 'iName',
                                      type : 'string'
                                  },
                                  {
                                      name : 'iId',
                                      type : 'long'
                                  },
                                  { 
                                      name : 'iCreateUser',
                                      type : 'string'
                                  },
                                  {
                                      name : 'iCreateTime',
                                      type : 'string'
                                  }, 
                                  { 
                                      name : 'iModifyUser',
                                      type : 'string'
                                  },
                                  {
                                      name : 'iModifyTime',
                                      type : 'string'
                                  },
                                  {
                                      name : 'iParentId',
                                      type : 'string'
                                  }
                                  
                          ]
                      });
                      /** *********************Store********************* */
                      /** 编目列表数据源* */

                      var catalogingStore = Ext.create ('Ext.data.TreeStore',
                      {
                          model : 'catalogingModel',
                          proxy :
                          {
                              type : 'ajax',
                              url : 'catalogingTreeList.do?scriptId='+oldScriptId
                          },
                          root : {
                              expanded : true,
                              leaf : false
                          },
                          autoLoad:true
                      });
                      var gridColumns = [
                                         {
                                             text : 'ID',
                                             dataIndex : 'iId',
                                             flex : 1,
                                             hidden : true
                                         },
                                         {
                                             
                                             xtype: 'treecolumn', 
                                             text : '编目名称',
                                             dataIndex : 'iName',
                                             flex : 1,
                                             renderer : function(value, metadata) {
                                                 metadata.tdAttr = 'data-qtip="' + value + '"';
                                                 return value;
                                             }
                                         }
//                                         ,{
//                                             text : '确认项',
//                                             dataIndex : 'iId',
//                                             xtype : 'actioncolumn',
//                                             sortable : false,
//                                             menuDisabled : true,
//                                             width : 80,
//                                             items : [ {
//                                                 iconCls:'role_permission',
//                                                 tooltip : '确认',
//                                                 handler : okdata
//                                             } ]
//                                         }
                                 ];
                      
                      var catalogingForm = Ext.create('Ext.form.Panel', {
                          border: false,
                          collapsible : false,
                          region : 'north',
                          dockedItems : [ {
                              xtype : 'toolbar',
                              dock : 'top',
                              baseCls:'customize_gray_back',
                              border : false,
                              items : ['->',
                  	            {
                    	            itemId: 'clear',
                  	                text: '清空',
                  	                cls: 'Common_Btn',
                  	                handler:function() {
                  	                	catalogName.setValue("");
                                        catalogSelectId.setValue("");
                                        this.up('window').close();
                  					}  
                  	            }]
                          }]       
                      });
                         /** *********************Panel********************* */
                         /** 业务系统类型列表panel* */
                         var gridPanel = Ext.create ('Ext.tree.Panel',
                         {
                             store : catalogingStore,
                             region : 'center',
                             padding : panel_margin,
//                             selModel : Ext.create ('Ext.selection.CheckboxModel'),
                             useArrows: true,  
                             rootVisible: false,  
                             columns : gridColumns,
                             cls:'customize_panel_back',
                             columnLines : true,
                             border : true,
                             listeners: {
                                 itemdblclick : function(dbclickthis, record, item,index, e, eOpts) {
                                     var iId=record.data.iId;
                                     var iName=record.data.iName;
                                     catalogName.setValue(iName);
                                     catalogSelectId.setValue(iId);
                                     this.up('window').close();
                                 }
                             }
                         });
//                         function okdata() {
//                             var record= gridPanel.getSelectionModel().getSelection();
//                              var iid = record[0].data.iId;
//                              var iName = record[0].data.iName;
//                              catalogName.setValue(iName);
//                              catalogSelectId.setValue(iid);
//                              this.up('window').close();
//                          }
                Ext.create('Ext.window.Window', {
                      title : '选择编目配置',
                      modal : true,
                      closeAction : 'destroy',
                      constrain : true,
                      autoScroll : true,
                      width : 500,
                      height : 600,
                      draggable : false,// 禁止拖动
                      resizable : false,// 禁止缩放
                      layout : 'border',
                      items: [catalogingForm,gridPanel]
                  }).show();
               });  
           }
        }
    });
    var lableSelectId = new Ext.form.TextField({
        name: 'lableSelectId',
        fieldLabel: '选择标签Id',
        hidden:true,
        displayField: 'lableSelectId'
           });
    var lableName = new Ext.form.TextField({
        name: 'lableName',
        fieldLabel: '选择标签',
        displayField: 'lableName',
        emptyText: '--点击选择标签--',
        labelWidth: 80,
        labelAlign: 'right',
        padding: '0 5 0 0',
        columnWidth: .5,
        listeners: {
            render: function(p) {
            p.getEl().on('click', function(p){
                Ext.define ('labelModel',
                    {
                        extend : 'Ext.data.Model',
                        fields : [
                                {
                                    name : 'iName',
                                    type : 'string'
                                },
                                {
                                    name : 'iId',
                                    type : 'long'
                                },
                                { 
                                    name : 'iCreateUser',
                                    type : 'string'
                                },
                                {
                                    name : 'iCreateTime',
                                    type : 'string'
                                }, 
                                { 
                                    name : 'iModifyUser',
                                    type : 'string'
                                },
                                {
                                    name : 'iModifyTime',
                                    type : 'string'
                                },
                                {
                                    name : 'iParentId',
                                    type : 'string'
                                },
                                {
                                    name : 'selectStatus',
                                    type : 'string'
                                }
                                
                        ]
                    });
                    /** *********************Store********************* */
                    /** 编目列表数据源* */

                    var labelStore = Ext.create ('Ext.data.TreeStore',
                    {
                        model : 'labelModel',
                        proxy :
                        {
                            type : 'ajax',
                            url : 'labelTreeList.do?scriptId='+oldScriptId
                        },
                        root : {
                            expanded : true,
                            leaf : false
                        },
                        autoLoad:true
                    });
                    var selModelLable = Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true
                    });
                   
                    labelStore.on('beforeload', function(store, options) {
                        var new_params = {
                        		scriptId:oldScriptId,
 //                               name:lbName.getValue()
                        };

                        Ext.apply(labelStore.proxy.extraParams, new_params);
                    });
                    
                    labelStore.on('load',function(store,node,records,successful,eOpts){                    	
                    	console.log('lableLoadCount',lableLoadCount);
                    	var addrecords = [];// 存放选中记录
                        for(var i = 0; i<records.length;i++){                              	
                        	var selectStatus=records[i].data.selectStatus
                        	var iId=records[i].data.iId
                        	if(selectStatus==1 && lableLoadCount==0){
                        		lableIds.push(iId);
                        		lableNames.push(records[i].data.iName);
                        	}  
                        	
                        	if(lableIds.indexOf(iId)>-1){
                        		addrecords.push(records[i]);
                        	}
                        }  
                        selModelLable.select(addrecords);// 选中记
                        lableLoadCount++; 
                     }); 
                    
                    var lableForm = Ext.create('Ext.form.Panel', {
                        border: false,
                        collapsible : false,
                        region : 'north',
                        dockedItems : [ {
                            xtype : 'toolbar',
                            dock : 'top',
                            baseCls:'customize_gray_back',
                            border : false,
                            items : ['->',{
                  	                itemId: 'oper',
                	                text: '确定',
                	                cls: 'Common_Btn',
                	                handler: lableSelect
                	            },{
                  	                itemId: 'clear',
                	                text: '清空',
                	                cls: 'Common_Btn',
                	                handler:function() {
                	                	var data = lableGrid.getView().getSelectionModel().getSelection();                	                	
                	                    selModelLable.deselect(data);// 取消记录
                	            	    lableSelectId.setValue("");
                	            	    lableName.setValue("");
                	                	lableWindow.close();
                					}  
                	            }]
                        }]       
                    });
                    var gridColumns = [
                                       {
                                           text : 'ID',
                                           dataIndex : 'iId',
                                           flex : 1,
                                           hidden : true
                                       },
                                       {
                                           
                                           xtype: 'treecolumn', 
                                           text : '标签名称',
                                           dataIndex : 'iName',
                                           flex : 1,
                                           renderer : function(value, metadata) {
                                               metadata.tdAttr = 'data-qtip="' + value + '"';
                                               return value;
                                           }
                                       }
                               ];
                    lableGrid = Ext.create ('Ext.tree.Panel',
                    {                    	
                    	selModel: selModelLable,
                        store : labelStore,
                        region : 'center',
                        padding : panel_margin,
//                        selModel : Ext.create ('Ext.selection.CheckboxModel'),
                        useArrows: true,  
                        rootVisible: false,  
                        columns : gridColumns,
                        cls:'customize_panel_back',
                        columnLines : true,
                        border : true,
                	    listeners: {
                			select : function(selModelLable, record, index, eOpts) {
                				if(lableIds.indexOf(record.data.iId) <= -1){
                					lableIds.push(record.data.iId);  
                					lableNames.push(record.data.iName);  
                				}
                				
                				             				
                			},
                			deselect : function(roleSelModel, record, index, eOpts) {
                				var delID=record.data.iId;
                				var delName=record.data.iName;
                				lableIds.splice(lableIds.indexOf(delID),1);
                				lableNames.splice(lableNames.indexOf(delName),1);
                			}
                        }
                    });

                    lableWindow=Ext.create('Ext.window.Window', {
                    title : '选择标签配置',
                    modal : true,
                    closeAction : 'destroy',
                    constrain : true,
                    autoScroll : true,
                    width : 500,
                    height : 600,
                    draggable : false,// 禁止拖动
                    resizable : false,// 禁止缩放
                    layout : 'border',
                    items: [lableForm, lableGrid]
                }).show();
             });  
          } 
        }
    });

    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 80,
        labelAlign: 'right',
        padding: '0 5 0 0',
        columnWidth: .5,
        regex: /^[0-9a-zA-Z_]{1,}$/,
		regexText:'只允许输入数字、字母、下划线' //^\w+$/gi,  
    });

    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '请输入脚本说明...',
        columnWidth: 1,
        height: 300,
        fieldLabel: '脚本说明',
        labelWidth: 80,
        labelAlign: 'right',
        autoScroll: true,
        listeners: {
        	'blur': function( me, e, eOpts) {
        		scriptDesc = me.getValue();
        		funcDescInWin.setValue(scriptDesc);
        	}
        }
    });
    
    var funcScriptDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcScriptDesc',
        displayField: 'funcScriptDesc',
        fieldLabel: '脚本输出说明',
        emptyText: '请输入脚本输出说明...',
        columnWidth: .5,
        height: 300,
        labelWidth: 85,
        labelAlign: 'right',
        autoScroll: true,
        hidden:true,
        listeners: {
            'blur': function( me, e, eOpts) {
                scriptDesc = me.getValue();
            }
        }
    });
    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
    	name: 'funcDescInWin',
    	fieldLabel: '功能说明',
    	displayField: 'funcDescInWin',
    	emptyText: '请输入功能说明...',
    	labelWidth: 80,
    	labelAlign: 'right',
    	height: 300,
        columnWidth: .99,
    	autoScroll: true
    });
    Ext.define('dbModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'id',
            type : 'string'
        }, {
            name : 'name',
            type : 'string'
        } ]
    });
    var dbStore = Ext.create('Ext.data.Store', {
        autoDestroy : true,
        autoLoad : true,
        model : 'dbModel',
        proxy : {
            type : 'ajax',
            url : 'getDatabaseType.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });
 // 数据库类型
	var dbType = Ext.create('Ext.form.field.ComboBox', {
		name : 'dbType',
		labelWidth : 80,
		 labelAlign: 'right',
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '数据库类型',
		padding : '0 5 0 0',
		editable : false,
		displayField : 'name',
		valueField : 'id',
		emptyText : '--请选择数据库类型--',
		store : dbStore
	});
	
	// 服务类型
	var serviceType = Ext.create('Ext.form.field.ComboBox', {
		name : 'serviceType',
		labelWidth : 80,
		 labelAlign: 'right',
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '脚本类型',
		padding : '0 5 0 0',
		editable : false,
		displayField : 'text',
		valueField : 'value',
		value : '1',
		emptyText : '--请选择脚本类型--',
		store : new Ext.data.SimpleStore({
			fields : [ 'value', 'text' ],
			data : [ [ '0', '应用' ], [ '1', '采集' ] ]
		}),
		listeners : {
			select : function(combo, record, opts) {
	    
	            },
			change : function(nf, newv, oldv) {
	                
	            }
		}
	});
	var isPerm = Ext.create('Ext.form.field.ComboBox', {
	    name : 'isPerm',
	    labelWidth : 80,
	    labelAlign: 'right',
	    columnWidth : .5,
	    queryMode : 'local',
	    fieldLabel : '是否复核',
	    padding : '0 5 0 0',
	    editable : false,
	    displayField : 'text',
	    valueField : 'value',
	    value : '1',
	    emptyText : '--请选择是否复核--',
	    store : new Ext.data.SimpleStore({
	        fields : [ 'value', 'text' ],
	        data : [ [ '0', '是' ], [ '1', '否' ] ]
	    }),
	    listeners : {
	    select : function(combo, record, opts) {
	    },
	    change : function(nf, newv, oldv) {
	    }
	    }
	});
	   var userType = Ext.create('Ext.form.field.ComboBox', {
	        name : 'userType',
	        labelWidth : 80,
	        labelAlign: 'right',
	        columnWidth : .5,
	        queryMode : 'local',
	        fieldLabel : '执行账号',
	        padding : '0 5 0 0',
	        editable : false,
	        displayField : 'text',
	        valueField : 'value',
	        value : '1',
	        emptyText : '--请选择执行账号--',
	        store : new Ext.data.SimpleStore({
	            fields : [ 'value', 'text' ],
	            data : [ [ '0', '查询' ], [ '1', '操作' ] ]
	        }),
	        listeners : {
	        select : function(combo, record, opts) {
	        },
	        change : function(nf, newv, oldv) {
	        }
	        }
	    });
	var formItems = [ {
			border : false,
			layout : 'column',
			margin : '10',
			items : [scName,userType]
		}, {
            border : false,
            layout : 'column',
            margin : '10',
            items : [ dbType, serviceType ]
        }, {
            border : false,
            layout : 'column',
            margin : '10',
            items : [ isPerm ]//
        },{
            layout : 'column',
            border : false,
            margin : '10',
            items : [ catalogName,lableName]
        }, {
			layout : 'column',
			border : false,
			margin : '10',
			items : [ funcDesc]
		} ];
	
    var scriptForm = Ext.create('Ext.form.Panel', {
        width: contentPanel.getWidth()*0.6,
        height: contentPanel.getHeight()*0.8,
        border: false,
        layout: 'anchor',
        collapsible : false,
        items: formItems
    });

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 20,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: oldScriptUuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

	var defultEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true
		})
	});
	var passwordEditor = Ext.create('Ext.grid.CellEditor',{
		field : Ext.create('Ext.form.field.Text',{
			selectOnFocus : true,
			inputType : 'password'
		})
	});
    var paramTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "string"
        },
        {
            "name": "int"
        },
        {
            "name": "float"
        },{
            "name": "boolean"
        }
        ]
    });

    var paramColumns = [
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },{
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 55,
        editor: {
            allowBlank: false
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){ 
        	 var backValue = "";
	        	if(record.get('paramType')== 'IN-string(加密)'){
	        		backValue = StringToPassword(record.get('paramDefaultValue'));
	        	}else{
	        		backValue = record.get('paramDefaultValue');
	        	}
	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
		             		+"<br>默认值："+backValue
		             		+"<br>排序："+record.get('paramOrder')
		             		+"<br>描述："+record.get('paramDesc')) 
		             		+ '"';  
	        	
	        	return value; 
        }
    },
    {
			xtype : 'gridcolumn',
			dataIndex : 'paramType',
			width : 100,
			text : '参数类型',
			editor : {
				xtype: 'combobox',
			    store: paramTypeStore,
			    queryMode: 'local',
			    displayField: 'name',
			    valueField: 'name',
			    listeners:{
		        	change:function(field,newValue,oldValue){
		        		if(oldValue=='IN-string(加密)' && newValue!='IN-string(加密)'){
		        			var paramDefaultValue = paramGrid.getView().getSelectionModel().getSelection()[0];
		        			paramDefaultValue.set("paramDefaultValue","");
		        		}
		        	}
		        }
			},
		     renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	        	var coun ='';
	        	if(value == 'IN-string(加密)'){
					 coun =  StringToPassword(record.get('paramDefaultValue'));
	        	} else{
	        		 coun = record.get('paramDefaultValue');
	        	} 
	        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
		             		+"<br>默认值："+coun
		             		+"<br>排序："+record.get('paramOrder')
		             		+"<br>描述："+record.get('paramDesc')) 
		             		+ '"';  
	            return value;
	        }
		}, 
	    {
				xtype : 'gridcolumn',
				dataIndex : 'paramDefaultValue',
				width: 80,
				text : '默认值',
				getEditor : function(record) {
					if (record.get('paramType') != 'IN-string(加密)' ) {
						return defultEditor;
					} else {
						return passwordEditor;
					}
				},
				renderer : function(value, metaData, record, rowIdx, colIdx, store){  
		        	var backValue = "";
		        	if(record.get('paramType')== 'IN-string(加密)'){
		        		backValue = StringToPassword(value);
		        	}else{
		        		backValue = value;
		        	}
		        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
			             		+"<br>默认值："+backValue
			             		+"<br>排序："+record.get('paramOrder')
			             		+"<br>描述："+record.get('paramDesc')) 
			             		+ '"';  
		        	
		        	return backValue;
		        }
			},
		    {
		        text: '描述',
		        dataIndex: 'paramDesc',
		        flex: 1,
		        editor: {
		            allowBlank: true
		        },
		        renderer:function (value, metaData, record, rowIdx, colIdx, store){
		        	 var backValue = "";
			        	if(record.get('paramType')== 'IN-string(加密)'){
			        		backValue = StringToPassword(record.get('paramDefaultValue'));
			        	}else{
			        		backValue = record.get('paramDefaultValue');
			        	}
			        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
				             		+"<br>默认值："+backValue
				             		+"<br>排序："+record.get('paramOrder')
				             		+"<br>描述："+record.get('paramDesc')) 
				             		+ '"';  
			        	
			        	return value; 
		        }
		    }];

    function removeByValue(arr, val) {
	  for(var i=0; i<arr.length; i++) {
	    if(arr[i] == val) {
	      arr.splice(i, 1);
	      break;
	    }
	  }
    }

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var paramGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        store: paramStore,
        cls:'window_border panel_space_left panel_space_right',
        selModel: selModel,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns,
        emptyText: '没有脚本参数',
        tools: [{
        	    type:'plus',
            	tooltip: '增加',
                handler: addParam
            },
            {
            	type:'minus',
                tooltip: '删除',
                callback: function(panel, tool, event) {
                    var data = paramGrid.getView().getSelectionModel().getSelection();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的行!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要删除参数？", function(button, text) {
                            if (button == "yes") {
                            	var deleteIds = [];
                            	$.each(data, function(index, record){
                            		if(record.data.iid>0) {
                            			deleteIds.push(record.data.iid);
                            		}else{
                            		    paramStore.remove(data);
                            		}
                            	});
                            if(deleteIds.length>0){
                            	Ext.Ajax.request({
                                    url: 'deleteScriptParams.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                    	iids: deleteIds
                                    },
                                    success: function(response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            paramStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function(result, request) {
                                        secureFilterRs(result, "删除失败！");
                                    }
                                });
                             }else{
                             	paramGrid.getView().refresh();
                             }
                            }
                        });
                    }
                }
            }]
    });
    function addParam() {
        var store = paramGrid.getStore();
        var ro = store.getCount();
        var p = {
                iid: '',
                paramOrder: ro+1,
                paramType: 'string',
                paramDefaultValue: '',
                paramDesc: ''
        };
        store.insert(0, p);
        paramGrid.getView().refresh();
    }
    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
    	region: 'east',
        border: false,
        width: 450,
        height: contentPanel.getHeight(),
        layout: 'border',
        items: [paramGrid]
    });
    var agentPullChosedCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentPullChosed',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        valueField: 'iid',
        editable: true,
        emptyText: '--请尝试服务器--',
        store: agentPullChosedStore
    });
    
    var refreshTryForBasic;
	function tryATry() {
	    		editor.save();
	        	var content = document.getElementById('code').value;
	        	if(!content) {
	        		Ext.Msg.alert('提示', '请填入脚本内容！');
	        		return;
	        	}
	        	
	            var agentId = agentPullChosedCb.getValue();
	            if(!agentId || agentId=='') {
	            	Ext.Msg.alert('提示', '请选择尝试服务器！');
	                return;
	            }
		        function tryReal () {
		        	if(paramInvalidMessage) {
						Ext.MessageBox.alert("提示", paramInvalidMessage);
					} else {
					$('#consoleLog').html("");
		            var scriptPara = getParams();
		            var type = "sql";
		            var  tryRequestId = "-1";
		            var tryAgentIp = "";
		            var tryAgentPort = 15000;
		            Ext.Ajax.request({
			            url: 'tryATryScript.do',
			            method: 'POST',
			            sync: true,
			            timeout:6000000000,
			            params: {
			                scriptName: 'script',
			                scriptType: type,
			                scriptContent: document.getElementById('code').value,
			                scriptPara: scriptPara,
			                agentId: agentId,
							sqlexecModel:chooseSqlExecModel.getValue()
			            },
			            success: function(response, request) {
			                var success = Ext.decode(response.responseText).success;
			                var message = Ext.decode(response.responseText).message;
			                var content = Ext.decode(response.responseText).content;
			                //自动添加输出规则
			                if(success) {
								$('#consoleLog').html(content);
								consolePanel.body.scroll('bottom', 300000);
								tryRequestId = Ext.decode(response.responseText).requestId;
								tryAgentIp = Ext.decode(response.responseText).agentIp;
								tryAgentPort = Ext.decode(response.responseText).agentPort;
			                }else{
								$('#consoleLog').html(content);
							}
			                Ext.Msg.alert('提示', message);
			            },
			            failure: function(result, request) {
			                $('#consoleLog').html("failure:获取信息失败");
			                secureFilterRs(result, "出现错误！");
			            }
			        });
		           }
		         }
		        var p = orgParams();
	        	var paramInvalidMessage = p.paramInvalidMessage;
	        	var someParamIsEmpty = p.someParamIsEmpty;
	        	if(someParamIsEmpty) {
	        		Ext.MessageBox.buttonText.yes = "确定"; 
	        		Ext.MessageBox.buttonText.no = "取消"; 
	        		Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function(id){
	        			if(id=='yes') {
	        				tryReal();
	        			}
	        		});
	        	} else {
	        		tryReal();
	        	}
	    }
    
    var tryATrybtn = Ext.create('Ext.Button', {
        text: '尝试',
        width: 80,
        cls: 'Common_Btn',
        handler: function() {
				tryATry();
        }
    });
    var saveDatabtn = Ext.create('Ext.Button', {
        text: '保存',
        width: 80,
        cls: 'Common_Btn',
        handler: function() {
        	saveData();
        }
    });
    var nextStepbtn = Ext.create('Ext.Button', {
        text: '下一步',
        width: 80,
        cls: 'Common_Btn',
        handler: function() {
        	nextStep();
        }
    });
    var returnScriptbtn = Ext.create('Ext.Button', {
        text: '返回',
        width: 80,
        cls: 'Common_Btn',
        handler: function() {
        	returnScriptManage();
        }
    });
    
    var mainP = Ext.create('Ext.panel.Panel', {
    	minHeight: 80,
        border: false,
        autoScroll: true,
        region:'center',
        cls:'customize_panel_back',
        height: contentPanel.getHeight(),
        html: '<textarea id="code" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>',
        tbar: [chooseSqlExecModel,agentPullChosedCb,tryATrybtn,'->',saveDatabtn,nextStepbtn,returnScriptbtn]
    });

    var consolePanel = Ext.create('Ext.panel.Panel', {
	  	height: 150,
	  	region:'south',
	    border: true,
	    collapsible : false,
        collapsed: false,
	    autoScroll: true,
	    title: '日志',
	    html: '<pre id="consoleLog" style="height:100%;background: #4b4b4b;color: white;margin:0;"></pre>'
	  });
    var maimPanels = Ext.create('Ext.panel.Panel', {
	      border: false,
	      layout:{
	    	  type:'border'
	      },
	      title: "编辑框---sql",
	      items: [mainP,consolePanel]
  });

    var returnScriptManageBtn = Ext.create('Ext.Button', {
        text: '返回',
        handler: returnScriptManage
    });
    var onLineBtn = Ext.create('Ext.Button', {
        text: '上线',
        handler: function(){
        Ext.Msg.confirm("请确认", "是否要设置脚本为上线状态?",
            function(button, text) {
            if (button == "yes") {
                editor.save();
                var content = document.getElementById('code').value;
                if(content.trim() == ''){
                    Ext.MessageBox.alert("提示", "脚本内容不能为空!");
                    return ;
                }
                save(1);
            }
        })
    }
    });
    var saveBtn = Ext.create('Ext.Button', {
        text: '保存',
         handler: function(){
                save(-1); // 正常保存
         }
    });
    var preStepbtn = Ext.create('Ext.Button', {
        text: '上一步',
        cls: 'Common_Btn',
        handler: function() {
        tabsCenter.setActiveTab(0);
        }
    });
    //左侧Panel的上半部分，编写脚本的Panel
    var basicPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        autoScroll: true,
        title: "脚本信息",
        layout: 'border',
        items: [scriptForm],
        buttonAlign: 'center',
        buttons: [saveBtn,onLineBtn,preStepbtn,returnScriptManageBtn]
  });
    var tabsCenter = Ext.createWidget('tabpanel', {
    	// activeTab: 1, //指定默认的活动tab
    	region: 'center',
    	minHeight: 80,
        height: contentPanel.getHeight(),
        plain: true,                        // True表示tab候选栏上没有背景图片（默认为false）
        enableTabScroll: true,              // 选项卡过多时，允许滚动
        defaults: { autoScroll: true },
        border:true,
        layout: {
            type: 'border'
        },
        items: [ maimPanels,basicPanel]
    });
  
    //脚本编写左侧主panel
    var westPanel = Ext.create('Ext.panel.Panel', {
    	region: 'center',
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        cls:'customize_panel_back',
        height: contentPanel.getHeight(),
        items: [tabsCenter]
    });
    
    
    //脚本编写页面主Panel
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "editdbaasscript_area",
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        padding: 5,
        autoScroll: true,
    	border : true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',
        height: contentPanel.getHeight()-modelHeigth ,
        items: [westPanel, paramsAndFuncDescPanel]
    });
    
   var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
       	mode: 'sql',
       	theme: "lesser-dark", // 主题
        keyMap: "sublime", // 快键键风格
        extraKeys: {
        	"Ctrl-Q": "autocomplete",
        	"Ctrl-D":"deleteLine"
        },
        lineNumbers: true, // 显示行号
        smartIndent: true, // 智能缩进
        indentUnit: 4, // 智能缩进单位为4个空格长度
        indentWithTabs: true, // 使用制表符进行智能缩进
        lineWrapping: true, // 
        // 在行槽中添加行号显示器、折叠器、语法检测器
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"], 
        foldGutter: true, // 启用行槽中的代码折叠
        autofocus: true, // 自动聚焦
        matchBrackets: true, // 匹配结束符号，比如"]、}"
        autoCloseBrackets: true, // 自动闭合符号
        styleActiveLine: true // 显示选中行的样式
    });
   
    editor.setSize(mainP.getWidth()-2, mainP.getHeight()-60 );
    contentPanel.on('resize',function() {
        editor.getDoc().clearHistory();
        mainPanel.setHeight(contentPanel.getHeight()-modelHeigth );
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainP.getWidth()-2, mainP.getHeight()-60);
    });
    tabsCenter.on('resize', function() {
    	editor.getDoc().clearHistory();
    	editor.setSize(mainP.getWidth()-2, mainP.getHeight()-60 );
    });
    if(oldScriptId>0) {
    	Ext.Ajax.request({
            url: 'queryScriptInfo.do',
            method: 'POST',
            async: false,
            params: {
            	iid: oldScriptId
            },
            success: function(response, request) {
                var reader = Ext.decode(response.responseText);
                if(reader.ISCRIPTNAME) {
                	maimPanels.setTitle("脚本编辑_"+reader.ISCRIPTNAME);
                } else {
                	maimPanels.setTitle("编辑框");
                }
                oldScriptId=reader.IID;
                oldScriptUuid=reader.ISCRIPTUUID;
                scName.setValue(reader.ISCRIPTNAME);
                funcDesc.setValue(reader.IFUNCDESC);
                chooseSqlExecModel.setValue(reader.ISQLEXECMODEL);
                userType.setValue(reader.IUSERTYPE);
                dbType.setValue(reader.IDBTYPE);
                serviceType.setValue(reader.ISERVICETYPE);
                isPerm.setValue(reader.ISPERM);
                funcScriptDesc.setValue(reader.IINPUTDESC);
                catalogSelectId.setValue(reader.catalogSelectId);
                catalogName.setValue(reader.catalogName);
                lableSelectId.setValue(reader.lableSelectId);
                lableName.setValue(reader.lableName);
                var scriptT = reader.ISCRIPTTYPE;
                if (scriptT == 'sh') {
//                	scriptRadio.items.items[0].setValue(true);
                    editor.setOption("mode", 'shell');
                } else if (scriptT == 'bat') {
                    editor.setOption("mode", 'bat');
                } else if (scriptT == 'py') {
                    editor.setOption("mode", 'python');
                } else if (scriptT == 'sql') {
                    editor.setOption("mode", 'sql');
                } else if (scriptT == 'perl') {
                    editor.setOption("mode", 'text/x-perl');
                }
                editor.setOption('value', '');
                editor.setOption('value', reader.ICONTENT);
//                editor.getDoc().setValue(reader.ICONTENT);
                editor.refresh();
                paramStore.load({params:{scriptId:oldScriptUuid}});
            },
            failure: function(result, request) {
                secureFilterRs(result, "获取脚本信息失败！");
            }
        });
    }
    
    function returnScriptManage(){
        Ext.Msg.confirm("请确认", "是否要返回到我的脚本页面?",
        function(button, text) {
            if (button == "yes") {
                contentPanel.getLoader().load({
                    url: 'myDbaasScriptManage.do',
                    params: {
                        'filterScriptName': filter_scriptNameForDbaas,
                        'filterScriptType': filter_scriptTypeForDbaas,
                        'filterScriptStatus':filter_scriptStatusForDbaas
                    },
                    scripts: true
                });
                }
            })
     }

    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
    
    function orgParams() {
		paramStore.sort('paramOrder', 'ASC');
		var m = paramStore.getRange(0, paramStore.getCount() - 1);
		var aaaa = [];
		var someParamIsEmpty = false;
		var paramInvalidMessage = '';
		for (var i = 0, len = m.length; i < len; i++) {
			var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
			var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue"): '';

			if(!paramDefaultValue) {
				someParamIsEmpty = true;
			}
		    if ((paramType == 'OUT-int'||paramType == 'IN-int')&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                	paramInvalidMessage = '参数类型为int，但参数值不是int类型！';
                	break;
                }
            }
            if ((paramType == 'OUT-float'||paramType == 'IN-float')&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                	paramInvalidMessage = '参数类型为float，但参数值不是float类型！';
                	break;
                }
            }
			if (paramDefaultValue.indexOf('"') >= 0) {
					paramInvalidMessage = 'bat脚本暂时不支持具有双引号的参数值';
					break;
			}
			aaaa.push(paramDefaultValue);
		}
		
		return {
			someParamIsEmpty: someParamIsEmpty,
			paramInvalidMessage: paramInvalidMessage,
			scriptPara: aaaa.join("@@script@@service@@")
		};
	}
	
	function getParams () {
		paramStore.sort('paramOrder', 'ASC');
		var m = paramStore.getRange(0, paramStore.getCount() - 1);
		var aaaa = [];
		for (var i = 0, len = m.length; i < len; i++) {
			var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue"): '';
			var paramType=m[i].get("paramType") ? m[i].get("paramType"): '';
				aaaa.push(paramType+":"+paramDefaultValue);
		}
		return aaaa.join("@@script@@service@@");
	}
	
    function nextStep() {
        tabsCenter.setActiveTab(1);
    }
    function saveData() {
        var scName1 = scName.getValue();
        var userType1 = userType.getValue();
        var isPerm1 = isPerm.getValue();
        var dbType1 = dbType.getValue() || "";// 数据库类型
        var serviceType1 = serviceType.getValue() || "";// 服务类型
        var funcScriptDesc1 = funcScriptDesc.getValue() || "";// 发起审核
        var funcDesc1 = funcDesc.getValue();//启动用户
        if ("" == scName1) {
            setMessage('脚本名称不能为空！');
            tabsCenter.setActiveTab(1);
            return;
        }
        if ("" == userType1) {
            setMessage('执行账号不能为空！');
            tabsCenter.setActiveTab(1);
            return;
        }
        if ("" == isPerm1) {
            setMessage('双人复核不能为空！');
            tabsCenter.setActiveTab(1);
            return;
        }
        if ("" == dbType1) {
            setMessage('数据库类型不能为空！');
            tabsCenter.setActiveTab(1);
            return;
        }
        if ("" == serviceType1) {
            setMessage('脚本类型不能为空！');
            tabsCenter.setActiveTab(1);
            return;
        }
//        if ("" == funcScriptDesc1) {
//            setMessage('脚本输出说明不能为空！');
//            tabsCenter.setActiveTab(1);
//            return;
//        }
        if ("" == funcDesc1) {
            setMessage('脚本说明不能为空！');
            tabsCenter.setActiveTab(1);
            return;
        }
//        if ("" == catalogSelectId) {
//            setMessage('请选择编目！');
//            tabsCenter.setActiveTab(1);
//            return;
//        }
//        if ("" == lableSelectId) {
//            setMessage('请选择标签！');
//            tabsCenter.setActiveTab(1);
//            return;
//        }
       save(-1);
    }
    var onlineId=oldScriptId;
    var onlineUuId=oldScriptUuid;
    function save(flag) {
//        var scriptfile = scriptForm.getForm().findField("scriptfile").getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var scName1 = scName.getValue();
        var userType1 = userType.getValue();
        var isPerm1 = isPerm.getValue();
        var dbType1 = dbType.getValue() || "";// 数据库类型
        var serviceType1 = serviceType.getValue() || "";// 服务类型
        var funcScriptDesc1 = funcScriptDesc.getValue() || "";// 发起审核
        var funcDesc1 = funcDesc.getValue();//启动用户
        if ("" == scName1) {
            setMessage('脚本名称不能为空！');
            return;
        }
        if (fucCheckLength(scName1) > 250) {
            setMessage('脚本名称不能超过250字符！');
            return;
        }
        if ("" == userType1) {
            setMessage('执行账号不能为空！');
            return;
        }
        if ("" == isPerm1) {
            setMessage('双人复核不能为空！');
            return;
        }
        if ("" == dbType1) {
            setMessage('数据库类型不能为空！');
            return;
        }
        if ("" == serviceType1) {
            setMessage('脚本类型不能为空！');
            return;
        }
//        if ("" == funcScriptDesc1) {
//            setMessage('脚本输出说明不能为空！');
//            return;
//        }
//        if (fucCheckLength(funcScriptDesc1) > 4000) {
//            setMessage('脚本输出说明不能超过4000字符！');
//            return;
//        }
        if ("" == funcDesc1) {
            setMessage('脚本说明不能为空！');
            return;
        }
        if (fucCheckLength(funcDesc1) > 4000) {
            setMessage('脚本说明不能超过4000字符！');
            return;
        }
//        if ("" == catalogSelectId) {
//            setMessage('请选择编目！');
//            tabsCenter.setActiveTab(1);
//            return;
//        }
//        if ("" == lableSelectId) {
//            setMessage('请选择标签！');
//            tabsCenter.setActiveTab(1);
//            return;
//        }
        var m = paramStore.getRange();
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
            var iorder = m[i].get("paramOrder");
            if ("" == paramType) {
                setMessage('参数类型不能为空！');
                return;
            }
            if (fucCheckLength(paramDesc) > 250) {
                setMessage('参数描述不能超过250字符！');
                return;
            }

           if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    setMessage('参数类型为int，但参数默认值不是int类型！');
                    return;
                }
            }
            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    setMessage('参数类型为float，但参数默认值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"')>=0) {
                if(checkRadioForBasicScriptEdit == '1') {
                    Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
                }
            }
            for ( var k = 0; k < paramStore.getCount(); k++) {
                var record = paramStore.getAt(k);
                var order = record.data.paramOrder;
                if (order == iorder) {
                    n = n + 1;
                }
            }
            if (n > 1) {
                Ext.MessageBox.alert("提示", "参数顺序不能重复！");
                return;
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }

        jsonData = jsonData + "]";
        
        editor.save();
        var content = document.getElementById('code').value;
        if (content.trim() == '') {
            Ext.MessageBox.alert("提示", "脚本内容不能为空！");
                return;
            }
        var type = "sql";
        var saveUrl = "saveDbaasScriptCreate.do";
        if(oldScriptUuid!=0) {
            saveUrl = "saveDbaasScriptEdit.do"
        }
        console.log(3333,onlineId);
        if(flag==1) {
            saveUrl = "dbaasScriptOnline.do"
            Ext.Ajax.request({
                url : 'isDbaasScriptOnline.do',
                params : {
                    iid: onlineId
                },
                success : function(response, opts) {
                    var success = Ext.decode(response.responseText).success;
                    if(success) {
                        Ext.Msg.alert('提示', '状态为“已上线”脚本，不可重复上线，若有变更请先保存后在上线！');
//                        Ext.Msg.confirm("请确认", "该脚本已上线，不可重复上线?",
//                            function(button, text) {
//                                if (button == "yes") {
//                                    saveOnline(saveUrl,oldScriptId,oldScriptUuid,scName1,userType1,isPerm1,dbType1,serviceType1,funcScriptDesc1,content,jsonData,funcDesc1,flag,type);
//                                    }
//                                })
                    }else {
                                    saveOnline(saveUrl,onlineId,onlineUuId,scName1,userType1,isPerm1,dbType1,serviceType1,funcScriptDesc1,content,jsonData,funcDesc1,flag,type);
                    }
                },
                failure : function(response, opts) {
                    Ext.Msg.alert('提示', '检测脚本是否已上线异常！');
                    return;
                }
            });
        }else {
            saveOnline(saveUrl,oldScriptId,oldScriptUuid,scName1,userType1,isPerm1,dbType1,serviceType1,funcScriptDesc1,content,jsonData,funcDesc1,flag,type);
        }
    }
    
    function saveOnline(saveUrl,oldScriptId,oldScriptUuid,scName1,userType1,isPerm1,dbType1,serviceType1,funcScriptDesc1,content,jsonData,funcDesc1,flag,type) {
      Ext.Ajax.request({
          url: saveUrl,
          method: 'POST',
          sync: true,
          params: {
              iid: onlineId,
              uuid:onlineUuId,
              // 系统名称 系统分类
              scriptName: scName1,
              userType: userType1,
              isPerm: isPerm1,
              dbType: dbType1,
              serviceType: serviceType1,
              funcScriptDesc: funcScriptDesc1,
              content: content,
              params: jsonData,
              funcDesc : funcDesc1,
              sqlExecModel:chooseSqlExecModel.getValue(),
              scriptType:type,
              catalogSelectId:catalogSelectId.getValue(),
              catalogName:catalogName.getValue(),
              lableSelectId:lableSelectId.getValue(),
              lableName:lableName.getValue(),
              scriptstatus:flag
          },
          success: function(response, request) {
              var success = Ext.decode(response.responseText).success;
              var msg = Ext.decode(response.responseText).message;
              var scriptExits = Ext.decode(response.responseText).scriptExits;
              if(scriptExits) {
                  Ext.Msg.alert('提示', '脚本名称重复！');
                  return;
              }
              onlineId = Ext.decode(response.responseText).iid;
              oldScriptUuid = Ext.decode(response.responseText).uuid;
              oldScriptId = Ext.decode(response.responseText).iid;
              oldScriptUuid = Ext.decode(response.responseText).uuid;
              paramStore.load({
                  params: {
                	  scriptId: Ext.decode(response.responseText).uuid
                  }
              });
//              paramStore.load();
              Ext.Msg.alert('提示', msg);
              if(scName1) {
                  maimPanels.setTitle(scName1);
              } else {
                  maimPanels.setTitle("编辑框");
              }
              if(success && flag==1) {
                  contentPanel.getLoader().load({
                      url: 'myDbaasScriptManage.do',
                      params: {
                          'filterScriptName': filter_scriptNameForDbaas,
                          'filterScriptType': filter_scriptTypeForDbaas,
                          'filterScriptStatus':filter_scriptStatusForDbaas
                      },
                      scripts: true
                  });
              }
          },
          failure: function(result, request) {
              secureFilterRs(result, "操作失败！");
          }
      });
  }
    
    
    function lableSelect() {
        var data = lableGrid.getView().getSelectionModel().getSelection();        
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {      
		    lableSelectId.setValue(lableIds.join(','));
		    lableName.setValue(lableNames.join(','));
        	lableWindow.close();
        }            	        	        	
    }

    
    
});
