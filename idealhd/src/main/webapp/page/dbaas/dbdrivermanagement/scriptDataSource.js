var pageBar;
Ext.onReady(function() {
	var globalSelectRecord;
	Ext.tip.QuickTipManager.init();
    var dataSourceStore;
    var dataSourceGrid;
    // 清理主面板的各种监听时间
   //destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IDSNAME',
            type: 'string'
        },
        {
            name: 'IDSIP',
            type: 'string'
        },
        {
            name: 'IDSDRIVER',
            type: 'string'
        },
        {
        	name: 'IDSDRIVER_ORI',
        	type: 'string'
        },
        {
            name: 'IDSPORT',
            type: 'string'
        },
        {
            name: 'IDSUSER',
            type: 'string'
        },
        {
            name: 'IDSPWD',
            type: 'string'
        },
        {
            name: 'rIDSPWD',
            type: 'string'
        },
        {
            name: 'IDSROLE',
            type: 'string'
        },
        {
            name: 'IDSINSTANCE',
            type: 'string'
        }
        ,
        {
            name: 'IDBURL',
            type: 'string'
        },
        {
        	name: 'IDBURL_ORI',
        	type: 'string'
        },
        {
            name: 'IDBTYPE',
            type: 'string'
        },
        {
        	name: 'IDBTYPE_ORI',
        	type: 'string'
        }
        ]
    });
    
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 16,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'dataSourceList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var dataBaseTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "oracel"
        },
        {
            "name": "sysdba"
        },
        {
            "name": "system"
        },
        {
            "name": "nomal"
        }
        ]
    });
    
    var dataBaseTypeStore2 = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "oracle"
        },
        {
    		"name": "db2"
    	},
    	{
    		"name": "mysql"
    	}
        ]
    });
    var dataStore = Ext.create('Ext.data.Store', {
    	fields: ['name'],
    	data: [{
    		"name": "com.ibm.db2.jcc.DB2Driver"
    	}, {
            "name": "oracle.jdbc.driver.OracleDriver"
        },
        {
            "name": "com.mysql.jdbc.Driver"
        }
    	]
    });
    var dataStore2 = Ext.create('Ext.data.Store', {
    	fields: ['name'],
    	data: [{
    		"name": "**********************"
    	}, {
    		"name": "*****************************"
    	},
    	{
    		"name": "************************"
    	}
    	]
    });
    
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
            baseName: nameField.getValue()
        };

        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
   var olddbtypefordriver=""; 
   var olddbtypeforUrl=""; 
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '数据源名称',
        dataIndex: 'IDSNAME',
        width: 180,
        flex: 1,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: 'IP地址',
        dataIndex: 'IDSIP',
        width: 100,
//        flex: 1,
//        editor: {
//            allowBlank: false
//        }
        renderer:getAgentIP
    },
    {
        text: '端口',
        dataIndex: 'IDSPORT',
        width: 80,
        hidden: true,
//        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
    	text: 'DB类型',
        dataIndex: 'IDBTYPE',
        width: 70,
//        editor: {
//            allowBlank: false
//        }
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataBaseTypeStore2,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            listeners : {
            	'change' : function(combo,nv,ov) {
//            		var sel = dataSourceGrid.getSelectionModel().getSelection();
            		console.log(globalSelectRecord);
            		if(nv=='db2'){
            			globalSelectRecord.data.IDSDRIVER = "com.ibm.db2.jcc.DB2Driver";
            			globalSelectRecord.data.IDBURL = '**********************';
                   	}else if (nv=='oracle'){
                   		globalSelectRecord.data.IDSDRIVER = "oracle.jdbc.driver.OracleDriver";
                   		globalSelectRecord.data.IDBURL = '*****************************';
                   	}else if (nv=='mysql'){
                   		globalSelectRecord.data.IDSDRIVER = "com.mysql.jdbc.Driver";
                   		globalSelectRecord.data.IDBURL = '************************';
                   	}
            		if(nv==globalSelectRecord.data.IDBTYPE_ORI) {
            			globalSelectRecord.data.IDSDRIVER = globalSelectRecord.data.IDSDRIVER_ORI;
            			globalSelectRecord.data.IDBURL = globalSelectRecord.data.IDBURL_ORI;
            		}
            		dataSourceGrid.getView().refresh();
            	}
            }
        })
    },{
    	text: '驱动类',
        dataIndex: 'IDSDRIVER',
        width: 200,
        editor: {
            allowBlank: true
        }
    },
    {
    	text: 'DBURL',
        dataIndex: 'IDBURL',
        width: 300,
        editor: {
            allowBlank: true
        }
    },
    {
        text: '用户名',
        dataIndex: 'IDSUSER',
        width: 100,
//        flex: 1,
        editor: {
            allowBlank: false
        }
    },
    {
        header: '密码',
        dataIndex: 'IDSPWD',
        width: 100,
        editor: new Ext.form.TextField({
            inputType: 'password',
            allowBlank: true,
            allowNegative: true
        }),
        renderer: retNotView
    },
    {
        header: '确认密码',
        dataIndex: 'rIDSPWD',
        width: 100,
        editor: new Ext.form.TextField({
            inputType: 'password',
            //设置输入类型为password
            allowBlank: true,
            //			 			minLength :8,
            //			 			maxLength :20,
            allowNegative: true
        }),
        renderer: retNotView
    },
    {
        text: '用户角色',
        dataIndex: 'IDSROLE',
        width: 80,hidden: true,
//        flex: 1,
        editor: new Ext.form.field.ComboBox({
            allowBlank: true,
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            // 是否可输入编辑
            store: dataBaseTypeStore,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name'
        })
    },
    {
        text: '数据库实例',
        dataIndex: 'IDSINSTANCE',
        width: 100, hidden: true,
//        flex: 1,
        editor: {
            allowBlank: false
        }
    }/*,
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 150,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state'); // 其实是requestID
            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showbsTypeWindow(' + iid + ','+ state + ')">' + '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;配置类型' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
        }
    }*/];
    
    
    function retNotView(value) {
        var coun = "";
        if (value.trim().length > 0) {
            for (var i = 0; i < value.length; i++) {
                coun = coun + "*";
            }
        }
        if (value.trim() == "") {
            coun = "";
        }
        return coun;
    }
    
    // 分页工具
    pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '数据源名称',
        labelWidth: 100,
        labelAlign: 'right',
        name: 'dataBaseNameParam',
        width: '30%'
    });

    dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        id: 'dataSourceGrid',
        store: dataSourceStore,
        padding : grid_space,
        selModel: selModel,
        plugins: [cellEditing],
        bbar: pageBar,
        border: false,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        dockedItems: [{
            xtype: 'toolbar',
            items: [nameField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                	QueryMessage();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                text: '增加',
                cls: 'Common_Btn',
                //iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveDatabase
            },
            {
                text: '导入',
                cls: 'Common_Btn',
                handler: saveDatabase
            },
            {
                text: '导出',
                cls: 'Common_Btn',
                handler: saveDatabase
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                //iconCls:'sc_delete',
                disabled: true,
                handler: deleteDataBase
            }]
        }],
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	}
        }
    });

    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
		/*dataSourceStore.reload({
			params : {
				start : 0,
				limit : 16,
				baseName : nameField.getValue()
			}
		});*/
	}
    
    dataSourceGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        dataSourceGrid.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "scriptService_grid_areaDataSource",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function clearQueryWhere() {
    	nameField.setValue('');
    }
    function add() {
        var store = dataSourceGrid.getStore();
        var p = {
        		IDSIP: '',
        		IDSNAME: '',
        		IDSDRIVER: '',
        		IDSPORT: '50000',
        		IDSUSER: '',
        		IDSPWD: '',
        		rIDSPWD:'',
        		IDSROLE: 'sysdba',
        		IDSINSTANCE:'entegor'
        };
        store.insert(0, p);
        dataSourceGrid.getView().refresh();
    }
    function saveDatabase() {
        var m = dataSourceStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var IDSNAME = m[i].get("IDSNAME").trim();
            if ("" == IDSNAME || null == IDSNAME) {
                setMessage('数据源名不能为空！');
                return;
            }
            
//            var IDSIP = m[i].get("IDSIP").trim();
//            if ("" == IDSIP || null == IDSIP) {
//                setMessage('IP不能为空！');
//                return;
//            }
//            if(!checkIp(IDSIP)){
//            	setMessage('IP格式不正确！');
//                return;
//            }
//            
            var IDSPORT = m[i].get("IDSPORT").trim();
            if ("" == IDSPORT || null == IDSPORT) {
                setMessage('端口不能为空！');
                return;
            }
            
            if(!isNumber(IDSPORT)){
            	setMessage('端口号格式不正确！');
                return;
            }
            var IDBTYPE = m[i].get("IDBTYPE").trim();
            if ("" == IDBTYPE || null == IDBTYPE) {
                setMessage('DB类型不能为空！');
                return;
            }
            var IDBURL = m[i].get("IDBURL").trim();
            if ("" == IDBURL || null == IDBURL) {
                setMessage('DBURL不能为空！');
                return;
            }
            
            var IDSDRIVER = m[i].get("IDSDRIVER").trim();
            if ("" == IDSDRIVER || null == IDSDRIVER) {
                setMessage('驱动不能为空！');
                return;
            }
            
            var IDSUSER = m[i].get("IDSUSER").trim();
            if ("" == IDSUSER || null == IDSUSER) {
                setMessage('用户名不能为空！');
                return;
            }
            
            var IDSPWD = m[i].get("IDSPWD").trim();
            if ("" == IDSPWD || null == IDSPWD) {
                setMessage('密码不能为空！');
                return;
            }
            
            var rIDSPWD = m[i].get("rIDSPWD").trim();
            if (IDSPWD != rIDSPWD) {
                setMessage('两次密码输入不一致！');
                return;
            }
            
            var IDSROLE = m[i].get("IDSROLE").trim();
            if ("" == IDSROLE || null == IDSROLE) {
                setMessage('用户角色不能为空！');
                return;
            }
            var IDSINSTANCE = m[i].get("IDSINSTANCE").trim();
            if ("" == IDSINSTANCE || null == IDSINSTANCE) {
                setMessage('数据库实例不能为空！');
                return;
            }
            
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveDatasource.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    dataSourceStore.modified = [];
                    dataSourceStore.reload();
                    Ext.Msg.alert('提示', message);
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function isNumber(val){
    	if (val == null) return true;
        var regObj = /^\d*$/g;
        if (regObj.test(val)) {
            return true;
        } else {
            return false;
        }
    }
    
    function checkIp(val){
    	var regObj = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/   
    	 if (regObj.test(val)) {
             return true;
         } else {
             return false;
         }
    }
    
    function deleteDataBase() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                             }
                    });
                    
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteDataSource.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                            	dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }

});

function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}


//AgentIP显示
function getAgentIP(value, p, record) 
{
	var displayValue = value;
	if(null==displayValue || ""==displayValue)
	{
		displayValue= "AgentIP";
	}
	return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"cpipSelectShow('"
	+record.get("IID")+"','"+record.get("IDSIP")+"');\">"
				+"<span class='abc'>"
				+displayValue
				+"</span>"
			+"</a>";
} 
//function cpipSelectShow(dsid,agentIp)
//{
//	alert('select cpip ... dsid='+dsid);return;
//}