var dataSourceStore;
Ext.onReady(function() {
    var choice=-1;
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   // destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'IDBUSER',
            type: 'string'
        },
        {
            name: 'IDBP<PERSON>',
            type: 'string'
        },
        {
            name: 'ICOPYNUM',
            type: 'string'
        },
        {
            name: 'IAPPLYUSER',
            type: 'string'
        },
        {
            name: 'ISID',
            type: 'string'
        },
        {
            name: 'ITYPE',
            type: 'long'
        },
        {
            name: 'IENV',
            type: 'long'
        },
        {
            name: 'IDBID',
            type: 'string'
        },
        {
            name: 'IDBVERSION',
            type: 'string'
        },       
        {
            name: 'ISTATUS',
            type: 'string'
        },
        {
            name: 'IPOSITION',
            type: 'int'
        },
        {
        	name: 'IALTERLOGNAME',
        	type: 'string'
        },
        {
            name: 'ICPU',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY',
            type: 'string'
        }
        ,
        {
            name: 'IDISK',
            type: 'string'
        } ,
        {
            name: 'IMODEL',
            type: 'string'
        },{
            name: 'IBUSINESSTYPE',
            type: 'string'
        },{
            name: 'continueCount',
            type: 'long'
        },{
            name: 'cumulateCount',
            type: 'long'
        },{
            name: 'lastcheck',
            type: 'string'
        },{
            name: 'rac',
            type: 'string'
        },{
        	name: 'istartuptime',
        	type: 'string'
        },{
        	name: 'iqdbuser',
        	type: 'string'
        },{
        	name: 'iqdbpwd',
        	type: 'string'
        },{
        	name: 'IRACGROUP',
        	type: 'string'
        },{
        	name: 'instancename',
        	type: 'string'
        }
        
        ]
    });
    
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceManageList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        groupField:'IRACGROUP' 
    });
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
        	baseBusiness: businessField.getValue(),
        	baseIp: ipField.getValue(),
			baseSid: sidField.getValue(),
			state:ss_state,
			dataType:dataTypeField.getValue(),
			racGroup:racGroupField.getValue()
        };

        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });

    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        }        ]
    });
    
    var sysDataStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
    var sysDataByUserIdStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageByUserId.do?projectFlag=1',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
    var dataTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getDatabaseType.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65, 
// hidden: true,
// locked : true,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },{
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        minWidth: 200,
        flex:1,
        locked : true,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'sysName',
            valueField: 'sysName',
			forceSelection : true, // 要求输入值必须在列表中存在
            store: sysDataByUserIdStore
        }
        ),
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        locked : true,
        width: 90,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: dataTypeModel
        }),renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: 'IP',
        dataIndex: 'IIP',
        locked : true,
        width: 120,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        header: '实例名',
        dataIndex: 'instancename',
        minWidth: 120,
        flex:1,
        locked : true,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '端口号',
        dataIndex: 'IDBPORT',
        width: 80,
        editor: {
            xtype: 'numberfield',
            maxValue: 65535,
            minValue: 1
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        minWidth: 120,
        flex:1,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库版本',
        dataIndex: 'IDBVERSION',
        minWidth: 100,
        flex:1,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: 'Rac',
        dataIndex: 'rac',
        width: 60,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '副本号',
        dataIndex: 'ICOPYNUM',
        width: 80,
        hidden: true,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        width: 140,
        hidden:true,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: qdbuserSwitch?'用户':'查询用户',
        dataIndex: 'IDBUSER',
        width: 100,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '查询密码',
        dataIndex: 'IDBPWD',
        width: 80,
        hidden:true,
        editor: new Ext.form.TextField({ 						
			inputType:'password', // 设置输入类型为password
			allowBlank: false,
			allowNegative: true
		 }),
		renderer:retNotView
    },
    {
        text: '操作用户',
        dataIndex: 'iqdbuser',
        width: 100,
        hidden:qdbuserSwitch,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '操作密码',
        dataIndex: 'iqdbpwd',
        width: 80,
        hidden:true,
        editor: new Ext.form.TextField({ 						
			inputType:'password', // 设置输入类型为password
			allowBlank: false,
			allowNegative: true
		 }),
		renderer:retNotView
    },
    {
        text: '<span style="color:#dddddd;">DBID</span>',
        dataIndex: 'IDBID', 
        width: 70,
        renderer : function(value, metadata) {
        	metadata.css='x-grid-back-red';
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '日志文件',
        dataIndex: 'IALTERLOGNAME',
        hidden:logSwitch,
        width: 140,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
    		metadata.tdAttr = 'data-qtip="' + value + '"';
    		return value;
    	}
	},
    {
    	header: '分组名称',
        dataIndex: 'IRACGROUP',
        width: 100,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
    		metadata.tdAttr = 'data-qtip="' + value + '"';
    		return value;
    	}
	},
	   {
    	text: 'CPU',
        dataIndex: 'ICPU',
        width: 60,
        hidden:true,
        editor:false
    },{
    	text: '内存 剩余',
        dataIndex: 'IMEMORY',
        width: 100,
        hidden:true,
        editor:false
    },
    {
        text: '磁盘空间',
        dataIndex: 'IDISK',
        width: 120,
        hidden:true,
        editor:false
    },
    {
        header: '环境',
        dataIndex: 'IENV',
        width: 65,
        hidden: true,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "测试",
                    value: "0"
                },
                {
            		name: "生产",
            		value: "1"
            	},
                {
            		name: "研发",
            		value: "2"
            	}]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('IENV');
            if(IFLAG=='0'){
            	return "测试";
            }else if(IFLAG=='1'){
            	return "生产";
            }else if(IFLAG=='2'){
            	return "研发";
            }
        }
    },
    {
        header: '<span style="color:#dddddd;">申请人</span>',
        dataIndex: 'IAPPLYUSER',
        width: 90,
        hidden: true,
        renderer : function(value, metadata) {
        	metadata.css='x-grid-back-red';
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    }, {
        header: '资源模式',
        dataIndex: 'IMODEL',
        width: 65,
        hidden: true,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            hidden:true,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "RAC",
                    value: "1"
                },{
                    name: "实例",
                    value: "2"
                },
                {
            		name: "DATAGUARD",
            		value: "3"
            	},
                {
            		name: "用户",
            		value: "4"
            	}]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('IMODEL');
            if(IFLAG=='1'){
            	return "RAC";
            }else if(IFLAG=='2'){
            	return "实例";
            }else if(IFLAG=='3'){
            	return "DATAGUARD";
            }else if(IFLAG=='4'){
            	return "用户";
            }
        }
    },
    {
        header: '<span style="color:#dddddd;">所属类型</span>',
        dataIndex: 'ITYPE',
        width: 90,
        hidden: true,
        renderer: function(value, metadata) {
        	metadata.css='x-grid-back-red';
            if(value=='1'){
            	return "人工";
            }else{
            	return "自动";
            }
        }
    },{
        text: '设备启动时间',
        dataIndex: 'istartuptime',
        hidden: true,
        width: 90,
        editor:false
    },{
        text: '系统分类',
        dataIndex: 'IBUSINESSTYPE',
        hidden: true,
        width: 90,
        editor:false
    },{
        text: '连续错误',
        dataIndex: 'continueCount',
        width: 70,
        editor:false
    },{
        text: '累计错误',
        dataIndex: 'cumulateCount',
        width: 70,
        editor:false
    },{
        header: '状态',
        dataIndex: 'ISTATUS',
        width: 80,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "有效",
                    value: "0"
                },
                {
            		name: "查询用户异常",
            		value: "1"
            	},{
            		name: "操作用户异常",
            		value: "2"
            	},{
            		name: "全部异常",
            		value: "3"
            	}]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('ISTATUS');
            if(IFLAG=='0'){
            	return "<span class='Complete_Green State_Color'>有效</span>";
            }else if(IFLAG=='1'){
            	return "<span class='Abnormal_yellow State_Color'>查询用户失效</span>";
            }else if(IFLAG=='2'){
            	return "<span class='Abnormal_yellow State_Color'>操作用户失效</span>";
            }else if(IFLAG=='3'){
            	return "<span class='Abnormal_yellow State_Color'>全部失效</span>";
            }
        }
    },{
        text: '最近检测时间',
        dataIndex: 'lastcheck',
        width: 160,
// flex:1,
        editor:false
    },
    {
        text: '操作',
        flex:1,
        dataIndex: 'stepOperation',
        width: 80,
        minWidth:100,
        renderer: function(value, p, record, rowIndex) {
        	var iid = record.get('IID');
            return '<div>' + '<a href="javascript:void(0)" onclick="testvalid('+iid+')">' + '&nbsp;测试连通性' + '</a>' + '</div>';
// return '<div>' + '<a href="javascript:void(0)"
// onclick="showDetail('+iid+')">' + '&nbsp;查看详情' + '</a>' + '</div>';
        }
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

// var businessField = Ext.create("Ext.form.field.Text", {
// fieldLabel: '业务系统',
// labelWidth: 65,
// labelAlign: 'left',
// name: 'dataBaseNameParam',
// width: '18%'
// });
    var businessField =  Ext.create("Ext.form.field.ComboBox",{
 // fieldLabel: '业务系统',
        labelWidth: 65,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '13%',
    	triggerAction: 'all',
    	editable : true,
        queryMode: 'local',
        emptyText: "--请选择业务系统--",
        displayField: 'sysName',
        valueField: 'sysName',
        store: sysDataByUserIdStore,
        listeners : {
            beforequery : function(e) {  
                var combo = e.combo;     
                if(!e.forceAll){     
                    var value = e.query;     
                    combo.store.filterBy(function(record, id){     
                        var text = record.get(combo.displayField);     
                        return (text.toUpperCase().indexOf(value.toUpperCase())!=-1);     
                    });  
                    combo.expand();     
                    return false;     
                }  
            }  
        }
    });
    
    var ipField = Ext.create("Ext.form.field.Text", {
// fieldLabel: 'IP',
        labelWidth: 25,
        labelAlign: 'left',
        emptyText: "--请输入IP--",
        name: 'dataBaseNameParam',
        width: '10%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var sidField = Ext.create("Ext.form.field.Text", {
 // fieldLabel: '服务名',
        labelWidth: 50,
        labelAlign: 'left',
        emptyText: "--请输入服务名--",
        name: 'dataBaseNameParam',
        width: '13%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    
    var dataTypeField =  Ext.create("Ext.form.field.ComboBox",{
// fieldLabel: '业务系统',
        labelWidth: 65,
        labelAlign: 'left',
        name: 'dataTypeParam',
        width: '13%',
    	triggerAction: 'all',
    	editable : true,
        queryMode: 'local',
        emptyText: "--请选择数据源类型--",
        displayField: 'name',
        valueField: 'name',   
        store: dataTypeModel,      
// listeners : {
// beforequery : function(e) {
// var combo = e.combo;
// if(!e.forceAll){
// var value = e.query;
// combo.store.filterBy(function(record, id){
// var text = record.get(combo.displayField);
// return (text.indexOf(value)!=-1);
// });
// combo.expand();
// return false;
// }
// }
// }
    });
    
    var racGroupField = Ext.create("Ext.form.field.Text", {
// fieldLabel: 'IP',
      labelWidth: 25,
      labelAlign: 'left',
      emptyText: "--请输入分组名称--",
      name: 'racGroupParam',
      width: '11%',
      listeners: {
          specialkey: function(field, e){
              if (e.getKey() == e.ENTER) {
              	pageBar.moveFirst();
              }
          }
      }
  });
    
	/** 导入导出按钮* */
	var importOrExportButton = Ext.create("Ext.Button",{
            text: '导入/导出',
            cls:'Common_Btn',
            menu: {
                xtype: 'menu',
                plain: true,
                items: {
                    xtype: 'buttongroup',
                    columns: 2,
                    defaults: {
                        xtype: 'button'
                    },
                    items: [ {
						text : '导入',
						cls:'Common_Btn',
						handler : uploadExcel
					},
					{
						text : '导出',
						cls:'Common_Btn',
						handler: function() {
			                	var record = dataSourceGrid.getSelectionModel().getSelection();
			                	var ids=[];
			            		if(record.length!=0){
			            			Ext.each(record,function(item){
			            				var iid = item.get('IID');
			            				if(iid != 0){
			            					ids.push(iid);
			            					ids.join(',')
			            				}
			            			});
			            		}
			            		window.location.href = 'exportResourceManage.do?ids='+ids;
			            	}
					}]
                }
            }
	});
    
    var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			baseCls:'customize_gray_back',
			border : false,
			items : [businessField,
	            ipField,
	            sidField,
	            dataTypeField,
	            racGroupField,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            },
	            {
	                xtype: 'button',
					cls: 'Common_Btn',
					hidden:ss_state==''?true:false,
	                text: '返回',
	                handler: function() {
						contentPanel.setTitle('');
						contentPanel.getLoader().load({
								url : 'pandect2.do',
								sync : true,
								params : {
								},
								scripts : true
							});
					}
	            },'->',
	            {
	                text: '增加',
	                cls: 'Common_Btn',
	             // handler: add
	                handler:function(){
	                	saveDatabase(0)// 参数为0时表示添加
	                },
	                listeners :{
	    		        'change':function(){
	    		        	oldServiceType=null;
	    		        }
	    		    }
	            },
	            {
	              // text: '保存',
	            	text:'编辑',
	                cls: 'Common_Btn',
	             // handler: saveDatabase
	                handler:function(){
	                	saveDatabase(1)// 参数为1时表示修改
	                }
	            },importOrExportButton, {
	                itemId: 'delete',
	                text: '删除',
	                cls: 'Common_Btn',
	                disabled: true,
	                handler: deleteDataManage
	            },{
	                itemId: 'data',
	                text: '数据详情',
	                hidden:true,
	                cls: 'Common_Btn',
	                handler: getData
	            }]
			}]
	});
    
    var dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
        selModel: selModel,
      // plugins: [cellEditing],
        padding : panel_margin,
		border: false,
		cls:'customize_panel_back',
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        features : [
                    Ext.create ('Ext.grid.feature.Grouping',
                    {
                        groupByText: '用本字段分组',
                        showGroupsText : '显示分组',
// groupHeaderTpl : '状态:{name} ({rows.length})', // 分组显示的模板
                        groupHeaderTpl: [
// '状态: {name:this.formatName} ({rows.length})',
                                         '<div>分组信息--- 名称:{name:this.formatName}-(数量{rows.length})</div>',
                                         {
                                             formatName: function(name) {
                                                 var value ='';
                                                 if(name =='') {
                                                     value='未分组'; 
                                                 }else {
                                                     value=name;
                                                 }
                                                    
                                                 return Ext.String.trim(value);
                                             }
                                         }
                                     ],
                        startCollapsed : false
                    // 设置初始分组是不是收起
                    })
                ],
        listeners: {
// 'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts)
// {
// alert(123);
// showEditForm(record);
// },
        	itemdblclick : function(dbclickthis, record, item,index, e, eOpts) {
//        		choice=1;
//            	var position=record.data.IPOSITION;
//            	if(position==0){
//        			Ext.Msg.alert('提示', '该资源您没有修改权限！');
//            		return;
//        		}else{
//            		showEditForm(record);
//            	}
            	getDataRecord(record);
			}
        }
    });
    function retNotView(value){
    	var coun ="";
    	if (value && value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value && value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
  // 导入
    function uploadExcel(){
    	var uploadWindows;
    	var uploadForm;
		Ext.Ajax.request({
		    url : 'chkIsManager.do',
		    method : 'POST',
		    success: function(response, opts) {    		    	
		        var isManager = Ext.decode(response.responseText);
		        if(!isManager) {
		    		Ext.Msg.alert('提示', "您不是管理员或管理者身份，不允许导入");
		    		return;
		    	} else {
		    		 uploadForm = Ext.create('Ext.form.FormPanel',{
		    	        	border : false,
		    	        	items : [{
		    	            	xtype: 'filefield',
		    	    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
		    	    			fieldLabel: '选择文件',
		    	    			labelWidth: 80,
		    	    			anchor: '90%',
		    	    			margin: '10 10 0 40',
		    	    			buttonText: '浏览'
		    	            }],
		    	            buttonAlign : 'center',
		    	            buttons :[{
		    	            	text : '确定',
		    	            	handler :upExeclData
		    	            },{
		    	            	text : '取消',
		    	            	handler : function(){
		    	            		
		    	            		uploadWindows.close();
		    	            	}
		    	            }]
		    	        });
		    	        /**
						 * Excel导入Agent信息窗体
						 */
		    	        uploadWindows = Ext.create('Ext.window.Window', {
		    	    		title : 'Excel导入',
		    	    		layout : 'fit',
		    	    		height : 150,
		    	    		width : 600,
		    	    		modal : true,
		    	    		items : [ uploadForm ],
		    	    		listeners : {
		    	    			close : function(g, opt) {
		    	    				uploadForm.destroy();
		    	    			}
		    	    		}
		    	    	});
		    	        uploadWindows.show();
		    	        function upExeclData(){   
	    		    		var form = uploadForm.getForm();
	    		    		var hdupfile=form.findField("fileName").getValue();
	    		    		if(hdupfile==''){
	    		    			Ext.Msg.alert('提示',"请选择文件...");
	    		    			return ;
	    		    		}
	    		    		uploadTemplate(form);
   		 
		    	        }
		    	        function uploadTemplate(form) {
		    	      	   if (form.isValid()) {
// Ext.Msg.wait('处理中，请稍后...', '提示');
		    	             form.submit({
		    	               url: 'importResourceManage.do',
		    	                 success: function(form, action) {
		    	                    var success = Ext.decode(action.response.responseText).success;
		    	                    var sumsg = Ext.decode(action.response.responseText).message;
		    	                    var operId = Ext.decode(action.response.responseText).operId;
		    	                    if(!success){
		    	                    	 Ext.Msg.alert('提示',sumsg);
		    	                    }else{
		    	                    	Ext.MessageBox.show({
		    	                    		title: '数据导入',
		    	                    		msg: '数据正在导入验证中，请稍等...',
		    	                    		progressText: '',
		    	                    		width: 300,
		    	                    		progress: true,
		    	                    		closable: false,
		    	                    		animateTarget: 'mb'
		    	                    	});
		    	                    	flow(operId);
// Ext.Msg.alert('提示',sumsg);
		    	                    	uploadWindows.close();
//		    	                    	dataSourceStore.reload();
//		    	                    	sysDataByUserIdStore.reload();
		    	                    }
		    	                    return;
		    	                 },
		    	                 failure: function(form, action) {
		    	                	 var success = Ext.decode(action.response.responseText).success;
		    	                     var msg = Ext.decode(action.response.responseText).message;
		    	                     var operId = Ext.decode(action.response.responseText).operId;
		    	                      Ext.Msg.alert('提示',msg);
		    	                   return;
		    	                 }
		    	             });
		    	      	   }
		    	      	 }
		    	        var t1;
		    	        function flow(operId) {
		    	            Ext.Ajax.request({
		    	                url: 'importResourceManageResult.do',
		    	                params: {
		    	                	id: operId
		    	                },
		    	                success: function(response, request) {
		    	                	var fin = Ext.decode(response.responseText).fin;
		    	                	var total = Ext.decode(response.responseText).total;
		    	                	var mess = Ext.decode(response.responseText).message;
		    	                    if (fin == total ) {
		    	                        Ext.MessageBox.hide();
		    	                        if(mess!=''){
		    	                        	Ext.Msg.alert('提示',"以下系统在应用系统中不存在：<br/>"+mess);
		    	                        }
		    	                    	dataSourceStore.reload();
		    	                    	sysDataByUserIdStore.reload();
		    	                        if(t1){window.clearTimeout(t1);}//去掉定时器  
		    	                    }else {
		    	                        Ext.MessageBox.updateProgress(fin/total, '总进度 '+fin+"/"+total);
		    	                        t1 = setTimeout(flow(operId), 2*1000);
		    	                    }
		    	                }
		    	            });
		    	        }
		    	}
		    }
		})
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    
    dataSourceGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resource_manage",
        layout: 'border',
        bodyCls:'service_platform_bodybg customize_stbtn',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function checkIP(ip) {
    	var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
    	if (reg.test(ip)) {
    		return true;
    	} else {
    		return false;
    	}
    }   
    function clearQueryWhere() {
    	businessField.setValue('');
    	ipField.setValue('');
    	sidField.setValue('');
    	dataTypeField.setValue('');
    	racGroupField.setValue('');
    }
/*
 * function add() { var store = dataSourceGrid.getStore(); var p = { IBUSINESS:
 * '', IIP: '', IDBPORT: '', INAME: '', IRSTYPE: '', IDBUSER: '', IDBPWD: '',
 * IFLAG:'1', IENV:'0', ISID:'', ITYPE:1 }; store.insert(0, p);
 * dataSourceGrid.getView().refresh(); } function saveDatabase() { var m =
 * dataSourceStore.getModifiedRecords(); if (m.length < 1) {
 * setMessage('无需要增加或者修改的数据！'); return; }
 * 
 * var jsonData = "["; for (var i = 0,len = m.length; i < len; i++) { var
 * IBUSINESS = m[i].get("IBUSINESS").trim(); if ("" == IBUSINESS || null ==
 * IBUSINESS) { setMessage('业务系统不能为空！'); return; } var IIP =
 * m[i].get("IIP").trim(); if ("" == IIP || null == IIP) {
 * setMessage('服务器IP不能为空！'); return; } if (!checkIP(IIP)) {
 * setMessage('服务器IP:'+IIP+'格式不正确!'); return; } var IDBUSER =
 * m[i].get("IDBUSER").trim(); if ("" == IDBPORT || null == IDBUSER) {
 * setMessage('数据库用户不能为空！'); return; } if (fucCheckLength(IDBUSER) > 255) {
 * setMessage('数据库用户不能超过255字符！'); return; } var IDBPWD =
 * m[i].get("IDBPWD").trim(); if ("" == IDBPWD || null == IDBPWD) {
 * setMessage('数据库密码不能为空！'); return; } if (fucCheckLength(IDBPWD) > 25) {
 * setMessage('数据库密码不能超过25字符！'); return; } var IRSTYPE = m[i].get("IRSTYPE"); if
 * ("" == IRSTYPE || null == IRSTYPE) { setMessage('数据源类型不能为空！'); return; } var
 * ICOPYNUM = m[i].get("ICOPYNUM"); if ("" == ICOPYNUM || null == ICOPYNUM) {
 * setMessage('副本号不能为空！'); return; } if (fucCheckLength(IRSTYPE) > 25) {
 * setMessage('数据源类型不能超过25字符！'); return; } var IDBPORT = m[i].get("IDBPORT"); if
 * ("" == IDBPORT || null == IDBPORT) { setMessage('数据库端口不能为空！'); return; }else
 * if(!isNumber(IDBPORT)){ setMessage('请正确填写数据库端口！'); return; } var ISID =
 * m[i].get("ISID").trim(); if ("" == ISID || null == ISID) {
 * setMessage('SID不能为空！'); return; } if (fucCheckLength(ISID) > 25) {
 * setMessage('SID不能超过25字符！'); return; } // var ITYPE =
 * m[i].get("ITYPE").trim(); // if ("" == ITYPE || null == ITYPE) { //
 * setMessage('所属类型不能为空！'); // return; // } var ss = Ext.JSON.encode(m[i].data);
 * if (i == 0) jsonData = jsonData + ss; else jsonData = jsonData + "," + ss; }
 * jsonData = jsonData + "]"; Ext.Ajax.request({ url: 'saveResourceManage.do',
 * method: 'POST', params: { jsonData: jsonData }, success: function(response,
 * request) { var success = Ext.decode(response.responseText).success; var
 * message = Ext.decode(response.responseText).message; if (success) {
 * dataSourceStore.modified = []; dataSourceStore.reload(); Ext.Msg.alert('提示',
 * message); } else { Ext.Msg.alert('提示', message); } }, failure:
 * function(result, request) { secureFilterRs(result, "操作失败！"); } }); }
 */
    function isNumber(val){
    	if (val == null) return true;
        var regObj = /^\d*$/g;
        if (regObj.test(val)) {
            return true;
        } else {
            return false;
        } 
    }
    
    function deleteDataManage() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
        	
        	for(var i=0;i<data.length;i++){
        		var position=data[i].data.IPOSITION;
        		if(position==0){
        			Ext.Msg.alert('提示', '您没有删除权限!');
            		return;
        		}
        	}
        	
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteResourceManage.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            dataSourceStore.reload();
                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    	dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }
    
    function getDataRecord(record){
    		var map=record.data;
    		var contenHtml='  <div class="table_content2">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
    		contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
    		for(var k in map) {
    			var val='';
    			var value=map[k];
    			if(k=='IBUSINESS'){
    				val='业务系统'; 
    			}
    			if(k=='IRSTYPE'){
    				val='数据源类型'; 
    			}
    			if(k=='IIP'){
    				val='数据库IP'; 
    			}
    			if(k=='instancename'){
    				val='实例名'; 
    			}
    			if(k=='IDBPORT'){
    				val='端口号'; 
    			}
    			if(k=='ISID'){
    				val='服务名'; 
    			}
    			if(k=='IDBVERSION'){
    				val='数据库版本'; 
    			}
    			if(k=='rac'){
    				val='Rac'; 
    			}
    			if(k=='INAME'){
    				val='设备名称'; 
    			}
    			if(k=='IDBUSER'){
    				if(qdbuserSwitch){
    					val='数据库用户'; 
    				}else{
    					val='查询用户'; 
    				}
    			}
    			if(k=='iqdbuser'){
    				if(qdbuserSwitch){
    					val='操作用户'; 
    				}
    			}
    			if(k=='IDBID'){
    				val='IDBID'; 
    			}
    			if(k=='IRACGROUP'){
    				val='分组名称'; 
    			}
    			if(k=='istartuptime'){
    				val='设备启动时间'; 
    			}
    			if(k=='continueCount'){
    				val='连续错误'; 
    			}
    			if(k=='cumulateCount'){
    				val='累计错误'; 
    			}
    			if(k=='cumulateCount'){
    				val='累计错误'; 
    			}
    			if(k=='ISTATUS'){
    				val='状态'; 
    				if(value==0){
    					value='有效'; 
    				}
    				if(value==1){
    					value='查询用户异常'; 
    				}
    				if(value==2){
    					value='操作用户异常'; 
    				}
    				if(value==3){
    					value='全部异常';  
    				}
    			}
    			if(k=='lastcheck'){
    				val='最近检测时间'; 
    			}
    			if(val!=''){
    				contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+val+"</span></td><td><span class=\"wt05\">"+value+"</span></td></tr>"
    			}
    		}
    		contenHtml=contenHtml+"</tbody></table></div></div>";
    		showWindow(contenHtml);
    }
    function getData(){
    	 var data = dataSourceGrid.getView().getSelectionModel().getSelection();
         if (data.length != 1) {
             Ext.Msg.alert('提示', '请先选择您要操作的一行记录!');
             return;
         }else{
        	 var map=data[0].data;
        	 var contenHtml='  <div class="table_content2">  <div id="table" class=\'table_chart\' ><table cellpadding=\'0\' cellspacing=\'0\' border=\'0\' style="width:540px">';
        	 contenHtml=contenHtml+"  <thead> <tr><th><span class=\"wt04\">key</span></th><th><span class=\"wt05\">value</span></th></tr></thead> <tbody  class=\"table_tbody\">";
        	 for(var k in map) {
        		 var val='';
        		 var value=map[k];
    			 if(k=='IBUSINESS'){
    				 val='业务系统'; 
    			 }
    			 if(k=='IRSTYPE'){
    				 val='数据源类型'; 
    			 }
    			 if(k=='IIP'){
    				 val='数据库IP'; 
    			 }
    			 if(k=='instancename'){
    				 val='实例名'; 
    			 }
    			 if(k=='IDBPORT'){
    				 val='端口号'; 
    			 }
    			 if(k=='ISID'){
    				 val='服务名'; 
    			 }
    			 if(k=='IDBVERSION'){
    				 val='数据库版本'; 
    			 }
    			 if(k=='rac'){
    				 val='Rac'; 
    			 }
    			 if(k=='INAME'){
    				 val='设备名称'; 
    			 }
    			 if(k=='IDBUSER'){
    				 if(qdbuserSwitch){
    					 val='数据库用户'; 
    				 }else{
    					 val='查询用户'; 
    				 }
    			 }
    			 if(k=='iqdbuser'){
    				 if(qdbuserSwitch){
    					 val='操作用户'; 
    				 }
    			 }
    			 if(k=='IDBID'){
    				 val='IDBID'; 
    			 }
    			 if(k=='IRACGROUP'){
    				 val='分组名称'; 
    			 }
    			 if(k=='istartuptime'){
    				 val='设备启动时间'; 
    			 }
    			 if(k=='continueCount'){
    				 val='连续错误'; 
    			 }
    			 if(k=='cumulateCount'){
    				 val='累计错误'; 
    			 }
    			 if(k=='cumulateCount'){
    				 val='累计错误'; 
    			 }
    			 if(k=='ISTATUS'){
    				 val='状态'; 
    				 if(value==0){
    					 value='有效'; 
    				 }
    				 if(value==1){
    					 value='查询用户异常'; 
    				 }
    				 if(value==2){
    					 value='操作用户异常'; 
    				 }
    				 if(value==3){
    					 value='全部异常';  
    				 }
    			 }
    			 if(k=='lastcheck'){
    				 val='最近检测时间'; 
    			 }
    			 if(val!=''){
    				 contenHtml=contenHtml+"<tr><td><span class=\"wt04\">"+val+"</span></td><td><span class=\"wt05\">"+value+"</span></td></tr>"
    			 }
        	 }
        	 contenHtml=contenHtml+"</tbody></table></div></div>";
        	 showWindow(contenHtml);
         } 
    }
    
    function showWindow(contenHtml){
        var resultDesc = Ext.create('Ext.panel.Panel', {
               region:'center',
               border:false,
               readOnly: true,
           }); 
        resultDesc.html=contenHtml;     
        var detailWindow = Ext.create('widget.window', {
           title: '详情',
           closable: true,
           closeAction: 'hide',
           resizable: false,
           modal: true,
           width : 600,
           height :500,
           layout: {
               type: 'border',
               padding: 5
           },
           items: [resultDesc]
           
       });
       detailWindow.show ();
   }
    function saveDatabase(c){
    	if(!queryUserBtnPermisBoolean(1,'','saveResourceManage.do')){
    		Ext.Msg.alert('提示', '该资源您没有修改权限！');
    		return;
    	}
    	choice=c;
    	if(choice==0){
    		showEditForm();
    	}else if(choice==1){
    		var record = dataSourceGrid.getSelectionModel().getSelection();    		
        	if(record.length==0){
        		Ext.Msg.alert('提示', '请选择一条要编辑的记录！');
        		return;
        	}else if(record.length>1){
        		Ext.Msg.alert('提示', '只能选择一条要编辑的记录！');
        		return;
        	} 
        	var position=record[0].data.IPOSITION;
        	if(position==0){
    			Ext.Msg.alert('提示', '该资源您没有修改权限！');
        		return;
    		}else{
        		showEditForm(record[0]);
        	}
    	}
    }
	var businnessTypeStore = Ext.create('Ext.data.JsonStore', {
		fields: ['INAME', 'INAME'],
		// autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getBusinessTypeCode.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
	businnessTypeStore.on('beforeload', function(store, options) {
			var new_params = {
			};
			Ext.apply(businnessTypeStore.proxy.extraParams, new_params);
	});

    // 编辑时打开一个记录窗口
    function showEditForm(record) {
    	var SRTeditWindow;
    	var oldIRSTYPE="";
        if (null==SRTeditWindow ) {
            var form = Ext.widget('form', {
                border: false,
                bodyPadding: 3,
                // baseCls:'customize_gray_back',
                autoScroll: true,
                items: [ 
                {
					border : false,
					layout : 'column',
					items :[{
		                	xtype:'hiddenfield',
		                	name:'IID'
			             }, {
		                	xtype:'hiddenfield',
		                	name:'IAPPLYUSER'
			                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
	                	xtype : 'combo',
	                    name:'IRSTYPE',
	                    fieldLabel: '数据源类型',
	                    triggerAction: 'all',
	                    editable: false,
	                    queryMode: 'local',
	                    displayField: 'name',
	                    valueField: 'name',
	                    columnWidth : .5,
	                    width : contentPanel.getWidth() - 20,
						labelWidth:75,
	                    labelAlign:'right',
	                    padding : '5 5 10 5',
	                    store: dataTypeModel,
	                    allowBlank: false,
	                    maxLength:50,
	                    listeners :{
	                    	"change" : function(e){
	                    		var IDBPORT =form.getForm().findField('IDBPORT').getValue();
	                    		if(e.value=="MYSQL"){
	                    			if (IDBPORT=='' || (oldIRSTYPE!='' && oldIRSTYPE!=e.value)){
	                    				form.getForm().findField('IDBPORT').setValue(3306);
	                    			}
	                    			form.getForm().findField('iqdbuser').hide();
	                    			form.getForm().findField('iqdbpwd').hide();
	                    		}else if(e.value=="DB2"){
	                    			if (IDBPORT=='' || (oldIRSTYPE!='' && oldIRSTYPE!=e.value)){
	                    			form.getForm().findField('IDBPORT').setValue(50000);
	                    			}
	                    			form.getForm().findField('iqdbuser').show();
	                    			form.getForm().findField('iqdbpwd').show();
	                    		}else{
	                    			if (IDBPORT=='' || (oldIRSTYPE!='' && oldIRSTYPE!=e.value)){
	                    			form.getForm().findField('IDBPORT').setValue(17632);
	                    			}
	                    			form.getForm().findField('iqdbuser').show();
	                    			form.getForm().findField('iqdbpwd').show();
	                    		}
	                    		oldIRSTYPE=form.getForm().findField('IRSTYPE').getValue();
	                    	}
	                    }
	                },{
		                	xtype : 'combo',
		                    name:   'IBUSINESS',
		                    fieldLabel: '业务系统',
		                    triggerAction: 'all',
// editable: false,
		                    multiSelect: true,
		                    queryMode: 'local',
		                    emptyText: "--请选择--",
		                    displayField: 'sysName',
		                    valueField: 'sysName',
		                    // labelWidth : 80,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
							labelWidth:75,
		                    labelAlign:'right',
		                    padding : '5 5 10 5',
		                    store: sysDataByUserIdStore,
		                    allowBlank: false,
		                    maxLength:255,
		                    listeners : {
		                        beforequery : function(e) {  
		                            var combo = e.combo;     
		                            if(!e.forceAll){     
		                                var value = e.query;     
		                                combo.store.filterBy(function(record, id){     
		                                    var text = record.get(combo.displayField);     
		                                    return (text.toUpperCase().indexOf(value.toUpperCase())!=-1);     
		                                });  
		                                combo.expand();     
		                                return false;     
		                            }  
		                        }  
		                    }
		                },
                        {
                            xtype: 'combo',                     
                            name:'IBUSINESSTYPE',
                            fieldLabel: '系统分类',
                            triggerAction: 'all',
                            // 用all表示把下拉框列表框的列表值全部显示出来
                            editable: false,
                            queryMode: 'local',
                            emptyText: "--请选择系统分类--",
		                    hidden:true,
                            displayField: 'INAME',
                            valueField: 'INAME',
                            columnWidth : .5,
                            width : contentPanel.getWidth() - 20,
							labelWidth:75,
		                    labelAlign:'right',
                            padding : '5 5 10 5',
                            store:businnessTypeStore,
                            allowBlank: true,
                            maxLength:50
                        }]
                },
                {
					border : false,
					layout : 'column',
					items :[
// {
// xtype : 'combo',
// name:'IRSTYPE',
// fieldLabel: '数据源类型',
// triggerAction: 'all',
// editable: false,
// queryMode: 'local',
// displayField: 'name',
// valueField: 'name',
// columnWidth : .5,
// width : contentPanel.getWidth() - 20,
// labelWidth:75,
// labelAlign:'right',
// padding : '5 5 10 5',
// store: dataTypeModel,
// allowBlank: false,
// maxLength:50
// },
						{
		                	xtype: 'textfield',  
		                    name:'ICOPYNUM',
		                    fieldLabel: '副本号',
		                    allowBlank: true,
		                    hidden:true,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                },{
                            xtype: 'textfield',
                            name:'IIP',
                            fieldLabel: '服务器IP',
                            columnWidth : .5,
                            width : contentPanel.getWidth() - 20,
							labelWidth:75,
		                    labelAlign:'right',
                            padding : '5 5 10 5',
                            regex:/^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/,
                            regexText:'IP格式错误',
                            allowBlank: false,
                            maxLength:50
                        },{
		                	xtype: 'numberfield',
		                    name:'IDBPORT',
		                    fieldLabel: '数据库端口',
		                    regex:/^\d*$/,
		                    regexText:'请填写数字',
		                    value:17632,
		                    maxValue: 65535,
		                    minValue: 1,
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
							labelWidth:75,
		                    labelAlign:'right',
		                    padding : '5 5 10 5',
		                    maxLength:5
		                }]
                },
                {
					border : false,
					layout : 'column',
					items : [{
                        xtype: 'textfield',
                        name:'ISID',
                        fieldLabel: '服务名',
                        //allowBlank: false,
                        columnWidth : .5,
                        width : contentPanel.getWidth() - 20,
						labelWidth:75,
	                    labelAlign:'right',
                        padding : '5 5 10 5',
                        maxLength:25
                    }]
                },
                {
					border : false,
					layout : 'column',
					items : [ {
                        xtype: 'textfield',
                        name:'IDBUSER',
                        fieldLabel: '查询用户',
                        allowBlank: false,
                        columnWidth : .5,
                        width : contentPanel.getWidth() - 20,
                        labelWidth:75,
                        labelAlign:'right',
                        padding : '5 5 10 5',
                        maxLength:255
                        },{
	                    xtype: 'textfield',
	                    name:'IDBPWD',
	                    fieldLabel: '查询密码',
	                    inputType: 'password',
	                    allowBlank: false,
	                    columnWidth : .5,
	                    width : contentPanel.getWidth() - 20,
						labelWidth:75,
	                    labelAlign:'right',
	                    padding : '5 5 10 5',
	                    maxLength:25
	                }]
                },{
                    border : false,
                    layout : 'column',
                    items : [ {
                        xtype: 'textfield',
                        name:'iqdbuser',
                        fieldLabel: '操作用户',
                        allowBlank: false,
                        columnWidth : .5,
                        width : contentPanel.getWidth() - 20,
                        labelWidth:75,
                        labelAlign:'right',
                        hidden:qdbuserSwitch,
                        padding : '5 5 10 5',
                        maxLength:255
                        },{
                        xtype: 'textfield',
                        name:'iqdbpwd',
                        fieldLabel: '操作密码',
                        inputType: 'password',
                        hidden:qdbuserSwitch,
                        allowBlank: false,
                        columnWidth : .5,
                        width : contentPanel.getWidth() - 20,
                        labelWidth:75,
                        labelAlign:'right',
                        padding : '5 5 10 5',
                        maxLength:25
                    }]
                },
                {
					border : false,
					layout : 'column',
					items : [{
	                            xtype : 'combo',
	                            name:'IMODEL',
	                            fieldLabel: '资源模式',
	                            hidden:othSwitch,
	                            triggerAction: 'all',
	                            editable: false,
	                            queryMode: 'local',
	                            emptyText: "--请选择--",
	                            displayField: 'name',
	                            valueField: 'value',
	                            columnWidth : .5,
	                            labelWidth:75,
	                            labelAlign:'right',
	                            padding : '5 5 10 5',
	                            store: Ext.create('Ext.data.Store', {
	                                fields: ['name','value'],
	                                data: [{
	                                    name: "RAC",
	                                    value: "1"
	                                },{
	                                    name: "实例",
	                                    value: "2"
	                                },
	                                {
	                                    name: "DATAGUARD",
	                                    value: "3"
	                                },
	                                {
	                                    name: "用户",
	                                    value: "4"
	                                }]
	                            }),
	                            allowBlank: true,
	                        },{
	                            labelWidth:75,
	                            labelAlign:'right',
	                            hidden:othSwitch,
		                	// 控件类型为numberfield
		                	xtype: "numberfield",
		                	// 字段名称，绑定和获取数据的时候用到
		                	name: "IDISK",
		                	// 显示的标签
		                	fieldLabel: "磁盘(G)",
		                	// 控件的值
		                	value: 50,
		                	// 能否为空，true为必填项，false为可以为空
		                	allowBlank: true,
		                	// 最大值
		                	maxValue: 1048576,
		                	// 最小值
		                	minValue: 50,
		                	// 获得焦点时选中输入的内容
		                	selectOnFocus: true,
		                	// 是否只读，true为只读，false为可编辑
		                	readOnly: false,
		                	// 是否可用，true为不可用，false为可用
		                	disabled: false,
		                	// 是否隐藏上下调节按钮
		                	hideTrigger: false,
		                	// 键盘导航是否可用，启用后可以通过键盘的上下箭头调整数值
		                	keyNavEnabled: true,
		                	// 鼠标滚轮是否可用，启用后可以通过滚动鼠标滚轮调整数值
		                	mouseWheelEnabled: true,
		                	// 通过调节按钮、键盘、鼠标滚轮调节数值时的大小
		                	step: 2,
		                	columnWidth : .5,
		                    padding : '5 5 10 5'
		                }]
    			},
    			{
					border : false,
					layout : 'column',
					items :[{
					    labelWidth:75,
					    labelAlign:'right',
		                	 // 控件类型为numberfield
		                    xtype: "numberfield",
		                    // 字段名称，绑定和获取数据的时候用到
		                    name: "ICPU",
		                    // 显示的标签
		                    fieldLabel: "CPU",
		                    // 控件的值
		                    value: 2,
		                    // 能否为空，true为必填项，false为可以为空
		                    allowBlank: true,
		                    // 最大值
		                    maxValue: 16,
		                    // 最小值
		                    minValue: 1,
		                    // 获得焦点时选中输入的内容
		                    selectOnFocus: true,
		                    // 是否只读，true为只读，false为可编辑
		                    readOnly: false,
		                    // 是否可用，true为不可用，false为可用
		                    disabled: false,
		                    // 是否隐藏上下调节按钮
		                    hideTrigger: false,
		                    // 键盘导航是否可用，启用后可以通过键盘的上下箭头调整数值
		                    keyNavEnabled: true,
		                    // 鼠标滚轮是否可用，启用后可以通过滚动鼠标滚轮调整数值
		                    mouseWheelEnabled: true,
		                    // 通过调节按钮、键盘、鼠标滚轮调节数值时的大小
		                    step: 2,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    hidden:othSwitch
		                },{
		                    labelWidth:75,
		                    labelAlign:'right',
		                	// 控件类型为numberfield
		                	xtype: "numberfield",
		                	// 字段名称，绑定和获取数据的时候用到
		                	name: "IMEMORY",
		                	// 显示的标签
		                	fieldLabel: "内存",
		                	// 控件的值
		                	value: 2,
		                	// 能否为空，true为必填项，false为可以为空
		                	allowBlank: true,
		                	// 最大值
		                	maxValue: 128,
		                	// 最小值
		                	minValue: 2,
		                	// 获得焦点时选中输入的内容
		                	selectOnFocus: true,
		                	// 是否只读，true为只读，false为可编辑
		                	readOnly: false,
		                	// 是否可用，true为不可用，false为可用
		                	disabled: false,
		                	// 是否隐藏上下调节按钮
		                	hideTrigger: false,
		                	// 键盘导航是否可用，启用后可以通过键盘的上下箭头调整数值
		                	keyNavEnabled: true,
		                	// 鼠标滚轮是否可用，启用后可以通过滚动鼠标滚轮调整数值
		                	mouseWheelEnabled: true,
		                	// 通过调节按钮、键盘、鼠标滚轮调节数值时的大小
		                	step: 2,
		                	columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    hidden:othSwitch
		                }]
                },
                 {  border : false,
                    layout : 'column',
                    items :[{
		                    xtype: 'combo',
		                    name:'IENV',
		                    fieldLabel: '环境',
		                    triggerAction: 'all',
		                    // 用all表示把下拉框列表框的列表值全部显示出来
		                    editable: false,
		                    hidden:othSwitch,
		                    queryMode: 'local',
		                    emptyText: "--请选择--",
		                    displayField: 'name',
		                    valueField: 'value',
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    store: Ext.create('Ext.data.Store', {
		                        fields: ['name','value'],
		                        data: [{
		                            name: "测试",
		                            value: "0"
		                        },
		                        {
		                    		name: "生产",
		                    		value: "1"
		                    	},
		                        {
		                    		name: "研发",
		                    		value: "2"
		                    	}]
		                    }),
		                    allowBlank: true,
		                    maxLength:50
		                }
		                ]
                }, 
                {
					border : false,
					layout : 'column',
					items :[{
					    labelWidth:75,
		    				xtype: 'textfield',
		                	name:'IALTERLOGNAME',
		                	fieldLabel: '日志文件',
		                	allowBlank: true,
		                	hidden:logSwitch,
		                	columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                	maxLength:25
		                },
		                {
		                    labelWidth:75,
		                	xtype : 'combo',
		                    name:'ISTATUS',
		                    fieldLabel: '状态',
		                    triggerAction: 'all',
		                    editable: false,
		                    queryMode: 'local',
		                    emptyText: "--请选择--",
		                    displayField: 'name',
		                    valueField: 'value',
		                    hidden:true,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    store: Ext.create('Ext.data.Store', {
		                        fields: ['name','value'],
		                        data: [{
		                            name: "有效",
		                            value: "0"
		                        },
		                        {
		                    		name: "失效",
		                    		value: "1"
		                    	}]
		                    }),
		                    allowBlank: true,
		                    maxLength:50
		                }]
                },{
					border : false,
					layout : 'column',
					items :[{
					        labelWidth:75,
					        labelAlign:'right',
		    				xtype: 'textfield',
		                	name:'IRACGROUP',
		                	fieldLabel: '分组名称',
		                	allowBlank: true,
		                	columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                	maxLength:25
		                }
		                ]
                
                }],
                dockedItems : [{
					xtype : 'toolbar',
					border : false,
					baseCls:'customize_gray_back',
					dock : 'bottom',
					items : ['->',{
						cls : 'Common_Btn',
						textAlign : 'center',
						text: '取消',
	                    handler: function() {                   	
	                        this.up('form').getForm().reset();
	                        this.up('window').close();
	                        SRTeditWindow=null;
	                    }
	                }, {
	                	cls : 'Common_Btn',
	    				textAlign : 'center',
	                	text: '保存',
	                    handler: function() {
	                		var IID="";
	                		var url="saveResourceManage.do";	                		
	                    	if(choice==1){
	                    		IID=form.getForm().findField('IID').getValue();
	                    		url="updateResourceManage.do";
	                    	}
//	                    	var n=0;
//	                    	var syslen=0;
//	                    	var cname = form.getForm().findField('IBUSINESS').getValue();
//	                    	var strs=cname.split(","); //字符分割
//	                    	syslen=strs.length;
//	                    	for (var i=0;i<syslen;i++ )
//                			{
//	                    		var tname = strs[i];
//	                    		for ( var k = 0; k < sysDataByUserIdStore.getCount(); k++) {
//		                			var record = sysDataByUserIdStore.getAt(k);
//		                			if (tname == record.data.sysName.trim()) {
//		                				n = n + 1;
//		                				continue;
//		                			}
//	                    		}
//                			}
//	                		if (n < 1 || n<syslen) {
//	                			// 应用系统名称不能重复
//	                			Ext.MessageBox.alert("提示", "您所选择的应用系统名称不存在，请重新选择！");
//	                			return;
//	                		}
	                		var IRSTYPE = form.getForm().findField('IRSTYPE').getValue();
	                        if ("" == IRSTYPE || null == IRSTYPE) {
	                            setMessage('数据源类型不能为空！');
	                            return;
	                        }
	                        var ISID = form.getForm().findField('ISID').getValue();
	                        if (("" == ISID || null == ISID) && IRSTYPE != "MYSQL") {
	                            setMessage('MYSQL以外的数据源类型，服务名不能为空！');
	                            return;
	                        }
	                        if (fucCheckLength(ISID) > 25) {
	                            setMessage('服务名不能超过25字符！');
	                            return;
	                        }
	                		var IIP = form.getForm().findField('IIP').getValue();
	                        if ("" == IIP || null == IIP) {
	                            setMessage('服务器IP不能为空！');
	                            return;
	                        }
	                        if (!checkIP(IIP)) {
	                        	setMessage('服务器IP:'+IIP+'格式不正确!');
	                        	return;
	            			}
	                        var IDBPORT = form.getForm().findField('IDBPORT').getValue();
	                        if ("" == IDBPORT || null == IDBPORT) {
	                        	setMessage('数据库端口不能为空！');
	                            return;
	                        }else if(!isNumber(IDBPORT)){
	                        	setMessage('请正确填写数据库端口！');
	                            return;
	                        }
	                        var IDBUSER = form.getForm().findField('IDBUSER').getValue();
	                        if ("" == IDBUSER || null == IDBUSER) {
	                            setMessage('查询用户不能为空！');
	                            return;
	                        }
	                        if (fucCheckLength(IDBUSER) > 100) {
	                            setMessage('查询用户不能超过100字符！');
	                            return;
	                        }
	                        var IDBPWD = form.getForm().findField('IDBPWD').getValue();
	                        if ("" == IDBPWD || null == IDBPWD) {
                                setMessage('查询密码不能为空！');
	                            return;
	                        }
	                        if (fucCheckLength(IDBPWD) > 25) {
                                setMessage('查询密码不能超过25字符！');
	                            return;
	                        }
	                        if(!qdbuserSwitch) {
    	                        var Iqdbuser = form.getForm().findField('iqdbuser').getValue();
    	                        if (("" == Iqdbuser || null == Iqdbuser) && IRSTYPE != "MYSQL") {
    	                            setMessage('操作用户不能为空！');
    	                            return;
    	                        }
    	                        if (fucCheckLength(Iqdbuser) > 255) {
    	                            setMessage('操作用户不能超过100字符！');
    	                            return;
    	                        }
    	                        var Iqdbpwd = form.getForm().findField('iqdbuser').getValue();
    	                        if (("" == Iqdbpwd || null == Iqdbpwd) && IRSTYPE != "MYSQL") {
    	                            setMessage('操作密码不能为空！');
    	                            return;
    	                        }
    	                        if (fucCheckLength(Iqdbpwd) > 25) {
    	                            setMessage('操作密码不能超过25字符！');
    	                            return;
    	                        }
	                        }
                        	 var jsonData = '{"IID":"'+IID+ '",';
                        	 jsonData=jsonData+'"IDBID":"",';
                        	 jsonData=jsonData+'"ITYPE":"1",';
                        	 jsonData=jsonData+'"IAPPLYUSER":"'+form.getForm().findField('IAPPLYUSER').getValue() + '",';
                        	 jsonData=jsonData+'"IBUSINESS":"'+form.getForm().findField('IBUSINESS').getValue() + '",';
                        	 jsonData=jsonData+'"IIP":"'+form.getForm().findField('IIP').getValue() + '",';
                        	 jsonData=jsonData+'"IRSTYPE":"'+form.getForm().findField('IRSTYPE').getValue() + '",';
                        	 jsonData=jsonData+'"ICOPYNUM":"'+form.getForm().findField('ICOPYNUM').getValue() + '",';
                        	 jsonData=jsonData+'"IDBPORT":"'+form.getForm().findField('IDBPORT').getValue() + '",';
                        	 jsonData=jsonData+'"IDBUSER":"'+form.getForm().findField('IDBUSER').getValue() + '",';
                        	 jsonData=jsonData+'"IDBPWD":"'+form.getForm().findField('IDBPWD').getValue() + '",';
                        	 jsonData=jsonData+'"Iqdbuser":"'+form.getForm().findField('iqdbuser').getValue() + '",';
                        	 jsonData=jsonData+'"Iqdbpwd":"'+form.getForm().findField('iqdbpwd').getValue() + '",';
                        	 jsonData=jsonData+'"ISID":"'+form.getForm().findField('ISID').getValue() + '",';
                        	 jsonData=jsonData+'"IMODEL":"'+form.getForm().findField('IMODEL').getValue() + '",';
                        	 jsonData=jsonData+'"IFLAG":0,';
                        	 if(logSwitch){
                        		 jsonData=jsonData+'"IALTERLOGNAME":"",';
                        	 }else{
                        		 jsonData=jsonData+'"IALTERLOGNAME":"'+form.getForm().findField('IALTERLOGNAME').getValue() + '",';
                        	 }
                        	 jsonData=jsonData+'"ISTATUS":"'+form.getForm().findField('ISTATUS').getValue() + '",';
                        	 jsonData=jsonData+'"ICPU":"'+form.getForm().findField('ICPU').getValue() + '",';
                        	 jsonData=jsonData+'"IMEMORY":"'+form.getForm().findField('IMEMORY').getValue() + '",';
                        	 jsonData=jsonData+'"IDISK":"'+form.getForm().findField('IDISK').getValue() + '",';
                        	 jsonData=jsonData+'"IENV":"'+form.getForm().findField('IENV').getValue() + '",';
                        	 jsonData=jsonData+'"IBUSINESSTYPE":"'+form.getForm().findField('IBUSINESSTYPE').getValue() + '",';
                        	 jsonData=jsonData+'"IRACGROUP":"'+form.getForm().findField('IRACGROUP').getValue() + '"}';
                        	 Ext.Msg.wait('处理中，请稍后...', '提示');  
                        	 Ext.Ajax.request({
    							method:"POST",
    							timeout:6000000,
    							url:url,
    							params:{jsonData:jsonData,dataModel:0},
    							success:function(response){
    								Ext.Msg.hide();
    								var text = Ext.JSON.decode(response.responseText);
    								if(text.success){
    									Ext.Msg.alert('提示', text.message);
    									dataSourceStore.reload();   
    								}else{
    		    						Ext.Msg.alert('提示', text.message);
    								}
    							},
    							failure:function(form, action){
    								switch (action.failureType) {
    		    					case Ext.form.action.Action.CLIENT_INVALID:
    		    						Ext.Msg.alert('提示', '连接异常！');
    		    						break;
    		    					case Ext.form.action.Action.SERVER_INVALID:
    		    						Ext.Msg.alert('提示', action.result.message);
    		    					}
    							}
    						});
                            this.up('window').close();
                            SRTeditWindow=null;
	                    }
	                },{
	                	cls : 'Common_Btn',
	    				textAlign : 'center',
	                	text: '重置',
	                    handler: function() {
	                    	this.up('form').getForm().reset();
	                    	if(choice==1){
	                    		this.up('form').getForm().loadRecord(record);
	                    	}	                      	                        
	                    }
	                }]
                }]
            });
            if(choice==1){
            	form.loadRecord(record);
            }
            SRTeditWindow = Ext.widget('window', {
                title: '编辑记录',
                closeAction: 'hide',
                constrain: true,
                resizable: false,
    			width : contentPanel.getWidth()*0.6,
    			// height : contentPanel.getHeight(),
                minWidth: 300,
                minHeight: 300,
                layout: 'fit',
                modal: true,
                items: form,
                defaultFocus: 'firstName'
            });
        }
        SRTeditWindow.show();
    }    

});

function testvalid(iid) {
    Ext.Msg.wait('处理中，请稍后...', '提示');  
    Ext.Ajax.request({
       method:"POST",
       timeout:300000,// 5分钟
       url:"chkResourceValid.do",
       params:{iid:iid,from:0},
       success: function(response, request) {
           var success = Ext.decode(response.responseText).success;
           var message = Ext.decode(response.responseText).message;
           if(success){
               Ext.Msg.alert('提示', message);
           }else{
               Ext.Msg.alert('提示', message);
           }
           dataSourceStore.reload();
       },
       failure:function(form, action){
           switch (action.failureType) {
           case Ext.form.action.Action.CLIENT_INVALID:
               Ext.Msg.alert('提示', '连接异常！');
               break;
           case Ext.form.action.Action.SERVER_INVALID:
               Ext.Msg.alert('提示', action.result.message);
           }
       }
   });
}

function showDetail(IID) {
	Ext.create('Ext.window.Window', {
	    title: '详情',
	    height: '60%',  // Number型 也可以是字符串类型 width:'60%'
	    width: '60%',
	    layout: 'fit',
	    constrain: true, 		// 闲置窗口不超出浏览器
	    constrainHeader:true, 	// 标题不能超出浏览器边界
	    modal: true,			// 设置模态窗口
	    plain:true, 			// 窗口设置透明背景
	    draggable: false,
	    resizable: false,
	    loader: {
			url: 'todetailResourceManage.do',
			params : {
				IID : IID
			},
			autoLoad: true,
			scripts: true
		},
	    autoScroll:true // 显示滚动条
	}).show();
}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}