Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	Ext.define('resourceModel', {
		extend : 'Ext.data.Model',
		fields : [ {
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        }
        ,
        {
            name: 'IIP',
            type: 'string'
        }
        ,
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IFLAG',
            type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'IDBUSER',
            type: 'string'
        },
        {
            name: 'IDBPWD',
            type: 'string'
        },
        {
            name: 'ICOPYNUM',
            type: 'string'
        },
        {
            name: 'IAPPLYUSER',
            type: 'string'
        },
        {
            name: 'ISID',
            type: 'string'
        },
        {
            name: 'ITYPE',
            type: 'long'
        },
        {
            name: 'ISTATUS',
            type: 'string'
        }
		]

	});

	/** *********************Store********************* */
	/** 任务列表数据源* */
	var resourceStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'resourceModel',
		pageSize : 6,
		proxy : {
			type : 'ajax',
			url : 'detailResourceManage.do?id=' +IID ,// utLogInfoRecord.do
		
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'totalUT'
			}
		}
	});

	resourceStore.on('load', function(store, options, success) {
		var reader = store.getProxy().getReader();
		Ext.getCmp("INAME").setValue(reader.jsonData.INAME);
		Ext.getCmp("IRSTYPE").setValue(reader.jsonData.IRSTYPE);
		Ext.getCmp("IIP").setValue(reader.jsonData.IIP);
		Ext.getCmp("IDBPORT").setValue(reader.jsonData.IDBPORT);
//		Ext.getCmp("IFLAG").setValue(reader.jsonData.IFLAG);
		var statusValue=reader.jsonData.ISTATUS;
		if(statusValue=="0"){
			Ext.getCmp("ISTATUS").setValue("有效");
		}else{
			Ext.getCmp("ISTATUS").setValue("失效");
		}
		Ext.getCmp("IBUSINESS").setValue(reader.jsonData.IBUSINESS);
		Ext.getCmp("IDBUSER").setValue(reader.jsonData.IDBUSER);
		Ext.getCmp("IDBPWD").setValue(reader.jsonData.IDBPWD);
		Ext.getCmp("ICOPYNUM").setValue(reader.jsonData.ICOPYNUM);
		Ext.getCmp("IAPPLYUSER").setValue(reader.jsonData.IAPPLYUSER);
		Ext.getCmp("ISID").setValue(reader.jsonData.ISID);
		Ext.getCmp("ITYPE").setValue(reader.jsonData.ITYPE);
	});

	var resourceForm = Ext.create('Ext.form.Panel', {
		layout : 'anchor',
		region : 'center',
		buttonAlign : 'center',
		border : false,
		bodyCls : 'x-docked-noborder-top',
		items : [ {
			layout : 'column',
			anchor : '95%',
			padding : '5 0 5 0',
			border : false,
			items : [ {
				id : "INAME",
				fieldLabel : '设备名称',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : "IRSTYPE",
				fieldLabel : '类型',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : 'IIP',
				fieldLabel : 'IP',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : 'IDBPORT',
				fieldLabel : 'IDBPORT',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ISTATUS',
				fieldLabel : '状态',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {//IBUSINESS, IDBUSER, IDBPWD, IAPPLYUSER, ISID,ITYPE
				id : 'IBUSINESS',
				fieldLabel : '业务系统',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IDBUSER',
				fieldLabel : '数据源用户',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IDBPWD',
				fieldLabel : '数据源密码',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : 'IAPPLYUSER',
				fieldLabel : '申请人',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ISID',
				fieldLabel : 'SID',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ITYPE',
				fieldLabel : '类型',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ICOPYNUM',
				fieldLabel : '副本号',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}
			]
		} ]
	});
	var workItemRecord_mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "ut_grid",
		width : '100%',
		height : '100%',
		autoScroll :true,
		border : false,
		bodyPadding : 5,
		layout : 'border',
		items : [ resourceForm ]
	});

	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(workItemRecord_mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});

});
