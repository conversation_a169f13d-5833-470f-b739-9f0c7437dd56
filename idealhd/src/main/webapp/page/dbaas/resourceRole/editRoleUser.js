Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	/** *********************Store********************* */
	var selModelp = Ext.create ('Ext.selection.CheckboxModel',
	{
		checkOnly : true,
		mode : "SINGLE"
	});
	
	 Ext.define('userModel', {
	        extend: 'Ext.data.Model',
	        fields: [{
		            name: 'userId',
		            type: 'long'
		        },
		        {
		            name: 'userName',
		            type: 'string'
		        }
	        ]
	    });
	
	var userStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model: 'userModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getUserlistByResIdAndRoleId.do?resId=' + resId+'&column='+column,
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	var rolePanel = Ext.create ('Ext.grid.Panel',
	{
	    height : 340,
	    width : '100%',
	    border : true,
	    store : userStore,
	    columns : [
	            {
	                text : 'userId',
	                dataIndex : 'userId',
	                hidden : true
	            },
	            {
	                text : '姓名',
	                dataIndex : 'userName',
	                flex : 1
	            }
	    ],
	    forceFit : true,
	    selModel : selModelp,
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        dock : 'top',
		        items : [
		                {
		                    xtype : 'button',
		                    cls : 'Common_Btn',
		                    text : '保存',
		                    handler : saveRole_local
		                },
		                {
		                    xtype : 'button',
		                    cls : 'Common_Btn',
		                    text : '关闭',
		                    handler : function ()
		                    {
			                    parent.roleWindow.close();
		                    }
		                }
		        ]
		    }
	    ]
	});
	
	/** 主panel* */
	var mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "MainDiv",
	    width : '100%',
	    height : '100%',
	    border : false,
	    bodyPadding : 5,
	    items : [
		    rolePanel
	    ]
	});
	
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	/** *********************方法********************* */
	// 保存记录
	function saveRole_local ()
	{
		var checkedRecords = rolePanel.getSelectionModel ().getSelection();
		if(checkedRecords.length==0){
			 Ext.Msg.alert('提示', "请选择一名人员！");
			 return false;
		}
		Ext.Ajax.request (
		{
		    url : 'editResourceRole.do',
		    params :
		    {
		        userId : checkedRecords[0].data.userId,
		        resId : resId,
		        roleNum:column=="DBA-A"?1:2
		    },
		    success : function (response)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    if (success)
			    {
			        Ext.Msg.alert('提示', "保存成功！");
			        parent.dataSourceStore.reload();
			        parent.roleWindow.close();
			    }
			    else
			    {
			        Ext.Msg.alert('提示', message);
			    }
		    },
		    failure : function ()
		    {
		        Ext.Msg.alert('提示', "保存失败！");
		    }
		});
		
	}
});
