var dataSourceStore;
var roleWindow;
Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   // destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
       
        {
            name: 'ISID',
            type: 'string'
        },
        {
        	name: 'IRACGROUP',
        	type: 'string'
        },
        {
        	name: 'roleNumber',
        	type: 'int'
        },
        {
        	name: 'DBA-A',
        	type: 'string'
        },
        {
        	name: 'DBA-B',
        	type: 'string'
        },{
        	name: 'iracgroup',
        	type: 'string'
        },{
            name: 'continueCount',
            type: 'long'
        },{
            name: 'cumulateCount',
            type: 'long'
        },       
        {
            name: 'istatus',
            type: 'string'
        }
        ]
    });
    
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceRoleList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        },
        groupField:'IRACGROUP' 
    });
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
        	baseBusiness: businessField.getValue(),
        	baseIp: ipField.getValue(),
			baseSid: sidField.getValue(),
			state:ss_state,
			dataType:dataTypeField.getValue(),
			racGroup:racGroupField.getValue(),
			dataRoot:dataRootField.getValue()
        };

        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });

    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        }        ]
    });
    
    
    var sysDataByUserIdStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageByUserId.do?projectFlag=1',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
    var dataTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getDatabaseType.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
    
    var dataRootModel = Ext.create('Ext.data.Store', {
		fields : [ 'value', 'text' ],
		data : [{
			"value" : "1",
			"text" : "DBA-A"
		},
		 {
			 "value": "2",
			 "text": "DBA-B"
		},{
			 "value": "-1",
			 "text": "未分配"
		}]
	});
    
    
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65, 
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },{
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        minWidth: 200,
        flex:1,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        width: 90,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: 'IP',
        dataIndex: 'IIP',
        width: 120,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '端口号',
        dataIndex: 'IDBPORT',
        width: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        minWidth: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: 'DBA-A',
        dataIndex: 'DBA-A',
        minWidth: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: 'DBA-B',
        dataIndex: 'DBA-B',
        minWidth: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '分组名称',
        dataIndex: 'iracgroup',
        width: 100,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
    		metadata.tdAttr = 'data-qtip="' + value + '"';
    		return value;
    	}
	},{
        text: '连续错误',
        dataIndex: 'continueCount',
        width: 80,
        editor:false
    },{
        text: '累计错误',
        dataIndex: 'cumulateCount',
        width: 80,
        editor:false
    },{
        header: '状态',
        dataIndex: 'istatus',
        minWidth: 120,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "有效",
                    value: "0"
                },
                {
            		name: "查询用户异常",
            		value: "1"
            	},{
            		name: "操作用户异常",
            		value: "2"
            	},{
            		name: "全部异常",
            		value: "3"
            	}]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('istatus');
            if(IFLAG=='0'){
            	return "<span class='Complete_Green State_Color'>有效</span>";
            }else if(IFLAG=='1'){
            	return "<span class='Abnormal_yellow State_Color'>查询用户失效</span>";
            }else if(IFLAG=='2'){
            	return "<span class='Abnormal_yellow State_Color'>操作用户失效</span>";
            }else if(IFLAG=='3'){
            	return "<span class='Abnormal_yellow State_Color'>全部失效</span>";
            }
        }
    }
    ];
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var businessField =  Ext.create("Ext.form.field.ComboBox",{
        labelWidth: 65,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '13%',
    	triggerAction: 'all',
    	editable : true,
        queryMode: 'local',
        emptyText: "--请选择业务系统--",
        displayField: 'sysName',
        valueField: 'sysName',
        store: sysDataByUserIdStore,
        listeners : {
            beforequery : function(e) {  
                var combo = e.combo;     
                if(!e.forceAll){     
                    var value = e.query;     
                    combo.store.filterBy(function(record, id){     
                        var text = record.get(combo.displayField);     
                        return (text.toUpperCase().indexOf(value.toUpperCase())!=-1);     
                    });  
                    combo.expand();     
                    return false;     
                }  
            }  
        }
    });
    
    var ipField = Ext.create("Ext.form.field.Text", {
        labelWidth: 25,
        labelAlign: 'left',
        emptyText: "--请输入IP--",
        name: 'dataBaseNameParam',
        width: '10%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var sidField = Ext.create("Ext.form.field.Text", {
        labelWidth: 50,
        labelAlign: 'left',
        emptyText: "--请输入服务名--",
        name: 'dataBaseNameParam',
        width: '13%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    
    var dataTypeField =  Ext.create("Ext.form.field.ComboBox",{
        labelWidth: 65,
        labelAlign: 'left',
        name: 'dataTypeParam',
        width: '13%',
    	triggerAction: 'all',
    	editable : true,
        queryMode: 'local',
        emptyText: "--请选择数据源类型--",
        displayField: 'name',
        valueField: 'name',   
        store: dataTypeModel    
    });
    
    var racGroupField = Ext.create("Ext.form.field.Text", {
      labelWidth: 25,
      labelAlign: 'left',
      emptyText: "--请输入分组名称--",
      name: 'racGroupParam',
      width: '11%',
      listeners: {
          specialkey: function(field, e){
              if (e.getKey() == e.ENTER) {
              	pageBar.moveFirst();
              }
          }
      }
    });
    
    var dataRootField =  Ext.create("Ext.form.field.ComboBox",{
        labelWidth: 65,
        labelAlign: 'left',
        name: 'dataTypeParam',
        width: '13%',
    	triggerAction: 'all',
    	hidden:true,
    	editable : true,
        queryMode: 'local',
        emptyText: "--请选择所属DBA权限--",
        displayField : 'text',
		valueField : 'value',
        store: dataRootModel    
    });
    
	
    
    var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			baseCls:'customize_gray_back',
			border : false,
			items : [businessField,
	            ipField,
	            sidField,
	            dataTypeField,
	            racGroupField,
	            dataRootField,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            }
	           ]
			}]
	});
    
    var dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
        selModel: selModel,
        padding : panel_margin,
		border: false,
		cls:'customize_panel_back',
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        features : [
                    Ext.create ('Ext.grid.feature.Grouping',
                    {
                        groupByText: '用本字段分组',
                        showGroupsText : '显示分组',
                        groupHeaderTpl: [
                                         '<div>分组信息--- 名称:{name:this.formatName}-(数量{rows.length})</div>',
                                         {
                                             formatName: function(name) {
                                                 var value ='';
                                                 if(name =='') {
                                                     value='未分组'; 
                                                 }else {
                                                     value=name;
                                                 }
                                                    
                                                 return Ext.String.trim(value);
                                             }
                                         }
                                     ],
                        startCollapsed : false
                    // 设置初始分组是不是收起
                    })
                ],
        listeners: {
        	celldblclick: function (view, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		var column = view.getHeaderAtIndex( td.cellIndex).dataIndex;
        		if(column=="DBA-A"||column=="DBA-B"){
        			editRole(record,column);
        		}
        	}
        }
    });

    function editRole(record,column){
    	roleWindow=Ext.create ('Ext.window.Window',
			{
			    title : '选择人员',
			    modal : true,
			    closeAction : 'destroy',
			    constrain : true,
			    autoScroll : true,
			    width : 365,
			    height : 385,
			    minWidth : 350,
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
			    layout : 'fit',
			    loader :
			    {
			        url : 'editRoleUserJsp.do',
			        params :
			        {
			        	resId : record.get("IID"),
			        	column : column,
			        },
			        autoLoad : true,
			        scripts : true
			    }
			});
    	roleWindow.show();
    }

    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resource_manage",
        layout: 'border',
        bodyCls:'service_platform_bodybg customize_stbtn',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function clearQueryWhere() {
    	businessField.setValue('');
    	ipField.setValue('');
    	sidField.setValue('');
    	dataTypeField.setValue('');
    	racGroupField.setValue('');
    	dataRootField.setValue('');
    }

});



function editRole(iid,roleNumber) {
    if(roleNumber==-1){
    	Ext.Msg.alert('提示',"该资源尚未配置DBA类型权限，请到应用系统管理进行权限设定");
    	return false;
    }
    var dutyStore = Ext.create('Ext.data.Store', {
        fields: ['name', 'value'],
        data : [
            {"name":1, "value":"DBA-A"},
            {"name":2, "value":"DBA-B"}
        ]
    });
    var editWindow;
    if (null==editWindow ) {
        var form = Ext.widget('form', {
            border: false,
            bodyPadding: 3,
            autoScroll: true,
            items: [ 
	                {
                    border : false,
                    layout : 'column',
                    items :[
							{
								xtype: 'combobox',
								name : 'duty',
								queryMode : 'local',
								fieldLabel : '所属权限',
								labelWidth: 105,
								padding : '5 5 10 5',
								editable : false,
								allowBlank: false,
								displayField : 'value',
								valueField : 'name',
								labelAlign : 'right',
								columnWidth : 0.96,
								emptyText : '--请选择所属权限--',			
								store : dutyStore
							}
                            ]
                    }
                ],
            dockedItems : [{
                xtype : 'toolbar',
                border : false,
                baseCls:'customize_gray_back',
                dock : 'bottom',
                items : ['->',{
                    cls : 'Common_Btn',
                    textAlign : 'center',
                    text: '取消',
                    handler: function() {                       
                        this.up('form').getForm().reset();
                        this.up('window').close();
                        usreditWindow=null;
                    }
                }, {
                    cls : 'Common_Btn',
                    textAlign : 'center',
                    text: '确定',
                    handler: function() {
                    	var duty=form.getForm().findField('duty').getValue();
                    	if(!duty){
                    		Ext.Msg.alert('提示', "请选择所属角色");
                    		return false;
                    	}
                    	Ext.Ajax.request({
                            method:"POST",
                            timeout:300000,// 5分钟
                            url:"editResourceRole.do",
                            params:{resourceId:iid,roleNumber:duty},
                            success: function(response, request) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if(success){
                                	 editWindow.close();
                                     editWindow=null;
                                	 dataSourceStore.reload();
                                }
                                Ext.Msg.alert('提示', message);
                            },
                            failure:function(form, action){
                                switch (action.failureType) {
                                case Ext.form.action.Action.CLIENT_INVALID:
                                    Ext.Msg.alert('提示', '连接异常！');
                                    break;
                                case Ext.form.action.Action.SERVER_INVALID:
                                    Ext.Msg.alert('提示', action.result.message);
                                }
                            }
                        });
                       
                   	 }
                }]
            }]
        });
        editWindow = Ext.widget('window', {
            title: '编辑',
            closeAction: 'hide',
            constrain: true,
            resizable: false,
            width : contentPanel.getWidth()*0.5,
            minWidth: 200,
            minHeight: 200,
            layout: 'fit',
            modal: true,
            items: form,
            defaultFocus: 'firstName'
        });
    }
    editWindow.show();
}
