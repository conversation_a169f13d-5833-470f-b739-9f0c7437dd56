Ext.onReady(function(){
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	Ext.define('scriptService', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		},{
			name : 'flowId',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		},{
			name : 'sysName',
			type : 'string'
		}, {
			name : 'bussName',
			type : 'string'
		}, {
			name : 'serviceType',
			type : 'string'
		}, {
			name : 'dbType',
			type : 'string'
		}, {
			name : 'scriptType',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'ssuer',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		},{
			name : 'servicePara',
			type : 'string'
		}, {
			name : 'platForm',
			type : 'string'
		}, {
			name : 'status',
			type : 'int'
		},{
			name : 'content',
			type : 'string'
		},{
			name : 'serviceTy',
			type : 'String'
		}, {
			name : 'startType',
			type : 'String'
		},{
			name : 'bussId',
			type : 'int'
		}, {
			name : 'bussTypeId',
			type : 'int'
		}, {
			name : 'scriptLevel',
			type : 'int'
		}, {
			name : 'isFlow',
			type : 'string'
		} ]
	});
	var scriptservice_columns = [ {
			text : '序号',
			xtype : 'rownumberer',
			align:'left',
			width : 70
		}, {
			text : '主键',
			dataIndex : 'iid',
			hidden : true
		}, {
			text : 'bind',
			dataIndex : 'flowId',
			hidden : true
		}, {
			text : '服务名称',
			dataIndex : 'serviceName',
			width : 400,
			flex : 1
		}, {
			text : '服务类型',
			dataIndex : 'serviceType',
		//	hidden : true,
			width : 80,
			renderer : function(value, p, record) {
				var backValue = "";
				if (value == 0) {
					backValue = "应用";
				} else if (value == 1) {
					backValue = "采集";
				}	
				return backValue;
			}
		}, {
			text : '数据库类型',
			dataIndex : 'dbType',
		//	hidden : true,
			width : 100
		}, {
			text : '脚本类型',
			dataIndex : 'scriptType',
			width : 80,
			hidden : true,
			renderer : function(value, p, record) {
				var backValue = "";
				if (value == "sh") {
					backValue = "shell";
				} else if (value == "perl") {
					backValue = "perl";
				} else if (value == "py") {
					backValue = "python";
				} else if (value == "bat") {
					backValue = "bat";
				} else if (value == "sql") {
					backValue = "sql";
				}
				if (record.get('isFlow') == '1') {
					backValue = "组合";
				}
				return backValue;
			}
		}, {
			text : '发布人',
			dataIndex : 'ssuer',
			width : 150,
			hidden : true
		}, {
			text : '版本号',
			dataIndex : 'version',
			width : 80,
			hidden : true
		}, {
			text : '发布状态',
			dataIndex : 'startType',
			width : 100,
			hidden : true
		},{
			text : '有效状态',
			dataIndex : 'status',
			width : 80,
			hidden : true,
			renderer : function(value, p, record) {
				var backValue = "";
				if (value == 1) {
					backValue = "<span class='Complete_Green State_Color'>启用</span>";
				} else if (value == 2) {
					backValue = "<span class='Abnormal_yellow State_Color'>禁用</span>";
				}
				return backValue;
			}
		}
		];
	
	var scriptservice_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'scriptService',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceDatasForGroup.do?posiflag=1',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	scriptservice_store.on ('beforeload', function (store, options)
				{
					var new_params =
					{
						serviceName : search_form.getForm().findField('serviceName').getValue().trim(),
						groupId : -1
					};
					Ext.apply (scriptservice_store.proxy.extraParams, new_params);
				});
	var selModelServer = Ext.create('Ext.selection.CheckboxModel');
	var pageBarServer = Ext.create('Ext.PagingToolbar', {
	    	store: scriptservice_store,
	        dock: 'bottom',
	        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border:false
	});
	var scriptservice_grid = Ext.create('Ext.grid.Panel', {
			region : 'center',
			store : scriptservice_store,
			columnLines : true,
			border:true,
			bbar: pageBarServer,
			padding : panel_margin,
			columns : scriptservice_columns,
			selModel : selModelServer
	});
		
	var search_form = Ext.create('Ext.form.Panel', {
			region : 'north',
			bodyCls : 'x-docked-noborder-top',
			border : false,
			dockedItems : [ {
				xtype : 'toolbar',
				dock : 'top',
				border : false,
				items : [{
						xtype: 'textfield',
						fieldLabel : '服务名',
						labelWidth : 60,
						labelAlign : 'left',
						name : 'serviceName',
						width : '30%'
					},{
						xtype: 'button',
	 		            textAlign:'center',
	  		            text: '查询',
	  		            cls : 'Common_Btn',
	  		            handler:function(){
	  		            	scriptservice_store.reload();
	  		            }
					},{
						xtype: 'button',
	 		            textAlign:'center',
	  		            text: '清空',
	  		            cls : 'Common_Btn',
	  		            handler:function(){
	  		            	search_form.getForm().findField('serviceName').setValue('');
	  		            }
					},'->',{
	  		            xtype: 'button',
	 		            textAlign:'center',
	  		            text: '发起审核',
	  		            cls : 'Common_Btn',
	  		            handler:function(){
	  		            	execServiceForSign();
	  		            }
	  		        }
				]
			} ]
		});
	var rightPanelM = Ext.create('Ext.panel.Panel', {
			layout : 'border',
			border : false,
			items : [ search_form, scriptservice_grid ]
	});
	var panelrigth = Ext.create('Ext.panel.Panel', {
			layout : 'fit',
	    	//title : '服务信息',
			region : 'center',
			split : true,
			bodyCls : 'x-docked-noborder-top',
			border : false,
			width : '50%', 
			items : [ rightPanelM ]
		});
		
	var mainPanel = Ext.create('Ext.panel.Panel',{
	    	height:contentPanel.getHeight()-modelHeigth-50,
	        renderTo : 'servicesinglemanage',
	        border : true,
	        bodyPadding : grid_margin,
	        layout : 'border',
	        bodyCls:'service_platform_bodybg',
	    	items : [panelrigth]
	    });
	    
	    contentPanel.on('resize',function(){
	    	mainPanel.setHeight(contentPanel.getHeight()-modelHeigth-50);
	    	if(win){
	    		win.setWidth(contentPanel.getWidth()-500);
		  		win.setHeight(contentPanel.getHeight()-100);
	    	}
	    });
	    
	    function execServiceForSign(){
	    	Ext.Msg.confirm('确认提示','您确定要进行发起操作吗？',function(bn){
	    		if(bn=='yes'){
	    			var selDatas = scriptservice_grid.getSelectionModel().getSelection();
	    			if(selDatas.length==0 ||selDatas.length>1){
	    				Ext.Msg.alert('消息提示','请选择且只能选择一条记录进行发起操作！');
	    				return;
	    			}
	    			var iids = [];
	    			var groupId=-1;
	    			Ext.Array.each(selDatas, function(record) {
	    	            var iid = record.get('iid');
	    	            // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
	    	            iids.push(iid);
	    	        });
	    				execServiceForExec(iids,groupId,0);
	    			
	    		}
	    	});
	    }
	    function execServiceForExec(iids,groupId,pubflag){
			Ext.Ajax.request({
				url : 'serviceGroupHasVersion.do',
				method : 'POST',
				params : {
					ids : iids,
					groupId:groupId
				},
				success : function(response, opts) {
				var hasVersion = Ext.decode(response.responseText).hasVersion;
				if (hasVersion == 1 && onlysignal_flag == true) {
					Ext.Msg.alert('提示', "该服务组已经发起过！");
					return;
				} else {
				Ext.Ajax.request({
					url : 'serviceGroupStatus.do',
					method : 'POST',
					params : {
						ids : iids,
						groupId:groupId
					},
					success : function(response, opts) {
						var status = Ext.decode(response.responseText).status;
						if (status == 1) {
							Ext.Msg.alert('提示', "该申请正处于审核中！");
							return;
						}else if (status == 2 && onlysignal_flag == true) {
							Ext.Msg.alert('提示', "该申请已发起，不能重复发起！");
							return;
						} else {
							var requiredCfg = '<span style="color:red;font-weight:bold" data-qtip="按输入周期执行">  *</span>';
							Ext.define('AuditorModel', {
								extend : 'Ext.data.Model',
								fields : [ {
									name : 'loginName',
									type : 'string'
								}, {
									name : 'fullName',
									type : 'string'
								} ]
							});

							var auditorStore_sm = Ext.create('Ext.data.Store', {
								autoLoad : true,
								model : 'AuditorModel',
								proxy : {
									type : 'ajax',
									url : 'getExecAuditorList.do?scriptLevel=1',//getPublishAuditorList.do
									reader : {
										type : 'json',
										root : 'dataList'
									}
								}
							});
							
							var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
								editable : false,
								fieldLabel : "审核人",
								labelWidth : 60,
								store : auditorStore_sm,
								queryMode : 'local',
								columnWidth : .98,
								margin : '10 0 0 0',
								displayField : 'fullName',
								valueField : 'loginName'
							});
							
							var planTime_sm = Ext.create('Go.form.field.DateTime', {
								fieldLabel : '计划时间',
								format : 'Y-m-d H:i:s',
//													hidden : true,
								labelWidth : 70,
								columnWidth : .5,
								minValue: new Date(),
								margin : '10 0 0 0'
							});
							var versionAresource = Ext.create('Ext.form.DisplayField', {
								fieldLabel : '资源配置',
								labelWidth : 70,
								columnWidth : .5,
								margin : '10 0 10 0', 
								value:'<span style=\'color: red\'>点我进行资源组与资源版本配置</span>',//<img src="images/result_report.png" align="left" width="80px" height="30px"></img>
								listeners: {
					        	    render: function(p) {
					            	    p.getEl().on('click', function(p){
					            	    	$('#scripttempService_dev').each(function() {
						                	    $(this).remove();
						                	});
					            	    	Ext.create('Ext.window.Window', {
					            				title : '版本与资源组配置',
					            				modal : true,
					            				closeAction : 'destroy',
					            				constrain : true,
					            				autoScroll : true,
					            				width : 700,
					            				height : 600,
					            				draggable : false,// 禁止拖动
					            				resizable : false,// 禁止缩放
					            				layout : 'fit',
					            				loader : {
					            					url : 'goVersionAndResource.do',
					            					params : {
					            						serviceId : groupId,
					            						serviceGroupId:groupId
					            					},
					            					autoLoad : true,
					            					scripts : true
					            				}
					            			}).show();
					            	    });
					            	}}
							});

							var cycleStore = Ext.create('Ext.data.Store', {
								fields : [ 'name', 'value' ],
								data : [  {
									"name" : "0",
									"value" : "按计划执行一次"
								},{
									"name" : "1",
									"value" : "间隔x日"
								}, {
									"name" : "2",
									"value" : "间隔x小时"
								}, {
									"name" : "3",
									"value" : "间隔x分钟"
								} ]
							});

							 var planTime_MM = Ext.create('Ext.form.field.ComboBox', {
									fieldLabel : '周期类型',
									editable : false,
									name : 'MM',
									padding : '0 5 0 0',
									matchFieldWidth:false,// 此处要有
									labelWidth :60,
									columnWidth : .35,
									store: {
										 	fields: ['value'],
										    data : [
										    	{"value":"按计划执行一次"},
										        {"value":"间隔x日"},
										        {"value":"间隔x小时"},
										        {"value":"间隔x分钟"}
										    ]
									 },
									 displayField:'value',
									 value:"间隔x日",
									 listeners:{
										 select : function(nf, newv, oldv) {
											},
										 change : function(nf, newv, oldv) {
											 if(newv=='间隔x日'){
												 planTime_DD.setValue('');
												 planTime_HH.setValue('');
												 planTime_mi.setValue('');
												 planTime_DD.show();
												 planTime_HH.show();
												 planTime_mi.show();
											 }else if(newv=='间隔x小时'){
												 planTime_DD.setValue('');
												 planTime_HH.setValue('');
												 planTime_mi.setValue('');
												 planTime_DD.hide();
												 planTime_HH.show();
												 planTime_mi.show();
											 }else if(newv=='间隔x分钟'){
												 planTime_DD.setValue('');
												 planTime_HH.setValue('');
												 planTime_mi.setValue('');
												 planTime_DD.hide();
												 planTime_HH.hide();
												 planTime_mi.show();
											 }else if(newv=='按计划执行一次'){
												 planTime_DD.setValue('');
												 planTime_HH.setValue('');
												 planTime_mi.setValue('');
												 planTime_DD.hide();
												 planTime_HH.hide();
												 planTime_mi.hide();
											 }
										 }
									 }
								 });
							var planTime_DD = Ext.create('Ext.form.NumberField', {
								fieldLabel : '天数',
								editable : true,
								name : 'DD',
								padding : '0 5 0 0',
								labelWidth : 40,
								columnWidth : .20,
								listeners : {
									select : function(nf, newv, oldv) {
									},
									change : function(nf, newv, oldv) {
										if (null == newv || newv == '' || trim(newv) == '') {
											planTime_DD.setValue("")
										} else {
											if (/^[0-9]([0-9])*$/.test(newv)) {
												if (newv > 31) {
													Ext.Msg.alert("提示", "天数值需在1~31之间!");
													planTime_DD.setValue(oldv)
												}
												return true;
											} else {
												Ext.Msg.alert("提示", "天数窗口只能正整数");
												planTime_DD.setValue(oldv)
												return false;
											}
										}
									}
								}
							});
							var planTime_HH = Ext.create('Ext.form.NumberField', {
								fieldLabel : '小时',
								editable : true,
								name : 'HH',
								padding : '0 5 0 0',
								labelWidth : 40,
								columnWidth : .20,
								listeners : {
									select : function(nf, newv, oldv) {
									},
									change : function(nf, newv, oldv) {
										if (null == newv || newv == '' || trim(newv) == '') {
											planTime_HH.setValue("")
										} else {
											if (/^[0-9]([0-9])*$/.test(newv)) {
												if (newv > 23) {
													Ext.Msg.alert("提示", "小时值需在1~23之间!");
													planTime_HH.setValue(oldv)
												}
												return true;
											} else {
												Ext.Msg.alert("提示", "小时窗口只能正整数");
												planTime_HH.setValue(oldv)
												return false;
											}
										}
									}
								}
							});
							var planTime_mi = Ext.create('Ext.form.NumberField', {
								fieldLabel : '分钟',
								editable : true,
								name : 'mi',
								padding : '0 5 0 0',
								labelWidth : 40,
								columnWidth : .20,
								listeners : {
									select : function(nf, newv, oldv) {
									},
									change : function(nf, newv, oldv) {
										if (null == newv || newv == '' || trim(newv) == '') {
											planTime_mi.setValue("")
										} else {
											if (/^[0-9]([0-9])*$/.test(newv)) {
												if (newv > 59) {
													Ext.Msg.alert("提示", "分钟值需在1~59之间!");
													planTime_mi.setValue(oldv)
												}
												return true;
											} else {
												Ext.Msg.alert("提示", "分钟窗口只能正整数");
												planTime_mi.setValue(oldv)
												return false;
											}
										}
									}
								}
							});
								var newv=planTime_MM.getValue();
								if(newv=='间隔x日'){
									 planTime_DD.show();
									 planTime_HH.show();
									 planTime_mi.show();
								 }else if(newv=='间隔x小时'){
									 planTime_DD.setValue('');
									 planTime_HH.setValue('');
									 planTime_mi.setValue('');
									 planTime_DD.hide();
									 planTime_HH.show();
									 planTime_mi.show();
								 }else if(newv=='间隔x分钟'){
									 planTime_DD.setValue('');
									 planTime_HH.setValue('');
									 planTime_mi.setValue('');
									 planTime_DD.hide();
									 planTime_HH.hide();
									 planTime_mi.show();
								 }else if(newv=='按计划执行一次'){
									 planTime_DD.setValue('');
									 planTime_HH.setValue('');
									 planTime_mi.setValue('');
									 planTime_DD.hide();
									 planTime_HH.hide();
									 planTime_mi.hide();
								 }
							var cfg_Display = Ext.create('Ext.form.DisplayField', {
								name : 'display',
								fieldLabel : '周期配置',
								afterLabelTextTpl : requiredCfg,
								labelWidth : 350,
								columnWidth : .9
							});
							var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
								name : 'pubdesc',
								fieldLabel : '详细说明',
								emptyText : '',
								labelWidth : 60,
								margin : '10 0 0 0',
								height : 80,
								columnWidth : .98,
								autoScroll : true
							});
							var taskName = new Ext.form.TextField({
								name : 'taskName',
								fieldLabel : '任务名称',
								displayField : 'taskName',
								emptyText : '',
								labelWidth : 60,
								columnWidth : .5
							});
							var formPanel = Ext.create('Ext.form.Panel', {
								width : 600,
								layout : 'anchor',
								bodyCls : 'x-docked-noborder-top',
								buttonAlign : 'center',
								border : false,
								items : [ {
									anchor : '98%',
									padding : '5 0 5 5',
									border : false,
									items : [ {
										layout : 'column',
										border : false,
										padding : '5 10 5 5',
										items : [ planTime_sm]
									},{
										layout : 'column',
										border : false,
										padding : '5 10 5 5',
										items : [ versionAresource ]
									},{
										layout : 'column',
										border : false,
										padding : '5 10 5 5',
										items : [ taskName ]
									},
//									{
//										layout : 'column',
//										border : false,
//										padding : '5 10 5 5',
//										items : [ cfg_Display ]
//									}, 
									{
										layout : 'column',
										border : false,
										padding : '5 10 5 5',
										items : [ planTime_MM, planTime_DD, planTime_HH, planTime_mi ]
									},
									{
										layout : 'column',
										border : false,padding : '5 10 5 5',
										items : [ auditorComBox_sm ]
									}, {
										layout : 'column',
										border : false,padding : '5 10 5 5',
										items : [ pubDesc_sm ]
									} ]
								} ]
							});	
							var	publishAuditingSMWin = Ext.create('Ext.window.Window', {
									title : '确认审核信息',
									closable : true,
//									closeAction : 'close',
									resizable : false,
									closeAction : 'destory',
									modal : true,
									width : 600,
									height : 500,
									minWidth : 350,
									layout : {
										type : 'border',
										padding : 5
									},
									items : [ formPanel ],
									dockedItems : [ {
										xtype : 'toolbar',
										border : false,
										dock : 'bottom',
										layout : {
											pack : 'center'
										},
										items : [ {
											xtype : "button",
											cls : 'Common_Btn',
											text : "确定",
											handler : function() {
												var planTime = planTime_sm.getRawValue();
												var planTime_MM_1 = planTime_MM.getValue();
												var planTime_DD_1 = planTime_DD.getValue();
												var planTime_HH_1 = planTime_HH.getValue();
												var planTime_mi_1 = planTime_mi.getValue();
												var scriptLevel = 100;
												var publishDesc = pubDesc_sm.getValue();
												var auditor = auditorComBox_sm.getValue();
												var task = taskName.getValue();
												if (!task) {
													Ext.Msg.alert('提示',"没有填写任务名称！");
													return;
												}
												if (!scriptLevel) {
													Ext.Msg.alert('提示', "没有选择风险级别！");
													return;
												}
												if (!publishDesc) {
													Ext.Msg.alert('提示', "没有填写详细说明！");
													return;
												}
												if (!auditor) {
													Ext.Msg.alert('提示', "没有选择审核人！");
													return;
												}
												if(planTime_MM_1=='间隔x日'){
													if (planTime_DD_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
														Ext.Msg.alert('提示',"天数必须填写！");
														return;
													}
												}else if(planTime_MM_1=='间隔x小时'){
													if (planTime_HH_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
														Ext.Msg.alert('提示',"小时必须填写！");
														return;
													}
												}else if(planTime_MM_1=='间隔x分钟'){
													if (planTime_mi_1==''||planTime_mi_1==null||planTime_mi_1=='null') {
														Ext.Msg.alert('提示',"分钟必须填写！");
														return;
													}
												}else if(planTime_MM_1=='按计划执行一次'){
												}else{
													Ext.Msg.alert('提示',"请选择周期类型！");
													return;
												}
												var sIds = new Array();
												sIds.push(groupId);
												Ext.Ajax.request({
													url : 'scriptPublishAuditingForGroup.do',
													method : 'POST',
													params : {
														sIds : sIds,
														planTime : planTime,
														scriptLevel : scriptLevel,
														publishDesc : publishDesc,
														auditor : auditor,
														flag : pubflag,
														planTimeType : planTime_MM_1,
														planTimeDD : planTime_DD_1,
														planTimeHH : planTime_HH_1,
														planTimeMM : planTime_mi_1,
														taskName:task,
														serviceType : 1,
														iids:iids
													// 0-来着个人脚本库
													},
													success : function(response, opts) {
														var success = Ext.decode(response.responseText).success;
														var message = Ext.decode(response.responseText).message;
														if (!success) {
															Ext.MessageBox.alert("提示", message);
														} else {
															Ext.MessageBox.alert("提示", "请求已经发送到审核人");
														}
														publishAuditingSMWin.close();
														this.up("window").close();
													},
													failure : function(result, request) {
														secureFilterRs(result, "操作失败！");
														publishAuditingSMWin.close();
														this.up("window").close();
													}
												});
											}
										}, {
											xtype : "button",
											cls : 'Common_Btn',
											text : "取消",
											handler : function() {
												this.up("window").close();
											}
										} ]
									}]
								}).show();
							}
						}, failure : function(result, request) {
							secureFilterRs(result, "操作失败！");
							return;
						}
						});
				  		}
					},failure : function(result, request) {
						secureFilterRs(result, "操作失败！");
						return;
					}
				});
	    }
})