Ext.onReady(function(){
	destroyRubbish();
	var tmpgroupId;
	Ext.tip.QuickTipManager.init();
	Ext.define('DataModel', {
        extend: 'Ext.data.Model',
        fields: [
        	{
    			name : 'IID',
    			type : 'string'
    		}, {
    			name : 'INAME',
    			type : 'string'
    		}, {
    			name : 'INAMEDESC',
    			type : 'string'
    		}, {
    			name : 'ICREATETIME',
    			type : 'string'
    		}, {
    			name : 'ICREATEUSERID',
    			type : 'string'
    		}, {
    			name : 'IUPDATETIME',
    			type : 'string'
    		}, {
    			name : 'IUPDATEUSERID',
    			type : 'string'
    		}, {
    			name : 'ISTATUS',
    			type : 'string'
    		}, {
    			name : 'ISERVICEAUTO',
    			type : 'string'
    		}
        ]
    });
    
	var sgmstore = Ext.create('Ext.data.Store', {
		pageSize : 50,
		autoLoad : true,
		model : 'DataModel',
		proxy : {
			type : 'ajax',
			url : 'getServiceGroupManageList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	 var serviceAutoStore = Ext.create('Ext.data.Store', {
			fields: ['id','name'],
			data : [
			        {"id":"0","name":"DBA"},
			        {"id":"1","name":"项目组"}]
		 });

	 var serviceAutoCombo = Ext.create('Ext.form.field.ComboBox', {
			margin : '5',
			store : serviceAutoStore,
			queryMode : 'local',
			width : 600,
			forceSelection : true, // 要求输入值必须在列表中存在
			typeAhead : true, // 允许自动选择
			displayField : 'name',
			valueField : 'id',
			editable : false,
			triggerAction : "all"
		});
	var columns = [ {
		xtype : 'rownumberer',
		align:'left',
		header : '序号',
		align : 'center',
		width : 60
	}, {
		text : 'TABLE_IID',
		dataIndex : 'IID',
		align : 'left',
		hidden : true
	}, {
		text : '组名称',
		dataIndex : 'INAME',
		align : 'left',
		editor: {
            allowBlank: false
        },
        width : 300,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, {
		text : '组描述',
		dataIndex : 'INAMEDESC',
		align : 'left',
		editor: {
            allowBlank: true
        },
		width : 300,
		flex : 1,
		renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	},{
		text : '服务权限',
		dataIndex : 'ISERVICEAUTO',
		editor : serviceAutoCombo,
		align : 'left',
		width : 100,
		renderer : function(value, metadata, record) {
					var index = serviceAutoStore.find('id', value);
					if (index != -1) {
						return serviceAutoStore.getAt(index).data.name;
					} else {
						return '';
					}
				}
	},{
		text : '创建人',
		dataIndex : 'ICREATEUSERID',
		align : 'left',
		width : 180,
		hidden : true
	}, {
		text : '创建时间',
		dataIndex : 'ICREATETIME',
		align : 'center',
		width : 180,
		hidden : true
	}, {
		text : '是否发起',
		dataIndex : 'ISTATUS',
		align : 'center',
		width : 180,
		renderer : function(value, p, record) {
			var backValue = "";
			if (value == 0) {
				backValue = "<span class='Not_running State_Color'>未发起</span>";
			} else if (value == 1) {
				backValue = "<span class='Abnormal_Complete_purple State_Color'>审核中</span>";
			} else if (value == 2) {
				backValue = "<span class='Complete_Green State_Color'>已发起</span>";
			}	
			return backValue;
		}
	} ];

    var selModel = Ext.create('Ext.selection.CheckboxModel');

    var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: sgmstore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false
    });
    
	sgmstore.on('beforeload', function(store, options) {
		var new_params = {
			sname : form.getForm().findField('sname').getValue()
		};
		Ext.apply(sgmstore.proxy.extraParams, new_params);
	});
	
	var sname = Ext.create("Ext.form.field.Text", {
		fieldLabel : '组名称',
		labelWidth : 60,
		labelAlign : 'left',
		name : 'sname',
		width : '30%'
	});

	var queryButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '查询',
		handler : queryWhere
	});
	/** 重置按钮* */
	var resetButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '重置',
		handler : resetWhere
	});
	var form = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [ sname,queryButtonForBSM,resetButtonForBSM, '->', {
				xtype : 'button',
				text : '新增',
				cls : 'Common_Btn',
				handler : addInfo
			}, {
				xtype : 'button',
				text : '保存',
				cls : 'Common_Btn',
				handler : saveInfo
			}, {
				xtype : 'button',
				text : '删除',
				cls : 'Common_Btn',
				handler : delInfo
			},{
  		            xtype: 'button',
 		            textAlign:'center',
  		            text: '发起审核',
  		            cls : 'Common_Btn',
  		            handler:execServiceForGroup
  		        }			
			]
		} ]
	});
    var groupName_grid = Ext.create('Ext.grid.Panel', {
    	region:'center',
    	selModel:selModel,
	    store:sgmstore,
	    padding : panel_margin,
	    bbar: pageBar,
		border:true,
	    columnLines : true,
	    columns:columns,
	    viewConfig:{  
            enableTextSelection:true  
        },
	    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
 	    dockedItems: [{}]
	});  
    var panelleftM = Ext.create('Ext.panel.Panel',{
    	layout : 'border',
    	border : false,
		items : [form,groupName_grid]
    });
    
    var panelleft = Ext.create('Ext.panel.Panel',{
    	title:'服务组配置',
    	layout : 'fit',
		border:false,
		bodyCls : 'x-docked-noborder-top',
		region : 'west',
		split : true,
		width : '65%', 
		items : [panelleftM]
    });
    
	Ext.define('scriptService', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		},{
			name : 'flowId',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		},{
			name : 'sysName',
			type : 'string'
		}, {
			name : 'bussName',
			type : 'string'
		}, {
			name : 'serviceType',
			type : 'string'
		}, {
			name : 'dbType',
			type : 'string'
		}, {
			name : 'scriptType',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'ssuer',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		},{
			name : 'servicePara',
			type : 'string'
		}, {
			name : 'platForm',
			type : 'string'
		}, {
			name : 'status',
			type : 'int'
		},{
			name : 'content',
			type : 'string'
		},{
			name : 'serviceTy',
			type : 'String'
		}, {
			name : 'startType',
			type : 'String'
		},{
			name : 'bussId',
			type : 'int'
		}, {
			name : 'bussTypeId',
			type : 'int'
		}, {
			name : 'scriptLevel',
			type : 'int'
		}, {
			name : 'isFlow',
			type : 'string'
		} ]
	});
	var scriptservice_columns = [ {
			text : '序号',
			xtype : 'rownumberer',
			align:'left',
			width : 70
		}, {
			text : '主键',
			dataIndex : 'iid',
			hidden : true
		}, {
			text : 'bind',
			dataIndex : 'flowId',
			hidden : true
		}, {
			text : '服务名称',
			dataIndex : 'serviceName',
			width : 250,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
			
		}, {
			text : '服务类型',
			dataIndex : 'serviceType',
		//	hidden : true,
			width : 100,
			renderer : function(value, p, record) {
				var backValue = "";
				if (value == 0) {
					backValue = "应用";
				} else if (value == 1) {
					backValue = "采集";
				}	
				return backValue;
			}
		}, {
			text : '数据库类型',
			dataIndex : 'dbType',
		//	hidden : true,
			width : 100,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		}, {
			text : '脚本类型',
			dataIndex : 'scriptType',
			width : 80,
			hidden : true,
			renderer : function(value, p, record) {
				var backValue = "";
				if (value == "sh") {
					backValue = "shell";
				} else if (value == "perl") {
					backValue = "perl";
				} else if (value == "py") {
					backValue = "python";
				} else if (value == "bat") {
					backValue = "bat";
				} else if (value == "sql") {
					backValue = "sql";
				}
				if (record.get('isFlow') == '1') {
					backValue = "组合";
				}
				return backValue;
			}
		}, {
			text : '发布人',
			dataIndex : 'ssuer',
			width : 150,
			hidden : true
		}, {
			text : '版本号',
			dataIndex : 'version',
			width : 80,
			hidden : true
		}, {
			text : '发布状态',
			dataIndex : 'startType',
			width : 100,
			hidden : true
		},{
			text : '有效状态',
			dataIndex : 'status',
			width : 80,
			hidden : true,
			renderer : function(value, p, record) {
				var backValue = "";
				if (value == 1) {
					backValue = "<span class='Complete_Green State_Color'>启用</span>";
				} else if (value == 2) {
					backValue = "<span class='Abnormal_yellow State_Color'>禁用</span>";
				}
				return backValue;
			}
		}
		];
	
	var scriptservice_store = Ext.create('Ext.data.Store', {
		autoLoad : false,
		pageSize : 50,
		model : 'scriptService',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceDatasForGroup.do?posiflag=0',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
    var selModelServer = Ext.create('Ext.selection.CheckboxModel');
    var pageBarServer = Ext.create('Ext.PagingToolbar', {
    	store: scriptservice_store,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false
    });
	var scriptservice_grid = Ext.create('Ext.grid.Panel', {
		region : 'center',
		store : scriptservice_store,
		columnLines : true,
		border:true,
		bbar: pageBarServer,
		padding : panel_margin,
		columns : scriptservice_columns,
		selModel : selModelServer
	});
	
	var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : ['->',{
		        	xtype: 'button',
  		            text: '增加',
  		            textAlign:'center',
  		            cls : 'Common_Btn',
  		            handler:addServiceForGroup
  		        },{
  		            xtype: 'button',
 		            textAlign:'center',
  		            text: '解绑',
  		            cls : 'Common_Btn',
  		            handler:function(){
  		            	deleteServiceForGroup();
  		            }
  		        }/*,{
  		            xtype: 'button',
 		            textAlign:'center',
  		            text: '发起审核',
  		            cls : 'Common_Btn',
  		            handler:function(){
  		            	execServiceForSign();
  		            }
  		        }*/
			]
		} ]
	});
	groupName_grid.on("select",function(obj, record, index, eOpts){
    	var groupId = record.get('IID');
    	if(groupId<1){
    		return;
    	}
    	tmpgroupId=groupId;
    	scriptservice_store.load({
    		params: {
    			groupId : groupId
    		}
    	});
    });
	var rightPanelM = Ext.create('Ext.panel.Panel', {
		layout : 'border',
		border : false,
		items : [ search_form, scriptservice_grid ]
	});
	var panelrigth = Ext.create('Ext.panel.Panel', {
		layout : 'fit',
    	title : '服务信息',
		region : 'center',
		split : true,
		bodyCls : 'x-docked-noborder-top',
		border : false,
		width : '50%', 
		items : [ rightPanelM ]
	});
	
    var mainPanel = Ext.create('Ext.panel.Panel',{
    	height:contentPanel.getHeight()-modelHeigth-50,
        renderTo : "servicegroupmanage_grid_area",
        border : true,
        bodyPadding : grid_margin,
        layout : 'border',
        bodyCls:'service_platform_bodybg',
    	items : [panelleft,panelrigth]
    });
    
    contentPanel.on('resize',function(){
    	mainPanel.setHeight(contentPanel.getHeight()-modelHeigth-50);
    	if(win){
    		win.setWidth(contentPanel.getWidth()-500);
	  		win.setHeight(contentPanel.getHeight()-100);
    	}
    });
	/**
	 * 新增
	 */
	function addInfo() {
		var store = groupName_grid.getStore();
		var p = {
			IID : '0',
			INAME : '',
			INAMEDESC : '',
			ICREATEUSERID : '',
			ICREATETIME : '',
			ISERVICEAUTO:''
		};
		store.insert(0, p);
		groupName_grid.getView().refresh();
	}
	function saveInfo() {
		var m = sgmstore.getModifiedRecords();
		if (m.length < 1) {
			Ext.Msg.alert('提示','无需要增加或者修改的数据！');
			return;
		}
		Ext.MessageBox.wait("数据处理中...", "进度条");
		var jsonData = "[";
		var execflag=0;
		for (var i = 0, len = m.length; i < len; i++) {
			var INAME = m[i].get("INAME").trim();
			if ("" == INAME || null == INAME) {
				Ext.Msg.alert('提示','组名称不能为空！');
				return;
			}
			if (fucCheckLength(INAME) > 255) {
				Ext.Msg.alert('提示','组名称不能超过255字符！');
				return;
			}
			var INAMEDESC = m[i].get("INAMEDESC").trim();
			if ("" != INAMEDESC && null != INAMEDESC) {
				if (fucCheckLength(INAMEDESC) > 255) {
					Ext.Msg.alert('提示','组描述不能超过255字符！');
					return;
				}
			}
			var serviceAutoVaule = m[i].get("ISERVICEAUTO");
			console.log("serviceAutoVaule:"+serviceAutoVaule);
			if ("" == serviceAutoVaule || null == serviceAutoVaule) {
				Ext.Msg.alert('提示','服务权限不能为空');
				return;
			}
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
			//校验组名是否存在
			var allrecords = sgmstore.getRange(0,sgmstore.getCount());
			var count =0;
			Ext.each(allrecords, function(r) {
				if(m[i].get("INAME").trim()==r.data.INAME.trim()){
					count++;
				}
				if(count>1){
					execflag=1;
					Ext.Msg.show({
					     title:'提示',
					     msg: " 组名称:'"+m[i].get("INAME")+"'重复",
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.WARNING
					});
					return;
				}
			});
		}
		jsonData = jsonData + "]";

		if(execflag==1){
			return;
		}
		Ext.Ajax.request({
			url : 'saveServiceGroup.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					sgmstore.modified = [];
					sgmstore.reload();
					Ext.Msg.alert('提示', message);
				} else {
					Ext.Msg.alert('提示', message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}
	/**
	 * 删除
	 */
	function delInfo() {
		var record = groupName_grid.getSelectionModel().getSelection();		
		if (record.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条要删除的记录！');
			return;
		} else {
			var delstate="确定要删除";
			var jsonArray = [];		
			var count=0;
			Ext.each(record, function(item) {
				var iid = item.data.IID;
				var istatus=item.get("ISTATUS");				
				if(istatus==1){
					count++							
				}
				if(istatus==2){
					delstate="已发起,删除组将删除组内已经发起的所有服务,确认删除？";
				}
			});
			if(count>0){
				Ext.Msg.alert('提示', '所选服务组有处于审核中的，请重新选择！');
				return;
			}
			// 需要支持批量删除
			Ext.Msg.confirm('系统提示', delstate, function(btn) {
				if (btn == 'yes') {
					Ext.each(record, function(item) {
						var iid = item.data.IID;
						  if(iid==0){
		                  	sgmstore.remove(record);
		                  }else{
		                	  jsonArray.push(iid);
		                  }
					});	
					
					if (jsonArray.length > 0) {
						Ext.Ajax.request({
							method : "POST",
							url : "delServiceGroup.do",
							params : {
								deleteIds : jsonArray.join(',')
							},
							success : function(response) {
								var text = Ext.JSON.decode(response.responseText);
								if (text.success) {
									Ext.Msg.alert('提示', '删除成功！');
									sgmstore.load();
								} else {
									Ext.Msg.alert('提示', text.msg);
								}
							},
							failure : function(form, action) {
								switch (action.failureType) {
								case Ext.form.action.Action.CLIENT_INVALID:
									Ext.Msg.alert('提示', '连接异常！');
									break;
								case Ext.form.action.Action.SERVER_INVALID:
									Ext.Msg.alert('提示', action.result.msg);
								}
							}
						});
					}					
				}
			});
			
		}
		
	}
    
    //用户信息增加
    function addServiceForGroup(){
    	var selDatas = groupName_grid.getSelectionModel().getSelection();
		if(selDatas==""){
			Ext.Msg.alert('消息提示','请选择已保存的组,再进行此操作！');
			return;
		}
		var m = sgmstore.getModifiedRecords();
		if(m.length>0){
			Ext.Msg.alert('消息提示','请先保存组信息,再进行此操作！');
			return;
		}
		if(win){
			win.close();
		}
		if(selDatas.length!=1){
				Ext.Msg.alert('消息提示','请选择一条组信息再进行此操作！');
				return;
		}
		var groupId = selDatas[0].data.IID;
		 var win = Ext.create('Ext.window.Window', {
		  		title : '服务信息',
		  		autoScroll : true,
		  		modal : true,
		  		resizable : false,
		  		closeAction : 'destroy',
		  		width : contentPanel.getWidth()-500,
		  		height : contentPanel.getHeight()-100,
		  		loader : {
			 			url : "addServiceForGroup.do",
			 			params : {
			 				groupId : groupId
	  					},
	  					autoLoad: true,
	  					autoDestroy : true,
	  					scripts : true
		  			}
		  	}).show();
		 win.on("close",function(){
			 scriptservice_store.reload();
			 sgmstore.reload();
		 });
    }
    //信息删除
    function deleteServiceForGroup(){
    	Ext.Msg.confirm('确认提示','您确定要进行此操作吗？',function(bn){
    		if(bn=='yes'){
    			var selDatas = scriptservice_grid.getSelectionModel().getSelection();
    			if(selDatas.length==0){
    				Ext.Msg.alert('消息提示','请选择记录进行操作！');
    				return;
    			}
    			var iids = [];
    			Ext.Array.each(selDatas, function(record) {
                    var iid = record.get('flowId');
                    // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                    if(iid==0){
                    	sgmstore.remove(record);
                    }else{
                    	iids.push(iid);
                    }
                });
    			Ext.Ajax.request({
    			    url: 'deleteUserGroupForBind.do',
    			    params: {
    			    	iids: iids,
    			    	groupId:tmpgroupId
    			    },
    			    success: function(response){
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
							if (success) {
								scriptservice_store.reload();
	    			        	//sgmstore.reload();
								Ext.Msg.alert('提示', message);
							} else {
								Ext.Msg.alert('提示', message);
							}
						},
    			    failure: function(result, request) {
    			    	secureFilterRs(result,"请求返回失败！",request);
    			    }
    			});
    		}
    	});
    }
    
    function queryWhere() {
    	sgmstore.load();
		pageBar.moveFirst();
	}
    function resetWhere ()
	{
    	sname.setValue ('');
	}
    
    function execServiceForSign(){
    	Ext.Msg.confirm('确认提示','您确定要进行发起操作吗？',function(bn){
    		if(bn=='yes'){
    			var selDatas = scriptservice_grid.getSelectionModel().getSelection();
    			if(selDatas.length==0 ||selDatas.length>1){
    				Ext.Msg.alert('消息提示','请选择且只能选择一条记录进行发起操作！');
    				return;
    			}
    			var iids = [];
    			var groupId=-1;
    			Ext.Array.each(selDatas, function(record) {
    	            var iid = record.get('iid');
    	            // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
    	            iids.push(iid);
    	        });
    				execServiceForExec(iids,groupId,0);
    			
    		}
    	});
    }

    function execServiceForGroup(){
	Ext.Msg.confirm('确认提示','您确定要进行发起操作吗？',function(bn){
	if(bn=='yes'){
		var selDatas = groupName_grid.getSelectionModel().getSelection();
		if(selDatas.length==0 ||selDatas.length>1){
			Ext.Msg.alert('消息提示','请选择且只能选择一条记录进行发起操作！');
			return;
		}
		var iids = [];
		var groupId=-1;
		Ext.Array.each(selDatas, function(record) {
            var iid = record.get('IID');
            groupId=iid;
            // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
            if(iid==0){
            	Ext.Msg.alert('消息提示','请先保存后在进行发起操作！');
            	return;
            }else{
            	iids.push(iid);
            }
        });
			execServiceForExec(iids,groupId,1);
		}
	  });
	}
    
    function execServiceForExec(iids,groupId,pubflag){
		Ext.Ajax.request({
			url : 'serviceGroupHasVersion.do',
			method : 'POST',
			params : {
				ids : iids,
				groupId:groupId
			},
			success : function(response, opts) {
			var hasVersion = Ext.decode(response.responseText).hasVersion;
			if (hasVersion == 1 && flag == true) {
				Ext.Msg.alert('提示', "该服务组已经发起过！");
				return;
			} else {
			Ext.Ajax.request({
				url : 'serviceGroupStatus.do',
				method : 'POST',
				params : {
					ids : iids,
					groupId:groupId
				},
				success : function(response, opts) {
					var status = Ext.decode(response.responseText).status;
					if (status == 1) {
						Ext.Msg.alert('提示', "该申请正处于审核中！");
						return;
					}else if (status == 2 && flag == true) {
						Ext.Msg.alert('提示', "该申请已发起，不能重复发起！");
						return;
					} else {
						var requiredCfg = '<span style="color:red;font-weight:bold" data-qtip="按输入周期执行">  *</span>';
						Ext.define('AuditorModel', {
							extend : 'Ext.data.Model',
							fields : [ {
								name : 'loginName',
								type : 'string'
							}, {
								name : 'fullName',
								type : 'string'
							} ]
						});

						var auditorStore_sm = Ext.create('Ext.data.Store', {
							autoLoad : true,
							model : 'AuditorModel',
							proxy : {
								type : 'ajax',
								url : 'getExecAuditorList.do?scriptLevel=1',//getPublishAuditorList.do
								reader : {
									type : 'json',
									root : 'dataList'
								}
							}
						});
						
						var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
							editable : false,
							fieldLabel : "审核人",
							labelWidth : 60,
							store : auditorStore_sm,
							queryMode : 'local',
							columnWidth : .98,
							margin : '10 0 0 0',
							displayField : 'fullName',
							valueField : 'loginName'
						});
						
						var planTime_sm = Ext.create('Go.form.field.DateTime', {
							fieldLabel : '计划时间',
							format : 'Y-m-d H:i:s',
//												hidden : true,
							labelWidth : 70,
							columnWidth : .5,
							minValue: new Date(),
							margin : '10 0 0 0'
						});
						var versionAresource = Ext.create('Ext.form.DisplayField', {
							fieldLabel : '资源配置',
							labelWidth : 70,
							columnWidth : .6,
							margin : '10 0 10 0', 
							value:'<span style=\'color: red;\'>点我进行资源组与资源版本配置</span>',//<img src="images/result_report.png" align="left" width="80px" height="30px"></img>
							listeners: {
				        	    render: function(p) {
				            	    p.getEl().on('click', function(p){
				            	    	$('#scripttempService_dev').each(function() {
					                	    $(this).remove();
					                	});
				            	    	Ext.create('Ext.window.Window', {
				            				title : '版本与资源组配置',
				            				modal : true,
				            				closeAction : 'destroy',
				            				constrain : true,
				            				autoScroll : true,
				            				width : 860,
				            				height : 600,
				            				draggable : false,// 禁止拖动
				            				resizable : false,// 禁止缩放
				            				layout : 'fit',
				            				loader : {
				            					url : 'goVersionAndResource.do',
				            					params : {
				            						serviceId : groupId,
				            						serviceGroupId:groupId
				            					},
				            					autoLoad : true,
				            					scripts : true
				            				}
				            			}).show();
				            	    });
				            	}}
						});

						var cycleStore = Ext.create('Ext.data.Store', {
							fields : [ 'name', 'value' ],
							data : [  {
								"name" : "0",
								"value" : "按计划执行一次"
							},{
								"name" : "1",
								"value" : "间隔x日"
							}, {
								"name" : "2",
								"value" : "间隔x小时"
							}, {
								"name" : "3",
								"value" : "间隔x分钟"
							} ]
						});

						 var planTime_MM = Ext.create('Ext.form.field.ComboBox', {
								fieldLabel : '周期类型',
								editable : false,
								name : 'MM',
								padding : '0 5 0 0',
								matchFieldWidth:false,// 此处要有
								labelWidth :65,
								columnWidth : .35,
								store: {
									 	fields: ['value'],
									    data : [
									    	{"value":"按计划执行一次"},
									        {"value":"间隔x日"},
									        {"value":"间隔x小时"},
									        {"value":"间隔x分钟"}
									    ]
								 },
								 displayField:'value',
								 value:"间隔x日",
								 listeners:{
									 select : function(nf, newv, oldv) {
										},
									 change : function(nf, newv, oldv) {
										 if(newv=='间隔x日'){
											 planTime_DD.setValue('');
											 planTime_HH.setValue('');
											 planTime_mi.setValue('');
											 planTime_DD.show();
											 planTime_HH.show();
											 planTime_mi.show();
										 }else if(newv=='间隔x小时'){
											 planTime_DD.setValue('');
											 planTime_HH.setValue('');
											 planTime_mi.setValue('');
											 planTime_DD.hide();
											 planTime_HH.show();
											 planTime_mi.show();
										 }else if(newv=='间隔x分钟'){
											 planTime_DD.setValue('');
											 planTime_HH.setValue('');
											 planTime_mi.setValue('');
											 planTime_DD.hide();
											 planTime_HH.hide();
											 planTime_mi.show();
										 }else if(newv=='按计划执行一次'){
											 planTime_DD.setValue('');
											 planTime_HH.setValue('');
											 planTime_mi.setValue('');
											 planTime_DD.hide();
											 planTime_HH.hide();
											 planTime_mi.hide();
										 }
									 }
								 }
							 });
						var planTime_DD = Ext.create('Ext.form.NumberField', {
							fieldLabel : '天数',
							editable : true,
							name : 'DD',
							padding : '0 5 0 0',
							labelWidth : 40,
							columnWidth : .20,
							listeners : {
								select : function(nf, newv, oldv) {
								},
								change : function(nf, newv, oldv) {
									if (null == newv || newv == '' || trim(newv) == '') {
										planTime_DD.setValue("")
									} else {
										if (/^[0-9]([0-9])*$/.test(newv)) {
											if (newv > 31) {
												Ext.Msg.alert("提示", "天数值需在1~31之间!");
												planTime_DD.setValue(oldv)
											}
											return true;
										} else {
											Ext.Msg.alert("提示", "天数窗口只能正整数");
											planTime_DD.setValue(oldv)
											return false;
										}
									}
								}
							}
						});
						var planTime_HH = Ext.create('Ext.form.NumberField', {
							fieldLabel : '小时',
							editable : true,
							name : 'HH',
							padding : '0 5 0 0',
							labelWidth : 40,
							columnWidth : .20,
							listeners : {
								select : function(nf, newv, oldv) {
								},
								change : function(nf, newv, oldv) {
									if (null == newv || newv == '' || trim(newv) == '') {
										planTime_HH.setValue("")
									} else {
										if (/^[0-9]([0-9])*$/.test(newv)) {
											if (newv > 23) {
												Ext.Msg.alert("提示", "小时值需在1~23之间!");
												planTime_HH.setValue(oldv)
											}
											return true;
										} else {
											Ext.Msg.alert("提示", "小时窗口只能正整数");
											planTime_HH.setValue(oldv)
											return false;
										}
									}
								}
							}
						});
						var planTime_mi = Ext.create('Ext.form.NumberField', {
							fieldLabel : '分钟',
							editable : true,
							name : 'mi',
							padding : '0 5 0 0',
							labelWidth : 40,
							columnWidth : .20,
							listeners : {
								select : function(nf, newv, oldv) {
								},
								change : function(nf, newv, oldv) {
									if (null == newv || newv == '' || trim(newv) == '') {
										planTime_mi.setValue("")
									} else {
										if (/^[0-9]([0-9])*$/.test(newv)) {
											if (newv > 59) {
												Ext.Msg.alert("提示", "分钟值需在1~59之间!");
												planTime_mi.setValue(oldv)
											}
											return true;
										} else {
											Ext.Msg.alert("提示", "分钟窗口只能正整数");
											planTime_mi.setValue(oldv)
											return false;
										}
									}
								}
							}
						});
							var newv=planTime_MM.getValue();
							if(newv=='间隔x日'){
								 planTime_DD.show();
								 planTime_HH.show();
								 planTime_mi.show();
							 }else if(newv=='间隔x小时'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.show();
								 planTime_mi.show();
							 }else if(newv=='间隔x分钟'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.hide();
								 planTime_mi.show();
							 }else if(newv=='按计划执行一次'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.hide();
								 planTime_mi.hide();
							 }
						var cfg_Display = Ext.create('Ext.form.DisplayField', {
							name : 'display',
							fieldLabel : '周期配置',
							afterLabelTextTpl : requiredCfg,
							labelWidth : 350,
							columnWidth : .9
						});
						var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
							name : 'pubdesc',
							fieldLabel : '详细说明',
							emptyText : '',
							labelWidth : 65,
							margin : '10 0 0 0',
							height : 80,
							columnWidth : .98,
							autoScroll : true
						});
						var taskName = new Ext.form.TextField({
							name : 'taskName',
							fieldLabel : '任务名称',
							displayField : 'taskName',
							emptyText : '',
							labelWidth : 65,
							columnWidth : .5
						});
						var formPanel = Ext.create('Ext.form.Panel', {
							width : 600,
							layout : 'anchor',
							bodyCls : 'x-docked-noborder-top',
							buttonAlign : 'center',
							border : false,
							items : [ {
								anchor : '98%',
								padding : '5 0 5 5',
								border : false,
								items : [ {
									layout : 'column',
									border : false,
									padding : '5 10 5 5',
									items : [ planTime_sm]
								},{
									layout : 'column',
									border : false,
									padding : '5 10 5 5',
									items : [ versionAresource ]
								},{
									layout : 'column',
									border : false,
									padding : '5 10 5 5',
									items : [ taskName ]
								},
//								{
//									layout : 'column',
//									border : false,
//									padding : '5 10 5 5',
//									items : [ cfg_Display ]
//								}, 
								{
									layout : 'column',
									border : false,
									padding : '5 10 5 5',
									items : [ planTime_MM, planTime_DD, planTime_HH, planTime_mi ]
								},
								{
									layout : 'column',
									border : false,padding : '5 10 5 5',
									items : [ auditorComBox_sm ]
								}, {
									layout : 'column',
									border : false,padding : '5 10 5 5',
									items : [ pubDesc_sm ]
								} ]
							} ]
						});	
						var	publishAuditingSMWin = Ext.create('Ext.window.Window', {
								title : '确认审核信息',
								closable : true,
//								closeAction : 'close',
								resizable : false,
								closeAction : 'destory',
								modal : true,
								width : 600,
								height : 500,
								minWidth : 350,
								layout : {
									type : 'border',
									padding : 5
								},
								items : [ formPanel ],
								dockedItems : [ {
									xtype : 'toolbar',
									border : false,
									dock : 'bottom',
									layout : {
										pack : 'center'
									},
									items : [ {
										xtype : "button",
										cls : 'Common_Btn',
										text : "确定",
										handler : function() {
											var planTime = planTime_sm.getRawValue();
											var planTime_MM_1 = planTime_MM.getValue();
											var planTime_DD_1 = planTime_DD.getValue();
											var planTime_HH_1 = planTime_HH.getValue();
											var planTime_mi_1 = planTime_mi.getValue();
											var scriptLevel = 100;
											var publishDesc = pubDesc_sm.getValue();
											var auditor = auditorComBox_sm.getValue();
											var task = taskName.getValue();
											if (!planTime) {
												Ext.Msg.alert('提示',"没有填写计划时间！");
												return;
											}
											if (!task) {
												Ext.Msg.alert('提示',"没有填写任务名称！");
												return;
											}
											if (!scriptLevel) {
												Ext.Msg.alert('提示', "没有选择风险级别！");
												return;
											}
											if (!publishDesc) {
												Ext.Msg.alert('提示', "没有填写详细说明！");
												return;
											}
											if (!auditor) {
												Ext.Msg.alert('提示', "没有选择审核人！");
												return;
											}
											if(planTime_MM_1=='间隔x日'){
												if (planTime_DD_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
													Ext.Msg.alert('提示',"天数必须填写！");
													return;
												}
											}else if(planTime_MM_1=='间隔x小时'){
												if (planTime_HH_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
													Ext.Msg.alert('提示',"小时必须填写！");
													return;
												}
											}else if(planTime_MM_1=='间隔x分钟'){
												if (planTime_mi_1==''||planTime_mi_1==null||planTime_mi_1=='null') {
													Ext.Msg.alert('提示',"分钟必须填写！");
													return;
												}
											}else if(planTime_MM_1=='按计划执行一次'){
											}else{
												Ext.Msg.alert('提示',"请选择周期类型！");
												return;
											}
											var sIds = new Array();
											sIds.push(groupId);
											Ext.Ajax.request({
												url : 'scriptPublishAuditingForGroup.do',
												method : 'POST',
												params : {
													sIds : sIds,
													planTime : planTime,
													scriptLevel : scriptLevel,
													publishDesc : publishDesc,
													auditor : auditor,
													flag : pubflag,
													planTimeType : planTime_MM_1,
													planTimeDD : planTime_DD_1,
													planTimeHH : planTime_HH_1,
													planTimeMM : planTime_mi_1,
													taskName:task,
													serviceType : 1,
													iids:iids
												// 0-来着个人脚本库
												},
												success : function(response, opts) {
													var success = Ext.decode(response.responseText).success;
													var message = Ext.decode(response.responseText).message;
													if (!success) {
														Ext.MessageBox.alert("提示", message);
													} else {
														Ext.MessageBox.alert("提示", "请求已经发送到审核人");
													}
													publishAuditingSMWin.close();
													this.up("window").close();
												},
												failure : function(result, request) {
													secureFilterRs(result, "操作失败！");
													publishAuditingSMWin.close();
													this.up("window").close();
												}
											});
										}
									}, {
										xtype : "button",
										cls : 'Common_Btn',
										text : "取消",
										handler : function() {
											this.up("window").close();
										}
									} ]
								}]
							}).show();
						}
					}, failure : function(result, request) {
						secureFilterRs(result, "操作失败！");
						return;
					}
					});
			  		}
				},failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
					return;
				}
			});
    }
    String.prototype.trim = function ()
    {
    	return this.replace (/(^\s*)|(\s*$)/g, "");
    }
});
