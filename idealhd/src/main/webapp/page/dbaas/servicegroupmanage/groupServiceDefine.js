var scriptServiceReleaseStore;
Ext.onReady(function() {

// 清理主面板的各种监听时间
//	destroyRubbish();
	var itemsPerPage = 30;
	var publishAuditingSMWin;
	var auditing_form_sm;
	var auditorStore_sm;
	var auditorComBox_sm;
	var pubDesc_sm;	
	var choice=-1;
	var sysID;
	var busID;

	
	  var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';	  
	
	var scriptStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"-1", "name":"草稿"},
			{"id":"1", "name":"已上线"},
			{"id":"3", "name":"共享"},
			{"id":"2", "name":"审核中"}
		]
	});
	var serviceTypeStore = Ext.create('Ext.data.Store', {
		fields : [ 'value', 'text' ],
		data : [ {
			"value" : "-1",
			"text" : "全部"
		}, {
			"value" : "0",
			"text" : "应用"
		},
		 {
			 "value": "1",
			 "text": "采集"
		}]
	});
	var serviceTypeParam = Ext.create('Ext.form.field.ComboBox', {
		name : 'serviceType',
		padding : '0 5 0 0',
		labelWidth : 65,
		columnWidth : .5,
		queryMode : 'local',
		fieldLabel : '服务类型',
		displayField : 'text',
		valueField : 'value',
		hidden:db_serviceType,
		editable : false,
		emptyText : '--请选择服务类型--',
		store : serviceTypeStore,
		value :filter_serviceType,
		listeners : {
		afterRender : function(combo) {
			combo.setValue(filter_serviceType);
			}
		}
	});	
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		labelWidth : 65,
//		padding : '5',
		width :'20%',
        labelAlign : 'right',
        value: filter_serviceName,
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});
	var serviceId = new Ext.form.TextField({
		name : 'serviceId',
		fieldLabel : '服务号',
		emptyText : '--请输入服务号--',
		labelWidth : 65,
//		padding : '5',
		width :'20%',
		labelAlign : 'right',
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					pageBar.moveFirst();
				}
			}
		}
	});
 	
	var	searchItems = Ext.create('Ext.ux.ideal.form.Panel', {
		 	region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	bodyCls : 'x-docked-noborder-top',
	    	border : false,
	    	 iqueryFun : function(){
		    	queryData();
		    },
	    	dockedItems : [{
				xtype : 'toolbar',
				border : false,
				baseCls:'customize_gray_back',
				dock : 'top',
				items: [serviceId,sName,serviceTypeParam,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() { 
					queryData();
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
						filter = {};
					}
				},'->', {
	                text: '增加',
	                cls: 'Common_Btn',
	                handler:function(){
	                	saveDatabase(0)//参数为0时表示添加
	                },
	            },
	            {
	            	text:'编辑',
	                cls: 'Common_Btn',
	                handler:function(){
	                	saveDatabase(1)//参数为1时表示修改
	                }
	            },{
					text : '发布',
					cls : 'Common_Btn',
					handler : function(){
						var seledCnt = selModel.getCount();
						if(seledCnt != 1){
							Ext.MessageBox.alert("提示", "请选择要发布的记录，且每次只能选择一条！");
							return ;
						}
						var ss = selModel.getSelection();
						var version = ss[0].data.version; 
						
						var iid =  ss[0].data.iid; // 其实是requestID
						var bussId = ss[0].data.bussId;
						var bussTypeId  =ss[0].data.bussTypeId;
						var serviceType=ss[0].data.serviceType;
						var serviceName = ss[0].data.serviceName;
						var scriptType = ss[0].data.scriptType;
						var dbType=ss[0].data.dbType;	
						var status = ss[0].data.status;
						var hasVersion = 0;
						if(db_projectFlag==1){//===================光大开关
							if(version==1){
								version=true;
							}else{
								version=false;
							}
						}
						if(version) {			
							hasVersion = 1; // 最新版本有 版本号
						}
						publishScript(iid, 0,0,0,hasVersion, status,bussId,bussTypeId,serviceType,serviceName,scriptType,dbType);
						
					}
				}, '-',
				{
					text: '删除',
					itemId: 'delete',
					cls : 'Common_Btn',
					handler: deleteServiceRelease
				}]
			}]
		});
	 
	 Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'uuid'         ,type : 'String'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'},
		    {name : 'dbType' ,type : 'string'},
		    {name : 'ssuer' ,type : 'string'},
		    {name : 'startType' ,type : 'string'},
		    {name : 'serviceType' ,type : 'string'},
		    {name : 'isExam' ,type : 'int'},
		    {name : 'serviceId' ,type : 'string'},
		    {name : 'funcdesc' ,type : 'string'}
	    ]
	});
	
	 scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : itemsPerPage,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'getGroupServiceList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	scriptServiceReleaseStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		serviceId:serviceId.getValue().trim(),
		    		sname:sName.getValue().trim(),
		    		serviceType:serviceTypeParam.getValue()
		    };
		    
		    Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
	    });

	var scriptServiceReleaseColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 70
		}, 
		{
		    text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
			text : '服务号',
			dataIndex : 'serviceId',
			width : 120,
//			flex:1,
			editor: {
				allowBlank: false
			},
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},
		{
			text : '服务名称',
		    dataIndex : 'serviceName',
		    width : 160,
		    editor: {
	            allowBlank: false
	        },
		    renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},
		{
			text : '脚本名称',
			dataIndex : 'scriptName',
			width : 160,
			editor: {
		        allowBlank: false
		    },
		}, 
		{
		    text : '一级分类',
		    dataIndex :'sysName',
		    flex:1,
		    minWidth : 100
		}, 
		{
			text : '二级分类',
			dataIndex :'bussName',
			flex:1,
			minWidth : 100
		},
		{
		    text : '脚本类型',
		    dataIndex : 'scriptType',
		    width : 80,
//		    flex:1,
		    renderer:function(value,p,record,rowIndex){
		    	var backValue = "";
				if (value == "sh") {
					backValue = "shell";
				} else if (value == "perl") {
					backValue = "perl";
				} else if (value == "py") {
					backValue = "python";
				} else if (value == "bat") {
					backValue = "bat";
				} else if (value == "sql") {
					backValue = "sql";
				}
				return backValue;
		    }
		},  
		{
			text : '适用平台',
			dataIndex : 'platForm',
			flex:1,
			width : 100
		},{		
			text : '数据库类型',
		    dataIndex : 'dbType',
		    minWidth : 85,
		    flex:1,
		    renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},{
		    dataIndex : 'version',
		    hidden:true,
		    width : 80
		},{
			text : '服务类型',
		    dataIndex : 'serviceType',
		    width : 80,
//		    flex:1,
		    renderer : function(value, metaData, record) {
				var backValue = "";
				if (value == '0') {
					backValue = '应用';
				} else if (value == '1') {
					backValue = '采集';
				} else if (value == '100') {
					backValue = '组合';
				} 
				return backValue;
			}
		},{
			text : '发布人',
		    dataIndex : 'ssuer',
		    flex:1,
		    width : 150,
		    minWidth: 100,
		},

		{
			text : '服务状态',
			dataIndex : 'status',
			width : 80,
//			flex:1,
			renderer:function(value,p,record,rowIndex){
		    	if(value==-1) {
		    		return '<font color="#F01024">草稿</font>';
		    	} else if (value==1) {
		    		return '<font color="#0CBF47">已上线</font>';
		    	} else if (value==2) {
		    		return '<font color="#FFA602">审核中</font>';
		    	} else if (value==3) {
		    		return '<font color="#13B1F5">已共享</font>';
		    	} else if (value==9) {
		    		return '<font color="">已共享未发布</font>';
		    	} else {
		    		return '<font color="#CCCCCC">未知</font>';
		    	}
		    }
		},
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			align:'left',
//			flex:1,
			width:210,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid'); // 其实是requestID
				var serviceName = record.get('serviceName');
				var scriptType = record.get('scriptType');
				var bussId = record.get('bussId');
				var bussTypeId  =record.get('bussTypeId');
				var version = record.get('version');
				var status = record.get('status');
				var uuid = record.get('uuid');
				var hasVersion = 0;
				if(version) {
					hasVersion = 1; // 最新版本有 版本号
				}					
				var serviceType=record.get("serviceType");
				var scriptName=record.get("scriptName");
				//使用平台
				var platForm=record.get("platForm");
				//发起审核
				var isExam=record.get("isExam");
				//数据库类型
				var dbType=record.get("dbType");
				var retHtml='<a href="javascript:void(0)" onclick="addServiceForGroup('+iid+","+bussId+","+bussTypeId+","+serviceType+",\'"+scriptType+"\',\'"+dbType+"\',"+status+')">'+
			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"></img>&nbsp;绑定服务'+
	   			   	'</a>'+
					  '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
	   				retHtml=retHtml+ '<span class="switch_span">'+
	   			   	'<a href="javascript:void(0)" onclick="searchVersion('+iid+',\''+uuid+'\')">'+
	   			   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_version"></img>&nbsp;查看版本'+
	   			   	'</a>'+
					  '</span>' +'&nbsp;&nbsp;&nbsp;&nbsp;';					  
				return retHtml;
			}
		}
	];
	  var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: scriptServiceReleaseStore,
		  	dock: 'bottom',
			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			displayInfo: true,
			border:false,
			displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
			emptyMsg: "没有记录"
		  });
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	var selModel=Ext.create('Ext.selection.CheckboxModel', {
		id:'selModelSS',
		checkOnly: true
	});	
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		overflowY:'hidden',
	    store : scriptServiceReleaseStore,
	    selModel : selModel,
	    plugins: [ cellEditing ],
	    border:true,
	    bbar : pageBar,
	    padding : panel_margin,
	    columnLines : true,
	    cls:'customize_panel_back',
	    columns : scriptServiceReleaseColumns
	});
	
	 var mainPanel = Ext.create('Ext.panel.Panel',{
		 	renderTo : "groupServiceDefine_grid_area",
	        width : contentPanel.getWidth(),
		    height :contentPanel.getHeight()-modelHeigth,
	        bodyPadding : grid_margin,
		    border : true,
	        layout: 'border',
	        bodyCls:'service_platform_bodybg',
	        cls:'customize_panel_back',
	        items : [searchItems,scriptServiceReleaseGrid]
	});
	 
	 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function (){
			mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
		});
		
  
	/* 解决IE下trim问题 */
	String.prototype.trim=function(){
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(mainPanel);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});	
	
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    bussData.on('load', function(store, options) {
    	if(sysID) {
    		bussCb.setValue(sysID);
    		bussTypeData.load({
    			params: {
    				fk: sysID
    			}
    		});
    	}
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    
    bussTypeData.on('load', function(store, options) {
    	if(busID) {
    		bussTypeCb.setValue(busID);
    	}
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
            	if(this.value!=''&&this.value!=null){
                    bussTypeCb.clearValue();
                    bussTypeCb.applyEmptyText();
                    bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                    bussTypeData.load({
                        params: {
                            fk: this.value
                        }
                    });
            	}    
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussTypeId',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });
	
	 var serName = new Ext.form.TextField({
	    	name: 'serviceName',
	    	fieldLabel: '服务名称',
	    	afterLabelTextTpl: required,
	    	displayField: 'serviceName',
	    	emptyText: '',
	    	labelWidth: 70,
	    	padding: '0 5 0 0',
	    	columnWidth: .5
	    });
	    var scName = new Ext.form.TextField({
	        name: 'scriptName',
	        fieldLabel: '脚本名称',
	        afterLabelTextTpl: required,
	        displayField: 'scriptName',
	        emptyText: '',
	        labelWidth: 70,
	        padding: '0 5 0 0',
	        columnWidth: .5,
	        regex: /^[0-9a-zA-Z_]{1,}$/,
			regexText:'只允许输入数字、字母、下划线' //^\w+$/gi,  
	    });	    
	   var usePlantFormStore = Ext.create('Ext.data.JsonStore', {
			fields: ['INAME', 'ICODEVALUE'],
			//autoDestroy : true,
			autoLoad : true,
			proxy : {
				type : 'ajax',
				url : 'getScriptPlatformCode.do',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});
	    
		usePlantFormStore.on('beforeload', function(store, options) {
				var new_params = {
					gdSwitch :  1
				};
				Ext.apply(usePlantFormStore.proxy.extraParams, new_params);
		});
	    
	    
	    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
	        name: 'platForm',
	        padding: '0 5 0 0',
	        labelWidth: 70,
	        columnWidth: .5,
	        queryMode: 'local',
	        fieldLabel: '适用平台',
//	        afterLabelTextTpl: required,
	        displayField: 'INAME',
	        valueField: 'ICODEVALUE',
	        multiSelect: true,//启用多选
	        editable: false,
	        emptyText: '--请选择平台--',
	        store: usePlantFormStore
	    });
	    
	      // 添加下拉显示条数菜单选中事件
		usePlantForm.on("select", function(comboBox,records,eOpts) {
				if(records.length>1){
					var windowsFlag = false;
					var linuxFlag = false;
					Ext.each(records, function(record) {
						var iname1 = record.data.INAME;
						if(iname1 == 'Windows'){
							windowsFlag = true;
						}
						if(iname1 != 'Windows'){
							linuxFlag = true;
						}
						if(linuxFlag && windowsFlag){
							 Ext.Msg.alert('提示', 'Windows平台和非Windows平台不能同时选择！');
							 usePlantForm.clearValue();
							 return;
						}
					});
				}
				 var scriptTypeValue =scriptType.getValue() ; 
				 if(scriptTypeValue==''){
					 Ext.Msg.alert('提示', '请先选择脚本类型');
					 	usePlantForm.setValue();
					 	return;
				 }
				 if(scriptTypeValue =='bat' && comboBox.getValue() != 'Windows'){
				 	Ext.Msg.alert('提示', 'bat脚本不能选择非Windows平台');
				 	usePlantForm.setValue();
				 	return;
				 }
				 if((scriptTypeValue =='sh' || scriptTypeValue =='perl' ) && comboBox.getValue() == 'Windows'){
				 	Ext.Msg.alert('提示', '只允许 bat、python、sql脚本才能选择Windows平台');
				 	usePlantForm.setValue();
				 	return;
				 }  
		});

	    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
	    	name: 'funcdesc',
	    	fieldLabel: '功能说明',
	    	afterLabelTextTpl: required,
	    	displayField: 'funcdesc',
	    	emptyText: '请输入功能说明...',
	    	labelWidth: 70,
	    	height: 136,
	        columnWidth: .99,
	    	autoScroll: true
	    });
	    Ext.define('dbModel', {
	        extend : 'Ext.data.Model',
	        fields : [ {
	            name : 'id',
	            type : 'string'
	        }, {
	            name : 'name',
	            type : 'string'
	        } ]
	    });
	    var dbStore = Ext.create('Ext.data.Store', {
	        autoDestroy : true,
	        autoLoad : true,
	        model : 'dbModel',
	        proxy : {
	            type : 'ajax',
	            url : 'getDatabaseType.do',
	            reader : {
	                type : 'json',
	                root : 'dataList'
	            }
	        }
	    });
	 // 数据库类型		
		var dbType = Ext.create('Ext.form.field.ComboBox', {
			name : 'dbType',
			labelWidth : 70,
			columnWidth : .5,
			queryMode : 'local',
			afterLabelTextTpl : required,
			fieldLabel : '数据库类型',
			padding : '0 5 0 0',
			editable : false,
			displayField : 'name',
			valueField : 'id',
			emptyText : '--请选择数据库类型--',
			store : dbStore
//			new Ext.data.SimpleStore({
//				fields : [ 'id', 'text' ],
//				data : [ [ '1', 'ORACLE' ], [ '2', 'DB2' ], [ '3', 'MYSQL' ] ]
//			})
		});

		// 服务类型
		var serviceType = Ext.create('Ext.form.field.ComboBox', {
			name : 'serviceType',
			labelWidth : 70,
			columnWidth : .5,
			queryMode : 'local',
			afterLabelTextTpl : required,
			fieldLabel : '服务类型',
			padding : '0 5 0 0',
			editable : false,
			displayField : 'text',
			valueField : 'value',
			value : '1',
			emptyText : '--请选择服务类型--',
			store : new Ext.data.SimpleStore({
				fields : [ 'value', 'text' ],
				data : [ [ '0', '应用' ], [ '1', '采集' ] ]
			})
		});
		// 发起审核
		var isExam = Ext.create('Ext.form.field.ComboBox', {
			name : 'isExam',
			labelWidth : 70,
			columnWidth : .5,
			queryMode : 'local',
			afterLabelTextTpl : required,
			fieldLabel : '发起审核',
			padding : '0 5 0 0',
			editable : false,
			displayField : 'text',
			valueField : 'value',
			value : 0,
			emptyText : '--请选择--',
			store: Ext.create('Ext.data.Store', {
				fields : [ 'value', 'text' ],
                data: [{
                	text: "是",
                    value: 1
                },
                {
                	text: "否",
            		value: 0
            	}]
            })

		});

			var scriptType = Ext.create('Ext.form.field.ComboBox', {
				name : 'scriptType',
				labelWidth : 70,
				columnWidth : .5,
				queryMode : 'local',
				afterLabelTextTpl : required,
				fieldLabel : '脚本类型',
				padding : '0 5 0 0',
				editable : false,
				displayField : 'text',
				valueField : 'value',
				value : '',
				emptyText : '--请选择--',
				store : new Ext.data.SimpleStore({
					fields : [ 'value', 'text' ],
					data : [ [ 'sh', 'shell' ], [ 'perl', 'perl' ],[ 'py', 'python' ], [ 'bat', 'bat' ], [ 'sql', 'sql' ]]
				})
			});
	var formItems = [  {
		border : false,
		layout : 'column',
		items :[{
            	xtype:'hiddenfield',
            	name:'iid'
             }]
    },{
		border : false,
		layout : 'column',
		margin : '5',
		items : [ serName, scName ]
	}, {
		border : false,
		layout : 'column',
		margin : '5',
		items : [ bussCb, bussTypeCb ]
	}, {
		border : false,
		layout : 'column',
		margin : '5',
		items : [ dbType, serviceType ]
	}, {
		layout : 'column',
		border : false,
		margin : '5',
		items : [scriptType , isExam ]
	}, {
		layout : 'column',
		border : false,
		margin : '5',
		items : [  usePlantForm]
	},  {
		layout : 'column',
		border : false,
		margin : '5',
		items : [ funcDescInWin ]
	} ];

	
	  function saveDatabase(c){
		  choice=c;
	    	if(choice==0){
	    		showEditForm();
	    	}else if(choice==1){
	    		var record = scriptServiceReleaseGrid.getSelectionModel().getSelection();    		
	        	if(record.length==0){
	        		Ext.Msg.alert('提示', '请选择一条要编辑的记录！');
	        		return;
	        	}else if(record.length>1){
	        		Ext.Msg.alert('提示', '只能选择一条要编辑的记录！');
	        		return;
	        	} 
	        	sysID = record[0].data.bussId;
	            busID = record[0].data.bussTypeId;
	            if(record[0].data.status==2){
		            Ext.Msg.alert('提示', '该服务正在审核中，不可编辑！');
		            return;
	            }
	    		bussData.load();
	        	showEditForm(record[0]);          	
	    	}
	    	
	    }	
	    //编辑时打开一个记录窗口
	    function showEditForm(record) {
	    	var SRTeditWindow;
	        if (null==SRTeditWindow ) {
	            var form = Ext.widget('form', {
	                border: false,
	                bodyPadding: 3,
	                //baseCls:'customize_gray_back',
	                autoScroll: true,
	                items: formItems,	                
	                dockedItems : [{
						xtype : 'toolbar',
						border : false,
						baseCls:'customize_gray_back',
						dock : 'bottom',
						items : ['->',{
							cls : 'Common_Btn',
							textAlign : 'center',
							text: '取消',
		                    handler: function() {                   	
		                        this.up('form').getForm().reset();
		                        this.up('window').close();
		                        SRTeditWindow=null;
		                    }
		                }, {
		                	cls : 'Common_Btn',
		    				textAlign : 'center',
		                	text: '保存',
		                    handler: function() { 	  				
		    	  				var IID=0;
		                    	if(choice==1){
		                    		IID=form.getForm().findField('iid').getValue();
		                    	}		
		                    	
		                    	var sysId = bussCb.getValue();
		    					var bussTypeId = bussTypeCb.getValue();
		    					var serverName = serName.getValue();
		    					var scriptName1 = scName.getValue();
		    					var up = usePlantForm.getValue() || "";
		    					var scriptDesc1 = funcDescInWin.getValue();
		    					var dbType2 = dbType.getValue() || "";// 数据库类型
		    					var serviceType2 = serviceType.getValue() || "";// 服务类型
		    					var isExam2 = isExam.getValue() || "";// 发起审核
		    					if (serverName.trim() == '') {
		    						Ext.MessageBox.alert("提示", "服务名称不能为空!");
		    						return;
		    					}
		    					if (scriptName1.trim() == '') {
		    						Ext.MessageBox.alert("提示", "脚本名称不能为空!");
		    						return;
		    					}

		    					if (!bussTypeId) {
		    						Ext.MessageBox.alert("提示", "请选择操作类型!");
		    						return;
		    					}
		    					if (dbType2.trim() == '') {
		    						Ext.MessageBox.alert("提示", "数据库类型不能为空!");
		    						return;
		    					}
		    					if ( serviceType2.trim() == '') {
		    						Ext.MessageBox.alert("提示", "服务类型不能为空!");
		    						return;
		    					}

		    					if (up.length ==0) {
		    						Ext.MessageBox.alert("提示", "适用平台不能为空!");
		    						return;
		    					}
		    					if (scriptDesc1.trim() == '') {
		    						Ext.MessageBox.alert("提示", "功能说明不能为空!");
		    						return;
		    					}
		                       	 var jsonData = '{"iid":"'+IID+ '",';
		                     	 jsonData=jsonData+'"serviceName":"'+serName.getValue() + '",';
		                       	 jsonData=jsonData+'"sysName":"'+bussCb.getRawValue() + '",';
		                       	 jsonData=jsonData+'"bussName":"'+bussTypeCb.getRawValue() + '",';
		                       	 jsonData=jsonData+'"bussId":'+bussCb.getValue() + ',';
		                       	 jsonData=jsonData+'"bussTypeId":'+bussTypeCb.getValue() + ',';
		                       	 jsonData=jsonData+'"scriptType":'+scriptType.getValue() + ',';
		                       	 jsonData=jsonData+'"scriptName":"'+scName.getValue() + '",';
		                       	 jsonData=jsonData+'"platForm":"'+usePlantForm.getValue() + '",';
		                       	 jsonData=jsonData+'"content":"'+funcDescInWin.getValue() + '",';
		                       	 jsonData=jsonData+'"status":'+-1 + ',';
		                       	 jsonData=jsonData+'"dbType":'+dbType.getValue() + ',';
		                     	 jsonData=jsonData+'"serviceType":'+serviceType.getValue() + ',';
		                     	 jsonData=jsonData+'"isExam":'+isExam.getValue() + '}';  
		                       	 Ext.Msg.wait('处理中，请稍后...', '提示');  
		                       	 var url="saveGroupservice.do";
		                       	 if(choice==1){
		                       		url="editGroupservice.do"; 
		                       	 }
		                       	 Ext.Ajax.request({
		    							method:"POST",
		    							timeout:60000,
		    							url:url,
		    							params:{jsonData:jsonData},
		    							success:function(response){
		    								var text = Ext.JSON.decode(response.responseText);
		    								if(text.success){
		    									Ext.Msg.alert('提示', text.message);
		    									scriptServiceReleaseStore.reload();   
		    								}else{
		    		    						Ext.Msg.alert('提示', text.message);
		    								}
		    								
		    							},
		    							failure:function(form, action){
		    								Ext.Msg.alert('提示', text.message);
		    							}
		    						});
		                           this.up('window').close();
		                           SRTeditWindow=null;  							  						  				
		    		        
		                    }
		                },{
		                	cls : 'Common_Btn',
		    				textAlign : 'center',
		                	text: '重置',
		                    handler: function() {
		                        this.up('form').getForm().reset();
		                        this.up('form').getForm().loadRecord(record);
		                    }
		                }]
	                }	                
	                ]
	               
	            });
	            if(choice==1){
	            	form.loadRecord(record);
	            }
	            SRTeditWindow = Ext.widget('window', {
	            	title: '基本信息',
	                closable: true,
	                closeAction: 'hide',
	                resizable: false,
	                modal: true,
	                width: contentPanel.getWidth()*0.62,
	                height: contentPanel.getHeight()*0.70,
	      			minWidth: 350,
	      			minHeight: 350,
	                layout: {
	                    type: 'border',
	                    padding: 5
	                },
	                layout: 'fit',
	                modal: true,
	                items: form,
	                defaultFocus: 'firstName',
	                listeners: {
	                    'close': function(field, e){
	                    		form.getForm().reset();
	                    		sysID ='';
	            	            busID ='';
//		                      this.up('window').close();
//		                      SRTeditWindow=null;
	                    }
	                },
	            });
	        }
	        SRTeditWindow.show();
	    }    
  	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do?dbaasFlag=1',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "审核人",
	    labelWidth: 93,
	    labelAlign:'right',
	    store: auditorStore_sm,
	    queryMode: 'local',
	    columnWidth:db_projectFlag == 1?.98:.95,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    editable : true,
	    valueField: 'loginName',//,
	    listeners: { //监听 
	        render : function(combo) {//渲染 
	            combo.getStore().on("load", function(s, r, o) { 
	                combo.setValue(r[0].get('loginName'));//第一个值 
	            });
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            } 
	    } 
	  });
	
	pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '发布申请说明',
        labelAlign:'right',
        emptyText: '',
        labelWidth: 93,
        margin : '10 0 0 0',
        maxLength: 255, 
        height: 55,
        columnWidth:.98,
        autoScroll: true
    });
	
	var serviceAutoStore_sm = Ext.create('Ext.data.Store', {
	    fields: ['iid', 'serviceAuto'],
	    data : [
	        {"iid":"0", "serviceAuto":"DBA"},
	        {"iid":"1", "serviceAuto":"项目组"}
	    ]
	});
	var serviceAuto_sm = Ext.create('Ext.form.field.ComboBox', {

		labelWidth: 93,
		labelAlign:'right',

		name: 'scriptLevel',
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '服务权限',
        margin : '10 0 0 0',
        displayField: 'serviceAuto',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择服务权限--',
        store: serviceAutoStore_sm
    });
	auditing_form_sm = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	bodyCls : 'x-docked-noborder-top',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
//	    	layout:'form',
	    	anchor:'98%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_sm]
	    	},{
				layout : 'column',
				border : false,
				items : [ serviceAuto_sm ]
			},{
	    		layout:'column',
		    	border : false,
		    	items:[pubDesc_sm]
	    	}]
	    }]
	});
	
	function deleteServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt < 1){
			Ext.MessageBox.alert("提示", "请选择要删除的服务！");
			return ;
		}
		var ss = selModel.getSelection();
		for ( var i = 0, len = ss.length; i < len; i++) {
			if(ss[i].data.status!=-1) {
				Ext.MessageBox.alert("提示", "只能删除状态为'草稿'的服务！");
				return ;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认删除", "是否删除选中的服务", function(id){if(id=='yes') release(1);});
	}
		    
	function release(optionState){
		var url = 'scriptService/serviceRelease.do';
		var message="脚本发布成功";
		var errorMessage="脚本发布失败";
		if(optionState==1){
			message="脚本删除成功";
			errorMessage="脚本删除失败";
			url = "scriptService/deleteScriptForTest.do";
	    } 		
		var jsonData = getSelectedJsonData();
		if(jsonData=="[]"){
			Ext.MessageBox.alert("提示", signMessage);
			return ;
		}	
	    Ext.Ajax.request({
		    url : url,
		    method : 'POST',
		    params : {
		  	  jsonData : jsonData,
		  	  optionState:optionState,
//		  	  shareType:shareType,
//		  	  chosedShareIds:chosedShareIds,
		  	  isflow:0,
		  	  isCustomTask:0
		    },
		    success: function(response, opts) {
		    	if(optionState==1) {
		    		var message1 = Ext.decode(response.responseText).message;
		    		Ext.MessageBox.show({
		                title : "提示",
		                msg : message1,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		    	} else {
		    		var success = Ext.decode(response.responseText).success;
			        if (success) {
			            Ext.MessageBox.show({
			                title : "提示",
			                msg : message,
			                buttonText: {
			                    yes: '确定'
			                },
			                buttons: Ext.Msg.YES
			              });
			          } else {
			            Ext.MessageBox.show({
			              title : "提示",
			              msg : errorMessage,
			              buttonText: {
			                  yes: '确定'
			              },
			              buttons: Ext.Msg.YES
			            });
			          }
		    	}
		    	scriptServiceReleaseStore.reload();
		        
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
	}
	
	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedJsonData(){
		var flowIdList = scriptServiceReleaseGrid.getSelectionModel().getSelection();
		if (flowIdList.length < 1) {
			return;
		}
		var jsonData = "[";
		for ( var i = 0, len = flowIdList.length; i < len; i++) {
			if (i == 0)
			{
				jsonData = jsonData + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			} else {
				jsonData = jsonData + "," + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			}
		}
		jsonData = jsonData + "]";
		return jsonData ;
	}
	
	
	  
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function queryData(){
    	scriptServiceReleaseStore.reload();
    }
	function clearQueryWhere(){
		serviceId.setValue('');
		sName.setValue('');
		serviceTypeParam.setValue('');
	}
	//从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key ,jsonObj){
		 //var eValue=eval('jsonObj.'+key);  
		 return jsonObj[''+key+''];
	}	
		
	function publishScript(iid,a,b,c,hasVersion, status,bussId,bussTypeId,serviceType,serviceName,scriptType,dbType){
			
		if(hasVersion==1) {
			Ext.Msg.alert('提示', "该服务已经发布过！");
			return;
		}		
		if (status==1) { // 处于审核中
			Ext.Msg.alert('提示', "该服务已上线，不可重复发布！");
			return;
		}			
		if (status==2) { // 处于审核中
			Ext.Msg.alert('提示', "该服务正处于审核中！");
			return;
		}			
		
  		var groupId = iid;
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认发布", "是否确认发布该服务", function(id){
			if(id=='yes') {
				Ext.Ajax.request({
					url : "getServiceDatasForGroupyz.do",
					method : 'POST',
					async: false,
		 			params : {
		 				start:0,
		 				limit:1,
		 				flag:0,
		 				groupId : iid,
		 				bussId:bussId,
		 				bussTypeId:bussTypeId,
		 				serviceType:serviceType,
		 				scriptType:scriptType,
		 				dbType:dbType
 					},
  				    success: function(response, opts) {
  				        var total = Ext.decode(response.responseText).total;
  				        if(total==0) {
  				        	Ext.MessageBox.alert("提示", "发布前请先绑定原子服务！");
  				        }else{
  				        	if (!publishAuditingSMWin) {
  								publishAuditingSMWin = Ext.create('widget.window', {
  					                title: '确认审核信息',
  					                closable: true,
  					                closeAction: 'hide',
  					                modal: true,
  					                width: 600,
  					                minWidth: 350,
  					                height: 300,
  					                layout: {
  					                    type: 'border',
  					                    padding: 5
  					                },
  					                items: [auditing_form_sm],
  					                dockedItems : [ {
  										xtype : 'toolbar',
  										dock : 'bottom',
  										layout: {pack: 'center'},
  										items : [ {
  							  			xtype: "button",
  							  			cls:'Common_Btn',
  							  			text: "确定", 
  							  			handler: function () { 
  							  				//判断输入的审核人是否合法 start
  						            	var displayField =auditorComBox_sm.getRawValue();
  										if(!Ext.isEmpty(displayField)){
  											//判断输入是否合法标志，默认false，代表不合法
  											var flag = false;
  											//遍历下拉框绑定的store，获取displayField
  											auditorStore_sm.each(function (record) {
  												//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
  											    var data_fullName = record.get('fullName');
  											    if(data_fullName == displayField){
  											    	flag =true;
  											    	//combo.setValue(record.get('loginName'));
  											    }
  											});
  											if(!flag){
  											 	Ext.Msg.alert('提示', "输入的审核人非法");
  											 	auditorComBox_sm.setValue("");
  											 	return;
  											} 
  										}
  							  				var publishDesc = pubDesc_sm.getValue();
  							  				var auditor = auditorComBox_sm.getValue();
  							  				var serviceAuto=serviceAuto_sm.getValue();

  								  			if (!serviceAuto) {
  						  					Ext.Msg.alert('提示',"没有选择服务权限！");
  						  					return;
  						  				}
  							  				if(!publishDesc) {
  							  					Ext.Msg.alert('提示', "没有填写发布申请说明！");
  							  					return;
  							  				}
  							  				if(publishDesc.length > 255) {
  							  					Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
  							  					return;
  							  				}
  							  				if(!auditor) {
  							  					Ext.Msg.alert('提示', "没有选择审核人！");
  							  					return;
  							  				}		
  							  				var flowIdList = scriptServiceReleaseGrid.getSelectionModel().getSelection();
  							  				
  							  				Ext.Ajax.request({
  							  				    url : 'serviceGroupPublishAuditing.do',
  							  				    method : 'POST',
  							  				    async: false,
  							  				    params : {
  							  				    	groupId : flowIdList[0].data.iid,
  							  				    	publishDesc: publishDesc,
  							  				    	auditor: auditor,
  							  				  	  	serviceAuto:serviceAuto,
  							  				  	  	serviceName:flowIdList[0].data.serviceName
  							  				    },
  							  				    success: function(response, opts) {
  							  				        var success = Ext.decode(response.responseText).success;
  							  				        var message = Ext.decode(response.responseText).message;
  							  				        if(!success) {
  							  				        	Ext.MessageBox.alert("提示", message);
  							  				        } else {
  							  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
  							  				        }
  							  				      scriptServiceReleaseStore.load();
  							  				      publishAuditingSMWin.close();
  							  				      
  							  				    },
  							  				    failure: function(result, request) {
  							  				    	secureFilterRs(result,"操作失败！");
  							  				    	publishAuditingSMWin.close();
  							  				    }
  							  			    });
  							  				
  								        }
  							  		}, { 
  							  			xtype: "button", 
  							  			cls:'Common_Btn',
  							  			text: "取消", 
  							  			handler: function () {
  							  				this.up("window").close();
  							  			}
  							  		}]
  								}]
  					            });				            
  					        }
  				        }
  				        publishAuditingSMWin.show();
						auditorStore_sm.load();
						pubDesc_sm.setValue('');
						serviceAuto_sm.setValue('');
  				    },
  				    failure: function(result, request) {
  				    	secureFilterRs(result,"操作失败！");
  				    	publishAuditingSMWin.close();
  				    }
  			    });
				
		        	
			}
		});
		
	}		
	

});

function addServiceForGroup(iid,bussId,bussTypeId,serviceType,scriptType,dbType,status){
	if(status==1){		
		Ext.Msg.confirm("请确认", "该服务已上线，确认要修改?",
    	function(button, text) {
        	if (button == "yes") {
        		addServiceForGroup1(iid,bussId,bussTypeId,serviceType,scriptType,dbType,status);
        	}
    	})
	}else if(status==2){
		Ext.Msg.confirm("请确认", "该服务正在审核中，不可修改",
		    	function(button, text) {
		        	if (button == "yes") {
		        		addServiceForGroup1(iid,bussId,bussTypeId,serviceType,scriptType,dbType,status);
		        	}
		    	})
	}else{
		addServiceForGroup1(iid,bussId,bussTypeId,serviceType,scriptType,dbType,status);
	}
	
}

function addServiceForGroup1(iid,bussId,bussTypeId,serviceType,scriptType,dbType,status){		
	  Ext.create('Ext.window.Window', {
	  		title : '服务信息',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'destroy',
	  		width : contentPanel.getWidth(),
	  		height : contentPanel.getHeight(),
	  		loader : {
		 			url : "bindServiceForGroup.do",
		 			params : {
		 				groupId : iid,
		 				bussId:bussId,
		 				bussTypeId:bussTypeId,
		 				serviceType:serviceType,
		 				scriptType:scriptType,
		 				dbType:dbType,
		 				status:status
 					},
 					autoLoad: true,
 					autoDestroy : true,
 					scripts : true
	  			},
	  		listeners : {
	  			'close':function(){
	  				scriptServiceReleaseStore.reload();
	  				}
	  			}
	  	}).show();
		 win.on("close",function(){
			 scriptservice_store.reload();
			 sgmstore.reload();
		 });
}
function searchVersion(iid){
	
	  Ext.create('Ext.window.Window', {
	  		title : '版本信息',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'destroy',
	  		width : contentPanel.getWidth(),
	  		height : contentPanel.getHeight(),
	  		loader : {
		 			url : "initGroupServiceVersion.do",
		 			params : {
		 				groupId : iid
					},
					autoLoad: true,
					autoDestroy : true,
					scripts : true
	  			}
	  	}).show();
	 win.on("close",function(){
		 scriptservice_store.reload();
		 sgmstore.reload();
	 });

}



