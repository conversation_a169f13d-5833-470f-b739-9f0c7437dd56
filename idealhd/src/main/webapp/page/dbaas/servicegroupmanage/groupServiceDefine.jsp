<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<html>
<head>
<script type="text/javascript">
var flag = <%=request.getAttribute("flag")%>
var db_f_class = <%=request.getAttribute("db_f_class")%>;
var db_s_class = <%=request.getAttribute("db_s_class")%>;
var db_s_level = <%=request.getAttribute("db_s_level")%>;
var db_serviceType = <%=request.getAttribute("db_serviceType")%>;
var db_scriptType = <%=request.getAttribute("db_serviceType")%>;
var db_dbType = <%=request.getAttribute("db_dbType")%>;
var db_ssuer = <%=request.getAttribute("db_ssuer")%>;
var shareSwitch = <%=request.getAttribute("shareSwitch")%>;
var importSwitch = <%=request.getAttribute("importSwitch")%>;
var db_projectFlag=<%=request.getAttribute("db_projectFlag")==null?0:request.getAttribute("db_projectFlag")%>;
var filter_bussId = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeId = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptType = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
var filter_scriptStatus = '<%=request.getParameter("filter_scriptStatus")==null?-10000:request.getParameter("filter_scriptStatus")%>';
var filter_patFromValue ='<%=request.getParameter("filter_patFromValue")==null?"":request.getParameter("filter_patFromValue")%>';
var db_usePlantFormswitch = <%=request.getAttribute("db_usePlantFormswitch")%>;
var db_queryIssueRecordswitch= <%=request.getAttribute("db_queryIssueRecordswitch")%>;
var db_sendScriptswitch= <%=request.getAttribute("db_sendScriptswitch")%>;
var filter_serviceType = '<%=request.getParameter("filter_serviceType")==null?-1:request.getParameter("filter_serviceType")%>';
var db_isshareswitch=<%=request.getAttribute("db_isshareswitch")%>;
var db_useTimesswitch=<%=request.getAttribute("db_useTimesswitch")%>;
var db_winTimesswitch=<%=request.getAttribute("db_winTimesswitch")%>;
var db_versionswitch=<%=request.getAttribute("db_versionswitch")%>;
var db_scriptNameswitch=<%=request.getAttribute("db_scriptNameswitch")%>;
var db_scriptTypeswitch=<%=request.getAttribute("db_scriptTypeswitch")%>;
var db_statusswitch=<%=request.getAttribute("db_statusswitch")%>;
var filter = {
		'filter_bussId': filter_bussId,
		'filter_bussTypeId': filter_bussTypeId,
		'filter_scriptName': filter_scriptName,
		'filter_serviceName': filter_serviceName,
		'filter_scriptType': filter_scriptType,
		'filter_scriptStatus':filter_scriptStatus,
		'filter_serviceType':filter_serviceType,
		'filter_patFromValue':filter_patFromValue
	};
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/servicegroupmanage/groupServiceDefine.js"></script>
</head>
<body>
<div id="groupServiceDefine_grid_area" style="width: 100%;height: 100%">
</div>
</body>
</html>