Ext.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			Ext.tip.QuickTipManager.init();
			Ext.define('versionModel', {
			    extend : 'Ext.data.Model',
			    fields : [ 
				    {name : 'iid'         ,type : 'long'}, 
				    {name : 'uuid'     ,type : 'string'},
				    {name : 'createTime' ,type : 'string'}, 
				    {name : 'version'     ,type : 'string'},
				    {name : 'onlyVersion'     ,type : 'string'},
				    {name : 'status',type : 'long'},
				    {name : 'updateUserId',type : 'string'},
				    {name : 'updateUserName',type : 'string'},
				    {name : 'analyzeText',type : 'string'}
			    ]
			});
			Ext.define('scriptService', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				}, {
					name : 'serviceName',
					type : 'string'
				},{
					name : 'sysName',
					type : 'string'
				}, {
					name : 'bussName',
					type : 'string'
				}, {
					name : 'serviceType',
					type : 'string'
				}, {
					name : 'dbType',
					type : 'string'
				}, {
					name : 'scriptType',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'ssuer',
					type : 'string'
				}, {
					name : 'version',
					type : 'string'
				},{
					name : 'servicePara',
					type : 'string'
				}, {
					name : 'platForm',
					type : 'string'
				}, {
					name : 'status',
					type : 'int'
				},{
					name : 'content',
					type : 'string'
				},{
					name : 'serviceTy',
					type : 'String'
				}, {
					name : 'startType',
					type : 'String'
				},{
					name : 'bussId',
					type : 'int'
				}, {
					name : 'bussTypeId',
					type : 'int'
				}, {
					name : 'scriptLevel',
					type : 'int'
				}, {
					name : 'isFlow',
					type : 'string'
				}, {
					name : 'serviceAuto',
					type : 'string'
				} ,{
					name : 'serviceId',
					type : 'string'
				},{
					name : 'bindStatus',
					type : 'string'
				},{
					name : 'order',
					type : 'int'
				} ]
			});
			/** 查询按钮* */
			var queryButtonForBSM = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    textAlign : 'center',
			    text : '查询',
			    handler : queryWhere
			});
			/** 重置按钮* */
			var resetButtonForBSM = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    textAlign : 'center',
			    text : '重置',
			    handler : resetWhere
			});
			var serviceNameForQuery = Ext.create ('Ext.form.TextField',
			{
			    emptyText : '--请输入服务名--',
			    labelWidth : 50,
			    width : '20%',
			    xtype : 'textfield',
			    listeners: {  
		              specialkey: function(field,e){    
		                  if (e.getKey()==Ext.EventObject.ENTER){    
		                    scriptservice_store.reload();
		                  }  
		              }  
		          }
			});
					
			var serviceIdForQuery = Ext.create ('Ext.form.TextField',
			{
			    emptyText : '--请输入服务ID--',
			    labelWidth : 50,
			    width : '20%',
			    xtype : 'textfield',
			    listeners: {  
		              specialkey: function(field,e){    
		                  if (e.getKey()==Ext.EventObject.ENTER){    
		                    scriptservice_store.reload();
		                  }  
		              }  
		          }
			});
			
			var versionStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'versionModel',
				proxy : {
					type : 'ajax',
					url : 'getScriptServiceVersionListForAllScript.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			
			versionStore.on('beforeload', function (store, options) {
				    var new_params = {  
				    		serviceId:groupId,
					    	flag:0
				    };
				    
				    Ext.apply(versionStore.proxy.extraParams, new_params);
			    });
			
			versionStore.on('load', function (store, options) {
				versionGrid.getSelectionModel().select(0, true);
		    });
			
		    var scriptservice_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				pageSize : 50,
				model : 'scriptService',
				proxy : {
					type : 'ajax',
					url : 'getServiceDatasSelectGroup.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
		    var pageBarServer = Ext.create('Ext.PagingToolbar', {
		    	store: scriptservice_store,
		        dock: 'bottom',
		        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		        displayInfo: true,
		        border:false
		    });
		    
		    scriptservice_store.on ('beforeload', function (store, options)
			{
				var new_params =
				{
					serviceName : serviceNameForQuery.getValue().trim(),
					serviceId:serviceIdForQuery.getValue().trim(),
					groupId : groupId,
					flag:0,
					bindStatus:1
				};
				Ext.apply (scriptservice_store.proxy.extraParams, new_params);
			});
			var versionColumns = [{
					text : '序号',
					xtype : 'rownumberer',
					width : 40
				}, 
				{
				    text : '服务主键',
				    dataIndex : 'iid',
				    width : 40,
				    hidden : true
				}, {
				    text : '服务主键uuid',
				    dataIndex : 'uuid',
				    width : 40,
				    hidden : true
				},{
				    text : '是否禁用',
				    dataIndex : 'status',
				    width : 80,
				    renderer : function(value, p, record) {
		       		 var backValue = "";
		       		 if (value == 2) {
		       			 backValue = "是";
		       		 } else {
		       			 backValue = "否";
		       		 }
		       		 return backValue;
		       	 }
				},  {
					text : '版本',
					dataIndex : 'onlyVersion',
					minWidth:60,
					flex:1,
					renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				},
				{
					text : '创建时间',
				    dataIndex : 'createTime',
				    width:180,
				    renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}, 
				{
					text : '修改者id',
				    dataIndex : 'updateUserId',
				    width:100,
				    renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}, 
				{
					text : '修改者姓名',
				    dataIndex : 'updateUserName',
				    width:100,
				    renderer:function (value, metaData, record, rowIdx, colIdx, store){  
		                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
		                return value;  
		            }
				}, 								
				{ 
					text: '操作',  
					dataIndex: 'stepOperation',
					align:'left',
					width:100,
					renderer : displayWord,
					listeners:{
	                    click:function(a,b,c,d){
	                        var uuid=a.getStore().getAt(c).data.uuid;
	                        var iid=a.getStore().getAt(c).data.iid;
	                        //alert(enable);
	                        toAnalyzeTextWin(uuid,iid);
	                    }
		            }
				}
			];						
			var scriptservice_columns = [ {
				text : '序号',
				xtype : 'rownumberer',
				align:'left',
				width : 55
			}, {
				text : '主键',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : '服务ID',
				dataIndex : 'serviceId',
				minWidth : 100,
				flex:1,
				renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
			}, {
				text : '服务名称',
				dataIndex : 'serviceName',
				width : 100,
				renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
			}, {
				text : '脚本名称',
				dataIndex : 'scriptName',
				width : 100,
//				flex : 1,
				 renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
			}, {
				text : '服务类型',
				dataIndex : 'serviceType',
				hidden : true,
				width : 90,
				renderer : function(value, p, record) {
					var backValue = "";
					if (value == 0) {
						backValue = "应用";
					} else if (value == 1) {
						backValue = "采集";
					}	
					return backValue;
				}
			}, {
				text : '数据库类型',
				dataIndex : 'dbType',
				width : 100,
				 renderer : function(value, metadata) {
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
			}, {
				text : '脚本类型',
				dataIndex : 'scriptType',
				width : 80,
				hidden : true,
				renderer : function(value, p, record) {
					var backValue = "";
					if (value == "sh") {
						backValue = "shell";
					} else if (value == "perl") {
						backValue = "perl";
					} else if (value == "py") {
						backValue = "python";
					} else if (value == "bat") {
						backValue = "bat";
					} else if (value == "sql") {
						backValue = "sql";
					}
					if (record.get('isFlow') == '1') {
						backValue = "组合";
					}
					return backValue;
				}
			},{
				text : '有效状态',
				dataIndex : 'status',
				width : 90,
				hidden : true,
				renderer : function(value, p, record) {
					var backValue = "";
					if (value == 1) {
						backValue = "<span class='Complete_Green State_Color'>启用</span>";
					} else if (value == 2) {
						backValue = "<span class='Abnormal_yellow State_Color'>禁用</span>";
					}
					return backValue;
				}
			},{
				text : '服务权限',
				dataIndex : 'serviceAuto',
			//	hidden : true,
				width : 90
			},{
				text : '执行顺序',
				dataIndex : 'order',
				editor : {
					allowBlank : false,
					xtype : 'numberfield',
					maxValue : 30,
					minValue : 1
				},
				width : 90
			}
			];
			   var selModel = Ext.create('Ext.selection.CheckboxModel', {
			        checkOnly: true
//			        mode: "SINGLE"
			    });
			var versionGrid = Ext.create('Ext.grid.Panel', {
				width : '45%',
			    /*height : contentPanel.getHeight() - 140,*/
			    store : versionStore,
			    selModel : selModel,
			    cls:'customize_panel_back panel_space_right',
			    border:true,
			    columnLines : true,
			    columns : versionColumns,
			    region: 'west',
			    listeners: {
			        select: function( me, record, index, eOpts ) {
			        	newServiceId = record.get('iid');
			        	newUuid = record.get('uuid');
			        	scriptservice_store.load({
							params: {
								groupId : newServiceId,
								flag:0,
								bindStatus:1
						    } 
						});
			        	
			        }
			    },
			    dockedItems:[
								{
									xtype : 'toolbar',
									border : false,
									dock : 'top',
									items: [
										'->',
									{
										text: '版本比对',
										cls : 'Common_Btn',
										handler: compareScript
									}]
							}]
			});
		    
			var selModelServer = Ext.create('Ext.selection.CheckboxModel');
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		        clicksToEdit:1
		    });
			var Form1 = Ext.create('Ext.form.FormPanel', {
				region: 'north',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [serviceIdForQuery,serviceNameForQuery,queryButtonForBSM, resetButtonForBSM]
				} ]
			});
			var scriptGrid = Ext.create ('Ext.grid.Panel',
			{
			    store : scriptservice_store,
			    selModel : selModelServer,
			    region : 'center',
			    plugins: [cellEditing],
			    padding : panel_margin,
				border:true,
				bbar:pageBarServer,
			    columnLines : true,
			    columns : scriptservice_columns,
			    collapsible : false,
			    viewConfig:{  
		            enableTextSelection:true  
		        },
			    listeners: {
					select : function(selModelp, record, index, eOpts) {},
					deselect : function(roleSelModel, record, index, eOpts) {}
		        }
			});		    		    		  
			var scriptPanel = Ext.create ('Ext.panel.Panel',
			{
			    layout : 'border',
			    region : 'center',
			    bodyCls:'service_platform_bodybg',
			    bodyPadding : grid_margin,
			    border : true,
			    items : [Form1,scriptGrid]
			});						
			mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "servicegroupVersion_area",
				layout: {
		            type: 'border'
		        },
				border : false,
				height : contentPanel.getHeight()-modelHeigth,
				items : [ versionGrid, scriptPanel ]
			});
		    contentPanel.on('resize',function() {
		        mainPanel.setHeight(contentPanel.getHeight());
		        mainPanel.setWidth(contentPanel.getWidth());
		    });
			
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};
			
			function compareScript() {
				var seledCnt = selModel.getCount();
				if(seledCnt != 2){
					Ext.MessageBox.alert("提示", "请选择两个不同版本的服务进行比对！");
					return ;
				}
				var ss = selModel.getSelection();
				var ids = [];
				for ( var i = 0; i < 2; i++) {
					var iidi =ss[i].data.iid;
					var version1 =ss[i].data.version
					ids.push(iidi+"::"+version1);
				}
				
				  if(ids.length>0){
					    var compareWin = Ext.create('widget.window', {
					        title: '对比详情',
					        closable: true,
					        closeAction: 'destroy',
					        width: contentPanel.getWidth()-200,
					        minWidth: 350,
					        height: contentPanel.getHeight(),
					        draggable: false,
					        // 禁止拖动
					        resizable: false,
					        // 禁止缩放
					        modal: true,
					        loader: {
					            url: 'gServiceVersionCompare.do',
					            params: {
					            	ids : ids.join(',')
					            },
					            autoLoad: true,
					            scripts: true
					        }
					    });
					    compareWin.show();					 
	                }
			}
		   		   
		   function displayWord(value, p, record) {
				return "<a style='cursor: pointer;'>查看分析算法</a>";
			}
		   
		   function toAnalyzeTextWin(uuid,iid){
				Ext.create('Ext.window.Window', {
					title: '分析算法',
					height: '60%',  //Number型  也可以是字符串类型  width:'60%'
					width: '60%',
					layout: 'fit',
					constrain: true, 		//闲置窗口不超出浏览器
					constrainHeader:true, 	//标题不能超出浏览器边界
					modal: true,			//设置模态窗口
					plain:true, 			//窗口设置透明背景
					draggable: false,
					resizable: false,
					loader: {
						url: 'getAnalyzeTextWin.do',
						params : {
							uuid : uuid,
							iid:iid
						},
						autoLoad: true,
						scripts: true
					}
				}).show();
			}
		  function searcformquery(){
			  agent_grid.ipage.moveFirst();
		  }
		function queryWhere ()
		{
			scriptservice_store.load();
			pageBarServer.moveFirst();
		}
		function resetWhere ()
		{
			serviceNameForQuery.setValue ('');
			serviceIdForQuery.setValue ('');
		}
			

});
