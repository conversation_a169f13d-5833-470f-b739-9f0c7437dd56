Ext.onReady(function() {
	destroyRubbish();
	Ext.define('DataModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'IID',
			type : 'string'
		}, {
			name : 'INAME',
			type : 'string'
		}, {
			name : 'INAMEDESC',
			type : 'string'
		}, {
			name : 'ICREATETIME',
			type : 'string'
		}, {
			name : 'ICREATEUSERID',
			type : 'string'
		}, {
			name : 'IUPDATETIME',
			type : 'string'
		}, {
			name : 'IUPDATEUSERID',
			type : 'string'
		} ]
	});

	var bindService = {
	    	text : '绑定服务',
			xtype : 'actioncolumn',
			width : 100,
			dataIndex : 'groupId',
			align : 'center',
			sortable : false,
			menuDisabled : true,
			items : [ {
				iconCls:'role_permission',
				tooltip : '绑定服务',
				handler : function (a,b,c,d,e,record) {
					onShowServiceRelation (record.get ('IID'), record.get ('INAME'));
				}
			} ]
		}
	
	var columns = [ {
		xtype : 'rownumberer',
		header : '序号',
		align : 'center',
		width : 60
	}, {
		text : 'TABLE_IID',
		dataIndex : 'IID',
		align : 'left',
		hidden : true
	}, {
		text : '组名称',
		dataIndex : 'INAME',
		align : 'left',
		editor: {
            allowBlank: false
        },
        width : 300
	}, {
		text : '组描述',
		dataIndex : 'INAMEDESC',
		align : 'left',
		editor: {
            allowBlank: true
        },
		width : 300,
		flex : 1
	},bindService, {
		text : '创建人',
		dataIndex : 'ICREATEUSERID',
		align : 'left',
		width : 180
	}, {
		text : '创建时间',
		dataIndex : 'ICREATETIME',
		align : 'center',
		width : 180
	} ];

	var sgmstore = Ext.create('Ext.data.Store', {
		pageSize : 50,
		autoLoad : true,
		model : 'DataModel',
		proxy : {
			type : 'ajax',
			url : 'getServiceGroupManageList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}

	});

	var pageBar = Ext.create('Ext.PagingToolbar', {
		pageSize : 50,
		store : sgmstore,
		baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		displayInfo : true,
		border : false,
		displayMsg : '显示{0}-{1}条，共{2}条',
		emptyMsg : "没有数据"
	});

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	var sname = Ext.create("Ext.form.field.Text", {
		fieldLabel : '组名称',
		labelWidth : 60,
		labelAlign : 'left',
		name : 'sname',
		width : '18%'
	});

	sgmstore.on('beforeload', function(store, options) {
		var new_params = {
			sname : form.getForm().findField('sname').getValue()
		};
		Ext.apply(sgmstore.proxy.extraParams, new_params);

	});
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true
	});
	
	var form = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [ sname, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function() {
					sgmstore.load();
					pageBar.moveFirst();
				}
			}, '->', {
				xtype : 'button',
				text : '新增',
				cls : 'Common_Btn',
				handler : addInfo
			}, {
				xtype : 'button',
				text : '保存',
				cls : 'Common_Btn',
				handler : saveInfo
			}, {
				xtype : 'button',
				text : '删除',
				cls : 'Common_Btn',
				handler : delInfo
			}
			// ,
			// {
			// xtype: 'button',
			// text: '编辑',
			// cls:'Common_Btn',
			// handler: function() {
			// editInfo();
			// }
			// }
			]
		} ]
	});
	
	var grid = Ext.create('Ext.grid.Panel', {
		region : "center",
		plugins : [ cellEditing ],
		store : sgmstore,
		border : true,
		margins : '8',
		columns : columns,
		selModel : selModel
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		layout : "border",
		height : contentPanel.getHeight() - modelHeigth,
		width : contentPanel.getWidth(),
		border : false,
		bbar : pageBar,
		bodyCls : 'service_platform_bodybg',
		items : [ form,grid],
		renderTo : "servicegroupmanage_grid_area"
	});

	contentPanel.on("resize", function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
	});
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});

	function onShowServiceRelation(serviceId, sysName) {
	}
	
	/**
	 * 新增
	 */
	function addInfo() {
		var store = grid.getStore();
		var p = {
			IID : '0',
			INAME : '',
			INAMEDESC : '',
			ICREATEUSERID : '',
			ICREATETIME : ''
		};
		store.insert(0, p);
		grid.getView().refresh();
	}
	function saveInfo() {
		var m = sgmstore.getModifiedRecords();
		if (m.length < 1) {
			setMessage('无需要增加或者修改的数据！');
			return;
		}
		Ext.MessageBox.wait("数据处理中...", "进度条");
		var jsonData = "[";
		var execflag=0;
		for (var i = 0, len = m.length; i < len; i++) {
			var INAME = m[i].get("INAME").trim();
			if ("" == INAME || null == INAME) {
				setMessage('组名称不能为空！');
				return;
			}
			if (fucCheckLength(INAME) > 255) {
				setMessage('组名称不能超过255字符！');
				return;
			}
			var INAMEDESC = m[i].get("INAMEDESC").trim();
			if ("" != INAMEDESC && null != INAMEDESC) {
				if (fucCheckLength(INAMEDESC) > 255) {
					setMessage('组描述不能超过255字符！');
					return;
				}
			}
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
			//校验组名是否存在
			var allrecords = sgmstore.getRange(0,sgmstore.getCount());
			var count =0;
			Ext.each(allrecords, function(r) {
				if(m[i].get("INAME").trim()==r.data.INAME.trim()){
					count++;
				}
				if(count>1){
					execflag=1;
					Ext.Msg.show({
					     title:'提示',
					     msg: " 组名称:'"+m[i].get("INAME")+"'重复",
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.WARNING
					});
					return;
				}
			});
		}
		jsonData = jsonData + "]";

		if(execflag==1){
			return;
		}
		Ext.Ajax.request({
			url : 'saveServiceGroup.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					sgmstore.modified = [];
					sgmstore.reload();
					Ext.Msg.alert('提示', message);
				} else {
					Ext.Msg.alert('提示', message);
				}
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}
	/**
	 * 删除
	 */
	function delInfo() {
		var record = grid.getSelectionModel().getSelection();
		if (record.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条要删除的记录！');
			return;
		} else {
			// 需要支持批量删除
			Ext.Msg.confirm('系统提示', '确定要删除', function(btn) {
				if (btn == 'yes') {
					var jsonArray = [];
					Ext.each(record, function(item) {
						var iid = item.data.IID;
						if ("" != iid) {
							jsonArray.push(iid);
						}else{
							sgmstore.remove(record);
						}
					});
					Ext.Ajax.request({
						method : "POST",
						url : "delServiceGroup.do",
						params : {
							deleteIds : jsonArray.join(',')
						},
						success : function(response) {
							var text = Ext.JSON.decode(response.responseText);
							if (text.success) {
								Ext.Msg.alert('提示', '删除成功！');
								sgmstore.load();
							} else {
								Ext.Msg.alert('提示', text.msg);
							}
						},
						failure : function(form, action) {
							switch (action.failureType) {
							case Ext.form.action.Action.CLIENT_INVALID:
								Ext.Msg.alert('提示', '连接异常！');
								break;
							case Ext.form.action.Action.SERVER_INVALID:
								Ext.Msg.alert('提示', action.result.msg);
							}
						}
					});
				}
			});
		}
	}
	
});
