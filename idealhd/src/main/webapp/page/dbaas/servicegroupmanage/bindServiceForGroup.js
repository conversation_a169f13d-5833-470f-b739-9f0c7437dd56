Ext.onReady(function() {
  var currentGroupId=-1;
  var curServer=-1;
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  
  Ext.define('scriptService', {
	    extend : 'Ext.data.Model',
	    fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		},{
			name : 'sysName',
			type : 'string'
		}, {
			name : 'bussName',
			type : 'string'
		}, {
			name : 'serviceType',
			type : 'string'
		}, {
			name : 'dbType',
			type : 'string'
		}, {
			name : 'scriptType',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'ssuer',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		},{
			name : 'servicePara',
			type : 'string'
		}, {
			name : 'platForm',
			type : 'string'
		}, {
			name : 'status',
			type : 'int'
		},{
			name : 'content',
			type : 'string'
		},{
			name : 'serviceTy',
			type : 'String'
		}, {
			name : 'startType',
			type : 'String'
		},{
			name : 'bussId',
			type : 'int'
		}, {
			name : 'bussTypeId',
			type : 'int'
		}, {
			name : 'scriptLevel',
			type : 'int'
		}, {
			name : 'isFlow',
			type : 'string'
		}, {
			name : 'serviceAuto',
			type : 'string'
		} ,{
			name : 'serviceId',
			type : 'string'
		},{
			name : 'bindStatus',
			type : 'string'
		},{
			name : 'order',
			type : 'int'
		} ]
	});
	
  var scriptservice_columns = [ {
		text : '序号',
		xtype : 'rownumberer',
		align:'left',
		width : 70
	}, {
		text : '主键',
		dataIndex : 'iid',
		hidden : true
	},{
		text : '版本号',
		dataIndex : 'version',
		width : 80
	}, {
		text : '服务名称',
		dataIndex : 'serviceName',
		width : 300,
		flex : 1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	}, {
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 300,
		flex : 1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	}, {
		text : '适用平台',
		dataIndex : 'platForm',
		width : 100
	}, {
		text : '执行顺序',
		dataIndex : 'order',
		editor : {
			allowBlank : false,
			xtype : 'numberfield',
			maxValue : 30,
			minValue : 1
		},
		width : 90
	}
	];
  
  var scriptserviceun_columns = [ {
		text : '序号',
		xtype : 'rownumberer',
		align:'left',
		width : 70
	}, {
		text : '主键',
		dataIndex : 'iid',
		hidden : true
	}, {
		text : '版本号',
		dataIndex : 'version',
		width : 80
	}, {
		text : '服务名称',
		dataIndex : 'serviceName',
		width : 300,
		flex : 1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	}, {
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 300,
		flex : 1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	}, {
		text : '脚本类型',
		dataIndex : 'scriptType',
		width : 80,
//		hidden : true,
		renderer : function(value, p, record) {
			var backValue = "";
			if (value == "sh") {
				backValue = "shell";
			} else if (value == "perl") {
				backValue = "perl";
			} else if (value == "py") {
				backValue = "python";
			} else if (value == "bat") {
				backValue = "bat";
			} else if (value == "sql") {
				backValue = "sql";
			}
			if (record.get('isFlow') == '1') {
				backValue = "组合";
			}
			return backValue;
		}
	},{
		text : '适用平台',
		dataIndex : 'platForm',
		width : 100
	}
	];
	
  var store = Ext.create('Ext.data.Store', {
    autoDestroy: true,
    pageSize: 50,
    model : 'scriptService',
	proxy : {
		type : 'ajax',
		url : 'getServiceDatasSelectGroup.do',
		reader : {
			type : 'json',
			root : 'dataList',
			totalProperty : 'total'
		}
	}
  });
  
  var itNameField = Ext.create("Ext.form.field.Text", {
		fieldLabel : '服务名称',
		labelWidth : 65,
		labelAlign : 'left',
		name : 'dataBaseNameParam',
		columnWidth : '.35',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBarRight.moveFirst();
                }
            }
        }
			
	});
  
  store.on('beforeload', function (store, options) {
	  var new_params =
		{
			serviceName : itNameField.getValue().trim(),
			groupId : groupIdForS,
			flag:0,
			bussId : bussId,
			bussTypeId : bussTypeId,
			serviceType : serviceType,
			scriptType :scriptType,
			dbType:dbType,
			bindStatus:0
		};
		Ext.apply (store.proxy.extraParams, new_params);
  });
  
  store.addListener('load',function(){
    var records=[];//存放选中记录
    for(var i=0;i<store.getCount();i++){
      var record = store.getAt(i);
      if(record.data.checked){
        records.push(record);
      }
    }
	selModel.select(records);//选中记录
  });
  
  var choosedStore = Ext.create('Ext.data.Store', {
	    autoDestroy: true,
	    pageSize: 50,
	    model: 'scriptService',
	    proxy: {
	      type: 'ajax',
	      url: 'getServiceDatasSelectGroup.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
  choosedStore.on('beforeload', function (store, options) {
	  var new_params =
		{
			serviceName : itNameField.getValue().trim(),
			groupId : groupIdForS,
			flag:0,
			bussId : bussId,
			bussTypeId : bussTypeId,
			serviceType : serviceType,
			scriptType :scriptType,
			dbType:dbType,
			bindStatus:1
		};
		Ext.apply (choosedStore.proxy.extraParams, new_params);
  });  
  choosedStore.addListener('load',function(){
	    var records=[];//存放选中记录
	    for(var i=0;i<choosedStore.getCount();i++){
	      var record = choosedStore.getAt(i);
	      if(record.data.checked){
	        records.push(record);
	      }
	    }
	    chosedSelModel.select(records);//选中记录
	  });
  
	var pageBarRight = Ext.create('Ext.PagingToolbar', {
    	store: store, 
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        displayInfo: true
    });  
	var pageBarLeft = Ext.create('Ext.PagingToolbar', {
    	store: choosedStore, 
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        displayInfo: true
    }); 
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	var chosedSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	queryWhere();	 
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
  var unChosedServList = Ext.create('Ext.grid.Panel', {
    region : 'center',
    title : '待选择的服务',
    width : "50%",
    border:true,
    padding : panel_margin,
    bbar: pageBarRight,
    multiSelect: true,
    split : true,
	viewConfig : {
		plugins : {
			ptype : 'gridviewdragdrop',
			dragGroup : 'secondGridDDGroup',
			dropGroup : 'firstGridDDGroup'
		},
		listeners : {
			drop : function(node, data, dropRec, dropPosition) {
				var dropOn = dropRec ? ' ' + dropPosition + ' '
						+ dropRec.get('name') : ' on empty view';
			}
		}
	},
    columnLines : true,
    emptyText: '没有服务信息',
    store : store,
    columns : scriptserviceun_columns,		
    listeners : {
		itemclick : function(dv, record, item, index, e) {
			store.each(function(rec) {
				rec.set('itemClicked', false);
			});
			record.set('itemClicked', true);
		}
	},
    dockedItems: [{
	      xtype: 'toolbar',
	      items: [itNameField,{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function() {
						QueryMessage();
					}
	      		},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function() {
						clearQueryWhere();
					}
				},'->',{
		          itemId : 'save_band',
		          text : '保存',
		          disabled : status==2,
		          cls : 'Common_Btn',
		          handler : onSaveListener
	        }]
	    }]
  });
  
  function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBarRight.moveFirst();
		store.load({
			params : {
				start : 0,
				limit : 25
			}
		});
  }
  
  function clearQueryWhere() {
		itNameField.setValue('');
  }
  
  var ChosedServList = Ext.create('Ext.grid.Panel', {
	    region : 'west',
	    title : '绑定服务',
	    width : "50%",
	    bbar: pageBarLeft,
	    border:true,
	    padding : panel_margin,
	    multiSelect: true,
	    split : true,
//	    cls:'window_border panel_space_top panel_space_right',
	    cls:'customize_panel_back',
	    viewConfig : {
			plugins : {
				ptype : 'gridviewdragdrop',
				dragGroup : 'firstGridDDGroup',
				dropGroup : 'secondGridDDGroup'
			},
			listeners : {
				drop : function(node, data, dropRec, dropPosition) {
					var dropOn = dropRec ? ' ' + dropPosition + ' '
							+ dropRec.get('name') : ' on empty view';
				}
			}
		},
	    columnLines : true,
	    emptyText: '没有服务信息',
	    store : choosedStore,
	    columns : scriptservice_columns,
	    plugins: [cellEditing],
	    listeners : {
			itemclick : function(dv, record, item, index, e) {
				choosedStore.each(function(rec) {
					rec.set('itemClicked', false);
				});
				record.set('itemClicked', true);
			}
		},
	    dockedItems: [{
	      xtype: 'toolbar',
	      items: [{
	        itemId : 'savess',
	        text : '还原',
	        //iconCls:'sc_reduction',
	        cls : 'Common_Btn',
	        disabled : status==2,
	        handler : function(){
                store.reload();
                choosedStore.reload();
	        }
	      },'-',{
	          itemId : 'save_band',
	          text : '保存',
	          cls : 'Common_Btn',
	          disabled : status==2,
//	          disabled : true,
	          handler : onSaveListener
	        }]
	    }]
	  });
	
  var mainPanel = Ext.create('Ext.panel.Panel', {
		width : '100%',
		height : '100%',
	    layout : 'border',
	    header : false,
//	    bodyStyle:'width:100%',  
//	    autoWidth:true  ,
	    bodyPadding : grid_margin,
	    border : true,
	    bodyCls:'service_platform_bodybg',
	    items : [ ChosedServList, unChosedServList ],
	    renderTo : "servicegroup_area"
  });
  function queryWhere(){
		store.load({
		       params: { start: 0, limit: 50}
		});
		choosedStore.load({
		       params: { start: 0, limit: 50}
		});
	}
  /* 解决IE下trim问题 */
  String.prototype.trim = function() {
	  return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  /** 窗口尺寸调节* */
  contentPanel.on('resize', function() {
	  mainPanel.setHeight(contentPanel.getHeight());
	  mainPanel.setWidth(contentPanel.getWidth());
  });
	
  	//当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
  
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };
  
  /**
   * @desc 单击保存按钮后，对“左屏”中的数据进行保存。
   * 	   （1）左屏保存时，对服务器信息进行保存，
   * 	   （2）对左屏中所有的记录都进行保存。所以每条记录的checed属性必须全部为为true。
   * */
  function onSaveListener(){
	  var leftDrops = [];			   		//从左屏拽到右屏的数据,需要清理			
	  var leftDropsOrder = [];
	  var jsonArray = [];
	  choosedStore.each(function(rec) {
		  leftDrops.push(rec.get("iid"));
		  leftDropsOrder.push(rec.get("order"));
	  });
	if(leftDropsOrder.length>0) {
		for(var i=0;i<leftDropsOrder.length;i++){	
			jsonArray.push(leftDrops[i]+"%"+leftDropsOrder[i]);
		}
    }
	
	Ext.Ajax.request({
		method:"POST",
		url:"bindServiceforGroup.do",
		params:{
			groupId:groupIdForS,
			ids:jsonArray.join (',')
		},
		success:function(response){
			var text = Ext.JSON.decode(response.responseText);
			Ext.Msg.alert('提示', text.message);
			groupIdForS=text.groupId;
			store.load();
			choosedStore.load();
		},
		failure:function(form, action){
			switch (action.failureType) {
			case Ext.form.action.Action.CLIENT_INVALID:
				Ext.Msg.alert('提示', '连接异常！');
				break;
			case Ext.form.action.Action.SERVER_INVALID:
				Ext.Msg.alert('提示', action.result.msg);
			}
		}
	});	
  }
  
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
});
