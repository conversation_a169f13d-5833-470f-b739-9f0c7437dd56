<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
var groupIdForS = '<%=request.getAttribute("groupId") == null ? "" : request.getAttribute("groupId")%>';
var bussId = '<%=request.getParameter("bussId")==null?-1:request.getParameter("bussId")%>';
var bussTypeId = '<%=request.getParameter("bussTypeId")==null?-1:request.getParameter("bussTypeId")%>';
var serviceType = '<%=request.getParameter("serviceType")==null?-1:request.getParameter("serviceType")%>';
var scriptType = '<%=request.getParameter("scriptType")==null?-1:request.getParameter("scriptType")%>';
var dbType = '<%=request.getParameter("dbType")==null?-1:request.getParameter("dbType")%>';
var status = '<%=request.getParameter("status")==null?-1:request.getParameter("status")%>';
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/servicegroupmanage/bindServiceForGroup.js"></script>
</head>
<body>
<div id="servicegroup_area" style="width: 100%;height: 100%">
</div>
</body>
</html>