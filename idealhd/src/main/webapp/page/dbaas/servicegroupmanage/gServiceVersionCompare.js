Ext.onReady(function() {
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  
  Ext.define('scriptService', {
	    extend : 'Ext.data.Model',
	    fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		},{
			name : 'sysName',
			type : 'string'
		}, {
			name : 'bussName',
			type : 'string'
		}, {
			name : 'serviceType',
			type : 'string'
		}, {
			name : 'dbType',
			type : 'string'
		}, {
			name : 'scriptType',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'ssuer',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		},{
			name : 'servicePara',
			type : 'string'
		}, {
			name : 'platForm',
			type : 'string'
		}, {
			name : 'status',
			type : 'int'
		},{
			name : 'content',
			type : 'string'
		},{
			name : 'serviceTy',
			type : 'String'
		}, {
			name : 'startType',
			type : 'String'
		},{
			name : 'bussId',
			type : 'int'
		}, {
			name : 'bussTypeId',
			type : 'int'
		}, {
			name : 'scriptLevel',
			type : 'int'
		}, {
			name : 'isFlow',
			type : 'string'
		}, {
			name : 'serviceAuto',
			type : 'string'
		} ,{
			name : 'serviceId',
			type : 'string'
		},{
			name : 'bindStatus',
			type : 'string'
		},{
			name : 'order',
			type : 'int'
		} ]
	});
	
  var scriptservice_columns = [ {
		text : '序号',
		xtype : 'rownumberer',
		align:'left',
		width : 70
	}, {
		text : '主键',
		dataIndex : 'iid',
		hidden : true
	}, {
		text : '服务名称',
		dataIndex : 'serviceName',
		width : 300,
		flex : 1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	}, {
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 300,
		flex : 1,
		 renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
	}, {
		text : '适用平台',
		dataIndex : 'platForm',
		width : 100
	}, {
		text : '执行顺序',
		dataIndex : 'order',
		editor : {
			allowBlank : false,
			xtype : 'numberfield',
			maxValue : 30,
			minValue : 1
		},
		width : 90
	}
	];
  
	
  var store0 = Ext.create('Ext.data.Store', {
	autoLoad : true,
    pageSize: 50,
    model : 'scriptService',
	proxy : {
		type : 'ajax',
		url : 'getServiceDatasSelectGroup.do',
		reader : {
			type : 'json',
			root : 'dataList',
			totalProperty : 'total'
		}
	}
  });
  
  var itNameField = Ext.create("Ext.form.field.Text", {
		fieldLabel : '服务名称',
		labelWidth : 65,
		labelAlign : 'left',
		name : 'dataBaseNameParam',
		columnWidth : '.35',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBarRight.moveFirst();
                }
            }
        }
			
	});
  
  store0.on('beforeload', function (store, options) {
	  var new_params =
		{
			serviceName : itNameField.getValue().trim(),
			groupId : iid0,
			flag:0,
			bindStatus:1
		};
		Ext.apply (store0.proxy.extraParams, new_params);
  });
  
  
  var store1 = Ext.create('Ext.data.Store', {
	  	autoLoad : true,
	    pageSize: 50,
	    model: 'scriptService',
	    proxy: {
	      type: 'ajax',
	      url: 'getServiceDatasSelectGroup.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
  store1.on('beforeload', function (store, options) {
	  var new_params =
		{
			serviceName : itNameField.getValue().trim(),
			groupId : iid1,
			flag:0,
			bindStatus:1
		};
		Ext.apply (store1.proxy.extraParams, new_params);
  });  
  
	var pageBarRight = Ext.create('Ext.PagingToolbar', {
    	store: store0, 
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        displayInfo: true
    });  
	var pageBarLeft = Ext.create('Ext.PagingToolbar', {
    	store: store1, 
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        displayInfo: true
    }); 
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	var chosedSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	queryWhere();	 
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
  var gridPanel0 = Ext.create('Ext.grid.Panel', {
    region : 'center',
    title : title0,
    width : "50%",
    border:true,
    padding : panel_margin,
    bbar: pageBarRight,
    multiSelect: true,
    split : true,
    columnLines : true,
    emptyText: '没有服务信息',
    store : store0,
    columns : scriptservice_columns,		
  });
  
  
  var gridPanel1 = Ext.create('Ext.grid.Panel', {
	    region : 'west',
	    title : title1,
	    width : "50%",	    
	    border:true,
	    padding : panel_margin,
	    bbar: pageBarLeft,
	    multiSelect: true,
	    split : true,
	    columnLines : true,
	    emptyText: '没有服务信息',
	    store : store1,
	    columns : scriptservice_columns
	  });
	
  var mainPanel = Ext.create('Ext.panel.Panel', {
		width : '100%',
		height : '100%',
	    layout : 'border',
	    header : false,
	    bodyPadding : grid_margin,
	    border : true,
	    bodyCls:'service_platform_bodybg',
	    items : [ gridPanel0, gridPanel1 ],
	    renderTo : "gServiceVersionCompare_area"
  });
  function queryWhere(){
		store0.load({
		       params: { start: 0, limit: 50}
		});
		store1.load({
		       params: { start: 0, limit: 50}
		});
	}
  /* 解决IE下trim问题 */
  String.prototype.trim = function() {
	  return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  /** 窗口尺寸调节* */
  contentPanel.on('resize', function() {
	  mainPanel.setHeight(contentPanel.getHeight());
	  mainPanel.setWidth(contentPanel.getWidth());
  });
	
  	//当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
  
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };    
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
});
