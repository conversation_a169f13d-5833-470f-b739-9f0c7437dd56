var bsTypeWindow;
Ext.onReady(function() {
    var bsManagerStore;
    var bsManagerGrid;
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.define('bsManangerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'bsName',
            type: 'string'
        },
        {
            name: 'state',
            type: 'string'
        }]
    });
    
    bsManagerStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 15,
        model: 'bsManangerModel',
        proxy: {
            type: 'ajax',
            url: 'bsManager/queryBsModel.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    bsManagerStore.on('beforeload', function(store, options) {
        var new_params = {
            bsName: nameField.getValue()
        };

        Ext.apply(bsManagerStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40,
        resizable: true
    },
    {
        text: '类别主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '业务系统名称',
        dataIndex: 'bsName',
        width: 200,
        flex: 1,
        editor: {
            allowBlank: false
        }
    },{
        text: '状态',
        dataIndex: 'state',
        width: 200,
        renderer:function(value,p,record){
        	var backValue = "";
        	if(value==0){
        		backValue = "使用中";
        	}else if(value==1){
        		backValue = "被删除";
        	}
        	return backValue;
        }
    },
    {
        text: '操作',
        dataIndex: 'stepOperation',
        width: 150,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid'); // 其实是requestID
            var state = record.get('state'); // 其实是requestID
            console.log('asdf',iid);
            console.log('state',state);
            if(!iid) {
            	iid = -1;
            	state = -1;
            }
            return '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="showbsTypeWindow(' + iid + ','+ state + ')">' + '<img src="images/monitor_bg.png" align="absmiddle" class="script_set"></img>&nbsp;配置类型' + '</a>' + '</span>' + '&nbsp;&nbsp;&nbsp;&nbsp;';
        }
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: bsManagerStore,
        dock: 'bottom',
        displayInfo: true,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '业务系统名称',
        labelWidth: 100,
        labelAlign: 'right',
        name: 'bsNameParam',
        width: '30%'
    });
    var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		bodyCls : 'x-docked-noborder-top',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border:false,
			items : [nameField, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                    pageBar.moveFirst();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            },
            {
                text: '增加',
                cls: 'Common_Btn',
                //iconCls:'sc_add',
                handler: add
            },
            {
                text: '保存',
                cls: 'Common_Btn',
                //iconCls:'sc_save',
                handler: saveBsManager
            }, '-', {
                itemId: 'delete',
                text: '删除',
                cls: 'Common_Btn',
                //iconCls:'sc_delete',
                disabled: true,
                handler: deleteBsManager
            },'-', {
                itemId: 'recover',
                text: '状态恢复',
                cls: 'Common_Btn',
                //iconCls:'sc_return',
                handler: updateBs
            },
            {
                text: '导入',
                cls: 'Common_Btn',
                handler: saveBsManager
            },
            {
                text: '导出',
                cls: 'Common_Btn',
                handler: saveBsManager
            }
			]
		} ]
	});
    bsManagerGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        id: 'bsmGrid',
        store: bsManagerStore,
        selModel: selModel,
        plugins: [cellEditing],
        bbar: pageBar,
        border: false,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        dockedItems: [{
            border:false,
            items: [search_form]
        }]
    });

    bsManagerGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        bsManagerGrid.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "serviceSysMaintenance_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        items: [bsManagerGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
		if(bsTypeWindow) {
			bsTypeWindow.center();
		}
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    // 从一个json对象中，解析出key=iid的value,返回改val
    function parsIIDJson(key, jsonObj) {
        var eValue = eval('jsonObj.' + key);
        return jsonObj['' + key + ''];
    }
    function clearQueryWhere() {
        search_form.getForm().findField("bsNameParam").setValue('');
    }
    function add() {
        var store = bsManagerGrid.getStore();
        var p = {
            iid: '',
            bsName: '',
            stepOperation: ''
        };
        store.insert(0, p);
        bsManagerGrid.getView().refresh();
    }
    function saveBsManager() {
        var m = bsManagerStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
            var n = 0;
            var bsName = m[i].get("bsName").trim();

            if ("" == bsName || null == bsName) {
                setMessage('一级分类不能为空！');
                return;
            }
            if (fucCheckLength(bsName) > 200) {
                setMessage('一级分类不能超过200字符！');
                return;
            }
            
            for (var k = 0; k < bsManagerStore.getCount(); k++) {
				var record = bsManagerStore.getAt(k);
				var cname = record.data.bsName;
				if (cname.trim() == bsName) {
					n = n + 1;
				}
			}
			if (n > 1) {
				setMessage('业务系统名称不能有重复！');
				return;
			}

            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Msg.alert('提示', '保存成功');
//        Ext.Ajax.request({
//            url: 'saveBsManager.do',
//            method: 'POST',
//            params: {
//                jsonData: jsonData
//            },
//            success: function(response, request) {
//                var success = Ext.decode(response.responseText).success;
//                if (success) {
//                    bsManagerStore.modified = [];
//                    bsManagerStore.reload();
//                    Ext.Msg.alert('提示', '保存成功');
//                } else {
//                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
//                }
//            },        
//            failure: function(result, request) {
//                secureFilterRs(result, "操作失败！");
//            }
//        });
    }

    function updateBs() {
    	var data = bsManagerGrid.getView().getSelectionModel().getSelection();
    	if (data.length == 0) {
    		Ext.Msg.alert('提示', '请先选择您要操作的行!');
    		return;
    	} else {
    		Ext.Msg.confirm("请确认", "是否要恢复一级分类?",
    				function(button, text) {
    			if (button == "yes") {
    				var ids = [];
    				Ext.Array.each(data,
    						function(record) {
    					var iid = record.get('iid');
    					var status = record.get('state');
                        if(0==status){
                        Ext.Msg.alert('提示', '该类别已经是使用中状态！');	
                        return;
                        }
    					// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
    					if (iid) {
    						ids.push(iid);
    					}
    				});
    				if (ids.length == 0) {
    					bsManagerStore.reload();
    					return;
    				}
    				Ext.Ajax.request({
    					url: 'updateBs.do',
    					params: {
    						updateIds: ids.join(',')
    					},
    					method: 'POST',
    					success: function(response, opts) {
    						var success = Ext.decode(response.responseText).success;
    						// 当后台数据同步成功时
    						if (success) {
    							bsManagerStore.reload();
    							Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
    						} else {
    							Ext.Msg.alert('提示', '恢复失败！');
    						}
    					},
    					failure: function(result, request) {
    						secureFilterRs(result, "操作失败！");
    					}
    				});
    			}
    		});
    	}
    }
    function deleteBsManager() {
        var data = bsManagerGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除一级分类?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
//                    var flags=0;
                    Ext.Array.each(data,
                    function(record) {
                        var iid = record.get('iid');
                        var status = record.get('state');
                        if(1==status){
                        Ext.Msg.alert('提示', '该类别已经是被删除状态！');	
                        return;
                        }
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
//                        	if (iid!=1 && iid!=2&& iid!=3&& iid!=4&& iid!=5&& iid!=6){
                        		ids.push(iid);
//                        	}else{
//                        		flags=1;
//                        	}
                        }else{
                        	 bsManagerStore.remove(record);
                             }
                    });
//                    if (ids.length == 0) {
//                    	/*if(flags==1){
//                    		 Ext.Msg.alert('提示', '特定类别不可删除！');
//                    	}*/
//                        bsManagerStore.reload();
//                        return;
//                    }
                    if(ids.length>0){
                        Ext.Ajax.request({
                        url: 'deleteBsModel.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                bsManagerStore.reload();
//                                if(flags==1){
//                                	Ext.Msg.alert('提示', "特定类别不可删除,其他类别"+Ext.decode(response.responseText).message);
//                                }else{
                                	Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
//                                }
                            } else {
                                Ext.Msg.alert('提示', '删除失败！');
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    }else{
                    bsManagerGrid.getView().refresh();
                    }
                }
            });
        }
    }

});

function showbsTypeWindow(bsManagerId,state) {
	if (bsManagerId == -1) {
		Ext.Msg.alert('提示', '请先保存类别后再配置类型。');
		return;
	}
	if (state==1){
		setMessage("状态为“被删除”时，不可配置类型!");
		}else{
			if (bsTypeWindow == undefined || !bsTypeWindow.isVisible()) {
				bsTypeWindow = Ext.create('Ext.window.Window', {
					title: '二级分类',
					modal: true,
					closeAction: 'destroy',
					constrain: true,
					autoScroll: true,
					width: contentPanel.getWidth(),
					height: contentPanel.getHeight(),
					draggable: false,
					// 禁止拖动
					resizable: false,
					// 禁止缩放
					layout: 'fit',
					loader: {
						url: 'forwardBsType.do',
						params: {
							bsManagerId: bsManagerId
						},
						autoLoad: true,
						scripts: true
					}
				});
			}
			bsTypeWindow.show();
	}
}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}