<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<html>
<head>
<script type="text/javascript">
var report_classData='<%=request.getAttribute("classData")%>';
var report_col='<%=request.getAttribute("col")%>';
var report_coldate='<%=request.getAttribute("coldate")%>';
var report_startDate='<%=request.getAttribute("startDate")%>';
var report_endDate='<%=request.getAttribute("endDate")%>';
var report_collectValue='<%=request.getAttribute("collectValue")%>';
var report_flag='<%=request.getAttribute("flag")%>';
var report_sqlContent='<%=request.getAttribute("sqlContent")%>';
var fieldsData_R = [];
var listData_R = [];
var columnsData_R = [{ text: '序号', xtype:'rownumberer', width: 70 }];
</script>
<%
List listCol_R = (List)request.getAttribute("listCol");
%>
<script type="text/javascript">
<%
for(int i = 0;i<listCol_R.size();i++){
	Map map = (Map)listCol_R.get(i);
%>
	var fieldsRow_R = {};
	fieldsRow_R.name = '<%=(String)map.get("colValue")%>';
	fieldsRow_R.type = 'string';
	fieldsData_R.push(fieldsRow_R);
	var columnsRow_R = {};
	columnsRow_R.text = '<%=(String)map.get("colName")%>';
	columnsRow_R.dataIndex = '<%=(String)map.get("colValue")%>';
	columnsRow_R.width = '200';
	columnsData_R.push(columnsRow_R);
<%
}
%>

</script>
</head>
<body>
<div id="generalSubCreateReportDir" style="width: 100%;height: 100%"></div>
</body>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/generalDefine/generalSubCreateReportDir.js"></script>
</html>