Ext
		.onReady(function() {
			var editor;
			var sqlContent;
			var sqlPanel = Ext
					.create(
							'Ext.panel.Panel',
							{
								border : true,
								region : 'center',
								minHeight : 80,
								title : "编辑框-SQL定义...",
								html : '<textarea id="code-generalArea" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
							});
			var btnform = new Ext.form.FormPanel({
				region : 'north',
				border : false,
				dockedItems : [
						{
							xtype : 'toolbar',
							border : false,
							dock : 'top',
							items : [ '->', {
										xtype : 'button',
										cls : 'Common_Btn',
										text : 'SQL分析',
										handler : function() {
											execute();
										}
									} ]
						}]
			});
			var col = 1;
			var colStore = Ext.create('Ext.data.Store', {
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'queryGeneralColDir.do',
					actionMethods : 'post',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				},
				fields : [ 'colId', 'colName' ]
			});
			var classColStore = Ext.create('Ext.data.Store', {
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'queryGeneralClassColDataDir.do',
					actionMethods : 'post',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				},
				fields : [ 'colId', 'colName' ]
			});
			var classValueCombo = Ext.create('Ext.form.field.ComboBox', {
				store : classColStore,
				labelWidth : 65,
				fieldLabel : '分组数据',
				editable : true,
				queryMode : 'local',
				width : '23%',
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				multiSelect : true,// 启用多选
				displayField : 'colName',
				valueField : 'colId',
				triggerAction : "all"
			});
			var classCombo = Ext.create('Ext.form.field.ComboBox', {
				store : colStore,
				queryMode : 'local',
				fieldLabel : '分类字段',
				labelWidth : 65,
				width : '23%',
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				displayField : 'colName',
				valueField : 'colId',
				triggerAction : "all",
				listeners : {
					select : function(combo, records, options) {
						classValueCombo.clearValue();
						classValueCombo.applyEmptyText();
						classColStore.load({
							params : {
								col : this.value,
								sqlContent : document.getElementById('code-generalArea').value
							}
						});
					},
					change : function() { // old is keyup
						classValueCombo.clearValue();
						classValueCombo.applyEmptyText();
						// classValueCombo.getPicker().getSelectionModel().doMultiSelect([],
						// false);
						classColStore.load({
							params : {
								col : this.value,
								sqlContent:document.getElementById('code-generalArea').value
							}
						});
					}
				}
			});
			var collectCombo = Ext.create('Ext.form.field.ComboBox', {
				store : colStore,
				queryMode : 'local',
				fieldLabel : '采集字段',
				labelWidth : 65,
				width : '23%',
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				displayField : 'colName',
				valueField : 'colId',
				triggerAction : "all",
				listeners : {}
			});
			var timeCombo = Ext.create('Ext.form.field.ComboBox', {
				store : colStore,
				queryMode : 'local',
				labelWidth : 65,
				fieldLabel : '时间字段',
				width : '23.2%',
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				displayField : 'colName',
				valueField : 'colId',
				triggerAction : "all",
				listeners : {
					select : function(combo, records, options) {
					}
				}
			});

			var startDate = Ext.create('Go.form.field.DateTime', {
				fieldLabel : '起始日期',
				format : 'Y-m-d',
				labelWidth : 65,
				width : '23%'
			});
			var endDate = Ext.create('Go.form.field.DateTime', {
				fieldLabel : '截至日期',
				format : 'Y-m-d',
				labelWidth : 65,
				width : '23%'
			});

			var form = new Ext.form.FormPanel({
				region : 'north',
				// collapsible: true,
				// collapsed: true ,
				border : false,
				dockedItems : [
						{
							xtype : 'toolbar',
							border : false,
							items : [ classCombo, classValueCombo,
									collectCombo]
						}, {
							xtype : 'toolbar',
							border : false,
							items : [timeCombo,startDate, endDate, {
								xtype : 'button',
								cls : 'Common_Btn',
								text : '查询',
								handler : function() {
									query();
								}
							} ]
						} ]
			});

			var tabPanelCollectSub = Ext
					.create(
							'Ext.tab.Panel',
							{
								tabPosition : 'top',
								region : 'center',
								activeTab : 0,
								border : false,
								defaults : {
									autoScroll : false
								},
								items : [
										{
											hidden : true
										},
										{
											title : '表格',
											border : false,
											id : 'general_collectPanel',
											loader : {
												url : 'detailSubGeneralReportDir.do?tmp='
														+ new Date().getTime(),
												autoLoad : false,
												loadMask : true,
												scripts : true
											},
											listeners : {
												activate : function(tab) {
													$('#generalSubCreateReportDir').each(
														function() {
															$(this).remove();
														});

													var classData1 = classValueCombo
															.getValue();
													var collectValue1 = collectCombo
															.getValue();
													var col1 = classCombo
															.getValue();
													var coldate1 = timeCombo
															.getValue();
													var startDate1 = startDate
															.getRawValue();
													var endDate1 = endDate
															.getRawValue();
													tab.loader
															.load({
																params : {
																	classData : classData1,
																	collectValue : collectValue1,
																	col : col1,
																	coldate : coldate1,
																	startDate : startDate1,
																	endDate : endDate1,
																	sqlContent :sqlContent,
																	flag : col
																}
															});
												}
											}
										},
										{
											title : '图表',
											border : false,
											id : 'general_charPanel',
											loader : {
												url : 'detailSubGeneralReportChart.do',
												autoLoad : false,
												loadMask : true,
												scripts : true
											},
											listeners : {
												activate : function(tab) {
													$('#generalSubCreateChartReportDir').each(
														function() {
															$(this).remove();
														});
													var classData1 = classValueCombo
															.getValue();
													var collectValue1 = collectCombo
															.getValue();
													var col1 = classCombo
															.getValue();
													var coldate1 = timeCombo
															.getValue();
													var startDate1 = startDate
															.getRawValue();
													var endDate1 = endDate
															.getRawValue();
													var runFlag = false;
													if (col1 == ''
															|| col1 == null) {
														runFlag = true;
													}
													if (coldate1 == ''
															|| coldate1 == null) {
														runFlag = true;
													}
													if (collectValue1 == ''
															|| collectValue1 == null) {
														runFlag = true;
													}
													if (startDate1 == ''
															|| startDate1 == null) {
														runFlag = true;
													}
													if (endDate1 == ''
															|| endDate1 == null) {
														runFlag = true;
													}
													var sqlContent1 = sqlContent;
													if (trim(sqlContent1) == '' || (null == sqlContent1)) {
														runFlag = true;
													}
													if (!runFlag) {
														tab.loader
																.load({
																	params : {
																		flag : 1,
																		classData : classData1,
																		collectValue : collectValue1,
																		col : col1,
																		coldate : coldate1,
																		startDate : startDate1,
																		endDate : endDate1,
																		sqlContent : sqlContent1
																	}
																});
													}
												}
											}
										} ]
							});
			tabPanelCollectSub.setActiveTab(1); // 激活Tab 页
			var centerPanel = Ext.create('Ext.panel.Panel', {
				border : false,
				region : 'center',
				split : true,
				layout : {
					type : 'border'
				},
				cls:' panel_space_left',
				items : [ form, tabPanelCollectSub ]
			});

			var westPanel = Ext.create('Ext.panel.Panel', {
				region : 'west',
				width : '40%',
				layout : 'border',
				split : true,
				collapsible : true,
				 cls:'customize_panel_back',
				collapsed : true,
				items : [btnform,sqlPanel ]
			});
			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "generalCreate_Dir",
				layout : 'border',
				bodyCls : 'service_platform_bodybg',
				border : false,
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				items : [ westPanel, centerPanel ]
			});
			editor = CodeMirror.fromTextArea(document
					.getElementById('code-generalArea'),
					{
						mode : 'sql',
						lineNumbers : true,
						border:false,
						matchBrackets : true,
						extraKeys : {
							"F11" : function(cm) {
								cm.setOption("fullScreen", !cm
										.getOption("fullScreen"));
							},
							"Esc" : function(cm) {
								if (cm.getOption("fullScreen"))
									cm.setOption("fullScreen", false);
							}
						}
					});
			editor.setSize('100%', contentPanel
						.getHeight()
						- modelHeigth-151);
			editor.setOption("mode", 'text/x-plsql');
			editor.setOption('value', "");
			// editor.save();
			function setMessage(msg) {
				Ext.Msg.alert('提示', msg);
			}
			contentPanel.on('resize', function() {
				editor.getDoc().clearHistory();
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
				editor.setSize('100%', contentPanel
						.getHeight()
						- modelHeigth-151);
			});
			function execute() {
				editor.save();
				var content = document.getElementById('code-generalArea').value;
				var jsonData = "";
				if (trim(content) == '' || (null == content)) {
					Ext.MessageBox.alert("提示", "请输入SQL");
					return false;
				} else {
					jsonData = content;
				}
				
				Ext.Ajax.request({
					url : 'execSqlCheck.do',
					method : 'POST',
					sync : true,
					timeout:6000000000,
					params : {
						jsonData:jsonData
					},
					success : function(response, request) {
						var success=Ext.decode(response.responseText).success;
						var message=Ext.decode(response.responseText).message;
						if(success){
							colStore.load({
								params : {
									jsonData : jsonData
								}
							});
						}else{
							colStore.load();
						}
						Ext.MessageBox.alert("提示", message);
					},
					failure : function(result, request) {
						secureFilterRs(result, "出现错误！");
					}
				});
			}
			
			function getData(classData1,collectValue1,col1,coldate1,startDate1,endDate1,sqlContent){
				Ext.Ajax.request({
					url : 'getGeneralReportGraphAndListDir.do',
					method : 'POST',
					sync : true,
					timeout:6000000000,
					params : {
						  classChkData:classData1,
						  collectValue:collectValue1,
						  col:col1,
						  coldate:coldate1,
						  startDate:startDate1,
						  endDate:endDate1,
						  sqlContent:sqlContent
					},
					success : function(response, request) {
						var graph=Ext.decode(response.responseText).graphData;
						var seriesList = graph.seriesData;
						var overTimeChart = echarts.init(document.getElementById("general_charPanel"));
						var seri=[];						
			            for(var t = 0; t < seriesList.length; t++){
			                var series = seriesList[t];
			                var item={
			                		name:series.name,
			                        type:'line',
			                        data:series.data,
			                        markPoint : {
			                            data : [
			                                {type : 'max', name: '最大值'},
			                                {type : 'min', name: '最小值'}
			                            ]
			                        },
			                        markLine : {
			                            data : [
			                                {type : 'average', name: '平均值'}
			                            ]
			                        }
			                };
			                seri.push(item);
			            }
						var option = {
						    tooltip : {
						        trigger: 'axis'
						    },
						    color:["#5168fc","#f57242"],
						    legend: {
						    	//动态数据 注意此处构建的后台数据为list数组
						        data:graph.legendData
						    },
						    toolbox: {
						        show : true,
						        feature : {
						            mark : {show: true},
						           // dataView : {show: true, readOnly: false},
						            magicType : {show: true, type: ['line', 'bar',]},
						            restore : {show: true}//,
						            //saveAsImage : {show: true}
						        }
						    },
						    calculable : true,
						    xAxis : [
						        {
						            type : 'category',
						            boundaryGap : false,
						            //动态数据
						            data : graph.xAxisData
						        }
						    ],
						    yAxis : [
						        {
						            type : 'value',
						            axisLabel : {
						                formatter: '{value}'
						            }
						        }
						    ],
						  	//动态数据
						    series :  seri
						};
						overTimeChart.setOption(option,true); 
					},
					failure : function(result, request) {
						secureFilterRs(result, "出现错误！");
					}
				});
			}
			
			function query(){
				var classData1=classValueCombo.getValue();
				var collectValue1=collectCombo.getValue();
				var col1=classCombo.getValue();
				var coldate1=timeCombo.getValue();
				var startDate1=startDate.getRawValue();
				var endDate1=endDate.getRawValue();
				var vara=false;
				var varb=false;
				var varc=false;
					
				if (col1 == ''||col1 ==null) {
					vara=true;
//					Ext.MessageBox.alert("提示", "分组字段不能为空,数据来源于SQL定义中的SQL分析!");
//					return;
				}
//				if (coldate1 == ''||coldate1 ==null) {
//					varb=true;
//					Ext.MessageBox.alert("提示", "时间字段不能为空!");
//					return;
//				}
				if (collectValue1 == ''||collectValue1 ==null) {
					varc=true;
//					Ext.MessageBox.alert("提示", "采集字段不能为空,数据来源于SQL定义中的SQL分析!");
//					return;
				}
				if(vara && !varc){
					Ext.MessageBox.alert("提示", "选择分组字段，采集字段不能为空!");
					return;
				}
				if(!vara && varc){
					Ext.MessageBox.alert("提示", "选择采集字段，分组字段不能为空!");
					return;
				}
//				if (startDate1 == ''||startDate1 ==null) {
//					Ext.MessageBox.alert("提示", "起始日期不能为空!");
//					return;
//				}
//				if (endDate1 == ''||endDate1 ==null) {
//					Ext.MessageBox.alert("提示", "截止日期不能为空!");
//					return;
//				}
				tabPanelCollectSub.setActiveTab (2);
				col=0;
				var content = document.getElementById('code-generalArea').value;
				var jsonData = "";
				if (trim(content) == '' || (null == content)) {
					Ext.MessageBox.alert("提示", "请输入SQL");
					return false;
				} else {
					jsonData = content;
				}
				sqlContent=jsonData;
				getData(classData1,collectValue1,col1,coldate1,startDate1,endDate1,jsonData);
				tabPanelCollectSub.setActiveTab (1);
				}
		})
