Ext.onReady(function() {
		var tableData_store = Ext.create('Ext.data.Store', {
			fields: fieldsData,
			data :listData
	    });
	    var tableData_columns = columnsData;
	    var tablePanel = Ext.create('Ext.grid.Panel', {
		    store:tableData_store,
		    padding : panel_margin,
			border:true,
		    columnLines : true,
			height : 220,
		    columns:tableData_columns
		});
	var rightPanel = Ext.create('Ext.panel.Panel', {
		border:false,
		region : 'center',
		split : true,
		items : [ tablePanel]
	});
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "generalSubCreate",
		layout : 'border',
		bodyCls:'service_platform_bodybg',
		border : false,
		height : 220,
		items : [rightPanel ]
	});
})
