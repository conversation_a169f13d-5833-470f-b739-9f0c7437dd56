Ext.onReady(function() {
var col=1;
var colStore = Ext.create('Ext.data.Store', {
	autoLoad : true,
	proxy : {
		type : 'ajax',
		url : 'queryGeneralCol.do',
		actionMethods : 'post',
		reader : {
			type : 'json',
			root : 'dataList'
		}
	},
	fields : [ 'colId', 'colName' ]
});
var classColStore = Ext.create('Ext.data.Store', {
	autoLoad : false,
	proxy : {
		type : 'ajax',
		url : 'queryGeneralClassColData.do',
		actionMethods : 'post',
		reader : {
			type : 'json',
			root : 'dataList'
		}
	},
	fields : [ 'colId', 'colName' ]
});
var classValueCombo = Ext.create('Ext.form.field.ComboBox', {
	store : classColStore,
	labelWidth : 65,
	fieldLabel : '分组数据',
	editable: true,
	queryMode : 'local',
	width : '23%',
	forceSelection : true, // 要求输入值必须在列表中存在
	typeAhead : true, // 允许自动选择
	multiSelect: true,//启用多选
	displayField : 'colName',
	valueField : 'colId',
	triggerAction : "all"
});
var classCombo = Ext.create('Ext.form.field.ComboBox', {
	store : colStore,
	queryMode : 'local',
	fieldLabel : '分类字段',
	labelWidth : 65,
	width : '23%',
	forceSelection : true, // 要求输入值必须在列表中存在
	typeAhead : true, // 允许自动选择
	displayField : 'colName',
	valueField : 'colId',
	triggerAction : "all",
	listeners : {
		select : function(combo, records, options) {
			classValueCombo.clearValue();
			classValueCombo.applyEmptyText();
			classColStore.load({
				params : {
					col : this.value,
					templateId:col_iid_temp
				}
			});
		},change : function() { // old is keyup
			classValueCombo.clearValue();
			classValueCombo.applyEmptyText();
//						classValueCombo.getPicker().getSelectionModel().doMultiSelect([], false);
			classColStore.load({
				params : {
					col : this.value,
					templateId:col_iid_temp
				}
			});
		}
	}
});
var collectCombo = Ext.create('Ext.form.field.ComboBox', {
	store : colStore,
	queryMode : 'local',
	fieldLabel : '采集字段',
	labelWidth : 65,
	width : '23%',
	forceSelection : true, // 要求输入值必须在列表中存在
	typeAhead : true, // 允许自动选择
	displayField : 'colName',
	valueField : 'colId',
	triggerAction : "all",
	listeners : {
	}
});
var timeCombo = Ext.create('Ext.form.field.ComboBox', {
	store : colStore,
	queryMode : 'local',
	labelWidth : 65,
	fieldLabel : '时间字段',
	width : '23%',
	forceSelection : true, // 要求输入值必须在列表中存在
	typeAhead : true, // 允许自动选择
	displayField : 'colName',
	valueField : 'colId',
	triggerAction : "all",
	listeners : {
		select : function(combo, records, options) {
		}
	}
});
colStore.on('beforeload', function(store, options) {
	var new_params = {
		templateId : col_iid_temp
	};
	Ext.apply(colStore.proxy.extraParams, new_params);
});
var startDate = Ext.create('Go.form.field.DateTime', {
	fieldLabel : '起始日期',
	format : 'Y-m-d',
	labelWidth : 65,
	width : '23%',
	value:new Date()
});
var endDate = Ext.create('Go.form.field.DateTime', {
	fieldLabel : '截至日期',
	format : 'Y-m-d',
	labelWidth : 65,
	width : '23%',
	value:new Date()
});
var backButton = Ext.create("Ext.Button", {
	cls : 'Common_Btn',
	text : "返回",
	handler : back
});
			
var form = new Ext.form.FormPanel({
	region: 'north',
	bodyPadding : 5,
	baseCls:'customize_gray_back',
//	collapsible: true,
//	collapsed: true ,
	border : false,
	dockedItems : [ {
		xtype : 'toolbar',
		border : false,
		baseCls:'customize_gray_back',
		dock : 'top',
		items : [classCombo,classValueCombo,collectCombo,timeCombo]
	}, {
		xtype : 'toolbar',
		border : false,
		baseCls:'customize_gray_back',
		items : [startDate,endDate, {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '查询',
            handler: function() {
            	query();
            }
        },backButton ]
	}]
});

var charPanel = Ext.create('Ext.panel.Panel', {
	region : 'center',
	id:'general_charPanel',
	height:'100%',
	border : false
//	,
//	loader : {
//		url : 'detailSubGeneralReport.do?tmp=' + new Date().getTime()+"&templateId="+col_iid_temp,
//		autoLoad : false,
//		scripts : true
//	}
});
var tablePanel = Ext.create('Ext.panel.Panel', {
	region : 'north',
	height:'92%',
	collapsible: true,
	collapsed: false ,
	border : false,
	loader : {
		url : 'detailSubGeneralReport.do?flag=1&tmp=' + new Date().getTime()+"&templateId="+col_iid_temp,
		autoLoad : false,
		scripts : true
	}
});

var autoCharPanel = Ext.create('Ext.panel.Panel', {
	border : true,
	region : 'center',
	 layout: {
         type: 'border'
     },
	split : true,
	items : [ tablePanel,charPanel]
});

var tabPanelCollectSub = Ext.create ('Ext.tab.Panel',
		{
		    tabPosition : 'top',
		    region : 'center',
		    activeTab : 0,
		    width : '100%',
		    height : contentPanel.getHeight () - 120,
		    border : false,
		    defaults :
		    {
			    autoScroll : false
		    },
		    items : [
		            {
			            hidden : true
		            },
		            {
		                title : '表格',
		                border : false,
		                id:'general_collectPanel',
		                loader :
		                {
		                    url : 'detailSubGeneralReport.do?tmp=' + new Date().getTime(),
		                    autoLoad : false,
		                    loadMask : true,
		                    scripts : true
		                },
		                listeners :
		                {
			                activate : function (tab)
			                {
			                	$('#generalSubCreateReport').each(function() {
			                	    $(this).remove();
			                	});
			                	
			                	var classData1=classValueCombo.getValue();
			                	var collectValue1=collectCombo.getValue();
			                	var col1=classCombo.getValue();
			                	var coldate1=timeCombo.getValue();
			                	var startDate1=startDate.getRawValue();
			                	var endDate1=endDate.getRawValue();
				                tab.loader.load (
				                {
					                params :
					                {
					                	  classData:classData1,
						       			  collectValue:collectValue1,
						       			  col:col1,
						       			  coldate:coldate1,
						       			  startDate:startDate1,
						       			  endDate:endDate1,
	 				          			  templateId:col_iid_temp,
					                	  flag:col
					                }
				                });
			                }
		                }
		            },
		            {
		                title : '图表',
		                border : false,
		                id:'general_charPanel',
		                loader :
		                {
		                    url : 'detailSubGeneralReport.do',
		                    autoLoad : false,
		                    loadMask : true,
		                    scripts : true
		                },
		                listeners :
		                {
			                activate : function (tab)
			                {
			                	$('#generalSubCreateChartReport').each(function() {
			                	    $(this).remove();
			                	});
			                	var classData1=classValueCombo.getValue();
			                	var collectValue1=collectCombo.getValue();
			                	var col1=classCombo.getValue();
			                	var coldate1=timeCombo.getValue();
			                	var startDate1=startDate.getRawValue();
			                	var endDate1=endDate.getRawValue();
			                	var runFlag=false;
			                	if (col1 == ''||col1 ==null) {
			                		runFlag=true;
			                	}
			                	if (coldate1 == ''||coldate1 ==null) {
			                		runFlag=true;
			                	}
			                	if (collectValue1 == ''||collectValue1 ==null) {
			                		runFlag=true;
			                	}
			                	if (startDate1 == ''||startDate1 ==null) {
			                		runFlag=true;
			                	}
			                	if (endDate1 == ''||endDate1 ==null) {
			                		runFlag=true;
			                	}
			                	if(!runFlag){
			                		 tab.loader.load (
			 				                {
			 				                	params: {
			 				          			  flag:1,
			 				          			classData:classData1,
								       			  collectValue:collectValue1,
								       			  col:col1,
								       			  coldate:coldate1,
								       			  startDate:startDate1,
								       			  endDate:endDate1,
			 				          			  templateId:col_iid_temp,
			 				                      },
			 				                });
			                	}
			                }
		                }
		            }
		    ]
		});
tabPanelCollectSub.setActiveTab (1); // 激活Tab 页

var mainPanel = Ext.create('Ext.panel.Panel', {
	renderTo : "generalExternalReport_div",
	layout : 'border',
	bodyCls : 'service_platform_bodybg',
	bodyPadding : grid_margin,
	border : true,
	width : contentPanel.getWidth(),
	height : contentPanel.getHeight() - modelHeigth - 20,
	items : [ form, tabPanelCollectSub]
});
function back() {
	destroyRubbish();
	contentPanel.getLoader().load({
		url : 'generalList.do',
			scripts : true
		});
	}

function getData(classData1,collectValue1,col1,coldate1,startDate1,endDate1){
	Ext.Ajax.request({
		url : 'getGeneralReportGraphAndList.do',
		method : 'POST',
		sync : true,
		timeout:6000000000,
		params : {
			  classChkData:classData1,
			  collectValue:collectValue1,
			  col:col1,
			  coldate:coldate1,
			  startDate:startDate1,
			  endDate:endDate1,
			  templateId:col_iid_temp
		},
		success : function(response, request) {
			var graph=Ext.decode(response.responseText).graphData;
			var msg = graph.msg;
			if(msg.length>0) {
			    Ext.MessageBox.alert("提示", msg);
			}
			var seriesList = graph.seriesData;
			var overTimeChart = echarts.init(document.getElementById("general_charPanel"));
			var seri=[];
            for(var t = 0; t < seriesList.length; t++){
                var series = seriesList[t];
                var item={
                		name:series.name,
                        type:'line',
                        data:series.data,
                        markPoint : {
                            data : [
                                {type : 'max', name: '最大值'},
                                {type : 'min', name: '最小值'}
                            ]
                        },
                        markLine : {
                            data : [
                                {type : 'average', name: '平均值'}
                            ]
                        }
                };
                seri.push(item);
            }
			var option = {
			    tooltip : {
			        trigger: 'axis'
			    },
			    color:["#5168fc","#f57242"],
			    legend: {
			    	//动态数据 注意此处构建的后台数据为list数组
			        data:graph.legendData
			    },
			    toolbox: {
			        show : true,
			        feature : {
			            mark : {show: true},
			           // dataView : {show: true, readOnly: false},
			            magicType : {show: true, type: ['line', 'bar']},
			            restore : {show: true}//,
			            //saveAsImage : {show: true}
			        }
			    },
			    calculable : true,
			    xAxis : [
			        {
			            type : 'category',
			            boundaryGap : false,
			            //动态数据
			            data : graph.xAxisData
			        }
			    ],
			    yAxis : [
			        {
			            type : 'value',
			            axisLabel : {
			                formatter: '{value}'
			            }
			        }
			    ],
			  	//动态数据
			    series :  seri
			};
			overTimeChart.setOption(option,true); 
		},
		failure : function(result, request) {
			secureFilterRs(result, "出现错误！");
		}
	});
}

function query(){
	var classData1=classValueCombo.getValue();
	var collectValue1=collectCombo.getValue();
	var col1=classCombo.getValue();
	var coldate1=timeCombo.getValue();
	var startDate1=startDate.getRawValue();
	var endDate1=endDate.getRawValue();
	var templateId=col_iid_temp;
	if (col1 == ''||col1 ==null) {
		Ext.MessageBox.alert("提示", "分组字段不能为空!");
		return;
	}
	if (coldate1 == ''||coldate1 ==null) {
		Ext.MessageBox.alert("提示", "时间字段不能为空!");
		return;
	}
	if (collectValue1 == ''||collectValue1 ==null) {
		Ext.MessageBox.alert("提示", "采集字段不能为空!");
		return;
	}
	if (startDate1 == ''||startDate1 ==null) {
		Ext.MessageBox.alert("提示", "起始日期不能为空!");
		return;
	}
	if (endDate1 == ''||endDate1 ==null) {
		Ext.MessageBox.alert("提示", "截止日期不能为空!");
		return;
	}
	tabPanelCollectSub.setActiveTab (2);
	col=0;
	getData(classData1,collectValue1,col1,coldate1,startDate1,endDate1);
	tabPanelCollectSub.setActiveTab (1);
	}
})
