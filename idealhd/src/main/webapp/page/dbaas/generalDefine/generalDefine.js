Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'ICOLLEC_TYPE',
            type: 'string'
        },
        {
            name: 'ICOLLEC_CONTENT',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IUSER',
            type: 'string'
        },
        {
            name: 'IPWD',
            type: 'string'
        },
        {
            name: 'IINSTANCE',
            type: 'string'
        },
        {
            name: 'ICREATETIME',
            type: 'string'
        },
        {
            name: 'IUPDATETIME',
            type: 'string'
        },
        {
            name: 'ISTATUS',
            type: 'string'
        }
        ]
    });
  var  dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 16,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'dataGatheringList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
  dataSourceStore.on('beforeload', function(store, options) {
      var new_params = {
  		searchBusiness: searchBusiness.getValue(),
  		searchIp: searchIp.getValue()
      };
      Ext.apply(dataSourceStore.proxy.extraParams, new_params);
  });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        width: 80,
        flex:1,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '采集类型',
        dataIndex: 'ICOLLEC_TYPE',
        width: 80,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = '数据库';
			} else if (value == '2') {
				backValue = '文件';
			} 
			return backValue;
		}
    },
    {
        text: '脚本内容',
        dataIndex: 'ICOLLEC_CONTENT',
        width: 260,
        minWidth: 200,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	text: 'IP',
        dataIndex: 'IIP',
        width: 120,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	text: '用户名',
        dataIndex: 'IUSER',
        width: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    /*{
    	text: '密码',
        dataIndex: 'IPWD',
        width: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },*/
    {
    	text: 'SID',
        dataIndex: 'IINSTANCE',
        width: 80,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	text: '创建时间',
        dataIndex: 'ICREATETIME',
        width: 160,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	text: '修改时间',
        dataIndex: 'IUPDATETIME',
        width: 160,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '运行状态',////0未运行，1运行中，2异常，3完成
        dataIndex: 'ISTATUS',
        width: 80,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '0') {
				backValue = "<span class='Not_running State_Color'>未运行</span>";
			} else if (value == '1') {
				backValue = "<span class='Abnormal_Complete_purple State_Color'>运行中</span>";
			} else if (value == '2') {
				backValue = "<span class='Abnormal_yellow State_Color'>异常</span>";
			} else if (value == '3') {
				backValue = "<span class='Complete_Green State_Color'>完成</span>";
			}
			return backValue;
		}
    },
    {
        text: '操作',
        width: 80,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('IID');   
            var search_ip=searchIp.getValue();
            var search_business=searchBusiness.getValue();
            return '<div>' + '<a href="javascript:void(0)" onclick="update('+iid+',\''+search_ip+'\',\''+search_business+'\')">' + '&nbsp;编辑' + '</a>' + '</div>';
        }
    }];
    // 分页工具
   var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        displayInfo: true,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var searchIp = Ext.create("Ext.form.field.Text", {
        fieldLabel: 'IP',
        labelWidth: 30,
        name: 'searchIp',
        labelAlign : 'right',
        width : '20%',
        value:filter_searchIp,
        listeners:{
	        specialkey: function(field, e){
	            if (e.getKey() == e.ENTER) {
	            	pageBar.moveFirst();
	            }
	        }
	    }
    });
    var searchBusiness = Ext.create("Ext.form.field.Text", {
        fieldLabel: '业务系统',
        labelWidth: 65,
        labelAlign : 'right',
        name: 'searchBusiness',
        width : '25%',
        value:filter_searchBusiness,
        listeners:{
	        specialkey: function(field, e){
	            if (e.getKey() == e.ENTER) {
	            	pageBar.moveFirst();
	            }
	        }
	    }
    });
    
	var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		baseCls: 'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls: 'customize_gray_back',
			border : false,
			dock : 'top',
			items : [
	            searchIp,searchBusiness,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            },'->',
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '创建',
	                handler: function() {
	                	create();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '删除',
	                handler: function() {
	                	deleteDataManage();
	                }
	            }]
			}]
	});
    
  var  dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	padding : panel_margin,
        store: dataSourceStore,
        selModel: selModel,
        plugins: [cellEditing],
        border: true,
        bbar: pageBar,
		columnLines: true,
		cls: 'customize_panel_back',
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		update(record.data.IID,record.data.IIP,record.data.IBUSINESS);
        	}
        }
    });
    function create(){
    	destroyRubbish();
//		contentPanel.getLoader().load({
//			url: 'createGeneral.do',
//			scripts: true
//		});
    	contentPanel.getLoader().load({
    		url: 'createGeneralCollect.do',
//        		url: 'updateGeneral.do',
    		params : {id : -1},
    		scripts: true
    	});
	}
    function deleteDataManage() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteDataGathering.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                            	dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    	dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "generalList",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyCls:'service_platform_bodybg',
        bodyPadding : grid_margin,
        border : true,
        items: [form,dataSourceGrid]
    });
    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
	    function(obj, options, eOpts) {
	        Ext.destroy(mainPanel);
	        if (Ext.isIE) {
	            CollectGarbage();
	        }
    	}
    );
    function clearQueryWhere() {
    	searchIp.setValue('');
    	searchBusiness.setValue('');
    }
});
function update(id,search_ip,search_business){
	destroyRubbish();
	contentPanel.getLoader().load({
		url: 'updateGeneralCollect.do',
//		url: 'updateGeneral.do',
		params : {id : id,
			 filter_searchIp:search_ip,
		     filter_searchBusiness:search_business,
	         },
		scripts: true
	});
}