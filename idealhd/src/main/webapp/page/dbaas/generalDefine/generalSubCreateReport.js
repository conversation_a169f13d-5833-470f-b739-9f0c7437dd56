Ext.onReady(function() {
//	var tableData_store_R = Ext.create('Ext.data.Store', {
//		fields:fieldsData_R,
//		data:listData_R
//    });
	Ext.define('Data', {
        extend: 'Ext.data.Model',
        fields: fieldsData_R
    });
	   var tableData_store_R = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        autoDestroy: true,
	        pageSize: 10000,
	        model: 'Data',
	        proxy: {
	            type: 'ajax',
	            url: 'detailSubGeneralReportData.do',
	            reader: {
	                type: 'json',
	                root: 'listData'
	            }
	        }
	    });
	   tableData_store_R.on('beforeload', function(store, options) {
	        var new_params = {
	        		templateId:report_templateId,
	        		classData:report_classData,
	        		col:report_col,
	        		coldate:report_coldate,
	        		startDate:report_startDate,
	        		endDate:report_endDate
	        };
	        Ext.apply(tableData_store_R.proxy.extraParams, new_params);
	    });
	   if(report_flag=='0'){
		   tableData_store_R.load();
	   }
    var tablePanel_R = Ext.create('Ext.grid.Panel', {
	    store:tableData_store_R,
	    padding : panel_margin,
		border:true,
	    columnLines : true,
		height : contentPanel.getHeight() - modelHeigth-230,
	    columns:columnsData_R
	});
	var rightPanel_R = Ext.create('Ext.panel.Panel', {
		border:false,
		region : 'center',
		split : true,
		items : [ tablePanel_R]
	});
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "generalSubCreateReport",
		layout : 'border',
		bodyCls:'service_platform_bodybg',
		border : false,
		height : contentPanel.getHeight() - modelHeigth-230,
		items : [rightPanel_R]
	});
})
