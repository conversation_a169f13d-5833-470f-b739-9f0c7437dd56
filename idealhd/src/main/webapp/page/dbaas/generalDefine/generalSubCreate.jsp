<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<html>
<head>
<%
List listCol = (List)request.getAttribute("listCol");
List listData = (List)request.getAttribute("listData");
%>
<script type="text/javascript">
var fieldsData = [];
var listData = [];
var columnsData = [{ text: '序号', xtype:'rownumberer', width: 70 }];
<%
for(int i = 0;i<listCol.size();i++){
	Map map = (Map)listCol.get(i);
%>
	var fieldsRow = {};
	fieldsRow.name = '<%=(String)map.get("colValue")%>';
	fieldsRow.type = 'string';
	fieldsData.push(fieldsRow);
	var columnsRow = {};
	columnsRow.text = '<%=(String)map.get("colName")%>';
	columnsRow.dataIndex = '<%=(String)map.get("colValue")%>';
	columnsRow.flex = 1;
	columnsData.push(columnsRow);
	listData=<%=listData%>;
<%
}
%>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/generalDefine/generalSubCreate.js"></script>
</head>
<body>
<div id="generalSubCreate" style="width: 100%;height: 100%"></div>
</body>
</html>