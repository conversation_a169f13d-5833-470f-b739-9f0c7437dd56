Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
	var tabPanelCollect = Ext.create ('Ext.tab.Panel',
			{
			    tabPosition : 'top',
			    region : 'center',
			    activeTab : 0,
			    width : '100%',
			    height : contentPanel.getHeight () - 38,
			    border : false,
			    defaults :
			    {
				    autoScroll : false
			    },
			    items : [
			            {
				            hidden : true
			            },
			            {
			                title : '数据采集',
			                border : false,
			                loader :
			                {
			                    url : 'updateGeneral.do',
			                    autoLoad : false,
			                    loadMask : true,
			                    params : {
			                    	id:coll_iid,
			        				'filter_searchIpForgeneralDefineCollect':filter_searchIpForgeneralDefineCollect,
			        				'filter_searchBusinessForgeneralDefineCollect':filter_searchBusinessForgeneralDefineCollect
			        			},
			                    scripts : true
			                },
			                listeners :
			                {
				                activate : function (tab)
				                {
				                	$('#generalSubCreate').each(function() {
				                	    $(this).remove();
				                	});
					                tab.loader.load (
					                {
						                params :
						                {
						                	id : coll_iid
						                }
					                });
				                }
			                }
			            },
			            {
			                title : '数据展示',
			                border : false,
			                loader :
			                {
			                    url : 'generalExternalReport.do',
			                    autoLoad : false,
			                    loadMask : true,
			                    scripts : true
			                },
			                listeners :
			                {
				                activate : function (tab)
				                {
				                	$('#generalSubCreateReport').each(function() {
				                	    $(this).remove();
				                	});
					                tab.loader.load (
					                {
						                params :
						                {
						                	id : coll_iid
						                }
					                });
				                }
			                }
			            }
			    ]
			});
	tabPanelCollect.setActiveTab (1); // 激活Tab 页
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "generalDefineCollect",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyCls:'service_platform_bodybg',
        border : true,
        items: [tabPanelCollect]
    });
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
	    function(obj, options, eOpts) {
	        Ext.destroy(mainPanel);
	        if (Ext.isIE) {
	            CollectGarbage();
	        }
    	}
    );

});