Ext.Loader.setConfig({
  enabled:true,
  disableCaching:false,
  paths:{
    'Go':'js/ux/gooo'
  }
});

var scriptServiceReleaseStore;
Ext.onReady(function() {
// 清理主面板的各种监听时间
	destroyRubbish();
	
	var publishAuditingSMWin;
	var auditing_form_sm;
	var auditorStore_sm;
	var auditorComBox_sm;
	var planTime_sm;
	var pubDesc_sm;
	var scriptLevelCb_sm;
	var filter;
	
	var bussData = Ext.create('Ext.data.Store', {
		fields : [ 'iid', 'bsName' ],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsAll.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var bussTypeData = Ext.create('Ext.data.Store', {
		fields : [ 'sysTypeId', 'sysType' ],
		autoLoad : false,
		proxy : {
			type : 'ajax',
			url : 'bsManager/getBsTypeByFk.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var cataData = [
        {"id":"-1", "name":"全部"},
        {"id":"sh", "name":"shell"},
        {"id":"bat", "name":"bat"},
        {"id":"py", "name":"python"},
        {"id":"perl", "name":"perl"},
        {"id":"sql", "name":"sql"}
    ];
//	if(scriptServiceScriptFlowSwitch) {
//		cataData.push({"id":"-2", "name":"组合"});
//	}
	var cataStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : cataData
	});
	var scriptStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"-1", "name":"草稿"},
			{"id":"1", "name":"已上线"},
			{"id":"3", "name":"共享"},
			{"id":"2", "name":"审核中"}
		]
	});
	var bussCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'sysName',
		labelWidth : 90,
		queryMode : 'local',
		fieldLabel : '一级分类',
//		padding : '5',
		
		displayField : 'bsName',
		valueField : 'iid',
		editable : false,
		emptyText : '--请选择一级分类--',
		store : bussData,
		width : '33.3%',
        labelAlign : 'right',
		listeners : {
			change : function() { 
				bussTypeCb.clearValue();
				bussTypeCb.applyEmptyText();
				bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
				bussTypeData.load({
					params : {
						fk : this.value
					}
				});
			},
			afterRender: function(combo) {
				if(filter_bussId!='-1' && filter_bussId!='') {
					bussCb.setValue(parseInt(filter_bussId));
				}
            }
		}
	});
	
	bussData.on('load', function (store, options) {
		if(filter_bussId!='-1'&& filter_bussId!='') {
			bussCb.setValue(parseInt(filter_bussId));
		}
    });

	/** 操作类型* */
	var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'bussType',
//		padding : '5',
		labelWidth : 90,
		queryMode : 'local',
		fieldLabel : '二级分类',
		displayField : 'sysType',
		valueField : 'sysTypeId',
		editable : false,
		hidden:true,
		emptyText : '--请选择二级分类--',
		store : bussTypeData,
		width : '33.3%',
        labelAlign : 'right'
	});
	
	/** 脚本类型* */
	var scriptTypeParam = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptTypeParam',
//		padding : '5',
		labelWidth : 90,
		queryMode : 'local',
		fieldLabel : '脚本类型',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择脚本类型--',
		store : cataStore,
		width : '33.9%',
        labelAlign : 'right',
    	listeners: {
            afterRender: function(combo) {
               if(filter_scriptType=='-1') {
					combo.setValue(cataStore.getAt(0).data.id);
				} else if(filter_scriptType=='sh'){
					combo.setValue(cataStore.getAt(1).data.id);
				}else if(filter_scriptType=='bat'){
					combo.setValue(cataStore.getAt(2).data.id);
				}else if(filter_scriptType=='py'){
					combo.setValue(cataStore.getAt(3).data.id);
				}else if(filter_scriptType=='perl'){
					combo.setValue(cataStore.getAt(4).data.id);
				}else if(filter_scriptType=='sql'){
					combo.setValue(cataStore.getAt(5).data.id);
				}//else if(filter_scriptType=='-2'){
//					combo.setValue(cataStore.getAt(6).data.id);
//				}
            }
        }
	});
	
	var scriptStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'scriptStatus',
		labelWidth : 90,
		queryMode : 'local',
		fieldLabel : '脚本状态',
		displayField : 'name',
		valueField : 'id',
//		padding : '5',
		editable : false,
		emptyText : '--请选择脚本状态--',
		store : scriptStatusStore,
		width : '33.9%',
		labelAlign : 'right',
		listeners: {
            afterRender: function(combo) {
               if(filter_scriptStatus=='-10000') {
					combo.setValue(scriptStatusStore.getAt(0).data.id);
				} else if(filter_scriptStatus=='-1'){
					combo.setValue(scriptStatusStore.getAt(1).data.id);
				}else if(filter_scriptStatus=='1'){
					combo.setValue(scriptStatusStore.getAt(2).data.id);
				}else if(filter_scriptStatus=='3'){
					combo.setValue(scriptStatusStore.getAt(3).data.id);
				}else if(filter_scriptStatus=='2'){
					combo.setValue(scriptStatusStore.getAt(4).data.id);
				}
            }
        }
	});
	var sName = new Ext.form.TextField({
		name : 'serverName',
		fieldLabel : '服务名称',
		emptyText : '--请输入服务名称--',
		labelWidth : 90,
//		padding : '5',
		width : '33.3%',
        labelAlign : 'right',
        value: filter_serviceName
        
	});
	var scName = new Ext.form.TextField({
		name : 'scriptName',
		fieldLabel : '脚本名称',
		emptyText : '--请输入脚本名称--',
		labelWidth : 90,
//		padding : '5',
		width : '33.3%',
        labelAlign : 'right',
        value: filter_scriptName
	});
	
	 var search_form = Ext.create('Ext.form.Panel', {
		 	region:'north',
	    	layout : 'anchor',
	    	buttonAlign : 'center',
	    	bodyCls : 'x-docked-noborder-top',
	    	border : false,
	    	dockedItems : [{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [sName,scName,bussCb,bussTypeCb]
			},{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items: [scriptTypeParam,scriptStatusCb,{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler : function() {
						pageBar.moveFirst();
						var filter_bussId = bussCb.getValue();
						var filter_bussTypeId = bussTypeCb.getValue();
						var filter_scriptName = scName.getValue();
						var filter_serviceName = sName.getValue();
						var filter_scriptType = scriptTypeParam.getValue();
						var filter_scriptStatus = scriptStatusCb.getValue();
						filter = {
							'filter_bussId': filter_bussId,
							'filter_bussTypeId': filter_bussTypeId,
							'filter_scriptName': filter_scriptName,
							'filter_serviceName': filter_serviceName,
							'filter_scriptType': filter_scriptType,
							'filter_scriptStatus':filter_scriptStatus
						};
					}
				},{
					xtype : 'button',
					text : '清空',
					cls : 'Common_Btn',
					handler : function() {
						clearQueryWhere();
						filter = {};
					}
				},
		    	{
			        text : '共享',
			        cls : 'Common_Btn',
			        handler : shareServiceRelease
		    	},{
			        text : '发布',
			        cls : 'Common_Btn',
			        handler : function(){
			        	var seledCnt = selModel.getCount();
			    		if(seledCnt != 1){
			    			Ext.MessageBox.alert("提示", "请选择要发布的记录，且每次只能选择一条！");
			    			return ;
			    		}
			    		var ss = selModel.getSelection();
			    		var version = ss[0].data.version;
						var hasVersion = 0;
						if(version) {
							hasVersion = 1; // 最新版本有 版本号
						}
						if(ss[0].get('isflow')=='1') {
							// 检查脚本组合里，是否有没有发布的脚本
							Ext.Ajax.request({
							    url : 'checkScriptFlowHasTestScript.do',
							    method : 'POST',
							    params : {
							  	  serviceId : ss[0].data.iid
							    },
							    success: function(response, opts) {
						    		var success = Ext.decode(response.responseText).success;
						    		if(success) {
						    			publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
						    		} else {
						    			var message = Ext.decode(response.responseText).message;
						    			Ext.MessageBox.alert("提示", "该脚本组合中，某些步骤所选择的脚本服务没有发布，请发布后重试。<br>没有发布的脚本有："+message);
						    			return;
						    		}
							    },
							    failure: function(result, request) {
							    	secureFilterRs(result,"操作失败！");
							    }
						    });
						} else {
							publishScript(ss[0].data.iid, 0,0,0,hasVersion, ss[0].data.status);
						}
			        	
			        }
		    	}, '-', {
			        text: '删除',
			        cls : 'Common_Btn',
			        handler: deleteServiceRelease
		    	}, {
			        text: '导入',
			        cls : 'Common_Btn',
			        handler: uploadExcel
		    	}
		    ]
			}]
		});
	
	Ext.define('scriptServiceReleaseModel', {
	    extend : 'Ext.data.Model',
	    fields : [ 
		    {name : 'iid'         ,type : 'long'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'sysName'     ,type : 'string'}, 
		    {name : 'bussName'    ,type : 'string'},
		    {name : 'buss'    ,type : 'string'},
		    {name : 'bussType'    ,type : 'string'},
		    {name : 'bussId'    ,type : 'int'},
		    {name : 'bussTypeId'    ,type : 'int'},
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'isflow'  ,type : 'string'}, 
		    {name : 'scriptName'  ,type : 'string'}, 
		    {name : 'servicePara' ,type : 'string'}, 
		    {name : 'serviceState',type : 'string'}, 
		    {name : 'isshare',type : 'string'},
		    {name : 'platForm',type : 'string'}, 
		    {name : 'content'     ,type : 'string'},
		    {name : 'version'     ,type : 'string'},
		    {name : 'status'     ,type : 'int'}
	    ]
	});
	
	scriptServiceReleaseStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 15,
		model : 'scriptServiceReleaseModel',
		proxy : {
			type : 'ajax',
			url : 'scriptService/queryServiceForMySelf.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	scriptServiceReleaseStore.on('beforeload', function (store, options) {
		    var new_params = {  
		    		bussId:bussCb.getValue(),
		    		bussTypeId:bussTypeCb.getValue(),
		    		scriptName:scName.getValue(),
		    		serviceName:sName.getValue(),
		    		scriptType:scriptTypeParam.getValue(),
		    		scriptStatus:scriptStatusCb.getValue(),
		    		onlyScript: 1
		    };
		    
		    Ext.apply(scriptServiceReleaseStore.proxy.extraParams, new_params);
	    });
	
	var scriptServiceReleaseColumns = [{
			text : '序号',
			xtype : 'rownumberer',
			width : 70
		}, 
		{
		    text : '服务主键',
		    dataIndex : 'iid',
		    width : 40,
		    hidden : true
		}, 
		{
			text : '服务名称',
		    dataIndex : 'serviceName',
		    width : 200,flex:1
		},
		{
			text : '脚本名称',
			dataIndex : 'scriptName',
			width : 260,flex:1
		}, 
		{
		    text : '一级分类',
		    dataIndex : 'buss',
		    width : 200,flex:1
		}, 
		{
			text : '二级分类',
			dataIndex : 'bussType',
			width : 250,flex:1
		},
		{
		    text : '脚本类型',
		    dataIndex : 'scriptType',
		    width : 80,flex:1,
		    renderer:function(value,p,record,rowIndex){
		    	var isflow = record.get('isflow');
		    	var backValue = "";
				if (value == "sh") {
					backValue = "shell";
				} else if (value == "perl") {
					backValue = "perl";
				} else if (value == "py") {
					backValue = "python";
				} else if (value == "bat") {
					backValue = "bat";
				} else if (value == "sql") {
					backValue = "sql";
				}
				if (isflow == '1') {
					backValue = "组合";
				}
				return backValue;
		    }
		}, 
		{
			text : '适用平台',
			dataIndex : 'platForm',
			width : 150
		}, 
		{
			text : '脚本状态',
			dataIndex : 'status',
			width : 100,
			renderer:function(value,p,record,rowIndex){
		    	if(value==-1) {
		    		return '<font color="#F01024">草稿</font>';
		    	} else if (value==1) {
		    		return '<font color="#0CBF47">已上线</font>';
		    	} else if (value==2) {
		    		return '<font color="#FFA602">审核中</font>';
		    	} else if (value==3) {
		    		return '<font color="#13B1F5">已共享</font>';
		    	} else if (value==9) {
		    		return '<font color="">已共享未发布</font>';
		    	} else {
		    		return '<font color="#CCCCCC">未知</font>';
		    	}
		    }
		},
		{
			text : '共享状态',
			dataIndex : 'isshare',
			width : 100,
			renderer:function(value,p,record,rowIndex){
			if(value==0) {
		    		return '<font color="">未共享</font>';
		    } else if (value==1) {
		    		return '<font color="#0CBF47">已共享</font>';
		    } else {
		    		return '<font color="#CCCCCC">未知</font>';
		    }
			}
		},
		{ 
			text: '操作',  
			dataIndex: 'stepOperation',
			align:'left',
			width:300,
			renderer:function(value,p,record,rowIndex){
				var iid =  record.get('iid'); // 其实是requestID
				var isflow = record.get('isflow');
				var serviceName = record.get('serviceName');
				var scriptType = record.get('scriptType');
				var bussId = record.get('bussId');
				var bussTypeId  =record.get('bussTypeId');
				var version = record.get('version');
				var status = record.get('status');
				var hasVersion = 0;
				if(version) {
					hasVersion = 1; // 最新版本有 版本号
				}
				var editFuncName = "editScript";
				var testFuncName = "testScriptNew";
				var viewVersionFuncName = "viewVersion";
				
				if(isflow=='1') {
					editFuncName = "editScriptFlow";
					testFuncName = "testScriptFlow";
					viewVersionFuncName = "viewVersionForFlow";
				}
				
				
				/*'<span class="switch_span">'+
   			   	'<a href="javascript:void(0)" onclick="publishScript('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+','+hasVersion+','+record.get('status')+')">'+
   			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"></img>&nbsp;发布'+
   			   	'</a>'+
   			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'+*/
        		return '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+editFuncName+'('+iid+','+status+',\''+serviceName+'\','+bussId+','+bussTypeId+','+hasVersion+')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_edit"></img>&nbsp;编辑'+
	       			   	'</a>'+
	       			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'+
	       			   
	       			 '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+testFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+',\''+scriptType+'\')">'+
	       			 '<img src="images/monitor_bg.png" align="absmiddle" class="script_test"/>'+'测试'+
	       			   	'</a>'+
	       			   '</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;'+
	       			   
		       		   '<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="'+viewVersionFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+',\''+scriptType+'\')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="monitor_version"></img>&nbsp;查看版本'+
	       			   	'</a>'+
	       			   '</span>' +'&nbsp;&nbsp;&nbsp;&nbsp;'+
	       			   
	       			   (isflow=='1'?'<span class="switch_span">'+
		       			   	'<a href="javascript:void(0)" onclick="'+testFuncName+'('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+')">'+
	       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_test"></img>&nbsp;测试'+
	       			   	'</a>'+
	       			   '</span>':(sendSwitch?'<span class="switch_span">'+
			       			   	'<a href="javascript:void(0)" onclick="sendScript('+iid+',\''+serviceName+'\','+bussId+','+bussTypeId+')">'+
		       			   		'<img src="images/monitor_bg.png" align="absmiddle" class="script_test"></img>&nbsp;下发'+
		       			   	'</a>'+
	       			   '</span>':''));
        		;
			}
		}
	];
	// 分页工具
//	var pageBar = Ext.create('Ext.PagingToolbar', {
//    	store: scriptServiceReleaseStore, 
//        dock: 'bottom',
//        displayInfo: true,
//        afterPageText:' 页 共 {0} 页',
//        beforePageText:'第 ',
//        firstText:'第一页 ',
//        prevText:'前一页',
//        nextText:'下一页',
//        lastText:'最后一页',
//        refreshText:'刷新',
//        displayMsg:'第{0}条 到 {1} 条数据  共找到{2}条记录',
//        emptyMsg:'找不到任何记录'
//	});
	  var pageBar = Ext.create('Ext.PagingToolbar', {
		  	store: scriptServiceReleaseStore,
		    dock: 'bottom',
		    displayInfo: true,
		    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			border:false,
		    emptyMsg:'找不到任何记录'
		  });
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 1
	});
	var selModel=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
	var scriptServiceReleaseGrid = Ext.create('Ext.grid.Panel', {
		region: 'center',
		autoScroll: true,
	    store : scriptServiceReleaseStore,
	    selModel : selModel,
	    plugins: [ cellEditing ],
	    border:true,
	    bbar : pageBar,
	    padding : panel_margin,
	    columnLines : true,
	    columns : scriptServiceReleaseColumns
	});
	 var mainPanel = Ext.create('Ext.panel.Panel',{
		 renderTo : "scriptService_grid_area",
	        width : contentPanel.getWidth(),
		    height :contentPanel.getHeight(),
		    border:true,
		    bodyPadding : grid_margin,
	        layout: 'border',
	        bodyCls:'service_platform_bodybg',
	        items : [search_form,scriptServiceReleaseGrid]
	});
	 
	 /** 窗口尺寸调节* */
		contentPanel.on ('resize', function (){
			mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
//			scriptServiceReleaseGrid.setHeight (contentPanel.getHeight ()-55);
		});
		
//	scriptServiceReleaseGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
//		scriptServiceReleaseGrid.down('#delete').setDisabled(selections.length === 0);
//	});

//	function setMessage(msg){
//		Ext.Msg.alert('提示', msg);
//	}
  
	/* 解决IE下trim问题 */
	String.prototype.trim=function(){
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		Ext.destroy(mainPanel);
		if(Ext.isIE){
			CollectGarbage(); 
		}
	});
	function shareServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt !=1){
			Ext.MessageBox.alert("提示", "请选择要共享的脚本！每次只能选择一条记录！");
			return ;
		}
		var ss = scriptServiceReleaseGrid.getSelectionModel().getSelection()[0];
		if(ss.data.status!=1) {
			Ext.MessageBox.alert("提示", "只有已上线的服务可以共享！");
			return;
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认共享", "是否共享选中的服务", function(id){if(id=='yes') release(4);});
	}
	
	
	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	/*auditorStore.on('beforeload', function(store, options) {
		var new_params =
		{
				bussTypeIds : bussTypeIds
		};
		Ext.apply(auditorStore.proxy.extraParams, new_params);
	});
	auditorStore.on('load', function(  store,records, successful, eOpts ) {
	 var countNum=0;
		for( var i = 0; i < auditorStore.getCount(); i++)
		{
			if(auditorComBox.getValue()==auditorStore.getAt(i).data.loginName)
				{
				countNum++;
				}
		}
		
		if(countNum==0)
			{
			auditorComBox.setValue('');
			}
	});*/

	auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    editable: false,
	    fieldLabel: "审核人",
	    labelWidth: 60,
//	    padding: 5,
	    store: auditorStore_sm,
	    queryMode: 'local',
//	    width: 200,
	    columnWidth:.98,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    valueField: 'loginName'//,
	    //value: auditor
	  });
	
	planTime_sm = Ext.create('Go.form.field.DateTime',{
	    fieldLabel:'计划时间',
	    format:'Y-m-d H:i:s',
	    labelWidth : 60,
	    hidden:true,
//	    width:200,
	    columnWidth:.98,
	    margin : '10 0 0 0'
	  });
	
	pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '详细说明',
        emptyText: '',
        labelWidth: 60,
        margin : '10 0 0 0',
         maxLength: 255, 
        height: 80,
        columnWidth:.98,
        autoScroll: true
    });
	
	var levelStore_sm = Ext.create('Ext.data.Store', {
	    fields: ['iid', 'scriptLevel'],
	    data : [
//	        {"iid":"1", "scriptLevel":"高风险脚本"},
//	        {"iid":"2", "scriptLevel":"中风险脚本"},
//	        {"iid":"3", "scriptLevel":"低风险脚本"}
	        {"iid":"1", "scriptLevel":"DBA授权"},
	        {"iid":"2", "scriptLevel":"项目经理授权"},
	        {"iid":"3", "scriptLevel":"普通用户"}
	    ]
	});
	
	scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 60,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '授权模式',
        margin : '10 0 0 0',
        displayField: 'scriptLevel',
        valueField: 'iid',
        editable: false,
        hidden: true,
        emptyText: '--请选择授权模式--',
        store: levelStore_sm
    });
	
	auditing_form_sm = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	bodyCls : 'x-docked-noborder-top',
    	buttonAlign : 'center',
    	border : false,
	    items: [{
//	    	layout:'form',
	    	anchor:'98%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,		    	
	    		items:[planTime_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[scriptLevelCb_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[pubDesc_sm]
	    	}]
	    }]
	});
	
	function saveServiceRelease() {
		/*var seledCnt = selModel.getCount();
		if(seledCnt < 1){
			Ext.MessageBox.alert("提示", "请选择要发布的服务！");
			return ;
		}
		
		var flowIdList = Ext.getCmp('releaseGrid').getSelectionModel().getSelection();
		for ( var i = 0, len = flowIdList.length; i < len; i++) {
			bussTypeIds.push(flowIdList[i].data.bussTypeId);
		}
		bussTypeIds = bussTypeIds.uniquelize();
		
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认发布", "是否发布选中的服务", function(id){
			if(id=='yes') {
				if (!win) {
		            win = Ext.create('widget.window', {
		                title: '确认审核信息',
		                closable: true,
		                closeAction: 'hide',
		                width: 600,
		                minWidth: 350,
		                height: 350,
		                layout: {
		                    type: 'border',
		                    padding: 5
		                },
		                items: [auditing_form],
		                buttons: [{ 
				  			xtype: "button",
				  			cls:'Common_Btn',
				  			text: "确定", 
				  			handler: function () { 
				  				flowIdList = Ext.getCmp('releaseGrid').getSelectionModel().getSelection();
				  				var sIds = new Array();
				  				for ( var i = 0, len = flowIdList.length; i < len; i++) {
				  					sIds.push(flowIdList[i].data.iid);
				  				}
				  				Ext.Ajax.request({
				  				    url : 'scriptService/sendAudit.do',
				  				    method : 'POST',
				  				    params : {
				  				    	sIds : sIds,
				  				    	auditor: auditorComBox.getValue(),
				  				  	  flag:0 //0-来着个人脚本库
				  				    },
				  				    success: function(response, opts) {
				  				        var success = Ext.decode(response.responseText).success;
				  				        //var message = Ext.decode(response.responseText).message;
				  				      win.close();
				  				      Ext.MessageBox.alert("提示", "请求已经发送到审核人");
				  				    },
				  				    failure: function(result, request) {
				  				    	secureFilterRs(result,"操作失败！");
				  				    	win.close();
				  				    }
				  			    });
				  				
					        }
				  		}, { 
				  			xtype: "button", 
				  			cls:'Gray_button',
				  			text: "取消", 
				  			handler: function () {
				  				this.up("window").close();
				  			}
				  		}]
		            });
		            
		        }
				win.show();
				auditorStore.load();
			}
		});*/
	}
	
	function deleteServiceRelease() {
		var seledCnt = selModel.getCount();
		if(seledCnt < 1){
			Ext.MessageBox.alert("提示", "请选择要删除的脚本！");
			return ;
		}
		var ss = selModel.getSelection();
		for ( var i = 0, len = ss.length; i < len; i++) {
			if(ss[i].data.status!=-1) {
				Ext.MessageBox.alert("提示", "只能删除状态为'草稿'的脚本！");
				return ;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认删除", "是否删除选中的脚本", function(id){if(id=='yes') release(1);});
	}
	function release(optionState){
		var url = 'scriptService/serviceRelease.do';
		var message="脚本发布成功";
		var errorMessage="脚本发布失败";
		if(optionState==1){
			message="脚本删除成功";
			errorMessage="脚本删除失败";
			url = "scriptService/deleteScriptForTest.do";
	    } else if(optionState==4){
			message="脚本共享成功";
			errorMessage="脚本共享失败";
		}
		
		var jsonData = getSelectedJsonData();
		if(jsonData=="[]"){
			Ext.MessageBox.alert("提示", signMessage);
			return ;
		}	
	    Ext.Ajax.request({
		    url : url,
		    method : 'POST',
		    params : {
		  	  jsonData : jsonData,
		  	  optionState:optionState
		    },
		    success: function(response, opts) {
		    	if(optionState==1) {
		    		var message1 = Ext.decode(response.responseText).message;
		    		Ext.MessageBox.show({
		                title : "提示",
		                msg : message1,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		    	} else {
		    		var success = Ext.decode(response.responseText).success;
			        //var message = Ext.decode(response.responseText).message;
			        if (success) {
			            Ext.MessageBox.show({
			                title : "提示",
			                msg : message,
			                buttonText: {
			                    yes: '确定'
			                },
			                buttons: Ext.Msg.YES
			              });
			          } else {
			            Ext.MessageBox.show({
			              title : "提示",
			              msg : errorMessage,
			              buttonText: {
			                  yes: '确定'
			              },
			              buttons: Ext.Msg.YES
			            });
			          }
		    	}
		    	scriptServiceReleaseStore.reload();
		        
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
	}
	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedJsonData(){
		var flowIdList = scriptServiceReleaseGrid.getSelectionModel().getSelection();
		if (flowIdList.length < 1) {
			return;
		}
		var jsonData = "[";
		for ( var i = 0, len = flowIdList.length; i < len; i++) {
			if (i == 0)
			{
				jsonData = jsonData + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			} else {
				jsonData = jsonData + "," + '{"iid":"'+ parsIIDJson('iid' ,flowIdList[i].data) + '"}';
			}
		}
		jsonData = jsonData + "]";
		return jsonData ;
	}
	function clearQueryWhere(){
		bussCb.setValue('');
		bussTypeCb.setValue('');
		scName.setValue('');
		sName.setValue('');
		scriptTypeParam.setValue('');
		scriptStatusCb.setValue('');
	}
	//从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key ,jsonObj){
		 var eValue=eval('jsonObj.'+key);  
		 return jsonObj[''+key+''];
	}
	
	function publishScript(iid,a,b,c,hasVersion, status){
		
		if(hasVersion==1) {
			Ext.Msg.alert('提示', "该服务已经发布过！");
			return;
		}
		
		if (status==2) { // 处于审核中
			Ext.Msg.alert('提示', "该服务正处于审核中！");
			return;
		}
		
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认发布", "是否确认发布该服务", function(id){
			if(id=='yes') {
				if (!publishAuditingSMWin) {
					publishAuditingSMWin = Ext.create('widget.window', {
		                title: '确认审核信息',
		                closable: true,
		                closeAction: 'hide',
		                modal: true,
		                width: 600,
		                minWidth: 350,
		                height: 350,
		                layout: {
		                    type: 'border',
		                    padding: 5
		                },
		                items: [auditing_form_sm],
		                buttons: [{ 
				  			xtype: "button",
//				  			cls:'Common_Btn',
				  			text: "确定", 
				  			handler: function () { 
				  				var planTime = planTime_sm.getRawValue();
				  				var scriptLevel = scriptLevelCb_sm.getValue();
				  				scriptLevel = 100;
				  				var publishDesc = pubDesc_sm.getValue();
				  				var auditor = auditorComBox_sm.getValue();
//				  				if(!planTime) {
//				  					Ext.Msg.alert('提示', "没有填写计划时间！");
//				  					return;
//				  				}
				  				
				  				if(!scriptLevel) {
				  					Ext.Msg.alert('提示', "没有选择风险级别！");
				  					return;
				  				}
				  				if(!publishDesc) {
				  					Ext.Msg.alert('提示', "没有填写详细说明！");
				  					return;
				  				}
				  				if(publishDesc.length > 255) {
				  					Ext.Msg.alert('提示', "详细说明内容长度超过255个字符！");
				  					return;
				  				}
				  				if(!auditor) {
				  					Ext.Msg.alert('提示', "没有选择审核人！");
				  					return;
				  				}
				  				
					    		var ss = selModel.getSelection();
				  				
				  				var sIds = new Array();
				  				sIds.push(ss[0].data.iid);
				  				Ext.Ajax.request({
				  				    url : 'scriptPublishAuditing.do',
				  				    method : 'POST',
				  				    params : {
				  				    	sIds : sIds,
				  				    	planTime: planTime,
				  				    	scriptLevel: scriptLevel,
				  				    	publishDesc: publishDesc,
				  				    	auditor: auditor,
				  				  	  flag:0 //0-来着个人脚本库
				  				    },
				  				    success: function(response, opts) {
				  				        var success = Ext.decode(response.responseText).success;
				  				        var message = Ext.decode(response.responseText).message;
				  				        if(!success) {
				  				        	Ext.MessageBox.alert("提示", message);
				  				        } else {
				  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
				  				        }
				  				      scriptServiceReleaseStore.load();
				  				      publishAuditingSMWin.close();
				  				      
				  				    },
				  				    failure: function(result, request) {
				  				    	secureFilterRs(result,"操作失败！");
				  				    	publishAuditingSMWin.close();
				  				    }
				  			    });
				  				
					        }
				  		}, { 
				  			xtype: "button", 
//				  			cls:'Gray_button',
				  			text: "取消", 
				  			handler: function () {
				  				this.up("window").close();
				  			}
				  		}]
		            });
		            
		        }
				publishAuditingSMWin.show();
				auditorStore_sm.load();
				planTime_sm.setValue('');
				scriptLevelCb_sm.setValue('');
				pubDesc_sm.setValue('');
				auditorComBox_sm.setValue('');
				
			}
		});
		
	}
	   function uploadExcel(){
	    	var uploadWindows;
	    	var uploadForm
	        uploadForm = Ext.create('Ext.form.FormPanel',{
	        	border : false,
	        	items : [{
	            	xtype: 'filefield',
	    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
	    			fieldLabel: '选择文件',
	    			labelWidth: 80,
	    			anchor: '90%',
//	    			labelAlign: 'right',
	    			margin: '10 10 0 40',
	    			buttonText: '浏览'
	            }],
	            buttonAlign : 'center',
	            buttons :[{
	            	text : '确定',
	            	handler :upExeclData
	            },{
	            	text : '取消',
	            	handler : function(){
	            		uploadWindows.close();
	            	}
	            }]
	        });
	        uploadWindows = Ext.create('Ext.window.Window', {
	    		title : '脚本导入',
	    		layout : 'fit',
	    		height : 140,
	    		width : 600,
	    		modal : true,
//	    		autoScroll : true,
	    		items : [ uploadForm ],
	    		listeners : {
	    			close : function(g, opt) {
	    				uploadForm.destroy();
	    			}
	    		}
	    	});
	        uploadWindows.show();
	        function upExeclData(){
	        	var form = uploadForm.getForm();
	    		var hdupfile=form.findField("fileName").getValue();
	    		if(hdupfile==''){
	    			Ext.Msg.alert('提示',"请选择文件...");
	    			return ;
	    		}
	    		uploadTemplate(form);
	        }
	        function uploadTemplate(form) {
	      	   if (form.isValid()) {
	             form.submit({
	               url: 'uploaScripts.do',
	                 success: function(form, action) {
	                 var sumsg = Ext.decode(action.response.responseText).message;
	                    Ext.Msg.alert('提示',sumsg);
	            		uploadWindows.close();
	            		pageBar.moveFirst();
	            		/*scriptServiceReleaseStore.reload();*/
	                    return;
	                 },
	                 failure: function(form, action) {
	                     var msg = Ext.decode(action.response.responseText).message;
	                     var mess = Ext.create('Ext.window.MessageBox', {
	                     minHeight : 110,
	                     minWidth : 500,
	                     resizable : false
	                   });
	                     Ext.Msg.alert('提示',msg);
	                   return;
	                 }
	             });
	      	   }
	      	 }
	    }	
	
	
});


//编辑按钮执行一个.do跳转到编辑页面
function editScript(iid,status,a,b,c,hasVersion){
//	alert(iid);
	 if (status == 2 ) { // 已经不是草稿状态，处于审核中或者已经上线
		Ext.Msg.alert('提示', "该脚本正在审核中，不能编辑！");
		return;
	}else{
		destroyRubbish(); //销毁本页垃圾
		contentPanel.getLoader().load({
			url: 'forwardEditScript.do?serviceId='+iid + '&hasVersion='+hasVersion,
			params: filter,
			scripts: true
		});
	}
}
function editScriptFlow(iid,status,serviceName,bussId,bussTypeId){
//	alert(iid);
	if (status == 2 ) { // 已经不是草稿状态，处于审核中或者已经上线
		Ext.Msg.alert('提示', "该脚本正在审核中，不能编辑！");
		return;
	}else{
		destroyRubbish(); //销毁本页垃圾
		var params = filter;
		params['iid'] = iid;
		params['serviceName'] = serviceName;
		params['actionType'] = 'edit';
		params['bussId'] = bussId;
		params['bussTypeId'] = bussTypeId;
		params['flag'] = 0;
		contentPanel.getLoader().load({url: 'flowCustomizedInitScriptService.do',
			params: params,
			scripts: true});
	}
}
//
function testScript(iid){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: 'scriptExecStart.do?serviceId='+iid+'&flag=0'+'&url=forwardScriptServiceRelease.do',scripts: true});
}

function testScriptNew(iid,serviceName,bussId,bussTypeId,scriptType){
	var chosedAgentWin;
	var chosedAgentIds = new Array();
	var upldWin;
	var upLoadformPane = '';
	var cpdsMap = {};//<cpid,dsid>
    var selCpId = -1;
	Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
			id : '-1',
			name : '未分组',
			description : ''
		}); 
		resourceGroupStore.insert(0,ins_rec);
	});
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    emptyText : '--请选择资源组--',
			    labelAlign : 'right',
			    labelWidth : 70,
			    width : '25.5%',
	            columnWidth:1,
			    multiSelect: true,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
		    	listeners: {
	    	      change: function( comb, newValue, oldValue, eOpts ) {
	    	    	  pageBar.moveFirst();
	    	      }
		    	}
	});
	
	var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '25.5%',
		labelAlign : 'right'
	});
	
	Ext.define('appNameModel', {
    	extend: 'Ext.data.Model',
    	fields : [ {
    		name : 'appName',
    		type : 'string'
    	}]
    });
	
	var app_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'appNameModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentAppNameList.do?envType=0',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var app_name = Ext.create('Ext.form.ComboBox', {
		name : 'appname',
	    fieldLabel: "应用名称",
	    emptyText : '--请选择应用名称--',
	    store: app_name_store,
	    queryMode: 'local',
	    width: "24.5%",
	    displayField: 'appName',
	    valueField: 'appName',
	    labelWidth : 70,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
	
	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'Agent IP',
		displayField : 'agentip',
		emptyText : '--请输入Agent IP--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25.7%'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '主机名称',
		displayField : 'hostname',
		emptyText : '--请输入主机名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '24.6%'
	});
	
	Ext.define('sysNameModel', {
    	extend: 'Ext.data.Model',
    	fields : [ {
    		name : 'sysName',
    		type : 'string'
    	}]
    });
	
	var sys_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'sysNameModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentSysNameList.do?envType=0',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var sys_name = Ext.create('Ext.form.ComboBox', {
		name : 'sysname',
	    fieldLabel: "系统名称",
	    emptyText : '--请选择系统名称--',
	    store: sys_name_store,
	    queryMode: 'local',
	    width: "25%",
	    displayField: 'sysName',
	    valueField: 'sysName',
	    labelWidth : 70,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
	
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '系统类型',
		displayField : 'ostype',
		emptyText : '--请输入系统类型--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25.5%'
	});
	
	var search_ip_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [ sys_name, app_name, host_name, os_type
			]
		},
		{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [ agent_ip, resourceGroupObj, agentStatusCb,
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function(){
						pageBar.moveFirst();
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function(){
						agent_ip.setValue('');
				    	app_name.setValue('');
						sys_name.setValue('');
						host_name.setValue('');
						os_type.setValue('');
				    	resourceGroupObj.setValue('');
				    	agentStatusCb.setValue('');
					}
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '导入',
					handler : importExcel
				}
			]
		}]
	});
    
    function checkFile(fileName){
	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
	    if(!file_reg.test(fileName)){  
	    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
	    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
	        return false;
	    }
	    return true;
	}
    
    function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:85,
	        bodyCls : 'x-docked-noborder-top',
		    frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 80,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: [
					{
						id:'upldBtnIdAudi',
						text: '导入Agent文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
			    			if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}

							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'importAgentForStart.do',
									params:{
										envType:0
				                	},
								    success: function(form, action) {
								       var msg = Ext.decode(action.response.responseText).message;
								       
								    	   var status = Ext.decode(action.response.responseText).status;
								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
								    	   
								    	   if(status==1) {
								    		   if(matchAgentIds && matchAgentIds.length>0) {
								    			   Ext.MessageBox.buttonText.yes = "确定"; 
								    				Ext.MessageBox.buttonText.no = "取消"; 
								    			   Ext.Msg.confirm("请确认", msg, function(id){
										  				 if(id=='yes'){
									  						Ext.Msg.alert('提示', "导入成功！");
									  						agent_ip.setValue('');
													    	app_name.setValue('');
															sys_name.setValue('');
															host_name.setValue('');
															os_type.setValue('');
													    	resourceGroupObj.setValue('');
													    	agentStatusCb.setValue('');
													    	chosedAgentIds = matchAgentIds;
												    	   pageBar.moveFirst();
										  				 }
										    		   });
								    		   } else {
								    			   Ext.Msg.alert('提示-没有匹配项', msg);
								    		   }
								    		   
								    	   } else {
								    		   Ext.Msg.alert('提示', "导入成功！");
									    	   agent_ip.setValue('');
										    	app_name.setValue('');
												sys_name.setValue('');
												host_name.setValue('');
												os_type.setValue('');
										    	resourceGroupObj.setValue('');
										    	agentStatusCb.setValue('');
										    	chosedAgentIds = msg;
									    	   pageBar.moveFirst();
								    	   }
								    	   
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}, {
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '设备信息批量导入',
		    width: 400,
		    height: 120,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}

	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'appName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
    
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 30,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	
	var agent_store_chosed = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 30,
		model: 'agentModel',
		proxy: {
            type: 'ajax',
            url: 'getAgentChosedList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
	});
    
    var agent_columns = [
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: '系统名称',  dataIndex: 'sysName',flex:1},
                        { text: '应用名称',  dataIndex: 'appName',flex:1},
                        { text: '主机名称',  dataIndex: 'hostName',flex:1},
                        { text: 'IP',  dataIndex: 'agentIp',width:150},
                        { text: '端口号',  dataIndex: 'agentPort',width:100},
                        { text: '系统类型',  dataIndex: 'osType',width:140},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden: true,
                        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }
                        },
		                { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ];
    
    agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp : Ext.util.Format.trim(agent_ip.getValue()),
	    	appName : app_name.getValue()==null?'':Ext.util.Format.trim(app_name.getValue()+""),
			sysName : sys_name.getValue()==null?'':Ext.util.Format.trim(sys_name.getValue()+""),
			hostName : Ext.util.Format.trim(host_name.getValue()),
			osType : Ext.util.Format.trim(os_type.getValue()),
	    	rgIds:resourceGroupObj.getValue(),
	    	agentState: agentStatusCb.getValue(),
	    	flag: 0
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    agent_store_chosed.on('beforeload', function (store, options) {
    	var new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds)
    	};
    	
    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    agent_store_chosed.on('load', function(store, options) {
    	agent_grid_chosed.getSelectionModel().select(0);  
    });
    
    agent_store.on('load', function (store, options) {
    	var records=[];//存放选中记录
	  for(var i=0;i<agent_store.getCount();i++){
	      var record = agent_store.getAt(i);
	      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
    	    {   
	    	  
	    	  if((+chosedAgentIds[ii])==record.data.iid)
	    		  {
	    		  records.push(record);
	    		  }
    	    }   
	  }
	  agent_grid.getSelectionModel().select(records, false, true);//选中记录
    });
    Ext.define('dbModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',        type: 'string'},
            {name: 'driverClass',type: 'string'},
            {name: 'dbUrl',      type: 'string'},
            {name: 'dbUser',     type: 'string'},
            {name: 'dbType',     type: 'string'}
        ]
    });
   
    var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
                            { text: '主键',  dataIndex: 'iid',hidden:true},
                            { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }},
                            { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }},
                            { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
                            { text: 'DB类型',  dataIndex: 'dbType',width:110}];
       
    var pageBar = Ext.create('Ext.PagingToolbar', {
		store : agent_store,
		dock : 'bottom',
		displayInfo : true
	});
    
    var agent_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
	    store:agent_store,
	    border:false,
	    columnLines : true,
	    columns:agent_columns,
	    bbar : pageBar,
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    listeners: {
	        select: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.push(record.get('iid'));
            	}
            },
	        deselect: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
            		chosedAgentIds.remove(record.get('iid'));
            	}
            }
	    }
	});
    
    var pageBarForAgentChosedGrid = Ext.create('Ext.PagingToolbar', {
    	store : agent_store_chosed,
    	dock : 'bottom',
    	displayInfo : true
    });
    var dbinfo_store = Ext.create('Ext.data.Store', {
		autoLoad: false,
		pageSize: 50,
		model: 'dbModel',
		proxy: {
			type: 'ajax',
			url: 'getDbSqlDriverInfo.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			select:function(selModel, record, index, eOpts) {
				 dbinfo_store.load({
			            params: {
			            	agentId: record.get("iid"),
			                agentIp: record.get("agentIp"),
			                agentPort: record.get("agentPort")
			            }
			        });
				 //当前选中cpid
				 selCpId = record.get("iid");
			 },
			 deselect:function(selModel, record, index, eOpts) {
				 dbinfo_store.removeAll();
				 var cpid = record.get("iid");
				 cpdsMap[cpid] = -1;//清空
				 selCpId = -1;
			 }
		}
	});
    var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
    	title: '已选服务器',
    	region : 'west',
    	store:agent_store_chosed,
    	border:true,
    	width:'100%',
    	columnLines : true,
//    	height: 450,
    	emptyText: '没有选择服务器',
    	columns:agent_columns,
    	selModel:selModelForagent_grid_chosed,
    	bbar : pageBarForAgentChosedGrid,
    	dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [{
				xtype : 'button',
				cls :'Common_Btn',
				text : '删除',
				handler : function() {
					var records = agent_grid_chosed.getSelectionModel().getSelection();
					if(records.length>0) {
	  					for(var i = 0, len = records.length; i < len; i++){
	  						chosedAgentIds.remove(records[i].get('iid'));
	  					}
	  					pageBarForAgentChosedGrid.moveFirst();
	  					pageBar.moveFirst();
	  				} else {
	  					Ext.Msg.alert('提示', "请选择服务器！");
                        return;
	  				}
				}
			},
			{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加服务器',
				handler : function(){
					if(!chosedAgentWin) {
						chosedAgentWin = Ext.create('Ext.window.Window', {
					  		title : '增加服务器',
					  		autoScroll : true,
					  		modal : true,
					  		resizable : false,
					  		closeAction : 'hide',
					  		layout: 'border',
					  		width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
					  		items:[search_ip_form, agent_grid],
					  		dockedItems: [{
					            xtype: 'toolbar',
					            dock:'bottom',
					            layout: {pack: 'center'},
						        items: [{ 
						  			xtype: "button",
						  			text: "确定", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				agent_store_chosed.load();
						  				this.up("window").close();
						  			}
						  		 },{ 
						  			xtype: "button",
						  			text: "关闭", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				this.up("window").close();
						  			}
						  		 }]
					  		}]
					  	});
					}
					chosedAgentWin.show();
					agent_store.load();
				}
			} ]
		}]
    });
    // 定义复选框
	var selModelForDbsource = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		mode : "SINGLE",
		listeners : {
			select:function(selModel2, record, index, eOpts) {
				var dsid = record.get("iid");
				if(selCpId != -1)
				{
					cpdsMap[selCpId] = dsid;//绑定
				}
			},
			deselect:function(selModel2, record, index, eOpts) {
				cpdsMap[selCpId] = -1;//清空
			}
		}
	});
    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
	    store:dbinfo_store,
	    width:'70%',
	    region : 'center',
	    border:true,
	    columnLines : true,
	    columns:dbsource_columns,
	    selModel:selModelForDbsource
	});
    dbinfo_store.on('load', function(store, options) {
    	db_soucre_grid.getSelectionModel().select(0);  
    });
    
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 30,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: iid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    var paramColumns = [
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 60,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '参数值',
        dataIndex: 'paramDefaultValue',
        width: 70,
        editor: {
            allowBlank: true
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    if(scriptType=='sql'){
		agent_grid_chosed.setWidth((contentPanel.getWidth()-250)/2);
		db_soucre_grid.setWidth((contentPanel.getWidth()-250)/2);
		db_soucre_grid.show();
		}else{
			agent_grid_chosed.setWidth((contentPanel.getWidth()-250));
			db_soucre_grid.hide();
		}
    var centerPanel = Ext.create('Ext.panel.Panel', {
    	region : 'center',
    	border: false,
        layout : 'border',
        items: [agent_grid_chosed,db_soucre_grid]
    });
    
    var paramGrid = Ext.create('Ext.grid.Panel', {
    	region : 'north',
    	title: "脚本参数",
        store: paramStore,
        plugins: [cellEditing],
        border: false,
        columnLines: true,
//        collapsible : true,
//        collapsed: true,
        height:150,
        columns: paramColumns
    });
    
    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
    	region : 'center',
    	border: true,
        layout : 'border',
        height:contentPanel.getHeight()-140,
        items: [centerPanel, paramGrid]
    });
    function setMessage(msg){
		Ext.Msg.alert('提示', msg);
	}
    var testChooseAgentWin = Ext.create('Ext.window.Window', {
	  		title : '选择测试服务器',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		layout: 'border',
	  		closeAction : 'destroy',
	  		width : contentPanel.getWidth()-250,
	  		height : contentPanel.getHeight(),
	  		items:[chooseAgentPanel],
	  		dockedItems: [{
	            xtype: 'toolbar',
	            dock:'bottom',
	            layout: {pack: 'center'},
	            items: [{ 
	  			xtype: "button",
	  			text: "确定", 
	  			cls:'Common_Btn',
	  			margin:'6',
	  			handler: function () {
	  				var me = this;
	  				var agents = new Array();
	  				if(chosedAgentIds.length<=0) {
	  					 setMessage('请选择服务器！');
                        return;
	  				}
	  				var isOk = false;
	  				var agentStateMsg = "";
	  				
	  				// 检查agent状态
	  				Ext.Ajax.request({
		      			url : 'checkAgentState.do',
		      			method : 'POST',
		      			async: false,
		      			params : {
		      				agentIds: chosedAgentIds
		      			},
		      			success : function(response, request) {
		      				isOk = Ext.decode(response.responseText).isOk;
		      				agentStateMsg = Ext.decode(response.responseText).agentStateMsg;
		      			},
		      			failure : function(result, request) {
		      				agentStateMsg = "检查Agent状态出错！";
		      			}
		      		});
	  				
	  				function realTest() {
	  					if(scriptType=='sql'){
	  						 var records = agent_grid_chosed.getSelectionModel().getSelection();
	  		  				if(records.length>0) {
	  		  					for(var i = 0, len = records.length; i < len; i++){
	  		  						var cpidTmp = records[i].get('iid');
	  		  						var dsidTmp = cpdsMap[cpidTmp];
	  		  						if(null==dsidTmp||undefined==dsidTmp||""==dsidTmp||-1==dsidTmp)
	  		  						{
	  		  							var agentIp = records[i].get('agentIp');
	  		  				            var agentPort = records[i].get('agentPort');
	  		  							var dsErrMsg = "服务器【"+agentIp+":"+agentPort+"】没有选择数据源！";
	  		  							Ext.Msg.alert('提示', dsErrMsg);
	  		  	                        return;
	  		  						}
	  		  						var tmpRec = {
	  		  							iid:cpidTmp,
	  		  						    dsid:dsidTmp
	  		  						};
//	  	  							tmpRec.cpid=cpidTmp;
//	  	  							tmpRec.dsid=dsidTmp;
	  		  						agents.push(tmpRec);
	  		  					}
	  		  				}
	  					}else{
	  					$.each(chosedAgentIds, function(i,v){
	  							var a = {
	  									iid: v
	  							};
	  							agents.push(a);
		  				});
	  					}
		  			
		  				paramStore.sort('paramOrder', 'ASC');
	                	var m = paramStore.getRange(0, paramStore.getCount()-1);
		  		        var jsonDataPara = "[";
		  		        for (var i = 0, len = m.length; i < len; i++) {
		  		            var ss = Ext.JSON.encode(m[i].data);
		  		            if (i == 0) jsonDataPara = jsonDataPara + ss;
		  		            else jsonDataPara = jsonDataPara + "," + ss;
		  		        }
		  		       jsonDataPara = jsonDataPara + "]";
	                	var aaaa = new Array();
	                    for (var i = 0, len = m.length; i < len; i++) {
	                        var n = 0;
	                        var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
	                        var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
	                        var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
	                        if ("" == paramType) {
	                            setMessage('参数类型不能为空！');
	                            return;
	                        }
	                        if (fucCheckLength(paramDesc) > 250) {
	                            setMessage('参数描述不能超过250字符！');
	                            return;
	                        }

	                        if (paramType == 'int') {
	                            if (!checkIsInteger(paramDefaultValue)) {
	                                setMessage('参数类型为int，但参数默认值不是int类型！');
	                                return;
	                            }
	                        }
	                        if (paramType == 'float') {
	                            if (!checkIsDouble(paramDefaultValue)) {
	                                setMessage('参数类型为float，但参数默认值不是float类型！');
	                                return;
	                            }
	                        }
	                        aaaa.push(paramDefaultValue);
	                    }

		  				
	                    var scriptPara = aaaa.join("@@script@@service@@");
	                    var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
		  				var dsid=0;
//		  				if(dsrecords.length>0){
//		  					dsid =  Ext.JSON.encode(dsrecords[0].data.dsId);
//		  				}
		  				Ext.Ajax.request({
			      			url : 'execScriptServiceForSync.do',
			      			method : 'POST',
			      			params : {
			      				serviceId: iid,
			      				execUser: "",
			      				scriptPara: scriptPara,
			      				jsonData:JSON.stringify(agents),
			      				jsonDataPara: jsonDataPara,
			      				dbsourceid:dsid,
			      				ifrom :0,
			      				flag: 0
			      			},
			      			success : function(response, request) {
	  		                	me.up("window").close();
	    		  				Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
			      			},
			      			failure : function(result, request) {
			      				Ext.Msg.alert('提示', '执行失败！');
			      			}
			      		});
	  				}
	  				
	  				if(isOk) {
	  					realTest();
	  				} else {
	  					Ext.Msg.confirm("请确认", agentStateMsg+"<br>选择的代理状态为异常，是否仍然进行测试？", function(id){
  			    			if(id=='yes') {
  			    				realTest();
  			    			}
  			    		});
	  				}
		        }
	  		}, { 
	  			xtype: "button", 
	  			text: "取消", 
	  			cls:'Common_Btn',
	  			handler: function () {
	  				this.up("window").close();
	  			}
	  		}]
	  	  }]
	  	}).show();
}
function sendScript(iid,serviceName,bussId,bussTypeId){
	Ext.define('resourceGroupModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		}, {
			name : 'description',
			type : 'string'
		}]
	});
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'resourceGroupModel',
		proxy: {
			type: 'ajax',
			url: 'getResGroupForScriptService.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'totalCount'
			}
		}
	});
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
			id : '-1',
			name : '未分组',
			description : ''
		}); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox', {
		fieldLabel : '资源组',
		labelAlign : 'right',
		labelWidth : 70,
		margin: '10 0 10 0',
		width:'25.5%',
		multiSelect: true,
		store : resourceGroupStore,
		displayField : 'name',
		valueField : 'id',
		triggerAction : 'all',
		editable : false,
		mode : 'local',
		listeners: {
			change: function( comb, newValue, oldValue, eOpts ) {
				/*chosedResGroups_forest = new Array();
			    	    	  for(var i=0;i<newValue.length;i++) {
			    	    		  chosedResGroups_forest.push(newValue[i]);
			    	    	  }*/
				agent_store.load();
			}
		}
			});
	var search_form = Ext.create('Ext.form.Panel', {
		layout : 'anchor',
		buttonAlign : 'center',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [resourceGroupObj,{
				fieldLabel: 'IP',
				labelAlign : 'right',
				labelWidth : 70,
				name: 'agentIp',
				width:'25%',
				xtype: 'textfield'
			},{
				fieldLabel: 'Agent描述',
				labelAlign : 'right',
				labelWidth : 70,
				name: 'agentDesc',
				width:'25%',
				xtype: 'textfield'
			},{
				xtype: 'button',
				cls:'Common_Btn',
				margin: '0 5 0 0',
				text : '查询',
				handler : function() {
					pageBar1.moveFirst();
				}
			},{
				xtype: 'button',
				cls:'Common_Btn',
				margin: '0 5 0 0',
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			}]
		},
		{
			xtype : 'toolbar',
			dock : 'top',
			items : [{
				fieldLabel: '下发路径',
				labelAlign : 'right',
				labelWidth : 70,
				margin: '10 0 10 0',
				name: 'sendPath',
				width:'75%',
				xtype: 'textfield'
			}]
		}]
	});
	
	Ext.define('agentModel', {
		extend: 'Ext.data.Model',
		idProperty: 'iid',
		fields: [
			{name: 'iid',     type: 'string'},
			{name: 'agentIp',     type: 'string'},
			{name: 'agentPort',     type: 'string'},
			{name: 'agentDesc',     type: 'string'},
			{name: 'agentDesc',     type: 'string'},
			{name: 'agentState',     type: 'int'}
			]
	});
	
	var agent_store = Ext.create('Ext.data.Store', {
		autoLoad: false,
		pageSize: 50,
		model: 'agentModel',
		proxy: {
			type: 'ajax',
			url: 'getAllAgentList.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});
	
	var agent_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
		{ text: '主键',  dataIndex: 'iid',hidden:true},
		{ text: 'IP',  dataIndex: 'agentIp',width:120},
		{ text: '端口号',  dataIndex: 'agentPort',width:100},
		{ text: '描述',  dataIndex: 'agentDesc',flex:1},
		{ text: '状态',  dataIndex: 'agentState',width:130,renderer:function(value,p,record){
			var backValue = "";
			if(value==0){
				backValue = "Agent正常";
			}else if(value==1){
				backValue = "Agent异常";
			}
			return backValue;
		}}
		];
	
	agent_store.on('beforeload', function (store, options) {
		var new_params = {  
				agentIp:search_form.getForm().findField("agentIp").getValue(),
				agentDesc:search_form.getForm().findField("agentDesc").getValue(),
				rgIds:resourceGroupObj.getValue(),
				flag: -1
		};
		
		Ext.apply(agent_store.proxy.extraParams, new_params);
	});
	var pageBar1 = Ext.create('Ext.PagingToolbar', {
		store: agent_store,
		dock: 'bottom',
		displayInfo: true
	});
	var agent_grid = Ext.create('Ext.grid.Panel', {
		height: contentPanel.getHeight()-255,
		store:agent_store,
		border:true,
		columnLines : true,
		columns:agent_columns,
		selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
		bbar: pageBar1
	});
	function clearQueryWhere(){
		search_form.getForm().findField("agentIp").setValue('');
		search_form.getForm().findField("agentDesc").setValue('');
	}
	
	var testChooseAgentWin = Ext.create('Ext.window.Window', {
		title : '选择服务器',
		autoScroll : true,
		modal : true,
		resizable : false,
		closeAction : 'destroy',
		width : contentPanel.getWidth()-250,
		height : contentPanel.getHeight()-60,
		items:[search_form,agent_grid],
		buttons: [{ 
			xtype: "button",
			text: "确定", 
			handler: function () {
				var me = this;
				var records = agent_grid.getSelectionModel().getSelection();
				if(records.length<1) {
					Ext.Msg.alert('提示', '请选择测试服务器！');
					return;
				}
				var agentIds = new Array();
				$.each(records, function(i,v) {
					agentIds.push(v.data.iid);
				});
				
				var sendPath = Ext.util.Format.trim(search_form.getForm().findField("sendPath").getValue());
				
				if (!Ext.isEmpty(sendPath)) {
//					Ext.Msg.alert('提示', '请填写下发路径！');
//					return;
					if(sendPath.indexOf("\\")>-1) {
	                	Ext.Msg.alert('提示', '下发路径的分隔符请使用 "/"！');
	                    return;
	                }
					
					var lastStr = sendPath.charAt(sendPath.length-1);
					if(lastStr!='/') {
						Ext.Msg.alert('提示', '下发路径请以 "/"结尾！');
	                    return;
					}
				}
				
				Ext.Ajax.request({
					url : 'sendScriptToAgents.do',
					method : 'POST',
					params : {
						serviceId: iid,
						agentIds:agentIds,
						sendPath: sendPath
					},
					success : function(response, request) {
						var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						
						me.up("window").close();
						Ext.Msg.alert('提示', message);
					},
					failure : function(result, request) {
						Ext.Msg.alert('提示', '下发失败！');
					}
				});
			}
		}, { 
			xtype: "button", 
			text: "取消", 
			handler: function () {
				this.up("window").close();
			}
		}]
	}).show();
//	resourceGroupObj.setValue('');
//	search_form.getForm().findField("agentIp").setValue('');
//	search_form.getForm().findField("agentDesc").setValue('');
	agent_store.load();
	
}
function testScriptFlow(iid,serviceName,bussId,bussTypeId){
	destroyRubbish(); //销毁本页垃圾
	var params = filter;
	params['iid'] = iid;
	params['serviceName'] = serviceName;
	params['actionType'] = 'exec';
	params['bussId'] = bussId;
	params['bussTypeId'] = bussTypeId;
	params['flag'] = 0;
	contentPanel.getLoader().load({url: 'flowCustomizedInitScriptService.do',
		params: params,
		scripts: true});
}

function viewVersion(iid,serviceName,bussId,bussTypeId, scriptType){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: 'scriptViewVersion.do?serviceId='+iid +'&flag=0',
		params: filter,
		scripts: true});
}

function viewVersionForFlow(iid,serviceName,bussId,bussTypeId){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: 'scriptViewVersionForFlow.do?serviceId='+iid +'&flag=0',
		params: filter,
		scripts: true
	});
}

function putToolBox(iid,serviceName,bussId,bussTypeId){
	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
		if (btn == 'yes') {
			Ext.Ajax.request({
				url : 'putScriptToToolBox.do',
				method : 'POST',
				params : {
					iid:iid, 
					table:'IEAI_SCRIPT_TEST'
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', message);
					} else {
						Ext.Msg.alert('提示', message);
					}
					scriptServiceReleaseStore.reload();
				},
				failure : function(result, request) {
					secureFilterRs(result,"操作失败！");
				}
			});
		}
	});
}
function topit(iid,serviceName,bussId,bussTypeId){
	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
		if (btn == 'yes') {
			Ext.Ajax.request({
				url : 'scriptTopIt.do',
				method : 'POST',
				params : {
					iid:iid, 
					table:'IEAI_SCRIPT_TEST'
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.Msg.alert('提示', message);
					} else {
						Ext.Msg.alert('提示', message);
					}
					scriptServiceReleaseStore.reload();
				},
				failure : function(result, request) {
					secureFilterRs(result,"操作失败！");
				}
			});
		}
	});
}