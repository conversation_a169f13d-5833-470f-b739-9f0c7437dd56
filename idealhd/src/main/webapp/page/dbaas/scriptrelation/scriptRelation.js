var showScriptsforRelWin;
var scriptRel_bindScriptColumnsWin;
var scriptRel_scriptRelStore;
var scriptRel_treePanel;
var scriptRel_params_store
Ext.onReady (function ()
{
    var choice=0;
    var selectIid=0;
    var selectIid1=0;
    var selServiceId;
    var selParentServiceId;
	// 清理主面板的各种监听时间
	destroyRubbish ();
	/** *********************Model********************* */
	 Ext.define('scriptRelData', {
	        extend: 'Ext.data.Model',
	        fields: [
	            {name: 'id',     type: 'int'},
	            {name: 'name',     type: 'string'},
	            {name: 'parentId',     type: 'string'},
	            {name: 'scriptName',    type: 'string'},
	            {name: 'serviceId',    type: 'string'},
	            {name: 'parentServiceId',   type: 'string'},
	            {name : 'rootId',type : 'string'},
	            {name : 'dbtype',type : 'string'}
	        ]
	     });
	 Ext.define('paramsData', {
	    extend: 'Ext.data.Model',
	    fields: [
	        {name: 'iid',     type: 'string'},
	        {name: 'scriptId', type: 'string'},
	        {name: 'paramType', type: 'string'},
	        {name: 'paramDesc',     type: 'string'},
	        {name: 'paramOrder',   type: 'string'},
	        {name: 'relServiceId',   type: 'string'},
	        {name: 'relColumn',   type: 'string'},
	        {name: 'relServiceName',   type: 'string'},
	        {name: 'relColumnId',   type: 'string'}
	    ]
    });     
	/** *********************Store********************* */
	 scriptRel_scriptRelStore = Ext.create ('Ext.data.TreeStore',
	{
	    model : 'scriptRelData',	    
	    proxy :
	    {
	        type : 'ajax',
	        async:false,
	        url : 'findScriptRelTreeList.do'
	    },
	    root : {
            expanded : true,
            leaf : false,
            nodeType: 'async'
        },
        autoLoad:true,
        autoSync : true
	});
	 scriptRel_scriptRelStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			iid:selectIid,
			name : nameForQuery.getValue ().trim ()
		};
		Ext.apply (scriptRel_scriptRelStore.proxy.extraParams, new_params);
	});
	
	 scriptRel_scriptRelStore.on('load',function(store,node,records,successful,eOpts){}); 
	/** *********************组件********************* */
	/** 查询按钮* */
	var queryButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '查询',
	    handler : queryWhere
	});

	var addButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '增加',
	    handler:function(){
	    	saveScriptRel(0)//参数为0时表示添加
        },
	 //   handler : onAddListener
	});
	var editorButtonForBSM = Ext.create ("Ext.Button",
	    {
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '编辑',
	    handler:function(){
	    	saveScriptRel(1)//参数为0时表示添加
        },
	   // handler : onEditorListener
	    });
	/** 删除按钮* */
	var deleteButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '删除',
	    disabled : true,
	    handler : onDeleteListener
	});

    var nameForQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入脚本名--',
		labelWidth : 50,
		width : '25%',
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					var name = field.getValue() == null ? "" : field.getValue();
					scriptRel_scriptRelStore.load( {
						params : {
							name : name.trim()
						}
					});
				}
			}
		}
	});
	
	var Form1 = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items : [ '->', addButtonForBSM,editorButtonForBSM,deleteButtonForBSM]
		} ]
	});
	
	
    scriptRel_params_store = Ext.create('Ext.data.Store', {
        autoLoad : false,
        autoDestroy : true,
        pageSize: 100,
        model: 'paramsData',
        proxy: {
            type: 'ajax',
            url: 'getparamsByRelationId.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	    
    scriptRel_params_store.on('beforeload', function (store, options) {
        var new_params = {  
                serviceId : selServiceId,
                relationId:selectIid1
        };
     Ext.apply(scriptRel_params_store.proxy.extraParams, new_params);
    });
	
    var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: scriptRel_params_store,
    	dock: 'bottom',
    	baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
    	displayInfo: true,
    	border : false
    });
	
	/** 列表Columns* */
	var gridColumns = [
	       	        {
	       	            text : 'ID',
	       	            dataIndex : 'iid',
	       	            flex : 1,
	       	            hidden : true
	       	        },
	       	        {
	       	        	xtype: 'treecolumn', 
                        text : '脚本名称',
//                        sortable : true,
                        dataIndex : 'scriptName',
                        flex : 1,
                        renderer : function(value, metadata) {
	       					metadata.tdAttr = 'data-qtip="' + value + '"';
	       					return value;
	       				}
                    },
	       	        {
	       	            text : 'parentId',
	       	            dataIndex : 'parentId',
	       	            flex : 1,
	       	            hidden : true
	       	        },
	       	        {
	       	            text : 'serviceId',
	       	            dataIndex : 'serviceId',
	       	            flex : 1,
	       	            hidden : true
	       	        }
	       	];
		
    var params_columns = [
                        {
                            text: '主键',
                            dataIndex: 'iid',
                            width: 40,
                            hidden: true
                        },{
                            text: '顺序',
                            dataIndex: 'paramOrder',
                            width: 55
                        },
                        {
                			xtype : 'gridcolumn',
                			dataIndex : 'paramType',
                			width : 100,
                			text : '参数类型'
                    	}, {            				
            				xtype : 'gridcolumn',
            		        text: '参数描述',
            		        dataIndex: 'paramDesc',
            		        minWidth: 100,
            		        flex: 1,
            		    },
                    	{
            				xtype : 'gridcolumn',
            				dataIndex : 'relServiceName',
            				minWidth: 100,
            				flex: 1,
            				text : '脚本'
            				
            			},
                	    {
            				xtype : 'gridcolumn',
            				dataIndex : 'relColumn',
            				minWidth: 100,
            				text : '所选列',
            				flex: 1,             				
            			},
            		    {
    	       	            text : '操作',
    	       	            sortable : true,
//    	       	            dataIndex : 'iServiceId',
    	       	            width: 80,
    	       	            renderer:function(value, p, record) {   	       	            	
    	       	            	if(value!=0){
    	       	            		return "<a href=\"#\" valign=\"middle\" onclick=\"bindScriptColumn('"
    		       	         		+selServiceId+"','"+record.get("paramType")+"',"+selectIid1+",'"+selParentServiceId+"',"+record.get("paramOrder")+","+record.get("relColumnId")+");\"><span class='abc'>绑定关系列</span></a>";
    	       	            	}
    	       	         	},
    	       	        }];

	var Form2 = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items : [ '->']
		} ]
	});
	/** *********************Panel********************* */
    var params_grid = Ext.create('Ext.grid.Panel', {
        selModel:Ext.create('Ext.selection.CheckboxModel',{checkOnly: true}),
        store:scriptRel_params_store,
        padding : panel_margin,
        border:true,
        bbar: pageBar,  
        columnLines : true,
        columns:params_columns,
        region : 'center',
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
    });
	var eastPanel = Ext.create('Ext.panel.Panel',{ 
//	    title : '参数配置',
	    region: 'east',
        border: false,
        width: '60%',
        height: contentPanel.getHeight(),
        layout: 'border',
	    items : [Form2,params_grid]
	});
	
	scriptRel_treePanel = Ext.create ('Ext.tree.Panel',
	{
	    store : scriptRel_scriptRelStore,
	    region : 'center',
	    padding : panel_margin,
	    selModel : Ext.create('Ext.selection.CheckboxModel', {mode : "SIMPLE"}),
        useArrows: true,  
        rootVisible: false,  
		columns : gridColumns,
		cls:'customize_panel_back',
        columnLines : true,
        border : true,
	    listeners: {
	    	select( t, record, index, eOpts ){	
	    		selServiceId=record.data.serviceId;
	    		selectIid1=record.data.id;		
	    		selParentServiceId=record.data.parentServiceId;
	    		scriptRel_params_store.load({params:{serviceId:selServiceId,relationId:selectIid1}});
	    	}
	    	,	    
			cellclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
				selectIid=record.data.id		    	 
		    }
	    }

	});
	/** 判断删除按钮是否可用* */
	scriptRel_treePanel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		deleteButtonForBSM.setDisabled (selections.length === 0);
	});
	var centerPanel = Ext.create('Ext.panel.Panel', {		 
//		title : '脚本关系',
    	region: 'center',
        border: false,
        width: '40%',
        height: contentPanel.getHeight(),
        layout: 'border',
        items: [Form1,scriptRel_treePanel]
    });
	/** 主Panel* */
	var mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "scriptRelation_area",
	    layout : 'border',
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight () - modelHeigth,
	    bodyPadding : grid_margin,
	    border : true,
	    bodyCls:'service_platform_bodybg',
	    defaults: {
            split: true
        },
        padding: 5,
	    items : [centerPanel,eastPanel]
	});
	
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		mainPanel.setWidth(contentPanel.getWidth ());
		mainPanel.setHeight (contentPanel.getHeight () - 35);
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
	    function(obj, options, eOpts) {
	        Ext.destroy(mainPanel);
	        if (Ext.isIE) {
	            CollectGarbage();
	        }
	});
	
	
	
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	

	function queryWhere ()
	{
		scriptRel_scriptRelStore.load();
	}

	function saveScriptRel (choice)
	{	
		var record = scriptRel_treePanel.getSelectionModel ().getSelection ();
		var iid=0;
		var serviceId;
		var parentId=0;	
		var parentServiceId;
		var rootId; 
		var dbtype;
		if (record.length==1){			
			iid=record[0].get ('id');
			serviceId=record[0].get ('serviceId');	
			parentId=record[0].get ('parentId');	
			parentServiceId=record[0].get ('parentServiceId');	
			rootId=record[0].get ('rootId');	
			dbtype=record[0].get ('dbtype');
			
		}
    	if(choice==0){
    		if(record.length>1){
    			Ext.Msg.alert("提示","请选择一条信息新增！");
    			return;
    		}else if (record.length==1){    			  
    			showScriptsforRel(0,iid,serviceId,rootId,dbtype);
    		}else if (record.length==0){
    			showScriptsforRel(0,0,'',0,dbtype);
    		}
    	}else if(choice==1){    		
        	if(record.length==0){
        		Ext.Msg.alert('提示', '请选择一条要编辑的记录！');
        		return;
        	}else if(record.length>1){
        		Ext.Msg.alert('提示', '只能选择一条要编辑的记录！');
        		return;
        	} 
        	showScriptsforRel(iid,parentId,serviceId,rootId,dbtype);      	
    	}			
	}


	function onDeleteListener (btn)
	{
		var record = scriptRel_treePanel.getSelectionModel().getSelection();		
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		var mess="删除该列同时会删除子数据，是否确认删除选中数据!";
		Ext.MessageBox.buttonText.yes = "确定"; 
		Ext.MessageBox.buttonText.no = "取消"; 
		Ext.Msg.confirm("确认删除", mess, function(id){if(id=='yes')deletefun();});		
	}	
	
});
function deletefun(){
	var record = scriptRel_treePanel.getSelectionModel().getSelection();	
	Ext.MessageBox.wait ("数据处理中...", "进度条");
	var ids = [];
	Ext.Array.each (record, function (recordObj)
	{
		var cpId = recordObj.get ('id');
		// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
		if (-1 != cpId)
		{
			ids.push (cpId);
		}
	});
	Ext.Ajax.request (
	{
		url : 'deleteScriptRelation.do',
	    timeout : 30000,
	    params :
	    {
	    	ids : ids.join (',')
	    },
	    method : 'POST',
	    success : function (response, opts)
	    {
		    var success = Ext.decode (response.responseText).success;					    
		    if (success)
		    {
		    	Ext.Array.each (ids, function (id)
				{
		    		if(scriptRel_scriptRelStore.getNodeById(id)){
		    			scriptRel_scriptRelStore.getNodeById(id).remove();
		    		}
				});						    	
		    }
		    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
	    },
	    failure : function (result, request)
	    {
		    /*Ext.MessageBox.hide ();
		    Ext.Msg.alert ('提示', '请求超时！');*/
	    	secureFilterRs(result,"请求返回失败！",request);
	    }
	});		
}


function showScriptsforRel(iid,parentId,serviceId,rootId,dbtype){
	if(iid!=0){
		Ext.Ajax.request({
		    url : 'getScriptRelColumnModelByRelId.do',
		    method : 'POST',
		    params : {
		    	relationId: iid
		    },
		    success: function(response, opts) {
		    	 var count = Ext.decode(response.responseText).count;
		    	 if(count>0){
		    		var mess="该脚本已经绑定关系列，确认修改，需重新绑定!";
	    			Ext.MessageBox.buttonText.yes = "确定"; 
	    			Ext.MessageBox.buttonText.no = "取消"; 
	    			Ext.Msg.confirm("确定", mess, function(id){
						if(id=='yes'){
							showScriptsWin(iid,parentId,serviceId,rootId,dbtype);
						}
	    			});
		    	 }else{
		    		 showScriptsWin(iid,parentId,serviceId,rootId,dbtype);
		    	 }
		    }
		})	
	}else{
		showScriptsWin(iid,parentId,serviceId,rootId,dbtype);
	}
	
}
function showScriptsWin(iid,parentId,serviceId,rootId,dbtype){
	showScriptsforRelWin=Ext.create ('Ext.window.Window',
			{
			    title : '脚本详情',
			    modal : true,
			    closeAction : 'destroy',
			    constrain : true,
			    autoScroll : true,
			    width : 715,
			    height :500,
			    minWidth : 700,
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
			    layout : 'fit',
			    loader :
			    {
			        url : 'showScriptsforRel.do',
			        params :
			        {
			        	iid : iid,
			        	parentId:parentId,
			        	serviceId:serviceId,
			        	rootId:rootId,
			        	dbtype:dbtype
			        },
			        autoLoad : true,
			        scripts : true
			    }
			});
			showScriptsforRelWin.show ();

}


function bindScriptColumn(serviceId,paramType,relationId,parentServiceId,order,relColumnId){
	scriptRel_bindScriptColumnsWin=Ext.create ('Ext.window.Window',
	{
	    title : '关系脚本列详情',
	    modal : true,
	    closeAction : 'destroy',
	    constrain : true,
	    autoScroll : true,
	    width : 715,
	    height :500,
	    minWidth : 700,
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    layout : 'fit',
	    loader :
	    {
	        url : 'bindScriptColumns.do',
	        params :
	        {
	        	relationId:relationId,
	        	serviceId : serviceId,
	        	paramType:paramType,
	        	parentServiceId:parentServiceId,
	        	order:order,
	        	relColumnId:relColumnId
	        },
	        autoLoad : true,
	        scripts : true
	    }
	});
	scriptRel_bindScriptColumnsWin.show ();
}


function addparams(){
	
}
function adduserInfo () {}

function saveUserInfo(){}

function aaa(){
	var grid_panel=Ext.getCmp('grid_panel');
	if(grid_panel){
		var store=grid_panel.getStore();
		store.load();
	}
}


function  getrootNodeiId(record){	
	var iparentid=0;
	var parentnodeId=record.parentNode.id;	
	if(parentnodeId!='reservePlanModel-root'){
		return getrootNodeiId(record.parentNode)
	}else{		
		iparentid=record.data.iId;
		return iparentid
	}
	
}

function  expandAllNodeByRootNode(node){	
	node.expand();
	var childNodes =node.childNodes;
	if(node.childNodes.length!=0){		
    	for(var i = 0; i<childNodes.length;i++){             		 
    		expandAllNodeByRootNode(childNodes[i])
    	}		
	}
}