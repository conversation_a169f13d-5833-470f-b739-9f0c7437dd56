var ServiceStartFormWindow;
Ext.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			Ext.tip.QuickTipManager.init();
			var mainPanel;
			var attachmentIds = [];
			var editPanel = Ext.create('Ext.panel.Panel',{
				region : 'center',
				minHeight : 150,
				border : true,
				collapsible : false,
				autoScroll : true,
				title : "编辑框",
				height : contentPanel.getHeight(),
				html : '<textarea id="startNoSql-code-edit-view" value style="height:100%;"></textarea>'
			});
			Ext.define('serviceStartModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'argsIid',
					type : 'long'
				}, {
					name : 'argsOrder',
					type : 'string'
				}, {
					name : 'argsType',
					type : 'string'
				}, {
					name : 'argsDefaultValue',
					type : 'string'
				}, {
					name : 'argsDesc',
					type : 'string'
				}, {
					name : 'serverIid',
					type : 'string'
				}, {
					name : 'IIP',
					type : 'string'
				}, {
					name : 'serviceSystem',
					type : 'string'
				}, {
					name : 'serviceDBType',
					type : 'string'
				}, {
					name : 'serviceDBVersion',
					type : 'string'
				}, {
					name : 'loginName',
					type : 'string'
				}, {
					name : 'fullName',
					type : 'string'
				}, {
					name : 'agentIp',
					type : 'string'
				}, {
					name : 'agentPort',
					type : 'string'
				}, {
					name : 'ostype',
					type : 'string'
				}, {
					name : 'insName',
					type : 'string'
				}, {
					name : 'insUserName',
					type : 'string'
				}, {
					name : 'paramRuleIn',
					type : 'string'
				}, {
					name : 'paramRuleOut',
					type : 'string'
				}, {
					name : 'paramRuleType',
					type : 'int'
				}, {
					name : 'paramRuleLen',
					type : 'int'
				}, {
					name : 'paramRuleDesc',
					type : 'string'
				}, {
					name : 'paramRuleOrder',
					type : 'int'
				} ]
			});
		    /** Agent 数据Model* */
		    Ext.define('AgentModel', {
		        extend: 'Ext.data.Model',
		        fields: [{
		            name: 'iid',
		            type: 'string'
		        },
		        {
		            name: 'iequipmentorvm',
		            type: 'string'
		        },
		        {
		            name: 'iagentname',
		            type: 'string'
		        },
		        {
		            name: 'iagentdesc',
		            type: 'string'
		        },
		        {
		            name: 'iagentip',
		            type: 'string'
		        },
		        {
		            name: 'iagentport',
		            type: 'string'
		        },
		        {
		            name: 'iagentclusterid',
		            type: 'string'
		        },
		        {
		            name: 'iagentclustername',
		            type: 'string'
		        },
		        {
		            name: 'iagentos',
		            type: 'string'
		        },
		        {
		            name: 'iagentcomputername',
		            type: 'string'
		        },
		        {
		            name: 'iagentstartuser',
		            type: 'string'
		        },
		        {
		            name: 'iagentstate',
		            type: 'string'
		        },
		        {
		            name: 'iagentversion',
		            type: 'string'
		        },
		        {
		            name: 'iagentactivitynum',
		            type: 'int'
		        },{
			      name : 'isource_path',
			      type : 'string'
			    },{
				  name : 'itarget_path',
				  type : 'string'
				}, {
			      name : 'ibackup_path',
			      type : 'string'
			    }, {
				    name : 'ioperation_user',
				    type : 'string'
				}, {
				    name : 'ioperation_password',
				    type : 'string'
				}, {
					name : 'ios_name',
					type : 'string'
				}, {
					name : 'udpateosname',
					type : 'string'
				}, {
					name : 'iconnect_type',
					type : 'string'
				}, {
					name : 'iconnect_port',
					type : 'string'
				}, {
			      name : 'itransmission_type',
			      type : 'string'
			    }, {
			      name : 'itransmission_ip',
			      type : 'string'
			    }, {
			      name : 'itransmission_prot',
			      type : 'string'
				}, {
			      name : 'itransmission_user',
			      type : 'string'
				}, {
			      name : 'itransmission_password',
			      type : 'string'
				}, {
				      name : 'ienvtype',
				      type : 'string'
				},{
					name : 'iagentCchange',
					type : 'string'
				},{
					name : 'ifsendMsg',
					type : 'int'
				},{
					name : 'iagentUpId',
					type : 'int'
				},{
					name : 'icustom_cmd',
					type : 'string'
				},{
					name : 'icustom_mess',
					type : 'string'
				},{
					name : 'icreateuser',
					type : 'string'
				},{
					name : 'icreatetime',
					type : 'string'
				}, {
				      name : 'issued',
				      type : 'string'
				},{
		            name : 'idcid',
		            type : 'string'
		      },
		      {
		          name: 'iagentwebport',
		          type: 'string'
		      }]
		    });

			var checkUserStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getExecAuditorList.do?scriptLevel=0&dbaasFlag=1',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			Ext.define('attachmentModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'int'
				}, {
					name : 'attachmentName',
					type : 'string'
				}, {
					name : 'attachmentSize',
					type : 'string'
				}, {
					name : 'attachmentUploadTime',
					type : 'string'
				} ]
			});

			var attachmentStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 10,
				model : 'attachmentModel',
				proxy : {
					type : 'ajax',
					url : 'getAllScriptAttachment.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			attachmentStore.on('beforeload', function(store, options) {
				var new_params = {
					scriptId : tmp_uuid,
					ids : attachmentIds
				};

				Ext.apply(attachmentStore.proxy.extraParams, new_params);
			});

			var attachmentColumns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '主键',
						dataIndex : 'iid',
						width : 40,
						hidden : true
					},
					{
						text : '附件名称',
						dataIndex : 'attachmentName',
						flex : 1,
						renderer : function(value, metaData, record, rowIdx,
								colIdx, store) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode(value) + '"';
							return value;
						}
					} /*
						 * , { text: '附件大小', dataIndex: 'attachmentSize', width:
						 * 200 }, { text: '上传时间', dataIndex:
						 * 'attachmentUploadTime', flex: 1 }
						 */];

			var attachmentGrid = Ext.create('Ext.grid.Panel', {
				title : "附件",
				width : '100%',
				region : 'south',
				height : '50%',
				store : attachmentStore,
				border : true,
				columnLines : true,
				columns : attachmentColumns
			});

			var checkUserComBox = Ext.create('Ext.form.ComboBox', {
				editable : false,
				fieldLabel : "审核人",
				labelWidth : 60,
				hidden : exam == 0 ? true : false,
				store : checkUserStore,
				queryMode : 'local',
				margin : '5 5 0 3',
				displayField : 'fullName',
				valueField : 'loginName'
			});
			if (exam == 1) {
				checkUserComBox.show();
			} else {
				checkUserComBox.hide();
			}								 
			var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
				name : 'pubdesc',
				fieldLabel : '详细说明',
				emptyText : '',
				labelWidth : 65,
				margin : '5 5 0 3',
				height : 100,
//						columnWidth : .98,
				autoScroll : true
			});
			 var serviceStartFormPanel = new Ext.form.FormPanel({
				frame : true,
				buttonAlign : 'center',
				layout : "column", // 从左往右的布局
				border : false,
				items : [
						{
							labelAlign : "left",
							fieldLabel : "任务名称",// 文本框标题
							margin : '5 5 0 3',
							labelWidth : 65,
							hidden:true,
							name : 'taskName',
							xtype : 'textfield'
						},
						{
							// columnWidth : 20,
							labelAlign : "left",
							fieldLabel : "服务名称",// 文本框标题
							margin : '5 5 0 3',
							xtype : 'textfield',
							labelWidth : 65,
							readOnly : true,// 不可编辑,只读
							value : startNoSql_serviceName
						},
						{
							labelAlign : "left",
							fieldLabel : "脚本类型",// 文本框标题
							labelWidth : 65,
							margin : '5 5 0 3',
							xtype : 'textfield',
							readOnly : true,// 不可编辑,只读
							value : startNoSql_scriptType
						},										
						checkUserComBox ,pubDesc_sm]
			})

			var leftPanel = Ext.create('Ext.panel.Panel', {
				width : 260,
				height :'30%',
				margin : '5 5 0 0',
				border : true,
				columnLines : true,
				region : 'west',
				items : [ serviceStartFormPanel ]
			});
			var argsStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getArgsByServiceStart.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			argsStore.on('beforeload', function(store, options) {
				var new_params = {
						scriptuuid : tmp_uuid
				};
				Ext.apply(argsStore.proxy.extraParams, new_params);
			});
			var serviceStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getServiceByServiceStart.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			serviceStore.on('beforeload', function(store, options) {
				var new_params = {
						idbType : dbType
//					createUserName : startForm_createusername
				};
				Ext.apply(serviceStore.proxy.extraParams, new_params);
			});
			var serviceAgentStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getAllAgentList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			serviceAgentStore.on('beforeload', function(store, options) {
				var new_params = {
					flag : 1
				};
				Ext.apply(serviceAgentStore.proxy.extraParams, new_params);
			});
			var argsColumns = [
					{
						text : '主键',
						dataIndex : 'argsIid',
						hidden : true
					},
					{
						text : '顺序',
						dataIndex : 'argsOrder',
						width : 120,
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					},
					{
						text : '默认值',
						dataIndex : 'argsDefaultValue',
						width : 120,
						editor : {
							allowBlank : false
						},
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					},
					{
						text : '类型',
						dataIndex : 'argsType',
						width : 120,
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					},
					{
						text : '描述',
						dataIndex : 'argsDesc',
						width : 120,
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					} ];
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});

			var selModelM = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			var argsGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				height : '50%',
				margin : '0 0 0 0',
				title : "参数",
				store : argsStore,
				plugins : [ cellEditing ],
				border : false,
				columnLines : true,
				columns : argsColumns
			});

			var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
				region : 'east',
				collapsible : false,
				border : false,
				width : '30%',
				minWidth : 120,
				minHeight : 140,
				layout : {
					type : 'border'
				},
				items : [ argsGrid, attachmentGrid ]
			});
			
			    
			var agentColumns = [{
		        text: '序号',
		        width: 65,
		        xtype: 'rownumberer'
		    },
		    {
		        text: 'IID',
		        dataIndex: 'iid',
		        width: 45,
		        hidden: true
		    },
		    {
		        text: '名称',
		        dataIndex: 'iagentname',
		        width: 150,
		        editor: {
		            xtype: 'textfield',
		            maxLength: 255
		        },
		        flex : 1
		    }, {
			        text: '地址',
			        dataIndex: 'iagentip',
			        width: 130,
			        editor: {
			            xtype: 'textfield',
			            maxLength: 15
			        },
			        flex : 1
		    },
		    {
		        text: '端口号',
		        dataIndex: 'iagentport',
		        width: 100,
		        editor: {
		            xtype: 'numberfield',
		            maxValue: 65535,
		            minValue: 0
		        },
		        flex : 1
		    },{
		        text: '操作系统',
		        dataIndex: 'iagentos',
		        width: 100,
		        flex : 1
		    }, {
		        text: '计算机名',
		        dataIndex: 'iagentcomputername',
		        width: 100,
		        flex : 1
		    }, {
		        text: '启动用户',
		        dataIndex: 'iagentstartuser',
		        width: 100
		    }, {
		        text: '版本',
		        dataIndex: 'iagentversion',
		        width: 70,
		        flex : 1
		    },  {
		        text: '状态',
		        dataIndex: 'iagentstate',
		        width: 80,
		        renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
		        	var cls = "";
		        	if(value=='0') {
		        		cls = "<span class='Green_color State_Color'>正常</span>";
		        	} else if(value=='1') {
		        		cls = "<span class='Red_color State_Color'>异常</span>";
		        	} else if(value=='2') {
		        		return '升级中';
		        	} else if(value=='3'){
		        		return '未知';
		        	} else if(value == '4') {
		        		if(agentPauseRecoverSwitch){
		        			return "<span class='Red_color State_Color'>暂停</span>";
		        		}else{
		        			return "<span class='Red_color State_Color'>异常</span>";
		        		}
		        	}else {
		        		return '新建';
		        	}
		        	return cls;
			        }
			 }]					
				 													
			var agentStore = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        autoDestroy: true,
		        remoteSort: true,
		        model: 'AgentModel',
		        pageSize: 100,
		        proxy: {
		            type: 'ajax',
		            url: 'getAgentMaintainList.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
		    
		    agentStore.on('beforeload', function(store, options) {
		        var new_params = {
		        };
		        Ext.apply(agentStore.proxy.extraParams, new_params);
		    });
			var serviceAgentGrid = Ext.create('Ext.grid.Panel', {
				region : 'south',
				height : '50%',
				margin : '0 0 0 0',
				width : contentPanel.getWidth(),
				title : "服务器",
				store : agentStore,
				border : false,
				columnLines : true,
				columns : agentColumns,
				selModel : selModelM
			});
			var rightPanel = Ext.create('Ext.panel.Panel',{
				region : 'center',
				padding : '5 0 0 0',
				layout : {
					type : 'border'
				},
				autoScroll : true,
				border : false,
				height : contentPanel.getHeight(),
				items : [ editPanel,serviceAgentGrid,paramsAndFuncDescPanel ],
				buttonAlign : 'center',
				buttons : ['->', {
					text : '发起',
					handler : function() {
						Ext.Msg.confirm("请确认","是否确认进行发起操作?",function(button, text) {
							if (button == "yes") {
								Ext.MessageBox.wait("任务发起中...","进度条");
								var taskNames = serviceStartFormPanel.getForm().findField('taskName').getValue();
								var serviceRecord= serviceAgentGrid.getSelectionModel().getSelection();			
								var publishDesc=serviceStartFormPanel.getForm().findField('pubdesc').getValue();
								var agents = new Array();
								Ext.Array.each(serviceRecord,function(r) {
									agents.push(r.get('iid')+ "_1");
								});
								if (agents.length == 0) {
									Ext.Msg.alert("提示","请选择服务器");
									return;
								}																					
								var checkUser = "";
								if (exam == 1&& ("" == checkUserComBox.getValue() || null == checkUserComBox.getValue())) {
									Ext.Msg.alert("提示","请选择审核人");
									return;
								} else {
									checkUser = checkUserComBox.getValue();;
								}
								var args = new Array();
								var record_args = argsStore.data;
								for (var i = 0; i < record_args.length; i++) {
									args.push(record_args.items[i].data);
								}

								var m = argsStore.getRange();
								var jsonData = "[";
								for (var i = 0, len = m.length; i < len; i++) {
									var n = 0;
									var paramType = m[i].get("argsType") ? m[i].get("argsType").trim(): '';
									var paramDefaultValue = m[i].get("argsDefaultValue") ? m[i].get("argsDefaultValue").trim(): '';
									if ((paramType == 'OUT-int'
											|| paramType == 'IN-int' || paramType == 'int')
											&& paramDefaultValue) {
										if (!checkIsInteger(paramDefaultValue)) {
											setMessage('参数类型为int，但参数默认值不是int类型！');
											return;
										}
									}
									if ((paramType == 'OUT-float'
											|| paramType == 'IN-float' || paramType == 'float')
											&& paramDefaultValue) {
										if (!checkIsDouble(paramDefaultValue)) {
											setMessage('参数类型为float，但参数默认值不是float类型！');
											return;
										}
									}
									var ss = Ext.JSON.encode(m[i].data);
									if (i == 0)
										jsonData = jsonData+ ss;
									else
										jsonData = jsonData+ ","+ ss;
								}
								jsonData = jsonData+ "]";
								var pubflag=0;
								if(isAutoSub==2){
									pubflag=1;
								}
								Ext.Ajax.request({
									//		url : 'serviceStart.do',
									url : 'serviceStartDbaasForRroject.do',
									method : 'POST',
									params : {
										serviceIid : tmp_serviceId,
										auditor : exam == 0 ? "": checkUser, // 执行人
										isDelay : false, // 是否定时延时
										agents : agents, // 服务器id
										iisexam : exam, // 是否需要审核
										args : args,
										argsData : jsonData,
										taskNames : taskNames,
										serviceType : serviceType,
										scriptType : startNoSql_scriptType,																				
										flag : pubflag,
										planTime:'',
										planTimeType : '立即执行',
										planTimeDD : '',
										planTimeHH : '',
										planTimeMM : '',
										publishDesc:publishDesc,
										sId:tmp_serviceId,

									},
									success : function(response,opts) {
										var success = Ext.decode(response.responseText).success;
										var message = Ext.decode(response.responseText).message;
										if (!success) {
											Ext.MessageBox.alert("提示", message);
										} else {
											if(exam == 0){
												Ext.MessageBox.alert("提示", "发起成功");
											}else{
												Ext.MessageBox.alert("提示", "请求已经发送到审核人");
											}			
											
										}																					
									},
									failure : function(result,request) {
										secureFilterRs(result,"操作失败！");

									}
								});
							}
						});
					}
				} ]
			});
			mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "startConfigForNoSql_area",
				layout : {
					type : 'border'
				},
				border : false,
				height : contentPanel.getHeight() - modelHeigth,
				items : [ leftPanel, rightPanel ]
			});

			var editor = CodeMirror.fromTextArea(document.getElementById('startNoSql-code-edit-view'), {
				mode : 'sql',
				lineNumbers : true,
				matchBrackets : true,
				readOnly : true
			});

			contentPanel.on('resize', function() {
				editor.getDoc().clearHistory();
				mainPanel.setHeight(contentPanel.getHeight());
				mainPanel.setWidth(contentPanel.getWidth());
				// editor.setSize(editPanel.getWidth(),
				// editPanel.getHeight());
			});

			var editorStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'editScriptModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryOneService.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			editorStore.on('beforeload', function(store, options) {
				var queryparams = {
						iid : tmp_serviceId
				};
				Ext.apply(editorStore.proxy.extraParams, queryparams);
			});
			editorStore.on('load', function(store, options, success) {
				var reader = store.getProxy().getReader();
				var scriptT = reader.jsonData.scriptType;
				if (scriptT == 'sh') {
					editor.setOption("mode", 'shell');
				} else if (scriptT == 'bat') {
					editor.setOption("mode", 'bat');
				} else if (scriptT == 'py') {
					editor.setOption("mode", 'python');
				} else if (scriptT == 'sql') {
					editor.setOption("mode", 'text/x-plsql');
				} else if (scriptT == 'perl') {
					editor.setOption("mode", 'text/x-perl');
				}
				editor.setOption('value', reader.jsonData.content);
			});
		});
