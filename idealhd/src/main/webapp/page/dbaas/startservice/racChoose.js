Ext.onReady(function() {
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  
  //alert(tmp_serviceId+" "+chooseResource_dbType);
  
  	var store = Ext.create('Ext.data.Store', {
	    fields:['value', 'display'],
	    data:{'items':[
	    	{ 'value': '0',  "display":"所有"  },
	        { 'value': '1',  "display":"是"  },
	        { 'value': '2',  "display":"否" }
	    ]},
	    proxy: {
	        type: 'memory',
	        reader: {
	            type: 'json',
	            root: 'items'
	        }
	    }
	});

	var selModel = Ext.create ('Ext.selection.CheckboxModel',
			{
				checkOnly : true,
				mode : 'SINGLE'
			});
	var grid = Ext.create('Ext.grid.Panel', {
	    store: store,
	    columns: [
	        { text: '值',  dataIndex: 'value', hidden: true },
	        { text: '是否RAC', dataIndex: 'display', flex: 1 }
	    ],
	    margins : 8,
		border:true,
	    split : true,
	    columnLines : true,
	    region: 'center',
	    selModel : selModel,
	    listeners:{
	    	afterrender : function(){
	    		grid.getSelectionModel().select(0,true);
	    		if(passRac==1){
		    		grid.getSelectionModel().select(1,true);
	    		} 
	    		if(passRac==2){
		    		grid.getSelectionModel().select(2,true);
	    		}
	    	}
	    }
	});



	var form = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : ['->', {
				xtype : 'button',
				text : '确定',
				cls : 'Common_Btn',
				handler : onSaveListener
			}]
		}]
	});
	var panel = Ext.create('Ext.panel.Panel',{
		renderTo : "rac_area",
    	layout : 'border',
		border:true,
		width : contentPanel.getWidth()*0.35,
		height :contentPanel.getHeight()*0.63-123,
		columnLines : true,
		items : [form,grid],

    });
	
	  /* 解决IE下trim问题 */
	  String.prototype.trim=function(){
	    return this.replace(/(^\s*)|(\s*$)/g, "");
	  };
	  
	  
	  function onSaveListener(){
		  var value=grid.getSelectionModel().getSelection()[0].data.value;
		  passRac=0;
		  if(value==1){
			  passRac=1;
		  }
		  if(value==2){
			  passRac=2;
		  }
		  
		  var m = chooseResoureForTypeStore.getRange() ;
		  chooseResoureForType_data =new Array();
		  var jsonDate = "[";
		  for (var i = 0,len = m.length; i < len; i++) {
			   var ss = Ext.JSON.encode(m[i].data);
			   chooseResoureForType_data.push(m[i].data);   
			   if (i == 0) jsonDate = jsonDate + ss;
			   else jsonDate = jsonDate + "," + ss;
		  }
		  jsonDate = jsonDate + "]";
		  chooseResoureForTypeData=jsonDate;
		    
		  chooseResource_dataSourceStore.reload();
	  }

});

