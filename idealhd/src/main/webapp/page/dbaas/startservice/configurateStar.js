var permissionPanel;
Ext.onReady(function() {
	
//////////////////////////资源配置start////////////////////
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'IDBUSER',
            type: 'string'
        },
        {
            name: 'IDBP<PERSON>',
            type: 'string'
        },
        {
            name: 'ICOPYNUM',
            type: 'string'
        },
        {
            name: 'IAPPLYUSER',
            type: 'string'
        },
        {
            name: 'ISID',
            type: 'string'
        },
        {
            name: 'ITYP<PERSON>',
            type: 'long'
        },
        {
            name: 'IENV',
            type: 'long'
        },
        {
            name: 'IDBID',
            type: 'string'
        },
        {
            name: 'IDBVERSION',
            type: 'string'
        },       
        {
            name: 'ISTATUS',
            type: 'string'
        },
        {
            name: 'IPOSITION',
            type: 'int'
        },
        {
        	name: 'IALTERLOGNAME',
        	type: 'string'
        },
        {
            name: 'ICPU',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY',
            type: 'string'
        }
        ,
        {
            name: 'IDISK',
            type: 'string'
        } ,
        {
            name: 'IMODEL',
            type: 'string'
        },{
            name: 'IBUSINESSTYPE',
            type: 'string'
        },{
            name: 'continueCount',
            type: 'long'
        },{
            name: 'cumulateCount',
            type: 'long'
        }
        ]
    });
    
    
    Ext.define('chooseModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'type',
            type: 'long'
        },
        {
            name: 'condition',
            type: 'string'
        }
        ]
    });
    		    
    chooseResource_dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 100,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            async : false,
            url: 'getResourceListByVrs.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total',
            }
        }
    });
// = Ext.decode(response.responseText).total;
    

    chooseResource_dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
        	serviceId:tmp_serviceId,
        	versionData : chooseResoureForVersionDateNameRet.join(','),
        	resourceGroupData:chooseResoureForResourceGroupDate.join(','),
        	systemTypeData:chooseResoureForSystemTypeNameDate.join(','),
        	jsonTypeData:chooseResoureForTypeData  ,
        	dbType:chooseResource_dbType,
        	passRac:passRac
        };

        Ext.apply(chooseResource_dataSourceStore.proxy.extraParams, new_params);
    });
    
    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        }        ]
    });
    var sysDataStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
    var dataTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getDatabaseType.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65, 
//        locked : true,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },{
        text: '<span style="color:#dddddd;">DBID</span>',
        dataIndex: 'IDBID', 
//        locked : true,        
        width: 100,
        hidden: true,
        renderer : function(value, metadata) {
        	metadata.css='x-grid-back-red';
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        width: 100,
//        locked : true,
        flex:1,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'sysName',
            valueField: 'sysName',
            store: sysDataStore
        }
        ),
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '服务器IP',
        dataIndex: 'IIP',
//        locked : true,
        width: 100,
        flex:1,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
//        locked : true,
        width: 100,
        flex:1,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: dataTypeModel
        }),renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库版本',
        dataIndex: 'IDBVERSION',
        width: 100,
        hidden: true,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '副本号',
        dataIndex: 'ICOPYNUM',
        width: 80,
        hidden: true,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库端口',
        dataIndex: 'IDBPORT',
        width: 100,
        flex:1,
        editor: {
            xtype: 'numberfield',
            maxValue: 65535,
            minValue: 1
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        width: 140,
        hidden:true,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据源用户',
        dataIndex: 'IDBUSER',
        width: 100,
        flex:1,
        hidden: true,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	header: '数据源密码',
        dataIndex: 'IDBPWD',
        width: 100,
        flex:1,
        hidden: true,
        editor: new Ext.form.TextField({ 						
			inputType:'password', //设置输入类型为password
			allowBlank: false,
			allowNegative: true
		 }),
		renderer:retNotView
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        width: 140,
        flex:1,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
  
    {
        header: '状态',
        dataIndex: 'ISTATUS',
        width: 80,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "有效",
                    value: "0"
                },
                {
            		name: "失效",
            		value: "1"
            	}]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('ISTATUS');
            if(IFLAG=='0'){
            	return "<span class='Complete_Green State_Color'>有效</span>";
            }else{
            	return "<span class='Abnormal_yellow State_Color'>失效</span>";
            }
        }
    },   
    {
        header: '<span style="color:#dddddd;">申请人</span>',
        dataIndex: 'IAPPLYUSER',
        width: 90,
        hidden: true,
        renderer : function(value, metadata) {
        	metadata.css='x-grid-back-red';
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: chooseResource_dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var ipField = Ext.create("Ext.form.field.Text", {
//        fieldLabel: 'IP',
        labelWidth: 25,
        labelAlign: 'left',
        emptyText: "--请输入IP--",
        name: 'dataBaseNameParam',
        width: '13%'
    });
    var sidField = Ext.create("Ext.form.field.Text", {
 //       fieldLabel: '服务名',
        labelWidth: 50,
        labelAlign: 'left',
        emptyText: "--请输入服务名--",
        name: 'dataBaseNameParam',
        width: '15%'
    });       
    
    var selModel_chooseResource=Ext.create('Ext.selection.CheckboxModel', {
		checkOnly: true
	});
    chooseResource_dataSourceGrid = Ext.create('Ext.grid.Panel', { 
    	title:'资源列表',
    	region: 'center',
    	selModel :selModel_chooseResource,
        store: chooseResource_dataSourceStore,
 //       selModel: selModel,
        padding : panel_margin,
		border: false,
		cls:'customize_panel_back',
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
    });

	 var typeStore = Ext.create('Ext.data.Store', {
			fields: ['id','name'],
			data : [
			        {"id":1,"name":"版本"},
			        {"id":2,"name":"资源组"},
			        {"id":3,"name":"系统分类"}]
		 });
	 if(dbType == 'ORACLE'){
		 typeStore = Ext.create('Ext.data.Store', {
				fields: ['id','name'],
				data : [
				        {"id":1,"name":"版本"},
				        {"id":2,"name":"资源组"},
				        {"id":3,"name":"系统分类"},
				        {"id":4,"name":"RAC"}]
			 }); 
	 }

	 var typeCombo = Ext.create('Ext.form.field.ComboBox', {
			margin : '5',
			store : typeStore,
			queryMode : 'local',
			width : 600,
			forceSelection : true, // 要求输入值必须在列表中存在
			typeAhead : true, // 允许自动选择
			displayField : 'name',
			valueField : 'id',
			editable : false,
			triggerAction : "all",
			listeners:{
				'select':function( combo, records, eOpts ){
					var m = chooseResoureForTypeStore.getRange() ;
					var data = chooseGrid.getView().getSelectionModel().getSelection();
				    for (var i = 0,len = m.length; i < len; i++) {
				        var ss = m[i].data.type;
				        if(records[0].data.id==ss && m[i].data!=data[0].data){
				        	 Ext.Msg.alert('提示', '该类型已经选择，请选择其他类型!');				        	 
				        	 chooseResoureForTypeStore.remove(data );
				             var p = {
				             		serviceId:tmp_serviceId,
				             		order: data[0].data.order,
				             		type: 0,
				             		condition: 'AND'
				             };
				        	 chooseResoureForTypeStore.insert(0, p);
				             chooseGrid.getView().refresh();
				             queryWhere(0);
			                 return;
				        }	    				        
				    }
				    queryWhere(records[0].data.id);
				}
			}
		});
	 
	 var conditionStore = Ext.create('Ext.data.Store', {
			fields: ['id','name'],
			data : [
			        {"id":"AND","name":"AND"},
			        {"id":"OR","name":"OR"}]
		 });

	 var conditionCombo = Ext.create('Ext.form.field.ComboBox', {
			margin : '5',
			store : conditionStore,
			queryMode : 'local',
			width : 600,
			forceSelection : true, // 要求输入值必须在列表中存在
			typeAhead : true, // 允许自动选择
			displayField : 'name',
			valueField : 'id',
			editable : false,
			triggerAction : "all"
		});
    var chooseColumns = [ 
	{
		text : '顺序',
		dataIndex : 'order',
		width : 55,
		editor : {
			allowBlank : false,
			xtype : 'numberfield',
			maxValue : 30,
			minValue : 1
		},
	}, {
		text : '类别',
		dataIndex : 'type',
		editor : typeCombo,
		align : 'left',
		flex:1,
		width : 100,
		renderer : function(value, metadata, record) {				
					var index = typeStore.find('id', value);
					if (index != -1) {
						return typeStore.getAt(index).data.name;
					} else {
						return '';
					}
					
				}
	},{
		text : '条件',
		dataIndex : 'condition',
		editor : conditionCombo,
		align : 'left',
		width : 100,
		renderer : function(value, metadata, record) {
					var index = conditionStore.find('id', value);
					if (index != -1) {
						return conditionStore.getAt(index).data.name;
					} else {
						return '';
					}
				}
	},{
		text : '操作',
		dataIndex : 'sets',
		align : 'left',
		width : 80,
		hidden:true,
		renderer : function(value, metadata, record) {
			var type =  record.get('type');			
			return ' <div> <a href="javascript:void(0)" onclick="queryWhereset('+type+')"> 属性选择 </a>&nbsp;&nbsp;</div>';		         		                		                                	                     		 
		}
	}
	];
            
	chooseResoureForTypeStore = new Ext.data.ArrayStore({
		 	autoLoad: true,
	        autoDestroy: true,
			fields: ['serviceId','order','type','condition'],
			data: []
		});	
	chooseResoureForTypeStore.load(function(records, operation, success) {
	    for (var i = 0,len = chooseResoureForType_data.length; i < len; i++) {
	    	chooseResoureForTypeStore.add(chooseResoureForType_data[i]);
	    }
	});
	
    var chooseGrid = Ext.create('Ext.grid.Panel', {
    	region: 'north',
    	height:'30%',
//    	title: '条件组合',	
        store: chooseResoureForTypeStore,
        plugins: [cellEditing],
        padding : panel_margin,
		border: true,
//		autoScroll: true,
//		cls:'customize_panel_back',
        columnLines: true,
        columns: chooseColumns,
	    listeners: {
//	    	select: function(t,record, index, eOpts) {
//	    		
//	    		var type =  record.get('type');			
//				return ' <div> <a href="javascript:void(0)" onclick="queryWhereset('+type+')"> 属性选择 </a>&nbsp;&nbsp;</div>';
//	    	    },
	    	itemdblclick : function(dbclickthis, record, item,index, e, eOpts) {
	    		var type=record.get("type");
	    		queryWhere(type);
	    		if(type!=''){
	    			queryWhere(type)
	    		}
	    	}
	        },
		tools : [ {
			type : 'plus',
			tooltip : '增加',
			handler : addOut
		}, {
			type : 'minus',
			tooltip : '删除',
			callback : function(panel, tool, event) {
                var data = chooseGrid.getView().getSelectionModel().getSelection();
                if (data.length == 0) {
                    Ext.Msg.alert('提示', '请先选择您要操作的行!');
                    return;
                } else {
                    Ext.Msg.confirm("请确认", "是否真的要删除？", function(button, text) {
                        if (button == "yes") {   
                        	chooseResoureForTypeStore.remove(data);  
                        	chooseResoureForType_data.remove(data[0].data);
                        	if(data[0].data.type==1){
                        		chooseResoureForVersionDate = [];//所选版本的id集合
                            	chooseResoureForVersionDateRet = [];//所选的id##isLeaf集合，给最后入库提供
                            	chooseResoureForVersionDateNameRet = [];//所选的id##Name##isLeaf集合，给确定之后，资源展示提供
                        	}
                        	if(data[0].data.type==2){
                        		chooseResoureForResourceGroupDate = [];//所选的资源组id集合
                        	}
                        	if(data[0].data.type==3){
                        		chooseResoureForSystemTypeDate = [];//所选的系统类型id集合
                            	chooseResoureForSystemTypeNameDate = [];//所选的系统类型name集合,给确定之后，资源展示提供
                        	}
                        	if(data[0].data.type==4){
                        		passRac = 0;
                        	}
                        	chooseGrid.getView().refresh();
                        	permissionPanel.setTitle("配置 ");
                        	permissionPanel.getLoader().load({
            					url: 'welcomeTochooseResoure.do',
            					params: {serviceId : tmp_serviceId},
            					scripts: true
            				});

                        }
                    });
			
                }
			}
		} ]
    });
    if(chooseResoureForTypeStore.getCount()==0){
    	addOut();
    }
	    
    permissionPanel = Ext.create('Ext.panel.Panel',{
    	region: 'center',  
		title: '配置',
		activeItem: 0,
		autoScroll: true,
        loader: {
            url: 'welcomeTochooseResoure.do',
            autoLoad: true,
            scripts : true
        }
 //       html: '<p style="margin-top:100px;font-size: 23px;text-align:center;">请选择资源配置</p>'
   });
	
    function retNotView(value){
    	var coun ="";
    	if (value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    
 
    
    
    function clearQueryWhere() {
    	ipField.setValue('');
    	sidField.setValue('');
    }
	var businnessTypeStore = Ext.create('Ext.data.JsonStore', {
		fields: ['INAME', 'INAME'],
		//autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getBusinessTypeCode.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    
	businnessTypeStore.on('beforeload', function(store, options) {
			var new_params = {
			};
			Ext.apply(businnessTypeStore.proxy.extraParams, new_params);
	});
    function addOut() {
        var store = chooseGrid.getStore();
        var ro = store.getCount();
        if(ro==4){
        	Ext.Msg.alert('提示',"最多填写三个条件！");
			return;
        }
        var p = {
        		serviceId:tmp_serviceId,
        		order: ro+1,
        		type: 0,
        		condition: 'AND'
        };
        store.insert(0, p);
        chooseGrid.getView().refresh();
    }  
	
	//////////////////////////资源配置end////////////////////
    
//////////////////////////脚本参数start////////////////////
    
    var paramTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "IN-string"
        },
        {
            "name": "IN-int"
        },
        {
            "name": "IN-float"
        }]
    });
    
    var paramTypeCombo = Ext.create('Ext.form.field.ComboBox', {
        margin: '5',
        store: paramTypeStore,
        queryMode: 'local',
        width: 600,
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'name',
        triggerAction: "all"
    });
    var paramColumns = [/*{
                        text: '序号',
                        xtype: 'rownumberer',
                        width: 40
                    },*/
                    {
                        text: '主键',
                        dataIndex: 'iid',
                        width: 40,
                        hidden: true
                    },
                    {
                        text: '顺序',
                        dataIndex: 'paramOrder',
                        flex: 1,
                        editor: {
                            allowBlank: false
                        },
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"';  
                            return value;  
                        }
                    },
                    {
                        text: '类型',
                        dataIndex: 'paramType',
                        flex: 1,
                        editor: paramTypeCombo,
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"'; 
                            return value;  
                        }
                    },
                    {
                        text: '默认值',
                        dataIndex: 'paramDefaultValue',
                        flex: 1,
                        editor: {
                            allowBlank: true
                        },
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"'; 
                            return value;  
                        }
                    },
                    {
                        text: '描述',
                        dataIndex: 'paramDesc',
                        flex: 1,
                        editor: {
                            allowBlank: true
                        },
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"';
                            return value;  
                        }
                    }];
    
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });
    
    
	var cellEditing3 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: tmp_uuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });
    
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
	   var paramGrid = Ext.create('Ext.grid.Panel', {
	        title: "脚本参数",
	        store: paramStore,
	        margin: '0 0 5 0',
	        selModel: selModel,
	        emptyText: '没有脚本参数',
	        plugins: [cellEditing3],
	        border: true,
	        columnLines: true,
	        columns: paramColumns,
	        tools: [{
	        	type:'plus',
	            tooltip: '增加',
	            handler: addParam
	            },
	            {
	            	type:'minus',
	                tooltip: '删除',
	                callback: function(panel, tool, event) {
	                    var data = paramGrid.getView().getSelectionModel().getSelection();
	                    if (data.length == 0) {
	                        Ext.Msg.alert('提示', '请先选择您要操作的行!');
	                        return;
	                    } else {
	                        Ext.Msg.confirm("请确认", "是否真的要删除参数？", function(button, text) {
	                            if (button == "yes") {
	                            	if(hasVersionForUpdateScriptEdit==1) {
	                            		Ext.Msg.confirm("请确认", "该脚本服务已经上线，删除参数会生成无版本号版本", function(button, text) {
	                            			if (button == "yes") {
	                            				paramStore.remove(data);
	                            				
	                            				save(0);
	                            				hasVersionForUpdateScriptEdit = 0;
	                            			}
	                            		});
	                            	} else {
	                            		var deleteIds = [];
	                                	$.each(data, function(index, record){
	                                		
	                                		if(record.data.iid>0) {
	                                			deleteIds.push(record.data.iid);
	                                		}else{
	                                		    paramStore.remove(data);
	                                		}
	                                	});
	                                	if(deleteIds.length>0){
		                                	Ext.Ajax.request({
		                                        url: 'deleteScriptParams.do',
		                                        method: 'POST',
		                                        sync: true,
		                                        params: {
		                                        	iids: deleteIds
		                                        },
		                                        success: function(response, request) {
		                                            var success = Ext.decode(response.responseText).success;
		                                            if (success) {
		                                                Ext.Msg.alert('提示', '删除成功！');
		                                                paramStore.load();
		                                            } else {
		                                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
		                                            }
		                                        },
		                                        failure: function(result, request) {
		                                            secureFilterRs(result, "保存失败！");
		                                        }
		                                    });
	                                	}else{
					                      paramGrid.getView().refresh();
					                    }
	                            	}
	                            }
	                        });
	                    }
	                }
	            }]
	    });
    
    
    
    
    
    
    
//////////////////////////脚本参数end////////////////////
    
	
	var requiredCfg = '<span style="color:red;font-weight:bold" data-qtip="按输入周期执行">  *</span>';
	Ext.define('AuditorModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'loginName',
			type : 'string'
		}, {
			name : 'fullName',
			type : 'string'
		} ]
	});

	var auditorStore_sm = Ext.create('Ext.data.Store', {
		autoLoad : true,
		model : 'AuditorModel',
		proxy : {
			type : 'ajax',
			url : 'getExecAuditorList.do?scriptLevel=1&dbaasFlag=1',//getPublishAuditorList.do
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
		editable : false,
		fieldLabel : "审核人",
		labelWidth : 65,
		store : auditorStore_sm,
		queryMode : 'local',
		columnWidth : .95,
		margin : '10 0 0 0',
		displayField : 'fullName',
		valueField : 'loginName',
		hidden : exam == 0 ? true : false,
	});
	
	var planTime_sm = Ext.create('Go.form.field.DateTime', {
		fieldLabel : '计划时间',
		format : 'Y-m-d H:i:s',
//							hidden : true,
		labelWidth : 65,
		columnWidth : .95,
		minValue: new Date(),
		margin : '10 0 0 0'
	});
	
	 var chooseResoureVersionAresource = Ext.create('Ext.form.DisplayField', {
		fieldLabel : '资源配置',
		labelWidth : 70,
		columnWidth : .6,
		margin : '10 0 10 0', 
		value:'<span style=\'color: red;\'>点我进行资源配置</span>',
		listeners: {
    	    render: function(p) {
        	    p.getEl().on('click', function(p){
        	    	$('#scripttempService_dev').each(function() {
                	    $(this).remove();
                	});
        	    	Ext.create('Ext.window.Window', {
        				title : '条件组合配置',
        				modal : true,
        				closeAction : 'destroy',
        				constrain : true,
        				autoScroll : true,
        				width : 1000,
        				height : 800,
        				draggable : false,// 禁止拖动
        				resizable : false,// 禁止缩放
        				layout : 'fit',
        				loader : {
        					url : 'goChooseResource.do',
        					params : {
        						serviceId : iid,
        						serviceGroupId:iid,
        						dbType:dbType
        					},
        					autoLoad : true,
        					scripts : true
        				}
        			}).show();
        	    });
        	}}
	});

	 var planTime_MM = Ext.create('Ext.form.field.ComboBox', {
			fieldLabel : '周期类型',
			editable : false,
			name : 'MM',
			padding : '0 5 0 0',
			matchFieldWidth:false,// 此处要有
			labelWidth :65,
			columnWidth : .35,
			store: {
				 	fields: ['value'],
				    data : [
				        {"value":"立即执行"},
				    	{"value":"按计划执行一次"},
				        {"value":"间隔x日"},
				        {"value":"间隔x小时"},
				        {"value":"间隔x分钟"}
				    ]
			 },
			 displayField:'value',
			 value:"间隔x日",
			 listeners:{
				 select : function(nf, newv, oldv) {
					},
				 change : function(nf, newv, oldv) {
					 if(newv=='间隔x日'){
						 planTime_DD.setValue('');
						 planTime_HH.setValue('');
						 planTime_mi.setValue('');
						 planTime_DD.show();
						 planTime_HH.show();
						 planTime_mi.show();
						 planTime_sm.show();
					 }else if(newv=='间隔x小时'){
						 planTime_DD.setValue('');
						 planTime_HH.setValue('');
						 planTime_mi.setValue('');
						 planTime_DD.hide();
						 planTime_HH.show();
						 planTime_mi.show();
						 planTime_sm.show();
					 }else if(newv=='间隔x分钟'){
						 planTime_DD.setValue('');
						 planTime_HH.setValue('');
						 planTime_mi.setValue('');
						 planTime_DD.hide();
						 planTime_HH.hide();
						 planTime_mi.show();
						 planTime_sm.show();
					 }else if(newv=='按计划执行一次' ){
						 planTime_DD.setValue('');
						 planTime_HH.setValue('');
						 planTime_mi.setValue('');
						 planTime_DD.hide();
						 planTime_HH.hide();
						 planTime_mi.hide();
						 planTime_sm.show();
					 }else if( newv=='立即执行'){
						 planTime_DD.setValue('');
						 planTime_HH.setValue('');
						 planTime_mi.setValue('');
						 planTime_DD.hide();
						 planTime_HH.hide();
						 planTime_mi.hide();
						 planTime_sm.hide();
					 }
				 }
			 }
		 });
	var planTime_DD = Ext.create('Ext.form.NumberField', {
		fieldLabel : '天数',
		editable : true,
		name : 'DD',
		padding : '0 5 0 0',
		labelWidth : 40,
		columnWidth : .20,
		listeners : {
			select : function(nf, newv, oldv) {
			},
			change : function(nf, newv, oldv) {
				if (null == newv || newv == '' || trim(newv) == '') {
					planTime_DD.setValue("")
				} else {
					if (/^[0-9]([0-9])*$/.test(newv)) {
						if (newv > 31) {
							Ext.Msg.alert("提示", "天数值需在1~31之间!");
							planTime_DD.setValue(oldv)
						}
						return true;
					} else {
						Ext.Msg.alert("提示", "天数窗口只能正整数");
						planTime_DD.setValue(oldv)
						return false;
					}
				}
			}
		}
	});
	var planTime_HH = Ext.create('Ext.form.NumberField', {
		fieldLabel : '小时',
		editable : true,
		name : 'HH',
		padding : '0 5 0 0',
		labelWidth : 40,
		columnWidth : .20,
		listeners : {
			select : function(nf, newv, oldv) {
			},
			change : function(nf, newv, oldv) {
				if (null == newv || newv == '' || trim(newv) == '') {
					planTime_HH.setValue("")
				} else {
					if (/^[0-9]([0-9])*$/.test(newv)) {
						if (newv > 23) {
							Ext.Msg.alert("提示", "小时值需在1~23之间!");
							planTime_HH.setValue(oldv)
						}
						return true;
					} else {
						Ext.Msg.alert("提示", "小时窗口只能正整数");
						planTime_HH.setValue(oldv)
						return false;
					}
				}
			}
		}
	});
	var planTime_mi = Ext.create('Ext.form.NumberField', {
		fieldLabel : '分钟',
		editable : true,
		name : 'mi',
		padding : '0 5 0 0',
		labelWidth : 40,
		columnWidth : .20,
		listeners : {
			select : function(nf, newv, oldv) {
			},
			change : function(nf, newv, oldv) {
				if (null == newv || newv == '' || trim(newv) == '') {
					planTime_mi.setValue("")
				} else {
					if (/^[0-9]([0-9])*$/.test(newv)) {
						if (newv > 59) {
							Ext.Msg.alert("提示", "分钟值需在1~59之间!");
							planTime_mi.setValue(oldv)
						}
						return true;
					} else {
						Ext.Msg.alert("提示", "分钟窗口只能正整数");
						planTime_mi.setValue(oldv)
						return false;
					}
				}
			}
		}
	});
		var newv=planTime_MM.getValue();
		if(newv=='间隔x日'){
			 planTime_DD.show();
			 planTime_HH.show();
			 planTime_mi.show();
		 }else if(newv=='间隔x小时'){
			 planTime_DD.setValue('');
			 planTime_HH.setValue('');
			 planTime_mi.setValue('');
			 planTime_DD.hide();
			 planTime_HH.show();
			 planTime_mi.show();
		 }else if(newv=='间隔x分钟'){
			 planTime_DD.setValue('');
			 planTime_HH.setValue('');
			 planTime_mi.setValue('');
			 planTime_DD.hide();
			 planTime_HH.hide();
			 planTime_mi.show();
		 }else if(newv=='按计划执行一次'){
			 planTime_DD.setValue('');
			 planTime_HH.setValue('');
			 planTime_mi.setValue('');
			 planTime_DD.hide();
			 planTime_HH.hide();
			 planTime_mi.hide();
		 }
	var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
		name : 'pubdesc',
		fieldLabel : '详细说明',
		emptyText : '',
		labelWidth : 65,
		margin : '10 0 0 0',
		height : 80,
		columnWidth : .95,
		autoScroll : true
	});
	var formPanel = Ext.create('Ext.form.Panel', {
		title:'基本信息',
		border : false,		
	    bodyPadding: 3,
	    autoScroll: true,
		items : [ {
//			anchor : '98%',
//			padding : '5 0 5 5',
//			border : false,
			
			items : [
			         {
				layout : 'column',
				border : false,
				padding : '5 10 5 5',
				items : [ planTime_MM, planTime_DD, planTime_HH, planTime_mi ]
			},{
				layout : 'column',
				border : false,
				padding : '5 10 5 5',
				items : [ planTime_sm]
			},
//			{
//				layout : 'column',
//				border : false,
//				padding : '5 10 5 5',
//				items : [ chooseResoureVersionAresource ]
//			},							
			{
				layout : 'column',
				border : false,
				padding : '5 10 5 5',
				items : [ auditorComBox_sm ]
			}, {
				layout : 'column',
				border : false,padding : '5 10 5 5',
				items : [ pubDesc_sm ]
			} 
			]
		} ]
	});	
	
    var mainPage_S = Ext.create('Ext.panel.Panel', {
    	region: 'center',
	    layout : 'border',
	    header : false,
	    border : false,
//		bodyPadding : grid_margin,
	    items: [chooseResource_dataSourceGrid],
	  });
    
    var mainPage_C = Ext.create('Ext.panel.Panel', {
    	region: 'west',
        border: false,
        width:'45%',
        layout:'border',
        items: [chooseGrid,permissionPanel],
 //       cls:'window_border panel_space_right'
    });
    
    var mainPage = Ext.create('Ext.panel.Panel', {
    	title:'资源配置',	
	    layout : 'border',
	    header : false,
	    border : false,
//		bodyPadding : grid_margin,
	    items: [mainPage_C,mainPage_S],
//	    renderTo : "resource_List"
	  }); 	 
	 
	var tablePanel=Ext.create('Ext.tab.Panel', {
			region : 'center',
			width : contentPanel.getWidth()*0.8,
			height : contentPanel.getHeight()*0.9-40,		
//			minHeight: 80,
			layout: {
	            type: 'border'
	        },
//		    listeners: {
//		        beforetabchange: function(tabs, newTab, oldTab) {
//		            return newTab.title != 'P2';
//		        }
//		    },
	        items: [ formPanel,mainPage,paramGrid]
		}); 
	
	 var startConfigPanel = Ext.create('Ext.panel.Panel', {
		    renderTo: "startConfig_area",
	        layout: 'border',
	        bodyPadding : grid_margin,
	        border : true,	        
		    width : contentPanel.getWidth()*0.8,
			height : contentPanel.getHeight()*0.9,			
	        bodyCls:'service_platform_bodybg',
	        items: [tablePanel],
	        buttonAlign: 'center',
			dockedItems : [ {
	            xtype: 'toolbar',
				border : false,
	//			baseCls: 'customize_gray_back',
	            items: [
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '发起',
	            	handler: function(){
	        			start();
        	
	        		}
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '取消',
	            	handler: function(){
	            		dbaasStart_publishSMWin.close();
	            		dbaasStart_publishSMWin=null;
//	                	destroyRubbish();
//	            		contentPanel.getLoader().load({
//	            			url: 'goServiceStart.do',
//	            			params: {
////	    						'switchFlag':projectFlag
//	    						
//	            			},
//	            			scripts: true
//	            		});
//	            		if (refreshTryForUpdate) {
//	                  		clearInterval(refreshTryForUpdate);
//	                  	}
//	            		$('#uploadify-base-edit').uploadify('destroy');
	                }
        	
	        	}
	            
	            ]
	        }]
	    });
	 /* 解决IE下trim问题 */
	    String.prototype.trim = function() {
	        return this.replace(/(^\s*)|(\s*$)/g, "");
	    };
	    /** 窗口尺寸调节* */
	    contentPanel.on('resize', function() {
	    	startConfigPanel.setWidth(contentPanel.getWidth()*0.8);
	    	startConfigPanel.setHeight(contentPanel.getHeight()*0.9);
	    });
	    // 当页面即将离开的时候清理掉自身页面生成的组建
	    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
	        Ext.destroy(tablePanel);
	        if (Ext.isIE) {
	            CollectGarbage();
	        }
	    });
	    function addParam() {
	        var store = paramGrid.getStore();
	        var ro = store.getCount();
	        var p = {
	        		iid: '',
	        		paramOrder: ro+1,
	        		paramType: 'IN-string',
	        		paramDefaultValue: '',
	        		paramDesc: ''
	        };
	        store.insert(0, p);
	        paramGrid.getView().refresh();
	    }
	    
	    
	    function start() {

			var planTime = planTime_sm.getRawValue();
			var planTime_MM_1 = planTime_MM.getValue();
			var planTime_DD_1 = planTime_DD.getValue();
			var planTime_HH_1 = planTime_HH.getValue();
			var planTime_mi_1 = planTime_mi.getValue();
			var scriptLevel = 100;
			var publishDesc = pubDesc_sm.getValue();
			var auditor = auditorComBox_sm.getValue();
			if (!planTime && planTime_MM_1!='立即执行') {
				Ext.Msg.alert('提示',"没有填写计划时间！");
				return;
			}

			if (!publishDesc) {
				Ext.Msg.alert('提示', "没有填写详细说明！");
				return;
			}
			if (exam == 1&& !auditor) {
				Ext.Msg.alert("提示","请选择审核人");
				return;
			} 
			if(planTime_MM_1=='间隔x日'){
				if (planTime_DD_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
					Ext.Msg.alert('提示',"天数必须填写！");
					return;
				}
			}else if(planTime_MM_1=='间隔x小时'){
				if (planTime_HH_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
					Ext.Msg.alert('提示',"小时必须填写！");
					return;
				}
			}else if(planTime_MM_1=='间隔x分钟'){
				if (planTime_mi_1==''||planTime_mi_1==null||planTime_mi_1=='null') {
					Ext.Msg.alert('提示',"分钟必须填写！");
					return;
				}
			}else if(planTime_MM_1=='按计划执行一次' || planTime_MM_1=='立即执行' ){
				
			}else{
				Ext.Msg.alert('提示',"请选择周期类型！");
				return;
			}
			var pubflag=0;
			if(isAutoSub==2){
				pubflag=1;
			}
			
			
			/**参数校验start*/
			
			var args = new Array();
			var record_args = paramStore.data;
			for (var i = 0; i < record_args.length; i++) {
				args.push(record_args.items[i].data);
			}

	        var m = paramStore.getRange();
	        var jsonData = "[";
	        for (var i = 0, len = m.length; i < len; i++) {
	            var n = 0;
	            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
	            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
	            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
	            var iorder = m[i].get("paramOrder");
	            if ("" == paramType) {
	                setMessage('参数类型不能为空！');
	                return;
	            }
	            if (fucCheckLength(paramDesc) > 250) {
	                setMessage('参数描述不能超过250字符！');
	                return;
	            }

	           if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramDefaultValue) {
	                if (!checkIsInteger(paramDefaultValue)) {
	                    setMessage('参数类型为int，但参数默认值不是int类型！');
	                    return;
	                }
	            }
	            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramDefaultValue) {
	                if (!checkIsDouble(paramDefaultValue)) {
	                    setMessage('参数类型为float，但参数默认值不是float类型！');
	                    return;
	                }
	            }
	            for ( var k = 0; k < paramStore.getCount(); k++) {
	    			var record = paramStore.getAt(k);
	    			var order = record.data.paramOrder;
	    			if (order == iorder) {
	    				n = n + 1;
	    			}
	    		}
	    		if (n > 1) {
	    			Ext.MessageBox.alert("提示", "参数顺序不能重复！");
	    			return;
	    		}
	            var ss = Ext.JSON.encode(m[i].data);
	            if (i == 0) jsonData = jsonData + ss;
	            else jsonData = jsonData + "," + ss;
	        }

	        jsonData = jsonData + "]";
			/**参数校验end*/
			
	        var record =  chooseResource_dataSourceGrid.getSelectionModel().getSelection();    		
	       if(record.length==0){
	            Ext.Msg.confirm("请确认", "没有选择资源数据，将在列表中所有资源下执行?",
	                    function(button, text) {
	                        if (button == "yes") {
	            				Ext.Ajax.request({
	            					url : 'getResourceListByVrsCount.do',
	            					method : 'POST',
	            					params : {
	            			        	versionData : chooseResoureForVersionDateNameRet.join(','),
	            			        	resourceGroupData:chooseResoureForResourceGroupDate.join(','),
	            			        	systemTypeData:chooseResoureForSystemTypeNameDate.join(','),
	            			        	jsonTypeData:chooseResoureForTypeData  ,
	            			        	dbType:dbType,
	            			        	passRac:passRac
	            					},
	            					success : function(response, opts) {
	            						var total = Ext.decode(response.responseText).total;
	            						if (total<1) {
	            				//			Ext.MessageBox.alert("提示", "所选资源组与资源版本配置中没有可用资源");
	            							Ext.MessageBox.alert("提示", "资源列表中没有可用资源");
	            							return;
	            						}
	            						Ext.Ajax.request({
	            							url : 'scriptPublishAuditingForGroup.do',
	            							method : 'POST',
	            							params : {
	            								sId : tmp_serviceId,
	            								planTime : planTime,
	            								scriptLevel : scriptLevel,
	            								publishDesc : publishDesc,
	            								auditor : auditor,
	            								flag : pubflag,
	            								planTimeType : planTime_MM_1,
	            								planTimeDD : planTime_DD_1,
	            								planTimeHH : planTime_HH_1,
	            								planTimeMM : planTime_mi_1,
	            								serviceType : serviceType,
	            								iid:tmp_serviceId,
	            								versionData : chooseResoureForVersionDateRet.join(','),
	            					        	resourceGroupData:chooseResoureForResourceGroupDate.join(','),
	            					        	systemTypeData:chooseResoureForSystemTypeDate.join(','),
	            					        	jsonTypeData:chooseResoureForTypeData,
	            					        	params:jsonData,
	            					        	passRac:passRac
	            							// 0-来着个人脚本库
	            							},
	            							success : function(response, opts) {
	            								var success = Ext.decode(response.responseText).success;
	            								var message = Ext.decode(response.responseText).message;
	            								if (!success) {
	            									Ext.MessageBox.alert("提示", message);
	            								} else {
	            									if(exam == 0){
	            										Ext.MessageBox.alert("提示", "发起成功");
	            									}else{
	            										Ext.MessageBox.alert("提示", "请求已经发送到审核人");
	            									}			
	            									
	            								}
	            								console.log("dbaasStart_publishSMWin");
	            								console.log(dbaasStart_publishSMWin);
	            								dbaasStart_publishSMWin.close();
	            								dbaasStart_publishSMWin=null;
	            							//	this.up("window").close();
	            							},
	            							failure : function(result, request) {
	            								secureFilterRs(result, "操作失败！");
	            								dbaasStart_publishSMWin=null;
	            							//	this.up("window").close();
	            							}
	            						});
	            					},
	            					failure : function(result, request) {
	            						secureFilterRs(result, "操作失败！");
	            					}
	            				});
	                        }
	                        })

	       }else{
	    	   
				var agents = new Array();
				Ext.Array.each(record,function(r) {
					agents.push(r.get('IID')+ "_1");
				});
	            Ext.Msg.confirm("请确认", "将在所选资源下执行?",
	                    function(button, text) {
	                        if (button == "yes") {
								Ext.Ajax.request({
									url : 'serviceStartDbaasForRroject.do',
									method : 'POST',
									params : {
										serviceIid : tmp_serviceId,
										auditor : auditor, // 执行人
										isDelay : false, // 是否定时延时
										agents : agents, // 服务器id
										iisexam : exam, // 是否需要审核
										args : args,
										argsData : jsonData,
										serviceType : serviceType,																		
										flag : pubflag,
										planTime:planTime,
										planTimeType : planTime_MM_1,
										planTimeDD : planTime_DD_1,
										planTimeHH : planTime_HH_1,
										planTimeMM : planTime_mi_1,
										publishDesc:publishDesc,
										sId:tmp_serviceId

									},
									success : function(response,opts) {
										var success = Ext.decode(response.responseText).success;
										var message = Ext.decode(response.responseText).message;
										if (!success) {
											Ext.MessageBox.alert("提示", message);
										} else {
											if(exam == 0){
												Ext.MessageBox.alert("提示", "发起成功");
											}else{
												Ext.MessageBox.alert("提示", "请求已经发送到审核人");
											}			
											
										}		
										dbaasStart_publishSMWin.close();
        								dbaasStart_publishSMWin=null;
									},
									failure : function(result,request) {
										secureFilterRs(result,"操作失败！");
										dbaasStart_publishSMWin=null;

									}
								});
	                        	
	                        }
	                        })
	       }

		}
	    	
	    
	
	
	
});
function queryWhereset(type){
	if(type!=''){ 
		queryWhere(type);
	}else{
		setMessage("请看选择类别，在进行属性配置！") 
	}
}
function queryWhere(type){
	if(type==0){ 
		permissionPanel.setTitle("配置 ");
		permissionPanel.getLoader().load({
			url: 'welcomeTochooseResoure.do',
			params: {serviceId : tmp_serviceId,dbType:chooseResource_dbType},
			scripts: true
		});
	} 
		if(type==1){ 
			permissionPanel.setTitle("配置 - - 版本");
			permissionPanel.getLoader().load({
				url: 'versionChoose.do',
				params: {serviceId : tmp_serviceId,dbType:chooseResource_dbType},
				scripts: true
			});
		} else if (type==2){ 
		  permissionPanel.setTitle("配置 - - 资源组");
		  permissionPanel.getLoader().load({
			  url: 'resourceGroupChoose.do',
			  params: {serviceId:tmp_serviceId,dbType:chooseResource_dbType},
			  scripts: true
		  });
		}else if (type==3){ 
			permissionPanel.setTitle("配置 --系统分类");
		    permissionPanel.getLoader().load({
		    url: 'systemTypeChoose.do',
		    params: {serviceId:tmp_serviceId,dbType:chooseResource_dbType},
		    scripts: true
		   });
		}else if (type==4){ 
			permissionPanel.setTitle("配置 --Rac");
		    permissionPanel.getLoader().load({
		    url: 'racChoose.do',
		    params: {serviceId:tmp_serviceId,dbType:chooseResource_dbType},
		    scripts: true
		   });
		}
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}