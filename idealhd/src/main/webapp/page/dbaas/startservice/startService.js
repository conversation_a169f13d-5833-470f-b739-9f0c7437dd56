
var chooseResoureForVersionDate = [];//所选版本的id集合
var chooseResoureForVersionDateRet = [];//所选的id##isLeaf集合，给最后入库提供
var chooseResoureForVersionDateNameRet = [];//所选的id##Name##isLeaf集合，给确定之后，资源展示提供
var chooseResoureForResourceGroupDate = [];//所选的资源组id集合
var chooseResoureForSystemTypeDate = [];//所选的系统类型id集合
var chooseResoureForSystemTypeNameDate = [];//所选的系统类型name集合,给确定之后，资源展示提供
var chooseResoureForTypeData;//类型关系数据
var chooseResource_dataSourceGrid;
var chooseResource_dataSourceStore;
var chooseResoureForTypeStore;
var chooseResoureForTypeStoreCount;
var serviceList_dataSourceStore;
var chooseResoureVersionAresource;
var chooseResoureForType_data = new Array();
var dbaasStart_publishSMWin;
var publishAuditingNoSqlWin;
var passRac = 0;//是否过滤Rac
Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
    Ext.define('scriptService', {
        extend: 'Ext.data.Model',
        fields: [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'serviceName',
			type : 'string'
		}, {
			name : 'sysName',
			type : 'string'
		}, {
			name : 'bussName',
			type : 'string'
		}, {
			name : 'serviceType',
			type : 'string'
		}, {
			name : 'dbType',
			type : 'string'
		}, {
			name : 'scriptType',
			type : 'string'
		}, {
			name : 'scriptName',
			type : 'string'
		}, {
			name : 'servicePara',
			type : 'string'
		}, {
			name : 'platForm',
			type : 'string'
		},{
			name : 'ssuer',
			type : 'string'
		}, {
			name : 'status',
			type : 'int'
		}, {
			name : 'content',
			type : 'string'
		}, {
			name : 'version',
			type : 'string'
		},{
			name : 'serviceTy',
			type : 'String'
		}, {
			name : 'startType',
			type : 'String'
		},{
			name : 'bussId',
			type : 'int'
		}, {
			name : 'bussTypeId',
			type : 'int'
		}, {
			name : 'scriptLevel',
			type : 'int'
		}, {
			name : 'isFlow',
			type : 'string'
		}, {
			name : 'isEMscript',
			type : 'string'
		}, {
			name : 'iappSysIds',
			type : 'string'
		}, {
			name : 'iappSysNames',
			type : 'string'
		} , {
			name : 'outTableName',
			type : 'string'
		}, {
			name : 'sqlModel',
			type : 'string'
		}, {
			name : 'serviceId',
			type : 'string'
		},{
			name : 'scriptuuid',
			type : 'string'
		},{
			name : 'isAutoSub',
			type : 'int'
		},{
			name : 'iisexam',
			type : 'int'
		},{
        	name: 'isCollected',
        	type: 'string'
        },
        {
        	name: 'scriptuuid',
        	type: 'string'
        },{
            name: 'serviceId',
            type: 'string'
        }]
    });
    
    serviceList_dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'scriptService',
        proxy: {
            type: 'ajax',
            url: 'getStartServiceList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    serviceList_dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
        	serviceName: serviceName.getValue(),
        	serviceId: serviceid.getValue()
        };
        Ext.apply(serviceList_dataSourceStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
    	text: 'UUID',
        dataIndex: 'scriptuuid',
        width: 40,
        hidden: true
    },
    {
        text: '服务ID',
        dataIndex: 'serviceId',
        flex:1,
        width: 200,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '服务名称',
        dataIndex: 'serviceName',
        flex:1,
        width: 200,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
		text : '脚本名称',
		dataIndex : 'scriptName',
		width : 120,
		flex:1,
		hidden:true,
		editor: {
	        allowBlank: false
	    },
	}, 
    {
        text: '服务类型',
        dataIndex: 'serviceType',
        width: 90,
		hidden:true,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '0') {
				backValue = '应用';
			} else if (value == '1') {
				backValue = '采集';
			} else if (value == '100') {
				backValue = '组合';
			} 
			return backValue;
		}
    },{
	    text : '一级分类',
	    dataIndex :'sysName',
	    flex:1,
	    width : 120
	}, 
	{
		text : '二级分类',
		dataIndex :'bussName',
		flex:1,
		width : 120
	},
    {
        text: '脚本类型',
        dataIndex: 'scriptType',
        width: 90,
		hidden:true,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == 'sh') {
				backValue = 'shell';
			}else if (value == 'perl') {
				backValue = 'perl';
			}else if (value == 'py'){
				backValue = 'python';
			}else if (value == 'bat'){
				backValue = 'bat';
			}else if (value == 'sql'){
				backValue = 'sql';
			}else if (value == '100'){
				backValue = '组合';
			}
			return backValue;
		}
    },
    {
        text: '数据源类型',
        dataIndex: 'dbType',
        width: 100,
		hidden:true,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	text: '发布人',
        dataIndex: 'ssuer',
        width: 100,
        hidden:true,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
    	text: '适用平台',
        dataIndex: 'platForm',
        width: 90,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },   
    {
    	text: '发起审核',
        dataIndex: 'IISEXAM',
        width: 80,
        hidden:true,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = '是';
			} else{
				backValue = '否';
			}
			return backValue;
		}
    },{
			text : '结果表名称',
			dataIndex : 'outTableName',
			width : 250,
			hidden:true,
//			editor : {
//				xtype : 'textfield'
//			},
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}	
		},
		{

			text : '收藏',
			xtype: 'actioncolumn',
	        width: 50,
	        hidden:false,
	        items: [{
	            getClass: function(v, meta, rec) {
	            		console.log(rec.get('isCollected'))
		                if (rec.get('isCollected') == '0') { // 没有收藏
		                    return 'uncollection-col';
		                } else { // 已经收藏
		                    return 'collection-col';
		                }            	
	            },
	            getTip: function(v, meta, rec) {
		                if (rec.get('isCollected') == '0') {
		                    return '收藏';
		                } else {
		                    return '取消收藏';
		                }
	            	
	            },
	            handler: function(grid, rowIndex, colIndex) {            	
	                var rec = grid.getStore().getAt(rowIndex);
	                var isCollected=rec.data.isCollected;
	                	Ext.Ajax.request({
	                		url : 'addOrCancelcollectService.do',
	                		method : 'POST',
	                		params : {
	                			scriptuuid:rec.get('scriptuuid'),
	                			serviceid:rec.get('serviceId')
	                		},
	                		success : function(response, request) {
	                			if(isCollected=='0'){
	                				rec.data.isCollected='1';
	                			}else{
	                				rec.data.isCollected='0';
	                			}   
	                			
//	                			var index=serviceList_dataSourceStore.indexOf(rec)
	                		
//	                			serviceList_dataSourceStore.remove(rec)
//	                			serviceList_dataSourceStore.insert(index,rec) ;
	                			serviceList_dataSourceStore.reload();
	                		},
	                		failure : function(result, request) {
	                			secureFilterRs(result,"操作失败！");
	                		}
	                	});                
	            }
	        }]
	    },
    {
        text: '操作',
        width: 90,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('iid');
            var exam = record.get('iisexam');
            var type = record.get('serviceType');
            var state = record.get('IFLAG');
            var uuid = record.get('scriptuuid');
            var serviceName = record.get('serviceName');
            var isAutoSub = record.get('isAutoSub');  
            var dbType = record.get('dbType');
            var scriptType = record.get('scriptType');

        	return '<div>' 
        	+ '<a href="javascript:void(0)" onclick="serviceStart('+ iid + ',' + exam + ',' + type +',\''+state+'\''+',\''+uuid+'\',\''+serviceName+'\','+isAutoSub+',\''+dbType+'\',\''+scriptType+'\')">&nbsp;发起</a>' 
        	 + '&nbsp;&nbsp;&nbsp;'                    
             + '</div>';
        }
    }];
    // 分页工具
   var pageBar = Ext.create('Ext.PagingToolbar', {
        store: serviceList_dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var serviceName = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务名称',
        labelWidth: 65,
        labelAlign: 'right',
        name: 'dataBaseNameParam',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var serviceid = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务号',
        labelWidth: 65,
        labelAlign: 'right',
        name: 'serviceid',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
	var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		baseCls: 'customize_gray_back',
		dockedItems : [ {
            xtype: 'toolbar',
			border : false,
			baseCls: 'customize_gray_back',
            items: [
            serviceName,serviceid,
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                	QueryMessage();
                }
            },
            {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: function() {
                    clearQueryWhere();
                }
            }]
        }]
	});
 var   dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	padding : panel_margin,
        store:serviceList_dataSourceStore,
        selModel: selModel,
        plugins: [cellEditing],
        border: true,
		bbar: pageBar,
		cls: 'customize_panel_back',
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        	}
        }
    });
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "serviceList_area",
        layout: 'border',
        bodyPadding : grid_margin,
        border : true,
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyCls:'service_platform_bodybg',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function clearQueryWhere() {
    	serviceName.setValue('');
    	serviceid.setValue('');
    }
});

function serviceStart(iid,exam,type,state,uuid,serviceName,isAutoSub,dbType,scriptType) {
	//把之前定义的审核数据清空
	 chooseResoureForVersionDate = [];//所选版本的id集合
	 chooseResoureForVersionDateRet = [];//所选的id##isLeaf集合，给最后入库提供
	 chooseResoureForVersionDateNameRet = [];//所选的id##Name##isLeaf集合，给确定之后，资源展示提供
	 chooseResoureForResourceGroupDate = [];//所选的资源组id集合
	 chooseResoureForSystemTypeDate = [];//所选的系统类型id集合
	 chooseResoureForSystemTypeNameDate = [];//所选的系统类型name集合,给确定之后，资源展示提供
	 chooseResoureForTypeData;//类型关系数据
	 chooseResource_dataSourceGrid;
	 chooseResource_dataSourceStore;
	 chooseResoureForTypeStore;
	 chooseResoureForTypeStoreCount;
	 serviceList_dataSourceStore;
	 chooseResoureForType_data = [];
	execServiceForExec(iid,exam,type,state,uuid,serviceName,isAutoSub,dbType,scriptType);
}

function execServiceForExec(iid,exam,type,state,uuid,serviceName,isAutoSub,dbType,scriptType){
		if(scriptType=='sql'){
			dbaasStart_publishSMWin = Ext.create('widget.window', {
					header :false,
					title : '手动发起服务',
					closable : true,
					closeAction : 'destroy',
//					width : contentPanel.getWidth(),
//					height : contentPanel.getHeight(),
					width : contentPanel.getWidth()*0.8,
					height : contentPanel.getHeight()*0.9,
					minWidth : 350,					
					draggable : false,
					// 禁止拖动
					resizable : false,
					// 禁止缩放    
					modal : true,			
					loader : {
						url : 'goConfigurateStart.do',
						params : {
							serviceId : iid,
							serviceGroupId:iid,
							dbType:dbType,
							exam:exam,
							uuid:uuid,
							isAutoSub:isAutoSub,
							serviceType:type
						},
						autoLoad : true,
						scripts : true
					}
				});
			dbaasStart_publishSMWin.show();
		}else{
			//agent类型
			publishAuditingNoSqlWin = Ext.create('widget.window', {
					title : '手动发起服务',
					closable : true,
					closeAction : 'destroy',
					width : contentPanel.getWidth()*0.8,
					height : contentPanel.getHeight()*0.9,
					minWidth : 350,					
					draggable : false,
					// 禁止拖动
					resizable : false,
					// 禁止缩放    
					modal : true,			
					loader : {
						url : 'goStartDbaForNoSql.do',
						params : {
							serviceId : iid,
							serviceGroupId:iid,
							dbType:dbType,
							exam:exam,
							uuid:uuid,
							isAutoSub:isAutoSub,
							serviceType:type,
							serviceName:serviceName,
							scriptType:scriptType
						},
						autoLoad : true,
						scripts : true
					}
				});
			publishAuditingNoSqlWin.show();
			publishAuditingNoSqlWin = Ext.create('widget.window', {});
		}			


					 

}




function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}