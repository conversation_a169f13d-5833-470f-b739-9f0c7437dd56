Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    var publishAuditingSMWin;
    var relID=0;
    var oldMainTableName;
    var oldChildTableName;
    Ext.define('paramRuleModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'int'
		}, {
			name : 'paramRuleIn',
			type : 'string'
		},{
			name : 'paramRuleType',
			type : 'int'
		},{
			name : 'paramRuleLen',
			type : 'int'
		}, {
			name : 'paramRuleOut',
			type : 'string'
		}, {
			name : 'paramRuleDesc',
			type : 'string'
		}, {
			name : 'paramRuleOrder',
			type : 'int'
		}, {
			name : 'dataList',
			type : 'string'
		} ]
	});
          
	    
    Ext.define('originalModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramRuleOrder',
            type: 'string'
        },
        {
            name: 'paramRuleDesc',
            type: 'string'
        },
        {
            name: 'paramRuleType',
            type: 'string'
        },
        {
            name: 'paramRule<PERSON>en',
            type: 'int'
        },
        {
            name: 'paramRuleOut',
            type: 'string'
        },
        {
            name: 'paramRuleIn',
            type: 'string'
        }]
    });
  
    var originalStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 1000,
        model: 'originalModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    originalStore.on('beforeload', function(store, options) {
    	
        var new_params = {
        	scriptId: scriptuuid,
            iflag:10
            
        };

        Ext.apply(originalStore.proxy.extraParams, new_params);
    });
    
    
    Ext.define('mainTableModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'columnOrder',
            type: 'string'
        },
        {
            name: 'columnName',
            type: 'string'
        },
        {
            name: 'columnType',
            type: 'string'
        },
        {
            name: 'columnLen',
            type: 'int'
        },
        {
            name: 'columnDesc',
            type: 'string'
        },{
            name: 'scriptuuid',
            type: 'String'
        },{
            name: 'tableName',
            type: 'string'
        },{
            name: 'value',
            type: 'string'
        },{
            name: 'operator',
            type: 'int'
        }]
    });
    
    
    var mainTableStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 10,
		model : 'mainTableModel',
		proxy : {
			type : 'ajax',
			url : 'getColunmsByServiceId.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
    mainTableStore.on('beforeload', function(store, options) {
		var new_params = {
			iflag : flag,
			scriptuuid:scriptuuid,
			iserviceid:iserviceid,
			mcFlag:0    //主表标识是0，子表标识是1
		};
		Ext.apply(mainTableStore.proxy.extraParams, new_params);
	});
    var childTableStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'mainTableModel',
        proxy: {
            type: 'ajax',
            url: 'getColunmsByServiceId.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    childTableStore.on('beforeload', function(store, options) {
        var new_params = {
        		iflag : flag,
        		scriptuuid:scriptuuid,
        		iserviceid:iserviceid,
    			mcFlag:1
        };

        Ext.apply(childTableStore.proxy.extraParams, new_params);
    });
//    childTableStore.on('load', function(me, records, successful, eOpts) { 
//    	$.each(records, function(index, record){
//    		attachmentIds.push(record.get('iid'));
//    	});
//	});
    var operatorStore = Ext.create('Ext.data.Store', {
		fields : [ 'name','id' ],
		data : [{
			"name" : "like",
			"id" : 1
		}, {
			"name" : ">",
			"id" : 2
		}, {
			"name" : ">=",
			"id" : 3
		}, {
			"name" : "=",
			"id" : 4
		}, {
			"name" : "<",
			"id" : 5
		}, {
			"name" : "=<",
			"id" : 6
		}]
	});

  

	var operatorCombo = Ext.create('Ext.form.field.ComboBox', {
		store : operatorStore,
		queryMode : 'local',
		forceSelection : true,
		// 要求输入值必须在列表中存在
		typeAhead : true,
		// 允许自动选择
		displayField : 'name',
		valueField : 'id',
		triggerAction : "all"
	});
    
    
    var columnTypeStore = Ext.create('Ext.data.Store', {
		fields : [ 'name','id' ],
		data : [{
			"name" : "VARCHAR",
			"id" : 0
		}, {
			"name" : "INTEGER",
			"id" : 1
		}, {
			"name" : "DECIMAL",
			"id" : 2
		}, {
			"name" : "TIMESTAMP",
			"id" : 3
		}, {
			"name" : "CLOB",
			"id" : 4
		}]
	});

  

	var mainColumnTypeCombo = Ext.create('Ext.form.field.ComboBox', {
		store : columnTypeStore,
		queryMode : 'local',
		forceSelection : true,
		// 要求输入值必须在列表中存在
		typeAhead : true,
		// 允许自动选择
		displayField : 'name',
		valueField : 'id',
		triggerAction : "all"
	});
	var childColumnTypeCombo = Ext.create('Ext.form.field.ComboBox', {
		store : columnTypeStore,
		queryMode : 'local',
		forceSelection : true,
		// 要求输入值必须在列表中存在
		typeAhead : true,
		// 允许自动选择
		displayField : 'name',
		valueField : 'id',
		triggerAction : "all"
	});
    var originalColumns = [
			{
				text : '主键',
				dataIndex : 'iid',
				width : 40,
				hidden : true
			},
			{
				text : '顺序',
				dataIndex : 'paramRuleOrder',
				width : 50,
				enableTextSelection: true  ,
				editor : {},
				renderer : function(value, metaData, record, rowIdx,
						colIdx, store) {
					metaData.tdAttr = 'data-qtip="'
							+ Ext.String
									.htmlEncode(/*
												 * " 输入：" +
												 * record.get('paramRuleIn') + "<br>
												 */"输出列名称："
											+ record
													.get('paramRuleOut')
											+ "<br>排序："
											+ record
													.get('paramRuleOrder')
											+ "<br>描述："
											+ record
													.get('paramRuleDesc'))
							+ '"';
					return value;
				}
			}, {
				text : '分隔符',
				dataIndex : 'paramRuleIn',
				width : 85,
				editor : {},
				hidden:true
			}, {
				text : '输出列名称',
				enableTextSelection: true  ,
				dataIndex : 'paramRuleOut',
				width : 140,
				editor : {
					allowBlank : false
				},
				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
   					metaData.tdAttr = 'data-qtip="'
   						+ Ext.String.htmlEncode(" 输出列名称：" + value) + '"';
   					return value;
   				}    
			},
			{
				text : '类型',
				dataIndex : 'paramRuleType',
				width : 85,
				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
					if(value==0){
						value="VARCHAR";
					}else if(value==1){
						value="INTEGER";
					}else if(value==2){
						value="DECIMAL";
					}else if(value==3){
						value="TIMESTAMP";
					}else if(value==4){
						value="CLOB";
					}else{
						value="VARCHAR"
					}
					metaData.tdAttr = 'data-qtip="'
						+ Ext.String.htmlEncode(" 类型：" + value) + '"';
					return value;
				}
			}, {
				text : '长度',
				dataIndex : 'paramRuleLen',
				width : 85,
				value:50,
				editor :  { }
			}, {
				text : '别名',
				dataIndex : 'paramRuleDesc',
				flex : 1,
				editor : {
					allowBlank : true
				},
				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
   					metaData.tdAttr = 'data-qtip="'
   						+ Ext.String.htmlEncode(" 别名：" + value) + '"';
   					return value;
   				}    
			}];
    
    var mainTableColumns = [
               			{
               				text : '主键',
               				dataIndex : 'iid',
               				width : 40,
               				hidden : true
               			},
               			{
               				id:'mainColumnOrder',
               				text : '顺序',
               				dataIndex : 'columnOrder',
               				width : 50,
//               				editor : {
//               					allowBlank : false,
//               					xtype : 'numberfield',
//               					maxValue : 30,
//               					minValue : 1
//               				},
               				renderer : function(value, metaData, record, rowIdx,
               						colIdx, store) {
               					metaData.tdAttr = 'data-qtip="'
               							+ Ext.String
               									.htmlEncode(/*
               												 * " 输入：" +
               												 * record.get('paramRuleIn') + "<br>
               												 */"输出列名称："
               											+ record
               													.get('columnName')
               											+ "<br>排序："
               											+ record
               													.get('columnOrder')
               											+ "<br>别名："
               											+ record
               													.get('columnDesc'))
               							+ '"';
               					return value;
               				}
               			}, {
               				text : '输出列名称',
               				id:'maincolumnNameId',
               				dataIndex : 'columnName',
               				width : 140,
               				editor : {
               				//	allowBlank : false
               				},
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 输出列名称：" + value) + '"';
               					return value;
               				}    
               			},
               			{
               				id:'maincolumnTypeID',
               				text : '类型',
               				dataIndex : 'columnType',
               				width : 85,
 //              				editor : mainColumnTypeCombo,
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
               					if(value==0){
               						value="VARCHAR";
               					}else if(value==1){
               						value="INTEGER";
               					}else if(value==2){
               						value="DECIMAL";
               					}else if(value==3){
               						value="TIMESTAMP";
               					}else if(value==4){
               						value="CLOB";
               					}else{
               						value="VARCHAR"
               					}
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 类型：" + value) + '"';
               					return value;
               				}               	
               			}, {
               				id:'maincolumnLen',
               				text : '长度',
               				dataIndex : 'columnLen',
               				width : 85,
               				value:50,
//               				editor :  {
//               		            xtype: 'numberfield',
//               		            maxValue: 4000,
//               		            minValue: 1
//               		        }
               			}, {
               				text : '别名',
               				id:'maincolumnDescID',
               				dataIndex : 'columnDesc',
               				flex : 1,
               				editor : {
               				//	allowBlank : true
               				},
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 别名：" + value) + '"';
               					return value;
               				}       
               			},{
               				id:'operatorID',
               				text : '运算符',
               				dataIndex : 'operator',
               				width : 85,
               				hidden:true,
               				editor : operatorCombo,
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
               					if(value==1){
               						value="like";
               					}else if(value==2){
               						value=">";
               					}else if(value==3){
               						value=">=";
               					}else if(value==4){
               						value="=";
               					}else if(value==5){
               						value="<=";
               					}else if(value==6){
               						value="<";
               					}else if(value==7){
               						value="!=";
               					}else{
               						value=""
               					}
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 运算符：" + value) + '"';
               					return value;
               				}               	
               			},{
               				text : '值',
               				id:'valueID',
               				dataIndex : 'value',
               				flex : 1,
               				hidden:true,
               				editor : {
               					allowBlank : true
               				},
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 值：" + value) + '"';
               					return value;
               				}    
               			}];
    
    var childTableColumns = [
                   			{
                   				text : '主键',
                   				dataIndex : 'iid',
                   				width : 40,
                   				hidden : true
                   			},
                   			{
                   				id:'childColumnOrder',
                   				text : '顺序',
                   				dataIndex : 'columnOrder',
                   				width : 50,
                   				editor : {
                   					allowBlank : false,
                   					xtype : 'numberfield',
                   					maxValue : 30,
                   					minValue : 1
                   				},
                   				renderer : function(value, metaData, record, rowIdx,
                   						colIdx, store) {
                   					metaData.tdAttr = 'data-qtip="'
                   							+ Ext.String
                   									.htmlEncode(/*
                   												 * " 输入：" +
                   												 * record.get('paramRuleIn') + "<br>
                   												 */"输出列名称："
                   											+ record
                   													.get('columnName')
                   											+ "<br>排序："
                   											+ record
                   													.get('columnOrder')
                   											+ "<br>描述："
                   											+ record
                   													.get('columnDesc'))
                   							+ '"';
                   					return value;
                   				}
                   			}, {
                   				text : '输出列名称',
                   				dataIndex : 'columnName',
                   				id:'childcolumnNameId',
                   				width : 180,
                   				editor : {
                   					allowBlank : false
                   				},
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 输出列名称：" + value) + '"';
                   					return value;
                   				}
                   			},
                   			{
                   				id:'childcolumnType',
                   				text : '类型',
                   				dataIndex : 'columnType',
                   				width : 85,
                   				editor : childColumnTypeCombo,
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
                   					if(value==0){
                   						value="VARCHAR";
                   					}else if(value==1){
                   						value="INTEGER";
                   					}else if(value==2){
                   						value="DECIMAL";
                   					}else if(value==3){
                   						value="TIMESTAMP";
                   					}else if(value==4){
                   						value="CLOB";
                   					}else{
                   						value="VARCHAR"
                   					}
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 类型：" + value) + '"';
                   					return value;
                   				}
                   			},
                   			{
                   				id:'childcolumnLen',
                   				text : '长度',
                   				dataIndex : 'columnLen', 
                   				width : 85,
                   				value:50,
                   				editor :  {
                   		            xtype: 'numberfield',
                   		            maxValue: 4000,
                   		            minValue: 1
                   		        }
                   			}, {
                   				text : '别名',
                   				dataIndex : 'columnDesc',
                   				id:'childcolumnDescID',
                   				flex : 1,
                   				editor : {
                   					allowBlank : true
                   				},
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 别名：" + value) + '"';
                   					return value;
                   				}
                   			}];
   

    var cellEditing1 = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 1
	});
    var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
    		clicksToEdit : 2
    	});

    var cellEditing3 = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var originalTableName =Ext.create('Ext.form.Panel', {
        width: '100%',
 //       baseCls:'customize_gray_back',
        items: [{
            xtype: 'displayfield',
            fieldLabel: '原始表名',
            labelWidth: 70,
            name: 'original',
            value: lastOutTableName
        }]
    });
    var mainName = new Ext.form.TextField({
        name: 'mainName',
        fieldLabel: '主表名',
        emptyText: '',
        labelWidth: 70,
        width:300,
        regex: /^[0-9a-zA-Z_]{1,}$/,
		regexText:'只允许输入数字、字母、下划线',
		value: outTableName+'_MAIN',
		listeners : {           
            'beforerender' : function(t, eOpts){
	            if(anaStatus==1 || anaStatus==2){
	            	t.setReadOnly(true);
	            }
            }
        }
		
    });
    var mainTableName =Ext.create('Ext.form.Panel', {
        width: '100%',
        items: [mainName]
    });
    var childName = new Ext.form.TextField({
        name: 'childName',
        fieldLabel: '子表名',
        emptyText: '',
        labelWidth: 70,
        width:300,
        regex: /^[0-9a-zA-Z_]{1,}$/,
		regexText:'只允许输入数字、字母、下划线',
		value: outTableName+'_CHILD',
		listeners : {           
            'beforerender' : function(t, eOpts){
	            if(anaStatus==1 || anaStatus==2){
	            	t.setReadOnly(true);
	            }
            }
        }
    });
    var childTableName =Ext.create('Ext.form.Panel', {
        width: '100%',
        hidden:true,
        dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			baseCls:'customize_gray_back',
			border : false,
			items : [childName,'->',{
				 text: '增加',
	             xtype : 'button',
	             cls: 'Common_Btn',
	             listeners : {           
			            'beforerender' : function(t, eOpts){
			            	if(!adddelflag){
				            	t.setDisabled(true);
				            }
			            }
			        },
				 handler: function(){
					 addchildPara();
				 }
				
			},
			{
				text: '删除',
	            xtype : 'button',
	            cls: 'Common_Btn',
	            listeners : {           
		            'beforerender' : function(t, eOpts){
		            	if(!adddelflag){         	
			            	t.setDisabled(true);
			            }
		            }
		        },
	            handler: function(){
	            	deleteChild()
           	}
			}]
		}]
    });
    var originalGrid = Ext.create('Ext.grid.Panel', {
        width: '100%',
        flex: 3,
//        title: "原始表结构",
        plugins : [ cellEditing1 ],
        store: originalStore,
        margin: '0 0 5 0',
//        selModel: selModel,
        emptyText: '没有数据',
        border: true,
        columnLines: true,
        columns: originalColumns
    });
    var mainTableGrid = Ext.create('Ext.grid.Panel',{
		width: '100%',
		flex: 2,
//		title : '主表结构',
		plugins : [ cellEditing2 ],
		store : mainTableStore,
		border : true,
		columnLines : true,
		columns : mainTableColumns,
		emptyText : '没有主表结构',
		bodyPadding : grid_margin,
	    bodyCls:'service_platform_bodybg',
		listeners : {           
            'beforeedit' : function(editor, context, eOpts){
//            	if(anaStatus==1 || anaStatus==2 || anaStatus==4){         	
           		Ext.getCmp('maincolumnDescID').setEditor('');
           		Ext.getCmp('maincolumnNameId').setEditor('');
           		Ext.getCmp('maincolumnTypeID').setEditor('');
           		Ext.getCmp('mainColumnOrder').setEditor('');
           		Ext.getCmp('maincolumnLen').setEditor('');           		
//            	}
            },
			'cellclick':function ( t, td, cellIndex, record, tr, rowIndex, e, eOpts ){
				if(record.data.columnName=='IRESID_BULID_IN' ||record.data.columnName=='ISTARTTIME_BULID_IN'){
					Ext.Msg.alert( '提示', '该列为不可编辑列');
				}
    }
        }
//		tools : [
//				{
//					type : 'plus',
//					tooltip : '增加',
//					hidden:true,
//					listeners : {           
//			            'beforerender' : function(t, eOpts){
//				            if(anaStatus==1 || anaStatus==2){
//				            	t.setDisabled(true);
//				            }
//			            }
//			        },
//					handler : 
//						addMainPara
//				},
//				{
//					type : 'minus',
//					tooltip : '删除',
//					hidden:true,
//					listeners : {           
//			            'beforerender' : function(t, eOpts){
//				            if(anaStatus==1 || anaStatus==2){
//				            	t.setDisabled(true);
//				            }
//			            }
//			        },
//					callback : function(panel, tool,
//							event) {
//						var data = mainTableGrid.getView().getSelectionModel().getSelection();
//						if (data.length == 0) {
//							Ext.Msg.alert('提示','请先选择您要操作的行!');
//							return;
//						} else {
//							Ext.Msg.confirm("请确认","是否真的要删除所选记录？",
//							function(button,text) {
//								if (button == "yes") {
//									var deleteIds = [];
//									$.each(data,function(index,record) {
//										if (record.data.iid > 0) {
//											deleteIds.push(record.data.iid);
//										} else {
//											mainTableStore.remove(data);
//										}
//									});
//									if (deleteIds.length > 0) {
//										Ext.Ajax.request({
//										url : 'deleteColumns.do',
//										method : 'POST',
//										sync : true,
//										params : {
//											iids : deleteIds,
//										},
//										success : function( response, request) {
//											var success = Ext .decode(response.responseText).success;
//											if (success) {
//												Ext.Msg.alert( '提示', '删除成功！');
//												mainTableStore .load();
//											} else {
//												Ext.Msg.alert( '提示', Ext .decode(response.responseText).message);
//											}
//										},
//										failure : function( result, request) {
//											secureFilterRs( result, "保存失败！");
//										}
//									});
//									} else {
//										mainTableGrid.getView().refresh();
//									}
//								}
//							});
//						}
//					}
//				} ]
		});
    
    
  var exitChild=0;
    var checkbox=Ext.create('Ext.form.field.Checkbox', {
    	boxLabel  : '是否关联子表',
        name      : 'topping',
        inputValue: '1',
        listeners : {
        	  "change" : function(obj,ischecked){
        		if(ischecked==true){
            		childTableName.setVisible(true);
            		childTableGrid.setVisible(true);
            		exitChild=1;
//            		childTableStore.add({columnOrder: 1,columnName:"IRESID_BULID_IN",columnType:1,columnLen:19,columnDesc:"资源Id"}, 
//            				{columnOrder: 2,columnName:"ISTARTTIME_BULID_IN",columnType:3,columnLen:19,columnDesc:"创建时间"});
        		}else{
        			childTableName.hide();
            		childTableGrid.hide();
            		exitChild=0;
//            		childTableStore.removeAll();
        		}
        		
        	 },
        	'beforerender' : function(t, eOpts){
        		if(anaStatus==1){
        			t.setReadOnly(true);
        		}
        	}

         }
    })
  var checkboxFrom=Ext.create('Ext.form.Panel', {
	  		width: '100%',
     		bodyPadding: 8,
    	    items: [checkbox]
    });
    var childTableGrid = Ext.create('Ext.grid.Panel',{
		width: '100%',
		flex: 3,
		hidden:true,
		plugins : [ cellEditing3 ],
		store : childTableStore,
		border : true,
		columnLines : true,
		columns : childTableColumns,
		emptyText : '没有子表结构',
		listeners : {            
            'beforeedit' : function(editor, context, eOpts){
            	if(anaStatus==1 || anaStatus==4){         	     	
               		Ext.getCmp('childcolumnDescID').setEditor('');
               		Ext.getCmp('childcolumnNameId').setEditor('');
	           		Ext.getCmp('childcolumnType').setEditor('');
	           		Ext.getCmp('childColumnOrder').setEditor('');
	           		Ext.getCmp('childcolumnLen').setEditor('');
            	}
            },
            'cellclick':function ( t, td, cellIndex, record, tr, rowIndex, e, eOpts ){
            	if(record.data.columnName=='IRESID_BULID_IN' || record.data.columnName=='ISTARTTIME_BULID_IN'){
            		Ext.Msg.alert( '提示', '该行不可编辑');
            	}
            },
        }
		});
  //-----------------------------------  
    Ext.define('originalModel_group', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'iserviceid',
            type: 'string'
        },
        {
        	name: 'iservicename',
        	type: 'string'
        },
        {
            name: 'tablename',
            type: 'string'
        }]
    }); 
    var originalStore_group = Ext.create('Ext.data.Store', {
        autoLoad: isautosub==2,
        autoDestroy: true,
        pageSize: 10,
        model: originalModel_group,
        proxy: {
            type: 'ajax',
            url: 'getOuttableEachSInG.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    originalStore_group.on('beforeload', function(store, options) {
        var new_params = {
        	gid : iserviceid
        };

        Ext.apply(originalStore_group.proxy.extraParams, new_params);
    });
    var originalColumns_group = [
		{
			text : '主键',
			dataIndex : 'iid',
			width : 40,
			hidden : true
		},
		{
			text : '服务ID',
			dataIndex : 'iserviceid',
			width : 100
		},
		{
			text : '绑定服务',
			dataIndex : 'iservicename',
			width : 100
		},
		{
			text : '结果表名称',
			dataIndex : 'tablename',
			editor : {
					allowBlank : true
				},
			flex :1
		}];
    var originalGird_group =Ext.create('Ext.grid.Panel', {
        width: '100%',
        height:35,
        title: "原始表组",
        flex: 2,
        store: originalStore_group,
        margin: '0 0 5 0',
        selModel: selModel,
        plugins : [ Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
		}) ],
        emptyText: '没有脚本参数',
        border: true,
        columnLines: true,
        columns: originalColumns_group
    });
  //--------------------------------  
	var	paramItems = [originalTableName,originalGrid,mainTableName, mainTableGrid,checkboxFrom,childTableName,childTableGrid ];
	if(isautosub==2){
			paramItems = [originalGird_group,mainTableName, mainTableGrid,checkboxFrom,childTableName,childTableGrid ];
	}
    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
    	region: 'east',
        collapsible : false,
        border: false,
        width: '40%',
        layout:'vbox',
        items: paramItems,
        cls:'window_border panel_space_right'
    });
    
	                 
    var bbarItems = [ {xtype : 'tbtext',text : '提示：当光标位于编辑器内部时，可以使用F11按键进入<b>全屏编辑模式</b>'} ];
	
    var endAnalyName = new Ext.form.DisplayField({
    	name: 'endAnalyName',
    	margin:'0 10 0 10'
    });
    var analyName = new Ext.form.TextField({
        name: 'analyName',
        emptyText: '输入算法名称',
        width:300,
        margin:'0 10 0 10',
        regex: /^[0-9a-zA-Z_]{1,}$/,
		regexText:'只允许输入数字、字母、下划线',
		enableKeyEvents:true,
        listeners:{                 
             keyup:function(){        
            	 endAnalyName.setValue(analyName.getValue()+";");
             }               
         }
    });
	var defaultPanel = Ext.create('Ext.form.Panel', {
		bodyCls : 'x-docked-noborder-top',
		border : false,
		region: 'north',
		items : [
				{
					layout : 'column',
					border : false,
					items : [{
			            xtype: 'displayfield',
			            name: 'analyStart',
			            margin:'0 0 0 20',
			            value: "CREATE OR REPLACE PROCEDURE"
			        },analyName ,{
			            xtype: 'displayfield',
			            name: 'analyEnd',
			            value: "( AV_FLOW_ID  IN NUMBER) AS "
			        },{
			            xtype: 'displayfield',
			            name: 'analyEnd',
			            cls:'field-red', 
			            margin:'0 0 0 20',
			            value: "说明:算法调用传递本次执行流程ID，通过流程ID字段“IFLAG_BULID_IN”到表中查询本次运行的资源“IRESID_BULID_IN”信息"
			        }]
				}
			]
	});
	var southPanel = Ext.create('Ext.form.Panel', {
		bodyCls : 'x-docked-noborder-top',
		border : false,
		region: 'south',
		items : [{
			layout : 'column',
			border : false,
			items : [{
				xtype: 'displayfield',
				name: 'analyStart',
				margin:'0 0 0 20',
				value: "END "
			},endAnalyName]
			}]
		});
    
    var mainP = Ext.create('Ext.panel.Panel', {
        border: true,
        region: 'center',
        autoScroll: false,
        margin:'5 0 5 0',
       // title: "定义算法: "+iserviceid,
        html: '<textarea id="setAnaCode-edit" value style="height:100%;" placeholder="请输入算法代码..."></textarea>',
        buttonAlign: 'center'
    });
	var mainAnalyP = Ext.create('Ext.panel.Panel', {
	    border: true,
	    region: 'center',
	    autoScroll: false,
	    layout: {
	        type: 'border'
	    },
	    title: "定义算法: "+iserviceid,  bbar: bbarItems,
	    items: [defaultPanel, mainP,southPanel]
	});
    var buttonItems= [{
        text: '提交',
//        cls: 'Common_Btn',
        handler: function(){
        	save(1);
        }
    },{
        text: '保存',
//        cls: 'Common_Btn',
        handler: function(){
        	save(0); // 正常保存
        }
    },{
        text: '内容初始化',
//      cls: 'Common_Btn',
        hidden:!delAndBlank,
      handler: function(){
    	  analyInit(); // 正常保存
      }
  },{
        text: '删除',
        hidden:!delAndBlank,
//      cls: 'Common_Btn',
      handler: function(){
      	del();
      }
  },{
        text: '返回',
//        cls: 'Common_Btn',
        handler: function(){
        	destroyRubbish();
    		contentPanel.getLoader().load({
    			url: 'analyzeManage.do',
    			params: {
					'filter_serviceName': filter_serviceNameForSetAnalyze,
					'filter_scriptStatus':filter_scriptStatusForSetAnalyze,
					'filter_serviceType':filter_serviceTypeForSetAnalyze,					
    			},
    			scripts: true
    		});
    		if (refreshTryForUpdate) {
          		clearInterval(refreshTryForUpdate);
          	}
    		$('#uploadify-base-edit').uploadify('destroy');
        }
    }]
    var westPanel = Ext.create('Ext.panel.Panel', {
    	region: 'center',
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        items: [mainAnalyP],
	    buttonAlign: 'center',
	    cls:'window_border customize_panel_back panel_space_right',
	    buttons: buttonItems
    });     
   
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "setAnalyzeFun_area",
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
    	border : true,
    	bodyPadding : 0,
	    bodyCls:'service_platform_bodybg',
        height: contentPanel.getHeight()-modelHeigth ,
        items: [westPanel, paramsAndFuncDescPanel]
    });

    var editor = CodeMirror.fromTextArea(document.getElementById('setAnaCode-edit'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        extraKeys: {
            "F11": function(cm) {
              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
            },
            "Esc": function(cm) {
              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
            }
          }
    });
    editor.setOption("mode", 'text/x-plsql');
    
    editor.setOption('value', "\nBEGIN \n \r DECLARE \n  LI_RETURN   SMALLINT; \n BEGIN \n\r \r \r END;");
    editor.setSize(mainAnalyP.getWidth()-2, mainAnalyP.getHeight()-modelHeigth-110);  
    contentPanel.on('resize',
    function() {
        editor.getDoc().clearHistory();
        mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainAnalyP.getWidth()-2, mainAnalyP.getHeight()-modelHeigth-110);
 
    });    
    westPanel.on('resize', function() {
    	editor.getDoc().clearHistory();
    	editor.setSize(mainAnalyP.getWidth()-2, mainAnalyP.getHeight()-modelHeigth-110);
    });
            
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };  
    
    //保存脚本信息
    //保存脚本信息
    function save(ignoreFlag) { 
    	var url;
    	if(flag==0){
    		url= "saveAnalyze.do";
    	}else{
    		url= "updateAnalyze.do";
    	}    	
    	if(anaStatus==1) {
    		Ext.Msg.alert('提示', "该算法正处于审核中，不能进行修改");
    		return;
    	}
    	var mainjsonData = "[";
		var patt1=new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
		var m = mainTableStore.getRange();
		var outArr = {};
		var mainContain =[];
		var maincolumnType=[];
		var operateValue;
		var retValue;
		var analyNames = analyName.getValue();
		if(analyNames=='' || analyNames.trim()=='') {
    		Ext.Msg.alert('提示', "请输入算法名称");
    		return;
    	}
		for (var i = 0, len = m.length; i < len; i++) {
			var n = 0;			
			var columnOrder = m[i].get("columnOrder") ? m[i].get("columnOrder").trim() : '';
            var columnName = m[i].get("columnName") ? m[i].get("columnName").trim() : '';
            var columnLen = m[i].get("columnLen") ? m[i].get("columnLen") : '';
            var columnType = m[i].get("columnType") ? m[i].get("columnType").trim() : '';
            var columnDesc = m[i].get("columnDesc") ? m[i].get("columnDesc").trim() : '';
            var operator = m[i].get("operator") ? m[i].get("operator"): 0;
            var value = m[i].get("value") ? m[i].get("value").trim() : '';
			if ("" == columnOrder) {
				Ext.MessageBox.alert("提示",'输出规则顺序不能为空！');
				return;
			}
			columnName =columnName.toUpperCase();
			if ("" == columnName) {
				Ext.MessageBox.alert("提示",'输出列名称不能为空！');
				return;
			}
			if ("IID" == columnName) {
				Ext.MessageBox.alert("提示",'主表中IID为内置字段,请重新填写！');
				return;
			}
			if("IRESULT" ==columnName){
				operateValue=operator;
				retValue=value;
			}
			if (mainContain.contains(columnName)>0) {
				Ext.MessageBox.alert("提示",'输出列名称不能重复！');
				return;
			}else{
				mainContain.push(columnName)
			}
			if(!patt1.test(columnName)){
				Ext.MessageBox.alert("提示",'请正确填写输出列名称！');
				return;
			}
			if (columnLen == 0) {
				Ext.MessageBox.alert("提示",'长度不能为0字符！');
				return;
			}else{
				if(columnType==2){
					if(columnLen>25){
						Ext.MessageBox.alert("提示",'NUMBER类型的长度最大为25!');
						return;
					}
				}
			}
			if (fucCheckLength(columnDesc) > 250) {
				Ext.MessageBox.alert("提示",'输出规则描述不能超过250字符！');
				return;
			}
			if (outArr[columnOrder]) {
				Ext.MessageBox.alert("提示",'输出规则序号不能重复！');
				return;
			} else {
				outArr[columnOrder] = true;
			}
			maincolumnType.push(columnType);
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				mainjsonData = mainjsonData + ss;
			else
				mainjsonData = mainjsonData + "," + ss;
		}
		if(maincolumnType.contains(3)==0){
			Ext.MessageBox.alert("提示",'主表中没有Date类型字段，请重新填写！');
			return;
		}
		mainjsonData = mainjsonData + "]";
		
    	var childjsonData = "[";
		var child = childTableStore.getRange();
		var coutArr = {};
		var carrContain =[];		
		for (var i = 0, len = child.length; i < len; i++) {
			var n = 0;			
			var columnOrder = child[i].get("columnOrder") ? child[i].get("columnOrder").trim() : '';
            var columnName = child[i].get("columnName") ? child[i].get("columnName").trim() : '';
            var columnLen = child[i].get("columnLen") ? child[i].get("columnLen"): '';
            var columnType = child[i].get("columnType") ? child[i].get("columnType").trim() : '';
            var columnDesc = child[i].get("columnDesc") ? child[i].get("columnDesc").trim() : '';
			if ("" == columnOrder) {
				Ext.MessageBox.alert("提示",'输出规则顺序不能为空！');
				return;
			}
			columnName =columnName.toUpperCase();
			if ("" == columnName) {
				Ext.MessageBox.alert("提示",'输出列名称不能为空！');
				return;
			}
			
			if ("IID" == columnName) {
				Ext.MessageBox.alert("提示",'子表中IID为内置字段,请重新填写！');
				return;
			}
			if (carrContain.contains(columnName)>0) {
				Ext.MessageBox.alert("提示",'输出列名称不能重复！');
				return;
			}else{
				carrContain.push(columnName)
			}
			if(!patt1.test(columnName)){
				Ext.MessageBox.alert("提示",'请正确填写输出列名称！');
				return;
			}
			
			if (columnLen == 0) {
				Ext.MessageBox.alert("提示",'长度不能为0字符！');
				return;
			}else{
				if(columnType==2){
					if(columnLen>25){
						Ext.MessageBox.alert("提示",'NUMBER类型的长度最大为25!');
						return;
					}
				}
			}
			if (fucCheckLength(columnDesc) > 250) {
				Ext.MessageBox.alert("提示",'输出规则描述不能超过250字符！');
				return;
			}
			if (coutArr[columnOrder]) {
				Ext.MessageBox.alert("提示",'输出规则序号不能重复！');
				return;
			} else {
				coutArr[columnOrder] = true;
			}
			var ss = Ext.JSON.encode(child[i].data);
			if (i == 0)
				childjsonData = childjsonData + ss;
			else
				childjsonData = childjsonData + "," + ss;
		}
		
		childjsonData = childjsonData + "]";
        editor.save();
        var content = document.getElementById('setAnaCode-edit').value;
        if (content.trim() == '') {
        	Ext.MessageBox.alert("提示", "分析算法内容不能为空！");
                return;
            }
        var originaName=originalTableName.getForm().findField("original").getRawValue();
        var mainName=mainTableName.getForm().findField("mainName").getRawValue();
        var reg = new RegExp("^[a-zA-Z]");
		if(!reg.test(mainName)){
			alert('主表名称首字符必须是字母，请重新输入');
			return;
		}
        var childName='';
        if(exitChild==1){
        	childName=childTableName.getForm().findField("childName").getRawValue();
			if(!reg.test(childName)){
				alert('子表名称首字符必须是字母，请重新输入');
				return;
			}
        	
        }
        if(originaName==mainName){
        	Ext.MessageBox.alert("提示",'主表名不能和原始表名重复！');
			return;
        }        
        if(mainName==childName){
        	Ext.MessageBox.alert("提示",'主表名不能和子表名重复！');
			return;
        }
        if(exitChild==1 && childTableStore.getCount()==2){
        	Ext.MessageBox.alert("提示",'请为子表定义表数据接收列！');
			return;
        }
				Ext.Ajax.request({
							url : url,
							method : 'POST',
			//				sync: true,
							async: false,
							params : {
								scriptuuid : scriptuuid,
								mainData:mainjsonData,
								childData:childjsonData,
								content : content,
								originaName:originaName,
								mainName:mainName,
								childName:childName,
								oldMainTableName:oldMainTableName,
								oldChildTableName:oldChildTableName,
								lastOutTableName:lastOutTableName,
								anaStatus:anaStatus,
								exitChild:exitChild,
								operate:operateValue,
								value:analyNames,
								iserviceid:iserviceid
							},
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;		
								var message = Ext.decode(response.responseText).message;
								if(flag==0){
									relID=Ext.decode(response.responseText).relId;
						    	}								
								if( ignoreFlag==1 && success) {    
									 publish(content);//提交审核
									 return;
								} else {
									Ext.Msg.alert('提示', message);	
									if (success) {
										 childTableStore.load();
										 mainTableStore.load();		
										 editScriptStore.load();	
										 destroyRubbish();
								    		contentPanel.getLoader().load({
								    			url: 'setAnalyzeFun.do',
								    			params: {
								    				scriptuuid : scriptuuid,
													outTableName : outTableName,
													lastOutTableName : lastOutTableName,
													flag:1,
													anaStatus:anaStatus,
													filter_serviceName:filter_serviceNameForSetAnalyze,
													filter_serviceType:filter_serviceTypeForSetAnalyze,
													filter_scriptStatus:filter_scriptStatusForSetAnalyze,
													iserviceid:iserviceid,
													isautosub:isautosub
					
								    			},
								    			scripts: true
								    		});
								    		if (refreshTryForUpdate) {
								          		clearInterval(refreshTryForUpdate);
								          	}
								    		$('#uploadify-base-edit').uploadify('destroy');
										 
									} 
								}
								
						

							},
							failure : function(result, request) {
								secureFilterRs(result, "保存失败！");
							}
						});
		if(isautosub==2){
			saveOriginal_group();
		}
		
		
    }
    function analyInit() { 
    	Ext.Msg.confirm("请确认","是否真的要初始化算法内容，若选择“是”，则该服务对应的“所有算法内容将会被清除，主/表不会被改动”",
			function(button,text) {
				if (button == "yes") {
					Ext.Ajax.request({
						url : 'initAnalyContent.do',
						method : 'POST',
						async: false,
						params : {
							scriptuuid : scriptuuid,
							iserviceid:iserviceid
						},
						success : function(response, request) {
							var success = Ext.decode(response.responseText).success;		
							var message = Ext.decode(response.responseText).message;
							if(success){
								 destroyRubbish();
						 		contentPanel.getLoader().load({
					    			url: 'setAnalyzeFun.do',
					    			params: {
					    				scriptuuid : scriptuuid,
										outTableName : outTableName,
										lastOutTableName : lastOutTableName,
										flag:1,
										anaStatus:anaStatus,
										filter_serviceName:filter_serviceNameForSetAnalyze,
										filter_serviceType:filter_serviceTypeForSetAnalyze,
										filter_scriptStatus:filter_scriptStatusForSetAnalyze,
										iserviceid:iserviceid,
										isautosub:isautosub
		
					    			},
					    			scripts: true
					    		});
							}
						},
						failure : function(result, request) {
							secureFilterRs(result, "初始化算法内容失败！");
						}
					});
				}
    	});
    
    }
    function del() { 
    	Ext.Msg.confirm("请确认","是否真的要删除该算法，删除算法会连带删除算法所创建的“主/子表及已经生效的算法”？",
			function(button,text) {
				if (button == "yes") {
					Ext.Ajax.request({
						url : 'delAnalyData.do',
						method : 'POST',
						async: false,
						params : {
							scriptuuid : scriptuuid,
							iserviceid:iserviceid
						},
						success : function(response, request) {
							var success = Ext.decode(response.responseText).success;		
							var message = Ext.decode(response.responseText).message;
							if(success){
								 destroyRubbish();
						    		contentPanel.getLoader().load({
						    			url: 'analyzeManage.do',
						    			params: {
											'filter_serviceName': filter_serviceNameForSetAnalyze,
											'filter_scriptStatus':filter_scriptStatusForSetAnalyze,
											'filter_serviceType':filter_serviceTypeForSetAnalyze,					
						    			},
						    			scripts: true
						    		});
						    		if (refreshTryForUpdate) {
						          		clearInterval(refreshTryForUpdate);
						          	}
						    		$('#uploadify-base-edit').uploadify('destroy');
					    	}								
						},
						failure : function(result, request) {
							secureFilterRs(result, "删除失败！");
						}
					});
				}
    	});
    } 
    
    function addMainPara() {
   	 var store = mainTableGrid.getStore();
        var ro = store.getCount();
        var p = {
        		iid: '',
        		columnOrder: ro+1,
        		columnName: '',
        		columnType: 0,
        		columnLen: 50,
        		columnDesc: ''
        };
        store.insert(0, p);
        mainTableGrid.getView().refresh();
   }
    
    function addchildPara () {
      	 var store = childTableGrid.getStore();
         var ro = store.getCount();
         var p = {
         		iid: '',
         		columnOrder: ro+1,
         		columnName: '',
         		columnType: 0,
         		columnLen: 50,
         		columnDesc: ''
         };
         store.insert(0, p);
         childTableGrid.getView().refresh();
    }
    
    
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'queryOneAnalyze.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
        		scriptuuid: scriptuuid,
        		iserviceid: iserviceid
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
    	if(flag!=0){
            var reader = store.getProxy().getReader();
            mainName.setValue(reader.jsonData.main);
            var child=reader.jsonData.child;
            if(child==null){
            	checkbox.setValue(false)
            }else{
            	if(child!=outTableName){
            		checkbox.setValue(true)
                	childName.setValue(child);
            	}else{
            		checkbox.setValue(false);
                	childTableName.hide();
            		childTableGrid.hide();
            		exitChild=0;
            	}
            	
            }
 //           editor.setOption("mode", 'text/x-plsql');
            editor.setOption('value', reader.jsonData.analyzeText);
            analyName.setValue(reader.jsonData.analyName);
            endAnalyName.setValue(reader.jsonData.analyName+";");
            relID=reader.jsonData.relId;
            oldMainTableName=reader.jsonData.main;
            oldChildTableName=reader.jsonData.child;
    	}

    });
    
    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '申请说明',
        emptyText: '',
        labelWidth: 85,
        margin : '10 0 0 0',
        height: 80,
        columnWidth:.98,
        autoScroll: true
    });
    
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	var auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do?dbaasFlag=1',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    editable: true,
	    fieldLabel: "审核人",
	    labelWidth: 85,
	    store: auditorStore_sm,
	    queryMode: 'local',
	    columnWidth:.98,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    valueField: 'loginName',
	    listeners: { //监听 
	    	select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
			blur:function(combo, records, eOpts){
				var displayField =auditorComBox_sm.getRawValue();
				if(!Ext.isEmpty(displayField)){
					//判断输入是否合法标志，默认false，代表不合法
					var flag = false;
					//遍历下拉框绑定的store，获取displayField
					auditorStore_sm.each(function (record) {
						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
					    var data_fullName = record.get('fullName');
					    if(data_fullName == displayField){
					    	flag =true;
					    	combo.setValue(record.get('loginName'));
					    }
					});
					if(!flag){
					 	Ext.Msg.alert('提示', "输入的审核人非法");
					 	auditorComBox_sm.setValue("");
					 	return;
					} 
				}
				
			},
			beforequery: function(e) {				
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            } 
	    } 
	  });
	var  auditing_form_sm = Ext.create('Ext.form.Panel', {
		width : 600,
		layout : 'anchor',
		bodyCls : 'x-docked-noborder-top',
		buttonAlign : 'center',
		border : false,
		items : [ {
			anchor : '98%',
			padding : '5 0 5 0',
			border : false,
			items : [
					{
						layout : 'column',
						border : false,
						items : [ auditorComBox_sm ]
					},
					{
						layout : 'column',
						border : false,
						items : [ pubDesc_sm ]
					}
				]
		} ]
	});
    function publish(content){    	
		 Ext.Ajax.request({
		    url : 'analyzeStatus.do',
		    method : 'POST',
		    params : {
		    	scriptuuid: scriptuuid,
		    	iserviceid: iserviceid
		    },
		    success: function(response, opts) {
		        var status = Ext.decode(response.responseText).status;
		        if(status==1) {
		    		Ext.Msg.alert('提示', "该算法正处于审核中！");
		    		return;
		    	} else {
					publishAuditingSMWin = Ext.create('widget.window', {
		                title: '确认审核信息',
		                closable: true,
		                closeAction: 'hide',
		                resizable: false,
		                modal: true,
		                width: 600,
		                minWidth: 350,
		                height: 300,
		                layout: {
		                    type: 'border',
		                    padding: 5
		                },
		                items: [auditing_form_sm],
		                dockedItems : [ {
							xtype : 'toolbar',
							dock : 'bottom',
							layout: {pack: 'center'},
							items : [ { 
				  			xtype: "button",
				  			cls:'Common_Btn',
				  			text: "确定", 
				  			handler: function () { 
				  				var publishDesc = pubDesc_sm.getValue();
				  				var auditor = auditorComBox_sm.getValue();
				  				if(!publishDesc) {
				  					Ext.Msg.alert('提示', "没有填写发布申请说明！");
				  					return;
				  				}
				  				if(publishDesc.length > 255) {
				  					Ext.Msg.alert('提示', "发布申请说明内容长度超过255个字符！");
				  					return;
				  				}
				  				if(!auditor) {
				  					Ext.Msg.alert('提示', "没有选择审核人！");
				  					return;
				  				}
				  				
				  				Ext.Ajax.request({
				  				    url : 'analyzePublishAuditing.do',
				  				    method : 'POST',
				  				    params : {
				  				    	relId : relID,
				  				    	publishDesc: publishDesc,
				  				    	auditor: auditor,
				  				    	content:content
				  				    },
				  				    success: function(response, opts) {
				  				        var success = Ext.decode(response.responseText).success;
				  				        var message = Ext.decode(response.responseText).message;
				  				        if(!success) {
				  				        	Ext.MessageBox.alert("提示", message);
				  				        } else {
				  				        	anaStatus=1;
				  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
				  				        }
				  				      publishAuditingSMWin.close();
				  				      
				  				    },
				  				    failure: function(result, request) {
				  				    	secureFilterRs(result,"操作失败！");
				  				    	publishAuditingSMWin.close();
				  				    }
				  			    });
				  				
					        }
				  		}, { 
				  			xtype: "button", 
				  			cls:'Common_Btn',
				  			text: "取消", 
				  			handler: function () {
				  				this.up("window").close();
				  			}
				  		}]
		                }]
		            });
		            
		        
				publishAuditingSMWin.show();
				auditorStore_sm.load();
				pubDesc_sm.setValue('');
		    	}
	    	}
    	})        	           		   	  		    		    					    		   			  	   
    }
    
    function deleteChild(){
		var data = childTableGrid.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示','请先选择您要操作的行!');
			return;
		}
		if (data[0].data.columnName=='IRESID_BULID_IN' || data[0].data.columnName=='ISTARTTIME_BULID_IN') {
			Ext.Msg.alert('提示','该行不可删除!');
			return;
		} else {
			Ext.Msg.confirm("请确认","是否真的要删除所选记录？",
			function(button,text) {
				if (button == "yes") {
					var deleteIds = [];
					$.each(data,function(index,record) {
						childTableStore.remove(data);
						if (record.data.iid > 0) {
							deleteIds.push(record.data.iid);
						} else {
							childTableGrid.remove(data);
						}
						if (deleteIds.length > 0) {
							Ext.Ajax.request({
							url : 'deleteColumns.do',
							method : 'POST',
							sync : true,
							params : {
								iids : deleteIds,
							},
							success : function( response, request) {
								var success = Ext .decode(response.responseText).success;
								if (success) {
									Ext.Msg.alert( '提示', '删除成功！');
									childTableStore .load();
								} else {
									Ext.Msg.alert( '提示', Ext .decode(response.responseText).message);
								}
							},
							failure : function( result, request) {
								secureFilterRs( result, "保存失败！");
							}
						});
						} else {
							childTableGrid.getView().refresh();
						}
					});
				}
			});
		}
	
    }
    
    function saveOriginal_group(){
    	var m = originalStore_group.getModifiedRecords();
    	if (m.length < 1) {
            return;
        }
        var jsonData = "[";
        for (var i = 0; i < m.length; i++) {
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveOriginalGroup.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
            	originalStore_group.modified = [];
            	originalStore_group.reload();
            },
            failure: function(result, request) {
            	Ext.Msg.alert( '提示', "操作失败！");
            }
        });
    }
});