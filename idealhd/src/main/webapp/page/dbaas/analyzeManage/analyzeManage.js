Ext.onReady(function() {
	destroyRubbish();

    var serviceTypeStore= Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [{
            "id": "0",
            "name": "应用"
        },
        {
            "id": "1",
            "name": "采集"
        }
        ]
    });

    var serviceType = Ext.create('Ext.form.field.ComboBox', {
        name: 'serviceType',
        labelWidth: 70,
        labelAlign: 'right',
        width: '20%',
        queryMode: 'local',
        fieldLabel: '服务类型',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '--请选择服务类型--',
        store: serviceTypeStore,
    	listeners: {
            afterRender: function(combo) {
               if(filter_serviceType=='0') {
					combo.setValue(serviceTypeStore.getAt(0).data.id);
				} else if(filter_serviceType=='1'){
					combo.setValue(serviceTypeStore.getAt(1).data.id);
				}
            }
        }
    });

 

	var sName = new Ext.form.TextField({
		name : 'serviceName',
		fieldLabel : '服务名称',
		displayField : 'serverName',
		emptyText : '--请输入服务名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		value: filter_serviceName,
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
	});

	var statusStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "-1",
			"name" : "全部"
		}, {
			"id" : "1",
			"name" : "启用"
		}, {
			"id" : "2",
			"name" : "禁用"
		} ]
	});


	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true
	});

	var	searchItems=[ {
			xtype : 'toolbar',
			baseCls: 'customize_gray_back',
			dock : 'top',
			items : [ sName, serviceType, {
				fieldLabel : '状态',
				labelWidth : 40,
			    labelAlign : 'right',
				width : '15%',
				name : 'status',
				displayField : 'name',
				valueField : 'id',
				store : statusStore,
				hidden:true,
				queryMode : 'local',
				listeners : {
					afterRender : function(combo) {
						combo.setValue(statusStore.getAt(0).data.id);
						if(filter_serviceStatus=='-1') {
							combo.setValue(statusStore.getAt(0).data.id);
						} else if(filter_serviceStatus=='1'){
							combo.setValue(statusStore.getAt(1).data.id);
						}else if(filter_serviceStatus=='2'){
							combo.setValue(statusStore.getAt(2).data.id);
						}
				
					}
				},
				editable : false,
				xtype : 'combobox'
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function() {
					pageBar.moveFirst();
				}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				// columnWidth:.07,
				text : '清空',
				handler : function() {
					clearQueryWhere();
				}
			}]
		} ];
		
	var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		border : false,
		bodyCls : 'x-docked-noborder-top',
		baseCls: 'customize_gray_back',
		dockedItems : searchItems
	});
	Ext.define('scriptService', {
			extend : 'Ext.data.Model',
			fields : [ {
				name : 'iid',
				type : 'string'
			}, {
				name : 'scriptuuid',
				type : 'string'
			}, {
				name : 'serviceName',
				type : 'string'
			},{
				name : 'serviceType',
				type : 'string'
			}, {
				name : 'dbType',
				type : 'string'
			}, {
				name : 'scriptType',
				type : 'string'
			},  {
				name : 'platForm',
				type : 'string'
			},{
				name : 'ssuer',
				type : 'string'
			}, {
				name : 'status',
				type : 'int'
			}, {
				name : 'content',
				type : 'string'
			}, {
				name : 'version',
				type : 'string'
			},{
				name : 'iappSysIds',
				type : 'string'
			}, {
				name : 'iappSysNames',
				type : 'string'
			} , {
				name : 'outTableName',
				type : 'string'
			},
			{
				name : 'anaStatus',
				type : 'string'
			},
			{
				name : 'serviceId',
				type : 'string'
			},
			{
				name : 'isAutoSub',
				type : 'string'
			}]
		});
	var scriptservice_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'scriptService',
		proxy : {
			type : 'ajax',
			url : 'getScriptServiceListforAna.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	Ext.define('AppSysModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		} ]
	});

	Ext.define('AppSysModel1', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'int',
			useNull : true
		}, {
			name : 'name',
			type : 'string'
		} ]
	});
	var appSysStore1 = Ext.create('Ext.data.Store', {
		autoLoad : false,
		autoDestroy : true,
		model : 'AppSysModel1',
		proxy : {
			type : 'ajax',
			url : 'getAppSysList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	appSysStore1.on('load', function() {
		var ins_rec = Ext.create('AppSysModel', {
			id : '-1',
			name : '未选系统'
		});
		appSysStore1.insert(0, ins_rec);

	});
	var clickGroudRow;

	var scriptservice_columns = [
                 {
					 text : '序号',
					 align:'left',
                	 xtype : 'rownumberer',
                	 width : 65
                 },
                 {
                	 text : '主键',
                	 dataIndex : 'iid',
                	 hidden : true
                 },
                 {
         		    text : '服务主键uuid',
         		    dataIndex : 'scriptuuid',
         		    width : 40,
         		    hidden : true
         		},{
         			text : '服务号',
         		    dataIndex : 'serviceId',
         		    width : 120
         		},
                 {
                	 text : '服务名称',
                	 dataIndex : 'serviceName',
            		minWidth : 200,
        			flex:1,
         			 renderer : function(value, metadata) {
        				metadata.tdAttr = 'data-qtip="' + value + '"';
        				return value;
        			}	
                 },               
                 {
                	 text : '适用平台',
                	 dataIndex : 'platForm',
                	 hidden : true,
                	 width : 100
                 },{
                	 text : '单服务/组合',
                	 dataIndex : 'isAutoSub',
                	 width :50,
                	 hidden:true
                 },
                {
                	 text : '服务类型',
                	 dataIndex : 'serviceType',
                	 width : 80,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == 0) {
                			 backValue = "应用";
                		 } else if (value == 1) {
                			 backValue = "采集";
                		 }	
                		 return backValue;
                	 }
                 }, {
                	 text : '数据库类型',
                	 dataIndex : 'dbType',
                	 width : 80
                 },
                 {
                	 text : '脚本类型',
                	 dataIndex : 'scriptType',
                	 width : 80,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == "sh") {
                			 backValue = "shell";
                		 } else if (value == "perl") {
                			 backValue = "perl";
                		 } else if (value == "py") {
                			 backValue = "python";
                		 } else if (value == "bat") {
                			 backValue = "bat";
                		 } else if (value == "sql") {
                			 backValue = "sql";
                		 }
                		 if (record.get('isFlow') == '1') {
                			 backValue = "组合";
                		 }
                		 return backValue;
                	 }
                 }, {
         			text : '发布人',
        			dataIndex : 'ssuer',
        			width : 120,
         			 renderer : function(value, metadata) {
            				metadata.tdAttr = 'data-qtip="' + value + '"';
            				return value;
            			}	
        		},{
                	 text : '版本',
                	 dataIndex : 'version',
                	 width : 60
                 },                               
                 {
                	 text : '所属系统',
                	 dataIndex : 'iappSysNames',
                	 width : 80,
                	 hidden : true,
//                	 editor : appSysCombo,
                	 renderer : function(value, metadata, record) {
                		 var iappSysIds = record.data.iappSysIds;
                		 var value1 = iappSysIds.split(',');
                		 var str = '';
                		 for (var i = 0, len = iappSysIds.length; i < len; i++) {
                			 var index = appSysStore1.find('id', value1[i]);
                			 if (index != -1) {
                				 if (str != '') {
                					 str = str + "|"
                					 + appSysStore1.getAt(index).data.name;
                				 } else {
                					 str = str + appSysStore1.getAt(index).data.name;
                				 }
                			 }
                		 }
                		 record.data.iappSysNames = str;
                		 return str;
                	 }
                 
                 },
                 {
                	 text : '状态',
                	 dataIndex : 'status',
                	 width : 60,
                	 hidden:true,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                	
                		 if (value == 1) {
                			 backValue = "启用";
                		 } else if (value == 2) {
                			 backValue = "禁用";
                		 }
                		 return backValue;
                	 }
                 },  {
                	 text : '分析算法状态',
                	 dataIndex : 'anaStatus',
                	 width : 120,
                	 renderer : function(value, p, record) {
                		 var backValue = "";
                		 if (value == -1) {
                			 backValue = "未定义";
                		 } else if (value == 0) {
                			 backValue = "草稿"
                		 } else if (value == 1) {
                			 backValue = "审核中";
                		 } else if (value == 2) {
                			 backValue = "已上线";
                		 } else if (value == 3) {
                			 backValue = "拒绝";
                		 } else if (value == 4) {
                			 backValue = "变更拒绝";
                		 }               		 
                		 return backValue;
                	 }
                 }, {
         			text : '结果表名称',
        			dataIndex : 'outTableName',
        			minWidth : 160,
        			flex:1,
        			editor : {
        				xtype : 'textfield'
        			},
        			renderer : function(value, metadata) {
        				metadata.tdAttr = 'data-qtip="' + value + '"';
        				return value;
        			}	
        		},
                 {
                    text : '监控指标',
                    dataIndex: 'xq',
                    hidden:true,
                    width : 80,
                    renderer : function(value, p, record) {
        		    var scriptuuid = record.get('scriptuuid'); 
                    var serviceId = record.get('serviceId'); 
                        return '<div><a href="javascript:void(0)" onclick="viewDetailForMonitoring(\'' + serviceId +'\',\''+scriptuuid +'\')" style="">维护</a></div>';                                                                                                                
                    }
                 },
                 {
                	 text : '操作',
                	 dataIndex : 'xq',
                	 width : 150,
                	 renderer : function(value, p, record) {
                		 var scriptuuid = record.get('scriptuuid');
                		 var outTableName = record.get('outTableName'); 
                		 var anaStatus=record.get('anaStatus'); 
 						 var filter_serviceName=sName.getValue();
          				 var filter_serviceType=serviceType.getValue();
          				 var filter_scriptStatus=search_form.getForm().findField("status").getValue();
          				 var iserviceid = record.get('serviceId');
          				 var isautosub = record.get('isAutoSub');
          				 var serviceId = record.get('serviceId');
          				 
                 		 return '<div><a href="javascript:void(0)" onclick="setAnalyzeFun(\'' + scriptuuid +'\',\''+outTableName +'\',\''+filter_serviceName+'\',\''+filter_serviceType+'\',\''+filter_scriptStatus+'\','+anaStatus+',\''+iserviceid+'\',\''+isautosub+'\')" style="">算法定义</a>'
                 		 +'&nbsp;&nbsp;&nbsp;<a href="javascript:void(0)" onclick="toAnalyzeVersion(\''+iserviceid+'\')">查看版本</a></div>';                		                		                                	                     		 
                	 }
                 } ];

	scriptservice_store.on('beforeload',function(store, options) {
	var new_params = {
				serviceName : search_form.getForm()
						.findField("serviceName").getValue(),
				status : 1,//search_form.getForm().findField("status").getValue(),
				serviceType : search_form.getForm().findField("serviceType").getValue()||-1,
				switchFlag:1
			};
			Ext.apply(scriptservice_store.proxy.extraParams, new_params);
		});

	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : scriptservice_store,
		dock : 'bottom',
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		border:false,
		displayInfo : true
	});
	appSysStore1.load({
		callback : function(records, operation, success) {
			scriptservice_store.load();
		}
	});
	var scriptservice_grid = Ext.create('Ext.grid.Panel', {
		region : 'center',
		store : scriptservice_store,
		plugins : [ Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 2
		}) ],
		border:true,	
		padding : panel_margin,
		bbar : pageBar,
		columnLines : true,
		cls: 'customize_panel_back',
		columns : scriptservice_columns,
		selModel : selModel
	});
	
	scriptservice_grid.on("celldblclick", function(obj, td, cellIndex, record,tr, rowIndex, e, eOpts) {
		clickGroudRow = rowIndex;
	})

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "analyzeManage_area",
		layout : 'border',
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		bodyPadding : grid_margin,
		border : true,
		bodyCls:'service_platform_bodybg',
		// bodyPadding : 5,
		items : [ search_form, scriptservice_grid ]
	});
	contentPanel.on('resize', function() {
		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.setHeight() - modelHeigth);
	});
	function clearQueryWhere() {
		search_form.getForm().findField("serviceName").setValue('');
		search_form.getForm().findField("status").setValue('-1');
		search_form.getForm().findField("serviceType").setValue('');
		serviceType.clearValue();
	}
});
var flag=0;
//编辑按钮执行一个.do跳转到编辑页面   + iid   
function setAnalyzeFun(scriptuuid,outTableName,filter_serviceName,filter_serviceType,filter_scriptStatus,anaStatus,iserviceid,isautosub){
	Ext.Ajax.request({
		url : 'checkColumsBySid.do',
		method : 'POST',
		params : {
			scriptuuid:scriptuuid,
			iserviceid:iserviceid
		},
		success : function(response, request) {
			flag = Ext.decode(response.responseText).flag;
			contentPanel.getLoader().load({
		        url: "setAnalyzeFun.do",
		        scripts: true,
		        params: {
		        	scriptuuid : scriptuuid,
					outTableName : outTableName,
					flag:flag,
					anaStatus:anaStatus,
					filter_serviceName:filter_serviceName,
					filter_serviceType:filter_serviceType,
					filter_scriptStatus:filter_scriptStatus,
					iserviceid:iserviceid,
					isautosub:isautosub
		        }
		    });	

		},
		failure : function(result, request) {
//			secureFilterRs(result,"操作失败！");
		}
	});
	
	
}
function toAnalyzeVersion(sid){
	Ext.create('Ext.window.Window', {
		title: '分析算法版本',
		height: contentPanel.getHeight(),  //Number型  也可以是字符串类型  width:'60%'
		width: contentPanel.getWidth(),
		layout: 'fit',
		constrain: true, 		//闲置窗口不超出浏览器
		constrainHeader:true, 	//标题不能超出浏览器边界
		modal: true,			//设置模态窗口
		plain:true, 			//窗口设置透明背景
		draggable: false,
		resizable: false,
		loader: {
			url: 'toAnalyzeVersion.do',
			params : {
				sid : sid
			},
			autoLoad: true,
			scripts: true
		}
	}).show();
}
function viewDetailForMonitoring(serviceId,scriptuuid) {
//  if (!DetailWinTi) {
      var DetailWinTi = Ext.create('widget.window', {
          title: '维护监控指标',
          closable: true,
          closeAction: 'destroy',
          width: contentPanel.getWidth()*0.7,
          minWidth: 350,
          height: contentPanel.getHeight()*0.7,
          draggable: false,
          // 禁止拖动
          resizable: false,
          // 禁止缩放
          modal: true,
          loader: {
              url: 'viewDetailForMonitoring.do',
              params: {
                  scriptuuid: scriptuuid,
                  serviceId: serviceId
              },
              autoLoad: true,
              scripts: true
          }
      });
      DetailWinTi.show();
  }