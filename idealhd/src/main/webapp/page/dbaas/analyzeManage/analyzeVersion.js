Ext.onReady(function() {
	destroyRubbish();
	//alert(SID)
	Ext.define('versionModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'iserviceid',
			type : 'string'
		}, {
			name : 'iversion',
			type : 'string'
		}, {
			name : 'icreatetime',
			type : 'string'
		}, {
			name : 'content',
			type : 'string'
		} ,{
			name : 'serviceName',
			type : 'string'
		} ]
	});
	var versionStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 50,
		model : 'versionModel',
		proxy : {
			type : 'ajax',
			url : 'getAnalyzeVersion.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	versionStore.on('beforeload',function(store, options) {
		var new_params = {
					sid:SID
				};
		Ext.apply(versionStore.proxy.extraParams, new_params);
	});
	var versionColumns = [
        {
			text : '序号',
			align:'left',
	       	xtype : 'rownumberer',
	       	width : 65
        },{
	       	text : '主键',
	       	dataIndex : 'iid',
	       	hidden : true
        },{
		    text : '服务名',
		    dataIndex : 'serviceName',
		    flex:1
		},
        {
		    text : '服务ID',
		    dataIndex : 'iserviceid',
		    flex:1
		},{
			text : '版本',
			dataIndex : 'iversion',
			width : 160
		},{
			text : '创建时间',
			dataIndex : 'icreatetime',
			width : 160
		}//{
//			text : '操作',
//			flex : 1,
//			renderer : function(value, p, record) {
//				 return '<div><a href="javascript:void(0)" onclick="">查看内容</a>'
//			}
//		}
		];
	var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		store : versionStore,
		dock : 'bottom',
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		border:false,
		displayInfo : true
	});
	
	var grid = Ext.create('Ext.grid.Panel', {
		region : 'center',
		width: "45%",
		store : versionStore,
		border:true,	
		padding : panel_margin,
		bbar : pageBar,
		columnLines : true,
		 margins : 8,
		//cls: 'customize_panel_back',
//		cls:'panel_space_left ',
		columns : versionColumns,
		//selModel : selModel
	});
	var content = Ext.create('Ext.form.field.TextArea', {
		fieldLabel : '内容',
		padding : panel_margin,
		labelWidth : 40,
		region : 'east',
	    labelAlign:'top',
	    margins : 8,
	    width: "50%",
	    name: 'content',
//	    cls:'panel_space_right', 
	    emptyText : '点击左侧记录查看内容',
	    allowBlank: true,
	    editable: false
	});

	grid.on("cellclick", function(obj, td, cellIndex, record,tr, rowIndex, e, eOpts) {
		//alert(record.get("content"))
		content.setValue(record.get("content"));
	})
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "analyzeVersion_area",
		layout : 'border',
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		bodyPadding : grid_margin,
		border : true,
		bodyCls:'service_platform_bodybg',
		// bodyPadding : 5,
		items : [ grid ,content]
	});
	contentPanel.on('resize', function() {
		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.setHeight() - modelHeigth);
	});
})