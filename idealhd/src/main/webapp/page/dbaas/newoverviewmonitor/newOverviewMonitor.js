/*******************************************************************************
 * 新资源看板
 ******************************************************************************/
Ext.onReady(function() {
	var newcenterDataView;
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.define('NewOverviewMonitorModel', {
		extend : 'Ext.data.Model',
		fields: [{
			name: 'IID',
			type: 'long'
		},{
			name: 'INAME',
			type: 'string'
		},{
			name: 'IIP',
			type: 'string'
		},{
			name: 'IPORT',
			type: 'string'
		},{
			name: 'IRSTYPE',
			type: 'string'
		},{
			name: 'IVERSION',
			type: 'string'
		},{
			name: 'IPLATFORM',
			type: 'string'
		},{
			name: 'IFLAG',
			type: 'string'
		},{
			name: 'ICREATETIME',
			type: 'long'
		},{
			name: 'IUPDATETIME',
			type: 'long'
		},{
			name: 'ICPU',
			type: 'string'
		},{
			name: 'ICPUUSE',
			type: 'string'
		},{
			name: 'IMEMORY',
			type: 'string'
		},{
			name: 'IMEMORY_REMAIN',
			type: 'string'
		},{
			name: 'IDISK',
			type: 'string'
		},{
			name: 'IDISK_REMAIN',
			type: 'string'
		},{
			name: 'IPASSWORD',
			type: 'String'
		},{
			name: 'ISYSDBNAME',
			type: 'string'
		},{
			name: 'ISID',
			type: 'string'
		},{
			name: 'iType',
			type: 'string'
		}]
	});

	var centerStore = Ext.create('Ext.data.Store', {
		model : 'NewOverviewMonitorModel',
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'resourceBaseList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	centerStore.on("load", function(store) {
		// 数据加载完成后执行
		// shanshuo(store);
	})
	newcenterDataView = Ext.create('Ext.view.View',{
		store : centerStore,
		tpl : [
			'<tpl for=".">',
			'<div class="view_mtor2">',
				'<div class="v_mtor_cn">',
					'<span class=" <tpl >  v_mtor_blue v_color_icon  </tpl>"></span>',
					'<span class="v_mtor_text">',
						'<h1>{IIP}</h1>',
						'<h2>服务器IP</h2>',
					'</span>',
					
					'<span class="v_mtor_link"><a href="javascript:void(0)" onclick="newresDisplay(\'{IIP}\');">详情+</a></span>',
				'</div>',
				'<div class="v_mtor_list">',
					'<ul>',
						'<li>',
							'<span class="v_mtor_row">版本</span>',
							'<span class="v_mtor_row02">{IVERSION}</span>',
						'</li>',
						'<li>',
							'<span class="v_mtor_row">设备名称</span>',
							'<span class="v_mtor_row02">{INAME}</span>',
						'</li>',
						'<li>',
							'<span class="v_mtor_row">资源类型</span>',
							'<span class="v_mtor_row02">{IRSTYPE}</span>',
						'</li>',
						'<li>',
							'<span class="v_mtor_row">所属平台</span>',
							'<span class="v_mtor_row03" >{IPLATFORM}</span>',
						'</li>',
						'<li>',
							'<span class="v_mtor_row">类型</span>',
							'<span class="v_mtor_row03" >{iType}</span>',
						'</li>',
					'</ul>',
				'</div>',
			'</div>',
			'</tpl>',
		],
		multiSelect : true,
		trackOver : true,
		emptyText : '没有监控方案'

	});

	var resultsPanel = Ext.create('Ext.panel.Panel', {
		width : contentPanel.getWidth(),
		border : false,
		height : contentPanel.getHeight() - modelHeigth,
		overflowY : 'auto',
		renderTo : 'newresbasedisplay_area',
		items : [ newcenterDataView ]
	});

});

function newresDisplay(iip){
	contentPanel.getLoader().load({
		url: 'gotoResDisplay.do',
		params : {
			iip : iip	
		},
		scripts: true
	});
}