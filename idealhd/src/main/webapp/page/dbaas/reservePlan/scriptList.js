/*******************************************************************************
 *预案定义页绑定脚本操作
 ******************************************************************************/
Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	/** *********************Store********************* */
	var scriptStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    pageSize : 50,
	    fields : [
	           'serviceId', 'scriptName','scriptType','dbType','isAutoSub','catalogName','lableName','version','serviceType'
	    ],
	    proxy :
	    {
	        type : 'ajax',
	        url : 'findScriptInfoForReservePlan.do' ,
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	scriptStore.on('beforeload', function (store, options) {
	    var new_params = {  
	    		serviceName:sName.getValue().trim(),
	    		serviceType:serviceTypeParam.getValue(),
	    		iDbType:iDbType
	    };
	    Ext.apply(scriptStore.proxy.extraParams, new_params);
    });
	
	   var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
		  	store: scriptStore,
		  	pageSize : 50,
		  	dock: 'bottom',
			baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
			displayInfo: true,
			border:false,
			displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
			emptyMsg: "没有记录"
   });
	   
	   var serviceTypeStore = Ext.create('Ext.data.Store', {
			fields : [ 'value', 'text' ],
			data : [ {
				"value" : "-1",
				"text" : "全部"
			}, {
				"value" : "0",
				"text" : "应用"
			},
			 {
				 "value": "1",
				 "text": "采集"
			}]
		});
		var serviceTypeParam = Ext.create('Ext.form.field.ComboBox', {
			name : 'serviceType',
			padding : '0 5 0 0',
			labelWidth : 65,
			columnWidth : .5,
			queryMode : 'local',
			fieldLabel : '脚本类型',
			displayField : 'text',
			valueField : 'value',
			editable : false,
			emptyText : '--请选择服务类型--',
			store : serviceTypeStore,
			listeners : {
			afterRender : function(combo) {
				}
			}
		});
		
		var sName = new Ext.form.TextField({
			name : 'serverName',
			fieldLabel : '脚本名称',
			emptyText : '--请输入脚本名称--',
			labelWidth : 65,
			width :'150',
	        labelAlign : 'right',
	        listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	pageBar.moveFirst();
	                }
	            }
	        }
		});
		var	searchForm = Ext.create('Ext.ux.ideal.form.Panel', {
			 	region:'north',
		    	layout : 'anchor',
		    	buttonAlign : 'center',
		    	bodyCls : 'x-docked-noborder-top',
		    	border : false,
		    	dockedItems : [{
					xtype : 'toolbar',
					border : false,
					baseCls:'customize_gray_back',
					dock : 'top',
					items: [sName,serviceTypeParam,{   
						xtype : 'button',
						text : '查询',
						cls : 'Common_Btn',
						handler : function() { 
						queryData();
						}
					},{
						xtype : 'button',
						text : '清空',
						cls : 'Common_Btn',
						handler : function() {
							clearQueryWhere();
						}
					},'->', {
						xtype: 'button',
						cls:'Common_Btn',
						margin: '0 5 0 0',
						text : '保存',
						handler: relationReservePlanScript
					}]
				}]
			});
		
		function relationReservePlanScript(){
			var seledCnt = selModel.getCount();
			if(seledCnt < 1){
				Ext.MessageBox.alert("提示", "请选择要绑定的脚本！");
				return ;
			}else if(seledCnt > 1){
				Ext.MessageBox.alert("提示", "只能选择一个脚本绑定！");
				return ;
			}
	        var mess="是否绑定选中的脚本？";
			Ext.MessageBox.buttonText.yes = "确定"; 
			Ext.MessageBox.buttonText.no = "取消"; 
			Ext.Msg.confirm("确认绑定", mess, function(id){if(id=='yes') relation();});
		}
		function relation(){
			var	url = "relationReservePlanScript.do";
			var flowIdList = scriptPanel.getSelectionModel().getSelection();
			var iServiceId=flowIdList[0].data.serviceId;
		    Ext.Ajax.request({
			    url : url,
			    method : 'POST',
			    params : {
			    	iServiceId : iServiceId,
			    	reservePlanId: reservePlanId
			    },
			    success: function(response, opts) {
		    		var message1 = Ext.decode(response.responseText).message;
		    		Ext.MessageBox.show({
		                title : "提示",
		                msg : message1,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		    		parent.aaa();
		    		var win = parent.Ext.getCmp('showAllScriptForReservePlan');
		    	    if (win) {win.close();}
			    },
			    failure: function(result, request) {
			    	secureFilterRs(result,"操作失败！");
			    }
		    });
		}
		
		function clearQueryWhere(){
			sName.setValue('');
			serviceTypeParam.setValue('');
		}
		function queryData(){
			pageBar.moveFirst();
		}
	var selModel=Ext.create('Ext.selection.CheckboxModel', {
		mode : "SINGLE",
		checkOnly: true
	});
		
	var scriptPanel = Ext.create ('Ext.grid.Panel',
	{
		region:'center',
	    height : 380,
	    width : '100%',
	    border : true,
	    selModel : selModel,
	    store : scriptStore,
	    columns : [
	    	{
				text : '序号',
				xtype : 'rownumberer',
				width : 70
			},
			{
			    text : '服务号',
			    dataIndex : 'serviceId',
			    width : 100,
			    hidden:true,
			    renderer : function(value, metadata) {
			    metadata.tdAttr = 'data-qtip="' + value + '"';
			    return value;
			    }
			},
			{
				text : '脚本名称',
				dataIndex : 'scriptName',
				width : 100,
				flex:1
			}, 
			{
			    text : '脚本类型',
			    dataIndex : 'scriptType',
			    width : 100,
			    hidden:true,
			    renderer:function(value,p,record,rowIndex){
			    	var backValue = "";
					if (value == "sh") {
						backValue = "shell";
					} else if (value == "perl") {
						backValue = "perl";
					} else if (value == "py") {
						backValue = "python";
					} else if (value == "bat") {
						backValue = "bat";
					} else if (value == "sql") {
						backValue = "sql";
					}
					return backValue;
			    }
			}, 
			{
				text : '数据库类型',
			    dataIndex : 'dbType',
			    width : 90,
			    renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},{
				 text : '执行账号',
		            dataIndex : 'userType',
		            width : 100,
		            renderer : function(value, metadata) {
		                if(value=='0') {
		                    return "查询";
		                }else {
		                    return "操作";
		                }
		             }
	        },{
	            text : '编目',
	            dataIndex : 'catalogName',
	            width : 100,
	            flex:1
	        },{
	            text : '标签',
	            dataIndex : 'lableName',
	            width : 100,
	            flex:1
	        },{
				text : '版本号',
			    dataIndex : 'version',
			    width : 80
			},{
				text : '脚本类型',
			    dataIndex : 'serviceType',
			    width : 100
			}
	    ],
	    bbar : pageBar,
	    padding : panel_margin,
	    columnLines : true,
	    cls:'customize_panel_back'
	});
	
	/** 主panel* */
	var mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "MainDiv",
	    layout : 'border',
	    width : '100%',
	    height : '100%',
	    border : false,
	    bodyPadding : 5,
	    items : [
	    	searchForm,scriptPanel
	    ]
	});
	
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
});
