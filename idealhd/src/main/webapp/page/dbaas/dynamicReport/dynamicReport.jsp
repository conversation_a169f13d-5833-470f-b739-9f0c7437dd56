<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>动态结果展示</title>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="js/jquery.nicescroll.js"></script>
<link rel="stylesheet" href="css/analysisstyle.css">
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/report/echarts.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/dynamicReport/dynamicReport.js"></script>
</head>
<body>
	<div class="data_demon">
		<div>
			<select id="select" class="data_selct"></select>
		   	<span class="data_st_time">开始时间：<input id="startTime" name="startTime" class="start_time" class="Wdate" onclick="WdatePicker({skin:'whyGreen',maxDate: '%y-%M-%d' })"/></span>
		   	<span class="data_st_time">结束时间：<input id="endTime" name="endTime" class="start_time" class="Wdate" onclick="WdatePicker({skin:'whyGreen',maxDate: '%y-%M-%d' })"/></span>
		   	<button type="button" onclick="getData()" class="Common_Btn d_srch_btn">查询</button>
		</div>
		<div class="data_chart" id="graph">暂无数据</div>
		<div class="data_demon_tab" id="list">
			<!-- <table cellpadding="0" cellspacing="0" border="0">
				<thead>
					<tr>
						<th>姓名</th>
						<th>年龄</th>
						<th>性别</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>张三</td>
						<td>23</td>
						<td>男</td>
					</tr>
				</tbody>
			</table> -->
		</div>
	</div>
</body>
<script type="text/javascript">
	$(".data_demon_tab").niceScroll({
		autohidemode : true,
		cursorcolor : "#c6cbd6",
		cursorborder : "1px solid #c6cbd6"
	});
</script>
</html>