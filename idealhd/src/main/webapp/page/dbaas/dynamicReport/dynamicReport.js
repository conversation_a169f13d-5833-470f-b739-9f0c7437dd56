Ext.onReady(function() {
	var editor;
	Ext.define('dataSourceModel', {});
	
    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        }        ]
    });
    var sysDataStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageList.do?projectFlag=1',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	var operationSystem = new Ext.form.field.ComboBox (
			{
				name : 'operationSystem',
				fieldLabel : '数据类型',
				displayField : 'sysName',
				valueField : 'sysName',
			    emptyText: '--请选择数据类型--',
			    labelWidth : "80",
			    padding : '5 5 10 5',
			    queryMode: 'local',
			    width : '30%',
			    editable: false,
			    store: sysDataStore
			});
	var sqlForm = new Ext.form.FormPanel ({
		layout : 'anchor',
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',border : false,
			dock : 'top',
			items : [ operationSystem, {
				fieldLabel : '开始时间',
				xtype : 'datefield',
				labelAlign : 'right',
				width : '22%',
				labelWidth : 70,
				name : 'startTime',
				format : 'Y-m-d H:i:s',
				value : filter_startTime
			}, {
				fieldLabel : '结束时间',
				xtype : 'datefield',
				labelAlign : 'right',
				width : '22%',
				labelWidth : 70,
				name : 'endTime',
				format : 'Y-m-d H:i:s',
				value : filter_endTime
			}, {
				xtype : 'button',
				text : '查询',
				cls : 'Common_Btn',
				handler : function() {
				}
			}]
		} ]
	});
	
	var sqlPanel = Ext.create('Ext.panel.Panel', {
		width : "50%",
		border:true,
		region : 'west',
		split : true,
		layout : "fit",
		items : [ sqlForm]
	});
	 Ext.define('DataModel', {
        extend: 'Ext.data.Model',
        fields: [
        	{ name: 'ccorder', type: 'int' },
        	{ name: 'ccin', type: 'string' }, 
        	{ name: 'ccout', type: 'string' }, 
        	{ name: 'ccdesc', type: 'string' }
        ]
    });

	var dbform = new Ext.form.FormPanel (
			{
				region: 'north',
				layout : 'column',
				bodyPadding : 5,
				border:false,
				items : [fileUpload, dbType, ip,dbport, sid,username, password
       		        ],
			    dockedItems : [
	       		    {
	    				xtype : 'toolbar',
	    				border : false,
	    				dock : 'top',
	    				items: [executeButton,
       		 		        saveButton,
       		 		        backButton]
	    			}
	       	    ]
			});	

	var paramRulesStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramRuleModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do?iflag=1',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	paramRulesStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: oldIID
        };
        Ext.apply(paramRulesStore.proxy.extraParams, new_params);
    });
	var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
	 var paramRulesColumns = [
          {
              text: '主键',
              dataIndex: 'iid',
              width: 40,
              hidden: true
          },{
              text: '顺序',
              dataIndex: 'paramRuleOrder',
              width: 50,
              editor: {
                  allowBlank: false
              },
              renderer:function (value, metaData, record, rowIdx, colIdx, store){  
              	 metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 输入："+record.get('paramRuleIn') 
                   		+"<br>输出："+record.get('paramRuleOut')
                   		+"<br>排序："+record.get('paramRuleOrder')
                   		+"<br>描述："+record.get('paramRuleDesc')) 
                   		+ '"'; 
                  return value;  
              }
          },
          {
              text: '规则',
              dataIndex: 'paramRuleIn',
              width: 85,
              editor: {
                  allowBlank: false
              }
          },
          {
              text: '输出',
              dataIndex: 'paramRuleOut',
              width: 80,
              editor: {
                  allowBlank: false
              }
          },
          {
              text: '描述',
              dataIndex: 'paramRuleDesc',
              flex: 1,
              editor: {
                  allowBlank: true
              }
          }];
	 Ext.define('paramRuleModel', {
	        extend: 'Ext.data.Model',
	        fields: [{
	            name: 'iid',
	            type: 'int'
	        },
	        {
	            name: 'paramRuleIn',
	            type: 'string'
	        },
	        {
	            name: 'paramRuleOut',
	            type: 'string'
	        },
	        {
	            name: 'paramRuleDesc',
	            type: 'string'
	        },
	        {
	            name: 'paramRuleOrder',
	            type: 'int'
	        }]
	    });
	 //输出规则
	var outruleGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        title: '输出规则',
        plugins: [cellEditing2],
        store: paramRulesStore,
        border: true,
        columnLines: true,
        columns: paramRulesColumns,
        emptyText: '没有规则参数',
        tools: [{
    	    type:'plus',
        	tooltip: '增加',
            handler: function() {
            	paramRulesStore.insert(0, new paramRuleModel());
            	cellEditing2.startEditByPosition({
                    row: 0,
                    column: 0
                });
            }
        },
        {
        	type:'minus',
            tooltip: '删除',
            callback: function(panel, tool, event) {
                var data = outruleGrid.getView().getSelectionModel().getSelection();
                if (data.length == 0) {
                    Ext.Msg.alert('提示', '请先选择您要操作的行!');
                    return;
                } else {
                    Ext.Msg.confirm("请确认", "是否真的要删除参数？", function(button, text) {
                        if (button == "yes") {
                        	var deleteIds = [];
                        	$.each(data, function(index, record){
                        		if(record.data.iid>0) {
                        			deleteIds.push(record.data.iid);
                        		}else{
                        			paramRulesStore.remove(data);
                        		}
                        	});
                        if(deleteIds.length>0){
                        	Ext.Ajax.request({
                                url: 'deleteScriptRuleParams.do',
                                method: 'POST',
                                sync: true,
                                params: {
                                	iids: deleteIds,
                                	iflag:"1"
                                },
                                success: function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    if (success) {
                                        Ext.Msg.alert('提示', '删除成功！');
                                        paramRulesStore.load();
                                    } else {
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                    }
                                },
                                failure: function(result, request) {
                                    secureFilterRs(result, "保存失败！");
                                }
                            });
                         }else{
                        	 outruleGrid.getView().refresh();
                         }
                        }
                    });
                }
            }
        }]
    });
	
	var exportPanel = Ext.create('Ext.panel.Panel', {
//		layout : 'fit',
		region : 'center',
		  layout: {
	            type: 'border'
	        },
	    height:contentPanel.getHeight() - modelHeigth,
	    border : true,
		items : [ dbform/*, outruleGrid*/]//, grid 
	});
	var tablePanel = Ext.create('Ext.panel.Panel', {
		height : contentPanel.getWidth() ,
		border : false,
		loader : {
			url : 'detailSubGeneral.do?tmp='+ new Date().getTime(),
			autoLoad : true,
			scripts : true
		}
	});
	var rightPanel = Ext.create('Ext.panel.Panel', {
		border:false,
		region : 'south',
		split : true,
//		collapsed : true,
		width : "100%",
		height:'50%',
//		layout : "fit",
		items : [ tablePanel]
	});
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "generalCreate",
		layout : 'border',
		 bodyCls:'service_platform_bodybg',
		    bodyPadding : grid_margin,
		    border : true,
		width:contentPanel.getWidth() ,
		height : contentPanel.getHeight() - modelHeigth-20,
		items : [ sqlPanel, exportPanel,rightPanel ]
	});
	editor = CodeMirror.fromTextArea(document.getElementById('code-sqlArea'),
			{
				mode : 'sql',
				lineNumbers : true,
				matchBrackets : true,
				extraKeys : {
					"F11" : function(cm) {
						cm.setOption("fullScreen", !cm
								.getOption("fullScreen"));
					},
					"Esc" : function(cm) {
						if (cm.getOption("fullScreen"))
							cm.setOption("fullScreen", false);
					}
				}
			});
	resourceStore.on ('beforeload', function (store, options)
    		{
    			var new_params =
    			{
    				id : oldIID
    			};
    			Ext.apply (resourceStore.proxy.extraParams, new_params);
    		});
	resourceStore.on('load', function(store, options, success) {
		var reader = store.getProxy().getReader();
		operationSystem.setValue(reader.jsonData.IBUSINESS);
		if(reader.jsonData.ICOLLEC_TYPE){
			typeComboBox.setValue(reader.jsonData.ICOLLEC_TYPE);
		}
//		sqlArea.setValue(reader.jsonData.ICOLLEC_CONTENT);
		editor.setOption('value', reader.jsonData.ICOLLEC_CONTENT);
		editor.getDoc().setValue(reader.jsonData.ICOLLEC_CONTENT);
		dbType.setValue(reader.jsonData.IDB_TYPE);
		dbport.setValue(reader.jsonData.IDBPORT);
		ip.setValue(reader.jsonData.IIP);
		username.setValue(reader.jsonData.IUSER);
		password.setValue(reader.jsonData.IPWD);
		sid.setValue(reader.jsonData.IINSTANCE);
	});
	resourceStore.load();
	editor.setSize(sqlPanel.getWidth() - 10, sqlPanel.getHeight() - 165);
	function changeValue() {
        var a = typeComboBox.getValue();
        if(a==1){//1数据库 2文件
        	fileUpload.hide();
        	dbport.show();
        	dbType.show();
        	ip.show();
        	username.show();
        	password.show();
        	sid.show();
        }else{
        	fileUpload.show();
        	dbport.hide();
        	dbType.hide();
        	ip.hide();
        	username.hide();
        	password.hide();
        	sid.hide();
        }
	}
	
	function setMessage(msg){
		Ext.Msg.alert('提示', msg);
	}	
	
	
    function checkIP(ip) {
    	var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
    	if (reg.test(ip)) {
    		return true;
    	} else {
    		return false;
    	}
    }   
	function execute(){//保存+执行
		saveDataFor();
//		Ext.Msg.alert('提示', "执行成功！");
    }
    function back(){
		destroyRubbish();
		contentPanel.getLoader().load({url: 'generalList.do',
		scripts: true});
	}
    function saveData() { 
    	saveDataExec(1);
    }
    function saveDataFor() { 
    	saveDataExec(0);
    }
    function saveDataExec(flags) { 
    	editor.save();
    	// 业务系统
		var operationSystemName = sqlForm.getForm ().findField ("operationSystem").getValue();
		// 采集类型
		var dataType = sqlForm.getForm ().findField ("dataType").getValue();
		// sql文本
//		var sqlArea = sqlForm.getForm ().findField ("sqlArea").getValue();
		var content = document.getElementById('code-sqlArea').value;
		// 数据库类型
		var dbType = dbform.getForm ().findField ("dbType").getValue();
		var dbPort = dbport.getValue();
		// 文件todo
		// ip
		var ip = dbform.getForm ().findField ("ip").getValue();
		// 用户名
		var username = dbform.getForm ().findField ("username").getValue();
		// 密码
		var password = dbform.getForm ().findField ("password").getValue();
		// 数据库实例
		var sid = dbform.getForm ().findField ("sid").getValue();
		var jsonData = "{";
		jsonData = jsonData + "\"IID\":\""+oldIID+"\",";
		if (trim (operationSystemName) == '' || (null == operationSystemName))
		{
			Ext.MessageBox.alert ("提示", "请选择业务系统");
			return false;
		} else {
			jsonData = jsonData + "\"IBUSINESS\":\""+operationSystemName+"\",";
		}
		if (trim (dbType) == '' || (null == dbType))
		{
			Ext.MessageBox.alert ("提示", "请选择数据库类型");
			return false;
		}else {
			jsonData = jsonData + "\"IDB_TYPE\":\""+dbType+"\",";
		}
		if (trim (dataType) == '' || (null == dataType))
		{
			Ext.MessageBox.alert ("提示", "请选择采集类型");
			return false;
		}else {
			jsonData = jsonData + "\"ICOLLEC_TYPE\":\""+dataType+"\",";
		}
//		if (trim (sqlArea) == '' || (null == sqlArea))
//		{
//			Ext.MessageBox.alert ("提示", "请输入SQL");
//			return false;
//		}else {
//			jsonData = jsonData + "\"ICOLLEC_CONTENT\":\""+sqlArea+"\",";
//		}
		if (trim (content) == '' || (null == content))
		{
			Ext.MessageBox.alert ("提示", "请输入SQL");
			return false;
		}else {
			jsonData = jsonData + "\"ICOLLEC_CONTENT\":\""+content+"\",";
		}
		if (trim (dbPort) == '' || (null == dbPort))
		{
			Ext.MessageBox.alert ("提示", "请选择数据库端口");
			return false;
		}else {
		      if(dbPort >65535){
		    	  setMessage('端口号不能大于65535');
		          return;
		      }
			jsonData = jsonData + "\"IDBPORT\":\""+dbPort+"\",";
		}
		if (trim (ip) == '' || (null == ip))
		{
			Ext.MessageBox.alert ("提示", "请输入IP");
			return false;
		}else {
			if(!checkIP(ip)){
				Ext.MessageBox.alert ("提示", "输入IP格式不正确");
				return false;
			}
			jsonData = jsonData + "\"IIP\":\""+ip+"\",";
		}
		
		if (trim (username) == '' || (null == username))
		{
			Ext.MessageBox.alert ("提示", "请填写用户名");
			return false;
		}else {
			if (fucCheckLength(username) > 255) {
	              setMessage('数据库用长度不能超过255字符！');
	              return;
	          }
			jsonData = jsonData + "\"IUSER\":\""+username+"\",";
		}
		if (trim (username) == '' || (null == username))
		{
			Ext.MessageBox.alert ("提示", "请填写用户名");
			return false;
		}else {
			jsonData = jsonData + "\"IUSER\":\""+username+"\",";
		}
		if (trim (password) == '' || (null == password))
		{
			Ext.MessageBox.alert ("提示", "请填写密码");
			return false;
		}else {
			if (fucCheckLength(password) > 255) {
	              setMessage('数据库密码长度不能超过255字符！');
	              return;
	          }
			jsonData = jsonData + "\"IPWD\":\""+password+"\",";
		}
		if (trim (sid) == '' || (null == sid))
		{
			Ext.MessageBox.alert ("提示", "请填写实例");
			return false;
		}else {
			if (fucCheckLength(sid) > 255) {
	              setMessage('数据库SID长度不能超过255字符！');
	              return;
	          }
			jsonData = jsonData + "\"IINSTANCE\":\""+sid+"\"}";
		}
		var rulejsonData = "[";
    	var mm = paramRulesStore.getRange();
        for (var i = 0, len = mm.length; i < len; i++) {
            var paramRuleIn = mm[i].get("paramRuleIn") ? mm[i].get("paramRuleIn").trim() : '';
            var paramRuleOut = mm[i].get("paramRuleOut") ? mm[i].get("paramRuleOut").trim() : '';
            var paramRuleDesc = mm[i].get("paramRuleDesc") ? mm[i].get("paramRuleDesc").trim() : '';
            if ("" == paramRuleOut) {
                setMessage('输出不能为空！');
                return;
            }
            if (fucCheckLength(paramRuleDesc) > 250) {
                setMessage('参数描述不能超过250字符！');
                return;
            }
            var ss = Ext.JSON.encode(mm[i].data);
            if (i == 0) rulejsonData = rulejsonData + ss;
            else rulejsonData = rulejsonData + "," + ss;
        }
        rulejsonData = rulejsonData + "]";
		Ext.Ajax.request (
		{
		    url : "saveDataGathering.do",
		    method : 'POST',
		    params: {
                jsonData: jsonData.replace(/[\r\n]/g,""),
                ruleparams: rulejsonData
            },
		    success : function (response, options)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    if(flags==1){
			    	Ext.Msg.alert('提示', message);
				    if (success==true)
				    {
				    	Ext.Msg.alert('提示', message);
				    }
				    else
				    {
					    Ext.MessageBox.show (
					    {
					        width : 300,
					        title : "提交失败",
					        msg : message,
					        buttonText :
					        {
						        yes : '确定'
					        },
					        buttons : Ext.Msg.YES
					    });
				    }
			    }else{
			    	Ext.Ajax.request ({
					    url : "excuDataGathering.do",
					    method : 'POST',
					    params: {
			                id:oldIID
			            },
					    success : function (response, options)
					    {
						    var success = Ext.decode (response.responseText).success;
						    var message = Ext.decode (response.responseText).message;
						    var tableData = Ext.decode (response.responseText).dataList;
//						    Ext.Msg.alert('提示', message);
						    if (success==true)
						    {
						    	tablePanel.getLoader().load({
						    		url : 'detailSubGeneral.do?tmp='+ new Date().getTime(),
									  params: {
										  tableData:JSON.stringify(tableData)
							            },
						    		script:true
						    		});
						    	Ext.Msg.alert('提示', message);
						    }
						    else
						    {
							    Ext.MessageBox.show (
							    {
							        width : 300,
							        title : "提交失败",
							        msg : message,
							        buttonText :
							        {
								        yes : '确定'
							        },
							        buttons : Ext.Msg.YES
							    });
						    }
					    },
					    failure : function(result, request) {
					    	Ext.Msg.alert('提示', "提交失败");
						}
					});
			    }
		    },
		    failure : function(result, request) {
		    	Ext.Msg.alert('提示', "提交失败");
			}
		});
    }
})




$(function(){
		$.ajax({
		    type:"GET",
		    url:"getDynamicSelect.do",
		    success:function(data){
		        $.each(data, function (index,item) { 
		        	var productName = data[index]; 
		        	$('#select').append("<option value='"+productName+"'>"+productName+"</option>")
		        });
		    }
		});	
	});
	function getData(){
		var selectType = $("#select").val();
		var startTime = $("#startTime").val();
		var endTime = $("#endTime").val();
		$.ajax({
			type :"post",
			url : "getDynamicReportGraphAndList.do",
			data : {
				selectType : selectType,
				startTime : startTime,
				endTime : endTime
			},
			dataType : "json",
			success : function(data) {
				$('#graph').removeAttr('_echarts_instance_');
				$("#graph").html('');
				$("#list").html('');
				var graph=data.graphData;
				var tablelist=data.tableData;
				var seriesList = graph.seriesData;
				var overTimeChart = echarts.init(document.getElementById("graph"));
				//构建图表数据
				//解析series数据
				console.info(seriesList.length);
				var seri=[];
                for(var t = 0; t < seriesList.length; t++){
                    var series = seriesList[t];
                    var item={
                    		name:series.name,
                            type:'line',
                            data:series.data,
                            markPoint : {
                                data : [
                                    {type : 'max', name: '最大值'},
                                    {type : 'min', name: '最小值'}
                                ]
                            },
                            markLine : {
                                data : [
                                    {type : 'average', name: '平均值'}
                                ]
                            }
                    };
                    seri.push(item);
                }
				var option = {
				    tooltip : {
				        trigger: 'axis'
				    },
				    color:["#5168fc","#f57242"],
				    legend: {
				    	//动态数据 注意此处构建的后台数据为list数组
				        data:graph.legendData
				    },
				    toolbox: {
				        show : true,
				        feature : {
				            mark : {show: true},
				            dataView : {show: true, readOnly: false},
				            magicType : {show: true, type: ['line', 'bar']},
				            restore : {show: true},
				            saveAsImage : {show: true}
				        }
				    },
				    calculable : true,
				    xAxis : [
				        {
				            type : 'category',
				            boundaryGap : false,
				            //动态数据
				            data : graph.xAxisData
				        }
				    ],
				    yAxis : [
				        {
				            type : 'value',
				            axisLabel : {
				                formatter: '{value}'
				            }
				        }
				    ],
				  	//动态数据
				    series :  seri
				};
				overTimeChart.setOption(option,true); 
				//构建表格数据
				var tablehtml="";
				tablehtml+="<table cellpadding='0' cellspacing='0' border='0'>";
				tablehtml+="<thead>";
				tablehtml+="<tr>";
				tablehtml+="<th>姓名</th>";
				tablehtml+="<th>年龄</th>";
				tablehtml+="<th>性别</th>";
				tablehtml+="</tr>";
				tablehtml+="</thead>";
				tablehtml+="<tbody>";
				$.each(tablelist, function(index, item) {
					tablehtml+="<tr>";
					tablehtml+="<td>"+item.name+"</td>";
					tablehtml+="<td>"+item.age+"</td>";
					tablehtml+="<td>"+item.sex+"</td>";
					tablehtml+="</tr>";
				});
				tablehtml+="</tbody>";
				$("#list").append(tablehtml);
			}
		});
	}