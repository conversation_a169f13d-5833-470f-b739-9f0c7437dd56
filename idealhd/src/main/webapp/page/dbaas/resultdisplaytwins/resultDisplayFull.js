Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
	Ext.define('scriptService', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		},{
			name : 'uuid',
			type : 'string'
		},{
			name : 'serviceid',
			type : 'string'
		},{
			name : 'servicename',
			type : 'string'
		},{
			name : 'scripttype',
			type : 'string'
		},{
			name : 'issuer',
			type : 'string'
		},{
			name : 'starttime',
			type : 'string'
		}]
	})
	var scriptservice_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		pageSize : 200,
		model : 'scriptService',
		proxy : {
			type : 'ajax',
			url : 'getResultDisplayFull.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var scriptservice_columns = [ {
		text : '序号',
		xtype : 'rownumberer',
		align:'left',
		width : 70
	}, {
		text : 'id',
		dataIndex : 'iid',
		width: 120,
		hidden : true
	}, {
		text : 'uuid',
		dataIndex : 'uuid',
		width: 120,
		hidden : true
	},{
		text : '服务ID',
		dataIndex :'serviceid',
		flex:1
	},{
		text : '服务名称',
		dataIndex :'servicename',
		flex:1
	},{
		text : '脚本类型',
		dataIndex :'scripttype',
		width: 120
	},{
		text : '发起人',
		dataIndex :'issuer',
		width: 160
	},{
		text : '发起时间',
		dataIndex :'starttime',
		width: 160
	},{
		text : '结果',
		width: 120,
		
		renderer: function(value, p, record, rowIndex) {
            var sid = record.get('serviceid');
            	return '<div>' 
            	+ '<a href="toResultDisplayFullDetail.do?sid='+sid+'" target="_Blank">&nbsp;查看结果</a>' 
           	 		                 + '</div>';
        }
	}];
	
	// 分页工具
	var pageBar = Ext.create('Ext.PagingToolbar', {
		store : scriptservice_store,
		dock : 'bottom',
		baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		displayInfo : true,
		border : false,
		emptyMsg : '找不到任何记录'
	});


	var sID = new Ext.form.TextField({
		name : 'serviceID',
		fieldLabel : '服务ID',
		displayField : 'serverID',
		emptyText : '--请输入服务ID--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%'
	});
	var sName = new Ext.form.TextField({
		name : 'serviceName',
		fieldLabel : '服务名称',
		displayField : 'serverName',
		emptyText : '--请输入服务名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%'
	});
	var sUser = new Ext.form.TextField({
		name : 'serviceUser',
		fieldLabel : '发起人',
		displayField : 'serverUser',
		emptyText : '--请输入发起人--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%'
	});
	var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			dock : 'top',
			border : false,
			items : [sID, sName, sUser, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : function() {
					//QueryMessage();
					 scriptservice_store.on ('beforeload', function (store, options)
								{
									var new_params =
									{
										sid : sID.getValue().trim(),
										sname :sName.getValue().trim(),
										suser : sUser.getValue().trim()
									};
									Ext.apply (scriptservice_store.proxy.extraParams, new_params);
								});
					 scriptservice_store.load();
				}
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '重置',
				handler : function() {
					sID.setValue('');
					sName.setValue('');
					sUser.setValue('');
				}
			}]
		} ]
	});
	var dataSourceGrid = Ext.create('Ext.grid.Panel', {
		region : 'center',
		cls:'customize_panel_back',
		store : scriptservice_store,
		padding : panel_margin,
		border : true,
		bbar : pageBar,
		columnLines : true,
		columns : scriptservice_columns,
	});


	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "resultDisplayFull",
		layout : 'border',
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		bodyPadding : grid_margin,
		border : true,
		bodyCls : 'service_platform_bodybg',
		items : [ form, dataSourceGrid ]
	});

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
	});

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
})