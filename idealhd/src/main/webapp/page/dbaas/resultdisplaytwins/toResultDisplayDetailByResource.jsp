<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page isELIgnored="false"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>结果展示</title>
<script type="text/javascript" src="js/layui/layui.js"></script>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="js/jquery.nicescroll.js"></script>

<link rel="stylesheet" href="js/layui/css/layui.css" />
<link rel="stylesheet" href="css/abnormal.css"/>
<script type="text/javascript"	src="<%=request.getContextPath()%>/page/dbaas/report/echarts.min.js"></script>
<script type="text/javascript">
	var SID = '<%=request.getAttribute("sid")%>';
	var resId = '<%=request.getAttribute("resId")%>';
	var startTime= '<%=request.getAttribute("startTime")%>';
	var insId= <%=request.getAttribute("insId")%>;
	var flowId= <%=request.getAttribute("flowId")%>;
	
	var keydata = new Array();
	function getview(){
		$.ajax({
			type : "post",
			url : "getResultDisplayFullDetailByResId.do",
			data : {
				sid : SID,
				resId:resId,
				startTime:startTime,
				ins:insId,
				flowId:flowId
			},
			dataType : "json",
			success : function(data) {
				$("#contains_new").html('');
				//$.each(data, function(i, resource) {
						var table = data.table;
						$("#contains_new").append(table);
				//	});
	
			}
		}); 
	};
	getview();
	
	function getDataByTime(sid){
		var startTime = $("#select_s_date_"   + sid).val();
		var endTime = $("#select_e_date_"   + sid).val();
		//alert(rid+","+sid+"\n"+startTime+"\n"+endTime);
		
	 $.ajax({
			type : "post",
			url : "getResultDisplayFullDetailByTime.do",
			data : {
				sid : sid,
				startTime : startTime,
				endTime : endTime
			},
			dataType : "json",
			success : function(data) {
				$("#table_chart_"  + sid).html('');

				//替换div中的table
				$("#table_chart_"   + sid).append(data.table);
				
				$(".table_chart").niceScroll({
					autohidemode : true,
					cursorcolor : "#c6cbd6",
					cursorborder : "1px solid #c6cbd6"
				});
			}
		}); 
	}

</script>
</head>
<body>
	<!-- <h1>分析结果展示</h1> -->
	<div id="contains_new"></div>
</body>
</html>