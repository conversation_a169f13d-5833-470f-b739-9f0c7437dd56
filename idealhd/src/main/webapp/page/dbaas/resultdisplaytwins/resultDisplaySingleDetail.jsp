<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page isELIgnored="false"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>结果展示</title>
<script type="text/javascript" src="js/layui/layui.js"></script>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="js/jquery.nicescroll.js"></script>
<link rel="stylesheet" href="css/analysisstyle_new.css">
<link rel="stylesheet" href="js/layui/css/layui.css" />
<script type="text/javascript"	src="<%=request.getContextPath()%>/page/dbaas/report/echarts.min.js"></script>
<script type="text/javascript">
	var SID = '<%=request.getAttribute("sid")%>';
	var RID = '<%=request.getAttribute("rid")%>';
	var keydata = new Array();
	$(function() {
 	$.ajax({
			type : "post",
			url : "getResultDisplaySingleDetail.do",
			data : {
				rid : RID,
				sid : SID
			},
			dataType : "json",
			success : function(data) {
				$("#contains_new").html('');
					var table = data.table;
					$("#contains_new").append(table);
			}
		}); 
	});
	function getDataByTime(rid,sid,flag){
		var startTime = $("#select_s_date_"  + rid + "_" + sid).val();
		var endTime = $("#select_e_date_"  + rid + "_" + sid).val();
		//alert(rid+","+sid+"\n"+startTime+"\n"+endTime);
		
	 $.ajax({
			type : "post",
			url : "getResultDisplaySingleDetailByTime.do",
			data : {
				rid : rid,
				sid : sid,
				startTime : startTime,
				endTime : endTime,
				flag : flag
			},
			dataType : "json",
			success : function(data) {
				$("#table_chart_" + rid + "_" + sid).html('');

				//替换div中的table
				$("#table_chart_"  + rid + "_" + sid).append(data.table);
				
				$(".table_chart").niceScroll({
					autohidemode : true,
					cursorcolor : "#c6cbd6",
					cursorborder : "1px solid #c6cbd6"
				});
			}
		}); 
	}
</script>
</head>
<body>
	<h1>结果展示</h1>
	<div id="contains_new"></div>
</body>
</html>