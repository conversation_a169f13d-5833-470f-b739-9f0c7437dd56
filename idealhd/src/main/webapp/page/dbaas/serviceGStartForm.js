var ServiceStartFormWindow;
Ext
		.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			Ext.tip.QuickTipManager.init();
			var mainPanel;
			Ext.define('serviceStartModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'argsIid',
					type : 'long'
				}, {
					name : 'argsOrder',
					type : 'string'
				}, {
					name : 'argsType',
					type : 'string'
				}, {
					name : 'argsDefaultValue',
					type : 'string'
				}, {
					name : 'argsDesc',
					type : 'string'
				}, {
					name : 'serverIid',
					type : 'string'
				}, {
					name : 'IIP',
					type : 'string'
				}, {
					name : 'serviceSystem',
					type : 'string'
				}, {
					name : 'serviceDBType',
					type : 'string'
				}, {
					name : 'serviceDBVersion',
					type : 'string'
				}, {
					name : 'loginName',
					type : 'string'
				}, {
					name : 'fullName',
					type : 'string'
				}, {
					name : 'agentIp',
					type : 'string'
				}, {
					name : 'agentPort',
					type : 'string'
				}, {
					name : 'ostype',
					type : 'string'
				}, {
					name : 'insName',
					type : 'string'
				}, {
					name : 'insUserName',
					type : 'string'
				}, {
					name : 'paramRuleIn',
					type : 'string'
				}, {
					name : 'paramRuleOut',
					type : 'string'
				}, {
					name : 'paramRuleType',
					type : 'int'
				}, {
					name : 'paramRuleLen',
					type : 'int'
				}, {
					name : 'paramRuleDesc',
					type : 'string'
				}, {
					name : 'paramRuleOrder',
					type : 'int'
				} ]
			});
			Ext.define('scriptService', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'string'
				},{
					name : 'flowId',
					type : 'string'
				}, {
					name : 'serviceName',
					type : 'string'
				},{
					name : 'sysName',
					type : 'string'
				}, {
					name : 'bussName',
					type : 'string'
				}, {
					name : 'serviceType',
					type : 'string'
				}, {
					name : 'dbType',
					type : 'string'
				}, {
					name : 'scriptType',
					type : 'string'
				}, {
					name : 'scriptName',
					type : 'string'
				}, {
					name : 'ssuer',
					type : 'string'
				}, {
					name : 'version',
					type : 'string'
				},{
					name : 'servicePara',
					type : 'string'
				}, {
					name : 'platForm',
					type : 'string'
				}, {
					name : 'status',
					type : 'int'
				},{
					name : 'content',
					type : 'string'
				},{
					name : 'serviceTy',
					type : 'String'
				}, {
					name : 'startType',
					type : 'String'
				},{
					name : 'bussId',
					type : 'int'
				}, {
					name : 'bussTypeId',
					type : 'int'
				}, {
					name : 'scriptLevel',
					type : 'int'
				}, {
					name : 'isFlow',
					type : 'string'
				} ]
			});
			var checkUserStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getExecAuditorList.do?scriptLevel=0&dbaasFlag=1',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});


			var checkUserComBox = Ext.create('Ext.form.ComboBox', {
				editable : false,
				fieldLabel : "审核人",
				labelWidth : 60,
				hidden : startForm_iisexam == 0 ? true : false,
				store : checkUserStore,
				queryMode : 'local',
				margin : '5 5 0 3',
				displayField : 'fullName',
				valueField : 'loginName'
			});
			if (startForm_iisexam == 1) {
				checkUserComBox.show();
			} else {
				checkUserComBox.hide();
			}			

			
			var planTime_sm = Ext.create('Go.form.field.DateTime', {
				fieldLabel : '计划时间',
				format : 'Y-m-d H:i:s',
				labelAlign : "left",
				labelWidth : 65,
//				columnWidth : .5,
				minValue: new Date(),
				margin : '5 5 0 3'
			});
			
			 var planTime_MM = Ext.create('Ext.form.field.ComboBox', {
					fieldLabel : '周期类型',
					labelAlign : "left",
					editable : false,
					name : 'MM',
					margin : '5 5 0 3',
					matchFieldWidth:false,// 此处要有
					labelWidth :65,
		//			columnWidth : .35,
					store: {
						 	fields: ['value'],
						    data : [
						        {"value":"立即执行"},
						    	{"value":"按计划执行一次"},
						        {"value":"间隔x日"},
						        {"value":"间隔x小时"},
						        {"value":"间隔x分钟"}
						    ]
					 },
					 displayField:'value',
					 value:"间隔x日",
					 listeners:{
						 select : function(nf, newv, oldv) {
							},
						 change : function(nf, newv, oldv) {
							 if(newv=='间隔x日'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.show();
								 planTime_HH.show();
								 planTime_mi.show();
							 }else if(newv=='间隔x小时'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.show();
								 planTime_mi.show();
							 }else if(newv=='间隔x分钟'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.hide();
								 planTime_mi.show();
							 }else if(newv=='按计划执行一次'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.hide();
								 planTime_mi.hide();
							 }
							 else if( newv=='立即执行'){
								 planTime_DD.setValue('');
								 planTime_HH.setValue('');
								 planTime_mi.setValue('');
								 planTime_DD.hide();
								 planTime_HH.hide();
								 planTime_mi.hide();
								 planTime_sm.hide();
							 }
						 }
					 }
				 });

			
				var planTime_DD = Ext.create('Ext.form.NumberField', {
					fieldLabel : '天数',
					labelAlign : "left",
					editable : true,
					name : 'DD',
					margin : '5 5 0 3',
					labelWidth : 37,
//					columnWidth : .20,
					listeners : {
						select : function(nf, newv, oldv) {
						},
						change : function(nf, newv, oldv) {
							if (null == newv || newv == '' || trim(newv) == '') {
								planTime_DD.setValue("")
							} else {
								if (/^[0-9]([0-9])*$/.test(newv)) {
									if (newv > 31) {
										Ext.Msg.alert("提示", "天数值需在1~31之间!");
										planTime_DD.setValue(oldv)
									}
									return true;
								} else {
									Ext.Msg.alert("提示", "天数窗口只能正整数");
									planTime_DD.setValue(oldv)
									return false;
								}
							}
						}
					}
				});
				var planTime_HH = Ext.create('Ext.form.NumberField', {
					fieldLabel : '小时',
					labelAlign : "left",
					editable : true,
					name : 'HH',
					margin : '5 5 0 3',
					labelWidth : 37,
//					columnWidth : .20,
					listeners : {
						select : function(nf, newv, oldv) {
						},
						change : function(nf, newv, oldv) {
							if (null == newv || newv == '' || trim(newv) == '') {
								planTime_HH.setValue("")
							} else {
								if (/^[0-9]([0-9])*$/.test(newv)) {
									if (newv > 23) {
										Ext.Msg.alert("提示", "小时值需在1~23之间!");
										planTime_HH.setValue(oldv)
									}
									return true;
								} else {
									Ext.Msg.alert("提示", "小时窗口只能正整数");
									planTime_HH.setValue(oldv)
									return false;
								}
							}
						}
					}
				});
				var planTime_mi = Ext.create('Ext.form.NumberField', {
					fieldLabel : '分钟',
					labelAlign : "left",
					editable : true,
					name : 'mi',
					margin : '5 5 0 3',
					labelWidth : 37,
//					columnWidth : .20,
					listeners : {
						select : function(nf, newv, oldv) {
						},
						change : function(nf, newv, oldv) {
							if (null == newv || newv == '' || trim(newv) == '') {
								planTime_mi.setValue("")
							} else {
								if (/^[0-9]([0-9])*$/.test(newv)) {
									if (newv > 59) {
										Ext.Msg.alert("提示", "分钟值需在1~59之间!");
										planTime_mi.setValue(oldv)
									}
									return true;
								} else {
									Ext.Msg.alert("提示", "分钟窗口只能正整数");
									planTime_mi.setValue(oldv)
									return false;
								}
							}
						}
					}
				});
					
										
				var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
					name : 'pubdesc',
					fieldLabel : '详细说明',
					emptyText : '',
					labelWidth : 65,
					margin : '5 5 0 3',
					height : 100,
//						columnWidth : .98,
					autoScroll : true
				});
			var serviceStartFormPanel = new Ext.form.FormPanel({
				frame : true,
				buttonAlign : 'center',
				layout : "column", // 从左往右的布局
				border : false,
				items : [
						{
							labelAlign : "left",
							fieldLabel : "任务名称",// 文本框标题
							margin : '5 5 0 3',
							labelWidth : 65,
							hidden:true,
							name : 'taskName',
							xtype : 'textfield'
						},
						{
							// columnWidth : 20,
							labelAlign : "left",
							fieldLabel : "服务名称",// 文本框标题
							margin : '5 5 0 3',
							xtype : 'textfield',
							labelWidth : 65,
							readOnly : true,// 不可编辑,只读
							value : startForm_serviceName
						},
						{
							labelAlign : "left",
							fieldLabel : "脚本类型",// 文本框标题
							labelWidth : 65,
							margin : '5 5 0 3',
							xtype : 'textfield',
							readOnly : true,// 不可编辑,只读
							value : startForm_scriptType
						}, 
						
						planTime_MM,
						{
							layout : 'column',
							border : false,
							margin : '5 5 0 3',
							items : [ planTime_DD, planTime_HH, planTime_mi ]
						},
						planTime_sm,
						checkUserComBox ,pubDesc_sm]
			})

			var leftPanel = Ext.create('Ext.panel.Panel', {
				width : 260,
				height : contentPanel.getHeight() - 146,
				margin : '5 5 0 0',
				border : true,
				columnLines : true,
				region : 'west',
				items : [ serviceStartFormPanel ]
			});
			var serviceStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getServiceByServiceStart.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			serviceStore.on('beforeload', function(store, options) {
				var new_params = {
					idbType : startGForm_dbType
				};
				Ext.apply(serviceStore.proxy.extraParams, new_params);
			});

			var serviceColumns = [ {
				text : '主键',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : 'IP',
				dataIndex : 'agentIp',
				flex : 1,
				width : 180
			}, {
				text : '端口号',
				dataIndex : 'agentPort',
				width : 150
			}, {
				text : '数据库类型',
				dataIndex : 'ostype',
				width : 140
			}, {
				text : '数据库实例名',
				dataIndex : 'insName',
				width : 140
			}, {
				text : '数据库用户名',
				dataIndex : 'insUserName',
				width : 140
			} ];

			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			var scriptservice_columns = [ {
				text : '序号',
				xtype : 'rownumberer',
				align:'left',
				width : 70
			}, {
				text : '主键',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : 'bind',
				dataIndex : 'flowId',
				hidden : true
			}, {
				text : '服务名称',
				dataIndex : 'serviceName',
				width : 250,
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			
				
			}, {
				text : '服务类型',
				dataIndex : 'serviceType',
				width : 100,
				flex:1,
				renderer : function(value, p, record) {
					var backValue = "";
					if (value == 0) {
						backValue = "应用";
					} else if (value == 1) {
						backValue = "采集";
					}	
					return backValue;
				}
			}, {
				text : '数据库类型',
				dataIndex : 'dbType',
			//	hidden : true,
				width : 100,
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '脚本类型',
				dataIndex : 'scriptType',
				width : 80,
				flex:1,
				hidden : true,
				renderer : function(value, p, record) {
					var backValue = "";
					if (value == "sh") {
						backValue = "shell";
					} else if (value == "perl") {
						backValue = "perl";
					} else if (value == "py") {
						backValue = "python";
					} else if (value == "bat") {
						backValue = "bat";
					} else if (value == "sql") {
						backValue = "sql";
					}
					if (record.get('isFlow') == '1') {
						backValue = "组合";
					}
					return backValue;
				}
			}, {
				text : '发布人',
				dataIndex : 'ssuer',
				width : 150,
				flex:1,
			}, {
				text : '版本号',
				dataIndex : 'version',
				width : 80,
				hidden : true
			}
			];
			
			var scriptservice_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'scriptService',
				proxy : {
					type : 'ajax',
					url : 'getScriptServiceDatasForGroup.do?posiflag=0',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			scriptservice_store.on('beforeload', function(store, options) {
				var new_params = {
						groupId : groupId
				};
				Ext.apply(scriptservice_store.proxy.extraParams, new_params);
			});
			
			var scriptservice_grid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				title : "服务信息",
				margin : '0 0 0 0',
				width : contentPanel.getWidth(),
				store : scriptservice_store,
				columnLines : true,
				border:false,
//				columnLines : true,				
				columns : scriptservice_columns
//				selModel : selModelServer
			});
	
		

			var serviceGrid = Ext.create('Ext.grid.Panel', {
				region : 'south',
				height : 200,
				margin : '0 0 0 0',
				width : contentPanel.getWidth(),
				title : "服务器",
				store : serviceStore,
				border : false,
				columnLines : true,
				columns : serviceColumns,
				selModel : selModel
			});
	


			var rightPanel = Ext.create('Ext.panel.Panel',{
				region : 'center',
				padding : '5 0 0 0',
				layout : {
					type : 'border'
				},
				autoScroll : true,
				border : false,
				height : contentPanel.getHeight(),
				items : [  serviceGrid,scriptservice_grid],
				buttonAlign : 'center',
				buttons : [ {
				text : '发起',
				handler : function() {
					Ext.Msg.confirm("请确认","是否确认进行发起操作?",
							function(button, text) {
								if (button == "yes") {
									Ext.MessageBox.wait("任务发起中...","进度条");
									var taskNames = serviceStartFormPanel.getForm().findField('taskName').getValue();
//									if (taskNames == "") {
//										Ext.Msg.alert("提示","请输入任务名称");
//										return;
//									}
									var serviceRecord = serviceGrid.getSelectionModel().getSelection();							
									var agents = new Array();
									Ext.Array.each(serviceRecord,function(r) {
										agents.push(r.get('iid')+ "_1");
									});
									if (agents.length == 0) {
										Ext.Msg.alert("提示","请选择服务器");
										return;
									}
									
									var planTime = planTime_sm.getRawValue();
									var planTime_MM_1 = planTime_MM.getValue();
									var planTime_DD_1 = planTime_DD.getValue();
									var planTime_HH_1 = planTime_HH.getValue();
									var planTime_mi_1 = planTime_mi.getValue();
									var scriptLevel = 100;		
									var publishDesc = pubDesc_sm.getValue();
									if (!publishDesc) {
										Ext.Msg.alert('提示', "没有填写详细说明！");
										return;
									}
									if (!planTime && planTime_MM_1!='立即执行') {
										Ext.Msg.alert('提示',"没有填写计划时间！");
										return;
									}
									if(planTime_MM_1=='间隔x日'){
										if (planTime_DD_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
											Ext.Msg.alert('提示',"天数必须填写！");
											return;
										}
									}else if(planTime_MM_1=='间隔x小时'){
										if (planTime_HH_1==''||planTime_HH_1==null||planTime_HH_1=='null') {
											Ext.Msg.alert('提示',"小时必须填写！");
											return;
										}
									}else if(planTime_MM_1=='间隔x分钟'){
										if (planTime_mi_1==''||planTime_mi_1==null||planTime_mi_1=='null') {
											Ext.Msg.alert('提示',"分钟必须填写！");
											return;
										}
									}else if(planTime_MM_1==''||planTime_MM_1==null||planTime_MM_1=='null'){
										Ext.Msg.alert('提示',"请选择周期类型！");
										return;
									}
																											
									
									var execRule = "";
									var isDelay = false;  	
									var checkUser = "";
									if (startForm_iisexam == 1&& ("" == checkUserComBox.getValue() || null == checkUserComBox.getValue())) {
										Ext.Msg.alert("提示","请选择审核人");
										return;
									} else {
										checkUser = checkUserComBox.getValue();
									}																						
								 	var servicejsonData = "[";
									var service = scriptservice_store.getRange();		
									for (var i = 0, len = service.length; i < len; i++) {		
										var iid = service[i].get("iid") ? service[i].get("iid") : '';		
										var ss = Ext.JSON.encode(service[i].data);
										console.log(service[i].data);
										if (i == 0)
											servicejsonData = servicejsonData + ss;
										else
											servicejsonData = servicejsonData + "," + ss;
									}									
									servicejsonData = servicejsonData + "]";  	
									var pubflag=0;
									if(startGForm_isAutoSub==2){
										pubflag=1;
									}		
									Ext.Ajax.request({
												url : 'serviceStartDbaasForRroject.do',
												method : 'POST',
												params : {
													auditor : startForm_iisexam == 0 ? ""
															: checkUser, // 执行人
													isDelay : isDelay, // 是否定时延时
													agents : agents, // 服务器id
													execRule : execRule, // 执行规则
													iisexam : startForm_iisexam, // 是否需要审核
													taskNames : taskNames,
													servicejsonData:servicejsonData,
													groupId:groupId,
													flag : pubflag,
													planTime:planTime,
													planTimeType : planTime_MM_1,
													planTimeDD : planTime_DD_1,
													planTimeHH : planTime_HH_1,
													planTimeMM : planTime_mi_1,
													publishDesc:publishDesc,
													sId:groupId,
													serviceType:startForm_serviceType,
													
												},
												success : function(response,opts) {
													var success = Ext.decode(response.responseText).success;
													var message = Ext.decode(response.responseText).message;
													if (!success) {
														Ext.MessageBox.alert("提示", message);
													} else {
														if(startForm_iisexam == 0){
															Ext.MessageBox.alert("提示", "发起成功");
														}else{
															Ext.MessageBox.alert("提示", "请求已经发送到审核人");
														}			
														
													}												
												},
												failure : function(result,request) {
													secureFilterRs(result,"操作失败！");
												}
											});
								}
							});
						}
								} ]
							});
			
			
			mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "serviceStartForm",
				layout : {
					type : 'border'
				},
				border : false,
				height : contentPanel.getHeight() - modelHeigth,
				items : [ leftPanel, rightPanel ]
			});



			contentPanel.on('resize', function() {

				mainPanel.setHeight(contentPanel.getHeight());
				mainPanel.setWidth(contentPanel.getWidth());

			});

		});
