var ServiceStartFormWindow;
Ext
		.onReady(function() {
			// 清理主面板的各种监听时间
			destroyRubbish();
			Ext.tip.QuickTipManager.init();
			var mainPanel;
			var attachmentIds = [];
			var editPanel = Ext
					.create(
							'Ext.panel.Panel',
							{
								region : 'center',
								minHeight : 150,
								border : true,
								collapsible : false,
								autoScroll : true,
								title : "编辑框",
								height : contentPanel.getHeight(),
								html : '<textarea id="code-edit-view" value style="height:100%;"></textarea>'
							});
			Ext.define('serviceStartModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'long'
				}, {
					name : 'argsIid',
					type : 'long'
				}, {
					name : 'argsOrder',
					type : 'string'
				}, {
					name : 'argsType',
					type : 'string'
				}, {
					name : 'argsDefaultValue',
					type : 'string'
				}, {
					name : 'argsDesc',
					type : 'string'
				}, {
					name : 'serverIid',
					type : 'string'
				}, {
					name : 'IIP',
					type : 'string'
				}, {
					name : 'serviceSystem',
					type : 'string'
				}, {
					name : 'serviceDBType',
					type : 'string'
				}, {
					name : 'serviceDBVersion',
					type : 'string'
				}, {
					name : 'loginName',
					type : 'string'
				}, {
					name : 'fullName',
					type : 'string'
				}, {
					name : 'agentIp',
					type : 'string'
				}, {
					name : 'agentPort',
					type : 'string'
				}, {
					name : 'ostype',
					type : 'string'
				}, {
					name : 'insName',
					type : 'string'
				}, {
					name : 'insUserName',
					type : 'string'
				}, {
					name : 'paramRuleIn',
					type : 'string'
				}, {
					name : 'paramRuleOut',
					type : 'string'
				}, {
					name : 'paramRuleType',
					type : 'int'
				}, {
					name : 'paramRuleLen',
					type : 'int'
				}, {
					name : 'paramRuleDesc',
					type : 'string'
				}, {
					name : 'paramRuleOrder',
					type : 'int'
				} ]
			});

			var checkUserStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getExecAuditorList.do?scriptLevel=0',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			Ext.define('attachmentModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iid',
					type : 'int'
				}, {
					name : 'attachmentName',
					type : 'string'
				}, {
					name : 'attachmentSize',
					type : 'string'
				}, {
					name : 'attachmentUploadTime',
					type : 'string'
				} ]
			});

			var attachmentStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 10,
				model : 'attachmentModel',
				proxy : {
					type : 'ajax',
					url : 'getAllScriptAttachment.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});

			attachmentStore.on('beforeload', function(store, options) {
				var new_params = {
					scriptId : startForm_uuid,
					ids : attachmentIds
				};

				Ext.apply(attachmentStore.proxy.extraParams, new_params);
			});

			var attachmentColumns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						width : 40
					},
					{
						text : '主键',
						dataIndex : 'iid',
						width : 40,
						hidden : true
					},
					{
						text : '附件名称',
						dataIndex : 'attachmentName',
						flex : 1,
						renderer : function(value, metaData, record, rowIdx,
								colIdx, store) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode(value) + '"';
							return value;
						}
					} /*
						 * , { text: '附件大小', dataIndex: 'attachmentSize', width:
						 * 200 }, { text: '上传时间', dataIndex:
						 * 'attachmentUploadTime', flex: 1 }
						 */];

			var attachmentGrid = Ext.create('Ext.grid.Panel', {
				title : "附件",
				width : '100%',
				region : 'south',
				height : '50%',
				// margin: 10,
				store : attachmentStore,
				border : true,
				columnLines : true,
				columns : attachmentColumns
			});

			var checkUserComBox = Ext.create('Ext.form.ComboBox', {
				editable : false,
				fieldLabel : "审核人",
				labelWidth : 60,
				hidden : startForm_iisexam == 0 ? true : false,
				store : checkUserStore,
				queryMode : 'local',
				margin : '5 5 0 3',
				displayField : 'fullName',
				valueField : 'loginName'
			});
			if (startForm_iisexam == 1) {
				checkUserComBox.show();
			} else {
				checkUserComBox.hide();
			}
			var planTime_Y = Ext.create('Ext.form.NumberField', {
				fieldLabel : '年',
				editable : true,
				name : 'y',
				padding : '5 5 0 0',
				labelWidth : 60,
				listeners : {
					select : function(nf, newv, oldv) {
					},
					change : function(nf, newv, oldv) {
						if (null == newv || newv == '' || trim(newv) == '') {
							planTime_Y.setValue("")
						} else {
							if (/^[0-9]([0-9])*$/.test(newv)) {
								return true;
							} else {
								Ext.Msg.alert("提示", "天数窗口只能正整数");
								planTime_Y.setValue(oldv)
								return false;
							}
						}
					}
				}
			});
			var planTime_MON = Ext.create('Ext.form.NumberField', {
				fieldLabel : '月',
				editable : true,
				name : 'm',
				padding : '5 5 0 0',
				labelWidth : 60,
				listeners : {
					select : function(nf, newv, oldv) {
					},
					change : function(nf, newv, oldv) {
						if (null == newv || newv == '' || trim(newv) == '') {
							planTime_MON.setValue("")
						} else {
							if (/^[0-9]([0-9])*$/.test(newv)) {
								if (newv > 12) {
									Ext.Msg.alert("提示", "月份数值需在1~12之间!");
									planTime_MON.setValue(oldv)
								}
								return true;
							} else {
								Ext.Msg.alert("提示", "月份窗口只能正整数");
								planTime_MON.setValue(oldv)
								return false;
							}
						}
					}
				}
			});
			var planTime_DD = Ext.create('Ext.form.NumberField', {
				fieldLabel : '天数',
				editable : true,
				name : 'd',
				padding : '5 5 0 0',
				labelWidth : 60,
				listeners : {
					select : function(nf, newv, oldv) {
					},
					change : function(nf, newv, oldv) {
						if (null == newv || newv == '' || trim(newv) == '') {
							planTime_DD.setValue("")
						} else {
							if (/^[0-9]([0-9])*$/.test(newv)) {
								if (newv > 31) {
									Ext.Msg.alert("提示", "天数值需在1~31之间!");
									planTime_DD.setValue(oldv)
								}
								return true;
							} else {
								Ext.Msg.alert("提示", "天数窗口只能正整数");
								planTime_DD.setValue(oldv)
								return false;
							}
						}
					}
				}
			});
			var planTime_HH = Ext.create('Ext.form.NumberField', {
				fieldLabel : '小时',
				editable : true,
				name : 'h',
				padding : '5 5 0 0',
				labelWidth : 60,
				listeners : {
					select : function(nf, newv, oldv) {
					},
					change : function(nf, newv, oldv) {
						if (null == newv || newv == '' || trim(newv) == '') {
							planTime_HH.setValue("")
						} else {
							if (/^[0-9]([0-9])*$/.test(newv)) {
								if (newv > 23) {
									Ext.Msg.alert("提示", "小时值需在1~23之间!");
									planTime_HH.setValue(oldv)
								}
								return true;
							} else {
								Ext.Msg.alert("提示", "小时窗口只能正整数");
								planTime_HH.setValue(oldv)
								return false;
							}
						}
					}
				}
			});
			var planTime_mi = Ext.create('Ext.form.NumberField', {
				fieldLabel : '分钟',
				editable : true,
				name : 'mm',
				padding : '5 5 0 0',
				labelWidth : 60,
				listeners : {
					select : function(nf, newv, oldv) {
					},
					change : function(nf, newv, oldv) {
						if (null == newv || newv == '' || trim(newv) == '') {
							planTime_mi.setValue("")
						} else {
							if (/^[0-9]([0-9])*$/.test(newv)) {
								if (newv > 59) {
									Ext.Msg.alert("提示", "分钟值需在1~59之间!");
									planTime_mi.setValue(oldv)
								}
								return true;
							} else {
								Ext.Msg.alert("提示", "分钟窗口只能正整数");
								planTime_mi.setValue(oldv)
								return false;
							}
						}
					}
				}
			});

			var serviceStartFormPanel = new Ext.form.FormPanel({
				frame : true,
				buttonAlign : 'center',
				layout : "column", // 从左往右的布局
				border : false,
				items : [
						{
							labelAlign : "left",
							fieldLabel : "任务名称",// 文本框标题
							margin : '5 5 0 3',
							labelWidth : 65,
							name : 'taskName',
							xtype : 'textfield'
						},
						{
							// columnWidth : 20,
							labelAlign : "left",
							fieldLabel : "服务名称",// 文本框标题
							margin : '5 5 0 3',
							xtype : 'textfield',
							labelWidth : 65,
							readOnly : true,// 不可编辑,只读
							value : startForm_serviceName
						},
						{
							labelAlign : "left",
							fieldLabel : "脚本类型",// 文本框标题
							labelWidth : 65,
							margin : '5 5 0 3',
							xtype : 'textfield',
							readOnly : true,// 不可编辑,只读
							value : startForm_scriptType
						},
						{
							xtype : 'radiogroup',
							name : 'execRuleName',
							margin : '5 5 0 3',
							labelWidth : 65,
							fieldLabel : '执行规则',// 显示在前面的
							layout : 'column',
							items : [ {
								boxLabel : '手动执行',// 显示在后面的
								inputValue : '1',
								name : 'execRule'// 必须一样的
							}, {
								boxLabel : '定时执行一次',// 显示在后面的
								inputValue : '2',
								name : 'execRule'
							}, {
								boxLabel : '定期执行',// 显示在后面的
								name : 'execRule',// 必须一样的
								inputValue : '3'
							} ],
							listeners : {
								"change" : function(g, newValue, oldValue) {
									var v = serviceStartFormPanel.getForm()
											.findField('execRule')
											.getGroupValue();
									if (v == 1 || v == '1') {
										planTime_Y.hide();
										planTime_MON.hide();
										planTime_DD.hide();
										planTime_HH.hide();
										planTime_mi.hide();
									} else if (v == 2 || v == '2') {
										planTime_Y.show();
										planTime_MON.show();
										planTime_DD.show();
										planTime_HH.show();
										planTime_mi.show();
									} else {
										planTime_Y.hide();
										planTime_MON.hide();
										planTime_DD.show();
										planTime_HH.show();
										planTime_mi.show();
									}
								}
							},
						}, planTime_Y, planTime_MON, planTime_DD, planTime_HH,
						planTime_mi, {
							xtype : 'numberfield',// 类型
							allowBlank : false,// 是否允许为空
							labelWidth : 60,
							maxValue : 59,// 最大值
							minValue : 1, // 最小值
							name : 's',
							hidden : true,
							fieldLabel : '秒'// 显示在前面的
						}, checkUserComBox ]
			})

			var leftPanel = Ext.create('Ext.panel.Panel', {
				width : 260,
				height : contentPanel.getHeight() - 146,
				margin : '5 5 0 0',
				border : true,
				columnLines : true,
				region : 'west',
				items : [ serviceStartFormPanel ]
			});
			var argsStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getArgsByServiceStart.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			argsStore.on('beforeload', function(store, options) {
				var new_params = {
						scriptuuid : startForm_uuid
				};
				Ext.apply(argsStore.proxy.extraParams, new_params);
			});
			var serviceStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getServiceByServiceStart.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			serviceStore.on('beforeload', function(store, options) {
				var new_params = {
					idbType : startForm_idbtype,
					createUserName : startForm_createusername
				};
				Ext.apply(serviceStore.proxy.extraParams, new_params);
			});
			var serviceAgentStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getAllAgentList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			serviceAgentStore.on('beforeload', function(store, options) {
				var new_params = {
					flag : 1
				};
				Ext.apply(serviceAgentStore.proxy.extraParams, new_params);
			});
			var argsColumns = [
					{
						text : '主键',
						dataIndex : 'argsIid',
						hidden : true
					},
					{
						text : '顺序',
						dataIndex : 'argsOrder',
						width : 120,
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					},
					{
						text : '默认值',
						dataIndex : 'argsDefaultValue',
						width : 120,
						editor : {
							allowBlank : false
						},
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					},
					{
						text : '类型',
						dataIndex : 'argsType',
						width : 120,
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					},
					{
						text : '描述',
						dataIndex : 'argsDesc',
						width : 120,
						hidden : false,
						renderer : function(value, metaData, record) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('argsOrder')
											+ "<br>默认值："
											+ record.get('argsDefaultValue')
											+ "<br>类型："
											+ record.get('argsType')
											+ "<br>描述："
											+ record.get('argsDesc')) + '"';
							return value;
						}
					} ];
			var serviceAgentColumns = [ {
				text : '主键',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : 'IP',
				dataIndex : 'agentIp',
				flex : 1,
				width : 180
			}, {
				text : '端口号',
				dataIndex : 'agentPort',
				width : 150
			}, {
				text : '数据库类型',
				dataIndex : 'ostype',
				width : 140
			} ];
			var serviceColumns = [ {
				text : '主键',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : 'IP',
				dataIndex : 'agentIp',
				flex : 1,
				width : 180
			}, {
				text : '端口号',
				dataIndex : 'agentPort',
				width : 150
			}, {
				text : '数据库类型',
				dataIndex : 'ostype',
				width : 140
			}, {
				text : '数据库实例名',
				dataIndex : 'insName',
				width : 140
			}, {
				text : '数据库用户名',
				dataIndex : 'insUserName',
				width : 140
			} ];
			var p4Columns = [
					{
						text : '主键',
						dataIndex : 'iid',
						width : 40,
						hidden : true
					},
					{
						text : '顺序',
						dataIndex : 'paramRuleOrder',
						width : 60,
						editor : {
							allowBlank : false,
							xtype : 'numberfield',
							maxValue : 30,
							minValue : 1
						},
						renderer : function(value, metaData, record, rowIdx,
								colIdx, store) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('paramRuleOrder')
											+ "<br>输出列名称："
											+ record.get('paramRuleOut')
											+ "<br>别名："
											+ record.get('paramRuleDesc'))
									+ '"';
							return value;
						}
					},
					{
						text : '分隔符',
						dataIndex : 'paramRuleIn',
						width : 85,
						editor : {},
						hidden : true

					},
					{
						text : '输出列名称',
						dataIndex : 'paramRuleOut',
						width : 130,
						editor : {
							allowBlank : false
						},
						renderer : function(value, metaData, record, rowIdx,
								colIdx, store) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('paramRuleOrder')
											+ "<br>输出列名称："
											+ record.get('paramRuleOut')
											+ "<br>别名："
											+ record.get('paramRuleDesc'))
									+ '"';
							return value;
						}
					},
					{
						text : '类型',
						dataIndex : 'paramRuleType',
						width : 85,
						renderer : function(value, metaData, record, rowIdx,
								colIdx, store) {

							if (value == 0) {
								value = "VARCHAR";
							} else if (value == 1) {
								value = "INTEGER";
							} else if (value == 2) {
								value = "NUMBER";
							} else if (value == 3) {
								value = "DATE";
							} else if (value == 4) {
								value = "CLOB";
							} else {
								value = "VARCHAR"
							}
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode(" 类型：" + value)
									+ '"';
							return value;
						}
					},
					{
						text : '长度',
						dataIndex : 'paramRuleLen',
						width : 85,
						value : 50,
						editor : {
							xtype : 'numberfield',
							maxValue : 4000,
							minValue : 1
						}
					},
					{
						text : '别名',
						dataIndex : 'paramRuleDesc',
						width : 120,
						editor : {
							allowBlank : true
						},
						renderer : function(value, metaData, record, rowIdx,
								colIdx, store) {
							metaData.tdAttr = 'data-qtip="'
									+ Ext.String.htmlEncode("顺序："
											+ record.get('paramRuleOrder')
											+ "<br>输出列名称："
											+ record.get('paramRuleOut')
											+ "<br>别名："
											+ record.get('paramRuleDesc'))
									+ '"';
							return value;
						}
					} ];
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});

			var selModelM = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});

			var ruleparamStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'serviceStartModel',
				proxy : {
					type : 'ajax',
					url : 'getAllScriptRuleOutParams.do?iflag=0&scriptId='
							+ startForm_uuid,
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var p4Grid = Ext.create('Ext.grid.Panel', {
				region : 'south',
				height : '50%',
				store : ruleparamStore,
				columnLines : true,
				columns : p4Columns
			});
			var argsGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				height : 150,
				margin : '0 0 0 0',
				title : "参数",
				store : argsStore,
				plugins : [ cellEditing ],
				width : contentPanel.getWidth() / 2,
				border : false,
				columnLines : true,
				columns : argsColumns
			});

			var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
				region : 'east',
				collapsible : false,
				border : false,
				width : 350,
				minWidth : 120,
				minHeight : 140,
				layout : {
					type : 'border'
				},
				items : [ argsGrid, p4Grid, attachmentGrid ]
			});

			var serviceGrid = Ext.create('Ext.grid.Panel', {
				region : 'south',
				height : 200,
				margin : '0 0 0 0',
				width : contentPanel.getWidth(),
				title : "服务器",
				store : serviceStore,
				border : false,
				columnLines : true,
				columns : serviceColumns,
				selModel : selModel
			});
			var serviceAgentGrid = Ext.create('Ext.grid.Panel', {
				region : 'south',
				height : 200,
				margin : '0 0 0 0',
				width : contentPanel.getWidth(),
				title : "服务器",
				store : serviceAgentStore,
				border : false,
				columnLines : true,
				columns : serviceAgentColumns,
				selModel : selModelM
			});

			var gridPanel = Ext.create('Ext.panel.Panel', {
				region : 'center',
				layout : {
					type : 'border'
				},
				border : true,
				height : 200,
				collapsible : false,
				autoScroll : true,
				items : [ paramsAndFuncDescPanel ]
			});

			if (startForm_scriptType == 'sql') {
				p4Grid.show();
				attachmentGrid.hide();
				serviceGrid.show()
				serviceAgentGrid.hide();
			} else {
				p4Grid.hide();
				attachmentGrid.show();
				serviceGrid.hide()
				serviceAgentGrid.show();
			}
			var rightPanel = Ext.create('Ext.panel.Panel',{
				region : 'center',
				padding : '5 0 0 0',
				layout : {
					type : 'border'
				},
				autoScroll : true,
				border : false,
				height : contentPanel.getHeight(),
				items : [ editPanel, serviceGrid,
						serviceAgentGrid,
						paramsAndFuncDescPanel ],
				buttonAlign : 'center',
				buttons : [ {
				text : '发起',
				handler : function() {
					Ext.Msg.confirm("请确认","是否确认进行发起操作?",
							function(button, text) {
								if (button == "yes") {
									Ext.MessageBox.wait("任务发起中...","进度条");
									var taskNames = serviceStartFormPanel.getForm().findField('taskName').getValue();
									if (taskNames == "") {
										Ext.Msg.alert("提示","请输入任务名称");
										return;
									}
									var serviceRecord;
									if (startForm_scriptType == 'sql') {
										serviceRecord = serviceGrid
												.getSelectionModel()
												.getSelection();
									} else {
										serviceRecord = serviceAgentGrid
												.getSelectionModel()
												.getSelection();
									}
									var agents = new Array();
									Ext.Array.each(serviceRecord,function(r) {
														agents.push(r.get('iid')+ "_1");
													});
									if (agents.length == 0) {
										Ext.Msg.alert("提示","请选择服务器");
										return;
									}    	
									var exec = [];
									var execRule = "";
									var isDelay = false;
									var execTime = "";
									var checkUser = "";
									if (startForm_iisexam == 1
											&& ("" == checkUserComBox.getValue() || null == checkUserComBox.getValue())) {
										Ext.Msg.alert("提示","请选择审核人");
										return;
									} else {
										checkUser = checkUserComBox.getValue();
									}
									var args = new Array();
									var record_args = argsStore.data;
									for (var i = 0; i < record_args.length; i++) {
										args.push(record_args.items[i].data);
									}

									var m = argsStore.getRange();
									var jsonData = "[";
									for (var i = 0, len = m.length; i < len; i++) {
										var n = 0;
										var paramType = m[i].get("argsType") ? m[i].get("argsType").trim(): '';
										var paramDefaultValue = m[i].get("argsDefaultValue") ? m[i].get("argsDefaultValue").trim(): '';
										if ((paramType == 'OUT-int'
												|| paramType == 'IN-int' || paramType == 'int')
												&& paramDefaultValue) {
											if (!checkIsInteger(paramDefaultValue)) {
												setMessage('参数类型为int，但参数默认值不是int类型！');
												return;
											}
										}
										if ((paramType == 'OUT-float'
												|| paramType == 'IN-float' || paramType == 'float')
												&& paramDefaultValue) {
											if (!checkIsDouble(paramDefaultValue)) {
												setMessage('参数类型为float，但参数默认值不是float类型！');
												return;
											}
										}
										var ss = Ext.JSON.encode(m[i].data);
										if (i == 0)
											jsonData = jsonData+ ss;
										else
											jsonData = jsonData+ ","+ ss;
									}
									jsonData = jsonData+ "]";
									Ext.Ajax.request({
												url : 'groupServiceStart.do',
												method : 'POST',
												params : {
													loginName : startForm_createusername,
													serviceIid : startForm_iid,
													auditor : startForm_iisexam == 0 ? ""
															: checkUser, // 执行人
													isDelay : isDelay, // 是否定时延时
													execTime : execTime, // 执行时间
													agents : agents, // 服务器id
													execRule : execRule, // 执行规则
													iisexam : startForm_iisexam, // 是否需要审核
													args : args,
													argsData : jsonData,
													taskNames : taskNames,
													serviceType : startForm_serviceType,
													scriptType : startForm_scriptType
												},
												success : function(
														response,
														opts) {
													var success = Ext
															.decode(response.responseText).success;
													var message = Ext
															.decode(response.responseText).message;
														Ext.MessageBox.alert("提示",message);
												},
												failure : function(result,request) {
													secureFilterRs(result,"操作失败！");
												}
											});
								}
							});
						}
								} ]
							});
			mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "serviceStartForm",
				layout : {
					type : 'border'
				},
				border : false,
				height : contentPanel.getHeight() - modelHeigth,
				items : [ leftPanel, rightPanel ]
			});
			// if(startForm_serviceType=='0'){
			serviceStartFormPanel.getForm().findField('execRuleName').hide();
			planTime_Y.hide();
			planTime_MON.hide();
			planTime_DD.hide();
			planTime_HH.hide();
			planTime_mi.hide();
			// serviceGrid.hide();
			// }else{
			// serviceStartFormPanel.getForm().findField('execRuleName').show();
			// planTime_Y.show();
			// planTime_MON.show();
			// planTime_DD.show();
			// planTime_HH.show();
			// planTime_mi.show();
			// serviceGrid.show();
			// }
			var editor = CodeMirror.fromTextArea(document
					.getElementById('code-edit-view'), {
				mode : 'sql',
				lineNumbers : true,
				matchBrackets : true,
				readOnly : true
			});

			contentPanel.on('resize', function() {
				editor.getDoc().clearHistory();
				mainPanel.setHeight(contentPanel.getHeight());
				mainPanel.setWidth(contentPanel.getWidth());
				// editor.setSize(editPanel.getWidth(),
				// editPanel.getHeight());
			});

			var editorStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'editScriptModel',
				proxy : {
					type : 'ajax',
					url : 'scriptService/queryOneService.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			editorStore.on('beforeload', function(store, options) {
				var queryparams = {
					iid : startForm_iid
				};
				Ext.apply(editorStore.proxy.extraParams, queryparams);
			});
			editorStore.on('load', function(store, options, success) {
				var reader = store.getProxy().getReader();
				var scriptT = reader.jsonData.scriptType;
				if (scriptT == 'sh') {
					editor.setOption("mode", 'shell');
				} else if (scriptT == 'bat') {
					editor.setOption("mode", 'bat');
				} else if (scriptT == 'py') {
					editor.setOption("mode", 'python');
				} else if (scriptT == 'sql') {
					editor.setOption("mode", 'text/x-plsql');
				} else if (scriptT == 'perl') {
					editor.setOption("mode", 'text/x-perl');
				}
				editor.setOption('value', reader.jsonData.content);
			});
		});
