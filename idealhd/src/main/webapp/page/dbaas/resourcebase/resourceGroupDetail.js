Ext.onReady(function() {
	var globalSelectRecord;
	Ext.tip.QuickTipManager.init();
    var dataSourceStore;
    var dataSourceGrid;
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'IDBUSER',
            type: 'string'
        },
        {
            name: 'IDBPWD',
            type: 'string'
        },
        {
            name: 'ICOPYNUM',
            type: 'string'
        },
        {
            name: 'IAPPLYUSER',
            type: 'string'
        },
        {
            name: 'ISID',
            type: 'string'
        },
        {
            name: 'ITYPE',
            type: 'long'
        },
        {
            name: 'IENV',
            type: 'long'
        },
        {
            name: 'IDBI<PERSON>',
            type: 'string'
        }
        ,
        {
            name: 'IDBVERSION',
            type: 'string'
        }
        ]
    });
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 16,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceGroupRelationList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    dataSourceStore.addListener('load',function(){
    	var group_Id = -1;
    	if(groupId!=null&&groupId!=''){   	
        	group_Id = groupId;
    	}
    	Ext.Ajax.request({
            url: 'relationResourceGroup.do',
            params: {
                id: group_Id
            },
            method: 'POST',
            success: function(response, opts) {
            	console.log("console.log(selects);");
            	console.log(response);
            	var selects = Ext.decode(response.responseText);
            	
            	console.log(selects);
            	var records=[];//存放选中记录
        	    for(var i=0;i<dataSourceStore.getCount();i++){
        	      var record = dataSourceStore.getAt(i);
        	      for(var j=0;j<selects.length;j++){
        	    	  if(selects[j]==record.data.IID){
        	    		  records.push(record);
        	    	  }
        	      }
        	    }
        	    selModel.select(records);//选中记录
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
	});
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
        		isSelect: selectField.getValue(),
            	ip: ipField.getValue(),
            	dataBaseName: dataBaseNameField.getValue(),
        		groupId:groupId
        };
        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '副本号',
        dataIndex: 'ICOPYNUM',
        hidden: true,
        width: 80,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		},
    },
    {
        text: 'DBID',
        dataIndex: 'IDBID',
        width: 100,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '版本号',
        dataIndex: 'IDBVERSION',
        width: 100
    },
    {
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        width: 140,
        flex:1,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '服务器IP',
        dataIndex: 'IIP',
        width: 100,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库端口',
        dataIndex: 'IDBPORT',
        width: 100
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        hidden: true,
        width: 120,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        width: 100
    },
    {
        text: '数据源用户',
        dataIndex: 'IDBUSER',
        width: 100,
        hidden: true
    },
    {
    	header: '数据源密码',
        dataIndex: 'IDBPWD',
        width: 90,
		renderer:retNotView,
		hidden: true
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        flex:1,
        width: 90
    },
    {
        header: '状态',
        dataIndex: 'IFLAG',
        width: 65,
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('IFLAG');
            if(IFLAG=='1'){
            	return "有效";
            }else{
            	return "无效";
            }
        },
        hidden: true
    },
    {
        header: '环境',
        dataIndex: 'IENV',
        hidden: true,
        width: 65,
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('IENV');
            if(IFLAG=='0'){
            	return "测试";
            }else if(IFLAG=='1'){
            	return "生产";
            }else if(IFLAG=='2'){
            	return "研发";
            }
        }
    },
    {
        header: '申请人',
        dataIndex: 'IAPPLYUSER',
        width: 90,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		},
		hidden: true
    },
    {
        header: '所属类型',
        dataIndex: 'ITYPE',
        hidden: true,
        width: 90,
        renderer: function(value, p, record, rowIndex) {
            var ITYPE = record.get('ITYPE');
            if(ITYPE=='1'){
            	return "人工";
            }else{
            	return "自动";
            }
        }
    }];
    // 分页工具
  var  pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        displayInfo: true,
	    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var selectStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"0", "name":"全部"},
			{"id":"1", "name":"已绑定"}
		]
	});
    var selectField = Ext.create('Ext.form.field.ComboBox', {
		name : 'selectField',
		labelWidth : 65,
		queryMode : 'local',
		fieldLabel : '绑定业务',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		value : flag==1?'1':'0',//
		store : selectStore,
		hidden:flag==1?true:false,
		width : '15%',
        labelAlign : 'left'
	});
    var ipField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务器IP',
        labelWidth: 60,
        labelAlign: 'left',
        name: 'ipFieldParam',
        width: '20%'
    });
    var dataBaseNameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务名',
        labelWidth: 50,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '18%'
    });
    var form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [selectField,ipField,dataBaseNameField,
			    {
		        xtype: 'button',
		        cls: 'Common_Btn',
		        text: '查询',
		        handler: function() {
		        	QueryMessage();
		        }
		    },'->',
		    {
		        text: '保存',
		        cls: 'Common_Btn',
		        handler: saveDatabase,
		        hidden:flag==1?true:false
		    }]
		} ]
	});
    
    dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
        selModel: selModel,
        plugins: [cellEditing],
        padding : panel_margin,
        border: true,
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	}
        }
    });
    function retNotView(value){
    	var coun ="";
    	if (value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resourceGroupDetail",
        layout: 'border',
		width : contentPanel.getWidth()*0.7,
		height : contentPanel.getHeight()*0.8-50,
        border: true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
//    contentPanel.on('resize', function() {
//    	mainPanel.setHeight (contentPanel.getWidth()*0.8-50);
//		mainPanel.setWidth ( '100%');
//    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function saveDatabase() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
        	Ext.Msg.confirm("请确认", "没有选择记录，点击保存后将解绑该资源组对应的所有数据?",
                    function(button, text) {
                        if (button == "yes") {
                            var ids = [];
                            Ext.Array.each(data,function(record) {
                                var iid = record.get('IID');
                                if (iid) {
                                	ids.push(iid);
                                }
                            });
                              Ext.Ajax.request({
                                url: 'saveResourceGroupRelation.do',
                                params: {
                                	groupId:groupId,
                                	resourceIds: ids.join(',')
                                },
                                method: 'POST',
                                success: function(response, opts) {
                                    var success = Ext.decode(response.responseText).success;
                                    if (success) {
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        parent.resourceConfig2.close ();
                                    } else {
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                    }
                                },
                                failure: function(result, request) {
                                    secureFilterRs(result, "操作失败！");
                                }
                            });
                        }
                    });
        } else {
            Ext.Msg.confirm("请确认", "是否要关联数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        if (iid) {
                        	ids.push(iid);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'saveResourceGroupRelation.do',
                        params: {
                        	groupId:groupId,
                        	resourceIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            if (success) {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                parent.resourceConfig2.close ();
                            } else {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                   }
                }
            });
        }
    }
});
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}