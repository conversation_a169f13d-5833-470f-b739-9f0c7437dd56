Ext.onReady(function() {
  Ext.require([ 'Ext.data.*', 'Ext.grid.*', 'Ext.selection.CellModel']);
  Ext.tip.QuickTipManager.init();
  
  Ext.define('dataSourceModel', {
      extend: 'Ext.data.Model',
      fields: [
	      {name: 'IID',type: 'long'},
	      {name: 'INAME',type: 'string'},
	      {name: 'IIP',type: 'string'},
	      {name: 'IDBPORT',type: 'long'},
	      {name: 'IRSTYPE',type: 'string'},
	      {name: 'IFLAG',type: 'string'},
	      {name: 'IBUSINESS',type: 'string'},
	      {name: 'IDBUSER',type: 'string'},
	      {name: 'IDBP<PERSON>',type: 'string'},
	      {name: 'ICOPYNUM',type: 'string'},
	      {name: 'IAPPLYUSER',type: 'string'},
	      {name: 'ISID',type: 'string'},
	      {name: 'ITYPE',type: 'long'},
	      {name: 'IENV',type: 'long'},
	      {name: 'IDBID',type: 'string'},
	      {name: 'ISTATUS',type: 'string'},
	      {name: 'IDBVERS<PERSON>',type: 'string'}
      ]
  });
	// ----------------------右侧------------------------------//
  var choosedStore = Ext.create('Ext.data.Store', {
      autoLoad: true,
      autoDestroy: true,
      model: 'dataSourceModel',
      proxy: {
          type: 'ajax',
          url: 'relationResourceGroup.do',
          reader: {
              type: 'json',
              root: 'dataList'
          }
      }
  });
  choosedStore.addListener('load',function(){
	    var records=[];//存放选中记录
	    for(var i=0;i<choosedStore.getCount();i++){
	      var record = choosedStore.getAt(i);
	      if(record.data.checked){
	        records.push(record);
	      }
	    }
	    chosedSelModel.select(records);//选中记录
	  });
  choosedStore.on('beforeload', function(store, options) {
      var new_params = {
      		id:groupId,
      		ip: ipFieldc.getValue(),
      		businessSys:businessSysdc.getValue(),
      		dataBaseName: dataBaseNameFieldc.getValue(),
      };
      Ext.apply(choosedStore.proxy.extraParams, new_params);
  });
  
	var scriptServiceReleaseLColumns = [{
		text : '序号',
		xtype : 'rownumberer',
		align:'left',
		width : 60
	},{
		xtype : 'checkcolumn',
		header : '选中',
		hidden : true,
		dataIndex : 'checked',
		width : 95,
		stopSelection : false,
		listeners : {
			checkchange : function(column, recordIndex, checked) {
				if (checked)
					ChosedServList.down('#view').setDisabled(
							false);
				else {
					var serverStore = ChosedServList.getStore();
					var storeCnt = serverStore.getCount();
					var isChecked = null;
					var cnt = 0;
					for (var i = 0; i < storeCnt; i++) {
						isChecked = serverStore.getAt(i).get(
								'checked');
						if (isChecked == true) {
							cnt++;
						}
					}
				}
			}
		}
	}, 
	 {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '副本号',
        dataIndex: 'ICOPYNUM',
        hidden: true,
        width: 80,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		},
    },
    {
        text: 'DBID',
        dataIndex: 'IDBID',
        hidden: true,
        width: 100,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '版本号',
        dataIndex: 'IDBVERSION',
        width: 90
    },
    {
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        minWidth: 140,
        flex:1,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '服务器IP',
        dataIndex: 'IIP',
        width: 100,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库端口',
        dataIndex: 'IDBPORT',
        hidden: true,
        width: 100
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        hidden: true,
        width: 120,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        width: 80
    },
    {
        text: '数据源用户',
        dataIndex: 'IDBUSER',
        width: 100,
        hidden: true
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        flex:1,
        minWidth: 90
    },
    {
        header: '状态',
        dataIndex: 'ISTATUS',
        width: 60,
        renderer: function(value, p, record, rowIndex) {
            if(value=='0'){
            	return "<span class='Complete_Green State_Color'>有效</span>";
            }else if(value=='1'){
            	return "<span class='Abnormal_yellow State_Color'>查询用户失效</span>";
            }else if(value=='2'){
            	return "<span class='Abnormal_yellow State_Color'>操作用户失效</span>";
            }else if(value=='3'){
            	return "<span class='Abnormal_yellow State_Color'>全部失效</span>";
            }
        }
    },
    {
        header: '环境',
        dataIndex: 'IENV',
        hidden: true,
        width: 60,
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('IENV');
            if(IFLAG=='0'){
            	return "测试";
            }else if(IFLAG=='1'){
            	return "生产";
            }else if(IFLAG=='2'){
            	return "研发";
            }
        }
    }];
	
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
	
	var chosedSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	
	 var businessSysdc = Ext.create("Ext.form.field.Text", {
	        fieldLabel: '业务系统',
	        labelWidth: 60,
	        labelAlign: 'left',
	        name: 'businessSysParam',
	        width: '25%',
	        listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	QueryMessage1();
	                }
	            }
	        }
     });
	
	 var ipFieldc = Ext.create("Ext.form.field.Text", {
	        fieldLabel: '服务器IP',
	        labelWidth: 60,
	        labelAlign: 'left',
	        name: 'ipFieldParam',
	        width: '25%',
	        listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	QueryMessage1();
	                }
	            }
	        }
	    });
	    var dataBaseNameFieldc = Ext.create("Ext.form.field.Text", {
	        fieldLabel: '服务名',
	        labelWidth: 50,
	        labelAlign: 'left',
	        name: 'dataBaseNameParam',
	        width: '25%',
	        listeners: {
	            specialkey: function(field, e){
	                if (e.getKey() == e.ENTER) {
	                	QueryMessage1();
	                }
	            }
	        }
	    });
		  var ChosedServList = Ext.create('Ext.grid.Panel', {
			    region : 'center',
			    title : '已选资源',
			    cls:'customize_panel_back',
			    multiSelect: true,
			    split : true,
			    viewConfig : {
					plugins : {
						ptype : 'gridviewdragdrop',
						dragGroup : 'firstGridDDGroup',
						dropGroup : 'secondGridDDGroup'
					},
					listeners : {
						drop : function(node, data, dropRec, dropPosition) {
							onSaveListener(data.records,"add");
						}
					}
				},
			    columnLines : true,
			    emptyText: '没有资源信息',
			    store : choosedStore,
			    columns : scriptServiceReleaseLColumns,
			    plugins: [cellEditing],
			    listeners : {
					itemclick : function(dv, record, item, index, e) {
						choosedStore.each(function(rec) {
							rec.set('itemClicked', false);
						});
						record.set('itemClicked', true);
						console.log(record.get("IID"));
					}
				},
			    dockedItems: [{
			      xtype: 'toolbar',
			      items: [businessSysdc,ipFieldc,dataBaseNameFieldc,{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : function() {
							QueryMessage1();
						}
		      		},{
		                xtype: 'button',
		                cls: 'Common_Btn',
		                text: '清空',
		                handler: function() {
		                    clearQueryWhere1();
		                }
		            }]
			    }]
			  });
	//--------------------右侧结束---------------------//			       
	
	//-----------------左侧开始-------------------//
		  var store = Ext.create('Ext.data.Store', {
				autoLoad: true,
			    autoDestroy: true,
			    pageSize: 50,
			    model: 'dataSourceModel',
			    proxy: {
			      type: 'ajax',
			      url: 'resourceGroupRelationList.do',
			      reader: {
			        type: 'json',
			        root: 'dataList',
			        totalProperty: 'total'
			      }
			    }
			  });
			  
			  store.on('beforeload', function (store, options) {
			      var new_params = { 
			    		  groupId : groupId,
			    		  isSelect: 0,
			    		  ip: ipField.getValue(),
			              dataBaseName: dataBaseNameField.getValue(),
			              businessSys:businessSys.getValue(),
			    		  };
			      Ext.apply(store.proxy.extraParams, new_params);
			  });
			  store.addListener('load',function(){
			    var records=[];//存放选中记录
			    for(var i=0;i<store.getCount();i++){
			      var record = store.getAt(i);
			      if(record.data.checked){
			        records.push(record);
			      }
			    }
			    selModel.select(records);//选中记录
			  });
	
	
	
	var scriptServiceReleaseRColumns = [{
		text : '序号',
		xtype : 'rownumberer',
		align:'left',
		width : 60
	},{
		xtype : 'checkcolumn',
		header : '选中',
		hidden : true,
		dataIndex : 'checked',
		width : 95,
		stopSelection : false,
		listeners : {
			checkchange : function(column, recordIndex, checked) {
				if (checked)
					unChosedServList.down('#view').setDisabled(
							false);
				else {
					var serverStore = unChosedServList.getStore();
					var storeCnt = serverStore.getCount();
					var isChecked = null;
					var cnt = 0;
					for (var i = 0; i < storeCnt; i++) {
						isChecked = serverStore.getAt(i).get(
						'checked');
						if (isChecked == true) {
							cnt++;
						}
					}
				}
			}
		}
	}, 
	{
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '副本号',
        dataIndex: 'ICOPYNUM',
        hidden: true,
        width: 80,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		},
    },
    {
        text: 'DBID',
        dataIndex: 'IDBID',
        hidden: true,
        width: 100,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '版本号',
        dataIndex: 'IDBVERSION',
        width: 90
    },
    {
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        minWidth: 140,
        flex:1,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '服务器IP',
        dataIndex: 'IIP',
        width: 100,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库端口',
        dataIndex: 'IDBPORT',
        width: 100,
        hidden: true
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        hidden: true,
        width: 120,renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        width: 80
    },
    {
        text: '数据源用户',
        dataIndex: 'IDBUSER',
        width: 100,
        hidden: true
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        flex:1,
        minWidth: 90
    },
    {
        header: '状态',
        dataIndex: 'ISTATUS',
        width: 60,
        renderer: function(value, p, record, rowIndex) {
            if(value=='0'){
            	return "<span class='Complete_Green State_Color'>有效</span>";
            }else if(value=='1'){
            	return "<span class='Abnormal_yellow State_Color'>查询用户失效</span>";
            }else if(value=='2'){
            	return "<span class='Abnormal_yellow State_Color'>操作用户失效</span>";
            }else if(value=='3'){
            	return "<span class='Abnormal_yellow State_Color'>全部失效</span>";
            }
        }
    },
    {
        header: '环境',
        dataIndex: 'IENV',
        hidden: true,
        width: 60,
        renderer: function(value, p, record, rowIndex) {
            var IFLAG = record.get('IENV');
            if(IFLAG=='0'){
            	return "测试";
            }else if(IFLAG=='1'){
            	return "生产";
            }else if(IFLAG=='2'){
            	return "研发";
            }
        }
    }];
  
  function retNotView(value){
  	var coun ="";
  	if (value.trim().length>0){
  		for (var i=0;i<value.length;i++){
  			coun=coun+"*";
  		}
  	}
  	if(value.trim()==""){
  		coun="";
  	}
  	return coun ;
  }
  
	var pageBarRight = Ext.create('Ext.PagingToolbar', {
    	store: store, 
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        displayInfo: true
    });  
	
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(sm, selections) {
			}
		}
	});
	var businessSys = Ext.create("Ext.form.field.Text", {
        fieldLabel: '业务系统',
        labelWidth: 60,
        labelAlign: 'left',
        width: '25%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	QueryMessage();
                }
            }
        }
 });
    var ipField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务器IP',
        labelWidth: 60,
        labelAlign: 'left',
        name: 'ipFieldParam',
        width: '25%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	QueryMessage();
                }
            }
        }
    });
    var dataBaseNameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务名',
        labelWidth: 50,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '25%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	QueryMessage();
                }
            }
        }
    });
var unChosedServList = Ext.create('Ext.ux.ideal.grid.Panel', {
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
// var unChosedServList = Ext.create('Ext.grid.Panel', {
    region : 'east',
    title : '待选资源',
    width : "50%",
//    bbar: pageBarRight,
    multiSelect: true,
    cls:'customize_panel_back ',
    split : true,
	viewConfig : {
		plugins : {
			ptype : 'gridviewdragdrop',
			dragGroup : 'secondGridDDGroup',
			dropGroup : 'firstGridDDGroup'
		},
		listeners : {
			drop : function(node, data, dropRec, dropPosition) {
						onSaveListener(data.records,"delete");
			}
		}
	},
    columnLines : true,
    emptyText: '没有资源信息',
    store : store,
    columns : scriptServiceReleaseRColumns,		
    listeners : {
		itemclick : function(dv, record, item, index, e) {
			store.each(function(rec) {
				rec.set('itemClicked', false);
			});
			record.set('itemClicked', true);
		}
	},
    dockedItems: [{
	      xtype: 'toolbar',
	      items: [businessSys,ipField,dataBaseNameField,{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function() {
						QueryMessage();
					}
	      		},{
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            }]
	    }]
  });
  
  function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		store.load(/*{
			params : {
				start : 0,
				limit : 16
			}
		}*/);
  }
  
  function QueryMessage1() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		choosedStore.load();
}
  function clearQueryWhere() {
	  ipField.setValue('');
	  dataBaseNameField.setValue('');
	  businessSys.setValue('');
  }
  function clearQueryWhere1() {
	  ipFieldc.setValue('');
	  dataBaseNameFieldc.setValue('');
	  businessSysdc.setValue('');
  }
 
	
  var mainPage_S = Ext.create('Ext.panel.Panel', {
    width : '100%',
    height : '100%',
    layout : 'border',
    header : false,
    bodyPadding : grid_margin,
    border : true,
    bodyCls:'service_platform_bodybg',
    items : [unChosedServList,ChosedServList],   
    renderTo : "resourceGroupDetail"
  });
  
  /** 窗口尺寸调节* */
//  contentPanel.on('resize', function() {
//	  mainPage_S.setHeight (contentPanel.getHeight()*0.8);
//	  mainPage_S.setWidth (contentPanel.getWidth()*0.8);
//	  if(resourceConfig2)
//		{
//		  resourceConfig2.setHeight(contentPanel.getHeight()*0.8);
//		  resourceConfig2.setWidth(contentPanel.getWidth()*0.8);
//		}
//  });
  // 当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader().on("beforeload",
  function(obj, options, eOpts) {
      Ext.destroy(mainPage_S);
      if (Ext.isIE) {
          CollectGarbage();
      }
  });
  
//  function queryWhere(){
//		store.load({
//		       params: { start: 0, limit: 50,groupId:groupId}
//		});
//		choosedStore.load({
//		       params: { start: 0, limit: 50,groupId:groupId}
//		});
//	}
  
  //数组功能扩展
  Array.prototype.each = function(fn){  
      fn = fn || Function.K;  
       var a = [];  
       var args = Array.prototype.slice.call(arguments, 1);  
       for(var i = 0; i < this.length; i++){  
           var res = fn.apply(this,[this[i],i].concat(args));  
           if(res != null) a.push(res);  
       }  
       return a;  
  }; 
  //数组是否包含指定元素
  Array.prototype.contains = function(suArr){
      for(var i = 0; i < this.length; i ++){  
          if(this[i] == suArr){
              return true;
          } 
       } 
       return false;
  }
  //不重复元素构成的数组
  Array.prototype.uniquelize = function(){  
       var ra = new Array();  
       for(var i = 0; i < this.length; i ++){  
          if(!ra.contains(this[i])){  
              ra.push(this[i]);  
          }  
       }  
       return ra;  
  };
  //两个数组并集
  Array.union = function(a, b){  
       return a.concat(b).uniquelize();  
  };
  
  function onSaveListener(records,type){
	  onSaveLeftScreen(records,type);
  }
  /**
   * @desc 单击保存按钮后，对“左屏”中的数据进行保存。
   * 	   （1）左屏保存时，对服务器信息进行保存，
   * 	   （2）对左屏中所有的记录都进行保存。所以每条记录的checed属性必须全部为为true。
   * */
  function onSaveLeftScreen(records,type){
	  var ids="";
	  for(var i=0;i<records.length;i++){
		  if(i==0){
			  ids=records[i].data.IID;
		  }else{
			  ids=ids+","+records[i].data.IID;
		  }
	  }
	  
    Ext.Ajax.request({
      url : 'saveResourceGroupRelation.do',  
      method : 'POST',
      params : {
    	groupId:groupId,
    	ids: ids,
    	type:type
      },
      success : function(response, request) {
        var success = Ext.decode(response.responseText).success;
        if (success) {
            store.reload();
            choosedStore.reload();
          } else {
            Ext.Msg.alert('提示', '操作失败！');
          }
      },
      failure : function(result, request) {
        Ext.Msg.alert('提示', '操作失败');
      }
    });
  }
  
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
});
