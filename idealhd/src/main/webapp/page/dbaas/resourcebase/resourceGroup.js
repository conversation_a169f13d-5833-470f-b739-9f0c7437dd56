var isextendWindow2;
Ext.onReady(function() {
	var globalSelectRecord;
	Ext.tip.QuickTipManager.init();
    var resGroupStore;
    var resGroupGrid;
    // 清理主面板的各种监听时间
   //destroyRubbish();
    Ext.define('resGroupModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IGNAME',
            type: 'string'
        },
        {
            name: 'IGDESC',
            type: 'string'
        },
        {
            name: 'ICREATETIME',
            type: 'string'
        },
        {
            name: 'IUPDATETIME',
            type: 'string'
        }
        ]
    });
   
    resGroupStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 100,
        model: 'resGroupModel',
        proxy: {
            type: 'ajax',
            url: 'resGroupList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    resGroupStore.on('beforeload', function(store, options) {
        var new_params = {
            groupName: nameField.getValue()
        };

        Ext.apply(resGroupStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '资源组名称',
        dataIndex: 'IGNAME',
        width: 120,
        flex:1,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
//    { 
//    	header: '角色', 
//    	dataIndex: 'IGROLE' ,	
//        renderer:getRoleinfo,
//        readOnly :true
//	},
	{
        text: '描述',
        dataIndex: 'IGDESC',
        width: 120,
        editor: {
            allowBlank: true
        },flex:1,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '创建时间',
        dataIndex: 'ICREATETIME',
        width: 160,
        editor:false,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '修改时间',
        dataIndex: 'IUPDATETIME',
        width: 160,
        editor:false,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '操作',
        width: 100,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('IID');
            return '<div>' + '<a href="javascript:void(0)" onclick="showDetailRes('+iid+')">' + '&nbsp;资源配置' + '</a>' + '</div>';
        }
    }];
    // 分页工具
   var pageBar = Ext.create('Ext.PagingToolbar', {
        store: resGroupStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });
   
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var nameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '资源组名称',
        labelWidth: 80,
        labelAlign: 'left',
        name: 'groupName',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    pageBar.moveFirst();
                }
            }
        }
    });
	var form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
			baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
				baseCls:'customize_gray_back',
			dock : 'top',
			items : [
	            nameField,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            },'->',
	            {
	                text: '增加',
	                cls: 'Common_Btn',
	                handler: add
	            },
	            {
	                text: '保存',
	                cls: 'Common_Btn',
	                handler: saveDatabase
	            }, '-', {
	                itemId: 'delete',
	                text: '删除',
	                cls: 'Common_Btn',
	                disabled: true,
	                handler: deleteResGroup
	            }]
		} ]
	});
    resGroupGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: resGroupStore,
        selModel: selModel,
        plugins: [cellEditing],
		padding : panel_margin,
			cls:'customize_panel_back',
        border: true,
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	}
        }
    });
    function getRoleinfo(value, p, record) {
		return "<a href=\"#\" valign=\"middle\" onclick=\"displayRole_local('"+record.get("IID")+"');\"><span class='abc'>查看角色</span></a>";
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    
    resGroupGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resource_group",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,resGroupGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function clearQueryWhere() {
    	nameField.setValue('');
    }
    function add() {
        var store = resGroupGrid.getStore();
        var p = {
        		IID: '',
        		IGNAME: '',
        		IGDESC: '',
        		ICREATETIME: '',
        		IUPDATETIME: ''
        };
        store.insert(0, p);
        resGroupGrid.getView().refresh();
    }
    function saveDatabase() {
        var m = resGroupStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
        	var IGNAME = m[i].get("IGNAME").trim();
            if ("" == IGNAME || null == IGNAME) {
                setMessage('资源组名称不能为空！');
                return;
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveResourceGroup.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    resGroupStore.modified = [];
                    resGroupStore.reload();
                    Ext.Msg.alert('提示', message);
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }
    
    function deleteResGroup() {
        var data = resGroupGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 resGroupStore.remove(record);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteResourceGroup.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            resGroupStore.reload();
                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);   
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    	resGroupGrid.getView().refresh();
                    }
                }
            });
        }
    }

});
function showDetailRes(IID) {
	if(!IID){
		Ext.Msg.show({
		     title:'提示',
		     msg: '请保存资源组后，再进行资源配置！',
		     buttons: Ext.Msg.OK,
		     icon: Ext.Msg.INFO
		});	
		return false;
	}
	var resourceConfig2 = Ext.create('Ext.window.Window', {
	    title: '资源配置',
	    width : contentPanel.getWidth()*.9,
		height : contentPanel.getHeight()*0.8,
	    layout: 'fit',
	    constrain: true, 		//闲置窗口不超出浏览器
	    constrainHeader:true, 	//标题不能超出浏览器边界
	    modal: true,			//设置模态窗口
	    plain:true, 			//窗口设置透明背景
	    draggable: false,
	    resizable: false,
	    loader: {
			url: 'groupConfig.do',
			params : {
				IID : IID,
				flag:0
			},
			autoLoad: true,
			scripts: true
		}
	//,
	    //autoScroll:true //显示滚动条
	});
	resourceConfig2.show();
}
function displayRole_local(recordiid) {
	if(recordiid==""||recordiid==null||recordiid=="undefined"){
		Ext.Msg.show({
		     title:'提示',
		     msg: '请保存资源组后，再设置角色！',
		     buttons: Ext.Msg.OK,
		     icon: Ext.Msg.INFO
		});	
	}else{
		isextendWindow2=Ext.create ('Ext.window.Window',
				{
				    title : '角色信息',
				    modal : true,
				    closeAction : 'destroy',
				    constrain : true,
				    autoScroll : true,
				    width : 365,
				    height : 385,
				    minWidth : 350,
				    draggable : false,// 禁止拖动
				    resizable : false,// 禁止缩放
				    layout : 'fit',
				    loader :
				    {
				        url : 'getRoleListForGroupInit.do',
				        params :
				        {
				        	groupId : recordiid
				        },
				        autoLoad : true,
				        scripts : true
				    }
				});
		isextendWindow2.show ();
	}
}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}