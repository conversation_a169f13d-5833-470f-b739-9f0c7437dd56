/*******************************************************************************
 * 用户角色修改操作
 ******************************************************************************/
Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	/** *********************Store********************* */
	var selModelp = Ext.create ('Ext.selection.CheckboxModel',
	{
		checkOnly : true
	});
	var rolestore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    fields : [
	            'roleName', 'iroleId', 'ischeck'
	    ],
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getRoleListForGroup.do?groupId=' + groupId,
	        reader :
	        {
	            type : 'json',
	            root : 'rolelist'
	        }
	    }
	});
	rolestore.addListener ('load', function ()
	{
		var records = [];// 存放选中记录
		for (var i = 0; i < rolestore.getCount (); i++)
		{
			var record = rolestore.getAt (i);
			if (record.data.ischeck != 0)
			{
				records.push (record);
			}
		}
		selModelp.select (records);// 选中记录
	});
	
	var rolePanel = Ext.create ('Ext.grid.Panel',
	{
	    height : 340,
	    width : '100%',
	    border : true,
	    store : rolestore,
	    columns : [
	            {
	                text : 'ischeck',
	                dataIndex : 'ischeck',
	                hidden : true
	            },
	            {
	                text : 'iroleId',
	                dataIndex : 'iroleId',
	                hidden : true
	            },
	            {
	                text : '角色',
	                dataIndex : 'roleName',
	                flex : 1
	            }
	    ],
	    forceFit : true,
	    selModel : selModelp,
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        dock : 'top',
		        items : [
		                {
		                    xtype : 'button',
		                    cls : 'Common_Btn',
		                    text : '保存',
		                    handler : saveRole_local
		                },
		                {
		                    xtype : 'button',
		                    cls : 'Common_Btn',
		                    text : '关闭',
		                    handler : function ()
		                    {
			                    parent.isextendWindow2.close ();
		                    }
		                }
		        ]
		    }
	    ]
	});
	
	/** 主panel* */
	var mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "MainDiv",
	    width : '100%',
	    height : '100%',
	    border : false,
	    bodyPadding : 5,
	    items : [
		    rolePanel
	    ]
	});
	
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	/** *********************方法********************* */
	// 保存记录
	function saveRole_local ()
	{
		var checkedRecords = rolePanel.getSelectionModel ().getSelection ();
		var roleidlist = new Array ();
		Ext.each (checkedRecords, function (record)
		{
			roleidlist.push (record.data.iroleId);
		});
		Ext.Ajax.request (
		{
		    url : 'saveRolesForGroup.do',
		    params :
		    {
		        jsonData : Ext.encode (roleidlist),
		        groupId : groupId
		    },
		    success : function (response)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    if (success)
			    {
				    Ext.Msg.show (
				    {
				        title : '提示',
				        msg : '保存成功！',
				        buttons : Ext.Msg.OK,
				        icon : Ext.Msg.INFO
				    });
				    parent.isextendWindow2.close ();
			    }
			    else
			    {
				    Ext.Msg.show (
				    {
				        title : '提示',
				        msg : message,
				        buttons : Ext.Msg.OK,
				        icon : Ext.Msg.INFO
				    });
			    }
			    rolestore.reload ();
		    },
		    failure : function ()
		    {
			    Ext.Msg.show (
			    {
			        title : '提示',
			        msg : '保存失败！',
			        buttons : Ext.Msg.OK,
			        icon : Ext.Msg.WARNING
			    });
		    }
		});
		
	}
});
