Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	Ext.define('resourceModel', {
		extend : 'Ext.data.Model',
		fields : [ {
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
            name: 'IVERSION',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IPORT',
            type: 'string'
        },
        {
            name: 'IFLAG',
            type: 'string'
        },
        {
            name: 'ICREATETIME',
            type: 'long'
        },
        {
            name: 'IUPDATETIME',
            type: 'long'
        }
        ,
        {
            name: 'ICPU',
            type: 'string'
        }
        ,
        {
            name: 'ICPUUSE',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY_REMAIN',
            type: 'string'
        }
        ,
        {
            name: 'IDISK',
            type: 'string'
        }
        ,
        {
            name: 'IDISK_REMAIN',
            type: 'string'
        }
		]

	});

	/** *********************Store********************* */
	/** 任务列表数据源* */
	var resourceStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'resourceModel',
		pageSize : 6,
		proxy : {
			type : 'ajax',
			url : 'detailResourceBase.do?id=' +IID ,// utLogInfoRecord.do
		
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'totalUT'
			}
		}
	});

	resourceStore.on('load', function(store, options, success) {
		var reader = store.getProxy().getReader();
		Ext.getCmp("INAME").setValue(reader.jsonData.INAME);
		Ext.getCmp("IRSTYPE").setValue(reader.jsonData.IRSTYPE);
		Ext.getCmp("IVERSION").setValue(reader.jsonData.IVERSION);
		Ext.getCmp("IIP").setValue(reader.jsonData.IIP);
		Ext.getCmp("IPORT").setValue(reader.jsonData.IPORT);
		Ext.getCmp("IFLAG").setValue(reader.jsonData.IFLAG);
		
		Ext.getCmp("ICREATETIME").setValue(reader.jsonData.ICREATETIME);
		Ext.getCmp("IUPDATETIME").setValue(reader.jsonData.IUPDATETIME);
		Ext.getCmp("ICPU").setValue(reader.jsonData.ICPU);
		Ext.getCmp("ICPUUSE").setValue(reader.jsonData.ICPUUSE);
		Ext.getCmp("IMEMORY").setValue(reader.jsonData.IMEMORY);
		Ext.getCmp("IMEMORY_REMAIN").setValue(reader.jsonData.IMEMORY_REMAIN);
		Ext.getCmp("IDISK").setValue(reader.jsonData.IDISK);
		Ext.getCmp("IDISK_REMAIN").setValue(reader.jsonData.IDISK_REMAIN);
	});

	var resourceForm = Ext.create('Ext.form.Panel', {
		region : 'center',
		buttonAlign : 'center',
		border : false,
		bodyCls : 'x-docked-noborder-top',
		items : [ {
			layout : 'column',
			anchor : '95%',
			padding : '5 0 5 0',
			border : false,
			items : [ {
				id : "INAME",
				fieldLabel : '设备名称',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : "IRSTYPE",
				fieldLabel : '类型',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			},  {
				id : "IVERSION",
				fieldLabel : '版本',
				labelAlign : 'right',
				labelWidth : 90,
				width : '45%',
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : 'IIP',
				fieldLabel : 'IP',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			},{
				id : 'IPORT',
				fieldLabel : '端口',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IFLAG',
				fieldLabel : '状态',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ICREATETIME',
				fieldLabel : '创建时间',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IUPDATETIME',
				fieldLabel : '更新时间',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ICPU',
				fieldLabel : 'CPU',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'ICPUUSE',
				fieldLabel : 'CPU占用',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IMEMORY',
				fieldLabel : '内存',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IMEMORY_REMAIN',
				fieldLabel : '内存剩余',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IDISK',
				fieldLabel : '磁盘',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}, {
				id : 'IDISK_REMAIN',
				fieldLabel : '磁盘剩余',
				labelAlign : 'right',
				labelWidth : 90,
				padding : '5 0 5 5',
				width : '45%',
				xtype : 'textfield'
			}]
		} ]
	});
	var workItemRecord_mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "ut_grid",
		width : '100%',
		height : '100%',
		autoScroll :true,
		border : false,
		bodyPadding : 5,
		layout : 'border',
		items : [ resourceForm ]
	});

	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(workItemRecord_mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});

});
