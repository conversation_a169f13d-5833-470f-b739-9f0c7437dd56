Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   //destroyRubbish();
	 var choice=-1;
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IPORT',
            type: 'string'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
            name: 'IVERSION',
            type: 'string'
        },
        {
        	name: 'IPLATFORM',
        	type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'ICREATETIME',
            type: 'long'
        },
        {
            name: 'IUPDATETIME',
            type: 'long'
        },
        {
            name: 'ICPU',
            type: 'string'
        }
        ,
        {
            name: 'ICPUUSE',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY_REMAIN',
            type: 'string'
        }
        ,
        {
            name: 'IDISK',
            type: 'string'
        }
        ,
        {
            name: 'IDISK_REMAIN',
            type: 'string'
        }
        ,
        {
        	name: 'IPASSWORD',
        	type: 'String'
        }
        ,
        {
        	name: 'ISYSDBNAME',
        	type: 'string'
        }
        ,
        {
        	name: 'ISID',
        	type: 'string'
        },{
        	name: 'iType',
        	type: 'string'
        }
        ]
    });
   
   var dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceBaseList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var dataTypeModel = Ext.create('Ext.data.Store', {
            fields: ['name'],
            autoLoad : true,
            autoDestroy : true,
            proxy : {
                type : 'ajax',
                url : 'getDatabaseType.do',
                reader : {
                    type : 'json',
                    root : 'dataList'
                }
            }
        });
    var iTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "RAC"
        },
        {
    		"name": "INSTANCE"
    	},
    	{
    		"name": "DATAGUARD"
    	}
        ]
    });
    var platFormStore = Ext.create('Ext.data.Store', {
    	 fields: ['name'],
         data: [{'name': 'Windows'}, {'name': 'Linux'}]
    });
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
            baseIp: ipField.getValue(),
            baseRstype: typeField.getValue()
        };

        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65,
        locked : true,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '服务器IP',
        dataIndex: 'IIP',
        width: 100, 
        locked : true,
        editor: {
            allowBlank: true
        },
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '版本',
        dataIndex: 'IVERSION',
        width: 100, 
        locked : true,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        width: 120,
        locked : true,
        editor: {
            allowBlank: false
        },
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '资源类型',
        dataIndex: 'IRSTYPE',
        width: 100, 
        locked : true,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: dataTypeModel
        }),renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '服务器端口',
        dataIndex: 'IPORT',
        width: 100,
        editor: {
            allowBlank: true
        }
    },
    {
        text: '资源所属平台',
        dataIndex: 'IPLATFORM',
        width: 120,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: platFormStore
        }),
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '类型',
        dataIndex: 'iType',
        width: 100,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: iTypeModel
        }),renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '服务名',
        dataIndex: 'ISID',
        width: 140,
        hidden:true, 
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: 'DBA用户名',
        dataIndex: 'ISYSDBNAME',
        width: 100,
        hidden:true,
        editor: {
            allowBlank: false
        },renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '密码',
        dataIndex: 'IPASSWORD',
        width: 100,
        hidden:true,
   	 	editor: new Ext.form.TextField({ 						
			inputType:'password', //设置输入类型为password
			allowBlank: true,
			allowNegative: true
		 }),
		 renderer:retNotView
    },
    {
    	text: 'CPU',
        dataIndex: 'ICPU',
        width: 60,
        editor:false
    },{
    	text: 'CPU剩余',
        dataIndex: 'ICPUUSE',
        width: 100,
        editor:false
    },
    {
    	text: '内存 ',
        dataIndex: 'IMEMORY',
        width: 100,
        editor:false
    },
    {
    	text: '内存剩余',
        dataIndex: 'IMEMORY_REMAIN',
        width: 100,
        editor:false
    },
    {
        text: '磁盘容量(G)',
        dataIndex: 'IDISK',
        width: 120,
        editor:false
    }, {
        text: '磁盘剩余(G)',
        dataIndex: 'IDISK_REMAIN',
        width: 120,
        editor:false
    },
    {
        text: '创建时间',
        dataIndex: 'ICREATETIME',
        width: 150,
        editor:false,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '修改时间',
        dataIndex: 'IUPDATETIME',
        width: 150,
        editor:false,
        renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        header: '状态',
        dataIndex: 'IFLAG',
        width: 80,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: Ext.create('Ext.data.Store', {
                fields: ['name'],
                data: [{
                	'name':'有效'
                },
                {
                	'name':'失效'
            	}
                ]
            })
        })
    },
    {
        text: '操作',
        width: 100,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('IID');
            return '<div>' + '<a href="javascript:void(0)" onclick="showDetail('+iid+')">' + '&nbsp;查看详情' + '</a>' + '</div>';
        }
    }];
    function retNotView(value){
    	var coun ="";
    	if (value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
    // 分页工具
  var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

//    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
//        clicksToEdit: 1
//    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var ipField = Ext.create("Ext.form.field.Text", {
        fieldLabel: 'IP',
        labelWidth: 37,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '18%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    var typeField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '资源类型',
        labelWidth: 65,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '20%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
    
	/**导入导出按钮**/
	var importOrExportButton = Ext.create("Ext.Button",{
            text: '导入/导出',
            cls:'Common_Btn',
            menu: {
                xtype: 'menu',
                plain: true,
                items: {
                    xtype: 'buttongroup',
                    columns: 2,
                    defaults: {
                        xtype: 'button'
                    },
                    items: [ {
						text : '导入',
						cls:'Common_Btn',
						handler : uploadExcel
					},
					{
						text : '导出',
						cls:'Common_Btn',
						handler: function() {
		                	var record = dataSourceGrid.getSelectionModel().getSelection();
		                	var ids=[];
		            		if(record.length!=0){
		            			Ext.each(record,function(item){
		            				var iid = item.get('IID');
		            				if(iid != 0){
		            					ids.push(iid);
		            					ids.join(',')
		            				}
		            			});
		            		}
		            		window.location.href = 'exportResourceBase.do?ids='+ids;
		            	}
					}]
                }
            }
	});
	var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [
	            ipField,
	            typeField,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            },'->',
	            {
	                text: '增加',
	                cls: 'Common_Btn',
	             //   handler: add
	                handler:function(){
	                	saveDatabase(0)//参数为0时表示添加
	                }
	            },
	            {
	             //   text: '保存',
	            	text: '编辑',
	                cls: 'Common_Btn',
	            //    handler: saveDatabase
	                handler:function(){
	                	saveDatabase(1)//参数为1时表示修改
	                }
	            },importOrExportButton, '-', {
	                itemId: 'delete',
	                text: '删除',
	                cls: 'Common_Btn',
	                disabled: true,
	                handler: deleteDataBase
	            }]
			}]
	});
  var  dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
		padding : panel_margin,
		cls:'customize_panel_back',
        selModel: selModel,
        border: false,
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	itemdblclick : function(dbclickthis, record, item,index, e, eOpts) {
        		choice=1;
            	showEditForm(record);
			}
        }
    });
    dataSourceGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
    	form.down('#delete').setDisabled(selections.length === 0);
    }); 
    //导入
    function uploadExcel(){
    	var uploadWindows;
    	var uploadForm
        uploadForm = Ext.create('Ext.form.FormPanel',{
        	border : false,
        	items : [{
            	xtype: 'filefield',
    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
    			fieldLabel: '选择文件',
    			labelWidth: 80,
    			anchor: '90%',
    			margin: '10 10 0 40',
    			buttonText: '浏览'
            }],
            buttonAlign : 'center',
            buttons :[{
            	text : '确定',
            	handler :upExeclData
            },{
            	text : '取消',
            	handler : function(){
            		uploadWindows.close();
            	}
            }]
        });
        /**
         * Excel导入Agent信息窗体
         */
        uploadWindows = Ext.create('Ext.window.Window', {
    		title : 'Excel导入',
    		layout : 'fit',
    		height : 150,
    		width : 600,
    		modal : true,
//    		autoScroll : true,
    		items : [ uploadForm ],
    		listeners : {
    			close : function(g, opt) {
    				uploadForm.destroy();
    			}
    		}
    	});
        uploadWindows.show();
        function upExeclData(){
        	var form = uploadForm.getForm();
    		var hdupfile=form.findField("fileName").getValue();
    		if(hdupfile==''){
    			Ext.Msg.alert('提示',"请选择文件...");
    			return ;
    		}
    		uploadTemplate(form);
        }
        function uploadTemplate(form) {
      	   if (form.isValid()) {
             form.submit({
               url: 'importResourceBase.do',
                 success: function(form, action) {
                    var sumsg = Ext.decode(action.response.responseText).message;
                    Ext.Msg.alert('提示',sumsg);
            		uploadWindows.close();
            		dataSourceStore.reload();
                    return;
                 },
                 failure: function(form, action) {
                     var msg = Ext.decode(action.response.responseText).message;
//                     var mess = Ext.create('Ext.window.MessageBox', {
//                     minHeight : 110,
//                     minWidth : 500,
//                     resizable : false
//                   });
                     Ext.Msg.alert('提示',msg);
                   return;
                 }
             });
      	   }
      	 }
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resource_base",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg customize_stbtn',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function clearQueryWhere() {
    	ipField.setValue('');
    	typeField.setValue('');
    }
 /*   function add() {
        var store = dataSourceGrid.getStore();
        var p = {
        		IID: '',
        		IIP: '',
        		IPORT: '15000',
        		INAME: '',
        		IPLATFORM: '',
        		IRSTYPE: '',
        		IVERSION: '',
        		ISID: '',
        		ISYSDBNAME: '',
        		IPASSWORD: '',
        		ICPU: '',
        		ICPUUSE: '',
        		IMEMORY: '',
        		IDISK: '',
        		ICREATETIME: '',
        		IUPDATETIME: '',
        		IFLAG: ''
        };
        store.insert(0, p);
        dataSourceGrid.getView().refresh();
    }
    function saveDatabase() {
        var m = dataSourceStore.getModifiedRecords();
        if (m.length < 1) {
            setMessage('无需要增加或者修改的数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0,
        len = m.length; i < len; i++) {
        	var IIP = m[i].get("IIP").trim();
            if ("" == IIP || null == IIP) {
                setMessage('服务器IP不能为空！');
                return;
            }
            var IPORT = m[i].get("IPORT").trim();
            if ("" == IPORT || null == IPORT) {
                setMessage('服务器端口不能为空！');
                return;
            }
            var INAME = m[i].get("INAME").trim();
            if ("" == INAME || null == INAME) {
                setMessage('设备名称不能为空！');
                return;
            }
            var IRSTYPE = m[i].get("IRSTYPE").trim();
            if ("" == IRSTYPE || null == IRSTYPE) {
                setMessage('资源类型不能为空！');
                return;
            }
            var IVERSION = m[i].get("IVERSION").trim();
            if ("" == IVERSION || null == IVERSION) {
                setMessage('资源版本不能为空！');
                return;
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        Ext.Ajax.request({
            url: 'saveResourceBase.do',
            method: 'POST',
            params: {
                jsonData: jsonData
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    dataSourceStore.modified = [];
                    dataSourceStore.reload();
                    Ext.Msg.alert('提示', message);
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
    }

    function isNumber(val){
    	if (val == null) return true;
        var regObj = /^\d*$/g;
        if (regObj.test(val)) {
            return true;
        } else {
            return false;
        }
    }
    
    function checkIp(val){
    	var regObj = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/   
    	 if (regObj.test(val)) {
             return true;
         } else {
             return false;
         }
    }
   */  
    function deleteDataBase() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要删除数据源?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'deleteResourceBase.do',
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {                
                            dataSourceStore.reload();
                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message); 
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    	dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }
  
    function saveDatabase(c){
    	choice=c;
    	if(choice==0){
    		showEditForm();
    	}else if(choice==1){
    		var record = dataSourceGrid.getSelectionModel().getSelection();
        	if(record.length==0){
        		Ext.Msg.alert('提示', '请选择一条要编辑的记录！');
        		return;
        	}else if(record.length>1){
        		Ext.Msg.alert('提示', '只能选择一条要编辑的记录！');
        		return;
        	}else{
        		showEditForm(record[0]);
        	}	
    	}
    	
    	
    }

    //编辑时打开一个记录窗口
    function showEditForm(record) {
    	var SRTeditWindow;
        if (null==SRTeditWindow ) {
            var form = Ext.widget('form', {
                border: false,
                bodyPadding: 3,
                autoScroll: true,
                items: [ {
                	xtype:'hiddenfield',
                	name:'IID'
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                    xtype: 'textfield',
		                    name:   'IIP',
		                    fieldLabel: '服务器IP',
		                    regex:/^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/,
		                    regexText:'IP格式错误',
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }, {
		                    xtype: 'textfield',
		                    name:'IVERSION',
		                    fieldLabel: '版本',
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                    xtype: 'textfield',
		                    name:'INAME',
		                    fieldLabel: '设备名称',
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }, {
		                	xtype : 'combo',  
		                    name:'IRSTYPE',
		                    fieldLabel: '资源类型',
		                    triggerAction: 'all',
		                    editable: false,
		                    queryMode: 'local',
		                    displayField: 'name',
		                    valueField: 'name',
		                    store: dataTypeModel,
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                    xtype: 'textfield',
		                    name:'IPORT',
		                    fieldLabel: '服务器端口',
		                    value:'15000',
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }, {
		                	xtype : 'combo',
		                    name:'IPLATFORM',
		                    fieldLabel: '资源所属平台',
		                    triggerAction: 'all',
		                    editable: false,
		                    queryMode: 'local',
		                    displayField: 'name',
		                    valueField: 'name',
		                    store: platFormStore,
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                	xtype : 'combo',
		                    name:'iType',
		                    fieldLabel: '类型',
		                    triggerAction: 'all',
		                    editable: false,
		                    queryMode: 'local',
		                    displayField: 'name',
		                    valueField: 'name',
		                    store: iTypeModel,
		                    allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }, {
		                    xtype: 'textfield',
		                    name:'ISID',
		                    fieldLabel: '实例名',
		                   // allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                    xtype: 'textfield',
		                    name:'ISYSDBNAME',
		                    fieldLabel: 'DBA用户名',
		                   // allowBlank: false,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }, {
		                    xtype: 'textfield',
		                    name:'IPASSWORD',
		                    fieldLabel: '密码',
		                   // allowBlank: false,
		                    inputType: 'password',
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    maxLength:50
		                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                	xtype : 'combo',
		                    name:'IFLAG',
		                    fieldLabel: '状态',
		                    triggerAction: 'all',
		                    editable: false,
		                    queryMode: 'local',
		                    displayField: 'name',
		                    valueField: 'name',
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5',
		                    store: Ext.create('Ext.data.Store', {
		                        fields: ['name'],
		                        data: [{
		                        	'name':'有效'
		                        },
		                        {
		                        	'name':'失效'
		                    	}
		                        ]
		                    }),
		                    maxLength:50
		                },{
		                	 //控件类型为numberfield
		                    xtype: "numberfield",
		                    //字段名称，绑定和获取数据的时候用到
		                    name: "ICPU",
		                    //显示的标签
		                    fieldLabel: "CPU",
		                    //控件的值
		                    value: 1,
		                    //能否为空，true为必填项，false为可以为空
		                    allowBlank: false,
		                    //最大值
		                    maxValue: 100,
		                    //最小值
		                    minValue: 4,
		                    //获得焦点时选中输入的内容
		                    selectOnFocus: true,
		                    //是否只读，true为只读，false为可编辑
		                    readOnly: false,
		                    //是否可用，true为不可用，false为可用
		                    disabled: false,
		                    //是否隐藏上下调节按钮
		                    hideTrigger: false,
		                    //键盘导航是否可用，启用后可以通过键盘的上下箭头调整数值
		                    keyNavEnabled: true,
		                    //鼠标滚轮是否可用，启用后可以通过滚动鼠标滚轮调整数值
		                    mouseWheelEnabled: true,
		                    //通过调节按钮、键盘、鼠标滚轮调节数值时的大小
		                    step: 2,
		                    columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5'
		                }]
                },
                {
					border : false,
					layout : 'column',
					items :[{
		                	//控件类型为numberfield
		                	xtype: "numberfield",
		                	//字段名称，绑定和获取数据的时候用到
		                	name: "IMEMORY",
		                	//显示的标签
		                	fieldLabel: "内存",
		                	//控件的值
		                	value: 1,
		                	//能否为空，true为必填项，false为可以为空
		                	allowBlank: false,
		                	//最大值
		                	maxValue: 100,
		                	//最小值
		                	minValue: 16,
		                	//获得焦点时选中输入的内容
		                	selectOnFocus: true,
		                	//是否只读，true为只读，false为可编辑
		                	readOnly: false,
		                	//是否可用，true为不可用，false为可用
		                	disabled: false,
		                	//是否隐藏上下调节按钮
		                	hideTrigger: false,
		                	//键盘导航是否可用，启用后可以通过键盘的上下箭头调整数值
		                	keyNavEnabled: true,
		                	//鼠标滚轮是否可用，启用后可以通过滚动鼠标滚轮调整数值
		                	mouseWheelEnabled: true,
		                	//通过调节按钮、键盘、鼠标滚轮调节数值时的大小
		                	step: 2,
		                	columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5'
		                },{
		                	//控件类型为numberfield
		                	xtype: "numberfield",
		                	//字段名称，绑定和获取数据的时候用到
		                	name: "IDISK",
		                	//显示的标签
		                	fieldLabel: "磁盘空间(G)",
		                	//控件的值
		                	value: 1,
		                	//能否为空，true为必填项，false为可以为空
		                	allowBlank: false,
		                	//最大值
		                	maxValue: 1048576,
		                	//最小值
		                	minValue: 1024,
		                	//获得焦点时选中输入的内容
		                	selectOnFocus: true,
		                	//是否只读，true为只读，false为可编辑
		                	readOnly: false,
		                	//是否可用，true为不可用，false为可用
		                	disabled: false,
		                	//是否隐藏上下调节按钮
		                	hideTrigger: false,
		                	//键盘导航是否可用，启用后可以通过键盘的上下箭头调整数值
		                	keyNavEnabled: true,
		                	//鼠标滚轮是否可用，启用后可以通过滚动鼠标滚轮调整数值
		                	mouseWheelEnabled: true,
		                	//通过调节按钮、键盘、鼠标滚轮调节数值时的大小
		                	step: 2,
		                	columnWidth : .5,
		                    width : contentPanel.getWidth() - 20,
		                    padding : '5 5 10 5'
		                }]    	
                }],
                dockedItems : [{
					xtype : 'toolbar',
					border : false,
					baseCls:'customize_gray_back',
					dock : 'bottom',
					items : ['->', {
							cls : 'Common_Btn',
		    				textAlign : 'center',    
							text: '取消',
		                    handler: function() {
		                        this.up('form').getForm().reset();
		                        this.up('window').close();
		                        SRTeditWindow=null;
		                    }
		                }, {
		                	cls : 'Common_Btn',
		    				textAlign : 'center',
		                	text: '保存',
		                    handler: function() {
		                    	var IID="";
		                    	var url="saveResourceBase.do";
		                    	if(choice==1){
		                    		IID=form.getForm().findField('IID').getValue();
		                    		url="updateResourceBase.do";
		                    	}
		                        if (this.up('form').getForm().isValid()) {
		                        	 var jsonData = '{"IID":"'+IID+ '",';
		                        	 jsonData=jsonData+'"IIP":"'+form.getForm().findField('IIP').getValue() + '",';
		                        	 jsonData=jsonData+'"IVERSION":"'+form.getForm().findField('IVERSION').getValue() + '",';
		                        	 jsonData=jsonData+'"INAME":"'+form.getForm().findField('INAME').getValue() + '",';
		                        	 jsonData=jsonData+'"IRSTYPE":"'+form.getForm().findField('IRSTYPE').getValue() + '",';
		                        	 jsonData=jsonData+'"IPORT":"'+form.getForm().findField('IPORT').getValue() + '",';
		                        	 jsonData=jsonData+'"IPLATFORM":"'+form.getForm().findField('IPLATFORM').getValue() + '",';
		                        	 jsonData=jsonData+'"iType":"'+form.getForm().findField('iType').getValue() + '",';
		                        	 jsonData=jsonData+'"ISID":"'+form.getForm().findField('ISID').getValue() + '",';
		                        	 jsonData=jsonData+'"ISYSDBNAME":"'+form.getForm().findField('ISYSDBNAME').getValue() + '",';
		                        	 jsonData=jsonData+'"IPASSWORD":"'+form.getForm().findField('IPASSWORD').getValue() + '",';
		                        	 jsonData=jsonData+'"ICPU":"'+form.getForm().findField('ICPU').getValue() + '",';
		                        	 jsonData=jsonData+'"IMEMORY":"'+form.getForm().findField('IMEMORY').getValue() + '",';
		                        	 jsonData=jsonData+'"IDISK":"'+form.getForm().findField('IDISK').getValue() + '",';
		                        	 jsonData=jsonData+'"IFLAG":"'+form.getForm().findField('IFLAG').getValue() + '"}';
		                        	 Ext.Ajax.request({
		    							method:"POST",
		    							url:url,
		    							params:{jsonData:jsonData},
		    							success:function(response){
		    								var text = Ext.JSON.decode(response.responseText);
		    								if(text.success){
		    									Ext.Msg.alert('提示', '操作成功！');
		    									dataSourceStore.reload();   
		    								}else{
		    		    						Ext.Msg.alert('提示', text.message);
		    								}
		    								
		    							},
		    							failure:function(form, action){
		    								switch (action.failureType) {
		    		    					case Ext.form.action.Action.CLIENT_INVALID:
		    		    						Ext.Msg.alert('提示', '连接异常！');
		    		    						break;
		    		    					case Ext.form.action.Action.SERVER_INVALID:
		    		    						Ext.Msg.alert('提示', action.result.message);
		    		    					}
		    							}
		    						});
		                            this.up('window').close();
		                            SRTeditWindow=null;
		                        }
		                    }
		                },{
		                	cls : 'Common_Btn',
		    				textAlign : 'center',
		                	text: '重置',
		                    handler: function() {
		                        this.up('form').getForm().reset();
		                        this.up('form').getForm().loadRecord(record);
		                    }
		                }]
                }]
            });
            if(choice==1){
            	form.loadRecord(record);
            }

            SRTeditWindow = Ext.widget('window', {
                title: '编辑记录',
                closeAction: 'hide',
                constrain: true,
                resizable: false,
    			width : contentPanel.getWidth()*0.6,
    			//height : contentPanel.getHeight(),
                minWidth: 300,
                minHeight: 300,
                layout: 'fit',
                modal: true,
                items: form,
                defaultFocus: 'firstName'
            });
        }
        SRTeditWindow.show();
    }    
    
 
});
function showDetail(IID) {
	Ext.create('Ext.window.Window', {
	    title: '详情',
	    height: '60%',  //Number型  也可以是字符串类型  width:'60%'
	    width: '60%',
	    layout: 'fit',
	    constrain: true, 		//闲置窗口不超出浏览器
	    constrainHeader:true, 	//标题不能超出浏览器边界
	    modal: true,			//设置模态窗口
	    plain:true, 			//窗口设置透明背景
	    draggable: false,
	    resizable: false,
	    loader: {
			url: 'todetailResourceBase.do',
			params : {
				IID : IID
			},
			autoLoad: true,
			scripts: true
		},
	    autoScroll:true //显示滚动条
	}).show();
}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}
