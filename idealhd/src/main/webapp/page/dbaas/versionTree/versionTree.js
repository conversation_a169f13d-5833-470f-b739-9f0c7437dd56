var instanceWindow;
var parentIds=[];
Ext.onReady (function ()
{
    var choice=0;
    var ishiddenDbType=true;
	// 清理主面板的各种监听时间
	destroyRubbish ();
	/** *********************Model********************* */
	/** 编目Model* */
	Ext.define ('businessTypeModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		            {
		                name : 'ibusName',
		                type : 'string'
		            },
		            {
		                name : 'iid',
		                type : 'long'
		            },
		            {
		                name : 'icreateUser',
		                type : 'string'
		            },
		            {
		                name : 'icreateTime',
		                type : 'string'
		            },
		            {
		                name : 'ibusnDesc',
		                type : 'string'
		            },
		            {
		                name : 'iorder',
		                type : 'string'
		            },
		            {
		                name : 'iparentid',
		                type : 'string'
		            },
	                {
	                    name : 'iproType',
	                    type : 'string'
	                },
	                {
	                    name : 'iprotypeName',
	                    type : 'string'
	                }
		            
		    ]
		});
	/** *********************Store********************* */
	/** 编目列表数据源* */

	var businessTypeStore = Ext.create ('Ext.data.TreeStore',
	{
	    model : 'businessTypeModel',	    
	    proxy :
	    {
	        type : 'ajax',
	        async:false,
	        url : 'versionTreeList.do'
	    },
	    root : {
            expanded : true,
            leaf : false
        },
        autoLoad:true,
        autoSync : true
	});
	businessTypeStore.on ('beforeload', function (store, options)
			{
				var new_params =
				{
					busName : sysNameForQuery.getValue ().trim ()
				};
				Ext.apply (businessTypeStore.proxy.extraParams, new_params);
			});
	
	businessTypeStore.on('load',function(store,node,records,successful,eOpts){ 
		//展开节点
        if(parentIds.length>0){
        	grid_panel.getRootNode( ) 
        	var allrootNode=grid_panel.getRootNode().childNodes;
        	for(var i = 0; i<allrootNode.length;i++){             		 
        		var iid=allrootNode[i].data.iid;
        		if(parentIds.indexOf(iid)>-1){
        			expandAllNodeByRootNode(allrootNode[i])
        		}
        	}

        }	
     }); 
	/** *********************组件********************* */
	/** 查询按钮* */
	var queryButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '查询',
	    handler : queryWhere
	});
	/** 重置按钮* */
	var resetButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '重置',
	    handler : resetWhere
	});
	/** 增加按钮* */
	var addButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '增加',
	    handler:function(){
	    	onAddListener(0)//参数为0时表示添加
        },
	 //   handler : onAddListener
	});
	var editorButtonForBSM = Ext.create ("Ext.Button",
	    {
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '编辑',
	    handler:function(){
	    	onAddListener(1)//参数为0时表示添加
        },
	   // handler : onEditorListener
	    });
	/** 删除按钮* */
	var deleteButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '删除',
	    disabled : true,
	    handler : onDeleteListener
	});
	/** 保存按钮* */
//	var saveButtonForBSM = Ext.create ("Ext.Button",
//	{
//	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    text : '保存',
//	    handler : onSaveListener
//	});
	/** 业务系统类型名查询输入框* */
	var sysNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    emptyText : '--请输入名称--',
	    labelWidth : 80,
	    width : 350,
	    xtype : 'textfield',
	    fieldLabel:'属性名称',
	    listeners: {
			specialkey: function(field,e){ 
					if (e.getKey()==Ext.EventObject.ENTER){
						businessTypeStore.load({
        					params: {
								busName : sysNameForQuery.getValue ().trim ()
        					}
        				});
					}
				}
		}
	});
	
	var Form1 = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border : false,
			dock : 'top',
			items : [sysNameForQuery, queryButtonForBSM, resetButtonForBSM, '->', addButtonForBSM,editorButtonForBSM,deleteButtonForBSM]
		} ]
	});
	
	/** 列表Columns* */
	var gridColumns = [
	       	        {
	       	            text : 'ID',
	       	            dataIndex : 'iid',
	       	            flex : 1,
	       	            hidden : true
	       	        },
	       	        {
	       	        	
	       	        	xtype: 'treecolumn', 
	       	            text : '属性名称',
	       	            // sortable : true,
	       	            dataIndex : 'ibusName',
	       	            flex : 1,
	       	            editor :
	       	            {
	       		            allowBlank : false
	       	            },
	       	            renderer : function(value, metadata) {
	       					metadata.tdAttr = 'data-qtip="' + value + '"';
	       					return value;
	       				}
	       	        },
	       	        {
                        text : '所属类型',
                        sortable : true,
                        dataIndex : 'iprotypeName',
                        flex : 1,
                        renderer : function(value, metadata) {
	       					metadata.tdAttr = 'data-qtip="' + value + '"';
	       					return value;
	       				}
                    },
	       	        {
	       	            text : '创建人',
	       	            sortable : true,
	       	            dataIndex : 'icreateUser',
	       	            flex : 1,
	       	            renderer : function(value, metadata) {
	       					metadata.tdAttr = 'data-qtip="' + value + '"';
	       					return value;
	       				}
	       	        },
	       	        {
	       	            text : '创建时间',
	       	            sortable : true,
	       	            dataIndex : 'icreateTime',
	       	            flex : 1,
	       	            renderer : function(value, metadata) {
	       					metadata.tdAttr = 'data-qtip="' + value + '"';
	       					return value;
	       				}
	       	        },
	       	        {
	       	            text : '版本信息描述',
	       	            sortable : true,
	       	            dataIndex : 'ibusnDesc',
	       	            flex : 1,
	       	            editor :
	       	            {
	       		            allowBlank : true
	       	            },
	       	            renderer : function(value, metadata) {
	       					metadata.tdAttr = 'data-qtip="' + value + '"';
	       					return value;
	       				}
	       	        },
	       	        {
	       	            text : '排序号',
	       	            sortable : true,
	       	            dataIndex : 'iorder',
	       	            hidden:true,
	       	            editor :
	       	            {
	       		            allowBlank : true
	       	            }
	       	        },
	       	        {
	       	            text : 'iparentid',
	       	            dataIndex : 'iparentid',
	       	            flex : 1,
	       	            hidden : true,
	       	            renderer:function(value, p, record) {
	       	            	if(record.get("iparentid")==0){
	       	            		return "";
	       	            	}
	       	            	parentIds=[];
	       	     		    var iparentid=getrootNodeiId(record);
	       	     		    parentIds.push(iparentid);
	       	         	},
	       	        }
	       	];
	/** *********************Panel********************* */
	/** 业务系统类型列表panel* */
	var grid_panel = Ext.create ('Ext.tree.Panel',
	{
		id:'grid_panel',
	    store : businessTypeStore,
	    region : 'center',
	    padding : panel_margin,
	    selModel : Ext.create ('Ext.selection.CheckboxModel'),
        useArrows: true,  
        rootVisible: false,  
		columns : gridColumns,
		cls:'customize_panel_back',
        columnLines : true,
        border : true
	});
	/** 判断删除按钮是否可用* */
	grid_panel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		deleteButtonForBSM.setDisabled (selections.length === 0);
	});
	/** 主Panel* */
	var businessType_mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "versionTree",
	    layout : 'border',
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight () - modelHeigth,
	    bodyPadding : grid_margin,
	    border : true,
	    bodyCls:'service_platform_bodybg',
	    items : [Form1,grid_panel]
	});
	
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		businessType_mainPanel.setWidth(contentPanel.getWidth ());
		businessType_mainPanel.setHeight (contentPanel.getHeight () - 35);
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (businessType_mainPanel);
		Ext.destroy (instanceWindow);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	
	
	
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	
	/** 查询业务系统* */
	function queryWhere ()
	{
		businessTypeStore.load();
	}
	/** 重置业务系统类别查询条件* */
	function resetWhere ()
	{
		sysNameForQuery.setValue ('');
	}
	
	/** 增加业务系统类型* */
	function onAddListener (c)
	{
		ishiddenDbType=true;
		parentIds=[];
		choice=c;		
		var currparentid=0;
		var record = grid_panel.getSelectionModel ().getSelection ();
		var iparentid=0;		
    	if(choice==0){
    		if(record.length>1){
    			Ext.Msg.alert("提示","请选择一条信息新增！");
    			return;
    		}else if (record.length==1){
    		   iparentid=record[0].get ('iid');
    		   currparentid=record[0].get ('iparentid');
			   if(null==iparentid||iparentid.length==0){
				   Ext.Msg.alert("提示","请选择保存信息再新增！");
				   return;
			   }
    		   var yIproType = record[0].get("iproType");
			   var p = Ext.create ('businessTypeModel',
	    				{
	    		         iid:0,
	    				 ibusName:"",
	    				 iproType:yIproType,
	    				 icreateUser:loginName+"/"+loginId,
	    				 icreateTime : "",
	    				 ibusnDesc : "",
	    				 iparentid:iparentid
	    		});
               showEditForm(p,iparentid);
    		}else if (record.length==0){
    			ishiddenDbType=false;
    			var p = Ext.create ('businessTypeModel',
	    				{
	    		         iid:0,
	    				 ibusName:"",
	    				 iproType:"",
	    				 icreateUser:loginName+"/"+loginId,
	    				 icreateTime : "",
	    				 ibusnDesc : "",
	    				 iparentid:""
	    		});
    			showEditForm(p,0);
    		}
    		
    	}else if(choice==1){    		
        	if(record.length==0){
        		Ext.Msg.alert('提示', '请选择一条要编辑的记录！');
        		return;
        	}else if(record.length>1){
        		Ext.Msg.alert('提示', '只能选择一条要编辑的记录！');
        		return;
        	} 
        	
        	iparentid=record[0].get ('iid');
        	var parentid1=record[0].get ('iparentid');
        	if(parentid1==0){
				ishiddenDbType=false;
			}
        	showEditForm(record[0],iparentid);
        	
    	}

    	if(iparentid!=0){
    		var rootparentid=getrootNodeiId(record[0]);
    		parentIds.push(rootparentid);
    	}		
		
	}
	 
	 var dataTypeModel = Ext.create('Ext.data.Store', {
         fields: ['id','name'],
         autoLoad : true,
         autoDestroy : true,
         proxy : {
             type : 'ajax',
             url : 'getDatabaseType.do?dataFlag=1',
             reader : {
                 type : 'json',
                 root : 'dataList'
             }
         }
     });
    function showEditForm(record,iparentid) {
        var editWindow;
        if (null==editWindow ) {
            var form = Ext.widget('form', {
                border: false,
                bodyPadding: 3,
                autoScroll: true,
                items: [ 
		                {
		                    border : false,
		                    layout : 'column',
		                    items :[{
		                            xtype:'hiddenfield',
		                            name:'iid'
		                         }]
		                },
		                {
		                    border : false,
		                    layout : 'column',
		                    items :[{ 
	                                xtype: 'textfield',
	                                name:'ibusName',
	                                fieldLabel: '属性名称',
	                                padding : '5 5 10 5',
	                                columnWidth : 1,
	                                labelAlign : 'right',
	                                allowBlank: false,
	                                maxLength:50
	                            }, {
	                                xtype: 'textfield',
	                                name:'ibusnDesc',
	                                fieldLabel: '版本描述',
	                                labelAlign : 'right',
	                                padding : '5 5 10 5',
	                                columnWidth : 1,
	                                allowBlank: false,
	                                maxLength:50
	                            }]
		                },{
                        border : false,
                        layout : 'column',
                        hidden:ishiddenDbType,
                        items :[{
	                        	xtype: 'combo',
	                            name:'iproType',
	                            fieldLabel: '所属类型',
	                            labelAlign : 'right',
	                            padding : '5 5 10 5',
	                            triggerAction: 'all',
	                            editable: false,
	                            queryMode: 'local',
	                            displayField: 'name',
	                            valueField: 'id',
	                            columnWidth : 1,
	                            allowBlank: false,
	                            store: dataTypeModel,
	                            maxLength:50
							}]
                        }
                    ],
                dockedItems : [{
                    xtype : 'toolbar',
                    border : false,
                    baseCls:'customize_gray_back',
                    dock : 'bottom',
                    items : ['->',{
                        cls : 'Common_Btn',
                        textAlign : 'center',
                        text: '取消',
                        handler: function() {                       
                            this.up('form').getForm().reset();
                            this.up('window').close();
                            usreditWindow=null;
                        }
                    }, {
                        cls : 'Common_Btn',
                        textAlign : 'center',
                        text: '保存',
                        handler: function() {
                        	var pro=form.getForm().findField('iproType').getValue();
	                       	var procheck = record.get('iproType');
	                       	if(choice==0&&null==pro){
	                       		pro = procheck;
	                       		form.getForm().findField('iproType').setValue(procheck);
	                       	}
	                       	record.set("ibusName",form.getForm().findField('ibusName').getValue());
	                        record.set("ibusnDesc",form.getForm().findField('ibusnDesc').getValue());
	                        record.set("iproType",pro);
	                        record.set("icreateUser",loginName+"/"+loginId);
	                        record.set("iparentid",iparentid);
                       	 
	                       	if (this.up('form').getForm().isValid()) {
	                            saveListener(record);
	                            choice=0;
	                            this.up('window').close();
	                            editWindow=null;
	                       	 }
                        }
                    },{
                        cls : 'Common_Btn',
                        textAlign : 'center',
                        text: '重置',
                        handler: function() {
                            this.up('form').getForm().reset();
                            if(choice==1){
                            	this.up('form').getForm().loadRecord(record);
                            }
                            
                        }
                    }]
                }]
            });
            if(choice==1){form.loadRecord(record);}
            editWindow = Ext.widget('window', {
                title: '编辑',
                closeAction: 'hide',
                constrain: true,
                resizable: false,
                width : contentPanel.getWidth()*0.5,
                minWidth: 300,
                minHeight: 300,
                layout: 'fit',
                modal: true,
                items: form,
                defaultFocus: 'firstName'
            });
        }
        editWindow.show();
    }
	
	/** 保存业务系统类型* */
    function saveListener(record)
    {
    	var ss = Ext.JSON.encode (record.data);
        var jsonData = "[" + ss+ "]";
        Ext.MessageBox.wait ("数据处理中...", "进度条");
        Ext.Ajax.request (
            {
                url : 'saveVersionTree.do',
                timeout : 30000,
                params :
                {
                incdata : jsonData
                },
                method : 'POST',
                success : function (response, opts)
                {
                    var success = Ext.decode (response.responseText).success;
                    // 当后台数据同步成功时
                    queryWhere();   
                    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
                },
                failure : function (result, request)
                {
                    secureFilterRs(result,"请求返回失败！",request);
                }
            });
    }
	function onSaveListener (btn)
	{
		var m = businessTypeStore.getModifiedRecords ();
		if (m.length < 1)
		{
			Ext.Msg.alert ('提示', '无需要增加或者修改的数据！');
			return;
		}
		
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++)
		{
			var ss = Ext.JSON.encode (m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
		Ext.MessageBox.wait ("数据处理中...", "进度条");
		Ext.Ajax.request (
		{
		    url : 'saveVersionTree.do',
		    timeout : 30000,
		    params :
		    {
			    incdata : jsonData
		    },
		    method : 'POST',
		    success : function (response, opts)
		    {
			    var success = Ext.decode (response.responseText).success;
			    // 当后台数据同步成功时
			    if (success)
			    {
			    	queryWhere();
			    }
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
		    },
		    failure : function (result, request)
		    {
			    /*Ext.MessageBox.hide ();
			    Ext.Msg.alert ('提示', '请求超时！');*/
		    	secureFilterRs(result,"请求返回失败！",request);
		    }
		});
	}
	/** 删除业务系统类型* */
	function onDeleteListener (btn)
	{
		parentIds=[];
		var record = grid_panel.getSelectionModel ().getSelection ();
		var iparentid=getrootNodeiId(record[0]);
		parentIds.push(iparentid);
		for(var i = 0; i<record.length;i++){   
			var iparentid=getrootNodeiId(record[i]);		
			parentIds.push(iparentid);		
		} 		
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		var goBack = false;// 判断是否需要删除数据库数据
		for (var i = 0; i < record.length; i++)
		{
			
			if (-1 != record[i].get ('iid'))// 如果预删除数据id不为-1，则需要删除数据库数据
			{
				goBack = true;
				break;
			}
			
		}
		if (goBack)// 通过数据库删除数据
		{
			Ext.MessageBox.confirm ('提示', "删除预案同时会删除子数据，是否确认删除选中数据!", function (btn)
			{
				if (btn == 'no')
				{
					return;
				}
				if (btn == 'yes')
				{
					
					Ext.MessageBox.wait ("数据处理中...", "进度条");
					var ids = [];
					Ext.Array.each (record, function (recordObj)
					{
						var cpId = recordObj.get ('iid');
						// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
						if (-1 != cpId)
						{
							ids.push (cpId);
						}
					});
					Ext.Ajax.request (
					{
						url : 'deleteVersionTree.do',
					    timeout : 30000,
					    params :
					    {
						    // incdata : t_date
					    	deleteIds : ids.join (',')
					    },
					    method : 'POST',
					    success : function (response, opts)
					    {
						    var success = Ext.decode (response.responseText).success;
						    
						    if (success)
						    {
						    	queryWhere();						    	
						    }
						    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
					    },
					    failure : function (result, request)
					    {
						    /*Ext.MessageBox.hide ();
						    Ext.Msg.alert ('提示', '请求超时！');*/
					    	secureFilterRs(result,"请求返回失败！",request);
					    }
					});
					
				}
			});
		}
		else
		{
			for (var j = 0; j < record.length; j++)
			{
				// 如果不需要删除数据库数据，则不刷新列表，仅移除store数据
				businessTypeStore.remove (record[j]);
				
			}
		}
		
	}
	

	
});
// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
// 例子：
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
Date.prototype.Format = function (fmt)
{ // author: meizz
	var o =
	{
	    "M+" : this.getMonth () + 1, // 月份
	    "d+" : this.getDate (), // 日
	    "h+" : this.getHours (), // 小时
	    "m+" : this.getMinutes (), // 分
	    "s+" : this.getSeconds (), // 秒
	    "q+" : Math.floor ((this.getMonth () + 3) / 3), // 季度
	    "S" : this.getMilliseconds ()
	// 毫秒
	};
	if (/(y+)/.test (fmt))
		fmt = fmt.replace (RegExp.$1, (this.getFullYear () + "").substr (4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp ("(" + k + ")").test (fmt))
			fmt = fmt.replace (RegExp.$1, (RegExp.$1.length == 1) ? (o[k])
			        : (("00" + o[k]).substr (("" + o[k]).length)));
	return fmt;
};


function  getrootNodeiId(record){	
	var iparentid=0;
	var parentnodeId=record.parentNode.id;	
	if(parentnodeId!='businessTypeModel-root'){
		return getrootNodeiId(record.parentNode)
	}else{		
		iparentid=record.data.iid;
		return iparentid;
	}
	
}

function  expandAllNodeByRootNode(node){	
	node.expand();
	var childNodes =node.childNodes;
	if(node.childNodes.length!=0){		
    	for(var i = 0; i<childNodes.length;i++){             		 
    		expandAllNodeByRootNode(childNodes[i])
    	}		
	}
}