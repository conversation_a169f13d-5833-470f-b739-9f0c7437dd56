Ext.onReady(function(){
	 destroyRubbish();
	 Ext.tip.QuickTipManager.init();
	 Ext.define('service', {
        extend: 'Ext.data.Model',
        fields: [
    	    {name : 'iid'         ,type : 'long'}, 
		    {name : 'uuid'         ,type : 'string'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'serviceId' ,type : 'string'}, 
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'flowId'  ,type : 'long'}, 
		    {name : 'coatId'  ,type : 'long'}, 
		    {name : 'platForm',type : 'string'},
		    {name : 'startTime',type : 'string'},
		    {name : 'resId',type : 'string'},
		    {name : 'isAutoSub',type : 'string'}, 
		    {name : 'serviceType',type : 'string'}
		    
        ]
     });	 	 
	 var runServiceSon_store = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        autoDestroy : true,
//		    remoteSort: true,
	        pageSize: 30,
	        model: 'service',
	        proxy: {
	            type: 'ajax',
	            url: 'getRunServiceSonList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
		 

	 var runService_columns = [{
						text : '序号',
						xtype : 'rownumberer',
						 width : '30',
					}, {
						text : 'iid',
						dataIndex : 'iid',
						hidden : true
					}, {
						text : '发起时间',
						dataIndex : 'startTime',
						flex:1,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					}, {
						text : '服务名称',
						dataIndex : 'serviceName',
						flex:1,
						renderer : function(value, metadata,record,rowIndex) {
			
								metadata.tdAttr = 'data-qtip="' + value + '"';
								return value;
							
						}
					},{
						text : '服务号',
						dataIndex : 'serviceId',
						flex:1,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					}, {
						text : '脚本类型',
						dataIndex : 'scriptType',
						hidden:true,
						width : '50',
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					}, {
						text : '适用平台',
						dataIndex : 'platForm',
						width : '60',
					}
					];   
	 
	 //左侧页数工具栏
	 var leftsounthPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	    	store: runServiceSon_store,
	        dock: 'bottom',
	        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border : false
	     });
     
     var serviceNameQuery = Ext.create('Ext.form.TextField', {
		emptyText : '--请输入服务名称--',
		labelWidth : 50,
		width : '25%',
		xtype : 'textfield',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					return;
				}
			}
		}
	});
     var serviceIdQuery = Ext.create('Ext.form.TextField', {
 		emptyText : '--请输入服务号--',
 		labelWidth : 50,
 		width : '25%',
 		xtype : 'textfield',
 		listeners : {
 			specialkey : function(field, e) {
 				if (e.getKey() == Ext.EventObject.ENTER) {
 					return;
 				}
 			}
 		}
 	});
	var queryButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '查询',
		handler : queryWhere
	});
	 
	var resetButtonForBSM = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '重置',
		handler : resetWhere
	});
	var stopButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '返回',
        handler: function(){
        	destroyRubbish();
        	var url='serviceRunMonitorPage.do';       	
    		contentPanel.getLoader().load({
    			url: url,
    			params: {					
    			},
    			scripts: true
    		});
    		if (refreshTryForUpdate) {
          		clearInterval(refreshTryForUpdate);
          	}
    		$('#uploadify-base-edit').uploadify('destroy');
        }
	});

	runServiceSon_store.on('beforeload',function(store, options) {
			var serviceName = serviceNameQuery.getValue()==null?"":serviceNameQuery.getValue();
			var serviceId = serviceIdQuery.getValue()==null?"":serviceIdQuery.getValue();
			var new_params = {
					serviceName:serviceName.trim(),
					serviceId : serviceId.trim(),
					flowId:flowId
					};
					Ext.apply(runServiceSon_store.proxy.extraParams, new_params);
				});
	 
	 
	 
	 var runServiceSon_grid = Ext.create('Ext.grid.Panel', {
		    store:runServiceSon_store,
		    padding : panel_margin,
			border:true,
			bbar: leftsounthPageBar,
		    columnLines : true,
		    columns:runService_columns,
		    viewConfig:{  
	            enableTextSelection:true  
	        },
		    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
	 	    dockedItems: [{
	  	    xtype: 'toolbar',
	  	  	border : false,
		  		items: [/*serviceNameQuery,serviceIdQuery,queryButtonForBSM,resetButtonForBSM,*/'->',stopButton
		  		]}
		  	]
		});
	
	 var panelleft = Ext.create('Ext.panel.Panel',{
	    	title:'原子服务',
	    	layout : 'fit',
	    	border:false,
			region : 'west',
			split : true,
			width : '40%',
			bodyCls : 'x-docked-noborder-top',
			cls:'customize_panel_back panel_space_right',
			items : [runServiceSon_grid]
	    });	 
		
	   var nextService_store = Ext.create('Ext.data.Store', {
		        autoLoad : false,
		        autoDestroy : true,
		        pageSize: 100,
		        model: 'service',
		        proxy: {
		            type: 'ajax',
		            url: 'getSonNextServiceListByFlowId.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
    var nextService_columns = [{
		text : '序号',
		xtype : 'rownumberer',
		width:'70',
	}, {
		text : 'iid',
		dataIndex : 'iid',
		hidden : true
	}, {
		text : '服务名称',
		dataIndex : 'serviceName',
		flex:1,
		renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	},{
		text : '服务号',
		dataIndex : 'serviceId',
		flex:1,
		renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, {
		text : '脚本类型',
		dataIndex : 'scriptType',
		hidden:true,
		renderer : function(value, metadata) {
			metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
	}, {
		text : '适用平台',
		dataIndex : 'platForm',
		flex:1,
	},{
		text : '操作',
		dataIndex : 'iid',  
		flex:1,
		renderer : function(value, p,record) {
			var uuid=record.get("uuid");
			var resId=record.get("resId");
			var scriptType=record.get("scriptType");
			var isAutoSub=record.get("isAutoSub");
			var serviceType=record.get("serviceType");
			return "<span class=\"switch_span\"><a href=\"javascript:void(0)\" onclick=\"serviceStart('"+ value +"','"+uuid+"','"+resId+"','"+scriptType+"','"+isAutoSub+"','"+serviceType+"');\">&nbsp;发起</a></span>";
		}
	}
];
	    nextService_store.on('beforeload', function (store, options) {
//	        var selDatas = runServiceSon_store.getSelectionModel().getSelection();
//	        var ii  = selDatas.length;
//	        var flowId=-1;
//	        if(ii<1){
//	        	flowId = -1;
//	        }else{
//	        	flowId = selDatas[ii-1].get('flowId');
//	        }
	        var new_params = {  
	        		flowId : flowId,
	            };
	     Ext.apply(nextService_store.proxy.extraParams, new_params);
	  });
    var rightPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: nextService_store,
        dock: 'bottom',
        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border : false
    });
    var  nextService_grid= Ext.create('Ext.grid.Panel', {
        store:nextService_store,
        padding : panel_margin,
        border:true,
        bbar: rightPageBar,  
        columnLines : true,
        columns:nextService_columns
    });
	var rigthUser = Ext.create('Ext.panel.Panel',{ 
	    title : '推荐服务',
	    layout : 'fit',
	    bodyCls : 'x-docked-noborder-top',
	    cls:'customize_panel_back',
	    region : 'east',
	    border : false,
	    width : '30%',
	    items : [nextService_grid]
	});

	
	
	
	 Ext.define('history', {
	        extend: 'Ext.data.Model',
	        fields: [
	    	    {name : 'id',type : 'long'}, 
			    {name : 'instanceName',type : 'string'}, 
			    {name : 'agentIp' ,type : 'string'},
			    {name : 'agentPort' ,type : 'string'},
			    {name : 'resId' ,type : 'string'},
			    {name : 'state' ,type : 'long'},
			    {name : 'flowId' ,type : 'long'},
			    {name : 'coatId' ,type : 'long'}			    
	        ]
	     });
	 var history_store = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        autoDestroy : true,
	        pageSize: 30,
	        model: 'history',
	        proxy: {
	            type: 'ajax',
	            url: 'getRunServiceHistoryList.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	    var history_columns = [{
			text : '序号',
			xtype : 'rownumberer',
			width:'70',
		}, {
			text : 'id',
			dataIndex : 'id',
			hidden : true
		}, {
			text : '资源信息',
			dataIndex : 'instanceName',
			flex:1,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		}, {
			text : 'agentIp',
			dataIndex : 'agentIp',
			hidden : true,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},{
			text : '操作',
			dataIndex : 'id',
			flex:1,
			renderer : function(value, p,record) {
						return "<span class=\"switch_span\"><a href=\"#\" valign=\"middle\" onclick=\"openActWindowForExecForFlow('" +record.get('id')  +"','" + record.get('state')  + "', '"+ record.get('agentIp') + "','"+ record.get('agentPort') +"');\"><span class='abc'>查看结果</span></a></span>";
			}
		}
	];   
	    
	 var centerPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	        store: history_store,
	        dock: 'bottom',
	        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border : false
	    });
	    var history_grid= Ext.create('Ext.grid.Panel', {
	        store:history_store,
	        padding : panel_margin,
	        border:true,
	        bbar: centerPageBar,  
	        columnLines : true,
	        columns:history_columns
	    });
	var centerPanel = Ext.create('Ext.panel.Panel',{ 
	    title : '资源信息',
	    layout : 'fit',
	    bodyCls : 'x-docked-noborder-top',
	    cls:'customize_panel_back panel_space_right',
	    region : 'center',
	    border : false,
	    width : '30%',
	    items : [history_grid]
	});
	
	
	var mainPanel = Ext.create('Ext.panel.Panel',{
    	height:contentPanel.getHeight()-modelHeigth,
        renderTo : "AppSysManage_area",
        border : true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',	
        layout : 'border',
    	items : [panelleft,centerPanel,rigthUser]
    });

	runServiceSon_grid.on("select",function(obj, record, index, eOpts){
    	var coatId = record.get('coatId'); 
    	var flowId = record.get('flowId'); 
    	
    	if(coatId<1){
    		return;
    	}
    	nextService_grid.getStore().removeAll(); 
		history_store.load({
    	    params: {
    	    	coatId : coatId,
    	    	flowId : flowId
    	    }
    
    	});
    	
    	
    });
	
	
	
	history_grid.on("select",function(obj, record, index, eOpts){
    	var flowId = record.get('flowId'); 
    	var resId = record.get('resId'); 
    	var coatId = record.get('coatId'); 
    	
    	if(flowId<1){
    		return;
    	}
    	nextService_store.load({
    	    params: {
    	    	flowId : flowId,
    	    	resId:resId,
    	    	coatId:coatId
    	}
    	});
    });
	
	
	
	
    contentPanel.on('resize',function(){    	
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    function queryWhere() {
    	var serviceName = serviceNameQuery.getValue()==null?"":serviceNameQuery.getValue();
    	var serviceId= serviceIdQuery.getValue()==null?"":serviceIdQuery.getValue();
    	runServiceSon_store.load({
    			params: {
    				serviceName:serviceName.trim(),
    				serviceId:serviceId.trim()
    			}    				    			
    	});
	}

    function resetWhere ()
	{
    	serviceNameQuery.setValue ('');
    	serviceIdQuery.setValue ('');
	}

});
function openActWindowForExecForFlow(requestId, state,agentIp,agentPort) {
	var runningWindow = null;
	var surl = "getScriptExecOutput.do";
	if (runningWindow == undefined || !runningWindow.isVisible()) {
		runningWindow = Ext.create('Ext.window.Window', {
			title : '查看结果',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			width : contentPanel.getWidth()*0.8,
			height : contentPanel.getHeight()*0.8,
			loader : {
	 			url : "dbaasoutputpage.do",
	 			 params:{
	 				 	surl:surl,state:state, requestId:requestId, agentIp:agentIp, agentPort:agentPort,flag3ForExecForFlow:1
	 			 	},
					autoLoad: true,
					autoDestroy : true,
					scripts : true
  			},
			layout : 'fit'
		});
	}
	runningWindow.show();
}

function serviceStart(iid,uuid,resId,scriptType,isAutoSub,serviceType){
    var ServiceStartFormWindow = Ext.create('widget.window', {
    	id:'ServiceStartFormWindow',
		title : '发起服务',
		closable : true,
		closeAction : 'destroy',
		width : contentPanel.getWidth()*0.7,
		minWidth : 350,
		height : contentPanel.getHeight()*0.7,
		draggable : false,
		// 禁止拖动
		resizable : false,
		// 禁止缩放
		modal : true,			
		loader : {
			url : 'nextServiceStartPage.do',
			params : {
				iid : iid,
				uuid : uuid,
				resId:resId,  
				scriptType:scriptType,
				isAutoSub:isAutoSub,
				serviceType:serviceType
			},
			autoLoad : true,
			scripts : true
		}
	});
	ServiceStartFormWindow.show();
}

//编辑按钮执行一个.do跳转到编辑页面
function gotoSonServiceRunMonitorPage(iid,uuid,flowId){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({
		url: 'gotoSonServiceRunMonitorPage.do?flowId='+flowId,
		params: { 
	    },
		scripts: true
	});
}