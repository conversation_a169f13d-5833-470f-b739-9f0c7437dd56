 var permissionPanel;
Ext.onReady(function() {

var paramTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "IN-string"
        },
        {
            "name": "IN-int"
        },
        {
            "name": "IN-float"
        },
        {
            "name": "OUT-string"
        },
        {
            "name": "OUT-int"
        },
        {
            "name": "OUT-float"
        }]
    });
    
    var paramTypeCombo = Ext.create('Ext.form.field.ComboBox', {
        margin: '5',
        store: paramTypeStore,
        queryMode: 'local',
        width: 600,
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'name',
        triggerAction: "all"
    });
    var paramColumns = [
                    {
                        text: '主键',
                        dataIndex: 'iid',
                        width: 40,
                        hidden: true
                    },
                    {
                        text: '顺序',
                        dataIndex: 'paramOrder',
                        width: 100,
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"';  
                            return value;  
                        }
                    },
                    {
                        text: '类型',
                        dataIndex: 'paramType',
                        width: 200,
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"'; 
                            return value;  
                        }
                    },
                    {
                        text: '默认值',
                        dataIndex: 'paramDefaultValue',
                        flex: 1,
                        editor: {
                            allowBlank: true
                        },
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"'; 
                            return value;  
                        }
                    },
                    {
                        text: '描述',
                        dataIndex: 'paramDesc',
                        flex: 1,
                        editor: {
                            allowBlank: true
                        },
                        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                        	metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
                            		+"<br>默认值："+record.get('paramDefaultValue')
                            		+"<br>排    序："+record.get('paramOrder')
                            		+"<br>描    述："+record.get('paramDesc')) 
                            		+ '"';
                            return value;  
                        }
                    }];
    
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [
        	{name: 'iid',type: 'int'},
	        {name: 'paramType',type: 'string'},
	        {name: 'paramDefaultValue',type: 'string'},
	        {name: 'paramDesc',type: 'string'},
	        {name: 'paramOrder', type: 'int' }
        ]
    });
    var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
		name : 'pubdesc',
		fieldLabel : '详细说明',
		emptyText : '',
		labelWidth : 80,
		margin : '10 0 0 20',
		height : 95,
		width: 600,
		autoScroll : true
	});
    var formPanel = Ext.create('Ext.form.Panel', {
    	width : '100%',
		height : '20%',
		border : false,
		region : 'north',
		buttonAlign : 'center',
		dockedItems : [ {
            xtype: 'toolbar',
			border : false,
			baseCls: 'customize_gray_back',
            items: [pubDesc_sm]
        }]
	});
    
    
    
    
	var cellEditing3 = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: startForm_uuid
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });
    
	   var paramGrid = Ext.create('Ext.grid.Panel', {
	        width: '100%',
	        region: 'center',
	        cls:'customize_panel_back',
	        title: "脚本参数",
	        store: paramStore,
	        margin: '0 0 5 0',
	        selModel: false,
	        emptyText: '没有脚本参数',
	        plugins: [cellEditing3],
	        border: true,
	        columnLines: true,
	        columns: paramColumns
	    });
	   
		var submitPanel = Ext.create('Ext.form.Panel', {
			border : false,
			region : 'south',
			buttonAlign : 'center',
			height:'10%',
			dockedItems : [ {
	            xtype: 'toolbar',
				border : false,
				baseCls: 'customize_gray_back',
	            items: ['->',
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '发起',
	                handler: function() {
	                	start();
	                }
	            }
	            ]
	        }]
		});
		function start(){	
			Ext.MessageBox.wait("任务发起中...","进度条");
			var publishDesc = pubDesc_sm.getValue();
			if (!publishDesc) {
				Ext.Msg.alert('提示', "没有填写详细说明！");
				return;
			}
			/**参数校验start*/
			var args = new Array();
			var record_args = paramStore.data;
			for (var i = 0; i < record_args.length; i++) {
				args.push(record_args.items[i].data);
			}
	        var m = paramStore.getRange();
	        var jsonData = "[";
	        for (var i = 0, len = m.length; i < len; i++) {
	            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
	            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
	            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
	            if ("" == paramType) {
	                setMessage('参数类型不能为空！');
	                return;
	            }
	            if (fucCheckLength(paramDesc) > 250) {
	                setMessage('参数描述不能超过250字符！');
	                return;
	            }
	           if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramDefaultValue) {
	                if (!checkIsInteger(paramDefaultValue)) {
	                    setMessage('参数类型为int，但参数默认值不是int类型！');
	                    return;
	                }
	            }
	            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramDefaultValue) {
	                if (!checkIsDouble(paramDefaultValue)) {
	                    setMessage('参数类型为float，但参数默认值不是float类型！');
	                    return;
	                }
	            }
	        	var ss = Ext.JSON.encode(m[i].data);
				if (i == 0)
					jsonData = jsonData+ ss;
				else
					jsonData = jsonData+ ","+ ss;
	        }
	        jsonData = jsonData + "]";
	        var pubflag=0;
			if(startForm_isAutoSub==2){
				pubflag=1;
			}
			Ext.Ajax.request({
				url : 'serviceStartDbaasForRroject.do',
				method : 'POST',
				params : {
					serviceIid : startForm_iid,
					auditor :'',
					isDelay : false, // 是否定时延时
					agents : resId+ "_1", // 服务器id
					iisexam : 0, // 是否需要审核
					args : args,
					argsData : jsonData,
					taskNames : '',
					serviceType : startForm_serviceType,
					scriptType : startForm_scriptType,																				
					flag : pubflag,
					planTime:'',
					planTimeType : '立即执行',
					planTimeDD : '',
					planTimeHH : '',
					planTimeMM : '',
					publishDesc:publishDesc,
					sId:startForm_iid,
					runServiceflowId:runServiceflowId
				},
				success : function(response,opts) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (!success) {
						Ext.MessageBox.alert("提示", message);
					} else {
						Ext.MessageBox.alert("提示", "发起成功");
						var win = parent.Ext.getCmp('ServiceStartFormWindow');
			    	    if (win) {win.close();}
					}																					
				},
				failure : function(result,request) {
					secureFilterRs(result,"操作失败！");

				}
			});
		
	        
	        
		}
	   var mainPanel = Ext.create('Ext.panel.Panel', {
			renderTo : "startConfig_area",
			layout : 'border',
			cls:'customize_panel_back',
			width : contentPanel.getWidth()*0.7,
			height : contentPanel.getHeight()*0.7- modelHeigth,
			bodyPadding : grid_margin,
			border : true,
			bodyCls:'service_platform_bodybg',
			items : [ formPanel,paramGrid, submitPanel ]
		});
		contentPanel.on('resize', function() {
			mainPanel.setWidth(contentPanel.getWidth());
			mainPanel.setHeight(contentPanel.setHeight() - modelHeigth);
		});
});