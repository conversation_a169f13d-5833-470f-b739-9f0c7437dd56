Ext.onReady(function(){
    var pagelimit=50;
	 destroyRubbish();
	 Ext.tip.QuickTipManager.init();
	 
	 /* ---------------------    model  start -------------------------------------*/
	 Ext.define('service', {
        extend: 'Ext.data.Model',
        fields: [
    	    {name : 'iid'         ,type : 'long'}, 
		    {name : 'uuid'         ,type : 'string'}, 
		    {name : 'serviceName' ,type : 'string'}, 
		    {name : 'serviceId' ,type : 'string'}, 
		    {name : 'scriptType'  ,type : 'string'}, 
		    {name : 'flowId'  ,type : 'long'}, 
		    {name : 'coatId'  ,type : 'long'}, 
		    {name : 'platForm',type : 'string'},
		    {name : 'startTime',type : 'string'},
		    {name : 'resId',type : 'string'},
		    {name : 'isAutoSub',type : 'string'}, 
		    {name : 'serviceType',type : 'string'}
        ]
     });
	 Ext.define('history', {
	        extend: 'Ext.data.Model',
	        fields: [
	    	    {name : 'id',type : 'long'}, 
			    {name : 'instanceName',type : 'string'}, 
			    {name : 'agentIp' ,type : 'string'},
			    {name : 'agentPort' ,type : 'string'},
			    {name : 'resId' ,type : 'string'},
			    {name : 'state' ,type : 'long'},
			    {name : 'flowId' ,type : 'long'}
			    
	        ]
	     });
	 Ext.define('nextServiceResult', {
	        extend: 'Ext.data.Model',
	        fields: [
			    {name : 'instanceName',type : 'string'}, 
			    {name : 'coatId' ,type : 'long'},
			    {name : 'flowId' ,type : 'long'},
			    {name : 'startTime',type : 'string'},
			    {name : 'resId',type : 'long'}, 
	        ]
	     });
	 /* ---------------------    model  end -------------------------------------*/
	 
	 
	 
	 /* ---------------------    store  start -------------------------------------*/
	 	//左侧store
		 var runService_store = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        autoDestroy : true,
		        pageSize: pagelimit,
		        model: 'service',
		        proxy: {
		            type: 'ajax',
		            url: 'getRunServiceList.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
		 runService_store.on('beforeload',function(store, options) {
			 	history_grid.getStore().removeAll();  
			 	nextService_grid.getStore().removeAll();  
		    	nextServiceResult_grid.getStore().removeAll();  
				var serviceName = serviceNameQuery.getValue()==null?"":serviceNameQuery.getValue();
				var serviceId = serviceIdQuery.getValue()==null?"":serviceIdQuery.getValue();
				var new_params = {
						serviceName:serviceName.trim(),
						serviceId : serviceId.trim(),
				};
				Ext.apply(runService_store.proxy.extraParams, new_params);
		  });
		 
		 
		
		 //中间
		 var history_store = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        autoDestroy : true,
		        pageSize: 30,
		        model: 'history',
		        proxy: {
		            type: 'ajax',
		            url: 'getRunServiceHistoryList.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    });
			//右上store
		 var nextService_store = Ext.create('Ext.data.Store', {
		        autoLoad : false,
		        autoDestroy : true,
		        pageSize: 100,
		        model: 'service',
		        proxy: {
		            type: 'ajax',
		            url: 'getNextServiceListByFlowId.do',
		            reader: {
		                type: 'json',
		                root: 'dataList',
		                totalProperty: 'total'
		            }
		        }
		    }); 
		 //右下
		 var nextServiceResult_store = Ext.create('Ext.data.Store', {
		        autoLoad: true,
		        autoDestroy : true,
		        model: 'nextServiceResult',
		        proxy: {
		            type: 'ajax',
		            url: 'getNextServiceHistoryList.do',
		            reader: {
		                type: 'json',
		                root: 'dataList'
		            }
		        }
		    });
	 /* ---------------------    store  end -------------------------------------*/
	 
	 /* ---------------------    column  start -------------------------------------*/
		 //左侧
		 var runService_columns = [{
				text : '序号',
				xtype : 'rownumberer',
				 width : '30',
			}, {
				text : 'iid',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : '发起时间',
				dataIndex : 'startTime',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '服务名称',
				dataIndex : 'serviceName',
				flex:1,
				renderer : function(value, metadata,record,rowIndex) {
					var iid=record.get('iid');
					var uuid=record.get('uuid');
					var flowId=record.get('flowId');
					var isAutoSub=record.get('isAutoSub');						
					if(isAutoSub==2){
						
						var retHtml='<span class="switch_span">'+
	       			   	'<a href="javascript:void(0)" onclick="gotoSonServiceRunMonitorPage('+iid+',\''+uuid+'\','+flowId+')">'+value+
	       			   	'</a>'+'</span>'+'&nbsp;&nbsp;&nbsp;&nbsp;';
						return retHtml;
					}else{
						metadata.tdAttr = 'data-qtip="' + value + '"';
						return value;
					}
					
				}
			},{
				text : '服务号',
				dataIndex : 'serviceId',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '脚本类型',
				dataIndex : 'scriptType',
				hidden:true,
				width : '50',
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '适用平台',
				dataIndex : 'platForm',
				width : '60',
			}
			];   
		 //中间
		 var history_columns = [{
				text : '序号',
				xtype : 'rownumberer',
				width:'70',
			}, {
				text : 'id',
				dataIndex : 'id',
				hidden : true
			}, {
				text : '资源信息',
				dataIndex : 'instanceName',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : 'agentIp',
				dataIndex : 'agentIp',
				hidden : true,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},{
				text : '操作',
				dataIndex : 'id',
				flex:1,
				renderer : function(value, p,record) {
							return "<span class=\"switch_span\"><a href=\"#\" valign=\"middle\" onclick=\"openActWindowForExecForFlow(" +record.get('flowId')  +"," + record.get('resId')  + ");\"><span class='abc'>查看结果</span></a></span>";
				}
			}
		]; 
		 
		 //右上
		 var nextService_columns = [{
				text : '序号',
				xtype : 'rownumberer',
				width:'70',
			}, {
				text : 'iid',
				dataIndex : 'iid',
				hidden : true
			}, {
				text : '服务名称',
				dataIndex : 'serviceName',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},{
				text : '服务号',
				dataIndex : 'serviceId',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '脚本类型',
				dataIndex : 'scriptType',
				hidden:true,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '适用平台',
				dataIndex : 'platForm',
				flex:1,
			},{
				text : '操作',
				dataIndex : 'iid',  
				flex:1,
				renderer : function(value, p,record) {
					var uuid=record.get("uuid");
					var resId=record.get("resId");
					var scriptType=record.get("scriptType");
					var isAutoSub=record.get("isAutoSub");
					var serviceType=record.get("serviceType");
					var flowId=record.get("flowId");
					return "<span class=\"switch_span\"><a href=\"javascript:void(0)\" onclick=\"serviceStart('"+ value +"','"+uuid+"','"+resId+"','"+scriptType+"','"+isAutoSub+"','"+serviceType+"','"+flowId+"');\">&nbsp;发起</a></span>";
				}
			}
		];
		 
		 
		 //右下
		 var nextServiceResult_columns = [{
				text : '序号',
				xtype : 'rownumberer',
				width:'70',
			}, {
				text : '发起时间',
				dataIndex : 'startTime',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			}, {
				text : '资源信息',
				dataIndex : 'instanceName',
				flex:1,
				renderer : function(value, metadata) {
					metadata.tdAttr = 'data-qtip="' + value + '"';
					return value;
				}
			},{
				text : '操作',
				dataIndex : 'coatId',
				flex:1,
				renderer : function(flowId, p,record) {
							return "<span class=\"switch_span\"><a href=\"#\" valign=\"middle\" onclick=\"openActWindowForExecForFlow(" +record.get('flowId')   +"," + record.get('resId')  + ");\"><span class='abc'>查看结果</span></a></span>";
				}
			}
		]; 
		 
	 /* ---------------------    column  end -------------------------------------*/ 

	/* ---------------------    form start -------------------------------------*/ 
		 var serviceNameQuery = Ext.create('Ext.form.TextField', {
				emptyText : '--请输入服务名称--',
				labelWidth : 50,
				width : '25%',
				xtype : 'textfield',
			    listeners: {
			          specialkey: function(field, e){
			              if (e.getKey() == e.ENTER) {
			            	  leftPageBar.moveFirst();
			              }
			          }
			      }
			});
		     var serviceIdQuery = Ext.create('Ext.form.TextField', {
		 		emptyText : '--请输入服务号--',
		 		labelWidth : 50,
		 		width : '25%',
		 		xtype : 'textfield',
		 	    listeners: {
		 	          specialkey: function(field, e){
		 	              if (e.getKey() == e.ENTER) {
		 	            	 leftPageBar.moveFirst();
		 	              }
		 	          }
		 	      }
		 	});
			var queryButtonForBSM = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
				textAlign : 'center',
				text : '查询',
				handler : queryWhere
			});
			var resetButtonForBSM = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
				textAlign : 'center',
				text : '重置',
				handler : resetWhere
			});
			var stopButton = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
				textAlign : 'center',
				text : '确认',
				handler : stopSerivce
			});
	/* ---------------------    form end -------------------------------------*/ 
		
	/* ---------------------    PageBar start -------------------------------------*/ 
			
	 var leftPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    	store: runService_store,
        dock: 'bottom',
        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border : false
     });
	 var rightPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	        store: nextService_store,
	        dock: 'bottom',
	        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border : false
	    });
	 var centerPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	        store: history_store,
	        dock: 'bottom',
	        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border : false
	    });
	 var selModel = Ext.create('Ext.selection.CheckboxModel');
	 /* ---------------------    PageBar end -------------------------------------*/ 
	 /* ---------------------    grid start -------------------------------------*/ 
	 //左侧
	 var runService_grid = Ext.create('Ext.grid.Panel', {
    	selModel:selModel,
	    store:runService_store,
	    padding : panel_margin,
		border:true,
		bbar: leftPageBar,
	    columnLines : true,
	    columns:runService_columns,
	    viewConfig:{  
            enableTextSelection:true  
        },
	    plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })],
 	    dockedItems: [{
  	    xtype: 'toolbar',
  	  	border : false,
	  		items: [serviceNameQuery,serviceIdQuery,queryButtonForBSM,resetButtonForBSM,'->',stopButton
	  		]}
	  	]
	});
	 //中间
	 var history_grid= Ext.create('Ext.grid.Panel', {
	        store:history_store,
	        padding : panel_margin,
	        border:true,
	        bbar: centerPageBar,  
	        columnLines : true,
	        columns:history_columns
	    });
	 //右上
	 var  nextService_grid= Ext.create('Ext.grid.Panel', {
	        store:nextService_store,
	        padding : panel_margin,
	        border:true,
	        bbar: rightPageBar,  
	        columnLines : true,
	        columns:nextService_columns
	    });
	//右下
	 var  nextServiceResult_grid= Ext.create('Ext.grid.Panel', {
	        store:nextServiceResult_store,
	        padding : panel_margin,
	        border:true,
	        columnLines : true,
	        columns:nextServiceResult_columns
	    });
	 
	 
	 /* ---------------------    grid end -------------------------------------*/ 
	 
	 /* ---------------------    布局 start -------------------------------------*/ 
	 
	 var panelleft = Ext.create('Ext.panel.Panel',{
	    	title:'服务信息',
	    	layout : 'fit',
	    	border:false,
			region : 'west',
			split : true,
			width : '40%',
			bodyCls : 'x-docked-noborder-top',
			cls:'customize_panel_back panel_space_right',
			items : [runService_grid]
	    });
		var centerPanel = Ext.create('Ext.panel.Panel',{ 
		    title : '资源信息',
		    layout : 'fit',
		    bodyCls : 'x-docked-noborder-top',
		    cls:'customize_panel_back panel_space_right',
		    region : 'center',
		    border : false,
		    width : '30%',
		    items : [history_grid]
		});
		var rigthtTop = Ext.create('Ext.panel.Panel',{ 
		    title : '推荐服务',
		    layout : 'fit',
		    height:'50%',
		    bodyCls : 'x-docked-noborder-top',
		    cls:'customize_panel_back',
		    region : 'north',
		    border : false,
		    items : [nextService_grid]
		});
		var rigthtBottom = Ext.create('Ext.panel.Panel',{ 
		    title : '推荐服务资源信息',
		    layout : 'fit',
		    margin:'5 0 0 0',
		    bodyCls : 'x-docked-noborder-top',
		    cls:'customize_panel_back',
		    region : 'center',
		    border : false,
		    items : [nextServiceResult_grid]
		});
		
		
		var rigthUser = Ext.create('Ext.panel.Panel',{ 
		    layout : 'border',
		    bodyCls : 'x-docked-noborder-top',
		    region : 'east',
		    border : false,
		    width : '30%',
		    items : [rigthtTop,rigthtBottom]
		});
	var mainPanel = Ext.create('Ext.panel.Panel',{
    	height:contentPanel.getHeight()-modelHeigth,
        renderTo : "AppSysManage_area",
        border : true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',	
        layout : 'border',
    	items : [panelleft,centerPanel,rigthUser]
    });
	   contentPanel.on('resize',function(){    	
	    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
			mainPanel.setWidth (contentPanel.getWidth () );
	    });
	    
	 /* ---------------------    布局 end -------------------------------------*/ 
	
	   /* ---------------------    事件-------------------------------------*/ 
	   //左侧点击列表
	   runService_grid.on("itemclick",function(obj, record, index, eOpts){		
	    	var flowId = record.get('flowId');        	
	    	nextService_grid.getStore().removeAll();  
	    	nextServiceResult_grid.getStore().removeAll();  
			Ext.Ajax.request({
				url : 'getsingleCoatByFlowId.do',
				params : {
					flowId : flowId
				},
				method : 'POST',
				success : function(response,opts) {
					var coatId =Ext.decode(response.responseText).coatId;
					history_store.load({
		        	    params: {
		        	    	coatId : coatId,
		        	    	flowId : flowId
		        	    }
		        	});
				},
				failure : function(result,request) {
				}
			});
	    });
	   //中间
	   history_grid.on("itemclick",function(obj, record, index, eOpts){
	    	var flowId = record.get('flowId'); 
	    	var resId = record.get('resId'); 
	    	nextServiceResult_grid.getStore().removeAll();  
	    	nextService_store.load({
	    	    params: {
	    	    	flowId : flowId,
	    	    	resId:resId
	    	}
	    	});
	    });
	   //右上点击列表
	   nextService_grid.on("itemclick",function(obj, record, index, eOpts){		
	    	var uuid = record.get('uuid');   
	    	var resId = record.get('resId'); 
	    	var flowId = record.get('flowId'); 
	    	//var runServiceflowId = record.get('runServiceflowId');   
	    	nextServiceResult_store.load({
	    	    params: {
	    	    	uuid : uuid,
	    	    	resId:resId,
	    	    	runServiceflowId:flowId
	    	}
	    	});
	    });
	   /* ---------------------    事件-------------------------------------*/ 
	
	 
	
		
	
	
	
 
    function queryWhere() {
    	var serviceName = serviceNameQuery.getValue()==null?"":serviceNameQuery.getValue();
    	var serviceId= serviceIdQuery.getValue()==null?"":serviceIdQuery.getValue();
    	runService_store.load({
    			params: {
    				serviceName:serviceName.trim(),
    				serviceId:serviceId.trim()
    			}    				    			
    	});
    	
	}
    
    function stopSerivce ()
	{
		var data = runService_grid.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
		Ext.Msg.alert('提示', '请先选择您要操作的行!');
		return;
			} else {
			Ext.Msg.confirm( "请确认", "是否确定结束所选服务，确认后服务链路将结束！", function(button, text) {
				if (button == "yes") {
					var ids = [];
					Ext.Array.each(data, function(
							record) {
						var checkItemId = record.get('flowId');
						// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
						if (checkItemId) {
							ids.push(checkItemId);
						}
					});
					Ext.Ajax
							.request({
								url : 'stopServiceByFlowId.do',
								params : {
									ids : ids.join(',')
								},
								method : 'POST',
								success : function(response,opts) {
									var success = Ext.decode(response.responseText).success;
									var message =Ext.decode(response.responseText).message;
									// 当后台数据同步成功时
									if (success) {
										runService_store.reload({
										    params:{
										        start:1,
										        limit:pagelimit
										        }
										});
									} 
									Ext.Msg.alert('提示',message);
								},
								failure : function(result,request) {
								}
							});
					
				}
			});
		}
	
	}
    function resetWhere ()
	{
    	serviceNameQuery.setValue ('');
    	serviceIdQuery.setValue ('');
	}

});


function openActWindowForExecForFlow(flowId,resId) {
	var runningWindow;
	if ( runningWindow == undefined ||!runningWindow.isVisible()) {
		runningWindow = Ext.create('Ext.window.Window', {
			title : '查看结果',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			width : contentPanel.getWidth()*0.8,
			height : contentPanel.getHeight()*0.8,
			loader : {
	 			url : "dbaasSGoutputpage.do",
	 			 params:{
	 				flowId:flowId,resId:resId
	 			 	},
					autoLoad: true,
					autoDestroy : true,
					scripts : true
  			},
			layout : 'fit'
		});
	}
	runningWindow.show();
}

function serviceStart(iid,uuid,resId,scriptType,isAutoSub,serviceType,runServiceflowId){
    var ServiceStartFormWindow = Ext.create('widget.window', {
    	id:'ServiceStartFormWindow',
		title : '发起服务',
		closable : true,
		closeAction : 'destroy',
		width : contentPanel.getWidth()*0.7,
		minWidth : 350,
		height : contentPanel.getHeight()*0.7,
		draggable : false,
		// 禁止拖动
		resizable : false,
		// 禁止缩放
		modal : true,			
		loader : {
			url : 'nextServiceStartPage.do',
			params : {
				iid : iid,
				uuid : uuid,
				resId:resId,  
				scriptType:scriptType,
				isAutoSub:isAutoSub,
				serviceType:serviceType,
				runServiceflowId:runServiceflowId
			},
			autoLoad : true,
			scripts : true
		}
	});
	ServiceStartFormWindow.show();
}

//编辑按钮执行一个.do跳转到编辑页面
function gotoSonServiceRunMonitorPage(iid,uuid,flowId){
	destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({
		url: 'gotoSonServiceRunMonitorPage.do?flowId='+flowId,
		params: { 
	    },
		scripts: true
	});
}