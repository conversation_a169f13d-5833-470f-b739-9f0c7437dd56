Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
	// 清理主面板的各种监听时间
	 var html="<div style='margin-left:30px;'>"+
		"<p>SQL_ID ctuq4m3a71nb2</p>"+
		"<p>--------------------</p>"+
		"<p>select * from AAAA_IFULLNAME061101 t WHERE T.ID>1000</p>"+
		"<p>R_NEW(current_timestamp,8)   WHERE T.IID=:1</p>"+
		"<p>Plan hash value: 560350613</p>"+
		"<p>-----------------------------------------------------------------------------------</p>"+
		"<p>| Id  | Operation          | Name                    | Rows  | Bytes | Cost (%CPU)|</p>"+
		"<p>-----------------------------------------------------------------------------------</p>"+
		"<p>|   0 | UPDATE STATEMENT   |                         |       |       |     1 (100)|</p>"+
		"<p>|   1 |  UPDATE            | IEAI_SCRIPT_INSTANCE    |       |       |            |</p>"+
		"<p>|   2 |   INDEX UNIQUE SCAN| PK_IEAI_SCRIPT_INSTANCE |     1 |    16 |     0   (0)|</p>"+
		"<p>-----------------------------------------------------------------------------------</p>"+
		"<p>Query Block Name / Object Alias (identified by operation id):</p>"+
		"<p>-------------------------------------------------------------</p>"+
		"<p>   1 - UPD$1</p>"+
		"<p>   2 - UPD$1 / T@UPD$1</p>"+
		"<p>Outline Data</p>"+
		"<p>		-------------</p></div>";
	 var html1="<div style='margin-left:30px;'>"+
		"<p>SQL_ID 1auq4m3a71nb2</p>"+
		"<p>--------------------</p>"+
		"<p>select * from AAAA_IFULLNAME061101 t WHERE T.ID>1000</p>"+
		"<p>R_NEW(current_timestamp,8)   WHERE T.IID=:1</p>"+
		"<p>Plan hash value: 560350613</p>"+
		"<p>-----------------------------------------------------------------------------------</p>"+
		"<p>| Id  | Operation          | Name                    | Rows  | Bytes | Cost (%CPU)|</p>"+
		"<p>-----------------------------------------------------------------------------------</p>"+
		"<p>|   0 | UPDATE STATEMENT   |                         |       |       |     1 (100)|</p>"+
		"<p>|   1 |  UPDATE            | IEAI_SCRIPT_INSTANCE    |       |       |            |</p>"+
		"<p>|   2 |   INDEX UNIQUE SCAN| PK_IEAI_SCRIPT_INSTANCE |     1 |    12 |     0   (0)|</p>"+
		"<p>-----------------------------------------------------------------------------------</p>"+
		"<p>Query Block Name / Object Alias (identified by operation id):</p>"+
		"<p>-------------------------------------------------------------</p>"+
		"<p>   1 - UPD$1</p>"+
		"<p>   2 - UPD$1 / T@UPD$1</p>"+
		"<p>Outline Data</p>"+
		"<p>		-------------</p></div>";
	  var toplpLog = Ext.create('Ext.form.field.Display', {
		  fieldLabel : '执行计划',
		  labelWidth: 75,
    	  labelAlign: 'left'
      });
	  toplpLog.setValue(html);
	var panel1 = Ext.create('Ext.panel.Panel', {
		layout : 'border',
		region : 'center',
		margin : '5',
		items : [ toplpLog ]
	});
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "viewsqlCenterImpactAnalyPlanSing_manage",
		layout : 'border',
		bodyCls : 'service_platform_bodybg',
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight() - modelHeigth,
		bodyPadding : grid_margin,
		border : true,
		items : [ panel1]
	});

	/* 解决IE下trim问题 */
	String.prototype.trim = function() {
		return this.replace(/(^\s*)|(\s*$)/g, "");
	};

	/** 窗口尺寸调节* */
	contentPanel.on('resize', function() {
		mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
});
