var dbSourceStore = null;
var dbSourceGrid = null;
var baseDBSType=null;
var baseDBSURL=null;
var baseDBName=null;
var checkOpenWindow;
Ext.onReady(function() {
			destroyRubbish();
			Ext.tip.QuickTipManager.init();

			// 数据源管理IGROUPMESSGEID
			Ext.define('dbSourceModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'idbsourceid',
					type : 'string'
				}, {
					name : 'igroupmessgeid',
					type : 'string'
				}, {
					name : 'idbsourcename',
					type : 'string'
				}, {
					name : 'iserverip',
					type : 'string'
				}, {
					name : 'idburl',
					type : 'string'
				}, {
					name : 'idbuser',
					type : 'string'
				}, {
					name : 'isyncProgress',
					type : 'string'
				},{
					name : 'iisbasic',
					type : 'string'
				} ]
			});

			// 数据源column
			var columnDBSource = [
					{
						text : 'iid',
						dataIndex : 'idbsourceid',
						width : 70,
						hidden : true
					},
					{
						text : '通知人员',
						dataIndex : 'idbsourcename',
						width : 120,
						editor: {
				            allowBlank: false
				        }
					},
					{
						text : '业务类型',
						dataIndex : 'iserverip',
						width : 120,
						editor: {
				            allowBlank: false
				        }
					}, {
						text : '通知内容',
						dataIndex : 'idburl',
						width : 250,
						editor: {
				            allowBlank: false
				        }
					},
					{
						text : '查看明细', // 获取按钮
						dataIndex : '',
						width : 120,
						renderer : function(value, p, r) {
							// 数据源id
							var idbsourceid = r.get('idbsourceid');
							temp = "<input type='button' value='查看明细' onclick=addSyncServer(\'" + idbsourceid + "\') class='dbsourBtn'/>";
							return temp;
						}
					}];

			// 数据源同步Store
			dbSourceStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				model : 'dbSourceModel',
				proxy : {
					type : 'ajax',
					url : 'getDBSourceForGrid.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			/** 设为基线库* */
			var setBaiscDBBtn = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
//				textAlign : 'center',
//				width : 105,
//				height : 22,
				text : "设为基线库",
				handler : function() {
					setBaiscDBBtnHandel();
				}
			});

			/** 清除数据源* */
			var clearDBSrcBtn = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
//				textAlign : 'center',
//				width : 105,
//				height : 22,
				text : "清除数据源",
				handler : function() {
					clearDBSrcBtnHandel();
				}
			});

			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true,
				listeners : {
					selectionchange : function(selModel, selections) {
					}
				}
			});
			var nameField = Ext.create("Ext.form.field.Text", {
		        fieldLabel: '通知人员',
		        labelWidth: 100,
		        labelAlign: 'right',
		        name: 'dataBaseNameParam',
		        width: '20%'
		    });
			var nameIP = Ext.create("Ext.form.field.Text", {
		        fieldLabel: '业务类型',
		        labelWidth: 60,
		        labelAlign: 'right',
		        name: 'dataBaseNameParam',
		        width: '20%'
		    });
			var nameSID = Ext.create("Ext.form.field.Text", {
		        fieldLabel: '通知内容',
		        labelWidth: 60,
		        labelAlign: 'right',
		        name: 'dataBaseNameParam',
		        width: '20%'
		    });
			// 数据源表格grid
			dbSourceGrid = Ext.create('Ext.grid.Panel', {
				dockedItems : [ {
					xtype : 'toolbar'
				} ],
				region : 'center',
				store : dbSourceStore,
				border : false,
				columnLines : true,
				forceFit : true,
				selModel : selModel,
				multiSelect : false,
				cls:'online_grid',
				plugins : [ Ext.create('Ext.grid.plugin.CellEditing', {
					clicksToEdit : 1
				}) ],
				columns : columnDBSource,
				dockedItems : [{
		            xtype: 'toolbar',
		            items: [nameField, nameIP, nameSID, {
		                xtype: 'button',
		                cls: 'Common_Btn',
		                text: '查询',
		                handler: function() {
		                	QueryMessage();
		                }
		            },
		            {
		                xtype: 'button',
		                cls: 'Common_Btn',
		                text: '清空',
		                handler: function() {
		                    clearQueryWhere();
		                }
		            }, '-', {
		                itemId: 'delete',
		                text: '删除',
		                cls: 'Common_Btn',
		                //iconCls:'sc_delete',
		                disabled: true,
		                handler: add
		            }]
		        }]
			});
			function add() {
				var store = dbSourceGrid.getStore();
		        var p = {
		        		IDSIP: '',
		        		IDSNAME: '',
		        		IDSDRIVER: '',
		        		IDSPORT: '50000',
		        		IDSUSER: '',
		        		IDSPWD: '',
		        		rIDSPWD:'',
		        		IDSROLE: 'sysdba',
		        		IDSINSTANCE:'entegor'
		        };
		        store.insert(0, p);
		        dbSourceGrid.getView().refresh(); 
		    }
			var mainPanel = Ext.create("Ext.panel.Panel", {
				layout : 'border',
				height : contentPanel.getHeight()-modelHeigth,
				width : contentPanel.getWidth(),
				layout : 'border',
			    border : false,
				items : [ dbSourceGrid ],
				renderTo : "grid_areaForDBsource"
			});

			/** 清除数据源* */
			function clearDBSrcBtnHandel() {
				var storeCnt = dbSourceStore.getCount();
				var iisbasic = null;
				var isIisbasic = false;
				var cnt = 0;
				if (storeCnt == 0) {
					Ext.Msg.alert('提示', '没有可清除的数据');
					return;
				} else {
					var checkids = [];
					var checkRecord = dbSourceGrid.getSelectionModel()
							.getSelection();
					for (var i = 0; i < checkRecord.length; i++) {
						iisbasic = checkRecord[i].get('iisbasic');
						if (iisbasic == true) {
							isIisbasic = true;
						}
						var dbsourseid = checkRecord[i].get('idbsourceid');
						checkids.push(dbsourseid);
						cnt++;
					}
					var jsonData = "[";
					for (var i = 0; i < checkRecord.length; i++) {
						var ss = Ext.JSON.encode(checkRecord[i].data);
						if (i == 0) {
							jsonData = jsonData + ss;
						} else {
							jsonData = jsonData + "," + ss;
						}
					}
					jsonData = jsonData + "]";
					if (isIisbasic) {
						checkOpenWindow = Ext
								.create(
										'Ext.window.Window',
										{
											title : '清理数据前设置新基线',
											modal : true,
											closeAction : 'destroy',
											constrain : true,
											// autoScroll:true,
											width : 900,
											height : contentPanel.getHeight() - 20,
											draggable : true,// 禁止拖动
											resizable : true,// 禁止缩放
											layout : 'fit',
											loader : {
												url : 'clearBeforeSetBasicLine.do?checkids='
														+ checkids
														+ "&jsonData="
														+ jsonData,
												autoLoad : true,
												scripts : true
											}
										}).show();
					} else {
						Ext.Msg
								.confirm(
										"请确认",
										"是否真的要清除数据？<br>(基线库数据不会被清除)",
										function(button, text) {
											if (button == "yes") {
												Ext.MessageBox.wait("数据处理中...", "进度条");
												if (cnt > 0) {
													Ext.Msg
															.confirm(
																	'提示',
																	'数据源管理面板中有'
																			+ cnt
																			+ '条记录选中，是否要清除掉',
																	function(
																			btn) {
																		if (btn == 'yes') {
																			Ext.Ajax
																					.request({
																						url : 'clearDBSrcByPK.do',
																						timeout : 60000,
																						params : {
																							arrIdbsourceid : checkids
																									.join(','),
																							jsonData : jsonData,
																							basicdbsourceid : 0
																						},
																						method : 'POST',
																						success : function(
																								response,
																								opts) {
																							var success = Ext
																									.decode(response.responseText).success;
																							if (success) {
																								dbSourceStore
																										.reload();
																								Ext.Msg
																										.alert(
																												'提示',
																												'数据清除成功');

																							} else {
																								Ext.Msg
																										.alert(
																												'提示',
																												'数据清除失败');
																							}
																						}
																					});
																		}

																		if (btn == 'no') {
																			return;
																		}

																	}, this);
												} else {
													Ext.Msg.alert('提示',
															'没有选择要清除的数据');
												}

											}
										});
					}
				}
			}
			/** 窗口尺寸调节* */
			contentPanel.on ('resize', function ()
					{
				mainPanel.setWidth(contentPanel.getWidth ());
				mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
					});
		});

// function ------------------------
/** 设为基线库* */
function setBaiscDBBtnHandel() {

	var data = dbSourceGrid.getSelectionModel().getSelection();
	if (data.length != 1) {
		Ext.Msg.alert('提示', '基线库选择一条且只能选择一条数据源用来设置成基线库');
		return;
	} else {
		Ext.Msg
				.confirm(
						"请确认",
						"是否真的要设置成基线库？",
						function(button, text) {
							if (button == "yes") {
								var serverId = data[0].get('idbsourceid');
								var ips = data[0].get('iserverip');
								if (ips == '') {
									Ext.Msg.alert('提示', '该数据源不可用，不能设置为基线');
									return;
								}
								Ext.Ajax
										.request({
											url : 'setBaicLineDbSrc.do',
											timeout : 60000,
											params : {
												idbsourceid : serverId
											},
											method : 'POST',
											success : function(response, opts) {
												var success = Ext
														.decode(response.responseText).success;
												if (success) {
													dbSourceStore.reload();
													Ext.Msg.alert('提示',
															'数据源设置成基线库成功');

												} else {
													var message = Ext
															.decode(response.responseText).message;
													Ext.Msg.alert('提示',
															'数据源设置成基线库失败,'
																	+ message);
												}
											}
										});
							}
						});
	}

}
function addSyncServer(idbsourceid) {
	var form = new Ext.FormPanel(
			{
				labelAlign : 'top',
				frame : true,
				bodyStyle : 'padding:5px 5px 0',
				layout : 'form',
				items : [ {
					xtype : 'textfield',
					fieldLabel : '用户名',
					name : 'ip',
					anchor : '100%'
				}],
				buttonAlign : 'center',
				buttons : [
						{
							text : '确定',
							handler : function() {
								var ip = form.getForm().findField('ip')
										.getValue();
								if ("" == ip || null == ip) {
									setMessage('用户名不能为空！');
									return;
								}
								this.setDisabled(true);
							}
						}, {
							text : '取消',
							handler : function() {
								win.close(this);
							}
						} ]
			});

	var win = new Ext.Window({
		title : '增加新的服务器',
		closeable : true,
		modal : true,
		width : 560,
		resizable : false,
		plain : true,
		layout : 'form',
		items : [ form ]
	});

	win.show();
}
// 以基线库为基准，同步数据
function sync(idbsourceid) {
	Ext.Msg
			.confirm(
					'提示',
					'是否要按照基线库进行同步数据？',
					function(btn) {
						if (btn == 'yes') {
							Ext.MessageBox.wait("数据处理中...", "进度条");
							Ext.Ajax
									.request({
										url : 'dbSrcSync.do',
										timeout : 60000,
										params : {
											idbsourceid : idbsourceid
										},
										method : 'POST',
										success : function(response, opts) {
											var success = Ext
													.decode(response.responseText).success;
											if (success) {
												dbSourceStore.reload();
												Ext.Msg.alert('提示', '数据同步成功');

											} else {
												var message = Ext
														.decode(response.responseText).message;
												Ext.Msg.alert('提示', '数据同步失败,'
														+ message);
											}
										}
									});
						}

						if (btn == 'no') {
							return;
						}

					}, this);
}

function setMessage(msg) {
	Ext.Msg.alert('提示', msg);
}
/**
 * @desc 增加被同步服务器
 * @param idbsourceid
 *            数据源id
 */
function addSyncServer(idbsourceid) {
	var form = new Ext.FormPanel(
			{
				labelAlign : 'top',
				frame : true,
				bodyStyle : 'padding:5px 5px 0',
				layout : 'form',
				items : [ {
					xtype : 'textfield',
					fieldLabel : '用户名',
					name : 'ip',
					anchor : '100%'
				},{
					xtype : 'textfield',
					fieldLabel : '密码',
					name : 'ip',
					anchor : '100%'
				} ],
				buttonAlign : 'center',
				buttons : [
						{
							text : '确定',
							handler : function() {
								var self = this;
								var ip = form.getForm().findField('ip')
										.getValue();
								if ("" == ip || null == ip) {
									setMessage('用户名不能为空！');
									return;
								}
								this.setDisabled(true);
							}
						}, {
							text : '取消',
							handler : function() {
								win.close(this);
							}
						} ]
			});

	var win = new Ext.Window({
		title : '增加新的服务器',
		closeable : true,
		modal : true,
		// modal为True表示为当window显示时对其后面的一切内容进行遮罩，
		// false表示为限制对其它UI元素的语法（默认为 false）。
		width : 560,
		resizable : false,
		plain : true,
		// Plain为True表示为渲染window body的背景为透明的背景，这样看来window body与边框元素（framing
		// elements）融为一体，
		// false表示为加入浅色的背景，使得在视觉上body元素与外围边框清晰地分辨出来（默认为false）。
		layout : 'form',
		items : [ form ]
	});

	win.show();
}

//选择一条记录，点击同步详细信息按钮触发该方法。
function validateDataSource(){
	var data = dbSourceGrid.getSelectionModel().getSelection();
	if (data.length != 1) {
		Ext.Msg.alert('提示', '请选择一条记录查看同步详细信息');
		return;
	}
	var idbsourcename = data[0].get('idbsourcename');
	var igroupmessgeid  = data[0].get('igroupmessgeid');
	var iisbasic  = data[0].get('iisbasic');
	var dburl=data[0].get('idburl');
	if("1"==iisbasic||baseDBSURL==dburl){
		Ext.Msg.alert('提示', '选择的数据源为基线数据源，不能同步基础数据，请重新选择！');
		return;
	}
	//如果数据源可用，即可查看同步的详细信息
	Ext.Ajax.request({
		url : 'checkDataSource.do',
		params:{syncDBName:idbsourcename,dbType:igroupmessgeid,baseType:baseDBSType,baseDBName:baseDBName},
		method : 'POST',
		success : function(response, opts) {
			var success = Ext.decode(response.responseText).success;
			if (success) {
				showDataSyncWindow ();
			} else {
				var message = Ext.decode(response.responseText).message;
				Ext.Msg.alert('提示', '查看同步详细信息失败,'+ message);
			}
		}
	});
}
var dataSyncWindow;
function showDataSyncWindow ()
{
	var data = dbSourceGrid.getSelectionModel().getSelection();
	if (data.length != 1) {
		Ext.Msg.alert('提示', '请选择一条记录查看同步详细信息');
		return;
	}
	var idbsourcename = data[0].get('idbsourcename');
	var igroupmessgeid  = data[0].get('igroupmessgeid');
	var iisbasic  = data[0].get('iisbasic');
	var idburl = data[0].get('idburl');
	var dbuser = data[0].get('idbuser');
	if("1"==iisbasic){
		Ext.Msg.alert('提示', '选择的数据源为基线数据源，不能同步基础数据，请重新选择！');
		return;
	}
	if (dataSyncWindow == undefined || !dataSyncWindow.isVisible ())
	{
		dataSyncWindow = Ext.create ('Ext.window.Window',
		{
		    title : '同步基础数据详细信息',
		    modal : true,
		    closeAction : 'destroy',
		    constrain : true,
		    autoScroll : true,
		    width : contentPanel.getWidth (),
		    height : contentPanel.getHeight (),
		    draggable : false,// 禁止拖动
		    resizable : false,// 禁止缩放
		    layout : 'fit',
		    loader :
		    {
		        url : 'dataSyncLogInfo.do',
		        params:{syncDBName:idbsourcename,dbType:igroupmessgeid,baseType:baseDBSType,baseDBName:baseDBName,idburl:idburl,dbuser:dbuser},
		        autoLoad : true,
		        scripts : true,
		        autoDestroy : true
		    }
		})
	}
	dataSyncWindow.show ();
}

//同步基础数据按钮事件
function synDBDataBtnHandel(){
	var checkRecord = dbSourceGrid.getSelectionModel().getSelection();
	if (checkRecord.length == 0) {
		Ext.Msg.alert('提示', '请选择要同步的数据源');
		return;
	}
	var key="";
	var dbtype1="";
	var jsonData = [];
	for (var i = 0; i < checkRecord.length; i++) {
		var ss = checkRecord[i].get('igroupmessgeid');
		var dburl = checkRecord[i].get('idburl');
		var iisbasic  = checkRecord[i].get('iisbasic');
		var idbuser = checkRecord[i].get('idbuser');
		var isSync = checkRecord[i].get('isSync');
		if("1"==iisbasic||baseDBSURL==dburl){
			Ext.Msg.alert('提示', '您选择的数据源中存在基线数据源，请重现选择！');
			return;
		}
		if(isSync!=null&&""!=isSync){
			Ext.Msg.alert('提示', '您选择的数据源中正在进行同步，请重现选择！');
			return;
		}
		jsonData.push({'dbtype':ss,'dburl':dburl,'idbuser':idbuser});
	}
	var result=[];
	for(var i=0;i<jsonData.length;i++){
		var isRepeated=false;
		var key =jsonData[i].dburl+jsonData[i].idbuser;
		for(var j=0;j<result.length;j++){
			var key1=result[j].dburl+result[j].idbuser;
			if(key==key1){
				isRepeated = true;
                break;
			}
		}
		if (!isRepeated) {
            result.push(jsonData[i]);
        }
	}
	//循环拿到所有选择的待同步数据源类型
	var dbTypes="[";
	for(var i=0;i<result.length;i++){
		if(i==0){
//			dbTypes=dbTypes+result[0].dbtype;
			dbTypes=dbTypes+"{"+"\"dbType\":\""+result[i].dbtype+"\",\"idburl\":\""+result[i].dburl+"\",\"dbuser\":\""+result[i].idbuser+"\"}"
		}
		else{
//			dbTypes=dbTypes+","+result[i].dbtype
			dbTypes=dbTypes+",{"+"\"dbType\":\""+result[i].dbtype+"\",\"idburl\":\""+result[i].dburl+"\",\"dbuser\":\""+result[i].idbuser+"\"}"
		}
		
		
	}
	dbTypes=dbTypes+"]";
	
	startSynDBData(dbTypes);
	
}
//查询是否正在同步
function startSynDBData(dbTypes){
	Ext.Ajax.request({
		url : 'batchDataSync.do',
		params:{
			"jsonData":dbTypes,
			"baseType":baseDBSType,
			"baseDBName":baseDBName
		},
		method : 'POST',
		success : function(response, opts) {
			var success = Ext.decode(response.responseText).success;
			if (success) {
				Ext.Msg.alert('提示', '开始同步基础数据');
//				showMessageWindow ();
			} else {
				var message = Ext.decode(response.responseText).message;
				Ext.Msg.alert('提示', '同步基础数据失败,'+ message);
			}
		}
	});
}
