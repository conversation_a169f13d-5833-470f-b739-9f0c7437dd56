Ext.onReady(function() {
	var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
	//1.通知组用户
	var groupUsers = new Ext.form.field.ComboBox (
	{
		name : 'groupUsers',
		fieldLabel : '通知组及用户',
		displayField: 'text',
	    valueField: 'value',
	    emptyText: '--请选择用户--',
	    labelWidth : 70,
	    padding : '5 5 10 5',
	    queryMode: 'local',
	    afterLabelTextTpl: required,
	    columnWidth: .5,
	    editable: false,
	    width : contentPanel.getWidth () - 20,
	    store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '用户1'], ['2', '用户2'], ['3', '用户3'], ['4', '用户4'], ['5', '业务系统5']]
        })
	});
	//2.协议
	var protocol = new Ext.form.field.ComboBox (
	{
		name : 'protocol',
		fieldLabel : '协议',
		displayField: 'text',
	    valueField: 'value',
	    emptyText: '--请选择协议--',
	    labelWidth : 70,
	    padding : '5 5 10 5',
	    queryMode: 'local',
	    afterLabelTextTpl: required,
	    columnWidth: .5,
	    editable: false,
	    width : contentPanel.getWidth () - 20,
	    store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', 'SMTP'], ['2', 'POP'], ['3', 'MIME']]
        })
	});
	//3.服务器地址
	var url = new Ext.form.TextField (
	{
		name : 'url',
		fieldLabel : '服务器地址',
		displayField : 'url',
	    emptyText : '',
	    labelWidth : 70,
	    padding : '5 5 10 5',
	    width : contentPanel.getWidth () - 20
	});
	//4.服务器端口
	var port = new Ext.form.TextField (
	{
		name : 'port',
		fieldLabel : '服务器端口',
		displayField : 'port',
	    emptyText : '',
	    labelWidth : 70,
	    padding : '5 5 10 5',
	    width : contentPanel.getWidth () - 20
	});
	//5.是否认证
	var auth = new Ext.form.field.ComboBox (
			{
				name : 'auth',
				fieldLabel : '是否认证',
				displayField: 'text',
			    valueField: 'value',
			    emptyText: '--请选择--',
			    labelWidth : 70,
			    padding : '5 5 10 5',
			    queryMode: 'local',
			    afterLabelTextTpl: required,
			    columnWidth: .5,
			    editable: false,
			    width : contentPanel.getWidth () - 20,
			    store: new Ext.data.SimpleStore({
		            fields: ['value', 'text'],
		            data: [['1', '是'], ['2', '否']]
		        })
			});
	//6.ssl认证
	var sslauth = new Ext.form.field.ComboBox (
			{
				name : 'sslauth',
				fieldLabel : '是否ssl认证',
				displayField: 'text',
			    valueField: 'value',
			    emptyText: '--请选择--',
			    labelWidth : 70,
			    padding : '5 5 10 5',
			    queryMode: 'local',
			    afterLabelTextTpl: required,
			    columnWidth: .5,
			    editable: false,
			    width : contentPanel.getWidth () - 20,
			    store: new Ext.data.SimpleStore({
		            fields: ['value', 'text'],
		            data: [['1', '是'], ['2', '否']]
		        })
			});
	/** 提交按钮* */
	var submitButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : "提交",
	    handler : submitFunction
	});
	var cancel = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    textAlign : 'center',
			    text : "取消",
			    handler : submitFunction
			});
	function submitFunction(){
	}
	var form = new Ext.form.FormPanel (
			{
				region: 'north',
				layout : 'anchor',
				bodyPadding : 5,
				border:false,
			    defaultType : 'textfield',
			    defaults :
			    {
			    	anchor : '100%'
			    },
			    items : [
			            groupUsers, protocol,url, port,auth,sslauth
			    ],
			    dockedItems : [
			       		    {
			       		        dock : 'bottom',
			       		        xtype : 'toolbar',
			       		        items : [
			       		                 {
			       		                	 xtype: 'tbseparator'
			       		 		        },	       
			       		 		        {
			       		 		            xtype: 'tbfill'
			       		 		        },submitButton,cancel
			       		        ]
			       		    }
			       	    ]
			});	
	rightPanel = Ext.create ("Ext.panel.Panel",
			{
			    width : contentPanel.getWidth (),
			    height : contentPanel.getHeight () - modelHeigth,
			    layout : 'border',
			    border : false,
			    items : [form],
			    renderTo : "resource_apply"
			});
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		rightPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		rightPanel.setWidth (contentPanel.getWidth () );
	});
	//当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy (rightPanel);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
    });
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
});

