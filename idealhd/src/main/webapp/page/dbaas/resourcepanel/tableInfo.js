Ext.onReady(function() {
//清理主面板的各种监听时间
  destroyRubbish();
  Ext.tip.QuickTipManager.init();
//			------------------------左侧面板开始-----------------------
  
 
  Ext.define('listData', {
      extend: 'Ext.data.Model',
      fields: [
		    {name : 'name',type : 'string'},
		    {name : 'type',type : 'string'},
		    {name : 'data_length',type : 'string'},
		    {name : 'nullable',type : 'string'}
      ]
   });
	
	 var right_store = Ext.create('Ext.data.Store', {
	        autoLoad: true,
	        autoDestroy : true,
	        model: 'listData',
	        proxy: {
	            type: 'ajax',
	            url: 'viewDetailForDateType.do',
	            reader: {
	                type: 'json',
	                root: 'dataList'
	            }
	        }
	    });
	 right_store.on ('beforeload', function (store, options)
				{
					var new_params =
					{
							name:name,
							type:type,
							iResId : iResId,
							flag:obhdbaas_resource_flag
					};
					Ext.apply (right_store.proxy.extraParams, new_params);
				});
	 var  right_columns = [{
			text : '序号',
			xtype : 'rownumberer',
			width:65
		}, {
			text : '列名',
			dataIndex : 'name',
			flex:1
		}, {
			text : '类型',
			dataIndex : 'type',
			flex:1
		}, {
			text : '非空',
			dataIndex : 'nullable',
			flex:1,
			renderer:function(value, p, record) {
				if(value=='Y'||value=='YES'){
					return "否";
				}
				if(value=='N'||value=='NO'){
					return "是";
				}
			}
		}];
  

	 var grid= Ext.create('Ext.grid.Panel', {
	        store:right_store,
	        padding : panel_margin,
	        border:true,
	        columnLines : true,
	        height : contentPanel.getHeight()-modelHeigth,
	        columns:right_columns
	    });
	 
	  var mainPanel = Ext.create('Ext.panel.Panel',{
	    renderTo : "table_area",
	    border : true,
	    layout : 'fit',
	  	items : [grid]
	  });
	    contentPanel.on('resize',function(){     
	        mainPanel.setHeight ("100%");
	        mainPanel.setWidth ("100%");
	    });
});	 
