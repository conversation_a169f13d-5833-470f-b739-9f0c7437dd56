<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>结果展示<%=request.getAttribute("iid") %></title>
<script type="text/javascript" src="js/layui/layui.js"></script>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript" src="js/jquery.nicescroll.js"></script>
<link rel="stylesheet" href="css/analysisstyle_new.css">
<link rel="stylesheet" href="js/layui/css/layui.css" />
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dbaas/report/echarts.min.js"></script>
<%
    String iid = request.getParameter("iid");
%>
<script type="text/javascript">
var iid="<%=iid%>";
var keyname = new Array();
var keydata = new Array();
/* $(function(){
		$("#tab").html('数据加载中,请稍后.....');
		$.ajax({
			type : "post",
			//url : "getTemplateResultDetail.do",
			url : "gotoResultCj.do",
			data :{
			   tmpid : iid
			},
			dataType : "json",
			success : function(data){ 
				$("#contains_new").html('');
				$("#tab").html('');
				var tables = "";
				$.each(data,function(i,resource){
					$.each(resource, function(index, item) {
						if(index=='tabs'){
							$("#tab").append(item);
						}
						if(index=='bodys'){
						    tables=tables+item;
					    }
					});
				});
				$("#contains_new").append(tables);
				$("#tab1").attr('style','font-weight:bold;text-decoration:underline');
				$.each(data, function(i, resource) {
					$.each(resource, function(index, item) {
						if(index=='datas'&&item!=''){
							var arrs=item.split(";;;");
							for(var i=0;i<arrs.length;i++){
								var arr=arrs[i].split(","); 
								var a1=arr[0].substring(1,arr[0].length-1);
								var a2=arr[1].substring(1,arr[1].length-1);
								var a3=arr[2].substring(1,arr[2].length-1);
								var a4=arr[3].substring(1,arr[3].length-1);
								var a5=arr[4].substring(1,arr[4].length-1);
								var a6=arr[5].substring(1,arr[5].length-1);
								var a7=arr[6].substring(1,arr[6].length-1);
								var a8=arr[7].substring(1,arr[7].length-1);
								getChart(a1,a2,a3,a4,a5,a6,a7,a8);
							}
						}
					});
				});	
				
			}
		});
		$(".table_chart").niceScroll({
			autohidemode : true,
			cursorcolor : "#c6cbd6",
			cursorborder : "1px solid #c6cbd6"
		}); 
		$(".table_chart_shell").niceScroll({
			autohidemode : true,
			cursorcolor : "#c6cbd6",
			cursorborder : "1px solid #c6cbd6"
		}); 	
});	 */
	 function changeTab(i){
		$("#tab"+i).attr('style','font-weight:bold;text-decoration:underline').siblings("li").removeAttr("style");
		$("#content"+i).attr('style','z-index:1').siblings("div").removeAttr("style");
	}
	//起始时间,结束时间，区间运行
	function getResultSplitDate(splitsdate, splitedate, splitdata,serviceid) {
	 	var sdatadate = $("#" + splitsdate).val();
		var edatadate = $("#" + splitedate).val();
		if(sdatadate != null && sdatadate != '' && edatadate != null && edatadate != ''){
			var url_lo = 'getResultSplitDate.do?sdate=' + sdatadate + '&edate='+ edatadate + '&serviceid=\'' + serviceid+'\'';
			$.get(url_lo,
					function(data) {
			       var length = data.length;
                   var html2 = '<option value="" ></option>';
                   for (var i = 0; i < length; i++) {
                       html2 += '<option value="' + data[i][0] + '">' + data[i][1] + '</option>';
                   }
                   $("#"+splitdata).html(html2);
					})
		} 
	}
	
	//表格折线图
	 function getChart(tableName, sdate, edate,tableId, serviceid, sqlid,splitdate,resId) {
		//console.log(tableName, sdate, edate,tableId, serviceid, sqlid,splitdate);
		var table = $("#" + tableName).text();
		var sdatadate = $("#" + sdate).val();
		var edatadate = $("#" + edate).val();
		var splitdate = $("#" + splitdate).val();
		var sqls = $("#" + sqlid).val();
		table = table.split(":")[1];
	    var div = "#table_chart" + "_" + tableId+"_" +serviceid ;
        $(div).html("数据加载中，请稍后......");
		$.ajax({
			type : "post",
			url : "getResultGraphByTable.do",
			data : {
				tmpId : tmpId,
				sdatadate : sdatadate,
				edatadate : edatadate,
				sqls:sqls,
				serviceid:serviceid,
				splitdate:splitdate,
				chartid:tableId,
				resId:resId
			},
			dataType : "json",
			success : function(data) {
					//每个服务的表格
					var table = data.bodys;
					var divid = "chart_" + tableId + "_"+ serviceid;
					keyname=data.keyname;
					keydata=data.keydata;
					var keyname_pie= new Array();
					$.each(keyname,function (index2, item2){
						keyname_pie.push(item2.trim());
					})
					var divs = document.getElementById(divid).style.display;
					if (data.chartType == '') {
						document.getElementById(divid).style.display = 'none';
					} else if (divs == 'none') {
						document.getElementById(divid).style.display = '';
					}
	             
	                $(div).html(data.bodys);
					
					if (data.chartType == 2) {
						nameList=data.nameList;
						var overTimeChart = echarts.init(document.getElementById(divid));
						var option = {
							    legend: {
							        x: 'right',
							        data:nameList
							    },
							   tooltip: {
							        trigger: 'axis'
							      
							    },
							xAxis : {
								type : 'category',
								data : keyname
							},
							yAxis : {
								type : 'value'
							},
							series : keydata,
							color:['#5168fc','#1741bc','#bc42f5','#7248de','#00a332','#fdc03e','#ff992b','#fd703e','#00c679','#6095f7'], 
							 grid : {
									left : '0%',
									right : '5%',
									bottom : '0%',
									top : '50',
									containLabel : true
								}
						};
						overTimeChart.setOption(option, true);
					} else if (data.chartType == 3) {
						var overTimeChart = echarts.init(document.getElementById(divid));
						option = {
							    legend: {
							        data:keydata
							    },
								 xAxis: {
								        type: 'category',
								        data: keyname
								    },
								    tooltip:{
								    	trigger:'axis'
								    },
								    yAxis: {
								        type: 'value'
								    },
							      series :keydata,
							      color:['#5168fc','#1741bc','#bc42f5','#7248de','#00a332','#fdc03e','#ff992b','#fd703e','#00c679','#6095f7'], 
							      grid : {
										left : '0%',
										right : '5%',
										bottom : '0%',
										top : '50',
										containLabel : true
									}
						};
						overTimeChart.setOption(option, true);
					} else if (data.chartType == 1) {
						var overTimeChart = echarts.init(document.getElementById(divid));
						option = {
								tooltip:{
									trigger:'item',
									formatter:"{a} <br/> {b} :{c} ({d}%)"
								},
							    legend: {
							    	  orient:'vertical',
							    	  x:'left',
							    	  data: keyname_pie
							    },
							    series : [
							        {
							            type: 'pie',
							            radius : '85%',
							            center: ['35%', '55%'],
							            data:keydata
							        }
							    ],
							    color:['#5168fc','#1741bc','#bc42f5','#7248de','#00a332','#fdc03e','#ff992b','#fd703e','#00c679','#6095f7'], 
							      grid : {
										left : '0%',
										right : '5%',
										bottom : '0%',
										top : '50',
										containLabel : true
									},
							};
						overTimeChart.setOption(option, true);
					}

 				 $(".table_chart").niceScroll({
					autohidemode : true,
					cursorcolor : "#c6cbd6",
					cursorborder : "1px solid #c6cbd6"
				}); 
 				 $(".table_chart_shell").niceScroll({
					autohidemode : true,
					cursorcolor : "#c6cbd6",
					cursorborder : "1px solid #c6cbd6"
				}); 
			}
		});
	}
	 
	function exportChart(cols,tablename,serviceId,sqlWhere,sqlsplit){
		var sql = "select ISTARTTIME_BULID_IN as 执行时间," + cols + " from "
        + tablename + " A  where  ISERVICEID_BULID_IN='" + serviceId + "' "
        + sqlWhere + sqlsplit + " order by iid desc";
		cols="执行时间,"+cols;
		//alert(sql);
		window.location.href = 'exportResultServiceDetail.do?tableTitle='+tablename+"&cols="+cols+"&sql="+sql;
	}
	function exportChart2(cols,serviceName,serviceId,start,end){
		var sql="select ins.iagentip,ins.iagentport,ins.istarttime,ins.iendtime,ins.istdout,ins.istderr from IEAI_SCRIPT_instance ins WHERE ins.imainid IN (select c.iid from IEAI_SCRIPTS_COAT c WHERE c.iflowid=(select MAX(t.iid) from IEAI_SCRIPT_flow t WHERE t.imxserviceid="+serviceId
			+" AND t.istatus=20)) and ins.istarttime >=" + start
            + " and ins.istarttime <=" + end;
		window.location.href = 'exportResultServiceDetail.do?tableTitle='+serviceName+"&cols="+cols+"&sql="+sql;
	}
 
	
</script>
<style type="text/css">
*{
margin:0;
padding: 0;
}
#tab li{
float: left;
list-style: none;
width: 240px;
height: 40px;
line-height: 40px;
cursor: pointer;
text-align: center;
background-color: #f7f8fa;
}
#contains_new{
position: relative;
}
.cont{
background-color: #f7f8fa;
padding:30px;
position: absolute;
top: 40px;
left: 0;
}

</style>
</head>
<body>
	<ul id="tab">
	</ul>
	<div id="contains_new">
	</div>
</body>
</html>


