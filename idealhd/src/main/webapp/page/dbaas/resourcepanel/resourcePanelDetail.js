Ext.onReady(function() {
//清理主面板的各种监听时间
  destroyRubbish();
  Ext.tip.QuickTipManager.init();
  Ext.Ajax.timeout=100000; 
  function showTip(data, metadata, record_start, rowindex, columnindex, store) {
      var rowdata=record_start.data;
      if(rowdata != ''){ // 此处对空白文本做过滤。
          var ss='';
          for(var k in rowdata) {
              ss=ss+k+" :  "+ rowdata[k].replace(/'/g,"\"") + "<br>";
          }
          metadata.tdAttr = "data-qtip='" + ss + "'";
      }
      return data;
  }
  function showTipChart(klass, item) {
		var data = item.storeItem.data;
		if(data != ''){ 
	          var ss='';
	          for(var k in data) {
	              ss=ss+k+" :  "+ data[k].replace(/'/g,"\"") + "<br/>";
	          }
	      }
		 this.setTitle(ss);
	}
  
///////////////// 会话TOP5 /////////////////////////////
	  Ext.define('sessionModel', {
			extend : 'Ext.data.Model',
			fields : [{name : 'tablespace',type : 'string'},
				{name: 'mb',type: 'string'},
				{name : 'free_mb',type : 'string'},
				{name : 'sid',type : 'string'},
				{name : 'serial#',type : 'string'},
				{name : 'username',type : 'string'},
				{name : 'status',type : 'string'},
				{name : 'osuser',type : 'string'},
				{name : 'machine',type : 'string'},
				{name : 'program',type : 'string'},
				{name : 'action',type : 'string'},
				{name : 'sql_text',type : 'string'}]
		});
		var sessionStore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			pageSize : 25,
			model : 'sessionModel',
			proxy : {
				type : 'ajax',
				url : 'resourcePanelGetSession.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			}
		});
		sessionStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(sessionStore.proxy.extraParams, new_params);
		});
		var sessionColumns = [
				{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
				{
			        text: '操作',
			        dataIndex: 'Operation',
			        width: 40,
			        renderer: function(value, p, record, rowIndex) {
			        	var sid = record.get('sid');
			        	var serial = record.get('serial#');
			        	var gridname = 'sessionGrid'
			            return '<div>' + '<a href="javascript:void(0)" onclick="killSession(\''+sid+'\',\''+serial+'\',\''+gridname+'\')">' + ' KILL </a></div>';			        
			        	}
			    },
				{text : '表空间',dataIndex : 'tablespace',width : 120,renderer:showTip},
				{text : 'mb',dataIndex : 'mb',width : 60,renderer:showTip},
				{text : 'free_mb',dataIndex : 'free_mb',width : 70,renderer:showTip},
				{text : '会话ID',dataIndex : 'sid',width : 70,renderer:showTip},
				{text : '序列号',dataIndex : 'serial#',width : 70,renderer:showTip},
				{text : '用户名',dataIndex : 'username',width : 100,renderer:showTip},
				{text : '状态',dataIndex : 'status',width : 100,renderer:showTip},
				{text : '操作系统用户',dataIndex : 'osuser',width : 100,renderer:showTip},
				{text : '机器',dataIndex : 'machine',width : 100},
				{text : '程序',dataIndex : 'program',width : 100},
				{text : '动作',dataIndex : 'action',width : 100},
				{text : 'SQL',dataIndex : 'sql_text',width : 100}];
		var sessionGrid = Ext.create('Ext.grid.Panel', {
				id : 'sessionGrid',
				region : 'center',
				store : sessionStore,
				padding : panel_margin,
				border : true,
				columnLines : true,
				cls: 'customize_panel_back',
				columns : sessionColumns,
				listeners:{
					celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
			    		var map=record.data;
			    		var ss='';
			    	    for(var k in map) {
			    	    	ss=ss+k+' : '+map[k]+ '\n';
			    	    }
			    		showWindow(ss);		        	
		    	    }
				}	
			});
  var gridPanel1 = Ext.create('Ext.panel.Panel',{
		title:'会话 TOP5',
	  	margin:'5 0 0 0',
	  	region:'north',
	  	height:'33%',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	    split: true,
	  	items : [sessionGrid]
	  });
 /////////////////////////////////////////////////////////////////////////
  
  /////////////////////////// 锁 TOP10 ////////////////////////////////////
	Ext.define('lockStateModel', {
		extend : 'Ext.data.Model',
		fields : [{name : 'sid',type : 'string'},
			{name: 'serial',type: 'string'},
			{name : 'locked_mode',type : 'string'},
			{name : 'oracle_username',type : 'string'},
			{name : 'user',type : 'string'},
			{name : 'os_user_name',type : 'string'},
			{name : 'machine',type : 'string'},
			{name : 'terminal',type : 'string'},
			{name : 'sql_text',type : 'string'},
			{name : 'sql_id',type : 'string'},
			{name : 'action',type : 'string'}]
	});
	  var lockStateStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'lockStateModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetLockState.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	  lockStateStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(lockStateStore.proxy.extraParams, new_params);
		});
	  var lockStateColumns = [
			{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
			{
		        text: '操作',
		        dataIndex: 'Operation',
		        width: 40,
		        renderer: function(value, p, record, rowIndex) {
		        	var sid = record.get('sid');
		        	var serial = record.get('serial');
		        	var gridname = 'lockStateGrid';
		            return '<div>' + '<a href="javascript:void(0)" onclick="killSession(\''+sid+'\',\''+serial+'\',\''+gridname+'\')">' + ' KILL </a></div>';			        }
		    },
			{text : '会话id',dataIndex : 'sid',width : 60,renderer:showTip},
			{text : '序列号',dataIndex : 'serial',width : 60,renderer:showTip},
			{
				text : '锁模式',
				dataIndex : 'locked_mode',
				width : 80,
				renderer : function(value, p, record, rowIndex) {
					if(value=='0')return"NONE";
					else if(value=='1')return"空";
					else if(value=='2')return"行共享";
					else if(value=='3')return"行独占";
					else if(value=='4')return"共享锁";
					else if(value=='5')return"共享行独占";
					else if(value=='6')return"独占";
					else return value;
				}
			},
			{text : '数据库用户名',dataIndex : 'oracle_username',width : 60,renderer:showTip},
			{text : '用户id',dataIndex : 'user',width : 40,hidden : true},
			{text : '操作系统用户名',dataIndex : 'os_user_name',width : 60,renderer:showTip},
			{text : '机器',dataIndex : 'machine',width : 60,renderer:showTip},
			{text : '终端',dataIndex : 'terminal',width : 60,renderer:showTip},
			{text : 'sql内容',dataIndex : 'sql_text',width : 60, 
			    renderer: function(value, p, record, rowIndex) {
                    var sql_id = record.get('sql_id');
                    if(sql_id!=null && sql_id!='') {
                        return '<div>' + '<a href="javascript:void(0)" onclick="queryplan(\''+sql_id+'\')">' +value +'</a></div>';
                    }else {
                        return value;
                    }
                }
			},
			{text : 'sql_id',dataIndex : 'sql_id',width : 60,hidden : true},
			{text : '活动',dataIndex : 'action',width : 60}];
	  var lockStateGrid = Ext.create('Ext.grid.Panel', {
			id : 'lockStateGrid',
			region : 'center',
			store : lockStateStore,
			padding : panel_margin,
			border : true,
			columnLines : true,
			cls: 'customize_panel_back',
			columns : lockStateColumns,
			listeners:{
				celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
		    		var map=record.data;
		    		var ss='';
		    	    for(var k in map) {
		    	    	ss=ss+k+' : '+map[k]+ '\n';
		    	    }
		    		showWindow(ss);		        	
	    	    }
			}			
		});	
  var gridPanel2 = Ext.create('Ext.panel.Panel',{
		title:'锁 TOP10',
	  	margin:'0 0 0 0',
	  	region:'center',
	  	height:'33%',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	    bodyCls:'service_platform_bodybg',
	  	items : [lockStateGrid]
	  });
  ///////////////////////////////////////////////////////////////////////////
  
  /////////////////////////////////  执行计划       ////////////////////////////////
  var gridPanel3 = Ext.create('Ext.panel.Panel',{
		title:'表格3',
	  	margin:'5 0 0 0',
	  	flex:1,
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	    html:'<table><tr><td>111</td><td>222</td></tr><tr><td>333</td><td>444</td></tr></table>',
	  	items : []
	  });
  //////////////////////////////////////////////////////////////////////////
  
  ///////////////////// 进程历史TOP10 ///////////////////////////////////
  Ext.define('processHistoryModel', {
		extend : 'Ext.data.Model',
		fields : [{name : 'sql_id',type : 'string'},
			{name: 'application_wait_time',type: 'string'},
			{name : 'user_io_wait_time',type : 'string'},
			{name : 'cpu_time',type : 'string'},
			{name : 'elapsed_time',type : 'string'}]
	});
	var processHistoryStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'processHistoryModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetProcessHistory.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	processHistoryStore.on('beforeload', function(store, options) {
	     var new_params = {iresid:IRESID};
	        Ext.apply(processHistoryStore.proxy.extraParams, new_params);
	});
	var processHistoryColumns = [
			{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
			{text : 'sql_id',dataIndex : 'sql_id',width : 120,flex:1,renderer:showTip},
			{text : 'appwaittime',dataIndex : 'application_wait_time',width : 100,renderer:showTip},
			{text : 'iowaittime',dataIndex : 'user_io_wait_time',width : 100,renderer:showTip},
			{text : 'cputime',dataIndex : 'cpu_time',width : 100,renderer:showTip},
			{text : 'elapsedtime',dataIndex : 'elapsed_time',width : 100,renderer:showTip}];
	var processHistoryGrid = Ext.create('Ext.grid.Panel', {
			region : 'center',
			store : processHistoryStore,
			padding : panel_margin,
			border : true,
			columnLines : true,
			cls: 'customize_panel_back',
			columns : processHistoryColumns,
			listeners:{
				celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
		    		var map=record.data;
		    		var ss='';
		    	    for(var k in map) {
		    	    	ss=ss+k+' : '+map[k]+ '\n';
		    	    }
		    		showWindow(ss);		        	
	    	    }
			}		
		});
  var gridPanel4 = Ext.create('Ext.panel.Panel',{
		title:'执行历史',
	  	margin:'5 0 0 0',
	  	region:'south',
	  	height:'33%',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	    bodyCls:'service_platform_bodybg',
	  	items : [processHistoryGrid]
	  });
  ////////////////////////////////////////////////////////
  
  ////////////// 左侧面板组合 ///////////////////////////////
  var leftPanel = Ext.create('Ext.panel.Panel',{
	width: '35%',
  	height:'100%',
  	region:'west',
  	margin:'5 0 5 5',
    bodyPadding : grid_margin,
    cls:'customize_panel_back',
    bodyCls:'service_platform_bodybg',
    layout: 'border',
  	items : [gridPanel1,gridPanel2,/*gridPanel3,*/gridPanel4]
  });
 ///////////////////////////////////////////////////////////////////////////////
  
  /////////////////////////////// top sql ////////////////////////////////////
		  Ext.define('topSqlModel', {
				extend : 'Ext.data.Model',
				fields : [{name : 'owner',type : 'string'},
					{name: 'sql_text',type: 'string'},
					{name : 'sql_full_text',type : 'string'},
					{name : 'sql_id',type : 'string'},
					{name : 'phv',type : 'string'},
					{name : 'execs',type : 'string'},
					{name : 'avg_ela',type : 'string'},
					{name : 'avg_lio',type : 'string'},
					{name : 'avg_pio',type : 'string'},
					{name : 'avg_cpu',type : 'string'}]
			});
		var topSqlStore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			pageSize : 25,
			model : 'topSqlModel',
			proxy : {
				type : 'ajax',
				url : 'resourcePanelGetTopSql.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			}
		});
		topSqlStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(topSqlStore.proxy.extraParams, new_params);
		});
		var topSqlColumns = [
				{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
				{text : '用户',dataIndex : 'owner',width : 100,renderer:showTip},
				{text : 'sql',dataIndex : 'sql_text',width : 120,renderer:showTip},
				{text : 'sql内容',dataIndex : 'sql_full_text',width : 200,hidden : true},
				{text : 'sql_id',dataIndex : 'sql_id',width : 100, renderer: function(value, p, record, rowIndex) {
                    var sql_id = record.get('sql_id');
                    if(sql_id!=null && sql_id!='') {
                        return '<div>' + '<a href="javascript:void(0)" onclick="queryplan(\''+sql_id+'\')">' +value +'</a></div>';
                    }else {
                        return value;
                    }
                }},
				{text : '执行次数',dataIndex : 'execs',width : 80,renderer:showTip},
				{text : 'phv',dataIndex : 'phv',width : 100,renderer:showTip},
				{text : 'avg_ela',dataIndex : 'avg_ela',width : 100},
				{text : 'avg_lio',dataIndex : 'avg_lio',width : 100},
				{text : 'avg_pio',dataIndex : 'avg_pio',width : 100},
				{text : 'avg_cpu',dataIndex : 'avg_cpu',width : 100}];
		var topSqlGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : topSqlStore,
				padding : panel_margin,
				border : true,
				columnLines : true,
				cls: 'customize_panel_back',
				columns : topSqlColumns,
				listeners:{
					celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
			    		var map=record.data;
			    		var ss='';
			    	    for(var k in map) {
			    	    	ss=ss+k+' : '+map[k]+ '\n';
			    	    }
			    		showWindow(ss);		        	
		    	    }
				}		
			});
  var topSqlPanel = Ext.create('Ext.panel.Panel',{
	  	id:'topSqlPanel',
		title:'<a href="javascript:void(0)" onclick="changePanel();">TOP SQL  +</a>',
	  	margin:'0 5 0 0',
		width:'50%',
		height:'100%',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	  	items : [topSqlGrid]
	  });
  ////////////////////////////////////////////////////////////////////////
  
/////////////////////////// top event /////////////////////////////////////  
	  Ext.define('topEventModel', {
			extend : 'Ext.data.Model',
			fields : [{name : 'event_name',type : 'string'},
				{name: 'waits',type: 'string'},
				{name : 'touts',type : 'string'},
				{name : 'waited',type : 'string'},
				{name : 'avg_waited',type : 'string'},
				{name : 'waits_pct',type : 'string'},
				{name : 'touts_pct',type : 'string'},
				{name : 'waited_pct',type : 'string'}]
		});
	  var topEventStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'topEventModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetTopEvent.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	  topEventStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(topEventStore.proxy.extraParams, new_params);
		});
	  var topEventColumns = [
			{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
			{text : '事件名',dataIndex : 'event_name',width : 140,renderer:showTip},
			{text : '等待次数',dataIndex : 'waits',width : 80,renderer:showTip},
			{text : '超时次数',dataIndex : 'touts',width : 80,renderer:showTip},
			{text : '等待时间',dataIndex : 'waited',width : 80,renderer:showTip},
			{text : '平均等待时间',dataIndex : 'avg_waited',width : 80,renderer:showTip},
			{text : '等待率',dataIndex : 'waits_pct',width : 80,renderer:showTip},
			{text : '超时率',dataIndex : 'touts_pct',width : 60,renderer:showTip},
			{text : '等待时间率',dataIndex : 'waited_pct',width : 60,renderer:showTip}];
	  var topEventGrid = Ext.create('Ext.grid.Panel', {
			region : 'center',
			store : topEventStore,
			padding : panel_margin,
			border : true,
			columnLines : true,
			cls: 'customize_panel_back',
			columns : topEventColumns,
			listeners:{
				celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
		    		var map=record.data;
		    		var ss='';
		    	    for(var k in map) {
		    	    	ss=ss+k+' : '+map[k]+ '\n';
		    	    }
		    		showWindow(ss);		        	
	    	    }
			}		
		});
  var topEventPanel = Ext.create('Ext.panel.Panel',{
	  	id:'topEventPanel',
		title:'<a href="javascript:void(0)" onclick="changePanel();">TOP EVENT  +</a>',
		height:'100%',
		width:'50%',
		margin:'0 0 0 0',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	    bodyCls:'service_platform_bodybg',
	  	items : [topEventGrid]
	  });
  //////////////////////////////////////////////////////////////////////////
  
  /////////////////////////  使用频率最高的5个查询 ////////////////////////////
  Ext.define('topFrequenceModel', {
		extend : 'Ext.data.Model',
		fields : [{name : 'sql_text',type : 'string'},
			{name: 'executions',type: 'string'}]
	});
	var topFrequenceStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'topFrequenceModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetTopFrequence.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	topFrequenceStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(topFrequenceStore.proxy.extraParams, new_params);
		});
	var topFrequenceColumns = [
			{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
			{text : 'SQL',dataIndex : 'sql_text',flex : 1,renderer:showTip},
			{text : '执行次数',dataIndex : 'executions',width : 90,renderer:showTip}];
	var topFrequenceGrid = Ext.create('Ext.grid.Panel', {
			region : 'center',
			store : topFrequenceStore,
			padding : panel_margin,
			border : true,
			columnLines : true,
			cls: 'customize_panel_back',
			columns : topFrequenceColumns,
			listeners:{
				celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
		    		var map=record.data;
		    		var ss='';
		    	    for(var k in map) {
		    	    	ss=ss+k+' : '+map[k]+ '\n';
		    	    }
		    		showWindow(ss);		        	
	  	    }
			}		
		});
	var topFrequencePanel = Ext.create('Ext.panel.Panel',{
		id:'topFrequencePanel',
		title:'<a href="javascript:void(0)" onclick="changePanel();">使用频率TOP5  +</a>',
		height:'100%',
		width:'50%',
		margin:'5 0 0 0',
	  bodyPadding : grid_margin,
	  cls:'customize_panel_back',
	  layout : 'border',
	  bodyCls:'service_platform_bodybg',
		items : [topFrequenceGrid]
	});
  /////////////////////////////////////////////////////////////////////////////
  
  ////////////////////////  消耗磁盘读取最多的sql top5 ///////////////////////
	Ext.define('topConsumeModel', {
		extend : 'Ext.data.Model',
		fields : [{name : 'sql_text',type : 'string'},
			{name: 'disk_reads',type: 'string'}]
	});
	var topConsumeStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'topConsumeModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetTopConsume.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	topConsumeStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(topConsumeStore.proxy.extraParams, new_params);
		});
	var topConsumeColumns = [
			{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
			{text : 'SQL',dataIndex : 'sql_text',flex : 1,renderer:showTip},
			{text : '消耗磁盘',dataIndex : 'disk_reads',width : 90,renderer:showTip}];
	var topConsumeGrid = Ext.create('Ext.grid.Panel', {
			region : 'center',
			store : topConsumeStore,
			padding : panel_margin,
			border : true,
			columnLines : true,
			cls: 'customize_panel_back',
			columns : topConsumeColumns,
			listeners:{
				celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
		    		var map=record.data;
		    		var ss='';
		    	    for(var k in map) {
		    	    	ss=ss+k+' : '+map[k]+ '\n';
		    	    }
		    		showWindow(ss);		        	
	  	    }
			}		
		});
	var topConsumePanel = Ext.create('Ext.panel.Panel',{
		id:'topConsumePanel',
		title:'<a href="javascript:void(0)" onclick="changePanel();">磁盘读取Top5  +</a>',
		height:'100%',
		width:'50%',
		margin:'5 0 0 0',
	  bodyPadding : grid_margin,
	  cls:'customize_panel_back',
	  layout : 'border',
	  bodyCls:'service_platform_bodybg',
		items : [topConsumeGrid]
	});
////////////////////////////////////////////////////////////////////////////
  
  //////////////////////// 右上面板组合 /////////////////////////////////////
  var rightTopPanel = Ext.create('Ext.panel.Panel',{
	  	id:'rightTopPanel',
	  	margin:'5 0 0 0',
	  	region:'north',
	  	height:'33%',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : {
			type : 'hbox',
			align : 'stretch'
		},
		bodyCls:'service_platform_bodybg',
	  	items : [topSqlPanel,topEventPanel]
	  });
  ////////////////////////////////////////////////////////////////////////
  
  ////////////////////// CPU /////////////////////////////////////////////
  Ext.define('CPUModel', {
		extend : 'Ext.data.Model',
		fields : [{name : 'name',type : 'string'},
					{name: 'pctused',type: 'string'}]
	});
  var CPUStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'CPUModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetCpuForChart.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	CPUStore.on('beforeload', function(store, options) {
	   var new_params = {iresid:IRESID};
	      Ext.apply(CPUStore.proxy.extraParams, new_params);
	});
  var CPUChart = Ext.create('Ext.chart.Chart', {
		 region: 'center',
		 theme : 'CustomBlue',
	animate: true,
	shadow: false,
	store: CPUStore,
	padding : '15 10 -10 0',
	axes: [{
	   type: 'Numeric',
	   dashSize : 0.5,
	   position: 'left',
	   fields: ['pctused'],
	   grid: true,
	   majorTickSteps:5,
	   minimum: 0
	}, {
	   type: 'Category',
	   dashSize : 0.5,
	   position: 'bottom',
	   fields: ['name']
	}],
	series: [{
	   type: 'column',
	   style: { width: 20 },
	   xPadding : 40,
	   axis: 'left',
	   xField: 'name',
	   yField: 'pctused',
	   renderer: function(sprite, storeItem, barAttr, i, store) {    
	       barAttr.fill = '#3398DB';  
	   return barAttr;    
	} ,
		tips: {renderer:showTipChart}
	}]
	});
  var cpuPanel = Ext.create('Ext.panel.Panel',{
		title:'CPU',
	  	width:'25%',
	  	height:'100%',
	  	margin:'5 5 0 0',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
//	    html:'<p>NO<br/>CONTENT<br/>HERE</p>',
	  	items : [CPUChart]
	  });
  ///////////////////////////////////////////////////////////////////////
  
  ///////////////////////// 内存 ///////////////////////////////////////
  Ext.define('memoryModel', {
		extend : 'Ext.data.Model',
		fields : [{name : 'name',type : 'string'},
			{name: 'total',type: 'string'},
			{name: 'used',type: 'string'},
			{name: 'free',type: 'string'},
			{name: 'pctused',type: 'string'}]
	});
	var memoryStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'memoryModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetMemoryForChart.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	memoryStore.on('beforeload', function(store, options) {
	   var new_params = {iresid:IRESID};
	      Ext.apply(memoryStore.proxy.extraParams, new_params);
	});
	var memoryChart = Ext.create('Ext.chart.Chart', {
		 region: 'center',
		 theme : 'CustomBlue',
	animate: true,
	shadow: false,
	store: memoryStore,
	padding : '15 10 -10 0',
	axes: [{
	   type: 'Numeric',
	   dashSize : 0.5,
	   position: 'left',
	   fields: ['pctused'],
	   grid: true,
	   majorTickSteps:5,
	   minimum: 0
	}, {
	   type: 'Category',
	   dashSize : 0.5,
	   position: 'bottom',
	   fields: ['name']
	}],
	series: [{
	   type: 'column',
	   style: { width: 10 },
	   xPadding : 10,
	   axis: 'left',
	   xField: 'name',
	   yField: 'pctused',
	   renderer: function(sprite, storeItem, barAttr, i, store) {    
	       barAttr.fill = '#ff7596';  
	   return barAttr;    
	} ,
		tips: {renderer:showTipChart}
	}]
	});
  var memoryPanel = Ext.create('Ext.panel.Panel',{
		title:'内存',
	  	width:'25%',
	  	height:'100%',
	  	margin:'5 5 0 0',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	  	items : [memoryChart]
	  });
////////////////////////////////////////////////////////////////////////////
  
  /////////////////// ASM /////////////////////////////////////////
	  Ext.define('asmModel', {
			extend : 'Ext.data.Model',
			fields : [{name : 'dg_name',type : 'string'},
				{name: 'pre',type: 'string'}]
		});
	var asmStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'asmModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetAsmForChart.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	asmStore.on('beforeload', function(store, options) {
	     var new_params = {iresid:IRESID};
	        Ext.apply(asmStore.proxy.extraParams, new_params);
	});
  var asmChart = Ext.create('Ext.chart.Chart', {
		 region: 'center',
		 theme : 'CustomBlue',
     animate: true,
     shadow: false,
     store: asmStore,
     padding : '15 10 -10 0',
     axes: [{
         type: 'Numeric',
         dashSize : 0.5,
         position: 'left',
         fields: ['pre'],
         grid: true,
         majorTickSteps:5,
         minimum: 0
     }, {
         type: 'Category',
         dashSize : 0.5,
         position: 'bottom',
         fields: ['dg_name']
     }],
     series: [{
         type: 'column',
         style: { width: 20 },
         xPadding : 40,
         axis: 'left',
         xField: 'dg_name',
         yField: 'pre',
         renderer: function(sprite, storeItem, barAttr, i, store) {    
             barAttr.fill = '#f39801';  
         return barAttr;    
     },
		tips: {renderer:showTipChart}  
     }]
 });
  var asmPanel = Ext.create('Ext.panel.Panel',{
		title:'<a href="javascript:void(0)" onclick="showAsmCHarts();">ASM+</a>',
	  	width:'25%',
	  	height:'100%',
	  	margin:'5 5 0 0',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	  	items : [asmChart]
	  });
  ///////////////////////////////////////////////////////////////////////////////////
  
  //////////////////////////   /////////////////////////////////////////////////
	  Ext.define('tablespaceModel', {
			extend : 'Ext.data.Model',
			fields : [{name : 'tablespace_name',type : 'string'},
				{name: 'file_name',type: 'string'},
				{name: 'total',type: 'string'},
				{name : 'used',type : 'string'},
				{name : 'free',type : 'string'},
				{name : 'user',type : 'string'},
				{name : 'preused',type : 'string'},
				{name : 'prefree',type : 'string'},
				{name : 'tablecount',type : 'string'},
				{name : 'indexcount',type : 'string'},
				{name : 'state',type : 'string'},
				{name : 'expUse',type : 'string'},
				{name : 'preused_forchart',type : 'string'}]
		});
	var tablespaceStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 25,
		model : 'tablespaceModel',
		proxy : {
			type : 'ajax',
			url : 'resourcePanelGetTablespace.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	tablespaceStore.on('beforeload', function(store, options) {
		     var new_params = {iresid:IRESID};
		        Ext.apply(tablespaceStore.proxy.extraParams, new_params);
		});
	  var tablespaceChart = Ext.create('Ext.chart.Chart', {
			 region: 'center',
			 theme : 'CustomBlue',
	     animate: true,
	     shadow: false,
	     store: tablespaceStore,
	     padding : '15 10 -10 0',
	     axes: [{
	         type: 'Numeric',
	         dashSize : 0.5,
	         position: 'left',
	         fields: ['preused_forchart'],
	         grid: true,
	         majorTickSteps:5,
	         minimum: 0
	     }, {
	         type: 'Category',
	         dashSize : 0.5,
	         position: 'bottom',
	         fields: ['tablespace_name']
	     }],
	     series: [{
	         type: 'column',
	         style: { width: 10 },
	         xPadding : 10,
	         axis: 'left',
	         xField: 'tablespace_name',
	         yField: 'preused_forchart',
	         renderer: function(sprite, storeItem, barAttr, i, store) {    
	             barAttr.fill = '#28acb9';  
	         return barAttr;    
	     } ,
	     	tips: {renderer:showTipChart}   
	     }]
	 });
  var tablespaceChartPanel = Ext.create('Ext.panel.Panel',{
		title:'表空间%',
	  	width:'25%',
	  	height:'100%',
	  	margin:'5 0 0 0',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	  	items : [tablespaceChart]
	  });
  ///////////////////////////////////////////////////////////////////////////////////////
  
  /////////////////// 右中面板组合 ///////////////////////////////////////////////////
  var rightCenterPanel = Ext.create('Ext.panel.Panel',{
	  	region:'center',
	  	height:'33%',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : {
			type : 'hbox',
			align : 'stretch'
		},
	    bodyCls:'service_platform_bodybg',
	  	items : [cpuPanel,memoryPanel,asmPanel,tablespaceChartPanel]
	  });
/////////////////////////////////////////////////////////////////////////////////////////////
  
  /////////////////////  表空间  //////////////////////////////////////////////////////////
	 
	  var tablespaceColumns = [
			{text : '序号',xtype : 'rownumberer',align:'left',width : 35},
			{text : '表空间名',dataIndex : 'tablespace_name',width : 120,flex:1,renderer:showTip},
			{text : '总空间',dataIndex : 'total',width : 100,renderer:showTip},
			{text : '文件系统',dataIndex : 'file_name',width : 100,hidden:true},
			{text : '已用',dataIndex : 'used',width : 100,renderer:showTip},
			{text : '可用',dataIndex : 'free',width : 100,renderer:showTip},
			{text : '使用率',dataIndex : 'preused',width : 100,renderer:showTip},
			{text : '空闲率',dataIndex : 'prefree',width : 100,renderer:showTip},
			{text : '表数量',dataIndex : 'tablecount',width : 100,renderer:showTip},
			{text : '索引数量',dataIndex : 'indexcount',width : 100,renderer:showTip},
			{text : '状态',dataIndex : 'state',width : 80,renderer:showTip},
			{text : '预算',dataIndex : 'expUse',width : 100,hidden:true},
			{
                text: '扩容',
                dataIndex: 'Operation',
                width: 40,
                renderer: function(value, p, record, rowIndex) {
                    var tablespacename = record.get('tablespace_name');
                    var file_name = record.get('file_name');
                    file_name=file_name.replace(/\\/g,"/");
                    return '<div>' + '<a href="javascript:void(0)" onclick="altertablespace(\''+tablespacename+'\',\''+file_name+'\')">' + ' 扩容 </a></div>';
                    }
            },];
	  var tablespaceGrid = Ext.create('Ext.grid.Panel', {
		  	id : 'tablespaceGrid',
			region : 'center',
			store : tablespaceStore,
			padding : panel_margin,
			border : true,
			columnLines : true,
			cls: 'customize_panel_back',
			columns : tablespaceColumns,
			listeners:{
				celldblclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
		    		var map=record.data;
		    		var ss='';
		    	    for(var k in map) {
		    	    	ss=ss+k+' : '+map[k]+ '\n';
		    	    }
		    		showWindow(ss);		        	
	    	    }
			}		
		});
	  var sqlmonitorPanel = Ext.create('Ext.panel.Panel',{
		  	region:'center',
		    bodyPadding : grid_margin,
		    cls:'customize_panel_back',
		    layout : 'border',
		  	items : [tablespaceGrid]
		  });	  
  var rightButtomPanel = Ext.create('Ext.panel.Panel',{
		title:'<a href="javascript:showSpacelineDiv();">表空间</a>',
		region:'south',
	  	height:'33%',
	  	margin:'5 0 0 0',
	    bodyPadding : grid_margin,
	    cls:'customize_panel_back',
	    layout : 'border',
	    bodyCls:'service_platform_bodybg',
	  	items : [tablespaceGrid]
	  });

  ///////////////////////////////////////////////////////////////////////
  
  /////////////////  右侧面板组合 /////////////////////////////////////
  var rightPanel = Ext.create('Ext.panel.Panel',{
	//title:'标题2',
	width: '65%',
  	height:'100%',
  	region:'center',
  	margin:'5 5 5 5',
    bodyPadding : grid_margin,
    cls:'customize_panel_back',
    bodyCls:'service_platform_bodybg',
    layout: 'border',
  	items : [rightTopPanel,rightCenterPanel,rightButtomPanel]
  });
 /////////////////////////////////////////////////////////////////////// 
 
//返回功能
	var form = new Ext.form.FormPanel({
		region : 'north',
		bodyPadding : 5,
		bodyCls : 'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		border : false
//		,
//		dockedItems : [ {
//			xtype : 'toolbar',
//			border : false,
//			dock : 'top',
//			baseCls:'customize_gray_back',
//			items : [
//			         '->', {
//				xtype : 'button',
//				cls : 'Common_Btn',
//				text : "返回",
//				handler : back
//			} ]
//		} ]
	});	
	  contentPanel.setTitle(contentPanel.title+'<a href="#" onclick="back()"><span>--资源全貌-->返回</span></a>');
	  var mainPanel = Ext.create('Ext.panel.Panel',{
	  	height:contentPanel.getHeight()-modelHeigth,
	    renderTo : "main_area",
	    border : true,
	    layout : 'border',
	  	items : [/*form,*/leftPanel,rightPanel]
	  });
	    contentPanel.on('resize',function(){     
	        mainPanel.setHeight (contentPanel.getHeight ()-modelHeigth);
	        mainPanel.setWidth (contentPanel.getWidth () );
	    });
	    
 ////////////////////////////  方法 /////////////////////////////////////
	
    function showWindow(content){
   	 var resultDesc = Ext.create('Ext.form.field.TextArea', {
   	        name: 'funcdesc',
   	        displayField: 'funcdesc',
   	        columnWidth: 1,
   	        region:'center',
   	        border:false,
   	        readOnly: true,
   	        autoScroll: true
   	    });
   	    resultDesc.setValue(content);
   	 var detailWindow = Ext.create('widget.window', {
		    title: '详情',
		    closable: true,
		    closeAction: 'hide',
		    resizable: false,
		    modal: true,
		    width : 715,
		    height :500,
		    minWidth : 700,
		    layout: {
		        type: 'border',
		        padding: 5
		    },
		    items: [resultDesc],
			}, { 
				xtype: "button", 
				cls:'Common_Btn',
				text: "取消", 
				handler: function () {
					this.up("window").close();
				}
		});		
		detailWindow.show ();
	}   
});

function changePanel(){
	var fxkGroup = new Ext.form.CheckboxGroup({
		id : 'fxk',
		region : 'center',
        xtype : 'checkboxgroup',
        width : 350,
        columns : 1, 
        items : [{
                    boxLabel : 'TOP SQL',
                    inputValue : "1"
                }, {
                    boxLabel : 'TOP EVENT',
                    inputValue : "2"
                }, {
                    boxLabel : '使用频率Top5',
                    inputValue : "3"
                }, {
                    boxLabel : '磁盘读取Top5',
                    inputValue : "4"
                }]
    });
	var form = new Ext.form.FormPanel({
		region : 'south',
		bodyPadding : 5,
		bodyCls : 'x-docked-noborder-top',
		border : false,
		 buttonAlign : 'center',
         buttons :[{
         	text : '确定',
         	handler :function(){
         		var fxkCheck = Ext.getCmp('fxk').items;
         		var chk=[];
         		for(var i = 0; i < fxkCheck.length; i++){
         			if(fxkCheck.get(i).checked){
         				chk.push(fxkCheck.get(i).inputValue);
         			}
         		}
         		change(chk);
         		changeWindow.close();
         	}
         },{
         	text : '取消',
         	handler : function(){    		
         		changeWindow.close();
         	}
         }]
	});	
	  var changePanel = Ext.create('Ext.panel.Panel',{
			title:'选择其中两项展示在右上角',
			region:'center',
		  	margin:'5 0 0 0',
		    bodyPadding : grid_margin,
		    cls:'customize_panel_back',
		    layout : 'border',
		  	items : [fxkGroup,form ]
		  });
	 var changeWindow = Ext.create('widget.window', {
		    title: '切换面板',
		    closable: true,
		    closeAction: 'destroy',
		    resizable: false,
		    modal: true,
		    width : 500,
		    height :300,
		    minWidth : 300,
		    layout: {
		        type: 'border',
		        padding: 5
		    },
		    items: [changePanel],
			}, { 
				xtype: "button", 
				cls:'Common_Btn',
				text: "取消", 
				handler: function () {
					this.up("window").close();
				}
		});		
		changeWindow.show ();
	} 

function change(check){
	if(check.length!=2){
		Ext.Msg.alert('提示', '必须选择两项！');
		return;
	}
	var rightTopPanel = Ext.getCmp('rightTopPanel');
	var topSqlPanel = Ext.getCmp('topSqlPanel');
	var topEventPanel = Ext.getCmp('topEventPanel');
	var topFrequencePanel = Ext.getCmp('topFrequencePanel');
	var topConsumePanel = Ext.getCmp('topConsumePanel');
	rightTopPanel.removeAll(false);
	for(var i=0;i<check.length;i++){
		switch(check[i]){
			case '1':
				rightTopPanel.add(topSqlPanel);
				break;
			case '2':
				rightTopPanel.add(topEventPanel);
				break;
			case '3':
				rightTopPanel.add(topFrequencePanel);
				break;
			case '4':
				rightTopPanel.add(topConsumePanel);
				break;
		}
	}
}
function back() {
    destroyRubbish();
    contentPanel.getLoader().load({
        url : 'gotoNewResource.do',
        scripts : true
    });
}

function killSession(sid,serial,grid){
    Ext.Msg.confirm("请确认", "确定要杀死该会话？",
    function(button, text) {
        if (button == "yes") {      
              Ext.Ajax.request({
                url: 'resourcePanelKillSession.do',
                params: {
                    iresid : IRESID,
                	sid : sid,
                    serial : serial
                },
                method: 'POST',
                success: function(response, opts) {
                		Ext.getCmp(grid).getStore().reload();
                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                },
                failure: function(result, request) {
                	 Ext.Msg.alert('提示', "操作失败！");
                }
            });
        }       
    });

}
function altertablespace(tablespacename,file_name){
    var fileName = new Ext.form.TextField({
        name : 'fileName',
        fieldLabel : '文件系统',
        displayField : 'fileName',
        emptyText : '',
        labelAlign: 'right',
        labelWidth: 70,
        margin : '10 0 0 0',
        allowDecimals:false,
        allowNegative:false,
        allowBlank: false,
        columnWidth : .98
    });
    fileName.setValue(file_name);
    var insName = new Ext.form.TextField({
        name : 'tablespace',
        fieldLabel : '表空间名',
        displayField : 'tablespace',
        emptyText : '',
        labelAlign: 'right',
        labelWidth: 70,
        margin : '10 0 0 0',
        allowDecimals:false,
        allowNegative:false,
        allowBlank: false,
        columnWidth : .98
    });
    insName.setValue(tablespacename);
    var maxsize = new Ext.form.NumberField({
        name : 'maxsize',
        fieldLabel : '最大值(G)',
        displayField : 'maxsize',
        emptyText : '',
        labelAlign: 'right',
        labelWidth: 70,
        margin : '10 0 0 0',
        value:'32',
        maxValue:'32',
        allowDecimals:false,
        allowNegative:false,
        allowBlank: false,
        columnWidth : .98
    });
    var setsize = new Ext.form.NumberField({
        name : 'setsize',
        fieldLabel : '设置值(M)',
        displayField : 'setsize',
        emptyText : '',
        labelAlign: 'right',
        labelWidth: 70,
        margin : '10 0 0 0',
        allowDecimals:false,
        allowNegative:false,
        allowBlank: false,
        columnWidth : .98
    });
    var form_sm = Ext.create('Ext.form.Panel', {
        width: 600,
        layout : 'anchor',
        bodyCls : 'x-docked-noborder-top',
        buttonAlign : 'center',
        border : false,
        items: [{
            anchor:'98%',
            padding : '5 0 5 0',
            border : false,
            items: [{
                layout : 'column',
                border : false,
                items : [ fileName,insName,maxsize,setsize]
            }]
        }]
    });
    
    var tablespaceWindow = Ext.create('widget.window', {
           title: '表空间信息',
           closable: true,
           closeAction: 'hide',
           resizable: false,
           modal: true,
           width : 715,
           height :500,
           minWidth : 500,
           layout: {
               type: 'border',
               padding: 5
           },
           items: [form_sm],
           dockedItems : [ {
               xtype : 'toolbar',
               border : false,
               dock : 'bottom',
               layout: {pack: 'center'},
               items : [ {
               xtype: "button",
               cls:'Common_Btn',
               text: "确定", 
               handler: function () {
                   Ext.MessageBox.wait ("数据处理中...", "进度条");
                   Ext.Ajax.request({
                       url: 'resourceTablespaceSet.do',
                       params: {
                       tablespacename : tablespacename,
                       iresid : IRESID,
                       tablespacesize:insName.getValue(),
                       filename:fileName.getValue(),
                       setsize:setsize.getValue(),
                       maxsize:maxsize.getValue(),
                       exectype:0
                   },
                       method: 'POST',
                       success: function(response, opts) {
                       Ext.getCmp('tablespaceGrid').getStore().reload();
                       tablespaceWindow.close();	
                       Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                   },
                       failure: function(result, request) {
                       Ext.Msg.alert('提示', "操作失败！");
                   }
                   });
               
               }
           }, { 
               xtype: "button", 
               cls:'Common_Btn',
               text: "取消", 
               handler: function () {
                   this.up("window").close();
               }
           }]
           }]
           });     
    tablespaceWindow.show ();
}
function showSpacelineDiv(){
	Ext.onReady(function () {
		var chart, timeAxis;
		var dataList = [];
		var nameList = [];
		var seriesArr = [];
		Ext.Ajax.request({  
			url : 'getSpaceSurplusLineData.do',  
			method : 'post', 
			async: false,
			params : {  
				iresId : IRESID
			},
		    success: function(response, opts) {
		        dataList= Ext.decode (response.responseText).dataList;
		        nameList= Ext.decode (response.responseText).nameList;
		    },
		    failure: function(result, opts) {
		    	Ext.Msg.alert("提示","请求返回失败！");
		    }
		});
		
	    	for(var i=1;i<nameList.length;i++){
	    		var series = {
			                type: 'line',
			                highlight: {
			                    size: 7,
			                    radius: 7
			                },
			                axis: 'left',
			                xField: nameList[0],
			                yField: nameList[i],
			                markerConfig: {
			                    type: 'cross',
			                    size: 4,
			                    radius: 4,
			                    'stroke-width': 0
			                }
				    };
				seriesArr.push(series);
	    	}
	   
	    var store =  new Ext.data.JsonStore({
	    	fields:nameList,
	    	data: dataList
	    });
	    var win = Ext.create('Ext.window.Window', {
	    	width: 800,
	        height: 600,
	        minHeight: 400,
	        minWidth: 550,
	        maximizable: true,
	        title: '剩余空间(G)',
	        layout: 'fit',
	        items: [{
	            xtype: 'chart',
	            width:750,
	            style: 'background:#fff',
	            store: store,
	            itemId: 'chartCmp',
	            axes: [{
	            	title: '剩余空间',
	                type: 'Numeric',
	                position: 'left',
	                grid: {
	                    odd: {
	                        fill: '#dedede',
	                        stroke: '#ddd',
	                        'stroke-width': 0.5
	                    }
	                }
	            }, {
	            	title: '日期',
	                type: 'Category',
	                position: 'bottom',
	                fields: 'name'
	            }],
	            legend: {
	    	    	bottom: 10,
	    	        left: 'center',
	    	        textStyle:{//图例文字的样式
	    	            color:'#fff',
	    	        }
	    	    },
	            series:seriesArr
	        }]
	    }).show();
	    chart = win.child('#chartCmp');
	    timeAxis = chart.axes.get(1);
	});
}
function queryplan(sql_id){
    var planDesc = Ext.create('Ext.form.field.TextArea', {
           name: 'planDesc',
           displayField: 'planDesc',
           columnWidth: 1,
           region:'center',
           border:false,
           readOnly: true,
           autoScroll: true
       });
    Ext.Ajax.request({
        url: 'getsqlplane.do',
        params: {
            iresid : IRESID,
            sql_id : sql_id
        },
        method: 'POST',
        success: function(response, opts) {
                planDesc.setValue(Ext.decode(response.responseText).content);
        },
        failure: function(result, request) {
             Ext.Msg.alert('提示', "操作失败！");
        }
    });
    var planWindow = Ext.create('widget.window', {
           title: '详情',
           closable: true,
           closeAction: 'hide',
           resizable: false,
           modal: true,
           width : 715,
           height :500,
           minWidth : 700,
           layout: {
               type: 'border',
               padding: 5
           },
           items: [planDesc],
           });     
    planWindow.show ();
}
function showAsmCHarts(){
    Ext.onReady(function () {
        var chart, timeAxis;
        var dataF = [];
        var dataG = [];
        var dataGroup = [];
        var seriesArr = [];
        
        Ext.Ajax.request({  
            url : 'asmLineChartList.do',  
            method : 'post', 
            async: false,
            params : {  
                iresId : IRESID
            },
            success: function(response, opts) {
                var success = Ext.decode(response.responseText).success;
                var dataList= Ext.decode (response.responseText).dataList;
                var groupName= Ext.decode (response.responseText).groupName;
                if (success) {
                    dataF.push('itime');
                    for(var i=0;i<groupName.length;i++){
                        dataF.push(groupName[i]);
                        dataGroup.push(groupName[i]);
                        seriesfun(groupName[i]);
                    }
                    for(var j=0;j<dataList.length;j++){
                        dataG.push(dataList[j]);
                    }
                    console.log(dataF);
                    console.log(dataG);
                    console.log(seriesArr);
                }
            },
            failure: function(result, opts) {
                Ext.Msg.alert("提示","请求返回失败！");
            }
        });
        
        function seriesfun(value){
            var series = {
                    type: 'line',
                    axis: ['left', 'bottom'],
                    xField: 'itime',
                    yField: value,
                    label: {
                        display: 'none',
                        field: value,
                        renderer: function(v) { return v >> 0; },
                        'text-anchor': 'middle'
                    },
                    markerConfig: {
                        radius: 5,
                        size: 5
                    }
                };
            seriesArr.push(series);
        }
        var store = Ext.create('Ext.data.JsonStore', {
            fields: dataF,
            data: dataG
        });
    
        var win = Ext.create('Ext.window.Window', {
            width: 600,
            height: 500,
            minHeight: 400,
            minWidth: 550,
            maximizable: true,
            title: '使用量(G)',
            layout: 'fit',
            items: [{
                xtype: 'chart',
                style: 'background:#fff',
                store: store,
                itemId: 'chartCmp',
                axes: [{
                    type: 'Numeric',
                    position: 'left',
                    fields: dataGroup,
                    grid: {
                        odd: {
                            fill: '#dedede',
                            stroke: '#ddd',
                            'stroke-width': 0.5
                        }
                    }
                }, {
                    type: 'Category',
                    position: 'bottom',
                    fields: 'itime'
                }],
                legend: {
                    bottom: 10,
                    left: 'center',
                    textStyle:{//图例文字的样式
                        color:'#fff',
                    }
                },
                series: seriesArr
            }]
        }).show();
        chart = win.child('#chartCmp');
        timeAxis = chart.axes.get(1);
    });
}