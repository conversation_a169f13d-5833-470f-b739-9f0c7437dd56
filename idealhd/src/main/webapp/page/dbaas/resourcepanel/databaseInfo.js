var type=1;
var data=['','TABLE', 'PROCEDURE','FUNCTION' ]
Ext.onReady(function() {
//清理主面板的各种监听时间
  destroyRubbish();
  Ext.tip.QuickTipManager.init();
  Ext.Ajax.timeout=100000; 
//			------------------------左侧面板开始-----------------------
  
  Ext.define('typeModel', {
	     extend: 'Ext.data.Model',
	     fields: [
	         {name: 'name', type: 'string'},
	         {name: 'type',  type: 'int'}
	     ]
	 });
  
	 var left_store = Ext.create('Ext.data.Store', {
		 model: 'typeModel',
		 data : [
	         {name: 'TABLE',    type: 1},
	         {name: 'PROCEDURE', type:2},
	         {name: 'FUNCTION', type:3},
	     ]
	    });
	 
	 var  left_columns = [{
			text : '序号',
			xtype : 'rownumberer',
			width:65
		}, {
			text : '类型',
			dataIndex : 'name',
			flex:1
		}, {
			text : 'type',
			dataIndex : 'type',
			hidden : true
		}];
	 var  left_grid= Ext.create('Ext.grid.Panel', {
	        store:left_store,
	        padding : panel_margin,
	        border:true,
	        bbar: rightPageBar,  
	        columnLines : true,
	        columns:left_columns
	    });
	 
	 var leftPanel = Ext.create('Ext.panel.Panel',{
	    	title:'数据库对象',
	    	layout : 'fit',
	    	border:false,
			region : 'west',
			split : true,
			width : '30%',
			bodyCls : 'x-docked-noborder-top',
			cls:'customize_panel_back panel_space_right',
			items : [left_grid]
	    });
	 
	 left_grid.on("itemclick",function(obj, record, index, eOpts){		
			type = record.get('type');     
	    	right_store.load({
        	    params: {
        	    	page : 1,
        	    	limit : 30,
        	    	start:0
        	    }
        	});
	    	right_grid.setTitle( data[type] );
	    });
//		------------------------左侧面板结束-----------------------
	 
  Ext.define('listData', {
      extend: 'Ext.data.Model',
      fields: [
		    {name : 'name',type : 'string'},
		    {name : 'type',type : 'int'}
      ]
   });
	
	 var right_store = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        autoDestroy : true,
	        pageSize: 30,
	        model: 'listData',
	        proxy: {
	            type: 'ajax',
	            url: 'getInfoListByLeft.do',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty: 'total'
	            }
	        }
	    });
	 right_store.on ('beforeload', function (store, options)
				{
					var new_params =
					{
							type:type,
							iResId : iResId
					};
					Ext.apply (right_store.proxy.extraParams, new_params);
				});
	 var  right_columns = [{
			text : '序号',
			xtype : 'rownumberer',
			width:65
		}, {
			text : '类型',
			dataIndex : 'name',
			flex:1
		}, {
			text : 'type',
			dataIndex : 'type',
			hidden:true
		}, {
			text : '操作',
			dataIndex : 'iid',
			width:65,
			renderer:function(value, p, record) {
					return "<a href='#' valign='middle' onclick=\"showWindow('"
					+record.get('name')+"',"+record.get('type')+");\"><span class='abc'>详情</span></a>";
			}
		}];
  
	 var rightPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
	        store: right_store,
	        dock: 'bottom',
	        baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	        displayInfo: true,
	        border : false
	    });
	 var  right_grid= Ext.create('Ext.grid.Panel', {
	        store:right_store,
	        padding : panel_margin,
	        border:true,
	        bbar: rightPageBar,  
	        columnLines : true,
	        columns:right_columns
//	        listeners:{
//				cellclick: function(t,td, cellIndex, record, tr, rowIndex, e, eOpts) {
//					showWindow(record);		        	
//	    	    }
//			}	
	        
	    });

		var rightPanel = Ext.create('Ext.panel.Panel',{ 
		    layout : 'fit',
		    bodyCls : 'x-docked-noborder-top',
		    cls:'customize_panel_back panel_space_right',
		    region : 'center',
		    border : false,
		    width : '60%',
		    items : [right_grid]
		});
	 
	 
  contentPanel.setTitle(contentPanel.title+'<a href="#" onclick="back()"><span>--数据库-->返回</span></a>');
	  var mainPanel = Ext.create('Ext.panel.Panel',{
	  	height:contentPanel.getHeight()-modelHeigth,
	    renderTo : "main_area",
	    border : true,
	    layout : 'border',
	  	items : [leftPanel,rightPanel]
	  });
	    contentPanel.on('resize',function(){     
	        mainPanel.setHeight (contentPanel.getHeight ()-modelHeigth);
	        mainPanel.setWidth (contentPanel.getWidth () );
	    });
	    
	    
	    
});	 
function showWindow(name,type){
    var DetailWinTi;
    if(type==1) {
       DetailWinTi = Ext.create('widget.window', {
            title: '详情',
            closable: true,
            closeAction: 'destroy',
            width : contentPanel.getWidth()*0.8,
            height : contentPanel.getHeight(),
            minWidth: 300,
            minHeight: 300,
            layout: 'fit',
            draggable: false,
            // 禁止拖动
            resizable: false,
            // 禁止缩放
            modal: true,
            loader: {
                url: 'viewDetailForDateTypePage.do',
                params: {
                    name: name,
                    type:type,
                    iResId:iResId
                },
                autoLoad: true,
                scripts: true
            }
        });
    }else {
        
        var html="";
        
        Ext.Ajax.request({
            url: 'viewDetailForDateType.do',
            async:false,
            params: {
                iResId:iResId,type:type,name:name
            },
            method: 'POST',
            success: function(response, opts) {
               var result= Ext.decode(response.responseText).dataList;
                for(var i=0;i<result.length;i++){
                    html=html+"<p>"+result[i].name+"</p>";
              }
            },
            failure: function(result, request) {
                secureFilterRs(result, "操作失败！");
            }
        });
        
        var textPanel = Ext.create('Ext.form.field.Display', {
        });
        textPanel.setValue(html);
        var froms=Ext.create('Ext.form.Panel', {
            baseCls:'customize_panel_back',// panel_space_bottom
            border: true,
            width : '97%',
            items: [textPanel]
        });
        
        DetailWinTi = Ext.widget('window', {
            title: '详情',
            closeAction: 'hide',
            constrain: true,
            resizable: false,
            width : contentPanel.getWidth()*0.8,
            height : contentPanel.getHeight(),
            minWidth: 300,
            minHeight: 300,
            autoScroll: true,
            modal: true,
            items: froms,
            defaultFocus: 'firstName'
        });
    }
	DetailWinTi.show();
} 



function back() {
    destroyRubbish();
    contentPanel.getLoader().load({
        url : 'gotoNewResource.do',
        scripts : true
    });
}