//$(function() {
//$.ajax({
//	url:"viewDetailForDateType.do",
//	data:{iResId:iResId,type:type,name:name},
//	type: 'POST',
//	dataType:'json',
//	success:function(result){
//		for(var i=0;i<result.dataList.length;i++){
//			$("#text_area").append("<p>"+result.dataList[i].name+"</p>");
//		}
//		
//	}
//});
//});
Ext.onReady(function() {
  //清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
//            ------------------------左侧面板开始-----------------------
    var textPanel = Ext.create('Ext.form.field.Display', {
        border:false,
        autoScroll: true
    });
    
    var from=Ext.create('Ext.form.Panel', {
        region:'center',
        height: '95%',
        baseCls:'customize_panel_back',// panel_space_bottom
        border: true,
        items: [textPanel]
    });
    
    var mainPanel = Ext.create('Ext.panel.Panel',{
          renderTo : "database_text_area",
          border : true,
          layout:'border',
          items : [from]
        });
      contentPanel.on('resize',function(){     
          mainPanel.setHeight ("100%");
          mainPanel.setWidth ("100%");
      });
      Ext.Ajax.request({
          url: 'viewDetailForDateType.do',
          params: {
              iResId:iResId,type:type,name:name,flag:obhdbaas_resource_flag
          },
          method: 'POST',
          success: function(response, opts) {
             var result= Ext.decode(response.responseText).dataList;
             var html="";
              for(var i=0;i<result.length;i++){
                  html=html+"<p>"+result[i].name+"</p>";
            }
              console.log(1111,html);
              textPanel.html=html;
          },
          failure: function(result, request) {
              secureFilterRs(result, "操作失败！");
          }
      });
  });  
