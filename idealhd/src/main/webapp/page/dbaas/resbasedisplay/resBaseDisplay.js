Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   //destroyRubbish();
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'INAME',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IPORT',
            type: 'string'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
            name: 'IVERSION',
            type: 'string'
        },
        {
        	name: 'IPLATFORM',
        	type: 'string'
        },
        {
        	name: 'IFLAG',
        	type: 'string'
        },
        {
            name: 'ICREATETIME',
            type: 'long'
        },
        {
            name: 'IUPDATETIME',
            type: 'long'
        },
        {
            name: 'ICPU',
            type: 'string'
        }
        ,
        {
            name: 'ICPUUSE',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY',
            type: 'string'
        }
        ,
        {
            name: 'IMEMORY_REMAIN',
            type: 'string'
        }
        ,
        {
            name: 'IDISK',
            type: 'string'
        }
        ,
        {
            name: 'IDISK_REMAIN',
            type: 'string'
        }
        ,
        {
        	name: 'IPASSWORD',
        	type: 'String'
        }
        ,
        {
        	name: 'ISYSDBNAME',
        	type: 'string'
        }
        ,
        {
        	name: 'ISID',
        	type: 'string'
        },{
        	name: 'iType',
        	type: 'string'
        }
        ]
    });
   
   var dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceBaseList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    var dataTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "ORACLE"
        },
        {
    		"name": "DB2"
    	},
    	{
    		"name": "MYSQL"
    	}
        ]
    });
    var iTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "RAC"
        },
        {
    		"name": "INSTANCE"
    	},
    	{
    		"name": "DATAGUARD"
    	}
        ]
    });
    var platFormStore = Ext.create('Ext.data.Store', {
    	 fields: ['name'],
         data: [{'name': 'Windows'}, {'name': 'Linux'}]
    });

    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '服务器IP',
        dataIndex: 'IIP',
        flex: 1, 
        renderer: function(value, p, record, rowIndex) {
            var iip = record.get('IIP');
            return '<div>' + '<a href="javascript:void(0)" onclick="resDisplay(\''+iip+'\')">' + iip + '</a>' + '</div>';
        }
    },
    {
        text: '版本',
        dataIndex: 'IVERSION',
        width: 100, 
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '设备名称',
        dataIndex: 'INAME',
        width: 120,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '资源类型',
        dataIndex: 'IRSTYPE',
        width: 100, 
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: dataTypeModel
        }),renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },{
        text: '服务器端口',
        dataIndex: 'IPORT',
        width: 100,
        editor: {
            allowBlank: true
        }
    },
    {
        text: '资源所属平台',
        dataIndex: 'IPLATFORM',
        width: 120,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: platFormStore
        }),
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '类型',
        dataIndex: 'iType',
        width: 100,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            // 用all表示把下拉框列表框的列表值全部显示出来
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: iTypeModel
        }),renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        header: '状态',
        dataIndex: 'IFLAG',
        width: 80,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            editable: false,
            queryMode: 'local',
            displayField: 'name',
            valueField: 'name',
            store: Ext.create('Ext.data.Store', {
                fields: ['name'],
                data: [{
                	'name':'有效'
                },
                {
                	'name':'失效'
            	}
                ]
            })
        })
    }];
    function retNotView(value){
    	var coun ="";
    	if (value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
    // 分页工具
  var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });



  var  dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
		padding : panel_margin,
			cls:'customize_panel_back',
        //selModel: selModel,
        border: true,
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
    });


    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resbasedisplay_area",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',
        items: [dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

  


})

function resDisplay(iip){
	//alert(iip);
	contentPanel.getLoader().load({
		url: 'gotoResDisplay.do',
		params : {
			iip : iip
		},
		scripts: true
	});
	
	/*
	Ext.create('Ext.window.Window', {
	    title: '资源',
	    height: '60%',  //Number型  也可以是字符串类型  width:'60%'
	    width: '60%',
	    layout: 'fit',
	    constrain: true, 		//闲置窗口不超出浏览器
	    constrainHeader:true, 	//标题不能超出浏览器边界
	    modal: true,			//设置模态窗口
	    plain:true, 			//窗口设置透明背景
	    draggable: false,
	    resizable: false,
	    loader: {
			url: 'gotoResDisplay.do',
			params : {
				iip : iip
			},
			autoLoad: true,
			scripts: true
		},
	    autoScroll:true //显示滚动条
	}).show();
	
	*/
}
