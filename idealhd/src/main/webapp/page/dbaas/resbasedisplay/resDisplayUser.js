Ext
		.onReady(function() {
			Ext.tip.QuickTipManager.init();
			// 清理主面板的各种监听时间
			// destroyRubbish();
			Ext.define('dataSourceModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'IID',
					type : 'long'
				}, {
					name : 'INAME',
					type : 'string'
				}, {
					name : 'IIP',
					type : 'string'
				}, {
					name : 'IDBPORT',
					type : 'long'
				}, {
					name : 'IRSTYPE',
					type : 'string'
				}, {
					name : 'IFLAG',
					type : 'string'
				}, {
					name : 'IBUSINESS',
					type : 'string'
				}, {
					name : 'IDBUSER',
					type : 'string'
				}, {
					name : 'IDBPWD',
					type : 'string'
				}, {
					name : 'ICOPYNUM',
					type : 'string'
				}, {
					name : 'IAPPLYUSER',
					type : 'string'
				}, {
					name : 'ISID',
					type : 'string'
				}, {
					name : 'ITYPE',
					type : 'long'
				}, {
					name : 'IENV',
					type : 'long'
				}, {
					name : 'IDBID',
					type : 'string'
				}, {
					name : 'IDBVERSION',
					type : 'string'
				}, {
					name : 'ISTATUS',
					type : 'string'
				}, {
					name : 'IPOSITION',
					type : 'int'
				}, {
					name : 'IALTERLOGNAME',
					type : 'string'
				}, {
					name : 'ICPU',
					type : 'string'
				}, {
					name : 'IMEMORY',
					type : 'string'
				}, {
					name : 'IDISK',
					type : 'string'
				}, {
					name : 'IMODEL',
					type : 'string'
				} ]
			});

			var dataSourceStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				pageSize : 50,
				model : 'dataSourceModel',
				proxy : {
					type : 'ajax',
					url : 'resourceManageList.do',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			dataSourceStore.on('beforeload', function(store, options) {
				var new_params = {
					baseIp : iip,
					dataFlag : 2
				};

				Ext.apply(dataSourceStore.proxy.extraParams, new_params);
			});
			var dataTypeModel = Ext.create('Ext.data.Store', {
				fields : [ 'name', 'value' ],
				data : [ {
					name : "人工",
					value : "1"
				}, {
					name : "自动",
					value : "2"
				} ]
			});
			Ext.define('sysModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'sysName',
					type : 'string'
				} ]
			});
			var sysDataStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'sysModel',
				proxy : {
					type : 'ajax',
					url : 'getAppSysManageList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var sysDataByUserIdStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'sysModel',
				proxy : {
					type : 'ajax',
					url : 'getAppSysManageByUserId.do?projectFlag=1',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});

			var dataTypeModel = Ext.create('Ext.data.Store', {
				fields : [ 'name' ],
				autoLoad : true,
				autoDestroy : true,
				proxy : {
					type : 'ajax',
					url : 'getDatabaseType.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var scriptServiceReleaseColumns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						align:'left',
						width : 70,
						locked : true,
						resizable : true
					},
					{
						text : '主键',
						dataIndex : 'IID',
						width : 40,
						hidden : true
					},
					{
						text : '<span style="color:#dddddd;">DBID</span>',
						dataIndex : 'IDBID',
						locked : true,
						width : 100,
						renderer : function(value, metadata) {
							metadata.css = 'x-grid-back-red';
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '业务系统',
						dataIndex : 'IBUSINESS',
						width : 100,
						locked : true,
						editor : new Ext.form.field.ComboBox({
							triggerAction : 'all',
							// 用all表示把下拉框列表框的列表值全部显示出来
							editable : false,
							queryMode : 'local',
							emptyText : "--请选择--",
							displayField : 'sysName',
							valueField : 'sysName',
							store : sysDataByUserIdStore
						}),
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '服务器IP',
						dataIndex : 'IIP',
						locked : true,
						width : 100,
						editor : {
							allowBlank : true
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '数据源类型',
						dataIndex : 'IRSTYPE',
						locked : true,
						width : 100,
						editor : new Ext.form.field.ComboBox({
							triggerAction : 'all',
							// 用all表示把下拉框列表框的列表值全部显示出来
							editable : false,
							queryMode : 'local',
							displayField : 'name',
							valueField : 'name',
							store : dataTypeModel
						}),
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '数据库版本',
						dataIndex : 'IDBVERSION',
						width : 100,
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '副本号',
						dataIndex : 'ICOPYNUM',
						width : 80,
						editor : {
							allowBlank : false
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '数据库端口',
						dataIndex : 'IDBPORT',
						width : 100,
						editor : {
							xtype : 'numberfield',
							maxValue : 65535,
							minValue : 1
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '设备名称',
						dataIndex : 'INAME',
						width : 140,
						editor : {
							allowBlank : false
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : '数据源用户',
						dataIndex : 'IDBUSER',
						width : 100,
						editor : {
							allowBlank : false
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						header : '数据源密码',
						dataIndex : 'IDBPWD',
						width : 100,
						editor : new Ext.form.TextField({
							inputType : 'password', // 设置输入类型为password
							allowBlank : false,
							allowNegative : true
						}),
						renderer : retNotView
					},
					{
						header : '服务名',
						dataIndex : 'ISID',
						width : 140,
						editor : {
							allowBlank : true
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						header : '资源模式',
						dataIndex : 'IMODEL',
						width : 65,
						editor : new Ext.form.field.ComboBox({
							triggerAction : 'all',
							// 用all表示把下拉框列表框的列表值全部显示出来
							editable : false,
							queryMode : 'local',
							emptyText : "--请选择--",
							displayField : 'name',
							valueField : 'value',
							store : Ext.create('Ext.data.Store', {
								fields : [ 'name', 'value' ],
								data : [ {
									name : "RAC",
									value : "1"
								}, {
									name : "实例",
									value : "2"
								}, {
									name : "DATAGUARD",
									value : "3"
								}, {
									name : "用户",
									value : "4"
								} ]
							})
						}),
						renderer : function(value, p, record, rowIndex) {
							var IFLAG = record.get('IMODEL');
							if (IFLAG == '1') {
								return "RAC";
							} else if (IFLAG == '2') {
								return "实例";
							} else if (IFLAG == '3') {
								return "DATAGUARD";
							} else if (IFLAG == '4') {
								return "用户";
							}
						}
					},
					{
						header : '日志文件',
						dataIndex : 'IALTERLOGNAME',
						// hidden:logSwitch,
						hidden : true,
						width : 140,
						editor : {
							allowBlank : true
						},
						renderer : function(value, metadata) {
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						text : 'CPU',
						dataIndex : 'ICPU',
						width : 60,
						editor : false,
						hidden : true
					},
					{
						text : '内存 总/剩余',
						dataIndex : 'IMEMORY',
						width : 100,
						editor : false,
						hidden : true
					},
					{
						text : '磁盘空间 总/剩余',
						dataIndex : 'IDISK',
						width : 120,
						editor : false,
						hidden : true
					},
					{
						header : '状态',
						dataIndex : 'ISTATUS',
						width : 80,
						editor : new Ext.form.field.ComboBox({
							triggerAction : 'all',
							// 用all表示把下拉框列表框的列表值全部显示出来
							editable : false,
							queryMode : 'local',
							emptyText : "--请选择--",
							displayField : 'name',
							valueField : 'value',
							store : Ext.create('Ext.data.Store', {
								fields : [ 'name', 'value' ],
								data : [ {
									name : "有效",
									value : "0"
								}, {
									name : "失效",
									value : "1"
								} ]
							})
						}),
						renderer : function(value, p, record, rowIndex) {
							var IFLAG = record.get('ISTATUS');
							if (IFLAG == '0') {
								return "<span class='Complete_Green State_Color'>有效</span>";
							} else {
								return "<span class='Abnormal_yellow State_Color'>失效</span>";
							}
						}
					},
					{
						header : '环境',
						dataIndex : 'IENV',
						width : 65,
						editor : new Ext.form.field.ComboBox({
							triggerAction : 'all',
							// 用all表示把下拉框列表框的列表值全部显示出来
							editable : false,
							queryMode : 'local',
							emptyText : "--请选择--",
							displayField : 'name',
							valueField : 'value',
							store : Ext.create('Ext.data.Store', {
								fields : [ 'name', 'value' ],
								data : [ {
									name : "测试",
									value : "0"
								}, {
									name : "生产",
									value : "1"
								}, {
									name : "研发",
									value : "2"
								} ]
							})
						}),
						renderer : function(value, p, record, rowIndex) {
							var IFLAG = record.get('IENV');
							if (IFLAG == '0') {
								return "测试";
							} else if (IFLAG == '1') {
								return "生产";
							} else if (IFLAG == '2') {
								return "研发";
							}
						}
					},
					{
						header : '<span style="color:#dddddd;">申请人</span>',
						dataIndex : 'IAPPLYUSER',
						width : 90,
						renderer : function(value, metadata) {
							metadata.css = 'x-grid-back-red';
							metadata.tdAttr = 'data-qtip="' + value + '"';
							return value;
						}
					},
					{
						header : '<span style="color:#dddddd;">所属类型</span>',
						dataIndex : 'ITYPE',
						width : 90,
						renderer : function(value, metadata) {
							metadata.css = 'x-grid-back-red';
							if (value == '1') {
								return "人工";
							} else {
								return "自动";
							}
						}
					},
					{
						text : '操作',
						dataIndex : 'stepOperation',
						width : 80,
						renderer : function(value, p, record, rowIndex) {
							var iid = record.get('IID');
							return '<div>'
									+ '<a href="javascript:void(0)" onclick="showDetail('
									+ iid + ')">' + '&nbsp;查看详情' + '</a>'
									+ '</div>';
						}
					} ];
			// 分页工具
			var pageBar = Ext.create('Ext.PagingToolbar', {
				store : dataSourceStore,
				dock : 'bottom',
				baseCls : Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				displayInfo : true,
				border : false,
				emptyMsg : '找不到任何记录'
			});

			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});

			var form = new Ext.form.FormPanel({
				region : 'north',
				bodyPadding : 5,
				bodyCls : 'x-docked-noborder-top',
				border : false,
				dockedItems : [ {
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ '->', {
						xtype : 'button',
						cls : 'Common_Btn',
						text : "返回",
						handler : back
					} ]
				} ]
			});
			function back() {
				destroyRubbish();
				contentPanel.getLoader().load({
					url : 'gotoResDisplay.do',
					params : {
						iip : iip
					},
					scripts : true
				});
			}
			var dataSourceGrid = Ext.create('Ext.grid.Panel', {
				region : 'center',
				store : dataSourceStore,
				// selModel: selModel,
				// plugins: [cellEditing],
				padding : panel_margin,
				border : true,
				bbar : pageBar,
				columnLines : true,
				columns : scriptServiceReleaseColumns,

			});

			function retNotView(value) {
				var coun = "";
				if (value.trim().length > 0) {
					for (var i = 0; i < value.length; i++) {
						coun = coun + "*";
					}
				}
				if (value.trim() == "") {
					coun = "";
				}
				return coun;
			}

			function QueryMessage() {
				if (Ext.isIE) {
					CollectGarbage();
				}
				pageBar.moveFirst();
			}

			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "resdisplayUser_area",
				layout : 'border',
				bodyCls : 'service_platform_bodybg',
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				bodyPadding : grid_margin,
				border : true,
				items : [ form, dataSourceGrid ]
			});

			/* 解决IE下trim问题 */
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};

			/** 窗口尺寸调节* */
			contentPanel.on('resize', function() {
				mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				mainPanel.setWidth(contentPanel.getWidth());
			});

			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(mainPanel);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
		});
function showDetail(IID) {
	Ext.create('Ext.window.Window', {
		title : '详情',
		height : '60%', // Number型 也可以是字符串类型 width:'60%'
		width : '60%',
		layout : 'fit',
		constrain : true, // 闲置窗口不超出浏览器
		constrainHeader : true, // 标题不能超出浏览器边界
		modal : true, // 设置模态窗口
		plain : true, // 窗口设置透明背景
		draggable : false,
		resizable : false,
		loader : {
			url : 'todetailResourceManage.do',
			params : {
				IID : IID
			},
			autoLoad : true,
			scripts : true
		},
		autoScroll : true
	// 显示滚动条
	}).show();
}
function setMessage(msg) {
	Ext.Msg.alert('提示', msg);
}
