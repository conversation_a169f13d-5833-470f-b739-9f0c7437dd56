<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var sForm;
	var checkRadioForBasicScriptEdit= 0;
	var sessionIdForBasicScriptEdit = '<%=request.getSession().getId()%>';
	<% if (null==request.getParameter("oldScriptId") && null==request.getAttribute("oldScriptId")) { %>
  	var oldScriptId=0;
<% } else { %>
	<% if(null!=request.getParameter("oldScriptId")) { %>
	  var oldScriptId=<%=request.getParameter("oldScriptId")%>;
	<% } else { %>
	  var oldScriptId=<%=request.getAttribute("oldScriptId")%>;
	<% } %>
<% } %>
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/md5.js"></script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dbaas/servicedefinition/serviceDefinition.js"></script>
</head>
<body>
	<div id="gridBasicScript_area" style="width: 100%; height: 100%;"></div>
</body>
</html>