Ext.onReady(function() {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    var scriptForm;    
    var uploadProcessWin;
    var baseInfoOfScriptWin;
    var chooseTestAgentWin;
    var publishAuditingSMWin;
    var attachmentIds = [];
    var scriptName = "";
    var saveFromBottom = true;
    var countdown = 10;
    var tryRequestId = '';
    var tryAgentIp = '';
    var tryAgentPort = '';
    var cmdRequestId = '';
    var cmdAgentIp = '';
    var cmdAgentPort = '';
    var refreshTryForBasic;
    var refreshCmdForBasic;
    var scriptDesc = "";
    var newServiceId = 0;
    var isFromTryATry = 0;
    var agentChosedId = 0;
    var whichButtonIsClicked = 0; // 1:尝试一下 2:测试 3:终端选择服务器
    var cmdVForbasicEdit = null;//CMD输入内容
    var sysID;
	var busID;
    
	var chosedAgentWin;
	var chosedAgentIds = [];
	
    var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    
/*
 * Ext.MessageBox.prompt('脚本名称', '请输入脚本名称:', function(btn, text, cfg){
 * if(btn=='ok') { if(Ext.isEmpty(Ext.util.Format.trim(text))) { var newMsg = '<span
 * style="color:red">脚本名称不能为空</span>'; Ext.Msg.show(Ext.apply({}, { msg: newMsg },
 * cfg)); } else { scriptName = Ext.util.Format.trim(text);
 * $('#s-n-t').html(Ext.util.Format.trim(text)); }
 *  } else { Ext.MessageBox.alert("提示", "脚本名称可以在保存时再填写！"); }
 * 
 * });
 */
	var authSateComboStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data: [[2, 'DBA可见'], [3, '项目经理可见'],[4, '普通用户可见']]
    });
    
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    bussData.on('load', function(store, options) {
    	if(sysID) {
    		bussCb.setValue(sysID);
    		bussTypeData.load({
    			params: {
    				fk: sysID
    			}
    		});
    	}
    });
    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    
    bussTypeData.on('load', function(store, options) {
    	if(busID) {
    		bussTypeCb.setValue(busID);
    	}
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: true,
        emptyText: '--请选一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        afterLabelTextTpl: required,
        fieldLabel: '二级分类',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: true,
        emptyText: '--请选择二级分类--',
        store: bussTypeData
    });
    var authCombox = Ext.create('Ext.form.field.ComboBox', {
        name: 'authoCombox',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '权限',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        hidden:true,
        store: authSateComboStore
    });

    /** *********************Panel********************* */
    var FieldContainer_win = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 60,
        name: 'ra_s_type_win',
        items: [{
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '0',
            boxLabel: 'shell',
            checked:true,
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                        if (checkRadioForBasicScriptEdit != 0) {
                            editor.setOption("mode", 'shell');
                            checkRadioForBasicScriptEdit = 0;
                        }
                        mainP.setTitle('编辑框---shell');
                        clickNotdoc();
                    }
                }
            }
        },
        {
            name: 'ra_s_type_win',
            width: 50,
            inputValue: '1',
            boxLabel: 'bat',
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                        if (checkRadioForBasicScriptEdit != 1) {
                        	checkRadioForBasicScriptEdit = 1;
                            editor.setOption("mode", 'bat');
                        }
                        mainP.setTitle('编辑框---bat');
                        clickNotdoc();
                    }
                }
            }
        },
        {
            name: 'ra_s_type_win',
            width: 50,
            inputValue: '2',
            boxLabel: 'perl',
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                    	checkRadioForBasicScriptEdit = 2;
                        editor.setOption("mode", 'text/x-perl');
                        mainP.setTitle('编辑框---perl');
                        clickNotdoc();
                    }
                }
            }
        },
        {
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '3',
            boxLabel: 'python',
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                    	checkRadioForBasicScriptEdit = 3;
                        editor.setOption("mode", 'python');
                        mainP.setTitle('编辑框---python');
                        clickNotdoc();
                    }
                }
            }
        },{
            name: 'ra_s_type_win',
            width: 60,
            inputValue: '4',
            boxLabel: 'sql',
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                            editor.setOption("mode", 'text/x-plsql');
                            checkRadioForBasicScriptEdit = 4;
                            mainP.setTitle('编辑框---sql');
                            clickNotdoc();
                    }
                }
            }
        },{
            name: 'ra_s_type_win',
            width: 60,
            hidden:true,
            inputValue: '5',
            boxLabel: '文档',
            listeners: {
                click: {
                    element: 'el',
                    fn: function(value) {
                    	checkRadioForBasicScriptEdit = 5;
                        clickdoc();
                        /*
						 * authCombox.show(); docfile.show(); scName.hide();
						 * errExcepResult.hide(); excepResult.hide();
						 * 
						 * tryTestbtn.disabled =true; publishBtn.disabled =true;
						 * saveBtn.disabled =true;
						 */
                    }
                }
            }
        }]
    });
    
    var FieldContainerForOnecmd = new Ext.form.RadioGroup({
        fieldLabel: '脚本类型',
        labelWidth: 60,
        name: 'ra_s_type1',
        items: [{
            name: 'ra_s_type1',
            width: 60,
            inputValue: '0',
            boxLabel: 'shell',
            checked: true
        },
        {
            name: 'ra_s_type1',
            width: 50,
            inputValue: '1',
            boxLabel: 'bat'
        },
        {
            name: 'ra_s_type1',
            width: 50,
            inputValue: '2',
            boxLabel: 'perl'
        },
        {
            name: 'ra_s_type1',
            width: 60,
            inputValue: '3',
            boxLabel: 'python'
        }]
    });
    var agentPullChosedStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getTryAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var agentPullChosedStoreForOneCmd = Ext.create('Ext.data.Store', {
        fields: ['iid', 'agent'],
        autoLoad: true,
        proxy: {
            type: 'ajax',
            url: 'getTryAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var agentPullChosedCbForOneCmd = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentPullChosedForOneCmd',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择服务器--',
        store: agentPullChosedStoreForOneCmd
    });
    var agentPullChosedCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'agentPullChosed',
        labelWidth: 70,
        width: 170,
        queryMode: 'local',
        fieldLabel: '',
        displayField: 'agent',
        valueField: 'iid',
        editable: false,
        emptyText: '--请选择服务器--',
        store: agentPullChosedStore
    });
    
    var agentPullChosedStoreForCmd = Ext.create('Ext.data.Store', {
    	fields: ['iid', 'agentIp'],
    	autoLoad: true,
    	proxy: {
    		type: 'ajax',
    		url: 'getAllAgentListNoPage.do?flag=0',
    		reader: {
    			type: 'json',
    			root: 'dataList'
    		}
    	}
    });
    
    var agentPullChosedCbForCmd = Ext.create('Ext.form.field.ComboBox', {
    	name: 'agentPullChosedForCmd',
    	labelWidth: 70,
    	width: 120,
    	queryMode: 'local',
    	fieldLabel: '',
    	displayField: 'agentIp',
    	valueField: 'iid',
    	editable: true,
    	emptyText: '请选择服务器',
    	store: agentPullChosedStoreForCmd
    });
    
    var protocolTypeStore = Ext.create('Ext.data.Store', {
        fields: ['iid', 'name'],
        data : [
            {"iid":"1", "name":"SSH"},
            {"iid":"2", "name":"TELNET"}
        ]
    });
    
    var scriptfile = new  Ext.form.field.File({
		name: 'scriptfile', // 设置该文件上传空间的name，也就是请求参数的名字
		fieldLabel: '本地文件',
		labelWidth: 70,
		labelStyle:'margin:10px 0 0 0',
		padding: '0 8 0 0',
		msgTarget: 'side',
		anchor: '100%',
		height : 30,
		buttonText: '选择本地文件',
	    columnWidth: 1
    });
    
    var chooseServerTypeForConsole = Ext.create('Ext.form.field.ComboBox', {
    	name: 'chooseServerTypeForConsole',
    	width: 80,
    	displayField: 'name',
    	valueField: 'iid',
    	editable: false,
    	queryMode: 'local',
    	emptyText: '协议',
    	store: protocolTypeStore
    });
    
    var choosePortForConsole = new Ext.form.NumberField({
        name: 'choosePortForConsole',
        emptyText: '端口',
        width: 70
    });
    
    var userNameForConsole = new Ext.form.TextField({
    	name: 'userNameForConsole',
    	emptyText: '用户名',
    	width: 100
    });
    
    var userPasswordForConsole = new Ext.form.TextField({
    	name: 'userPasswordForConsole',
    	emptyText: '密码',
    	inputType: 'password',
    	width: 100
    });
    
    var sName = new Ext.form.TextField({
    	name: 'serverName',
    	fieldLabel: '服务名称',
    	afterLabelTextTpl: required,
    	displayField: 'serverName',
    	emptyText: '',
    	labelWidth: 70,
    	padding: '0 5 0 0',
    	columnWidth: .5
    });
    var docfile = new  Ext.form.field.File({
		name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
		fieldLabel: '选择文档',
		labelWidth: 70,
		labelStyle:'margin:10px 0 0 0',
		padding: '0 8 0 0',
		msgTarget: 'side',
		anchor: '100%',
		height : 30,
		buttonText: '浏览文档',
		hidden:true,
	    columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        afterLabelTextTpl: required,
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '适用平台',
        afterLabelTextTpl: required,
        displayField: 'text',
        valueField: 'value',
        editable: false,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['AIX', 'AIX'], ['Linux/AIX', 'Linux/AIX']]
        })
    });
    var driverType = Ext.create('Ext.form.field.ComboBox', {
    	name: 'driverType',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '驱动类型',
        afterLabelTextTpl: required,
        displayField: 'text',
        valueField: 'value',
        editable: false,
        emptyText: '--请选择类型--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['ORACLE', 'ORACLE'], ['MYSQL', 'MYSQL'], ['DB2', 'DB2']]
        })
    });
    var actExam = Ext.create('Ext.form.field.ComboBox', {
    	name: 'actExam',
        padding: '0 5 0 0',
        labelWidth: 70,
        columnWidth: .5,
        queryMode: 'local',
        fieldLabel: '执行审核',
        afterLabelTextTpl: required,
        displayField: 'text',
        valueField: 'value',
        editable: false,
        emptyText: '--请选择--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['1', '是'], ['0', '否']]
        })
    });
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 70,
        value:0,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 70,
        padding: '0 5 0 0',
        columnWidth: .5
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '请输入功能说明...',
        columnWidth: 1,
        height: 136,
        hidden:true,
        autoScroll: true,
        listeners: {
        	'blur': function( me, e, eOpts) {
        		scriptDesc = me.getValue();
        		funcDescInWin.setValue(scriptDesc);
        	}
        }
    });
    var funcDescInWin = Ext.create('Ext.form.field.TextArea', {
    	name: 'funcDescInWin',
    	fieldLabel: '功能说明',
    	displayField: 'funcDescInWin',
    	emptyText: '请输入功能说明...',
    	labelWidth: 70,
    	height: 136,
        columnWidth: 1,
    	autoScroll: true
    });
    var secondForm = [{
        border: false,
        layout: 'column',
        margin: '5',
        items: [sName, scName,authCombox]
    },{
        border: false,
        layout: 'column',
        margin: '5',
        items: [bussCb, bussTypeCb]
    },
    {
        layout: 'column',
        border: false,
        margin: '5',
        items: [excepResult, errExcepResult]
    },
    {
        layout: 'column',
        border: false,
        margin: '5',
        items: [usePlantForm]
    },
    {
        id:"driver",
    	hidden:false,
    	layout: 'column',
        border: false,
        margin: '5',
        items: [driverType]
    },{
        layout: 'column',
        border: false,
        margin: '5',
        items: [actExam]
    },{
        layout: 'column',
        border: false,
        margin: '5',
        items: [docfile]
    },     
    {
        layout: 'column',
        border: false,
        margin: '5',
        items: [scriptfile]
    },     
    {
        layout: 'column',
        border: false,
        margin: '5',
        items: [funcDescInWin]
    }];
     scriptForm = Ext.create('Ext.form.Panel', {
    	width: 590,
        height: 350,
        bodyCls : 'x-docked-noborder-top',
        border: false,
        layout: 'anchor',
        collapsible : false,
        items: secondForm
    });
    
     var funcDescForm = Ext.create('Ext.form.Panel', {
     	region: 'north',
         width: '100%',
         bodyCls : 'x-docked-noborder-top',
//         height: 168,
         border: true,
//         layout: 'anchor',
         margin: '0 0 0 0',
         collapsible : false,
         title: '基本信息',
         items: [{
        	 hidden:true,
             layout: 'column',
             border: false,
             items: [funcDesc]
         }],
         tools:[
            {
             type:'print',
             tooltip: '基本信息',
             handler: function(event, toolEl, panelHeader) {
             	if(!scName.getValue()) {
             		scName.setValue(scriptName);
             	}
             	if(!funcDescInWin.getValue()) {
             		funcDescInWin.setValue(scriptDesc);
             	}
             	//saveFromBottom = false;
             	if(checkRadioForBasicScriptEdit == 4){
             		Ext.getCmp("driver").setVisible(true);
             	}else{
             		Ext.getCmp("driver").setVisible(false);
             	}
             	baseInfoOfScriptWin.show();
             }
         },{
             type:'help',
             tooltip: '帮助',
             callback: function(panel, tool, event) {
             	window.open("scriptHelpDoc.do","resizable=yes").focus(); 
             }
         }]
     });
    
    if (!baseInfoOfScriptWin) {
		baseInfoOfScriptWin = Ext.create('widget.window', {
            title: '基本信息',
            closable: true,
            closeAction: 'hide',
           // tbar: [FieldContainer_win,'->'/* agentPullChosedCb,tryATrybtn */],
            resizable: false,
            modal: true,
            width: 600,
            minWidth: 350,
            height: 445,
            layout: {
                type: 'border',
                padding: 5
            },
            items: [scriptForm],
            buttons: [{ 
            	xtype: "button",
	  			text: "确定", 
	  			handler: function () { 
	  				if (saveFromBottom) {
	  					if(checkRadioForBasicScriptEdit==5){
	  						savedoc(scriptForm.getForm());
	  					/*
						 * scriptForm.getForm().submit({
						 * url:'uploadTemplate.do', waitMsg:'文件上传中',//提示等待信息
						 * success:function(){
						 * Ext.Msg.alert("success","文件上传成功"); } });
						 */
	  					}else{
	  						var importfile=scriptForm.getForm().findField("scriptfile").getValue();
	  						if(importfile!=''){
	  							scriptForm.getForm().submit({
	  								url: 'ajaxImportScript.do',
	  							    success: function(form, action) 
	  							    {
	  							    	var result = Ext.JSON.decode(action.response.responseText);
	  							    	var flag = result.success;// 提示信息
	  							    	var msg = result.message;// 提示信息
//	  									document.getElementById("code").value=msg;
	  							    	if(flag){
	  							    		editor.getDoc().setValue(msg);
		  					                editor.refresh();
	  							    	}
	  							    },
	  							    failure: function(form, action) {
	  							    }
	  							});
//		  						$.get(importfile).success(function(content){
//		  							alert(content);
//		  						}); 
		  					}
	  						var sysId = bussCb.getValue();
		  			        var bussTypeId = bussTypeCb.getValue();
		  			        var serverName = sName.getValue();
		  			        var scriptName1 = scName.getValue();
		  			        var up = usePlantForm.getValue() || "";
		  			        var scriptDesc1 = funcDescInWin.getValue();

		  			        if(!scriptName1) {
		  			    		scName.setValue(scriptName);
		  			    	}
		  			        if(!scriptDesc1) {
		  			        	funcDescInWin.setValue(scriptDesc);
		  			        }
		  			       if (serverName.trim() == '') {
		  			            Ext.MessageBox.alert("提示", "服务名称不能为空!");
		  			            return;
		  			        }
		  			        if (scriptName1.trim() == '') {
		  			            Ext.MessageBox.alert("提示", "脚本名称不能为空!");
		  			            return;
		  			        }
		  			        if (!sysId) {
		  			            Ext.MessageBox.alert("提示", "请选择脚本分类!");
		  			            return;
		  			        }
		  			        if (!bussTypeId) {
		  			            Ext.MessageBox.alert("提示", "请选择操作类型!");
		  			            return;
		  			        }
		  			        
		  			        if (up.trim() == '') {
		  			            Ext.MessageBox.alert("提示", "使用平台不能为空!");
		  			            return;
		  			        }
		  			        if (scriptDesc1.trim() == '') {
		  			        	Ext.MessageBox.alert("提示", "功能说明不能为空!");
		  			        	return;
		  			        }
	  						save(0);
	  					}
	  				} else {
	  					if(checkRadioForBasicScriptEdit!=5){
  						var importfile=scriptForm.getForm().findField("scriptfile").getValue();
  						if(importfile!=''){
  							scriptForm.getForm().submit({
  								url: 'ajaxImportScript.do',
  							    success: function(form, action) 
  							    {
  							    	var result = Ext.JSON.decode(action.response.responseText);
  							    	var flag = result.success;// 提示信息
  							    	var msg = result.message;// 提示信息
//  									document.getElementById("code").value=msg;
  							    	if(flag){
  							    		editor.getDoc().setValue(msg);
	  					                editor.refresh();
  							    	}
  							    },
  							    failure: function(form, action) {
  							    }
  							});
//	  						$.get(importfile).success(function(content){
//	  							alert(content);
//	  						}); 
	  					}
	  				}
	  					this.up("window").close();
	  				}
		        }
	  		}, { 
	  			xtype: "button", 
	  			text: "取消", 
	  			handler: function () {
	  				this.up("window").close();
	  			}
	  		}]
        });
    }

    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });

    var paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: newServiceId
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: newServiceId,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    attachmentStore.on('load', function(me, records, successful, eOpts) { 
    	$.each(records, function(index, record){
    		attachmentIds.push(record.get('iid'));
    	});
    	console.log(attachmentIds);
	});

    var paramTypeStore = Ext.create('Ext.data.Store', {
        fields: ['name'],
        data: [{
            "name": "IN-string"
        },
        {
            "name": "IN-int"
        },
        {
            "name": "IN-float"
        },
        {
            "name": "OUT-string"
        },
        {
            "name": "OUT-int"
        },
        {
            "name": "OUT-float"
        }]
    });

    var paramTypeCombo = Ext.create('Ext.form.field.ComboBox', {
        store: paramTypeStore,
        queryMode: 'local',
        forceSelection: true,
        // 要求输入值必须在列表中存在
        typeAhead: true,
        // 允许自动选择
        displayField: 'name',
        valueField: 'name',
        triggerAction: "all"
    });

    var paramColumns = [
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },{
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        editor: {
            allowBlank: false
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
        	 metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
             		+"<br>默认值："+record.get('paramDefaultValue')
             		+"<br>排序："+record.get('paramOrder')
             		+"<br>描述："+record.get('paramDesc')) 
             		+ '"'; 
            return value;  
        }
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 85,
        editor: paramTypeCombo,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
            		+"<br>默认值："+record.get('paramDefaultValue')
            		+"<br>排    序："+record.get('paramOrder')
            		+"<br>描    述："+record.get('paramDesc')) 
            		+ '"';
            return value;  
        }
    },
    {
        text: '默认值',
        dataIndex: 'paramDefaultValue',
        width: 80,
        editor: {
            allowBlank: true
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
        	 metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
             		+"<br>默认值："+record.get('paramDefaultValue')
             		+"<br>排序："+record.get('paramOrder')
             		+"<br>描述："+record.get('paramDesc')) 
             		+ '"';  
            return value;  
        }
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        editor: {
            allowBlank: true
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
        	 metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(" 类型："+record.get('paramType') 
             		+"<br>默认值："+record.get('paramDefaultValue')
             		+"<br>排序："+record.get('paramOrder')
             		+"<br>描述："+record.get('paramDesc')) 
             		+ '"'; 
            return value;  
        }
    }];
    
    function removeByValue(arr, val) {
	  for(var i=0; i<arr.length; i++) {
	    if(arr[i] == val) {
	      arr.splice(i, 1);
	      break;
	    }
	  }
    }
    
    var attachmentColumns = [/*
								 * { text: '序号', xtype: 'rownumberer', width: 40 },
								 */
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    /*
	 * { text: '附件大小', dataIndex: 'attachmentSize', width: 200 }, { text:
	 * '上传时间', dataIndex: 'attachmentUploadTime', flex: 1 },
	 */
    {
        menuDisabled: true,
        sortable: false,
        xtype: 'actioncolumn',
        width: 30,
        items: [{
            iconCls: 'attachment_delete',
            tooltip: '删除',
            handler: function(grid, rowIndex, colIndex) {
                var rec = attachmentStore.getAt(rowIndex);
                var a = [];
                a.push(rec.get('iid'));
                Ext.Ajax.request({
                    url: 'deleteScriptAttachment.do',
                    method: 'POST',
                    sync: true,
                    params: {
                        iids: a
                    },
                    success: function(response, request) {
                        var success = Ext.decode(response.responseText).success;
                        if (success) {
                            Ext.Msg.alert('提示', '删除成功！');
                            removeByValue(attachmentIds, rec.get('iid'));
                            attachmentStore.load();
                            if(newServiceId!=0){
            	              save(0);
            	              }
                        } else {
                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                        }
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "保存失败！");
                    }
                });
            }
        }]
    }];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
//    var selModelAtta = Ext.create('Ext.selection.CheckboxModel', {
//        checkOnly: true
//    });
    var paramGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        title: "脚本参数",
        store: paramStore,
        margin: '0 0 5 0',
        selModel: selModel,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns,
        emptyText: '没有脚本参数',
        tools: [{
        	    type:'plus',
            	tooltip: '增加',
                handler: function() {
                    paramStore.insert(0, new paramModel());
                    cellEditing.startEditByPosition({
                        row: 0,
                        column: 0
                    });
                }
            },
            {
            	type:'minus',
                tooltip: '删除',
                callback: function(panel, tool, event) {
                    var data = paramGrid.getView().getSelectionModel().getSelection();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的行!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要删除参数？", function(button, text) {
                            if (button == "yes") {
                            	var deleteIds = [];
                            	$.each(data, function(index, record){
                            		if(record.data.iid>0) {
                            			deleteIds.push(record.data.iid);
                            		}else{
                            		    paramStore.remove(data);
                            		}
                            	});
                            if(deleteIds.length>0){
                            	Ext.Ajax.request({
                                    url: 'deleteScriptParams.do',
                                    method: 'POST',
                                    sync: true,
                                    params: {
                                    	iids: deleteIds
                                    },
                                    success: function(response, request) {
                                        var success = Ext.decode(response.responseText).success;
                                        if (success) {
                                            Ext.Msg.alert('提示', '删除成功！');
                                            paramStore.load();
                                        } else {
                                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        }
                                    },
                                    failure: function(result, request) {
                                        secureFilterRs(result, "保存失败！");
                                    }
                                });
                             }else{
                             	paramGrid.getView().refresh();
                             }
                            }
                        });
                    }
                }
            }]
    });
    
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	region: 'south',
    	cls: 'attachments',
        height: 250,
        title: '附件',
        store: attachmentStore,
// selModel: selModelAtta,
        border: true,
        columnLines: true,
        columns: attachmentColumns,
        emptyText: '没有附件',
        tools:[{
            type:'plus',
            tooltip: '增加',
            id: 'uploadify-base'
        }]
    });

    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
    	region: 'east',
        border: false,
        width: 320,
        height: contentPanel.getHeight(),
        layout: 'border',
//        tbar: ['->',{
//            text: '帮助文档',
//            cls: 'Common_Btn',
//            handler: function(){
//            	window.open("scriptHelpDoc.do","resizable=yes").focus(); 
//            }
//        },
//        {
//            text: '基本信息',
//            cls: 'Common_Btn',
//            handler: function(){
//            	if(!scName.getValue()) {
//            		scName.setValue(scriptName);
//            	}
//                if(checkRadioForBasicScriptEdit==5){
//                	saveFromBottom = true;
//                }else {
//                	saveFromBottom = false;
//                }
//            	baseInfoOfScriptWin.show();
//            }
//        }],
        items: [funcDescForm,paramGrid,attachmentGrid]
    });
    
    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '资源组',
			    emptyText : '--请选择资源组--',
			    labelAlign : 'right',
	            labelWidth : 70,
	            width : '25.5%',
	            columnWidth:1,
			    multiSelect: true,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
			    	listeners: {
			    	      change: function( comb, newValue, oldValue, eOpts ) {
			    	    	  /*
								 * chosedResGroups_forest = new Array(); for(var
								 * i=0;i<newValue.length;i++) {
								 * chosedResGroups_forest.push(newValue[i]); }
								 */
			    	    	 // agent_store.load();
			    	      	pageBar.moveFirst();
			    	      }
			    	}
	});
	
	var app_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'appNameModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentAppNameList.do?envType=0',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var app_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
		name : 'appname',
	    fieldLabel: "应用名称",
	    emptyText : '--请选择应用名称--',
	    store: app_name_store,
	    queryMode: 'local',
	    width: "25%",
	    displayField: 'appName',
	    valueField: 'appName',
	    labelWidth : 70,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
	
	var sys_name_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		model: 'sysNameModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentSysNameList.do?envType=0',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	
	var sys_name = Ext.create('Ext.form.ComboBox', {
//	    editable: false,
		name : 'sysname',
	    fieldLabel: "系统名称",
	    emptyText : '--请选择系统名称--',
	    store: sys_name_store,
	    queryMode: 'local',
	    width: "25%",
	    displayField: 'sysName',
	    valueField: 'sysName',
	    labelWidth : 70,
		labelAlign : 'right',
		listeners: {
			beforequery : function(e){
	            var combo = e.combo;
	              if(!e.forceAll){
	              	var value = Ext.util.Format.trim(e.query);
	              	combo.store.filterBy(function(record,id){
	              		var text = record.get(combo.displayField);
	              		return (text.toLowerCase().indexOf(value.toLowerCase())!=-1);
	              	});
	              combo.expand();
	              return false;
	              }
	         }
		}
	  });
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '主机名称',
		displayField : 'hostname',
		emptyText : '--请输入主机名称--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25%'
	});
	
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '系统类型',
		displayField : 'ostype',
		emptyText : '--请输入系统类型--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25%'
	});
	
	var agent_ip = new Ext.form.TextField({
		name : 'agentIp',
		fieldLabel : 'Agent IP',
		displayField : 'agentIp',
		emptyText : '--请输入Agent IP--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '25.5%'
	});
	
	
	var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 70,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '25.2%',
		labelAlign : 'right'
	});
	
	var search_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ sys_name, app_name, host_name, os_type
			]
		},
		{
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [ agent_ip, resourceGroupObj, agentStatusCb, 
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function(){
						pageBar.moveFirst();
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function(){
						agent_ip.setValue('');
				    	app_name.setValue('');
						sys_name.setValue('');
						host_name.setValue('');
						os_type.setValue('');
				    	resourceGroupObj.setValue('');
				    	agentStatusCb.setValue('');
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查看已选择服务器',
					hidden: true,
					handler : function(){
						if(!chosedAgentWin) {
							chosedAgentWin = Ext.create('Ext.window.Window', {
						  		title : '已选择服务器',
						  		autoScroll : true,
						  		modal : true,
						  		resizable : false,
						  		closeAction : 'hide',
						  		width : contentPanel.getWidth()-250,
						  		height : 530,
						  		items:[agent_grid_chosed],
						  		buttonAlign: 'center',
						  		buttons: [{ 
						  			xtype: "button", 
						  			text: "关闭", 
						  			handler: function () {
						  				this.up("window").close();
						  			}
						  		}]
						  	});
						}
						chosedAgentWin.show();
						agent_store_chosed.load();
					}
				}
			]
		}]
	});
	
//	var search_form = Ext.create('Ext.form.Panel', {
//    	layout : 'anchor',
//    	height : 60,
//    	region : 'north',
//    	buttonAlign : 'center',
//    	border : false,
//	    items: [{
//	    	layout:'form',
//	    	anchor:'95%',
//	    	padding : '5 0 5 0',
//	    	border : false,
//	    	items: [{
//	    		layout:'column',
//		    	border : false,		    	
//	    		items:[resourceGroupObj,{
//		            fieldLabel: 'IP',
//		            labelAlign : 'right',
//		            labelWidth : 70,
//		            margin : '0 10 0 10',
//		            name: 'agentIp',
//		            columnWidth:.45,
//		            xtype: 'textfield'
//		        },{
//		            fieldLabel: 'Agent描述',
//		            labelAlign : 'right',
//		            labelWidth : 70,
//		            hidden: true,
//		            margin : '0 10 0 10',
//		            name: 'agentDesc',
//		            columnWidth:.45,
//		            xtype: 'textfield'
//		        },{
//					xtype: 'button',
//// cls:'Common_Btn',
//					margin: '0 5 0 0',
//					text : '查询',
//					handler : function() {
//						pageBar.moveFirst();
//					}
//				},{
//					xtype: 'button',
//// cls:'Common_Btn',
//					margin: '0 5 0 0',
//					text : '清空',
//					handler : function() {
//						clearQueryWhere();
//					}
//				}]
//	    	}]
//	    }]
//	});
	
    

	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'sysName',     type: 'string'},
            {name: 'appName',     type: 'string'},
            {name: 'hostName',     type: 'string'},
            {name: 'osType',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
	Ext.define('dsModel', {
		extend: 'Ext.data.Model',
		idProperty: 'dsId',
		fields: [
			{name : 'dsId', type : 'int'},  
			{name : 'dsIp', type : 'String'},  
			{name : 'dsName', type : 'String'},
			{name : 'dsUser', type : 'String'},
			{name : 'dsPwd', type : 'String'}, 
			{name : 'dsRole', type : 'String'},
			{name : 'dsIns', type : 'String'}, 
			{name : 'dsUrl', type : 'String'}, 
			{name : 'dsType', type : 'String'}
			]
	});
    
	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: false,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
	var dsinfo_store = Ext.create('Ext.data.Store', {
		autoLoad: false,
		pageSize: 50,
		model: 'dsModel',
		proxy: {
			type: 'ajax',
			url: 'getDsInfoByDsId.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
    
//    var agent_columns = [{ text: '主键',  dataIndex: 'iid',hidden:true},
//        { text: 'IP',  dataIndex: 'agentIp',flex:1},
//        { text: '端口号',  dataIndex: 'agentPort',width:180},
//        { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden:true},
//        { text: '状态',  dataIndex: 'agentState',width:180,renderer:function(value,p,record){
//        	var backValue = "";
//        	if(value==0){
//        		backValue = "Agent正常";
//        	}else if(value==1){
//        		backValue = "Agent异常";
//        	}
//        	return backValue;
//        }}];
		var agent_columns = [
	        { text: '主键',  dataIndex: 'iid',hidden:true},
	        { text: '系统名称',  dataIndex: 'sysName',flex:1},
	        { text: '应用名称',  dataIndex: 'appName',flex:1},
	        { text: '主机名称',  dataIndex: 'hostName',flex:1},
	        { text: 'IP',  dataIndex: 'agentIp',width:150},
	        { text: '端口号',  dataIndex: 'agentPort',width:100},
	        { text: '系统类型',  dataIndex: 'osType',width:140},
	        { text: '描述',  dataIndex: 'agentDesc',flex:1,hidden: true,
	        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
	                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
	                return value;  
	            }
	        },
	        { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
	        	var backValue = "";
	        	if(value==0){
	        		backValue = "Agent正常";
	        	}else if(value==1){
	        		backValue = "Agent异常";
	        	}
	        	return backValue;
	        }}
	       ];
    var ds_columns = [
    	{ text: '主键',  dataIndex: 'dsId',hidden:true},
    	{ text: 'DSIP',  dataIndex: 'dsIp',hidden:true},
    	{ text: '数据源名称',  dataIndex: 'dsName',width:100},
    	{ text: '用户',  dataIndex: 'dsUser',width:90},
    	{ text: '密码',  dataIndex: 'dsPwd',hidden:true},
    	{ text: '角色',  dataIndex: 'dsRole',hidden:true},
    	{ text: '实例名',  dataIndex: 'dsIns',hidden:true},
    	{ text: '类型',  dataIndex: 'dsType',width:50},
    	{ text: 'URL',  dataIndex: 'dsUrl',flex:1}
    	];
    
    agent_store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	agentIp : Ext.util.Format.trim(agent_ip.getValue()),
	    	appName : app_name.getValue()==null?'':Ext.util.Format.trim(app_name.getValue()+""),
			sysName : sys_name.getValue()==null?'':Ext.util.Format.trim(sys_name.getValue()+""),
			hostName : Ext.util.Format.trim(host_name.getValue()),
			osType : Ext.util.Format.trim(os_type.getValue()),
	    	rgIds:resourceGroupObj.getValue(),
	    	agentState: agentStatusCb.getValue(),
	    	flag: 0
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    var pageBar = Ext.create('Ext.PagingToolbar', {
    	store: agent_store,
        dock: 'bottom',
        displayInfo: true
    });
    
    var agent_grid = Ext.create('Ext.grid.Panel', {
    	region : 'west',
 	    width:'100%',
	    store:agent_store,
	    border:true,
	    columnLines : true,
	    columns:agent_columns,
	    bbar: pageBar,
	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true}),
	    listeners :{
			select: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.push(record.get('iid'));
            	}
            },
	        deselect: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
            		chosedAgentIds.remove(record.get('iid'));
            	}
            }
		}
	});
    agent_store.on('load', function(store, options) {
    	agent_grid.getSelectionModel().select(0);  
    });
    var db_soucre_grid = Ext.create('Ext.grid.Panel', {
	    store:dsinfo_store,
	    width:'70%',
	    region : 'center',
	    border:true,
	    columnLines : true,
	    columns:ds_columns
	});
    dsinfo_store.on('load', function(store, options) {
    	db_soucre_grid.getSelectionModel().select(0);  
    });
    if(chooseTestAgentWin) {
    	chooseTestAgentWin.show();
	} else {
		var grid_panel = Ext.create('Ext.panel.Panel', {
			width : contentPanel.getWidth()-252,
			height : contentPanel.getHeight()-150,
			layout : 'border',
			header : false,
			border : false,
			// items : [search_form, agent_grid, db_soucre_grid ]
			items : [search_form, agent_grid]
		});
    	chooseTestAgentWin = Ext.create('Ext.window.Window', {
	  		title : '选择测试服务器',
	  		autoScroll : true,
	  		modal : true,
	  		resizable : false,
	  		closeAction : 'hide',
	  		width : contentPanel.getWidth()-250,
	  		height : contentPanel.getHeight()-60,
	  		items:[grid_panel],
	  		buttons: [{ 
	  			xtype: "button",
// cls:'Common_Btn',
	  			text: "确定", 
	  			handler: function () {
	  				var type = "sh";
	  		        if (checkRadioForBasicScriptEdit == '0') {
	  		            type = "sh";
	  		        } else if (checkRadioForBasicScriptEdit == '1') {
	  		            type = "bat";
	  		        } else if (checkRadioForBasicScriptEdit == '2') {
	  		            type = "perl";
	  		        } else if (checkRadioForBasicScriptEdit == '3') {
	  		            type = "py";
	  		        }else if (checkRadioForBasicScriptEdit == '4') {
	  		            type = "sql";
	  		        }
	  				var me = this;
	  				var records = agent_grid.getSelectionModel().getSelection();
	  				var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
	  				if(records.length!=1) {
	  					Ext.Msg.alert('提示', '请选择记录，并且只能选择一条记录！');
	  					return;
	  				}
	  				if(checkRadioForBasicScriptEdit==4){
		  				if(dsrecords.length!=1){
		  				Ext.Msg.alert('提示', '请选择数据源，并且只能选择一个！');
		  					return;
		  				}
		  				}
	  				if(whichButtonIsClicked==1) { // 尝试一下

		  				
		  				Ext.Ajax.request({
		  		            url: 'tryATry.do',
		  		            method: 'POST',
		  		            sync: true,
		  		            params: {
		  		                scriptName: scriptName,
		  		                scriptType: type,
		  		                scriptContent: document.getElementById('code').value,
		  		                scriptPara: scriptPara,
		  		                scriptAttachmentIds: attachmentIds,
		  		                agentId: records[0].get('iid')
		  		            },
		  		            success: function(response, request) {
		  		                var success = Ext.decode(response.responseText).success;
		  		                var message = Ext.decode(response.responseText).message;
		  		                if(success) {
		  		                	isFromTryATry = 1;
		  		                	tryRequestId = Ext.decode(response.responseText).requestId;
		  		                	tryAgentIp = Ext.decode(response.responseText).agentIp;
		  		                	tryAgentPort = Ext.decode(response.responseText).agentPort;
		  		                	
		  		                	if (refreshTryForBasic) {
			  		              		clearInterval(refreshTryForBasic);
			  		              	}
		  		                	
		  		                	refreshTryForBasic = setInterval(function() {
			  		              		loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
			  		              	}, 5 * 1000);
		  		                	
		  		                	me.up("window").close();
		    		  				settime(tryATrybtn, '尝试');
		  		                	
		  		                }
		  		                Ext.Msg.alert('提示', message);
		  		                
		  		            },
		  		            failure: function(result, request) {
		  		                secureFilterRs(result, "出现错误！");
		  		            }
		  		        });
	  				} else if(whichButtonIsClicked==2) {
		  				var jsonData = "[" + Ext.JSON.encode(records[0].data) + "]";
		  				var dsrecords = db_soucre_grid.getSelectionModel().getSelection();
		  				var dsid=0;
		  				if(dsrecords.length>0){
		  					dsid =  Ext.JSON.encode(dsrecords[0].data.dsId);
		  				}
		  				var scriptPara = getParams();
		  				var m = paramStore.getRange(0, paramStore.getCount()-1);   
		  		        var jsonDataPara = "[";
		  		        for (var i = 0, len = m.length; i < len; i++) {
		  		            var ss = Ext.JSON.encode(m[i].data);
		  		            if (i == 0) jsonDataPara = jsonDataPara + ss;
		  		            else jsonDataPara = jsonDataPara + "," + ss;
		  		        }
		  		      jsonDataPara = jsonDataPara + "]";
		  		  	
		  				Ext.Ajax.request({
			      			url : 'execScriptServiceForSync.do',
			      			method : 'POST',
			      			params : {
			      				serviceId: newServiceId,
			      				execUser: "",
			      				scriptPara: scriptPara,
			      				jsonDataPara: jsonDataPara,
			      				jsonData:jsonData,
			      				dbsourceid:dsid,
			      				ifrom :0,
			      				flag: 0
			      			},
			      			success : function(response, request) {
// var coatId = Ext.decode(response.responseText).coatId;
// var flowId = Ext.decode(response.responseText).flowId;
			      				var requestId = Ext.decode(response.responseText).requestIds[0];
			      				isFromTryATry = 0;
			      				tryRequestId = requestId;
	  		                	tryAgentIp = records[0].get('agentIp');
	  		                	tryAgentPort = records[0].get('agentPort');
	  		                	
	  		                	if (refreshTryForBasic) {
		  		              		clearInterval(refreshTryForBasic);
		  		              	}
	  		                	
	  		                	refreshTryForBasic = setInterval(function() {
		  		              		loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
		  		              	}, 5 * 1000);
	  		                	
	  		                	me.up("window").close();
	  		                	$('#consoleLog').html('');
	    		  				settime(tryTestbtn, '测试');
	    		  				Ext.Msg.alert('提示', "脚本已在指定服务器上运行！");
			      			},
			      			failure : function(result, request) {
			      				Ext.Msg.alert('提示', '执行失败！');
			      				$this.html('执行脚本');
			      			}
			      		});
	  				} else if(whichButtonIsClicked==3) {
	  					agentChosedId = records[0].get('iid');
	  					var agentChosedIp = records[0].get('agentIp');
	  					var agentChosedPort = records[0].get('agentPort');
	  					$('#c-a-i').html('已选服务器 '+agentChosedIp+':'+agentChosedPort);
	  					Ext.Ajax.request({
			      			url : 'getAgentOs.do',
			      			method : 'POST',
			      			params : {
			      				agentIp: agentChosedIp,
			      				agentPort: agentChosedPort
			      			},
			      			success : function(response, request) {
			      				var agentOs = Ext.decode(response.responseText).agentOs;
			      				$('#c-a-i').html($('#c-a-i').html()+ " " + agentOs);
			      			},
			      			failure : function(result, request) {
			      			}
			      		});
	  					
	  					me.up("window").close();
	  				}
		        }
	  		}, { 
	  			xtype: "button", 
// cls:'Gray_button',
	  			text: "取消", 
	  			handler: function () {
	  				this.up("window").close();
	  			}
	  		}]
	  	});
	}
    
    function clearQueryWhere(){
    	resourceGroupObj.setValue('');
    	search_form.getForm().findField("agentIp").setValue('');
    	search_form.getForm().findField("agentDesc").setValue('');
    }
    
	    function tryATry() {
	    		editor.save();
	    		 if (checkRadioForBasicScriptEdit == '4') {
	    			 Ext.Msg.alert('提示', 'sql类型脚本暂不支持尝试功能！');
		        		return;
			        }
	        	var content = document.getElementById('code').value;
	        	if(!content) {
	        		Ext.Msg.alert('提示', '请填入脚本内容！');
	        		return;
	        	}
	        	
	            var agentId = agentPullChosedCb.getValue();
	            if(!agentId) {
	            	Ext.Msg.alert('提示', '请选择服务器！');
	                return;
	            }
		        function tryReal () {
		        	if(paramInvalidMessage) {
						Ext.MessageBox.alert("提示", paramInvalidMessage);
					} else {
		            whichButtonIsClicked = 1;
		            var scriptPara = getParams();
		            var type = "sh";
			        if (checkRadioForBasicScriptEdit == '0') {
			            type = "sh";
			        } else if (checkRadioForBasicScriptEdit == '1') {
			            type = "bat";
			        } else if (checkRadioForBasicScriptEdit == '2') {
			            type = "perl";
			        } else if (checkRadioForBasicScriptEdit == '3') {
			            type = "py";
			        }else if (checkRadioForBasicScriptEdit == '4') {
			            type = "sql";
			        }
					Ext.Ajax.request({
			            url: 'tryATry.do',
			            method: 'POST',
			            sync: true,
			            params: {
			                scriptName: 'script',
			                scriptType: type,
			                scriptContent: document.getElementById('code').value,
			                scriptPara: scriptPara,
			                scriptAttachmentIds: attachmentIds,
			                agentId: agentId
			            },
			            success: function(response, request) {
			                var success = Ext.decode(response.responseText).success;
			                var message = Ext.decode(response.responseText).message;
			                if(success) {
			                	isFromTryATry = 1;
			                	tryRequestId = Ext.decode(response.responseText).requestId;
			                	tryAgentIp = Ext.decode(response.responseText).agentIp;
			                	tryAgentPort = Ext.decode(response.responseText).agentPort;
			                	
			                	if (refreshTryForBasic) {
				              		clearInterval(refreshTryForBasic);
				              	}
				                	
				                refreshTryForBasic = setInterval(function() {
				              		loadShelloutputhisInfo(tryRequestId, tryAgentIp, tryAgentPort);
				              	}, 5 * 1000);
				                	
				  				settime(tryATrybtn, '尝试');
			                	
			                }
			                Ext.Msg.alert('提示', message);
			                
			            },
			            failure: function(result, request) {
			                secureFilterRs(result, "出现错误！");
			            }
			        });
		           }
		         }
		        var p = orgParams();
	        	var paramInvalidMessage = p.paramInvalidMessage;
	        	var someParamIsEmpty = p.someParamIsEmpty;
	        	if(someParamIsEmpty) {
	        		Ext.MessageBox.buttonText.yes = "确定"; 
	        		Ext.MessageBox.buttonText.no = "取消"; 
	        		Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function(id){
	        			if(id=='yes') {
	        				tryReal();
	        			}
	        		});
	        	} else {
	        		tryReal();
	        	}
	    }
    
    function tryCmd() {
    	var content = cmdContent.getValue();
    	if(!content) {
    		Ext.Msg.alert('提示', '请填入命令！');
    		return;
    	}
        
        var agentId = agentPullChosedCbForOneCmd.getValue();
        if(!agentId) {
        	Ext.Msg.alert('提示', '请选择服务器！');
            return;
        }

        var typeValue = FieldContainerForOnecmd.getValue( )['ra_s_type1'];
        var type = "sh";
        if (typeValue == '0') {
            type = "sh";
        } else if (typeValue == '1') {
            type = "bat";
        } else if (typeValue == '2') {
            type = "perl";
        } else if (typeValue == '3') {
            type = "py";
        }
		
		Ext.Ajax.request({
            url: 'tryATry.do',
            method: 'POST',
            sync: true,
            params: {
                scriptName: 'scriptCmd',
                scriptType: type,
                scriptContent: content,
                agentId: agentId
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if(success) {
                	cmdRequestId = Ext.decode(response.responseText).requestId;
                	cmdAgentIp = Ext.decode(response.responseText).agentIp;
                	cmdAgentPort = Ext.decode(response.responseText).agentPort;
                	
                	if (refreshCmdForBasic) {
	              		clearInterval(refreshCmdForBasic);
	              	}
	                	
                	refreshCmdForBasic = setInterval(function() {
                		loadShelloutputhisInfoCmd(cmdRequestId, cmdAgentIp, cmdAgentPort);
	              	}, 4 * 1000);
	                	
	  				settime(cmdbtn, '执行');
	  				$('#consoleLogForCmd').html("");
                }
                Ext.Msg.alert('提示', message);
                
            },
            failure: function(result, request) {
                secureFilterRs(result, "出现错误！");
            }
        });
    }
    
    var tryATrybtn = Ext.create('Ext.Button', {
        text: '尝试',
        width: 80,
        cls: 'Common_Btn',
        handler: function() {
				tryATry();
        }
    });
    
    var cmdbtn = Ext.create('Ext.Button', {
        text: '执行',
        width: 80,
        cls: 'Common_Btn',
        handler: function() {
        	tryCmd();
        }
    });
    
    function settime(btn, text) { 
    	if (countdown == 0) { 
    		btn.setDisabled(false);   
    		btn.setText(text);
    		btn.setWidth(80);
	    	countdown = 10; 
    	} else { 
    		btn.setDisabled(true);  
    		btn.setText(text+"(" + countdown + ")");
    		btn.setWidth(100);
	    	countdown--; 
	    	setTimeout(function() { 
	    		settime(btn, text) 
	    	},1000) 
    	} 
    	
    } 
    var cmdStr = new Ext.form.TextField({
    	width:'40%',
		labelWidth : 70,
		fieldLabel : 'CMD',
		margin :'5,0,5,0',
		listeners : {
			specialkey : function(textfield, e) {
				if (e.getKey() == Ext.EventObject.ENTER) {
					 cmdVForbasicEdit = cmdStr.getValue();
					cmdStr.setValue("");
				}
			}
		}
	});
    var mainP = Ext.create('Ext.panel.Panel', {
    	minHeight: 80,
        border: false,
        autoScroll: true,
        title: "编辑框---shell",
        height: contentPanel.getHeight(),
        html: '<textarea id="code" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>',
        tbar: [FieldContainer_win,'->', agentPullChosedCb,tryATrybtn],
        bbar: [cmdStr,'->',{ xtype: 'tbtext', text: '提示：当光标位于编辑器内部时，可以使用F11按键进入<b>全屏编辑模式</b>' }]
    });
    
//    var cmdField = new Ext.form.TextField({
//        name: 'cmd',
//        emptyText: '请输入命令',
//        width: 280
//    });
    
    var sTStore = Ext.create('Ext.data.Store', {
	    fields: ['id', 'name'],
	    data : [
	        {"id":"bat", "name":"BAT"},
	        {"id":"sh", "name":"SHELL"},
	        {"id":"py", "name":"PYTHON"},
	        {"id":"perl", "name":"PERL"}
	    ]
	});
    
    var sTCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'stcb',
        padding: '0 5 0 0',
        queryMode: 'local',
        displayField: 'name',
        valueField: 'id',
        editable: false,
        emptyText: '',
        value: 'bat',
        width: 90,
        store: sTStore
    });
    
    var execCmdBtn = Ext.create('Ext.Button', {
        text: '连接',
        cls: 'Common_Btn',
        handler: function() {
        	var agentIp = agentPullChosedCbForCmd.getRawValue() || ''; 
        	var protocol = chooseServerTypeForConsole.getValue() || '';
        	var shellPort = (choosePortForConsole.getValue() || '') + "";
        	var shellUserName = userNameForConsole.getValue() || '';
        	var shellUserPassword = userPasswordForConsole.getValue() || '';
        	if(Ext.isEmpty(Ext.util.Format.trim(agentIp))) {
        		Ext.Msg.alert('提示', '请选择服务器！');
        		return;
        	}
        	if(Ext.isEmpty(Ext.util.Format.trim(protocol))) {
        		Ext.Msg.alert('提示', '请选择协议！');
        		return;
        	}
        	if(Ext.isEmpty(Ext.util.Format.trim(shellPort))) {
        		Ext.Msg.alert('提示', '请填写端口号！');
        		return;
        	} else {
        		if(!checkIsInteger(shellPort)) {
        			Ext.Msg.alert('提示', '端口号必须为正整数！');
            		return;
        		}
        	}
        	if(Ext.isEmpty(Ext.util.Format.trim(shellUserName))) {
        		Ext.Msg.alert('提示', '请填写用户名！');
        		return;
        	}
        	if(Ext.isEmpty(Ext.util.Format.trim(shellUserPassword))) {
        		Ext.Msg.alert('提示', '请填写密码！');
        		return;
        	}
        	
        	var options = {
                host: agentIp,
                port: shellPort,
                username: shellUserName,
                password: shellUserPassword
            }
        	connect(protocol, options);
        }
    });
    var chooseAgentBtnForCmd = Ext.create('Ext.Button', {
        text: '选择服务器',
        cls: 'Common_Btn',
        handler: function() {
        	whichButtonIsClicked = 3;
        	chooseTestAgentWin.show();
        	resourceGroupObj.setValue('');
    		search_form.getForm().findField("agentIp").setValue('');
        	search_form.getForm().findField("agentDesc").setValue('');
        	agent_store.load();
        }
    });
    
    var tapPanelForConsole = Ext.create("Ext.tab.Panel", {
		activeTab : 0, // 默认激活第几个tab页
		border: false,
		bodyStyle: 'background:#000;',
		plugins : Ext.create('Ext.ux.TabCloseMenu', {
			closeTabText : '关闭本页',
			closeOthersTabsText : '关闭其他',
			closeAllTabsText : '关闭全部',
			closeLeftAllTabsText : '关闭左侧全部',
			closeRightAllTabsText : '关闭右侧全部'
		}),
		listeners : {
			'tabchange' : function(tab, newc, oldc) {
// var termold = $('#term-create-'+ hex_md5(newc.title)).data('term');
// if(termold) {
// termold.focus();
// }
			},
			'beforeremove' : function(tabs, tab) {
				var termold = $('#term-create-'+ hex_md5(tab.title)).data('term');
				var clientold = $('#term-create-'+ hex_md5(tab.title)).data('client');
				if(termold) {
					termold.destroy();
				}
				if(clientold) {
					clientold.close();
				}
			}
		}
	});
    
    var consoleOneCmdPanel = Ext.create('Ext.panel.Panel', {
	      border: true,
	      autoScroll: true,
	      bodyStyle: 'background:#000;',
	      title: "终端",
	      tbar: [agentPullChosedCbForCmd,chooseServerTypeForConsole,choosePortForConsole,userNameForConsole,userPasswordForConsole,execCmdBtn],
	      items: [tapPanelForConsole]
  });
    var cmdContent = new Ext.form.TextField({
		name : 'cmdContent',
//		fieldLabel: '命令',
		emptyText : '--请输入命令--',
//		labelWidth : 50,
		width : '85%',
        labelAlign : 'right'
	});
    
    var consoleCmdPanel = Ext.create('Ext.panel.Panel', {
    	region: 'center',
	    border: false,
	    autoScroll: true,
	    title: '日志',
	    html: '<pre id="consoleLogForCmd" style="height:100%;background: #4b4b4b;color: white;margin:0;"></pre>'
	  });
    
    var oneCmdPanel = Ext.create('Ext.panel.Panel', {
	      border: false,
	      autoScroll: true,
	      title: "单指令测试",
	      layout: 'border',
	      items: [consoleCmdPanel],
	      dockedItems: [{
	            xtype: 'toolbar',
	            dock : 'top',
				border: false,
	            items: [FieldContainerForOnecmd,'->', agentPullChosedCbForOneCmd]
	            },{
	            xtype: 'toolbar',
	            dock : 'top',
				border: false,
		            items: [cmdContent,'->',cmdbtn]
		        }]
});
    
    //左侧Panel的上半部分，编写脚本的Panel
    var tabsCenter = Ext.createWidget('tabpanel', {
    	// activeTab: 1, //指定默认的活动tab
    	region: 'center',
    	minHeight: 80,
        height: contentPanel.getHeight(),
        plain: true,                        // True表示tab候选栏上没有背景图片（默认为false）
        enableTabScroll: true,              // 选项卡过多时，允许滚动
        defaults: { autoScroll: true },
        border:true,
        items: [mainP,  consoleOneCmdPanel,oneCmdPanel]
    });
    
    var importPanel = Ext.create('Ext.panel.Panel', {
    	region: 'south',
    	height: 150,
    	border: false,
    	collapsible : true,
    	autoScroll: true,
    	title: '日志',
    	html: '<pre id="consoleLog" style="height:100%;background: #4b4b4b;color: white;margin:0;"></pre>'
    });
    var consolePanel = Ext.create('Ext.panel.Panel', {
	  	region: 'south',
	  	height: 150,
	    border: true,
	    collapsible : false,
	    autoScroll: true,
	    title: '日志',
	    html: '<pre id="consoleLog" style="height:100%;background: #4b4b4b;color: white;margin:0;"></pre>'
	  });
    var tryTestbtn = Ext.create('Ext.Button', {
        
        text: '测试',
        handler: function() {
        	editor.save();
        	 var content = document.getElementById('code').value;
        	 if(content.trim() == ''){
        		 Ext.MessageBox.alert("提示", "测试脚本内容不能为空!");
        		 return ;
        	 }
        	if(checkRadioForBasicScriptEdit==4){
    			agent_grid.setWidth((contentPanel.getWidth()-250)/2);
    			db_soucre_grid.setWidth((contentPanel.getWidth()-250)/2);
    			db_soucre_grid.show();
      		}else{
      			agent_grid.setWidth((contentPanel.getWidth()-250));
      			db_soucre_grid.hide();
      		}
        	var p = orgParams();
        	var paramInvalidMessage = p.paramInvalidMessage;
        	var someParamIsEmpty = p.someParamIsEmpty;
        	if(isAuditing()) {
        		Ext.MessageBox.buttonText.yes = "确定"; 
        		Ext.MessageBox.buttonText.no = "取消"; 
        		Ext.Msg.confirm("请确认", "该原子脚本处于审核中，修改的内容不会保存，是否继续测试？", function(id){
        			if(id=='yes') {
                    	if(someParamIsEmpty) {
                    		Ext.MessageBox.buttonText.yes = "确定"; 
                    		Ext.MessageBox.buttonText.no = "取消"; 
                    		Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function(id){
                    			if(id=='yes') {
                    				if(paramInvalidMessage) {
                    					Ext.MessageBox.alert("提示", paramInvalidMessage);
                    				} else {
                    					testScript();
                    				}
                    			}
                    		});
                    	} else {
                    		if(paramInvalidMessage) {
            					Ext.MessageBox.alert("提示", paramInvalidMessage);
            				} else {
            					testScript();
            				}
                    	}
        			}
        		});
        	} else {
            	if(someParamIsEmpty) {
            		Ext.MessageBox.buttonText.yes = "确定"; 
            		Ext.MessageBox.buttonText.no = "取消"; 
            		Ext.Msg.confirm("请确认", "参数没有填写默认值，是否进行测试？", function(id){
            			if(id=='yes') {
            				if(paramInvalidMessage) {
            					Ext.MessageBox.alert("提示", paramInvalidMessage);
            				} else {
            					save(0, testScript, '测试');
            				}
            			}
            		});
            	} else {
            		if(paramInvalidMessage) {
    					Ext.MessageBox.alert("提示", paramInvalidMessage);
    				} else {
    					save(0, testScript, '测试');
    				}
            	}
        	}
        }
    });
    var publishBtn = Ext.create('Ext.Button', {
    	text: '发布',
    	 handler: function(){
    		 editor.save();
           	 var content = document.getElementById('code').value;
           	 if(content.trim() == ''){
           		 Ext.MessageBox.alert("提示", "脚本内容不能为空!");
           		 return ;
           	 }
           	if(isAuditing()) {
        		Ext.MessageBox.alert("提示", "该原子脚本处于审核中，不能再次发布！");
        	} else {
        		save(0, publishScript, '发布');
        	}
         }
    });
    var saveBtn = Ext.create('Ext.Button', {
    	text: '保存',
    	 handler: function(){
    		 if(isAuditing()) {
         		Ext.MessageBox.alert("提示", "该原子脚本处于审核中，不能保存！");
         	} else {
         		save(0); // 正常保存
         	}
         }
    });
    
    //脚本编写左侧主panel
    var westPanel = Ext.create('Ext.panel.Panel', {
    	region: 'center',
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
        height: contentPanel.getHeight(),
        items: [tabsCenter, consolePanel],
	    buttonAlign: 'center',
	    buttons: [tryTestbtn,publishBtn,saveBtn, {
	          text: '重置',
	          handler: quxiao
	      }]
    });
    

//    var newOtherBtn = Ext.create('Ext.Button', {
//    	text: '新建另一个',
//    	handler: function(){
//        	Ext.MessageBox.buttonText.yes = "确认";
//            Ext.MessageBox.buttonText.no = "取消";
//            Ext.Msg.confirm("请确认", "确认要新建另一个原子脚本吗？", function(id) {
//                if (id == 'yes'){
//                	destroyRubbish(); // 销毁本页垃圾
//                	contentPanel.getLoader().load({
//                        url: 'basicScriptEdit.do',
//                        scripts: true
//                    });
//                }
//            });
//            if (refreshTryForBasic) {
//          		clearInterval(refreshTryForBasic);
//          	}
//        }
//    });
    
    //脚本编写页面主Panel
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "gridBasicScript_area",
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        padding: 5,
        autoScroll: true,
        border: false,
        height: contentPanel.getHeight()-modelHeigth ,
        items: [westPanel, paramsAndFuncDescPanel]
    });
    
   var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        extraKeys: {
            "F11": function(cm) {
              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
            },
            "Esc": function(cm) {
              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
            }
          }
    });
   
    editor.setSize(mainP.getWidth()-2, mainP.getHeight()-85 );
    contentPanel.on('resize',
    function() {
        editor.getDoc().clearHistory();
        mainPanel.setHeight(contentPanel.getHeight()-modelHeigth );
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainP.getWidth()-2, mainP.getHeight()-85);
        if(baseInfoOfScriptWin) {
        	baseInfoOfScriptWin.center();
        }
        if(publishAuditingSMWin) {
        	publishAuditingSMWin.center();
        }
        if(chooseTestAgentWin) {
        	chooseTestAgentWin.center();
        }
    });
    tabsCenter.on('resize', function() {
    	editor.getDoc().clearHistory();
    	editor.setSize(mainP.getWidth()-2, mainP.getHeight()-45 );
    });
    if(oldScriptId>0) {
    	Ext.Ajax.request({
            url: 'scriptService/copyAttachments.do',
            method: 'POST',
            async: false,
            params: {
            	iid: oldScriptId
            },
            success: function(response, request) {
            		 var reader = Ext.decode(response.responseText);
                 	 attachmentIds = reader.attachmentIds;
                     attachmentStore.load({params:{scriptId:0, ids:attachmentIds}});
            	 
            },
            failure: function(result, request) {
                secureFilterRs(result, "复制附件出错！");
            }
        });
    	
    	Ext.Ajax.request({
            url: 'scriptService/queryOneService.do',
            method: 'POST',
            async: false,
            params: {
            	iid: oldScriptId
            },
            success: function(response, request) {
                var reader = Ext.decode(response.responseText);
                scName.setValue(reader.scriptName);
//                $('#s-n-t').html(reader.scriptName);
                if(reader.scriptName) {
                	mainP.setTitle(reader.scriptName);
                } else {
                	mainP.setTitle("编辑框");
                }
                scriptName = reader.scriptName;
                sName.setValue(reader.serviceName);
                scName.setValue(reader.scriptName);
                excepResult.setValue(reader.excepResult);
                errExcepResult.setValue(reader.errExcepResult);
                usePlantForm.setValue(reader.platForm);
                funcDesc.setValue(reader.funcDesc);
                funcDescInWin.setValue(reader.funcDesc);

                var scriptT = reader.scriptType;
                if (scriptT == 'sh') {
                	FieldContainer_win.items.items[0].setValue(true);
                    checkRadioForBasicScriptEdit = 0;
                    editor.setOption("mode", 'shell');
                } else if (scriptT == 'bat') {
                	FieldContainer_win.items.items[1].setValue(true);
                    checkRadioForBasicScriptEdit = 1;
                    editor.setOption("mode", 'bat');
                } else if (scriptT == 'py') {
                	FieldContainer_win.items.items[3].setValue(true);
                    checkRadioForBasicScriptEdit = 3;
                    editor.setOption("mode", 'python');
                } else if (scriptT == 'SQL') {
                	FieldContainer_win.items.items[4].setValue(true);
                    checkRadioForBasicScriptEdit = 4;
                    editor.setOption("mode", 'sql');
                } else if (scriptT == 'perl') {
                	FieldContainer_win.items.items[2].setValue(true);
                    checkRadioForBasicScriptEdit = 2;
                    editor.setOption("mode", 'text/x-perl');
                }
                editor.setOption('value', '');
                editor.setOption('value', reader.content);
                sysID = parseInt(reader.sysName);
                busID = parseInt(reader.bussName);
                bussData.load();
                paramStore.load({params:{scriptId:oldScriptId}});
            },
            failure: function(result, request) {
                secureFilterRs(result, "获取脚本信息失败！");
            }
        });
    }
    

    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
    
    function orgParams() {
		paramStore.sort('paramOrder', 'ASC');
		var m = paramStore.getRange(0, paramStore.getCount() - 1);
		var aaaa = [];
		var someParamIsEmpty = false;
		var paramInvalidMessage = '';
		for (var i = 0, len = m.length; i < len; i++) {
			var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
			var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue"): '';

			if(!paramDefaultValue) {
				someParamIsEmpty = true;
			}
		    if ((paramType == 'OUT-int'||paramType == 'IN-int')&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                	paramInvalidMessage = '参数类型为int，但参数值不是int类型！';
                	break;
                }
            }
            if ((paramType == 'OUT-float'||paramType == 'IN-float')&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                	paramInvalidMessage = '参数类型为float，但参数值不是float类型！';
                	break;
                }
            }
			if (paramDefaultValue.indexOf('"') >= 0) {
				if (checkRadioForBasicScriptEdit == '1') {
					paramInvalidMessage = 'bat脚本暂时不支持具有双引号的参数值';
					break;
				}
			}
			aaaa.push(paramDefaultValue);
		}
		
		return {
			someParamIsEmpty: someParamIsEmpty,
			paramInvalidMessage: paramInvalidMessage,
			scriptPara: aaaa.join("@@script@@service@@")
		};
	}
	
	function getParams () {
		paramStore.sort('paramOrder', 'ASC');
		var m = paramStore.getRange(0, paramStore.getCount() - 1);
		var aaaa = [];
		for (var i = 0, len = m.length; i < len; i++) {
			var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue"): '';
			aaaa.push(paramDefaultValue);
		}
		return aaaa.join("@@script@@service@@");
	}
    function quxiao() {
        Ext.MessageBox.buttonText.yes = "确定";
        Ext.MessageBox.buttonText.no = "取消";
        if(newServiceId==0) {
        	Ext.Msg.confirm("请确认", "是否确认重置所有内容？", function(id) {
                if (id == 'yes') {
                	forword("basicScriptEditDbaas.do", "脚本编写")
                };
            });
        } else {
        	Ext.Msg.confirm("请确认", "是否确认重置所有内容？<br>将恢复到最后一次保存时的状态！", function(id) {
                if (id == 'yes') {
                	Ext.Ajax.request({
                        url: 'scriptService/queryOneService.do',
                        method: 'POST',
                        sync: true,
                        params: {
                        	iid: newServiceId
                        },
                        success: function(response, request) {
                            var reader = Ext.decode(response.responseText);
                            scName.setValue(reader.scriptName);
//                            $('#s-n-t').html(reader.scriptName);
                            if(reader.scriptName) {
                            	mainP.setTitle(reader.scriptName);
                            } else {
                            	mainP.setTitle("编辑框");
                            }
                            scriptName = reader.scriptName;
                            sName.setValue(reader.serviceName);
                            scName.setValue(reader.scriptName);
                            excepResult.setValue(reader.excepResult);
                            errExcepResult.setValue(reader.errExcepResult);
                            usePlantForm.setValue(reader.platForm);
                            funcDesc.setValue(reader.funcDesc);
                            funcDescInWin.setValue(reader.funcDesc);

                            var scriptT = reader.scriptType;
                            if (scriptT == 'sh') {
                            	FieldContainer_win.items.items[0].setValue(true);
                                checkRadioForBasicScriptEdit = 0;
                                editor.setOption("mode", 'shell');
                            } else if (scriptT == 'bat') {
                            	FieldContainer_win.items.items[1].setValue(true);
                                checkRadioForBasicScriptEdit = 1;
                                editor.setOption("mode", 'bat');
                            } else if (scriptT == 'py') {
                            	FieldContainer_win.items.items[3].setValue(true);
                                checkRadioForBasicScriptEdit = 3;
                                editor.setOption("mode", 'python');
                            } else if (scriptT == 'SQL') {
                            	FieldContainer_win.items.items[4].setValue(true);
                                checkRadioForBasicScriptEdit = 4;
                                editor.setOption("mode", 'sql');
                            } else if (scriptT == 'perl') {
                            	FieldContainer_win.items.items[2].setValue(true);
                                checkRadioForBasicScriptEdit = 2;
                                editor.setOption("mode", 'text/x-perl');
                            }
                            editor.setOption('value', '');
                            editor.setOption('value', reader.content);
                            sysID = parseInt(reader.sysName);
                            busID = parseInt(reader.bussName);
                            bussData.load();
                            attachmentIds = [];
                            paramStore.load();
                            attachmentStore.load();
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "获取脚本信息失败！");
                        }
                    });
                };
            });
        }
    }
    
    function testScript(){
    	whichButtonIsClicked = 2;
    	chooseTestAgentWin.show();
    	resourceGroupObj.setValue('');
		//search_form.getForm().findField("agentIp").setValue('');
    	//search_form.getForm().findField("agentDesc").setValue('');
    	agent_store.load();
	}
    
    function isAuditing() {
    	var isAuditing = false;
		Ext.Ajax.request({
		    url : 'scriptStatus.do',
		    method : 'POST',
		    async: false,
		    params : {
		    	serviceId: newServiceId
		    },
		    success: function(response, opts) {
		        var status = Ext.decode(response.responseText).status;
		        if(status==2) {
		        	isAuditing = true;
		    	}
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
		return isAuditing;
    }


    function save(ignoreFlag, callback, callType) { 
    	// ignoreFlag ::
    	// 0:正常保存
    	// 1:忽略提醒命令，继续保存
    	var scriptfile = scriptForm.getForm().findField("scriptfile").getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var sysId = bussCb.getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussTypeId = bussTypeCb.getValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var sysName = bussCb.getRawValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussType = bussTypeCb.getRawValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var serverName = sName.getValue();
        var scriptName1 = scName.getValue();
        var up = usePlantForm.getValue() || "";
        var errExcepResult1 = errExcepResult.getValue();
        var excepResult1 = excepResult.getValue();
        var scriptDesc1 = funcDescInWin.getValue();
        if (!scriptfile) {
        	saveFromBottom = true;
        }
        if(!scriptName1) {
    		scName.setValue(scriptName);
    	}
        if(!scriptDesc1) {
        	funcDescInWin.setValue(scriptDesc);
        }
        baseInfoOfScriptWin.close();
        if (!sysId) {
        	//saveFromBottom = true;
        	baseInfoOfScriptWin.show();
           // Ext.MessageBox.alert("提示", "请选择一级分类!");
            return;
        }
        if (!bussTypeId) {
        	//saveFromBottom = true;
        	baseInfoOfScriptWin.show();
         //   Ext.MessageBox.alert("提示", "请选择二级分类!");
            return;
        }
        if (serverName.trim() == '') {
        	//saveFromBottom = true;
        	baseInfoOfScriptWin.show();
          //  Ext.MessageBox.alert("提示", "服务名称不能为空!");
            return;
        }
        if (scriptName1.trim() == '') {
        	//saveFromBottom = true;
        	baseInfoOfScriptWin.show();
         //   Ext.MessageBox.alert("提示", "脚本名称不能为空!");
            return;
        }
//        $('#s-n-t').html(Ext.util.Format.trim(scriptName1));
      //  attachmentGrid.setTitle('脚本工程('+Ext.util.Format.trim(scriptName1)+')');
        if (up.trim() == '') {
        //	saveFromBottom = true;
        	baseInfoOfScriptWin.show();
         //   Ext.MessageBox.alert("提示", "适用平台不能为空!");
            return;
        }
        if (scriptDesc1.trim() == '') {
            //	saveFromBottom = true;
            	baseInfoOfScriptWin.show();
//                Ext.MessageBox.alert("提示", "功能说明不能为空!");
                return;
            }

//        var m = paramStore.getModifiedRecords();
        var m = paramStore.getRange();
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var n = 0;
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
            if ("" == paramType) {
                setMessage('参数类型不能为空！');
                return;
            }
            if (fucCheckLength(paramDesc) > 250) {
                setMessage('参数描述不能超过250字符！');
                return;
            }

           if ((paramType == 'OUT-int'||paramType == 'IN-int'||paramType == 'int')&&paramDefaultValue) {
                if (!checkIsInteger(paramDefaultValue)) {
                    setMessage('参数类型为int，但参数默认值不是int类型！');
                    return;
                }
            }
            if ((paramType == 'OUT-float'||paramType == 'IN-float'||paramType == 'float')&&paramDefaultValue) {
                if (!checkIsDouble(paramDefaultValue)) {
                    setMessage('参数类型为float，但参数默认值不是float类型！');
                    return;
                }
            }
            if (paramDefaultValue.indexOf('"')>=0) {
            	if(checkRadioForBasicScriptEdit == '1') {
            		Ext.Msg.alert('提示', 'bat脚本暂时不支持具有双引号的参数值');
                    return;
            	}
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }

        jsonData = jsonData + "]";

        editor.save();
        var content = document.getElementById('code').value;
        var type = "sh";
        if (checkRadioForBasicScriptEdit == '0') {
            type = "sh";
        } else if (checkRadioForBasicScriptEdit == '1') {
            type = "bat";
        } else if (checkRadioForBasicScriptEdit == '2') {
            type = "perl";
        } else if (checkRadioForBasicScriptEdit == '3') {
            type = "py";
        }else if (checkRadioForBasicScriptEdit == '4') {
            type = "sql";
        }
        var saveUrl = "saveScriptEdit.do";
        if(newServiceId!=0) {
        	saveUrl = "updateScriptEdit.do"
        }
        Ext.Ajax.request({
            url: saveUrl,
            method: 'POST',
            sync: true,
            params: {
            	iid: newServiceId,
                sysName: sysName,
                sysId: sysId,
                bussTypeId: bussTypeId,
                bussType: bussType,
                serverName: serverName,
                // 系统名称 系统分类
                scriptName: scriptName1,
                excepResult: excepResult1,
                errExcepResult: errExcepResult1,
                usePlantForm: up,
                funcDesc: scriptDesc1,
                checkRadio: type,
                content: content,
                params: jsonData,
                attachmentIds: attachmentIds,
                ignoreTipCmd: ignoreFlag
            },
            success: function(response, request) {
                var success = Ext.decode(response.responseText).success;
                var scriptExits = Ext.decode(response.responseText).scriptExits;
                if(scriptExits) {
                	Ext.Msg.alert('提示', '服务名称重复！');
                	return;
                }
                if(newServiceId!=0) {
                	var iid = Ext.decode(response.responseText).newId;
                } else {
                	var iid = Ext.decode(response.responseText).iid;
                }
                
                if (success && iid != "" && iid != null) {
                	newServiceId = iid;
                    attachmentIds = [];
                    paramStore.load();
                    attachmentStore.load();
                    /*
					 * if (refreshTry) { clearInterval(refreshTry); }
					 */
                	if(callback) {
                		callback();
                	} else {
                		Ext.Msg.alert('提示', '保存成功！');
                	}
                	if(scriptName1) {
                    	mainP.setTitle(scriptName1);
                    } else {
                    	mainP.setTitle("编辑框");
                    }
                	funcDesc.setValue(scriptDesc1);
                } else {
                	var ccc="保存";
                	if(callback && callType) {
                		ccc = callType;
                	}
                	var hasTipKeyWord = Ext.decode(response.responseText).hasTipKeyWord;
                	var hasScreenKeyWord = Ext.decode(response.responseText).hasScreenKeyWord;
                	if(hasScreenKeyWord) {
                		Ext.Msg.alert('提示', "脚本中存在屏蔽命令，无法"+ ccc +"！<br>"+Ext.decode(response.responseText).message);
                		return;
                	}
                	if(hasTipKeyWord) {
// Ext.Msg.alert('提示',
// "脚本中存在提醒命令，无法保存！<br>"+Ext.decode(response.responseText).message);
                		Ext.Msg.confirm("请确认", "脚本中存在提醒命令，是否继续"+ccc+"脚本？<br>"+Ext.decode(response.responseText).message, function(button, text) {
                            if (button == "yes") {
                            	save(1, callback, callType);
                            }
                        });
                	}
                }
            },
            failure: function(result, request) {
                secureFilterRs(result, "保存失败！");
            }
        });
    }
    
    var uploadDomJudge = setInterval(function(){
        if($('#uploadify-base').length == 0){
          
        } else {
          $('#uploadify-base').uploadify({
// 'auto' : false,
        	  'buttonClass' : 'some-class',
              'fileObjName' : 'files',
              'buttonCursor': 'hand',
              'buttonText' : '',
              'height': 16,
              'width': 16,
              'fileSizeLimit':'3MB',
              'successTimeout': 600,
        	  'debug' : false,
              'queueID'  : 'uploadify-process',
              'swf'      : 'js/uploadify/uploadify.swf',
              'uploader' : 'uploadScriptAttachmentFile.do;jsessionid='+sessionIdForBasicScriptEdit,
              'method'   : 'post',
              'onInit'   : function(instance) {
            	  uploadProcessWin = Ext.create("Ext.Window",{
                      title : '文件上传进度显示',
                      width : 300,     
                      height: 300,   
                      autoScroll: true,
                      closable : false,
                      modal:true,
                      html : '<div id="uploadify-process"></div>'                      
                    });
              },
              'onFallback' : function() {
                alert('没有检查到Flash插件，不能使用文件上传功能');
              },
              'onDialogOpen' : function() {
            	  uploadProcessWin.show();
              },
              'onDialogClose'  : function(queueData) {
            	  if(queueData.queueLength>0){
                      uploadProcessWin.show();
                    }else{
                      uploadProcessWin.hide();
                    }
              },
              'onUploadSuccess' : function(file, data, response) {
            	  if(response){
                      var res = Ext.JSON.decode(data);
                      if(res.success){
// attachmentIds.concat(res.ids.split(","));
                    	  attachmentIds.push.apply(attachmentIds,res.ids.split(","));
                      }
            	  }
              },
              'onUploadError' : function(file, errorCode, errorMsg, errorString) {
              },
              'onQueueComplete' : function(queueData) {
            	  uploadProcessWin.hide();
            	  attachmentStore.load();
            	  if(newServiceId!=0){
            	  save(0);
            	  }
              }
            });
          clearInterval(uploadDomJudge);
        }
      },100);
    
    
    function loadShelloutputhisInfo(requestId, iip, iport) {
    	var surl = "getScriptExecOutputForTry.do";
    	Ext.Ajax.request({
    		url : surl,
    		params : {
    			isFromTryATry: isFromTryATry,
    			requestId : requestId,
    			agentIp : iip,
    			agentPort : iport,
    			input :cmdVForbasicEdit
    		},
    		success : function(response, opts) {
    			var success = Ext.decode(response.responseText).success;
    			var msg = Ext.decode(response.responseText).out;
    			var status = Ext.decode(response.responseText).status;
    			if(!Ext.isEmpty(Ext.util.Format.trim(msg))) {
    				$('#consoleLog').html(msg);
    				consolePanel.body.scroll('bottom',300000);
    			}
    			if(status==2) {
    				if (refreshTryForBasic) {
	              		clearInterval(refreshTryForBasic);
	              	}
    			}
    		},
    		failure : function(response, opts) {
    			$('#consoleLog').html('获取执行信息失败');
    		}

    	});
    	cmdVForbasicEdit = null;
    }
    
    function loadShelloutputhisInfoCmd(requestId, iip, iport) {
    	var surl = "getScriptExecOutputForTry.do";
    	Ext.Ajax.request({
    		url : surl,
    		params : {
    			isFromTryATry: 1,
    			requestId : requestId,
    			agentIp : iip,
    			agentPort : iport,
    			input :cmdVForbasicEdit
    		},
    		success : function(response, opts) {
    			var success = Ext.decode(response.responseText).success;
    			var msg = Ext.decode(response.responseText).out;
    			var status = Ext.decode(response.responseText).status;
    			if(!Ext.isEmpty(Ext.util.Format.trim(msg))) {
    				$('#consoleLogForCmd').html(msg);
    				consoleOneCmdPanel.body.scroll('bottom',300000);
    			}
    			if(status==2) {
    				if (refreshCmdForBasic) {
    					clearInterval(refreshCmdForBasic);
    				}
    				execCmdBtn.setDisabled(false); 
    			}
    		},
    		failure : function(response, opts) {
    			$('#consoleLogForCmd').html('获取执行信息失败');
    		}
    		
    	});
    	cmdVForbasicEdit = null;
    }
    
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	var auditorStore_sm = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getPublishAuditorList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	var auditorComBox_sm = Ext.create('Ext.form.ComboBox', {
	    editable: false,
	    fieldLabel: "审核人",
	    labelWidth: 60,
// padding: 5,
	    store: auditorStore_sm,
	    queryMode: 'local',
// width: 200,
	    columnWidth:.98,
	    margin : '10 0 0 0',
	    displayField: 'fullName',
	    valueField: 'loginName'// ,
	    // value: auditor
	  });
	
	var planTime_sm = Ext.create('Go.form.field.DateTime',{
	    fieldLabel:'计划时间',
	    format:'Y-m-d H:i:s',
	    hidden:true,
	    labelWidth : 60,
// width:200,
	    columnWidth:.98,
	    margin : '10 0 0 0'
	  });
	
	var pubDesc_sm = Ext.create('Ext.form.field.TextArea', {
        name: 'pubdesc',
        fieldLabel: '详细说明',
        emptyText: '',
        labelWidth: 60,
        margin : '10 0 0 0',
        height: 80,
        columnWidth:.98,
        autoScroll: true
    });
	
	var levelStore_sm = Ext.create('Ext.data.Store', {
	    fields: ['iid', 'scriptLevel'],
	    data : [
	        {"iid":"1", "scriptLevel":"高风险脚本"},
	        {"iid":"2", "scriptLevel":"中风险脚本"},
	        {"iid":"3", "scriptLevel":"低风险脚本"}
	    ]
	});
	
	var scriptLevelCb_sm = Ext.create('Ext.form.field.ComboBox', {
        name: 'scriptLevel',
        labelWidth: 60,
        columnWidth: .98,
        queryMode: 'local',
        fieldLabel: '授权模式',
        margin : '10 0 0 0',
        displayField: 'scriptLevel',
        valueField: 'iid',
        editable: false,
        hidden: true,
        queryMode: 'local',
        emptyText: '--请选择授权模式--',
        store: levelStore_sm
    });
	
	var auditing_form_sm = Ext.create('Ext.form.Panel', {
		width: 600,
    	layout : 'anchor',
    	buttonAlign : 'center',
    	bodyCls : 'x-docked-noborder-top',
    	border : false,
	    items: [{
// layout:'form',
	    	anchor:'98%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [{
	    		layout:'column',
		    	border : false,		    	
	    		items:[planTime_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[scriptLevelCb_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[auditorComBox_sm]
	    	},{
	    		layout:'column',
		    	border : false,
		    	items:[pubDesc_sm]
	    	}]
	    }]
	});
	
    function publishScript(){
    	Ext.Ajax.request({
		    url : 'scriptHasVersion.do',
		    method : 'POST',
		    params : {
		    	serviceId: newServiceId
		    },
		    success: function(response, opts) {
		        var hasVersion = Ext.decode(response.responseText).hasVersion;
		        if(hasVersion==1) {
		    		Ext.Msg.alert('提示', "该脚本已经发布过！");
		    		return;
		    	} else {
		    		Ext.Ajax.request({
		    		    url : 'scriptStatus.do',
		    		    method : 'POST',
		    		    params : {
		    		    	serviceId: newServiceId
		    		    },
		    		    success: function(response, opts) {
		    		        var status = Ext.decode(response.responseText).status;
		    		        if(status==2) {
		    		    		Ext.Msg.alert('提示', "该脚本正处于审核中！");
		    		    		return;
		    		    	} else {
		    		    		if (!publishAuditingSMWin) {
    		        				publishAuditingSMWin = Ext.create('widget.window', {
    		        	                title: '确认审核信息',
    		        	                closable: true,
    		        	                closeAction: 'hide',
    		        	                resizable: false,
    		        	                modal: true,
    		        	                width: 600,
    		        	                minWidth: 350,
    		        	                height: 350,
    		        	                layout: {
    		        	                    type: 'border',
    		        	                    padding: 5
    		        	                },
    		        	                items: [auditing_form_sm],
    		        	                buttons: [{ 
    		        			  			xtype: "button",
// cls:'Common_Btn',
    		        			  			text: "确定", 
    		        			  			handler: function () { 
    		        			  				var planTime = planTime_sm.getRawValue();
    		        			  				var scriptLevel = scriptLevelCb_sm.getValue();
    		        			  				scriptLevel = 100;
    		        			  				var publishDesc = pubDesc_sm.getValue();
    		        			  				var auditor = auditorComBox_sm.getValue();
//    		        			  				if(!planTime) {
//    		        			  					Ext.Msg.alert('提示', "没有填写计划时间！");
//    		        			  					return;
//    		        			  				}
    		        			  				
    		        			  				if(!scriptLevel) {
    		        			  					Ext.Msg.alert('提示', "没有选择风险级别！");
    		        			  					return;
    		        			  				}
    		        			  				if(!publishDesc) {
    		        			  					Ext.Msg.alert('提示', "没有填写详细说明！");
    		        			  					return;
    		        			  				}
    		        			  				if(!auditor) {
    		        			  					Ext.Msg.alert('提示', "没有选择审核人！");
    		        			  					return;
    		        			  				}
    		        			  				
    		        			  				var sIds = new Array();
    		        			  				sIds.push(newServiceId);
    		        			  				Ext.Ajax.request({
    		        			  				    url : 'scriptPublishAuditing.do',
    		        			  				    method : 'POST',
    		        			  				    params : {
    		        			  				    	sIds : sIds,
    		        			  				    	planTime: planTime,
    		        			  				    	scriptLevel: scriptLevel,
    		        			  				    	publishDesc: publishDesc,
    		        			  				    	auditor: auditor,
    		        			  				  	  flag:0 // 0-来着个人脚本库
    		        			  				    },
    		        			  				    success: function(response, opts) {
    		        			  				        var success = Ext.decode(response.responseText).success;
    		        			  				        var message = Ext.decode(response.responseText).message;
    		        			  				        if(!success) {
    		        			  				        	Ext.MessageBox.alert("提示", message);
    		        			  				        } else {
    		        			  				        	Ext.MessageBox.alert("提示", "请求已经发送到审核人");
    		        			  				        }
    		        			  				      publishAuditingSMWin.close();
    		        			  				      
    		        			  				    },
    		        			  				    failure: function(result, request) {
    		        			  				    	secureFilterRs(result,"操作失败！");
    		        			  				    	publishAuditingSMWin.close();
    		        			  				    }
    		        			  			    });
    		        			  				
    		        				        }
    		        			  		}, { 
    		        			  			xtype: "button", 
// cls:'Gray_button',
    		        			  			text: "取消", 
    		        			  			handler: function () {
    		        			  				this.up("window").close();
    		        			  			}
    		        			  		}]
    		        	            });
    		        	            
    		        	        }
    		        			publishAuditingSMWin.show();
    		        			auditorStore_sm.load();
    		        			planTime_sm.setValue('');
    		        			scriptLevelCb_sm.setValue('');
    		        			pubDesc_sm.setValue('');
    		        			auditorComBox_sm.setValue('');
		    		    		/*
								 * Ext.MessageBox.buttonText.yes = "确定";
								 * Ext.MessageBox.buttonText.no = "取消";
								 * Ext.Msg.confirm("确认发布", "是否确认发布该脚本",
								 * function(id){ if(id=='yes') { } });
								 */
		    		    	}
		    		    },
		    		    failure: function(result, request) {
		    		    	secureFilterRs(result,"操作失败！");
		    		    	return;
		    		    }
		    	    });
		    	}
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    	return;
		    }
	    });
    }
    function savedoc(fileform ) { 
    	// ignoreFlag ::
    	// 0:正常保存
    	// 1:忽略提醒命令，继续保存
        var sysId = bussCb.getValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussTypeId = bussTypeCb.getValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var sysName = bussCb.getRawValue(); // scriptForm.getForm().findField("sysName").getRawValue();
        var bussType = bussTypeCb.getRawValue(); // scriptForm.getForm().findField("bussType").getRawValue();
        var serverName = sName.getValue();
        var scriptName1 = scName.getValue();
        var up = usePlantForm.getValue() || "";
        var fd = funcDesc.getValue();
        var auths=  authCombox.getValue();
        
		var upfile=fileform.findField("file").getValue();
		if(upfile==''){
			Ext.Msg.alert('提示',"请选择文档");
			return ;
		}
		if(auths==''){
			Ext.Msg.alert('提示',"请选权限");
			return ;
		}
        if (!sysId) {
        	saveFromBottom = true;
        	baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "请选择一级分类!");
            return;
        }
        if (!bussTypeId) {
        	saveFromBottom = true;
        	baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "请选择二级分类!");
            return;
        }
        if (serverName.trim() == '') {
        	saveFromBottom = true;
        	baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "服务名称不能为空!");
            return;
        }
       /*
		 * if (scriptName1.trim() == '') { saveFromBottom = true;
		 * baseInfoOfScriptWin.show(); Ext.MessageBox.alert("提示", "脚本名称不能为空!");
		 * return; }
		 */
        // $('#s-n-t').html(Ext.util.Format.trim(scriptName1));
        if (up.trim() == '') {
        	saveFromBottom = true;
        	baseInfoOfScriptWin.show();
            Ext.MessageBox.alert("提示", "适用平台不能为空!");
            return;
        }
        if (fd.trim() == '') {
            Ext.MessageBox.alert("提示", "功能说明不能为空!");
            return;
        }
        
        Ext.MessageBox.wait("数据处理中...", "进度条");
        fileform.submit({
			url: 'uploadTemplate.do',
			params:{
				  // itype:itype,
            	  itemplatedes:fd,
            	  iservename:serverName,
            	  iplatform:up,
            	  iauth:auths,
            	  iserveone:sysId,
            	  iservetwo:bussTypeId
        	  },
		    success: function(form, action) 
		    {
		    	var respText = Ext.JSON.decode(action.response.responseText);
		    	var msg = respText.message;// 提示信息
				var isOk =  respText.isOk;// 是否导入成功
				// 提示消息
				if(isOk)
				{
					Ext.Msg.alert('提示', msg);
					// 导入成功，跳 转到文档管理页面
					// forwardUrl('templateManagement.do')
					baseInfoOfScriptWin.close();
					form.reset();
					popNewTab("文档管理", 'templateManagement.do', {}, 10,true);
					//
				}else{
					Ext.Msg.alert('提示', msg);
				}
				
		    },
		    failure: function(form, action) {
		    	 secureFilterRsFrom(form, action);
		    }
		});
    }
    function forwardUrl(_url) {
 	   contentPanel.getLoader().load({
 	        url: _url,
 	        scripts: true
 	    });
    }
    function openTerminal(webshell_endpoint, server_type, options) {
    	var tabTitle = options.host + '-'
    	if(server_type=="1") {
    		tabTitle += "SSH";
    	} else if(server_type=="2") {
    		tabTitle += "TELNET";
    	}
    	var tabCount = 0; // 页签组中当前功能tab页的数量
    	var existedTab = null;
    	tapPanelForConsole.items.each(function(item) {
    		if (item.title == tabTitle) {
    			tabCount = 1;
    			existedTab = item;
    		}
    	});
    	var mdfive = hex_md5(tabTitle);
    	if(tabCount>0) {
    		var termold = $('#term-create-'+ mdfive).data('term');
    		var clientold = $('#term-create-'+ mdfive).data('client');
    		if(termold) {
    			termold.destroy();
    		}
    		if(clientold) {
    			clientold.close();
    		}
    	} else {
    		existedTab = tapPanelForConsole.add({
    			title : tabTitle,
    			bodyStyle: 'background:#000;',
    			border: false,
    			height: consoleOneCmdPanel.getHeight()-85,
    			activeItem : 0,
    			autoScroll : true,
    			html: '<div id="term-create-'+ mdfive +'"></div>',
    			closable: true // 允许关闭
    		});
    	}
    	tapPanelForConsole.setActiveTab(existedTab);
    	
    	var client = new WSSHClient(webshell_endpoint, server_type);
        var term = new Terminal({cols: 80, rows: parseInt((consoleOneCmdPanel.getHeight()-85)/16), screenKeys: true, useStyle:true});
        term.on('data', function (data) {
            client.sendClientData(data);
        });
        term.open();
        
        $(term.element).detach().appendTo('#term-create-'+mdfive);
        $('#term-create-'+ mdfive).data('term', term);
        $('#term-create-'+ mdfive).data('client', client);
        term.write('Connecting...');
        client.connect({
            onError: function (error) {
                term.write('Error: ' + error + '\r\n');
                console.debug('error happened');
            },
            onConnect: function () {
                client.sendInitData(options);
                client.sendClientData('\r');
                console.debug('connection established');
            },
            onClose: function () {
                term.write("\rconnection closed")
                console.debug('connection reset by peer');
            },
            onData: function (data) {
                term.write(data);
                console.debug('get data:' + data);
            }
        });
    }
    //baseInfoOfScriptWin.show();
    function connect(server_type, options) {
    	if(webshell_endpoint=="") {
    		Ext.MessageBox.alert("提示", "Web Shell服务没有配置，无法使用仿真终端功能！");
    		return;
    	}
        openTerminal(webshell_endpoint, server_type, options)

    }
    function clickNotdoc(){
    	 authCombox.hide();
       	docfile.hide();
       	scName.show();
       	errExcepResult.show();
       	excepResult.show();
       	
       	tryTestbtn.disabled =false;
     	publishBtn.disabled =false;
     	saveBtn.disabled =false;
    }
    function clickdoc(){
    	authCombox.show();
    	scriptfile.hide();
    	docfile.show();
    	scName.hide();
    	errExcepResult.hide();
    	excepResult.hide();
    
    	tryTestbtn.disabled =true;
    	publishBtn.disabled =true;
    	saveBtn.disabled =true;
    }
   
});