<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean sendSwitch = Environment.getInstance().getScriptServiceSendSwitch();
%>
<html>
<head>
<script>
var filter_bussId = '<%=request.getParameter("filter_bussId")==null?-1:request.getParameter("filter_bussId")%>';
var filter_bussTypeId = '<%=request.getParameter("filter_bussTypeId")==null?-1:request.getParameter("filter_bussTypeId")%>';
var filter_scriptName = '<%=request.getParameter("filter_scriptName")==null?"":request.getParameter("filter_scriptName")%>';
var filter_serviceName = '<%=request.getParameter("filter_serviceName")==null?"":request.getParameter("filter_serviceName")%>';
var filter_scriptType = '<%=request.getParameter("filter_scriptType")==null?-1:request.getParameter("filter_scriptType")%>';
var filter_scriptStatus = '<%=request.getParameter("filter_scriptStatus")==null?-10000:request.getParameter("filter_scriptStatus")%>';

var filter = {
		'filter_bussId': filter_bussId,
		'filter_bussTypeId': filter_bussTypeId,
		'filter_scriptName': filter_scriptName,
		'filter_serviceName': filter_serviceName,
		'filter_scriptType': filter_scriptType,
		'filter_scriptStatus':filter_scriptStatus
	};
var sendSwitch = <%=sendSwitch%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/common/array.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/dbaas/serviceManager.js"></script>
</head>
<body>
<div id="scriptService_grid_area" style="width: 100%;height: 100%"></div>
</body>
</html>