var resourceforTryList;
var tryResourceConfigGridStore;
Ext.onReady(function() {
    var choice=0;
	var iipQuery = new Ext.form.TextField({
		name : 'iip',
//		fieldLabel : 'iip',
		displayField : 'iip',
		emptyText : '--请输入IP--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '15%',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBarConfig.moveFirst();
                }
            }
        }
	});
	var ibusinessQuery = new Ext.form.TextField({
		name : 'sysname',
//	    fieldLabel: "业务系统",
		displayField : 'hostname',
		emptyText : '--请输入业务系统--',
		labelWidth : 70,
		labelAlign : 'right',
		width : '20%',
		hidden:true,
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBarConfig.moveFirst();
                }
            }
        }
	});

		
	var tryResourceConfigForm = Ext.create('Ext.form.Panel', {
		region : 'north',
		border:false,
		bodyCls : 'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		dockedItems : [{
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			border:false,
			dock : 'top',
			items : [ ibusinessQuery,iipQuery ,{
				xtype : 'button',
				cls : 'Common_Btn',
	            textAlign:'center',
				text : '查询',
				handler:query
			},{
				xtype : 'button',
				border:false,
				cls : 'Common_Btn',
	            textAlign:'center',
				text : '清空',
				handler:function(){
					ibusinessQuery.setValue('');
					iipQuery.setValue('');
				}
			},'->',{
				xtype : 'button',
				cls : 'Common_Btn',
	            textAlign:'center',
				text : '增加',	
				handler:function(){
					addRow(0)//参数为0时表示添加
                }
			},{
                text:'编辑',
                cls: 'Common_Btn',
                handler:function(){
			        addRow(1)//参数为1时表示修改
                }
            }, {
				xtype : 'button',
				cls : 'Common_Btn',
	            textAlign:'center',
				text : '删除',
				handler:delRows
			}]
		}]
	});	
	
    Ext.define('tryResourceConfigGridModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },{
            name: 'zid',
            type: 'long'
        },
        {
            name: 'iip',
            type: 'string'
        },
        {
            name: 'idbport',
            type: 'long'
        },
        {
            name: 'irestype',
            type: 'string'
        },
        {
            name: 'ibusiness',
            type: 'string'
        },
        {
            name: 'customName',
            type: 'string'
        },
        {
            name: 'idbuser',
            type: 'string'
        },
        {
            name: 'idbpwd',
            type: 'string'
        },
        {
            name: 'isid',
            type: 'string'
        },
        {
            name: 'idbid',
            type: 'string'
        },
        {
            name: 'idbversion',
            type: 'string'
        },       
        {
            name: 'istatus',
            type: 'string'
        },{
            name: 'lastcheck',
            type: 'string'
        }
        ]
    });
	tryResourceConfigGridStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
	//	remoteSort: true,
		pageSize : 30,
		model : 'tryResourceConfigGridModel',
		proxy : {
			type : 'ajax',
			url : 'getTryResourceList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	var pageBarConfig = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: tryResourceConfigGridStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
        emptyMsg: "没有记录"
    });
	
	var cellEditing = Ext.create ('Ext.grid.plugin.CellEditing',{
		clicksToEdit : 2
	});
 
	var tryResourceConfigGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'center',
		padding : panel_margin,
		cls:'customize_panel_back',
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		border:true,
		columns : [{ text: '序号', xtype:'rownumberer', width: 70 },
            { text: '主键',  dataIndex: 'iid',hidden:true},
            { text: 'R主键',  dataIndex: 'zid',hidden:true},
            { text: '业务系统',  dataIndex: 'ibusiness',flex: 1,hidden:true},
            { text: '显示名称',  dataIndex: 'customName',flex: 1,hidden:tryResourceAddModeSwitch?false:true},
            { text: '数据源类型',  dataIndex: 'irestype',flex: 1},
            { text: 'IP',  dataIndex: 'iip',flex: 1},
            { text: '端口',  dataIndex: 'idbport',flex: 1},
            { text: '服务名',  dataIndex: 'isid',flex: 1},
            { text: '用户名',  dataIndex: 'idbuser',flex: 1},
            {
            	header: '密码',
                dataIndex: 'idbpwd',
                width: 100,
                flex:1,
        		renderer:retNotView
            },{
                header: '状态',
                dataIndex: 'istatus',
                width: 80,
                renderer: function(value, p, record, rowIndex) {
                    var IFLAG = record.get('istatus');
                    if(IFLAG=='0'){
                    	return "<span class='Complete_Green State_Color'>有效</span>";
                    }else{
                    	return "<span class='Abnormal_yellow State_Color'>失效</span>";
                    }
                }
            },{
                text: '最近检测时间',
                dataIndex: 'lastcheck',
                width: 160,
                minWidth:160,
                labelWidth: 100,
                flex:1,
                editor:false
            },
            {
                text: '操作',
                dataIndex: 'stepOperation',
                flex:1,
                renderer: function(value, p, record, rowIndex) {
                	var iid = record.get('iid');
                    return '<div>' + '<a href="javascript:void(0)" onclick="testvalid('+iid+')">' + '&nbsp;测试连通性' + '</a>' + '</div>';
                }
            }],
		store : tryResourceConfigGridStore,
		selModel:Ext.create('Ext.selection.CheckboxModel', {}),
	    plugins : [cellEditing],
        listeners: {
            itemdblclick : function(dbclickthis, record, item,index, e, eOpts) {
	        choice=1;
	        if(tryResourceAddModeSwitch){
	        	showEditForm(record);
	        }	        
          }
        }
	});
	var tryResourceConfigPanel = Ext.create('Ext.panel.Panel', {
		border:true,
		bodyPadding : grid_margin,
		layout : 'border',
		height : contentPanel.getHeight()-modelHeigth,
		width  : contentPanel.getWidth,
		bodyCls:'service_platform_bodybg',		
		renderTo : "tryResourceConfig_div",
		items : [ tryResourceConfigForm, tryResourceConfigGrid ]
	});
	
	tryResourceConfigGridStore.on ('beforeload', function (store, options){
		var new_params ={
				hasPage: false,
	    		ip : Ext.util.Format.trim(iipQuery.getValue()),
	    		businessName : Ext.util.Format.trim(ibusinessQuery.getValue()),
			};
		Ext.apply (tryResourceConfigGridStore.proxy.extraParams, new_params);
	});
	
	contentPanel.on('resize', function() {
		tryResourceConfigPanel.setHeight(contentPanel.getHeight()-modelHeigth);
		tryResourceConfigPanel.setWidth(contentPanel.getWidth());
	});
	
	function query()
	{
		pageBarConfig.moveFirst();
	}
    function retNotView(value){
    	var coun ="";
    	if (value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
    function addRow(c) {
    	if(tryResourceAddModeSwitch){
    		choice=c;
        	if(choice==0){
        		showEditForm();
        	}else if(choice==1){
        		var record = tryResourceConfigGrid.getSelectionModel().getSelection();    		
            	if(record.length==0){
            		Ext.Msg.alert('提示', '请选择一条要编辑的记录！');
            		return;
            	}else if(record.length>1){
            		Ext.Msg.alert('提示', '只能选择一条要编辑的记录！');
            		return;
            	} 
            	showEditForm(record[0]);
        	}
    	}else{
    		resourceforTryList = Ext.create('Ext.window.Window', {
    		    title: '选择数据库',
    		    height: '60%',  //Number型  也可以是字符串类型  width:'60%'
    		    width: '50%',
    		    layout: 'fit',
    		    constrain: true, 		//闲置窗口不超出浏览器
    		    constrainHeader:true, 	//标题不能超出浏览器边界
    		    modal: true,			//设置模态窗口
    		    plain:true, 			//窗口设置透明背景
    		    draggable: false,
    		    resizable: false,
    		    loader: {
    				url: 'toResourceList.do',
//    				params : {
//    					IID : IID,
//    					flag:0
//    				},
    				autoLoad: true,
    				scripts: true
    			}
    		//,
    		    //autoScroll:true //显示滚动条
    		});
    		resourceforTryList.show();
    	}

    }
	var businnessTypeStore = Ext.create('Ext.data.JsonStore', {
		fields: ['INAME', 'INAME'],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getBusinessTypeCode.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    var dataTypeModel = Ext.create('Ext.data.Store', {
        fields: ['name'],
        autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getDatabaseType.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
    Ext.define('sysModel', {
        extend: 'Ext.data.Model',
        fields: [
        {
            name: 'sysName',
            type: 'string'
        }        ]
    });
    var sysDataByUserIdStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'sysModel',
		proxy : {
			type : 'ajax',
			url : 'getAppSysManageByUserId.do?projectFlag=1',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
    function checkIP(ip) {
    	var reg = /^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/;
    	if (reg.test(ip)) {
    		return true;
    	} else {
    		return false;
    	}
    } 
    
  //编辑时打开一个记录窗口
    function showEditForm(record) {
    	var SRTeditWindow;
    	var oldIRSTYPE="";
        if (null==SRTeditWindow ) {
            var form = Ext.widget('form', {
                border: false,
                bodyPadding: 3,
                autoScroll: true,
                items: [ 
                {
    				border : false,
    				layout : 'column',
    				items :[{
    	                	xtype:'hiddenfield',
    	                	name:'iid'
    		             }, {
    	                	xtype:'hiddenfield',
    	                	name:'zid'
    		                }]
                },
                {
    				border : false,
    				layout : 'column',
    				items :[{
    	                	xtype : 'combo',
    	                    name:   'ibusiness',
    	                    fieldLabel: '业务系统',
    	                    triggerAction: 'all',
//    	                    editable: false,
    	                    queryMode: 'local',
    	                    emptyText: "--请选择--",
    	                    displayField: 'sysName',
    	                    valueField: 'sysName',
    	                    //labelWidth : 80,
    	                    columnWidth : .5,
    	                    width : contentPanel.getWidth() - 20,
    						labelWidth:75,
    	                    labelAlign:'right',
    	                    padding : '5 5 10 5',
    	                    store: sysDataByUserIdStore,
    	                    allowBlank: false,
    	                    hidden:true,
    	                    maxLength:50,
    	                    listeners : {
    	                        beforequery : function(e) {  
    	                            var combo = e.combo;     
    	                            if(!e.forceAll){     
    	                                var value = e.query;     
    	                                combo.store.filterBy(function(record, id){     
    	                                    var text = record.get(combo.displayField);     
    	                                    return (text.toUpperCase().indexOf(value.toUpperCase())!=-1);     
    	                                });  
    	                                combo.expand();     
    	                                return false;     
    	                            }  
    	                        }  
    	                    }
    	                },
                        {
                            xtype: 'combo',                     
                            name:'IBUSINESSTYPE',
                            fieldLabel: '系统分类',
                            triggerAction: 'all',
                            // 用all表示把下拉框列表框的列表值全部显示出来
                            editable: false,
                            queryMode: 'local',
                            emptyText: "--请选择系统分类--",
    	                    hidden:true,
                            displayField: 'INAME',
                            valueField: 'INAME',
                            columnWidth : .5,
                            width : contentPanel.getWidth() - 20,
    						labelWidth:75,
    	                    labelAlign:'right',
                            padding : '5 5 10 5',
                            store:businnessTypeStore,
                            allowBlank: true,
                            maxLength:50
                        }]
                },{
                    border : false,
                    layout : 'column',
                    items :[
                            {
                            xtype: 'textfield',
                            name:'customName',
                            fieldLabel: '显示名称',
                            allowBlank: false,
                            columnWidth : .5,
                            width : contentPanel.getWidth() - 20,
                            labelWidth:75,
                            labelAlign:'right',
                            padding : '5 5 10 5',
                            maxLength:25
                        }]
                },
                {
    				border : false,
    				layout : 'column',
    				items :[
    					{
                            xtype : 'combo',
                            name:'irestype',
                            fieldLabel: '数据源类型',
                            triggerAction: 'all',
                            editable: false,
                            queryMode: 'local',
                            displayField: 'name',
                            valueField: 'name',
                            columnWidth : .5,
                            width : contentPanel.getWidth() - 20,
                            labelWidth:75,
                            labelAlign:'right',
                            padding : '5 5 10 5',
                            store: dataTypeModel,
                            allowBlank: false,
                            maxLength:50,
    	                    listeners :{
    	                    	"change" : function(e){
    	                    		var IDBPORT=form.getForm().findField('idbport').getValue();
    	                    		if(e.value=="MYSQL"){
    	                    			if (IDBPORT==''  || (oldIRSTYPE!='' && oldIRSTYPE!=e.value)){
    	                    			form.getForm().findField('idbport').setValue(3306);
    	                    			}
    	                    		}else if(e.value=="DB2"){
    	                    			if (IDBPORT==''  || (oldIRSTYPE!='' && oldIRSTYPE!=e.value)){
    	                    			form.getForm().findField('idbport').setValue(50000);
    	                    			}
    	                    		}else{
    	                    			if (IDBPORT==''  || (oldIRSTYPE!='' && oldIRSTYPE!=e.value)){
    	                    			form.getForm().findField('idbport').setValue(17632);
    	                    			}
    	                    		}
    	                    		oldIRSTYPE=form.getForm().findField('irestype').getValue();
    	                    	}
    	                    }
                        },{
                            xtype: 'textfield',
                            name:'isid',
                            fieldLabel: '服务名',
//                            allowBlank: false,
                            columnWidth : .5,
                            width : contentPanel.getWidth() - 20,
    						labelWidth:75,
    	                    labelAlign:'right',
                            padding : '5 5 10 5',
                            maxLength:25
                        }]
                },
                {
    				border : false,
    				layout : 'column',
    				items : [{
                        xtype: 'textfield',
                        name:'iip',
                        fieldLabel: '服务器IP',
                        columnWidth : .5,
                        width : contentPanel.getWidth() - 20,
                        labelWidth:75,
                        labelAlign:'right',
                        padding : '5 5 10 5',
                        regex:/^((?:(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(?:25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d))))$/,
                        regexText:'IP格式错误',
                        allowBlank: false,
                        maxLength:50
                        },{
    	                	xtype: 'numberfield',
    	                    name:'idbport',
    	                    fieldLabel: '数据库端口',
    	                    regex:/^\d*$/,
    	                    regexText:'请填写数字',
    	                    value:17632,
    	                    maxValue: 65535,
    	                    minValue: 1,
    	                    allowBlank: false,
    	                    columnWidth : .5,
    	                    width : contentPanel.getWidth() - 20,
    						labelWidth:75,
    	                    labelAlign:'right',
    	                    padding : '5 5 10 5',
    	                    maxLength:5
    	                }]
                },
                {
    				border : false,
    				layout : 'column',
    				items : [{
                        xtype: 'textfield',
                        name:'idbuser',
                        fieldLabel: '数据源用户',
                        allowBlank: false,
                        columnWidth : .5,
                        width : contentPanel.getWidth() - 20,
                        labelWidth:75,
                        labelAlign:'right',
                        padding : '5 5 10 5',
                        maxLength:255
                        }, {
    	                    xtype: 'textfield',
    	                    name:'idbpwd',
    	                    fieldLabel: '数据源密码',
    	                    inputType: 'password',
    	                    allowBlank: false,
    	                    columnWidth : .5,
    	                    width : contentPanel.getWidth() - 20,
    						labelWidth:75,
    	                    labelAlign:'right',
    	                    padding : '5 5 10 5',
    	                    maxLength:25
    	                }]
                }],
                dockedItems : [{
    				xtype : 'toolbar',
    				border : false,
    				baseCls:'customize_gray_back',
    				dock : 'bottom',
    				items : ['->',{
    					cls : 'Common_Btn',
    					textAlign : 'center',
    					text: '取消',
                        handler: function() {                   	
                            this.up('form').getForm().reset();
                            this.up('window').close();
                            SRTeditWindow=null;
                        }
                    }, {
                    	cls : 'Common_Btn',
        				textAlign : 'center',
                    	text: '保存',
                        handler: function() {
                    		var IID="";
                    		var url="saveResourceManage.do";	                		
                        	if(choice==1){
                        		IID=form.getForm().findField('iid').getValue();
                        		url="updateResourceManage.do";
                        	}
                        
                    		var IRSTYPE = form.getForm().findField('irestype').getValue();
                            if ("" == IRSTYPE || null == IRSTYPE) {
                                setMessage('数据源类型不能为空！');
                                return;
                            }
                            if(IRSTYPE!='MYSQL'){
                            	 var ISID = form.getForm().findField('isid').getValue();
                                 if ("" == ISID || null == ISID) {
                                     setMessage('服务名不能为空！');
                                     return;
                                 }
                                 if (fucCheckLength(ISID) > 25) {
                                     setMessage('服务名不能超过25字符！');
                                     return;
                                 }
                            }
                           
                    		var IIP = form.getForm().findField('iip').getValue();
                            if ("" == IIP || null == IIP) {
                                setMessage('服务器IP不能为空！');
                                return;
                            }
                            if (!checkIP(IIP)) {
                            	setMessage('服务器IP:'+IIP+'格式不正确!');
                            	return;
                			}
                            var IDBPORT = form.getForm().findField('idbport').getValue();
                            if ("" == IDBPORT || null == IDBPORT) {
                            	setMessage('数据库端口不能为空！');
                                return;
                            }else if(!isNumber(IDBPORT)){
                            	setMessage('请正确填写数据库端口！');
                                return;
                            }
                            var IDBUSER = form.getForm().findField('idbuser').getValue();
                            if ("" == IDBUSER || null == IDBUSER) {
                                setMessage('数据源用户不能为空！');
                                return;
                            }
                            if (fucCheckLength(IDBUSER) > 255) {
                                setMessage('数据源用户不能超过255字符！');
                                return;
                            }
                            var IDBPWD = form.getForm().findField('idbpwd').getValue();
                            if ("" == IDBPWD || null == IDBPWD) {
                                setMessage('数据源密码不能为空！');
                                return;
                            }
                            if (fucCheckLength(IDBPWD) > 25) {
                                setMessage('数据源密码不能超过25字符！');
                                return;
                            }
                            form.getForm().findField('ibusiness').setValue("尝试服务器");
                            form.getForm().findField('IBUSINESSTYPE').setValue("测试验证类");
                        	var jsonData = '{"IID":"'+IID+ '",';
                        	 jsonData=jsonData+'"IDBID":"",';
                        	 jsonData=jsonData+'"zid":"'+form.getForm().findField('zid').getValue() + '",';
                        	 jsonData=jsonData+'"customName":"'+form.getForm().findField('customName').getValue() + '",';
                        	 jsonData=jsonData+'"ITYPE":"1",';
                        	 jsonData=jsonData+'"IAPPLYUSER":"",';
                        	 jsonData=jsonData+'"IBUSINESS":"'+form.getForm().findField('ibusiness').getValue() + '",';
                        	 jsonData=jsonData+'"IIP":"'+form.getForm().findField('iip').getValue() + '",';
                        	 jsonData=jsonData+'"IRSTYPE":"'+form.getForm().findField('irestype').getValue() + '",';
                        	 jsonData=jsonData+'"ICOPYNUM":"0",';
                        	 jsonData=jsonData+'"IDBPORT":"'+form.getForm().findField('idbport').getValue() + '",';
                        	 jsonData=jsonData+'"INAME":"",';
                        	 jsonData=jsonData+'"IDBUSER":"'+form.getForm().findField('idbuser').getValue() + '",';
                        	 jsonData=jsonData+'"IDBPWD":"'+form.getForm().findField('idbpwd').getValue() + '",';
                        	 jsonData=jsonData+'"ISID":"'+form.getForm().findField('isid').getValue() + '",';
                        	 jsonData=jsonData+'"IMODEL":"2",';
                        	 jsonData=jsonData+'"IFLAG":1,';
                        	 jsonData=jsonData+'"IALTERLOGNAME":"",';
                        	 jsonData=jsonData+'"ISTATUS":"0",';
                        	 jsonData=jsonData+'"ICPU":"8",';
                        	 jsonData=jsonData+'"IMEMORY":"16",';
                        	 jsonData=jsonData+'"IDISK":"1024",';
                        	 jsonData=jsonData+'"IENV":"1",';
                        	 jsonData=jsonData+'"IBUSINESSTYPE":"'+form.getForm().findField('IBUSINESSTYPE').getValue() + '"}';
                        	 Ext.Msg.wait('处理中，请稍后...', '提示');  
                        	 Ext.Ajax.request({
    							method:"POST",
    							timeout:6000000,
    							url:url,
    							params:{jsonData:jsonData,dataModel:1},
    							success:function(response){
    								Ext.Msg.hide();
    								var text = Ext.JSON.decode(response.responseText);
    								if(text.success){
    									Ext.Msg.alert('提示', text.message);
    									tryResourceConfigGridStore.reload();   
    								}else{
    		    						Ext.Msg.alert('提示', text.message);
    								}
    							},
    							failure:function(form, action){
    								switch (action.failureType) {
    		    					case Ext.form.action.Action.CLIENT_INVALID:
    		    						Ext.Msg.alert('提示', '连接异常！');
    		    						break;
    		    					case Ext.form.action.Action.SERVER_INVALID:
    		    						Ext.Msg.alert('提示', action.result.message);
    		    					}
    							}
    						});
                            this.up('window').close();
                            SRTeditWindow=null;
                        }
                    },{
                    	cls : 'Common_Btn',
        				textAlign : 'center',
        				hidden:true,
                    	text: '重置',
                        handler: function() {
                            this.up('form').getForm().reset();
                            this.up('form').getForm().loadRecord(record);
                        }
                    }]
                }]
            });
            if(choice==1){
            	form.loadRecord(record);
            }
            SRTeditWindow = Ext.widget('window', {
                title: '编辑记录',
                closeAction: 'hide',
                constrain: true,
                resizable: false,
    			width : contentPanel.getWidth()*0.6,
    			//height : contentPanel.getHeight(),
                minWidth: 300,
                minHeight: 300,
                layout: 'fit',
                modal: true,
                items: form,
                defaultFocus: 'firstName'
            });
        }
        SRTeditWindow.show();
    }
	
	function delRows() {
		var records = tryResourceConfigGrid.getSelectionModel().getSelection();
		if(records.length==0){
			 Ext.Msg.alert('提示', '请选择要删除的记录!');
             return;
		}else{
			Ext.Msg.confirm("提示","确认删除?", function(btn){
			    if (btn == 'yes'){
			    	var iidlist = new Array(); 
					Ext.each(records, function(record) {
						iidlist.push(record.data.iid); 
					});							
					Ext.Ajax.request({
					    url: 'delTryResourceInfo.do',
						params : {
							ids : iidlist
						},
						method : 'POST', 
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            if (success) {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                tryResourceConfigGridStore.reload();
                                
                            } else {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }							
					});
			    }
			});	
		}		
	}

});


function testvalid(iid) {
    Ext.Msg.wait('处理中，请稍后...', '提示');  
    Ext.Ajax.request({
       method:"POST",
       timeout:60000,
       url:"chkResourceValid.do",
       params:{iid:iid,from:1},
       success: function(response, request) {
           var success = Ext.decode(response.responseText).success;
           var message = Ext.decode(response.responseText).message;
           if(success){
               Ext.Msg.alert('提示', message);
           }else{
               Ext.Msg.alert('提示', message);
           }
           tryResourceConfigGridStore.reload();
       },
       failure:function(form, action){
           switch (action.failureType) {
           case Ext.form.action.Action.CLIENT_INVALID:
               Ext.Msg.alert('提示', '连接异常！');
               break;
           case Ext.form.action.Action.SERVER_INVALID:
               Ext.Msg.alert('提示', action.result.message);
           }
       }
   });
}



