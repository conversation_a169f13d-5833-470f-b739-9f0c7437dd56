Ext.onReady(function() {
	var globalSelectRecord;
	Ext.tip.QuickTipManager.init();
    var dataSourceStore;
    var dataSourceGrid;
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IDBPORT',
            type: 'long'
        },
        {
            name: 'IRSTYPE',
            type: 'string'
        },
        {
            name: 'IBUSINESS',
            type: 'string'
        },
        {
            name: 'IDBUSER',
            type: 'string'
        },
        {
            name: 'ISID',
            type: 'string'
        }
        ]
    });
    dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 16,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'getResourceNoTry.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
            	ip: ipField.getValue(),
            	dataBaseName: dataBaseNameField.getValue()
        };
        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 70,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '业务系统',
        dataIndex: 'IBUSINESS',
        flex:1,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '服务器IP',
        dataIndex: 'IIP',
        flex:1,
        renderer : function(value, metadata) {
        	metadata.tdAttr = 'data-qtip="' + value + '"';
			return value;
		}
    },
    {
        text: '数据库端口',
        dataIndex: 'IDBPORT',
        flex:1,
        width: 100
        
    },
    {
        text: '数据源类型',
        dataIndex: 'IRSTYPE',
        flex:1,
        width: 100
    },
    {
        text: '数据源用户',
        dataIndex: 'IDBUSER',
        flex:1,
        width: 100
    },
    {
        header: '服务名',
        dataIndex: 'ISID',
        flex:1,
        width: 90
    }];
    // 分页工具
  var  pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        displayInfo: true,
	    baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    var ipField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务器IP',
        labelWidth: 69,
        labelAlign: 'left',
        name: 'ipFieldParam',
        width: '20%'
    });
    var dataBaseNameField = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务名',
        labelWidth: 50,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '18%'
    });
    var form = Ext.create('Ext.form.FormPanel', {
		region: 'north',
		padding : '5 0 5 0',
		border : false,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ipField,dataBaseNameField,
			    {
		        xtype: 'button',
		        cls: 'Common_Btn',
		        text: '查询',
		        handler: function() {
		        	QueryMessage();
		        }
		    },'->',
		    {
		        text: '保存',
		        cls: 'Common_Btn',
		        handler: saveDatabase
		    }]
		} ]
	});
    
    dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
        store: dataSourceStore,
        selModel: selModel,
        plugins: [cellEditing],
        padding : panel_margin,
        border: true,
        bbar: pageBar,
        columnLines: true,
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        		globalSelectRecord = record;
        	}
        }
    });
    function retNotView(value){
    	var coun ="";
    	if (value.trim().length>0){
    		for (var i=0;i<value.length;i++){
    			coun=coun+"*";
    		}
    	}
    	if(value.trim()==""){
    		coun="";
    	}
    	return coun ;
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "resourceListForTry_div",
        layout: 'border',
        width : '100%',
        height :contentPanel.getHeight()*0.6,
        border: true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight ()*0.6);
		mainPanel.setWidth (contentPanel.getWidth ()*0.5 );
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    function saveDatabase() {
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
        	 Ext.Msg.alert('提示', '请先选择您要添加的记录!');
             return;
        } else {
            Ext.Msg.confirm("请确认", "是否要添加为测试数据库?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        if (iid) {
                        	ids.push(iid);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: 'saveTryResource.do',
                        params: {
                        	resourceIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            if (success) {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                parent.resourceforTryList.close ();
                                tryResourceConfigGridStore.reload();
                                
                            } else {
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                   }
                }
            });
        }
    }
});
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}