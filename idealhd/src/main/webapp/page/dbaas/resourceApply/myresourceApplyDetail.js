Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	Ext.define('resourceModel', {
		extend : 'Ext.data.Model',
		fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'OPERATIONSYSTEM',
            type: 'string'
        },
        {
            name: 'DATATYP<PERSON>',
            type: 'string'
        },
        {
            name: 'DATAVERSION',
            type: 'string'
        },
        {
            name: 'SPECIFICATION',
            type: 'string'
        },
        {
            name: 'GENRE',
            type: 'string'
        },
        {
            name: 'DESKSPACE',
            type: 'string'
        },
        {
            name: 'VERIFIER',
            type: 'string'
        },
        {
            name: 'ADDTIME',
            type: 'string'
        },
        {
            name: 'RETURNTIME',
            type: 'string'
        },
        {
            name: 'RETURNCONTENT',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IIFLAG',
            type: 'string'
        },
        {
        	name: 'SETMODEL',
        	type: 'string'
        },
        {
        	name: 'ENV',
        	type: 'string'
        },
        {
        	name: 'USEDATE',
        	type: 'string'
        },{
        	name: 'IRESAPPPARAMVALUE',
        	type: 'string'
        }
        ]
	});

	/** *********************Store********************* */
	/** 任务列表数据源* */
	var resourceStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'resourceModel',
		pageSize : 6,
		proxy : {
			type : 'ajax',
			url : 'detailResourceApply.do?id=' +IID ,// utLogInfoRecord.do
		
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'totalUT'
			}
		}
	});
	var backInfoTA = Ext.create('Ext.form.field.TextArea', {
		fieldLabel: '拒绝原因',
		labelWidth: 58,
		padding : '5 0 5 35',
		height: 98,
		hidden:true,
		width:contentPanel.getWidth ()*2/3,
		autoScroll: true
	});
	var iresappParamTA = Ext.create('Ext.form.field.TextArea', {
		id : 'IRESAPPPARAMVALUE',
		fieldLabel: '配置参数',
		labelWidth: 60,
		padding : '5 0 5 35',
		height: 308,
		width:contentPanel.getWidth ()*2/3,
		autoScroll: true
	});
	resourceStore.on('load', function(store, options, success) {
		var reader = store.getProxy().getReader();
		Ext.getCmp("OPERATIONSYSTEM").setValue(reader.jsonData.OPERATIONSYSTEM);
		Ext.getCmp("DATATYPE").setValue(reader.jsonData.DATATYPE);
		Ext.getCmp("DATAVERSION").setValue(reader.jsonData.DATAVERSION);
		var value = reader.jsonData.SPECIFICATION;
		var backValue = "";
		if (value == '1') {
			backValue = '1 核 1G';
		}else if (value == '2') {
			backValue = '1 核 2G';
		}else if (value == '3') {
			backValue = '2 核 4G';
		}else if (value == '4') {
			backValue = '2 核 8G';
		}else if (value == '5') {
			backValue = '4 核 8G';
		}else if (value == '6') {
			backValue = '4 核 16G';
		}else if (value == '7') {
			backValue = '8 核 16G';
		}else if (value == '8') {
			backValue = '8 核 32G';
		}else if (value == '9') {
			backValue = '16 核 64G';
		}
		Ext.getCmp("SPECIFICATION").setValue(backValue);
		var v1=reader.jsonData.GENRE;
		var v1back = "";
		if (v1 == '1') {
			v1back = '共享';
		}else if (v1 == '2') {
			v1back = '独享';
		}
		Ext.getCmp("GENRE").setValue(v1back);
		Ext.getCmp("DESKSPACE").setValue(reader.jsonData.DESKSPACE);
		Ext.getCmp("VERIFIER").setValue(reader.jsonData.VERIFIER);
		Ext.getCmp("ADDTIME").setValue(reader.jsonData.ADDTIME);
		Ext.getCmp("RETURNTIME").setValue(reader.jsonData.RETURNTIME);
		Ext.getCmp("RETURNCONTENT").setValue(reader.jsonData.RETURNCONTENT);
		Ext.getCmp("IIP").setValue(reader.jsonData.IIP);
		var v2=reader.jsonData.IIFLAG;
		var v2back = "";
		if (v2 == '1') {
			v2back = '已申请';
		}else if (v2 == '2') {
			v2back = '申请成功';
		}else if (v2 == '3') {
			v2back = '申请失败';
		}else if (v2 == '4') {
			v2back = '取消申请';
		}else if (v2 == '5') {
			backInfoTA.show()
			v2back = '拒绝申请';
			backInfoTA.setValue(reader.jsonData.IBACKINFO);
		}
		Ext.getCmp("IIFLAG").setValue(v2back);
		
		var v3=reader.jsonData.SETMODEL;
		var v3back = "";
		if (v3 == '1') {
			v3back = 'Rac';
		}else if (v3 == '2') {
			v3back = '实例';
		}else if (v3 == '3') {
			v3back = 'Data Guard';
		}
		Ext.getCmp("SETMODEL").setValue(v3back);
		
		var v4=reader.jsonData.ENV;
		var v4back = "";
		if (v4 == '1') {
			v4back = '测试';
		}else if (v4 == '2') {
			v4back = '开发';
		}else if (v4 == '3') {
			v4back = '生产';
		}
		Ext.getCmp("ENV").setValue(v4back);
		
		var v5=reader.jsonData.USEDATE;
		var v5back = "";
		if (v5 == '1') {
			v5back = '一个月';
		}else if (v5 == '2') {
			v5back = '二个月';
		}else if (v5 == '3') {
			v5back = '三个月';
		}else if (v5 == '4') {
			v5back = '四个月';
		}else if (v5 == '5') {
			v5back = '五个月';
		}else if (v5 == '6') {
			v5back = '六个月';
		}else if (v5 == '7') {
			v5back = '七个月';
		}else if (v5 == '8') {
			v5back = '八个月';
		}else if (v5 == '9') {
			v5back = '九个月';
		}else if (v5 == '10') {
			v5back = '十个月';
		}else if (v5 == '11') {
			v5back = '十一个月';
		}else if (v5 == '12') {
			v5back = '一年';
		}else if (v5 == '21') {
			v5back = '二年';
		}else if (v5 == '31') {
			v5back = '长期';
		}
		Ext.getCmp("USEDATE").setValue(v5back);
		
		Ext.getCmp("IRESAPPPARAMVALUE").setValue(reader.jsonData.IRESAPPPARAMVALUE);
	});
	
	var resourceForm = Ext.create('Ext.form.Panel', {
		layout : 'anchor',
		region : 'center',
		bodyCls : 'x-docked-noborder-top',
		buttonAlign : 'center',
		border : false,
		items : [ {
			layout : 'column',
			anchor : '95%',
			padding : '5 0 5 0',
			border : false,
			items : [ {
				id : "OPERATIONSYSTEM",
				fieldLabel : '业务系统',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				width : 400,
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : "DATATYPE",
				fieldLabel : '数据库类型',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				width : 400,
				padding : '5 0 5 5',
				xtype : 'textfield'
			},  {
				id : "DATAVERSION",
				fieldLabel : '数据库版本',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				width : 400,
				padding : '5 0 5 5',
				xtype : 'textfield'
			}, {
				id : 'SPECIFICATION',
				fieldLabel : '规格',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'GENRE',
				fieldLabel : '类型',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				hidden:true,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'DESKSPACE',
				fieldLabel : '申请表空间',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				hidden:true,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'SETMODEL',
				fieldLabel : '部署类型',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			},{
				id : 'ENV',
				fieldLabel : '环境',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			},{
				id : 'USEDATE',
				fieldLabel : '使用期限',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			},{
				id : 'VERIFIER',
				fieldLabel : '审核人',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'ADDTIME',
				fieldLabel : '申请资源时间',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'RETURNTIME',
				fieldLabel : '返回时间',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'RETURNCONTENT',
				fieldLabel : '返回内容',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'IIP',
				fieldLabel : 'IP',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			}, {
				id : 'IIFLAG',
				fieldLabel : '申请状态',
				labelAlign : 'right',
				labelWidth : 90,
				height : 30,
				padding : '5 0 5 5',
				width : 400,
				xtype : 'textfield'
			},iresappParamTA,backInfoTA]
		} ]
	});
	var workItemRecord_mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "myresourceApplyDetail",
		width : '100%',
		height : '100%',
		autoScroll :true,
		border : false,
		bodyPadding : 5,
		layout : 'border',
		items : [ resourceForm ]
	});

	contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
		Ext.destroy(workItemRecord_mainPanel);
		if (Ext.isIE) {
			CollectGarbage();
		}
	});

});
