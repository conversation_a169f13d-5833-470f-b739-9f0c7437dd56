Ext
		.onReady(function() {
			var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
			// 1.业务系统
			var businessSystemStore = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'isysname' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getResourceBusinessSystemList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var operationSystem = new Ext.form.field.ComboBox({
				name : 'operationSystem',
				fieldLabel : '业务系统',
				displayField : 'isysname',
				valueField : 'iid',
				emptyText : '--请选择业务系统--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
				store : businessSystemStore
			});
			// 2.数据库类型
			var dataTypeStore = Ext.create('Ext.data.Store', {
				fields : [ 'idbtype' ],
				// autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getResourceDbTypeList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var dataVersionStore = Ext.create('Ext.data.Store', {
				fields : [ 'versions' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'getVersionListByType.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var dataType = new Ext.form.field.ComboBox({
				name : 'dataType',
				fieldLabel : '数据库类型',
				displayField : 'idbtype',
				valueField : 'idbtype',
				emptyText : '--数据库类型--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
				store : dataTypeStore,
				listeners : {
					select : function(combo, record, opts) {
						var dbtype = record[0].get("idbtype");

						dataVersionStore.load({
							params : {
								dbtype : dbtype,
								setmodel : setmodel.getValue()
							}
						});

						dataVersion.clearValue();
						dataVersion.bindStore(dataVersionStore);
					}
				}
			});
			// 3.数据库版本
			var dataVersion = new Ext.form.field.ComboBox({
				name : 'dataVersion',
				fieldLabel : '版本',
				displayField : 'versions',
				valueField : 'versions',
				emptyText : '--数据库版本--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
			});
			// 4.规格
			var specificationStore = Ext.create('Ext.data.Store', {
				fields : [ 'id', 'name' ],
				data : [ {
					"id" : "1",
					"name" : "1 核 1G"
				}, {
					"id" : "2",
					"name" : "1 核 2G"
				}, {
					"id" : "3",
					"name" : "2 核 4G"
				}, {
					"id" : "4",
					"name" : "2 核 8G"
				}, {
					"id" : "5",
					"name" : "4 核 8G"
				}, {
					"id" : "6",
					"name" : "4 核 16G"
				}, {
					"id" : "7",
					"name" : "8 核 16G"
				}, {
					"id" : "8",
					"name" : "8 核 32G"
				}, {
					"id" : "9",
					"name" : "16 核 64G"
				} ]
			});
			var specification = new Ext.form.field.ComboBox({
				name : 'specification',
				fieldLabel : '规格',
				displayField : 'name',
				valueField : 'id',
				emptyText : '--规格--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				value : '2',
				width : contentPanel.getWidth() - 20,
				store : specificationStore
			});
			// 5.类型
			var genre = new Ext.form.field.ComboBox({
				name : 'genre',
				fieldLabel : '类型',
				displayField : 'name',
				valueField : 'id',
				emptyText : '--类型--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
				store : new Ext.data.SimpleStore({
					fields : [ 'id', 'name' ],
					data : [ [ '1', '共享' ], [ '2', '独享' ] ]
				})
			});
			// 6.磁盘空间

			var deskSpace = new Ext.form.TextField({
				name : 'deskSpace',
				fieldLabel : '表空间大小',
				displayField : 'deskSpace',
				emptyText : '--请输入表空间大小--',
				labelWidth : 90,
				padding : '5 5 10 5',
				width : (contentPanel.getWidth() - 20) / 2
			});

			var isAuto = new Ext.form.Checkbox({
				name : 'isAuto',
				id : 'isAuto',
				inputValue : '1',
				disabled : true,
				boxLabel : '是否自增长'
			})

			var setmodel = new Ext.form.field.ComboBox({
				name : 'setmodel',
				fieldLabel : '部署模式',
				displayField : 'name',
				valueField : 'id',
				emptyText : '--部署模式--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
				store : new Ext.data.SimpleStore(
						{
							fields : [ 'id', 'name' ],
							data : [/* ['1', 'Rac'], */[ '4', '用户' ],
									[ '2', '实例' ] /* , ['3', 'Data Guard'] */]
						}),
				listeners : {
					select : function(combo, record, opts) {
						var iid = record[0].get("id");
						resappParamStore.load({
							params : {
								type : iid
							}
						});
						resappParam.setValue('');
						resappParamSubStore.load({
							params : {
								id : 0
							}
						});
						dataTypeStore.load({
							params : {
								setmodel : iid
							}
						});
					},
					change : function(combo, record, opts) {
						dataType.setValue('');
					}
				}
			});
			var env = new Ext.form.field.ComboBox({
				name : 'env',
				fieldLabel : '部署环境',
				displayField : 'name',
				valueField : 'id',
				emptyText : '--环境--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
				store : new Ext.data.SimpleStore({
					fields : [ 'id', 'name' ],
					data : [ [ '1', '测试' ], [ '2', '开发' ], [ '3', '生产' ] ]
				})
			});
			var usedate = new Ext.form.field.ComboBox({
				name : 'usedate',
				fieldLabel : '使用期限',
				displayField : 'name',
				valueField : 'id',
				emptyText : '--使用期限--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				value : '3',
				width : contentPanel.getWidth() - 20,
				store : new Ext.data.SimpleStore({
					fields : [ 'id', 'name' ],
					data : [ [ '1', '一个月' ], [ '2', '二个月' ], [ '3', '三个月' ],
							[ '4', '四个月' ], [ '5', '五个月' ], [ '6', '六个月' ],
							[ '7', '七个月' ], [ '8', '八个月' ], [ '9', '九个月' ],
							[ '10', '十个月' ], [ '11', '十一个月' ], [ '12', '一年' ],
							[ '21', '二年' ], [ '31', '长期' ] ]
				})
			});
			// 7.审核人
			var verifierStore = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'ifullname' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getVerifierList.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var verifier = new Ext.form.field.ComboBox({
				name : 'verifier',
				fieldLabel : '审核人',
				displayField : 'ifullname',
				valueField : 'iid',
				emptyText : '--请选择审核人--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : contentPanel.getWidth() - 20,
				store : verifierStore
			});
			// --------------------------
			var resappParamStore = Ext.create('Ext.data.Store', {
				fields : [ 'iid', 'iname' ],
				autoLoad : false,
				proxy : {
					type : 'ajax',
					url : 'getResappParam.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			var resappParamSubStore = Ext.create('Ext.data.Store',
					{
						fields : [ 'iid', 'iflag', 'iname', 'ivalue', 'iorder',
								'idesc' ],
						autoLoad : false,
						proxy : {
							type : 'ajax',
							url : 'getResappParamSub.do',
							reader : {
								type : 'json',
								root : 'dataList'
							}
						}
					});
			var resappParam = new Ext.form.field.ComboBox({
				name : 'resappParam',
				fieldLabel : '模板类型',
				displayField : 'iname',
				valueField : 'iid',
				emptyText : '--模板类型--',
				labelWidth : 90,
				padding : '5 5 10 5',
				queryMode : 'local',
				afterLabelTextTpl : required,
				columnWidth : .5,
				editable : false,
				width : '30%',
				store : resappParamStore,
				listeners : {
					select : function(combo, record, opts) {
						var iid = record[0].get("iid");
						resappParamSubStore.load({
							params : {
								id : iid
							}
						});
					}
				}
			});
			var resappParamColumns = [ {
				text : '序号',
				xtype : 'rownumberer',
				align:'left',
				width : 80,
				resizable : true
			}, {
				text : '主键',
				dataIndex : 'iid',
				width : 70,
				hidden : true
			}, {
				text : 'iflag',
				dataIndex : 'iflag',
				width : 70,
				hidden : true
			}, {
				text : '参数名',
				dataIndex : 'iname',
				flex : 1,
				width : 200
			}, {
				text : '参数值',
				dataIndex : 'ivalue',
				flex : 1,
				editor : true
			}, {
				text : '说明',
				dataIndex : 'idesc',
				width : 200,
				editor : false
			} ];
			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 1
			});
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true
			});
			var resappParamGird = Ext.create('Ext.grid.Panel', {
				region : 'center',
				padding : panel_margin,
				store : resappParamSubStore,
				selModel : selModel,
				plugins : [ cellEditing ],
				border : true,
				columnLines : true,
				cls:'customize_panel_back',
				columns : resappParamColumns
			});

			// ---------------------------

			/** 提交按钮* */
			var submitButton = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
				textAlign : 'center',
				text : "提交",
				handler : submitResource
			});
			var cancel = Ext.create("Ext.Button", {
				cls : 'Common_Btn',
				textAlign : 'center',
				text : "取消",
				handler : clearQueryWhere
			});
			var form = new Ext.form.FormPanel({
				region : 'north',
				bodyPadding : 5,
				border : true,
				baseCls:'customize_gray_back',
				items : [ {
					border : false,
					layout : 'column',
					items : [ operationSystem, setmodel ]
				}, {
					border : false,
					layout : 'column',
					items : [ dataType, dataVersion ]
				}, {
					border : false,
					layout : 'column',
					items : [ specification, env ]
				}, {
					border : false,
					layout : 'column',
					items : [ usedate, verifier ]
				}
				/*
				 * ,{ border: false, layout: 'column', items: [resappParam] }
				 */
				/*
				 * ,{ border: false, layout: 'column', items: [deskSpace] }
				 */
				// operationSystem, dataType,dataVersion,/* specification,*/
				// genre,deskSpace,verifier,setmodel,env,usedate
				],
				dockedItems : [ {
					xtype : 'toolbar',
					border : false,
					baseCls:'customize_gray_back',
					dock : 'bottom',
					items : [ resappParam, '->', submitButton, cancel ]
				} ]
			});
			var rightPanel = Ext.create("Ext.panel.Panel", {
				width : contentPanel.getWidth(),
				height : contentPanel.getHeight() - modelHeigth,
				layout : 'border',
				border : false,
				items : [ form, resappParamGird ],
				bodyCls : 'service_platform_bodybg',
				renderTo : "resourceApply_area"
			});

			/** 窗口尺寸调节* */
			contentPanel.on('resize', function() {
				rightPanel.setHeight(contentPanel.getHeight() - modelHeigth);
				rightPanel.setWidth(contentPanel.getWidth());
			});
			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(rightPanel);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
			/** *********************方法********************* */
			/* 解决IE下trim问题 */
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};
			/**
			 * 取消
			 * 
			 * @returns
			 */
			function clearQueryWhere() {
				form.getForm().findField("operationSystem").setValue('');
				form.getForm().findField("dataType").setValue('');
				form.getForm().findField("dataVersion").setValue('');
				form.getForm().findField("specification").setValue('');
				form.getForm().findField("genre").setValue('');
				form.getForm().findField("deskSpace").setValue('');
				form.getForm().findField("verifier").setValue('');
				form.getForm().findField("setmodel").setValue('');
				form.getForm().findField("env").setValue('');
				form.getForm().findField("usedate").setValue('');
			}
			/**
			 * 提交资源申请
			 * 
			 * @returns
			 */
			function submitResource() {
				Ext.MessageBox.buttonText.yes = "确定";
				Ext.MessageBox.buttonText.no = "取消";
				Ext.Msg.confirm("确认提交", "是否确认提交该资源申请?", function(id) {
					if (id == 'yes')
						submitIns();
				});
			}
			/**
			 * 提交数据验证
			 * 
			 * @returns
			 */
			function submitIns() {
				// 业务系统
				var operationSystemName = form.getForm().findField(
						"operationSystem").getRawValue();
				// 数据库类型
				var dataTypeId = form.getForm().findField("dataType")
						.getValue();
				var dataTypeName = form.getForm().findField("dataType")
						.getRawValue();
				// 数据库版本
				var dataVersionId = form.getForm().findField("dataVersion")
						.getValue();
				var dataVersionName = form.getForm().findField("dataVersion")
						.getRawValue();
				// 规格
				var specificationId = form.getForm().findField("specification")
						.getValue();
				var specificationName = form.getForm().findField(
						"specification").getRawValue();
				// //类型
				// var genreId = form.getForm ().findField ("genre").getValue();
				// var genreName = form.getForm ().findField
				// ("genre").getRawValue();
				// 磁盘空间
				// var deskSpace = form.getForm ().findField
				// ("deskSpace").getRawValue();
				// 审核人
				var verifierId = form.getForm().findField("verifier")
						.getValue();
				var verifierName = form.getForm().findField("verifier")
						.getRawValue();
				// 部署模式
				var setmodelId = form.getForm().findField("setmodel")
						.getValue();
				var setmodelName = form.getForm().findField("setmodel")
						.getRawValue();
				// 环境
				var envId = form.getForm().findField("env").getValue();
				var envName = form.getForm().findField("env").getRawValue();
				// 使用期限
				var usedateId = form.getForm().findField("usedate").getValue();
				var usedateName = form.getForm().findField("usedate")
						.getRawValue();
				;
				//
				var resappParamId = form.getForm().findField("resappParam")
						.getValue();
				var resappParamName = form.getForm().findField("resappParam")
						.getRawValue();
				var jsonData = "{";
				if (trim(operationSystemName) == ''
						|| (null == operationSystemName)) {
					Ext.MessageBox.alert("提示", "请选择业务系统");
					return false;
				} else {
					jsonData = jsonData + "\"OPERATIONSYSTEM\":\""
							+ operationSystemName + "\",";
				}
				if (trim(dataTypeId) == '' || (null == dataTypeId)) {
					Ext.MessageBox.alert("提示", "请选择数据库类型");
					return false;
				} else {
					jsonData = jsonData + "\"DATATYPE\":\"" + dataTypeId
							+ "\",";
				}
				if (trim(dataVersionId) == '' || (null == dataVersionId)) {
					Ext.MessageBox.alert("提示", "请选择数据库版本");
					return false;
				} else {
					jsonData = jsonData + "\"DATAVERSION\":\"" + dataVersionId
							+ "\",";
				}
				if (trim(specificationId) == '' || (null == specificationId)) {
					Ext.MessageBox.alert("提示", "请选择规格");
					return false;
				} else {
					jsonData = jsonData + "\"SPECIFICATION\":\""
							+ specificationId + "\",";
				}
				// jsonData = jsonData + "\"SPECIFICATION\":\"8\",";
				// if (trim (genreId) == '' || (null == genreId))
				// {
				// Ext.MessageBox.alert ("提示", "请选择类型");
				// return false;
				// }else {
				// jsonData = jsonData + "\"GENRE\":\""+genreId+"\",";
				// }
				// if (trim (deskSpace) == '' || (null == deskSpace))
				// {
				// Ext.MessageBox.alert ("提示", "请输入磁盘空间");
				// return false;
				// }else {
				// jsonData = jsonData + "\"DESKSPACE\":\""+deskSpace+"\",";
				// }
				if (trim(verifierName) == '' || (null == verifierName)) {
					Ext.MessageBox.alert("提示", "请选择审核人");
					return false;
				} else {
					jsonData = jsonData + "\"VERIFIER\":\"" + verifierId + "\"";
				}
				// POC新增字段
				if (trim(setmodelName) == '' || (null == setmodelName)) {
					Ext.MessageBox.alert("提示", "请选择部署模式");
					return false;
				} else {
					jsonData = jsonData + ",\"SETMODEL\":\"" + setmodelId
							+ "\",";
				}
				if (trim(envName) == '' || (null == envName)) {
					Ext.MessageBox.alert("提示", "请选择环境");
					return false;
				} else {
					jsonData = jsonData + "\"ENV\":\"" + envId + "\",";
				}
				if (trim(usedateName) == '' || (null == usedateName)) {
					Ext.MessageBox.alert("提示", "请选择使用期限");
					return false;
				} else {
					jsonData = jsonData + "\"USEDATE\":\"" + usedateId + "\",";
				}
				if (trim(resappParamName) == '' || (null == resappParamName)) {
					Ext.MessageBox.alert("提示", "请选择模板");
					return false;
				} else {
					jsonData = jsonData + "\"RESAPPPARAM\":\"" + resappParamId
							+ "\"";
				}
				jsonData = jsonData + "}";

				var store = resappParamGird.getStore();
				var count = store.getCount();
				var resappParamStr = '';
				for (var i = 0; i < count; i++) {
					var record = store.getAt(i);
					var iorder = record.data.iorder;
					if (iorder == null || iorder == 'null')
						iorder = 0;
					resappParamStr += "\'" + record.data.iname + "\',\'"
							+ record.data.ivalue + "\'," + iorder + ","
							+ record.data.iflag + ";";
				}
				resappParamStr = resappParamStr.substring(0,
						resappParamStr.length - 1);

				Ext.Ajax
						.request({
							url : 'resourceApplySubmit.do',
							method : 'POST',
							params : {
								jsonData : jsonData,
								resappParamStr : resappParamStr
							},
							success : function(response, options) {
								var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
								Ext.Msg.alert('提示', message);
								if (success) {
									contentPanel.getLoader().load({
										url : 'resourceApplyInit.do',
										scripts : true
									});
								} else {
									Ext.MessageBox.show({
										width : 300,
										title : "提交失败",
										msg : message,
										buttonText : {
											yes : '确定'
										},
										buttons : Ext.Msg.YES
									});
								}
							},
							failure : function(result, request) {// alert(result);
								Ext.Msg.alert('提示', "操作失败");
							}
						});
			}
		});
