Ext.onReady(function() {
	Ext.tip.QuickTipManager.init();
    // 清理主面板的各种监听时间
   //destroyRubbish();IID, ISERVICESNAME, ISCRIPTTYPE, IDBTYPE,ISSUER
    Ext.define('dataSourceModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },
        {
            name: 'OPERATIONSYSTEM',
            type: 'string'
        },
        {
            name: 'DATATYPE',
            type: 'string'
        },
        {
            name: 'DATAVERSION',
            type: 'string'
        },
        {
            name: 'SPECIFICATION',
            type: 'string'
        },
        {
            name: 'GENRE',
            type: 'string'
        },
        {
            name: 'DESKSPACE',
            type: 'string'
        },
        {
            name: 'VERIFIER',
            type: 'string'
        },
        {
            name: 'ADDTIME',
            type: 'string'
        },
        {
            name: 'RETURNTIME',
            type: 'string'
        },
        {
            name: 'RETURNCONTENT',
            type: 'string'
        },
        {
            name: 'IIP',
            type: 'string'
        },
        {
            name: 'IIFLAG',
            type: 'string'
        },
        {
            name: 'IBACKINFO',
            type: 'string'
        },
        {
        	name: 'SETMODEL',
        	type: 'string'
        },
        {
        	name: 'ENV',
        	type: 'string'
        },
        {
        	name: 'USEDATE',
        	type: 'string'
        },
        {
        	name: 'IRESAPPPARAM',
        	type: 'string'
        }
        ]
    });
    
  var  dataSourceStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'dataSourceModel',
        proxy: {
            type: 'ajax',
            url: 'resourceApplyList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    dataSourceStore.on('beforeload', function(store, options) {
        var new_params = {
        	serviceName: serviceName.getValue(),
        	flag:appFlag
        };
        Ext.apply(dataSourceStore.proxy.extraParams, new_params);
    });
    var scriptServiceReleaseColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 80,
        resizable: true
    },
    {
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },
    {
        text: '业务系统',
        dataIndex: 'OPERATIONSYSTEM',
        flex: 1
    },
    {
        text: '数据库类型',
        dataIndex: 'DATATYPE',
        width: 100
    },
    {
        text: '数据库版本',
        dataIndex: 'DATAVERSION',
        width: 120
    },
    {
    	text: '规格',
        dataIndex: 'SPECIFICATION',
        width: 70,
        //hidden:true,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = '1 核 1G';
			}else if (value == '2') { 
				backValue = '1 核 2G';
			}else if (value == '3') {
				backValue = '2 核 4G';
			}else if (value == '4') {
				backValue = '2 核 8G';
			}else if (value == '5') {
				backValue = '4 核 8G';
			}else if (value == '6') {
				backValue = '4 核 16G';
			}else if (value == '7') {
				backValue = '8 核 16G';
			}else if (value == '8') {
				backValue = '8 核 32G';
			}else if (value == '9') {
				backValue = '16 核 64G';
			}
			return backValue;
		}
    },
    {
    	text: '类型',
        dataIndex: 'GENRE',
        width: 70,
        hidden:true,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = '共享';
			}else if (value == '2') {
				backValue = '独享';
			}
			return backValue;
		}
    },
    {
    	text: '部署模式',
        dataIndex: 'SETMODEL',
        width: 70,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = 'Rac';
			}else if (value == '2') {
				backValue = '实例';
			}else if (value == '3') {
				backValue = 'Data Guard';
			}else if (value == '4') {
				backValue = '用户';
			}
			return backValue;
		}
    },
    {
    	text: '环境',
        dataIndex: 'ENV',
        width: 70,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = '测试';
			}else if (value == '2') {
				backValue = '开发';
			}else if (value == '3') {
				backValue = '生产';
			}
			return backValue;
		}
    },
    {
    	text: '使用期限',
        dataIndex: 'USEDATE',
        width: 70,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = '一个月';
			}else if (value == '2') {
				backValue = '二个月';
			}else if (value == '3') {
				backValue = '三个月';
			}else if (value == '4') {
				backValue = '四个月';
			}else if (value == '5') {
				backValue = '五个月';
			}else if (value == '6') {
				backValue = '六个月';
			}else if (value == '7') {
				backValue = '七个月';
			}else if (value == '8') {
				backValue = '八个月';
			}else if (value == '9') {
				backValue = '九个月';
			}else if (value == '10') {
				backValue = '十个月';
			}else if (value == '11') {
				backValue = '十一个月';
			}else if (value == '12') {
				backValue = '一年';
			}else if (value == '21') {
				backValue = '二年';
			}else if (value == '31') {
				backValue = '长期';
			}
			return backValue;
		}
    },
    {
    	text: '申请表空间',
        dataIndex: 'DESKSPACE',
        width: 120,
        hidden:true
    },
    {
    	text: '参数模板',
        dataIndex: 'IRESAPPPARAM',
        width: 120
    },
    {
    	text: '审核人',
        dataIndex: 'VERIFIER',
        width: 120
    },
    {
    	text: '申请资源时间',
        dataIndex: 'ADDTIME',
        width: 140
    },
    {
    	text: '结果返回时间',
        dataIndex: 'RETURNTIME',
        width: 140
    },
    {
    	text: '申请状态',
        dataIndex: 'IIFLAG',
        width: 80,
        renderer : function(value, metaData, record) {
			var backValue = "";
			if (value == '1') {
				backValue = "<span class='Abnormal_Complete_purple State_Color'>已申请</span>";
			}else if (value == '5') {
				backValue = "<span class='Complete_Green State_Color'>申请成功</span>";
			}else if (value == '3') {
				backValue = "<span class='Abnormal_yellow State_Color'>申请失败</span>";
			}else if (value == '4') {
				backValue = "<span class='Ignore State_Color'>取消申请</span>";
			}else if (value == '2') {
				backValue = "<span class='Abnormal_Operation_orange State_Color'>拒绝申请</span>";
			}else if (value == '20') {
				backValue = "<span class='Abnormal_Complete_purple State_Color'>回收中</span>";
			}else if (value == '30') {
				backValue = "<span class='Abnormal_yellow State_Color'>回收失败</span>";
			}else if (value == '40') {
				backValue = "<span class='Complete_Green State_Color'>回收完成</span>";
			}
			return backValue;
		}
    },
    {
    	text: '拒绝原因',
        dataIndex: 'IBACKINFO',
        width: 120,
		renderer:function(value,p,record){ 
		    p.tdAttr = " data-qtip = '"+value+"'"; 
		    return value; 
		  }
    },
    {
        text: '操作',
        width: 150,
        renderer: function(value, p, record, rowIndex) {
            var iid = record.get('IID');
            if(appFlag==1){
            	return '<div><a href="javascript:void(0)" onclick="execRecover('+iid+')">' + '&nbsp;资源回收' + '</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="javascript:void(0)" onclick="showDetail('+iid+')">' + '&nbsp;查看详情' + '</a></div>';
            }else{
            	return '<div><a href="javascript:void(0)" onclick="showDetail('+iid+')">' + '&nbsp;查看详情' + '</a></div>';
            }
        }
    }];
    // 分页工具
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: dataSourceStore,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var serviceName = Ext.create("Ext.form.field.Text", {
        fieldLabel: '服务名称',
        labelWidth: 70,
        labelAlign: 'left',
        name: 'dataBaseNameParam',
        width: '30%',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	pageBar.moveFirst();
                }
            }
        }
    });
	var form = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		padding : '5 0 5 0',
		baseCls:'customize_gray_back',
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [
	            serviceName,
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '查询',
	                handler: function() {
	                	QueryMessage();
	                }
	            },
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '清空',
	                handler: function() {
	                    clearQueryWhere();
	                }
	            },'->',
	            {
	                xtype: 'button',
	                cls: 'Common_Btn',
	                text: '取消申请',
	                handler: deleteDataBase
	            }]
			}]
	});
   var dataSourceGrid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	padding : panel_margin,
        store: dataSourceStore,
//        selModel: selModel,
        plugins: [cellEditing],
        border: true,
        bbar: pageBar,
		columnLines: true,
		cls:'customize_panel_back',
        columns: scriptServiceReleaseColumns,
        listeners: {
        	'celldblclick': function(self, td, cellIndex, record, tr, rowIndex, e, eOpts) {
        	}
        }
    });
    function deleteDataBase() {
    	var url='cancleResourceApply.do';
    	if(appFlag == 1){
    		url='cancleResourceApplyapp.do'
    	}
        var data = dataSourceGrid.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请先选择您要操作的行!');
            return;
        } else {
            Ext.Msg.confirm("请确认", "是否要取消申请?",
            function(button, text) {
                if (button == "yes") {
                    var ids = [];
                    Ext.Array.each(data,function(record) {
                        var iid = record.get('IID');
                        // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                        if (iid) {
                        	ids.push(iid);
                        }else{
                        	 dataSourceStore.remove(record);
                        }
                    });
                    if(ids.length>0){
                      Ext.Ajax.request({
                        url: url,
                        params: {
                            deleteIds: ids.join(',')
                        },
                        method: 'POST',
                        success: function(response, opts) {
                            var success = Ext.decode(response.responseText).success;
                            // 当后台数据同步成功时
                            if (success) {
                                dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            } else {
                            	dataSourceStore.reload();
                                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                            }
                        },
                        failure: function(result, request) {
                            secureFilterRs(result, "操作失败！");
                        }
                    });
                    } else{
                    	dataSourceGrid.getView().refresh();
                    }
                }
            });
        }
    }
    function QueryMessage() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pageBar.moveFirst();
	}
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "myresourceapply",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: true,
        bodyPadding : grid_margin,
        bodyCls:'service_platform_bodybg',
        items: [form,dataSourceGrid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
    
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });
    
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
    function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    
    function clearQueryWhere() {
    	serviceName.setValue('');
    }
});
function showDetail(IID) {
	Ext.create('Ext.window.Window', {
	    title: '详情',
	    height: '80%',  //Number型  也可以是字符串类型  width:'60%'
	    width: '80%',
	    layout: 'fit',
	    constrain: true, 		//闲置窗口不超出浏览器
	    constrainHeader:true, 	//标题不能超出浏览器边界
	    modal: true,			//设置模态窗口
	    plain:true, 			//窗口设置透明背景
	    draggable: false,
	    resizable: false,
	    loader: {
			url: 'toMyResourceApplyDetail.do',
			params : {
				IID : IID
			},
			autoLoad: true,
			scripts: true
		},
	    autoScroll:true //显示滚动条
	}).show();
}
function execRecover(IID) {
	 var ids = [];
	 ids.push(IID);
	Ext.Ajax.request({
        url: 'recoverResourceApplyState.do',
        params: {
            revoverIds: ids.join(',')
        },
        method: 'POST',
        success: function(response, opts) {
            var success = Ext.decode(response.responseText).success;
            // 当后台数据同步成功时
            if(success){
                Ext.Msg.confirm("请确认", "确认要进行资源回收操作,回收后将会清理对应资源?",
                	    function(button, text) {
                	        if (button == "yes") {
                	            /*Ext.Array.each(data,function(record) {
                	                var iid = record.get('IID');
                	                // 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
                	                if (iid) {
                	                	ids.push(iid);
                	                }
                	            });*/
                	            if(ids.length>0){
                	              Ext.Ajax.request({
                	                url: 'recoverResourceApply.do',
                	                params: {
                	                    revoverIds: ids.join(',')
                	                },
                	                method: 'POST',
                	                success: function(response, opts) {
                	                    var success = Ext.decode(response.responseText).success;
                	                    // 当后台数据同步成功时
                	                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                	                    dataSourceStore.reload();
                	                },
                	                failure: function(result, request) {
                	                    secureFilterRs(result, "操作失败！");
                	                }
                	            });
                	            } else{
                	            	dataSourceGrid.getView().refresh();
                	            }
                	        }
                	    });
            }else{
            	Ext.Msg.alert('提示', "该状态不可回收");
            }
        },
        failure: function(result, request) {
            secureFilterRs(result, "操作失败！");
        }
    });
	
}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}