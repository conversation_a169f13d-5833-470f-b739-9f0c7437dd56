package com.ideal.ieai.server.repository.db;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.*;
import java.util.Calendar;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.Arrays;
import java.util.List;

public class LoggableConnectionNew implements Connection {

    private static final org.apache.log4j.Logger sqlLogger = org.apache.log4j.Logger.getLogger("SQL_LOGGER");
    private final Connection delegate;
    private static final Map<Integer, String> callerCache = new ConcurrentHashMap<>();
    
    // 需要屏蔽SQL日志的轮询线程类名关键字
    private static final List<String> POLLING_THREAD_KEYWORDS = Arrays.asList(
        "Thread", "Poll", "Monitor", "Scheduler", "Timer", "Quartz", "Job", 
        "Timeout", "Heartbeat", "Check", "Refresh", "Sync", "Clear", "Cleanup",
        "Watcher", "Watching", "Report", "Avg", "Count", "Status", "State",
        "Maintenance", "Appointment", "Immediate", "LineUp", "Warn", "Warning",
        "Alarm", "Push", "Upload", "Download", "Collect", "Agent", "Server",
        "Engine", "Bootstrap", "Service", "Manager", "Repository", "Util"
    );

    public LoggableConnectionNew(Connection connection) {
        this.delegate = connection;
    }

    /**
     * 判断是否应该屏蔽SQL日志（基于调用者类名）
     */
    private boolean shouldFilterSqlLog(String className) {
        if (className == null) {
            return false;
        }
        
        // 检查类名是否包含轮询线程相关的关键字
        for (String keyword : POLLING_THREAD_KEYWORDS) {
            if (className.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取调用者信息（类名、方法名和行号）
     */
    private String getCallerInfo() {
        int hashCode = Thread.currentThread().hashCode();
        String cached = callerCache.get(hashCode);
        if (cached != null) {
            return cached;
        }

        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        // 跳过getStackTrace、getCallerInfo、logSql等方法
        for (int i = 4; i < stackTrace.length; i++) {
            StackTraceElement element = stackTrace[i];
            String className = element.getClassName();
            
            // 跳过LoggableConnection相关的类
            if (!className.contains("LoggableConnection") && 
                !className.contains("LoggableStatement") && 
                !className.contains("LoggablePreparedStatement") && 
                !className.contains("LoggableCallableStatement")) {
                
                String callerInfo = String.format("%s.%s:%d", 
                    className, element.getMethodName(), element.getLineNumber());
                
                // 缓存结果
                callerCache.put(hashCode, callerInfo);
                return callerInfo;
            }
        }
        
        return "Unknown caller";
    }

    /**
     * 打印SQL日志
     */
    private void logSql(String sql, String method) {
        String callerInfo = getCallerInfo();
        
        // 检查是否应该过滤此SQL日志
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (int i = 3; i < stackTrace.length; i++) {
            String className = stackTrace[i].getClassName();
            if (shouldFilterSqlLog(className)) {
                // 如果是轮询线程相关的类，则不记录SQL日志
                return;
            }
        }
        
        String logMessage = String.format("SQL executed by: %s\nMethod: %s\nSQL: %s\n--------------------------------------------------",
                callerInfo, method, sql);
        sqlLogger.info(logMessage);
    }

    @Override
    public Statement createStatement() throws SQLException {
        return new LoggableStatement(delegate.createStatement(), this);
    }

    @Override
    public PreparedStatement prepareStatement(String sql) throws SQLException {
        logSql(sql, "prepareStatement");
        return new LoggablePreparedStatement(delegate.prepareStatement(sql), sql, this);
    }

    @Override
    public CallableStatement prepareCall(String sql) throws SQLException {
        logSql(sql, "prepareCall");
        return new LoggableCallableStatement(delegate.prepareCall(sql), sql, this);
    }

    @Override
    public String nativeSQL(String sql) throws SQLException {
        logSql(sql, "nativeSQL");
        return delegate.nativeSQL(sql);
    }

    @Override
    public void setAutoCommit(boolean autoCommit) throws SQLException {
        delegate.setAutoCommit(autoCommit);
    }

    @Override
    public boolean getAutoCommit() throws SQLException {
        return delegate.getAutoCommit();
    }

    @Override
    public void commit() throws SQLException {
        delegate.commit();
    }

    @Override
    public void rollback() throws SQLException {
        delegate.rollback();
    }

    @Override
    public void close() throws SQLException {
        delegate.close();
    }

    @Override
    public boolean isClosed() throws SQLException {
        return delegate.isClosed();
    }

    @Override
    public DatabaseMetaData getMetaData() throws SQLException {
        return delegate.getMetaData();
    }

    @Override
    public void setReadOnly(boolean readOnly) throws SQLException {
        delegate.setReadOnly(readOnly);
    }

    @Override
    public boolean isReadOnly() throws SQLException {
        return delegate.isReadOnly();
    }

    @Override
    public void setCatalog(String catalog) throws SQLException {
        delegate.setCatalog(catalog);
    }

    @Override
    public String getCatalog() throws SQLException {
        return delegate.getCatalog();
    }

    @Override
    public void setTransactionIsolation(int level) throws SQLException {
        delegate.setTransactionIsolation(level);
    }

    @Override
    public int getTransactionIsolation() throws SQLException {
        return delegate.getTransactionIsolation();
    }

    @Override
    public SQLWarning getWarnings() throws SQLException {
        return delegate.getWarnings();
    }

    @Override
    public void clearWarnings() throws SQLException {
        delegate.clearWarnings();
    }

    @Override
    public Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
        return new LoggableStatement(delegate.createStatement(resultSetType, resultSetConcurrency), this);
    }

    @Override
    public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        logSql(sql, "prepareStatement with resultSetType and resultSetConcurrency");
        return new LoggablePreparedStatement(delegate.prepareStatement(sql, resultSetType, resultSetConcurrency), sql, this);
    }

    @Override
    public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        logSql(sql, "prepareCall with resultSetType and resultSetConcurrency");
        return new LoggableCallableStatement(delegate.prepareCall(sql, resultSetType, resultSetConcurrency), sql, this);
    }

    @Override
    public Map<String, Class<?>> getTypeMap() throws SQLException {
        return delegate.getTypeMap();
    }

    @Override
    public void setTypeMap(Map<String, Class<?>> map) throws SQLException {
        delegate.setTypeMap(map);
    }

    @Override
    public void setHoldability(int holdability) throws SQLException {
        delegate.setHoldability(holdability);
    }

    @Override
    public int getHoldability() throws SQLException {
        return delegate.getHoldability();
    }

    @Override
    public Savepoint setSavepoint() throws SQLException {
        return delegate.setSavepoint();
    }

    @Override
    public Savepoint setSavepoint(String name) throws SQLException {
        return delegate.setSavepoint(name);
    }

    @Override
    public void rollback(Savepoint savepoint) throws SQLException {
        delegate.rollback(savepoint);
    }

    @Override
    public void releaseSavepoint(Savepoint savepoint) throws SQLException {
        delegate.releaseSavepoint(savepoint);
    }

    @Override
    public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        return new LoggableStatement(delegate.createStatement(resultSetType, resultSetConcurrency, resultSetHoldability), this);
    }

    @Override
    public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        logSql(sql, "prepareStatement with resultSetHoldability");
        return new LoggablePreparedStatement(delegate.prepareStatement(sql, resultSetType, resultSetConcurrency, resultSetHoldability), sql, this);
    }

    @Override
    public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        logSql(sql, "prepareCall with resultSetHoldability");
        return new LoggableCallableStatement(delegate.prepareCall(sql, resultSetType, resultSetConcurrency, resultSetHoldability), sql, this);
    }

    @Override
    public PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
        logSql(sql, "prepareStatement with autoGeneratedKeys");
        return new LoggablePreparedStatement(delegate.prepareStatement(sql, autoGeneratedKeys), sql, this);
    }

    @Override
    public PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
        logSql(sql, "prepareStatement with columnIndexes");
        return new LoggablePreparedStatement(delegate.prepareStatement(sql, columnIndexes), sql, this);
    }

    @Override
    public PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
        logSql(sql, "prepareStatement with columnNames");
        return new LoggablePreparedStatement(delegate.prepareStatement(sql, columnNames), sql, this);
    }

    @Override
    public Clob createClob() throws SQLException {
        return delegate.createClob();
    }

    @Override
    public Blob createBlob() throws SQLException {
        return delegate.createBlob();
    }

    @Override
    public NClob createNClob() throws SQLException {
        return delegate.createNClob();
    }

    @Override
    public SQLXML createSQLXML() throws SQLException {
        return delegate.createSQLXML();
    }

    @Override
    public boolean isValid(int timeout) throws SQLException {
        return delegate.isValid(timeout);
    }

    @Override
    public void setClientInfo(String name, String value) throws SQLClientInfoException {
        delegate.setClientInfo(name, value);
    }

    @Override
    public void setClientInfo(Properties properties) throws SQLClientInfoException {
        delegate.setClientInfo(properties);
    }

    @Override
    public String getClientInfo(String name) throws SQLException {
        return delegate.getClientInfo(name);
    }

    @Override
    public Properties getClientInfo() throws SQLException {
        return delegate.getClientInfo();
    }

    @Override
    public Array createArrayOf(String typeName, Object[] elements) throws SQLException {
        return delegate.createArrayOf(typeName, elements);
    }

    @Override
    public Struct createStruct(String typeName, Object[] attributes) throws SQLException {
        return delegate.createStruct(typeName, attributes);
    }

    @Override
    public void setSchema(String schema) throws SQLException {
        delegate.setSchema(schema);
    }

    @Override
    public String getSchema() throws SQLException {
        return delegate.getSchema();
    }

    @Override
    public void abort(Executor executor) throws SQLException {
        delegate.abort(executor);
    }

    @Override
    public void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {
        delegate.setNetworkTimeout(executor, milliseconds);
    }

    @Override
    public int getNetworkTimeout() throws SQLException {
        return delegate.getNetworkTimeout();
    }

    @Override
    public <T> T unwrap(Class<T> iface) throws SQLException {
        return delegate.unwrap(iface);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return delegate.isWrapperFor(iface);
    }

    /**
     * LoggableStatement - Statement的装饰器实现
     */
    public static class LoggableStatement implements Statement {
        private final Statement delegate;
        private final LoggableConnectionNew connection;

        public LoggableStatement(Statement statement, LoggableConnectionNew connection) {
            this.delegate = statement;
            this.connection = connection;
        }

        @Override
        public ResultSet executeQuery(String sql) throws SQLException {
            connection.logSql(sql, "Statement.executeQuery");
            return delegate.executeQuery(sql);
        }

        @Override
        public int executeUpdate(String sql) throws SQLException {
            connection.logSql(sql, "Statement.executeUpdate");
            return delegate.executeUpdate(sql);
        }

        @Override
        public void close() throws SQLException {
            delegate.close();
        }

        @Override
        public int getMaxFieldSize() throws SQLException {
            return delegate.getMaxFieldSize();
        }

        @Override
        public void setMaxFieldSize(int max) throws SQLException {
            delegate.setMaxFieldSize(max);
        }

        @Override
        public int getMaxRows() throws SQLException {
            return delegate.getMaxRows();
        }

        @Override
        public void setMaxRows(int max) throws SQLException {
            delegate.setMaxRows(max);
        }

        @Override
        public void setEscapeProcessing(boolean enable) throws SQLException {
            delegate.setEscapeProcessing(enable);
        }

        @Override
        public int getQueryTimeout() throws SQLException {
            return delegate.getQueryTimeout();
        }

        @Override
        public void setQueryTimeout(int seconds) throws SQLException {
            delegate.setQueryTimeout(seconds);
        }

        @Override
        public void cancel() throws SQLException {
            delegate.cancel();
        }

        @Override
        public SQLWarning getWarnings() throws SQLException {
            return delegate.getWarnings();
        }

        @Override
        public void clearWarnings() throws SQLException {
            delegate.clearWarnings();
        }

        @Override
        public void setCursorName(String name) throws SQLException {
            delegate.setCursorName(name);
        }

        @Override
        public boolean execute(String sql) throws SQLException {
            connection.logSql(sql, "Statement.execute");
            return delegate.execute(sql);
        }

        @Override
        public ResultSet getResultSet() throws SQLException {
            return delegate.getResultSet();
        }

        @Override
        public int getUpdateCount() throws SQLException {
            return delegate.getUpdateCount();
        }

        @Override
        public boolean getMoreResults() throws SQLException {
            return delegate.getMoreResults();
        }

        @Override
        public void setFetchDirection(int direction) throws SQLException {
            delegate.setFetchDirection(direction);
        }

        @Override
        public int getFetchDirection() throws SQLException {
            return delegate.getFetchDirection();
        }

        @Override
        public void setFetchSize(int rows) throws SQLException {
            delegate.setFetchSize(rows);
        }

        @Override
        public int getFetchSize() throws SQLException {
            return delegate.getFetchSize();
        }

        @Override
        public int getResultSetConcurrency() throws SQLException {
            return delegate.getResultSetConcurrency();
        }

        @Override
        public int getResultSetType() throws SQLException {
            return delegate.getResultSetType();
        }

        @Override
        public void addBatch(String sql) throws SQLException {
            delegate.addBatch(sql);
        }

        @Override
        public void clearBatch() throws SQLException {
            delegate.clearBatch();
        }

        @Override
        public int[] executeBatch() throws SQLException {
            return delegate.executeBatch();
        }

        @Override
        public Connection getConnection() throws SQLException {
            return connection;
        }

        @Override
        public boolean getMoreResults(int current) throws SQLException {
            return delegate.getMoreResults(current);
        }

        @Override
        public ResultSet getGeneratedKeys() throws SQLException {
            return delegate.getGeneratedKeys();
        }

        @Override
        public int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
            connection.logSql(sql, "Statement.executeUpdate with autoGeneratedKeys");
            return delegate.executeUpdate(sql, autoGeneratedKeys);
        }

        @Override
        public int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
            connection.logSql(sql, "Statement.executeUpdate with columnIndexes");
            return delegate.executeUpdate(sql, columnIndexes);
        }

        @Override
        public int executeUpdate(String sql, String[] columnNames) throws SQLException {
            connection.logSql(sql, "Statement.executeUpdate with columnNames");
            return delegate.executeUpdate(sql, columnNames);
        }

        @Override
        public boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
            connection.logSql(sql, "Statement.execute with autoGeneratedKeys");
            return delegate.execute(sql, autoGeneratedKeys);
        }

        @Override
        public boolean execute(String sql, int[] columnIndexes) throws SQLException {
            connection.logSql(sql, "Statement.execute with columnIndexes");
            return delegate.execute(sql, columnIndexes);
        }

        @Override
        public boolean execute(String sql, String[] columnNames) throws SQLException {
            connection.logSql(sql, "Statement.execute with columnNames");
            return delegate.execute(sql, columnNames);
        }

        @Override
        public int getResultSetHoldability() throws SQLException {
            return delegate.getResultSetHoldability();
        }

        @Override
        public boolean isClosed() throws SQLException {
            return delegate.isClosed();
        }

        @Override
        public void setPoolable(boolean poolable) throws SQLException {
            delegate.setPoolable(poolable);
        }

        @Override
        public boolean isPoolable() throws SQLException {
            return delegate.isPoolable();
        }

        @Override
        public void closeOnCompletion() throws SQLException {
            delegate.closeOnCompletion();
        }

        @Override
        public boolean isCloseOnCompletion() throws SQLException {
            return delegate.isCloseOnCompletion();
        }

        @Override
        public long executeLargeUpdate(String sql) throws SQLException {
            connection.logSql(sql, "Statement.executeLargeUpdate");
            return delegate.executeLargeUpdate(sql);
        }

        @Override
        public long executeLargeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
            connection.logSql(sql, "Statement.executeLargeUpdate with autoGeneratedKeys");
            return delegate.executeLargeUpdate(sql, autoGeneratedKeys);
        }

        @Override
        public long executeLargeUpdate(String sql, int[] columnIndexes) throws SQLException {
            connection.logSql(sql, "Statement.executeLargeUpdate with columnIndexes");
            return delegate.executeLargeUpdate(sql, columnIndexes);
        }

        @Override
        public long executeLargeUpdate(String sql, String[] columnNames) throws SQLException {
            connection.logSql(sql, "Statement.executeLargeUpdate with columnNames");
            return delegate.executeLargeUpdate(sql, columnNames);
        }

        @Override
        public long getLargeUpdateCount() throws SQLException {
            return delegate.getLargeUpdateCount();
        }

        @Override
        public long getLargeMaxRows() throws SQLException {
            return delegate.getLargeMaxRows();
        }

        @Override
        public void setLargeMaxRows(long max) throws SQLException {
            delegate.setLargeMaxRows(max);
        }

        @Override
        public <T> T unwrap(Class<T> iface) throws SQLException {
            return delegate.unwrap(iface);
        }

        @Override
        public boolean isWrapperFor(Class<?> iface) throws SQLException {
            return delegate.isWrapperFor(iface);
        }
    }
