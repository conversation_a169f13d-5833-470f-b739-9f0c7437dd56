package com.ideal.ieai.server.repository.activity;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;

/**
 * <ul>
 * <li>Title: ScriptCallRuntimeManager.java</li>
 * <li>Description:
 * 1.脚本活动启动后，保存脚本活动的属性
 * 2.脚本的状态为结束时，删除存在表里的记录
 * 3.脚本轮询线程启动后，获取已经被启动的脚本线程</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2019年3月25日
 */
public class ScriptCallRuntimeManager       
{
    private static final String                   METHOD    = "method：";
    private static final String                   EXCEPTION = ";Exception:";
    private ScriptCallRuntimeManager(){}

    private static final ScriptCallRuntimeManager _manager  = new ScriptCallRuntimeManager();

    public static ScriptCallRuntimeManager getInstance ()
    {
        return _manager;
    }

    private static final Logger log = Logger.getLogger(ScriptCallRuntimeManager.class);

    /**
     * 
     * <li>Description:脚本活动启动后，保存脚本活动的属性</li> 
     * <AUTHOR>
     * 2019年3月24日 
     * @param actRuntimeIid
     * @param scriptCallFlowIid
     * @param workflowid
     * @param scriptuuid
     * @param actName
     * @param dbtype
     * return void
     */
    public void insert ( long actRuntimeIid, long scriptCallFlowIid, long workflowid, String scriptuuid, String actName,
            int dbtype )
    {
        PreparedStatement ps = null;
        Connection con = null;
        String actSql = "insert into IEAI_SCRIPT_CALL_RUNTIME(IID,SCRIPTFLOWID,SCRIPTUUID,ACTNAME,WORKFLOWID,ACTRUNTIMEIID)values(?,?,?,?,?,?)";
        try
        {
            con = DBManager.getInstance().getJdbcConnection(dbtype);
            ps = con.prepareStatement(actSql);
            long iid = IdGenerator.createId("IEAI_SCRIPT_CALL_RUNTIME", con);
            ps.setLong(1, iid);
            ps.setLong(2, scriptCallFlowIid);
            ps.setString(3, scriptuuid);
            ps.setString(4, actName);
            ps.setLong(5, workflowid);
            ps.setLong(6, actRuntimeIid);
            ps.executeUpdate();
            con.commit();

        } catch (DBException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (RepositoryException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closeConnection(con, "insert", log);
            DBResource.closePreparedStatement(ps, "insert", log);
        }
    }

    /**
     * 
     * <li>Description: 查询将要监视的脚本活动状态的集合</li> 
     * <AUTHOR>
     * 2019年3月24日 
     * @param serviceip
     * @return
     * return List<ScriptCallBean>
     */
    public List<ScriptCallBean> getStartScriptactivityList ( String serviceip,int start, int limit )
    {
        PreparedStatement ps = null;
        Connection con = null;
        ResultSet rs = null;
        List<ScriptCallBean> scriptCallBeans = new ArrayList<ScriptCallBean>();
        String sql = "";
        if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.MYSQL)){
            sql = " SELECT a.IEXECATID,a.ISTATUS,a.ISCRIPTFLOWID,a.USERNAME,a.PORT,a.SERVICEIP from IEAI_SCRIPT_RUNTIME_INFO a  ,IEAI_WORKFLOWINSTANCE b  where a.WORKFLOWID =b.IFLOWID and  b.IHOSTNAME=? and b.ISTATUS = 0 and a.IEXECATID is not null and a.ISTATUS in (0,3) ORDER BY a.IEXECATID  LIMIT ?,? ";
        } else if (DBManager.Orcl_Faimily()) {
            sql = " SELECT * FROM  ( SELECT ROWNUM AS RN, C.* FROM ( SELECT a.IEXECATID,a.ISTATUS,a.ISCRIPTFLOWID,a.USERNAME,a.PORT,a.SERVICEIP from IEAI_SCRIPT_RUNTIME_INFO a  ,IEAI_WORKFLOWINSTANCE b  where a.WORKFLOWID =b.IFLOWID and  b.IHOSTNAME=? and b.ISTATUS = 0 and a.IEXECATID is not null and a.ISTATUS in (0,3) ORDER BY a.IEXECATID  ) C )  WHERE RN BETWEEN ?  AND ? ";
            limit = start + limit;
            start = start + 1;
        } else {
            sql = " SELECT * FROM (SELECT ROW_NUMBER() OVER( ORDER BY a.IEXECATID DESC ) AS ROWNUM,a.IEXECATID,a.ISTATUS,a.ISCRIPTFLOWID,a.USERNAME,a.PORT,a.SERVICEIP from IEAI_SCRIPT_RUNTIME_INFO a  ,IEAI_WORKFLOWINSTANCE b  where a.WORKFLOWID =b.IFLOWID and  b.IHOSTNAME=? and b.ISTATUS = 0 and a.IEXECATID is not null and a.ISTATUS in (0,3) ) WHERE ROWNUM BETWEEN ? AND ? ";
            limit = start + limit;
            start = start + 1;
        }
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setString(1, serviceip);
            ps.setInt(2, start);
            ps.setInt(3, limit);
            rs = ps.executeQuery();
            while (rs.next())
            {
                ScriptCallBean scriptCallBean = new ScriptCallBean();
                scriptCallBean.setIscriptflowid(rs.getLong("ISCRIPTFLOWID"));
                scriptCallBean.setExecatid(rs.getLong("IEXECATID"));
                scriptCallBean.setUsername(rs.getString("USERNAME"));
                scriptCallBean.setPort(rs.getString("PORT"));
                scriptCallBean.setServiceip(rs.getString("SERVICEIP"));
                scriptCallBean.setIstatus(rs.getLong("ISTATUS"));
                if(scriptCallBean.getExecatid() != null){
                scriptCallBeans.add(scriptCallBean);}
            }

        } catch (DBException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closeConn(con, rs, ps, "delayMonitor", log);
        }
        return scriptCallBeans;
    }

    /**
     * 
     * <li>Description: 脚本活动的状态为结束时，删除将要此脚本的相关属性</li> 
     * <AUTHOR>
     * 2019年3月24日 
     * @param scriptcallflowid
     * return void
     */
    public int delete ( Long scriptcallflowid )
    {
        PreparedStatement ps = null;
        Connection con = null;
        ResultSet rs = null;
        int deletes = 0;
        String sql = "DELETE FROM IEAI_SCRIPT_RUNTIME_INFO WHERE ISCRIPTFLOWID = ? ";
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setLong(1, scriptcallflowid);
            deletes =   ps.executeUpdate();
            con.commit();

        } catch (DBException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closeConn(con, rs, ps, "delete", log);
        }
        return deletes;

    }
    /**
     * 
     * <li>Description: 根据脚本的flowid，获取脚本再次进入活动的id</li> 
     * <AUTHOR>
     * 2019年3月25日 
     * @param scriptcallflowid
     * @return
     * return Long
     */
    public Long queryExecactId ( Long scriptcallflowid )
    { 
        Long execactid = 0L;
        PreparedStatement ps = null;
        Connection con = null;
        ResultSet rs = null;
        String sql = "select IEXECATID from IEAI_SCRIPT_RUNTIME_INFO where ISCRIPTFLOWID = ? ";
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setLong(1, scriptcallflowid);
           rs = ps.executeQuery();
           while(rs.next()){
               execactid = rs.getLong("IEXECATID");
           }

        } catch (DBException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closeConn(con, rs, ps, "delete", log);
        }
        return execactid;

    }
   /**
    * 
    * <li>Description:第一次进入活动的脚本被轮询后，改变状态，下次不在被轮询</li> 
    * <AUTHOR>
    * 2019年3月25日 
    * @param scriptcallflowid
    * return void
    */
    public void updatescriptIstatus ( Long scriptcallflowid,Long istatus )
    {
        PreparedStatement ps = null;
        Connection con = null;
        String sql = "UPDATE IEAI_SCRIPT_RUNTIME_INFO SET ISTATUS=? WHERE ISCRIPTFLOWID=?";
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setLong(1, istatus);
            ps.setLong(2, scriptcallflowid);
            ps.executeUpdate();
           con.commit();

        } catch (DBException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closePSConn(con, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        
    }
    
    /**   
     * @Title: expcountUpdate   
     * @Description: 用于脚本调用独立线程轮询异常脚本活动的重试次数记录   
     * @param scriptcallflowid
     * @param iexpcount      
     * @author: Sayai 
     * @date:   2019年3月27日 上午9:06:39   
     */
    public void expcountUpdate ( Long scriptcallflowid, Long iexpcount )
    {
        PreparedStatement ps = null;
        Connection con = null;
        String sql = "UPDATE IEAI_SCRIPT_RUNTIME_INFO SET IEXPCOUNT = ? WHERE ISCRIPTFLOWID=?";
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            ps = con.prepareStatement(sql);
            ps.setLong(1, iexpcount);
            ps.setLong(2, scriptcallflowid);
            ps.executeUpdate();
            con.commit();
            
        } catch (DBException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closePSConn(con, ps, "updatescriptIstatus", log);
        }
        
    }

    /**   
     * @Title: updateStatesByRuningtimeActID   
     * @Description: 将运行状态的活动手动略过  
     * @param runtimeActiid
     * @param sysType
     * @param actName
     * @param l      
     * @author: lyq 
     * @throws DBException 
     * @date:   2019年4月22日 下午9:19:38   
     */
    public void updateStatesByRuntimeActiid ( long runtimeActiid, int sysType, long actIstatus )
            throws DBException
    {

        PreparedStatement ps = null;
        Connection con = null;
        String sql = "UPDATE IEAI_SCRIPT_RUNTIME_INFO SET ISTATUS = ? WHERE IACTRUNTIMEIID =? ";
        try
        {
            con = DBManager.getInstance().getJdbcConnection(sysType);
            ps = con.prepareStatement(sql);
            ps.setLong(1, actIstatus);
            ps.setLong(2, runtimeActiid);
            ps.executeUpdate();
            con.commit();

        } catch (SQLException e)
        {
            log.error(METHOD + Thread.currentThread().getStackTrace()[1].getMethodName() + EXCEPTION, e);
        } finally
        {
            DBResource.closePSConn(con, ps, "updatescriptIstatus", log);
        }

    }
}
